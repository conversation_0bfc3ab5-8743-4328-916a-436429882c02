var e,t,s,o,i,a,l,n;import{Q as r,o as c,f as u,w as d,h as p,t as h,i as m,g as f,D as g,C as y,O as k,q as v,e as C,p as _,R as b,S as I,T as w,j as x,F as S,k as A,J as z,U as E,K as Q,L as B,A as T,z as P,V as j,W as F,X as R,Y as $,Z as V,_ as O,a0 as J,a1 as H,r as q,a2 as X,a3 as W,$ as N,a as U,a4 as G,a5 as K,u as Z,a6 as D,a7 as L,a8 as Y,a9 as M}from"./index-KqVIYTFB.js";import{_ as ee,r as te,d as se,o as oe}from"./uni-app.es.elp5fm4t.js";import{_ as ie}from"./z-paging.DXcm7bPn.js";const ae=ee({__name:"u-voicePlay",props:{url:{type:String,default:""},duration:{type:Number,default:0},isPlay:{type:[Boolean],default:!1},direction:{type:String,default:"right"},size:{type:Number,default:24},color:{type:String,default:"#222"}},setup(e){const t=e,s=r((()=>({transform:`scale(${t.size/24})`}))),o=r((()=>({transform:`rotate(${{right:"0deg",bottom:"90deg",left:"180deg",top:"270deg"}[t.direction||"left"]})`,width:t.size+"px",height:t.size+"px"})));return(t,i)=>{const a=k,l=v;return c(),u(l,{class:"box",style:g(e.duration>=10?"width: 200rpx;":"width:150rpx;")},{default:d((()=>["left"==e.direction?(c(),u(a,{key:0,class:"durationText"},{default:d((()=>[p(h(e.duration)+"″",1)])),_:1})):m("",!0),f(l,{style:g([o.value])},{default:d((()=>[f(l,{class:y(["audio-style",e.isPlay?"animation":"noAnimation"]),style:g([s.value])},{default:d((()=>[f(l,{class:"small",style:g({"background-color":e.color})},null,8,["style"]),f(l,{class:"middle",style:g({"border-right-color":e.color})},null,8,["style"]),f(l,{class:"large",style:g({"border-right-color":e.color})},null,8,["style"])])),_:1},8,["style","class"])])),_:1},8,["style"]),"right"==e.direction?(c(),u(a,{key:1,class:"durationText"},{default:d((()=>[p(h(e.duration)+"″",1)])),_:1})):m("",!0)])),_:1},8,["style"])}}},[["__scopeId","data-v-fec8e46e"]]);const le=ee({name:"chat-item",props:{item:{type:Object,default:()=>({time:"",icon:"",name:"",content:"",isMe:!1})}},data:()=>({}),methods:{handleMessageAudioClick(e){this.$emit("handleMessageAudioClick",e)},previewVideo(e){this.$emit("previewVideo",e)}}},[["render",function(e,t,s,o,i,a){const l=k,n=_,r=v,b=te(C("uv-icon"),se),I=te(C("u-voicePlay"),ae);return c(),u(r,{class:"chat-item"},{default:d((()=>[s.item.time&&s.item.time.length?(c(),u(l,{key:0,class:"chat-time"},{default:d((()=>[p(h(s.item.time),1)])),_:1})):m("",!0),f(r,{class:y({"chat-container":!0,"chat-location-me":"kefu"!=s.item.to_type})},{default:d((()=>[f(r,{class:"chat-icon-container"},{default:d((()=>[f(n,{class:"chat-icon",src:s.item.from_avatar,mode:"aspectFill"},null,8,["src"])])),_:1}),f(r,{class:"chat-content-container"},{default:d((()=>[f(l,{class:y({"chat-user-name":!0,"chat-location-me":"kefu"!=s.item.to_type})},{default:d((()=>[p(h(s.item.from_nickname),1)])),_:1},8,["class"]),f(r,{class:"chat-text-container-super",style:g([{justifyContent:"kefu"==s.item.to_type?"flex-start":"flex-end"}])},{default:d((()=>[1==s.item.msg_type?(c(),u(r,{key:0,class:y({"chat-text-container":!0,"chat-text-container-me":"kefu"!=s.item.to_type})},{default:d((()=>[f(l,{class:y({"chat-text":!0,"chat-text-me":"kefu"!=s.item.to_type})},{default:d((()=>[p(h(s.item.msg),1)])),_:1},8,["class"])])),_:1},8,["class"])):m("",!0),2==s.item.msg_type?(c(),u(r,{key:1,onClick:t[0]||(t[0]=e=>a.previewVideo(s.item)),class:"margin-top-10"},{default:d((()=>[f(n,{src:s.item.msg,class:"border-radius-12",style:{width:"400rpx"},mode:"widthFix"},null,8,["src"])])),_:1})):m("",!0),3==s.item.msg_type?(c(),u(r,{key:2,class:""},{default:d((()=>[f(r,{class:"goods display-flex margin-right-10 margin-top-10",style:{width:"450rpx","border-radius":"30rpx"}},{default:d((()=>[f(n,{src:s.item.goods.image,style:{width:"140rpx",height:"140rpx"},class:"border-radius-12",mode:"aspectFill"},null,8,["src"]),f(r,{class:"margin-left-20 file-1"},{default:d((()=>[f(r,{style:{"font-size":"30rpx","font-weight":"500","-webkit-line-clamp":"2"},class:"webkit-line-clamp"},{default:d((()=>[p(h(s.item.goods.name),1)])),_:1}),f(r,{style:{color:"#ff4c3b"},class:"margin-top-10"},{default:d((()=>[f(l,{class:"font-size-24"},{default:d((()=>[p("￥")])),_:1}),f(l,{class:"font-size-32 font-weight-bold"},{default:d((()=>[p(h(s.item.goods.min_price),1)])),_:1}),f(l,{class:"font-size-26 margin-left-10"},{default:d((()=>[p("起")])),_:1})])),_:1})])),_:1})])),_:1})])),_:1})):m("",!0),5==s.item.msg_type?(c(),u(r,{key:3,onClick:t[1]||(t[1]=e=>a.previewVideo(s.item)),class:"position-relative margin-top-10",style:{width:"400rpx"}},{default:d((()=>[f(n,{src:s.item.msg+"?x-oss-process=video/snapshot,t_1000,f_jpg,w_0,h_0,m_fast,ar_auto",class:"border-radius-12",style:{width:"400rpx"},mode:"widthFix"},null,8,["src"]),f(r,{class:"position-absolute transform-translate-center",style:{top:"50%",left:"50%"}},{default:d((()=>[f(b,{name:"play-circle",color:"#333333",size:"40"})])),_:1})])),_:1})):m("",!0),4==s.item.msg_type?(c(),u(r,{key:4,onClick:t[2]||(t[2]=e=>a.handleMessageAudioClick(s.item)),class:y({"chat-text-container":!0,"chat-text-container-me":"kefu"!=s.item.to_type})},{default:d((()=>[f(I,{isPlay:s.item.isPlay,duration:s.item.voice_duration,direction:"kefu"==s.item.to_type?"right":"left"},null,8,["isPlay","duration","direction"])])),_:1},8,["class"])):m("",!0)])),_:1},8,["style"])])),_:1})])),_:1},8,["class"])])),_:1})}],["__scopeId","data-v-81f6ed45"]]);const ne=ee({name:"chat-input-bar",data:()=>({msg:"",emojisArr:["😊","😨","😍","😳","😎","😭","😌","😵","😴","😢","😅","😡","😜","😲","😟","😤","😞","😫","😣","😈","😉","😯","😕","😰","😋","😝","😓","😀","😂","😘","😒","😏","😶","😱","😖","😩","😔","😑","😚","😪","😇","🙊","👊","👎","☝️","✌️","😬","😷","🙈","👌","👋","✊","💪","😆","☺️","🙉","👍","🙏","✋","☀️","☕️","⛄️","📚","🎁","🎉","🍦","☁️","❄️","⚡️","💰","🎂","🎓","🍖","☔️","⛅️","✏️","💩","🎄","🍷","🎤","🏀","🀄️","💣","📢","🌍","🍫","🎲","🏂","💡","💤","🚫","🌻","🍻","🎵","🏡","💢","📞","🚿","🍚","👪","👼","💊","🔫","🌹","🐶","💄","👫","👽","💋","🌙","🍉","🐷","💔","👻","😈","💍","🌲","🐴","👑","🔥","⭐️","⚽️","🕖","⏰","😁","🚀","⏳"],focus:!1,emojiType:"",isflag:!0}),methods:{openType(){this.emojiType=this.emojiType&&"emoji"!==this.emojiType?"emoji":"keyboard",this.isflag&&"keyboard"==this.emojiType?this.isflag=!1:setTimeout((()=>{this.isflag=!0}),500)},chooseVideo(){var e=this;b({count:9,sizeType:["original","compressed"],sourceType:["camera","album"],success:function(t){console.log(t),e.$emit("uploadFiles",{...t,msg_type:5})}})},chooseImage(){var e=this;I({count:9,sizeType:["original","compressed"],sourceType:["camera","album"],success:async function(t){console.log(t),e.$emit("uploadFiles",{...t,msg_type:2})}})},updateKeyboardHeightChange(e){e.height>0&&(this.emojiType="emoji")},hidedKeyboard(){"keyboard"===this.emojiType&&(this.emojiType="")},emojiChange(e){setTimeout((()=>{this.isflag=!0}),500),this.$emit("emojiTypeChange",this.emojiType),"keyboard"===this.emojiType?this.focus=!0:(this.focus=!1,w()),this.emojiType=this.emojiType&&"emoji"!==this.emojiType?"emoji":"keyboard"},emojiClick(e){this.msg+=e},sendClick(){this.msg.length&&(this.$emit("send",this.msg,1),this.msg="")}}},[["render",function(e,t,s,o,i,a){const l=z,n=v,r=_,y=k,b=te(C("uv-icon"),se),I=E;return c(),u(n,{class:"chat-input-bar-container"},{default:d((()=>[f(n,{class:"chat-input-bar"},{default:d((()=>[f(n,{class:"chat-input-container"},{default:d((()=>[f(l,{focus:i.focus,class:"chat-input",modelValue:i.msg,"onUpdate:modelValue":t[0]||(t[0]=e=>i.msg=e),"adjust-position":!1,"confirm-type":"send",type:"text",placeholder:"请输入内容",onConfirm:a.sendClick},null,8,["focus","modelValue","onConfirm"])])),_:1}),f(n,{class:"emoji-container"},{default:d((()=>[f(r,{class:"emoji-img",src:`/static/${i.emojiType||"emoji"}.png`,onClick:a.emojiChange},null,8,["src","onClick"])])),_:1}),i.msg?(c(),u(n,{key:0,class:"chat-input-send",onClick:a.sendClick},{default:d((()=>[f(y,{class:"chat-input-send-text"},{default:d((()=>[p("发送")])),_:1})])),_:1},8,["onClick"])):(c(),u(n,{key:1,onClick:a.openType,class:"margin-left-20"},{default:d((()=>[f(b,{name:"plus-circle",size:"30"})])),_:1},8,["onClick"]))])),_:1}),f(n,{class:"emoji-panel-container",style:g([{height:"keyboard"===i.emojiType?"400rpx":"0px"}])},{default:d((()=>[i.isflag?(c(),u(I,{key:0,"scroll-y":"",style:{height:"100%",flex:"1"}},{default:d((()=>[f(n,{class:"emoji-panel"},{default:d((()=>[(c(!0),x(S,null,A(i.emojisArr,((e,t)=>(c(),u(y,{class:"emoji-panel-text",key:t,onClick:t=>a.emojiClick(e)},{default:d((()=>[p(h(e),1)])),_:2},1032,["onClick"])))),128))])),_:1})])),_:1})):m("",!0),f(n,{class:"display-flex"},{default:d((()=>[f(n,{onClick:t[1]||(t[1]=e=>a.chooseImage()),class:"display-flex margin-right-30 flex-direction-column center align-items"},{default:d((()=>[f(n,{class:"border-radius-12 display-flex center align-items",style:{width:"120rpx",height:"120rpx",background:"#F5F5F5"}},{default:d((()=>[f(r,{src:"data:image/png;base64,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",style:{width:"70rpx",height:"70rpx"},mode:"widthFix"})])),_:1}),f(n,{class:"font-size-26 margin-top-05 color-666"},{default:d((()=>[p(" 相册 ")])),_:1})])),_:1}),f(n,{onClick:t[2]||(t[2]=e=>a.chooseVideo()),class:"display-flex flex-direction-column center align-items"},{default:d((()=>[f(n,{class:"border-radius-12 display-flex center align-items",style:{width:"120rpx",height:"120rpx",background:"#F5F5F5"}},{default:d((()=>[f(r,{src:"/kefu_m/assets/xj-Cu_vROjH.png",style:{width:"70rpx",height:"70rpx"},mode:"widthFix"})])),_:1}),f(n,{class:"font-size-26 margin-top-05 color-666"},{default:d((()=>[p(" 相机 ")])),_:1})])),_:1})])),_:1})])),_:1},8,["style"])])),_:1})}],["__scopeId","data-v-adc9c9f3"]]);const re=ee({name:"uv-search",emits:["click","input","change","clear","search","custom","focus","blur","clickIcon","update:modelValue"],mixins:[Q,B,{props:{value:{type:[String,Number],default:""},modelValue:{type:[String,Number],default:""},shape:{type:String,default:"round"},bgColor:{type:String,default:"#f2f2f2"},placeholder:{type:String,default:"请输入关键字"},clearabled:{type:Boolean,default:!0},focus:{type:Boolean,default:!1},showAction:{type:Boolean,default:!0},actionStyle:{type:Object,default:()=>({})},actionText:{type:String,default:"搜索"},inputAlign:{type:String,default:"left"},inputStyle:{type:Object,default:()=>({})},disabled:{type:Boolean,default:!1},borderColor:{type:String,default:"transparent"},searchIconColor:{type:String,default:"#909399"},color:{type:String,default:"#606266"},placeholderColor:{type:String,default:"#909399"},searchIcon:{type:String,default:"search"},searchIconSize:{type:[Number,String],default:22},margin:{type:String,default:"0"},animation:{type:Boolean,default:!1},maxlength:{type:[String,Number],default:-1},height:{type:[String,Number],default:32},label:{type:[String,Number,null],default:null},boxStyle:{type:[String,Object],default:()=>({})},...null==(t=null==(e=uni.$uv)?void 0:e.props)?void 0:t.search}}],data(){return{keyword:"",showClear:!1,show:!1,focused:this.focus}},created(){this.keyword=this.modelValue},watch:{value(e){this.keyword=e},modelValue(e){this.keyword=e}},computed:{showActionBtn(){return!this.animation&&this.showAction}},methods:{keywordChange(){this.$emit("input",this.keyword),this.$emit("update:modelValue",this.keyword),this.$emit("change",this.keyword)},inputChange(e){this.keyword=e.detail.value,this.keywordChange()},clear(){this.keyword="",this.$nextTick((()=>{this.$emit("clear")})),this.keywordChange()},search(e){this.$emit("search",e.detail.value);try{w()}catch(t){}},custom(){this.$emit("custom",this.keyword);try{w()}catch(e){}},getFocus(){this.focused=!0,this.animation&&this.showAction&&(this.show=!0),this.$emit("focus",this.keyword)},blur(){setTimeout((()=>{this.focused=!1}),100),this.show=!1,this.$emit("blur",this.keyword)},clickHandler(){this.disabled&&this.$emit("click")},clickIcon(){this.$emit("clickIcon")}}},[["render",function(e,t,s,o,i,a){const l=v,n=te(C("uv-icon"),se),r=z,_=k;return c(),u(l,{class:"uv-search",onClick:a.clickHandler,style:g([{margin:e.margin},e.$uv.addStyle(e.customStyle)])},{default:d((()=>[f(l,{class:"uv-search__content",style:g([{backgroundColor:e.bgColor,borderRadius:"round"==e.shape?"100px":"4px",borderColor:e.borderColor},e.$uv.addStyle(e.boxStyle)])},{default:d((()=>[e.disabled?(c(),u(l,{key:0,class:"uv-search__content__disabled"})):m("",!0),T(e.$slots,"prefix",{},(()=>[f(l,{class:"uv-search__content__icon"},{default:d((()=>[f(n,{onClick:a.clickIcon,size:e.searchIconSize,name:e.searchIcon,color:e.searchIconColor?e.searchIconColor:e.color},null,8,["onClick","size","name","color"])])),_:1})]),!0),f(r,{"confirm-type":"search",onBlur:a.blur,value:i.keyword,onConfirm:a.search,onInput:a.inputChange,disabled:e.disabled,onFocus:a.getFocus,focus:e.focus,maxlength:e.maxlength,"placeholder-class":"uv-search__content__input--placeholder",placeholder:e.placeholder,"placeholder-style":`color: ${e.placeholderColor}`,class:"uv-search__content__input",type:"text",style:g([{textAlign:e.inputAlign,color:e.color,backgroundColor:e.bgColor,height:e.$uv.addUnit(e.height)},e.inputStyle])},null,8,["onBlur","value","onConfirm","onInput","disabled","onFocus","focus","maxlength","placeholder","placeholder-style","style"]),i.keyword&&e.clearabled&&i.focused?(c(),u(l,{key:1,class:"uv-search__content__icon uv-search__content__close",onClick:a.clear},{default:d((()=>[f(n,{name:"close",size:"11",color:"#ffffff",customStyle:"line-height: 12px"})])),_:1},8,["onClick"])):m("",!0),T(e.$slots,"suffix",{},void 0,!0)])),_:3},8,["style"]),f(_,{style:g([e.actionStyle]),class:y(["uv-search__action",[(a.showActionBtn||i.show)&&"uv-search__action--active"]]),onClick:P(a.custom,["stop","prevent"])},{default:d((()=>[p(h(e.actionText),1)])),_:1},8,["style","class","onClick"])])),_:3},8,["onClick","style"])}],["__scopeId","data-v-b3baced2"]]);class ce{constructor(e,t){this.options=e,this.animation=j({...e}),this.currentStepAnimates={},this.next=0,this.$=t}_nvuePushAnimates(e,t){let s=this.currentStepAnimates[this.next],o={};if(o=s||{styles:{},config:{}},ue.includes(e)){o.styles.transform||(o.styles.transform="");let s="";"rotate"===e&&(s="deg"),o.styles.transform+=`${e}(${t+s}) `}else o.styles[e]=`${t}`;this.currentStepAnimates[this.next]=o}_animateRun(e={},t={}){let s=this.$.$refs.ani.ref;if(s)return new Promise(((o,i)=>{nvueAnimation.transition(s,{styles:e,...t},(e=>{o()}))}))}_nvueNextAnimate(e,t=0,s){let o=e[t];if(o){let{styles:i,config:a}=o;this._animateRun(i,a).then((()=>{t+=1,this._nvueNextAnimate(e,t,s)}))}else this.currentStepAnimates={},"function"==typeof s&&s(),this.isEnd=!0}step(e={}){return this.animation.step(e),this}run(e){this.$.animationData=this.animation.export(),this.$.timer=setTimeout((()=>{"function"==typeof e&&e()}),this.$.durationTime)}}const ue=["matrix","matrix3d","rotate","rotate3d","rotateX","rotateY","rotateZ","scale","scale3d","scaleX","scaleY","scaleZ","skew","skewX","skewY","translate","translate3d","translateX","translateY","translateZ"];function de(e,t){if(t)return clearTimeout(t.timer),new ce(e,t)}ue.concat(["opacity","backgroundColor"],["width","height","left","right","top","bottom"]).forEach((e=>{ce.prototype[e]=function(...t){return this.animation[e](...t),this}}));const pe=ee({name:"uv-transition",mixins:[Q,B],emits:["click","change"],props:{show:{type:Boolean,default:!1},mode:{type:[Array,String,null],default:()=>"fade"},duration:{type:[String,Number],default:300},timingFunction:{type:String,default:"ease-out"},customClass:{type:String,default:""},cellChild:{type:Boolean,default:!1}},data:()=>({isShow:!1,transform:"",opacity:1,animationData:{},durationTime:300,config:{}}),watch:{show:{handler(e){e?this.open():this.isShow&&this.close()},immediate:!0}},computed:{transformStyles(){const e={transform:this.transform,opacity:this.opacity,...this.$uv.addStyle(this.customStyle),"transition-duration":this.duration/1e3+"s"};return this.$uv.addStyle(e,"string")}},created(){this.config={duration:this.duration,timingFunction:this.timingFunction,transformOrigin:"50% 50%",delay:0},this.durationTime=this.duration},methods:{init(e={}){e.duration&&(this.durationTime=e.duration),this.animation=de(Object.assign(this.config,e),this)},onClick(){this.$emit("click",{detail:this.isShow})},step(e,t={}){if(this.animation){for(let t in e)try{"object"==typeof e[t]?this.animation[t](...e[t]):this.animation[t](e[t])}catch(s){console.error(`方法 ${t} 不存在`)}return this.animation.step(t),this}},run(e){this.animation&&this.animation.run(e)},open(){clearTimeout(this.timer),this.transform="",this.isShow=!0;let{opacity:e,transform:t}=this.styleInit(!1);void 0!==e&&(this.opacity=e),this.transform=t,this.$nextTick((()=>{this.timer=setTimeout((()=>{this.animation=de(this.config,this),this.tranfromInit(!1).step(),this.animation.run(),this.opacity=1,this.$emit("change",{detail:this.isShow}),this.transform=""}),20)}))},close(e){this.animation&&this.tranfromInit(!0).step().run((()=>{this.isShow=!1,this.animationData=null,this.animation=null;let{opacity:e,transform:t}=this.styleInit(!1);this.opacity=e||1,this.transform=t,this.$emit("change",{detail:this.isShow})}))},styleInit(e){let t={transform:""},s=(e,s)=>{"fade"===s?t.opacity=this.animationType(e)[s]:t.transform+=this.animationType(e)[s]+" "};return"string"==typeof this.mode?s(e,this.mode):this.mode.forEach((t=>{s(e,t)})),t},tranfromInit(e){let t=(e,t)=>{let s=null;"fade"===t?s=e?0:1:(s=e?"-100%":"0","zoom-in"===t&&(s=e?.8:1),"zoom-out"===t&&(s=e?1.2:1),"slide-right"===t&&(s=e?"100%":"0"),"slide-bottom"===t&&(s=e?"100%":"0")),this.animation[this.animationMode()[t]](s)};return"string"==typeof this.mode?t(e,this.mode):this.mode.forEach((s=>{t(e,s)})),this.animation},animationType:e=>({fade:e?1:0,"slide-top":`translateY(${e?"0":"-100%"})`,"slide-right":`translateX(${e?"0":"100%"})`,"slide-bottom":`translateY(${e?"0":"100%"})`,"slide-left":`translateX(${e?"0":"-100%"})`,"zoom-in":`scaleX(${e?1:.8}) scaleY(${e?1:.8})`,"zoom-out":`scaleX(${e?1:1.2}) scaleY(${e?1:1.2})`}),animationMode:()=>({fade:"opacity","slide-top":"translateY","slide-right":"translateX","slide-bottom":"translateY","slide-left":"translateX","zoom-in":"scale","zoom-out":"scale"}),toLine:e=>e.replace(/([A-Z])/g,"-$1").toLowerCase()}},[["render",function(e,t,s,o,i,a){const l=v;return i.isShow?(c(),u(l,{key:0,ref:"ani",animation:i.animationData,class:y(s.customClass),style:g(a.transformStyles),onClick:a.onClick},{default:d((()=>[T(e.$slots,"default")])),_:3},8,["animation","class","style","onClick"])):m("",!0)}]]);const he=ee({name:"uv-overlay",emits:["click"],mixins:[Q,B,{props:{show:{type:Boolean,default:!1},zIndex:{type:[String,Number],default:10070},duration:{type:[String,Number],default:300},opacity:{type:[String,Number],default:.5},...null==(o=null==(s=uni.$uv)?void 0:s.props)?void 0:o.overlay}}],watch:{show(e){document.querySelector("body").style.overflow=e?"hidden":""}},computed:{overlayStyle(){const e={position:"fixed",top:0,left:0,right:0,zIndex:this.zIndex,bottom:0,"background-color":`rgba(0, 0, 0, ${this.opacity})`};return this.$uv.deepMerge(e,this.$uv.addStyle(this.customStyle))}},methods:{clickHandler(){this.$emit("click")},clear(){}}},[["render",function(e,t,s,o,i,a){const l=te(C("uv-transition"),pe);return c(),u(l,{show:e.show,mode:"fade","custom-class":"uv-overlay",duration:e.duration,"custom-style":a.overlayStyle,onClick:a.clickHandler,onTouchmove:P(a.clear,["stop","prevent"])},{default:d((()=>[T(e.$slots,"default",{},void 0,!0)])),_:3},8,["show","duration","custom-style","onClick","onTouchmove"])}],["__scopeId","data-v-9cc2c1b3"]]);const me=ee({name:"uv-status-bar",mixins:[Q,B,{props:{bgColor:{type:String,default:"transparent"}}}],data:()=>({}),computed:{style(){const e={};return e.height=this.$uv.addUnit(this.$uv.sys().statusBarHeight,"px"),this.bgColor&&(this.bgColor.indexOf("gradient")>-1?e.backgroundImage=this.bgColor:e.background=this.bgColor),this.$uv.deepMerge(e,this.$uv.addStyle(this.customStyle))}}},[["render",function(e,t,s,o,i,a){const l=v;return c(),u(l,{style:g([a.style]),class:"uv-status-bar"},{default:d((()=>[T(e.$slots,"default",{},void 0,!0)])),_:3},8,["style"])}],["__scopeId","data-v-08fe2518"]]);const fe=ee({name:"uv-safe-bottom",mixins:[Q,B],data:()=>({safeAreaBottomHeight:0,isNvue:!1}),computed:{style(){return this.$uv.deepMerge({},this.$uv.addStyle(this.customStyle))}},mounted(){}},[["render",function(e,t,s,o,i,a){const l=v;return c(),u(l,{class:y(["uv-safe-bottom",[!i.isNvue&&"uv-safe-area-inset-bottom"]]),style:g([a.style])},null,8,["style","class"])}],["__scopeId","data-v-8065622b"]]);const ge=ee({name:"uv-popup",components:{keypress:{name:"Keypress",props:{disable:{type:Boolean,default:!1}},mounted(){const e={esc:["Esc","Escape"],tab:"Tab",enter:"Enter",space:[" ","Spacebar"],up:["Up","ArrowUp"],left:["Left","ArrowLeft"],right:["Right","ArrowRight"],down:["Down","ArrowDown"],delete:["Backspace","Delete","Del"]};document.addEventListener("keyup",(t=>{if(this.disable)return;const s=Object.keys(e).find((s=>{const o=t.key,i=e[s];return i===o||Array.isArray(i)&&i.includes(o)}));s&&setTimeout((()=>{this.$emit(s,{})}),0)}))},render:()=>{}}},mixins:[Q,B],emits:["change","maskClick"],props:{mode:{type:String,default:"center"},duration:{type:[String,Number],default:300},zIndex:{type:[String,Number],default:997},bgColor:{type:String,default:"#ffffff"},safeArea:{type:Boolean,default:!0},overlay:{type:Boolean,default:!0},closeOnClickOverlay:{type:Boolean,default:!0},overlayOpacity:{type:[Number,String],default:.4},overlayStyle:{type:[Object,String],default:""},safeAreaInsetBottom:{type:Boolean,default:!0},safeAreaInsetTop:{type:Boolean,default:!1},closeable:{type:Boolean,default:!1},closeIconPos:{type:String,default:"top-right"},zoom:{type:Boolean,default:!0},round:{type:[Number,String],default:0},...null==(a=null==(i=uni.$uv)?void 0:i.props)?void 0:a.popup},watch:{type:{handler:function(e){this.config[e]&&this[this.config[e]](!0)},immediate:!0},isDesktop:{handler:function(e){this.config[e]&&this[this.config[this.mode]](!0)},immediate:!0},showPopup(e){document.getElementsByTagName("body")[0].style.overflow=e?"hidden":"visible"}},data(){return{ani:[],showPopup:!1,showTrans:!1,popupWidth:0,popupHeight:0,config:{top:"top",bottom:"bottom",center:"center",left:"left",right:"right",message:"top",dialog:"center",share:"bottom"},transitionStyle:{position:"fixed",left:0,right:0},maskShow:!0,mkclick:!0,popupClass:this.isDesktop?"fixforpc-top":"top",direction:""}},computed:{isDesktop(){return this.popupWidth>=500&&this.popupHeight>=500},bg(){return""===this.bgColor||"none"===this.bgColor||this.$uv.getPx(this.round)>0?"transparent":this.bgColor},contentStyle(){const e={};if(this.bgColor&&(e.backgroundColor=this.bg),this.round){const t=this.$uv.addUnit(this.round),s=this.direction?this.direction:this.mode;e.backgroundColor=this.bgColor,"top"===s?(e.borderBottomLeftRadius=t,e.borderBottomRightRadius=t):"bottom"===s?(e.borderTopLeftRadius=t,e.borderTopRightRadius=t):"center"===s&&(e.borderRadius=t)}return this.$uv.deepMerge(e,this.$uv.addStyle(this.customStyle))}},unmounted(){this.setH5Visible()},created(){this.messageChild=null,this.clearPropagation=!1},methods:{setH5Visible(){document.getElementsByTagName("body")[0].style.overflow="visible"},closeMask(){this.maskShow=!1},clear(e){e.stopPropagation(),this.clearPropagation=!0},open(e){if(this.showPopup)return;if(e&&-1!==["top","center","bottom","left","right","message","dialog","share"].indexOf(e)?this.direction=e:e=this.mode,!this.config[e])return this.$uv.error(`缺少类型：${e}`);this[this.config[e]](),this.$emit("change",{show:!0,type:e})},close(e){this.showTrans=!1,this.$emit("change",{show:!1,type:this.mode}),clearTimeout(this.timer),this.timer=setTimeout((()=>{this.showPopup=!1}),300)},touchstart(){this.clearPropagation=!1},onTap(){this.clearPropagation?this.clearPropagation=!1:(this.$emit("maskClick"),this.closeOnClickOverlay&&this.close())},top(e){this.popupClass=this.isDesktop?"fixforpc-top":"top",this.ani=["slide-top"],this.transitionStyle={position:"fixed",zIndex:this.zIndex,left:0,right:0,backgroundColor:this.bg},e||(this.showPopup=!0,this.showTrans=!0,this.$nextTick((()=>{this.messageChild&&"message"===this.mode&&this.messageChild.timerClose()})))},bottom(e){this.popupClass="bottom",this.ani=["slide-bottom"],this.transitionStyle={position:"fixed",zIndex:this.zIndex,left:0,right:0,bottom:0,backgroundColor:this.bg},e||(this.showPopup=!0,this.showTrans=!0)},center(e){this.popupClass="center",this.ani=this.zoom?["zoom-in","fade"]:["fade"],this.transitionStyle={position:"fixed",zIndex:this.zIndex,display:"flex",flexDirection:"column",bottom:0,left:0,right:0,top:0,justifyContent:"center",alignItems:"center"},e||(this.showPopup=!0,this.showTrans=!0)},left(e){this.popupClass="left",this.ani=["slide-left"],this.transitionStyle={position:"fixed",zIndex:this.zIndex,left:0,bottom:0,top:0,backgroundColor:this.bg,display:"flex",flexDirection:"column"},e||(this.showPopup=!0,this.showTrans=!0)},right(e){this.popupClass="right",this.ani=["slide-right"],this.transitionStyle={position:"fixed",zIndex:this.zIndex,bottom:0,right:0,top:0,backgroundColor:this.bg,display:"flex",flexDirection:"column"},e||(this.showPopup=!0,this.showTrans=!0)}}},[["render",function(e,t,s,o,i,a){const l=te(C("uv-overlay"),he),n=te(C("uv-status-bar"),me),r=te(C("uv-safe-bottom"),fe),p=te(C("uv-icon"),se),h=v,k=te(C("uv-transition"),pe),_=F("keypress");return i.showPopup?(c(),u(h,{key:0,class:y(["uv-popup",[i.popupClass,a.isDesktop?"fixforpc-z-index":""]]),style:g([{zIndex:s.zIndex}])},{default:d((()=>[f(h,{onTouchstart:a.touchstart},{default:d((()=>[i.maskShow&&s.overlay?(c(),u(l,{key:"1",show:i.showTrans,duration:s.duration,"custom-style":s.overlayStyle,opacity:s.overlayOpacity,zIndex:s.zIndex,onClick:a.onTap},null,8,["show","duration","custom-style","opacity","zIndex","onClick"])):m("",!0),f(k,{key:"2",mode:i.ani,name:"content","custom-style":i.transitionStyle,duration:s.duration,show:i.showTrans,onClick:a.onTap},{default:d((()=>[f(h,{class:y(["uv-popup__content",[i.popupClass]]),style:g([a.contentStyle]),onClick:a.clear},{default:d((()=>[s.safeAreaInsetTop?(c(),u(n,{key:0})):m("",!0),T(e.$slots,"default",{},void 0,!0),s.safeAreaInsetBottom?(c(),u(r,{key:1})):m("",!0),s.closeable?(c(),u(h,{key:2,onClick:P(a.close,["stop"]),class:y(["uv-popup__content__close",["uv-popup__content__close--"+s.closeIconPos]]),"hover-class":"uv-popup__content__close--hover","hover-stay-time":"150"},{default:d((()=>[f(p,{name:"close",color:"#909399",size:"18",bold:""})])),_:1},8,["onClick","class"])):m("",!0)])),_:3},8,["style","class","onClick"])])),_:3},8,["mode","custom-style","duration","show","onClick"])])),_:3},8,["onTouchstart"]),i.maskShow?(c(),u(_,{key:0,onEsc:a.onTap},null,8,["onEsc"])):m("",!0)])),_:3},8,["class","style"])):m("",!0)}],["__scopeId","data-v-db11292f"]]);const ye=ee({name:"chat-script",data:()=>({keyword:"",dataList:[]}),methods:{itemClick(e){this.$emit("quickreply",e)},sous(){this.$refs.paging.reload()},open(){this.$refs.ScriptPopup.open()},close(){this.$refs.ScriptPopup.close()},queryList(e,t){const s={page_no:e,page_size:t,keyword:this.keyword};R(s).then((e=>{console.log(e),this.$refs.paging.complete(e.data.list),console.log(this.dataList)})).catch((e=>{this.$refs.paging.complete(!1)}))}}},[["render",function(e,t,s,o,i,a){const l=te(C("uv-search"),re),n=v,r=te(C("z-paging"),ie),m=te(C("uv-popup"),ge);return c(),u(n,null,{default:d((()=>[f(m,{mode:"bottom",round:"10",ref:"ScriptPopup"},{default:d((()=>[f(n,{style:{height:"70vh",background:"#F5F5F5"}},{default:d((()=>[f(n,{class:"display-flex flex-direction-column"},{default:d((()=>[f(n,{style:{padding:"20rpx"},class:"file-1"},{default:d((()=>[f(r,{ref:"paging",modelValue:i.dataList,"onUpdate:modelValue":t[1]||(t[1]=e=>i.dataList=e),onQuery:a.queryList},{top:d((()=>[f(n,{style:{background:"#FFFFFF",padding:"20rpx"}},{default:d((()=>[f(l,{placeholder:"请输入搜索内容",onCustom:a.sous,modelValue:i.keyword,"onUpdate:modelValue":t[0]||(t[0]=e=>i.keyword=e)},null,8,["onCustom","modelValue"])])),_:1})])),default:d((()=>[(c(!0),x(S,null,A(i.dataList,((e,t)=>(c(),u(n,{class:"item",key:t,onClick:s=>a.itemClick(e,t)},{default:d((()=>[f(n,{class:"font-size-26"},{default:d((()=>[p("标题："+h(e.title),1)])),_:2},1024),f(n,{class:"font-size-26"},{default:d((()=>[p("回复内容："+h(e.content),1)])),_:2},1024),f(n,{class:"item-line"})])),_:2},1032,["onClick"])))),128))])),_:1},8,["modelValue","onQuery"])])),_:1})])),_:1})])),_:1})])),_:1},512)])),_:1})}],["__scopeId","data-v-d0f321fb"]]);const ke=ee({name:"uv-tags",emits:["click","close"],mixins:[Q,B,{props:{type:{type:String,default:"primary"},disabled:{type:[Boolean,String],default:!1},size:{type:String,default:"medium"},shape:{type:String,default:"square"},text:{type:[String,Number],default:""},bgColor:{type:String,default:""},color:{type:String,default:""},borderColor:{type:String,default:""},name:{type:[String,Number],default:""},plainFill:{type:Boolean,default:!1},plain:{type:Boolean,default:!1},closable:{type:Boolean,default:!1},closeColor:{type:String,default:"#C6C7CB"},closePlace:{type:String,default:"right-top"},show:{type:Boolean,default:!0},icon:{type:String,default:""},iconColor:{type:String,default:""},cellChild:{type:Boolean,default:!1},...null==(n=null==(l=uni.$uv)?void 0:l.props)?void 0:n.tags}}],computed:{style(){const e={};return this.bgColor&&(e.backgroundColor=this.bgColor),this.color&&(e.color=this.color),this.borderColor&&(e.borderColor=this.borderColor),e},textColor(){const e={};return this.color&&(e.color=this.color),e},imgStyle(){const e="large"===this.size?"17px":"medium"===this.size?"15px":"13px";return{width:e,height:e}},closeSize(){return"large"===this.size?15:"medium"===this.size?13:12},iconSize(){return"large"===this.size?21:"medium"===this.size?19:16},elIconColor(){return this.iconColor?this.iconColor:this.plain?this.type:"#ffffff"}},methods:{closeHandler(){this.$emit("close",this.name)},clickHandler(){this.$emit("click",this.name)}}},[["render",function(e,t,s,o,i,a){const l=_,n=te(C("uv-icon"),se),r=v,b=k,I=te(C("uv-transition"),pe);return c(),u(I,{mode:"fade",show:e.show,"cell-child":e.cellChild},{default:d((()=>[f(r,{class:"uv-tags-wrapper"},{default:d((()=>[f(r,{class:y(["uv-tags",[`uv-tags--${e.shape}`,!e.plain&&`uv-tags--${e.type}`,e.plain&&`uv-tags--${e.type}--plain`,`uv-tags--${e.size}`,`uv-tags--${e.size}--${e.closePlace}`,e.plain&&e.plainFill&&`uv-tags--${e.type}--plain--fill`]]),onClick:P(a.clickHandler,["stop"]),style:g([{marginRight:e.closable&&"right-top"==e.closePlace?"10px":0,marginTop:e.closable&&"right-top"==e.closePlace?"10px":0},a.style,e.$uv.addStyle(e.customStyle)])},{default:d((()=>[T(e.$slots,"icon",{},(()=>[e.icon?(c(),u(r,{key:0,class:"uv-tags__icon"},{default:d((()=>[e.$uv.test.image(e.icon)?(c(),u(l,{key:0,src:e.icon,style:g([a.imgStyle])},null,8,["src","style"])):(c(),u(n,{key:1,color:a.elIconColor,name:e.icon,size:a.iconSize},null,8,["color","name","size"]))])),_:1})):m("",!0)]),!0),f(b,{class:y(["uv-tags__text",[`uv-tags__text--${e.type}`,e.plain&&`uv-tags__text--${e.type}--plain`,`uv-tags__text--${e.size}`]]),style:g([a.textColor])},{default:d((()=>[p(h(e.text),1)])),_:1},8,["style","class"]),e.closable&&"right"==e.closePlace?(c(),u(r,{key:0,class:y(["uv-tags__close",[`uv-tags__close--${e.size}`,`uv-tags__close--${e.closePlace}`]]),onClick:P(a.closeHandler,["stop"]),style:g({backgroundColor:e.closeColor})},{default:d((()=>[f(n,{name:"close",size:a.closeSize,color:"#ffffff"},null,8,["size"])])),_:1},8,["class","onClick","style"])):m("",!0)])),_:3},8,["class","onClick","style"]),e.closable&&"right-top"==e.closePlace?(c(),u(r,{key:0,class:y(["uv-tags__close",[`uv-tags__close--${e.size}`,`uv-tags__close--${e.closePlace}`]]),onClick:P(a.closeHandler,["stop"]),style:g({backgroundColor:e.closeColor})},{default:d((()=>[f(n,{name:"close",size:a.closeSize,color:"#ffffff"},null,8,["size"])])),_:1},8,["class","onClick","style"])):m("",!0)])),_:3})])),_:3},8,["show","cell-child"])}],["__scopeId","data-v-fe23ee2d"]]);const ve=ee({name:"chat-order",props:{userId:{type:[Number,String],default:36}},data:()=>({keyword:"",dataList:[]}),methods:{itemClick(e){this.$emit("quickreply",e)},sous(){this.$refs.paging.reload()},open(){this.$refs.ScriptPopup.open()},close(){this.$refs.ScriptPopup.close()},queryList(e,t){const s={page_no:e,page_size:t,user_id:this.userId,order_sn:this.keyword};$(s).then((e=>{console.log(e),this.$refs.paging.complete(e.data.list),console.log(this.dataList)})).catch((e=>{this.$refs.paging.complete(!1)}))}}},[["render",function(e,t,s,o,i,a){const l=te(C("uv-search"),re),n=v,r=_,m=te(C("uv-tags"),ke),g=te(C("z-paging"),ie),y=te(C("uv-popup"),ge);return c(),u(n,null,{default:d((()=>[f(y,{mode:"bottom",round:"10",ref:"ScriptPopup"},{default:d((()=>[f(n,{style:{height:"80vh",background:"#F5F5F5"}},{default:d((()=>[f(n,{class:"display-flex flex-direction-column"},{default:d((()=>[f(n,{style:{padding:"20rpx"},class:"file-1"},{default:d((()=>[f(g,{ref:"paging",modelValue:i.dataList,"onUpdate:modelValue":t[1]||(t[1]=e=>i.dataList=e),onQuery:a.queryList},{top:d((()=>[f(n,{style:{background:"#FFFFFF",padding:"20rpx"}},{default:d((()=>[f(l,{placeholder:"请输入订单号搜索",onCustom:a.sous,modelValue:i.keyword,"onUpdate:modelValue":t[0]||(t[0]=e=>i.keyword=e)},null,8,["onCustom","modelValue"])])),_:1})])),default:d((()=>[(c(!0),x(S,null,A(i.dataList,((e,t)=>(c(),u(n,{class:"item",key:t,onClick:s=>a.itemClick(e,t)},{default:d((()=>[f(n,{class:""},{default:d((()=>[(c(!0),x(S,null,A(e.order_goods,((e,t)=>(c(),u(n,{class:"display-flex align-items"},{default:d((()=>[f(r,{src:e.image,style:{width:"130rpx",height:"130rpx"},mode:"aspectFill"},null,8,["src"]),f(n,{class:"margin-left-20"},{default:d((()=>[f(n,{class:"font-size-26 webkit-line-clamp",style:{"-webkit-line-clamp":"1"}},{default:d((()=>[p(h(e.goods_name),1)])),_:2},1024),f(n,{class:"font-size-24 color-999 margin-top-10"},{default:d((()=>[p(h(e.spec_value),1)])),_:2},1024),f(n,{class:"margin-top-10 display-flex space-between align-items"},{default:d((()=>[f(n,{class:"font-size-24"},{default:d((()=>[p(" ￥"+h(e.goods_price),1)])),_:2},1024),f(n,{class:"font-size-24 color-999"},{default:d((()=>[p(" x"+h(e.goods_num),1)])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1024)))),256)),f(n,{class:"margin-top-10 display-flex align-items"},{default:d((()=>[f(n,{class:"font-size-24 color-999"},{default:d((()=>[p(" 订单类型： ")])),_:1}),f(n,{class:"font-size-24 file-1"},{default:d((()=>[p(h(e.order_type_text),1)])),_:2},1024)])),_:2},1024),f(n,{class:"margin-top-10 display-flex align-items"},{default:d((()=>[f(n,{class:"font-size-24 color-999"},{default:d((()=>[p(" 订单编号： ")])),_:1}),f(n,{class:"font-size-24 file-1"},{default:d((()=>[p(h(e.order_sn),1)])),_:2},1024)])),_:2},1024),f(n,{class:"margin-top-10 display-flex align-items"},{default:d((()=>[f(n,{class:"font-size-24 color-999"},{default:d((()=>[p(" 订单状态： ")])),_:1}),f(n,{class:"font-size-24 display-flex file-1"},{default:d((()=>[f(m,{text:e.order_status_text,plain:"",size:"mini"},null,8,["text"])])),_:2},1024)])),_:2},1024),f(n,{class:"margin-top-10 display-flex align-items"},{default:d((()=>[f(n,{class:"font-size-24 color-999"},{default:d((()=>[p(" 订单金额： ")])),_:1}),f(n,{class:"font-size-24 file-1"},{default:d((()=>[p(" ￥"+h(e.order_amount),1)])),_:2},1024)])),_:2},1024),f(n,{class:"margin-top-10 display-flex align-items"},{default:d((()=>[f(n,{class:"font-size-24 color-999"},{default:d((()=>[p(" 下单时间： ")])),_:1}),f(n,{class:"font-size-24 file-1"},{default:d((()=>[p(h(e.create_time),1)])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1032,["onClick"])))),128))])),_:1},8,["modelValue","onQuery"])])),_:1})])),_:1})])),_:1})])),_:1},512)])),_:1})}],["__scopeId","data-v-2a9375be"]]);const Ce=ee({name:"u-previewVideo",props:{imageArr:{type:Array,default:[]},current:{type:[Number,String],default:0}},data:()=>({showVideoPreview:!1,currentVideoUrl:"https://huohanghang.oss-cn-beijing.aliyuncs.com/uploads/user/81/20250611/20250611161604bbbf88244.mp4"}),methods:{handleFullscreen(e){console.log(e)},videoError(e){console.log(e)},open(){this.showVideoPreview=!this.showVideoPreview}}},[["render",function(e,t,s,o,i,a){const l=_,n=V,r=v,g=O,y=J,k=te(C("uv-overlay"),he);return c(),u(k,{opacity:"1",show:i.showVideoPreview,onClick:t[1]||(t[1]=e=>i.showVideoPreview=!1)},{default:d((()=>[f(r,{class:"position-relative"},{default:d((()=>[f(y,{current:s.current,onChange:t[0]||(t[0]=e=>s.current=e.detail.current),style:{width:"750rpx",height:"100vh"}},{default:d((()=>[(c(!0),x(S,null,A(s.imageArr,((e,t)=>(c(),u(g,{key:t,style:{width:"100%",height:"100%"}},{default:d((()=>[f(r,{style:{width:"100%",height:"100%"},class:"display-flex center align-items"},{default:d((()=>[2==e.msg_type?(c(),u(l,{key:0,style:{width:"750rpx"},src:e.msg,mode:"widthFix"},null,8,["src"])):m("",!0),5==e.msg_type?(c(),u(n,{key:1,style:{width:"750rpx",height:"90vh"},src:e.msg,"enable-progress-gesture":!1,"show-fullscreen-btn":!1,onFullscreenchange:a.handleFullscreen,onError:a.videoError},null,8,["src","onFullscreenchange","onError"])):m("",!0)])),_:2},1024)])),_:2},1024)))),128))])),_:1},8,["current"]),f(r,{style:{left:"50%",top:"5%","z-index":"99999"},class:"transform-translate-center position-absolute font-size-26 color-FFF"},{default:d((()=>[p(h(s.current+1)+"/"+h(s.imageArr.length),1)])),_:1})])),_:1})])),_:1},8,["show"])}]]),_e=ee({__name:"chat",setup(e){const t=H(),s=q(null),o=q(null),i=q([]),a=q(""),l=q({}),n=q(null),r=q(null),m=q(null),g=q("ending"),y=q(-1),k=q([]),b=q(null),I=q(0);function w(e){console.log(e),r.value.close(),B(e.content,1)}X((()=>{t.offEnded(),t.stop(),y.value=-1,g.value="ending"})),oe((function(e){a.value=e.userid,W({title:e.name}),N("addChatRecordData"),U("addChatRecordData",(e=>{!function(e){s.value.addChatRecordData(e)}(e)})),async function(e){const{code:t,data:s}=await G({user_id:e});1==t&&(l.value=s,W({title:s.nickname}))}(e.userid)}));const z=(e,t)=>{const o={user_id:a.value,page_no:e,page_size:t};K(o).then((e=>{s.value.complete(e.data.list.reverse())})).catch((e=>{s.value.complete(!1)}))},E=e=>{o.value.updateKeyboardHeightChange(e)},Q=()=>{o.value.hidedKeyboard()},B=(e,t)=>{let s={msg:e,msg_type:t,to_id:a.value,to_type:"user"};Z().globalData.socket.send({event:"chat",data:s})};async function T(e){if(D({title:"上传中...",mask:!0}),5==e.msg_type)!async function(e){const{code:t,data:s}=await L(e);1==t&&B(s.uri,5);await Y()}(e.tempFilePath);else for(var t=0;t<e.tempFilePaths.length;t++){P(e.tempFilePaths[t])}}async function P(e){const{code:t,data:s}=await M(e);1==t&&B(s.uri,2),await Y()}return(e,P)=>{const j=te(C("chat-item"),le),F=v,R=te(C("chat-input-bar"),ne),$=te(C("z-paging"),ie),V=te(C("chat-script"),ye),O=te(C("chat-order"),ve),J=_,H=te(C("uv-popup"),ge),q=te(C("u-previewVideo"),Ce);return c(),u(F,{class:"content"},{default:d((()=>[f($,{ref_key:"chatpaging",ref:s,modelValue:i.value,"onUpdate:modelValue":P[3]||(P[3]=e=>i.value=e),"use-chat-record-mode":"","safe-area-inset-bottom":"","bottom-bg-color":"#FFFFFF",onQuery:z,onKeyboardHeightChange:E,onHidedKeyboard:Q},{bottom:d((()=>[f(F,{class:"display-flex",style:{padding:"10rpx 0"}},{default:d((()=>[f(F,{onClick:P[0]||(P[0]=e=>r.value.open()),class:"label"},{default:d((()=>[p(" 快捷回复 ")])),_:1}),f(F,{onClick:P[1]||(P[1]=e=>n.value.open()),class:"label"},{default:d((()=>[p(" 用户资料 ")])),_:1}),f(F,{onClick:P[2]||(P[2]=e=>m.value.open()),class:"label"},{default:d((()=>[p(" 订单信息 ")])),_:1})])),_:1}),f(R,{ref_key:"inputBar",ref:o,onUploadFiles:T,onSend:B},null,512)])),default:d((()=>[(c(!0),x(S,null,A(i.value,((e,s)=>(c(),u(F,{key:s,style:{position:"relative"}},{default:d((()=>[f(F,{style:{transform:"scaleY(-1)"}},{default:d((()=>[f(j,{onPreviewVideo:e=>function(e){console.log(e);let t=i.value.filter((e=>5==e.msg_type||2==e.msg_type)).reverse();I.value=t.findIndex((t=>t.id==e.id)),k.value=t,b.value.open()}(e),onHandleMessageAudioClick:e=>function(e,s){console.log(e,s);let o=e.msg;console.log(i.value[s]),s==y.value?"playing"==g.value?(g.value="pause",i.value[s].isPlay=!1,t.pause()):"ending"==g.value?(t.offEnded(),i.value[s].isPlay=!0,t.play()):"pause"==g.value&&(i.value[s].isPlay=!0,t.play(),g.value="playing"):(y.value=s,i.value.forEach((e=>{e.isPlay=!1})),i.value[s].isPlay=!0,t.offEnded(),t.stop(),t.src=o,t.autoplay=!0,t.play(),g.value="playing"),t.onEnded((()=>{console.log("播放结束"),i.value[y.value].isPlay=!1,g.value="ending",t.seek(0),y.value=-1}))}(e,s),item:e},null,8,["onPreviewVideo","onHandleMessageAudioClick","item"])])),_:2},1024)])),_:2},1024)))),128))])),_:1},8,["modelValue"]),f(V,{ref_key:"chatscript",ref:r,onQuickreply:w},null,512),f(O,{userId:a.value,ref_key:"chatorder",ref:m},null,8,["userId"]),f(H,{mode:"bottom",round:"10",ref_key:"userPopup",ref:n},{default:d((()=>[f(F,{style:{height:"30vh"}},{default:d((()=>[f(F,{class:"padding-about-20 padding-top-30 font-size-26"},{default:d((()=>[f(F,{class:"display-flex align-items"},{default:d((()=>[f(J,{src:l.value.avatar,style:{width:"100rpx",height:"100rpx"},mode:"aspectFill"},null,8,["src"]),f(F,{class:"font-size-32 margin-left-20 font-weight-bold"},{default:d((()=>[p(h(l.value.nickname),1)])),_:1})])),_:1}),f(F,{class:"margin-top-10"},{default:d((()=>[p(" 用户编号： "+h(l.value.sn),1)])),_:1}),f(F,{class:"margin-top-10"},{default:d((()=>[p(" 手机号码："+h(l.value.mobile),1)])),_:1}),f(F,{class:"margin-top-10"},{default:d((()=>[p(" 累计消费："+h(l.value.total_order_amount),1)])),_:1}),f(F,{class:"margin-top-10"},{default:d((()=>[p(" 注册时间："+h(l.value.create_time),1)])),_:1})])),_:1})])),_:1})])),_:1},512),f(q,{ref_key:"openView",ref:b,current:I.value,imageArr:k.value},null,8,["current","imageArr"])])),_:1})}}},[["__scopeId","data-v-6d2a7114"]]);export{_e as default};
