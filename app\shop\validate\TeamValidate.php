<?php


namespace app\shop\validate;


use app\common\basics\Validate;

class TeamValidate extends Validate
{
    protected $rule = [
        'id'                  => 'require',
        'goods_id'            => 'require',
        'activity_start_time' => 'require',
        'activity_end_time'   => 'require',
        'agreement'   => 'require',
    ];

    protected $message = [
        'id.require'                  => '缺少id字段',
        'goods_id.require'            => '请选择商品',
        'activity_start_time.require' => '请选择团开始时间',
        'activity_end_time.require'   => '请选择团结束时间',
        'agreement.require'   => '请阅读协议内容并勾选同意',
    ];

    protected $scene = [
        'id'   => ['id'],
        'add'  => ['goods_id', 'people_num', 'effective_time', 'activity_start_time', 'activity_end_time','agreement'],
        'edit' => ['id', 'goods_id', 'people_num', 'effective_time', 'activity_start_time', 'activity_end_time'],
    ];
}