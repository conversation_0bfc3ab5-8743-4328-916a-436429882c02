(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["bundle-pages-bargain_code-bargain_code"],{"0537":function(t,e,a){var n=a("24fb");e=n(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */uni-page-body[data-v-14038cf4]{\n  /* 定义一些主题色及基础样式 */font-family:PingFang SC,Arial,Hiragino Sans GB,Microsoft YaHei,sans-serif;font-size:%?28?%;color:#333;padding-bottom:env(safe-area-inset-bottom);background-color:#f6f6f6}body.?%PAGE?%[data-v-14038cf4]{background-color:#f6f6f6}.bold[data-v-14038cf4]{font-weight:700}\n/* 定义字体颜色 */.primary[data-v-14038cf4]{color:#ff2c3c}.bg-primary[data-v-14038cf4]{background-color:#ff2c3c}.bg-white[data-v-14038cf4]{background-color:#fff}.bg-body[data-v-14038cf4]{background-color:#f6f6f6}.bg-gray[data-v-14038cf4]{background-color:#e5e5e5}.black[data-v-14038cf4]{color:#101010}.white[data-v-14038cf4]{color:#fff}.normal[data-v-14038cf4]{color:#333}.lighter[data-v-14038cf4]{color:#666}.muted[data-v-14038cf4]{color:#999}\n/* 定义字体大小 */.xxl[data-v-14038cf4]{font-size:%?36?%}.xl[data-v-14038cf4]{font-size:%?34?%}.lg[data-v-14038cf4]{font-size:%?32?%}.md[data-v-14038cf4]{font-size:%?30?%}.nr[data-v-14038cf4]{font-size:%?28?%}.sm[data-v-14038cf4]{font-size:%?26?%}.xs[data-v-14038cf4]{font-size:%?24?%}.xxs[data-v-14038cf4]{font-size:%?22?%}\n/* 定义常用外边距 */.ml5[data-v-14038cf4]{margin-left:%?5?%}.ml10[data-v-14038cf4]{margin-left:%?10?%}.ml20[data-v-14038cf4]{margin-left:%?20?%}.ml30[data-v-14038cf4]{margin-left:%?30?%}.mr5[data-v-14038cf4]{margin-right:%?5?%}.mr10[data-v-14038cf4]{margin-right:%?10?%}.mr20[data-v-14038cf4]{margin-right:%?20?%}.mr30[data-v-14038cf4]{margin-right:%?30?%}.mt5[data-v-14038cf4]{margin-top:%?5?%}.mt10[data-v-14038cf4]{margin-top:%?10?%}.mt20[data-v-14038cf4]{margin-top:%?20?%}.mt30[data-v-14038cf4]{margin-top:%?30?%}.mb5[data-v-14038cf4]{margin-bottom:%?5?%}.mb10[data-v-14038cf4]{margin-bottom:%?10?%}.mb20[data-v-14038cf4]{margin-bottom:%?20?%}.mb30[data-v-14038cf4]{margin-bottom:%?30?%}\n/* 定义常用的弹性布局 */.flex1[data-v-14038cf4]{flex:1}.flexnone[data-v-14038cf4]{flex:none}.wrap[data-v-14038cf4]{flex-wrap:wrap}.row[data-v-14038cf4]{display:flex;align-items:center}.row-center[data-v-14038cf4]{display:flex;align-items:center;justify-content:center}.row-end[data-v-14038cf4]{display:flex;align-items:center;justify-content:flex-end}.row-between[data-v-14038cf4]{display:flex;align-items:center;justify-content:space-between}.row-around[data-v-14038cf4]{display:flex;align-items:center;justify-content:space-around}.column[data-v-14038cf4]{display:flex;flex-direction:column;justify-content:center}.column-center[data-v-14038cf4]{display:flex;flex-direction:column;align-items:center;justify-content:center}.column-around[data-v-14038cf4]{display:flex;flex-direction:column;align-items:center;justify-content:space-around}.column-end[data-v-14038cf4]{display:flex;flex-direction:column;align-items:center;justify-content:flex-end}.column-between[data-v-14038cf4]{display:flex;flex-direction:column;align-items:center;justify-content:space-between}\n/* 超出隐藏 */.line1[data-v-14038cf4]{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.line-1[data-v-14038cf4]{word-break:break-all;display:-webkit-box;-webkit-line-clamp:1;-webkit-box-orient:vertical;overflow:hidden}.line2[data-v-14038cf4]{word-break:break-all;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical;overflow:hidden}\n/* 中划线 */.line-through[data-v-14038cf4]{text-decoration:line-through}\n/* br60 */.br60[data-v-14038cf4]{border-radius:%?60?%}\n/* 初始化按钮 */uni-page-body uni-button[data-v-14038cf4]{padding:0;margin:0;background-color:initial;font-weight:400;font-size:%?28?%;overflow:unset;margin-left:0;margin-right:0}uni-page-body uni-button[data-v-14038cf4]::after{border:none}uni-button[type=primary][data-v-14038cf4]{background-color:#ff2c3c}.button-hover[type=primary][data-v-14038cf4]{background-color:#ff2c3c}uni-button[disabled][type=primary][data-v-14038cf4]{background-color:#ff2c3c}\n/* 按钮大小 */uni-button[size="xs"][data-v-14038cf4]{line-height:%?58?%;height:%?58?%;font-size:%?26?%;padding:0 %?30?%}uni-button[size="sm"][data-v-14038cf4]{line-height:%?62?%;height:%?62?%;font-size:%?28?%;padding:0 %?30?%}uni-button[size="md"][data-v-14038cf4]{line-height:%?70?%;height:%?70?%;font-size:%?30?%;padding:0 %?30?%}uni-button[size="lg"][data-v-14038cf4]{line-height:%?80?%;height:%?80?%;font-size:%?32?%;padding:0 %?30?%}.icon-xs[data-v-14038cf4]{min-height:%?28?%;min-width:%?28?%;height:%?28?%;width:%?28?%;vertical-align:middle}.icon-sm[data-v-14038cf4]{min-height:%?30?%;min-width:%?30?%;height:%?30?%;width:%?30?%;vertical-align:middle}.icon[data-v-14038cf4]{min-height:%?34?%;min-width:%?34?%;height:%?34?%;width:%?34?%;vertical-align:middle}.icon-md[data-v-14038cf4]{min-height:%?44?%;min-width:%?44?%;height:%?44?%;width:%?44?%;vertical-align:middle}.icon-lg[data-v-14038cf4]{min-height:%?52?%;min-width:%?52?%;height:%?52?%;width:%?52?%;vertical-align:middle}.icon-xl[data-v-14038cf4]{min-height:%?64?%;min-width:%?64?%;height:%?64?%;width:%?64?%;vertical-align:middle}.icon-xxl[data-v-14038cf4]{min-height:%?120?%;min-width:%?120?%;height:%?120?%;width:%?120?%;vertical-align:middle}.img-null[data-v-14038cf4]{width:%?300?%;height:%?300?%}\n/* 隐藏滚动条 */[data-v-14038cf4]::-webkit-scrollbar{width:0;height:0;color:transparent}.bargain-list-container .bargain-list[data-v-14038cf4]{background-color:#fff}.bargain-list-container .bargain-list .header[data-v-14038cf4]{height:%?80?%;padding:%?20?% %?20?%}.bargain-list-container .bargain-list .content[data-v-14038cf4]{padding:%?30?% %?24?% %?18?%}.bargain-list-container .bargain-list .content .goods-info[data-v-14038cf4]{margin-left:%?24?%;flex:1}.bargain-list-container .bargain-list .content .goods-info .goods-name[data-v-14038cf4]{line-height:%?40?%}.bargain-list-container .bargain-list .footer[data-v-14038cf4]{padding:%?19?% %?24?%}.bargain-list-container .bargain-list .footer .bargain-btn[data-v-14038cf4]{height:%?62?%;padding:0 %?44?%;background-color:#ff2c3c}.bargain-list-container .bargain-list .footer .purchase-btn[data-v-14038cf4]{height:%?62?%;padding:0 %?44?%;background-color:rgba(255,44,60,.1)}.bargain-list-container .bargain-list .footer .footer-btn[data-v-14038cf4]:not(:last-child){margin-right:%?20?%}.bargain-list-container .data-null[data-v-14038cf4]{margin-top:%?150?%}',""]),t.exports=e},"0d41":function(t,e,a){"use strict";a.r(e);var n=a("c829"),i=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(r);e["default"]=i.a},"13c5":function(t,e,a){"use strict";a.r(e);var n=a("6533"),i=a("65de");for(var r in i)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(r);a("36bd");var o=a("f0c5"),c=Object(o["a"])(i["default"],n["b"],n["c"],!1,null,"d7f27a88",null,!1,n["a"],void 0);e["default"]=c.exports},"2ca6":function(t,e,a){"use strict";a("7a82");var n=a("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("a9e3"),a("99af");var i=a("c60f"),r=n(a("bde1")),o=n(a("4316")),c={mixins:[r.default,o.default],data:function(){return{lists:[],downOption:{auto:!1},upOption:{auto:!0,noMoreSize:4,empty:{icon:"/static/images/goods_null.png",tip:"暂无砍价记录~",fixed:!0}}}},props:{bargainType:{type:Number,default:-1}},mounted:function(){this.$getBargainActivityList()},methods:{upCallback:function(t){this.$getBargainActivityList(t.num,t.size)},$getBargainActivityList:function(){var t=this,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10,n=this.bargainType;(0,i.getBargainActivityList)({type:n,page_no:e,page_size:a}).then((function(a){if(a){var n=a.data.list,i=n.length,r=!!a.data.more;1==e&&(t.lists=[]),console.log(r),t.lists=t.lists.concat(n),t.mescroll.endSuccess(i,r)}})).catch((function(){t.mescroll.endErr()}))}}};e.default=c},"36bd":function(t,e,a){"use strict";var n=a("5f08"),i=a.n(n);i.a},4316:function(t,e,a){"use strict";a("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("a9e3");var n={props:{i:Number,index:{type:Number,default:function(){return 0}}},data:function(){return{downOption:{auto:!1},upOption:{auto:!1},isInit:!1}},watch:{index:function(t){this.i!==t||this.isInit||(this.isInit=!0,this.mescroll&&this.mescroll.triggerDownScroll())}},methods:{mescrollInitByRef:function(){if(!this.mescroll||!this.mescroll.resetUpScroll){var t=this.$refs.mescrollRef||this.$refs["mescrollRef"+this.i];t&&(this.mescroll=t.mescroll)}},mescrollInit:function(t){this.mescroll=t,this.mescrollInitByRef&&this.mescrollInitByRef(),this.i===this.index&&(this.isInit=!0,this.mescroll.triggerDownScroll())}}},i=n;e.default=i},"5c5c":function(t,e,a){"use strict";a.r(e);var n=a("ec44"),i=a("e3de");for(var r in i)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(r);a("d26f");var o=a("f0c5"),c=Object(o["a"])(i["default"],n["b"],n["c"],!1,null,"14038cf4",null,!1,n["a"],void 0);e["default"]=c.exports},"5f08":function(t,e,a){var n=a("9d6d");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var i=a("4f06").default;i("7ac327e3",n,!0,{sourceMap:!1,shadowMode:!1})},6533:function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return r})),a.d(e,"a",(function(){return n}));var n={uIcon:a("90f3").default},i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{class:{"custom-image":!0,"image-round":t.round},style:[t.viewStyle],on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onClick.apply(void 0,arguments)}}},[t.error?t._e():a("v-uni-image",{staticClass:"image",attrs:{src:t.src,mode:t.mode,"lazy-load":t.lazyLoad,"show-menu-by-longpress":t.showMenuByLongpress},on:{load:function(e){arguments[0]=e=t.$handleEvent(e),t.onLoaded.apply(void 0,arguments)},error:function(e){arguments[0]=e=t.$handleEvent(e),t.onErrored.apply(void 0,arguments)}}}),t.loading&&t.showLoading?a("v-uni-view",{staticClass:"loading-wrap image"},[t.useLoadingSlot?t._t("loading"):a("u-icon",{attrs:{color:"#aaa",name:"photo-fill",size:"45"}})],2):t._e(),t.error&&t.showError?a("v-uni-view",{staticClass:"error-wrap image"},[t.useErrorSlot?t._t("error"):a("u-icon",{attrs:{color:"#aaa",name:"error-circle-fill",size:"45"}}),a("v-uni-text",{staticClass:"sm"},[t._v("加载失败")])],2):t._e()],1)},r=[]},"65de":function(t,e,a){"use strict";a.r(e);var n=a("815f"),i=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(r);e["default"]=i.a},"815f":function(t,e,a){"use strict";a("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n={props:{src:{type:String},round:Boolean,width:{type:null},height:{type:null},radius:null,lazyLoad:{type:Boolean,default:!0},useErrorSlot:Boolean,useLoadingSlot:Boolean,showMenuByLongpress:Boolean,mode:{type:String,default:"scaleToFill"},showError:{type:Boolean,default:!0},showLoading:{type:Boolean,default:!0},customStyle:{type:Object,default:function(){}}},data:function(){return{error:!1,loading:!0,viewStyle:{}}},created:function(){this.setStyle()},methods:{setStyle:function(){var t=this.width,e=this.height,a=this.radius,n={};t&&(n.width=t),e&&(n.height=e),a&&(n["overflow"]="hidden",n["border-radius"]=a),this.viewStyle=n,this.customStyle&&(this.viewStyle=Object.assign(this.viewStyle,this.customStyle))},onLoaded:function(t){this.loading=!1,this.$emit("load",t.detail)},onErrored:function(t){this.error=!1,this.loading=!0,this.$emit("error",t.detail)},onClick:function(t){this.$emit("click",t.detail)}},watch:{src:function(){this.error=!1,this.loading=!0},width:function(){this.setStyle()},height:function(){this.setStyle()}}};e.default=n},"9d6d":function(t,e,a){var n=a("24fb");e=n(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.custom-image[data-v-d7f27a88]{position:relative;display:block;width:100%;height:100%}.custom-image.image-round[data-v-d7f27a88]{overflow:hidden;border-radius:50%}.custom-image .image[data-v-d7f27a88]{display:block;width:100%;height:100%}.custom-image .loading-wrap[data-v-d7f27a88],\n.custom-image .error-wrap[data-v-d7f27a88]{position:absolute;top:0;left:0;display:flex;flex-direction:column;align-items:center;justify-content:center;color:#969799;font-size:%?28?%;background-color:#f7f8fa}',""]),t.exports=e},ad03:function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return r})),a.d(e,"a",(function(){return n}));var n={tabs:a("741a").default,tab:a("5652").default,bargainList:a("5c5c").default},i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{staticClass:"bargain-code-container"},[a("tabs",{attrs:{active:t.active,isScroll:!1},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.onChange.apply(void 0,arguments)}}},t._l(t.bargain,(function(e,n){return a("tab",{key:e.type,attrs:{name:e.name}},[e.isShow?a("bargain-list",{ref:e.ref_name,refInFor:!0,attrs:{bargainType:e.type}}):t._e()],1)})),1)],1)},r=[]},c60f:function(t,e,a){"use strict";a("7a82");var n=a("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.cancelIntegralOrder=function(t){return i.default.post("integral_order/cancel",{id:t})},e.closeBargainOrder=function(t){return i.default.get("bargain/closeBargain",{params:t})},e.confirmIntegralOrder=function(t){return i.default.post("integral_order/confirm",{id:t})},e.delIntegralOrder=function(t){return i.default.post("integral_order/del",{id:t})},e.getActivityGoodsLists=function(t){return i.default.get("activity_area/activityGoodsList",{params:t})},e.getBargainActivityDetail=function(t){return i.default.get("bargain/bargainDetail",{params:t})},e.getBargainActivityList=function(t){return i.default.get("bargain/orderList",{params:t})},e.getBargainDetail=function(t){return i.default.get("bargain/detail",{params:t})},e.getBargainList=function(t){return i.default.get("bargain/lists",{params:t})},e.getBargainNumber=function(){return i.default.get("bargain/barginNumber")},e.getBargainPost=function(t){return i.default.get("share/shareBargain",{params:t})},e.getCoupon=function(t){return i.default.post("coupon/getCoupon",{coupon_id:t})},e.getCouponList=function(t){return i.default.get("coupon/getCouponList",{params:t})},e.getGroupList=function(t){return i.default.get("team/activity",{params:t})},e.getIntegralGoods=function(t){return i.default.get("integral_goods/lists",{params:t})},e.getIntegralGoodsDetail=function(t){return i.default.get("integral_goods/detail",{params:t})},e.getIntegralOrder=function(t){return i.default.get("integral_order/lists",{params:t})},e.getIntegralOrderDetail=function(t){return i.default.get("integral_order/detail",{params:{id:t}})},e.getIntegralOrderTraces=function(t){return i.default.get("integral_order/orderTraces",{params:{id:t}})},e.getMyCoupon=function(t){return i.default.get("coupon/myCouponList",{params:t})},e.getOrderCoupon=function(t){return i.default.post("coupon/getBuyCouponList",t)},e.getSeckillGoods=function(t){return i.default.get("seckill_goods/getSeckillGoods",{params:t})},e.getSeckillTime=function(){return i.default.get("seckill_goods/getSeckillTime")},e.getSignLists=function(){return i.default.get("sign/lists")},e.getSignRule=function(){return i.default.get("sign/rule")},e.getTeamInfo=function(t){return i.default.get("team/teamInfo",{params:t})},e.getUserGroup=function(t){return i.default.get("team/record",{params:t})},e.helpBargain=function(t){return i.default.post("bargain/knife",t)},e.integralSettlement=function(t){return i.default.get("integral_order/settlement",{params:t})},e.integralSubmitOrder=function(t){return i.default.post("integral_order/submitOrder",t)},e.launchBargain=function(t){return i.default.post("bargain/sponsor",t)},e.teamBuy=function(t){return i.default.post("team/buy",t)},e.teamCheck=function(t){return i.default.post("team/check",t)},e.teamKaiTuan=function(t){return i.default.post("team/kaituan",t)},e.userSignIn=function(){return i.default.get("sign/sign")};var i=n(a("3b33"));a("b08d")},c829:function(t,e,a){"use strict";a("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("c740");var n=a("98a1"),i={data:function(){return{active:0,bargainCodeType:n.bargainType.ALL,bargain:[{name:"全部",type:n.bargainType.ALL,ref_name:"all",isShow:!0},{name:"砍价中",type:n.bargainType.BARGINNING,ref_name:"barginning",isShow:!1},{name:"砍价成功",type:n.bargainType.SUCCESS,ref_name:"success",isShow:!1},{name:"砍价失败",type:n.bargainType.FAIL,ref_name:"fail",isShow:!1}]}},onLoad:function(t){},onReachBottom:function(){var t=this.active,e=this.bargain,a=e[t].ref_name,n=this.$refs[a][0];n.$getBargainActivityList&&n.$getBargainActivityList()},methods:{onChange:function(t){var e=this,a=this.bargain;console.log(t);var n=a[t].ref_name,i=a.findIndex((function(t){return t.ref_name==n}));-1!=i&&(this.bargain[i].isShow=!0,this.active=i),this.$nextTick((function(){console.log(e.$refs,"refs",n),console.log("this.$refs[all]",e.$refs["all"]),e.$refs[n]&&e.$refs[n][0].$getBargainActivityList&&e.$refs[n][0].$getBargainActivityList()}))}}};e.default=i},ca0c:function(t,e,a){var n=a("0537");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var i=a("4f06").default;i("7da2ecc2",n,!0,{sourceMap:!1,shadowMode:!1})},d26f:function(t,e,a){"use strict";var n=a("ca0c"),i=a.n(n);i.a},e3de:function(t,e,a){"use strict";a.r(e);var n=a("2ca6"),i=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(r);e["default"]=i.a},ec44:function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return r})),a.d(e,"a",(function(){return n}));var n={mescrollUni:a("0bbb").default,customImage:a("13c5").default},i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("mescroll-uni",{ref:"mescrollRef",attrs:{top:"80rpx",down:t.downOption,up:t.upOption},on:{down:function(e){arguments[0]=e=t.$handleEvent(e),t.downCallback.apply(void 0,arguments)},up:function(e){arguments[0]=e=t.$handleEvent(e),t.upCallback.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"bargain-list-container"},t._l(t.lists,(function(e,n){return a("v-uni-view",{key:e.id,staticClass:"bargain-list mt20"},[a("v-uni-view",{staticClass:"header row-between"},[a("v-uni-view",{staticClass:"count-down-container row"},[a("v-uni-text",{staticClass:"muted xs",staticStyle:{"margin-left":"8rpx"}},[t._v(t._s(e.shop_name))])],1),a("v-uni-view",{staticClass:"primary sm"},[t._v(t._s(e.status_text))])],1),a("v-uni-navigator",{staticClass:"content row",attrs:{"hover-class":"none",url:"/pages/bundle/bargain_process/bargain_process?bargainId="+e.id}},[a("custom-image",{attrs:{width:"180rpx",height:"180rpx",src:e.image,radius:"10rpx"}}),a("v-uni-view",{staticClass:"goods-info"},[a("v-uni-view",{staticClass:"goods-name line2 nr normal"},[t._v(t._s(e.name))]),a("v-uni-view",{staticClass:"mt10 row"},[a("v-uni-view",{staticClass:"sm primary"},[t._v("已砍至"),a("v-uni-text",{staticClass:"sm",staticStyle:{"font-weight":"500","line-height":"48rpx"}},[t._v("￥"),a("v-uni-text",{staticClass:"xl"},[t._v(t._s(e.current_price))])],1)],1),a("v-uni-view",{staticClass:"xs muted ml20"},[t._v("原价"),a("v-uni-text",{staticStyle:{"text-decoration":"line-through"}},[t._v("¥"+t._s(e.price))])],1)],1),a("v-uni-view",{staticClass:"xxs",staticStyle:{color:"#FFA200","line-height":"30rpx","margin-top":"4rpx"}},[t._v(t._s(e.bargain_tips))])],1)],1),a("v-uni-view",{staticClass:"footer row-between"},[a("v-uni-view",{staticClass:"count-down-container row"},[a("v-uni-text",{staticClass:"muted xs",staticStyle:{"margin-left":"8rpx"}},[t._v("发起时间："+t._s(e.create_time))])],1),""!=e.btn_tips?a("v-uni-navigator",{staticClass:"bargain-btn footer-btn white row-center br60",attrs:{"hover-class":"none",url:"/bundle/pages/bargain_process/bargain_process?bargainId="+e.id}},[t._v(t._s(e.btn_tips))]):t._e()],1)],1)})),1)],1)},r=[]},f16f:function(t,e,a){"use strict";a.r(e);var n=a("ad03"),i=a("0d41");for(var r in i)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(r);var o=a("f0c5"),c=Object(o["a"])(i["default"],n["b"],n["c"],!1,null,null,null,!1,n["a"],void 0);e["default"]=c.exports}}]);