<?php
namespace app\common\command;

use app\admin\logic\PurchaserAllocationLogic;
use app\common\service\PurchaserAllocationService;
use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\facade\Log;

/**
 * 采购人员分配定时任务
 * Class PurchaserAllocationCommand
 * @package app\common\command
 */
class PurchaserAllocationCommand extends Command
{
    protected function configure()
    {
        $this->setName('purchaser:allocate')
            ->setDescription('采购人员分配相关任务')
            ->addArgument('action', null, '操作类型：yearly-check(年度检查), force-reallocate(强制重新分配), stats(统计信息)')
            ->addOption('shop-id', 's', null, '指定商家ID')
            ->addOption('dry-run', 'd', null, '试运行模式，不实际执行分配');
    }

    protected function execute(Input $input, Output $output)
    {
        $action = $input->getArgument('action') ?: 'yearly-check';
        $shopId = $input->getOption('shop-id');
        $dryRun = $input->getOption('dry-run');

        if ($dryRun) {
            $output->writeln('<info>运行在试运行模式，不会实际执行分配操作</info>');
        }

        switch ($action) {
            case 'yearly-check':
                $this->yearlyCheck($output, $dryRun);
                break;
            case 'force-reallocate':
                $this->forceReAllocate($output, $shopId, $dryRun);
                break;
            case 'stats':
                $this->showStatistics($output);
                break;
            default:
                $output->writeln('<error>未知的操作类型：' . $action . '</error>');
                return 1;
        }

        return 0;
    }

    /**
     * 年度检查和重新分配
     * @param Output $output
     * @param bool $dryRun
     */
    protected function yearlyCheck(Output $output, $dryRun = false)
    {
        $output->writeln('<info>开始执行年度采购人员分配检查...</info>');

        // 获取需要重新分配的商家
        $shops = PurchaserAllocationService::getShopsNeedReAllocation();
        
        if (empty($shops)) {
            $output->writeln('<info>没有需要重新分配的商家</info>');
            return;
        }

        $output->writeln('<info>找到 ' . count($shops) . ' 个需要重新分配的商家：</info>');
        
        foreach ($shops as $shop) {
            $output->writeln("  - 商家ID: {$shop['id']}, 名称: {$shop['name']}, 等级: {$shop['tier_level']}");
        }

        if ($dryRun) {
            $output->writeln('<comment>试运行模式，跳过实际分配操作</comment>');
            return;
        }

        // 执行批量重新分配
        $shopIds = array_column($shops, 'id');
        $results = PurchaserAllocationService::batchReAllocate($shopIds);

        $output->writeln('<info>批量重新分配完成：</info>');
        $output->writeln("  - 总数: {$results['total']}");
        $output->writeln("  - 成功: " . count($results['success']));
        $output->writeln("  - 失败: " . count($results['failed']));

        if (!empty($results['failed'])) {
            $output->writeln('<error>失败的商家ID: ' . implode(', ', array_keys($results['failed'])) . '</error>');
            foreach ($results['failed'] as $shopId => $reason) {
                $output->writeln("<error>  - 商家ID: {$shopId}, 失败原因: {$reason}</error>");
            }
        }
    }

    /**
     * 强制重新分配
     * @param Output $output
     * @param int|null $shopId
     * @param bool $dryRun
     */
    protected function forceReAllocate(Output $output, $shopId = null, $dryRun = false)
    {
        if ($shopId) {
            $output->writeln("<info>开始为商家 {$shopId} 执行强制重新分配...</info>");
            
            if ($dryRun) {
                $output->writeln('<comment>试运行模式，跳过实际分配操作</comment>');
                return;
            }

            $result = PurchaserAllocationService::reAllocateForShop($shopId);
            
            if ($result) {
                $output->writeln('<info>重新分配成功</info>');
            } else {
                $output->writeln('<error>重新分配失败</error>');
                $output->writeln('<error>原因: ' . PurchaserAllocationLogic::getError() . '</error>');
            }
        } else {
            $output->writeln('<info>开始为所有商家执行强制重新分配...</info>');
            
            if ($dryRun) {
                $output->writeln('<comment>试运行模式，跳过实际分配操作</comment>');
                return;
            }

            $result = PurchaserAllocationService::yearlyReAllocation();
            
            if ($result) {
                $output->writeln('<info>全部重新分配成功</info>');
            } else {
                $output->writeln('<error>全部重新分配失败</error>');
                $output->writeln('<error>原因: ' . PurchaserAllocationLogic::getError() . '</error>');
            }
        }
    }

    /**
     * 显示统计信息
     * @param Output $output
     */
    protected function showStatistics(Output $output)
    {
        $output->writeln('<info>采购人员分配统计信息：</info>');

        $stats = PurchaserAllocationService::getAllocationStatistics();
        
        if (empty($stats)) {
            $output->writeln('<error>获取统计信息失败</error>');
            return;
        }

        $output->writeln("总体统计：");
        $output->writeln("  - 总商家数: {$stats['total_shops']}");
        $output->writeln("  - 已分配商家数: {$stats['allocated_shops']}");
        $output->writeln("  - 分配率: {$stats['allocation_rate']}%");

        if (!empty($stats['tier_stats'])) {
            $output->writeln("\n按等级统计：");
            foreach ($stats['tier_stats'] as $tier) {
                $tierName = $this->getTierLevelName($tier['tier_level']);
                $rate = $tier['total_shops'] > 0 ? round(($tier['allocated_shops'] / $tier['total_shops']) * 100, 2) : 0;
                $output->writeln("  - {$tierName}: {$tier['allocated_shops']}/{$tier['total_shops']} ({$rate}%)");
            }
        }

        if (!empty($stats['user_level_stats'])) {
            $output->writeln("\n用户等级分布：");
            foreach ($stats['user_level_stats'] as $level) {
                $levelName = $this->getUserLevelName($level['user_level']);
                $output->writeln("  - {$levelName}: {$level['count']}人");
            }
        }
    }

    /**
     * 获取商家等级名称
     * @param int $tierLevel
     * @return string
     */
    protected function getTierLevelName($tierLevel)
    {
        $names = [
            0 => '0元入驻',
            1 => '商家会员',
            2 => '实力厂商'
        ];
        
        return $names[$tierLevel] ?? '未知等级';
    }

    /**
     * 获取用户等级名称
     * @param int $level
     * @return string
     */
    protected function getUserLevelName($level)
    {
        $names = [
            1 => '低活跃度',
            2 => '中活跃度',
            3 => '高活跃度'
        ];
        
        return $names[$level] ?? '未知等级';
    }
}
