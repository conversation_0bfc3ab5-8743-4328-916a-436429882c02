{layout name="layout2" /}
<div class="layui-form" lay-filter="layuiadmin-form-reply" id="layuiadmin-form-reply" style="padding: 20px 30px 0 0;">
    <div class="layui-form-item">
        <label class="layui-form-label"><font color="red">*</font> 规则名称：</label>
        <div class="layui-input-inline">
            <input type="text" name="name" value="" lay-verify="required" placeholder="" autocomplete="off" class="layui-input">
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label"></label><span style="color: #a3a3a3;font-size: 9px">方便通过名称管理默认回复内容</span>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label"><font color="red">*</font>关键词：</label>
        <div class="layui-input-inline">
            <input type="text" name="keyword" value="" lay-verify="required" placeholder="" autocomplete="off" class="layui-input">
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label"></label><span style="color: #a3a3a3;font-size: 9px">关键词不能超过5个字</span>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">匹配方式：</label>
        <div class="layui-input-inline">
            <select name="matching_type">
                <option value="1">全匹配</option>
                <option value="2">模糊匹配</option>
            </select>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">内容类型：</label>
        <div class="layui-input-inline">
            <select name="content_type" lay-filter="content_type_change">
                <option value="1">文本</option>
                <option value="2">图片</option>
                <option value="3">文本+图片</option>
            </select>
        </div>
    </div>
    <!-- 第一条消息内容 -->
    <div class="layui-form-item" id="first_content_area">
        <label class="layui-form-label">回复内容：</label>
        <div class="layui-input-block">
            <textarea name="content" placeholder="请输入内容" class="layui-textarea" rows="4"></textarea>
        </div>
    </div>

    <!-- 单独图片上传区域 -->
    <div class="layui-form-item" id="single_image_area" style="display: none;">
        <label class="layui-form-label">图片：</label>
        <div class="layui-input-block">
            <div class="like-upload-image">
                <div class="upload-image-elem"><a class="add-upload-image" data-field="image_url"> + 添加图片</a></div>
            </div>
        </div>
    </div>

    <!-- 第二条消息配置区域 -->
    <div id="second_message_area" style="display: none;">
        <div class="layui-form-item">
            <label class="layui-form-label">第二条消息类型：</label>
            <div class="layui-input-inline">
                <select name="second_content_type" lay-filter="second_content_type_change">
                    <option value="">请选择</option>
                    <option value="1">文本</option>
                    <option value="2">图片</option>
                </select>
            </div>
        </div>

        <div class="layui-form-item" id="second_text_area" style="display: none;">
            <label class="layui-form-label">第二条消息内容：</label>
            <div class="layui-input-block">
                <textarea name="second_content" placeholder="请输入第二条消息内容" class="layui-textarea" rows="3"></textarea>
            </div>
        </div>

        <div class="layui-form-item" id="second_image_area" style="display: none;">
            <label class="layui-form-label">第二条消息图片：</label>
            <div class="layui-input-block">
                <div class="like-upload-image">
                    <div class="upload-image-elem"><a class="add-upload-image" data-field="second_image_url"> + 添加图片</a></div>
                </div>
            </div>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label">启用状态：</label>
        <div class="layui-input-inline">
            <input type="radio" name="status" value="1" title="启用" checked>
            <input type="radio" name="status" value="0" title="停用" >
        </div>
    </div>


    <div class="layui-form-item layui-hide">
        <input type="button" lay-submit lay-filter="add-reply-submit" id="add-reply-submit" value="确认">
    </div>
</div>

<script src="/static/admin/js/function.js"></script>
<script>
    layui.config({
        version:"{$front_version}",
        base: '/static/lib/' //静态资源所在路径
    }).use(['form'], function(){
        var $ = layui.$
            ,form = layui.form;

        // 监听内容类型变化
        form.on('select(content_type_change)', function(data){
            toggleContentTypeAreas(data.value);
        });

        // 监听第二条消息类型变化
        form.on('select(second_content_type_change)', function(data){
            toggleSecondContentAreas(data.value);
        });

        // 切换内容类型显示区域
        function toggleContentTypeAreas(value) {
            if (value == '1') { // 文本
                $('#first_content_area').show();
                $('#single_image_area').hide();
                $('#second_message_area').hide();
            } else if (value == '2') { // 图片
                $('#first_content_area').hide();
                $('#single_image_area').show();
                $('#second_message_area').hide();
            } else if (value == '3') { // 文本+图片
                $('#first_content_area').show();
                $('#single_image_area').hide();
                $('#second_message_area').show();
            }
        }

        like.delUpload();
        $(document).on("click", ".add-upload-image", function () {
            var field = $(this).data('field');
            like.imageUpload({
                limit: 1,
                field: field,
                that: $(this)
            });
        });

        // 切换第二条消息显示区域
        function toggleSecondContentAreas(value) {
            if (value == '1') { // 文本
                $('#second_text_area').show();
                $('#second_image_area').hide();
            } else if (value == '2') { // 图片
                $('#second_text_area').hide();
                $('#second_image_area').show();
            } else {
                $('#second_text_area').hide();
                $('#second_image_area').hide();
            }
        }
    });
</script>
