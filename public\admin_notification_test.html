<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员通知测试工具</title>
    <link rel="stylesheet" href="/static/admin/css/layui.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: #fff;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"], textarea, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        textarea {
            height: 100px;
            resize: vertical;
        }
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        button:hover {
            background-color: #45a049;
        }
        .response {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            background-color: #f9f9f9;
            border-left: 4px solid #4CAF50;
            display: none;
        }
        .error {
            border-left-color: #f44336;
        }
        .log-container {
            margin-top: 20px;
            padding: 15px;
            background-color: #f9f9f9;
            border-radius: 4px;
            max-height: 300px;
            overflow-y: auto;
        }
        .log-entry {
            margin-bottom: 5px;
            padding: 5px;
            border-bottom: 1px solid #eee;
        }
        .log-entry.info {
            color: #0066cc;
        }
        .log-entry.success {
            color: #4CAF50;
        }
        .log-entry.error {
            color: #f44336;
        }
        .tabs {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 1px solid #ddd;
        }
        .tab {
            padding: 10px 20px;
            cursor: pointer;
            background-color: #f1f1f1;
            margin-right: 5px;
            border-radius: 4px 4px 0 0;
        }
        .tab.active {
            background-color: #4CAF50;
            color: white;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
        .btn-group {
            margin-top: 20px;
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }
        .btn-test {
            background-color: #2196F3;
        }
        .btn-test:hover {
            background-color: #0b7dda;
        }
        .btn-direct {
            background-color: #9C27B0;
        }
        .btn-direct:hover {
            background-color: #7B1FA2;
        }
        .btn-websocket {
            background-color: #FF9800;
        }
        .btn-websocket:hover {
            background-color: #F57C00;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>管理员通知测试工具</h1>
        
        <div class="tabs">
            <div class="tab active" data-tab="notification-test">通知测试</div>
            <div class="tab" data-tab="websocket-test">WebSocket测试</div>
            <div class="tab" data-tab="direct-test">直接显示测试</div>
        </div>
        
        <div class="tab-content active" id="notification-test">
            <form id="notification-form">
                <div class="form-group">
                    <label for="title">通知标题</label>
                    <input type="text" id="title" name="title" value="测试通知" required>
                </div>
                
                <div class="form-group">
                    <label for="content">通知内容</label>
                    <textarea id="content" name="content" required>这是一条测试通知，请查收！</textarea>
                </div>
                
                <div class="form-group">
                    <label for="type">通知类型</label>
                    <select id="type" name="type">
                        <option value="admin_notification">管理员通知</option>
                        <option value="system_notification">系统通知</option>
                        <option value="custom_notification">自定义通知</option>
                        <option value="error_notification">错误通知</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="url">跳转URL（可选）</label>
                    <input type="text" id="url" name="url" placeholder="点击通知后跳转的URL">
                </div>
                
                <div class="form-group">
                    <label for="icon">通知图标</label>
                    <select id="icon" name="icon">
                        <option value="0">默认图标</option>
                        <option value="1">成功图标</option>
                        <option value="2">错误图标</option>
                        <option value="3">警告图标</option>
                        <option value="4">信息图标</option>
                    </select>
                </div>
                
                <button type="submit" class="btn-test">发送通知</button>
            </form>
            
            <div class="response" id="response"></div>
        </div>
        
        <div class="tab-content" id="websocket-test">
            <h2>WebSocket消息测试</h2>
            <p>此功能用于测试直接通过WebSocket发送消息。</p>
            
            <div class="form-group">
                <label for="ws-event">事件名称</label>
                <select id="ws-event" name="ws-event">
                    <option value="notification">notification</option>
                    <option value="admin_notification">admin_notification</option>
                    <option value="custom_notification">custom_notification</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="ws-title">通知标题</label>
                <input type="text" id="ws-title" name="ws-title" value="WebSocket测试通知">
            </div>
            
            <div class="form-group">
                <label for="ws-content">通知内容</label>
                <textarea id="ws-content" name="ws-content">这是一条通过WebSocket发送的测试通知，请查收！</textarea>
            </div>
            
            <div class="btn-group">
                <button id="send-ws-message" class="btn-websocket">发送WebSocket消息</button>
                <button id="simulate-ws-message" class="btn-websocket">模拟接收WebSocket消息</button>
            </div>
        </div>
        
        <div class="tab-content" id="direct-test">
            <h2>直接显示通知测试</h2>
            <p>此功能用于测试直接调用通知显示函数，不经过WebSocket或API。</p>
            
            <div class="form-group">
                <label for="direct-title">通知标题</label>
                <input type="text" id="direct-title" name="direct-title" value="直接显示测试通知">
            </div>
            
            <div class="form-group">
                <label for="direct-content">通知内容</label>
                <textarea id="direct-content" name="direct-content">这是一条直接显示的测试通知，请查收！</textarea>
            </div>
            
            <div class="form-group">
                <label for="direct-type">通知类型</label>
                <select id="direct-type" name="direct-type">
                    <option value="admin_notification">管理员通知</option>
                    <option value="system_notification">系统通知</option>
                    <option value="custom_notification">自定义通知</option>
                    <option value="error_notification">错误通知</option>
                </select>
            </div>
            
            <button id="show-direct-notification" class="btn-direct">直接显示通知</button>
        </div>
        
        <div class="log-container">
            <h3>操作日志</h3>
            <div id="log"></div>
        </div>
    </div>
    
    <script src="/static/admin/js/jquery.min.js"></script>
    <script src="/static/admin/js/layui.js"></script>
    <script src="/test_notification_console.js"></script>
    <script>
        // 添加日志函数
        function addLog(message, type = 'info') {
            const logContainer = document.getElementById('log');
            const logEntry = document.createElement('div');
            logEntry.className = 'log-entry ' + type;
            logEntry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logContainer.insertBefore(logEntry, logContainer.firstChild);
        }
        
        // 切换标签页
        document.querySelectorAll('.tab').forEach(tab => {
            tab.addEventListener('click', function() {
                // 移除所有标签页的active类
                document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
                // 移除所有内容区的active类
                document.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));
                
                // 添加当前标签页的active类
                this.classList.add('active');
                // 显示对应的内容区
                const tabId = this.getAttribute('data-tab');
                document.getElementById(tabId).classList.add('active');
            });
        });
        
        // 发送通知表单提交
        document.getElementById('notification-form').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const title = document.getElementById('title').value;
            const content = document.getElementById('content').value;
            const type = document.getElementById('type').value;
            const url = document.getElementById('url').value;
            const icon = document.getElementById('icon').value;
            
            addLog(`准备发送通知: ${title}`);
            
            // 调用测试函数
            testNotification(title, content, type, url, icon);
        });
        
        // 发送WebSocket消息
        document.getElementById('send-ws-message').addEventListener('click', function() {
            const event = document.getElementById('ws-event').value;
            const title = document.getElementById('ws-title').value;
            const content = document.getElementById('ws-content').value;
            
            addLog(`准备发送WebSocket消息: ${event} - ${title}`);
            
            // 调用测试函数
            testWebSocketMessage(event, {
                title: title,
                content: content,
                type: event,
                url: '',
                icon: 0,
                timestamp: Date.now()
            });
        });
        
        // 模拟接收WebSocket消息
        document.getElementById('simulate-ws-message').addEventListener('click', function() {
            const event = document.getElementById('ws-event').value;
            const title = document.getElementById('ws-title').value;
            const content = document.getElementById('ws-content').value;
            
            addLog(`准备模拟接收WebSocket消息: ${event} - ${title}`);
            
            // 调用测试函数
            testReceiveWebSocketMessage(event, {
                title: title,
                content: content,
                type: event,
                url: '',
                icon: 0,
                timestamp: Date.now()
            });
        });
        
        // 直接显示通知
        document.getElementById('show-direct-notification').addEventListener('click', function() {
            const title = document.getElementById('direct-title').value;
            const content = document.getElementById('direct-content').value;
            const type = document.getElementById('direct-type').value;
            
            addLog(`准备直接显示通知: ${title}`);
            
            // 调用测试函数
            testShowNotification(title, content, type, '', 0);
        });
        
        // 页面加载完成
        window.addEventListener('load', function() {
            addLog('页面加载完成，可以开始测试通知功能');
            
            // 初始化LayUI
            layui.use(['layer'], function() {
                var layer = layui.layer;
                
                // 显示欢迎消息
                layer.msg('通知测试工具已加载', {icon: 1, time: 2000});
            });
        });
    </script>
</body>
</html>
