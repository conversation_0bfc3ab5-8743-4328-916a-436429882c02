{layout name="layout2" /}
<style>
    .tips{
        color: red;
    }
</style>
<div class="layui-form" lay-filter="layuiadmin-form-user" id="layuiadmin-form-user" style="padding: 20px 30px 0 0;">

    <input type="hidden" value="{$info.id}" name="id">
    <input type="hidden" value="{$type}" name="type">

    <!--通知场景-->
    <div class="layui-form-item">
        <label class="layui-form-label">通知场景：</label>
        <div class="layui-input-inline" style="padding-top: 8px">
            {$info.scene}
        </div>
    </div>

    <!--模板编号-->
    <div class="layui-form-item">
        <label class="layui-form-label"><span class="tips">*</span>模板编号：</label>
        <div class="layui-input-block">
            <div class="layui-col-md4">
                <input type="text" name="template_sn"  value="{$info.oa_notice.template_sn | default = ''}"   autocomplete="off" class="layui-input" lay-verify="required" lay-vertype="tips">
            </div>
        </div>
    </div>

    <!--模板名称-->
    <div class="layui-form-item">
        <label class="layui-form-label"><span class="tips">*</span>模板名称：</label>
        <div class="layui-input-block">
            <div class="layui-col-md4">
            <input type="text" name="name" value="{$info.oa_notice.name | default = ''}"  autocomplete="off" class="layui-input" lay-verify="required" lay-vertype="tips">
            </div>
        </div>
    </div>

    <!--模板id-->
    <div class="layui-form-item">
        <label class="layui-form-label"><span class="tips">*</span>模板ID：</label>
        <div class="layui-input-block">
            <div class="layui-col-md4">
                <input type="text" name="template_id"  value="{$info.oa_notice.template_id | default = ''}" autocomplete="off" class="layui-input"  lay-verify="required" lay-vertype="tips">
            </div>
        </div>
    </div>

    <!--场景变量-->
    <div class="layui-form-item">
        <label class="layui-form-label">场景变量：</label>
        <div class="layui-input-block">
            {foreach $info.variable as $item => $val}
            <button type="button"  class="layui-btn layui-btn-primary variable-btn" data-value="{$item}">{$val}</button>
            {/foreach}
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label"></label>
        <div class="layui-form-mid layui-word-aux">系统在当前场景预定义好的通知变量</div>
    </div>

    <!--头部标题-->
    <div class="layui-form-item">
        <label class="layui-form-label"><span class="tips">*</span>头部标题：</label>
        <div class="layui-input-block">
            <div class="layui-col-md4">
                <input type="text" name="first" value="{$info.oa_notice.first | default = ''}" autocomplete="off" class="layui-input" lay-verify="required" lay-vertype="tips">
                <div class="layui-form-mid layui-word-aux">{{first.DT}}的内容，支持嵌入场景变量，复制场景变量的值填入即可生效</div>
            </div>
        </div>
    </div>


    <!--模板内容-->
    <div class="layui-form-item">
        <label class="layui-form-label"><span class="tips">*</span>模板内容：</label>
        <div class="layui-input-inline">
            <button class="layui-btn layui-btn-sm layui-btn-normal addTpl" type="button">新增</button>
        </div>
    </div>

    <!--模板-->
    <div class="template-body">
        {notempty name ="$info.oa_notice.tpl"}
            {foreach $info.oa_notice.tpl as $k => $item}
            <div class="layui-form-item template">
                <div class="layui-input-block">
                    <label class="layui-form-label">字段名称：</label>
                    <div class="layui-input-inline">
                        <input type="text" name="tpl_name[]" value="{$item.tpl_name}" autocomplete="off" class="layui-input"  lay-verify="required" lay-vertype="tips">
                    </div>
                    <label class="layui-form-label">关键词：</label>
                    <div class="layui-input-inline">
                        <input type="text" name="tpl_keyword[]" value="{$item.tpl_keyword}" autocomplete="off" class="layui-input" lay-verify="required" lay-vertype="tips">
                    </div>
                    <label class="layui-form-label">字段内容：</label>
                    <div class="layui-input-inline">
                        <input type="text" name="tpl_content[]" value="{$item.tpl_content}" autocomplete="off" class="layui-input" lay-verify="required" lay-vertype="tips">
                    </div>
                    <button class="layui-btn layui-btn-sm layui-btn-danger delTpl" type="button">删除</button>
                </div>
            </div>
            {/foreach}
        {/notempty}
    </div>

    <!--模板预览-->
    <div class="layui-form-item">
        <label class="layui-form-label"><span class="tips">*</span>模板预览：</label>
        <div class="layui-input-block">
            <div class="layui-col-sm4">
                <textarea name="content" id="content" class="layui-textarea"></textarea>
            </div>
        </div>
    </div>

    <!--尾部描述-->
    <div class="layui-form-item">
        <label class="layui-form-label"><span class="tips">*</span>尾部描述：</label>
        <div class="layui-input-block">
            <div class="layui-col-md4">
                <input type="text" name="remark" value="{$info.oa_notice.remark | default = ''}" autocomplete="off" class="layui-input"  lay-verify="required" lay-vertype="tips">
                <div class="layui-form-mid layui-word-aux">{{remrk.DT}}的内容，支持嵌入场景变量，复制场景变量的值填入即可生效</div>
            </div>
        </div>
    </div>


    <div class="layui-form-item">
        <label class="layui-form-label">状态</label>
        <div class="layui-input-inline">
            <input type="radio" name="status" value="1" title="开启" {if ($info.oa_notice.status ?? 0) == 1}checked{/if} />
            <input type="radio" name="status" value="0" title="关闭" {if ($info.oa_notice.status ?? 0) == 0}checked{/if}  />
            <div class="layui-form-mid layui-word-aux">开启或关闭当前场景通知</div>
        </div>
    </div>
    <div class="layui-form-item layui-hide">
        <input type="button" lay-submit lay-filter="addSubmit" id="addSubmit" value="确认">
    </div>
</div>
<script>
    layui.config({
        version:"{$front_version}",
        base: '/static/plug/'
    }).extend({
        base: '/static/plug/layui-admin/dist/layuiadmin/'
    }).use(['form'], function() {
        var $ = layui.$
            ,form = layui.form;

        //场景变量复制
        $(document).on('click', '.variable-btn', function () {
            var copyText = $(this).data('value');
            let aux = document.createElement("input");
            aux.setAttribute("value", '{'+copyText+'}');
            document.body.appendChild(aux);
            aux.select();
            document.execCommand("copy");
            document.body.removeChild(aux);
            layer.msg('已复制场景变量');
        });

        // 添加模板
        $(document).on('click','.addTpl',function() {
            var tpl = '<div class="layui-form-item template">' +
                '            <div class="layui-input-block">' +
                '                <label class="layui-form-label">字段名称：</label>' +
                '                <div class="layui-input-inline">' +
                '                    <input type="text" name="tpl_name[]" autocomplete="off" class="layui-input" value="" lay-verify="required" lay-vertype="tips">' +
                '                </div>' +
                '                <label class="layui-form-label">关键词：</label>' +
                '                <div class="layui-input-inline">' +
                '                    <input type="text" name="tpl_keyword[]" autocomplete="off" class="layui-input" value="" lay-verify="required" lay-vertype="tips">' +
                '                </div>' +
                '                <label class="layui-form-label">字段内容：</label>' +
                '                <div class="layui-input-inline">' +
                '                    <input type="text" name="tpl_content[]" autocomplete="off" class="layui-input" value="" lay-verify="required" lay-vertype="tips">' +
                '                </div>' +
                '                <button class="layui-btn layui-btn-sm layui-btn-danger delTpl" type="button">删除</button>' +
                '            </div>' +
                '        </div>';

            $('.template-body').append(tpl);
        });



        refreshContent();

        //监听字段名称
        $(document).on("input  propertychange","input[name='tpl_name[]']",function() {
            refreshContent();
        });

        //监听关键词
        $(document).on("input  propertychange","input[name='tpl_keyword[]']",function() {
            refreshContent();
        });

        // 删除模板
        $(document).on('click','.delTpl',function() {
            var len = $("div.template-body > div.template").length;
            if (len <= 1) {
                layer.msg('最少保留一个模板内容');
                return;
            } else {
                $(this).parents('.template').remove();
                refreshContent();
            }
        });

        //渲染模板预览
        function refreshContent() {
            var map = [];
            $(".template").each(function() {
                var index = $(this).index();
                var key_value = $(this).children().find("input[name='tpl_keyword[]']").val();
                var name_value = $(this).children().find("input[name='tpl_name[]']").val();
                map[index] = {name:name_value, key:key_value};
            });

            if (map.length > 0) {
                var content = '{{first.DATA}}' + "\n";

                for (var i=0; i < map.length; i++) {
                    var key = '{{'+map[i].key+'.DATA}}';
                    var name = map[i].name;
                    if (map[i].key === undefined || map[i].key === '') {
                        key = '';
                    }
                    if (map[i].name === undefined || map[i].name === '') {
                        name = '';
                    }
                    content += name + ':' + key + "\n";
                }
                content += '{{remark.DATA}}';
                $('#content').text(content);
            }
        }

    })
</script>