var lock = {};
var load = {};
layui.define(["jquery", "form", "upload"], function (exports) {
    $ = layui.$;
    upload = layui.upload;
    var ojb = {
        ajax: function (json) {
            var load_index = null;
            if (json.beforeSend === undefined) {
                if (lock[json.url.replace("/", "_")] !== undefined) {
                    return
                }
                lock[json.url.replace("/", "_")] = true;
                json.beforeSend = function () {
                    load[json.url.replace("/", "_")] = setTimeout(function () {
                        load_index = layer.load(1, {shade: [0.1, "#fff"]})
                    }, 1500)
                }
            }
            if (json.error === undefined) {
                json.error = function (res) {
                    layer.msg("网络错误", {offset: "240px", icon: 2, time: 1500}, function () {
                        return
                    })
                }
            }
            if (json.timeout === undefined) {
                json.timeout = 30000
            }
            if (json.type === undefined) {
                json.type = "get"
            }
            json.complete = function (xhr, status) {
                delete lock[json.url.replace("/", "_")];
                if (status == "timeout") {
                    layer.msg("请求超时，请重试", {offset: "240px", icon: 2, time: 1500}, function () {
                        if (load_index !== undefined) {
                            layer.close(load_index)
                        }
                        return
                    });
                    return
                }
                clearTimeout(load[json.url.replace("/", "_")]);
                if (load_index !== undefined) {
                    layer.close(load_index)
                }
                res = xhr.responseJSON;
                if (res !== undefined && res.code == -1) {
                    window.location.href = window.location.href
                }
                if (res !== undefined && res.code == 0 && res.show == 1) {
                    layer.msg(res.msg, {offset: "240px", icon: 2, time: 1500}, function () {
                        return;
                    })
                }
            };
            $.ajax(json)
        }, imageUpload: function (element, upload_call_back,css, area, title) {
            var area = (area === undefined) ? ["90%", "90%"] : area;
            var title = (title === undefined) ? "上传图片" : title;
            var css = css === undefined ? false : css;
            var click_element = element;
            if(css === true){
                $(element).addClass('upload-image-div');
                $(element).html(' <a class="upload-image-a" > + 添加图片</a>');
                click_element = element+ ' .upload-image-a';
            }
            $(document).on("click", click_element, function () {
                var click_element = $(this);
                if(css === true){
                    click_element = click_element.parent();
                }
                var windows = layer.open({type: 2, title: title, content: image_upload_url, area: area});
                window.callback = function (uri) {
                    upload_call_back(uri, click_element);
                    return uri
                };
                window.callbackSetUri = function (uri) {
                    upload_call_back(uri, click_element);
                    layer.close(windows);
                    return uri
                }
            })
        }, showImg: function (url, xp) {
            function getImageWidth(url, callback) {
                var img = new Image();
                img.src = url;
                if (img.complete) {
                    callback(img.width, img.height)
                } else {
                    img.onload = function () {
                        callback(img.width, img.height)
                    }
                }
            }
            xp === undefined ? 500 : xp;
            getImageWidth(url, function (width, height) {
                if (height > xp) {
                    var ratio = width / height;
                    height = xp;
                    width = height * ratio
                }
                if (width > xp) {
                    var ratio = height / width;
                    width = xp;
                    height = width * ratio
                }
                layer.closeAll();
                layer.open({
                    type: 1,
                    closeBtn: 1,
                    shade: false,
                    title: false,
                    shadeClose: false,
                    area: ["auth", "auth"],
                    content: '<img src="' + url + '" width="' + width + 'px" height="' + height + 'px">'
                })
            })
        },
        // 从URL中取出文件名(除去http网址, 根据存储对象)
        getUrlFileName: function(url, domain) {
            return url.replace(domain, '');
           // var data = url.split('/');
           // return data[data.length - 3] + '/' + data[data.length - 2] + '/' + data[data.length - 1];
        },
        videoUpload: function (options) {
            // 支持两种调用方式：
            // 1. 对象参数：like.videoUpload({limit: 1, field: "video", that: $(this), content: '/shop/file/videoList'})
            // 2. 直接参数：like.videoUpload(element, url)

            var element, url, field, that, content, limit;

            if (typeof options === 'object' && options.that) {
                // 对象参数方式（shop模块使用）
                that = options.that;
                element = that[0];
                field = options.field || 'video';
                content = options.content || '/shop/file/videoList';
                limit = options.limit || 1;

                // 打开视频选择弹窗
                var windows = layer.open({
                    type: 2,
                    title: "上传视频",
                    content: content,
                    area: ["90%", "90%"]
                });

                // 设置回调函数
                window.callback = function (uri) {
                    // 创建视频显示HTML
                    var template = '<div class="upload-video-div">' +
                        '<video width="160" height="160" src="' + uri + '" controls autoplay></video>' +
                        '<div class="del-upload-btn">x</div>' +
                        '<input type="hidden" name="' + field + '" value="' + uri + '">' +
                        '</div>';

                    // 添加到页面
                    that.parent().before(template);
                    that.hide();

                    layer.msg('上传成功');
                    return uri;
                };

                window.callbackSetUri = function (uri) {
                    window.callback(uri);
                    layer.close(windows);
                    return uri;
                };

            } else {
                // 直接参数方式（admin模块使用）
                element = arguments[0];
                url = arguments[1];

                // 如果元素已经有upload-video-div类，说明已经初始化过了，直接返回
                if ($(element).hasClass('upload-video-div')) {
                    return;
                }

                $(element).addClass('upload-video-div');
                $(element).html(' <a class="upload-video-a" > + 添加视频</a>');

                upload.render({ //允许上传的文件后缀
                    elem: element
                    ,url: url       //改成您自己的上传接口
                    ,accept: 'file' //普通文件
                    ,exts: 'mp3|mp4|AVI|mov|rmvb|rm|FLV|3GP|wav'
                    ,done: function(res){
                        if (res.code === 1){
                            var video = '<div class="show-video">' +
                                ' <video width="160" height="160"  src="'+res.data.domain+res.data.uri+'"controls autoplay></video>'
                                +'<a class="goods-video-del-x goods-video-del">x</a>'
                                + '<input type="hidden" name="video" value="/'+res.data.uri+'">'
                                '</div>';

                            $(element).after(video);
                            $(element).hide();
                            layer.msg('上传成功');

                            // 绑定删除事件
                            $(element).next('.show-video').find('.goods-video-del-x').on('click', function() {
                                var $showVideo = $(this).parent();
                                var $uploadDiv = $showVideo.prev('.upload-video-div');

                                layer.confirm('确定要删除这个视频吗？', {
                                    icon: 3,
                                    title: '确认删除'
                                }, function(index) {
                                    // 删除视频div
                                    $showVideo.remove();

                                    // 显示上传按钮
                                    $uploadDiv.show();

                                    // 重置上传按钮内容和类
                                    $uploadDiv.removeClass('upload-video-div');
                                    $uploadDiv.html('<a class="upload-video-a"> + 添加视频</a>');

                                    // 重新绑定上传事件
                                    setTimeout(function() {
                                        like.videoUpload($uploadDiv[0], url);
                                    }, 100);

                                    layer.close(index);
                                    layer.msg('视频已删除');
                                });
                            });
                        } else {
                            layer.msg(res.msg);
                        }
                    }
                });
            }
        }
        ,setSelect: function(selectedValue, dataArray, selectName, placeholder) {
            // 设置下拉选择框的选项
            // selectedValue: 要选中的值
            // dataArray: 选项数据数组，格式如 [{id: 1, name: '选项1'}, ...]
            // selectName: select元素的name属性
            // placeholder: 默认提示文本

            var $select = $('select[name="' + selectName + '"]');
            if ($select.length === 0) {
                console.warn('未找到name为 "' + selectName + '" 的select元素');
                return;
            }

            // 清空现有选项
            $select.empty();

            // 添加默认选项
            if (placeholder) {
                $select.append('<option value="">' + placeholder + '</option>');
            }

            // 添加数据选项
            if (dataArray && Array.isArray(dataArray)) {
                for (var i = 0; i < dataArray.length; i++) {
                    var item = dataArray[i];
                    var value = item.id || item.value || '';
                    var text = item.name || item.text || item.title || '';
                    var selected = (selectedValue && selectedValue == value) ? ' selected' : '';
                    $select.append('<option value="' + value + '"' + selected + '>' + text + '</option>');
                }
            }

            // 重新渲染layui表单
            if (typeof layui !== 'undefined' && layui.form) {
                layui.form.render('select');
            }
        }
        ,certUpload:function (element, url, domain) {
            console.log('aaa')
            upload.render({
                elem: element
                ,url: url
                ,accept:'file'
                ,exts:'pem|txt|doc'
                ,done: function(res, index, upload) {
                    var html = '<div class="pay-li">\n' +
                        '<img class="pay-img" ' +
                        'src="/static/common/image/default/upload.png">' +
                        '<a class="pay-img-del-x" style="display: none">x</a>' +
                        '</div>';
                    $(element).prev().val(res.data.uri.replace(domain, ''));
                    $(element).after(html);
                    $(element).css('display','none');
                }
            });
        }
    };

    // 将like对象暴露到全局，以便在shop模块中使用
    window.like = ojb;

    exports("like", ojb)
});