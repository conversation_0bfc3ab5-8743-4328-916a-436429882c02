

<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>layuiAdmin 主页示例模板一</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
  <link rel="stylesheet" href="../../layuiadmin/layui/css/layui.css" media="all">
  <link rel="stylesheet" href="../../layuiadmin/style/admin.css" media="all">
</head>
<body>

  <div class="layui-fluid">
    <div class="layui-row layui-col-space15">
      <div class="layui-col-md8">    
        <div class="layui-card">
          <div class="layui-card-header">
            最近更新
            <a lay-href="https://www.layui.com/doc/base/changelog.html" class="layui-a-tips">全部更新</a>
          </div>
          <div class="layui-card-body">
            <div class="layui-row layui-col-space10">
              <div class="layui-col-xs12 layui-col-sm4">
                <div class="layuiadmin-card-text">
                  <div class="layui-text-top"><i class="layui-icon layui-icon-water"></i><a lay-href="https://www.layui.com/doc/modules/flow.html">flow</a></div>
                  <p class="layui-text-center">修复开启 isLazyimg:true 后, 图片懒加载但是图片不存在的报错问题</p>
                  <p class="layui-text-bottom"><a lay-href="https://www.layui.com/doc/modules/flow.html">流加载</a><span>7 天前</span></p>
                </div>
              </div>
              <div class="layui-col-xs12 layui-col-sm4">
                <div class="layuiadmin-card-text">
                  <div class="layui-text-top"><i class="layui-icon layui-icon-upload-circle"></i><a lay-href="https://www.layui.com/doc/modules/upload.html">upload</a></div>
                  <p class="layui-text-center">修复开启 size 参数后，文件超出规定大小时，提示信息有误的问题</p>
                  <p class="layui-text-bottom"><a lay-href="https://www.layui.com/doc/modules/upload.html">文件上传</a><span>7 天前</span></p>
                </div>
              </div>
              <div class="layui-col-xs12 layui-col-sm4">
                <div class="layuiadmin-card-text">
                  <div class="layui-text-top"><i class="layui-icon layui-icon-form"></i><a lay-href="https://www.layui.com/doc/modules/form.html#val">form</a></div>
                  <p class="layui-text-center">增加 form.val(filter, fields)方法，用于给指定表单集合的元素初始赋值</p>
                  <p class="layui-text-bottom"><a lay-href="https://www.layui.com/doc/modules/form.html">表单</a><span>7 天前</span></p>
                </div>
              </div>
              <div class="layui-col-xs12 layui-col-sm4">
                <div class="layuiadmin-card-text">
                  <div class="layui-text-top"><i class="layui-icon layui-icon-form"></i><a lay-href="https://www.layui.com/doc/modules/form.html">form</a></div>
                  <p class="layui-text-center">对 select 组件新增上下键（↑ ↓）回车键（Enter）选择功能</p>
                  <p class="layui-text-bottom"><a lay-href="https://www.layui.com/doc/modules/form.html">表单</a><span>7 天前</span></p>
                </div>
              </div>
              <div class="layui-col-xs12 layui-col-sm4">
                <div class="layuiadmin-card-text">
                  <div class="layui-text-top"><i class="layui-icon layui-icon-form"></i><a lay-href="https://www.layui.com/doc/modules/form.html">form</a></div>
                  <p class="layui-text-center">优化 switch 开关组件，让其能根据文本自由伸缩宽</p>
                  <p class="layui-text-bottom"><a lay-href="https://www.layui.com/doc/modules/form.html">表单</a><span>7 天前</span></p>
                </div>
              </div>
              <div class="layui-col-xs12 layui-col-sm4">
                <div class="layuiadmin-card-text">
                  <div class="layui-text-top"><i class="layui-icon layui-icon-form"></i><a lay-href="https://www.layui.com/doc/modules/form.html">form</a></div>
                  <p class="layui-text-center">修复 checkbox 复选框组件在高分辨屏下出现的样式不雅问题</p>
                  <p class="layui-text-bottom"><a lay-href="https://www.layui.com/doc/modules/form.html">表单</a><span>7 天前</span></p>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="layui-card">
          <div class="layui-card-header">动态</div>
          <div class="layui-card-body">
            <dl class="layuiadmin-card-status">
              <dd>
                <div class="layui-status-img"><a href="javascript:;"><img src="../../layuiadmin/style/res/logo.png"></a></div>
                <div>
                  <p>胡歌 在 <a lay-href="http://fly.layui.com/vipclub/list/layuiadmin/">layuiadmin专区</a> 回答问题</p>
                  <span>几秒前</span>
                </div>
              </dd>
              <dd>
                <div class="layui-status-img"><a href="javascript:;"><img src="../../layuiadmin/style/res/logo.png"></a></div>
                <div>
                  <p>彭于晏 在 <a lay-href="http://fly.layui.com/vipclub/list/layuiadmin/">layuiadmin专区</a> 进行了 <a lay-href="http://fly.layui.com/vipclub/list/layuiadmin/column/quiz/">提问</a></p>
                  <span>2天前</span>
                </div>
              </dd>
              <dd>
                <div class="layui-status-img"><a href="javascript:;"><img src="../../layuiadmin/style/res/logo.png"></a></div>
                <div>
                  <p>贤心 将 <a lay-href="https://www.layui.com/">layui</a> 更新至 2.3.0 版本</p>
                  <span>7天前</span>
                </div>
              </dd>
               <dd>
                <div class="layui-status-img"><a href="javascript:;"><img src="../../layuiadmin/style/res/logo.png"></a></div>
                <div>
                  <p>吴尊 在 <a lay-href="http://fly.layui.com/">Fly社区</a> 发布了 <a lay-href="http://fly.layui.com/column/suggest/">建议</a></p>
                  <span>7天前</span>
                </div>
              </dd>
              <dd>
                <div class="layui-status-img"><a href="javascript:;"><img src="../../layuiadmin/style/res/logo.png"></a></div>
                <div>
                  <p>许上进 在 <a lay-href="http://fly.layui.com/">Fly社区</a> 发布了 <a lay-href="http://fly.layui.com/column/suggest/">建议</a></p>
                  <span>8天前</span>
                </div>
              </dd>
              <dd>
                <div class="layui-status-img"><a href="javascript:;"><img src="../../layuiadmin/style/res/logo.png"></a></div>
                <div>
                  <p>小蚊子 在 <a lay-href="http://fly.layui.com/vipclub/list/layuiadmin/">layuiadmin专区</a> 进行了 <a lay-href="http://fly.layui.com/vipclub/list/layuiadmin/column/quiz/">提问</a></p>
                  <span>8天前</span>
                </div>
              </dd>
            </dl>  
          </div>
        </div>     
      </div>
      <div class="layui-col-md4">
        <div class="layui-card">
          <div class="layui-card-header">便捷导航</div>
          <div class="layui-card-body">
            <div class="layuiadmin-card-link">
              <a href="javascript:;">操作一</a>
              <a href="javascript:;">操作二</a> 
              <a href="javascript:;">操作三</a> 
              <a href="javascript:;">操作四</a> 
              <a href="javascript:;">操作五</a> 
              <a href="javascript:;">操作六</a>
              <button class="layui-btn layui-btn-primary layui-btn-xs">
                <i class="layui-icon layui-icon-add-1" style="position: relative; top: -1px;"></i>添加
              </button>
            </div>        
          </div>
        </div>
        <div class="layui-card">
          <div class="layui-card-header">八卦新闻</div>
          <div class="layui-card-body">

              <div class="layui-carousel layadmin-carousel layadmin-dataview" data-anim="fade" lay-filter="LAY-index-pageone">
                <div carousel-item id="LAY-index-pageone">
                  <div><i class="layui-icon layui-icon-loading1 layadmin-loading"></i></div>
                </div>
              </div> 

          </div>
        </div>
        <div class="layui-card">
          <div class="layui-card-header">重点组件</div>
          <div class="layui-card-body">
            <ul class="layui-row layuiadmin-card-team">
              <li class="layui-col-xs6">
                <a lay-href="https://www.layui.com/doc/modules/table.html">
                  <span class="layui-team-img"><img src="../../layuiadmin/style/res/logo.png"></span> 
                  <span>数据表格</span>
                </a>
              </li>
              <li class="layui-col-xs6">
                <a lay-href="https://www.layui.com/doc/modules/layim.html">
                  <span class="layui-team-img"><img src="../../layuiadmin/style/res/logo.png"></span> 
                  <span>即时通讯</span>
                </a>
              </li>
              <li class="layui-col-xs6">
                <a lay-href="https://www.layui.com/doc/modules/form.html">
                  <span class="layui-team-img"><img src="../../layuiadmin/style/res/logo.png"></span> 
                  <span>表单</span>
                </a>
              </li>
              <li class="layui-col-xs6">
                <a lay-href="https://www.layui.com/doc/modules/layer.html">
                  <span class="layui-team-img"><img src="../../layuiadmin/style/res/logo.png"></span> 
                  <span>弹出层</span>
                </a>
              </li>
              <li class="layui-col-xs6">
                <a lay-href="https://www.layui.com/doc/modules/upload.html">
                  <span class="layui-team-img"><img src="../../layuiadmin/style/res/logo.png"></span> 
                  <span>文件上传</span>
                </a>
              </li>
              <li class="layui-col-xs6">
                <a lay-href="https://www.layui.com/doc/modules/rate.html">
                  <span class="layui-team-img"><img src="../../layuiadmin/style/res/logo.png"></span> 
                  <span>评分</span>
                </a>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script src="../../layuiadmin/layui/layui.js"></script>  
  <script>
  layui.config({
    base: '../../layuiadmin/' //静态资源所在路径
  }).extend({
    index: 'lib/index' //主入口模块
  }).use(['index', 'sample']);
  </script>
</body>
</html>