(window.webpackJsonp=window.webpackJsonp||[]).push([[56,10,12,19],{473:function(e,t,r){"use strict";var o=r(14),n=r(4),d=r(5),l=r(141),c=r(24),v=r(18),f=r(290),m=r(54),_=r(104),h=r(289),x=r(3),y=r(105).f,C=r(45).f,w=r(23).f,D=r(474),S=r(475).trim,k="Number",T=n.Number,O=T.prototype,N=n.TypeError,F=d("".slice),I=d("".charCodeAt),j=function(e){var t=h(e,"number");return"bigint"==typeof t?t:z(t)},z=function(e){var t,r,o,n,d,l,c,code,v=h(e,"number");if(_(v))throw N("Cannot convert a Symbol value to a number");if("string"==typeof v&&v.length>2)if(v=S(v),43===(t=I(v,0))||45===t){if(88===(r=I(v,2))||120===r)return NaN}else if(48===t){switch(I(v,1)){case 66:case 98:o=2,n=49;break;case 79:case 111:o=8,n=55;break;default:return+v}for(l=(d=F(v,2)).length,c=0;c<l;c++)if((code=I(d,c))<48||code>n)return NaN;return parseInt(d,o)}return+v};if(l(k,!T(" 0o1")||!T("0b1")||T("+0x1"))){for(var M,R=function(e){var t=arguments.length<1?0:T(j(e)),r=this;return m(O,r)&&x((function(){D(r)}))?f(Object(t),r,R):t},A=o?y(T):"MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,EPSILON,MAX_SAFE_INTEGER,MIN_SAFE_INTEGER,isFinite,isInteger,isNaN,isSafeInteger,parseFloat,parseInt,fromString,range".split(","),E=0;A.length>E;E++)v(T,M=A[E])&&!v(R,M)&&w(R,M,C(T,M));R.prototype=O,O.constructor=R,c(n,k,R,{constructor:!0})}},474:function(e,t,r){var o=r(5);e.exports=o(1..valueOf)},475:function(e,t,r){var o=r(5),n=r(36),d=r(19),l=r(476),c=o("".replace),v="["+l+"]",f=RegExp("^"+v+v+"*"),m=RegExp(v+v+"*$"),_=function(e){return function(t){var r=d(n(t));return 1&e&&(r=c(r,f,"")),2&e&&(r=c(r,m,"")),r}};e.exports={start:_(1),end:_(2),trim:_(3)}},476:function(e,t){e.exports="\t\n\v\f\r                　\u2028\u2029\ufeff"},477:function(e,t,r){var content=r(480);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[e.i,content,""]]),content.locals&&(e.exports=content.locals);(0,r(17).default)("7c52e05d",content,!0,{sourceMap:!1})},478:function(e,t,r){"use strict";r.r(t);r(473);var o={data:function(){return{priceSlice:{}}},components:{},props:{firstSize:{type:Number,default:14},secondSize:{type:Number,default:14},color:{type:String},weight:{type:[String,Number],default:400},price:{type:[String,Number],default:""},showSubscript:{type:Boolean,default:!0},subscriptSize:{type:Number,default:14},lineThrough:{type:Boolean,default:!1}},created:function(){this.priceFormat()},watch:{price:function(e){this.priceFormat()}},methods:{priceFormat:function(){var e=this.price,t={};null!==e&&(e=String(e).split("."),t.first=e[0],t.second=e[1],this.priceSlice=t)}}},n=(r(479),r(8)),component=Object(n.a)(o,(function(){var e=this,t=e._self._c;return t("span",{class:(e.lineThrough?"line-through":"")+"price-format",style:{color:e.color,"font-weight":e.weight}},[e.showSubscript?t("span",{style:{"font-size":e.subscriptSize+"px","margin-right":"1px"}},[e._v("¥")]):e._e(),e._v(" "),t("span",{style:{"font-size":e.firstSize+"px","margin-right":"1px"}},[e._v(e._s(e.priceSlice.first))]),e._v(" "),e.priceSlice.second?t("span",{style:{"font-size":e.secondSize+"px"}},[e._v("."+e._s(e.priceSlice.second))]):e._e()])}),[],!1,null,null,null);t.default=component.exports},479:function(e,t,r){"use strict";r(477)},480:function(e,t,r){var o=r(16)(!1);o.push([e.i,".price-format{display:flex;align-items:baseline}",""]),e.exports=o},485:function(e,t,r){"use strict";r.r(t);r(473),r(86),r(62),r(12),r(107),r(40),r(106);var o=6e4,n=36e5,d=24*n;function l(e){return(0+e.toString()).slice(-2)}var c={components:{},props:{isSlot:{type:Boolean,default:!1},time:{type:Number,default:0},format:{type:String,default:"hh:mm:ss"},autoStart:{type:Boolean,default:!0}},watch:{time:{immediate:!0,handler:function(e){e&&this.reset()}}},data:function(){return{timeObj:{},formateTime:0}},created:function(){},computed:{},methods:{createTimer:function(e){return setTimeout(e,100)},isSameSecond:function(e,t){return Math.floor(e)===Math.floor(t)},start:function(){this.counting||(this.counting=!0,this.endTime=Date.now()+1e3*this.remain,this.setTimer())},setTimer:function(){var e=this;this.tid=this.createTimer((function(){var t=e.getRemain();e.isSameSecond(t,e.remain)&&0!==t||e.setRemain(t),0!==e.remain&&e.setTimer()}))},getRemain:function(){return Math.max(this.endTime-Date.now(),0)},pause:function(){this.counting=!1,clearTimeout(this.tid)},reset:function(){this.pause(),this.remain=this.time,this.setRemain(this.remain),this.autoStart&&this.start()},setRemain:function(e){var t=this.format;this.remain=e;var time,r=(time=e,{days:Math.floor(time/d),hours:l(Math.floor(time%d/n)),minutes:l(Math.floor(time%n/o)),seconds:l(Math.floor(time%o/1e3))});this.formateTime=function(e,t){var r=t.days,o=t.hours,n=t.minutes,d=t.seconds;return-1!==e.indexOf("dd")&&(e=e.replace("dd",r)),-1!==e.indexOf("hh")&&(e=e.replace("hh",l(o))),-1!==e.indexOf("mm")&&(e=e.replace("mm",l(n))),-1!==e.indexOf("ss")&&(e=e.replace("ss",l(d))),e}(t,r),this.$emit("change",r),0===e&&(this.pause(),this.$emit("finish"))}}},v=r(8),component=Object(v.a)(c,(function(){var e=this,t=e._self._c;return e.time>=0?t("div",[t("client-only",[e.isSlot?e._t("default"):t("span",[e._v(e._s(e.formateTime))])],2)],1):e._e()}),[],!1,null,null,null);t.default=component.exports},486:function(e,t,r){"use strict";r.d(t,"b",(function(){return n})),r.d(t,"a",(function(){return d}));r(29),r(63),r(46),r(40),r(20),r(64),r(65),r(47);var o=r(37);r(108),r(62),r(12);var n=function(e){var time=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1e3,t=arguments.length>2?arguments[2]:void 0,r=new Date(0).getTime();return function(){var o=(new Date).getTime();if(o-r>time){for(var n=arguments.length,d=new Array(n),l=0;l<n;l++)d[l]=arguments[l];e.apply(t,d),r=o}}};function d(e){var p="";if("object"==Object(o.a)(e)){for(var t in p="?",e)p+="".concat(t,"=").concat(e[t],"&");p=p.slice(0,-1)}return p}},510:function(e,t,r){var content=r(527);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[e.i,content,""]]),content.locals&&(e.exports=content.locals);(0,r(17).default)("2129d832",content,!0,{sourceMap:!1})},526:function(e,t,r){"use strict";r(510)},527:function(e,t,r){var o=r(16)(!1);o.push([e.i,".deliver-search-container .deliver-box .deliver-recode-box[data-v-79dec466]{padding:10px 20px;background-color:#f2f2f2}.deliver-search-container .deliver-box .deliver-recode-box .recode-img[data-v-79dec466]{position:relative;width:72px;height:72px}.deliver-search-container .deliver-box .deliver-recode-box .recode-img .float-count[data-v-79dec466]{position:absolute;bottom:0;height:20px;width:100%;background-color:rgba(0,0,0,.5);color:#fff;font-size:12px}.deliver-search-container .deliver-box .deliver-recode-box .recode-info-container[data-v-79dec466]{flex:1}.deliver-search-container .deliver-box .deliver-recode-box .recode-info-container .recode-label[data-v-79dec466]{width:70px}.deliver-search-container .deliver-box .deliver-recode-box .recode-info-container .copy-btn[data-v-79dec466]{height:20px;min-width:42px;border:1px solid #ff2c3c;font-size:12px;margin-left:8px;border-radius:60px;cursor:pointer}.deliver-search-container .deliver-box .deliver-recode-box .recode-info-container .copy-btn[data-v-79dec466]:hover{background-color:#fff}.deliver-search-container .deliver-box .deliver-flow-box[data-v-79dec466]{padding-left:15px}.deliver-search-container .deliver-box .time-line-title[data-v-79dec466]{font-weight:500px;font-size:16px;margin-bottom:10px}",""]),e.exports=o},538:function(e,t,r){"use strict";r.r(t);var o=r(9),n=(r(53),r(473),r(12),{props:{value:{type:Boolean,default:!1},aid:{type:Number|String}},data:function(){return{showDialog:!1,deliverBuy:{},delivery:{},deliverFinish:{},deliverOrder:{},deliverShipment:{},deliverTake:{},timeLineArray:[]}},watch:{value:function(e){console.log(e,"val"),this.showDialog=e},showDialog:function(e){e&&this.aid&&(this.timeLineArray=[],this.getDeliverTraces()),this.$emit("input",e)}},methods:{getDeliverTraces:function(){var e=this;return Object(o.a)(regeneratorRuntime.mark((function t(){var data,r,o,n,d,l,c,v,f;return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return data={id:e.aid},t.next=3,e.$get("order/orderTraces",{params:data});case 3:1==(r=t.sent).code&&(o=r.data,n=o.buy,d=o.delivery,l=o.finish,c=o.order,v=o.shipment,f=o.take,e.deliverBuy=n,e.delivery=d,e.deliverFinish=l,e.deliverOrder=c,e.deliverShipment=v,e.deliverTake=f,e.timeLineArray.push(e.deliverFinish),e.timeLineArray.push(e.delivery),e.timeLineArray.push(e.deliverShipment),e.timeLineArray.push(e.deliverBuy),console.log(e.timeLineArray));case 5:case"end":return t.stop()}}),t)})))()},onCopy:function(){var e=document.createElement("input");e.value=this.deliverOrder.invoice_no,document.body.appendChild(e),e.select(),document.execCommand("Copy"),this.$message.success("复制成功"),e.remove()}}}),d=(r(526),r(8)),component=Object(d.a)(n,(function(){var e=this,t=e._self._c;return t("div",{staticClass:"deliver-search-container"},[t("el-dialog",{attrs:{visible:e.showDialog,top:"30vh",width:"900px",title:"物流查询"},on:{"update:visible":function(t){e.showDialog=t}}},[t("div",{staticClass:"deliver-box"},[t("div",{staticClass:"deliver-recode-box flex"},[t("div",{staticClass:"recode-img"},[t("el-image",{staticStyle:{width:"100%",height:"100%"},attrs:{fit:"cover",src:e.deliverOrder.image}}),e._v(" "),t("div",{staticClass:"float-count flex row-center"},[e._v("共"+e._s(e.deliverOrder.count)+"件商品")])],1),e._v(" "),t("div",{staticClass:"recode-info-container m-l-10"},[t("div",{staticClass:"flex"},[t("div",{staticClass:"recode-label"},[e._v("物流状态：")]),e._v(" "),t("div",{staticClass:"primary lg",staticStyle:{"font-weight":"500"}},[e._v(e._s(e.deliverOrder.tips))])]),e._v(" "),t("div",{staticClass:"flex",staticStyle:{margin:"6px 0"}},[t("div",{staticClass:"recode-label"},[e._v("快递公司：")]),e._v(" "),t("div",[e._v(e._s(e.deliverOrder.shipping_name))])]),e._v(" "),t("div",{staticClass:"flex"},[t("div",{staticClass:"recode-label"},[e._v("快递单号：")]),e._v(" "),t("div",[e._v(e._s(e.deliverOrder.invoice_no))]),e._v(" "),t("div",{staticClass:"copy-btn primary flex row-center",on:{click:e.onCopy}},[e._v("复制")])])])]),e._v(" "),t("div",{staticClass:"deliver-flow-box m-t-16"},[t("el-timeline",[e.deliverFinish.tips?t("el-timeline-item",[t("div",[t("div",{staticClass:"flex lg"},[t("div",{staticClass:"m-r-8",staticStyle:{"font-weight":"500"}},[e._v("\n                                    "+e._s(e.deliverTake.contacts)+"\n                                ")]),e._v(" "),t("div",{staticStyle:{"font-weight":"500"}},[e._v(e._s(e.deliverTake.mobile))])]),e._v(" "),t("div",{staticClass:"lighter m-t-8"},[e._v(e._s(e.deliverTake.address))])])]):e._e(),e._v(" "),e.deliverFinish.tips?t("el-timeline-item",{attrs:{timestamp:e.deliverFinish.time}},[t("div",{staticClass:"time-line-title"},[e._v(e._s(e.deliverFinish.title))]),e._v(" "),t("div",[e._v(e._s(e.deliverFinish.tips))])]):e._e(),e._v(" "),e.delivery.traces&&e.delivery.traces.length?t("el-timeline-item",{attrs:{timestamp:e.delivery.time}},[t("div",{staticClass:"time-line-title m-b-8"},[e._v(e._s(e.delivery.title))]),e._v(" "),e._l(e.delivery.traces,(function(r,o){return t("el-timeline-item",{key:o,attrs:{timestamp:r[0]}},[t("div",{staticClass:"muted"},[e._v(e._s(r[1]))])])}))],2):e._e(),e._v(" "),e.deliverShipment.tips?t("el-timeline-item",{attrs:{timestamp:e.deliverShipment.time}},[t("div",{staticClass:"time-line-title"},[e._v(e._s(e.deliverShipment.title))]),e._v(" "),t("div",[e._v(e._s(e.deliverShipment.tips))])]):e._e(),e._v(" "),e.deliverBuy.tips?t("el-timeline-item",{attrs:{timestamp:e.deliverBuy.time}},[t("div",{staticClass:"time-line-title"},[e._v(e._s(e.deliverBuy.title))]),e._v(" "),t("div",[e._v(e._s(e.deliverBuy.tips))])]):e._e()],1)],1)])])],1)}),[],!1,null,"79dec466",null);t.default=component.exports},596:function(e,t,r){var content=r(686);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[e.i,content,""]]),content.locals&&(e.exports=content.locals);(0,r(17).default)("77d9e96c",content,!0,{sourceMap:!1})},685:function(e,t,r){"use strict";r(596)},686:function(e,t,r){var o=r(16)(!1);o.push([e.i,".order-detail[data-v-08cd3b1f]{padding:0 16px 20px}.order-detail .detail-hd[data-v-08cd3b1f]{padding:14px 5px;border-bottom:1px solid #e5e5e5}.order-detail .address[data-v-08cd3b1f]{padding:16px 0}.order-detail .address>div[data-v-08cd3b1f]{margin-bottom:10px}.order-detail .address-item[data-v-08cd3b1f]{display:flex}.order-detail .address-item-label[data-v-08cd3b1f]{width:70px;text-align:justify;-moz-text-align-last:justify;text-align-last:justify}.order-detail .detail-con .title[data-v-08cd3b1f]{height:40px;background:#f2f2f2;border:1px solid #e5e5e5;padding:0 20px}.order-detail .detail-con .goods .goods-hd[data-v-08cd3b1f],.order-detail .detail-con .goods .goods-list[data-v-08cd3b1f]{padding:10px 20px;border:1px solid #e5e5e5;border-top:0 solid #e5e5e5}.order-detail .detail-con .goods .goods-hd .goods-item[data-v-08cd3b1f],.order-detail .detail-con .goods .goods-list .goods-item[data-v-08cd3b1f]{padding:10px 0}.order-detail .detail-con .goods .goods-hd .goods-item .goods-name[data-v-08cd3b1f],.order-detail .detail-con .goods .goods-list .goods-item .goods-name[data-v-08cd3b1f]{line-height:1.5}.order-detail .detail-con .goods .info .goods-img[data-v-08cd3b1f]{width:72px;height:72px;margin-right:10px}.order-detail .detail-con .goods .num[data-v-08cd3b1f],.order-detail .detail-con .goods .price[data-v-08cd3b1f],.order-detail .detail-con .goods .total[data-v-08cd3b1f]{width:150px}.order-detail .detail-footer[data-v-08cd3b1f]{padding:25px 20px;justify-content:flex-end}.order-detail .detail-footer .money>div[data-v-08cd3b1f]{text-align:right}.order-detail .detail-footer .money>div[data-v-08cd3b1f]:first-of-type{width:80px}.order-detail .detail-footer .money>div[data-v-08cd3b1f]:last-of-type{width:120px;display:flex;justify-content:flex-end}.order-detail .detail-footer .oprate-btn .btn[data-v-08cd3b1f]{width:152px;height:44px;cursor:pointer;border-radius:2px}.order-detail .detail-footer .oprate-btn .btn.plain[data-v-08cd3b1f],.order-detail .qr-container[data-v-08cd3b1f]{border:1px solid hsla(0,0%,89.8%,.89804)}.order-detail .qr-container[data-v-08cd3b1f]{width:120px;height:120px;padding:6px;border-radius:6px}",""]),e.exports=o},729:function(e,t,r){"use strict";r.r(t);r(29);var o=r(9),n=(r(86),r(53),r(486),{head:function(){return{title:this.$store.getters.headTitle,link:[{rel:"icon",type:"image/x-icon",href:this.$store.getters.favicon}]}},layout:"user",asyncData:function(e){return Object(o.a)(regeneratorRuntime.mark((function t(){var r,o,n,data;return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return r=e.$get,o=e.query,t.next=3,r("order/getOrderDetail",{params:{id:o.id}});case 3:if(n=t.sent,data=n.data,1!=n.code){t.next=8;break}return t.abrupt("return",{orderDetail:data,id:o.id});case 8:case"end":return t.stop()}}),t)})))()},data:function(){return{orderDetail:{},showDeliverPop:!1}},mounted:function(){this.orderDetail.delivery_type},methods:{getOrderDetail:function(){var e=this;return Object(o.a)(regeneratorRuntime.mark((function t(){var r,data;return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,e.$get("order/getOrderDetail",{params:{id:e.id}});case 2:r=t.sent,data=r.data,1==r.code&&(e.orderDetail=data);case 6:case"end":return t.stop()}}),t)})))()},handleOrder:function(e){var t=this;this.type=e,this.$confirm(this.getTipsText(e),{title:"温馨提示",center:!0,confirmButtonText:"确定",cancelButtonText:"取消",width:"300px",callback:function(e){"confirm"==e&&t.postOrder()}})},postOrder:function(){var e=this;return Object(o.a)(regeneratorRuntime.mark((function t(){var r,o,n,d,code,l;return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:r=e.type,o=e.id,n="",t.t0=r,t.next=0===t.t0?5:1===t.t0?7:2===t.t0?9:11;break;case 5:return n="order/cancel",t.abrupt("break",11);case 7:return n="order/del",t.abrupt("break",11);case 9:return n="order/confirm",t.abrupt("break",11);case 11:return t.next=13,e.$post(n,{id:o});case 13:d=t.sent,code=d.code,d.data,l=d.msg,1==code&&(e.$message({message:l,type:"success"}),1==r?setTimeout((function(){e.$router.go(-1)}),1500):e.getOrderDetail());case 18:case"end":return t.stop()}}),t)})))()},getTipsText:function(e){switch(e){case 0:return"确认取消订单吗？";case 1:return"确认删除订单吗?";case 2:return"确认收货吗?"}}},computed:{getOrderStatus:function(){return function(e){var text="";switch(e){case 0:text="待支付";break;case 1:text="待发货";break;case 2:text="待收货";break;case 3:text="已完成";break;case 4:text="订单已关闭"}return text}},getCancelTime:function(){return function(time){return time-Date.now()/1e3}}}}),d=(r(685),r(8)),component=Object(d.a)(n,(function(){var e=this,t=e._self._c;return t("div",{staticClass:"order-detail"},[t("div",{staticClass:"detail-hd row-between"},[t("div",{staticClass:"lg"},[e._v("订单详情")]),e._v(" "),t("div",{class:["status lg",{primary:0==e.orderDetail.order_status}]},[e._v("\n            "+e._s(e.orderDetail.order_status_desc)+"\n        ")])]),e._v(" "),1!=e.orderDetail.delivery_type?t("div",{staticClass:"address"},[t("div",{staticClass:"address-item"},[t("div",{staticClass:"lighter address-item-label"},[e._v("收件人：")]),e._v(" "),t("div",[e._v(e._s(e.orderDetail.consignee))])]),e._v(" "),t("div",{staticClass:"address-item"},[t("div",{staticClass:"lighter address-item-label"},[e._v("联系方式：")]),e._v(" "),t("div",[e._v(e._s(e.orderDetail.mobile))])]),e._v(" "),t("div",{staticClass:"address-item"},[t("div",{staticClass:"lighter address-item-label"},[e._v("收货地址：")]),e._v(" "),t("div",[e._v(e._s(e.orderDetail.delivery_address))])])]):e._e(),e._v(" "),t("div",{staticClass:"detail-con"},[t("div",{staticClass:"title flex"},[t("nuxt-link",{staticClass:"flex-1 lighter sm line-1 m-r-20",staticStyle:{"min-width":"0"},attrs:{to:"/shop_street_detail?id=".concat(e.orderDetail.shop.id)}},[e._v("\n                "+e._s(e.orderDetail.shop.name)+"\n            ")]),e._v(" "),t("div",{staticClass:"flex-1 lighter sm"},[e._v("\n                下单时间："+e._s(e.orderDetail.create_time)+"\n            ")]),e._v(" "),t("div",{staticClass:"flex-1 lighter sm"},[e._v("\n                订单编号："+e._s(e.orderDetail.order_sn)+"\n            ")]),e._v(" "),4!=e.orderDetail.pay_way_base?t("div",{class:["status sm",{primary:0==e.orderDetail.order_status}]},[e._v("\n                "+e._s(e.getOrderStatus(e.orderDetail.order_status))+"\n            ")]):e._e(),e._v(" "),4==e.orderDetail.pay_way_base?t("div",{class:["status sm",{primary:0==e.orderDetail.order_status}]},[e._v("\n                线下支付\n            ")]):e._e()],1),e._v(" "),t("div",{staticClass:"goods"},[e._m(0),e._v(" "),t("div",{staticClass:"goods-list"},e._l(e.orderDetail.order_goods,(function(r,o){return t("div",{key:o,staticClass:"goods-item flex"},[t("nuxt-link",{staticClass:"info flex flex-1",attrs:{to:"/goods_details/".concat(r.goods_id)}},[t("el-image",{staticClass:"goods-img",attrs:{src:r.image,alt:""}}),e._v(" "),t("div",{staticClass:"goods-info flex-1"},[t("div",{staticClass:"goods-name line-2"},[r.is_seckill?t("el-tag",{attrs:{size:"mini",effect:"plain"}},[e._v("秒杀")]):e._e(),e._v("\n                                "+e._s(r.goods_name)+"\n                            ")],1),e._v(" "),t("div",{staticClass:"sm lighter m-t-8"},[e._v("\n                                "+e._s(r.spec_value)+"\n                            ")])])],1),e._v(" "),t("div",{staticClass:"price"},[t("price-formate",{attrs:{price:r.goods_price}})],1),e._v(" "),t("div",{staticClass:"num flex row-center"},[e._v("\n                        "+e._s(r.goods_num)+"\n                    ")]),e._v(" "),t("div",{staticClass:"total flex row-center"},[t("price-formate",{attrs:{price:r.sum_price}})],1)],1)})),0)]),e._v(" "),e.orderDetail.user_remark?t("div",{staticClass:"m-t-16"},[t("span",{staticClass:"lighter m-r-8"},[e._v("买家留言：")]),e._v(" "),t("span",[e._v(e._s(e.orderDetail.user_remark))])]):e._e(),e._v(" "),e.orderDetail.delivery_content?t("div",{staticClass:"m-t-16"},[t("span",{staticClass:"lighter m-r-8"},[e._v("发货内容：")]),e._v(" "),t("span",[e._v(e._s(e.orderDetail.delivery_content))])]):e._e()]),e._v(" "),t("div",{staticClass:"detail-footer flex"},[t("div",[t("div",{staticClass:"flex-col",staticStyle:{"align-items":"flex-end"}},[t("div",{staticClass:"money flex m-b-8"},[t("div",{staticClass:"lighter"},[e._v("商品总价：")]),e._v(" "),t("div",[t("price-formate",{attrs:{price:e.orderDetail.goods_price}})],1)]),e._v(" "),t("div",{staticClass:"money flex m-b-8"},[t("div",{staticClass:"lighter"},[e._v("运费：")]),e._v(" "),t("div",[t("price-formate",{attrs:{price:e.orderDetail.shipping_price}})],1)]),e._v(" "),t("div",{staticClass:"money flex m-b-8"},[t("div",{staticClass:"lighter"},[e._v("会员抵扣：")]),e._v(" "),t("div",{staticClass:"primary"},[e._v("\n                        -"),t("price-formate",{attrs:{price:e.orderDetail.member_amount,color:"#FF0808"}})],1)]),e._v(" "),0!=e.orderDetail.discount_amount?t("div",{staticClass:"money flex m-b-16"},[t("div",{staticClass:"lighter"},[e._v("优惠券：")]),e._v(" "),t("div",[e._v("\n                        -\n                        "),t("price-formate",{attrs:{price:e.orderDetail.discount_amount}})],1)]):e._e(),e._v(" "),t("div",{staticClass:"money flex"},[t("div",{staticClass:"lighter"},[e._v("实付金额：")]),e._v(" "),t("div",{staticClass:"primary"},[t("price-formate",{attrs:{price:e.orderDetail.order_amount,"subscript-size":14,"first-size":28,"second-size":28}})],1)])]),e._v(" "),t("div",{staticClass:"oprate-btn flex row-right m-t-16"},[e.orderDetail.cancel_btn?t("div",{staticClass:"btn plain flex row-center lighter",on:{click:function(t){return e.handleOrder(0)}}},[e._v("\n                    取消订单\n                ")]):e._e(),e._v(" "),e.orderDetail.delivery_btn?t("div",{staticClass:"btn plain flex row-center m-l-8 lighter",on:{click:function(t){e.showDeliverPop=!0}}},[e._v("\n                    物流查询\n                ")]):e._e(),e._v(" "),e.orderDetail.take_btn?t("div",{staticClass:"btn bg-primary flex row-center white m-l-8",on:{click:function(t){return e.handleOrder(2)}}},[e._v("\n                    确认收货\n                ")]):e._e(),e._v(" "),e.orderDetail.del_btn?t("div",{staticClass:"btn plain flex row-center lighter m-l-8",on:{click:function(t){return e.handleOrder(1)}}},[e._v("\n                    删除订单\n                ")]):e._e(),e._v(" "),e.orderDetail.pay_btn&&4!=e.orderDetail.pay_way_base?t("nuxt-link",{staticClass:"btn bg-primary flex row-center white m-l-8",attrs:{to:"/payment?id=".concat(e.orderDetail.id,"&from=order")}},[t("span",{staticClass:"mr8"},[e._v("去付款")]),e._v(" "),e.getCancelTime(e.orderDetail.order_cancel_time)>0?t("count-down",{attrs:{time:e.getCancelTime(e.orderDetail.order_cancel_time),format:"hh:mm:ss"},on:{finish:e.getOrderDetail}}):e._e()],1):e._e()],1)])]),e._v(" "),t("deliver-search",{attrs:{aid:e.id},model:{value:e.showDeliverPop,callback:function(t){e.showDeliverPop=t},expression:"showDeliverPop"}})],1)}),[function(){var e=this,t=e._self._c;return t("div",{staticClass:"goods-hd lighter flex"},[t("div",{staticClass:"info flex-1"},[e._v("商品信息")]),e._v(" "),t("div",{staticClass:"price flex row-center"},[e._v("商品价格")]),e._v(" "),t("div",{staticClass:"num flex row-center"},[e._v("数量")]),e._v(" "),t("div",{staticClass:"total flex row-center"},[e._v("合计")])])}],!1,null,"08cd3b1f",null);t.default=component.exports;installComponents(component,{PriceFormate:r(478).default,CountDown:r(485).default,DeliverSearch:r(538).default})}}]);