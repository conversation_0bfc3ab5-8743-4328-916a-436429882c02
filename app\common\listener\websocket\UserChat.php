<?php

namespace app\common\listener\websocket;

use app\common\enum\ChatMsgEnum;
use app\common\enum\ChatRecordEnum;
use app\common\model\goods\Goods;
use app\common\model\kefu\ChatRecord;
use app\common\server\UrlServer;
use app\common\websocket\Response;
use think\facade\Log;

/**
 * 用户对用户聊天事件
 * Class UserChat
 * @package app\common\listener\websocket
 */
class UserChat
{
    protected $response;

    public function __construct(Response $response)
    {
        $this->response = $response;
    }

    public function handle($params)
    {
        $from_fd = $params['fd'];
        $request_data = $params['data'];
        $handleClass = $params['handle'];

        $from_data = $handleClass->getDataByFd($from_fd);

        // 验证发送者信息
        if (empty($from_data['type']) || empty($from_data['uid'])) {
            return $handleClass->pushData($from_fd, 'error', $this->response->formatSendError('用户信息不存在或不在线'));
        }

        // 验证是否为用户对用户聊天类型
        if ($from_data['type'] !== 'user_chat' && $from_data['type'] !== 'user') {
            return $handleClass->pushData($from_fd, 'error', $this->response->formatSendError('无效的聊天类型'));
        }

        // 验证接收者ID
        if (empty($request_data['to_id']) || !is_numeric($request_data['to_id'])) {
            return $handleClass->pushData($from_fd, 'error', $this->response->formatSendError('接收者ID无效'));
        }

        // 验证消息内容
        if (empty($request_data['msg']) && $request_data['msg_type'] == ChatMsgEnum::TYPE_TEXT) {
            return $handleClass->pushData($from_fd, 'error', $this->response->formatSendError('消息内容不能为空'));
        }

        // 获取接收者连接
        $to_fd = $handleClass->getFdByUid($request_data['to_id'], 'user_chat');
        if (empty($to_fd)) {
            // 如果用户不在用户聊天模式，尝试普通用户模式
            $to_fd = $handleClass->getFdByUid($request_data['to_id'], 'user');
        }

        // 添加聊天记录
        $record = $this->insertRecord([
            'shop_id' => 0, // 用户对用户聊天不属于特定商家
            'from_id' => $from_data['uid'],
            'from_type' => 'user',
            'to_id' => $request_data['to_id'],
            'to_type' => 'user',
            'msg' => $request_data['msg'],
            'msg_type' => $request_data['msg_type'],
            'voice_duration' => $request_data['voice_duration'] ?? 0,
        ]);

        if (!$record) {
            return $handleClass->pushData($from_fd, 'error', $this->response->formatSendError('消息发送失败'));
        }

        // 格式化消息数据
        $record['from_avatar'] = !empty($from_data['avatar']) ? UrlServer::getFileUrl($from_data['avatar']) : '';
        $record['from_nickname'] = $from_data['nickname'];
        $record['create_time_stamp'] = $record['create_time'];
        $record['create_time'] = date('Y-m-d H:i:s', $record['create_time']);
        $record['update_time'] = date('Y-m-d H:i:s', $record['update_time']);
        $record['goods'] = [];
        
        if ($request_data['msg_type'] == ChatMsgEnum::TYPE_GOODS) {
            $record['goods'] = json_decode($record['msg'], true);
        }

        // 发送给发送者（确认消息）
        $handleClass->pushData($from_fd, 'user_chat', $record);

        // 发送给接收者
        if (!empty($to_fd)) {
            $handleClass->pushData($to_fd, 'user_chat', $record);
        } else {
            // 接收者不在线，可以考虑存储离线消息或推送通知
            Log::info("用户对用户消息发送: 接收者不在线, from_id={$from_data['uid']}, to_id={$request_data['to_id']}");
        }

        return true;
    }

    /**
     * 增加聊天记录
     * @param $data
     * @return array|false
     */
    public function insertRecord($data)
    {
        try {
            switch ($data['msg_type']) {
                case ChatMsgEnum::TYPE_IMG:
                    $msg = $data['msg'];
                    break;

                case ChatMsgEnum::TYPE_GOODS:
                    $goods = Goods::where(['id' => $data['msg']])->field([
                        'id', 'image', 'min_price', 'name'
                    ])->findOrEmpty();

                    $msg = json_encode([
                        'id' => $goods['id'] ?? 0,
                        'image' => $goods->getData('image') ?? '',
                        'min_price' => $goods['min_price'] ?? 0,
                        'name' => $goods['name'] ?? '',
                    ], true);
                    break;

                case ChatMsgEnum::TYPE_VOICE:
                    $msg = $data['msg'];
                    break;

                case ChatMsgEnum::TYPE_VIDEO:
                    $msg = $data['msg'];
                    break;

                case ChatMsgEnum::TYPE_ORDER:
                    $msg = $data['msg'];
                    break;

                default:
                    $msg = htmlspecialchars($data['msg']);
            }

            $result = ChatRecord::create([
                'shop_id' => $data['shop_id'],
                'from_id' => $data['from_id'],
                'from_type' => $data['from_type'],
                'to_id' => $data['to_id'],
                'to_type' => $data['to_type'],
                'msg' => $msg,
                'msg_type' => $data['msg_type'],
                'voice_duration' => $data['voice_duration'] ?? 0,
                'is_read' => 0,
                'type' => ChatRecordEnum::TYPE_NORMAL,
                'create_time' => time(),
            ]);

            return $result->toArray();
        } catch (\Throwable $e) {
            Log::error("用户对用户聊天记录插入失败: " . $e->getMessage());
            return false;
        }
    }
}
