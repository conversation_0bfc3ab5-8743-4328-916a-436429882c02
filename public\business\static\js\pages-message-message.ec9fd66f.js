(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-message-message"],{1993:function(n,e,t){"use strict";t.r(e);var o=t("af22"),i=t.n(o);for(var a in o)["default"].indexOf(a)<0&&function(n){t.d(e,n,(function(){return o[n]}))}(a);e["default"]=i.a},"540e":function(n,e,t){"use strict";t("6a54");var o=t("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.apiSilentLogin=e.apiOALogin=e.apiKFLogin=e.apiCodeUrlGet=e.apiAuthLogin=e.apiAccountLogin=void 0;var i=o(t("9b1b")),a=o(t("be47"));e.apiAccountLogin=function(n){return a.default.post("login/account",(0,i.default)({},n))};e.apiAuthLogin=function(n){return a.default.post("login/authLogin",n)};e.apiSilentLogin=function(n){return a.default.post("login/silentLogin",n)};e.apiOALogin=function(n){return a.default.post("login/oaLogin",n)};e.apiCodeUrlGet=function(){return a.default.get("login/codeUrl",{params:{url:encodeURIComponent(location.href)}})};e.apiKFLogin=function(n){return a.default.post("index/Kefulogin",n)}},"81ab":function(n,e,t){"use strict";t.r(e);var o=t("ad92"),i=t("1993");for(var a in i)["default"].indexOf(a)<0&&function(n){t.d(e,n,(function(){return i[n]}))}(a);var u=t("828b"),r=Object(u["a"])(i["default"],o["b"],o["c"],!1,null,"136bbf4e",null,!1,o["a"],void 0);e["default"]=r.exports},ad92:function(n,e,t){"use strict";t.d(e,"b",(function(){return o})),t.d(e,"c",(function(){return i})),t.d(e,"a",(function(){}));var o=function(){var n=this,e=n.$createElement,t=n._self._c||e;return t("v-uni-view",[t("v-uni-web-view",{attrs:{src:n.$store.state.app.config.kefu_service_url+"?token="+n.token},on:{message:function(e){arguments[0]=e=n.$handleEvent(e),n.handleMessage.apply(void 0,arguments)}}})],1)},i=[]},af22:function(n,e,t){"use strict";t("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o=t("540e"),i={data:function(){return{token:""}},onLoad:function(n){this.login()},methods:{handleMessage:function(n){alert(n);var e=n.detail.data;console.log("收到来自WebView的消息:",e)},login:function(){var n=this;console.log(this.$store.state.app.userinfo),(0,o.apiKFLogin)({shop_id:this.$store.state.app.userinfo.shop_id,admin_id:this.$store.state.app.userinfo.id}).then((function(e){n.token=e.token})).catch((function(n){console.error(n)}))}}};e.default=i}}]);