<style>
    .layui-table-cell {
        height: auto;
    }
</style>
<div class="layui-fluid">

    <div class="layui-form layui-card-header layuiadmin-card-header-auto">
        <div class="layui-form-item">

            <div class="layui-inline">
                <label class="layui-form-label">会员信息:</label>
                <div class="layui-input-block">
                    <select name="search_key">
                        <option value="sn">会员编号</option>
                        <option value="nickname">用户昵称</option>
                        <option value="mobile">手机号码</option>
                    </select>
                </div>
            </div>

            <div class="layui-inline">
                <input type="text" name="keyword" id="audit_keyword" placeholder="请输入搜索内容" autocomplete="off" class="layui-input">
            </div>

            <div class="layui-inline">
                <label class="layui-form-label">审核状态:</label>
                <div class="layui-input-block">
                    <select name="status" id="audit_status">
                        <option value="">全部</option>
                        {foreach $status as $item => $val}
                        <option value="{$item}">{$val}</option>
                        {/foreach}
                    </select>
                </div>
            </div>

            <div class="layui-inline">
                <button class="layui-btn layui-btn-sm layuiadmin-btn-audit {$view_theme_color}" lay-submit lay-filter="audit-search">
                    查询
                </button>
                <button class="layui-btn layui-btn-sm layuiadmin-btn-audit layui-btn-primary " lay-submit
                        lay-filter="audit-clear-search">重置
                </button>
            </div>
        </div>
    </div>


    <table id="audit-lists" lay-filter="audit-lists"></table>

    <script type="text/html" id="audit-operation">
        {{#  if(d.status == 0){ }}
        <a class="layui-btn layui-btn-normal layui-btn-sm" lay-event="pass">审核通过</a>
        <a class="layui-btn layui-btn-danger layui-btn-sm" lay-event="refuse">审核拒绝</a>
        {{#  } }}
    </script>

    <script type="text/html" id="icon">
        <img src="{{d.icon}}" style="height:80px;width: 80px" class="image-show">
    </script>

    <!--会员信息-->
    <script type="text/html" id="user-info">
        <img src="{{d.avatar}}" style="height:80px;width: 80px" class="image-show">
        <div class="layui-input-inline"  style="text-align: left">
            <p>会员编号:{{d.sn}}</p>
            <p>用户昵称:{{d.nickname}}</p>
            <p>手机号码:{{d.mobile}}</p>
            <p>会员等级:{{d.level}}</p>
            <!--            <p>性别:{{d.sex}}</p>-->
            <!--            <p>注册时间:{{d.create_time}}</p>-->
        </div>
    </script>

    <!--上级推荐人信息-->
    <script type="text/html" id="leader-info">
        {{#  if(d.leader.length == 0){ }}
        <p>无</p>

        {{#  } else { }}
        <div class="layui-input-inline" >
            <p>会员编号:{{d.leader.sn}}</p>
            <p>用户昵称:{{d.leader.nickname}}</p>
            <!--                <p>手机号码:{{d.leader.mobile}}</p>-->
            <p>会员等级: {{d.leader.level}}</p>
        </div>
        {{#  } }}
    </script>

</div>