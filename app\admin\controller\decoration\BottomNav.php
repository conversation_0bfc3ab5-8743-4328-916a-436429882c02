<?php

namespace app\admin\controller\decoration;
use app\admin\logic\decoration\BottomNavLogic;
use app\common\basics\AdminBase;
use app\common\server\JsonServer;

class BottomNav extends AdminBase{

    /**
     * Notes:获取底部导航
     * @return \think\response\View
     * @author: cjhao 2021/4/22 11:45
     */
    public function lists(){
        if($this->request->isAjax()){
            $list = BottomNavLogic::lists();
            return JsonServer::success('',$list);
        }
        return view();
    }

    /**
     * Notes:编辑底部导航
     * @return \think\response\Json|\think\response\View
     * @author: cjhao 2021/4/27 16:09
     */
    public function edit(){
        if($this->request->isAjax()){
            $post = $this->request->post();
            BottomNavLogic::edit($post);
            return JsonServer::success('修改成功');

        }
        $id = $this->request->get('id');
        $detail = BottomNavLogic::getBootomNav($id);
        return view('',['detail'=>$detail]);


    }
}