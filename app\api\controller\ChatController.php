<?php

namespace app\api\controller;

use app\common\basics\Api;
use app\common\logic\ChatLogic;

/**
 * 聊天相关API控制器
 */
class ChatController extends Api
{
    /**
     * 检查客服配置
     */
    public function checkConfig()
    {
        try {
            $shop_id = $this->request->post('shop_id/d', 0);
            
            // 检查客服配置
            $check = ChatLogic::checkConfig($shop_id);
            
            if ($check === true) {
                return $this->success('客服配置正常', [
                    'shop_id' => $shop_id,
                    'config_setting' => ChatLogic::getConfigSetting($shop_id),
                    'cache_drive' => ChatLogic::getCacheDrive(),
                    'chat_prefix' => ChatLogic::getChatPrefix()
                ]);
            } else {
                return $this->fail(ChatLogic::getError() ?: '客服配置检查失败');
            }
        } catch (\Exception $e) {
            return $this->fail('检查配置时发生错误: ' . $e->getMessage());
        }
    }
    
    /**
     * 获取客服配置详情
     */
    public function getConfigDetail()
    {
        try {
            $shop_id = $this->request->post('shop_id/d', 0);
            
            $config_setting = ChatLogic::getConfigSetting($shop_id);
            $cache_drive = ChatLogic::getCacheDrive();
            $chat_prefix = ChatLogic::getChatPrefix();
            
            $config_text = '';
            switch ($config_setting) {
                case 1:
                    $config_text = '人工客服';
                    break;
                case 2:
                    $config_text = '在线客服';
                    break;
                default:
                    $config_text = '未知配置';
            }
            
            return $this->success('获取配置成功', [
                'shop_id' => $shop_id,
                'config_setting' => $config_setting,
                'config_text' => $config_text,
                'cache_drive' => $cache_drive,
                'chat_prefix' => $chat_prefix,
                'is_valid' => ($config_setting == 2 && $cache_drive == 'redis')
            ]);
        } catch (\Exception $e) {
            return $this->fail('获取配置时发生错误: ' . $e->getMessage());
        }
    }
    
    /**
     * 测试WebSocket连接参数
     */
    public function testConnectionParams()
    {
        try {
            $token = $this->request->post('token/s', '');
            $shop_id = $this->request->post('shop_id/d', 0);
            $type = $this->request->post('type/s', 'kefu');
            $client = $this->request->post('client/d', 2);
            
            if (empty($token)) {
                return $this->fail('token不能为空');
            }
            
            // 构建WebSocket连接URL
            $ws_url = "wss://kefu.huohanghang.cn/?type={$type}&token={$token}&client={$client}&shop_id={$shop_id}";
            
            // 检查配置
            $config_check = ChatLogic::checkConfig($shop_id);
            
            return $this->success('连接参数生成成功', [
                'ws_url' => $ws_url,
                'params' => [
                    'token' => $token,
                    'type' => $type,
                    'client' => $client,
                    'shop_id' => $shop_id
                ],
                'config_valid' => $config_check === true,
                'config_error' => $config_check === true ? null : ChatLogic::getError()
            ]);
        } catch (\Exception $e) {
            return $this->fail('生成连接参数时发生错误: ' . $e->getMessage());
        }
    }
}
