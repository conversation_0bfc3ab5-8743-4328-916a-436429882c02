# 微信公众号取消关注检测功能说明

## 功能概述

本功能增强了微信公众号关注用户同步命令，能够检测并标记取消关注的用户。通过三阶段同步机制，准确识别用户的关注状态变化。

## 工作原理

### 三阶段同步机制

1. **第一阶段：标记待验证**
   - 将所有现有的已关注用户（subscribe=1）标记为待验证状态（subscribe=2）
   - 这样可以区分本次同步前后的用户状态

2. **第二阶段：同步关注用户**
   - 从微信API获取当前所有关注用户列表
   - 将获取到的用户标记为已关注状态（subscribe=1）
   - 更新用户的详细信息（昵称、头像等）

3. **第三阶段：处理取消关注**
   - 将仍然是待验证状态（subscribe=2）的用户标记为未关注（subscribe=0）
   - 这些用户就是在本次同步中发现的取消关注用户

### 状态值说明

- `subscribe = 0`：未关注/已取消关注
- `subscribe = 1`：已关注
- `subscribe = 2`：待验证状态（仅在同步过程中临时使用）

## 使用方法

### 手动执行同步

```bash
cd /path/to/kshop
php think wechat:followers
```

### 定时任务执行

使用提供的脚本设置定时任务：

```bash
bash scripts/setup_wechat_cron.sh
```

## 输出信息

同步完成后会显示以下统计信息：

```
开始同步微信公众号关注用户列表...
标记现有用户为待验证状态...
已标记 150 个现有用户为待验证状态
已处理 120 个用户
处理取消关注的用户...
发现 30 个用户取消关注
同步完成！总计: 120 个用户，新增: 5 个用户，取消关注: 30 个用户
```

## 日志记录

同步过程会记录详细的日志信息到 `ls_log` 表中：

```json
{
    "total_count": 120,
    "new_count": 5,
    "unfollow_count": 30,
    "mark_count": 150
}
```

## 数据库变更

执行以下SQL文件来更新数据库结构：

```bash
mysql -u用户名 -p数据库名 < database/migrations/20241229_update_wechat_followers_subscribe_field.sql
```

## 注意事项

1. **同步频率**：建议每3-5分钟执行一次，避免过于频繁调用微信API
2. **API限制**：注意微信API的调用频率限制
3. **数据一致性**：同步过程中如果出现异常，可能会有部分用户保持待验证状态，下次同步会自动修正
4. **历史数据**：首次启用此功能时，所有现有用户都会被重新验证一次

## 相关文件

- `app/common/command/WechatFollowers.php` - 主要同步命令
- `database/wechat_followers.sql` - 数据表结构
- `database/migrations/20241229_update_wechat_followers_subscribe_field.sql` - 字段更新脚本
- `scripts/setup_wechat_cron.sh` - 定时任务设置脚本

## 故障排除

### 如果发现大量用户被误标记为取消关注

1. 检查微信公众号配置是否正确
2. 检查网络连接是否稳定
3. 查看错误日志：`SELECT * FROM ls_log WHERE type = 'wechat_followers_sync' ORDER BY create_time DESC LIMIT 10`

### 如果同步过程中断

1. 手动将所有待验证状态的用户恢复为已关注：
   ```sql
   UPDATE ls_wechat_followers SET subscribe = 1 WHERE subscribe = 2;
   ```
2. 重新执行同步命令
