<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Cdb\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * DescribeDatabases返回参数结构体
 *
 * @method integer getTotalCount() 获取符合查询条件的实例总数。
 * @method void setTotalCount(integer $TotalCount) 设置符合查询条件的实例总数。
 * @method array getItems() 获取返回的实例信息。
 * @method void setItems(array $Items) 设置返回的实例信息。
 * @method array getDatabaseList() 获取数据库名以及字符集
 * @method void setDatabaseList(array $DatabaseList) 设置数据库名以及字符集
 * @method string getRequestId() 获取唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
 * @method void setRequestId(string $RequestId) 设置唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
 */
class DescribeDatabasesResponse extends AbstractModel
{
    /**
     * @var integer 符合查询条件的实例总数。
     */
    public $TotalCount;

    /**
     * @var array 返回的实例信息。
     */
    public $Items;

    /**
     * @var array 数据库名以及字符集
     */
    public $DatabaseList;

    /**
     * @var string 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
     */
    public $RequestId;

    /**
     * @param integer $TotalCount 符合查询条件的实例总数。
     * @param array $Items 返回的实例信息。
     * @param array $DatabaseList 数据库名以及字符集
     * @param string $RequestId 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("TotalCount",$param) and $param["TotalCount"] !== null) {
            $this->TotalCount = $param["TotalCount"];
        }

        if (array_key_exists("Items",$param) and $param["Items"] !== null) {
            $this->Items = $param["Items"];
        }

        if (array_key_exists("DatabaseList",$param) and $param["DatabaseList"] !== null) {
            $this->DatabaseList = [];
            foreach ($param["DatabaseList"] as $key => $value){
                $obj = new DatabasesWithCharacterLists();
                $obj->deserialize($value);
                array_push($this->DatabaseList, $obj);
            }
        }

        if (array_key_exists("RequestId",$param) and $param["RequestId"] !== null) {
            $this->RequestId = $param["RequestId"];
        }
    }
}
