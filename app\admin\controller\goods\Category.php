<?php


namespace app\admin\controller\goods;

use app\common\basics\AdminBase;
use app\admin\logic\goods\CategoryLogic;
use app\admin\validate\goods\CategoryValidate;
use think\exception\ValidateException;
use app\common\server\JsonServer;
use think\facade\View;

/**
 * 平台商品分类
 * Class Category
 * @package app\admin\controller\goods
 */
class Category extends AdminBase
{
    /**
     * 列表
     */
    public function lists()
    {
        if ($this->request->isAjax()) {
            $category_tree = CategoryLogic::lists();
            // reqData方式渲染
            $treeTableData = [
                'code' => 0,
                'msg' => '分类列表',
                'data' => json_encode($category_tree)
            ];
            return json($treeTableData);
        }
        return view();
    }

    /**
     * 添加
     */
    public function add()
    {
        if ($this->request->isAjax()) {
            $post = $this->request->post();
            $post['del'] = 0;
            try {
                validate(CategoryValidate::class)->scene('add')->check($post);
            } catch (ValidateException $e) {
                return JsonServer::error($e->getError());
            }
            $res = CategoryLogic::add($post);
            if ($res) {
                return JsonServer::success('分类添加成功');
            } else {
                return JsonServer::error('分类添加失败');
            }
        }

        $category_list = CategoryLogic::categoryTwoTree();
        return view('add', ['category_list' => $category_list]);
    }

    /**
     * 删除
     */
    public function del()
    {
        $post = $this->request->post();
        try {
            validate(CategoryValidate::class)->scene('del')->check($post);
        } catch (ValidateException $e) {
            return JsonServer::error($e->getError());
        }
        $res = CategoryLogic::del($post);
        if ($res) {
            return JsonServer::success('删除分类成功');
        } else {
            return JsonServer::error('删除分类失败');
        }
    }


    /**
     * 编辑
     */
    public function edit()
    {
        if ($this->request->isAjax()) {
            $post = $this->request->post();
            $post['del'] = 0;
            try {
                validate(CategoryValidate::class)->scene('edit')->check($post);
            } catch (ValidateException $e) {
                return JsonServer::error($e->getError());
            }
            $res = CategoryLogic::edit($post);
            if ($res) {
                return JsonServer::success('编辑分类成功');
            } else {
                return JsonServer::error('编辑分类失败');
            }
        }

        $id = $this->request->get('id');
        $detail = CategoryLogic::getCategory($id);
        $category_list = CategoryLogic::categoryTwoTree();
        return view('edit', [
            'detail' => $detail,
            'category_list' => $category_list
        ]);
    }

    /**
     * 修改显示状态（保留兼容性）
     */
    public function switchStatus()
    {
        $post = $this->request->post();
        $res = CategoryLogic::switchStatus($post);
        if ($res) {
            return JsonServer::success('修改成功');
        } else {
            return JsonServer::error('修改失败');
        }
    }

    /**
     * 切换商家端可选状态
     */
    public function switchShopVisible()
    {
        $post = $this->request->post();
        $res = CategoryLogic::switchShopVisible($post);
        if ($res) {
            return JsonServer::success('修改成功');
        } else {
            return JsonServer::error('修改失败');
        }
    }

    /**
     * 切换API显示状态
     */
    public function switchApiVisible()
    {
        $post = $this->request->post();
        $res = CategoryLogic::switchApiVisible($post);
        if ($res) {
            return JsonServer::success('修改成功');
        } else {
            return JsonServer::error('修改失败');
        }
    }

    /**
     * 更新字段值（内联编辑）
     */
    public function updateField()
    {
        $post = $this->request->post();

        // 记录调试信息
        \think\facade\Log::info('updateField接收到的参数:', $post);

        // 验证必要参数
        if (empty($post['id']) || empty($post['field']) || !isset($post['value'])) {
            \think\facade\Log::error('参数不完整:', $post);
            return JsonServer::error('参数不完整');
        }

        // 允许更新的字段白名单
        $allowedFields = ['name', 'sort'];
        if (!in_array($post['field'], $allowedFields)) {
            \think\facade\Log::error('不允许更新的字段:', $post['field']);
            return JsonServer::error('不允许更新该字段');
        }

        // 验证数据
        $validateData = [
            'id' => $post['id'],
            $post['field'] => $post['value']
        ];

        try {
            if ($post['field'] === 'name') {
                validate(CategoryValidate::class)->scene('updateName')->check($validateData);
            } elseif ($post['field'] === 'sort') {
                validate(CategoryValidate::class)->scene('updateSort')->check($validateData);
            }
        } catch (ValidateException $e) {
            \think\facade\Log::error('验证失败:', $e->getError());
            return JsonServer::error($e->getError());
        }

        // 调用逻辑层更新
        $res = CategoryLogic::updateField($post['id'], $post['field'], $post['value']);
        \think\facade\Log::info('更新结果:', ['result' => $res]);

        if ($res) {
            return JsonServer::success('更新成功');
        } else {
            return JsonServer::error('更新失败');
        }
    }
}