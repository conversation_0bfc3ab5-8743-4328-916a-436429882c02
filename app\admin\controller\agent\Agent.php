<?php
namespace app\admin\controller\agent;

use app\admin\logic\finance\ShopWithdrawalLogic;
use think\facade\Db;
use think\facade\View;
use app\common\model\agent\Agent as AgentModel;
use app\common\model\agent\AgentMerchantfees;
use app\admin\logic\agent\AgentLogic;
use app\admin\logic\agent\AgentDepositLogic;
use app\common\basics\AdminBase;
use app\common\server\JsonServer;
use app\common\logic\OrderRefundLogic;
use app\common\server\UrlServer;

class Agent extends AdminBase
{
    /**
     * 代理详情设置页面
     * @return \think\response\View
     */
    public function detail()
    {
        $user_id = $this->request->get('id/d', 0);
        if (empty($user_id)) {
            return JsonServer::error('参数错误，用户ID缺失');
        }
        // 获取用户信息
        $user = \app\common\model\user\User::where('id', $user_id)->find();
        if (!$user) {
            return JsonServer::error('用户不存在');
        }
        // 获取代理信息
        $agent = AgentModel::where('user_id', $user_id)->find();
        $user['avatar']=UrlServer::getFileUrl($user['avatar']);
         $region_desc='暂无';
        if(!empty($agent['province'])){
            $region = Db::name('dev_region')
            ->where('id', 'IN', [$agent['province'], $agent['city'], $agent['district']])
            ->order('level asc')
            ->column('name');
              $region_desc = implode('-', $region);
        }
      

      
        // 获取顾问信息（如有）
        
        return view('agent/detail', [
            'user' => $user,
            'agent' => $agent,
            'region_desc'=>$region_desc
        ]);
    }

    /*
     * 代理列表
     *
     */
     public function index(){
         if ($this->request->isPost()) {
             $params = $this->request->post();
             $result = AgentLogic::lists($params);
             return JsonServer::success('', $result);
         }
         $total = AgentLogic::depositTotal();
         $levels = ['一级代理', '二级代理', '三级代理'];
         return view('agent/index', ['levels' => $levels,'total'=>$total]);
     }

    /**
     * 获取不同类型的用户列表
     * @return \think\response\Json
     */
    public function getUsersByType()
    {
        $user_id = $this->request->get('user_id', 0);
        $type = $this->request->get('type', 0);
        $page = $this->request->get('page', 1);
        $limit = $this->request->get('limit', 10);

        $data = AgentLogic::getUsersByType($user_id, $type, $page, $limit);
        return JsonServer::success('获取成功', $data);
    }

    /*
     * 代理结算
     * @notes 代理结算
     */
    public function settle()
    {
        if($this->request->isAjax()){
            $get= $this->request->post();
            $lists = AgentLogic::settle($get);
            return JsonServer::success('获取成功', $lists);
        }

        $statistics = AgentLogic::statistics2();
        View::assign('statistics', $statistics);
        return view('agent/settle');
    }

    /**
     * @Notes: 代理结算记录
     * @Author: 张无忌
     */
    public function settlementRecord()
    {
        if ($this->request->isAjax()) {
            $get = $this->request->get();
            $lists = AgentLogic::record($get);
            return JsonServer::success('获取成功', $lists);
        }

        $shop_id = $this->request->get('agent_id');
        $statistics = AgentLogic::statistics2($shop_id);
        View::assign('agent_id', $shop_id);
        View::assign('statistics', $statistics);
        return view('agent/settlement_record');
    }
    /**
     * @notes 渲染开通代理表单页面
     * @return \think\response\View
     */
    public function openAgentForm()
    {
        return view('agent/open_agent_form');
    }

    /**
     * @notes 后台开通代理
     * @return \think\response\Json
     */
    public function open()
    {
        if($this->request->isPost()) {
            $params = $this->request->post();
            $result = AgentLogic::openAgentFromAdmin($params);
            if($result) {
                return JsonServer::success('开通成功');
            }
            return JsonServer::error(AgentLogic::getError() ?: '开通失败');
        }
        return JsonServer::error('请求方式错误');
    }

    /**
     * @notes 获取用户上级代理信息
     * @return \think\response\Json
     */
    public function getSupervisorInfo()
    {
        if($this->request->isPost()) {
            $user_identifier = $this->request->post('user_identifier');
            $result = AgentLogic::getAgentSupervisorInfo($user_identifier);
            if($result) {
                return JsonServer::success('获取成功', $result);
            }
            return JsonServer::error(AgentLogic::getError() ?: '用户不存在或查询失败');
        }
        return JsonServer::error('请求方式错误');
    }


    /**
     * @notes 用户列表
     * @return \think\response\Json|\think\response\View
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date 2021/9/3 11:50
     */
    public function userLists()
    {
        if ($this->request->isPost()) {
            $params = $this->request->post();
            $lists = AgentLogic::getUserLists($params);
            return JsonServer::success('', $lists);
        }
        return view('agent/user_lists');
    }


    /**
     * @notes 代理调整
     * @return \think\response\Json|\think\response\View
     * <AUTHOR>
     * @date 2021/9/3 14:10
     */
    public function adjust()
    {
        if($this->request->isPost()) {
            $params = $this->request->post();
            $result = AgentLogic::adjust($params);
            if($result) {
                return JsonServer::success('调整成功');
            }
            return JsonServer::error(AgentLogic::getError());
        }
        $params = $this->request->get();
        $user = AgentLogic::getUser($params);
        return view('agent/adjust', [
            'user' => $user
        ]);
    }

    /**
     * @notes 代理配置
     */
    public function setting()
    {

        if ($this->request->isAjax()) {
            $post = $this->request->post();

            $aa=AgentLogic::setConfig($post);

            return $aa;
        }
        $config = AgentLogic::getConfig();
        return view('agent/setting', ['config' => $config]);
    }
    /**
     * @Notes: 商家结算详细记录
     * @Author: 张无忌
     */
    public function settlementDetail()
    {
        $settle_id = $this->request->get('id');
        $get = $this->request->get();
        $lists = AgentLogic::detail($get);

        View::assign('id', $settle_id);
        View::assign('detail', $lists);
        return view('agent/settlement_detail');
    }
    /*
    * 取消代理
    */
    public function isOrderFreeze()
    {
        if ($this->request->isAjax()) {
            $post = $this->request->post();
            $result = AgentLogic::audit($post);
            if (false === $result) {
                return JsonServer::error(AgentLogic::getError() ?: '操作失败');
            }
            return JsonServer::success('操作成功');
        }
        $id = $this->request->get();
        $detail=AgentLogic::detail($id);
        if(!isset($detail['detal'])){
            return JsonServer::error( '信息失效');
        }
        return view('agent/audit', [
            'detail' => $detail
        ]);

    }
    /*
     * 取消代理
     */
    public function isFreeze()
    {
        $post = $this->request->post();
        $result=AgentLogic::freeze($post);
        if($result) {
            return JsonServer::success('调整成功');
        }

    }


    /**
     * @Notes: 商家提现列表
     * @Author: 张无忌
     */
    public function withdrawal()
    {
        if($this->request->isAjax()){
            $get= $this->request->get();
            $lists = AgentLogic::withdrawalists($get);
            return JsonServer::success('获取成功', $lists);
        }

        View::assign('summary', AgentLogic::summary());
        View::assign('statistics', AgentLogic::statistics2());
        return view('agent/withdrawal');
    }

    /**
     * @Notes: 商家提现详细
     * @Author: 张无忌
     * @return \think\response\View
     */
    public function withdrawalDetail()
    {
        $id = $this->request->get('id');
        View::assign('detail', AgentLogic::Agentdetail($id));
        return view('agent/withdrawal_detail');
    }

    /**
     * @Notes: 商家提现统计
     * @Author: 张无忌
     */
    public function withdrawalStatistics()
    {
        if ($this->request->isAjax()) {
            $statistics = ShopWithdrawalLogic::statistics();
            return JsonServer::success('获取成功', $statistics);
        }

        return JsonServer::error('请求异常');
    }

    /**
     * @Notes: 审核商家提现
     * @Author: 张无忌
     */
    public function withdrawalExamine()
    {
        if ($this->request->isAjax()) {
            $post = $this->request->post();
            $res = AgentLogic::examine($post);
            if ($res === false) {
                $error = AgentLogic::getError() ?: '审核失败';
                return JsonServer::error($error);
            }

            return JsonServer::success('审核成功');
        }

        return view('agent/withdrawal_examine');
    }

    /**
     * @Notes: 商家提现转账
     * @Author: 张无忌
     */
    public function withdrawalTransfer()
    {
        if ($this->request->isAjax()) {
            $post = $this->request->post();
            $res = AgentLogic::transfer($post);
            if ($res === false) {
                $error = AgentLogic::getError() ?: '审核失败';
                return JsonServer::error($error);
            }

            return JsonServer::success('审核成功');
        }

        $id = $this->request->get('id');
        View::assign('detail', AgentLogic::Agentdetail($id));
        return view('agent/withdrawal_transfer');
    }

    /**
     * @notes 在线转账
     * @return \think\response\Json|void
     * <AUTHOR>
     * @datetime 2023-06-07 09:48:22
     */
    function WithdrawalTransferOnline()
    {
        if ($this->request->isAjax()) {
            $post = $this->request->post();
            $res = AgentLogic::transfer_online($post);
            if ($res === false) {
                $error = AgentLogic::getError() ? : '在线转账失败';
                return JsonServer::error($error);
            }

            return JsonServer::success(AgentLogic::getError() ? : '在线转账成功');
        }
    }

    /**
     * @notes 代理保证金管理
     * @return \think\response\Json|\think\response\View
     * <AUTHOR> @date
     */
    public function deposit()
    {
        if ($this->request->isPost()) {
            $params = $this->request->post();
            $result = AgentLogic::depositList($params);
            return JsonServer::success('', $result);
        }
        return view('agent/deposit');
    }

    /**
     * @notes 代理保证金退款
     * @return \think\response\Json
     * <AUTHOR> @date
     */
    public function refundDeposit()
    {
        if ($this->request->isPost()) {
            $id = $this->request->post('id');
            if (empty($id)) {
                return JsonServer::error('参数错误');
            }

            $result = AgentLogic::refundDeposit($id, $this->adminId);
            if ($result === false) {
                return JsonServer::error(AgentLogic::getError() ?: '退款失败');
            }
            return JsonServer::success('退款成功');
        }
        return JsonServer::error('请求方式错误');
    }

    /**
     * @notes 保证金退款审核页面
     * @return \think\response\View
     * <AUTHOR> @date
     */
    public function auditRefund()
    {
        $id = $this->request->get('id/d');
        if (empty($id)) {
            return JsonServer::error('参数错误');
        }

        // 获取保证金记录
        $deposit = AgentMerchantfees::where('id', $id)->find();
        if (!$deposit) {
            return JsonServer::error('保证金记录不存在');
        }

        // 检查状态是否为待审核
        if ($deposit['status'] != 7) {
            return JsonServer::error('只有待审核状态的保证金才能进行审核操作');
        }

        // 获取用户信息
        $user = \app\common\model\user\User::where('id', $deposit['user_id'])->find();
        if ($user) {
            $deposit['nickname'] = $user['nickname'];
            $deposit['mobile'] = $user['mobile'];
        }

        View::assign('deposit', $deposit);
         return view('agent/audit_refund');
    }

    /**
     * @notes 处理保证金退款审核
     * @return \think\response\Json
     * <AUTHOR> @date
     */
    public function processAudit()
    {
        if ($this->request->isPost()) {
            $post = $this->request->post();

            // 验证参数
            if (empty($post['id']) || empty($post['action'])) {
                return JsonServer::error('参数错误');
            }

            $result = AgentLogic::processRefundAudit($post['id'], $post['action'], $post['opinion'] ?? '', $this->adminId);
            if ($result === false) {
                return JsonServer::error(AgentLogic::getError() ?: '审核失败');
            }

            $action_text = $post['action'] == 'approve' ? '通过' : '拒绝';
            return JsonServer::success('审核' . $action_text . '成功');
        }
        return JsonServer::error('请求方式错误');
    }

    /**
     * @notes 代理保证金手动退款页面（银行卡/支付宝）
     * @return \think\response\View
     * <AUTHOR> @date
     */
    public function manualRefund()
    {
        if ($this->request->isPost()) {
            $post = $this->request->post();

            // 验证参数
            if (empty($post['id']) || empty($post['refund_id']) ||
                empty($post['payment_type']) || empty($post['transfer_voucher'])) {
                return JsonServer::error('参数错误');
            }

            // 根据支付方式验证参数
            if ($post['payment_type'] == 1 && empty($post['bank_id'])) {
                return JsonServer::error('请选择银行卡');
            }

            if ($post['payment_type'] == 2 && empty($post['alipay_id'])) {
                return JsonServer::error('请选择支付宝账户');
            }

            // 添加管理员ID
            $post['admin_id'] = $this->adminId;

            // 调用逻辑层处理手动退款
            $result = AgentLogic::manualRefund($post);
            if ($result === true) {
                return JsonServer::success('手动退款操作成功');
            } else {
                return JsonServer::error(AgentLogic::getError() ?: '手动退款失败');
            }
        }

        $id = $this->request->get('id/d');
        if (empty($id)) {
            return JsonServer::error('参数错误');
        }

        // 获取退款记录
        $refund = Db::name('agent_deposit_refund')
            ->where('id', $id)
            ->find();

        if (!$refund) {
            return JsonServer::error('退款记录不存在');
        }

        // 检查状态是否为待打款
        if ($refund['status'] != 2) {
            return JsonServer::error('只有待打款状态的退款才能进行手动退款操作');
        }

        // 获取用户信息
        $user = \app\common\model\user\User::where('id', $refund['user_id'])->find();
        if ($user) {
            $refund['nickname'] = $user['nickname'];
            $refund['mobile'] = $user['mobile'];
        }

        // 获取用户银行卡和支付宝信息
        $bank_cards = AgentBank::where('user_id', $refund['user_id'])->select();
        $alipay_accounts = AgentAlipay::where('user_id', $refund['user_id'])->select();
        View::assign('refund', $refund);
        View::assign('bank_cards', $bank_cards);
        View::assign('alipay_accounts', $alipay_accounts);

        return view('agent/manual_refund');
    }

    /**
     * @notes 保证金明细
     * @return \think\response\Json|\think\response\View
     * <AUTHOR> @date
     */
    public function depositDetails()
    {
        if ($this->request->isPost()) {
            $params = $this->request->post();
            $result = AgentDepositLogic::getDepositDetails($params);
            return JsonServer::success('', $result);
        }
        $user_id = $this->request->get('user_id/d');
        if (empty($user_id)) {
            return JsonServer::error('参数错误');
        }
        // 获取用户信息
        $user = \app\common\model\user\User::where('id', $user_id)->find();
        if (!$user) {
            return JsonServer::error('用户不存在');
        }
        View::assign('user_id', $user_id);
        View::assign('user', $user);

        return view('agent/deposit_details');
    }

    /**
     * @notes 调整保证金
     * @return \think\response\Json|\think\response\View
     * <AUTHOR> @date
     */
    public function adjustDeposit()
    {
        if ($this->request->isPost()) {
            $post = $this->request->post();

            // 验证参数
            if (empty($post['user_id']) || !isset($post['adjust_type']) || !isset($post['amount'])) {
                return JsonServer::error('参数错误');
            }

            if (!is_numeric($post['amount']) || $post['amount'] <= 0) {
                return JsonServer::error('调整金额必须为正数');
            }
            // 添加管理员ID
            $post['admin_id'] = $this->adminId;
            $result = AgentDepositLogic::adjustDeposit($post);
            if ($result) {
                return JsonServer::success('保证金调整成功');
            } else {
                return JsonServer::error(AgentDepositLogic::getError() ?: '保证金调整失败');
            }
        }
        $user_id = $this->request->get('user_id/d');
        $is_popup = $this->request->get('is_popup/d', 0); // 判断是否为弹窗
        if (empty($user_id)) {
            return JsonServer::error('参数错误');
        }
        // 获取用户信息
        $user = \app\common\model\user\User::where('id', $user_id)->find();
        if (!$user) {
            return JsonServer::error('用户不存在');
        }
       
        View::assign('user_id', $user_id);
        View::assign('user', $user);
        View::assign('is_popup', $is_popup);
        if ($is_popup) {
            return view('agent/adjust_deposit_popup');
        } else {
            return view('agent/adjust_deposit');
        }
    }

    /**
     * 保存代理顾问信息
     * @return \think\response\Json
     */
    public function saveDetail()
    {
        // 根据新需求，此方法不再需要保存任何信息
        return JsonServer::success('仅为展示页面');
    }
    
    /*
     * 信息资料列表
     */
    public function infoList()
    {
        if ($this->request->isPost()) {
            $params = $this->request->post();
            $page = $params['page'] ?? $this->page_no;
            $limit = $params['limit'] ?? $this->page_size;

            $query = Db::name('agent_consultant')->alias('ic')
                ->join('user u', 'ic.uid = u.id', 'LEFT')
                ->field('ic.*, u.nickname, u.avatar, u.sn as user_sn');

            if (!empty($params['name'])) {
                $query->where('ic.name', 'like', '%' . $params['name'] . '%');
            }
            if (!empty($params['mobile'])) {
                $query->where('ic.mobile', 'like', '%' . $params['mobile'] . '%');
            }

            $count = $query->count();
            $lists = $query->page($page, $limit)->order('ic.id', 'desc')->select()->toArray();

            return JsonServer::success('', [
                'count' => $count,
                'lists' => $lists,
            ]);
        }
        return view('agent/info_list');
    }
}
