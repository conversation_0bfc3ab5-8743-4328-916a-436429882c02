/*
 Navicat Premium Dump SQL

 Source Server         : B2B商城
 Source Server Type    : MySQL
 Source Server Version : 50744 (5.7.44-log)
 Source Host           : *************:3306
 Source Schema         : kshop

 Target Server Type    : MySQL
 Target Server Version : 50744 (5.7.44-log)
 File Encoding         : 65001

 Date: 24/06/2025 09:15:11
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for ls_account_log
-- ----------------------------
DROP TABLE IF EXISTS `ls_account_log`;
CREATE TABLE `ls_account_log`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `log_sn` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '流水单号（20位）',
  `user_id` int(11) NOT NULL COMMENT '用户id',
  `source_type` smallint(5) UNSIGNED NOT NULL DEFAULT 100 COMMENT '来源类型',
  `source_id` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '来源id',
  `source_sn` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '来源单号',
  `change_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '变动总数',
  `left_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '剩余总数 (UNSIGNED removed as it can be negative)',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '说明',
  `extra` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '额外的字段说明',
  `change_type` tinyint(1) UNSIGNED NULL DEFAULT 0 COMMENT '1-增加；2-减少',
  `create_time` int(10) UNSIGNED NOT NULL COMMENT '变动时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_source_type_id`(`source_type`, `source_id`) USING BTREE,
  INDEX `idx_log_sn`(`log_sn`) USING BTREE,
  INDEX `idx_create_time`(`create_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 41 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '账户流水变动表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_activity_area
-- ----------------------------
DROP TABLE IF EXISTS `ls_activity_area`;
CREATE TABLE `ls_activity_area`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '专区名称',
  `synopsis` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '专区简介',
  `image` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '专区活动',
  `status` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '专区状态：1-开启；0-关闭',
  `create_time` int(10) UNSIGNED NOT NULL COMMENT '创建时间',
  `update_time` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '更新时间',
  `del` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除：1-是；0-否',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_status_del`(`status`, `del`) USING BTREE,
  INDEX `idx_create_time`(`create_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '活动专区' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_activity_area_goods
-- ----------------------------
DROP TABLE IF EXISTS `ls_activity_area_goods`;
CREATE TABLE `ls_activity_area_goods`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `activity_area_id` int(11) NOT NULL COMMENT '专区id',
  `shop_id` int(11) NOT NULL COMMENT '商户id',
  `goods_id` int(11) NOT NULL COMMENT '商品id',
  `item_id` int(11) NOT NULL COMMENT '规格id',
  `audit_status` tinyint(1) UNSIGNED NOT NULL COMMENT '审核状态：0-待审核；1-审核通过；2-审核拒绝',
  `status` tinyint(1) UNSIGNED NOT NULL COMMENT '状态：1-显示；0-隐藏',
  `create_time` int(10) UNSIGNED NOT NULL COMMENT '创建时间',
  `update_time` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '更新时间',
  `del` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除：1-是；0-否',
  `audit_remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '审核备注',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_activity_area_id`(`activity_area_id`) USING BTREE,
  INDEX `idx_goods_id`(`goods_id`) USING BTREE,
  INDEX `idx_item_id`(`item_id`) USING BTREE,
  INDEX `idx_shop_id`(`shop_id`) USING BTREE,
  INDEX `idx_audit_status`(`audit_status`) USING BTREE,
  INDEX `idx_status_del`(`status`, `del`) USING BTREE,
  INDEX `idx_create_time`(`create_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '活动专区商品表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_ad
-- ----------------------------
DROP TABLE IF EXISTS `ls_ad`;
CREATE TABLE `ls_ad`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `pid` int(11) NOT NULL,
  `title` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '广告标题',
  `terminal` tinyint(1) UNSIGNED NOT NULL DEFAULT 1 COMMENT '终端,1-移动端商城；2-PC端商城',
  `remark` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '文本说明',
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '图片地址',
  `link_type` tinyint(1) UNSIGNED NULL DEFAULT 0 COMMENT '链接类型：0-为空；1-商城页面；2-商品页面；3-自定义类型',
  `ad_fee` decimal(8, 2) NOT NULL DEFAULT 0.00 COMMENT '广告位费用',
  `shop_id` int(11) NOT NULL DEFAULT 0 COMMENT '商铺ID,租赁时用',
  `link` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '链接地址：ad_type为1时，保存商品页面路径id；ad_type为2时，保存商品id；ad_type为3时，保存自定义链接',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '广告描述',
  `category_id` int(10) NULL DEFAULT NULL COMMENT '商品分类（pc端广告）',
  `status` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '广告状态：1-开启；0-关闭',
  `start_time` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '投放时间',
  `end_time` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '结束时间',
  `sort` int(10) UNSIGNED NULL DEFAULT 50 COMMENT '排序',
  `create_time` int(10) UNSIGNED NOT NULL COMMENT '创建时间',
  `update_time` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '更新时间',
  `del` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除：1-是；0-否',
  `ad_order_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '广告订单ID',
  `add_field` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '附加字段',
  `bj_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '图片地址',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_pid`(`pid`) USING BTREE,
  INDEX `idx_terminal_status_del`(`terminal`, `status`, `del`) USING BTREE,
  INDEX `idx_shop_id`(`shop_id`) USING BTREE,
  INDEX `idx_link_type`(`link_type`) USING BTREE,
  INDEX `idx_category_id`(`category_id`) USING BTREE,
  INDEX `idx_ad_order_id`(`ad_order_id`) USING BTREE,
  INDEX `idx_start_end_time`(`start_time`, `end_time`) USING BTREE,
  INDEX `idx_sort`(`sort`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 161 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '广告表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_ad_order
-- ----------------------------
DROP TABLE IF EXISTS `ls_ad_order`;
CREATE TABLE `ls_ad_order`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_sn` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '订单编号',
  `ad_position_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '广告位名称',
  `user_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '购买用户id',
  `ad_buynums` int(10) NOT NULL DEFAULT 1 COMMENT '广告数量',
  `shop_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '商家id',
  `status` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '状态：0-未付款；1-已付款',
  `buy_time` int(10) UNSIGNED NOT NULL COMMENT '购买时长',
  `start_time` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '投放时间',
  `end_time` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '结束时间',
  `sort` int(10) UNSIGNED NOT NULL COMMENT '购买排序升序',
  `create_time` int(10) UNSIGNED NOT NULL COMMENT '创建时间',
  `update_time` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '更新时间',
  `ad_price` decimal(8, 2) NOT NULL DEFAULT 0.00 COMMENT '广告位费',
  `del` tinyint(1) UNSIGNED NOT NULL COMMENT '删除状态：1-是；0-否',
  `ad_sn` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '广告区域位置编码',
  `ad_yn` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '广告块位置编码',
  `wechat_mini_express_sync` tinyint(1) NULL DEFAULT 0 COMMENT '微信小程序发货信息录入：0-未录入；1-已录入；2-录入失败',
  `wechat_mini_express_sync_time` int(11) NULL DEFAULT 0 COMMENT '微信发货录入时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_order_sn`(`order_sn`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_shop_id`(`shop_id`) USING BTREE,
  INDEX `idx_status_del`(`status`, `del`) USING BTREE,
  INDEX `idx_ad_position_id`(`ad_position_id`) USING BTREE,
  INDEX `idx_start_end_time`(`start_time`, `end_time`) USING BTREE,
  INDEX `idx_create_time`(`create_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 217 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '广告位购买记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_ad_position
-- ----------------------------
DROP TABLE IF EXISTS `ls_ad_position`;
CREATE TABLE `ls_ad_position`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `terminal` tinyint(1) UNSIGNED NOT NULL DEFAULT 1 COMMENT '终端：1-移动端；2-PC端',
  `is_sys` tinyint(1) UNSIGNED NOT NULL COMMENT '0-广告位1-系统配置',
  `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '广告位名称',
  `attr` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '广告位属性：0-系统默认；1-自定义',
  `width` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '广告位建议宽度',
  `height` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '广告位建议高度',
  `ad_fee` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '广告费用',
  `status` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '状态：0-停用；1-显示',
  `create_time` int(10) UNSIGNED NOT NULL COMMENT '创建时间',
  `image` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '广告示意图',
  `billing_cycle` tinyint(1) UNSIGNED NULL DEFAULT 0 COMMENT '计费周期0天1周2月3年',
  `lun_time` tinyint(1) UNSIGNED NULL DEFAULT 3 COMMENT '轮播时长单位秒',
  `is_buy` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否出售0:不出售 1出售',
  `update_time` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '更新时间',
  `del` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '删除状态：1-是；0-否',
  `ad_sn` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '广告区域位置编码',
  `ad_buys` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '已卖数量',
  `ad_nums` int(10) UNSIGNED NOT NULL DEFAULT 1 COMMENT '广告位数量',
  `ad_yn` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '广告块位置编码',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_terminal_status_del`(`terminal`, `status`, `del`) USING BTREE,
  INDEX `idx_is_buy`(`is_buy`) USING BTREE,
  INDEX `idx_create_time`(`create_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 81 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '广告位表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_admin
-- ----------------------------
DROP TABLE IF EXISTS `ls_admin`;
CREATE TABLE `ls_admin`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `root` tinyint(1) UNSIGNED NULL DEFAULT 1 COMMENT '0-非超级管理员；1-超级管理；',
  `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '名称',
  `type` tinyint(3) UNSIGNED NULL DEFAULT 0 COMMENT '账号类型：0-默认管理后台；其他根据业务再定',
  `account` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '账号',
  `password` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '密码',
  `salt` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '密码盐',
  `role_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '角色id',
  `create_time` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '修改时间',
  `login_time` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '最后登录时间',
  `login_ip` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '最后登录ip',
  `disable` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否禁用：0-否；1-是；',
  `deleted_at` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '删除时间戳',
  `del` tinyint(10) NOT NULL DEFAULT 0 COMMENT '0为非删除状态，非0位删除时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `account`(`account`) USING BTREE,
  INDEX `idx_role_id`(`role_id`) USING BTREE,
  INDEX `idx_deleted_at`(`deleted_at`) USING BTREE,
  INDEX `idx_disable`(`disable`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '平台管理员表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_after_sale
-- ----------------------------
DROP TABLE IF EXISTS `ls_after_sale`;
CREATE TABLE `ls_after_sale`  (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `sn` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '退款单号',
  `user_id` int(10) UNSIGNED NOT NULL COMMENT '用户id',
  `order_id` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '订单id',
  `order_goods_id` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '订单商品关联表id',
  `goods_id` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '商品id',
  `item_id` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '规格id',
  `goods_num` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '商品数量',
  `refund_reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '退款原因',
  `refund_remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '退款说明',
  `refund_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '退款图片',
  `refund_type` tinyint(1) UNSIGNED NULL DEFAULT NULL COMMENT '退款类型;0-仅退款;1-退款退货',
  `refund_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '退款金额',
  `refund_way` tinyint(1) UNSIGNED NULL DEFAULT 1 COMMENT '退款方式：1-原路退回；2-退回余额；',
  `express_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '快递公司名称',
  `invoice_no` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '快递单号',
  `express_remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '物流备注说明',
  `express_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '物流凭证',
  `confirm_take_time` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '确认收货时间',
  `status` tinyint(1) UNSIGNED NULL DEFAULT 0 COMMENT '售后状态;0-申请退款;1-商家拒绝;2-商品待退货;3-商家待收货;4-商家拒收货;5-等待退款;6-退款成功',
  `audit_time` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '审核时间',
  `admin_id` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '门店管理员id',
  `admin_remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '售后说明',
  `create_time` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '更新时间',
  `del` tinyint(1) UNSIGNED NULL DEFAULT 0 COMMENT '撤销状态;0-正常;1-已撤销',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_order_id`(`order_id`) USING BTREE,
  INDEX `idx_order_goods_id`(`order_goods_id`) USING BTREE,
  INDEX `idx_goods_id`(`goods_id`) USING BTREE,
  INDEX `idx_item_id`(`item_id`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_sn`(`sn`) USING BTREE,
  INDEX `idx_create_time`(`create_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 12 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '售后退款表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_after_sale_log
-- ----------------------------
DROP TABLE IF EXISTS `ls_after_sale_log`;
CREATE TABLE `ls_after_sale_log`  (
  `id` int(10) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `type` tinyint(1) UNSIGNED NULL DEFAULT 0 COMMENT '操作类型;0-会员;1-门店',
  `channel` smallint(5) UNSIGNED NULL DEFAULT 0 COMMENT '渠道编号。变动方式。',
  `order_id` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '订单id',
  `after_sale_id` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '售后订单id',
  `handle_id` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '操作人id',
  `content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '日志内容',
  `create_time` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_order_id`(`order_id`) USING BTREE,
  INDEX `idx_after_sale_id`(`after_sale_id`) USING BTREE,
  INDEX `idx_handle_id`(`handle_id`) USING BTREE,
  INDEX `idx_create_time`(`create_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 53 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '售后日志表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_agent
-- ----------------------------
DROP TABLE IF EXISTS `ls_agent`;
CREATE TABLE `ls_agent`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `user_id` int(10) UNSIGNED NOT NULL COMMENT '用户id',
  `area_id` int(10) UNSIGNED NOT NULL COMMENT '代理区域ID',
  `is_type` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否区域代理 0-否 1-是',
  `is_freeze` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否冻结 0-否 1-是',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  `level` tinyint(1) UNSIGNED NOT NULL DEFAULT 1 COMMENT '代理级别（1为一级代理，2为二级代理）',
  `parent_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '邀请人ID,只有二级代理有',
  `distribution_start_time` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '成为代理时间',
  `distribution_end_time` int(10) UNSIGNED NOT NULL COMMENT '代理失效时间',
  `mnp_qr_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '小程序分享二维码',
  `h5_qr_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT 'h5分享二维码',
  `app_qr_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT 'app分享二维码',
  `agent_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '代理唯一编码',
  `create_time` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '更新时间',
  `delete_time` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '删除时间',
  `buy_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '代理成为方式,系统设置,线上购买',
  `freeze_time` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '代理冻结时间',
  `del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除,0:正常1:删除',
  `hetong_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '合同协议地址',
  `open_time` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '代理恢复时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_area_id`(`area_id`) USING BTREE,
  INDEX `idx_is_type`(`is_type`) USING BTREE,
  INDEX `idx_is_freeze`(`is_freeze`) USING BTREE,
  INDEX `idx_level`(`level`) USING BTREE,
  INDEX `idx_parent_id`(`parent_id`) USING BTREE,
  INDEX `idx_agent_code`(`agent_code`) USING BTREE,
  INDEX `idx_distribution_start_end_time`(`distribution_start_time`, `distribution_end_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 54 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '代理人信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_agent_alipay
-- ----------------------------
DROP TABLE IF EXISTS `ls_agent_alipay`;
CREATE TABLE `ls_agent_alipay`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `agent_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '代理ID',
  `account` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '支付宝账号',
  `username` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '姓名',
  `del` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除[0=否, 1=是]',
  `create_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_agent_id`(`agent_id`) USING BTREE,
  INDEX `idx_del`(`del`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 12 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '代理支付宝账号' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_agent_bank
-- ----------------------------
DROP TABLE IF EXISTS `ls_agent_bank`;
CREATE TABLE `ls_agent_bank`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `agent_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '代理ID',
  `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '提现银行',
  `branch` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '银行开户行',
  `mobile` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '预留手机号',
  `nickname` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '开户名称',
  `account` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '银行卡账号',
  `del` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除[0=否, 1=是]',
  `create_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_agent_id`(`agent_id`) USING BTREE,
  INDEX `idx_del`(`del`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 21 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '代理银行账号表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_agent_deposit_details
-- ----------------------------
DROP TABLE IF EXISTS `ls_agent_deposit_details`;
CREATE TABLE `ls_agent_deposit_details`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '记录的唯一标识符',
  `user_id` int(11) NOT NULL COMMENT '代理用户ID',
  `deposit_id` int(11) NOT NULL COMMENT '代理保证金表ID',
  `sn` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '明细单号',
  `pay_status` int(1) NULL DEFAULT 0 COMMENT '支付状态0:未支付 1:已支付 ',
  `pay_time` int(11) NULL DEFAULT NULL COMMENT '支付时间',
  `transaction_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '交易订单ID',
  `deposit_change` decimal(10, 2) NOT NULL COMMENT '保证金变动金额（正数表示增加，负数表示扣除）',
  `change_type` tinyint(4) NOT NULL COMMENT '变动类型：1-线上缴纳 2-人工增加 3-人工扣除 4-人工退还',
  `amount` decimal(10, 2) NOT NULL COMMENT '变动金额的绝对值',
  `reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '保证金变动原因',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `admin_id` int(11) NULL DEFAULT NULL COMMENT '操作管理员ID',
  `change_date` date NOT NULL COMMENT '变动日期',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录最后更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_deposit_id`(`deposit_id`) USING BTREE,
  INDEX `idx_change_type`(`change_type`) USING BTREE,
  INDEX `idx_change_date`(`change_date`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '代理保证金明细表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_agent_deposit_log
-- ----------------------------
DROP TABLE IF EXISTS `ls_agent_deposit_log`;
CREATE TABLE `ls_agent_deposit_log`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `deposit_id` int(11) NOT NULL COMMENT '保证金ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `admin_id` int(11) NOT NULL DEFAULT 0 COMMENT '管理员ID',
  `action` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '操作类型',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '备注',
  `create_time` int(11) NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_deposit_id`(`deposit_id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_admin_id`(`admin_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '代理保证金操作日志表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ls_agent_merchantfees
-- ----------------------------
DROP TABLE IF EXISTS `ls_agent_merchantfees`;
CREATE TABLE `ls_agent_merchantfees`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(10) UNSIGNED NOT NULL COMMENT '缴费人id',
  `order_sn` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '订单编码',
  `amount` decimal(10, 2) NOT NULL COMMENT '缴纳保证金金额',
  `payment_date` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '付款时间(即为成为代理时间,所有收益从此时开始算)',
  `transaction_id` varchar(35) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '交易号',
  `old_money` decimal(10, 2) NOT NULL COMMENT '当时缴纳保证金额度',
  `refund_request_time` int(11) NOT NULL DEFAULT 0 COMMENT '退款申请时间戳',
  `refund_publicity_end_time` int(11) NULL DEFAULT 0 COMMENT '申请后公示期结束时间戳',
  `publicity_period_end_time` int(11) NOT NULL DEFAULT 0 COMMENT '公示期结束时间戳',
  `status` tinyint(1) UNSIGNED NOT NULL COMMENT '0未支付1已支付2退款中3已退款',
  `refund_time` int(11) NULL DEFAULT 0 COMMENT '退款时间',
  `qian_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `wechat_mini_express_sync` tinyint(1) NULL DEFAULT 0 COMMENT '微信小程序发货信息录入：0-未录入；1-已录入；2-录入失败',
  `document_path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '退款协议路径',
  `wechat_mini_express_sync_time` int(11) NULL DEFAULT 0 COMMENT '微信发货录入时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_order_sn`(`order_sn`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_payment_date`(`payment_date`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 184 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '代理保证金订单表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_agent_order
-- ----------------------------
DROP TABLE IF EXISTS `ls_agent_order`;
CREATE TABLE `ls_agent_order`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `sn` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '记录编号',
  `p_user_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '付款人父级id',
  `g_user_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '付款人id',
  `user_id` int(10) UNSIGNED NOT NULL COMMENT '用户id',
  `real_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '真实姓名',
  `level` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '代理等级',
  `ratio` decimal(10, 2) NOT NULL COMMENT '佣金比例',
  `commissions_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '服务购买记录表ID',
  `money` decimal(10, 2) UNSIGNED NOT NULL COMMENT '佣金',
  `status` tinyint(1) UNSIGNED NOT NULL DEFAULT 1 COMMENT '状态：1-待返佣；2-已结算；3-冻结；4-已退款',
  `order_type` tinyint(1) UNSIGNED NOT NULL DEFAULT 1 COMMENT '状态：1-用户会员购买；2-商家入驻费；3-商家检验费；4-入驻及检验费',
  `create_time` int(10) UNSIGNED NOT NULL COMMENT '创建时间',
  `update_time` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '更新时间',
  `shop_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '店铺id',
  `settlement_time` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '结算时间',
  `pt_get` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '平台应得',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '冻结原因',
  `order_total` decimal(10, 2) UNSIGNED NOT NULL COMMENT '订单总额',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_p_user_id`(`p_user_id`) USING BTREE,
  INDEX `idx_g_user_id`(`g_user_id`) USING BTREE,
  INDEX `idx_commissions_id`(`commissions_id`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_order_type`(`order_type`) USING BTREE,
  INDEX `idx_shop_id`(`shop_id`) USING BTREE,
  INDEX `idx_sn`(`sn`) USING BTREE,
  INDEX `idx_create_time`(`create_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 28 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '代理服务佣金记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_agent_relationships
-- ----------------------------
DROP TABLE IF EXISTS `ls_agent_relationships`;
CREATE TABLE `ls_agent_relationships`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(10) UNSIGNED NOT NULL COMMENT '受邀用户ID',
  `agent_id` int(10) UNSIGNED NOT NULL COMMENT '代理表ID',
  `sponsor_id` int(10) UNSIGNED NOT NULL COMMENT '邀请人ID（即上级代理ID）0-为平台',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_agent_id`(`agent_id`) USING BTREE,
  INDEX `idx_sponsor_id`(`sponsor_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 762 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '代理关系表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_agent_revoke_records
-- ----------------------------
DROP TABLE IF EXISTS `ls_agent_revoke_records`;
CREATE TABLE `ls_agent_revoke_records`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(10) UNSIGNED NOT NULL COMMENT '受邀用户ID',
  `agent_id` int(10) UNSIGNED NOT NULL COMMENT '被撤销的代理ID',
  `sponsor_id` int(10) UNSIGNED NOT NULL COMMENT '邀请人ID（即上级代理ID）0-为平台',
  `revoke_reason` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '撤销原因',
  `revoke_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '撤销时间',
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_agent_id`(`agent_id`) USING BTREE,
  INDEX `idx_sponsor_id`(`sponsor_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '代理撤销记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_agent_settings
-- ----------------------------
DROP TABLE IF EXISTS `ls_agent_settings`;
CREATE TABLE `ls_agent_settings`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `platform_ratio` decimal(5, 2) UNSIGNED NOT NULL COMMENT '平台抽成比例',
  `level1_ratio` decimal(5, 2) UNSIGNED NOT NULL COMMENT '一级代理抽成比例',
  `level2_ratio` decimal(5, 2) UNSIGNED NOT NULL COMMENT '二级代理抽成比例',
  `min_withdrawal_amount` decimal(10, 2) NOT NULL COMMENT '最小提现金额',
  `order_payment_days` int(10) UNSIGNED NOT NULL COMMENT '佣金停放时长',
  `transfer_arrival_time` time NOT NULL COMMENT '提现到账时间（预估）',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '代理设置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_agent_settlement
-- ----------------------------
DROP TABLE IF EXISTS `ls_agent_settlement`;
CREATE TABLE `ls_agent_settlement`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `agent_order_id` int(10) UNSIGNED NOT NULL COMMENT '结算订单ID',
  `cn_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '结算方式:手动结算 自动结算',
  `cn_price` decimal(10, 2) NOT NULL COMMENT '结算佣金金额',
  `settlement_date` datetime NOT NULL COMMENT '结算时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_agent_order_id`(`agent_order_id`) USING BTREE,
  INDEX `idx_settlement_date`(`settlement_date`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 10 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_agent_withdrawal
-- ----------------------------
DROP TABLE IF EXISTS `ls_agent_withdrawal`;
CREATE TABLE `ls_agent_withdrawal`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
  `sn` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '提现单号',
  `user_id` int(10) UNSIGNED NOT NULL,
  `agent_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '代理ID',
  `type` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '提现类型 0银行卡 10支付宝',
  `bank_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '银行卡账号ID',
  `alipay_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '支付宝账号id',
  `apply_amount` decimal(8, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '申请提现金额',
  `left_amount` decimal(8, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '实际到账金额(扣除手续费)',
  `poundage_amount` decimal(8, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '手续费金额',
  `poundage_ratio` decimal(5, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '手续费比例',
  `explain` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '提现说明',
  `status` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '提现状态[0=申请中, 1=处理中, 2=转账成功, 3=转账失败]',
  `transfer_voucher` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '转账凭证(图)',
  `transfer_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '转账时间',
  `transfer_content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '转账说明',
  `create_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_agent_id`(`agent_id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_type`(`type`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_sn`(`sn`) USING BTREE,
  INDEX `idx_create_time`(`create_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '代理提现表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_article
-- ----------------------------
DROP TABLE IF EXISTS `ls_article`;
CREATE TABLE `ls_article`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
  `cid` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '分类ID',
  `shop_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '店铺ID',
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '文章标题',
  `image` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '封面图',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '文章内容',
  `visit` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '浏览数',
  `wechatUrl` varchar(350) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '公众号链接',
  `likes` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '点赞数',
  `sort` smallint(5) UNSIGNED NOT NULL DEFAULT 0 COMMENT '排序',
  `is_notice` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否公共[0=否，1=是]',
  `is_show` tinyint(1) UNSIGNED NOT NULL DEFAULT 1 COMMENT '是否显示[0=否，1=是]',
  `del` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除[0=否，1=是]',
  `create_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  `intro` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '文章简介',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_cid`(`cid`) USING BTREE,
  INDEX `idx_shop_id`(`shop_id`) USING BTREE,
  INDEX `idx_is_show_del`(`is_show`, `del`) USING BTREE,
  INDEX `idx_is_notice`(`is_notice`) USING BTREE,
  INDEX `idx_create_time`(`create_time`) USING BTREE,
  INDEX `idx_sort`(`sort`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 21 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '文章表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_article_category
-- ----------------------------
DROP TABLE IF EXISTS `ls_article_category`;
CREATE TABLE `ls_article_category`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '分类名称',
  `is_show` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否显示[0=否，1=是]',
  `del` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除[0=否， 1=是]',
  `create_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_is_show_del`(`is_show`, `del`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '文章分类表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_bank
-- ----------------------------
DROP TABLE IF EXISTS `ls_bank`;
CREATE TABLE `ls_bank`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `bank_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '银行名称',
  `bank_code` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '银行标识码',
  `bank_logo` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '银行logo',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `bank_name`(`bank_name`) USING BTREE,
  INDEX `idx_bank_code`(`bank_code`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 112 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_bargain
-- ----------------------------
DROP TABLE IF EXISTS `ls_bargain`;
CREATE TABLE `ls_bargain`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `goods_id` int(10) UNSIGNED NOT NULL COMMENT '商品ID',
  `time_limit` decimal(5, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '限制有效时间(小时)',
  `activity_start_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '活动开始时间',
  `activity_end_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '活动结束时间',
  `bargain_min_price` decimal(8, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '砍价低价 最底价',
  `bargain_max_price` decimal(8, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '砍价低价 最高价',
  `payment_where` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '支付条件[1=砍到低价, 2=任意金额]',
  `knife_type` tinyint(1) UNSIGNED NOT NULL DEFAULT 1 COMMENT '砍价金额类型[1=随机金额,2=固定金额]',
  `knife_price` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '每到金额,如果是随机金额用逗号隔开如: 1,28',
  `share_title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '分享标题',
  `share_intro` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '分享简介',
  `status` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '活动状态[0=已结束, 1=进行中]',
  `del` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除[0=否， 1=是]',
  `shop_id` int(10) UNSIGNED NOT NULL COMMENT '商家id',
  `audit_status` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '审核状态：0-待审核；1-审核通过；2-审核拒绝',
  `audit_remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '审核备注',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_goods_id`(`goods_id`) USING BTREE,
  INDEX `idx_shop_id`(`shop_id`) USING BTREE,
  INDEX `idx_status_del`(`status`, `del`) USING BTREE,
  INDEX `idx_audit_status`(`audit_status`) USING BTREE,
  INDEX `idx_activity_time`(`activity_start_time`, `activity_end_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '砍价活动商品' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_bargain_item
-- ----------------------------
DROP TABLE IF EXISTS `ls_bargain_item`;
CREATE TABLE `ls_bargain_item`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `bargain_id` int(10) UNSIGNED NOT NULL COMMENT '砍价活动ID',
  `goods_id` int(10) UNSIGNED NOT NULL COMMENT '商品ID',
  `item_id` int(10) UNSIGNED NOT NULL COMMENT '商品规格ID',
  `floor_price` decimal(8, 2) UNSIGNED NOT NULL COMMENT '砍价活动底价',
  `first_knife_price` decimal(8, 2) UNSIGNED NOT NULL COMMENT '首刀的价格,不能高于商品价,不能低于底价',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_bargain_id`(`bargain_id`) USING BTREE,
  INDEX `idx_goods_id`(`goods_id`) USING BTREE,
  INDEX `idx_item_id`(`item_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '砍价活动商品规格表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_bargain_knife
-- ----------------------------
DROP TABLE IF EXISTS `ls_bargain_knife`;
CREATE TABLE `ls_bargain_knife`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `bargain_id` int(10) UNSIGNED NOT NULL COMMENT '砍价活动ID',
  `launch_id` int(10) UNSIGNED NOT NULL COMMENT '砍价发起ID',
  `user_id` int(10) UNSIGNED NOT NULL COMMENT '用户ID',
  `surplus_price` decimal(8, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '砍掉后剩余金额',
  `help_price` decimal(8, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '助力砍掉金额',
  `help_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '助力砍价时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `bargain_id`(`bargain_id`) USING BTREE,
  INDEX `launch_id`(`launch_id`) USING BTREE,
  INDEX `user_id`(`user_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '砍价助力表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_bargain_launch
-- ----------------------------
DROP TABLE IF EXISTS `ls_bargain_launch`;
CREATE TABLE `ls_bargain_launch`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `bargain_id` int(10) UNSIGNED NOT NULL COMMENT '砍价活动ID',
  `goods_id` int(10) UNSIGNED NOT NULL COMMENT '商品ID',
  `user_id` int(10) UNSIGNED NOT NULL COMMENT '用户ID',
  `order_id` int(10) UNSIGNED NOT NULL COMMENT '订单ID',
  `goods_snap` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '商品规格信息',
  `bargain_snap` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '砍价活动快照信息',
  `help_number` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '共助力次数(已砍了多少次)',
  `bargain_price` decimal(8, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '砍价活动价格',
  `current_price` decimal(8, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '当前砍到的价格',
  `launch_start_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '砍价发起时间',
  `launch_end_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '砍价结束时间',
  `bargain_end_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '最后砍价时间 [ 砍成功时间 / 砍失败时间]',
  `payment_limit_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '砍价成功 限制多少时间内付款( 最后砍价成功时间 + 限制时间)',
  `status` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '当前砍价状态[0=进行中，1=砍成功，2=砍失败，3=活动提前结束(保留)]',
  `shop_id` int(10) UNSIGNED NOT NULL COMMENT '商家id',
  `bargain_sn` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '砍价订单编号',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `bargain_id`(`bargain_id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_goods_id`(`goods_id`) USING BTREE,
  INDEX `idx_order_id`(`order_id`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_shop_id`(`shop_id`) USING BTREE,
  INDEX `idx_bargain_sn`(`bargain_sn`) USING BTREE,
  INDEX `idx_launch_time`(`launch_start_time`, `launch_end_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '砍价发起表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_bottom_nav
-- ----------------------------
DROP TABLE IF EXISTS `ls_bottom_nav`;
CREATE TABLE `ls_bottom_nav`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '导航名称',
  `color` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '导航颜色',
  `icon` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '图标',
  `select_color` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '选中颜色',
  `select_icon` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '选中图标',
  `create_time` int(10) UNSIGNED NOT NULL COMMENT '创建时间',
  `update_time` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '更新时间',
  `del` tinyint(1) UNSIGNED NOT NULL COMMENT '删除状态：1-是；0-否',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_del`(`del`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '底部导航' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_cache_search
-- ----------------------------
DROP TABLE IF EXISTS `ls_cache_search`;
CREATE TABLE `ls_cache_search`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `type` tinyint(1) UNSIGNED NULL DEFAULT NULL COMMENT '类型：1-增加索引；2-更新同义词',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `command` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '命令内容',
  `parameter` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '参数',
  `status` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '状态：1-运行；2-停止；3-错误；',
  `expression` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '运行规则',
  `error` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '运行失败原因',
  `create_time` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '最后执行时间',
  `last_time` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '最后执行时间',
  `time` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '实时执行时长',
  `max_time` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '最大执行时长',
  `system` tinyint(3) UNSIGNED NULL DEFAULT 0 COMMENT '是否系统任务：0-否；1-是；',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_type`(`type`) USING BTREE,
  INDEX `idx_system`(`system`) USING BTREE,
  INDEX `idx_create_time`(`create_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 22 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '索引配置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_cart
-- ----------------------------
DROP TABLE IF EXISTS `ls_cart`;
CREATE TABLE `ls_cart`  (
  `id` int(8) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '购物车表',
  `user_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户id',
  `shop_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '商家id',
  `goods_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '商品id',
  `goods_num` smallint(5) UNSIGNED NOT NULL DEFAULT 0 COMMENT '购买数量',
  `item_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '规格ID',
  `selected` tinyint(1) UNSIGNED NOT NULL DEFAULT 1 COMMENT '选中状态;1-选中;0-未选中',
  `create_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `type` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '0:普通 1:集采',
  `update_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user_id`(`user_id`) USING BTREE,
  INDEX `goods_id`(`goods_id`) USING BTREE,
  INDEX `idx_shop_id`(`shop_id`) USING BTREE,
  INDEX `idx_item_id`(`item_id`) USING BTREE,
  INDEX `idx_selected`(`selected`) USING BTREE,
  INDEX `idx_type`(`type`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 158 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '购物车' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_chat_record
-- ----------------------------
DROP TABLE IF EXISTS `ls_chat_record`;
CREATE TABLE `ls_chat_record`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `shop_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '客服的商家id，0为平台客服',
  `from_id` int(10) UNSIGNED NOT NULL COMMENT '发送人user _id',
  `from_type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '发送人类型',
  `to_id` int(10) UNSIGNED NOT NULL COMMENT '接收人user_id',
  `to_type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '接收人类型',
  `msg` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '聊天消息',
  `msg_type` tinyint(1) UNSIGNED NOT NULL DEFAULT 1 COMMENT '聊天类型,1-文本,2-图片,3-商品 4:语音消息,5:视频消息, 6:订单消息;',
  `voice_duration` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '语音时长(秒),仅语音消息有效',
  `is_read` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '已读状态,0-未读,1-已读',
  `type` tinyint(1) UNSIGNED NOT NULL DEFAULT 1 COMMENT '记录类型，1-普通记录，2-提醒记录',
  `create_time` int(10) UNSIGNED NOT NULL COMMENT '创建时间',
  `update_time` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_shop_id`(`shop_id`) USING BTREE,
  INDEX `idx_from_to`(`from_id`, `from_type`, `to_id`, `to_type`) USING BTREE,
  INDEX `idx_to_read_time`(`to_id`, `to_type`, `is_read`, `create_time`) USING BTREE,
  INDEX `idx_create_time`(`create_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 984 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '聊天记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_chat_relation
-- ----------------------------
DROP TABLE IF EXISTS `ls_chat_relation`;
CREATE TABLE `ls_chat_relation`  (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `shop_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '客服的商家id，0为平台客服',
  `user_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '发送人的uid',
  `kefu_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '送达人的uid',
  `nickname` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '用户昵称',
  `avatar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '用户头像',
  `client` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '客户端类型：[1-微信小程序,2-公众号,3-ios,4-android,5-pc,6-h5]',
  `msg` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '消息',
  `msg_type` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '消息内容',
  `is_read` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '已读状态,0-未读,1-已读',
  `create_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '添加时间',
  `update_time` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_shop_id`(`shop_id`) USING BTREE,
  INDEX `idx_user_kefu_update`(`user_id`, `kefu_id`, `update_time`) USING BTREE,
  INDEX `idx_kefu_read_update`(`kefu_id`, `is_read`, `update_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 145 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '聊天关系表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_commission_adjustment
-- ----------------------------
DROP TABLE IF EXISTS `ls_commission_adjustment`;
CREATE TABLE `ls_commission_adjustment`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `order_id` int(11) UNSIGNED NOT NULL COMMENT '订单ID',
  `order_goods_id` int(11) UNSIGNED NULL DEFAULT NULL COMMENT '订单商品ID',
  `adjustment_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '调整类型：1-退款调整，2-其他调整',
  `original_commission` decimal(8, 2) NOT NULL DEFAULT 0.00 COMMENT '原始佣金金额',
  `adjusted_commission` decimal(8, 2) NOT NULL DEFAULT 0.00 COMMENT '调整后佣金金额',
  `adjustment_amount` decimal(8, 2) NOT NULL DEFAULT 0.00 COMMENT '调整金额（正数表示退还给商家，负数表示扣除）',
  `refund_amount` decimal(8, 2) NULL DEFAULT 0.00 COMMENT '关联的退款金额',
  `reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '调整原因',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：1-待处理，2-已处理',
  `admin_id` int(11) UNSIGNED NULL DEFAULT NULL COMMENT '操作管理员ID',
  `create_time` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_order_id`(`order_id`) USING BTREE,
  INDEX `idx_order_goods_id`(`order_goods_id`) USING BTREE,
  INDEX `idx_adjustment_type`(`adjustment_type`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '佣金调整记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ls_commissions
-- ----------------------------
DROP TABLE IF EXISTS `ls_commissions`;
CREATE TABLE `ls_commissions`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '购买人ID',
  `commission_amount` decimal(10, 2) NOT NULL COMMENT '佣金金额(按比例计算后的金额)',
  `commission_type` tinyint(1) UNSIGNED NOT NULL COMMENT '服务类型:0-集采购会员 1-商家入住费2-商家评级费',
  `earned_at` datetime NOT NULL COMMENT '服务支付时间',
  `paid_at` datetime NULL DEFAULT NULL COMMENT '佣金到账时间(根据当前代理配置后推)',
  `status` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '佣金状态0-待结算(未到账)1-已结算',
  `order_amount` decimal(10, 2) NOT NULL COMMENT '服务总金额',
  `agent_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否通过代理购买,0-自主购买,代理推荐购买',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user_id`(`user_id`) USING BTREE,
  INDEX `idx_agent_id`(`agent_id`) USING BTREE,
  INDEX `idx_commission_type`(`commission_type`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_earned_at`(`earned_at`) USING BTREE,
  INDEX `idx_paid_at`(`paid_at`) USING BTREE,
  CONSTRAINT `fk_commissions_user` FOREIGN KEY (`user_id`) REFERENCES `ls_user` (`id`) ON DELETE CASCADE ON UPDATE NO ACTION
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '服务购买及佣金计算记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_common_refund
-- ----------------------------
DROP TABLE IF EXISTS `ls_common_refund`;
CREATE TABLE `ls_common_refund`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '退款记录ID',
  `refund_sn` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '退款单号',
  `refund_type` tinyint(1) NOT NULL COMMENT '退款类型：1-商家保证金 2-广告费 3-会员费 4-代理保证金 5-检验费',
  `source_id` int(11) NOT NULL COMMENT '关联的原始记录ID',
  `shop_id` int(11) NULL DEFAULT NULL COMMENT '商家ID',
  `user_id` int(11) NULL DEFAULT NULL COMMENT '用户ID',
  `agent_id` int(11) NULL DEFAULT NULL COMMENT '代理ID',
  `refund_amount` decimal(10, 2) NOT NULL COMMENT '退款金额',
  `total_amount` decimal(10, 2) NOT NULL COMMENT '原始总金额',
  `payment_method` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '原支付方式',
  `transaction_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '原支付交易号',
  `refund_status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '退款状态：0-退款中 1-退款成功 2-退款失败',
  `refund_msg` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '退款结果信息',
  `transfer_voucher` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '转账凭证图片路径',
  `manual_refund_type` tinyint(1) NULL DEFAULT NULL COMMENT '手动退款类型：1-银行卡 2-支付宝',
  `bank_id` int(11) NULL DEFAULT NULL COMMENT '关联的银行卡ID',
  `alipay_id` int(11) NULL DEFAULT NULL COMMENT '关联的支付宝账户ID',
  `manual_refund_time` datetime NULL DEFAULT NULL COMMENT '手动退款时间',
  `wechat_refund_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '微信退款单号',
  `admin_id` int(11) NULL DEFAULT NULL COMMENT '操作管理员ID',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_refund_sn`(`refund_sn`) USING BTREE,
  INDEX `idx_refund_type_source`(`refund_type`, `source_id`) USING BTREE,
  INDEX `idx_shop_id`(`shop_id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_agent_id`(`agent_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 41 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '通用退款记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ls_community_article
-- ----------------------------
DROP TABLE IF EXISTS `ls_community_article`;
CREATE TABLE `ls_community_article`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `user_id` int(8) UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户id',
  `cate_id` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '分类id',
  `topic_id` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '话题id',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '内容',
  `goods` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '相关商品id',
  `shop` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '相关店铺id',
  `videos` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '视频',
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '首图',
  `like` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '点赞数量',
  `comment` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '评论数量',
  `status` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '审核状态 0-待审核 1-审核通过 2-审核拒绝',
  `audit_remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '审核备注',
  `audit_time` int(11) UNSIGNED NULL DEFAULT NULL COMMENT '审核时间',
  `create_time` int(11) UNSIGNED NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '更新时间',
  `wxqrcode` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '微信二维码',
  `click` int(11) NULL DEFAULT 0 COMMENT '浏览量',
  `del` tinyint(10) UNSIGNED NULL DEFAULT 0 COMMENT '删除标志 1-是 0-否',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 68 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '种草社区文章表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_community_article_image
-- ----------------------------
DROP TABLE IF EXISTS `ls_community_article_image`;
CREATE TABLE `ls_community_article_image`  (
  `id` int(10) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `article_id` int(10) NOT NULL COMMENT '文章id',
  `image` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '图片',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 16 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '种草社区文章图片表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_community_article_video
-- ----------------------------
DROP TABLE IF EXISTS `ls_community_article_video`;
CREATE TABLE `ls_community_article_video`  (
  `id` int(10) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `article_id` int(10) NOT NULL COMMENT '文章id',
  `videos` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '图片',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '种草社区文章图片表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_community_category
-- ----------------------------
DROP TABLE IF EXISTS `ls_community_category`;
CREATE TABLE `ls_community_category`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `name` varchar(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '分类名称',
  `sort` int(11) UNSIGNED NULL DEFAULT 255 COMMENT '排序',
  `is_show` tinyint(1) UNSIGNED NULL DEFAULT 1 COMMENT '是否显示  1-是  0-否',
  `create_time` int(11) UNSIGNED NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '更新时间',
  `del` tinyint(10) UNSIGNED NULL DEFAULT 0 COMMENT '删除标志  1-是  0-否',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '种草社区分类表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_community_comment
-- ----------------------------
DROP TABLE IF EXISTS `ls_community_comment`;
CREATE TABLE `ls_community_comment`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '商品评论id',
  `user_id` int(11) NOT NULL DEFAULT 0 COMMENT '用户id',
  `article_id` int(11) NOT NULL DEFAULT 0 COMMENT '文章id',
  `pid` int(11) NOT NULL DEFAULT 0 COMMENT '上级评论id',
  `comment` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商品评论',
  `ancestor_relation` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '上级评论关系链',
  `like` int(11) NULL DEFAULT 0 COMMENT '点赞数量',
  `status` tinyint(1) NULL DEFAULT 0 COMMENT '状态 0-审核中 1-审核通过 2-审核拒绝',
  `create_time` int(11) UNSIGNED NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` int(11) UNSIGNED NULL DEFAULT NULL COMMENT '更新时间',
  `del` tinyint(1) UNSIGNED NULL DEFAULT 0 COMMENT '删除标志 0-未删除 1-删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '种草社区评论表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_community_exhibition
-- ----------------------------
DROP TABLE IF EXISTS `ls_community_exhibition`;
CREATE TABLE `ls_community_exhibition`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `user_id` int(8) UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户id',
  `cate_id` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '分类id',
  `click_nums` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '点击量',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '内容',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '标题',
  `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '详细地址',
  `can_nums` int(11) NULL DEFAULT NULL COMMENT '参加人数',
  `areas` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '面积',
  `person_nums` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '观众数量',
  `clicks` int(11) NULL DEFAULT NULL,
  `shop_nums` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '展商数量',
  `latitude` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '纬度',
  `longitude` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '经度',
  `videos` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '视频',
  `image` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '首图',
  `status` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '审核状态 0-待审核 1-审核通过 2-审核拒绝',
  `audit_remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '审核备注',
  `audit_time` int(11) UNSIGNED NULL DEFAULT NULL COMMENT '审核时间',
  `start_time` int(11) UNSIGNED NULL DEFAULT NULL COMMENT '开始时间',
  `end_time` int(11) UNSIGNED NULL DEFAULT NULL COMMENT '结束时间',
  `create_time` int(11) UNSIGNED NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '更新时间',
  `del` tinyint(10) UNSIGNED NULL DEFAULT 0 COMMENT '删除标志 1-是 0-否',
  `province_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '省ID',
  `city_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '市ID',
  `district_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '区ID',
  `sort` smallint(5) UNSIGNED NOT NULL DEFAULT 0 COMMENT '排序',
  `is_show` smallint(5) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否显示 1不显示0显示',
  `wechat_image` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '首图',
  `site` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '场地',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 19 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '发现展会表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_community_exhibition_image
-- ----------------------------
DROP TABLE IF EXISTS `ls_community_exhibition_image`;
CREATE TABLE `ls_community_exhibition_image`  (
  `id` int(10) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `article_id` int(10) NOT NULL COMMENT '文章id',
  `image` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '图片',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 114 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '种草社区文章图片表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_community_exhibitionform
-- ----------------------------
DROP TABLE IF EXISTS `ls_community_exhibitionform`;
CREATE TABLE `ls_community_exhibitionform`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `user_id` int(8) UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户id',
  `exhibition_id` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '展会信息id',
  `type_id` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '分类1：展位咨询0：观展登记',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '姓名',
  `company` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '公司名称',
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '手机号码',
  `sex` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '性别：0-男 1-女',
  `create_time` int(11) UNSIGNED NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '更新时间',
  `del` tinyint(10) UNSIGNED NULL DEFAULT 0 COMMENT '删除标志 1-是 0-否',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '发现展会登记信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_community_follow
-- ----------------------------
DROP TABLE IF EXISTS `ls_community_follow`;
CREATE TABLE `ls_community_follow`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `user_id` int(8) UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户id',
  `follow_id` int(8) UNSIGNED NOT NULL DEFAULT 0 COMMENT '关注的用户id',
  `status` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '关注状态  0-未关注  1-已关注',
  `create_time` int(11) UNSIGNED NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 23 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '种草社区关注表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_community_like
-- ----------------------------
DROP TABLE IF EXISTS `ls_community_like`;
CREATE TABLE `ls_community_like`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '点赞类型 1-文章 2-评论',
  `user_id` int(11) UNSIGNED NOT NULL COMMENT '用户id',
  `relation_id` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '文章id',
  `create_time` int(11) UNSIGNED NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` int(11) UNSIGNED NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '种草社区点赞表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_community_search_record
-- ----------------------------
DROP TABLE IF EXISTS `ls_community_search_record`;
CREATE TABLE `ls_community_search_record`  (
  `id` int(20) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) UNSIGNED NOT NULL COMMENT '用户id',
  `keyword` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '关键字',
  `count` int(11) UNSIGNED NOT NULL DEFAULT 1 COMMENT '次数',
  `create_time` int(11) UNSIGNED NULL DEFAULT NULL COMMENT '添加时间',
  `update_time` int(11) UNSIGNED NULL DEFAULT NULL COMMENT '更新时间',
  `del` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否已删除,0-未删除,1-已删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '种草社区搜索记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_community_topic
-- ----------------------------
DROP TABLE IF EXISTS `ls_community_topic`;
CREATE TABLE `ls_community_topic`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `name` varchar(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '话题名称',
  `cid` int(11) UNSIGNED NOT NULL COMMENT '分类id',
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '分类图标',
  `sort` int(11) UNSIGNED NULL DEFAULT 255 COMMENT '排序',
  `is_show` tinyint(1) UNSIGNED NULL DEFAULT 1 COMMENT '是否显示 1-是  0-否',
  `is_recommend` tinyint(1) UNSIGNED NULL DEFAULT 0 COMMENT '是否推荐 1-是 0-否',
  `article_num` int(11) NULL DEFAULT 0 COMMENT '文章数量',
  `click` int(11) NULL DEFAULT 0 COMMENT '点击数',
  `create_time` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `update_time` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '更新时间',
  `del` tinyint(10) UNSIGNED NULL DEFAULT 0 COMMENT '删除标志 1-是 0-否',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '种草社区话题表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_community_user
-- ----------------------------
DROP TABLE IF EXISTS `ls_community_user`;
CREATE TABLE `ls_community_user`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `user_id` int(11) UNSIGNED NOT NULL COMMENT '用户id',
  `signature` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '签名',
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '背景图',
  `create_time` int(11) UNSIGNED NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` int(11) UNSIGNED NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 15 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '种草社区用户信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_config
-- ----------------------------
DROP TABLE IF EXISTS `ls_config`;
CREATE TABLE `ls_config`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `shop_id` int(10) NULL DEFAULT 0 COMMENT '店铺id',
  `type` varchar(24) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '类型',
  `name` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '名称',
  `value` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '值',
  `update_time` int(10) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 272 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '配置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_coupon
-- ----------------------------
DROP TABLE IF EXISTS `ls_coupon`;
CREATE TABLE `ls_coupon`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '优惠券名称',
  `send_time_start` int(10) NOT NULL COMMENT '发放开始时间',
  `send_time_end` int(10) NOT NULL COMMENT '发放结束时间',
  `money` decimal(10, 2) NOT NULL COMMENT '优惠券面额(元)',
  `condition_type` tinyint(1) NULL DEFAULT NULL COMMENT '使用条件类型：1-无门槛；2-订单满足金额',
  `condition_money` decimal(10, 2) NULL DEFAULT NULL COMMENT '使用条件类型为2时：该字段为订单满足金额可使用',
  `send_total_type` tinyint(1) NULL DEFAULT NULL COMMENT '发送总量类型：1-不限制；2-限制张数',
  `send_total` int(10) NULL DEFAULT NULL COMMENT '发送总量类型为2时：该字段为限制的张数',
  `use_time_type` tinyint(1) NULL DEFAULT NULL COMMENT '用券时间类型：1-固定时间；2-领券当天起；3-领券次日起',
  `use_time_start` int(10) NULL DEFAULT NULL COMMENT '用券时间类型为1时：该字段为使用开始时间；',
  `use_time_end` int(10) NULL DEFAULT NULL COMMENT '用券时间类型为1时：该字段为使用结束时间；',
  `use_time` int(10) NULL DEFAULT NULL COMMENT '用券时间类型为2、3时：该字段为多少天内可用；',
  `get_type` tinyint(1) NULL DEFAULT NULL COMMENT '领取类型：1-直接领取；2-商家赠送;',
  `get_num_type` tinyint(1) NULL DEFAULT NULL COMMENT '领取次数类型：1-不限制领取次数；2-限制次数；3-每天限制数量',
  `get_num` int(10) NULL DEFAULT NULL COMMENT '领取次数类型为：2、3时：该字段为领取限制的数量',
  `use_goods_type` tinyint(1) NULL DEFAULT NULL COMMENT '适用商品类型:1-全部商品；2-指定商品；3-指定商品不可用',
  `status` tinyint(255) NOT NULL DEFAULT 0 COMMENT '优惠券状态：1-上架中；0-已下架',
  `create_time` int(10) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) NULL DEFAULT NULL COMMENT '更新时间',
  `del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除；1-是；0-否',
  `shop_id` int(11) UNSIGNED NOT NULL COMMENT '商家id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商家优惠券' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_coupon_goods
-- ----------------------------
DROP TABLE IF EXISTS `ls_coupon_goods`;
CREATE TABLE `ls_coupon_goods`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `coupon_id` int(10) NOT NULL COMMENT '优惠券id',
  `goods_id` int(10) NOT NULL COMMENT '商品id',
  `create_time` int(10) NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商家优惠券适配商品表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_coupon_list
-- ----------------------------
DROP TABLE IF EXISTS `ls_coupon_list`;
CREATE TABLE `ls_coupon_list`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `user_id` int(10) NOT NULL COMMENT '用户id',
  `coupon_id` int(10) NOT NULL COMMENT '优惠券id',
  `coupon_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '优惠券券码',
  `status` tinyint(1) NULL DEFAULT NULL COMMENT '状态；0-未使用;1-已使用;2-已过期',
  `order_id` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '优惠券使用的订单id',
  `use_time` int(10) NULL DEFAULT NULL COMMENT '使用时间',
  `create_time` int(10) NOT NULL COMMENT '创建时间',
  `update_time` int(10) NULL DEFAULT NULL COMMENT '更新时间',
  `del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除;1-是；0-否',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商家优惠券领取记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_delivery
-- ----------------------------
DROP TABLE IF EXISTS `ls_delivery`;
CREATE TABLE `ls_delivery`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '发货单ID',
  `order_id` int(11) UNSIGNED NOT NULL COMMENT '订单ID',
  `order_sn` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '订单编号',
  `user_id` int(11) UNSIGNED NOT NULL COMMENT '用户ID',
  `admin_id` int(11) UNSIGNED NULL DEFAULT 0 COMMENT '管理员ID',
  `consignee` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '收货人',
  `mobile` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '联系手机',
  `province` int(11) UNSIGNED NULL DEFAULT NULL COMMENT '省ID',
  `city` int(11) UNSIGNED NULL DEFAULT NULL COMMENT '市ID',
  `district` int(11) UNSIGNED NULL DEFAULT NULL COMMENT '区ID',
  `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '地址',
  `shipping_status` tinyint(1) UNSIGNED NULL DEFAULT 0 COMMENT '发货状态;0-未发货;1-已发货',
  `shipping_id` int(10) NULL DEFAULT NULL COMMENT '物流公司id',
  `shipping_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '快递名称',
  `invoice_no` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '物流单号',
  `send_type` tinyint(1) NULL DEFAULT 0 COMMENT '配送方式:1-快递配送;2-无需快递',
  `create_time` int(11) NOT NULL COMMENT '创建时间',
  `del` tinyint(1) NULL DEFAULT 0 COMMENT '是否删除',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `order_id`(`order_id`) USING BTREE,
  INDEX `user_id`(`user_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 37 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '发货单表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_demand_article
-- ----------------------------
DROP TABLE IF EXISTS `ls_demand_article`;
CREATE TABLE `ls_demand_article`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
  `ctype` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '分类:0:采购需求1：货源信息',
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '文章标题',
  `video_url` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '视频',
  `wechat_img` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '微信二维码',
  `image` varchar(600) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '需求图集',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '内容',
  `visit` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '浏览数',
  `likes` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '点赞数',
  `sort` smallint(5) UNSIGNED NOT NULL DEFAULT 0 COMMENT '排序',
  `is_notice` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否公共[0=否，1=是]',
  `is_show` tinyint(1) UNSIGNED NOT NULL DEFAULT 1 COMMENT '是否显示[0=否，1=是]',
  `del` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除[0=否，1=是]',
  `create_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `end_time` int(10) NOT NULL DEFAULT 0 COMMENT '截止日期',
  `update_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  `intro` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '文章简介',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '文章表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_dev_auth
-- ----------------------------
DROP TABLE IF EXISTS `ls_dev_auth`;
CREATE TABLE `ls_dev_auth`  (
  `id` int(4) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `type` tinyint(1) NULL DEFAULT 1 COMMENT '1-菜单；2-权限',
  `system` tinyint(1) NULL DEFAULT 0 COMMENT '是否为系统级菜单',
  `pid` int(4) NOT NULL DEFAULT 0 COMMENT '分级id',
  `name` varchar(12) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '名称',
  `icon` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '图标',
  `uri` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '后台uri',
  `sort` int(4) NULL DEFAULT 50 COMMENT '排序',
  `disable` tinyint(1) NULL DEFAULT 0 COMMENT '状态：0-启用；1-禁用；',
  `create_time` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '修改时间',
  `del` tinyint(10) NOT NULL DEFAULT 0 COMMENT '0为非删除状态，非0位删除时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 497 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '平台菜单表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_dev_crontab
-- ----------------------------
DROP TABLE IF EXISTS `ls_dev_crontab`;
CREATE TABLE `ls_dev_crontab`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `type` tinyint(1) NULL DEFAULT NULL COMMENT '类型：1-定时任务；2-守护进程',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `command` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '命令内容',
  `parameter` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '参数',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '状态：1-运行；2-停止；3-错误；',
  `expression` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '运行规则',
  `error` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '运行失败原因',
  `create_time` int(11) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` int(11) NULL DEFAULT NULL COMMENT '最后执行时间',
  `last_time` int(11) NULL DEFAULT NULL COMMENT '最后执行时间',
  `time` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '实时执行时长',
  `max_time` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '最大执行时长',
  `system` tinyint(4) NULL DEFAULT 0 COMMENT '是否系统任务：0-否；1-是；',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 23 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '定时任务配置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_dev_navigation
-- ----------------------------
DROP TABLE IF EXISTS `ls_dev_navigation`;
CREATE TABLE `ls_dev_navigation`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '导航名称',
  `selected_icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '选中图标',
  `un_selected_icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '未选中图标',
  `page_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '页面路径',
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '状态 1-显示 0-隐藏',
  `create_time` int(11) NOT NULL COMMENT '创建时间',
  `update_time` int(11) NULL DEFAULT NULL COMMENT '更新时间',
  `del` tinyint(1) NULL DEFAULT NULL COMMENT '删除标识[0-未删除 1-已删除]',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '底部导航表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_dev_notice_setting
-- ----------------------------
DROP TABLE IF EXISTS `ls_dev_notice_setting`;
CREATE TABLE `ls_dev_notice_setting`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '表id',
  `scene` int(10) NOT NULL COMMENT '场景',
  `desc` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '场景描述',
  `type` tinyint(1) NOT NULL COMMENT '通知对象;1-会员;2-商家;3-平台',
  `variable` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '场景变量',
  `system_notice` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '系统通知设置',
  `sms_notice` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '短信通知设置',
  `oa_notice` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '公众号通知设置',
  `mnp_notice` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '小程序通知设置',
  `support` char(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '支持类型',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 19 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '通知设置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_dev_pay
-- ----------------------------
DROP TABLE IF EXISTS `ls_dev_pay`;
CREATE TABLE `ls_dev_pay`  (
  `id` tinyint(3) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '表id',
  `code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '支付英文代号',
  `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '支付方式名称',
  `short_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '简称',
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '图标',
  `sort` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '排序',
  `config` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '对应支付配置(json字符串)',
  `status` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '状态;0-关闭;1-启用',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `pay_code`(`code`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 68 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '支付方式表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_dev_region
-- ----------------------------
DROP TABLE IF EXISTS `ls_dev_region`;
CREATE TABLE `ls_dev_region`  (
  `id` int(10) NOT NULL DEFAULT 0 COMMENT '地区编号',
  `parent_id` int(10) NOT NULL DEFAULT 0 COMMENT '父级地区编码',
  `level` tinyint(1) NOT NULL DEFAULT 0 COMMENT '等级 0-国家；1-省份；2-地级市；3-县区',
  `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '名称',
  `short` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '简称',
  `city_code` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '地区编码',
  `zip_code` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '邮政编码',
  `gcj02_lng` varchar(24) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '纬度',
  `gcj02_lat` varchar(24) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '经度',
  `db09_lng` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '纬度',
  `db09_lat` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '经度',
  `remark1` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '',
  `remark2` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '地区表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_dev_shop_auth
-- ----------------------------
DROP TABLE IF EXISTS `ls_dev_shop_auth`;
CREATE TABLE `ls_dev_shop_auth`  (
  `id` int(4) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `type` tinyint(1) NULL DEFAULT 1 COMMENT '1-菜单；2-权限',
  `system` tinyint(1) NULL DEFAULT 0 COMMENT '是否为系统级菜单',
  `pid` int(4) NOT NULL DEFAULT 0 COMMENT '分级id',
  `name` varchar(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '名称',
  `icon` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '图标',
  `uri` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '后台uri',
  `sort` int(4) NULL DEFAULT 50 COMMENT '排序',
  `disable` tinyint(1) NULL DEFAULT 0 COMMENT '状态：0-启用；1-禁用；',
  `create_time` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '修改时间',
  `del` tinyint(10) NOT NULL DEFAULT 0 COMMENT '0为非删除状态，非0位删除时间',
  `merchant_types` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '0,1,2' COMMENT '允许的商家类型：0=0元入驻,1=商家会员,2=实力厂商，多个用逗号分隔',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 509 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '商家菜单表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_dev_sms_config
-- ----------------------------
DROP TABLE IF EXISTS `ls_dev_sms_config`;
CREATE TABLE `ls_dev_sms_config`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '短信通道',
  `describe` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '描述',
  `sign` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '签名',
  `app_key` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'app_key',
  `secret_key` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'secret_key',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '状态：0-关闭；1-开启',
  `del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除；0-否；1-是',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '短信配置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_distribution
-- ----------------------------
DROP TABLE IF EXISTS `ls_distribution`;
CREATE TABLE `ls_distribution`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `user_id` int(11) UNSIGNED NOT NULL COMMENT '用户id',
  `level_id` int(11) NOT NULL COMMENT '分销会员等级id',
  `is_distribution` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否分销会员 0-否 1-是',
  `is_freeze` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否冻结 0-否 1-是',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  `distribution_time` int(10) NULL DEFAULT NULL COMMENT '成为分销会员时间',
  `mnp_qr_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '小程序分享二维码',
  `h5_qr_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT 'h5分享二维码',
  `app_qr_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT 'app分享二维码',
  `create_time` int(10) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) NULL DEFAULT NULL COMMENT '更新时间',
  `delete_time` int(10) NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 63 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '会员分销信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_distribution_goods
-- ----------------------------
DROP TABLE IF EXISTS `ls_distribution_goods`;
CREATE TABLE `ls_distribution_goods`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `shop_id` int(11) NOT NULL COMMENT '店铺id',
  `goods_id` int(11) UNSIGNED NOT NULL COMMENT '商品id',
  `item_id` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '商品规格id',
  `level_id` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '分销会员等级id',
  `self_ratio` decimal(10, 0) NULL DEFAULT NULL COMMENT '自购佣金比例',
  `first_ratio` decimal(10, 2) NULL DEFAULT NULL COMMENT '一级佣金比例',
  `second_ratio` decimal(10, 2) NULL DEFAULT NULL COMMENT '二级佣金比例',
  `third_ratio` decimal(10, 2) NULL DEFAULT NULL COMMENT '三级佣金比例',
  `is_distribution` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否参与分销 0-不参与 1-参与',
  `rule` tinyint(1) NOT NULL COMMENT '佣金规则 1-按分销等级比例分佣 2-单独设置分佣比例',
  `create_time` int(10) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) NULL DEFAULT NULL COMMENT '更新时间',
  `delete_time` int(10) NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '分销商品表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_distribution_level
-- ----------------------------
DROP TABLE IF EXISTS `ls_distribution_level`;
CREATE TABLE `ls_distribution_level`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '等级名称',
  `weights` tinyint(2) NOT NULL COMMENT '等级权重',
  `first_ratio` float UNSIGNED NOT NULL DEFAULT 0 COMMENT '一级佣金比例',
  `second_ratio` float UNSIGNED NOT NULL DEFAULT 0 COMMENT '二级佣金比例',
  `third_ratio` float UNSIGNED NOT NULL DEFAULT 0 COMMENT '三级佣金比例',
  `self_ratio` float UNSIGNED NOT NULL DEFAULT 0 COMMENT '自购佣金比例',
  `is_default` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否默认等级 0-否 1-是',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '等级描述',
  `update_relation` tinyint(1) UNSIGNED NOT NULL DEFAULT 1 COMMENT '升级关系，1-OR关系 2-AND关系',
  `create_time` int(10) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) NULL DEFAULT NULL COMMENT '更新时间',
  `delete_time` int(10) NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '分销等级表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_distribution_level_update
-- ----------------------------
DROP TABLE IF EXISTS `ls_distribution_level_update`;
CREATE TABLE `ls_distribution_level_update`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT ' ',
  `level_id` int(11) UNSIGNED NOT NULL COMMENT '分销会员等级id',
  `key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '条件名称',
  `value_int` int(11) NULL DEFAULT NULL COMMENT '整型条件值',
  `value_decimal` decimal(10, 2) NULL DEFAULT NULL COMMENT '浮点型条件值',
  `value_text` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '文本型条件值,主要用于存储json格式字符串',
  `create_time` int(11) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` int(11) NULL DEFAULT NULL COMMENT '更新时间',
  `delete_time` int(11) NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '分销升级条件表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_distribution_member_apply
-- ----------------------------
DROP TABLE IF EXISTS `ls_distribution_member_apply`;
CREATE TABLE `ls_distribution_member_apply`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户id',
  `real_name` varchar(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '真实姓名',
  `province` int(11) UNSIGNED NULL DEFAULT 0 COMMENT '省份',
  `city` int(11) UNSIGNED NULL DEFAULT 0 COMMENT '城市',
  `district` int(11) UNSIGNED NULL DEFAULT 0 COMMENT '县区',
  `reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '申请原因',
  `status` tinyint(1) NULL DEFAULT 0 COMMENT '状态：0-待审核；1-审核通过；2-审核不通过',
  `create_time` int(10) NOT NULL COMMENT '创建时间',
  `update_time` int(10) NULL DEFAULT NULL COMMENT '更新时间',
  `denial_reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '拒绝原因',
  `mobile` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '手机号',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '分销会员申请表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_distribution_order_goods
-- ----------------------------
DROP TABLE IF EXISTS `ls_distribution_order_goods`;
CREATE TABLE `ls_distribution_order_goods`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `sn` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '记录编号',
  `user_id` int(11) NOT NULL COMMENT '用户id',
  `real_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '真实姓名',
  `level_id` int(11) NULL DEFAULT NULL COMMENT '分销会员等级',
  `level` int(11) NULL DEFAULT NULL COMMENT '分销层级',
  `ratio` decimal(10, 2) NOT NULL COMMENT '佣金比例',
  `order_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '订单ID',
  `order_goods_id` int(11) NOT NULL COMMENT '订单商品id',
  `goods_num` int(10) NOT NULL DEFAULT 1 COMMENT '商品数量',
  `money` decimal(10, 2) UNSIGNED NOT NULL COMMENT '佣金',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：1-待返佣；2-已结算；3-已失效；',
  `create_time` int(10) NOT NULL COMMENT '创建时间',
  `update_time` int(10) NULL DEFAULT NULL COMMENT '更新时间',
  `shop_id` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '店铺id',
  `settlement_time` int(10) NULL DEFAULT NULL COMMENT '结算时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user_id_source_id`(`user_id`) USING BTREE COMMENT '一个用户只有一个订单商品的分佣订单'
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '分销订单表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_express
-- ----------------------------
DROP TABLE IF EXISTS `ls_express`;
CREATE TABLE `ls_express`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '快递公司',
  `icon` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '快递图标',
  `website` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '公司网址',
  `code` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '快递编码',
  `code100` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '快递100编码',
  `sort` int(10) NOT NULL DEFAULT 0 COMMENT '排序',
  `codebird` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '快递鸟编码',
  `del` tinyint(1) NULL DEFAULT 0 COMMENT '是否删除,0-未删除,1-已删除',
  `create_time` int(11) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` int(11) NULL DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 21 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '快递公司表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_face_sheet_sender
-- ----------------------------
DROP TABLE IF EXISTS `ls_face_sheet_sender`;
CREATE TABLE `ls_face_sheet_sender`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `shop_id` int(11) NOT NULL COMMENT '店铺id',
  `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '发件人名称',
  `mobile` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '发件人电话',
  `province_id` int(10) UNSIGNED NOT NULL COMMENT '省ID',
  `city_id` int(10) UNSIGNED NOT NULL COMMENT '市ID',
  `district_id` int(10) UNSIGNED NOT NULL COMMENT '区ID',
  `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '详细地址',
  `create_time` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '更新时间',
  `delete_time` int(10) NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '电子面单发送人模板表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_face_sheet_template
-- ----------------------------
DROP TABLE IF EXISTS `ls_face_sheet_template`;
CREATE TABLE `ls_face_sheet_template`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `shop_id` int(11) NOT NULL COMMENT '店铺id',
  `express_id` int(10) UNSIGNED NOT NULL COMMENT '快递公司ID',
  `name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '模板名称',
  `template_id` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '模板ID',
  `partner_id` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '电子面单账号',
  `partner_key` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '电子面单密码',
  `net` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '网点标识',
  `pay_type` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '运费支付方式',
  `create_time` int(10) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '更新时间',
  `delete_time` int(10) NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '电子面单模板表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_feedback
-- ----------------------------
DROP TABLE IF EXISTS `ls_feedback`;
CREATE TABLE `ls_feedback`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL DEFAULT 0,
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `images` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `status` tinyint(1) NULL DEFAULT 0 COMMENT '0:未处理1已处理',
  `comments` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 12 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_file
-- ----------------------------
DROP TABLE IF EXISTS `ls_file`;
CREATE TABLE `ls_file`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '文件名称',
  `cid` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '类目ID',
  `type` tinyint(2) UNSIGNED NOT NULL DEFAULT 10 COMMENT '类型[10=图片, 2=视频]',
  `size` int(10) NOT NULL DEFAULT 0,
  `uri` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '文件路径',
  `create_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `del` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除[0=否，1=是]',
  `shop_id` int(10) NOT NULL COMMENT '店铺id 0-平台 ',
  `images` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '视频封面',
  `user_id` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户id 前端用户上传的图片',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2287 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '文件管理表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_file_cate
-- ----------------------------
DROP TABLE IF EXISTS `ls_file_cate`;
CREATE TABLE `ls_file_cate`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `shop_id` int(10) NOT NULL COMMENT '店铺id 0-平台',
  `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '分类名称',
  `pid` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '父级ID',
  `type` tinyint(2) UNSIGNED NOT NULL DEFAULT 10 COMMENT '类型[10=图片，20=视频，30=文件]',
  `level` tinyint(1) NULL DEFAULT NULL COMMENT '层级',
  `sort` smallint(5) UNSIGNED NOT NULL DEFAULT 0 COMMENT '排序',
  `del` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除[0=否，1=是]',
  `create_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 46 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '文件分类表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_footprint
-- ----------------------------
DROP TABLE IF EXISTS `ls_footprint`;
CREATE TABLE `ls_footprint`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `type` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '提醒类型',
  `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '提醒名称',
  `template` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '提醒模板',
  `status` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '气泡状态[0=关闭,1=开启]',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '足迹气泡表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_footprint_record
-- ----------------------------
DROP TABLE IF EXISTS `ls_footprint_record`;
CREATE TABLE `ls_footprint_record`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `type` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '提醒类型',
  `user_id` int(10) UNSIGNED NOT NULL COMMENT '用户ID',
  `foreign_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '外键ID(取决于type字段, 有可能是商品ID, 也可能是其它表的ID)',
  `template` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '模板信息',
  `create_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2366 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '足迹气泡记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_free_shipping_config
-- ----------------------------
DROP TABLE IF EXISTS `ls_free_shipping_config`;
CREATE TABLE `ls_free_shipping_config`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `shop_id` int(11) NOT NULL COMMENT '店铺id',
  `status` tinyint(1) NOT NULL COMMENT '是否开启包邮活动 0-否 1-是',
  `goods_type` tinyint(1) NOT NULL COMMENT '活动商品类型 1-全部商品',
  `free_rule` tinyint(1) NOT NULL COMMENT '免邮规则 1-按订单金额',
  `create_time` int(10) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) NULL DEFAULT NULL COMMENT '更新时间',
  `del` tinyint(1) NULL DEFAULT 0 COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '包邮配置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_free_shipping_region
-- ----------------------------
DROP TABLE IF EXISTS `ls_free_shipping_region`;
CREATE TABLE `ls_free_shipping_region`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `shop_id` int(11) NOT NULL COMMENT '店铺id',
  `region` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '地区编号',
  `order_amount` decimal(10, 2) NOT NULL COMMENT '订单金额',
  `create_time` int(10) NULL DEFAULT NULL COMMENT '创建时间',
  `del` tinyint(1) NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '包邮地区表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_freight
-- ----------------------------
DROP TABLE IF EXISTS `ls_freight`;
CREATE TABLE `ls_freight`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '表id',
  `shop_id` int(11) NOT NULL COMMENT '商户id',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '模板名称',
  `charge_way` tinyint(1) NULL DEFAULT 0 COMMENT '计费方式:1-重量计费;2-体积计费;3-件数计费',
  `remark` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '备注',
  `create_time` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '添加时间',
  `update_time` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '运费模板表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_freight_config
-- ----------------------------
DROP TABLE IF EXISTS `ls_freight_config`;
CREATE TABLE `ls_freight_config`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '表id',
  `freight_id` int(11) NULL DEFAULT NULL COMMENT '模板id',
  `first_unit` int(10) NULL DEFAULT NULL COMMENT '首重/件',
  `first_money` decimal(10, 0) NULL DEFAULT NULL COMMENT '首重/件价格',
  `continue_unit` int(10) NULL DEFAULT NULL COMMENT '续重/件',
  `continue_money` decimal(10, 0) NULL DEFAULT NULL COMMENT '首重/件价格',
  `region` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '地区id',
  `create_time` int(10) NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 34 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '运费模板配置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_goods
-- ----------------------------
DROP TABLE IF EXISTS `ls_goods`;
CREATE TABLE `ls_goods`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `type` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '商品类型 0-实物商品 1-虚拟商品',
  `name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商品名称',
  `code` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商品编码',
  `shop_id` int(11) UNSIGNED NOT NULL COMMENT '商家id',
  `shop_cate_id` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '商家商品分类id',
  `first_cate_id` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '平台商品一级分类id',
  `second_cate_id` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '平台商品二级分类id',
  `third_cate_id` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '平台商品三级分类id',
  `brand_id` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '品牌id',
  `unit_id` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '商品单位id',
  `supplier_id` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '供应商id',
  `status` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '销售状态: 0-仓库中；1-上架中',
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商品主图',
  `video` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商品视频',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商品简介',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '商品详细描述',
  `sort` int(11) UNSIGNED NOT NULL DEFAULT 255 COMMENT '排序,值越小越靠前',
  `sales_actual` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '实际销量',
  `clicks` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '点击量',
  `is_analyzed` tinyint(11) NOT NULL DEFAULT 0 COMMENT '0',
  `clicks_virtual` int(11) UNSIGNED NULL DEFAULT 0 COMMENT '虚拟点击量',
  `spec_type` tinyint(1) UNSIGNED NOT NULL COMMENT '商品规格:1-统一规格；2-多规格；',
  `max_price` decimal(10, 2) UNSIGNED NOT NULL COMMENT '最高价格(SKU中最高价)',
  `min_price` decimal(10, 2) UNSIGNED NOT NULL COMMENT '最低价格(SKU中最低价)',
  `market_price` decimal(10, 2) UNSIGNED NOT NULL COMMENT '市场价(最低价格对应商品吊牌价/划线价)',
  `stock` int(10) UNSIGNED NOT NULL COMMENT '总库存',
  `express_type` tinyint(1) UNSIGNED NOT NULL DEFAULT 1 COMMENT '运费类型：1-包邮；2-统一运费；3-运费模板',
  `express_money` decimal(10, 2) UNSIGNED NOT NULL COMMENT '统一运费金额',
  `express_template_id` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '运费模板',
  `is_recommend` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否商品推荐 0-否 1-是',
  `audit_status` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '审核状态 0-待审核;1-审核通过;2-审核失败',
  `audit_remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '审核备注',
  `create_time` int(11) UNSIGNED NOT NULL COMMENT '商品创建时间',
  `update_time` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '商品更新时间',
  `del` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '删除状态 0-正常 1-已删除 2-回收站',
  `stock_warn` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '库存预警',
  `column_ids` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商品栏目,多个用逗号分隔',
  `sales_virtual` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '虚拟销量',
  `sort_weight` int(11) UNSIGNED NOT NULL DEFAULT 255 COMMENT '商品权重，值越小权重越大',
  `poster` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '分享海报',
  `is_show_stock` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否显示库存 0-不显示 1-显示',
  `is_member` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否启用会员价 0-不启用 1-启用',
  `is_distribution` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否开启分销 0-不开启 1-开启',
  `first_ratio` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '一级分销比例',
  `second_ratio` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '二级分销比例',
  `third_ratio` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '三级分销比例',
  `delivery_type` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '1' COMMENT '配送方式（以逗号分隔）1-快递发货 2-虚拟发货  3-线下自提',
  `after_pay` tinyint(1) NULL DEFAULT 0 COMMENT '买家付款后  1-自动大货 2-手动发货',
  `after_delivery` tinyint(1) NULL DEFAULT 0 COMMENT '发货后 1-自动完成订单 2-需要买家确认收货',
  `is_list` tinyint(1) NOT NULL DEFAULT 0 COMMENT '规格是否显示列表或者图片0：列表',
  `join_jc` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否加入拼单集采0:不加入 1:加入',
  `hgou_lv` float NOT NULL DEFAULT 0 COMMENT '回购率',
  `year_jc_sales` int(5) NOT NULL DEFAULT 0 COMMENT '集采年销量',
  `year_sales` int(5) NOT NULL DEFAULT 0 COMMENT '年销量',
  `collect_nums` int(5) NOT NULL DEFAULT 0 COMMENT '收藏量',
  `reshare_nums` int(5) NOT NULL DEFAULT 0 COMMENT '有效分享次数',
  `share_nums` int(5) NOT NULL DEFAULT 0 COMMENT '分享次数',
  `goods_label_top` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商品标签(集采众筹,拼单集采,新品上新,行家严选)',
  `goods_label` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商品标签(年度榜,收藏榜,推荐榜)',
  `is_hot` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否热卖',
  `split_word` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商品分词库',
  `delivery_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '发货内容',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `name`(`name`) USING BTREE COMMENT '商品名称',
  INDEX `remark`(`remark`) USING BTREE COMMENT '商品介绍'
) ENGINE = InnoDB AUTO_INCREMENT = 1364 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_goods_brand
-- ----------------------------
DROP TABLE IF EXISTS `ls_goods_brand`;
CREATE TABLE `ls_goods_brand`  (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '品牌名称',
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '品牌图片',
  `initial` varchar(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '品牌首字母',
  `is_show` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否显示:1-是.0-否',
  `sort` int(5) UNSIGNED NULL DEFAULT 255 COMMENT '排序',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '品牌描述',
  `create_time` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '修改时间',
  `del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除,0-未删除,1-已删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品品牌表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_goods_category
-- ----------------------------
DROP TABLE IF EXISTS `ls_goods_category`;
CREATE TABLE `ls_goods_category`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `name` varchar(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '分类名称',
  `pid` int(11) UNSIGNED NOT NULL COMMENT '父级id',
  `level` tinyint(1) UNSIGNED NULL DEFAULT NULL COMMENT '等级',
  `sort` int(11) UNSIGNED NULL DEFAULT 255 COMMENT '排序',
  `is_show` tinyint(1) UNSIGNED NULL DEFAULT 1 COMMENT '是否显示:1-是;0-否',
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '分类图标',
  `bg_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '首页分类背景图',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '分类描述',
  `create_time` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `update_time` int(10) NULL DEFAULT NULL COMMENT '更新时间',
  `del` tinyint(10) UNSIGNED NULL DEFAULT 0 COMMENT '删除标志:1-是；0-否',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 187 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '平台商品分类' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_goods_click
-- ----------------------------
DROP TABLE IF EXISTS `ls_goods_click`;
CREATE TABLE `ls_goods_click`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `shop_id` int(10) NOT NULL DEFAULT 1 COMMENT '商家id',
  `user_id` int(11) NULL DEFAULT NULL COMMENT '用户id,NULL代表游客',
  `goods_id` int(11) UNSIGNED NOT NULL COMMENT '商品id',
  `create_time` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5361 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品点击表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_goods_collect
-- ----------------------------
DROP TABLE IF EXISTS `ls_goods_collect`;
CREATE TABLE `ls_goods_collect`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `shop_id` int(10) NOT NULL DEFAULT 0 COMMENT '商家id',
  `user_id` int(11) UNSIGNED NOT NULL COMMENT '用户id',
  `goods_id` int(11) UNSIGNED NOT NULL COMMENT '商品id',
  `create_time` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `update_time` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  `status` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否收藏 0-未收藏 1-已收藏',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 73 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品收藏表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_goods_column
-- ----------------------------
DROP TABLE IF EXISTS `ls_goods_column`;
CREATE TABLE `ls_goods_column`  (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `types` tinyint(1) NOT NULL DEFAULT 0 COMMENT '0商品1商家',
  `name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '栏目名称',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '栏目描述',
  `image` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `sort` int(5) NULL DEFAULT 0 COMMENT '排序',
  `status` tinyint(1) NULL DEFAULT 0 COMMENT '显示状态:0-隐藏;1-显示',
  `create_time` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '修改时间',
  `del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除,0-未删除,1-已删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 12 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品栏目表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_goods_comment
-- ----------------------------
DROP TABLE IF EXISTS `ls_goods_comment`;
CREATE TABLE `ls_goods_comment`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '商品评论id',
  `goods_id` int(128) NOT NULL COMMENT '商品id',
  `item_id` int(128) NOT NULL COMMENT '规格id',
  `user_id` int(11) NOT NULL COMMENT '用户id',
  `order_goods_id` int(11) NOT NULL COMMENT '订单商品表id',
  `goods_comment` tinyint(1) NOT NULL COMMENT '商品评论星级 1 一星 2 二星 3三星 4四星 5五星',
  `service_comment` tinyint(1) NOT NULL COMMENT '服务评论星级 1 一星 2 二星 3三星 4四星 5五星',
  `express_comment` tinyint(1) NOT NULL COMMENT '物流评论星级 1 一星 2 二星 3三星 4四星 5五星',
  `comment` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商品评论',
  `create_time` int(11) NOT NULL COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  `del` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '删除标志:0-未删除.1-删除',
  `description_comment` tinyint(1) NULL DEFAULT NULL COMMENT '描述相符星级1 一星 2 二星 3三星 4四星 5五星',
  `status` tinyint(2) UNSIGNED NOT NULL DEFAULT 1 COMMENT '显示状态 0-隐藏 1-显示 ',
  `reply` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商家回复',
  `shop_id` int(11) UNSIGNED NOT NULL COMMENT '店铺id',
  `is_image_comment` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否为图片评价 0-否 1-是',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 13 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品评论表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_goods_comment_image
-- ----------------------------
DROP TABLE IF EXISTS `ls_goods_comment_image`;
CREATE TABLE `ls_goods_comment_image`  (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `goods_comment_id` int(10) NOT NULL COMMENT '商品评价id',
  `uri` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '图片',
  `shop_id` int(11) UNSIGNED NOT NULL COMMENT '店铺id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 14 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品评论图片' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_goods_footprint
-- ----------------------------
DROP TABLE IF EXISTS `ls_goods_footprint`;
CREATE TABLE `ls_goods_footprint`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `shop_id` int(10) NOT NULL DEFAULT 0 COMMENT '商家id',
  `user_id` int(11) UNSIGNED NOT NULL COMMENT '用户id',
  `nums` int(11) NOT NULL,
  `goods_id` int(11) UNSIGNED NOT NULL COMMENT '商品id',
  `create_time` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `update_time` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 672 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品足迹表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_goods_image
-- ----------------------------
DROP TABLE IF EXISTS `ls_goods_image`;
CREATE TABLE `ls_goods_image`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `goods_id` int(11) UNSIGNED NOT NULL COMMENT '商品id',
  `uri` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '图片路径',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4546 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品轮播图表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_goods_item
-- ----------------------------
DROP TABLE IF EXISTS `ls_goods_item`;
CREATE TABLE `ls_goods_item`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商品SKU图像',
  `goods_id` int(11) UNSIGNED NOT NULL COMMENT '商品id',
  `spec_value_ids` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '多个规格id，隔开',
  `spec_value_str` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '多个规格名称，隔开',
  `market_price` decimal(10, 2) UNSIGNED NOT NULL COMMENT '市场价',
  `price` decimal(10, 2) UNSIGNED NOT NULL COMMENT '价格',
  `stock` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '库存',
  `volume` decimal(10, 3) UNSIGNED NOT NULL COMMENT '体积',
  `weight` decimal(10, 3) UNSIGNED NOT NULL COMMENT '重量',
  `bar_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '条码',
  `chengben_price` decimal(10, 2) UNSIGNED NULL DEFAULT NULL COMMENT '成本价',
  `pdjc_price` decimal(10, 2) UNSIGNED NULL DEFAULT 0.00 COMMENT '拼单集采价',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 41215 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品SKU表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_goods_spec
-- ----------------------------
DROP TABLE IF EXISTS `ls_goods_spec`;
CREATE TABLE `ls_goods_spec`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `goods_id` int(11) UNSIGNED NOT NULL COMMENT '商品id',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '规格名称',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1723 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品规格项表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_goods_spec_value
-- ----------------------------
DROP TABLE IF EXISTS `ls_goods_spec_value`;
CREATE TABLE `ls_goods_spec_value`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `goods_id` int(11) UNSIGNED NOT NULL COMMENT '商品id',
  `spec_id` int(11) UNSIGNED NOT NULL COMMENT '规格id',
  `value` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '规格值',
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2612 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品规格值表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_goods_unit
-- ----------------------------
DROP TABLE IF EXISTS `ls_goods_unit`;
CREATE TABLE `ls_goods_unit`  (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '名称',
  `sort` int(5) UNSIGNED NULL DEFAULT 255 COMMENT '排序',
  `create_time` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '修改时间',
  `del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除,0-未删除,1-已删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '商品单位表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_help
-- ----------------------------
DROP TABLE IF EXISTS `ls_help`;
CREATE TABLE `ls_help`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
  `cid` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '分类ID',
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '帮助标题',
  `image` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '封面图',
  `document_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '文档路径',
  `document_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '文档名称',
  `cate_label` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '协议标签',
  `intro` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '简介',
  `content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '文章内容',
  `visit` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '浏览数',
  `likes` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '点赞数',
  `sort` smallint(5) UNSIGNED NOT NULL DEFAULT 0 COMMENT '排序',
  `is_show` tinyint(1) UNSIGNED NOT NULL DEFAULT 1 COMMENT '是否显示[0=否，1=是]',
  `del` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除[0=否，1=是]',
  `create_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 36 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '帮助表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_help_category
-- ----------------------------
DROP TABLE IF EXISTS `ls_help_category`;
CREATE TABLE `ls_help_category`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '分类名称',
  `is_show` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否显示[0=否，1=是]',
  `del` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除[0=否， 1=是]',
  `create_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '帮助分类表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_images
-- ----------------------------
DROP TABLE IF EXISTS `ls_images`;
CREATE TABLE `ls_images`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `image_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `source_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `created_at` datetime NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 161 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_integral_delivery
-- ----------------------------
DROP TABLE IF EXISTS `ls_integral_delivery`;
CREATE TABLE `ls_integral_delivery`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '发货单ID',
  `order_id` int(11) UNSIGNED NOT NULL COMMENT '订单ID',
  `order_sn` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '订单编号',
  `user_id` int(11) UNSIGNED NOT NULL COMMENT '用户ID',
  `admin_id` int(11) UNSIGNED NULL DEFAULT 0 COMMENT '管理员ID',
  `consignee` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '收货人',
  `mobile` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '联系手机',
  `province` int(11) UNSIGNED NULL DEFAULT NULL COMMENT '省ID',
  `city` int(11) UNSIGNED NULL DEFAULT NULL COMMENT '市ID',
  `district` int(11) UNSIGNED NULL DEFAULT NULL COMMENT '区ID',
  `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '地址',
  `shipping_status` tinyint(1) UNSIGNED NULL DEFAULT 0 COMMENT '发货状态,0-未发货,1-已发货',
  `shipping_id` int(10) NULL DEFAULT NULL COMMENT '物流公司id',
  `shipping_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '快递名称',
  `invoice_no` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '物流单号',
  `send_type` tinyint(1) NULL DEFAULT 0 COMMENT '配送方式:1-快递配送,2-无需快递',
  `create_time` int(11) NOT NULL COMMENT '创建时间',
  `del` tinyint(1) NULL DEFAULT 0 COMMENT '是否删除',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `order_id`(`order_id`) USING BTREE,
  INDEX `user_id`(`user_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '积分商城发货单表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_integral_goods
-- ----------------------------
DROP TABLE IF EXISTS `ls_integral_goods`;
CREATE TABLE `ls_integral_goods`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商品名称',
  `code` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商品编码',
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商品主图',
  `type` tinyint(1) UNSIGNED NOT NULL DEFAULT 1 COMMENT '类型：1商品 2-红包',
  `market_price` decimal(10, 2) UNSIGNED NULL DEFAULT NULL COMMENT '市场价(最低价格对应商品吊牌价/划线价)',
  `stock` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '总库存',
  `status` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '商品状态: 0-下架,1-上架中',
  `exchange_way` tinyint(1) NOT NULL DEFAULT 1 COMMENT '兑换方式 1-积分 2-积分加余额',
  `need_integral` int(11) NOT NULL DEFAULT 0 COMMENT '积分',
  `need_money` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '金额',
  `delivery_way` tinyint(1) NOT NULL DEFAULT 1 COMMENT '配送方式  0-无需物流 1-快递',
  `balance` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '红包面值-待定',
  `express_type` tinyint(1) UNSIGNED NOT NULL DEFAULT 1 COMMENT '运费类型：1-包邮,2-统一运费,3-运费模板',
  `express_money` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '统一运费金额',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '商品详细描述',
  `sales` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '销量',
  `sort` smallint(5) UNSIGNED NULL DEFAULT 0 COMMENT '排序',
  `create_time` int(11) UNSIGNED NOT NULL COMMENT '商品创建时间',
  `update_time` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '商品更新时间',
  `del` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '删除状态 0-正常 1-已删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '积分商品表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_integral_order
-- ----------------------------
DROP TABLE IF EXISTS `ls_integral_order`;
CREATE TABLE `ls_integral_order`  (
  `id` int(10) NOT NULL AUTO_INCREMENT COMMENT '订单id',
  `order_sn` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '订单编号',
  `user_id` int(8) UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户id',
  `exchange_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '兑换类型 1-商品 2-红包',
  `exchange_way` tinyint(1) NOT NULL DEFAULT 1 COMMENT '兑换方式 1-积分 2-积分加余额',
  `order_source` tinyint(1) NOT NULL DEFAULT 1 COMMENT '订单来源 1-小程序 2-h5 3-ios 4-安卓',
  `order_status` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '订单状态 0-待付款 1-待发货 2-待收货 3-已发货 4-已取消',
  `pay_status` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '支付状态 0-待支付 1-已支付 2-已退款 3-拒绝退款',
  `pay_way` tinyint(2) NULL DEFAULT 0 COMMENT '1-微信支付  2-支付宝支付 3-余额支付  4-线下支付',
  `pay_time` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '支付时间',
  `delivery_way` tinyint(1) NOT NULL DEFAULT 0 COMMENT '配送方式 0-无需快递 1-快递配送',
  `consignee` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '收货人',
  `province` int(11) UNSIGNED NULL DEFAULT 0 COMMENT '省份',
  `city` int(11) UNSIGNED NULL DEFAULT 0 COMMENT '城市',
  `district` int(11) UNSIGNED NULL DEFAULT 0 COMMENT '县区',
  `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '地址',
  `mobile` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '手机',
  `order_amount` decimal(10, 2) NOT NULL COMMENT '应支付金额（商品金额+运费）',
  `order_integral` int(10) NOT NULL COMMENT '应支付积分（商品应付积分）',
  `total_num` int(10) NOT NULL DEFAULT 0 COMMENT '订单商品数量',
  `goods_price` decimal(10, 2) NOT NULL COMMENT '商品应付金额',
  `shipping_status` tinyint(1) UNSIGNED NULL DEFAULT 0 COMMENT '发货状态',
  `shipping_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '运费',
  `shipping_time` int(11) NULL DEFAULT 0 COMMENT '最后新发货时间',
  `transaction_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '第三方平台交易流水号',
  `hfdg_params` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '汇付斗拱记录参数',
  `goods_snap` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商品信息快照',
  `user_remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '用户备注',
  `cancel_time` int(10) NULL DEFAULT NULL COMMENT '订单取消时间',
  `refund_status` tinyint(1) NULL DEFAULT 0 COMMENT '退款状态 0-未退款 1-已退款',
  `refund_amount` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '退款金额',
  `refund_integral` int(10) NULL DEFAULT 0 COMMENT '退回积分',
  `confirm_time` int(10) NULL DEFAULT NULL COMMENT '确认收货时间',
  `create_time` int(10) UNSIGNED NOT NULL COMMENT '下单时间',
  `update_time` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '更新时间',
  `del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '删除标识 1-删除 0-未删除',
  `wechat_mini_express_sync` tinyint(1) NULL DEFAULT 0 COMMENT '微信小程序发货信息录入：0-未录入；1-已录入；2-录入失败',
  `wechat_mini_express_sync_time` int(11) NULL DEFAULT 0 COMMENT '微信发货录入时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `order_sn`(`order_sn`) USING BTREE,
  INDEX `user_id`(`user_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '积分订单表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_integral_order_refund
-- ----------------------------
DROP TABLE IF EXISTS `ls_integral_order_refund`;
CREATE TABLE `ls_integral_order_refund`  (
  `id` int(6) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `order_id` int(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '订单id',
  `user_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '下单用户id，冗余字段',
  `refund_sn` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '退款单号，一个订单分多次退款则有多个退款单号',
  `order_amount` decimal(10, 2) UNSIGNED NOT NULL COMMENT '订单总的应付款金额，冗余字段',
  `refund_amount` decimal(10, 2) UNSIGNED NOT NULL COMMENT '本次退款金额',
  `transaction_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '第三方平台交易流水号',
  `hfdg_params` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '汇付斗拱记录参数',
  `refund_status` tinyint(1) UNSIGNED NULL DEFAULT 0 COMMENT '退款状态，0退款中，1完成退款，2退款失败，3退款异常（人工去后台查询）',
  `refund_way` tinyint(1) UNSIGNED NULL DEFAULT 0 COMMENT '退款方式，0原路退',
  `refund_time` int(1) UNSIGNED NULL DEFAULT 0 COMMENT '退款时间',
  `wechat_refund_id` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '微信返回退款id',
  `refund_msg` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '第三方返回信息',
  `create_time` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(10) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '积分订单退款记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_jcai_activity
-- ----------------------------
DROP TABLE IF EXISTS `ls_jcai_activity`;
CREATE TABLE `ls_jcai_activity`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '集采ID',
  `shop_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '门店ID',
  `goods_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '商品ID',
  `people_num` smallint(5) UNSIGNED NOT NULL DEFAULT 0 COMMENT '众筹所需人数',
  `share_title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '分享标题',
  `intro` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
  `share_intro` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '分享简介',
  `team_max_price` decimal(8, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '最高众筹价',
  `team_min_price` decimal(8, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '最低众筹价',
  `sales_volume` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '众筹数量(发起众筹就计算)',
  `audit` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '审核状态[0=待审核，1=审核通过，2=拒绝通过]',
  `status` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '活动状态[0=停止中, 1=活动中,2=已结束]',
  `del` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除[0=否, 1=是]',
  `explain` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '审核说明',
  `effective_time` double(5, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '众筹有效时间(小时, 过了就失效)',
  `activity_start_time` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '众筹开始时间',
  `activity_end_time` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '众筹结束时间',
  `create_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  `total_num` decimal(8, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '募集总金额',
  `buy_num` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '限购',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 32 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '集采众筹主表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_jcai_found
-- ----------------------------
DROP TABLE IF EXISTS `ls_jcai_found`;
CREATE TABLE `ls_jcai_found`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `shop_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '店铺ID',
  `jcai_activity_id` int(10) NOT NULL COMMENT '集采ID',
  `jcai_sn` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '集采编号',
  `user_id` int(10) NOT NULL COMMENT '众筹用户ID',
  `people` smallint(5) UNSIGNED NOT NULL DEFAULT 0 COMMENT '集采购所需人数',
  `join` smallint(5) UNSIGNED NOT NULL DEFAULT 0 COMMENT '现集采购人数',
  `status` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '状态[0-待众筹, 1-众筹成功, 2-众筹失败]',
  `goods_snap` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '活动商品快照',
  `kaituan_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '众筹时间',
  `invalid_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '众筹失效时间',
  `jcai_end_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '众筹成功 / 失败的时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '众筹开始表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_jcai_goods
-- ----------------------------
DROP TABLE IF EXISTS `ls_jcai_goods`;
CREATE TABLE `ls_jcai_goods`  (
  `id` int(10) UNSIGNED ZEROFILL NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `jcai_id` int(10) NOT NULL COMMENT '集众筹ID',
  `goods_id` int(10) UNSIGNED NOT NULL COMMENT '商品ID',
  `item_id` int(10) UNSIGNED NOT NULL COMMENT '规格ID',
  `jcai_price` decimal(8, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '集采购价',
  `pdjcai_price` decimal(8, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '拼单集采价',
  `sales_volume` decimal(8, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '销量',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 219 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '拼团商品表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_jcai_join
-- ----------------------------
DROP TABLE IF EXISTS `ls_jcai_join`;
CREATE TABLE `ls_jcai_join`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `sn` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '集采编号',
  `shop_id` int(10) NOT NULL COMMENT '店铺ID',
  `jcai_activity_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '集采活动ID',
  `jcai_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '集采ID',
  `user_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户ID',
  `order_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '订单ID',
  `identity` tinyint(1) UNSIGNED NOT NULL DEFAULT 2 COMMENT '身份[1=集采发起人， 2=参加集采]',
  `jcai_snap` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '拼团快照',
  `status` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '状态[0-待集采; 1-集采成功; 2-集采失败]',
  `create_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '拼团时间',
  `update_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 14 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '参与集采表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_jcai_order
-- ----------------------------
DROP TABLE IF EXISTS `ls_jcai_order`;
CREATE TABLE `ls_jcai_order`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `order_sn` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '订单编号',
  `transaction_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '第三方平台交易流水号',
  `hfdg_params` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '汇付斗拱记录参数',
  `order_source` tinyint(1) NULL DEFAULT 1 COMMENT '订单来源：1-小程序;2-h5;3-ios;4-安卓',
  `pay_way` tinyint(2) NOT NULL DEFAULT 1 COMMENT '支付方式：1-微信支付  2-支付宝支付 3-余额支付',
  `pay_status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '支付状态：0-待支付；1-已支付',
  `pay_time` int(10) NULL DEFAULT NULL COMMENT '支付时间',
  `user_id` int(11) NOT NULL COMMENT '用户id',
  `template_id` int(11) NULL DEFAULT NULL COMMENT '模板id',
  `order_amount` float NOT NULL COMMENT '会员金额',
  `buy_time` int(10) NULL DEFAULT NULL COMMENT '购买时长',
  `wechat_mini_express_sync` tinyint(3) UNSIGNED NULL DEFAULT 0 COMMENT '微信小程序发货信息录入',
  `wechat_mini_express_sync_time` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '录入时间',
  `create_time` int(10) NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 87 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '充值订单表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_jcai_precontract
-- ----------------------------
DROP TABLE IF EXISTS `ls_jcai_precontract`;
CREATE TABLE `ls_jcai_precontract`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `user_id` int(8) UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户id',
  `jcai_id` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '集采众筹id',
  `create_time` int(11) UNSIGNED NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '更新时间',
  `del` tinyint(10) UNSIGNED NULL DEFAULT 0 COMMENT '通知标识,0:未通知 1已通知',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '集采众筹预约表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_jcai_record
-- ----------------------------
DROP TABLE IF EXISTS `ls_jcai_record`;
CREATE TABLE `ls_jcai_record`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `activity_id` int(10) UNSIGNED NOT NULL COMMENT '关联的众筹活动ID',
  `user_id` int(10) UNSIGNED NOT NULL COMMENT '支持的用户ID',
  `order_sn` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '关联的订单号',
  `support_price` decimal(10, 2) NOT NULL COMMENT '支持时的价格 (通常等于活动价)',
  `support_count` int(10) UNSIGNED NOT NULL DEFAULT 1 COMMENT '支持的数量 (购买件数)',
  `support_amount` decimal(15, 2) NOT NULL COMMENT '支持总金额 (price * count)',
  `pay_status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '支付状态: 0=未支付, 1=已支付',
  `pay_time` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '支付时间戳',
  `refund_status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '退款状态: 0=未退款, 1=处理中, 2=已退款 (众筹失败时用)',
  `refund_time` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '退款时间戳',
  `create_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '记录创建时间',
  `update_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '记录更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_order_sn`(`order_sn`) USING BTREE,
  INDEX `idx_activity_id`(`activity_id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '众筹支持记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ls_jcai_template
-- ----------------------------
DROP TABLE IF EXISTS `ls_jcai_template`;
CREATE TABLE `ls_jcai_template`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `give_type` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '0-\'月\',1-\'季\',\'年\',\'两年\',\'终身\'',
  `money` decimal(10, 2) NOT NULL COMMENT '充值金额',
  `h_money` decimal(10, 2) NOT NULL COMMENT '优惠金额',
  `sort` int(10) NULL DEFAULT NULL COMMENT '排序',
  `is_recommend` tinyint(1) NULL DEFAULT 0 COMMENT '是否推荐：1-是；0-否',
  `create_time` int(10) NOT NULL COMMENT '创建时间',
  `update_time` int(10) NULL DEFAULT NULL COMMENT '更新时间',
  `del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除：1-是；0-否',
  `types` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '类型0：用户集采 1：商家集采',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 19 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '集采会员方案配置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_kefu
-- ----------------------------
DROP TABLE IF EXISTS `ls_kefu`;
CREATE TABLE `ls_kefu`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `shop_id` int(11) NOT NULL DEFAULT 0 COMMENT '门店id',
  `admin_id` int(11) NOT NULL COMMENT '管理员_id',
  `nickname` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '客服昵称',
  `avatar` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '客服头像',
  `disable` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否禁用,0-启用,1-禁用',
  `sort` smallint(5) UNSIGNED NULL DEFAULT 1 COMMENT '排序',
  `create_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  `del` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除[0=否，1=是]',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 74 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '客服表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_kefu_lang
-- ----------------------------
DROP TABLE IF EXISTS `ls_kefu_lang`;
CREATE TABLE `ls_kefu_lang`  (
  `id` int(10) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `shop_id` int(10) NULL DEFAULT 0 COMMENT '0-为平台，非0为门店id',
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '标题',
  `content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '内容',
  `sort` smallint(5) UNSIGNED NULL DEFAULT 1 COMMENT '排序',
  `create_time` int(10) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) NULL DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 256 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '客服话术表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_kefu_session
-- ----------------------------
DROP TABLE IF EXISTS `ls_kefu_session`;
CREATE TABLE `ls_kefu_session`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `kefu_id` int(11) NOT NULL COMMENT '管理员id',
  `shop_id` int(11) NOT NULL DEFAULT 0 COMMENT '非0为商家id，0为平台id',
  `token` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '令牌',
  `client` tinyint(1) NOT NULL COMMENT '客户端类型：[1-微信小程序,2-公众号,3-ios,4-android,5-pc,6-h5]',
  `update_time` int(10) NOT NULL COMMENT '更新时间',
  `expire_time` int(10) NOT NULL COMMENT '到期时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `user_id_client`(`kefu_id`, `client`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 29 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '客服token表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_live_goods
-- ----------------------------
DROP TABLE IF EXISTS `ls_live_goods`;
CREATE TABLE `ls_live_goods`  (
  `id` int(8) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `shop_id` int(10) NOT NULL DEFAULT 0 COMMENT '商家id',
  `source_type` tinyint(1) NULL DEFAULT 0 COMMENT '产品来源 0-商品库 1-自定义',
  `source_id` int(10) NULL DEFAULT 0 COMMENT '商品来源id',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '直播商品名称',
  `price_type` tinyint(10) NOT NULL DEFAULT 1 COMMENT '价格类型，1：一口价（只需要传入price，price2不传） 2：价格区间（price字段为左边界，price2字段为右边界，price和price2必传） 3：显示折扣价（price字段为原价，price2字段为现价， price和price2必传）',
  `price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '价格',
  `price2` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '价格',
  `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商品详情页的小程序路径，路径参数存在 url 的，该参数的值需要进行 encode 处理再填入',
  `cover_img_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商品封面',
  `cover_img` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商品封面',
  `sys_audit_status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '系统审核状态 0-待平台审核 1-待微信审核 2-审核通过 3-审核未通过',
  `wx_audit_status` tinyint(1) NULL DEFAULT 0 COMMENT '微信商品审核状态，0：未审核。1：审核中，2：审核通过，3：审核驳回',
  `wx_audit_id` int(10) NULL DEFAULT 0 COMMENT '微信商品审核id',
  `audit_remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '审核备注',
  `wx_goods_id` int(10) NULL DEFAULT 0 COMMENT '微信商品id',
  `create_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '修改时间',
  `del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除 1-是 0-否',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '直播商品表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_live_room
-- ----------------------------
DROP TABLE IF EXISTS `ls_live_room`;
CREATE TABLE `ls_live_room`  (
  `id` int(8) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `wx_room_id` int(10) NULL DEFAULT 0 COMMENT '微信直播间id',
  `shop_id` int(10) NOT NULL DEFAULT 0 COMMENT '商家id',
  `type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '直播间类型 【1: 推流，0：手机直播】',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '直播间名称',
  `start_time` int(10) NOT NULL COMMENT '开始时间',
  `end_time` int(10) NOT NULL COMMENT '结束时间',
  `anchor_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '主播昵称',
  `anchor_wechat` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '主播微信号',
  `share_img` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '分享图',
  `feeds_img` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '直播封面图',
  `cover_img` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '直播间背景图',
  `share_img_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '分享图-微信media_id',
  `feeds_img_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '封面图-微信media_id',
  `cover_img_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '背景图-微信media_id',
  `is_feeds_public` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否开启官方收录 【0：开启，1：关闭】',
  `close_like` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否关闭点赞 【0：开启，1：关闭】',
  `close_goods` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否关闭货架 【0：开启，1：关闭】',
  `close_comment` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否关闭评论 【0：开启，1：关闭】',
  `close_replay` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否关闭回放 【0：开启，1：关闭】',
  `close_share` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否关闭分享 【0：开启，1：关闭】',
  `close_kf` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否关闭客服 【0：开启，1：关闭】',
  `goods_num` int(10) NULL DEFAULT 0 COMMENT '直播间商品数量',
  `audit_status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '审核状态 【0：待审核 1-审核通过  2-审核未通过】',
  `audit_remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '审核备注',
  `live_status` tinyint(1) NULL DEFAULT 102 COMMENT '直播间状态。101：直播中，102：未开始，103已结束，104禁播，105：暂停，106：异常，107：已过期',
  `sort` int(10) NULL DEFAULT 255 COMMENT '推荐值排序',
  `create_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '修改时间',
  `del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除 1-是 0-否',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '直播间表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_log
-- ----------------------------
DROP TABLE IF EXISTS `ls_log`;
CREATE TABLE `ls_log`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '',
  `log` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
  `creat_time` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1457 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_menu_decorate
-- ----------------------------
DROP TABLE IF EXISTS `ls_menu_decorate`;
CREATE TABLE `ls_menu_decorate`  (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '菜单名称',
  `decorate_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '菜单类型：1-首页导航；2-个人中心',
  `module_type` tinyint(1) NOT NULL COMMENT '模块类型：1-找产品2-找工厂3-集采购',
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '菜单图标',
  `link_type` tinyint(1) NOT NULL COMMENT '链接类型：1-商场模块；2-自定义链接',
  `link_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '链接地址',
  `sort` int(10) NULL DEFAULT 0 COMMENT '菜单排序',
  `is_show` tinyint(1) NULL DEFAULT 1 COMMENT '是否显示；1-是；0-否',
  `create_time` int(10) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) NULL DEFAULT NULL COMMENT '更新时间',
  `del` tinyint(1) NULL DEFAULT 0 COMMENT '是否删除；0-否；1-是',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 39 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '菜单表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_notice
-- ----------------------------
DROP TABLE IF EXISTS `ls_notice`;
CREATE TABLE `ls_notice`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` int(10) UNSIGNED NOT NULL COMMENT '用户id',
  `title` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '标题',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '内容',
  `scene` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '场景',
  `read` tinyint(1) NULL DEFAULT 0 COMMENT '已读状态;0-未读,1-已读',
  `create_time` int(10) NULL DEFAULT NULL COMMENT '创建时间',
  `receive_type` tinyint(1) NULL DEFAULT 0 COMMENT '通知接收对象类型;1-会员;2-商家;3-平台;4-游客(未注册用户)',
  `send_type` tinyint(1) NULL DEFAULT 0 COMMENT '通知发送类型 1-系统通知 2-短信通知 3-微信模板 4-微信小程序',
  `extra` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '其他',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 327 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '通知记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_offline_messages
-- ----------------------------
DROP TABLE IF EXISTS `ls_offline_messages`;
CREATE TABLE `ls_offline_messages`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `to_id` int(11) NOT NULL,
  `to_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `msg` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `msg_type` int(11) NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 357 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_order
-- ----------------------------
DROP TABLE IF EXISTS `ls_order`;
CREATE TABLE `ls_order`  (
  `id` int(10) NOT NULL AUTO_INCREMENT COMMENT '订单id',
  `trade_id` int(11) NULL DEFAULT 0 COMMENT '交易订单id(父订单id)',
  `shop_id` int(10) NOT NULL DEFAULT 0 COMMENT '店铺id',
  `order_sn` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '订单编号',
  `user_id` int(8) UNSIGNED NULL DEFAULT 0 COMMENT '用户id',
  `pickup_code` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '提货码',
  `order_type` tinyint(1) NULL DEFAULT 0 COMMENT '订单类型;0-普通订单',
  `order_source` tinyint(1) NULL DEFAULT 1 COMMENT '订单来源;1-小程序;2-h5;3-ios;4-安卓',
  `order_status` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '订单状态;0-待付款;1-待发货;2-待收货;3-已完成;4-已关闭',
  `pay_status` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '支付状态;0-待支付;1-已支付;2-已退款;3-拒绝退款',
  `delivery_type` tinyint(1) NULL DEFAULT 0 COMMENT '配送方式：0-快递配送 1-虚拟发货 2-线下自提',
  `pay_way` tinyint(2) NULL DEFAULT 1 COMMENT '1-微信支付  2-支付宝支付 3-余额支付  4-线下支付',
  `pay_time` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '支付时间',
  `consignee` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '收货人',
  `province` int(11) UNSIGNED NULL DEFAULT 0 COMMENT '省份',
  `city` int(11) UNSIGNED NULL DEFAULT 0 COMMENT '城市',
  `district` int(11) UNSIGNED NULL DEFAULT 0 COMMENT '县区',
  `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '地址',
  `mobile` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '手机',
  `goods_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '订单商品总价',
  `order_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '应付款金额',
  `discount_amount` decimal(10, 2) UNSIGNED NULL DEFAULT 0.00 COMMENT '优惠金额',
  `total_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '订单总价',
  `total_num` int(10) NULL DEFAULT 0 COMMENT '订单商品数量',
  `shipping_status` tinyint(1) UNSIGNED NULL DEFAULT 0 COMMENT '发货状态',
  `shipping_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '运费',
  `shipping_time` int(11) NULL DEFAULT 0 COMMENT '最后新发货时间',
  `wechat_mini_express_sync` tinyint(3) UNSIGNED NULL DEFAULT 0 COMMENT '微信小程序发货信息录入',
  `wechat_mini_express_sync_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '录入时间',
  `transaction_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '第三方平台交易流水号',
  `hfdg_params` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '汇付斗拱记录参数',
  `user_remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '用户备注',
  `confirm_take_time` int(10) NULL DEFAULT NULL COMMENT '确认收货时间',
  `cancel_time` int(10) NULL DEFAULT NULL COMMENT '订单取消时间',
  `refund_status` tinyint(1) NULL DEFAULT 0 COMMENT '退款状态：0-未退款；1-部分退款；2-全部退款',
  `settle_id` int(11) NULL DEFAULT 0 COMMENT '结算id；0-未结算',
  `settle_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '结算金额',
  `refund_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '退款金额',
  `platform_commission` decimal(10, 2) NULL DEFAULT NULL COMMENT '平台抽成',
  `coupon_list_id` int(10) NULL DEFAULT NULL COMMENT '用户优惠券id',
  `order_remarks` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '订单备注',
  `is_comment` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '标识子订单是否已评价\r\n0-所有子订单未评价\r\n1-部分子订单已评价\r\n2-所有子订单已评价',
  `distribution_money` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '佣金金额',
  `is_cancel` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '标识子订单是否是直接退款，0表示售后退款或者未退款，1表示直接退款',
  `member_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '会员价优惠金额',
  `award_integral_status` tinyint(1) NULL DEFAULT 0 COMMENT '默认为0-不赠送积分；1-订单付款时赠送积分;2-订单发货时赠送积分;3-订单完成时赠送积分;4-订单完成并且超过售后期赠送积分;',
  `award_integral` int(10) NULL DEFAULT 0 COMMENT '赠送积分数量',
  `is_award_integral` tinyint(1) NOT NULL DEFAULT 0 COMMENT '积分是否已结算:0-否;1-是;',
  `delivery_content` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '虚拟商品-发货内容',
  `delivery_id` int(11) NULL DEFAULT 0 COMMENT '发货单ID',
  `verification_status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '核销状态:0-待核销;1-已核销;',
  `create_time` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '下单时间',
  `update_time` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '更新时间',
  `delete` tinyint(1) NULL DEFAULT 0 COMMENT '商家删除：1-删除；0-未删除',
  `del` tinyint(1) NULL DEFAULT 0 COMMENT '删除标识;1-删除;0-未删除',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `order_sn`(`order_sn`) USING BTREE,
  INDEX `user_id`(`user_id`) USING BTREE,
  INDEX `idx_userid_orderstatus_del`(`user_id`, `order_status`, `del`) USING BTREE,
  INDEX `idx_tradeid`(`trade_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 287 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '订单表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_order_goods
-- ----------------------------
DROP TABLE IF EXISTS `ls_order_goods`;
CREATE TABLE `ls_order_goods`  (
  `id` int(10) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `shop_id` int(11) NULL DEFAULT NULL COMMENT '店铺id',
  `order_id` int(10) NULL DEFAULT 0 COMMENT '订单id',
  `goods_id` int(10) NULL DEFAULT 0 COMMENT '商品id',
  `item_id` int(10) NULL DEFAULT 0 COMMENT '规格id',
  `goods_num` int(10) NULL DEFAULT 0 COMMENT '商品数量',
  `goods_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品名称',
  `goods_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '商品价格',
  `total_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '商品总价',
  `total_pay_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '实际支付商品金额',
  `discount_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '优惠金额',
  `spec_value` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品规格值',
  `spec_value_ids` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品规格id',
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品主图',
  `weight` decimal(10, 3) UNSIGNED NULL DEFAULT NULL COMMENT '重量',
  `shipping_status` tinyint(1) NULL DEFAULT 0 COMMENT '0-未发货;1-已发货',
  `delivery_id` int(11) NULL DEFAULT 0 COMMENT '发货单ID',
  `refund_status` tinyint(1) NULL DEFAULT 0 COMMENT '售后状态;0-未申请退款;1-申请退款;2-等待退款;3-退款成功;',
  `is_comment` tinyint(1) NULL DEFAULT 0 COMMENT '是否已评论；0-否；1-是',
  `commission_ratio` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '抽成比例',
  `create_time` int(10) NULL DEFAULT 0 COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_orderid_iscomment`(`order_id`, `is_comment`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 321 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '订单商品表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_order_invoice
-- ----------------------------
DROP TABLE IF EXISTS `ls_order_invoice`;
CREATE TABLE `ls_order_invoice`  (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `shop_id` int(10) NOT NULL DEFAULT 0 COMMENT '店铺id',
  `user_id` int(10) NOT NULL DEFAULT 0 COMMENT '用户id',
  `order_id` int(10) NOT NULL DEFAULT 0 COMMENT '订单id',
  `type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '发票类型 0-普通 1-专用',
  `header_type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '抬头类型 0-个人 1-企业',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '发票抬头名称',
  `duty_number` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '税号',
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '开票人邮箱',
  `mobile` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '企业电话',
  `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '企业地址',
  `bank` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '开户行',
  `bank_account` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '银行账号',
  `invoice_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '发票金额',
  `invoice_number` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '开票票号',
  `status` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '开票状态 0-未开票 1-已开票',
  `invoice_time` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '开票时间',
  `create_time` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单发票表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_order_log
-- ----------------------------
DROP TABLE IF EXISTS `ls_order_log`;
CREATE TABLE `ls_order_log`  (
  `id` int(10) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `type` tinyint(1) NULL DEFAULT 0 COMMENT '操作类型;0-会员;1-门店',
  `channel` smallint(5) UNSIGNED NULL DEFAULT 0 COMMENT '渠道编号。变动方式。',
  `order_id` int(10) NULL DEFAULT NULL COMMENT '订单id',
  `handle_id` int(10) NULL DEFAULT NULL COMMENT '操作人id',
  `shop_id` int(10) NULL DEFAULT NULL COMMENT '门店id',
  `content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '日志内容',
  `create_time` int(10) NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 504 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单日志表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_order_refund
-- ----------------------------
DROP TABLE IF EXISTS `ls_order_refund`;
CREATE TABLE `ls_order_refund`  (
  `id` int(6) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `order_id` int(1) UNSIGNED NULL DEFAULT 0 COMMENT '订单id',
  `user_id` mediumint(8) UNSIGNED NOT NULL DEFAULT 0 COMMENT '下单用户id，冗余字段',
  `refund_sn` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '退款单号，一个订单分多次退款则有多个退款单号',
  `order_amount` decimal(10, 2) UNSIGNED NOT NULL COMMENT '订单总的应付款金额，冗余字段',
  `refund_amount` decimal(10, 2) UNSIGNED NULL DEFAULT NULL COMMENT '本次退款金额',
  `transaction_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '第三方平台交易流水号',
  `hfdg_params` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '汇付斗拱记录参数',
  `refund_status` tinyint(1) UNSIGNED NULL DEFAULT 0 COMMENT '退款状态，0退款中，1完成退款，2退款失败，3退款异常（人工去后台查询）',
  `refund_way` tinyint(1) UNSIGNED NULL DEFAULT 0 COMMENT '退款方式，0原路退',
  `refund_at` int(1) UNSIGNED NULL DEFAULT 0 COMMENT '退款时间',
  `wechat_refund_id` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '微信返回退款id',
  `refund_msg` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '微信返回信息',
  `create_time` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(10) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 16 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单退款表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_order_trade
-- ----------------------------
DROP TABLE IF EXISTS `ls_order_trade`;
CREATE TABLE `ls_order_trade`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `t_sn` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '订单编号',
  `user_id` int(10) NULL DEFAULT 0 COMMENT '用户id',
  `goods_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '订单商品总价',
  `order_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '应付款金额',
  `total_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '订单总价',
  `discount_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '优惠金额',
  `transaction_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '第三方平台交易流水号',
  `hfdg_params` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '汇付斗拱记录参数',
  `create_time` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '下单时间',
  `shop_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `order_sn`(`t_sn`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 276 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '总订单' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_printer
-- ----------------------------
DROP TABLE IF EXISTS `ls_printer`;
CREATE TABLE `ls_printer`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `shop_id` int(11) NOT NULL DEFAULT 0 COMMENT '商家id',
  `config_id` tinyint(1) NOT NULL COMMENT '配置id',
  `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '打印机名称',
  `machine_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '终端号',
  `private_key` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '秘钥',
  `print_number` tinyint(1) NOT NULL DEFAULT 1 COMMENT '打印联数：默认1张',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '打印机状态：1开启；0-关闭',
  `auto_print` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否自动打印：1-是；0-否',
  `create_time` int(11) NOT NULL COMMENT '创建时间',
  `update_time` int(11) NULL DEFAULT NULL COMMENT '更新时间',
  `del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '删除状态：1-是；0-否',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '打印机列表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_printer_config
-- ----------------------------
DROP TABLE IF EXISTS `ls_printer_config`;
CREATE TABLE `ls_printer_config`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '打印机配置名称',
  `shop_id` int(11) NOT NULL DEFAULT 0 COMMENT '商家id',
  `client_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '应用id',
  `client_secret` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '应用秘钥',
  `type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '类型：1-易联云',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否开启',
  `update_time` int(11) NULL DEFAULT NULL COMMENT '更新时间',
  `del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '删除状态：1-是；0-否',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '打印配置' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_product_word
-- ----------------------------
DROP TABLE IF EXISTS `ls_product_word`;
CREATE TABLE `ls_product_word`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `goods_id` int(11) NOT NULL COMMENT '商品ID',
  `word_id` int(11) NOT NULL COMMENT '分词ID',
  `created_at` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uniq_product_word`(`goods_id`, `word_id`) USING BTREE,
  INDEX `idx_word_id`(`word_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 10818 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品分词关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_recharge_order
-- ----------------------------
DROP TABLE IF EXISTS `ls_recharge_order`;
CREATE TABLE `ls_recharge_order`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `order_sn` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '订单编号',
  `transaction_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '第三方平台交易流水号',
  `hfdg_params` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '汇付斗拱记录参数',
  `order_source` tinyint(1) NULL DEFAULT 1 COMMENT '订单来源：1-小程序;2-h5;3-ios;4-安卓',
  `pay_way` tinyint(2) NOT NULL DEFAULT 1 COMMENT '支付方式：1-微信支付  2-支付宝支付 3-余额支付',
  `pay_status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '支付状态：0-待支付；1-已支付',
  `pay_time` int(10) NULL DEFAULT NULL COMMENT '支付时间',
  `user_id` int(11) NOT NULL COMMENT '用户id',
  `template_id` int(11) NULL DEFAULT NULL COMMENT '模板id',
  `order_amount` decimal(10, 2) NOT NULL COMMENT '充值金额',
  `give_money` decimal(10, 2) NULL DEFAULT NULL COMMENT '赠送金额',
  `give_integral` int(10) NULL DEFAULT NULL COMMENT '赠送积分',
  `give_growth` int(10) NULL DEFAULT NULL COMMENT '赠送成长值',
  `wechat_mini_express_sync` tinyint(3) UNSIGNED NULL DEFAULT 0 COMMENT '微信小程序发货信息录入',
  `wechat_mini_express_sync_time` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '录入时间',
  `create_time` int(10) NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 14 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '充值订单表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_recharge_template
-- ----------------------------
DROP TABLE IF EXISTS `ls_recharge_template`;
CREATE TABLE `ls_recharge_template`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `money` decimal(10, 2) NOT NULL COMMENT '充值金额',
  `give_money` decimal(10, 2) NULL DEFAULT NULL COMMENT '赠送金额',
  `sort` int(10) NULL DEFAULT NULL COMMENT '排序',
  `is_recommend` tinyint(1) NULL DEFAULT 0 COMMENT '是否推荐：1-是；0-否',
  `create_time` int(10) NOT NULL COMMENT '创建时间',
  `update_time` int(10) NULL DEFAULT NULL COMMENT '更新时间',
  `del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除：1-是；0-否',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '充值模板表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_role
-- ----------------------------
DROP TABLE IF EXISTS `ls_role`;
CREATE TABLE `ls_role`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `name` varchar(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '父级id',
  `auth_ids` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '权限',
  `desc` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '描述',
  `del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除：0-否；1-是；',
  `create_time` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '平台角色表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_role_auth_index
-- ----------------------------
DROP TABLE IF EXISTS `ls_role_auth_index`;
CREATE TABLE `ls_role_auth_index`  (
  `role_id` int(11) NOT NULL COMMENT '角色id',
  `menu_auth_id` int(11) NOT NULL COMMENT '菜单权限id',
  PRIMARY KEY (`role_id`, `menu_auth_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '平台角色与权限关系表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_search_record
-- ----------------------------
DROP TABLE IF EXISTS `ls_search_record`;
CREATE TABLE `ls_search_record`  (
  `id` int(20) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) UNSIGNED NOT NULL COMMENT '用户id',
  `keyword` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '关键字',
  `update_time` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `count` int(11) UNSIGNED NOT NULL DEFAULT 1 COMMENT '次数',
  `del` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否已删除,0-未删除,1-已删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 300 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '搜索关键字表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_seckill_goods
-- ----------------------------
DROP TABLE IF EXISTS `ls_seckill_goods`;
CREATE TABLE `ls_seckill_goods`  (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `seckill_id` int(10) NOT NULL COMMENT '秒杀时间id',
  `goods_id` int(10) NOT NULL COMMENT '参与秒杀商品的id',
  `item_id` int(11) NULL DEFAULT NULL COMMENT '参与秒杀规格id',
  `price` decimal(10, 2) NULL DEFAULT NULL COMMENT '秒杀活动价',
  `sales_sum` int(10) NULL DEFAULT 0 COMMENT '销量',
  `create_time` int(10) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) NULL DEFAULT NULL COMMENT '更新时间',
  `del` tinyint(1) NULL DEFAULT NULL COMMENT '是否删除；1-是；0-否',
  `shop_id` int(10) NULL DEFAULT NULL COMMENT '店铺id',
  `review_status` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '审核状态 0-待审核 1-审核通过 2-审核拒绝',
  `review_desc` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '审核说明',
  `start_date` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '开始日期',
  `end_date` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '结束日期',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '限时秒杀商品表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_seckill_time
-- ----------------------------
DROP TABLE IF EXISTS `ls_seckill_time`;
CREATE TABLE `ls_seckill_time`  (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `start_time` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '开始时间',
  `end_time` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '结束时间',
  `create_time` int(10) NOT NULL COMMENT '创建时间',
  `update_time` int(10) NULL DEFAULT NULL COMMENT '更新时间',
  `del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '删除时间；1-是；0-否',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '秒杀时段表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_session
-- ----------------------------
DROP TABLE IF EXISTS `ls_session`;
CREATE TABLE `ls_session`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `user_id` int(11) NOT NULL COMMENT '用户id',
  `token` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '令牌',
  `client` tinyint(1) NOT NULL COMMENT '客户端类型：1-微信小程序；2-h5；3-ios；4-android',
  `update_time` int(10) NOT NULL COMMENT '更新时间',
  `expire_time` int(10) NOT NULL COMMENT '到期时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `user_id_client`(`user_id`, `client`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 68 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '会话表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_shop
-- ----------------------------
DROP TABLE IF EXISTS `ls_shop`;
CREATE TABLE `ls_shop`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `cid` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '主营类目',
  `type` tinyint(1) NOT NULL DEFAULT 2 COMMENT '商家类型[1=官方自营, 2=入驻商家]',
  `tier_level` tinyint(1) NOT NULL DEFAULT 0 COMMENT '商家等级：0=0元入驻，1=商家会员，2=实力厂商',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商家名称',
  `nickname` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '联系人名称',
  `mobile` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '联系电话',
  `wallet` decimal(8, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '商家钱包(可提现)',
  `logo` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商家LOGO',
  `background` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '背景图',
  `license` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '资质图片',
  `keywords` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商家关键词,逗号分隔',
  `intro` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '商家简介',
  `weight` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '商品权重[数字约大,权重越大]',
  `trade_service_fee` decimal(8, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '每笔交易服务费,百分比计算,体现时收取,[0=不收取手续费]',
  `shop_label` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
  `weekdays` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '工作日，逗号隔开如 1,2,3,4,5,6,0',
  `service_mobile` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '客服电话',
  `province_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '省ID',
  `city_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '市ID',
  `district_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '区ID',
  `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '详细地址',
  `longitude` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '经度',
  `latitude` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '纬度',
  `refund_address` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '退款地址，JSON格式',
  `run_start_time` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '营业时间',
  `run_end_time` int(10) NULL DEFAULT NULL COMMENT '营业结束时间',
  `is_run` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '营业状态[0=暂停营业, 1=营业中]',
  `is_freeze` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '商家状态[0=正常, 1=冻结]',
  `is_product_audit` tinyint(1) UNSIGNED NOT NULL COMMENT '产品审核[0=不审核, 1=审核]',
  `is_recommend` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '推荐商家,会在首页显示[0=否, 1=是]',
  `del` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除[0=否, 1=是]',
  `expire_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '到期时间',
  `tier_expire_time` int(11) NOT NULL DEFAULT 0 COMMENT '等级到期时间',
  `tier_upgrade_time` int(11) NOT NULL DEFAULT 0 COMMENT '等级升级时间',
  `create_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  `score` float UNSIGNED NOT NULL DEFAULT 0 COMMENT '店铺评分',
  `star` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '店铺评级',
  `visited_num` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '进店人数',
  `is_distribution` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否允许分销 0-不允许(默认) 1-允许',
  `is_pay` tinyint(1) UNSIGNED NOT NULL DEFAULT 1 COMMENT '是否开启支付功能  0-关闭 1-开启',
  `cover` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'PC端店铺封面图',
  `banner` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'PC端店铺头图',
  `business_license` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '营业执照',
  `other_qualifications` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '其他资质',
  `open_invoice` tinyint(1) UNSIGNED NOT NULL DEFAULT 1 COMMENT '发票开关 0- 关闭 1-开启',
  `spec_invoice` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否支持专票 0-不支持 1-支持',
  `delivery_type` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '1' COMMENT '配送方式 1-快递配置 2-线下自提',
  `jcshop_vip` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否是集采商家会员即是否缴纳保证金',
  `yan_level` int(11) NOT NULL DEFAULT 0 COMMENT '检验标签同shop_level表id',
  `yan_time` int(11) NULL DEFAULT 0 COMMENT '检验标签显示时长0-未核验',
  `yan_fee` tinyint(1) NULL DEFAULT 0 COMMENT '是否已缴纳检验费',
  `entry_time` int(11) NOT NULL DEFAULT 0 COMMENT '入驻时长0-未缴入驻费',
  `return_rate` float NULL DEFAULT NULL COMMENT '回头率',
  `shopvip_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '入驻截止日期，即交入驻费时间往后延期',
  `year_sales` int(5) NOT NULL DEFAULT 0 COMMENT '年销量',
  `total_sales` int(5) NOT NULL DEFAULT 0 COMMENT '总销量',
  `videos` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '商家视频',
  `f_user` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '分配检验人员id',
  `follow_nums` int(5) NOT NULL DEFAULT 0 COMMENT '收藏人数',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 159 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '商家表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_shop_account_log
-- ----------------------------
DROP TABLE IF EXISTS `ls_shop_account_log`;
CREATE TABLE `ls_shop_account_log`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `log_sn` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '流水单号（20位）',
  `shop_id` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '商家ID',
  `source_type` smallint(5) UNSIGNED NOT NULL DEFAULT 100 COMMENT '来源类型',
  `source_id` int(11) UNSIGNED NULL DEFAULT 0 COMMENT '来源id',
  `source_sn` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '来源单号',
  `change_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '变动总数',
  `left_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '剩余总数',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '说明',
  `extra` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '额外的字段说明',
  `change_type` tinyint(1) UNSIGNED NULL DEFAULT 0 COMMENT '1-增加；2-减少',
  `create_time` int(10) UNSIGNED NOT NULL COMMENT '变动时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '商家流水表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_shop_ad
-- ----------------------------
DROP TABLE IF EXISTS `ls_shop_ad`;
CREATE TABLE `ls_shop_ad`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `create_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  `del` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除[0=否, 1=是]',
  `shop_id` int(11) NOT NULL DEFAULT 0,
  `ad_order_id` int(11) NOT NULL COMMENT '广告购买ID',
  `title` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '广告标题',
  `place` tinyint(3) UNSIGNED NOT NULL COMMENT '位置 1店铺首页',
  `terminal` tinyint(3) UNSIGNED NOT NULL COMMENT '终端 PC 移动',
  `image` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '图片',
  `link` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '链接',
  `sort` int(10) UNSIGNED NOT NULL DEFAULT 255 COMMENT '排序',
  `status` tinyint(3) UNSIGNED NOT NULL COMMENT '状态1-启用 0-停用',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '商家广告表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_shop_admin
-- ----------------------------
DROP TABLE IF EXISTS `ls_shop_admin`;
CREATE TABLE `ls_shop_admin`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `root` tinyint(1) NULL DEFAULT 1 COMMENT '0-非超级管理员；1-超级管理；',
  `shop_id` int(11) NOT NULL DEFAULT 0 COMMENT '商家id',
  `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '名称',
  `account` varchar(34) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '账号',
  `password` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '密码',
  `salt` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '密码盐',
  `role_id` int(11) NOT NULL DEFAULT 0 COMMENT '角色id',
  `create_time` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '修改时间',
  `login_time` int(10) NULL DEFAULT NULL COMMENT '最后登录时间',
  `login_ip` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '最后登录ip',
  `disable` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否禁用：0-否；1-是；',
  `del` tinyint(10) NOT NULL DEFAULT 0 COMMENT '0为非删除状态，非0位删除时间',
  `unionid` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '微信unionid',
  `oa_openid` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '微信公众号openid',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `account`(`account`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 147 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '商家管理员表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_shop_admin_auth
-- ----------------------------
DROP TABLE IF EXISTS `ls_shop_admin_auth`;
CREATE TABLE `ls_shop_admin_auth`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `admin_id` int(11) NOT NULL COMMENT '管理员id',
  `shop_id` int(11) NOT NULL COMMENT '商家id',
  `openid` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '微信openid',
  `unionid` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '微信unionid',
  `create_time` int(10) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) NULL DEFAULT NULL COMMENT '更新时间',
  `client` tinyint(1) NOT NULL COMMENT '客户端类型：1-微信小程序；2-h5；3-ios；4-android',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `openid`(`openid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 17 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商家管理员微信授权表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ls_shop_alipay
-- ----------------------------
DROP TABLE IF EXISTS `ls_shop_alipay`;
CREATE TABLE `ls_shop_alipay`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `shop_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '商家ID',
  `account` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '支付宝账号',
  `username` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '姓名',
  `del` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除[0=否, 1=是]',
  `create_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '商家支付宝账号' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_shop_apply
-- ----------------------------
DROP TABLE IF EXISTS `ls_shop_apply`;
CREATE TABLE `ls_shop_apply`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `cid` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '主营类目',
  `target_tier_level` tinyint(1) NOT NULL DEFAULT 0 COMMENT '目标等级：0=0元入驻，1=商家会员，2=实力厂商',
  `is_prepaid` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否预付费：0=未支付，1=已支付',
  `user_id` int(10) UNSIGNED NOT NULL COMMENT '用户ID',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商家名称',
  `nickname` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '联系人名称',
  `mobile` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '联系电话',
  `account` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商家账号',
  `password` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '登录密码',
  `license` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '资质图片',
  `audit_explain` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '审核说明',
  `audit_status` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '审核状态[1=待审核, 2=审核通过, 3=审核拒绝]',
  `del` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除[0=否, 1=是]',
  `apply_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '申请时间',
  `create_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 38 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '商家入驻申请表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_shop_bank
-- ----------------------------
DROP TABLE IF EXISTS `ls_shop_bank`;
CREATE TABLE `ls_shop_bank`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `shop_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '商家ID',
  `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '提现银行',
  `branch` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '银行支行',
  `nickname` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '开户名称',
  `account` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '银行卡账号',
  `del` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除[0=否, 1=是]',
  `create_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '商家银行账号表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_shop_category
-- ----------------------------
DROP TABLE IF EXISTS `ls_shop_category`;
CREATE TABLE `ls_shop_category`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '类目名称',
  `image` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '类目图标',
  `sort` smallint(5) NOT NULL DEFAULT 0 COMMENT '排序,数字越大越靠前',
  `del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除[0=否, 1=是]',
  `create_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 16 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '主营类目表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_shop_deactivate_apply
-- ----------------------------
DROP TABLE IF EXISTS `ls_shop_deactivate_apply`;
CREATE TABLE `ls_shop_deactivate_apply`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `shop_id` int(11) UNSIGNED NOT NULL COMMENT '店铺ID',
  `admin_id` int(11) UNSIGNED NOT NULL COMMENT '管理员ID',
  `reason` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '注销原因',
  `check_result` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '注销条件检查结果',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '状态：0待审核 1已通过 2已拒绝',
  `audit_time` int(11) UNSIGNED NULL DEFAULT NULL COMMENT '审核时间',
  `audit_remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '审核备注',
  `execute_time` int(11) UNSIGNED NULL DEFAULT NULL COMMENT '执行注销时间',
  `execute_result` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '执行注销结果',
  `create_time` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `ip` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'IP地址',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_shop_id`(`shop_id`) USING BTREE,
  INDEX `idx_admin_id`(`admin_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '店铺注销申请表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ls_shop_deposit
-- ----------------------------
DROP TABLE IF EXISTS `ls_shop_deposit`;
CREATE TABLE `ls_shop_deposit`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_sn` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '订单sn',
  `transaction_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '微信支付交易号',
  `type` tinyint(1) NULL DEFAULT 0 COMMENT '0:缴纳保证1:补缴',
  `user_id` int(11) NOT NULL DEFAULT 0,
  `shop_id` int(11) NOT NULL,
  `deposit_amount` decimal(10, 2) NOT NULL,
  `payment_method` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `payment_date` datetime NOT NULL,
  `docs` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '文件',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `status` tinyint(1) NOT NULL COMMENT '0待审核 1已通过 2未通过',
  `pay_status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '0未支付1已支付2退款中3已退款',
  `refund_status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '退款状态：0未申请 1申请中 2已退款 3拒绝退款',
  `refund_apply_time` datetime NULL DEFAULT NULL COMMENT '退款申请时间',
  `refund_reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '退款申请原因',
  `refund_time` datetime NULL DEFAULT NULL COMMENT '退款时间',
  `refund_remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '退款备注',
  `refund_admin_id` int(11) NULL DEFAULT NULL COMMENT '处理退款的管理员ID',
  `refund_publicity_start_time` datetime NULL DEFAULT NULL COMMENT '退款公示期开始时间',
  `refund_publicity_end_time` datetime NULL DEFAULT NULL COMMENT '退款公示期结束时间',
  `images` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `wechat_mini_express_sync` tinyint(1) NULL DEFAULT 0 COMMENT '微信小程序发货信息录入：0-未录入；1-已录入；2-录入失败',
  `wechat_mini_express_sync_time` int(11) NULL DEFAULT 0 COMMENT '微信发货录入时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 34 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_shop_deposit_details
-- ----------------------------
DROP TABLE IF EXISTS `ls_shop_deposit_details`;
CREATE TABLE `ls_shop_deposit_details`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '记录的唯一标识符',
  `shop_id` int(11) NOT NULL COMMENT '商家的唯一标识符',
  `sn` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `deposit_id` int(11) NOT NULL COMMENT '商家保证金表id',
  `deposit_change` decimal(10, 2) NOT NULL COMMENT '保证金变动金额（正数表示补缴，负数表示扣除）',
  `change_type` tinyint(4) NULL DEFAULT NULL COMMENT '1-缴纳 2-增加 3-扣除 4-退还',
  `amount` decimal(10, 2) NULL DEFAULT NULL,
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '保证金变动原因',
  `change_date` date NOT NULL COMMENT '变动日期',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录最后更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商家保证金明细表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_shop_follow
-- ----------------------------
DROP TABLE IF EXISTS `ls_shop_follow`;
CREATE TABLE `ls_shop_follow`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `user_id` int(10) UNSIGNED NOT NULL COMMENT '用户id',
  `shop_id` int(10) NOT NULL COMMENT '店铺id',
  `status` tinyint(1) NOT NULL COMMENT '关注状态 0-取消关注 1-关注',
  `create_time` int(10) UNSIGNED NOT NULL COMMENT '创建时间',
  `update_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '店铺关注表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_shop_footprint
-- ----------------------------
DROP TABLE IF EXISTS `ls_shop_footprint`;
CREATE TABLE `ls_shop_footprint`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `user_id` int(10) UNSIGNED NOT NULL COMMENT '用户id',
  `nums` int(11) NOT NULL COMMENT '进店次数',
  `shop_id` int(10) NOT NULL COMMENT '店铺id',
  `create_time` int(10) UNSIGNED NOT NULL COMMENT '创建时间',
  `update_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 192 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '店铺足迹表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_shop_goods_category
-- ----------------------------
DROP TABLE IF EXISTS `ls_shop_goods_category`;
CREATE TABLE `ls_shop_goods_category`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `name` varchar(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '分类名称',
  `sort` int(11) UNSIGNED NULL DEFAULT 255 COMMENT '排序',
  `is_show` tinyint(1) UNSIGNED NULL DEFAULT 1 COMMENT '是否显示:1-是;0-否',
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '分类图标',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '分类描述',
  `create_time` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `update_time` int(10) NULL DEFAULT NULL COMMENT '更新时间',
  `del` tinyint(10) UNSIGNED NULL DEFAULT 0 COMMENT '删除标志:1-是；0-否',
  `shop_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '商家id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '店铺商品分类表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_shop_icon_config
-- ----------------------------
DROP TABLE IF EXISTS `ls_shop_icon_config`;
CREATE TABLE `ls_shop_icon_config`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `shop_id` int(11) NOT NULL DEFAULT 0 COMMENT '商家ID',
  `icon_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '图标URL',
  `icon_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '图标名称',
  `icon_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '图标路径/跳转链接',
  `auth_id` int(11) NOT NULL DEFAULT 0 COMMENT '关联权限ID(ls_dev_shop_auth表)',
  `merchant_types` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0,1,2' COMMENT '允许访问的商家类型：0=0元入驻,1=商家会员,2=实力厂商，多个用逗号分隔',
  `sort_order` int(11) NOT NULL DEFAULT 50 COMMENT '排序字段，数值越小越靠前',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  `created_at` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '创建时间',
  `updated_at` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_shop_id`(`shop_id`) USING BTREE,
  INDEX `idx_auth_id`(`auth_id`) USING BTREE,
  INDEX `idx_status_sort`(`status`, `sort_order`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 34 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商家功能图标配置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ls_shop_industry_data
-- ----------------------------
DROP TABLE IF EXISTS `ls_shop_industry_data`;
CREATE TABLE `ls_shop_industry_data`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `data_date` date NOT NULL COMMENT '数据日期',
  `category_id` int(11) UNSIGNED NOT NULL COMMENT '行业/商品分类ID',
  `metric_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '指标类型 (例如: total_sales_volume, average_order_value, visitor_count)',
  `metric_value` decimal(15, 2) NOT NULL COMMENT '指标值',
  `region_id` int(11) UNSIGNED NULL DEFAULT NULL COMMENT '地区ID (可选, 用于区域分析)',
  `create_time` int(10) UNSIGNED NOT NULL COMMENT '创建时间',
  `update_time` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_data_date`(`data_date`) USING BTREE,
  INDEX `idx_category_id`(`category_id`) USING BTREE,
  INDEX `idx_metric_type`(`metric_type`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商家行业数据表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_shop_inspection
-- ----------------------------
DROP TABLE IF EXISTS `ls_shop_inspection`;
CREATE TABLE `ls_shop_inspection`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `shop_id` int(11) NOT NULL COMMENT '商家ID',
  `staff_id` int(11) NOT NULL COMMENT '检验人员ID',
  `images` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '图片列表，JSON格式存储',
  `videos` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '视频列表，JSON格式存储',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '检验说明',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '检验状态：0-待审核；1-已通过；2-未通过',
  `reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '未通过原因',
  `create_time` int(11) NOT NULL COMMENT '创建时间（上传时间）',
  `update_time` int(11) NOT NULL COMMENT '更新时间',
  `audit_time` int(11) NULL DEFAULT NULL COMMENT '审核时间',
  `audit_user_id` int(11) NULL DEFAULT NULL COMMENT '审核人ID',
  `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除：0-否；1-是',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_shop_id`(`shop_id`) USING BTREE,
  INDEX `idx_staff_id`(`staff_id`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_create_time`(`create_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商家实地检验记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ls_shop_level
-- ----------------------------
DROP TABLE IF EXISTS `ls_shop_level`;
CREATE TABLE `ls_shop_level`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '名称',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '等级备注',
  `background_image` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '背景图片',
  `image` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '等级图标',
  `privilege` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '等级权益',
  `discount` decimal(8, 2) UNSIGNED NULL DEFAULT NULL COMMENT '年费',
  `hdiscount` decimal(8, 2) UNSIGNED NULL DEFAULT NULL COMMENT '和入驻一起的年费',
  `create_time` int(10) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) NULL DEFAULT NULL COMMENT '更新时间',
  `del` tinyint(10) NULL DEFAULT 0 COMMENT '是否删除;1-是；0-否',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商户等级表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_shop_merchantfees
-- ----------------------------
DROP TABLE IF EXISTS `ls_shop_merchantfees`;
CREATE TABLE `ls_shop_merchantfees`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `shop_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL COMMENT '缴费人id',
  `order_sn` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '订单编码',
  `amount` decimal(10, 2) NOT NULL,
  `feetype` tinyint(1) NOT NULL COMMENT '0入驻费1高级入驻包含验厂',
  `payment_date` int(11) NOT NULL DEFAULT 0,
  `status` tinyint(1) NOT NULL COMMENT '0未支付1已支付2退款中3已退款',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `wechat_mini_express_sync` tinyint(1) NULL DEFAULT 0 COMMENT '微信小程序发货信息录入：0-未录入；1-已录入；2-录入失败',
  `wechat_mini_express_sync_time` int(11) NULL DEFAULT 0 COMMENT '微信发货录入时间',
  `tier_level` tinyint(1) NULL DEFAULT 0 COMMENT '目标等级：0=0元入驻,1=商家会员,2=实力厂商',
  `tier_type` tinyint(1) NULL DEFAULT 0 COMMENT '等级类型：0=新入驻，1=升级，2=续费',
  `transaction_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '微信交易号',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 503 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商家入驻费检验费表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_shop_operation_log
-- ----------------------------
DROP TABLE IF EXISTS `ls_shop_operation_log`;
CREATE TABLE `ls_shop_operation_log`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `shop_id` int(11) UNSIGNED NOT NULL COMMENT '店铺ID',
  `admin_id` int(11) UNSIGNED NOT NULL COMMENT '管理员ID',
  `action` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '操作行为',
  `ip` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'IP地址',
  `create_time` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `data` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '操作数据',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_shop_id`(`shop_id`) USING BTREE,
  INDEX `idx_admin_id`(`admin_id`) USING BTREE,
  INDEX `idx_create_time`(`create_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '店铺操作日志表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ls_shop_role
-- ----------------------------
DROP TABLE IF EXISTS `ls_shop_role`;
CREATE TABLE `ls_shop_role`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `shop_id` int(11) NOT NULL DEFAULT 0 COMMENT '商家id',
  `name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '父级id',
  `auth_ids` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '权限',
  `desc` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '描述',
  `del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除：0-否；1-是；',
  `create_time` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '商家角色表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_shop_role_auth_index
-- ----------------------------
DROP TABLE IF EXISTS `ls_shop_role_auth_index`;
CREATE TABLE `ls_shop_role_auth_index`  (
  `role_id` int(11) NOT NULL COMMENT '角色id',
  `menu_auth_id` int(11) NOT NULL COMMENT '菜单权限id',
  PRIMARY KEY (`role_id`, `menu_auth_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '商家角色与权限表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_shop_session
-- ----------------------------
DROP TABLE IF EXISTS `ls_shop_session`;
CREATE TABLE `ls_shop_session`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `shop_id` int(11) NOT NULL DEFAULT 0 COMMENT '商家id',
  `admin_id` int(11) NOT NULL COMMENT '管理员id',
  `token` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '令牌',
  `client` tinyint(1) NOT NULL COMMENT '客户端类型：1-微信小程序，2-oa，3-ios，4-android，5-pc，6-h5(非微信环境)',
  `update_time` int(10) NOT NULL COMMENT '更新时间',
  `expire_time` int(10) NOT NULL COMMENT '到期时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `user_id_client`(`admin_id`, `client`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 25 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商家移动端登录凭证表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_shop_settlement
-- ----------------------------
DROP TABLE IF EXISTS `ls_shop_settlement`;
CREATE TABLE `ls_shop_settlement`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `shop_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '商户ID',
  `settle_sn` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '结算编号',
  `deal_order_count` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '已结算成交订单数',
  `business_money` decimal(8, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '已结算营业额',
  `refund_order_money` decimal(8, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '退款订单金额',
  `after_sales_money` decimal(8, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '售后退款金额',
  `distribution_money` decimal(8, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '已结算分销佣金金额',
  `platform_commission_money` decimal(8, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '平台佣金金额',
  `entry_account_money` decimal(8, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '已结算入账金额',
  `trade_service_fee` decimal(8, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '本次结算交易总服务费',
  `trade_service_ratio` float(5, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '交易服务费比例',
  `create_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '结算时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `shop_id`(`shop_id`) USING BTREE,
  INDEX `settle_sn`(`settle_sn`) USING BTREE,
  INDEX `create_time`(`create_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '商家结算表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_shop_settlement_record
-- ----------------------------
DROP TABLE IF EXISTS `ls_shop_settlement_record`;
CREATE TABLE `ls_shop_settlement_record`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `settle_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '结算ID',
  `order_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '订单ID',
  `settle_sn` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '结算编号',
  `order_sn` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '订单编号',
  `order_amount` decimal(8, 2) UNSIGNED NULL DEFAULT 0.00 COMMENT '订单金额',
  `refund_amount` decimal(8, 2) UNSIGNED NULL DEFAULT 0.00 COMMENT '订单退款金额',
  `after_sales_amount` decimal(8, 2) UNSIGNED NULL DEFAULT 0.00 COMMENT '售后退款金额',
  `distribution_amount` decimal(8, 2) UNSIGNED NULL DEFAULT 0.00 COMMENT '已结算分销金额',
  `platform_commission_amount` decimal(8, 2) UNSIGNED NULL DEFAULT 0.00 COMMENT '平台佣金金额',
  `entry_account_amount` decimal(8, 2) UNSIGNED NULL DEFAULT 0.00 COMMENT '已结算入账金额',
  `trade_service_fee` decimal(8, 2) UNSIGNED NULL DEFAULT 0.00 COMMENT '交易服务费金额',
  `trade_service_ratio` float(5, 2) UNSIGNED NULL DEFAULT 0.00 COMMENT '交易服务费比例(%)',
  `order_complete_time` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '订单完成时间',
  `create_time` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '结算时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `settle_id`(`settle_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '商家结算记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_shop_stat
-- ----------------------------
DROP TABLE IF EXISTS `ls_shop_stat`;
CREATE TABLE `ls_shop_stat`  (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `shop_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '商铺id',
  `ip` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'IP',
  `count` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '次数',
  `create_time` int(10) UNSIGNED NOT NULL COMMENT '变动时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 371 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '访问店铺用户统计表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_shop_tier_config
-- ----------------------------
DROP TABLE IF EXISTS `ls_shop_tier_config`;
CREATE TABLE `ls_shop_tier_config`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `tier_level` tinyint(1) NOT NULL COMMENT '等级：0=0元入驻，1=商家会员，2=实力厂商',
  `tier_image` varchar(350) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `tier_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '等级名称',
  `tier_price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '等级价格',
  `duration_days` int(11) NOT NULL DEFAULT 365 COMMENT '有效期天数',
  `features` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '功能特权JSON配置',
  `limits` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '限制配置JSON',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '等级描述',
  `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：0=禁用，1=启用',
  `create_time` int(11) NOT NULL,
  `update_time` int(11) NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `tier_level`(`tier_level`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商家等级配置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ls_shop_tier_upgrade
-- ----------------------------
DROP TABLE IF EXISTS `ls_shop_tier_upgrade`;
CREATE TABLE `ls_shop_tier_upgrade`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `shop_id` int(11) NOT NULL COMMENT '商家ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `from_tier` tinyint(1) NOT NULL COMMENT '原等级',
  `to_tier` tinyint(1) NOT NULL COMMENT '目标等级',
  `upgrade_type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '升级类型：0=新入驻，1=等级升级，2=续费',
  `order_sn` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '订单号',
  `amount` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '支付金额',
  `payment_status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '支付状态：0=待支付，1=已支付',
  `audit_status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '审核状态：0=待审核，1=已通过，2=已拒绝',
  `effective_time` int(11) NOT NULL DEFAULT 0 COMMENT '生效时间',
  `expire_time` int(11) NOT NULL DEFAULT 0 COMMENT '到期时间',
  `create_time` int(11) NOT NULL,
  `update_time` int(11) NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `shop_id`(`shop_id`) USING BTREE,
  INDEX `user_id`(`user_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商家等级升级记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ls_shop_withdrawal
-- ----------------------------
DROP TABLE IF EXISTS `ls_shop_withdrawal`;
CREATE TABLE `ls_shop_withdrawal`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
  `sn` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '提现单号',
  `shop_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '商家ID',
  `type` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '提现类型 0银行卡 10支付宝',
  `bank_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '银行卡账号ID',
  `alipay_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '支付宝账号id',
  `apply_amount` decimal(8, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '申请提现金额',
  `left_amount` decimal(8, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '实际到账金额(扣除手续费)',
  `poundage_amount` decimal(8, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '手续费金额',
  `poundage_ratio` double(5, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '手续费比例',
  `explain` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '提现说明',
  `status` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '提现状态[0=申请中, 1=处理中, 2=转账成功, 3=转账失败]',
  `transfer_voucher` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '转账凭证(图)',
  `transfer_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '转账时间',
  `transfer_content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '转账说明',
  `create_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商家提现表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_sign_daily
-- ----------------------------
DROP TABLE IF EXISTS `ls_sign_daily`;
CREATE TABLE `ls_sign_daily`  (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `type` varchar(24) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '类型 [1=每日签到 2=连续签到]',
  `integral` int(10) NULL DEFAULT 0 COMMENT '赠送积分',
  `integral_status` tinyint(1) NULL DEFAULT 0 COMMENT '赠送积分状态：[1-是 0-否]',
  `growth` int(16) NULL DEFAULT 0 COMMENT '成长值',
  `growth_status` tinyint(1) NULL DEFAULT 0 COMMENT '赠送优惠劵状态：[1-是 0-否]',
  `days` int(24) NULL DEFAULT 1 COMMENT '连续签到天数',
  `create_time` int(10) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) NULL DEFAULT NULL COMMENT '更新时间',
  `del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除：[0-否 1-是]',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '签到规则表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_sms_log
-- ----------------------------
DROP TABLE IF EXISTS `ls_sms_log`;
CREATE TABLE `ls_sms_log`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `message_key` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '消息key',
  `mobile` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '手机号码',
  `content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '发送内容',
  `code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '发送关键字（注册、找回密码）',
  `is_verify` tinyint(1) NULL DEFAULT 0 COMMENT '是否已验证；0-否；1-是',
  `check_num` int(5) NULL DEFAULT 0 COMMENT '验证次数',
  `send_status` tinyint(1) NOT NULL COMMENT '发送状态：0-发送中；1-发送成功；2-发送失败',
  `send_time` int(10) NOT NULL COMMENT '发送时间',
  `results` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '短信结果',
  `create_time` int(10) NOT NULL COMMENT '创建时间',
  `update_time` int(10) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '短信发送记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_stat
-- ----------------------------
DROP TABLE IF EXISTS `ls_stat`;
CREATE TABLE `ls_stat`  (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `ip` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'IP',
  `count` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '次数',
  `create_time` int(10) UNSIGNED NOT NULL COMMENT '变动时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 535 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户访问统计表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_stat_daily_goods
-- ----------------------------
DROP TABLE IF EXISTS `ls_stat_daily_goods`;
CREATE TABLE `ls_stat_daily_goods`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `shop_id` int(11) UNSIGNED NOT NULL COMMENT '店铺ID',
  `goods_id` int(11) UNSIGNED NOT NULL COMMENT '商品ID',
  `date` date NOT NULL COMMENT '统计日期',
  `total_sales_amount` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '当日销售额(支付金额)',
  `total_sales_quantity` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '当日销售量(支付件数)',
  `view_count` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '当日商品访客数 (需额外实现)',
  `add_to_cart_count` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '当日加购件数 (需额外实现)',
  `create_time` int(11) UNSIGNED NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` int(11) UNSIGNED NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_shop_goods_date`(`shop_id`, `goods_id`, `date`) USING BTREE COMMENT '店铺、商品和日期唯一索引',
  INDEX `idx_date`(`date`) USING BTREE COMMENT '日期索引',
  INDEX `idx_goods_date`(`goods_id`, `date`) USING BTREE COMMENT '商品和日期索引'
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品日统计表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ls_stat_daily_shop
-- ----------------------------
DROP TABLE IF EXISTS `ls_stat_daily_shop`;
CREATE TABLE `ls_stat_daily_shop`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `shop_id` int(11) UNSIGNED NOT NULL COMMENT '店铺ID',
  `date` date NOT NULL COMMENT '统计日期',
  `total_sales_amount` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '当日总销售额(支付金额)',
  `total_order_count` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '当日总订单数(支付订单)',
  `paying_customer_count` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '当日支付买家数',
  `total_refund_amount` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '当日退款金额',
  `total_refund_count` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '当日退款笔数',
  `new_customer_count` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '当日新增支付买家数',
  `create_time` int(11) UNSIGNED NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` int(11) UNSIGNED NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_shop_date`(`shop_id`, `date`) USING BTREE COMMENT '店铺和日期唯一索引',
  INDEX `idx_date`(`date`) USING BTREE COMMENT '日期索引'
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '店铺日统计表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ls_stat_goods_conversion
-- ----------------------------
DROP TABLE IF EXISTS `ls_stat_goods_conversion`;
CREATE TABLE `ls_stat_goods_conversion`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `shop_id` int(11) UNSIGNED NOT NULL COMMENT '店铺ID',
  `goods_id` int(11) UNSIGNED NOT NULL COMMENT '商品ID',
  `date` date NOT NULL COMMENT '统计日期',
  `view_count` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '访客数',
  `cart_count` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '加购人数',
  `order_count` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '下单人数',
  `pay_count` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '支付人数',
  `create_time` int(11) UNSIGNED NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` int(11) UNSIGNED NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_shop_goods_date`(`shop_id`, `goods_id`, `date`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品转化率统计表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ls_stat_hourly_goods
-- ----------------------------
DROP TABLE IF EXISTS `ls_stat_hourly_goods`;
CREATE TABLE `ls_stat_hourly_goods`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `shop_id` int(11) UNSIGNED NOT NULL COMMENT '店铺ID',
  `goods_id` int(11) UNSIGNED NOT NULL COMMENT '商品ID',
  `date` date NOT NULL COMMENT '统计日期',
  `hour` tinyint(2) UNSIGNED NOT NULL COMMENT '统计小时(0-23)',
  `sales_amount` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '销售额',
  `sales_quantity` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '销售量',
  `view_count` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '访客数',
  `create_time` int(11) UNSIGNED NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` int(11) UNSIGNED NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_shop_goods_date_hour`(`shop_id`, `goods_id`, `date`, `hour`) USING BTREE,
  INDEX `idx_date_hour`(`date`, `hour`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品小时统计表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ls_stat_hourly_shop
-- ----------------------------
DROP TABLE IF EXISTS `ls_stat_hourly_shop`;
CREATE TABLE `ls_stat_hourly_shop`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `shop_id` int(11) UNSIGNED NOT NULL COMMENT '店铺ID',
  `date` date NOT NULL COMMENT '统计日期',
  `hour` tinyint(2) UNSIGNED NOT NULL COMMENT '统计小时(0-23)',
  `sales_amount` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '销售额',
  `order_count` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '订单数',
  `customer_count` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '支付买家数',
  `create_time` int(11) UNSIGNED NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` int(11) UNSIGNED NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_shop_date_hour`(`shop_id`, `date`, `hour`) USING BTREE,
  INDEX `idx_date_hour`(`date`, `hour`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '店铺小时统计表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ls_stat_shop_visitor
-- ----------------------------
DROP TABLE IF EXISTS `ls_stat_shop_visitor`;
CREATE TABLE `ls_stat_shop_visitor`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `shop_id` int(11) UNSIGNED NOT NULL COMMENT '店铺ID',
  `date` date NOT NULL COMMENT '统计日期',
  `visitor_count` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '访客数',
  `new_visitor_count` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '新访客数',
  `old_visitor_count` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '老访客数',
  `total_visit_time` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '总访问时长(秒)',
  `bounce_count` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '跳出次数',
  `create_time` int(11) UNSIGNED NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` int(11) UNSIGNED NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_shop_date`(`shop_id`, `date`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '店铺访客分析表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ls_supplier
-- ----------------------------
DROP TABLE IF EXISTS `ls_supplier`;
CREATE TABLE `ls_supplier`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `shop_id` int(11) NOT NULL COMMENT '店铺id',
  `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '名称',
  `contact` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '联系人',
  `mobile` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '联系电话',
  `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '详细地址',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '备注',
  `del` tinyint(2) NULL DEFAULT 0 COMMENT '删除,0-未删除,1-已删除',
  `create_time` int(11) UNSIGNED NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` int(11) UNSIGNED NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '商家供货商表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_system_log
-- ----------------------------
DROP TABLE IF EXISTS `ls_system_log`;
CREATE TABLE `ls_system_log`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `admin_id` int(11) NOT NULL COMMENT '管理员',
  `name` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '管理员名称',
  `account` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '管理员账号',
  `create_time` int(10) NULL DEFAULT NULL COMMENT '创建时间',
  `uri` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '访问链接',
  `type` varchar(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '请求方式',
  `param` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '请求数据',
  `ip` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'Ip地址',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 36077 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '系统日志表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_team_activity
-- ----------------------------
DROP TABLE IF EXISTS `ls_team_activity`;
CREATE TABLE `ls_team_activity`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '团活动ID',
  `shop_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '门店ID',
  `goods_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '商品ID',
  `people_num` smallint(5) UNSIGNED NOT NULL DEFAULT 0 COMMENT '成团所需人数',
  `share_title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '分享标题',
  `share_intro` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '分享简介',
  `team_max_price` decimal(8, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '最高拼团价',
  `team_min_price` decimal(8, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '最低拼团价',
  `sales_volume` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '拼团数量(发起拼团就计算)',
  `audit` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '审核状态[0=待审核，1=审核通过，2=拒绝通过]',
  `status` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '活动状态[0=停止中, 1=活动中]',
  `del` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除[0=否, 1=是]',
  `explain` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '审核说明',
  `effective_time` double(5, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '成团有效时间(小时, 过了就失效)',
  `activity_start_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '活动开始时间',
  `activity_end_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '活动结束时间',
  `create_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '拼团活动表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_team_found
-- ----------------------------
DROP TABLE IF EXISTS `ls_team_found`;
CREATE TABLE `ls_team_found`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `shop_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '店铺ID',
  `team_activity_id` int(10) NOT NULL COMMENT '团活动ID',
  `team_sn` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '拼团编号',
  `user_id` int(10) NOT NULL COMMENT '团长用户ID',
  `people` smallint(5) UNSIGNED NOT NULL DEFAULT 0 COMMENT '成团所需人数',
  `join` smallint(5) UNSIGNED NOT NULL DEFAULT 0 COMMENT '现参团人数',
  `status` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '状态[0-待成团, 1-拼团成功, 2-拼团失败]',
  `goods_snap` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '活动商品快照',
  `kaituan_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '开团时间',
  `invalid_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '团失效时间',
  `team_end_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '拼团成功 / 失败的时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '拼团开团表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_team_goods
-- ----------------------------
DROP TABLE IF EXISTS `ls_team_goods`;
CREATE TABLE `ls_team_goods`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `team_id` int(10) NOT NULL COMMENT '团活动ID',
  `goods_id` int(10) UNSIGNED NOT NULL COMMENT '商品ID',
  `item_id` int(10) UNSIGNED NOT NULL COMMENT '规格ID',
  `team_price` decimal(8, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '拼团价',
  `sales_volume` decimal(8, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '销量',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '拼团商品表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_team_join
-- ----------------------------
DROP TABLE IF EXISTS `ls_team_join`;
CREATE TABLE `ls_team_join`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `sn` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '参团编号',
  `shop_id` int(10) NOT NULL COMMENT '店铺ID',
  `team_activity_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '团活动ID',
  `team_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '团ID',
  `user_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户ID',
  `order_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '订单ID',
  `identity` tinyint(1) UNSIGNED NOT NULL DEFAULT 2 COMMENT '身份[1=团长， 2=团员]',
  `team_snap` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '拼团快照',
  `status` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '状态[0-待成团; 1-成团成功; 2-成团失败]',
  `create_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '拼团时间',
  `update_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '拼团参团表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_treaty
-- ----------------------------
DROP TABLE IF EXISTS `ls_treaty`;
CREATE TABLE `ls_treaty`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `type` smallint(5) UNSIGNED NOT NULL DEFAULT 0 COMMENT '协议类型[10=入驻协议]',
  `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '协议名称',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '协议内容',
  `create_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '协议表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_user
-- ----------------------------
DROP TABLE IF EXISTS `ls_user`;
CREATE TABLE `ls_user`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `sn` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '会员码',
  `root` tinyint(1) UNSIGNED NULL DEFAULT 0 COMMENT '是否为超级管理：0-否；1-是；',
  `nickname` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户昵称',
  `avatar` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户头像',
  `mobile` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '手机号码',
  `real_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '真实姓名',
  `level` tinyint(1) UNSIGNED NULL DEFAULT 0 COMMENT '等级',
  `group_id` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '所属分组id',
  `sex` tinyint(1) UNSIGNED NULL DEFAULT 0 COMMENT '性别:0-未知；1-男；2-女',
  `birthday` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '生日',
  `user_money` decimal(10, 2) UNSIGNED NULL DEFAULT 0.00 COMMENT '用户余额',
  `user_integral` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '用户积分',
  `total_order_amount` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '消费累计额度',
  `total_recharge_amount` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '累计充值金额',
  `account` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '账号',
  `password` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '密码',
  `pay_password` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '支付密码',
  `salt` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '密码盐',
  `first_leader` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '第一个上级',
  `second_leader` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '第二个上级',
  `third_leader` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '第三个上级',
  `ancestor_relation` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '所有的上级关系链',
  `is_distribution` tinyint(1) UNSIGNED NULL DEFAULT 0 COMMENT '是否分销会员：1-是；0-否；',
  `distribution_add_remarks` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '后台添加分销会员备注信息',
  `freeze_distribution` tinyint(1) UNSIGNED NULL DEFAULT 0 COMMENT '冻结分销资格: 1-冻结; 0-正常',
  `distribution_h5_qr_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '分销h5二维码\n',
  `distribution_mnp_qr_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '分销小程序二维码\n',
  `distribution_app_qr_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '分销app二维码\n',
  `distribution_code` varchar(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分销码',
  `create_time` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '修改时间',
  `login_time` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '最后登录时间',
  `login_ip` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '最后登录ip',
  `disable` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否禁用：0-否；1-是；',
  `del` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '0为非删除状态，1为已删除',
  `user_growth` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '用户成长值',
  `activity_score` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '活跃度积分',
  `is_purchaser` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否为采购商：0-否；1-是',
  `last_activity_login` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '最后活跃登录时间',
  `earnings` decimal(10, 2) UNSIGNED NULL DEFAULT 0.00 COMMENT '佣金收益',
  `client` tinyint(1) UNSIGNED NOT NULL COMMENT '注册来源',
  `tag_ids` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '会员标签',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  `is_new_user` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否是新注册用户:1-是;0-否;',
  `user_delete` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户已注销 0否 1是',
  `shop_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '商户ID',
  `jcvip` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否是集采会员0否1是',
  `vip_time` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '集采会员有效截止时间',
  `is_agent` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否是代理',
  `agent_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '代理ID',
  `free_entry_clicked` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否点击过0元入驻按钮:0-未点击,1-已点击',
  `custom_deposit_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '自定义招商顾问保证金金额，NULL或0表示使用通用配置',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `distribution_code`(`distribution_code`) USING BTREE,
  UNIQUE INDEX `sn`(`sn`) USING BTREE,
  INDEX `idx_firstleader`(`first_leader`) USING BTREE,
  INDEX `idx_mobile`(`mobile`) USING BTREE,
  INDEX `idx_level`(`level`) USING BTREE,
  INDEX `idx_group_id`(`group_id`) USING BTREE,
  INDEX `idx_leader1`(`first_leader`) USING BTREE,
  INDEX `idx_leader2`(`second_leader`) USING BTREE,
  INDEX `idx_leader3`(`third_leader`) USING BTREE,
  INDEX `idx_is_distribution`(`is_distribution`) USING BTREE,
  INDEX `idx_freeze_distribution`(`freeze_distribution`) USING BTREE,
  INDEX `idx_disable_del`(`disable`, `del`) USING BTREE,
  INDEX `idx_create_time`(`create_time`) USING BTREE,
  INDEX `idx_shop_id`(`shop_id`) USING BTREE,
  INDEX `idx_is_agent`(`is_agent`) USING BTREE,
  INDEX `idx_agent_id`(`agent_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1026 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_user_activity_log
-- ----------------------------
DROP TABLE IF EXISTS `ls_user_activity_log`;
CREATE TABLE `ls_user_activity_log`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` int(10) UNSIGNED NOT NULL COMMENT '用户ID',
  `activity_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '活动类型：purchaser_login,publish_demand,chat,purchase',
  `score_change` int(10) NOT NULL DEFAULT 0 COMMENT '积分变化（可为负数）',
  `before_score` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '变化前积分',
  `after_score` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '变化后积分',
  `before_level` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '变化前等级',
  `after_level` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '变化后等级',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '备注说明',
  `extra_data` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '额外数据（JSON格式）',
  `create_time` int(10) UNSIGNED NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_activity_type`(`activity_type`) USING BTREE,
  INDEX `idx_create_time`(`create_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 185 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户活跃度日志表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ls_user_address
-- ----------------------------
DROP TABLE IF EXISTS `ls_user_address`;
CREATE TABLE `ls_user_address`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(10) UNSIGNED NOT NULL COMMENT '用户id',
  `contact` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '收货人',
  `telephone` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '联系方式',
  `province_id` int(11) NOT NULL COMMENT '省',
  `city_id` int(11) NULL DEFAULT NULL COMMENT '市',
  `district_id` int(11) NULL DEFAULT NULL COMMENT '区',
  `address` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '详细地址',
  `post_code` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '邮编',
  `longitude` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '经度',
  `latitude` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '纬度',
  `is_default` tinyint(1) UNSIGNED NULL DEFAULT 0 COMMENT '是否默认(1为默认)',
  `create_time` int(11) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` int(11) NULL DEFAULT NULL COMMENT '修改时间',
  `del` tinyint(1) NULL DEFAULT 0 COMMENT '删除,0-未删除,1-已删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 85 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '用户地址表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_user_auth
-- ----------------------------
DROP TABLE IF EXISTS `ls_user_auth`;
CREATE TABLE `ls_user_auth`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户id',
  `openid` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '微信openid',
  `unionid` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '微信unionid',
  `create_time` int(10) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) NULL DEFAULT NULL COMMENT '更新时间',
  `client` tinyint(1) NOT NULL COMMENT '客户端类型：1-微信小程序；2-h5；3-ios；4-android',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `openid`(`openid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 64 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户权限表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_user_distribution
-- ----------------------------
DROP TABLE IF EXISTS `ls_user_distribution`;
CREATE TABLE `ls_user_distribution`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(10) NULL DEFAULT NULL COMMENT '会员id',
  `distribution_order_num` int(10) NULL DEFAULT 0 COMMENT '分销订单数量',
  `distribution_money` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '分销金额',
  `fans` int(10) NULL DEFAULT 0 COMMENT '粉丝数量',
  `order_num` int(10) NULL DEFAULT 0 COMMENT '已支付订单数量',
  `order_amount` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '已支付订单金额',
  `create_time` int(10) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) NULL DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_userid_orderamount_fans_ordernum`(`user_id`, `order_amount`, `fans`, `order_num`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 63 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户分销扩展表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_user_level
-- ----------------------------
DROP TABLE IF EXISTS `ls_user_level`;
CREATE TABLE `ls_user_level`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '名称',
  `growth_value` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '成长值',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '等级备注',
  `background_image` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '背景图片',
  `image` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '等级图标',
  `privilege` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '等级权益',
  `discount` decimal(4, 2) UNSIGNED NULL DEFAULT NULL COMMENT '等级折扣',
  `create_time` int(10) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) NULL DEFAULT NULL COMMENT '更新时间',
  `del` tinyint(10) NULL DEFAULT 0 COMMENT '是否删除;1-是；0-否',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户等级表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_user_reports
-- ----------------------------
DROP TABLE IF EXISTS `ls_user_reports`;
CREATE TABLE `ls_user_reports`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `comm_id` int(11) NOT NULL DEFAULT 0 COMMENT '举报作品ID',
  `user_id` int(11) NOT NULL,
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '举报说明',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '0 未处理  1已处理 2 合规',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user_id`(`user_id`) USING BTREE,
  INDEX `STATUS`(`status`) USING BTREE,
  INDEX `created_at`(`created_at`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_user_sign
-- ----------------------------
DROP TABLE IF EXISTS `ls_user_sign`;
CREATE TABLE `ls_user_sign`  (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `days` int(11) NOT NULL DEFAULT 0 COMMENT '连续签到天数',
  `user_id` int(10) NOT NULL COMMENT '用户id',
  `sign_time` int(10) NOT NULL COMMENT '签到时间',
  `integral` int(11) NULL DEFAULT 0 COMMENT '签到奖励积分',
  `continuous_integral` int(11) NULL DEFAULT 0 COMMENT '连续奖励积分',
  `growth` int(11) NULL DEFAULT 0 COMMENT '签到奖励成长值',
  `continuous_growth` int(11) NULL DEFAULT 0 COMMENT '连续签到奖励成长值',
  `create_time` int(10) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) NULL DEFAULT NULL COMMENT '更新时间',
  `del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除：0-否；1-是；',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '会员签到记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_user_tag
-- ----------------------------
DROP TABLE IF EXISTS `ls_user_tag`;
CREATE TABLE `ls_user_tag`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '名称',
  `remark` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  `create_time` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `update_time` int(10) NOT NULL COMMENT '更新时间',
  `del` tinyint(1) UNSIGNED NULL DEFAULT 0 COMMENT '是否删除；1-是；0-否',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户标签表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_user_transfer_records
-- ----------------------------
DROP TABLE IF EXISTS `ls_user_transfer_records`;
CREATE TABLE `ls_user_transfer_records`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `transferred_user_id` int(11) NOT NULL COMMENT '被转移的用户ID',
  `original_agent_id` int(11) NOT NULL COMMENT '原始代理ID',
  `new_agent_id` int(11) NOT NULL COMMENT '新代理ID, 可为空表示转移给平台',
  `transfer_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '转移时间',
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '下级用户转移记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_verification
-- ----------------------------
DROP TABLE IF EXISTS `ls_verification`;
CREATE TABLE `ls_verification`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `order_id` int(11) NOT NULL COMMENT '订单ID',
  `shop_id` int(11) NOT NULL COMMENT '自提门店ID',
  `handle_id` int(11) NOT NULL DEFAULT 0 COMMENT '操作人ID(0为系统核销)',
  `verification_scene` tinyint(1) NOT NULL DEFAULT 0 COMMENT '核销场景:0-系统;1-管理员;2-会员;',
  `snapshot` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '核销员数据快照',
  `create_time` int(10) NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '核销表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_wechat_reply
-- ----------------------------
DROP TABLE IF EXISTS `ls_wechat_reply`;
CREATE TABLE `ls_wechat_reply`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '规则名称',
  `keyword` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '关键词',
  `reply_type` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '回复类型',
  `matching_type` tinyint(1) NULL DEFAULT NULL COMMENT '匹配方式：null-不设置；1-全匹配；2-模糊匹配',
  `content_type` tinyint(1) NULL DEFAULT NULL COMMENT '内容类型：null-不设置；1-文本；',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '回复内容',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '启动状态：1-启动；0-关闭',
  `sort` int(11) NULL DEFAULT 0 COMMENT '排序',
  `create_time` int(10) NOT NULL COMMENT '创建时间',
  `update_time` int(10) NULL DEFAULT NULL COMMENT '更新时间',
  `del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除：1-是；0-否',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '微信回复配置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_withdraw_apply
-- ----------------------------
DROP TABLE IF EXISTS `ls_withdraw_apply`;
CREATE TABLE `ls_withdraw_apply`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `sn` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '提现单号',
  `batch_no` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商家批次单号',
  `user_id` int(11) NOT NULL COMMENT '用户id',
  `real_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '真实姓名',
  `account` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '账号',
  `type` tinyint(1) NOT NULL COMMENT '类型：\r\n1-提现到钱包余额；\r\n2-提现到微信零钱；\r\n3-提现到微信收款码;\r\n4-提现到支付宝收款码',
  `money` decimal(10, 2) UNSIGNED NOT NULL COMMENT '提现金额',
  `left_money` decimal(10, 2) NULL DEFAULT NULL COMMENT '用户可得的金额(扣除手续费后)',
  `money_qr_code` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '收款二维码',
  `poundage` decimal(10, 2) NULL DEFAULT NULL COMMENT '手续费',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '提现申请备注',
  `status` tinyint(1) NOT NULL COMMENT '状态：\r\n1-待提现\r\n2-提现中\r\n3-提现成功\r\n4-提现失败',
  `create_time` int(10) NOT NULL COMMENT '创建时间',
  `update_time` int(10) NULL DEFAULT NULL COMMENT '更新时间',
  `pay_desc` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '微信零钱支付信息',
  `payment_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '支付单号',
  `payment_time` int(11) NULL DEFAULT NULL COMMENT '支付时间',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '审核备注',
  `transfer_voucher` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '转账凭证',
  `transfer_time` int(11) NULL DEFAULT NULL COMMENT '转账时间',
  `pay_search_desc` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '微信零钱支付查询结果',
  `transfer_description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '转账备注',
  `repay_time` int(11) NULL DEFAULT NULL COMMENT '重新支付时间',
  `bank` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '提现银行',
  `subbank` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '提现银行支行',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '提现申请表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ls_word
-- ----------------------------
DROP TABLE IF EXISTS `ls_word`;
CREATE TABLE `ls_word`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `word` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '分词内容',
  `goods_id` int(11) NOT NULL,
  `tags` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '标签',
  `created_at` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 10818 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '分词表' ROW_FORMAT = DYNAMIC;

SET FOREIGN_KEY_CHECKS = 1;
