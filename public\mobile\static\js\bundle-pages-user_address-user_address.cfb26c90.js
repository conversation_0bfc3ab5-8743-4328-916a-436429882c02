(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["bundle-pages-user_address-user_address"],{"1aeb":function(e,t,s){var i=s("24fb");t=i(!1),t.push([e.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.user-address[data-v-7f6d32e8]{padding-bottom:calc(%?140?% + env(safe-area-inset-bottom))}.user-address .no-address[data-v-7f6d32e8]{padding-top:%?300?%;text-align:center}.user-address .address-list[data-v-7f6d32e8]{padding:%?10?% 0}.user-address .address-list .item[data-v-7f6d32e8]{padding:0 %?30?%}.user-address .address-list .item .address[data-v-7f6d32e8]{padding:%?20?% 0;border-bottom:1px solid #e5e5e5}.user-address .address-list .item .operation[data-v-7f6d32e8]{height:%?80?%}.user-address .address-list .u-radio-group[data-v-7f6d32e8]{display:block}.user-address .footer[data-v-7f6d32e8]{position:fixed;left:0;right:0;bottom:0;height:%?118?%;padding:0 %?30?%;box-sizing:initial;padding-bottom:env(safe-area-inset-bottom)}.user-address .footer .btn[data-v-7f6d32e8]{flex:1;height:%?80?%}.tips-dialog[data-v-7f6d32e8]{height:%?230?%;width:100%}',""]),e.exports=t},"1de6":function(e,t,s){"use strict";s.d(t,"b",(function(){return a})),s.d(t,"c",(function(){return n})),s.d(t,"a",(function(){return i}));var i={uRadioGroup:s("5bd2").default,uRadio:s("5f34").default,uModal:s("53c9").default},a=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("v-uni-view",{staticClass:"user-address"},[e.hasAddress?s("v-uni-view",{staticClass:"address-list"},[s("u-radio-group",{staticClass:"radio-group",attrs:{"active-color":e.colorConfig.primary},on:{change:function(t){arguments[0]=t=e.$handleEvent(t),e.radioChange.apply(void 0,arguments)}},model:{value:e.currentId,callback:function(t){e.currentId=t},expression:"currentId"}},e._l(e.addressList,(function(t,i){return s("v-uni-view",{key:i,staticClass:"item bg-white m-b-20",on:{click:function(s){arguments[0]=s=e.$handleEvent(s),e.onSelect(t.id)}}},[s("v-uni-view",{staticClass:"address"},[s("v-uni-view",{staticClass:"consignee md bold"},[e._v(e._s(t.contact)),s("v-uni-text",{staticClass:"phone m-l-10"},[e._v(e._s(t.telephone))])],1),s("v-uni-view",{staticClass:"lighter sm m-t-10"},[e._v(e._s(t.province)+" "+e._s(t.city)+" "+e._s(t.district)+" "+e._s(t.address))])],1),s("v-uni-view",{staticClass:"operation flex row-between"},[s("v-uni-view",{on:{click:function(t){t.stopPropagation(),arguments[0]=t=e.$handleEvent(t)}}},[s("u-radio",{staticClass:"radio flex",attrs:{name:t.id}},[s("v-uni-text",{staticClass:"xs"},[e._v(e._s(e.currentId==t.id?"默认":"设为默认"))])],1)],1),s("v-uni-view",{staticClass:"flex row-center"},[s("router-link",{attrs:{to:{path:"/bundle/pages/address_edit/address_edit",query:{id:t.id}}}},[s("v-uni-view",{staticClass:"flex m-r-20"},[s("v-uni-image",{staticClass:"icon-md m-r-10",attrs:{src:"/static/images/icon_edit.png"}}),e._v("编辑")],1)],1),s("v-uni-view",{staticClass:"flex m-l-20",on:{click:function(s){s.stopPropagation(),arguments[0]=s=e.$handleEvent(s),e.showSurePop(t.id)}}},[s("v-uni-image",{staticClass:"icon-md m-r-10",attrs:{src:"/static/images/icon_del_1.png"}}),e._v("删除")],1)],1)],1)],1)})),1)],1):s("v-uni-view",{staticClass:"no-address flex-col col-center"},[s("v-uni-image",{staticClass:"img-null mt20",attrs:{src:"/static/images/address_null.png"}}),s("v-uni-view",{staticClass:"sm muted"},[e._v("暂无添加地址，请添加~")])],1),s("u-modal",{attrs:{id:"delete-dialog","show-cancel-button":!0,"confirm-text":"狠心删除","confirm-color":e.colorConfig.primary,"show-title":!1},on:{confirm:function(t){arguments[0]=t=e.$handleEvent(t),e.delAddressFun.apply(void 0,arguments)}},model:{value:e.deleteSure,callback:function(t){e.deleteSure=t},expression:"deleteSure"}},[s("v-uni-view",{staticClass:"flex-col col-center tips-dialog p-t-40"},[s("v-uni-image",{staticClass:"icon-lg",attrs:{src:"/static/images/icon_warning.png"}}),s("v-uni-view",{staticStyle:{"margin-top":"30rpx"}},[e._v("确认删除该地址吗？")])],1)],1),s("v-uni-view",{staticClass:"footer flex row-between fixed bg-white"},[e.isWeixin?s("v-uni-view",{staticClass:"btn flex row-center bg-gray br60 m-r-20",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.getWxAddressFun.apply(void 0,arguments)}}},[s("v-uni-image",{staticClass:"icon-lg m-r-10",attrs:{src:"/static/images/icon_wechat.png"}}),s("v-uni-text",{staticClass:"md"},[e._v("微信导入")])],1):e._e(),s("router-link",{staticClass:"flex-1",attrs:{to:{path:"/bundle/pages/address_edit/address_edit"}}},[s("v-uni-view",{staticClass:"btn bg-primary white md flex row-center br60"},[e._v("新增收货地址")])],1)],1)],1)},n=[]},5011:function(e,t,s){"use strict";s.r(t);var i=s("1de6"),a=s("b80b");for(var n in a)["default"].indexOf(n)<0&&function(e){s.d(t,e,(function(){return a[e]}))}(n);s("9b5d");var d=s("f0c5"),r=Object(d["a"])(a["default"],i["b"],i["c"],!1,null,"7f6d32e8",null,!1,i["a"],void 0);t["default"]=r.exports},"79f1":function(e,t,s){"use strict";s("7a82");var i=s("ee27").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=i(s("f07e")),n=i(s("c964"));s("7db0"),s("d3b7"),s("e9c4"),s("14d9");var d=s("1524"),r=i(s("7b2e")),o=s("a5ae"),u={data:function(){return{addressList:[],hasAddress:!0,deleteSure:!1,currentId:0,isWeixin:!0}},onLoad:function(e){this.type=this.$Route.query.type,this.isWeixin=(0,o.isWeixinClient)()},onShow:function(){this.getAddressListsFun()},methods:{onSelect:function(e){this.type&&(uni.$emit("selectaddress",{id:e}),uni.navigateBack())},getAddressListsFun:function(){var e=this;(0,d.getAddressLists)().then((function(t){if(1==t.code)if(t.data.length){e.addressList=t.data;var s=t.data.find((function(e){return e.is_default}));e.currentId=s?s.id:0,e.hasAddress=!0}else e.hasAddress=!1;else e.hasAddress=!1}))},radioChange:function(e){var t=this;return(0,n.default)((0,a.default)().mark((function s(){var i,n,r;return(0,a.default)().wrap((function(s){while(1)switch(s.prev=s.next){case 0:return s.next=2,(0,d.setDefaultAddress)(e);case 2:i=s.sent,n=i.code,r=i.msg,1==n&&(t.$toast({title:r}),t.getAddressListsFun());case 6:case"end":return s.stop()}}),s)})))()},onLoadFun:function(){this.getAddressListsFun()},delAddressFun:function(){var e=this;(0,d.delAddress)(this.delectId).then((function(t){1==t.code&&(e.$toast({title:t.msg}),e.deleteSure=!1,e.getAddressListsFun())}))},getWxAddressFun:function(){var e=this;r.default.getWxAddress().then((function(t){uni.setStorageSync("wxAddress",JSON.stringify(t)),setTimeout((function(){e.$Router.push({path:"/bundle/pages/address_edit/address_edit"})}),200)}))},showSurePop:function(e){this.deleteSure=!0,this.delectId=e}}};t.default=u},"9b5d":function(e,t,s){"use strict";var i=s("a541"),a=s.n(i);a.a},a541:function(e,t,s){var i=s("1aeb");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[e.i,i,""]]),i.locals&&(e.exports=i.locals);var a=s("4f06").default;a("2db1e070",i,!0,{sourceMap:!1,shadowMode:!1})},b80b:function(e,t,s){"use strict";s.r(t);var i=s("79f1"),a=s.n(i);for(var n in i)["default"].indexOf(n)<0&&function(e){s.d(t,e,(function(){return i[e]}))}(n);t["default"]=a.a}}]);