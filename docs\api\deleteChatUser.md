# 删除联系人接口文档

## 接口信息
- **接口路径**: `/api/user/deleteChatUser`
- **请求方式**: `POST`
- **接口描述**: 删除当前用户与指定客服的联系关系，但保留聊天记录

## 请求参数

### Headers
```
Content-Type: application/json
Authorization: Bearer {token}  // 用户登录token
```

### Body参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| kefu_id | int | 是 | 客服ID |
| shop_id | int | 是 | 商家ID |

### 请求示例
```json
{
    "kefu_id": 123,
    "shop_id": 456
}
```

## 响应参数

### 成功响应
```json
{
    "code": 1,
    "msg": "删除成功",
    "data": null,
    "show": 0
}
```

### 失败响应
```json
{
    "code": 0,
    "msg": "参数错误",
    "data": null,
    "show": 1
}
```

或

```json
{
    "code": 0,
    "msg": "删除失败",
    "data": null,
    "show": 1
}
```

## 错误码说明
| 错误码 | 说明 |
|--------|------|
| 0 | 失败 |
| 1 | 成功 |

## 业务逻辑说明
1. 接口需要用户登录验证
2. 验证kefu_id和shop_id参数是否为空
3. 根据当前用户ID、客服ID和商家ID删除ChatRelation表中的联系关系记录
4. **重要**: 只删除联系关系，不删除ChatRecord表中的聊天记录
5. 删除成功返回成功信息，删除失败返回失败信息

## 数据库操作
- **影响表**: `ls_chat_relation`
- **操作类型**: DELETE
- **查询条件**: 
  - user_id = 当前登录用户ID
  - kefu_id = 请求参数中的客服ID  
  - shop_id = 请求参数中的商家ID

## 注意事项
1. 此接口只删除联系人关系，聊天记录会被保留
2. 删除后用户在联系人列表中将看不到该客服
3. 如果需要重新建立联系，用户需要重新发起聊天
4. 接口需要用户登录状态，未登录用户无法调用
