(window.webpackJsonp=window.webpackJsonp||[]).push([[5,19],{473:function(t,e,r){"use strict";var n=r(14),o=r(4),c=r(5),l=r(141),f=r(24),d=r(18),v=r(290),h=r(54),m=r(104),_=r(289),y=r(3),S=r(105).f,w=r(45).f,x=r(23).f,N=r(474),z=r(475).trim,C="Number",I=o.Number,E=I.prototype,k=o.TypeError,F=c("".slice),A=c("".charCodeAt),M=function(t){var e=_(t,"number");return"bigint"==typeof e?e:T(e)},T=function(t){var e,r,n,o,c,l,f,code,d=_(t,"number");if(m(d))throw k("Cannot convert a Symbol value to a number");if("string"==typeof d&&d.length>2)if(d=z(d),43===(e=A(d,0))||45===e){if(88===(r=A(d,2))||120===r)return NaN}else if(48===e){switch(A(d,1)){case 66:case 98:n=2,o=49;break;case 79:case 111:n=8,o=55;break;default:return+d}for(l=(c=F(d,2)).length,f=0;f<l;f++)if((code=A(c,f))<48||code>o)return NaN;return parseInt(c,n)}return+d};if(l(C,!I(" 0o1")||!I("0b1")||I("+0x1"))){for(var O,L=function(t){var e=arguments.length<1?0:I(M(t)),r=this;return h(E,r)&&y((function(){N(r)}))?v(Object(e),r,L):e},R=n?S(I):"MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,EPSILON,MAX_SAFE_INTEGER,MIN_SAFE_INTEGER,isFinite,isInteger,isNaN,isSafeInteger,parseFloat,parseInt,fromString,range".split(","),V=0;R.length>V;V++)d(I,O=R[V])&&!d(L,O)&&x(L,O,w(I,O));L.prototype=E,E.constructor=L,f(o,C,L,{constructor:!0})}},474:function(t,e,r){var n=r(5);t.exports=n(1..valueOf)},475:function(t,e,r){var n=r(5),o=r(36),c=r(19),l=r(476),f=n("".replace),d="["+l+"]",v=RegExp("^"+d+d+"*"),h=RegExp(d+d+"*$"),m=function(t){return function(e){var r=c(o(e));return 1&t&&(r=f(r,v,"")),2&t&&(r=f(r,h,"")),r}};t.exports={start:m(1),end:m(2),trim:m(3)}},476:function(t,e){t.exports="\t\n\v\f\r                　\u2028\u2029\ufeff"},477:function(t,e,r){var content=r(480);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,r(17).default)("7c52e05d",content,!0,{sourceMap:!1})},478:function(t,e,r){"use strict";r.r(e);r(473);var n={data:function(){return{priceSlice:{}}},components:{},props:{firstSize:{type:Number,default:14},secondSize:{type:Number,default:14},color:{type:String},weight:{type:[String,Number],default:400},price:{type:[String,Number],default:""},showSubscript:{type:Boolean,default:!0},subscriptSize:{type:Number,default:14},lineThrough:{type:Boolean,default:!1}},created:function(){this.priceFormat()},watch:{price:function(t){this.priceFormat()}},methods:{priceFormat:function(){var t=this.price,e={};null!==t&&(t=String(t).split("."),e.first=t[0],e.second=t[1],this.priceSlice=e)}}},o=(r(479),r(8)),component=Object(o.a)(n,(function(){var t=this,e=t._self._c;return e("span",{class:(t.lineThrough?"line-through":"")+"price-format",style:{color:t.color,"font-weight":t.weight}},[t.showSubscript?e("span",{style:{"font-size":t.subscriptSize+"px","margin-right":"1px"}},[t._v("¥")]):t._e(),t._v(" "),e("span",{style:{"font-size":t.firstSize+"px","margin-right":"1px"}},[t._v(t._s(t.priceSlice.first))]),t._v(" "),t.priceSlice.second?e("span",{style:{"font-size":t.secondSize+"px"}},[t._v("."+t._s(t.priceSlice.second))]):t._e()])}),[],!1,null,null,null);e.default=component.exports},479:function(t,e,r){"use strict";r(477)},480:function(t,e,r){var n=r(16)(!1);n.push([t.i,".price-format{display:flex;align-items:baseline}",""]),t.exports=n},531:function(t,e,r){var content=r(557);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,r(17).default)("4a38586a",content,!0,{sourceMap:!1})},556:function(t,e,r){"use strict";r(531)},557:function(t,e,r){var n=r(16)(!1);n.push([t.i,".activity-area[data-v-008ee916]{padding:16px;border-radius:6px;background-color:#fff}.activity-area[data-v-008ee916] .swiper-container{width:100%;height:280px}.activity-area .goods-list .goods-item[data-v-008ee916]{width:31.5%}.activity-area .goods-list .goods-item .goods-img[data-v-008ee916]{width:100%;height:0;padding-top:100%;position:relative}.activity-area .goods-list .goods-item .goods-img .el-image[data-v-008ee916]{position:absolute;width:100%;height:100%;left:0;top:0}.activity-area .goods-list .goods-item .name[data-v-008ee916]{line-height:20px;height:40px}",""]),t.exports=n},605:function(t,e,r){"use strict";r.r(e);r(29),r(62);var n={components:{},props:{url:{type:String,default:""},title:{type:String},list:{type:Array,default:function(){return[]}}},data:function(){return{swiperOptions:{direction:"vertical",initialSlide:0,height:280,autoplay:!0},pageSize:3}},computed:{swiperSize:function(){return Math.ceil(this.list.length/this.pageSize)},getSwiperList:function(){var t=this;return function(e){return t.list.slice(e*t.pageSize,(e+1)*t.pageSize)}}}},o=(r(556),r(8)),component=Object(o.a)(n,(function(){var t=this,e=t._self._c;return t.list.length?e("div",{staticClass:"activity-area m-t-16"},[e("div",{staticClass:"title flex row-between"},[e("div",{staticClass:"font-size-20"},[t._v(t._s(t.title))]),t._v(" "),e("nuxt-link",{staticClass:"more lighter",attrs:{to:t.url}},[t._v("更多 "),e("i",{staticClass:"el-icon-arrow-right"})])],1),t._v(" "),e("div",{staticClass:"activity-goods m-t-16"},[e("client-only",[e("swiper",{ref:"headerSwiper",attrs:{options:t.swiperOptions}},t._l(t.swiperSize,(function(r,n){return e("swiper-slide",{key:n,staticClass:"swiper-item"},[e("div",{staticClass:"goods-list flex row-between"},t._l(t.getSwiperList(n),(function(r,n){return e("nuxt-link",{key:n,staticClass:"goods-item",attrs:{to:"/goods_details/".concat(r.id)}},[e("div",{staticClass:"goods-img"},[e("el-image",{attrs:{lazy:"",src:r.image,fit:"cover",alt:""}})],1),t._v(" "),e("div",{staticClass:"name line-2 m-t-10"},[t._v(t._s(r.name))]),t._v(" "),e("div",{staticClass:"price flex col-baseline m-t-10"},[e("div",{staticClass:"primary m-r-8"},[e("price-formate",{attrs:{price:r.min_price,"first-size":16}})],1),t._v(" "),e("div",{staticClass:"muted sm line-through"},[e("price-formate",{attrs:{price:r.market_price}})],1)]),t._v(" "),e("div",{staticClass:"muted xs m-t-10"},[t._v("\n                                    "+t._s(r.sales_total)+"人购买\n                                ")])])})),1)])})),1)],1)],1)]):t._e()}),[],!1,null,"008ee916",null);e.default=component.exports;installComponents(component,{PriceFormate:r(478).default})}}]);