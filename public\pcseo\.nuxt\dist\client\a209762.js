(window.webpackJsonp=window.webpackJsonp||[]).push([[11],{437:function(e,t,r){"use strict";var n=r(17),l=r(2),o=r(3),d=r(136),v=r(27),c=r(18),m=r(271),f=r(52),h=r(135),_=r(270),x=r(5),y=r(98).f,w=r(44).f,C=r(26).f,N=r(438),S=r(439).trim,I="Number",E=l.Number,k=E.prototype,A=l.TypeError,T=o("".slice),F=o("".charCodeAt),O=function(e){var t=_(e,"number");return"bigint"==typeof t?t:L(t)},L=function(e){var t,r,n,l,o,d,v,code,c=_(e,"number");if(h(c))throw A("Cannot convert a Symbol value to a number");if("string"==typeof c&&c.length>2)if(c=S(c),43===(t=F(c,0))||45===t){if(88===(r=F(c,2))||120===r)return NaN}else if(48===t){switch(F(c,1)){case 66:case 98:n=2,l=49;break;case 79:case 111:n=8,l=55;break;default:return+c}for(d=(o=T(c,2)).length,v=0;v<d;v++)if((code=F(o,v))<48||code>l)return NaN;return parseInt(o,n)}return+c};if(d(I,!E(" 0o1")||!E("0b1")||E("+0x1"))){for(var B,D=function(e){var t=arguments.length<1?0:E(O(e)),r=this;return f(k,r)&&x((function(){N(r)}))?m(Object(t),r,D):t},M=n?y(E):"MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,EPSILON,MAX_SAFE_INTEGER,MIN_SAFE_INTEGER,isFinite,isInteger,isNaN,isSafeInteger,parseFloat,parseInt,fromString,range".split(","),R=0;M.length>R;R++)c(E,B=M[R])&&!c(D,B)&&C(D,B,w(E,B));D.prototype=k,k.constructor=D,v(l,I,D)}},438:function(e,t,r){var n=r(3);e.exports=n(1..valueOf)},439:function(e,t,r){var n=r(3),l=r(33),o=r(16),d=r(440),v=n("".replace),c="["+d+"]",m=RegExp("^"+c+c+"*"),f=RegExp(c+c+"*$"),h=function(e){return function(t){var r=o(l(t));return 1&e&&(r=v(r,m,"")),2&e&&(r=v(r,f,"")),r}};e.exports={start:h(1),end:h(2),trim:h(3)}},440:function(e,t){e.exports="\t\n\v\f\r                　\u2028\u2029\ufeff"},475:function(e,t,r){var content=r(493);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[e.i,content,""]]),content.locals&&(e.exports=content.locals);(0,r(14).default)("db2946c2",content,!0,{sourceMap:!1})},492:function(e,t,r){"use strict";r(475)},493:function(e,t,r){var n=r(13)(!1);n.push([e.i,".deliver-search-container .deliver-box .deliver-recode-box[data-v-79dec466]{padding:10px 20px;background-color:#f2f2f2}.deliver-search-container .deliver-box .deliver-recode-box .recode-img[data-v-79dec466]{position:relative;width:72px;height:72px}.deliver-search-container .deliver-box .deliver-recode-box .recode-img .float-count[data-v-79dec466]{position:absolute;bottom:0;height:20px;width:100%;background-color:rgba(0,0,0,.5);color:#fff;font-size:12px}.deliver-search-container .deliver-box .deliver-recode-box .recode-info-container[data-v-79dec466]{flex:1}.deliver-search-container .deliver-box .deliver-recode-box .recode-info-container .recode-label[data-v-79dec466]{width:70px}.deliver-search-container .deliver-box .deliver-recode-box .recode-info-container .copy-btn[data-v-79dec466]{height:20px;min-width:42px;border:1px solid #ff2c3c;font-size:12px;margin-left:8px;border-radius:60px;cursor:pointer}.deliver-search-container .deliver-box .deliver-recode-box .recode-info-container .copy-btn[data-v-79dec466]:hover{background-color:#fff}.deliver-search-container .deliver-box .deliver-flow-box[data-v-79dec466]{padding-left:15px}.deliver-search-container .deliver-box .time-line-title[data-v-79dec466]{font-weight:500px;font-size:16px;margin-bottom:10px}",""]),e.exports=n},502:function(e,t,r){"use strict";r.r(t);var n=r(6),l=(r(51),r(437),{props:{value:{type:Boolean,default:!1},aid:{type:Number|String}},data:function(){return{showDialog:!1,deliverBuy:{},delivery:{},deliverFinish:{},deliverOrder:{},deliverShipment:{},deliverTake:{},timeLineArray:[]}},watch:{value:function(e){console.log(e,"val"),this.showDialog=e},showDialog:function(e){e&&this.aid&&(this.timeLineArray=[],this.getDeliverTraces()),this.$emit("input",e)}},methods:{getDeliverTraces:function(){var e=this;return Object(n.a)(regeneratorRuntime.mark((function t(){var data,r,n,l,o,d,v,c,m;return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return data={id:e.aid},t.next=3,e.$get("order/orderTraces",{params:data});case 3:1==(r=t.sent).code&&(n=r.data,l=n.buy,o=n.delivery,d=n.finish,v=n.order,c=n.shipment,m=n.take,e.deliverBuy=l,e.delivery=o,e.deliverFinish=d,e.deliverOrder=v,e.deliverShipment=c,e.deliverTake=m,e.timeLineArray.push(e.deliverFinish),e.timeLineArray.push(e.delivery),e.timeLineArray.push(e.deliverShipment),e.timeLineArray.push(e.deliverBuy),console.log(e.timeLineArray));case 5:case"end":return t.stop()}}),t)})))()},onCopy:function(){var e=document.createElement("input");e.value=this.deliverOrder.invoice_no,document.body.appendChild(e),e.select(),document.execCommand("Copy"),this.$message.success("复制成功"),e.remove()}}}),o=(r(492),r(9)),component=Object(o.a)(l,(function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"deliver-search-container"},[r("el-dialog",{attrs:{visible:e.showDialog,top:"30vh",width:"900px",title:"物流查询"},on:{"update:visible":function(t){e.showDialog=t}}},[r("div",{staticClass:"deliver-box"},[r("div",{staticClass:"deliver-recode-box flex"},[r("div",{staticClass:"recode-img"},[r("el-image",{staticStyle:{width:"100%",height:"100%"},attrs:{fit:"cover",src:e.deliverOrder.image}}),e._v(" "),r("div",{staticClass:"float-count flex row-center"},[e._v("共"+e._s(e.deliverOrder.count)+"件商品")])],1),e._v(" "),r("div",{staticClass:"recode-info-container m-l-10"},[r("div",{staticClass:"flex"},[r("div",{staticClass:"recode-label"},[e._v("物流状态：")]),e._v(" "),r("div",{staticClass:"primary lg",staticStyle:{"font-weight":"500"}},[e._v(e._s(e.deliverOrder.tips))])]),e._v(" "),r("div",{staticClass:"flex",staticStyle:{margin:"6px 0"}},[r("div",{staticClass:"recode-label"},[e._v("快递公司：")]),e._v(" "),r("div",[e._v(e._s(e.deliverOrder.shipping_name))])]),e._v(" "),r("div",{staticClass:"flex"},[r("div",{staticClass:"recode-label"},[e._v("快递单号：")]),e._v(" "),r("div",[e._v(e._s(e.deliverOrder.invoice_no))]),e._v(" "),r("div",{staticClass:"copy-btn primary flex row-center",on:{click:e.onCopy}},[e._v("复制")])])])]),e._v(" "),r("div",{staticClass:"deliver-flow-box m-t-16"},[r("el-timeline",[e.deliverFinish.tips?r("el-timeline-item",[r("div",[r("div",{staticClass:"flex lg"},[r("div",{staticClass:"m-r-8",staticStyle:{"font-weight":"500"}},[e._v("\n                                    "+e._s(e.deliverTake.contacts)+"\n                                ")]),e._v(" "),r("div",{staticStyle:{"font-weight":"500"}},[e._v(e._s(e.deliverTake.mobile))])]),e._v(" "),r("div",{staticClass:"lighter m-t-8"},[e._v(e._s(e.deliverTake.address))])])]):e._e(),e._v(" "),e.deliverFinish.tips?r("el-timeline-item",{attrs:{timestamp:e.deliverFinish.time}},[r("div",{staticClass:"time-line-title"},[e._v(e._s(e.deliverFinish.title))]),e._v(" "),r("div",[e._v(e._s(e.deliverFinish.tips))])]):e._e(),e._v(" "),e.delivery.traces&&e.delivery.traces.length?r("el-timeline-item",{attrs:{timestamp:e.delivery.time}},[r("div",{staticClass:"time-line-title m-b-8"},[e._v(e._s(e.delivery.title))]),e._v(" "),e._l(e.delivery.traces,(function(t,n){return r("el-timeline-item",{key:n,attrs:{timestamp:t[0]}},[r("div",{staticClass:"muted"},[e._v(e._s(t[1]))])])}))],2):e._e(),e._v(" "),e.deliverShipment.tips?r("el-timeline-item",{attrs:{timestamp:e.deliverShipment.time}},[r("div",{staticClass:"time-line-title"},[e._v(e._s(e.deliverShipment.title))]),e._v(" "),r("div",[e._v(e._s(e.deliverShipment.tips))])]):e._e(),e._v(" "),e.deliverBuy.tips?r("el-timeline-item",{attrs:{timestamp:e.deliverBuy.time}},[r("div",{staticClass:"time-line-title"},[e._v(e._s(e.deliverBuy.title))]),e._v(" "),r("div",[e._v(e._s(e.deliverBuy.tips))])]):e._e()],1)],1)])])],1)}),[],!1,null,"79dec466",null);t.default=component.exports}}]);