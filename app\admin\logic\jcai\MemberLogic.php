<?php

namespace app\admin\logic\jcai;

use app\common\basics\Logic;
use app\common\model\jcai\JcaiOrder;
use app\common\server\UrlServer;
use Exception;

class MemberLogic extends Logic
{
    /**
     * @Notes: 获取集采会员订单列表
     * @Author: Cline
     * @param $get
     * @return array|bool
     */
    public static function lists($get)
    {
        try {
            $where = [];
            if (!empty($get['datetime']) && $get['datetime']) {
                $dateRange = explode(' - ', $get['datetime']);
                if (count($dateRange) === 2) {
                    $start = trim($dateRange[0]);
                    $end = trim($dateRange[1]);

                    $startTime = strtotime($start . ' 00:00:00');
                    $endTime = strtotime($end . ' 23:59:59');

                    if ($startTime !== false && $endTime !== false) {
                        $where[] = ['O.create_time', '>=', $startTime];
                        $where[] = ['O.create_time', '<=', $endTime];
                    }
                }
            }

            if (!empty($get['order_sn']) && $get['order_sn']) {
                $where[] = ['O.order_sn', 'like', '%' . $get['order_sn'] . '%'];
            }

            if (isset($get['pay_status']) && $get['pay_status'] !== '' && is_numeric($get['pay_status'])) {
                $where[] = ['O.pay_status', '=', (int)$get['pay_status']];
            }

            $model = new JcaiOrder();
            $lists = $model->alias('O')
                ->field(['O.*', 'U.nickname', 'U.sn as user_sn', 'U.avatar'])
                ->join('user U', 'U.id = O.user_id')
                ->where($where)
                ->order('O.create_time desc')
                ->paginate([
                    'list_rows' => isset($get['limit']) && is_numeric($get['limit']) ? (int)$get['limit'] : 20,
                    'page' => isset($get['page']) && is_numeric($get['page']) ? (int)$get['page'] : 1,
                    'var_page' => 'page'
                ])->toArray();

            foreach ($lists['data'] as &$item) {
                // 安全处理支付时间
                $item['pay_time_text'] = '';
                if (!empty($item['pay_time']) && is_numeric($item['pay_time'])) {
                    $item['pay_time_text'] = date('Y-m-d H:i:s', (int)$item['pay_time']);
                }

                // 安全处理创建时间
                $item['create_time_text'] = '';
                if (!empty($item['create_time']) && is_numeric($item['create_time'])) {
                    $item['create_time_text'] = date('Y-m-d H:i:s', (int)$item['create_time']);
                }

                $item['pay_status_text'] = $item['pay_status'] == 1 ? '已支付' : '待支付';
                $item['avatar'] = UrlServer::getFileUrl($item['avatar']);
            }
            return ['count' => $lists['total'], 'lists' => $lists['data']];
        } catch (Exception $e) {
            // 记录详细错误信息
            \think\facade\Log::write('JcaiMember lists error: ' . $e->getMessage() . ' File: ' . $e->getFile() . ' Line: ' . $e->getLine(), 'jcai_error');
            static::$error = $e->getMessage();
            return false;
        }
    }

    /**
     * @Notes: 统计
     * @Author: Cline
     * @return array
     */
    public static function statistics()
    {
        $model = new JcaiOrder();
        $total = $model->count();
        $paid = $model->where('pay_status', 1)->count();
        $unpaid = $model->where('pay_status', 0)->count();
        return compact('total', 'paid', 'unpaid');
    }

    /**
     * @Notes: 调试数据类型
     * @Author: system
     * @return array
     */
    public static function debugDataTypes()
    {
        try {
            $model = new JcaiOrder();
            $sample = $model->alias('O')
                ->field(['O.*', 'U.nickname', 'U.sn as user_sn', 'U.avatar'])
                ->join('user U', 'U.id = O.user_id')
                ->limit(1)
                ->find();

            if ($sample) {
                $debug = [];
                foreach ($sample->toArray() as $key => $value) {
                    $debug[$key] = [
                        'value' => $value,
                        'type' => gettype($value),
                        'is_numeric' => is_numeric($value)
                    ];
                }
                return $debug;
            }
            return ['message' => '没有找到数据'];
        } catch (Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }
}
