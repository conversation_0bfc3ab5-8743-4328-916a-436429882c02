(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-sessionlist-sessionlist"],{"0003":function(e,t,i){"use strict";i("6a54");var a=i("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=a(i("2634")),n=a(i("2fdc"));i("64aa");var r=a(i("3918")),l=a(i("a674")),s={props:{loadingMoreCustomStyle:{type:Object,default:r.default.gc("loadingMoreCustomStyle",{})},loadingMoreTitleCustomStyle:{type:Object,default:r.default.gc("loadingMoreTitleCustomStyle",{})},loadingMoreLoadingIconCustomStyle:{type:Object,default:r.default.gc("loadingMoreLoadingIconCustomStyle",{})},loadingMoreLoadingIconType:{type:String,default:r.default.gc("loadingMoreLoadingIconType","flower")},loadingMoreLoadingIconCustomImage:{type:String,default:r.default.gc("loadingMoreLoadingIconCustomImage","")},loadingMoreLoadingAnimated:{type:Boolean,default:r.default.gc("loadingMoreLoadingAnimated",!0)},loadingMoreEnabled:{type:Boolean,default:r.default.gc("loadingMoreEnabled",!0)},toBottomLoadingMoreEnabled:{type:Boolean,default:r.default.gc("toBottomLoadingMoreEnabled",!0)},loadingMoreDefaultAsLoading:{type:Boolean,default:r.default.gc("loadingMoreDefaultAsLoading",!1)},loadingMoreDefaultText:{type:[String,Object],default:r.default.gc("loadingMoreDefaultText",null)},loadingMoreLoadingText:{type:[String,Object],default:r.default.gc("loadingMoreLoadingText",null)},loadingMoreNoMoreText:{type:[String,Object],default:r.default.gc("loadingMoreNoMoreText",null)},loadingMoreFailText:{type:[String,Object],default:r.default.gc("loadingMoreFailText",null)},hideNoMoreInside:{type:Boolean,default:r.default.gc("hideNoMoreInside",!1)},hideNoMoreByLimit:{type:Number,default:r.default.gc("hideNoMoreByLimit",0)},showDefaultLoadingMoreText:{type:Boolean,default:r.default.gc("showDefaultLoadingMoreText",!0)},showLoadingMoreNoMoreView:{type:Boolean,default:r.default.gc("showLoadingMoreNoMoreView",!0)},showLoadingMoreNoMoreLine:{type:Boolean,default:r.default.gc("showLoadingMoreNoMoreLine",!0)},loadingMoreNoMoreLineCustomStyle:{type:Object,default:r.default.gc("loadingMoreNoMoreLineCustomStyle",{})},insideMore:{type:Boolean,default:r.default.gc("insideMore",!1)},lowerThreshold:{type:[Number,String],default:r.default.gc("lowerThreshold","100rpx")}},data:function(){return{M:l.default.More,loadingStatus:l.default.More.Default,loadingStatusAfterRender:l.default.More.Default,loadingMoreTimeStamp:0,loadingMoreDefaultSlot:null,showLoadingMore:!1,customNoMore:-1}},computed:{zLoadMoreConfig:function(){return{status:this.loadingStatusAfterRender,defaultAsLoading:this.loadingMoreDefaultAsLoading||this.useChatRecordMode&&this.chatLoadingMoreDefaultAsLoading,defaultThemeStyle:this.finalLoadingMoreThemeStyle,customStyle:this.loadingMoreCustomStyle,titleCustomStyle:this.loadingMoreTitleCustomStyle,iconCustomStyle:this.loadingMoreLoadingIconCustomStyle,loadingIconType:this.loadingMoreLoadingIconType,loadingIconCustomImage:this.loadingMoreLoadingIconCustomImage,loadingAnimated:this.loadingMoreLoadingAnimated,showNoMoreLine:this.showLoadingMoreNoMoreLine,noMoreLineCustomStyle:this.loadingMoreNoMoreLineCustomStyle,defaultText:this.finalLoadingMoreDefaultText,loadingText:this.finalLoadingMoreLoadingText,noMoreText:this.finalLoadingMoreNoMoreText,failText:this.finalLoadingMoreFailText,hideContent:!this.loadingMoreDefaultAsLoading&&this.listRendering,unit:this.unit,isChat:this.useChatRecordMode,chatDefaultAsLoading:this.chatLoadingMoreDefaultAsLoading}},finalLoadingMoreThemeStyle:function(){return this.loadingMoreThemeStyle.length?this.loadingMoreThemeStyle:this.defaultThemeStyle},finalLowerThreshold:function(){return r.default.convertToPx(this.lowerThreshold)},showLoadingMoreDefault:function(){return this._showLoadingMore("Default")},showLoadingMoreLoading:function(){return this._showLoadingMore("Loading")},showLoadingMoreNoMore:function(){return this._showLoadingMore("NoMore")},showLoadingMoreFail:function(){return this._showLoadingMore("Fail")},showLoadingMoreCustom:function(){return this._showLoadingMore("Custom")},loadingMoreFixedHeight:function(){return r.default.addUnit("80rpx",this.unit)}},methods:{pageReachBottom:function(){!this.useChatRecordMode&&this.toBottomLoadingMoreEnabled&&this._onLoadingMore("toBottom")},doLoadMore:function(e){this._onLoadingMore(e)},_checkScrolledToBottom:function(e){var t=this,i=arguments.length>1&&void 0!==arguments[1]&&arguments[1];-1===this.cacheScrollNodeHeight?this._getNodeClientRect(".zp-scroll-view").then((function(i){if(i){var a=i[0].height;t.cacheScrollNodeHeight=a,e-a<=t.finalLowerThreshold&&t._onLoadingMore("toBottom")}})):(e-this.cacheScrollNodeHeight<=this.finalLowerThreshold?this._onLoadingMore("toBottom"):e-this.cacheScrollNodeHeight<=500&&!i&&r.default.delay((function(){t._getNodeClientRect(".zp-scroll-view",!0,!0).then((function(e){if(e){t.oldScrollTop=e[0].scrollTop;var i=e[0].scrollHeight-t.oldScrollTop;t._checkScrolledToBottom(i,!0)}}))}),150,"checkScrolledToBottomDelay"),this.oldScrollTop<=150&&0!==this.oldScrollTop&&r.default.delay((function(){0!==t.oldScrollTop&&t._getNodeClientRect(".zp-scroll-view",!0,!0).then((function(e){e&&0===e[0].scrollTop&&0!==t.oldScrollTop&&t._onScrollToUpper()}))}),150,"checkScrolledToTopDelay"))},_onLoadingMore:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"click";this.isIos&&"toBottom"===t&&!this.scrollToBottomBounceEnabled&&this.scrollEnable&&(this.scrollEnable=!1,this.$nextTick((function(){e.scrollEnable=!0}))),this._emitScrollEvent("scrolltolower"),this.isOnly||!this.loadingMoreEnabled||this.loadingStatus!==l.default.More.Default&&this.loadingStatus!==l.default.More.Fail||this.loading||this.showEmpty||this._doLoadingMore()},_doLoadingMore:function(){var e=this;this.pageNo>=this.defaultPageNo&&this.loadingStatus!==l.default.More.NoMore&&(this.pageNo++,this._startLoading(!1),this.isLocalPaging?this._localPagingQueryList(this.pageNo,this.defaultPageSize,this.localPagingLoadingTime,(function(t){e.completeByTotal(t,e.totalLocalPagingList.length),e.queryFrom=l.default.QueryFrom.LoadMore})):(this._emitQuery(this.pageNo,this.defaultPageSize,l.default.QueryFrom.LoadMore),this._callMyParentQuery()),this.loadingType=l.default.LoadingType.LoadMore)},_preCheckShowNoMoreInside:function(e,t,i){var a=this;this.loadingStatus===l.default.More.NoMore&&this.hideNoMoreByLimit>0&&e.length?this.showLoadingMore=e.length>this.hideNoMoreByLimit:this.loadingStatus===l.default.More.NoMore&&this.hideNoMoreInside&&e.length||this.insideMore&&!1!==this.insideOfPaging&&e.length?(this.$nextTick((function(){a._checkShowNoMoreInside(e,t,i)})),this.insideMore&&!1!==this.insideOfPaging&&e.length&&(this.showLoadingMore=e.length)):this.showLoadingMore=e.length},_checkShowNoMoreInside:function(e,t,i){var a=this;return(0,n.default)((0,o.default)().mark((function n(){var r,l,s,d,h;return(0,o.default)().wrap((function(o){while(1)switch(o.prev=o.next){case 0:if(o.prev=0,o.t0=t,o.t0){o.next=6;break}return o.next=5,a._getNodeClientRect(".zp-scroll-view");case 5:o.t0=o.sent;case 6:if(r=o.t0,!a.usePageScroll){o.next=11;break}r&&(l=r[0].top+r[0].height,a.insideOfPaging=l<a.windowHeight,a.hideNoMoreInside&&(a.showLoadingMore=!a.insideOfPaging),a._updateInsideOfPaging()),o.next=22;break;case 11:if(o.t1=i,o.t1){o.next=16;break}return o.next=15,a._getNodeClientRect(".zp-paging-container-content");case 15:o.t1=o.sent;case 16:s=o.t1,d=s?s[0].height:0,h=r?r[0].height:0,a.insideOfPaging=d<h,a.hideNoMoreInside&&(a.showLoadingMore=!a.insideOfPaging),a._updateInsideOfPaging();case 22:o.next=29;break;case 24:o.prev=24,o.t2=o["catch"](0),a.insideOfPaging=!e.length,a.hideNoMoreInside&&(a.showLoadingMore=!a.insideOfPaging),a._updateInsideOfPaging();case 29:case"end":return o.stop()}}),n,null,[[0,24]])})))()},_showLoadingMore:function(e){if(!this.showLoadingMoreWhenReload&&(this.loadingStatus===l.default.More.Default&&!this.nShowBottom||!this.realTotalData.length))return!1;if((!this.showLoadingMoreWhenReload||this.isUserPullDown||this.loadingStatus!==l.default.More.Loading)&&!this.showLoadingMore||!this.loadingMoreEnabled&&(!this.showLoadingMoreWhenReload||this.isUserPullDown||this.loadingStatus!==l.default.More.Loading)||this.isOnly)return!1;if(this.useChatRecordMode&&"Loading"!==e)return!1;if(!this.zSlots)return!1;if("Custom"===e)return this.showDefaultLoadingMoreText&&!(this.loadingStatus===l.default.More.NoMore&&!this.showLoadingMoreNoMoreView);var t=this.loadingStatus===l.default.More[e]&&this.zSlots["loadingMore".concat(e)]&&("NoMore"!==e||this.showLoadingMoreNoMoreView);return t}}};t.default=s},"09d1":function(e,t,i){"use strict";i("6a54");var a=i("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,i("64aa");var o=a(i("a126")),n={name:"z-paging-empty-view",data:function(){return{}},props:{emptyViewText:{type:String,default:"没有数据哦~"},emptyViewImg:{type:String,default:""},showEmptyViewReload:{type:Boolean,default:!1},emptyViewReloadText:{type:String,default:"重新加载"},isLoadFailed:{type:Boolean,default:!1},emptyViewStyle:{type:Object,default:function(){return{}}},emptyViewImgStyle:{type:Object,default:function(){return{}}},emptyViewTitleStyle:{type:Object,default:function(){return{}}},emptyViewReloadStyle:{type:Object,default:function(){return{}}},emptyViewZIndex:{type:Number,default:9},emptyViewFixed:{type:Boolean,default:!0},unit:{type:String,default:"rpx"}},computed:{emptyImg:function(){return this.isLoadFailed?o.default.base64Error:o.default.base64Empty},finalEmptyViewStyle:function(){return this.emptyViewStyle["z-index"]=this.emptyViewZIndex,this.emptyViewStyle}},methods:{reloadClick:function(){this.$emit("reload")},emptyViewClick:function(){this.$emit("viewClick")}}};t.default=n},"0d0b":function(e){e.exports=JSON.parse('{"zp.refresher.default":"繼續下拉重繪","zp.refresher.pulling":"鬆開立即重繪","zp.refresher.refreshing":"正在重繪...","zp.refresher.complete":"重繪成功","zp.refresher.f2":"鬆手進入二樓","zp.loadingMore.default":"點擊加載更多","zp.loadingMore.loading":"正在加載...","zp.loadingMore.noMore":"沒有更多了","zp.loadingMore.fail":"加載失敗，點擊重新加載","zp.emptyView.title":"沒有數據哦~","zp.emptyView.reload":"重新加載","zp.emptyView.error":"很抱歉，加載失敗","zp.refresherUpdateTime.title":"最後更新：","zp.refresherUpdateTime.none":"無","zp.refresherUpdateTime.today":"今天","zp.refresherUpdateTime.yesterday":"昨天","zp.systemLoading.title":"加載中..."}')},"0d8a":function(e,t,i){"use strict";i("6a54");var a=i("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=a(i("39d8")),n=a(i("a126")),r=a(i("a674")),l={name:"z-paging-load-more",data:function(){return{M:r.default.More,zTheme:{title:{white:"#efefef",black:"#a4a4a4"},line:{white:"#efefef",black:"#eeeeee"},circleBorder:{white:"#aaaaaa",black:"#c8c8c8"},circleBorderTop:{white:"#ffffff",black:"#444444"},flower:{white:n.default.base64FlowerWhite,black:n.default.base64Flower},indicator:{white:"#eeeeee",black:"#777777"}}}},props:["zConfig"],computed:{ts:function(){return this.c.defaultThemeStyle},c:function(){return this.zConfig||{}},ownLoadingMoreText:function(){var e;return(e={},(0,o.default)(e,this.M.Default,this.c.defaultText),(0,o.default)(e,this.M.Loading,this.c.loadingText),(0,o.default)(e,this.M.NoMore,this.c.noMoreText),(0,o.default)(e,this.M.Fail,this.c.failText),e)[this.finalStatus]},finalStatus:function(){return this.c.defaultAsLoading&&this.c.status===this.M.Default?this.M.Loading:this.c.status},finalLoadingIconType:function(){return this.c.loadingIconType}},methods:{doClick:function(){this.$emit("doClick")}}};t.default=l},"15d19":function(e,t,i){"use strict";i("6a54");var a=i("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,i("64aa"),i("5c47"),i("a1c1"),i("f7a5");var o=a(i("2634")),n=a(i("2fdc")),r=a(i("3918")),l=a(i("a674")),s={props:{usePageScroll:{type:Boolean,default:r.default.gc("usePageScroll",!1)},scrollable:{type:Boolean,default:r.default.gc("scrollable",!0)},showScrollbar:{type:Boolean,default:r.default.gc("showScrollbar",!0)},scrollX:{type:Boolean,default:r.default.gc("scrollX",!1)},scrollToTopBounceEnabled:{type:Boolean,default:r.default.gc("scrollToTopBounceEnabled",!1)},scrollToBottomBounceEnabled:{type:Boolean,default:r.default.gc("scrollToBottomBounceEnabled",!0)},scrollWithAnimation:{type:Boolean,default:r.default.gc("scrollWithAnimation",!1)},scrollIntoView:{type:String,default:r.default.gc("scrollIntoView","")}},data:function(){return{scrollTop:0,oldScrollTop:0,scrollLeft:0,oldScrollLeft:0,scrollViewStyle:{},scrollViewContainerStyle:{},scrollViewInStyle:{},pageScrollTop:-1,scrollEnable:!0,privateScrollWithAnimation:-1,cacheScrollNodeHeight:-1,superContentHeight:0,lastScrollHeight:0,lastScrollDirection:"",setContentHeightPending:!1}},watch:{oldScrollTop:function(e){!this.usePageScroll&&this._scrollTopChange(e,!1)},pageScrollTop:function(e){this.usePageScroll&&this._scrollTopChange(e,!0)},usePageScroll:{handler:function(e){var t=this;this.loaded&&this.autoHeight&&this._setAutoHeight(!e),e&&this.$nextTick((function(){var e=t.$refs["zp-scroll-view"].$refs.main;e&&(e.style={})}))},immediate:!0},finalScrollTop:function(e){this.renderPropScrollTop=e<6?0:10}},computed:{finalScrollWithAnimation:function(){return-1!==this.privateScrollWithAnimation?1===this.privateScrollWithAnimation:this.scrollWithAnimation},finalScrollViewStyle:function(){return 1!=this.superContentZIndex&&(this.scrollViewStyle["z-index"]=this.superContentZIndex,this.scrollViewStyle["position"]="relative"),this.scrollViewStyle},finalScrollTop:function(){return this.usePageScroll?this.pageScrollTop:this.oldScrollTop},finalIsOldWebView:function(){return this.isOldWebView&&!this.usePageScroll},finalScrollable:function(){return this.scrollable&&!this.usePageScroll&&this.scrollEnable&&(!!this.refresherCompleteScrollable||this.refresherStatus!==l.default.Refresher.Complete)&&(!!this.refresherRefreshingScrollable||this.refresherStatus!==l.default.Refresher.Loading)}},methods:{scrollToTop:function(e){var t=this,i=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];this.useChatRecordMode&&i&&!this.isChatRecordModeAndNotInversion?this.scrollToBottom(e,!1):this.$nextTick((function(){t._scrollToTop(e,!1)}))},scrollToBottom:function(e){var t=this,i=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];this.useChatRecordMode&&i&&!this.isChatRecordModeAndNotInversion?this.scrollToTop(e,!1):this.$nextTick((function(){t._scrollToBottom(e)}))},scrollIntoViewById:function(e,t,i){this._scrollIntoView(e,t,i)},scrollIntoViewByNodeTop:function(e,t,i){var a=this;this.scrollTop=this.oldScrollTop,this.$nextTick((function(){a._scrollIntoViewByNodeTop(e,t,i)}))},scrollToY:function(e,t,i){var a=this;this.scrollTop=this.oldScrollTop,this.$nextTick((function(){a._scrollToY(e,t,i)}))},scrollToX:function(e,t,i){var a=this;this.scrollLeft=this.oldScrollLeft,this.$nextTick((function(){a._scrollToX(e,t,i)}))},scrollIntoViewByIndex:function(e,t,i){var a=this;e>=this.realTotalData.length?r.default.consoleErr("当前滚动的index超出已渲染列表长度，请先通过refreshToPage加载到对应index页并等待渲染成功后再调用此方法！"):this.$nextTick((function(){if(a.finalUseVirtualList){var o=a.cellHeightMode===l.default.CellHeightMode.Fixed;r.default.delay((function(){if(a.finalUseVirtualList){var n=o?a.virtualCellHeight*e:a.virtualHeightCacheList[e].lastTotalHeight;a.scrollToY(n,t,i)}}),o?0:100)}}))},scrollIntoViewByView:function(e,t,i){this._scrollIntoView(e,t,i)},updatePageScrollTop:function(e){this.pageScrollTop=e},updatePageScrollTopHeight:function(){this._updatePageScrollTopOrBottomHeight("top")},updatePageScrollBottomHeight:function(){this._updatePageScrollTopOrBottomHeight("bottom")},updateLeftAndRightWidth:function(){var e=this;this.finalIsOldWebView&&this.$nextTick((function(){return e._updateLeftAndRightWidth(e.scrollViewContainerStyle,"zp-page")}))},updateScrollViewScrollTop:function(e){var t=this,i=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];this._updatePrivateScrollWithAnimation(i),this.scrollTop=this.oldScrollTop,this.$nextTick((function(){t.scrollTop=e,t.oldScrollTop=t.scrollTop}))},_onScrollToUpper:function(){var e=this;this._emitScrollEvent("scrolltoupper"),this.$emit("scrollTopChange",0),this.$nextTick((function(){e.oldScrollTop=0}))},_onScrollToLower:function(e){(!e.detail||!e.detail.direction||"bottom"===e.detail.direction)&&this.toBottomLoadingMoreEnabled&&this._onLoadingMore(this.useChatRecordMode?"click":"toBottom")},_scrollToTop:function(){var e=this,t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];this.usePageScroll?this.$nextTick((function(){uni.pageScrollTo({scrollTop:0,duration:t?100:0})})):(this._updatePrivateScrollWithAnimation(t),this.scrollTop=this.oldScrollTop,this.$nextTick((function(){e.scrollTop=0,e.oldScrollTop=e.scrollTop})))},_scrollToBottom:function(){var e=arguments,t=this;return(0,n.default)((0,o.default)().mark((function i(){var a,n,r,l,s;return(0,o.default)().wrap((function(i){while(1)switch(i.prev=i.next){case 0:if(a=!(e.length>0&&void 0!==e[0])||e[0],!t.usePageScroll){i.next=4;break}return t.$nextTick((function(){uni.pageScrollTo({scrollTop:Number.MAX_VALUE,duration:a?100:0})})),i.abrupt("return");case 4:return i.prev=4,t._updatePrivateScrollWithAnimation(a),i.next=8,t._getNodeClientRect(".zp-paging-container");case 8:return n=i.sent,i.next=11,t._getNodeClientRect(".zp-scroll-view");case 11:r=i.sent,l=n?n[0].height:0,s=r?r[0].height:0,l>s&&(t.scrollTop=t.oldScrollTop,t.$nextTick((function(){t.scrollTop=l-s+t.virtualPlaceholderTopHeight,t.oldScrollTop=t.scrollTop}))),i.next=19;break;case 17:i.prev=17,i.t0=i["catch"](4);case 19:case"end":return i.stop()}}),i,null,[[4,17]])})))()},_scrollIntoView:function(e){var t=this,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,a=arguments.length>2&&void 0!==arguments[2]&&arguments[2],o=arguments.length>3?arguments[3]:void 0;try{this.scrollTop=this.oldScrollTop,this.$nextTick((function(){t._getNodeClientRect("#"+e.replace("#",""),!1).then((function(n){n?t._getNodeClientRect(".zp-scroll-view-container").then((function(e){e&&(t._scrollIntoViewByNodeTop(n[0].top-e[0].top,i,a),o&&o())})):r.default.consoleErr("无法获取".concat(e,"的节点信息，请检查！"))}))}))}catch(n){}},_scrollIntoViewByNodeTop:function(e){var t=this,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,a=arguments.length>2&&void 0!==arguments[2]&&arguments[2];this.isChatRecordModeAndInversion?this._getNodeClientRect(".zp-scroll-view").then((function(o){o&&t._scrollToY(o[0].height-e,i,a,!0)})):this._scrollToY(e,i,a,!0)},_scrollToY:function(e){var t=this,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,a=arguments.length>2&&void 0!==arguments[2]&&arguments[2],o=arguments.length>3&&void 0!==arguments[3]&&arguments[3];this._updatePrivateScrollWithAnimation(a),r.default.delay((function(){if(t.usePageScroll){o&&-1!==t.pageScrollTop&&(e+=t.pageScrollTop);var n=e-i;uni.pageScrollTo({scrollTop:n,duration:a?100:0})}else o&&(e+=t.oldScrollTop),t.scrollTop=e-i}),10)},_scrollToX:function(e){var t=this,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,a=arguments.length>2&&void 0!==arguments[2]&&arguments[2];this._updatePrivateScrollWithAnimation(a),r.default.delay((function(){t.usePageScroll?r.default.consoleErr("使用页面滚动时不支持scrollToX"):t.scrollLeft=e-i}),10)},_scroll:function(e){var t=this;this.$emit("scroll",e);var i=e.detail,a=i.scrollTop,o=i.scrollLeft,n=i.scrollHeight;if(this.watchScrollDirectionChange){var l=this.oldScrollTop>a?"top":"bottom";(a<=0||!this.scrollEnable)&&(l="top"),a>this.lastScrollHeight-this.scrollViewHeight-1&&this.scrollEnable&&(l="bottom"),l!==this.lastScrollDirection&&(this.$emit("scrollDirectionChange",l),this.lastScrollDirection=l),this.lastScrollHeight===n||this.setContentHeightPending||(this.setContentHeightPending=!0,r.default.delay((function(){t.lastScrollHeight=n,t.setContentHeightPending=!1})))}this.finalUseVirtualList&&this._updateVirtualScroll(a,this.oldScrollTop-a),this.oldScrollTop=a,this.oldScrollLeft=o;var s=e.detail.scrollHeight-this.oldScrollTop;!this.isIos&&this._checkScrolledToBottom(s)},_emitScrollEvent:function(e){var t="scrolltolower"===e?"scrolltoupper":"scrolltolower",i=this.useChatRecordMode&&!this.isChatRecordModeAndNotInversion?t:e;this.$emit(i)},_updatePrivateScrollWithAnimation:function(e){var t=this;this.privateScrollWithAnimation=e?1:0,r.default.delay((function(){return t.$nextTick((function(){t.privateScrollWithAnimation=-1}))}),100,"updateScrollWithAnimationDelay")},_doCheckScrollViewShouldFullHeight:function(e){var t=this;this.autoFullHeight&&this.usePageScroll&&this.isTotalChangeFromAddData?this.$nextTick((function(){t._checkScrollViewShouldFullHeight((function(i,a){t._preCheckShowNoMoreInside(e,i,a)}))})):this._preCheckShowNoMoreInside(e)},_checkScrollViewShouldFullHeight:function(e){var t=this;return(0,n.default)((0,o.default)().mark((function i(){var a,n,r,l;return(0,o.default)().wrap((function(i){while(1)switch(i.prev=i.next){case 0:return i.prev=0,i.next=3,t._getNodeClientRect(".zp-scroll-view");case 3:return a=i.sent,i.next=6,t._getNodeClientRect(".zp-paging-container-content");case 6:if(n=i.sent,a&&n){i.next=9;break}return i.abrupt("return");case 9:r=n[0].height,l=a[0].top,t.isAddedData&&r+l<=t.windowHeight?(t._setAutoHeight(!0,a),e(a,n)):(t._setAutoHeight(!1),e(null,null)),i.next=17;break;case 14:i.prev=14,i.t0=i["catch"](0),e(null,null);case 17:case"end":return i.stop()}}),i,null,[[0,14]])})))()},_updateCachedSuperContentHeight:function(){var e=this;return(0,n.default)((0,o.default)().mark((function t(){var i;return(0,o.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e._getNodeClientRect(".z-paging-content");case 2:i=t.sent,i&&(e.superContentHeight=i[0].height);case 4:case"end":return t.stop()}}),t)})))()},_scrollTopChange:function(e,t){this.$emit("scrollTopChange",e),this.$emit("update:scrollTop",e),this._checkShouldShowBackToTop(e);var i=e>5?6:0;t&&this.wxsPageScrollTop!==i?this.wxsPageScrollTop=i:t||this.wxsScrollTop===i||(this.wxsScrollTop=i,i>6&&(this.scrollEnable=!0))},_updatePageScrollTopOrBottomHeight:function(e){var t=this;if(this.usePageScroll){this._doCheckScrollViewShouldFullHeight(this.realTotalData);var i=".zp-page-".concat(e),a="margin".concat(e.slice(0,1).toUpperCase()+e.slice(1)),o=this.safeAreaInsetBottom&&!this.zSlots.bottom&&!this.useSafeAreaPlaceholder;this.$nextTick((function(){r.default.delay((function(){t._getNodeClientRect(i).then((function(i){if(i){var n=i[0].height;"bottom"===e?o&&(n+=t.safeAreaBottom):t.cacheTopHeight=n,t.$set(t.scrollViewStyle,a,"".concat(n,"px"))}else o&&t.$set(t.scrollViewStyle,a,"".concat(t.safeAreaBottom,"px"))}))}),0)}))}}}};t.default=s},"1ea2":function(e,t,i){"use strict";var a=i("af9e"),o=i("1c06"),n=i("ada5"),r=i("5d6e"),l=Object.isExtensible,s=a((function(){l(1)}));e.exports=s||r?function(e){return!!o(e)&&((!r||"ArrayBuffer"!==n(e))&&(!l||l(e)))}:l},2087:function(e,t,i){"use strict";i("6a54");var a=i("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=a(i("2634")),n=a(i("2fdc"));i("64aa"),i("dd2b"),i("bf0f"),i("c223"),i("f7a5"),i("aa9c"),i("5ef2"),i("e966");var r=a(i("3918")),l=a(i("2a2e")),s=a(i("a674")),d={props:{useVirtualList:{type:Boolean,default:r.default.gc("useVirtualList",!1)},useCompatibilityMode:{type:Boolean,default:r.default.gc("useCompatibilityMode",!1)},extraData:{type:Object,default:r.default.gc("extraData",{})},useInnerList:{type:Boolean,default:r.default.gc("useInnerList",!1)},forceCloseInnerList:{type:Boolean,default:r.default.gc("forceCloseInnerList",!1)},cellKeyName:{type:String,default:r.default.gc("cellKeyName","")},innerListStyle:{type:Object,default:r.default.gc("innerListStyle",{})},innerCellStyle:{type:Object,default:r.default.gc("innerCellStyle",{})},preloadPage:{type:[Number,String],default:r.default.gc("preloadPage",12),validator:function(e){return e<=0&&r.default.consoleErr("preload-page必须大于0！"),e>0}},cellHeightMode:{type:String,default:r.default.gc("cellHeightMode",s.default.CellHeightMode.Fixed)},fixedCellHeight:{type:[Number,String],default:r.default.gc("fixedCellHeight",0)},virtualListCol:{type:[Number,String],default:r.default.gc("virtualListCol",1)},virtualScrollFps:{type:[Number,String],default:r.default.gc("virtualScrollFps",80)},virtualCellIdPrefix:{type:String,default:r.default.gc("virtualCellIdPrefix","")},virtualInSwiperSlot:{type:Boolean,default:!1}},data:function(){return{virtualListKey:r.default.getInstanceId(),virtualCellHeight:0,virtualScrollTimeStamp:0,virtualList:[],virtualPlaceholderTopHeight:0,virtualPlaceholderBottomHeight:0,virtualTopRangeIndex:0,virtualBottomRangeIndex:0,lastVirtualTopRangeIndex:0,lastVirtualBottomRangeIndex:0,virtualItemInsertedCount:0,virtualHeightCacheList:[],getCellHeightRetryCount:{fixed:0,dynamic:0},updateVirtualListFromDataChange:!1}},watch:{realTotalData:function(){this.updateVirtualListRender()},virtualList:function(e){this.$emit("update:virtualList",e),this.$emit("virtualListChange",e)},virtualPlaceholderTopHeight:function(e){this.$emit("virtualTopHeightChange",e)}},computed:{virtualCellIndexKey:function(){return l.default.listCellIndexKey},finalUseVirtualList:function(){return this.useVirtualList&&this.usePageScroll&&r.default.consoleErr("使用页面滚动时，开启虚拟列表无效！"),this.useVirtualList&&!this.usePageScroll},finalUseInnerList:function(){return this.useInnerList||this.finalUseVirtualList&&!this.forceCloseInnerList},finalCellKeyName:function(){return this.cellKeyName},finalVirtualPageHeight:function(){return this.scrollViewHeight>0?this.scrollViewHeight:this.windowHeight},finalFixedCellHeight:function(){return r.default.convertToPx(this.fixedCellHeight)},fianlVirtualCellIdPrefix:function(){var e=this.virtualCellIdPrefix?this.virtualCellIdPrefix+"-":"";return e+"zp-id"},finalPlaceholderTopHeightStyle:function(){return{transform:this.virtualPlaceholderTopHeight>0?"translateY(".concat(this.virtualPlaceholderTopHeight,"px)"):"none"}},virtualRangePageHeight:function(){return this.finalVirtualPageHeight*this.preloadPage},virtualScrollDisTimeStamp:function(){return 1e3/this.virtualScrollFps}},methods:{doInsertVirtualListItem:function(e,t){var i=this;if(this.cellHeightMode===s.default.CellHeightMode.Dynamic){this.realTotalData.splice(t,0,e),this.virtualItemInsertedCount++,e&&"[object Object]"===Object.prototype.toString.call(e)||(e={item:e});var a=this.virtualCellIndexKey;e[a]="custom-".concat(this.virtualItemInsertedCount),e[l.default.listCellIndexUniqueKey]="".concat(this.virtualListKey,"-").concat(e[a]),this.$nextTick((0,n.default)((0,o.default)().mark((function n(){var s,d,h,u,c,f,g;return(0,o.default)().wrap((function(o){while(1)switch(o.prev=o.next){case 0:s=0;case 1:if(!(s<=10)){o.next=19;break}return o.next=4,r.default.wait(l.default.delayTime);case 4:return o.next=6,i._getVirtualCellNodeByIndex(e[a]);case 6:if(d=o.sent,d){o.next=10;break}return s++,o.abrupt("continue",1);case 10:for(h=d?d[0].height:0,u=i.virtualHeightCacheList[t-1],c=u?u.totalHeight:0,i.virtualHeightCacheList.splice(t,0,{height:h,lastTotalHeight:c,totalHeight:c+h}),f=t+1;f<i.virtualHeightCacheList.length;f++)g=i.virtualHeightCacheList[f],g.lastTotalHeight+=h,g.totalHeight+=h;return i._updateVirtualScroll(i.oldScrollTop),o.abrupt("break",19);case 19:case"end":return o.stop()}}),n)}))))}},didUpdateVirtualListCell:function(e){var t=this;if(this.cellHeightMode===s.default.CellHeightMode.Dynamic){var i=this.virtualHeightCacheList[e];this.$nextTick((function(){t._getVirtualCellNodeByIndex(e).then((function(a){var o=a?a[0].height:0,n=o-i.height;i.height=o,i.totalHeight=i.lastTotalHeight+o;for(var r=e+1;r<t.virtualHeightCacheList.length;r++){var l=t.virtualHeightCacheList[r];l.totalHeight+=n,l.lastTotalHeight+=n}}))}))}},didDeleteVirtualListCell:function(e){if(this.cellHeightMode===s.default.CellHeightMode.Dynamic){for(var t=this.virtualHeightCacheList[e],i=e+1;i<this.virtualHeightCacheList.length;i++){var a=this.virtualHeightCacheList[i];a.totalHeight-=t.height,a.lastTotalHeight-=t.height}this.virtualHeightCacheList.splice(e,1)}},updateVirtualListRender:function(){var e=this;this.finalUseVirtualList&&(this.updateVirtualListFromDataChange=!0,this.$nextTick((function(){e.getCellHeightRetryCount.fixed=0,e.realTotalData.length?e.cellHeightMode===s.default.CellHeightMode.Fixed&&e.isFirstPage&&e._updateFixedCellHeight():e._resetDynamicListState(!e.isUserPullDown),e._updateVirtualScroll(e.oldScrollTop)})))},_updateFixedCellHeight:function(){var e=this;this.finalFixedCellHeight?this.virtualCellHeight=this.finalFixedCellHeight:this.$nextTick((function(){r.default.delay((function(){e._getVirtualCellNodeByIndex(0).then((function(t){if(t)e.virtualCellHeight=t[0].height,e._updateVirtualScroll(e.oldScrollTop);else{if(e.getCellHeightRetryCount.fixed>10)return;e.getCellHeightRetryCount.fixed++,e._updateFixedCellHeight()}}))}),l.default.delayTime,"updateFixedCellHeightDelay")}))},_updateDynamicCellHeight:function(e){var t=this,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"bottom",a="top"===i,s=this.virtualHeightCacheList,d=a?[]:s,h=0;this.$nextTick((function(){r.default.delay((0,n.default)((0,o.default)().mark((function n(){var r,l,u,c,f,g,p;return(0,o.default)().wrap((function(o){while(1)switch(o.prev=o.next){case 0:r=0;case 1:if(!(r<e.length)){o.next=16;break}return o.next=4,t._getVirtualCellNodeByIndex(e[r][t.virtualCellIndexKey]);case 4:if(l=o.sent,u=l?l[0].height:0,l){o.next=9;break}return t.getCellHeightRetryCount.dynamic<=10&&(s.splice(s.length-r,r),t.getCellHeightRetryCount.dynamic++,t._updateDynamicCellHeight(e,i)),o.abrupt("return");case 9:c=d.length?d.slice(-1)[0]:null,f=c?c.totalHeight:0,d.push({height:u,lastTotalHeight:f,totalHeight:f+u}),a&&(h+=u);case 13:r++,o.next=1;break;case 16:if(a&&e.length){for(g=0;g<s.length;g++)p=s[g],p.lastTotalHeight+=h,p.totalHeight+=h;t.virtualHeightCacheList=d.concat(s)}t._updateVirtualScroll(t.oldScrollTop);case 18:case"end":return o.stop()}}),n)}))),l.default.delayTime,"updateDynamicCellHeightDelay")}))},_setCellIndex:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"bottom",i=0,a=this.virtualCellIndexKey;if("bottom"===t&&[s.default.QueryFrom.Refresh,s.default.QueryFrom.Reload].indexOf(this.queryFrom)>=0&&this._resetDynamicListState(),this.totalData.length&&this.queryFrom!==s.default.QueryFrom.Refresh){if("bottom"===t){i=this.realTotalData.length;var o=this.realTotalData.length?this.realTotalData.slice(-1)[0]:null;o&&void 0!==o[a]&&(i=o[a]+1)}else if("top"===t){var n=this.realTotalData.length?this.realTotalData[0]:null;n&&void 0!==n[a]&&(i=n[a]-e.length)}}else this._resetDynamicListState();for(var d=0;d<e.length;d++){var h=e[d];h&&"[object Object]"===Object.prototype.toString.call(h)||(h={item:h}),h[l.default.listCellIndexUniqueKey]&&(h=r.default.deepCopy(h)),h[a]=i+d,h[l.default.listCellIndexUniqueKey]="".concat(this.virtualListKey,"-").concat(h[a]),e[d]=h}this.getCellHeightRetryCount.dynamic=0,this.cellHeightMode===s.default.CellHeightMode.Dynamic&&this._updateDynamicCellHeight(e,t)},_updateVirtualScroll:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,i=r.default.getTime();if(0===e&&this._resetTopRange(),!(0!==e&&this.virtualScrollTimeStamp&&i-this.virtualScrollTimeStamp<=this.virtualScrollDisTimeStamp)){this.virtualScrollTimeStamp=i;var a=0,o=this.cellHeightMode;if(o===s.default.CellHeightMode.Fixed)a=parseInt(e/this.virtualCellHeight)||0,this._updateFixedTopRangeIndex(a),this._updateFixedBottomRangeIndex(a);else if(o===s.default.CellHeightMode.Dynamic){var n=t>0?"top":"bottom",l=this.virtualRangePageHeight,d=e-l,h=e+this.finalVirtualPageHeight+l,u=0,c=0,f=!1,g=this.virtualHeightCacheList,p=g?g.slice(-1)[0]:null,m=this.virtualTopRangeIndex;if("bottom"===n)for(var v=m;v<g.length;v++){var y=g[v];if(y&&y.totalHeight>d){this.virtualTopRangeIndex=v,this.virtualPlaceholderTopHeight=y.lastTotalHeight;break}}else{for(var T=!1,w=m;w>=0;w--){var S=g[w];if(S&&S.totalHeight<d){this.virtualTopRangeIndex=w,this.virtualPlaceholderTopHeight=S.lastTotalHeight,T=!0;break}}!T&&this._resetTopRange()}for(var b=this.virtualTopRangeIndex;b<g.length;b++){var x=g[b];if(x&&x.totalHeight>h){u=b,c=p.totalHeight-x.totalHeight,f=!0;break}}f&&0!==this.virtualBottomRangeIndex?(this.virtualBottomRangeIndex=u,this.virtualPlaceholderBottomHeight=c):(this.virtualBottomRangeIndex=this.realTotalData.length?this.realTotalData.length-1:this.pageSize,this.virtualPlaceholderBottomHeight=0),this._updateVirtualList()}}},_updateFixedTopRangeIndex:function(e){var t=0===this.virtualCellHeight?0:e-(parseInt(this.finalVirtualPageHeight/this.virtualCellHeight)||1)*this.preloadPage;t*=this.virtualListCol,t=Math.max(0,t),this.virtualTopRangeIndex=t,this.virtualPlaceholderTopHeight=t/this.virtualListCol*this.virtualCellHeight},_updateFixedBottomRangeIndex:function(e){var t=0===this.virtualCellHeight?this.pageSize:e+(parseInt(this.finalVirtualPageHeight/this.virtualCellHeight)||1)*(this.preloadPage+1);t*=this.virtualListCol,t=Math.min(this.realTotalData.length,t),this.virtualBottomRangeIndex=t,this.virtualPlaceholderBottomHeight=(this.realTotalData.length-t)*this.virtualCellHeight/this.virtualListCol,this._updateVirtualList()},_updateVirtualList:function(){var e=this.updateVirtualListFromDataChange||this.lastVirtualTopRangeIndex!==this.virtualTopRangeIndex||this.lastVirtualBottomRangeIndex!==this.virtualBottomRangeIndex;e&&(this.updateVirtualListFromDataChange=!1,this.lastVirtualTopRangeIndex=this.virtualTopRangeIndex,this.lastVirtualBottomRangeIndex=this.virtualBottomRangeIndex,this.virtualList=this.realTotalData.slice(this.virtualTopRangeIndex,this.virtualBottomRangeIndex+1))},_resetDynamicListState:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.virtualHeightCacheList=[],e&&(this.virtualList=[]),this.virtualTopRangeIndex=0,this.virtualPlaceholderTopHeight=0},_resetTopRange:function(){this.virtualTopRangeIndex=0,this.virtualPlaceholderTopHeight=0,this._updateVirtualList()},_checkVirtualListScroll:function(){var e=this;this.finalUseVirtualList&&this.$nextTick((function(){e._getNodeClientRect(".zp-paging-touch-view").then((function(t){var i=t?t[0].top:0;(!t||i===e.pagingOrgTop&&0!==e.virtualPlaceholderTopHeight)&&e._updateVirtualScroll(0)}))}))},_getVirtualCellNodeByIndex:function(e){var t=this.finalUseInnerList;return this._getNodeClientRect("#".concat(this.fianlVirtualCellIdPrefix,"-").concat(e),t)},_innerCellClick:function(e,t){this.$emit("innerCellClick",e,t)}}};t.default=d},2720:function(e,t,i){"use strict";var a=i("5ab9"),o=i.n(a);o.a},2843:function(e,t,i){"use strict";i("6a54");var a=i("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=a(i("bd07")),n=a(i("e1f9")),r=a(i("0d0b")),l={en:o.default,"zh-Hans":n.default,"zh-Hant":r.default};t.default=l},"2a2e":function(e,t,i){"use strict";i("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default={version:"2.8.7",delayTime:100,errorUpdateKey:"z-paging-error-emit",completeUpdateKey:"z-paging-complete-emit",cachePrefixKey:"z-paging-cache",listCellIndexKey:"zp_index",listCellIndexUniqueKey:"zp_unique_index"}},"2aca":function(e,t,i){"use strict";i.d(t,"b",(function(){return a})),i.d(t,"c",(function(){return o})),i.d(t,"a",(function(){}));var a=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("v-uni-view",{staticStyle:{height:"100%"}},[i("v-uni-view",{class:e.showUpdateTime?"zp-r-container zp-r-container-padding":"zp-r-container"},[i("v-uni-view",{staticClass:"zp-r-left"},[e.status!==e.R.Loading?i("v-uni-image",{class:e.leftImageClass,style:[e.leftImageStyle,e.imgStyle],attrs:{src:e.leftImageSrc}}):i("v-uni-image",{class:{"zp-line-loading-image":e.refreshingAnimated,"zp-r-left-image":!0,"zp-r-left-image-pre-size-rpx":"rpx"===e.unit,"zp-r-left-image-pre-size-px":"px"===e.unit},style:[e.leftImageStyle,e.imgStyle],attrs:{src:e.leftImageSrc}})],1),i("v-uni-view",{staticClass:"zp-r-right"},[i("v-uni-text",{staticClass:"zp-r-right-text",style:[e.rightTextStyle,e.titleStyle]},[e._v(e._s(e.currentTitle))]),e.showUpdateTime&&e.refresherTimeText.length?i("v-uni-text",{staticClass:"zp-r-right-text",class:{"zp-r-right-time-text-rpx":"rpx"===e.unit,"zp-r-right-time-text-px":"px"===e.unit},style:[{color:e.zTheme.title[e.ts]},e.updateTimeStyle]},[e._v(e._s(e.refresherTimeText))]):e._e()],1)],1)],1)},o=[]},"2f21":function(e,t,i){"use strict";i.r(t);var a=i("09d1"),o=i.n(a);for(var n in a)["default"].indexOf(n)<0&&function(e){i.d(t,e,(function(){return a[e]}))}(n);t["default"]=o.a},"2f2a":function(e,t,i){var a=i("c86c");t=a(!1),t.push([e.i,"/* [z-paging]公用的静态css资源 */.zp-line-loading-image[data-v-dff48604]{\n-webkit-animation:loading-flower-data-v-dff48604 1s steps(12) infinite;animation:loading-flower-data-v-dff48604 1s steps(12) infinite;\ncolor:#666}.zp-line-loading-image-rpx[data-v-dff48604]{margin-right:%?8?%;width:%?34?%;height:%?34?%}.zp-line-loading-image-px[data-v-dff48604]{margin-right:4px;width:17px;height:17px}.zp-loading-image-ios-rpx[data-v-dff48604]{width:%?40?%;height:%?40?%}.zp-loading-image-ios-px[data-v-dff48604]{width:20px;height:20px}.zp-loading-image-android-rpx[data-v-dff48604]{width:%?34?%;height:%?34?%}.zp-loading-image-android-px[data-v-dff48604]{width:17px;height:17px}\n@-webkit-keyframes loading-flower-data-v-dff48604{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}@keyframes loading-flower-data-v-dff48604{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}\n.zp-l-container[data-v-dff48604]{\nclear:both;display:flex;\nflex-direction:row;align-items:center;justify-content:center}.zp-l-container-rpx[data-v-dff48604]{height:%?80?%;font-size:%?27?%}.zp-l-container-px[data-v-dff48604]{height:40px;font-size:14px}.zp-l-line-loading-custom-image[data-v-dff48604]{color:#a4a4a4}.zp-l-line-loading-custom-image-rpx[data-v-dff48604]{margin-right:%?8?%;width:%?28?%;height:%?28?%}.zp-l-line-loading-custom-image-px[data-v-dff48604]{margin-right:4px;width:14px;height:14px}.zp-l-line-loading-custom-image-animated[data-v-dff48604]{\n-webkit-animation:loading-circle-data-v-dff48604 1s linear infinite;animation:loading-circle-data-v-dff48604 1s linear infinite\n}.zp-l-circle-loading-view[data-v-dff48604]{border:%?3?% solid #ddd;border-radius:50%;\n-webkit-animation:loading-circle-data-v-dff48604 1s linear infinite;animation:loading-circle-data-v-dff48604 1s linear infinite;\n}.zp-l-circle-loading-view-rpx[data-v-dff48604]{margin-right:%?8?%;width:%?23?%;height:%?23?%}.zp-l-circle-loading-view-px[data-v-dff48604]{margin-right:4px;width:12px;height:12px}.zp-l-text-rpx[data-v-dff48604]{font-size:%?30?%;margin:%?0?% %?6?%}.zp-l-text-px[data-v-dff48604]{font-size:15px;margin:0 3px}.zp-l-line-rpx[data-v-dff48604]{height:1px;width:%?100?%;margin:%?0?% %?10?%}.zp-l-line-px[data-v-dff48604]{height:1px;width:50px;margin:%?0?% 5px}\n@-webkit-keyframes loading-circle-data-v-dff48604{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}@keyframes loading-circle-data-v-dff48604{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}\n\n",""]),e.exports=t},3639:function(e,t,i){i("bf0f"),i("18f7"),i("d0af"),i("de6c"),i("6a54"),i("9a2c");var a=i("bdbb")["default"];function o(e){if("function"!==typeof WeakMap)return null;var t=new WeakMap,i=new WeakMap;return(o=function(e){return e?i:t})(e)}e.exports=function(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==a(e)&&"function"!==typeof e)return{default:e};var i=o(t);if(i&&i.has(e))return i.get(e);var n={},r=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var l in e)if("default"!==l&&Object.prototype.hasOwnProperty.call(e,l)){var s=r?Object.getOwnPropertyDescriptor(e,l):null;s&&(s.get||s.set)?Object.defineProperty(n,l,s):n[l]=e[l]}return n["default"]=e,i&&i.set(e,n),n},e.exports.__esModule=!0,e.exports["default"]=e.exports},"386c":function(e,t,i){"use strict";i.r(t);var a=i("8bcb"),o=i.n(a);for(var n in a)["default"].indexOf(n)<0&&function(e){i.d(t,e,(function(){return a[e]}))}(n);t["default"]=o.a},3918:function(e,t,i){"use strict";i("6a54");var a=i("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=a(i("b7c7")),n=a(i("fcf3")),r=a(i("9b1b"));i("c223"),i("bf0f"),i("5ef2"),i("5c47"),i("a1c1"),i("64aa"),i("473f"),i("f7a5"),i("e838"),i("aa9c"),i("dc8a"),i("c9b5"),i("ab80");var l=a(i("7053")),s=a(i("2a2e")),d="Z-PAGING-REFRESHER-TIME-STORAGE-KEY",h=null,u=!1,c=null,f={};function g(){return uni.getStorageSync(d)}function p(e){return uni.upx2px(e)}function m(){return(new Date).getTime()}function v(e){return e=e.toString(),1===e.length?"0"+e:e}var y={gc:function(e,t){return function(){if(function(){if(u)return;l.default&&Object.keys(l.default).length&&(h=l.default);!h&&uni.$zp&&(h=uni.$zp.config);h=h?Object.keys(h).reduce((function(e,t){return e[function(e){return e.replace(/-([a-z])/g,(function(e,t){return t.toUpperCase()}))}(t)]=h[t],e}),{}):null,u=!0}(),!h)return t;var i=h[e];return void 0===i?t:i}},setRefesrherTime:function(e,t){var i=g()||{};i[t]=e,uni.setStorageSync(d,i)},getRefesrherFormatTimeByKey:function(e,t){var i=function(e){var t=g();return t&&t[e]?t[e]:null}(e),a=i?function(e,t){var i=new Date(e),a=new Date,o=new Date(e).setHours(0,0,0,0),n=(new Date).setHours(0,0,0,0),r=o-n,l="",s=function(e){var t=e.getHours(),i=e.getMinutes();return"".concat(v(t),":").concat(v(i))}(i);l=0===r?t.today:-864e5===r?t.yesterday:function(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],i=e.getFullYear(),a=e.getMonth()+1,o=e.getDate();return t?"".concat(i,"-").concat(v(a),"-").concat(v(o)):"".concat(v(a),"-").concat(v(o))}(i,i.getFullYear()!==a.getFullYear());return"".concat(l," ").concat(s)}(i,t):t.none;return"".concat(t.title).concat(a)},getTouch:function(e){var t=null;if(e.touches&&e.touches.length)t=e.touches[0];else if(e.changedTouches&&e.changedTouches.length)t=e.changedTouches[0];else{if(!e.datail||e.datail=={})return{touchX:0,touchY:0};t=e.datail}return{touchX:t.clientX,touchY:t.clientY}},getTouchFromZPaging:function e(t){if(t&&t.tagName&&"BODY"!==t.tagName&&"UNI-PAGE-BODY"!==t.tagName){var i=t.classList;return i&&i.contains("z-paging-content")?{isFromZp:!0,isPageScroll:i.contains("z-paging-content-page"),isReachedTop:i.contains("z-paging-reached-top"),isUseChatRecordMode:i.contains("z-paging-use-chat-record-mode")}:e(t.parentNode)}return{isFromZp:!1}},getParent:function e(t){return t?t.$refs.paging?t:e(t.$parent):null},convertToPx:function(e){var t=Object.prototype.toString.call(e);if("[object Number]"===t)return e;var i=!1;return-1!==e.indexOf("rpx")||-1!==e.indexOf("upx")?(e=e.replace("rpx","").replace("upx",""),i=!0):-1!==e.indexOf("px")&&(e=e.replace("px","")),isNaN(e)?0:Number(i?p(e):e)},getTime:m,getInstanceId:function(){for(var e=[],t=0;t<10;t++)e[t]="0123456789abcdef".substr(Math.floor(16*Math.random()),1);return e.join("")+m()},consoleErr:function(e){console.error("[z-paging]".concat(e))},delay:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:s.default.delayTime,i=arguments.length>2?arguments[2]:void 0,a=setTimeout(e,t);return i&&(f[i]&&clearTimeout(f[i]),f[i]=a),a},wait:function(e){return new Promise((function(t){setTimeout(t,e)}))},isPromise:function(e){return"[object Promise]"===Object.prototype.toString.call(e)},addUnit:function(e,t){if("[object String]"===Object.prototype.toString.call(e)){var i=e;i=i.replace("rpx","").replace("upx","").replace("px",""),-1===e.indexOf("rpx")&&-1===e.indexOf("upx")&&-1!==e.indexOf("px")&&(i=2*parseFloat(i)),e=i}return"rpx"===t?e+"rpx":e/2+"px"},deepCopy:function e(t){if("object"!==(0,n.default)(t)||null===t)return t;var i=Array.isArray(t)?[]:{};for(var a in t)t.hasOwnProperty(a)&&(i[a]=e(t[a]));return i},rpx2px:p,getSystemInfoSync:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(e&&c)return c;var t=["DeviceInfo","AppBaseInfo","WindowInfo"],i=t.reduce((function(e,t){var i="get".concat(t);return uni[i]&&uni.canIUse(i)&&(e[t.charAt(0).toLowerCase()+t.slice(1)]=uni[i]()),e}),{}),a=i.deviceInfo,o=i.appBaseInfo,n=i.windowInfo;return c=a&&o&&n?(0,r.default)((0,r.default)((0,r.default)({},a),o),n):uni.getSystemInfoSync(),c},useBufferedInsert:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:50,i=[],a=null,n=[];return function(r){var l,s="[object Array]"!==Object.prototype.toString.call(r)?[r]:r;(l=i).push.apply(l,(0,o.default)(s));for(var d=arguments.length,h=new Array(d>1?d-1:0),u=1;u<d;u++)h[u-1]=arguments[u];n=h,a||(a=setTimeout((function(){e.apply(void 0,[1===i.length?i[0]:i].concat((0,o.default)(n))),i=[],a=null}),1===i.length?10:t))}}};t.default=y},4681:function(e,t,i){var a=i("c86c");t=a(!1),t.push([e.i,".zp-container[data-v-34819baf]{\ndisplay:flex;\nalign-items:center;justify-content:center}.zp-container-fixed[data-v-34819baf]{\nposition:absolute;top:0;left:0;width:100%;height:100%;\n}.zp-main[data-v-34819baf]{\ndisplay:flex;\nflex-direction:column;align-items:center;padding:%?50?% %?0?%}.zp-main-image-rpx[data-v-34819baf]{width:%?240?%;height:%?240?%}.zp-main-image-px[data-v-34819baf]{width:120px;height:120px}.zp-main-title[data-v-34819baf]{color:#aaa;text-align:center}.zp-main-title-rpx[data-v-34819baf]{font-size:%?28?%;margin-top:%?10?%;padding:%?0?% %?20?%}.zp-main-title-px[data-v-34819baf]{font-size:14px;margin-top:5px;padding:0 10px}.zp-main-error-btn[data-v-34819baf]{border:solid 1px #ddd;color:#aaa}.zp-main-error-btn-rpx[data-v-34819baf]{font-size:%?28?%;padding:%?8?% %?24?%;border-radius:%?6?%;margin-top:%?50?%}.zp-main-error-btn-px[data-v-34819baf]{font-size:14px;padding:4px 12px;border-radius:3px;margin-top:25px}",""]),e.exports=t},4915:function(e,t,i){"use strict";t["a"]=function(e){(e.options.wxs||(e.options.wxs={}))["pagingWxs"]=function(e){var t=0,i=-1,a=-1;function o(e,t){var i=f(t),o={},n={};if(t.callMethod("_handleListTouchstart"),!i||(o=i.getState(),n=i.getDataset(),!g(e,i,0))){var r=o.isTouchEnded;o.oldMoveDis=0;var l=c(e),s=m(n.loading);o.startY=l.touchY,a=o.startY,o.lastTouch=l,!s&&r&&(o.isTouchmoving=!1),o.isTouchEnded=!1,t.callMethod("_handleRefresherTouchstart",l)}}function n(e,t){var i=c(e),a=f(t),o=a.getDataset(),n=o.refresherthreshold,r=o.refresherf2threshold,l=m(o.refresherf2enabled),s=(m(o.isios),a.getState()),h=m(o.watchtouchdirectionchange),v={},y=0,T=!1;if(h){v=u(e,a),y=v.currentDis,T=v.isDown;var w=T?"top":"bottom";T==s.oldTouchDirection&&T!=s.oldEmitedTouchDirection&&(t.callMethod("_handleTouchDirectionChange",{direction:w}),s.oldEmitedTouchDirection=T),s.oldTouchDirection=T}if(g(e,a,1))return p(s,t,!1),!0;if(!function(e,t,i,a){var o=a.refreshermaxangle,n=m(a.refresheraecc),r=i.lastTouch,l=i.reachMaxAngle,s=i.oldMoveDis;if(!r)return!0;if(o>=0&&o<=90&&r){if((!s||s<1)&&!n&&null!=l&&!l)return!1;var d=Math.abs(t.touchX-r.touchX),h=Math.abs(t.touchY-r.touchY),u=Math.sqrt(Math.pow(d,2)+Math.pow(h,2));if((d||h)&&d>1){var c=Math.asin(h/u)/Math.PI*180;if(c<o){var f=i.hitReachMaxAngleCount||0;return i.hitReachMaxAngleCount=++f,i.hitReachMaxAngleCount>2&&(i.lastTouch=t,i.reachMaxAngle=!1),!1}}}return i.lastTouch=t,!0}(0,i,s,o))return p(s,t,!1),!0;if(v=u(e,a),y=v.currentDis,T=v.isDown,y<0)return d(0,a,s,!1),p(s,t,!1),!0;if(T&&!s.disabledBounce)return t.callMethod("_handleScrollViewBounce",{bounce:!1}),s.disabledBounce=!0,p(s,t,T),!T;d(y,a,s,!1);var S=s.refresherStatus,b=m(o.oldistouchmoving),x=m(o.hastouchmove),R=s.isTouchmoving;return s.refresherStatus=y>=n?l&&y>r?"goF2":"releaseToRefresh":"default",R||(s.isTouchmoving=!0,R=!0),s.isTouchEnded&&(s.isTouchEnded=!1),x&&t.callMethod("_handleWxsPullingDown",{moveDis:y,diffDis:v.diffDis}),void 0!=S&&S==s.refresherStatus&&b==R||t.callMethod("_handleRefresherTouchmove",y,i),p(s,t,T),!T}function r(e,t){c(e);var i=f(t),a=(i.getDataset(),i.getState());if(a.disabledBounce&&(t.callMethod("_handleScrollViewBounce",{bounce:!0}),a.disabledBounce=!1),!g(e,i,2)&&(a.reachMaxAngle=!0,a.hitReachMaxAngleCount=0,a.fixedIsTopHitCount=0,a.isTouchmoving)){var o=a.refresherStatus,n=a.moveDis,r=i.getDataset().refresherthreshold,l=u(e,i).currentDis;if(l>=r&&"releaseToRefresh"===o||(a.isTouchmoving=!1),t.callMethod("_handleRefresherTouchend",l),a.isTouchEnded=!0,!(n<r)){var s=!1;l>=r&&(l=r,s=!0),d(l,i,a,s)}}}function l(){if(!navigator)return!1;if(-1!=i)return i;return i=["Android","iPhone","SymbianOS","Windows Phone","iPad","iPod"].every((function(e){return navigator.userAgent.indexOf(e)<0})),i}var s=!1;function d(e,t,i,a){e=e||0,i.moveDis!=e&&(i.moveDis=e,h("translateY("+e+"px)",t,a,""))}function h(e,t,i,a){var o=t.getDataset();m(o.refreshernotransform)||(e="translateY(0px)"==e?"none":e,t.requestAnimationFrame((function(){var o={transform:e};i&&(o["transition"]="transform .1s linear"),a.length&&(o["transition"]=a),t.setStyle(o)})))}function u(e,i){var o=i.getState(),n=parseFloat(i.getDataset().refresherthreshold),r=parseFloat(i.getDataset().refresheroutrate),l=parseFloat(i.getDataset().refresherpullrate),s=c(e),d=o.startY&&"NaN"!=o.startY?o.startY:a,h=s.touchY-d,u=o.oldMoveDis||0;o.oldMoveDis=h;var f=h-u;return f>0&&(f*=l,t>n&&(f*=1-r)),f=f>100?f/100:f>20?f/2.2:f,t+=f,t=Math.max(0,t),{currentDis:t,diffDis:f,isDown:f>0}}function c(e){var t=e;return e.touches&&e.touches.length?t=e.touches[0]:e.changedTouches&&e.changedTouches.length?t=e.changedTouches[0]:e.datail&&e.datail!={}&&(t=e.datail),{touchX:t.clientX,touchY:t.clientY}}function f(e){var t=e.getState().currentIns;return t||e.callMethod("_handlePropUpdate"),t}function g(e,t,i){var a=t.getDataset(),o=t.getState(),n=m(a.loading),r=m(a.usechatrecordmode),l=m(a.refresherenabled),s=m(a.usecustomrefresher),d=m(a.usepagescroll),h=parseFloat(a.pagescrolltop),u=parseFloat(a.scrolltop),c=!1;var f=o.fixedIsTopHitCount||0;return c?(f++,f<=2&&(c=!1),o.fixedIsTopHitCount=f):o.fixedIsTopHitCount=0,n||r||!l||!s||d&&s&&h>5&&!c||!d&&s&&u>5&&!c}function p(e,t,i){var a=e.onPullingDown||!1;a!=i&&t.callMethod("_handleWxsPullingDownStatusChange",i),e.onPullingDown=i}function m(e){return e=("string"===typeof e?JSON.parse(e):e)||!1,1==e||"true"==e}return e.exports={touchstart:o,touchmove:n,touchend:r,mousedown:function(e,t){l()&&(o(e,t),s=!0)},mousemove:function(e,t){l()&&s&&n(e,t)},mouseup:function(e,t){l()&&(r(e,t),s=!1)},mouseleave:function(e,t){l()&&(s=!1)},propObserver:function(e,i,a,o){var n=a.getState()||{};n.currentIns=o;var r=o.getDataset();if(r.loading,e&&-1!=e.indexOf("end")){var l=e.split("end")[0];h("translateY(0px)",o,!1,l),n.moveDis=0,n.oldMoveDis=0,t=0}else if(e&&-1!=e.indexOf("begin")){var s=o.getDataset().refresherthreshold;d(s,o,n,!1)}}},e.exports}({exports:{}})}},5075:function(e,t,i){"use strict";var a=i("ae5c"),o=i("71e9"),n=i("e7e3"),r=i("52df"),l=i("81a7"),s=i("1fc1"),d=i("1297"),h=i("d67c"),u=i("5112"),c=i("7e91"),f=TypeError,g=function(e,t){this.stopped=e,this.result=t},p=g.prototype;e.exports=function(e,t,i){var m,v,y,T,w,S,b,x=i&&i.that,R=!(!i||!i.AS_ENTRIES),M=!(!i||!i.IS_RECORD),C=!(!i||!i.IS_ITERATOR),A=!(!i||!i.INTERRUPTED),I=a(t,x),z=function(e){return m&&c(m,"normal",e),new g(!0,e)},L=function(e){return R?(n(e),A?I(e[0],e[1],z):I(e[0],e[1])):A?I(e,z):I(e)};if(M)m=e.iterator;else if(C)m=e;else{if(v=u(e),!v)throw new f(r(e)+" is not iterable");if(l(v)){for(y=0,T=s(e);T>y;y++)if(w=L(e[y]),w&&d(p,w))return w;return new g(!1)}m=h(e,v)}S=M?e.next:m.next;while(!(b=o(S,m)).done){try{w=L(b.value)}catch(D){c(m,"throw",D)}if("object"==typeof w&&w&&d(p,w))return w}return new g(!1)}},5154:function(e,t,i){"use strict";i("6a54");var a=i("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,i("64aa"),i("5ef2"),i("c9b5"),i("bf0f"),i("ab80"),i("c223");var o=a(i("3918")),n=(a(i("2a2e")),a(i("a674"))),r={props:{refresherThemeStyle:{type:String,default:o.default.gc("refresherThemeStyle","")},refresherImgStyle:{type:Object,default:o.default.gc("refresherImgStyle",{})},refresherTitleStyle:{type:Object,default:o.default.gc("refresherTitleStyle",{})},refresherUpdateTimeStyle:{type:Object,default:o.default.gc("refresherUpdateTimeStyle",{})},watchRefresherTouchmove:{type:Boolean,default:o.default.gc("watchRefresherTouchmove",!1)},loadingMoreThemeStyle:{type:String,default:o.default.gc("loadingMoreThemeStyle","")},refresherOnly:{type:Boolean,default:o.default.gc("refresherOnly",!1)},refresherDefaultDuration:{type:[Number,String],default:o.default.gc("refresherDefaultDuration",100)},refresherCompleteDelay:{type:[Number,String],default:o.default.gc("refresherCompleteDelay",0)},refresherCompleteDuration:{type:[Number,String],default:o.default.gc("refresherCompleteDuration",300)},refresherRefreshingScrollable:{type:Boolean,default:o.default.gc("refresherRefreshingScrollable",!0)},refresherCompleteScrollable:{type:Boolean,default:o.default.gc("refresherCompleteScrollable",!1)},useCustomRefresher:{type:Boolean,default:o.default.gc("useCustomRefresher",!0)},refresherFps:{type:[Number,String],default:o.default.gc("refresherFps",40)},refresherMaxAngle:{type:[Number,String],default:o.default.gc("refresherMaxAngle",40)},refresherAngleEnableChangeContinued:{type:Boolean,default:o.default.gc("refresherAngleEnableChangeContinued",!1)},refresherDefaultText:{type:[String,Object],default:o.default.gc("refresherDefaultText",null)},refresherPullingText:{type:[String,Object],default:o.default.gc("refresherPullingText",null)},refresherRefreshingText:{type:[String,Object],default:o.default.gc("refresherRefreshingText",null)},refresherCompleteText:{type:[String,Object],default:o.default.gc("refresherCompleteText",null)},refresherGoF2Text:{type:[String,Object],default:o.default.gc("refresherGoF2Text",null)},refresherDefaultImg:{type:String,default:o.default.gc("refresherDefaultImg",null)},refresherPullingImg:{type:String,default:o.default.gc("refresherPullingImg",null)},refresherRefreshingImg:{type:String,default:o.default.gc("refresherRefreshingImg",null)},refresherCompleteImg:{type:String,default:o.default.gc("refresherCompleteImg",null)},refresherRefreshingAnimated:{type:Boolean,default:o.default.gc("refresherRefreshingAnimated",!0)},refresherEndBounceEnabled:{type:Boolean,default:o.default.gc("refresherEndBounceEnabled",!0)},refresherEnabled:{type:Boolean,default:o.default.gc("refresherEnabled",!0)},refresherThreshold:{type:[Number,String],default:o.default.gc("refresherThreshold","80rpx")},refresherDefaultStyle:{type:String,default:o.default.gc("refresherDefaultStyle","black")},refresherBackground:{type:String,default:o.default.gc("refresherBackground","transparent")},refresherFixedBackground:{type:String,default:o.default.gc("refresherFixedBackground","transparent")},refresherFixedBacHeight:{type:[Number,String],default:o.default.gc("refresherFixedBacHeight",0)},refresherOutRate:{type:Number,default:o.default.gc("refresherOutRate",.65)},refresherF2Enabled:{type:Boolean,default:o.default.gc("refresherF2Enabled",!1)},refresherF2Threshold:{type:[Number,String],default:o.default.gc("refresherF2Threshold","200rpx")},refresherF2Duration:{type:[Number,String],default:o.default.gc("refresherF2Duration",200)},showRefresherF2:{type:Boolean,default:o.default.gc("showRefresherF2",!0)},refresherPullRate:{type:Number,default:o.default.gc("refresherPullRate",.75)},showRefresherUpdateTime:{type:Boolean,default:o.default.gc("showRefresherUpdateTime",!1)},refresherUpdateTimeKey:{type:String,default:o.default.gc("refresherUpdateTimeKey","default")},refresherVibrate:{type:Boolean,default:o.default.gc("refresherVibrate",!1)},refresherNoTransform:{type:Boolean,default:o.default.gc("refresherNoTransform",!1)},useRefresherStatusBarPlaceholder:{type:Boolean,default:o.default.gc("useRefresherStatusBarPlaceholder",!1)}},data:function(){return{R:n.default.Refresher,refresherStatus:n.default.Refresher.Default,refresherTouchstartY:0,lastRefresherTouchmove:null,refresherReachMaxAngle:!0,refresherTransform:"translateY(0px)",refresherTransition:"",finalRefresherDefaultStyle:"black",refresherRevealStackCount:0,refresherCompleteTimeout:null,refresherCompleteSubTimeout:null,refresherEndTimeout:null,isTouchmovingTimeout:null,refresherTriggered:!1,isTouchmoving:!1,isTouchEnded:!1,isUserPullDown:!1,privateRefresherEnabled:-1,privateShowRefresherWhenReload:!1,customRefresherHeight:-1,showCustomRefresher:!1,doRefreshAnimateAfter:!1,isRefresherInComplete:!1,showF2:!1,f2Transform:"",pullDownTimeStamp:0,moveDis:0,oldMoveDis:0,currentDis:0,oldCurrentMoveDis:0,oldRefresherTouchmoveY:0,oldTouchDirection:"",oldEmitedTouchDirection:"",oldPullingDistance:-1,refresherThresholdUpdateTag:0}},watch:{refresherDefaultStyle:{handler:function(e){e.length&&(this.finalRefresherDefaultStyle=e)},immediate:!0},refresherStatus:function(e){e===n.default.Refresher.Loading&&this._cleanRefresherEndTimeout(),this.refresherVibrate&&(e===n.default.Refresher.ReleaseToRefresh||e===n.default.Refresher.GoF2)&&this._doVibrateShort(),this.$emit("refresherStatusChange",e),this.$emit("update:refresherStatus",e)},refresherEnabled:function(e){!e&&this.endRefresh()}},computed:{pullDownDisTimeStamp:function(){return 1e3/this.refresherFps},refresherThresholdUnitConverted:function(){return o.default.addUnit(this.refresherThreshold,this.unit)},finalRefresherEnabled:function(){return!this.layoutOnly&&!this.useChatRecordMode&&(-1===this.privateRefresherEnabled?this.refresherEnabled:1===this.privateRefresherEnabled)},finalRefresherThreshold:function(){var e=this.refresherThresholdUnitConverted,t=!1;return e===o.default.addUnit(80,this.unit)&&(t=!0,this.showRefresherUpdateTime&&(e=o.default.addUnit(120,this.unit))),t&&this.customRefresherHeight>0?this.customRefresherHeight+this.finalRefresherThresholdPlaceholder:o.default.convertToPx(e)+this.finalRefresherThresholdPlaceholder},finalRefresherF2Threshold:function(){return o.default.convertToPx(o.default.addUnit(this.refresherF2Threshold,this.unit))},finalRefresherThresholdPlaceholder:function(){return this.useRefresherStatusBarPlaceholder?this.statusBarHeight:0},finalRefresherFixedBacHeight:function(){return o.default.convertToPx(this.refresherFixedBacHeight)},finalRefresherThemeStyle:function(){return this.refresherThemeStyle.length?this.refresherThemeStyle:this.defaultThemeStyle},finalRefresherOutRate:function(){var e=this.refresherOutRate;return e=Math.max(0,e),e=Math.min(1,e),e},finalRefresherPullRate:function(){var e=this.refresherPullRate;return e=Math.max(0,e),e},finalRefresherTransform:function(){return this.refresherNoTransform||"translateY(0px)"===this.refresherTransform?"none":this.refresherTransform},finalShowRefresherWhenReload:function(){return this.showRefresherWhenReload||this.privateShowRefresherWhenReload},finalRefresherTriggered:function(){return!(!this.finalRefresherEnabled||this.useCustomRefresher)&&this.refresherTriggered},showRefresher:function(){var e=this.finalRefresherEnabled||this.useCustomRefresher&&!this.useChatRecordMode;return this.active&&-1===this.customRefresherHeight&&e&&this.updateCustomRefresherHeight(),e},hasTouchmove:function(){return!(this.$listeners&&!this.$listeners.refresherTouchmove)}},methods:{endRefresh:function(){var e=this;this.totalData=this.realTotalData,this._refresherEnd(),this._endSystemLoadingAndRefresh(),this._handleScrollViewBounce({bounce:!0}),this.$nextTick((function(){e.refresherTriggered=!1}))},updateCustomRefresherHeight:function(){var e=this;o.default.delay((function(){return e.$nextTick(e._updateCustomRefresherHeight)}))},goF2:function(){this._handleGoF2()},closeF2:function(){this._handleCloseF2()},_onRefresh:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];(!e||this.finalRefresherEnabled&&!this.useCustomRefresher)&&(this.$emit("onRefresh"),this.$emit("Refresh"),this.loading||this.isRefresherInComplete||(this.loadingType=n.default.LoadingType.Refresher,this.nShowRefresherReveal||(this.isUserPullDown=t,this.isUserReload=!t,this._startLoading(!0),this.refresherTriggered=!0,this.reloadWhenRefresh&&t&&(this.useChatRecordMode?this._onLoadingMore("click"):this._reload(!1,!1,t)))))},_onRestore:function(){this.refresherTriggered="restore",this.$emit("onRestore"),this.$emit("Restore")},_handleRefresherTouchstart:function(e){!this.loading&&this.isTouchEnded&&(this.isTouchmoving=!1),this.loadingType=n.default.LoadingType.Refresher,this.isTouchmovingTimeout&&clearTimeout(this.isTouchmovingTimeout),this.isTouchEnded=!1,this.refresherTransition="",this.refresherTouchstartY=e.touchY,this.$emit("refresherTouchstart",this.refresherTouchstartY),this.lastRefresherTouchmove=e,this._cleanRefresherCompleteTimeout(),this._cleanRefresherEndTimeout()},_handleRefresherTouchmove:function(e,t){this.refresherReachMaxAngle=!0,this.isTouchmovingTimeout&&clearTimeout(this.isTouchmovingTimeout),this.isTouchmoving=!0,this.isTouchEnded=!1,e>=this.finalRefresherThreshold?this.refresherStatus=this.refresherF2Enabled&&e>=this.finalRefresherF2Threshold?n.default.Refresher.GoF2:n.default.Refresher.ReleaseToRefresh:this.refresherStatus=n.default.Refresher.Default,this.moveDis=e},_handleRefresherTouchend:function(e){var t=this;this.isTouchmovingTimeout&&clearTimeout(this.isTouchmovingTimeout),this.refresherReachMaxAngle=!0,this.isTouchEnded=!0;var i=this.finalRefresherThreshold;e>=i&&[n.default.Refresher.ReleaseToRefresh,n.default.Refresher.GoF2].indexOf(this.refresherStatus)>=0?this.refresherStatus===n.default.Refresher.GoF2?(this._handleGoF2(),this._refresherEnd()):(o.default.delay((function(){t._emitTouchmove({pullingDistance:i,dy:t.moveDis-i})}),.1),this.moveDis=i,this.refresherStatus=n.default.Refresher.Loading,this._doRefresherLoad()):(this._refresherEnd(),this.isTouchmovingTimeout=o.default.delay((function(){t.isTouchmoving=!1}),this.refresherDefaultDuration)),this.scrollEnable=!0,this.$emit("refresherTouchend",e)},_handleListTouchstart:function(){this.useChatRecordMode&&this.autoHideKeyboardWhenChat&&(uni.hideKeyboard(),this.$emit("hidedKeyboard"))},_handleScrollViewBounce:function(e){var t=e.bounce;this.usePageScroll||this.scrollToTopBounceEnabled||(this.wxsScrollTop<=5?(this.refresherTransition="",this.scrollEnable=t):t&&(this.scrollEnable=t))},_handleWxsPullingDownStatusChange:function(e){this.wxsOnPullingDown=e,e&&!this.useChatRecordMode&&(this.renderPropScrollTop=0)},_handleWxsPullingDown:function(e){var t=e.moveDis,i=e.diffDis;this._emitTouchmove({pullingDistance:t,dy:i})},_handleTouchDirectionChange:function(e){var t=e.direction;this.$emit("touchDirectionChange",t)},_handlePropUpdate:function(){this.wxsPropType=o.default.getTime().toString()},_refresherEnd:function(){var e=this,t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],i=arguments.length>1&&void 0!==arguments[1]&&arguments[1],a=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=!(arguments.length>3&&void 0!==arguments[3])||arguments[3];if(this.loadingType===n.default.LoadingType.Refresher){var l=i&&(a||this.showRefresherWhenReload)?this.refresherCompleteDelay:0,s=l>0?n.default.Refresher.Complete:n.default.Refresher.Default;if(this.finalShowRefresherWhenReload){var d=this.refresherRevealStackCount;if(this.refresherRevealStackCount--,d>1)return}this._cleanRefresherEndTimeout(),this.refresherEndTimeout=o.default.delay((function(){e.refresherStatus=s,s!==n.default.Refresher.Complete&&(e.isRefresherInComplete=!1)}),this.refresherStatus!==n.default.Refresher.Default&&s===n.default.Refresher.Default?this.refresherCompleteDuration:0),l>0&&(this.isRefresherInComplete=!0),this._cleanRefresherCompleteTimeout(),this.refresherCompleteTimeout=o.default.delay((function(){var t=1,a=e.refresherEndBounceEnabled&&i?"cubic-bezier(0.19,1.64,0.42,0.72)":"linear";i&&(t=e.refresherEndBounceEnabled?e.refresherCompleteDuration/1e3:e.refresherCompleteDuration/3e3),e.refresherTransition="transform ".concat(i?t:e.refresherDefaultDuration/1e3,"s ").concat(a),e.wxsPropType=e.refresherTransition+"end"+o.default.getTime(),e.moveDis=0,s===n.default.Refresher.Complete&&(e.refresherCompleteSubTimeout&&(clearTimeout(e.refresherCompleteSubTimeout),e.refresherCompleteSubTimeout=null),e.refresherCompleteSubTimeout=o.default.delay((function(){e.$nextTick((function(){e.refresherStatus=n.default.Refresher.Default,e.isRefresherInComplete=!1}))}),800*t)),e._emitTouchmove({pullingDistance:0,dy:e.moveDis})}),l)}r&&(o.default.delay((function(){return e.loading=!1}),t?10:0),a&&this._onRestore())},_handleGoF2:function(){var e=this;!this.showF2&&this.refresherF2Enabled&&(this.$emit("refresherF2Change","go"),this.showRefresherF2&&(this.f2Transform="translateY(".concat(-this.superContentHeight,"px)"),this.showF2=!0,o.default.delay((function(){e.f2Transform="translateY(0px)"}),100,"f2ShowDelay")))},_handleCloseF2:function(){var e=this;this.showF2&&this.refresherF2Enabled&&(this.$emit("refresherF2Change","close"),this.showRefresherF2&&(this.f2Transform="translateY(".concat(-this.superContentHeight,"px)"),o.default.delay((function(){e.showF2=!1,e.nF2Opacity=0}),this.refresherF2Duration,"f2CloseDelay")))},_doRefresherRefreshAnimate:function(){this._cleanRefresherCompleteTimeout();var e=!this.doRefreshAnimateAfter&&this.finalShowRefresherWhenReload&&-1===this.customRefresherHeight&&this.refresherThreshold===o.default.addUnit(80,this.unit);e?this.doRefreshAnimateAfter=!0:(this.refresherRevealStackCount++,this.wxsPropType="begin"+o.default.getTime(),this.moveDis=this.finalRefresherThreshold,this.refresherStatus=n.default.Refresher.Loading,this.isTouchmoving=!0,this.isTouchmovingTimeout&&clearTimeout(this.isTouchmovingTimeout),this._doRefresherLoad(!1))},_doRefresherLoad:function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];this._onRefresh(!1,e),this.loading=!0},_updateCustomRefresherHeight:function(){var e=this;this._getNodeClientRect(".zp-custom-refresher-slot-view").then((function(t){e.customRefresherHeight=t?t[0].height:0,e.showCustomRefresher=e.customRefresherHeight>0,e.doRefreshAnimateAfter&&(e.doRefreshAnimateAfter=!1,e._doRefresherRefreshAnimate())}))},_emitTouchmove:function(e){e.viewHeight=this.finalRefresherThreshold,e.rate=e.viewHeight>0?e.pullingDistance/e.viewHeight:0,this.hasTouchmove&&this.oldPullingDistance!==e.pullingDistance&&this.$emit("refresherTouchmove",e),this.oldPullingDistance=e.pullingDistance},_cleanRefresherCompleteTimeout:function(){this.refresherCompleteTimeout=this._cleanTimeout(this.refresherCompleteTimeout)},_cleanRefresherEndTimeout:function(){this.refresherEndTimeout=this._cleanTimeout(this.refresherEndTimeout)}}};t.default=r},"53e0":function(e,t,i){"use strict";i("6a54");var a=i("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,i("c223");var o=a(i("39d8")),n=a(i("a126")),r=a(i("3918")),l=a(i("a674")),s={name:"z-paging-refresh",data:function(){return{R:l.default.Refresher,refresherTimeText:"",zTheme:{title:{white:"#efefef",black:"#555555"},arrow:{white:n.default.base64ArrowWhite,black:n.default.base64Arrow},flower:{white:n.default.base64FlowerWhite,black:n.default.base64Flower},success:{white:n.default.base64SuccessWhite,black:n.default.base64Success},indicator:{white:"#eeeeee",black:"#777777"}}}},props:["status","defaultThemeStyle","defaultText","pullingText","refreshingText","completeText","goF2Text","defaultImg","pullingImg","refreshingImg","completeImg","refreshingAnimated","showUpdateTime","updateTimeKey","imgStyle","titleStyle","updateTimeStyle","updateTimeTextMap","unit","isIos"],computed:{ts:function(){return this.defaultThemeStyle},statusTextMap:function(){var e;this.updateTime();var t=this.R,i=this.defaultText,a=this.pullingText,n=this.refreshingText,r=this.completeText,l=this.goF2Text;return e={},(0,o.default)(e,t.Default,i),(0,o.default)(e,t.ReleaseToRefresh,a),(0,o.default)(e,t.Loading,n),(0,o.default)(e,t.Complete,r),(0,o.default)(e,t.GoF2,l),e},currentTitle:function(){return this.statusTextMap[this.status]||this.defaultText},leftImageClass:function(){var e="zp-r-left-image-pre-size-".concat(this.unit);return this.status===this.R.Complete?e:"zp-r-left-image ".concat(e," ").concat(this.status===this.R.Default?"zp-r-arrow-down":"zp-r-arrow-top")},leftImageStyle:function(){var e=this.showUpdateTime,t=e?r.default.addUnit(36,this.unit):r.default.addUnit(34,this.unit);return{width:t,height:t,"margin-right":e?r.default.addUnit(20,this.unit):r.default.addUnit(9,this.unit)}},leftImageSrc:function(){var e=this.R,t=this.status;return t===e.Default?this.defaultImg?this.defaultImg:this.zTheme.arrow[this.ts]:t===e.ReleaseToRefresh?this.pullingImg?this.pullingImg:this.defaultImg?this.defaultImg:this.zTheme.arrow[this.ts]:t===e.Loading?this.refreshingImg?this.refreshingImg:this.zTheme.flower[this.ts]:t===e.Complete?this.completeImg?this.completeImg:this.zTheme.success[this.ts]:t===e.GoF2?this.zTheme.arrow[this.ts]:""},rightTextStyle:function(){var e={};return e["color"]=this.zTheme.title[this.ts],e["font-size"]=r.default.addUnit(30,this.unit),e}},methods:{addUnit:function(e,t){return r.default.addUnit(e,t)},updateTime:function(){this.showUpdateTime&&(this.refresherTimeText=r.default.getRefesrherFormatTimeByKey(this.updateTimeKey,this.updateTimeTextMap))}}};t.default=s},"5ab9":function(e,t,i){var a=i("cb25");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[e.i,a,""]]),a.locals&&(e.exports=a.locals);var o=i("967d").default;o("f5cdd0aa",a,!0,{sourceMap:!1,shadowMode:!1})},"5d6e":function(e,t,i){"use strict";var a=i("af9e");e.exports=a((function(){if("function"==typeof ArrayBuffer){var e=new ArrayBuffer(8);Object.isExtensible(e)&&Object.defineProperty(e,"a",{value:8})}}))},"60ff":function(e,t,i){var a=i("e6af");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[e.i,a,""]]),a.locals&&(e.exports=a.locals);var o=i("967d").default;o("017af74f",a,!0,{sourceMap:!1,shadowMode:!1})},"62d3":function(e,t,i){"use strict";i("6a54");var a=i("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,i("e966"),i("bf0f"),i("5c47"),i("fd3c"),i("c223");var o=a(i("3918")),n={data:function(){return{systemInfo:null,cssSafeAreaInsetBottom:-1,isReadyDestroy:!1}},computed:{windowTop:function(){return this.systemInfo&&this.systemInfo.windowTop||0},safeAreaBottom:function(){if(!this.systemInfo)return 0;var e=0;return e=Math.max(this.cssSafeAreaInsetBottom,0),e},isOldWebView:function(){try{var e=o.default.getSystemInfoSync(!0).system.split(" "),t=e[0],i=parseInt(e[1]);if("iOS"===t&&i<=10||"Android"===t&&i<=6)return!0}catch(a){return!1}return!1},zSlots:function(){return this.$scopedSlots||this.$slots}},beforeDestroy:function(){this.isReadyDestroy=!0},methods:{updateFixedLayout:function(){var e=this;this.fixed&&this.$nextTick((function(){e.systemInfo=o.default.getSystemInfoSync()}))},_getNodeClientRect:function(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],i=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(this.isReadyDestroy)return Promise.resolve(!1);var a=t?uni.createSelectorQuery().in(!0===t?this:t):uni.createSelectorQuery();return i?a.select(e).scrollOffset():a.select(e).boundingClientRect(),new Promise((function(e,t){a.exec((function(t){e(!(!t||""==t||void 0==t||!t.length)&&t)}))}))},_updateLeftAndRightWidth:function(e,t){var i=this;this.$nextTick((function(){setTimeout((function(){["left","right"].map((function(a){i._getNodeClientRect(".".concat(t,"-").concat(a)).then((function(t){i.$set(e,a,t?t[0].width+"px":"0px")}))}))}),0)}))},_getCssSafeAreaInsetBottom:function(e){var t=this;this._getNodeClientRect(".zp-safe-area-inset-bottom").then((function(i){t.cssSafeAreaInsetBottom=i?i[0].height:-1,i&&e&&e()}))},_getSystemInfoSync:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return o.default.getSystemInfoSync(e)}}};t.default=n},6569:function(e,t,i){"use strict";i.d(t,"b",(function(){return a})),i.d(t,"c",(function(){return o})),i.d(t,"a",(function(){}));var a=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("v-uni-view",{staticClass:"zp-l-container",class:{"zp-l-container-rpx":"rpx"===e.c.unit,"zp-l-container-px":"px"===e.c.unit},style:[e.c.customStyle],on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.doClick.apply(void 0,arguments)}}},[e.c.hideContent?e._e():[e.c.showNoMoreLine&&e.finalStatus===e.M.NoMore?i("v-uni-text",{class:{"zp-l-line-rpx":"rpx"===e.c.unit,"zp-l-line-px":"px"===e.c.unit},style:[{backgroundColor:e.zTheme.line[e.ts]},e.c.noMoreLineCustomStyle]}):e._e(),e.finalStatus===e.M.Loading&&e.c.loadingIconCustomImage?i("v-uni-image",{class:{"zp-l-line-loading-custom-image":!0,"zp-l-line-loading-custom-image-animated":e.c.loadingAnimated,"zp-l-line-loading-custom-image-rpx":"rpx"===e.c.unit,"zp-l-line-loading-custom-image-px":"px"===e.c.unit},style:[e.c.iconCustomStyle],attrs:{src:e.c.loadingIconCustomImage}}):e._e(),e.finalStatus!==e.M.Loading||"flower"!==e.finalLoadingIconType||e.c.loadingIconCustomImage.length?e._e():i("v-uni-image",{class:{"zp-line-loading-image":!0,"zp-line-loading-image-rpx":"rpx"===e.c.unit,"zp-line-loading-image-px":"px"===e.c.unit},style:[e.c.iconCustomStyle],attrs:{src:e.zTheme.flower[e.ts]}}),e.finalStatus!==e.M.Loading||"circle"!==e.finalLoadingIconType||e.c.loadingIconCustomImage.length?e._e():i("v-uni-text",{staticClass:"zp-l-circle-loading-view",class:{"zp-l-circle-loading-view-rpx":"rpx"===e.c.unit,"zp-l-circle-loading-view-px":"px"===e.c.unit},style:[{borderColor:e.zTheme.circleBorder[e.ts],borderTopColor:e.zTheme.circleBorderTop[e.ts]},e.c.iconCustomStyle]}),!e.c.isChat||!e.c.chatDefaultAsLoading&&e.finalStatus===e.M.Default||e.finalStatus===e.M.Fail?i("v-uni-text",{class:{"zp-l-text-rpx":"rpx"===e.c.unit,"zp-l-text-px":"px"===e.c.unit},style:[{color:e.zTheme.title[e.ts]},e.c.titleCustomStyle]},[e._v(e._s(e.ownLoadingMoreText))]):e._e(),e.c.showNoMoreLine&&e.finalStatus===e.M.NoMore?i("v-uni-text",{class:{"zp-l-line-rpx":"rpx"===e.c.unit,"zp-l-line-px":"px"===e.c.unit},style:[{backgroundColor:e.zTheme.line[e.ts]},e.c.noMoreLineCustomStyle]}):e._e()]],2)},o=[]},7053:function(e,t,i){"use strict";i("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default={}},"70c0":function(e,t,i){"use strict";i.r(t);var a=i("8764"),o=i("2f21");for(var n in o)["default"].indexOf(n)<0&&function(e){i.d(t,e,(function(){return o[e]}))}(n);i("8c83");var r=i("828b"),l=Object(r["a"])(o["default"],a["b"],a["c"],!1,null,"34819baf",null,!1,a["a"],void 0);t["default"]=l.exports},7322:function(e,t,i){"use strict";i.r(t);var a=i("74ae"),o=i.n(a);for(var n in a)["default"].indexOf(n)<0&&function(e){i.d(t,e,(function(){return a[e]}))}(n);t["default"]=o.a},"74ae":function(e,t,i){"use strict";i("6a54");var a=i("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=a(i("a179")),n={name:"z-paging",mixins:[o.default]};t.default=n},7567:function(e,t,i){"use strict";i("6a54");var a=i("3639").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=a(i("bc91")),n={components:{},data:function(){return{dataList:[]}},onLoad:function(e){},onShow:function(){},methods:{sessionlist:function(e,t){console.log(e,t,o);var i={page_no:e,page_size:t,nickname:""};o.chatuserlist(i).then((function(e){paging.value.complete(e.data.list)})).catch((function(e){paging.value.complete(!1)}))}},computed:{},watch:{}};t.default=n},7658:function(e,t,i){"use strict";var a=i("8bdb"),o=i("85c1"),n=i("bb80"),r=i("8466"),l=i("81a9"),s=i("d0b1"),d=i("5075"),h=i("b720"),u=i("474f"),c=i("1eb8"),f=i("1c06"),g=i("af9e"),p=i("29ba"),m=i("181d"),v=i("dcda");e.exports=function(e,t,i){var y=-1!==e.indexOf("Map"),T=-1!==e.indexOf("Weak"),w=y?"set":"add",S=o[e],b=S&&S.prototype,x=S,R={},M=function(e){var t=n(b[e]);l(b,e,"add"===e?function(e){return t(this,0===e?0:e),this}:"delete"===e?function(e){return!(T&&!f(e))&&t(this,0===e?0:e)}:"get"===e?function(e){return T&&!f(e)?void 0:t(this,0===e?0:e)}:"has"===e?function(e){return!(T&&!f(e))&&t(this,0===e?0:e)}:function(e,i){return t(this,0===e?0:e,i),this})},C=r(e,!u(S)||!(T||b.forEach&&!g((function(){(new S).entries().next()}))));if(C)x=i.getConstructor(t,e,y,w),s.enable();else if(r(e,!0)){var A=new x,I=A[w](T?{}:-0,1)!==A,z=g((function(){A.has(1)})),L=p((function(e){new S(e)})),D=!T&&g((function(){var e=new S,t=5;while(t--)e[w](t,t);return!e.has(-0)}));L||(x=t((function(e,t){h(e,b);var i=v(new S,e,x);return c(t)||d(t,i[w],{that:i,AS_ENTRIES:y}),i})),x.prototype=b,b.constructor=x),(z||D)&&(M("delete"),M("has"),y&&M("get")),(D||I)&&M(w),T&&b.clear&&delete b.clear}return R[e]=x,a({global:!0,constructor:!0,forced:x!==S},R),m(x,e),T||i.setStrong(x,e,y),x}},"772b":function(e,t,i){"use strict";i.r(t);var a=i("6569"),o=i("faf4");for(var n in o)["default"].indexOf(n)<0&&function(e){i.d(t,e,(function(){return o[e]}))}(n);i("d761");var r=i("828b"),l=Object(r["a"])(o["default"],a["b"],a["c"],!1,null,"dff48604",null,!1,a["a"],void 0);t["default"]=l.exports},"836f":function(e,t,i){"use strict";i.r(t);var a=i("f422"),o=i("e684");for(var n in o)["default"].indexOf(n)<0&&function(e){i.d(t,e,(function(){return o[e]}))}(n);var r=i("828b"),l=Object(r["a"])(o["default"],a["b"],a["c"],!1,null,"4bb11fc7",null,!1,a["a"],void 0);t["default"]=l.exports},8764:function(e,t,i){"use strict";i.d(t,"b",(function(){return a})),i.d(t,"c",(function(){return o})),i.d(t,"a",(function(){}));var a=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("v-uni-view",{class:{"zp-container":!0,"zp-container-fixed":e.emptyViewFixed},style:[e.finalEmptyViewStyle],on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.emptyViewClick.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"zp-main"},[e.emptyViewImg.length?i("v-uni-image",{class:{"zp-main-image-rpx":"rpx"===e.unit,"zp-main-image-px":"px"===e.unit},style:[e.emptyViewImgStyle],attrs:{mode:"aspectFit",src:e.emptyViewImg}}):i("v-uni-image",{class:{"zp-main-image-rpx":"rpx"===e.unit,"zp-main-image-px":"px"===e.unit},style:[e.emptyViewImgStyle],attrs:{src:e.emptyImg}}),i("v-uni-text",{staticClass:"zp-main-title",class:{"zp-main-title-rpx":"rpx"===e.unit,"zp-main-title-px":"px"===e.unit},style:[e.emptyViewTitleStyle]},[e._v(e._s(e.emptyViewText))]),e.showEmptyViewReload?i("v-uni-text",{class:{"zp-main-error-btn":!0,"zp-main-error-btn-rpx":"rpx"===e.unit,"zp-main-error-btn-px":"px"===e.unit},style:[e.emptyViewReloadStyle],on:{click:function(t){t.stopPropagation(),arguments[0]=t=e.$handleEvent(t),e.reloadClick.apply(void 0,arguments)}}},[e._v(e._s(e.emptyViewReloadText))]):e._e()],1)],1)},o=[]},"87a3":function(e,t,i){"use strict";i("6a54");var a=i("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,i("64aa");var o=a(i("3918")),n={props:{hideEmptyView:{type:Boolean,default:o.default.gc("hideEmptyView",!1)},emptyViewText:{type:[String,Object],default:o.default.gc("emptyViewText",null)},showEmptyViewReload:{type:Boolean,default:o.default.gc("showEmptyViewReload",!1)},showEmptyViewReloadWhenError:{type:Boolean,default:o.default.gc("showEmptyViewReloadWhenError",!0)},emptyViewReloadText:{type:[String,Object],default:o.default.gc("emptyViewReloadText",null)},emptyViewImg:{type:String,default:o.default.gc("emptyViewImg","")},emptyViewErrorText:{type:[String,Object],default:o.default.gc("emptyViewErrorText",null)},emptyViewErrorImg:{type:String,default:o.default.gc("emptyViewErrorImg","")},emptyViewStyle:{type:Object,default:o.default.gc("emptyViewStyle",{})},emptyViewSuperStyle:{type:Object,default:o.default.gc("emptyViewSuperStyle",{})},emptyViewImgStyle:{type:Object,default:o.default.gc("emptyViewImgStyle",{})},emptyViewTitleStyle:{type:Object,default:o.default.gc("emptyViewTitleStyle",{})},emptyViewReloadStyle:{type:Object,default:o.default.gc("emptyViewReloadStyle",{})},emptyViewFixed:{type:Boolean,default:o.default.gc("emptyViewFixed",!1)},emptyViewCenter:{type:Boolean,default:o.default.gc("emptyViewCenter",!0)},autoHideEmptyViewWhenLoading:{type:Boolean,default:o.default.gc("autoHideEmptyViewWhenLoading",!0)},autoHideEmptyViewWhenPull:{type:Boolean,default:o.default.gc("autoHideEmptyViewWhenPull",!0)},emptyViewZIndex:{type:Number,default:o.default.gc("emptyViewZIndex",9)}},data:function(){return{customerEmptyViewErrorText:""}},computed:{finalEmptyViewImg:function(){return this.isLoadFailed?this.emptyViewErrorImg:this.emptyViewImg},finalShowEmptyViewReload:function(){return this.isLoadFailed?this.showEmptyViewReloadWhenError:this.showEmptyViewReload},showEmpty:function(){return!(this.isOnly||this.hideEmptyView||this.realTotalData.length)&&(!this.autoHideEmptyViewWhenLoading||(!(!this.isAddedData||this.firstPageLoaded||this.loading)||!this.autoHideEmptyViewWhenPull&&!this.isUserReload))}},methods:{_emptyViewReload:function(){var e=this,t=!1;this.$emit("emptyViewReload",(function(i){void 0!==i&&!0!==i||(e.fromEmptyViewReload=!0,e.reload().catch((function(){}))),t=!0})),this.$nextTick((function(){t||(e.fromEmptyViewReload=!0,e.reload().catch((function(){})))}))},_emptyViewClick:function(){this.$emit("emptyViewClick")}}};t.default=n},"8bcb":function(e,t,i){"use strict";i("6a54");var a=i("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=a(i("2634")),n=a(i("2fdc")),r=a(i("9b1b"));i("64aa"),i("c9b5"),i("bf0f"),i("ab80"),i("dc8a");var l=a(i("a126")),s=a(i("2a2e")),d=a(i("3918")),h=a(i("9f56")),u=a(i("772b")),c=a(i("70c0")),f=a(i("62d3")),g=a(i("e488")),p=a(i("d72d")),m=a(i("bb24")),v=a(i("87a3")),y=a(i("5154")),T=a(i("0003")),w=a(i("da16")),S=a(i("9c5b")),b=a(i("15d19")),x=a(i("e583")),R=a(i("2087")),M=a(i("a674")),C=d.default.getSystemInfoSync(),A={name:"z-paging",components:{zPagingRefresh:h.default,zPagingLoadMore:u.default,zPagingEmptyView:c.default},mixins:[f.default,g.default,p.default,m.default,v.default,y.default,T.default,w.default,S.default,b.default,x.default,R.default],data:function(){return{base64BackToTop:l.default.base64BackToTop,loadingType:M.default.LoadingType.Refresher,requestTimeStamp:0,wxsPropType:"",renderPropScrollTop:-1,checkScrolledToBottomTimeOut:null,cacheTopHeight:-1,statusBarHeight:C.statusBarHeight,scrollViewHeight:0,pagingOrgTop:-1,insideOfPaging:-1,isLoadFailed:!1,isIos:"ios"===C.platform,disabledBounce:!1,fromCompleteEmit:!1,disabledCompleteEmit:!1,pageLaunched:!1,active:!1,wxsIsScrollTopInTopRange:!0,wxsScrollTop:0,wxsPageScrollTop:0,wxsOnPullingDown:!1}},props:{delay:{type:[Number,String],default:d.default.gc("delay",0)},minDelay:{type:[Number,String],default:d.default.gc("minDelay",0)},pagingStyle:{type:Object,default:d.default.gc("pagingStyle",{})},pagingClass:{type:[String,Array,Object],default:d.default.gc("pagingClass","")},height:{type:String,default:d.default.gc("height","")},width:{type:String,default:d.default.gc("width","")},maxWidth:{type:String,default:d.default.gc("maxWidth","")},bgColor:{type:String,default:d.default.gc("bgColor","")},pagingContentStyle:{type:Object,default:d.default.gc("pagingContentStyle",{})},autoHeight:{type:Boolean,default:d.default.gc("autoHeight",!1)},autoHeightAddition:{type:[Number,String],default:d.default.gc("autoHeightAddition","0px")},defaultThemeStyle:{type:String,default:d.default.gc("defaultThemeStyle","black")},fixed:{type:Boolean,default:d.default.gc("fixed",!0)},safeAreaInsetBottom:{type:Boolean,default:d.default.gc("safeAreaInsetBottom",!1)},useSafeAreaPlaceholder:{type:Boolean,default:d.default.gc("useSafeAreaPlaceholder",!1)},bottomBgColor:{type:String,default:d.default.gc("bottomBgColor","")},topZIndex:{type:Number,default:d.default.gc("topZIndex",99)},superContentZIndex:{type:Number,default:d.default.gc("superContentZIndex",1)},contentZIndex:{type:Number,default:d.default.gc("contentZIndex",1)},f2ZIndex:{type:Number,default:d.default.gc("f2ZIndex",100)},autoFullHeight:{type:Boolean,default:d.default.gc("autoFullHeight",!0)},watchTouchDirectionChange:{type:Boolean,default:d.default.gc("watchTouchDirectionChange",!1)},watchScrollDirectionChange:{type:Boolean,default:d.default.gc("watchScrollDirectionChange",!1)},layoutOnly:{type:Boolean,default:d.default.gc("layoutOnly",!1)},unit:{type:String,default:d.default.gc("unit","rpx")}},created:function(){this.createdReload&&!this.isOnly&&this.auto&&(this._startLoading(),this.$nextTick(this._preReload))},mounted:function(){var e=this;this.active=!0,this.wxsPropType=d.default.getTime().toString(),this.renderJsIgnore,this.createdReload||this.isOnly||!this.auto||d.default.delay((function(){return e.$nextTick(e._preReload)}),0),this.finalUseCache&&this._setListByLocalCache();var t;t=s.default.delayTime,this.$nextTick((function(){e.systemInfo=d.default.getSystemInfoSync(),!e.usePageScroll&&e.autoHeight&&e._setAutoHeight(),e.loaded=!0,d.default.delay((function(){e.updateFixedLayout(),e._updateCachedSuperContentHeight(),e._updateScrollViewHeight()}))})),this.updatePageScrollTopHeight(),this.updatePageScrollBottomHeight(),this.updateLeftAndRightWidth(),this.finalRefresherEnabled&&this.useCustomRefresher&&this.$nextTick((function(){e.isTouchmoving=!0})),this.layoutOnly||this._onEmit(),this.$nextTick((function(){setTimeout((function(){e._getCssSafeAreaInsetBottom((function(){return e.safeAreaInsetBottom&&e.updatePageScrollBottomHeight()}))}),t)}))},destroyed:function(){this._handleUnmounted()},watch:{defaultThemeStyle:{handler:function(e){e.length&&(this.finalRefresherDefaultStyle=e)},immediate:!0},autoHeight:function(e){this.loaded&&!this.usePageScroll&&this._setAutoHeight(e)},autoHeightAddition:function(e){this.loaded&&!this.usePageScroll&&this.autoHeight&&this._setAutoHeight(e)}},computed:{finalPagingStyle:function(){var e=(0,r.default)({},this.pagingStyle);if(!this.systemInfo)return e;var t=this.windowTop,i=this.windowBottom;return!this.usePageScroll&&this.fixed&&(t&&!e.top&&(e.top=t+"px"),i&&!e.bottom&&(e.bottom=i+"px")),this.bgColor.length&&!e["background"]&&(e["background"]=this.bgColor),this.height.length&&!e["height"]&&(e["height"]=this.height),this.width.length&&!e["width"]&&(e["width"]=this.width),this.maxWidth.length&&!e["max-width"]&&(e["max-width"]=this.maxWidth,e["margin"]="0 auto"),e},finalPagingContentStyle:function(){return 1!=this.contentZIndex&&(this.pagingContentStyle["z-index"]=this.contentZIndex,this.pagingContentStyle["position"]="relative"),this.pagingContentStyle},finalUseSafeAreaPlaceholder:function(){return this.useSafeAreaPlaceholder&&!this.zSlots.bottom},renderJsIgnore:function(){var e=this;return(this.usePageScroll&&this.useChatRecordMode||!this.refresherEnabled&&this.scrollable||!this.useCustomRefresher)&&this.$nextTick((function(){e.renderPropScrollTop=10})),0},windowHeight:function(){return this.systemInfo&&this.systemInfo.windowHeight||0},windowBottom:function(){return this.systemInfo&&this.systemInfo.windowBottom||0},isIosAndH5:function(){return this.isIos},isOnly:function(){return this.layoutOnly||this.refresherOnly}},methods:{getVersion:function(){return"z-paging v".concat(s.default.version)},setSpecialEffects:function(e){this.setListSpecialEffects(e)},setListSpecialEffects:function(e){this.nFixFreezing=e&&Object.keys(e).length,this.isIos&&(this.privateRefresherEnabled=0),!this.usePageScroll&&this.$refs["zp-n-list"].setSpecialEffects(e)},_doVibrateShort:function(){},_setAutoHeight:function(){var e=arguments,t=this;return(0,n.default)((0,o.default)().mark((function i(){var a,n,r,l,s,h,u,c,f;return(0,o.default)().wrap((function(i){while(1)switch(i.prev=i.next){case 0:if(a=!(e.length>0&&void 0!==e[0])||e[0],n=e.length>1&&void 0!==e[1]?e[1]:null,r="min-height",i.prev=3,!a){i.next=17;break}if(i.t0=n,i.t0){i.next=10;break}return i.next=9,t._getNodeClientRect(".zp-scroll-view");case 9:i.t0=i.sent;case 10:return l=i.t0,i.next=13,t._getNodeClientRect(".zp-page-bottom");case 13:s=i.sent,l&&(h=l[0].top,u=t.windowHeight-h,u-=s?s[0].height:0,c=d.default.convertToPx(t.autoHeightAddition)," !important",f=u+c-(t.insideMore?1:0)+"px !important",t.$set(t.scrollViewStyle,r,f),t.$set(t.scrollViewInStyle,r,f)),i.next=19;break;case 17:t.$delete(t.scrollViewStyle,r),t.$delete(t.scrollViewInStyle,r);case 19:i.next=23;break;case 21:i.prev=21,i.t1=i["catch"](3);case 23:case"end":return i.stop()}}),i,null,[[3,21]])})))()},_updateScrollViewHeight:function(){var e=this;return(0,n.default)((0,o.default)().mark((function t(){var i,a;return(0,o.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e._getNodeClientRect(".zp-scroll-view");case 2:i=t.sent,i&&(a=i[0].height,e.scrollViewHeight=a,e.pagingOrgTop=i[0].top);case 4:case"end":return t.stop()}}),t)})))()},_handleUnmounted:function(){this.active=!1,this.layoutOnly||this._offEmit()},_updateInsideOfPaging:function(){this.insideMore&&!0===this.insideOfPaging&&setTimeout(this.doLoadMore,200)},_cleanTimeout:function(e){return e&&(clearTimeout(e),e=null),e},_onEmit:function(){var e=this;uni.$on(s.default.errorUpdateKey,(function(t){e.loading&&(t&&(e.customerEmptyViewErrorText=t),e.complete(!1).catch((function(){})))})),uni.$on(s.default.completeUpdateKey,(function(t){setTimeout((function(){if(e.loading)if(e.disabledCompleteEmit)e.disabledCompleteEmit=!1;else{var i=t.type||"normal",a=t.list||t,o=t.rule;switch(e.fromCompleteEmit=!0,i){case"normal":e.complete(a);break;case"total":e.completeByTotal(a,o);break;case"nomore":e.completeByNoMore(a,o);break;case"key":e.completeByKey(a,o);break;default:break}}}),1)}))},_offEmit:function(){uni.$off(s.default.errorUpdateKey),uni.$off(s.default.completeUpdateKey)}}};t.default=A},"8c83":function(e,t,i){"use strict";var a=i("d545"),o=i.n(a);o.a},"9c5b":function(e,t,i){"use strict";i("6a54");var a=i("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,i("64aa");var o=a(i("3918")),n={props:{useChatRecordMode:{type:Boolean,default:o.default.gc("useChatRecordMode",!1)},chatRecordMoreOffset:{type:[Number,String],default:o.default.gc("chatRecordMoreOffset","0rpx")},autoHideKeyboardWhenChat:{type:Boolean,default:o.default.gc("autoHideKeyboardWhenChat",!0)},autoAdjustPositionWhenChat:{type:Boolean,default:o.default.gc("autoAdjustPositionWhenChat",!0)},chatAdjustPositionOffset:{type:[Number,String],default:o.default.gc("chatAdjustPositionOffset","0rpx")},autoToBottomWhenChat:{type:Boolean,default:o.default.gc("autoToBottomWhenChat",!1)},showChatLoadingWhenReload:{type:Boolean,default:o.default.gc("showChatLoadingWhenReload",!1)},chatLoadingMoreDefaultAsLoading:{type:Boolean,default:o.default.gc("chatLoadingMoreDefaultAsLoading",!0)}},data:function(){return{keyboardHeight:0,isKeyboardHeightChanged:!1}},computed:{finalChatRecordMoreOffset:function(){return o.default.convertToPx(this.chatRecordMoreOffset)},finalChatAdjustPositionOffset:function(){return o.default.convertToPx(this.chatAdjustPositionOffset)},chatRecordRotateStyle:function(){var e,t=this;return e=this.useChatRecordMode?{transform:"scaleY(-1)"}:{},this.$emit("update:cellStyle",e),this.$emit("cellStyleChange",e),this.$nextTick((function(){t.isFirstPage&&t.isChatRecordModeAndNotInversion&&t.$nextTick((function(){t._scrollToBottom(!1),o.default.delay((function(){t._scrollToBottom(!1),o.default.delay((function(){t._scrollToBottom(!1)}),50)}),50)}))})),e},isChatRecordModeHasTransform:function(){return this.useChatRecordMode&&this.chatRecordRotateStyle&&this.chatRecordRotateStyle.transform},isChatRecordModeAndNotInversion:function(){return this.isChatRecordModeHasTransform&&"scaleY(1)"===this.chatRecordRotateStyle.transform},isChatRecordModeAndInversion:function(){return this.isChatRecordModeHasTransform&&"scaleY(-1)"===this.chatRecordRotateStyle.transform},chatRecordModeSafeAreaBottom:function(){return this.safeAreaInsetBottom&&!this.keyboardHeight?this.safeAreaBottom:0}},mounted:function(){this.addKeyboardHeightChangeListener()},methods:{addChatRecordData:function(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],i=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];this.useChatRecordMode&&(this.isTotalChangeFromAddData=!0,this.addDataFromTop(e,t,i))},doChatRecordLoadMore:function(){this.useChatRecordMode&&this._onLoadingMore("click")},addKeyboardHeightChangeListener:function(){},_handleKeyboardHeightChange:function(e){var t=this;this.$emit("keyboardHeightChange",e),this.autoAdjustPositionWhenChat&&(this.isKeyboardHeightChanged=!0,this.keyboardHeight=e.height>0?e.height+this.finalChatAdjustPositionOffset:e.height),this.autoToBottomWhenChat&&this.keyboardHeight>0&&o.default.delay((function(){t.scrollToBottom(!1),o.default.delay((function(){t.scrollToBottom(!1)}))}))}}};t.default=n},"9f56":function(e,t,i){"use strict";i.r(t);var a=i("2aca"),o=i("a2e4");for(var n in o)["default"].indexOf(n)<0&&function(e){i.d(t,e,(function(){return o[e]}))}(n);i("2720");var r=i("828b"),l=Object(r["a"])(o["default"],a["b"],a["c"],!1,null,"6737cf27",null,!1,a["a"],void 0);t["default"]=l.exports},a06d:function(e,t,i){"use strict";i.d(t,"b",(function(){return o})),i.d(t,"c",(function(){return n})),i.d(t,"a",(function(){return a}));var a={zPagingEmptyView:i("70c0").default},o=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("v-uni-view",{class:[{"z-paging-content":!0,"z-paging-content-full":!e.usePageScroll,"z-paging-content-fixed":!e.usePageScroll&&e.fixed,"z-paging-content-page":e.usePageScroll,"z-paging-reached-top":e.renderPropScrollTop<1,"z-paging-use-chat-record-mode":e.useChatRecordMode},e.pagingClass],style:[e.finalPagingStyle]},[-1===e.cssSafeAreaInsetBottom?i("v-uni-view",{staticClass:"zp-safe-area-inset-bottom"}):e._e(),e.showF2&&e.showRefresherF2?i("v-uni-view",{staticClass:"zp-f2-content",style:[{transform:e.f2Transform,transition:"transform .2s linear",height:e.superContentHeight+"px","z-index":e.f2ZIndex}],on:{touchmove:function(t){t.stopPropagation(),t.preventDefault(),arguments[0]=t=e.$handleEvent(t)}}},[e._t("f2")],2):e._e(),e.zSlots.top?[e.usePageScroll?i("v-uni-view",{staticClass:"zp-page-top",style:[{top:e.windowTop+"px","z-index":e.topZIndex}],on:{touchmove:function(t){t.stopPropagation(),t.preventDefault(),arguments[0]=t=e.$handleEvent(t)}}},[e._t("top")],2):e._t("top")]:e._e(),i("v-uni-view",{class:{"zp-view-super":!0,"zp-scroll-view-super":!e.usePageScroll},style:[e.finalScrollViewStyle]},[e.zSlots.left?i("v-uni-view",{class:{"zp-page-left":!0,"zp-absoulte":e.finalIsOldWebView}},[e._t("left")],2):e._e(),i("v-uni-view",{class:{"zp-scroll-view-container":!0,"zp-absoulte":e.finalIsOldWebView},style:[e.scrollViewContainerStyle]},[i("v-uni-scroll-view",{ref:"zp-scroll-view",class:{"zp-scroll-view":!0,"zp-scroll-view-absolute":!e.usePageScroll,"zp-scroll-view-hide-scrollbar":!e.showScrollbar},style:[e.chatRecordRotateStyle],attrs:{"scroll-top":e.scrollTop,"scroll-left":e.scrollLeft,"scroll-x":e.scrollX,"scroll-y":e.finalScrollable,"enable-back-to-top":e.finalEnableBackToTop,"show-scrollbar":e.showScrollbar,"scroll-with-animation":e.finalScrollWithAnimation,"scroll-into-view":e.scrollIntoView,"lower-threshold":e.finalLowerThreshold,"upper-threshold":5,"refresher-enabled":e.finalRefresherEnabled&&!e.useCustomRefresher,"refresher-threshold":e.finalRefresherThreshold,"refresher-default-style":e.finalRefresherDefaultStyle,"refresher-background":e.refresherBackground,"refresher-triggered":e.finalRefresherTriggered},on:{scroll:function(t){arguments[0]=t=e.$handleEvent(t),e._scroll.apply(void 0,arguments)},scrolltolower:function(t){arguments[0]=t=e.$handleEvent(t),e._onScrollToLower.apply(void 0,arguments)},scrolltoupper:function(t){arguments[0]=t=e.$handleEvent(t),e._onScrollToUpper.apply(void 0,arguments)},refresherrestore:function(t){arguments[0]=t=e.$handleEvent(t),e._onRestore.apply(void 0,arguments)},refresherrefresh:function(t){arguments[0]=t=e.$handleEvent(t),e._onRefresh(!0)}}},[i("v-uni-view",{staticClass:"zp-paging-touch-view",on:{touchstart:function(t){t=e.$handleWxsEvent(t),e.pagingWxs.touchstart(t,e.$getComponentDescriptor())},touchmove:function(t){t=e.$handleWxsEvent(t),e.pagingWxs.touchmove(t,e.$getComponentDescriptor())},touchend:function(t){t=e.$handleWxsEvent(t),e.pagingWxs.touchend(t,e.$getComponentDescriptor())},touchcancel:function(t){t=e.$handleWxsEvent(t),e.pagingWxs.touchend(t,e.$getComponentDescriptor())},mousedown:function(t){t=e.$handleWxsEvent(t),e.pagingWxs.mousedown(t,e.$getComponentDescriptor())},mousemove:function(t){t=e.$handleWxsEvent(t),e.pagingWxs.mousemove(t,e.$getComponentDescriptor())},mouseup:function(t){t=e.$handleWxsEvent(t),e.pagingWxs.mouseup(t,e.$getComponentDescriptor())},mouseleave:function(t){t=e.$handleWxsEvent(t),e.pagingWxs.mouseleave(t,e.$getComponentDescriptor())}}},[e.finalRefresherFixedBacHeight>0?i("v-uni-view",{staticClass:"zp-fixed-bac-view",style:[{background:e.refresherFixedBackground,height:e.finalRefresherFixedBacHeight+"px"}]}):e._e(),i("v-uni-view",{wxsProps:{"change:renderPropIsIosAndH5":"isIosAndH5","change:prop":"wxsPropType"},staticClass:"zp-paging-main",style:[e.scrollViewInStyle,{transform:e.finalRefresherTransform,transition:e.refresherTransition}],attrs:{"change:prop":e.pagingWxs.propObserver,prop:e.wxsPropType,"data-refresherThreshold":e.finalRefresherThreshold,"data-refresherF2Enabled":e.refresherF2Enabled,"data-refresherF2Threshold":e.finalRefresherF2Threshold,"data-isIos":e.isIos,"data-loading":e.loading||e.isRefresherInComplete,"data-useChatRecordMode":e.useChatRecordMode,"data-refresherEnabled":e.finalRefresherEnabled,"data-useCustomRefresher":e.useCustomRefresher,"data-pageScrollTop":e.wxsPageScrollTop,"data-scrollTop":e.wxsScrollTop,"data-refresherMaxAngle":e.refresherMaxAngle,"data-refresherNoTransform":e.refresherNoTransform,"data-refresherAecc":e.refresherAngleEnableChangeContinued,"data-usePageScroll":e.usePageScroll,"data-watchTouchDirectionChange":e.watchTouchDirectionChange,"data-oldIsTouchmoving":e.isTouchmoving,"data-refresherOutRate":e.finalRefresherOutRate,"data-refresherPullRate":e.finalRefresherPullRate,"data-hasTouchmove":e.hasTouchmove,"change:renderPropIsIosAndH5":e.pagingRenderjs.renderPropIsIosAndH5Change,renderPropIsIosAndH5:e.isIosAndH5}},[e.showRefresher?i("v-uni-view",{staticClass:"zp-custom-refresher-view",style:[{"margin-top":"-"+(e.finalRefresherThreshold+e.refresherThresholdUpdateTag)+"px",background:e.refresherBackground,opacity:e.isTouchmoving?1:0}]},[i("v-uni-view",{staticClass:"zp-custom-refresher-container",style:[{height:e.finalRefresherThreshold+"px",background:e.refresherBackground}]},[e.useRefresherStatusBarPlaceholder?i("v-uni-view",{staticClass:"zp-custom-refresher-status-bar-placeholder",style:[{height:e.statusBarHeight+"px"}]}):e._e(),i("v-uni-view",{staticClass:"zp-custom-refresher-slot-view"},[e.zSlots.refresherComplete&&e.refresherStatus===e.R.Complete||e.zSlots.refresherF2&&e.refresherStatus===e.R.GoF2?e._e():e._t("refresher",null,{refresherStatus:e.refresherStatus})],2),e.zSlots.refresherComplete&&e.refresherStatus===e.R.Complete?e._t("refresherComplete"):e.zSlots.refresherF2&&e.refresherStatus===e.R.GoF2?e._t("refresherF2"):e.showCustomRefresher?e._e():i("z-paging-refresh",{ref:"refresh",staticClass:"zp-custom-refresher-refresh",style:[{height:e.finalRefresherThreshold-e.finalRefresherThresholdPlaceholder+"px"}],attrs:{status:e.refresherStatus,defaultThemeStyle:e.finalRefresherThemeStyle,defaultText:e.finalRefresherDefaultText,isIos:e.isIos,pullingText:e.finalRefresherPullingText,refreshingText:e.finalRefresherRefreshingText,completeText:e.finalRefresherCompleteText,goF2Text:e.finalRefresherGoF2Text,defaultImg:e.refresherDefaultImg,pullingImg:e.refresherPullingImg,refreshingImg:e.refresherRefreshingImg,completeImg:e.refresherCompleteImg,refreshingAnimated:e.refresherRefreshingAnimated,showUpdateTime:e.showRefresherUpdateTime,updateTimeKey:e.refresherUpdateTimeKey,updateTimeTextMap:e.finalRefresherUpdateTimeTextMap,imgStyle:e.refresherImgStyle,titleStyle:e.refresherTitleStyle,updateTimeStyle:e.refresherUpdateTimeStyle,unit:e.unit}})],2)],1):e._e(),i("v-uni-view",{staticClass:"zp-paging-container",style:[{justifyContent:e.useChatRecordMode?"flex-end":"flex-start"}]},[e.showLoading&&e.zSlots.loading&&!e.loadingFullFixed?e._t("loading"):e._e(),i("v-uni-view",{staticClass:"zp-paging-container-content",style:[e.finalPlaceholderTopHeightStyle,e.finalPagingContentStyle]},[e._t("default"),e.finalUseInnerList?[e._t("header"),i("v-uni-view",{staticClass:"zp-list-container",style:[e.innerListStyle]},[e.finalUseVirtualList?e._l(e.virtualList,(function(t,a){return i("v-uni-view",{key:t["zp_unique_index"],staticClass:"zp-list-cell",style:[e.innerCellStyle],attrs:{id:e.fianlVirtualCellIdPrefix+"-"+t[e.virtualCellIndexKey]},on:{click:function(i){arguments[0]=i=e.$handleEvent(i),e._innerCellClick(t,e.virtualTopRangeIndex+a)}}},[e.useCompatibilityMode?i("v-uni-view",[e._v("使用兼容模式请在组件源码z-paging.vue第105行中注释这一行，并打开下面一行注释")]):e._t("cell",null,{item:t,index:e.virtualTopRangeIndex+a})],2)})):e._l(e.realTotalData,(function(t,a){return i("v-uni-view",{key:a,staticClass:"zp-list-cell",on:{click:function(i){arguments[0]=i=e.$handleEvent(i),e._innerCellClick(t,a)}}},[e._t("cell",null,{item:t,index:a})],2)}))],2),e._t("footer")]:e._e(),e.useChatRecordMode&&e.realTotalData.length>=e.defaultPageSize&&(e.loadingStatus!==e.M.NoMore||e.zSlots.chatNoMore)&&(e.realTotalData.length||e.showChatLoadingWhenReload&&e.showLoading)&&!e.isFirstPageAndNoMore?[i("v-uni-view",{style:[e.chatRecordRotateStyle]},[e.loadingStatus===e.M.NoMore&&e.zSlots.chatNoMore?e._t("chatNoMore"):[e.zSlots.chatLoading?e._t("chatLoading",null,{loadingMoreStatus:e.loadingStatus}):i("z-paging-load-more",{attrs:{zConfig:e.zLoadMoreConfig},on:{doClick:function(t){arguments[0]=t=e.$handleEvent(t),e._onLoadingMore("click")}}})]],2)]:e._e(),e.useVirtualList?i("v-uni-view",{staticClass:"zp-virtual-placeholder",style:[{height:e.virtualPlaceholderBottomHeight+"px"}]}):e._e(),e.showLoadingMoreDefault?e._t("loadingMoreDefault"):e.showLoadingMoreLoading?e._t("loadingMoreLoading"):e.showLoadingMoreNoMore?e._t("loadingMoreNoMore"):e.showLoadingMoreFail?e._t("loadingMoreFail"):e.showLoadingMoreCustom?i("z-paging-load-more",{attrs:{zConfig:e.zLoadMoreConfig},on:{doClick:function(t){arguments[0]=t=e.$handleEvent(t),e._onLoadingMore("click")}}}):e._e(),e.safeAreaInsetBottom&&e.finalUseSafeAreaPlaceholder&&!e.useChatRecordMode?i("v-uni-view",{staticClass:"zp-safe-area-placeholder",style:[{height:e.safeAreaBottom+"px"}]}):e._e()],2),e.showEmpty?i("v-uni-view",{class:{"zp-empty-view":!0,"zp-empty-view-center":e.emptyViewCenter},style:[e.emptyViewSuperStyle,e.chatRecordRotateStyle]},[e.zSlots.empty?e._t("empty",null,{isLoadFailed:e.isLoadFailed}):i("z-paging-empty-view",{attrs:{emptyViewImg:e.finalEmptyViewImg,emptyViewText:e.finalEmptyViewText,showEmptyViewReload:e.finalShowEmptyViewReload,emptyViewReloadText:e.finalEmptyViewReloadText,isLoadFailed:e.isLoadFailed,emptyViewStyle:e.emptyViewStyle,emptyViewTitleStyle:e.emptyViewTitleStyle,emptyViewImgStyle:e.emptyViewImgStyle,emptyViewReloadStyle:e.emptyViewReloadStyle,emptyViewZIndex:e.emptyViewZIndex,emptyViewFixed:e.emptyViewFixed,unit:e.unit},on:{reload:function(t){arguments[0]=t=e.$handleEvent(t),e._emptyViewReload.apply(void 0,arguments)},viewClick:function(t){arguments[0]=t=e.$handleEvent(t),e._emptyViewClick.apply(void 0,arguments)}}})],2):e._e()],2)],1)],1)],1)],1),e.zSlots.right?i("v-uni-view",{class:{"zp-page-right":!0,"zp-absoulte zp-right":e.finalIsOldWebView}},[e._t("right")],2):e._e()],1),i("v-uni-view",{staticClass:"zp-page-bottom-container",style:{background:e.bottomBgColor}},[e.zSlots.bottom?[e.usePageScroll?i("v-uni-view",{staticClass:"zp-page-bottom",style:[{bottom:e.windowBottom+"px",background:e.bottomBgColor}],on:{touchmove:function(t){t.stopPropagation(),t.preventDefault(),arguments[0]=t=e.$handleEvent(t)}}},[e._t("bottom"),e.safeAreaInsetBottom?i("v-uni-view",{style:[{height:e.safeAreaBottom+"px"}]}):e._e()],2):e._t("bottom")]:e._e(),!e.safeAreaInsetBottom||e.usePageScroll||e.finalUseSafeAreaPlaceholder||e.useChatRecordMode?e._e():i("v-uni-view",{style:[{height:e.safeAreaBottom+"px"}]}),e.useChatRecordMode&&e.autoAdjustPositionWhenChat?[i("v-uni-view",{style:[{height:e.chatRecordModeSafeAreaBottom+"px"}]}),i("v-uni-view",{staticClass:"zp-page-bottom-keyboard-placeholder-animate",style:[{height:e.keyboardHeight+"px"}]})]:e._e()],2),e.showBackToTopClass?i("v-uni-view",{class:e.finalBackToTopClass,style:[e.finalBackToTopStyle],on:{click:function(t){t.stopPropagation(),arguments[0]=t=e.$handleEvent(t),e._backToTopClick.apply(void 0,arguments)}}},[e.zSlots.backToTop?e._t("backToTop"):i("v-uni-image",{staticClass:"zp-back-to-top-img",class:{"zp-back-to-top-img-inversion":e.useChatRecordMode&&!e.backToTopImg.length},attrs:{src:e.backToTopImg.length?e.backToTopImg:e.base64BackToTop}})],2):e._e(),e.showLoading&&e.zSlots.loading&&e.loadingFullFixed?i("v-uni-view",{staticClass:"zp-loading-fixed"},[e._t("loading")],2):e._e()],2)},n=[]},a126:function(e,t,i){"use strict";i("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default={base64Arrow:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABkBAMAAACCzIhnAAAAD1BMVEVHcExRUVFMTExRUVFRUVE9CdWsAAAABHRSTlMAjjrY9ZnUjwAAAQFJREFUWMPt2MsNgzAMgGEEE1B1gKJmAIRYoCH7z9RCXrabh33iYktcIv35EEg5ZBh07pvxJU6MFSPOSRnjnBUjUsaciRUjMsb4xIoRCWNiYsUInzE5sWKEyxiYWDbyefqHx1zIeiYTk7mQYziTYecxHvEJjwmIT3hMQELCYSISEg4TkZj0mYTEpM8kJCU9JiMp6TEZyUmbAUhO2gxAQNJiIAKSFgMRmNQZhMCkziAEJTUGIyipMRjBSZkhCE7KDEFIUmTeGCHJxWz0zXaE0GTCG8ZFtEaS347r/1fe11YyHYVfubxayfjoHmc0YYwmmmiiiSaaaKLJ7ckyz5ve+dw3Xw2emdwm9xSbAAAAAElFTkSuQmCC",base64ArrowWhite:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABkBAMAAACCzIhnAAAAElBMVEVHcEz///////////////////+IGTx/AAAABnRSTlMA/dAkXZOhASU/AAABYElEQVRYw+2YwXLCIBCGsdAHWGbyAKZ4zxi9O017rxLf/1UaWFAgA1m8dcpedNSPf/l/Vh0Ya/Wn6hN0JcGvoCqRM4C8VBFiDwBqqNuJKV0rAnCgy3AUqZE57x0iqTL8Br4U3WBf/YWaIlTKfAcELU/h9w72CSVPa3C3OCDvhpHbRp/s2vq4fHhCeiCl2A3m4Qd71DQR257mFBlMcTlbFnFWzNtHxewYEfSiaLS4el8d8nyhmKJd1CF4eOS0keLMAuSxubLBIeIGQW8YHCFFo7EH9+YDcQt9FMZEswTheaNxTHwHT8SZorJjMrEVwo4Zo0U8HSEyZvJMOg4RjnmmRr8nDYeIz3OMkbfE/QhBo+U9RnZJxjGCRh/WKmHEMWLNkfPKsGh/CWJk1JjG0kcuJggTt34VDP8aWAFhp4nybVb5+9qQhjSkIQ1pSEMa8k+Q5U9rV3dF8MpFBK+/7miVq1/HZ2qmo9D+pAAAAABJRU5ErkJggg==",base64Flower:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABkBAMAAACCzIhnAAAAKlBMVEVHcEzDw8Ovr6+pqamUlJTCwsKenp61tbWxsbGysrLNzc2bm5u5ubmjo6MpovhuAAAACnRSTlMA/P79/sHDhiZS0DxZowAABBBJREFUWMPtl89rE0EUx7ctTXatB3MI1SWnDbUKPUgXqh4ED8Uf7KUVSm3ooVSpSii0Fn/gD4j4o+APiEoVmos9FO2celiqZVgwgaKHPQiCCkv+F99kM7Ozm5kxq1dfD91k9pPve9/3ZjbRNHHok/mKli4eIPNgSuRObuN9SqSEzM20iGnm0yIbqCuV7NSSSIV7uyPM6JMBYdeTOanh/QihJYZsUCSby+VkMj2AvOt0rAeQAwqE3lfKMZVlQCZk1QOCKkkVPadITCfIRNKxfoJI5+0OIFtJx14CMSg1mRSDko7VAfksRQzEbGYqxOJcVTWMCH2I1/IACNW0PWU2M8cmAVHtnH5mM1VRWtwKZjOd5JbF6s1IbaYqaotjNlPHgDAnlAizubTR6ovMYn052g/U5qcmOpi0WL8xTS/3IfSet5m8MEr5ajjF5le6dq/OJpobrdY0t3i9QgefWrxW9/1BLhk0E9m8FeUMhhXal499iD0eQRfDF+ts/tttORRerfp+oV7f4xJj82iUYm1Yzod+ZQEAlS/8mMBwKebVmCVp1f0JLS6zKd17+iwRKTARVg2SHtz3iEbBH+Q+U28zW2Jiza8Tjb1YFoYZMsJyjDqp3M9XBQdSdPLFdxEpvOB37JrHcmR/y9+LgoTlCFGZEa2sc6d4PGlweEa2JSVPoVm+IfGG3ZL037iV9oH+P+Jxc4HGVflNq1M0pivao/EopO4b/ojVCP9GjmiXOeS0DOn1o/iiccT4ORnyvBGF3yUywkQajW4Ti0SGuiy/wVSg/L8w+X/8Q+hvUx8Xd90z4oV5a1i88MbFWHz0WZZ1UrTwBGPX3Rat9AFiXRMRjoMdIdJLEOt2h7jrYOzgOamKZSWSNspOS0X8SAqRYmxRL7sg4eLzYmNehcxh3uoyud/BH2Udux4ywxFTc1xC7Mgf4vMhc5S+kSH3Y7yj+qpwIWSoPTVCOOPVthGx9FbGqrwFw6wSFxJr+17zeKcztt3u+2roAEVgUjDd+AHGuxHy2rZHaa8JMkTHEeyi85ANPO9j9BVuBRD2FY5LDMo/Sz/2hReqGIs/KiFin+CsPsYO/yvM3jL2vE8EbX7/Bf8ejtr2GLN65bioAdgLd8Bis/mD5GmP2qeqyo2ZwQEOtAjRIDH7mBKpUcMoApbZJ5UIxkEwxyMZyMxW/uKFvHCFR3SSmerHyDNQ2dF4JG6zIMpBgLfjSF9x1D6smFcYnGApjmSLICO3ecCDWrQ48geba9DI3STy2i7ax6WIB62fSyIZIiO3GFQqSURp8wCo7GhJBGwuSovJBNjb7kT6FPVnIa9qJ2Ko+l9mefGIdinaMp0yC1URYiwsdfNE45EuA5Cx9EhalfvN5s+UyItm81vaB3p4joniN+SCP7Qc1hblAAAAAElFTkSuQmCC",base64FlowerWhite:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABkBAMAAACCzIhnAAAAElBMVEX///9HcEz///////////////84chYNAAAABnRSTlP/AGzCOYZj5g1nAAACfklEQVRYw+2YTVPDIBCGtza9Jw25a0bvcax30o73OOr//yvma2F3YWlpPTijXNpAHrK8LLALVPFium2vNIFSbwGKTGQA2GUiHcD29yDNy3sMIdUBQl7r2H8mOEVqAHgPkYZUS6Qc2zYhQqtjyDZEximCZwWZLIBeIgYShs2NzxKpSUehYpMJhURGb+O+w5BpMCAREKPnCDHbIY20SzhM5yxziAXpOiBXydrekT9i5XDEq4NIIHHgyU5mRGqviII4mREJJA4QJzMiILwlRJzpKxJKvCBm8OsBBbLux0tsPl4RKYm5aPu6jw1U4mGxEUR9g8M1PcqBEp/WJliNgYOXueBzS4jZSIcgY5lCtevgDSgyzE+rAfuOTQMq0yzvoGH18qju27Mayzs4fPyMziCx81NJa5RNfW7vPYK9KOfDiVkBxFHG8hAj9txuoBuSWORsFfkpBf7xKFLSeaOefEojh5jz22DJEqMP8fUyaKdQx+RnG+yXMpe8Aars8ueR1pVH/bW3FyyvPRw90upLDHwpgBDtg4aUBNkxRLXMAi03IhcZtr1m+FeI/O/JNyDmmL1djLOauSlNflBpW18RQ2bPqXI22MXXEk75KRHTnkPkYbESbdKP2ZFk0r5sIwffAjy1lx+vx7NLjB6/E7Jfv5ERKhzpN0w8IDE8IGFDv5dhz10s7GFiXRZcUeLCEG5P5nDq9k4PFDcoMpE3GY4OuxuCXhmuyNB6k0RsLIAvqp9NE5r8ZCSS8gxnUp7ODdYhZTqxuiJ9uyJJtPmpqJ7wVj+XVieS903iViHziqAhchLEJAyb7jWU647EpUofQ0ziUuXXXhDddtlllSwjgSQu7r4BRWhQqfDPMVwAAAAASUVORK5CYII=",base64Success:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABkBAMAAACCzIhnAAAAElBMVEVRUVFHcExTU1NRUVFRUVFRUVFOSlSUAAAABnRSTlP/AI6+VySB3ZENAAACcElEQVRYw+2YyYKCMAyGI8hdpdxdZu7gcpdZ7jL6/s8yYheSNi0aPdqbwOffpGmaFOYPD3gj4bisN7vddv17N/JVgxn5x12IWgIaWTuO/IE3PseQbwjGPo2cgRmHFLJwdm/X643zwiqOKPPJ1nj3sjEP2iiifZWj5bhopSyGaEO2HX5fbQJzwJ+W7x/jw5ZFjsEU0PMph9xE8i5EqprKALW95eJQURkgzw98uJ/JvwGecR7bIjWWsUgVrrIfFZ2HlLy3sKETD1mmRLRMRhGVssRa0xJkdn3SpJBymBkM8+pSSDXMDNyDaToVHd2fgpNt0sjwiUZO19+jGQ+gQEg9Oq+bufmAVGihomNmjQG7UG3020vrlm7lkFnKFGU3kZ0KGAdmKe821pipQ+qEKcrZeTL2g5FsUks4cStjEZWwXg0b0n4GxmEpkWwIs5VBynjgK7xZaz1/0D7OxkVuLpsY5BQNFyLS84VBjjbg0iL2r2EQHBOxBhikuUOkdxODVF1cxHoWtPPsiyXO455Iv34hssCO8EV4ZIYTjS8SR4qYSHRiTiYQ4ZFbHi0iIhhBTi6dTCgSWRcnw4h4yGTuyTAiOGBIWGoZTgSHJQl+LcOJ4OCnW6yX2bMnJ9pidCOXtkTkTrIGpYuOynAiOF14SamMiOCk5Ke+mq8BcOrrvym8d0zKIQnWT+M1WwOQNO4fFiWb18hhERxJPx2fblbPHHyC41VyiAtKBUFBIih7JMWVoIQTFIr3lKPN80WvoLSWFPC653ioTZA0I0FrQ7qU6asaK0H7JmkSJa2ooOGVtNUsc3j9FYHkIkJy3SG6VHnfXKXGP9t4N9Q4Ye98AAAAAElFTkSuQmCC",base64SuccessWhite:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABkBAMAAACCzIhnAAAAGFBMVEVHcEz///////////////////////////8dS1W+AAAAB3RSTlMAiVYk6KvDHLfaegAAAo1JREFUWMPtWEtzmzAQNhCTq910ytXpiyvxTNOr60zrayepx9d02gnX4sTm7xcEiJX2gdnkGJ1A4tOnfWqXyeR1vMRYzrcPD9v5h5MBl3/Ldvx4cxIg/FWC8X0xjLjalM54uhhCfCrRuJURX0pi3EmIqZV7O59vrRZmguStHL9b7S7ftfLwOtiZDw7AHMtmquAQ12b5Wwbnordm8g9zLLO49qc/m2n6aKnhwPOGZ08hAiNHhheiHae1lOUPGZpQkPKa3q0mOUjaRzSRaGUjpy/mmWSwySSpllcEteBKAT52KEnSbblA51pJEPxBQoiH1FP4E3s5+FJv07h6/ylD6ui7B+9fq/ehrFB98ghec9EoVtyjK8pqCHLmCBOwMWSCeWFNN4MbPAk55NhsvoFHSSVR0k5TCTTEzlUGcqV/nVp7n9oIVkmtaqbAEqEgfdgHJPwsEAyZ9r4VAZXFjpEwyaw3+H2v42KYxKhs1XvY/gSSGv+IHyUSuHXCeZhLAgVI3EjgSGo1Fb3xO0tGGU9S2/KAIbtjxpJASG73qox6w5LUq0cEOa+iIONIWIilQSQ0pPa2jgaRQAgQP7c0mITRWGxpMAmEQFN2NAQJNCV0mI6GIIEO47hlQ0ORQLd0nL+hoUjg1m6I1TRr8uYEAriBHLcVFQ5UEMiBe3XkTBEG04WXlGKGxPnMS305XQPA1Ocn2JiuAZwE66fxnKwBnDTuXxZTMq85lwW6kt5ndLqZPefiU1yvmktcUSooChJF2aMprhQlnKJQ5FxRKkcVRa+itNYU8Io2oVkY14w0NMWYlqft91Bj9VHq+ca3b43BxjWJmla0sfKohlfTVpPN+93L/yLQ/IjQ/O5Q/VR5HdL4D7mlxmjwVdELAAAAAElFTkSuQmCC",base64Empty:"data:image/png;base64,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",base64Error:"data:image/png;base64,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",base64BackToTop:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMgAAADIBAMAAABfdrOtAAAAElBMVEVRUVH+/v5HcEyZmZlRUVFRUVGm1ByOAAAABnRSTlPMzADMTZAJBBGsAAAEnElEQVR42t2cS27jMAyGf7/2U+QCQeDsbeQCgZDujaC5/1UmkzaJn+JDFGcw3LdfflKibJkkDnxrL7dbg7sNt6+L4O8OYBM+B0ys+QrGkHZG+OEEQ8g6go8Bx1GIGMdpNOQyIG6XdMgnSPtKhLQDGEZFBgYMkhKFtGBb0EIEjDgFRowoBVaMGAWpMedEfxMiZtwpUsgZCqtlkCNUdpVAWigtCCCDFtLwIWeoreZCWiRYYEKGFEjDg+yRZCUH0iLRAgNyToXUNCRZyMqWhGnUN2IPm3wSlwJ7IUspyCBkIQUZhCykIIeQuRTkEDKXAuM9srrtYbrZN7Y98giZSoFd+t1OxmMITG0dcrSFXFchZ1tIvQZpYWxhBbK3hpQrkMEa0iwh5t4a+QvZvDXyF7J5a+Qv5PPW21/I5623v5DPW29/IaO3Xv5Clrw1y1/Ikrdm+Qs5svw83yNnSJ5BQb4F/F7EIEJSnThGBAXxkFQfLOviQUE8JAUPsosHBfGQfDAtHhREQ1JxIV00KIgmrnRI84S0yAd5BAXxxJUck0f6Qnwr9qmr6xF5xLMjcwn/iudIEAdWnyjkEXlQKZiRVzoqRyLbgeUKKR8Q4alY7cSnoxzSf2ggsqehKr6YVpcXpOd7H93f60cKhOd7Re2LteUF4eLqiVS1mr0ge4io6C2+soaFkJ7MuuuQs1yITEp9hwwKISIpzR2iESKSIoT0rLNwuVHQqoSIpAQJpGce60vIUSdEIuUqgPTsJ5QFZK8UIpBS8iG94GFrDjlrhfCl8CG96Llxmle4kEr6vKWBPIVo9kqDQSRk9/3cWoikcCFPAd33v4dIChPyEvLzBA6RlEYWke4JEUnhKXkLeUEKxRHJFfKCQHGucIW8IdZSRkLeEGMpYyEjiK2UsZARxFTKRMgYYillImQMMZQyFTKB2EmZCplAuFLIHT8TMoWwpQwiIVMIUwqpZP5bp5CCvCTiQKr5f5lCQN+tPCBn2ZvVDFJwIDUP0m1BYAfZYRNSsCB7BqTbhoARePIxtZ9tgwWkoJcwCalmv3MBAemtO4R6dah2HaKQqj8Zvp9sQDjvJ21+SPCBHPJDDk6QITekEV7gqCC19CpKAym9IMfckKv4olMBCeIrWwVEfvkshzQekO9r9P1/ALk+IG1eSPCDiCJfyG+FyU+A6ZCa/piZDinpz7LpkCv5gdkAEshP5emQhv7onw6pGeULyZCSUYiRDAmMkpJkCKs4JhFSq8p8hJBSVbAkhARV6ZUQoisik0FqXTmcDHLVFfbJIEFXoiiCNMpiSxGkVJaNiiBBWQArgTTaUl4JpNQWJUsgQVteXQg+AKkLxQWFGKW+5J2+eVp4S168X3CF1CltCKdTJ8lb84YK2bUBO+wZW0Pqv9nk4tKu49N45NJC5dMM5tLW5tOg59Jq6NM06dL+abFXwr/RkuvTXJwae1abtE/Dt0/ruksTvs84AZ/BCC4jHnyGVfiM3VBQFANEXEah+Ax18RlP4zNox2dkkM/wI58xTn8yDCXGYCDV3W5RGSajtXyGhG1jbpbjzpwGt/0MJft8jqC7iUbQ/QZaxdnKqcIftwAAAABJRU5ErkJggg=="}},a179:function(e,t,i){"use strict";i("6a54");var a=i("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=a(i("3918")),n={startY:0,isTouchFromZPaging:!1,isUsePageScroll:!1,isReachedTop:!0,isIosAndH5:!1,useChatRecordMode:!1,appLaunched:!1},r={mounted:function(){window&&this._handleTouch()},methods:{renderPropIsIosAndH5Change:function(e){-1!==e&&(n.isIosAndH5=e)},_handleTouch:function(){window.$zPagingRenderJsInited||(window.$zPagingRenderJsInited=!0,window.addEventListener("touchstart",this._handleTouchstart,{passive:!0}),window.addEventListener("touchmove",this._handleTouchmove,{passive:!1}))},_handleTouchstart:function(e){var t=o.default.getTouch(e);n.startY=t.touchY;var i=o.default.getTouchFromZPaging(e.target);n.isTouchFromZPaging=i.isFromZp,n.isUsePageScroll=i.isPageScroll,n.isReachedTop=i.isReachedTop,n.useChatRecordMode=i.isUseChatRecordMode},_handleTouchmove:function(e){var t=o.default.getTouch(e),i=t.touchY-n.startY;n.isTouchFromZPaging&&(n.isReachedTop&&(n.useChatRecordMode?i<0:i>0)||!n.useChatRecordMode&&n.isIosAndH5&&!n.isUsePageScroll&&i<0)&&e.cancelable&&!e.defaultPrevented&&e.preventDefault()},_removeAllEventListener:function(){window.removeEventListener("touchstart"),window.removeEventListener("touchmove")}}};t.default=r},a2e4:function(e,t,i){"use strict";i.r(t);var a=i("53e0"),o=i.n(a);for(var n in a)["default"].indexOf(n)<0&&function(e){i.d(t,e,(function(){return a[e]}))}(n);t["default"]=o.a},a674:function(e,t,i){"use strict";i("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default={LoadingType:{Refresher:"refresher",LoadMore:"load-more"},Refresher:{Default:"default",ReleaseToRefresh:"release-to-refresh",Loading:"loading",Complete:"complete",GoF2:"go-f2"},More:{Default:"default",Loading:"loading",NoMore:"no-more",Fail:"fail"},QueryFrom:{UserPullDown:"user-pull-down",Reload:"reload",Refresh:"refresh",LoadMore:"load-more"},CellHeightMode:{Fixed:"fixed",Dynamic:"dynamic"},CacheMode:{Default:"default",Always:"always"}}},b3e2:function(e,t,i){"use strict";var a,o=i("c238"),n=i("85c1"),r=i("bb80"),l=i("a74c"),s=i("d0b1"),d=i("7658"),h=i("d871c"),u=i("1c06"),c=i("235c").enforce,f=i("af9e"),g=i("a20b"),p=Object,m=Array.isArray,v=p.isExtensible,y=p.isFrozen,T=p.isSealed,w=p.freeze,S=p.seal,b=!n.ActiveXObject&&"ActiveXObject"in n,x=function(e){return function(){return e(this,arguments.length?arguments[0]:void 0)}},R=d("WeakMap",x,h),M=R.prototype,C=r(M.set);if(g)if(b){a=h.getConstructor(x,"WeakMap",!0),s.enable();var A=r(M["delete"]),I=r(M.has),z=r(M.get);l(M,{delete:function(e){if(u(e)&&!v(e)){var t=c(this);return t.frozen||(t.frozen=new a),A(this,e)||t.frozen["delete"](e)}return A(this,e)},has:function(e){if(u(e)&&!v(e)){var t=c(this);return t.frozen||(t.frozen=new a),I(this,e)||t.frozen.has(e)}return I(this,e)},get:function(e){if(u(e)&&!v(e)){var t=c(this);return t.frozen||(t.frozen=new a),I(this,e)?z(this,e):t.frozen.get(e)}return z(this,e)},set:function(e,t){if(u(e)&&!v(e)){var i=c(this);i.frozen||(i.frozen=new a),I(this,e)?C(this,e,t):i.frozen.set(e,t)}else C(this,e,t);return this}})}else(function(){return o&&f((function(){var e=w([]);return C(new R,e,1),!y(e)}))})()&&l(M,{set:function(e,t){var i;return m(e)&&(y(e)?i=w:T(e)&&(i=S)),C(this,e,t),i&&i(e),this}})},bb24:function(e,t,i){"use strict";i("6a54");var a=i("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=a(i("3918")),n=(a(i("2a2e")),a(i("a674")),{props:{},data:function(){return{nRefresherLoading:!1,nListIsDragging:!1,nShowBottom:!0,nFixFreezing:!1,nShowRefresherReveal:!1,nLoadingMoreFixedHeight:!1,nShowRefresherRevealHeight:0,nOldShowRefresherRevealHeight:-1,nRefresherWidth:o.default.rpx2px(750),nListHeight:0,nF2Opacity:0}},computed:{},mounted:function(){},methods:{}});t.default=n},bbf1:function(e,t,i){"use strict";var a=i("60ff"),o=i.n(a);o.a},bc91:function(e,t){},bd07:function(e){e.exports=JSON.parse('{"zp.refresher.default":"Pull down to refresh","zp.refresher.pulling":"Release to refresh","zp.refresher.refreshing":"Refreshing...","zp.refresher.complete":"Refresh succeeded","zp.refresher.f2":"Refresh to enter 2f","zp.loadingMore.default":"Click to load more","zp.loadingMore.loading":"Loading...","zp.loadingMore.noMore":"No more data","zp.loadingMore.fail":"Load failed,click to reload","zp.emptyView.title":"No data","zp.emptyView.reload":"Reload","zp.emptyView.error":"Sorry,load failed","zp.refresherUpdateTime.title":"Last update: ","zp.refresherUpdateTime.none":"None","zp.refresherUpdateTime.today":"Today","zp.refresherUpdateTime.yesterday":"Yesterday","zp.systemLoading.title":"Loading..."}')},c238:function(e,t,i){"use strict";var a=i("af9e");e.exports=!a((function(){return Object.isExtensible(Object.preventExtensions({}))}))},cb25:function(e,t,i){var a=i("c86c");t=a(!1),t.push([e.i,"/* [z-paging]公用的静态css资源 */.zp-line-loading-image[data-v-6737cf27]{\n-webkit-animation:loading-flower-data-v-6737cf27 1s steps(12) infinite;animation:loading-flower-data-v-6737cf27 1s steps(12) infinite;\ncolor:#666}.zp-line-loading-image-rpx[data-v-6737cf27]{margin-right:%?8?%;width:%?34?%;height:%?34?%}.zp-line-loading-image-px[data-v-6737cf27]{margin-right:4px;width:17px;height:17px}.zp-loading-image-ios-rpx[data-v-6737cf27]{width:%?40?%;height:%?40?%}.zp-loading-image-ios-px[data-v-6737cf27]{width:20px;height:20px}.zp-loading-image-android-rpx[data-v-6737cf27]{width:%?34?%;height:%?34?%}.zp-loading-image-android-px[data-v-6737cf27]{width:17px;height:17px}\n@-webkit-keyframes loading-flower-data-v-6737cf27{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}@keyframes loading-flower-data-v-6737cf27{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}\n.zp-r-container[data-v-6737cf27]{\ndisplay:flex;height:100%;\nflex-direction:row;justify-content:center;align-items:center}.zp-r-container-padding[data-v-6737cf27]{\n}.zp-r-left[data-v-6737cf27]{\ndisplay:flex;\nflex-direction:row;align-items:center;overflow:hidden;\n}.zp-r-left-image[data-v-6737cf27]{transition-duration:.2s;transition-property:-webkit-transform;transition-property:transform;transition-property:transform,-webkit-transform;color:#666}.zp-r-left-image-pre-size-rpx[data-v-6737cf27]{\nwidth:%?34?%;height:%?34?%;overflow:hidden\n}.zp-r-left-image-pre-size-px[data-v-6737cf27]{\nwidth:17px;height:17px;overflow:hidden\n}.zp-r-arrow-top[data-v-6737cf27]{-webkit-transform:rotate(0deg);transform:rotate(0deg)}.zp-r-arrow-down[data-v-6737cf27]{-webkit-transform:rotate(180deg);transform:rotate(180deg)}.zp-r-right[data-v-6737cf27]{\ndisplay:flex;\nflex-direction:column;align-items:center;justify-content:center}.zp-r-right-time-text-rpx[data-v-6737cf27]{margin-top:%?10?%;font-size:%?26?%}.zp-r-right-time-text-px[data-v-6737cf27]{margin-top:5px;font-size:13px}",""]),e.exports=t},cddc:function(e,t,i){"use strict";i("6a54");var a=i("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=a(i("9b1b"));function n(){return getApp()}function r(){return n()&&n().globalData}function l(e,t){try{setTimeout((function(){r()&&(n().globalData["zp_handle".concat(e,"Callback")]=t)}),1)}catch(i){}}function s(e){return r()?n().globalData["zp_handle".concat(e,"Callback")]:null}var d={handleQuery:function(e){return l("Query",e),this},_handleQuery:function(e,t,i,a){var o=s("Query");return o?o(e,t,i,a):[e,t,i]},handleFetchParams:function(e){return l("FetchParams",e),this},_handleFetchParams:function(e,t){var i=s("FetchParams");return i?i(e,t||{}):(0,o.default)({pageNo:e.pageNo,pageSize:e.pageSize},t||{})},handleFetchResult:function(e){return l("FetchResult",e),this},_handleFetchResult:function(e,t,i){var a=s("FetchResult");return a&&a(e,t,i),!!a},handleLanguage2Local:function(e){return l("Language2Local",e),this},_handleLanguage2Local:function(e,t){var i=s("Language2Local");return i?i(e,t):t}};t.default=d},d0af:function(e,t,i){"use strict";i("b3e2")},d0b1:function(e,t,i){"use strict";var a=i("8bdb"),o=i("bb80"),n=i("11bf"),r=i("1c06"),l=i("338c"),s=i("d6b1").f,d=i("80bb"),h=i("8449"),u=i("1ea2"),c=i("d7b4"),f=i("c238"),g=!1,p=c("meta"),m=0,v=function(e){s(e,p,{value:{objectID:"O"+m++,weakData:{}}})},y=e.exports={enable:function(){y.enable=function(){},g=!0;var e=d.f,t=o([].splice),i={};i[p]=1,e(i).length&&(d.f=function(i){for(var a=e(i),o=0,n=a.length;o<n;o++)if(a[o]===p){t(a,o,1);break}return a},a({target:"Object",stat:!0,forced:!0},{getOwnPropertyNames:h.f}))},fastKey:function(e,t){if(!r(e))return"symbol"==typeof e?e:("string"==typeof e?"S":"P")+e;if(!l(e,p)){if(!u(e))return"F";if(!t)return"E";v(e)}return e[p].objectID},getWeakData:function(e,t){if(!l(e,p)){if(!u(e))return!0;if(!t)return!1;v(e)}return e[p].weakData},onFreeze:function(e){return f&&g&&u(e)&&!l(e,p)&&v(e),e}};n[p]=!0},d545:function(e,t,i){var a=i("4681");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[e.i,a,""]]),a.locals&&(e.exports=a.locals);var o=i("967d").default;o("61da2760",a,!0,{sourceMap:!1,shadowMode:!1})},d72d:function(e,t,i){"use strict";i("6a54");var a=i("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,i("bf0f"),i("5c47"),i("a1c1"),i("23f4"),i("7d2f"),i("9c4e"),i("ab80"),i("5ef2");var o=i("d3b4"),n=a(i("2843")),r=(a(i("3918")),a(i("2a2e")),a(i("cddc"))),l=(0,o.initVueI18n)(n.default),s=l.t,d={computed:{finalLanguage:function(){try{var e=uni.getLocale(),t=this.systemInfo.appLanguage;return"auto"===e?r.default._handleLanguage2Local(t,this._language2Local(t)):e}catch(i){return"zh-Hans"}},finalRefresherDefaultText:function(){return this._getI18nText("zp.refresher.default",this.refresherDefaultText)},finalRefresherPullingText:function(){return this._getI18nText("zp.refresher.pulling",this.refresherPullingText)},finalRefresherRefreshingText:function(){return this._getI18nText("zp.refresher.refreshing",this.refresherRefreshingText)},finalRefresherCompleteText:function(){return this._getI18nText("zp.refresher.complete",this.refresherCompleteText)},finalRefresherUpdateTimeTextMap:function(){return{title:s("zp.refresherUpdateTime.title"),none:s("zp.refresherUpdateTime.none"),today:s("zp.refresherUpdateTime.today"),yesterday:s("zp.refresherUpdateTime.yesterday")}},finalRefresherGoF2Text:function(){return this._getI18nText("zp.refresher.f2",this.refresherGoF2Text)},finalLoadingMoreDefaultText:function(){return this._getI18nText("zp.loadingMore.default",this.loadingMoreDefaultText)},finalLoadingMoreLoadingText:function(){return this._getI18nText("zp.loadingMore.loading",this.loadingMoreLoadingText)},finalLoadingMoreNoMoreText:function(){return this._getI18nText("zp.loadingMore.noMore",this.loadingMoreNoMoreText)},finalLoadingMoreFailText:function(){return this._getI18nText("zp.loadingMore.fail",this.loadingMoreFailText)},finalEmptyViewText:function(){return this.isLoadFailed?this.finalEmptyViewErrorText:this._getI18nText("zp.emptyView.title",this.emptyViewText)},finalEmptyViewReloadText:function(){return this._getI18nText("zp.emptyView.reload",this.emptyViewReloadText)},finalEmptyViewErrorText:function(){return this.customerEmptyViewErrorText||this._getI18nText("zp.emptyView.error",this.emptyViewErrorText)},finalSystemLoadingText:function(){return this._getI18nText("zp.systemLoading.title",this.systemLoadingText)}},methods:{getLanguage:function(){return this.finalLanguage},_getI18nText:function(e,t){var i=Object.prototype.toString.call(t);if("[object Object]"===i){var a=t[this.finalLanguage];if(a)return a}else if("[object String]"===i)return t;return s(e)},_language2Local:function(e){var t=e.toLowerCase().replace(new RegExp("_",""),"-");return-1!==t.indexOf("zh")?"zh"===t||"zh-cn"===t||-1!==t.indexOf("zh-hans")?"zh-Hans":"zh-Hant":-1!==t.indexOf("en")?"en":e}}};t.default=d},d761:function(e,t,i){"use strict";var a=i("e587"),o=i.n(a);o.a},d871c:function(e,t,i){"use strict";var a=i("bb80"),o=i("a74c"),n=i("d0b1").getWeakData,r=i("b720"),l=i("e7e3"),s=i("1eb8"),d=i("1c06"),h=i("5075"),u=i("4d16"),c=i("338c"),f=i("235c"),g=f.set,p=f.getterFor,m=u.find,v=u.findIndex,y=a([].splice),T=0,w=function(e){return e.frozen||(e.frozen=new S)},S=function(){this.entries=[]},b=function(e,t){return m(e.entries,(function(e){return e[0]===t}))};S.prototype={get:function(e){var t=b(this,e);if(t)return t[1]},has:function(e){return!!b(this,e)},set:function(e,t){var i=b(this,e);i?i[1]=t:this.entries.push([e,t])},delete:function(e){var t=v(this.entries,(function(t){return t[0]===e}));return~t&&y(this.entries,t,1),!!~t}},e.exports={getConstructor:function(e,t,i,a){var u=e((function(e,o){r(e,f),g(e,{type:t,id:T++,frozen:void 0}),s(o)||h(o,e[a],{that:e,AS_ENTRIES:i})})),f=u.prototype,m=p(t),v=function(e,t,i){var a=m(e),o=n(l(t),!0);return!0===o?w(a).set(t,i):o[a.id]=i,e};return o(f,{delete:function(e){var t=m(this);if(!d(e))return!1;var i=n(e);return!0===i?w(t)["delete"](e):i&&c(i,t.id)&&delete i[t.id]},has:function(e){var t=m(this);if(!d(e))return!1;var i=n(e);return!0===i?w(t).has(e):i&&c(i,t.id)}}),o(f,i?{get:function(e){var t=m(this);if(d(e)){var i=n(e);return!0===i?w(t).get(e):i?i[t.id]:void 0}},set:function(e,t){return v(this,e,t)}}:{add:function(e){return v(this,e,!0)}}),u}}},da16:function(e,t,i){"use strict";i("6a54");var a=i("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=a(i("3918")),n=a(i("a674")),r={props:{autoHideLoadingAfterFirstLoaded:{type:Boolean,default:o.default.gc("autoHideLoadingAfterFirstLoaded",!0)},loadingFullFixed:{type:Boolean,default:o.default.gc("loadingFullFixed",!1)},autoShowSystemLoading:{type:Boolean,default:o.default.gc("autoShowSystemLoading",!1)},systemLoadingMask:{type:Boolean,default:o.default.gc("systemLoadingMask",!0)},systemLoadingText:{type:[String,Object],default:o.default.gc("systemLoadingText",null)}},data:function(){return{loading:!1,loadingForNow:!1}},watch:{loadingStatus:function(e){var t=this;this.$emit("loadingStatusChange",e),this.$nextTick((function(){t.loadingStatusAfterRender=e})),!this.useChatRecordMode||!this.isFirstPage||e!==n.default.More.NoMore&&e!==n.default.More.Fail?this.isFirstPageAndNoMore=!1:this.isFirstPageAndNoMore=!0},loading:function(e){e&&(this.loadingForNow=e)}},computed:{showLoading:function(){return!(this.firstPageLoaded||!this.loading||!this.loadingForNow)&&(this.finalShowSystemLoading&&uni.showLoading({title:this.finalSystemLoadingText,mask:this.systemLoadingMask}),this.autoHideLoadingAfterFirstLoaded?!!this.fromEmptyViewReload||!this.pagingLoaded:this.loadingType===n.default.LoadingType.Refresher)},finalShowSystemLoading:function(){return this.autoShowSystemLoading&&this.loadingType===n.default.LoadingType.Refresher}},methods:{_startLoading:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];(this.showLoadingMoreWhenReload&&!this.isUserPullDown||!e)&&(this.loadingStatus=n.default.More.Loading),this.loading=!0},_endSystemLoadingAndRefresh:function(){this.finalShowSystemLoading&&uni.hideLoading(),!this.useCustomRefresher&&uni.stopPullDownRefresh()}}};t.default=r},e1f9:function(e){e.exports=JSON.parse('{"zp.refresher.default":"继续下拉刷新","zp.refresher.pulling":"松开立即刷新","zp.refresher.refreshing":"正在刷新...","zp.refresher.complete":"刷新成功","zp.refresher.f2":"松手进入二楼","zp.loadingMore.default":"点击加载更多","zp.loadingMore.loading":"正在加载...","zp.loadingMore.noMore":"没有更多了","zp.loadingMore.fail":"加载失败，点击重新加载","zp.emptyView.title":"没有数据哦~","zp.emptyView.reload":"重新加载","zp.emptyView.error":"很抱歉，加载失败","zp.refresherUpdateTime.title":"最后更新：","zp.refresherUpdateTime.none":"无","zp.refresherUpdateTime.today":"今天","zp.refresherUpdateTime.yesterday":"昨天","zp.systemLoading.title":"加载中..."}')},e488:function(e,t,i){"use strict";i("6a54");var a=i("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=a(i("5de6")),n=a(i("b7c7"));i("64aa"),i("c223"),i("bf0f"),i("dd2b"),i("f7a5"),i("dc69");var r=a(i("3918")),l=a(i("2a2e")),s=a(i("a674")),d=a(i("cddc")),h={props:{defaultPageNo:{type:Number,default:r.default.gc("defaultPageNo",1),observer:function(e){this.pageNo=e}},defaultPageSize:{type:Number,default:r.default.gc("defaultPageSize",10),validator:function(e){return e<=0&&r.default.consoleErr("default-page-size必须大于0！"),e>0}},dataKey:{type:[Number,String,Object],default:r.default.gc("dataKey",null)},useCache:{type:Boolean,default:r.default.gc("useCache",!1)},cacheKey:{type:String,default:r.default.gc("cacheKey",null)},cacheMode:{type:String,default:r.default.gc("cacheMode",s.default.CacheMode.Default)},autowireListName:{type:String,default:r.default.gc("autowireListName","")},autowireQueryName:{type:String,default:r.default.gc("autowireQueryName","")},fetch:{type:Function,default:null},fetchParams:{type:Object,default:r.default.gc("fetchParams",null)},auto:{type:Boolean,default:r.default.gc("auto",!0)},reloadWhenRefresh:{type:Boolean,default:r.default.gc("reloadWhenRefresh",!0)},autoScrollToTopWhenReload:{type:Boolean,default:r.default.gc("autoScrollToTopWhenReload",!0)},autoCleanListWhenReload:{type:Boolean,default:r.default.gc("autoCleanListWhenReload",!0)},showRefresherWhenReload:{type:Boolean,default:r.default.gc("showRefresherWhenReload",!1)},showLoadingMoreWhenReload:{type:Boolean,default:r.default.gc("showLoadingMoreWhenReload",!1)},createdReload:{type:Boolean,default:r.default.gc("createdReload",!1)},localPagingLoadingTime:{type:[Number,String],default:r.default.gc("localPagingLoadingTime",200)},concat:{type:Boolean,default:r.default.gc("concat",!0)},callNetworkReject:{type:Boolean,default:r.default.gc("callNetworkReject",!0)},value:{type:Array,default:function(){return[]}}},data:function(){return{currentData:[],totalData:[],realTotalData:[],totalLocalPagingList:[],dataPromiseResultMap:{reload:null,complete:null,localPaging:null},isSettingCacheList:!1,pageNo:1,currentRefreshPageSize:0,isLocalPaging:!1,isAddedData:!1,isTotalChangeFromAddData:!1,privateConcat:!0,myParentQuery:-1,firstPageLoaded:!1,pagingLoaded:!1,loaded:!1,isUserReload:!0,fromEmptyViewReload:!1,queryFrom:"",listRendering:!1,isHandlingRefreshToPage:!1,isFirstPageAndNoMore:!1,totalDataChangeThrow:!0,addDataFromTopBufferedInsert:r.default.useBufferedInsert(this._addDataFromTop)}},computed:{pageSize:function(){return this.defaultPageSize},finalConcat:function(){return this.concat&&this.privateConcat},finalUseCache:function(){return this.useCache&&!this.cacheKey&&r.default.consoleErr("use-cache为true时，必须设置cache-key，否则缓存无效！"),this.useCache&&!!this.cacheKey},finalCacheKey:function(){return this.cacheKey?"".concat(l.default.cachePrefixKey,"-").concat(this.cacheKey):null},isFirstPage:function(){return this.pageNo===this.defaultPageNo}},watch:{totalData:function(e,t){this._totalDataChange(e,t,this.totalDataChangeThrow),this.totalDataChangeThrow=!0},currentData:function(e,t){this._currentDataChange(e,t)},useChatRecordMode:function(e,t){e&&(this.nLoadingMoreFixedHeight=!1)},value:{handler:function(e){e!==this.totalData&&(this.totalDataChangeThrow=!1,this.totalData=e)},immediate:!0}},methods:{complete:function(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];return this.customNoMore=-1,this.addData(e,t)},completeByKey:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,i=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];return null!==t&&null!==this.dataKey&&t!==this.dataKey?(this.isFirstPage&&this.endRefresh(),new Promise((function(e){return e()}))):(this.customNoMore=-1,this.addData(e,i))},completeByTotal:function(e,t){var i=this,a=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];if("undefined"==t)this.customNoMore=-1;else{var o=this._checkDataType(e,a,!1);if(e=o.data,a=o.success,t>=0&&a)return new Promise((function(o,n){i.$nextTick((function(){var r=!1,l=i.pageNo==i.defaultPageNo?0:i.realTotalData.length,s=i.privateConcat?e.length:0,d=l+s-t;d>=0&&(r=!0,d=i.defaultPageSize-d,i.privateConcat&&d>0&&d<e.length&&(e=e.splice(0,d))),i.completeByNoMore(e,r,a).then((function(e){return o(e)})).catch((function(){return n()}))}))}))}return this.addData(e,a)},completeByNoMore:function(e,t){var i=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];return"undefined"!=t&&(this.customNoMore=1==t?1:0),this.addData(e,i)},completeByError:function(e){return this.customerEmptyViewErrorText=e,this.complete(!1)},addData:function(e){var t=this,i=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];this.fromCompleteEmit||(this.disabledCompleteEmit=!0,this.fromCompleteEmit=!1);var a=r.default.getTime(),o=a-this.requestTimeStamp,n=this.minDelay;this.isFirstPage&&this.finalShowRefresherWhenReload&&(n=Math.max(400,n));var l=this.requestTimeStamp>0&&o<n?n-o:0;return this.$nextTick((function(){r.default.delay((function(){t._addData(e,i,!1)}),t.delay>0?t.delay:l)})),new Promise((function(e,i){t.dataPromiseResultMap.complete={resolve:e,reject:i}}))},addDataFromTop:function(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],i=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];(this.finalUseVirtualList?this.addDataFromTopBufferedInsert:this._addDataFromTop)(e,t,i)},resetTotalData:function(e){this.isTotalChangeFromAddData=!0,e="[object Array]"!==Object.prototype.toString.call(e)?[e]:e,this.totalData=e},setLocalPaging:function(e){var t=this,i=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];return this.isLocalPaging=!0,this.$nextTick((function(){t._addData(e,i,!0)})),new Promise((function(e,i){t.dataPromiseResultMap.localPaging={resolve:e,reject:i}}))},reload:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.showRefresherWhenReload;return t&&(this.privateShowRefresherWhenReload=t,this.isUserPullDown=!0),this.showLoadingMoreWhenReload||(this.listRendering=!0),this.$nextTick((function(){e._preReload(t,!1)})),new Promise((function(t,i){e.dataPromiseResultMap.reload={resolve:t,reject:i}}))},refresh:function(){return this._handleRefreshWithDisPageNo(this.pageNo-this.defaultPageNo+1)},refreshToPage:function(e){return this.isHandlingRefreshToPage=!0,this._handleRefreshWithDisPageNo(e+this.defaultPageNo-1)},updateCache:function(){this.finalUseCache&&this.totalData.length&&this._saveLocalCache(this.totalData.slice(0,Math.min(this.totalData.length,this.pageSize)))},clean:function(){this._reload(!0),this._addData([],!0,!1)},clear:function(){this.clean()},_preReload:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.showRefresherWhenReload,i=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,o=this.finalRefresherEnabled&&this.useCustomRefresher;-1===this.customRefresherHeight&&o?r.default.delay((function(){a++,a%10===0&&e._updateCustomRefresherHeight(),e._preReload(t,i,a)}),l.default.delayTime/2):(this.isUserReload=!0,this.loadingType=s.default.LoadingType.Refresher,t?(this.privateShowRefresherWhenReload=t,this.useCustomRefresher?this._doRefresherRefreshAnimate():this.refresherTriggered=!0):this._refresherEnd(!1,!1,!1,!1),this._reload(!1,i))},_reload:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],i=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(this.isAddedData=!1,this.insideOfPaging=-1,this.cacheScrollNodeHeight=-1,this.pageNo=this.defaultPageNo,this._cleanRefresherEndTimeout(),!this.privateShowRefresherWhenReload&&!e&&this._startLoading(!0),this.firstPageLoaded=!0,this.isTotalChangeFromAddData=!1,this.isSettingCacheList||(this.totalData=[]),!e){this._emitQuery(this.pageNo,this.defaultPageSize,i?s.default.QueryFrom.UserPullDown:s.default.QueryFrom.Reload);var a=0;if(r.default.delay(this._callMyParentQuery,a),!t&&this.autoScrollToTopWhenReload){var o=!0;o&&this._scrollToTop(!1)}}},_addData:function(e,t,i){var a=this;this.isAddedData=!0,this.fromEmptyViewReload=!1,this.isTotalChangeFromAddData=!0,this.refresherTriggered=!1,this._endSystemLoadingAndRefresh();var o=this.isUserPullDown;this.showRefresherUpdateTime&&this.isFirstPage&&(r.default.setRefesrherTime(r.default.getTime(),this.refresherUpdateTimeKey),this.$refs.refresh&&this.$refs.refresh.updateTime()),!i&&o&&this.isFirstPage&&(this.isUserPullDown=!1),this.listRendering=!0,this.$nextTick((function(){r.default.delay((function(){return a.listRendering=!1}))}));var n=this._checkDataType(e,t,i);e=n.data,t=n.success;var d=l.default.delayTime;if(this.useChatRecordMode&&(d=0),this.loadingForNow=!1,r.default.delay((function(){a.pagingLoaded=!0,a.$nextTick((function(){!i&&a._refresherEnd(d>0,!0,o)}))})),this.isFirstPage&&(this.isLoadFailed=!t,this.$emit("isLoadFailedChange",this.isLoadFailed),this.finalUseCache&&t&&(this.cacheMode===s.default.CacheMode.Always||this.isSettingCacheList)&&this._saveLocalCache(e)),this.isSettingCacheList=!1,t){if((!1!==this.privateConcat||this.isHandlingRefreshToPage||this.loadingStatus!==s.default.More.NoMore)&&(this.loadingStatus=s.default.More.Default),i){this.totalLocalPagingList=e;var h=this.defaultPageNo,u=this.queryFrom!==s.default.QueryFrom.Refresh?this.defaultPageSize:this.currentRefreshPageSize;this._localPagingQueryList(h,u,0,(function(e){r.default.delay((function(){a.completeByTotal(e,a.totalLocalPagingList.length)}),0)}))}else{r.default.delay((function(){a._currentDataChange(e,a.currentData),a._callDataPromise(!0,a.totalData)}),0)}this.isHandlingRefreshToPage&&(this.isHandlingRefreshToPage=!1,this.pageNo=this.defaultPageNo+Math.ceil(e.length/this.pageSize)-1,e.length%this.pageSize!==0&&(this.customNoMore=1))}else this._currentDataChange(e,this.currentData),this._callDataPromise(!1),this.loadingStatus=s.default.More.Fail,this.isHandlingRefreshToPage=!1,this.loadingType===s.default.LoadingType.LoadMore&&this.pageNo--},_totalDataChange:function(e,t){var i=this,a=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];(this.isUserReload&&this.autoCleanListWhenReload||!this.firstPageLoaded||e.length||!t.length)&&(this._doCheckScrollViewShouldFullHeight(e),this.realTotalData.length||e.length||(a=!1),this.realTotalData=e,a&&(this.$emit("input",e),this.$emit("update:list",e),this.$emit("listChange",e),this._callMyParentList(e)),this.firstPageLoaded=!1,this.isTotalChangeFromAddData=!1,this.$nextTick((function(){r.default.delay((function(){i._getNodeClientRect(".zp-paging-container-content").then((function(e){e&&i.$emit("contentHeightChanged",e[0].height)}))}),l.default.delayTime*(i.isIos?1:3))})))},_currentDataChange:function(e,t){if(e=(0,n.default)(e),this.finalUseVirtualList&&this._setCellIndex(e,"bottom"),this.isFirstPage&&this.finalConcat&&(this.totalData=[]),-1!==this.customNoMore?(1===this.customNoMore||0!==this.customNoMore&&!e.length)&&(this.loadingStatus=s.default.More.NoMore):(!e.length||e.length&&e.length<this.defaultPageSize)&&(this.loadingStatus=s.default.More.NoMore),this.totalData.length)if(this.finalConcat){this.oldScrollTop;this.totalData=[].concat((0,n.default)(this.totalData),(0,n.default)(e))}else this.totalData=e;else this.totalData=e;this.privateConcat=!0},_handleRefreshWithDisPageNo:function(e){var t=this;if(!this.isHandlingRefreshToPage&&!this.realTotalData.length)return this.reload();if(e>=1){this.loading=!0,this.privateConcat=!1;var i=e*this.pageSize;this.currentRefreshPageSize=i,this.isLocalPaging&&this.isHandlingRefreshToPage?this._localPagingQueryList(this.defaultPageNo,i,0,(function(e){t.complete(e)})):(this._emitQuery(this.defaultPageNo,i,s.default.QueryFrom.Refresh),this._callMyParentQuery(this.defaultPageNo,i))}return new Promise((function(e,i){t.dataPromiseResultMap.reload={resolve:e,reject:i}}))},_localPagingQueryList:function(e,t,i,a){e=Math.max(1,e),t=Math.max(1,t);var o=(0,n.default)(this.totalLocalPagingList),l=(e-1)*t,s=Math.min(o.length,l+t),d=o.splice(l,s-l);r.default.delay((function(){return a(d)}),i)},_addDataFromTop:function(e){var t=this,i=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],a=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],o=!this.isChatRecordModeAndNotInversion;e="[object Array]"!==Object.prototype.toString.call(e)?[e]:o?e.reverse():e,this.finalUseVirtualList&&this._setCellIndex(e,"top"),this.totalData=o?[].concat((0,n.default)(e),(0,n.default)(this.totalData)):[].concat((0,n.default)(this.totalData),(0,n.default)(e)),i&&r.default.delay((function(){return t.useChatRecordMode?t.scrollToBottom(a):t.scrollToTop(a)}))},_saveLocalCache:function(e){uni.setStorageSync(this.finalCacheKey,e)},_setListByLocalCache:function(){this.totalData=uni.getStorageSync(this.finalCacheKey)||[],this.isSettingCacheList=!0},_callMyParentList:function(e){if(this.autowireListName.length){var t=r.default.getParent(this.$parent);t&&t[this.autowireListName]&&(t[this.autowireListName]=e)}},_callMyParentQuery:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;if(this.autowireQueryName){if(-1===this.myParentQuery){var i=r.default.getParent(this.$parent);i&&i[this.autowireQueryName]&&(this.myParentQuery=i[this.autowireQueryName])}-1!==this.myParentQuery&&(t>0?this.myParentQuery(e,t):this.myParentQuery(this.pageNo,this.defaultPageSize))}},_emitQuery:function(e,t,i){var a=this;this.queryFrom=i,this.requestTimeStamp=r.default.getTime();var l=this.realTotalData.slice(-1),s=(0,o.default)(l,1),h=s[0];if(this.fetch){var u=d.default._handleFetchParams({pageNo:e,pageSize:t,from:i,lastItem:h||null},this.fetchParams),c=this.fetch(u);d.default._handleFetchResult(c,this,u)||(r.default.isPromise(c)?c.then((function(e){a.complete(e)})).catch((function(e){a.complete(!1)})):this.complete(c))}else this.$emit.apply(this,["query"].concat((0,n.default)(d.default._handleQuery(e,t,i,h||null))))},_callDataPromise:function(e,t){for(var i in this.dataPromiseResultMap){var a=this.dataPromiseResultMap[i];a&&(e?a.resolve({totalList:t,noMore:this.loadingStatus===s.default.More.NoMore}):this.callNetworkReject&&a.reject("z-paging-".concat(i,"-error")))}},_checkDataType:function(e,t,i){var a=Object.prototype.toString.call(e);return"[object Boolean]"===a?(t=e,e=[]):"[object Array]"!==a&&(e=[],"[object Undefined]"!==a&&"[object Null]"!==a&&r.default.consoleErr("".concat(i?"setLocalPaging":"complete","参数类型不正确，第一个参数类型必须为Array!"))),{data:e,success:t}}}};t.default=h},e583:function(e,t,i){"use strict";i("6a54");var a=i("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,i("64aa"),i("c223");var o=a(i("3918")),n={props:{autoShowBackToTop:{type:Boolean,default:o.default.gc("autoShowBackToTop",!1)},backToTopThreshold:{type:[Number,String],default:o.default.gc("backToTopThreshold","400rpx")},backToTopImg:{type:String,default:o.default.gc("backToTopImg","")},backToTopWithAnimate:{type:Boolean,default:o.default.gc("backToTopWithAnimate",!0)},backToTopBottom:{type:[Number,String],default:o.default.gc("backToTopBottom","160rpx")},backToTopStyle:{type:Object,default:o.default.gc("backToTopStyle",{})},enableBackToTop:{type:Boolean,default:o.default.gc("enableBackToTop",!0)}},data:function(){return{backToTopClass:"zp-back-to-top zp-back-to-top-hide",lastBackToTopShowTime:0,showBackToTopClass:!1}},computed:{backToTopThresholdUnitConverted:function(){return o.default.addUnit(this.backToTopThreshold,this.unit)},backToTopBottomUnitConverted:function(){return o.default.addUnit(this.backToTopBottom,this.unit)},finalEnableBackToTop:function(){return!this.usePageScroll&&this.enableBackToTop},finalBackToTopThreshold:function(){return o.default.convertToPx(this.backToTopThresholdUnitConverted)},finalBackToTopStyle:function(){var e=this.backToTopStyle;return e.bottom||(e.bottom=this.windowBottom+o.default.convertToPx(this.backToTopBottomUnitConverted)+"px"),e.position||(e.position=this.usePageScroll?"fixed":"absolute"),e},finalBackToTopClass:function(){return"".concat(this.backToTopClass," zp-back-to-top-").concat(this.unit)}},methods:{_backToTopClick:function(){var e=this,t=!1;this.$emit("backToTopClick",(function(i){(void 0===i||!0===i)&&e._handleToTop(),t=!0})),this.$nextTick((function(){!t&&e._handleToTop()}))},_handleToTop:function(){!this.backToTopWithAnimate&&this._checkShouldShowBackToTop(0),this.useChatRecordMode?this.scrollToBottom(this.backToTopWithAnimate):this.scrollToTop(this.backToTopWithAnimate)},_checkShouldShowBackToTop:function(e){var t=this;this.autoShowBackToTop?e>this.finalBackToTopThreshold?this.showBackToTopClass||(this.showBackToTopClass=!0,this.lastBackToTopShowTime=(new Date).getTime(),o.default.delay((function(){t.backToTopClass="zp-back-to-top zp-back-to-top-show"}),300)):this.showBackToTopClass&&(this.backToTopClass="zp-back-to-top zp-back-to-top-hide",o.default.delay((function(){t.showBackToTopClass=!1}),(new Date).getTime()-this.lastBackToTopShowTime<500?0:300)):this.showBackToTopClass=!1}}};t.default=n},e587:function(e,t,i){var a=i("2f2a");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[e.i,a,""]]),a.locals&&(e.exports=a.locals);var o=i("967d").default;o("b5a163e0",a,!0,{sourceMap:!1,shadowMode:!1})},e684:function(e,t,i){"use strict";i.r(t);var a=i("7567"),o=i.n(a);for(var n in a)["default"].indexOf(n)<0&&function(e){i.d(t,e,(function(){return a[e]}))}(n);t["default"]=o.a},e6af:function(e,t,i){var a=i("c86c");t=a(!1),t.push([e.i,"/* [z-paging]公共css*/.z-paging-content[data-v-c22f146a]{position:relative;flex-direction:column;\noverflow:hidden\n}.z-paging-content-full[data-v-c22f146a]{\ndisplay:flex;width:100%;height:100%\n}.z-paging-content-fixed[data-v-c22f146a], .zp-loading-fixed[data-v-c22f146a]{position:fixed;\nheight:auto;width:auto;\ntop:0;left:0;bottom:0;right:0}.zp-f2-content[data-v-c22f146a]{width:100%;position:fixed;top:0;left:0;background-color:#fff}.zp-page-top[data-v-c22f146a], .zp-page-bottom[data-v-c22f146a]{\nwidth:auto;\nposition:fixed;left:0;right:0;z-index:999}.zp-page-left[data-v-c22f146a], .zp-page-right[data-v-c22f146a]{\nheight:100%\n}.zp-scroll-view-super[data-v-c22f146a]{flex:1;overflow:hidden;position:relative}.zp-view-super[data-v-c22f146a]{\ndisplay:flex;\nflex-direction:row}.zp-scroll-view-container[data-v-c22f146a], .zp-scroll-view[data-v-c22f146a]{position:relative;\nheight:100%;width:100%\n}.zp-absoulte[data-v-c22f146a]{\nposition:absolute;top:0;width:auto\n}.zp-scroll-view-absolute[data-v-c22f146a]{position:absolute;top:0;left:0}\n.zp-scroll-view-hide-scrollbar[data-v-c22f146a] ::-webkit-scrollbar{display:none;-webkit-appearance:none;width:0!important;height:0!important;background:transparent}\n.zp-paging-touch-view[data-v-c22f146a]{width:100%;height:100%;position:relative}.zp-fixed-bac-view[data-v-c22f146a]{position:absolute;width:100%;top:0;left:0;height:200px}.zp-paging-main[data-v-c22f146a]{height:100%;\ndisplay:flex;\nflex-direction:column}.zp-paging-container[data-v-c22f146a]{flex:1;position:relative;\ndisplay:flex;\nflex-direction:column}.zp-chat-record-loading-custom-image[data-v-c22f146a]{width:%?35?%;height:%?35?%;\n-webkit-animation:loading-flower-data-v-c22f146a 1s linear infinite;animation:loading-flower-data-v-c22f146a 1s linear infinite\n}.zp-page-bottom-keyboard-placeholder-animate[data-v-c22f146a]{transition-property:height;transition-duration:.15s;\nwill-change:height\n}.zp-custom-refresher-container[data-v-c22f146a]{overflow:hidden}.zp-custom-refresher-refresh[data-v-c22f146a]{\ndisplay:block\n}.zp-back-to-top[data-v-c22f146a]{z-index:999;position:absolute;bottom:%?0?%;transition-duration:.3s;transition-property:opacity}.zp-back-to-top-rpx[data-v-c22f146a]{width:%?76?%;height:%?76?%;bottom:%?0?%;right:%?25?%}.zp-back-to-top-px[data-v-c22f146a]{width:38px;height:38px;bottom:0;right:13px}.zp-back-to-top-show[data-v-c22f146a]{opacity:1}.zp-back-to-top-hide[data-v-c22f146a]{opacity:0}.zp-back-to-top-img[data-v-c22f146a]{\nwidth:100%;height:100%;\n\n\nz-index:999}.zp-back-to-top-img-inversion[data-v-c22f146a]{-webkit-transform:rotate(180deg);transform:rotate(180deg)}.zp-empty-view[data-v-c22f146a]{\n\nflex:1}.zp-empty-view-center[data-v-c22f146a]{\ndisplay:flex;\nflex-direction:column;align-items:center;justify-content:center}.zp-loading-fixed[data-v-c22f146a]{z-index:9999}.zp-safe-area-inset-bottom[data-v-c22f146a]{position:absolute;\nheight:env(safe-area-inset-bottom)\n}.zp-n-refresh-container[data-v-c22f146a]{\ndisplay:flex;\njustify-content:center;width:%?750?%}.zp-n-list-container[data-v-c22f146a]{\ndisplay:flex;\nflex-direction:row;flex:1}\n\n/* [z-paging]公用的静态css资源 */.zp-line-loading-image[data-v-c22f146a]{\n-webkit-animation:loading-flower-data-v-c22f146a 1s steps(12) infinite;animation:loading-flower-data-v-c22f146a 1s steps(12) infinite;\ncolor:#666}.zp-line-loading-image-rpx[data-v-c22f146a]{margin-right:%?8?%;width:%?34?%;height:%?34?%}.zp-line-loading-image-px[data-v-c22f146a]{margin-right:4px;width:17px;height:17px}.zp-loading-image-ios-rpx[data-v-c22f146a]{width:%?40?%;height:%?40?%}.zp-loading-image-ios-px[data-v-c22f146a]{width:20px;height:20px}.zp-loading-image-android-rpx[data-v-c22f146a]{width:%?34?%;height:%?34?%}.zp-loading-image-android-px[data-v-c22f146a]{width:17px;height:17px}\n@-webkit-keyframes loading-flower-data-v-c22f146a{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}@keyframes loading-flower-data-v-c22f146a{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}\n\n",""]),e.exports=t},f0cf:function(e,t,i){"use strict";i.r(t);var a=i("a06d"),o=i("7322");for(var n in o)["default"].indexOf(n)<0&&function(e){i.d(t,e,(function(){return o[e]}))}(n);var r=i("386c");for(var n in r)["default"].indexOf(n)<0&&function(e){i.d(t,e,(function(){return r[e]}))}(n);i("bbf1");var l=i("828b"),s=i("4915");o["default"].__module="pagingRenderjs";var d=Object(l["a"])(r["default"],a["b"],a["c"],!1,null,"c22f146a",null,!1,a["a"],o["default"]);"function"===typeof s["a"]&&Object(s["a"])(d),t["default"]=d.exports},f422:function(e,t,i){"use strict";i.d(t,"b",(function(){return o})),i.d(t,"c",(function(){return n})),i.d(t,"a",(function(){return a}));var a={zPaging:i("f0cf").default},o=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("z-paging",{ref:"paging",on:{query:function(t){arguments[0]=t=e.$handleEvent(t),e.sessionlist.apply(void 0,arguments)}},model:{value:e.dataList,callback:function(t){e.dataList=t},expression:"dataList"}},[i("v-uni-view",{staticClass:"padding-about-20"},[i("v-uni-view",{staticClass:"view margin-top-20"},[i("v-uni-view",{staticClass:"display-flex flex-direction-column center align-items",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.handleMessage.apply(void 0,arguments)}}},[i("v-uni-image",{staticStyle:{width:"80rpx",height:"80rpx"},attrs:{src:"/static/logo.png"}}),i("v-uni-view",{staticClass:"font-size-26 color-333 "},[e._v("权威验证")])],1)],1)],1),i("v-uni-view",{staticClass:"padding-about-20"},e._l(e.dataList,(function(t,a){return i("v-uni-view",{key:a,staticClass:"display-flex align-items",staticStyle:{padding:"20rpx 0","border-bottom":"1rpx solid #F5F5F5"},on:{click:function(i){arguments[0]=i=e.$handleEvent(i),e.gotochat(t.user_id)}}},[i("v-uni-view",{staticClass:"position-relative",staticStyle:{width:"100rpx",height:"100rpx"}},[i("v-uni-image",{staticClass:"border-radius-12",staticStyle:{width:"100rpx",height:"100rpx"},attrs:{src:t.avatar,mode:""}}),t.is_read?e._e():i("v-uni-view",{staticClass:"position-absolute transform-translate-center",staticStyle:{top:"4%",right:"-15%"}},[i("v-uni-view",{staticClass:"border-radius",staticStyle:{border:"solid #f56c6c 10rpx"}})],1)],1),i("v-uni-view",{staticClass:" file-1 margin-left-20"},[i("v-uni-view",{staticClass:"display-flex space-between align-items"},[i("v-uni-view",{staticClass:"font-size-28 font-weight-bold"},[e._v(e._s(t.nickname))]),i("v-uni-view",{staticClass:"font-size-22 color-999"},[e._v(e._s(t.create_time))])],1),i("v-uni-view",{staticClass:"font-size-26 webkit-line-clamp margin-top-10",staticStyle:{"-webkit-line-clamp":"1"}},[e._v(e._s(t.msg))])],1)],1)})),1)],1)},n=[]},faf4:function(e,t,i){"use strict";i.r(t);var a=i("0d8a"),o=i.n(a);for(var n in a)["default"].indexOf(n)<0&&function(e){i.d(t,e,(function(){return a[e]}))}(n);t["default"]=o.a}}]);