{"scope": "alipay", "name": "easysdk-marketing-openlife", "version": "0.0.1", "main": "./main.tea", "java": {"package": "com.alipay.easysdk.marketing.openlife", "baseClient": "com.alipay.easysdk.kernel.BaseClient"}, "csharp": {"namespace": "Alipay.EasySDK.Marketing.OpenLife", "baseClient": "Alipay.EasySDK.Kernel:BaseClient"}, "typescript": {"baseClient": "@alipay/easysdk-baseclient"}, "php": {"package": "Alipay.EasySDK.Marketing.OpenLife"}, "go": {"namespace": "marketing/openlife"}, "libraries": {"EasySDKKernel": "alipay:easysdk-kernel:*"}}