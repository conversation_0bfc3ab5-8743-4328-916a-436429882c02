{layout name="layout2" /}
<style>
    .layui-form-label{
        width: 120px;
    }
    .reqRed::before {
        content: '*';
        color: red;
        margin-right: 5px;
    }
</style>
<div class="layui-form" lay-filter="process_refund" id="layuiadmin-form-process-refund" style="padding: 20px 30px 0 0;">
    <input type="hidden" value="{$deposit.id}" name="deposit_id">
    <div class="layui-fluid">
        <div class="layui-card">
            <div class="layui-card-header">处理退款申请</div>
            <div class="layui-card-body">
                <div class="layui-form-item">
                    <label class="layui-form-label">商家名称：</label>
                    <div class="layui-input-inline">
                        <label class="layui-form-mid">{$deposit.shop_name}</label>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">当前保证金余额：</label>
                    <div class="layui-input-inline">
                        <label class="layui-form-mid">{$deposit.current_amount}</label>
                    </div>
                    <label class="layui-form-mid">元</label>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">申请原因：</label>
                    <div class="layui-input-block">
                        <div class="layui-form-mid">{$deposit.refund_reason}</div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">申请时间：</label>
                    <div class="layui-input-block">
                        <div class="layui-form-mid">{$deposit.refund_apply_time}</div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label reqRed">处理结果：</label>
                    <div class="layui-input-block">
                        <input type="radio" name="status" value="2" title="同意退款" lay-filter="refund_status" checked>
                        <input type="radio" name="status" value="3" title="拒绝退款" lay-filter="refund_status">
                    </div>
                </div>
                <div class="layui-form-item" id="remark-container">
                    <label class="layui-form-label"><span class="required-mark" style="color:red;display:none;">*</span>备注说明：</label>
                    <div class="layui-input-block">
                        <textarea type="text" name="remark" lay-verify="refund_remark" lay-vertype="tips" placeholder="请输入备注说明" autocomplete="off" class="layui-textarea" style="width: 50%;"></textarea>
                        <div class="layui-form-mid layui-word-aux" style="margin-top: 5px;">拒绝退款时必填，不超过100字</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="layui-form-item layui-hide">
        <input type="button" lay-submit lay-filter="process_refund_submit" id="process_refund_submit" value="确认">
    </div>
</div>
<script>
    layui.config({
        version:"{$front_version}",
        base: '/static/lib/' //静态资源所在路径
    }).use(['form'], function(){
        var $ = layui.$, form = layui.form;
        
        // 监听退款状态变化
        form.on('radio(refund_status)', function(data){
            if(data.value == '3') { // 拒绝退款
                $('.required-mark').show();
            } else {
                $('.required-mark').hide();
            }
        });
        
        // 表单验证
        form.verify({
            refund_remark: function(value) {
                var status = $('input[name="status"]:checked').val();
                if (status == '3' && !value) {
                    return '拒绝退款时必须填写备注说明';
                }
                if (value.length > 100) {
                    return '备注说明不能超过100字';
                }
            }
        });
    })
</script>
