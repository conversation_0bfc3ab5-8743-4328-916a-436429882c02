<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Ccc\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * ivr 按键信息
 *
 * @method string getKey() 获取按键
注意：此字段可能返回 null，表示取不到有效值。
 * @method void setKey(string $Key) 设置按键
注意：此字段可能返回 null，表示取不到有效值。
 * @method string getLabel() 获取按键关联的标签
注意：此字段可能返回 null，表示取不到有效值。
 * @method void setLabel(string $Label) 设置按键关联的标签
注意：此字段可能返回 null，表示取不到有效值。
 */
class IVRKeyPressedElement extends AbstractModel
{
    /**
     * @var string 按键
注意：此字段可能返回 null，表示取不到有效值。
     */
    public $Key;

    /**
     * @var string 按键关联的标签
注意：此字段可能返回 null，表示取不到有效值。
     */
    public $Label;

    /**
     * @param string $Key 按键
注意：此字段可能返回 null，表示取不到有效值。
     * @param string $Label 按键关联的标签
注意：此字段可能返回 null，表示取不到有效值。
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("Key",$param) and $param["Key"] !== null) {
            $this->Key = $param["Key"];
        }

        if (array_key_exists("Label",$param) and $param["Label"] !== null) {
            $this->Label = $param["Label"];
        }
    }
}
