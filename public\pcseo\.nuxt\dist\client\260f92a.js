(window.webpackJsonp=window.webpackJsonp||[]).push([[9],{437:function(t,e,n){"use strict";var r=n(17),o=n(2),f=n(3),c=n(136),m=n(27),h=n(18),l=n(271),d=n(52),v=n(135),N=n(270),T=n(5),I=n(98).f,S=n(44).f,E=n(26).f,_=n(438),y=n(439).trim,x="Number",w=o.Number,M=w.prototype,O=o.TypeError,R=f("".slice),A=f("".charCodeAt),F=function(t){var e=N(t,"number");return"bigint"==typeof e?e:k(e)},k=function(t){var e,n,r,o,f,c,m,code,h=N(t,"number");if(v(h))throw O("Cannot convert a Symbol value to a number");if("string"==typeof h&&h.length>2)if(h=y(h),43===(e=A(h,0))||45===e){if(88===(n=A(h,2))||120===n)return NaN}else if(48===e){switch(A(h,1)){case 66:case 98:r=2,o=49;break;case 79:case 111:r=8,o=55;break;default:return+h}for(c=(f=R(h,2)).length,m=0;m<c;m++)if((code=A(f,m))<48||code>o)return NaN;return parseInt(f,r)}return+h};if(c(x,!w(" 0o1")||!w("0b1")||w("+0x1"))){for(var V,$=function(t){var e=arguments.length<1?0:w(F(t)),n=this;return d(M,n)&&T((function(){_(n)}))?l(Object(e),n,$):e},j=r?I(w):"MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,EPSILON,MAX_SAFE_INTEGER,MIN_SAFE_INTEGER,isFinite,isInteger,isNaN,isSafeInteger,parseFloat,parseInt,fromString,range".split(","),G=0;j.length>G;G++)h(w,V=j[G])&&!h($,V)&&E($,V,S(w,V));$.prototype=M,M.constructor=$,m(o,x,$)}},438:function(t,e,n){var r=n(3);t.exports=r(1..valueOf)},439:function(t,e,n){var r=n(3),o=n(33),f=n(16),c=n(440),m=r("".replace),h="["+c+"]",l=RegExp("^"+h+h+"*"),d=RegExp(h+h+"*$"),v=function(t){return function(e){var n=f(o(e));return 1&t&&(n=m(n,l,"")),2&t&&(n=m(n,d,"")),n}};t.exports={start:v(1),end:v(2),trim:v(3)}},440:function(t,e){t.exports="\t\n\v\f\r                　\u2028\u2029\ufeff"},449:function(t,e,n){"use strict";n.r(e);n(437),n(81),n(61),n(24),n(100),n(80),n(99);var r=6e4,o=36e5,f=24*o;function c(t){return(0+t.toString()).slice(-2)}var m={components:{},props:{isSlot:{type:Boolean,default:!1},time:{type:Number,default:0},format:{type:String,default:"hh:mm:ss"},autoStart:{type:Boolean,default:!0}},watch:{time:{immediate:!0,handler:function(t){t&&this.reset()}}},data:function(){return{timeObj:{},formateTime:0}},created:function(){},computed:{},methods:{createTimer:function(t){return setTimeout(t,100)},isSameSecond:function(t,e){return Math.floor(t)===Math.floor(e)},start:function(){this.counting||(this.counting=!0,this.endTime=Date.now()+1e3*this.remain,this.setTimer())},setTimer:function(){var t=this;this.tid=this.createTimer((function(){var e=t.getRemain();t.isSameSecond(e,t.remain)&&0!==e||t.setRemain(e),0!==t.remain&&t.setTimer()}))},getRemain:function(){return Math.max(this.endTime-Date.now(),0)},pause:function(){this.counting=!1,clearTimeout(this.tid)},reset:function(){this.pause(),this.remain=this.time,this.setRemain(this.remain),this.autoStart&&this.start()},setRemain:function(t){var e=this.format;this.remain=t;var time,n=(time=t,{days:Math.floor(time/f),hours:c(Math.floor(time%f/o)),minutes:c(Math.floor(time%o/r)),seconds:c(Math.floor(time%r/1e3))});this.formateTime=function(t,e){var n=e.days,r=e.hours,o=e.minutes,f=e.seconds;return-1!==t.indexOf("dd")&&(t=t.replace("dd",n)),-1!==t.indexOf("hh")&&(t=t.replace("hh",c(r))),-1!==t.indexOf("mm")&&(t=t.replace("mm",c(o))),-1!==t.indexOf("ss")&&(t=t.replace("ss",c(f))),t}(e,n),this.$emit("change",n),0===t&&(this.pause(),this.$emit("finish"))}}},h=n(9),component=Object(h.a)(m,(function(){var t=this,e=t.$createElement,n=t._self._c||e;return t.time>=0?n("div",[n("client-only",[t.isSlot?t._t("default"):n("span",[t._v(t._s(t.formateTime))])],2)],1):t._e()}),[],!1,null,null,null);e.default=component.exports}}]);