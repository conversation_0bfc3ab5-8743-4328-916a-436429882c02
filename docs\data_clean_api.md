# 数据清理API接口文档

## 概述

数据清理API提供了清理数据库中垃圾数据的功能，包括已注销用户数据和孤立的商品数据。

## 接口列表

### 1. 获取垃圾数据统计信息

**接口地址：** `GET /shopapi/data_clean/stats`

**功能描述：** 获取系统中垃圾数据的统计信息

**请求参数：** 无

**返回示例：**
```json
{
    "code": 1,
    "msg": "获取统计信息成功",
    "data": {
        "deleted_users": 5,
        "deleted_goods": 12,
        "orphaned_goods_data": {
            "goods_item": 23,
            "goods_spec": 15,
            "goods_spec_value": 45,
            "goods_image": 8,
            "goods_comment": 3,
            "goods_collect": 7,
            "goods_click": 156,
            "goods_footprint": 89
        }
    }
}
```

### 2. 清理已注销用户数据

**接口地址：** `POST /shopapi/data_clean/clean_deleted_users`

**功能描述：** 删除已注销的用户及其所有关联数据

**请求参数：** 无

**清理的数据表：**
- `ls_user` - 用户主表
- `ls_user_address` - 用户地址
- `ls_user_auth` - 用户授权信息
- `ls_user_distribution` - 用户分销信息
- `ls_user_reports` - 用户举报记录
- `ls_user_sign` - 用户签到记录
- `ls_user_transfer_records` - 用户转移记录
- `ls_user_activity_log` - 用户活动日志
- `ls_goods_collect` - 商品收藏
- `ls_goods_click` - 商品点击记录
- `ls_goods_footprint` - 商品足迹
- `ls_goods_comment` - 商品评论
- `ls_order` - 订单
- `ls_order_goods` - 订单商品
- `ls_cart` - 购物车
- `ls_recharge_order` - 充值订单
- `ls_user_account_log` - 用户账户日志
- `ls_coupon_list` - 优惠券
- `ls_distribution_order` - 分销订单
- `ls_after_sale` - 售后记录

**返回示例：**
```json
{
    "code": 1,
    "msg": "清理已注销用户数据完成",
    "data": {
        "deleted_users": 5,
        "total_deleted_records": 234,
        "cleaned_tables": {
            "user_address": 8,
            "user_auth": 5,
            "goods_collect": 12,
            "goods_comment": 3,
            "order": 15,
            "user": 5
        }
    }
}
```

### 3. 清理孤立商品数据

**接口地址：** `POST /shopapi/data_clean/clean_orphaned_goods`

**功能描述：** 清理主表中不存在但附属表中存在的商品数据

**请求参数：** 无

**清理的数据表：**
- `ls_goods` - 已删除的商品主表数据
- `ls_goods_item` - 商品规格
- `ls_goods_spec` - 商品规格项
- `ls_goods_spec_value` - 商品规格值
- `ls_goods_image` - 商品图片
- `ls_goods_comment` - 商品评论
- `ls_goods_comment_image` - 商品评论图片
- `ls_goods_collect` - 商品收藏
- `ls_goods_click` - 商品点击记录
- `ls_goods_footprint` - 商品足迹

**返回示例：**
```json
{
    "code": 1,
    "msg": "清理孤立商品数据完成",
    "data": {
        "total_deleted_records": 156,
        "cleaned_tables": {
            "goods_item": 23,
            "goods_spec": 15,
            "goods_spec_value": 45,
            "goods_image": 8,
            "goods_comment": 3,
            "goods_collect": 7,
            "goods_click": 42,
            "goods_footprint": 13,
            "goods": 12
        }
    }
}
```

### 4. 执行完整清理

**接口地址：** `POST /shopapi/data_clean/clean_all`

**功能描述：** 执行所有清理操作，包括用户数据和商品数据

**请求参数：** 无

**返回示例：**
```json
{
    "code": 1,
    "msg": "数据清理完成",
    "data": {
        "total_deleted_records": 390,
        "details": {
            "user_clean": {
                "code": 1,
                "msg": "清理已注销用户数据完成",
                "data": {
                    "deleted_users": 5,
                    "total_deleted_records": 234,
                    "cleaned_tables": {...}
                }
            },
            "goods_clean": {
                "code": 1,
                "msg": "清理孤立商品数据完成",
                "data": {
                    "total_deleted_records": 156,
                    "cleaned_tables": {...}
                }
            }
        }
    }
}
```

## 安全特性

1. **事务处理：** 所有清理操作都在数据库事务中执行，确保数据一致性
2. **分批处理：** 大量数据采用分批处理，避免长时间锁表
3. **表存在检查：** 清理前检查表是否存在，避免SQL错误
4. **详细日志：** 记录详细的操作日志，便于追踪和审计
5. **错误回滚：** 出现错误时自动回滚事务

## 使用建议

1. **备份数据：** 执行清理前建议备份重要数据
2. **测试环境：** 先在测试环境验证清理效果
3. **低峰期执行：** 建议在系统访问量较低时执行清理操作
4. **监控日志：** 执行后检查系统日志确认操作结果

## 测试页面

访问 `/data_clean_test.html` 可以使用可视化界面测试所有接口功能。

## 错误码说明

- `code: 1` - 操作成功
- `code: 0` - 操作失败，具体错误信息在 `msg` 字段中

## 注意事项

⚠️ **重要警告：** 
- 数据清理操作不可逆，请谨慎使用
- 建议在执行前先调用统计接口了解将要删除的数据量
- 生产环境使用前请充分测试
