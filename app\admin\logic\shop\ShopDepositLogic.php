<?php


namespace app\admin\logic\shop;


use app\common\basics\Logic;
use app\common\enum\CommunityLikeEnum;
use app\common\enum\ShopEnum;
use app\common\model\community\CommunityComment;
use app\common\model\community\CommunityLike;
use app\common\model\shop\Shop;
use app\common\model\shop\ShopAdmin;
use app\common\model\shop\ShopDepositDetails;
use app\common\server\UrlServer;
use Exception;
use think\facade\Db;

class ShopDepositLogic extends Logic
{
    /**
     * NOTE: 商家列表
     * @author: 张无忌
     * @param $get
     * @return array|bool
     */
    public static function lists($get)
    {
        try {
            $where = [
                ['del', '=', 0]
            ];

            if (!empty($get['name']) and $get['name'])
                $where[] = ['name', 'like', '%'.$get['name'].'%'];

            if (!empty($get['type']) and is_numeric($get['type']))
                $where[] = ['type', '=', $get['type']];

            if (!empty($get['cid']) and is_numeric($get['cid']))
                $where[] = ['cid', '=', $get['cid']];

            if (isset($get['is_recommend']) && $get['is_recommend'] != '')
                $where[] = ['is_recommend', '=', $get['is_recommend']];

            if (isset($get['is_run']) && $get['is_run'] != '')
                $where[] = ['is_run', '=', $get['is_run']];

            if (isset($get['is_freeze']) and $get['is_freeze'] != '')
                $where[] = ['is_freeze', '=', $get['is_freeze']];

            if (!empty($get['expire_start_time']) and $get['expire_start_time'])
                $where[] = ['expire_time', '>=', strtotime($get['expire_start_time'])];

            if (!empty($get['expire_end_time']) and $get['expire_end_time'])
                $where[] = ['expire_time', '<=', strtotime($get['expire_end_time'])];

            $condition = 'del=0';
            // 到期状态
            if (isset($get['expire_status']) and $get['expire_status'] != '') {
                if ($get['expire_status']) {
                    // 已到期
                    $where[] = ['expire_time', '<', time()];
                    $where[] = ['expire_time', '>', 0];
                } else {
                    // 未到期
                    $condition = "expire_time=0 OR expire_time >". time();
                }
            }

            $model = new Shop();
            $lists = $model->field(true)
                ->where($where)
                ->whereRaw($condition)
                ->order('id', 'desc')
                ->order('weight', 'asc')
                ->with(['category', 'admin'])
                ->append(['expire_desc'])
                ->paginate([
                    'page'      => $get['page'],
                    'list_rows' => $get['limit'],
                    'var_page' => 'page'
                ])
                ->toArray();

            foreach ($lists['data'] as &$item) {
                $item['category']  = $item['category']['name'] ?? '未知';
                $item['type']      = ShopEnum::getShopTypeDesc($item['type']);
                $item['is_run']    = ShopEnum::getShopIsRunDesc($item['is_run']);
                $item['is_freeze'] = ShopEnum::getShopFreezeDesc($item['is_freeze']);
                $item['is_recommend'] = ShopEnum::getShopIsRecommendDesc($item['is_recommend']);
                $item['account'] = $item['admin']['account'] ?? '';
            }

            return ['count'=>$lists['total'], 'lists'=>$lists['data']];
        } catch (Exception $e) {
            return ['error'=>$e->getMessage()];
        }
    }

    /**
     * NOTE: 商家详细
     * @author: 张无忌
     * @param $id
     * @return array
     */
    public static function detail($id)
    {
        $model = new Shop();
        $detail = $model->findOrEmpty($id)->toArray();
        return $detail;
    }

    public static function getAccountInfo($id)
    {
        $detail = ShopAdmin::field('id,account')->where(['shop_id' => $id, 'root' => 1])->findOrEmpty()->toArray();
        return $detail;
    }
    /**
     * @notes 保证金明细
     * @param $get
     * @return array
     * @throws \think\db\exception\DbException
     * <AUTHOR>
     * @date 2022/5/11 10:14
     */
    public static function getRelationData($get)
    {
        $type = $get['type'] ?? 'comment';
        $model=new ShopDepositDetails();
        if ($type == 'comment') {
            $lists = $model->where([
                    'shop_id' => $get['id']
                ])->where('deposit_change', '<', 0)
                ->paginate([
                    'page' => $get['page'],
                    'list_rows' => $get['limit'],
                    'var_page' => 'page'
                ])
                ->toArray();

        } else {
            $lists = $model->where([
                'shop_id' => $get['id']
            ])->where('deposit_change', '>', 0)
                ->paginate([
                    'page' => $get['page'],
                    'list_rows' => $get['limit'],
                    'var_page' => 'page'
                ])
                ->toArray();
        }

        return ['count' => $lists['total'], 'lists' => $lists['data']];
    }
    /**
     * NOTE: 新增商家
     * @author: 张无忌
     * @param $post
     * @return bool
     */
    public static function add($post)
    {
        Db::startTrans();
        try {
            // 校验配送方式
            self::checkDeliveryType($post);

            // 创建商家
            $shop = Shop::create([
                'cid'               => $post['cid'],
                'type'              => $post['type'],
                'name'              => $post['name'],
                'nickname'          => $post['nickname'],
                'mobile'            => $post['mobile'],
                'logo'              => $post['logo'] ?? '',
                'background'        => $post['background'] ?? '',
                'license'           => $post['license'] ?? '',
                'keywords'          => $post['keywords'] ?? '',
                'intro'             => $post['intro'] ?? '',
                'weight'            => $post['weight'] ?? 0,
                'trade_service_fee' => $post['trade_service_fee'],
                'is_run'            => $post['is_run'],
                'is_freeze'         => $post['is_freeze'],
                'is_product_audit'  => $post['is_product_audit'],
                'is_recommend'      => $post['is_recommend'] ?? 0,
                'expire_time'       => !empty($post['expire_time']) ? strtotime($post['expire_time']) : 0,
                'province_id'    => $post['province_id'] ?? 0,
                'city_id'        => $post['city_id'] ?? 0,
                'district_id'    => $post['district_id'] ?? 0,
                'address'        => $post['address'] ?? '',
                'longitude'      => $post['longitude'] ?? '',
                'latitude'       => $post['latitude'] ?? '',
                'delivery_type'  => $post['delivery_type'] ?? [1]
            ]);
            // 创建账号
            // 新增商家登录账号
            $time = time();
            $salt = substr(md5($time . $post['name']), 0, 4);//随机4位密码盐
            ShopAdmin::create([
                'root' => 1,
                'shop_id' => $shop->id,
                'name' => '超级管理员',
                'account' => $post['account'],
                'password' => generatePassword($post['password'], $salt),
                'salt' => $salt,
                'role_id' => 0,
                'create_time' => $time,
                'update_time' => $time,
                'disable' => 0,
                'del' => 0
            ]);

            Db::commit();
            return true;
        } catch (Exception $e) {
            Db::rollback();
            static::$error = $e->getMessage();
            return false;
        }
    }

    /**
     * NOTE: 编辑商家
     * @author: 张无忌
     * @param $post
     * @return bool
     */
    public static function edit($post)
    {
        try {
            // 校验配送方式
            self::checkDeliveryType($post);

            Shop::update([
                'cid'               => $post['cid'],
                'type'              => $post['type'],
                'name'              => $post['name'],
                'nickname'          => $post['nickname'],
                'mobile'            => $post['mobile'],
                'logo'              => $post['logo'] ?? '',
                'keywords'          => $post['keywords'] ?? '',
                'intro'             => $post['intro'] ?? '',
                'trade_service_fee' => $post['trade_service_fee'],
                'is_run'            => $post['is_run'],
                'is_freeze'         => $post['is_freeze'],
                'is_product_audit'  => $post['is_product_audit'],
                'expire_time'       => !empty($post['expire_time']) ? strtotime($post['expire_time']) : 0,
                'province_id'    => $post['province_id'] ?? 0,
                'city_id'        => $post['city_id'] ?? 0,
                'district_id'    => $post['district_id'] ?? 0,
                'address'        => $post['address'] ?? '',
                'longitude'      => $post['longitude'] ?? '',
                'latitude'       => $post['latitude'] ?? '',
                'delivery_type'  => $post['delivery_type'] ?? [1],
                'business_license'   => empty($post['business_license']) ? '' : UrlServer::setFileUrl($post['business_license']),
                'other_qualifications' => isset($post['other_qualifications']) ? json_encode($post['other_qualifications'], JSON_UNESCAPED_UNICODE) : '',
            ], ['id'=>$post['id']]);

            return true;
        } catch (Exception $e) {
            static::$error = $e->getMessage();
            return false;
        }
    }

    /**
     * NOTE: 设置商家
     * @author: 张无忌
     * @param $post
     * @return bool
     */
    public static function set($post)
    {
        try {
            Shop::update([
                'is_distribution' => $post['is_distribution'] ?? 0,
                'is_recommend' => $post['is_recommend'] ?? 0,
                'is_pay' => $post['is_pay'] ?? 1, //是否开启支付功能,默认开启
                'weight'       => $post['weight']
            ], ['id'=>$post['id']]);

            return true;
        } catch (Exception $e) {
            static::$error = $e->getMessage();
            return false;
        }
    }

    /**
     * NOTE: 更新账号密码
     * @author: 张无忌
     * @param $post
     * @return bool
     */
    public static function account($post)
    {
        Db::startTrans();
        try {
            if(!isset($post['account']) || empty($post['account'])) {
                throw new \think\Exception('账户不能为空');
            }
            $shopAdmin = ShopAdmin::where([
                ['account', '=', trim($post['account'])],
                ['shop_id', '<>', $post['id']]
            ])->findOrEmpty();
            if(!$shopAdmin->isEmpty()) {
                throw new \think\Exception('账户已存在，请更换其他名称重试');
            }

            $shopAdmin = ShopAdmin::where(['shop_id' => $post['id'], 'root' => 1])->findOrEmpty();

            $shopAdminUpdateData = [
                'account'     => $post['account'],
                'update_time' => time()
            ];
            if (!empty($post['password'])) {
                $shopAdminUpdateData['password'] = generatePassword($post['password'], $shopAdmin->salt);
            }
            ShopAdmin::where(['shop_id' => $post['id'], 'root' => 1])->update($shopAdminUpdateData);

            Db::commit();
            return true;
        } catch (Exception $e) {
            Db::rollback();
            static::$error = $e->getMessage();
            return false;
        }
    }


    /**
     * @notes 批量更新商家营业状态或冻结状态
     * @param $ids
     * @param $field
     * @param $value
     * @return Shop|false
     * <AUTHOR>
     * @date 2022/3/17 10:42
     */
    public static function batchOperation($ids, $field, $value)
    {
        try {
            $result = Shop::whereIn('id', $ids)->update([
                $field  => $value,
                'update_time' => time()
            ]);
            return $result;
        } catch (\Exception $e) {
            self::$error = $e->getMessage();
            return false;
        }
    }

    /**
     * 审核保证金
     * @param array $post
     * @return bool
     */
    public static function audit(array $post)
    {
        Db::startTrans();
        try {
            $id = $post['id'];
            $status = $post['status'];
            $remark = $post['remark'] ?? '';

            // 获取保证金信息
            $deposit = Db::name('shop_deposit')->where('id', $id)->find();
            if (!$deposit) {
                throw new \Exception('保证金记录不存在');
            }

            if ($deposit['status'] != 0) {
                throw new \Exception('该保证金已审核，请勿重复操作');
            }

            // 更新保证金状态
            $updateData = [
                'status' => $status,
                'remark' => $remark,
                'updated_at' => date('Y-m-d H:i:s')
            ];

            Db::name('shop_deposit')->where('id', $id)->update($updateData);

            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            self::$error = $e->getMessage();
            return false;
        }
    }

    /**
     * 调整保证金
     * @param array $post
     * @return bool
     */
    public static function adjustDeposit(array $post)
    {
        Db::startTrans();
        try {
            $deposit_id = $post['deposit_id'];
            $shop_id = $post['shop_id'];
            $change_type = $post['change_type']; // 2-增加 3-扣除
            $amount = floatval($post['amount']);
            $reason = $post['reason'];

            // 验证金额
            if ($amount <= 0) {
                throw new \Exception('变动金额必须大于0');
            }

            // 获取保证金信息
            $deposit = Db::name('shop_deposit')->where('id', $deposit_id)->find();
            if (!$deposit) {
                throw new \Exception('保证金记录不存在');
            }

            // 计算当前余额
            $current_balance = self::calculateCurrentBalance($deposit_id);

            // 如果是扣除，检查余额是否足够并设置为负数
            if ($change_type == 3) {
                if ($amount > floatval($current_balance)) {
                    throw new \Exception('保证金余额不足，当前余额：' . $current_balance);
                }

                // 扣除时，金额为负数
                $amount = -$amount;
            }

            // 创建保证金明细记录
            $detail_data = [
                'shop_id' => $shop_id,
                'deposit_id' => $deposit_id,
                'sn' => createSn('shop_deposit_details', 'sn'),
                'deposit_change' => $amount, // 扣除为负，增加为正
                'change_type' => $change_type,
                'amount' => abs($amount), // 保存绝对值
                'reason' => $reason,
                'change_date' => date('Y-m-d'),
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            Db::name('shop_deposit_details')->insert($detail_data);

            // 更新保证金记录的更新时间
            Db::name('shop_deposit')->where('id', $deposit_id)->update([
                'updated_at' => date('Y-m-d H:i:s')
            ]);

            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            self::$error = $e->getMessage();
            return false;
        }
    }

    /**
     * 计算当前保证金余额
     * @param int $deposit_id 保证金ID
     * @return float 当前余额（保留两位小数）
     */
    public static function calculateCurrentBalance($deposit_id)
    {
        // 获取保证金信息
        $deposit = Db::name('shop_deposit')->where('id', $deposit_id)->find();
        if (!$deposit) {
            return '0.00';
        }

        // 获取初始保证金金额
        $total_deposit = floatval($deposit['deposit_amount']);

        // 获取所有变动记录的总和（包括增加和扣除）
        $changes = Db::name('shop_deposit_details')
            ->where('deposit_id', $deposit_id)
            ->sum('deposit_change');

        // 计算当前余额 = 初始金额 + 所有变动（增加为正，扣除为负）
        $current_balance = $total_deposit + floatval($changes);

        // 格式化为保留两位小数
        return number_format($current_balance, 2, '.', '');
    }

    /**
     * 取消集采商家,退集采联盟商家保证金
     * @param array $post
     * @return bool
     */
    public static function refundAndCloseJcShop(array $post)
    {
        Db::startTrans();
        try {
            $deposit_id = $post['deposit_id'];
            $admin_id = $post['admin_id'] ?? 0;

            // 获取保证金信息
            $deposit = Db::name('shop_deposit')->where('id', $deposit_id)->find();
            if (!$deposit) {
                throw new \Exception('保证金记录不存在');
            }

            $shop_id = $deposit['shop_id'];

            // 检查保证金状态
            if ($deposit['status'] != 1) {
                throw new \Exception('保证金审核未通过，无法退款关店');
            }

            // 计算当前余额
            $current_balance = floatval(self::calculateCurrentBalance($deposit_id));
            if ($current_balance <= 0) {
                throw new \Exception('保证金余额为0，无法退款');
            }
            // 5. 更新店铺状态为已关闭
            Db::name('shop')->where('id', $shop_id)->update([
                'jcshop_vip' => 0, // 取消会员资格
                'update_time' => time()
            ]);

            // 6. 创建退款明细记录
            $detail_data = [
                'shop_id' => $shop_id,
                'deposit_id' => $deposit_id,
                'sn' => createSn('shop_deposit_details', 'sn'),
                'deposit_change' => -$current_balance, // 退款为负数
                'change_type' => 4, // 4-退还
                'amount' => $current_balance,
                'reason' => '退集采联盟商家保证金',
                'change_date' => date('Y-m-d'),
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            Db::name('shop_deposit_details')->insert($detail_data);

            // 7. 使用通用退款逻辑处理退款
            $refund_params = [
                'refund_type' => \app\common\logic\CommonRefundLogic::REFUND_TYPE_SHOP_DEPOSIT,
                'source_id' => $deposit_id,
                'shop_id' => $shop_id,
                'user_id' => $deposit['user_id'],
                'refund_amount' => $current_balance,
                'total_amount' => $deposit['deposit_amount'],
                'payment_method' => $deposit['payment_method'],
                'transaction_id' => $deposit['transaction_id'], // 使用订单号作为交易ID
                'admin_id' => $admin_id,
                'remark' => '管理员操作退费关店',
                'order_source' => 1 // 默认来源
            ];

            // 调用通用退款逻辑
            $refund_result = \app\common\logic\CommonRefundLogic::refund($refund_params);
            if (!$refund_result) {
                throw new \Exception(\app\common\logic\CommonRefundLogic::getError() ?: '退款处理失败');
            }

            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            self::$error = $e->getMessage();
            return false;
        }
    }


    /**
     * 商家销户
     * @param array $post
     * @return bool
     */
    public static function refundAndCloseShop(array $post)
    {
        Db::startTrans();
        try {
            $deposit_id = $post['deposit_id'];
            $admin_id = $post['admin_id'] ?? 0;

            // 获取保证金信息
            $deposit = Db::name('shop_deposit')->where('id', $deposit_id)->find();
            if (!$deposit) {
                throw new \Exception('保证金记录不存在');
            }

            $shop_id = $deposit['shop_id'];

            // 检查保证金状态
            if ($deposit['status'] != 1) {
                throw new \Exception('保证金审核未通过，无法退款关店');
            }

            // 计算当前余额
            $current_balance = floatval(self::calculateCurrentBalance($deposit_id));
            if ($current_balance <= 0) {
                throw new \Exception('保证金余额为0，无法退款');
            }

            // 1. 下架店铺所有商品
            Db::name('goods')->where('shop_id', $shop_id)->update([
                'is_show' => 0,
                'update_time' => time()
            ]);

            // 2. 清空user表shop_id关联
            Db::name('user')->where('shop_id', $shop_id)->update([
                'shop_id' => 0,
                'update_time' => time()
            ]);

            // 3. 删除shop_admin表及相关表数据
            Db::name('shop_admin')->where('shop_id', $shop_id)->update([
                'del' => 1,
                'update_time' => time()
            ]);

            // 4. 清除广告位配置
            Db::name('ad')->where('shop_id', $shop_id)->update([
                'status' => 0,
                'update_time' => time()
            ]);

            // 5. 更新店铺状态为已关闭
            Db::name('shop')->where('id', $shop_id)->update([
                'is_run' => 0, // 关闭营业状态
                'is_freeze' => 1, // 冻结状态
                'jcshop_vip' => 0, // 取消会员资格
                'update_time' => time()
            ]);

            // 6. 创建退款明细记录
            $detail_data = [
                'shop_id' => $shop_id,
                'deposit_id' => $deposit_id,
                'sn' => createSn('shop_deposit_details', 'sn'),
                'deposit_change' => -$current_balance, // 退款为负数
                'change_type' => 4, // 4-退还
                'amount' => $current_balance,
                'reason' => '退费关店',
                'change_date' => date('Y-m-d'),
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            Db::name('shop_deposit_details')->insert($detail_data);

            // 7. 使用通用退款逻辑处理退款
            $refund_params = [
                'refund_type' => \app\common\logic\CommonRefundLogic::REFUND_TYPE_SHOP_DEPOSIT,
                'source_id' => $deposit_id,
                'shop_id' => $shop_id,
                'user_id' => $deposit['user_id'],
                'refund_amount' => $current_balance,
                'total_amount' => $deposit['deposit_amount'],
                'payment_method' => $deposit['payment_method'],
                'admin_id' => $admin_id,
                'remark' => '管理员操作退费关店',
                'order_source' => 'oa' // 默认来源
            ];

            // 调用通用退款逻辑
            $refund_result = \app\common\logic\CommonRefundLogic::refund($refund_params);
            if (!$refund_result) {
                throw new \Exception(\app\common\logic\CommonRefundLogic::getError() ?: '退款处理失败');
            }

            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            self::$error = $e->getMessage();
            return false;
        }
    }


    /**
     * @notes 校验配送方式
     * @param $post
     * @return bool
     * @throws \Exception
     * <AUTHOR>
     * @date 2022/11/1 11:30
     */
    public static function checkDeliveryType($post)
    {
        // 校验配送方式
        if (empty($post['delivery_type'])) {
            throw new \Exception('至少选择一种配送方式');
        }

        // 线下自提时，商家地址必填
        if (in_array(ShopEnum::DELIVERY_SELF, $post['delivery_type'])) {
            if (empty($post['province_id']) || empty($post['city_id']) || empty($post['district_id']) || empty($post['address'])) {
                throw new \Exception('线下自提需完善商家地址');
            }
        }
        return true;
    }



}
