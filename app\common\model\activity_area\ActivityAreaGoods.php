<?php

namespace app\common\model\activity_area;

use app\common\basics\Models;

class ActivityAreaGoods extends Models{

    //审核状态
    const AUDIT_STATUS_WAIT = 0;
    const AUDIT_STATUS_PASS = 1;
    const AUDIT_STATUS_REFUSE = 2;

    /**
     * @notes 获取活动专区商品的审核状态
     * @param bool $from
     * @return string|string[]
     * <AUTHOR>
     * @date 2021/7/13 6:37 下午
     */
    public static function getAuditStatus($from = true){
        $desc = [
            self::AUDIT_STATUS_WAIT     => '待审核',
            self::AUDIT_STATUS_PASS     => '审核通过',
            self::AUDIT_STATUS_REFUSE   => '审核拒绝',
        ];
        if(true === $from){
            return $desc;
        }
        return $desc[$from] ?? '';
    }

    /**
     * @notes 审核状态
     * @param $value
     * @param $data
     * @return string|string[]
     * <AUTHOR>
     * @date 2021/7/13 6:37 下午
     */
    public static function getAuditStatusAttr($value, $data){
        return self::getAuditStatus($data['audit_status']);
    }

}