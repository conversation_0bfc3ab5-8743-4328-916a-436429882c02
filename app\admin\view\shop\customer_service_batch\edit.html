{layout name="layout2" /}
<div class="layui-form" lay-filter="layuiadmin-form-admin" id="layuiadmin-form-admin" style="padding: 20px 30px 0 0;">
    <input type="hidden" name="id" value="{$info.id}">
    
    <div class="layui-form-item">
        <label class="layui-form-label">所属商家</label>
        <div class="layui-input-block">
            <div class="layui-form-mid">{$info.shop_name}</div>
        </div>
    </div>
    
    <div class="layui-form-item">
        <label class="layui-form-label">客服昵称</label>
        <div class="layui-input-inline">
            <input type="text" name="nickname" value="{$info.nickname}" lay-verify="required" placeholder="请输入客服昵称" autocomplete="off" class="layui-input">
        </div>
        <div class="layui-form-mid layui-word-aux">客服显示的昵称</div>
    </div>
    
    <div class="layui-form-item">
        <label class="layui-form-label">客服账号</label>
        <div class="layui-input-inline">
            <input type="text" name="account" value="{$info.account}" lay-verify="required" placeholder="请输入客服账号" autocomplete="off" class="layui-input">
        </div>
        <div class="layui-form-mid layui-word-aux">客服登录账号</div>
    </div>
    
    <div class="layui-form-item">
        <label class="layui-form-label">客服头像</label>
        <div class="layui-input-inline">
            <div class="upload-image">
                <input type="hidden" name="avatar" value="{$info.avatar}">
                <div class="upload-image-preview">
                    {if condition="$info.avatar"}
                        <img src="{$info.avatar}" style="width: 100px; height: 100px; border-radius: 50%;" alt="客服头像">
                    {else/}
                        <div style="width: 100px; height: 100px; border: 1px dashed #ccc; display: flex; align-items: center; justify-content: center; border-radius: 50%;">
                            <span>暂无头像</span>
                        </div>
                    {/if}
                </div>
                <button type="button" class="layui-btn layui-btn-sm" id="upload-avatar">上传头像</button>
            </div>
        </div>
        <div class="layui-form-mid layui-word-aux">建议尺寸：100x100像素</div>
    </div>
    
    <div class="layui-form-item">
        <label class="layui-form-label">状态</label>
        <div class="layui-input-block">
            <input type="radio" name="disable" value="0" title="启用" {if condition="$info.disable == 0"}checked{/if}>
            <input type="radio" name="disable" value="1" title="禁用" {if condition="$info.disable == 1"}checked{/if}>
        </div>
    </div>
    
    <div class="layui-form-item">
        <label class="layui-form-label">排序</label>
        <div class="layui-input-inline">
            <input type="number" name="sort" value="{$info.sort|default='0'}" placeholder="请输入排序值" autocomplete="off" class="layui-input">
        </div>
        <div class="layui-form-mid layui-word-aux">数值越小越靠前</div>
    </div>
    
    <div class="layui-form-item">
        <div class="layui-input-block">
            <button class="layui-btn" lay-submit lay-filter="editSubmit" id="editSubmit">确认保存</button>
            <button type="reset" class="layui-btn layui-btn-primary">重置</button>
        </div>
    </div>
</div>

<script>
    layui.config({
        version: "{$front_version}",
        base: '/static/lib/'
    }).use(['form', 'upload'], function () {
        var form = layui.form;
        var upload = layui.upload;
        var $ = layui.jquery;

        // 头像上传
        upload.render({
            elem: '#upload-avatar',
            url: '{:url("admin/file/upload")}',
            accept: 'images',
            size: 2048, // 2MB
            done: function(res){
                if(res.code == 1){
                    $('input[name="avatar"]').val(res.data.url);
                    $('.upload-image-preview').html('<img src="' + res.data.url + '" style="width: 100px; height: 100px; border-radius: 50%;" alt="客服头像">');
                    layer.msg('上传成功', {icon: 1});
                } else {
                    layer.msg(res.msg || '上传失败', {icon: 2});
                }
            },
            error: function(){
                layer.msg('上传失败', {icon: 2});
            }
        });

        // 监听提交
        form.on('submit(editSubmit)', function(data) {
            var field = data.field;

            like.ajax({
                url: '{:url("edit")}',
                data: field,
                type: "post",
                success: function(res) {
                    if (res.code == 1) {
                        layer.msg(res.msg, {icon: 1, time: 1000}, function() {
                            var index = parent.layer.getFrameIndex(window.name);
                            parent.layer.close(index);
                        });
                    } else {
                        layer.msg(res.msg, {icon: 2, time: 2000});
                    }
                }
            });
            return false;
        });
    });
</script>
