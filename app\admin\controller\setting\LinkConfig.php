<?php
namespace app\admin\controller\setting;

use app\common\basics\AdminBase;
use app\common\server\ConfigServer;
use app\common\server\JsonServer;
use app\common\enum\AdEnum;

/**
 * 跳转配置管理
 * Class LinkConfig
 * @package app\admin\controller\setting
 */
class LinkConfig extends AdminBase
{
    /**
     * 跳转配置页面
     * @return \think\response\View
     */
    public function index()
    {
        // 获取当前配置
        $mobile_pages = ConfigServer::get('link_config', 'mobile_pages', []);

        // 如果配置为空，使用默认配置
        if (empty($mobile_pages)) {
            $mobile_pages = $this->getDefaultMobilePages();
        }

        return view('', [
            'mobile_pages' => $mobile_pages
        ]);
    }

    /**
     * 保存跳转配置
     * @return \think\response\Json
     */
    public function save()
    {
        if (!$this->request->isPost()) {
            return JsonServer::error('请求方式错误');
        }

        $post = $this->request->post();

        // 调试信息
        \think\facade\Log::info('跳转配置保存请求: ' . json_encode($post, JSON_UNESCAPED_UNICODE));

        // 验证数据
        if (!isset($post['mobile_pages'])) {
            return JsonServer::error('参数缺失：mobile_pages');
        }

        // 处理移动端页面配置
        $mobile_pages = [];
        if (!empty($post['mobile_pages'])) {
            foreach ($post['mobile_pages'] as $page) {
                if (!empty($page['name']) && !empty($page['path'])) {
                    $mobile_pages[] = [
                        'name' => trim($page['name']),
                        'path' => trim($page['path']),
                        'is_tab' => intval($page['is_tab'] ?? 0)
                    ];
                }
            }
        }

        // 保存配置
        ConfigServer::set('link_config', 'mobile_pages', $mobile_pages);

        // 调试信息
        \think\facade\Log::info('跳转配置保存成功，页面数量: ' . count($mobile_pages));

        return JsonServer::success('保存成功');
    }

    /**
     * 重置为默认配置
     * @return \think\response\Json
     */
    public function reset()
    {
        if (!$this->request->isPost()) {
            return JsonServer::error('请求方式错误');
        }

        $mobile_pages = $this->getDefaultMobilePages();

        ConfigServer::set('link_config', 'mobile_pages', $mobile_pages);

        return JsonServer::success('重置成功');
    }

    /**
     * 获取默认移动端页面配置
     * @return array
     */
    private function getDefaultMobilePages()
    {
        return [
            [
                'name' => '首页',
                'path' => '/pages/Home/home/<USER>',
                'is_tab' => 1,
            ],
            [
                'name' => '商家后台',
                'path' => '/subcontract/MerchantCenter/index',
                'is_tab' => 0,
            ],
            [
                'name' => '客服中心',
                'path' => '/subcontract/Mine/customerService/index',
                'is_tab' => 1,
            ],
            [
                'name' => '产品库（分类页）',
                'path' => '/pages/Classify/classify/index',
                'is_tab' => 1,
            ],
            [
                'name' => '采购信息',
                'path' => '/pages/Discover/discover/index?activeIndex=0',
                'is_tab' => 1,
            ],
            [
                'name' => '货源信息',
                'path' => '/pages/Discover/discover/index?activeIndex=1',
                'is_tab' => 1,
            ],
            [
                'name' => '找展会',
                'path' => '/pages/Discover/discover/index?activeIndex=2',
                'is_tab' => 1,
            ],
            [
                'name' => '新资讯',
                'path' => '/pages/Discover/discover/index?activeIndex=3',
                'is_tab' => 1,
            ],
            [
                'name' => '会员开通',
                'path' => '/subcontract/Mine/member/member',
                'is_tab' => 0,
            ],
            [
                'name' => '招商顾问申请',
                'path' => '/subcontract/Mine/Applyforagency/index',
                'is_tab' => 0,
            ],
            [
                'name' => '新品上新',
                'path' => '/subcontract/Home/upNew/index',
                'is_tab' => 0,
            ],
            [
                'name' => '行家严选',
                'path' => '/subcontract/Home/strictSelection/index',
                'is_tab' => 0,
            ],
            [
                'name' => '热卖爆品',
                'path' => '/subcontract/Home/HOTSALE/index',
                'is_tab' => 0,
            ],
            [
                'name' => '机构必采',
                'path' => '/subcontract/Home/mustAdopt/index',
                'is_tab' => 0,
            ],
            [
                'name' => '居家爆品',
                'path' => '/subcontract/Home/based/index',
                'is_tab' => 0,
            ],
            [
                'name' => '商家入驻',
                'path' => '/subcontract/Mine/JoinMerchant/index',
                'is_tab' => 0,
            ],
            [
                'name' => '收藏榜',
                'path' => '/subcontract/Home/favorite/index',
                'is_tab' => 0,
            ],
            [
                'name' => '年度榜',
                'path' => '/subcontract/Home/annual/index',
                'is_tab' => 0,
            ],
            [
                'name' => '货直播',
                'path' => '/subcontract/Home/goodsLive/index',
                'is_tab' => 0,
            ],
            [
                'name' => '权威认证',
                'path' => '/subcontract/Home/authoritativeVerification/index',
                'is_tab' => 0,
            ],
            [
                'name' => '热销企业',
                'path' => '/subcontract/Home/CJenterprises/index',
                'is_tab' => 0,
            ],
            [
                'name' => '集采联盟',
                'path' => '/subcontract/Home/CJAlliance/index',
                'is_tab' => 0,
            ],
            [
                'name' => '工厂年度榜',
                'path' => '/subcontract/Home/CJannualHelp/index',
                'is_tab' => 0,
            ],
            [
                'name' => '集采众筹',
                'path' => '/subcontract/Home/purchase/index',
                'is_tab' => 0,
            ],
            [
                'name' => '集采爆品',
                'path' => '/subcontract/Home/centralizedProcurement/index',
                'is_tab' => 0,
            ],
            [
                'name' => '集采推荐榜',
                'path' => '/subcontract/Home/recommend/index',
                'is_tab' => 0,
            ],
            [
                'name' => '集采年度榜',
                'path' => '/subcontract/Home/CPannual/index',
                'is_tab' => 0,
            ],
            [
                'name' => '发布货源信息',
                'path' => '/subcontract/Business/release/release?activeIndex=1',
                'is_tab' => 0,
            ],
            [
                'name' => '发布采购信息',
                'path' => '/subcontract/Business/release/release?activeIndex=0',
                'is_tab' => 0,
            ],
            [
                'name' => '我的用户',
                'path' => '/subcontract/Mine/mineUser/Direct/index',
                'is_tab' => 0,
            ],
            [
                'name' => '我的收益',
                'path' => '/subcontract/Mine/mineIncome/index',
                'is_tab' => 0,
            ],
        ];
    }

}
