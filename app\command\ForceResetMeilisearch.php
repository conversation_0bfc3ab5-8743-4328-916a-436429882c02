<?php
declare (strict_types = 1);

namespace app\command;

use app\common\library\MeiliSearch;
use think\console\Command;
use think\console\Input;
use think\console\Output;

/**
 * 强制重置MeiliSearch索引
 */
class ForceResetMeilisearch extends Command
{
    protected function configure()
    {
        // 指令配置
        $this->setName('force_reset_meilisearch')
            ->setDescription('强制重置MeiliSearch索引');
    }

    protected function execute(Input $input, Output $output)
    {
        $output->writeln("开始强制重置MeiliSearch索引...");

        // 初始化MeiliSearch客户端
        $meili = new MeiliSearch();

        // 删除索引
        $output->writeln('正在删除索引...');
        $result = $meili->deleteIndex('goods');
        $output->writeln('索引删除结果: ' . json_encode($result));

        // 创建索引
        $output->writeln('正在创建索引...');
        $settings = [
            'searchableAttributes' => [
                'name',
                'remark',
                'split_word',
                'content'
            ],
            'filterableAttributes' => [
                'shop_id',
                'first_cate_id',
                'second_cate_id',
                'third_cate_id',
                'brand_id',
                'status',
                'is_hot',
                'is_recommend'
            ],
            'sortableAttributes' => [
                'min_price',
                'sales_actual',
                'sales_virtual',
                'create_time',
                'update_time'
            ],
            'rankingRules' => [
                'words',
                'typo',
                'proximity',
                'attribute',
                'sort',
                'exactness'
            ]
        ];
        $result = $meili->createIndex('goods', $settings);
        $output->writeln('索引创建结果: ' . json_encode($result));

        $output->writeln("MeiliSearch索引重置完成，请运行 sync_goods_to_meilisearch 命令同步数据");
    }
}
