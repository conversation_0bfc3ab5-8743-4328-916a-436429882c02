<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员通知系统测试</title>
    <script src="/static/admin/js/jquery.min.js"></script>
    
    <!-- 惊艳通知弹窗样式 -->
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .test-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }
        
        button {
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .btn-system { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; }
        .btn-success { background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%); color: white; }
        .btn-error { background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%); color: white; }
        .btn-warning { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white; }
        .btn-info { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); color: white; }
        
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        
        .status {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
        }
        
        .status h3 {
            margin: 0 0 10px 0;
            color: #495057;
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            margin: 5px 0;
            padding: 5px 0;
            border-bottom: 1px solid #e9ecef;
        }
        
        .status-item:last-child {
            border-bottom: none;
        }
        
        .status-good { color: #28a745; }
        .status-bad { color: #dc3545; }
        
        /* 通知容器 */
        .amazing-notification {
            position: fixed;
            top: 70px;
            right: 20px;
            width: 380px;
            max-width: 90vw;
            z-index: 99999;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        /* 通知卡片 */
        .notification-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 16px;
            padding: 0;
            margin-bottom: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.15), 0 0 0 1px rgba(255,255,255,0.1);
            overflow: hidden;
            position: relative;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255,255,255,0.2);
            transform: translateX(400px);
            opacity: 0;
            animation: slideInRight 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55) forwards;
        }

        /* 不同类型的渐变背景 */
        .notification-card.system {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .notification-card.success {
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        }
        .notification-card.error {
            background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
        }
        .notification-card.warning {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        .notification-card.info {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        /* 光效背景 */
        .notification-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            animation: shimmer 2s infinite;
        }

        /* 头部区域 */
        .notification-header {
            padding: 20px 20px 15px;
            position: relative;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        /* 图标容器 */
        .notification-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: rgba(255,255,255,0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
            animation: pulse 2s infinite;
            position: relative;
            overflow: hidden;
        }

        .notification-icon::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: conic-gradient(transparent, rgba(255,255,255,0.3), transparent);
            animation: rotate 3s linear infinite;
        }

        /* 标题和内容 */
        .notification-content {
            flex: 1;
            color: white;
        }

        .notification-title {
            font-size: 18px;
            font-weight: 600;
            margin: 0 0 8px 0;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
            animation: fadeInUp 0.8s ease-out 0.2s both;
        }

        .notification-message {
            font-size: 14px;
            line-height: 1.5;
            opacity: 0.95;
            margin: 0;
            text-shadow: 0 1px 2px rgba(0,0,0,0.2);
            animation: fadeInUp 0.8s ease-out 0.4s both;
            white-space: pre-line;
        }

        /* 关闭按钮 */
        .notification-close {
            position: absolute;
            top: 15px;
            right: 15px;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .notification-close:hover {
            background: rgba(255,255,255,0.3);
            transform: scale(1.1);
        }

        /* 进度条 */
        .notification-progress {
            position: absolute;
            bottom: 0;
            left: 0;
            height: 4px;
            background: rgba(255,255,255,0.3);
            width: 100%;
            animation: progressBar 8s linear forwards;
        }

        /* 粒子效果容器 */
        .notification-particles {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            overflow: hidden;
        }

        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: rgba(255,255,255,0.6);
            border-radius: 50%;
            animation: float 3s infinite ease-in-out;
        }

        /* 动画定义 */
        @keyframes slideInRight {
            0% {
                transform: translateX(400px);
                opacity: 0;
            }
            100% {
                transform: translateX(0);
                opacity: 1;
            }
        }

        @keyframes slideOutRight {
            0% {
                transform: translateX(0);
                opacity: 1;
            }
            100% {
                transform: translateX(400px);
                opacity: 0;
            }
        }

        @keyframes fadeInUp {
            0% {
                opacity: 0;
                transform: translateY(20px);
            }
            100% {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
                box-shadow: 0 0 0 0 rgba(255,255,255,0.4);
            }
            50% {
                transform: scale(1.05);
                box-shadow: 0 0 0 10px rgba(255,255,255,0);
            }
        }

        @keyframes shimmer {
            0% {
                left: -100%;
            }
            100% {
                left: 100%;
            }
        }

        @keyframes rotate {
            0% {
                transform: rotate(0deg);
            }
            100% {
                transform: rotate(360deg);
            }
        }

        @keyframes progressBar {
            0% {
                width: 100%;
            }
            100% {
                width: 0%;
            }
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px) rotate(0deg);
                opacity: 1;
            }
            50% {
                transform: translateY(-20px) rotate(180deg);
                opacity: 0.5;
            }
        }

        /* 震动效果 */
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
            20%, 40%, 60%, 80% { transform: translateX(5px); }
        }

        .notification-card.shake {
            animation: shake 0.5s ease-in-out;
        }

        /* 发光效果 */
        .notification-card.glow {
            box-shadow: 0 0 30px rgba(255,255,255,0.5), 0 20px 40px rgba(0,0,0,0.15);
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🎉 管理员通知系统测试</h1>
        
        <div class="test-buttons">
            <button class="btn-system" onclick="testNotification('system')">🔔 系统通知</button>
            <button class="btn-success" onclick="testNotification('success')">✅ 成功通知</button>
            <button class="btn-error" onclick="testNotification('error')">❌ 错误通知</button>
            <button class="btn-warning" onclick="testNotification('warning')">⚠️ 警告通知</button>
            <button class="btn-info" onclick="testNotification('info')">💡 信息通知</button>
            <button class="btn-info" onclick="testShopEntryNotification()">🏪 入驻申请通知</button>
        </div>
        
        <div class="test-buttons">
            <button onclick="testApiNotification()">📡 测试API通知</button>
            <button onclick="clearAllNotifications()">🗑️ 清除所有通知</button>
            <button onclick="checkSystemStatus()">🔍 检查系统状态</button>
        </div>
        
        <div class="status" id="systemStatus">
            <h3>系统状态</h3>
            <div class="status-item">
                <span>通知系统:</span>
                <span id="notificationStatus" class="status-good">✅ 已加载</span>
            </div>
            <div class="status-item">
                <span>WebSocket连接:</span>
                <span id="websocketStatus" class="status-bad">❌ 未连接</span>
            </div>
            <div class="status-item">
                <span>API状态:</span>
                <span id="apiStatus" class="status-bad">⏳ 检查中...</span>
            </div>
        </div>
    </div>

    <script>
        // 管理员信息设置
        window.ADMIN_ID = 1;
        window.ADMIN_NICKNAME = '测试管理员';
        window.ADMIN_TOKEN = 'test_token';

        // 初始化通知系统
        $(document).ready(function(){
            initNotificationSystem();
            checkSystemStatus();
        });

        // 初始化通知系统
        function initNotificationSystem() {
            // 确保通知容器存在
            if (!document.querySelector('.amazing-notification')) {
                var container = document.createElement('div');
                container.className = 'amazing-notification';
                document.body.appendChild(container);
            }
            
            console.log('通知系统初始化完成');
        }

        // 惊艳通知弹窗函数
        function showAmazingNotification(title, content, type, url, icon) {
            console.log('显示惊艳通知:', { title: title, content: content, type: type, url: url, icon: icon });

            // 确保通知容器存在
            var container = document.querySelector('.amazing-notification');
            if (!container) {
                container = document.createElement('div');
                container.className = 'amazing-notification';
                document.body.appendChild(container);
            }

            // 确定通知类型和图标
            var notificationType = 'system';
            var iconSymbol = '🔔';
            
            if (type === 'success_notification' || type === 'success' || icon === 1) {
                notificationType = 'success';
                iconSymbol = '✅';
            } else if (type === 'error_notification' || type === 'error' || icon === 2) {
                notificationType = 'error';
                iconSymbol = '❌';
            } else if (type === 'warning_notification' || type === 'warning' || icon === 3) {
                notificationType = 'warning';
                iconSymbol = '⚠️';
            } else if (type === 'info_notification' || type === 'info' || icon === 4) {
                notificationType = 'info';
                iconSymbol = '💡';
            }

            // 创建通知卡片
            var notificationId = 'notification-' + Date.now();
            var cardHtml = `
                <div class="notification-card ${notificationType}" id="${notificationId}">
                    <!-- 光效背景 -->
                    <div class="notification-particles">
                        ${generateParticles()}
                    </div>
                    
                    <!-- 头部内容 -->
                    <div class="notification-header">
                        <div class="notification-icon">
                            <span style="position: relative; z-index: 2;">${iconSymbol}</span>
                        </div>
                        <div class="notification-content">
                            <h4 class="notification-title">${title || '系统通知'}</h4>
                            <p class="notification-message">${content || '您有一条新的消息'}</p>
                        </div>
                    </div>
                    
                    <!-- 关闭按钮 -->
                    <button class="notification-close" onclick="closeAmazingNotification('${notificationId}')">
                        ×
                    </button>
                    
                    <!-- 进度条 -->
                    <div class="notification-progress"></div>
                </div>
            `;

            // 添加到容器
            container.insertAdjacentHTML('beforeend', cardHtml);
            
            // 获取刚创建的通知元素
            var notificationElement = document.getElementById(notificationId);
            
            // 添加点击事件（如果有URL）
            if (url) {
                notificationElement.style.cursor = 'pointer';
                notificationElement.addEventListener('click', function(e) {
                    if (!e.target.classList.contains('notification-close')) {
                        window.open(url, '_blank');
                        closeAmazingNotification(notificationId);
                    }
                });
            }

            // 添加震动效果（错误通知）
            if (notificationType === 'error') {
                setTimeout(function() {
                    notificationElement.classList.add('shake');
                }, 100);
            }

            // 添加发光效果（成功通知）
            if (notificationType === 'success') {
                setTimeout(function() {
                    notificationElement.classList.add('glow');
                }, 200);
            }

            // 自动关闭
            setTimeout(function() {
                closeAmazingNotification(notificationId);
            }, 8000);

            return notificationId;
        }

        // 生成粒子效果
        function generateParticles() {
            var particles = '';
            for (var i = 0; i < 8; i++) {
                var left = Math.random() * 100;
                var delay = Math.random() * 3;
                var duration = 2 + Math.random() * 2;
                particles += `<div class="particle" style="left: ${left}%; animation-delay: ${delay}s; animation-duration: ${duration}s;"></div>`;
            }
            return particles;
        }

        // 关闭通知函数
        function closeAmazingNotification(notificationId) {
            var notification = document.getElementById(notificationId);
            if (notification) {
                // 添加退出动画
                notification.style.animation = 'slideOutRight 0.4s ease-in-out forwards';
                
                // 动画完成后移除元素
                setTimeout(function() {
                    if (notification && notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 400);
            }
        }

        // 清除所有通知
        function clearAllNotifications() {
            var container = document.querySelector('.amazing-notification');
            if (container) {
                var notifications = container.querySelectorAll('.notification-card');
                notifications.forEach(function(notification) {
                    notification.style.animation = 'slideOutRight 0.4s ease-in-out forwards';
                });
                
                setTimeout(function() {
                    if (container) {
                        container.innerHTML = '';
                    }
                }, 400);
            }
        }

        // 测试不同类型的通知
        function testNotification(type) {
            var notifications = {
                system: {
                    title: '🔔 系统通知',
                    content: '系统运行正常，所有服务已启动。\n当前时间：' + new Date().toLocaleString(),
                    type: 'system'
                },
                success: {
                    title: '✅ 操作成功',
                    content: '数据备份已完成，系统运行稳定。\n备份文件已保存到安全位置。',
                    type: 'success'
                },
                error: {
                    title: '❌ 错误警报',
                    content: '检测到系统异常，请立即处理！\n错误代码：ERR_001',
                    type: 'error'
                },
                warning: {
                    title: '⚠️ 警告提醒',
                    content: '磁盘空间不足，建议清理无用文件。\n当前可用空间：15%',
                    type: 'warning'
                },
                info: {
                    title: '💡 信息提示',
                    content: '今日访问量已达到新高，系统表现良好。\n访问量：10,000+ PV',
                    type: 'info'
                }
            };

            var notification = notifications[type];
            if (notification) {
                showAmazingNotification(notification.title, notification.content, notification.type);
            }
        }

        // 测试商家入驻申请通知
        function testShopEntryNotification() {
            showAmazingNotification(
                '🏪 新的0元入驻申请',
                '商家名称：测试商家\n申请人：张三\n联系电话：13800138000\n申请时间：' + new Date().toLocaleString(),
                'info',
                '/admin/shop/entry_list',
                4
            );
        }

        // 测试API通知
        function testApiNotification() {
            $.get('/api/admin_notification/testNotification', {
                title: '📡 API测试通知',
                content: '这是通过API发送的测试通知\n发送时间：' + new Date().toLocaleString(),
                type: 'info',
                icon: 4
            }).done(function(response) {
                console.log('API通知响应:', response);
                $('#apiStatus').html('✅ API正常').removeClass('status-bad').addClass('status-good');
            }).fail(function() {
                $('#apiStatus').html('❌ API异常').removeClass('status-good').addClass('status-bad');
            });
        }

        // 检查系统状态
        function checkSystemStatus() {
            // 检查通知系统
            if (typeof showAmazingNotification === 'function') {
                $('#notificationStatus').html('✅ 已加载').removeClass('status-bad').addClass('status-good');
            } else {
                $('#notificationStatus').html('❌ 未加载').removeClass('status-good').addClass('status-bad');
            }

            // 检查API状态
            $.get('/api/admin_notification/testNotification', {
                title: '系统检查',
                content: '正在检查API状态...',
                type: 'system'
            }).done(function(response) {
                $('#apiStatus').html('✅ API正常').removeClass('status-bad').addClass('status-good');
            }).fail(function() {
                $('#apiStatus').html('❌ API异常').removeClass('status-good').addClass('status-bad');
            });

            // WebSocket状态（这里简化处理）
            $('#websocketStatus').html('⚠️ 测试页面未连接').removeClass('status-bad status-good');
        }

        // 全局暴露函数
        window.showAmazingNotification = showAmazingNotification;
        window.closeAmazingNotification = closeAmazingNotification;
        window.clearAllNotifications = clearAllNotifications;
    </script>
</body>
</html>