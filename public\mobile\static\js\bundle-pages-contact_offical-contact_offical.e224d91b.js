(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["bundle-pages-contact_offical-contact_offical"],{"03a8":function(e,t,n){var o=n("24fb"),i=n("1de5"),a=n("3104f"),r=n("8ec9");t=o(!1);var s=i(a),c=i(r);t.push([e.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */uni-page-body[data-v-1b8ee771]{padding:0}.contact-offical[data-v-1b8ee771]{min-height:100vh;background-image:url('+s+");background-position:50%;background-size:cover;background-repeat:no-repeat}.contact-offical.shop-contact[data-v-1b8ee771]{background-image:url("+c+")}.contact-offical.shop-contact .header .line[data-v-1b8ee771]{background:#3a67e4}.contact-offical .header[data-v-1b8ee771]{padding:%?80?% %?50?% 0}.contact-offical .header .line[data-v-1b8ee771]{height:%?25?%;background:#be000e;border-radius:%?20?%}.contact-offical .content[data-v-1b8ee771]{margin-top:%?-10?%}.contact-offical .content .content-view[data-v-1b8ee771]{width:%?620?%;padding:%?80?% %?20?% %?40?%}.contact-offical .content .phone-btn[data-v-1b8ee771]{padding:%?2?% %?19?%;border:1px solid #d7d7d7;border-radius:%?10?%}.contact-offical .content .copy-btn[data-v-1b8ee771]{background:linear-gradient(#12c96e,#0abd57 50.76%,#03b240);width:%?460?%;height:%?84?%;box-shadow:0 3px 6px rgba(0,0,0,.16)}",""]),e.exports=t},1522:function(e,t,n){"use strict";n.d(t,"b",(function(){return i})),n.d(t,"c",(function(){return a})),n.d(t,"a",(function(){return o}));var o={uIcon:n("90f3").default},i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("v-uni-view",{staticClass:"u-image",style:[e.wrapStyle,e.backgroundStyle],on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.onClick.apply(void 0,arguments)}}},[e.isError?e._e():n("v-uni-image",{staticClass:"u-image__image",style:{borderRadius:"circle"==e.shape?"50%":e.$u.addUnit(e.borderRadius)},attrs:{src:e.src,mode:e.mode,"lazy-load":e.lazyLoad},on:{error:function(t){arguments[0]=t=e.$handleEvent(t),e.onErrorHandler.apply(void 0,arguments)},load:function(t){arguments[0]=t=e.$handleEvent(t),e.onLoadHandler.apply(void 0,arguments)}}}),e.showLoading&&e.loading?n("v-uni-view",{staticClass:"u-image__loading",style:{borderRadius:"circle"==e.shape?"50%":e.$u.addUnit(e.borderRadius),backgroundColor:this.bgColor}},[e.$slots.loading?e._t("loading"):n("u-icon",{attrs:{name:e.loadingIcon,width:e.width,height:e.height}})],2):e._e(),e.showError&&e.isError&&!e.loading?n("v-uni-view",{staticClass:"u-image__error",style:{borderRadius:"circle"==e.shape?"50%":e.$u.addUnit(e.borderRadius)}},[e.$slots.error?e._t("error"):n("u-icon",{attrs:{name:e.errorIcon,width:e.width,height:e.height}})],2):e._e()],1)},a=[]},"1de5":function(e,t,n){"use strict";e.exports=function(e,t){return t||(t={}),e=e&&e.__esModule?e.default:e,"string"!==typeof e?e:(/^['"].*['"]$/.test(e)&&(e=e.slice(1,-1)),t.hash&&(e+=t.hash),/["'() \t\n]/.test(e)||t.needQuotes?'"'.concat(e.replace(/"/g,'\\"').replace(/\n/g,"\\n"),'"'):e)}},2322:function(e,t,n){"use strict";n("7a82"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("a9e3");var o={name:"u-image",props:{src:{type:String,default:""},mode:{type:String,default:"aspectFill"},width:{type:[String,Number],default:"100%"},height:{type:[String,Number],default:"auto"},shape:{type:String,default:"square"},borderRadius:{type:[String,Number],default:0},lazyLoad:{type:Boolean,default:!0},showMenuByLongpress:{type:Boolean,default:!0},loadingIcon:{type:String,default:"photo"},errorIcon:{type:String,default:"error-circle"},showLoading:{type:Boolean,default:!0},showError:{type:Boolean,default:!0},fade:{type:Boolean,default:!0},webp:{type:Boolean,default:!1},duration:{type:[String,Number],default:500},bgColor:{type:String,default:"#f3f4f6"}},data:function(){return{isError:!1,loading:!0,opacity:1,durationTime:this.duration,backgroundStyle:{}}},watch:{src:{immediate:!0,handler:function(e){e?this.isError=!1:(this.isError=!0,this.loading=!1)}}},computed:{wrapStyle:function(){var e={};return e.width=this.$u.addUnit(this.width),e.height=this.$u.addUnit(this.height),e.borderRadius="circle"==this.shape?"50%":this.$u.addUnit(this.borderRadius),e.overflow=this.borderRadius>0?"hidden":"visible",this.fade&&(e.opacity=this.opacity,e.transition="opacity ".concat(Number(this.durationTime)/1e3,"s ease-in-out")),e}},methods:{onClick:function(){this.$emit("click")},onErrorHandler:function(e){this.loading=!1,this.isError=!0,this.$emit("error",e)},onLoadHandler:function(){var e=this;if(this.loading=!1,this.isError=!1,this.$emit("load"),!this.fade)return this.removeBgColor();this.opacity=0,this.durationTime=0,setTimeout((function(){e.durationTime=e.duration,e.opacity=1,setTimeout((function(){e.removeBgColor()}),e.durationTime)}),50)},removeBgColor:function(){this.backgroundStyle={backgroundColor:"transparent"}}}};t.default=o},"23c5":function(e,t,n){"use strict";n("7a82");var o=n("ee27").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=o(n("f07e")),a=o(n("c964"));n("a9e3");var r=n("20b7"),s=n("7e6f"),c=n("b08d"),u={name:"contactOffical",data:function(){return{server:{config:{},shop:{}},showPhoneCall:!1,shopId:0,isShow:!1}},onLoad:function(){this.shopId=Number(this.$Route.query.id),this.getServiceFun()},methods:{getServiceFun:function(){var e=this;return(0,a.default)((0,i.default)().mark((function t(){var n,o,a;return(0,i.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!e.shopId){t.next=6;break}return t.next=3,(0,s.getShopService)(e.shopId);case 3:t.t0=t.sent,t.next=9;break;case 6:return t.next=8,(0,r.getService)();case 8:t.t0=t.sent;case 9:n=t.t0,o=n.data,a=n.code,1==a&&(e.server=o,e.isShow=!0);case 13:case"end":return t.stop()}}),t)})))()},onCopy:function(e){(0,c.copy)(e)},onCall:function(){wx.makePhoneCall({phoneNumber:String(this.server.config&&this.server.config.phone||this.server.phone),success:function(e){console.log("成功",e)},fail:function(e){console.log("失败",e)}})}}};t.default=u},"3104f":function(e,t,n){e.exports=n.p+"bundle/static/contact_official_bg.png"},6021:function(e,t,n){"use strict";var o=n("64b1"),i=n.n(o);i.a},"61e4":function(e,t,n){"use strict";var o=n("c27e"),i=n.n(o);i.a},"64b1":function(e,t,n){var o=n("6e35");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[e.i,o,""]]),o.locals&&(e.exports=o.locals);var i=n("4f06").default;i("3d1e497c",o,!0,{sourceMap:!1,shadowMode:!1})},"6e35":function(e,t,n){var o=n("24fb");t=o(!1),t.push([e.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.u-image[data-v-1bf07c9a]{position:relative;transition:opacity .5s ease-in-out}.u-image__image[data-v-1bf07c9a]{width:100%;height:100%}.u-image__loading[data-v-1bf07c9a], .u-image__error[data-v-1bf07c9a]{position:absolute;top:0;left:0;width:100%;height:100%;display:flex;flex-direction:row;align-items:center;justify-content:center;background-color:#f3f4f6;color:#909399;font-size:%?46?%}',""]),e.exports=t},"70ed":function(e,t,n){"use strict";n.r(t);var o=n("23c5"),i=n.n(o);for(var a in o)["default"].indexOf(a)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(a);t["default"]=i.a},"72c09":function(e,t,n){"use strict";n.d(t,"b",(function(){return i})),n.d(t,"c",(function(){return a})),n.d(t,"a",(function(){return o}));var o={uImage:n("ba4b").default,uModal:n("8d42").default},i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:e.isShow,expression:"isShow"}],staticClass:"contact-offical",class:{"shop-contact":e.shopId}},[e.shopId?[n("v-uni-view",{staticClass:"header white"},[n("v-uni-view",{staticClass:"title font-size-50 text-center"},[e._v("店铺客服")]),n("v-uni-view",{staticClass:"line m-t-36"})],1),n("v-uni-view",{staticClass:"content flex-col col-center"},[n("v-uni-view",{staticClass:"content-view flex-col col-center bg-white text-center"},[n("v-uni-view",{staticClass:"flex col-center m-b-40 m-l-40",staticStyle:{"align-self":"flex-start"}},[n("u-image",{attrs:{width:"88rpx",height:"88rpx","border-radius":"50%",src:e.server.shop.logo}}),n("v-uni-view",{staticClass:"lg m-l-20"},[e._v(e._s(e.server.shop.name))])],1),n("u-image",{attrs:{width:"360rpx",height:"360rpx",src:e.server.config.image}}),e.server.config.wechat?n("v-uni-view",{staticClass:"flex row-center copy-btn white lg br60 m-t-50 m-b-40",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.onCopy(e.server.config.wechat)}}},[e._v("复制去微信添加")]):e._e(),n("v-uni-view",{staticClass:"lighter xs"},[e._v("营业时间："+e._s(e.server.config.business_time))]),n("v-uni-view",{staticClass:"xs lighter m-t-10 flex"},[e._v("客服电话："+e._s(e.server.config.phone)),n("v-uni-view",{staticClass:"phone-btn m-l-30",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.showPhoneCall=!0}}},[e._v("拨打")])],1)],1)],1),n("u-modal",{attrs:{content:"即将打电话给"+e.server.config.phone,"show-cancel-button":!0,"confirm-text":"呼叫","confirm-color":e.colorConfig.primary},on:{confirm:function(t){arguments[0]=t=e.$handleEvent(t),e.onCall.apply(void 0,arguments)}},model:{value:e.showPhoneCall,callback:function(t){e.showPhoneCall=t},expression:"showPhoneCall"}})]:[n("v-uni-view",{staticClass:"header white"},[n("v-uni-view",{staticClass:"title font-size-50 text-center"},[e._v("平台客服")]),n("v-uni-view",{staticClass:"line m-t-36"})],1),n("v-uni-view",{staticClass:"content flex-col col-center"},[n("v-uni-view",{staticClass:"content-view flex-col col-center bg-white text-center"},[n("u-image",{attrs:{width:"360rpx",height:"360rpx",src:e.server.image}}),e.server.wechat?n("v-uni-view",{staticClass:"flex row-center copy-btn lg br60 white m-t-50 m-b-40",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.onCopy(e.server.wechat)}}},[e._v("复制去微信添加")]):e._e(),n("v-uni-view",{staticClass:"lighter xs"},[e._v("营业时间："+e._s(e.server.business_time))]),n("v-uni-view",{staticClass:"xs lighter m-t-10 flex"},[e._v("平台电话："+e._s(e.server.phone)),n("v-uni-view",{staticClass:"phone-btn m-l-30",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.showPhoneCall=!0}}},[e._v("拨打")])],1)],1)],1),n("u-modal",{attrs:{content:"即将打电话给"+e.server.phone,"show-cancel-button":!0,"confirm-text":"呼叫","confirm-color":e.colorConfig.primary},on:{confirm:function(t){arguments[0]=t=e.$handleEvent(t),e.onCall.apply(void 0,arguments)}},model:{value:e.showPhoneCall,callback:function(t){e.showPhoneCall=t},expression:"showPhoneCall"}})]],2)},a=[]},"7e6f":function(e,t,n){"use strict";n("7a82");var o=n("ee27").default;Object.defineProperty(t,"__esModule",{value:!0}),t.apiInvoiceAdd=function(e){return i.default.post("order_invoice/add",e)},t.apiInvoiceDetail=function(e){return i.default.get("order_invoice/detail",{params:e})},t.apiInvoiceEdit=function(e){return i.default.post("order_invoice/edit",e)},t.apiOrderInvoiceDetail=function(e){return i.default.get("order/invoice",{params:e})},t.changeShopFollow=function(e){return i.default.post("shop_follow/changeStatus",e)},t.getInvoiceSetting=function(e){return i.default.get("order_invoice/setting",{params:e})},t.getNearbyShops=function(e){return i.default.get("shop/getNearbyShops",{params:e})},t.getShopCategory=function(){return i.default.get("shop_category/getList")},t.getShopGoodsCategory=function(e){return i.default.get("shop_goods_category/getShopGoodsCategory",{params:e})},t.getShopInfo=function(e){return i.default.get("shop/getShopInfo",{params:e})},t.getShopList=function(e){return i.default.get("shop/getShopList",{params:e})},t.getShopService=function(e){return i.default.get("setting/getShopCustomerService",{params:{shop_id:e}})},t.getTreaty=function(){return i.default.get("ShopApply/getTreaty")},t.shopApply=function(e){return i.default.post("ShopApply/apply",e)},t.shopApplyDetail=function(e){return i.default.get("ShopApply/detail",{params:{id:e}})},t.shopApplyRecord=function(e){return i.default.get("ShopApply/record",{params:e})};var i=o(n("3b33"));n("b08d")},"8ec9":function(e,t,n){e.exports=n.p+"bundle/static/shop_official_bg.png"},af8d:function(e,t,n){"use strict";n.r(t);var o=n("2322"),i=n.n(o);for(var a in o)["default"].indexOf(a)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(a);t["default"]=i.a},ba4b:function(e,t,n){"use strict";n.r(t);var o=n("1522"),i=n("af8d");for(var a in i)["default"].indexOf(a)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(a);n("6021");var r=n("f0c5"),s=Object(r["a"])(i["default"],o["b"],o["c"],!1,null,"1bf07c9a",null,!1,o["a"],void 0);t["default"]=s.exports},c27e:function(e,t,n){var o=n("03a8");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[e.i,o,""]]),o.locals&&(e.exports=o.locals);var i=n("4f06").default;i("21bacfc8",o,!0,{sourceMap:!1,shadowMode:!1})},f8776:function(e,t,n){"use strict";n.r(t);var o=n("72c09"),i=n("70ed");for(var a in i)["default"].indexOf(a)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(a);n("61e4");var r=n("f0c5"),s=Object(r["a"])(i["default"],o["b"],o["c"],!1,null,"1b8ee771",null,!1,o["a"],void 0);t["default"]=s.exports}}]);