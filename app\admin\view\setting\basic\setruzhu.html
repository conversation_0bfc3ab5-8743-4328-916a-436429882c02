{layout name="layout1" /}
<div class="wrapper">
    <div class="layui-card">
        <div class="layui-card-body">
            <div class="layui-collapse like-layui-collapse" style="border:1px dashed #c4c4c4; margin-bottom: 30px;">
                <div class="layui-colla-item">
                    <h2 class="layui-colla-title like-layui-colla-title" style="background-color: #fff">操作提示</h2>
                    <div class="layui-colla-content layui-show">
                        *设置商城商家入驻页设置。
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-form">
            <div class="layui-tab layui-tab-card">
                <ul class="layui-tab-title">
                    <li  style="display: none">H5商城</li>
                    <li class="layui-this">小程序商城</li>
                </ul>
                <div class="layui-tab-content">
                    <!-- H5 -->
                    <div class="layui-tab-item layui-show">
                        <!-- H5分享标题 -->

                        <!-- 小程序分享图片 -->
                        <div class="layui-form-item">
                            <label class="layui-form-label">背景图片：</label>
                            <div class="layui-input-inline">
                                <div class="like-upload-image">
                                    {if !empty($config.mnp_ruzhu_image)}
                                    <div class="upload-image-div">
                                        <img src="{$config.mnp_ruzhu_image}" alt="img" style="height: 80px;width:auto">
                                        <input name="mnp_ruzhu_image" type="hidden" value="{$config.mnp_ruzhu_image}">
                                        <div class="del-upload-btn">x</div>
                                    </div>
                                    <div class="upload-image-elem" style="display:none;"><a class="add-upload-image mnp_ruzhu_image"> + 添加图片</a></div>
                                    {else}
                                    <div class="upload-image-elem"><a class="add-upload-image mnp_ruzhu_image"> + 添加图片</a></div>
                                    {/if}
                                </div>
                            </div>
                        </div>

                        <!-- 小程序分享图片 -->
                        <div class="layui-form-item">
                            <label class="layui-form-label">联系人二维码：</label>
                            <div class="layui-input-inline">
                                <div class="like-upload-image">
                                    {if !empty($config.mnp_lxr_image)}
                                    <div class="upload-image-div">
                                        <img src="{$config.mnp_lxr_image}" alt="img" style="height: 80px;width:auto">
                                        <input name="mnp_lxr_image" type="hidden" value="{$config.mnp_lxr_image}">
                                        <div class="del-upload-btn">x</div>
                                    </div>
                                    <div class="upload-image-elem" style="display:none;"><a class="add-upload-image mnp_lxr_image"> + 添加图片</a></div>
                                    {else}
                                    <div class="upload-image-elem"><a class="add-upload-image mnp_lxr_image"> + 添加图片</a></div>
                                    {/if}
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- 小程序 -->

                    <!-- 提交 -->
                    <div class="layui-form-item" style="margin-top:30px">
                        <div class="layui-input-block">
                            <button class="layui-btn layui-bg-blue" lay-submit lay-filter="addSublime">提交</button>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>
</div>


<script>
    layui.use(['table', 'form'], function () {
        var form = layui.form;

        like.delUpload();
        $(document).on("click", ".h5_share_image", function () {
            like.imageUpload({
                limit: 1,
                field: "h5_share_image",
                that: $(this)
            });
        });

        $(document).on("click", ".mnp_ruzhu_image", function () {
            like.imageUpload({
                limit: 1,
                field: "mnp_ruzhu_image",
                that: $(this)
            });
        }); $(document).on("click", ".mnp_lxr_image", function () {
            like.imageUpload({
                limit: 1,
                field: "mnp_lxr_image",
                that: $(this)
            });
        });

        // 监听提交
        form.on('submit(addSublime)', function (data) {
            like.ajax({
                url: '{:url("setting.Basic/setruzhudata")}'
                , data: data.field
                , type: 'post'
                , success: function (res) {
                    if (res.code === 1) {
                        layer.msg(res.msg, {
                            offset: '15px'
                            , icon: 1
                            , time: 1000
                        },function () {
                            location.href = location.href;
                        });
                    }
                }
            });
            return false;
        });
    });
</script>