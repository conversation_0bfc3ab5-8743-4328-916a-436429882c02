<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Bm\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * RunUserCmd返回参数结构体
 *
 * @method array getSuccessTaskInfoSet() 获取运行成功的任务信息列表
 * @method void setSuccessTaskInfoSet(array $SuccessTaskInfoSet) 设置运行成功的任务信息列表
 * @method array getFailedTaskInfoSet() 获取运行失败的任务信息列表
 * @method void setFailedTaskInfoSet(array $FailedTaskInfoSet) 设置运行失败的任务信息列表
 * @method string getRequestId() 获取唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
 * @method void setRequestId(string $RequestId) 设置唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
 */
class RunUserCmdResponse extends AbstractModel
{
    /**
     * @var array 运行成功的任务信息列表
     */
    public $SuccessTaskInfoSet;

    /**
     * @var array 运行失败的任务信息列表
     */
    public $FailedTaskInfoSet;

    /**
     * @var string 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
     */
    public $RequestId;

    /**
     * @param array $SuccessTaskInfoSet 运行成功的任务信息列表
     * @param array $FailedTaskInfoSet 运行失败的任务信息列表
     * @param string $RequestId 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("SuccessTaskInfoSet",$param) and $param["SuccessTaskInfoSet"] !== null) {
            $this->SuccessTaskInfoSet = [];
            foreach ($param["SuccessTaskInfoSet"] as $key => $value){
                $obj = new SuccessTaskInfo();
                $obj->deserialize($value);
                array_push($this->SuccessTaskInfoSet, $obj);
            }
        }

        if (array_key_exists("FailedTaskInfoSet",$param) and $param["FailedTaskInfoSet"] !== null) {
            $this->FailedTaskInfoSet = [];
            foreach ($param["FailedTaskInfoSet"] as $key => $value){
                $obj = new FailedTaskInfo();
                $obj->deserialize($value);
                array_push($this->FailedTaskInfoSet, $obj);
            }
        }

        if (array_key_exists("RequestId",$param) and $param["RequestId"] !== null) {
            $this->RequestId = $param["RequestId"];
        }
    }
}
