<?php

namespace app\admin\logic\content;

use app\common\basics\Logic;
use app\common\model\content\LearningCenterCategory;
use Exception;

class LearningCenterCategoryLogic extends Logic
{
    /**
     * 获取学习中心分类列表
     * @param $get
     * @return array
     */
    public static function lists($get)
    {
        try {
            $where = [
                ['del', '=', 0]
            ];

            $model = new LearningCenterCategory();
            $lists = $model->field(true)
                ->where($where)
                ->order('sort', 'asc')
                ->paginate([
                    'page'      => $get['page'],
                    'list_rows' => $get['limit'],
                    'var_page'  => 'page'
                ])
                ->toArray();

            foreach ($lists['data'] as &$item) {
                $item['is_show'] = $item['is_show'] ? '启用' : '停用';
            }

            return ['count'=>$lists['total'], 'lists'=>$lists['data']];
        } catch (Exception $e) {
            return ['error'=>$e->getMessage()];
        }
    }

    /**
     * @Notes: 获取学习中心分类
     * @Author: 系统
     * @return array
     */
    public static function getCategory()
    {
        try {
            $model = new LearningCenterCategory();
            return $model->field(true)
                ->where(['del'=>0, 'is_show'=>1])
                ->order('sort', 'asc')
                ->select()
                ->toArray();

        } catch (\Exception $e) {
            return [];
        }
    }

    /**
     * 获取学习中心分类详细
     * @param $id
     * @return array
     */
    public static function detail($id)
    {
        $model = new LearningCenterCategory();
        return $model->field(true)->findOrEmpty($id)->toArray();
    }

    /**
     * 添加学习中心分类
     * @param $post
     * @return bool
     */
    public static function add($post)
    {
        try {
            LearningCenterCategory::create([
                'name'    => $post['name'],
                'sort'    => $post['sort'] ?? 0,
                'is_show' => $post['is_show']
            ]);

            return true;
        } catch (\Exception $e) {
            static::$error = $e->getMessage();
            return false;
        }
    }

    /**
     * 编辑学习中心分类
     * @param $post
     * @return bool
     */
    public static function edit($post)
    {
        try {
            $model = new LearningCenterCategory();
            $model->where(['id'=>$post['id']])->update([
                'name'    => $post['name'],
                'sort'    => $post['sort'] ?? 0,
                'is_show' => $post['is_show']
            ]);

            return true;
        } catch (\Exception $e) {
            static::$error = $e->getMessage();
            return false;
        }
    }

    /**
     * 删除学习中心分类
     * @param $id
     * @return bool
     */
    public static function del($id)
    {
        try {
            // 检查该分类下是否有内容
            $model = new LearningCenterCategory();
            $category = $model->with(['learningCenter'=>function($query){
                $query->where(['del'=>0]);
            }])->findOrEmpty($id)->toArray();

            if (!empty($category['learning_center'])) {
                static::$error = '该分类下有内容，不能删除';
                return false;
            }

            $model->where(['id'=>$id])->update(['del'=>1]);
            return true;
        } catch (\Exception $e) {
            static::$error = $e->getMessage();
            return false;
        }
    }

    /**
     * 设置学习中心分类状态
     * @param $post
     * @return bool
     */
    public static function setStatus($post)
    {
        try {
            $model = new LearningCenterCategory();
            $model->where(['id'=>$post['id']])->update(['is_show'=>$post['status']]);
            return true;
        } catch (\Exception $e) {
            static::$error = $e->getMessage();
            return false;
        }
    }
}