{layout name="layout2" /}
<style>
    .layui-form-label{
        width: 120px;
    }
</style>
<div class="layui-form" lay-filter="layuiadmin-form" id="layuiadmin-form" style="padding: 20px 30px 0 0;">

    <!--支付简称-->
    <div class="layui-form-item">
        <label class="layui-form-label">支付简称：</label>
        <div class="layui-input-block">
            <div class="layui-col-md4">
                <input type="text" name="short_name" value="{$info.short_name | default = ''}" lay-verify="check_required" lay-verType="tips"  autocomplete="off" class="layui-input">
                <div class="layui-form-mid layui-word-aux">会员在商城看见的支付名称</div>
            </div>
        </div>
    </div>

    <!--支付图标-->
    <div class="layui-form-item">
        <label class="layui-form-label">支付图标</label>
        <div class="layui-input-inline">
            <div class="like-upload-image">
                {if $info.image}
                <div class="upload-image-div">
                    <img src="{$info.image}" alt="img">
                    <input type="hidden" name="image" value="{$info.image}">
                    <div class="del-upload-btn">x</div>
                </div>
                <div class="upload-image-elem" style="display:none;"><a class="add-upload-image"> + 添加图片</a></div>
                {else}
                <div class="upload-image-elem"><a class="add-upload-image"> + 添加图片</a></div>
                {/if}
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"></label>
            <label class=" layui-form-mid layui-word-aux">支付方式图标。建议尺寸：宽100px*高100px，jpg，jpeg，png格式</label>
        </div>
    </div>

    <!--应用ID-->
    <div class="layui-form-item">
        <label class="layui-form-label">应用ID：</label>
        <div class="layui-input-block">
            <div class="layui-col-md4">
                <input type="text" name="app_id" value="{$info.config.app_id | default = ''}" lay-verify="check_required" lay-verType="tips" autocomplete="off" class="layui-input">
                <div class="layui-form-mid layui-word-aux">支付宝应用APP_ID</div>
            </div>
        </div>
    </div>

    <!--应用私钥-->
    <div class="layui-form-item">
        <label class="layui-form-label">应用私钥：</label>
        <div class="layui-input-block">
            <div class="layui-col-md4">
                <input type="text" name="private_key" value="{$info.config.private_key | default = ''}" lay-verify="check_required" lay-verType="tips" autocomplete="off" class="layui-input">
                <div class="layui-form-mid layui-word-aux">应用私钥（private_key）</div>
            </div>
        </div>
    </div>

    <!-- 接口加密方式 -->
    <div class="layui-form-item">
        <label class="layui-form-label">接口加密方式：</label>
        <div class="layui-input-block">
            <input type="radio" name="api_type" value="certificate" title="证书" checked>
        </div>
    </div>

    <!--应用公钥证书-->
    <div class="layui-form-item layui-form-text">
        <label class="layui-form-label">应用公钥证书：</label>
        <div class="layui-input-block">
            <div class="layui-col-md4">
                <textarea placeholder="请输入应用公钥证书" name="app_cert" class="layui-textarea">{$info.config.app_cert | default = ''}</textarea>
                <div class="layui-form-mid layui-word-aux">应用公钥证书（appCertPublicKey）</div>
            </div>
        </div>
    </div>

    <!--支付宝公钥证书-->
    <div class="layui-form-item layui-form-text">
        <label class="layui-form-label">支付宝公钥证书：</label>
        <div class="layui-input-block">
            <div class="layui-col-md4">
                <textarea placeholder="请输入支付宝公钥证书" name="ali_public_cert" class="layui-textarea">{$info.config.ali_public_cert | default = ''}</textarea>
                <div class="layui-form-mid layui-word-aux">支付宝公钥证书（alipayCertPublicKey）</div>
            </div>
        </div>
    </div>

    <!--支付宝CA根证书-->
    <div class="layui-form-item layui-form-text">
        <label class="layui-form-label">支付宝根证书：</label>
        <div class="layui-input-block">
            <div class="layui-col-md4">
                <textarea placeholder="请输入支付宝CA根证书" name="ali_root_cert" class="layui-textarea">{$info.config.ali_root_cert | default = ''}</textarea>
                <div class="layui-form-mid layui-word-aux">支付宝根证书（alipayRootCert）</div>
            </div>
        </div>
    </div>

    <!--排序-->
    <div class="layui-form-item">
        <label class="layui-form-label">排序：</label>
        <div class="layui-input-block">
            <div class="layui-col-md4">
                <input type="number"  name="sort" value="{$info.sort | default = ''}" placeholder="请输入排序" class="layui-input">
                <div class=" layui-form-mid layui-word-aux">排序越小越前</div>
            </div>
        </div>
    </div>

    <!--状态-->
    <div class="layui-form-item" id="pay_use">
        <label class="layui-form-label" >状态：</label>
        <div class="layui-input-block">
            <input type="radio" name="status" value=1 title="启用"{if condition="$info.status eq 1" }checked{/if}>
            <input type="radio" name="status" value=0 title="关闭" {if condition="$info.status eq 0" }checked{/if}>
        </div>
    </div>

    <div class="layui-form-item layui-hide">
        <input type="button" lay-submit lay-filter="edit-submit" id="edit-submit" value="确认">
    </div>
</div>

<script>
    layui.use(['form'], function(){
        var $ = layui.$
            ,form = layui.form;

        like.delUpload();
        $(document).on("click", ".add-upload-image", function () {
            like.imageUpload({
                limit: 1,
                field: "image",
                that: $(this)
            });
        });

        form.verify({
            check_required: function (value, item) {
                var status = $('input[name="status"]:checked').val();
                value = value.trim();
                if (status == 1 && value.length == 0) {
                    return '请填写完整';
                }
            }
        });

    })
</script>