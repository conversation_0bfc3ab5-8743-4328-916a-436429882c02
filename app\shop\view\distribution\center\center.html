{layout name="layout1" /}
<style>
    .title {
        font-size: 16px;
        text-align: center;
    }
    .data {
        margin-top: 10px;
        font-size: 20px;
        text-align: center;
    }
    .layui-table {
        width: 500px;
        margin-left: 10px;
    }
    html,
    body {
        height: 100%;
    }
    #content {
        height: 100%;
        background: #FFFFFF;
    }
</style>
<div class="wrapper">
    <div class="layui-card">
        <div class="layui-card-body">
            <div id="content">
    <fieldset class="layui-elem-field layui-field-title">
        <legend>佣金概览</legend>
        <div class="layui-field-box">
            <div class="layui-row">
                <div class="layui-col-md2 title">
                    累计已入账佣金/元
                </div>
                <div class="layui-col-md2 title">
                    今日入账佣金/元
                </div>
                <div class="layui-col-md2 title">
                    待结算佣金/元
                </div>
                <div class="layui-col-md2 title">
                    今日新增待结算佣金/元
                </div>
            </div>
            <div class="layui-row">
                <div class="layui-col-md2 data">
                    {$data.earnings.total_success}
                </div>
                <div class="layui-col-md2 data">
                    {$data.earnings.total_today_success}
                </div>
                <div class="layui-col-md2 data">
                    {$data.earnings.total_wait}
                </div>
                <div class="layui-col-md2 data">
                    {$data.earnings.total_today_wait}
                </div>
            </div>
        </div>
    </fieldset>
    <fieldset class="layui-elem-field layui-field-title">
        <legend>分销商品排行榜</legend>
        <div class="layui-field-box">
            <table class="layui-table">
                <colgroup>
                    <col width="150">
                    <col width="200">
                    <col>
                </colgroup>
                <thead>
                <tr>
                    <th>排名</th>
                    <th>商品名称</th>
                    <th>已入账佣金</th>
                </tr>
                </thead>
                <tbody>
                {foreach $data.top as $key=>$item }
                <tr>
                    <td>{$key+1}</td>
                    <td> <img src="{$item.goods_image}" style="height:60px;width: 60px;margin-right:5px;" class="image-show">{$item.goods_name}</td>
                    <td>{$item.total_money}</td>
                </tr>
                {/foreach}
                </tbody>
            </table>
        </div>
    </fieldset>
</div>
        </div>
    </div>
</div>
<script>
    layui.config({
        version:"{$front_version}",
        base: '/static/lib/' //静态资源所在路径
    }).use(['table'], function(){
        let table = layui.table;
    });
</script>