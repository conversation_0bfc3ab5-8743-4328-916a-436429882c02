<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFrameworks>netstandard2.0;net461</TargetFrameworks>
    <PackOnBuild>true</PackOnBuild>
    <PackageVersion>2.1.2</PackageVersion>
    <Authors>antopen</Authors>
    <NeutralLanguage>zh</NeutralLanguage>
    <PackageLicenseUrl>https://github.com/alipay/alipay-easysdk/blob/master/LICENSE</PackageLicenseUrl>
    <Description>Alipay Easy SDK for .NET allows you to enjoy a minimalist programming experience and quickly access the various high-frequency capabilities of the Alipay Open Platform.</Description>
    <PackageId>AlipayEasySDK</PackageId>
    <Owners>antopen</Owners>
    <PackageProjectUrl>https://github.com/alipay/alipay-easysdk/tree/master/csharp</PackageProjectUrl>
    <Summary>Alipay Easy SDK for .NET allows you to enjoy a minimalist programming experience and quickly access the various high-frequency capabilities of the Alipay Open Platform.</Summary>
    <Title>Alipay Easy SDK</Title>
  </PropertyGroup>

  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType></DebugType>
  </PropertyGroup>
  <ItemGroup>
    <Compile Remove="Class1.cs" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Factory\" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="AlipayEasySDK.Kernel" Version="1.0.5" />
  </ItemGroup>
</Project>
