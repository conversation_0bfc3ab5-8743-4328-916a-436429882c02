{layout name="layout2" /}
<style>
    .layui-form-label{
        width: 90px;
    }
    .reqRed::before {
        content: '*';
        color: red;
        margin-right: 5px;
    }
</style>
<div class="layui-form" lay-filter="adjust" id="layuiadmin-form-user" style="padding: 20px 30px 0 0;">
    <input type="hidden" value="{$info.id}" name="id">
    <input type="hidden" value="money" name="type" id="type">
    <div class="layui-fluid">
        <div class="layui-card">
            <div class="layui-tab layui-tab-card"  lay-filter="adjust">
                <ul class="layui-tab-title">
                    <li data-type="money" class="layui-this">保证金调整</li>

                </ul>
                <div class="layui-tab-content" >

                    <div class="layui-tab-item  layui-show " >
                        <div class="layui-form-item">
                            <label class="layui-form-label">当前余额：</label>
                            <div class="layui-input-inline">
                                <label class="layui-form-mid">{$info.user_money}</label>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label reqRed">余额增减：</label>
                            <div class="layui-input-block">
                                <input type="radio" name="money_handle" value="1" title="增加余额">
                                <input type="radio" name="money_handle" value="0" title="扣减余额">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label reqRed">调整余额：</label>
                            <div class="layui-input-inline">
                                <input type="number" min="0" name="money" value="" lay-verify="" lay-vertype="tips" placeholder="" autocomplete="off" class="layui-input">
                                <label class="layui-form-mid layui-word-aux">请输入调整的余额金额</label>
                            </div>
                            <label class="layui-form-mid">元</label>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label reqRed">备注：</label>
                            <div class="layui-input-block">
                                <textarea type="text" name="money_remark" autocomplete="off" class="layui-textarea" style="width: 30%;"></textarea>
                                <label class="layui-form-mid layui-word-aux" style="margin-left: 10px;">不超过100字</label>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="layui-form-item layui-hide">
        <input type="button" lay-submit lay-filter="adjust_user-submit" id="adjust_user-submit" value="确认">
    </div>
</div>
<script>
    layui.config({
        version:"{$front_version}",
        base: '/static/lib/' //静态资源所在路径
    }).use(['form', 'element'], function(){
        var $ = layui.$,form = layui.form ;
        var element = layui.element;

        element.on('tab(adjust)', function(){
            $('#type').val($(this).attr('data-type'));
        });
    })
</script>
