<?php
namespace app\admin\controller\wechat;

use app\common\basics\AdminBase;
use app\common\server\JsonServer;
use app\common\model\wechat\WechatReply;
use app\api\logic\WechatLogic;
use app\common\server\WeChatServer;
use EasyWeChat\Factory;
use EasyWeChat\Kernel\Messages\Image;
use EasyWeChat\Kernel\Messages\Text;

class Debug extends AdminBase
{
    /**
     * 调试进采购群功能
     */
    public function index()
    {
        if ($this->request->isAjax()) {
            $action = $this->request->post('action');

            switch ($action) {
                case 'test_upload':
                    return $this->testUpload();
                case 'test_send':
                    return $this->testSend();
                case 'check_config':
                    return $this->checkConfig();
                default:
                    return JsonServer::error('未知操作');
            }
        }

        return view('debug');
    }

    /**
     * 测试图片上传
     */
    private function testUpload()
    {
        try {
            // 获取进采购群配置
            $reply = WechatReply::where([
                'keyword' => '进采购群',
                'del' => 0
            ])->find();

            if (!$reply || empty($reply['second_image_url'])) {
                return JsonServer::error('未找到图片配置');
            }

            $image_url = $reply['second_image_url'];
            $image_path = app()->getRootPath() . 'public' . $image_url;

            if (!file_exists($image_path)) {
                return JsonServer::error('图片文件不存在: ' . $image_path);
            }

            // 测试上传到微信
            $config = WeChatServer::getOaConfig();
            $app = Factory::officialAccount($config);

            $result = $app->media->upload('image', $image_path);

            return JsonServer::success('上传成功', [
                'result' => $result,
                'image_path' => $image_path,
                'file_size' => filesize($image_path)
            ]);

        } catch (\Exception $e) {
            return JsonServer::error('上传失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 测试发送消息
     */
    private function testSend()
    {
        try {
            $openid = $this->request->post('openid');
            if (empty($openid)) {
                return JsonServer::error('请输入openid');
            }

            $config = WeChatServer::getOaConfig();
            $app = Factory::officialAccount($config);

            // 获取进采购群配置
            $reply = WechatReply::where([
                'keyword' => '进采购群',
                'del' => 0
            ])->find();

            if (!$reply) {
                return JsonServer::error('未找到回复配置');
            }

            $results = [];

            // 发送第一条文本消息
            if (!empty($reply['content'])) {
                $textResult = $app->customer_service->message(new Text($reply['content']))->to($openid)->send();
                $results['text'] = $textResult;
            }

            // 发送第二条图片消息
            if (!empty($reply['second_image_url'])) {
                $image_path = app()->getRootPath() . 'public' . $reply['second_image_url'];

                if (file_exists($image_path)) {
                    // 上传图片获取media_id
                    $uploadResult = $app->media->upload('image', $image_path);
                    $results['upload'] = $uploadResult;

                    if (isset($uploadResult['media_id'])) {
                        // 发送图片消息
                        $imageResult = $app->customer_service->message(new Image($uploadResult['media_id']))->to($openid)->send();
                        $results['image'] = $imageResult;
                    }
                } else {
                    $results['error'] = '图片文件不存在: ' . $image_path;
                }
            }

            return JsonServer::success('发送完成', $results);

        } catch (\Exception $e) {
            return JsonServer::error('发送失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 检查配置
     */
    private function checkConfig()
    {
        try {
            $config = WeChatServer::getOaConfig();
            $app = Factory::officialAccount($config);

            // 检查access_token
            $token = $app->access_token->getToken();

            // 检查回复配置
            $reply = WechatReply::where([
                'keyword' => '进采购群',
                'del' => 0
            ])->find();

            $data = [
                'wechat_config' => [
                    'app_id' => $config['app_id'] ?? '',
                    'secret' => !empty($config['secret']) ? '已配置' : '未配置',
                    'token' => $config['token'] ?? '',
                    'access_token' => $token['access_token'] ?? ''
                ],
                'reply_config' => $reply ? $reply->toArray() : null,
                'image_check' => null
            ];

            // 检查图片文件
            if ($reply && !empty($reply['second_image_url'])) {
                $image_path = app()->getRootPath() . 'public' . $reply['second_image_url'];
                $data['image_check'] = [
                    'path' => $image_path,
                    'exists' => file_exists($image_path),
                    'size' => file_exists($image_path) ? filesize($image_path) : 0
                ];
            }

            return JsonServer::success('检查完成', $data);

        } catch (\Exception $e) {
            return JsonServer::error('检查失败: ' . $e->getMessage());
        }
    }
}
