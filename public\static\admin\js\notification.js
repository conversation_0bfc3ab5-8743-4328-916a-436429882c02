/**
 * 管理员通知工具
 * 用于显示通知和处理WebSocket消息
 */

// 通知声音URL
var notificationSoundUrl = 'https://jcstatics.jiaqingfu.com.cnuploads/audio/tomsg.mp3';

// 调试模式
var debugMode = true;

// 全局变量
var wsDebugInfo = {
    url: '',
    adminId: '',
    nickname: '',
    token: '',
    connectionAttempts: 0,
    lastConnectionTime: new Date(),
    heartbeats: 0,
    messages: []
};

/**
 * 显示通知
 * @param {string} title 通知标题
 * @param {string} content 通知内容
 * @param {string} type 通知类型
 * @param {string} url 跳转URL
 * @param {number} icon 通知图标
 * @returns {number} 通知层索引
 */
function showNotification(title, content, type, url, icon) {
    if (debugMode) {
        console.log('%c显示通知:', 'color: green; font-weight: bold', {
            title: title,
            content: content,
            type: type,
            url: url,
            icon: icon
        });
    }

    // 确保layer已加载
    if (typeof layer === 'undefined') {
        console.error('LayUI layer未加载，无法显示通知');
        alert('通知: ' + title + '\n' + content);
        return;
    }

    // 根据通知类型设置不同的样式
    var notificationSkin = 'layui-layer-molv'; // 默认墨绿皮肤
    var notificationIcon = icon || 0; // 使用传入的图标或默认图标

    switch (type) {
        case 'admin_notification':
            notificationSkin = 'layui-layer-molv'; // 墨绿皮肤
            break;
        case 'system':
        case 'system_notification':
            notificationSkin = 'layui-layer-lan'; // 蓝色皮肤
            notificationIcon = 1;
            break;
        case 'personal':
        case 'custom_notification':
            notificationSkin = 'layui-layer-rim'; // 简约风格
            break;
        case 'error_notification':
            notificationSkin = 'layui-layer-red'; // 红色皮肤
            notificationIcon = 2;
            break;
        default:
            // 使用默认样式
    }

    try {
        // 先尝试使用简单的消息提示，确保layer功能正常
        layer.msg('收到新通知: ' + title, {icon: notificationIcon, time: 3000});

        // 显示 LayUI 通知
        var layerIndex = layer.open({
            type: 1,
            title: title,
            content: '<div style="padding: 20px; line-height: 1.8;">' + content.replace(/\n/g, '<br>') + '</div>',
            shade: 0,
            offset: 'rb', // 右下角
            anim: 2,      // 从最底部往上滑入
            time: 15000,  // 15秒后自动关闭
            area: ['350px', 'auto'], // 固定宽度，高度自适应
            skin: notificationSkin,
            btn: url ? ['查看详情', '关闭'] : ['关闭'],
            yes: function(index) {
                if (url) {
                    // 如果有URL，点击"查看详情"按钮时跳转
                    window.open(url, '_blank');
                }
                layer.close(index);
            },
            btn2: function(index) {
                layer.close(index);
                return false;
            },
            success: function(layero, index) {
                if (debugMode) {
                    console.log('通知显示成功，层索引:', index);
                }
                // 可以在这里添加点击事件等
                $(layero).find('.layui-layer-content').css('max-height', '300px').css('overflow-y', 'auto');
            },
            cancel: function() {
                if (debugMode) {
                    console.log('用户关闭了通知');
                }
            }
        });

        if (debugMode) {
            console.log('通知层索引:', layerIndex);
        }

        // 播放提示音（静音模式，避免错误）
        playNotificationSound(true).catch(function(error) {
            // 忽略错误
            if (debugMode) {
                console.log('忽略播放声音错误:', error);
            }
        });

        return layerIndex;
    } catch (error) {
        console.error('显示通知时出错:', error);
        // 如果layer弹窗失败，尝试使用原生alert
        alert('通知: ' + title + '\n' + content);
    }
}

/**
 * 播放通知声音
 * @param {boolean} silent 是否静音播放（不抛出错误）
 * @returns {Promise} 播放结果的Promise
 */
function playNotificationSound(silent) {
    // 如果用户设置了不播放声音，直接返回
    if (window.disableNotificationSound) {
        if (debugMode) {
            console.log('通知声音已被用户禁用');
        }
        return Promise.resolve();
    }

    return new Promise(function(resolve, reject) {
        try {
            // 检查是否支持Audio
            if (typeof Audio === 'undefined') {
                if (!silent) {
                    console.warn('浏览器不支持Audio API');
                }
                return resolve();
            }

            var audio = new Audio(notificationSoundUrl);
            audio.volume = 0.7; // 设置音量为70%

            // 设置静音（避免自动播放策略限制）
            if (window.muteNotificationSound) {
                audio.muted = true;
            }

            var playPromise = audio.play();

            if (playPromise !== undefined) {
                playPromise.then(function() {
                    if (debugMode) {
                        console.log('提示音播放成功' + (window.muteNotificationSound ? '（静音模式）' : ''));
                    }
                    resolve();
                }).catch(function(error) {
                    if (!silent) {
                        console.error('播放提示音失败:', error);
                        // 浏览器可能因自动播放策略阻止播放声音
                        console.warn('提示音播放失败，可能需要用户交互才能播放声音');

                        // 如果是自动播放策略问题，设置静音标志
                        if (error.name === 'NotAllowedError') {
                            window.muteNotificationSound = true;
                            console.log('已设置静音模式，下次通知将静音播放');

                            // 显示提示
                            if (typeof layer !== 'undefined') {
                                layer.msg('通知声音已静音，点击页面任意位置启用声音', {
                                    icon: 0,
                                    time: 5000
                                });
                            }
                        }
                    }
                    reject(error);
                });
            } else {
                if (debugMode) {
                    console.log('浏览器不支持音频播放Promise');
                }
                resolve();
            }
        } catch (audioError) {
            if (!silent) {
                console.error('创建或播放音频对象时出错:', audioError);
            }
            reject(audioError);
        }
    });
}

/**
 * 启用通知声音
 */
function enableNotificationSound() {
    window.disableNotificationSound = false;
    window.muteNotificationSound = false;

    // 尝试播放一个测试声音
    playNotificationSound(true).then(function() {
        console.log('通知声音已启用');
        if (typeof layer !== 'undefined') {
            // layer.msg('通知声音已启用', {icon: 1, time: 2000});
        }
    }).catch(function() {
        window.muteNotificationSound = true;
        console.log('通知声音已启用（静音模式）');
        if (typeof layer !== 'undefined') {
            // layer.msg('通知声音已启用（静音模式）', {icon: 0, time: 2000});
        }
    });
}

/**
 * 禁用通知声音
 */
function disableNotificationSound() {
    window.disableNotificationSound = true;
    console.log('通知声音已禁用');
    if (typeof layer !== 'undefined') {
        layer.msg('通知声音已禁用', {icon: 0, time: 2000});
    }
}

/**
 * 发送测试通知
 * @param {string} title 通知标题
 * @param {string} content 通知内容
 * @param {string} type 通知类型
 * @param {string} url 跳转URL
 * @param {number} icon 通知图标
 */
function sendTestNotification(title, content, type, url, icon) {
    title = title || '测试通知';
    content = content || '这是一条测试通知，请查收！';
    type = type || 'admin_notification';
    url = url || '';
    icon = icon || 0;

    if (debugMode) {
        console.log('%c发送测试通知:', 'color: blue; font-weight: bold', {
            title: title,
            content: content,
            type: type,
            url: url,
            icon: icon
        });
    }

    // 发送GET请求到测试通知API
    $.get('/api/notification/testNotification', {
        title: title,
        content: content,
        type: type,
        url: url,
        icon: icon
    }, function(response) {
        if (response.code === 1) {
            if (debugMode) {
                console.log('%c测试通知发送成功:', 'color: green; font-weight: bold', response);
            }
            layer.msg('测试通知发送成功', {icon: 1, time: 2000});
        } else {
            console.error('测试通知发送失败:', response);
            layer.msg('测试通知发送失败: ' + response.msg, {icon: 2, time: 3000});
        }
    }).fail(function(xhr, status, error) {
        console.error('测试通知请求失败:', error);
        layer.msg('测试通知请求失败: ' + error, {icon: 2, time: 3000});
    });
}

/**
 * 发送WebSocket通知
 * @param {string} title 通知标题
 * @param {string} content 通知内容
 * @param {string} type 通知类型
 * @param {string} url 跳转URL
 * @param {number} icon 通知图标
 */
function sendWebSocketNotification(title, content, type, url, icon) {
    title = title || '测试通知';
    content = content || '这是一条测试通知，请查收！';
    type = type || 'admin_notification';
    url = url || '';
    icon = icon || 0;

    if (debugMode) {
        console.log('%c发送WebSocket通知:', 'color: blue; font-weight: bold', {
            title: title,
            content: content,
            type: type,
            url: url,
            icon: icon
        });
    }

    // 发送POST请求到WebSocket API
    $.ajax({
        url: '/api/websocket/sendAdminNotification',
        type: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            title: title,
            content: content,
            type: type,
            url: url,
            icon: icon
        }),
        success: function(response) {
            if (response.code === 1) {
                if (debugMode) {
                    console.log('%cWebSocket通知发送成功:', 'color: green; font-weight: bold', response);
                }
                layer.msg('WebSocket通知发送成功', {icon: 1, time: 2000});
            } else {
                console.error('WebSocket通知发送失败:', response);
                layer.msg('WebSocket通知发送失败: ' + response.msg, {icon: 2, time: 3000});
            }
        },
        error: function(xhr, status, error) {
            console.error('WebSocket通知请求失败:', error);
            layer.msg('WebSocket通知请求失败: ' + error, {icon: 2, time: 3000});
        }
    });
}

// 导出函数
window.showNotification = showNotification;
window.playNotificationSound = playNotificationSound;
window.sendTestNotification = sendTestNotification;
window.sendWebSocketNotification = sendWebSocketNotification;
window.wsDebugInfo = wsDebugInfo;
