{extend name="layout1" /}

{block name="content"}
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-header">WebSocket通知测试</div>
        <div class="layui-card-body">
            <form class="layui-form" action="" lay-filter="notification-form">
                <div class="layui-form-item">
                    <label class="layui-form-label">通知标题</label>
                    <div class="layui-input-block">
                        <input type="text" name="title" required lay-verify="required" placeholder="请输入通知标题" autocomplete="off" class="layui-input" value="测试通知">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">通知内容</label>
                    <div class="layui-input-block">
                        <textarea name="content" placeholder="请输入通知内容" class="layui-textarea" required lay-verify="required">这是一条测试通知，用于测试WebSocket通知功能。</textarea>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">通知类型</label>
                    <div class="layui-input-block">
                        <select name="type" lay-verify="required">
                            <option value="admin_notification">管理员通知</option>
                            <option value="system_notification">系统通知</option>
                            <option value="custom_notification">自定义通知</option>
                            <option value="error_notification">错误通知</option>
                        </select>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">跳转URL</label>
                    <div class="layui-input-block">
                        <input type="text" name="url" placeholder="点击通知跳转的URL（可选）" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">通知图标</label>
                    <div class="layui-input-block">
                        <select name="icon">
                            <option value="0">默认图标</option>
                            <option value="1">成功图标</option>
                            <option value="2">错误图标</option>
                            <option value="3">警告图标</option>
                            <option value="4">信息图标</option>
                        </select>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-input-block">
                        <button class="layui-btn" lay-submit lay-filter="notification-submit">发送通知</button>
                        <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                    </div>
                </div>
            </form>
            
            <div class="layui-card">
                <div class="layui-card-header">WebSocket状态</div>
                <div class="layui-card-body">
                    <div class="layui-form-item">
                        <label class="layui-form-label">连接状态</label>
                        <div class="layui-input-block">
                            <span id="ws-status" class="layui-badge layui-bg-gray">未连接</span>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">WebSocket URL</label>
                        <div class="layui-input-block">
                            <span id="ws-url">-</span>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <div class="layui-input-block">
                            <button type="button" class="layui-btn layui-btn-sm" id="test-connection">测试连接</button>
                            <button type="button" class="layui-btn layui-btn-sm layui-btn-primary" id="show-config">显示配置</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
layui.use(['form', 'layer', 'jquery'], function(){
    var form = layui.form;
    var layer = layui.layer;
    var $ = layui.jquery;
    
    // 监听表单提交
    form.on('submit(notification-submit)', function(data){
        var field = data.field;
        
        // 发送通知
        $.ajax({
            url: '{:url("api/notification/sendToAdmin")}',
            type: 'POST',
            data: field,
            dataType: 'json',
            success: function(res){
                if(res.code === 1){
                    layer.msg(res.msg, {icon: 1});
                } else {
                    layer.msg(res.msg, {icon: 2});
                }
            },
            error: function(xhr, status, error){
                layer.msg('发送失败: ' + error, {icon: 2});
            }
        });
        
        return false; // 阻止表单跳转
    });
    
    // 测试连接按钮
    $('#test-connection').on('click', function(){
        // 获取WebSocket配置
        $.ajax({
            url: '{:url("api/notification/websocketConfig")}',
            type: 'GET',
            dataType: 'json',
            success: function(res){
                if(res.code === 1){
                    var config = res.data;
                    var wsUrl = config.websocket_url;
                    
                    // 显示WebSocket URL
                    $('#ws-url').text(wsUrl);
                    
                    // 测试WebSocket连接
                    try {
                        var testSocket = new WebSocket(wsUrl);
                        
                        // 更新状态为连接中
                        $('#ws-status').removeClass('layui-bg-gray layui-bg-green layui-bg-red')
                            .addClass('layui-bg-blue')
                            .text('连接中...');
                        
                        testSocket.onopen = function(){
                            // 更新状态为已连接
                            $('#ws-status').removeClass('layui-bg-gray layui-bg-blue layui-bg-red')
                                .addClass('layui-bg-green')
                                .text('已连接');
                            
                            // 3秒后关闭测试连接
                            setTimeout(function(){
                                testSocket.close();
                            }, 3000);
                        };
                        
                        testSocket.onerror = function(){
                            // 更新状态为连接失败
                            $('#ws-status').removeClass('layui-bg-gray layui-bg-blue layui-bg-green')
                                .addClass('layui-bg-red')
                                .text('连接失败');
                        };
                        
                        testSocket.onclose = function(){
                            // 更新状态为已断开
                            $('#ws-status').removeClass('layui-bg-blue layui-bg-green')
                                .addClass('layui-bg-gray')
                                .text('已断开');
                        };
                    } catch(e) {
                        // 更新状态为连接失败
                        $('#ws-status').removeClass('layui-bg-gray layui-bg-blue layui-bg-green')
                            .addClass('layui-bg-red')
                            .text('连接失败: ' + e.message);
                    }
                } else {
                    layer.msg(res.msg, {icon: 2});
                }
            },
            error: function(xhr, status, error){
                layer.msg('获取WebSocket配置失败: ' + error, {icon: 2});
            }
        });
    });
    
    // 显示配置按钮
    $('#show-config').on('click', function(){
        // 获取WebSocket配置
        $.ajax({
            url: '{:url("api/notification/websocketConfig")}',
            type: 'GET',
            dataType: 'json',
            success: function(res){
                if(res.code === 1){
                    var config = res.data;
                    
                    // 格式化配置信息
                    var configHtml = '<div style="padding: 20px;">';
                    configHtml += '<p><strong>WebSocket URL:</strong> ' + config.websocket_url + '</p>';
                    configHtml += '<p><strong>WSS URL:</strong> ' + (config.wss_url || '未配置') + '</p>';
                    configHtml += '<p><strong>强制使用WS协议:</strong> ' + (config.force_ws ? '是' : '否') + '</p>';
                    configHtml += '<p><strong>允许跨域连接:</strong> ' + (config.allow_cross_domain ? '是' : '否') + '</p>';
                    configHtml += '<p><strong>心跳间隔:</strong> ' + config.heartbeat_interval + ' 秒</p>';
                    configHtml += '<p><strong>重连间隔:</strong> ' + config.reconnect_interval + ' 秒</p>';
                    configHtml += '<p><strong>最大重连次数:</strong> ' + config.max_reconnect_attempts + '</p>';
                    configHtml += '<p><strong>调试模式:</strong> ' + (config.debug ? '开启' : '关闭') + '</p>';
                    configHtml += '<p><strong>当前页面协议:</strong> ' + window.location.protocol + '</p>';
                    configHtml += '</div>';
                    
                    // 显示配置信息
                    layer.open({
                        type: 1,
                        title: 'WebSocket配置信息',
                        content: configHtml,
                        area: ['500px', 'auto']
                    });
                } else {
                    layer.msg(res.msg, {icon: 2});
                }
            },
            error: function(xhr, status, error){
                layer.msg('获取WebSocket配置失败: ' + error, {icon: 2});
            }
        });
    });
});
</script>
{/block}
