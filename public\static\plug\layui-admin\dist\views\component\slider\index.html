

<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>滑块组件</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
  <link rel="stylesheet" href="../../../layuiadmin/layui/css/layui.css" media="all">
  <link rel="stylesheet" href="../../../layuiadmin/style/admin.css" media="all">
</head>
<body>

  <div class="layui-card layadmin-header">
    <div class="layui-breadcrumb" lay-filter="breadcrumb">
      <a lay-href="">主页</a>
      <a><cite>组件</cite></a>
      <a><cite>滑块组件</cite></a>
    </div>
  </div>
  
  <style>
  .test-slider-demo{margin: 45px 30px;}
  </style>

  <div class="layui-fluid">
    <div class="layui-row layui-col-space15">
      <div class="layui-col-md6">
        <div class="layui-card">
          <div class="layui-card-header">基础效果</div>
          <div class="layui-card-body">
            <div id="test-slider-dome1" class="test-slider-demo"></div>
          </div>
        </div>
        <div class="layui-card">
          <div class="layui-card-header">定义初始值</div>
          <div class="layui-card-body">
            <div id="test-slider-dome1" class="test-slider-demo"></div>
          </div>
        </div>
        <div class="layui-card">
          <div class="layui-card-header">设置最大最小值</div>
          <div class="layui-card-body">
            <div id="test-slider-dome3" class="test-slider-demo"></div>
          </div>
        </div>
        <div class="layui-card">
          <div class="layui-card-header">设置步长</div>
          <div class="layui-card-body">
            <div id="test-slider-dome4" class="test-slider-demo"></div>
            <div id="test-slider-dome5" class="test-slider-demo"></div>
          </div>
        </div>
        <div class="layui-card">
          <div class="layui-card-header">设置提示文本</div>
          <div class="layui-card-body">
            <div id="test-slider-dome6" class="test-slider-demo"></div>
            <div id="test-slider-dome7" class="test-slider-demo"></div>
            <div id="test-slider-tips1" style="position:relative; left: 30px; top: -20px;"></div>
          </div>
        </div>
        <div class="layui-card">
          <div class="layui-card-header">开启输入框</div>
          <div class="layui-card-body">
            <div id="test-slider-dome8" class="test-slider-demo"></div>
          </div>
        </div>
      </div>
      <div class="layui-col-md6">
        <div class="layui-card">
          <div class="layui-card-header">开启范围选择</div>
          <div class="layui-card-body">
            <div id="test-slider-dome9" class="test-slider-demo"></div>
            <div id="test-slider-tips2" style="position:relative; left: 30px; margin-top: -10px;"></div>
            <div id="test-slider-dome10" class="test-slider-demo"></div>
          </div>
        </div>
        <div class="layui-card">
          <div class="layui-card-header">垂直滑块</div>
          <div class="layui-card-body">
            <div id="test-slider-dome11" style="margin: 45px 30px; display: inline-block;"></div>
            <div id="test-slider-dome12" style="margin: 45px 30px; display: inline-block;"></div>
            <div id="test-slider-dome13" style="margin: 45px 30px; display: inline-block;"></div>
            <div id="test-slider-dome14" style="margin: 45px 30px; display: inline-block;"></div>
          </div>
        </div>
        <div class="layui-card">
          <div class="layui-card-header">自定义颜色</div>
          <div class="layui-card-body">
            <div id="test-slider-dome15" class="test-slider-demo"></div>
            <div id="test-slider-dome16" class="test-slider-demo"></div>
            <div id="test-slider-dome17" class="test-slider-demo"></div>
          </div>
        </div>
        <div class="layui-card">
          <div class="layui-card-header">禁用滑块</div>
          <div class="layui-card-body">
            <div id="test-slider-dome18" class="test-slider-demo"></div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script src="../../../layuiadmin/layui/layui.js"></script>  
  <script>
  layui.config({
    base: '../../../layuiadmin/' //静态资源所在路径
  }).extend({
    index: 'lib/index' //主入口模块
  }).use(['index', 'slider'], function(){
    var $ = layui.$
    ,slider = layui.slider;
  
    //默认滑块
    slider.render({
      elem: '#test-slider-dome1'
    });
    
    //定义初始值
    slider.render({
      elem: '#test-slider-dome2'
      ,value: 20 //初始值
    });
    
    //设置最大最小值
    slider.render({
      elem: '#test-slider-dome3'
      ,min: 20 //最小值
      ,max: 50 //最大值
    });
    
    //设置步长
    slider.render({
      elem: '#test-slider-dome4'
      ,step: 10 //步长
    });
    
    slider.render({
      elem: '#test-slider-dome5'
      ,step: 10 //步长
      ,showstep: true //开启间隔点
    });
    
    //设置提示文本
    slider.render({
      elem: '#test-slider-dome6'
      ,min: 20
      ,max: 1000
      ,setTips: function(value){ //自定义提示文本
       return value + 'GB';
      }
    });
    slider.render({
      elem: '#test-slider-dome7'
      ,tips: false //关闭默认提示层
      ,change: function(value){
        $('#test-slider-tips1').html('当前数值：'+ value);
      }
    });
    
    //开启输入框
    slider.render({
      elem: '#test-slider-dome8'
      ,input: true //输入框
    });
    
    //开启范围选择
    slider.render({
      elem: '#test-slider-dome9'
      ,value: 40 //初始值
      ,range: true //范围选择
      ,change: function(vals){
        $('#test-slider-tips2').html('开始值：'+ vals[0] + '、结尾值：'+ vals[1]);
      }
    });
    slider.render({
      elem: '#test-slider-dome10'
      ,value: [30, 60] //初始值
      ,range: true //范围选择
    });
    
    //垂直滑块
    slider.render({
      elem: '#test-slider-dome11'
      ,type: 'vertical' //垂直滑块
    });
    slider.render({
      elem: '#test-slider-dome12'
      ,value: 30
      ,type: 'vertical' //垂直滑块
    });
    slider.render({
      elem: '#test-slider-dome13'
      ,value: 50
      ,range: true //范围选择
      ,type: 'vertical' //垂直滑块
    });
    slider.render({
      elem: '#test-slider-dome14'
      ,value: 80
      ,input: true //输入框
      ,type: 'vertical' //垂直滑块
    });
    
    //自定义颜色
    slider.render({
      elem: '#test-slider-dome15'
      ,theme: '#1E9FFF' //主题色
    });
    slider.render({
      elem: '#test-slider-dome16'
      ,value: 50
      ,theme: '#5FB878' //主题色
    });
    slider.render({
      elem: '#test-slider-dome17'
      ,value: [30, 70]
      ,range: true
      ,theme: '#FF5722' //主题色
    });
    
    //禁用滑块
    slider.render({
      elem: '#test-slider-dome18'
      ,value: 35
      ,disabled: true //禁用滑块
    });
  });
  </script>
</body>
</html>