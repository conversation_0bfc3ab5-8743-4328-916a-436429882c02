<?php

declare(strict_types=1);

namespace app\common\server;

use think\facade\Db;
use think\facade\Log;
use AlibabaCloud\SDK\Alinlp\V20200629\Alinlp;
use AlibabaCloud\SDK\Alinlp\V20200629\Models\GetWsChGeneralRequest;
use Darabonba\OpenApi\Models\Config;

class WordUpdateServer
{

    // API请求超时时间
    protected $timeout = 5;

    /**
     * 启动分词更新服务
     */
    public function start()
    {
        while (true) {
            try {
                // 获取一个待分词的商品
                $goods = Db::name('goods')
                    ->where('del', 0)
                    ->where('status', 1)
                    ->where('is_analyzed', 0)
                    ->order('id', 'asc')
                    ->find();

                if ($goods) {
                    $this->processGoods($goods);
                }

                // 每秒处理一个商品
                sleep(1);
            } catch (\Exception $e) {
                Log::error('Word update error: ' . $e->getMessage());
                sleep(5); // 出错时等待5秒再重试
            }
        }
    }

    /**
     * 处理单个商品
     * @param array $goods
     */
    protected function processGoods(array $goods)
    {
        Db::startTrans();
        try {
            // 调用分词API
            $keywords = $this->callSplitApi($goods['name']);

            // 保存分词结果
            $this->saveKeywords($goods['id'], $keywords);

            // 标记为已分词
            Db::name('goods')
                ->where('id', $goods['id'])
                ->update(['is_analyzed' => 1]);

            Db::commit();
            Log::info("Processed goods: {$goods['id']} - {$goods['name']}");
        } catch (\Exception $e) {
            Db::rollback();
            Log::error("Error processing goods {$goods['id']}: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * 调用分词API
     * @param string $text
     * @return array
     */
    protected function callSplitApi(string $text): array
    {
        $config                  = new Config();
        $config->accessKeyId     = 'LTAI5tPpqw9JoKAZKrY4k14Q';
        $config->accessKeySecret = '******************************';
        $config->TokenizerId        = "GENERAL_CHN";
        $config->regionId        = "cn-hangzhou";
        $config->Action        = "GetWsChGeneral";
        $client                  = new Alinlp($config);
        $request                 = new GetWsChGeneralRequest();
        $request->serviceCode    = 'alinlp';
        $request->OutType    = 1;
        $request->text           = $text;
        try {
            $response = $client->getNerChEcom($request);
            $json_string = json_encode($response->body, JSON_UNESCAPED_UNICODE);
            $data=json_decode($json_string,true);
            $keywords=json_decode($data['data'],true);
            return $keywords['result'];
        } catch (TeaUnableRetryError $e) {
            return [];
        }
    }

    /**
     * 保存分词结果
     * @param int $goodsId
     * @param array $keywords
     */
    protected function saveKeywords(int $goodsId, array $keywords)
    {
        foreach ($keywords as $keyword) {
            // 保存到word表
            $wordId = Db::name('word')
                ->insertGetId([
                    'word' => $keyword['word'],
                    'tags' => implode(',', $keyword['tags']),
                    'created_at' => date('Y-m-d H:i:s')
                ]);

            // 保存关联关系到product_word表
            Db::name('product_word')->insert([
                'goods_id' => $goodsId,
                'word_id' => $wordId,
                'created_at' => date('Y-m-d H:i:s')
            ]);
        }
    }
}