/* 全局样式重置和基础设置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* 管理端样式 */
body.admin-login {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, 'Helvetica Neue', <PERSON><PERSON>, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    overflow-x: hidden;
}

/* 商家端样式 */
body.shop-login {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    overflow-x: hidden;
}

/* 背景动态效果容器 */
.background-effects {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
    overflow: hidden;
}

/* 增强版粒子动画 */
.particles-container {
    position: absolute;
    width: 100%;
    height: 100%;
}

.particle {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(1px);
}

.particle-1 {
    width: 4px;
    height: 4px;
    top: 20%;
    left: 10%;
    animation: float-1 8s ease-in-out infinite;
}

.particle-2 {
    width: 6px;
    height: 6px;
    top: 60%;
    left: 80%;
    animation: float-2 10s ease-in-out infinite;
}

.particle-3 {
    width: 3px;
    height: 3px;
    top: 80%;
    left: 20%;
    animation: float-3 12s ease-in-out infinite;
}

.particle-4 {
    width: 5px;
    height: 5px;
    top: 10%;
    left: 70%;
    animation: float-4 9s ease-in-out infinite;
}

.particle-5 {
    width: 2px;
    height: 2px;
    top: 40%;
    left: 5%;
    animation: float-5 11s ease-in-out infinite;
}

.particle-6 {
    width: 4px;
    height: 4px;
    top: 70%;
    left: 60%;
    animation: float-6 7s ease-in-out infinite;
}

.particle-7 {
    width: 3px;
    height: 3px;
    top: 30%;
    left: 90%;
    animation: float-7 13s ease-in-out infinite;
}

.particle-8 {
    width: 5px;
    height: 5px;
    top: 90%;
    left: 40%;
    animation: float-8 6s ease-in-out infinite;
}

/* 粒子动画关键帧 */
@keyframes float-1 {
    0%, 100% { transform: translate(0, 0) scale(1); opacity: 0.3; }
    25% { transform: translate(20px, -30px) scale(1.2); opacity: 0.6; }
    50% { transform: translate(-10px, -60px) scale(0.8); opacity: 0.8; }
    75% { transform: translate(30px, -40px) scale(1.1); opacity: 0.5; }
}

@keyframes float-2 {
    0%, 100% { transform: translate(0, 0) rotate(0deg); opacity: 0.4; }
    33% { transform: translate(-25px, 40px) rotate(120deg); opacity: 0.7; }
    66% { transform: translate(15px, -20px) rotate(240deg); opacity: 0.6; }
}

@keyframes float-3 {
    0%, 100% { transform: translate(0, 0) scale(1); opacity: 0.2; }
    50% { transform: translate(40px, 30px) scale(1.5); opacity: 0.8; }
}

@keyframes float-4 {
    0%, 100% { transform: translate(0, 0) rotate(0deg) scale(1); opacity: 0.5; }
    25% { transform: translate(15px, -25px) rotate(90deg) scale(1.3); opacity: 0.7; }
    50% { transform: translate(-20px, -50px) rotate(180deg) scale(0.9); opacity: 0.9; }
    75% { transform: translate(25px, -15px) rotate(270deg) scale(1.1); opacity: 0.6; }
}

@keyframes float-5 {
    0%, 100% { transform: translate(0, 0); opacity: 0.3; }
    50% { transform: translate(-30px, 50px); opacity: 0.7; }
}

@keyframes float-6 {
    0%, 100% { transform: translate(0, 0) scale(1) rotate(0deg); opacity: 0.4; }
    33% { transform: translate(20px, -30px) scale(1.2) rotate(120deg); opacity: 0.8; }
    66% { transform: translate(-15px, 20px) scale(0.8) rotate(240deg); opacity: 0.6; }
}

@keyframes float-7 {
    0%, 100% { transform: translate(0, 0); opacity: 0.2; }
    50% { transform: translate(-40px, -40px); opacity: 0.6; }
}

@keyframes float-8 {
    0%, 100% { transform: translate(0, 0) rotate(0deg); opacity: 0.5; }
    50% { transform: translate(30px, -60px) rotate(180deg); opacity: 0.9; }
}

/* 流动光效 */
.flowing-lights {
    position: absolute;
    width: 100%;
    height: 100%;
}

.light-beam {
    position: absolute;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    border-radius: 2px;
}

.beam-1 {
    width: 200px;
    height: 2px;
    top: 25%;
    left: -200px;
    animation: beam-flow-1 15s linear infinite;
}

.beam-2 {
    width: 150px;
    height: 1px;
    top: 65%;
    left: -150px;
    animation: beam-flow-2 20s linear infinite;
}

.beam-3 {
    width: 100px;
    height: 1px;
    top: 85%;
    left: -100px;
    animation: beam-flow-3 12s linear infinite;
}

@keyframes beam-flow-1 {
    0% { left: -200px; opacity: 0; }
    10% { opacity: 1; }
    90% { opacity: 1; }
    100% { left: 100%; opacity: 0; }
}

@keyframes beam-flow-2 {
    0% { left: -150px; opacity: 0; }
    15% { opacity: 0.8; }
    85% { opacity: 0.8; }
    100% { left: 100%; opacity: 0; }
}

@keyframes beam-flow-3 {
    0% { left: -100px; opacity: 0; }
    20% { opacity: 0.6; }
    80% { opacity: 0.6; }
    100% { left: 100%; opacity: 0; }
}

/* 网格背景 */
.grid-background {
    position: absolute;
    width: 100%;
    height: 100%;
    opacity: 0.03;
}

.grid-line {
    position: absolute;
    background: rgba(255, 255, 255, 0.1);
}

.grid-vertical {
    width: 1px;
    height: 100%;
    left: 50%;
    animation: grid-pulse-v 8s ease-in-out infinite;
}

.grid-horizontal {
    width: 100%;
    height: 1px;
    top: 50%;
    animation: grid-pulse-h 10s ease-in-out infinite;
}

@keyframes grid-pulse-v {
    0%, 100% { opacity: 0.1; transform: translateX(-50%) scaleY(1); }
    50% { opacity: 0.3; transform: translateX(-50%) scaleY(1.2); }
}

@keyframes grid-pulse-h {
    0%, 100% { opacity: 0.1; transform: translateY(-50%) scaleX(1); }
    50% { opacity: 0.3; transform: translateY(-50%) scaleX(1.2); }
}

/* 渐变光晕 */
.gradient-orbs {
    position: absolute;
    width: 100%;
    height: 100%;
}

.orb {
    position: absolute;
    border-radius: 50%;
    filter: blur(40px);
    animation-timing-function: ease-in-out;
    animation-iteration-count: infinite;
}

.orb-1 {
    width: 300px;
    height: 300px;
    background: radial-gradient(circle, rgba(102, 126, 234, 0.1) 0%, transparent 70%);
    top: 10%;
    left: 20%;
    animation: orb-float-1 20s infinite;
}

.orb-2 {
    width: 200px;
    height: 200px;
    background: radial-gradient(circle, rgba(118, 75, 162, 0.08) 0%, transparent 70%);
    top: 60%;
    right: 15%;
    animation: orb-float-2 25s infinite;
}

.orb-3 {
    width: 150px;
    height: 150px;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.05) 0%, transparent 70%);
    bottom: 20%;
    left: 10%;
    animation: orb-float-3 18s infinite;
}

@keyframes orb-float-1 {
    0%, 100% { transform: translate(0, 0) scale(1); }
    25% { transform: translate(30px, -20px) scale(1.1); }
    50% { transform: translate(-20px, 40px) scale(0.9); }
    75% { transform: translate(40px, 20px) scale(1.05); }
}

@keyframes orb-float-2 {
    0%, 100% { transform: translate(0, 0) scale(1); }
    33% { transform: translate(-40px, 30px) scale(1.2); }
    66% { transform: translate(20px, -30px) scale(0.8); }
}

@keyframes orb-float-3 {
    0%, 100% { transform: translate(0, 0) scale(1); }
    50% { transform: translate(25px, -35px) scale(1.3); }
}

/* 主容器 */
.login-container {
    position: relative;
    z-index: 2;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 20px;
}

/* 顶部Logo */
.header-logo {
    position: absolute;
    top: 30px;
    left: 40px;
    z-index: 10;
}

.logo-img {
    height: 120px;
    width: auto;
    filter: drop-shadow(0 2px 8px rgba(0, 0, 0, 0.1));
}

/* 主登录区域 */
.login-main {
    display: flex;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 24px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    max-width: 1000px;
    width: 100%;
    min-height: 600px;
}

/* 左侧装饰区域 */
.login-left {
    flex: 1;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 60px 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.login-left::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    animation: grain 20s linear infinite;
}

@keyframes grain {
    0% { transform: translate(0, 0); }
    100% { transform: translate(-100px, -100px); }
}

.left-content {
    position: relative;
    z-index: 2;
    text-align: center;
    color: white;
}

.welcome-text h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 16px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.welcome-text p {
    font-size: 1.1rem;
    opacity: 0.9;
    margin-bottom: 40px;
    line-height: 1.6;
}

.feature-list {
    margin-bottom: 40px;
}

.feature-item {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
    font-size: 1rem;
    opacity: 0.9;
}

.feature-item i {
    margin-right: 12px;
    font-size: 1.2rem;
}

/* 高端装饰元素 */
.decorative-elements {
    margin-top: 40px;
    position: relative;
    height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 几何图形组合 */
.geometric-shapes {
    position: absolute;
    width: 100%;
    height: 100%;
}

.shape {
    position: absolute;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(2px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.shape-1 {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    top: 20%;
    left: 20%;
    animation: shape-rotate-1 15s linear infinite;
}

.shape-2 {
    width: 40px;
    height: 40px;
    top: 60%;
    right: 25%;
    transform: rotate(45deg);
    animation: shape-float-2 12s ease-in-out infinite;
}

.shape-3 {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    bottom: 30%;
    left: 15%;
    animation: shape-pulse-3 8s ease-in-out infinite;
}

.shape-4 {
    width: 50px;
    height: 50px;
    top: 10%;
    right: 15%;
    clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
    animation: shape-bounce-4 10s ease-in-out infinite;
}

.shape-5 {
    width: 35px;
    height: 35px;
    bottom: 20%;
    right: 30%;
    border-radius: 8px;
    animation: shape-wiggle-5 14s ease-in-out infinite;
}

/* 几何图形动画 */
@keyframes shape-rotate-1 {
    0% { transform: rotate(0deg) scale(1); }
    50% { transform: rotate(180deg) scale(1.2); }
    100% { transform: rotate(360deg) scale(1); }
}

@keyframes shape-float-2 {
    0%, 100% { transform: rotate(45deg) translateY(0); }
    50% { transform: rotate(45deg) translateY(-20px); }
}

@keyframes shape-pulse-3 {
    0%, 100% { transform: scale(1); opacity: 0.6; }
    50% { transform: scale(1.3); opacity: 1; }
}

@keyframes shape-bounce-4 {
    0%, 100% { transform: translateY(0); }
    25% { transform: translateY(-15px); }
    50% { transform: translateY(0); }
    75% { transform: translateY(-8px); }
}

@keyframes shape-wiggle-5 {
    0%, 100% { transform: rotate(0deg); }
    25% { transform: rotate(5deg); }
    50% { transform: rotate(0deg); }
    75% { transform: rotate(-5deg); }
}

/* 数据可视化图表 */
.data-visualization {
    position: relative;
    z-index: 2;
    text-align: center;
}

.chart-container {
    display: flex;
    align-items: end;
    justify-content: center;
    gap: 4px;
    height: 80px;
    margin-bottom: 12px;
}

.chart-bar {
    width: 8px;
    background: linear-gradient(to top, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.8));
    border-radius: 4px 4px 0 0;
    animation: chart-grow 2s ease-out infinite alternate;
}

.chart-label {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
}

@keyframes chart-grow {
    0% { transform: scaleY(0.7); opacity: 0.6; }
    100% { transform: scaleY(1); opacity: 1; }
}

/* 科技感线条 */
.tech-lines {
    position: absolute;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.line {
    position: absolute;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    height: 1px;
}

.line-1 {
    width: 80px;
    top: 25%;
    left: 10%;
    animation: line-extend-1 6s ease-in-out infinite;
}

.line-2 {
    width: 60px;
    top: 70%;
    right: 20%;
    animation: line-extend-2 8s ease-in-out infinite;
}

.line-3 {
    width: 100px;
    bottom: 25%;
    left: 30%;
    animation: line-extend-3 7s ease-in-out infinite;
}

@keyframes line-extend-1 {
    0%, 100% { width: 0; opacity: 0; }
    50% { width: 80px; opacity: 1; }
}

@keyframes line-extend-2 {
    0%, 100% { width: 0; opacity: 0; }
    50% { width: 60px; opacity: 1; }
}

@keyframes line-extend-3 {
    0%, 100% { width: 0; opacity: 0; }
    50% { width: 100px; opacity: 1; }
}

/* 浮动图标 */
.floating-icons {
    position: absolute;
    width: 100%;
    height: 100%;
}

.floating-icon {
    position: absolute;
    width: 24px;
    height: 24px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(2px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.floating-icon i {
    font-size: 10px;
    color: rgba(255, 255, 255, 0.8);
}

.icon-1 {
    top: 15%;
    left: 70%;
    animation: icon-float-1 9s ease-in-out infinite;
}

.icon-2 {
    top: 45%;
    left: 10%;
    animation: icon-float-2 11s ease-in-out infinite;
}

.icon-3 {
    bottom: 35%;
    right: 20%;
    animation: icon-float-3 13s ease-in-out infinite;
}

.icon-4 {
    bottom: 15%;
    left: 60%;
    animation: icon-float-4 7s ease-in-out infinite;
}

@keyframes icon-float-1 {
    0%, 100% { transform: translate(0, 0) rotate(0deg); }
    25% { transform: translate(10px, -15px) rotate(90deg); }
    50% { transform: translate(-5px, -25px) rotate(180deg); }
    75% { transform: translate(15px, -10px) rotate(270deg); }
}

@keyframes icon-float-2 {
    0%, 100% { transform: translate(0, 0) scale(1); }
    50% { transform: translate(20px, 15px) scale(1.2); }
}

@keyframes icon-float-3 {
    0%, 100% { transform: translate(0, 0) rotate(0deg); }
    33% { transform: translate(-12px, 8px) rotate(120deg); }
    66% { transform: translate(8px, -12px) rotate(240deg); }
}

@keyframes icon-float-4 {
    0%, 100% { transform: translate(0, 0) scale(1); }
    25% { transform: translate(-8px, -12px) scale(0.9); }
    50% { transform: translate(12px, -8px) scale(1.1); }
    75% { transform: translate(-6px, 10px) scale(0.95); }
}

/* 右侧登录表单区域 */
.login-right {
    flex: 1;
    padding: 60px 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: white;
}

.login-form-container {
    width: 100%;
    max-width: 400px;
}

/* 表单头部 */
.form-header {
    text-align: center;
    margin-bottom: 40px;
}

.form-title {
    font-size: 2rem;
    font-weight: 700;
    color: #1a1a1a;
    margin-bottom: 8px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.form-subtitle {
    font-size: 0.95rem;
    color: #666;
    font-weight: 400;
}

/* 登录表单 */
.login-form {
    width: 100%;
}

/* 输入组 */
.input-group {
    margin-bottom: 24px;
}

.input-label {
    display: block;
    font-size: 0.9rem;
    font-weight: 600;
    color: #374151;
    margin-bottom: 8px;
}

.input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    background: #f8fafc;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    transition: all 0.3s ease;
    overflow: hidden;
}

.input-wrapper:hover {
    border-color: #cbd5e0;
    background: #f1f5f9;
}

.input-wrapper.focused {
    border-color: #667eea;
    background: white;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.input-icon {
    padding: 0 16px;
    color: #9ca3af;
    font-size: 1.1rem;
    transition: color 0.3s ease;
}

.input-wrapper.focused .input-icon {
    color: #667eea;
}

.form-input {
    flex: 1;
    padding: 16px 16px 16px 0;
    border: none;
    background: transparent;
    font-size: 1rem;
    color: #1f2937;
    outline: none;
    font-family: inherit;
}

.form-input::placeholder {
    color: #9ca3af;
    font-weight: 400;
}

.password-toggle {
    padding: 0 16px;
    color: #9ca3af;
    cursor: pointer;
    font-size: 1.1rem;
    transition: color 0.3s ease;
}

.password-toggle:hover {
    color: #667eea;
}

/* 表单选项 */
.form-options {
    margin-bottom: 32px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.checkbox-wrapper {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 0.9rem;
    color: #4b5563;
}

/* 隐藏layui原生复选框 */
.checkbox-wrapper input[type="checkbox"] {
    display: none !important;
}

/* 隐藏layui复选框相关元素 */
.checkbox-wrapper .layui-form-checkbox,
.form-options .layui-form-checkbox,
.layui-form-checkbox[lay-skin="primary"],
.layui-form-checkbox,
.layui-form-checkbox *,
.form-options .layui-form-checkbox *,
.checkbox-wrapper .layui-form-checkbox * {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    position: absolute !important;
    left: -9999px !important;
}

/* 确保只显示我们自定义的复选框 */
.form-options .layui-form-item {
    margin: 0 !important;
}

.form-options .layui-form-checkbox + .checkmark {
    display: none !important;
}

/* 强制隐藏所有可能的layui复选框样式 */
.login-form .layui-form-checkbox {
    display: none !important;
}

.login-form input[type="checkbox"] + div {
    display: none !important;
}

.checkmark {
    width: 18px;
    height: 18px;
    border: 2px solid #d1d5db;
    border-radius: 4px;
    margin-right: 8px;
    position: relative;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.checkbox-wrapper input[type="checkbox"]:checked + .checkmark {
    background: #667eea;
    border-color: #667eea;
}

.checkbox-wrapper input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.checkbox-text {
    font-weight: 500;
}

/* 登录按钮 */
.login-btn {
    width: 100%;
    padding: 16px 24px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 12px;
    color: white;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    position: relative;
    overflow: hidden;
}

.login-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.login-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.login-btn:hover::before {
    left: 100%;
}

.login-btn:active {
    transform: translateY(0);
}

.login-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

.btn-text {
    font-family: inherit;
}

.btn-icon {
    font-size: 0.9rem;
    transition: transform 0.3s ease;
}

.login-btn:hover .btn-icon {
    transform: translateX(2px);
}

/* 底部版权信息 */
.login-footer {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 10;
}

.footer-content {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 0.85rem;
    color: rgba(255, 255, 255, 0.8);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.separator {
    opacity: 0.6;
}

.footer-link {
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-link:hover {
    color: white;
    text-decoration: underline;
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .login-main {
        max-width: 900px;
        margin: 0 20px;
    }

    .login-left {
        padding: 40px 30px;
    }

    .login-right {
        padding: 40px 30px;
    }

    .welcome-text h1 {
        font-size: 2rem;
    }

    .form-title {
        font-size: 1.75rem;
    }
}

@media (max-width: 768px) {
    .login-main {
        flex-direction: column;
        max-width: 500px;
        min-height: auto;
    }

    .login-left {
        padding: 40px 30px 30px;
        min-height: 300px;
    }

    .login-right {
        padding: 30px;
    }

    .welcome-text h1 {
        font-size: 1.75rem;
        margin-bottom: 12px;
    }

    .welcome-text p {
        font-size: 1rem;
        margin-bottom: 30px;
    }

    .feature-list {
        margin-bottom: 20px;
    }

    .feature-item {
        margin-bottom: 15px;
        font-size: 0.9rem;
    }

    .decorative-image {
        margin-top: 20px;
    }

    .form-title {
        font-size: 1.5rem;
    }

    .form-subtitle {
        font-size: 0.9rem;
    }

    .header-logo {
        top: 20px;
        left: 20px;
    }

    .logo-img {
        height: 120px;
    }

    .login-footer {
        bottom: 20px;
    }

    .footer-content {
        font-size: 0.8rem;
        flex-direction: column;
        gap: 4px;
        text-align: center;
    }

    .separator {
        display: none;
    }
}

@media (max-width: 480px) {
    .login-container {
        padding: 10px;
    }

    .login-main {
        margin: 0 10px;
        border-radius: 16px;
    }

    .login-left {
        padding: 30px 20px 20px;
        min-height: 250px;
    }

    .login-right {
        padding: 20px;
    }

    .welcome-text h1 {
        font-size: 1.5rem;
    }

    .welcome-text p {
        font-size: 0.9rem;
    }

    .form-title {
        font-size: 1.25rem;
    }

    .input-wrapper {
        border-radius: 8px;
    }

    .login-btn {
        border-radius: 8px;
        padding: 14px 20px;
    }

    .header-logo {
        top: 15px;
        left: 15px;
    }

    .logo-img {
        height: 35px;
    }
}

/* 入场动画 */
.animate-slide-up {
    opacity: 0;
    transform: translateY(30px);
    animation: slideUp 0.8s ease-out forwards;
}

@keyframes slideUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 左侧内容入场动画 */
.left-content {
    animation: fadeInLeft 1s ease-out;
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* 主登录区域入场动画 */
.login-main {
    animation: scaleIn 0.8s ease-out;
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* 增强的输入框动画 */
.input-wrapper {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.input-wrapper:focus-within {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
}

/* 按钮点击波纹效果 */
.login-btn {
    position: relative;
    overflow: hidden;
}

.login-btn::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.login-btn:active::after {
    width: 300px;
    height: 300px;
}

/* 标题文字动画 */
.form-title {
    background-size: 200% 200%;
    animation: gradientShift 3s ease-in-out infinite;
}

@keyframes gradientShift {
    0%, 100% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
}

/* 特色功能项悬停效果 */
.feature-item {
    transition: all 0.3s ease;
    cursor: default;
}

.feature-item:hover {
    transform: translateX(10px);
    color: rgba(255, 255, 255, 1);
}

.feature-item:hover i {
    transform: scale(1.2);
    transition: transform 0.3s ease;
}

/* Logo悬停效果 */
.logo-img {
    transition: all 0.3s ease;
}

.logo-img:hover {
    transform: scale(1.05);
    filter: drop-shadow(0 4px 12px rgba(0, 0, 0, 0.2));
}

/* 复选框增强动画 */
.checkmark {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.checkbox-wrapper:hover .checkmark {
    border-color: #667eea;
    transform: scale(1.1);
}

/* 密码切换按钮动画 */
.password-toggle {
    transition: all 0.3s ease;
}

.password-toggle:active {
    transform: scale(0.9);
}

/* 页脚动画 */
.login-footer {
    animation: fadeInUp 1s ease-out 0.5s both;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateX(-50%) translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
}

/* 加载动画 */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.fa-spin {
    animation: spin 1s linear infinite;
}

/* 输入框标签浮动效果 */
.input-label {
    transition: all 0.3s ease;
}

.input-wrapper.focused + .input-label,
.form-input:focus + .input-label {
    color: #667eea;
    transform: translateY(-2px);
}

/* 表单容器悬停效果 */
.login-form-container {
    transition: all 0.3s ease;
}

/* 增强的阴影效果 */
.login-main:hover {
    box-shadow: 0 25px 80px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
    transition: all 0.3s ease;
}

/* 背景渐变动画 */
body {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #667eea 100%);
    background-size: 400% 400%;
    animation: gradientBackground 15s ease infinite;
}

@keyframes gradientBackground {
    0%, 100% {
        background-position: 0% 50%;
    }
    25% {
        background-position: 100% 50%;
    }
    50% {
        background-position: 100% 100%;
    }
    75% {
        background-position: 0% 100%;
    }
}

/* 平滑滚动 */
html {
    scroll-behavior: smooth;
}

/* 选中文本样式 */
::selection {
    background: rgba(102, 126, 234, 0.2);
    color: #1f2937;
}

::-moz-selection {
    background: rgba(102, 126, 234, 0.2);
    color: #1f2937;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}