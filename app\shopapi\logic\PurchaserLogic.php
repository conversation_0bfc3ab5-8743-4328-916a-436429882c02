<?php
namespace app\shopapi\logic;

use app\common\basics\Logic;
use app\common\model\ShopPurchaserAllocation;
use app\common\model\MassMessageLimit;
use app\common\model\shop\Shop;
use app\api\logic\MassMessageLogic;
use think\facade\Db;
use app\common\server\UrlServer;
use app\common\enum\ChatMsgEnum;

/**
 * 采购人员逻辑层
 * Class PurchaserLogic
 * @package app\shopapi\logic
 */
class PurchaserLogic extends Logic
{
    /**
     * 获取商家采购人员列表
     * @param int $shopId 商家ID
     * @param int $page 页码
     * @param int $limit 每页数量
     * @return array|false
     */
    public static function getShopPurchaserList($shopId, $page = 1, $limit = 20,$get=[])
    {
       
            $where = [
            ['spa.shop_id', '=', $shopId],
            ['spa.status', '=', 1]
            ];

        if(!empty($get['keyword'])){
            $where[] = ['u.nickname|u.mobile', 'like', '%' . $get['keyword'] . '%'];
        }
        $data= Db::name('shop_purchaser_allocation')->alias('spa')
            ->leftJoin('user u', 'spa.user_id = u.id')
            ->where($where)
            ->field('spa.*, u.nickname, u.avatar, u.mobile, u.activity_score,u.user_delete')
            ->order('spa.user_level asc, spa.user_score desc')
            ->page($page, $limit)
            ->select()
            ->toArray();

        $count = Db::name('shop_purchaser_allocation')->alias('spa')
            ->leftJoin('user u', 'spa.user_id = u.id')
            ->where($where)
            ->count();
            foreach($data as $k=>&$v){
                $v['avatar'] = UrlServer::getFileUrl($v['avatar']);
            }
            
            // 添加等级名称
            foreach ($data as &$item) {
                $item['level_name'] = self::getUserLevelName($item['user_level']);
                $item['allocation_time_text'] = date('Y-m-d H:i:s', $item['allocation_time']);
            }
            
            return [
                'lists' => $data,
                'count' => $count,
                'page_no' => $page,
                'page_size' => $limit,
                'more' => ($page * $limit) < $count
            ];
            
       
    }

    /**
     * 获取商家分配统计
     * @param int $shopId 商家ID
     * @return array|false
     */
    public static function getShopAllocationStats($shopId)
    {
        try {
            $stats = ShopPurchaserAllocation::getShopAllocationStats($shopId);
            
            // 获取商家等级和配置
            $shop = Shop::find($shopId);
            if ($shop) {
                $config = MassMessageLimit::getPurchaserAllocationConfig($shop->tier_level);
                $stats['config'] = $config;
                $stats['tier_level'] = $shop->tier_level;
                $stats['tier_name'] = MassMessageLimit::getTierLevelName($shop->tier_level);
                
                // 计算分配完成度
                $stats['allocation_rate'] = $config['total_count'] > 0 ? 
                    round(($stats['total'] / $config['total_count']) * 100, 2) : 0;
            }
            
            return $stats;
            
        } catch (\Exception $e) {
            self::$error = $e->getMessage();
            return false;
        }
    }

    /**
     * 获取商家群发信息限制信息
     * @param int $shopId 商家ID
     * @return array|false
     */
    public static function getMassMessageLimit($shopId)
    {
        try {
            $shop = Shop::find($shopId);
            if (!$shop) {
                self::$error = '商家不存在';
                return false;
            }
            
            // 获取配置
            $config = MassMessageLimit::getConfigByTierLevel($shop->tier_level);
            if (!$config) {
                return [
                    'daily_limit' => 0,
                    'sent_today' => 0,
                    'remaining' => 0,
                    'max_text' =>300,
                    'image_size'=>2,
                    'tier_level' => $shop->tier_level,
                    'tier_name' => MassMessageLimit::getTierLevelName($shop->tier_level)
                ];
            }
            
            // 统计今日已发送数量
            $sentToday = Db::name('mass_message_record')
                ->where('shop_id', $shopId)
                ->whereTime('create_time', 'today')
                ->count();
            
            return [
                'daily_limit' => $config['daily_limit'],
                'sent_today' => $sentToday,
                'max_text' =>300,
                'image_size'=>2,
                'remaining' => max(0, $config['daily_limit'] - $sentToday),
                'tier_level' => $shop->tier_level,
                'tier_name' => MassMessageLimit::getTierLevelName($shop->tier_level)
            ];
            
        } catch (\Exception $e) {
            self::$error = $e->getMessage();
            return false;
        }
    }

    /**
     * 检查今日群发信息是否超限
     * @param int $shopId 商家ID
     * @return bool
     */
    public static function checkMassMessageLimit($shopId)
    {
        return MassMessageLogic::checkMassMessageLimit($shopId);
    }

    /**
     * 记录群发信息发送
     * @param int $shopId 商家ID
     * @param array $data 发送数据
     * @return array|false
     */
    public static function recordMassMessage($shopId, $data)
    {
        Db::startTrans();
        try {
            // 1. 检查是否超限
            if (!self::checkMassMessageLimit($shopId)) {
                self::$error = '今日群发次数已达上限';
                return false;
            }

            // 2. 获取商家关联的user_id作为发送方
         
            $admin_id = Db::name('shop_admin')->where('shop_id', $shopId)->where('root',1)->value('id');
            if (empty($admin_id)) {
                self::$error = '商家未开通客服系统';
                return false;
            }
            $kefu=Db::name('kefu')->where('shop_id', $shopId)->where('admin_id',$admin_id)->find();
            if (empty($kefu)) {
                self::$error = '商家未开通客服系统';
                return false;
            }

           
            $fromUid =$kefu['id'];
            $time = time();
            $content = $data['content'];
            $messageType = $data['message_type'] ?? ChatMsgEnum::TYPE_TEXT; // 默认文本
            $targetUserIds = $data['target_user_ids'];
            $successCount = 0;
            $failCount = 0;
         
            // 3. 遍历目标用户，插入聊天记录
            foreach ($targetUserIds as $toUid) {
                if($fromUid == $toUid) {
                    continue; // 不能给自己发
                }
                $toUser=Db::name('user')->field('avatar,nickname')->where('id', $toUid)->where('user_delete',0)->find();
                 if(empty($toUser)){
                      $failCount++;
                      continue;
                 }   
                // 4. 查找或创建聊天关系
               $relation2 = Db::name('chat_relation')
                    ->where(function ($query) use ($fromUid, $toUid) {
                        $query->where('kefu_id', $fromUid)->where('user_id', $toUid);
                    })
                    ->whereOr(function ($query) use ($fromUid, $toUid) {
                        $query->where('user_id', $toUid)->where('kefu_id', $fromUid);
                    })
                    ->find();  
                if (!$relation2) {
                    $relationId = Db::name('chat_relation')->insertGetId([
                        'kefu_id' => $toUid,
                        'user_id' => $fromUid,
                        'avatar'=> $toUser['avatar'],
                        'nickname'=> $toUser['nickname'],
                        'msg_type'=>  $messageType,
                        'msg'=>  $content,
                        'client'=>  2,
                        'shop_id' => $shopId,
                        'create_time' => $time,
                    ]);
                  
                }   
                $relation1 = Db::name('chat_relation')
                   
                    ->where(function ($query) use ($fromUid, $toUid) {
                        $query->where('user_id', $toUid)->where('kefu_id', $fromUid);
                    })
                    ->find();  
               
                if (!$relation1) {
                    $relationId = Db::name('chat_relation')->insertGetId([
                        'user_id' => $toUid,
                        'kefu_id' => $fromUid,
                        'avatar'=> $toUser['avatar'],
                        'nickname'=> $toUser['nickname'],
                        'msg_type'=>  $messageType,
                        'msg'=>  $content,
                        'client'=>  2,
                        'shop_id' => $shopId,
                        'create_time' => $time,
                    ]);
                } 
                if(!empty($content)){
                // 5. 插入聊天记录
                // 文本消息
                $chatRecordData = [
                 
                    'from_id' => $fromUid,
                    'from_type' => 'kefu',
                    'to_type'=>'user',
                    'to_id' => $toUid,
                    'msg_type' => ChatMsgEnum::TYPE_TEXT,
                    'msg' => $content,
                    'is_read' => 0,
                    'shop_id' => $shopId,
                    'create_time' => $time,
                ];
                Db::name('chat_record')->insert($chatRecordData);
                }
            
                // 如果有附件，再插入一条记录
                if (!empty($data['file'])) {
                    $chatRecordFileData = [
                        'from_id' => $fromUid,
                        'to_id' => $toUid,
                        'from_type' => 'kefu',
                        'to_type'=>'user',
                        'msg_type' => $messageType,
                        'msg' => $data['file'], // 附件内容
                        'is_read' => 0,
                        'shop_id' => $shopId,
                        'create_time' => $time,
                    ];
                    Db::name('chat_record')->insert($chatRecordFileData);
                }
                $successCount++;
            }

            // 6. 插入群发总记录
            $massRecordData = [
                'shop_id' => $shopId,
                'message_type' => $messageType, // 记录实际的消息类型
                'content' => $content,
                'file' => $data['file'], // 记录文件内容
                'target_user_ids' => json_encode($targetUserIds),
                'send_count' => count($targetUserIds),
                'success_count' => $successCount,
                'fail_count' => $failCount,
                'send_status' => 2, // 发送完成
                'create_time' => $time,
                'send_time' => $time,
                'complete_time' => $time,
            ];
            $massRecordId = Db::name('mass_message_record')->insertGetId($massRecordData);

            Db::commit();

            return [
                'mass_record_id' => $massRecordId,
                'success_count' => $successCount,
                'fail_count' => $failCount,
            ];

        } catch (\Exception $e) {
            Db::rollback();
            self::$error = $e->getMessage().'-'.$e->getLine();
            return false;
        }
    }

    /**
     * 获取群发信息发送记录
     * @param int $shopId 商家ID
     * @param int $page 页码
     * @param int $limit 每页数量
     * @return array|false
     */
    public static function getMassMessageRecords($shopId, $page = 1, $limit = 20)
    {
        try {
            $where = [
                ['shop_id', '=', $shopId]
            ];
            
            $total = Db::name('mass_message_record')->where($where)->count();
            
            $list = Db::name('mass_message_record')
                ->where($where)
                ->order('create_time desc')
                ->page($page, $limit)
                ->select()
                ->toArray();
            
            foreach ($list as &$item) {
                $item['create_time_text'] = date('Y-m-d H:i:s', $item['create_time']);
                $item['send_time_text'] = $item['send_time'] ? date('Y-m-d H:i:s', $item['send_time']) : '';
                $item['target_user_ids'] = json_decode($item['target_user_ids'], true);
                $item['send_status_text'] = self::getSendStatusText($item['send_status']);
            }
            
            return [
                'list' => $list,
                'total' => $total,
                'page' => $page,
                'limit' => $limit,
                'has_more' => ($page * $limit) < $total
            ];
            
        } catch (\Exception $e) {
            self::$error = $e->getMessage();
            return false;
        }
    }

    /**
     * 手动重新分配采购人员
     * @param int $shopId 商家ID
     * @return bool
     */
    public static function manualReAllocate($shopId)
    {
        try {
            $shop = Shop::find($shopId);
            if (!$shop) {
                self::$error = '商家不存在';
                return false;
            }
            
            return \app\admin\logic\PurchaserAllocationLogic::autoAllocatePurchasers($shopId, $shop->tier_level);
            
        } catch (\Exception $e) {
            self::$error = $e->getMessage();
            return false;
        }
    }

    /**
     * 获取采购人员详情
     * @param int $shopId 商家ID
     * @param int $userId 用户ID
     * @return array|false
     */
    public static function getPurchaserDetail($shopId, $userId)
    {
        try {
            // 检查是否为该商家分配的采购人员
            $allocation = ShopPurchaserAllocation::where([
                ['shop_id', '=', $shopId],
                ['user_id', '=', $userId],
                ['allocation_year', '=', date('Y')],
                ['status', '=', 1]
            ])->find();
            
            if (!$allocation) {
                self::$error = '该用户不是您的分配采购人员';
                return false;
            }
            
            // 获取用户详细信息
            $user = Db::name('user')->where('id', $userId)->find();
            if (!$user) {
                self::$error = '用户不存在';
                return false;
            }
            
            return [
                'user_info' => [
                    'id' => $user['id'],
                    'nickname' => $user['nickname'],
                    'avatar' => $user['avatar'],
                    'mobile' => $user['mobile'],
                    'activity_score' => $user['activity_score'],
                    'is_purchaser' => $user['is_purchaser']
                ],
                'allocation_info' => [
                    'user_level' => $allocation['user_level'],
                    'level_name' => self::getUserLevelName($allocation['user_level']),
                    'allocation_time' => date('Y-m-d H:i:s', $allocation['allocation_time']),
                    'allocation_year' => $allocation['allocation_year']
                ]
            ];
            
        } catch (\Exception $e) {
            self::$error = $e->getMessage();
            return false;
        }
    }

    /**
     * 获取用户等级名称
     * @param int $level
     * @return string
     */
    private static function getUserLevelName($level)
    {
        $names = [
            1 => '低活跃度',
            2 => '中活跃度',
            3 => '高活跃度'
        ];
        
        return $names[$level] ?? '未知等级';
    }

    /**
     * 获取发送状态文本
     * @param int $status
     * @return string
     */
    private static function getSendStatusText($status)
    {
        $texts = [
            0 => '待发送',
            1 => '发送中',
            2 => '发送完成',
            3 => '发送失败'
        ];
        
        return $texts[$status] ?? '未知状态';
    }
}
