(window.webpackJsonp=window.webpackJsonp||[]).push([[44],{581:function(t,e,r){var content=r(653);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,r(17).default)("4c362332",content,!0,{sourceMap:!1})},649:function(t,e,r){t.exports=r.p+"img/time.af8667a.png"},650:function(t,e,r){t.exports=r.p+"img/error.2251fae.png"},651:function(t,e,r){t.exports=r.p+"img/success.e4b77b0.png"},652:function(t,e,r){"use strict";r(581)},653:function(t,e,r){var n=r(16)(!1);n.push([t.i,".detail,.detail .main{width:100%;height:788px}.detail .main{padding:30px}.detail .main .header{padding:0 0 40px;margin-bottom:25px;border-bottom:1px dotted #e5e5e5}.detail .main .header img{width:32px;height:32px}.detail .main .header .admin{background:#f6f6f6;padding:0 30px 16px}.detail .main .header .admin a:hover{color:#ff2c3c}",""]),t.exports=n},715:function(t,e,r){"use strict";r.r(e);r(29);var n=[function(){var t=this._self._c;return t("div",{staticClass:"flex normal xxl bold",staticStyle:{"font-weight":"600"}},[t("img",{staticClass:"m-r-12",attrs:{src:r(649),alt:""}}),this._v("\n                    恭喜您，资料提交成功\n                ")])},function(){var t=this._self._c;return t("div",{staticClass:"flex normal xxl bold",staticStyle:{"font-weight":"600"}},[t("img",{staticClass:"m-r-12",attrs:{src:r(650),alt:""}}),this._v("\n                    很遗憾，审核不通过！\n                ")])},function(){var t=this._self._c;return t("div",{staticClass:"flex normal xxl bold",staticStyle:{"font-weight":"600"}},[t("img",{staticClass:"m-r-12",attrs:{src:r(651),alt:""}}),this._v("\n                    恭喜您，审核已通过！\n                ")])},function(){var t=this,e=t._self._c;return e("div",{staticClass:"xs m-t-16"},[e("span",[t._v("登录密码：")]),t._v("\n                        登录密码：密码是您在创建账号时设置的登录密码，如忘记密码可联系官方客服进行修改！\n                    ")])}],l=r(9),o=(r(53),{head:function(){return{title:this.$store.getters.headTitle,link:[{rel:"icon",type:"image/x-icon",href:this.$store.getters.favicon}]}},data:function(){return{detail:{}}},mounted:function(){var t=this;return Object(l.a)(regeneratorRuntime.mark((function e(){var r,data;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return console.log(t.$route.query.id),e.next=3,t.$get("ShopApply/detail",{params:{id:t.$route.query.id}});case 3:r=e.sent,data=r.data,t.detail=data,console.log("我艹啊私房话哀诉还是");case 7:case"end":return e.stop()}}),e)})))()},methods:{}}),d=(r(652),r(8)),component=Object(d.a)(o,(function(){var t=this,e=t._self._c;return e("div",{staticClass:"detail"},[e("div",{staticClass:"m-t-20"},[e("el-breadcrumb",{attrs:{separator:"/"}},[e("el-breadcrumb-item",{attrs:{to:{path:"/"}}},[t._v("首页")]),t._v(" "),e("el-breadcrumb-item",{attrs:{to:{path:"/store_settled"}}},[e("a",[t._v("商家入驻")])]),t._v(" "),e("el-breadcrumb-item",{attrs:{to:{path:"/store_settled/record"}}},[e("a",[t._v("申请列表")])]),t._v(" "),e("el-breadcrumb-item",[t._v("详情")])],1)],1),t._v(" "),e("div",{staticClass:"main bg-white m-t-20"},[e("div",{staticClass:"header"},[e("div",{staticClass:"m-b-30 pointer",on:{click:function(e){return t.$router.back()}}},[e("i",{staticClass:"el-icon-arrow-left"}),t._v("\n                返回\n            ")]),t._v(" "),1==t.detail.audit_status?e("div",[t._m(0),t._v(" "),e("div",{staticClass:"xs muted m-t-12 m-l-42"},[t._v("\n                    预计在3个工作日内审核完毕，如通过我们将会发送短信通知您，请注意查收！\n                ")])]):3==t.detail.audit_status?e("div",[t._m(1),t._v(" "),e("div",{staticClass:"xs muted m-t-12 m-l-42"},[t._v("\n                    请尽量完善您的资料信息再重新提交！\n                ")])]):2==t.detail.audit_status?e("div",[t._m(2),t._v(" "),e("div",{staticClass:"xs muted m-t-12 m-l-42"},[t._v("\n                    您的审核已通过\n                ")]),t._v(" "),e("div",{staticClass:"admin m-t-20"},[e("div",{staticClass:"xs p-t-16"},[e("span",[t._v("PC管理后台地址：")]),t._v(" "),e("a",{attrs:{href:t.detail.admin_address}},[t._v("\n                            "+t._s(t.detail.admin_address)+"\n                        ")])]),t._v(" "),e("div",{staticClass:"xs m-t-16"},[e("span",[t._v("商家账号：")]),t._v(" "),e("a",{attrs:{href:"",target:"_blank",rel:"noopener noreferrer"}},[t._v("\n                            "+t._s(t.detail.account)+"\n                        ")])]),t._v(" "),t._m(3)])]):t._e()]),t._v(" "),e("div",{staticClass:"section"},[e("div",{staticClass:"xl bold normal m-b-30"},[t._v("资料详情")]),t._v(" "),e("el-form",{ref:"form",staticClass:"demo-form",attrs:{model:t.detail,size:"medium","label-position":"left","label-width":"110px"}},[e("el-form-item",{attrs:{label:"商家名称:",prop:"name"}},[e("span",[t._v(t._s(t.detail.name))])]),t._v(" "),e("el-form-item",{attrs:{label:"主营类目:",prop:"name"}},[e("span",[t._v(t._s(t.detail.cid_desc))])]),t._v(" "),e("el-form-item",{attrs:{label:"联系人姓名:",prop:"name"}},[e("span",[t._v(t._s(t.detail.nickname))])]),t._v(" "),e("el-form-item",{attrs:{label:"手机号码:",prop:"name"}},[e("span",[t._v(t._s(t.detail.mobile))])]),t._v(" "),e("el-form-item",{attrs:{label:"商家账号:",prop:"name"}},[e("span",[t._v(t._s(t.detail.account))])]),t._v(" "),e("el-form-item",{attrs:{label:"营业执照:",prop:"name"}},t._l(t.detail.license,(function(t,r){return e("el-image",{key:r,staticStyle:{width:"72px",height:"72px","margin-right":"10px"},attrs:{src:t,fit:"fit"}})})),1)],1)],1)])])}),n,!1,null,null,null);e.default=component.exports}}]);