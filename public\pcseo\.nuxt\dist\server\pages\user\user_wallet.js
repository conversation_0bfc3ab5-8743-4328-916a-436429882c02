exports.ids = [54];
exports.modules = {

/***/ 239:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(318);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(4).default
module.exports.__inject__ = function (context) {
  add("3665ce96", content, true, context)
};

/***/ }),

/***/ 317:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_sass_resources_loader_lib_loader_js_ref_7_oneOf_1_4_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_user_wallet_vue_vue_type_style_index_0_id_7421fc7d_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(239);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_sass_resources_loader_lib_loader_js_ref_7_oneOf_1_4_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_user_wallet_vue_vue_type_style_index_0_id_7421fc7d_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_sass_resources_loader_lib_loader_js_ref_7_oneOf_1_4_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_user_wallet_vue_vue_type_style_index_0_id_7421fc7d_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_sass_resources_loader_lib_loader_js_ref_7_oneOf_1_4_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_user_wallet_vue_vue_type_style_index_0_id_7421fc7d_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_sass_resources_loader_lib_loader_js_ref_7_oneOf_1_4_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_user_wallet_vue_vue_type_style_index_0_id_7421fc7d_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 318:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(3);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".user-wallet-container[data-v-7421fc7d]{width:980px;padding:10px 10px 60px}.user-wallet-container .user-wallet-header[data-v-7421fc7d]{padding:10px 5px;border-bottom:1px solid #e5e5e5}.user-wallet-container[data-v-7421fc7d]  .el-tabs__header{margin-left:5px}.user-wallet-container[data-v-7421fc7d]  .el-tabs .el-tabs__nav-scroll{padding:0}.user-wallet-container .user-wallet-content[data-v-7421fc7d]{margin-top:17px}.user-wallet-container .user-wallet-content .wallet-info-box[data-v-7421fc7d]{padding:24px;background:linear-gradient(87deg,#ff2c3c,#ff9e2c)}.user-wallet-container .user-wallet-content .wallet-info-box .user-wallet-info .title[data-v-7421fc7d]{color:#ffdcd7;margin-bottom:8px}.user-wallet-container .user-wallet-table[data-v-7421fc7d]{background-color:#f2f2f2}.user-wallet-container .user-wallet-table[data-v-7421fc7d]  .el-table{color:#222}.user-wallet-container .user-wallet-table[data-v-7421fc7d]  .el-table .el-button--text{color:#222;font-weight:400}.user-wallet-container .user-wallet-table[data-v-7421fc7d]  .el-table th{background-color:#f2f2f2}.user-wallet-container .user-wallet-table[data-v-7421fc7d]  .el-table thead{color:#555;font-weight:400}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 363:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/user/user_wallet.vue?vue&type=template&id=7421fc7d&scoped=true&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:"user-wallet-container"},[_vm._ssrNode("<div class=\"user-wallet-header lg\" data-v-7421fc7d>\n        我的钱包\n    </div> "),_vm._ssrNode("<div class=\"user-wallet-content\" data-v-7421fc7d>","</div>",[_vm._ssrNode("<div class=\"wallet-info-box flex\" data-v-7421fc7d><div class=\"user-wallet-info\" data-v-7421fc7d><div class=\"xs title\" data-v-7421fc7d>我的余额</div> <div class=\"nr white flex\" style=\"font-weight: 500;align-items: baseline;\" data-v-7421fc7d>¥<label style=\"font-size: 24px;\" data-v-7421fc7d>"+_vm._ssrEscape(_vm._s(_vm.wallet.user_money || 0))+"</label></div></div> <div class=\"user-wallet-info\" style=\"margin-left: 144px\" data-v-7421fc7d><div class=\"xs title\" data-v-7421fc7d>累计消费</div> <div class=\"nr white flex\" style=\"font-weight: 500;align-items: baseline;\" data-v-7421fc7d>¥<label style=\"font-size: 24px;\" data-v-7421fc7d>"+_vm._ssrEscape(_vm._s(_vm.wallet.total_order_amount || 0))+"</label></div></div></div> "),_c('el-tabs',{staticClass:"mt10",on:{"tab-click":_vm.handleClick},model:{value:(_vm.activeName),callback:function ($$v) {_vm.activeName=$$v},expression:"activeName"}},_vm._l((_vm.userWallet),function(item,index){return _c('el-tab-pane',{key:index,attrs:{"label":item.name,"name":item.type}},[_c('div',{staticClass:"user-wallet-table"},[_c('el-table',{staticStyle:{"width":"100%"},attrs:{"data":item.list}},[_c('el-table-column',{attrs:{"prop":"source_type","label":"类型"}}),_vm._v(" "),_c('el-table-column',{attrs:{"prop":"change_amount","label":"金额"},scopedSlots:_vm._u([{key:"default",fn:function(scope){return _c('div',{class:{'primary': scope.row.change_type == 1}},[_vm._v("\n                                "+_vm._s(scope.row.change_amount)+"\n                            ")])}}],null,true)}),_vm._v(" "),_c('el-table-column',{attrs:{"prop":"create_time","label":"时间"}})],1)],1)])}),1)],2)],2)}
var staticRenderFns = []


// CONCATENATED MODULE: ./pages/user/user_wallet.vue?vue&type=template&id=7421fc7d&scoped=true&

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/user/user_wallet.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
/* harmony default export */ var user_walletvue_type_script_lang_js_ = ({
  head() {
    return {
      title: this.$store.getters.headTitle,
      link: [{
        rel: "icon",
        type: "image/x-icon",
        href: this.$store.getters.favicon
      }]
    };
  },

  layout: "user",

  data() {
    return {
      activeName: "all",
      userWallet: [{
        type: "all",
        list: [],
        name: "全部记录",
        count: 0,
        page: 1
      }, {
        type: "output",
        list: [],
        name: "收入记录",
        count: 0,
        page: 1
      }, {
        type: "income",
        list: [],
        name: "消费记录",
        count: 0,
        page: 1
      }]
    };
  },

  async asyncData({
    $get,
    query
  }) {
    let wallet = {};
    let recodeList = [];
    let walletRes = await $get("user/myWallet");
    let recodeRes = await $get("user/accountLog", {
      params: {
        page_no: 1,
        page_size: 10,
        source: 1,
        type: 0
      }
    });

    if (walletRes.code == 1) {
      wallet = walletRes.data;
    }

    if (recodeRes.code == 1) {
      recodeList = recodeRes.data.list;
    }

    return {
      wallet,
      recodeList
    };
  },

  fetch() {
    this.handleClick();
  },

  methods: {
    handleClick() {
      this.getRecodeList();
    },

    changePage(val) {
      this.userWallet.some(item => {
        if (item.type == this.activeName) {
          item.page = val;
        }
      });
      this.getRecodeList();
    },

    async getRecodeList() {
      const {
        activeName,
        userWallet
      } = this;
      let type = activeName == "all" ? 0 : activeName == "income" ? 2 : 1;
      const item = userWallet.find(item => item.type == activeName);
      const {
        data: {
          list,
          count
        },
        code
      } = await this.$get("user/accountLog", {
        params: {
          page_size: 10,
          page_no: item.page,
          type: type,
          source: 1
        }
      });

      if (code == 1) {
        this.recodeList = {
          list,
          count
        };
      }
    }

  },
  watch: {
    recodeList: {
      immediate: true,

      handler(val) {
        console.log("val:", val);
        this.userWallet.some(item => {
          if (item.type == this.activeName) {
            Object.assign(item, val);
            return true;
          }
        });
      }

    }
  }
});
// CONCATENATED MODULE: ./pages/user/user_wallet.vue?vue&type=script&lang=js&
 /* harmony default export */ var user_user_walletvue_type_script_lang_js_ = (user_walletvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(1);

// CONCATENATED MODULE: ./pages/user/user_wallet.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(317)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  user_user_walletvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  "7421fc7d",
  "70693cbb"
  
)

/* harmony default export */ var user_wallet = __webpack_exports__["default"] = (component.exports);

/***/ })

};;
//# sourceMappingURL=user_wallet.js.map