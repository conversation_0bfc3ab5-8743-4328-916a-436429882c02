<?php

/**
 * This file is part of the Carbon package.
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

/*
 * Authors: <AUTHORS>
 */
return array_replace_recursive(require __DIR__.'/en.php', [
    'formats' => [
        'L' => 'DD/MM/YYYY',
    ],
    'months' => ['Qunxa Garablu', '<PERSON><PERSON><PERSON>do', '<PERSON>ig<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>ah Alsa', '<PERSON>asa Dirri', '<PERSON><PERSON>rri', '<PERSON><PERSON><PERSON>i', '<PERSON>u', '<PERSON>teli', '<PERSON><PERSON><PERSON>', 'Kaxxa Garablu'],
    'months_short' => ['Qun', 'Nah', 'Cig', 'Agd', 'Cax', 'Qas', 'Qad', 'Leq', 'Way', 'Dit', 'Xim', 'Kax'],
    'weekdays' => ['Acaada', '<PERSON>tle<PERSON>', '<PERSON><PERSON><PERSON>', 'Arbaqa', 'Ka<PERSON><PERSON>', 'Gumqata', 'Sabti'],
    'weekdays_short' => ['Aca', 'Etl', 'Tal', 'Arb', 'Kam', 'Gum', 'Sab'],
    'weekdays_min' => ['Aca', 'Etl', 'Tal', 'Arb', 'Kam', 'Gum', 'Sab'],
    'first_day_of_week' => 1,
    'day_of_first_week_of_year' => 1,
    'meridiem' => ['saaku', 'carra'],
]);
