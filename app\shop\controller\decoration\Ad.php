<?php

namespace app\shop\controller\decoration;

use app\common\basics\ShopBase;
use app\common\enum\ShopAdEnum;
use app\common\model\shop\ShopAd;
use app\common\model\shop\ShopCategory;
use app\common\model\shop\ShopGoodsCategory;
use app\common\server\JsonServer;
use app\common\server\UrlServer;
use app\shop\logic\decoration\ShopAdLogic;
use think\response\Json;
use think\response\View;
use think\facade\Db;
use app\common\enum\PayEnum;
use think\facade\Log; // 引入 Log Facade

class Ad extends ShopBase
{
    /**
     * @notes 广告列表
     * @return Json|View
     * <AUTHOR>
     * @datetime 2023-12-05 10:06:46
     */
    function lists()
    {
        if ($this->request->isAjax()) {
            $data = ShopAdLogic::lists($this->request->get(), $this->shop_id);
            return ['code' => 1, 'msg' => '成功', 'data' => $data];
        }

        return view();
    }

    /**
     * @notes 新增广告
     * @return Json|View
     * <AUTHOR>
     * @datetime 2023-12-05 11:58:07
     */
    function add()
    {
        if ($this->request->isAjax()) {
            $result = ShopAdLogic::add(input(), $this->shop_id);
            if ($result) {
                return ['code' => 1, 'msg' => '添加成功'];
            }

            return ['code' => 0, 'msg' => ShopAdLogic::getError() ? : '添加失败'];
        }
        $ad=Db::name('ad_order')->alias('a')
            ->leftJoin('ad_position b', 'a.ad_position_id=b.id')
            ->field('a.id as ad_order_id,a.start_time,a.end_time,a.sort,a.ad_buynums,b.*')
            ->where('a.shop_id', $this->shop_id)
            ->where('a.end_time','>',time())
            ->where('a.status',1)
            ->select()->toArray();

        if($ad){
            foreach ($ad as $k=>$v){
                $buy_nums=Db::name('ad_order')
                    ->where('shop_id', $this->shop_id)
                    ->where('ad_position_id', $v['id'])
                    ->where('end_time','>',time())
                    ->where('status',1)
                    ->sum('ad_buynums');
                if(empty($buy_nums)){
                    unset($ad[$k]);
                }
                $have_nums=Db::name('ad')
                    ->where('shop_id', $this->shop_id)
                    ->where('ad_order_id', $v['ad_order_id'])
                    ->where('status', 1)
                    ->where('del', 0)
                    ->count();

               if($buy_nums<=$have_nums){
                   unset($ad[$k]);
               }
            }
        }


        return view('', [
            'placeList'     => ShopAdEnum::getPlaceDesc(),
            'terminalList'  => ShopAdEnum::getTerminal(),
            'ad'=>$ad
        ]);
    }

    /**
     * @notes 编辑广告
     * @return Json|View
     * <AUTHOR>
     * @datetime 2023-12-05 11:58:17
     */
    function edit()
    {

        if ($this->request->isAjax()) {
            $result = ShopAdLogic::edit(input(), $this->shop_id);
            if ($result) {
                return ['code' => 1, 'msg' => '编辑成功'];
            }

            return ['code' => 0, 'msg' => ShopAdLogic::getError() ? : '编辑失败'];
        }
        $ad=Db::name('ad')->alias('a')
            ->leftJoin('ad_position b', 'a.pid=b.id')
            ->field('b.name,b.id')
            ->where('a.shop_id', $this->shop_id)
            ->find();
        return view('', [
            'info'          => \app\common\model\Ad::where('id', input('id/d'))->where('shop_id', $this->shop_id)->findOrEmpty()->toArray(),
            'placeList'     => ShopAdEnum::getPlaceDesc(),
            'terminalList'  => ShopAdEnum::getTerminal(),
            'ad'=>$ad
        ]);
    }

    /**
     * @notes 设置状态
     * @return Json
     * <AUTHOR>
     * @datetime 2023-12-05 11:59:01
     */
    function status()
    {
        if ($this->request->isAjax()) {
            ShopAdLogic::status(input(), $this->shop_id);
            return JsonServer::success('成功');
        }
    }


    /**
     * @notes 设置状态
     * @return Json
     * <AUTHOR>
     * @datetime 2023-12-05 11:59:01
     */
    function shopstatus()
    {

        if ($this->request->isAjax()) {
            $post = $this->request->post();
            $result=ShopAdLogic::shopstatus($post, $this->shop_id);
            if ($result!==true) {
                return JsonServer::error($result);
            }
            return JsonServer::success('操作成功');
        }
    }

    /**
     * @notes 删除广告
     * @return Json
     * <AUTHOR>
     * @datetime 2023-12-05 11:58:34
     */
    function delete()
    {
        if ($this->request->isAjax()) {
            ShopAdLogic::delete(input(), $this->shop_id);
            return JsonServer::success('成功');
        }
    }


    /**
     * @notes 删除广告
     * @return Json
     * <AUTHOR>
     * @datetime 2023-12-05 11:58:34
     */
    function shopdelete()
    {
        if ($this->request->isAjax()) {
            ShopAdLogic::shopdelete(input(), $this->shop_id);
            return JsonServer::success('成功');
        }
    }

    /**
     * @notes 选择链接
     * @return Json|View
     * <AUTHOR>
     * @datetime 2023-12-05 18:33:00
     */
    function select_link()
    {
        if ($this->request->isAjax()) {

            return JsonServer::success('');
        }

        return view('', [
            'links'             => ShopAdEnum::getLinkPage(),
            'shopLinkPaths'     => ShopAdEnum::getShopLinkPaths(),
            'goodsCategoryList' => ShopGoodsCategory::where('shop_id', $this->shop_id)->where('is_show', 1)->select()->toArray(),
            'select_link'       => input('link/s', ''),
            'getShopGoodsListPath'      => ShopAdEnum::getShopGoodsListPath(),
            'getShopGoodsCategoryPath'  => ShopAdEnum::getShopGoodsCategoryPath(),
        ]);
    }


    /*
     * 购买广告位
     *
     *
     */
    function buylists()
    {
        if ($this->request->isAjax()) {
            return JsonServer::success('', ShopAdLogic::buylists($this->request->get(), $this->shop_id));
        }

        return view();
    }


    /*
     * 购买广告位
     *
     *
     */
    function buyinfo()
    {
        if ($this->request->isAjax()) {
            $post=$this->request->post();
            $post['id']=$this->request->get('id');
            $result=ShopAdLogic::AdOrderadd($post, $this->shop_id);
            if ($result) {
                return JsonServer::success('添加成功',['order_id'=>$result]);
            }else{
                return JsonServer::error(ShopAdLogic::getError() ? : '购买失败');
            }
        }
        $billing_cycle_txt = ['天', '周', '月', '年'];
        return view('',['detail'=>Db::name('ad_position')->where(['id'=>input('id')])->find(),'cycle'=>$billing_cycle_txt]);
    }

    /*
    *广告购买记录
    */
    function buyloglists()
    {
        if ($this->request->isAjax()) {
            $params = $this->request->get();
            $limit = $this->request->get('limit', 15);
            $page = $this->request->get('page', 1);

            $model = Db::name('ad_order')->alias('ao')
                ->leftJoin('ad_position ap', 'ao.ad_position_id = ap.id')
                ->leftJoin('ad a', 'a.ad_order_id = ao.id')
                ->where('ao.shop_id', $this->shop_id);

            // 搜索条件
            if (!empty($params['position_name'])) {
                $model->where('ap.name', 'like', '%' . trim($params['position_name']) . '%');
            }
            if (isset($params['status']) && $params['status'] !== '') {
                $status = intval($params['status']);
                if ($status == 0) {
                    // 未付款：订单状态为0
                    $model->where('ao.status', '=', 0);
                } elseif ($status == 1) {
                    // 未配置：已支付但未配置广告内容且未过期
                    $model->where('ao.status', '=', 1)->whereNull('a.id')->where('ao.end_time', '>', time());
                } elseif ($status == 2) {
                    // 已配置：已支付且已配置广告内容（不管是否过期）
                    $model->where(function($q) {
                        $q->where('ao.status', '=', 1)->whereNotNull('a.id')
                          ->whereOr('ao.status', '=', 2); // 状态为2的订单也认为是已配置
                    });
                } elseif ($status == 3) {
                    // 已失效：已过期且未配置
                    $model->where('ao.status', '=', 1)->where('ao.end_time', '<', time())->whereNull('a.id');
                }
            }
            if (!empty($params['start_time'])) {
                $model->where('ao.create_time', '>=', strtotime($params['start_time']));
            }
            if (!empty($params['end_time'])) {
                $model->where('ao.create_time', '<=', strtotime($params['end_time']));
            }

            $count = $model->group('ao.id')->count();
            // 需要复制 $model 查询对象，因为 count() 后无法再进行 field/order/page 操作
            $query = Db::name('ad_order')->alias('ao')
                ->leftJoin('ad_position ap', 'ao.ad_position_id = ap.id')
                ->leftJoin('ad a', 'a.ad_order_id = ao.id')
                ->where('ao.shop_id', $this->shop_id);

            // 重新应用搜索条件 (因为 $model 在 count 后状态改变)
            if (!empty($params['position_name'])) {
                $query->where('ap.name', 'like', '%' . trim($params['position_name']) . '%');
            }
            if (isset($params['status']) && $params['status'] !== '') {
                $status = intval($params['status']);
                if ($status == 0) {
                    // 未付款：订单状态为0
                    $query->where('ao.status', '=', 0);
                } elseif ($status == 1) {
                    // 未配置：已支付但未配置广告内容且未过期
                    $query->where('ao.status', '=', 1)->whereNull('a.id')->where('ao.end_time', '>', time());
                } elseif ($status == 2) {
                    // 已配置：已支付且已配置广告内容（不管是否过期）
                    $query->where(function($q) {
                        $q->where('ao.status', '=', 1)->whereNotNull('a.id')
                          ->whereOr('ao.status', '=', 2); // 状态为2的订单也认为是已配置
                    });
                } elseif ($status == 3) {
                    // 已失效：已过期且未配置
                    $query->where('ao.status', '=', 1)->where('ao.end_time', '<', time())->whereNull('a.id');
                }
            }
            if (!empty($params['start_time'])) {
                $query->where('ao.create_time', '>=', strtotime($params['start_time']));
            }
            if (!empty($params['end_time'])) {
                $query->where('ao.create_time', '<=', strtotime($params['end_time']));
            }

            $lists = $query->field('ao.*, ap.name as ad_position_name, ap.image as ad_position_image') // 获取所需字段
                ->group('ao.id') // 按广告订单ID分组，确保不重复
                ->order('ao.id desc')
                ->page($page, $limit)
                ->select()
                ->toArray();

            foreach ($lists as &$item) {
                // 处理广告位信息 (从 join 中获取)
                $item['ad_position_info'] = [
                    'name' => $item['ad_position_name'] ?? '未知',
                    'image' => $item['ad_position_image'] ?? ''
                ];

                // 处理广告位图片URL
                if (!empty($item['ad_position_info']['image'])) {
                    $item['ad_position_info']['image'] = \app\common\server\UrlServer::getFileUrl($item['ad_position_info']['image']);
                }

                unset($item['ad_position_name'], $item['ad_position_image']);

                // 根据订单状态和是否已配置广告更新状态文本
                if ($item['status'] == 0) {
                    // 未付款状态
                    $item['status_text'] = '未付款';
                    $item['status'] = 0;
                } elseif ($item['status'] == 2) {
                    // 订单状态为2，直接认为是已配置
                    $item['status_text'] = '已配置';
                    $item['status'] = 2;
                    // 获取广告内容数据
                    $ad_content_data = Db::name('ad')->where('ad_order_id', $item['id'])->find();
                    $item['_ad_content_cache'] = $ad_content_data;
                } else {
                    // 已支付状态（status=1），先检查是否已配置广告内容
                    $ad_content_data = Db::name('ad')->where('ad_order_id', $item['id'])->find();
                    if ($ad_content_data) {
                        // 已配置广告内容（不管是否过期）
                        $item['status_text'] = '已配置';
                        $item['status'] = 2;
                        $item['_ad_content_cache'] = $ad_content_data;
                    } else {
                        // 未配置广告内容，检查是否过期
                        if ($item['end_time'] > 0 && $item['end_time'] < time()) {
                            // 已过期且未配置
                            $item['status_text'] = '已失效';
                            $item['status'] = 3;
                        } else {
                            // 未过期且未配置
                            $item['status_text'] = '未配置';
                            $item['status'] = 1;
                        }
                        $item['_ad_content_cache'] = null;
                    }
                }
                // 计算剩余时间等
                if ($item['status'] >= 1 && $item['status'] < 3 && $item['start_time'] && $item['end_time']) {
                    $item['remaining_seconds'] = max(0, $item['end_time'] - time());
                    $item['remaining_days'] = floor($item['remaining_seconds'] / 86400);
                } else {
                    $item['remaining_seconds'] = 0;
                    $item['remaining_days'] = 0;
                }
                $item['create_time_text'] = date('Y-m-d H:i:s', $item['create_time']);
                $item['start_time_text'] = $item['start_time'] ? date('Y-m-d H:i:s', $item['start_time']) : '-';
                $item['end_time_text'] = $item['end_time'] ? date('Y-m-d H:i:s', $item['end_time']) : '-';

                // 使用缓存的广告内容数据
                $ad_content_data = $item['_ad_content_cache'] ?? null;
                if ($ad_content_data) {
                    // 如果找到了广告内容
                    $item['ad_content'] = $ad_content_data;
                    // 只有当内容存在且有图片时才生成 URL
                    if (!empty($item['ad_content']['image'])) {
                        $item['ad_content']['image_url'] = \app\common\server\UrlServer::getFileUrl($item['ad_content']['image']);
                    } else {
                        $item['ad_content']['image_url'] = null; // 确保 image_url 存在
                    }
                     if (empty($item['ad_content']['title'])) {
                        $item['ad_content']['title'] = null; // 确保 title 存在
                    }
                } else {
                    // 如果没找到广告内容，提供一个包含空默认值的结构
                    $item['ad_content'] = [
                        'title' => null,
                        'image' => null,
                        'image_url' => null,
                        // 可以根据 ad 表的结构添加其他字段的默认值
                    ];
                }

                // 清理临时缓存字段
                unset($item['_ad_content_cache']);
            }

            // 返回符合 layui table 的格式
            return JsonServer::success('', ['count' => $count, 'lists' => $lists]);
        }

        return view();
    }

    /**
     * @notes 查看广告购买记录详情 - 视图
     * @return View
     * <AUTHOR>
     */
    public function buyLogDetailView()
    {
        // 直接返回视图，视图内部通过 JS 请求数据
        return view('buylog_detail', [
            'id' => $this->request->get('id/d') // 将 ID 传递给视图，方便 JS 获取
        ]);
    }

    /**
     * @notes 获取广告购买记录详情 - 数据接口
     * @return Json
     * <AUTHOR>
     */
    public function buyLogDetail()
    {
        $id = $this->request->get('id/d');
        if (empty($id)) {
            return JsonServer::error('缺少订单ID');
        }

        $detail = Db::name('ad_order')->alias('ao')
            ->leftJoin('ad_position ap', 'ao.ad_position_id = ap.id')
            ->leftJoin('ad a', 'a.ad_order_id = ao.id') // 左连接广告内容表
            ->leftJoin('user u', 'ao.user_id = u.id') // 左连接用户表
            ->where('ao.id', $id)
            ->where('ao.shop_id', $this->shop_id) // 确保是当前商家的订单
            ->field([
                'ao.*',
                'ap.name as ad_position_name',
                'ap.image as ad_position_image',
                'ap.terminal as ad_terminal', // 查询终端类型
                'a.title as ad_title',
                'a.image as ad_image',
                'a.link_type',
                'a.link', // 修正：使用 a.link 替代 a.link_address
                'u.nickname as user_nickname' // 获取用户昵称
            ])
            ->find();

        if (!$detail) {
            return JsonServer::error('未找到订单记录或无权访问');
        }

        // 数据处理，类似 buyloglists
        $detail['ad_position_info'] = [
            'name' => $detail['ad_position_name'] ?? '未知',
            'image' => $detail['ad_position_image'] ?? null,
            'image_url' => ($detail['ad_position_image']) ? \app\common\server\UrlServer::getFileUrl($detail['ad_position_image']) : null,
            'terminal' => $detail['ad_terminal'] ?? null // 保存终端类型
        ];

        // --- 处理广告内容链接 --- START ---
        $link_type_text = '-';
        $full_link_address = '-';
        $terminal = $detail['ad_terminal']; // 获取终端

        if (isset($detail['link_type'])) {
            $link_type = intval($detail['link_type']);
            // $link_address_raw = $detail['link_address'] ?? ''; // 旧代码
            $link_address_raw = $detail['link'] ?? ''; // 修正：使用 $detail['link']

            switch ($link_type) {
                case 1: // 商城页面 (假设 link 存的是页面标识 key)
                    // 需要 AdEnum 类来处理
                    if (class_exists('\app\common\enum\AdEnum')) {
                        $pageInfo = \app\common\enum\AdEnum::getLinkPage($terminal, $link_address_raw);
                        if ($pageInfo && isset($pageInfo['name'])) {
                            $link_type_text = '商城页面: ' . $pageInfo['name'];
                        }
                        if ($pageInfo && isset($pageInfo['path'])) {
                            $full_link_address = $pageInfo['path'];
                        }
                    } else {
                         $link_type_text = '商城页面 (AdEnum 未找到)';
                         $full_link_address = $link_address_raw;
                    }
                    break;
                case 2: // 商品页面 (假设 link 存的是商品 ID)
                    $link_type_text = '商品页面';
                    if (class_exists('\app\common\enum\AdEnum')) {
                        $goodsBasePath = \app\common\enum\AdEnum::getGoodsPath($terminal);
                        $full_link_address = $goodsBasePath . '?id=' . $link_address_raw; // 拼接 ID
                    } else {
                        $link_type_text .= ' (AdEnum 未找到)';
                         $full_link_address = '?id=' . $link_address_raw;
                    }
                    break;
                case 3: // 自定义链接 (假设 link 存的是完整 URL)
                    $link_type_text = '自定义链接';
                    $full_link_address = $link_address_raw;
                    break;
                default:
                     $link_type_text = '无链接';
                    break;
            }
        }
        // --- 处理广告内容链接 --- END ---

        $detail['ad_content'] = [
            'title' => $detail['ad_title'] ?? null,
            'image' => $detail['ad_image'] ?? null,
            'image_url' => ($detail['ad_image']) ? \app\common\server\UrlServer::getFileUrl($detail['ad_image']) : null,
            'link_type' => $detail['link_type'] ?? null,
            // 'link_address' => $detail['link_address'] ?? null, // 旧代码
            'link' => $detail['link'] ?? null, // 修正：保存正确的 link 字段
            'link_type_text' => $link_type_text, // 使用上面计算的值
            'full_link_address' => $full_link_address // 使用上面计算的值
        ];

        $detail['status_text'] = PayEnum::getPayStatus($detail['status']);
        $detail['pay_way_text'] = isset($detail['pay_way']) ? PayEnum::getPayWay($detail['pay_way']) : '-'; // 支付方式文本

        if ($detail['status'] == PayEnum::ISPAID && $detail['start_time'] && $detail['end_time']) {
            $detail['remaining_seconds'] = max(0, $detail['end_time'] - time());
            $detail['remaining_days'] = floor($detail['remaining_seconds'] / 86400);
        } else {
            $detail['remaining_seconds'] = 0;
            $detail['remaining_days'] = 0;
        }

        $detail['create_time_text'] = date('Y-m-d H:i:s', $detail['create_time']);
        $detail['pay_time_text'] = (isset($detail['pay_time']) && $detail['pay_time']) ? date('Y-m-d H:i:s', $detail['pay_time']) : '-';
        $detail['start_time_text'] = $detail['start_time'] ? date('Y-m-d H:i:s', $detail['start_time']) : '-';
        $detail['end_time_text'] = $detail['end_time'] ? date('Y-m-d H:i:s', $detail['end_time']) : '-';

        // 清理不需要的原始字段
        unset($detail['ad_position_name'], $detail['ad_position_image'], $detail['ad_terminal'], $detail['ad_title'], $detail['ad_image'], $detail['link_type'], $detail['link']); // 修正：清理 terminal 和 link

        return JsonServer::success('获取成功', $detail);
    }

    /**
     * @notes 发起广告订单支付
     * @return Json
     * <AUTHOR>
     */
    public function initiateAdPayment()
    {
        $order_sn = $this->request->post('order_sn');
        if (empty($order_sn)) {
            return JsonServer::error('缺少订单号');
        }

        // 查找订单，并验证状态和归属
        $order = Db::name('ad_order')
            ->where('order_sn', $order_sn)
            ->where('shop_id', $this->shop_id)
            ->find();

        if (!$order) {
            // dump($this->shop_id); // 如果需要检查当前 shop_id，取消此行注释
            return JsonServer::error('订单不存在或不属于您');
        }

        if ($order['status'] != PayEnum::UNPAID) {
            // 如果订单已支付，并且有支付数据，尝试直接返回支付数据（防止重复支付）
            if ($order['status'] == PayEnum::ISPAID && !empty($order['pay_data'])) {
                 try {
                    $payData = json_decode($order['pay_data'], true);
                    // 简单验证 pay_data 是否看起来像支付参数
                    if ($payData && isset($payData['appId']) && isset($payData['paySign'])) {
                         return JsonServer::success('订单已支付，返回支付参数', $payData);
                    }
                 } catch (\Exception $e) {
                     // pay_data 解析失败，继续按未支付处理或提示错误
                     Log::error("解析已支付订单 {$order_sn} 的 pay_data 失败: " . $e->getMessage());
                 }
            }
            // 对于其他非未支付状态，返回错误
            return JsonServer::error('订单状态错误，无法支付');
        }

        // 确定支付方式 (这里暂时硬编码为微信支付)
        $pay_way = PayEnum::WECHAT_PAY;

        // 确定订单来源标识符
        $from = 'AdOrder'; // 修正：根据之前的讨论

        // 确定客户端类型 (请确认 5 是否正确代表 ClientEnum::SHOP)
        $client = 5; // 修正：根据之前的讨论

        try {
            // 检查 PayLogic 类和方法是否存在
            if (!class_exists('\app\api\logic\PayLogic') || !method_exists('\app\api\logic\PayLogic', 'wechatPay')) {
                 Log::error("支付处理类或方法不存在: \app\api\logic\PayLogic::wechatPay");
                 return JsonServer::error('支付处理逻辑不可用');
            }

            // 调用支付逻辑
            $payResult = \app\api\logic\PayLogic::wechatPay($order_sn, $from, $client, null); // 假设 user_id 传 null

            // --- 更详细地检查 $payResult ---
            $isValidPayResult = false;
            if (!empty($payResult) && (is_array($payResult) || is_object($payResult))) {
                // 转换为数组以便检查键
                $payResultArray = (array)$payResult;
                // 检查微信 JSAPI 支付所需的核心字段是否存在
                if (isset($payResultArray['appId']) &&
                    isset($payResultArray['timeStamp']) &&
                    isset($payResultArray['nonceStr']) &&
                    isset($payResultArray['package']) && // 注意大小写，通常是 'package'
                    isset($payResultArray['signType']) &&
                    isset($payResultArray['paySign'])) {
                    $isValidPayResult = true;
                }
            }

            if ($isValidPayResult) {
                // 支付参数看起来有效，更新订单的 pay_data (可选，但推荐)
                 Db::name('ad_order')
                    ->where('order_sn', $order_sn)
                    ->update(['pay_data' => json_encode($payResult, JSON_UNESCAPED_UNICODE)]);

                return JsonServer::success('支付参数生成成功', $payResult);
            } else {
                // $payResult 为空、不是数组/对象，或缺少关键字段
                $errorMsg = '未能生成有效的支付参数';
                $logMsg = "调用 PayLogic::wechatPay 返回无效结果。OrderSN: {$order_sn}, Result: " . json_encode($payResult, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
                Log::error($logMsg); // 记录详细日志

                // 尝试获取 PayLogic 中的错误信息
                if (method_exists('\app\api\logic\PayLogic', 'getError') && $logicError = \app\api\logic\PayLogic::getError()) {
                    $errorMsg .= ': ' . $logicError;
                }
                return JsonServer::error($errorMsg, [], 0, 1); // 返回 code: 0, show: 1
            }

        } catch (\Exception $e) {
            // 捕获支付逻辑中可能抛出的异常
            $logMsg = "发起广告订单支付时发生异常。OrderSN: {$order_sn}, Error: " . $e->getMessage() . "\nTrace: " . $e->getTraceAsString();
            Log::error($logMsg);
            return JsonServer::error('支付请求处理失败，请稍后重试或联系管理员');
        }
    }

    /**
     * @notes 显示配置页面
     * @return string
     * <AUTHOR>
     * @datetime 2025-05-30
     */
    public function config()
    {
        return view();
    }

    /**
     * @notes 获取广告订单信息
     * @return Json
     * <AUTHOR>
     * @datetime 2025-05-30
     */
    public function getAdOrderInfo()
    {
        $id = $this->request->param('id/d', 0);

        if (!$id) {
            return JsonServer::error('参数错误');
        }

        try {
            // 获取订单信息
            $orderInfo = Db::name('ad_order')
                ->alias('ao')
                ->leftJoin('ad_position ap', 'ao.ad_position_id = ap.id')
                ->where('ao.id', $id)
                ->where('ao.shop_id', $this->shop_id)
                ->field([
                    'ao.id', 'ao.order_sn', 'ao.ad_position_id', 'ao.ad_buynums',
                    'ao.ad_price', 'ao.start_time', 'ao.end_time', 'ao.status',
                    'ap.id as position_id', 'ap.name as position_name',
                    'ap.image as position_image', 'ap.width', 'ap.height'
                ])
                ->find();

            if (!$orderInfo) {
                return JsonServer::error('订单不存在');
            }

            // 组装广告位信息
            $orderInfo['ad_position_info'] = [
                'id' => $orderInfo['position_id'],
                'name' => $orderInfo['position_name'],
                'image' => $orderInfo['position_image'] ? UrlServer::getFileUrl($orderInfo['position_image']) : '',
                'width' => $orderInfo['width'],
                'height' => $orderInfo['height']
            ];

            return JsonServer::success('获取成功', $orderInfo);

        } catch (\Exception $e) {
            return JsonServer::error('获取失败：' . $e->getMessage());
        }
    }

    /**
     * @notes 配置广告内容
     * @return Json
     * <AUTHOR>
     * @datetime 2025-05-30
     */
    public function configAdContent()
    {
        if ($this->request->isAjax()) {
            $post = $this->request->post();
            if (empty($post['buy_log_id'])) {
                return JsonServer::error('缺少购买记录ID');
            }
            if (empty($post['title'])) {
                return JsonServer::error('请输入广告标题');
            }
            if (empty($post['image'])) {
                return JsonServer::error('请上传广告图片');
            }

            $buyLogId = $post['buy_log_id'];
            $order = Db::name('ad_order')
                ->where('id', $buyLogId)
                ->where('shop_id', $this->shop_id)
                ->find();

            if (!$order) {
                return JsonServer::error('购买记录不存在或不属于您');
            }

            // 检查订单状态：1=已支付，2=已配置
            if ($order['status'] != 1) {
                if ($order['status'] == 0) {
                    return JsonServer::error('订单未支付，无法配置广告内容');
                } elseif ($order['status'] == 2) {
                    return JsonServer::error('该订单已配置过广告内容');
                } else {
                    return JsonServer::error('订单状态异常，无法配置广告内容');
                }
            }

            // 检查是否已过期
            if ($order['end_time'] && $order['end_time'] < time()) {
                return JsonServer::error('订单已过期，无法配置广告内容');
            }

            // 检查是否已经存在广告内容（双重保险）
            $existingAd = Db::name('ad')
                ->where('ad_order_id', $buyLogId)
                ->where('del', 0)
                ->find();

            if ($existingAd) {
                return JsonServer::error('该订单已配置过广告内容，请勿重复配置');
            }

            // 使用事务确保数据一致性
            Db::startTrans();
            try {
                $data = [
                    'ad_order_id' => $buyLogId,
                    'pid' => $order['ad_position_id'],
                    'shop_id' => $this->shop_id,
                    'title' => trim($post['title']),
                    'image' => $post['image'],
                    'link' => trim($post['link'] ?? ''),
                    'description' => trim($post['description'] ?? ''),
                    'status' => 1,
                    'del' => 0,
                    'create_time' => time(),
                    'update_time' => time()
                ];

                // 插入广告内容
                $adId = Db::name('ad')->insertGetId($data);
                if (!$adId) {
                    throw new \Exception('插入广告内容失败');
                }

                // 更新订单状态为已配置
                $updateResult = Db::name('ad_order')
                    ->where('id', $buyLogId)
                    ->update([
                        'status' => 2,
                        'update_time' => time()
                    ]);

                if (!$updateResult) {
                    throw new \Exception('更新订单状态失败');
                }

                Db::commit();
                return JsonServer::success('配置成功');

            } catch (\Exception $e) {
                Db::rollback();
                return JsonServer::error('配置失败：' . $e->getMessage());
            }
        }
        return JsonServer::error('无效的请求方式');
    }
}
