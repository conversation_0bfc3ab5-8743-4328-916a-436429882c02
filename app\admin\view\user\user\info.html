{layout name="layout2" /}
<style>
    .layui-form-label{
        width: 120px;
    }
    .layui-card .layui-tab{
        margin-left: 7px;
    }
    .layui-form-label{
        margin-left: 20px;
        width: 98px;
    }
    .layui-input-inline{
        width:160px;
    }
    .layui-table-cell {
        height: auto;
    }

</style>
<div class="layui-form" lay-filter="layuiadmin-form-user_group" id="layuiadmin-form-user_group" style="padding: 20px 30px 0 0;">
    <div class="layui-form-item div-flex">
        <fieldset class="layui-elem-field layui-field-title">
            <legend>基础信息</legend>
        </fieldset>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">用户编号：</label>
        <div class="layui-input-inline">
            <label class="layui-form-mid">{$detail.sn}</label>
        </div>
        <label class="layui-form-label">用户昵称：</label>
        <div class="layui-input-inline">
            <label class="layui-form-mid">{$detail.nickname}</label>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">会员头像：</label>
        <div class="layui-input-inline">
            <label class="layui-form-mid">
                <img class="image-show" src="{$detail.avatar}" width="80px" height="80px">
            </label>
        </div>
        <label class="layui-form-label">手机号码：</label>
        <div class="layui-input-inline">
            <label class="layui-form-mid">{$detail.mobile}</label>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">用户标签：</label>
        <div class="layui-input-inline">
            <label class="layui-form-mid">{$detail.tag_str}</label>
        </div>
        <label class="layui-form-label">活跃度：</label>
        <div class="layui-input-inline">
            <label class="layui-form-mid">{$detail.user_growth}</label>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">性别：</label>
        <div class="layui-input-inline">
            <label class="layui-form-mid">{$detail.sex}</label>
        </div>
        <label class="layui-form-label">生日：</label>
        <div class="layui-input-inline">
            <label class="layui-form-mid">{$detail.birthday}</label>
        </div>
    </div>
    <div class="layui-input-item">

    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">注册来源：</label>
        <div class="layui-input-inline">
            <label class="layui-form-mid">{$detail.client_desc}</label>
        </div>
        <label class="layui-form-label">注册时间：</label>
        <div class="layui-input-inline">
            <label class="layui-form-mid">{$detail.create_time}</label>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">最后登录：</label>
        <div class="layui-input-inline">
            <label class="layui-form-mid">{$detail.login_time}</label>
        </div>
        <label class="layui-form-label">用户状态：</label>
        <div class="layui-input-inline">
            <label class="layui-form-mid">
                {$detail.disable ? '禁用' : '正常'}
                {$detail.user_delete ? '（<span style="color: red">已注销</span>）' : ''}
            </label>
        </div>
    </div>
    <div class="layui-form-item div-flex">
        <fieldset class="layui-elem-field layui-field-title">
            <legend>钱包信息</legend>
        </fieldset>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">总资产：</label>
        <div class="layui-input-inline">
            <label class="layui-form-mid">¥ {$detail.assets}</label>
        </div>
        <label class="layui-form-label">不可提现余额：</label>
        <div class="layui-input-inline">
            <label class="layui-form-mid">¥ {$detail.user_money}</label>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">可提现余额：</label>
        <div class="layui-input-inline">
            <label class="layui-form-mid">¥ {$detail.earnings}</label>
        </div>
    </div>
    <div class="layui-form-item div-flex">
        <fieldset class="layui-elem-field layui-field-title">
            <legend>消费能力</legend>
        </fieldset>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">成交订单笔数：</label>
        <div class="layui-input-inline">
            <label class="layui-form-mid">{$detail.order_num}</label>
        </div>
        <label class="layui-form-label">消费金额：</label>
        <div class="layui-input-inline">
            <label class="layui-form-mid">¥ {$detail.total_amount}</label>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">客单价：</label>
        <div class="layui-input-inline">
            <label class="layui-form-mid">¥ {$detail.avg_amount}</label>
        </div>
    </div>
    <div class="layui-form-item div-flex"  style="display: none;">
        <fieldset class="layui-elem-field layui-field-title">
            <legend>推荐信息</legend>
        </fieldset>
    </div>
    <div class="layui-form-item" style="display: none;">
        <label class="layui-form-label">上级推荐人：</label>
        <div class="layui-input-inline" style="width: 100px;">
            {if $detail.first_leader_info != '系统'}
            <label class="layui-form-mid" style="width:300px">{$detail.first_leader_info.nickname}({$detail.first_leader_info.sn})</label>
            {else}
            <label class="layui-form-mid">{$detail.first_leader_info}</label>
            {/if}
        </div>
    </div>
    <div class="layui-form-item"  style="display: none;">
        <label class="layui-form-label">推荐下级人数：</label>
        <div class="layui-input-inline">
            <label class="layui-form-mid">{$detail.fans}人</label>
        </div>
        <div class="layui-input-inline">
            <button class="layui-btn layui-btn-sm layui-bg-blue" id="showFans" data-id="{$detail.id}">查看推荐下级</button>
        </div>
    </div>
</div>

<script>
    layui.config({
        version:"{$front_version}",
        base: '/static/lib/' //静态资源所在路径
    }).use(['jquery', 'layer'], function(){
        let $ = layui.jquery;
        let layer = layui.layer;

        // 查看推荐下级
        $('#showFans').click(function() {
            let id = $(this).data('id');
            // 弹窗显示添加页
            layer.open({
                type: 2
                ,title: "查看下级推荐"
                ,content: "{:url('user.user/fans')}?id=" + id
                ,area: ["90%", "90%"]
                ,btn: ["返回"]
            });
        });
    });
</script>