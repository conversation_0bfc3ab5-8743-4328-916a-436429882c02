<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{$view_env_name}{$config.name}</title>
    <link rel="shortcut icon" href="{$storageUrl}{$config.web_favicon}"/>
    <link rel="stylesheet" href="__PUBLIC__/static/lib/layui/css/layui.css">
    <link rel="stylesheet" href="__PUBLIC__/static/admin/css/app.css">
     <link rel="stylesheet" href="__PUBLIC__/static/admin/fontawesome/all.min.css">
    <link rel="stylesheet" href="__PUBLIC__/static/admin/css/layout-fix.css">
    <script src="__PUBLIC__/static/lib/layui/layui.js"></script>
    <script src="__PUBLIC__/static/admin/js/app.js"></script>
    <script src="__PUBLIC__/static/admin/js/layout-fix.js"></script>
    <style>
        /* CSS变量定义 - 支持主题切换 */
        :root {
            /* 浅色主题 */
            --bg-primary: #f8fafc;
            --bg-secondary: #ffffff;
            --bg-tertiary: #f1f5f9;
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --text-muted: #94a3b8;
            --border-color: #e2e8f0;
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
            --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --gradient-success: linear-gradient(135deg, #4ade80 0%, #22c55e 100%);
            --gradient-warning: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
            --gradient-danger: linear-gradient(135deg, #f87171 0%, #ef4444 100%);
            --gradient-info: linear-gradient(135deg, #60a5fa 0%, #3b82f6 100%);
            --accent-primary: #667eea;
            --accent-success: #10b981;
            --accent-warning: #f59e0b;
            --accent-danger: #ef4444;
            --accent-info: #3b82f6;
            --sidebar-width: 200px;
            --header-height: 50px;
            --tabs-height: 40px;
        }

        /* 深色主题 */
        [data-theme="dark"] {
            --bg-primary: #0f172a;
            --bg-secondary: #1e293b;
            --bg-tertiary: #334155;
            --text-primary: #f8fafc;
            --text-secondary: #cbd5e1;
            --text-muted: #64748b;
            --border-color: #475569;
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.4);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.4);
        }

        /* 紫色主题 */
        [data-theme="purple"] {
            --bg-primary: #faf5ff;
            --bg-secondary: #ffffff;
            --bg-tertiary: #f3e8ff;
            --text-primary: #581c87;
            --text-secondary: #7c3aed;
            --text-muted: #a855f7;
            --border-color: #e9d5ff;
            --gradient-primary: linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%);
            --accent-primary: #8b5cf6;
        }

        /* 绿色主题 */
        [data-theme="green"] {
            --bg-primary: #f0fdf4;
            --bg-secondary: #ffffff;
            --bg-tertiary: #dcfce7;
            --text-primary: #14532d;
            --text-secondary: #16a34a;
            --text-muted: #22c55e;
            --border-color: #bbf7d0;
            --gradient-primary: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
            --accent-primary: #22c55e;
        }

        /* 蓝色主题 */
        [data-theme="blue"] {
            --bg-primary: #eff6ff;
            --bg-secondary: #ffffff;
            --bg-tertiary: #dbeafe;
            --text-primary: #1e3a8a;
            --text-secondary: #3b82f6;
            --text-muted: #60a5fa;
            --border-color: #bfdbfe;
            --gradient-primary: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            --accent-primary: #3b82f6;
        }

        /* 基础样式重置 */
        * {
            box-sizing: border-box;
        }

        html, body {
            background: var(--bg-primary) !important;
            color: var(--text-primary) !important;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
            transition: all 0.3s ease;
            margin: 0;
            padding: 0;
            height: 100%;
        }

        /* 主题切换器 - 改为下拉式，避免遮挡 */
        .theme-switcher {
            position: relative;
            display: inline-block;
        }

        .theme-toggle-btn {
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 8px 12px;
            cursor: pointer;
            color: var(--text-primary);
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 6px;
            transition: all 0.3s ease;
        }

        .theme-toggle-btn:hover {
            background: var(--bg-tertiary);
            color: var(--accent-primary);
        }

        .theme-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            box-shadow: var(--shadow-lg);
            padding: 8px;
            display: none;
            z-index: 9999;
            min-width: 160px;
        }

        .theme-dropdown.show {
            display: block;
        }

        .theme-options {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .theme-option {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
            color: var(--text-primary);
            font-size: 14px;
        }

        .theme-option:hover {
            background: var(--bg-tertiary);
            color: var(--accent-primary);
        }

        .theme-option.active {
            background: var(--accent-primary);
            color: white;
        }

        .theme-color-indicator {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            border: 2px solid var(--border-color);
        }

        .theme-light .theme-color-indicator { background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%); }
        .theme-dark .theme-color-indicator { background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%); }
        .theme-purple .theme-color-indicator { background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%); }
        .theme-green .theme-color-indicator { background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%); }
        .theme-blue .theme-color-indicator { background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%); }

        /* 重写头部样式 */
        .layui-header {
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            right: 0 !important;
            z-index: 1000 !important;
            height: var(--header-height) !important;
            line-height: var(--header-height) !important;
            background: var(--bg-secondary) !important;
            border-bottom: 1px solid var(--border-color) !important;
            box-shadow: var(--shadow-sm) !important;
            padding: 0 24px !important;
        }

        .layui-header .layui-nav {
            background: transparent !important;
        }

        .layui-header .layui-nav .layui-nav-item {
            height: var(--header-height) !important;
            line-height: var(--header-height) !important;
        }

        .layui-header .layui-nav .layui-nav-item a,
        .layui-header .layui-nav .layui-nav-item .layui-icon {
            color: var(--text-primary) !important;
            transition: all 0.3s ease;
        }

        .layui-header .layui-nav .layui-nav-item a:hover {
            color: var(--accent-primary) !important;
            background: var(--bg-tertiary) !important;
            border-radius: 8px !important;
        }

        .layui-header .layui-layout-left {
            left: var(--sidebar-width) !important;
        }

        .layui-header .layui-layout-left .layui-nav-item a {
            padding: 8px 12px !important;
            border-radius: 8px !important;
            margin: 0 4px !important;
        }

        .layui-header .layui-layout-right .layui-nav-item a {
            padding: 8px 16px !important;
            border-radius: 8px !important;
            margin: 0 4px !important;
        }

        /* 用户信息下拉菜单 */
        .layui-header .userinfo.layui-nav-child {
            top: calc(var(--header-height) + 8px) !important;
            background: var(--bg-secondary) !important;
            border: 1px solid var(--border-color) !important;
            border-radius: 12px !important;
            box-shadow: var(--shadow-lg) !important;
            padding: 8px !important;
        }

        .layui-header .userinfo.layui-nav-child dd a {
            color: var(--text-primary) !important;
            padding: 8px 16px !important;
            border-radius: 8px !important;
            transition: all 0.3s ease !important;
        }

        .layui-header .userinfo.layui-nav-child dd a:hover {
            background: var(--bg-tertiary) !important;
            color: var(--accent-primary) !important;
        }
        /* 重写侧边栏样式 */
        .layui-sidebar {
            background: var(--bg-secondary) !important;
            border-right: 1px solid var(--border-color) !important;
        }

        /* Logo区域 */
        .layui-sidebar .layui-logo {
            background: var(--bg-secondary) !important;
            border-bottom: 1px solid var(--border-color) !important;
        }

        .layui-sidebar .layui-logo img {
            height: 20px !important;
            width: auto !important;
        }

        /* 菜单容器 */
        .layui-sidebar .layui-side-menu {
            background: var(--bg-secondary) !important;
        }

        /* 菜单项 */
        .layui-sidebar .layui-side-menu li > a {
            color: var(--text-secondary) !important;
        }

        .layui-sidebar .layui-side-menu li > a:hover {
            color: var(--accent-primary) !important;
        }

        .layui-sidebar .layui-side-menu li > a.active {
            color: var(--accent-primary) !important;
            background: var(--bg-tertiary) !important;
        }

        /* 子菜单主题适配 */
        .layui-sidebar .layui-side-menu li .child-menu {
            background: var(--bg-secondary, #FFFFFF) !important;
            border-right: 1px solid var(--border-color, #eee) !important;
        }

        .layui-sidebar .layui-side-menu li .child-menu dt strong {
            color: var(--text-primary, #333) !important;
            border-bottom: 1px solid var(--border-color, #eee) !important;
        }

        .layui-sidebar .layui-side-menu li .child-menu dd a {
            color: var(--text-secondary, #666) !important;
        }

        .layui-sidebar .layui-side-menu li .child-menu dd a.active {
            color: var(--accent-primary, #3A91FB) !important;
        }

        .layui-sidebar .layui-side-menu li .child-menu dd a:hover {
            color: var(--accent-primary, #3A91FB) !important;
        }

        /* 标签页样式 - 改进配色 */
        .lay-pagetabs {
            background: var(--bg-secondary) !important;
            border-bottom: 1px solid var(--border-color) !important;
            box-shadow: var(--shadow-sm) !important;
        }

        .lay-pagetabs .lay-tabs-control {
            background: var(--bg-secondary) !important;
            color: var(--text-secondary) !important;
            border-left: 1px solid var(--border-color) !important;
            transition: all 0.3s ease !important;
        }

        .lay-pagetabs .lay-tabs-control:hover {
            background: var(--bg-tertiary) !important;
            color: var(--accent-primary) !important;
        }

        /* 标签页内容 - 改进配色和样式 */
        .lay-pagetabs .layui-tab-title li {
            background: var(--bg-secondary) !important;
            color: var(--text-secondary) !important;
            border-right: 1px solid var(--border-color) !important;
            transition: all 0.3s ease !important;
            position: relative !important;
            border-radius: 8px 8px 0 0 !important;
            margin-right: 2px !important;
        }

        .lay-pagetabs .layui-tab-title li:hover {
            background: var(--bg-tertiary) !important;
            color: var(--text-primary) !important;
            transform: translateY(-1px) !important;
        }

        .lay-pagetabs .layui-tab-title li.layui-this {
            background: linear-gradient(135deg, var(--accent-primary) 0%, var(--accent-primary) 100%) !important;
            color: white !important;
            box-shadow: var(--shadow-md) !important;
            border-color: var(--accent-primary) !important;
        }

        .lay-pagetabs .layui-tab-title li.layui-this::after {
            content: '';
            position: absolute;
            bottom: -1px;
            left: 0;
            right: 0;
            height: 2px;
            background: var(--accent-primary);
            border-radius: 2px;
        }

        .lay-pagetabs .layui-tab-title li .layui-tab-close {
            background: rgba(255, 255, 255, 0.2) !important;
            color: var(--text-secondary) !important;
            border-radius: 50% !important;
            transition: all 0.3s ease !important;
        }

        .lay-pagetabs .layui-tab-title li .layui-tab-close:hover {
            background: #ff4757 !important;
            color: white !important;
            transform: scale(1.1) !important;
        }

        .lay-pagetabs .layui-tab-title li.layui-this .layui-tab-close {
            background: rgba(255, 255, 255, 0.3) !important;
            color: white !important;
        }

        /* 主体区域 - 修复内容被遮挡问题 */
        .layui-body {
            position: absolute !important;
            left: var(--sidebar-width) !important;
            top: calc(var(--header-height) + var(--tabs-height)) !important;
            right: 0 !important;
            bottom: 0 !important;
            background: var(--bg-primary) !important;
            padding: 0 !important;
            overflow: auto !important;
            box-sizing: border-box !important;
            transition: left 0.3s ease !important;
        }

        /* 没有二级菜单时的布局调整 - 提高优先级 */
        body.no-submenu .layui-sidebar {
            width: 110px !important;
        }

        body.no-submenu .layui-body {
            left: 110px !important;
            position: absolute !important;
        }

        body.no-submenu .layui-header .layui-layout-left {
            left: 110px !important;
        }

        /* 有二级菜单时的布局 - 提高优先级 */
        body.has-submenu .layui-sidebar {
            width: 232px !important;
        }

        body.has-submenu .layui-body {
            left: 232px !important;
            position: absolute !important;
        }

        body.has-submenu .layui-header .layui-layout-left {
            left: 232px !important;
        }

        .layui-body .lay-tabsbody-item {
            width: 100% !important;
            height: 100% !important;
            display: none !important;
            border-radius: 12px !important;
            overflow: visible !important;
            box-shadow: var(--shadow-md) !important;
            padding: 12px !important;
            margin: 0 !important;
            box-sizing: border-box !important;
        }

        .layui-body .lay-tabsbody-item.layui-show {
            display: block !important;
        }

        .layui-body .lay-iframe {
            width: 100% !important;
            height: 100% !important;
            border: 0 !important;
            border-radius: 8px !important;
            background: var(--bg-secondary) !important;
            overflow: auto !important;
            margin: 0 !important;
            padding: 0 !important;
        }

        /* 响应式设计 */
        @media (max-width: 1024px) {
            :root {
                --sidebar-width: 200px;
            }
        }

        @media (max-width: 768px) {
            :root {
                --sidebar-width: 200px;
                --header-height: 56px;
                --tabs-height: 44px;
            }

            .theme-toggle-btn {
                padding: 6px 8px;
                font-size: 12px;
            }

            .theme-toggle-btn span {
                display: none;
            }

            .layui-header {
                padding: 0 16px !important;
            }

            .layui-sidebar .layui-logo {
                padding: 0 16px !important;
            }

            .layui-sidebar .layui-side-menu li {
                margin: 0 8px 4px 8px !important;
            }

            .layui-sidebar .layui-side-menu li > a {
                padding: 0 12px !important;
                font-size: 13px !important;
            }

            .layui-sidebar .layui-side-menu li > a > i {
                margin-right: 8px !important;
                font-size: 14px !important;
            }

            .lay-pagetabs {
                padding: 0 60px 0 12px !important;
            }

            .lay-pagetabs .layui-tab-title li {
                max-width: 150px !important;
                padding: 0 32px 0 12px !important;
            }

            .layui-body {
                padding: 12px !important;
            }
        }

        @media (max-width: 480px) {
            :root {
                --sidebar-width: 60px;
            }

            .theme-toggle-btn {
                padding: 4px 6px;
                font-size: 10px;
            }

            .theme-dropdown {
                min-width: 140px;
                right: -20px;
            }

            .layui-sidebar .layui-side-menu li > a cite {
                display: none !important;
            }

            .layui-sidebar .layui-side-menu li > a {
                justify-content: center !important;
                padding: 0 !important;
            }

            .layui-sidebar .layui-side-menu li > a > i {
                margin-right: 0 !important;
            }

            .layui-sidebar .layui-side-menu li .child-menu {
                left: 60px !important;
                width: 200px !important;
            }

            .lay-pagetabs .layui-tab-title li {
                max-width: 100px !important;
                font-size: 12px !important;
            }
        }

        /* 动画效果 */
        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        @keyframes fadeInUp {
            from {
                transform: translateY(20px);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        .animate-slide-in-right {
            animation: slideInRight 0.3s ease-out;
        }

        .animate-fade-in-up {
            animation: fadeInUp 0.3s ease-out;
        }

        /* 加载状态 */
        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            border-radius: 12px;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid var(--border-color);
            border-top: 4px solid var(--accent-primary);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 滚动条美化 */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        ::-webkit-scrollbar-track {
            background: var(--bg-tertiary);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb {
            background: var(--border-color);
            border-radius: 4px;
            transition: all 0.3s ease;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--text-muted);
        }

        /* 选择文本样式 */
        ::selection {
            background: var(--accent-primary);
            color: white;
        }

        ::-moz-selection {
            background: var(--accent-primary);
            color: white;
        }

        /* 布局控制 - 最高优先级，彻底解决遮挡问题 */
        .layui-body {
            left: 110px !important;
            transition: left 0.3s ease !important;
            position: absolute !important;
        }

        /* 当有二级菜单显示时 - 使用CSS :has选择器 */
        body:has(.layui-sidebar .layui-side-menu li > dl[style*="display: block"]) .layui-body,
        body.has-submenu .layui-body {
            left: 240px !important;
        }

        body:has(.layui-sidebar .layui-side-menu li > dl[style*="display: block"]) .layui-header .layui-layout-left,
        body.has-submenu .layui-header .layui-layout-left {
            left: 240px !important;
        }

        /* 没有二级菜单时 */
        body.no-submenu .layui-body {
            left: 110px !important;
        }

        body.no-submenu .layui-header .layui-layout-left {
            left: 110px !important;
        }

        /* 侧边栏宽度控制 */
        .layui-sidebar {
            width: 110px !important;
        }
    </style>
</head>
<body>


    <!-- 头部区域 -->
    <div class="layui-header">
        <ul class="layui-nav layui-layout-left">
            <li class="layui-nav-item">
                <a class="refresh" href="javascript:" title="刷新">
                   <i class="layui-icon layui-icon-refresh-3"></i>
                </a>
            </li>
        </ul>
        <ul class="layui-nav layui-layout-right">
            <li class="layui-nav-item layui-hide-xs">
                <a class="fullscreen" href="javascript:">
                    <i class="layui-icon layui-icon-screen-full"></i>
                </a>
            </li>
            <!-- 主题切换器 -->
            <!-- <li class="layui-nav-item">
                <div class="theme-switcher" >
                    <div class="theme-toggle-btn" onclick="toggleThemeDropdown()" style="height:50px;">
                        <i class="fas fa-palette"></i>
                        <span>主题</span>
                        <i class="layui-icon layui-icon-down"></i>
                    </div>
                    <div class="theme-dropdown" id="themeDropdown">
                        <div class="theme-options">
                            <div class="theme-option theme-light active" data-theme="light">
                                <div class="theme-color-indicator"></div>
                                <span>浅色主题</span>
                            </div>
                            <div class="theme-option theme-dark" data-theme="dark">
                                <div class="theme-color-indicator"></div>
                                <span>深色主题</span>
                            </div>
                            <!-- <div class="theme-option theme-purple" data-theme="purple">
                                <div class="theme-color-indicator"></div>
                                <span>紫色主题</span>
                            </div>
                            <div class="theme-option theme-green" data-theme="green">
                                <div class="theme-color-indicator"></div>
                                <span>绿色主题</span>
                            </div>
                            <div class="theme-option theme-blue" data-theme="blue">
                                <div class="theme-color-indicator"></div>
                                <span>蓝色主题</span>
                            </div> -->
                        <!-- </div>
                    </div>
                </div>
            </li> -->
            <li class="layui-nav-item">
                <a href="javascript:">
                    <cite>{$admin_name}（{$role_name}）</cite>
                <span class="layui-nav-more"></span></a>
                <dl class="layui-nav-child layui-anim layui-anim-upbit userinfo">
<!--                    <dd><a lay-id="u-1" href="javascript:" data-url="pages/member/user.html">个人中心<span class="layui-badge-dot"></span></a></dd>-->
<!--                    <dd><hr></dd>-->
                    <dd><a href="{:url('login/logout')}" id="logout">退出登录</a></dd>
                </dl>
            </li>
        </ul>
    </div>

    <!-- 菜单区域 -->
    <div class="layui-sidebar ">
        <div class="layui-logo">
            <img style="height:20px;width: 77px" src="{$storageUrl}{$config.backstage_logo}">
        </div>
        <ul class="layui-side-menu">
            {volist name="menu" id="vo"}
                <li>
                    {empty name="vo.sub"}

                    <a lay-id="{$vo.id==466?0:$vo.id}" lay-href="{:url($vo.uri)}">
                        <i class="layui-icon {$vo.icon}"></i>
                        <cite>{$vo.name}</cite>
                    </a>
                    {else/}
                        <a href="javascript:">
                            <i class="layui-icon {$vo.icon}"></i>
                            <cite>{$vo.name}</cite>
                        </a>

                        <dl class="child-menu">
                            <dt><strong>{$vo.name}</strong></dt>
                            {volist name="vo.sub" id="second"}
                                <dd>

                                    {empty name="second.sub"}
                                        <a lay-id="{$second.id}" lay-href="{:url($second.uri)}">{$second.name}</a>
                                    {else /}
                                        <a class="child-menu-title">
                                            <i class="layui-icon layui-icon-triangle-d"></i>
                                            <cite>{$second.name}</cite>
                                        </a>
                                        <dl>
                                            {volist name="second.sub" id="third"}
                                            <dd><a lay-id="{$third.id}" lay-href="{:url($third.uri)}">{$third.name}</a></dd>
                                            {/volist}
                                        </dl>
                                    {/empty}
                                </dd>
                            {/volist}
                        </dl>
                    {/empty}
                </li>
            {/volist}
        </ul>
    </div>

    <!-- 标签区域 -->
    <div class="lay-pagetabs" id="LAY_app_tabs">
        <div class="layui-icon lay-tabs-control layui-icon-prev" lay-event="leftPage"></div>
        <div class="layui-icon lay-tabs-control layui-icon-next" lay-event="rightPage"></div>
        <div class="layui-icon lay-tabs-control layui-icon-down">
            <ul class="layui-nav lay-tabs-select" lay-filter="lay-pagetabs-nav">
                <li class="layui-nav-item" lay-unselect>
                    <a href="javascript:"><span class="layui-nav-more"></span></a>
                    <dl class="layui-nav-child layui-anim-fadein11 ">
                        <dd lay-event="closeThisTabs"><a href="javascript:">关闭当前标签页</a></dd>
                        <dd lay-event="closeOtherTabs"><a href="javascript:">关闭其它标签页</a></dd>
                        <dd lay-event="closeAllTabs"><a href="javascript:">关闭全部标签页</a></dd>
                    </dl>
                </li>
            </ul>
        </div>
        <div class="layui-tab" lay-unauto lay-allowclose="true" lay-filter="lay-layout-tabs" id="lay-layout-tabs ul">
            <ul class="layui-tab-title" id="LAY_app_tabsheader">
                <li lay-id="0" lay-attr="{:url('index/stat')}" class="layui-this">工作台<i class="layui-icon layui-tab-close">ဆ</i></li>

            </ul>
        </div>
    </div>

    <!-- 主体区域 -->
    <div class="layui-body" id="LAY_app_body">
        <div lay-id="0" class="lay-tabsbody-item layui-show">
            <iframe src="{:url('index/stat')}" class="lay-iframe" name="view_frame_0"></iframe>
        </div>
    </div>
<script>
    // 确保 layui 加载完成后再执行
    layui.use(['element', 'jquery'], function(){
        var $ = layui.jquery;
        var element = layui.element;

        // 监听 Tab 切换，为新打开的 iframe 设置 name
        element.on('tab(lay-layout-tabs)', function(data){
            var iframe = $(this.elem).find('.layui-tab-item.layui-show iframe.lay-iframe');
            if (iframe.length > 0 && !iframe.attr('name')) {
                // 为iframe设置一个唯一的name，如果还没有的话
                iframe.attr('name', 'view_frame_' + data.index);
            }
        });

        // 页面加载时，为初始的iframe设置name
        var initialIframe = $('#LAY_app_body').find('iframe.lay-iframe[name="view_frame"]');
        if(initialIframe.length > 0){
            initialIframe.attr('name', 'view_frame_0');
        }
    });

    // 布局初始化函数 - 处理没有二级菜单时的布局调整
    window.initializeLayout = function() {
        // 检查当前是否有显示的二级菜单
        var hasVisibleSubmenu = false;
        var allSubmenus = document.querySelectorAll('.layui-sidebar > .layui-side-menu li > dl');

        for(var i = 0; i < allSubmenus.length; i++) {
            var style = window.getComputedStyle(allSubmenus[i]);
            if(style.display === 'block') {
                hasVisibleSubmenu = true;
                break;
            }
        }

        console.log('[Layout] Has visible submenu:', hasVisibleSubmenu);

        if (hasVisibleSubmenu) {
            document.body.classList.remove('no-submenu');
            document.body.classList.add('has-submenu');
            console.log('[Layout] Applied has-submenu layout');

            // 确保有二级菜单时主体内容不被遮挡
            var body = document.querySelector('.layui-body');
            var header = document.querySelector('.layui-header .layui-layout-left');
            if(body) body.style.left = '232px';
            if(header) header.style.left = '232px';

        } else {
            document.body.classList.remove('has-submenu');
            document.body.classList.add('no-submenu');
            console.log('[Layout] Applied no-submenu layout');

            // 确保没有二级菜单时主体内容紧挨着一级菜单
            var body = document.querySelector('.layui-body');
            var header = document.querySelector('.layui-header .layui-layout-left');
            if(body) body.style.left = '110px';
            if(header) header.style.left = '110px';
        }
    };

    // 页面加载完成后立即执行
    document.addEventListener('DOMContentLoaded', function() {
        window.initializeLayout();
        setTimeout(window.initializeLayout, 100);
        setTimeout(window.initializeLayout, 500);
    });

    // 监听菜单点击事件
    document.addEventListener('click', function(e) {
        var target = e.target;
        if (target.closest('.layui-sidebar .layui-side-menu li > a')) {
            setTimeout(window.initializeLayout, 100);
        }
    });

    // 窗口加载完成后也执行一次
    window.addEventListener('load', function() {
        setTimeout(window.initializeLayout, 100);
    });
</script>
<script>
    // //保留这个空的script标签，以防后续需要添加其他脚本
    // function refreshHighlight(url,id) {
    //     console.log(url);
    //     $ = layui.jquery;

    //     $(".layui-nav[lay-filter='lay-pagetabs-nav'] a").each(function (ind, val) {
    //         if($(this).attr('href') === url){
    //             $('.layui-nav dd').removeClass('layui-this');

    //             $(this).parent('dd').addClass('layui-this');
    //         console.log(id);

    //         }else{
    //             var html = '<li lay-id="'+id+'" lay-attr="'+url+'" class="layui-this">工作台2<i class="layui-icon layui-tab-close">ဆ</i></li>';
    //             $('.lay-layout-tabs ul').append(html);
    //         console.log(html);
    //         }
    //     })
    // }
</script>
</body>
</html>