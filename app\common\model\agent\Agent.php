<?php
namespace app\common\model\agent;

use app\common\basics\Models;
use app\model\User;
use app\model\AgentRelationship;
use app\model\Commission;
use think\facade\Db;

class Agent extends Models
{
    /**
     * 添加代理关系
     *
     * @param int $userId 用户ID
     * @param int $sponsorId 上级代理ID
     * @param int $level 代理级别
     * @return bool
     */
    public static function addRelationship($userId, $sponsorId, $level)
    {
        $relationship = new AgentRelationship();
        $relationship->user_id = $userId;
        $relationship->sponsor_id = $sponsorId;
        $relationship->level = $level;

        return $relationship->save();
    }

    //绑定用户到推荐人
    public function bindUser($userId, $referrerId)
    {
        // 更新用户的 referred_by 字段
        User::where('id', $userId)->update(['referred_by' => $referrerId]);

        // 如果推荐人是代理，则更新被推荐人的 parent_id 和 level 字段
        $referrer = User::find($referrerId);
        if ($referrer && in_array($referrer->level, [1, 2])) {
            User::where('id', $userId)->update([
                'parent_id' => $referrerId,
                'level' => ($referrer->level == 1) ? 2 : 0 // 如果推荐人是一级代理，则被推荐人成为二级代理；否则不是代理
            ]);
        }
    }

    // 计算佣金分成
    public function calculateCommission($orderId, $totalCommission)
    {
        // 假设订单表中有一个字段 user_id 来表示下单用户
        $order = Db::name('orders')->where('id', $orderId)->find();
        if (!$order) {
            return [];
        }

        $userId = $order['user_id'];
        $commissions = [];

        // 找到所有上级代理，直到找到顶级代理或没有上级代理为止
        while ($userId) {
            $user = User::find($userId);
            if (!$user) {
                break;
            }

            $commissionRate = $this->getCommissionRate($user->level); // 根据代理级别获取佣金比例
            $commissionAmount = ($totalCommission * $commissionRate) / 100;
            $commissions[] = [
                'user_id' => $user->id,
                'level' => $user->level,
                'commission_amount' => $commissionAmount,
            ];

            // 停止在找到顶级代理时
            if ($user->level == 1) {
                break;
            }

            // 移动到上级代理
            $userId = $user->parent_id;
        }

        // 剩余部分为平台佣金
        $platformCommission = $totalCommission - array_sum(array_column($commissions, 'commission_amount'));
        $commissions[] = [
            'user_id' => 0, // 假设平台用户ID为0
            'level' => 0,
            'commission_amount' => $platformCommission,
        ];

        return $commissions;
    }

    // 根据代理级别获取佣金比例
    private function getCommissionRate($level)
    {
        // 这里定义佣金比例，可以根据实际情况调整
        $rates = [
            1 => 30, // 一级代理佣金比例
            2 => 20, // 二级代理佣金比例
            0 => 50, // 平台佣金比例（假设为剩余部分）
        ];
        return $rates[$level] ?? 0;
    }
}