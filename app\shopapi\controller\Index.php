<?php


namespace app\shopapi\controller;

use app\common\basics\ShopApi;
use app\shopapi\logic\IndexLogic;
use app\common\server\JsonServer;
use app\api\logic\WechatLogic;
use app\shop\logic\kefu\KefuLogic;
use app\common\server\FileServer;
use app\common\server\ConfigServer;
use think\facade\Db;

/**
 * 商家移动端账号登录
 * Class Account
 * @package app\shopapi\controller
 */
class Index extends ShopApi
{

    public $like_not_need_login = ['config','copyright','jsConfig','index','getIconConfig'];
    /**
     * 微信公众号接口回调
     */
    public function index()
    {
        $params = $this->request->get('');
        WechatLogic::index($params);
    }
    /**
     * @notes 基础配置
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2021/11/13 17:12
     */
    public function config()
    {
        return JsonServer::success('', IndexLogic::config());
    }

    public function jsConfig()
    {
        $url = $this->request->get('url');
        $result = WeChatLogic::jsConfig($url);
        if ($result['code'] != 1) {
            return JsonServer::error('',[$result]);
        }
        if($result['data']['url']){
            //获取url"https://www.huohanghang.cn/business/?code=0816NUll2qoDxf4KoQol2QJDfx36NUlE&state=b98403abc29d0b38f6ada58b9dc57361"中的code值，并赋值给result['data']['code']
            $url_old=explode('&', $result['data']['url'])[0]??'';
            $result['data']['code']=explode('=', $url_old)[1]??'';
        }


        return JsonServer::success('', $result['data']);
    }
    /**
     * @notes 版权资质
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2022/2/22 11:10 上午
     */
    public function copyright()
    {
        $shop_id = $this->shop_id;
        $result = IndexLogic::copyright($shop_id);
        return JsonServer::success('',$result);
    }



    /**
     * 申请退保证金
     * @return \think\response\Json
     */
    public function applyRefundDeposit()
    {
        if ($this->request->isPost()) {
            $post = $this->request->post();

            // 验证参数
            if (empty($post['reason'])) {
                return JsonServer::error('请填写退款原因');
            }

            // 获取商家ID
            $shop_id = $this->shop_id;
            if (empty($shop_id)) {
                return JsonServer::error('请先登录');
            }

            // 调用逻辑层处理退款申请
            $result = IndexLogic::applyRefundDeposit($shop_id, $post['reason']);

            if ($result === true) {
                return JsonServer::success('申请退款成功，请等待平台审核');
            } else {
                return JsonServer::error($result);
            }
        }

        return JsonServer::error('请求方式错误');
    }

    /**
     * @notes 获取商家保证金退款警告信息
     * @return \think\response\Json
     */
    public function getShopDepositRefundNotice()
    {
        $data = IndexLogic::getShopDepositRefundNotice();
        return JsonServer::success('获取成功', $data);
    }

    /**
     * @notes 检查商家保证金退款资格
     * @return \think\response\Json
     */
    public function checkShopDepositRefundCondition()
    {
        // 获取商家ID
        $shop_id = $this->shop_id;
        if (empty($shop_id)) {
            return JsonServer::error('请先登录');
        }

        $result = IndexLogic::checkShopDepositRefundCondition($shop_id);
        return json($result);
    }

    /**
     * @notes 确认商家保证金退款申请
     * @return \think\response\Json
     */
    public function confirmShopDepositRefund()
    {
        if ($this->request->isPost()) {
            $post = $this->request->post();

            // 获取商家ID
            $shop_id = $this->shop_id;
            if (empty($shop_id)) {
                return JsonServer::error('请先登录');
            }

            $result = IndexLogic::confirmShopDepositRefund($shop_id, $post);

            // 检查返回结果是否是数组格式（新格式）
            if (is_array($result) && isset($result['code'])) {
                // 直接返回标准格式的结果
                return json($result);
            } else if ($result === true) {
                // 兼容旧格式
                return JsonServer::success('退款申请已提交，请等待平台审核');
            } else {
                // 兼容旧格式
                return JsonServer::error($result ?: '申请失败');
            }
        }

        return JsonServer::error('请求方式错误');
    }

    /**
     * @notes 撤销商家保证金退款申请
     * @return \think\response\Json
     */
    public function cancelShopDepositRefund()
    {
        if ($this->request->isPost()) {
            // 获取商家ID
            $shop_id = $this->shop_id;
            if (empty($shop_id)) {
                return JsonServer::error('请先登录');
            }

            $result = IndexLogic::cancelShopDepositRefund($shop_id);

            // 检查返回结果格式
            if (is_array($result) && isset($result['code'])) {
                // 直接返回标准格式的结果
                return json($result);
            } else if ($result === true) {
                // 兼容旧格式
                return JsonServer::success('退款申请已成功撤销');
            } else {
                // 兼容旧格式
                return JsonServer::error($result ?: '撤销失败');
            }
        }

        return JsonServer::error('请求方式错误');
    }

    /**
     * 判断能否退保证金
     *
     *
     * */






     /**
     * @notes 登录工作台
     * @return \think\response\Json|void
     * <AUTHOR>
     * @date 2021/12/20 10:46
     */
    public function Kefulogin()
    {


            $shop_id=$this->request->post('shop_id/d');
            $admin_id=$this->request->post('admin_id/d');
            $id=Db::name('kefu')->where('shop_id',$shop_id)->where('admin_id',$admin_id)->value('id');
            $res = KefuLogic::loginToToken($id, $shop_id);
            if (false === $res) {
                return JsonServer::error(KefuLogic::getError() ?: '系统错误');
            }
            return JsonServer::success('', ['token' => $res]);

    }

    /**
     * @notes 获取商家功能图标配置列表
     * @return \think\response\Json
     * <AUTHOR> Assistant
     * @date 2024/12/18
     */
    public function getIconConfig()
    {
        try {
            // 获取当前登录的商家ID和管理员ID
            $shop_id = $this->shop_id ?? 0;
            $admin_id = $this->admin_id ?? 0;

            // 获取type参数
            $type = input('type', '');






            // 构建查询条件
            $where = [
                ['status', '=', 1], // 只获取启用的图标
            ];

            // 权限过滤逻辑
            $auth_ids = [];

            if (!empty($shop_id) && !empty($admin_id)) {
                // 已登录用户，进行权限验证
                $admin_info = Db::name('shop_admin')->where('id', $admin_id)->find();

                if (!empty($admin_info)) {
                    // 获取角色权限ID列表
                    if (!empty($admin_info['role_id'])) {
                        $role_info = Db::name('shop_role')->where('id', $admin_info['role_id'])->find();
                        if (!empty($role_info['auth_ids'])) {
                            $auth_ids = explode(',', $role_info['auth_ids']);
                        }
                    }

                    // 如果是超级管理员(root=1)，获取所有权限
                    if ($admin_info['root'] == 1) {
                        $all_auths = Db::name('dev_shop_auth')
                            ->where('disable', 0)
                            ->where('del', 0)
                            ->column('id');
                        $auth_ids = $all_auths;
                    }
                }

                // 添加商家ID条件（shop_id=0表示全局配置，shop_id>0表示特定商家配置）
                $where[] = ['shop_id', 'in', [0, $shop_id]];
            }
            $no_auth=[0];//未配置权限的所有人都显示
            $auth_ids=array_merge($auth_ids,$no_auth);

            // 如果type为chat，只显示指定的图标
            if ($type === 'chat') {
                // chat类型只显示：一键推广、会员列表、客服设置、客服列表、客服话术、采购商列表
                // 使用图标名称精确匹配，避免auth_id=0的其他图标干扰
                $chat_icon_names = ['一键推广', '会员列表', '客服设置', '客服列表', '客服话术'];
                $where[] = ['icon_name', 'in', $chat_icon_names];
            } else {
                // 如果有权限限制，添加权限过滤
                if (!empty($auth_ids)) {
                    $where[] = ['auth_id', 'in', $auth_ids];
                }
            }

            // 查询图标配置
            $icon_list = Db::name('shop_icon_config')
                ->where($where)
                ->order('sort_order ASC, id ASC')
                ->select()
                ->toArray();

            // 获取商家类型
            $merchant_type = 0; // 默认为0元入驻
            if (!empty($shop_id)) {
                $shop_info = Db::name('shop')->where('id', $shop_id)->find();
                if (!empty($shop_info)) {
                    $merchant_type = $shop_info['tier_level'] ?? 0;
                }
            }

            // 处理图标数据，添加权限判断字段
            foreach ($icon_list as &$icon) {
                // 如果图标URL不是完整URL，则添加域名前缀
                if (!empty($icon['icon_url']) && strpos($icon['icon_url'], 'http') === false) {
                    $icon['icon_url'] = request()->domain() . $icon['icon_url'];
                }

                // 检查商家类型权限，添加can_access字段
                $allowed_types = explode(',', $icon['merchant_types'] ?? '0,1,2');
                $icon['can_access'] = in_array((string)$merchant_type, $allowed_types);

                // 移除敏感字段
                unset($icon['created_at'], $icon['updated_at'], $icon['merchant_types']);
            }

            return JsonServer::success('获取成功', [
                'list' => $icon_list,
                'total' => count($icon_list),
                'is_login' => !empty($shop_id) && !empty($admin_id),
                'merchant_type' => $merchant_type // 返回商家类型供前端参考
            ]);

        } catch (\Exception $e) {
            return JsonServer::error('获取失败：' . $e->getMessage());
        }
    }
}