<?php
namespace app\shop\controller;

use app\common\basics\ShopBase;
use app\common\server\JsonServer;
use app\shop\logic\PurchaserContactLogic;

/**
 * 采购商联系记录管理
 * Class PurchaserContact
 * @package app\shop\controller
 */
class PurchaserContact extends ShopBase
{
    /**
     * 联系记录列表
     * @return \think\response\Json|\think\response\View
     */
    public function lists()
    {
        if ($this->request->isAjax()) {
            $get = $this->request->get();
            $result = PurchaserContactLogic::getShopContactRecords($this->shop_id, $get);
            
            if ($result === false) {
                return JsonServer::error(PurchaserContactLogic::getError() ?: '获取失败');
            }
            
            return JsonServer::success('获取成功', $result);
        }
        
        return view('', [
            'shop_id' => $this->shop_id,
            'shop_name' => $this->shop_name
        ]);
    }

    /**
     * 联系记录详情
     * @return \think\response\Json|\think\response\View
     */
    public function detail()
    {
        $id = $this->request->get('id');
        
        if (empty($id)) {
            return JsonServer::error('记录ID不能为空');
        }
        
        if ($this->request->isAjax()) {
            $result = PurchaserContactLogic::getContactDetail($id, $this->shop_id);
            
            if ($result === false) {
                return JsonServer::error(PurchaserContactLogic::getError() ?: '获取失败');
            }
            
            return JsonServer::success('获取成功', $result);
        }
        
        return view('', [
            'id' => $id,
            'shop_id' => $this->shop_id
        ]);
    }

    /**
     * 联系统计
     * @return \think\response\Json|\think\response\View
     */
    public function stats()
    {
        if ($this->request->isAjax()) {
            $get = $this->request->get();
            $result = PurchaserContactLogic::getShopContactStats($this->shop_id, $get);
            
            if ($result === false) {
                return JsonServer::error(PurchaserContactLogic::getError() ?: '获取失败');
            }
            
            return JsonServer::success('获取成功', $result);
        }
        
        return view('', [
            'shop_id' => $this->shop_id,
            'shop_name' => $this->shop_name
        ]);
    }

    /**
     * 导出联系记录
     * @return \think\response\Json
     */
    public function export()
    {
        $get = $this->request->get();
        $result = PurchaserContactLogic::exportShopContactRecords($this->shop_id, $get);
        
        if ($result === false) {
            return JsonServer::error(PurchaserContactLogic::getError() ?: '导出失败');
        }
        
        return JsonServer::success('导出成功', $result);
    }
}
