<?php
namespace app\common\service;

use app\common\model\Notification;
use app\common\model\NotificationRead;
use app\common\websocket\AdminNotificationHandler;
use app\common\websocket\Events as GatewayWorkerEvents; // Alias for clarity
use GatewayWorker\Lib\Gateway; // For GatewayWorker
use think\facade\Log;
use think\swoole\욀think\swoole\Websocket; // Required for type hinting if using think-swoole directly

class NotificationService
{
    protected $adminNotificationHandler;
    protected $websocket;

    // It's better to inject dependencies if possible, or use app() helper
    public function __construct()
    {
        // For think-swoole based admin notifications
        if (class_exists(AdminNotificationHandler::class) && app()->has(AdminNotificationHandler::class)) {
            $this->adminNotificationHandler = app(AdminNotificationHandler::class);
        }

        // For think-swoole general websocket, if needed for shop later
        if (class_exists(Websocket::class) && app()->has(Websocket::class)) {
            $this->websocket = app(Websocket::class);
        }
    }

    /**
     * Creates and sends a notification.
     *
     * @param string $targetType 'admin' or 'shop'
     * @param string $title Notification title
     * @param string $content Notification content
     * @param string $notificationType Developer-defined type (e.g., 'order_created')
     * @param array|null $targetUserIds Specific user IDs to target. Null for all users of $targetType.
     * @param string|null $url URL for the notification
     * @param string|null $icon Icon for the notification
     * @param string|null $createdBySystemType System component that created this (e.g., 'api', 'shop_api', 'admin_module')
     * @param int|null $createdById ID of the user/admin who triggered this, if applicable
     * @return Notification|false The created Notification object or false on failure
     */
    public function sendNotification(
        string $targetType,
        string $title,
        string $content,
        string $notificationType,
        ?array $targetUserIds = null,
        ?string $url = null,
        ?string $icon = null,
        ?string $createdBySystemType = 'system',
        ?int $createdById = null
    ) {
        try {
            $notification = new Notification();
            $notification->target_type = $targetType;
            $notification->target_user_ids = $targetUserIds; // Will be auto-converted to JSON by model
            $notification->title = $title;
            $notification->content = $content;
            $notification->notification_type = $notificationType;
            $notification->url = $url;
            $notification->icon = $icon;
            $notification->created_by_type = $createdBySystemType;
            $notification->created_by_id = $createdById;

            if (!$notification->save()) {
                Log::error("Failed to save notification to database: " . $notification->getError());
                return false;
            }

            $this->pushNotificationViaWebSocket($notification);

            return $notification;
        } catch (\Exception $e) {
            Log::error("Error creating notification: " . $e->getMessage() . "\n" . $e->getTraceAsString());
            return false;
        }
    }

    /**
     * Pushes the notification via the appropriate WebSocket server.
     *
     * @param Notification $notification
     */
    protected function pushNotificationViaWebSocket(Notification $notification)
    {
        $payload = [
            'id' => $notification->id,
            'title' => $notification->title,
            'content' => $notification->content,
            'notification_type' => $notification->notification_type,
            'url' => $notification->url,
            'icon' => $notification->icon,
            'created_at' => $notification->created_at ? $notification->created_at->toDateTimeString() : date('Y-m-d H:i:s'), // Ensure consistent format
            'timestamp' => $notification->created_at ? $notification->created_at->getTimestamp() : time() // For compatibility with some JS clients
        ];

        if ($notification->target_type === Notification::TARGET_TYPE_ADMIN) {
            if ($this->adminNotificationHandler) {
                // If targetUserIds is null, send to all admins in the 'admin_group'
                // If targetUserIds is set, the handler would need logic to send to specific FDs
                // For now, AdminNotificationHandler sends to 'admin_group' by default in its send method.
                // We might need to enhance AdminNotificationHandler to send to specific FDs if $notification->target_user_ids is not null.
                // However, the current AdminNotificationHandler::sendNotificationToAdmin sends to the whole group.
                // Let's assume for now we will broadcast to all admins if target_type is admin.
                // A more refined approach would be to iterate targetUserIds and send to each FD.
                Log::info("Pushing notification to ADMIN via AdminNotificationHandler: " . json_encode($payload));
                $this->adminNotificationHandler->sendNotificationToAdmin(
                    $payload['title'],
                    $payload['content'],
                    $payload['notification_type'],
                    $payload['url'],
                    0 // icon parameter in handler, map from $payload['icon'] if needed
                );
            } else {
                Log::warning("AdminNotificationHandler not available for admin notification.");
            }
        } elseif ($notification->target_type === Notification::TARGET_TYPE_SHOP) {
            // Using GatewayWorker for shop notifications as per Events.php
            // We need to determine the group or specific client_ids for shops.
            // Events.php uses 'shop_group'.
            $groupName = 'shop_group'; // As defined in Events.php
            $message = json_encode([
                'type' => 'notification', // Common type for frontend to listen to
                'data' => $payload
            ]);

            if (class_exists(Gateway::class)) { // Check if GatewayWorker class exists
                 if (is_array($notification->target_user_ids) && !empty($notification->target_user_ids)) {
                    foreach ($notification->target_user_ids as $shopId) {
                        // Assuming shop UIDs are bound as 'shop_SHOPID'
                        $uidToNotify = 'shop_' . $shopId;
                        
                        // Add debug logging
                        Log::info("Checking online status for shop UID: {$uidToNotify}");
                        
                        if (Gateway::isUidOnline($uidToNotify)) {
                             Log::info("Pushing notification to specific SHOP user {$uidToNotify} via GatewayWorker: " . json_encode($payload));
                            Gateway::sendToUid($uidToNotify, $message);
                            Log::info("Notification sent successfully to {$uidToNotify}");
                        } else {
                            Log::warning("Shop user {$uidToNotify} is not online for notification. Shop ID: {$shopId}");
                            
                            // Also try to send to shop group as fallback
                            Log::info("Attempting to send to shop_group as fallback for shop ID: {$shopId}");
                            Gateway::sendToGroup($groupName, $message);
                        }
                    }
                } else {
                    // Send to all shops in the group
                    Log::info("Pushing notification to SHOP group via GatewayWorker: " . json_encode($payload));
                    Gateway::sendToGroup($groupName, $message);
                }
            } else {
                 Log::error("GatewayWorker Gateway class not available. Cannot send shop notification. Message: " . $message);
                 // Fallback or logging if GatewayWorker is not running or no one is in the group
                 // This part depends on how GatewayWorker is started and integrated.
                 // A simple check:
                 if (!class_exists(Gateway::class)) {
                     Log::error("GatewayWorker class does not exist. Cannot send shop notifications.");
                 } else {
                     // Check if any clients are in the group
                     $clients_in_group = Gateway::getClientIdCountByGroup($groupName);
                     if ($clients_in_group === 0) {
                         Log::warning("No clients in shop_group. Shop notification not sent to any active WebSocket clients.");
                     } else {
                         // This case should have been handled by sendToGroup if clients were present.
                         // Potentially, Gateway::$businessWorker might not be set if called from a non-GatewayWorker process.
                         Log::warning("GatewayWorker is available, but message might not have been sent to shop_group. Clients in group: " . $clients_in_group);
                     }
                 }
            }
        } else {
            Log::warning("Unsupported target_type for WebSocket push: " . $notification->target_type);
        }
    }

    /**
     * Marks a notification as read for a specific user.
     *
     * @param int $notificationId
     * @param string $userType 'admin' or 'shop'
     * @param int $userId
     * @return NotificationRead|false
     */
    public function markAsRead(int $notificationId, string $userType, int $userId)
    {
        try {
            // Check if already marked as read
            $existingRead = NotificationRead::where('notification_id', $notificationId)
                ->where('user_type', $userType)
                ->where('user_id', $userId)
                ->find();

            if ($existingRead) {
                return $existingRead;
            }

            $read = new NotificationRead();
            $read->notification_id = $notificationId;
            $read->user_type = $userType;
            $read->user_id = $userId;
            // read_at is set by default by the database

            if (!$read->save()) {
                Log::error("Failed to mark notification as read: " . $read->getError());
                return false;
            }
            return $read;
        } catch (\Exception $e) {
            Log::error("Error marking notification as read: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Marks all unread notifications for a user as read.
     *
     * @param string $userType
     * @param int $userId
     * @return bool
     */
    public function markAllAsRead(string $userType, int $userId): bool
    {
        try {
            $unreadNotifications = Notification::scopeUnreadForUser($userType, $userId)->select();
            $allMarked = true;
            foreach ($unreadNotifications as $notification) {
                if (!$this->markAsRead($notification->id, $userType, $userId)) {
                    $allMarked = false;
                    Log::warning("Failed to mark notification ID {$notification->id} as read for {$userType} {$userId}");
                }
            }
            return $allMarked;
        } catch (\Exception $e) {
            Log::error("Error in markAllAsRead for {$userType} {$userId}: " . $e->getMessage());
            return false;
        }
    }


    /**
     * Get notifications for a user, with read status.
     *
     * @param string $userType
     * @param int $userId
     * @param int $perPage
     * @param int $page
     * @return \think\Paginator
     */
    public function getUserNotifications(string $userType, int $userId, int $perPage = 15, int $page = 1)
    {
        $notifications = Notification::scopeForUser($userType, $userId)
            ->with(['reads' => function ($query) use ($userType, $userId) {
                $query->where('user_type', $userType)->where('user_id', $userId);
            }])
            ->paginate([
                'list_rows' => $perPage,
                'page' => $page,
            ]);

        // Process to add an 'is_read' attribute
        $notifications->each(function ($notification) {
            $notification->is_read = !$notification->reads->isEmpty();
            // unset($notification->reads); // Clean up the loaded relationship if not needed directly in output
            return $notification;
        });

        return $notifications;
    }

    /**
     * Get unread notification count for a user.
     *
     * @param string $userType
     * @param int $userId
     * @return int
     */
    public function getUnreadNotificationCount(string $userType, int $userId): int
    {
        return Notification::scopeUnreadForUser($userType, $userId)->count();
    }

    /**
     * Get notifications for admin management, with filtering and pagination.
     *
     * @param array $filters
     * @param int $page
     * @param int $perPage
     * @return \think\Paginator
     */
    public function getManagedNotifications(array $filters = [], int $page = 1, int $perPage = 15)
    {
        $query = Notification::order('created_at', 'desc');

        if (!empty($filters['target_type'])) {
            $query->where('target_type', $filters['target_type']);
        }
        if (!empty($filters['notification_type'])) {
            $query->where('notification_type', 'like', '%' . $filters['notification_type'] . '%');
        }
        if (!empty($filters['title'])) {
            $query->where('title', 'like', '%' . $filters['title'] . '%');
        }
        if (!empty($filters['date_range']) && count($filters['date_range']) == 2) {
            $startDate = strtotime($filters['date_range'][0]);
            $endDate = strtotime($filters['date_range'][1] . ' 23:59:59'); // Include end of day
            if ($startDate && $endDate) {
                $query->whereBetween('created_at', [$startDate, $endDate]);
            }
        }

        return $query->paginate([
            'list_rows' => $perPage,
            'page'      => $page,
        ]);
    }
}
