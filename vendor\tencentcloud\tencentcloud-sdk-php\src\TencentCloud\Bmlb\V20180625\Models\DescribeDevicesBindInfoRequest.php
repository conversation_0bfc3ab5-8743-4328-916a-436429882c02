<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Bmlb\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * DescribeDevicesBindInfo请求参数结构体
 *
 * @method string getVpcId() 获取黑石私有网络唯一ID。
 * @method void setVpcId(string $VpcId) 设置黑石私有网络唯一ID。
 * @method array getInstanceIds() 获取主机ID或虚机IP列表，可用于获取绑定了该主机的负载均衡列表。
 * @method void setInstanceIds(array $InstanceIds) 设置主机ID或虚机IP列表，可用于获取绑定了该主机的负载均衡列表。
 */
class DescribeDevicesBindInfoRequest extends AbstractModel
{
    /**
     * @var string 黑石私有网络唯一ID。
     */
    public $VpcId;

    /**
     * @var array 主机ID或虚机IP列表，可用于获取绑定了该主机的负载均衡列表。
     */
    public $InstanceIds;

    /**
     * @param string $VpcId 黑石私有网络唯一ID。
     * @param array $InstanceIds 主机ID或虚机IP列表，可用于获取绑定了该主机的负载均衡列表。
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("VpcId",$param) and $param["VpcId"] !== null) {
            $this->VpcId = $param["VpcId"];
        }

        if (array_key_exists("InstanceIds",$param) and $param["InstanceIds"] !== null) {
            $this->InstanceIds = $param["InstanceIds"];
        }
    }
}
