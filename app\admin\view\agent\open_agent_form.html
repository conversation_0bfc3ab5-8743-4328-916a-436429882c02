{layout name="layout1" /}
<div class="layui-form" lay-filter="openAgentForm" id="openAgentForm" style="padding: 20px;">
    <div class="layui-form-item">
        <label class="layui-form-label">用户ID/手机号：</label>
        <div class="layui-input-block">
            <input type="text" name="user_identifier" lay-verify="required" placeholder="请输入用户ID或手机号" autocomplete="off" class="layui-input" id="userIdentifier">
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">用户昵称：</label>
        <div class="layui-input-block">
            <input type="text" name="nickname" autocomplete="off" class="layui-input" id="userNickname" readonly>
            <input type="hidden" name="user_id" id="userIdHidden">
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">上级代理：</label>
        <div class="layui-input-block">
            <input type="text" name="sponsor_name" autocomplete="off" class="layui-input" id="sponsorName" readonly>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">保证金金额：</label>
        <div class="layui-input-block">
            <input type="number" name="deposit_amount" lay-verify="required|number|min_deposit" placeholder="请输入保证金金额" autocomplete="off" class="layui-input" step="0.01" min="0">
        </div>
    </div>
    <div class="layui-form-item layui-hide">
        <button class="layui-btn" lay-submit lay-filter="openSubmit" id="openSubmit">立即提交</button>
    </div>
</div>

<script>
    layui.use(['form', 'jquery'], function () {
        var form = layui.form;
        var $ = layui.jquery;

        // 自定义验证规则
        form.verify({
            min_deposit: function(value){
                if(value < 0){
                    return '保证金金额不能为负数';
                }
            }
        });

        // 监听用户ID/手机号输入框失去焦点事件
        $('#userIdentifier').on('blur', function() {
            var userIdentifier = $(this).val();
            if (userIdentifier) {
                $.ajax({
                    url: '{:url("agent.agent/getSupervisorInfo")}', // 后端接口
                    type: 'post',
                    data: {user_identifier: userIdentifier},
                    dataType: 'json',
                    success: function(res) {
                        if (res.code === 1) {
                            $('#userNickname').val(res.data.nickname);
                            $('#userIdHidden').val(res.data.user_id);
                            $('#sponsorName').val(res.data.sponsor_name);
                        } else {
                            layer.msg(res.msg || '查询失败', {icon: 5});
                            $('#userNickname').val('');
                            $('#userIdHidden').val('');
                            $('#sponsorName').val('');
                        }
                    },
                    error: function() {
                        layer.msg('请求失败，请稍后重试', {icon: 5});
                        $('#userNickname').val('');
                        $('#userIdHidden').val('');
                        $('#sponsorName').val('');
                    }
                });
            } else {
                $('#userNickname').val('');
                $('#userIdHidden').val('');
                $('#sponsorName').val('');
            }
        });

        // 提交表单时，确保user_id不为空
        form.on('submit(openSubmit)', function(data){
            if (!data.field.user_id) {
                layer.msg('请先输入有效的用户ID或手机号并确认', {icon: 5});
                return false;
            }
            return true;
        });

        form.render();
    });
</script>
