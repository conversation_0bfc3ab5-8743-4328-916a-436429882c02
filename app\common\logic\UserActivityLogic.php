<?php

namespace app\common\logic;

use app\common\basics\Logic;
use app\common\enum\UserActivityEnum;
use app\common\model\user\User;
use app\common\model\user\UserActivityLog;
use app\common\server\ConfigServer;
use think\facade\Db;
use think\facade\Cache;

/**
 * 用户活跃度逻辑类
 * Class UserActivityLogic
 * @package app\common\logic
 */
class UserActivityLogic extends Logic
{
    /**
     * 添加活跃度积分
     * @param int $user_id 用户ID
     * @param string $activity_type 活动类型
     * @param array $extra_data 额外数据
     * @return bool
     */
    public static function addActivityScore($user_id, $activity_type, $extra_data = [])
    {
        try {
            // 检查系统是否启用
            if (!self::isSystemEnabled()) {
                return true; // 系统未启用，直接返回成功
            }

            // 获取用户信息
            $user = User::find($user_id);
            if (!$user) {
                return false;
            }

            // 检查是否需要加分
            $score_change = self::calculateScoreChange($user, $activity_type, $extra_data);
            if ($score_change <= 0) {
                return true; // 不需要加分
            }

            Db::startTrans();
            
            // 更新用户积分
            $before_score = $user->activity_score;
            $after_score = $before_score + $score_change;
            $before_level = $user->level;
            
            // 计算新等级
            $after_level = self::calculateLevel($after_score);
            
            // 更新用户数据
            $update_data = [
                'activity_score' => $after_score,
                'level' => $after_level,
            ];
            
            // 如果是发布采购信息，标记为采购商
            if ($activity_type == UserActivityEnum::ACTIVITY_PUBLISH_DEMAND) {
                $update_data['is_purchaser'] = 1;
            }
            
            // 如果是采购商登录，更新最后活跃登录时间
            if ($activity_type == UserActivityEnum::ACTIVITY_PURCHASER_LOGIN) {
                $update_data['last_activity_login'] = time();
            }
            
            User::where('id', $user_id)->update($update_data);
            
            // 记录日志
            UserActivityLog::create([
                'user_id' => $user_id,
                'activity_type' => $activity_type,
                'score_change' => $score_change,
                'before_score' => $before_score,
                'after_score' => $after_score,
                'before_level' => $before_level,
                'after_level' => $after_level,
                'remark' => UserActivityEnum::getActivityDesc($activity_type),
                'extra_data' => $extra_data,
                'create_time' => time(),
            ]);
            
            Db::commit();
            return true;
            
        } catch (\Exception $e) {
            Db::rollback();
            self::setError($e->getMessage());
            return false;
        }
    }

    /**
     * 计算积分变化
     * @param User $user
     * @param string $activity_type
     * @param array $extra_data
     * @return int
     */
    private static function calculateScoreChange($user, $activity_type, $extra_data)
    {
        switch ($activity_type) {
            case UserActivityEnum::ACTIVITY_PURCHASER_LOGIN:
                return self::calculatePurchaserLoginScore($user);
                
            case UserActivityEnum::ACTIVITY_PUBLISH_DEMAND:
                return self::getActivityConfig(UserActivityEnum::CONFIG_PUBLISH_DEMAND_SCORE);
                
            case UserActivityEnum::ACTIVITY_CHAT:
                return self::calculateChatScore($user);
                
            case UserActivityEnum::ACTIVITY_PURCHASE:
                return self::getActivityConfig(UserActivityEnum::CONFIG_PURCHASE_SCORE);
                
            default:
                return 0;
        }
    }

    /**
     * 计算采购商登录积分
     * @param User $user
     * @return int
     */
    private static function calculatePurchaserLoginScore($user)
    {
        // 只有采购商才能获得登录积分
        if (!$user->is_purchaser) {
            return 0;
        }
        
        $check_days = self::getActivityConfig(UserActivityEnum::CONFIG_LOGIN_CHECK_DAYS);
        $last_login = $user->last_activity_login ?: 0;
        $now = time();
        
        // 检查是否在指定天数内已经登录过
        if ($now - $last_login < $check_days * 86400) {
            return 0; // 已经在指定天数内登录过
        }
        
        return self::getActivityConfig(UserActivityEnum::CONFIG_PURCHASER_LOGIN_SCORE);
    }

    /**
     * 计算聊天积分
     * @param User $user
     * @return int
     */
    private static function calculateChatScore($user)
    {
        $daily_limit = self::getActivityConfig(UserActivityEnum::CONFIG_CHAT_DAILY_LIMIT);
        $today_start = strtotime(date('Y-m-d'));
        
        // 检查今天已经获得的聊天积分次数
        $today_count = UserActivityLog::where([
            'user_id' => $user->id,
            'activity_type' => UserActivityEnum::ACTIVITY_CHAT,
            'create_time' => ['>=', $today_start]
        ])->count();
        
        if ($today_count >= $daily_limit) {
            return 0; // 今天已达到上限
        }
        
        return self::getActivityConfig(UserActivityEnum::CONFIG_CHAT_SCORE);
    }

    /**
     * 根据积分计算等级
     * @param int $score
     * @return int
     */
    public static function calculateLevel($score)
    {
        $level_configs = [
            5 => self::getActivityConfig(UserActivityEnum::CONFIG_LEVEL_5_SCORE),
            4 => self::getActivityConfig(UserActivityEnum::CONFIG_LEVEL_4_SCORE),
            3 => self::getActivityConfig(UserActivityEnum::CONFIG_LEVEL_3_SCORE),
            2 => self::getActivityConfig(UserActivityEnum::CONFIG_LEVEL_2_SCORE),
            1 => self::getActivityConfig(UserActivityEnum::CONFIG_LEVEL_1_SCORE),
        ];
        
        foreach ($level_configs as $level => $required_score) {
            if ($score >= $required_score) {
                return $level;
            }
        }
        
        return 0; // 无等级
    }

    /**
     * 获取活跃度配置
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    public static function getActivityConfig($key, $default = null)
    {
        $default = $default ?? UserActivityEnum::DEFAULT_CONFIG[$key] ?? null;
        return ConfigServer::get('user_activity', $key, $default);
    }

    /**
     * 设置活跃度配置
     * @param string $key
     * @param mixed $value
     * @return mixed
     */
    public static function setActivityConfig($key, $value)
    {
        return ConfigServer::set('user_activity', $key, $value);
    }

    /**
     * 检查系统是否启用
     * @return bool
     */
    public static function isSystemEnabled()
    {
        return (bool)self::getActivityConfig(UserActivityEnum::CONFIG_IS_ENABLED);
    }

    /**
     * 标记用户为采购商
     * @param int $user_id
     * @return bool
     */
    public static function markAsPurchaser($user_id)
    {
        return User::where('id', $user_id)->update(['is_purchaser' => 1]) !== false;
    }

    /**
     * 重新计算所有用户等级
     * @return bool
     */
    public static function recalculateAllUserLevels()
    {
        try {
            $users = User::field('id,activity_score,level')->where('del', 0)->select();
            
            foreach ($users as $user) {
                $new_level = self::calculateLevel($user->activity_score);
                if ($new_level != $user->level) {
                    User::where('id', $user->id)->update(['level' => $new_level]);
                }
            }
            
            return true;
        } catch (\Exception $e) {
            self::setError($e->getMessage());
            return false;
        }
    }
}
