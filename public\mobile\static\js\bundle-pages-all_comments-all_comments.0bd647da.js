(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["bundle-pages-all_comments-all_comments"],{3269:function(t,e,n){var a=n("24fb");e=a(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.all-comments[data-v-1cac97dc]{padding-top:%?20?%}.all-comments .header .title[data-v-1cac97dc]{padding:%?24?% %?26?%;border-bottom:var(--border)}.all-comments .header .tab[data-v-1cac97dc]{padding:%?30?% 0 %?10?% %?20?%;flex-wrap:wrap}.all-comments .header .tab .tab-item[data-v-1cac97dc]{padding:%?9?% %?29?%}.all-comments .main .evaluation-list .evaluation-item[data-v-1cac97dc]{padding:%?20?%}.all-comments .main .evaluation-list .evaluation-item[data-v-1cac97dc]:not(:last-of-type){border-bottom:1px solid #e5e5e5}.all-comments .main .evaluation-list .evaluation-item .avatar[data-v-1cac97dc]{width:%?60?%;height:%?60?%;border-radius:50%}.all-comments .main .evaluation-list .evaluation-item .seller-recall-container[data-v-1cac97dc]{padding:%?24?% %?20?%;border-radius:%?12?%}',""]),t.exports=e},"4c39":function(t,e,n){"use strict";n.r(e);var a=n("62f7"),i=n("c526");for(var s in i)["default"].indexOf(s)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(s);n("9939");var o=n("f0c5"),c=Object(o["a"])(i["default"],a["b"],a["c"],!1,null,"1cac97dc",null,!1,a["a"],void 0);e["default"]=c.exports},"53f3":function(t,e,n){"use strict";n("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a={data:function(){return{mescroll:null}},onPullDownRefresh:function(){this.mescroll&&this.mescroll.onPullDownRefresh()},onPageScroll:function(t){this.mescroll&&this.mescroll.onPageScroll(t)},onReachBottom:function(){this.mescroll&&this.mescroll.onReachBottom()},methods:{mescrollInit:function(t){console.log(t),this.mescroll=t,this.mescrollInitByRef()},mescrollInitByRef:function(){if(!this.mescroll||!this.mescroll.resetUpScroll){var t=this.$refs.mescrollRef;t&&(this.mescroll=t.mescroll)}},downCallback:function(){var t=this;this.mescroll.optUp.use?this.mescroll.resetUpScroll():setTimeout((function(){t.mescroll.endSuccess()}),500)},upCallback:function(){var t=this;setTimeout((function(){t.mescroll.endErr()}),500)}},mounted:function(){this.mescrollInitByRef()}},i=a;e.default=i},"62f7":function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return s})),n.d(e,"a",(function(){return a}));var a={uRate:n("e721").default,uImage:n("f919").default},i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",[n("mescroll-body",{ref:"mescrollRef",attrs:{up:t.upOption},on:{init:function(e){arguments[0]=e=t.$handleEvent(e),t.mescrollInit.apply(void 0,arguments)},down:function(e){arguments[0]=e=t.$handleEvent(e),t.downCallback.apply(void 0,arguments)},up:function(e){arguments[0]=e=t.$handleEvent(e),t.upCallback.apply(void 0,arguments)}}},[n("v-uni-view",{staticClass:"all-comments"},[t.isEmpty?t._e():n("v-uni-view",{staticClass:"header bg-white"},[n("v-uni-view",{staticClass:"title xs"},[n("v-uni-text",{staticClass:"lighter m-r-10"},[t._v("商品好评率")]),n("v-uni-text",{staticClass:"primary"},[t._v(t._s(t.percent))])],1),n("v-uni-view",{staticClass:"tab flex flex-wrap"},t._l(t.categoryList,(function(e,a){return n("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:e.count,expression:"item.count"}],key:a,class:"tab-item xs m-r-10  br60 m-b-20 "+(t.type==e.id?"bg-primary white":"bg-gray"),on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onChangType(a)}}},[t._v(t._s(e.name)+"("+t._s(e.count)+")")])})),1)],1),n("v-uni-view",{staticClass:"main bg-white"},[n("v-uni-view",{staticClass:"evaluation-list"},t._l(t.commentList,(function(e,a){return n("v-uni-view",{key:a,staticClass:"evaluation-item"},[n("v-uni-view",{staticClass:"user-info flex"},[n("v-uni-image",{staticClass:"avatar m-r-20",attrs:{src:e.avatar}}),n("v-uni-view",{staticClass:"user-name md m-r-10"},[t._v(t._s(e.nickname))]),n("u-rate",{attrs:{disabled:!0,size:"26rpx",color:t.colorConfig.primary},model:{value:e.goods_comment,callback:function(n){t.$set(e,"goods_comment",n)},expression:"item.goods_comment"}})],1),n("v-uni-view",{staticClass:"muted xs m-t-10"},[n("v-uni-text",{staticClass:"m-r-20"},[t._v(t._s(e.create_time))]),n("v-uni-text",{directives:[{name:"show",rawName:"v-show",value:e.spec_value_str,expression:"item.spec_value_str"}]},[t._v(t._s(e.spec_value_str))])],1),e.comment?n("v-uni-view",{staticClass:"dec m-t-20"},[t._v(t._s(e.comment))]):t._e(),e.image.length?n("v-uni-view",{staticClass:"img m-t-20 flex flex-wrap"},t._l(e.image,(function(a,i){return n("v-uni-view",{key:i,staticClass:"img-item m-r-20 m-b-20",attrs:{"data-current":a,"data-uri":e.image},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.previewImage.apply(void 0,arguments)}}},[n("u-image",{staticClass:"goods-img",attrs:{width:"160rpx",fit:"cover",height:"160rpx",radius:"6rpx","lazy-load":!0,src:a}})],1)})),1):t._e(),e.reply?n("v-uni-view",{staticClass:"seller-recall-container bg-gray m-t-10"},[n("v-uni-view",{staticClass:"lighter"},[t._v("商家回复："),n("v-uni-text",{staticClass:"normal"},[t._v(t._s(e.reply))])],1)],1):t._e()],1)})),1)],1)],1)],1)],1)},s=[]},"6b5e":function(t,e,n){var a=n("3269");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var i=n("4f06").default;i("16242558",a,!0,{sourceMap:!1,shadowMode:!1})},9939:function(t,e,n){"use strict";var a=n("6b5e"),i=n.n(a);i.a},b2fd:function(t,e,n){"use strict";n("7a82");var a=n("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("99af"),n("d3b7");var i=a(n("f07e")),s=a(n("c964")),o=n("b550"),c=a(n("53f3")),l={mixins:[c.default],data:function(){return{active:0,type:"",commentList:[],categoryList:[],percent:"",isEmpty:!0,upOption:{auto:!1,empty:{icon:"/static/images/goods_null.png",tip:"暂无评价"}}}},onLoad:function(t){this.id=this.$Route.query.id},onShow:function(){this.id=this.$Route.query.id},methods:{downCallback:function(t){var e=this;return(0,s.default)((0,i.default)().mark((function t(){return(0,i.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.getCommentCategoryFun();case 2:e.mescroll.resetUpScroll();case 3:case"end":return t.stop()}}),t)})))()},upCallback:function(t){var e=this,n=t.num,a=t.size;(0,o.getCommentList)({type:this.type,goods_id:this.id,page_no:n,page_size:a}).then((function(n){if(1==n.code){var a=n.data.lists,i=a.length,s=!!n.data.more;1==t.num&&(e.commentList=[]),e.commentList=e.commentList.concat(a),e.mescroll.endSuccess(i,s)}}))},onChangType:function(t){this.active=t,this.type=this.categoryList[t].id,this.commentList=[],this.mescroll.resetUpScroll()},getCommentCategoryFun:function(){var t=this;return new Promise((function(e){(0,o.getCommentCategory)(t.id).then((function(n){var a=n.code,i=n.data,s=i.comment,o=i.percent;1==a&&(t.categoryList=s,t.percent=o,t.type=s[t.active].id,t.isEmpty=!s[0].count,t.$nextTick((function(){return e()})))}))}))},previewImage:function(t){var e=t.currentTarget.dataset,n=e.current,a=e.uri,i=a;uni.previewImage({current:n,urls:i})}}};e.default=l},c526:function(t,e,n){"use strict";n.r(e);var a=n("b2fd"),i=n.n(a);for(var s in a)["default"].indexOf(s)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(s);e["default"]=i.a}}]);