(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["bundle-pages-exchange_order_details-exchange_order_details"],{"069f":function(t,e,i){"use strict";i("7a82");var a=i("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=a(i("f07e")),r=a(i("c964"));i("a9e3");var s={props:{type:Number,orderId:[Number,String]},data:function(){return{show:!1}},methods:{open:function(){this.show=!0},close:function(){this.show=!1},onConfirm:function(){var t=this;return(0,r.default)((0,n.default)().mark((function e(){return(0,n.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.type,t.orderId,null,t.$emit("confirm");case 3:case"end":return e.stop()}}),e)})))()}},computed:{getTipsText:function(){var t=this.type;switch(t){case 0:return"确认取消订单吗？";case 1:return"确认删除订单吗?";case 2:return"确认收货吗?"}}}};e.default=s},1377:function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=i("b08d"),n={name:"float-tab",data:function(){return{showMore:!1,top:0}},mounted:function(){var t=this;(0,a.getRect)(".tab-img",!1,this).then((function(e){t.height=e.height,console.log(t.height)}))},methods:{onChange:function(){this.showMore=!this.showMore}},watch:{showMore:function(t){this.top=t?-this.height:0}}};e.default=n},1522:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return r})),i.d(e,"a",(function(){return a}));var a={uIcon:i("90f3").default},n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"u-image",style:[t.wrapStyle,t.backgroundStyle],on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onClick.apply(void 0,arguments)}}},[t.isError?t._e():i("v-uni-image",{staticClass:"u-image__image",style:{borderRadius:"circle"==t.shape?"50%":t.$u.addUnit(t.borderRadius)},attrs:{src:t.src,mode:t.mode,"lazy-load":t.lazyLoad},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.onErrorHandler.apply(void 0,arguments)},load:function(e){arguments[0]=e=t.$handleEvent(e),t.onLoadHandler.apply(void 0,arguments)}}}),t.showLoading&&t.loading?i("v-uni-view",{staticClass:"u-image__loading",style:{borderRadius:"circle"==t.shape?"50%":t.$u.addUnit(t.borderRadius),backgroundColor:this.bgColor}},[t.$slots.loading?t._t("loading"):i("u-icon",{attrs:{name:t.loadingIcon,width:t.width,height:t.height}})],2):t._e(),t.showError&&t.isError&&!t.loading?i("v-uni-view",{staticClass:"u-image__error",style:{borderRadius:"circle"==t.shape?"50%":t.$u.addUnit(t.borderRadius)}},[t.$slots.error?t._t("error"):i("u-icon",{attrs:{name:t.errorIcon,width:t.width,height:t.height}})],2):t._e()],1)},r=[]},2322:function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("a9e3");var a={name:"u-image",props:{src:{type:String,default:""},mode:{type:String,default:"aspectFill"},width:{type:[String,Number],default:"100%"},height:{type:[String,Number],default:"auto"},shape:{type:String,default:"square"},borderRadius:{type:[String,Number],default:0},lazyLoad:{type:Boolean,default:!0},showMenuByLongpress:{type:Boolean,default:!0},loadingIcon:{type:String,default:"photo"},errorIcon:{type:String,default:"error-circle"},showLoading:{type:Boolean,default:!0},showError:{type:Boolean,default:!0},fade:{type:Boolean,default:!0},webp:{type:Boolean,default:!1},duration:{type:[String,Number],default:500},bgColor:{type:String,default:"#f3f4f6"}},data:function(){return{isError:!1,loading:!0,opacity:1,durationTime:this.duration,backgroundStyle:{}}},watch:{src:{immediate:!0,handler:function(t){t?this.isError=!1:(this.isError=!0,this.loading=!1)}}},computed:{wrapStyle:function(){var t={};return t.width=this.$u.addUnit(this.width),t.height=this.$u.addUnit(this.height),t.borderRadius="circle"==this.shape?"50%":this.$u.addUnit(this.borderRadius),t.overflow=this.borderRadius>0?"hidden":"visible",this.fade&&(t.opacity=this.opacity,t.transition="opacity ".concat(Number(this.durationTime)/1e3,"s ease-in-out")),t}},methods:{onClick:function(){this.$emit("click")},onErrorHandler:function(t){this.loading=!1,this.isError=!0,this.$emit("error",t)},onLoadHandler:function(){var t=this;if(this.loading=!1,this.isError=!1,this.$emit("load"),!this.fade)return this.removeBgColor();this.opacity=0,this.durationTime=0,setTimeout((function(){t.durationTime=t.duration,t.opacity=1,setTimeout((function(){t.removeBgColor()}),t.durationTime)}),50)},removeBgColor:function(){this.backgroundStyle={backgroundColor:"transparent"}}}};e.default=a},2875:function(t,e,i){"use strict";i.r(e);var a=i("a712"),n=i("c13e");for(var r in n)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(r);i("8fed");var s=i("f0c5"),o=Object(s["a"])(n["default"],a["b"],a["c"],!1,null,"061dd044",null,!1,a["a"],void 0);e["default"]=o.exports},"2c99":function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return r})),i.d(e,"a",(function(){return a}));var a={uImage:i("ba4b").default,priceFormat:i("fefe").default,loadingView:i("2875").default,orderDialog:i("a7f3").default,floatTab:i("65c2").default},n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",[i("v-uni-view",{staticClass:"order-details"},[i("v-uni-view",{staticClass:"header-bg"}),i("v-uni-view",{staticClass:"main"},[i("v-uni-view",{staticClass:"header"},[0==t.orderDetail.order_status?i("v-uni-view",{staticClass:"item"},[i("v-uni-view",{staticClass:"white lg m-b-10"},[t._v("等待买家付款")])],1):t._e(),1==t.orderDetail.order_status?i("v-uni-view",{staticClass:"item"},[i("v-uni-view",{staticClass:"white lg m-b-10"},[t._v("待发货")]),i("v-uni-view",{staticClass:"white sm"},[t._v("您的商品正在打包中，请耐心等待…")])],1):t._e(),2==t.orderDetail.order_status?i("v-uni-view",{staticClass:"item"},[i("v-uni-view",{staticClass:"white lg m-b-10"},[t._v("待收货")]),i("v-uni-view",{staticClass:"white sm"},[t._v("您的商品正在路中，请耐心等待…")])],1):t._e(),3==t.orderDetail.order_status?i("v-uni-view",{staticClass:"item"},[i("v-uni-view",{staticClass:"white lg m-b-10"},[t._v("已完成")]),i("v-uni-view",{staticClass:"white sm"},[t._v("商品已签收，期待再次购买！")])],1):t._e(),4==t.orderDetail.order_status?i("v-uni-view",{staticClass:"item"},[i("v-uni-view",{staticClass:"white lg m-b-10"},[t._v("订单已关闭")])],1):t._e()],1),i("v-uni-view",{staticClass:"address-wrap flex contain"},[i("v-uni-image",{staticClass:"icon-md m-r-20",attrs:{src:"/static/images/icon_address.png"}}),i("v-uni-view",{staticClass:"address"},[i("v-uni-view",[i("v-uni-text",{staticClass:"name md m-r-10"},[t._v(t._s(t.orderDetail.consignee))]),i("v-uni-text",{staticClass:"phone md"},[t._v(t._s(t.orderDetail.mobile))]),i("v-uni-view",{staticClass:"area sm m-t-10 lighter"},[t._v(t._s(t.orderDetail.delivery_address))])],1)],1)],1),i("v-uni-view",{staticClass:"goods contain"},[i("v-uni-view",{staticClass:"flex"},[i("u-image",{attrs:{src:t.orderDetail.goods.image,"border-radius":"10",width:"160",height:"160"}}),i("v-uni-view",{staticClass:"goods-info flex-1 m-l-20"},[i("v-uni-view",{staticClass:"goods-name line-2"},[t._v(t._s(t.orderDetail.goods.name))]),i("v-uni-view",{staticClass:"flex row-between"},[i("v-uni-view",{staticClass:"goods-price primary m-t-10"},[i("price-format",{attrs:{"show-subscript":!1,"first-size":36,"second-size":24,price:t.orderDetail.goods.need_integral}}),i("v-uni-text",{staticClass:"xs"},[t._v("积分")]),2===t.orderDetail.goods.exchange_way?[i("v-uni-text",[t._v("+")]),i("price-format",{attrs:{"show-subscript":!1,"first-size":36,"second-size":24,price:t.orderDetail.goods.need_money}}),i("v-uni-text",{staticClass:"xs"},[t._v("元")])]:t._e()],2),i("v-uni-view",{staticClass:"lighter"},[t._v("×"+t._s(t.orderDetail.total_num))])],1)],1)],1)],1),i("v-uni-view",{staticClass:"price contain"},[i("v-uni-view",{staticClass:"flex row-between"},[i("v-uni-view",[t._v("商品金额")]),i("v-uni-view",{staticClass:"black"},[i("price-format",{attrs:{"show-subscript":!1,price:t.orderDetail.order_integral}}),i("v-uni-text",{staticClass:"xs"},[t._v("积分")]),t.orderDetail.goods_price>0?[i("v-uni-text",[t._v("+")]),i("price-format",{attrs:{"show-subscript":!1,price:t.orderDetail.goods_price}}),i("v-uni-text",{staticClass:"xs"},[t._v("元")])]:t._e()],2)],1),t.orderDetail.shipping_price?i("v-uni-view",{staticClass:"flex row-between"},[i("v-uni-view",[t._v("运费")]),i("v-uni-view",{staticClass:"black"},[t._v("+"),i("price-format",{attrs:{price:t.orderDetail.shipping_price}})],1)],1):t._e(),i("v-uni-view",{staticClass:"flex row-right"},[i("v-uni-view",{staticClass:"lighter"},[t._v("实付金额：")]),i("v-uni-view",{staticClass:"primary"},[i("price-format",{attrs:{"show-subscript":!1,"first-size":36,"second-size":24,price:t.orderDetail.order_integral}}),i("v-uni-text",{staticClass:"xs"},[t._v("积分")]),t.orderDetail.order_amount>0?[i("v-uni-text",[t._v("+")]),i("price-format",{attrs:{"show-subscript":!1,"first-size":36,"second-size":24,price:t.orderDetail.order_amount}}),i("v-uni-text",{staticClass:"xs"},[t._v("元")])]:t._e()],2)],1)],1),i("v-uni-view",{staticClass:"order-info contain"},[i("v-uni-view",{staticClass:"item flex",staticStyle:{"align-items":"flex-start"}},[i("v-uni-view",{staticClass:"title"},[t._v("买家留言")]),i("v-uni-view",{staticClass:"black"},[t._v(t._s(t.orderDetail.user_remark||"无"))])],1)],1),i("v-uni-view",{staticClass:"order-info contain"},[i("v-uni-view",{staticClass:"item flex"},[i("v-uni-view",{staticClass:"title"},[t._v("订单编号")]),i("v-uni-view",{staticClass:"black"},[t._v(t._s(t.orderDetail.order_sn))])],1),i("v-uni-view",{staticClass:"item flex"},[i("v-uni-view",{staticClass:"title"},[t._v("支付方式")]),i("v-uni-view",{staticClass:"black"},[t._v(t._s(t.orderDetail.pay_way))])],1),i("v-uni-view",{staticClass:"item flex"},[i("v-uni-view",{staticClass:"title"},[t._v("下单时间")]),i("v-uni-view",{staticClass:"black"},[t._v(t._s(t.orderDetail.create_time))])],1),t.orderDetail.pay_time?i("v-uni-view",{staticClass:"item flex"},[i("v-uni-view",{staticClass:"title"},[t._v("付款时间")]),i("v-uni-view",{staticClass:"black"},[t._v(t._s(t.orderDetail.pay_time))])],1):t._e(),t.orderDetail.shipping_time?i("v-uni-view",{staticClass:"item flex"},[i("v-uni-view",{staticClass:"title"},[t._v("发货时间")]),i("v-uni-view",{staticClass:"black"},[t._v(t._s(t.orderDetail.shipping_time))])],1):t._e(),t.orderDetail.confirm_take_time?i("v-uni-view",{staticClass:"item flex"},[i("v-uni-view",{staticClass:"title"},[t._v("成交时间")]),i("v-uni-view",{staticClass:"black"},[t._v(t._s(t.orderDetail.confirm_take_time))])],1):t._e(),t.orderDetail.cancel_time?i("v-uni-view",{staticClass:"item flex"},[i("v-uni-view",{staticClass:"title"},[t._v("关闭时间")]),i("v-uni-view",{staticClass:"black"},[t._v(t._s(t.orderDetail.cancel_time))])],1):t._e()],1),t.orderDetail.btns.cancel_btn||t.orderDetail.btns.delivery_btn||t.orderDetail.btns.confirm_btn||t.orderDetail.btns.del_btn||t.orderDetail.btns.pay_btn?i("v-uni-view",{staticClass:"footer bg-white flex fixed"},[i("v-uni-view",{staticClass:"flex-1"}),t.orderDetail.btns.cancel_btn?i("v-uni-view",[i("v-uni-button",{staticClass:"plain br60",attrs:{size:"sm","hover-class":"none"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.handleOrder(0)}}},[t._v("取消订单")])],1):t._e(),t.orderDetail.btns.delivery_btn?i("router-link",{attrs:{to:{path:"/bundle/pages/goods_logistics/goods_logistics",query:{id:t.orderDetail.id,type:"integral"}}}},[i("v-uni-button",{staticClass:"plain br60",attrs:{size:"sm","hover-class":"none"}},[t._v("查看物流")])],1):t._e(),t.orderDetail.btns.confirm_btn?i("v-uni-view",{staticClass:"m-l-20"},[i("v-uni-button",{staticClass:"plain br60 primary red",attrs:{size:"sm","hover-class":"none"},on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.handleOrder(2)}}},[t._v("确认收货")])],1):t._e(),t.orderDetail.btns.del_btn?i("v-uni-view",[i("v-uni-button",{staticClass:"plain br60",attrs:{size:"sm","hover-class":"none"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.handleOrder(1)}}},[t._v("删除订单")])],1):t._e(),t.orderDetail.btns.pay_btn?i("v-uni-view",{staticClass:"m-l-20"},[i("v-uni-button",{staticClass:"bg-primary br60 white",attrs:{size:"sm"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.payNow.apply(void 0,arguments)}}},[t._v("立即付款")])],1):t._e()],1):t._e()],1)],1),t.isFirstLoading?i("loading-view"):t._e(),i("order-dialog",{ref:"orderDialog",attrs:{orderId:t.orderDetail.id,type:t.type},on:{confirm:function(e){arguments[0]=e=t.$handleEvent(e),t.confirmDialog.apply(void 0,arguments)}}}),i("float-tab")],1)},r=[]},"2f85":function(t,e,i){var a=i("24fb");e=a(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.order-details[data-v-67741eaa]{position:relative;padding-bottom:calc(%?120?% + env(safe-area-inset-bottom))}.order-details .contain[data-v-67741eaa]{margin:0 %?20?% %?20?%;border-radius:%?14?%;background-color:#fff}.order-details .header-bg[data-v-67741eaa]{position:absolute;top:0;width:100%;height:%?200?%;background-color:#ff2c3c;z-index:0}.order-details .header[data-v-67741eaa]{padding:%?24?% %?40?%}.order-details .main[data-v-67741eaa]{position:relative;z-index:1}.order-details .goods[data-v-67741eaa]{padding:%?30?% %?24?%}.order-details .goods .goods-name[data-v-67741eaa]{line-height:%?40?%;height:%?80?%}.order-details .address-wrap[data-v-67741eaa]{height:%?164?%;padding:0 %?24?%}.order-details .order-info[data-v-67741eaa]{padding:%?12?% 0}.order-details .order-info .item[data-v-67741eaa]{padding:%?12?% %?24?%}.order-details .order-info .item .title[data-v-67741eaa]{width:%?180?%;flex:none}.order-details .price[data-v-67741eaa]{padding:%?24?% 0}.order-details .price > uni-view[data-v-67741eaa]{height:%?60?%;padding:0 %?24?%}.order-details .footer[data-v-67741eaa]{position:fixed;bottom:0;left:0;right:0;height:%?100?%;padding:0 %?24?%;box-sizing:initial;padding-bottom:env(safe-area-inset-bottom)}.order-details .footer .plain[data-v-67741eaa]{border:1px solid #bbb}.order-details .footer .plain.red[data-v-67741eaa]{border:1px solid #ff2c3c}',""]),t.exports=e},"3c87":function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return r})),i.d(e,"a",(function(){return a}));var a={uModal:i("8d42").default},n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("u-modal",{attrs:{"show-cancel-button":!0,content:t.getTipsText,"confirm-color":"#ff2c3c"},on:{confirm:function(e){arguments[0]=e=t.$handleEvent(e),t.onConfirm.apply(void 0,arguments)}},model:{value:t.show,callback:function(e){t.show=e},expression:"show"}})},r=[]},"45ee":function(t,e,i){var a=i("24fb");e=a(!1),e.push([t.i,".price-format[data-v-0a5a34e0]{font-family:Avenir,SourceHanSansCN,PingFang SC,Arial,Hiragino Sans GB,Microsoft YaHei,sans-serif}",""]),t.exports=e},"4d65":function(t,e,i){"use strict";i.r(e);var a=i("069f"),n=i.n(a);for(var r in a)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(r);e["default"]=n.a},6021:function(t,e,i){"use strict";var a=i("64b1"),n=i.n(a);n.a},"64b1":function(t,e,i){var a=i("6e35");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("4f06").default;n("3d1e497c",a,!0,{sourceMap:!1,shadowMode:!1})},"65c2":function(t,e,i){"use strict";i.r(e);var a=i("ad6f"),n=i("7c75");for(var r in n)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(r);i("9415");var s=i("f0c5"),o=Object(s["a"])(n["default"],a["b"],a["c"],!1,null,"130bc95c",null,!1,a["a"],void 0);e["default"]=o.exports},"6d79":function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("a9e3"),i("acd8");var a={data:function(){return{priceSlice:{}}},components:{},props:{firstSize:{type:[String,Number],default:28},secondSize:{type:[String,Number],default:28},color:{type:String},weight:{type:[String,Number],default:400},price:{type:[String,Number],default:""},showSubscript:{type:Boolean,default:!0},subscriptSize:{type:[String,Number],default:28},lineThrough:{type:Boolean,default:!1}},created:function(){this.priceFormat()},watch:{price:function(t){this.priceFormat()}},methods:{priceFormat:function(){var t=this.price,e={};null!==t&&""!==t&&void 0!==t?(t=parseFloat(t),t=String(t).split("."),e.first=t[0],e.second=t[1],this.priceSlice=e):this.priceSlice={first:0}}}};e.default=a},"6e35":function(t,e,i){var a=i("24fb");e=a(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.u-image[data-v-1bf07c9a]{position:relative;transition:opacity .5s ease-in-out}.u-image__image[data-v-1bf07c9a]{width:100%;height:100%}.u-image__loading[data-v-1bf07c9a], .u-image__error[data-v-1bf07c9a]{position:absolute;top:0;left:0;width:100%;height:100%;display:flex;flex-direction:row;align-items:center;justify-content:center;background-color:#f3f4f6;color:#909399;font-size:%?46?%}',""]),t.exports=e},"7c75":function(t,e,i){"use strict";i.r(e);var a=i("1377"),n=i.n(a);for(var r in a)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(r);e["default"]=n.a},"822c":function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a={data:function(){return{}},props:{type:{type:String,default:"fixed"},backgroundColor:{type:String,default:"#fff"}},methods:{}};e.default=a},8340:function(t,e,i){"use strict";i("7a82");var a=i("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("caad6"),i("14d9");var n=a(i("f07e")),r=a(i("c964")),s=i("c60f"),o={data:function(){return{orderDetail:{goods:{},btns:{}},team:{},isFirstLoading:!0,type:0,showCancel:"",showLoading:!1}},onLoad:function(t){var e=this.$Route.query.id;this.id=e,this.getOrderDetailFun(),uni.$on("payment",this.payCallback)},onUnload:function(){uni.$off("payment",this.payCallback)},methods:{payCallback:function(t){var e=this;setTimeout((function(){t.result?(e.$toast({title:"支付成功"}),e.getOrderDetailFun()):e.$toast({title:"支付失败"})}),500)},confirmDialog:function(){var t=this;return(0,r.default)((0,n.default)().mark((function e(){var i,a,r;return(0,n.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:i=t.type,a=t.id,r=null,e.t0=i,e.next=0===e.t0?5:1===e.t0?9:2===e.t0?13:17;break;case 5:return e.next=7,(0,s.cancelIntegralOrder)(a);case 7:return r=e.sent,e.abrupt("break",17);case 9:return e.next=11,(0,s.delIntegralOrder)(a);case 11:return r=e.sent,e.abrupt("break",17);case 13:return e.next=15,(0,s.confirmIntegralOrder)(a);case 15:return r=e.sent,e.abrupt("break",17);case 17:1==r.code&&(uni.$emit("refreshorder"),[0,2].includes(i)?t.getOrderDetailFun():1==i&&setTimeout((function(){uni.navigateBack()}),2e3));case 18:case"end":return e.stop()}}),e)})))()},dialogOpen:function(){this.$refs.orderDialog.open()},handleOrder:function(t){var e=this;this.type=t,this.$nextTick((function(){e.dialogOpen()}))},payNow:function(){this.$Router.push({path:"/pages/payment/payment",query:{from:"integral",order_id:this.id}})},getOrderDetailFun:function(){var t=this;(0,s.getIntegralOrderDetail)(this.id).then((function(e){1==e.code?(t.orderDetail=e.data,t.$nextTick((function(){t.isFirstLoading=!1}))):setTimeout((function(){return uni.navigateBack()}),1500)}))}},computed:{}};e.default=o},"8aee":function(t,e,i){var a=i("b9d8");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("4f06").default;n("901755d2",a,!0,{sourceMap:!1,shadowMode:!1})},"8fed":function(t,e,i){"use strict";var a=i("e2db"),n=i.n(a);n.a},9415:function(t,e,i){"use strict";var a=i("8aee"),n=i.n(a);n.a},a4da:function(t,e,i){var a=i("2f85");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("4f06").default;n("a45d186c",a,!0,{sourceMap:!1,shadowMode:!1})},a712:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return r})),i.d(e,"a",(function(){return a}));var a={uLoading:i("8fc0").default},n=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{class:"loading "+("flex"==this.type?"flex":""),style:{backgroundColor:this.backgroundColor}},[e("u-loading",{attrs:{mode:"flower",size:60}})],1)},r=[]},a7f3:function(t,e,i){"use strict";i.r(e);var a=i("3c87"),n=i("4d65");for(var r in n)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(r);var s=i("f0c5"),o=Object(s["a"])(n["default"],a["b"],a["c"],!1,null,"15d7f11b",null,!1,a["a"],void 0);e["default"]=o.exports},ad6f:function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return n})),i.d(e,"a",(function(){}));var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"float-tab ~column"},[i("v-uni-navigator",{staticClass:"tab-img",style:{top:3*t.top+"px"},attrs:{"hover-class":"none","open-type":"switchTab",url:"/pages/index/index"}},[i("v-uni-image",{staticClass:"tab-icon",attrs:{src:"/static/images/icon_float_home.png"}})],1),i("v-uni-navigator",{staticClass:"tab-img",style:{top:2*t.top+"px"},attrs:{"hover-class":"none","open-type":"navigate",url:"/bundle/pages/chat/chat"}},[i("v-uni-image",{staticClass:"tab-icon",attrs:{src:"/static/images/icon_float_help.png"}})],1),i("v-uni-navigator",{staticClass:"tab-img",style:{top:t.top+"px"},attrs:{"hover-class":"none","open-type":"switchTab",url:"/pages/shop_cart/shop_cart"}},[i("v-uni-image",{staticClass:"tab-icon",attrs:{src:"/static/images/icon_float_cart.png"}})],1),i("v-uni-image",{staticClass:"tab-img",staticStyle:{"z-index":"99"},style:{transform:"rotateZ("+(t.showMore?135:0)+"deg)"},attrs:{src:"/static/images/icon_float_more.png"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onChange.apply(void 0,arguments)}}})],1)},n=[]},af8d:function(t,e,i){"use strict";i.r(e);var a=i("2322"),n=i.n(a);for(var r in a)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(r);e["default"]=n.a},b83e:function(t,e,i){var a=i("24fb");e=a(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.loading[data-v-061dd044]{position:fixed;top:0;left:0;width:100vw;height:100vh;z-index:9999;display:flex;justify-content:center;align-items:center}.loading.flex[data-v-061dd044]{position:static;flex:1;width:100%}.loading .loading-img[data-v-061dd044]{width:%?100?%;height:%?100?%}',""]),t.exports=e},b9d8:function(t,e,i){var a=i("24fb");e=a(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.float-tab[data-v-130bc95c]{position:fixed;right:%?16?%;bottom:%?200?%;width:%?96?%;height:%?96?%;z-index:777}.float-tab .tab-img[data-v-130bc95c]{width:100%;height:100%;position:absolute;transition:all .5s}.float-tab .tab-img .tab-icon[data-v-130bc95c]{width:100%;height:100%}',""]),t.exports=e},ba4b:function(t,e,i){"use strict";i.r(e);var a=i("1522"),n=i("af8d");for(var r in n)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(r);i("6021");var s=i("f0c5"),o=Object(s["a"])(n["default"],a["b"],a["c"],!1,null,"1bf07c9a",null,!1,a["a"],void 0);e["default"]=o.exports},bd6f:function(t,e,i){"use strict";i.r(e);var a=i("6d79"),n=i.n(a);for(var r in a)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(r);e["default"]=n.a},c13e:function(t,e,i){"use strict";i.r(e);var a=i("822c"),n=i.n(a);for(var r in a)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(r);e["default"]=n.a},c3e6:function(t,e,i){"use strict";i.r(e);var a=i("8340"),n=i.n(a);for(var r in a)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(r);e["default"]=n.a},c495:function(t,e,i){var a=i("45ee");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("4f06").default;n("54b253da",a,!0,{sourceMap:!1,shadowMode:!1})},c60f:function(t,e,i){"use strict";i("7a82");var a=i("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.cancelIntegralOrder=function(t){return n.default.post("integral_order/cancel",{id:t})},e.closeBargainOrder=function(t){return n.default.get("bargain/closeBargain",{params:t})},e.confirmIntegralOrder=function(t){return n.default.post("integral_order/confirm",{id:t})},e.delIntegralOrder=function(t){return n.default.post("integral_order/del",{id:t})},e.getActivityGoodsLists=function(t){return n.default.get("activity_area/activityGoodsList",{params:t})},e.getBargainActivityDetail=function(t){return n.default.get("bargain/bargainDetail",{params:t})},e.getBargainActivityList=function(t){return n.default.get("bargain/orderList",{params:t})},e.getBargainDetail=function(t){return n.default.get("bargain/detail",{params:t})},e.getBargainList=function(t){return n.default.get("bargain/lists",{params:t})},e.getBargainNumber=function(){return n.default.get("bargain/barginNumber")},e.getBargainPost=function(t){return n.default.get("share/shareBargain",{params:t})},e.getCoupon=function(t){return n.default.post("coupon/getCoupon",{coupon_id:t})},e.getCouponList=function(t){return n.default.get("coupon/getCouponList",{params:t})},e.getGroupList=function(t){return n.default.get("team/activity",{params:t})},e.getIntegralGoods=function(t){return n.default.get("integral_goods/lists",{params:t})},e.getIntegralGoodsDetail=function(t){return n.default.get("integral_goods/detail",{params:t})},e.getIntegralOrder=function(t){return n.default.get("integral_order/lists",{params:t})},e.getIntegralOrderDetail=function(t){return n.default.get("integral_order/detail",{params:{id:t}})},e.getIntegralOrderTraces=function(t){return n.default.get("integral_order/orderTraces",{params:{id:t}})},e.getMyCoupon=function(t){return n.default.get("coupon/myCouponList",{params:t})},e.getOrderCoupon=function(t){return n.default.post("coupon/getBuyCouponList",t)},e.getSeckillGoods=function(t){return n.default.get("seckill_goods/getSeckillGoods",{params:t})},e.getSeckillTime=function(){return n.default.get("seckill_goods/getSeckillTime")},e.getSignLists=function(){return n.default.get("sign/lists")},e.getSignRule=function(){return n.default.get("sign/rule")},e.getTeamInfo=function(t){return n.default.get("team/teamInfo",{params:t})},e.getUserGroup=function(t){return n.default.get("team/record",{params:t})},e.helpBargain=function(t){return n.default.post("bargain/knife",t)},e.integralSettlement=function(t){return n.default.get("integral_order/settlement",{params:t})},e.integralSubmitOrder=function(t){return n.default.post("integral_order/submitOrder",t)},e.launchBargain=function(t){return n.default.post("bargain/sponsor",t)},e.teamBuy=function(t){return n.default.post("team/buy",t)},e.teamCheck=function(t){return n.default.post("team/check",t)},e.teamKaiTuan=function(t){return n.default.post("team/kaituan",t)},e.userSignIn=function(){return n.default.get("sign/sign")};var n=a(i("3b33"));i("b08d")},d5b0:function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return n})),i.d(e,"a",(function(){}));var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-text",{class:(t.lineThrough?"line-through":"")+" price-format",style:{color:t.color,"font-weight":t.weight}},[t.showSubscript?i("v-uni-text",{style:{"font-size":t.subscriptSize+"rpx","margin-right":"2rpx"}},[t._v("¥")]):t._e(),i("v-uni-text",{style:{"font-size":t.firstSize+"rpx","margin-right":"1rpx"}},[t._v(t._s(t.priceSlice.first))]),t.priceSlice.second?i("v-uni-text",{style:{"font-size":t.secondSize+"rpx"}},[t._v("."+t._s(t.priceSlice.second))]):t._e()],1)},n=[]},dc8a:function(t,e,i){"use strict";i.r(e);var a=i("2c99"),n=i("c3e6");for(var r in n)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(r);i("e1ba");var s=i("f0c5"),o=Object(s["a"])(n["default"],a["b"],a["c"],!1,null,"67741eaa",null,!1,a["a"],void 0);e["default"]=o.exports},e1ba:function(t,e,i){"use strict";var a=i("a4da"),n=i.n(a);n.a},e2db:function(t,e,i){var a=i("b83e");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("4f06").default;n("fbf8b9f0",a,!0,{sourceMap:!1,shadowMode:!1})},ee17:function(t,e,i){"use strict";var a=i("c495"),n=i.n(a);n.a},fefe:function(t,e,i){"use strict";i.r(e);var a=i("d5b0"),n=i("bd6f");for(var r in n)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(r);i("ee17");var s=i("f0c5"),o=Object(s["a"])(n["default"],a["b"],a["c"],!1,null,"0a5a34e0",null,!1,a["a"],void 0);e["default"]=o.exports}}]);