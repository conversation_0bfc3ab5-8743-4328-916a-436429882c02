<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Cfw\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * DescribeSyncAssetStatus请求参数结构体
 *
 * @method integer getType() 获取0: 互联网防火墙开关，1：vpc 防火墙开关
 * @method void setType(integer $Type) 设置0: 互联网防火墙开关，1：vpc 防火墙开关
 */
class DescribeSyncAssetStatusRequest extends AbstractModel
{
    /**
     * @var integer 0: 互联网防火墙开关，1：vpc 防火墙开关
     */
    public $Type;

    /**
     * @param integer $Type 0: 互联网防火墙开关，1：vpc 防火墙开关
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("Type",$param) and $param["Type"] !== null) {
            $this->Type = $param["Type"];
        }
    }
}
