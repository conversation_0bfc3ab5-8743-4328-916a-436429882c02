<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Bmlb\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * DescribeTrafficMirrorReceivers返回参数结构体
 *
 * @method array getReceiverSet() 获取接收机列表，具体结构描述如data结构所示。
 * @method void setReceiverSet(array $ReceiverSet) 设置接收机列表，具体结构描述如data结构所示。
 * @method integer getTotalCount() 获取接收机总数。
 * @method void setTotalCount(integer $TotalCount) 设置接收机总数。
 * @method string getRequestId() 获取唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
 * @method void setRequestId(string $RequestId) 设置唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
 */
class DescribeTrafficMirrorReceiversResponse extends AbstractModel
{
    /**
     * @var array 接收机列表，具体结构描述如data结构所示。
     */
    public $ReceiverSet;

    /**
     * @var integer 接收机总数。
     */
    public $TotalCount;

    /**
     * @var string 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
     */
    public $RequestId;

    /**
     * @param array $ReceiverSet 接收机列表，具体结构描述如data结构所示。
     * @param integer $TotalCount 接收机总数。
     * @param string $RequestId 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("ReceiverSet",$param) and $param["ReceiverSet"] !== null) {
            $this->ReceiverSet = [];
            foreach ($param["ReceiverSet"] as $key => $value){
                $obj = new TrafficMirrorReceiver();
                $obj->deserialize($value);
                array_push($this->ReceiverSet, $obj);
            }
        }

        if (array_key_exists("TotalCount",$param) and $param["TotalCount"] !== null) {
            $this->TotalCount = $param["TotalCount"];
        }

        if (array_key_exists("RequestId",$param) and $param["RequestId"] !== null) {
            $this->RequestId = $param["RequestId"];
        }
    }
}
