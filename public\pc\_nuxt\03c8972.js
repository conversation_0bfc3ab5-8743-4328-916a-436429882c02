(window.webpackJsonp=window.webpackJsonp||[]).push([[28,7],{477:function(e,t,r){var content=r(480);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[e.i,content,""]]),content.locals&&(e.exports=content.locals);(0,r(17).default)("7c52e05d",content,!0,{sourceMap:!1})},478:function(e,t,r){"use strict";r.r(t);r(473);var o={data:function(){return{priceSlice:{}}},components:{},props:{firstSize:{type:Number,default:14},secondSize:{type:Number,default:14},color:{type:String},weight:{type:[String,Number],default:400},price:{type:[String,Number],default:""},showSubscript:{type:Boolean,default:!0},subscriptSize:{type:Number,default:14},lineThrough:{type:<PERSON>olean,default:!1}},created:function(){this.priceFormat()},watch:{price:function(e){this.priceFormat()}},methods:{priceFormat:function(){var e=this.price,t={};null!==e&&(e=String(e).split("."),t.first=e[0],t.second=e[1],this.priceSlice=t)}}},d=(r(479),r(8)),component=Object(d.a)(o,(function(){var e=this,t=e._self._c;return t("span",{class:(e.lineThrough?"line-through":"")+"price-format",style:{color:e.color,"font-weight":e.weight}},[e.showSubscript?t("span",{style:{"font-size":e.subscriptSize+"px","margin-right":"1px"}},[e._v("¥")]):e._e(),e._v(" "),t("span",{style:{"font-size":e.firstSize+"px","margin-right":"1px"}},[e._v(e._s(e.priceSlice.first))]),e._v(" "),e.priceSlice.second?t("span",{style:{"font-size":e.secondSize+"px"}},[e._v("."+e._s(e.priceSlice.second))]):e._e()])}),[],!1,null,null,null);t.default=component.exports},479:function(e,t,r){"use strict";r(477)},480:function(e,t,r){var o=r(16)(!1);o.push([e.i,".price-format{display:flex;align-items:baseline}",""]),e.exports=o},511:function(e,t,r){var content=r(540);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[e.i,content,""]]),content.locals&&(e.exports=content.locals);(0,r(17).default)("24e33a71",content,!0,{sourceMap:!1})},539:function(e,t,r){"use strict";r(511)},540:function(e,t,r){var o=r(16)(!1);o.push([e.i,".address-list[data-v-425028d8] .el-dialog__body{height:460px;overflow-y:auto}.address-list .list[data-v-425028d8]{margin:0 auto;width:800px}.address-list .list .item[data-v-425028d8]{position:relative;cursor:pointer;height:100px;padding:16px 150px 16px 20px;border:1px solid hsla(0,0%,89.8%,.89804);border-radius:2px}.address-list .list .item.active[data-v-425028d8]{border-color:#ff2c3c}.address-list .list .item .oprate[data-v-425028d8]{position:absolute;right:20px;bottom:9px}.address-list .dialog-footer[data-v-425028d8]{text-align:center}.address-list .dialog-footer .el-button[data-v-425028d8]{width:160px}",""]),e.exports=o},562:function(e,t,r){var content=r(609);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[e.i,content,""]]),content.locals&&(e.exports=content.locals);(0,r(17).default)("b6ec830a",content,!0,{sourceMap:!1})},600:function(e,t,r){"use strict";r.r(t);var o=r(9),d=(r(53),{components:{},props:{value:{type:Boolean,default:!1}},data:function(){return{showDialog:!1,showAddressAdd:!1,addressList:[],selectId:0,editId:""}},methods:{getAddress:function(){var e=this;return Object(o.a)(regeneratorRuntime.mark((function t(){var r,code,data;return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,e.$get("user_address/lists");case 2:r=t.sent,code=r.code,data=r.data,1==code&&(e.addressList=data);case 6:case"end":return t.stop()}}),t)})))()},setDefault:function(e){var t=this;return Object(o.a)(regeneratorRuntime.mark((function r(){var o,code,d;return regeneratorRuntime.wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return r.next=2,t.$post("user_address/setDefault",{id:e});case 2:o=r.sent,code=o.code,o.data,d=o.msg,1==code&&(t.$message({message:d,type:"success"}),t.getAddress());case 7:case"end":return r.stop()}}),r)})))()},onConfirm:function(){this.$emit("confirm",this.selectId),this.showDialog=!1}},watch:{value:function(e){this.showDialog=e,1==e&&this.getAddress()},showDialog:function(e){this.$emit("input",e)}}}),n=(r(539),r(8)),component=Object(n.a)(d,(function(){var e=this,t=e._self._c;return t("div",{staticClass:"address-list"},[t("el-dialog",{attrs:{title:"更换地址",visible:e.showDialog,width:"900px"},on:{"update:visible":function(t){e.showDialog=t}}},[t("div",{staticClass:"list black"},e._l(e.addressList,(function(r,o){return t("div",{key:o,class:["item m-b-16",{active:r.id==e.selectId}],on:{click:function(t){e.selectId=r.id}}},[t("div",[t("span",{staticClass:"weigth-500"},[e._v(e._s(r.contact))]),e._v("\n                    "+e._s(r.telephone)+"\n                    "),r.is_default?t("el-tag",{attrs:{size:"mini",type:"warning",effect:"dark"}},[e._v("默认")]):e._e()],1),e._v(" "),t("div",{staticClass:"lighter m-t-8"},[e._v("\n                    "+e._s(r.province)+" "+e._s(r.city)+" "+e._s(r.district)+"\n                    "+e._s(r.address)+"\n                ")]),e._v(" "),t("div",{staticClass:"oprate lighter flex"},[t("div",{staticClass:"m-r-16",on:{click:function(t){t.stopPropagation(),e.editId=r.id,e.showAddressAdd=!0}}},[e._v("\n                        修改\n                    ")]),e._v(" "),t("div",{on:{click:function(t){return t.stopPropagation(),e.setDefault(r.id)}}},[e._v("设为默认")])])])})),0),e._v(" "),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{attrs:{type:"primary"},on:{click:e.onConfirm}},[e._v("确认")]),e._v(" "),t("el-button",{on:{click:function(t){e.showDialog=!1}}},[e._v("取消")])],1)]),e._v(" "),t("address-add",{attrs:{aid:e.editId},on:{success:e.getAddress},model:{value:e.showAddressAdd,callback:function(t){e.showAddressAdd=t},expression:"showAddressAdd"}})],1)}),[],!1,null,"425028d8",null);t.default=component.exports;installComponents(component,{AddressAdd:r(534).default})},608:function(e,t,r){"use strict";r(562)},609:function(e,t,r){var o=r(16)(!1);o.push([e.i,'.confirm-order[data-v-61c9e9bf]{padding:16px 0}.confirm-order .title[data-v-61c9e9bf]{padding:12px 20px;font-weight:700}.confirm-order .title>i[data-v-61c9e9bf]{cursor:pointer}.confirm-order .contact[data-v-61c9e9bf]{padding:10px 20px 22px}.confirm-order .contact-item[data-v-61c9e9bf]{display:flex;align-items:center;height:36px}.confirm-order .contact-item-label[data-v-61c9e9bf]{width:72px;color:#999}.confirm-order .order-hd .address[data-v-61c9e9bf]{padding:10px 20px 22px}.confirm-order .order-hd .address .address-con[data-v-61c9e9bf]{position:relative;cursor:pointer;width:800px;height:100px;padding:16px 150px 16px 20px;border:1px solid #ff2c3c;border-radius:2px}.confirm-order .order-hd .address .address-con:hover .oprate[data-v-61c9e9bf]{display:flex}.confirm-order .order-hd .address .address-con .oprate[data-v-61c9e9bf]{display:none;position:absolute;right:20px;bottom:9px}.confirm-order .order-hd .address .address-add[data-v-61c9e9bf]{cursor:pointer;width:320px;height:100px;border:1px dashed hsla(0,0%,89.8%,.89804)}.confirm-order .order-con .shop[data-v-61c9e9bf]{padding:0 20px}.confirm-order .order-con .shop .shop-name[data-v-61c9e9bf]{height:40px;background-color:#f6f6f6;line-height:40px}.confirm-order .order-con .goods[data-v-61c9e9bf]{border-bottom:1px dashed hsla(0,0%,89.8%,.89804)}.confirm-order .order-con .goods .goods-hd[data-v-61c9e9bf]{height:40px;padding:0 20px}.confirm-order .order-con .goods .goods-list .item[data-v-61c9e9bf]{padding:10px 0}.confirm-order .order-con .goods .goods-list .item-disabled[data-v-61c9e9bf]{position:relative}.confirm-order .order-con .goods .goods-list .item-disabled[data-v-61c9e9bf]:before{z-index:9;position:absolute;top:0;left:0;bottom:0;right:0;height:100%;display:block;content:"";background-color:hsla(0,0%,100%,.5)}.confirm-order .order-con .goods .info[data-v-61c9e9bf]{width:500px}.confirm-order .order-con .goods .info .pictrue[data-v-61c9e9bf]{margin-right:10px}.confirm-order .order-con .goods .info .pictrue .el-image[data-v-61c9e9bf]{width:72px;height:72px}.confirm-order .order-con .goods .info .name[data-v-61c9e9bf]{margin-bottom:10px}.confirm-order .order-con .goods .info .delivery-support[data-v-61c9e9bf]{font-size:12px;padding:4px 15px;border-radius:60px;margin-left:20px;background-color:#f4f4f4;color:#666}.confirm-order .order-con .goods .price[data-v-61c9e9bf]{width:200px}.confirm-order .order-con .goods .num[data-v-61c9e9bf]{width:250px}.confirm-order .order-con .goods .money[data-v-61c9e9bf]{width:200px}.confirm-order .order-con .input .textarea[data-v-61c9e9bf]{margin:0 20px;width:1000px}.confirm-order .order-con .integral .check-box[data-v-61c9e9bf]{padding:0 20px 12px}.confirm-order .order-footer[data-v-61c9e9bf]{margin-top:2px;padding:25px 20px;justify-content:flex-end}.confirm-order .order-footer .btn[data-v-61c9e9bf]{width:152px;height:44px;border-radius:6px;cursor:pointer}',""]),e.exports=o},698:function(e,t,r){"use strict";r.r(t);r(29),r(26),r(20),r(30),r(21),r(31);var o=r(10),d=r(9),n=(r(53),r(191),r(66),r(25),r(12),r(40),r(106),r(13));function c(object,e){var t=Object.keys(object);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(object);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(object,e).enumerable}))),t.push.apply(t,r)}return t}function l(e){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?c(Object(source),!0).forEach((function(t){Object(o.a)(e,t,source[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(source)):c(Object(source)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(source,t))}))}return e}var f={head:function(){return{title:this.$store.getters.headTitle,link:[{rel:"icon",type:"image/x-icon",href:this.$store.getters.favicon}]}},asyncData:function(e){return Object(d.a)(regeneratorRuntime.mark((function t(){var r,o,d,n,c,l;return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return r=e.query,o=e.$post,e.$get,d=JSON.parse(decodeURIComponent(r.data)),n=d.goods,c=(c=d.carts)||[],l={},t.next=6,o("order/settlement",{goods:JSON.stringify(n),cart_id:c.join()}).then((function(e){var code=e.code,data=e.data;e.msg;1==code&&(l.orderInfo=data,l.address=null==data?void 0:data.address,l.userRemark=data.shop.map((function(e){return{shop_id:e.shop_id,remark:""}})),l.selecteCoupon=data.shop.map((function(){return""})))}));case 6:return t.abrupt("return",l);case 7:case"end":return t.stop()}}),t)})))()},data:function(){return{orderInfo:{},address:{},carts:[],active:0,userRemark:[],selecteCoupon:[],showAddress:!1,showAddressAdd:!1,addressId:"",editId:"",isEdit:!1,shopPage:1}},watch:{address:{handler:function(e){this.addressId=e.id},immediate:!0}},methods:l(l({},Object(n.b)(["getPublicData"])),{},{editAddress:function(e){this.editId=e,this.showAddressAdd=!0},changeAddress:function(e){this.addressId=e,this.orderBuy()},submitOrder:function(){var e=this;return Object(d.a)(regeneratorRuntime.mark((function t(){var r,o,d,n,c,l,f,v,m,data;return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return d=e.carts,n=e.goods,c=e.selecteCoupon,l=e.orderInfo.shop,f=null===(r=l[0])||void 0===r?void 0:r.delivery_type,n[0].delivery_type=null===(o=l[0])||void 0===o?void 0:o.delivery_type,v={goods:JSON.stringify(n),address_id:e.addressId,cart_id:d.join(),coupon_id:c.filter((function(e){return e})),delivery_type:f,remark:e.userRemark.length?JSON.stringify(e.userRemark):""},t.next=7,e.$post("order/submitOrder",v);case 7:m=t.sent,data=m.data,1==m.code&&(e.getPublicData(),e.$router.replace({path:"/payment",query:{id:data.trade_id,from:data.type}}));case 11:case"end":return t.stop()}}),t)})))()},orderBuy:function(){var e=this;return Object(d.a)(regeneratorRuntime.mark((function t(){var r,data,address;return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return console.log(e.selecteCoupon),t.next=3,e.$post("order/settlement",{goods:JSON.stringify(e.goods),address_id:e.addressId,cart_id:e.carts.join(),coupon_id:e.selecteCoupon.filter((function(e){return e}))});case 3:r=t.sent,data=r.data,1==r.code&&(address=data.address,e.orderInfo=data,e.address=address);case 7:case"end":return t.stop()}}),t)})))()}}),created:function(){var e=JSON.parse(decodeURIComponent(this.$route.query.data)),t=e.goods,r=e.type,o=e.carts;this.goods=t,this.type=r,this.carts=o||[]}},v=(r(608),r(8)),component=Object(v.a)(f,(function(){var e=this,t=e._self._c;return t("div",{staticClass:"confirm-order"},[t("div",{staticClass:"order-hd bg-white m-b-16"},[t("div",[t("div",{staticClass:"title lg"},[e._v("收货地址")]),e._v(" "),t("div",{staticClass:"address flex row-between"},[t("div",{directives:[{name:"show",rawName:"v-show",value:e.address.contact,expression:"address.contact"}],staticClass:"address-con"},[t("div",[t("span",{staticClass:"weight-500"},[e._v(e._s(e.address.contact))]),e._v("\n                        "+e._s(e.address.telephone)+"\n                        "),e.address.is_default?t("el-tag",{attrs:{size:"mini",type:"warning",effect:"dark"}},[e._v("默认")]):e._e()],1),e._v(" "),t("div",{staticClass:"lighter m-t-8"},[e._v("\n                        "+e._s(e.address.province)+" "+e._s(e.address.city)+"\n                        "+e._s(e.address.district)+"\n                        "+e._s(e.address.address)+"\n                    ")]),e._v(" "),t("div",{staticClass:"oprate primary flex"},[t("div",{staticClass:"m-r-16",on:{click:function(t){return e.editAddress(e.address.id)}}},[e._v("\n                            修改\n                        ")]),e._v(" "),t("div",{on:{click:function(t){e.showAddress=!0}}},[e._v("更换地址")])])]),e._v(" "),t("div",{staticClass:"address-add flex row-center",on:{click:function(t){return e.editAddress("")}}},[e._v("\n                    + 添加地址\n                ")])])])]),e._v(" "),t("div",{staticClass:"order-con bg-white"},[t("div",{staticClass:"goods m-b-16"},[t("div",{staticClass:"title lg"},[e._v("商品明细")]),e._v(" "),e._m(0),e._v(" "),t("div",{staticClass:"shop"},e._l(e.orderInfo.shop,(function(r,o){return t("div",{key:o,staticClass:"shop-item flex-col flex-1"},[t("div",{staticClass:"shop-name p-l-10 m-b-10 flex flex-1",staticStyle:{width:"1140px"}},[e._v("\n                        "+e._s(r.shop_name)+"\n                    ")]),e._v(" "),t("div",{staticClass:"goods-list flex flex-wrap flex-1",staticStyle:{width:"1140px"}},e._l(r.goods,(function(r,o){return t("div",{key:o,class:["flex","item"]},[t("div",{staticClass:"info flex"},[t("div",{staticClass:"pictrue flex-none"},[t("el-image",{attrs:{src:r.image}})],1),e._v(" "),t("div",[t("div",{staticClass:"name line-2"},[e._v("\n                                        "+e._s(r.name)+"\n                                    ")]),e._v(" "),t("div",{staticClass:"muted m-t-10 xs"},[e._v("\n                                        "+e._s(r.spec_value)+"\n                                    ")])])]),e._v(" "),t("div",{staticClass:"price",staticStyle:{"padding-left":"70px"}},[t("price-formate",{attrs:{price:r.price}}),e._v(" "),r.member_amount?t("div",{staticClass:"flex m-t-6"},[t("span",{staticClass:"xs primary"},[e._v("会员价:")]),e._v(" "),t("price-formate",{attrs:{price:r.member_amount,color:"#FF0808"}})],1):e._e()],1),e._v(" "),t("div",{staticClass:"num text-center"},[e._v("\n                                "+e._s(r.num)+"\n                            ")]),e._v(" "),t("div",{staticClass:"money flex row-center"},[t("price-formate",{attrs:{price:r.sum_price}})],1)])})),0),e._v(" "),t("div",{staticClass:"flex flex-1 col-top m-t-20 row-between",staticStyle:{width:"1140px"}},[t("div",{staticClass:"flex flex-1"},[t("div",{staticClass:"remark flex flex-1 m-r-40"},[t("div",{staticStyle:{width:"70px"}},[e._v("买家备注：")]),e._v(" "),t("div",{staticClass:"textarea",staticStyle:{width:"280px"}},[t("el-input",{attrs:{size:"small",placeholder:"选填，给商家备注留言，100字以内",resize:"none"},model:{value:e.userRemark[o].remark,callback:function(t){e.$set(e.userRemark[o],"remark",t)},expression:"userRemark[index].remark"}})],1)]),e._v(" "),0==e.orderInfo.order_type?t("div",{staticClass:"coupon flex flex-1 m-r-10"},[t("div",[e._v("店铺优惠：")]),e._v(" "),t("el-select",{attrs:{size:"small",placeholder:"请选择"},on:{change:e.orderBuy},model:{value:e.selecteCoupon[o],callback:function(t){e.$set(e.selecteCoupon,o,t)},expression:"selecteCoupon[index]"}},[t("el-option",{attrs:{label:"不使用",value:0}}),e._v(" "),e._l(r.coupon_list,(function(e){return t("el-option",{key:e.value,attrs:{label:e.coupon_name,value:e.id}})}))],2)],1):e._e(),e._v(" "),t("div",{staticClass:"remark flex flex-1"},[t("div",[e._v("配送方式：")]),e._v(" "),t("span",[e._v(e._s(r.delivery_type_text))])])]),e._v(" "),t("div",{staticClass:"flex-col"},[r.discount_amount?t("div",{staticClass:"flex coupon m-b-10 flex-1 row-right"},[t("div",[e._v("优惠：")]),e._v(" "),t("div",[e._v("-￥"+e._s(r.discount_amount))])]):e._e(),e._v(" "),t("div",{staticClass:"flex remark m-b-10 flex-1 row-right"},[t("div",[e._v("运费：")]),e._v(" "),t("div",[e._v("￥"+e._s(r.shipping_price))])]),e._v(" "),t("div",{staticClass:"flex m-b-20 flex-1 row-right"},[t("span",{staticClass:"m-r-10"},[e._v("店铺合计")]),e._v(" "),t("price-formate",{attrs:{color:"#FF2C3C",price:r.total_amount,firstSize:17,subscriptSize:12,secondSize:12}})],1)])])])})),0)])]),e._v(" "),t("div",{staticClass:"order-footer flex bg-white"},[t("div",{staticClass:"flex col-center"},[t("div",{staticClass:"money flex m-r-16"},[t("div",{staticClass:"lighter"},[e._v("实付金额：")]),e._v(" "),t("div",{staticClass:"primary",staticStyle:{"font-size":"20px"}},[t("span",{staticClass:"xxs"},[e._v("¥")]),e._v(e._s(e.orderInfo.total_amount)+"\n                ")])]),e._v(" "),t("div",{staticClass:"white bg-primary lg btn flex row-center",on:{click:e.submitOrder}},[e._v("\n                去结算\n            ")])])]),e._v(" "),t("address-add",{attrs:{aid:e.editId},on:{success:e.orderBuy},model:{value:e.showAddressAdd,callback:function(t){e.showAddressAdd=t},expression:"showAddressAdd"}}),e._v(" "),t("address-list",{on:{confirm:e.changeAddress},model:{value:e.showAddress,callback:function(t){e.showAddress=t},expression:"showAddress"}})],1)}),[function(){var e=this,t=e._self._c;return t("div",{staticClass:"goods-hd flex lighter"},[t("div",{staticClass:"info text-center"},[e._v("商品信息")]),e._v(" "),t("div",{staticClass:"price text-center"},[e._v("商品价格")]),e._v(" "),t("div",{staticClass:"num text-center"},[e._v("数量")]),e._v(" "),t("div",{staticClass:"money text-center"},[e._v("合计")])])}],!1,null,"61c9e9bf",null);t.default=component.exports;installComponents(component,{PriceFormate:r(478).default,AddressAdd:r(534).default,AddressList:r(600).default})}}]);