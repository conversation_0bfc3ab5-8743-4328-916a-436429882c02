<?php
namespace app\common\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\facade\Db;
use think\facade\Log;

class StatDaily extends Command
{
    protected function configure()
    {
        $this->setName('stat_daily')
            ->setDescription('每日数据汇总统计');
    }

    protected function execute(Input $input, Output $output)
    {
        try {
            $date = date('Y-m-d', strtotime('-1 day')); // 统计昨天的数据
            $time = time();

            // 1. 汇总店铺数据
            $this->summaryShopData($date, $time);
            
            // 2. 汇总商品数据
            $this->summaryGoodsData($date, $time);
            
            // 3. 汇总行业数据
            $this->summaryIndustryData($date, $time);

            $output->writeln("数据汇总完成: " . date('Y-m-d H:i:s'));
        } catch (\Exception $e) {
            Log::error("数据汇总异常: " . $e->getMessage());
            $output->writeln("数据汇总异常: " . $e->getMessage());
        }
    }

    /**
     * 汇总店铺数据
     */
    protected function summaryShopData($date, $time)
    {
        // 汇总店铺小时数据到日数据
        $shops = Db::name('stat_hourly_shop')
            ->field([
                'shop_id',
                'date',
                'SUM(sales_amount) as total_sales_amount',
                'SUM(order_count) as total_order_count',
                'SUM(customer_count) as total_customer_count'
            ])
            ->where('date', $date)
            ->group('shop_id, date')
            ->select();

        // 获取店铺访客数据
        $visitors = Db::name('stat_shop_visitor')
            ->where('date', $date)
            ->column('visitor_count, new_visitor_count', 'shop_id');

        foreach ($shops as $shop) {
            $visitor = $visitors[$shop['shop_id']] ?? ['visitor_count' => 0, 'new_visitor_count' => 0];
            
            Db::name('la_stat_daily_shop')->insert([
                'shop_id' => $shop['shop_id'],
                'date' => $date,
                'total_sales_amount' => $shop['total_sales_amount'],
                'total_order_count' => $shop['total_order_count'],
                'total_customer_count' => $shop['total_customer_count'],
                'visitor_count' => $visitor['visitor_count'],
                'new_customer_count' => $visitor['new_visitor_count'],
                'create_time' => $time,
                'update_time' => $time
            ], true)->onDuplicate([
                'total_sales_amount', 'total_order_count', 'total_customer_count',
                'visitor_count', 'new_customer_count', 'update_time'
            ]);
        }
    }

    /**
     * 汇总商品数据
     */
    protected function summaryGoodsData($date, $time)
    {
        // 汇总商品小时数据到日数据
        $goods = Db::name('stat_hourly_goods')
            ->field([
                'shop_id',
                'goods_id',
                'date',
                'SUM(sales_amount) as total_sales_amount',
                'SUM(sales_quantity) as total_sales_quantity',
                'SUM(view_count) as view_count'
            ])
            ->where('date', $date)
            ->group('shop_id, goods_id, date')
            ->select();

        // 获取商品加购数据
        $cartData = Db::name('cart')
            ->field([
                'goods_id',
                'COUNT(DISTINCT user_id) as add_to_cart_count'
            ])
            ->whereTime('create_time', 'between', [
                strtotime($date),
                strtotime($date) + 86399
            ])
            ->group('goods_id')
            ->column('add_to_cart_count', 'goods_id');

        foreach ($goods as $item) {
            Db::name('la_stat_daily_goods')->insert([
                'shop_id' => $item['shop_id'],
                'goods_id' => $item['goods_id'],
                'date' => $date,
                'total_sales_amount' => $item['total_sales_amount'],
                'total_sales_quantity' => $item['total_sales_quantity'],
                'view_count' => $item['view_count'],
                'add_to_cart_count' => $cartData[$item['goods_id']] ?? 0,
                'create_time' => $time,
                'update_time' => $time
            ], true)->onDuplicate([
                'total_sales_amount', 'total_sales_quantity',
                'view_count', 'add_to_cart_count', 'update_time'
            ]);
        }
    }

    /**
     * 汇总行业数据
     */
    protected function summaryIndustryData($date, $time)
    {
        // 获取所有商品的分类信息
        $goodsCategories = Db::name('goods')
            ->alias('g')
            ->join('la_stat_daily_goods sdg', 'g.id = sdg.goods_id')
            ->field([
                'g.first_category_id as category_id',
                'SUM(sdg.total_sales_amount) as sales_amount',
                'SUM(sdg.total_sales_quantity) as sales_volume',
                'SUM(sdg.view_count) as visitor_count'
            ])
            ->where('sdg.date', $date)
            ->group('g.first_category_id')
            ->select();

        foreach ($goodsCategories as $category) {
            // 销售额统计
            Db::name('ls_shop_industry_data')->insert([
                'data_date' => $date,
                'category_id' => $category['category_id'],
                'metric_type' => 'total_sales_amount',
                'metric_value' => $category['sales_amount'],
                'create_time' => $time
            ]);

            // 销量统计
            Db::name('ls_shop_industry_data')->insert([
                'data_date' => $date,
                'category_id' => $category['category_id'],
                'metric_type' => 'total_sales_volume',
                'metric_value' => $category['sales_volume'],
                'create_time' => $time
            ]);

            // 访客数统计
            Db::name('ls_shop_industry_data')->insert([
                'data_date' => $date,
                'category_id' => $category['category_id'],
                'metric_type' => 'visitor_count',
                'metric_value' => $category['visitor_count'],
                'create_time' => $time
            ]);
        }
    }
}