<?php

namespace app\api\controller;

use app\common\basics\Api;
use app\api\logic\GoodsLogic;
use app\common\server\JsonServer;
use think\facade\Db;
use think\facade\Validate;


class Goods extends Api
{
    public $like_not_need_login = ['getHotGoods','getBibuySellers','getRecommendedPurchaseList','getpurchaseExplosives','getPurchaseAnnualRank','getHotSellers','getNewProduct','getProductCollectionRanking','getAnnualTopProducts','getHomeHitsList','getchooseByExpertList','getpdGoodsList', 'getGoodsDetail', 'getHotList', 'getGoodsList', 'getGoodsListTemplate', 'getGoodsListByColumnId'];

    /**
     * 商品详情
     */
    public function getGoodsDetail()
    {
        if ($this->request->isGet()) {
            $goodsId = $this->request->get('goods_id', '', 'trim');
            $validate = Validate::rule('goods_id', 'require|integer|gt:0');
            if (!$validate->check(['goods_id' => $goodsId])) {
                return JsonServer::error($validate->getError());
            }
            $goodsDetail = GoodsLogic::getGoodsDetail($goodsId, $this->user_id);
            if (false === $goodsDetail) {
                $error = GoodsLogic::getError() ?? '获取商品详情失败';
                return JsonServer::error($error);
            }
            return JsonServer::success('获取商品详情成功', $goodsDetail);
        } else {
            return JsonServer::error('请求方式错误');
        }
    }

    /**
     * 热销榜单
     */
    public function getHotList()
    {
        return $this->getGoodsListTemplate($this->request, 'getHotList');
    }

    /**
     * 商品列表
     */
    public function getGoodsList()
    {
        return $this->getGoodsListTemplate($this->request, 'getGoodsList');
    }

    /**
     * 商品列表模板
     * 作用：代码复用
     */
    public function getGoodsListTemplate($request, $methodName)
    {
        if ($request->isGet()) {
            $get = $this->request->get();
            $get['user_id'] = $this->user_id;
            $get['page_no'] = $this->page_no;
            $get['page_size'] = $this->page_size;
            $data = GoodsLogic::$methodName($get); // 可变方法
            return JsonServer::success('获取成功', $data);
        } else {
            return JsonServer::error('请求方式错误');
        }
    }

    /**
     * 根据商品栏目获取商品列表
     */
    public function getGoodsListByColumnId()
    {
        if ($this->request->isGet()) {
            $columnId = $this->request->get('column_id', '', 'trim');
            $validate = Validate::rule('column_id', 'require|integer|gt:0');
            if (!$validate->check(['column_id' => $columnId])) {
                return JsonServer::error($validate->getError());
            }
            $data = GoodsLogic::getGoodsListByColumnId($columnId, $this->page_no, $this->page_size);
            return JsonServer::success('获取成功', $data);
        } else {
            return JsonServer::error('请求方式错误');
        }
    }


    /*
     * 获取拼单集采的商品列表
     */
    public function getpdGoodsList()
    {
        return $this->getGoodsListTemplate($this->request, 'getpdGoodsList');
    }


    /*
     * 金刚区页面商品列表
     * 行家严选
     */
    public function getchooseByExpertList()
    {
        return $this->getGoodsListTemplate($this->request, 'chooseByExpert');
    }

    /*
    * 金刚区页面商品列表
    * 居家爆品
    */
    public function getHomeHitsList()
    {
        return $this->getGoodsListTemplate($this->request, 'getHomeHitsList');
    }

    /*
       * 金刚区页面商品列表
       * 找产品-年度榜
   */
    public function getAnnualTopProducts()
    {
        return $this->getGoodsListTemplate($this->request, 'getAnnualTopProducts');
    }


    /*
      * 金刚区页面商品列表
      * 找产品-收藏榜
    */
    public function getProductCollectionRanking()
    {
        return $this->getGoodsListTemplate($this->request, 'getProductCollectionRanking');
    }


    /*
      * 金刚区页面商品列表
      * 找产品-新品上新
    */
    public function getNewProduct()
    {
        return $this->getGoodsListTemplate($this->request, 'getNewProduct');
    }

    /*
      * 金刚区页面商品列表
      * 找产品-热卖爆品
    */
    public function getHotSellers()
    {
        return $this->getGoodsListTemplate($this->request, 'getHotSellers');
    }

    /*
      * 金刚区页面商品列表
      * 找产品-机构必采
    */
    public function getBibuySellers()
    {
        return $this->getGoodsListTemplate($this->request, 'getBibuySellers');
    }


    /*
      * 金刚区页面商品列表
      * 集采购-年度榜
    */
    public function getPurchaseAnnualRank()
    {
        return $this->getGoodsListTemplate($this->request, 'getPurchaseAnnualRank');
    }

    /*
         * 金刚区页面商品列表
         * 集采购-集采爆品
       */
    public function getpurchaseExplosives()
    {
        return $this->getGoodsListTemplate($this->request, 'getpurchaseExplosives');
    }

    /*
         * 金刚区页面商品列表
         * 找厂家-热销企业-热门工厂精选
       */
    public function getHotGoods()
    {
        return $this->getGoodsListTemplate($this->request, 'getHotGoods');
    }


    /*
         * 金刚区页面商品列表
         * 集采购-推荐榜
       */
    public function getRecommendedPurchaseList()
    {
        return $this->getGoodsListTemplate($this->request, 'getRecommendedPurchaseList');
    }



    /*
     * 获取商品分词
     *
     */
    public function getGoodsWords(){

        Db::name('goods')->field('id,title')->toArray();

        // GoodsLogic::saveKeywords();
    }

}