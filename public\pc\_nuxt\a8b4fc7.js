(window.webpackJsonp=window.webpackJsonp||[]).push([[45,10],{473:function(e,t,r){"use strict";var n=r(14),o=r(4),c=r(5),l=r(141),m=r(24),f=r(18),d=r(290),h=r(54),v=r(104),x=r(289),_=r(3),S=r(105).f,w=r(45).f,y=r(23).f,k=r(474),O=r(475).trim,N="Number",I=o.Number,T=I.prototype,$=o.TypeError,E=c("".slice),C=c("".charCodeAt),M=function(e){var t=x(e,"number");return"bigint"==typeof t?t:R(t)},R=function(e){var t,r,n,o,c,l,m,code,f=x(e,"number");if(v(f))throw $("Cannot convert a Symbol value to a number");if("string"==typeof f&&f.length>2)if(f=O(f),43===(t=C(f,0))||45===t){if(88===(r=C(f,2))||120===r)return NaN}else if(48===t){switch(C(f,1)){case 66:case 98:n=2,o=49;break;case 79:case 111:n=8,o=55;break;default:return+f}for(l=(c=E(f,2)).length,m=0;m<l;m++)if((code=C(c,m))<48||code>o)return NaN;return parseInt(c,n)}return+f};if(l(N,!I(" 0o1")||!I("0b1")||I("+0x1"))){for(var j,A=function(e){var t=arguments.length<1?0:I(M(e)),r=this;return h(T,r)&&_((function(){k(r)}))?d(Object(t),r,A):t},P=n?S(I):"MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,EPSILON,MAX_SAFE_INTEGER,MIN_SAFE_INTEGER,isFinite,isInteger,isNaN,isSafeInteger,parseFloat,parseInt,fromString,range".split(","),D=0;P.length>D;D++)f(I,j=P[D])&&!f(A,j)&&y(A,j,w(I,j));A.prototype=T,T.constructor=A,m(o,N,A,{constructor:!0})}},474:function(e,t,r){var n=r(5);e.exports=n(1..valueOf)},475:function(e,t,r){var n=r(5),o=r(36),c=r(19),l=r(476),m=n("".replace),f="["+l+"]",d=RegExp("^"+f+f+"*"),h=RegExp(f+f+"*$"),v=function(e){return function(t){var r=c(o(t));return 1&e&&(r=m(r,d,"")),2&e&&(r=m(r,h,"")),r}};e.exports={start:v(1),end:v(2),trim:v(3)}},476:function(e,t){e.exports="\t\n\v\f\r                　\u2028\u2029\ufeff"},485:function(e,t,r){"use strict";r.r(t);r(473),r(86),r(62),r(12),r(107),r(40),r(106);var n=6e4,o=36e5,c=24*o;function l(e){return(0+e.toString()).slice(-2)}var m={components:{},props:{isSlot:{type:Boolean,default:!1},time:{type:Number,default:0},format:{type:String,default:"hh:mm:ss"},autoStart:{type:Boolean,default:!0}},watch:{time:{immediate:!0,handler:function(e){e&&this.reset()}}},data:function(){return{timeObj:{},formateTime:0}},created:function(){},computed:{},methods:{createTimer:function(e){return setTimeout(e,100)},isSameSecond:function(e,t){return Math.floor(e)===Math.floor(t)},start:function(){this.counting||(this.counting=!0,this.endTime=Date.now()+1e3*this.remain,this.setTimer())},setTimer:function(){var e=this;this.tid=this.createTimer((function(){var t=e.getRemain();e.isSameSecond(t,e.remain)&&0!==t||e.setRemain(t),0!==e.remain&&e.setTimer()}))},getRemain:function(){return Math.max(this.endTime-Date.now(),0)},pause:function(){this.counting=!1,clearTimeout(this.tid)},reset:function(){this.pause(),this.remain=this.time,this.setRemain(this.remain),this.autoStart&&this.start()},setRemain:function(e){var t=this.format;this.remain=e;var time,r=(time=e,{days:Math.floor(time/c),hours:l(Math.floor(time%c/o)),minutes:l(Math.floor(time%o/n)),seconds:l(Math.floor(time%n/1e3))});this.formateTime=function(e,t){var r=t.days,n=t.hours,o=t.minutes,c=t.seconds;return-1!==e.indexOf("dd")&&(e=e.replace("dd",r)),-1!==e.indexOf("hh")&&(e=e.replace("hh",l(n))),-1!==e.indexOf("mm")&&(e=e.replace("mm",l(o))),-1!==e.indexOf("ss")&&(e=e.replace("ss",l(c))),e}(t,r),this.$emit("change",r),0===e&&(this.pause(),this.$emit("finish"))}}},f=r(8),component=Object(f.a)(m,(function(){var e=this,t=e._self._c;return e.time>=0?t("div",[t("client-only",[e.isSlot?e._t("default"):t("span",[e._v(e._s(e.formateTime))])],2)],1):e._e()}),[],!1,null,null,null);t.default=component.exports},493:function(e,t,r){"use strict";r.d(t,"d",(function(){return n})),r.d(t,"e",(function(){return o})),r.d(t,"c",(function(){return c})),r.d(t,"b",(function(){return l})),r.d(t,"a",(function(){return m}));var n=5,o={SMS:0,ACCOUNT:1},c={REGISTER:"ZCYZ",FINDPWD:"ZHMM",LOGIN:"YZMDL",SJSQYZ:"SJSQYZ",CHANGE_MOBILE:"BGSJHM",BIND:"BDSJHM"},l={NONE:"",SEX:"sex",NICKNAME:"nickname",AVATAR:"avatar",MOBILE:"mobile"},m={NORMAL:"normal",HANDLING:"apply",FINISH:"finish"}},575:function(e,t,r){var content=r(638);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[e.i,content,""]]),content.locals&&(e.exports=content.locals);(0,r(17).default)("08e9c7b7",content,!0,{sourceMap:!1})},637:function(e,t,r){"use strict";r(575)},638:function(e,t,r){var n=r(16)(!1);n.push([e.i,".store{width:100%}.store .main{width:660px;margin:0 auto;padding-bottom:52px}.store .main .title{padding:16px 0;color:#101010;font-size:18px;text-align:center}.store .main ::v-deep .el-input__inner{border-radius:0!important}.store .main .avatar-uploader .el-upload{width:100px;height:100px;border:1px solid #d9d9d9;cursor:pointer;position:relative;line-height:0;padding:20px 0;color:#101010;overflow:hidden;border-radius:0}.store .main .avatar-uploader .el-upload:hover{border-color:#ff2c3c}.store .main .avatar-uploader-icon{font-size:28px;color:#8c939d;text-align:center}.store .main .avatar{width:100px;height:100px;display:block}",""]),e.exports=n},709:function(e,t,r){"use strict";r.r(t);r(29);var n=r(10),o=r(9),c=(r(40),r(66),r(26),r(20),r(25),r(12),r(30),r(21),r(31),r(53),r(189)),l=r(493);function m(object,e){var t=Object.keys(object);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(object);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(object,e).enumerable}))),t.push.apply(t,r)}return t}function f(e){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?m(Object(source),!0).forEach((function(t){Object(n.a)(e,t,source[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(source)):m(Object(source)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(source,t))}))}return e}var d={head:function(){return{title:this.$store.getters.headTitle,link:[{rel:"icon",type:"image/x-icon",href:this.$store.getters.favicon}]}},data:function(){return{checked:!1,action:c.a.baseUrl+"/api/file/formimage",category:[],fileList:[],content:"",dialogVisible:!1,canSendPwd:!0,form:{cid:"",clabel:"",name:"",nickname:"",mobile:"",account:"",password:"",code:""},rules:{name:[{required:!0,message:"请输入商家名称",trigger:"blur"}],cid:[{required:!0,message:"请选择主营类目",trigger:"change"}],nickname:[{required:!0,message:"请输入联系人姓名",trigger:"blur"}],mobile:[{required:!0,message:"请输入手机号码",trigger:"blur"},{pattern:/^[1][3,4,5,6.7,8,9][0-9]{9}$/,message:"请输入正确的手机号"}],code:[{required:!0,message:"请输入验证码",trigger:"blur"}],account:[{required:!0,message:"请输入登录账号",trigger:"blur"}],password:[{required:!0,message:"请输入设置登录密码",trigger:"blur"}],imageForm:[{required:!0,message:"请上传营业执照",trigger:"blur"}]}}},asyncData:function(e){return Object(o.a)(regeneratorRuntime.mark((function t(){var r,n,data;return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return r=e.$get,t.next=3,r("shop_category/getList");case 3:return n=t.sent,data=n.data,t.abrupt("return",{category:data});case 6:case"end":return t.stop()}}),t)})))()},mounted:function(){this.getServiceData()},methods:{sndSmsToPhone:function(){var e=this;return Object(o.a)(regeneratorRuntime.mark((function t(){var r;return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(/^[1][3,4,5,6.7,8,9][0-9]{9}$/.test(e.form.mobile)){t.next=2;break}return t.abrupt("return",e.$message.error("请输入正确的手机号码"));case 2:return t.next=4,e.$post("sms/send",{mobile:e.form.mobile,key:l.c.SJSQYZ});case 4:if(r=t.sent,1!=r.code){t.next=11;break}return e.canSendPwd=!1,t.abrupt("return",e.$message.success("发送成功"));case 11:return t.abrupt("return",e.$message.error("发送失败"));case 12:case"end":return t.stop()}}),t)})))()},uploadFileSuccess:function(e,t){this.fileList.push(e.data.uri),console.log(e,this.fileList)},handleRemove:function(e,t){if(console.log(t),t.length>=0){var r=t.map((function(e){return e.response.data.uri}));this.fileList=r}},getServiceData:function(){var e=this;return Object(o.a)(regeneratorRuntime.mark((function t(){var r;return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,e.$get("ShopApply/getTreaty");case 2:1==(r=t.sent).code&&(e.content=r.data.content);case 4:case"end":return t.stop()}}),t)})))()},onSubmitStore:function(e){var t=this;if(!this.checked)return this.$message.error("请同意并阅读服务协议");this.$refs[e].validate(function(){var e=Object(o.a)(regeneratorRuntime.mark((function e(r){var n,data,code;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!r){e.next=10;break}return e.next=3,t.$post("ShopApply/apply",f(f({},t.form),{},{license:t.fileList}));case 3:n=e.sent,data=n.data,code=n.code,n.msg,1==code&&t.$router.push({path:"/store_settled/detail",query:{id:data.id}}),e.next=11;break;case 10:return e.abrupt("return",!1);case 11:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}())}}},h=d,v=(r(637),r(8)),component=Object(v.a)(h,(function(){var e=this,t=e._self._c;return t("div",[t("div",{staticClass:"m-t-20"},[t("el-breadcrumb",{attrs:{separator:"/"}},[t("el-breadcrumb-item",{attrs:{to:{path:"/"}}},[e._v("首页")]),e._v(" "),t("el-breadcrumb-item",[e._v("商家入驻")])],1)],1),e._v(" "),t("div",{staticClass:"store bg-white m-t-16"},[t("div",{staticClass:"main"},[t("div",{staticClass:"title"},[e._v("入驻申请")]),e._v(" "),t("el-form",{ref:"form",staticClass:"demo-form",attrs:{model:e.form,rules:e.rules,"label-width":"110px"}},[t("el-form-item",{attrs:{label:"商家名称:",prop:"name"}},[t("el-input",{attrs:{placeholder:"请输入商家名称"},model:{value:e.form.name,callback:function(t){e.$set(e.form,"name",t)},expression:"form.name"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"主营类目:",prop:"cid"}},[t("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择"},model:{value:e.form.cid,callback:function(t){e.$set(e.form,"cid",t)},expression:"form.cid"}},e._l(e.category,(function(r){return t("el-option",{key:r.id,attrs:{label:r.name,value:r.id}},[e._v("\n                            "+e._s(r.name)+"\n                        ")])})),1)],1),e._v(" "),t("el-form-item",{attrs:{label:"联系人姓名:",prop:"nickname"}},[t("el-input",{attrs:{placeholder:"请输入联系人姓名"},model:{value:e.form.nickname,callback:function(t){e.$set(e.form,"nickname",t)},expression:"form.nickname"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"手机号码:",prop:"mobile"}},[t("el-input",{attrs:{placeholder:"请输入手机号码"},model:{value:e.form.mobile,callback:function(t){e.$set(e.form,"mobile",t)},expression:"form.mobile"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"验证码:",prop:"code"}},[t("el-input",{staticStyle:{width:"355px"},attrs:{placeholder:"请输入验证码"},model:{value:e.form.code,callback:function(t){e.$set(e.form,"code",t)},expression:"form.code"}}),e._v(" "),t("el-button",{staticStyle:{"margin-left":"14px",width:"175px"},on:{click:e.sndSmsToPhone}},[e.canSendPwd?t("div",[e._v("获取验证码")]):t("count-down",{attrs:{time:60,format:"ss秒",autoStart:""},on:{finish:function(t){e.canSendPwd=!0}}})],1)],1),e._v(" "),t("el-form-item",{attrs:{label:"创建账号:",prop:"account"}},[t("el-input",{attrs:{placeholder:"请设置登录账号(可用手机号代替)"},model:{value:e.form.account,callback:function(t){e.$set(e.form,"account",t)},expression:"form.account"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"设置密码:",prop:"password"}},[t("el-input",{attrs:{placeholder:"请输入登录密码"},model:{value:e.form.password,callback:function(t){e.$set(e.form,"password",t)},expression:"form.password"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"营业执照:",prop:""}},[t("el-upload",{staticClass:"avatar-uploader",attrs:{action:e.action,"show-file-list":!0,"list-type":"picture-card","on-success":e.uploadFileSuccess,"on-remove":e.handleRemove,headers:{token:e.$store.state.token}}},[t("i",{staticClass:"el-icon-picture avatar-uploader-icon"}),e._v(" "),t("div",{staticClass:"m-t-20 xs"},[e._v("上传图片")])]),e._v(" "),t("div",{staticClass:"xs muted"},[e._v("支持jpg、png、jpeg格式的图片，最多可上传10张")])],1),e._v(" "),t("el-form-item",{attrs:{label:""}},[t("div",{staticClass:"xs muted m-t-20"},[t("el-checkbox",{model:{value:e.checked,callback:function(t){e.checked=t},expression:"checked"}}),e._v("\n                        同意并阅读"),t("span",{staticClass:"primary pointer",on:{click:function(t){e.dialogVisible=!0}}},[e._v("《服务协议》")])],1),e._v(" "),t("div",{staticClass:"flex m-t-10"},[t("el-button",{staticClass:"bg-primary white",staticStyle:{width:"213px"},on:{click:function(t){return e.onSubmitStore("form")}}},[e._v("\n                            提交申请\n                        ")]),e._v(" "),t("span",{staticClass:"m-l-20 xs muted pointer",on:{click:function(t){return e.$router.push("/store_settled/record")}}},[e._v("查看申请记录")])],1)])],1)],1)]),e._v(" "),t("el-dialog",{attrs:{title:"提示",visible:e.dialogVisible,width:"50%"},on:{"update:visible":function(t){e.dialogVisible=t}}},[t("div",{staticStyle:{height:"40vh","overflow-y":"auto"}},[t("div",{domProps:{innerHTML:e._s(e.content)}})]),e._v(" "),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.dialogVisible=!1}}},[e._v("取 消")]),e._v(" "),t("el-button",{attrs:{type:"primary"},on:{click:function(t){e.dialogVisible=!1}}},[e._v("确 定")])],1)])],1)}),[],!1,null,null,null);t.default=component.exports;installComponents(component,{CountDown:r(485).default})}}]);