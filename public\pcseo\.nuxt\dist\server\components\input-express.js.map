{"version": 3, "file": "components/input-express.js", "sources": ["webpack:///./components/upload.vue?d4ec", "webpack:///./components/upload.vue?cda5", "webpack:///./components/upload.vue?8307", "webpack:///./components/upload.vue?42a2", "webpack:///./components/upload.vue", "webpack:///./components/upload.vue?2a5d", "webpack:///./components/upload.vue?5689", "webpack:///./components/input-Express.vue?85f1", "webpack:///./components/input-Express.vue?cc20", "webpack:///./components/input-Express.vue?9722", "webpack:///./components/input-Express.vue?4bf9", "webpack:///./components/input-Express.vue", "webpack:///./components/input-Express.vue?35c8", "webpack:///./components/input-Express.vue?5971"], "sourcesContent": ["// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/sass-resources-loader/lib/loader.js??ref--7-oneOf-1-4!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./upload.vue?vue&type=style&index=0&id=05db7967&lang=scss&scoped=true&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"05ffbf2f\", content, true, context)\n};", "export * from \"-!../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/sass-resources-loader/lib/loader.js??ref--7-oneOf-1-4!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./upload.vue?vue&type=style&index=0&id=05db7967&lang=scss&scoped=true&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".v-upload .el-upload--picture-card[data-v-05db7967]{width:76px;height:76px;line-height:76px}.v-upload .el-upload-list--picture-card .el-upload-list__item[data-v-05db7967]{width:76px;height:76px}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"v-upload\"},[_c('el-upload',{attrs:{\"list-type\":\"picture-card\",\"action\":_vm.url + '/api/file/formimage',\"limit\":_vm.limit,\"on-success\":_vm.success,\"on-error\":_vm.error,\"on-remove\":_vm.remove,\"on-change\":_vm.onChange,\"headers\":{ token: _vm.$store.state.token },\"auto-upload\":_vm.autoUpload}},[(_vm.isSlot)?_vm._t(\"default\"):_c('div',[_c('div',{staticClass:\"muted xs\"},[_vm._v(\"上传图片\")])])],2)],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport config from '~/config/app'\nexport default {\n    components: {},\n    props: {\n        limit: {\n            type: Number,\n            default: 1,\n        },\n        isSlot: {\n            type: Boolean,\n            default: false,\n        },\n        autoUpload: {\n            type: Boolean,\n            default: true,\n        },\n        onChange: {\n            type: Function,\n            default: () => {},\n        },\n    },\n    watch: {},\n    data() {\n        return {\n            url: config.baseUrl,\n        }\n    },\n    created() {},\n    computed: {},\n    methods: {\n        success(res, file, fileList) {\n            if (!this.autoUpload) {\n                return\n            }\n            this.$message({\n                message: '上传成功',\n                type: 'success',\n            })\n            this.$emit('success', fileList)\n        },\n        remove(file, fileList) {\n            this.$emit('remove', fileList)\n        },\n        error(res) {\n            this.$message({\n                message: '上传失败，请重新上传',\n                type: 'error',\n            })\n        },\n    },\n}\n", "import mod from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./upload.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./upload.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./upload.vue?vue&type=template&id=05db7967&scoped=true&\"\nimport script from \"./upload.vue?vue&type=script&lang=js&\"\nexport * from \"./upload.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./upload.vue?vue&type=style&index=0&id=05db7967&lang=scss&scoped=true&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  \"05db7967\",\n  \"388748c3\"\n  \n)\n\nexport default component.exports", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/sass-resources-loader/lib/loader.js??ref--7-oneOf-1-4!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./input-Express.vue?vue&type=style&index=0&id=13601821&lang=scss&scoped=true&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"5eb5ac17\", content, true, context)\n};", "export * from \"-!../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/sass-resources-loader/lib/loader.js??ref--7-oneOf-1-4!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./input-Express.vue?vue&type=style&index=0&id=13601821&lang=scss&scoped=true&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".input-express .dialog-footer[data-v-13601821]{text-align:center}.input-express .dialog-footer .el-button[data-v-13601821]{width:160px}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"input-express\"},[_c('el-dialog',{attrs:{\"title\":\"填写快递单号\",\"visible\":_vm.showDialog,\"width\":\"926px\"},on:{\"update:visible\":function($event){_vm.showDialog=$event}}},[_c('el-form',{ref:\"inputForm\",attrs:{\"inline\":\"\",\"label-width\":\"100px\",\"model\":_vm.form,\"rules\":_vm.rules}},[_c('el-form-item',{attrs:{\"label\":\"物流公司：\",\"prop\":\"business\"}},[_c('el-input',{attrs:{\"size\":\"small\",\"placeholder\":\"请输入物流公司名称\"},model:{value:(_vm.form.business),callback:function ($$v) {_vm.$set(_vm.form, \"business\", $$v)},expression:\"form.business\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"快递单号：\",\"prop\":\"number\"}},[_c('el-input',{attrs:{\"size\":\"small\",\"placeholder\":\"请输入快递单号\"},model:{value:(_vm.form.number),callback:function ($$v) {_vm.$set(_vm.form, \"number\", $$v)},expression:\"form.number\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"备注说明：\",\"prop\":\"desc\"}},[_c('el-input',{staticStyle:{\"width\":\"632px\"},attrs:{\"type\":\"textarea\",\"placeholder\":\"请输入详细内容，选填\",\"resize\":\"none\",\"rows\":\"5\"},model:{value:(_vm.form.desc),callback:function ($$v) {_vm.$set(_vm.form, \"desc\", $$v)},expression:\"form.desc\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"上传凭证：\",\"prop\":\"upload\"}},[_c('div',{staticClass:\"xs muted\"},[_vm._v(\"请上传快递单号凭证，选填\")]),_vm._v(\" \"),_c('upload',{attrs:{\"isSlot\":\"\",\"file-list\":_vm.fileList,\"limit\":3},on:{\"success\":_vm.uploadSuccess}},[_c('div',{staticClass:\"column-center\",staticStyle:{\"height\":\"100%\"}},[_c('i',{staticClass:\"el-icon-camera xs\",staticStyle:{\"font-size\":\"24px\"}})])])],1)],1),_vm._v(\" \"),_c('div',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{attrs:{\"type\":\"primary\"},on:{\"click\":_vm.submitForm}},[_vm._v(\"确定\")]),_vm._v(\" \"),_c('el-button',{on:{\"click\":function($event){_vm.showDialog = false}}},[_vm._v(\"取消\")])],1)],1)],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nexport default {\n    components: {\n    },\n    data() {\n        return {\n            showDialog: false,\n            form: {\n                // 物流公司\n                business: \"\",\n                // 快递单号\n                number: \"\",\n                // 详细内容\n                desc: \"\",\n            },\n            rules: {\n                business: [{ required: true, message: \"请输入物流公司\" }],\n                number: [{ required: true, message: \"请输入快递单号\" }],\n            },\n            fileList: [],\n        };\n    },\n    props: {\n        value: {\n            type: Boolean,\n            default: false,\n        },\n        aid: {\n            type: [String, Number],\n            default: -1,\n        },\n    },\n    methods: {\n        submitForm() {\n            console.log(this.$refs);\n            this.$refs[\"inputForm\"].validate(async (valid) => {\n                if (valid) {\n                    let fileList = [];\n                    this.fileList.forEach((item) => {\n                        fileList.push(item.response.data);\n                    });\n                    let data = {\n                        id: this.aid,\n                        express_name: this.form.business,\n                        invoice_no: this.form.number,\n                        express_remark: this.form.desc,\n                        express_image:\n                            fileList.length <= 0 ? \"\" : fileList[0].base_url,\n                    };\n                    let res = await this.$post(\"after_sale/express\", data);\n                    if (res.code == 1) {\n                        this.$message({\n                            message: \"提交成功\",\n                            type: \"success\",\n                        });\n                        this.showDialog = false;\n                        this.$emit(\"success\");\n                    }\n                } else {\n                    return false;\n                }\n            });\n        },\n        uploadSuccess(e) {\n            let fileList = Object.assign([], e);\n            this.fileList = fileList;\n        },\n    },\n    watch: {\n        value(val) {\n            this.showDialog = val;\n        },\n        showDialog(val) {\n            this.$emit(\"input\", val);\n        },\n    },\n};\n", "import mod from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./input-Express.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./input-Express.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./input-Express.vue?vue&type=template&id=13601821&scoped=true&\"\nimport script from \"./input-Express.vue?vue&type=script&lang=js&\"\nexport * from \"./input-Express.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./input-Express.vue?vue&type=style&index=0&id=13601821&lang=scss&scoped=true&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  \"13601821\",\n  \"6e88187b\"\n  \n)\n\nexport default component.exports\n\n/* nuxt-component-imports */\ninstallComponents(component, {Upload: require('/Users/<USER>/Desktop/vue/pc/components/upload.vue').default})\n"], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACXA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACNA;AACA;AACA;;;;;;;;ACFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAbA;AAkBA;AACA;AAAA;AACA;AACA;AADA;AAGA;AACA;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAFA;AAIA;AACA;AApBA;AA5BA;;ACvBA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;ACvBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACXA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACNA;AACA;AACA;;;;;ACFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AANA;AAQA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAFA;AAIA;AAdA;AAgBA;AACA;AAAA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AALA;AAUA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AALA;AAQA;AACA;AAAA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAnCA;AAoCA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAPA;AAnEA;;AChCA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;A", "sourceRoot": ""}