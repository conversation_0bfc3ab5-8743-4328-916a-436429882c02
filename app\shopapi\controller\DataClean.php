<?php

namespace app\shopapi\controller;

use app\common\basics\ShopApi;
use app\common\server\JsonServer;
use think\facade\Db;
use think\facade\Log;

/**
 * 数据清理控制器
 * Class DataClean
 * @package app\shopapi\controller
 */
class DataClean extends ShopApi
{
    public $like_not_need_login = ['cleanDeletedUsers','cleanOrphanedGoodsData', 'getCleanStats ', 'cleanOrphanedUserAddressData', 'cleanOrphanedUserAuthData', 'cleanOrphanedUserDistributionData', 'cleanOrphanedUserReportsData', 'cleanOrphanedUserSignData', 'cleanOrphanedUserTransferRecordsData', 'cleanOrphanedUserActivityLogData', 'cleanOrphanedGoodsCollectData', 'cleanOrphanedGoodsClickData', 'cleanOrphanedGoodsFootprintData', 'cleanOrphanedGoodsCommentData', 'cleanOrphanedOrderGoodsData', 'cleanOrphanedCartData', 'cleanOrphanedRechargeOrderData', 'cleanOrphanedUserAccountLogData', 'cleanOrphanedCouponListData', 'cleanOrphanedDistributionOrderData', 'cleanOrphanedAfter'];

    /**
     * 清理已注销用户及其关联数据
     * @return \think\response\Json
     */
    public function cleanDeletedUsers()
    {
        if (!$this->request->isPost()) {
            return JsonServer::error('请求方式错误');
        }

        try {
            Db::startTrans();

            // 获取已注销的用户ID列表
            $deletedUserIds = Db::name('user')
                ->where('user_delete', 1)
                ->column('id');

            if (empty($deletedUserIds)) {
                Db::commit();
                return JsonServer::success('没有找到已注销的用户', ['deleted_count' => 0]);
            }

            $deletedCount = 0;
            $cleanedTables = [];

            // 清理用户相关表数据
            $userRelatedTables = [
                'user_address' => 'user_id',
                'user_auth' => 'user_id',
                'user_distribution' => 'user_id',
                'user_reports' => 'user_id',
                'user_sign' => 'user_id',
                'user_transfer_records' => 'transferred_user_id',
                'user_activity_log' => 'user_id',
                'goods_collect' => 'user_id',
                'goods_click' => 'user_id',
                'goods_footprint' => 'user_id',
                'goods_comment' => 'user_id',
                'order' => 'user_id',
                // 'order_goods' => 'user_id',
                'cart' => 'user_id',
                'recharge_order' => 'user_id',
                // 'user_account_log' => 'user_id',
                'coupon_list' => 'user_id',
                'distribution_order' => 'user_id',
                // 'after_sale' => 'user_id'
            ];

            // 分批处理，避免一次性删除过多数据
            $batchSize = 100;
            $userBatches = array_chunk($deletedUserIds, $batchSize);

            foreach ($userBatches as $userBatch) {
                foreach ($userRelatedTables as $table => $userField) {
                    // 检查表是否存在
                    if (!$this->tableExists($table)) {
                        continue;
                    }

                    $count = Db::name($table)
                        ->whereIn($userField, $userBatch)
                        ->delete();

                    if ($count > 0) {
                        $cleanedTables[$table] = ($cleanedTables[$table] ?? 0) + $count;
                        $deletedCount += $count;
                    }
                }
            }

            // 最后删除用户主表数据
            foreach ($userBatches as $userBatch) {
                $userCount = Db::name('user')
                    ->whereIn('id', $userBatch)
                    ->delete();

                $cleanedTables['user'] = ($cleanedTables['user'] ?? 0) + $userCount;
                $deletedCount += $userCount;
            }

            Db::commit();

            Log::info('清理已注销用户数据完成', [
                'deleted_user_count' => count($deletedUserIds),
                'cleaned_tables' => $cleanedTables,
                'total_deleted' => $deletedCount
            ]);

            return JsonServer::success('清理已注销用户数据完成', [
                'deleted_users' => count($deletedUserIds),
                'total_deleted_records' => $deletedCount,
                'cleaned_tables' => $cleanedTables
            ]);

        } catch (\Exception $e) {
            Db::rollback();
            Log::error('清理已注销用户数据失败: ' . $e->getMessage());
            return JsonServer::error('清理失败: ' . $e->getMessage());
        }
    }

    /**
     * 清理孤立的商品数据
     * @return \think\response\Json
     */
    public function cleanOrphanedGoodsData()
    {
        if (!$this->request->isPost()) {
            return JsonServer::error('请求方式错误');
        }

        try {
            Db::startTrans();

            $deletedCount = 0;
            $cleanedTables = [];

            // 获取主商品表中存在的商品ID
            $existingGoodsIds = Db::name('goods')
                ->where('del', '<>', 1)
                ->column('id');

            // 商品相关附属表
            $goodsRelatedTables = [
                'goods_item' => 'goods_id',
                'goods_spec' => 'goods_id',
                'goods_spec_value' => 'goods_id',
                'goods_image' => 'goods_id',
                'goods_comment' => 'goods_id',
                'goods_collect' => 'goods_id',
                'goods_click' => 'goods_id',
                'goods_footprint' => 'goods_id'
            ];

            foreach ($goodsRelatedTables as $table => $goodsField) {
                // 检查表是否存在
                if (!$this->tableExists($table)) {
                    continue;
                }

                // 如果没有有效商品，删除所有数据
                if (empty($existingGoodsIds)) {
                    $orphanedCount = Db::name($table)->delete();
                } else {
                    // 分批处理孤立数据
                    $batchSize = 1000;
                    $totalOrphanedCount = 0;

                    do {
                        $orphanedIds = Db::name($table)
                            ->whereNotIn($goodsField, $existingGoodsIds)
                            ->limit($batchSize)
                            ->column('id');

                        if (!empty($orphanedIds)) {
                            $count = Db::name($table)
                                ->whereIn('id', $orphanedIds)
                                ->delete();
                            $totalOrphanedCount += $count;
                        }
                    } while (!empty($orphanedIds) && count($orphanedIds) == $batchSize);

                    $orphanedCount = $totalOrphanedCount;
                }

                if ($orphanedCount > 0) {
                    $cleanedTables[$table] = $orphanedCount;
                    $deletedCount += $orphanedCount;
                }
            }

            // 处理需要关联查询的表（如goods_comment_image）
            if ($this->tableExists('goods_comment_image') && $this->tableExists('goods_comment')) {
                $orphanedCommentImageIds = Db::name('goods_comment_image')->alias('gci')
                    ->leftJoin('goods_comment gc', 'gci.goods_comment_id = gc.id')
                    ->leftJoin('goods g', 'gc.goods_id = g.id')
                    ->where(function($query) {
                        $query->where('g.id',null)->whereOr('g.del', 1);
                    })
                    ->column('gci.id');

                if (!empty($orphanedCommentImageIds)) {
                    $count = Db::name('goods_comment_image')
                        ->whereIn('id', $orphanedCommentImageIds)
                        ->delete();
                    if ($count > 0) {
                        $cleanedTables['goods_comment_image'] = $count;
                        $deletedCount += $count;
                    }
                }
            }

            // 清理已删除商品的主表数据
            $deletedGoodsCount = Db::name('goods')
                ->where('del', 1)
                ->delete();

            if ($deletedGoodsCount > 0) {
                $cleanedTables['goods'] = $deletedGoodsCount;
                $deletedCount += $deletedGoodsCount;
            }

            Db::commit();

            Log::info('清理孤立商品数据完成', [
                'cleaned_tables' => $cleanedTables,
                'total_deleted' => $deletedCount
            ]);

            return JsonServer::success('清理孤立商品数据完成', [
                'total_deleted_records' => $deletedCount,
                'cleaned_tables' => $cleanedTables
            ]);

        } catch (\Exception $e) {
            Db::rollback();
            Log::error('清理孤立商品数据失败: ' . $e->getMessage().'_'.$e->getLine());
            return JsonServer::error('清理失败: ' . $e->getMessage().'_'.$e->getLine());
        }
    }

    /**
     * 执行完整的数据清理
     * @return \think\response\Json
     */
    public function cleanAll()
    {
        if (!$this->request->isPost()) {
            return JsonServer::error('请求方式错误');
        }

        try {
            $results = [];
            
            // 清理已注销用户数据
            $userCleanResult = $this->cleanDeletedUsers();
            $userCleanData = json_decode($userCleanResult->getContent(), true);
            $results['user_clean'] = $userCleanData;
            
            // 清理孤立商品数据
            $goodsCleanResult = $this->cleanOrphanedGoodsData();
            $goodsCleanData = json_decode($goodsCleanResult->getContent(), true);
            $results['goods_clean'] = $goodsCleanData;
            
            $totalDeleted = ($userCleanData['data']['total_deleted_records'] ?? 0) + 
                           ($goodsCleanData['data']['total_deleted_records'] ?? 0);

            return JsonServer::success('数据清理完成', [
                'total_deleted_records' => $totalDeleted,
                'details' => $results
            ]);

        } catch (\Exception $e) {
            Log::error('完整数据清理失败: ' . $e->getMessage());
            return JsonServer::error('清理失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取垃圾数据统计信息
     * @return \think\response\Json
     */
    public function getCleanStats()
    {
        if (!$this->request->isGet()) {
            return JsonServer::error('请求方式错误');
        }

        try {
            $stats = [];

            // 统计已注销用户数量
            $deletedUsersCount = Db::name('user')
                ->where('user_delete', 1)
                ->count();
            $stats['deleted_users'] = $deletedUsersCount;

            // 统计已删除商品数量
            $deletedGoodsCount = Db::name('goods')
                ->where('del', 1)
                ->count();
            $stats['deleted_goods'] = $deletedGoodsCount;

            // 获取存在的商品ID
            $existingGoodsIds = Db::name('goods')
                ->where('del', '<>', 1)
                ->column('id');

            // 统计孤立的商品相关数据
            $orphanedStats = [];
            $goodsRelatedTables = [
                'goods_item' => 'goods_id',
                'goods_spec' => 'goods_id',
                'goods_spec_value' => 'goods_id',
                'goods_image' => 'goods_id',
                'goods_comment' => 'goods_id',
                'goods_collect' => 'goods_id',
                'goods_click' => 'goods_id',
                'goods_footprint' => 'goods_id'
            ];

            foreach ($goodsRelatedTables as $table => $goodsField) {
                if (!$this->tableExists($table)) {
                    continue;
                }

                $orphanedCount = empty($existingGoodsIds) ?
                    Db::name($table)->count() :
                    Db::name($table)->whereNotIn($goodsField, $existingGoodsIds)->count();
                $orphanedStats[$table] = $orphanedCount;
            }

            $stats['orphaned_goods_data'] = $orphanedStats;

            return JsonServer::success('获取统计信息成功', $stats);

        } catch (\Exception $e) {
            Log::error('获取清理统计失败: ' . $e->getMessage());
            return JsonServer::error('获取统计失败: ' . $e->getMessage());
        }
    }

    /**
     * 检查表是否存在
     * @param string $tableName
     * @return bool
     */
    private function tableExists($tableName)
    {
        try {
            $prefix = config('database.connections.mysql.prefix');
            $fullTableName = $prefix . $tableName;

            $result = Db::query("SHOW TABLES LIKE '{$fullTableName}'");
            return !empty($result);
        } catch (\Exception $e) {
            Log::warning("检查表 {$tableName} 是否存在时出错: " . $e->getMessage());
            return false;
        }
    }
}
