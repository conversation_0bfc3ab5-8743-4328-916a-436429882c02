<?php
namespace app\shopapi\controller;

use app\common\basics\ShopApi;
use app\common\server\JsonServer;
use app\shopapi\logic\IndustryAnalysisLogic;

/**
 * 行业数据分析 - 控制器
 * Class IndustryAnalysis
 * @package app\shopapi\controller
 */
class IndustryAnalysis extends ShopApi
{
    /**
     * @notes 获取行业概览数据
     * @return \think\response\Json
     */
    public function overview()
    {
        $params = $this->request->get();
        $data = IndustryAnalysisLogic::getIndustryOverview($this->shop_id, $params);
        return JsonServer::success('获取成功', $data);
    }

    /**
     * @notes 获取销售趋势数据
     * @return \think\response\Json
     */
    public function salesTrend()
    {
        $params = $this->request->get();
        $data = IndustryAnalysisLogic::getSalesTrend($this->shop_id, $params);
        return JsonServer::success('获取成功', $data);
    }

    /**
     * @notes 获取品类分析数据
     * @return \think\response\Json
     */
    public function categoryAnalysis()
    {
        $params = $this->request->get();
        $data = IndustryAnalysisLogic::getCategoryAnalysis($this->shop_id, $params);
        return JsonServer::success('获取成功', $data);
    }

    /**
     * @notes 获取区域分析数据
     * @return \think\response\Json
     */
    public function regionAnalysis()
    {
        $params = $this->request->get();
        $data = IndustryAnalysisLogic::getRegionAnalysis($this->shop_id, $params);
        return JsonServer::success('获取成功', $data);
    }

    /**
     * @notes 获取竞争分析数据
     * @return \think\response\Json
     */
    public function competitorAnalysis()
    {
        $params = $this->request->get();
        $data = IndustryAnalysisLogic::getCompetitorAnalysis($this->shop_id, $params);
        return JsonServer::success('获取成功', $data);
    }

    /**
     * @notes 获取行业排行数据
     * @return \think\response\Json
     */
    public function industryRanking()
    {
        $params = $this->request->get();
        $data = IndustryAnalysisLogic::getIndustryRanking($this->shop_id, $params);
        return JsonServer::success('获取成功', $data);
    }

    /**
     * @notes 导出行业分析数据
     * @return \think\response\Json
     */
    public function export()
    {
        $params = $this->request->get();
        $result = IndustryAnalysisLogic::exportData($this->shop_id, $params);
        if ($result !== true) {
            return JsonServer::error($result);
        }
        return JsonServer::success('导出成功');
    }
}
