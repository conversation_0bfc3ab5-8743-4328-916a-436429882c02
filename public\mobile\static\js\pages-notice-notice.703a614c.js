(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-notice-notice"],{"02f3":function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return s})),n.d(e,"a",(function(){}));var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{staticClass:"notice"},[n("mescroll-body",{ref:"mescrollRef",attrs:{up:t.upOption},on:{init:function(e){arguments[0]=e=t.$handleEvent(e),t.mescrollInit.apply(void 0,arguments)},down:function(e){arguments[0]=e=t.$handleEvent(e),t.downCallback.apply(void 0,arguments)},up:function(e){arguments[0]=e=t.$handleEvent(e),t.upCallback.apply(void 0,arguments)}}},t._l(t.lists,(function(e,i){return n("v-uni-view",{key:i,staticClass:"notice-item bg-white"},[n("v-uni-view",{staticClass:"flex row-between item-header"},[n("v-uni-view",{staticClass:"header-title md bold"},[t._v(t._s(e.title))]),n("v-uni-view",{staticClass:"header-time muted xs"},[t._v(t._s(e.create_time))])],1),n("v-uni-view",{staticClass:"item-main m-t-24"},[n("v-uni-view",{staticClass:"content sm lighter"},[t._v(t._s(e.content))])],1)],1)})),1)],1)},s=[]},"07fc":function(t,e,n){"use strict";n.r(e);var i=n("02f3"),s=n("9828");for(var o in s)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return s[t]}))}(o);n("3cc6");var c=n("f0c5"),l=Object(c["a"])(s["default"],i["b"],i["c"],!1,null,"30c8b855",null,!1,i["a"],void 0);e["default"]=l.exports},"0918":function(t,e,n){"use strict";n("7a82");var i=n("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("99af");var s=n("b550"),o=i(n("53f3")),c={mixins:[o.default],data:function(){return{lists:[],upOption:{empty:{icon:"/static/images/news_null.png",tip:"暂无消息通知~"}}}},onLoad:function(){switch(this.type=this.$Route.query.type,this.type){case"system":uni.setNavigationBarTitle({title:"系统通知"});break;case"earning":uni.setNavigationBarTitle({title:"收益通知"});break}},methods:{upCallback:function(t){var e=this;(0,s.getNoticeLists)({page_size:t.size,page_no:t.num,type:this.type}).then((function(n){var i=n.data;1==t.num&&(e.lists=[]);var s=i.list,o=s.length,c=!!i.more;e.lists=e.lists.concat(s),e.mescroll.endSuccess(o,c)})).catch((function(){e.mescroll.endErr()}))}}};e.default=c},"2f5b":function(t,e,n){var i=n("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.notice[data-v-30c8b855]{overflow:hidden}.notice .notice-item[data-v-30c8b855]{padding:0 %?20?% %?30?%;border-radius:%?10?%;margin:%?20?% %?20?% 0}.notice .notice-item .item-header[data-v-30c8b855]{padding:%?19?% 0;border-bottom:1px solid #e5e5e5}',""]),t.exports=e},"3cc6":function(t,e,n){"use strict";var i=n("9925"),s=n.n(i);s.a},"53f3":function(t,e,n){"use strict";n("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i={data:function(){return{mescroll:null}},onPullDownRefresh:function(){this.mescroll&&this.mescroll.onPullDownRefresh()},onPageScroll:function(t){this.mescroll&&this.mescroll.onPageScroll(t)},onReachBottom:function(){this.mescroll&&this.mescroll.onReachBottom()},methods:{mescrollInit:function(t){console.log(t),this.mescroll=t,this.mescrollInitByRef()},mescrollInitByRef:function(){if(!this.mescroll||!this.mescroll.resetUpScroll){var t=this.$refs.mescrollRef;t&&(this.mescroll=t.mescroll)}},downCallback:function(){var t=this;this.mescroll.optUp.use?this.mescroll.resetUpScroll():setTimeout((function(){t.mescroll.endSuccess()}),500)},upCallback:function(){var t=this;setTimeout((function(){t.mescroll.endErr()}),500)}},mounted:function(){this.mescrollInitByRef()}},s=i;e.default=s},9828:function(t,e,n){"use strict";n.r(e);var i=n("0918"),s=n.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);e["default"]=s.a},9925:function(t,e,n){var i=n("2f5b");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var s=n("4f06").default;s("111af811",i,!0,{sourceMap:!1,shadowMode:!1})}}]);