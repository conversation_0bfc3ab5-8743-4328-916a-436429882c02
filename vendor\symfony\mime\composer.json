{"name": "symfony/mime", "type": "library", "description": "Allows manipulating MIME messages", "keywords": ["mime", "mime-type"], "homepage": "https://symfony.com", "license": "MIT", "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "require": {"php": ">=7.1.3", "symfony/polyfill-intl-idn": "^1.10", "symfony/polyfill-mbstring": "^1.0"}, "require-dev": {"egulias/email-validator": "^2.1.10|^3.1", "symfony/dependency-injection": "^3.4|^4.1|^5.0"}, "conflict": {"egulias/email-validator": "~3.0.0", "symfony/mailer": "<4.4"}, "autoload": {"psr-4": {"Symfony\\Component\\Mime\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "minimum-stability": "dev"}