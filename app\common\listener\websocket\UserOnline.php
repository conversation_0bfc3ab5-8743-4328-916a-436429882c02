<?php

namespace app\common\listener\websocket;


use app\common\logic\ChatLogic;
use app\common\model\kefu\ChatRelation;
use app\common\model\OfflineMessages;

/**
 * 用户上线
 * Class UserOnline
 * @package app\common\listener\websocket
 */
class UserOnline
{
    public function handle($params)
    {
        $handleClass = $params['handle'];
        $fd = $params['fd'];

        // 当前用户信息
        $user = $handleClass->getDataByFd($fd);

        // 接收人信息
        $to_id = $params['data']['kefu_id'] ?? 0;

        if (empty($user['type'] || $user['type'] != 'user' || empty($to_id))) {
            return true;
        }
        $offlineMessages = new OfflineMessages();
            // 假设OfflineMessage模型有一个方法getOfflineMessagesByUserId来获取离线消息
        $offlineMessages = $offlineMessages->getOfflineMessagesByUserId($user['uid'], $user['type']);

        foreach ($offlineMessages as $message) {
            // 推送离线消息给用户
            $handleClass->pushData($to_id, 'offline_message', $message);

            // 标记消息为已读或删除消息（取决于你的业务逻辑）
            $message->delete();
        }
        // 是否有绑定关系
        $relation_id = ChatLogic::bindRelation($user['uid'], $to_id, $user['shop_id'], [
            'client' => $user['client'],
        ], 1);

        $relation = ChatRelation::where(['id' => $relation_id])->findOrEmpty();

        $to_fd = $handleClass->getFdByUid($to_id, 'kefu');
        if (!empty($to_fd)) {
            if (!$relation->isEmpty()) {
                // 转换为数组以便修改和传递
                $relationData = $relation->toArray();
                $relationData['online'] = 1;
                return $handleClass->pushData($to_fd, 'user_online', $relationData);
            }
        }
        return true;
    }
}