(window.webpackJsonp=window.webpackJsonp||[]).push([[40],{537:function(t,e,r){var content=r(606);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,r(14).default)("882b3648",content,!0,{sourceMap:!1})},602:function(t,e,r){t.exports=r.p+"img/time.af8667a.png"},603:function(t,e,r){t.exports=r.p+"img/error.2251fae.png"},604:function(t,e,r){t.exports=r.p+"img/success.e4b77b0.png"},605:function(t,e,r){"use strict";r(537)},606:function(t,e,r){var n=r(13)(!1);n.push([t.i,".detail,.detail .main{width:100%;height:788px}.detail .main{padding:30px}.detail .main .header{padding:0 0 40px;margin-bottom:25px;border-bottom:1px dotted #e5e5e5}.detail .main .header img{width:32px;height:32px}.detail .main .header .admin{background:#f6f6f6;padding:0 30px 16px}.detail .main .header .admin a:hover{color:#ff2c3c}",""]),t.exports=n},666:function(t,e,r){"use strict";r.r(e);var n=[function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"flex normal xxl bold",staticStyle:{"font-weight":"600"}},[n("img",{staticClass:"m-r-12",attrs:{src:r(602),alt:""}}),t._v("\n                    恭喜您，资料提交成功\n                ")])},function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"flex normal xxl bold",staticStyle:{"font-weight":"600"}},[n("img",{staticClass:"m-r-12",attrs:{src:r(603),alt:""}}),t._v("\n                    很遗憾，审核不通过！\n                ")])},function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"flex normal xxl bold",staticStyle:{"font-weight":"600"}},[n("img",{staticClass:"m-r-12",attrs:{src:r(604),alt:""}}),t._v("\n                    恭喜您，审核已通过！\n                ")])},function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",{staticClass:"xs m-t-16"},[r("span",[t._v("登录密码：")]),t._v("\n                        登录密码：密码是您在创建账号时设置的登录密码，如忘记密码可联系官方客服进行修改！\n                    ")])}],l=r(6),o=(r(51),{head:function(){return{title:this.$store.getters.headTitle,link:[{rel:"icon",type:"image/x-icon",href:this.$store.getters.favicon}]}},data:function(){return{detail:{}}},mounted:function(){var t=this;return Object(l.a)(regeneratorRuntime.mark((function e(){var r,data;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return console.log(t.$route.query.id),e.next=3,t.$get("ShopApply/detail",{params:{id:t.$route.query.id}});case 3:r=e.sent,data=r.data,t.detail=data,console.log("我艹啊私房话哀诉还是");case 7:case"end":return e.stop()}}),e)})))()},methods:{}}),d=(r(605),r(9)),component=Object(d.a)(o,(function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",{staticClass:"detail"},[r("div",{staticClass:"m-t-20"},[r("el-breadcrumb",{attrs:{separator:"/"}},[r("el-breadcrumb-item",{attrs:{to:{path:"/"}}},[t._v("首页")]),t._v(" "),r("el-breadcrumb-item",{attrs:{to:{path:"/store_settled"}}},[r("a",[t._v("商家入驻")])]),t._v(" "),r("el-breadcrumb-item",{attrs:{to:{path:"/store_settled/record"}}},[r("a",[t._v("申请列表")])]),t._v(" "),r("el-breadcrumb-item",[t._v("详情")])],1)],1),t._v(" "),r("div",{staticClass:"main  bg-white m-t-20"},[r("div",{staticClass:"header"},[r("div",{staticClass:"m-b-30 pointer",on:{click:function(e){return t.$router.back()}}},[r("i",{staticClass:"el-icon-arrow-left"}),t._v("\n                返回\n            ")]),t._v(" "),1==t.detail.audit_status?r("div",[t._m(0),t._v(" "),r("div",{staticClass:"xs muted m-t-12 m-l-42"},[t._v("\n                    预计在3个工作日内审核完毕，如通过我们将会发送短信通知您，请注意查收！\n                ")])]):3==t.detail.audit_status?r("div",[t._m(1),t._v(" "),r("div",{staticClass:"xs muted m-t-12 m-l-42"},[t._v("\n                    请尽量完善您的资料信息再重新提交！\n                ")])]):2==t.detail.audit_status?r("div",[t._m(2),t._v(" "),r("div",{staticClass:"xs muted m-t-12 m-l-42"},[t._v("\n                    您的审核已通过\n                ")]),t._v(" "),r("div",{staticClass:"admin m-t-20"},[r("div",{staticClass:"xs p-t-16"},[r("span",[t._v("PC管理后台地址：")]),t._v(" "),r("a",{attrs:{href:t.detail.admin_address}},[t._v("\n                            "+t._s(t.detail.admin_address)+"\n                        ")])]),t._v(" "),r("div",{staticClass:"xs m-t-16"},[r("span",[t._v("商家账号：")]),t._v(" "),r("a",{attrs:{href:"",target:"_blank",rel:"noopener noreferrer"}},[t._v("\n                            "+t._s(t.detail.account)+"\n                        ")])]),t._v(" "),t._m(3)])]):t._e()]),t._v(" "),r("div",{staticClass:"section"},[r("div",{staticClass:"xl bold normal m-b-30"},[t._v("资料详情")]),t._v(" "),r("el-form",{ref:"form",staticClass:"demo-form",attrs:{model:t.detail,size:"medium","label-position":"left","label-width":"110px"}},[r("el-form-item",{attrs:{label:"商家名称:",prop:"name"}},[r("span",[t._v(t._s(t.detail.name))])]),t._v(" "),r("el-form-item",{attrs:{label:"主营类目:",prop:"name"}},[r("span",[t._v(t._s(t.detail.cid_desc))])]),t._v(" "),r("el-form-item",{attrs:{label:"联系人姓名:",prop:"name"}},[r("span",[t._v(t._s(t.detail.nickname))])]),t._v(" "),r("el-form-item",{attrs:{label:"手机号码:",prop:"name"}},[r("span",[t._v(t._s(t.detail.mobile))])]),t._v(" "),r("el-form-item",{attrs:{label:"商家账号:",prop:"name"}},[r("span",[t._v(t._s(t.detail.account))])]),t._v(" "),r("el-form-item",{attrs:{label:"营业执照:",prop:"name"}},t._l(t.detail.license,(function(t,e){return r("el-image",{key:e,staticStyle:{width:"72px",height:"72px","margin-right":"10px"},attrs:{src:t,fit:"fit"}})})),1)],1)],1)])])}),n,!1,null,null,null);e.default=component.exports}}]);