{layout name="layout1" /}
<style>
    .layui-table-cell {
        height: auto !important;
        white-space: normal;
    }
    .status-badge {
        padding: 4px 12px;
        border-radius: 12px;
        color: white;
        font-size: 12px;
        font-weight: 500;
    }
    .status-0 { background-color: #999; }
    .status-1 { background-color: #5FB878; }
    .status-2 { background-color: #FFB800; }
    .status-3 { background-color: #FF5722; }
    .status-4 { background-color: #01AAED; }
    .status-5 { background-color: #F56C6C; }

    /* 统计卡片样式 */
    .summary-cards {
        margin-bottom: 20px;
    }
    .summary-card {
        background: #fff;
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        border: 1px solid #e6e6e6;
        transition: all 0.3s ease;
    }
    .summary-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(0,0,0,0.15);
    }
    .summary-card .layui-card-header {
        border-bottom: 1px solid #f0f0f0;
        font-weight: 500;
        color: #666;
    }
    .summary-card .layui-card-body p {
        font-size: 24px;
        font-weight: bold;
        color: #333;
        margin: 0;
    }
</style>

<div class="wrapper">
    <div class="layui-card">
        <div class="layui-card-body">
            <!-- 操作提示 -->
            <div class="layui-collapse like-layui-collapse" lay-accordion="" style="border:1px dashed #c4c4c4">
                <div class="layui-colla-item">
                    <h2 class="layui-colla-title like-layui-colla-title" style="background-color: #fff">操作提示</h2>
                    <div class="layui-colla-content layui-show">
                        <p>*查看代理保证金的详细信息和状态变化。</p>
                        <p>*支持按用户信息、状态等条件筛选保证金记录。</p>
                        <p>*可查看保证金的支付、退款等详细流程信息。</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 保证金汇总 -->
        <div class="layui-card-body">
            <h2 style="margin: 20px 0;">保证金汇总</h2>
            <div class="layui-row layui-col-space15 summary-cards">
                <div class="layui-col-sm6 layui-col-md3">
                    <div class="layui-card summary-card">
                        <div class="layui-card-header">总保证金金额</div>
                        <div class="layui-card-body"><p id="total_deposit">¥0</p></div>
                    </div>
                </div>
                <div class="layui-col-sm6 layui-col-md3">
                    <div class="layui-card summary-card">
                        <div class="layui-card-header">已支付保证金</div>
                        <div class="layui-card-body"><p id="paid_deposit">¥0</p></div>
                    </div>
                </div>
                <div class="layui-col-sm6 layui-col-md3">
                    <div class="layui-card summary-card">
                        <div class="layui-card-header">退款中保证金</div>
                        <div class="layui-card-body"><p id="refunding_deposit">¥0</p></div>
                    </div>
                </div>
                <div class="layui-col-sm6 layui-col-md3">
                    <div class="layui-card summary-card">
                        <div class="layui-card-header">已退款保证金</div>
                        <div class="layui-card-body"><p id="refunded_deposit">¥0</p></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主体内容 -->
        <div class="layui-card-body">
            <!-- 搜索区域 -->
            <div class="layui-form" lay-filter="">
                <div class="layui-form-item">
                    <div class="layui-row">
                        <div class="layui-inline">
                            <label class="layui-form-label">用户信息:</label>
                            <div class="layui-input-block">
                                <input type="text" name="keyword" placeholder="用户昵称/编号" autocomplete="off" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">保证金状态:</label>
                            <div class="layui-input-block">
                                <select name="status" lay-search="">
                                    <option value="">全部状态</option>
                                    <option value="0">未支付</option>
                                    <option value="1">已支付</option>
                                    <option value="2">公示期</option>
                                    <option value="2_refund">退款中(公示期)</option>
                                    <option value="3">退款申请中</option>
                                    <option value="4">已退款</option>
                                    <option value="5">退款失败</option>
                                </select>
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">支付时间:</label>
                            <div class="layui-input-inline">
                                <input type="text" id="start_time" name="start_time" class="layui-input" autocomplete="off" placeholder="开始时间">
                            </div>
                            <div class="layui-form-mid">-</div>
                            <div class="layui-input-inline">
                                <input type="text" id="end_time" name="end_time" class="layui-input" autocomplete="off" placeholder="结束时间">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <button class="layui-btn" lay-submit lay-filter="search">
                                <i class="layui-icon layui-icon-search"></i> 搜索
                            </button>
                            <button class="layui-btn layui-btn-primary" type="reset">
                                <i class="layui-icon layui-icon-refresh"></i> 重置
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 数据表格 -->
            <table id="deposit-lists" lay-filter="deposit-lists"></table>

            <!-- 操作列模板 -->
            <script type="text/html" id="table-operation">
                <a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="detail">详情</a>
            </script>
        </div>
    </div>
</div>

<script>
layui.config({
    version: "{$front_version}",
    base: '/static/lib/' //静态资源所在路径
}).use(['form'], function(){
    var $ = layui.$
        , form = layui.form
        , table = layui.table
        , element = layui.element
        , laydate = layui.laydate;

    // 日期选择器
    laydate.render({type: "datetime", elem: "#start_time", trigger: "click"});
    laydate.render({type: "datetime", elem: "#end_time", trigger: "click"});

    // 用户信息模板
    var userInfoTpl = function(d) {
        return '<div style="display: flex; align-items: center;">' +
               '<img src="' + (d.avatar || '__ADMIN_PATH__/images/default_avatar.png') + '" style="width: 40px; height: 40px; border-radius: 50%; margin-right: 10px;">' +
               '<div>' +
               '<div style="font-weight: bold;">' + (d.nickname || '未知用户') + '</div>' +
               '<div style="color: #999; font-size: 12px;">编号: ' + (d.user_sn || '') + '</div>' +
               '</div>' +
               '</div>';
    };

    // 状态模板
    var statusTpl = function(d) {
        var statusText = '';
        var statusClass = '';
        switch(d.status) {
            case 0:
                statusText = '未支付';
                statusClass = 'status-0';
                break;
            case 1:
                statusText = '已支付';
                statusClass = 'status-1';
                break;
            case 2:
                if (d.refund_request_time > 0) {
                    statusText = '退款中(公示期)';
                    statusClass = 'status-2';
                } else {
                    statusText = '公示期';
                    statusClass = 'status-2';
                }
                break;
            case 3:
                statusText = '退款申请中';
                statusClass = 'status-3';
                break;
            case 4:
                statusText = '已退款';
                statusClass = 'status-4';
                break;
            case 5:
                statusText = '退款失败';
                statusClass = 'status-5';
                break;
            default:
                statusText = '未知状态';
                statusClass = 'status-0';
        }
        return '<span class="status-badge ' + statusClass + '">' + statusText + '</span>';
    };

    // 渲染表格
    like.tableLists("#deposit-lists", "{:url()}", [
        {field: "id", width: 60, title: "ID"}
        , {field: "user_info", width: 200, align: "center", title: "用户信息", templet: userInfoTpl}
        , {field: "amount", width: 120, align: "center", title: "保证金金额", templet: function(d){
            return '<span style="color: #FF5722; font-weight: bold;">¥' + (d.amount || 0) + '</span>';
        }}
        , {field: "current_balance", width: 120, align: "center", title: "当前余额", templet: function(d){
            return '<span style="color: #5FB878; font-weight: bold;">¥' + (d.current_balance || 0) + '</span>';
        }}
        , {field: "status", width: 120, align: "center", title: "状态", templet: statusTpl}
        , {field: "payment_date", width: 160, align: "center", title: "支付时间"}
        , {field: "refund_request_time", width: 160, align: "center", title: "退款申请时间"}
        , {field: "refund_time", width: 160, align: "center", title: "退款时间"}
        , {title: "操作", width: 120, align: "center", fixed: "right", toolbar: '#table-operation'}
    ]);

    // 监听搜索
    form.on('submit(search)', function (data) {
        var field = data.field;
        // 执行重载
        table.reload('deposit-lists', {
            where: field,
            page: {
                curr: 1
            }
        });
    });

    // 监听工具条
    table.on('tool(deposit-lists)', function(obj){
        var data = obj.data;
        if(obj.event === 'detail'){
            layer.open({
                type: 2,
                title: '保证金详情',
                shadeClose: true,
                shade: 0.8,
                area: ['80%', '80%'],
                content: '{:url("finance.agent/depositInfo")}?id=' + data.id
            });
        }
    });

    // 加载统计数据
    like.ajax({
        url: "{:url('finance.agent/depositStatistics')}",
        type: "GET",
        success: function(res) {
            if(res.code === 1) {
                $("#total_deposit").html("¥" + (res.data.total_deposit || 0));
                $("#paid_deposit").html("¥" + (res.data.paid_deposit || 0));
                $("#refunding_deposit").html("¥" + (res.data.refunding_deposit || 0));
                $("#refunded_deposit").html("¥" + (res.data.refunded_deposit || 0));
            }
        }
    });
});
</script>
