{layout name="layout2" /}
<style>
    .layui-form-label {
        color: #6a6f6c;
        width: 100px;
    }
    .layui-input-block {
        margin-left: 130px;
    }
    .tips{
        color: red;
    }
    .goods-li {
        float: left;
        opacity: 1;
        position: relative;
    }

    .goods-img {
        width: 80px;
        height: 80px;
        padding: 4px;
    }
    .goods-img-del-x {
        position: absolute;
        z-index: 100;
        top: -4px;
        right: -2px;
        width: 20px;
        height: 20px;
        font-size: 16px;
        line-height: 16px;
        color: #fff;
        text-align: center;
        cursor: pointer;
        background: hsla(0, 0%, 60%, .6);
        border-radius: 10px;
    }
</style>
<div class="layui-form" lay-filter="layuiadmin-form-user_level" id="layuiadmin-form-user_level" style="padding: 20px 30px 0 0;">
    <input type="hidden" name="id" value="{$detail.id}">
    <div class="layui-form-item">
        <label class="layui-form-label"><span class="tips">*</span>店铺徽标名称：</label>
        <div class="layui-input-inline">
            <input type="text" name="name" value="{$detail.name}" lay-verify="required" lay-verType="tips"  autocomplete="off" class="layui-input">
        </div>
    </div>

    <div class="layui-form-item ">
        <label class="layui-form-label"><span class="tips">*</span>等级图标：</label>
        <div class="layui-inline" >
            <div class="like-upload-image">
                {if $detail.image}
                <div class="upload-image-div">
                    <img src="{$detail.image}" alt="img">
                    <input type="hidden" name="image" value="{$detail.image}">
                    <div class="del-upload-btn">x</div>
                </div>
                <div class="upload-image-elem" style="display:none;"><a class="add-upload-image" id="image"> + 添加图片</a></div>
                {else}
                <div class="upload-image-elem"><a class="add-upload-image" id="image"> + 添加图片</a></div>
                {/if}
            </div>
            <div class=" layui-form-mid layui-word-aux" style="white-space: nowrap">建议尺寸：100*100像素，jpg，jpeg，png图片类型</div>

        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">等级说明：</label>
        <div class="layui-input-block">
            <textarea name="remark" value="{$detail.remark}"  class="layui-textarea">{$detail.remark}</textarea>
        </div>
    </div>

    <div class="layui-form-item layui-hide">
        <input type="button" lay-submit lay-filter="edit-user_level-submit" id="edit-user_level-submit" value="确认">
    </div>
</div>
<style>
    .layui-form-label {
        width: 100px;
    }
    .layui-input-block {
        margin-left: 130px;
    }
</style>
<script>
    layui.config({
        version:"{$front_version}",
        base: '/static/lib/' //静态资源所在路径
    }).use(['form'], function(){
        var $ = layui.$
            ,form = layui.form;

        // 图片上传
        like.delUpload();
        // 图标
        $(document).on("click", "#image", function () {
            like.imageUpload({
                limit: 1,
                field: "image",
                that: $(this)
            });
        })
        // 背景图
        $(document).on("click", "#background_image", function () {
            like.imageUpload({
                limit: 1,
                field: "background_image",
                that: $(this)
            });
        })

    })

</script>