<?php
namespace app\shopapi\http\middleware;

use think\Request;
use think\Response;

class Cors
{
    public function handle(Request $request, \Closure $next)
    {
        $response = $next($request);

        $response->header('Access-Control-Allow-Origin', '*');

        // 设置允许的方法
        $response->header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');

        // 设置允许的请求头
        $response->header('Access-Control-Allow-Headers', 'Content-Type, Authorization, userauth');

        // 对于预检请求，直接返回响应
        if ($request->isOptions()) {
            return $response;
        }

        return $response;
    }
}