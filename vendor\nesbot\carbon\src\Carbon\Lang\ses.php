<?php

/**
 * This file is part of the Carbon package.
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

return array_replace_recursive(require __DIR__.'/en.php', [
    'meridiem' => ['<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>'],
    'weekdays' => ['Alhadi', 'Atinni', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>'],
    'weekdays_short' => ['Alh', 'Ati', 'Ata', 'Ala', 'Alm', 'Alz', 'Asi'],
    'weekdays_min' => ['Alh', 'Ati', 'Ata', 'Ala', 'Alm', 'Alz', 'Asi'],
    'months' => ['Žanwiye', 'Fe<PERSON>iriye', 'Marsi', 'A<PERSON>ril', 'Me', '<PERSON>uwe<PERSON>', '<PERSON>uyye', 'Ut', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>'],
    'months_short' => ['<PERSON><PERSON>', 'Fee', '<PERSON>', 'A<PERSON>', 'Me', '<PERSON>uw', '<PERSON>uy', 'Ut', '<PERSON>k', 'Okt', 'Noo', 'Dee'],
    'first_day_of_week' => 1,
    'formats' => [
        'LT' => 'HH:mm',
        'LTS' => 'HH:mm:ss',
        'L' => 'D/M/YYYY',
        'LL' => 'D MMM YYYY',
        'LLL' => 'D MMMM YYYY HH:mm',
        'LLLL' => 'dddd D MMMM YYYY HH:mm',
    ],

    'month' => ':count alaada', // less reliable
    'm' => ':count alaada', // less reliable
    'a_month' => ':count alaada', // less reliable

    'hour' => ':count ɲaajin', // less reliable
    'h' => ':count ɲaajin', // less reliable
    'a_hour' => ':count ɲaajin', // less reliable

    'minute' => ':count zarbu', // less reliable
    'min' => ':count zarbu', // less reliable
    'a_minute' => ':count zarbu', // less reliable

    'year' => ':count jiiri',
    'y' => ':count jiiri',
    'a_year' => ':count jiiri',

    'week' => ':count jirbiiyye',
    'w' => ':count jirbiiyye',
    'a_week' => ':count jirbiiyye',

    'day' => ':count zaari',
    'd' => ':count zaari',
    'a_day' => ':count zaari',

    'second' => ':count ihinkante',
    's' => ':count ihinkante',
    'a_second' => ':count ihinkante',
]);
