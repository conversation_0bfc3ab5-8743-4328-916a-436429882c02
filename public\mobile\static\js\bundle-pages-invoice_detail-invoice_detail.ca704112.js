(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["bundle-pages-invoice_detail-invoice_detail"],{"15a3":function(t,e){t.exports="data:image/png;base64,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"},"2ea7":function(t,e,i){"use strict";i.r(e);var n=i("4b84"),a=i.n(n);for(var s in n)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(s);e["default"]=a.a},"3a5c":function(t,e){t.exports="data:image/png;base64,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"},"4b84":function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("14d9");var n=i("7161"),a=i("753f"),s={data:function(){return{invoiceInfo:{},goodsInfo:{},shopInfo:{},orderId:"",create_time:"",order_status_text:"",order_sn:""}},methods:{initInvoiceInfoFunc:function(){var t=this;(0,n.apiOrderInvoiceDetail)({id:this.orderId}).then((function(e){t.invoiceInfo=e.data.invoice,t.goodsInfo=e.data.order_goods,t.shopInfo=e.data.shop,t.create_time=e.data.create_time,t.order_status_text=e.data.order_status_text,t.order_sn=e.data.order_sn}))},toEditInvoice:function(){this.$Router.push({path:"/bundle/pages/invoice/invoice",query:{invoice_id:this.invoiceInfo.id,shop_id:this.shopInfo.id,type:a.invoiceType["ORDERDETAILEdit"]}})}},onLoad:function(){var t=this.$Route.query;this.orderId=t.id||""},onShow:function(){this.initInvoiceInfoFunc()}};e.default=s},"4b9e":function(t,e,i){var n=i("24fb");e=n(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.invoice-detail[data-v-1a14080b]{padding-bottom:%?120?%;background:linear-gradient(180deg,#ff2c3c %?230?%,transparent 0)}.invoice-detail .header[data-v-1a14080b]{height:%?140?%}.invoice-detail .header uni-image[data-v-1a14080b]{width:%?44?%;height:%?44?%}.invoice-detail .main .card[data-v-1a14080b]{padding:%?24?% 0 %?30?% 0;border-radius:%?14?%}.invoice-detail .main .form-item[data-v-1a14080b]{display:flex;padding:%?12?% %?30?%;color:#333;font-size:%?28?%}.invoice-detail .main .form-item .label[data-v-1a14080b]{width:%?120?%;text-align:right;margin-right:%?40?%}.invoice-detail .main .form-item .content[data-v-1a14080b]{flex:1}.invoice-detail .footer[data-v-1a14080b]{left:0;bottom:%?20?%;width:100%;padding:%?24?%;position:fixed}.invoice-detail .footer .btn[data-v-1a14080b]{height:%?88?%;color:#fff;background-color:#ff2c3c}',""]),t.exports=e},"4e02":function(t,e,i){var n=i("4b9e");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=i("4f06").default;a("cec7d7a6",n,!0,{sourceMap:!1,shadowMode:!1})},"5a0b":function(t,e,i){var n=i("7829");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=i("4f06").default;a("8ea98558",n,!0,{sourceMap:!1,shadowMode:!1})},"68d96":function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return s})),i.d(e,"a",(function(){return n}));var n={shopTitle:i("f8ba").default,orderGoods:i("c3dd").default},a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{staticClass:"invoice-detail"},[n("v-uni-view",{staticClass:"header white text-center flex-col row-center"},[n("v-uni-view",{staticClass:"flex row-center lg bold"},[t.invoiceInfo.status?n("v-uni-image",{attrs:{src:i("3a5c"),mode:""}}):n("v-uni-image",{attrs:{src:i("15a3"),mode:""}}),t._v(t._s(t.invoiceInfo.status_text||""))],1),t.invoiceInfo.status?n("v-uni-view",{staticClass:"sm m-t-10"},[t._v("已开发票金额："+t._s(t.invoiceInfo.invoice_amount))]):n("v-uni-view",{staticClass:"sm m-t-10"},[t._v("正在开具发票中，请耐心等候…")])],1),n("v-uni-view",{staticClass:"main"},[n("v-uni-view",{staticClass:"card bg-white"},[n("v-uni-view",{staticClass:"lg bold p-l-30 p-b-18"},[t._v("发票信息")]),n("v-uni-view",{staticClass:"form-item"},[n("v-uni-view",{staticClass:"label"},[t._v("发票金额")]),n("v-uni-view",{staticClass:"content"},[t._v(t._s(t.invoiceInfo.invoice_amount||""))])],1),n("v-uni-view",{staticClass:"form-item"},[n("v-uni-view",{staticClass:"label"},[t._v("发票类型")]),n("v-uni-view",{staticClass:"content"},[t._v(t._s(0==t.invoiceInfo.type?"普通":"专用"))])],1),n("v-uni-view",{staticClass:"form-item"},[n("v-uni-view",{staticClass:"label"},[t._v("抬头类型")]),n("v-uni-view",{staticClass:"content"},[t._v(t._s(t.invoiceInfo.header_type_text))])],1),n("v-uni-view",{staticClass:"form-item"},[n("v-uni-view",{staticClass:"label"},[t._v("抬头名称")]),n("v-uni-view",{staticClass:"content"},[t._v(t._s(t.invoiceInfo.name||""))])],1),t.invoiceInfo.duty_number?n("v-uni-view",{staticClass:"form-item"},[n("v-uni-view",{staticClass:"label"},[t._v("税号")]),n("v-uni-view",{staticClass:"content"},[t._v(t._s(t.invoiceInfo.duty_number||""))])],1):t._e(),n("v-uni-view",{staticClass:"form-item"},[n("v-uni-view",{staticClass:"label"},[t._v("邮箱")]),n("v-uni-view",{staticClass:"content"},[t._v(t._s(t.invoiceInfo.email||""))])],1),t.invoiceInfo.address?n("v-uni-view",{staticClass:"form-item"},[n("v-uni-view",{staticClass:"label"},[t._v("企业地址")]),n("v-uni-view",{staticClass:"content"},[t._v(t._s(t.invoiceInfo.address||""))])],1):t._e(),t.invoiceInfo.mobile?n("v-uni-view",{staticClass:"form-item"},[n("v-uni-view",{staticClass:"label"},[t._v("企业电话")]),n("v-uni-view",{staticClass:"content"},[t._v(t._s(t.invoiceInfo.mobile||""))])],1):t._e(),t.invoiceInfo.bank?n("v-uni-view",{staticClass:"form-item"},[n("v-uni-view",{staticClass:"label"},[t._v("开户银行")]),n("v-uni-view",{staticClass:"content"},[t._v(t._s(t.invoiceInfo.bank||""))])],1):t._e(),t.invoiceInfo.bank_account?n("v-uni-view",{staticClass:"form-item"},[n("v-uni-view",{staticClass:"label"},[t._v("银行账号")]),n("v-uni-view",{staticClass:"content"},[t._v(t._s(t.invoiceInfo.bank_account||""))])],1):t._e(),n("v-uni-view",{staticClass:"form-item"},[n("v-uni-view",{staticClass:"label"},[t._v("申请时间")]),n("v-uni-view",{staticClass:"content"},[t._v(t._s(t.invoiceInfo.create_time||""))])],1)],1),n("v-uni-view",{staticClass:"card bg-white m-t-20"},[n("v-uni-view",{staticClass:"m-l-20"},[n("shop-title",{attrs:{shop:t.shopInfo,"is-link":!1}})],1),n("order-goods",{attrs:{list:t.goodsInfo}}),n("v-uni-view",{staticClass:"form-item"},[n("v-uni-view",{staticClass:"label"},[t._v("订单状态")]),n("v-uni-view",{staticClass:"content"},[t._v(t._s(t.order_status_text))])],1),n("v-uni-view",{staticClass:"form-item"},[n("v-uni-view",{staticClass:"label"},[t._v("订单编号")]),n("v-uni-view",{staticClass:"content"},[t._v(t._s(t.order_sn))])],1),n("v-uni-view",{staticClass:"form-item"},[n("v-uni-view",{staticClass:"label"},[t._v("下单时间")]),n("v-uni-view",{staticClass:"content"},[t._v(t._s(t.create_time))])],1)],1)],1),t.invoiceInfo.status?t._e():n("v-uni-view",{staticClass:"footer"},[n("v-uni-view",{staticClass:"btn br60"},[n("v-uni-button",{staticClass:"btn br60",attrs:{size:"lg"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toEditInvoice.apply(void 0,arguments)}}},[t._v("编辑发票")])],1)],1)],1)},s=[]},7161:function(t,e,i){"use strict";i("7a82");var n=i("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.apiInvoiceAdd=function(t){return a.default.post("order_invoice/add",t)},e.apiInvoiceDetail=function(t){return a.default.get("order_invoice/detail",{params:t})},e.apiInvoiceEdit=function(t){return a.default.post("order_invoice/edit",t)},e.apiOrderInvoiceDetail=function(t){return a.default.get("order/invoice",{params:t})},e.changeShopFollow=function(t){return a.default.post("shop_follow/changeStatus",t)},e.getInvoiceSetting=function(t){return a.default.get("order_invoice/setting",{params:t})},e.getNearbyShops=function(t){return a.default.get("shop/getNearbyShops",{params:t})},e.getShopCategory=function(){return a.default.get("shop_category/getList")},e.getShopGoodsCategory=function(t){return a.default.get("shop_goods_category/getShopGoodsCategory",{params:t})},e.getShopInfo=function(t){return a.default.get("shop/getShopInfo",{params:t})},e.getShopList=function(t){return a.default.get("shop/getShopList",{params:t})},e.getShopService=function(t){return a.default.get("setting/getShopCustomerService",{params:{shop_id:t}})},e.getTreaty=function(){return a.default.get("ShopApply/getTreaty")},e.shopApply=function(t){return a.default.post("ShopApply/apply",t)},e.shopApplyDetail=function(t){return a.default.get("ShopApply/detail",{params:{id:t}})},e.shopApplyRecord=function(t){return a.default.get("ShopApply/record",{params:t})};var a=n(i("2774"));i("a5ae")},7829:function(t,e,i){var n=i("24fb");e=n(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.shop-title[data-v-705e9a38]{height:%?80?%;flex:1;min-width:0}.shop-title .tag[data-v-705e9a38]{background:linear-gradient(267deg,#ff2c3c,#f52e99);border-radius:%?6?%;padding:%?5?% %?9?%}',""]),t.exports=e},"7fa1":function(t,e,i){"use strict";var n=i("4e02"),a=i.n(n);a.a},"8eb8":function(t,e,i){"use strict";i.r(e);var n=i("68d96"),a=i("2ea7");for(var s in a)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(s);i("7fa1");var o=i("f0c5"),c=Object(o["a"])(a["default"],n["b"],n["c"],!1,null,"1a14080b",null,!1,n["a"],void 0);e["default"]=c.exports},9194:function(t,e,i){"use strict";var n=i("5a0b"),a=i.n(n);a.a},b06b:function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("14d9");var n={name:"shop-title",options:{virtualHost:!0},props:{name:{type:String},shop:{type:Object},isLink:{type:Boolean,default:!0}},data:function(){return{}},methods:{toShop:function(){var t=this.isLink,e=this.shop;t&&this.$Router.push({path:"/pages/store_index/store_index",query:{id:e.shop_id||e.id}})}}};e.default=n},bde3:function(t,e,i){"use strict";i.r(e);var n=i("b06b"),a=i.n(n);for(var s in n)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(s);e["default"]=a.a},cdb6:function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return s})),i.d(e,"a",(function(){return n}));var n={uIcon:i("6976").default},a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"shop-title flex",on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.toShop.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"shop-name line-1 bold"},[t._v(t._s(t.shop.shop_name||t.shop.name||t.name))]),t.isLink?i("u-icon",{staticClass:"m-l-10 m-r-20",attrs:{name:"arrow-right",size:"28"}}):t._e()],1)},s=[]},f8ba:function(t,e,i){"use strict";i.r(e);var n=i("cdb6"),a=i("bde3");for(var s in a)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(s);i("9194");var o=i("f0c5"),c=Object(o["a"])(a["default"],n["b"],n["c"],!1,null,"705e9a38",null,!1,n["a"],void 0);e["default"]=c.exports}}]);