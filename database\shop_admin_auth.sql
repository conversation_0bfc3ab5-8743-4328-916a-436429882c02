CREATE TABLE `ls_shop_admin_auth` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `admin_id` int(11) NOT NULL COMMENT '管理员id',
  `shop_id` int(11) NOT NULL COMMENT '商家id',
  `openid` varchar(32) NOT NULL COMMENT '微信openid',
  `unionid` varchar(32) DEFAULT '' COMMENT '微信unionid',
  `create_time` int(10) DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) DEFAULT NULL COMMENT '更新时间',
  `client` tinyint(1) NOT NULL COMMENT '客户端类型：1-微信小程序；2-h5；3-ios；4-android',
  PRIMARY KEY (`id`),
  UNIQUE KEY `openid` (`openid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商家管理员微信授权表';
