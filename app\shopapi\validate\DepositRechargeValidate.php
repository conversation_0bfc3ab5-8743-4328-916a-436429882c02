<?php
namespace app\shopapi\validate;

use app\common\basics\Validate;

/**
 * 保证金充值验证类
 * Class DepositRechargeValidate
 * @package app\shopapi\validate
 */
class DepositRechargeValidate extends Validate
{
    protected $rule = [
        'amount'   => 'require|float|gt:0',
        'pay_way'  => 'require|in:1,2,3,4'
    ];


    protected $message = [
        'amount.require'   => '金额不能为空',
        'amount.float'     => '金额格式不正确',
        'amount.gt'        => '金额必须大于0',
        'pay_way.require'  => '请选择支付方式',
        'pay_way.in'       => '支付方式不正确'
    ];
} 