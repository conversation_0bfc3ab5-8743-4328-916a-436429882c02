<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>集采会员数据调试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1, h2 {
            color: #333;
            border-bottom: 2px solid #4CAF50;
            padding-bottom: 10px;
        }
        .test-button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .test-button:hover {
            background-color: #45a049;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            font-size: 12px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .debug-table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        .debug-table th, .debug-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .debug-table th {
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 集采会员数据调试工具</h1>
        <p>此工具用于调试 /admin/jcai.Member/lists.html 接口的数据类型问题</p>
    </div>

    <div class="container">
        <h2>📊 数据类型检查</h2>
        <button class="test-button" onclick="checkDataTypes()">检查数据类型</button>
        <div id="data-types-result" class="result" style="display:none;"></div>
    </div>

    <div class="container">
        <h2>🧪 接口测试</h2>
        
        <div class="form-group">
            <label for="test-datetime">日期范围测试:</label>
            <input type="text" id="test-datetime" placeholder="2024-01-01 - 2024-01-31">
        </div>
        
        <div class="form-group">
            <label for="test-order-sn">订单号测试:</label>
            <input type="text" id="test-order-sn" placeholder="输入订单号">
        </div>
        
        <div class="form-group">
            <label for="test-pay-status">支付状态测试:</label>
            <select id="test-pay-status">
                <option value="">全部</option>
                <option value="0">待支付</option>
                <option value="1">已支付</option>
            </select>
        </div>
        
        <button class="test-button" onclick="testListsAPI()">测试列表接口</button>
        <div id="lists-test-result" class="result" style="display:none;"></div>
    </div>

    <div class="container">
        <h2>🔧 错误排查</h2>
        <button class="test-button" onclick="testEmptyParams()">测试空参数</button>
        <button class="test-button" onclick="testInvalidDate()">测试无效日期</button>
        <button class="test-button" onclick="testSpecialChars()">测试特殊字符</button>
        <div id="error-test-result" class="result" style="display:none;"></div>
    </div>

    <script>
        // 显示结果的通用函数
        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.className = `result ${type}`;
            element.textContent = message;
            element.style.display = 'block';
        }

        // 发送API请求的通用函数
        async function sendRequest(url, data = {}, method = 'GET') {
            try {
                const options = {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json',
                    }
                };
                
                if (method === 'POST') {
                    options.body = JSON.stringify(data);
                }
                
                const response = await fetch(url, options);
                return await response.json();
            } catch (error) {
                return { error: error.message };
            }
        }

        // 检查数据类型
        async function checkDataTypes() {
            showResult('data-types-result', '正在检查数据类型...', 'info');

            const result = await sendRequest('/admin/jcai.Member/debug');

            if (result.code === 1) {
                let message = '数据类型检查结果：\n\n';
                const data = result.data;
                
                if (data.error) {
                    message += `错误：${data.error}`;
                    showResult('data-types-result', message, 'error');
                    return;
                }
                
                if (data.message) {
                    message += data.message;
                    showResult('data-types-result', message, 'info');
                    return;
                }

                // 创建表格显示数据类型
                let tableHtml = '<table class="debug-table"><tr><th>字段</th><th>值</th><th>类型</th><th>是否数值</th></tr>';
                
                Object.keys(data).forEach(key => {
                    const item = data[key];
                    tableHtml += `<tr>
                        <td>${key}</td>
                        <td>${item.value}</td>
                        <td>${item.type}</td>
                        <td>${item.is_numeric ? '是' : '否'}</td>
                    </tr>`;
                });
                
                tableHtml += '</table>';
                
                const element = document.getElementById('data-types-result');
                element.className = 'result success';
                element.innerHTML = '数据类型检查结果：<br>' + tableHtml;
                element.style.display = 'block';
            } else {
                showResult('data-types-result', `检查失败：${result.msg || '未知错误'}`, 'error');
            }
        }

        // 测试列表接口
        async function testListsAPI() {
            showResult('lists-test-result', '正在测试列表接口...', 'info');

            const params = new URLSearchParams();
            
            const datetime = document.getElementById('test-datetime').value;
            const orderSn = document.getElementById('test-order-sn').value;
            const payStatus = document.getElementById('test-pay-status').value;
            
            if (datetime) params.append('datetime', datetime);
            if (orderSn) params.append('order_sn', orderSn);
            if (payStatus !== '') params.append('pay_status', payStatus);
            
            params.append('page', '1');
            params.append('limit', '10');

            const url = `/admin/jcai.Member/lists?${params.toString()}`;
            
            try {
                const response = await fetch(url, {
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                });
                
                const result = await response.json();
                
                if (result.code === 1) {
                    const message = `接口测试成功：
总数：${result.data.count}
数据条数：${result.data.lists.length}
参数：${params.toString()}`;
                    showResult('lists-test-result', message, 'success');
                } else {
                    showResult('lists-test-result', `接口测试失败：${result.msg}`, 'error');
                }
            } catch (error) {
                showResult('lists-test-result', `请求异常：${error.message}`, 'error');
            }
        }

        // 测试空参数
        async function testEmptyParams() {
            showResult('error-test-result', '正在测试空参数...', 'info');
            
            try {
                const response = await fetch('/admin/jcai.Member/lists', {
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                });
                
                const result = await response.json();
                
                if (result.code === 1) {
                    showResult('error-test-result', '空参数测试成功', 'success');
                } else {
                    showResult('error-test-result', `空参数测试失败：${result.msg}`, 'error');
                }
            } catch (error) {
                showResult('error-test-result', `空参数测试异常：${error.message}`, 'error');
            }
        }

        // 测试无效日期
        async function testInvalidDate() {
            showResult('error-test-result', '正在测试无效日期...', 'info');
            
            const invalidDates = [
                'invalid-date',
                '2024-13-01 - 2024-13-31',
                '2024/01/01 - 2024/01/31',
                '2024-01-01',
                '2024-01-01 -',
                '- 2024-01-31'
            ];
            
            let results = [];
            
            for (const datetime of invalidDates) {
                try {
                    const params = new URLSearchParams();
                    params.append('datetime', datetime);
                    params.append('page', '1');
                    params.append('limit', '10');
                    
                    const response = await fetch(`/admin/jcai.Member/lists?${params.toString()}`, {
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest'
                        }
                    });
                    
                    const result = await response.json();
                    results.push(`${datetime}: ${result.code === 1 ? '成功' : result.msg}`);
                } catch (error) {
                    results.push(`${datetime}: 异常 - ${error.message}`);
                }
            }
            
            showResult('error-test-result', '无效日期测试结果：\n' + results.join('\n'), 'info');
        }

        // 测试特殊字符
        async function testSpecialChars() {
            showResult('error-test-result', '正在测试特殊字符...', 'info');
            
            const specialChars = [
                "'; DROP TABLE jcai_order; --",
                '<script>alert("xss")</script>',
                '中文测试',
                '!@#$%^&*()',
                '\\n\\r\\t'
            ];
            
            let results = [];
            
            for (const orderSn of specialChars) {
                try {
                    const params = new URLSearchParams();
                    params.append('order_sn', orderSn);
                    params.append('page', '1');
                    params.append('limit', '10');
                    
                    const response = await fetch(`/admin/jcai.Member/lists?${params.toString()}`, {
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest'
                        }
                    });
                    
                    const result = await response.json();
                    results.push(`${orderSn}: ${result.code === 1 ? '成功' : result.msg}`);
                } catch (error) {
                    results.push(`${orderSn}: 异常 - ${error.message}`);
                }
            }
            
            showResult('error-test-result', '特殊字符测试结果：\n' + results.join('\n'), 'info');
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('集采会员数据调试工具已加载');
        });
    </script>
</body>
</html>
