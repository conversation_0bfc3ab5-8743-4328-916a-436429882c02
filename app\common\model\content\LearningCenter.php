<?php

namespace app\common\model\content;

use app\common\basics\Models;

class LearningCenter extends Models
{
    /**
     * @Notes: 关联学习中心分类模型
     * @Author: 系统
     */
    public function category()
    {
        return $this->hasOne('LearningCenterCategory', 'id', 'category_id');
    }

    public function setContentAttr($value, $data)
    {
        $content = $data['content'];
        if (!empty($content)) {
            $content = HtmlSetImage($content);
        }
        return $content;
    }

    public function getContentAttr($value, $data)
    {
        $content = $data['content'];
        if (!empty($content)) {
            $content = HtmlGetImage($content);
        }
        return $content;
    }

    public function setImagesAttr($value)
    {
        if (is_array($value)) {
            return json_encode($value);
        }
        return $value;
    }

    public function getImagesAttr($value)
    {
        if ($value && is_string($value)) {
            return json_decode($value, true);
        }
        return $value ?: [];
    }
}