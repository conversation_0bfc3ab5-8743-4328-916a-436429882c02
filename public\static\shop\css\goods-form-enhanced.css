/* 商品表单增强样式 - 重构版本 */

/* 整体布局优化 */
.goods-form-container {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

/* Tab标签优化 */
.layui-tab-title {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    margin: 0;
    padding: 0 20px;
}

.layui-tab-title li {
    color: rgba(255, 255, 255, 0.7) !important;
    font-weight: 500;
    padding: 0 25px;
    margin: 0 5px;
    border-radius: 6px 6px 0 0;
    transition: all 0.3s ease;
    position: relative;
}

.layui-tab-title li:hover {
    color: rgba(255, 255, 255, 0.9) !important;
    background: rgba(255, 255, 255, 0.1);
}

.layui-tab-title li.layui-this {
    color: #333 !important;
    background: #fff;
    font-weight: 600;
}

.layui-tab-title li.layui-this::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #667eea, #764ba2);
}

/* 内容区域优化 */
.layui-tab-content {
    padding: 0;
    background: #fff;
}

.layui-tab-item {
    padding: 30px;
    min-height: 500px;
}

/* 表单项优化 */
.layui-form-item {
    margin-bottom: 25px;
    position: relative;
}

.layui-form-label {
    width: 120px;
    font-weight: 500;
    color: #333;
    padding: 9px 15px;
    background: #f8f9fa;
    border-radius: 6px 0 0 6px;
    border-right: 3px solid #e9ecef;
}

.layui-form-label .form-label-asterisk {
    color: #ff4757;
    margin-right: 3px;
}

.layui-input-block {
    margin-left: 120px;
    position: relative;
}

.layui-input, .layui-textarea, .layui-select {
    border: 2px solid #e9ecef;
    border-radius: 0 6px 6px 0;
    padding: 10px 15px;
    transition: all 0.3s ease;
    font-size: 14px;
}

.layui-input:focus, .layui-textarea:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* 提示文字优化 */
.form-tips {
    color: #6c757d;
    font-size: 12px;
    margin-top: 8px;
    padding-left: 15px;
    line-height: 1.4;
}

/* 图片上传区域优化 */
.image-upload-container {
    border: 2px dashed #e9ecef;
    border-radius: 8px;
    padding: 20px;
    background: #f8f9fa;
    transition: all 0.3s ease;
    position: relative;
}

.image-upload-container:hover {
    border-color: #667eea;
    background: #f0f4ff;
}

.like-upload-image {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    align-items: flex-start;
}

/* 图片项优化 */
.upload-image-div {
    position: relative;
    width: 120px;
    height: 120px;
    border-radius: 8px;
    overflow: hidden;
    cursor: move;
    transition: all 0.3s ease;
    background: #fff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.upload-image-div:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.upload-image-div img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* 图片序号和主图标识 */
.upload-image-div[data-order]::before {
    content: attr(data-order);
    position: absolute;
    top: 8px;
    left: 8px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
    z-index: 3;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.upload-image-div[data-order="1"]::after {
    content: "主图";
    position: absolute;
    top: 8px;
    right: 8px;
    background: #ff4757;
    color: white;
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: bold;
    z-index: 3;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* 图片控制按钮 */
.image-controls {
    position: absolute;
    top: 8px;
    right: 35px;
    display: flex;
    gap: 4px;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 4;
}

.upload-image-div:hover .image-controls {
    opacity: 1;
}

.image-controls button {
    width: 24px;
    height: 24px;
    border: none;
    border-radius: 4px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    font-weight: bold;
    transition: all 0.3s ease;
}

.image-controls button:hover {
    background: rgba(0, 0, 0, 0.9);
    transform: scale(1.1);
}

.btn-set-main {
    background: #ff4757 !important;
}

.btn-set-main:hover {
    background: #ff3838 !important;
}

/* 删除按钮 */
.del-upload-btn {
    position: absolute;
    top: -8px;
    right: -8px;
    width: 20px;
    height: 20px;
    background: #ff4757;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
    cursor: pointer;
    z-index: 5;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.del-upload-btn:hover {
    background: #ff3838;
    transform: scale(1.1);
}

/* 添加图片按钮 */
.upload-image-elem {
    width: 120px;
    height: 120px;
    border: 2px dashed #ccc;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #fff;
}

.upload-image-elem:hover {
    border-color: #667eea;
    background: #f0f4ff;
}

.add-upload-image {
    color: #667eea;
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
}

.add-upload-image::before {
    content: '+';
    font-size: 24px;
    font-weight: bold;
}

/* 拖拽状态样式 */
.sortable-ghost {
    opacity: 0.5;
    background: #f0f4ff;
    border: 2px dashed #667eea;
    transform: rotate(5deg);
}

.sortable-chosen {
    transform: rotate(2deg) scale(1.05);
    z-index: 999;
}

.sortable-drag {
    transform: rotate(2deg) scale(1.05);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
    z-index: 999;
}

/* 拖拽提示 */
.upload-image-div::before {
    content: "⋮⋮";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: rgba(255, 255, 255, 0.8);
    font-size: 18px;
    font-weight: bold;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
    z-index: 2;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
}

.upload-image-div:hover::before {
    opacity: 1;
}

/* 响应式优化 */
@media (max-width: 768px) {
    .layui-form-label {
        width: 100px;
        font-size: 13px;
    }
    
    .layui-input-block {
        margin-left: 100px;
    }
    
    .upload-image-div {
        width: 100px;
        height: 100px;
    }
    
    .upload-image-elem {
        width: 100px;
        height: 100px;
    }
}
