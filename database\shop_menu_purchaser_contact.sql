-- 添加商家后台采购商联系记录菜单

-- 首先查找"客户管理"或类似的父级菜单ID，如果没有则创建一个
-- 假设我们在"营销管理"下添加，或者创建一个新的"客户管理"菜单

-- 1. 添加"客户管理"一级菜单（如果不存在）
INSERT INTO `ls_dev_shop_auth` (`type`, `system`, `pid`, `name`, `icon`, `uri`, `sort`, `disable`, `del`, `merchant_types`) 
SELECT 1, 0, 0, '客户管理', 'layui-icon-user', '', 45, 0, 0, '0,1,2'
WHERE NOT EXISTS (
    SELECT 1 FROM `ls_dev_shop_auth` WHERE `name` = '客户管理' AND `pid` = 0 AND `del` = 0
);

-- 获取"客户管理"菜单的ID（用于后续插入子菜单）
SET @customer_menu_id = (SELECT `id` FROM `ls_dev_shop_auth` WHERE `name` = '客户管理' AND `pid` = 0 AND `del` = 0 LIMIT 1);

-- 2. 添加"联系记录"二级菜单
INSERT INTO `ls_dev_shop_auth` (`type`, `system`, `pid`, `name`, `icon`, `uri`, `sort`, `disable`, `del`, `merchant_types`) VALUES
(@customer_menu_id, 0, @customer_menu_id, '联系记录', 'layui-icon-dialogue', 'purchaser_contact/lists', 40, 0, 0, '0,1,2');

-- 获取"联系记录"菜单的ID
SET @contact_menu_id = (SELECT `id` FROM `ls_dev_shop_auth` WHERE `name` = '联系记录' AND `pid` = @customer_menu_id AND `del` = 0 LIMIT 1);

-- 3. 添加"联系记录"的子菜单权限
INSERT INTO `ls_dev_shop_auth` (`type`, `system`, `pid`, `name`, `icon`, `uri`, `sort`, `disable`, `del`, `merchant_types`) VALUES
(2, 0, @contact_menu_id, '查看记录', '', 'purchaser_contact/lists', 39, 0, 0, '0,1,2'),
(2, 0, @contact_menu_id, '记录详情', '', 'purchaser_contact/detail', 38, 0, 0, '0,1,2'),
(2, 0, @contact_menu_id, '联系统计', '', 'purchaser_contact/stats', 37, 0, 0, '0,1,2'),
(2, 0, @contact_menu_id, '导出记录', '', 'purchaser_contact/export', 36, 0, 0, '0,1,2');

-- 如果想要将联系记录菜单添加到现有的其他菜单下，可以使用以下SQL：
-- 例如添加到"营销管理"下：
/*
-- 查找营销管理菜单ID
SET @marketing_menu_id = (SELECT `id` FROM `ls_dev_shop_auth` WHERE `name` LIKE '%营销%' AND `pid` = 0 AND `del` = 0 LIMIT 1);

-- 如果找到营销管理菜单，则在其下添加联系记录
INSERT INTO `ls_dev_shop_auth` (`type`, `system`, `pid`, `name`, `icon`, `uri`, `sort`, `disable`, `del`, `merchant_types`) 
SELECT 1, 0, @marketing_menu_id, '联系记录', 'layui-icon-dialogue', 'purchaser_contact/lists', 40, 0, 0, '0,1,2'
WHERE @marketing_menu_id IS NOT NULL;
*/
