{layout name="layout1" /}
<style>
    .layui-table-cell {
        height:auto;
    }
    .goods-content>div:not(:last-of-type) {
        bsettlement-bottom:1px solid #DCDCDC;
    }
    .goods-data::after{
        display: block;
        content: '';
        clear: both;
    }
    .goods_name_hide{
        overflow:hidden;
        white-space:nowrap;
        text-overflow: ellipsis;
    }
    .operation-btn {
        margin: 5px;
    }
    .table-operate{
        text-align: left;
        font-size:14px;
        padding:0 5px;
        height:auto;
        overflow:visible;
        text-overflow:inherit;
        white-space:normal;
        word-break: break-all;
    }
</style>

<div class="wrapper">
    <div class="layui-card">
        <div class="layui-card-body">
            <div class="layui-collapse like-layui-collapse" style="bsettlement:1px dashed #c4c4c4">
                <div class="layui-colla-item">
                    <h2 class="layui-colla-title like-layui-colla-title" style="background-color: #fff">操作提示</h2>
                    <div class="layui-colla-content layui-show">
                        <p>*查看各级代理订单结算的情况。</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 结算汇总 -->
        <h2 style="margin: 20px;">结算汇总</h2>
        <div style="margin: 0 20px">
            <div class="layui-row layui-col-space15">
                <div class="layui-col-sm6 layui-col-md3">
                    <div class="layui-card" style="box-shadow:none;">
                        <div class="layui-card-header" style="border-bottom:0;">已结算商家入驻费总额</div>
                        <div class="layui-card-body"><p>￥{$statistics.settled_merchant_entry_fee}</p></div>
                    </div>
                </div>
                <div class="layui-col-sm6 layui-col-md3">
                    <div class="layui-card" style="box-shadow:none;">
                        <div class="layui-card-header" style="border-bottom:0;">已结算商家检验费总额</div>
                        <div class="layui-card-body"><p>￥{$statistics.settled_merchant_inspection_fee}</p></div>
                    </div>
                </div>
                <div class="layui-col-sm6 layui-col-md3">
                    <div class="layui-card" style="box-shadow:none;">
                        <div class="layui-card-header" style="border-bottom:0;">待结算商家入驻费总额</div>
                        <div class="layui-card-body"><p>￥{$statistics.pending_merchant_entry_fee}</p></div>
                    </div>
                </div>
                <div class="layui-col-sm6 layui-col-md3">
                    <div class="layui-card" style="box-shadow:none;">
                        <div class="layui-card-header" style="border-bottom:0;">待结算商家检验费总额</div>
                        <div class="layui-card-body"><p>￥{$statistics.pending_merchant_inspection_fee}</p></div>
                    </div>
                </div>

            </div>
            <div class="layui-row layui-col-space15">
                <div class="layui-col-sm6 layui-col-md3" >
                    <div class="layui-card" style="box-shadow:none;">
                        <div class="layui-card-header" style="border-bottom:0;">已结算会员购买佣金</div>
                        <div class="layui-card-body"><p>￥{$statistics.settled_member_commission}</p></div>
                    </div>
                </div>
                <div class="layui-col-sm6 layui-col-md3" >
                    <div class="layui-card" style="box-shadow:none;">
                        <div class="layui-card-header" style="border-bottom:0;">待结算会员购买佣金</div>
                        <div class="layui-card-body"><p>￥{$statistics.pending_member_commission}</p></div>
                    </div>
                </div>
                <div class="layui-col-sm6 layui-col-md3" >
                    <div class="layui-card" style="box-shadow:none;">
                        <div class="layui-card-header" style="border-bottom:0;">已结算商家组合购买佣金</div>
                        <div class="layui-card-body"><p>￥{$statistics.settled_combined_merchant_commission}</p></div>
                    </div>
                </div>
                <div class="layui-col-sm6 layui-col-md3" >
                    <div class="layui-card" style="box-shadow:none;">
                        <div class="layui-card-header" style="border-bottom:0;">待结算商家组合购买佣金</div>
                        <div class="layui-card-body"><p>￥{$statistics.pending_combined_merchant_commission}</p></div>
                    </div>
                </div>
            </div>




        </div>

        <!-- 结算记录 -->
        <h2 style="margin: 20px;">结算记录</h2>
        <div class="layui-card-body layui-form">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label for="name" class="layui-form-label">代理名称：</label>
                    <div class="layui-inline">
                        <div class="layui-input-inline" >
                            <input type="text" id="name" name="name" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">结算时间：</label>
                    <div class="layui-inline" style="margin-right:0;">
                        <div class="layui-input-inline" >
                            <input type="text" id="start_time" name="start_time" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">至</div>
                    <div class="layui-inline" style="margin-right:0;">
                        <div class="layui-input-inline" style="margin-right:0;">
                            <input type="text" id="end_time" name="end_time" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                </div>
                <div class="layui-inline">
                    <a class="layui-btn layui-btn-sm layui-btn-normal" lay-submit lay-filter="search">搜索</a>
                    <a class="layui-btn layui-btn-sm layui-btn-primary" lay-submit lay-filter="clear-search">重置</a>
                    <a class="layui-btn layui-btn-sm layui-btn-primary" lay-submit lay-filter="data-export">导出</a>
                </div>
            </div>
        </div>

        <!-- 主体内容 -->
        <div class="layui-card">
            <div class="layui-card-body">
                <table id="like-table-lists" lay-filter="like-table-lists"></table>
                <script type="text/html" id="table-storeInfo">
                    <img src="{{d.logo}}" alt="图标" style="width:20px;height:20px;margin-right:5px;">
                    <div class="layui-inline" style="text-align:left;">
                        <p>商家编号：{{d.shop_id}}</p>
                        <p>商家名称：{{d.name}}</p>
                        <p>商家类型：{{d.type}}</p>
                    </div>
                </script>
                <script type="text/html" id="table-operation">
                    <a class="layui-btn layui-btn-sm layui-btn-primary" lay-event="record">佣金明细</a>
                </script>
                <script type="text/html" id="user-info">
                    <img src="{{d.avatar}}" style="height:50px;width: 50px;margin-right: 10px;" class="image-show">
                    <div class="layui-input-inline" style="text-align:left;width: 140px">
                        <p>手机：{{d.mobile}}</p>
                        <p style="width: 180px;text-overflow:ellipsis;overflow: hidden">用户昵称：{{d.nickname}}</p>
                    </div>
                </script>
            </div>
        </div>

    </div>
</div>

<script>
    layui.use(["form"], function() {
        var $ = layui.$;
        var form = layui.form;
        var table = layui.table;
        var laydate = layui.laydate;

        laydate.render({type:"datetime", elem:"#start_time", trigger:"click"});
        laydate.render({type:"datetime", elem:"#end_time", trigger:"click"});

        like.tableLists("#like-table-lists", '{:url("agent.agent/settle")}', [
            {field:"id", width:60, title:"ID"}
            ,{title: '用户信息',width: 270,align: 'center', templet: '#user-info'}
            ,{title: '待返佣金',width: 170,align: 'center', field: "pending_commissions"}
            ,{title: '已结算佣金',width: 170,align: 'center', field: "settled_commissions"}
            ,{title: '已冻结佣金',width: 220,align: 'center', field: "invalid_commissions"}
            ,{title:"操作", width:140, align:"center", fixed:"right", toolbar:"#table-operation"}
        ]);

        var active = {
            record: function (obj) {
                layer.open({
                    type: 2
                    ,title: "结算记录"
                    ,content: "{:url('agent.agent/settlementRecord')}?agent_id="+obj.data.user_id
                    ,area: ["90%", "90%"]
                });
            }
        };
        like.eventClick(active);

        form.on("submit(search)", function(data){
            table.reload("like-table-lists", {
                where: data.field,
                page: {
                    curr: 1
                }
            });
        });

        form.on("submit(clear-search)", function(){
            $("#start_time").val("");
            $("#end_time").val("");
            $("#name").val("");
            table.reload("like-table-lists", {
                where: {},
                page: {
                    curr: 1
                }
            });
        });


        // 导出
        form.on('submit(data-export)', function (data) {
            var field = data.field;
            like.ajax({
                url: '{:url("finance.Shop/settlementExport")}'
                , data: field
                , type: 'get'
                , success: function (res) {
                    if (res.code == 1) {
                        window.location.href = res.data.url;
                    }
                }
            });
        });

    });
</script>
