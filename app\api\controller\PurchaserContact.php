<?php
namespace app\api\controller;

use app\api\logic\PurchaserContactLogic;
use app\common\basics\Api;
use app\common\server\JsonServer;

/**
 * 采购商联系记录相关接口
 * Class PurchaserContact
 * @package app\api\controller
 */
class PurchaserContact extends Api
{
    public $like_not_need_login = [];

    /**
     * 记录联系采购商
     * @return \think\response\Json
     */
    public function recordContact()
    {
        $post = $this->request->post();
        
        // 验证必要参数
        if (empty($post['purchaser_id'])) {
            return JsonServer::error('采购商ID不能为空');
        }
        
        // 获取用户信息
        if (empty($this->user_id)) {
            return JsonServer::error('请先登录');
        }
        
        // 获取客户端信息
        $clientInfo = [
            'ip' => $this->request->ip(),
            'user_agent' => $this->request->header('user-agent'),
            'source' => $post['source'] ?? 'web'
        ];
        
        $result = PurchaserContactLogic::recordContact($this->user_id, $post['purchaser_id'], $clientInfo);
        
        if ($result === false) {
            return JsonServer::error(PurchaserContactLogic::getError() ?: '记录失败');
        }
        
        return JsonServer::success('联系记录成功', $result);
    }

    /**
     * 获取用户的联系记录列表
     * @return \think\response\Json
     */
    public function getContactRecords()
    {
        $get = $this->request->get();
        
        if (empty($this->user_id)) {
            return JsonServer::error('请先登录');
        }
        
        $result = PurchaserContactLogic::getUserContactRecords($this->user_id, $this->page_no, $this->page_size, $get);
        
        if ($result === false) {
            return JsonServer::error(PurchaserContactLogic::getError() ?: '获取失败');
        }
        
        return JsonServer::success('获取成功', $result);
    }

    /**
     * 获取采购商的被联系记录（供商家后台使用）
     * @return \think\response\Json
     */
    public function getPurchaserContactRecords()
    {
        $get = $this->request->get();
        
        if (empty($get['purchaser_id'])) {
            return JsonServer::error('采购商ID不能为空');
        }
        
        $result = PurchaserContactLogic::getPurchaserContactRecords($get['purchaser_id'], $this->page_no, $this->page_size, $get);
        
        if ($result === false) {
            return JsonServer::error(PurchaserContactLogic::getError() ?: '获取失败');
        }
        
        return JsonServer::success('获取成功', $result);
    }

    /**
     * 获取联系统计信息
     * @return \think\response\Json
     */
    public function getContactStats()
    {
        $get = $this->request->get();
        
        // 可以根据用户ID或采购商ID获取统计
        $userId = $this->user_id;
        $purchaserId = $get['purchaser_id'] ?? null;
        
        $result = PurchaserContactLogic::getContactStats($userId, $purchaserId, $get);
        
        if ($result === false) {
            return JsonServer::error(PurchaserContactLogic::getError() ?: '获取失败');
        }
        
        return JsonServer::success('获取成功', $result);
    }
}
