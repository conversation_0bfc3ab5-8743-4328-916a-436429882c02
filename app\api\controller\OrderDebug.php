<?php

namespace app\api\controller;

use app\common\basics\Api;
use app\common\enum\PayEnum;
use app\common\model\order\Order;
use app\common\model\user\UserAuth;
use app\common\server\JsonServer;
use app\common\server\WechatMiniExpressSendSyncServer;

/**
 * 订单微信同步调试控制器
 * Class OrderDebug
 * @package app\api\controller
 */
class OrderDebug extends Api
{
    public $like_not_need_login = ['debugConfirm', 'debugDelivery', 'manualConfirmSync', 'testFullConfirmFlow', 'checkWechatConfig', 'testCreateWechatOrder'];
    /**
     * @notes 调试用户确认收货微信同步
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2024-01-01 00:00:00
     */
    public function debugConfirm()
    {
        try {
            $orderId = $this->request->post('order_id');

            if (empty($orderId)) {
                return JsonServer::error('订单ID不能为空');
            }

            // 获取订单信息
            $order = Order::where('id', $orderId)->find();

            if (!$order) {
                return JsonServer::error('订单不存在');
            }

            $orderData = $order->toArray();

            // 检查调试信息
            $debugInfo = [
                'order_id' => $orderId,
                'order_sn' => $orderData['order_sn'],
                'pay_way' => $orderData['pay_way'],
                'is_wechat_pay' => $orderData['pay_way'] == PayEnum::WECHAT_PAY,
                'user_id' => $orderData['user_id'],
                'order_status' => $orderData['order_status'],
                'shipping_status' => $orderData['shipping_status'],
                'confirm_take_time' => $orderData['confirm_take_time'],
                'transaction_id' => $orderData['transaction_id'] ?? '',
                'trade_id' => $orderData['trade_id'] ?? '',
            ];

            // 检查用户openid
            $user = UserAuth::where('user_id', $orderData['user_id'])
                ->where('client', 1) // 小程序
                ->findOrEmpty();

            $debugInfo['user_openid'] = $user->openid ?? '';
            $debugInfo['has_openid'] = !empty($user->openid);

            // 检查微信交易号
            $transaction_id = '';
            if (!empty($orderData['transaction_id'])) {
                $transaction_id = $orderData['transaction_id'];
                $debugInfo['transaction_source'] = 'order.transaction_id';
            } elseif (!empty($orderData['trade_id'])) {
                $transaction_id = \app\common\model\order\OrderTrade::where('id', $orderData['trade_id'])->value('transaction_id', '');
                $debugInfo['transaction_source'] = 'order_trade.transaction_id';
            }

            $debugInfo['final_transaction_id'] = $transaction_id;
            $debugInfo['has_transaction_id'] = !empty($transaction_id);

            // 如果是微信支付，尝试调用微信API
            if ($orderData['pay_way'] == PayEnum::WECHAT_PAY) {
                if (empty($user->openid)) {
                    $debugInfo['sync_result'] = '失败：用户openid不存在';
                } elseif (empty($transaction_id)) {
                    $debugInfo['sync_result'] = '失败：微信交易号不存在';
                } else {
                    try {
                        $result = WechatMiniExpressSendSyncServer::_sync_order_confirm($orderData);
                        $debugInfo['sync_result'] = $result ? '成功' : '失败';
                        $debugInfo['sync_detail'] = '微信API调用完成，请查看日志文件获取详细信息';
                    } catch (\Exception $e) {
                        $debugInfo['sync_result'] = '异常：' . $e->getMessage();
                        $debugInfo['sync_detail'] = $e->getTraceAsString();
                    }
                }
            } else {
                $debugInfo['sync_result'] = '跳过：非微信支付订单';
            }

            return JsonServer::success('调试完成', $debugInfo);

        } catch (\Exception $e) {
            return JsonServer::error('调试失败: ' . $e->getMessage());
        }
    }

    /**
     * @notes 调试发货微信同步
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2024-01-01 00:00:00
     */
    public function debugDelivery()
    {
        try {
            $orderId = $this->request->post('order_id');
            
            if (empty($orderId)) {
                return JsonServer::error('订单ID不能为空');
            }

            // 获取订单信息
            $order = Order::where('id', $orderId)->find();
            
            if (!$order) {
                return JsonServer::error('订单不存在');
            }

            $orderData = $order->toArray();
            
            // 检查调试信息
            $debugInfo = [
                'order_id' => $orderId,
                'pay_way' => $orderData['pay_way'],
                'is_wechat_pay' => $orderData['pay_way'] == PayEnum::WECHAT_PAY,
                'user_id' => $orderData['user_id'],
                'order_status' => $orderData['order_status'],
                'shipping_status' => $orderData['shipping_status'],
                'delivery_type' => $orderData['delivery_type'],
                'wechat_mini_express_sync' => $orderData['wechat_mini_express_sync'] ?? 0,
            ];

            // 检查用户openid
            $user = UserAuth::where('user_id', $orderData['user_id'])
                ->where('client', 1) // 小程序
                ->findOrEmpty();
            
            $debugInfo['user_openid'] = $user->openid ?? '';
            $debugInfo['has_openid'] = !empty($user->openid);

            // 如果是微信支付，尝试调用微信API
            if ($orderData['pay_way'] == PayEnum::WECHAT_PAY) {
                if (empty($user->openid)) {
                    $debugInfo['sync_result'] = '失败：用户openid不存在';
                } else {
                    try {
                        $result = WechatMiniExpressSendSyncServer::_sync_order($orderData);
                        $debugInfo['sync_result'] = $result ? '成功' : '失败';
                        $debugInfo['sync_detail'] = '微信API调用完成';
                    } catch (\Exception $e) {
                        $debugInfo['sync_result'] = '异常：' . $e->getMessage();
                        $debugInfo['sync_detail'] = $e->getTraceAsString();
                    }
                }
            } else {
                $debugInfo['sync_result'] = '跳过：非微信支付订单';
            }

            return JsonServer::success('调试完成', $debugInfo);

        } catch (\Exception $e) {
            return JsonServer::error('调试失败: ' . $e->getMessage());
        }
    }

    /**
     * @notes 检查微信配置
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2024-01-01 00:00:00
     */
    public function checkWechatConfig()
    {
        try {
            $configInfo = [
                'mnp_app_id' => config('project.mnp.app_id'),
                'mnp_secret' => config('project.mnp.secret') ? '已配置' : '未配置',
                'express_send_sync' => \app\common\server\ConfigServer::get('mnp', 'express_send_sync', 1),
            ];

            // 检查微信小程序实例
            try {
                $app = WechatMiniExpressSendSyncServer::getMiniApp();
                $configInfo['wechat_app_status'] = '正常';
            } catch (\Exception $e) {
                $configInfo['wechat_app_status'] = '异常：' . $e->getMessage();
            }

            // 检查access_token
            try {
                $app = WechatMiniExpressSendSyncServer::getMiniApp();
                $token = WechatMiniExpressSendSyncServer::getToken($app);
                $configInfo['access_token'] = $token ? '获取成功' : '获取失败';
            } catch (\Exception $e) {
                $configInfo['access_token'] = '异常：' . $e->getMessage();
            }

            return JsonServer::success('配置检查完成', $configInfo);

        } catch (\Exception $e) {
            return JsonServer::error('检查失败: ' . $e->getMessage());
        }
    }

    /**
     * @notes 手动触发确认收货同步
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2024-01-01 00:00:00
     */
    public function manualConfirmSync()
    {
        try {
            $orderId = $this->request->post('order_id');

            if (empty($orderId)) {
                return JsonServer::error('订单ID不能为空');
            }

            // 获取订单信息
            $order = Order::where('id', $orderId)->find();

            if (!$order) {
                return JsonServer::error('订单不存在');
            }

            $orderData = $order->toArray();

            // 检查是否为微信支付
            if ($orderData['pay_way'] != PayEnum::WECHAT_PAY) {
                return JsonServer::error('该订单不是微信支付，无需同步');
            }

            // 更新确认收货时间（如果还没有的话）
            if (empty($orderData['confirm_take_time'])) {
                Order::where('id', $orderId)->update(['confirm_take_time' => time()]);
                $orderData['confirm_take_time'] = time();
            }

            // 调用微信确认收货同步
            $result = WechatMiniExpressSendSyncServer::_sync_order_confirm($orderData);

            if ($result) {
                return JsonServer::success('微信确认收货信息同步成功', [
                    'order_id' => $orderId,
                    'confirm_take_time' => date('Y-m-d H:i:s', $orderData['confirm_take_time'])
                ]);
            } else {
                return JsonServer::error('微信确认收货信息同步失败');
            }

        } catch (\Exception $e) {
            return JsonServer::error('同步失败: ' . $e->getMessage());
        }
    }

    /**
     * @notes 完整测试用户确认收货流程
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2024-01-01 00:00:00
     */
    public function testFullConfirmFlow()
    {
        try {
            $orderId = $this->request->post('order_id');
            $userId = $this->request->post('user_id', 1); // 默认用户ID为1

            if (empty($orderId)) {
                return JsonServer::error('订单ID不能为空');
            }

            // 获取订单信息
            $order = Order::where('id', $orderId)->find();

            if (!$order) {
                return JsonServer::error('订单不存在');
            }

            $result = [];
            $result['step1_order_check'] = [
                'order_id' => $orderId,
                'order_status' => $order->order_status,
                'shipping_status' => $order->shipping_status,
                'pay_way' => $order->pay_way,
                'is_wechat_pay' => $order->pay_way == PayEnum::WECHAT_PAY,
            ];

            // 检查订单状态
            if ($order->order_status == \app\common\enum\OrderEnum::ORDER_STATUS_COMPLETE) {
                $result['step2_status_check'] = '订单已完成';
            } elseif ($order->shipping_status == 0) {
                $result['step2_status_check'] = '订单未发货';
            } else {
                $result['step2_status_check'] = '订单状态正常，可以确认收货';

                // 模拟确认收货流程
                $order->order_status = \app\common\enum\OrderEnum::ORDER_STATUS_COMPLETE;
                $order->update_time = time();
                $order->confirm_take_time = time();
                $order->save();

                $result['step3_update_order'] = '订单状态更新成功';

                // 同步微信
                if ($order->pay_way == PayEnum::WECHAT_PAY) {
                    try {
                        $orderData = Order::where('id', $orderId)->find()->toArray();
                        $syncResult = WechatMiniExpressSendSyncServer::_sync_order_confirm($orderData);
                        $result['step4_wechat_sync'] = $syncResult ? '微信同步成功' : '微信同步失败';
                    } catch (\Exception $e) {
                        $result['step4_wechat_sync'] = '微信同步异常：' . $e->getMessage();
                    }
                } else {
                    $result['step4_wechat_sync'] = '跳过：非微信支付订单';
                }
            }

            return JsonServer::success('完整流程测试完成', $result);

        } catch (\Exception $e) {
            return JsonServer::error('测试失败: ' . $e->getMessage());
        }
    }

    /**
     * @notes 测试微信商城订单创建
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2024-01-01 00:00:00
     */
    public function testCreateWechatOrder()
    {
        try {
            $orderId = $this->request->post('order_id');

            if (empty($orderId)) {
                return JsonServer::error('订单ID不能为空');
            }

            // 获取订单信息
            $order = Order::where('id', $orderId)->find();

            if (!$order) {
                return JsonServer::error('订单不存在');
            }

            $orderData = $order->toArray();

            // 检查是否为微信支付
            if ($orderData['pay_way'] != PayEnum::WECHAT_PAY) {
                return JsonServer::error('该订单不是微信支付，无需创建微信订单');
            }

            // 调用微信订单创建
            $result = WechatMiniExpressSendSyncServer::_create_wechat_order($orderData);

            if ($result) {
                return JsonServer::success('微信订单创建成功', [
                    'order_id' => $orderId,
                    'order_sn' => $orderData['order_sn']
                ]);
            } else {
                return JsonServer::error('微信订单创建失败');
            }

        } catch (\Exception $e) {
            return JsonServer::error('创建失败: ' . $e->getMessage());
        }
    }
}
