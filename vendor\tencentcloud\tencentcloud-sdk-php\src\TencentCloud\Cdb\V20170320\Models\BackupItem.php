<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Cdb\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * 创建备份时，指定需要备份的库表信息
 *
 * @method string getDb() 获取需要备份的库名
 * @method void setDb(string $Db) 设置需要备份的库名
 * @method string getTable() 获取需要备份的表名。 如果传该参数，表示备份该库中的指定表。如果不传该参数则备份该db库
 * @method void setTable(string $Table) 设置需要备份的表名。 如果传该参数，表示备份该库中的指定表。如果不传该参数则备份该db库
 */
class BackupItem extends AbstractModel
{
    /**
     * @var string 需要备份的库名
     */
    public $Db;

    /**
     * @var string 需要备份的表名。 如果传该参数，表示备份该库中的指定表。如果不传该参数则备份该db库
     */
    public $Table;

    /**
     * @param string $Db 需要备份的库名
     * @param string $Table 需要备份的表名。 如果传该参数，表示备份该库中的指定表。如果不传该参数则备份该db库
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("Db",$param) and $param["Db"] !== null) {
            $this->Db = $param["Db"];
        }

        if (array_key_exists("Table",$param) and $param["Table"] !== null) {
            $this->Table = $param["Table"];
        }
    }
}
