<?php


namespace app\api\logic;

use app\common\model\distribution\Distribution;
use app\common\model\distribution\DistributionLevel;
use app\common\model\goods\GoodsItem;
use app\common\model\ProductWord;
use app\common\model\shop\ShopFollow;
use app\common\model\user\User;
use app\common\basics\Logic;
use app\common\enum\FootprintEnum;
use app\common\model\distribution\DistributionGoods;
use app\common\model\goods\Goods;
use app\common\model\order\OrderGoods;
use app\common\model\goods\GoodsCollect;
use app\common\model\goods\GoodsClick;
use app\common\model\goods\GoodsSpec;
use app\common\model\goods\GoodsComment;
use app\common\model\goods\GoodsCommentImage;
use app\common\model\SearchRecord;
use app\common\enum\GoodsEnum;
use app\common\model\seckill\SeckillGoods;
use app\common\model\shop\Shop;
use app\common\model\team\TeamActivity;
use app\common\model\team\TeamFound;
use app\common\model\team\TeamGoods;
use app\common\model\user\UserLevel;
use app\common\model\Word;
use app\shopapi\logic\ShopLogic as ShopApiLogic;
use app\common\server\AreaServer;
use app\common\server\ConfigServer;
use app\common\server\UrlServer;
use app\common\server\WordUpdateServer;
use think\facade\Db;
use think\db\Raw;

use think\facade\Validate;

class GoodsLogic extends Logic
{

    /**
     * 商品详情
     */
    public static function getGoodsDetail($goodsId, $userId)
    {
        //获取用户折扣
        $discount = 10;
        if($userId){
            $user = User::where('id', $userId)->find();
            if($user && isset($user['level'])){
                $user_discount = UserLevel::where('id', $user['level'])->value('discount');
                if($user_discount && $user_discount > 0 && $user_discount <= 10){
                    $discount = $user_discount;
                }
            }
        }


        // 销售中商品：未删除/审核通过/已上架
        $onSaleWhere = [
            'del' => GoodsEnum::DEL_NORMAL, // 未删除
            'status' => GoodsEnum::STATUS_SHELVES, // 上架中
            'audit_status' => GoodsEnum::AUDIT_STATUS_OK, // 审核通过
        ];

        $goodsDetail = Goods::with(['goods_image', 'goods_item', 'shop'])
            ->field('id,hgou_lv,express_money,express_type,express_template_id,join_jc,type,name,image,video,remark,content,market_price,min_price,max_price,is_show_stock,stock,sales_actual,sales_virtual,clicks,clicks_virtual,shop_id,poster,delivery_type,is_list')
            ->where($onSaleWhere)
            ->where('id', $goodsId)
            ->findOrEmpty();

        if ($goodsDetail->isEmpty()) {
            self::$error = '商品已下架';
            return false;
        }

        //处理默认配送方式
        if ($goodsDetail['type'] == GoodsEnum::TYPE_VIRTUAL) {
            $goodsDetail['default_delivery_type'] = GoodsEnum::DELIVERY_VIRTUAL;
        } else {
            // 快递和自提
            $goodsDetail['default_delivery_type'] = (int)explode(',',$goodsDetail['delivery_type'])[0];
        }

        //判断是否是拼单集采商品,如果是拼单集采商品，获取拼单集采商品信息
        if($goodsDetail['join_jc'] == 1){
            $goodsDetail['min_price'] = GoodsItem::where('goods_id',$goodsId)->min('pdjc_price');
            $goodsDetail['max_price'] = GoodsItem::where('goods_id',$goodsId)->max('pdjc_price');
        }


        //如果`express_type` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '运费类型：1-包邮；2-统一运费；3-运费模板',
        //如果express_type =3 为运费模板 则显示默认全国的运费
        if($goodsDetail['express_type'] == GoodsEnum::EXPRESS_TYPE_TEMPLATE){
            $goodsDetail['express_money'] = Db::name('freight_config')->where('freight_id', $goodsDetail['express_template_id'])->where('region','all')->value('first_money');
            unset($goodsDetail['express_template_id']);
        }

        Db::startTrans();
        try{
            // 轮播图加域名
            foreach($goodsDetail['goods_image'] as &$item) {
                $item['uri'] = empty($item['uri']) ? '' : UrlServer::getFileUrl($item['uri']);
            }
            // 会员价
            $goodsDetail['member_price'] = 0;
            // 会员价数组
            $member_price = [];
            foreach ($goodsDetail['goods_item'] as &$goods_item) {
                $is_member = Goods::where('id',$goods_item['goods_id'])->value('is_member');
                $goods_item['is_member'] = $is_member;
                if($is_member == 1 && $discount && $userId){
                    $goods_item['member_price'] = round($goods_item['price']* $discount/10,2);
                    $goodsDetail['member_price'] =  round($goods_item['price']* $discount/10,2);
                    $member_price[] = $goodsDetail['member_price'];
                }
                // 规格图片处理
                $goods_item['image'] = empty($goods_item['image']) ? $goodsDetail['image'] : $goods_item['image'];

                if($goodsDetail['join_jc'] == 1){
                    $goods_item['price'] = $goods_item['pdjc_price'];

                }
            }

            !empty($member_price) && $goodsDetail['member_price'] = min($member_price);
            //计算商品回购率
            $hgou_lv=self::calculateRepurchaseRate($goodsId);
            //更新商品回购率
            // 增加点击量
            $goodsDetail->clicks += 1;
            $goodsDetail->hgou_lv = $hgou_lv;
            $goodsDetail->save();

            // 转数组
            $goodsDetailArr = $goodsDetail->toArray();

            $goodsDetailArr['poster'] = !empty($goodsDetailArr['poster']) ? UrlServer::getFileUrl($goodsDetailArr['poster']) : '';

            // 新增点击记录
            GoodsClick::create([
                'shop_id' => $goodsDetailArr['shop_id'],
                'user_id' => $userId,
                'goods_id' => $goodsId,
                'create_time' => time()
            ]);

            //店铺信息
            switch ($goodsDetailArr['shop']['type']){
                case 1 :
                    $type_desc = '官方自营';
                    break;
                case 2 :
                    $type_desc = '入驻商家';
                    break;
                default :
                    $type_desc = '入驻商家';
                    break;
            }
            $follow = Db::name('shop_follow')->where(['shop_id' => $goodsDetailArr['shop_id'],'status' => 1])->count('id');
            $goodsDetailArr['shop']['type_desc'] = $type_desc; //商家类型
            $goodsDetailArr['shop']['follow_num'] = $follow; //收藏人数

            //客服二维码
            $customer_image = ConfigServer::get('shop_customer_service','image','',$goodsDetailArr['shop_id']);
            if($customer_image){
                $customer_image = UrlServer::getFileUrl($customer_image);
            }
            $goodsDetailArr['shop']['customer_image'] = $customer_image;
            // 用户是否关注店铺
            $goodsDetailArr['shop']['shop_follow_status'] = 0;
            if($userId) { // 用户已登录
                $shopFollow = ShopFollow::where(['user_id'=>$userId, 'shop_id'=>$goodsDetailArr['shop_id']])->findOrEmpty();
                if(!$shopFollow->isEmpty()) {
                    $goodsDetailArr['shop']['shop_follow_status'] = $shopFollow['status'];
                }

                $shopfootprint = Db::name('goods_footprint')->where(['user_id'=>$userId, 'goods_id'=>$goodsId])->value('id');

                if(!empty($shopfootprint)) {
                    Db::name('goods_footprint')->where(['id'=>$shopfootprint])->inc('nums')->update(['update_time' => time()]);
                }else{
                    Db::name('goods_footprint')->insert(['user_id'=>$userId, 'goods_id'=>$goodsId, 'create_time'=>time()]);
                }
            }

            // 店铺在售商品数量
            $goodsDetailArr['shop']['goods_on_sale'] = Goods::where($onSaleWhere)
                ->where('shop_id', $goodsDetailArr['shop_id'])
                ->count();

            // 店铺推荐商品列表(9个)
            $goodsDetailArr['shop']['goods_list'] = Goods::field('id,name,image,market_price,min_price')
                ->where($onSaleWhere)
                ->where([
                    'shop_id' => $goodsDetailArr['shop_id'],
//                    'is_recommend' => 1, // 推荐
                ])
                ->order([
                    'sales_actual' => 'desc',
                    'id' => 'desc'
                ])
                ->limit(9)
                ->select()
                ->toArray();

            // 总销量 = 实际销量 + 虚拟销量
            $goodsDetailArr['sales_sum'] = $goodsDetailArr['sales_actual'] + $goodsDetailArr['sales_virtual'];
            // 标识活动信息
            $goodsDetailArr['activity'] = [
                'type' => 0,
                'type_desc' => '普通商品'
            ];
            // 检查商品是否在参与活动，替换商品价格
            $goodsDetailArr = self::checkActivity($goodsDetailArr);
            // 是否收藏
            $goodsDetailArr['is_collect'] = 0;
            if($userId) { // 非游客
                $goodsCollect = GoodsCollect::where([
                    'user_id' => $userId,
                    'goods_id' => $goodsId
                ])->findOrEmpty();
                if(!$goodsCollect->isEmpty()) {
                    $goodsDetailArr['is_collect'] = $goodsCollect->status ? 1 : 0;
                }
            }
            // 规格项及规格值信息
            $goodsDetailArr['goods_spec'] = GoodsSpec::with('spec_value')
                ->where('goods_id', $goodsId)->select();


            //获取规格图片和库存信息
            foreach($goodsDetailArr['goods_spec'] as $key => &$value){
                foreach($value['spec_value'] as $subKey => &$subValue){
                    // 使用更安全的查询方式，避免直接使用字符串拼接造成SQL注入
                    $goodsItemInfo = Db::name('goods_item')
                        ->whereRaw("FIND_IN_SET(?, spec_value_ids)", [$subValue['id']])
                        ->field('image, stock')
                        ->find();

                    // 设置规格值图片
                    $subValue['image'] = empty($goodsItemInfo['image']) ? $goodsDetail['image'] : UrlServer::getFileUrl($goodsItemInfo['image']);

                    // 设置规格值库存信息
                    $subValue['stock'] = $goodsItemInfo['stock'] ?? 0;
                    $subValue['is_stock_out'] = ($subValue['stock'] <= 0) ? 1 : 0; // 1表示缺货，0表示有库存
                }
            }

            // 新增：按第一个规格值分组的库存信息
            $goodsDetailArr['spec_stock_groups'] = [];

            // 获取第一个规格（按ID排序最小的规格）
            $firstSpec = Db::name('goods_spec')
                ->where('goods_id', $goodsId)
                ->order('id', 'asc')
                ->find();

            if ($firstSpec) {
                // 获取第一个规格的所有规格值
                $firstSpecValues = Db::name('goods_spec_value')
                    ->where('spec_id', $firstSpec['id'])
                    ->where('goods_id', $goodsId)
                    ->order('id', 'asc')
                    ->select()
                    ->toArray();

                // 获取所有商品SKU项
                $allGoodsItems = Db::name('goods_item')
                    ->where('goods_id', $goodsId)
                    ->field('id,image,spec_value_ids,spec_value_str,price,stock,market_price,chengben_price,pdjc_price')
                    ->select()
                    ->toArray();

                // 按第一个规格值分组 - 显示所有规格值，不管是否有对应的SKU
                foreach ($firstSpecValues as $specValue) {
                    $groupData = [
                        'first_spec_name' => $firstSpec['name'],
                        'first_spec_value' => $specValue['value'],
                        'first_spec_value_id' => $specValue['id'],
                        'first_spec_value_image' => empty($specValue['image']) ? '' : UrlServer::getFileUrl($specValue['image']),
                        'combinations' => []
                    ];

                    // 查找包含该规格值的所有SKU组合
                    foreach ($allGoodsItems as $item) {
                        $specValueIds = explode(',', $item['spec_value_ids']);

                        // 检查是否包含当前规格值
                        if (in_array($specValue['id'], $specValueIds)) {
                            $combination = [
                                'item_id' => $item['id'],
                                'spec_value_ids' => $item['spec_value_ids'],
                                'spec_value_str' => $item['spec_value_str'],
                                'stock' => $item['stock'],
                                'price' => $item['price'],
                                'market_price' => $item['market_price'],
                                'image' => empty($item['image']) ? $goodsDetail['image'] : UrlServer::getFileUrl($item['image']),
                                'is_stock_out' => ($item['stock'] <= 0) ? 1 : 0
                            ];

                            // 如果是拼单集采商品，使用拼单集采价格
                            if ($goodsDetail['join_jc'] == 1) {
                                $combination['price'] = $item['pdjc_price'];
                                $combination['pdjc_price'] = $item['pdjc_price'];
                            }

                            // 添加成本价（如果存在）
                            if (!empty($item['chengben_price'])) {
                                $combination['chengben_price'] = $item['chengben_price'];
                            }

                            $groupData['combinations'][] = $combination;
                        }
                    }

                    // 显示所有规格值，即使没有对应的SKU组合
                    $goodsDetailArr['spec_stock_groups'][] = $groupData;
                }
            }

            // 商品评价
            // 首先检查当前商品是否有评价
            $goods_comment_count = Db::name('goods_comment')
                ->where(['goods_id' => $goodsId, 'del' => 0, 'status' => 1])
                ->count('id');

            $comment_source = 'goods'; // 评价来源标识：goods-商品评价，shop-店铺评价
            $query_params = ['goods_id' => $goodsId];

            // 如果当前商品没有评价，则查询店铺所有商品的评价
            if($goods_comment_count == 0) {
                $shop_comment_count = Db::name('goods_comment')
                    ->where(['shop_id' => $goodsDetailArr['shop_id'], 'del' => 0, 'status' => 1])
                    ->count('id');

                if($shop_comment_count > 0) {
                    $comment_source = 'shop';
                    $query_params = ['shop_id' => $goodsDetailArr['shop_id']];
                    $goods_comment_count = $shop_comment_count;
                }
            }

            // 获取评价分类统计
            $commentCategory = GoodsCommentLogic::category($query_params);
            $goodsDetailArr['comment']['percent'] = $commentCategory['percent'];
            $goodsDetailArr['comment']['source'] = $comment_source; // 标识评价来源

            // 计算平均评分
            if($goods_comment_count > 0){
                if($comment_source == 'goods') {
                    $all_comment = Db::name('goods_comment')->where(['goods_id' => $goodsId, 'del' => 0, 'status' => 1])->sum('goods_comment');
                } else {
                    $all_comment = Db::name('goods_comment')->where(['shop_id' => $goodsDetailArr['shop_id'], 'del' => 0, 'status' => 1])->sum('goods_comment');
                }
                $goods_comment = round($all_comment / $goods_comment_count, 2);
                $goodsDetailArr['comment']['goods_comment'] = $goods_comment;
            } else {
                $goodsDetailArr['comment']['goods_comment'] = 0;
            }

            // 最新一条评论
            $one = [];
            if($goods_comment_count > 0) {
                $oneQuery = GoodsComment::alias('gc')
                    ->field('gc.id,gc.goods_comment,gc.create_time,gc.comment,u.avatar,u.nickname,g.name as goods_name')
                    ->leftJoin('user u', 'u.id=gc.user_id')
                    ->leftJoin('goods g', 'g.id=gc.goods_id')
                    ->where([
                        ['gc.del', '=', 0],
                        ['gc.status', '=', 1],
                    ]);

                if($comment_source == 'goods') {
                    $oneQuery->where('gc.goods_id', '=', $goodsId);
                } else {
                    $oneQuery->where('gc.shop_id', '=', $goodsDetailArr['shop_id']);
                }

                $one = $oneQuery->order('create_time', 'desc')->findOrEmpty();

                if(!$one->isEmpty()) {
                    $one = $one->toArray();
                    // 头像
                    $one['avatar'] = UrlServer::getFileUrl($one['avatar']);
                    // 图片评价
                    $one['image'] = GoodsCommentImage::where('goods_comment_id', $one['id'])->column('uri');
                    foreach($one['image'] as $subKey => $subItem) {
                        $one['image'][$subKey] = UrlServer::getFileUrl($subItem);
                    }
                } else {
                    $one = [];
                }
            }
            $goodsDetailArr['comment']['one'] = $one;
            
            // 判断是否是拼团商品
            $teamActivity = (new TeamActivity())
                ->field(['id,people_num,team_max_price,team_min_price,sales_volume,activity_end_time,share_title,share_intro'])
                ->where([
                    ['goods_id', '=', $goodsId],
                    ['audit', '=', 1],
                    ['status', '=', 1],
                    ['del', '=', 0],
                    ['activity_start_time', '<=', time()],
                    ['activity_end_time', '>=', time()]
            ])->findOrEmpty()->toArray();

            if ($teamActivity) {
                $teamFound = (new TeamFound())->alias('TF')
                    ->field(['TF.*', 'U.nickname,U.avatar'])
                    ->limit(8)
                    ->order('id desc')
                    ->where('TF.team_activity_id', '=', $teamActivity['id'])
                    ->where('TF.people','exp',' > TF.join ')
                    ->where([
                        ['status', '=', 0],
                        ['invalid_time', '>=', time()]
                    ])->join('user U', 'U.id=TF.user_id')
                      ->select()->toArray();

                foreach ($teamFound as &$found) {
                    unset($found['shop_id']);
                    unset($found['team_sn']);
                    unset($found['goods_snap']);
                    unset($found['team_end_time']);
                    $found['avatar'] = UrlServer::getFileUrl($found['avatar']);
                    $found['surplus_time'] = intval($found['invalid_time'] - time());
                }

                $teamActivity['share_title'] = !empty($teamActivity['share_title']) ? $teamActivity['share_title'] : $goodsDetailArr['name'];
                $teamActivity['share_intro'] = !empty($teamActivity['share_intro']) ? $teamActivity['share_intro'] : $goodsDetailArr['remark'];

                $goodsDetailArr['activity'] = ['type'=>2, 'type_desc'=>'拼团商品', 'info'=>$teamActivity, 'found'=>$teamFound];
                $teamGoods = (new TeamGoods())->where(['team_id'=>$teamActivity['id']])->select()->toArray();
                foreach ($goodsDetailArr['goods_item'] as &$item) {
                    foreach ($teamGoods as $team) {
                        if ($item['id'] === $team['item_id']) {
                            $item['team_price'] = $team['team_price'];
                        }
                    }
                }
            }

            // 预估佣金(计算出最高可得佣金)
            $goodsDetailArr['distribution'] = self::getDistribution($goodsId, $userId);

            // 虚拟浏览量
            $goodsDetailArr['clicks'] += $goodsDetailArr['clicks_virtual'];
            //将content中字符串中是style样式都去除掉
            $goodsDetailArr['content'] = preg_replace('/style=".*?"/', '', $goodsDetailArr['content']);
            // 记录访问足迹
            event('Footprint', [
                'type'    => FootprintEnum::BROWSE_GOODS,
                'user_id' => $userId,
                'foreign_id' => $goodsId
            ]);
            $shop_address=Db::name('shop')->field('province_id,city_id,district_id,address,total_sales')->where('id', $goodsDetailArr['shop_id'])->find();
            $shop_province = $shop_address['province_id'] ?? ''; //省份
              $goodsDetailArr['total_sales'] =$shop_address['total_sales'];
            $shop_city = $shop_address['city_id'] ?? ''; //城市
            $shop_district = $shop_address['district_id'] ?? ''; //县区
            $shop_address = $shop_address['address'] ?? ''; //县区
            $shop_address['address'] ?? ''; //详细地址

            $goodsDetailArr['shop_province'] = AreaServer::getAddress($shop_province);
            $goodsDetailArr['shop_city'] = AreaServer::getAddress($shop_city);
            $goodsDetailArr['shop_district'] = AreaServer::getAddress($shop_district);
            $goodsDetailArr['shop_address'] =$shop_address;//1
            Db::commit();
            return $goodsDetailArr;
        }catch(\Exception $e) {
            Db::rollback();
            self::$error = $e->getMessage();
            return false;
        }
    }

    /**
     * 热销榜单
     */
    public static  function getHotList($get)
    {
        // 销售中商品：未删除/审核通过/已上架
        $where = [
            ['del', '=', GoodsEnum::DEL_NORMAL],  // 未删除
            ['status', '=', GoodsEnum::STATUS_SHELVES], // 上架中
            ['audit_status', '=', GoodsEnum::AUDIT_STATUS_OK], // 审核通过
            ['join_jc', '=', 0], // 没有加入拼单集采通过
        ];
        $order = [
            'sales_total' => 'desc', // 实际销量+虚拟销量倒序
            'sales_actual' => 'desc', // 实际销量倒序
            'id' => 'desc'
        ];

        return self::getGoodsListTemplate($where, $order, $get);
    }

    /**
     * 商品列表
     */
    public static function getGoodsList($get)
    {
        // 销售中商品：未删除/审核通过/已上架
        $where = [
            ['del', '=', GoodsEnum::DEL_NORMAL],  // 未删除
            ['status', '=', GoodsEnum::STATUS_SHELVES], // 上架中
            ['audit_status', '=', GoodsEnum::AUDIT_STATUS_OK], // 审核通过
            ['join_jc', '=', 0], // 没有加入拼单集采通过
        ];

        $order=[];
        //如果用户没登录
        if($get['user_id'] && !empty($get['keyword'])) { // 记录关键词
            $get['filed_asc']=self::getRecommendGoods($get);
        }else{
           if(!empty($get['sort_by_price']) || !empty($get['sort_by_sales']) || !empty($get['sort_by_create'])){
           
            }else{
                $order = [
                    'sort_weight' => 'asc', // 商品权重，数字越小权重越大
                    'sort' => 'asc',
                    'id' => 'desc'
                ];
                $get['is_rand']=1;
                }
        
        }

        #获取当前商品ID的属性
        return self::getGoodsListTemplate($where, $order, $get);
    }


    /**
     * 商品列表模板
     * 作用：代码复用
     */
    public static function getGoodsListTemplate($where, $order, $get)
    {
        // 查询所有店铺
        $shopApiLogic = new ShopApiLogic();
        $list = Shop::where('del', 0)->select();
        $filteredList = array_filter($list->toArray(), function($shop) use ($shopApiLogic) {
            $completionResult = $shopApiLogic->checkProfileCompletion($shop['id']);
            return empty($completionResult);
        });

        $where[] = ['shop_id', 'in', array_column($filteredList, 'id')];
        $get['where_raw']=isset($get['where_raw']) ? $get['where_raw'] : '';
        if (!empty(self::filterShopsIds())) {
            // 过滤已删除、已冻结、已暂停营业、已到期的店铺
            $where[] = ['shop_id', 'not in', self::filterShopsIds()];
        }
        //增加一个随机数
        $whereOr = isset($get['whereOr']) ? $get['whereOr'] : [];
        $goods_tui_id=$get['goods_tui_id'] ?? '';
        if ( $goods_tui_id != '' ){
            #获取当前商品ID的属性
            $second_cate_id=Db::name('goods')->where('id', $goods_tui_id)->value('second_cate_id');
            if(!empty($second_cate_id)){
                $where[] = ['second_cate_id', '=',$second_cate_id];
            }

            unset($get['shop_id']);
            unset($get['is_recommend']);
        }
        // 平台分类
        if(isset($get['platform_cate_id']) && !empty($get['platform_cate_id']) && filter_var($get['platform_cate_id'], FILTER_VALIDATE_INT)) {
            $where[] = ['first_cate_id|second_cate_id|third_cate_id', '=', $get['platform_cate_id']];
        }

        //二级类目
        if(isset($get['second_cate_id']) && !empty($get['second_cate_id'])) {
            $tid=1;
            $where[] = ['second_cate_id', '=', $get['second_cate_id']];
        }
        // 品牌
        if(isset($get['brand_id']) && !empty($get['brand_id']) && filter_var($get['brand_id'], FILTER_VALIDATE_INT)) {
            $where[] = ['brand_id', '=', $get['brand_id']];
        }

        // 关键词处理
        if (isset($get['keyword']) && !empty($get['keyword'])) {
            // 清理和预处理关键词
            $keyword = trim(urldecode($get['keyword']));

            // 安全过滤：移除HTML标签和特殊字符
            if (preg_match('/<[^>]+>/', $keyword)) {
                $keyword = strip_tags($keyword);
            }

            // 记录原始关键词，用于后续高亮处理
            $get['original_keyword'] = $keyword;

            // 检查是否包含空格（多个关键词）
            $keywordParts = preg_split('/\s+/', $keyword);
            $isMultiKeyword = count($keywordParts) > 1;

            // 使用简单分词处理关键词，避免依赖阿里云服务
            $segmentedWords = [$keyword];

            // 尝试使用阿里云分词（如果可用）
            try {
                $words = Alisegment($keyword);
                if (!empty($words['words']) && is_array($words['words'])) {
                    $segmentedWords = $words['words'];
                }
            } catch (\Exception $e) {
                // 如果分词失败，记录错误但继续使用原始关键词
                \think\facade\Log::error('阿里云分词失败，使用原始关键词: ' . $e->getMessage());
            }

            // 合并空格分割的关键词和分词结果
            $allKeywordParts = array_unique(array_merge($keywordParts, $segmentedWords));

            // 记录关键词部分，用于后续高亮处理
            $get['keyword_parts'] = $keywordParts;
            $get['segmented_words'] = $segmentedWords;
            $get['all_keyword_parts'] = $allKeywordParts;

            // 关键词长度统计（中文字符安全计数）
            $wordCount = mb_strlen($keyword, 'UTF-8');

            // 记录用户搜索关键词（条件：有用户ID且关键词长度>1）
            if (!empty($get['user_id']) && $wordCount > 1) {
                self::recordKeyword($keyword, $get['user_id']);
            }

            // 优先使用传统数据库搜索，避免 MeiliSearch 连接问题
            \think\facade\Log::info('开始商品搜索，关键词: ' . $keyword);

            // 直接使用传统搜索方法
            self::fallbackToTraditionalSearch($keyword, $keywordParts, $isMultiKeyword, $get, $where, true);

            // 可选：尝试使用MeiliSearch进行搜索（如果需要）
            $useMeiliSearch = false; // 暂时禁用 MeiliSearch
            if ($useMeiliSearch) {
                try {
                    // 初始化MeiliSearch客户端
                    $meili = new \app\common\library\MeiliSearch();

                    // 构建搜索过滤条件
                    $filter = [];

                    // 添加已有的where条件到filter
                    foreach ($where as $condition) {
                        if (is_array($condition) && count($condition) === 3) {
                            $field = $condition[0];
                            $operator = $condition[1];
                            $value = $condition[2];

                            // 处理不同的操作符
                            if ($operator === '=') {
                                $filter[] = "{$field} = {$value}";
                            } elseif ($operator === 'in') {
                                if (is_array($value)) {
                                    $valueStr = implode(',', $value);
                                    $filter[] = "{$field} IN [{$valueStr}]";
                                }
                            } elseif ($operator === 'not in') {
                                if (is_array($value)) {
                                    $valueStr = implode(',', $value);
                                    $filter[] = "{$field} NOT IN [{$valueStr}]";
                                }
                            }
                        }
                    }

                    // 构建搜索选项
                    $searchOptions = [
                        'limit' => (int)$get['page_size'],
                        'offset' => ((int)$get['page_no'] - 1) * (int)$get['page_size'],
                        'attributesToRetrieve' => ['id', 'name', 'image', 'remark', 'min_price', 'market_price',
                            'sales_actual', 'sales_virtual', 'first_cate_id', 'second_cate_id', 'third_cate_id',
                            'sort_weight', 'brand_id', 'shop_id', 'is_hot', 'goods_label', 'goods_label_top', 'join_jc', 'year_jc_sales', 'year_sales', 'video',
                            'category_path', 'brand_name', 'tags', 'content'], // Added new fields, removed split_word
                        'attributesToHighlight' => ['name', 'remark', 'content', 'category_path', 'brand_name', 'tags'], // Updated
                        'highlightPreTag' => '<b style="color: red;">',
                        'highlightPostTag' => '</b>',
                        'matchingStrategy' => 'all',
                        'attributesToSearchOn' => ['name', 'remark', 'content', 'category_path', 'brand_name', 'tags'], // Updated
                        // 'minWordSizeForTypos' => 1, // 允许更短词的拼写错误
                        'cropLength' => 30, // 限制高亮片段长度
                    ];

                    // 添加过滤条件
                    if (!empty($filter)) {
                        $searchOptions['filter'] = implode(' AND ', $filter);
                    }

                    // 添加排序条件
                    if (!empty($order)) {
                        $sortArray = [];
                        foreach ($order as $field => $direction) {
                            $sortArray[] = "{$field}:{$direction}";
                        }
                        $searchOptions['sort'] = $sortArray;
                    }

                    // 执行搜索
                    \think\facade\Log::info('MeiliSearch准备执行搜索，原始关键词: ' . $keyword . ', 选项: ' . json_encode($searchOptions));

                    $searchResults = []; // Initialize searchResults
                    $isOriginalSingleTerm = (strpos($keyword, ' ') === false); // Check if keyword (after initial processing) has spaces

                    if ($isOriginalSingleTerm) {
                        // Original input was a single logical unit like "老人鞋"
                        $searchQueryAttempt = '"' . $keyword . '"'; // Enforce phrase search for MeiliSearch
                        \think\facade\Log::info('MeiliSearch尝试短语搜索: ' . $searchQueryAttempt);
                        $searchResults = $meili->advancedSearch('goods', $searchQueryAttempt, $searchOptions);
                        \think\facade\Log::info('MeiliSearch短语搜索结果 (' . $searchQueryAttempt . '): ' . json_encode($searchResults));

                        if (empty($searchResults['hits'])) {
                            // Phrase search failed, try non-phrase search with the original single term
                            \think\facade\Log::info('MeiliSearch短语搜索失败，尝试非短语原始单一名词搜索: ' . $keyword);
                            $searchResults = $meili->advancedSearch('goods', $keyword, $searchOptions);
                            \think\facade\Log::info('MeiliSearch非短语原始单一名词搜索结果: ' . json_encode($searchResults));
                        }
                    } else {
                        // Original input had spaces, e.g., "老人 透气 鞋".
                        // MeiliSearch with `matchingStrategy: 'all'` on this multi-term query.
                        \think\facade\Log::info('MeiliSearch尝试多词匹配搜索: ' . $keyword);
                        $searchResults = $meili->advancedSearch('goods', $keyword, $searchOptions);
                        \think\facade\Log::info('MeiliSearch多词匹配搜索结果 (' . $keyword . '): ' . json_encode($searchResults));
                    }

                    // 如果上述各种基于原始关键词的尝试后仍然没有结果，并且有分词结果，则尝试基于分词的搜索
                    if (empty($searchResults['hits']) && !empty($segmentedWords)) {
                        // 使用分词结果中的第一个词进行搜索
                        $primaryWord = $segmentedWords[0];
                        \think\facade\Log::info('MeiliSearch尝试使用分词结果中的主要词进行搜索: ' . $primaryWord);
                        $searchResults = $meili->advancedSearch('goods', $primaryWord, $searchOptions);
                        \think\facade\Log::info('MeiliSearch分词主要词搜索结果: ' . json_encode($searchResults));

                        // 如果还是没有结果，并且分词结果多于一个，尝试使用所有分词结果组合搜索
                        if (empty($searchResults['hits']) && count($segmentedWords) > 1) {
                            $combinedSegmentedKeyword = implode(' ', $segmentedWords);
                            \think\facade\Log::info('MeiliSearch尝试使用组合分词结果进行搜索: ' . $combinedSegmentedKeyword);
                            $searchResults = $meili->advancedSearch('goods', $combinedSegmentedKeyword, $searchOptions);
                            \think\facade\Log::info('MeiliSearch组合分词搜索结果: ' . json_encode($searchResults));
                        }
                    }

                    // 如果搜索成功，使用搜索结果
                    if (isset($searchResults['hits']) && !empty($searchResults['hits'])) {
                        \think\facade\Log::info('MeiliSearch搜索结果数量: ' . count($searchResults['hits']));

                        // 提取商品ID列表
                        $goodsIds = array_column($searchResults['hits'], 'id');

                        // 保存高亮信息
                        $highlightInfo = [];
                        foreach ($searchResults['hits'] as $hit) {
                            if (isset($hit['_formatted'])) {
                                $highlightInfo[$hit['id']] = [
                                    'name' => $hit['_formatted']['name'] ?? $hit['name'],
                                    'remark' => $hit['_formatted']['remark'] ?? $hit['remark'],
                                    'content' => $hit['_formatted']['content'] ?? ($hit['content'] ?? ''),
                                    'category_path' => $hit['_formatted']['category_path'] ?? ($hit['category_path'] ?? []),
                                    'brand_name' => $hit['_formatted']['brand_name'] ?? ($hit['brand_name'] ?? ''),
                                    'tags' => $hit['_formatted']['tags'] ?? ($hit['tags'] ?? [])
                                ];
                            }
                        }

                        // 将高亮信息保存到$get中，以便后续处理
                        $get['highlight_info'] = $highlightInfo;

                        // 使用搜索结果的ID列表
                        if (!empty($goodsIds)) {
                            $where[] = ['id', 'in', $goodsIds];
                            // 保持结果顺序
                            $get['meili_search_ids'] = $goodsIds;
                        }
                    } else {
                        \think\facade\Log::error('MeiliSearch搜索结果为空，回退到传统搜索');
                        // 如果没有结果，回退到传统搜索, ensuring split_word is not used if deprecated
                        self::fallbackToTraditionalSearch($keyword, $keywordParts, $isMultiKeyword, $get, $where, true); // Pass true for useNewSearchFields
                    }
                } catch (\Exception $e) {
                    // 如果MeiliSearch出错，记录错误并回退到传统搜索
                    \think\facade\Log::error('MeiliSearch异常: ' . $e->getMessage());

                    // 使用传统搜索方法, ensuring split_word is not used if deprecated
                    self::fallbackToTraditionalSearch($keyword, $keywordParts, $isMultiKeyword, $get, $where, true); // Pass true for useNewSearchFields
                }
            }
        }

        // 店铺id
        if(isset($get['shop_id']) && !empty($get['shop_id']) && filter_var($get['shop_id'], FILTER_VALIDATE_INT)) {
            $where[] = ['shop_id', '=', $get['shop_id']];
        }

        // 店铺推荐
        if (Validate::must($get['is_recommend'] ?? '')) {
            $where[] = [ 'is_recommend', '=', $get['is_recommend'] ];
        }

        // 店铺分类
        if(isset($get['shop_cate_id']) && !empty($get['shop_cate_id']) && filter_var($get['shop_cate_id'], FILTER_VALIDATE_INT)) {
            $where[] = ['shop_cate_id', '=', $get['shop_cate_id']];
        }

        // 销量排序(实际销量 + 虚拟销量)
        if(isset($get['sort_by_sales']) && !empty($get['sort_by_sales'])) {
            $elt = ['sales_total'=> trim($get['sort_by_sales'])];
            $order = array_merge($elt, $order);
        }

        // 价格排序
        if(isset($get['sort_by_price']) && !empty($get['sort_by_price'])) {
            $elt = ['min_price'=> trim($get['sort_by_price'])];
            $order = array_merge($elt, $order);
        }

        // 新品排序
        if(isset($get['sort_by_create']) && !empty($get['sort_by_create'])) {
            $elt = ['create_time'=> trim($get['sort_by_create'])];
            $order = array_merge($elt, $order);
        }

        //随机排序
        if(isset($get['is_rand']) && !empty($get['is_rand'])) {
            $get['filed_asc'] ='RAND()';
        }

      
        




        $field = 'id,goods_label_top,goods_label,is_hot,year_jc_sales,join_jc,image,remark,year_sales,video,name,min_price,market_price,sales_actual,first_cate_id,
        second_cate_id,third_cate_id,sort_weight,brand_id,shop_id,sales_virtual,
        (sales_actual + sales_virtual) as sales_total';

        // 检查是否有MeiliSearch搜索结果
        if (isset($get['meili_search_ids']) && !empty($get['meili_search_ids'])) {
            // 使用MeiliSearch的结果顺序
            $goodsIds = $get['meili_search_ids'];

            // 使用FIELD函数保持MeiliSearch返回的顺序
            $orderByIds = 'FIELD(id,' . implode(',', $goodsIds) . ')';

            if(isset($tid)){
                $list = Goods::with(['shop','goods_image'])
                    ->field($field)
                    ->where($where)
                    ->where($get['where_raw'])
                    ->where(function ($query) use ($whereOr) {
                        $query->whereOr($whereOr);
                    })
                    ->orderRaw($orderByIds)
                    ->select();
            } else {
                $list = Goods::with(['shop'])
                    ->field($field)
                    ->where($where)
                    ->where($get['where_raw'])
                    ->where(function ($query) use ($whereOr) {
                        $query->whereOr($whereOr);
                    })
                    ->orderRaw($orderByIds)
                    ->select();
            }
        } else {
            // 使用传统查询方式
            if(isset($tid)){
                $list = Goods::with(['shop','goods_image'])
                    ->field($field)
                    ->where($where)
                    ->where($get['where_raw'])
                    ->where(function ($query) use ($whereOr) {
                        $query->whereOr($whereOr);
                    })
                    ->order($order)
                    ->page($get['page_no'], $get['page_size'])
                    ->select();
            } else {
                if(isset($get['filed_asc']) && !empty($get['filed_asc'])){
                    $list = Goods::with(['shop'])
                        ->field($field)
                        ->where($where)
                        ->where($get['where_raw'])
                        ->where(function ($query) use ($whereOr) {
                            $query->whereOr($whereOr);
                        })
                        ->orderRaw($get['filed_asc'])
                        ->page($get['page_no'], $get['page_size'])
                        ->select();
                } else {
                    $list = Goods::with(['shop'])
                        ->field($field)
                        ->where($where)
                        ->where(function ($query) use ($whereOr) {
                            $query->whereOr($whereOr);
                        })
                        ->where($get['where_raw'])
                        ->order($order)
                        ->page($get['page_no'], $get['page_size'])
                        ->select();
                }
            }
        }
       

        foreach ($list as &$item) {
            $item['shop_type'] = $item['shop']['type']??'';
            if(isset($item['goods_image'])){
                foreach ($item['goods_image'] as &$val) {
                    $val['uri'] =UrlServer::getFileUrl($val['uri']);
                }
                $item['collect_nums']=Db::name('goods_collect')->where('goods_id', $item['id'])->count()?:10;
            }else{
                unset($item['shop']);
            }
            //如果是拼单集采商品显示拼单集采价格
            if($item['join_jc']==1){
                $item['min_price']=Db::name('goods_item')->where('goods_id',$item['id'])->min('pdjc_price');
            }

            // 处理搜索关键词高亮
            if (isset($get['keyword']) && !empty($get['keyword'])) {
                // 保存原始名称和描述
                $item['name_original'] = $item['name'];
                $item['remark_original'] = $item['remark'];

                // 检查是否有MeiliSearch的高亮结果
                if (isset($get['highlight_info']) && !empty($get['highlight_info']) && isset($get['highlight_info'][$item['id']])) {
                    // 使用MeiliSearch的高亮结果
                    $highlightInfo = $get['highlight_info'][$item['id']];
                    $item['name_highlight'] = $highlightInfo['name'] ?? $item['name'];
                    $item['remark_highlight'] = $highlightInfo['remark'] ?? $item['remark'];
                } else {
                    // 使用传统高亮处理方式
                    // 检查是否是多关键词搜索
                    $isMultiKeyword = isset($get['keyword_parts']) && count($get['keyword_parts']) > 1;

                    if ($isMultiKeyword) {
                        // 多关键词高亮处理
                        $keywordParts = $get['keyword_parts'] ?? [];
                        $highlightedName = $item['name'];
                        $highlightedRemark = $item['remark'];

                        // 对每个关键词部分进行高亮处理
                        foreach ($keywordParts as $part) {
                            if (empty(trim($part))) continue;

                            $pattern = '/(' . preg_quote(trim($part), '/') . ')/iu';
                            $replacement = '<b style="color: red;">$1</b>';
                            $highlightedName = preg_replace($pattern, $replacement, $highlightedName);
                            $highlightedRemark = preg_replace($pattern, $replacement, $highlightedRemark);
                        }

                        // 高亮处理分词结果
                        $segmentedWords = $get['segmented_words'] ?? [];
                        if (!empty($segmentedWords) && is_array($segmentedWords)) {
                            foreach ($segmentedWords as $word) {
                                // 只处理长度>=2且不是原始关键词部分的分词结果
                                if (mb_strlen(trim($word), 'UTF-8') >= 2 && !in_array(trim($word), $keywordParts)) {
                                    $pattern = '/(' . preg_quote(trim($word), '/') . ')/iu';
                                    $replacement = '<b style="color: red;">$1</b>';
                                    $highlightedName = preg_replace($pattern, $replacement, $highlightedName);
                                    $highlightedRemark = preg_replace($pattern, $replacement, $highlightedRemark);
                                }
                            }
                        }

                        // 添加高亮字段
                        $item['name_highlight'] = $highlightedName;
                        $item['remark_highlight'] = $highlightedRemark;
                    } else {
                        // 单关键词高亮处理
                        $originalKeyword = $get['original_keyword'] ?? '';
                        $highlightedName = $item['name'];
                        $highlightedRemark = $item['remark'];

                        if (!empty($originalKeyword)) {
                            $pattern = '/(' . preg_quote($originalKeyword, '/') . ')/iu';
                            $replacement = '<b style="color: red;">$1</b>';
                            $highlightedName = preg_replace($pattern, $replacement, $highlightedName);
                            $highlightedRemark = preg_replace($pattern, $replacement, $highlightedRemark);
                        }

                        // 高亮处理分词结果
                        $segmentedWords = $get['segmented_words'] ?? [];
                        if (!empty($segmentedWords) && is_array($segmentedWords)) {
                            foreach ($segmentedWords as $word) {
                                if (trim($word) !== $originalKeyword && mb_strlen(trim($word), 'UTF-8') >= 2) {
                                    $pattern = '/(' . preg_quote(trim($word), '/') . ')/iu';
                                    $replacement = '<b style="color: red;">$1</b>';
                                    $highlightedName = preg_replace($pattern, $replacement, $highlightedName);
                                    $highlightedRemark = preg_replace($pattern, $replacement, $highlightedRemark);
                                }
                            }
                        }

                        // 添加高亮字段
                        $item['name_highlight'] = $highlightedName;
                        $item['remark_highlight'] = $highlightedRemark;
                    }
                }

                // 恢复原始字段
                $item['name'] = $item['name_original'];
                $item['remark'] = $item['remark_original'];

                // 删除临时字段
                unset($item['name_original']);
                unset($item['remark_original']);
            }

            //缓存读取商品标签，有效期6个小时
            $goodsLabelCacheKey = 'goods_label_' . $item['id'];
            $goodsLabelCache = cache($goodsLabelCacheKey);
            if (!$goodsLabelCache) {
                $goodsLabel = Db::name('goods_column')
                    ->field('name,image')
                    ->whereRaw("FIND_IN_SET(id,?)", [$item['goods_label']])
                    ->select()->toArray();
                cache($goodsLabelCacheKey, $goodsLabel, 3600 * 1);
                $item['goods_label'] = $goodsLabel;
            } else {
                $item['goods_label'] = $goodsLabelCache;
            }

            $goodsLabelTopCacheKey = 'goods_label_top_' . $item['id'];
            $goodsLabelTopCache = cache($goodsLabelTopCacheKey);
            if (!$goodsLabelTopCache) {
                $goodsLabelTop = Db::name('goods_column')
                    ->field('name,image')
                    ->whereRaw("FIND_IN_SET(id,?)", [$item['goods_label_top']])
                    ->select()->toArray();
                cache($goodsLabelTopCacheKey, $goodsLabelTop, 3600 * 6);
                $item['goods_label_top'] = $goodsLabelTop;
            } else {
                $item['goods_label_top'] = $goodsLabelTopCache;
            }

            $isHotCacheKey = 'is_hot_' . $item['id'];
            $isHotCache = cache($isHotCacheKey);
            if (!$isHotCache) {
                $isHot = Db::name('goods_column')
                    ->field('name,image')
                    ->whereRaw("FIND_IN_SET(id,?)", [$item['is_hot']])
                    ->select()->toArray();
                cache($isHotCacheKey, $isHot, 3600 * 6);
                $item['is_hot'] = $isHot;
            } else {
                $item['is_hot'] = $isHotCache;
            }
        }




        // 假设 $list 是一个对象数组，转换为数组后再处理
        if(isset($get['is_limit']) && !empty($get['is_limit'])){
            // 将对象数组转换为普通数组
            $listArray = json_decode(json_encode($list), true);
            // 取出前3个元素作为top数组
            $top = array_slice($listArray, 0, 3);
            // 剩下的元素作为list数组
            $listArray = array_slice($listArray, 3);
            // 组装最终数据结构
            $finalList = ['top' => $top, 'list' => $listArray];
        }
        else{
            $finalList = $list ? $list->toArray() : [];
        }

        $count = Goods::where($where)->count();

        $more = is_more($count, $get['page_no'], $get['page_size']);

        $data = [
            'lists'         => $finalList,
            'page_no'       => $get['page_no'],
            'page_size'     => $get['page_size'],
            'count'         => $count,
            'more'          => $more
        ];
        return $data;
    }

    /**
     * 根据商品栏目获取商品列表
     *
     * @param int $columnId 栏目ID
     * @param int $page_no 页码
     * @param int $page_size 每页数量
     * @return array 商品列表
     */
    public static function getGoodsListByColumnId($columnId, $page_no, $page_size)
    {
        $page_size=$page_size??10;
        // 销售中商品：未删除/审核通过/已上架
        $onSaleWhere = [
            ['del', '=', GoodsEnum::DEL_NORMAL],
            ['status', '=', GoodsEnum::STATUS_SHELVES],
            ['audit_status', '=', GoodsEnum::AUDIT_STATUS_OK],
        ];

        if (!empty(self::filterShopsIds())) {
            // 过滤已删除、已冻结、已暂停营业、已到期的店铺
            $onSaleWhere[] = ['shop_id', 'not in', self::filterShopsIds()];
        }

        // 添加栏目ID过滤条件
        if (!empty($columnId)) {
            $onSaleWhere[] = ['column_ids', 'like', "%{$columnId}%"];
        }

        $order = [
            'sort_weight' => 'asc', // 数字越小，权重越大
            'sales_actual' => 'desc',
            'id' => 'desc'
        ];

        $list = Goods::field('id,name,image,market_price,min_price,sales_actual,column_ids,sort_weight,sales_virtual,(sales_actual + sales_virtual) as sales_total')
            ->where($onSaleWhere)
            ->order($order)
            ->page($page_no, $page_size)
            ->select();

        $count = Goods::where($onSaleWhere)
            ->count();

        $list = $list ? $list->toArray() : [];

        $more = is_more($count, $page_no, $page_size);
        //获取商品标签

        $data = [
            'lists'          => $list,
            'page_no'       => $page_no,
            'page_size'     => $page_size,
            'count'         => $count,
            'more'          => $more
        ];
        return $data;
    }

    /**
     * @notes 获取已删除、已冻结、已暂停营业、已到期店铺的id
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date 2021/7/20 14:29
     */
    public static function filterShopsIds()
    {
        // 已删除、已冻结、已暂停营业的店铺
        $invalidShops = Shop::field('id,name')->whereOr([
            ['del', '=', 1], // 已删除
            ['is_freeze', '=', 1], // 已冻结
            ['is_run', '=', 0] // 暂停营业
        ])->select()->toArray();

        // 已过期的店铺
        $expiredShops = Shop::field('id,name')->where([
            ['expire_time', '<>', 0],
            ['expire_time', '<=', time()],
        ])->select()->toArray();

        $filterShops = array_merge($invalidShops, $expiredShops);
        $filterShopsIds = array_column($filterShops, 'id');
        return $filterShopsIds;
    }

    /**
     * 记录关键词
     */
    public static function recordKeyword($keyword, $user_id)
    {
        $record = SearchRecord::where(['user_id'=>$user_id,'keyword'=>$keyword,'del'=>0])->find();
        if($record){
            // 有该关键词记录, 更新
            return SearchRecord::where(['id'=>$record['id']])->update(['count'=>Db::raw('count+1'),'update_time'=>time()]);
        }
        // 无该关键词记录 > 新增
        return SearchRecord::create([
            'user_id'=>$user_id,
            'keyword'=>$keyword,
            'count' => 1,
            'update_time' => time(),
            'del' => 0
        ]);
    }

    /**
     * 回退到传统搜索方法
     *
     * @param string $keyword 搜索关键词
     * @param array $keywordParts 关键词分词结果
     * @param bool $isMultiKeyword 是否是多关键词
     * @param array &$get 请求参数
     * @param array &$where 查询条件
     */
    protected static function fallbackToTraditionalSearch($keyword, $keywordParts, $isMultiKeyword, &$get, &$where, $useNewSearchFields = false)
    {
        // 使用简单分词处理（如果之前没有处理过）
        if (!isset($get['segmented_words']) || empty($get['segmented_words'])) {
            $segmentedWords = [$keyword];

            // 尝试使用阿里云分词（如果可用）
            try {
                $words = Alisegment($keyword);
                if (!empty($words['words']) && is_array($words['words'])) {
                    $segmentedWords = $words['words'];
                }
            } catch (\Exception $e) {
                // 如果分词失败，记录错误但继续使用原始关键词
                \think\facade\Log::error('传统搜索分词失败，使用原始关键词: ' . $e->getMessage());
            }
            $get['segmented_words'] = $segmentedWords;
        } else {
            $segmentedWords = $get['segmented_words'];
        }

        // 合并空格分割的关键词和分词结果
        $allKeywordParts = isset($get['all_keyword_parts']) ? $get['all_keyword_parts'] : array_unique(array_merge($keywordParts, $segmentedWords));
        $get['all_keyword_parts'] = $allKeywordParts;

        // 构建搜索条件
        if ($useNewSearchFields) {
            // Based on the new MeiliSearch settings, 'split_word' is removed.
            // 'category_path', 'brand_name', 'tags' are array/object types in MeiliSearch and cannot be directly used in SQL LIKE.
            // We will search in 'name', 'remark', 'content'.
            // For 'category_path', 'brand_name', 'tags', a more complex subquery or join would be needed if traditional SQL must replicate this.
            // For simplicity in fallback, we'll stick to text fields that are directly comparable.
            $searchFields = ['name', 'remark', 'content'];
        } else {
            $searchFields = ['name', 'remark', 'split_word'];
        }

        // 优化搜索逻辑：提高精确性和相关性
        $where[] = function($query) use ($allKeywordParts, $keyword, $searchFields) {
            // 1. 最高优先级：完整关键词精确匹配
            $query->whereOr(function($subQuery) use ($keyword, $searchFields) {
                foreach ($searchFields as $field) {
                    $subQuery->whereOr($field, 'like', '%'.$keyword.'%');
                }
            });

            // 2. 处理同义词和相关词匹配
            $synonyms = self::getSynonyms($keyword);
            if (!empty($synonyms)) {
                foreach ($synonyms as $synonym) {
                    $query->whereOr(function($subQuery) use ($synonym, $searchFields) {
                        foreach ($searchFields as $field) {
                            $subQuery->whereOr($field, 'like', '%'.$synonym.'%');
                        }
                    });
                }
            }

            // 3. 分词匹配（仅当原关键词是复合词时）
            if (count($allKeywordParts) > 1) {
                // 对于复合词，要求所有分词都匹配（AND逻辑）
                $query->whereOr(function($subQuery) use ($allKeywordParts, $keyword, $searchFields) {
                    $validParts = array_filter($allKeywordParts, function($part) use ($keyword) {
                        return !empty(trim($part)) && trim($part) !== $keyword && mb_strlen(trim($part), 'UTF-8') >= 2;
                    });

                    if (count($validParts) >= 2) {
                        // 要求至少匹配两个分词（提高相关性）
                        foreach ($searchFields as $field) {
                            $subQuery->whereOr(function($fieldQuery) use ($validParts, $field) {
                                foreach ($validParts as $part) {
                                    $fieldQuery->where($field, 'like', '%'.trim($part).'%');
                                }
                            });
                        }
                    }
                });
            }
        };
    }

    /**
     * 获取关键词的同义词和相关词
     *
     * @param string $keyword 原始关键词
     * @return array 同义词数组
     */
    protected static function getSynonyms($keyword)
    {
        // 定义同义词映射表
        $synonymMap = [
            // 眼镜相关
            '眼镜' => ['老花镜', '近视镜', '太阳镜', '墨镜', '护目镜'],
            '老花镜' => ['眼镜', '花镜', '老花眼镜', '老人眼镜'],
            '近视镜' => ['眼镜', '近视眼镜'],
            '太阳镜' => ['眼镜', '墨镜', '遮阳镜'],
            '墨镜' => ['眼镜', '太阳镜', '遮阳镜'],

            // 鞋类相关
            '老人鞋' => ['健步鞋', '中老年鞋', '老年鞋', '舒适鞋'],
            '健步鞋' => ['老人鞋', '运动鞋', '休闲鞋'],
            '运动鞋' => ['跑步鞋', '健步鞋', '休闲鞋'],

            // 医疗器械相关
            '轮椅' => ['代步车', '助行器', '行走辅助器'],
            '拐杖' => ['助行器', '手杖', '行走辅助器'],
            '助行器' => ['拐杖', '轮椅', '行走辅助器'],

            // 护理用品相关
            '护理垫' => ['尿垫', '防漏垫', '床垫'],
            '成人纸尿裤' => ['尿不湿', '纸尿片', '护理垫'],
        ];

        $keyword = trim($keyword);
        $synonyms = [];

        // 直接匹配
        if (isset($synonymMap[$keyword])) {
            $synonyms = $synonymMap[$keyword];
        }

        // 模糊匹配（包含关系）
        foreach ($synonymMap as $key => $values) {
            if (strpos($keyword, $key) !== false || strpos($key, $keyword) !== false) {
                $synonyms = array_merge($synonyms, $values);
            }
        }

        // 去重并过滤掉原关键词
        $synonyms = array_unique($synonyms);
        $synonyms = array_filter($synonyms, function($synonym) use ($keyword) {
            return $synonym !== $keyword;
        });

        return array_values($synonyms);
    }

    //检查商品是否正在参加活动
    public static function checkActivity($goods){
        // 获取正在秒杀的时段
        $seckill_time = SeckillGoodsLogic::getSeckillTimeIng();

        if($seckill_time === false) {
            // 不在秒杀时段，直接返回
            return $goods;
        }

        // 判断是否是秒杀中的商品
        $seckill_goods = SeckillGoods::where([
            ['del', '=', 0],
            ['seckill_id', '=', $seckill_time['id']],
            ['goods_id', '=', $goods['id']],
            ['review_status', '=', 1],
        ])->select()->toArray();

        if(!$seckill_goods) {
            // 不是秒杀商品
            return $goods;
        }
        // 判断参与日期是否包含今天
        $flag = false;
        $now = time();
        foreach($seckill_goods as $item) {
            $start_date_time = strtotime($item['start_date'].' ' . $seckill_time['start_time']);
            $end_date_time = strtotime($item['end_date'].' ' . $seckill_time['end_time']);
            if($start_date_time < $now && $end_date_time > $now) {
                $flag = true;
                // 获取该商品的秒杀信息
                $seckill_goods_info = SeckillGoods::where([
                    'goods_id' => $goods['id'],
                    'seckill_id' => $seckill_time['id'],
                    'start_date' => $item['start_date'],
                    'end_date' => $item['end_date'],
                ])->column('goods_id,item_id,price', 'item_id');
                break;
            }
        }

        if($flag === false) {
            // 参与日期不在今天
            return $goods;
        }
        // 确定是秒杀中的商品
        // 先将商品市场价换成原SKU最小价
        $goods['market_price'] = $goods['min_price'];
        // 替换活动价
        foreach($goods['goods_item'] as &$item) {
            // 商品价格替换为最小的秒杀价
            if($goods['min_price'] > $seckill_goods_info[$item['id']]['price']) {
                $goods['min_price'] = $seckill_goods_info[$item['id']]['price'];
            }
            // 原市场价替换为原SKU售价
            $item['market_price'] = $item['price'];
            // SKU替换秒杀价
            $item['price'] = $seckill_goods_info[$item['id']]['price'];
        }
        $today_date = date('Y-m-d');
        $goods['activity'] = [
            'type' => 1,
            'type_desc' => '秒杀商品',
            'end_time' => strtotime($today_date.' '.$seckill_time['end_time'])
        ];
        return $goods;

    }

    /**
     * @notes 获取商品分销信息
     * @param $goodsId
     * @param $userId
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date 2021/9/6 18:48
     */
    public static function getDistribution($goodsId, $userId)
    {
        $earnings = 0;
        $goods = Goods::findOrEmpty($goodsId)->toArray();
        $distributionGoods = DistributionGoods::where('goods_id', $goodsId)->select()->toArray();
        if(!empty($distributionGoods) && $distributionGoods[0]['is_distribution'] && $distributionGoods[0]['rule'] == 2) {
            foreach($distributionGoods as $item) {
                $earnings = max($earnings, round($goods['max_price'] * $item['first_ratio'] / 100, 2));
                $earnings = max($earnings, round($goods['max_price'] * $item['second_ratio'] / 100, 2));
            }
        }
        if(!empty($distributionGoods) && $distributionGoods[0]['is_distribution'] && $distributionGoods[0]['rule'] == 1) {
            $levels = DistributionLevel::select()->toArray();
            foreach($levels as $item) {
                $earnings = max($earnings, round($goods['max_price'] * $item['first_ratio'] / 100, 2));
                $earnings = max($earnings, round($goods['max_price'] * $item['second_ratio'] / 100, 2));
            }
        }

        // 详情页是否显示佣金
        $isShow = ConfigServer::get('distribution', 'is_show_earnings', 0);
        // 系统总分销开关
        $distributionOpen = ConfigServer::get('distribution', 'is_open', 0);
        // 商家信息-获取商家是否被禁用分销功能(is_distribution)
        $shop = Shop::findOrEmpty($goods['shop_id'])->toArray();

        if ($distributionOpen && $shop['is_distribution'] && $isShow) {
            //详情页佣金可见用户 0-全部用户 1-分销商
            $scope = ConfigServer::get('distribution', 'show_earnings_scope', 0);
            $user = Distribution::where(['user_id' => $userId])->findOrEmpty()->toArray();
            if ($scope && empty($user['is_distribution'])) {
                $isShow = 0;
            }
        } else {
            $isShow = 0;
        }

        return [
            'is_show' => $isShow,
            'earnings' => $earnings
        ];
    }



    /**
     * 集采拼单商品列表
     */
    public static function getpdGoodsList($get)
    {
        // 销售中商品：未删除/审核通过/已上架
        $where = [
            ['del', '=', GoodsEnum::DEL_NORMAL],  // 未删除
            ['status', '=', GoodsEnum::STATUS_SHELVES], // 上架中
            ['audit_status', '=', GoodsEnum::AUDIT_STATUS_OK], // 审核通过
            ['join_jc', '=', 1], // 是否加入拼单集采通过
        ];

        $goods_ids=Db::name('jcai_activity')
            ->where([
                ['status','<>',2],
                ['del','=',0],
            ])
            ->column('goods_id');
        if($goods_ids){
            $where[]=['id','not in',$goods_ids];
        }

        $order = [
            'sort_weight' => 'asc', // 商品权重，数字越小权重越大
            'sort' => 'asc',
            'id' => 'desc'
        ];




        return self::getGoodsListTemplate($where, $order, $get);
    }


    /**
     * 行家严选
     */
    public static  function chooseByExpert($get)
    {
        // 销售中商品：未删除/审核通过/已上架
        $where = [
            ['del', '=', GoodsEnum::DEL_NORMAL],  // 未删除
            ['status', '=', GoodsEnum::STATUS_SHELVES], // 上架中
            ['audit_status', '=', GoodsEnum::AUDIT_STATUS_OK], // 审核通过
            ['join_jc', '=', 0], // 没有加入拼单集采通过
        ];
        $shop_ids=Db::name('shop')->where([
            ['yan_level','>',0],
            ['is_run','=',1],
            ['yan_fee','=', 1]
        ])->order('score desc,return_rate desc')->column('id');

        if(!empty($shop_ids)){
          $shop_ids=implode(',',$shop_ids);
          $get['filed_asc']=' field(shop_id,'.$shop_ids.') ';
        }else{
            $data = [
                'lists'         => [],
                'page_no'       => 0,
                'page_size'     => 0,
                'count'         => 0,
                'more'          => 0
            ];
            return $data;
        }

        $order = [
            'hgou_lv' => 'desc',
            'sales_total' => 'desc', // 虚拟销量倒序
        ];


        return self::getGoodsListTemplate($where, $order, $get);
    }


    /**
     * 居家爆品
     */
    public static  function getHomeHitsList($get)
    {
        // 销售中商品：未删除/审核通过/已上架
        $where = [
            ['del', '=', GoodsEnum::DEL_NORMAL],  // 未删除
            ['status', '=', GoodsEnum::STATUS_SHELVES], // 上架中
            ['audit_status', '=', GoodsEnum::AUDIT_STATUS_OK], // 审核通过
            ['join_jc', '=', 0], // 没有加入拼单集采通过
        ];
        $where[]=['first_cate_id','in',[1,6,12,16,15]];
        $order = [
            'sales_total' => 'desc', // 虚拟销量倒序
            'hgou_lv' => 'desc',
            'clicks' => 'desc', // 虚拟销量倒序
        ];
        return self::getGoodsListTemplate($where, $order, $get);
    }

    /**
     * 找厂家-热销企业-热门工厂精选
     */
    public static  function getHotGoods($get)
    {
        // 销售中商品：未删除/审核通过/已上架
        $where = [
            ['del', '=', GoodsEnum::DEL_NORMAL],  // 未删除
            ['status', '=', GoodsEnum::STATUS_SHELVES], // 上架中
            ['audit_status', '=', GoodsEnum::AUDIT_STATUS_OK], // 审核通过
            ['join_jc', '=', 0], // 没有加入拼单集采通过
        ];
        $order=[];
        $get['is_rand']=1;
        return self::getGoodsListTemplate($where, $order, $get);
    }


    /**
     * 找产品-年度榜
     */
    public static  function getAnnualTopProducts($get)
    {
        // 销售中商品：未删除/审核通过/已上架
        $where = [
            ['del', '=', GoodsEnum::DEL_NORMAL],  // 未删除
            ['status', '=', GoodsEnum::STATUS_SHELVES], // 上架中
            ['audit_status', '=', GoodsEnum::AUDIT_STATUS_OK], // 审核通过
            ['join_jc', '=', 0], // 没有加入拼单集采通过
        ];
        //当前年的开始到结束时间
        $start_time = strtotime(date('Y-01-01'));
        $end_time = time();
        $where_order[]=['o.create_time','between',[$start_time,$end_time]];
        //获取订单id
        $order_ids=OrderGoods::where($where_order)->alias('o')
            ->field('o.goods_id,count(o.goods_num) as num')
            ->LeftJoin('goods s','s.id=o.goods_id')
            ->where([
                ['o.refund_status','=',0],
                ['s.del','=',0],
                ['s.status','=',1],
            ])
            ->group('o.goods_id')
            ->order('num desc')
            ->select()->toArray();
        //更新goods表中的year_sales字段
        foreach ($order_ids as $item){
            $goods=Goods::find($item['goods_id']);
            if(!empty($goods)){
                $goods->year_sales=$item['num'];
                $goods->save();
            }
        }

        $order = [
            'year_sales' => 'desc', // 年销量倒序
            'sales_total' => 'desc', // 虚拟销量倒序
            'hgou_lv' => 'desc',
            'clicks' => 'desc', //点击量
        ];
        return self::getGoodsListTemplate($where, $order, $get);
    } /**
     * 找产品-机构必采
     */
    public static  function getBibuySellers($get)
    {
        // 销售中商品：未删除/审核通过/已上架
        $where = [
            ['del', '=', GoodsEnum::DEL_NORMAL],  // 未删除
            ['status', '=', GoodsEnum::STATUS_SHELVES], // 上架中
            ['audit_status', '=', GoodsEnum::AUDIT_STATUS_OK], // 审核通过
            ['join_jc', '=', 0], // 没有加入拼单集采通过
        ];
        $get['where_raw'] = "FIND_IN_SET(9,`goods_label_top`)";
        $order = [
            'year_sales' => 'desc', // 年销量倒序
            'sales_total' => 'desc', // 虚拟销量倒序
            'hgou_lv' => 'desc',
            'clicks' => 'desc', //点击量
        ];
        return self::getGoodsListTemplate($where, $order, $get);
    }


    /**
     * 集采购-年度榜
     */
    public static  function getPurchaseAnnualRank($get)
    {
        // 销售中商品：未删除/审核通过/已上架
        $where = [
            ['del', '=', GoodsEnum::DEL_NORMAL],  // 未删除
            ['status', '=', GoodsEnum::STATUS_SHELVES], // 上架中
            ['audit_status', '=', GoodsEnum::AUDIT_STATUS_OK], // 审核通过

        ];
        //当前年的开始到结束时间
        $start_time = strtotime(date('Y-01-01'));
        $end_time = time();
        $where_order[]=['o.create_time','between',[$start_time,$end_time]];
        //获取订单id
        $order_ids=OrderGoods::where($where_order)->alias('o')
            ->field('o.goods_id,count(o.goods_num) as num')
            ->LeftJoin('goods s','s.id=o.goods_id')
            ->where([
                ['o.refund_status','=',0],
                ['s.del','=',0],
                ['s.status','=',1],
                ['s.join_jc','=',1],
            ])
            ->group('o.goods_id')
            ->order('num desc')
            ->select()->toArray();
        //更新goods表中的year_sales字段
        foreach ($order_ids as $item){
            $goods=Goods::find($item['goods_id']);
            if(!empty($goods)){
                $goods->year_jc_sales=$item['num'];
                $goods->save();
            }
        }
        $where[]=['join_jc','=',1];
        $goods_ids=Db::name('jcai_activity')
            ->where([
                ['status','<>',2],
                ['del','=',0],
            ])
            ->column('goods_id');
        if($goods_ids){
            $where[]=['id','not in',$goods_ids];
        }
        $order = [
            'year_jc_sales' => 'desc', // 年销量倒序
            'sales_total' => 'desc', // 虚拟销量倒序
            'clicks' => 'desc', //点击量
        ];
        return self::getGoodsListTemplate($where, $order, $get);
    }

    /**
     * 找产品-收藏榜
     */
    public static  function getProductCollectionRanking($get)
    {
        // 销售中商品：未删除/审核通过/已上架
        $where = [
            ['del', '=', GoodsEnum::DEL_NORMAL],  // 未删除
            ['status', '=', GoodsEnum::STATUS_SHELVES], // 上架中
            ['audit_status', '=', GoodsEnum::AUDIT_STATUS_OK], // 审核通过
            ['join_jc', '=', 0], // 没有加入拼单集采通过
        ];

        $goods=Db::name('goods_collect')
            ->field('goods_id,count(id) as num')
            ->where('status',1)
            ->group('goods_id')
            ->order('num desc')
            ->select()->toArray();
       if(!empty($goods)){
           foreach ($goods as $item){
               $goods_id=$item['goods_id'];
               Goods::where('id',$goods_id)->update(['collect_nums'=>$item['num']]);
           }
       }
        $order = [
            'collect_nums' => 'desc', // 年销量倒序
            'sales_total' => 'desc', // 虚拟销量倒序
            'clicks' => 'desc', //点击量
        ];
        return self::getGoodsListTemplate($where, $order, $get);

    }

    /**
     * 找产品-新品上新
     */
    public static function getNewProduct($get)
    {
        // 销售中商品：未删除/审核通过/已上架
        $where = [
            ['del', '=', GoodsEnum::DEL_NORMAL],  // 未删除
            ['status', '=', GoodsEnum::STATUS_SHELVES], // 上架中
            ['audit_status', '=', GoodsEnum::AUDIT_STATUS_OK], // 审核通过
            ['join_jc', '=', 0], // 没有加入拼单集采通过
        ];

        // 使用whereOr查询条件，同时查询最近30天的商品和带有"新品上新"标签的商品
        $whereOr = [
            [
                ['create_time', '>=', strtotime(date('Y-m-d', strtotime("-30 day")))],
                ['create_time', '<', strtotime(date('Y-m-d'))]
            ],
            [
                ['goods_label_top', 'like', '%2%'] // 2是"新品上新"标签的ID
            ]
        ];
        //随机排序
        $order_array=['asc','desc'];
        $order_field_array=['update_time','max_price','sort_weight','create_time','id','name','shop_id','sort'];

        $ar1=rand(0,1);
        $ar2=rand(0,7);

        // 排序规则：先按权重排序，再按创建时间排序
        $order = [
             $order_field_array[$ar2] =>  $order_array[$ar1], // 商品权重，数字越小权重越大     
        ];

        $get['whereOr'] = $whereOr;

        return self::getGoodsListTemplate($where, $order, $get);
    }


    /**
     * 找产品-热卖爆品
     */
    public static  function getHotSellers($get)
    {
        // 销售中商品：未删除/审核通过/已上架
        $where = [
            ['del', '=', GoodsEnum::DEL_NORMAL],  // 未删除
            ['status', '=', GoodsEnum::STATUS_SHELVES], // 上架中
            ['audit_status', '=', GoodsEnum::AUDIT_STATUS_OK], // 审核通过
            ['join_jc', '=', 0], // 没有加入拼单集采通过
        ];
        //一个月区间上架的商品


        $order = [
            'sales_total' => 'desc',
        ];
        return self::getGoodsListTemplate($where, $order, $get);
    }


    /**
     * 集采购-集采爆品
     */
    public static  function getpurchaseExplosives($get)
    {
        // 销售中商品：未删除/审核通过/已上架
        $where = [
            ['del', '=', GoodsEnum::DEL_NORMAL],  // 未删除
            ['status', '=', GoodsEnum::STATUS_SHELVES], // 上架中
            ['audit_status', '=', GoodsEnum::AUDIT_STATUS_OK], // 审核通过

        ];
        //一个月区间上架的商品
        $goods_ids=Db::name('jcai_activity')
            ->where([
                ['status','<>',2],
                ['del','=',0],
            ])
            ->column('goods_id');
        if($goods_ids){
            $where[]=['id','not in',$goods_ids];
        }
        $where[]=['join_jc','=',1];
        $order = [
            'sales_total' => 'desc',
        ];
        return self::getGoodsListTemplate($where, $order, $get);
    }


    /**
     * 集采购-推荐榜
     */
    public static  function getRecommendedPurchaseList($get)
    {
        // 销售中商品：未删除/审核通过/已上架
        $where = [
            ['del', '=', GoodsEnum::DEL_NORMAL],  // 未删除
            ['status', '=', GoodsEnum::STATUS_SHELVES], // 上架中
            ['audit_status', '=', GoodsEnum::AUDIT_STATUS_OK], // 审核通过
        ];
        //一个月区间上架的商品
        $goods_ids=Db::name('jcai_activity')
            ->where([
                ['status','<>',2],
                ['del','=',0],
            ])
            ->column('goods_id');
        if($goods_ids){
            $where[]=['id','not in',$goods_ids];
        }
        $where[]=['join_jc','=',1];
        $order = [
            'reshare_nums' => 'desc',
            'sales_total' => 'desc',
            'clicks' => 'desc',
            'share_nums' => 'desc',
        ];
        return self::getGoodsListTemplate($where, $order, $get);
    }



    public static function calculateRepurchaseRate($goods_id)
    {

        // 或者如果不使用join，先获取order_goods表中的order_id，再查询order表

        $orderIds = DB::name('order_goods')
            ->where([
                ['goods_id','=',$goods_id],
                ['refund_status','=',0]
            ])
            ->group('order_id')
            ->column('order_id');
        if (empty($orderIds)) {
            return 0.00; // 如果没有任何订单包含该商品，则回购率为0
        }
        $user_id = DB::name('order')
            ->where([
                ['id','in',$orderIds],
                ['refund_status','=',0],
                ['pay_status','=',1],
//                ['order_status','=',3]
            ])
            ->group('user_id')
            ->count('user_id');


        if (empty($user_id)) {
            return 0.00; // 如果没有客户购买过该商品，则回购率为0
        }
        $user_ids = DB::name('order')
            ->where([
                ['id','in',$orderIds],
                ['refund_status','=',0],
                ['pay_status','=',1],
//                ['order_status','=',3]
            ])
            ->group('user_id')
            ->having('COUNT(user_id) > 1')
            ->count('user_id');

        if (empty($user_ids)) {
            return 0.00; // 如果没有客户购买过该商品，则回购率为0
        }
        // 计算回购率
        $repurchaseRate = $user_ids / $user_id;

        // 保留两位小数
        return round($repurchaseRate, 2);
    }


    /*
     *
     * 推荐商品根据用户浏览量及搜索关键词推荐商品
     */
    public static function getRecommendGoods($get){
        $record = SearchRecord::where(['user_id'=>$get['user_id'],'del'=>0])->field('keyword,update_time')->order('update_time desc')->limit(5)->select()->toArray();
        //获取关键词的相关商品ID
        $where= [
            ['del', '=', GoodsEnum::DEL_NORMAL],  // 未删除
            ['status', '=', GoodsEnum::STATUS_SHELVES], // 上架中
            ['audit_status', '=', GoodsEnum::AUDIT_STATUS_OK], // 审核通过
            ['shop_id', 'not in', self::filterShopsIds()]
        ];
        $goods_ids=[];
        if(!empty($record)){
            foreach($record as $word){
                $words=segment($word['keyword']);
                if(!empty($words)){
                    foreach ($words as $word2) {
                        $whereOr[] = ['name|remark|content', 'like', '%'.trim($word2).'%'];
                    }
                }
                if(!empty($whereOr)){
                    $goods_ids[$word['update_time']]=Db::name('goods')->where($where)->where(function ($query) use ($whereOr) {
                        $query->whereOr($whereOr);
                    })->orderRaw('rand()')->column('id');
                }

            }

            //将goods_ids数组按照时间戳排序
            if(!empty($goods_ids)){
                ksort($goods_ids);
                $goods_ids=array_values(end($goods_ids));
            }
        }

        $where2 = [
            ['G.del', '=', GoodsEnum::DEL_NORMAL],  // 未删除
            ['G.status', '=', GoodsEnum::STATUS_SHELVES], // 上架中
            ['G.audit_status', '=', GoodsEnum::AUDIT_STATUS_OK], // 审核通过
            ['G.shop_id', 'not in', self::filterShopsIds()]
        ];
        //点击次数
        $goods_ids2=GoodsClick::where(['c.user_id'=>$get['user_id']])->alias('c')
            ->leftJoin('goods G', 'G.id=c.goods_id')
            ->where($where2)
            ->group('c.goods_id')
            ->order('c.create_time desc')
            ->limit(10)->column('c.goods_id');

        // 合并商品ID列表，并保留关键词相关商品的顺序
        $all = array_merge($goods_ids, $goods_ids2);

        // 如果商品ID列表不为空，则进行排序
        if (!empty($all)) {
            $orther=Db::name('goods')
                ->where($where)
                ->whereNotIn('id', implode(',',$all))
                ->column('id');
            shuffle($all);
            if($orther){
                $all=array_merge($all,$orther);
            }
            $get['filed_asc'] = 'field(id, ' . implode(',', $all) . ')';
        }else{
            $get['filed_asc'] = 'rand()';
        }
        return $get['filed_asc'];
    }


    /**
     * 根据用户输入生成候选词（基于词性）
     * @param string $input 用户输入
     * @param int $limit 返回数量
     * @return array
     */
    public static function getSuggestionsByTags(string $input, int $limit = 10): array
    {
        if (empty($input)) {
            return [];
        }

        // 查找匹配的分词
        $words = Db::name('word')->where('word', 'like', $input . '%')
            ->order('word', 'asc')
            ->limit(100)
            ->select()
            ->column('id');

        if (empty($words)) {
            return [];
        }

        // 获取相关商品

        $productWords = ProductWord::with(['word', 'goods'])
            ->whereIn('word_id', $words)
            ->limit(100)
            ->select();

        if (empty($productWords)) {
            return [];
        }

        // 组织数据
        $candidates = [];
        foreach ($productWords as $pw) {

            // 检查$pw->goods是否为Collection类型，如果是，则遍历获取每个模型的name属性
            $productName = '';
            foreach ($pw['goods'] as $item) {
                $productName = $item['name'];
                break; // 只取第一个商品名称
            }
            if ($pw['word'] instanceof \think\model\Collection) {
                $tags = $pw['word']->column('tags');
            } else {
                // 否则直接获取单个模型的name属性
                $tags = $pw['word']['tags'];
            }

            // 计算权重
            $weight = self::calculateWordWeight($tags);

            $matchType = self::getMatchType($productName, $input);

            $candidates[] = [
                'text' => $productName,
                'weight' => $weight,
                'match_type' => $matchType,
                'length' => mb_strlen($productName)
            ];
        }

        // 去重
        $candidates = array_unique($candidates, SORT_REGULAR);

        // 排序
        usort($candidates, function($a, $b) {
            // 先按匹配类型排序
            if ($a['match_type'] !== $b['match_type']) {
                return $a['match_type'] <=> $b['match_type'];
            }

            // 再按权重排序
            if ($a['weight'] !== $b['weight']) {
                return $b['weight'] <=> $a['weight'];
            }

            // 最后按长度排序
            return $a['length'] <=> $b['length'];
        });

        // 取前N个
        $result = array_slice($candidates, 0, $limit);

        // 只返回文本
        return array_column($result, 'text');
    }

    /**
         * 计算词性权重
         * @param array $tags
         * @return int
         */
        protected static function calculateWordWeight(array $tags): int
        {
            $weight = 0;
            foreach ($tags as $tag) {
                // 使用静态属性或类常量替代 $this，因为这是在静态方法中
                $weight += self::$tagWeights[$tag] ?? 0;
                // 或者如果 $tagWeights 是一个静态方法返回的数组，可以这样调用
                // $weight += YourClassName::getTagWeights()[$tag] ?? 0;
            }
            return $weight;
        }

    /**
     * 获取匹配类型
     * @param string $text
     * @param string $input
     * @return int
     */
    protected static function getMatchType(string $text, string $input): int
    {

        // 0: 开头匹配（最高优先级）
        if (mb_strpos($text, $input) === 0) {
            return 0;
        }

        // 1: 包含匹配（次优先级）
        if (mb_strpos($text, $input) !== false) {
            return 1;
        }

        // 2: 其他匹配（最低优先级）
        return 2;
    }
    /**
     * @notes 根据已选规格获取剩余可用规格及库存
     * @param int $goods_id 商品ID
     * @param array $selected_spec_values 已选择的规格值名称数组，例如：['红色', 'S']
     * @return array|false
     * <AUTHOR>
     * @date 2025/07/18
     */
    public static function getAvailableSpecStock($goods_id, $selected_spec_values)
    {
        try {
            // 1. 根据规格值名称查询规格详情
            $selected_details = Db::name('goods_spec_value')
                ->where('goods_id', $goods_id)
                ->whereIn('value', $selected_spec_values)
                ->select()
                ->toArray();

            // 校验选择的规格是否都有效
            if (count($selected_details) !== count($selected_spec_values)) {
                self::$error = '一个或多个规格值无效';
                return false;
            }
            $selected_value_ids = array_column($selected_details, 'id');
            $selected_spec_ids = array_unique(array_column($selected_details, 'spec_id'));

            // 2. 查找包含所有已选规格的SKU (goods_item)
            $query = GoodsItem::where('goods_id', $goods_id);
            foreach ($selected_value_ids as $vid) {
                $query->whereRaw("FIND_IN_SET(?, spec_value_ids)", [$vid]);
            }
            $compatible_items = $query->select()->toArray();

            // 3. 从兼容的SKU中，聚合所有其他可选规格的库存
            $available_specs_stock = [];
            foreach ($compatible_items as $item) {
                $item_spec_ids = explode(',', $item['spec_value_ids']);
                foreach ($item_spec_ids as $spec_value_id) {
                    // 只处理非已选的规格
                    if (!in_array($spec_value_id, $selected_value_ids)) {
                        if (!isset($available_specs_stock[$spec_value_id])) {
                            $available_specs_stock[$spec_value_id] = 0;
                        }
                        $available_specs_stock[$spec_value_id] += $item['stock'];
                    }
                }
            }

            if (empty($available_specs_stock)) {
                return []; // 没有其他可搭配的规格了
            }

            // 4. 获取这些可用规格的详细信息
            $available_spec_ids = array_keys($available_specs_stock);
            $spec_details = Db::name('goods_spec_value')->alias('gsv')
                ->join('goods_spec gs', 'gsv.spec_id = gs.id')
                ->whereIn('gsv.id', $available_spec_ids)
                ->field('gsv.id, gsv.value, gsv.image, gs.id as spec_id, gs.name as spec_name')
                ->select()
                ->toArray();

            // 5. 整理数据结构，按规格项分组
            $result = [];
            foreach ($spec_details as $detail) {
                $spec_id = $detail['spec_id'];
                $spec_name = $detail['spec_name'];

                // 如果某个规格项已经被用户选择，则不应再出现在返回结果中
                if (in_array($spec_id, $selected_spec_ids)) {
                    continue;
                }

                if (!isset($result[$spec_name])) {
                    $result[$spec_name] = [
                        'name' => $spec_name,
                        'values' => []
                    ];
                }

                $stock = $available_specs_stock[$detail['id']] ?? 0;
                $result[$spec_name]['values'][] = [
                    'id' => $detail['id'],
                    'value' => $detail['value'],
                    'image' => $detail['image'] ? UrlServer::getFileUrl($detail['image']) : '',
                    'stock' => $stock,
                    'is_stock_out' => $stock <= 0,
                ];
            }

            return array_values($result);

        } catch (\Exception $e) {
            self::$error = '查询库存失败: ' . $e->getMessage();
            return false;
        }
    }
}
