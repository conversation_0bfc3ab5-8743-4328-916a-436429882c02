<?php
// +----------------------------------------------------------------------
// | likeshop100%开源免费商用商城系统
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | 开源版本可自由商用，可去除界面版权logo
// | 商业版本务必购买商业授权，以免引起法律纠纷
// | 禁止对系统程序代码以任何目的，任何形式的再发布
// | gitee下载：https://gitee.com/likeshop_gitee
// | github下载：https://github.com/likeshop-github
// | 访问官网：https://www.likeshop.cn
// | 访问社区：https://home.likeshop.cn
// | 访问手册：http://doc.likeshop.cn
// | 微信公众号：likeshop技术社区
// | likeshop团队 版权所有 拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeshopTeam
// +----------------------------------------------------------------------
namespace app\common\enum;
/**
 * 微信错误信息枚举类
 * Class WechatErrorEnum
 * @package app\common\enum
 */
class WechatErrorEnum
{
    //公共错误码
    const COMMONCODE = [
        40001       => '无效accesstoken',
        40014       => '无效accesstoken',
        41001       => 'accesstoken不存在',
        42001       => 'accesstoken过期',
        45009       => '天级别频率限制，2种解决途径2选1: 1.到小程序mp-开发管理 - 接口设置 - 调用额度重置;2.调用限频重置API',
        47001       => '非 json 请求',
        61007       => '小程序尚未将对应的权限集授权给第三方平台',
        61004       => '第三方平台出口 IP 未设置',
        40066       => 'api的 url 不存在（一般情况下填错了）',
        48001       => '小程序未获得该接口权限',
        87014       => '存在违规内容',
        300000      => '瞬时请求频率过高，请降低频率重试',
        990001      => 'OPENID非法',
        990002      => 'OPENID非法',
        990004      => 'OPENID缺失',
        990005      => 'page_size超过上限',
        990007      => '参数缺失',
        990008      => '参数过长',
        990009      => '开始时间比结束时间大',
        990010      => '系统繁忙，请稍后重试',
    ];
    //小程序直播错误码
    const LIVECODE = [
        -1          =>'系统错误',
        1           => '未创建直播间',
        1003        => '商品id不存在',
        47001       => '入参格式不符合规范',
        200002      => '入参错误',
        300001      => '禁止创建/更新商品 或 禁止编辑&更新房间',
        300002      => '名称长度不符合规则',
        300003      => '价格输入不合规（如：现价比原价大、传入价格非数字等）',
        300004      => '商品名称存在违规违法内容',
        300005      => '商品图片存在违规违法内容',
        300006      => '图片上传失败（如：mediaID过期）',
        300007      => '线上小程序版本不存在该链接',
        300008      => '添加商品失败',
        300009      => '商品审核撤回失败',
        300010      => '商品审核状态不对（如：商品审核中）',
        300011      => '操作非法（API不允许操作非 API 创建的商品）',
        300012      => '没有提审额度（每天500次提审额度）',
        300013      => '提审失败',
        300014      => '审核中，无法删除',
        300018      => '商品图片尺寸过大',
        300021      => '商品添加成功，审核失败',
        300022      => '此房间号不存在',
        300023      => '房间状态 拦截（当前房间状态不允许此操作）',
        300024      => '商品不存在',
        300025      => '商品审核未通过',
        300026      => '房间商品数量已经满额',
        300027      => '导入商品失败',
        300028      => '房间名称违规',
        300029      => '主播昵称违规',
        300030      => '主播昵称违规',
        300031      => '直播间封面图不合规',
        300032      => '直播间分享图违规',
        300033      => '添加商品超过直播间上限',
        300034      => '主播微信昵称长度不符合要求',
        300035      => '主播微信号不存在',
        300036      => '主播微信号未实名认证',
        300037      => '购物直播频道封面图不合规',
        300038      => '未在小程序管理后台配置客服',
        300039      => '主播副号微信号不合法',
        300040      => '名称含有非限定字符（含有特殊字符）',
        300041      => '创建者微信号不合法',
        300042      => '推流中禁止编辑房间',
        300043      => '每天只允许一场直播开启关注',
        300044      => '商品没有讲解视频',
        300045      => '讲解视频未生成',
        300046      => '讲解视频生成失败',
        300047      => '已有商品正在推送，请稍后再试',
        300048      => '拉取商品列表失败',
        300049      => '商品推送过程中不允许上下架',
        300050      => '排序商品列表为空',
        300051      => '解析 JSON 出错',
        300052      => '已下架的商品无法推送',
        300053      => '直播间未添加此商品',
        500001      => '副号不合规',
        500002      => '副号未实名',
        500003      => '已经设置过副号了，不能重复设置',
        500004      => '不能设置重复的副号',
        500005      => '副号不能和主号重复',
        600001      => '用户已被添加为小助手',
        600002      => '找不到用户',
        9410000     => '直播间列表为空',
        9410001     => '获取房间失败',
        9410002     => '获取商品失败',
        9410003     => '获取回放失败',

    ];

    /**
     * @notes 获取微信的错误信息
     * @param bool $from
     * @return array|mixed|string
     * @throws \ReflectionException
     * <AUTHOR>
     * @date 2022/6/9 16:42
     */
    public static function wechatErrorMessage($from = true)
    {
        //通过反射拿当前的常量
        $constantsList = (new \ReflectionClass(__CLASS__))->getConstants();
        $errorList = [];
        foreach ($constantsList as $constant) {
            $errorList = $errorList + $constant;
        }
        if (true === $from) {
            return $errorList;
        }
        return $errorList[$from] ?? '';
    }

}