(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-active_list-active_list"],{1526:function(t,e,i){var r=i("24fb");e=r(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.u-divider[data-v-4a6052c7]{width:100%;position:relative;text-align:center;display:flex;flex-direction:row;justify-content:center;align-items:center;overflow:hidden;flex-direction:row}.u-divider-line[data-v-4a6052c7]{border-bottom:1px solid #e4e7ed;-webkit-transform:scaleY(.5);transform:scaleY(.5);-webkit-transform-origin:center;transform-origin:center}.u-divider-line--bordercolor--primary[data-v-4a6052c7]{border-color:#ff2c3c}.u-divider-line--bordercolor--success[data-v-4a6052c7]{border-color:#19be6b}.u-divider-line--bordercolor--error[data-v-4a6052c7]{border-color:#ff2c3c}.u-divider-line--bordercolor--info[data-v-4a6052c7]{border-color:#909399}.u-divider-line--bordercolor--warning[data-v-4a6052c7]{border-color:#f90}.u-divider-text[data-v-4a6052c7]{white-space:nowrap;padding:0 %?16?%;display:inline-flex}',""]),t.exports=e},"49d8":function(t,e,i){var r=i("24fb");e=r(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.swiper-wrap[data-v-5a2d1aa7]{overflow:hidden;box-sizing:initial}.swiper-wrap .swiper-con[data-v-5a2d1aa7]{position:relative;height:100%;overflow:hidden;-webkit-transform:translateY(0);transform:translateY(0)}.swiper-wrap .swiper[data-v-5a2d1aa7]{width:100%;height:100%;position:relative}.swiper-wrap .swiper .slide-image[data-v-5a2d1aa7]{height:100%}.swiper-wrap .dots[data-v-5a2d1aa7]{position:absolute;left:50%;-webkit-transform:translateX(-50%);transform:translateX(-50%);bottom:%?20?%;display:flex}.swiper-wrap .dots .dot[data-v-5a2d1aa7]{width:%?8?%;height:%?8?%;border-radius:50%;margin-right:%?10?%;background-color:#fff}.swiper-wrap .dots .dot.active[data-v-5a2d1aa7]{width:%?16?%;border-radius:%?8?%;background-color:#ff2c3c}',""]),t.exports=e},"53f3":function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var r={data:function(){return{mescroll:null}},onPullDownRefresh:function(){this.mescroll&&this.mescroll.onPullDownRefresh()},onPageScroll:function(t){this.mescroll&&this.mescroll.onPageScroll(t)},onReachBottom:function(){this.mescroll&&this.mescroll.onReachBottom()},methods:{mescrollInit:function(t){console.log(t),this.mescroll=t,this.mescrollInitByRef()},mescrollInitByRef:function(){if(!this.mescroll||!this.mescroll.resetUpScroll){var t=this.$refs.mescrollRef;t&&(this.mescroll=t.mescroll)}},downCallback:function(){var t=this;this.mescroll.optUp.use?this.mescroll.resetUpScroll():setTimeout((function(){t.mescroll.endSuccess()}),500)},upCallback:function(){var t=this;setTimeout((function(){t.mescroll.endErr()}),500)}},mounted:function(){this.mescrollInitByRef()}},n=r;e.default=n},6326:function(t,e,i){"use strict";i.r(e);var r=i("9dbd"),n=i.n(r);for(var a in r)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return r[t]}))}(a);e["default"]=n.a},"6b1f":function(t,e,i){var r=i("f2874");r.__esModule&&(r=r.default),"string"===typeof r&&(r=[[t.i,r,""]]),r.locals&&(t.exports=r.locals);var n=i("4f06").default;n("be45203a",r,!0,{sourceMap:!1,shadowMode:!1})},7655:function(t,e,i){var r=i("49d8");r.__esModule&&(r=r.default),"string"===typeof r&&(r=[[t.i,r,""]]),r.locals&&(t.exports=r.locals);var n=i("4f06").default;n("1dd734b6",r,!0,{sourceMap:!1,shadowMode:!1})},7927:function(t,e,i){"use strict";i.r(e);var r=i("b4db"),n=i("b8de");for(var a in n)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(a);i("a5d1");var o=i("f0c5"),s=Object(o["a"])(n["default"],r["b"],r["c"],!1,null,"7230b8fa",null,!1,r["a"],void 0);e["default"]=s.exports},7930:function(t,e,i){var r=i("1526");r.__esModule&&(r=r.default),"string"===typeof r&&(r=[[t.i,r,""]]),r.locals&&(t.exports=r.locals);var n=i("4f06").default;n("4e33d9f6",r,!0,{sourceMap:!1,shadowMode:!1})},"7a02":function(t,e,i){"use strict";i.r(e);var r=i("c903"),n=i("bb64");for(var a in n)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(a);i("faf1");var o=i("f0c5"),s=Object(o["a"])(n["default"],r["b"],r["c"],!1,null,"4a6052c7",null,!1,r["a"],void 0);e["default"]=s.exports},8186:function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("a9e3"),i("c975");var r={name:"u-divider",props:{halfWidth:{type:[Number,String],default:150},borderColor:{type:String,default:"#dcdfe6"},type:{type:String,default:"primary"},color:{type:String,default:"#909399"},fontSize:{type:[Number,String],default:26},bgColor:{type:String,default:"#ffffff"},height:{type:[Number,String],default:"auto"},marginTop:{type:[String,Number],default:0},marginBottom:{type:[String,Number],default:0},useSlot:{type:Boolean,default:!0}},computed:{lineStyle:function(){var t={};return-1!=String(this.halfWidth).indexOf("%")?t.width=this.halfWidth:t.width=this.halfWidth+"rpx",this.borderColor&&(t.borderColor=this.borderColor),t}},methods:{click:function(){this.$emit("click")}}};e.default=r},"9d0f":function(t,e,i){"use strict";var r=i("7655"),n=i.n(r);n.a},"9dbd":function(t,e,i){"use strict";i("7a82");var r=i("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=r(i("f07e")),a=r(i("c964"));i("a9e3"),i("14d9");var o=i("b550"),s=(i("a5ae"),{data:function(){return{lists:[],currentSwiper:0}},props:{pid:{type:Number},circular:{type:Boolean,default:!0},autoplay:{type:Boolean,default:!0},height:{type:String},radius:{type:String,default:"0"},padding:{type:String,default:"0rpx"},previousMargin:{type:String,default:"0rpx"},isSwiper:{type:Boolean,default:!0}},created:function(){this.getAdListFun()},watch:{pid:function(t){this.getAdListFun()}},methods:{getAdListFun:function(){var t=this;return(0,a.default)((0,n.default)().mark((function e(){var i,r,a;return(0,n.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,(0,o.getAdList)({pid:t.pid,terminal:1});case 2:i=e.sent,r=i.code,a=i.data,1==r&&(t.lists=a);case 6:case"end":return e.stop()}}),e)})))()},swiperChange:function(t){this.currentSwiper=t.detail.current},goPage:function(t){var e=t.link,i=t.link_type,r=t.params,n=t.is_tab;switch(i){case 1:case 2:n?this.$Router.pushTab({path:e}):this.$Router.push({path:e,query:r});break;case 3:this.$Router.push({path:"/pages/webview/webview",query:{url:e}});break}}}});e.default=s},a5d1:function(t,e,i){"use strict";var r=i("6b1f"),n=i.n(r);n.a},b4db:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){return r}));var r={adSwipers:i("d058").default,uDivider:i("7a02").default,goodsList:i("c574").default},n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"hot-list"},[i("mescroll-body",{ref:"mescrollRef",on:{init:function(e){arguments[0]=e=t.$handleEvent(e),t.mescrollInit.apply(void 0,arguments)},down:function(e){arguments[0]=e=t.$handleEvent(e),t.downCallback.apply(void 0,arguments)},up:function(e){arguments[0]=e=t.$handleEvent(e),t.upCallback.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"header"},["hot"==t.type?i("ad-swipers",{attrs:{pid:12,height:"340rpx"}}):t._e(),"new"==t.type?i("ad-swipers",{attrs:{pid:13,height:"340rpx"}}):t._e()],1),i("v-uni-view",{staticClass:"main"},[i("v-uni-view",{staticClass:"list-title flex row-center"},["hot"==t.type?i("u-divider",{attrs:{color:t.colorConfig.normal,"bg-color":"transparent","font-size":38}},[t._v("热销榜单")]):t._e(),"new"==t.type?i("u-divider",{attrs:{color:t.colorConfig.normal,"bg-color":"transparent","font-size":38}},[t._v("新品推荐")]):t._e()],1),i("v-uni-view",{staticClass:"hot"},["hot"==t.type?i("goods-list",{attrs:{list:t.goodsList,type:"hot"}}):t._e()],1),i("v-uni-view",{staticClass:"news"},["new"==t.type?i("goods-list",{attrs:{list:t.goodsList,type:"double"}}):t._e()],1)],1)],1)],1)},a=[]},b8de:function(t,e,i){"use strict";i.r(e);var r=i("e72c"),n=i.n(r);for(var a in r)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return r[t]}))}(a);e["default"]=n.a},bb64:function(t,e,i){"use strict";i.r(e);var r=i("8186"),n=i.n(r);for(var a in r)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return r[t]}))}(a);e["default"]=n.a},c903:function(t,e,i){"use strict";i.d(e,"b",(function(){return r})),i.d(e,"c",(function(){return n})),i.d(e,"a",(function(){}));var r=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"u-divider",style:{height:"auto"==t.height?"auto":t.height+"rpx",backgroundColor:t.bgColor,marginBottom:t.marginBottom+"rpx",marginTop:t.marginTop+"rpx"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.click.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"u-divider-line",class:[t.type?"u-divider-line--bordercolor--"+t.type:""],style:[t.lineStyle]}),t.useSlot?i("v-uni-view",{staticClass:"u-divider-text",style:{color:t.color,fontSize:t.fontSize+"rpx"}},[t._t("default")],2):t._e(),i("v-uni-view",{staticClass:"u-divider-line",class:[t.type?"u-divider-line--bordercolor--"+t.type:""],style:[t.lineStyle]})],1)},n=[]},d058:function(t,e,i){"use strict";i.r(e);var r=i("d930"),n=i("6326");for(var a in n)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(a);i("9d0f");var o=i("f0c5"),s=Object(o["a"])(n["default"],r["b"],r["c"],!1,null,"5a2d1aa7",null,!1,r["a"],void 0);e["default"]=s.exports},d930:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){return r}));var r={uImage:i("f919").default},n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return t.lists.length?i("v-uni-view",{staticClass:"swiper-wrap",style:{height:t.height,padding:t.padding}},[i("v-uni-view",{staticClass:"swiper-con",style:{borderRadius:t.radius}},[t.isSwiper?[i("v-uni-swiper",{staticClass:"swiper",attrs:{autoplay:t.autoplay,circular:t.circular,"previous-margin":t.previousMargin,"display-multiple-items":"1"},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.swiperChange.apply(void 0,arguments)}}},t._l(t.lists,(function(e,r){return i("v-uni-swiper-item",{key:r},[i("v-uni-view",{staticStyle:{width:"100%",height:"100%"},attrs:{"data-item":e},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.goPage(e)}}},[i("u-image",{attrs:{mode:"aspectFill",width:"calc(100% - "+t.previousMargin+")",height:"100%","border-radius":t.radius,src:e.image}})],1)],1)})),1),t.lists.length>1?i("v-uni-view",{staticClass:"dots"},t._l(t.lists,(function(e,r){return i("v-uni-view",{key:r,class:"dot "+(r==t.currentSwiper?"active":"")})})),1):t._e()]:t._e(),t._l(t.lists,(function(e,r){return[r<1?i("v-uni-view",{key:r,staticStyle:{width:"100%",height:"100%"},attrs:{"data-item":e},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.goPage(e)}}},[i("u-image",{attrs:{mode:"aspectFill",width:"calc(100% - "+t.previousMargin+")",height:"100%","border-radius":t.radius,src:e.image}})],1):t._e()]}))],2)],1):t._e()},a=[]},e72c:function(t,e,i){"use strict";i("7a82");var r=i("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("99af");var n=i("b550"),a=r(i("53f3")),o={mixins:[a.default],data:function(){return{upOption:{empty:{icon:"/static/images/goods_null.png",tip:"暂无商品～"}},goodsList:[],type:""}},onLoad:function(){switch(this.type=this.$Route.query.type,this.type){case"hot":uni.setNavigationBarTitle({title:"热销榜单"});break;case"new":uni.setNavigationBarTitle({title:"新品推荐"});break}},methods:{upCallback:function(t){var e=this,i=t.num,r=t.size,a={page_no:i,page_size:r};switch(this.type){case"hot":a.sort_by_sales="desc";break;case"new":a.sort_by_create="desc";break}(0,n.getGoodsList)(a).then((function(i){if(1==i.code){var r=i.data.lists,n=r.length,a=!!i.data.more;1==t.num&&(e.goodsList=[]),e.goodsList=e.goodsList.concat(r),e.mescroll.endSuccess(n,a)}})).catch((function(t){e.mescroll.endErr()}))}}};e.default=o},f2874:function(t,e,i){var r=i("24fb");e=r(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.hot-list .main .list-title[data-v-7230b8fa]{margin:%?27?% 0 %?30?%}.hot-list .main .hot[data-v-7230b8fa]{padding:0 %?30?%}',""]),t.exports=e},faf1:function(t,e,i){"use strict";var r=i("7930"),n=i.n(r);n.a}}]);