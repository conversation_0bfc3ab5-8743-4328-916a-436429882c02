(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["bundle_b-pages-community_user_profile-community_user_profile"],{"0a51":function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"c",(function(){return o})),n.d(e,"a",(function(){return i}));var i={uUpload:n("aae9").default,uIcon:n("90f3").default},a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{staticClass:"user-profile"},[n("v-uni-view",{staticClass:"form bg-white m-t-20"},[n("v-uni-view",{staticClass:"form--item bb"},[n("v-uni-view",{staticClass:"label normal nr"},[t._v("签名")]),n("v-uni-view",{staticClass:"flex-1"},[n("v-uni-input",{attrs:{type:"text",placeholder:"请输入个性签名"},model:{value:t.formData.signature,callback:function(e){t.$set(t.formData,"signature",e)},expression:"formData.signature"}})],1)],1),n("v-uni-view",{staticClass:"form--item "},[n("v-uni-view",{staticClass:"label normal nr"},[t._v("背景")]),n("v-uni-view",{staticClass:"flex-1"},[n("u-upload",{ref:"upload",attrs:{action:t.action,header:{token:t.token,version:t.version},deletable:!0,"max-count":1,"custom-btn":!0,width:264,height:200,"file-list":t.fileList,"show-progress":!1},on:{"on-change":function(e){arguments[0]=e=t.$handleEvent(e),t.change.apply(void 0,arguments)},"on-remove":function(e){arguments[0]=e=t.$handleEvent(e),t.remove.apply(void 0,arguments)}}},[n("v-uni-view",{staticClass:"uplader-upload",attrs:{slot:"addBtn","hover-class":"slot-btn__hover","hover-stay-time":"150"},slot:"addBtn"},[n("u-icon",{attrs:{size:"48",color:"#dcdee0",name:"camera"}}),n("v-uni-view",{staticClass:"xs m-t-10"},[t._v("上传背景图")])],1)],1)],1)],1)],1),n("v-uni-view",{staticClass:"footer"},[n("v-uni-button",{staticClass:"bg-primary br60 white lg",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onSubmit.apply(void 0,arguments)}}},[t._v("确定")])],1)],1)},o=[]},"45ac":function(t,e,n){"use strict";n.r(e);var i=n("0a51"),a=n("9815");for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);n("8c6d");var u=n("f0c5"),r=Object(u["a"])(a["default"],i["b"],i["c"],!1,null,"056e761a",null,!1,i["a"],void 0);e["default"]=r.exports},"84cd":function(t,e,n){"use strict";n("7a82");var i=n("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=i(n("f3f3"));n("d81d"),n("b64b");var o=i(n("c094")),u=n("fe5f"),r=(n("b08d"),n("aa80")),s={data:function(){return{action:"",token:"",version:u.version,formData:{signature:"",image:""}}},computed:{fileList:function(){var t=this.formData.image;return t?[{url:t}]:[]}},onLoad:function(){this.action=u.baseURL+"/api/file/formimage",this.token=o.default.getters.token,this.initCommunitySetting()},methods:{initCommunitySetting:function(){var t=this;(0,r.getCommunitySetting)().then((function(e){Object.keys(t.formData).map((function(n){t.$set(t.formData,n,e.data[n])}))}))},change:function(t){this.$toast({title:JSON.parse(t.data).msg}),1==JSON.parse(t.data).code&&(this.formData.image=JSON.parse(t.data).data.uri)},remove:function(t){this.formData.image=""},onSubmit:function(){var t=this;(0,r.apiCommunitySetSetting)((0,a.default)({},this.formData)).then((function(e){t.$toast({title:e.msg}),setTimeout((function(){return t.$Router.back()}),500)}))}}};e.default=s},8749:function(t,e,n){var i=n("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.user-profile .bb[data-v-056e761a]{border-bottom:1px solid #f2f2f2}.user-profile .form[data-v-056e761a]{padding-left:%?24?%}.user-profile .form--item[data-v-056e761a]{display:flex;padding:%?30?% 0}.user-profile .form--item .label[data-v-056e761a]{width:%?100?%}.user-profile .uplader-upload[data-v-056e761a]{position:relative;width:%?264?%;height:%?200?%;padding-top:%?40?%;text-align:center;margin:%?11?%;border:2px dashed #e5e5e5;background-color:#fff}.user-profile .uplader-upload > uni-view[data-v-056e761a]{color:#bbb}.user-profile .footer[data-v-056e761a]{padding:%?50?% %?26?%}',""]),t.exports=e},"8c6d":function(t,e,n){"use strict";var i=n("f8f4"),a=n.n(i);a.a},9815:function(t,e,n){"use strict";n.r(e);var i=n("84cd"),a=n.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);e["default"]=a.a},aa80:function(t,e,n){"use strict";n("7a82");var i=n("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.apiCommunityAdd=function(t){return a.default.post("community/addArticle",t)},e.apiCommunityClearSearchHistory=function(){return a.default.post("community_search/clear")},e.apiCommunityCommentAdd=function(t){return a.default.post("community_comment/add",t)},e.apiCommunityCommentLike=function(t){return a.default.post("community/giveLike",t)},e.apiCommunityDel=function(t){return a.default.post("community/delArticle",t)},e.apiCommunityEdit=function(t){return a.default.post("community/editArticle",t)},e.apiCommunityFollow=function(t){return a.default.post("community/follow",t)},e.apiCommunitySetSetting=function(t){return a.default.post("community_user/setSetting",t)},e.getCommunityArticleLists=function(t){return a.default.get("community/articleLists",{params:t})},e.getCommunityCate=function(){return a.default.get("community/cate")},e.getCommunityCommentChildLists=function(t){return a.default.get("community_comment/commentChild",{params:t})},e.getCommunityCommentLists=function(t){return a.default.get("community_comment/lists",{params:t})},e.getCommunityDetail=function(t){return a.default.get("community/detail",{params:t})},e.getCommunityFollow=function(t){return a.default.get("community/followArticle",{params:t})},e.getCommunityGoods=function(t){return a.default.get("community/goods",{params:t})},e.getCommunityGoodsLists=function(t){return a.default.get("community/relationGoods",{params:t})},e.getCommunityLikeLists=function(t){return a.default.get("community/likeLists",{params:t})},e.getCommunityRecommendTopic=function(){return a.default.get("community/recommendTopic")},e.getCommunitySearchHistory=function(){return a.default.get("community_search/lists")},e.getCommunitySetting=function(){return a.default.get("community_user/getSetting")},e.getCommunityShop=function(t){return a.default.get("community/shop",{params:t})},e.getCommunityShopLists=function(t){return a.default.get("community/relationShop",{params:t})},e.getCommunityTopicArticle=function(t){return a.default.get("community/topicArticle",{params:t})},e.getCommunityTopicLists=function(t){return a.default.get("community/topicLists",{params:t})},e.getCommunityUserCenter=function(t){return a.default.get("community_user/center",{params:t})},e.getCommunityWorksLists=function(t){return a.default.get("community/worksLists",{params:t})};var a=i(n("3b33"))},f8f4:function(t,e,n){var i=n("8749");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var a=n("4f06").default;a("c9365930",i,!0,{sourceMap:!1,shadowMode:!1})}}]);