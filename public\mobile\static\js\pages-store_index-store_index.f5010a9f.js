(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-store_index-store_index"],{"00dd":function(t,e,n){"use strict";n("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i={data:function(){return{}},props:{type:{type:String,default:"fixed"},backgroundColor:{type:String,default:"#fff"}},methods:{}};e.default=i},"017b":function(t,e,n){"use strict";var i=n("048f"),a=n.n(i);a.a},"048f":function(t,e,n){var i=n("5c72");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var a=n("4f06").default;a("14f65568",i,!0,{sourceMap:!1,shadowMode:!1})},"0aff":function(t,e,n){"use strict";n.r(e);var i=n("00dd"),a=n.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);e["default"]=a.a},1034:function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"c",(function(){return o})),n.d(e,"a",(function(){return i}));var i={uIcon:n("6976").default},a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{},[n("v-uni-view",{staticClass:"u-navbar",class:{"u-navbar-fixed":t.isFixed,"u-border-bottom":t.borderBottom},style:[t.navbarStyle]},[n("v-uni-view",{staticClass:"u-status-bar",style:{height:t.statusBarHeight+"px"}}),n("v-uni-view",{staticClass:"u-navbar-inner",style:[t.navbarInnerStyle]},[t.isBack?n("v-uni-view",{staticClass:"u-back-wrap",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.goBack.apply(void 0,arguments)}}},[n("v-uni-view",{staticClass:"u-icon-wrap",style:{backgroundColor:t.backBg,borderRadius:"50%",padding:"8rpx"}},[n("u-icon",{attrs:{name:t.isHome?"home":t.backIconName,color:t.backIconColor,size:t.backIconSize}})],1),t.backText?n("v-uni-view",{staticClass:"u-icon-wrap u-back-text u-line-1",style:[t.backTextStyle]},[t._v(t._s(t.backText))]):t._e()],1):t._e(),t.title?n("v-uni-view",{staticClass:"u-navbar-content-title",style:[t.titleStyle]},[n("v-uni-view",{staticClass:"u-title u-line-1",style:{color:t.titleColor,fontSize:t.titleSize+"rpx",fontWeight:t.titleBold?"bold":"normal"}},[t._v(t._s(t.title))])],1):t._e(),n("v-uni-view",{staticClass:"u-slot-content"},[t._t("default")],2),n("v-uni-view",{staticClass:"u-slot-right"},[t._t("right")],2)],1)],1),t.isFixed&&!t.immersive?n("v-uni-view",{staticClass:"u-navbar-placeholder",style:{width:"100%",height:Number(t.navbarHeight)+t.statusBarHeight+"px"}}):t._e()],1)},o=[]},1080:function(t,e,n){"use strict";n.r(e);var i=n("8670"),a=n("bd8c");for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);n("017b");var r=n("f0c5"),s=Object(r["a"])(a["default"],i["b"],i["c"],!1,null,"e9923850",null,!1,i["a"],void 0);e["default"]=s.exports},"10e5":function(t,e,n){"use strict";n.r(e);var i=n("c54b"),a=n("b1fc");for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);n("9177");var r=n("f0c5"),s=Object(r["a"])(a["default"],i["b"],i["c"],!1,null,"9473640e",null,!1,i["a"],void 0);e["default"]=s.exports},1309:function(t,e,n){"use strict";n.r(e);var i=n("4528"),a=n("6e8b");for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);n("ad39");var r=n("f0c5"),s=Object(r["a"])(a["default"],i["b"],i["c"],!1,null,"53597a42",null,!1,i["a"],void 0);e["default"]=s.exports},1723:function(t,e,n){var i=n("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.loading[data-v-061dd044]{position:fixed;top:0;left:0;width:100vw;height:100vh;z-index:9999;display:flex;justify-content:center;align-items:center}.loading.flex[data-v-061dd044]{position:static;flex:1;width:100%}.loading .loading-img[data-v-061dd044]{width:%?100?%;height:%?100?%}',""]),t.exports=e},"1d4d":function(t,e,n){"use strict";var i=n("f1ff"),a=n.n(i);a.a},"1de5b":function(t,e,n){"use strict";t.exports=function(t,e){return e||(e={}),t=t&&t.__esModule?t.default:t,"string"!==typeof t?t:(/^['"].*['"]$/.test(t)&&(t=t.slice(1,-1)),e.hash&&(t+=e.hash),/["'() \t\n]/.test(t)||e.needQuotes?'"'.concat(t.replace(/"/g,'\\"').replace(/\n/g,"\\n"),'"'):t)}},"212e":function(t,e,n){var i=n("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.u-loading-circle[data-v-bf7076f2]{display:inline-flex;vertical-align:middle;width:%?28?%;height:%?28?%;background:0 0;border-radius:50%;border:2px solid;border-color:#e5e5e5 #e5e5e5 #e5e5e5 #8f8d8e;-webkit-animation:u-circle-data-v-bf7076f2 1s linear infinite;animation:u-circle-data-v-bf7076f2 1s linear infinite}.u-loading-flower[data-v-bf7076f2]{width:20px;height:20px;display:inline-block;vertical-align:middle;-webkit-animation:a 1s steps(12) infinite;animation:u-flower-data-v-bf7076f2 1s steps(12) infinite;background:transparent url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMjAiIGhlaWdodD0iMTIwIiB2aWV3Qm94PSIwIDAgMTAwIDEwMCI+PHBhdGggZmlsbD0ibm9uZSIgZD0iTTAgMGgxMDB2MTAwSDB6Ii8+PHJlY3Qgd2lkdGg9IjciIGhlaWdodD0iMjAiIHg9IjQ2LjUiIHk9IjQwIiBmaWxsPSIjRTlFOUU5IiByeD0iNSIgcnk9IjUiIHRyYW5zZm9ybT0idHJhbnNsYXRlKDAgLTMwKSIvPjxyZWN0IHdpZHRoPSI3IiBoZWlnaHQ9IjIwIiB4PSI0Ni41IiB5PSI0MCIgZmlsbD0iIzk4OTY5NyIgcng9IjUiIHJ5PSI1IiB0cmFuc2Zvcm09InJvdGF0ZSgzMCAxMDUuOTggNjUpIi8+PHJlY3Qgd2lkdGg9IjciIGhlaWdodD0iMjAiIHg9IjQ2LjUiIHk9IjQwIiBmaWxsPSIjOUI5OTlBIiByeD0iNSIgcnk9IjUiIHRyYW5zZm9ybT0icm90YXRlKDYwIDc1Ljk4IDY1KSIvPjxyZWN0IHdpZHRoPSI3IiBoZWlnaHQ9IjIwIiB4PSI0Ni41IiB5PSI0MCIgZmlsbD0iI0EzQTFBMiIgcng9IjUiIHJ5PSI1IiB0cmFuc2Zvcm09InJvdGF0ZSg5MCA2NSA2NSkiLz48cmVjdCB3aWR0aD0iNyIgaGVpZ2h0PSIyMCIgeD0iNDYuNSIgeT0iNDAiIGZpbGw9IiNBQkE5QUEiIHJ4PSI1IiByeT0iNSIgdHJhbnNmb3JtPSJyb3RhdGUoMTIwIDU4LjY2IDY1KSIvPjxyZWN0IHdpZHRoPSI3IiBoZWlnaHQ9IjIwIiB4PSI0Ni41IiB5PSI0MCIgZmlsbD0iI0IyQjJCMiIgcng9IjUiIHJ5PSI1IiB0cmFuc2Zvcm09InJvdGF0ZSgxNTAgNTQuMDIgNjUpIi8+PHJlY3Qgd2lkdGg9IjciIGhlaWdodD0iMjAiIHg9IjQ2LjUiIHk9IjQwIiBmaWxsPSIjQkFCOEI5IiByeD0iNSIgcnk9IjUiIHRyYW5zZm9ybT0icm90YXRlKDE4MCA1MCA2NSkiLz48cmVjdCB3aWR0aD0iNyIgaGVpZ2h0PSIyMCIgeD0iNDYuNSIgeT0iNDAiIGZpbGw9IiNDMkMwQzEiIHJ4PSI1IiByeT0iNSIgdHJhbnNmb3JtPSJyb3RhdGUoLTE1MCA0NS45OCA2NSkiLz48cmVjdCB3aWR0aD0iNyIgaGVpZ2h0PSIyMCIgeD0iNDYuNSIgeT0iNDAiIGZpbGw9IiNDQkNCQ0IiIHJ4PSI1IiByeT0iNSIgdHJhbnNmb3JtPSJyb3RhdGUoLTEyMCA0MS4zNCA2NSkiLz48cmVjdCB3aWR0aD0iNyIgaGVpZ2h0PSIyMCIgeD0iNDYuNSIgeT0iNDAiIGZpbGw9IiNEMkQyRDIiIHJ4PSI1IiByeT0iNSIgdHJhbnNmb3JtPSJyb3RhdGUoLTkwIDM1IDY1KSIvPjxyZWN0IHdpZHRoPSI3IiBoZWlnaHQ9IjIwIiB4PSI0Ni41IiB5PSI0MCIgZmlsbD0iI0RBREFEQSIgcng9IjUiIHJ5PSI1IiB0cmFuc2Zvcm09InJvdGF0ZSgtNjAgMjQuMDIgNjUpIi8+PHJlY3Qgd2lkdGg9IjciIGhlaWdodD0iMjAiIHg9IjQ2LjUiIHk9IjQwIiBmaWxsPSIjRTJFMkUyIiByeD0iNSIgcnk9IjUiIHRyYW5zZm9ybT0icm90YXRlKC0zMCAtNS45OCA2NSkiLz48L3N2Zz4=) no-repeat;background-size:100%}@-webkit-keyframes u-flower-data-v-bf7076f2{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}@keyframes u-flower-data-v-bf7076f2{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}@-webkit-keyframes u-circle-data-v-bf7076f2{0%{-webkit-transform:rotate(0);transform:rotate(0)}100%{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}',""]),t.exports=e},2251:function(t,e,n){"use strict";n("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("a9e3");var i=uni.getSystemInfoSync(),a={},o={name:"u-navbar",props:{height:{type:[String,Number],default:""},backIconColor:{type:String,default:"#606266"},backBg:{type:String,default:"transparent"},backIconName:{type:String,default:"nav-back"},backIconSize:{type:[String,Number],default:"42"},backText:{type:String,default:""},backTextStyle:{type:Object,default:function(){return{color:"#606266"}}},title:{type:String,default:""},titleWidth:{type:[String,Number],default:"250"},titleColor:{type:String,default:"#606266"},titleBold:{type:Boolean,default:!1},titleSize:{type:[String,Number],default:32},isBack:{type:[Boolean,String],default:!0},background:{type:Object,default:function(){return{background:"#ffffff"}}},isFixed:{type:Boolean,default:!0},immersive:{type:Boolean,default:!1},borderBottom:{type:Boolean,default:!0},zIndex:{type:[String,Number],default:""},customBack:{type:Function,default:null}},data:function(){return{menuButtonInfo:a,statusBarHeight:i.statusBarHeight,isHome:!1}},computed:{navbarInnerStyle:function(){var t={};return t.height=this.navbarHeight+"px",t},navbarStyle:function(){var t={};return t.zIndex=this.zIndex?this.zIndex:this.$u.zIndex.navbar,Object.assign(t,this.background),t},titleStyle:function(){var t={};return t.left=(i.windowWidth-uni.upx2px(this.titleWidth))/2+"px",t.right=(i.windowWidth-uni.upx2px(this.titleWidth))/2+"px",t.width=uni.upx2px(this.titleWidth)+"px",t},navbarHeight:function(){return this.height?this.height:44}},created:function(){var t=getCurrentPages().length;1==t&&(this.isHome=!0)},methods:{goBack:function(){"function"===typeof this.customBack?this.customBack.bind(this.$u.$parent.call(this))():this.isHome?uni.switchTab({url:"/pages/index/index"}):uni.navigateBack()}}};e.default=o},"238c":function(t,e,n){var i=n("1723");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var a=n("4f06").default;a("20834b9a",i,!0,{sourceMap:!1,shadowMode:!1})},"23c4":function(t,e,n){var i=n("24fb"),a=n("1de5b"),o=n("27d5"),r=n("b1e5");e=i(!1);var s=a(o),c=a(r);e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.coupon-list[data-v-fa6b581a]{padding:0 %?24?%;overflow:hidden}.coupon-list .coupon-item[data-v-fa6b581a]{position:relative;height:%?200?%;background-image:url('+s+");background-size:100% 100%}.coupon-list .coupon-item.gray[data-v-fa6b581a]{background-image:url("+c+")}.coupon-list .coupon-item.gray .btn.plain[data-v-fa6b581a]{color:#ccc}.coupon-list .coupon-item .price[data-v-fa6b581a]{width:%?200?%}.coupon-list .coupon-item .btn[data-v-fa6b581a]{line-height:%?52?%;height:%?52?%;position:absolute;right:%?20?%;bottom:%?20?%;width:%?120?%;text-align:center;padding:0}.coupon-list .coupon-item .btn.plain[data-v-fa6b581a]{background-color:#fff;color:#ff2c3c;border:1px solid currentColor}.coupon-list .coupon-item .receive[data-v-fa6b581a]{position:absolute;right:%?30?%;top:%?0?%;width:%?99?%;height:%?77?%}.coupon-list .icon[data-v-fa6b581a]{transition:all .4s}.coupon-list .rotate[data-v-fa6b581a]{-webkit-transform:rotate(-180deg);transform:rotate(-180deg)}.coupon-list .received[data-v-fa6b581a]{position:absolute;top:0;right:%?50?%;width:%?70?%;height:%?50?%}",""]),t.exports=e},"27d5":function(t,e,n){t.exports=n.p+"static/images/coupon_bg.png"},3219:function(t,e,n){"use strict";n.r(e);var i=n("cf13"),a=n.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);e["default"]=a.a},"3aff":function(t,e,n){var i=n("23c4");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var a=n("4f06").default;a("1f15a740",i,!0,{sourceMap:!1,shadowMode:!1})},"3e54":function(t,e,n){t.exports=n.p+"static/images/received.png"},"3f98":function(t,e,n){"use strict";n.r(e);var i=n("ac8b"),a=n("3219");for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);n("610e");var r=n("f0c5"),s=Object(r["a"])(a["default"],i["b"],i["c"],!1,null,"fa6b581a",null,!1,i["a"],void 0);e["default"]=s.exports},4528:function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"c",(function(){return o})),n.d(e,"a",(function(){return i}));var i={shopStop:n("10e5").default,uNavbar:n("53a6").default,uSearch:n("5744").default,uImage:n("f919").default,uIcon:n("6976").default,uTabs:n("1704").default,uSwiper:n("d996").default,goodsList:n("c574").default,uSticky:n("1080").default,sortNav:n("3055").default,couponList:n("3f98").default,loadingView:n("dbb2").default,uTabbar:n("9644").default},a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",[1===t.shopInfo.is_freeze||1===t.shopInfo.is_expire||0===t.shopInfo.is_run?n("v-uni-view",[n("shop-stop",{attrs:{is_freeze:t.shopInfo.is_freeze,is_expire:t.shopInfo.is_expire,is_run:t.shopInfo.is_run}})],1):n("v-uni-view",{staticClass:"store-index"},[n("v-uni-view",{staticClass:"store-header",style:{"background-image":"url("+(t.shopInfo.background?t.shopInfo.background:t.$getImageUri("/static/common/image/default/shopindex.png"))+")"}},[n("u-navbar",{attrs:{"border-bottom":!1,background:{"background-image":"url("+t.shopInfo.background+")","background-repeat":"no-repeat","background-size":"100% auto"},"back-bg":"rgba(255, 255, 255, 0.45)","back-icon-color":"rgb(255,255,255)","is-fixed":!0}},[n("v-uni-view",{staticClass:"store-search flex-1",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.handleSerach.apply(void 0,arguments)}}},[n("u-search",{attrs:{"bg-color":"white",shape:"round",placeholder:"搜索店内商品","wrap-bg-color":"transparent"},on:{search:function(e){arguments[0]=e=t.$handleEvent(e),t.refresh.apply(void 0,arguments)}},model:{value:t.keyword,callback:function(e){t.keyword=e},expression:"keyword"}})],1)],1),n("v-uni-view",{staticClass:"store-info m-t-20",style:{"padding-bottom":(t.tabbarcurrent,"200rpx")}},[n("v-uni-view",{staticClass:"flex row-between"},[n("v-uni-view",{staticClass:"flex"},[n("u-image",{attrs:{width:"100rpx",height:"100rpx","border-radius":"50%",src:t.shopInfo.logo}}),n("v-uni-view",{staticClass:"m-l-20 flex-col col-top"},[n("router-link",{attrs:{to:{path:"/pages/store_detail/store_detail",query:{id:t.shopInfo.id}}}},[n("v-uni-view",{staticClass:"lg white line-1 bold",staticStyle:{width:"420rpx"}},[t._v(t._s(t.shopInfo.name)),n("u-icon",{attrs:{name:"arrow-right"}})],1)],1),n("v-uni-view",{staticClass:"flex m-t-10"},[1==t.shopInfo.type?n("v-uni-view",{staticClass:"store-tag xxs white"},[t._v("自营")]):t._e(),n("v-uni-view",{staticClass:"white xs m-l-5"},[t._v(t._s(t.shopInfo.visited_num)+"人进店")])],1)],1)],1),n("v-uni-view",{staticClass:"flex row-center white br60 sm flex-none subscribe-btn",class:{gray:1===t.shopInfo.shop_follow_status},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.changeShopFollowFun.apply(void 0,arguments)}}},[t._v(t._s(1===t.shopInfo.shop_follow_status?"已关注":"+ 关注"))])],1),0==t.tabbarcurrent?n("v-uni-view",{staticClass:"p-t-20 p-b-20"},[n("u-tabs",{attrs:{current:t.tabcurrent,list:[{name:"精选"},{name:"商品"},{name:"领券"}],"is-scroll":!1,"bg-color":"transparent","active-color":"white","inactive-color":"white"},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.tabChange.apply(void 0,arguments)}}})],1):t._e()],1)],1),0==t.tabcurrent&&0==t.tabbarcurrent?n("v-uni-view",{staticClass:"content"},[t.shopInfo.ad.mobile.length?n("v-uni-view",[n("u-swiper",{attrs:{list:t.shopInfo.ad.mobile,height:"700"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.handleAdclick.apply(void 0,arguments)}}})],1):t._e(),t.shopInfo.goods_list.length?n("v-uni-view",{staticClass:"content-wrap"},[n("v-uni-view",{staticClass:"store-hot-goods bg-white m-t-20"},[n("v-uni-view",{staticClass:"store-hot-header flex"},[n("v-uni-view",{staticClass:"column-line m-r-20"}),n("v-uni-view",{staticClass:"md",staticStyle:{"font-weight":"500"}},[t._v("店铺推荐")]),n("v-uni-view",{staticClass:"md",staticStyle:{"margin-left":"auto"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.handlemore.apply(void 0,arguments)}}},[t._v("更多")])],1),n("v-uni-view",{staticClass:"store-hot-content"},[n("v-uni-scroll-view",{attrs:{"scroll-x":!0}},[n("v-uni-view",{staticClass:"goods p-l-20 p-r-20"},[n("goods-list",{attrs:{list:t.shopInfo.goods_list,type:"row"}})],1)],1)],1)],1)],1):t._e(),n("v-uni-view",{staticClass:"goods-display flex bg-body"},[n("v-uni-view",{staticClass:"category-row flex-1"},[n("u-sticky",{attrs:{"bg-color":"rgba(255, 255, 255, 0)",enable:t.enableFix,"offset-top":t.navHeight,"h5-nav-height":0}},[n("sort-nav",{attrs:{"show-type":!1,showCreate:!0},model:{value:t.sortConfig,callback:function(e){t.sortConfig=e},expression:"sortConfig"}})],1),n("mescroll-body",{ref:"mescrollRef",attrs:{height:t.meScrollH,up:t.upOption,down:{use:!1}},on:{init:function(e){arguments[0]=e=t.$handleEvent(e),t.mescrollInit.apply(void 0,arguments)},down:function(e){arguments[0]=e=t.$handleEvent(e),t.downCallback.apply(void 0,arguments)},up:function(e){arguments[0]=e=t.$handleEvent(e),t.upCallback.apply(void 0,arguments)}}},[n("v-uni-view",{staticClass:"bg-white"},[n("goods-list",{attrs:{list:t.goodsList,width:"315rpx"}})],1)],1)],1)],1)],1):t._e(),1==t.tabcurrent&&0==t.tabbarcurrent?n("v-uni-view",{staticStyle:{"margin-top":"-200rpx"}},[n("v-uni-view",{staticClass:"goods-display flex bg-body"},[n("v-uni-view",{staticClass:"category-row flex-1"},[n("u-sticky",{attrs:{"bg-color":"rgba(255, 255, 255, 0)",enable:t.enableFix,"offset-top":t.navHeight,"h5-nav-height":0}},[n("sort-nav",{attrs:{"show-type":!1},model:{value:t.sortConfig,callback:function(e){t.sortConfig=e},expression:"sortConfig"}})],1),n("mescroll-body",{ref:"mescrollRef",attrs:{height:t.meScrollH,up:t.upOption,down:{use:!1}},on:{init:function(e){arguments[0]=e=t.$handleEvent(e),t.mescrollInit.apply(void 0,arguments)},down:function(e){arguments[0]=e=t.$handleEvent(e),t.downCallback.apply(void 0,arguments)},up:function(e){arguments[0]=e=t.$handleEvent(e),t.upCallback.apply(void 0,arguments)}}},[n("v-uni-view",{staticClass:"bg-white"},[n("goods-list",{attrs:{list:t.goodsList}})],1)],1)],1)],1)],1):t._e(),2==t.tabcurrent&&0==t.tabbarcurrent?n("v-uni-view",{staticStyle:{"margin-top":"-200rpx"}},[t.couponlists.length?n("coupon-list",{attrs:{list:t.couponlists,"btn-type":3},on:{refresh:function(e){arguments[0]=e=t.$handleEvent(e),t.getGoodsCouponFun.apply(void 0,arguments)}}}):n("v-uni-view",{staticClass:"coupon-container"},[n("v-uni-view",{staticStyle:{"font-weight":"500"}},[t._v("优惠券")]),n("v-uni-view",{staticClass:"muted coupon"},[t._v("当前暂无可领取的优惠券")])],1)],1):t._e(),1==t.tabbarcurrent?n("v-uni-view",{staticClass:"category"},[n("v-uni-view",{staticClass:"goods-display bg-body",staticStyle:{display:"flex","border-radius":"20rpx"}},[n("v-uni-view",{staticClass:"category-aside"},[n("u-sticky",{attrs:{"bg-color":"rgba(255, 255, 255, 0)",enable:t.enableFix,"offset-top":t.navHeight,"h5-nav-height":0}},[n("v-uni-scroll-view",{style:{height:t.aslideH+"px",padding:"10rpx"},attrs:{"scroll-y":!0}},[n("v-uni-view",{staticClass:"aside-item flex row-center",class:{active:-1==t.active},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.changeActive(-1)}}},[n("v-uni-view",{staticClass:"xs text-center",class:{primary:-1==t.active},staticStyle:{width:"60rpx"}},[t._v("全部商品")])],1),t._l(t.category,(function(e,i){return n("v-uni-view",{key:i,staticClass:"aside-item flex row-center",class:{active:t.active==i},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.changeActive(i)}}},[n("v-uni-view",{staticClass:"xs text-center",class:{primary:t.active==i},staticStyle:{width:"60rpx"}},[t._v(t._s(e.name))])],1)}))],2)],1)],1),n("v-uni-view",{staticClass:"category-row flex-1"},[n("mescroll-body",{ref:"mescrollRef",attrs:{height:t.meScrollH,up:t.upOption,down:{use:!1}},on:{init:function(e){arguments[0]=e=t.$handleEvent(e),t.mescrollInit.apply(void 0,arguments)},down:function(e){arguments[0]=e=t.$handleEvent(e),t.downCallback.apply(void 0,arguments)},up:function(e){arguments[0]=e=t.$handleEvent(e),t.upCallback.apply(void 0,arguments)}}},[n("v-uni-view",{staticClass:"bg-white"},[n("goods-list",{attrs:{list:t.goodsList,type:"one"}})],1)],1)],1)],1)],1):t._e(),t.isFirstLoading?n("loading-view"):t._e()],1),n("u-tabbar",{attrs:{list:t.tabbarLists,"active-color":"#1679fc"},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.tabbarChange.apply(void 0,arguments)}},model:{value:t.tabbarcurrent,callback:function(e){t.tabbarcurrent=e},expression:"tabbarcurrent"}})],1)},o=[]},"4b4a":function(t,e,n){var i=n("e179");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var a=n("4f06").default;a("973711c2",i,!0,{sourceMap:!1,shadowMode:!1})},"53a6":function(t,e,n){"use strict";n.r(e);var i=n("1034"),a=n("ce5f");for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);n("73cb");var r=n("f0c5"),s=Object(r["a"])(a["default"],i["b"],i["c"],!1,null,"6d93ee5a",null,!1,i["a"],void 0);e["default"]=s.exports},"53f3":function(t,e,n){"use strict";n("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i={data:function(){return{mescroll:null}},onPullDownRefresh:function(){this.mescroll&&this.mescroll.onPullDownRefresh()},onPageScroll:function(t){this.mescroll&&this.mescroll.onPageScroll(t)},onReachBottom:function(){this.mescroll&&this.mescroll.onReachBottom()},methods:{mescrollInit:function(t){console.log(t),this.mescroll=t,this.mescrollInitByRef()},mescrollInitByRef:function(){if(!this.mescroll||!this.mescroll.resetUpScroll){var t=this.$refs.mescrollRef;t&&(this.mescroll=t.mescroll)}},downCallback:function(){var t=this;this.mescroll.optUp.use?this.mescroll.resetUpScroll():setTimeout((function(){t.mescroll.endSuccess()}),500)},upCallback:function(){var t=this;setTimeout((function(){t.mescroll.endErr()}),500)}},mounted:function(){this.mescrollInitByRef()}},a=i;e.default=a},"5c72":function(t,e,n){var i=n("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.u-sticky[data-v-e9923850]{z-index:9999999999}',""]),t.exports=e},"60a1":function(t,e,n){"use strict";n("7a82");var i=n("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("99af"),n("14d9");var a=i(n("f3f3")),o=i(n("f07e")),r=i(n("c964")),s=n("26cb"),c=n("7161"),u=(n("9e3f"),n("b550")),l=i(n("53f3")),d=n("b0cc"),f={mixins:[l.default],data:function(){return{tabbarcurrent:0,tabcurrent:0,isFirstLoading:!0,navOpacity:0,shopInfo:{goods_list:[],ad:{mobile:[]}},category:[],active:-1,upOption:{auto:!1,empty:{icon:"/static/images/goods_null.png",tip:"暂无商品"}},goodsList:[],categoodsList:[],sortConfig:{priceSort:"",saleSort:"",sort_by_create:""},couponlists:[],keyword:"",sys:{},enableFix:!0,id:"",shopLive:{},tabbarLists:[{iconPath:"home",selectedIconPath:"home-fill",text:"首页"},{iconPath:"grid",selectedIconPath:"grid-fill",text:"分类"}]}},onLoad:function(){var t=this;return(0,r.default)((0,o.default)().mark((function e(){return(0,o.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.id=t.$Route.query.id,t.sys=uni.getSystemInfoSync(),e.next=4,t.getShopInfoFun();case 4:return e.next=6,t.getShopGoodsCategoryFun();case 6:t.isFirstLoading=!1,t.mescroll.resetUpScroll();case 8:case"end":return e.stop()}}),e)})))()},onHide:function(){this.enableFix=!1},onShow:function(){this.enableFix=!0},onShareAppMessage:function(){return{title:this.shopInfo.name,path:"pages/store_index/store_index?invite_code=".concat(this.inviteCode,"&id=").concat(this.id)}},methods:{handleAdclick:function(t){var e=this.shopInfo.ad.mobile[t],n=e.link,i=e.link_query;console.log(n),this.$Router.push({path:n,query:i})},tabbarChange:function(t){this.mescroll.resetUpScroll(),this.tabbarcurrent=t},getGoodsCouponFun:function(){var t=this;return(0,r.default)((0,o.default)().mark((function e(){var n,i,a;return(0,o.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return console.log(t.goodsId),e.next=3,(0,d.getCouponList)({shop_id:t.id});case 3:n=e.sent,i=n.data,a=n.code,console.log(i),1==a&&(t.couponlists=i.lists);case 8:case"end":return e.stop()}}),e)})))()},tabChange:function(t){this.tabcurrent=t,2==t&&this.getGoodsCouponFun()},changeShopFollowFun:function(){var t=this;return(0,r.default)((0,o.default)().mark((function e(){var n,i,a;return(0,o.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t.isLogin){e.next=2;break}return e.abrupt("return",t.$Router.push("/pages/login/login"));case 2:return e.next=4,(0,c.changeShopFollow)({shop_id:t.id});case 4:n=e.sent,i=n.code,a=n.msg,1==i&&(t.$toast({title:a}),t.getShopInfoFun());case 8:case"end":return e.stop()}}),e)})))()},getShopInfoFun:function(){var t=this;return(0,r.default)((0,o.default)().mark((function e(){var n,i,a;return(0,o.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,(0,c.getShopInfo)({shop_id:t.id});case 2:n=e.sent,i=n.data,a=n.code,1==a&&(t.shopInfo=i);case 6:case"end":return e.stop()}}),e)})))()},getShopGoodsCategoryFun:function(){var t=this;return(0,r.default)((0,o.default)().mark((function e(){var n,i,a;return(0,o.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,(0,c.getShopGoodsCategory)({shop_id:t.id});case 2:n=e.sent,i=n.data,a=n.code,1==a&&(t.category=i);case 6:case"end":return e.stop()}}),e)})))()},changeActive:function(t){this.active=t,this.refresh()},refresh:function(){this.goodsList=[],1!==this.shopInfo.is_freeze&&1!==this.shopInfo.is_expire&&0!==this.shopInfo.is_run&&this.mescroll.resetUpScroll()},handleSerach:function(){this.$Router.push({path:"/bundle/pages/shop_search/shop_search",query:{id:this.id,is_recommend:0}})},handlemore:function(){this.$Router.push({path:"/bundle/pages/shop_search/shop_search",query:{id:this.id,is_recommend:1}})},upCallback:function(t){var e=this,n=(this.goodsList,this.keyword),i=this.sortConfig,a=i.priceSort,o=i.saleSort,r=i.sort_by_create,s=this.active,c=this.category,l=t.num,d=t.size,f=-1==s?"":c[s].id;setTimeout((function(){console.log(e.tabbarcurrent,"----"),(0,u.getGoodsList)({page_size:d,page_no:l,shop_id:e.id,shop_cate_id:1==e.tabbarcurrent?f:"",sort_by_price:1==e.tabbarcurrent?"":a,sort_by_sales:1==e.tabbarcurrent?"":o,sort_by_create:1==e.tabbarcurrent?"":r,keyword:n}).then((function(n){var i=n.data,a=i.lists,o=a.length,r=!!i.more;1==t.num&&(e.goodsList=[]),e.goodsList=e.goodsList.concat(a),e.mescroll.endSuccess(o,r)}))}),100)}},computed:(0,a.default)((0,a.default)({},(0,s.mapGetters)(["sysInfo"])),{},{navHeight:function(){return this.sysInfo.navHeight+"px"},aslideH:function(){var t=this.sys,e=t.windowHeight,n=(t.navHeight,t.safeArea);return n?(console.log(this.sysInfo),e-this.sysInfo.navHeight-100):0},meScrollH:function(){return this.aslideH-uni.upx2px(80)+"px"},$getImageUri:function(){var t=this;return function(e){return t.$store.state.app.config.base_domain+e}}}),watch:{sortConfig:{deep:!0,handler:function(t){console.log(t),this.refresh()}}}};e.default=f},"610e":function(t,e,n){"use strict";var i=n("3aff"),a=n.n(i);a.a},6119:function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"c",(function(){return o})),n.d(e,"a",(function(){return i}));var i={uIcon:n("6976").default,uBadge:n("c93e").default},a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return t.show?n("v-uni-view",{staticClass:"u-tabbar",on:{touchmove:function(e){e.stopPropagation(),e.preventDefault(),arguments[0]=e=t.$handleEvent(e),function(){}.apply(void 0,arguments)}}},[n("v-uni-view",{staticClass:"u-tabbar__content safe-area-inset-bottom",class:{"u-border-top":t.borderTop},style:{height:t.$u.addUnit(t.height),backgroundColor:t.bgColor}},[t._l(t.list,(function(e,i){return n("v-uni-view",{key:i,staticClass:"u-tabbar__content__item",class:{"u-tabbar__content__circle":t.midButton&&e.midButton},style:{backgroundColor:t.bgColor},on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.clickHandler(i)}}},[n("v-uni-view",{class:[t.midButton&&e.midButton?"u-tabbar__content__circle__button":"u-tabbar__content__item__button"]},[n("u-icon",{attrs:{size:t.midButton&&e.midButton?t.midButtonSize:t.iconSize,name:t.elIconPath(i),"img-mode":"scaleToFill",color:t.elColor(i),"custom-prefix":e.customIcon?"custom-icon":"uicon"}}),e.count?n("u-badge",{attrs:{count:e.count,"is-dot":e.isDot,offset:[-2,t.getOffsetRight(e.count,e.isDot)]}}):t._e()],1),n("v-uni-view",{staticClass:"u-tabbar__content__item__text",style:{color:t.elColor(i)}},[n("v-uni-text",{staticClass:"u-line-1"},[t._v(t._s(e.text))])],1)],1)})),t.midButton?n("v-uni-view",{staticClass:"u-tabbar__content__circle__border",class:{"u-border":t.borderTop},style:{backgroundColor:t.bgColor,left:t.midButtonLeft}}):t._e()],2),n("v-uni-view",{staticClass:"u-fixed-placeholder safe-area-inset-bottom",style:{height:"calc("+t.$u.addUnit(t.height)+" + "+(t.midButton?48:0)+"rpx)"}})],1):t._e()},o=[]},"6e8b":function(t,e,n){"use strict";n.r(e);var i=n("60a1"),a=n.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);e["default"]=a.a},7161:function(t,e,n){"use strict";n("7a82");var i=n("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.apiInvoiceAdd=function(t){return a.default.post("order_invoice/add",t)},e.apiInvoiceDetail=function(t){return a.default.get("order_invoice/detail",{params:t})},e.apiInvoiceEdit=function(t){return a.default.post("order_invoice/edit",t)},e.apiOrderInvoiceDetail=function(t){return a.default.get("order/invoice",{params:t})},e.changeShopFollow=function(t){return a.default.post("shop_follow/changeStatus",t)},e.getInvoiceSetting=function(t){return a.default.get("order_invoice/setting",{params:t})},e.getNearbyShops=function(t){return a.default.get("shop/getNearbyShops",{params:t})},e.getShopCategory=function(){return a.default.get("shop_category/getList")},e.getShopGoodsCategory=function(t){return a.default.get("shop_goods_category/getShopGoodsCategory",{params:t})},e.getShopInfo=function(t){return a.default.get("shop/getShopInfo",{params:t})},e.getShopList=function(t){return a.default.get("shop/getShopList",{params:t})},e.getShopService=function(t){return a.default.get("setting/getShopCustomerService",{params:{shop_id:t}})},e.getTreaty=function(){return a.default.get("ShopApply/getTreaty")},e.shopApply=function(t){return a.default.post("ShopApply/apply",t)},e.shopApplyDetail=function(t){return a.default.get("ShopApply/detail",{params:{id:t}})},e.shopApplyRecord=function(t){return a.default.get("ShopApply/record",{params:t})};var a=i(n("2774"));n("a5ae")},"73cb":function(t,e,n){"use strict";var i=n("bdc6"),a=n.n(i);a.a},"77e5":function(t,e,n){var i=n("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.u-swiper-wrap[data-v-59d8d8c3]{position:relative;overflow:hidden;-webkit-transform:translateY(0);transform:translateY(0)}.u-swiper-image[data-v-59d8d8c3]{width:100%;will-change:transform;height:100%;display:block;pointer-events:none}.u-swiper-indicator[data-v-59d8d8c3]{padding:0 %?24?%;position:absolute;display:flex;flex-direction:row;width:100%;z-index:1}.u-indicator-item-rect[data-v-59d8d8c3]{width:%?26?%;height:%?8?%;margin:0 %?6?%;transition:all .5s;background-color:rgba(0,0,0,.3)}.u-indicator-item-rect-active[data-v-59d8d8c3]{background-color:hsla(0,0%,100%,.8)}.u-indicator-item-dot[data-v-59d8d8c3]{width:%?14?%;height:%?14?%;margin:0 %?6?%;border-radius:%?20?%;transition:all .5s;background-color:rgba(0,0,0,.3)}.u-indicator-item-dot-active[data-v-59d8d8c3]{background-color:hsla(0,0%,100%,.8)}.u-indicator-item-round[data-v-59d8d8c3]{width:%?14?%;height:%?14?%;margin:0 %?6?%;border-radius:%?20?%;transition:all .5s;background-color:rgba(0,0,0,.3)}.u-indicator-item-round-active[data-v-59d8d8c3]{width:%?34?%;background-color:hsla(0,0%,100%,.8)}.u-indicator-item-number[data-v-59d8d8c3]{padding:%?6?% %?16?%;line-height:1;background-color:rgba(0,0,0,.3);border-radius:%?100?%;font-size:%?26?%;color:hsla(0,0%,100%,.8)}.u-list-scale[data-v-59d8d8c3]{-webkit-transform-origin:center center;transform-origin:center center}.u-list-image-wrap[data-v-59d8d8c3]{width:100%;height:100%;flex:1;transition:all .5s;overflow:hidden;box-sizing:initial;position:relative}.u-swiper-title[data-v-59d8d8c3]{position:absolute;background-color:rgba(0,0,0,.3);bottom:0;left:0;width:100%;font-size:%?28?%;padding:%?12?% %?24?%;color:hsla(0,0%,100%,.9)}.u-swiper-item[data-v-59d8d8c3]{display:flex;flex-direction:row;overflow:hidden;align-items:center}',""]),t.exports=e},"80c8":function(t,e,n){"use strict";var i=n("fa1f"),a=n.n(i);a.a},"85d6":function(t,e,n){var i=n("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */uni-page-body[data-v-9473640e]{background-color:#fff}body.?%PAGE?%[data-v-9473640e]{background-color:#fff}uni-page-body .stop_container[data-v-9473640e]{height:100vh;display:flex;flex-direction:column;justify-content:center;align-items:center}uni-page-body .img[data-v-9473640e]{width:%?300?%;height:%?300?%}',""]),t.exports=e},8670:function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return a})),n.d(e,"a",(function(){}));var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{},[n("v-uni-view",{staticClass:"u-sticky-wrap",class:[t.elClass],style:{height:t.fixed?t.height+"px":"auto",backgroundColor:t.bgColor}},[n("v-uni-view",{staticClass:"u-sticky",style:{position:t.fixed?"fixed":"static",top:t.stickyTop+"px",left:t.left+"px",width:"auto"==t.width?"auto":t.width+"px",zIndex:t.uZIndex}},[t._t("default")],2)],1)],1)},a=[]},"8ad1":function(t,e,n){"use strict";n("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("a9e3");var i={name:"u-loading",props:{mode:{type:String,default:"circle"},color:{type:String,default:"#c7c7c7"},size:{type:[String,Number],default:"34"},show:{type:Boolean,default:!0}},computed:{cricleStyle:function(){var t={};return t.width=this.size+"rpx",t.height=this.size+"rpx","circle"==this.mode&&(t.borderColor="#e4e4e4 #e4e4e4 #e4e4e4 ".concat(this.color?this.color:"#c7c7c7")),t}}};e.default=i},"8cfb":function(t,e,n){var i=n("85d6");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var a=n("4f06").default;a("544a4528",i,!0,{sourceMap:!1,shadowMode:!1})},9177:function(t,e,n){"use strict";var i=n("8cfb"),a=n.n(i);a.a},"939d":function(t,e,n){"use strict";var i=n("238c"),a=n.n(i);a.a},9644:function(t,e,n){"use strict";n.r(e);var i=n("6119"),a=n("9b6b");for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);n("9a4f");var r=n("f0c5"),s=Object(r["a"])(a["default"],i["b"],i["c"],!1,null,"94201f12",null,!1,i["a"],void 0);e["default"]=s.exports},"9a4f":function(t,e,n){"use strict";var i=n("4b4a"),a=n.n(i);a.a},"9b6b":function(t,e,n){"use strict";n.r(e);var i=n("e531"),a=n.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);e["default"]=a.a},"9e3f":function(t,e,n){"use strict";n("7a82");var i=n("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.getLiveLists=function(t){return a.default.get("live/lists",{params:t})},e.getShopLive=function(t){return a.default.get("live/shopLive",{params:t})};var a=i(n("2774"))},a21f:function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"c",(function(){return o})),n.d(e,"a",(function(){return i}));var i={uLoading:n("c1c1").default},a=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{class:"loading "+("flex"==this.type?"flex":""),style:{backgroundColor:this.backgroundColor}},[e("u-loading",{attrs:{mode:"flower",size:60}})],1)},o=[]},ac8b:function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"c",(function(){return o})),n.d(e,"a",(function(){return i}));var i={priceFormat:n("a272").default,uIcon:n("6976").default},a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"coupon-list"},t._l(t.list,(function(e,a){return i("v-uni-view",{key:a,staticClass:"m-t-20"},[i("v-uni-view",{class:"coupon-item flex "+(1==t.btnType||2==t.btnType?"gray":"")},[e.is_get?i("img",{staticClass:"received",attrs:{src:n("3e54"),alt:""}}):t._e(),i("v-uni-view",{staticClass:"price white flex-col col-center"},[i("v-uni-view",{staticClass:"xl"},[i("price-format",{attrs:{"first-size":60,"second-size":50,"subscript-size":34,price:e.money,weight:500}})],1),i("v-uni-view",{staticClass:"sm text-center"},[t._v(t._s(e.condition_type_desc))])],1),i("v-uni-view",{staticClass:"info m-l-20"},[i("v-uni-view",{staticClass:"lg m-b-20"},[t._v(t._s(e.coupon_name))]),i("v-uni-view",{staticClass:"xs lighter m-b-20"},[t._v(t._s(e.user_time_desc))]),i("v-uni-view",{staticClass:"xs lighter"},[t._v(t._s(e.use_scene_desc))])],1),i("v-uni-button",{directives:[{name:"show",rawName:"v-show",value:!(1==t.btnType||2==t.btnType),expression:"!(btnType == 1 || btnType == 2)"}],class:"btn br60 white xs "+("去使用"==t.getBtn(e)?"plain":""),attrs:{type:"primary"},on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.onHandle(e.id,e)}}},[t._v(t._s(t.getBtn(e)))]),e.is_get?i("v-uni-image",{staticClass:"receive",attrs:{src:"/static/images/coupon_receive.png"}}):t._e()],1),e.use_goods_desc?i("v-uni-view",{staticClass:"bg-white",staticStyle:{padding:"14rpx 20rpx"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onShowTips(a)}}},[i("v-uni-view",{staticClass:"flex row-between"},[i("v-uni-view",{staticClass:"xs"},[t._v("使用说明")]),i("u-icon",{class:t.showTips[a]?"rotate":"",attrs:{name:"arrow-down"}})],1),i("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:t.showTips[a],expression:"showTips[index]"}],staticClass:"m-t-10 xs"},[t._v(t._s(e.use_goods_desc))])],1):t._e()],1)})),1)},o=[]},ad39:function(t,e,n){"use strict";var i=n("f935"),a=n.n(i);a.a},ae01:function(t,e,n){"use strict";n.r(e);var i=n("bcf5"),a=n.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);e["default"]=a.a},b0cc:function(t,e,n){"use strict";n("7a82");var i=n("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.cancelIntegralOrder=function(t){return a.default.post("integral_order/cancel",{id:t})},e.closeBargainOrder=function(t){return a.default.get("bargain/closeBargain",{params:t})},e.confirmIntegralOrder=function(t){return a.default.post("integral_order/confirm",{id:t})},e.delIntegralOrder=function(t){return a.default.post("integral_order/del",{id:t})},e.getActivityGoodsLists=function(t){return a.default.get("activity_area/activityGoodsList",{params:t})},e.getBargainActivityDetail=function(t){return a.default.get("bargain/bargainDetail",{params:t})},e.getBargainActivityList=function(t){return a.default.get("bargain/orderList",{params:t})},e.getBargainDetail=function(t){return a.default.get("bargain/detail",{params:t})},e.getBargainList=function(t){return a.default.get("bargain/lists",{params:t})},e.getBargainNumber=function(){return a.default.get("bargain/barginNumber")},e.getBargainPost=function(t){return a.default.get("share/shareBargain",{params:t})},e.getCoupon=function(t){return a.default.post("coupon/getCoupon",{coupon_id:t})},e.getCouponList=function(t){return a.default.get("coupon/getCouponList",{params:t})},e.getGroupList=function(t){return a.default.get("team/activity",{params:t})},e.getIntegralGoods=function(t){return a.default.get("integral_goods/lists",{params:t})},e.getIntegralGoodsDetail=function(t){return a.default.get("integral_goods/detail",{params:t})},e.getIntegralOrder=function(t){return a.default.get("integral_order/lists",{params:t})},e.getIntegralOrderDetail=function(t){return a.default.get("integral_order/detail",{params:{id:t}})},e.getIntegralOrderTraces=function(t){return a.default.get("integral_order/orderTraces",{params:{id:t}})},e.getMyCoupon=function(t){return a.default.get("coupon/myCouponList",{params:t})},e.getOrderCoupon=function(t){return a.default.post("coupon/getBuyCouponList",t)},e.getSeckillGoods=function(t){return a.default.get("seckill_goods/getSeckillGoods",{params:t})},e.getSeckillTime=function(){return a.default.get("seckill_goods/getSeckillTime")},e.getSignLists=function(){return a.default.get("sign/lists")},e.getSignRule=function(){return a.default.get("sign/rule")},e.getTeamInfo=function(t){return a.default.get("team/teamInfo",{params:t})},e.getUserGroup=function(t){return a.default.get("team/record",{params:t})},e.helpBargain=function(t){return a.default.post("bargain/knife",t)},e.integralSettlement=function(t){return a.default.get("integral_order/settlement",{params:t})},e.integralSubmitOrder=function(t){return a.default.post("integral_order/submitOrder",t)},e.launchBargain=function(t){return a.default.post("bargain/sponsor",t)},e.teamBuy=function(t){return a.default.post("team/buy",t)},e.teamCheck=function(t){return a.default.post("team/check",t)},e.teamKaiTuan=function(t){return a.default.post("team/kaituan",t)},e.userSignIn=function(){return a.default.get("sign/sign")};var a=i(n("2774"));n("a5ae")},b1e5:function(t,e){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAr4AAADIBAMAAAD8Tf+zAAAAHlBMVEUAAADv7+/k5OTn5+fl5eXm5ubk5OTk5OTl5eX///8aoTc0AAAACHRSTlMAEDBAn6Dv8NSwICYAAAIkSURBVHja7dlREQIxDEXRAoMXpCABCUhAAhZwi4bM5G073bMSzsc2uRnfA77feb/Bly9fvnz58uVb9X3xjfpeP3yTvuPBN+p75xv1vfGN+l748vV/4Ot9M59tuF+8+SZ9n/ZjfYcvX758+fIt+urr4flXX7e/6Q989TP9ly9f/wfvm/mMb9d+oa9HffV1fYcvX758+fLlu5av+0V4v3C/sB/rO3z1yTm++jpf/we+3jfz2Y77hftF1Nf9Qt/hy5cvX758q776enj+1dftb/oDX/1M/+XL1//B+2Y+49u1X+jrUV99Xd/hy5cvX758+a7l634R3i/cL+zH+g5ffXKOr77O1/+Br/fNfLbjfuF+EfV1v9B3+PLly5cv36qvvh6ef/V1+5v+wFc/03/58vV/8L6Zz/h27Rf6etRXX9d3+PLly5cv36qvvh6ef/V1+5v+wFc/03/58vV/8L6Zz/h27Rf6etRXX9d3+PLly5cvX75r+bpfhPcL9wv7sb7DV5+c46uv8/V/4Ot9M5/tuF+4X0R93S/0Hb58+fLly7fqq6+H51993f6mP/DVz/Rfvnz9H7xv5jO+XfuFvh711df1Hb58+fLly5fvWr7uF+H9wv3Cfqzv8NUn5/jq63z9H/h638xnO+4X7hdRX/cLfYcvX758+fKt+urr4flXX7e/6Q989TP9ly9f/wfvm/mMb9d+oa9HffV1fYcvX758+fLly/dE3x8kOVDtActPQAAAAABJRU5ErkJggg=="},b1fc:function(t,e,n){"use strict";n.r(e);var i=n("d219"),a=n.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);e["default"]=a.a},bcf5:function(t,e,n){"use strict";n("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("a9e3"),n("c975");var i={name:"u-swiper",props:{list:{type:Array,default:function(){return[]}},title:{type:Boolean,default:!1},indicator:{type:Object,default:function(){return{}}},borderRadius:{type:[Number,String],default:8},interval:{type:[String,Number],default:3e3},mode:{type:String,default:"round"},height:{type:[Number,String],default:250},indicatorPos:{type:String,default:"bottomCenter"},effect3d:{type:Boolean,default:!1},effect3dPreviousMargin:{type:[Number,String],default:50},autoplay:{type:Boolean,default:!0},duration:{type:[Number,String],default:500},circular:{type:Boolean,default:!0},imgMode:{type:String,default:"aspectFill"},name:{type:String,default:"image"},bgColor:{type:String,default:"#f3f4f6"},current:{type:[Number,String],default:0},titleStyle:{type:Object,default:function(){return{}}}},watch:{list:function(t,e){t.length!==e.length&&(this.uCurrent=0)},current:function(t){this.uCurrent=t}},data:function(){return{uCurrent:this.current}},computed:{justifyContent:function(){return"topLeft"==this.indicatorPos||"bottomLeft"==this.indicatorPos?"flex-start":"topCenter"==this.indicatorPos||"bottomCenter"==this.indicatorPos?"center":"topRight"==this.indicatorPos||"bottomRight"==this.indicatorPos?"flex-end":void 0},titlePaddingBottom:function(){var t=0;return"none"==this.mode?"12rpx":(t=["bottomLeft","bottomCenter","bottomRight"].indexOf(this.indicatorPos)>=0&&"number"==this.mode?"60rpx":["bottomLeft","bottomCenter","bottomRight"].indexOf(this.indicatorPos)>=0&&"number"!=this.mode?"40rpx":"12rpx",t)},elCurrent:function(){return Number(this.current)}},methods:{listClick:function(t){this.$emit("click",t)},change:function(t){var e=t.detail.current;this.uCurrent=e,this.$emit("change",e)},animationfinish:function(t){}}};e.default=i},bd8c:function(t,e,n){"use strict";n.r(e);var i=n("e4b1"),a=n.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);e["default"]=a.a},bdc6:function(t,e,n){var i=n("f86e");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var a=n("4f06").default;a("ed7fa9e8",i,!0,{sourceMap:!1,shadowMode:!1})},c1c1:function(t,e,n){"use strict";n.r(e);var i=n("cf72"),a=n("e50a");for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);n("1d4d");var r=n("f0c5"),s=Object(r["a"])(a["default"],i["b"],i["c"],!1,null,"bf7076f2",null,!1,i["a"],void 0);e["default"]=s.exports},c54b:function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return a})),n.d(e,"a",(function(){}));var i=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{staticClass:"stop_container"},[e("v-uni-image",{staticClass:"img",attrs:{src:this.$getImageUri("/images/shop_stop/shop_stop.png")}}),e("v-uni-view",{staticClass:"lg"},[this._v("店铺暂停营业")]),e("v-uni-view",{staticClass:"muted",staticStyle:{"margin-top":"30rpx"}},[this._v("您要找的店铺暂停营业中 请稍后再来")])],1)},a=[]},ce5f:function(t,e,n){"use strict";n.r(e);var i=n("2251"),a=n.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);e["default"]=a.a},cf13:function(t,e,n){"use strict";n("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("a9e3"),n("d81d"),n("14d9");var i=n("b0cc"),a={data:function(){return{showTips:[]}},props:{list:{type:Array,default:function(){return[]}},btnType:{type:Number}},watch:{list:{handler:function(t){var e=t.map((function(t){return 0}));this.showTips=e},immediate:!0,deep:!0}},computed:{getBtn:function(){var t=this;return function(e){var n="";return n=e.is_get?e.can_continue_get?"继续领取":"去使用":0==t.btnType?"去使用":"领取",n}}},methods:{onHandle:function(t,e){this.id=t;var n=this.getBtn(e);switch(n){case"去使用":0==this.btnType?uni.redirectTo({url:"/pages/store_index/store_index?id=".concat(e.shop_id)}):uni.redirectTo({url:"/pages/store_index/store_index?id=".concat(e.shop.id)});break;case"领取":this.getCouponFun(),e.is_get=1,e.can_continue_get=0;break;case"继续领取":this.getCouponFun(),e.can_continue_get=0;break}},onShowTips:function(t){var e=this.showTips;this.showTips[t]=e[t]?0:1,this.showTips=Object.assign([],this.showTips)},getCouponFun:function(){var t=this;if(!this.isLogin)return this.$Router.push("/pages/login/login");(0,i.getCoupon)(this.id).then((function(e){1==e.code&&t.$toast({title:e.msg})}))}}};e.default=a},cf72:function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return a})),n.d(e,"a",(function(){}));var i=function(){var t=this.$createElement,e=this._self._c||t;return this.show?e("v-uni-view",{staticClass:"u-loading",class:"circle"==this.mode?"u-loading-circle":"u-loading-flower",style:[this.cricleStyle]}):this._e()},a=[]},d219:function(t,e,n){"use strict";n("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;e.default={props:{},computed:{$getImageUri:function(){var t=this;return function(e){return t.$store.state.app.config.base_domain+e}}}}},d996:function(t,e,n){"use strict";n.r(e);var i=n("f99e"),a=n("ae01");for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);n("80c8");var r=n("f0c5"),s=Object(r["a"])(a["default"],i["b"],i["c"],!1,null,"59d8d8c3",null,!1,i["a"],void 0);e["default"]=s.exports},dbb2:function(t,e,n){"use strict";n.r(e);var i=n("a21f"),a=n("0aff");for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);n("939d");var r=n("f0c5"),s=Object(r["a"])(a["default"],i["b"],i["c"],!1,null,"061dd044",null,!1,i["a"],void 0);e["default"]=s.exports},e179:function(t,e,n){var i=n("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.u-fixed-placeholder[data-v-94201f12]{box-sizing:initial}.u-tabbar__content[data-v-94201f12]{display:flex;align-items:center;position:relative;position:fixed;bottom:0;left:0;width:100%;z-index:998;box-sizing:initial}.u-tabbar__content__circle__border[data-v-94201f12]{border-radius:100%;width:%?110?%;height:%?110?%;top:%?-48?%;position:absolute;z-index:4;background-color:#fff;left:50%;-webkit-transform:translateX(-50%);transform:translateX(-50%)}.u-tabbar__content__circle__border[data-v-94201f12]:after{border-radius:100px}.u-tabbar__content__item[data-v-94201f12]{flex:1;justify-content:center;height:100%;padding:%?12?% 0;display:flex;flex-direction:row;flex-direction:column;align-items:center;position:relative}.u-tabbar__content__item__button[data-v-94201f12]{position:absolute;top:%?14?%;left:50%;-webkit-transform:translateX(-50%);transform:translateX(-50%)}.u-tabbar__content__item__text[data-v-94201f12]{color:#606266;font-size:%?26?%;line-height:%?28?%;position:absolute;bottom:%?14?%;left:50%;-webkit-transform:translateX(-50%);transform:translateX(-50%);width:100%;text-align:center}.u-tabbar__content__circle[data-v-94201f12]{position:relative;display:flex;flex-direction:row;flex-direction:column;justify-content:space-between;z-index:10;height:calc(100% - 1px)}.u-tabbar__content__circle__button[data-v-94201f12]{width:%?90?%;height:%?90?%;border-radius:100%;display:flex;flex-direction:row;justify-content:center;align-items:center;position:absolute;background-color:#fff;top:%?-40?%;left:50%;z-index:6;-webkit-transform:translateX(-50%);transform:translateX(-50%)}',""]),t.exports=e},e4b1:function(t,e,n){"use strict";n("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("a9e3"),n("2c3e"),n("e25e");var i={name:"u-sticky",props:{offsetTop:{type:[Number,String],default:0},index:{type:[Number,String],default:""},enable:{type:Boolean,default:!0},h5NavHeight:{type:[Number,String],default:44},bgColor:{type:String,default:"#ffffff"},zIndex:{type:[Number,String],default:""}},data:function(){return{fixed:!1,height:"auto",stickyTop:0,elClass:this.$u.guid(),left:0,width:"auto"}},watch:{offsetTop:function(t){this.initObserver()},enable:function(t){0==t?(this.fixed=!1,this.disconnectObserver("contentObserver")):this.initObserver()}},computed:{uZIndex:function(){return this.zIndex?this.zIndex:this.$u.zIndex.sticky}},mounted:function(){this.initObserver()},methods:{initObserver:function(){var t=this;if(this.enable){var e="string"==typeof this.offsetTop?parseInt(this.offsetTop):uni.upx2px(this.offsetTop);this.stickyTop=0!=this.offsetTop?e+this.h5NavHeight:this.h5NavHeight,this.disconnectObserver("contentObserver"),this.$nextTick((function(){t.$uGetRect("."+t.elClass).then((function(e){t.height=e.height,t.left=e.left,t.width=e.width,t.$nextTick((function(){t.observeContent()}))}))}))}},observeContent:function(){var t=this;this.disconnectObserver("contentObserver");var e=this.createIntersectionObserver({thresholds:[.95,.98,1]});e.relativeToViewport({top:-this.stickyTop}),e.observe("."+this.elClass,(function(e){t.enable&&t.setFixed(e.boundingClientRect.top)})),this.contentObserver=e},setFixed:function(t){var e=t<this.stickyTop;e?this.$emit("fixed",this.index):this.fixed&&this.$emit("unfixed",this.index),this.fixed=e},disconnectObserver:function(t){var e=this[t];e&&e.disconnect()}},beforeDestroy:function(){this.disconnectObserver("contentObserver")}};e.default=i},e50a:function(t,e,n){"use strict";n.r(e);var i=n("8ad1"),a=n.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);e["default"]=a.a},e531:function(t,e,n){"use strict";n("7a82");var i=n("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=i(n("f07e")),o=i(n("c964"));n("a9e3");var r={props:{show:{type:Boolean,default:!0},value:{type:[String,Number],default:0},bgColor:{type:String,default:"#ffffff"},height:{type:[String,Number],default:"50px"},iconSize:{type:[String,Number],default:40},midButtonSize:{type:[String,Number],default:90},activeColor:{type:String,default:"#303133"},inactiveColor:{type:String,default:"#606266"},midButton:{type:Boolean,default:!1},list:{type:Array,default:function(){return[]}},beforeSwitch:{type:Function,default:null},borderTop:{type:Boolean,default:!0},hideTabBar:{type:Boolean,default:!0}},data:function(){return{midButtonLeft:"50%",pageUrl:""}},created:function(){this.hideTabBar&&uni.hideTabBar();var t=getCurrentPages();this.pageUrl=t[t.length-1].route},computed:{elIconPath:function(){var t=this;return function(e){var n=t.list[e].pagePath;return n?n==t.pageUrl||n=="/"+t.pageUrl?t.list[e].selectedIconPath:t.list[e].iconPath:e==t.value?t.list[e].selectedIconPath:t.list[e].iconPath}},elColor:function(){var t=this;return function(e){var n=t.list[e].pagePath;return n?n==t.pageUrl||n=="/"+t.pageUrl?t.activeColor:t.inactiveColor:e==t.value?t.activeColor:t.inactiveColor}}},mounted:function(){this.midButton&&this.getMidButtonLeft()},methods:{clickHandler:function(t){var e=this;return(0,o.default)((0,a.default)().mark((function n(){var i;return(0,a.default)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(!e.beforeSwitch||"function"!==typeof e.beforeSwitch){n.next=10;break}if(i=e.beforeSwitch.bind(e.$u.$parent.call(e))(t),!i||"function"!==typeof i.then){n.next=7;break}return n.next=5,i.then((function(n){e.switchTab(t)})).catch((function(t){}));case 5:n.next=8;break;case 7:!0===i&&e.switchTab(t);case 8:n.next=11;break;case 10:e.switchTab(t);case 11:case"end":return n.stop()}}),n)})))()},switchTab:function(t){this.$emit("change",t),this.list[t].pagePath?uni.switchTab({url:this.list[t].pagePath}):this.$emit("input",t)},getOffsetRight:function(t,e){return e?-20:t>9?-40:-30},getMidButtonLeft:function(){var t=this.$u.sys().windowWidth;this.midButtonLeft=t/2+"px"}}};e.default=r},f1ef:function(t,e,n){var i=n("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */uni-page-body[data-v-53597a42]{padding:0}.category[data-v-53597a42]{margin-top:%?-150?%;margin-left:%?20?%;margin-right:%?20?%;background-color:#fff;border-radius:%?20?%}.category .category-aside[data-v-53597a42]{width:%?150?%;align-self:flex-start}.category .category-aside .aside-item[data-v-53597a42]{width:%?120?%;padding:%?20?% 0}.category .category-aside .aside-item.active[data-v-53597a42]{position:relative;background-color:#fff}.category .category-aside .aside-item.active[data-v-53597a42]::before{content:"";width:%?3?%;height:%?80?%;background-color:#ff2c3c;position:absolute;left:0}.store-index .store-header[data-v-53597a42]{background-repeat:no-repeat;background-size:100% auto}.store-index .store-header .store-info[data-v-53597a42]{padding-right:%?30?%;padding-left:%?24?%}.store-index .store-header .store-info .store-tag[data-v-53597a42]{background:linear-gradient(267deg,#ff2c3c,#f52e99);border-radius:%?6?%;padding:%?4?% %?9?%}.store-index .store-header .store-info .subscribe-btn[data-v-53597a42]{background:linear-gradient(97deg,#ff5784,#ff2c3c);height:%?52?%;width:%?128?%}.store-index .store-header .store-info .subscribe-btn.gray[data-v-53597a42]{background:#ccc}.store-index .content[data-v-53597a42]{margin-top:%?-200?%;margin-left:%?20?%;margin-right:%?20?%}.store-index .content .content-wrap[data-v-53597a42]{border-radius:%?20?%;overflow:hidden;margin-bottom:%?30?%}.store-index .content .store-hot-goods[data-v-53597a42]{overflow:hidden}.store-index .content .store-hot-goods .store-hot-header[data-v-53597a42]{padding:%?20?%}.store-index .content .store-hot-goods .store-hot-header .column-line[data-v-53597a42]{width:%?6?%;height:%?32?%;background-color:#ff2c3c}.store-index .content .store-hot-goods .store-hot-content .goods[data-v-53597a42]{display:inline-block}.store-index .content .store-hot-goods .store-hot-content .hot-goods-item[data-v-53597a42]{width:%?200?%}.store-index .content .store-hot-goods .store-hot-content .hot-goods-item .hot-goods-info .hot-goods-price[data-v-53597a42]{margin-top:%?5?%}.store-index .content .goods-display .category-row[data-v-53597a42]{align-self:flex-start;width:%?630?%}.store-index .content .goods-display .category-row .sort-tool-bar .sort-item[data-v-53597a42]{flex:1;height:%?80?%}.store-index .content .goods-display .category-row .goods-lists[data-v-53597a42]{padding:%?20?%}.store-index .content .goods-display .category-row .goods-lists .item[data-v-53597a42]{width:%?285?%;border-radius:%?10?%;overflow:hidden}.store-index .content .goods-display .category-row .goods-lists .item[data-v-53597a42]:nth-of-type(2n){margin-left:%?20?%}.store-index .content .goods-display .category-row .goods-lists .item .goods-info[data-v-53597a42]{padding:%?10?%}.coupon-container[data-v-53597a42]{background-color:#fff;height:%?1000?%;margin:0 %?20?%;border-radius:%?20?%;padding:%?20?%}.coupon-container .coupon[data-v-53597a42]{height:100%;display:flex;justify-content:center;align-items:center}',""]),t.exports=e},f1ff:function(t,e,n){var i=n("212e");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var a=n("4f06").default;a("5e1ac5de",i,!0,{sourceMap:!1,shadowMode:!1})},f86e:function(t,e,n){var i=n("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.u-navbar[data-v-6d93ee5a]{width:100%}.u-navbar-fixed[data-v-6d93ee5a]{position:fixed;left:0;right:0;top:0;z-index:991}.u-status-bar[data-v-6d93ee5a]{width:100%}.u-navbar-inner[data-v-6d93ee5a]{width:100%;display:flex;flex-direction:row;justify-content:space-between;position:relative;align-items:center}.u-back-wrap[data-v-6d93ee5a]{display:flex;flex-direction:row;align-items:center;flex:1;flex-grow:0;padding:%?14?% %?14?% %?14?% %?24?%}.u-back-text[data-v-6d93ee5a]{padding-left:%?4?%;font-size:%?30?%}.u-navbar-content-title[data-v-6d93ee5a]{display:flex;flex-direction:row;align-items:center;justify-content:center;flex:1;position:absolute;left:0;right:0;height:%?60?%;text-align:center;flex-shrink:0}.u-navbar-centent-slot[data-v-6d93ee5a]{flex:1}.u-title[data-v-6d93ee5a]{line-height:%?60?%;font-size:%?32?%;flex:1}.u-navbar-right[data-v-6d93ee5a]{flex:1;display:flex;flex-direction:row;align-items:center;justify-content:flex-end}.u-slot-content[data-v-6d93ee5a]{flex:1;display:flex;flex-direction:row;align-items:center;position:relative}',""]),t.exports=e},f935:function(t,e,n){var i=n("f1ef");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var a=n("4f06").default;a("29c56581",i,!0,{sourceMap:!1,shadowMode:!1})},f99e:function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return a})),n.d(e,"a",(function(){}));var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{staticClass:"u-swiper-wrap",style:{borderRadius:t.borderRadius+"rpx"}},[n("v-uni-swiper",{style:{height:t.height+"rpx",backgroundColor:t.bgColor},attrs:{current:t.elCurrent,interval:t.interval,circular:t.circular,duration:t.duration,autoplay:t.autoplay,"previous-margin":t.effect3d?t.effect3dPreviousMargin+"rpx":"0","next-margin":t.effect3d?t.effect3dPreviousMargin+"rpx":"0"},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.change.apply(void 0,arguments)},animationfinish:function(e){arguments[0]=e=t.$handleEvent(e),t.animationfinish.apply(void 0,arguments)}}},t._l(t.list,(function(e,i){return n("v-uni-swiper-item",{key:i,staticClass:"u-swiper-item"},[n("v-uni-view",{staticClass:"u-list-image-wrap",class:[t.uCurrent!=i?"u-list-scale":""],style:{borderRadius:t.borderRadius+"rpx",transform:t.effect3d&&t.uCurrent!=i?"scaleY(0.9)":"scaleY(1)",margin:t.effect3d&&t.uCurrent!=i?"0 20rpx":0},on:{click:function(e){e.stopPropagation(),e.preventDefault(),arguments[0]=e=t.$handleEvent(e),t.listClick(i)}}},[n("v-uni-image",{staticClass:"u-swiper-image",attrs:{src:e[t.name]||e,mode:t.imgMode}}),t.title&&e.title?n("v-uni-view",{staticClass:"u-swiper-title u-line-1",style:[{"padding-bottom":t.titlePaddingBottom},t.titleStyle]},[t._v(t._s(e.title))]):t._e()],1)],1)})),1),n("v-uni-view",{staticClass:"u-swiper-indicator",style:{top:"topLeft"==t.indicatorPos||"topCenter"==t.indicatorPos||"topRight"==t.indicatorPos?"12rpx":"auto",bottom:"bottomLeft"==t.indicatorPos||"bottomCenter"==t.indicatorPos||"bottomRight"==t.indicatorPos?"12rpx":"auto",justifyContent:t.justifyContent,padding:"0 "+(t.effect3d?"74rpx":"24rpx")}},["rect"==t.mode?t._l(t.list,(function(e,i){return n("v-uni-view",{key:i,staticClass:"u-indicator-item-rect",class:{"u-indicator-item-rect-active":i==t.uCurrent}})})):t._e(),"dot"==t.mode?t._l(t.list,(function(e,i){return n("v-uni-view",{key:i,staticClass:"u-indicator-item-dot",class:{"u-indicator-item-dot-active":i==t.uCurrent}})})):t._e(),"round"==t.mode?t._l(t.list,(function(e,i){return n("v-uni-view",{key:i,staticClass:"u-indicator-item-round",class:{"u-indicator-item-round-active":i==t.uCurrent}})})):t._e(),"number"==t.mode?[n("v-uni-view",{staticClass:"u-indicator-item-number"},[t._v(t._s(t.uCurrent+1)+"/"+t._s(t.list.length))])]:t._e()],2)],1)},a=[]},fa1f:function(t,e,n){var i=n("77e5");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var a=n("4f06").default;a("9f9a15a8",i,!0,{sourceMap:!1,shadowMode:!1})}}]);