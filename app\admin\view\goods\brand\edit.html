{layout name="layout2" /}

<div class="layui-form" lay-filter="layui-form" id="layui-form" style="padding: 20px 30px 0 0;">
    <input type="hidden" value="{$detail.id}" name="id">
    <!--品牌名称-->
    <div class="layui-form-item">
        <label class="layui-form-label"><font color="red">*</font>品牌名称</label>
        <div class="layui-input-inline">
            <input type="text" name="name" value="{$detail.name}" lay-verify="required" lay-verType="tips" placeholder="请输入品牌名称" autocomplete="off" class="layui-input">
        </div>
    </div>
    <!--品牌首字母-->
    <div class="layui-form-item">
        <label class="layui-form-label"><font color="red">*</font>品牌首字母</label>
        <div class="layui-input-inline">
            <select name="initial" id="initial">
                {foreach $capital as $val}
                <option value="{$val}" {if condition="$detail.initial eq $val" }selected{/if}>{$val}</option>
                {/foreach}
            </select>
        </div>
    </div>
    <!--品牌图片-->
    <div class="layui-form-item">
        <label class="layui-form-label">品牌图片</label>
        <div class="layui-input-block">
            <div class="like-upload-image">
                {if $detail.image}
                <div class="upload-image-div">
                    <img src="{$detail.image}" alt="img">
                    <input type="hidden" name="image" value="{$detail.image}">
                    <div class="del-upload-btn">x</div>
                </div>
                <div class="upload-image-elem" style="display:none;"><a class="add-upload-image"> + 添加图片</a></div>
                {else}
                <div class="upload-image-elem"><a class="add-upload-image"> + 添加图片</a></div>
                {/if}
            </div>
            <div class="layui-form-mid layui-word-aux">建议尺寸：宽200像素*高200像素的jpg，jpeg，png图片</div>
        </div>
    </div>
    <!--品牌排序-->
    <div class="layui-form-item">
        <label class="layui-form-label">品牌排序</label>
        <div class="layui-input-inline">
            <input type="number"  name="sort" value="{$detail.sort}"  placeholder="请输入品牌排序" class="layui-input">
        </div>
    </div>
    <!--是否显示-->
    <div class="layui-form-item">
        <label class="layui-form-label">显示状态</label>
        <div class="layui-input-inline">
            <input type="radio" name="is_show" value="1" title="显示" {if condition="$detail.is_show eq 1" }checked{/if}>
            <input type="radio" name="is_show" value="0" title="不显示" {if condition="$detail.is_show eq 0" }checked{/if}>
        </div>
    </div>
    <!--品牌描述-->
    <div class="layui-form-item">
        <label class="layui-form-label">品牌描述</label>
        <div class="layui-input-inline">
            <textarea name="remark"  placeholder="请输入品牌描述" class="layui-textarea">{$detail.remark}</textarea>
        </div>
    </div>

    <div class="layui-form-item layui-hide">
        <input type="button" lay-submit lay-filter="edit-submit" id="edit-submit" value="确认">
    </div>
</div>

<script>
    layui.use([], function () {
        like.delUpload();
        $(document).on("click", ".add-upload-image", function () {
            like.imageUpload({
                limit: 1,
                field: "image",
                that: $(this)
            });
        })
    });
</script>