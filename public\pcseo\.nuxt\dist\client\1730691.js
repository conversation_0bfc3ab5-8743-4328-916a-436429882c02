(window.webpackJsonp=window.webpackJsonp||[]).push([[19],{437:function(t,e,r){"use strict";var n=r(17),o=r(2),c=r(3),l=r(136),f=r(27),d=r(18),h=r(271),v=r(52),m=r(135),x=r(270),N=r(5),_=r(98).f,I=r(44).f,y=r(26).f,E=r(438),S=r(439).trim,w="Number",k=o.Number,A=k.prototype,C=o.TypeError,T=c("".slice),F=c("".charCodeAt),M=function(t){var e=x(t,"number");return"bigint"==typeof e?e:O(e)},O=function(t){var e,r,n,o,c,l,f,code,d=x(t,"number");if(m(d))throw C("Cannot convert a Symbol value to a number");if("string"==typeof d&&d.length>2)if(d=S(d),43===(e=F(d,0))||45===e){if(88===(r=F(d,2))||120===r)return NaN}else if(48===e){switch(F(d,1)){case 66:case 98:n=2,o=49;break;case 79:case 111:n=8,o=55;break;default:return+d}for(l=(c=T(d,2)).length,f=0;f<l;f++)if((code=F(c,f))<48||code>o)return NaN;return parseInt(c,n)}return+d};if(l(w,!k(" 0o1")||!k("0b1")||k("+0x1"))){for(var R,V=function(t){var e=arguments.length<1?0:k(M(t)),r=this;return v(A,r)&&N((function(){E(r)}))?h(Object(e),r,V):e},G=n?_(k):"MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,EPSILON,MAX_SAFE_INTEGER,MIN_SAFE_INTEGER,isFinite,isInteger,isNaN,isSafeInteger,parseFloat,parseInt,fromString,range".split(","),L=0;G.length>L;L++)d(k,R=G[L])&&!d(V,R)&&y(V,R,I(k,R));V.prototype=A,A.constructor=V,f(o,w,V)}},438:function(t,e,r){var n=r(3);t.exports=n(1..valueOf)},439:function(t,e,r){var n=r(3),o=r(33),c=r(16),l=r(440),f=n("".replace),d="["+l+"]",h=RegExp("^"+d+d+"*"),v=RegExp(d+d+"*$"),m=function(t){return function(e){var r=c(o(e));return 1&t&&(r=f(r,h,"")),2&t&&(r=f(r,v,"")),r}};t.exports={start:m(1),end:m(2),trim:m(3)}},440:function(t,e){t.exports="\t\n\v\f\r                　\u2028\u2029\ufeff"},473:function(t,e,r){var content=r(487);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,r(14).default)("d6370bb2",content,!0,{sourceMap:!1})},486:function(t,e,r){"use strict";r(473)},487:function(t,e,r){var n=r(13)(!1);n.push([t.i,".shop-item[data-v-871c1244]{width:270px;height:400px;background-size:cover;background-position:50%;padding:10px;border-radius:6px}.shop-item .shop-info[data-v-871c1244]{border-radius:6px;padding:18px 15px}.shop-item .shop-info .logo[data-v-871c1244]{width:70px;height:70px;border-radius:16px;margin-top:-45px}.shop-item .shop-info .sales[data-v-871c1244]{display:inline-block;padding:4px 10px;background-color:#f2f2f2;margin-top:6px;border-radius:4px}",""]),t.exports=n},500:function(t,e,r){"use strict";r.r(e);r(437);var n={components:{},props:{cover:{type:String},shopId:{type:[String,Number]},logo:{type:String},type:{type:[String,Number]},name:{type:String},sales:{type:[String,Number]}},methods:{}},o=(r(486),r(9)),component=Object(o.a)(n,(function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("nuxt-link",{staticClass:"shop-item flex-col row-right",style:{"background-image":"url("+t.cover+")"},attrs:{to:"/shop_street_detail?id="+t.shopId}},[r("div",{staticClass:"bg-white shop-info text-center"},[r("el-image",{staticClass:"logo",attrs:{src:t.logo}}),t._v(" "),r("div",{staticClass:"m-t-12 line-1 lg"},[1==t.type?r("el-tag",{attrs:{size:"mini"}},[t._v("自营")]):t._e(),t._v(" "+t._s(t.name)+"\n        ")],1),t._v(" "),r("span",{staticClass:"xs muted sales"},[t._v("共"+t._s(t.sales)+"件商品")])],1)])}),[],!1,null,"871c1244",null);e.default=component.exports}}]);