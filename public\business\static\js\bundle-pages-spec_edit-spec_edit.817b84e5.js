(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["bundle-pages-spec_edit-spec_edit"],{"08fa":function(e,t,i){"use strict";i.r(t);var a=i("5c60"),n=i("1b0c");for(var o in n)["default"].indexOf(o)<0&&function(e){i.d(t,e,(function(){return n[e]}))}(o);i("1813");var s=i("828b"),c=Object(s["a"])(n["default"],a["b"],a["c"],!1,null,"72c3f8ad",null,!1,a["a"],void 0);t["default"]=c.exports},1813:function(e,t,i){"use strict";var a=i("18bd"),n=i.n(a);n.a},"18bd":function(e,t,i){var a=i("cc3c");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[e.i,a,""]]),a.locals&&(e.exports=a.locals);var n=i("967d").default;n("ff965832",a,!0,{sourceMap:!1,shadowMode:!1})},"1b0c":function(e,t,i){"use strict";i.r(t);var a=i("c7ec"),n=i.n(a);for(var o in a)["default"].indexOf(o)<0&&function(e){i.d(t,e,(function(){return a[e]}))}(o);t["default"]=n.a},2319:function(e,t,i){var a=i("c846");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[e.i,a,""]]),a.locals&&(e.exports=a.locals);var n=i("967d").default;n("2b8f51b0",a,!0,{sourceMap:!1,shadowMode:!1})},"244d":function(e,t,i){"use strict";i.r(t);var a=i("f0f1"),n=i.n(a);for(var o in a)["default"].indexOf(o)<0&&function(e){i.d(t,e,(function(){return a[e]}))}(o);t["default"]=n.a},"3fdc":function(e,t,i){"use strict";i.r(t);var a=i("7d18"),n=i("244d");for(var o in n)["default"].indexOf(o)<0&&function(e){i.d(t,e,(function(){return n[e]}))}(o);i("dc27");var s=i("828b"),c=Object(s["a"])(n["default"],a["b"],a["c"],!1,null,"260e1ff2",null,!1,a["a"],void 0);t["default"]=c.exports},"5c60":function(e,t,i){"use strict";i.d(t,"b",(function(){return n})),i.d(t,"c",(function(){return o})),i.d(t,"a",(function(){return a}));var a={goodsCard:i("3fdc").default,modal:i("081c").default,uToast:i("341e").default},n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("v-uni-view",{staticClass:"goods-detail"},[i("v-uni-view",{staticClass:"m-t-20"},[i("goods-card",{attrs:{data:e.goodsInfo}})],1),i("v-uni-view",{staticClass:"muted xs p-l-20 p-b-20 p-t-20 flex row-between"},[e._v(e._s(1==e.goodsInfo.spec_type?"规格型号（统一规格）":"规格型号（多规格）")),2==e.goodsInfo.spec_type?i("v-uni-view",{staticClass:"p-r-20",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.spec_type=!e.spec_type}}},[e._v(e._s(e.spec_type?"取消批量":"批量设置"))]):e._e()],1),i("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:e.spec_type,expression:"spec_type"}],staticClass:"primary flex p-b-20"},[i("v-uni-view",{staticClass:"m-l-20",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.openSpecEditFunc("price")}}},[e._v("价格")]),i("v-uni-view",{staticClass:"m-l-20",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.openSpecEditFunc("market_price")}}},[e._v("市场价")]),i("v-uni-view",{staticClass:"m-l-20",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.openSpecEditFunc("chengben_price")}}},[e._v("成本价")]),i("v-uni-view",{staticClass:"m-l-20",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.openSpecEditFunc("stock")}}},[e._v("库存")])],1),e._l(e.goodsInfo.goods_item,(function(t,a){return[i("v-uni-view",{key:a+"_0",staticClass:"m-b-20 p-t-20 bg-white"},[i("v-uni-view",{staticClass:"m-b-10 primary nr title"},[e._v(e._s(t.spec_value_str))]),i("v-uni-view",{staticClass:"item bb"},[i("v-uni-view",[e._v("价格:")]),i("v-uni-view",{staticClass:"flex row-between"},[i("v-uni-input",{attrs:{type:"text",placeholder:"请输入价格"},model:{value:t.price,callback:function(i){e.$set(t,"price",i)},expression:"item.price"}}),e._v("元")],1)],1),i("v-uni-view",{staticClass:"item bb"},[i("v-uni-view",[e._v("市场价:")]),i("v-uni-view",{staticClass:"flex row-between"},[i("v-uni-input",{attrs:{type:"text",placeholder:"请输入市场价"},model:{value:t.market_price,callback:function(i){e.$set(t,"market_price",i)},expression:"item.market_price"}}),e._v("元")],1)],1),i("v-uni-view",{staticClass:"item bb"},[i("v-uni-view",[e._v("成本价:")]),i("v-uni-view",{staticClass:"flex row-between"},[i("v-uni-input",{attrs:{type:"text",placeholder:"请输入成本价"},model:{value:t.chengben_price,callback:function(i){e.$set(t,"chengben_price",i)},expression:"item.chengben_price"}}),e._v("元")],1)],1),i("v-uni-view",{staticClass:"item bb"},[i("v-uni-view",[e._v("库存:")]),i("v-uni-view",{staticClass:"flex row-between"},[i("v-uni-input",{attrs:{type:"text",placeholder:"请输入库存"},model:{value:t.stock,callback:function(i){e.$set(t,"stock",i)},expression:"item.stock"}})],1)],1)],1)]})),i("v-uni-view",{staticClass:"footer bg-white flex row-between fixed"},[i("v-uni-button",{staticClass:"btn br60 md white",attrs:{size:"md","hover-class":"none"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.onSubmit.apply(void 0,arguments)}}},[e._v("确认")])],1),i("modal",{attrs:{title:"批量设置",height:"200rpx"},on:{confirm:function(t){arguments[0]=t=e.$handleEvent(t),e.specEditFunc(e.action)}},model:{value:e.flag,callback:function(t){e.flag=t},expression:"flag"}},["price"==e.action?[i("v-uni-view",{staticClass:"black nr flex",staticStyle:{height:"200rpx"}},[e._v("价格："),i("v-uni-input",{staticClass:"input",attrs:{type:"text"},model:{value:e.price,callback:function(t){e.price=t},expression:"price"}}),e._v("元")],1)]:e._e(),"market_price"==e.action?[i("v-uni-view",{staticClass:"black nr flex",staticStyle:{height:"200rpx"}},[e._v("市场价："),i("v-uni-input",{staticClass:"input",attrs:{type:"text"},model:{value:e.market_price,callback:function(t){e.market_price=t},expression:"market_price"}}),e._v("元")],1)]:e._e(),"chengben_price"==e.action?[i("v-uni-view",{staticClass:"black nr flex",staticStyle:{height:"200rpx"}},[e._v("成本价："),i("v-uni-input",{staticClass:"input",attrs:{type:"text"},model:{value:e.chengben_price,callback:function(t){e.chengben_price=t},expression:"chengben_price"}}),e._v("元")],1)]:e._e(),"stock"==e.action?[i("v-uni-view",{staticClass:"black nr flex",staticStyle:{height:"200rpx"}},[e._v("库存："),i("v-uni-input",{staticClass:"input",attrs:{type:"text"},model:{value:e.stock,callback:function(t){e.stock=t},expression:"stock"}})],1)]:e._e()],2),i("u-toast",{ref:"uToast"})],2)},o=[]},"7d18":function(e,t,i){"use strict";i.d(t,"b",(function(){return n})),i.d(t,"c",(function(){return o})),i.d(t,"a",(function(){return a}));var a={uImage:i("543b").default},n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("v-uni-view",{staticClass:"goods bg-white"},[i("v-uni-view",{staticClass:"goods-wrap flex",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.toDetail(e.data.id)}}},[i("v-uni-view",{staticClass:"image"},[i("u-image",{attrs:{src:e.data.image,width:"160",height:"160"}})],1),i("v-uni-view",{staticClass:"m-l-16 line-1"},[i("v-uni-view",{staticClass:"goods-name line-1 m-t-10"},[e._v(e._s(e.data.name))]),i("v-uni-view",{staticClass:"goods-price primary m-t-10"},[e._v("¥"+e._s(e.data.min_price))]),i("v-uni-view",{staticClass:"muted flex row-between xs m-t-10"},[i("v-uni-view",[e._v("总库存: "+e._s(e.data.stock))]),i("v-uni-view",[e._v("总销量: "+e._s(e.data.sales_actual))])],1)],1)],1),i("v-uni-view",{staticClass:"goods-footer flex row-right"},[e._t("default")],2)],1)},o=[]},c7ec:function(e,t,i){"use strict";i("6a54");var a=i("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=a(i("2634")),o=a(i("2fdc")),s=a(i("b7c7"));i("bf0f"),i("2797"),i("fd3c"),i("7a76"),i("c9b5");var c=i("f864"),r=(i("1a7c"),{name:"GoodsDetail",data:function(){return{goodsInfo:{},action:"",spec_type:!1,flag:!1,price:"",market_price:"",chengben_price:"",stock:""}},methods:{initGoodsDetail:function(){var e=this;return new Promise((function(t,i){(0,c.apiGoodsDetail)({id:e.goods_id,visit:1}).then((function(t){e.goodsInfo=t})).catch((function(e){i(e)}))}))},openSpecEditFunc:function(e){this.action=e,this.flag=!0},specEditFunc:function(e){var t=this;this.goodsInfo.goods_item.forEach((function(i){i[e]=t[e]})),this.goodsInfo.goods_item=(0,s.default)(this.goodsInfo.goods_item)},onSubmit:function(){var e=this;return(0,o.default)((0,n.default)().mark((function t(){var i;return(0,n.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return[],i=e.goodsInfo.goods_item.map((function(e){var t=e.id,i=e.market_price,a=e.price,n=e.stock,o=e.chengben_price;return{id:t,market_price:i,price:a,stock:n,chengben_price:o}})),t.next=4,(0,c.apiGoodsEdit)({items:i});case 4:e.$refs.uToast.show({title:"设置成功",type:"success"}),setTimeout((function(){e.$Router.back()}),1e3);case 6:case"end":return t.stop()}}),t)})))()}},onLoad:function(){var e=this;return(0,o.default)((0,n.default)().mark((function t(){var i;return(0,n.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(i=e.$Route.query,e.goods_id=i.id,t.prev=2,e.goods_id){t.next=5;break}throw new Error("该商品不存在");case 5:return t.next=7,e.initGoodsDetail();case 7:t.next=12;break;case 9:t.prev=9,t.t0=t["catch"](2),console.log(t.t0);case 12:case"end":return t.stop()}}),t,null,[[2,9]])})))()}});t.default=r},c846:function(e,t,i){var a=i("c86c");t=a(!1),t.push([e.i,"@charset \"UTF-8\";\n/* 颜色变量 */\n/** S Font's size **/\n/** E Font's size **/[data-v-260e1ff2]:export{red_theme:#ff2c3c;orange_theme:#f7971e;pink_theme:#fa444d;gold_theme:#e0a356;blue_theme:#2f80ed;green_theme:#2ec840}.goods[data-v-260e1ff2]{width:100%;padding:%?20?%;margin-bottom:%?20?%}.goods-wrap[data-v-260e1ff2]{width:100%}.goods-wrap .goods-name[data-v-260e1ff2]{color:#101010;font-size:%?28?%}.goods-wrap .goods-price[data-v-260e1ff2]{color:red;font-size:%?28?%}.goods-wrap > uni-view[data-v-260e1ff2]{width:100%}.goods-wrap .image[data-v-260e1ff2]{flex:0}",""]),e.exports=t},cc3c:function(e,t,i){var a=i("c86c");t=a(!1),t.push([e.i,"@charset \"UTF-8\";\n/* 颜色变量 */\n/** S Font's size **/\n/** E Font's size **/[data-v-72c3f8ad]:export{red_theme:#ff2c3c;orange_theme:#f7971e;pink_theme:#fa444d;gold_theme:#e0a356;blue_theme:#2f80ed;green_theme:#2ec840}.goods-detail[data-v-72c3f8ad]{padding-bottom:%?200?%}.goods-detail .primary[data-v-72c3f8ad]{color:#40affa}.goods-detail .input[data-v-72c3f8ad]{width:%?300?%;padding:%?10?%;margin:0 %?20?%;border-radius:%?4?%;border:1px solid #dbdbdb}.goods-detail .item[data-v-72c3f8ad]{padding:%?30?%;display:flex;flex-direction:row;align-items:center;background-color:#fff;justify-content:flex-start}.goods-detail .item > uni-view[data-v-72c3f8ad]:first-child{width:%?180?%;color:#101010;font-size:%?28?%;font-weight:500}.goods-detail .item > uni-view[data-v-72c3f8ad]:last-child{width:%?400?%;text-align:left;height:%?76?%;padding:0 %?12?%;border-radius:%?6?%;border:1px solid #dbdbdb}.goods-detail .title[data-v-72c3f8ad]{color:#fff;display:inline-block;margin:0 %?20?%;padding:%?6?% %?16?%;border-radius:%?8?%;background-color:#40affa}.goods-detail .footer[data-v-72c3f8ad]{position:fixed;bottom:0;left:0;right:0;z-index:100;height:%?110?%;padding:0 %?30?%;box-sizing:initial;padding-bottom:env(safe-area-inset-bottom)}.goods-detail .footer .btn[data-v-72c3f8ad]{width:100%;height:%?88?%;line-height:%?88?%;background-color:#40affa}",""]),e.exports=t},dc27:function(e,t,i){"use strict";var a=i("2319"),n=i.n(a);n.a},f0f1:function(e,t,i){"use strict";i("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,i("aa9c");var a={name:"GoodsCard",props:{data:{type:Object,default:function(){}}},methods:{toDetail:function(e){this.$Router.push({path:"/pages/goods_detail/goods_detail",query:{id:e}})}}};t.default=a},f864:function(e,t,i){"use strict";i("6a54");var a=i("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.apiGoodsOperation=t.apiGoodsLists=t.apiGoodsEdit=t.apiGoodsDetail=void 0;var n=a(i("be47"));t.apiGoodsLists=function(e){return n.default.get("goods/lists",{params:e})};t.apiGoodsOperation=function(e){return n.default.post("goods/operation",e)};t.apiGoodsDetail=function(e){return n.default.get("goods/detail",{params:e})};t.apiGoodsEdit=function(e){return n.default.post("goods/edit",e)}}}]);