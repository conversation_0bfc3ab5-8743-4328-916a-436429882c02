<?php



namespace app\common\model\goods;


use app\common\basics\Models;


/**
 * 供货商
 * Class Supplier
 * @package app\common\model
 */
class Supplier extends Models
{

    /**
     * Notes: 获取以id为键的名称
     * <AUTHOR> 18:31)
     * @return array
     */
    public static function getNameColumn($shop_id = 0)
    {
        $condition[] = ['del', '=', 0];

        if ($shop_id > 0) {
            $condition[] = ['shop_id', '=', $shop_id];
        }

        $lists = self::where($condition)->column('id,name', 'id');

        return empty($lists) ? [] : $lists;
    }

}
