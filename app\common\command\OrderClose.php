<?php


namespace app\common\command;


use app\api\logic\GoodsColumnLogic;
use app\common\enum\OrderEnum;
use app\common\enum\PayEnum;
use app\common\model\CouponList;
use app\common\model\goods\Goods;
use app\common\model\goods\GoodsItem;
use app\common\model\order\Order;
use app\common\server\ConfigServer;
use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\facade\Db;
use think\facade\Log;

class OrderClose extends Command
{
    protected function configure()
    {
        $this->setName('order_close')
            ->setDescription('关闭订单');
    }

    protected function execute(Input $input, Output $output)
    {
        try {
            GoodsColumnLogic::getGoodsColumnMatch();//更新商品标签
            Db::name('ad_order')
                ->where('status',0)
                ->where('create_time', '<=', (time() - 300))
                ->delete();

            Db::name('jcai_activity')
                ->where('status',1)
                ->where('activity_end_time', '<=',time())
                ->update(['status'=>2,'update_time'=>time()]);
            //更新代理结算
            //获取未冻结的代理用户id数组
            $agent_users=Db::name('agent')->where('is_freeze', 0)->column('user_id');

            if($agent_users){
                $agent_order_id= Db::name('agent_order')->where(['status'=>1])->whereIn('user_id',$agent_users)->select()->toArray();
                //获取结算周期

                $now=strtotime(date('Y-m-d H:i',time()));
                $js_date=ConfigServer::get('agent', 'js_date', 0);
                foreach ($agent_order_id as $k=>$v){
                    $settlement_time=strtotime(date('Y-m-d H:i',$v['create_time']+$js_date*86400));

                    if($now>=$settlement_time){
                        $data=[
                            'status'=>2,
                            'settlement_time'=>time()
                        ];

                        Db::name('agent_order')->where(['id'=>$v['id']])->update($data);
                        //插入结算表
                        $data_settlement=[
                            'agent_order_id'=>$v['id'],
                            'cn_price'=>$v['money'],
                            'cn_type'=>'自动结算',
                            'settlement_date'=>date('Y-m-d H:i:s',time())
                        ];
                        Db::name('agent_settlement')->insert($data_settlement);
                    }
                }

            }
            //众筹结束
            $jcai_orders=Db::name('jcai_activity')->where(
                [
                    'audit'=>1,
                    'status'=>1,
                    'del'=>1
                ])->select()->toArray();
            if($jcai_orders){
                foreach ($jcai_orders as $k=>$v){
                    if($v['activity_end_time']<=time()){
                        //更新众筹活动状态
                        Db::name('jcai_activity')->where(['id'=>$v['id']])->update([
                            'status'=>2,
                            'update_time'=>time()
                        ]);
                    }
                }
            }


            //众筹退款
//            $jcai_order=Db::name('jcai_order')->where(['status'=>1,'pay_status'=>PayEnum::PAY_STATUS_PAID])->select()->toArray();
//            foreach ($jcai_order as $k=>$v){
//                if($v['end_time']<=time()){
////                    $data
//            )





            $time = time();
            $order_cancel_time = ConfigServer::get('transaction', 'unpaid_order_cancel_time', 60);
            // 配置0或为空时不取消订单
            if (empty($order_cancel_time)) {
                return true;
            }

            $order_cancel_time = $order_cancel_time * 60;

            $model = new Order();
            $order_list = $model->field(true)
                ->whereRaw("create_time+$order_cancel_time < $time")
                ->where([
                    ['order_type', '<>', OrderEnum::TEAM_ORDER],
                    ['order_status', '=', OrderEnum::ORDER_STATUS_NO_PAID],
                    ['pay_status', '=', OrderEnum::PAY_STATUS_NO_PAID]
                ])->with(['orderGoods'])
                ->select()->toArray();

            $order_ids          = []; //更新的订单
            $update_total_stock = []; //更新总库存
            $update_stock       = []; //更新规格库存
            $total_stock_num    = []; //总库存
            $stock_num          = []; //规格库存
            $update_coupon_ids  = []; //更新优惠券状态
            foreach ($order_list as $order) {
                $order_ids[] = $order['id'];
                //返回优惠券
                if ($order['coupon_list_id']) {
                    $update_coupon_ids[] = $order['coupon_list_id'];
                }

                foreach ($order['orderGoods'] as $order_goods) {
                    //更新商品总库存数据
                    if (isset($update_total_stock[$order_goods['goods_id']])) {
                        $total_stock_num[$order_goods['goods_id']] = $total_stock_num[$order_goods['goods_id']] + $order_goods['goods_num'];
                        $update_total_stock[$order_goods['goods_id']]['stock'] = Db::raw('stock+' . $total_stock_num[$order_goods['goods_id']]);
                    } else {
                        $total_stock_num[$order_goods['goods_id']] = $order_goods['goods_num'];
                        $update_total_stock[$order_goods['goods_id']] = [
                            'id' => $order_goods['goods_id'],
                            'stock' => Db::raw('stock+' . $total_stock_num[$order_goods['goods_id']])
                        ];
                    }
                    //更新商品规格库存数据
                    if (isset($update_stock[$order_goods['item_id']])) {
                        $stock_num[$order_goods['item_id']] = $stock_num[$order_goods['item_id']] + $order_goods['goods_num'];
                        $update_stock[$order_goods['item_id']]['stock'] = Db::raw('stock+' . $stock_num[$order_goods['item_id']]);
                    } else {
                        $stock_num[$order_goods['item_id']] = $order_goods['goods_num'];
                        $update_stock[$order_goods['item_id']] = [
                            'id' => $order_goods['item_id'],
                            'stock' => Db::raw('stock+' . $stock_num[$order_goods['item_id']])
                        ];
                    }
                }
            }

            // 更新订单状态为关闭
            if ($order_ids) {
                $update_data = [
                    'order_status' => OrderEnum::ORDER_STATUS_DOWN,
                    'update_time'  => $time,
                ];

                $model->where(['id' => $order_ids])->update($update_data);
            }


            //批量更新库存
            if($update_total_stock){
                (new Goods())->saveAll(array_values($update_total_stock));
                (new GoodsItem())->saveAll(array_values($update_stock));
            }

            if($update_coupon_ids){
                $update_coupon = [
                    'status'        => 0,
                    'use_time'      => '',
                    'order_id'      => '',
                    'update_time'   => $time,
                ];
                (new CouponList())->where(['id'=>$update_coupon_ids])->update($update_coupon);
            }

            // 删除超时未支付的代理保证金记录
            if (!empty($order_cancel_time)) {
                // 获取超时时间点
                $expire_time = $time - $order_cancel_time;

                // 查询超时未支付的代理保证金记录
                // 注意：这里假设 created_at 是 datetime 类型，需要使用 strtotime 转换为时间戳
                $expired_deposits = Db::name('agent_merchantfees')
                    ->where('status', 0)  // 未支付状态
                    ->where(function($query) use ($expire_time) {
                        // 两种情况：1. created_at 是 datetime 类型；2. created_at 是 int 类型
                        $query->whereTime('created_at', '<=', $expire_time)
                              ->whereOr('payment_date', '=', 0); // 未支付
                    })
                    ->select()
                    ->toArray();

                // 删除这些记录
                if (!empty($expired_deposits)) {
                    $deposit_ids = array_column($expired_deposits, 'id');
                    $delete_count = Db::name('agent_merchantfees')->whereIn('id', $deposit_ids)->delete();
                    // Log::write('自动删除超时未支付的代理保证金记录: ' . $delete_count . '条');
                }
            }

            return true;
        } catch (\Exception $e) {
            // Log::write('自动关闭订单异常:'.$e->getMessage());
            return false;
        }
    }
}