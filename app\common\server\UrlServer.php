<?php



namespace app\common\server;


/**
 * URL转换 服务类
 * Class UrlServer
 * @package app\common\server
 */
class UrlServer
{
    /**
     * Notes: 获取文件全路径
     * @param string $uri
     * <AUTHOR> 9:42)
     * @return string
     */
    public static function getFileUrl($uri='',$type='')
    {
        if (empty($uri)) return '';

        // 防护措施：如果传入的是数组，转换为字符串或返回空
        if (is_array($uri)) {
            // 如果是数组，尝试获取第一个元素
            if (!empty($uri) && isset($uri[0])) {
                $uri = is_array($uri[0]) ? ($uri[0]['abs_image'] ?? '') : $uri[0];
            } else {
                return '';
            }
        }

        // 确保$uri是字符串
        if (!is_string($uri)) {
            return '';
        }

        if (strstr($uri, 'http://'))  return $uri;
        if (strstr($uri, 'https://')) return $uri;

         $engine = ConfigServer::get('storage', 'default', 'local');
        // var_dump($engine);die;
        if (empty($engine) || $engine === 'local') {
            //图片分享处理
            if ($type && $type == 'share') {
                return ROOT_PATH . $uri;
            }
            $domain = request()->domain(true);
        } else {
            $config = ConfigServer::get('storage_engine',$engine);
            if ($engine === 'aliyun' && !empty($config['cdn_domain'])) {
                $domain = $config['cdn_domain'];
            } else {
            
                $domain = $config['domain'];
            }
        }
        return self::format($domain, $uri);
    }

    /**
     * Notes: 获取文件全路径Local
     * @param string $uri
     * <AUTHOR> 9:42)
     * @return string
     */
    public static function getFileUrlLocal($uri='',$type='')
    {
        if (empty($uri)) return '';
        if (strstr($uri, 'http://'))  return $uri;
        if (strstr($uri, 'https://')) return $uri;

         if ($type && $type == 'share') {
                return ROOT_PATH . $uri;
            }
        $domain = request()->domain(true);
        return self::format($domain, $uri);
    }

    /**
     * NOTE: 设置文件路径转相对路径
     * @author: 张无忌
     * @param string $uri
     * @return mixed
     */
    public static function setFileUrl($uri='')
    {
        $engine = ConfigServer::get('storage', 'default', 'local');
        if (empty($engine) || $engine === 'local') {
            $domain = request()->domain();
            return str_replace($domain.'/', '', $uri);
        } else {
            $config = ConfigServer::get('storage_engine', $engine);
            $domain = $config['domain'];
            if ($engine === 'aliyun' && !empty($config['cdn_domain'])) {
                $domain = $config['cdn_domain'];
            }
            return str_replace($domain, '', $uri);
        }
    }


    /**
     * @notes 处理域名
     * @param $domain
     * @param $uri
     * @return string
     * <AUTHOR>
     * @date 2022/6/6 15:41
     */
    public static function format($domain, $uri)
    {
        // 处理域名
        $domainLen = strlen($domain);
        $domainRight = substr($domain, $domainLen -1, 1);
        if ('/' == $domainRight) {
            $domain = substr_replace($domain,'',$domainLen -1, 1);
        }

        // 处理uri
        $uriLeft = substr($uri, 0, 1);
        if('/' == $uriLeft) {
            $uri = substr_replace($uri,'',0, 1);
        }

        return trim($domain) . '/' . trim($uri);
    }
}
