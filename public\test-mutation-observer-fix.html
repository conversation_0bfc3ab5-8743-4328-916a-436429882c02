<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MutationObserver修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        #more-spec-lists-table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        #more-spec-lists-table th,
        #more-spec-lists-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        #more-spec-lists-table th {
            background-color: #f2f2f2;
        }
        .btn {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .log-entry {
            margin: 2px 0;
        }
        .log-success { color: #28a745; }
        .log-error { color: #dc3545; }
        .log-info { color: #17a2b8; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 MutationObserver修复测试</h1>
        <p>这个页面用于测试修复后的MutationObserver功能，确保不再出现DOMNodeInserted弃用警告。</p>
        
        <div class="test-section">
            <h3>📊 测试表格</h3>
            <table id="more-spec-lists-table">
                <thead>
                    <tr>
                        <th>规格名称</th>
                        <th>规格值</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>颜色</td>
                        <td>红色</td>
                        <td><button class="btn" onclick="removeRow(this)">删除</button></td>
                    </tr>
                </tbody>
            </table>
            
            <button class="btn" onclick="addTableRow()">➕ 添加行</button>
            <button class="btn" onclick="clearTable()">🗑️ 清空表格</button>
        </div>
        
        <div class="test-section">
            <h3>📝 日志输出</h3>
            <div id="logContainer" class="log"></div>
            <button class="btn" onclick="clearLog()">清空日志</button>
        </div>
        
        <div class="test-section">
            <h3>🔍 浏览器控制台检查</h3>
            <p>请打开浏览器开发者工具的控制台，检查是否还有DOMNodeInserted相关的弃用警告。</p>
            <p>如果修复成功，应该不会再看到类似以下的警告：</p>
            <code>[Deprecation] Listener added for a 'DOMNodeInserted' mutation event...</code>
        </div>
    </div>

    <!-- 引入jQuery -->
    <script src="/static/plug/layui-admin/dist/layuiadmin/lib/jquery-3.4.1.min.js"></script>
    
    <!-- 引入修复后的goods-spec-enhanced.js -->
    <script src="/static/common/js/goods-spec-enhanced.js"></script>
    
    <script>
        // 日志函数
        function log(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.textContent = `[${timestamp}] ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
            
            // 同时输出到控制台
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
        
        // 添加表格行
        function addTableRow() {
            const tbody = document.querySelector('#more-spec-lists-table tbody');
            const newRow = document.createElement('tr');
            const colors = ['蓝色', '绿色', '黄色', '紫色', '橙色'];
            const randomColor = colors[Math.floor(Math.random() * colors.length)];
            
            newRow.innerHTML = `
                <td>颜色</td>
                <td>${randomColor}</td>
                <td><button class="btn" onclick="removeRow(this)">删除</button></td>
            `;
            
            tbody.appendChild(newRow);
            log(`添加了新行: ${randomColor}`, 'success');
        }
        
        // 删除表格行
        function removeRow(button) {
            const row = button.closest('tr');
            const color = row.cells[1].textContent;
            row.remove();
            log(`删除了行: ${color}`, 'info');
        }
        
        // 清空表格
        function clearTable() {
            const tbody = document.querySelector('#more-spec-lists-table tbody');
            tbody.innerHTML = '';
            log('清空了表格', 'info');
        }
        
        // 清空日志
        function clearLog() {
            document.getElementById('logContainer').innerHTML = '';
        }
        
        // 页面加载完成后初始化
        $(document).ready(function() {
            log('页面加载完成', 'success');
            log('MutationObserver修复版本已加载', 'success');
            
            // 监听表格重建事件
            $(document).on('tableRebuilt', function() {
                log('检测到表格重建事件', 'info');
            });
            
            // 测试MutationObserver是否正常工作
            setTimeout(function() {
                log('开始测试MutationObserver功能...', 'info');
                addTableRow();
            }, 2000);
        });
    </script>
</body>
</html>
