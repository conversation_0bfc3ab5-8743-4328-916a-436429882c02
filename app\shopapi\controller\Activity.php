<?php

namespace app\shopapi\controller;

use app\common\basics\ShopApi;
use app\common\server\JsonServer;
use app\shop\logic\jcai\ActivityLogic;
use app\shop\validate\TeamValidate;
use think\facade\Db;

class Activity extends ShopApi
{
    protected $logic;

    public function initialize()
    {
        parent::initialize();
        $this->logic = new ActivityLogic();
    }

    /**
     * @Notes: 拼团活动列表
     * @Author: 张无忌1
     */
    public function lists()
    {
        $get = $this->request->get();
        $result = $this->logic->lists($get, $this->shop_id);
        return (new JsonServer())->success('获取成功', $result);
    }


    /**
     * @Notes: 选择拼团商品
     * @Author: 张无忌
     */
    public function select()
    {
        $get = $this->request->get();
        $lists = $this->logic->select($get, $this->shop_id);
        return (new JsonServer())->success('获取成功', $lists);
    }

    /**
     * @Notes: 数据统计
     * @Author: 张无忌
     */
    public function statistics()
    {
        if ($this->request->isAjax()) {
            $params = $this->request->get();
            $statistics = $this->logic->getStatistics($this->shop_id, $params);
            return (new JsonServer())->success('获取成功', $statistics);
        }
        return (new JsonServer())->error('请求异常');
    }

    /**
     * @Notes: 拼团活动详细
     * @Author: 张无忌
     */
    public function detail()
    {
        $id = $this->request->get('id');
        $detail = $this->logic->detail($id, $this->shop_id);
        if (!$detail) {
            return (new JsonServer())->error($this->logic->getError() ?: '活动不存在或无权限查看');
        }
        // 组装枚举信息
        $detail['JcaiAuditEnum'] = (new \app\common\enum\JcaiAuditEnum());
        $detail['JcaiStatusEnum'] = (new \app\common\enum\JcaiStatusEnum());
        return (new JsonServer())->success('获取成功', $detail);
    }

    /**
     * @Notes: 新增拼团活动
     * @Author: 张无忌
     */
    public function add()
    {
        (new TeamValidate())->goCheck('add');
        $post = $this->request->post();
        $result = $this->logic->add($post, $this->shop_id);
        if ($result === false) {
            $message = $this->logic->getError() ?: '新增失败';
            return JsonServer::error($message);
        }
        return JsonServer::success('新增成功');
    }

    /**
     * @Notes: 编辑拼团活动
     * @Author: 张无忌
     */
    public function edit()
    {
        (new TeamValidate())->goCheck('edit');
        $post = $this->request->post();
        $result = $this->logic->edit($post, $this->shop_id);
        if ($result === false) {
            $message = $this->logic->getError() ?: '编辑失败';
            return JsonServer::error($message);
        }
        return JsonServer::success('编辑成功');
    }

    /**
     * @Notes: 删除拼团活动
     * @Author: 张无忌
     */
    public function del()
    {
       
          
            $id = $this->request->post('id');
            $result = $this->logic->del($id, $this->shop_id);
            if ($result === false) {
                $message = $this->logic->getError() ?: '删除失败';
                return JsonServer::error($message);
            }
            return JsonServer::success('删除成功');
       
    }

    /**
     * @Notes: 停止活动
     * @Author: 张无忌
     */
    public function stop()
    {
        (new TeamValidate())->goCheck('id');
        $id = $this->request->post('id');
        $result = $this->logic->stop($id);
        if ($result === false) {
            $message = $this->logic->getError() ?: '停止失败';
            return JsonServer::error($message);
        }
        return JsonServer::success('停止成功');
    }

    /**
     * @Notes: 开启拼团活动
     * @Author: 张无忌
     */
    public function open()
    {
        (new TeamValidate())->goCheck('id');
        $id = $this->request->post('id');
        $result = $this->logic->open($id);
        if ($result === false) {
            $message = $this->logic->getError() ?: '开启失败';
            return JsonServer::error($message);
        }
        return JsonServer::success('开启成功');
    }

    /**
     * @Notes: 商家端集采众筹订单列表
     * @Author: Cascade
     */
    public function jcaiCrowdList()
    {
        $get = $this->request->get();
        $result = $this->logic->jcaiCrowdList($get, $this->shop_id);
        return JsonServer::success('获取成功', $result);
    }

    /**
     * @Notes: 商家端集采众筹订单详情
     * @Author: Cascade
     */
    public function jcaiCrowdDetail()
    {
        $id = $this->request->get('id');
        $detail = $this->logic->jcaiCrowdDetail($id, $this->shop_id);
        if (!$detail) {
            return JsonServer::error($this->logic->getError() ?: '订单不存在或无权限查看');
        }
        return JsonServer::success('获取成功', $detail);
    }

    /*
     * agreementContent
     */
    public function agreementContent(){
        $data=Db::name('help')->where(['id'=>7])->value('content');
        return JsonServer::success('获取成功', $data);
    }
}
