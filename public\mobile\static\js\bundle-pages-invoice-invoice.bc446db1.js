(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["bundle-pages-invoice-invoice"],{"0c5b":function(e,t,i){"use strict";var n=i("9a30"),a=i.n(n);a.a},"12d8":function(e,t,i){"use strict";i.d(t,"b",(function(){return a})),i.d(t,"c",(function(){return o})),i.d(t,"a",(function(){return n}));var n={uIcon:i("6976").default},a=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("v-uni-view",{staticClass:"u-input",class:{"u-input--border":e.border,"u-input--error":e.validateState},style:{padding:"0 "+(e.border?20:0)+"rpx",borderColor:e.borderColor,textAlign:e.inputAlign},on:{click:function(t){t.stopPropagation(),arguments[0]=t=e.$handleEvent(t),e.inputClick.apply(void 0,arguments)}}},["textarea"==e.type?i("v-uni-textarea",{staticClass:"u-input__input u-input__textarea",style:[e.getStyle],attrs:{value:e.defaultValue,placeholder:e.placeholder,placeholderStyle:e.placeholderStyle,disabled:e.disabled,maxlength:e.inputMaxlength,fixed:e.fixed,focus:e.focus,autoHeight:e.autoHeight,"selection-end":e.uSelectionEnd,"selection-start":e.uSelectionStart,"cursor-spacing":e.getCursorSpacing,"show-confirm-bar":e.showConfirmbar},on:{input:function(t){arguments[0]=t=e.$handleEvent(t),e.handleInput.apply(void 0,arguments)},blur:function(t){arguments[0]=t=e.$handleEvent(t),e.handleBlur.apply(void 0,arguments)},focus:function(t){arguments[0]=t=e.$handleEvent(t),e.onFocus.apply(void 0,arguments)},confirm:function(t){arguments[0]=t=e.$handleEvent(t),e.onConfirm.apply(void 0,arguments)}}}):i("v-uni-input",{staticClass:"u-input__input",style:[e.getStyle],attrs:{type:"password"==e.type?"text":e.type,value:e.defaultValue,password:"password"==e.type&&!e.showPassword,placeholder:e.placeholder,placeholderStyle:e.placeholderStyle,disabled:e.disabled||"select"===e.type,maxlength:e.inputMaxlength,focus:e.focus,confirmType:e.confirmType,"cursor-spacing":e.getCursorSpacing,"selection-end":e.uSelectionEnd,"selection-start":e.uSelectionStart,"show-confirm-bar":e.showConfirmbar},on:{focus:function(t){arguments[0]=t=e.$handleEvent(t),e.onFocus.apply(void 0,arguments)},blur:function(t){arguments[0]=t=e.$handleEvent(t),e.handleBlur.apply(void 0,arguments)},input:function(t){arguments[0]=t=e.$handleEvent(t),e.handleInput.apply(void 0,arguments)},confirm:function(t){arguments[0]=t=e.$handleEvent(t),e.onConfirm.apply(void 0,arguments)}}}),i("v-uni-view",{staticClass:"u-input__right-icon u-flex"},[e.clearable&&""!=e.value&&e.focused?i("v-uni-view",{staticClass:"u-input__right-icon__clear u-input__right-icon__item",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.onClear.apply(void 0,arguments)}}},[i("u-icon",{attrs:{size:"32",name:"close-circle-fill",color:"#c0c4cc"}})],1):e._e(),e.passwordIcon&&"password"==e.type?i("v-uni-view",{staticClass:"u-input__right-icon__clear u-input__right-icon__item"},[i("u-icon",{attrs:{size:"32",name:e.showPassword?"eye-fill":"eye",color:"#c0c4cc"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.showPassword=!e.showPassword}}})],1):e._e(),"select"==e.type?i("v-uni-view",{staticClass:"u-input__right-icon--select u-input__right-icon__item",class:{"u-input__right-icon--select--reverse":e.selectOpen}},[i("u-icon",{attrs:{name:"arrow-down-fill",size:"26",color:"#c0c4cc"}})],1):e._e()],1)],1)},o=[]},3917:function(e,t,i){"use strict";i.r(t);var n=i("508a"),a=i.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){i.d(t,e,(function(){return n[e]}))}(o);t["default"]=a.a},3984:function(e,t,i){"use strict";i.r(t);var n=i("7630"),a=i.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){i.d(t,e,(function(){return n[e]}))}(o);t["default"]=a.a},"3ee8":function(e,t,i){"use strict";i("7a82");var n=i("ee27").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,i("a9e3"),i("498a");var a=n(i("3b7e")),o={name:"u-input",mixins:[a.default],props:{value:{type:[String,Number],default:""},type:{type:String,default:"text"},inputAlign:{type:String,default:"left"},placeholder:{type:String,default:"请输入内容"},disabled:{type:Boolean,default:!1},maxlength:{type:[Number,String],default:140},placeholderStyle:{type:String,default:"color: #c0c4cc;"},confirmType:{type:String,default:"done"},customStyle:{type:Object,default:function(){return{}}},fixed:{type:Boolean,default:!1},focus:{type:Boolean,default:!1},passwordIcon:{type:Boolean,default:!0},border:{type:Boolean,default:!1},borderColor:{type:String,default:"#dcdfe6"},autoHeight:{type:Boolean,default:!0},selectOpen:{type:Boolean,default:!1},height:{type:[Number,String],default:""},clearable:{type:Boolean,default:!0},cursorSpacing:{type:[Number,String],default:0},selectionStart:{type:[Number,String],default:-1},selectionEnd:{type:[Number,String],default:-1},trim:{type:Boolean,default:!0},showConfirmbar:{type:Boolean,default:!0}},data:function(){return{defaultValue:this.value,inputHeight:70,textareaHeight:100,validateState:!1,focused:!1,showPassword:!1,lastValue:""}},watch:{value:function(e,t){this.defaultValue=e,e!=t&&"select"==this.type&&this.handleInput({detail:{value:e}})}},computed:{inputMaxlength:function(){return Number(this.maxlength)},getStyle:function(){var e={};return e.minHeight=this.height?this.height+"rpx":"textarea"==this.type?this.textareaHeight+"rpx":this.inputHeight+"rpx",e=Object.assign(e,this.customStyle),e},getCursorSpacing:function(){return Number(this.cursorSpacing)},uSelectionStart:function(){return String(this.selectionStart)},uSelectionEnd:function(){return String(this.selectionEnd)}},created:function(){this.$on("on-form-item-error",this.onFormItemError)},methods:{handleInput:function(e){var t=this,i=e.detail.value;this.trim&&(i=this.$u.trim(i)),this.$emit("input",i),this.defaultValue=i,setTimeout((function(){t.dispatch("u-form-item","on-form-change",i)}),40)},handleBlur:function(e){var t=this;setTimeout((function(){t.focused=!1}),100),this.$emit("blur",e.detail.value),setTimeout((function(){t.dispatch("u-form-item","on-form-blur",e.detail.value)}),40)},onFormItemError:function(e){this.validateState=e},onFocus:function(e){this.focused=!0,this.$emit("focus")},onConfirm:function(e){this.$emit("confirm",e.detail.value)},onClear:function(e){this.$emit("input","")},inputClick:function(){this.$emit("click")}}};t.default=o},"448d":function(e,t,i){var n=i("24fb");t=n(!1),t.push([e.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.u-checkbox[data-v-0b68f884]{display:inline-flex;align-items:center;overflow:hidden;-webkit-user-select:none;user-select:none;line-height:1.8}.u-checkbox__icon-wrap[data-v-0b68f884]{color:#606266;flex:none;display:-webkit-flex;display:flex;flex-direction:row;align-items:center;justify-content:center;box-sizing:border-box;width:%?42?%;height:%?42?%;color:transparent;text-align:center;transition-property:color,border-color,background-color;font-size:20px;border:1px solid #c8c9cc;transition-duration:.2s}.u-checkbox__icon-wrap--circle[data-v-0b68f884]{border-radius:100%}.u-checkbox__icon-wrap--square[data-v-0b68f884]{border-radius:%?6?%}.u-checkbox__icon-wrap--checked[data-v-0b68f884]{color:#fff;background-color:#ff2c3c;border-color:#ff2c3c}.u-checkbox__icon-wrap--disabled[data-v-0b68f884]{background-color:#ebedf0;border-color:#c8c9cc}.u-checkbox__icon-wrap--disabled--checked[data-v-0b68f884]{color:#c8c9cc!important}.u-checkbox__label[data-v-0b68f884]{word-wrap:break-word;margin-left:%?10?%;margin-right:%?24?%;color:#606266;font-size:%?30?%}.u-checkbox__label--disabled[data-v-0b68f884]{color:#c8c9cc}',""]),e.exports=t},"508a":function(e,t,i){"use strict";i("7a82"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,i("a9e3"),i("14d9"),i("d81d");var n={name:"u-checkbox",props:{name:{type:[String,Number],default:""},shape:{type:String,default:""},value:{type:Boolean,default:!1},disabled:{type:[String,Boolean],default:""},labelDisabled:{type:[String,Boolean],default:""},activeColor:{type:String,default:""},iconSize:{type:[String,Number],default:""},labelSize:{type:[String,Number],default:""},size:{type:[String,Number],default:""}},data:function(){return{parentDisabled:!1,newParams:{}}},created:function(){this.parent=this.$u.$parent.call(this,"u-checkbox-group"),this.parent&&this.parent.children.push(this)},computed:{isDisabled:function(){return""!==this.disabled?this.disabled:!!this.parent&&this.parent.disabled},isLabelDisabled:function(){return""!==this.labelDisabled?this.labelDisabled:!!this.parent&&this.parent.labelDisabled},checkboxSize:function(){return this.size?this.size:this.parent?this.parent.size:34},checkboxIconSize:function(){return this.iconSize?this.iconSize:this.parent?this.parent.iconSize:20},elActiveColor:function(){return this.activeColor?this.activeColor:this.parent?this.parent.activeColor:"primary"},elShape:function(){return this.shape?this.shape:this.parent?this.parent.shape:"square"},iconStyle:function(){var e={};return this.elActiveColor&&this.value&&!this.isDisabled&&(e.borderColor=this.elActiveColor,e.backgroundColor=this.elActiveColor),e.width=this.$u.addUnit(this.checkboxSize),e.height=this.$u.addUnit(this.checkboxSize),e},iconColor:function(){return this.value?"#ffffff":"transparent"},iconClass:function(){var e=[];return e.push("u-checkbox__icon-wrap--"+this.elShape),1==this.value&&e.push("u-checkbox__icon-wrap--checked"),this.isDisabled&&e.push("u-checkbox__icon-wrap--disabled"),this.value&&this.isDisabled&&e.push("u-checkbox__icon-wrap--disabled--checked"),e.join(" ")},checkboxStyle:function(){var e={};return this.parent&&this.parent.width&&(e.width=this.parent.width,e.flex="0 0 ".concat(this.parent.width)),this.parent&&this.parent.wrap&&(e.width="100%",e.flex="0 0 100%"),e}},methods:{onClickLabel:function(){this.isLabelDisabled||this.isDisabled||this.setValue()},toggle:function(){this.isDisabled||this.setValue()},emitEvent:function(){var e=this;this.$emit("change",{value:!this.value,name:this.name}),setTimeout((function(){e.parent&&e.parent.emitEvent&&e.parent.emitEvent()}),80)},setValue:function(){var e=0;if(this.parent&&this.parent.children&&this.parent.children.map((function(t){t.value&&e++})),1==this.value)this.emitEvent(),this.$emit("input",!this.value);else{if(this.parent&&e>=this.parent.max)return this.$u.toast("最多可选".concat(this.parent.max,"项"));this.emitEvent(),this.$emit("input",!this.value)}}}};t.default=n},5668:function(e,t,i){"use strict";i.d(t,"b",(function(){return a})),i.d(t,"c",(function(){return o})),i.d(t,"a",(function(){return n}));var n={uIcon:i("6976").default,uRadioGroup:i("5bd2").default,uRadio:i("5f34").default,uInput:i("fb42").default,uPopup:i("5676").default,uCheckbox:i("e6a5").default},a=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("v-uni-view",{staticClass:"invoice"},[i("v-uni-view",{staticClass:"box bg-white"},[i("v-uni-view",{staticClass:"item flex row-between",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.handleOpenInvoiceType.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"label"},[e._v("发票类型")]),i("v-uni-view",{staticClass:"content flex-1 flex row-right"},[i("v-uni-text",[e._v(e._s(0==e.type?"增值税电子普通发票":"增值税专用发票"))]),1==e.formData.header_type?i("u-icon",{attrs:{name:"arrow-right",size:"22"}}):e._e()],1)],1),i("v-uni-view",{staticClass:"item flex row-between"},[i("v-uni-view",{staticClass:"label"},[e._v("抬头类型")]),i("v-uni-view",{staticClass:"content flex-1 flex row-right"},[i("u-radio-group",{model:{value:e.formData.header_type,callback:function(t){e.$set(e.formData,"header_type",t)},expression:"formData.header_type"}},e._l(e.list,(function(t,n){return i("u-radio",{key:n,attrs:{name:t.header_type,disabled:t.disabled,"active-color":e.colorConfig.primary}},[e._v(e._s(t.name))])})),1)],1)],1),i("v-uni-view",{staticClass:"item flex row-between"},[i("v-uni-view",{staticClass:"label"},[e._v("发票抬头")]),i("v-uni-view",{staticClass:"content"},[i("u-input",{attrs:{"input-align":"right",placeholder:"填写需要开具发票的姓名"},model:{value:e.formData.name,callback:function(t){e.$set(e.formData,"name",t)},expression:"formData.name"}})],1)],1),i("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:1===e.formData.header_type,expression:"formData.header_type === 1"}],staticClass:"item flex row-between"},[i("v-uni-view",{staticClass:"label"},[e._v("税号")]),i("v-uni-view",{staticClass:"content flex-1 flex row-right"},[i("u-input",{attrs:{"input-align":"right",placeholder:"纳税人识别号"},model:{value:e.formData.duty_number,callback:function(t){e.$set(e.formData,"duty_number",t)},expression:"formData.duty_number"}})],1)],1),i("v-uni-view",{staticClass:"item flex row-between"},[i("v-uni-view",{staticClass:"label"},[e._v("邮箱")]),i("v-uni-view",{staticClass:"content flex-1 flex row-right"},[i("u-input",{attrs:{"input-align":"right",placeholder:"您的联系邮箱"},model:{value:e.formData.email,callback:function(t){e.$set(e.formData,"email",t)},expression:"formData.email"}})],1)],1),i("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:1==e.type&&1==e.formData.header_type,expression:"type == 1 && formData.header_type == 1"}],staticClass:"item flex row-between"},[i("v-uni-view",{staticClass:"label"},[e._v("企业地址")]),i("v-uni-view",{staticClass:"content flex-1 flex row-right"},[i("u-input",{attrs:{"input-align":"right",placeholder:"必填"},model:{value:e.formData.address,callback:function(t){e.$set(e.formData,"address",t)},expression:"formData.address"}})],1)],1),i("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:1==e.type&&1==e.formData.header_type,expression:"type == 1 && formData.header_type == 1"}],staticClass:"item flex row-between"},[i("v-uni-view",{staticClass:"label"},[e._v("企业电话")]),i("v-uni-view",{staticClass:"content flex-1 flex row-right"},[i("u-input",{attrs:{"input-align":"right",placeholder:"必填"},model:{value:e.formData.mobile,callback:function(t){e.$set(e.formData,"mobile",t)},expression:"formData.mobile"}})],1)],1),i("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:1==e.type&&1==e.formData.header_type,expression:"type == 1 && formData.header_type == 1"}],staticClass:"item flex row-between"},[i("v-uni-view",{staticClass:"label"},[e._v("开户银行")]),i("v-uni-view",{staticClass:"content flex-1 flex row-right"},[i("u-input",{attrs:{"input-align":"right",placeholder:"必填"},model:{value:e.formData.bank,callback:function(t){e.$set(e.formData,"bank",t)},expression:"formData.bank"}})],1)],1),i("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:1==e.type&&1==e.formData.header_type,expression:"type == 1 && formData.header_type == 1"}],staticClass:"item flex row-between"},[i("v-uni-view",{staticClass:"label"},[e._v("银行账号")]),i("v-uni-view",{staticClass:"content flex-1 flex row-right"},[i("u-input",{attrs:{"input-align":"right",placeholder:"必填"},model:{value:e.formData.bank_account,callback:function(t){e.$set(e.formData,"bank_account",t)},expression:"formData.bank_account"}})],1)],1)],1),i("v-uni-view",{staticClass:"footer"},[e.order_id&&e.shop_id?i("v-uni-view",{},[i("v-uni-button",{staticClass:"submit-btn br60 white btn",attrs:{size:"lg"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.handleInvoiceAdd.apply(void 0,arguments)}}},[e._v("提交申请")])],1):e._e(),e.invoice_id&&e.shop_id?i("v-uni-view",{},[i("v-uni-button",{staticClass:"submit-btn br60 white btn",attrs:{size:"lg"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.handleInvoiceEdit.apply(void 0,arguments)}}},[e._v("提交编辑")])],1):e._e(),!e.shop_id||e.invoice_id||e.order_id?e._e():i("v-uni-view",{},[i("v-uni-button",{staticClass:"submit-btn br60 white btn",attrs:{size:"lg"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.handleConfirm.apply(void 0,arguments)}}},[e._v("确定")])],1),i("v-uni-view",{staticClass:"m-t-40"},[i("v-uni-button",{staticClass:"cancel-btn br60 white btn",attrs:{size:"lg"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.handleCancel.apply(void 0,arguments)}}},[e._v("不开发票")])],1)],1),i("u-popup",{attrs:{"border-radius":"14",mode:"bottom",closeable:!0,"safe-area-inset-bottom":!0},on:{open:function(t){arguments[0]=t=e.$handleEvent(t),e.type=e.formData.type},close:function(t){arguments[0]=t=e.$handleEvent(t),e.handleCloseInvoiceType.apply(void 0,arguments)}},model:{value:e.showCoupon,callback:function(t){e.showCoupon=t},expression:"showCoupon"}},[i("v-uni-view",{staticClass:"p-30 bg-body"},[i("v-uni-view",{staticClass:"text-center"},[e._v("发票类型选择")])],1),i("v-uni-view",{staticClass:"invoice-type bg-body"},[i("v-uni-view",{staticClass:"invoice-type--item bg-white flex row-between",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.type=0}}},[i("v-uni-view",[i("v-uni-view",{staticClass:"nr black"},[e._v("增值税电子普通发票")]),i("v-uni-view",{staticClass:"xs lighter m-t-14"},[e._v("默认发送至所提供的电子邮件")])],1),i("u-checkbox",{attrs:{value:0==e.type,shape:"circle",name:"0"}})],1),i("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:e.spec_invoice,expression:"spec_invoice"}],staticClass:"invoice-type--item bg-white flex row-between",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.type=1}}},[i("v-uni-view",[i("v-uni-view",{staticClass:"nr black"},[e._v("增值税专用发票")]),i("v-uni-view",{staticClass:"xs lighter m-t-14"},[e._v("纸质发票开出后将以邮寄形式交付")])],1),i("u-checkbox",{attrs:{value:1==e.type,shape:"circle",name:"1"}})],1),i("v-uni-view",{staticClass:"invoice-type-confirm-btn"},[i("v-uni-button",{staticClass:"confirm br60 white btn",attrs:{size:"lg"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.handleChoise.apply(void 0,arguments)}}},[e._v("确定")])],1)],1)],1)],1)},o=[]},"5d37":function(e,t,i){var n=i("a928");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[e.i,n,""]]),n.locals&&(e.exports=n.locals);var a=i("4f06").default;a("9e161c4e",n,!0,{sourceMap:!1,shadowMode:!1})},"5e23":function(e,t,i){var n=i("24fb");t=n(!1),t.push([e.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.u-input[data-v-2f408484]{position:relative;flex:1;display:flex;flex-direction:row}.u-input__input[data-v-2f408484]{font-size:%?28?%;color:#303133;flex:1}.u-input__textarea[data-v-2f408484]{width:auto;font-size:%?28?%;color:#303133;padding:%?10?% 0;line-height:normal;flex:1}.u-input--border[data-v-2f408484]{border-radius:%?6?%;border-radius:4px;border:1px solid #dcdfe6}.u-input--error[data-v-2f408484]{border-color:#fa3534!important}.u-input__right-icon__item[data-v-2f408484]{margin-left:%?10?%}.u-input__right-icon--select[data-v-2f408484]{transition:-webkit-transform .4s;transition:transform .4s;transition:transform .4s,-webkit-transform .4s}.u-input__right-icon--select--reverse[data-v-2f408484]{-webkit-transform:rotate(-180deg);transform:rotate(-180deg)}',""]),e.exports=t},"6baf":function(e,t,i){"use strict";i.r(t);var n=i("3ee8"),a=i.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){i.d(t,e,(function(){return n[e]}))}(o);t["default"]=a.a},7161:function(e,t,i){"use strict";i("7a82");var n=i("ee27").default;Object.defineProperty(t,"__esModule",{value:!0}),t.apiInvoiceAdd=function(e){return a.default.post("order_invoice/add",e)},t.apiInvoiceDetail=function(e){return a.default.get("order_invoice/detail",{params:e})},t.apiInvoiceEdit=function(e){return a.default.post("order_invoice/edit",e)},t.apiOrderInvoiceDetail=function(e){return a.default.get("order/invoice",{params:e})},t.changeShopFollow=function(e){return a.default.post("shop_follow/changeStatus",e)},t.getInvoiceSetting=function(e){return a.default.get("order_invoice/setting",{params:e})},t.getNearbyShops=function(e){return a.default.get("shop/getNearbyShops",{params:e})},t.getShopCategory=function(){return a.default.get("shop_category/getList")},t.getShopGoodsCategory=function(e){return a.default.get("shop_goods_category/getShopGoodsCategory",{params:e})},t.getShopInfo=function(e){return a.default.get("shop/getShopInfo",{params:e})},t.getShopList=function(e){return a.default.get("shop/getShopList",{params:e})},t.getShopService=function(e){return a.default.get("setting/getShopCustomerService",{params:{shop_id:e}})},t.getTreaty=function(){return a.default.get("ShopApply/getTreaty")},t.shopApply=function(e){return a.default.post("ShopApply/apply",e)},t.shopApplyDetail=function(e){return a.default.get("ShopApply/detail",{params:{id:e}})},t.shopApplyRecord=function(e){return a.default.get("ShopApply/record",{params:e})};var a=n(i("2774"));i("a5ae")},7630:function(e,t,i){"use strict";i("7a82");var n=i("ee27").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=n(i("f3f3"));i("ac1f"),i("00b4");var o=i("7161"),r=i("753f"),s={data:function(){return{shop_id:"",invoice_id:"",order_id:"",showCoupon:!1,list:[{name:"个人",header_type:0,disabled:!1},{name:"企业",header_type:1,disabled:!1}],formData:{id:"",order_id:"",type:0,header_type:0,name:"",duty_number:"",email:"",mobile:"",address:"",bank:"",bank_account:""},type:0,open_invoice:0,spec_invoice:0}},methods:{initInvoiceSetting:function(){var e=this;(0,o.getInvoiceSetting)({shop_id:this.shop_id}).then((function(t){e.spec_invoice=t.data.spec_invoice,t.data.open_invoice?e.open_invoice=t.data.open_invoice:e.$toast({title:"当前店铺暂未开启发票"})}))},getInvoiceDetail:function(){var e=this;(0,o.apiInvoiceDetail)({id:this.invoice_id}).then((function(t){for(var i in t.data)e.formData.hasOwnProperty(i)&&(e.formData[i]=t.data[i])}))},initRules:function(){var e=this.formData;return this.open_invoice?e.name?e.duty_number||1!=e.header_type?e.email?/^[0-9a-zA-Z_.-]+[@][0-9a-zA-Z_.-]+([.][a-zA-Z]+){1,2}$/.test(e.email)?e.address||1!=e.header_type||1!=e.type?e.mobile||1!=e.type?e.bank||1!=e.header_type||1!=e.type?!(!e.bank_account&&1==e.header_type&&1==e.type)||(this.$toast({title:"请输入银行账号"}),!1):(this.$toast({title:"请输入开户银行"}),!1):(this.$toast({title:"请输入手机号码"}),!1):(this.$toast({title:"请输入企业地址"}),!1):(this.$toast({title:"邮箱输入有误,请重新输入"}),!1):(this.$toast({title:"请输入邮箱地址"}),!1):(this.$toast({title:"请输入税号"}),!1):(this.$toast({title:"请输入发票抬头"}),!1):(this.$toast({title:"当前店铺暂未开启发票"}),!1)},handleOpenInvoiceType:function(){0!=this.formData.header_type&&(this.showCoupon=!0)},handleCloseInvoiceType:function(){this.type!=this.formData.type&&(this.type=1==this.type?0:1)},handleChoise:function(){this.formData.type=this.type,this.showCoupon=!1},handleConfirm:function(){this.initRules()&&(uni.$emit("invoice",(0,a.default)((0,a.default)({},this.formData),{},{shop_id:this.shop_id})),uni.navigateBack())},handleInvoiceAdd:function(){this.initRules()&&(0,o.apiInvoiceAdd)((0,a.default)((0,a.default)({},this.formData),{},{order_id:this.order_id})).then((function(e){1==e.code&&setTimeout((function(){uni.navigateBack()}),500)}))},handleInvoiceEdit:function(){this.initRules()&&(0,o.apiInvoiceEdit)((0,a.default)({},this.formData)).then((function(e){1==e.code&&setTimeout((function(){uni.navigateBack()}),500)}))},handleCancel:function(){this.order_id||this.invoice_id||uni.$emit("invoice",{del:!0,shop_id:this.shop_id}),uni.navigateBack()}},onLoad:function(){var e=this.$Route.query;switch(1*e.type){case r.invoiceType["SETTLEMENT"]:this.shop_id=e.shop_id||"","{}"!=e.invoice&&(this.formData=JSON.parse(e.invoice),this.type=this.formData.type);break;case r.invoiceType["ORDERDETAILEdit"]:this.invoice_id=e.invoice_id||"",this.shop_id=e.shop_id||"",this.getInvoiceDetail();break;case r.invoiceType["ORDERDETAILADD"]:this.order_id=e.order_id||"",this.shop_id=e.shop_id||"";break}this.initInvoiceSetting()}};t.default=s},"797b":function(e,t,i){"use strict";var n=i("5d37"),a=i.n(n);a.a},"9a30":function(e,t,i){var n=i("448d");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[e.i,n,""]]),n.locals&&(e.exports=n.locals);var a=i("4f06").default;a("62f4fc91",n,!0,{sourceMap:!1,shadowMode:!1})},a7d3:function(e,t,i){"use strict";i.r(t);var n=i("5668"),a=i("3984");for(var o in a)["default"].indexOf(o)<0&&function(e){i.d(t,e,(function(){return a[e]}))}(o);i("797b");var r=i("f0c5"),s=Object(r["a"])(a["default"],n["b"],n["c"],!1,null,"163c292c",null,!1,n["a"],void 0);t["default"]=s.exports},a928:function(e,t,i){var n=i("24fb");t=n(!1),t.push([e.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.invoice[data-v-163c292c]{height:calc(100vh - env(safe-area-inset-bottom));padding:%?24?%;position:relative}.invoice .box[data-v-163c292c]{padding:%?20?% 0;border-radius:%?16?%}.invoice .box .item[data-v-163c292c]{padding:%?16?% %?30?%}.invoice .box .item .label[data-v-163c292c]{font-size:%?28?%;color:#101010}.invoice .box .item .content[data-v-163c292c]{min-width:%?440?%}.invoice .footer[data-v-163c292c]{left:0;bottom:0;width:100%;padding:%?24?%;position:absolute}.invoice .footer .btn[data-v-163c292c]{height:%?88?%}.invoice .footer .submit-btn[data-v-163c292c]{color:#fff;background-color:#e39b37}.invoice .footer .cancel-btn[data-v-163c292c]{color:#e39b37;background-color:#fff}.invoice .invoice-type[data-v-163c292c]{padding:%?40?%;padding-bottom:0}.invoice .invoice-type .invoice-type--item[data-v-163c292c]{padding:%?28?% %?36?%;border-radius:%?40?%;margin-bottom:%?30?%}.invoice .invoice-type .invoice-type-confirm-btn[data-v-163c292c]{padding:%?90?% 0 %?50?% 0}.invoice .invoice-type .invoice-type-confirm-btn .confirm[data-v-163c292c]{background-color:#ff9e1e}',""]),e.exports=t},e19f:function(e,t,i){"use strict";var n=i("ef57"),a=i.n(n);a.a},e6a5:function(e,t,i){"use strict";i.r(t);var n=i("ff7c"),a=i("3917");for(var o in a)["default"].indexOf(o)<0&&function(e){i.d(t,e,(function(){return a[e]}))}(o);i("0c5b");var r=i("f0c5"),s=Object(r["a"])(a["default"],n["b"],n["c"],!1,null,"0b68f884",null,!1,n["a"],void 0);t["default"]=s.exports},ef57:function(e,t,i){var n=i("5e23");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[e.i,n,""]]),n.locals&&(e.exports=n.locals);var a=i("4f06").default;a("2faed928",n,!0,{sourceMap:!1,shadowMode:!1})},fb42:function(e,t,i){"use strict";i.r(t);var n=i("12d8"),a=i("6baf");for(var o in a)["default"].indexOf(o)<0&&function(e){i.d(t,e,(function(){return a[e]}))}(o);i("e19f");var r=i("f0c5"),s=Object(r["a"])(a["default"],n["b"],n["c"],!1,null,"2f408484",null,!1,n["a"],void 0);t["default"]=s.exports},ff7c:function(e,t,i){"use strict";i.d(t,"b",(function(){return a})),i.d(t,"c",(function(){return o})),i.d(t,"a",(function(){return n}));var n={uIcon:i("6976").default},a=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("v-uni-view",{staticClass:"u-checkbox",style:[e.checkboxStyle]},[i("v-uni-view",{staticClass:"u-checkbox__icon-wrap",class:[e.iconClass],style:[e.iconStyle],on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.toggle.apply(void 0,arguments)}}},[i("u-icon",{staticClass:"u-checkbox__icon-wrap__icon",attrs:{name:"checkbox-mark",size:e.checkboxIconSize,color:e.iconColor}})],1),i("v-uni-view",{staticClass:"u-checkbox__label",style:{fontSize:e.$u.addUnit(e.labelSize)},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.onClickLabel.apply(void 0,arguments)}}},[e._t("default")],2)],1)},o=[]}}]);