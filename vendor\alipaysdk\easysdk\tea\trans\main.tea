import EasySDKKernel;

type @kernel = EasySDKKernel

init(kernel: EasySDKKernel) {
  @kernel = kernel;
}

model PayeeInfo {
  identity: string(name='identity'),
  identityType: string(name='identity_type'),
  name: string(name='identity_type')
}

model AlipayFundTransUniTransferResponse {
  httpBody: string(name='http_body', description='响应原始字符串'),
  code: string(name='code'),
  msg: string(name='msg'),
  subCode: string(name='sub_code'),
  subMsg: string(name='sub_msg'),

  outBizNo: string(name='out_biz_no'),
  orderId: string(name='order_id'),
  payFundOrderId: string(name='pay_fund_order_id'),
  status: string(name='status'),
  transDate: string(name='trans_date')
}

api transfer(outBizNo: string, transAmount: string, productCode: string, payeeInfo: PayeeInfo): AlipayFundTransUniTransferResponse {
  var systemParams: map[string]string = {
    method = 'alipay.trade.create',
    app_id = @kernel.getConfig("appId"),
    timestamp = @kernel.getTimestamp(),
    format = 'json',
    version = '1.0',
    alipay_sdk = @kernel.getSdkVersion(),
    charset = 'UTF-8',
    sign_type = @kernel.getConfig("signType"),
    app_cert_sn = @kernel.getMerchantCertSN(),
    alipay_root_cert_sn = @kernel.getAlipayRootCertSN()
  };

  var bizParams: map[string]any = {
    out_biz_no = outBizNo,
    trans_amount = transAmount,
    product_code = productCode,
    payee_info = payeeInfo
  };

  var textParams: map[string]string = {
  };

  __request.protocol = @kernel.getConfig("protocol");
  __request.method = 'POST';
  __request.pathname = '/gateway.do';

  __request.headers = {
    host = @kernel.getConfig("gatewayHost"),
    content-type = 'application/x-www-form-urlencoded;charset=utf-8'
  };

  __request.query = @kernel.sortMap({
    sign = @kernel.sign(systemParams, bizParams, textParams, @kernel.getConfig("merchantPrivateKey")),
    ... systemParams,
    ... textParams
  });

  __request.body = @kernel.toUrlEncodedRequestBody(bizParams);
} returns {
  var respMap: map[string]any = @kernel.readAsJson(__response, "alipay.fund.trans.uni.transfer");

  if (@kernel.isCertMode()) {
    if (@kernel.verify(respMap, @kernel.extractAlipayPublicKey(@kernel.getAlipayCertSN(respMap)))) {
      return @kernel.toRespModel(respMap);
    }
  } else {
    if (@kernel.verify(respMap, @kernel.getConfig("alipayPublicKey"))) {
      return @kernel.toRespModel(respMap);
    }
  }
  
  throw {
    message = '验签失败，请检查支付宝公钥设置是否正确。'
  }
  
} runtime {
  ignoreSSL = @kernel.getConfig("ignoreSSL"),
  httpProxy = @kernel.getConfig("httpProxy"),
  connectTimeout = 15000,
  readTimeout = 15000,
  retry = {
    maxAttempts = 0
  }
}
