<?php

namespace app\common\model\shop;

use app\common\basics\Models;

/**
 * 商家移动端图标配置模型
 * Class ShopIconConfig
 * @package app\common\model\shop
 */
class ShopIconConfig extends Models
{
    protected $name = 'shop_icon_config';

    /**
     * 关联商家权限表
     * @return \think\model\relation\BelongsTo
     */
    public function shopAuth()
    {
        return $this->belongsTo(ShopAuth::class, 'auth_id', 'id');
    }
}
