<?php


namespace app\admin\logic\index;

use app\common\basics\Logic;
use app\common\server\UrlServer;
use app\common\enum\PayEnum;
use think\facade\Db;



/**
 * 工作台统计
 * Class StatLogic
 * @package app\admin\logic\index
 */
class StatLogic extends Logic
{
    //工作台基本数据 TODO
    public static function stat()
    {
        //更新时间
        $time = date('Y-m-d H:i:s', time());
        //头部数据统计
        $data = $where = [];
        $where[] = ['pay_status', '>', PayEnum::UNPAID];

        //成交笔数
        $order_num_all        = Db::name('order')
                                ->where($where)
                                ->count('id');
        $order_num_yesterday  = Db::name('order')
                                ->where($where)
                                ->whereTime('create_time', 'yesterday')
                                ->count('id');
        $order_num_today      = Db::name('order')
                                ->where($where)
                                ->whereTime('create_time', 'today')
                                ->count('id');
        $order_num_change_red = 0;
        $order_num_change_add = $order_num_today - $order_num_yesterday;
        if($order_num_change_add < 0){
            $order_num_change_red = abs($order_num_change_add);
        }

        //营业额
        $order_price_all       = Db::name('order')
                                ->where($where)
                                ->sum('order_amount');
        $order_price_yesterday = Db::name('order')
                                ->where($where)
                                ->whereTime('create_time', 'yesterday')
                                ->sum('order_amount');
        $order_price_today     = Db::name('order')
                                ->where($where)
                                ->whereTime('create_time', 'today')
                                ->sum('order_amount');
        $order_price_change_red = 0;
        $order_price_change_add = $order_price_today - $order_price_yesterday;
        if($order_price_change_add < 0){
            $order_price_change_red = abs($order_price_change_add);
        }

        //新增会员
        $add_user_all       = Db::name('user')
                                ->count('id');
        $add_user_yesterday = Db::name('user')
                                ->whereTime('create_time', 'yesterday')
                                ->count('id');
        $add_user_today     = Db::name('user')
                                ->whereTime('create_time', 'today')
                                ->count('id');
        $add_user_change_red = 0;
        $add_user_change_add = $add_user_today - $add_user_yesterday;
        if($add_user_change_add < 0){
            $add_user_change_red = abs($add_user_change_add);
        }

        //用户访问量UV
        $visit_user_all       = Db::name('stat')
                                ->group('ip')
                                ->count('id');
        $visit_user_yesterday = Db::name('stat')
                                ->whereTime('create_time', 'yesterday')
                                ->group('ip')
                                ->count('id');
        $visit_user_today     = Db::name('stat')
                                ->whereTime('create_time', 'today')
                                ->group('ip')
                                ->count('id');
        $visit_user_change_red = 0;
        $visit_user_change_add = $visit_user_today - $visit_user_yesterday;
        if($visit_user_change_add < 0){
            $visit_user_change_red = abs($visit_user_change_add);
        }

        // 待发货订单数 (状态为1，且已支付)
        $unshipped_where = [
            ['del', '=', 0],
            ['pay_status', '=', 1],
            ['order_status', '=', 1]
        ];

        $order_unshipped_all = Db::name('order')
            ->where($unshipped_where)
            ->count('id');
        $order_unshipped_yesterday = Db::name('order')
            ->where($unshipped_where)
            ->whereTime('create_time', 'yesterday')
            ->count('id');
        $order_unshipped_today = Db::name('order')
            ->where($unshipped_where)
            ->whereTime('create_time', 'today')
            ->count('id');
        $order_unshipped_change_red = 0;
        $order_unshipped_change_add = $order_unshipped_today - $order_unshipped_yesterday;
        if ($order_unshipped_change_add < 0) {
            $order_unshipped_change_red = abs($order_unshipped_change_add);
        }

        // 商圈数量统计
        $community_all = Db::name('community_article')
            ->where('del', 0)
            ->where('status', 1) // 假设状态1为已审核通过
            ->count('id');
        $community_yesterday = Db::name('community_article')
            ->where('del', 0)
            ->where('status', 1)
            ->whereTime('create_time', 'yesterday')
            ->count('id');
        $community_today = Db::name('community_article')
            ->where('del', 0)
            ->where('status', 1)
            ->whereTime('create_time', 'today')
            ->count('id');
        $community_change_red = 0;
        $community_change_add = $community_today - $community_yesterday;
        if ($community_change_add < 0) {
            $community_change_red = abs($community_change_add);
        }

        // 代理数量统计
        $agent_all = Db::name('agent')
            ->where('del', 0)
            ->count('id');
        $agent_yesterday = Db::name('agent')
            ->where('del', 0)
            ->whereTime('create_time', 'yesterday')
            ->count('id');
        $agent_today = Db::name('agent')
            ->where('del', 0)
            ->whereTime('create_time', 'today')
            ->count('id');
        $agent_change_red = 0;
        $agent_change_add = $agent_today - $agent_yesterday;
        if ($agent_change_add < 0) {
            $agent_change_red = abs($agent_change_add);
        }

        // 商家数量统计
        $shop_all = Db::name('shop')
            ->where('del', 0)
            ->count('id');
        $shop_yesterday = Db::name('shop')
            ->where('del', 0)
            ->whereTime('create_time', 'yesterday')
            ->count('id');
        $shop_today = Db::name('shop')
            ->where('del', 0)
            ->whereTime('create_time', 'today')
            ->count('id');
        $shop_change_red = 0;
        $shop_change_add = $shop_today - $shop_yesterday;
        if ($shop_change_add < 0) {
            $shop_change_red = abs($shop_change_add);
        }

        // 商品数量统计（在售商品）
        $goods_all = Db::name('goods')
            ->where('del', 0)
            ->where('status', 1) // 上架状态
            ->count('id');
        $goods_yesterday = Db::name('goods')
            ->where('del', 0)
            ->where('status', 1)
            ->whereTime('create_time', 'yesterday')
            ->count('id');
        $goods_today = Db::name('goods')
            ->where('del', 0)
            ->where('status', 1)
            ->whereTime('create_time', 'today')
            ->count('id');
        $goods_change_red = 0;
        $goods_change_add = $goods_today - $goods_yesterday;
        if ($goods_change_add < 0) {
            $goods_change_red = abs($goods_change_add);
        }

        // 售后申请统计 (状态为0，待审核)
        $aftersale_all = Db::name('after_sale')
            ->where('del', 0)
            ->where('status', 0) // 0=待审核
            ->count('id');
        $aftersale_yesterday = Db::name('after_sale')
            ->where('del', 0)
            ->whereTime('create_time', 'yesterday')
            ->count('id');
        $aftersale_today = Db::name('after_sale')
            ->where('del', 0)
            ->whereTime('create_time', 'today')
            ->count('id');
        $aftersale_change_red = 0;
        $aftersale_change_add = $aftersale_today - $aftersale_yesterday;
        if ($aftersale_change_add < 0) {
            $aftersale_change_red = abs($aftersale_change_add);
        }

        $data = [
            'order_num'         => [
                'yesterday'  => $order_num_yesterday,
                'today'      => $order_num_today,
                'change_add' => $order_num_change_add,
                'change_red' => $order_num_change_red,
                'all_num'    => $order_num_all
            ],
            'order_price'       => [
                'yesterday'  => number_format($order_price_yesterday,2),
                'today'      => number_format($order_price_today,2),
                'change_add' => number_format($order_price_change_add,2),
                'change_red' => number_format($order_price_change_red,2),
                'all_price'  => number_format($order_price_all,2)
            ],
            'add_user_num'      => [
                'yesterday'  => $add_user_yesterday,
                'today'      => $add_user_today,
                'change_add' => $add_user_change_add,
                'change_red' => $add_user_change_red,
                'all_num'    => $add_user_all
            ],
            'visit_user_num'    => [
                'yesterday'  => $visit_user_yesterday,
                'today'      => $visit_user_today,
                'change_add' => $visit_user_change_add,
                'change_red' => $visit_user_change_red,
                'all_num'    => $visit_user_all
            ],
            // 添加待发货订单数据
            'order_unshipped_num' => [
                'yesterday'  => $order_unshipped_yesterday,
                'today'      => $order_unshipped_today,
                'change_add' => $order_unshipped_change_add,
                'change_red' => $order_unshipped_change_red,
                'all_num'    => $order_unshipped_all
            ],
            // 添加商圈数据
            'community_num' => [
                'yesterday'  => $community_yesterday,
                'today'      => $community_today,
                'change_add' => $community_change_add,
                'change_red' => $community_change_red,
                'all_num'    => $community_all
            ],
            // 添加代理数据
            'agent_num' => [
                'yesterday'  => $agent_yesterday,
                'today'      => $agent_today,
                'change_add' => $agent_change_add,
                'change_red' => $agent_change_red,
                'all_num'    => $agent_all
            ],
            // 添加商家数据
            'shop_num' => [
                'yesterday'  => $shop_yesterday,
                'today'      => $shop_today,
                'change_add' => $shop_change_add,
                'change_red' => $shop_change_red,
                'all_num'    => $shop_all
            ],
            // 添加商品数据
            'goods_num' => [
                'yesterday'  => $goods_yesterday,
                'today'      => $goods_today,
                'change_add' => $goods_change_add,
                'change_red' => $goods_change_red,
                'all_num'    => $goods_all
            ],
            // 添加售后申请数据
            'aftersale_num' => [
                'yesterday'  => $aftersale_yesterday,
                'today'      => $aftersale_today,
                'change_add' => $aftersale_change_add,
                'change_red' => $aftersale_change_red,
                'all_num'    => $aftersale_all
            ],
        ];




        return [
            'time'      => $time,
            'data'      => $data,
        ];
    }


    //图标数据
    public static function graphData()
    {
        //当前时间戳
        $start_t = time();
        //echarts图表数据
        $echarts_order_amount = [];
        $echarts_user_pv = [];
        $dates = [];
        for ($i = 15; $i >= 1; $i--) {
            $where_start = strtotime("- ".$i."day", $start_t);
            $dates[] = date('m-d',$where_start);
            $start_now = strtotime(date('Y-m-d',$where_start));
            $end_now = strtotime(date('Y-m-d 23:59:59',$where_start));
            $amount = Db::name('order')
                    ->where([['create_time','between',[$start_now, $end_now]],['pay_status', '>', PayEnum::UNPAID]])
                    ->sum('order_amount');
            $pv = Db::name('stat')
                    ->where([['create_time','between',[$start_now, $end_now]]])
                    ->group('ip')
                    ->count('id');
            $echarts_order_amount[] = sprintf("%.2f",substr(sprintf("%.3f", $amount), 0, -2));
            $echarts_user_pv[] = $pv;
        }
        return [
            'echarts_order_amount'  => $echarts_order_amount,
            'echarts_user_visit'    => $echarts_user_pv,
            'dates'                 => $dates,
        ];
    }


    // 工作台商家数据
    public static function shopLists($get)
    {
        $shop_list = [];
        // 销冠商家、人气商家的商家列表
        if($get['type'] == 1){
            // 销冠商家
            $shop_list = Db::name('order')->alias('o')
                    ->join('shop s','s.id = o.shop_id')
                    ->where([['order_amount','>',0], ['pay_status', '>', PayEnum::UNPAID]])
                    ->group('shop_id')
                    ->limit(5)
                    ->order('order_amount desc')
                    ->column('o.shop_id,sum(o.order_amount) as order_amount,s.logo,s.name');
                    foreach($shop_list as $k => $shop){
                        $shop_list[$k]['number'] = $k+1;
                        $shop_list[$k]['order_amount'] = '￥'.number_format($shop['order_amount'],2);
                        $shop_list[$k]['logo'] = UrlServer::getFileUrl($shop['logo']);
                    }
        }else{
            // 人气商家
            $shop_list = Db::name('shop')
            ->order('visited_num desc')
            ->where([['visited_num','>',0]])
            ->limit(5)
            ->column('id,logo,name,visited_num');
            foreach($shop_list as $k => $shop){
                $shop_list[$k]['number'] = $k+1;
                $shop_list[$k]['logo'] = UrlServer::getFileUrl($shop['logo']);
            }
        }
        return ['count'=>0,'lists'=>$shop_list];
    }
}