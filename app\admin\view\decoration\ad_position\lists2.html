{layout name="layout1" /}

<div class="wrapper">
    <div class="layui-card">
        <!-- 操作提示 -->
        <div class="layui-card-body">
            <div class="layui-collapse" style="border:1px dashed #c4c4c4">
                <div class="layui-colla-item">
                    <h2 class="layui-colla-title like-layui-colla-title">操作提示</h2>
                    <div class="layui-colla-content layui-show">
                        <p>*平台管理配置位信息，系统默认了部分配置位，允许新建配置位。</p>
                        <p>*移动端商城含H5、小程序、APP。</p>
                        <p>*配置位停用时，该配置位所有配置都会隐藏。</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主体区域 -->
        <div class="layui-tab layui-tab-card" lay-filter="like-tabs">
            <ul class="layui-tab-title">
                <li lay-id="1" class="layui-this">移动端商城</li>
<!--                <li lay-id="2">PC端商城</li>-->
            </ul>
            <div class="layui-tab-content" style="padding: 6px 15px;">

                <button class="layui-btn layui-btn-sm layEvent {$view_theme_color}" lay-event="add" >新增配置位</button>
                <table id="like-table-lists" lay-filter="like-table-lists"></table>
                <script type="text/html" id="table-operation">
                    {{# if(3 !== d.attr){ }}
                    <a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="edit">编辑</a>
                    {{# } }}
                    {{# if(0 == d.status) { }}
                    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="status">启用</a>
                    {{# }else{ }}
                    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="status">停用</a>
                    {{# } }}
                    {{# if(1 == d.attr){ }}
                    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
                    {{# } }}
                </script>
            </div>
        </div>


    </div>
</div>

<script>
    layui.use(["table", "element", "laydate"], function(){
        var table   = layui.table;
        var element = layui.element;
        var laydate = layui.laydate;
        var terminal = 1;

        laydate.render({type:"datetime", elem:"#apply_start_time", trigger:"click"});
        laydate.render({type:"datetime", elem:"#apply_end_time", trigger:"click"});

        like.tableLists('#like-table-lists', '{:url()}', [
            {field: 'id', width: 60, title: 'ID'}
            ,{field: 'terminal_desc', width: 200, title: '终端'}
            ,{field: 'name', width: 260, align: 'center', title: '配置位'}

            ,{field: 'attr', width: 120, align: 'center',title: '配置位属性', templet: function(d){
                if(1 == d.attr){
                    return '自定义';
                }
                return  '系统默认';
            }}
            ,{field: 'ad_sn', width: 260, align: 'center', title: '区域码'}
            ,{field: 'ad_yn', width: 260, align: 'center', title: '区域码'}
            ,{field: 'status', width: 120, align: 'center', title: '配置位状态', templet: function(d){
                    if(1 == d.status){
                        return '开启';
                    }
                    return  '停用';
             }}
            ,{title: '操作', width: 180, align: 'center', fixed: 'right', toolbar: '#table-operation'}
        ],{terminal:terminal});

        element.on("tab(like-tabs)", function(){
            terminal = this.getAttribute("lay-id");
            table.reload("like-table-lists", {
                where: {terminal: terminal},
                page: { cur: 1 }
            });
        });

        var active = {
            add:function(obj){
                layer.open({
                    type: 2
                    ,title: '新增配置位'
                    ,content: '{:url("decoration.AdPosition/add2")}'
                    ,area: ['90%', '90%']
                    ,btn: ['确定', '取消']
                    ,yes: function(index, layero){
                        var iframeWindow = window['layui-layer-iframe'+ index]
                            ,submitID = 'addSubmit'
                            ,submit = layero.find('iframe').contents().find('#'+ submitID);
                        //监听提交
                        iframeWindow.layui.form.on('submit('+ submitID +')', function(data){
                            var field = data.field;
                            field['terminal'] = terminal,
                            like.ajax({
                                url:'{:url("decoration.AdPosition/add2")}',
                                data:field,
                                type:"post",
                                success:function(res)
                                {
                                    if(res.code == 1) {
                                        layui.layer.msg(res.msg);
                                        layer.close(index);
                                        table.reload('like-table-lists');
                                    }
                                }
                            });
                        });
                        submit.trigger('click');
                    }
                });

            },
            edit:function(obj){
                var id = obj.data.id;
                layer.open({
                    type: 2
                    ,title: '编辑配置位'
                    ,content: '{:url("decoration.AdPosition/edit2")}?id='+id
                    ,area: ['90%', '90%']
                    ,btn: ['确定', '取消']
                    ,yes: function(index, layero){
                        var iframeWindow = window['layui-layer-iframe'+ index]
                            ,submitID = 'editSubmit'
                            ,submit = layero.find('iframe').contents().find('#'+ submitID);
                        //监听提交
                        iframeWindow.layui.form.on('submit('+ submitID +')', function(data){
                            var field = data.field;
                                like.ajax({
                                    url:'{:url("decoration.AdPosition/edit2")}',
                                    data:field,
                                    type:"post",
                                    success:function(res)
                                    {
                                        if(res.code == 1) {
                                            layui.layer.msg(res.msg);
                                            layer.close(index);
                                            table.reload('like-table-lists');
                                        }
                                    }
                                });
                        });
                        submit.trigger('click');
                    }
                });
            },
            status: function(obj) {
                var status =   1 == obj.data.status ? 0 : 1;
                if(status){
                    var tips = "确定启用配置位:<span style='color: red'>"+ obj.data.name +"</span>";
                }else{
                    var tips = "确定停用配置位:<span style='color: red'>"+ obj.data.name +"</span>";
                }
                layer.confirm(tips, function(index) {
                    like.ajax({
                        url: "{:url('decoration.AdPosition/swtichStatus2')}",
                        data: {id: obj.data.id,status:status},
                        type: "POST",
                        success: function (res) {
                            if (res.code === 1) {
                                layui.layer.msg(res.msg);
                                layer.close(index);
                                table.reload('like-table-lists');
                            }
                        }
                    });
                    layer.close(index);
                })
            },
            del: function(obj) {
                layer.confirm("确定删除配置位:<span style='color: red'>"+ obj.data.name +"</span>", function(index) {
                    like.ajax({
                        url: "{:url('decoration.AdPosition/del2')}",
                        data: {id: obj.data.id},
                        type: "POST",
                        success: function (res) {
                            if (res.code === 1) {
                                layui.layer.msg(res.msg);
                                layer.close(index);
                                obj.del();
                            }
                        }
                    });
                    layer.close(index);
                })
            },
        };
        like.eventClick(active);

    })
</script>