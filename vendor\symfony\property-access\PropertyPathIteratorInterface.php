<?php

/*
 * This file is part of the Symfony package.
 *
 * (c) <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Symfony\Component\PropertyAccess;

/**
 * <AUTHOR> <b<PERSON><PERSON><PERSON>@gmail.com>
 */
interface PropertyPathIteratorInterface extends \Iterator, \SeekableIterator
{
    /**
     * Returns whether the current element in the property path is an array
     * index.
     *
     * @return bool
     */
    public function isIndex();

    /**
     * Returns whether the current element in the property path is a property
     * name.
     *
     * @return bool
     */
    public function isProperty();
}
