(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-webview-webview"],{"46d4":function(t,e,n){"use strict";n.d(e,"b",(function(){return u})),n.d(e,"c",(function(){return i})),n.d(e,"a",(function(){}));var u=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{staticClass:"page-body"},[e("v-uni-web-view",{attrs:{src:this.url}})],1)},i=[]},"66a9":function(t,e,n){"use strict";n.r(e);var u=n("46d4"),i=n("eed0");for(var r in i)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(r);var a=n("828b"),o=Object(a["a"])(i["default"],u["b"],u["c"],!1,null,"165ba533",null,!1,u["a"],void 0);e["default"]=o.exports},a086:function(t,e,n){"use strict";n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;e.default={data:function(){return{url:""}},onLoad:function(t){this.url=this.$Route.query.url},methods:{}}},eed0:function(t,e,n){"use strict";n.r(e);var u=n("a086"),i=n.n(u);for(var r in u)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return u[t]}))}(r);e["default"]=i.a}}]);