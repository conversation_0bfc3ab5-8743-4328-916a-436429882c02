<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Bmlb\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * DeleteTrafficMirror请求参数结构体
 *
 * @method array getTrafficMirrorIds() 获取流量镜像实例ID数组，可以批量删除，每次删除上限为20
 * @method void setTrafficMirrorIds(array $TrafficMirrorIds) 设置流量镜像实例ID数组，可以批量删除，每次删除上限为20
 */
class DeleteTrafficMirrorRequest extends AbstractModel
{
    /**
     * @var array 流量镜像实例ID数组，可以批量删除，每次删除上限为20
     */
    public $TrafficMirrorIds;

    /**
     * @param array $TrafficMirrorIds 流量镜像实例ID数组，可以批量删除，每次删除上限为20
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("TrafficMirrorIds",$param) and $param["TrafficMirrorIds"] !== null) {
            $this->TrafficMirrorIds = $param["TrafficMirrorIds"];
        }
    }
}
