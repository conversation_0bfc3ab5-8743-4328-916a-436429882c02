<?php
namespace app\api\logic;

use app\common\basics\Logic;
use app\common\enum\AdEnum;
use app\common\model\Ad;
use think\facade\Db;
use app\common\server\UrlServer;

class AdLogic extends Logic
{
    public static function lists($pid, $terminal, $ad_sn = '', $name = '', $link_type = '')
    {
        $where = [];
        if ($ad_sn) {
            if ($ad_sn == 'pbanner_new') {
                $where_adpoist[] = ['ad_sn', 'in', 'pbanner_new,pbanner'];
            } else if ($ad_sn == 'cbanner_new') {
                $where_adpoist[] = ['ad_sn', 'in', 'cbanner_new,cbanner'];
            } else {
                $where_adpoist[] = ['ad_sn', '=', $ad_sn];
            }
            $pid = Db::name('ad_position')->where($where_adpoist)->column('id');
            $where[] = ['a.pid', 'in', $pid];
        } else {
            if ($pid) {
                $where[] = ['a.pid', '=', $pid];
            }
        }
        if (!empty($name)) {
            $where[] = ['ap.name', '=', $name . '-轮播'];
        }
        if ($link_type) {
            $where[] = ['a.link_type', '=', $link_type];
        }
        //获取商家广告位

        $ad_list = Ad::alias('a')
            ->join('ad_position ap', 'a.pid = ap.id')
            ->where(['ap.terminal' => $terminal, 'a.status' => 1, 'a.del' => 0, 'ap.status' => 1, 'ap.del' => 0])
            ->where($where)
            ->field('a.*,ap.ad_sn,ap.ad_yn')
            ->order('a.sort asc, a.id desc')
            ->select()
            ->toArray();

        //获取商家购买的广告 ,替换默认广告, 如果广告到期了不显示,还显示默认广告  ,
        //店铺主页路径是subcontract/Home/storeDetails/index?id=店铺id

        $list = [];
        $groupedList = [];
        foreach ($ad_list as $key => $ad) {
            $url = $ad['link'];
            $is_tab = 0;
            $params = [];
            switch ($ad['link_type']) {
                case 1: // 商城页面
                    $page = AdEnum::getLinkPage($ad['terminal'], $ad['link']);
                    $url = $page['path'];
                    $is_tab = $page['is_tab'] ?? 0;
                    break;
                case 2: // 商品页面
                    $goods_path = AdEnum::getGoodsPath($ad['terminal']);
                    //显示商品最低价格
                    $min_price = Db::name('goods')
                        ->where(['id' => $ad['link']])
                        ->value('min_price');
                    $url = $goods_path;
                    $params = [
                        'id' => $ad['link'],
                    ];
                    break;
                case 4: // 店铺主页
                    $ad['link_type']=1;
                    $url = '/subcontract/Home/storeDetails/index?id='.$ad['link'];
                    break;
            }

            // 使用ad_sn作为键，将$list重新组织成数组
            $listItem = $list[] = [
                'image'     => UrlServer::getFileUrl($ad['image']),
                'bj_image'     => $ad['bj_image'] ? UrlServer::getFileUrl($ad['bj_image']) : '',
                'link'      => $url,
                'link_type' => $ad['link_type'],
                'params'    => $params,
                'is_tab'    => $is_tab,
                'min_price'    => $min_price ?? 0,
                'add_field'    => $ad['add_field'],
                'titke'    => $ad['title'],
                'remark'    => $ad['remark'],
                'ad_sn'    => $ad['ad_sn'],
                'ad_yn'    => $ad['ad_yn'],
                'ad_fee'    => $ad['ad_fee'],
                'id'    => $ad['id'],
            ];

            $groupedList[$ad['ad_yn']][] = $listItem;
        }
        if (!empty($name)) {
            $list = $list;
        } else {
            $list = $ad_sn ? $groupedList : $list;
        }
        return $list;
    }
    // public static function lists2($pid, $terminal, $ad_sn = '', $name = '')
    // {
    //     $where = [];

    //     $where[] = ['a.pid', 'in', '84,85,86,33'];
    //     //获取商家广告位

    //     $ad_list = Ad::alias('a')
    //         ->join('ad_position ap', 'a.pid = ap.id')
    //         ->where(['ap.terminal' => 1, 'a.status' => 1, 'a.del' => 0, 'ap.status' => 1, 'ap.del' => 0])
    //         ->where($where)
    //         ->field('a.*,ap.ad_sn,ap.ad_yn')
    //         ->order('a.sort asc, a.id desc')
    //         ->select()
    //         ->toArray();

    //     //获取商家购买的广告 ,替换默认广告, 如果广告到期了不显示,还显示默认广告  ,
    //     $list = [];
    //     $groupedList = [];
    //     foreach ($ad_list as $key => $ad) {
    //         $url = $ad['link'];
    //         $is_tab = 0;
    //         $params = [];
    //         switch ($ad['link_type']) {
    //             case 1: // 商城页面
    //                 $page = AdEnum::getLinkPage($ad['terminal'], $ad['link']);
    //                 $url = $page['path'];
    //                 $is_tab = $page['is_tab'] ?? 0;
    //                 break;
    //             case 2: // 商品页面
    //                 $goods_path = AdEnum::getGoodsPath($ad['terminal']);
    //                 //显示商品最低价格
    //                 $min_price = Db::name('goods')
    //                     ->where(['id' => $ad['link']])
    //                     ->value('min_price');
    //                 $url = $goods_path;
    //                 $params = [
    //                     'id' => $ad['link'],
    //                 ];
    //                 break;
    //         }
    //         // 使用ad_sn作为键，将$list重新组织成数组
    //         $listItem = $list[] = [
    //             'image'     => UrlServer::getFileUrl($ad['image']),
    //             'bj_image'     => $ad['bj_image'] ? UrlServer::getFileUrl($ad['bj_image']) : '',
    //             'link'      => $url,
    //             'link_type' => $ad['link_type'],
    //             'params'    => $params,
    //             'is_tab'    => $is_tab,
    //             'add_field'    => $ad['add_field'],
    //             'min_price'    => $min_price,
    //             'titke'    => $ad['title'],
    //             'remark'    => $ad['remark'],
    //             'ad_sn'    => $ad['ad_sn'],
    //             'ad_yn'    => $ad['ad_yn'],
    //             'ad_fee'    => $ad['ad_fee'],
    //             'id'    => $ad['id'],
    //         ];

    //         $groupedList[$ad['ad_yn']][] = $listItem;
    //     }
    //     if (!empty($name)) {
    //         $list = $list;
    //     } else {
    //         $list = $ad_sn ? $groupedList : $list;
    //     }
    //     return  $groupedList;
    // }

    /**
     * Notes:获取广告位列表
     * @param $terminal
     * @return array|\think\Model|null
     * @author: cjhao 2021/4/20 11:04
     */
    public static function getPositionList($terminal)
    {
        $data = Db::name('ad_position')
            ->where(['del' => 0, 'terminal' => $terminal, 'status' => 1])
            ->field('id,name,height,width,ad_sn')
            ->select();
        $list = [
            'data'     => $data

        ];
        return $list;
    }
}