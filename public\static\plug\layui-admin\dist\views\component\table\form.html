

<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>加入表单元素 - 数据表格</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
  <link rel="stylesheet" href="../../../layuiadmin/layui/css/layui.css" media="all">
  <link rel="stylesheet" href="../../../layuiadmin/style/admin.css" media="all">
</head>
<body>

  <div class="layui-card layadmin-header">
    <div class="layui-breadcrumb" lay-filter="breadcrumb">
      <a lay-href="">主页</a>
      <a><cite>组件</cite></a>
      <a><cite>数据表格</cite></a>
      <a><cite>加入表单元素</cite></a>
    </div>
  </div>
  
  <div class="layui-fluid">
    <div class="layui-row layui-col-space15">
      <div class="layui-col-md12">
        <div class="layui-card">
          <div class="layui-card-header">加入表单元素</div>
          <div class="layui-card-body">
            <table class="layui-hide" id="test-table-form"></table>
 
            <script type="text/html" id="test-table-switchTpl">
              <!-- 这里的 checked 的状态只是演示 -->
              <input type="checkbox" name="sex" lay-skin="switch" lay-text="女|男" lay-filter="test-table-sexDemo"
               value="{{ d.id }}" data-json="{{ encodeURIComponent(JSON.stringify(d)) }}" {{ d.id == 10003 ? 'checked' : '' }}>
            </script>
             
            <script type="text/html" id="test-table-checkboxTpl">
              <!-- 这里的 checked 的状态只是演示 -->
              <input type="checkbox" name="lock" title="锁定" lay-filter="test-table-lockDemo" 
               value="{{d.id}}" data-json="{{ encodeURIComponent(JSON.stringify(d)) }}" {{ d.id == 10006 ? 'checked' : '' }}>
            </script>
            
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <script src="../../../layuiadmin/layui/layui.js"></script>  
  <script>
  layui.config({
    base: '../../../layuiadmin/' //静态资源所在路径
  }).extend({
    index: 'lib/index' //主入口模块
  }).use(['index', 'table', 'form'], function(){
    var table = layui.table
    ,form = layui.form
    ,$ = layui.$;
  
    table.render({
      elem: '#test-table-form'
      ,url:'/demo/table/user/'
      ,cellMinWidth: 80
      ,cols: [[
        {type:'numbers'}
        ,{type: 'checkbox'}
        ,{field:'id', title:'ID', width:100, unresize: true, sort: true}
        ,{field:'username', title:'用户名'}
        ,{field:'city', title:'城市'}
        ,{field:'wealth', title: '财富', minWidth:120, sort: true}
        ,{field:'sex', title:'性别', width:85, templet: '#test-table-switchTpl', unresize: true}
        ,{field:'lock', title:'是否锁定', width:110, templet: '#test-table-checkboxTpl', unresize: true}
      ]]
      ,page: true
    });
    
    //监听性别操作
    form.on('switch(test-table-sexDemo)', function(obj){
      var json = JSON.parse(decodeURIComponent($(this).data('json')));
      layer.tips(this.value + ' ' + this.name + '：'+ obj.elem.checked, obj.othis);
      
      json = table.clearCacheKey(json);
      console.log(json); //当前行数据
    });
    
    //监听锁定操作
    form.on('checkbox(test-table-lockDemo)', function(obj){
    var json = JSON.parse(decodeURIComponent($(this).data('json')));
      layer.tips(this.value + ' ' + this.name + '：'+ obj.elem.checked, obj.othis);
      
      json = table.clearCacheKey(json);
      console.log(json); //当前行数据
    });
  
  });
  </script>
</body>
</html>