{"name": "symfony/cache", "type": "library", "description": "Provides an extended PSR-6, PSR-16 (and tags) implementation", "keywords": ["caching", "psr6"], "homepage": "https://symfony.com", "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "provide": {"psr/cache-implementation": "1.0|2.0", "psr/simple-cache-implementation": "1.0", "symfony/cache-implementation": "1.0|2.0"}, "require": {"php": ">=7.1.3", "psr/cache": "^1.0|^2.0", "psr/log": "~1.0", "symfony/cache-contracts": "^1.1.7|^2", "symfony/service-contracts": "^1.1|^2", "symfony/var-exporter": "^4.2|^5.0"}, "require-dev": {"cache/integration-tests": "dev-master", "doctrine/cache": "^1.6", "doctrine/dbal": "^2.6|^3.0", "predis/predis": "^1.1", "psr/simple-cache": "^1.0", "symfony/config": "^4.2|^5.0", "symfony/dependency-injection": "^3.4|^4.1|^5.0", "symfony/filesystem": "^4.4|^5.0", "symfony/http-kernel": "^4.4", "symfony/var-dumper": "^4.4|^5.0"}, "conflict": {"doctrine/dbal": "<2.6", "symfony/dependency-injection": "<3.4", "symfony/http-kernel": "<4.4|>=5.0", "symfony/var-dumper": "<4.4"}, "autoload": {"psr-4": {"Symfony\\Component\\Cache\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "minimum-stability": "dev"}