<?php

namespace app\admin\controller\jcai;

use app\common\basics\AdminBase;
use app\common\server\JsonServer;
use app\admin\logic\jcai\MemberLogic;
use think\facade\View;

class Member extends AdminBase
{
    /**
     * @Notes: 集采会员订单列表
     * @Author: Cline
     */
    public function lists()
    {
        if ($this->request->isAjax()) {
            $get = $this->request->get();
            $lists = MemberLogic::lists($get);
            if ($lists === false) {
                return JsonServer::error(MemberLogic::getError() ?: '获取失败');
            }
            return JsonServer::success('获取成功', $lists);
        }

        View::assign('statistics', MemberLogic::statistics());
        return view();
    }

    /**
     * @Notes: 调试数据类型
     * @Author: system
     */
    public function debug()
    {
        $debug = MemberLogic::debugDataTypes();
        return JsonServer::success('调试信息', $debug);
    }
}
