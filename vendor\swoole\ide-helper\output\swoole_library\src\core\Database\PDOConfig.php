<?php
/**
 * This file is part of Swoole.
 *
 * @link     https://www.swoole.com
 * @contact  <EMAIL>
 * @license  https://github.com/swoole/library/blob/master/LICENSE
 */

declare(strict_types=1);

namespace Swoole\Database;

class PDOConfig
{
    public const DRIVER_MYSQL = 'mysql';

    /** @var string */
    protected $driver = self::DRIVER_MYSQL;

    /** @var string */
    protected $host = '127.0.0.1';

    /** @var int */
    protected $port = 3306;

    /** @var null|string */
    protected $unixSocket;

    /** @var string */
    protected $dbname = 'test';

    /** @var string */
    protected $charset = 'utf8mb4';

    /** @var string */
    protected $username = 'root';

    /** @var string */
    protected $password = 'root';

    /** @var array */
    protected $options = [];

    public function getDriver(): string
    {
        return $this->driver;
    }

    public function withDriver(string $driver): self
    {
        $this->driver = $driver;
        return $this;
    }

    public function getHost(): string
    {
        return $this->host;
    }

    public function withHost($host): self
    {
        $this->host = $host;
        return $this;
    }

    public function getPort(): int
    {
        return $this->port;
    }

    public function hasUnixSocket(): bool
    {
        return isset($this->unixSocket);
    }

    public function getUnixSocket(): string
    {
        return $this->unixSocket;
    }

    public function withUnixSocket(?string $unixSocket): self
    {
        $this->unixSocket = $unixSocket;
        return $this;
    }

    public function withPort(int $port): self
    {
        $this->port = $port;
        return $this;
    }

    public function getDbname(): string
    {
        return $this->dbname;
    }

    public function withDbname(string $dbname): self
    {
        $this->dbname = $dbname;
        return $this;
    }

    public function getCharset(): string
    {
        return $this->charset;
    }

    public function withCharset(string $charset): self
    {
        $this->charset = $charset;
        return $this;
    }

    public function getUsername(): string
    {
        return $this->username;
    }

    public function withUsername(string $username): self
    {
        $this->username = $username;
        return $this;
    }

    public function getPassword(): string
    {
        return $this->password;
    }

    public function withPassword(string $password): self
    {
        $this->password = $password;
        return $this;
    }

    public function getOptions(): array
    {
        return $this->options;
    }

    public function withOptions(array $options): self
    {
        $this->options = $options;
        return $this;
    }

    /**
     * Returns the list of available drivers
     *
     * @return string[]
     */
    public static function getAvailableDrivers()
    {
        return [
            self::DRIVER_MYSQL,
        ];
    }
}
