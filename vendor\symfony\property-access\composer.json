{"name": "symfony/property-access", "type": "library", "description": "Provides functions to read and write from/to an object or array using a simple string notation", "keywords": ["property", "index", "access", "object", "array", "extraction", "injection", "reflection", "property path"], "homepage": "https://symfony.com", "license": "MIT", "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "require": {"php": ">=7.1.3", "symfony/inflector": "^3.4|^4.0|^5.0"}, "require-dev": {"symfony/cache": "^3.4|^4.0|^5.0"}, "suggest": {"psr/cache-implementation": "To cache access methods."}, "autoload": {"psr-4": {"Symfony\\Component\\PropertyAccess\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "minimum-stability": "dev"}