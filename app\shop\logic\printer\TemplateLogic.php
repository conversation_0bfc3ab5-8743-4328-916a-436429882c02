<?php


namespace app\shop\logic\printer;

use app\common\basics\Logic;
use app\common\model\printer\Printer;
use app\common\model\printer\PrinterConfig;
use app\common\server\UrlServer;
use app\common\server\YlyPrinter;

/**
 * 小票模板逻辑层
 * Class TemplateLogic
 * @package app\admin\logic\printer
 */
class TemplateLogic extends Logic
{

    /**
     * @notes 模板详情
     * @param $shop_id
     * @return array|\think\Model
     * <AUTHOR>
     * @date 2022/1/19 16:02
     */
    public static function getDetail($shop_id)
    {
        $result = PrinterLogic::getPrinterTpl($shop_id);
        $result['file_url'] = UrlServer::getFileUrl();
        return $result;
    }

    /**
     * @notes 编辑模板
     * @param $post
     * @param $shop_id
     * @return bool|string
     * <AUTHOR>
     * @date 2022/1/19 16:44
     */
    public static function edit($post, $shop_id)
    {
        try {

            PrinterLogic::setPrinterTpl($post, $shop_id);

            return true;
        } catch (\Exception $e) {
            return $e->getMessage();
        }
    }


    /**
     * @notes 打印机列表
     * @param $shop_id
     * @return mixed
     * <AUTHOR>
     * @date 2022/1/19 18:54
     */
    public static function getPrinterList($shop_id)
    {
        $where[] = ['p.del', '=', 0];
        $where[] = ['pc.type', '=', 1]; //类型为易联云
        $where[] = ['p.shop_id', '=', $shop_id];

        $result = (new Printer())->alias('p')
            ->join('printer_config pc', 'p.config_id = pc.id')
            ->where($where)
            ->column('machine_code,private_key,print_number', 'machine_code');
        return $result;
    }
}