{layout name="layout1" /}
<style>
    .layui-form-label {
        width: 120px;
    }
</style>
<div class="wrapper">
    <div class="layui-card">
        <!--操作提示-->
        <div class="layui-card-body">
            <div class="layui-collapse like-layui-collapse" lay-accordion="" style="border:1px dashed #c4c4c4">
                <div class="layui-colla-item">
                    <h2 class="layui-colla-title like-layui-colla-title" style="background-color: #fff">操作提示</h2>
                    <div class="layui-colla-content layui-show">
                        <p>*代理保证金管理，可查看保证金状态和处理退款申请；</p>
                    </div>
                </div>
            </div>
        </div>
        <!--搜索区域-->
        <div class="layui-card-body layui-form">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <div class="layui-form-label">用户信息：</div>
                    <div class="layui-input-inline">
                        <input type="text" id="keyword" name="keyword" placeholder="用户昵称/用户编号" class="layui-input" />
                    </div>
                </div>
                <div class="layui-inline">
                    <div class="layui-form-label">保证金状态：</div>
                    <div class="layui-input-inline">
                        <select name="status" id="status" placeholder="请选择">
                            <option value="">全部</option>
                            <option value="0">未支付</option>
                            <option value="1">已支付</option>
                            <option value="2">公示期结束(可退)</option>
                            <option value="2_refund">退款中(公示期)</option>
                            <option value="3">退款申请中</option>
                            <option value="4">已退款</option>
                            <option value="5">退款失败</option>
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <button class="layui-btn layui-btn-primary layui-bg-blue" lay-submit lay-filter="search">搜索</button>
                    <button class="layui-btn layui-btn-primary" lay-submit lay-filter="reset">重置</button>
                </div>
            </div>
        </div>
        <div class="layui-card-body">
            <!--数据表格-->
            <table id="lists" lay-filter="lists"></table>
            <!--工具条模板-->
            <script type="text/html" id="operation">
                {{#  if(d.status == 3){ }}
                <a class="layui-btn layui-btn-xs layui-btn-normal" lay-event="refund">微信退款</a>
                {{#  } }}
                {{#  if(d.status == 3 || d.status == 5){ }}
                <a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="manualRefund">手动退款</a>
                {{#  } }}
                <a class="layui-btn layui-btn-xs layui-btn-primary" lay-event="details">明细</a>
            </script>
            <!--自定义模板-->
            <script type="text/html" id="user-info">
                <img src="{{d.avatar}}" style="height:60px;width: 60px" class="image-show">
                <div class="layui-input-inline"  style="text-align: left;">
                    <p>用户编号:{{d.user_sn}}</p>
                    <p>用户昵称:{{d.nickname}}</p>
                </div>
            </script>
            <script type="text/html" id="status-info">
                {{#  if(d.status == 0){ }}
                <span class="layui-badge layui-bg-gray">未支付</span>
                {{#  } else if(d.status == 1){ }}
                <span class="layui-badge layui-bg-blue">已支付</span>
                {{#  } else if(d.status == 2){ }}
                    {{# if(d.refund_request_time > 0 && (d.refund_request_time + (d.refund_publicity_period_days * 86400)) > (new Date().getTime()/1000)){ }}
                    <span class="layui-badge layui-bg-orange">退款中(公示期)</span>
                    {{# } else { }}
                    <span class="layui-badge layui-bg-green">公示期结束(可退)</span>
                    {{# } }}
                {{#  } else if(d.status == 3 && d.refund_time==null){ }}
                <span class="layui-badge layui-bg-orange">退款申请中</span>
                {{#  } else if(d.refund_time > 0){ }}
                <span class="layui-badge layui-bg-cyan">已退款</span>
                {{#  } else if(d.status == 5){ }}
                <span class="layui-badge layui-bg-red">退款失败</span>
                {{#  } }}
            </script>
        </div>
    </div>
</div>

<script>
    layui.config({
        version:"{$front_version}",
        base: '/static/lib/'
    }).use(['table', 'form'], function () {
        let $ = layui.$
            , form = layui.form
            , table = layui.table;

        //监听搜索
        form.on('submit(search)', function(data){
            var field = data.field;
            //执行重载
            table.reload('lists', {
                where: field,
                page: {curr: 1}
            });
            return false;
        });

        //清空查询
        form.on('submit(reset)', function(){
            $('#keyword').val('');
            $('#status').val('');
            form.render('select');
            //刷新列表
            table.reload('lists', {
                where: [], page: {curr: 1}
            });
            return false;
        });

        // 数据表格渲染
        table.render({
            elem: '#lists'
            ,url: '{:url("agent.agent/deposit")}' //数据接口
            ,method: 'post'
            ,where: {
                status_not_zero: 1  // 自定义参数，在后端处理
            }
            ,page: true //开启分页
            ,cols: [[ //表头
                {templet: '#user-info', title: '用户信息', width:250}
                ,{field: 'amount', title: '保证金金额', width:120}
                ,{field: 'current_balance', title: '当前余额', width:120}
                ,{templet: '#status-info', title: '保证金状态', width:150}
                ,{field: 'payment_date', title: '支付时间', width: 180, templet: function(d){
                    return d.payment_date ? layui.util.toDateString(d.payment_date*1000, 'yyyy-MM-dd HH:mm:ss') : '';
                }}
                ,{field: 'publicity_period_end_time', title: '公示期结束时间', width: 180, templet: function(d){
                    return d.publicity_period_end_time ? layui.util.toDateString(d.publicity_period_end_time*1000, 'yyyy-MM-dd HH:mm:ss') : '';
                }}
                ,{field: 'refund_request_time', title: '退款申请时间', width: 180, templet: function(d){
                    return d.refund_request_time ? layui.util.toDateString(d.refund_request_time*1000, 'yyyy-MM-dd HH:mm:ss') : '';
                }}
                ,{field: 'refund_time', title: '退款时间', width: 180, templet: function(d){
                        return d.refund_time ? layui.util.toDateString(d.refund_time*1000, 'yyyy-MM-dd HH:mm:ss') : '';
                    }}
                // 显示申请后公示期结束时间
                //,{field: 'refund_publicity_end_time', title: '申请后公示期结束时间', width: 180, templet: function(d){
                    // 使用配置的退款公示期天数计算结束时间
                    //var refundPublicityEndTime = d.refund_request_time + (d.refund_publicity_period_days * 86400);
                    //return refundPublicityEndTime ? layui.util.toDateString(refundPublicityEndTime*1000, 'yyyy-MM-dd HH:mm:ss') : '';
                //}}
                ,{title: '操作', width: 200, align: 'center', toolbar: '#operation', fixed: 'right'}
            ]]
            , text: {none: '暂无数据！'}
            , parseData: function (res) { //将原始数据解析成 table 组件所规定的数据
                return {
                    "code": res.code,
                    "msg": res.msg,
                    "count": res.data.count, //解析数据长度
                    "data": res.data.lists, //解析数据列表
                };
            },
            response: {
                statusCode: 1
            }
            ,done: function(res, curr, count){
                // 解决操作栏因为内容过多换行问题
                $(".layui-table-main tr").each(function (index, val) {
                    $($(".layui-table-fixed-l .layui-table-body tbody tr")[index]).height($(val).height());
                    $($(".layui-table-fixed-r .layui-table-body tbody tr")[index]).height($(val).height());
                });
            }
        });

        // 监听工具条
        table.on('tool(lists)', function(obj){
            var data = obj.data;
            var layEvent = obj.event;

            if(layEvent === 'refund'){
                layer.confirm('确定要通过微信原路退还该代理的保证金吗？', function(index){
                    like.ajax({
                        url: '{:url("agent.agent/refundDeposit")}',
                        data: {id: data.id},
                        type: "post",
                        success:function(res) {
                            if(res.code === 1) {
                                layui.layer.msg(res.msg);
                                table.reload("lists");
                            } else {
                                layui.layer.msg(res.msg);
                            }
                        }
                    });
                    layer.close(index);
                });
            } else if(layEvent === 'manualRefund'){
                // 打开手动退款弹窗
                layer.open({
                    type: 2,
                    title: '代理保证金手动退款（银行卡/支付宝）',
                    area: ['600px', '500px'],
                    content: '{:url("agent.agent/manualRefund")}?id=' + data.id,
                    maxmin: true,
                    btn: ['确定', '取消'],
                    yes: function(index, layero){
                        var iframeWindow = window['layui-layer-iframe' + index];
                        var submit = layero.find('iframe').contents().find('#manualRefundSubmit');

                        iframeWindow.layui.form.on('submit(manualRefundSubmit)', function(data){
                            var field = data.field;

                            // 提交表单
                            var loadIndex = layer.load(2);
                            $.ajax({
                                url: '{:url("agent.agent/manualRefund")}',
                                type: 'post',
                                data: field,
                                success: function(res) {
                                    layer.close(loadIndex);
                                    if(res.code === 1) {
                                        layer.msg(res.msg, {icon: 1, time: 1500}, function(){
                                            layer.close(index);
                                            table.reload('lists');
                                        });
                                    } else {
                                        layer.msg(res.msg, {icon: 2});
                                    }
                                },
                                error: function() {
                                    layer.close(loadIndex);
                                    layer.msg('网络错误，请重试', {icon: 2});
                                }
                            });
                        });

                        submit.trigger('click');
                    }
                });
            } else if(layEvent === 'details'){
                // 打开保证金明细弹窗
                layer.open({
                    type: 2,
                    title: '代理保证金明细',
                    area: ['90%', '90%'],
                    content: '{:url("agent.agent/depositDetails")}?deposit_id=' + data.id + '&user_id=' + data.user_id + '&is_popup=1',
                    maxmin: true
                });
            }
        });
    });
</script>

