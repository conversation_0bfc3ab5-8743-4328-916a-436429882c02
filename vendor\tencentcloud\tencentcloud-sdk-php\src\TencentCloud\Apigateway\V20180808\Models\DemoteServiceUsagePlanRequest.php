<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Apigateway\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * DemoteServiceUsagePlan请求参数结构体
 *
 * @method string getUsagePlanId() 获取使用计划ID。
 * @method void setUsagePlanId(string $UsagePlanId) 设置使用计划ID。
 * @method string getServiceId() 获取待降级的服务唯一 ID。
 * @method void setServiceId(string $ServiceId) 设置待降级的服务唯一 ID。
 * @method string getEnvironment() 获取环境名称。
 * @method void setEnvironment(string $Environment) 设置环境名称。
 */
class DemoteServiceUsagePlanRequest extends AbstractModel
{
    /**
     * @var string 使用计划ID。
     */
    public $UsagePlanId;

    /**
     * @var string 待降级的服务唯一 ID。
     */
    public $ServiceId;

    /**
     * @var string 环境名称。
     */
    public $Environment;

    /**
     * @param string $UsagePlanId 使用计划ID。
     * @param string $ServiceId 待降级的服务唯一 ID。
     * @param string $Environment 环境名称。
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("UsagePlanId",$param) and $param["UsagePlanId"] !== null) {
            $this->UsagePlanId = $param["UsagePlanId"];
        }

        if (array_key_exists("ServiceId",$param) and $param["ServiceId"] !== null) {
            $this->ServiceId = $param["ServiceId"];
        }

        if (array_key_exists("Environment",$param) and $param["Environment"] !== null) {
            $this->Environment = $param["Environment"];
        }
    }
}
