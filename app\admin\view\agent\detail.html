{layout name="layout1" /}
<style>
    .layui-form-label {
        width: 120px;
        text-align: right;
        padding: 9px 15px;
        font-weight: bold;
    }
    .layui-input-block {
        margin-left: 150px;
        min-height: 36px;
        line-height: 36px;
    }
    .avatar-preview {
        width: 100px;
        height: 100px;
        border-radius: 50%;
        object-fit: cover;
        border: 2px solid #ddd;
    }
    .id-card-preview {
        width: 200px;
        height: 120px;
        object-fit: cover;
        border: 1px solid #ddd;
        margin: 5px 0;
    }
    .info-item {
        padding: 10px 0;
        border-bottom: 1px solid #eee;
    }
</style>
<div class="wrapper">
    <div class="layui-card">
      
        <div class="layui-card-body">
            <div class="layui-form">
                <div class="layui-form-item info-item">
                    <label class="layui-form-label">头像：</label>
                    <div class="layui-input-block">
                        <img src="{$user.avatar}" class="avatar-preview">
                    </div>
                </div>
                
                <div class="layui-form-item info-item">
                    <label class="layui-form-label">顾问编号：</label>
                    <div class="layui-input-block">
                        {$user.sn}
                    </div>
                </div>
                
                <div class="layui-form-item info-item">
                    <label class="layui-form-label">用户昵称：</label>
                    <div class="layui-input-block">
                        {$user.nickname}
                    </div>
                </div>
                
                <div class="layui-form-item info-item">
                    <label class="layui-form-label">手机号：</label>
                    <div class="layui-input-block">
                        {$agent.mobile|default=$user.mobile}
                    </div>
                </div>
                
                <div class="layui-form-item info-item">
                    <label class="layui-form-label">真实姓名：</label>
                    <div class="layui-input-block">
                        {$agent.name|default='--'}
                    </div>
                </div>
                  <div class="layui-form-item info-item">
                    <label class="layui-form-label">服务区域：</label>
                    <div class="layui-input-block">
                        {$region_desc}
                    </div>
                </div>
                <div class="layui-form-item info-item">
                    <label class="layui-form-label">地址：</label>
                    <div class="layui-input-block">
                        {$agent.address|default='--'}
                    </div>
                </div>
              
                <div class="layui-form-item info-item">
                    <label class="layui-form-label">身份证号：</label>
                    <div class="layui-input-block">
                        {$agent.id_card_number|default='-'}
                    </div>
                </div>
                <div class="layui-form-item info-item">
                    <label class="layui-form-label">身份证正面：</label>
                    <div class="layui-input-block">
                        <img src="{$agent.id_card_front|default='https://jcstatics.jiaqingfu.com.cn/applet/static/image.png'}" class="id-card-preview">
                    </div>
                </div>
                
                <div class="layui-form-item info-item">
                    <label class="layui-form-label">身份证反面：</label>
                    <div class="layui-input-block">
                        <img src="{$agent.id_card_back|default='https://jcstatics.jiaqingfu.com.cn/applet/static/image.png'}" class="id-card-preview">
                    </div>
                </div>
                 <div class="layui-form-item">
                    <div class="layui-input-block">
                        <button type="button" class="layui-btn layui-btn-primary" onclick="parent.layer.close(parent.layer.getFrameIndex(window.name))">关闭</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>