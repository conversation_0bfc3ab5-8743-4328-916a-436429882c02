<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据清理接口测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #fafafa;
        }
        .section h3 {
            color: #555;
            margin-top: 0;
        }
        button {
            background-color: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: #005a87;
        }
        button.danger {
            background-color: #dc3545;
        }
        button.danger:hover {
            background-color: #c82333;
        }
        button.warning {
            background-color: #ffc107;
            color: #212529;
        }
        button.warning:hover {
            background-color: #e0a800;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .result.success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .result.error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .loading {
            color: #007cba;
            font-style: italic;
        }
        .warning-text {
            color: #856404;
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>数据清理接口测试</h1>
        
        <div class="section">
            <h3>📊 获取垃圾数据统计</h3>
            <p>查看系统中的垃圾数据统计信息，包括已注销用户和孤立商品数据。</p>
            <button onclick="getStats()">获取统计信息</button>
            <div id="statsResult" class="result" style="display: none;"></div>
        </div>

        <div class="section">
            <h3>👥 清理已注销用户数据</h3>
            <div class="warning-text">
                ⚠️ 警告：此操作将永久删除已注销用户及其所有关联数据，包括订单、评论、收藏等。请谨慎操作！
            </div>
            <button class="danger" onclick="cleanDeletedUsers()">清理已注销用户</button>
            <div id="userResult" class="result" style="display: none;"></div>
        </div>

        <div class="section">
            <h3>📦 清理孤立商品数据</h3>
            <div class="warning-text">
                ⚠️ 警告：此操作将删除主表中不存在但附属表中存在的商品数据，包括规格、图片、评论等。
            </div>
            <button class="danger" onclick="cleanOrphanedGoods()">清理孤立商品数据</button>
            <div id="goodsResult" class="result" style="display: none;"></div>
        </div>

        <div class="section">
            <h3>🧹 执行完整清理</h3>
            <div class="warning-text">
                ⚠️ 严重警告：此操作将执行所有清理操作，包括删除已注销用户和孤立商品数据。此操作不可逆！
            </div>
            <button class="danger" onclick="cleanAll()">执行完整清理</button>
            <div id="allResult" class="result" style="display: none;"></div>
        </div>
    </div>

    <script>
        const API_BASE = '/shopapi/data_clean';
        
        function showResult(elementId, data, isError = false) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `result ${isError ? 'error' : 'success'}`;
            element.textContent = JSON.stringify(data, null, 2);
        }
        
        function showLoading(elementId) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = 'result loading';
            element.textContent = '正在处理中...';
        }
        
        async function makeRequest(url, method = 'GET') {
            try {
                const response = await fetch(url, {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                return await response.json();
            } catch (error) {
                throw new Error(`请求失败: ${error.message}`);
            }
        }
        
        async function getStats() {
            showLoading('statsResult');
            try {
                const data = await makeRequest(`${API_BASE}/stats`);
                showResult('statsResult', data);
            } catch (error) {
                showResult('statsResult', { error: error.message }, true);
            }
        }
        
        async function cleanDeletedUsers() {
            if (!confirm('确定要清理已注销用户数据吗？此操作不可逆！')) {
                return;
            }
            
            showLoading('userResult');
            try {
                const data = await makeRequest(`${API_BASE}/clean_deleted_users`, 'POST');
                showResult('userResult', data);
            } catch (error) {
                showResult('userResult', { error: error.message }, true);
            }
        }
        
        async function cleanOrphanedGoods() {
            if (!confirm('确定要清理孤立商品数据吗？此操作不可逆！')) {
                return;
            }
            
            showLoading('goodsResult');
            try {
                const data = await makeRequest(`${API_BASE}/clean_orphaned_goods`, 'POST');
                showResult('goodsResult', data);
            } catch (error) {
                showResult('goodsResult', { error: error.message }, true);
            }
        }
        
        async function cleanAll() {
            if (!confirm('确定要执行完整数据清理吗？这将删除所有垃圾数据，此操作不可逆！')) {
                return;
            }
            
            if (!confirm('最后确认：您真的要执行完整的数据清理操作吗？')) {
                return;
            }
            
            showLoading('allResult');
            try {
                const data = await makeRequest(`${API_BASE}/clean_all`, 'POST');
                showResult('allResult', data);
            } catch (error) {
                showResult('allResult', { error: error.message }, true);
            }
        }
        
        // 页面加载时自动获取统计信息
        window.onload = function() {
            getStats();
        };
    </script>
</body>
</html>
