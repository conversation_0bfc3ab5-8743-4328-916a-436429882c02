{layout name="layout2" /}

<div class="layui-card">
    <div class="layui-card-body">
        <div class="layui-form" lay-filter="detail-form">
            <div class="layui-form-item">
                <label class="layui-form-label">商家名称：</label>
                <div class="layui-input-block">
                    <div class="layui-form-mid">{$detail.shop_name}</div>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">联系电话：</label>
                <div class="layui-input-block">
                    <div class="layui-form-mid">{$detail.mobile}</div>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">申请人：</label>
                <div class="layui-input-block">
                    <div class="layui-form-mid">{$detail.admin_name}</div>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">注销原因：</label>
                <div class="layui-input-block">
                    <div class="layui-form-mid">{$detail.reason}</div>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">申请时间：</label>
                <div class="layui-input-block">
                    <div class="layui-form-mid">{$detail.create_time}</div>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">审核状态：</label>
                <div class="layui-input-block">
                    <div class="layui-form-mid">
                        {if $detail.status == 0}
                        <span class="layui-badge layui-bg-orange">待审核</span>
                        {elseif $detail.status == 1}
                        <span class="layui-badge layui-bg-green">已通过</span>
                        {elseif $detail.status == 2}
                        <span class="layui-badge">已拒绝</span>
                        {/if}
                    </div>
                </div>
            </div>
            {if $detail.status != 0}
            <div class="layui-form-item">
                <label class="layui-form-label">审核时间：</label>
                <div class="layui-input-block">
                    <div class="layui-form-mid">{$detail.audit_time}</div>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">审核备注：</label>
                <div class="layui-input-block">
                    <div class="layui-form-mid">{$detail.audit_remark|default='无'}</div>
                </div>
            </div>
            {/if}
            
            <div class="layui-form-item">
                <label class="layui-form-label">钱包余额：</label>
                <div class="layui-input-block">
                    <div class="layui-form-mid">{$detail.wallet} 元</div>
                </div>
            </div>
            
            {if $detail.has_deposit}
            <div class="layui-form-item">
                <label class="layui-form-label">保证金金额：</label>
                <div class="layui-input-block">
                    <div class="layui-form-mid">{$detail.deposit_amount} 元</div>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">支付时间：</label>
                <div class="layui-input-block">
                    <div class="layui-form-mid">{$detail.payment_date}</div>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">退款方式：</label>
                <div class="layui-input-block">
                    <div class="layui-form-mid">
                        {if $detail.deposit_refund_type == 'wechat'}
                        <span class="layui-badge layui-bg-blue">微信退款</span>（支付时间不超过365天）
                        {else}
                        <span class="layui-badge layui-bg-orange">转账退款</span>（支付时间超过365天）
                        {/if}
                    </div>
                </div>
            </div>
            {else}
            <div class="layui-form-item">
                <label class="layui-form-label">保证金状态：</label>
                <div class="layui-input-block">
                    <div class="layui-form-mid">未缴纳保证金</div>
                </div>
            </div>
            {/if}
            
            <div class="layui-form-item">
                <label class="layui-form-label">是否满足条件：</label>
                <div class="layui-input-block">
                    <div class="layui-form-mid">
                        {if $detail.can_deactivate}
                        <span class="layui-badge layui-bg-green">是</span>
                        {else}
                        <span class="layui-badge">否</span>
                        {/if}
                    </div>
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">检查结果：</label>
                <div class="layui-input-block">
                    <div class="layui-form-mid">
                        <div class="layui-collapse">
                            <div class="layui-colla-item">
                                <h2 class="layui-colla-title">查看详细检查结果</h2>
                                <div class="layui-colla-content">
                                    <table class="layui-table">
                                        <thead>
                                            <tr>
                                                <th>检查项</th>
                                                <th>状态</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {if !empty($detail.conditions)}
                                            <tr>
                                                <td>订单完结状态</td>
                                                <td>
                                                    {if $detail.conditions.orders_completed}
                                                    <span class="layui-badge layui-bg-green">通过</span>
                                                    {else}
                                                    <span class="layui-badge">未通过</span>
                                                    {/if}
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>售后处理状态</td>
                                                <td>
                                                    {if $detail.conditions.aftersale_completed}
                                                    <span class="layui-badge layui-bg-green">通过</span>
                                                    {else}
                                                    <span class="layui-badge">未通过</span>
                                                    {/if}
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>未结算资金</td>
                                                <td>
                                                    {if $detail.conditions.settlement_completed}
                                                    <span class="layui-badge layui-bg-green">通过</span>
                                                    {else}
                                                    <span class="layui-badge">未通过</span>
                                                    {/if}
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>未提现资金</td>
                                                <td>
                                                    {if $detail.conditions.withdrawal_completed}
                                                    <span class="layui-badge layui-bg-green">通过</span>
                                                    {else}
                                                    <span class="layui-badge">未通过</span>
                                                    {/if}
                                                </td>
                                            </tr>
                                            {else}
                                            <tr>
                                                <td colspan="2">无检查结果数据</td>
                                            </tr>
                                            {/if}
                                        </tbody>
                                    </table>
                                    
                                    {if !empty($detail.check_messages)}
                                    <div class="layui-elem-quote">
                                        <h4>检查消息：</h4>
                                        <ul>
                                            {foreach $detail.check_messages as $message}
                                            <li>{$message}</li>
                                            {/foreach}
                                        </ul>
                                    </div>
                                    {/if}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    layui.config({
        version:"{$front_version}",
        base: '/static/plug/layui-admin/dist/layuiadmin/' //静态资源所在路径
    }).extend({
        index: 'lib/index' //主入口模块
    }).use(['index', 'form', 'element'], function(){
        var $ = layui.$
            ,form = layui.form
            ,element = layui.element;
        
        // 初始化表单
        form.render();
    });
</script>
