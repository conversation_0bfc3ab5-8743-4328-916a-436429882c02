<?php



namespace app\common\model\shop;


use app\common\basics\Models;

/**
 * 商家角色
 * Class Menu
 * @package app\common\model
 */
class ShopRole extends Models
{
    protected $name = 'shop_role';

    /**
     * Notes: 获取角色名称
     * @param $role_id
     * <AUTHOR> 15:40)
     * @return mixed|string
     */
    public function getRoleName($role_id)
    {
        $role_name = $this
            ->where(['id' => $role_id])
            ->value('name');
        
        return empty($role_name) ? '系统管理员' : $role_name;
    }


    /**
     * Notes: 获取全部角色名称(以角色id为键,值为名称)
     * @param array $contidion
     * <AUTHOR> 10:46)
     * @return array
     */
    public function getNameColumn($contidion = [])
    {
        $role_name = $this
            ->where($contidion)
            ->where('del', 0)
            ->column('name', 'id');
        return $role_name;
    }


    /**
     * Notes:
     * @param array $where
     * @param string $field
     * <AUTHOR> 11:13)
     * @return \think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getRoleLists($where = [], $field = "*")
    {
        return $this->where(['del' => 0])->where($where)->field($field)->select();
    }

}