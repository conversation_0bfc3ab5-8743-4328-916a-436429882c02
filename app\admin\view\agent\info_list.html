{layout name="layout1" /}
<style>
    .layui-form-label {
        width: 120px;
    }
</style>
<div class="wrapper">
    <div class="layui-card">
        <!--搜索区域-->
        <div class="layui-card-body layui-form">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <div class="layui-form-label">姓名：</div>
                    <div class="layui-input-inline">
                        <input type="text" id="name" name="name" class="layui-input" />
                    </div>
                </div>
                <div class="layui-inline">
                    <div class="layui-form-label">手机号：</div>
                    <div class="layui-input-inline">
                        <input type="text" id="mobile" name="mobile" class="layui-input" />
                    </div>
                </div>
                <div class="layui-inline">
                    <button class="layui-btn layui-btn-primary layui-bg-blue" lay-submit lay-filter="search">搜索</button>
                    <button class="layui-btn layui-btn-primary" lay-submit lay-filter="reset">重置</button>
                </div>
            </div>
        </div>
        <div class="layui-card-body">
            <!--数据表格-->
            <table id="lists" lay-filter="lists"></table>
            <!--工具条模板-->
            <script type="text/html" id="user-info">
                <img src="{{d.avatar}}" style="height:60px;width: 60px" class="image-show">
                <div class="layui-input-inline"  style="text-align: left;">
                    <p>用户编号:{{d.user_sn}}</p>
                    <p>用户昵称:{{d.nickname}}</p>
                </div>
            </script>
        </div>
    </div>
</div>

<script>
    layui.config({
        version:"{$front_version}",
        base: '/static/lib/'
    }).use(['table', 'form'], function () {
        let $ = layui.$
            , form = layui.form
            , table = layui.table;

        //监听搜索
        form.on('submit(search)', function(data){
            var field = data.field;
            //执行重载
            table.reload('lists', {
                where: field,
                page: {curr: 1}
            });
        });

        //清空查询
        form.on('submit(reset)', function(){
            $('#name').val('');
            $('#mobile').val('');
            form.render('select');
            //刷新列表
            table.reload('lists', {
                where: {},
                page: {curr: 1}
            });
        });

        // 数据表格渲染
        table.render({
            elem: '#lists'
            ,url: '{:url("agent.agent/infoList")}' //数据接口
            ,method: 'post'
            ,page: true //开启分页
            ,cols: [[ //表头
                {field: 'id', title: 'ID', width:80, sort: true}
                ,{templet: '#user-info', title: '用户信息', width:250}
                ,{field: 'name', title: '姓名', width:150}
                ,{field: 'mobile', title: '手机号', width:150}
                ,{field: 'address', title: '地址'}
                ,{field: 'create_time', title: '提交时间', width:200}
            ]]
            , text: {none: '暂无数据！'}
            , parseData: function (res) { //将原始数据解析成 table 组件所规定的数据
                return {
                    "code": res.code,
                    "msg": res.msg,
                    "count": res.data.count, //解析数据长度
                    "data": res.data.lists, //解析数据列表
                };
            },
            response: {
                statusCode: 1
            }
        });
    });
</script>
