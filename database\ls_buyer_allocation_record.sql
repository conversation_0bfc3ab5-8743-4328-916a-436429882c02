-- 采购人员分配记录表
CREATE TABLE `ls_buyer_allocation_record` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `shop_id` int(11) NOT NULL COMMENT '商家ID',
  `user_id` int(11) NOT NULL COMMENT '分配的采购人员ID',
  `allocation_year` int(4) NOT NULL COMMENT '分配年份',
  `activity_level` tinyint(1) NOT NULL COMMENT '活跃度等级：1=1级，2=2级，3=3级',
  `create_time` int(11) UNSIGNED NOT NULL COMMENT '分配时间',
  PRIMARY KEY (`id`),
  INDEX `idx_shop_year` (`shop_id`, `allocation_year`),
  UNIQUE KEY `idx_shop_user_year` (`shop_id`, `user_id`, `allocation_year`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='采购人员分配记录表';
