<!-- 图片上传组件 -->
<div class="image-upload-component" data-field="{$field|default='image_url'}" data-title="{$title|default='图片'}">
    <input type="hidden" name="{$field|default='image_url'}" value="{$value|default=''}">
    
    <div class="upload-container">
        <div class="upload-area" id="upload_{$field|default='image_url'}" {if !empty($value)}style="display:none;"{/if}>
            <div class="upload-icon">
                <i class="layui-icon layui-icon-upload"></i>
            </div>
            <div class="upload-text">
                <p>点击上传{$title|default='图片'}</p>
                <p class="upload-tips">支持JPG/PNG格式，大小不超过2MB</p>
            </div>
        </div>
        
        <div class="image-preview" {if empty($value)}style="display:none;"{/if}>
            <div class="preview-image">
                <img src="{$value|default=''}" alt="预览图片">
            </div>
            <div class="image-actions">
                <button type="button" class="layui-btn layui-btn-sm layui-btn-normal" onclick="changeImage('{$field|default='image_url'}')">
                    <i class="layui-icon layui-icon-edit"></i> 更换
                </button>
                <button type="button" class="layui-btn layui-btn-sm layui-btn-danger" onclick="removeImage('{$field|default='image_url'}')">
                    <i class="layui-icon layui-icon-delete"></i> 删除
                </button>
            </div>
        </div>
    </div>
</div>

<style>
.image-upload-component {
    width: 100%;
}

.upload-container {
    position: relative;
    width: 100%;
    min-height: 120px;
}

.upload-area {
    border: 2px dashed #d2d2d2;
    border-radius: 8px;
    padding: 30px 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background-color: #fafafa;
}

.upload-area:hover {
    border-color: #1890ff;
    background-color: #f0f8ff;
}

.upload-icon i {
    font-size: 48px;
    color: #bbb;
    display: block;
    margin-bottom: 15px;
}

.upload-area:hover .upload-icon i {
    color: #1890ff;
}

.upload-text p {
    margin: 5px 0;
    color: #666;
}

.upload-text .upload-tips {
    font-size: 12px;
    color: #999;
}

.image-preview {
    border: 1px solid #e6e6e6;
    border-radius: 8px;
    padding: 15px;
    background-color: #fff;
}

.preview-image {
    text-align: center;
    margin-bottom: 15px;
}

.preview-image img {
    max-width: 100%;
    max-height: 200px;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.image-actions {
    text-align: center;
}

.image-actions button {
    margin: 0 5px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .upload-area {
        padding: 20px 15px;
    }
    
    .upload-icon i {
        font-size: 36px;
    }
    
    .preview-image img {
        max-height: 150px;
    }
}
</style>

<script>
    // 更换图片
    function changeImage(fieldName) {
        var container = $('input[name="' + fieldName + '"]').closest('.image-upload-component');
        container.find('.upload-area').click();
    }

    // 删除图片
    function removeImage(fieldName) {
        layer.confirm('确定要删除这张图片吗？', {icon: 3, title:'提示'}, function(index){
            // 清空隐藏域值
            $('input[name="' + fieldName + '"]').val('');
            
            // 清空预览图片
            var container = $('input[name="' + fieldName + '"]').closest('.image-upload-component');
            container.find('.preview-image img').attr('src', '');
            
            // 切换显示状态
            container.find('.upload-area').show();
            container.find('.image-preview').hide();
            
            layer.close(index);
            layer.msg('已删除', {icon: 1});
        });
    }
</script>
