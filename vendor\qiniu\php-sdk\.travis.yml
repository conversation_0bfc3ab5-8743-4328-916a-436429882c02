language: php

php:
  - 5.4
  - 5.5
  - 5.6
  - 7.0

dist: trusty
os: linux

before_script:
  - export QINIU_TEST_ENV="travis"
  - travis_retry composer self-update
  - travis_retry composer install --no-interaction --prefer-source --dev

script:
  - ./vendor/bin/phpcs --standard=PSR2 src
  - ./vendor/bin/phpcs --standard=PSR2 examples
  - ./vendor/bin/phpcs --standard=PSR2 tests
  - ./vendor/bin/phpunit --coverage-clover=coverage.xml

env:
  global:
    - QINIU_ACCESS_KEY=vHg2e7nOh7Jsucv2Azr5FH6omPgX22zoJRWa0FN5
    - secure: "V9BsntXQZwvO9EOD6itzaae2uq+GemzyTUTxMTJx1/jFoUNpCU2O2UAgjA2XSEr5sgci0KWDV4Krbzv3EBB4uplOFLMI3w32256UHbT9E0x3YjhfPJZk68MH1iS1be7X81LDHON7yveavK8987s3qzjeUcbfLSPgccT+cvf7+dc="

after_success:
  - bash <(curl -s https://codecov.io/bash)
