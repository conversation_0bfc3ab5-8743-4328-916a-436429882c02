<?php

namespace app\admin\controller\agent;

use app\admin\controller\BaseController;
use app\admin\model\agent\MerchantLevelConfig;
use app\common\logic\AgentLogic;

class MerchantLevelConfigController extends BaseController
{
    /**
     * @notes 配置列表
     */
    public function lists()
    {
        if ($this->request->isAjax()) {
            $params = $this->request->get();
            $lists = MerchantLevelConfig::lists($params);
            return $this->success('获取成功', $lists);
        }
        return $this->fetch();
    }

    /**
     * @notes 添加配置
     */
    public function add()
    {
        if ($this->request->isAjax()) {
            $params = $this->request->post();
            // 验证 level_id 是否已存在
            $check = MerchantLevelConfig::where('level_id', $params['level_id'])->find();
            if ($check) {
                return $this->error('该等级ID已存在，请勿重复添加');
            }
            $params['create_time'] = time();
            MerchantLevelConfig::create($params);
            return $this->success('添加成功');
        }
        return $this->fetch('form');
    }

    /**
     * @notes 编辑配置
     */
    public function edit($id)
    {
        $detail = MerchantLevelConfig::find($id);
        if ($this->request->isAjax()) {
            $params = $this->request->post();
            // 验证 level_id 是否与其他记录冲突
            $check = MerchantLevelConfig::where('level_id', $params['level_id'])->where('id', '<>', $id)->find();
            if ($check) {
                return $this->error('该等级ID已存在');
            }
            $params['update_time'] = time();
            $detail->save($params);
            return $this->success('更新成功');
        }
        $this->assign('detail', $detail);
        return $this->fetch('form');
    }

    /**
     * @notes 删除配置
     */
    public function del($id)
    {
        $result = MerchantLevelConfig::destroy($id);
        if ($result) {
            return $this->success('删除成功');
        }
        return $this->error('删除失败');
    }
}
