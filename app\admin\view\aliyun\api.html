{extend name="public/base" /}
{block name="content"}
<div class="layui-card">
    <div class="layui-card-header">阿里云API配置</div>
    <div class="layui-card-body">
        <form class="layui-form" action="" method="post">
            <div class="layui-form-item">
                <label class="layui-form-label">AccessKey ID</label>
                <div class="layui-input-block">
                    <input type="text" name="access_key_id" value="{$config.access_key_id}" autocomplete="off" placeholder="请输入AccessKey ID" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">AccessKey Secret</label>
                <div class="layui-input-block">
                    <input type="password" name="access_key_secret" value="{$config.access_key_secret}" autocomplete="off" placeholder="请输入AccessKey Secret" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button class="layui-btn" lay-submit lay-filter="formDemo">立即提交</button>
                    <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-input-block">
                    <div class="layui-card">
                        <div class="layui-card-header">配置说明</div>
                        <div class="layui-card-body">
                            <p>1. 阿里云AccessKey ID和AccessKey Secret可以在阿里云控制台的"访问控制"中创建和获取。</p>
                            <p>2. 请确保已开通阿里云自然语言处理服务(NLP)。</p>
                            <p>3. 配置完成后，系统将使用阿里云NLP服务优化搜索候选词功能。</p>
                            <p>4. 如果未配置或配置错误，系统将自动使用本地逻辑生成候选词。</p>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>
{/block}

{block name="script"}
<script>
    layui.use(['form', 'layer'], function(){
        var form = layui.form;
        var layer = layui.layer;
        
        //监听提交
        form.on('submit(formDemo)', function(data){
            $.ajax({
                url: "{:url('aliyun/api')}",
                type: 'post',
                data: data.field,
                success: function(res){
                    if(res.code == 1){
                        layer.msg(res.msg, {icon: 1});
                    }else{
                        layer.msg(res.msg, {icon: 2});
                    }
                }
            });
            return false;
        });
    });
</script>
{/block}
