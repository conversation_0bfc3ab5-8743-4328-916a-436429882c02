<?php


namespace app\api\controller;

use app\api\logic\CartLogic;
use app\api\validate\CartValidate;
use app\common\basics\Api;
use app\common\server\JsonServer;


/**
 * 购物车控制器
 * Class Cart
 * @package app\api\controller
 */
class Cart extends Api
{

        public $like_not_need_login = ['num'];

    /**
     * @notes 购物车列表
     * @return \think\response\Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date 2021/9/7 10:23
     */
    public function lists()
    {
        $get = $this->request->get();
        $lists = CartLogic::lists($this->user_id,$get);
        return JsonServer::success('获取成功', $lists);
    }


    /**
     * Notes: 添加
     * <AUTHOR> 15:52)
     * @return \think\response\Json
     */
    public function add()
    {
        $post = $this->request->post();
        (new CartValidate())->goCheck('add', ['user_id' => $this->user_id]);
        $res = CartLogic::add($post, $this->user_id);
        if (false === $res) {
            $error = CartLogic::getError() ?: '系统错误';
            return JsonServer::error($error);
        }
        return JsonServer::success('添加成功');
    }


    /**
     * Notes: 更改数量
     * <AUTHOR> 15:52)
     * @return \think\response\Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function change()
    {
        $post = $this->request->post();
        (new CartValidate())->goCheck('change', ['user_id' => $this->user_id]);
        $res = CartLogic::change($post['cart_id'], $post['goods_num']);
        if (true === $res) {
            return JsonServer::success('');
        }
        $error = CartLogic::getError() ?: '系统错误';
        return JsonServer::error($error);
    }

     /**
     * Notes: 更改数量
     * <AUTHOR> 15:52)
     * @return \think\response\Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function changeItem()
    {
        $post = $this->request->post();
        $res = CartLogic::changeItem($post['cart_id'], $post['item_id'],$post['goods_num']);
        if (true === $res) {
            return JsonServer::success('');
        }
        $error = CartLogic::getError() ?: '系统错误';
        return JsonServer::error($error);
    }

    /**
     * Notes: 删除
     * <AUTHOR> 15:52)
     * @return \think\response\Json
     */
    public function del()
    {
        $post = $this->request->post();
        (new CartValidate())->goCheck('del', ['user_id' => $this->user_id]);
        if (CartLogic::del($post['cart_id'], $this->user_id)) {
            return JsonServer::success('删除成功');
        }
        return JsonServer::error('删除失败');
    }


    /**
     * Notes: 更改选中状态
     * <AUTHOR> 15:52)
     * @return \think\response\Json
     */
    public function selected()
    {
        $post = $this->request->post();
        (new CartValidate())->goCheck('selected', ['user_id' => $this->user_id]);
        CartLogic::selected($post, $this->user_id);
        return JsonServer::success('');
    }


    /**
     * Notes: 购物车数量
     * <AUTHOR> 15:53)
     * @return \think\response\Json
     */
    public function num()
    {
        $this->user_id=$this->user_id??0;
        return JsonServer::success('', CartLogic::cartNum($this->user_id));
    }


}