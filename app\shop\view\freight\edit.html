{layout name="layout2" /}
<div class="layui-form" lay-filter="layuiadmin-form-ad_position" id="layuiadmin-form-category" style="padding: 20px 30px 0 0;background-color: #ffffff">

    <input type="hidden" name="id" value="{$detail.id}">

    <div class="layui-form-item">
        <label class="layui-form-label">模板名称</label>
        <div class="layui-input-inline">
            <input type="text" name="name" lay-verify="required" value="{$detail.name}" placeholder="请输入模板名称"
                   autocomplete="off" class="layui-input">
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label">计费方式</label>
        <div class="layui-input-block" id="charge_way">
            <input type="radio" class="type" name="charge_way" value="1" lay-filter="charge_way" title="按重量" {if
                   condition="$detail.charge_way eq 1" }checked{/if}>
            <input type="radio" class="type" name="charge_way" value="2" lay-filter="charge_way" title="按体积" {if
                   condition="$detail.charge_way eq 2" }checked{/if}>
            <input type="radio" class="type" name="charge_way" value="3" lay-filter="charge_way" title="按件数" {if
                   condition="$detail.charge_way eq 3" }checked{/if}>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label">备注</label>
        <div class="layui-input-inline">
            <textarea name="remark" placeholder="备注信息" class="layui-textarea">{$detail.remark}</textarea>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label">配送区域</label>
        <div class="layui-input-block">
            <table class="layui-table">
                <colgroup>
                    <col width="25%">
                    <col width="15%">
                    <col width="15%">
                    <col width="15%">
                    <col width="15%">
                    <col width="20%">
                </colgroup>
                <thead>
                <tr>
                    <th>可配送区域</th>
                    <th class="th_first_unit">首重 (kg)</th>
                    <th class="th_first_money">运费 (元)</th>
                    <th class="th_continue_unit">续重 (kg)</th>
                    <th class="th_continue_money">续费 (元)</th>
                    <th >操作</th>
                </tr>
                </thead>
                <tbody>
                {foreach $detail.configs as $k => $item}
                <tr class='area_tr area_tr{$k}' data-id="{$k}">
                    <input type='hidden' class='region region{$k}' name='region[]' value="{$item.region}">
                    <td class='area_name area_name{$k}' style="text-align: left">
                        {$item.region_name}
                    </td>
                    <td><input type='number' name='first_unit[]' lay-verify='required' value="{$item.first_unit}" autocomplete='off' class='layui-input'></td>
                    <td><input type='number' name='first_money[]' lay-verify='required' value="{$item.first_money}" autocomplete='off' class='layui-input'></td>
                    <td><input type='number' name='continue_unit[]' lay-verify='required' value="{$item.continue_unit}" autocomplete='off' class='layui-input'></td>
                    <td><input type='number' name='continue_money[]' lay-verify='required' value="{$item.continue_money}" autocomplete='off' class='layui-input'></td>

                    {if condition =" $item.region neq 'all' "}
                    <td  style="text-align: center">
                        <button class='layui-btn layui-btn-sm layui-btn-normal' type='button' onclick='editArea("{$k}")'>
                            <i class="layui-icon layui-icon-edit"></i>
                        </button>
                        <button class='layui-btn layui-btn-sm layui-btn-danger' type='button' onclick='delArea("{$k}")'>
                            <i class="layui-icon layui-icon-delete"></i>
                        </button>
                    </td>
                    {/if}
                </tr>
                {/foreach}
                <tr class="area_tbody"></tr>
                </tbody>
            </table>
        </div>
    </div>


    <div class="layui-form-item">
        <label class="layui-form-label"></label>
        <div class="layui-input-inline">
            <button type="button" id="btn-area" class="layui-btn layui-btn-sm layui-btn-normal layuiadmin-btn-select_area">
                添加运费规则
            </button>
        </div>
    </div>


    <div class="layui-form-item layui-hide">
        <input type="button" lay-submit lay-filter="edit-freight-submit" id="edit-freight-submit" value="确认">
    </div>
</div>

<script>
    $("html").css('background-color','#FFFFFF');

    var araeDataIds = '';//选中的地区id
    var araeDataNmae = '';//选中的地区名字
    layui.config({
        version:"{$front_version}",
        base: '/static/lib/'
    }).extend({
        likeedit: 'likeedit/likeedit'
    }).use(['table', 'form', 'element', 'likeedit'], function() {
        var form = layui.form
            ,$ = layui.$
            ,table = layui.table
            , element = layui.element
            , likeedit = layui.likeedit;


        window.callTree = function (data) {
            for (var i = 0; i < data.length; i++) {
                araeDataNmae += data[i]['context'] + ',';
                araeDataIds += data[i]['nodeId'] + ',';
            }
            araeDataNmae = araeDataNmae.substring(0, araeDataNmae.length - 1);
            araeDataIds = araeDataIds.substring(0, araeDataIds.length - 1);
        };
        //添加地区(选择地区页面)
        $(document).on('click', '#btn-area', function () {
            layer.open({
                type: 2
                , title: '配送区域'
                , content: '{:url("freight/area")}'
                , area: ['90%', '90%']
                , btn: ['确定', '返回']
                , yes: function (index, layero) {
                    var iframeWindow = window['layui-layer-iframe' + index]
                        , submitID = 'area-freight-submit'
                        , submit = layero.find('iframe').contents().find('#' + submitID);
                    //监听提交
                    iframeWindow.layui.form.on('submit(' + submitID + ')', function (data) {
                        if (araeDataIds == '') {
                            layer.msg('请选择地区');
                            return;
                        }
                        addArea(araeDataNmae);
                        $('input.region:last').val(araeDataIds);
                        araeDataNmae = '';
                        araeDataIds = '';
                        layer.close(index);
                    });
                    submit.trigger('click');
                }

            });
        });


        // 切换计费方式变动表格描述
        form.on('radio(charge_way)', function (data) {
            var value = data.value;
            showChargeWay(value)
        });

        var selectedChargeWay = $('input[name="charge_way"]:checked').val();
        showChargeWay(selectedChargeWay);
    });

    //增加模板行
    function addArea(data) {
        var add = ".area_tr";
        var id = $('tr.area_tr:last').attr('data-id');
        if (id === undefined) {
            id = 1;
            add = ".area_tbody";
        } else {
            add = add + id;
        }
        var v = parseInt(id) + 1;

        var str = "<tr class='area_tr area_tr"+v+"' data-id='" + v + "'>" +
            "<td><span class=' area_name area_name"+v+" '>"+data+"</span></td>" +
            "<input type='hidden' class='region region"+v+" ' name='region["+v+"]' value=''>" +
            "<td><input type='number' name='first_unit["+v+"]' lay-verify='required' autocomplete='off' class='layui-input'></td>" +
            "<td><input type='number' name='first_money["+v+"]' lay-verify='required' autocomplete='off' class='layui-input'></td>" +
            "<td><input type='number' name='continue_unit["+v+"]' lay-verify='required' autocomplete='off' class='layui-input'></td>" +
            "<td><input type='number' name='continue_money["+v+"]' lay-verify='required' autocomplete='off' class='layui-input'></td>" +
            "<td style='text-align:center'>" +
            "<button class='layui-btn layui-btn-sm layui-btn-normal' type='button' onclick='editArea(" + v + ")'>" +
            "<i class='layui-icon layui-icon-edit'></i>" +
            "</button>" +
            "<button class='layui-btn layui-btn-sm layui-btn-danger' type='button' onclick='delArea(" + v + ")'>" +
            "<i class='layui-icon layui-icon-delete'></i>" +
            "</button>" +
            "</td>" +
            "</tr>";
        $(add).after(str);
    }

    //删除模板行
    function delArea(value) {
        $(".area_tr" + value).remove();
    }

    //编辑模板行
    function editArea(value) {

        var regionSelected = '.region'+value;//选择编辑的行
        var selectIds = $(regionSelected).val();//选中行的地区id

        layer.open({
            type: 2
            , title: '配送区域'
            , content: '{:url("freight/areaEdit")}'
            , area: ['90%', '90%']
            , btn: ['确定', '返回']
            , success: function (layero,index) {
                var iframe = window['layui-layer-iframe' + index];
                iframe.editSelected(selectIds);
            }
            , yes: function (index, layero) {
                var iframeWindow = window['layui-layer-iframe' + index]
                    , submitID = 'area-freight-submit'
                    , submit = layero.find('iframe').contents().find('#' + submitID);
                //监听提交
                iframeWindow.layui.form.on('submit(' + submitID + ')', function (data) {
                    if (araeDataIds == '') {
                        layer.msg('请选择地区');
                        return;
                    }
                    $(".area_name" + value).text(araeDataNmae);
                    $(".region" + value).val(araeDataIds);
                    araeDataNmae = '';
                    araeDataIds = '';
                    layer.close(index);
                });
                submit.trigger('click');
            }
        });

    }


    // 显示不同计费方式单位
    function showChargeWay(value)
    {
        switch (value) {
            case '1': // 重量
                $('.th_first_unit').text("首重 (kg)");
                $('.th_continue_unit').text("续重 (kg)");
                break;
            case '2': // 体积
                $('.th_first_unit').text("首体积 (m³)");
                $('.th_continue_unit').text("续体积 (m³)");
                break;
            case '3': // 件数
                $('.th_first_unit').text("首件 (件)");
                $('.th_continue_unit').text("续件 (件)");
                break;
        }
    }

</script>
