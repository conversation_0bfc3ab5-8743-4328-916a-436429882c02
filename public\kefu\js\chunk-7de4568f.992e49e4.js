(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-7de4568f"],{9760:function(e,t,r){"use strict";r.r(t);var o=function(){var e=this,t=e._self._c;return t("div",{staticClass:"login"},[t("div",{staticClass:"main"},[t("div",{staticClass:"login-container"},[t("div",{staticClass:"title"},[e._v(e._s(e.$route.query.type?"商家客服登录":"平台客服登录"))]),t("div",{staticClass:"form m-t-40"},[t("el-form",{ref:"form",attrs:{model:e.accountObj,rules:e.rules}},[t("el-form-item",{attrs:{prop:"account"}},[t("el-input",{attrs:{placeholder:"请输入账号"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.$refs.inputPwd.focus()}},model:{value:e.accountObj.account,callback:function(t){e.$set(e.accountObj,"account",t)},expression:"accountObj.account"}},[t("i",{staticClass:"el-input__icon el-icon-s-custom",attrs:{slot:"prefix"},slot:"prefix"})])],1),t("el-form-item",{attrs:{prop:"password"}},[t("el-input",{ref:"inputPwd",attrs:{placeholder:"请输入密码","show-password":""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleLogin.apply(null,arguments)}},model:{value:e.accountObj.password,callback:function(t){e.$set(e.accountObj,"password",t)},expression:"accountObj.password"}},[t("i",{staticClass:"el-input__icon el-icon-s-cooperation",attrs:{slot:"prefix"},slot:"prefix"})])],1),t("div",{staticClass:"m-b-10 p-t-10"},[t("el-checkbox",{attrs:{label:"记住账号"},model:{value:e.rememberAccount,callback:function(t){e.rememberAccount=t},expression:"rememberAccount"}})],1),t("el-button",{staticStyle:{width:"100%"},attrs:{type:"primary",loading:e.loadingLogin},on:{click:e.handleLogin}},[e._v("登录")])],1)],1)])]),t("div",{staticClass:"footer"},[e._v(e._s(e.copyright))])])},c=[];const n={keyPrev:"kefu_",set(e,t,r){let o={expire:r?this.time()+r:"",value:t};"object"===typeof o&&(o=JSON.stringify(o));try{window.localStorage.setItem(e,o)}catch(c){return!1}},get(e){try{let t=window.localStorage.getItem(e);if(!t)return!1;const{value:r,expire:o}=JSON.parse(t);return o&&o<this.time()?(window.localStorage.removeItem(e),!1):r}catch(t){return!1}},time(){return Math.round((new Date).getTime()/1e3)},remove(e){e&&window.localStorage.removeItem(e)},getKey(e){return this.keyPrev+e}};var s=n,a=(r("b562"),r("2f62")),i={data(){return{rememberAccount:!1,loadingLogin:!1,accountObj:{account:"",password:""},rules:{account:[{required:!0,message:"请输入账号",trigger:["blur"]}],password:[{required:!0,message:"请输入密码",trigger:["blur"]}]}}},methods:{handleLogin(){this.$refs.form.validate(e=>{e&&(s.set("remember_account",{remember:this.rememberAccount,account:this.accountObj.account}),this.login())})},login(){this.loadingLogin=!0;const{account:e,password:t}=this.accountObj;this.$store.dispatch("login",{account:e,password:t,type:this.$route.query.type||0}).then(e=>{const{query:{redirect:t}}=this.$route,r="string"===typeof t?t:"/";this.$router.replace(r)}).catch(()=>{this.loadingLogin=!1})}},computed:{...Object(a["c"])(["copyright"])},created(){const e=s.get("remember_account");e.remember&&(this.rememberAccount=e.remember,this.accountObj.account=e.account)}},u=i,l=(r("e549"),r("2877")),p=Object(l["a"])(u,o,c,!1,null,null,null);t["default"]=p.exports},c5c8:function(e,t,r){e.exports={primary:"#4073FA"}},e549:function(e,t,r){"use strict";r("c5c8")}}]);
//# sourceMappingURL=chunk-7de4568f.992e49e4.js.map