<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>spec_value_str错误修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .log-entry {
            margin: 2px 0;
        }
        .log-success { color: #28a745; }
        .log-error { color: #dc3545; }
        .log-info { color: #17a2b8; }
        .log-warning { color: #ffc107; }
        .btn {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .btn-warning {
            background-color: #ffc107;
            color: #212529;
        }
        .btn-danger {
            background-color: #dc3545;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 spec_value_str错误修复测试</h1>
        <p>这个页面用于测试修复后的spec_value_str处理逻辑，确保不再出现"Undefined index: {spec_value_str}"错误。</p>
        
        <div class="test-section">
            <h3>🧪 测试数据模拟</h3>
            <button class="btn" onclick="testNormalData()">✅ 测试正常数据</button>
            <button class="btn btn-warning" onclick="testMissingSpecValueStr()">⚠️ 测试缺失spec_value_str</button>
            <button class="btn btn-danger" onclick="testNullData()">❌ 测试null数据</button>
            <button class="btn" onclick="testEmptyArray()">📭 测试空数组</button>
        </div>
        
        <div class="test-section">
            <h3>📝 测试日志</h3>
            <div id="logContainer" class="log"></div>
            <button class="btn" onclick="clearLog()">清空日志</button>
        </div>
        
        <div class="test-section">
            <h3>🔍 修复说明</h3>
            <p><strong>修复内容：</strong></p>
            <ul>
                <li>✅ 添加了spec_value_str存在性检查</li>
                <li>✅ 使用安全的属性访问方式</li>
                <li>✅ 为所有字段添加了默认值</li>
                <li>✅ 增加了错误日志记录</li>
            </ul>
            
            <p><strong>修复代码示例：</strong></p>
            <pre><code>// 修复前（容易出错）
if(spec_value_str == goods_info['item'][i]['spec_value_str']){

// 修复后（安全处理）
if(!goods_info['item'][i] || typeof goods_info['item'][i]['spec_value_str'] === 'undefined') {
    console.warn('商品项缺少spec_value_str字段:', goods_info['item'][i]);
    continue;
}</code></pre>
        </div>
    </div>

    <script>
        // 日志函数
        function log(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.textContent = `[${timestamp}] ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
            
            // 同时输出到控制台
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
        
        // 清空日志
        function clearLog() {
            document.getElementById('logContainer').innerHTML = '';
        }
        
        // 模拟修复后的处理逻辑
        function processGoodsInfo(goods_info) {
            log('开始处理商品信息...', 'info');
            
            if (!goods_info || !goods_info.item) {
                log('❌ 商品信息或item数组不存在', 'error');
                return false;
            }
            
            var processedCount = 0;
            var errorCount = 0;
            
            for(var i in goods_info['item']){
                // 检查spec_value_str是否存在，避免Undefined index错误
                if(!goods_info['item'][i] || typeof goods_info['item'][i]['spec_value_str'] === 'undefined') {
                    log('⚠️ 商品项缺少spec_value_str字段: ' + JSON.stringify(goods_info['item'][i]), 'warning');
                    errorCount++;
                    continue;
                }
                
                // 模拟表格行处理
                var spec_value_str = goods_info['item'][i]['spec_value_str'];
                log('✅ 处理规格: ' + spec_value_str, 'success');
                
                // 模拟数据赋值（使用安全的默认值）
                var spec_table_data = {};
                spec_table_data["spec_image[]"] = goods_info['item'][i]['abs_image'] || '';
                spec_table_data["price[]"] = goods_info['item'][i]['price'] || '';
                spec_table_data["stock[]"] = goods_info['item'][i]['stock'] || '';
                spec_table_data["spec_value_str[]"] = goods_info['item'][i]['spec_value_str'] || '';
                
                processedCount++;
            }
            
            log(`🎉 处理完成！成功: ${processedCount}, 错误: ${errorCount}`, processedCount > 0 ? 'success' : 'error');
            return true;
        }
        
        // 测试正常数据
        function testNormalData() {
            log('=== 测试正常数据 ===', 'info');
            var goods_info = {
                item: [
                    {
                        id: 1,
                        spec_value_str: '红色,S码',
                        price: '99.00',
                        stock: 10,
                        abs_image: '/uploads/image1.jpg'
                    },
                    {
                        id: 2,
                        spec_value_str: '蓝色,M码',
                        price: '109.00',
                        stock: 5,
                        abs_image: '/uploads/image2.jpg'
                    }
                ]
            };
            processGoodsInfo(goods_info);
        }
        
        // 测试缺失spec_value_str
        function testMissingSpecValueStr() {
            log('=== 测试缺失spec_value_str ===', 'info');
            var goods_info = {
                item: [
                    {
                        id: 1,
                        price: '99.00',
                        stock: 10,
                        abs_image: '/uploads/image1.jpg'
                        // 缺少spec_value_str字段
                    },
                    {
                        id: 2,
                        spec_value_str: '蓝色,M码',
                        price: '109.00',
                        stock: 5,
                        abs_image: '/uploads/image2.jpg'
                    }
                ]
            };
            processGoodsInfo(goods_info);
        }
        
        // 测试null数据
        function testNullData() {
            log('=== 测试null数据 ===', 'info');
            var goods_info = {
                item: [
                    null,
                    {
                        id: 2,
                        spec_value_str: null,
                        price: '109.00',
                        stock: 5,
                        abs_image: '/uploads/image2.jpg'
                    },
                    {
                        id: 3,
                        spec_value_str: '绿色,L码',
                        price: '119.00',
                        stock: 3,
                        abs_image: '/uploads/image3.jpg'
                    }
                ]
            };
            processGoodsInfo(goods_info);
        }
        
        // 测试空数组
        function testEmptyArray() {
            log('=== 测试空数组 ===', 'info');
            var goods_info = {
                item: []
            };
            processGoodsInfo(goods_info);
        }
        
        // 页面加载完成后初始化
        window.onload = function() {
            log('🔧 spec_value_str错误修复测试页面已加载', 'success');
            log('💡 点击测试按钮验证不同数据情况的处理', 'info');
        };
    </script>
</body>
</html>
