{layout name="layout2" /}
<style>
    .div-flex {
        display: flex;
        align-items: center;
        justify-content: left;
    }

    .width-160 {
        width: 200px;
    }
    .layui-table th {
        text-align: center;
    }
    .table-margin{
        margin-left: 50px;
        margin-right: 50px;
        text-align: center;
    }
    .image{
        height:80px;
        width: 80px;
    }

    .mt50{
        margin-left: 50px;
    }

</style>

<div class="layui-card-body" >
    <!--基本信息-->
    <div class="layui-form" lay-filter="layuiadmin-form-order" id="layuiadmin-form-order" >
    <input type="hidden" class="order_id" name="order_id" value="{$detail.id}">

    <div class="layui-form-item">
        <fieldset class="layui-elem-field layui-field-title">
            <legend>订单信息</legend>
        </fieldset>
    </div>

    <div class="layui-form-item div-flex">
        <label class="layui-form-label ">订单编号:</label>
        <div class="width-160">{$detail.order_sn}</div>
        <label class="layui-form-label ">订单类型:</label>
        <div class="width-160">{$detail.order_type_text}({$detail.order_source_text})</div>
        <label class="layui-form-label ">下单时间:</label>
        <div class="width-160">{$detail.create_time}</div>
    </div>

    <div class="layui-form-item div-flex">
        <label class="layui-form-label ">支付时间:</label>
        <div class="width-160">{$detail.pay_time}</div>
        <label class="layui-form-label ">支付状态:</label>
        <div class="width-160">{$detail.pay_status_text}</div>
        <label class="layui-form-label ">订单状态:</label>
        <div class="width-160">{$detail.order_status_text}</div>
    </div>

    <div class="layui-form-item">
        <fieldset class="layui-elem-field layui-field-title">
            <legend>会员信息</legend>
        </fieldset>
    </div>

    <div class="layui-form-item div-flex">
        <label class="layui-form-label ">会员编号:</label>
        <div class="width-160">{$detail.user.sn}</div>
        <label class="layui-form-label ">用户昵称:</label>
        <div class="width-160">{$detail.user.nickname}</div>
        <label class="layui-form-label ">手机号码:</label>
        <div class="width-160">{$detail.user.mobile}</div>
    </div>

    <div class="layui-form-item div-flex">
        <label class="layui-form-label ">性别:</label>
        <div class="width-160">{$detail.user.sex}</div>
        <label class="layui-form-label ">注册时间:</label>
        <div class="width-160">{$detail.user.create_time}</div>
    </div>

    {if $detail.delivery_type == 0 }
        <div class="layui-form-item">
            <fieldset class="layui-elem-field layui-field-title">
                <legend>收货信息</legend>
            </fieldset>
        </div>

        <div class="layui-form-item div-flex">
            <label class="layui-form-label ">收货人:</label>
            <div class="width-160">{$detail.consignee}</div>
            <label class="layui-form-label ">手机号:</label>
            <div class="width-160">{$detail.mobile}</div>
            <label class="layui-form-label ">收货地址:</label>
            <div class="width-160">{$detail.delivery_address}</div>
        </div>
    {/if}

    {if $detail.delivery_type == 2 }
        <div class="layui-form-item">
            <fieldset class="layui-elem-field layui-field-title">
                <legend>自提信息</legend>
            </fieldset>
        </div>

        <div class="layui-form-item div-flex">
            <label class="layui-form-label ">核销状态:</label>
            <div class="width-160">{$detail.verification_status_text | default = ''}</div>
            <label class="layui-form-label ">核销码:</label>
            <div class="width-160">{$detail.pickup_code | default = ''}</div>
            <label class="layui-form-label ">提货时间:</label>
            <div class="width-160">{$detail.confirm_take_time | default = ''}</div>
        </div>
    {/if}

    <div class="layui-form-item">
        <fieldset class="layui-elem-field layui-field-title">
            <legend>发票信息</legend>
        </fieldset>
    </div>

    <div class="layui-form-item div-flex">
        <label class="layui-form-label ">发票类型:</label>
        <div class="width-160">{$detail.invoice.type_text | default = ''}</div>
        <label class="layui-form-label ">抬头类型:</label>
        <div class="width-160">{$detail.invoice.header_type_text | default = ''}</div>
        <label class="layui-form-label ">发票抬头:</label>
        <div class="width-160">{$detail.invoice.name | default = ''}</div>
    </div>

    <div class="layui-form-item div-flex">
        <label class="layui-form-label ">税号:</label>
        <div class="width-160">{$detail.invoice.duty_number | default = ''}</div>
        <label class="layui-form-label ">邮箱:</label>
        <div class="width-160">{$detail.invoice.email | default = ''}</div>
        <label class="layui-form-label ">开户银行:</label>
        <div class="width-160">{$detail.invoice.bank | default = ''}</div>
    </div>

    <div class="layui-form-item div-flex">
        <label class="layui-form-label ">银行账号:</label>
        <div class="width-160">{$detail.invoice.bank | default = ''}</div>
        <label class="layui-form-label ">企业地址:</label>
        <div class="width-160">{$detail.invoice.address | default = ''}</div>
        <label class="layui-form-label ">企业电话:</label>
        <div class="width-160">{$detail.invoice.mobile | default = ''}</div>
    </div>


    <div class="layui-form-item">
        <fieldset class="layui-elem-field layui-field-title">
            <legend>商品信息</legend>
        </fieldset>
    </div>

    <?php $total_goods_money=0; ?>
    <div class="layui-form-item table-margin">
        <table class="layui-table">
                <colgroup>
                    <col width="250">
                    <col width="100">
                    <col width="100">
                    <col width="100">
                </colgroup>
                <thead>
                <tr>
                    <th>商品信息</th>
                    <th>价格(元)</th>
                    <th>数量</th>
                    <th>小计(元)</th>
                </tr>
                </thead>
                <tbody>
                {foreach $detail.order_goods as $k => $goods}
                <tr>
                    <td>
                        <div style="text-align: left">
                            <div class="layui-col-md3">
                                <img src="{$goods.goods_image}" class="image-show image" >
                            </div>
                            <div class="layui-col-md9">
                                <p style="margin-top: 10px">{$goods.goods_name}</p>
                                <br>
                                <p>{$goods.spec_value}</p>
                            </div>
                        </div>
                    </td>
                    <td>￥{$goods.goods_price}</td>
                    <td>{$goods.goods_num}</td>
                    <td>
                        <?php
                            $goods_money = bcadd($goods['goods_price'] * $goods['goods_num'],0, 2);
                            echo $goods_money;
                            $total_goods_money = bcadd($goods_money,$total_goods_money, 2);
                        ?>
                    </td>
                </tr>
                {/foreach}
                <tr>
                    <td colspan="3"></td>
                    <td style="text-align: left">
                        <p>商品金额:￥{$total_goods_money}</p>

                        <p>运费金额:￥{$detail.shipping_price}</p>

                        <p style="color: red">会员优惠:￥{$detail.member_amount}</p>

                        <p style="color: red">优惠金额:￥{$detail.discount_amount}</p>
                        <p>应付金额:￥{$detail.order_amount}</p>
                        <p>支付方式:{$detail.pay_way_text}</p>
                    </td>
                </tr>
                </tbody>
            </table>
    </div>


    {if ($detail.order_status > 1) and ($detail.shipping_status == 1) and ($detail.delivery_type == 1)}
        <div class="layui-form-item">
            <fieldset class="layui-elem-field layui-field-title">
                <legend>发货内容</legend>
            </fieldset>
        </div>

        <div class="layui-form-item table-margin">
            <textarea class="layui-textarea" disabled>{$detail.delivery_content}</textarea>
        </div>
    {/if}

    <div class="layui-form-item">
        <fieldset class="layui-elem-field layui-field-title">
            <legend>买家留言</legend>
        </fieldset>
    </div>

    <div class="layui-form-item table-margin">
        <textarea class="layui-textarea" disabled>{$detail.user_remark}</textarea>
    </div>


    <div class="layui-form-item">
        <fieldset class="layui-elem-field layui-field-title">
            <legend>订单操作</legend>
        </fieldset>
    </div>

    <div class="layui-form-item div-flex ">
        <div class="layui-input-block ">
            {eq name="$detail.order_status" value="0"}
            <button type="submit" class="layui-btn layui-btn-sm layui-btn-danger width_160 " id="cancel">取消订单</button>
            {/eq}

            {if ($detail.pay_status == 0 && $detail.pay_way == 4) }
            <a class="layui-btn layui-btn-danger layui-btn-sm operation-btn" id="confirmPay">确认付款</a>
            {/if}

            {if ($detail.send_btn) }
                <button type="submit" class="layui-btn layui-btn-sm layui-btn-normal width_160 " id="delivery">发货</button>
            {/if}

            {if ($detail.order_status == 2) }
                {if ($detail.delivery_type != 2) }
                    <button type="submit" class="layui-btn layui-btn-sm layui-btn-normal width_160 " id="confirm">确认收货</button>
                {/if}
            {/if}

            {if ($detail.verification_btn) }
            <a class="layui-btn layui-btn-normal layui-btn-sm operation-btn" id="verification">订单核销</a>
            {/if}

            {if ($detail.order_status == 4) && ($detail.del == 0) }
            <button type="submit" class="layui-btn layui-btn-sm layui-btn-danger width_160 " id="del">删除订单</button>
            {/if}
            <button type="button" class="layui-btn layui-btn-sm layui-btn-primary width_160 " id="back">返回</button>
        </div>
    </div>


    <div class="layui-form-item">
        <fieldset class="layui-elem-field layui-field-title">
            <legend>订单日志</legend>
        </fieldset>
    </div>

    <div class="layui-form-item table-margin">
        <table class="layui-table">
            <colgroup>
                <col width="100">
                <col width="100">
                <col width="250">
            </colgroup>
            <thead>
            <tr>
                <th>日期</th>
                <th>动作类型</th>
                <th>日志内容</th>
            </tr>
            </thead>
            <tbody>
                {empty name= "$logs"}
                    <tr>
                        <td colspan="3">暂无记录</td>
                    </tr>
                {else/}
                    {foreach $logs as $k => $log}
                    <tr>
                        <td>{$log.create_time}</td>
                        <td>{$log.channel}</td>
                        <td>{$log.content}</td>
                    </tr>
                    {/foreach}
                {/empty}
            </tbody>
        </table>
    </div>


</div>
</div>

<script type="text/javascript">
    //注意：选项卡 依赖 element 模块，否则无法进行功能性操作

    layui.config({
        version:"{$front_version}",
        base: '/static/lib/' //静态资源所在路径
    }).use(['form'], function(){
        var $ = layui.$;

        //主图放大
        $(document).on('click', '.image-show', function () {
            var src = $(this).attr('src');
            like.showImg(src,350);
        });


        $('#back').click(function () {
            var index=parent.layer.getFrameIndex(window.name); //获取当前窗口的name
            parent.layer.close(index);
            parent.layui.table.reload('order-lists');
            return true;
        });

        //取消订单
        var throttle = false;
        $('#cancel').click(function () {
            if (throttle) {
                layer.msg('请勿重复操作!');
                return
            }
            throttle = true;
            var order_id = $('.order_id').val();
            layer.confirm('确认取消订单吗?', {
                btn: ['确认','取消'] //按钮
            }, function(){
                like.ajax({
                    url: '{:url("order.order/cancel")}'
                    , data: {'order_id': order_id}
                    , type: 'post'
                    , success: function (res) {
                        throttle = false;
                        if (res.code == 1) {
                            layui.layer.msg(res.msg, {
                                offset: '15px'
                                , icon: 1
                                , time: 1000
                            },function () {
                                location.reload();
                            });
                        } else {
                            layer.msg(res.msg, {icon: 2}, function(){
                                // layer.msg('提示框关闭后的回调');
                            });
                        }
                    }
                    , error: function (res) {
                        throttle = false;
                        layer.msg(res.statusText);
                    }
                });
            });
        });


        //删除订单
        $('#del').click(function () {
            var order_id = $('.order_id').val();
            layer.confirm('删除后订单将消失，确认删除订单吗?', {
                btn: ['确认','取消'] //按钮
            }, function(){
                like.ajax({
                    url: '{:url("order.order/del")}'
                    , data: {'order_id': order_id}
                    , type: 'post'
                    , success: function (res) {
                        if (res.code == 1) {
                            layui.layer.msg(res.msg, {
                                offset: '15px'
                                , icon: 1
                                , time: 1000
                            },function () {
                                location.reload();
                            });
                        }
                    }
                });
            });
        });


        //退款
        $('#refund').click(function () {
            var order_id = $('.order_id').val();
            layer.msg('暂不可退');
        });

        //发货
        $('#delivery').click(function () {
            var id = $('.order_id').val();
            layer.open({
                type: 2
                ,title: '订单发货'
                ,content: '{:url("order.order/delivery")}?id='+id
                ,area: ['90%', '90%']
                ,yes: function(index, layero){
                }
            })
        });

        //确认收货
        $('#confirm').click(function () {
            var id = $('.order_id').val();
            layer.confirm('确认订单商品已收货吗？?', {
                btn: ['确认','取消'] //按钮
            }, function(){
                like.ajax({
                    url: '{:url("order.order/confirm")}'
                    , data: {'order_id': id}
                    , type: 'post'
                    , success: function (res) {
                        if (res.code == 1) {
                            layui.layer.msg(res.msg, {
                                offset: '15px'
                                , icon: 1
                                , time: 1000
                            },function () {
                                location.reload();
                            });
                        }
                    },
                });
            });
        });

        $('#verification').click(function () {
            var id = $('.order_id').val();
            layer.confirm('确定核销订单吗？请谨慎操作', {
                btn: ['确认','取消']
            }, function(){
                like.ajax({
                    url: '{:url("order.order/verification")}'
                    , data: {'order_id': id}
                    , type: 'post'
                    , success: function (res) {
                        if (res.code == 1) {
                            layui.layer.msg(res.msg, {
                                offset: '15px'
                                , icon: 1
                                , time: 1000
                            },function () {
                                location.reload();
                            });
                        }
                    },
                });
            });
        });

        //确认付款
        $('#confirmPay').click(function () {
            var id = $('.order_id').val();
            layer.confirm('确认付款订单吗？请谨慎操作', {
                btn: ['确认','取消'] //按钮
            }, function(){
                like.ajax({
                    url: '{:url("order.order/confirmPay")}'
                    , data: {'order_id': id}
                    , type: 'post'
                    , success: function (res) {
                        if (res.code == 1) {
                            layui.layer.msg(res.msg, {
                                offset: '15px'
                                , icon: 1
                                , time: 1000
                            },function () {
                                location.reload();
                            });
                        }
                    },
                });
            });
        });
    });
</script>