{layout name="layout1" /}

<style>
    .layui-table-cell{
        height:auto;
        overflow:hidden;
        text-overflow:inherit;
    }

    /* 优化操作列按钮样式 */
    .layui-table-body .layui-btn-xs {
        margin: 1px 1px;
        padding: 2px 6px;
        font-size: 11px;
        line-height: 1.4;
        border-radius: 2px;
    }

    /* 确保操作列内容不换行 */
    .layui-table td[data-field="0"] {
        white-space: nowrap;
    }

    /* 操作列按钮容器 */
    .layui-table .layui-table-cell {
        padding: 3px 5px;
        line-height: 1.2;
    }

    /* 操作列特殊样式 */
    .layui-table tbody tr td:last-child .layui-table-cell {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        align-items: center;
        gap: 2px;
    }
</style>

<div class="wrapper">
    <div class="layui-card">
        <!-- 操作提示 -->
        <div class="layui-card-body">
            <div class="layui-collapse" style="border:1px dashed #c4c4c4">
                <div class="layui-colla-item">
                    <h2 class="layui-colla-title like-layui-colla-title">操作提示</h2>
                    <div class="layui-colla-content layui-show">
                        <p>*查看并管理平台所有自营和入驻商家。</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 搜索区域 -->
        <div class="layui-card-body layui-form">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label for="name" class="layui-form-label">商家名称：</label>
                    <div class="layui-input-inline">
                        <input type="text" id="name" name="name" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label for="type" class="layui-form-label">检验状态：</label>
                    <div class="layui-input-inline">
                        <select name="is_yan" id="is_yan">
                            <option value="">全部</option>
                            <option value="1">已缴费已检验</option>
                            <option value="2">已缴费待检验</option>
                            <option value="3">未交费</option>
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <label for="type" class="layui-form-label">商家标识类型：</label>
                    <div class="layui-input-inline">
                        <select name="type" id="type">
                            <option value="">全部</option>
                            <option value="1">权威验厂</option>
                            <option value="2">权威验商</option>
                            <option value="3">权威品牌</option>
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <label for="cid" class="layui-form-label">主营类目：</label>
                    <div class="layui-input-inline">
                        <select name="cid" id="cid">
                            <option value="">全部</option>
                            {volist name="category" id="vo"}
                                <option value="{$vo.id}">{$vo.name}</option>
                            {/volist}
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <label for="tier_level" class="layui-form-label">商家等级：</label>
                    <div class="layui-input-inline">
                        <select name="tier_level" id="tier_level">
                            <option value="">全部</option>
                            <option value="0">0元入驻</option>
                            <option value="1">商家会员</option>
                            <option value="2">实力厂商</option>
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <label for="is_recommend" class="layui-form-label">推荐商家：</label>
                    <div class="layui-input-inline">
                        <select name="is_recommend" id="is_recommend">
                            <option value="">全部</option>
                            <option value="1">推荐</option>
                            <option value="0">不推荐</option>
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <label for="is_run" class="layui-form-label">营业状态：</label>
                    <div class="layui-input-inline">
                        <select name="is_run" id="is_run">
                            <option value="">全部</option>
                            <option value="1">营业中</option>
                            <option value="0">暂停营业</option>
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <label for="is_freeze" class="layui-form-label">商家状态：</label>
                    <div class="layui-input-inline">
                        <select name="is_freeze" id="is_freeze">
                            <option value="">全部</option>
                            <option value="0">正常</option>
                            <option value="1">冻结</option>
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <label for="is_freeze" class="layui-form-label">到期状态：</label>
                    <div class="layui-input-inline">
                        <select name="expire_status" id="expire_status">
                            <option value="">全部</option>
                            <option value="0">未到期</option>
                            <option value="1">已到期</option>
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">到期时间：</label>
                    <div class="layui-inline" style="margin-right:0;">
                        <div class="layui-input-inline" >
                            <input type="text" id="expire_start_time" name="expire_start_time" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline"> - </div>
                    <div class="layui-inline" style="margin-right:0;">
                        <div class="layui-input-inline" style="margin-right:0;">
                            <input type="text" id="expire_end_time" name="expire_end_time" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                </div>
                <div class="layui-inline">
                    <a class="layui-btn layui-btn-sm layui-btn-normal" lay-submit lay-filter="search">搜索</a>
                    <a class="layui-btn layui-btn-sm layui-btn-primary" lay-submit lay-filter="clear-search">重置</a>
                </div>
            </div>
        </div>

        <!-- 主体区域 -->
        <div class="layui-card-body">
            <button type="button" class="layui-btn layui-btn-normal layui-btn-sm layEvent" lay-event="add">新增商家</button>
            <button type="button" class="layui-btn layui-btn-primary layui-btn-sm layEvent" lay-event="business">批量营业</button>
            <button type="button" class="layui-btn layui-btn-primary layui-btn-sm layEvent" lay-event="stop_business">暂停营业</button>
            <button type="button" class="layui-btn layui-btn-primary layui-btn-sm layEvent" lay-event="freeze">冻结商家</button>
            <button type="button" class="layui-btn layui-btn-primary layui-btn-sm layEvent" lay-event="cancel_freeze">取消冻结</button>

            <table id="like-table-lists" lay-filter="like-table-lists"></table>
            <script type="text/html" id="table-storeInfo">
                <img src="{{d.logo}}" alt="图标" style="width:60px;height:60px;margin-right:5px;">
                <div class="layui-inline" style="text-align:left;">
                    <p>商家编号：{{d.id}}</p>
                    <p>商家名称：{{d.name}}</p>
                    <p>商家类型： {{d.type}}</p>
                </div>
            </script>
            <script type="text/html" id="table-level">
                {{#  if(d.level_info_image !=''){ }}
                <div class="layui-inline" style="text-align:left;">
                    <p>商家标识：
                        <img src="{{d.level_info_image}}" alt="log" style="width:18px;height:18px;">{{d.level_info_name}}</p>
                    <p>有效期：{{d.yan_time}}</p>
                        {{#  } else { }}
                        {{#  } }}

                </div>
            </script>
            <script type="text/html" id="table-yan-status">
                {{#  if(d.yan_fee != 1){ }}
                    <span class="layui-badge">未缴纳检验费</span>
                {{#  } else if(d.yan_fee == 1 && !d.has_inspection_record && (d.f_user == 0 || d.f_user == null)){ }}
                    <span class="layui-badge layui-bg-orange">待派遣检验人员</span>
                {{#  } else if(d.yan_fee == 1 && !d.has_inspection_record && d.f_user > 0){ }}
                    <span class="layui-badge layui-bg-blue">已分配人员待检验({{d.staff_nickname || '未知'}})</span>
                {{#  } else if(d.yan_fee == 1 && d.has_inspection_record && d.inspection_status == 0){ }}
                    <span class="layui-badge layui-bg-cyan">检验信息待审核</span>
                {{#  } else if(d.yan_fee == 1 && d.has_inspection_record && d.inspection_status == 1){ }}
                    <span class="layui-badge layui-bg-green">检验已通过</span>
                {{#  } else if(d.yan_fee == 1 && d.has_inspection_record && d.inspection_status == 2){ }}
                    <span class="layui-badge layui-bg-red">检验未通过</span>
                {{#  } else { }}
                    <span class="layui-badge layui-bg-gray">状态异常</span>
                {{#  } }}
            </script>

            <script type="text/html" id="table-goodsCount">
                <a class="layui-btn layui-btn-xs layui-btn-normal" lay-event="viewGoods" lay-data="{{d.id}}">{{d.goods_count}}</a>
            </script>

            <script type="text/html" id="table-operation">
                <a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="edit">编辑</a>
                <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="set">设置</a>
                <a class="layui-btn layui-btn-warm layui-btn-xs" lay-event="account">账号</a>
                {{#  if(d.yan_fee == 1){ }}
                <a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="inspection">检验</a>
                {{#  } }}
                <a class="layui-btn layui-btn-info layui-btn-xs" lay-event="check_qualification">资质检测</a>
            </script>
        </div>

    </div>
</div>

<script>
    layui.use(["table", "form","laydate"], function(){
        var table   = layui.table;
        var form    = layui.form;
        var laydate = layui.laydate;

        //日期时间范围
        laydate.render({
            elem: '#expire_start_time'
            , type: 'datetime'
            ,theme: '#1E9FFF'
        });

        laydate.render({
            elem: '#expire_end_time'
            , type: 'datetime'
            ,theme: '#1E9FFF'
        });


        // 初始化表格
        var tableIns = like.tableLists("#like-table-lists", "{:url()}", [
            {type: 'checkbox'}
            ,{field:"id", width:60, title:"ID"}
            ,{field:"storeInfo", width:250, title:"商家信息", templet:"#table-storeInfo"}
            ,{field:"account", width:100, align:"center",title:"商家账号"}
            ,{field:"tier_level_name", width:100, align:"center", title:"商家等级"}
            ,{field:"goods_count", width:100, align:"center", title:"商品数量", templet:"#table-goodsCount"}
            ,{field:"tier_expire_time_text", width:120, align:"center", title:"等级到期"}
            ,{field:"level", width:150, align:"center",title:"检验标识",templet:"#table-level"}
            ,{field:"yan_status", width:150, align:"center", title:"检验状态",templet:"#table-yan-status"}
            ,{field:"category", width:100, align:"center", title:"主营类目"}
            ,{field:"is_run", width:90, align:"center", title:"营业状态"}
            ,{field:"is_freeze", width:90, align:"center", title:"商家状态"}
            ,{field:"is_recommend", width:90, align:"center", title:"推荐商家"}
            ,{field:"create_time", width:160, align:"center", title:"开通日期"}
            ,{field:"expire_desc", width:160, align:"center", title:"到期状态"}
            ,{field:"expire_time", width:160, align:"center", title:"到期时间"}
            ,{title:"操作", width:280, align:"center", fixed:"right", toolbar:"#table-operation"}
        ]);


        var active = {
            add: function() {
                layer.open({
                    type: 2
                    ,title: "新增商家"
                    ,content: "{:url('shop.Store/add')}"
                    ,area: ["90%", "90%"]
                    ,btn: ["确定", "取消"]
                    ,yes: function(index, layero){
                        var iframeWindow = window["layui-layer-iframe" + index];
                        var submit = layero.find("iframe").contents().find("#addSubmit");
                        iframeWindow.layui.form.on("submit(addSubmit)", function(data){
                            like.ajax({
                                url: "{:url('shop.Store/add')}",
                                data: data.field,
                                type: "POST",
                                success:function(res) {
                                    if(res.code === 1) {
                                        layui.layer.msg(res.msg);
                                        layer.close(index);
                                        table.reload("like-table-lists", {
                                            where: {},
                                            page: { cur: 1 }
                                        });
                                    }
                                }
                            });
                        });
                        submit.trigger("click");
                    }
                });
            },
            edit: function(obj) {
                layer.open({
                    type: 2
                    ,title: "编辑商家"
                    ,content: "{:url('shop.Store/edit')}?id=" + obj.data.id
                    ,area: ["90%", "90%"]
                    ,btn: ["确定", "取消"]
                    ,yes: function(index, layero){
                        var iframeWindow = window["layui-layer-iframe" + index];
                        var submit = layero.find("iframe").contents().find("#addSubmit");
                        iframeWindow.layui.form.on("submit(addSubmit)", function(data){
                            data.field['id'] = obj.data.id;
                            like.ajax({
                                url: "{:url('shop.Store/edit')}",
                                data: data.field,
                                type: "POST",
                                success:function(res) {
                                    if(res.code === 1) {
                                        layui.layer.msg(res.msg);
                                        layer.close(index);
                                        table.reload("like-table-lists", {
                                            where: {},
                                            page: { cur: 1 }
                                        });
                                    }
                                }
                            });
                        });
                        submit.trigger("click");
                    }
                });
            },
            set: function(obj) {
                layer.open({
                    type: 2
                    ,title: "商家设置"
                    ,content: "{:url('shop.Store/set')}?id=" + obj.data.id
                    ,area: ["580px", "480px"]
                    ,btn: ["确定", "取消"]
                    ,yes: function(index, layero){
                        var iframeWindow = window["layui-layer-iframe" + index];
                        var submit = layero.find("iframe").contents().find("#addSubmit");
                        iframeWindow.layui.form.on("submit(addSubmit)", function(data){
                            data.field['id'] = obj.data.id;
                            like.ajax({
                                url: "{:url('shop.Store/set')}",
                                data: data.field,
                                type: "POST",
                                success:function(res) {
                                    if(res.code === 1) {
                                        layui.layer.msg(res.msg);
                                        layer.close(index);
                                        table.reload("like-table-lists", {
                                            where: {},
                                            page: { cur: 1 }
                                        });
                                    }
                                }
                            });
                        });
                        submit.trigger("click");
                    }
                });
            },
            account: function(obj) {
                layer.open({
                    type: 2
                    ,title: "账号管理"
                    ,content: "{:url('shop.Store/account')}?id=" + obj.data.id
                    ,area: ["480px", "420px"]
                    ,btn: ["确定", "取消"]
                    ,yes: function(index, layero){
                        var iframeWindow = window["layui-layer-iframe" + index];
                        var submit = layero.find("iframe").contents().find("#addSubmit");
                        iframeWindow.layui.form.on("submit(addSubmit)", function(data){
                            data.field['id'] = obj.data.id;
                            like.ajax({
                                url: "{:url('shop.Store/account')}",
                                data: data.field,
                                type: "POST",
                                success:function(res) {
                                    if(res.code === 1) {
                                        layui.layer.msg(res.msg);
                                        layer.close(index);
                                        table.reload("like-table-lists", {
                                            where: {},
                                            page: { cur: 1 }
                                        });
                                    }
                                }
                            });
                        });
                        submit.trigger("click");
                    }
                });
            },
            business: function() {
                batchOperation('is_run', 1);
            },
            stop_business: function() {
                batchOperation('is_run', 0);
            },
            freeze: function() {
                batchOperation('is_freeze', 1);
            },
            cancel_freeze: function() {
                batchOperation('is_freeze', 0);
            },
            inspection: function(obj) {
                console.log('检验按钮点击，shop_id:', obj.data.id);
                // 先获取检验状态，决定是否显示保存按钮
                like.ajax({
                    url: "{:url('shop.Store/getInspectionStatus')}",
                    data: { shop_id: obj.data.id },
                    type: "GET",
                    success: function(statusRes) {
                        console.log('获取检验状态成功:', statusRes);
                        var buttons = ["关闭"];
                        var btnCallback = null;

                        // 如果没有检验信息或状态不是待审核，则显示保存按钮
                        if (!statusRes.data || Object.keys(statusRes.data).length === 0 || statusRes.data.status != 0) {
                            console.log('允许编辑，显示保存按钮');
                            buttons = ["保存", "取消"];
                            btnCallback = function(index, layero) {
                                console.log('保存按钮点击');
                                var iframeWindow = window["layui-layer-iframe" + index];
                                var submit = layero.find("iframe").contents().find("#inspectionSubmit");
                                iframeWindow.layui.form.on("submit(inspectionSubmit)", function(formData) {
                                    console.log('检验表单提交:', formData.field);
                                    formData.field['shop_id'] = obj.data.id;
                                    like.ajax({
                                        url: "{:url('shop.Store/saveInspection')}",
                                        data: formData.field,
                                        type: "POST",
                                        success: function(res) {
                                            console.log('保存检验信息结果:', res);
                                            if (res.code === 1) {
                                                layui.layer.msg(res.msg);
                                                layer.close(index);
                                                table.reload("like-table-lists", {
                                                    where: {},
                                                    page: { cur: 1 }
                                                });
                                            } else {
                                                layui.layer.msg(res.msg, {icon: 2});
                                            }
                                        },
                                        error: function(xhr, status, error) {
                                            console.error('保存检验信息失败:', error);
                                            layer.msg('保存失败，请查看控制台日志', {icon: 2});
                                        }
                                    });
                                });
                                submit.trigger("click");
                            };
                        } else {
                            console.log('不允许编辑，仅显示关闭按钮');
                        }

                        console.log('打开检验模态框');
                        layer.open({
                            type: 2,
                            title: false, // 不显示标题栏
                            closeBtn: 0, // 不显示关闭按钮
                            content: "{:url('shop.Store/inspection')}?shop_id=" + obj.data.id,
                            area: ["95%", "90%"],
                            btn: buttons,
                            yes: btnCallback,
                            shade: 0.3,
                            maxmin: false,
                            skin: 'layui-layer-rim layui-layer-border',
                            success: function(layero, index) {
                                // 为弹窗添加圆角样式
                                layero.css({
                                    'border-radius': '12px',
                                    'overflow': 'hidden'
                                });

                                // 等待iframe加载完成后，传递检验数据用于回显
                                console.log('准备传递检验数据:', statusRes.data);

                                // 使用多次尝试的方式确保数据传递成功
                                var tryCount = 0;
                                var maxTries = 10;
                                var tryInterval = setInterval(function() {
                                    tryCount++;
                                    console.log('尝试传递数据，第' + tryCount + '次');

                                    try {
                                        var iframeWindow = window["layui-layer-iframe" + index];
                                        console.log('iframe窗口对象:', iframeWindow);

                                        if (iframeWindow && iframeWindow.setInspectionData) {
                                            console.log('找到setInspectionData函数，开始传递数据');
                                            if (statusRes.data && Object.keys(statusRes.data).length > 0) {
                                                console.log('传递的检验数据:', statusRes.data);
                                                iframeWindow.setInspectionData(statusRes.data);
                                                clearInterval(tryInterval);
                                                console.log('数据传递成功');
                                            } else {
                                                console.log('没有检验数据需要传递');
                                                clearInterval(tryInterval);
                                            }
                                        } else if (tryCount >= maxTries) {
                                            console.error('达到最大尝试次数，数据传递失败');
                                            clearInterval(tryInterval);
                                        } else {
                                            console.log('iframe还未准备好，继续等待...');
                                        }
                                    } catch (e) {
                                        console.error('传递检验数据失败:', e);
                                        if (tryCount >= maxTries) {
                                            clearInterval(tryInterval);
                                        }
                                    }
                                }, 300);
                            },
                            error: function(layero, index){
                                console.error('打开模态框失败');
                                layer.msg('打开检验窗口失败', {icon: 2});
                            }
                        });
                    },
                    error: function(xhr, status, error) {
                        console.error('获取检验状态失败:', error);
                        layer.msg('获取检验状态失败，请查看控制台日志', {icon: 2});
                    }
                });
            },
            viewGoods: function(shopId) {
                layer.open({
                    type: 2,
                    title: '商家商品列表',
                    content: "{:url('shop.Store/goodsList')}?shop_id=" + shopId.data.id,
                    area: ['80%', '80%'],
                    btn: ['关闭'],
                    yes: function(index, layero){
                        layer.close(index);
                    }
                });
            },
            check_qualification:function(shopId){
                like.ajax({
                    url: "{:url('shop.Store/checkShopQualification')}", // 后端接口
                    data: { shop_id: shopId.data.id },
                    type: "POST",
                    success: function(res) {
                       
                        if (res.code === 1) {
                            layer.closeAll(); // 关闭所有对话框
                            layer.open({
                                type: 1, // 页面层
                                title: '资质检测结果',
                                area: ['500px', 'auto'], // 宽度固定，高度自适应
                                content: '<div style="padding: 25px 30px;">' +
                                    '<p style="font-size: 16px; color: #333; font-weight: bold; margin-bottom: 15px;">资质检测结果：</p>' +
                                    '<div style="font-size: 14px; color: #666; line-height: 1.6; word-wrap: break-word;">' +
                                        res.msg +
                                    '</div>' +
                                    '<p style="font-size: 14px; color: #ff6b6b; margin-top: 20px; border-top: 1px solid #eee; padding-top: 15px;">' +
                                        '提醒：请核对资质信息，确保店铺正常运营。' +
                                    '</p>' +
                                '</div>',
                                skin: 'layui-layer-molv', // 墨绿风格，表示成功
                                closeBtn: 0, // 不显示右上角关闭按钮
                                btn: ['确认'], // 仅显示确认按钮
                                yes: function(index){
                                    layer.close(index);
                                },
                                shadeClose: false // 禁止点击遮罩关闭
                            });
                        } else {
                            layer.closeAll(); // 关闭所有对话框
                            layer.open({
                                type: 1, // 页面层
                                title: '资质检测结果',
                                area: ['500px', 'auto'], // 宽度固定，高度自适应
                                content: '<div style="padding: 25px 30px;">' +
                                    '<p style="font-size: 16px; color: #333; font-weight: bold; margin-bottom: 15px;">资质检测结果：</p>' +
                                    '<div style="font-size: 14px; color: #666; line-height: 1.6; word-wrap: break-word;">' +
                                        res.msg +
                                    '</div>' +
                                    '<p style="font-size: 14px; color: #ff6b6b; margin-top: 20px; border-top: 1px solid #eee; padding-top: 15px;">' +
                                        '提醒：请核对资质信息，确保店铺正常运营。' +
                                    '</p>' +
                                '</div>',
                                skin: 'layui-layer-lan', // 深蓝风格，表示失败
                                closeBtn: 0, // 不显示右上角关闭按钮
                                btn: ['确认'], // 仅显示确认按钮
                                yes: function(index){
                                    layer.close(index);
                                },
                                shadeClose: false // 禁止点击遮罩关闭
                            });
                        }
                    },
                    error: function(xhr, status, error) {
                        layer.closeAll(); // 关闭所有对话框
                        layer.closeAll('loading'); // 关闭加载动画
                        console.error('资质检测请求失败:', error);
                        layer.open({
                            type: 1, // 页面层
                            title: '错误',
                            area: ['500px', 'auto'], // 宽度固定，高度自适应
                            content: '<div style="padding: 25px 30px;">' +
                                '<p style="font-size: 16px; color: #333; font-weight: bold; margin-bottom: 15px;">资质检测失败：</p>' +
                                '<div style="font-size: 14px; color: #666; line-height: 1.6; word-wrap: break-word;">' +
                                    '请查看控制台日志获取详细信息。' + // 固定提示语
                                '</div>' +
                                '<p style="font-size: 14px; color: #ff6b6b; margin-top: 20px; border-top: 1px solid #eee; padding-top: 15px;">' +
                                    '提醒：请联系管理员或稍后重试。' + // 固定提示语
                                '</p>' +
                            '</div>',
                            skin: 'layui-layer-lan', // 深蓝风格
                            closeBtn: 0, // 不显示右上角关闭按钮
                            btn: ['确认'], // 仅显示确认按钮
                            yes: function(index){
                                layer.close(index);
                            },
                            shadeClose: false // 禁止点击遮罩关闭
                        });
                    }
                });
            }

        };
        like.eventClick(active);


        // 批量更新状态
        function batchOperation(field, value) {
            var checkStatus = table.checkStatus('like-table-lists');
            var checkData = checkStatus.data;
            var ids = [];
            // 取出选中的行ID
            checkData.forEach(function (item) {
                ids.push(parseInt(item['id']));
            });
            if (ids.length <= 0) {
                layui.layer.msg('请选择商家', {time: 1000});
                return false;
            }
            // 提交数据
            like.ajax({
                url:'{:url("shop.Store/batchOperation")}',
                data:{"ids":ids, 'field': field, 'value': value},
                type:"post",
                success:function(res) {
                    if(res.code === 1) {
                        layui.layer.msg(res.msg, {offset:'15px', icon:1 ,time: 1000});
                        table.reload('like-table-lists', { where: [] });
                    }
                }
            });
        }



        form.on("submit(search)", function(data){
            table.reload("like-table-lists", {
                where: data.field,
                page: {
                    curr: 1
                }
            });
        });


        form.on("submit(clear-search)", function(){
            $("#name").val("");
            $("#type").val("");
            $("#cid").val("");
            $("#is_recommend").val("");
            $("#is_run").val("");
            $("#is_freeze").val("");
            $("#expire_status").val("");
            $("#expire_start_time").val("");
            $("#expire_end_time").val("");
            form.render("select");
            table.reload("like-table-lists", {
                where: {},
                page: {
                    curr: 1
                }
            });
        });

        // 表格工具条事件
        table.on('tool(like-table-lists)', function(obj){
            var data = obj.data;
            var event = obj.event;
            console.log("工具条事件触发:", event, data);

            // 调试信息
            console.log("事件对象:", obj);
            console.log("事件名称:", event);
            console.log("行数据:", data);

            if (event === 'assign_staff') {
                // 派遣检验人员
                console.log("打开派遣检验人员模态窗口");
                layer.open({
                    type: 1,
                    title: '派遣检验人员',
                    area: ['500px', '400px'],
                    content: '<div class="layui-card-body" style="padding: 20px;">' +
                        '<div class="layui-form">' +
                        '<div class="layui-form-item">' +
                        '<label class="layui-form-label">搜索条件：</label>' +
                        '<div class="layui-input-inline">' +
                        '<select id="search_type" lay-filter="search_type">' +
                        '<option value="mobile">手机号</option>' +
                        '<option value="nickname">昵称</option>' +
                        '</select>' +
                        '</div>' +
                        '<div class="layui-input-inline">' +
                        '<input type="text" id="search_value" placeholder="请输入搜索内容" autocomplete="off" class="layui-input">' +
                        '</div>' +
                        '<button type="button" class="layui-btn layui-btn-normal" id="search_staff">搜索</button>' +
                        '</div>' +
                        '<div id="staff_list" style="margin-top: 20px;"></div>' +
                        '</div>' +
                        '</div>',
                    success: function(layero, index) {
                        form.render('select');

                        // 搜索检验人员
                        console.log("绑定搜索按钮事件");
                        $(layero).find('#search_staff').on('click', function() {
                            console.log("搜索按钮点击");
                            var search_type = $(layero).find('#search_type').val();
                            var search_value = $(layero).find('#search_value').val();

                            if (!search_value) {
                                layer.msg('请输入搜索内容');
                                return;
                            }

                            // 发送请求搜索检验人员
                            var searchUrl = "{:url('shop.Store/searchStaff')}";
                            console.log("搜索URL:", searchUrl);
                            like.ajax({
                                url: searchUrl,
                                data: {
                                    search_type: search_type,
                                    search_value: search_value
                                },
                                type: "GET",
                                success: function(res) {
                                    if (res.code === 1) {
                                        var html = '';
                                        if (res.data.length > 0) {
                                            html += '<table class="layui-table">';
                                            html += '<thead><tr><th>ID</th><th>昵称</th><th>手机号</th><th>操作</th></tr></thead>';
                                            html += '<tbody>';

                                            $.each(res.data, function(index, item) {
                                                html += '<tr>';
                                                html += '<td>' + item.id + '</td>';
                                                html += '<td>' + item.nickname + '</td>';
                                                html += '<td>' + item.mobile + '</td>';
                                                html += '<td><button class="layui-btn layui-btn-xs layui-btn-normal select-staff" data-id="' + item.id + '" data-nickname="' + item.nickname + '">选择</button></td>';
                                                html += '</tr>';
                                            });

                                            html += '</tbody></table>';
                                        } else {
                                            html = '<div class="layui-text" style="text-align: center; padding: 20px 0;">未找到相关人员</div>';
                                        }

                                        $(layero).find('#staff_list').html(html);

                                        // 选择检验人员
                                        console.log("绑定选择按钮事件");
                                        $(layero).find('.select-staff').on('click', function() {
                                            console.log("选择按钮点击");
                                            var staff_id = $(this).data('id');
                                            var staff_nickname = $(this).data('nickname');

                                            // 确认选择
                                            layer.confirm('确定选择 ' + staff_nickname + ' 作为检验人员？', {
                                                btn: ['确定', '取消']
                                            }, function() {
                                                // 发送请求分配检验人员
                                                var assignUrl = "{:url('shop.Store/assignStaff')}";
                                                console.log("分配URL:", assignUrl);
                                                like.ajax({
                                                    url: assignUrl,
                                                    data: {
                                                        shop_id: data.id,
                                                        staff_id: staff_id
                                                    },
                                                    type: "POST",
                                                    success: function(res) {
                                                        if (res.code === 1) {
                                                            layer.msg(res.msg, {icon: 1});
                                                            layer.close(index);
                                                            table.reload('like-table-lists');
                                                        } else {
                                                            layer.msg(res.msg, {icon: 2});
                                                        }
                                                    }
                                                });
                                            });
                                        });
                                    } else {
                                        layer.msg(res.msg, {icon: 2});
                                    }
                                }
                            });
                        });
                    }
                });
            } else if (event === 'edit') {
                active.edit(obj);
            } else if (event === 'set') {
                active.set(obj);
            } else if (event === 'account') {
                active.account(obj);
            } else if (event === 'inspection') {
                active.inspection(obj);
            } else if (event === 'viewGoods') {
                active.viewGoods(parseInt(data.id));
            } 
        });
    })
</script>
