<?php

namespace app\api\controller;

use app\common\basics\Api;
use app\common\logic\SmsLogic;
use app\api\validate\SmsSend;
use app\common\server\JsonServer;

class Sms extends Api
{
    public $like_not_need_login = ['send'];

    /**
     * 发送短信
     */
    public function send()
    {
        $client = $this->client;
        $mobile = $this->request->post('mobile');
        $key = $this->request->post('key');
        (new SmsSend())->goCheck('', ['mobile' => $mobile, 'key' => $key,'client' => $client,'user_id' => $this->user_id]);
        $result = SmsLogic::send($mobile, $key, $this->user_id);
        return JsonServer::success('发送成功',$result);
    }
}