How to contribute & use the issue tracker
=========================================

Nette welcomes your contributions. There are several ways to help out:

* Create an issue on GitHub, if you have found a bug
* Write test cases for open bug issues
* Write fixes for open bug/feature issues, preferably with test cases included
* Contribute to the [documentation](https://nette.org/en/writing)

Issues
------

Please **do not use the issue tracker to ask questions**. We will be happy to help you
on [Nette forum](https://forum.nette.org) or chat with us on [Gitter](https://gitter.im/nette/nette).

A good bug report shouldn't leave others needing to chase you up for more
information. Please try to be as detailed as possible in your report.

**Feature requests** are welcome. But take a moment to find out whether your idea
fits with the scope and aims of the project. It's up to *you* to make a strong
case to convince the project's developers of the merits of this feature.

Contributing
------------

If you'd like to contribute, please take a moment to read [the contributing guide](https://nette.org/en/contributing).

The best way to propose a feature is to discuss your ideas on [Nette forum](https://forum.nette.org) before implementing them.

Please do not fix whitespace, format code, or make a purely cosmetic patch.

Thanks! :heart:
