<?php


namespace app\shop\validate\goods;


use app\common\basics\Validate;
use app\common\model\goods\Goods;


/**
 * 供货商
 * Class SupplierValidate
 * @package app\admin\validate
 */
class SupplierValidate extends Validate
{

    protected $rule = [
        'id'        => 'require',
        'name'      => 'require|unique:supplier,name&del',
        'contact'   => 'require',
        'mobile'    => 'require|mobile',
        'address'   => 'require',
    ];

    protected $message = [
        'id.require'        => '参数缺失',
        'name.require'      => '参数缺失',
        'name.unique'       => '该名称已被使用',
        'contact.require'   => '请填写联系人',
        'mobile.require'    => '请填写联系电话',
        'mobile.mobile'     => '请填写正确联系电话',
        'address.require'   => '请填写联系地址',
    ];

    protected $scene = [
        'add'  =>  ['name', 'contact', 'mobile', 'address'],
        'edit' =>  ['name', 'contact', 'mobile', 'address'],
    ];

    public function sceneDel()
    {
        return $this->only(['id'])
            ->append('id','checkDel');
    }


    protected function checkDel($value,$rule,$data)
    {
        $check = Goods::where('supplier_id', $value)->find();
        if ($check) {
            return '供货商已经关联商品，无法删除';
        }
        return true;
    }

}