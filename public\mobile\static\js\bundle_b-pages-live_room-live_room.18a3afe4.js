(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["bundle_b-pages-live_room-live_room"],{"0427":function(t,e,a){"use strict";a.r(e);var i=a("fe1a"),A=a.n(i);for(var n in i)["default"].indexOf(n)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(n);e["default"]=A.a},"0498":function(t,e,a){var i=a("a21d");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var A=a("4f06").default;A("b3712138",i,!0,{sourceMap:!1,shadowMode:!1})},"0e29":function(t,e,a){"use strict";a.r(e);var i=a("23ae"),A=a.n(i);for(var n in i)["default"].indexOf(n)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(n);e["default"]=A.a},"1e2e":function(t,e,a){var i=a("3096");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var A=a("4f06").default;A("ccafd518",i,!0,{sourceMap:!1,shadowMode:!1})},"23ae":function(t,e,a){"use strict";a("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=a("a5ae"),A={name:"live-item",props:{data:{type:Object,default:function(){return{}}},type:{type:String,default:"column"}},data:function(){return{}},methods:{toLivePlayer:function(){(0,i.toLivePlayer)([this.data.wx_room_id])}}};e.default=A},3096:function(t,e,a){var i=a("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.u-image[data-v-1bf07c9a]{position:relative;transition:opacity .5s ease-in-out}.u-image__image[data-v-1bf07c9a]{width:100%;height:100%}.u-image__loading[data-v-1bf07c9a], .u-image__error[data-v-1bf07c9a]{position:absolute;top:0;left:0;width:100%;height:100%;display:flex;flex-direction:row;align-items:center;justify-content:center;background-color:#f3f4f6;color:#909399;font-size:%?46?%}',""]),t.exports=e},"3f30":function(t,e,a){"use strict";a.r(e);var i=a("4219"),A=a.n(i);for(var n in i)["default"].indexOf(n)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(n);e["default"]=A.a},4219:function(t,e,a){"use strict";a("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("a9e3");var i={name:"u-image",props:{src:{type:String,default:""},mode:{type:String,default:"aspectFill"},width:{type:[String,Number],default:"100%"},height:{type:[String,Number],default:"auto"},shape:{type:String,default:"square"},borderRadius:{type:[String,Number],default:0},lazyLoad:{type:Boolean,default:!0},showMenuByLongpress:{type:Boolean,default:!0},loadingIcon:{type:String,default:"photo"},errorIcon:{type:String,default:"error-circle"},showLoading:{type:Boolean,default:!0},showError:{type:Boolean,default:!0},fade:{type:Boolean,default:!0},webp:{type:Boolean,default:!1},duration:{type:[String,Number],default:500},bgColor:{type:String,default:"#f3f4f6"}},data:function(){return{isError:!1,loading:!0,opacity:1,durationTime:this.duration,backgroundStyle:{}}},watch:{src:{immediate:!0,handler:function(t){t?this.isError=!1:(this.isError=!0,this.loading=!1)}}},computed:{wrapStyle:function(){var t={};return t.width=this.$u.addUnit(this.width),t.height=this.$u.addUnit(this.height),t.borderRadius="circle"==this.shape?"50%":this.$u.addUnit(this.borderRadius),t.overflow=this.borderRadius>0?"hidden":"visible",this.fade&&(t.opacity=this.opacity,t.transition="opacity ".concat(Number(this.durationTime)/1e3,"s ease-in-out")),t}},methods:{onClick:function(){this.$emit("click")},onErrorHandler:function(t){this.loading=!1,this.isError=!0,this.$emit("error",t)},onLoadHandler:function(){var t=this;if(this.loading=!1,this.isError=!1,this.$emit("load"),!this.fade)return this.removeBgColor();this.opacity=0,this.durationTime=0,setTimeout((function(){t.durationTime=t.duration,t.opacity=1,setTimeout((function(){t.removeBgColor()}),t.durationTime)}),50)},removeBgColor:function(){this.backgroundStyle={backgroundColor:"transparent"}}}};e.default=i},"53f3":function(t,e,a){"use strict";a("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i={data:function(){return{mescroll:null}},onPullDownRefresh:function(){this.mescroll&&this.mescroll.onPullDownRefresh()},onPageScroll:function(t){this.mescroll&&this.mescroll.onPageScroll(t)},onReachBottom:function(){this.mescroll&&this.mescroll.onReachBottom()},methods:{mescrollInit:function(t){console.log(t),this.mescroll=t,this.mescrollInitByRef()},mescrollInitByRef:function(){if(!this.mescroll||!this.mescroll.resetUpScroll){var t=this.$refs.mescrollRef;t&&(this.mescroll=t.mescroll)}},downCallback:function(){var t=this;this.mescroll.optUp.use?this.mescroll.resetUpScroll():setTimeout((function(){t.mescroll.endSuccess()}),500)},upCallback:function(){var t=this;setTimeout((function(){t.mescroll.endErr()}),500)}},mounted:function(){this.mescrollInitByRef()}},A=i;e.default=A},"586d":function(t,e,a){var i=a("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.live-item[data-v-d5ab4bfa]{width:100%;position:relative;background-color:#fff;border-radius:%?20?%;overflow:hidden}.live-item--row[data-v-d5ab4bfa]{border-radius:%?0?%;display:flex;padding:%?20?%}.live-item--row .live-item__status[data-v-d5ab4bfa]{top:%?40?%;left:%?40?%}.live-item__image[data-v-d5ab4bfa]{padding-top:80%;height:0;position:relative}.live-item__image-content[data-v-d5ab4bfa]{position:absolute;top:0;left:0;right:0;bottom:0}.live-item__content[data-v-d5ab4bfa]{padding:0 %?20?% %?14?%}.live-item__status[data-v-d5ab4bfa]{position:absolute;top:%?20?%;left:%?24?%;z-index:1;display:flex;align-items:center;background-color:#ff2c3c;color:#fff;padding:%?8?% %?20?%;border-radius:%?100?%}.live-item__status--not-started[data-v-d5ab4bfa]{background-color:#06aee4}.live-item__status--closed[data-v-d5ab4bfa]{background-color:#46b89b}.live-item__status--expired[data-v-d5ab4bfa]{background-color:#fa3534}.live-item__status .status__text[data-v-d5ab4bfa]{margin-left:%?4?%;font-size:%?22?%}.live-item__btn[data-v-d5ab4bfa]{width:%?300?%;border:1px solid currentColor;padding:%?10?% 0;border-radius:%?100?%;text-align:center;margin-top:%?20?%}.live-item__btn--start[data-v-d5ab4bfa]{color:#06aee4}.live-item__btn--ing[data-v-d5ab4bfa]{color:#ff2c3c}',""]),t.exports=e},"61e2":function(t,e,a){"use strict";a.r(e);var i=a("8503"),A=a("68e1");for(var n in A)["default"].indexOf(n)<0&&function(t){a.d(e,t,(function(){return A[t]}))}(n);a("7eb4f");var r=a("f0c5"),o=Object(r["a"])(A["default"],i["b"],i["c"],!1,null,"22d9203e",null,!1,i["a"],void 0);e["default"]=o.exports},"68e1":function(t,e,a){"use strict";a.r(e);var i=a("f466"),A=a.n(i);for(var n in i)["default"].indexOf(n)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(n);e["default"]=A.a},"6d31":function(t,e,a){var i=a("586d");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var A=a("4f06").default;A("7ac788ec",i,!0,{sourceMap:!1,shadowMode:!1})},"7eb4f":function(t,e,a){"use strict";var i=a("0498"),A=a.n(i);A.a},8503:function(t,e,a){"use strict";a.d(e,"b",(function(){return A})),a.d(e,"c",(function(){return n})),a.d(e,"a",(function(){return i}));var i={uIcon:a("6976").default},A=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{staticClass:"u-avatar",style:[t.wrapStyle],on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.click.apply(void 0,arguments)}}},[!t.uText&&t.avatar?a("v-uni-image",{staticClass:"u-avatar__img",style:[t.imgStyle],attrs:{src:t.avatar,mode:t.imgMode},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.loadError.apply(void 0,arguments)}}}):t.uText?a("v-uni-text",{staticClass:"u-line-1",style:{fontSize:"38rpx"}},[t._v(t._s(t.uText))]):t._t("default"),t.showSex?a("v-uni-view",{staticClass:"u-avatar__sex",class:["u-avatar__sex--"+t.sexIcon],style:[t.uSexStyle]},[a("u-icon",{attrs:{name:t.sexIcon,size:"20"}})],1):t._e(),t.showLevel?a("v-uni-view",{staticClass:"u-avatar__level",style:[t.uLevelStyle]},[a("u-icon",{attrs:{name:t.levelIcon,size:"20"}})],1):t._e()],2)},n=[]},"9e3f":function(t,e,a){"use strict";a("7a82");var i=a("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.getLiveLists=function(t){return A.default.get("live/lists",{params:t})},e.getShopLive=function(t){return A.default.get("live/shopLive",{params:t})};var A=i(a("2774"))},a0ad:function(t,e,a){"use strict";a.r(e);var i=a("e309"),A=a("0e29");for(var n in A)["default"].indexOf(n)<0&&function(t){a.d(e,t,(function(){return A[t]}))}(n);a("cc7f");var r=a("f0c5"),o=Object(r["a"])(A["default"],i["b"],i["c"],!1,null,"d5ab4bfa",null,!1,i["a"],void 0);e["default"]=o.exports},a14a:function(t,e,a){"use strict";a.d(e,"b",(function(){return A})),a.d(e,"c",(function(){return n})),a.d(e,"a",(function(){return i}));var i={liveItem:a("a0ad").default},A=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{staticClass:"live-room"},[a("mescroll-body",{ref:"mescrollRef",attrs:{up:t.upOption},on:{init:function(e){arguments[0]=e=t.$handleEvent(e),t.mescrollInit.apply(void 0,arguments)},down:function(e){arguments[0]=e=t.$handleEvent(e),t.downCallback.apply(void 0,arguments)},up:function(e){arguments[0]=e=t.$handleEvent(e),t.upCallback.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"p-20 flex flex-wrap",staticStyle:{margin:"0 -8rpx"}},[t._l(t.liveLists,(function(t,e){return[101==t.live_status?a("v-uni-view",{key:t.id,staticClass:"m-b-20 p-l-8 p-r-8",staticStyle:{width:"100%"}},[a("live-item",{attrs:{data:t}})],1):a("v-uni-view",{key:t.id,staticClass:"m-b-20 p-l-8 p-r-8",staticStyle:{width:"50%"}},[a("live-item",{attrs:{data:t}})],1)]}))],2)],1)],1)},n=[]},a21d:function(t,e,a){var i=a("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.u-avatar[data-v-22d9203e]{display:inline-flex;align-items:center;justify-content:center;font-size:%?28?%;color:#606266;border-radius:10px;position:relative}.u-avatar__img[data-v-22d9203e]{width:100%;height:100%}.u-avatar__sex[data-v-22d9203e]{position:absolute;width:%?32?%;color:#fff;height:%?32?%;display:flex;flex-direction:row;justify-content:center;align-items:center;border-radius:%?100?%;top:5%;z-index:1;right:-7%;border:1px #fff solid}.u-avatar__sex--man[data-v-22d9203e]{background-color:#ff2c3c}.u-avatar__sex--woman[data-v-22d9203e]{background-color:#fa3534}.u-avatar__sex--none[data-v-22d9203e]{background-color:#f90}.u-avatar__level[data-v-22d9203e]{position:absolute;width:%?32?%;color:#fff;height:%?32?%;display:flex;flex-direction:row;justify-content:center;align-items:center;border-radius:%?100?%;bottom:5%;z-index:1;right:-7%;border:1px #fff solid;background-color:#f90}',""]),t.exports=e},ad94:function(t,e,a){"use strict";a.r(e);var i=a("a14a"),A=a("0427");for(var n in A)["default"].indexOf(n)<0&&function(t){a.d(e,t,(function(){return A[t]}))}(n);var r=a("f0c5"),o=Object(r["a"])(A["default"],i["b"],i["c"],!1,null,"1a96ce73",null,!1,i["a"],void 0);e["default"]=o.exports},c529:function(t,e,a){"use strict";var i=a("1e2e"),A=a.n(i);A.a},cc7f:function(t,e,a){"use strict";var i=a("6d31"),A=a.n(i);A.a},e309:function(t,e,a){"use strict";a.d(e,"b",(function(){return A})),a.d(e,"c",(function(){return n})),a.d(e,"a",(function(){return i}));var i={uIcon:a("6976").default,uImage:a("f919").default,uAvatar:a("61e2").default},A=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{staticClass:"live-item",class:{"live-item--row":"row"==t.type},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toLivePlayer.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"live-item__status",class:{"live-item__status--not-started":102==t.data.live_status,"live-item__status--closed":103==t.data.live_status,"live-item__status--expired":107==t.data.live_status}},[101==t.data.live_status?[a("u-icon",{attrs:{name:"/static/images/live.gif",size:24}}),a("v-uni-text",{staticClass:"status__text"},[t._v(t._s(t.data.live_status_text))])]:103==t.data.live_status?[a("u-icon",{attrs:{name:"play-circle",size:28}}),a("v-uni-text",{staticClass:"status__text"},[t._v("回放")])]:102==t.data.live_status?[a("u-icon",{attrs:{name:"clock",size:28}}),a("v-uni-text",{staticClass:"status__text"},[t._v(t._s(t.data.start_time_tips))])]:[a("u-icon",{attrs:{name:"error-circle",size:28}}),a("v-uni-text",{staticClass:"status__text"},[t._v(t._s(t.data.live_status_text))])]],2),"column"==t.type?[a("v-uni-view",{staticClass:"live-item__image"},[a("v-uni-view",{staticClass:"live-item__image-content"},[a("u-image",{attrs:{src:t.data.feeds_img,width:"100%",height:"100%",mode:"aspectFill"}})],1)],1),a("v-uni-view",{staticClass:"live-item__content"},[a("v-uni-view",{staticClass:"shop-logo m-l-20",staticStyle:{"margin-top":"-32rpx"}},[a("u-avatar",{attrs:{src:t.data.shop.logo,size:64}})],1),a("v-uni-view",{staticClass:"shop-name bold"},[t._v(t._s(t.data.shop.name))]),a("v-uni-view",{staticClass:"line-2 xxs"},[t._v(t._s(t.data.name))])],1)]:t._e(),"row"==t.type?[a("u-image",{attrs:{src:t.data.feeds_img,width:"240rpx",height:"240rpx",mode:"aspectFill"}}),a("v-uni-view",{staticClass:"flex-1 m-l-20",staticStyle:{"min-width":"0"}},[a("v-uni-view",{staticClass:"line-2 xxl bold"},[t._v(t._s(t.data.name))]),a("v-uni-view",{staticClass:"m-t-10"},[t._v("主播："+t._s(t.data.anchor_name))]),102==t.data.live_status?a("v-uni-view",{staticClass:"live-item__btn live-item__btn--start"},[t._v("开播提醒")]):t._e(),101==t.data.live_status?a("v-uni-view",{staticClass:"live-item__btn live-item__btn--ing"},[t._v("立即观看")]):t._e()],1)]:t._e()],2)},n=[]},f466:function(t,e,a){"use strict";a("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("a9e3");var i="data:image/jpg;base64,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",A={name:"u-avatar",props:{bgColor:{type:String,default:"transparent"},src:{type:String,default:""},size:{type:[String,Number],default:"default"},mode:{type:String,default:"circle"},text:{type:String,default:""},imgMode:{type:String,default:"aspectFill"},index:{type:[String,Number],default:""},sexIcon:{type:String,default:"man"},levelIcon:{type:String,default:"level"},levelBgColor:{type:String,default:""},sexBgColor:{type:String,default:""},showSex:{type:Boolean,default:!1},showLevel:{type:Boolean,default:!1}},data:function(){return{error:!1,avatar:this.src?this.src:i}},watch:{src:function(t){t?(this.avatar=t,this.error=!1):(this.avatar=i,this.error=!0)}},computed:{wrapStyle:function(){var t={};return t.height="large"==this.size?"120rpx":"default"==this.size?"90rpx":"mini"==this.size?"70rpx":this.size+"rpx",t.width=t.height,t.flex="0 0 ".concat(t.height),t.backgroundColor=this.bgColor,t.borderRadius="circle"==this.mode?"500px":"5px",this.text&&(t.padding="0 6rpx"),t},imgStyle:function(){var t={};return t.borderRadius="circle"==this.mode?"500px":"5px",t},uText:function(){return String(this.text)[0]},uSexStyle:function(){var t={};return this.sexBgColor&&(t.backgroundColor=this.sexBgColor),t},uLevelStyle:function(){var t={};return this.levelBgColor&&(t.backgroundColor=this.levelBgColor),t}},methods:{loadError:function(){this.error=!0,this.avatar=i},click:function(){this.$emit("click",this.index)}}};e.default=A},f743:function(t,e,a){"use strict";a.d(e,"b",(function(){return A})),a.d(e,"c",(function(){return n})),a.d(e,"a",(function(){return i}));var i={uIcon:a("6976").default},A=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{staticClass:"u-image",style:[t.wrapStyle,t.backgroundStyle],on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onClick.apply(void 0,arguments)}}},[t.isError?t._e():a("v-uni-image",{staticClass:"u-image__image",style:{borderRadius:"circle"==t.shape?"50%":t.$u.addUnit(t.borderRadius)},attrs:{src:t.src,mode:t.mode,"lazy-load":t.lazyLoad},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.onErrorHandler.apply(void 0,arguments)},load:function(e){arguments[0]=e=t.$handleEvent(e),t.onLoadHandler.apply(void 0,arguments)}}}),t.showLoading&&t.loading?a("v-uni-view",{staticClass:"u-image__loading",style:{borderRadius:"circle"==t.shape?"50%":t.$u.addUnit(t.borderRadius),backgroundColor:this.bgColor}},[t.$slots.loading?t._t("loading"):a("u-icon",{attrs:{name:t.loadingIcon,width:t.width,height:t.height}})],2):t._e(),t.showError&&t.isError&&!t.loading?a("v-uni-view",{staticClass:"u-image__error",style:{borderRadius:"circle"==t.shape?"50%":t.$u.addUnit(t.borderRadius)}},[t.$slots.error?t._t("error"):a("u-icon",{attrs:{name:t.errorIcon,width:t.width,height:t.height}})],2):t._e()],1)},n=[]},f919:function(t,e,a){"use strict";a.r(e);var i=a("f743"),A=a("3f30");for(var n in A)["default"].indexOf(n)<0&&function(t){a.d(e,t,(function(){return A[t]}))}(n);a("c529");var r=a("f0c5"),o=Object(r["a"])(A["default"],i["b"],i["c"],!1,null,"1bf07c9a",null,!1,i["a"],void 0);e["default"]=o.exports},fe1a:function(t,e,a){"use strict";a("7a82");var i=a("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("99af");var A=i(a("f07e")),n=i(a("c964")),r=i(a("53f3")),o=a("9e3f"),s={mixins:[r.default],data:function(){return{upOption:{auto:!1,empty:{icon:"/static/images/goods_null.png",tip:"暂无数据～"}},liveLists:[]}},onShareAppMessage:function(){var t=this.appConfig.share;return{title:t.mnp_share_title,path:"/bundle_b/pages/live-room/live-room?invite_code="+this.inviteCode}},methods:{downCallback:function(){var t=this;return(0,n.default)((0,A.default)().mark((function e(){return(0,A.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.mescroll.resetUpScroll();case 1:case"end":return e.stop()}}),e)})))()},onReflesh:function(){this.liveLists=[],this.mescroll.resetUpScroll()},upCallback:function(t){var e=this,a=t.num,i=t.size;(0,o.getLiveLists)({page_no:a,page_size:i}).then((function(a){if(1==a.code){var i=a.data.list,A=i.length,n=!!a.data.more;1==t.num&&(e.liveLists=[]),e.liveLists=e.liveLists.concat(i),e.mescroll.endSuccess(A,n)}})).catch((function(t){e.mescroll.endErr()}))}}};e.default=s}}]);