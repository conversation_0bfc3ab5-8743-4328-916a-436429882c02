{layout name="layout2" /}
<link href="__PUBLIC__/static/lib/layui/layeditor/layedit.css" rel="stylesheet"/>
<script src="__PUBLIC__/static/lib/layui/layeditor/index.js"></script>
<script src="__PUBLIC__/static/lib/layui/layeditor/ace/ace.js"></script>
<style>
    .layui-form-item .layui-input-inline { width: 340px; }
    .tips{
        color: red;
    }
    .layui-laydate-content>.layui-laydate-list {
        padding-bottom: 0px;
        overflow: hidden;
    }
    .layui-laydate-content>.layui-laydate-list>li{
        width:50%
    }

    .merge-box .scrollbox .merge-list {
        padding-bottom: 5px;
    }
    .layui-form-item .layui-form-label { width: 95px; }
    .layui-form-item .layui-input-inline { width: 240px; }
    .layui-form-item .map-container{ width: 600px; height: 400px; margin-left: 100px; margin-top: 20px; }
    .layui-form-select dl {
        z-index: 1001;
    }
</style>


<div class="layui-card layui-form" style="box-shadow:none;">
    <div class="layui-card-body">
        <div class="layui-form-item">
        <input type="hidden" name="user_id" id="user_id"  value="0">
        <label class="layui-form-label">展会负责人</label>
        <div class="layui-inline">
            <span id="user_selected"></span>
        </div>
        <div class="layui-inline">
            <button class="layui-btn layui-btn-sm layui-bg-blue" id="show-user">选择用户</button>
        </div>
        </div>
        <div class="layui-form-item">
            <label for="title" class="layui-form-label"><span style="color:red;">*</span>展会标题：</label>
            <div class="layui-input-inline">
                <input type="text" name="title" id="title" lay-verType="tips" lay-verify="required" autocomplete="off" class="layui-input">
            </div>
        </div>


        <div class="layui-form-item">
            <label class="layui-form-label"><span class="tips">*</span>开始时间</label>
            <div class="layui-input-inline">
                <input type="text" name="start_time" lay-verify="required" lay-verType="tips"  placeholder="请选择开始时间" autocomplete="off" class="layui-input time">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"><span class="tips">*</span>结束时间</label>
            <div class="layui-input-inline">
                <input type="text" name="end_time" lay-verify="required" lay-verType="tips"  placeholder="请选择结束时间" autocomplete="off" class="layui-input time">
            </div>
        </div>
        <div class="layui-form-item">
            <label for="sort" class="layui-form-label">展会排序：</label>
            <div class="layui-input-inline">
                <input type="text" name="sort" id="sort" autocomplete="off" class="layui-input">
            </div>
        </div>

        <div class="layui-form-item" style="margin-bottom: 0px">
            <label class="layui-form-label">展会图片：</label>
            <div class="layui-input-block" id="qualifications_images">
                <div class="like-upload-image">
                    <div class="upload-image-elem"><a class="add-upload-image add-other-qualifications" id="images"> + 添加图片</a></div>
                </div>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"></label>
            <span style="color: #a3a3a3;font-size: 9px">最多上传9张</span>
        </div>

        <div class="layui-form-item">
            <lable class="layui-form-label">观众数量:</lable>
            <div class="layui-inline">
                <input type="text" name="person_nums" lay-verify="required" class="layui-input" pattern="^[1-9]\d*$" title="请输入正整数，不能为0" placeholder="请输入正整数，不能为0" oninput="this.value=this.value.replace(/[^1-9\d]/g,'');">人
            </div>
        </div>
        <div class="layui-form-item">
            <lable class="layui-form-label">参加人数:</lable>
            <div class="layui-inline">
                <input type="text" name="can_nums"  class="layui-input" pattern="^[1-9]\d*$" title="请输入正整数，不能为0" placeholder="请输入正整数，不能为0" oninput="this.value=this.value.replace(/[^1-9\d]/g,'');">人
            </div>
        </div>
        <div class="layui-form-item">
            <lable class="layui-form-label">展商数量:</lable>
            <div class="layui-inline">
                <input type="text"  name="shop_nums"  class="layui-input">
            </div>
        </div>

        <div class="layui-form-item">
            <lable class="layui-form-label">面积:</lable>
            <div class="layui-inline">
                <input type="text"  name="areas"  class="layui-input">㎡
            </div>
        </div>
        <div class="layui-form-item">
            <lable class="layui-form-label">场地:</lable>
            <div class="layui-inline">
                <input type="text"  name="site"  class="layui-input" lay-verify="required" >
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">展会状态：</label>
            <div class="layui-input-inline">
                <input type="radio" name="is_show" value="1" title="显示" checked>
                <input type="radio" name="is_show" value="0" title="隐藏" >
            </div>
        </div>
        <div class="layui-form-item">
            <label for="content" class="layui-form-label">展会内容：</label>
            <div class="layui-input-block">
                <textarea name="content" id="content" lay-verify="content"></textarea>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">展会地址：</label>
            <div class="layui-input-block">
                <div class="layui-input-inline" style="width:120px;">
                    <select name="province_id" id="province" lay-filter="province"></select>
                </div>
                <div class="layui-input-inline" style="width:120px;">
                    <select name="city_id" id="city" lay-filter="city"></select>
                </div>
                <div class="layui-input-inline" style="width:120px;">
                    <select name="district_id" id="district"></select>
                </div>
                <div class="layui-input-inline">
                    <input type="text" name="address" id="address" autocomplete="off" class="layui-input">
                </div>
                <div class="layui-input-inline">
                    <button class="layui-btn layui-btn-normal" id="search_map">搜索地图</button>
                </div>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">地图定位：</label>
            <div class="layui-input-block">
                <div class="layui-inline" >
                    <div class="layui-input-inline" style="width:120px;margin-right:5px;">
                        <input type="text" name="longitude" autocomplete="off" class="layui-input">
                    </div>
                    <div class="layui-form-mid layui-word-aux">经度</div>
                </div>
                <div class="layui-inline" style="margin-right:0;">
                    <div class="layui-input-inline" style="width:120px;margin-right:5px;">
                        <input type="text" name="latitude" autocomplete="off" class="layui-input">
                    </div>
                    <div class="layui-form-mid layui-word-aux">纬度</div>
                </div>
            </div>
        </div>
        <div class="layui-form-item ">
            <label class="layui-form-label"></label>
            <div class="layui-input-block" style="margin-left:10px;">
                <div id="map-container" style="width: 700px;height: 400px;margin-left: 115px;"></div>
            </div>
        </div>
        <div class="layui-form-item layui-hide">
            <input type="button" lay-submit lay-filter="addSubmit" id="addSubmit" value="确认">
        </div>
    </div>
</div>
<script src="__PUBLIC__/static/common/js/area.js"></script>
<script src="https://map.qq.com/api/gljs?v=1.exp&key={$tx_map_key}&libraries=service"></script>
<script>
    layui.config({
        base: "/static/lib/"
    }).extend({
        likeedit: "likeedit/likeedit",
        likeArea: "likeArea/likeArea",
        txMap: "likeMap/txMap",
        customTxMap:'likeMap/customTxMap',
    }).use(["layEditor", "form", "likeArea", "txMap",'customTxMap'], function() {
        var form = layui.form;
        var layEditor = layui.layEditor;
        var likeArea = layui.likeArea;
        var laydate = layui.laydate;
        var txMap = layui.txMap;
        var customTxMap = layui.customTxMap;
        layEditor.set({
            uploadImage: {
                url: '{:url("file/lists")}?type=10'
            },
        })
        var ieditor = layEditor.build('content')
        form.verify({
            content: function (value) {
                return layEditor.sync(ieditor);
            }
        });
        var $ = layui.$
            , laydate = layui.laydate;
        lay('.time').each(function () {
            laydate.render({
                elem: this,
                trigger: 'click',
                type: 'datetime'
            });
        });
        like.delUpload();
        $(document).on("click", ".add-other-qualifications", function () {
            like.imageUpload({
                limit: 9,
                field: "images[]",
                that: $(this)
            });
        })
        $(document).on("click", ".add-upload-image2", function () {
            like.imageUpload({
                limit: 1,
                field: "wechat_image",
                that: $(this)
            });
        })
        likeArea.init(
            "province", "city", "district", "province_id", "city_id", "district_id",
            "", "", ""
        );
        likeArea.init(
            "refund_province", "refund_city", "refund_district", "refund_province_id", "refund_city_id", "refund_district_id",
            "", "", ""
        );
        $('#show-user').click(function() {
            layer.open({
                type: 2
                ,title: "选择用户"
                ,content: "{:url('community.CommunityExhibition/userLists')}"
                ,area: ["90%", "90%"]
                ,btn: ["确定", "取消"]
                ,yes: function(index, layero){
                    var iframeWindow = window["layui-layer-iframe" + index];
                    let user_selected = iframeWindow.user_selected();
                    $('#user_selected').html(user_selected.nickname + '(' + user_selected.sn + ')');
                    $('#user_id').val(user_selected.id);
                    layer.close(index);
                }
            });
            return false;
        });
        // txMap.init("", "");
        customTxMap.initMap('map-container');
        //搜索地图
        $("#search_map").click(function () {
            var province = $("#province");
            var city = $("#city");
            var district = $("#district");
            var address = $("input[name='address']").val();
            if (!province.val()) {
                layer.msg("请选择省份");
                return;
            }
            if (!city.val()) {
                layer.msg("请选择市");
                return;
            }
            if (!district.val()) {
                layer.msg("请选择镇/区");
                return;
            }
            var intactAddress = province.find("option:selected").text() + city.find("option:selected").text() + district.find("option:selected").text() + address;
            customTxMap.searchMap(intactAddress);
        })
    })
</script>