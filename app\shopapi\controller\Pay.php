<?php

namespace app\shopapi\controller;

use app\api\logic\PayLogic;
use app\common\basics\ShopApi;
use app\common\enum\OrderEnum;
use app\common\enum\PayEnum;
use app\common\model\AdOrder;
use app\common\model\agent\AgentMerchantfees;
use app\common\model\agent\AgentRelationship;
use app\common\model\Client_;
use app\common\model\jcai\JcaiOrder;
use app\common\model\order\OrderTrade;
use app\common\model\order\Order;
use app\common\model\order\OrderLog;
use app\common\model\RechargeOrder;
use app\common\model\shop\ShopDeposit;
use app\common\server\AliPayServer;
use app\common\server\DouGong\BaseAsync;
use app\common\server\DouGong\pay\PayZhengSao;
use app\common\server\JsonServer;
use app\common\server\WeChatPayServer;
use app\common\server\WeChatServer;
use app\common\model\integral\IntegralOrder;
use think\facade\Db;
use think\facade\Log;

/**
 * 商家端支付控制器
 * Class Pay
 * @package app\shopapi\controller
 */
class Pay extends ShopApi
{
    public $like_not_need_login = ['unifiedpay', 'notifyMnp', 'notifyOa', 'notifyApp', 'aliNotify', 'hfdgPayWechatNotify', 'hfdgPayAlipayNotify'];

    /**
     * @notes 统一支付入口
     * @return \think\response\Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function unifiedpay()
    {
        $post = $this->request->post();
        if(!isset($post['pay_way'])) {
            return JsonServer::error('请选择支付方式');
        }
    
        $pay_way = $post['pay_way'];
        
        // 根据来源类型处理不同的订单
        switch ($post['from']) {
            case 'order':
                // 更新支付方式
                $order = Order::findOrEmpty($post['order_id']);
                Order::where('id', $post['order_id'])->update(['pay_way' => $pay_way]);
                break;
            case 'trade':
                $order = OrderTrade::findOrEmpty($post['order_id']);
                // 更新支付方式
                Order::where('trade_id', $post['order_id'])->update(['pay_way' => $pay_way]);
                break;
            case 'recharge':
                $order = RechargeOrder::findOrEmpty($post['order_id']);
                // 更新支付方式
                RechargeOrder::where('id', $post['order_id'])->update(['pay_way' => $pay_way]);
                break;
            case 'AdOrder':
                $order = AdOrder::findOrEmpty($post['order_id']);
                $this->client = 5;
                $order['order_amount'] = $order['ad_price'];
                break;
            case 'bondcharge':
                $order = ShopDeposit::findOrEmpty($post['order_id']);
                $this->client = 5;
                $order['order_amount'] = $order['deposit_amount'];
                break;
            case 'rejcharge':
                $order = JcaiOrder::findOrEmpty($post['order_id']);
                // 更新支付方式
                JcaiOrder::where('id', $post['order_id'])->update(['pay_way' => $pay_way]);
                break;
            case 'agent':
                $order = AgentMerchantfees::findOrEmpty($post['order_id']);
                $order['order_amount'] = $order['amount'];
                break;
            case 'integral':
                $order = IntegralOrder::findOrEmpty($post['order_id']);
                // 更新支付方式
                IntegralOrder::where('id', $post['order_id'])->update(['pay_way' => $pay_way]);
                break;
        }

        // order,trade方式金额为0直接走余额支付
        if (isset($order) && $order['order_amount'] == 0) {
            return PayLogic::balancePay($post['order_id'], $post['from']);
        }

        // 根据支付方式处理
        switch ($pay_way) {
            case PayEnum::BALANCE_PAY: // 余额支付
                $result = PayLogic::balancePay($post['order_id'], $post['from']);
                break;
            case PayEnum::WECHAT_PAY: // 微信支付
                $result = PayLogic::wechatPay($post['order_id'], $post['from'], $this->client);
                break;
            case PayEnum::ALI_PAY: // 支付宝支付
                $result = PayLogic::aliPay($post['order_id'], $post['from'], $this->client);
                
                if (app()->isDebug()) {
                    Log::write($result, 'unifiedpay');
                }
                
                $data = [
                    'code' => 10001,
                    'msg' => '发起成功',
                    'data' => $result,
                    'show' => 0,
                ];
                return json($data);
            // 汇付斗拱 微信 支付宝
            case PayEnum::HFDG_WECHAT:
            case PayEnum::HFDG_ALIPAY:
            // 线下支付
            case PayEnum::OFFLINE_PAY:
                $data = [
                    'code' => 30001,
                    'msg' => '下单成功',
                    'data' => [],
                    'show' => 0,
                ];
                return json($data);
        }

        return $result;
    }

    /**
     * @notes 小程序回调
     * @throws \EasyWeChat\Kernel\Exceptions\Exception
     */
    public function notifyMnp()
    {
        $config = WeChatServer::getPayConfig(Client_::mnp);
        return WeChatPayServer::notify($config);
    }

    /**
     * @notes 公众号回调
     * @throws \EasyWeChat\Kernel\Exceptions\Exception
     */
    public function notifyOa()
    {
        $config = WeChatServer::getPayConfig(Client_::oa);
        return WeChatPayServer::notify($config);
    }

    /**
     * @notes APP回调
     * @throws \EasyWeChat\Kernel\Exceptions\Exception
     */
    public function notifyApp()
    {
        $config = WeChatServer::getPayConfig(Client_::ios);
        return WeChatPayServer::notify($config);
    }

    /**
     * @notes 支付宝回调
     * @return bool
     */
    public function aliNotify()
    {
        $data = $this->request->post();
        $result = (new AliPayServer())->verifyNotify($data);
        if (true === $result) {
            echo 'success';
        } else {
            echo 'fail';
        }
    }
    
    /**
     * @notes 汇付斗拱微信支付回调
     * @return string
     */
    public function hfdgPayWechatNotify()
    {
        $data = input();
        
        $async = new BaseAsync($data);
    
        $async->checkAsync();
        
        if ($async->getCheckSuccess()) {
            PayZhengSao::asyncSuccessDeal($data);
        }
        
        return $async->getCheckSuccess() ? 'success' : 'failed';
    }
    
    /**
     * @notes 汇付斗拱支付宝回调
     * @return string
     */
    public function hfdgPayAlipayNotify()
    {
        $data = input();
    
        $async = new BaseAsync($data);
    
        $async->checkAsync();
    
        if ($async->getCheckSuccess()) {
            PayZhengSao::asyncSuccessDeal($data);
        }
    
        return $async->getCheckSuccess() ? 'success' : 'failed';
    }
}
