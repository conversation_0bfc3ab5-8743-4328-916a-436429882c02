{"ActivityArea": {"description": "Auto imported from components/activity-area.vue"}, "AdItem": {"description": "Auto imported from components/ad-item.vue"}, "AddressAdd": {"description": "Auto imported from components/address-add.vue"}, "AddressList": {"description": "Auto imported from components/address-list.vue"}, "AfterSalesList": {"description": "Auto imported from components/after-sales-list.vue"}, "CommentList": {"description": "Auto imported from components/comment-list.vue"}, "CountDown": {"description": "Auto imported from components/count-down.vue"}, "CouponsList": {"description": "Auto imported from components/coupons-list.vue"}, "DeliverSearch": {"description": "Auto imported from components/deliver-search.vue"}, "EvaluationList": {"description": "Auto imported from components/evaluation-list.vue"}, "GoodsList": {"description": "Auto imported from components/goods-list.vue"}, "HomeSeckill": {"description": "Auto imported from components/home-seckill.vue"}, "InputExpress": {"description": "Auto imported from components/input-Express.vue"}, "NullData": {"description": "Auto imported from components/null-data.vue"}, "NumberBox": {"description": "Auto imported from components/number-box.vue"}, "OrderList": {"description": "Auto imported from components/order-list.vue"}, "PriceFormate": {"description": "Auto imported from components/price-formate.vue"}, "ShopItem": {"description": "Auto imported from components/shop-item.vue"}, "Upload": {"description": "Auto imported from components/upload.vue"}, "LayoutAslideNav": {"description": "Auto imported from components/layout/aslide-nav.vue"}, "LayoutCategory": {"description": "Auto imported from components/layout/category.vue"}, "LayoutFloatNav": {"description": "Auto imported from components/layout/float-nav.vue"}, "LayoutFooter": {"description": "Auto imported from components/layout/footer.vue"}, "LayoutHeader": {"description": "Auto imported from components/layout/header.vue"}, "LayoutMainNav": {"description": "Auto imported from components/layout/main-nav.vue"}}