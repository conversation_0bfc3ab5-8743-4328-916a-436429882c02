<?php


namespace app\admin\logic\community;


use app\common\basics\Logic;
use app\common\enum\CommunityLikeEnum;
use app\common\model\community\CommunityExhibition;
use app\common\model\community\CommunityComment;
use app\common\model\community\CommunityExhibitionImage;
use app\common\model\community\CommunityLike;
use app\common\model\community\CommunityTopic;
use app\common\logic\CommunityExhibitionLogic as CommonArticleLogic;
use app\common\server\UrlServer;
use think\Exception;
use think\facade\Db;


/**
 * 发现逻辑
 * Class CommunityExhibitionLogic
 * @package app\admin\logic\community
 */
class CommunityExhibitionformLogic extends Logic
{

    /**
     * @notes 文章列表
     * @param $get
     * @return array
     * <AUTHOR>
     * @date 2022/5/10 11:07
     */
    public static function lists($get)
    {
        $where = [
            ['a.del', '=', 0]
        ];

        if (!empty($get['keyword'])) {
            $where[] = ['u.sn|u.nickname|u.mobile', 'like', '%' . $get['keyword'] . '%'];
        }

        if (!empty($get['content'])) {
            $where[] = ['a.content', 'like', '%' . $get['content'] . '%'];
        }

        if (isset($get['status']) && $get['status'] != '') {
            $where[] = ['a.status', '=', $get['status']];
        }
        if (isset($get['start_time']) && $get['start_time'] != '') {
            $where[] = ['a.start_time', '>=', strtotime($get['start_time'])];
        }

        if (isset($get['end_time']) && $get['end_time'] != '') {
            $where[] = ['a.end_time', '<=', strtotime($get['end_time'])];
        }

        $model = new CommunityExhibition();
        $lists = $model->with(['images'])->alias('a')
            ->field('a.*,u.nickname,u.avatar,u.sn')
            ->join('user u', 'u.id = a.user_id')
            ->where($where)
            ->order(['id' => 'desc'])
            ->append(['status_desc'])
            ->paginate([
                'page' => $get['page'],
                'list_rows' => $get['limit'],
                'var_page' => 'page'
            ])
            ->toArray();

        foreach ($lists['data'] as &$item) {
            $item['avatar'] = !empty($item['avatar']) ? UrlServer::getFileUrl($item['avatar']) : '';
            $item['end_time'] = $item['end_time']?date('Y-m-d H:i:s', $item['end_time']):'';
            $item['start_time'] = $item['start_time']?date('Y-m-d H:i:s', $item['start_time']):'';

        }

        return ['count' => $lists['total'], 'lists' => $lists['data']];
    }

    public static function addExhibitionImage($image, $article_id)
    {
        if (!empty($image)) {
            $images = [];
            foreach ($image as $item) {
                $images[] = [
                    'article_id' => $article_id,
                    'image' => $item,
                ];
            }
            (new CommunityExhibitionImage())->saveAll($images);
        }
    }
    /**
     * @notes 文章详情
     * @param $id
     * @return array
     * <AUTHOR>
     * @date 2022/5/10 16:53
     */
    public static function detail($id)
    {
        $detail = CommunityExhibition::with(['images'])
            ->findOrEmpty($id);
        $detail['start_time']=date('Y-m-d H:i:s',$detail['start_time']);
        $detail['end_time']=date('Y-m-d H:i:s',$detail['end_time']);
        return $detail->toArray();
    }


    /**
     * @notes 删除文章
     * @param $id
     * @return bool
     * <AUTHOR>
     * @date 2022/5/10 16:34
     */
    public static function del($id)
    {
        Db::startTrans();
        try {
            $article = CommunityExhibition::find($id);
            $article->del = 1;
            $article->update_time = time();
            $article->save();

            if (!empty($article['topic_id'])) {
                CommunityTopic::decArticleNum($article['topic_id']);
            }

            Db::commit();
            return true;

        } catch (Exception $e) {
            Db::rollback();
            self::$error = $e->getMessage();
            return false;
        }
    }


}