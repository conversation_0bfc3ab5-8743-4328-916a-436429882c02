{layout name="layout1" /}

<div class="wrapper">
    <div class="layui-card">
        <!-- 操作提示 -->
        <div class="layui-card-body">
            <div class="layui-collapse" style="border:1px dashed #c4c4c4">
                <div class="layui-colla-item">
                    <h2 class="layui-colla-title like-layui-colla-title">操作提示</h2>
                    <div class="layui-colla-content layui-show">
                        <p>*移动端图标配置用于商家移动端应用的图标显示和跳转。</p>
                        <p>*可以为每个图标设置名称、图片、跳转路径和关联权限。</p>
                        <p>*图标建议尺寸为64x64像素，支持jpg、jpeg、png格式。</p>
                        <p>*排序值越小越靠前显示，禁用状态的图标不会在移动端显示。</p>
                        <p>*关联权限用于控制商家是否有权限访问该功能模块。</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主体区域 -->
        <div class="layui-card-body">
            <!-- 搜索区域 -->
            <div class="layui-form layui-card-header layuiadmin-card-header-auto">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">图标名称</label>
                        <div class="layui-input-inline">
                            <input type="text" name="icon_name" placeholder="请输入图标名称" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">状态</label>
                        <div class="layui-input-inline">
                            <select name="status">
                                <option value="">全部</option>
                                <option value="1">启用</option>
                                <option value="0">禁用</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn {$view_theme_color}" lay-submit lay-filter="search">搜索</button>
                        <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                    </div>
                </div>
            </div>

            <!-- 操作按钮 -->
            <div class="layui-form layui-card-header layuiadmin-card-header-auto">
                <div class="layui-form-item">
                    <div class="layui-btn-container">
                        <button class="layui-btn {$view_theme_color}" id="add">
                            <i class="layui-icon layui-icon-add-1"></i>添加图标
                        </button>
                    </div>
                </div>
            </div>

            <!-- 数据表格 -->
            <table class="layui-hide" id="like-table-lists" lay-filter="like-table-lists"></table>

            <!-- 操作列模板 -->
            <script type="text/html" id="table-operation">
                <a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="edit">
                    <i class="layui-icon layui-icon-edit"></i>编辑
                </a>
                <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">
                    <i class="layui-icon layui-icon-delete"></i>删除
                </a>
            </script>

            <!-- 状态开关模板 -->
            <script type="text/html" id="statusTpl">
                <input type="checkbox" lay-filter="switch-status" data-id="{{d.id}}" lay-skin="switch"
                       lay-text="启用|禁用" {{# if(d.status == 1){ }} checked {{# } }}/>
            </script>

            <!-- 图标显示模板 -->
            <script type="text/html" id="table-image">
                {{# if(d.icon_url){ }}
                <img src="{{d.icon_url}}" style="width: 32px; height: 32px; border-radius: 4px;">
                {{# } else { }}
                <span style="color: #999;">无图标</span>
                {{# } }}
            </script>

            <div class="page-loading">
                <div class="ball-loader sm">
                    <span></span>
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
layui.config({
    version: "{$front_version}",
    base: '/static/lib/'
}).use(['form', 'table'], function () {
    var form = layui.form,
        table = layui.table;

    // 渲染表格
    var tableIns = table.render({
        elem: '#like-table-lists',
        url: '{:url("shop.MobileIcon/lists")}',
        cols: [[
            {field: 'id', width: 60, title: 'ID', sort: true},
            {field: 'icon_url', width: 80, title: '图标', templet: '#table-image', align: 'center'},
            {field: 'icon_name', width: 120, title: '图标名称', edit: 'text'},
            {field: 'icon_path', width: 180, title: '跳转路径', edit: 'text'},
            {field: 'auth_name', width: 120, title: '关联权限'},
            {field: 'merchant_types_text', width: 150, title: '商家类型'},
            {field: 'sort_order', width: 80, title: '排序', sort: true, edit: 'text'},
            {field: 'status', width: 100, title: '状态', templet: '#statusTpl', align: 'center'},
            {field: 'created_at', width: 150, title: '创建时间', templet: function(d) {
                if (d.created_at) {
                    return layui.util.toDateString(d.created_at * 1000, 'yyyy-MM-dd HH:mm');
                }
                return '-';
            }},
            {title: '操作', width: 160, toolbar: '#table-operation', align: 'center'}
        ]],
        page: true,
        limit: 15,
        limits: [15, 30, 50, 100],
        text: {none: '暂无相关数据'},
        response: {
            statusCode: 1
        },
        parseData: function (res) {
            return {
                "code": res.code,
                "msg": res.msg,
                "count": res.data.count,
                "data": res.data.lists
            };
        }
    });

    // 搜索
    form.on('submit(search)', function (data) {
        tableIns.reload({
            where: data.field,
            page: {curr: 1}
        });
        return false;
    });

    // 状态切换
    form.on('switch(switch-status)', function (obj) {
        var id = obj.elem.attributes['data-id'].nodeValue;
        var status = obj.elem.checked ? 0 : 1; // 注意这里的逻辑

        like.ajax({
            url: '{:url("shop.MobileIcon/status")}',
            data: {id: id, disable: status},
            type: "post",
            success: function (res) {
                if (res.code == 1) {
                    layui.layer.msg(res.msg, {
                        offset: '15px',
                        icon: 1,
                        time: 1000
                    });
                } else {
                    // 如果失败，恢复开关状态
                    obj.elem.checked = !obj.elem.checked;
                    form.render('checkbox');
                    layui.layer.msg(res.msg || '操作失败', {icon: 2});
                }
            }
        });
    });

    // 监听单元格编辑
    table.on('edit(like-table-lists)', function(obj){
        var data = obj.data;
        var field = obj.field;
        var value = obj.value;

        // 处理不同字段的编辑
        if(field === 'sort_order') {
            // 验证排序值是否为数字
            if(isNaN(value) || value === '') {
                layer.tips('请输入数字', obj.td, {tips: [1, '#FF5722']});
                // 恢复原值
                obj.update({
                    sort_order: data.sort_order
                });
                return false;
            }

            // 发送Ajax请求更新排序
            like.ajax({
                url: '{:url("shop.MobileIcon/updateSort")}',
                data: {
                    id: data.id,
                    sort_order: parseInt(value)
                },
                type: "post",
                success: function(res) {
                    if(res.code == 1) {
                        layui.layer.msg('排序更新成功', {
                            offset: '15px',
                            icon: 1,
                            time: 1000
                        });
                        // 更新本地数据
                        obj.update({
                            sort_order: parseInt(value)
                        });
                    } else {
                        layui.layer.msg(res.msg || '更新失败', {icon: 2});
                        // 恢复原值
                        obj.update({
                            sort_order: data.sort_order
                        });
                    }
                },
                error: function() {
                    layui.layer.msg('网络错误，请重试', {icon: 2});
                    // 恢复原值
                    obj.update({
                        sort_order: data.sort_order
                    });
                }
            });
        } else if(field === 'icon_name') {
            // 验证图标名称
            if(value.trim() === '') {
                layer.tips('图标名称不能为空', obj.td, {tips: [1, '#FF5722']});
                // 恢复原值
                obj.update({
                    icon_name: data.icon_name
                });
                return false;
            }

            // 发送Ajax请求更新图标名称
            like.ajax({
                url: '{:url("shop.MobileIcon/updateField")}',
                data: {
                    id: data.id,
                    field: 'icon_name',
                    value: value.trim()
                },
                type: "post",
                success: function(res) {
                    if(res.code == 1) {
                        layui.layer.msg('图标名称更新成功', {
                            offset: '15px',
                            icon: 1,
                            time: 1000
                        });
                        // 更新本地数据
                        obj.update({
                            icon_name: value.trim()
                        });
                    } else {
                        layui.layer.msg(res.msg || '更新失败', {icon: 2});
                        // 恢复原值
                        obj.update({
                            icon_name: data.icon_name
                        });
                    }
                },
                error: function() {
                    layui.layer.msg('网络错误，请重试', {icon: 2});
                    // 恢复原值
                    obj.update({
                        icon_name: data.icon_name
                    });
                }
            });
        } else if(field === 'icon_path') {
            // 验证跳转路径
            if(value.trim() === '') {
                layer.tips('跳转路径不能为空', obj.td, {tips: [1, '#FF5722']});
                // 恢复原值
                obj.update({
                    icon_path: data.icon_path
                });
                return false;
            }

            // 发送Ajax请求更新跳转路径
            like.ajax({
                url: '{:url("shop.MobileIcon/updateField")}',
                data: {
                    id: data.id,
                    field: 'icon_path',
                    value: value.trim()
                },
                type: "post",
                success: function(res) {
                    if(res.code == 1) {
                        layui.layer.msg('跳转路径更新成功', {
                            offset: '15px',
                            icon: 1,
                            time: 1000
                        });
                        // 更新本地数据
                        obj.update({
                            icon_path: value.trim()
                        });
                    } else {
                        layui.layer.msg(res.msg || '更新失败', {icon: 2});
                        // 恢复原值
                        obj.update({
                            icon_path: data.icon_path
                        });
                    }
                },
                error: function() {
                    layui.layer.msg('网络错误，请重试', {icon: 2});
                    // 恢复原值
                    obj.update({
                        icon_path: data.icon_path
                    });
                }
            });
        }
    });

    // 工具栏事件
    table.on('tool(like-table-lists)', function (obj) {
        var data = obj.data;

        if (obj.event === 'edit') {
            layer.open({
                type: 2,
                title: '编辑图标',
                content: '{:url("shop.MobileIcon/edit")}?id=' + data.id,
                area: ['90%', '90%'],
                btn: ['确定', '取消'],
                yes: function (index, layero) {
                    var iframeWindow = window['layui-layer-iframe' + index],
                        submit = layero.find('iframe').contents().find('#submit');

                    iframeWindow.layui.form.on('submit(submit)', function (formData) {
                        like.ajax({
                            url: '{:url("shop.MobileIcon/edit")}',
                            data: formData.field,
                            type: "post",
                            success: function (res) {
                                if (res.code == 1) {
                                    layer.close(index);
                                    layui.layer.msg(res.msg, {
                                        offset: '15px',
                                        icon: 1,
                                        time: 1000
                                    }, function () {
                                        tableIns.reload();
                                    });
                                } else {
                                    layui.layer.msg(res.msg || '操作失败', {icon: 2});
                                }
                            }
                        });
                    });
                    submit.trigger('click');
                }
            });
        } else if (obj.event === 'del') {
            layer.confirm('确定删除此图标配置？', function (index) {
                like.ajax({
                    url: '{:url("shop.MobileIcon/del")}',
                    data: {ids: [data.id]},
                    type: "post",
                    success: function (res) {
                        if (res.code == 1) {
                            layer.close(index);
                            layui.layer.msg(res.msg, {
                                offset: '15px',
                                icon: 1,
                                time: 1000
                            }, function () {
                                tableIns.reload();
                            });
                        } else {
                            layui.layer.msg(res.msg || '删除失败', {icon: 2});
                        }
                    }
                });
            });
        }
    });

    // 添加按钮
    $('#add').click(function () {
        layer.open({
            type: 2,
            title: '添加图标',
            content: '{:url("shop.MobileIcon/add")}',
            area: ['90%', '90%'],
            btn: ['确定', '取消'],
            yes: function (index, layero) {
                var iframeWindow = window['layui-layer-iframe' + index],
                    submit = layero.find('iframe').contents().find('#submit');

                iframeWindow.layui.form.on('submit(submit)', function (formData) {
                    like.ajax({
                        url: '{:url("shop.MobileIcon/add")}',
                        data: formData.field,
                        type: "post",
                        success: function (res) {
                            if (res.code == 1) {
                                layer.close(index);
                                layui.layer.msg(res.msg, {
                                    offset: '15px',
                                    icon: 1,
                                    time: 1000
                                }, function () {
                                    tableIns.reload();
                                });
                            } else {
                                layui.layer.msg(res.msg || '添加失败', {icon: 2});
                            }
                        }
                    });
                });
                submit.trigger('click');
            }
        });
    });
});
</script>
