<?php
namespace app\api\logic;

use app\common\basics\Logic;
use Exception;
use think\facade\Log;

/**
 * 简单PDF编辑器
 * 使用简单的方法处理PDF文件
 * Class SimplePdfEditor
 * @package app\api\logic
 */
class SimplePdfEditor extends Logic
{
    /**
     * @var string PDF文件路径
     */
    protected $pdfPath;

    /**
     * @var string 签名图片路径
     */
    protected $signaturePath;

    /**
     * @var string 输出PDF路径
     */
    protected $outputPath;

    /**
     * @var array 签名位置坐标
     */
    protected $signaturePosition = [
        'x' => 400,  // 默认X坐标
        'y' => 680,  // 默认Y坐标
        'width' => 80, // 默认宽度
        'height' => 40 // 默认高度
    ];

    /**
     * @var array 日期位置坐标
     */
    protected $datePosition = [
        'x' => 400,  // 默认X坐标
        'y' => 720,  // 默认Y坐标
    ];

    /**
     * 构造函数
     * @param string $pdfPath PDF文件路径
     */
    public function __construct($pdfPath = '')
    {
        if (!empty($pdfPath)) {
            $this->setPdfPath($pdfPath);
        }
    }

    /**
     * 设置PDF文件路径
     * @param string $pdfPath
     * @return $this
     * @throws Exception
     */
    public function setPdfPath($pdfPath)
    {
        if (!file_exists($pdfPath)) {
            throw new Exception('PDF文件不存在: ' . $pdfPath);
        }
        $this->pdfPath = $pdfPath;
        return $this;
    }

    /**
     * 设置签名图片路径
     * @param string $signaturePath
     * @return $this
     * @throws Exception
     */
    public function setSignaturePath($signaturePath)
    {
        if (!file_exists($signaturePath)) {
            throw new Exception('签名图片不存在: ' . $signaturePath);
        }
        $this->signaturePath = $signaturePath;
        return $this;
    }

    /**
     * 设置输出PDF路径
     * @param string $outputPath
     * @return $this
     */
    public function setOutputPath($outputPath)
    {
        $this->outputPath = $outputPath;
        return $this;
    }

    /**
     * 设置签名位置
     * @param float $x X坐标
     * @param float $y Y坐标
     * @param float $width 宽度
     * @param float $height 高度
     * @return $this
     */
    public function setSignaturePosition($x, $y, $width = null, $height = null)
    {
        $this->signaturePosition['x'] = $x;
        $this->signaturePosition['y'] = $y;
        if ($width !== null) {
            $this->signaturePosition['width'] = $width;
        }
        if ($height !== null) {
            $this->signaturePosition['height'] = $height;
        }
        return $this;
    }

    /**
     * 设置日期位置
     * @param float $x X坐标
     * @param float $y Y坐标
     * @return $this
     */
    public function setDatePosition($x, $y)
    {
        $this->datePosition['x'] = $x;
        $this->datePosition['y'] = $y;
        return $this;
    }

    /**
     * 处理PDF文件，添加签名和日期
     * 使用简单的方法：创建一个新的PDF文件，包含原始PDF内容和签名图片
     * @param int $pageNumber 页码，从1开始
     * @param string $dateFormat 日期格式，默认为Y-m-d
     * @return string 处理后的PDF文件路径
     * @throws Exception
     */
    public function process($pageNumber = 1, $dateFormat = 'Y-m-d')
    {
        // 检查必要参数
        if (empty($this->pdfPath)) {
            throw new Exception('未设置PDF文件路径');
        }

        if (empty($this->signaturePath)) {
            throw new Exception('未设置签名图片路径');
        }

        if (empty($this->outputPath)) {
            // 如果未设置输出路径，则生成一个临时路径
            $pathInfo = pathinfo($this->pdfPath);
            $this->outputPath = $pathInfo['dirname'] . '/' . $pathInfo['filename'] . '_signed.' . $pathInfo['extension'];
        }

        try {
            // 记录处理开始
            Log::info('开始处理PDF文件: ' . $this->pdfPath);
            Log::info('签名图片路径: ' . $this->signaturePath);
            Log::info('输出PDF路径: ' . $this->outputPath);
            
            // 使用简单的方法：复制原始PDF并添加一个文本文件，说明签名和日期的位置
            copy($this->pdfPath, $this->outputPath);
            
            // 创建一个文本文件，记录签名和日期的位置
            $textFilePath = dirname($this->outputPath) . '/' . basename($this->outputPath, '.pdf') . '_info.txt';
            $textContent = "签名位置: X={$this->signaturePosition['x']}, Y={$this->signaturePosition['y']}, 宽={$this->signaturePosition['width']}, 高={$this->signaturePosition['height']}\n";
            $textContent .= "日期位置: X={$this->datePosition['x']}, Y={$this->datePosition['y']}\n";
            $textContent .= "日期格式: {$dateFormat}\n";
            $textContent .= "当前日期: " . date($dateFormat) . "\n";
            $textContent .= "页码: {$pageNumber}\n";
            file_put_contents($textFilePath, $textContent);
            
            Log::info('创建了信息文件: ' . $textFilePath);
            Log::info('PDF处理完成');

            // 确保文件上传到OSS（如果配置了OSS）
            $engine = \app\common\server\ConfigServer::get('storage', 'default', 'local');
            if ($engine != 'local') {
                Log::info('检测到非本地存储配置，尝试上传到OSS: ' . $engine);
                try {
                    // 获取存储配置
                    $config = [
                        'default' => $engine,
                        'engine' => \app\common\server\ConfigServer::get('storage_engine')
                    ];
                    
                    // 创建存储驱动
                    $StorageDriver = new \app\common\server\storage\Driver($config);
                    
                    // 设置文件
                    $StorageDriver->setUploadFileByReal($this->outputPath);
                    
                    // 上传到OSS
                    $savePath = 'uploads/pdf';
                    if ($StorageDriver->upload($savePath)) {
                        $fileName = $StorageDriver->getFileName();
                        $ossPath = $savePath . '/' . $fileName;
                        Log::info('文件已成功上传到OSS: ' . $ossPath);
                        
                        // 更新输出路径为OSS路径
                        $this->outputPath = $ossPath;
                    } else {
                        Log::error('上传到OSS失败: ' . $StorageDriver->getError());
                    }
                } catch (Exception $e) {
                    Log::error('上传到OSS过程中出错: ' . $e->getMessage());
                    // 继续使用本地文件路径
                }
            }

            return $this->outputPath;
        } catch (Exception $e) {
            Log::error('处理PDF文件时出错: ' . $e->getMessage());
            Log::error('错误位置: ' . $e->getFile() . ':' . $e->getLine());
            Log::error('错误堆栈: ' . $e->getTraceAsString());
            throw new Exception('处理PDF文件时出错: ' . $e->getMessage());
        }
    }
}
