<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新PC端UI预览</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Vue.js -->
    <script src="https://cdn.jsdelivr.net/npm/vue@2"></script>
    <style>
        body {
            background-color: #f8f9fa;
        }
        .product-card {
            transition: transform .2s;
        }
        .product-card:hover {
            transform: scale(1.05);
        }
    </style>
</head>
<body>

<div id="app" class="container mt-5">
    <h1 class="mb-4 text-center">{{ title }}</h1>

    <div class="row">
        <div v-for="product in products" :key="product.id" class="col-md-4 mb-4">
            <div class="card product-card">
                <img :src="product.image" class="card-img-top" :alt="product.name">
                <div class="card-body">
                    <h5 class="card-title">{{ product.name }}</h5>
                    <p class="card-text">{{ product.description }}</p>
                    <p class="card-text"><strong>价格:</strong> ¥{{ product.price }}</p>
                    <a href="#" class="btn btn-primary">加入购物车</a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    new Vue({
        el: '#app',
        data: {
            title: '商品列表',
            products: [
                {
                    id: 1,
                    name: 'K-Shop T恤',
                    description: '高品质纯棉T恤，舒适透气。',
                    price: 99,
                    image: 'https://via.placeholder.com/400x300.png?text=K-Shop+T-Shirt'
                },
                {
                    id: 2,
                    name: 'K-Shop 运动鞋',
                    description: '轻便耐磨，适合各种运动场景。',
                    price: 299,
                    image: 'https://via.placeholder.com/400x300.png?text=K-Shop+Sneakers'
                },
                {
                    id: 3,
                    name: 'K-Shop 背包',
                    description: '大容量设计，满足您的出行需求。',
                    price: 199,
                    image: 'https://via.placeholder.com/400x300.png?text=K-Shop+Backpack'
                }
            ]
        }
    });
</script>

</body>
</html>
