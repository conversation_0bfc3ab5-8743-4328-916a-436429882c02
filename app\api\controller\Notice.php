<?php
namespace app\api\controller;

use app\common\logic\SystemNoticeLogic;
use think\facade\Request;
use app\common\basics\Api;
use app\common\server\JsonServer;

/**
 * Class Notice
 * @package app\api\controller
 */
class Notice extends Api
{
     public function index()
    {
        $this->user_id=$this->user_id??0;
        return JsonServer::success('获取成功', SystemNoticeLogic::index($this->user_id));
    }
     /**
     * Notes: 消息列表
     * <AUTHOR> 0:55)
     */
    public function lists()
    {
        $type = $this->request->get('type');
        $lists = SystemNoticeLogic::lists($this->user_id, $type, $this->page_no, $this->page_size);
        return JsonServer::success('获取成功', $lists);
    }
    /**
     * 触发系统通知
     * http://your-domain.com/api/notice/trigger?title=测试&content=这是一个测试通知&type=info
     */
    public function trigger()
    {
        $title   = Request::param('title', '系统通知');
        $content = Request::param('content', '这是一条默认的系统通知内容。');
        $type    = Request::param('type', 'system'); // 支持 system, error, warning, info
        $url     = Request::param('url', '');

        if (empty($content)) {
            return json(['code' => -1, 'msg' => '通知内容不能为空']);
        }

        $result = SystemNoticeLogic::send($title, $content, $type, $url);

        if ($result) {
            return json(['code' => 0, 'msg' => '通知已成功发布']);
        } else {
            return json(['code' => -1, 'msg' => '通知发布失败']);
        }
    }
}