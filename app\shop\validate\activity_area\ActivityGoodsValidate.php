<?php

namespace app\shop\validate\activity_area;
use think\facade\Db;
use app\common\model\activity_area\ActivityAreaGoods;
use app\common\basics\Validate;


class ActivityGoodsValidate extends Validate{
    protected $rule = [
        'activity_id'    => 'require',
        'goods_id'     => 'require|checkGoods',
    ];
    protected $message = [
        'activity_id.require'       => '请选择活动专区',
        'goods_id.require'       => '请先添加商品',
    ];
    protected $scene = [
        'add' => ['activity_id','goods_id'],
    ];

    protected function checkGoods($value,$rule,$data){
        $activity_goods = ActivityAreaGoods::where(['activity_area_id'=>$data['activity_id'],'goods_id'=>$value[0],'del'=>0])
                        ->find();
        if($activity_goods){
            return '该商品已在该活动专区中，请勿重复添加';
        }
        return true;
    }
}