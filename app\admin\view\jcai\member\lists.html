{layout name="layout1" /}

<div class="wrapper">
    <div class="layui-card">
        <!-- 搜索区域 -->
        <div class="layui-card-body layui-form">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label for="order_sn" class="layui-form-label">订单编号：</label>
                    <div class="layui-inline" style="margin-right:0;">
                        <div class="layui-input-inline" >
                            <input type="text" id="order_sn" name="order_sn" placeholder="请输入订单编号" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                </div>
                <div class="layui-inline">
                    <label for="pay_status" class="layui-form-label">支付状态：</label>
                    <div class="layui-inline" style="margin-right:0;">
                        <div class="layui-input-inline" >
                            <select id="pay_status" name="pay_status">
                                <option value="">全部</option>
                                <option value="1">已支付</option>
                                <option value="0">待支付</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="layui-inline">
                    <label for="datetime" class="layui-form-label">创建时间：</label>
                    <div class="layui-inline" style="margin-right:0;">
                        <div class="layui-input-inline" >
                            <input type="text" id="datetime" name="datetime" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                </div>
                <div class="layui-inline">
                    <a class="layui-btn layui-btn-sm layui-btn-normal" lay-submit lay-filter="search">搜索</a>
                    <a class="layui-btn layui-btn-sm layui-btn-primary" lay-submit lay-filter="clear-search">重置</a>
                </div>
            </div>
        </div>

        <!-- 主体区域 -->
        <div class="layui-card-body">
            <div class="layui-tab layui-tab-card" lay-filter="like-tab">
                <ul class="layui-tab-title">
                    <li lay-id="" class="layui-this">全部({$statistics.total})</li>
                    <li lay-id="1">已支付({$statistics.paid})</li>
                    <li lay-id="0">待支付({$statistics.unpaid})</li>
                </ul>
                <div class="layui-tab-content" style="padding:20px;">
                    <table id="like-table-lists" lay-filter="like-table-lists"></table>
                    <script type="text/html" id="table-user">
                        <img src="{{d.avatar}}" alt="图" style="width:50px;height:50px;">
                        <div class="layui-inline">
                            <p>用户ID：{{d.user_id}}</p>
                            <p>用户昵称：{{d.nickname}}</p>
                        </div>
                    </script>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    layui.use(["table", "form", "element", "laydate"], function(){
        var table   = layui.table;
        var form    = layui.form;
        var element = layui.element;
        var laydate = layui.laydate;

        laydate.render({elem:"#datetime", range: true, trigger:"click"});

        like.tableLists("#like-table-lists", "{:url()}", [
            {field:"order_sn", title:"订单编号"}
            ,{field:"user", title:"用户信息", templet:"#table-user"}
            ,{field:"order_amount", align:"center",title:"订单金额"}
            ,{field:"pay_status_text", align:"center", title:"支付状态"}
            ,{field:"create_time_text", align:"center", title:"创建时间"}
            ,{field:"pay_time_text", align:"center", title:"支付时间"}
        ]);

        element.on("tab(like-tab)", function(){
            var type = this.getAttribute("lay-id");
            table.reload("like-table-lists", {
                where: {pay_status: type},
                page: {
                    curr: 1
                }
            });
        });

        form.on("submit(search)", function(data){
            table.reload("like-table-lists", {
                where: data.field,
                page: {
                    curr: 1
                }
            });
        });

        form.on("submit(clear-search)", function(){
            $("#order_sn").val("");
            $("#datetime").val("");
            $("#pay_status").val("");
            table.reload("like-table-lists", {
                where: {},
                page: {
                    curr: 1
                }
            });
        });
    })
</script>
