{"name": "danielst<PERSON>les/stringy", "description": "A string manipulation library with multibyte support", "keywords": ["multibyte", "string", "manipulation", "utility", "methods", "utf-8", "helpers", "utils", "utf"], "homepage": "https://github.com/danielstjules/Stringy", "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.danielstjules.com"}], "require": {"php": ">=5.4.0", "symfony/polyfill-mbstring": "~1.1"}, "require-dev": {"phpunit/phpunit": "~4.0"}, "support": {"issues": "https://github.com/danielstjules/Stringy/issues", "source": "https://github.com/danielstjules/Stringy"}, "autoload": {"psr-4": {"Stringy\\": "src/"}, "files": ["src/Create.php"]}, "autoload-dev": {"classmap": ["tests"]}}