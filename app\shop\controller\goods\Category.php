<?php


namespace app\shop\controller\goods;

use app\common\basics\ShopBase;
use app\common\server\JsonServer;
use think\exception\ValidateException;
use think\facade\View;
use app\shop\logic\goods\CategoryLogic;
use app\shop\validate\goods\GoodsCategoryValidate;

/**
 * 店铺商品分类
 * Class Category
 */
class Category extends ShopBase
{
  /**
   * 列表
   */
  public function lists(){
    if($this->request->isAjax()) {
      $get = $this->request->get();
      $get['shop_id'] = $this->shop_id;
      $data = CategoryLogic::lists($get);
      return JsonServer::success('获取列表成功', $data);
    }
    return view();
  }

  /**
   * 添加
   */
  public function add(){
    if($this->request->isAjax()) {
      $post = $this->request->post();
      $post['del'] = 0;
      $post['shop_id'] = $this->shop_id;
      try {
        validate(GoodsCategoryValidate::class)->scene('add')->check($post);
      } catch (ValidateException $e) {
          return JsonServer::error($e->getError());
      }
      $res = CategoryLogic::add($post);
      if($res) {
        return JsonServer::success('分类添加成功');
      }else{
        return JsonServer::error('分类添加失败');
      }
    }

    return view();
  }

  /**
   * 删除
   */
  public function del(){
    $post = $this->request->post();
    try {
      validate(GoodsCategoryValidate::class)->scene('del')->check($post);
    } catch (ValidateException $e) {
        return JsonServer::error($e->getError());
    }
    $res = CategoryLogic::del($post);
    if($res) {
      return JsonServer::success('删除分类成功');
    }else{
      return JsonServer::error('删除分类失败');
    }
  }


  /**
   * 编辑
   */
  public function edit(){
    if ($this->request->isAjax()) {
      $post = $this->request->post();
      $post['del'] = 0;
      $post['shop_id'] = $this->shop_id;
      try {
        validate(GoodsCategoryValidate::class)->scene('edit')->check($post);
      } catch (ValidateException $e) {
          return JsonServer::error($e->getError());
      }
      $res = CategoryLogic::edit($post);
      if($res) {
        return JsonServer::success('编辑分类成功');
      }else{
        return JsonServer::error('编辑分类失败');
      }
    }

    $id = $this->request->get('id');
    $detail = CategoryLogic::getCategory($id);
    return view('edit', ['detail' => $detail]);
  }

  /**
   * 修改显示状态
   */
  public function switchStatus(){
    $post = $this->request->post();
    $res = CategoryLogic::switchStatus($post);
    if($res) {
      return JsonServer::success('修改成功');
    }else{
      return JsonServer::error('修改失败');
    }
  }
}