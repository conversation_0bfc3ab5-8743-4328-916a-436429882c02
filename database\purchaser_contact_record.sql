-- 采购商联系记录表
DROP TABLE IF EXISTS `ls_purchaser_contact_record`;
CREATE TABLE `ls_purchaser_contact_record` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` int(11) NOT NULL COMMENT '联系用户ID',
  `purchaser_id` int(11) NOT NULL COMMENT '被联系的采购商ID（对应community_article表的id）',
  `contact_time` int(11) NOT NULL COMMENT '联系时间戳',
  `ip` varchar(45) DEFAULT NULL COMMENT '联系时的IP地址',
  `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理信息',
  `source` varchar(50) DEFAULT 'web' COMMENT '联系来源：web-网页，app-应用，mini-小程序',
  `create_time` int(11) NOT NULL COMMENT '创建时间',
  `update_time` int(11) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_purchaser_id` (`purchaser_id`),
  KEY `idx_contact_time` (`contact_time`),
  KEY `idx_user_purchaser` (`user_id`, `purchaser_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='采购商联系记录表';

-- 为了防止重复联系记录，可以添加唯一索引（可选）
-- ALTER TABLE `ls_purchaser_contact_record` ADD UNIQUE KEY `idx_unique_contact` (`user_id`, `purchaser_id`, `contact_time`);
