-- 采购人员分配相关数据库表设计

-- 1. 扩展群发信息限制表，增加采购人员分配配置
ALTER TABLE `ls_mass_message_limit` 
ADD COLUMN `total_purchaser_count` int(11) NOT NULL DEFAULT 0 COMMENT '总分配采购人员数量' AFTER `daily_limit`,
ADD COLUMN `level1_percent` decimal(5,2) NOT NULL DEFAULT 0.00 COMMENT '1级活跃度用户占比(%)' AFTER `total_purchaser_count`,
ADD COLUMN `level2_percent` decimal(5,2) NOT NULL DEFAULT 0.00 COMMENT '2级活跃度用户占比(%)' AFTER `level1_percent`,
ADD COLUMN `level3_percent` decimal(5,2) NOT NULL DEFAULT 0.00 COMMENT '3级活跃度用户占比(%)' AFTER `level2_percent`,
ADD COLUMN `level1_min_score` int(11) NOT NULL DEFAULT 0 COMMENT '1级活跃度最低积分' AFTER `level3_percent`,
ADD COLUMN `level2_min_score` int(11) NOT NULL DEFAULT 101 COMMENT '2级活跃度最低积分' AFTER `level1_min_score`,
ADD COLUMN `level3_min_score` int(11) NOT NULL DEFAULT 501 COMMENT '3级活跃度最低积分' AFTER `level2_min_score`;

-- 2. 创建商家采购人员分配记录表
CREATE TABLE `ls_shop_purchaser_allocation` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `shop_id` int(11) NOT NULL COMMENT '商家ID',
  `user_id` int(11) NOT NULL COMMENT '分配的用户ID',
  `user_level` tinyint(1) NOT NULL DEFAULT 1 COMMENT '用户活跃度等级：1-低活跃度，2-中活跃度，3-高活跃度',
  `user_score` int(11) NOT NULL DEFAULT 0 COMMENT '分配时用户的活跃度积分',
  `allocation_year` year NOT NULL COMMENT '分配年份',
  `allocation_time` int(11) NOT NULL COMMENT '分配时间',
  `shop_tier_level` tinyint(1) NOT NULL DEFAULT 0 COMMENT '分配时商家等级：0=0元入驻，1=商家会员，2=实力厂商',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：1=有效，0=无效',
  `create_time` int(11) NOT NULL COMMENT '创建时间',
  `update_time` int(11) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_shop_user_year` (`shop_id`, `user_id`, `allocation_year`),
  KEY `idx_shop_id` (`shop_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_allocation_year` (`allocation_year`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商家采购人员分配记录表';

-- 3. 创建群发信息发送记录表（如果不存在）
CREATE TABLE IF NOT EXISTS `ls_mass_message_record` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `shop_id` int(11) NOT NULL COMMENT '商家ID',
  `message_type` varchar(50) NOT NULL DEFAULT 'text' COMMENT '消息类型：text-文本，image-图片，mixed-图文',
  `content` text NOT NULL COMMENT '消息内容',
  `target_user_ids` text COMMENT '目标用户ID列表（JSON格式）',
  `send_count` int(11) NOT NULL DEFAULT 0 COMMENT '发送数量',
  `success_count` int(11) NOT NULL DEFAULT 0 COMMENT '成功数量',
  `fail_count` int(11) NOT NULL DEFAULT 0 COMMENT '失败数量',
  `send_status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '发送状态：0-待发送，1-发送中，2-发送完成，3-发送失败',
  `create_time` int(11) NOT NULL COMMENT '创建时间',
  `send_time` int(11) NULL DEFAULT NULL COMMENT '发送时间',
  `complete_time` int(11) NULL DEFAULT NULL COMMENT '完成时间',
  PRIMARY KEY (`id`),
  KEY `idx_shop_id` (`shop_id`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_send_status` (`send_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='群发信息发送记录表';

-- 4. 插入默认配置数据
INSERT INTO `ls_mass_message_limit` (`tier_level`, `daily_limit`, `total_purchaser_count`, `level1_percent`, `level2_percent`, `level3_percent`, `level1_min_score`, `level2_min_score`, `level3_min_score`, `create_time`, `update_time`) VALUES
(0, 5, 20, 50.00, 20.00, 30.00, 0, 101, 501, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(1, 20, 50, 40.00, 30.00, 30.00, 0, 101, 501, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(2, 50, 100, 30.00, 30.00, 40.00, 0, 101, 501, UNIX_TIMESTAMP(), UNIX_TIMESTAMP())
ON DUPLICATE KEY UPDATE
`daily_limit` = VALUES(`daily_limit`),
`total_purchaser_count` = VALUES(`total_purchaser_count`),
`level1_percent` = VALUES(`level1_percent`),
`level2_percent` = VALUES(`level2_percent`),
`level3_percent` = VALUES(`level3_percent`),
`level1_min_score` = VALUES(`level1_min_score`),
`level2_min_score` = VALUES(`level2_min_score`),
`level3_min_score` = VALUES(`level3_min_score`),
`update_time` = UNIX_TIMESTAMP();
