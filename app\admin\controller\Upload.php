<?php


namespace app\admin\controller;


use app\common\basics\AdminBase;
use app\common\server\FileServer;
use app\common\server\JsonServer;
use Exception;

class Upload extends AdminBase
{
//    public $like_not_need_login = ['image'];

    /**
     * NOTE: 上传图片
     * @author: 张无忌
     */
    public function image()
    {
        try {
            $cid = $this->request->post('cid');
            $result = FileServer::image($cid, 0); // 0 平台

            return JsonServer::success("上传成功", $result);
        } catch (Exception $e) {
            return JsonServer::error($e->getMessage());
        }
    }

    /**
     * NOTE: 上传视频
     * @author: 张无忌
     */
    public function video()
    {
        try {
            $cid = $this->request->post('cid');
            $result = FileServer::video($cid, 0); // 0 平台

            return JsonServer::success("上传成功", $result);
        } catch (Exception $e) {
            return JsonServer::error($e->getMessage());
        }
    }

    /**
     * NOTE: 上传文档
     * @author: 系统
     */
    public function document()
    {
        try {
            // 检查是否有文件上传
            if (!$this->request->file('file')) {
                return JsonServer::error('请选择要上传的文件');
            }

            $save_path = 'uploads/documents';
            $result = FileServer::other($save_path);

            // 记录调试信息
            \think\facade\Log::info('文档上传结果: ' . json_encode($result));

            // FileServer::other() 返回格式: ['上传文件成功', $data]
            if (is_array($result) && count($result) >= 2 && $result[0] === '上传文件成功') {
                $data = $result[1];
                // 格式化返回数据，与image上传保持一致
                $response = [
                    'id'   => $data['id'] ?? 0,
                    'name' => $data['name'] ?? '',
                    'uri'  => $data['uri'] ?? '',
                    'size' => $data['size'] ?? 0,
                ];
                return JsonServer::success("上传成功", $response);
            } else {
                $error_msg = '上传失败';
                if (is_array($result) && isset($result[1])) {
                    if (is_array($result[1]) && !empty($result[1])) {
                        $error_msg = $result[1][0]; // 取数组第一个元素
                    } elseif (is_string($result[1])) {
                        $error_msg = $result[1];
                    }
                }
                return JsonServer::error($error_msg);
            }
        } catch (Exception $e) {
            \think\facade\Log::error('文档上传异常: ' . $e->getMessage());
            return JsonServer::error($e->getMessage());
        }
    }
}