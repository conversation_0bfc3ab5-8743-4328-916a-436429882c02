{layout name="layout2" /}
<div class="layui-form" lay-filter="layuiadmin-form-reply" id="layuiadmin-form-reply" style="padding: 20px 30px 0 0;">
    <input name="id" type="hidden" value="{$detail.id}">
    <div class="layui-form-item">
        <label class="layui-form-label"><font color="red">*</font>规则名称：</label>
        <div class="layui-input-inline">
            <input type="text" name="name" value="{$detail.name}" lay-verify="required" placeholder="" autocomplete="off" class="layui-input">
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label"></label><span style="color: #a3a3a3;font-size: 9px">方便通过名称管理默认回复内容</span>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label">内容类型：</label>
        <div class="layui-input-inline">
            <select name="content_type" >
                <option value="1"  {if condition="$detail.content_type eq 1" } selected {/if} >文本</option>
            </select>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">回复内容：</label>
        <div class="layui-input-inline">
            <textarea name="content" placeholder="请输入内容" class="layui-textarea">{$detail.content}</textarea>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label">启用状态：</label>
        <div class="layui-input-inline">
            <input type="radio" name="status" value="1" title="启用" {if condition="$detail.status eq 1" } checked {/if}>
            <input type="radio" name="status" value="0" title="停用"{if condition="$detail.status eq 0" } checked {/if} >
        </div>
    </div>


    <div class="layui-form-item layui-hide">
        <input type="button" lay-submit lay-filter="edit-reply-submit" id="edit-reply-submit" value="确认">
    </div>
</div>

<script>
    layui.config({
        version:"{$front_version}",
        base: '/static/lib/' //静态资源所在路径
    }).use(['form'], function(){
        var $ = layui.$,form = layui.form;
    });
</script>
