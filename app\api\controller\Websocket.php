<?php
namespace app\api\controller;

use app\common\basics\Api;
use app\common\server\JsonServer;
use app\common\utils\Redis;
use think\facade\Config;
use think\facade\Log;
use think\facade\Request;

/**
 * WebSocket API控制器
 * 用于处理WebSocket相关的API请求
 */
class Websocket extends Api
{
    // 部分接口不需要登录验证
    public $like_not_need_login = ['sendAdminNotification', 'sendNotification', 'testConnection'];
    protected $noNeedRight = ['*'];
    
    /**
     * 发送管理员通知
     * @ApiTitle (发送管理员通知)
     * @ApiSummary (发送通知给所有管理员)
     * @ApiMethod (POST)
     * @ApiParams (
     *   {"name":"title", "type":"string", "require":true, "desc":"通知标题"},
     *   {"name":"content", "type":"string", "require":true, "desc":"通知内容"},
     *   {"name":"type", "type":"string", "require":false, "desc":"通知类型，默认为admin_notification"},
     *   {"name":"url", "type":"string", "require":false, "desc":"点击通知跳转的URL"},
     *   {"name":"icon", "type":"integer", "require":false, "desc":"通知图标：0-默认，1-成功，2-错误，3-警告，4-信息"}
     * )
     * @ApiReturn ({"code":1,"msg":"成功","data":null})
     */
    public function sendAdminNotification()
    {
        $params = Request::post();
        $params['title']=$params['title']??'你好.这是测试消息';
        $params['content']=$params['content']??'你好.这是测试消息内容';
        // 验证参数
        if (empty($params['title']) || empty($params['content'])) {
            return JsonServer::error('标题和内容不能为空');
        }
        
        $title = $params['title'];
        $content = $params['content'];
        $type = $params['type'] ?? 'admin_notification';
        $url = $params['url'] ?? '';
        $icon = isset($params['icon']) ? intval($params['icon']) : 0;
        
        // 记录请求信息
        trace('收到发送管理员通知请求: ' . json_encode([
            'title' => $title,
            'content' => $content,
            'type' => $type,
            'url' => $url,
            'icon' => $icon
        ], JSON_UNESCAPED_UNICODE), 'info');
        
        try {
            // 构建与test_websocket.html兼容的消息格式
            $wsMessage = [
                'event' => 'admin_notification',
                'data' => [
                    'type' => $type === 'admin_notification' ? 'admin_notification' : ($type === 'system_notification' ? 'system' : 'personal'),
                    'title' => $title,
                    'content' => $content,
                    'url' => $url,
                    'icon' => $icon,
                    'timestamp' => time()
                ]
            ];
            
            // 记录尝试发送的消息
            trace('尝试发送WebSocket消息: ' . json_encode($wsMessage), 'info');
            
            // 保存到Redis，让WebSocket服务器从Redis中读取并推送
            $redis = new Redis();
            
            // 检查Redis是否支持发布订阅
            if (method_exists($redis, 'publish')) {
                $redis->publish('admin_notifications', json_encode($wsMessage));
                trace('通知已发布到Redis频道', 'info');
            } else {
                // 使用set方法存储最新的通知
                $redis->set('latest_admin_notification', json_encode($wsMessage), 3600); // 1小时过期
                trace('通知已保存到Redis', 'info');
            }
            
            // 尝试使用GatewayWorker推送
            if (class_exists('\GatewayWorker\Lib\Gateway')) {
                try {
                    // 获取Gateway配置
                    $gateway_address = Config::get('gateway.register_address', '127.0.0.1:1236');
                    
                    // 设置Gateway注册地址
                    if (method_exists('\GatewayWorker\Lib\Gateway', 'setRegisterAddress')) {
                        \GatewayWorker\Lib\Gateway::setRegisterAddress($gateway_address);
                    }
                    
                    // 推送到管理员组
                    if (method_exists('\GatewayWorker\Lib\Gateway', 'sendToGroup')) {
                        \GatewayWorker\Lib\Gateway::sendToGroup('admin_group', json_encode($wsMessage));
                        trace('通知已通过GatewayWorker推送给平台管理员', 'info');
                    }
                } catch (\Exception $e) {
                    trace('GatewayWorker推送失败，但Redis推送已成功: ' . $e->getMessage(), 'warning');
                    // 不抛出异常，因为Redis推送已经成功
                }
            } else {
                trace('GatewayWorker类不存在，仅使用Redis推送', 'info');
            }
            
            return JsonServer::success('通知已发送');
        } catch (\Exception $e) {
            trace('发送WebSocket消息失败: ' . $e->getMessage(), 'error');
            return JsonServer::error('发送失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 发送通知
     * @ApiTitle (发送通知)
     * @ApiSummary (发送通知给指定用户或用户组)
     * @ApiMethod (POST)
     * @ApiParams (
     *   {"name":"title", "type":"string", "require":true, "desc":"通知标题"},
     *   {"name":"content", "type":"string", "require":true, "desc":"通知内容"},
     *   {"name":"type", "type":"string", "require":false, "desc":"通知类型，默认为notification"},
     *   {"name":"user_id", "type":"integer", "require":false, "desc":"接收用户ID，不传则为系统通知"},
     *   {"name":"receive_type", "type":"integer", "require":false, "desc":"接收类型：1-会员，2-商家，3-平台，4-游客"},
     *   {"name":"url", "type":"string", "require":false, "desc":"点击通知跳转的URL"},
     *   {"name":"icon", "type":"integer", "require":false, "desc":"通知图标：0-默认，1-成功，2-错误，3-警告，4-信息"}
     * )
     * @ApiReturn ({"code":1,"msg":"成功","data":null})
     */
    public function sendNotification()
    {
        $params = Request::post();
        $params['title']=$params['title']??'你好.这是测试消息';
        $params['content']=$params['content']??'你好.这是测试消息内容';
        // 验证参数
        if (empty($params['title']) || empty($params['content'])) {
            return JsonServer::error('标题和内容不能为空');
        }
        
        $title = $params['title'];
        $content = $params['content'];
        $type = $params['type'] ?? 'notification';
        $user_id = isset($params['user_id']) ? intval($params['user_id']) : 0;
        $receive_type = isset($params['receive_type']) ? intval($params['receive_type']) : 3; // 默认为平台通知
        $url = $params['url'] ?? '';
        $icon = isset($params['icon']) ? intval($params['icon']) : 0;
        
        // 记录请求信息
        trace('收到发送通知请求: ' . json_encode([
            'title' => $title,
            'content' => $content,
            'type' => $type,
            'user_id' => $user_id,
            'receive_type' => $receive_type,
            'url' => $url,
            'icon' => $icon
        ], JSON_UNESCAPED_UNICODE), 'info');
        
        try {
            // 根据接收类型确定目标组
            $targetGroup = '';
            switch ($receive_type) {
                case 1: // 会员
                    $targetGroup = 'user_group';
                    break;
                case 2: // 商家
                    $targetGroup = 'shop_group';
                    break;
                case 3: // 平台
                    $targetGroup = 'admin_group';
                    break;
                default:
                    $targetGroup = 'all';
            }
            
            // 构建消息
            $wsMessage = [
                'event' => $type,
                'data' => [
                    'type' => $type,
                    'title' => $title,
                    'content' => $content,
                    'user_id' => $user_id,
                    'receive_type' => $receive_type,
                    'url' => $url,
                    'icon' => $icon,
                    'timestamp' => time()
                ]
            ];
            
            // 记录尝试发送的消息
            trace('尝试发送WebSocket消息: ' . json_encode($wsMessage) . ' 到组: ' . $targetGroup, 'info');
            
            // 保存到Redis，让WebSocket服务器从Redis中读取并推送
            $redis = new Redis();
            
            // 检查Redis是否支持发布订阅
            if (method_exists($redis, 'publish')) {
                $redis->publish('notifications_' . $targetGroup, json_encode($wsMessage));
                trace('通知已发布到Redis频道: notifications_' . $targetGroup, 'info');
            } else {
                // 使用set方法存储最新的通知
                $redis->set('latest_notification_' . $targetGroup, json_encode($wsMessage), 3600); // 1小时过期
                trace('通知已保存到Redis', 'info');
            }
            
            // 尝试使用GatewayWorker推送
            if (class_exists('\GatewayWorker\Lib\Gateway')) {
                try {
                    // 获取Gateway配置
                    $gateway_address = Config::get('gateway.register_address', '127.0.0.1:1236');
                    
                    // 设置Gateway注册地址
                    if (method_exists('\GatewayWorker\Lib\Gateway', 'setRegisterAddress')) {
                        \GatewayWorker\Lib\Gateway::setRegisterAddress($gateway_address);
                    }
                    
                    // 推送到目标组
                    if (method_exists('\GatewayWorker\Lib\Gateway', 'sendToGroup')) {
                        \GatewayWorker\Lib\Gateway::sendToGroup($targetGroup, json_encode($wsMessage));
                        trace('通知已通过GatewayWorker推送给组: ' . $targetGroup, 'info');
                    }
                } catch (\Exception $e) {
                    trace('GatewayWorker推送失败，但Redis推送已成功: ' . $e->getMessage(), 'warning');
                    // 不抛出异常，因为Redis推送已经成功
                }
            } else {
                trace('GatewayWorker类不存在，仅使用Redis推送', 'info');
            }
            
            return JsonServer::success('通知已发送');
        } catch (\Exception $e) {
            trace('发送WebSocket消息失败: ' . $e->getMessage(), 'error');
            return JsonServer::error('发送失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 测试WebSocket连接
     * @ApiTitle (测试WebSocket连接)
     * @ApiSummary (测试WebSocket服务器连接是否正常)
     * @ApiMethod (GET)
     * @ApiReturn ({"code":1,"msg":"成功","data":{"status":"ok","message":"WebSocket服务器连接正常"}})
     */
    public function testConnection()
    {
        try {
            // 检查WebSocket服务器是否可用
            $wsUrl = Config::get('websocket.url', 'wss://kefu.huohanghang.cn');
            
            // 记录测试信息
            trace('测试WebSocket连接: ' . $wsUrl, 'info');
            
            // 这里可以添加实际的连接测试代码
            // 由于PHP不容易直接测试WebSocket连接，我们可以通过其他方式实现
            // 例如，检查Redis中是否有最新的心跳记录
            
            $redis = new Redis();
            $lastHeartbeat = $redis->get('websocket_last_heartbeat');
            
            if ($lastHeartbeat) {
                $lastHeartbeatTime = intval($lastHeartbeat);
                $now = time();
                
                if ($now - $lastHeartbeatTime < 300) { // 5分钟内有心跳
                    return JsonServer::success('WebSocket服务器连接正常', [
                        'status' => 'ok',
                        'message' => 'WebSocket服务器连接正常',
                        'last_heartbeat' => date('Y-m-d H:i:s', $lastHeartbeatTime),
                        'elapsed' => $now - $lastHeartbeatTime . '秒'
                    ]);
                } else {
                    return JsonServer::error('WebSocket服务器可能不可用，最后心跳时间: ' . date('Y-m-d H:i:s', $lastHeartbeatTime));
                }
            } else {
                // 没有心跳记录，尝试发送一个测试消息
                $testMessage = [
                    'event' => 'test_connection',
                    'data' => [
                        'timestamp' => time(),
                        'message' => '测试连接'
                    ]
                ];
                
                // 保存到Redis
                $redis->set('websocket_test_connection', json_encode($testMessage), 300); // 5分钟过期
                
                return JsonServer::success('已发送测试消息，请检查WebSocket服务器日志', [
                    'status' => 'pending',
                    'message' => '已发送测试消息，请检查WebSocket服务器日志'
                ]);
            }
        } catch (\Exception $e) {
            trace('测试WebSocket连接失败: ' . $e->getMessage(), 'error');
            return JsonServer::error('测试失败: ' . $e->getMessage());
        }
    }
}
