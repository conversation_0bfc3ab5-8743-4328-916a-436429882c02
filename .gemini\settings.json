{"theme": "De<PERSON>ult Light", "selectedAuthType": "oauth-personal", "mcpServers": {"memory": {"type": "stdio", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"]}, "sequential-thinking": {"type": "stdio", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]}, "context7": {"type": "stdio", "command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"], "env": {"CONTEXT7_API_URL": "https://api.context7.com"}}, "ssh-mpc-server": {"command": "npx", "args": ["-y", "@fangjunjie/ssh-mcp-server", "--host", "***************", "--port", "22", "--username", "root", "--password", "Ks81GHXkAd^U2x@_@"], "disabled": false}, "playwright": {"command": "npx", "args": ["@playwright/mcp@latest"]}, "browser-tools-mcp": {"command": "npx", "args": ["@agentdeskai/browser-tools-mcp@latest"], "env": {"BROWSER_TOOLS_HOST": "127.0.0.1", "BROWSER_TOOLS_PORT": "3025"}}}}