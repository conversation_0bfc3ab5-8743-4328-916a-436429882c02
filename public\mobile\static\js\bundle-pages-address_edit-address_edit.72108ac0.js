(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["bundle-pages-address_edit-address_edit"],{"1f71":function(e,t,i){"use strict";var n=i("5f34"),a=i.n(n);a.a},3345:function(e,t,i){"use strict";i.r(t);var n=i("f79d"),a=i.n(n);for(var s in n)["default"].indexOf(s)<0&&function(e){i.d(t,e,(function(){return n[e]}))}(s);t["default"]=a.a},4837:function(e,t,i){"use strict";i.d(t,"b",(function(){return a})),i.d(t,"c",(function(){return s})),i.d(t,"a",(function(){return n}));var n={uIcon:i("90f3").default},a=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("v-uni-view",{staticClass:"u-checkbox",style:[e.checkboxStyle]},[i("v-uni-view",{staticClass:"u-checkbox__icon-wrap",class:[e.iconClass],style:[e.iconStyle],on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.toggle.apply(void 0,arguments)}}},[i("u-icon",{staticClass:"u-checkbox__icon-wrap__icon",attrs:{name:"checkbox-mark",size:e.checkboxIconSize,color:e.iconColor}})],1),i("v-uni-view",{staticClass:"u-checkbox__label",style:{fontSize:e.$u.addUnit(e.labelSize)},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.onClickLabel.apply(void 0,arguments)}}},[e._t("default")],2)],1)},s=[]},"5f34":function(e,t,i){var n=i("f101");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[e.i,n,""]]),n.locals&&(e.exports=n.locals);var a=i("4f06").default;a("3690223c",n,!0,{sourceMap:!1,shadowMode:!1})},7630:function(e,t,i){"use strict";i.d(t,"b",(function(){return a})),i.d(t,"c",(function(){return s})),i.d(t,"a",(function(){return n}));var n={uField:i("e704").default,uCheckbox:i("dc83").default,uSelect:i("08cf").default},a=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("v-uni-view",{staticClass:"address-edit"},[i("v-uni-view",{staticClass:"form bg-white"},[i("u-field",{attrs:{label:"收货人",placeholder:"请填写收货人姓名"},model:{value:e.addressObj.contact,callback:function(t){e.$set(e.addressObj,"contact",t)},expression:"addressObj.contact"}}),i("u-field",{attrs:{label:"联系方式",placeholder:"请填写手机号码"},model:{value:e.addressObj.telephone,callback:function(t){e.$set(e.addressObj,"telephone",t)},expression:"addressObj.telephone"}}),i("v-uni-view",{on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.showRegion=!0}}},[i("u-field",{attrs:{disabled:!0,label:"所在地区",placeholder:"请选择省、市、区","right-icon":"arrow-right"},model:{value:e.region,callback:function(t){e.region=t},expression:"region"}})],1),i("v-uni-view",[i("u-field",{attrs:{type:"textarea",label:"详细地址",placeholder:"请填写小区、街道、门牌号等信息","field-style":{flex:1,height:"200rpx"}},model:{value:e.addressObj.address,callback:function(t){e.$set(e.addressObj,"address",t)},expression:"addressObj.address"}})],1)],1),i("v-uni-view",{staticClass:"m-t-10 m-b-10 bg-white p-20"},[i("u-checkbox",{attrs:{shape:"circle"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.changeDefault.apply(void 0,arguments)}},model:{value:e.addressObj.is_default,callback:function(t){e.$set(e.addressObj,"is_default",t)},expression:"addressObj.is_default"}},[i("v-uni-text",{staticClass:"xs"},[e._v("设置为默认")])],1)],1),i("v-uni-button",{staticClass:"my-btn bg-primary white br60",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.formSubmit.apply(void 0,arguments)}}},[e._v("完成")]),i("u-select",{attrs:{mode:"mutil-column-auto",list:e.lists},on:{confirm:function(t){arguments[0]=t=e.$handleEvent(t),e.regionChange.apply(void 0,arguments)}},model:{value:e.showRegion,callback:function(t){e.showRegion=t},expression:"showRegion"}})],1)},s=[]},7998:function(e,t,i){var n=i("ab4e");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[e.i,n,""]]),n.locals&&(e.exports=n.locals);var a=i("4f06").default;a("7f1256af",n,!0,{sourceMap:!1,shadowMode:!1})},ab4e:function(e,t,i){var n=i("24fb");t=n(!1),t.push([e.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.u-checkbox[data-v-0b68f884]{display:inline-flex;align-items:center;overflow:hidden;-webkit-user-select:none;user-select:none;line-height:1.8}.u-checkbox__icon-wrap[data-v-0b68f884]{color:#606266;flex:none;display:-webkit-flex;display:flex;flex-direction:row;align-items:center;justify-content:center;box-sizing:border-box;width:%?42?%;height:%?42?%;color:transparent;text-align:center;transition-property:color,border-color,background-color;font-size:20px;border:1px solid #c8c9cc;transition-duration:.2s}.u-checkbox__icon-wrap--circle[data-v-0b68f884]{border-radius:100%}.u-checkbox__icon-wrap--square[data-v-0b68f884]{border-radius:%?6?%}.u-checkbox__icon-wrap--checked[data-v-0b68f884]{color:#fff;background-color:#ff2c3c;border-color:#ff2c3c}.u-checkbox__icon-wrap--disabled[data-v-0b68f884]{background-color:#ebedf0;border-color:#c8c9cc}.u-checkbox__icon-wrap--disabled--checked[data-v-0b68f884]{color:#c8c9cc!important}.u-checkbox__label[data-v-0b68f884]{word-wrap:break-word;margin-left:%?10?%;margin-right:%?24?%;color:#606266;font-size:%?30?%}.u-checkbox__label--disabled[data-v-0b68f884]{color:#c8c9cc}',""]),e.exports=t},bce5:function(e,t,i){"use strict";i.r(t);var n=i("7630"),a=i("cad8");for(var s in a)["default"].indexOf(s)<0&&function(e){i.d(t,e,(function(){return a[e]}))}(s);i("1f71");var r=i("f0c5"),c=Object(r["a"])(a["default"],n["b"],n["c"],!1,null,"217828ec",null,!1,n["a"],void 0);t["default"]=c.exports},cad8:function(e,t,i){"use strict";i.r(t);var n=i("fe63"),a=i.n(n);for(var s in n)["default"].indexOf(s)<0&&function(e){i.d(t,e,(function(){return n[e]}))}(s);t["default"]=a.a},dc83:function(e,t,i){"use strict";i.r(t);var n=i("4837"),a=i("3345");for(var s in a)["default"].indexOf(s)<0&&function(e){i.d(t,e,(function(){return a[e]}))}(s);i("e32c");var r=i("f0c5"),c=Object(r["a"])(a["default"],n["b"],n["c"],!1,null,"0b68f884",null,!1,n["a"],void 0);t["default"]=c.exports},e32c:function(e,t,i){"use strict";var n=i("7998"),a=i.n(n);a.a},f101:function(e,t,i){var n=i("24fb");t=n(!1),t.push([e.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.address-edit[data-v-217828ec]{padding-top:%?10?%}.address-edit .my-btn[data-v-217828ec]{margin:%?30?% %?26?%;text-align:center}',""]),e.exports=t},f79d:function(e,t,i){"use strict";i("7a82"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,i("a9e3"),i("14d9"),i("d81d");var n={name:"u-checkbox",props:{name:{type:[String,Number],default:""},shape:{type:String,default:""},value:{type:Boolean,default:!1},disabled:{type:[String,Boolean],default:""},labelDisabled:{type:[String,Boolean],default:""},activeColor:{type:String,default:""},iconSize:{type:[String,Number],default:""},labelSize:{type:[String,Number],default:""},size:{type:[String,Number],default:""}},data:function(){return{parentDisabled:!1,newParams:{}}},created:function(){this.parent=this.$u.$parent.call(this,"u-checkbox-group"),this.parent&&this.parent.children.push(this)},computed:{isDisabled:function(){return""!==this.disabled?this.disabled:!!this.parent&&this.parent.disabled},isLabelDisabled:function(){return""!==this.labelDisabled?this.labelDisabled:!!this.parent&&this.parent.labelDisabled},checkboxSize:function(){return this.size?this.size:this.parent?this.parent.size:34},checkboxIconSize:function(){return this.iconSize?this.iconSize:this.parent?this.parent.iconSize:20},elActiveColor:function(){return this.activeColor?this.activeColor:this.parent?this.parent.activeColor:"primary"},elShape:function(){return this.shape?this.shape:this.parent?this.parent.shape:"square"},iconStyle:function(){var e={};return this.elActiveColor&&this.value&&!this.isDisabled&&(e.borderColor=this.elActiveColor,e.backgroundColor=this.elActiveColor),e.width=this.$u.addUnit(this.checkboxSize),e.height=this.$u.addUnit(this.checkboxSize),e},iconColor:function(){return this.value?"#ffffff":"transparent"},iconClass:function(){var e=[];return e.push("u-checkbox__icon-wrap--"+this.elShape),1==this.value&&e.push("u-checkbox__icon-wrap--checked"),this.isDisabled&&e.push("u-checkbox__icon-wrap--disabled"),this.value&&this.isDisabled&&e.push("u-checkbox__icon-wrap--disabled--checked"),e.join(" ")},checkboxStyle:function(){var e={};return this.parent&&this.parent.width&&(e.width=this.parent.width,e.flex="0 0 ".concat(this.parent.width)),this.parent&&this.parent.wrap&&(e.width="100%",e.flex="0 0 100%"),e}},methods:{onClickLabel:function(){this.isLabelDisabled||this.isDisabled||this.setValue()},toggle:function(){this.isDisabled||this.setValue()},emitEvent:function(){var e=this;this.$emit("change",{value:!this.value,name:this.name}),setTimeout((function(){e.parent&&e.parent.emitEvent&&e.parent.emitEvent()}),80)},setValue:function(){var e=0;if(this.parent&&this.parent.children&&this.parent.children.map((function(t){t.value&&e++})),1==this.value)this.emitEvent(),this.$emit("input",!this.value);else{if(this.parent&&e>=this.parent.max)return this.$u.toast("最多可选".concat(this.parent.max,"项"));this.emitEvent(),this.$emit("input",!this.value)}}}};t.default=n},fe63:function(e,t,i){"use strict";i("7a82");var n=i("ee27").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=n(i("f07e")),s=n(i("c964"));i("e25e"),i("99af");var r=i("8516"),c=n(i("d0ca")),o={data:function(){return{addressObj:{contact:"",telephone:"",province:"",city:"",district:"",address:"",is_default:!1},region:"",addressId:"",defaultRegion:["广东省","广州市","番禺区"],defaultRegionCode:"440113",showRegion:!1,lists:[]}},onLoad:function(e){var t=this;this.addressId=parseInt(e.id),e.id?(uni.setNavigationBarTitle({title:"编辑地址"}),this.getOneAddressFun()):(uni.setNavigationBarTitle({title:"添加地址"}),this.getWxAddressFun()),this.$nextTick((function(){t.lists=c.default}))},onUnload:function(){uni.removeStorageSync("wxAddress")},methods:{formSubmit:function(){var e=this;return(0,s.default)((0,a.default)().mark((function t(){var i,n,s,c,o,d,l,u,f,h,p,b,v,x;return(0,a.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(i=e.addressObj,n=i.contact,s=i.telephone,c=i.province_id,o=i.city_id,d=i.district_id,l=i.is_default,u=i.address,f=e.addressId,h=e.region,n){t.next=3;break}return t.abrupt("return",e.$toast({title:"请填写收货人姓名"}));case 3:if(s){t.next=5;break}return t.abrupt("return",e.$toast({title:"请填写手机号码"}));case 5:if(h){t.next=7;break}return t.abrupt("return",e.$toast({title:"请选择省、市、区"}));case 7:if(u){t.next=9;break}return t.abrupt("return",e.$toast({title:"请填写小区、街道、门牌号等信息"}));case 9:if(p={contact:n,telephone:s,province_id:parseInt(c),city_id:parseInt(o),district_id:parseInt(d),is_default:l?1:0,id:f,address:u},!f){t.next=16;break}return t.next=13,(0,r.editAddress)(p);case 13:t.t0=t.sent,t.next=19;break;case 16:return t.next=18,(0,r.addAddress)(p);case 18:t.t0=t.sent;case 19:b=t.t0,v=b.code,x=b.msg,1==v&&e.$toast({title:x},{tab:3,url:2});case 23:case"end":return t.stop()}}),t)})))()},regionChange:function(e){this.addressObj.province_id=e[0].value,this.addressObj.city_id=e[1].value,this.addressObj.district_id=e[2].value,this.region=e[0].label+" "+e[1].label+" "+e[2].label},getOneAddressFun:function(){var e=this;(0,r.getOneAddress)(this.addressId).then((function(t){if(1==t.code){var i=t.data,n=i.city,a=i.province,s=i.district;e.addressObj=t.data,e.region="".concat(a," ").concat(n," ").concat(s)}}))},getWxAddressFun:function(){var e=this,t=uni.getStorageSync("wxAddress");if(t){t=JSON.parse(t);var i=t,n=i.userName,a=i.telNumber,s=i.provinceName,c=i.cityName,o=i.detailInfo,d=t.countryName||t.countyName;(0,r.hasRegionCode)({province:s,city:c,district:d}).then((function(t){1==t.code&&(t.data.province&&t.data.city&&t.data.district&&(e.region="".concat(s," ").concat(c," ").concat(d),e.addressObj.province_id=t.data.province,e.addressObj.city_id=t.data.city,e.addressObj.district_id=t.data.district),e.addressObj.contact=n,e.addressObj.telephone=a,e.addressObj.address=o)}))}}}};t.default=o}}]);