/**
 * iframe页面主题辅助脚本
 * 自动为iframe页面添加主题支持
 * 使用方法：在iframe页面的head中引入此脚本即可
 */

(function() {
  'use strict';

  // 检查是否已经引入了主题CSS
  function ensureThemeCSS() {
    const existingThemeCSS = document.querySelector('link[href*="theme.css"]');
    if (!existingThemeCSS) {
      const link = document.createElement('link');
      link.rel = 'stylesheet';
      link.href = '../../layuiadmin/style/theme.css';
      link.media = 'all';
      document.head.appendChild(link);
      console.log('自动添加主题CSS文件');
    }
  }

  // 检查是否已经引入了主题同步脚本
  function ensureThemeSyncJS() {
    const existingThemeJS = document.querySelector('script[src*="theme-sync.js"]');
    if (!existingThemeJS) {
      const script = document.createElement('script');
      script.src = '../../layuiadmin/lib/theme-sync.js';
      script.onload = function() {
        console.log('主题同步脚本加载完成');
        // 立即初始化主题
        if (window.LayuiTheme) {
          window.LayuiTheme.init();
        }
      };
      document.head.appendChild(script);
      console.log('自动添加主题同步脚本');
    }
  }

  // 强制应用当前主题
  function forceApplyTheme() {
    const savedTheme = localStorage.getItem('layuiadmin-theme') || 'light';
    const body = document.body;
    const html = document.documentElement;
    
    // 移除所有主题类
    body.removeAttribute('data-theme');
    html.removeAttribute('data-theme');
    
    // 设置新主题
    if (savedTheme && savedTheme !== 'light') {
      body.setAttribute('data-theme', savedTheme);
      html.setAttribute('data-theme', savedTheme);
    }
    
    console.log('强制应用主题:', savedTheme);
  }

  // 初始化
  function init() {
    // 确保CSS和JS文件已引入
    ensureThemeCSS();
    ensureThemeSyncJS();
    
    // 立即应用主题
    forceApplyTheme();
    
    // 监听主题变化
    window.addEventListener('message', function(e) {
      if (e.data && e.data.type === 'theme-change') {
        forceApplyTheme();
      }
    });
  }

  // 页面加载完成后初始化
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', init);
  } else {
    init();
  }

  // 导出到全局
  window.IframeThemeHelper = {
    init: init,
    ensureThemeCSS: ensureThemeCSS,
    ensureThemeSyncJS: ensureThemeSyncJS,
    forceApplyTheme: forceApplyTheme
  };

})();
