<?php

namespace app\common\model\shop;

use app\common\basics\Models;

/**
 * 商家资质日志模型
 * Class ShopQualificationLog
 * @package app\common\model\shop
 */
class ShopQualificationLog extends Models
{
    /**
     * 关联商家资质表
     */
    public function shopQualification()
    {
        return $this->belongsTo(ShopQualification::class, 'shop_qualification_id', 'id');
    }

    /**
     * 获取操作类型描述
     */
    public function getActionTextAttr($value, $data)
    {
        $action = isset($data['action']) ? $data['action'] : '';
        $actionMap = [
            'upload' => '上传资质',
            'audit' => '审核资质',
            'update' => '更新资质',
            'delete' => '删除资质'
        ];
        return $actionMap[$action] ?? $action;
    }

    /**
     * 获取操作人类型描述
     */
    public function getOperatorTypeTextAttr($value, $data)
    {
        $operatorType = isset($data['operator_type']) ? intval($data['operator_type']) : 1;
        $typeMap = [
            1 => '商家',
            2 => '管理员'
        ];
        return $typeMap[$operatorType] ?? '未知';
    }

    /**
     * 记录操作日志
     */
    public static function addLog($shopQualificationId, $shopId, $qualificationId, $action, $oldStatus = 0, $newStatus = 0, $remark = '', $operatorId = 0, $operatorType = 1)
    {
        $data = [
            'shop_qualification_id' => $shopQualificationId,
            'shop_id' => $shopId,
            'qualification_id' => $qualificationId,
            'action' => $action,
            'old_status' => $oldStatus,
            'new_status' => $newStatus,
            'remark' => $remark,
            'operator_id' => $operatorId,
            'operator_type' => $operatorType,
            'create_time' => time()
        ];
        
        return self::create($data);
    }
}
