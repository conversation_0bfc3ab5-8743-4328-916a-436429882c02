(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["bundle-pages-activity_detail-activity_detail~bundle-pages-bargain_process-bargain_process~bundle-pag~4d847500"],{1996:function(t,i,e){"use strict";e.d(i,"b",(function(){return a})),e.d(i,"c",(function(){return o})),e.d(i,"a",(function(){return s}));var s={uImage:e("f919").default,priceFormat:e("a272").default},a=function(){var t=this,i=t.$createElement,e=t._self._c||i;return e("v-uni-view",{staticClass:"goods-list"},["waterfall"==t.type?e("v-uni-view",{staticClass:"goods-waterfall"},t._l(t.list,(function(i,s){return e("router-link",{key:s,attrs:{to:{path:"/pages/goods_details/goods_details",query:{id:i.goods_id||i.id}}}},[e("v-uni-view",{staticClass:"item bg-white m-t-20",style:{width:t.width}},[e("v-uni-view",{staticClass:"goods-img"},[e("u-image",{attrs:{width:t.width,height:t.width,"border-radius":10,src:i.image}})],1),e("v-uni-view",{staticClass:"goods-info"},[e("v-uni-view",{staticClass:"goods-name line-2"},[t._v(t._s(i.name))]),e("v-uni-view",{staticClass:"price mt10 row"},[e("price-format",{staticClass:"m-r-10",attrs:{color:t.colorConfig.primary,"first-size":34,"second-size":26,"subscript-size":26,price:i.min_price,weight:500}}),0!=i.market_price&&0!=i.price?e("price-format",{staticClass:"muted",attrs:{firstSize:24,secondSize:24,"subscript-size":24,"line-through":!0,price:i.market_price||i.price}}):t._e()],1)],1)],1)],1)})),1):t._e(),"double"==t.type?e("v-uni-view",{staticClass:"goods-double double flex flex-wrap row-between col-stretch p-l-20 p-r-20"},t._l(t.list,(function(i,s){return e("router-link",{key:s,staticClass:"m-t-20",attrs:{to:{path:"/pages/goods_details/goods_details",query:{id:i.goods_id||i.id}}}},[e("v-uni-view",{staticClass:"item bg-white",style:{width:t.width,height:"100%"}},[e("v-uni-view",{staticClass:"goods-img"},[e("u-image",{attrs:{width:t.width,height:t.width,"border-radius":10,src:i.image}})],1),e("v-uni-view",{staticClass:"goods-info"},[e("v-uni-view",{staticClass:"goods-name line-2",class:{"store-tag":1==i.shop_type}},[t._v(t._s(i.name))]),e("v-uni-view",{staticClass:"price mt10 row"},[e("price-format",{staticClass:"m-r-10",attrs:{color:t.colorConfig.primary,"first-size":34,"second-size":26,"subscript-size":26,price:i.activity_price||i.min_price,weight:500}}),0!=i.market_price&&0!=i.price?e("price-format",{staticClass:"muted",attrs:{firstSize:24,secondSize:24,"subscript-size":24,"line-through":!0,price:i.market_price||i.price}}):t._e()],1)],1)],1)],1)})),1):t._e(),"one"===t.type&&t.list.length?e("v-uni-view",{staticClass:"goods-one m-t-20"},t._l(t.list,(function(i,s){return e("router-link",{key:s,attrs:{to:{path:"/pages/goods_details/goods_details",query:{id:i.goods_id||i.id}}}},[e("v-uni-view",{staticClass:"item bg-white flex col-top"},[e("v-uni-view",{staticClass:"goods-img"},[e("u-image",{attrs:{width:"200rpx",height:"200rpx","border-radius":10,src:i.image}})],1),e("v-uni-view",{staticClass:"goods-info m-l-20 flex-1"},[e("v-uni-view",{staticClass:"goods-name line-2 m-b-10",class:{"store-tag":1==i.shop_type}},[t._v(t._s(i.name))]),e("v-uni-view",{staticClass:"flex row-between m-t-10"},[e("v-uni-view",{staticClass:"price m-t-10 flex"},[e("price-format",{staticClass:"m-r-10",attrs:{color:t.colorConfig.primary,"first-size":34,"second-size":26,"subscript-size":26,price:i.min_price||i.price,weight:500}}),0!=i.market_price&&0!=i.price?e("price-format",{staticClass:"muted",attrs:{firstSize:24,secondSize:24,"subscript-size":24,"line-through":!0,price:i.market_price||i.price}}):t._e()],1)],1)],1)],1)],1)})),1):t._e(),t.type.includes("row")?e("v-uni-view",{staticClass:"goods-row flex"},t._l(t.list,(function(i,s){return e("router-link",{key:s,staticClass:"item",attrs:{to:{path:"/pages/goods_details/goods_details",query:{id:i.goods_id||i.id}}}},[e("v-uni-view",{class:[{"bg-white":t.showBg}]},[e("v-uni-view",{staticClass:"goods-img"},[e("u-image",{attrs:{width:"240rpx",height:"240rpx","border-radius":10,src:i.image}})],1),e("v-uni-view",{staticClass:"goods-info"},[e("v-uni-view",{staticClass:"goods-name line-1 sm"},[t._v(t._s(i.name))]),e("v-uni-view",{staticClass:"price m-t-10 row"},[e("price-format",{staticClass:"m-r-10",attrs:{color:t.colorConfig.primary,"first-size":28,"second-size":22,"subscript-size":22,price:i.min_price,weight:500}}),0!=i.market_price&&0!=i.price?e("price-format",{staticClass:"muted",attrs:{firstSize:22,secondSize:22,"subscript-size":22,"line-through":!0,price:i.market_price||i.price}}):t._e()],1)],1),s<3&&"row-hot"==t.type?e("v-uni-image",{staticClass:"paixu",attrs:{src:"/static/images/No."+s+".png"}}):t._e()],1)],1)})),1):t._e(),"new"==t.type?e("v-uni-view",{staticClass:"goods-new"},t._l(t.list,(function(i,s){return e("router-link",{key:s,attrs:{to:{path:"/pages/goods_details/goods_details",query:{id:i.goods_id||i.id}}}},[e("v-uni-view",{staticClass:"item flex",class:[{"bg-white":t.showBg}]},[e("v-uni-view",{staticClass:"goods-img"},[e("u-image",{attrs:{width:"214rpx",height:"214rpx","border-radius":10,src:i.image}})],1),e("v-uni-view",{staticClass:"goods-info flex-1 m-l-20 flex-1"},[e("v-uni-view",{staticClass:"goods-name line-2"},[t._v(t._s(i.name))]),e("v-uni-view",{staticClass:"price m-t-20 flex row-between"},[e("v-uni-view",{staticClass:"muted xxs"},[t._v("原价"),e("price-format",{attrs:{"first-size":24,"second-size":24,"subscript-size":24,price:i.market_price}})],1),e("v-uni-view",{staticClass:"muted xxs"},[t._v(t._s(i.sales_total)+"人购买")])],1),e("v-uni-view",{staticClass:"btn flex row-between m-t-20"},[e("price-format",{staticClass:"m-r-10",attrs:{color:t.colorConfig.primary,"first-size":34,"second-size":26,"subscript-size":26,price:i.min_price,weight:500}}),e("v-uni-button",{staticClass:"bg-primary br60 white btn",attrs:{size:"xs"}},[t._v("去购买")])],1)],1)],1)],1)})),1):t._e(),"hot"==t.type?e("v-uni-view",{staticClass:"goods-hot"},t._l(t.list,(function(i,s){return e("router-link",{key:s,attrs:{to:{path:"/pages/goods_details/goods_details",query:{id:i.goods_id||i.id}}}},[e("v-uni-view",{staticClass:"item flex bg-white m-t-20"},[e("v-uni-view",{staticClass:"goods-img"},[e("u-image",{attrs:{"lazy-load":!0,width:"180rpx",height:"180rpx","border-radius":"6rpx",src:i.image}})],1),e("v-uni-view",{staticClass:"goods-info m-l-20 flex-1"},[e("v-uni-view",{staticClass:"goods-name line-2 m-b-10"},[t._v(t._s(i.name))]),e("v-uni-text",{staticClass:"sale br60 xxs"},[t._v("已有"+t._s(i.sales_total)+"人购买")]),e("v-uni-view",{staticClass:"row-between flex m-t-10"},[e("v-uni-view",{staticClass:"price m-t-10 flex"},[e("price-format",{staticClass:"m-r-10",attrs:{color:t.colorConfig.primary,"first-size":34,"second-size":26,"subscript-size":26,price:i.min_price,weight:500}}),e("price-format",{staticClass:"muted",attrs:{firstSize:24,secondSize:24,"subscript-size":24,"line-through":!0,price:i.market_price}})],1),e("v-uni-button",{staticClass:"bg-primary br60 white btn",attrs:{size:"xs"}},[t._v("立即抢购")])],1)],1),s<3?e("v-uni-image",{staticClass:"paixu",attrs:{src:"/static/images/No."+s+".png"}}):t._e()],1)],1)})),1):t._e(),"activity"==t.type?e("v-uni-view",{staticClass:"goods-hot"},t._l(t.list,(function(i,s){return e("router-link",{key:s,attrs:{to:{path:"/pages/goods_details/goods_details",query:{id:i.goods_id||i.id}}}},[e("v-uni-view",{staticClass:"item flex bg-white m-t-20"},[e("v-uni-view",{staticClass:"goods-img"},[e("u-image",{attrs:{"lazy-load":!0,width:"180rpx",height:"180rpx","border-radius":"6rpx",src:i.image}})],1),e("v-uni-view",{staticClass:"goods-info m-l-20 flex-1"},[e("v-uni-view",{staticClass:"goods-name line-2 m-b-10"},[t._v(t._s(i.name))]),e("v-uni-text",{staticClass:"views br60 xxs"},[t._v(t._s(i.views)+"浏览量")]),e("v-uni-view",{staticClass:"row-between flex m-t-10"},[e("v-uni-view",{staticClass:"price m-t-10 flex"},[e("price-format",{staticClass:"m-r-10",attrs:{color:t.colorConfig.primary,"first-size":34,"second-size":26,"subscript-size":26,price:i.price,weight:500}}),e("price-format",{staticClass:"muted",attrs:{firstSize:24,secondSize:24,"subscript-size":24,"line-through":!0,price:i.market_price}})],1),e("v-uni-button",{staticClass:"bg-primary br60 white btn",attrs:{size:"xs"}},[t._v("立即抢购")])],1)],1)],1)],1)})),1):t._e()],1)},o=[]},"1e2e":function(t,i,e){var s=e("3096");s.__esModule&&(s=s.default),"string"===typeof s&&(s=[[t.i,s,""]]),s.locals&&(t.exports=s.locals);var a=e("4f06").default;a("ccafd518",s,!0,{sourceMap:!1,shadowMode:!1})},"2f08":function(t,i,e){var s=e("7e15");s.__esModule&&(s=s.default),"string"===typeof s&&(s=[[t.i,s,""]]),s.locals&&(t.exports=s.locals);var a=e("4f06").default;a("bdb7f0cc",s,!0,{sourceMap:!1,shadowMode:!1})},3096:function(t,i,e){var s=e("24fb");i=s(!1),i.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.u-image[data-v-1bf07c9a]{position:relative;transition:opacity .5s ease-in-out}.u-image__image[data-v-1bf07c9a]{width:100%;height:100%}.u-image__loading[data-v-1bf07c9a], .u-image__error[data-v-1bf07c9a]{position:absolute;top:0;left:0;width:100%;height:100%;display:flex;flex-direction:row;align-items:center;justify-content:center;background-color:#f3f4f6;color:#909399;font-size:%?46?%}',""]),t.exports=i},"3ab4":function(t,i,e){"use strict";e.r(i);var s=e("4f01"),a=e.n(s);for(var o in s)["default"].indexOf(o)<0&&function(t){e.d(i,t,(function(){return s[t]}))}(o);i["default"]=a.a},"3bcf":function(t,i,e){"use strict";e("7a82"),Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0;var s={props:{type:{type:String,default:"double"},list:{type:Array,default:function(){return[]}},isBargain:{type:Boolean,default:!1},width:{type:String,default:"347rpx"},showBg:{type:Boolean,default:!0}},data:function(){return{}}};i.default=s},"3d0b":function(t,i,e){"use strict";e.r(i);var s=e("3bcf"),a=e.n(s);for(var o in s)["default"].indexOf(o)<0&&function(t){e.d(i,t,(function(){return s[t]}))}(o);i["default"]=a.a},"3f30":function(t,i,e){"use strict";e.r(i);var s=e("4219"),a=e.n(s);for(var o in s)["default"].indexOf(o)<0&&function(t){e.d(i,t,(function(){return s[t]}))}(o);i["default"]=a.a},4219:function(t,i,e){"use strict";e("7a82"),Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0,e("a9e3");var s={name:"u-image",props:{src:{type:String,default:""},mode:{type:String,default:"aspectFill"},width:{type:[String,Number],default:"100%"},height:{type:[String,Number],default:"auto"},shape:{type:String,default:"square"},borderRadius:{type:[String,Number],default:0},lazyLoad:{type:Boolean,default:!0},showMenuByLongpress:{type:Boolean,default:!0},loadingIcon:{type:String,default:"photo"},errorIcon:{type:String,default:"error-circle"},showLoading:{type:Boolean,default:!0},showError:{type:Boolean,default:!0},fade:{type:Boolean,default:!0},webp:{type:Boolean,default:!1},duration:{type:[String,Number],default:500},bgColor:{type:String,default:"#f3f4f6"}},data:function(){return{isError:!1,loading:!0,opacity:1,durationTime:this.duration,backgroundStyle:{}}},watch:{src:{immediate:!0,handler:function(t){t?this.isError=!1:(this.isError=!0,this.loading=!1)}}},computed:{wrapStyle:function(){var t={};return t.width=this.$u.addUnit(this.width),t.height=this.$u.addUnit(this.height),t.borderRadius="circle"==this.shape?"50%":this.$u.addUnit(this.borderRadius),t.overflow=this.borderRadius>0?"hidden":"visible",this.fade&&(t.opacity=this.opacity,t.transition="opacity ".concat(Number(this.durationTime)/1e3,"s ease-in-out")),t}},methods:{onClick:function(){this.$emit("click")},onErrorHandler:function(t){this.loading=!1,this.isError=!0,this.$emit("error",t)},onLoadHandler:function(){var t=this;if(this.loading=!1,this.isError=!1,this.$emit("load"),!this.fade)return this.removeBgColor();this.opacity=0,this.durationTime=0,setTimeout((function(){t.durationTime=t.duration,t.opacity=1,setTimeout((function(){t.removeBgColor()}),t.durationTime)}),50)},removeBgColor:function(){this.backgroundStyle={backgroundColor:"transparent"}}}};i.default=s},"4f01":function(t,i,e){"use strict";e("7a82"),Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0,e("a9e3"),e("acd8");var s={data:function(){return{priceSlice:{}}},components:{},props:{firstSize:{type:[String,Number],default:28},secondSize:{type:[String,Number],default:28},color:{type:String},weight:{type:[String,Number],default:400},price:{type:[String,Number],default:""},showSubscript:{type:Boolean,default:!0},subscriptSize:{type:[String,Number],default:28},lineThrough:{type:Boolean,default:!1}},created:function(){this.priceFormat()},watch:{price:function(t){this.priceFormat()}},methods:{priceFormat:function(){var t=this.price,i={};null!==t&&""!==t&&void 0!==t?(t=parseFloat(t),t=String(t).split("."),i.first=t[0],i.second=t[1],this.priceSlice=i):this.priceSlice={first:0}}}};i.default=s},6944:function(t,i,e){var s=e("24fb");i=s(!1),i.push([t.i,".price-format[data-v-0a5a34e0]{font-family:Avenir,SourceHanSansCN,PingFang SC,Arial,Hiragino Sans GB,Microsoft YaHei,sans-serif}",""]),t.exports=i},"7e15":function(t,i,e){var s=e("24fb");i=s(!1),i.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.goods-list .store-tag[data-v-2b46a432]::before{content:"自营";font-size:%?22?%;color:#fff;padding:0 %?10?%;background:linear-gradient(267deg,#ff2c3c,#f52e99);border-radius:%?6?%}.goods-list .goods-waterfall .item[data-v-2b46a432]{width:%?347?%;border-radius:%?10?%;overflow:hidden}.goods-list .goods-waterfall .item .goods-info[data-v-2b46a432]{padding:%?10?%}.goods-list .goods-double .item[data-v-2b46a432]{width:%?347?%;border-radius:%?10?%;overflow:hidden}.goods-list .goods-double .item .goods-info[data-v-2b46a432]{padding:%?10?%}.goods-list .goods-double .item .goods-info .goods-name[data-v-2b46a432]{height:%?80?%;line-height:%?40?%}.goods-list .goods-one .item[data-v-2b46a432]{padding:%?20?%}.goods-list .goods-one .item[data-v-2b46a432]:not(:last-of-type){margin-bottom:%?20?%}.goods-list .goods-seckill .item[data-v-2b46a432]{padding:%?20?%}.goods-list .goods-new .item[data-v-2b46a432]{padding:0 %?20?% %?20?%;border-radius:%?10?%}.goods-list .goods-row .item[data-v-2b46a432]{position:relative;width:%?240?%;border-radius:%?16?%;overflow:hidden}.goods-list .goods-row .item[data-v-2b46a432]:not(:last-of-type){margin-right:%?20?%}.goods-list .goods-row .item .goods-info[data-v-2b46a432]{padding:%?10?%}.goods-list .goods-hot .item[data-v-2b46a432]{position:relative;padding:%?30?% %?20?%;border-radius:%?10?%}.goods-list .goods-hot .item .goods-info[data-v-2b46a432]{width:%?450?%}.goods-list .goods-hot .item .goods-info .sale[data-v-2b46a432]{padding:%?4?% %?18?%;color:#f79c0c;background-color:rgba(247,156,12,.1)}.goods-list .goods-hot .item .goods-info .views[data-v-2b46a432]{padding:%?4?% %?18?%;color:#ff2c3c;background-color:rgba(237,83,73,.1)}.goods-list .goods-row .paixu[data-v-2b46a432],\n.goods-list .goods-hot .paixu[data-v-2b46a432]{position:absolute;top:0;left:%?20?%;width:%?48?%;height:%?60?%}',""]),t.exports=i},8158:function(t,i,e){"use strict";var s=e("e6f3"),a=e.n(s);a.a},a272:function(t,i,e){"use strict";e.r(i);var s=e("e2ba"),a=e("3ab4");for(var o in a)["default"].indexOf(o)<0&&function(t){e.d(i,t,(function(){return a[t]}))}(o);e("8158");var r=e("f0c5"),n=Object(r["a"])(a["default"],s["b"],s["c"],!1,null,"0a5a34e0",null,!1,s["a"],void 0);i["default"]=n.exports},b18c:function(t,i,e){"use strict";var s=e("2f08"),a=e.n(s);a.a},c529:function(t,i,e){"use strict";var s=e("1e2e"),a=e.n(s);a.a},c574:function(t,i,e){"use strict";e.r(i);var s=e("1996"),a=e("3d0b");for(var o in a)["default"].indexOf(o)<0&&function(t){e.d(i,t,(function(){return a[t]}))}(o);e("b18c");var r=e("f0c5"),n=Object(r["a"])(a["default"],s["b"],s["c"],!1,null,"2b46a432",null,!1,s["a"],void 0);i["default"]=n.exports},e2ba:function(t,i,e){"use strict";e.d(i,"b",(function(){return s})),e.d(i,"c",(function(){return a})),e.d(i,"a",(function(){}));var s=function(){var t=this,i=t.$createElement,e=t._self._c||i;return e("v-uni-text",{class:(t.lineThrough?"line-through":"")+" price-format",style:{color:t.color,"font-weight":t.weight}},[t.showSubscript?e("v-uni-text",{style:{"font-size":t.subscriptSize+"rpx","margin-right":"2rpx"}},[t._v("¥")]):t._e(),e("v-uni-text",{style:{"font-size":t.firstSize+"rpx","margin-right":"1rpx"}},[t._v(t._s(t.priceSlice.first))]),t.priceSlice.second?e("v-uni-text",{style:{"font-size":t.secondSize+"rpx"}},[t._v("."+t._s(t.priceSlice.second))]):t._e()],1)},a=[]},e6f3:function(t,i,e){var s=e("6944");s.__esModule&&(s=s.default),"string"===typeof s&&(s=[[t.i,s,""]]),s.locals&&(t.exports=s.locals);var a=e("4f06").default;a("66e034c8",s,!0,{sourceMap:!1,shadowMode:!1})},f743:function(t,i,e){"use strict";e.d(i,"b",(function(){return a})),e.d(i,"c",(function(){return o})),e.d(i,"a",(function(){return s}));var s={uIcon:e("6976").default},a=function(){var t=this,i=t.$createElement,e=t._self._c||i;return e("v-uni-view",{staticClass:"u-image",style:[t.wrapStyle,t.backgroundStyle],on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.onClick.apply(void 0,arguments)}}},[t.isError?t._e():e("v-uni-image",{staticClass:"u-image__image",style:{borderRadius:"circle"==t.shape?"50%":t.$u.addUnit(t.borderRadius)},attrs:{src:t.src,mode:t.mode,"lazy-load":t.lazyLoad},on:{error:function(i){arguments[0]=i=t.$handleEvent(i),t.onErrorHandler.apply(void 0,arguments)},load:function(i){arguments[0]=i=t.$handleEvent(i),t.onLoadHandler.apply(void 0,arguments)}}}),t.showLoading&&t.loading?e("v-uni-view",{staticClass:"u-image__loading",style:{borderRadius:"circle"==t.shape?"50%":t.$u.addUnit(t.borderRadius),backgroundColor:this.bgColor}},[t.$slots.loading?t._t("loading"):e("u-icon",{attrs:{name:t.loadingIcon,width:t.width,height:t.height}})],2):t._e(),t.showError&&t.isError&&!t.loading?e("v-uni-view",{staticClass:"u-image__error",style:{borderRadius:"circle"==t.shape?"50%":t.$u.addUnit(t.borderRadius)}},[t.$slots.error?t._t("error"):e("u-icon",{attrs:{name:t.errorIcon,width:t.width,height:t.height}})],2):t._e()],1)},o=[]},f919:function(t,i,e){"use strict";e.r(i);var s=e("f743"),a=e("3f30");for(var o in a)["default"].indexOf(o)<0&&function(t){e.d(i,t,(function(){return a[t]}))}(o);e("c529");var r=e("f0c5"),n=Object(r["a"])(a["default"],s["b"],s["c"],!1,null,"1bf07c9a",null,!1,s["a"],void 0);i["default"]=n.exports}}]);