<?php

namespace <PERSON><PERSON><PERSON>\Support\Test\Core;

use ReflectionClass;
use ReflectionException;
use <PERSON><PERSON>zong\Support\OS;
use PHPUnit\Framework\TestCase;

/**
 * Class OSTest
 *
 * @package Songshenzong\Support\Test\Core
 */
class OSTest extends TestCase
{

    /**
     * @throws ReflectionException
     */
    public function testGetsHomeDirectoryForWindowsUser()
    {
        putenv('HOME=');
        putenv('HOMEDRIVE=C:');
        putenv('HOMEPATH=\\Users\\Support');
        $ref    = new ReflectionClass(OS::class);
        $method = $ref->getMethod('getHomeDirectory');
        $method->setAccessible(true);
        $this->assertEquals('C:\\Users\\<USER>