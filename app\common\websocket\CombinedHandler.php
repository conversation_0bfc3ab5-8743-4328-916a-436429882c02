<?php

namespace app\common\websocket;

use app\common\enum\ChatMsgEnum;
use app\common\model\Admin;
use app\common\server\UrlServer;
use app\common\utils\Redis;
use app\common\websocket\GlobalWebSocketManager;
use Swoole\Server;
use Swoole\Websocket\Frame;
use think\App;
use think\Event;
use think\Request;
use think\swoole\Websocket;
use think\swoole\websocket\Room;
use think\facade\Db;
use think\facade\Log;

/**
 * 组合WebSocket处理类
 * 同时处理客服功能和管理员通知功能
 */
class CombinedHandler extends Websocket
{
    protected $server;
    protected $room;
    protected $parser;
    protected $cache;
    protected $prefix;
    protected $adminPrefix;

    public function __construct(App $app, Server $server, Room $room, Event $event, Parser $parser, Redis $redis)
    {
        $this->server = $server;
        $this->room = $room;
        $this->parser = $parser;
        $this->cache = $redis;
        $this->prefix = config('default.websocket_prefix');
        $this->adminPrefix = config('default.websocket_prefix') . 'admin_';
        parent::__construct($app, $server, $room, $event);
    }

    /**
     * WebSocket连接建立时的处理函数
     * @param int $fd 连接标识符
     * @param Request $request 请求对象
     * @return bool|mixed|void
     */
    public function onOpen($fd, Request $request)
    {
        try {
            // 记录所有接收到的参数
            $allParams = $request->get();
            Log::info("WebSocket连接打开: fd={$fd}, 参数=" . json_encode($allParams));

            // 获取请求参数
            $type = $request->get('type/s', ''); // user, kefu, admin

            // 根据连接类型处理不同的逻辑
            if ($type === 'admin') {
                return $this->handleAdminOpen($fd, $request);
            } else {
                return $this->handleChatOpen($fd, $request);
            }
        } catch (\Throwable $e) {
            Log::error("WebSocket onOpen错误: fd={$fd}, error=" . $e->getMessage() . "\n" . $e->getTraceAsString());

            // 向客户端发送错误消息
            try {
                $this->pushData($fd, 'error', [
                    'msg' => '连接错误: ' . $e->getMessage()
                ]);
            } catch (\Throwable $pushError) {
                Log::error("发送错误消息失败: " . $pushError->getMessage());
            }

            return $this->server->close($fd);
        }
    }

    /**
     * 处理管理员通知连接
     */
    protected function handleAdminOpen($fd, Request $request)
    {
        try {
            // 获取请求参数
            $admin_id = $request->get('admin_id/d', 0);
            $nickname = $request->get('nickname/s', '管理员');
            $token = $request->get('token/s', '');
            $client = $request->get('client/d', 1); // 添加client参数，默认为1表示管理员客户端

            // 记录详细日志
            Log::info("管理员WebSocket连接参数: fd={$fd}, admin_id={$admin_id}, nickname={$nickname}, token=" . (empty($token) ? '空' : '已设置') . ", client={$client}");

            // 验证参数
            if (empty($admin_id)) {
                $errorMsg = "管理员WebSocket连接参数缺失: fd={$fd}, admin_id={$admin_id}";
                Log::error($errorMsg);

                // 向客户端发送错误消息
                $this->pushData($fd, 'error', [
                    'msg' => '连接参数缺失，请提供管理员ID',
                    'missing_params' => ['admin_id']
                ]);

                return $this->server->close($fd);
            }

            // 绑定连接
            $bindResult = $this->bindAdminFd($admin_id, $nickname, $fd);
            Log::info("管理员绑定结果: fd={$fd}, admin_id={$admin_id}, result=" . ($bindResult ? '成功' : '失败'));

            // 发送连接成功消息
            return $this->pushData($fd, 'login', [
                'msg' => '管理员连接成功',
                'admin_id' => $admin_id,
                'nickname' => $nickname,
                'client' => $client, // 添加client参数到响应
                'timestamp' => time()
            ]);
        } catch (\Throwable $e) {
            Log::error("管理员WebSocket连接错误: fd={$fd}, error=" . $e->getMessage() . "\n" . $e->getTraceAsString());

            // 向客户端发送详细错误消息
            try {
                $this->pushData($fd, 'error', [
                    'msg' => '连接失败: ' . $e->getMessage()
                ]);
            } catch (\Throwable $pushError) {
                Log::error("发送错误消息失败: " . $pushError->getMessage());
            }

            return $this->server->close($fd);
        }
    }

    /**
     * 处理客服/用户聊天连接
     */
    protected function handleChatOpen($fd, Request $request)
    {
        // 获取请求参数
        $token = $request->get('token/s'); // 客服或用户token
        $type = $request->get('type/s'); // user, kefu, user_chat (用户对用户聊天)
        $client = $request->get('client/d');
        $shop_id = $request->get('shop_id/d', 0); //当前对话的商家id

        // 记录详细的参数信息
        Log::info("WebSocket连接参数: fd={$fd}, type={$type}, token=" . (empty($token) ? '空' : '已设置') . ", client={$client}, shop_id={$shop_id}");

        // 验证必要参数
        if (empty($token) || empty($type) || empty($client)) {
            $missingParams = [];
            if (empty($token)) $missingParams[] = 'token';
            if (empty($type)) $missingParams[] = 'type';
            if (empty($client)) $missingParams[] = 'client';

            $errorMsg = "WebSocket连接参数缺失: " . implode(', ', $missingParams);
            Log::error($errorMsg);

            // 向客户端发送错误消息
            $this->pushData($fd, 'error', [
                'msg' => '连接参数缺失，请提供必要的参数',
                'missing_params' => $missingParams
            ]);

            return $this->server->close($fd);
        }

        try {
            $user = $this->triggerEvent('login', ['client' => $client, 'token' => $token, 'type' => $type]);

            if ($user['code'] == 20001 || empty($user['data']['id'])) {
                throw new \Exception(empty($user['msg']) ? "未知错误" : $user['msg']);
            }
        } catch (\Throwable $e) {
            Log::error("客服WebSocket连接错误: fd={$fd}, error=" . $e->getMessage());

            // 向客户端发送详细错误消息
            $this->pushData($fd, 'error', [
                'msg' => '连接失败: ' . $e->getMessage()
            ]);

            return $this->server->close($fd);
        }

        // 登录者绑定fd
        $this->bindFd($type, $user['data'], $fd, $shop_id);

        // 如果是用户类型，注册到全局管理器（支持多种聊天类型）
        if ($type === 'user') {
            $globalManager = GlobalWebSocketManager::getInstance();
            $globalManager->registerUserConnection(
                $user['data']['id'],
                $fd,
                $type,
                [
                    'nickname' => $user['data']['nickname'],
                    'avatar' => $user['data']['avatar']
                ]
            );
        }

        $this->ping($fd);

        return $this->pushData($fd, 'login', [
            'msg' => '连接成功',
            'msg_type' => ChatMsgEnum::TYPE_TEXT
        ]);
    }

    /**
     * 接收WebSocket消息的处理函数
     * @param Frame $frame 消息帧
     * @return bool|mixed|void
     */
    public function onMessage(Frame $frame)
    {
        try {
            $param = $this->parser->decode($frame->data);

            // 处理心跳包
            if ('ping' === $param['event']) {
                return $this->ping($frame->fd);
            }

            // 获取连接信息
            $connInfo = $this->getDataByFd($frame->fd);

            // 如果是管理员连接，处理管理员通知逻辑
            if (!empty($connInfo) && $connInfo['type'] === 'admin') {
                // 处理通知事件
                if ('notification' === $param['event'] || 'admin_notification' === $param['event']) {
                    Log::info("收到管理员通知事件: " . json_encode($param));
                    // 直接推送给所有管理员
                    $adminFds = $this->room->getClients('admin_group');
                    Log::info("管理员组 ('admin_group') 中的客户端: " . json_encode($adminFds));
                    if (!empty($adminFds)) {
                        Log::info("准备推送通知给 " . count($adminFds) . " 个管理员连接。");
                        return $this->pushData($adminFds, 'notification', $param['data']);
                    } else {
                        Log::warning("在 'admin_group' 房间中没有找到任何客户端。通知无法发送。");
                    }
                    return true;
                }
            }

            // 处理其他事件
            $param['handle'] = $this;
            $param['fd'] = $frame->fd;

            // 检查事件名称是否有效
            if (empty($param['event']) || !is_string($param['event'])) {
                Log::error("WebSocket消息事件名称无效: fd={$frame->fd}, event=" . json_encode($param['event']) . ", 原始数据=" . $frame->data);
                return $this->pushData($frame->fd, 'error', [
                    'msg' => '消息格式错误：事件名称无效',
                    'msg_type' => ChatMsgEnum::TYPE_TEXT
                ]);
            }

            return $this->triggerEvent($param['event'], $param);
        } catch (\Throwable $e) {
            Log::error('WebSocket消息处理错误: ' . $e->getMessage() . "\n" . $e->getTraceAsString());
            return $this->pushData($frame->fd, 'error', [
                'msg' => $e->getMessage(),
                'msg_type' => ChatMsgEnum::TYPE_TEXT
            ]);
        }
    }

    /**
     * WebSocket连接关闭时的处理函数
     * @param int $fd 连接标识符
     * @param int $reactorId 反应堆ID
     */
    public function onClose($fd, $reactorId)
    {
        try {
            // 获取连接信息
            $connInfo = $this->getDataByFd($fd);

            // 根据连接类型处理不同的关闭逻辑
            if (!empty($connInfo) && $connInfo['type'] === 'admin') {
                // 管理员连接关闭
                Log::info("管理员WebSocket连接关闭: fd={$fd}");
                // 使用try-catch包装removeAdminBind调用，确保即使出错也能继续执行
                try {
                    $this->removeAdminBind($fd);
                } catch (\Throwable $e) {
                    Log::error("移除管理员绑定失败: fd={$fd}, error=" . $e->getMessage());
                }
            } else {
                // 客服/用户连接关闭
                try {
                    $this->triggerEvent('close', ['handle' => $this, 'fd' => $fd]);
                } catch (\Throwable $e) {
                    Log::error("触发关闭事件失败: fd={$fd}, error=" . $e->getMessage());
                }

                try {
                    // 如果是用户类型，从全局管理器移除
                    $connInfo = $this->getDataByFd($fd);
                    if (!empty($connInfo) && $connInfo['type'] === 'user') {
                        $globalManager = GlobalWebSocketManager::getInstance();
                        $globalManager->removeUserConnection($connInfo['uid'], $fd);
                    }

                    $this->removeBind($fd);
                } catch (\Throwable $e) {
                    Log::error("移除绑定失败: fd={$fd}, error=" . $e->getMessage());
                }
            }

            // 确保连接关闭
            if ($this->server->exist($fd)) {
                $this->server->close($fd);
            }
        } catch (\Throwable $e) {
            Log::error("WebSocket连接关闭处理失败: fd={$fd}, error=" . $e->getMessage());
            // 确保连接关闭，即使出现异常
            if ($this->server->exist($fd)) {
                $this->server->close($fd);
            }
        }
    }

    /**
     * 触发事件
     * @param string $event 事件名称
     * @param array $data 数据
     * @return mixed
     */
    public function triggerEvent(string $event, array $data)
    {
        return $this->event->until('swoole.websocket.' . $event, $data);
    }

    /**
     * 登录者的id绑定fd (客服/用户)
     */
    public function bindFd($type, $user, $fd, $shop_id)
    {
        $uid = $user['id'];

        // socket_fd_{fd} => ['uid' => {uid}, 'type' => {type}]
        // 以fd为键缓存当前fd的信息
        $fdKey = $this->prefix . 'fd_' . $fd;
        $fdData = [
            'uid' => $uid,
            'type' => $type,
            'nickname' => $user['nickname'],
            'avatar' => UrlServer::getFileUrl($user['avatar']),
            'client' => $user['client'],
            'shop_id' => $shop_id
        ];

        $this->cache->set($fdKey, json_encode($fdData, true));

        // socket_user_1(user_id) => {fd} 用户userid为1 的 fd
        // socket_kefu_2(kefu_id) => {fd} 客服kefu_id为2 的 fd
        $uidKey = $this->prefix . $type . '_' . $uid;
        $this->cache->sadd($uidKey, $fd);

        // socket_user => {fd} 在线用户的所有fd
        if ($type == 'kefu') {
            $groupKey = $this->prefix . 'shop_' . $shop_id . '_kefu';
        } else {
            // 用户类型统一加入user组，支持多种聊天功能
            $groupKey = $this->prefix . 'user';
        }
        $this->cache->sadd($groupKey, $uid);
    }

    /**
     * 绑定管理员与连接标识符
     */
    protected function bindAdminFd($admin_id, $nickname, $fd)
    {
        // 保存fd对应的管理员信息
        $fd_key = $this->adminPrefix . 'fd_' . $fd;
        $admin_data = [
            'uid' => $admin_id,
            'nickname' => $nickname,
            'type' => 'admin',
            'fd' => $fd
        ];
        $this->cache->set($fd_key, json_encode($admin_data), 86400);

        // 保存管理员ID对应的fd
        $admin_key = $this->adminPrefix . 'admin_' . $admin_id;
        $this->cache->set($admin_key, $fd, 86400);

        // 加入管理员组
        $this->room->add($fd, 'admin_group');

        Log::info("管理员绑定成功: admin_id={$admin_id}, nickname={$nickname}, fd={$fd}");
        return true;
    }

    /**
     * 移除绑定 (客服/用户)
     */
    public function removeBind($fd)
    {
        try {
            $data = $this->getDataByFd($fd);
            if ($data) {
                $key = $this->prefix . 'user';
                if($data['type'] == 'kefu') {
                    $key = $this->prefix . 'shop_'. $data['shop_id'] . '_kefu';
                }

                // 添加错误处理
                try {
                    $this->cache->srem($key, $data['uid']); // socket_user => 11
                } catch (\Throwable $e) {
                    Log::error("移除用户/客服组成员失败: fd={$fd}, key={$key}, uid={$data['uid']}, error=" . $e->getMessage());
                }

                try {
                    $this->cache->srem($this->prefix . $data['type'] . '_' . $data['uid'], $fd); // socket_user_uid => fd
                } catch (\Throwable $e) {
                    Log::error("移除用户/客服连接失败: fd={$fd}, type={$data['type']}, uid={$data['uid']}, error=" . $e->getMessage());
                }
            }

            // 删除fd对应的信息
            try {
                $this->cache->del($this->prefix . 'fd_' . $fd);
            } catch (\Throwable $e) {
                Log::error("删除连接信息失败: fd={$fd}, error=" . $e->getMessage());
            }

            return true;
        } catch (\Throwable $e) {
            Log::error("移除绑定失败: fd={$fd}, error=" . $e->getMessage());
            return false;
        }
    }

    /**
     * 移除管理员与连接标识符的绑定
     */
    protected function removeAdminBind($fd)
    {
        try {
            // 获取fd对应的管理员信息
            $fd_key = $this->adminPrefix . 'fd_' . $fd;
            $admin_data = $this->cache->get($fd_key);

            if (!empty($admin_data)) {
                $admin_data = json_decode($admin_data, true);
                $admin_id = $admin_data['uid'] ?? 0;

                // 删除管理员ID对应的fd
                if (!empty($admin_id)) {
                    $admin_key = $this->adminPrefix . 'admin_' . $admin_id;
                    $this->cache->del($admin_key);
                }
            }

            // 删除fd对应的管理员信息
            $this->cache->del($fd_key);

            // 从管理员组中移除 - 添加错误处理
            try {
                $this->room->delete($fd, 'admin_group');
            } catch (\Throwable $e) {
                // 记录错误但不中断流程
                Log::error("从管理员组移除连接失败: fd={$fd}, error=" . $e->getMessage());
                // 如果是内存分配错误，记录更详细的信息
                if (strpos($e->getMessage(), 'unable to allocate memory') !== false) {
                    Log::error("Swoole\Table内存分配失败，请考虑增加配置值或重启服务");
                }
            }

            return true;
        } catch (\Throwable $e) {
            Log::error("移除管理员绑定失败: fd={$fd}, error=" . $e->getMessage());
            return false;
        }
    }

    /**
     * 通过登录id和登录类型获取对应的fd
     */
    public function getFdByUid($uid, $type)
    {
        $key = $this->prefix . $type . '_' . $uid;
        return $this->cache->sMembers($key);
    }

    /**
     * 根据管理员ID获取连接标识符
     */
    public function getFdByAdminId($admin_id)
    {
        $admin_key = $this->adminPrefix . 'admin_' . $admin_id;
        return $this->cache->get($admin_key);
    }

    /**
     * 根据fd获取登录的id和登录类型
     */
    public function getDataByFd($fd)
    {
        // 先尝试从客服/用户缓存中获取
        $key = $this->prefix . 'fd_' . $fd;
        $result = $this->cache->get($key);

        if (empty($result)) {
            // 如果没有找到，尝试从管理员缓存中获取
            $key = $this->adminPrefix . 'fd_' . $fd;
            $result = $this->cache->get($key);
        }

        if (!empty($result)) {
            $result = json_decode($result, true);
        }
        return $result;
    }

    /**
     * 根据连接标识符获取管理员信息
     */
    public function getAdminByFd($fd)
    {
        $fd_key = $this->adminPrefix . 'fd_' . $fd;
        $result = $this->cache->get($fd_key);
        if (!empty($result)) {
            $result = json_decode($result, true);
        }
        return $result;
    }

    /**
     * 发送心跳包
     */
    public function ping($fd)
    {
        $data = $this->getDataByFd($fd);
        if (!empty($data)) {
            if ($data['type'] === 'admin') {
                return $this->pushData($fd, 'pong', [
                    'server_time' => time(),
                    'client_time' => time()
                ]);
            } else {
                return $this->pushData($fd, 'ping', ['client_time' => time()]);
            }
        }
        return true;
    }

    /**
     * 推送数据
     */
    public function pushData($fd, $event, $data)
    {
        $data = $this->parser->encode($event, $data);

        // fd非数组时转为数组
        if (!is_array($fd)) {
            $fd = [$fd];
        }

        // 向fd发送消息
        foreach ($fd as $item) {
            try {
                if ($this->server->exist($item)) {
                    // 使用Swoole\WebSocket\Server的push方法
                    if (method_exists($this->server, 'push')) {
                        $this->server->push($item, $data);
                    } else {
                        // 如果push方法不存在，尝试使用send方法
                        if (method_exists($this->server, 'send')) {
                            $this->server->send($item, $data);
                        } else {
                            Log::error("无法发送消息: 服务器对象不支持push或send方法");
                        }
                    }
                }
            } catch (\Throwable $e) {
                Log::error("推送消息失败: fd={$item}, error=" . $e->getMessage());
            }
        }
        return true;
    }

    /**
     * 在线fd
     */
    public function onlineFd($fd)
    {
        $result = [];

        if (empty($fd)) {
            return $result;
        }

        if (!is_array($fd)) {
            $fd = [$fd];
        }

        foreach ($fd as $item) {
            if ($this->server->exist($item)) {
                $result[] = $item;
            }
        }

        return $result;
    }

    /**
     * 发送通知给所有管理员
     */
    public function sendNotificationToAdmin($title, $content, $type = 'admin_notification', $url = '', $icon = 0)
    {
        try {
            // 构建通知数据
            $notificationData = [
                'type' => $type,
                'title' => $title,
                'content' => $content,
                'url' => $url,
                'icon' => $icon,
                'timestamp' => time()
            ];

            // 获取所有管理员的连接
            $adminFds = $this->room->getClients('admin_group');

            if (empty($adminFds)) {
                Log::warning("没有管理员在线，无法发送通知");
                return false;
            }

            Log::info("准备向" . count($adminFds) . "个管理员连接发送通知: " . json_encode($notificationData));

            // 发送通知
            return $this->pushData($adminFds, 'notification', $notificationData);
        } catch (\Throwable $e) {
            Log::error("发送管理员通知失败: " . $e->getMessage() . "\n" . $e->getTraceAsString());
            return false;
        }
    }
}
