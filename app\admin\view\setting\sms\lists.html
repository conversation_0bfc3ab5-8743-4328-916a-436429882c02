{layout name="layout1" /}
<style>
    .layui-table-cell {
        height: auto;
    }
</style>
<div class="wrapper">
    <div class="layui-card">
        <!--操作提示-->
        <div class="layui-card-body">
            <div class="layui-collapse like-layui-collapse" lay-accordion="" style="border:1px dashed #c4c4c4">
                <div class="layui-colla-item">
                    <h2 class="layui-colla-title like-layui-colla-title" style="background-color: #fff">操作提示</h2>
                    <div class="layui-colla-content layui-show">
                        *短信渠道配置，
                        <a href="https://cn.aliyun.com" target="_blank" style="color: rgb(64,115,250)">阿里云</a>、
                        <a href="https://cloud.tencent.com" target="_blank" style="color: rgb(64,115,250)">腾讯云</a>请前往申请短信服务。
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-tab layui-tab-card" lay-filter="tab-all">
            <ul class="layui-tab-title">
                <li data-type="1" class="layui-this">短信配置</li>
                <li data-type="2">发送记录</li>
            </ul>
            <!--搜索内容-->
            <div class="layui-card-body layui-form">
                <div class="layui-form-item search">
                        <div class="layui-inline">
                            <label class="layui-form-label">短信标题:</label>
                            <div class="layui-input-block">
                                <input type="text" name="name" id="name" placeholder="请输入短信标题" autocomplete="off"
                                       class="layui-input">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">发送手机号:</label>
                            <div class="layui-input-block">
                                <input type="text" name="mobile" id="mobile" placeholder="请输入手机号码" autocomplete="off"
                                       class="layui-input">
                            </div>
                        </div>

                        <div class="layui-inline">
                            <label class="layui-form-label">发送状态:</label>
                            <div class="layui-input-block">
                                <select name="send_status" id="send_status">
                                    <option value="">全部</option>
                                    {foreach $status_list as $item => $val}
                                    <option value="{$item}">{$val}</option>
                                    {/foreach}
                                </select>
                            </div>
                        </div>
                        <div class="layui-row">
                            <div class="layui-inline">
                                <label class="layui-form-label">创建时间:</label>
                                <div class="layui-input-inline">
                                    <div class="layui-input-inline">
                                        <input type="text" name="start_time" class="layui-input" id="start_time"
                                               placeholder="" autocomplete="off">
                                    </div>
                                </div>
                                <div class="layui-input-inline" style="margin-right: 5px;width: 20px;">
                                    <label class="layui-form-mid">至</label>
                                </div>
                                <div class="layui-input-inline">
                                    <input type="text" name="end_time" class="layui-input" id="end_time"
                                           placeholder="" autocomplete="off">
                                </div>
                            </div>

                            <div class="layui-inline">
                                <button class="layui-btn layui-btn-sm layuiadmin-btn-ad {$view_theme_color}" lay-submit
                                        lay-filter="sms-search">查询
                                </button>
                                <button class="layui-btn layui-btn-sm layuiadmin-btn-ad layui-btn-primary " lay-submit
                                        lay-filter="sms-clear-search">清空查询
                                </button>
                            </div>
                        </div>
                    </div>
            </div>
            <!--列表-->
            <div class="layui-tab-item layui-show">
                <div class="layui-card">
                    <div class="layui-card-body">
                        <table id="like-table-lists" lay-filter="like-table-lists"></table>
                        <script type="text/html" id="table-status">
                            {{#  if(d.status){ }}
                                开启
                            {{#  } else { }}
                                关闭
                            {{#  } }}
                        </script>
                        <script type="text/html" id="table-operation-config">
                            <a class="layui-btn layui-btn-normal layui-btn-sm"  lay-event="config">配置</a>
                        </script>
                        <script type="text/html" id="table-operation">
                            <a class="layui-btn layui-btn-primary layui-btn-sm" lay-event="detail">详情</a>
                        </script>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


<script>
    layui.use(['table', 'laydate', 'element', 'table'], function () {

        var $ = layui.$
            , form = layui.form
            , table = layui.table
            , element = layui.element
            , laydate = layui.laydate
            , type = 1;

        //监听搜索
        form.on('submit(sms-search)', function (data) {
            var field = data.field;
            table.reload('like-table-lists', {
                where: field, page: {curr: 1}
            });
        });

        //清空查询
        form.on('submit(sms-clear-search)', function () {
            $('#name').val('');
            $('#mobile').val('');
            $('#send_status').val('');
            $('#start_time').val('');
            $('#end_time').val('');
            form.render('select');
            //刷新列表
            table.reload('like-table-lists', {
                where: [], page: {curr: 1}
            });
        });

        getList();
        $('.search').hide();

        element.on('tab(tab-all)', function (data) {
            type = $(this).attr('data-type');
            $('.search').hide();
            if (type == 2) {
                $('.search').show();
            }
            getList();
        });

        //日期时间范围
        laydate.render({
            elem: '#start_time'
            , type: 'datetime'
        });

        laydate.render({
            elem: '#end_time'
            , type: 'datetime'
        });

        function getList() {
            var page = false;
            var url = '{:url("setting.Sms/lists")}';
            var cols = [
                {type: 'numbers', title: '序号', width: 60}
                , {field: 'name', title: '短信通道', align: 'center'}
                , {field: 'status', title: '状态', align: 'center', templet: '#table-status'}
                , {title: '操作', width: 250, align: 'center', fixed: 'right', toolbar: '#table-operation-config'}
            ];
            if (type == 2) {
                url = '{:url("setting.Sms/logLists")}';
                cols = [
                    {type: 'numbers', title: '序号', width: 60}
                    , {field: 'scene_name', title: '短信标题', align: 'center'}
                    , {field: 'content', title: '短信内容', align: 'center'}
                    , {field: 'mobile', title: '发送手机号', align: 'center'}
                    , {field: 'send_status', title: '发送状态', align: 'center'}
                    , {field: 'send_time', title: '发送时间', align: 'center'}
                    , {field: 'create_time', title: '创建时间', align: 'center'}
                    , {title: '操作', width: 250, align: 'center', fixed: 'right', toolbar: '#table-operation'}
                ];
                page = true;
            }
            like.tableLists('#like-table-lists', url, cols, {}, page);
        }


        var active = {
            config: function(obj) {
                layer.open({
                    type: 2
                    , title: '消息设置'
                    , content: '{:url("setting.Sms/config")}?engine=' + obj.data.engine
                    , area: ['90%', '90%']
                    , btn: ['保存', '取消']
                    , yes: function (index, layero) {
                        var iframeWindow = window["layui-layer-iframe" + index];
                        var submit = layero.find("iframe").contents().find("#addSubmit");
                        iframeWindow.layui.form.on("submit(addSubmit)", function(data){
                            var field = data.field;
                            like.ajax({
                                url: '{:url("setting.Sms/config")}',
                                data: field,
                                type: "post",
                                success: function (res) {
                                    if(res.code === 1) {
                                        layui.layer.msg(res.msg,{
                                            offset: '15px'
                                            , icon: 1
                                            , time: 1000
                                        });
                                        layer.close(index);
                                        table.reload("like-table-lists", {
                                            where: {},
                                            page: { cur: 1 }
                                        });
                                    }
                                }
                            });
                        });
                        submit.trigger('click');
                    }
                })
            },
            detail: function(obj) {
                layer.open({
                    type: 2
                    , title: '短信详情'
                    , content: '{:url("setting.Sms/detail")}?id=' + obj.data.id
                    , area: ['90%', '90%']
                })
            }
        };
        like.eventClick(active);
    });
</script>