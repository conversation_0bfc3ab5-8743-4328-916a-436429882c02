{layout name="layout1" /}

<style>
.inspection-container {
    width: 100%;
    margin: 0;
    padding: 15px;
    background: #f5f5f5;
    min-height: 100vh;
    box-sizing: border-box;
}

.inspection-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 15px 20px;
    margin: 0;
    position: relative;
    border-radius: 12px 12px 0 0;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.inspection-header h2 {
    margin: 0;
    font-size: 18px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;
}

.inspection-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.close-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    transition: all 0.3s ease;
}

.close-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.inspection-body {
    background: white;
    border-radius: 0 0 12px 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.1);
    overflow: hidden;
}

.form-section {
    padding: 25px;
    border-bottom: 1px solid #f0f0f0;
}

.form-section:last-child {
    border-bottom: none;
}

.section-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #f0f0f0;
    position: relative;
}

.section-title::before {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 40px;
    height: 2px;
    background: #667eea;
}

.upload-area {
    border: 2px dashed #d9d9d9;
    border-radius: 12px;
    padding: 30px 20px;
    text-align: center;
    background: #fafafa;
    transition: all 0.3s ease;
    min-height: 120px;
    display: block;
    width: 100%;
    box-sizing: border-box;
}

.upload-area:hover {
    border-color: #667eea;
    background: #f0f2ff;
}

.upload-btn {
    background: #667eea;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 10px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.upload-btn:hover {
    background: #5a6fd8;
    transform: translateY(-1px);
}



.readonly-field {
    background: #f5f5f5 !important;
    border: 1px solid #e6e6e6 !important;
    border-radius: 8px !important;
}

/* 为所有输入框添加圆角 */
.layui-input,
.layui-textarea,
.layui-select {
    border-radius: 8px !important;
}

/* 为按钮添加圆角 */
.layui-btn {
    border-radius: 8px !important;
}

.status-badge {
    display: inline-block;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    box-shadow: 0 2px 6px rgba(0,0,0,0.1);
}

.status-pending {
    background: #fff7e6;
    color: #fa8c16;
    border: 1px solid #ffd591;
}

.status-approved {
    background: #f6ffed;
    color: #52c41a;
    border: 1px solid #b7eb8f;
}

.status-rejected {
    background: #fff2f0;
    color: #ff4d4f;
    border: 1px solid #ffccc7;
}

.status-none {
    background: #f5f5f5;
    color: #999;
    border: 1px solid #d9d9d9;
}

.audit-section {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 20px;
    margin-top: 15px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.audit-buttons {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
}

.editor-container {
    border: 1px solid #e6e6e6;
    border-radius: 12px;
    overflow: hidden;
    min-height: 200px;
    box-shadow: 0 2px 6px rgba(0,0,0,0.05);
}

/* 上传组件样式优化 */
.like-upload-image,
.like-upload-video {
    width: 100%;
    margin-top: 15px;
}

.like-upload-image .upload-image-elem,
.like-upload-video .upload-video-elem {
    text-align: center;
    width: 100%;
    height: 100%;
}

.upload-image-div,
.upload-video-div {
    display: inline-block;
    margin-right: 15px;
    margin-bottom: 15px;
    position: relative;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    transition: all 0.3s ease;
}

.upload-image-div img,
.upload-video-div video {
    width: 150px;
    height: 120px;
    object-fit: cover;
    display: block;
    border-radius: 12px;
}

.del-upload-btn {
    position: absolute;
    top: 8px;
    right: 8px;
    background: rgba(255, 0, 0, 0.8);
    color: white;
    border: none;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    cursor: pointer;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    z-index: 10;
    line-height: 1;
}

.del-upload-btn:hover {
    background: rgba(255, 0, 0, 1);
    transform: scale(1.1);
}

/* 修复上传后的图片和视频显示 */
.upload-image-div:hover,
.upload-video-div:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0,0,0,0.15);
}

/* 确保上传区域的容器样式 */
.upload-container {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    align-items: flex-start;
}

/* 清除浮动 */
.upload-container::after {
    content: "";
    display: table;
    clear: both;
}

/* 上传按钮容器 */
.upload-area .upload-btn {
    margin: 0 auto;
    display: inline-block;
}

.upload-area .upload-image-elem,
.upload-area .upload-video-elem {
    display: block;
    width: 100%;
    text-align: center;
    margin: 0;
    padding: 0;
}

/* 强制重置上传组件的默认样式 */
.like-upload-image,
.like-upload-video {
    display: block !important;
    width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
}

.upload-image-elem,
.upload-video-elem {
    display: block !important;
    width: 100% !important;
    text-align: center !important;
    margin: 0 !important;
    padding: 0 !important;
}

/* 确保上传按钮居中 */
.upload-area .upload-btn {
    display: inline-block !important;
    margin: 0 auto !important;
    vertical-align: middle;
}

/* 修复视频控件样式 */
.upload-video-div video {
    background: #000;
}

/* 强制修复上传区域的布局问题 */
.form-section .upload-area {
    clear: both;
    overflow: hidden;
    position: relative;
}

.form-section .like-upload-image,
.form-section .like-upload-video {
    float: none !important;
    clear: both !important;
    position: static !important;
    transform: none !important;
}

/* 覆盖可能的第三方样式 */
.inspection-container .upload-area * {
    box-sizing: border-box;
}

.inspection-container .upload-btn {
    position: relative !important;
    left: auto !important;
    right: auto !important;
    top: auto !important;
    bottom: auto !important;
    transform: none !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .inspection-container {
        padding: 0;
    }

    .form-section {
        padding: 15px;
    }

    .upload-image-div,
    .upload-video-div {
        margin-right: 10px;
        margin-bottom: 10px;
    }

    .upload-image-div img,
    .upload-video-div video {
        width: 120px;
        height: 100px;
    }

    .layui-col-md6,
    .layui-col-md4 {
        width: 100% !important;
        margin-bottom: 15px;
    }

    .inspection-header {
        padding: 12px 15px;
    }

    .inspection-header h2 {
        font-size: 16px;
    }
}
</style>

<div class="inspection-container">
    <div class="inspection-header">
        <h2><i class="layui-icon layui-icon-survey"></i> 商家实地检验管理</h2>
        <button type="button" class="close-btn" onclick="parent.layer.closeAll();">
            <i class="layui-icon layui-icon-close"></i>
        </button>
    </div>

    <div class="inspection-body">
        <div class="layui-form">
            <input type="hidden" name="shop_id" value="{$shop_id}">
            <input type="hidden" name="inspection_id" value="{$inspection.id|default=''}">

            <!-- 基本信息 -->
            <div class="form-section">
                <div class="section-title">基本信息</div>

                <div class="layui-row layui-col-space15">
                    <div class="layui-col-md6">
                        <div class="layui-form-item">
                            <label class="layui-form-label">商家名称</label>
                            <div class="layui-input-block">
                                <input type="text" name="shop_name" value="{$shop_name|default=''}" readonly class="layui-input readonly-field">
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-md6">
                        <div class="layui-form-item">
                            <label class="layui-form-label">检验人员</label>
                            <div class="layui-input-block">
                                <input type="text" name="staff_name" value="{$inspection.staff_name|default=''}" placeholder="请输入检验人员姓名" class="layui-input" {if !empty($inspection) && $inspection.status == 0}readonly{/if}>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 检验图片 -->
            <div class="form-section">
                <div class="section-title">检验图片</div>

                {if !empty($inspection.images)}
                <div class="upload-container">
                    {foreach $inspection.images as $image}
                    <div class="upload-image-div">
                        <img src="{$image}" alt="检验图片">
                        <input type="hidden" name="images[]" value="{$image}">
                        {if empty($inspection) || $inspection.status != 0}
                        <button type="button" class="del-upload-btn">×</button>
                        {/if}
                    </div>
                    {/foreach}
                </div>
                {/if}

                {if empty($inspection) || $inspection.status != 0}
                <div class="upload-area">
                    <div class="like-upload-image">
                        <div class="upload-image-elem">
                            <button type="button" class="upload-btn add-upload-image" id="upload_images" style="width: 155px;height: 50px;">
                                <i class="layui-icon layui-icon-upload" ></i>
                                上传检验图片
                            </button>
                            <div style="margin-top: 10px; color: #999; font-size: 12px;">
                                支持 JPG、PNG、GIF 格式，最多上传5张
                            </div>
                        </div>
                    </div>
                </div>
                {/if}
            </div>

            <!-- 检验视频 -->
            <div class="form-section">
                <div class="section-title">检验视频</div>

                {if !empty($inspection.videos)}
                <div class="upload-container">
                    {foreach $inspection.videos as $video}
                    <div class="upload-video-div">
                        <video src="{$video}" controls></video>
                        <input type="hidden" name="videos[]" value="{$video}">
                        {if empty($inspection) || $inspection.status != 0}
                        <button type="button" class="del-upload-btn">×</button>
                        {/if}
                    </div>
                    {/foreach}
                </div>
                {/if}

                {if empty($inspection) || $inspection.status != 0}
                <div class="upload-area">
                    <div class="like-upload-video">
                        <div class="upload-video-elem" id="upload_videos">
                            <button type="button" class="upload-btn">
                                <i class="layui-icon layui-icon-video"></i>
                                上传检验视频
                            </button>
                            <div style="margin-top: 10px; color: #999; font-size: 12px;">
                                支持 MP4、AVI、MOV 格式，最多上传5个
                            </div>
                        </div>
                    </div>
                </div>
                {/if}
            </div>

            <!-- 检验说明 -->
            <div class="form-section">
                <div class="section-title">检验说明</div>

                {if !empty($inspection) && $inspection.status == 0}
                <div class="layui-form-item">
                    <div class="layui-input-block">
                        <div style="padding: 15px; background: #f5f5f5; border-radius: 6px; border: 1px solid #e6e6e6; min-height: 100px;">
                            {$inspection.description|default='暂无检验说明'}
                        </div>
                    </div>
                </div>
                {else}
                <div class="layui-form-item">
                    <div class="layui-input-block">
                        <div class="editor-container">
                            <textarea id="description" name="description" class="layui-textarea" style="border: none; resize: none;">{$inspection.description|default=''}</textarea>
                        </div>
                    </div>
                </div>
                {/if}
            </div>

            <!-- 时间信息 -->
            <div class="form-section">
                <div class="section-title">时间信息</div>

                <div class="layui-row layui-col-space15">
                    <div class="layui-col-md4">
                        <div class="layui-form-item">
                            <label class="layui-form-label">创建时间</label>
                            <div class="layui-input-block">
                                <input type="text" name="create_time" value="{if !empty($inspection.create_time)}{$inspection.create_time|date='Y-m-d H:i:s'}{/if}" readonly class="layui-input readonly-field">
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-md4">
                        <div class="layui-form-item">
                            <label class="layui-form-label">更新时间</label>
                            <div class="layui-input-block">
                                <input type="text" name="update_time" value="{if !empty($inspection.update_time)}{$inspection.update_time|date='Y-m-d H:i:s'}{/if}" readonly class="layui-input readonly-field">
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-md4">
                        <div class="layui-form-item">
                            <label class="layui-form-label">审核时间</label>
                            <div class="layui-input-block">
                                <input type="text" name="audit_time" value="{if !empty($inspection.audit_time)}{$inspection.audit_time|date='Y-m-d H:i:s'}{/if}" readonly class="layui-input readonly-field">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 状态信息 -->
            <div class="form-section">
                <div class="section-title">状态信息</div>

                <div class="layui-row layui-col-space15">
                    <div class="layui-col-md6">
                        <div class="layui-form-item">
                            <label class="layui-form-label">审核人</label>
                            <div class="layui-input-block">
                                <input type="text" name="audit_user" value="{$inspection.audit_user|default=''}" readonly class="layui-input readonly-field">
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-md6">
                        <div class="layui-form-item">
                            <label class="layui-form-label">检验状态</label>
                            <div class="layui-input-block" style="padding-top: 9px;">
                                {if !empty($inspection)}
                                    {if $inspection.status == 0}
                                        <span class="status-badge status-pending">待审核</span>
                                    {elseif $inspection.status == 1}
                                        <span class="status-badge status-approved">已通过</span>
                                    {else}
                                        <span class="status-badge status-rejected">未通过</span>
                                    {/if}
                                {else}
                                    <span class="status-badge status-none">未检验</span>
                                {/if}
                            </div>
                        </div>
                    </div>
                </div>

                {if !empty($inspection) && $inspection.status == 2}
                <div class="layui-form-item">
                    <label class="layui-form-label">未通过原因</label>
                    <div class="layui-input-block">
                        <div style="padding: 15px; background: #fff2f0; border-radius: 6px; border: 1px solid #ffccc7; color: #ff4d4f;">
                            {$inspection.reason|default='暂无原因说明'}
                        </div>
                    </div>
                </div>
                {/if}
            </div>

            <!-- 审核操作 -->
            {if !empty($inspection) && $inspection.status == 0}
            <div class="form-section">
                <div class="section-title">审核操作</div>

                <div class="audit-section">
                    <div class="audit-buttons">
                        <button type="button" class="layui-btn layui-btn-normal" id="audit_pass">
                            <i class="layui-icon layui-icon-ok"></i> 审核通过
                        </button>
                        <button type="button" class="layui-btn layui-btn-danger" id="audit_reject">
                            <i class="layui-icon layui-icon-close"></i> 审核拒绝
                        </button>
                    </div>

                    <div class="layui-form-item" id="reject_reason_container" style="display:none;">
                        <label class="layui-form-label">拒绝原因</label>
                        <div class="layui-input-block">
                            <textarea name="reject_reason" id="reject_reason" class="layui-textarea" placeholder="请输入审核拒绝的原因" style="min-height: 80px;"></textarea>
                        </div>
                    </div>
                </div>
            </div>
            {/if}

            <div class="layui-form-item" style="display:none;">
                <button type="submit" id="inspectionSubmit" lay-submit lay-filter="inspectionSubmit">提交</button>
            </div>
        </div>
    </div>
</div>

<script>
    layui.config({
        base: "/static/lib/"
    }).extend({
        layEditor: "layui/layeditor/index"
    }).use(["form", "layer", "layEditor"], function(){
        var form = layui.form;
        var layer = layui.layer;
        var layEditor = layui.layEditor;

        // 初始化富文本编辑器（仅在可编辑状态下）
        var ieditor = null;
        {if empty($inspection) || $inspection.status != 0}
        layEditor.set({
            uploadImage: {
                url: '{:url("Upload/image")}'
            }
        });
        ieditor = layEditor.build('description');
        {/if}

        // 删除上传文件功能
        like.delUpload();

        // 图片上传
        $(document).on("click", ".add-upload-image", function () {
            like.imageUpload({
                limit: 5,
                field: "images[]",
                that: $(this),
                content: '{:url("file/lists")}?type=10'
            });
        });
  // 商品视频
        $(document).on("click", "#upload_videos .upload-btn", function () {
            like.videoUpload({
                limit: 5,
                field: "videos[]",
                that: $(this),
                content: '/admin/file/videoList'
            });
        })

        // 视频上传
        // $(document).on("click", "#upload_videos .upload-btn", function () {
        //     like.videoUpload('#upload_videos', '{:url("Upload/video")}');
        // });

        // 表单提交
        form.on("submit(inspectionSubmit)", function(data){
            if (ieditor) {
                data.field['description'] = layEditor.getContent(ieditor);
            }
            return true;
        });

        // 审核通过
        $(document).on('click', '#audit_pass', function(){
            layer.confirm('确认审核通过吗？', {icon: 3, title:'提示'}, function(index){
                var shop_id = $('input[name="shop_id"]').val();
                var inspection_id = $('input[name="inspection_id"]').val();

                like.ajax({
                    url: "{:url('shop.Store/auditInspection')}",
                    data: {
                        shop_id: shop_id,
                        inspection_id: inspection_id,
                        status: 1
                    },
                    type: "POST",
                    success: function(res) {
                        if (res.code === 1) {
                            layer.msg('审核通过成功', {icon: 1});
                            setTimeout(function(){
                                parent.layer.closeAll();
                                parent.layui.table.reload("like-table-lists", {
                                    where: {},
                                    page: { cur: 1 }
                                });
                            }, 1000);
                        } else {
                            layer.msg(res.msg, {icon: 2});
                        }
                    }
                });
                layer.close(index);
            });
        });

        // 审核拒绝
        $(document).on('click', '#audit_reject', function(){
            $('#reject_reason_container').show();
            layer.confirm('确认审核拒绝吗？', {icon: 3, title:'提示'}, function(index){
                var reason = $('#reject_reason').val();
                if(!reason){
                    layer.msg('请输入拒绝原因', {icon: 2});
                    return;
                }

                var shop_id = $('input[name="shop_id"]').val();
                var inspection_id = $('input[name="inspection_id"]').val();

                like.ajax({
                    url: "{:url('shop.Store/auditInspection')}",
                    data: {
                        shop_id: shop_id,
                        inspection_id: inspection_id,
                        status: 2,
                        reason: reason
                    },
                    type: "POST",
                    success: function(res) {
                        if (res.code === 1) {
                            layer.msg('审核拒绝成功', {icon: 1});
                            setTimeout(function(){
                                parent.layer.closeAll();
                                parent.layui.table.reload("like-table-lists", {
                                    where: {},
                                    page: { cur: 1 }
                                });
                            }, 1000);
                        } else {
                            layer.msg(res.msg, {icon: 2});
                        }
                    }
                });
                layer.close(index);
            });
        });

        // 页面加载完成后立即注册全局函数
        console.log('检验页面脚本开始执行');

        // 全局函数：设置检验数据回显（供父窗口调用）
        window.setInspectionData = function(inspectionData) {
            console.log('=== 开始检验数据回显 ===');
            console.log('接收到的检验数据:', inspectionData);
            console.log('数据类型:', typeof inspectionData);
            console.log('数据键值:', Object.keys(inspectionData || {}));

            if (!inspectionData || Object.keys(inspectionData).length === 0) {
                console.log('没有检验数据需要回显');
                return;
            }

            try {
                // 回显检验人员
                if (inspectionData.staff_name) {
                    console.log('回显检验人员:', inspectionData.staff_name);
                    $('input[name="staff_name"]').val(inspectionData.staff_name);
                }

                // 回显图片
                if (inspectionData.images && Array.isArray(inspectionData.images) && inspectionData.images.length > 0) {
                    console.log('开始回显图片，数量:', inspectionData.images.length);
                    console.log('图片列表:', inspectionData.images);

                    // 找到图片区域的容器
                    var imageSection = $('.form-section').eq(1);
                    console.log('找到图片区域:', imageSection.length);

                    var imageContainer = imageSection.find('.upload-container');
                    if (imageContainer.length === 0) {
                        console.log('创建图片容器');
                        imageContainer = $('<div class="upload-container"></div>');
                        imageSection.find('.upload-area').before(imageContainer);
                    }

                    // 清空现有图片
                    imageContainer.find('.upload-image-div').remove();

                    // 添加图片
                    inspectionData.images.forEach(function(imageUrl, index) {
                        console.log('添加图片 ' + (index + 1) + ':', imageUrl);
                        var imageHtml = '<div class="upload-image-div">' +
                            '<img src="' + imageUrl + '" alt="检验图片">' +
                            '<input type="hidden" name="images[]" value="' + imageUrl + '">' +
                            '<button type="button" class="del-upload-btn">×</button>' +
                            '</div>';
                        imageContainer.append(imageHtml);
                    });

                    console.log('图片回显完成，共' + inspectionData.images.length + '张');
                } else {
                    console.log('没有图片需要回显');
                }

                // 回显视频
                if (inspectionData.videos && Array.isArray(inspectionData.videos) && inspectionData.videos.length > 0) {
                    console.log('开始回显视频，数量:', inspectionData.videos.length);
                    console.log('视频列表:', inspectionData.videos);

                    // 找到视频区域的容器
                    var videoSection = $('.form-section').eq(2);
                    console.log('找到视频区域:', videoSection.length);

                    var videoContainer = videoSection.find('.upload-container');
                    if (videoContainer.length === 0) {
                        console.log('创建视频容器');
                        videoContainer = $('<div class="upload-container"></div>');
                        videoSection.find('.upload-area').before(videoContainer);
                    }

                    // 清空现有视频
                    videoContainer.find('.upload-video-div').remove();

                    // 添加视频
                    inspectionData.videos.forEach(function(videoUrl, index) {
                        console.log('添加视频 ' + (index + 1) + ':', videoUrl);
                        var videoHtml = '<div class="upload-video-div">' +
                            '<video src="' + videoUrl + '" controls></video>' +
                            '<input type="hidden" name="videos[]" value="' + videoUrl + '">' +
                            '<button type="button" class="del-upload-btn">×</button>' +
                            '</div>';
                        videoContainer.append(videoHtml);
                    });

                    console.log('视频回显完成，共' + inspectionData.videos.length + '个');
                } else {
                    console.log('没有视频需要回显');
                }

                // 回显检验说明
                if (inspectionData.description) {
                    console.log('回显检验说明:', inspectionData.description);
                    if (ieditor) {
                        console.log('使用富文本编辑器回显');
                        // 延迟设置富文本内容，确保编辑器已初始化
                        setTimeout(function() {
                            layEditor.setContent(ieditor, inspectionData.description);
                            console.log('富文本内容设置完成');
                        }, 100);
                    } else {
                        console.log('使用普通文本框回显');
                        $('textarea[name="description"]').val(inspectionData.description);
                    }
                    console.log('检验说明回显完成');
                } else {
                    console.log('没有检验说明需要回显');
                }

                console.log('=== 检验数据回显完成 ===');
            } catch (e) {
                console.error('检验数据回显失败:', e);
                console.error('错误堆栈:', e.stack);
            }
        };

        console.log('setInspectionData函数已注册:', typeof window.setInspectionData);
    });
</script>
