{layout name="layout1" /}
<style>
    .layui-form-label {
        width: 120px;
    }
    .layui-input-block {
        margin-left: 150px;
    }
</style>
<div class="wrapper">
    <div class="layui-card">
        <div class="layui-card-body">
            <div class="layui-collapse like-layui-collapse" lay-accordion="" style="border:1px dashed #c4c4c4">
                <div class="layui-colla-item">
                    <h2 class="layui-colla-title like-layui-colla-title" style="background-color: #fff">操作提示</h2>
                    <div class="layui-colla-content layui-show">
                        <p>*设置会员下单的积分奖励</p>
                    </div>
                </div>
            </div>
            <!-- 表单区域 -->
            <div class="layui-form" style="margin-top: 15px;">
                <div class="layui-form-item">
                    <lable class="layui-form-label">消费赠送积分：</lable>
                    <div class="layui-input-block" style="width:300px;">
                        <input type="checkbox" name="open_award" lay-skin="switch" lay-text="ON|OFF" {if $open_award == 1}checked{/if}>
                    </div>
                </div>
                <div class="layui-form-item">
                    <lable class="layui-form-label">赠送积分事件：</lable>
                    <div class="layui-input-block" style="width:300px;">
                        <select name="award_event" lay-verify="custom_required" verify-msg="请选择">
                            <option value="">请选择</option>
                            {foreach $award_event_lists as $key=>$val}
                            <option value="{$key}" {if $award_event == $key}selected{/if}>{$val}</option>
                            {/foreach}
                        </select>
                    </div>
                </div>
                <div class="layui-form-item">
                    <lable class="layui-form-label">赠送积分比率：</lable>
                    <div class="layui-input-block" style="width:300px;">
                        <input type="number" name="award_ratio" class="layui-input" style="display: inline-block;width: 100px" value="{$award_ratio}">
                        <span style="">%</span>
                        <div class="layui-form-mid layui-word-aux" style="float: unset">比率必须为整数，例：当设置为100%时，100元=100积分</div>
                    </div>
                </div>

                <div class="layui-form-item">
                    <lable class="layui-form-label"></lable>
                    <div class="layui-input-block">
                        <button class="layui-btn layui-btn-normal" lay-submit lay-filter="set">确定</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    layui.config({
        version:"{$front_version}",
        base: '/static/lib/' //静态资源所在路径
    }).use(['form','element'], function(){
        var $ = layui.$,form = layui.form,element = layui.element;


        form.on('submit(set)', function(data) {
            like.ajax({
                url:'{:url("setting.MarketingConfig/orderAward")}',
                data: data.field,
                type:"post",
                success:function(res)
                {
                    if(res.code == 1)
                    {
                        layui.layer.msg(res.msg, {
                            offset: '15px'
                            , icon: 1
                            , time: 1000
                        });
                    }
                }
            });
        });

    });
</script>