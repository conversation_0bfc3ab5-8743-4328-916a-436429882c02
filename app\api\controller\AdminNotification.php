<?php

namespace app\api\controller;

use app\common\service\AdminNotificationService;
use think\facade\Request;
use think\facade\Log;
use app\common\basics\Api;
use app\common\server\JsonServer;
/**
 * 管理员通知API控制器
 */
class AdminNotification  extends Api
{
    /**
     * 不需要登录验证的方法
     * @var array
     */
    public $like_not_need_login = [
        'testNotification',
        'sendSystemNotification',
        'sendErrorNotification',
        'sendBatchNotifications',
        'getNotificationHistory',
        'getNotificationStats',
        'getPendingNotifications',
        'cleanExpiredNotifications',
        'sendQuickNotification'
    ];
    /**
     * 发送测试通知
     * @return \think\Response
     */
    public function testNotification()
    {
        try {
            $title = Request::get('title', '测试通知');
            $content = Request::get('content', '这是一条测试通知消息');
            $type = Request::get('type', 'admin');
            $url = Request::get('url', '');
            $icon = Request::get('icon/d', null);

            $result = AdminNotificationService::sendNotification($title, $content, $type, $url, $icon);

            if ($result) {
                return json([
                    'code' => 1,
                    'msg' => '测试通知发送成功',
                    'data' => [
                        'title' => $title,
                        'content' => $content,
                        'type' => $type,
                        'sent_at' => date('Y-m-d H:i:s')
                    ]
                ]);
            } else {
                return json([
                    'code' => 0,
                    'msg' => '测试通知发送失败',
                    'data' => null
                ]);
            }

        } catch (\Throwable $e) {
            Log::error('测试通知API异常: ' . $e->getMessage());
            return json([
                'code' => 0,
                'msg' => '发送失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 发送系统通知
     * @return \think\Response
     */
    public function sendSystemNotification()
    {
        try {
            $data = Request::post();
            
            $title = $data['title'] ?? '系统通知';
            $content = $data['content'] ?? '';
            $url = $data['url'] ?? '';

            if (empty($content)) {
                return json([
                    'code' => 0,
                    'msg' => '通知内容不能为空',
                    'data' => null
                ]);
            }

            $result = AdminNotificationService::sendSystemNotification($title, $content, $url);

            return json([
                'code' => $result ? 1 : 0,
                'msg' => $result ? '系统通知发送成功' : '系统通知发送失败',
                'data' => $result ? [
                    'title' => $title,
                    'content' => $content,
                    'type' => 'system_notification',
                    'sent_at' => date('Y-m-d H:i:s')
                ] : null
            ]);

        } catch (\Throwable $e) {
            Log::error('发送系统通知API异常: ' . $e->getMessage());
            return json([
                'code' => 0,
                'msg' => '发送失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 发送错误通知
     * @return \think\Response
     */
    public function sendErrorNotification()
    {
        try {
            $data = Request::post();
            
            $title = $data['title'] ?? '错误警报';
            $content = $data['content'] ?? '';
            $url = $data['url'] ?? '';

            if (empty($content)) {
                return json([
                    'code' => 0,
                    'msg' => '通知内容不能为空',
                    'data' => null
                ]);
            }

            $result = AdminNotificationService::sendErrorNotification($title, $content, $url);

            return json([
                'code' => $result ? 1 : 0,
                'msg' => $result ? '错误通知发送成功' : '错误通知发送失败',
                'data' => $result ? [
                    'title' => $title,
                    'content' => $content,
                    'type' => 'error_notification',
                    'sent_at' => date('Y-m-d H:i:s')
                ] : null
            ]);

        } catch (\Throwable $e) {
            Log::error('发送错误通知API异常: ' . $e->getMessage());
            return json([
                'code' => 0,
                'msg' => '发送失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 批量发送通知
     * @return \think\Response
     */
    public function sendBatchNotifications()
    {
        try {
            $data = Request::post();
            $notifications = $data['notifications'] ?? [];

            if (empty($notifications) || !is_array($notifications)) {
                return json([
                    'code' => 0,
                    'msg' => '通知列表不能为空',
                    'data' => null
                ]);
            }

            $results = AdminNotificationService::sendBatchNotifications($notifications);
            $successCount = count(array_filter($results));
            $totalCount = count($results);

            return json([
                'code' => 1,
                'msg' => "批量发送完成，成功 {$successCount}/{$totalCount} 条",
                'data' => [
                    'total' => $totalCount,
                    'success' => $successCount,
                    'failed' => $totalCount - $successCount,
                    'results' => $results,
                    'sent_at' => date('Y-m-d H:i:s')
                ]
            ]);

        } catch (\Throwable $e) {
            Log::error('批量发送通知API异常: ' . $e->getMessage());
            return json([
                'code' => 0,
                'msg' => '批量发送失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 获取通知历史记录
     * @return \think\Response
     */
    public function getNotificationHistory()
    {
        try {
            $date = Request::get('date', date('Y-m-d'));
            
            // 验证日期格式
            if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $date)) {
                return json([
                    'code' => 0,
                    'msg' => '日期格式错误，请使用 Y-m-d 格式',
                    'data' => null
                ]);
            }

            $history = AdminNotificationService::getNotificationHistory($date);

            return json([
                'code' => 1,
                'msg' => '获取成功',
                'data' => [
                    'date' => $date,
                    'count' => count($history),
                    'notifications' => $history
                ]
            ]);

        } catch (\Throwable $e) {
            Log::error('获取通知历史API异常: ' . $e->getMessage());
            return json([
                'code' => 0,
                'msg' => '获取失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 获取通知统计信息
     * @return \think\Response
     */
    public function getNotificationStats()
    {
        try {
            $date = Request::get('date', date('Y-m-d'));
            
            // 验证日期格式
            if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $date)) {
                return json([
                    'code' => 0,
                    'msg' => '日期格式错误，请使用 Y-m-d 格式',
                    'data' => null
                ]);
            }

            $stats = AdminNotificationService::getNotificationStats($date);

            return json([
                'code' => 1,
                'msg' => '获取成功',
                'data' => [
                    'date' => $date,
                    'stats' => $stats
                ]
            ]);

        } catch (\Throwable $e) {
            Log::error('获取通知统计API异常: ' . $e->getMessage());
            return json([
                'code' => 0,
                'msg' => '获取失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 获取待处理的通知（用于前端轮询）
     * @return \think\Response
     */
    public function getPendingNotifications()
    {
        try {
            $notifications = AdminNotificationService::getPendingNotifications();

            return json([
                'code' => 1,
                'msg' => '获取成功',
                'data' => [
                    'count' => count($notifications),
                    'notifications' => $notifications,
                    'timestamp' => time()
                ]
            ]);

        } catch (\Throwable $e) {
            Log::error('获取待处理通知API异常: ' . $e->getMessage());
            return json([
                'code' => 0,
                'msg' => '获取失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 清理过期通知记录
     * @return \think\Response
     */
    public function cleanExpiredNotifications()
    {
        try {
            AdminNotificationService::cleanExpiredNotifications();

            return json([
                'code' => 1,
                'msg' => '清理完成',
                'data' => [
                    'cleaned_at' => date('Y-m-d H:i:s')
                ]
            ]);

        } catch (\Throwable $e) {
            Log::error('清理过期通知API异常: ' . $e->getMessage());
            return json([
                'code' => 0,
                'msg' => '清理失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 发送预定义的快速通知
     * @return \think\Response
     */
    public function sendQuickNotification()
    {
        try {
            $type = Request::get('type', 'system');
            
            $quickNotifications = [
                'system' => [
                    'title' => '系统通知',
                    'content' => '系统运行正常，所有服务已启动。',
                    'method' => 'sendSystemNotification'
                ],
                'error' => [
                    'title' => '错误警报',
                    'content' => '检测到系统异常，请立即处理！',
                    'method' => 'sendErrorNotification'
                ],
                'warning' => [
                    'title' => '警告提醒',
                    'content' => '磁盘空间不足，建议清理无用文件。',
                    'method' => 'sendWarningNotification'
                ],
                'info' => [
                    'title' => '信息提示',
                    'content' => '今日访问量已达到新高，系统表现良好。',
                    'method' => 'sendInfoNotification'
                ],
                'success' => [
                    'title' => '操作成功',
                    'content' => '数据备份已完成，系统运行稳定。',
                    'method' => 'sendSuccessNotification'
                ]
            ];

            if (!isset($quickNotifications[$type])) {
                return json([
                    'code' => 0,
                    'msg' => '不支持的通知类型',
                    'data' => [
                        'supported_types' => array_keys($quickNotifications)
                    ]
                ]);
            }

            $notification = $quickNotifications[$type];
            $method = $notification['method'];
            
            $result = AdminNotificationService::$method($notification['title'], $notification['content']);

            return json([
                'code' => $result ? 1 : 0,
                'msg' => $result ? '快速通知发送成功' : '快速通知发送失败',
                'data' => $result ? [
                    'type' => $type,
                    'title' => $notification['title'],
                    'content' => $notification['content'],
                    'sent_at' => date('Y-m-d H:i:s')
                ] : null
            ]);

        } catch (\Throwable $e) {
            Log::error('发送快速通知API异常: ' . $e->getMessage());
            return json([
                'code' => 0,
                'msg' => '发送失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }
}