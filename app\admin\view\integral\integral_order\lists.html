{layout name="layout1" /}
<style>
    .layui-table-cell {
        height:auto;
    }
    .goods-content>div:not(:last-of-type) {
        border-bottom:1px solid #DCDCDC;
    }
    .goods-data::after{
        display: block;
        content: '';
        clear: both;
    }
    .goods_name_hide{
        overflow:hidden;
        white-space:nowrap;
        text-overflow: ellipsis;
    }
    .operation-btn {
        margin: 5px;
    }
    .table-operate{
        text-align: left;
        font-size:14px;
        padding:0 5px;
        height:auto;
        overflow:visible;
        text-overflow:inherit;
        white-space:normal;
        word-break: break-all;
    }
</style>

<div class="wrapper">
    <div class="layui-card">
        <div class="layui-card-body">
        <div class="layui-collapse like-layui-collapse" lay-accordion="" style="border:1px dashed #c4c4c4">
            <div class="layui-colla-item">
                <h2 class="layui-colla-title like-layui-colla-title" style="background-color: #fff">操作提示</h2>
                <div class="layui-colla-content layui-show">
                    <p>*会员在商城下单的列表。</p>
                    <p>*订单状态有待付款，待发货，待收货，已完成，已关闭；不做售后流程。</p>
                    <p>*待付款订单取消后则为已关闭。待付款订单支付后则为待发货。待发货订单发货后则为待收货。待收货订单收货后则为已完成。红包类型的订单一付款就变成已完成状态。</p>
                </div>
            </div>
        </div>
        </div>
        <div class="layui-tab layui-tab-card" lay-filter="tab-all">

            <ul class="layui-tab-title">
                <li data-type='' class="layui-this">全部</li>
                {foreach $order_status as $item => $val}
                <li data-type="{$item}">{$val}</li>
                {/foreach}
            </ul>
            <div class="layui-card-body layui-form">
                <div class="layui-form-item">
                    <div class="layui-row">
                        <div class="layui-inline">
                            <label class="layui-form-label">兑换单号:</label>
                            <div class="layui-input-block">
                                <input type="text" name="order_sn" id="order_sn" placeholder="请输入兑换单号"
                                       autocomplete="off" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">商品名称:</label>
                            <div class="layui-input-block">
                                <input type="text" name="goods_name" id="goods_name" placeholder="请输入商品名称"
                                       autocomplete="off" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">兑换类型:</label>
                            <div class="layui-input-block">
                                <select name="type" id="type">
                                    <option value="">全部</option>
                                    {foreach $type as $item => $val}
                                    <option value="{$item}">{$val}</option>
                                    {/foreach}
                                </select>
                            </div>
                        </div>

                        <div class="layui-inline">
                            <label class="layui-form-label">订单状态:</label>
                            <div class="layui-input-block">
                                <select name="order_status" id="order_status">
                                    <option value="">全部</option>
                                    {foreach $order_status as $item => $val}
                                    <option value="{$item}">{$val}</option>
                                    {/foreach}
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="layui-row">

                        <div class="layui-inline">
                            <label class="layui-form-label">下单时间:</label>
                            <div class="layui-input-inline">
                                <div class="layui-input-inline">
                                    <input type="text" name="start_time" class="layui-input" id="start_time"
                                           placeholder="" autocomplete="off">
                                </div>
                            </div>
                            <div class="layui-input-inline" style="margin-right: 5px;width: 20px;">
                                <label class="layui-form-mid">至</label>
                            </div>
                            <div class="layui-input-inline">
                                <input type="text" name="end_time" class="layui-input" id="end_time"
                                       placeholder="" autocomplete="off">
                            </div>
                        </div>

                        <div class="layui-inline">
                            <button class="layui-btn layui-btn-sm layuiadmin-btn-ad {$view_theme_color}" lay-submit
                                    lay-filter="order-search">查询
                            </button>
                            <button class="layui-btn layui-btn-sm layuiadmin-btn-ad layui-btn-primary " lay-submit
                                    lay-filter="order-clear-search">清空查询
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-tab-item layui-show">
                <div class="layui-card">
                    <div class="layui-card-body">
                        <table id="order-lists" lay-filter="order-lists"></table>

                        <script type="text/html" id="order-operation" >
                            <div class="table-operate">

                                <a class="layui-btn layui-btn-primary layui-btn-sm operation-btn" lay-event="detail">订单详情</a>
                                {{#  if(d.order_status == 1 && d.delivery_way == 1){ }}
                                <a class="layui-btn layui-btn-normal layui-btn-sm operation-btn" lay-event="delivery">发货</a>
                                {{#  } }}
                                {{#  if(d.order_status == 2 && d.delivery_way == 1 ){ }}
                                <a class="layui-btn layui-btn-normal layui-btn-sm operation-btn" lay-event="confirm">确认收货</a>
                                {{#  } }}
                                {{#  if(d.order_status == 2 && d.shipping_status == 1 ){ }}
                                <a class="layui-btn layui-btn-primary layui-btn-sm operation-btn" lay-event="express">物流查询</a>
                                {{#  } }}
                                {{#  if(d.order_status == 3 && d.shipping_status == 1 ){ }}
                                <a class="layui-btn layui-btn-primary layui-btn-sm operation-btn" lay-event="express">物流查询</a>
                                {{#  } }}
                                {{#  if(d.order_status <= 2 && d.exchange_type == 1){ }}
                                <a class="layui-btn layui-btn-danger layui-btn-sm operation-btn" lay-event="cancel">取消订单</a>
                                {{#  } }}
                            </div>
                        </script>

                        <script type="text/html" id="image">
                            <img src="{{d.image}}" style="height:80px;width: 80px" class="image-show">
                        </script>

                        <!--订单信息-->
                        <script type="text/html" id="order">
                            <div style="text-align: left">
                                <p>订单编号:{{d.order_sn}}</p>
                                <p>下单时间:{{d.create_time}}</p>
                                <p>支付时间:{{d.pay_time}}</p>
                            </div>
                        </script>

                        <!--会员信息-->
                        <script type="text/html" id="user">
                            <img src="{{d.user.avatar}}" style="height:80px;width: 80px" class="image-show">
                            <div class="layui-input-inline"  style="text-align: left;">
                                <p>会员编号:{{d.user.sn}}</p>
                                <p style="width: 180px;text-overflow:ellipsis;overflow: hidden">用户昵称:{{d.user.nickname}}</p>
                            </div>
                        </script>

                        <!--收货信息-->
                        <script type="text/html" id="delivery">
                            <div style="text-align: left">
                                <p>收货人:{{d.consignee}}</p>
                                <p>手机号码:{{d.mobile}}</p>
                                <p>收货地址:{{d.delivery_address}}</p>
                            </div>
                        </script>

                        <!--商品信息-->
                        <script type="text/html" id="goods">
                            <div class="goods-content">
                                <div style="text-align: left;" class="goods-data">
                                    <img src="{{d.goods_snap.image}}" style="height:80px;width: 80px;" class="image-show layui-col-md4">
                                    <div class="layui-input-inline layui-col-md8" style="margin-left: 10px">
                                        <span class="layui-col-md7 goods_name_hide">{{d.goods_snap.name}}</span>
                                        <br>
                                        <span class="layui-col-md7 goods_name_hide" style="width: 100%">积分金额：{{d.goods_snap.need_integral}}积分{{# if(d.goods_snap.exchange_way == 2){ }}+{{d.goods_snap.need_money}}元{{#  } }}</span>
                                        <br>
                                        <span class="layui-col-md5">数量：{{d.total_num}}</span>
                                    </div>
                                </div>
                            </div>
                        </script>

                        <!--实际支付-->
                        <script type="text/html" id="order_amount">
                            <div style="text-align: left">
                                <p>{{d.order_integral}}积分{{# if(d.order_amount > 0){ }}+{{d.order_amount}}元{{#  } }}</p>
                            </div>
                        </script>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
      layui.config({
        version:"{$front_version}",
        base: '/static/lib/' //静态资源所在路径
    }).use(['form'], function(){
        var $ = layui.$
            , form = layui.form
            , table = layui.table
            , element = layui.element
            , laydate = layui.laydate;

        //图片放大
        $(document).on('click', '.image-show', function () {
            var src = $(this).attr('src');
            like.showImg(src,400);
        });

        //监听搜索
        form.on('submit(order-search)', function (data) {
            var field = data.field;
            //执行重载
            table.reload('order-lists', {
                where: field,
                page: {
                    curr: 1
                }
            });
        });
        //清空查询
        form.on('submit(order-clear-search)', function () {
            $('#order_sn').val('');
            $('#goods_name').val('');
            $('#type').val('');
            $('#order_status').val('');
            $('#start_time').val('');
            $('#end_time').val('');
            form.render('select');
            //刷新列表
            table.reload('order-lists', {
                where: [],
                page: {
                    curr: 1
                }
            });
        });

        //日期时间范围
        laydate.render({
            elem: '#start_time'
            ,type: 'datetime'
            ,trigger: 'click'
            ,done: function (value, date, endDate) {
                var startDate = new Date(value).getTime();
                var endTime = new Date($('#end_time').val()).getTime();
                if (endTime < startDate) {
                    layer.msg('结束时间不能小于开始时间');
                    $('#start_time').val($('#end_time').val());
                }
            }
        });


        laydate.render({
            elem: '#end_time'
            ,type: 'datetime'
            ,trigger: 'click'
            ,done: function (value, date, endDate) {
                var startDate = new Date($('#start_time').val()).getTime();
                var endTime = new Date(value).getTime();
                console.log(startDate);
                if (endTime < startDate) {
                    layer.msg('结束时间不能小于开始时间');
                    $('#end_time').val($('#start_time').val());
                }
            }
        });

        //获取列表
        getList('');
        //切换列表
        element.on('tab(tab-all)', function (data) {
            $('#order_sn').val('');
            $('#goods_name').val('');
            $('#type').val('');
            $('#order_status').val('');
            $('#start_time').val('');
            $('#end_time').val('');
            form.render('select');
            var status = $(this).attr('data-type');
            getList(status);
        });

        function getList(status) {
            table.render({
                elem: '#order-lists'
                , url: '{:url("integral.IntegralOrder/lists")}?status=' + status
                , cols: [[
                    {field: 'order', title: '兑换单号', align: 'center',templet:'#order',width:230}
                    , {field: 'order_goods', title: '商品信息', align: 'center',templet:'#goods',width:350}
                    , {field: 'type_desc', title: '兑换类型', align: 'center',width:100}
                    , {field: 'user', title: '会员信息', templet:'#user',width:300}
                    , {field: 'order_amount', title: '实际支付', align: 'center',templet:'#order_amount',width:150}
                    , {field: 'delivery', title: '收货信息', align: 'center',templet:'#delivery',width:200}
                    , {field: 'order_status_desc', title: '订单状态', align: 'center',width:100}
                    , {fixed: 'right', title: '操作', width: 300, align: 'center', toolbar: '#order-operation'}
                ]]
                , page: true
                , text: {none: '暂无数据！'}
                ,response: {
                    statusCode: 1 
                  } 
                , parseData: function (res) {
                    return {
                        "code": res.code,
                        "msg": res.msg,
                        "count": res.data.count,
                        "data": res.data.lists,
                    };
                }
                ,done: function(res, curr, count){
                    // 解决操作栏因为内容过多换行问题
                    $(".layui-table-main tr").each(function (index, val) {
                        $($(".layui-table-fixed-l .layui-table-body tbody tr")[index]).height($(val).height());
                        $($(".layui-table-fixed-r .layui-table-body tbody tr")[index]).height($(val).height());
                    });
                }
            });
        }

        //监听工具条
        table.on('tool(order-lists)', function (obj) {
            var id = obj.data.id;
            //订单详情
            if(obj.event === 'detail'){
                layer.open({
                    type: 2
                    ,title: '订单详情'
                    ,content: '{:url("integral.IntegralOrder/detail")}?id='+id
                    ,area: ['90%', '90%']
                    ,yes: function(index, layero){
                        table.reload('order-lists');
                    }
                })
            }

            //发货
            if(obj.event === 'delivery'){
                layer.open({
                    type: 2
                    ,title: '订单发货'
                    ,content: '{:url("integral.IntegralOrder/delivery")}?id='+id
                    ,area: ['90%', '90%']
                    ,yes: function(index, layero){

                    }
                })
            }

            //物流信息
            if(obj.event === 'express'){
                layer.open({
                    type: 2
                    ,title: '物流信息'
                    ,content: '{:url("integral.IntegralOrder/express")}?id='+id
                    ,area: ['90%', '90%']
                    ,yes: function(index, layero){

                    }
                })
            }

            //确认收货
            if(obj.event === 'confirm'){
                layer.confirm('确认订单商品已收货吗?', {
                    btn: ['确认','取消'] //按钮
                }, function(){
                    like.ajax({
                        url: '{:url("integral.IntegralOrder/confirm")}'
                        , data: {'id': id}
                        , type: 'post'
                        , success: function (res) {
                            if (res.code == 1) {
                                layui.layer.msg(res.msg, {
                                    offset: '15px'
                                    , icon: 1
                                    , time: 1000
                                },function () {
                                    table.reload('order-lists');
                                });
                            }
                        },
                    });
                });
            }


            //取消订单
            if (obj.event === 'cancel') {
                layer.confirm('确认取消订单吗?', {
                    btn: ['确认', '取消'] //按钮
                }, function () {
                    like.ajax({
                        url: '{:url("integral.IntegralOrder/cancel")}'
                        , data: {'id': id}
                        , type: 'post'
                        , success: function (res) {
                            if (res.code == 1) {
                                layui.layer.msg(res.msg, {
                                    offset: '15px'
                                    , icon: 1
                                    , time: 1000
                                }, function () {
                                    table.reload('order-lists');
                                });
                            }
                        },
                    });
                });
            }


        });
    });
</script>