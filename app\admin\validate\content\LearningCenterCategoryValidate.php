<?php

namespace app\admin\validate\content;

use app\common\basics\Validate;

class LearningCenterCategoryValidate extends Validate
{
    protected $rule = [
        'id'      => 'require|number',
        'name'    => 'require|max:50',
        'sort'    => 'number',
        'is_show' => 'require|in:0,1'
    ];

    protected $message = [
        'id.require'      => 'ID不能为空',
        'id.number'       => 'ID必须为数字',
        'name.require'    => '分类名称不能为空',
        'name.max'        => '分类名称最多不能超过50个字符',
        'sort.number'     => '排序必须为数字',
        'is_show.require' => '是否显示不能为空',
        'is_show.in'      => '是否显示参数错误'
    ];

    protected $scene = [
        'id'   => ['id'],
        'add'  => ['name', 'sort', 'is_show'],
        'edit' => ['id', 'name', 'sort', 'is_show']
    ];
}