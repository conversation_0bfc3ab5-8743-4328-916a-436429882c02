/**
 * 视频上传功能修复脚本
 * 解决视频上传弹窗关闭后页面功能失效的问题
 */
(function() {
    'use strict';
    
    console.log('🔧 视频上传修复脚本已加载');
    
    // 保存原始的layer.open方法
    var originalLayerOpen = null;
    var originalCallback = null;
    
    // 页面功能恢复函数
    function restorePageFunctions() {
        console.log('🔄 正在恢复页面功能...');
        
        try {
            // 重新初始化layui组件
            if (typeof layui !== 'undefined') {
                layui.use(['form', 'laydate', 'element'], function() {
                    var form = layui.form;
                    var laydate = layui.laydate;
                    var element = layui.element;
                    
                    // 重新渲染表单组件
                    form.render();
                    
                    // 重新初始化日期选择器
                    if (document.querySelector('input[name="start_time"]')) {
                        laydate.render({
                            elem: 'input[name="start_time"]',
                            type: 'datetime'
                        });
                    }
                    
                    if (document.querySelector('input[name="end_time"]')) {
                        laydate.render({
                            elem: 'input[name="end_time"]',
                            type: 'datetime'
                        });
                    }
                    
                    console.log('✅ Layui组件已重新初始化');
                });
            }
            
            // 重新绑定图片上传事件
            setTimeout(function() {
                if (typeof like !== 'undefined' && like.imageUpload) {
                    // 重新绑定商品图片上传
                    $('#goodsimage').off('click').on('click', function() {
                        like.imageUpload({
                            limit: 6,
                            field: "goods_image[]",
                            that: $(this),
                            content: '/shop/file/lists?type=10'
                        });
                    });
                    
                    console.log('✅ 图片上传事件已重新绑定');
                }
            }, 200);
            
            console.log('✅ 页面功能恢复完成');
            
        } catch (e) {
            console.error('❌ 恢复页面功能时出错:', e);
        }
    }
    
    // 安全的视频上传函数
    function safeVideoUpload(options) {
        console.log('🎥 开始安全视频上传');
        
        // 保存当前的callback
        originalCallback = window.callback;
        
        var $that = options.that;
        var field = options.field || 'video';
        var content = options.content || '/shop/file/videoList';
        
        // 打开视频选择弹窗
        var videoWindow = layer.open({
            type: 2,
            title: "上传视频",
            content: content,
            area: ["90%", "90%"],
            success: function(layero, index) {
                console.log('📹 视频选择弹窗已打开');
                
                // 设置视频选择回调
                window.callback = function (uri) {
                    try {
                        console.log('📁 视频回调函数被调用，URI:', uri);

                        // 确保URI是有效的
                        if (!uri || uri.trim() === '') {
                            console.error('❌ 视频URI为空');
                            layer.msg('视频地址无效');
                            return false;
                        }

                        // 处理URI格式
                        var videoUri = uri;
                        if (!videoUri.startsWith('http') && !videoUri.startsWith('/')) {
                            videoUri = '/' + videoUri;
                        }

                        console.log('📁 处理后的视频URI:', videoUri);

                        // 创建视频显示HTML
                        var template = '<div class="upload-video-div">' +
                            '<video width="160" height="160" src="' + videoUri + '" controls autoplay></video>' +
                            '<div class="del-upload-btn">×</div>' +
                            '<input type="hidden" name="' + field + '" value="' + videoUri + '">' +
                            '</div>';

                        // 添加到页面
                        $that.parent().before(template);
                        $that.hide();

                        layer.msg('视频上传成功');
                        layer.close(index);

                        console.log('✅ 视频已成功添加到页面');

                    } catch (e) {
                        console.error('❌ 视频上传回调错误:', e);
                        layer.msg('视频上传失败: ' + e.message);
                    }

                    return uri;
                };
            },
            cancel: function(index) {
                console.log('❌ 用户取消了视频上传');
                restorePageFunctions();
                return true;
            },
            end: function() {
                console.log('🔚 视频上传弹窗已关闭');
                
                // 恢复原始callback
                if (originalCallback) {
                    window.callback = originalCallback;
                } else {
                    delete window.callback;
                }
                
                // 恢复页面功能
                restorePageFunctions();
            }
        });
    }
    
    // 监听页面加载完成
    $(document).ready(function() {
        console.log('📄 页面已加载，开始修复视频上传功能');
        
        // 重新绑定视频上传事件
        $(document).off('click', '#video').on('click', '#video', function(e) {
            e.preventDefault();
            console.log('🎯 点击了视频上传按钮');
            
            safeVideoUpload({
                limit: 1,
                field: "video",
                that: $(this),
                content: '/shop/file/videoList'
            });
        });
        
        console.log('✅ 视频上传事件已重新绑定');
    });
    
    // 定期检查页面功能状态
    var checkInterval = setInterval(function() {
        // 检查layui form是否正常工作
        if (typeof layui !== 'undefined') {
            try {
                var $testInput = $('input[type="text"]:first');
                if ($testInput.length > 0 && !$testInput.is(':disabled')) {
                    // 页面功能正常
                    return;
                }
            } catch (e) {
                console.warn('⚠️ 检测到页面功能异常，正在修复...');
                restorePageFunctions();
            }
        }
    }, 5000); // 每5秒检查一次
    
    // 页面卸载时清理
    $(window).on('beforeunload', function() {
        if (checkInterval) {
            clearInterval(checkInterval);
        }
    });
    
    // 暴露修复函数到全局
    window.VideoUploadFix = {
        restore: restorePageFunctions,
        safeUpload: safeVideoUpload
    };
    
    console.log('🛡️ 视频上传修复脚本初始化完成');
    
})();
