<?php

namespace app\api\controller;

use app\common\basics\Api;
use app\api\logic\PayLogic;
use app\common\enum\ClientEnum;
use app\common\enum\PayEnum;
use app\common\enum\ShopTierEnum;
use app\common\logic\ShopTierLogic;
use app\common\server\JsonServer;

/**
 * 商家等级相关接口
 */
class ShopTier extends Api
{
    /**
     * 获取等级配置列表
     */
    public function tierConfigs()
    {
        $configs = ShopTierLogic::getTierConfigs();
        
        // 格式化返回数据
        foreach ($configs as &$config) {
            $config['features'] = json_decode($config['features'], true) ?: [];
            $config['limits'] = json_decode($config['limits'], true) ?: [];
            $config['price_text'] = $config['price'] > 0 ? '¥' . $config['price'] : '免费';
        }

        return JsonServer::success('获取成功', $configs);
    }

    /**
     * 选择等级并创建支付订单
     */
    public function selectTier()
    {
        $post = $this->request->post();
        
        try {
            $targetTierLevel = intval($post['target_tier_level'] ?? 0);
            $userId = $this->user_id;

            if (!ShopTierEnum::isValidTier($targetTierLevel)) {
                return JsonServer::error('无效的等级参数');
            }

            $result = ShopTierLogic::createTierOrder($userId, $targetTierLevel);
            if ($result === false) {
                return JsonServer::error(ShopTierLogic::getError());
            }

            return JsonServer::success('处理成功', $result);

        } catch (\Exception $e) {
            return JsonServer::error($e->getMessage());
        }
    }

    /**
     * 创建支付订单
     */
    public function createPayment()
    {
        $post = $this->request->post();
        
        try {
            $orderSn = $post['order_sn'] ?? '';
            $payWay = intval($post['pay_way'] ?? PayEnum::WECHAT_PAY);
            $from = intval($post['from'] ?? ClientEnum::h5);

            if (empty($orderSn)) {
                return JsonServer::error('订单号不能为空');
            }

            // 查找订单
            $order = \app\common\model\shop\ShopMerchantfees::where('order_sn', $orderSn)->find();
            if (!$order) {
                return JsonServer::error('订单不存在');
            }

            if ($order->status != 0) {
                return JsonServer::error('订单状态异常');
            }

            if ($order->user_id != $this->user_id) {
                return JsonServer::error('订单不属于当前用户');
            }

            // 创建支付
            switch ($payWay) {
                case PayEnum::WECHAT_PAY:
                    $payInfo = PayLogic::wechatPay($orderSn, 'ruzhucharge', $from);
                    break;
                case PayEnum::ALI_PAY:
                    $payInfo = PayLogic::aliPay($orderSn, 'ruzhucharge', $from);
                    break;
                default:
                    return JsonServer::error('不支持的支付方式');
            }

            if ($payInfo === false) {
                return JsonServer::error(PayLogic::getError() ?: '创建支付失败');
            }

            return JsonServer::success('支付创建成功', $payInfo);

        } catch (\Exception $e) {
            return JsonServer::error($e->getMessage());
        }
    }

    /**
     * 获取用户支付状态
     */
    public function getPaymentStatus()
    {
        $orderSn = $this->request->get('order_sn');
        if (!$orderSn) {
            return JsonServer::error('订单号不能为空');
        }

        $order = \app\common\model\shop\ShopMerchantfees::where([
            'order_sn' => $orderSn,
            'user_id' => $this->user_id
        ])->find();

        if (!$order) {
            return JsonServer::error('订单不存在');
        }

        $tierConfig = ShopTierLogic::getTierConfig($order->tier_level);

        return JsonServer::success('获取成功', [
            'order_sn' => $order->order_sn,
            'amount' => $order->amount,
            'tier_level' => $order->tier_level,
            'tier_name' => $tierConfig ? $tierConfig->tier_name : ShopTierEnum::getTierName($order->tier_level),
            'status' => $order->status,
            'status_text' => $order->status == 1 ? '已支付' : '待支付',
            'created_at' => $order->created_at
        ]);
    }

    /**
     * 商家等级升级
     */
    public function upgrade()
    {
        $post = $this->request->post();
        
        try {
            $shopId = $this->getShopId();
            if (!$shopId) {
                return JsonServer::error('商家信息获取失败');
            }

            $targetTierLevel = intval($post['target_tier_level'] ?? 0);
            
            if (!ShopTierEnum::isValidTier($targetTierLevel)) {
                return JsonServer::error('无效的等级参数');
            }
            
            $result = ShopTierLogic::createUpgradeOrder($shopId, $targetTierLevel, $this->user_id);
            if ($result === false) {
                return JsonServer::error(ShopTierLogic::getError());
            }

            return JsonServer::success('升级订单创建成功', $result);

        } catch (\Exception $e) {
            return JsonServer::error($e->getMessage());
        }
    }

    /**
     * 获取可升级等级列表
     */
    public function getUpgradableTiers()
    {
        $shopId = $this->getShopId();
        if (!$shopId) {
            return JsonServer::error('商家信息获取失败');
        }

        $tiers = ShopTierLogic::getUpgradableTiers($shopId);
        return JsonServer::success('获取成功', $tiers);
    }

    /**
     * 获取商家ID（从session或其他方式）
     */
    private function getShopId()
    {
        // 这里需要根据实际情况获取商家ID
        // 可能从session、用户表关联等方式获取
        $user = \app\common\model\User::find($this->user_id);
        return $user ? $user->shop_id : 0;
    }
}
