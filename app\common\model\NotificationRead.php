<?php
namespace app\common\model;

use think\Model;

class NotificationRead extends Model
{
    protected $name = 'notification_reads'; // Explicitly define table name

    // Disable automatic writing of updated_at timestamp as it's not present in the table
    public $updateTime = false;

    // Define enum values for user_type
    const USER_TYPE_ADMIN = 'admin';
    const USER_TYPE_SHOP = 'shop';

    /**
     * Define relationship with Notification model
     * A read record belongs to one notification
     */
    public function notification()
    {
        return $this->belongsTo(Notification::class, 'notification_id', 'id');
    }
}
