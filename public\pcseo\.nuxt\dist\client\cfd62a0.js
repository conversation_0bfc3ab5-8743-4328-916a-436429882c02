(window.webpackJsonp=window.webpackJsonp||[]).push([[22,9],{437:function(e,t,o){"use strict";var n=o(17),r=o(2),l=o(3),c=o(136),d=o(27),f=o(18),m=o(271),h=o(52),v=o(135),x=o(270),w=o(5),k=o(98).f,y=o(44).f,S=o(26).f,C=o(438),_=o(439).trim,O="Number",N=r.Number,T=N.prototype,I=r.TypeError,E=l("".slice),L=l("".charCodeAt),R=function(e){var t=x(e,"number");return"bigint"==typeof t?t:M(t)},M=function(e){var t,o,n,r,l,c,d,code,f=x(e,"number");if(v(f))throw I("Cannot convert a Symbol value to a number");if("string"==typeof f&&f.length>2)if(f=_(f),43===(t=L(f,0))||45===t){if(88===(o=L(f,2))||120===o)return NaN}else if(48===t){switch(L(f,1)){case 66:case 98:n=2,r=49;break;case 79:case 111:n=8,r=55;break;default:return+f}for(c=(l=E(f,2)).length,d=0;d<c;d++)if((code=L(l,d))<48||code>r)return NaN;return parseInt(l,n)}return+f};if(c(O,!N(" 0o1")||!N("0b1")||N("+0x1"))){for(var j,A=function(e){var t=arguments.length<1?0:N(R(e)),o=this;return h(T,o)&&w((function(){C(o)}))?m(Object(t),o,A):t},$=n?k(N):"MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,EPSILON,MAX_SAFE_INTEGER,MIN_SAFE_INTEGER,isFinite,isInteger,isNaN,isSafeInteger,parseFloat,parseInt,fromString,range".split(","),D=0;$.length>D;D++)f(N,j=$[D])&&!f(A,j)&&S(A,j,y(N,j));A.prototype=T,T.constructor=A,d(r,O,A)}},438:function(e,t,o){var n=o(3);e.exports=n(1..valueOf)},439:function(e,t,o){var n=o(3),r=o(33),l=o(16),c=o(440),d=n("".replace),f="["+c+"]",m=RegExp("^"+f+f+"*"),h=RegExp(f+f+"*$"),v=function(e){return function(t){var o=l(r(t));return 1&e&&(o=d(o,m,"")),2&e&&(o=d(o,h,"")),o}};e.exports={start:v(1),end:v(2),trim:v(3)}},440:function(e,t){e.exports="\t\n\v\f\r                　\u2028\u2029\ufeff"},449:function(e,t,o){"use strict";o.r(t);o(437),o(81),o(61),o(24),o(100),o(80),o(99);var n=6e4,r=36e5,l=24*r;function c(e){return(0+e.toString()).slice(-2)}var d={components:{},props:{isSlot:{type:Boolean,default:!1},time:{type:Number,default:0},format:{type:String,default:"hh:mm:ss"},autoStart:{type:Boolean,default:!0}},watch:{time:{immediate:!0,handler:function(e){e&&this.reset()}}},data:function(){return{timeObj:{},formateTime:0}},created:function(){},computed:{},methods:{createTimer:function(e){return setTimeout(e,100)},isSameSecond:function(e,t){return Math.floor(e)===Math.floor(t)},start:function(){this.counting||(this.counting=!0,this.endTime=Date.now()+1e3*this.remain,this.setTimer())},setTimer:function(){var e=this;this.tid=this.createTimer((function(){var t=e.getRemain();e.isSameSecond(t,e.remain)&&0!==t||e.setRemain(t),0!==e.remain&&e.setTimer()}))},getRemain:function(){return Math.max(this.endTime-Date.now(),0)},pause:function(){this.counting=!1,clearTimeout(this.tid)},reset:function(){this.pause(),this.remain=this.time,this.setRemain(this.remain),this.autoStart&&this.start()},setRemain:function(e){var t=this.format;this.remain=e;var time,o=(time=e,{days:Math.floor(time/l),hours:c(Math.floor(time%l/r)),minutes:c(Math.floor(time%r/n)),seconds:c(Math.floor(time%n/1e3))});this.formateTime=function(e,t){var o=t.days,n=t.hours,r=t.minutes,l=t.seconds;return-1!==e.indexOf("dd")&&(e=e.replace("dd",o)),-1!==e.indexOf("hh")&&(e=e.replace("hh",c(n))),-1!==e.indexOf("mm")&&(e=e.replace("mm",c(r))),-1!==e.indexOf("ss")&&(e=e.replace("ss",c(l))),e}(t,o),this.$emit("change",o),0===e&&(this.pause(),this.$emit("finish"))}}},f=o(9),component=Object(f.a)(d,(function(){var e=this,t=e.$createElement,o=e._self._c||t;return e.time>=0?o("div",[o("client-only",[e.isSlot?e._t("default"):o("span",[e._v(e._s(e.formateTime))])],2)],1):e._e()}),[],!1,null,null,null);t.default=component.exports},454:function(e,t,o){"use strict";o.d(t,"d",(function(){return n})),o.d(t,"e",(function(){return r})),o.d(t,"c",(function(){return l})),o.d(t,"b",(function(){return c})),o.d(t,"a",(function(){return d}));var n=5,r={SMS:0,ACCOUNT:1},l={REGISTER:"ZCYZ",FINDPWD:"ZHMM",LOGIN:"YZMDL",SJSQYZ:"SJSQYZ",CHANGE_MOBILE:"BGSJHM",BIND:"BDSJHM"},c={NONE:"",SEX:"sex",NICKNAME:"nickname",AVATAR:"avatar",MOBILE:"mobile"},d={NORMAL:"normal",HANDLING:"apply",FINISH:"finish"}},533:function(e,t,o){var content=o(595);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[e.i,content,""]]),content.locals&&(e.exports=content.locals);(0,o(14).default)("0c894dd8",content,!0,{sourceMap:!1})},594:function(e,t,o){"use strict";o(533)},595:function(e,t,o){var n=o(13)(!1);n.push([e.i,'.login[data-v-a0d0f46a]{flex:1;background-size:cover;background-repeat:no-repeat;background-position:50%;min-width:1180px}.login .login-container[data-v-a0d0f46a]{margin:0 auto;width:1180px;height:100%;position:relative}.login .login-container .login-banner[data-v-a0d0f46a]{display:flex;align-items:center;justify-content:center;width:750px;margin-right:30px;height:440px;overflow:hidden;-webkit-animation:loadimg-data-v-a0d0f46a 2s infinite;animation:loadimg-data-v-a0d0f46a 2s infinite;transition:background-color 2s}@-webkit-keyframes loadimg-data-v-a0d0f46a{0%{background-color:#e4e4e4}50%{background-color:#f0f0f0}to{background-color:#e4e4e4}}@keyframes loadimg-data-v-a0d0f46a{0%{background-color:#e4e4e4}50%{background-color:#f0f0f0}to{background-color:#e4e4e4}}.login .login-container .login-float-form-wrap[data-v-a0d0f46a]{width:400px;height:440px}.login .login-container .login-float-form-wrap .login-box[data-v-a0d0f46a]{background-color:#fff;height:100%;display:flex;flex-direction:column;justify-content:space-between}.login .login-container .login-float-form-wrap .login-box .login-header-box[data-v-a0d0f46a]{padding-top:20px}.login .login-container .login-float-form-wrap .login-box .login-header-box .header-tabs .header-tab[data-v-a0d0f46a]{width:160px;height:35px;display:flex;flex-direction:column;align-items:center;cursor:pointer}.login .login-container .login-float-form-wrap .login-box .login-header-box .header-tabs .active-tab[data-v-a0d0f46a]{color:#ff2c3c;text-align:center}.login .login-container .login-float-form-wrap .login-box .login-header-box .header-tabs .active-tab[data-v-a0d0f46a]:after{content:"";height:2px;width:72px;margin-top:8px;background-color:#ff2c3c}.login .login-container .login-float-form-wrap .login-box .login-header-box .login-form-box[data-v-a0d0f46a]{padding:0 30px}.login .login-container .login-float-form-wrap .login-box .login-header-box .login-form-box .login-form-item[data-v-a0d0f46a]{margin-top:24px}.login .login-container .login-float-form-wrap .login-box .login-header-box .login-form-box .login-form-item .input-phone-num[data-v-a0d0f46a]{width:340px}.login .login-container .login-float-form-wrap .login-box .login-header-box .login-form-box .login-form-item .verify-code-img[data-v-a0d0f46a]{width:100px;height:40px;margin-left:20px;background-color:red}.login .login-container .login-float-form-wrap .login-box .login-header-box .login-form-box .login-form-item .sms-btn[data-v-a0d0f46a]{margin-left:20px;height:40px}.login .login-container .login-float-form-wrap .login-box .login-header-box .option-box[data-v-a0d0f46a]{padding:0 30px;margin-top:60px}.login .login-container .login-float-form-wrap .login-box .login-header-box .option-box[data-v-a0d0f46a]  .el-checkbox{color:#888}.login .login-container .login-float-form-wrap .login-box .login-footer-box[data-v-a0d0f46a]{height:50px;padding:15px}.login .login-container .login-float-form-wrap .login-box .login-footer-box .login__other-item[data-v-a0d0f46a]{cursor:pointer}.login .login-container .login-float-form-wrap .login-box .login-footer-box .login__weixin-icon[data-v-a0d0f46a]{width:1.5em;height:1.5em;text-align:center;line-height:1.5em;border-radius:50%;background-color:#0abd5d;color:#fff}',""]),e.exports=n},662:function(e,t,o){"use strict";o.r(t);o(22),o(19),o(21),o(28),o(20),o(29);var n=o(6),r=o(10),l=(o(51),o(80),o(99),o(454)),c=o(75),d=o.n(c),f=o(11);function m(object,e){var t=Object.keys(object);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(object);e&&(o=o.filter((function(e){return Object.getOwnPropertyDescriptor(object,e).enumerable}))),t.push.apply(t,o)}return t}function h(e){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?m(Object(source),!0).forEach((function(t){Object(r.a)(e,t,source[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(source)):m(Object(source)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(source,t))}))}return e}var v={head:function(){return{title:this.$store.getters.headTitle,link:[{rel:"icon",type:"image/x-icon",href:this.$store.getters.favicon}]}},layout:"main",components:{CountDown:o(449).default},data:function(){return{selectNumberType:"中国+86",account:"",password:"",telephone:"",verifyCode:"",smsCode:"",isRemember:!0,loginStatus:l.e.SMS,canSend:!0}},computed:h({},Object(f.d)(["config"])),methods:h(h(h({},Object(f.c)(["setToken"])),Object(f.b)(["getPublicData"])),{},{changeLoginType:function(e){this.loginStatus=e,this.telephone="",this.verifyCode="",this.smsCode="";var t=JSON.parse(localStorage.getItem("ACCOUNT")),o=JSON.parse(localStorage.getItem("TEL"));t&&t.account&&(this.account=t.account),o&&o.telephone&&(this.telephone=o.telephone)},goWechatLogin:function(){var e=this;this.$get("account/scanCode").then((function(e){var code=e.code,t=e.msg,data=e.data;if(1!==code)throw new Error(t);window.open(data.url,"_self")})).catch((function(t){e.$message.error(t.message)}))},handleWechatLogin:function(e){var t=this;this.$post("account/scanLogin",e).then((function(e){var code=e.code,o=e.msg,data=e.data;if(1!==code)throw new Error(o);d.a.set("token",data.token,{expires:60}),t.setToken(data.token),t.$router.replace({path:d.a.get("back_url")||"/"}),d.a.remove("back_url"),t.getPublicData()})).catch((function(e){t.$message.error(e.message)}))},sendSMSCode:function(){var e=this;return Object(n.a)(regeneratorRuntime.mark((function t(){var o;return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(e.canSend){t.next=2;break}return t.abrupt("return");case 2:return t.next=4,e.$post("sms/send",{mobile:e.telephone,key:l.c.LOGIN,client:l.d});case 4:1==(o=t.sent).code&&(e.$message({message:o.msg,type:"success"}),e.canSend=!1);case 6:case"end":return t.stop()}}),t)})))()},smsLogin:function(){var e=this;return Object(n.a)(regeneratorRuntime.mark((function t(){var o,n;return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,e.$post("account/smsLogin",{mobile:e.telephone,code:e.smsCode,client:l.d});case 2:1==(o=t.sent).code&&(n=o.data.token,d.a.set("token",n,{expires:60}),e.setToken(n),e.$router.replace({path:d.a.get("back_url")||"/"}),d.a.remove("back_url"),e.getPublicData(),e.isRemember?localStorage.setItem("TEL",JSON.stringify({telephone:e.telephone})):localStorage.setItem("TEL",JSON.stringify({telephone:""})));case 4:case"end":return t.stop()}}),t)})))()},accountLogin:function(){var e=this;return Object(n.a)(regeneratorRuntime.mark((function t(){var o,n;return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,e.$post("account/login",{mobile:e.account,password:e.password,client:l.d});case 2:1==(o=t.sent).code&&(n=o.data.token,d.a.set("token",n,{expires:60}),e.setToken(n),e.$router.replace({path:d.a.get("back_url")||"/"}),d.a.remove("back_url"),e.getPublicData(),e.isRemember?localStorage.setItem("ACCOUNT",JSON.stringify({account:e.account})):localStorage.setItem("ACCOUNT",JSON.stringify({account:""})));case 4:case"end":return t.stop()}}),t)})))()}}),created:function(){var e,t,o,n,r=this.$route.query;r.code&&r.state&&this.handleWechatLogin({code:r.code,state:r.state});var l=null!==(e=JSON.parse(localStorage.getItem("ACCOUNT")))&&void 0!==e?e:{},c=null!==(t=JSON.parse(localStorage.getItem("TEL")))&&void 0!==t?t:{};this.account=null!==(o=null==l?void 0:l.account)&&void 0!==o?o:"",this.telephone=null!==(n=null==c?void 0:c.telephone)&&void 0!==n?n:""}},x=(o(594),o(9)),component=Object(x.a)(v,(function(){var e=this,t=e.$createElement,o=e._self._c||t;return o("div",{staticClass:"login flex col-center"},[o("div",{staticClass:"login-container flex col-stretch"},[o("div",{staticClass:"login-banner"},[o("img",{attrs:{src:e.config.pc_login_logo,height:"100%"}})]),e._v(" "),o("div",{staticClass:"login-float-form-wrap"},[o("div",{staticClass:"login-box"},[o("div",{staticClass:"login-header-box"},[o("div",{staticClass:"header-tabs flex row-center"},[o("div",{staticClass:"header-tab xxl",class:{"active-tab":0==e.loginStatus},on:{click:function(t){return e.changeLoginType(0)}}},[e._v("\n                                验证码登录\n                            ")]),e._v(" "),o("div",{staticClass:"header-tab xxl",class:{"active-tab":1==e.loginStatus},on:{click:function(t){return e.changeLoginType(1)}}},[e._v("\n                                账号密码登录\n                            ")])]),e._v(" "),o("div",{directives:[{name:"show",rawName:"v-show",value:0==e.loginStatus,expression:"loginStatus == 0"}],on:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.smsLogin.apply(null,arguments)}}},[o("div",{staticClass:"login-form-box"},[o("div",{staticClass:"login-form-item"},[o("el-input",{staticClass:"input-phone-num",attrs:{placeholder:"请输入手机号码"},model:{value:e.telephone,callback:function(t){e.telephone=t},expression:"telephone"}},[o("el-select",{staticStyle:{width:"100px"},attrs:{slot:"prepend",placeholder:"请选择"},slot:"prepend",model:{value:e.selectNumberType,callback:function(t){e.selectNumberType=t},expression:"selectNumberType"}},[o("el-option",{attrs:{label:"中国+86",value:"1"}})],1)],1)],1),e._v(" "),o("div",{staticClass:"login-form-item flex"},[o("el-input",{staticStyle:{width:"210px"},attrs:{placeholder:"短信验证码"},model:{value:e.smsCode,callback:function(t){e.smsCode=t},expression:"smsCode"}}),e._v(" "),o("el-button",{staticClass:"sms-btn",on:{click:e.sendSMSCode}},[e.canSend?o("div",[e._v("获取验证码")]):o("count-down",{attrs:{time:60,format:"ss秒",autoStart:!0},on:{finish:function(t){e.canSend=!0}}})],1)],1)]),e._v(" "),o("div",{staticClass:"option-box flex-col"},[o("el-checkbox",{staticClass:"muted",model:{value:e.isRemember,callback:function(t){e.isRemember=t},expression:"isRemember"}},[e._v("记住账号")]),e._v(" "),o("div",{staticClass:"m-t-20 flex-col"},[o("el-button",{attrs:{type:"primary"},on:{click:e.smsLogin}},[e._v("立即登录")])],1)],1)]),e._v(" "),o("div",{directives:[{name:"show",rawName:"v-show",value:1==e.loginStatus,expression:"loginStatus == 1"}],on:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.accountLogin.apply(null,arguments)}}},[o("div",{staticClass:"login-form-box"},[o("div",{staticClass:"login-form-item"},[o("el-input",{staticClass:"input-phone-num",attrs:{placeholder:"请输入账号/手机号码"},model:{value:e.account,callback:function(t){e.account=t},expression:"account"}},[o("i",{staticClass:"el-icon-user",staticStyle:{"font-size":"18px"},attrs:{slot:"prepend"},slot:"prepend"})])],1),e._v(" "),o("div",{staticClass:"login-form-item flex"},[o("el-input",{attrs:{placeholder:"请输入密码","show-password":""},model:{value:e.password,callback:function(t){e.password=t},expression:"password"}},[o("i",{staticClass:"el-icon-more-outline",staticStyle:{"font-size":"18px"},attrs:{slot:"prepend"},slot:"prepend"})])],1)]),e._v(" "),o("div",{staticClass:"option-box flex-col"},[o("div",{staticClass:"flex row-between"},[o("el-checkbox",{staticClass:"muted",model:{value:e.isRemember,callback:function(t){e.isRemember=t},expression:"isRemember"}},[e._v("记住账号")]),e._v(" "),o("nuxt-link",{staticClass:"muted",attrs:{to:"/account/forget_pwd"}},[e._v("忘记密码？")])],1),e._v(" "),o("div",{staticClass:"m-t-20 flex-col"},[o("el-button",{attrs:{type:"primary"},on:{click:e.accountLogin}},[e._v("立即登录")])],1)])])]),e._v(" "),o("div",{staticClass:"login-footer-box flex row-between"},[o("div",{staticClass:"flex"},[o("div",{staticClass:"flex login__other-item",on:{click:e.goWechatLogin}},[o("i",{staticClass:"\n                                        iconfont\n                                        icon-weixin1\n                                        login__weixin-icon\n                                    "}),e._v(" "),o("div",{staticClass:"m-l-4 muted"},[e._v("微信")])])]),e._v(" "),o("nuxt-link",{staticClass:"primary",attrs:{to:"/account/register"}},[e._v("\n                            注册账号\n                        ")])],1)])])])])}),[],!1,null,"a0d0f46a",null);t.default=component.exports;installComponents(component,{CountDown:o(449).default})}}]);