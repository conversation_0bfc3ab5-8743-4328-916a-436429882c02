<?php

namespace app\common\enum;
class AdEnum{
    const MOBILE    = 1;
    const PC        = 2;


    /**
     * Notes:获取终端
     * @param bool $from
     * @return array|mixed
     * @author: cjhao 2021/4/19 11:32
     */
    public static function getTerminal($from = true){
        $desc = [
            self::MOBILE    => '移动端商城',
            self::PC        => 'PC端商城',
        ];
        if(true === $from){
            return $desc;
        }
        return $desc[$from];
    }

    /**
     * Notes:商城页面路径
     * @param bool $type
     * @param bool $from
     * @return array|mixed
     * @author: cjhao 2021/4/20 11:55
     */
    public static function getLinkPage($type = true,$from = true){
        // 优先从配置中获取页面数据
        $mobile_pages = \app\common\server\ConfigServer::get('link_config', 'mobile_pages', []);

        // 如果配置为空，使用默认配置
        if (empty($mobile_pages)) {
            $mobile_pages = self::getDefaultMobilePages();
        }

        // PC端使用默认配置
        $pc_pages = self::getDefaultPcPages();

        $page = [
            self::MOBILE => $mobile_pages,
            self::PC => $pc_pages,
        ];
        if(true !== $type){
            $page = $page[$type] ?? [];
        }
        if(true === $from){
            return $page;
        }
        return $page[$from] ?? [];
    }

    /**
     * Notes:获取商品详情路径
     * @param bool $from
     * @return array|mixed|string
     * @author: cjhao 2021/4/20 14:06
     */
    public static function getGoodsPath($from = true){
        $desc = [
            self::MOBILE    => '/subcontract/Home/productDetails/index',
            self::PC        => '/goods_details',
        ];
        if(true === $from){
            return $desc;
        }
        return $desc[$from] ?? '';
    }

    /**
     * Notes:获取默认移动端页面配置
     * @return array
     * @author: system
     */
    private static function getDefaultMobilePages(){
        return [
            // 主要导航页面（tabbar）
            [
                'name'      => '首页',
                'path'      => '/pages/Home/home/<USER>',
                'is_tab'    => 1,
            ],

            [
                'name'      => '商家后台',
                'path'      => '/subcontract/MerchantCenter/index',
                'is_tab'    => 0,
            ],
            [
                'name'      => '客服中心',
                'path'      => '/subcontract/Mine/customerService/index',
                'is_tab'    => 1,
            ],
            [
                'name'      => '产品库（分类页）',
                'path'      => '/pages/Classify/classify/index',
                'is_tab'    => 1,
            ],
            [
                'name'      => '采购信息',
                'path'      => '/pages/Discover/discover/index?activeIndex=0',
                'is_tab'    => 1,
            ],
            [
                'name'      => '货源信息',
                'path'      => '/pages/Discover/discover/index?activeIndex=1',
                'is_tab'    => 1,
            ],
            [
                'name'      => '找展会',
                'path'      => '/pages/Discover/discover/index?activeIndex=2',
                'is_tab'    => 1,
            ],
            [
                'name'      => '新资讯',
                'path'      => '/pages/Discover/discover/index?activeIndex=3',
                'is_tab'    => 1,
            ],

            // 会员相关页面
            [
                'name'      => '会员开通',
                'path'      => '/subcontract/Mine/member/member',
                'is_tab'    => 0,
            ],
            [
                'name'      => '招商顾问申请',
                'path'      => '/subcontract/Mine/Applyforagency/index',
                'is_tab'    => 0,
            ],


            // 商品推荐页面
            [
                'name'      => '新品上新',
                'path'      => '/subcontract/Home/upNew/index',
                'is_tab'    => 0,
            ],
            [
                'name'      => '行家严选',
                'path'      => '/subcontract/Home/strictSelection/index',
                'is_tab'    => 0,
            ],
            [
                'name'      => '热卖爆品',
                'path'      => '/subcontract/Home/HOTSALE/index',
                'is_tab'    => 0,
            ],
            [
                'name'      => '机构必采',
                'path'      => '/subcontract/Home/mustAdopt/index',
                'is_tab'    => 0,
            ],
            [
                'name'      => '居家爆品',
                'path'      => '/subcontract/Home/based/index',
                'is_tab'    => 0,
            ],
             [
                'name'      => '商家入驻',
                'path'      => '/subcontract/Mine/JoinMerchant/index',
                'is_tab'    => 0,
            ],

            // 榜单页面
            [
                'name'      => '收藏榜',
                'path'      => '/subcontract/Home/favorite/index',
                'is_tab'    => 0,
            ],
            [
                'name'      => '年度榜',
                'path'      => '/subcontract/Home/annual/index',
                'is_tab'    => 0,
            ],

            // 直播和认证
            [
                'name'      => '货直播',
                'path'      => '/subcontract/Home/goodsLive/index',
                'is_tab'    => 0,
            ],
            [
                'name'      => '权威认证',
                'path'      => '/subcontract/Home/authoritativeVerification/index',
                'is_tab'    => 0,
            ],

            // 企业相关
            [
                'name'      => '热销企业',
                'path'      => '/subcontract/Home/CJenterprises/index',
                'is_tab'    => 0,
            ],

            // 集采相关页面
            [
                'name'      => '集采联盟',
                'path'      => '/subcontract/Home/CJAlliance/index',
                'is_tab'    => 0,
            ],
            [
                'name'      => '工厂年度榜',
                'path'      => '/subcontract/Home/CJannualHelp/index',
                'is_tab'    => 0,
            ],
            [
                'name'      => '集采众筹',
                'path'      => '/subcontract/Home/purchase/index',
                'is_tab'    => 0,
            ],
            [
                'name'      => '集采爆品',
                'path'      => '/subcontract/Home/centralizedProcurement/index',
                'is_tab'    => 0,
            ],
            [
                'name'      => '集采推荐榜',
                'path'      => '/subcontract/Home/recommend/index',
                'is_tab'    => 0,
            ],
            [
                'name'      => '集采年度榜',
                'path'      => '/subcontract/Home/CPannual/index',
                'is_tab'    => 0,
            ],

            // 业务功能页面
            [
                'name'      => '发布货源信息',
                'path'      => '/subcontract/Business/release/release?activeIndex=1',
                'is_tab'    => 0,
            ],
            [
                'name'      => '发布采购信息',
                'path'      => '/subcontract/Business/release/release?activeIndex=0',
                'is_tab'    => 0,
            ],
            [
                'name'      => '我的用户',
                'path'      => '/subcontract/Mine/mineUser/Direct/index',
                'is_tab'    => 0,
            ],
            [
                'name'      => '我的收益',
                'path'      => '/subcontract/Mine/mineIncome/index',
                'is_tab'    => 0,
            ],

            // 外部链接页面
            [
                'name'      => '商家后台',
                'path'      => 'https://www.huohanghang.cn/business/',
                'is_tab'    => 0,
            ],
            [
                'name'      => '客服中心',
                'path'      => 'https://www.huohanghang.cn/kefu_m/',
                'is_tab'    => 0,
            ],
            [
                'name'      => '验厂认证',
                'path'      => 'https://www.huohanghang.cn/business/bundle/pages/verify/verify',
                'is_tab'    => 0,
            ],
            [
                'name'      => '厂商联盟',
                'path'      => 'https://www.huohanghang.cn/business/bundle/pages/ManufacturerAlliance/ManufacturerAlliance',
                'is_tab'    => 0,
            ],
        ];
    }

    /**
     * Notes:获取默认PC端页面配置
     * @return array
     * @author: system
     */
    private static function getDefaultPcPages(){
        return [
            [
                'name'      => '商品分类',
                'path'      => '/category',
                'is_tab'    => 0,
            ],
            [
                'name'      => '领券中心',
                'path'      => '/get_coupons',
                'is_tab'    => 0,
            ],
            [
                'name'      => '购物车',
                'path'      => '/get_cart',
                'is_tab'    => 0,
            ],
            [
                'name'      => '我的订单',
                'path'      => '/get_order',
                'is_tab'    => 0,
            ],
            [
                'name'      => '商家入驻',
                'path'      => '/shop',
                'is_tab'    => 0,
            ],
            [
                'name'      => '帮助中心',
                'path'      => '/help',
                'is_tab'    => 0,
            ],
            [
                'name'      => '限时秒杀',
                'path'      => '/seckill',
                'is_tab'    => 0,
            ],
            [
                'name'      => '热销榜单',
                'path'      => '/goods_list/1',
                'is_tab'    => 0,
            ],
            [
                'name'      => '新品推荐',
                'path'      => '/goods_list/2',
                'is_tab'    => 0,
            ],
            [
                'name'      => '店铺街',
                'path'      => '/shop_street',
                'is_tab'    => 0,
            ],
            [
                'name'      => '商城资讯',
                'path'      => '/news_list',
                'is_tab'    => 0,
            ],
        ];
    }
}