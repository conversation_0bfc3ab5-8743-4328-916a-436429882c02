<?php
namespace app\common\model;

use app\common\basics\Models;

class CacheSearch extends Models
{
    const type = [
        1 => '增加索引',
        2 => '增加同义词',
    ];

    public function getTypeDescAttr($value)
    {
        return self::type[$value];
    }

    public function getLastTimeStrAttr($value)
    {
        return date('Y-m-d H:i:s', $value);
    }

    public function getExpressionAttr($value, $data)
    {
        if($data['type'] == 2) {
            return '-';
        }
        return $value;
    }
}