{"version": 3, "file": "components/deliver-search.js", "sources": ["webpack:///./components/deliver-search.vue?9a2d", "webpack:///./components/deliver-search.vue?f82a", "webpack:///./components/deliver-search.vue?a04e", "webpack:///./components/deliver-search.vue?6097", "webpack:///./components/deliver-search.vue", "webpack:///./components/deliver-search.vue?b76a", "webpack:///./components/deliver-search.vue?bc14"], "sourcesContent": ["// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/sass-resources-loader/lib/loader.js??ref--7-oneOf-1-4!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./deliver-search.vue?vue&type=style&index=0&id=79dec466&lang=scss&scoped=true&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"db2946c2\", content, true, context)\n};", "export * from \"-!../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/sass-resources-loader/lib/loader.js??ref--7-oneOf-1-4!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./deliver-search.vue?vue&type=style&index=0&id=79dec466&lang=scss&scoped=true&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".deliver-search-container .deliver-box .deliver-recode-box[data-v-79dec466]{padding:10px 20px;background-color:#f2f2f2}.deliver-search-container .deliver-box .deliver-recode-box .recode-img[data-v-79dec466]{position:relative;width:72px;height:72px}.deliver-search-container .deliver-box .deliver-recode-box .recode-img .float-count[data-v-79dec466]{position:absolute;bottom:0;height:20px;width:100%;background-color:rgba(0,0,0,.5);color:#fff;font-size:12px}.deliver-search-container .deliver-box .deliver-recode-box .recode-info-container[data-v-79dec466]{flex:1}.deliver-search-container .deliver-box .deliver-recode-box .recode-info-container .recode-label[data-v-79dec466]{width:70px}.deliver-search-container .deliver-box .deliver-recode-box .recode-info-container .copy-btn[data-v-79dec466]{height:20px;min-width:42px;border:1px solid #ff2c3c;font-size:12px;margin-left:8px;border-radius:60px;cursor:pointer}.deliver-search-container .deliver-box .deliver-recode-box .recode-info-container .copy-btn[data-v-79dec466]:hover{background-color:#fff}.deliver-search-container .deliver-box .deliver-flow-box[data-v-79dec466]{padding-left:15px}.deliver-search-container .deliver-box .time-line-title[data-v-79dec466]{font-weight:500px;font-size:16px;margin-bottom:10px}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"deliver-search-container\"},[_c('el-dialog',{attrs:{\"visible\":_vm.showDialog,\"top\":\"30vh\",\"width\":\"900px\",\"title\":\"物流查询\"},on:{\"update:visible\":function($event){_vm.showDialog=$event}}},[_c('div',{staticClass:\"deliver-box\"},[_c('div',{staticClass:\"deliver-recode-box flex\"},[_c('div',{staticClass:\"recode-img\"},[_c('el-image',{staticStyle:{\"width\":\"100%\",\"height\":\"100%\"},attrs:{\"fit\":\"cover\",\"src\":_vm.deliverOrder.image}}),_vm._v(\" \"),_c('div',{staticClass:\"float-count flex row-center\"},[_vm._v(\"共\"+_vm._s(_vm.deliverOrder.count)+\"件商品\")])],1),_vm._v(\" \"),_c('div',{staticClass:\"recode-info-container m-l-10\"},[_c('div',{staticClass:\"flex\"},[_c('div',{staticClass:\"recode-label\"},[_vm._v(\"物流状态：\")]),_vm._v(\" \"),_c('div',{staticClass:\"primary lg\",staticStyle:{\"font-weight\":\"500\"}},[_vm._v(_vm._s(_vm.deliverOrder.tips))])]),_vm._v(\" \"),_c('div',{staticClass:\"flex\",staticStyle:{\"margin\":\"6px 0\"}},[_c('div',{staticClass:\"recode-label\"},[_vm._v(\"快递公司：\")]),_vm._v(\" \"),_c('div',[_vm._v(_vm._s(_vm.deliverOrder.shipping_name))])]),_vm._v(\" \"),_c('div',{staticClass:\"flex\"},[_c('div',{staticClass:\"recode-label\"},[_vm._v(\"快递单号：\")]),_vm._v(\" \"),_c('div',[_vm._v(_vm._s(_vm.deliverOrder.invoice_no))]),_vm._v(\" \"),_c('div',{staticClass:\"copy-btn primary flex row-center\",on:{\"click\":_vm.onCopy}},[_vm._v(\"复制\")])])])]),_vm._v(\" \"),_c('div',{staticClass:\"deliver-flow-box m-t-16\"},[_c('el-timeline',[(_vm.deliverFinish.tips)?_c('el-timeline-item',[_c('div',[_c('div',{staticClass:\"flex lg\"},[_c('div',{staticClass:\"m-r-8\",staticStyle:{\"font-weight\":\"500\"}},[_vm._v(\"\\n                                    \"+_vm._s(_vm.deliverTake.contacts)+\"\\n                                \")]),_vm._v(\" \"),_c('div',{staticStyle:{\"font-weight\":\"500\"}},[_vm._v(_vm._s(_vm.deliverTake.mobile))])]),_vm._v(\" \"),_c('div',{staticClass:\"lighter m-t-8\"},[_vm._v(_vm._s(_vm.deliverTake.address))])])]):_vm._e(),_vm._v(\" \"),(_vm.deliverFinish.tips)?_c('el-timeline-item',{attrs:{\"timestamp\":_vm.deliverFinish.time}},[_c('div',{staticClass:\"time-line-title\"},[_vm._v(_vm._s(_vm.deliverFinish.title))]),_vm._v(\" \"),_c('div',[_vm._v(_vm._s(_vm.deliverFinish.tips))])]):_vm._e(),_vm._v(\" \"),(_vm.delivery.traces && _vm.delivery.traces.length)?_c('el-timeline-item',{attrs:{\"timestamp\":_vm.delivery.time}},[_c('div',{staticClass:\"time-line-title m-b-8\"},[_vm._v(_vm._s(_vm.delivery.title))]),_vm._v(\" \"),_vm._l((_vm.delivery.traces),function(item,index){return _c('el-timeline-item',{key:index,attrs:{\"timestamp\":item[0]}},[_c('div',{staticClass:\"muted\"},[_vm._v(_vm._s(item[1]))])])})],2):_vm._e(),_vm._v(\" \"),(_vm.deliverShipment.tips)?_c('el-timeline-item',{attrs:{\"timestamp\":_vm.deliverShipment.time}},[_c('div',{staticClass:\"time-line-title\"},[_vm._v(_vm._s(_vm.deliverShipment.title))]),_vm._v(\" \"),_c('div',[_vm._v(_vm._s(_vm.deliverShipment.tips))])]):_vm._e(),_vm._v(\" \"),(_vm.deliverBuy.tips)?_c('el-timeline-item',{attrs:{\"timestamp\":_vm.deliverBuy.time}},[_c('div',{staticClass:\"time-line-title\"},[_vm._v(_vm._s(_vm.deliverBuy.title))]),_vm._v(\" \"),_c('div',[_vm._v(_vm._s(_vm.deliverBuy.tips))])]):_vm._e()],1)],1)])])],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nexport default {\n    props: {\n        value: {\n            type: Boolean,\n            default: false,\n        },\n        aid: {\n            type: Number | String\n        }\n    },\n    data() {\n        return {\n            showDialog: false,\n            deliverBuy: {},\n            delivery: {},\n            deliverFinish: {},\n            deliverOrder: {},\n            deliverShipment: {},\n            deliverTake: {},\n            timeLineArray: []\n        }\n    },\n    watch: {\n        value(val) {\n            console.log(val, 'val')\n            this.showDialog = val;\n        },\n        showDialog(val) {\n            if(val) {\n                if(this.aid) {\n                    this.timeLineArray = []\n                    this.getDeliverTraces();\n                }\n            }\n            this.$emit(\"input\", val);\n        },\n    },\n    methods: {\n        async getDeliverTraces() {\n            let data = {\n                id: this.aid\n            }\n            let res = await this.$get(\"order/orderTraces\", {params: data});\n            if(res.code == 1) {\n                let {buy, delivery, finish, order, shipment, take} = res.data\n                this.deliverBuy = buy;\n                this.delivery = delivery;\n                this.deliverFinish = finish;\n                this.deliverOrder = order;\n                this.deliverShipment = shipment;\n                this.deliverTake = take;\n                this.timeLineArray.push(this.deliverFinish);\n                this.timeLineArray.push(this.delivery);\n                this.timeLineArray.push(this.deliverShipment);\n                this.timeLineArray.push(this.deliverBuy);\n                console.log(this.timeLineArray)\n            }\n        },\n        onCopy() {\n            // this.deliverOrder.invoice_no;\n            let oInput = document.createElement('input');\n            oInput.value = this.deliverOrder.invoice_no;\n            document.body.appendChild(oInput);\n            oInput.select();\n            document.execCommand(\"Copy\");\n            this.$message.success(\"复制成功\");\n            oInput.remove();\n        }\n    }\n}\n", "import mod from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./deliver-search.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./deliver-search.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./deliver-search.vue?vue&type=template&id=79dec466&scoped=true&\"\nimport script from \"./deliver-search.vue?vue&type=script&lang=js&\"\nexport * from \"./deliver-search.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./deliver-search.vue?vue&type=style&index=0&id=79dec466&lang=scss&scoped=true&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  \"79dec466\",\n  \"0d71d492\"\n  \n)\n\nexport default component.exports"], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACXA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACNA;AACA;AACA;;;;;ACFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AADA;AALA;AACA;AAQA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AARA;AAUA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AAdA;AAeA;AACA;AACA;AACA;AADA;AAGA;AAAA;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AA/BA;AArCA;;ACvFA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;A", "sourceRoot": ""}