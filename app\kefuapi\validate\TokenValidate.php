<?php


namespace app\kefuapi\validate;


use app\common\basics\Validate;
use app\common\model\Admin;
use app\common\model\kefu\Kefu;
use app\common\model\kefu\KefuSession;
use app\common\model\shop\ShopAdmin;

/**
 * 客服登录验证
 * Class TokenValidate
 * @package app\kefu\validate
 */
class TokenValidate extends Validate
{
    protected $rule = [
        'token' => 'require|valid|chat',
    ];

    /**
     * @notes token验证
     * @param $token
     * @param $other
     * @param $data
     * @return bool|string
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date 2021/11/23 10:55
     */
    protected function valid($token, $other, $data)
    {
        $session = KefuSession::where(['token' => $token])->find();
        if (empty($session)) {
            return '会话失效，请重新登录';
        }
        if ($session['expire_time'] <= time()) {
            return '登录超时，请重新登录';
        }
        return true;
    }


    /**
     * @notes 用户验证
     * @param $token
     * @param $other
     * @param $data
     * @return bool|string
     * <AUTHOR>
     * @date 2021/11/23 17:29
     */
    protected function chat($token, $other, $data)
    {
        $kefu = (new Kefu())->alias('k')
            ->join('kefu_session ks', 'k.id = ks.kefu_id')
            ->where(['ks.token' => $token, 'k.del' => 0])
            ->field('k.*,ks.token,ks.client')
            ->hidden(['password'])
            ->findOrEmpty();

        if ($kefu->isEmpty()) {
            return '用户不存在';
        }

        // 获取客服对应的管理员信息
        if ($kefu['shop_id'] > 0) {
            $kefu_admin = (new ShopAdmin())->where(['id' => $kefu['admin_id']])->findOrEmpty();
        } else {
            $kefu_admin = (new Admin())->where(['id' => $kefu['admin_id']])->findOrEmpty();
        }

        if ($kefu_admin->isEmpty()) {
            return '关联管理员不存在';
        }

        if ($kefu['disable'] == 1 || $kefu_admin['disable'] == 1) {
            return '用户被禁用';
        }

        return true;
    }


}