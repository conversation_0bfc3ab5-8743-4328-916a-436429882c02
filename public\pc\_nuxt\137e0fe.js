(window.webpackJsonp=window.webpackJsonp||[]).push([[20],{530:function(e,t,n){var content=n(555);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[e.i,content,""]]),content.locals&&(e.exports=content.locals);(0,n(17).default)("1a750d06",content,!0,{sourceMap:!1})},554:function(e,t,n){"use strict";n(530)},555:function(e,t,n){var r=n(16)(!1);r.push([e.i,".server-explain-header[data-v-aa7ce78c]{margin-bottom:10px;cursor:pointer;line-height:30px}.server-explain-content[data-v-aa7ce78c]{height:400px;overflow-y:auto}.server-explain[data-v-aa7ce78c] .el-dialog__header{display:none}.server-explain[data-v-aa7ce78c] .el-dialog{top:50%;transform:translateY(-50%);border-radius:6px}.server-explain-footer[data-v-aa7ce78c]{margin-top:20px}",""]),e.exports=r},560:function(e,t,n){"use strict";n.r(t);var r=n(9),o=(n(53),{asyncData:function(e){return Object(r.a)(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:e.$get;case 1:case"end":return t.stop()}}),t)})))()},data:function(){return{content:"",dialogTableVisible:!1}},methods:{onConfirm:function(){localStorage.setItem("FIRSTENTRY",!0),this.dialogTableVisible=!1},getPolicyServerFunc:function(){var e=this;return Object(r.a)(regeneratorRuntime.mark((function t(){var n,data;return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,e.$get("policy/service");case 2:n=t.sent,data=n.data,e.content=data.content;case 5:case"end":return t.stop()}}),t)})))()},jumpUrl:function(e){window.open("server_explan?type=".concat(e),"_blank")}},mounted:function(){try{var e;null==(null!==(e=JSON.parse(localStorage.getItem("FIRSTENTRY")))&&void 0!==e?e:null)&&(this.dialogTableVisible=!0,this.getPolicyServerFunc())}catch(e){console.log(e)}}}),c=(n(554),n(8)),component=Object(c.a)(o,(function(){var e=this,t=e._self._c;return t("div",{staticClass:"server-explain"},[t("el-dialog",{attrs:{visible:e.dialogTableVisible,width:"800px","show-close":!1,"close-on-press-escape":!1,"close-on-click-modal":!1,top:"0"},on:{"update:visible":function(t){e.dialogTableVisible=t}}},[t("div",{staticClass:"server-explain-header"},[t("div",{staticClass:"lg"},[e._v("\n                协议声明\n            ")]),e._v(" "),t("div",{staticClass:"m-t-16 nr"},[t("span",{staticClass:"primary",attrs:{to:"/server_explan?type=1"},on:{click:function(t){return e.jumpUrl(1)}}},[e._v("《服务协议》、")]),t("span",{staticClass:"primary",attrs:{to:"/server_explan?type=2"},on:{click:function(t){return e.jumpUrl(2)}}},[e._v("《隐私政策》")]),e._v("\n                请您仔细阅读以上协议，其中有对您权利义务的特别约定等重要条款，请点击同意后方可使用本软件\n            ")])]),e._v(" "),t("div",{staticClass:"server-explain-content"},[t("div",{domProps:{innerHTML:e._s(e.content)}})]),e._v(" "),t("div",{staticClass:"server-explain-footer flex row-center"},[t("el-button",{attrs:{type:"primary"},on:{click:e.onConfirm}},[e._v("同意并继续")])],1)])],1)}),[],!1,null,"aa7ce78c",null);t.default=component.exports}}]);