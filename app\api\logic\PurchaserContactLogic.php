<?php
namespace app\api\logic;

use app\common\basics\Logic;
use app\common\model\PurchaserContactRecord;
use app\common\model\community\CommunityArticle;
use app\common\model\user\User;
use app\common\server\UrlServer;
use think\facade\Db;

/**
 * 采购商联系记录业务逻辑
 * Class PurchaserContactLogic
 * @package app\api\logic
 */
class PurchaserContactLogic extends Logic
{
    /**
     * 记录联系采购商
     * @param int $userId 用户ID
     * @param int $purchaserId 采购商ID
     * @param array $clientInfo 客户端信息
     * @return array|false
     */
    public static function recordContact($userId, $purchaserId, $clientInfo = [])
    {
        try {
            // 验证采购商是否存在
            $purchaser = CommunityArticle::where(['id' => $purchaserId, 'del' => 0])
                ->field('id,user_id,content')
                ->findOrEmpty();
            
            if ($purchaser->isEmpty()) {
                self::$error = '采购商不存在';
                return false;
            }
            
            // 验证用户是否存在
            $user = User::where(['id' => $userId, 'del' => 0])
                ->field('id,nickname,mobile')
                ->findOrEmpty();
            
            if ($user->isEmpty()) {
                self::$error = '用户不存在';
                return false;
            }
            
            // 检查是否在短时间内重复联系（防止刷接口）
            $recentContact = PurchaserContactRecord::where([
                'user_id' => $userId,
                'purchaser_id' => $purchaserId,
                'contact_time' => ['>', time() - 60] // 1分钟内
            ])->findOrEmpty();
            
            if (!$recentContact->isEmpty()) {
                self::$error = '请勿频繁联系，请稍后再试';
                return false;
            }
            
            // 记录联系
            $contactTime = time();
            $data = [
                'user_id' => $userId,
                'purchaser_id' => $purchaserId,
                'contact_time' => $contactTime,
                'ip' => $clientInfo['ip'] ?? '',
                'user_agent' => $clientInfo['user_agent'] ?? '',
                'source' => $clientInfo['source'] ?? 'web',
                'create_time' => $contactTime,
                'update_time' => $contactTime
            ];
            
            $result = PurchaserContactRecord::create($data);
            
            if ($result) {
                return [
                    'id' => $result->id,
                    'contact_time' => $contactTime,
                    'purchaser_info' => [
                        'id' => $purchaser->id,
                        'content' => $purchaser->content
                    ]
                ];
            }
            
            self::$error = '记录失败';
            return false;
            
        } catch (\Exception $e) {
            self::$error = '系统错误：' . $e->getMessage();
            return false;
        }
    }

    /**
     * 获取用户的联系记录列表
     * @param int $userId 用户ID
     * @param int $page 页码
     * @param int $limit 每页数量
     * @param array $params 查询参数
     * @return array|false
     */
    public static function getUserContactRecords($userId, $page = 1, $limit = 15, $params = [])
    {
        try {
            $where = ['pcr.user_id' => $userId];
            
            // 时间范围筛选
            if (!empty($params['start_time'])) {
                $where[] = ['pcr.contact_time', '>=', strtotime($params['start_time'])];
            }
            if (!empty($params['end_time'])) {
                $where[] = ['pcr.contact_time', '<=', strtotime($params['end_time'])];
            }
            
            $model = PurchaserContactRecord::alias('pcr')
                ->leftJoin('community_article ca', 'pcr.purchaser_id = ca.id')
                ->leftJoin('user u', 'ca.user_id = u.id')
                ->where($where)
                ->field('pcr.*,ca.content,ca.image,ca.wxqrcode,u.nickname as purchaser_nickname,u.avatar as purchaser_avatar,u.mobile as purchaser_mobile');
            
            $count = $model->count();
            $lists = $model->page($page, $limit)
                ->order('pcr.contact_time desc')
                ->select()
                ->toArray();
            
            foreach ($lists as &$item) {
                $item['contact_time_text'] = date('Y-m-d H:i:s', $item['contact_time']);
                $item['purchaser_avatar'] = UrlServer::getFileUrl($item['purchaser_avatar']);
                $item['image'] = UrlServer::getFileUrl($item['image']);
                $item['wxqrcode'] = UrlServer::getFileUrl($item['wxqrcode']);
            }
            
            return [
                'count' => $count,
                'lists' => $lists,
                'page' => $page,
                'limit' => $limit
            ];
            
        } catch (\Exception $e) {
            self::$error = '获取失败：' . $e->getMessage();
            return false;
        }
    }

    /**
     * 获取采购商的被联系记录
     * @param int $purchaserId 采购商ID
     * @param int $page 页码
     * @param int $limit 每页数量
     * @param array $params 查询参数
     * @return array|false
     */
    public static function getPurchaserContactRecords($purchaserId, $page = 1, $limit = 15, $params = [])
    {
        try {
            $where = ['pcr.purchaser_id' => $purchaserId];
            
            // 时间范围筛选
            if (!empty($params['start_time'])) {
                $where[] = ['pcr.contact_time', '>=', strtotime($params['start_time'])];
            }
            if (!empty($params['end_time'])) {
                $where[] = ['pcr.contact_time', '<=', strtotime($params['end_time'])];
            }
            
            $model = PurchaserContactRecord::alias('pcr')
                ->leftJoin('user u', 'pcr.user_id = u.id')
                ->where($where)
                ->field('pcr.*,u.nickname,u.avatar,u.mobile');
            
            $count = $model->count();
            $lists = $model->page($page, $limit)
                ->order('pcr.contact_time desc')
                ->select()
                ->toArray();
            
            foreach ($lists as &$item) {
                $item['contact_time_text'] = date('Y-m-d H:i:s', $item['contact_time']);
                $item['avatar'] = UrlServer::getFileUrl($item['avatar']);
            }
            
            return [
                'count' => $count,
                'lists' => $lists,
                'page' => $page,
                'limit' => $limit
            ];
            
        } catch (\Exception $e) {
            self::$error = '获取失败：' . $e->getMessage();
            return false;
        }
    }

    /**
     * 获取联系统计信息
     * @param int $userId 用户ID
     * @param int $purchaserId 采购商ID
     * @param array $params 查询参数
     * @return array|false
     */
    public static function getContactStats($userId = null, $purchaserId = null, $params = [])
    {
        try {
            $stats = [];
            
            // 用户联系统计
            if ($userId) {
                $userStats = PurchaserContactRecord::where('user_id', $userId);
                
                $stats['user_stats'] = [
                    'total_contacts' => $userStats->count(),
                    'today_contacts' => $userStats->where('contact_time', '>=', strtotime('today'))->count(),
                    'this_week_contacts' => $userStats->where('contact_time', '>=', strtotime('this week'))->count(),
                    'this_month_contacts' => $userStats->where('contact_time', '>=', strtotime('first day of this month'))->count(),
                ];
            }
            
            // 采购商被联系统计
            if ($purchaserId) {
                $purchaserStats = PurchaserContactRecord::where('purchaser_id', $purchaserId);
                
                $stats['purchaser_stats'] = [
                    'total_contacted' => $purchaserStats->count(),
                    'today_contacted' => $purchaserStats->where('contact_time', '>=', strtotime('today'))->count(),
                    'this_week_contacted' => $purchaserStats->where('contact_time', '>=', strtotime('this week'))->count(),
                    'this_month_contacted' => $purchaserStats->where('contact_time', '>=', strtotime('first day of this month'))->count(),
                ];
            }
            
            return $stats;
            
        } catch (\Exception $e) {
            self::$error = '获取统计失败：' . $e->getMessage();
            return false;
        }
    }
}
