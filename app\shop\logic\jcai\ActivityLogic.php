<?php


namespace app\shop\logic\jcai;


use app\common\basics\Logic;
use app\common\enum\GoodsEnum;
use app\common\enum\OrderEnum;
use app\common\enum\OrderLogEnum;
use app\common\enum\JcaiEnum as TeamEnum;
use app\common\logic\OrderRefundLogic;
use app\common\model\bargain\Bargain;
use app\common\model\goods\Goods;
use app\common\model\order\Order;
use app\common\model\seckill\SeckillGoods;
use app\common\model\jcai\JcaiActivity;
use app\common\model\jcai\JcaiFound;
use app\common\model\jcai\JcaiGoods;
use app\common\model\jcai\JcaiJoin;
use Exception;
use think\facade\Db;

class ActivityLogic extends Logic
{
    /**
     * @Notes: 获取集众筹活动
     * @Author: 张无忌
     * @param $get
     * @param $shop_id
     * @return array|bool
     */
    public static function lists($get, $shop_id)
    {
        try {

            $where[] = ['T.shop_id', '=', $shop_id];
            $where[] = ['T.del', '=', 0];
            if (!empty($get['datetime']) and $get['datetime']) {
                list($start, $end) = explode(' - ', $get['datetime']);
                $where[] = ['T.create_time', '>=', strtotime($start.' 00:00:00')];
                $where[] = ['T.create_time', '<=', strtotime($end.' 23:59:59')];
            }

            if (!empty($get['name']) and $get['name']) {
                $where[] = ['G.name', 'like', '%'.$get['name'].'%'];
            }

            if (isset($get['status']) and is_numeric($get['status'])) {
                $where[] = ['T.status', '=', intval($get['status'])];
            }

            if (isset($get['audit']) and $get['audit']!=='') {
                $where[] = ['T.audit', '=', $get['audit']];
            }

            $model = new JcaiActivity();
            $lists = $model->alias('T')->field(['T.*'])
                ->where($where)
                ->with(['goods'])
                ->join('goods G', 'G.id = T.goods_id')
                ->paginate([
                    'page' => $get['page'] ?? 1,
                    'list_rows' =>$get['limit'] ?? 20,
                    'var_page' => 'page'
                ])->toArray();

            $JcaiFoundModel = new JcaiFound();
            $JcaiJoinModel =  new JcaiJoin();
            foreach ($lists['data'] as &$item) {
                $item['activity_start_time'] = date('Y-m-d H:i', $item['activity_start_time']);
                $item['activity_end_time'] = date('Y-m-d H:i', $item['activity_end_time']);
                $item['status_text'] = TeamEnum::getTeamStatusDesc($item['status']);
                $item['audit_text'] = TeamEnum::getTeamAuditDesc($item['audit']);
                $item['team_count'] = $JcaiFoundModel->where(['jcai_activity_id'=>$item['id']])->count();
                $item['success_found'] = $JcaiFoundModel->where(['status'=>1, 'jcai_activity_id'=>$item['id']])->count();
                $item['join_found'] = $JcaiJoinModel->where(['jcai_activity_id'=>$item['id']])->count();
                $order_ids=Db::name('jcai_join')->where(['jcai_activity_id'=>$item['id']])->column('order_id');
                $item['order_total']=0;
                if($order_ids){
                    $item['order_total']=Db::name('order')->where(
                            'pay_status',1
                    )->whereIn('id',$order_ids)->sum('order_amount');
                }

            }

            return ['count'=>$lists['total'],'page'=>$get['page'],'limit'=>$get['limit'], 'lists'=>$lists['data'],'more'=>is_more($lists['total'], $get['page'], $get['limit'])];
        } catch (Exception $e) {
            static::$error = $e->getMessage();

            return false;
        }
    }

    /**
     * @Notes: 选择集众筹商品
     * @Author: 张无忌
     * @param $get
     * @param $shop_id
     * @return array
     */
    public static function select($get, $shop_id)
    {
        try {
            $where = [];
            if (!empty($get['name']) and $get['name']) {
                $where[] = ['name', 'like', '%'.$get['name'].'%'];
            }
            if (!empty($get['join_jc'])) {
                $where[] = ['join_jc', '=', $get['join_jc']];
            }
               
            $model = (new Goods());
            $lists = $model->field(['id,name,image,stock,max_price,min_price,market_price'])->where([
                ['shop_id', '=', $shop_id],
                ['audit_status', '=', 1],
                ['del', '=', 0],
                ['status', '=', 1],
                ['type', '=', GoodsEnum::TYPE_ACTUAL]
            ])->withAttr('is_activity', function ($value, $data) use($shop_id) {
                unset($value);
                // 是否是秒杀
                $seckill = (new SeckillGoods())->where(['goods_id'=>$data['id']])
                    ->where(['del'=>0, 'shop_id'=>$shop_id])
                    ->findOrEmpty()->toArray();
                if ($seckill) return '秒杀中';
                // 是否是砍价
                $bargain = (new Bargain())->where(['goods_id'=>$data['id']])
                    ->where('del', '=', 0)
                    ->where('shop_id', '=', $shop_id)
                    ->findOrEmpty()->toArray();
                if ($bargain) return '砍价中';

                return '正常';
            })->where($where)->with(['GoodsItem'])
                ->append(['is_activity'])
                ->paginate([
                    'page' => $get['page'] ?? 1,
                    'list_rows' => $get['limit'] ?? 20,
                    'var_page' => 'page'
                ])->toArray();

            return ['count'=>$lists['total'], 'lists'=>$lists['data']];
        } catch (Exception $e) {
            return ['error'=>$e->getMessage()];
        }
    }

    /**
     * @Notes: 数据统计
     * @Author: 张无忌
     * @param $shop_id
     * @return mixed
     */
    public static function statistics($shop_id)
    {
        $where[] = ['del', '=', 0];
        $where[] = ['shop_id', '=', $shop_id];

        $model = new JcaiActivity();
        $detail['total']       = $model->where($where)->count();
        $detail['stayAudit']   = $model->where($where)->where(['audit'=>0])->count();
        $detail['adoptAudit']  = $model->where($where)->where(['audit'=>1])->count();
        $detail['refuseAudit'] = $model->where($where)->where(['audit'=>2])->count();
        return $detail;
    }

    /**
     * @Notes: 集众筹活动详细
     * @Author: 张无忌
     * @param $id
     * @return array
     */
    public static function detail($id)
    {
        $model = new JcaiActivity();
        $detail = $model->field(true)
            ->with(['goods', 'teamGoods'])
            ->findOrEmpty($id)
            ->toArray();

        $detail['activity_start_time'] = date('Y-m-d H:i:s', $detail['activity_start_time']);
        $detail['activity_end_time'] = date('Y-m-d H:i:s', $detail['activity_end_time']);
        return $detail;
    }

    /**
     * @Notes: 新增集众筹活动
     * @Author: 张无忌
     * @param $post
     * @param $shop_id
     * @return bool
     */
    public static function add($post, $shop_id)
    {
        Db::startTrans();
        try {
            $goods = (new Goods())->findOrEmpty($post['goods_id'])->toArray();
            if (!$goods) throw new \think\Exception('选择的商品不存在');
            //如果没有加入拼单集采,需要通知
            if(empty($goods['join_jc'])){
                throw new \think\Exception('请先将产品加入到拼单集采中设置拼单集采价格');
            }
            $goods = (new JcaiActivity())->findOrEmpty($post['goods_id'])->toArray();
            if ($goods) throw new \think\Exception('选择的商品已存在');
            if (strtotime($post['activity_start_time']) >= strtotime($post['activity_end_time'])) {
                throw new \think\Exception('集众筹活动开始时间不能少于等于结束时间');
            }
            // 新增集众筹活动信息
            $team = JcaiActivity::create([
                'shop_id'        => $shop_id,
                'goods_id'       => $post['goods_id'],
                'share_title'    => $post['share_title'] ?? '',
                'share_intro'    => $post['share_intro'] ?? '',
                'intro'          =>       $post['intro'] ?? '',
                'team_max_price' => self::getMaxOrMinPrice($post)['max'],
                'team_min_price' => self::getMaxOrMinPrice($post)['min'],
                'audit'          => 0,
                'del'            => 0,
                'status'         => 1,
                'activity_start_time' => strtotime($post['activity_start_time']),
                'activity_end_time'   => strtotime($post['activity_end_time']),
                'create_time'         => time(),
                'buy_num'=>$post['buy_num']
            ]);

            // 新增集众筹商品规格
            $lists = [];
            foreach ($post['item'] as $key => $value) {
                foreach ($value as $K => $item) {
                    $lists[] = [
                        'jcai_id'    => $team['id'],
                        'goods_id'   => $key,
                        'item_id'    => $K,
                        'jcai_price' => $item
                    ];
                }
            }
            if (!empty($lists)) (new JcaiGoods())->insertAll($lists);

            Db::commit();
            return true;
        } catch (Exception $e) {
            Db::rollback();
            static::$error = $e->getMessage();
            return false;
        }
    }

    /**
     * @Notes: 编辑集众筹活动
     * @Author: 张无忌
     * @param $post
     * @param $shop_id
     * @return bool
     */
    public static function edit($post, $shop_id)
    {
        Db::startTrans();
        try {
            $goods = (new Goods())->findOrEmpty($post['goods_id'])->toArray();
            if (!$goods) throw new \think\Exception('选择的商品不存在');
            //如果没有加入拼单集采,需要通知
            if(empty($goods['join_jc'])){
                throw new \think\Exception('请先将产品加入到拼单集采中设置拼单集采价格');
            }
            if (strtotime($post['activity_start_time']) >= strtotime($post['activity_end_time'])) {
                throw new \think\Exception('团活动开始时间不能少于等于结束时间');
            }

            $activity = (new JcaiActivity())->findOrEmpty($post['id'])->toArray();
            $audit = $activity['audit'] != 1 ? 0 : 1;


            // 编辑集众筹活动信息
            JcaiActivity::update([
                'shop_id'             => $shop_id,
                'goods_id'            => $post['goods_id'],
                'people_num'          => $post['people_num'],
                'share_title'         => $post['share_title'] ?? '',
                'share_intro'         => $post['share_intro'] ?? '',
                'intro'          =>       $post['intro'] ?? '',
                'team_max_price'      => self::getMaxOrMinPrice($post)['max'],
                'team_min_price'      => self::getMaxOrMinPrice($post)['min'],
                'status'              => $post['status'],
                'audit'               => $audit,
                'effective_time'      => $post['effective_time'],
                'activity_start_time' => strtotime($post['activity_start_time']),
                'activity_end_time'   => strtotime($post['activity_end_time']),
            ], ['id'=>$post['id']]);

            // 删除旧的集众筹商品
            (new JcaiGoods())->where(['jcai_id'=>$post['id']])->delete();

            // 更新集众筹商品规格
            $lists = [];
            foreach ($post['item'] as $key => $value) {
                foreach ($value as $K => $item) {
                    $lists[] = [
                        'jcai_id'    => $post['id'],
                        'goods_id'   => $key,
                        'item_id'    => $K,
                        'jcai_price' => $item
                    ];
                }
            }
            if (!empty($lists)) (new JcaiGoods())->insertAll($lists);

            Db::commit();
            return true;
        } catch (Exception $e) {
            Db::rollback();
            static::$error = $e->getMessage();
            return false;
        }
    }

    /**
     * @Notes: 删除集众筹活动
     * @Author: 张无忌
     * @param $id
     * @return bool
     */
    public static function del($id)
    {
        try {
            JcaiActivity::update([
                'del' => 1,
                'update_time' => time()
            ], ['id'=>$id]);

            return true;
        } catch (Exception $e) {
            static::$error = $e->getMessage();
            return false;
        }
    }

    /**
     * @Notes: 停止集众筹活动
     * @Author: 张无忌
     * @param $id
     * @return bool
     */
    public static function stop($id)
    {
        try {
            JcaiActivity::update([
                'status' => 0,
                'update_time' => time()
            ], ['id'=>$id]);

            $team_ids = (new JcaiFound())->where(['jcai_activity_id'=>$id, 'status'=>0])->column('id');

            $JcaiJoin =  (new JcaiJoin())->alias('TJ')
                ->field(['TJ.*,O.order_sn,O.order_status,O.pay_status,O.refund_status,O.order_amount'])
                ->where('jcai_id', 'in', $team_ids)
                ->join('order O', 'O.id=TJ.order_id')
                ->select()->toArray();

            self::teamFail($JcaiJoin, $team_ids, time());

            return true;
        } catch (Exception $e) {
            static::$error = $e->getMessage();
            return false;
        }
    }

    /**
     * @Notes: 开启集众筹活动
     * @Author: 张无忌
     * @param $id
     * @return bool
     */
    public static function open($id)
    {
        try {
            JcaiActivity::update([
                'status' => 1,
                'update_time' => time()
            ], ['id'=>$id]);

            return true;
        } catch (Exception $e) {
            static::$error = $e->getMessage();
            return false;
        }
    }

    /**
     * @Notes: 查看最低活动价和最高活动价
     * @Author: 张无忌
     * @param $post
     * @return array
     */
    private static function getMaxOrMinPrice($post)
    {
        $team_price = [];
        foreach ($post['item'] as $key => $value) {
            foreach ($value as $K => $item) {
                array_push($team_price, $item);
            }
        }
        $team_max_price = !empty($team_price) ? max($team_price) : 0;
        $team_min_price = !empty($team_price) ? min($team_price) : 0;

        return [
            'max' => $team_max_price,
            'min' => $team_min_price
        ];
    }

    /**
     * @Notes: 集众筹失败
     * @Author: 张无忌
     * @param $JcaiJoin (参团列表数据)
     * @param $found_ids
     * @param $time (时间)
     * @throws \think\Exception
     */
    private static function teamFail($JcaiJoin, $found_ids, $time)
    {
        Db::startTrans();
        try {
            (new JcaiFound())->whereIn('id', $found_ids)
                ->update(['status'=>TeamEnum::TEAM_STATUS_FAIL, 'jcai_end_time'=>$time]);

            foreach ($JcaiJoin as $item) {
                JcaiJoin::update(['status' => TeamEnum::TEAM_STATUS_FAIL, 'update_time' => $time], ['id' => $item['id']]);
                if ($item['order_status'] == OrderEnum::ORDER_STATUS_DOWN) continue;
                if ($item['refund_status'] != OrderEnum::REFUND_STATUS_NO_REFUND) continue;
                $order = (new Order())->findOrEmpty($item['order_id'])->toArray();
                // 取消订单
                OrderRefundLogic::cancelOrder($order['id'], OrderLogEnum::TYPE_SYSTEM);
                if ($order['pay_status'] == OrderEnum::PAY_STATUS_PAID) {
                    // 更新订单状态
                    OrderRefundLogic::cancelOrderRefundUpdate($order);
                    // 订单退款
                    OrderRefundLogic::refund($order, $order['order_amount'], $order['order_amount']);
                }
            }
            Db::commit();
        } catch (Exception $e) {
            Db::rollback();
            throw new \think\Exception($e->getMessage());
        }
    }
}