<?php

namespace app\common\model\user;

use app\common\basics\Models;
use app\common\enum\UserActivityEnum;

/**
 * 用户活跃度日志模型
 * Class UserActivityLog
 * @package app\common\model\user
 */
class UserActivityLog extends Models
{
    /**
     * 获取活动类型描述
     * @param $value
     * @return string
     */
    public function getActivityTypeDescAttr($value, $data)
    {
        return UserActivityEnum::getActivityDesc($data['activity_type']);
    }

    /**
     * 获取创建时间格式化
     * @param $value
     * @return string
     */
    public function getCreateTimeAttr($value)
    {
        if (empty($value) || !is_numeric($value)) {
            return '';
        }
        return date('Y-m-d H:i:s', $value);
    }

    /**
     * 获取额外数据
     * @param $value
     * @return array
     */
    public function getExtraDataAttr($value)
    {
        return $value ? json_decode($value, true) : [];
    }

    /**
     * 设置额外数据
     * @param $value
     * @return string
     */
    public function setExtraDataAttr($value)
    {
        return is_array($value) ? json_encode($value, JSON_UNESCAPED_UNICODE) : $value;
    }

    /**
     * 关联用户模型
     * @return \think\model\relation\BelongsTo
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    /**
     * 获取积分变化文本
     * @param $value
     * @param $data
     * @return string
     */
    public function getScoreChangeTextAttr($value, $data)
    {
        $change = $data['score_change'];
        if ($change > 0) {
            return '+' . $change;
        } elseif ($change < 0) {
            return $change;
        } else {
            return '0';
        }
    }

    /**
     * 获取等级变化文本
     * @param $value
     * @param $data
     * @return string
     */
    public function getLevelChangeTextAttr($value, $data)
    {
        $before = UserActivityEnum::getLevelName($data['before_level']);
        $after = UserActivityEnum::getLevelName($data['after_level']);
        
        if ($data['before_level'] != $data['after_level']) {
            return $before . ' → ' . $after;
        }
        
        return $before;
    }
}
