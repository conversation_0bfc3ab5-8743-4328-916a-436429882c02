<?php

namespace Swoole;

class Atomic
{

    public function __construct($value = null)
    {
    }

    /**
     * @return mixed
     */
    public function add($add_value = null)
    {
    }

    /**
     * @return mixed
     */
    public function sub($sub_value = null)
    {
    }

    /**
     * @return mixed
     */
    public function get()
    {
    }

    /**
     * @return mixed
     */
    public function set($value)
    {
    }

    /**
     * @return mixed
     */
    public function wait($timeout = null)
    {
    }

    /**
     * @return mixed
     */
    public function wakeup($count = null)
    {
    }

    /**
     * @return mixed
     */
    public function cmpset($cmp_value, $new_value)
    {
    }


}
