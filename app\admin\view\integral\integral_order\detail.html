{layout name="layout2" /}
<style>
    .div-flex {
        display: flex;
        align-items: center;
        justify-content: left;
    }

    .width-160 {
        width: 200px;
    }
    .layui-table th {
        text-align: center;
    }
    .table-margin{
        margin-left: 50px;
        margin-right: 50px;
        text-align: center;
    }
    .image{
        height:80px;
        width: 80px;
    }

    .mt50{
        margin-left: 50px;
    }

</style>

<div class="layui-card-body" >
    <!--基本信息-->
    <div class="layui-form" lay-filter="layuiadmin-form-order" id="layuiadmin-form-order" >
    <input type="hidden" class="id" name="id" value="{$detail.id}">

    <div class="layui-form-item">
        <fieldset class="layui-elem-field layui-field-title">
            <legend>订单信息</legend>
        </fieldset>
    </div>

    <div class="layui-form-item div-flex">
        <label class="layui-form-label ">订单编号:</label>
        <div class="width-160">{$detail.order_sn}</div>
        <label class="layui-form-label ">兑换类型:</label>
        <div class="width-160">{$detail.type_desc}</div>
        <label class="layui-form-label ">下单时间:</label>
        <div class="width-160">{$detail.create_time}</div>
    </div>

    <div class="layui-form-item div-flex">
        <label class="layui-form-label ">支付状态:</label>
        <div class="width-160">{$detail.pay_status_desc}</div>
        <label class="layui-form-label ">订单状态:</label>
        <div class="width-160">{$detail.order_status_desc}</div>
        <label class="layui-form-label ">支付方式:</label>
        <div class="width-160">{$detail.pay_way_desc}</div>
    </div>

    <div class="layui-form-item div-flex">
        <label class="layui-form-label ">支付时间:</label>
        <div class="width-160">{$detail.pay_time}</div>
        <label class="layui-form-label ">完成时间:</label>
        <div class="width-160">{$detail.confirm_time}</div>
    </div>

    <div class="layui-form-item">
        <fieldset class="layui-elem-field layui-field-title">
            <legend>会员信息</legend>
        </fieldset>
    </div>

    <div class="layui-form-item div-flex">
        <label class="layui-form-label ">会员编号:</label>
        <div class="width-160">{$detail.user.sn}</div>
        <label class="layui-form-label ">用户昵称:</label>
        <div class="width-160">{$detail.user.nickname}</div>
        <label class="layui-form-label ">手机号码:</label>
        <div class="width-160">{$detail.user.mobile}</div>
    </div>

    <div class="layui-form-item div-flex">
        <label class="layui-form-label ">性别:</label>
        <div class="width-160">{$detail.user.sex}</div>
        <label class="layui-form-label ">注册时间:</label>
        <div class="width-160">{$detail.user.create_time}</div>
    </div>


    <div class="layui-form-item">
        <fieldset class="layui-elem-field layui-field-title">
            <legend>收货信息</legend>
        </fieldset>
    </div>

    <div class="layui-form-item div-flex">
        <label class="layui-form-label ">收货人:</label>
        <div class="width-160">{$detail.consignee}</div>
        <label class="layui-form-label ">手机号:</label>
        <div class="width-160">{$detail.mobile}</div>
        <label class="layui-form-label ">收货地址:</label>
        <div class="width-160">{$detail.delivery_address}</div>
    </div>


    <div class="layui-form-item">
        <fieldset class="layui-elem-field layui-field-title">
            <legend>商品信息</legend>
        </fieldset>
    </div>

    <div class="layui-form-item table-margin">
        <table class="layui-table">
                <colgroup>
                    <col width="250">
                    <col width="100">
                    <col width="200">
                    <col width="100">
                    <col width="100">
                    <col width="200">
                </colgroup>
                <thead>
                <tr>
                    <th>商品名称</th>
                    <th>市场价</th>
                    <th>兑换积分</th>
                    <th>数量</th>
                    <th>运费</th>
                    <th>实付</th>
                </tr>
                </thead>
                <tbody>
                <tr>
                    <td>
                        <div style="text-align: left">
                            <div class="layui-col-md4" >
                                <img src="{$detail.goods_snap.image}" class="image-show image" >
                            </div>
                            <div class="layui-col-md8" >
                                <p style="margin-top: 10px; margin-left: 10px">{$detail.goods_snap.name}</p>
                            </div>
                        </div>
                    </td>
                    <td>￥{$detail.goods_snap.market_price}</td>
                    <td>{$detail.goods_snap.need_integral}积分{if $detail.goods_snap.exchange_way == 2}+{$detail.goods_snap.need_money}元{/if}</td>
                    <td>{$detail.total_num}</td>
                    <td>￥{$detail.shipping_price}</td>
                    <td>{$detail.order_integral}积分{if $detail.order_amount > 0}+{$detail.order_amount}元{/if}</td>
                </tr>
                </tbody>
            </table>
    </div>

    <div class="layui-form-item">
        <fieldset class="layui-elem-field layui-field-title">
            <legend>买家留言</legend>
        </fieldset>
    </div>

    <div class="layui-form-item table-margin">
        <textarea class="layui-textarea" disabled>{$detail.user_remark}</textarea>
    </div>


    <div class="layui-form-item">
        <fieldset class="layui-elem-field layui-field-title">
            <legend>订单操作</legend>
        </fieldset>
    </div>

    <div class="layui-form-item div-flex ">
        <div class="layui-input-block ">
            {if $detail.order_status == 1 && $detail.delivery_way == 1}
            <button type="button" class="layui-btn layui-btn-sm layui-btn-normal width_160 " id="delivery">发货</button>
            {/if}
            {if $detail.order_status == 2 && $detail.delivery_way == 1}
            <button type="button" class="layui-btn layui-btn-sm layui-btn-normal width_160 " id="confirm">确认收货</button>
            {/if}
            <button type="button" class="layui-btn layui-btn-sm layui-btn-primary width_160 " id="back">返回</button>
        </div>
    </div>


</div>
</div>

<script type="text/javascript">
    //注意：选项卡 依赖 element 模块，否则无法进行功能性操作

    layui.config({
        version:"{$front_version}",
        base: '/static/lib/' //静态资源所在路径
    }).use(['form'], function(){
        var $ = layui.$;

        //主图放大
        $(document).on('click', '.image-show', function () {
            var src = $(this).attr('src');
            like.showImg(src,400);
        });


        $('#back').click(function () {
            var index=parent.layer.getFrameIndex(window.name); //获取当前窗口的name
            parent.layer.close(index);
            parent.layui.table.reload('order-lists');
            return true;
        });

        //发货
        $('#delivery').click(function () {
            var id = $('.id').val();
            layer.open({
                type: 2
                ,title: '订单发货'
                ,content: '{:url("integral.IntegralOrder/delivery")}?id='+id
                ,area: ['90%', '90%']
                ,yes: function(index, layero){
                }
            })
        });

        //确认收货
        $('#confirm').click(function () {
            var id = $('.id').val();
            layer.confirm('确认订单商品已收货吗？?', {
                btn: ['确认','取消'] //按钮
            }, function(){
                like.ajax({
                    url: '{:url("integral.IntegralOrder/confirm")}'
                    , data: {'id': id}
                    , type: 'post'
                    , success: function (res) {
                        if (res.code == 1) {
                            layui.layer.msg(res.msg, {
                                offset: '15px'
                                , icon: 1
                                , time: 1000
                            },function () {
                                location.reload();
                            });
                        }
                    },
                });
            });
        });

    });
</script>