{layout name="layout2" /}
<link href="__PUBLIC__/static/lib/layui/layeditor/layedit.css" rel="stylesheet"/>
<script src="__PUBLIC__/static/lib/layui/layeditor/index.js"></script>
<script src="__PUBLIC__/static/lib/layui/layeditor/ace/ace.js"></script>
<style>
    .layui-form-item .layui-input-inline { width: 340px; }
</style>

<div class="layui-card layui-form" style="box-shadow:none;">
    <div class="layui-card-body">
        <div class="layui-form-item">
            <label for="title" class="layui-form-label"><span style="color:red;">*</span>文章标题：</label>
            <div class="layui-input-inline">
                <input type="text" name="title" id="title" value="{$detail.title}" lay-verType="tips" lay-verify="required" autocomplete="off" class="layui-input">
            </div>
        </div>

        <div class="layui-form-item">
            <label for="cid" class="layui-form-label"><span style="color:red;">*</span>文章分类：</label>
            <div class="layui-input-inline">
                <select name="cid" id="cid" lay-verType="tips" lay-verify="required">
                    <option value="">全部</option>
                    {volist name="category" id="vo"}
                        <option value="{$vo.id}" {if $detail.cid==$vo.id}selected{/if}>{$vo.name}</option>
                    {/volist}
                </select>
            </div>
        </div>

        <div class="layui-form-item">
            <label for="intro" class="layui-form-label">文章简介：</label>
            <div class="layui-input-inline">
                <input type="text" name="intro" id="intro" value="{$detail.intro}" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label for="intro" class="layui-form-label" >公众号链接：</label>
            <div class="layui-input-inline">
                <input type="text" name="wechatUrl" value="{$detail.wechatUrl}" id="wechatUrl" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label for="sort" class="layui-form-label">文章排序：</label>
            <div class="layui-input-inline">
                <input type="text" name="sort" id="sort" value="{$detail.sort}" autocomplete="off" class="layui-input">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">封面图：</label>
            <div class="layui-input-block">
                <div class="like-upload-image">
                    {if $detail.image}
                    {foreach $detail.image as $item => $val}
                        <div class="upload-image-div">

                            <img src="{$val}" alt="img">
                            <input type="hidden" name="image[{$item}]" value="{$val}">
                            <div class="del-upload-btn">x</div>

                        </div>
                    {/foreach}
                        <div class="upload-image-elem" style="display:none;"><a class="add-upload-image"> + 添加图片</a></div>
                    {else}
                        <div class="upload-image-elem"><a class="add-upload-image"> + 添加图片</a></div>
                    {/if}
                </div>
                <div class="layui-form-mid layui-word-aux">建议尺寸：200*150像素</div>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">商城公告：</label>
            <div class="layui-input-inline">
                <input type="radio" name="is_notice" value="1" title="是" {if $detail.is_notice==1}checked{/if}>
                <input type="radio" name="is_notice" value="0" title="否" {if $detail.is_notice==0}checked{/if}>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">文章状态：</label>
            <div class="layui-input-inline">
                <input type="radio" name="is_show" value="1" title="显示" {if $detail.is_show==1}checked{/if}>
                <input type="radio" name="is_show" value="0" title="隐藏" {if $detail.is_show==0}checked{/if}>
            </div>
        </div>
        <div class="layui-form-item">
            <label for="content" class="layui-form-label">文章内容：</label>
            <div class="layui-input-block">
                <textarea name="content" id="content" lay-verify="content">{$detail.content|raw}</textarea>
            </div>
        </div>

        <div class="layui-form-item layui-hide">
            <input type="button" lay-submit lay-filter="addSubmit" id="addSubmit" value="确认">
        </div>
    </div>
</div>

<script>
    layui.config({
        base: "/static/lib/"
    }).extend({
        likeedit: "likeedit/likeedit"
    }).use(["layEditor", "form"], function(){
        var form = layui.form;
        var layEditor = layui.layEditor;
        layEditor.set({
            uploadImage: {
                url: '{:url("file/lists")}?type=10'
            },
        })
        var ieditor = layEditor.build('content')
        form.verify({
            content: function(value) {
                return layEditor.sync(ieditor);
            }
        });

        like.delUpload();
        $(document).on("click", ".add-upload-image", function () {
            like.imageUpload({
                limit: 100,
                field: "image[]",
                that: $(this)
            });
        });
    })
</script>