

<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>树形组件</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
  <link rel="stylesheet" href="../../../layuiadmin/layui/css/layui.css" media="all">
  <link rel="stylesheet" href="../../../layuiadmin/style/admin.css" media="all">
</head>
<body>

  <div class="layui-card layadmin-header">
    <div class="layui-breadcrumb" lay-filter="breadcrumb">
      <a lay-href="">主页</a>
      <a><cite>组件</cite></a>
      <a><cite>树形组件</cite></a>
    </div>
  </div>

  <div class="layui-fluid">
    <div class="layui-row layui-col-space15">
      <div class="layui-col-md12">
        <div class="layui-card">
          <div class="layui-card-header">基本演示</div>
          <div class="layui-card-body">
            <div class="layui-btn-container">
              <button type="button" class="layui-btn layui-btn-sm" lay-demo="getChecked">获取选中节点数据</button>
              <button type="button" class="layui-btn layui-btn-sm" lay-demo="setChecked">勾选指定节点</button>
              <button type="button" class="layui-btn layui-btn-sm" lay-demo="reload">重载实例</button>
            </div>
            <div id="test-tree-demo1"></div>
          </div>
        </div>
        <div class="layui-card">
          <div class="layui-card-header">常规用法</div>
          <div class="layui-card-body">
            <div id="test-tree-demo2"></div>
          </div>
        </div>
        <div class="layui-card">
          <div class="layui-card-header">无连接线风格</div>
          <div class="layui-card-body">
            <div id="test-tree-demo3"></div>
          </div>
        </div>
        <div class="layui-card">
          <div class="layui-card-header">仅节点左侧图标控制收缩</div>
          <div class="layui-card-body">
            <div id="test-tree-demo4"></div>
          </div>
        </div>
        <div class="layui-card">
          <div class="layui-card-header">手风琴模式</div>
          <div class="layui-card-body">
            <div id="test-tree-demo5"></div>
          </div>
        </div>
        <div class="layui-card">
          <div class="layui-card-header">点击节点新窗口跳转</div>
          <div class="layui-card-body">
            <div id="test-tree-demo6"></div>
          </div>
        </div>
        <div class="layui-card">
          <div class="layui-card-header">开启复选框</div>
          <div class="layui-card-body">
            <div id="test-tree-demo7"></div>
          </div>
        </div>
        <div class="layui-card">
          <div class="layui-card-header">开启节点操作图标</div>
          <div class="layui-card-body">
            <div id="test-tree-demo8"></div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script src="../../../layuiadmin/layui/layui.js"></script>  
  <script>
  layui.config({
    base: '../../../layuiadmin/' //静态资源所在路径
  }).extend({
    index: 'lib/index' //主入口模块
  }).use(['index', 'tree', 'util'], function(){
    var tree = layui.tree
    ,layer = layui.layer
    ,util = layui.util
    
    //模拟数据
    ,data = [{
      title: '一级1'
      ,id: 1
      ,field: 'name1'
      ,checked: true
      ,spread: true
      ,children: [{
        title: '二级1-1 可允许跳转'
        ,id: 3
        ,field: 'name11'
        ,href: 'https://www.layui.com/'
        ,children: [{
          title: '三级1-1-3'
          ,id: 23
          ,field: ''
          ,children: [{
            title: '四级1-1-3-1'
            ,id: 24
            ,field: ''
            ,children: [{
              title: '五级1-1-3-1-1'
              ,id: 30
              ,field: ''
            },{
              title: '五级1-1-3-1-2'
              ,id: 31
              ,field: ''
            }]
          }]
        },{
          title: '三级1-1-1'
          ,id: 7
          ,field: ''
          ,children: [{
            title: '四级1-1-1-1 可允许跳转'
            ,id: 15
            ,field: ''
            ,href: 'https://www.layui.com/doc/'
          }]
        },{
          title: '三级1-1-2'
          ,id: 8
          ,field: ''
          ,children: [{
            title: '四级1-1-2-1'
            ,id: 32
            ,field: ''
          }]
        }]
      },{
        title: '二级1-2'
        ,id: 4
        ,spread: true
        ,children: [{
          title: '三级1-2-1'
          ,id: 9
          ,field: ''
          ,disabled: true
        },{
          title: '三级1-2-2'
          ,id: 10
          ,field: ''
        }]
      },{
        title: '二级1-3'
        ,id: 20
        ,field: ''
        ,children: [{
          title: '三级1-3-1'
          ,id: 21
          ,field: ''
        },{
          title: '三级1-3-2'
          ,id: 22
          ,field: ''
        }]
      }]
    },{
      title: '一级2'
      ,id: 2
      ,field: ''
      ,spread: true
      ,children: [{
        title: '二级2-1'
        ,id: 5
        ,field: ''
        ,spread: true
        ,children: [{
          title: '三级2-1-1'
          ,id: 11
          ,field: ''
        },{
          title: '三级2-1-2'
          ,id: 12
          ,field: ''
        }]
      },{
        title: '二级2-2'
        ,id: 6
        ,field: ''
        ,children: [{
          title: '三级2-2-1'
          ,id: 13
          ,field: ''
        },{
          title: '三级2-2-2'
          ,id: 14
          ,field: ''
          ,disabled: true
        }]
      }]
    },{
      title: '一级3'
      ,id: 16
      ,field: ''
      ,children: [{
        title: '二级3-1'
        ,id: 17
        ,field: ''
        ,fixed: true
        ,children: [{
          title: '三级3-1-1'
          ,id: 18
          ,field: ''
        },{
          title: '三级3-1-2'
          ,id: 19
          ,field: ''
        }]
      },{
        title: '二级3-2'
        ,id: 27
        ,field: ''
        ,children: [{
          title: '三级3-2-1'
          ,id: 28
          ,field: ''
        },{
          title: '三级3-2-2'
          ,id: 29
          ,field: ''
        }]
      }]
    }]
    
    //模拟数据1
    ,data1 = [{
      title: '江西'
      ,id: 1
      ,children: [{
        title: '南昌'
        ,id: 1000
        ,children: [{
          title: '青山湖区'
          ,id: 10001
        },{
          title: '高新区'
          ,id: 10002
        }]
      },{
        title: '九江'
        ,id: 1001
      },{
        title: '赣州'
        ,id: 1002
      }]
    },{
      title: '广西'
      ,id: 2
      ,children: [{
        title: '南宁'
        ,id: 2000
      },{
        title: '桂林'
        ,id: 2001
      }]
    },{
      title: '陕西'
      ,id: 3
      ,children: [{
        title: '西安'
        ,id: 3000
      },{
        title: '延安'
        ,id: 3001
      }]
    }]
    
    //模拟数据2
    ,data2 = [{
      title: '早餐'
      ,id: 1
      ,children: [{
        title: '油条'
        ,id: 5
      },{
        title: '包子'
        ,id: 6
      },{
        title: '豆浆'
        ,id: 7
      }]
    },{
      title: '午餐'
      ,id: 2
      ,checked: true
      ,children: [{
        title: '藜蒿炒腊肉'
        ,id: 8
      },{
        title: '西湖醋鱼'
        ,id: 9
      },{
        title: '小白菜'
        ,id: 10
      },{
        title: '海带排骨汤'
        ,id: 11
      }]
    },{
      title: '晚餐'
      ,id: 3
      ,children: [{
        title: '红烧肉'
        ,id: 12
        ,fixed: true
      },{
        title: '番茄炒蛋'
        ,id: 13
      }]
    },{
      title: '夜宵'
      ,id: 4
      ,children: [{
        title: '小龙虾'
        ,id: 14
        ,checked: true
      },{
        title: '香辣蟹'
        ,id: 15
        ,disabled: true
      },{
        title: '烤鱿鱼'
        ,id: 16
      }]
    }];
   
    //基本演示
    tree.render({
      elem: '#test-tree-demo1'
      ,data: data
      ,showCheckbox: true  //是否显示复选框
      ,id: 'test-tree-demoId1'
      ,isJump: true //是否允许点击节点时弹出新窗口跳转
      ,click: function(obj){
        var data = obj.data;  //获取当前点击的节点数据
        layer.msg('状态：'+ obj.state + '<br>节点数据：' + JSON.stringify(data));
      }
    });
    
    //按钮事件
    util.event('lay-demo', {
      getChecked: function(othis){
        var checkedData = tree.getChecked('test-tree-demoId1'); //获取选中节点的数据
        
        layer.alert(JSON.stringify(checkedData), {shade:0});
        console.log(checkedData);
      }
      ,setChecked: function(){
        tree.setChecked('test-tree-demoId1', [12, 16]); //勾选指定节点
      }
      ,reload: function(){
        //重载实例
        tree.reload('test-tree-demoId1', {
          
        });
        
      }
    });
   
    //常规用法
    tree.render({
      elem: '#test-tree-demo2' //默认是点击节点可进行收缩
      ,data: data1
    });
    
    //无连接线风格
    tree.render({
      elem: '#test-tree-demo3'
      ,data: data1
      ,showLine: false  //是否开启连接线
    });
   
    //仅节点左侧图标控制收缩
    tree.render({
      elem: '#test-tree-demo4'
      ,data: data1
      ,onlyIconControl: true  //是否仅允许节点左侧图标控制展开收缩
      ,click: function(obj){
        layer.msg(JSON.stringify(obj.data));
      }
    });
    //手风琴模式
    tree.render({
      elem: '#test-tree-demo5'
      ,data: [{
        title: '优秀'
        ,children: [{
          title: '80 ~ 90'
        },{
          title: '90 ~ 100'
        }]
      },{
        title: '良好'
        ,children: [{
          title: '70 ~ 80'
        },{
          title: '60 ~ 70'
        }]
      },{
        title: '要努力奥'
        ,children: [{
          title: '0 ~ 60'
        }]
      }]
      ,accordion: true 
    });
   
    //点击节点新窗口跳转
    tree.render({
      elem: '#test-tree-demo6'
      ,data: data
      ,isJump: true  //link 为参数匹配
    });
   
    //开启复选框
    tree.render({
      elem: '#test-tree-demo7'
      ,data: data2
      ,showCheckbox: true
    });
   
    //开启节点操作图标
    tree.render({
      elem: '#test-tree-demo8'
      ,data: data1
      ,edit: ['add', 'update', 'del'] //操作节点的图标
      ,click: function(obj){
        layer.msg(JSON.stringify(obj.data));
      }
    });
  });
  </script>
</body>
</html>