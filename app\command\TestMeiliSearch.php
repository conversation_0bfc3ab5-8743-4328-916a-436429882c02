<?php

namespace app\command;

use app\common\library\MeiliSearch;
use think\console\Command;
use think\console\Input;
use think\console\Output;

/**
 * 测试MeiliSearch连接和配置
 */
class TestMeiliSearch extends Command
{
    protected function configure()
    {
        $this->setName('test:meilisearch')
            ->setDescription('测试MeiliSearch连接和配置');
    }

    protected function execute(Input $input, Output $output)
    {
        $output->writeln("开始测试MeiliSearch连接...");

        try {
            // 初始化MeiliSearch客户端
            $meili = new MeiliSearch();
            
            // 1. 测试基本连接
            $output->writeln("1. 测试基本连接...");
            if ($meili->testConnection()) {
                $output->writeln("<info>✓ MeiliSearch连接成功</info>");
            } else {
                $output->writeln("<error>✗ MeiliSearch连接失败</error>");
                return 1;
            }

            // 2. 测试索引状态
            $output->writeln("2. 检查goods索引状态...");
            $indexInfo = $meili->getIndex('goods');
            if (isset($indexInfo['error'])) {
                $output->writeln("<comment>! goods索引不存在: " . $indexInfo['error'] . "</comment>");
                
                // 尝试创建索引
                $output->writeln("3. 尝试创建goods索引...");
                $settings = [
                    'searchableAttributes' => [
                        'name',
                        'remark',
                        'content',
                        'split_word'
                    ],
                    'filterableAttributes' => [
                        'shop_id',
                        'status',
                        'audit_status',
                        'del'
                    ],
                    'sortableAttributes' => [
                        'min_price',
                        'sales_actual',
                        'create_time'
                    ]
                ];
                
                $result = $meili->createIndex('goods', $settings);
                if (isset($result['error'])) {
                    $output->writeln("<error>✗ 创建索引失败: " . $result['error'] . "</error>");
                } else {
                    $output->writeln("<info>✓ 索引创建成功</info>");
                }
            } else {
                $output->writeln("<info>✓ goods索引存在</info>");
                $output->writeln("索引信息: " . json_encode($indexInfo, JSON_UNESCAPED_UNICODE));
            }

            // 3. 测试搜索功能
            $output->writeln("4. 测试搜索功能...");
            $searchResult = $meili->searchInIndex('goods', '测试');
            if (isset($searchResult['error'])) {
                $output->writeln("<comment>! 搜索测试失败: " . $searchResult['error'] . "</comment>");
            } else {
                $output->writeln("<info>✓ 搜索功能正常</info>");
                $output->writeln("搜索结果数量: " . (isset($searchResult['hits']) ? count($searchResult['hits']) : 0));
            }

            // 4. 检查商品数据
            $output->writeln("5. 检查商品数据同步状态...");
            $goodsCount = \think\facade\Db::name('goods')
                ->where('del', 0)
                ->where('status', 1)
                ->where('audit_status', 1)
                ->count();
            
            $output->writeln("数据库中符合条件的商品数量: " . $goodsCount);
            
            // 检查MeiliSearch中的商品数量
            $meiliGoodsResult = $meili->searchInIndex('goods', '');
            $meiliGoodsCount = isset($meiliGoodsResult['estimatedTotalHits']) ? $meiliGoodsResult['estimatedTotalHits'] : 0;
            $output->writeln("MeiliSearch中的商品数量: " . $meiliGoodsCount);
            
            if ($goodsCount > 0 && $meiliGoodsCount == 0) {
                $output->writeln("<comment>! 商品数据未同步到MeiliSearch，建议运行同步命令</comment>");
                $output->writeln("运行命令: php think sync:goods-to-meilisearch");
            } elseif ($goodsCount > $meiliGoodsCount) {
                $output->writeln("<comment>! MeiliSearch中的商品数量少于数据库，建议重新同步</comment>");
                $output->writeln("运行命令: php think sync:goods-to-meilisearch --reset");
            } else {
                $output->writeln("<info>✓ 商品数据同步正常</info>");
            }

            $output->writeln("\n<info>MeiliSearch测试完成！</info>");
            return 0;

        } catch (\Exception $e) {
            $output->writeln("<error>测试过程中发生异常: " . $e->getMessage() . "</error>");
            return 1;
        }
    }
}
