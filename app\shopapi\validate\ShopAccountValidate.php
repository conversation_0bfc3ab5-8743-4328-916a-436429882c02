<?php
namespace app\shopapi\validate;

use app\common\validate\BaseValidate;

class ShopAccountValidate extends BaseValidate
{
    protected $rule = [
        'old_mobile' => 'require|mobile',
        'new_mobile' => 'require|mobile|different:old_mobile',
        'verify_code' => 'require|length:6',
        'password' => 'require|min:6',
        'reason' => 'require|max:500',
    ];

    protected $message = [
        'old_mobile.require' => '原手机号不能为空',
        'old_mobile.mobile' => '原手机号格式错误',
        'new_mobile.require' => '新手机号不能为空',
        'new_mobile.mobile' => '新手机号格式错误',
        'new_mobile.different' => '新手机号不能与原手机号相同',
        'verify_code.require' => '验证码不能为空',
        'verify_code.length' => '验证码必须是6位数字',
        'password.require' => '密码不能为空',
        'password.min' => '密码不能少于6位',
        'reason.require' => '注销原因不能为空',
        'reason.max' => '注销原因不能超过500字',
    ];

    protected $scene = [
        'updateMobile' => ['old_mobile', 'new_mobile', 'verify_code', 'password'],
        'deactivate' => ['password', 'reason'],
    ];
}