(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-news_list-news_list"],{"0304":function(t,e,i){var a=i("c715");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("4f06").default;n("9fed7410",a,!0,{sourceMap:!1,shadowMode:!1})},1522:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return r})),i.d(e,"a",(function(){return a}));var a={uIcon:i("90f3").default},n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"u-image",style:[t.wrapStyle,t.backgroundStyle],on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onClick.apply(void 0,arguments)}}},[t.isError?t._e():i("v-uni-image",{staticClass:"u-image__image",style:{borderRadius:"circle"==t.shape?"50%":t.$u.addUnit(t.borderRadius)},attrs:{src:t.src,mode:t.mode,"lazy-load":t.lazyLoad},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.onErrorHandler.apply(void 0,arguments)},load:function(e){arguments[0]=e=t.$handleEvent(e),t.onLoadHandler.apply(void 0,arguments)}}}),t.showLoading&&t.loading?i("v-uni-view",{staticClass:"u-image__loading",style:{borderRadius:"circle"==t.shape?"50%":t.$u.addUnit(t.borderRadius),backgroundColor:this.bgColor}},[t.$slots.loading?t._t("loading"):i("u-icon",{attrs:{name:t.loadingIcon,width:t.width,height:t.height}})],2):t._e(),t.showError&&t.isError&&!t.loading?i("v-uni-view",{staticClass:"u-image__error",style:{borderRadius:"circle"==t.shape?"50%":t.$u.addUnit(t.borderRadius)}},[t.$slots.error?t._t("error"):i("u-icon",{attrs:{name:t.errorIcon,width:t.width,height:t.height}})],2):t._e()],1)},r=[]},2322:function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("a9e3");var a={name:"u-image",props:{src:{type:String,default:""},mode:{type:String,default:"aspectFill"},width:{type:[String,Number],default:"100%"},height:{type:[String,Number],default:"auto"},shape:{type:String,default:"square"},borderRadius:{type:[String,Number],default:0},lazyLoad:{type:Boolean,default:!0},showMenuByLongpress:{type:Boolean,default:!0},loadingIcon:{type:String,default:"photo"},errorIcon:{type:String,default:"error-circle"},showLoading:{type:Boolean,default:!0},showError:{type:Boolean,default:!0},fade:{type:Boolean,default:!0},webp:{type:Boolean,default:!1},duration:{type:[String,Number],default:500},bgColor:{type:String,default:"#f3f4f6"}},data:function(){return{isError:!1,loading:!0,opacity:1,durationTime:this.duration,backgroundStyle:{}}},watch:{src:{immediate:!0,handler:function(t){t?this.isError=!1:(this.isError=!0,this.loading=!1)}}},computed:{wrapStyle:function(){var t={};return t.width=this.$u.addUnit(this.width),t.height=this.$u.addUnit(this.height),t.borderRadius="circle"==this.shape?"50%":this.$u.addUnit(this.borderRadius),t.overflow=this.borderRadius>0?"hidden":"visible",this.fade&&(t.opacity=this.opacity,t.transition="opacity ".concat(Number(this.durationTime)/1e3,"s ease-in-out")),t}},methods:{onClick:function(){this.$emit("click")},onErrorHandler:function(t){this.loading=!1,this.isError=!0,this.$emit("error",t)},onLoadHandler:function(){var t=this;if(this.loading=!1,this.isError=!1,this.$emit("load"),!this.fade)return this.removeBgColor();this.opacity=0,this.durationTime=0,setTimeout((function(){t.durationTime=t.duration,t.opacity=1,setTimeout((function(){t.removeBgColor()}),t.durationTime)}),50)},removeBgColor:function(){this.backgroundStyle={backgroundColor:"transparent"}}}};e.default=a},"2a11":function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return r})),i.d(e,"a",(function(){return a}));var a={adSwipers:i("578c").default,tabs:i("741a").default,tab:i("5652").default,uImage:i("ba4b").default},n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"news-list"},[i("mescroll-body",{ref:"mescrollRef",attrs:{up:t.upOption},on:{init:function(e){arguments[0]=e=t.$handleEvent(e),t.mescrollInit.apply(void 0,arguments)},down:function(e){arguments[0]=e=t.$handleEvent(e),t.downCallback.apply(void 0,arguments)},up:function(e){arguments[0]=e=t.$handleEvent(e),t.upCallback.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"contain"},[i("v-uni-view",{staticClass:"banner"},[0==t.type?i("ad-swipers",{attrs:{pid:14,height:"340rpx"}}):t._e(),1==t.type?i("ad-swipers",{attrs:{pid:15,height:"340rpx"}}):t._e()],1),i("tabs",{attrs:{current:t.active,"bar-width":60},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.changeActive.apply(void 0,arguments)}}},[i("tab",{attrs:{name:"全部"}}),t._l(t.categoryList,(function(t,e){return i("tab",{key:e,attrs:{name:t.name}})}))],2),i("v-uni-view",{staticClass:"main"},[i("v-uni-view",{staticClass:"article-list"},t._l(t.newsList,(function(e,a){return i("v-uni-view",{key:a,staticClass:"article-item bg-white"},[i("router-link",{attrs:{to:{path:"/pages/news_details/news_details",query:{id:e.id,type:t.type}}}},[i("v-uni-view",{staticClass:"flex col-top"},[i("v-uni-view",{staticClass:"info flex-1"},[i("v-uni-view",{staticClass:"title lg line-2 m-b-20"},[t._v(t._s(e.title))]),i("v-uni-view",{staticClass:"lighter line-2"},[i("v-uni-view",[t._v(t._s(e.intro))])],1)],1),i("u-image",{staticClass:"img m-l-20",attrs:{width:"240rpx",height:"180rpx",src:e.image}})],1),i("v-uni-view",{staticClass:"flex row-between m-t-20"},[i("v-uni-view",{staticClass:"xs muted"},[t._v("发布时间: "+t._s(e.create_time))]),i("v-uni-view",{staticClass:"flex"},[i("v-uni-image",{staticClass:"icon-sm",attrs:{src:"/static/images/icon_see.png"}}),i("v-uni-view",{staticClass:"m-l-10 xs muted"},[t._v(t._s(e.visit)+"人浏览")])],1)],1)],1)],1)})),1)],1)],1)],1)],1)},r=[]},"375d2":function(t,e,i){"use strict";i.r(e);var a=i("a98f"),n=i.n(a);for(var r in a)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(r);e["default"]=n.a},"4d95":function(t,e,i){"use strict";i.r(e);var a=i("2a11"),n=i("375d2");for(var r in n)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(r);i("5bf1");var s=i("f0c5"),o=Object(s["a"])(n["default"],a["b"],a["c"],!1,null,"858d7f12",null,!1,a["a"],void 0);e["default"]=o.exports},"578c":function(t,e,i){"use strict";i.r(e);var a=i("e71b"),n=i("ee9d");for(var r in n)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(r);i("f893");var s=i("f0c5"),o=Object(s["a"])(n["default"],a["b"],a["c"],!1,null,"5a2d1aa7",null,!1,a["a"],void 0);e["default"]=o.exports},"5bf1":function(t,e,i){"use strict";var a=i("0304"),n=i.n(a);n.a},6021:function(t,e,i){"use strict";var a=i("64b1"),n=i.n(a);n.a},"64b1":function(t,e,i){var a=i("6e35");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("4f06").default;n("3d1e497c",a,!0,{sourceMap:!1,shadowMode:!1})},"6e35":function(t,e,i){var a=i("24fb");e=a(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.u-image[data-v-1bf07c9a]{position:relative;transition:opacity .5s ease-in-out}.u-image__image[data-v-1bf07c9a]{width:100%;height:100%}.u-image__loading[data-v-1bf07c9a], .u-image__error[data-v-1bf07c9a]{position:absolute;top:0;left:0;width:100%;height:100%;display:flex;flex-direction:row;align-items:center;justify-content:center;background-color:#f3f4f6;color:#909399;font-size:%?46?%}',""]),t.exports=e},"89cb":function(t,e,i){var a=i("24fb");e=a(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.swiper-wrap[data-v-5a2d1aa7]{overflow:hidden;box-sizing:initial}.swiper-wrap .swiper-con[data-v-5a2d1aa7]{position:relative;height:100%;overflow:hidden;-webkit-transform:translateY(0);transform:translateY(0)}.swiper-wrap .swiper[data-v-5a2d1aa7]{width:100%;height:100%;position:relative}.swiper-wrap .swiper .slide-image[data-v-5a2d1aa7]{height:100%}.swiper-wrap .dots[data-v-5a2d1aa7]{position:absolute;left:50%;-webkit-transform:translateX(-50%);transform:translateX(-50%);bottom:%?20?%;display:flex}.swiper-wrap .dots .dot[data-v-5a2d1aa7]{width:%?8?%;height:%?8?%;border-radius:50%;margin-right:%?10?%;background-color:#fff}.swiper-wrap .dots .dot.active[data-v-5a2d1aa7]{width:%?16?%;border-radius:%?8?%;background-color:#ff2c3c}',""]),t.exports=e},"8c51":function(t,e,i){"use strict";i("7a82");var a=i("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=a(i("f07e")),r=a(i("c964"));i("a9e3"),i("14d9");var s=i("9953"),o=(i("b08d"),{data:function(){return{lists:[],currentSwiper:0}},props:{pid:{type:Number},circular:{type:Boolean,default:!0},autoplay:{type:Boolean,default:!0},height:{type:String},radius:{type:String,default:"0"},padding:{type:String,default:"0rpx"},previousMargin:{type:String,default:"0rpx"},isSwiper:{type:Boolean,default:!0}},created:function(){this.getAdListFun()},watch:{pid:function(t){this.getAdListFun()}},methods:{getAdListFun:function(){var t=this;return(0,r.default)((0,n.default)().mark((function e(){var i,a,r;return(0,n.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,(0,s.getAdList)({pid:t.pid,terminal:1});case 2:i=e.sent,a=i.code,r=i.data,1==a&&(t.lists=r);case 6:case"end":return e.stop()}}),e)})))()},swiperChange:function(t){this.currentSwiper=t.detail.current},goPage:function(t){var e=t.link,i=t.link_type,a=t.params,n=t.is_tab;switch(i){case 1:case 2:n?this.$Router.pushTab({path:e}):this.$Router.push({path:e,query:a});break;case 3:this.$Router.push({path:"/pages/webview/webview",query:{url:e}});break}}}});e.default=o},a98f:function(t,e,i){"use strict";i("7a82");var a=i("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("99af");var n=a(i("f07e")),r=a(i("c964")),s=i("9953"),o=a(i("bde1")),u={mixins:[o.default],data:function(){return{active:0,upOption:{auto:!1,empty:{icon:"/static/images/news_null.png",tip:"暂无数据"}},categoryList:[],newsList:[],type:-1}},onLoad:function(t){this.type=this.$Route.query.type||0,this.type?uni.setNavigationBarTitle({title:"帮助中心"}):uni.setNavigationBarTitle({title:"商城资讯"})},methods:{changeActive:function(t){this.active=t,this.newsList=[],this.mescroll.resetUpScroll()},downCallback:function(){var t=this;return(0,r.default)((0,n.default)().mark((function e(){return(0,n.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.getCategoryListFun();case 2:t.mescroll.resetUpScroll();case 3:case"end":return e.stop()}}),e)})))()},upCallback:function(t){var e=this,i=(this.type,this.active),a=this.categoryList;(0,s.getArticleList)({type:this.type,cid:this.active?a[i-1].id:"",page_size:t.size,page_no:t.num}).then((function(i){var a=i.data;1==t.num&&(e.newsList=[]);var n=a.list,r=n.length,s=!!a.more;e.newsList=e.newsList.concat(n),e.mescroll.endSuccess(r,s)})).catch((function(){e.mescroll.endErr()}))},getCategoryListFun:function(){var t=this;return(0,r.default)((0,n.default)().mark((function e(){var i,a,r;return(0,n.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,(0,s.getCategoryList)({type:t.type});case 2:i=e.sent,a=i.code,r=i.data,1==a&&(t.categoryList=r);case 6:case"end":return e.stop()}}),e)})))()}}};e.default=u},af8d:function(t,e,i){"use strict";i.r(e);var a=i("2322"),n=i.n(a);for(var r in a)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(r);e["default"]=n.a},ba4b:function(t,e,i){"use strict";i.r(e);var a=i("1522"),n=i("af8d");for(var r in n)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(r);i("6021");var s=i("f0c5"),o=Object(s["a"])(n["default"],a["b"],a["c"],!1,null,"1bf07c9a",null,!1,a["a"],void 0);e["default"]=o.exports},bde1:function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a={data:function(){return{mescroll:null}},onPullDownRefresh:function(){this.mescroll&&this.mescroll.onPullDownRefresh()},onPageScroll:function(t){this.mescroll&&this.mescroll.onPageScroll(t)},onReachBottom:function(){this.mescroll&&this.mescroll.onReachBottom()},methods:{mescrollInit:function(t){console.log(t),this.mescroll=t,this.mescrollInitByRef()},mescrollInitByRef:function(){if(!this.mescroll||!this.mescroll.resetUpScroll){var t=this.$refs.mescrollRef;t&&(this.mescroll=t.mescroll)}},downCallback:function(){var t=this;this.mescroll.optUp.use?this.mescroll.resetUpScroll():setTimeout((function(){t.mescroll.endSuccess()}),500)},upCallback:function(){var t=this;setTimeout((function(){t.mescroll.endErr()}),500)}},mounted:function(){this.mescrollInitByRef()}},n=a;e.default=n},c715:function(t,e,i){var a=i("24fb");e=a(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.news-list .main .article-list[data-v-858d7f12]{padding-top:%?20?%}.news-list .main .article-list .article-item[data-v-858d7f12]{padding:%?20?%;align-items:flex-start}.news-list .main .article-list .article-item[data-v-858d7f12]:not(:last-of-type){border-bottom:1px solid #e5e5e5}',""]),t.exports=e},e71b:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return r})),i.d(e,"a",(function(){return a}));var a={uImage:i("ba4b").default},n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return t.lists.length?i("v-uni-view",{staticClass:"swiper-wrap",style:{height:t.height,padding:t.padding}},[i("v-uni-view",{staticClass:"swiper-con",style:{borderRadius:t.radius}},[t.isSwiper?[i("v-uni-swiper",{staticClass:"swiper",attrs:{autoplay:t.autoplay,circular:t.circular,"previous-margin":t.previousMargin,"display-multiple-items":"1"},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.swiperChange.apply(void 0,arguments)}}},t._l(t.lists,(function(e,a){return i("v-uni-swiper-item",{key:a},[i("v-uni-view",{staticStyle:{width:"100%",height:"100%"},attrs:{"data-item":e},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.goPage(e)}}},[i("u-image",{attrs:{mode:"aspectFill",width:"calc(100% - "+t.previousMargin+")",height:"100%","border-radius":t.radius,src:e.image}})],1)],1)})),1),t.lists.length>1?i("v-uni-view",{staticClass:"dots"},t._l(t.lists,(function(e,a){return i("v-uni-view",{key:a,class:"dot "+(a==t.currentSwiper?"active":"")})})),1):t._e()]:t._e(),t._l(t.lists,(function(e,a){return[a<1?i("v-uni-view",{key:a,staticStyle:{width:"100%",height:"100%"},attrs:{"data-item":e},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.goPage(e)}}},[i("u-image",{attrs:{mode:"aspectFill",width:"calc(100% - "+t.previousMargin+")",height:"100%","border-radius":t.radius,src:e.image}})],1):t._e()]}))],2)],1):t._e()},r=[]},edb9:function(t,e,i){var a=i("89cb");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("4f06").default;n("0a552a2c",a,!0,{sourceMap:!1,shadowMode:!1})},ee9d:function(t,e,i){"use strict";i.r(e);var a=i("8c51"),n=i.n(a);for(var r in a)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(r);e["default"]=n.a},f893:function(t,e,i){"use strict";var a=i("edb9"),n=i.n(a);n.a}}]);