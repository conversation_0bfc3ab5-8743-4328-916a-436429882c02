<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Cis\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * InquiryPriceCreateCis请求参数结构体
 *
 * @method string getZone() 获取可用区
 * @method void setZone(string $Zone) 设置可用区
 * @method float getCpu() 获取CPU，单位：核
 * @method void setCpu(float $Cpu) 设置CPU，单位：核
 * @method float getMemory() 获取内存，单位：Gi
 * @method void setMemory(float $Memory) 设置内存，单位：Gi
 */
class InquiryPriceCreateCisRequest extends AbstractModel
{
    /**
     * @var string 可用区
     */
    public $Zone;

    /**
     * @var float CPU，单位：核
     */
    public $Cpu;

    /**
     * @var float 内存，单位：Gi
     */
    public $Memory;

    /**
     * @param string $Zone 可用区
     * @param float $Cpu CPU，单位：核
     * @param float $Memory 内存，单位：Gi
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("Zone",$param) and $param["Zone"] !== null) {
            $this->Zone = $param["Zone"];
        }

        if (array_key_exists("Cpu",$param) and $param["Cpu"] !== null) {
            $this->Cpu = $param["Cpu"];
        }

        if (array_key_exists("Memory",$param) and $param["Memory"] !== null) {
            $this->Memory = $param["Memory"];
        }
    }
}
