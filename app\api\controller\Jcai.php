<?php


namespace app\api\controller;


use app\api\logic\GoodsLogic;
use app\api\logic\JcaiLogic;
use app\api\validate\JcaiValidate;
use app\common\basics\Api;
use app\common\model\Jcai\JcaiFound;
use app\common\model\Jcai\JcaiJoin;
use app\api\validate\TeamValidate;
use app\common\server\JsonServer;
use think\exception\ValidateException;
use think\facade\Db;
use think\facade\Validate;

class Jcai extends Api
{
    public $like_not_need_login = ['activity'];

    /**
     * @Notes: 集众筹活动
     * @Author: 张无忌
     */
    public function activity()
    {
        $get = $this->request->get();
        $lists = JcaiLogic::activity($get, $this->user_id);
        if ($lists === false) {
            $message = JcaiLogic::getError() ?: '获取失败';
            return JsonServer::error($message);
        }
        return JsonServer::success('获取成功', $lists);
    }

    /**
     * @Notes: 参加集众筹
     * @Author: 张无忌
     */
    public function Jcaizc()
    {
        try{
            validate(TeamValidate::class)->scene('check')->check($this->request->post());
        }catch(ValidateException $e) {
            return JsonServer::error($e->getError(), [], 301);
        }
        $vip_time= Db::name('user')->where('id', $this->user_id)->value('vip_time');
        if ($vip_time < time()) {
            return JsonServer::error('',[],1102);
        }
        $post = $this->request->post();
        $info = JcaiLogic::kaituanInfo($post, $this->user_id);
        if ($info === false) {
            $message = JcaiLogic::getError() ?: '获取众筹信息失败';
            return JsonServer::error($message);
        }

//        if ($post['action'] == 'info') {
//            return JsonServer::success('OK', $info);
//        }

        $res = JcaiLogic::kaituan($info, $this->user_info,$post);
        if ($res === false) {
            $message = JcaiLogic::getError() ?: '参加众筹失败';
            return JsonServer::error($message);
        }
        return JsonServer::success('集众筹成功', $res);
    }

    /**
     * @Notes: 集众筹记录
     * @Author: 张无忌
     */
    public function record()
    {
        $get = $this->request->get();
        $lists = JcaiLogic::record($get, $this->user_id);
        if ($lists === false) {
            $message = JcaiLogic::getError() ?: '获取失败';
            return JsonServer::error($message);
        }
        return JsonServer::success('集众筹成功', $lists);
    }

    /*
     * 获取集采众筹详情
     */
    public function getJcaiDetail(){
        $post = $this->request->post();
        $info = JcaiLogic::kaituanInfo($post, $this->user_id);
        if ($info === false) {
            $message = JcaiLogic::getError() ?: '获取众筹信息失败';
            return JsonServer::error($message);
        }
        return JsonServer::success('', $info);
    }

    /**
     * @Notes: 验证众筹信息
     * @Author: 张无忌
     */
    public function check()
    {
        (new TeamValidate())->goCheck('check');

        $post = $this->request->post();
        $res = JcaiLogic::check($post, $this->user_id);
        if ($res === false) {
            $message = JcaiLogic::getError() ?: '验证失败';
            return JsonServer::error($message);
        }
        return JsonServer::success('验证通过');
    }

    /*
     * 获取集采拼单的商品
     * DM
     */
    public function getPdJcaiGoods(){
        if($this->request->isGet()) {
            $goodsId = $this->request->get('goods_id', '', 'trim');
            $validate = Validate::rule('goods_id', 'require|integer|gt:0');
            if(!$validate->check(['goods_id'=>$goodsId])) {
                return JsonServer::error($validate->getError());
            }
            $goodsDetail = GoodsLogic::getGoodsDetail($goodsId, $this->user_id);
            if(false === $goodsDetail) {
                $error = GoodsLogic::getError() ?? '获取商品详情失败';
                return JsonServer::error($error);
            }
            return JsonServer::success('获取商品详情成功', $goodsDetail);
        }else{
            return JsonServer::error('请求方式错误');
        }
    }

    /*
     * 添加集采众筹预约记录
     */
    public function addJcaiJoin(){

         JcaiLogic::addJcaiJoin($this->request->post());
//        if($res === false) {
//            $error = JcaiLogic::getError() ?? '添加失败';
//            return JsonServer::error($error);
//        }
        return JsonServer::success('预约成功');
    }


    /*
     * 预约集采众筹
     */
    public function joinJcai(){
        $res = JcaiLogic::addJcaiJoin($this->request->post());
        if($res === false) {
            $error = JcaiLogic::getError() ?? '预约失败';
            return JsonServer::error($error);
        }
        return JsonServer::success('预约成功');
    }




}