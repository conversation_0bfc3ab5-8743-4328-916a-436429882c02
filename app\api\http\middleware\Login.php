<?php


namespace app\api\http\middleware;

use app\api\cache\TokenCache;
use think\exception\ValidateException;
use app\api\validate\TokenValidate;
use app\common\model\user\User;
use app\common\model\Session;
use app\common\server\JsonServer;
use think\facade\Cache;

class Login
{
    /**
     * 登录验证
     * @param $request
     * @param \Closure $next
     * @return mixed|\think\response\Redirect
     */
    public function handle($request, \Closure $next)
    {
        //允许跨域调用
        header('Access-Control-Allow-Origin: *');
        header("Access-Control-Allow-Headers: Authorization, Sec-Fetch-Mode, DNT, X-Mx-ReqToken, Keep-Alive, User-Agent, If-Match, If-None-Match, If-Unmodified-Since, X-Requested-With, If-Modified-Since, Cache-Control, Content-Type, Accept-Language, Origin, Accept-Encoding,Access-Token,token");
        header('Access-Control-Allow-Methods: GET, POST, PATCH, PUT, DELETE');
        header('Access-Control-Max-Age: 1728000');
        header('Access-Control-Allow-Credentials:true');

        if (strtoupper($request->method()) == "OPTIONS") {
            return response();
        }

        $token = $request->header('token');

        // 免登录
        if ($this->isNotNeedLogin($request) && empty($token)) {
            return $next($request);
        }

        // 根据token读取缓存
        $token_cache = new TokenCache($token, ['token' => $token]);
        if (!$token_cache->isEmpty()) {
            $request->user_info = $token_cache->get();
            return $next($request);
        }

        //token验证，并生成缓存
        $validateError = '';
        try{
            validate(TokenValidate::class)->check(['token' => $token]);

            $userInfo = User::alias('u')
                ->join('session s', 'u.id=s.user_id')
                ->where(['s.token' => $token])
                ->field('u.*,s.token,s.client')
                ->find();
            $userInfo = $userInfo ? $userInfo->toArray() : [];

            // 检查当前用户状态
            if (!empty($userInfo) && ($userInfo['user_delete'] == 1 || $userInfo['disable'] == 1)) {
                // 用户已注销或被冻结，清除token
                $this->clearUserToken($token, $userInfo['id']);
                $validateError = $userInfo['user_delete'] == 1 ? '账号已注销，请重新登录' : '账号已被冻结，请联系客服';
                throw new ValidateException($validateError);
            }

            // 设置缓存
            $token_cache->set(600);
            // 设置用户信息
            $request->user_info = $userInfo;
            return $next($request);
        }catch(ValidateException $e) {
            $validateError = $e->getError();
        }

        //无需要登录，带token的情况
        if ($this->isNotNeedLogin($request) && $token) {
            return $next($request);
        }

        //登录失败
        $result = array(
            'code' => 401,
            'show' => 1,
            'msg'  => $validateError,
            'data' => []
        );
        return json($result);

    }


    /**
     * 是否免登录验证
     * @param $request
     * @return bool
     */
    private function isNotNeedLogin($request)
    {
        // 提取当前请求控制器名称
        $baseUrl = $request->baseUrl(); //   /api/goods/test
        $apperTwo = strpos($baseUrl, '/', 1);
        $apperThird  = strpos($baseUrl, '/', $apperTwo + 1);
        $len = $apperThird - $apperTwo - 1;
        $controllerName = substr($baseUrl, $apperTwo + 1, $len);

        // 控制名称处理(兼容下划线的情况 例：goods_columns 处理成 GoodsColumns)
        $controllerNameArr = explode('_', $controllerName);
        $controllerNameArr = array_map('ucfirst', $controllerNameArr);
        $controllerName = implode($controllerNameArr);

        // 实例化当前请求的控制器
        $controllerObj = invoke('\\app\\api\\controller\\' . $controllerName);
        $data = $controllerObj->like_not_need_login;
        if (empty($data)) {
            return false;
        }

        // 提取操作名称
        $action = strtolower(substr($baseUrl, $apperThird + 1));

        $data = array_map('strtolower', $data);

        if (!in_array($action, $data)) {
            return false;
        }
        return true;
    }

    /**
     * @notes 清除用户token
     * @param string $token
     * @param int $user_id
     * @return void
     * <AUTHOR>
     * @date 2024/12/19
     */
    private function clearUserToken($token, $user_id)
    {
        try {
            // 清除数据库中的session记录
            Session::where('user_id', $user_id)->delete();

            // 清除缓存中的token
            Cache::delete($token);

            // 记录日志
            trace("用户ID:{$user_id} 的token已被清除", 'info');
        } catch (\Exception $e) {
            // 清理token失败不影响正常流程，记录错误日志即可
            trace('清除用户token失败: ' . $e->getMessage(), 'error');
        }
    }
}
