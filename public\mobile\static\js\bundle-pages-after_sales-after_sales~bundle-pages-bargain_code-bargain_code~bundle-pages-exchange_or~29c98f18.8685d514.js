(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["bundle-pages-after_sales-after_sales~bundle-pages-bargain_code-bargain_code~bundle-pages-exchange_or~29c98f18"],{"0a2f":function(t,o,e){"use strict";o["a"]=function(t){(t.options.wxs||(t.options.wxs={}))["wxsBiz"]=function(t){var o={};function e(t,e){if(o.isMoveDown)o.downHight>=o.optDown.offset?(o.downHight=o.optDown.offset,o.callMethod(e,{type:"triggerDownScroll"})):(o.downHight=0,o.callMethod(e,{type:"endDownScroll"})),o.movetype=0,o.isMoveDown=!1;else if(!o.isScrollBody&&o.getScrollTop()===o.startTop){var n=o.getPoint(t).y-o.startPoint.y<0;if(n){var i=o.getAngle(o.getPoint(t),o.startPoint);i>80&&o.callMethod(e,{type:"triggerUpScroll"})}}o.callMethod(e,{type:"setWxsProp"})}return o.onMoving=function(t,o,e){t.requestAnimationFrame((function(){t.selectComponent(".mescroll-wxs-content").setStyle({"will-change":"transform",transform:"translateY("+e+"px)",transition:""});var n=t.selectComponent(".mescroll-wxs-progress");n&&n.setStyle({transform:"rotate("+360*o+"deg)"})}))},o.showLoading=function(t){o.downHight=o.optDown.offset,t.requestAnimationFrame((function(){t.selectComponent(".mescroll-wxs-content").setStyle({"will-change":"auto",transform:"translateY("+o.downHight+"px)",transition:"transform 300ms"})}))},o.endDownScroll=function(t){o.downHight=0,o.isDownScrolling=!1,t.requestAnimationFrame((function(){t.selectComponent(".mescroll-wxs-content").setStyle({"will-change":"auto",transform:"translateY(0)",transition:"transform 300ms"})}))},o.clearTransform=function(t){t.requestAnimationFrame((function(){t.selectComponent(".mescroll-wxs-content").setStyle({"will-change":"",transform:"",transition:""})}))},o.disabled=function(){return!o.optDown||!o.optDown.use||o.optDown.native},o.getPoint=function(t){return t?t.touches&&t.touches[0]?{x:t.touches[0].pageX,y:t.touches[0].pageY}:t.changedTouches&&t.changedTouches[0]?{x:t.changedTouches[0].pageX,y:t.changedTouches[0].pageY}:{x:t.clientX,y:t.clientY}:{x:0,y:0}},o.getAngle=function(t,o){var e=Math.abs(t.x-o.x),n=Math.abs(t.y-o.y),i=Math.sqrt(e*e+n*n),r=0;return 0!==i&&(r=Math.asin(n/i)/Math.PI*180),r},o.getScrollTop=function(){return o.scrollTop||0},o.getBodyHeight=function(){return o.bodyHeight||0},o.callMethod=function(t,o){t&&t.callMethod("wxsCall",o)},t.exports={propObserver:function(t){o.optDown=t.optDown,o.scrollTop=t.scrollTop,o.bodyHeight=t.bodyHeight,o.isDownScrolling=t.isDownScrolling,o.isUpScrolling=t.isUpScrolling,o.isUpBoth=t.isUpBoth,o.isScrollBody=t.isScrollBody,o.startTop=t.scrollTop},callObserver:function(t,e,n){o.disabled()||t.callType&&("showLoading"===t.callType?o.showLoading(n):"endDownScroll"===t.callType?o.endDownScroll(n):"clearTransform"===t.callType&&o.clearTransform(n))},touchstartEvent:function(t,e){o.downHight=0,o.startPoint=o.getPoint(t),o.startTop=o.getScrollTop(),o.startAngle=0,o.lastPoint=o.startPoint,o.maxTouchmoveY=o.getBodyHeight()-o.optDown.bottomOffset,o.inTouchend=!1,o.callMethod(e,{type:"setWxsProp"})},touchmoveEvent:function(t,n){var i=!0;if(o.disabled())return i;var r=o.getScrollTop(),s=o.getPoint(t),l=s.y-o.startPoint.y;if(l>0&&(o.isScrollBody&&r<=0||!o.isScrollBody&&(r<=0||r<=o.optDown.startTop&&r===o.startTop))&&!o.inTouchend&&!o.isDownScrolling&&!o.optDown.isLock&&(!o.isUpScrolling||o.isUpScrolling&&o.isUpBoth)){if(o.startAngle||(o.startAngle=o.getAngle(o.lastPoint,s)),o.startAngle<o.optDown.minAngle)return i;if(o.maxTouchmoveY>0&&s.y>=o.maxTouchmoveY)return o.inTouchend=!0,e(t,n),i;i=!1;var a=s.y-o.lastPoint.y;o.downHight<o.optDown.offset?(1!==o.movetype&&(o.movetype=1,o.callMethod(n,{type:"setLoadType",downLoadType:1}),o.isMoveDown=!0),o.downHight+=a*o.optDown.inOffsetRate):(2!==o.movetype&&(o.movetype=2,o.callMethod(n,{type:"setLoadType",downLoadType:2}),o.isMoveDown=!0),o.downHight+=a>0?a*o.optDown.outOffsetRate:a),o.downHight=Math.round(o.downHight);var c=o.downHight/o.optDown.offset;o.onMoving(n,c,o.downHight)}return o.lastPoint=s,i},touchendEvent:e},t.exports}({exports:{}})}},"0bbb":function(t,o,e){"use strict";e.r(o);var n=e("ad17"),i=e("12f9");for(var r in i)["default"].indexOf(r)<0&&function(t){e.d(o,t,(function(){return i[t]}))}(r);var s=e("cf41");for(var r in s)["default"].indexOf(r)<0&&function(t){e.d(o,t,(function(){return s[t]}))}(r);e("235d");var l=e("f0c5"),a=e("0a2f");i["default"].__module="renderBiz";var c=Object(l["a"])(s["default"],n["b"],n["c"],!1,null,"86fc822c",null,!1,n["a"],i["default"]);"function"===typeof a["a"]&&Object(a["a"])(c),o["default"]=c.exports},"12f9":function(t,o,e){"use strict";e.r(o);var n=e("b2a8"),i=e.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){e.d(o,t,(function(){return n[t]}))}(r);o["default"]=i.a},"235d":function(t,o,e){"use strict";var n=e("3ba6"),i=e.n(n);i.a},"3ba6":function(t,o,e){var n=e("c70e");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var i=e("4f06").default;i("2e27a74a",n,!0,{sourceMap:!1,shadowMode:!1})},ad17:function(t,o,e){"use strict";e.d(o,"b",(function(){return n})),e.d(o,"c",(function(){return i})),e.d(o,"a",(function(){}));var n=function(){var t=this,o=t.$createElement,e=t._self._c||o;return e("v-uni-view",{staticClass:"mescroll-uni-warp"},[e("v-uni-scroll-view",{staticClass:"mescroll-uni",class:{"mescroll-uni-fixed":t.isFixed},style:{height:t.scrollHeight,"padding-top":t.padTop,"padding-bottom":t.padBottom,top:t.fixedTop,bottom:t.fixedBottom,backgroundColor:t.bgColor},attrs:{id:t.viewId,"scroll-top":t.scrollTop,"scroll-with-animation":t.scrollAnim,"scroll-y":t.scrollable,"enable-back-to-top":!0,throttle:!1},on:{scroll:function(o){arguments[0]=o=t.$handleEvent(o),t.scroll.apply(void 0,arguments)}}},[e("v-uni-view",{wxsProps:{"change:prop":"wxsProp"},staticClass:"mescroll-uni-content mescroll-render-touch",attrs:{"change:prop":t.wxsBiz.propObserver,prop:t.wxsProp},on:{touchstart:function(o){o=t.$handleWxsEvent(o),t.wxsBiz.touchstartEvent(o,t.$getComponentDescriptor())},touchmove:function(o){o=t.$handleWxsEvent(o),t.wxsBiz.touchmoveEvent(o,t.$getComponentDescriptor())},touchend:function(o){o=t.$handleWxsEvent(o),t.wxsBiz.touchendEvent(o,t.$getComponentDescriptor())},touchcancel:function(o){o=t.$handleWxsEvent(o),t.wxsBiz.touchendEvent(o,t.$getComponentDescriptor())}}},[t.topbar&&t.statusBarHeight?e("v-uni-view",{staticClass:"mescroll-topbar",style:{height:t.statusBarHeight+"px",background:t.topbar}}):t._e(),e("v-uni-view",{wxsProps:{"change:prop":"callProp"},staticClass:"mescroll-wxs-content",style:{transform:t.translateY,transition:t.transition},attrs:{"change:prop":t.wxsBiz.callObserver,prop:t.callProp}},[t.mescroll.optDown.use?e("v-uni-view",{staticClass:"mescroll-downwarp",style:{background:t.mescroll.optDown.bgColor,color:t.mescroll.optDown.textColor}},[e("v-uni-view",{staticClass:"downwarp-content"},[e("v-uni-view",{staticClass:"downwarp-progress mescroll-wxs-progress",class:{"mescroll-rotate":t.isDownLoading},style:{"border-color":t.mescroll.optDown.textColor,transform:t.downRotate}}),e("v-uni-view",{staticClass:"downwarp-tip"},[t._v(t._s(t.downText))])],1)],1):t._e(),t._t("default"),t.isShowEmpty?e("mescroll-empty",{attrs:{option:t.mescroll.optUp.empty},on:{emptyclick:function(o){arguments[0]=o=t.$handleEvent(o),t.emptyClick.apply(void 0,arguments)}}}):t._e(),t.mescroll.optUp.use&&!t.isDownLoading&&3!==t.upLoadType?e("v-uni-view",{staticClass:"mescroll-upwarp",style:{background:t.mescroll.optUp.bgColor,color:t.mescroll.optUp.textColor}},[e("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:1===t.upLoadType,expression:"upLoadType===1"}]},[e("v-uni-view",{staticClass:"upwarp-progress mescroll-rotate",style:{"border-color":t.mescroll.optUp.textColor}}),e("v-uni-view",{staticClass:"upwarp-tip"},[t._v(t._s(t.mescroll.optUp.textLoading))])],1),2===t.upLoadType?e("v-uni-view",{staticClass:"upwarp-nodata"},[t._v(t._s(t.mescroll.optUp.textNoMore))]):t._e()],1):t._e()],2),t.bottombar&&t.windowBottom>0?e("v-uni-view",{staticClass:"mescroll-bottombar",style:{height:t.windowBottom+"px"}}):t._e(),t.safearea?e("v-uni-view",{staticClass:"mescroll-safearea"}):t._e()],1)],1),e("mescroll-top",{attrs:{option:t.mescroll.optUp.toTop},on:{click:function(o){arguments[0]=o=t.$handleEvent(o),t.toTopClick.apply(void 0,arguments)}},model:{value:t.isShowToTop,callback:function(o){t.isShowToTop=o},expression:"isShowToTop"}}),e("v-uni-view",{wxsProps:{"change:prop":"wxsProp"},attrs:{"change:prop":t.renderBiz.propObserver,prop:t.wxsProp}})],1)},i=[]},b2a8:function(t,o,e){"use strict";e("7a82");var n=e("ee27").default;Object.defineProperty(o,"__esModule",{value:!0}),o.default=void 0;var i=n(e("8c81")),r={mixins:[i.default]};o.default=r},bace:function(t,o,e){"use strict";e("7a82");var n=e("ee27").default;Object.defineProperty(o,"__esModule",{value:!0}),o.default=void 0,e("d401"),e("d3b7"),e("25f0"),e("a9e3"),e("c975"),e("ac1f"),e("5319"),e("e9c4"),e("498a");var i=n(e("c71f")),r=n(e("f122")),s=n(e("64a2")),l=n(e("99ff")),a=n(e("3058")),c={mixins:[a.default],components:{MescrollEmpty:s.default,MescrollTop:l.default},data:function(){return{mescroll:{optDown:{},optUp:{}},viewId:"id_"+Math.random().toString(36).substr(2,16),downHight:0,downRate:0,downLoadType:0,upLoadType:0,isShowEmpty:!1,isShowToTop:!1,scrollTop:0,scrollAnim:!1,windowTop:0,windowBottom:0,windowHeight:0,statusBarHeight:0}},props:{down:Object,up:Object,top:[String,Number],topbar:[Boolean,String],bottom:[String,Number],safearea:Boolean,fixed:{type:Boolean,default:!0},height:[String,Number],bottombar:{type:Boolean,default:!1},bgColor:{type:String,default:"transparent"}},computed:{isFixed:function(){return!this.height&&this.fixed},scrollHeight:function(){return this.isFixed?"auto":this.height?this.toPx(this.height)+"px":"100%"},numTop:function(){return this.toPx(this.top)},fixedTop:function(){return this.isFixed?this.numTop+this.windowTop+"px":0},padTop:function(){return this.isFixed?0:this.numTop+"px"},numBottom:function(){return this.toPx(this.bottom)},fixedBottom:function(){return this.isFixed?this.numBottom+this.windowBottom+"px":0},padBottom:function(){return this.isFixed?0:this.numBottom+"px"},isDownReset:function(){return 3===this.downLoadType||4===this.downLoadType},transition:function(){return this.isDownReset?"transform 300ms":""},translateY:function(){return this.downHight>0?"translateY("+this.downHight+"px)":""},scrollable:function(){return 0===this.downLoadType||this.isDownReset},isDownLoading:function(){return 3===this.downLoadType},downRotate:function(){return"rotate("+360*this.downRate+"deg)"},downText:function(){if(!this.mescroll)return"";switch(this.downLoadType){case 1:return this.mescroll.optDown.textInOffset;case 2:return this.mescroll.optDown.textOutOffset;case 3:return this.mescroll.optDown.textLoading;case 4:return this.mescroll.isDownEndSuccess?this.mescroll.optDown.textSuccess:0==this.mescroll.isDownEndSuccess?this.mescroll.optDown.textErr:this.mescroll.optDown.textInOffset;default:return this.mescroll.optDown.textInOffset}}},methods:{toPx:function(t){if("string"===typeof t)if(-1!==t.indexOf("px"))if(-1!==t.indexOf("rpx"))t=t.replace("rpx","");else{if(-1===t.indexOf("upx"))return Number(t.replace("px",""));t=t.replace("upx","")}else if(-1!==t.indexOf("%")){var o=Number(t.replace("%",""))/100;return this.windowHeight*o}return t?uni.upx2px(Number(t)):0},scroll:function(t){var o=this;this.mescroll.scroll(t.detail,(function(){o.$emit("scroll",o.mescroll)}))},emptyClick:function(){this.$emit("emptyclick",this.mescroll)},toTopClick:function(){this.mescroll.scrollTo(0,this.mescroll.optUp.toTop.duration),this.$emit("topclick",this.mescroll)},setClientHeight:function(){var t=this;0!==this.mescroll.getClientHeight(!0)||this.isExec||(this.isExec=!0,this.$nextTick((function(){t.getClientInfo((function(o){t.isExec=!1,o?t.mescroll.setClientHeight(o.height):3!=t.clientNum&&(t.clientNum=null==t.clientNum?1:t.clientNum+1,setTimeout((function(){t.setClientHeight()}),100*t.clientNum))}))})))},getClientInfo:function(t){var o=uni.createSelectorQuery();o=o.in(this);var e=o.select("#"+this.viewId);e.boundingClientRect((function(o){t(o)})).exec()}},created:function(){var t=this,o={down:{inOffset:function(){t.downLoadType=1},outOffset:function(){t.downLoadType=2},onMoving:function(o,e,n){t.downHight=n,t.downRate=e},showLoading:function(o,e){t.downLoadType=3,t.downHight=e},beforeEndDownScroll:function(o){return t.downLoadType=4,o.optDown.beforeEndDelay},endDownScroll:function(){t.downLoadType=4,t.downHight=0,t.downResetTimer&&clearTimeout(t.downResetTimer),t.downResetTimer=setTimeout((function(){4===t.downLoadType&&(t.downLoadType=0)}),300)},callback:function(o){t.$emit("down",o)}},up:{showLoading:function(){t.upLoadType=1},showNoMore:function(){t.$nextTick((function(){t.upLoadType=2}))},hideUpScroll:function(o){t.upLoadType=o.optUp.hasNext?0:3},empty:{onShow:function(o){t.isShowEmpty=o}},toTop:{onShow:function(o){t.isShowToTop=o}},callback:function(o){t.$emit("up",o),t.setClientHeight()}}};i.default.extend(o,r.default);var e=JSON.parse(JSON.stringify({down:t.down,up:t.up}));i.default.extend(e,o),t.mescroll=new i.default(e),t.mescroll.viewId=t.viewId,t.$emit("init",t.mescroll);var n=uni.getSystemInfoSync();n.windowTop&&(t.windowTop=n.windowTop),n.windowBottom&&(t.windowBottom=n.windowBottom),n.windowHeight&&(t.windowHeight=n.windowHeight),n.statusBarHeight&&(t.statusBarHeight=n.statusBarHeight),t.mescroll.setBodyHeight(n.windowHeight),t.mescroll.resetScrollTo((function(o,e){if(t.scrollAnim=0!==e,"string"!==typeof o){var n=t.mescroll.getScrollTop();0===e||300===e?(t.scrollTop=n,t.$nextTick((function(){t.scrollTop=o}))):t.mescroll.getStep(n,o,(function(o){t.scrollTop=o}),e)}else t.getClientInfo((function(e){var n,i=e.top;-1==o.indexOf("#")&&-1==o.indexOf(".")?n="#"+o:(n=o,-1!=o.indexOf(">>>")&&(n=o.split(">>>")[1].trim())),uni.createSelectorQuery().select(n).boundingClientRect((function(o){if(o){var e=t.mescroll.getScrollTop(),r=o.top-i;r+=e,t.isFixed||(r-=t.numTop),t.scrollTop=e,t.$nextTick((function(){t.scrollTop=r}))}else console.error(n+" does not exist")})).exec()}))})),t.up&&t.up.toTop&&null!=t.up.toTop.safearea||(t.mescroll.optUp.toTop.safearea=t.safearea)},mounted:function(){this.setClientHeight()}};o.default=c},bde1:function(t,o,e){"use strict";e("7a82"),Object.defineProperty(o,"__esModule",{value:!0}),o.default=void 0;var n={data:function(){return{mescroll:null}},onPullDownRefresh:function(){this.mescroll&&this.mescroll.onPullDownRefresh()},onPageScroll:function(t){this.mescroll&&this.mescroll.onPageScroll(t)},onReachBottom:function(){this.mescroll&&this.mescroll.onReachBottom()},methods:{mescrollInit:function(t){console.log(t),this.mescroll=t,this.mescrollInitByRef()},mescrollInitByRef:function(){if(!this.mescroll||!this.mescroll.resetUpScroll){var t=this.$refs.mescrollRef;t&&(this.mescroll=t.mescroll)}},downCallback:function(){var t=this;this.mescroll.optUp.use?this.mescroll.resetUpScroll():setTimeout((function(){t.mescroll.endSuccess()}),500)},upCallback:function(){var t=this;setTimeout((function(){t.mescroll.endErr()}),500)}},mounted:function(){this.mescrollInitByRef()}},i=n;o.default=i},c70e:function(t,o,e){var n=e("24fb");o=n(!1),o.push([t.i,".mescroll-uni-warp[data-v-86fc822c]{height:100%}.mescroll-uni-content[data-v-86fc822c]{height:100%;position:relative}.mescroll-uni[data-v-86fc822c]{\n\t/* border-radius: 20rpx 20rpx 0 0; */position:relative;width:100%;height:100%;min-height:%?200?%;overflow-y:auto;box-sizing:border-box /* 避免设置padding出现双滚动条的问题 */}\n\n/* 定位的方式固定高度 */.mescroll-uni-fixed[data-v-86fc822c]{z-index:1;position:fixed;top:0;left:0;right:0;bottom:0;width:auto; /* 使right生效 */height:auto /* 使bottom生效 */}\n\n/* 适配 iPhoneX */@supports (bottom:constant(safe-area-inset-bottom)) or (bottom:env(safe-area-inset-bottom)){.mescroll-safearea[data-v-86fc822c]{padding-bottom:constant(safe-area-inset-bottom);padding-bottom:env(safe-area-inset-bottom)}}\n\n/* 下拉刷新区域 */.mescroll-downwarp[data-v-86fc822c]{position:absolute;top:-100%;left:0;width:100%;height:100%;text-align:center}\n\n/* 下拉刷新--内容区,定位于区域底部 */.mescroll-downwarp .downwarp-content[data-v-86fc822c]{position:absolute;left:0;bottom:0;width:100%;min-height:%?60?%;padding:%?20?% 0;text-align:center}\n\n/* 下拉刷新--提示文本 */.mescroll-downwarp .downwarp-tip[data-v-86fc822c]{display:inline-block;font-size:%?28?%;vertical-align:middle;margin-left:%?16?%\n\t/* color: gray; 已在style设置color,此处删去*/}\n\n/* 下拉刷新--旋转进度条 */.mescroll-downwarp .downwarp-progress[data-v-86fc822c]{display:inline-block;width:%?32?%;height:%?32?%;border-radius:50%;border:%?2?% solid grey;border-bottom-color:transparent!important; /*已在style设置border-color,此处需加 !important*/vertical-align:middle}\n\n/* 旋转动画 */.mescroll-downwarp .mescroll-rotate[data-v-86fc822c]{-webkit-animation:mescrollDownRotate-data-v-86fc822c .6s linear infinite;animation:mescrollDownRotate-data-v-86fc822c .6s linear infinite}@-webkit-keyframes mescrollDownRotate-data-v-86fc822c{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}@keyframes mescrollDownRotate-data-v-86fc822c{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}\n\n/* 上拉加载区域 */.mescroll-upwarp[data-v-86fc822c]{box-sizing:border-box;min-height:%?110?%;padding:%?30?% 0;text-align:center;clear:both}\n\n/*提示文本 */.mescroll-upwarp .upwarp-tip[data-v-86fc822c],\n.mescroll-upwarp .upwarp-nodata[data-v-86fc822c]{display:inline-block;font-size:%?28?%;vertical-align:middle\n\t/* color: gray; 已在style设置color,此处删去*/}.mescroll-upwarp .upwarp-tip[data-v-86fc822c]{margin-left:%?16?%}\n\n/*旋转进度条 */.mescroll-upwarp .upwarp-progress[data-v-86fc822c]{display:inline-block;width:%?32?%;height:%?32?%;border-radius:50%;border:%?2?% solid grey;border-bottom-color:transparent!important; /*已在style设置border-color,此处需加 !important*/vertical-align:middle}\n\n/* 旋转动画 */.mescroll-upwarp .mescroll-rotate[data-v-86fc822c]{-webkit-animation:mescrollUpRotate-data-v-86fc822c .6s linear infinite;animation:mescrollUpRotate-data-v-86fc822c .6s linear infinite}@-webkit-keyframes mescrollUpRotate-data-v-86fc822c{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}@keyframes mescrollUpRotate-data-v-86fc822c{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}",""]),t.exports=o},cf41:function(t,o,e){"use strict";e.r(o);var n=e("bace"),i=e.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){e.d(o,t,(function(){return n[t]}))}(r);o["default"]=i.a}}]);