<?php
namespace app\common\command;

use app\common\server\WordUpdateServer;

use think\console\Command;
use think\console\Output;
use think\console\Input;


// 对应的命令行代码
class WordUpdate extends Command
{
    protected function configure()
    {
        $this->setName('word:update')
            ->setDescription('Update word splits for goods');
    }

    protected function execute(Input $input, Output $output)
    {
        $service = new WordUpdateServer();
        $service->start();
    }
}