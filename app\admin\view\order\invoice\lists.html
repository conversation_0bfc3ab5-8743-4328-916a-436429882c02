{layout name="layout1" /}

<div class="wrapper">
    <div class="layui-card">
        <!-- 操作提示 -->
        <div class="layui-card-body">
            <div class="layui-collapse" style="border:1px dashed #c4c4c4">
                <div class="layui-colla-item">
                    <h2 class="layui-colla-title like-layui-colla-title">操作提示</h2>
                    <div class="layui-colla-content layui-show">
                        <p>*商城发票管理中心。</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 搜索区域 -->
        <div class="layui-card-body layui-form">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label for="order_sn" class="layui-form-label">订单编号：</label>
                    <div class="layui-inline" style="margin-right:0;">
                        <div class="layui-input-inline" >
                            <input type="text" id="order_sn" name="order_sn" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">订单状态:</label>
                    <div class="layui-input-block">
                        <select name="order_status" id="order_status">
                            <option value="">全部</option>
                            {foreach $order_status as $item => $val}
                            <option value="{$item}">{$val}</option>
                            {/foreach}
                        </select>
                    </div>
                </div>
                <div class="layui-row">
                    <div class="layui-inline">
                        <label class="layui-form-label">下单时间:</label>
                        <div class="layui-input-inline">
                            <div class="layui-input-inline">
                                <input type="text" name="start_time" class="layui-input" id="start_time"
                                       placeholder="" autocomplete="off">
                            </div>
                        </div>
                        <div class="layui-input-inline" style="margin-right: 5px;width: 20px;">
                            <label class="layui-form-mid">至</label>
                        </div>
                        <div class="layui-input-inline">
                            <input type="text" name="end_time" class="layui-input" id="end_time"
                                   placeholder="" autocomplete="off">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <a class="layui-btn layui-btn-sm layui-btn-normal" lay-submit lay-filter="search">搜索</a>
                        <a class="layui-btn layui-btn-sm layui-btn-primary" lay-submit lay-filter="clear-search">重置</a>
                        <a class="layui-btn layui-btn-sm layui-btn-primary" lay-submit lay-filter="data-export">导出</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主体区域 -->
        <div class="layui-card-body">
            <div class="layui-tab layui-tab-card" lay-filter="like-tab">
                <ul class="layui-tab-title">
                    <li lay-id="" class="layui-this">全部</li>
                    <li lay-id="0">未开票</li>
                    <li lay-id="1">已开票</li>
                </ul>
                <div class="layui-tab-content" style="padding:20px;">
                    <table id="like-table-lists" lay-filter="like-table-lists"></table>
                    <script type="text/html" id="table-operation">
                        <a class="layui-btn layui-btn-sm layui-btn-normal" lay-event="invoice">发票详情</a>
                        <a class="layui-btn layui-btn-sm layui-btn-primary" lay-event="detail">订单详情</a>
                    </script>
                </div>
            </div>
        </div>

    </div>
</div>

<script>
    layui.use(["table", "form", "element", "laydate"], function(){
        var table   = layui.table;
        var form    = layui.form;
        var element = layui.element;
        var laydate = layui.laydate;


        //日期时间范围
        laydate.render({
            elem: '#start_time'
            , type: 'datetime'
            , theme: '#1E9FFF'
        });

        laydate.render({
            elem: '#end_time'
            , type: 'datetime'
            , theme: '#1E9FFF'
        });


        like.tableLists("#like-table-lists", "{:url('order.Invoice/lists')}", [
            {field:"order_sn", width:200, align:"center",title:"订单编号"}
            ,{field:"order_amount", width:100, align:"center", title:"订单金额"}
            ,{field:"type_text", width:220, align:"center", title:"发票类型"}
            ,{field:"header_type_text", width:130, align:"center", title:"发票抬头类型"}
            ,{field:"status_text", width:100, align:"center", title:"开票状态"}
            ,{field:"order_status", width:160, align:"center", title:"订单状态"}
            ,{field:"order_create_time", width:160, align:"center", title:"下单时间"}
            ,{title:"操作", width:200, align:"center", fixed:"right", toolbar:"#table-operation"}
        ]);


        var active = {
            invoice: function (obj) {
                layer.open({
                    type: 2
                    ,title: "发票详情"
                    ,content: "{:url('order.invoice/detail')}?id=" + obj.data.id
                    ,area: ["90%", "90%"]
                    ,yes: function(index, layero){
                    }
                });
            },
            detail: function (obj) {
                layer.open({
                    type: 2
                    ,title: '订单详情'
                    ,content: '{:url("order.order/detail")}?id='+obj.data.order_id
                    ,area: ['90%', '90%']
                    ,yes: function(index, layero){
                    }
                })
            }
        };
        like.eventClick(active);


        element.on("tab(like-tab)", function(){
            var status = this.getAttribute("lay-id");
            table.reload("like-table-lists", {
                where: {status: status},
                page: {
                    curr: 1
                }
            });
        });


        form.on("submit(search)", function(data){
            table.reload("like-table-lists", {
                where: data.field,
                page: {
                    curr: 1
                }
            });
        });


        form.on("submit(clear-search)", function(){
            $("#order_sn").val("");
            $("#order_status").val("");
            $("#start_time").val("");
            $("#end_time").val("");
            form.render();
            table.reload("like-table-lists", {
                where: {},
                page: {
                    curr: 1
                }
            });
        });


        // 导出
        form.on('submit(data-export)', function (data) {
            var field = data.field;
            like.ajax({
                url: '{:url("order.invoice/export")}'
                , data: field
                , type: 'get'
                , success: function (res) {
                    if (res.code == 1) {
                        window.location.href = res.data.url;
                    }
                }
            });
        });

    })
</script>