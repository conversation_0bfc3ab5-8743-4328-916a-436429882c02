<?php



namespace app\api\controller;

use app\common\server\ConfigServer;
use app\common\server\FileServer;
use app\common\basics\Api;
use app\common\server\JsonServer;
use app\common\server\UrlServer;

class File extends Api
{
    public $like_not_need_login = [];
    /**
     * showdoc
     * @catalog 接口/上传文件
     * @title form表单方式上传图片
     * @description 图片上传
     * @method post
     * @url /file/formimage
     * @return param name string 图片名称
     * @return param url string 文件地址
     * @remark
     * @number 1
     * @return {"code":1,"msg":"上传文件成功","data":{"url":"http:\/\/likeb2b2c.yixiangonline.com\/uploads\/images\/user\/20200810\/3cb866f6bb30b7239d91582f7d9822d6.png","name":"2.png"},"show":0,"time":"0.283254","debug":{"request":{"get":[],"post":[],"header":{"content-length":"103132","content-type":"multipart\/form-data; boundary=--------------------------206668736604428806173438","connection":"keep-alive","accept-encoding":"gzip, deflate, br","host":"www.likeb2b2c.com:20002","postman-token":"1f8aa4dd-f53c-4d12-98b4-8d901ac847db","cache-control":"no-cache","accept":"*\/*","user-agent":"PostmanRuntime\/7.26.2"}}}}
     */

    public function formImage()
    {
        try{
            $audit_article=ConfigServer::get('community', 'audit_article');
            $data = FileServer::image(0, 0, $this->user_id, 'uploads/user/'.$this->user_id);

            if($audit_article){

                $msg=image_filter($data['base_uri']);
                if($msg['data']['if_sensitive']){
                    $error_msg=isset($msg['data']['sensitive_msg'][0])?$msg['data']['sensitive_msg'][0]:'不合规';
                    //如果上传图片不合规，则返回信息

                    return JsonServer::error($error_msg);
                }
            }


            return JsonServer::success('上传成功', $data);
        }catch(\Exception $e) {
            return JsonServer::error($e->getMessage());
        }

    }


    /**
     * showdoc
     * @catalog 接口/上传文件
     * @title form表单方式上传图片
     * @description 图片上传
     * @method post
     * @url /file/formimage
     * @return param name string 图片名称
     * @return param url string 文件地址
     * @remark
     * @number 1
     * @return {"code":1,"msg":"上传文件成功","data":{"url":"http:\/\/likeb2b2c.yixiangonline.com\/uploads\/images\/user\/20200810\/3cb866f6bb30b7239d91582f7d9822d6.png","name":"2.png"},"show":0,"time":"0.283254","debug":{"request":{"get":[],"post":[],"header":{"content-length":"103132","content-type":"multipart\/form-data; boundary=--------------------------206668736604428806173438","connection":"keep-alive","accept-encoding":"gzip, deflate, br","host":"www.likeb2b2c.com:20002","postman-token":"1f8aa4dd-f53c-4d12-98b4-8d901ac847db","cache-control":"no-cache","accept":"*\/*","user-agent":"PostmanRuntime\/7.26.2"}}}}
     */
    public function formVideo()
    {
        try{
            $audit_article=ConfigServer::get('community', 'audit_article');
            $data = FileServer::video(0, 0, $this->user_id, 'uploads/user/'.$this->user_id);
//            $data['base_url'] ='https://www.huohanghang.cn/uploads/user/9/20241223/20241223103105091091070.mp4';#不合规
//            $data['base_url'] ='https://www.huohanghang.cn/uploads/user/9/20241223/2024122311133621b260528.mp4';#合规
//            $data['id']=122;
            if($audit_article){
                $msg=video_filter($data['base_url'],$data['id']);
                if(empty($msg)){
                    return JsonServer::error('视频文件损坏,请更换上传!');
                }else{
                    if(isset($msg['code']) && $msg['code']===1101){
                        return JsonServer::error($msg['msg'],[],1101);
                    }
                }

                if(isset($msg['data']['if_sensitive']) && !empty($msg['data']['if_sensitive'])){
                    if(is_array($msg['data']['sensitive_msg'])){
                        $error_msg=isset($msg['data']['sensitive_msg'][0])?$msg['data']['sensitive_msg'][0]:'不合规';
                    }else{
                        $error_msg='不合规';
                    }

                    return JsonServer::error($error_msg);
                }
            }
            return JsonServer::success('上传成功', $data);
        }catch(\Exception $e) {
            return JsonServer::error($e->getMessage());
        }
    }

    /**
     * showdoc
     * @catalog 接口/上传文件
     * @title form表单方式上传录音
     * @description 录音上传
     * @method post
     * @url /file/formaudio
     * @return param name string 录音名称
     * @return param url string 文件地址
     * @remark
     * @number 1
     * @return {"code":1,"msg":"上传成功","data":{"url":"http:\/\/likeb2b2c.yixiangonline.com\/uploads\/audio\/user\/20200810\/3cb866f6bb30b7239d91582f7d9822d6.mp3","name":"recording.mp3"},"show":0,"time":"0.283254"}
     */
    public function formAudio()
    {
        try{
            // 录音文件不需要审核，直接上传
            $data = FileServer::audio(0, 0, $this->user_id, 'uploads/user/'.$this->user_id);
            return JsonServer::success('上传成功', $data);
        }catch(\Exception $e) {
            return JsonServer::error($e->getMessage());
        }
    }

    /**
     * 将base64编码转换为文件并保存（支持上传到OSS）
     * @return \think\response\Json
     */
    public function base64tofile()
    {
        try {
            // 获取请求参数
            $post = $this->request->post();
            $base64 = $post['base64'] ?? '';
            $save_dir = $post['save_dir'] ?? 'uploads/base64/';
            $file_name = $post['file_name'] ?? '';
            $need_db = $post['need_db'] ?? false;
            $isLocal = $post['isLocal'] ?? false;
            $return_url = $post['return_url'] ?? false;

            // 如果没有提供base64数据，尝试从GET参数获取
            if (empty($base64)) {
                $get = $this->request->get();
                if (!empty($get['content'])) {
                    // 这里是处理二维码生成的情况
                    $param = [
                        'page' => $get['page'] ?? '',
                        'scene' => $get['scene'] ?? $get['content'] ?? '',
                        'save_dir' => $get['save_dir'] ?? $save_dir,
                        'file_name' => $get['file_name'] ?? $file_name,
                        'need_db' => $get['need_db'] ?? $need_db,
                        'cid' => $get['cid'] ?? 0,
                        'shop_id' => $get['shop_id'] ?? 0,
                        'user_id' => $this->user_id,
                        'isLocal' => $get['isLocal'] ?? $isLocal,
                        'return_url' => $get['return_url'] ?? $return_url
                    ];

                    $result = \app\common\server\QrCodeServer::makeMpWechatQrcode($param, 'base64tofile');
                    if ($result['code'] == 1) {
                        return JsonServer::success('生成成功', ['file_path' => $result['data']['qr_code']]);
                    } else {
                        return JsonServer::error($result['msg']);
                    }
                } else {
                    return JsonServer::error('缺少base64数据');
                }
            }

            // 使用FileServer的base64ToFile方法
            $file_path = FileServer::base64ToFile(
                $base64,
                $save_dir,
                $file_name,
                $need_db,
                $post['cid'] ?? 0,
                $post['shop_id'] ?? 0,
                $this->user_id,
                $isLocal
            );

            // 如果需要返回完整URL
            if ($return_url && !is_array($file_path)) {
                $file_path = UrlServer::getFileUrl($file_path);
            }

            return JsonServer::success('转换成功', ['file_path' => $file_path]);
        } catch (\Exception $e) {
            return JsonServer::error($e->getMessage());
        }
    }


}