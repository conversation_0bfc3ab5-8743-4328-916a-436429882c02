<?php

namespace app\shopapi\validate;

use think\Validate;

class RefundAddressValidate extends Validate
{
    protected $rule = [
        'id' => 'require|integer',
        'shop_id' => 'require|integer',
        'contact' => 'require|max:30',
        'mobile' => 'require|mobile',
        'province' => 'require|integer',
        'city' => 'require|integer',
        'district' => 'require|integer',
        'address' => 'require|max:200',
        'is_default' => 'in:0,1',
    ];

    protected $message = [
        'id.require' => '地址ID不能为空',
        'id.integer' => '地址ID必须为整数',
        'shop_id.require' => '商家ID不能为空',
        'shop_id.integer' => '商家ID必须为整数',
        'contact.require' => '联系人不能为空',
        'contact.max' => '联系人不能超过30个字符',
        'mobile.require' => '联系电话不能为空',
        'mobile.mobile' => '联系电话格式不正确',
        'province.require' => '省份不能为空',
        'province.integer' => '省份ID必须为整数',
        'city.require' => '城市不能为空',
        'city.integer' => '城市ID必须为整数',
        'district.require' => '区县不能为空',
        'district.integer' => '区县ID必须为整数',
        'address.require' => '详细地址不能为空',
        'address.max' => '详细地址不能超过200个字符',
        'is_default.in' => '是否默认地址参数错误',
    ];

    protected $scene = [
        'add' => ['shop_id', 'contact', 'mobile', 'province', 'city', 'district', 'address', 'is_default'],
        'edit' => ['id', 'shop_id', 'contact', 'mobile', 'province', 'city', 'district', 'address', 'is_default'],
    ];
    
    // 自定义手机号验证规则
    protected function mobile($value)
    {
        return preg_match('/^1[3-9]\d{9}$/', $value) ? true : false;
    }
}