<?php



namespace app\shop\controller;


use app\shop\logic\LoginLogic;
use app\shop\validate\LoginValidate;
use app\common\basics\ShopBase;
use app\common\server\JsonServer;
use app\shopapi\logic\ShopLogic as ShopApiLogic;

class Login extends ShopBase
{
    public $like_not_need_login = ['login'];

    /**
     * Notes: 登录
     * <AUTHOR> 15:08)
     */
    public function login()
    {
        if ($this->request->isAjax()) {
            $post = request()->post();
            (new LoginValidate())->goCheck();
            if (LoginLogic::login($post)){
                return JsonServer::success('登录成功');
            }
            $error = LoginLogic::getError() ?: '登录失败';
            return JsonServer::error($error);
        }
        return view('', [
            'account'  => cookie('account'),
            'config'  => LoginLogic::config(),
        ]);
    }

    /**
     * Notes: 退出登录
     * <AUTHOR> 18:44)
     */
    public function logout()
    {
        LoginLogic::logout();
        $this->redirect(url('login/login'));
    }

    /**
     * @notes 检查商家资料完整性
     * <AUTHOR>
     * @date 2024/05/17
     */
    public function checkProfileCompletion()
    {
        $shopId = $this->shop_id;
        $logic = new ShopApiLogic();
        $result = $logic->checkProfileCompletion($shopId);
        return JsonServer::success('ok', $result);
    }
}
