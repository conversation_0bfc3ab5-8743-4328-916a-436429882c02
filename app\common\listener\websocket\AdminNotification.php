<?php

namespace app\common\listener\websocket;

use app\common\websocket\Response;
use think\facade\Log;

/**
 * 管理员通知事件
 * Class AdminNotification
 * @package app\common\listener\websocket
 */
class AdminNotification
{
    protected $response;

    public function __construct(Response $response)
    {
        $this->response = $response;
    }

    /**
     * 处理管理员通知事件
     * @param array $params 参数
     * @return bool
     */
    public function handle($params)
    {
        $handleClass = $params['handle'];
        $fd = $params['fd'];
        $data = $params['data'] ?? [];

        // 获取当前管理员信息
        $admin = $handleClass->getAdminByFd($fd);
        if (empty($admin)) {
            Log::error("AdminNotification: 管理员信息不存在, fd={$fd}");
            return false;
        }

        // 记录通知事件
        Log::info("AdminNotification: 接收到通知事件, admin_id={$admin['uid']}, fd={$fd}, data=" . json_encode($data));

        // 根据通知类型处理
        $type = $data['type'] ?? '';
        switch ($type) {
            case 'system':
                // 系统通知，发送给所有管理员
                $this->sendSystemNotification($handleClass, $data);
                break;
            case 'personal':
                // 个人通知，只发送给指定管理员
                $this->sendPersonalNotification($handleClass, $data);
                break;
            default:
                // 默认通知类型
                $this->sendDefaultNotification($handleClass, $fd, $data);
                break;
        }

        return true;
    }

    /**
     * 发送系统通知
     * @param object $handleClass 处理类实例
     * @param array $data 通知数据
     * @return bool
     */
    protected function sendSystemNotification($handleClass, $data)
    {
        // 获取所有在线管理员
        $fds = $handleClass->room->getClients('admin_group');
        if (empty($fds)) {
            Log::info("AdminNotification: 没有在线管理员，无法发送系统通知");
            return false;
        }

        // 构建通知内容
        $notification = [
            'title' => $data['title'] ?? '系统通知',
            'content' => $data['content'] ?? '您有一条新的系统通知',
            'type' => 'system_notification',
            'url' => $data['url'] ?? '',
            'icon' => $data['icon'] ?? 1,
            'timestamp' => time()
        ];

        // 发送通知
        $handleClass->pushData($fds, 'notification', $notification);
        Log::info("AdminNotification: 系统通知已发送给" . count($fds) . "个管理员");

        return true;
    }

    /**
     * 发送个人通知
     * @param object $handleClass 处理类实例
     * @param array $data 通知数据
     * @return bool
     */
    protected function sendPersonalNotification($handleClass, $data)
    {
        $admin_id = $data['admin_id'] ?? 0;
        if (empty($admin_id)) {
            Log::error("AdminNotification: 个人通知缺少admin_id参数");
            return false;
        }

        // 获取管理员的连接标识符
        $fd = $handleClass->getFdByAdminId($admin_id);
        if (empty($fd)) {
            Log::info("AdminNotification: 管理员(ID={$admin_id})不在线，无法发送个人通知");
            return false;
        }

        // 构建通知内容
        $notification = [
            'title' => $data['title'] ?? '个人通知',
            'content' => $data['content'] ?? '您有一条新的个人通知',
            'type' => 'personal_notification',
            'url' => $data['url'] ?? '',
            'icon' => $data['icon'] ?? 0,
            'timestamp' => time()
        ];

        // 发送通知
        $handleClass->pushData($fd, 'notification', $notification);
        Log::info("AdminNotification: 个人通知已发送给管理员(ID={$admin_id})");

        return true;
    }

    /**
     * 发送默认通知
     * @param object $handleClass 处理类实例
     * @param int $fd 连接标识符
     * @param array $data 通知数据
     * @return bool
     */
    protected function sendDefaultNotification($handleClass, $fd, $data)
    {
        // 构建通知内容
        $notification = [
            'title' => $data['title'] ?? '通知',
            'content' => $data['content'] ?? '您有一条新的通知',
            'type' => 'admin_notification',
            'url' => $data['url'] ?? '',
            'icon' => $data['icon'] ?? 0,
            'timestamp' => time()
        ];

        // 发送通知
        $handleClass->pushData($fd, 'notification', $notification);
        Log::info("AdminNotification: 默认通知已发送给fd={$fd}");

        return true;
    }
}
