/* 布局修复 - 动态调整有无二级菜单的布局 */

/* 没有二级菜单时的布局调整 */
body.no-submenu .layui-sidebar {
    width: 110px !important;
}

body.no-submenu .layui-body {
    left: 110px !important;
    position: absolute !important;
}

body.no-submenu .layui-header .layui-layout-left {
    left: 110px !important;
}

/* 有二级菜单时的布局 - 确保不遮挡 */
body.has-submenu .layui-sidebar {
    width: 110px !important; /* 保持一级菜单宽度 */
}

body.has-submenu .layui-body {
    left: 232px !important; /* 110px(一级菜单) + 120px(二级菜单) + 2px(边距) */
    position: absolute !important;
}

body.has-submenu .layui-header .layui-layout-left {
    left: 232px !important;
}

/* 二级菜单的定位调整 */
body.has-submenu .layui-sidebar .layui-side-menu li .child-menu {
    left: 110px !important; /* 紧挨着一级菜单 */
    width: 120px !important;
    position: fixed !important;
    top: 50px !important;
    bottom: 0 !important;
    z-index: 40 !important;
    background: var(--bg-secondary, #FFFFFF) !important;
    border-right: 1px solid var(--border-color, #eee) !important;
}

/* 确保过渡效果 */
.layui-body {
    transition: left 0.3s ease !important;
}

.layui-sidebar {
    transition: width 0.3s ease !important;
}

.layui-header .layui-layout-left {
    transition: left 0.3s ease !important;
}
