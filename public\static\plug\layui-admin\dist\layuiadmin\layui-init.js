/**
 * Layui 初始化脚本
 * 解决 layui.cache.base 为 null 导致的错误
 */
(function() {
    'use strict';
    
    // 等待 layui 加载完成
    function initLayui() {
        if (typeof layui !== 'undefined') {
            // 确保 cache 对象存在
            if (!layui.cache) {
                layui.cache = {};
            }
            
            // 设置默认的 base 路径
            if (!layui.cache.base) {
                // 根据当前脚本路径自动检测
                var scripts = document.getElementsByTagName('script');
                var currentScript = scripts[scripts.length - 1];
                var src = currentScript.src;
                var basePath = src.substring(0, src.lastIndexOf('/') + 1);
                layui.cache.base = basePath;
            }
            
            // 确保 setter 对象存在并设置正确的路径
            if (!layui.setter) {
                layui.setter = {};
            }
            if (!layui.setter.base) {
                layui.setter.base = layui.cache.base;
            }
            
            console.log('Layui initialized with base path:', layui.cache.base);
        } else {
            // 如果 layui 还没加载，等待 100ms 后重试
            setTimeout(initLayui, 100);
        }
    }
    
    // 立即执行初始化
    initLayui();
    
    // 也在 DOM 加载完成后再次确保初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initLayui);
    } else {
        initLayui();
    }
})();
