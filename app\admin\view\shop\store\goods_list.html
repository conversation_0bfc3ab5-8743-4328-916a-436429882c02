{layout name="layout1" /}

<div class="wrapper">
    <div class="layui-card">
        <div class="layui-card-body">
            <table id="goodsListTable" lay-filter="goodsListTable"></table>
        </div>
    </div>
</div>

<script>
    layui.use(['table', 'form'], function() {
        var table = layui.table;
        var form = layui.form;
        var $ = layui.$;

        // 获取URL参数中的shop_id
        var shopId = new URLSearchParams(window.location.search).get('shop_id');

        if (shopId) {
            table.render({
                elem: '#goodsListTable',
                url: "{:url('shop.Store/getProducts')}", // 使用之前创建的获取商品列表接口
                where: { shop_id: shopId },
                page: true,
                limits: [10, 20, 30, 50],
                limit: 10,
                cols: [[
                    {field: 'id', title: 'ID', width: 80, sort: true},
                    {field: 'name', title: '商品名称'},
                    {field: 'status_desc', title: '销售状态', width: 100},
                    {field: 'del_desc', title: '删除状态', width: 100},
                    {field: 'audit_status_desc', title: '审核状态', width: 100},
                    {field: 'update_time', title: '更新时间', width: 180, sort: true}
                ]]
            });
        } else {
            layer.msg('缺少商家ID参数', {icon: 2});
        }
    });
</script>
