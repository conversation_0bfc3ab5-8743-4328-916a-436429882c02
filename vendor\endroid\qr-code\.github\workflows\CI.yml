name: CI

on: [push, pull_request]

jobs:
  build:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        php-versions: ['7.2', '7.3', '7.4']
      fail-fast: false
    steps:
      - uses: actions/checkout@v2
      - uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php-versions }}
          extensions: gd, mbstring, pcov, zip
          ini-values: max_execution_time=600, memory_limit=-1
      - name: Install dependencies
        run: |
          curl -sS https://getcomposer.org/installer | php -- --filename=composer
          ./composer install
      - name: Check code quality
        run: |
          vendor/bin/code-quality
      - name: Test against highest versions
        run: |
          vendor/bin/unit-test
          vendor/bin/functional-test
      - name: Test against lowest versions
        run: |
          ./composer update --prefer-lowest
          vendor/bin/unit-test
          vendor/bin/functional-test ^4.4
      - name: Archive code coverage results
        uses: actions/upload-artifact@v2
        with:
          name: coverage
          path: tests/coverage
