<?php

namespace app\shop\logic\decoration;

use app\api\logic\GoodsColumnLogic;
use app\common\basics\Logic;
use app\common\enum\ShopAdEnum;
use app\common\model\AdOrder;
use app\common\model\AdPosition;
use app\common\model\distribution\DistributionGoods;
use app\common\model\shop\ShopAd;
use app\common\model\Ad;
use app\common\server\FileServer;
use app\common\server\UrlServer;
use think\facade\Db;
use think\facade\Validate;

class ShopAdLogic extends Logic
{
    static function lists($params, $shop_id)
    {
        $where = [
            ['shop_id', '=', $shop_id],
        ];
        $page = $params['page'] ?? 1;
        $limit = $params['limit'] ?? 10;

        $append = ['place_name', 'terminal_name', 'status_name'];

        $lists = Ad::where($where)->page($page, $limit)->append($append)->order('id desc')->select()->toArray();
        $count = Ad::where($where)->count();
        foreach ($lists as $k => &$v) {

           $ad_option=Db::name('ad_position')->where('id', $v['pid'])->find();
           $v['image2']=UrlServer::getFileUrl($ad_option['image']);
           $ad_order=Db::name('ad_order')->where('id', $v['ad_order_id'])->find();
           $v['end_time']=$ad_order['end_time']?date('Y-m-d H:i:s',$ad_order['end_time']):'永久';
        }
        return ['count' => $count, 'lists' => $lists];
    }

    static function buylists($params, $shop_id)
    {
        $where = [
            ['is_buy', '=', 1],
        ];
        $page = $params['page'] ?? 1;
        $limit = $params['limit'] ?? 10;
        $lists = AdPosition::where($where)->page($page, $limit)->order('id desc')->select()->toArray();
        $count = AdPosition::where($where)->count();
        $billing_cycle_txt = ['天', '周', '月', '年'];
        foreach ($lists as $k => &$v) {
            $v['image'] = UrlServer::getFileUrl($v['image']);
            $v['cycle'] = '一' . $billing_cycle_txt[$v['billing_cycle']] . '起售';
            $ad_order = Db::name('ad_order')
                ->where('shop_id', $shop_id)
                ->where('ad_position_id', $v['id'])
                ->where('status', 1)
                ->find();

            $ad_count=Db::name('ad_order')
                ->where([
                    'ad_position_id'=> $v['id'],
                    'status'=>1,
                ])
                ->whereTime('end_time', '>', time())
                ->sum('ad_buynums');

            $v['have_nums']=$v['ad_nums']-$ad_count;
            $v['status_name'] = '未购买';
            if ($ad_order) {
                if ($ad_order['end_time'] > time() && $ad_order['status'] == 1) {
                    $v['status_name'] = '已购买';
                } else if ($ad_order['end_time'] < time() && $ad_order['status'] == 1) {
                    $v['status_name'] = '已过期';
                }
            }

            if(!empty($v['have_nums'])){
                $v['buy_status'] = 1;
            }else{
                $v['buy_status'] = 0;
            }

        }
        return ['count' => $count, 'lists' => $lists];
    }

    static function add($params, $shop_id)
    {

        $params['terminal']=0;

        if (empty($params['image'])) {
            static::$error = '广告图片必须';
            return false;
        }
        if (empty($params['place'])) {
            static::$error = '位置必须选择';
            return false;
        }
        //已购买的广告位
        $ad_buynums=Db::name('ad_order')
            ->where('shop_id', $shop_id)
            ->where('ad_position_id', $params['place'])
            ->whereTime('end_time', '>', time())
            ->where('status', 1)
            ->sum('ad_buynums');
        //已布置的广告位
        $have_nums= Db::name('ad')->where('pid', $params['place'])
            ->where('status', 1)
            ->where('del', 0)
            ->where('shop_id',$shop_id)
            ->count();

        //剩余的广告位
        if($ad_buynums<=$have_nums){
            static::$error = '该广告位已满或已过期,无法添加更多广告,请续费或者选择其他广告位';
            return false;
        }
        $ad_order = Db::name('ad_order')
            ->where('shop_id', $shop_id)
            ->where('ad_position_id', $params['place'])
            ->whereTime('end_time', '>', time())
            ->where('status', 1)
            ->order('id asc')
            ->find();

        if(empty($ad_order['id'])){
            static::$error = '该广告位已满或已过期,无法添加更多广告,请续费或者选择其他广告位';
            return false;
        }
        $params['shop_id'] = $shop_id;
        $params['pid'] = $params['place'];
        $params['create_time'] = time();
        $params['update_time'] = time();
        $params['terminal'] = 1;
        $params['link_type'] =$params['link']?2:0;
        if($params['link']){
            //只获取文本?后面的id
            $params['link'] = explode('?id=', $params['link']);
            if(count($params['link'])>1){
                $params['link']=$params['link'][1];
            }else{
                static::$error = '链接格式不正确';
                return false;
            }
        }
        $params['ad_order_id']=$ad_order['id'];
        $params['start_time']=$ad_order['create_time'];
        $params['end_time']=$ad_order['end_time'];
        Db::name('ad')->strict(false)->insert($params);
        return true;
    }

    static function edit($params, $shop_id)
    {



        if (empty($params['image'])) {
            static::$error = '广告图片必须';
            return false;
        }

        $where = [
            ['id', '=', $params['id']],
            ['shop_id', '=', $shop_id],
        ];
        $params['link_type'] =$params['link']?2:0;
        if($params['link']){
           //只获取文本?后面的id
            $params['link'] = explode('?id=', $params['link']);
            if(count($params['link'])>1){
                $params['link']=$params['link'][1];
            }else{
                static::$error = '链接格式不正确';
                return false;
            }
        }
        Ad::update($params, $where, [
            'shop_id',
            'title',
            'link_type',
            'image',
            'link'
        ]);

        return true;
    }

    static function status($params, $shop_id)
    {
        $where = [
            ['id', '=', $params['id']],
            ['shop_id', '=', $shop_id],
        ];

        Ad::update($params, $where, ['status']);

        return true;
    }

    static function shopstatus($params, $shop_id)
    {
       $place=Db::name('shop_ad')->where('id',$params['id'])->value('place');
       if($params['status']==1){
           $buy_nums=Db::name('ad_order')
               ->where('shop_id', $shop_id)
               ->where('status', 1)
               ->where('ad_position_id', $place)
               ->whereTime('end_time', '>', time())
               ->sum('ad_buynums');

           $shop_ad=Db::name('ad')
               ->where('pid',$place)
               ->where('shop_id',$shop_id)
               ->where('status',1)
               ->where('del',0)
               ->count();

           if((int)$buy_nums<=$shop_ad){

               return '广告位已满或已过期,无法启用';
           }
       }



        $where = [
            ['id', '=', $params['id']],
            ['shop_id', '=', $shop_id],
        ];

        ShopAd::update($params, $where, ['status']);

        return true;
    }

    static function delete($params, $shop_id)
    {
        Ad::destroy(function ($query) use ($params, $shop_id) {
            $query->where('shop_id', $shop_id)->where('id', $params['id']);
        });

        return true;
    }

    static function shopdelete($params, $shop_id)
    {
        Ad::destroy(function ($query) use ($params, $shop_id) {
            $query->where('shop_id', $shop_id)->where('id', $params['id']);
        });

        return true;
    }

    /*
     * 广告订单购买
     */

    static function AdOrderadd($params, $shop_id)
    {

        if (empty($params['id'])) {
            static::$error = '参数错误';
            return false;
        }
        if (empty($params['buy_time'])) {
            static::$error = '广告购买时间必须';
            return false;
        }

        if (empty($params['ad_buynums'])) {
            static::$error = '广告购买数量必须';
            return false;
        }
        $adinfo=Db::name('ad_position')->where('id', $params['id'])->find();
        $ad_count=Db::name('ad_order')
            ->where([
                'ad_position_id'=> $params['id'],
                'status'=>1,
            ])
            ->whereTime('end_time', '>', time())
            ->sum('ad_buynums');
        $have_nums=$adinfo['ad_nums']-$ad_count;
        if(empty($have_nums)){
            static::$error = '广告购买数量超出或已售罄';
            return false;
        }
        //根据$adinfo获取计费周期
        $billing_cycle=$adinfo['billing_cycle'];
        switch ($billing_cycle) {
            case 0:
                //按天计费
                $buy_time=$params['buy_time']*24*60*60;
                break;
            case 1:
                //按周计费
                $buy_time=$params['buy_time']*7*24*60*60;
                break;
            case 2:
                //按月计费
                $buy_time=$params['buy_time']*30*24*60*60;
                break;
            case 3:
                //按年计费
                $buy_time=$params['buy_time']*365*24*60*60;
                break;
            default:
                # code...
                break;
        }
        /*获取当前商家用户的user表的id*/
        $user_id=Db::name('user')->where('shop_id', $shop_id)->value('id');
        $params['user_id']=$user_id;


        $data=[
            'order_sn'=>createSn('ad_order','order_sn'),
            'ad_position_id'=>$params['id'],
            'user_id'=>$params['user_id']??0,
            'ad_buynums'=>$params['ad_buynums'],
            'shop_id'=>$shop_id,
            'status'=>0,
            'buy_time'=>$buy_time,
            'create_time'=>time(),
            'ad_price'=>$adinfo['ad_fee']*$params['ad_buynums']*$params['buy_time'],
            'ad_sn'=>$adinfo['ad_sn'],
            'ad_yn'=>$adinfo['ad_yn'],
        ];
        $params['shop_id'] = $shop_id;
        $order_id=AdOrder::create($data);
        return $order_id['id'];
    }

    /*
    *广告购买记录
    */
    static function buyloglists($params, $shop_id)
    {
        $where = [
            ['shop_id', '=', $shop_id],
        ];
        $page = $params['page'] ?? 1;
        $limit = $params['limit'] ?? 10;

        $lists = AdOrder::where($where)->page($page, $limit)->order('id desc')->select()->toArray();
        $count = AdOrder::where($where)->count();
        return ['count' => $count, 'lists' => $lists];
    }

}