<?php


namespace app\common\basics;


use think\App;
use think\exception\HttpResponseException;

/**
 * index基类
 * Class AdminBase
 * <AUTHOR>
 * @package app\common\basics
 */
abstract class IndexBase
{
    /**
     * Request实例
     */
    protected $request;

    /**
     * 应用实例
     */
    protected $app;

    /**
     * 构造方法
     * @access public
     * @param  App  $app  应用对象
     */
    public function __construct(App $app)
    {
        $this->app     = $app;
        $this->request = $this->app->request;
    }


    /**
     * Notes: 自定义重定向
     * @param mixed ...$args
     * <AUTHOR> 14:04)
     */
    public function redirect(...$args)
    {
        throw new HttpResponseException(redirect(...$args));
    }



}