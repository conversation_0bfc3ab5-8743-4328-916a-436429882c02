<?php



namespace app\common\model;


use app\common\basics\Models;
use app\common\model\goods\Goods;
use app\common\model\goods\GoodsItem;
use app\common\model\shop\Shop;

/**
 * 购物车
 * Class Cart
 * @package app\common\model
 */
class Cart extends Models
{

    public function goods()
    {
        return $this->hasOne(Goods::class, 'id', 'goods_id');
    }

    public function goodsItem()
    {
        return $this->hasOne(GoodsItem::class, 'id', 'item_id');
    }

    public function shop()
    {
        return $this->hasOne(Shop::class, 'id', 'shop_id');
    }

}