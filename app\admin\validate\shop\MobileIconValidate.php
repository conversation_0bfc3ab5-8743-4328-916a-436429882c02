<?php

namespace app\admin\validate\shop;

use app\common\basics\Validate;

/**
 * 移动端图标配置验证器
 * Class MobileIconValidate
 * @package app\admin\validate\shop
 */
class MobileIconValidate extends Validate
{
    protected $rule = [
        'id' => 'require|number',
        'icon_name' => 'require|max:50',
        'icon_url' => 'max:255',
        'icon_path' => 'max:255',
        'auth_id' => 'number',
        'sort_order' => 'number|between:0,999',
        'status' => 'in:0,1'
    ];

    protected $message = [
        'id.require' => 'ID不可为空',
        'id.number' => 'ID必须为数字',
        'icon_name.require' => '图标名称不可为空',
        'icon_name.max' => '图标名称不能超过50个字符',
        'icon_url.max' => '图标URL不能超过255个字符',
        'icon_path.max' => '图标路径不能超过255个字符',
        'auth_id.number' => '权限ID必须为数字',
        'sort_order.number' => '排序必须为数字',
        'sort_order.between' => '排序值必须在0-999之间',
        'status.in' => '状态值错误'
    ];

    protected $scene = [
        'add' => ['icon_name', 'icon_url', 'icon_path', 'auth_id', 'sort_order', 'status'],
        'edit' => ['id', 'icon_name', 'icon_url', 'icon_path', 'auth_id', 'sort_order', 'status']
    ];
}
