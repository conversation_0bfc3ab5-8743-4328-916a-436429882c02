<?php



namespace app\common\enum;


class SmsEnum
{
    /**
     * 短信发送状态
     */
    const SEND_ING = 0;
    const SEND_SUCCESS = 1;
    const SEND_FAIL = 2;

    public static function getSendStatusDesc($from)
    {
        $desc = [
            self::SEND_ING => '发送中',
            self::SEND_SUCCESS => '发送成功',
            self::SEND_FAIL => '发送失败',
        ];
        if ($from === true) {
            return $desc;
        }
        return $desc[$from] ?? '';
    }
}