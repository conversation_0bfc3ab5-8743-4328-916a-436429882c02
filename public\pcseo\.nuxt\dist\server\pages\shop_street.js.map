{"version": 3, "file": "pages/shop_street.js", "sources": ["webpack:///./components/shop-item.vue?4a98", "webpack:///./components/shop-item.vue?5f0b", "webpack:///./components/shop-item.vue?0a3d", "webpack:///./components/shop-item.vue?5656", "webpack:///./components/shop-item.vue", "webpack:///./components/shop-item.vue?d5fd", "webpack:///./components/shop-item.vue?eb53", "webpack:///./pages/shop_street.vue?4114", "webpack:///./pages/shop_street.vue?972d", "webpack:///./pages/shop_street.vue?c326", "webpack:///./pages/shop_street.vue?6c49", "webpack:///./pages/shop_street.vue", "webpack:///./pages/shop_street.vue?6a24", "webpack:///./pages/shop_street.vue?c943"], "sourcesContent": ["// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/sass-resources-loader/lib/loader.js??ref--7-oneOf-1-4!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./shop-item.vue?vue&type=style&index=0&id=871c1244&lang=scss&scoped=true&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"d6370bb2\", content, true, context)\n};", "export * from \"-!../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/sass-resources-loader/lib/loader.js??ref--7-oneOf-1-4!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./shop-item.vue?vue&type=style&index=0&id=871c1244&lang=scss&scoped=true&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".shop-item[data-v-871c1244]{width:270px;height:400px;background-size:cover;background-position:50%;padding:10px;border-radius:6px}.shop-item .shop-info[data-v-871c1244]{border-radius:6px;padding:18px 15px}.shop-item .shop-info .logo[data-v-871c1244]{width:70px;height:70px;border-radius:16px;margin-top:-45px}.shop-item .shop-info .sales[data-v-871c1244]{display:inline-block;padding:4px 10px;background-color:#f2f2f2;margin-top:6px;border-radius:4px}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('nuxt-link',{staticClass:\"shop-item flex-col row-right\",style:({\n        'background-image': (\"url(\" + _vm.cover + \")\"),\n    }),attrs:{\"to\":(\"/shop_street_detail?id=\" + _vm.shopId)}},[_c('div',{staticClass:\"bg-white shop-info text-center\"},[_c('el-image',{staticClass:\"logo\",attrs:{\"src\":_vm.logo}}),_vm._v(\" \"),_c('div',{staticClass:\"m-t-12 line-1 lg\"},[(_vm.type == 1)?_c('el-tag',{attrs:{\"size\":\"mini\"}},[_vm._v(\"自营\")]):_vm._e(),_vm._v(\" \"+_vm._s(_vm.name)+\"\\n        \")],1),_vm._v(\" \"),_c('span',{staticClass:\"xs muted sales\"},[_vm._v(\"共\"+_vm._s(_vm.sales)+\"件商品\")])],1)])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nexport default {\n    components: {},\n    props: {\n        cover: {\n            type: String,\n        },\n        shopId: {\n            type: [String, Number],\n        },\n        logo: {\n            type: String,\n        },\n        type: {\n            type: [String, Number],\n        },\n        name: {\n            type: String,\n        },\n        sales: {\n            type: [String, Number],\n        }\n    },\n    methods: {},\n}\n", "import mod from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./shop-item.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./shop-item.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./shop-item.vue?vue&type=template&id=871c1244&scoped=true&\"\nimport script from \"./shop-item.vue?vue&type=script&lang=js&\"\nexport * from \"./shop-item.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./shop-item.vue?vue&type=style&index=0&id=871c1244&lang=scss&scoped=true&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  \"871c1244\",\n  \"2279e038\"\n  \n)\n\nexport default component.exports", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/sass-resources-loader/lib/loader.js??ref--7-oneOf-1-4!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./shop_street.vue?vue&type=style&index=0&id=62a34c9e&lang=scss&scoped=true&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"5790a1c6\", content, true, context)\n};", "export * from \"-!../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/sass-resources-loader/lib/loader.js??ref--7-oneOf-1-4!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./shop_street.vue?vue&type=style&index=0&id=62a34c9e&lang=scss&scoped=true&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".shop-street[data-v-62a34c9e]{width:1180px;padding:20px 0}.shop-street .shop-cart[data-v-62a34c9e]{width:270px;height:400px;margin-bottom:20px}.shop-street .shop-cart[data-v-62a34c9e]:not(:nth-of-type(4n)){margin-right:20px}.shop-street .shop-cart .shop-desc[data-v-62a34c9e]{width:249px;height:124px;background-color:#fff;margin-top:247px;margin-bottom:9px;border-radius:6px;position:relative}.shop-street .shop-cart .shop-desc .shop-logo[data-v-62a34c9e]{position:absolute;top:-26px;left:89px;z-index:10}.shop-street .shop-cart .shop-desc .shop-name[data-v-62a34c9e]{margin-top:52px;padding:0 10px;margin-bottom:4px;text-align:center;font-size:16px;color:#101010}.shop-street .shop-cart .shop-desc .goods-num[data-v-62a34c9e]{width:82px;height:24px;text-align:center;background:#e5e5e5;padding-top:4px;margin-bottom:20px;font-size:12px;border-radius:4px;color:#999}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"street\"},[_vm._ssrNode(\"<div class=\\\"shop-street flex flex-wrap flex-center\\\" data-v-62a34c9e>\",\"</div>\",_vm._l((_vm.shopList),function(item,index){return _vm._ssrNode(\"<div data-v-62a34c9e>\",\"</div>\",[_vm._ssrNode(\"<div class=\\\"shop-cart\\\" data-v-62a34c9e>\",\"</div>\",[_c('shop-item',{attrs:{\"cover\":item.cover,\"shopId\":item.id,\"logo\":item.logo,\"type\":item.type,\"name\":item.name,\"sales\":item.on_sale_goods}})],1)])}),0),_vm._ssrNode(\" \"),(_vm.count)?_vm._ssrNode(\"<div style=\\\"padding-bottom: 38px; text-align: center\\\" data-v-62a34c9e>\",\"</div>\",[_c('el-pagination',{attrs:{\"background\":\"\",\"layout\":\"prev, pager, next\",\"total\":_vm.count,\"prev-text\":\"上一页\",\"next-text\":\"下一页\",\"hide-on-single-page\":\"\",\"page-size\":_vm.pageSize},on:{\"current-change\":_vm.changePage}})],1):_vm._e()],2)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\r\nexport default {\r\n    head() {\r\n        return {\r\n            title: this.$store.getters.headTitle,\r\n            link: [\r\n                {\r\n                    rel: 'icon',\r\n                    type: 'image/x-icon',\r\n                    href: this.$store.getters.favicon,\r\n                },\r\n            ],\r\n        }\r\n    },\r\n\r\n    async asyncData({ query, $get }) {\r\n        const {\r\n            data: { list, count },\r\n        } = await $get('shop/getShopList', {\r\n            params: {\r\n                page_size: 8,\r\n                page_no: 1,\r\n            },\r\n        })\r\n        return {\r\n            shopList: list,\r\n            count: count,\r\n        }\r\n    },\r\n\r\n    data() {\r\n        return {\r\n            shopList: [],\r\n            count: 0,\r\n            page: 1,\r\n            pageSize: 8,\r\n        }\r\n    },\r\n\r\n    methods: {\r\n        changePage(current) {\r\n            this.page = current\r\n            this.getShopList()\r\n        },\r\n\r\n        async getShopList() {\r\n            const { page, pageSize } = this\r\n            const {\r\n                data: { list, count },\r\n                code,\r\n            } = await this.$get('shop/getShopList', {\r\n                params: {\r\n                    page_size: pageSize,\r\n                    page_no: page,\r\n                },\r\n            })\r\n            if (code == 1) {\r\n                this.shopList = list\r\n                this.count = count\r\n            }\r\n        },\r\n    },\r\n}\r\n", "import mod from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./shop_street.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./shop_street.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./shop_street.vue?vue&type=template&id=62a34c9e&scoped=true&\"\nimport script from \"./shop_street.vue?vue&type=script&lang=js&\"\nexport * from \"./shop_street.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./shop_street.vue?vue&type=style&index=0&id=62a34c9e&lang=scss&scoped=true&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  \"62a34c9e\",\n  \"4577ddca\"\n  \n)\n\nexport default component.exports\n\n/* nuxt-component-imports */\ninstallComponents(component, {ShopItem: require('/Users/<USER>/Desktop/vue/pc/components/shop-item.vue').default})\n"], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACXA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACNA;AACA;AACA;AACA;AACA;;;;;ACJA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AADA;AAGA;AACA;AADA;AAGA;AACA;AADA;AAGA;AACA;AADA;AAGA;AACA;AADA;AAGA;AACA;AADA;AAhBA;AAoBA;AAtBA;;ACrBA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;ACvBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACXA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACNA;AACA;AACA;;;;;ACFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAHA;AAHA;AAUA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AADA;AAGA;AACA;AACA;AAFA;AADA;AAMA;AACA;AACA;AAFA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAJA;AAMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AADA;AACA;AAKA;AACA;AACA;AACA;AACA;AACA;AAtBA;AAtCA;;ACzCA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;A", "sourceRoot": ""}