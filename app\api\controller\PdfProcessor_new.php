<?php
namespace app\api\controller;

use app\api\logic\PdfSignatureProcessor;
use app\api\logic\PdfCreator;
use app\api\logic\ImageMagickPdfProcessor;
use app\api\logic\GdPdfProcessor;
use app\common\basics\Api;
use app\common\server\JsonServer;
use app\common\server\UrlServer;
use app\common\server\FileServer;
use think\facade\Log;
use Exception;

/**
 * PDF处理控制器
 * Class PdfProcessor
 * @package app\api\controller
 */
class PdfProcessor extends Api
{
    public $like_not_need_login = ['addSignature', 'createSignedPdf', 'addSignatureWithImageMagick', 'addSignatureWithGd'];

    /**
     * 使用GD库添加签名和日期到PDF文件
     * @return \think\response\Json
     */
    public function addSignatureWithGd()
    {
        try {
            // 获取请求参数
            $post = $this->request->post();
            
            // 检查必要参数
            if (empty($post['signature_base64']) && empty($post['signature_url'])) {
                return JsonServer::error('缺少签名图片数据或图片URL');
            }
            
            // 获取PDF文件路径
            $pdfPath = public_path() . 'images/share/zsxy.pdf';
            if (!file_exists($pdfPath)) {
                return JsonServer::error('PDF文件不存在');
            }
            
            // 准备临时目录
            $tempDir = runtime_path() . 'temp';
            // 确保temp目录存在
            if (!is_dir($tempDir)) {
                // 如果temp目录不存在，使用runtime目录
                $tempDir = runtime_path();
            }
            
            // 处理签名图片
            if (!empty($post['signature_url'])) {
                // 使用URL获取图片
                $signatureUrl = $post['signature_url'];
                
                // 从URL中提取文件扩展名
                $pathInfo = pathinfo($signatureUrl);
                $imageType = isset($pathInfo['extension']) ? strtolower($pathInfo['extension']) : 'jpg';
                
                // 确保扩展名是有效的图片类型
                if (!in_array($imageType, ['png', 'jpg', 'jpeg'])) {
                    $imageType = 'jpg'; // 默认使用jpg
                }
                
                // 保存签名图片到临时文件
                $signaturePath = $tempDir . '/' . uniqid() . '.' . $imageType;
                
                // 下载图片
                try {
                    // 使用curl下载图片（更可靠）
                    $ch = curl_init();
                    curl_setopt($ch, CURLOPT_URL, $signatureUrl);
                    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
                    curl_setopt($ch, CURLOPT_TIMEOUT, 30); // 增加超时时间
                    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
                    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true); // 允许重定向
                    $imageData = curl_exec($ch);
                    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                    curl_close($ch);
                    
                    if (!$imageData || $httpCode != 200) {
                        // 如果curl失败，尝试使用file_get_contents
                        $imageData = @file_get_contents($signatureUrl);
                        if ($imageData === false) {
                            return JsonServer::error('无法从URL下载图片，HTTP状态码: ' . $httpCode);
                        }
                    }
                    
                    // 保存图片
                    if (file_put_contents($signaturePath, $imageData) === false) {
                        return JsonServer::error('保存签名图片失败，可能是权限问题');
                    }
                    
                    // 验证图片是否有效
                    if (!file_exists($signaturePath)) {
                        return JsonServer::error('保存后的签名图片文件不存在');
                    }
                    
                    $fileSize = filesize($signaturePath);
                    if ($fileSize <= 0) {
                        return JsonServer::error('保存的签名图片文件为空，大小: ' . $fileSize);
                    }
                    
                    // 验证图片格式
                    $imageInfo = @getimagesize($signaturePath);
                    if (!$imageInfo) {
                        return JsonServer::error('无法获取图片信息，可能不是有效的图片文件');
                    }
                    
                    // 记录图片信息到日志
                    Log::info('签名图片信息: ' . json_encode($imageInfo));
                    Log::info('签名图片路径: ' . $signaturePath);
                    Log::info('签名图片大小: ' . $fileSize . ' 字节');
                    
                } catch (Exception $e) {
                    return JsonServer::error('下载图片时出错: ' . $e->getMessage());
                }
            } else {
                // 使用base64数据
                $signatureBase64 = $post['signature_base64'];
                
                // 检查并提取base64数据
                if (preg_match('/^data:image\/(png|jpeg|jpg|gif);base64,/', $signatureBase64, $matches)) {
                    $imageType = $matches[1];
                    $signatureBase64 = preg_replace('/^data:image\/(png|jpeg|jpg|gif);base64,/', '', $signatureBase64);
                    $signatureBase64 = str_replace(' ', '+', $signatureBase64); // 修复可能的空格问题
                } else {
                    // 如果没有前缀，假设是纯base64数据
                    $imageType = 'jpg'; // 默认使用jpg
                }
                
                // 确保扩展名是有效的图片类型
                if (!in_array($imageType, ['png', 'jpg', 'jpeg'])) {
                    $imageType = 'jpg';
                }
                
                // 保存签名图片到临时文件
                $signaturePath = $tempDir . '/' . uniqid() . '.' . $imageType;
                
                // 解码并保存
                $imageData = base64_decode($signatureBase64);
                if (!$imageData) {
                    return JsonServer::error('无效的base64图片数据');
                }
                
                if (file_put_contents($signaturePath, $imageData) === false) {
                    return JsonServer::error('保存签名图片失败，可能是权限问题');
                }
                
                // 验证图片是否有效
                if (!file_exists($signaturePath)) {
                    return JsonServer::error('保存后的签名图片文件不存在');
                }
                
                $fileSize = filesize($signaturePath);
                if ($fileSize <= 0) {
                    return JsonServer::error('保存的签名图片文件为空，大小: ' . $fileSize);
                }
                
                // 验证图片格式
                $imageInfo = @getimagesize($signaturePath);
                if (!$imageInfo) {
                    return JsonServer::error('无法获取图片信息，可能不是有效的图片文件');
                }
                
                // 记录图片信息到日志
                Log::info('签名图片信息: ' . json_encode($imageInfo));
                Log::info('签名图片路径: ' . $signaturePath);
                Log::info('签名图片大小: ' . $fileSize . ' 字节');
            }
            
            // 设置输出PDF路径
            $baseOutputDir = public_path() . 'uploads/pdf';
            // 确保基本输出目录存在
            if (!is_dir($baseOutputDir)) {
                // 如果pdf目录不存在，使用uploads目录
                $baseOutputDir = public_path() . 'uploads';
                // 如果uploads目录也不存在，使用public目录
                if (!is_dir($baseOutputDir)) {
                    $baseOutputDir = public_path();
                }
            }
            $outputPath = $baseOutputDir . '/' . uniqid() . '.pdf';
            
            // 创建日志文件
            $logFile = runtime_path() . 'log/pdf_processor.log';
            file_put_contents($logFile, date('Y-m-d H:i:s') . ' - 开始处理PDF文件' . PHP_EOL, FILE_APPEND);
            file_put_contents($logFile, date('Y-m-d H:i:s') . ' - PDF文件路径: ' . $pdfPath . PHP_EOL, FILE_APPEND);
            file_put_contents($logFile, date('Y-m-d H:i:s') . ' - 签名图片路径: ' . $signaturePath . PHP_EOL, FILE_APPEND);
            file_put_contents($logFile, date('Y-m-d H:i:s') . ' - 输出PDF路径: ' . $outputPath . PHP_EOL, FILE_APPEND);
            
            // 检查签名图片是否存在
            if (!file_exists($signaturePath)) {
                file_put_contents($logFile, date('Y-m-d H:i:s') . ' - 错误: 签名图片文件不存在: ' . $signaturePath . PHP_EOL, FILE_APPEND);
                return JsonServer::error('签名图片文件不存在: ' . $signaturePath);
            }
            
            // 检查图片文件大小
            $fileSize = filesize($signaturePath);
            file_put_contents($logFile, date('Y-m-d H:i:s') . ' - 签名图片文件大小: ' . $fileSize . ' 字节' . PHP_EOL, FILE_APPEND);
            if ($fileSize <= 0) {
                file_put_contents($logFile, date('Y-m-d H:i:s') . ' - 错误: 签名图片文件为空: ' . $signaturePath . ', 大小: ' . $fileSize . PHP_EOL, FILE_APPEND);
                return JsonServer::error('签名图片文件为空: ' . $signaturePath . ', 大小: ' . $fileSize);
            }
            
            // 创建处理器实例
            file_put_contents($logFile, date('Y-m-d H:i:s') . ' - 创建处理器实例' . PHP_EOL, FILE_APPEND);
            $processor = new GdPdfProcessor($pdfPath);
            $processor->setSignaturePath($signaturePath)
                ->setOutputPath($outputPath);
            
            // 设置签名位置（根据实际PDF调整）
            $signatureX = $post['signature_x'] ?? 400;
            $signatureY = $post['signature_y'] ?? 680;
            $signatureWidth = $post['signature_width'] ?? 80;
            $signatureHeight = $post['signature_height'] ?? 40;
            $processor->setSignaturePosition($signatureX, $signatureY, $signatureWidth, $signatureHeight);
            
            // 设置日期位置（根据实际PDF调整）
            $dateX = $post['date_x'] ?? 400;
            $dateY = $post['date_y'] ?? 720;
            $processor->setDatePosition($dateX, $dateY);
            
            // 处理PDF
            $pageNumber = $post['page_number'] ?? 1;
            // 使用简单的日期格式，避免中文乱码问题
            $dateFormat = $post['date_format'] ?? 'Y-m-d';
            file_put_contents($logFile, date('Y-m-d H:i:s') . ' - 开始处理PDF, 页码: ' . $pageNumber . ', 日期格式: ' . $dateFormat . PHP_EOL, FILE_APPEND);
            
            try {
                $resultPath = $processor->process($pageNumber, $dateFormat);
                file_put_contents($logFile, date('Y-m-d H:i:s') . ' - PDF处理完成, 输出路径: ' . $resultPath . PHP_EOL, FILE_APPEND);
                
                // 检查输出文件是否存在
                if (!file_exists($resultPath)) {
                    file_put_contents($logFile, date('Y-m-d H:i:s') . ' - 错误: 输出PDF文件不存在: ' . $resultPath . PHP_EOL, FILE_APPEND);
                    return JsonServer::error('输出PDF文件不存在: ' . $resultPath);
                }
                
                $outputFileSize = filesize($resultPath);
                file_put_contents($logFile, date('Y-m-d H:i:s') . ' - 输出PDF文件大小: ' . $outputFileSize . ' 字节' . PHP_EOL, FILE_APPEND);
                if ($outputFileSize <= 0) {
                    file_put_contents($logFile, date('Y-m-d H:i:s') . ' - 错误: 输出PDF文件为空: ' . $resultPath . ', 大小: ' . $outputFileSize . PHP_EOL, FILE_APPEND);
                    return JsonServer::error('输出PDF文件为空: ' . $resultPath . ', 大小: ' . $outputFileSize);
                }
            } catch (Exception $e) {
                file_put_contents($logFile, date('Y-m-d H:i:s') . ' - 错误: 处理PDF时出错: ' . $e->getMessage() . PHP_EOL, FILE_APPEND);
                file_put_contents($logFile, date('Y-m-d H:i:s') . ' - 错误位置: ' . $e->getFile() . ':' . $e->getLine() . PHP_EOL, FILE_APPEND);
                file_put_contents($logFile, date('Y-m-d H:i:s') . ' - 错误堆栈: ' . $e->getTraceAsString() . PHP_EOL, FILE_APPEND);
                return JsonServer::error('处理PDF文件时出错: ' . $e->getMessage());
            }
            
            // 清理临时文件
            file_put_contents($logFile, date('Y-m-d H:i:s') . ' - 清理临时文件: ' . $signaturePath . PHP_EOL, FILE_APPEND);
            @unlink($signaturePath);
            
            // 返回结果
            $fileUrl = UrlServer::getFileUrl(str_replace(public_path(), '', $resultPath));
            
            // 确定使用的签名来源
            $signatureSource = !empty($post['signature_url']) ? '图片URL' : 'base64图片';
            
            return JsonServer::success('处理成功', [
                'file_path' => $resultPath,
                'file_url' => $fileUrl,
                'message' => "已成功使用{$signatureSource}在PDF文件中添加签名和日期"
            ]);
            
        } catch (Exception $e) {
            // 记录详细错误信息
            $signatureSource = !empty($post['signature_url']) ? '图片URL' : 'base64图片';
            Log::error("PDF处理错误(使用GD库，{$signatureSource}): " . $e->getMessage());
            Log::error('错误位置: ' . $e->getFile() . ':' . $e->getLine());
            Log::error('错误堆栈: ' . $e->getTraceAsString());
            
            // 清理临时文件
            if (isset($signaturePath) && file_exists($signaturePath)) {
                @unlink($signaturePath);
            }
            
            if (isset($outputPath) && file_exists($outputPath)) {
                @unlink($outputPath);
            }
            
            return JsonServer::error('处理PDF文件时出错: ' . $e->getMessage());
        }
    }
}
