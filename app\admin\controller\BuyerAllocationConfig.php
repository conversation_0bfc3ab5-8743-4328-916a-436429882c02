<?php
namespace app\admin\controller;

use app\admin\model\BuyerAllocationConfig as BuyerAllocationConfigModel;
use app\common\basics\AdminBase;
use app\common\server\JsonServer;
use think\facade\View;

/**
 * 采购人员分配配置控制器 (已优化)
 */
class BuyerAllocationConfig extends AdminBase
{
    /**
     * @notes 列表
     */
    public function index()
    {
        if ($this->request->isAjax()) {
            $params = $this->request->get();
            $lists = BuyerAllocationConfigModel::lists($params);
            return JsonServer::success('获取成功', $lists);
        }
        return view();
    }

    /**
     * @notes 添加
     */
    public function add()
    {
        if ($this->request->isAjax()) {
            $params = $this->request->post();
            $validationError = $this->validateRequest($params);
            if ($validationError) {
                return $validationError;
            }

            $check = BuyerAllocationConfigModel::where('tier_level', $params['tier_level'])->find();
            if ($check) {
                return JsonServer::error('该商家等级的配置已存在');
            }

            $params['create_time'] = time();
            BuyerAllocationConfigModel::create($params);
            return JsonServer::success('添加成功');
        }
        return view('form');
    }

    /**
     * @notes 编辑
     */
    public function edit($id)
    {
        $detail = BuyerAllocationConfigModel::find($id);
        if (!$detail) {
            return JsonServer::error('未找到相关记录');
        }

        if ($this->request->isAjax()) {
            $params = $this->request->post();
            $validationError = $this->validateRequest($params, $id);
            if ($validationError) {
                return $validationError;
            }

            $check = BuyerAllocationConfigModel::where('tier_level', $params['tier_level'])->where('id', '<>', $id)->find();
            if ($check) {
                return JsonServer::error('该商家等级的配置已存在');
            }

            $params['update_time'] = time();
            $detail->save($params);
            return JsonServer::success('更新成功');
        }

        View::assign('detail', $detail);
        return view('form');
    }

    /**
     * @notes 删除
     */
    public function delete($id)
    {
        $result = BuyerAllocationConfigModel::destroy($id);
        if ($result) {
            return JsonServer::success('删除成功');
        }
        return JsonServer::error('删除失败');
    }

    /**
     * @notes 验证请求参数
     * @param array $params
     * @param int|null $id
     * @return \think\response\Json|null
     */
    private function validateRequest(array $params, int $id = null)
    {
        if (!isset($params['tier_level']) || $params['tier_level'] === '') {
            return JsonServer::error('请选择商家等级');
        }
        $totalRatio = ($params['level_1_ratio'] ?? 0) + ($params['level_2_ratio'] ?? 0) + ($params['level_3_ratio'] ?? 0);
        if ($totalRatio != 100) {
            return JsonServer::error('三个活跃度等级的比例总和必须等于100%');
        }
        return null;
    }
}
