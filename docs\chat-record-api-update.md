# 聊天记录API更新说明

## 🎯 需求描述

更新 `UserLogic::getChatRecord` 方法，新增 `$to_id` 参数，实现以下功能：

1. **客服聊天**：当 `shop_id` 不为空时，获取与商家客服的聊天记录
2. **用户聊天**：当 `shop_id` 为空且 `to_id` 不为空时，获取与指定用户的聊天记录
3. **空参数处理**：当两个参数都为空时，返回空结果

## 🔧 方法签名更新

### 原方法签名
```php
public static function getChatRecord($user_id, $shop_id, $page, $size)
```

### 新方法签名
```php
public static function getChatRecord($user_id, $to_id, $shop_id, $page, $size)
```

### 参数说明
- `$user_id`: 当前用户ID
- `$to_id`: 聊天对象用户ID（用户对用户聊天时使用）
- `$shop_id`: 商家ID（客服聊天时使用）
- `$page`: 页码
- `$size`: 每页数量

## 📊 逻辑流程

```mermaid
flowchart TD
    A[开始] --> B{shop_id 是否为空?}
    B -->|不为空| C[调用 getKefuChatRecord]
    B -->|为空| D{to_id 是否为空?}
    D -->|不为空| E[调用 getUserToChatRecord]
    D -->|为空| F[返回空结果]
    C --> G[返回客服聊天记录]
    E --> H[返回用户聊天记录]
    F --> I[返回空数据结构]
```

## 🔄 实现细节

### 1. 主方法逻辑
```php
public static function getChatRecord($user_id, $to_id, $shop_id, $page, $size)
{
    // 如果shop_id不为空，获取与商家客服的聊天记录
    if (!empty($shop_id)) {
        return self::getKefuChatRecord($user_id, $shop_id, $page, $size);
    }
    
    // 如果shop_id为空且to_id不为空，获取用户对用户的聊天记录
    if (!empty($to_id)) {
        return self::getUserToChatRecord($user_id, $to_id, $page, $size);
    }
    
    // 如果两个参数都为空，返回空结果
    return [
        'config' => 0,
        'kefu' => null,
        'record' => [
            'list' => [],
            'page' => $page,
            'size' => $size,
            'count' => 0,
            'more' => false
        ]
    ];
}
```

### 2. 客服聊天记录处理
```php
private static function getKefuChatRecord($user_id, $shop_id, $page, $size)
{
    // 查询条件：指定shop_id的客服聊天记录
    $map1 = [
        ['shop_id', '=', $shop_id],
        ['from_id', '=', $user_id],
        ['from_type', '=', 'user'],
    ];
    $map2 = [
        ['shop_id', '=', $shop_id],
        ['to_id', '=', $user_id],
        ['to_type', '=', 'user'],
    ];
    
    // 原有的客服聊天逻辑保持不变
    // ...
}
```

### 3. 用户对用户聊天记录处理
```php
private static function getUserToChatRecord($user_id, $to_id, $page, $size)
{
    // 查询条件：shop_id=0的用户间聊天记录
    $map1 = [
        ['shop_id', '=', 0],
        ['from_id', '=', $user_id],
        ['from_type', '=', 'user'],
        ['to_id', '=', $to_id],
        ['to_type', '=', 'user'],
    ];
    $map2 = [
        ['shop_id', '=', 0],
        ['from_id', '=', $to_id],
        ['from_type', '=', 'user'],
        ['to_id', '=', $user_id],
        ['to_type', '=', 'user'],
    ];
    
    // 获取聊天记录并标记已读
    // 返回特殊的用户聊天格式
}
```

## 📱 API调用示例

### 1. 获取客服聊天记录
```bash
GET /api/user/getChatRecord?shop_id=1&page=1&size=20
```

### 2. 获取用户对用户聊天记录
```bash
GET /api/user/getChatRecord?to_id=123&page=1&size=20
```

### 3. 空参数调用
```bash
GET /api/user/getChatRecord?page=1&size=20
```

## 📋 返回数据格式

### 客服聊天返回格式
```json
{
    "code": 1,
    "msg": "获取成功",
    "data": {
        "config": 1,
        "kefu": {
            "id": 1,
            "nickname": "客服小王",
            "avatar": "头像URL"
        },
        "record": {
            "list": [...],
            "page": 1,
            "size": 20,
            "count": 15,
            "more": false
        }
    }
}
```

### 用户聊天返回格式
```json
{
    "code": 1,
    "msg": "获取成功",
    "data": {
        "config": 1,
        "kefu": null,
        "chat_user": {
            "id": 123,
            "nickname": "用户昵称",
            "avatar": "头像URL"
        },
        "chat_type": "user_chat",
        "record": {
            "list": [...],
            "page": 1,
            "size": 20,
            "count": 10,
            "more": false
        }
    }
}
```

### 空参数返回格式
```json
{
    "code": 1,
    "msg": "获取成功",
    "data": {
        "config": 0,
        "kefu": null,
        "record": {
            "list": [],
            "page": 1,
            "size": 20,
            "count": 0,
            "more": false
        }
    }
}
```

## 🧪 测试接口

为了方便测试新功能，创建了专门的测试控制器：

### 测试路由
```
GET /api/chat_record_test/new_chat_record?to_id=123&shop_id=0    # 测试用户聊天
GET /api/chat_record_test/new_chat_record?to_id=0&shop_id=1     # 测试客服聊天
GET /api/chat_record_test/user_chat?to_id=123                   # 直接测试用户聊天
GET /api/chat_record_test/kefu_chat?shop_id=1                   # 直接测试客服聊天
GET /api/chat_record_test/empty_params                          # 测试空参数
```

## ✅ 验证要点

### 功能验证
1. **客服聊天**：`shop_id > 0` 时正确返回客服聊天记录
2. **用户聊天**：`to_id > 0` 且 `shop_id = 0` 时正确返回用户聊天记录
3. **参数优先级**：`shop_id` 优先于 `to_id`
4. **空参数处理**：两个参数都为空时返回空结果
5. **已读标记**：消息正确标记为已读

### 数据验证
1. **客服聊天**：查询 `shop_id > 0` 的记录
2. **用户聊天**：查询 `shop_id = 0` 且双方都是 `user` 类型的记录
3. **数据格式**：返回数据结构符合预期
4. **聊天对象信息**：用户聊天包含聊天对象的用户信息

## 🔧 兼容性说明

### 向后兼容
- 新增了 `$to_id` 参数，但保持了原有的客服聊天功能
- 客服聊天的逻辑和返回格式完全不变
- 现有调用客服聊天的代码需要适配新的参数顺序

### 调用方式更新
```php
// 原来的调用方式
UserLogic::getChatRecord($user_id, $shop_id, $page, $size);

// 新的调用方式
UserLogic::getChatRecord($user_id, $to_id, $shop_id, $page, $size);
```

## 📝 注意事项

1. **参数顺序**：新增的 `$to_id` 参数位于 `$user_id` 之后，`$shop_id` 之前
2. **优先级**：`shop_id` 优先于 `to_id`，如果两个都不为空，优先处理客服聊天
3. **数据库查询**：用户聊天记录的 `shop_id` 必须为 0
4. **权限控制**：确保用户只能获取自己参与的聊天记录
5. **性能考虑**：大量聊天记录时注意分页和索引优化

通过这些修改，现在可以通过同一个接口灵活地获取不同类型的聊天记录！
