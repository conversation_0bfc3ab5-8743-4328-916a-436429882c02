<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Apcas\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * 调用明细返回数据体
 *
 * @method integer getTotalCount() 获取符合条件的总条数
 * @method void setTotalCount(integer $TotalCount) 设置符合条件的总条数
 * @method array getCallDetailSet() 获取调用明细数组
 * @method void setCallDetailSet(array $CallDetailSet) 设置调用明细数组
 */
class CallDetails extends AbstractModel
{
    /**
     * @var integer 符合条件的总条数
     */
    public $TotalCount;

    /**
     * @var array 调用明细数组
     */
    public $CallDetailSet;

    /**
     * @param integer $TotalCount 符合条件的总条数
     * @param array $CallDetailSet 调用明细数组
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("TotalCount",$param) and $param["TotalCount"] !== null) {
            $this->TotalCount = $param["TotalCount"];
        }

        if (array_key_exists("CallDetailSet",$param) and $param["CallDetailSet"] !== null) {
            $this->CallDetailSet = [];
            foreach ($param["CallDetailSet"] as $key => $value){
                $obj = new CallDetailItem();
                $obj->deserialize($value);
                array_push($this->CallDetailSet, $obj);
            }
        }
    }
}
