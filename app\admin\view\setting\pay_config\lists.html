{layout name="layout1" /}
<style>
    .layui-table-cell {height: auto; }
</style>
<div class="wrapper">
    <div class="layui-card">
        <!--操作提示-->
        <div class="layui-card-body">
            <div class="layui-collapse like-layui-collapse" lay-accordion="" style="border:1px dashed #c4c4c4">
                <div class="layui-colla-item">
                    <h2 class="layui-colla-title like-layui-colla-title" style="background-color: #fff">操作提示</h2>
                    <div class="layui-colla-content layui-show">
                        <p>* 商城支付方式设置，不同应用场景支持的支付方式不同。</p>
                        <p>* 请前往
                            <a href="https://pay.weixin.qq.com" style="color:deepskyblue;" target="_blank">微信支付</a> 、
                            <a href="https://open.alipay.com" style="color:deepskyblue;" target="_blank">支付宝支付</a>、
                            <a href="https://paas.huifu.com" style="color:deepskyblue;" target="_blank">汇付支付</a>
                            申请对应的支付密钥。</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-form layui-card-header layuiadmin-card-header-auto">
            <h1 class="site-h1">支付设置</h1>
        </div>

        <div class="layui-card-body" id="card-body">
            <table id="like-table-lists" lay-filter="like-table-lists"></table>
            <script type="text/html" id="icon">
                <img src="{{d.image}}" style="width: 48px; height: 48px" class="image-show">
            </script>

            <script type="text/html" id="table-operation">
                <a class="layui-btn layui-btn-normal layui-btn-sm" lay-event="edit">配置</a>
            </script>
        </div>

    </div>
</div>


<script>
    layui.use(['table','form'], function(){
        var $ = layui.$
            ,form = layui.form
            ,table = layui.table;


        like.tableLists('#like-table-lists', '{:url("setting.pay_config/lists")}', [
            {field: 'name', title: '支付方式'}
            ,{field: 'image', title: '图标', toolbar: '#icon', align: 'center'}
            ,{field: 'short_name', title: '简称', align: 'center'}
            ,{field: 'status', title: '状态', minWidth: 40, align: 'center'}
            ,{field: 'sort', title: '排序', event: 'tips',  sort: true}
            ,{fixed: 'right', title: '操作', align: 'center', toolbar: '#table-operation'}
        ]);

        table.on('tool(like-table-lists)', function(obj) {
            if(obj.event === 'edit') {
                var name = obj.data.name;
                var code = obj.data.code;

                var edit_page;
                if (code == 'balance') {
                    edit_page = 'editBalance';
                }else if (code == 'wechat') {
                    edit_page = 'editWechat';
                }else if (code　==　'alipay') {
                    edit_page = 'editAlipay';
                }else if (code　==　'hfdg_wechat') {
                    edit_page = 'editHfdgWechat';
                }else if (code　==　'hfdg_alipay') {
                    edit_page = 'editHfdgAlipay';
                }else if (code　==　'offline') {
                    edit_page = 'editOffline';
                }
                
                layer.open({
                    type: 2
                    ,title: '编辑'+name
                    ,content: '{:url("setting.pay_config/'+edit_page+'")}?name='+name
                    ,area: ['90%', '90%']
                    ,btn: ['确定', '取消']
                    ,yes: function(index, layero)　{
                        var iframeWindow = window['layui-layer-iframe'+ index]
                            ,submit = layero.find('iframe').contents().find('#edit-submit');
                        //监听提交
                        iframeWindow.layui.form.on('submit(edit-submit)', function(data){
                            var field = data.field;
                            like.ajax({
                                url:'{:url("setting.pay_config/'+edit_page+'")}',
                                data:field,
                                type:"post",
                                success:function(res)
                                {
                                    if(res.code == 1)
                                    {
                                        layui.layer.msg(res.msg, {
                                            offset: '15px'
                                            , icon: 1
                                            , time: 1000
                                        });
                                        layer.close(index);
                                        table.reload('like-table-lists');
                                    }
                                }
                            });
                        });
                        submit.trigger('click');
                    }
                })
            }
        });
    });

</script>