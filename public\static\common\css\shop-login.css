/* 商家端登录页面专用样式 */

/* 全局样式重置和基础设置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', <PERSON><PERSON>, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    overflow-x: hidden;
}

/* 背景动态效果容器 */
.background-effects {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
    overflow: hidden;
}

/* 增强版粒子动画 */
.particles-container {
    position: absolute;
    width: 100%;
    height: 100%;
}

.particle {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(1px);
}

.particle-1 {
    width: 4px;
    height: 4px;
    top: 20%;
    left: 10%;
    animation: float-1 8s ease-in-out infinite;
}

.particle-2 {
    width: 6px;
    height: 6px;
    top: 60%;
    left: 20%;
    animation: float-2 10s ease-in-out infinite;
}

.particle-3 {
    width: 3px;
    height: 3px;
    top: 40%;
    left: 70%;
    animation: float-3 12s ease-in-out infinite;
}

.particle-4 {
    width: 5px;
    height: 5px;
    top: 80%;
    left: 80%;
    animation: float-4 9s ease-in-out infinite;
}

.particle-5 {
    width: 4px;
    height: 4px;
    top: 30%;
    left: 90%;
    animation: float-5 11s ease-in-out infinite;
}

/* 粒子浮动动画 */
@keyframes float-1 {
    0%, 100% { transform: translateY(0px) translateX(0px); opacity: 0.3; }
    25% { transform: translateY(-20px) translateX(10px); opacity: 0.7; }
    50% { transform: translateY(-40px) translateX(-5px); opacity: 1; }
    75% { transform: translateY(-20px) translateX(-10px); opacity: 0.7; }
}

@keyframes float-2 {
    0%, 100% { transform: translateY(0px) translateX(0px); opacity: 0.4; }
    33% { transform: translateY(-30px) translateX(-15px); opacity: 0.8; }
    66% { transform: translateY(-15px) translateX(20px); opacity: 1; }
}

@keyframes float-3 {
    0%, 100% { transform: translateY(0px) translateX(0px); opacity: 0.2; }
    50% { transform: translateY(-25px) translateX(-20px); opacity: 0.9; }
}

@keyframes float-4 {
    0%, 100% { transform: translateY(0px) translateX(0px); opacity: 0.5; }
    25% { transform: translateY(-35px) translateX(15px); opacity: 0.8; }
    75% { transform: translateY(-10px) translateX(-25px); opacity: 0.6; }
}

@keyframes float-5 {
    0%, 100% { transform: translateY(0px) translateX(0px); opacity: 0.3; }
    40% { transform: translateY(-20px) translateX(-10px); opacity: 1; }
    80% { transform: translateY(-30px) translateX(5px); opacity: 0.7; }
}

/* 波浪效果 */
.wave-effects {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 200px;
    background: linear-gradient(180deg, transparent 0%, rgba(255, 255, 255, 0.05) 100%);
    overflow: hidden;
}

.wave {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 200%;
    height: 100px;
    background: linear-gradient(90deg,
        transparent 0%,
        rgba(255, 255, 255, 0.1) 25%,
        rgba(255, 255, 255, 0.2) 50%,
        rgba(255, 255, 255, 0.1) 75%,
        transparent 100%);
    border-radius: 50% 50% 0 0;
    animation: wave-move 15s ease-in-out infinite;
}

.wave:nth-child(2) {
    animation-delay: -5s;
    opacity: 0.7;
    height: 80px;
}

.wave:nth-child(3) {
    animation-delay: -10s;
    opacity: 0.5;
    height: 60px;
}

@keyframes wave-move {
    0%, 100% { transform: translateX(-50%) translateY(0px); }
    50% { transform: translateX(-50%) translateY(-20px); }
}

/* 登录容器 */
.login-container {
    position: relative;
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    padding: 20px;
}

/* 顶部Logo */
.header-logo {
    position: absolute;
    top: 30px;
    left: 40px;
    z-index: 10;
}

.logo-img {
    height: 50px;
    width: auto;
    filter: drop-shadow(0 2px 8px rgba(0, 0, 0, 0.1));
}

/* 主登录区域 */
.login-main {
    display: flex;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 24px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    max-width: 1000px;
    width: 100%;
    min-height: 600px;
}

/* 左侧装饰区域 */
.login-left {
    flex: 1;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 60px 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.left-content {
    text-align: center;
    color: white;
    z-index: 2;
    position: relative;
}

.welcome-text h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 16px;
    background: linear-gradient(135deg, #ffffff 0%, #f0f0f0 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.welcome-text p {
    font-size: 1.1rem;
    opacity: 0.9;
    margin-bottom: 40px;
    line-height: 1.6;
}

/* 特性列表 */
.feature-list {
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin-bottom: 40px;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 20px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.feature-item:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateX(5px);
}

.feature-item i {
    font-size: 1.2rem;
    color: #ffffff;
}

.feature-item span {
    font-weight: 500;
    color: #ffffff;
}

/* 装饰元素 */
.decorative-elements {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.geometric-shapes {
    position: relative;
    width: 100%;
    height: 100%;
}

.shape {
    position: absolute;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(2px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.shape-1 {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    top: 20%;
    left: 20%;
    animation: shape-rotate-1 15s linear infinite;
}

.shape-2 {
    width: 40px;
    height: 40px;
    top: 60%;
    right: 25%;
    transform: rotate(45deg);
    animation: shape-float-2 12s ease-in-out infinite;
}

.shape-3 {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    bottom: 30%;
    left: 15%;
    animation: shape-pulse-3 8s ease-in-out infinite;
}

.shape-4 {
    width: 50px;
    height: 50px;
    top: 10%;
    right: 15%;
    clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
    animation: shape-bounce-4 10s ease-in-out infinite;
}

.shape-5 {
    width: 35px;
    height: 35px;
    bottom: 20%;
    right: 30%;
    border-radius: 8px;
    animation: shape-spin-5 14s linear infinite;
}

/* 形状动画 */
@keyframes shape-rotate-1 {
    0% { transform: rotate(0deg) scale(1); }
    50% { transform: rotate(180deg) scale(1.1); }
    100% { transform: rotate(360deg) scale(1); }
}

@keyframes shape-float-2 {
    0%, 100% { transform: rotate(45deg) translateY(0px); }
    50% { transform: rotate(45deg) translateY(-20px); }
}

@keyframes shape-pulse-3 {
    0%, 100% { transform: scale(1); opacity: 0.7; }
    50% { transform: scale(1.2); opacity: 1; }
}

@keyframes shape-bounce-4 {
    0%, 100% { transform: translateY(0px); }
    25% { transform: translateY(-15px); }
    50% { transform: translateY(-30px); }
    75% { transform: translateY(-15px); }
}

@keyframes shape-spin-5 {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 右侧登录表单区域 */
.login-right {
    flex: 1;
    padding: 60px 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #ffffff;
}

.login-form-container {
    width: 100%;
    max-width: 400px;
}

.form-title {
    font-size: 1.75rem;
    font-weight: 600;
    color: #1a1a1a;
    margin-bottom: 8px;
    text-align: center;
}

.form-subtitle {
    font-size: 0.95rem;
    color: #666;
    margin-bottom: 40px;
    text-align: center;
    line-height: 1.5;
}

/* 表单组 */
.form-group {
    margin-bottom: 24px;
}

.form-label {
    display: block;
    font-size: 0.9rem;
    font-weight: 500;
    color: #374151;
    margin-bottom: 8px;
}

/* 输入框包装器 */
.input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    background: #f8fafc;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    transition: all 0.3s ease;
    overflow: hidden;
}

.input-wrapper:focus-within {
    border-color: #667eea;
    background: #ffffff;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.input-icon {
    padding: 0 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #9ca3af;
    transition: color 0.3s ease;
}

.input-wrapper:focus-within .input-icon {
    color: #667eea;
}

.input-icon img {
    width: 20px;
    height: 20px;
    opacity: 0.7;
    transition: opacity 0.3s ease;
}

.input-wrapper:focus-within .input-icon img {
    opacity: 1;
}

.form-input {
    flex: 1;
    padding: 16px 20px 16px 0;
    border: none;
    background: transparent;
    font-size: 1rem;
    color: #1a1a1a;
    outline: none;
    font-family: inherit;
}

.form-input::placeholder {
    color: #9ca3af;
    transition: color 0.3s ease;
}

.form-input:focus::placeholder {
    color: #d1d5db;
}

/* 复选框区域 */
.form-checkbox {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 32px;
}

.form-checkbox input[type="checkbox"] {
    width: 18px;
    height: 18px;
    accent-color: #667eea;
}

.form-checkbox label {
    font-size: 0.9rem;
    color: #6b7280;
    cursor: pointer;
    user-select: none;
}

/* 登录按钮 */
.login-btn {
    width: 100%;
    padding: 16px 24px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.login-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.login-btn:active {
    transform: translateY(0);
}

.btn-text {
    position: relative;
    z-index: 2;
}

.btn-icon {
    position: relative;
    z-index: 2;
    transition: transform 0.3s ease;
}

/* 找回密码链接样式 */
.forgot-password {
    text-align: center;
    margin-top: 20px;
}

.forgot-link {
    color: #6b7280;
    text-decoration: none;
    font-size: 0.9rem;
    display: inline-flex;
    align-items: center;
    gap: 6px;
    transition: all 0.3s ease;
    padding: 8px 12px;
    border-radius: 8px;
}

.forgot-link:hover {
    color: #667eea;
    background-color: rgba(102, 126, 234, 0.1);
    text-decoration: none;
}

.forgot-link i {
    font-size: 0.8rem;
}

.login-btn:hover .btn-icon {
    transform: translateX(4px);
}

/* 按钮加载状态 */
.login-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

.login-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.login-btn:hover::before {
    left: 100%;
}

/* 底部版权信息 */
.login-footer {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 10;
}

.footer-content {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 0.85rem;
    color: rgba(255, 255, 255, 0.8);
    text-align: center;
}

.separator {
    color: rgba(255, 255, 255, 0.5);
}

.footer-link {
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-link:hover {
    color: #ffffff;
}

/* 入场动画 */
.animate-slide-up {
    opacity: 0;
    transform: translateY(30px);
    animation: slideUp 0.8s ease-out forwards;
}

@keyframes slideUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 左侧内容入场动画 */
.left-content {
    animation: fadeInLeft 1s ease-out;
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .login-main {
        max-width: 900px;
        min-height: 550px;
    }

    .login-left {
        padding: 40px 30px;
    }

    .login-right {
        padding: 40px 30px;
    }

    .welcome-text h1 {
        font-size: 2.2rem;
    }

    .feature-list {
        gap: 16px;
    }
}

@media (max-width: 768px) {
    .login-container {
        padding: 15px;
    }

    .login-main {
        flex-direction: column;
        max-width: 500px;
        min-height: auto;
        border-radius: 20px;
    }

    .login-left {
        padding: 40px 30px 30px;
        min-height: 300px;
    }

    .login-right {
        padding: 30px;
    }

    .welcome-text h1 {
        font-size: 1.8rem;
        margin-bottom: 12px;
    }

    .welcome-text p {
        font-size: 1rem;
        margin-bottom: 30px;
    }

    .feature-list {
        flex-direction: row;
        justify-content: center;
        gap: 12px;
        margin-bottom: 20px;
    }

    .feature-item {
        flex-direction: column;
        padding: 8px 12px;
        gap: 6px;
        text-align: center;
        min-width: 80px;
    }

    .feature-item span {
        font-size: 0.8rem;
    }

    .form-title {
        font-size: 1.5rem;
    }

    .form-subtitle {
        font-size: 0.9rem;
        margin-bottom: 30px;
    }

    .header-logo {
        top: 20px;
        left: 20px;
    }

    .logo-img {
        height: 40px;
    }

    .login-footer {
        position: relative;
        bottom: auto;
        left: auto;
        transform: none;
        margin-top: 20px;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .login-container {
        padding: 10px;
    }

    .login-main {
        margin: 0 10px;
        border-radius: 16px;
    }

    .login-left {
        padding: 30px 20px 20px;
        min-height: 250px;
    }

    .login-right {
        padding: 20px;
    }

    .welcome-text h1 {
        font-size: 1.5rem;
    }

    .welcome-text p {
        font-size: 0.9rem;
    }

    .form-title {
        font-size: 1.25rem;
    }

    .input-wrapper {
        border-radius: 8px;
    }

    .login-btn {
        border-radius: 8px;
        padding: 14px 20px;
    }

    .header-logo {
        top: 15px;
        left: 15px;
    }

    .logo-img {
        height: 35px;
    }
}

/* 兼容旧版浏览器 */
@supports not (backdrop-filter: blur(20px)) {
    .login-main {
        background: rgba(255, 255, 255, 0.98);
    }

    .input-wrapper:focus-within {
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
    }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    .login-main {
        border: 2px solid #000;
    }

    .input-wrapper {
        border-color: #000;
    }

    .login-btn {
        background: #000;
        border: 2px solid #000;
    }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .particle,
    .wave,
    .shape {
        animation: none;
    }
}
