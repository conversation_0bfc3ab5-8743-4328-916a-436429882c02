(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-confirm_order-confirm_order","bundle-pages-invoice_detail-invoice_detail~bundle-pages-order_details-order_details~bundle-pages-use~f8b4506c"],{"0048":function(e,t,i){"use strict";i("7a82"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,i("a9e3");var n={data:function(){return{coupons:[],selectId:""}},props:{list:{type:Array,default:function(){return[]}},type:{type:Number},couponId:[Number,String]},watch:{list:{handler:function(e){this.coupons=e},immediate:!0},couponId:{handler:function(e){this.selectId=e},immediate:!0}},methods:{onSelect:function(e){this.coupons;var t=this.type,i=this.selectId;1!=t&&(this.selectId=e==i?"":e)},onConfirmCoupon:function(){this.$emit("change",this.selectId)}}};t.default=n},"073a":function(e,t,i){"use strict";var n=i("07aa"),o=i.n(n);o.a},"07aa":function(e,t,i){var n=i("b1d4");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[e.i,n,""]]),n.locals&&(e.exports=n.locals);var o=i("4f06").default;o("3abbc265",n,!0,{sourceMap:!1,shadowMode:!1})},"14b0":function(e,t,i){var n=i("fb35");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[e.i,n,""]]),n.locals&&(e.exports=n.locals);var o=i("4f06").default;o("02236275",n,!0,{sourceMap:!1,shadowMode:!1})},1522:function(e,t,i){"use strict";i.d(t,"b",(function(){return o})),i.d(t,"c",(function(){return r})),i.d(t,"a",(function(){return n}));var n={uIcon:i("90f3").default},o=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("v-uni-view",{staticClass:"u-image",style:[e.wrapStyle,e.backgroundStyle],on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.onClick.apply(void 0,arguments)}}},[e.isError?e._e():i("v-uni-image",{staticClass:"u-image__image",style:{borderRadius:"circle"==e.shape?"50%":e.$u.addUnit(e.borderRadius)},attrs:{src:e.src,mode:e.mode,"lazy-load":e.lazyLoad},on:{error:function(t){arguments[0]=t=e.$handleEvent(t),e.onErrorHandler.apply(void 0,arguments)},load:function(t){arguments[0]=t=e.$handleEvent(t),e.onLoadHandler.apply(void 0,arguments)}}}),e.showLoading&&e.loading?i("v-uni-view",{staticClass:"u-image__loading",style:{borderRadius:"circle"==e.shape?"50%":e.$u.addUnit(e.borderRadius),backgroundColor:this.bgColor}},[e.$slots.loading?e._t("loading"):i("u-icon",{attrs:{name:e.loadingIcon,width:e.width,height:e.height}})],2):e._e(),e.showError&&e.isError&&!e.loading?i("v-uni-view",{staticClass:"u-image__error",style:{borderRadius:"circle"==e.shape?"50%":e.$u.addUnit(e.borderRadius)}},[e.$slots.error?e._t("error"):i("u-icon",{attrs:{name:e.errorIcon,width:e.width,height:e.height}})],2):e._e()],1)},r=[]},"180c":function(e,t,i){"use strict";var n=i("601a"),o=i.n(n);o.a},"19bf":function(e,t,i){"use strict";i.r(t);var n=i("0048"),o=i.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){i.d(t,e,(function(){return n[e]}))}(r);t["default"]=o.a},"1b46":function(e,t,i){"use strict";i.r(t);var n=i("c653"),o=i.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){i.d(t,e,(function(){return n[e]}))}(r);t["default"]=o.a},"1cbc":function(e,t,i){"use strict";i("7a82"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,i("a9e3");var n={name:"u-tag",props:{type:{type:String,default:"primary"},disabled:{type:[Boolean,String],default:!1},size:{type:String,default:"default"},shape:{type:String,default:"square"},text:{type:[String,Number],default:""},bgColor:{type:String,default:""},color:{type:String,default:""},borderColor:{type:String,default:""},closeColor:{type:String,default:""},index:{type:[Number,String],default:""},mode:{type:String,default:"light"},closeable:{type:Boolean,default:!1},show:{type:Boolean,default:!0}},data:function(){return{}},computed:{customStyle:function(){var e={};return this.color&&(e.color=this.color),this.bgColor&&(e.backgroundColor=this.bgColor),"plain"==this.mode&&this.color&&!this.borderColor?e.borderColor=this.color:e.borderColor=this.borderColor,e},iconStyle:function(){if(this.closeable){var e={};return"mini"==this.size?e.fontSize="20rpx":e.fontSize="22rpx","plain"==this.mode||"light"==this.mode?e.color=this.type:"dark"==this.mode&&(e.color="#ffffff"),this.closeColor&&(e.color=this.closeColor),e}},closeIconColor:function(){return this.closeColor?this.closeColor:this.color?this.color:"dark"==this.mode?"#ffffff":this.type}},methods:{clickTag:function(){this.disabled||this.$emit("click",this.index)},close:function(){this.$emit("close",this.index)}}};t.default=n},"1de5":function(e,t,i){"use strict";e.exports=function(e,t){return t||(t={}),e=e&&e.__esModule?e.default:e,"string"!==typeof e?e:(/^['"].*['"]$/.test(e)&&(e=e.slice(1,-1)),t.hash&&(e+=t.hash),/["'() \t\n]/.test(e)||t.needQuotes?'"'.concat(e.replace(/"/g,'\\"').replace(/\n/g,"\\n"),'"'):e)}},2322:function(e,t,i){"use strict";i("7a82"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,i("a9e3");var n={name:"u-image",props:{src:{type:String,default:""},mode:{type:String,default:"aspectFill"},width:{type:[String,Number],default:"100%"},height:{type:[String,Number],default:"auto"},shape:{type:String,default:"square"},borderRadius:{type:[String,Number],default:0},lazyLoad:{type:Boolean,default:!0},showMenuByLongpress:{type:Boolean,default:!0},loadingIcon:{type:String,default:"photo"},errorIcon:{type:String,default:"error-circle"},showLoading:{type:Boolean,default:!0},showError:{type:Boolean,default:!0},fade:{type:Boolean,default:!0},webp:{type:Boolean,default:!1},duration:{type:[String,Number],default:500},bgColor:{type:String,default:"#f3f4f6"}},data:function(){return{isError:!1,loading:!0,opacity:1,durationTime:this.duration,backgroundStyle:{}}},watch:{src:{immediate:!0,handler:function(e){e?this.isError=!1:(this.isError=!0,this.loading=!1)}}},computed:{wrapStyle:function(){var e={};return e.width=this.$u.addUnit(this.width),e.height=this.$u.addUnit(this.height),e.borderRadius="circle"==this.shape?"50%":this.$u.addUnit(this.borderRadius),e.overflow=this.borderRadius>0?"hidden":"visible",this.fade&&(e.opacity=this.opacity,e.transition="opacity ".concat(Number(this.durationTime)/1e3,"s ease-in-out")),e}},methods:{onClick:function(){this.$emit("click")},onErrorHandler:function(e){this.loading=!1,this.isError=!0,this.$emit("error",e)},onLoadHandler:function(){var e=this;if(this.loading=!1,this.isError=!1,this.$emit("load"),!this.fade)return this.removeBgColor();this.opacity=0,this.durationTime=0,setTimeout((function(){e.durationTime=e.duration,e.opacity=1,setTimeout((function(){e.removeBgColor()}),e.durationTime)}),50)},removeBgColor:function(){this.backgroundStyle={backgroundColor:"transparent"}}}};t.default=n},2698:function(e,t,i){"use strict";var n=i("5bed"),o=i.n(n);o.a},2875:function(e,t,i){"use strict";i.r(t);var n=i("a712"),o=i("c13e");for(var r in o)["default"].indexOf(r)<0&&function(e){i.d(t,e,(function(){return o[e]}))}(r);i("8fed");var a=i("f0c5"),s=Object(a["a"])(o["default"],n["b"],n["c"],!1,null,"061dd044",null,!1,n["a"],void 0);t["default"]=s.exports},"29d6":function(e,t,i){var n=i("aba1");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[e.i,n,""]]),n.locals&&(e.exports=n.locals);var o=i("4f06").default;o("2466de44",n,!0,{sourceMap:!1,shadowMode:!1})},"2aa2":function(e,t,i){"use strict";i.r(t);var n=i("fd55"),o=i("a876");for(var r in o)["default"].indexOf(r)<0&&function(e){i.d(t,e,(function(){return o[e]}))}(r);i("180c");var a=i("f0c5"),s=Object(a["a"])(o["default"],n["b"],n["c"],!1,null,"3b597fe7",null,!1,n["a"],void 0);t["default"]=s.exports},"2e12":function(e,t,i){"use strict";i.d(t,"b",(function(){return o})),i.d(t,"c",(function(){return r})),i.d(t,"a",(function(){return n}));var n={uIcon:i("90f3").default,orderShop:i("2aa2").default,priceFormat:i("fefe").default,loadingView:i("2875").default},o=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("v-uni-view",[i("v-uni-view",{staticClass:"confirm-order"},[i("v-uni-view",{staticClass:"confirm-con"},[i("v-uni-navigator",{attrs:{"hover-class":"none",url:"/bundle/pages/user_address/user_address?type=1"}},[void 0!=e.teamId||0==e.shopLists.length||e.deliveryType||0!=e.shopLists[0].goods[0].type?e._e():i("v-uni-view",{staticClass:"address flex bg-white"},[i("v-uni-image",{staticClass:"icon-md m-r-20",attrs:{src:"/static/images/icon_address.png"}}),i("v-uni-view",{staticClass:"flex-1 m-r-20"},[e.address.contact?i("v-uni-view",[i("v-uni-text",{staticClass:"name md m-r-10"},[e._v(e._s(e.address.contact))]),i("v-uni-text",{staticClass:"phone md"},[e._v(e._s(e.address.telephone))]),i("v-uni-view",{staticClass:"area sm m-t-10 lighter"},[e._v(e._s(e.address.province)+" "+e._s(e.address.city)+"\n                "+e._s(e.address.district)+" "+e._s(e.address.address))])],1):i("v-uni-view",{staticClass:"black md"},[e._v("设置收货地址")])],1),i("u-icon",{attrs:{name:"arrow-right"}})],1)],1),i("v-uni-navigator",{attrs:{"hover-class":"none",url:"/bundle/pages/user_address/user_address?type=1"}},[void 0!==e.teamId&&0!=e.shopLists.length&&1==e.shopLists.delivery_type?i("v-uni-view",{staticClass:"address flex bg-white"},[i("v-uni-image",{staticClass:"icon-md m-r-20",attrs:{src:"/static/images/icon_address.png"}}),i("v-uni-view",{staticClass:"flex-1 m-r-20"},[e.address.contact?i("v-uni-view",[i("v-uni-text",{staticClass:"name md m-r-10"},[e._v(e._s(e.address.contact))]),i("v-uni-text",{staticClass:"phone md"},[e._v(e._s(e.address.telephone))]),i("v-uni-view",{staticClass:"area sm m-t-10 lighter"},[e._v(e._s(e.address.province)+" "+e._s(e.address.city)+"\n                "+e._s(e.address.district)+" "+e._s(e.address.address))])],1):i("v-uni-view",{staticClass:"black md"},[e._v("设置收货地址")])],1),i("u-icon",{attrs:{name:"arrow-right"}})],1):e._e()],1),void 0==e.teamId?e._l(e.shopLists,(function(t,n){return i("v-uni-view",{key:n,staticClass:"goods contain"},[i("order-shop",{attrs:{"order-type":e.orderInfo.order_type,item:t,invoice:e.invoiceArr,teamId:e.teamId,bargainLaunchId:e.bargainLaunchId},on:{changeremark:function(t){arguments[0]=t=e.$handleEvent(t),e.changeRemark.apply(void 0,arguments)},changecoupon:function(t){arguments[0]=t=e.$handleEvent(t),e.changeCoupon(t,n)},changeDeliveryType:function(i){arguments[0]=i=e.$handleEvent(i),e.changeDeliveryType(i,t)}}})],1)})):e._e(),void 0!==e.teamId&&e.teamLists.delivery_types_arr.length?[i("v-uni-view",{staticClass:"goods contain"},[i("order-shop",{attrs:{"order-type":e.orderInfo.order_type,item:e.shopLists,invoice:e.invoiceArr,teamId:e.teamId,bargainLaunchId:e.bargainLaunchId},on:{changeremark:function(t){arguments[0]=t=e.$handleEvent(t),e.changeRemark.apply(void 0,arguments)},changeDeliveryType:function(t){arguments[0]=t=e.$handleEvent(t),e.changeDeliveryType(t,e.shopLists)}}})],1)]:e._e()],2),i("v-uni-view",{staticClass:"footer bg-white flex row-between fixed"},[i("v-uni-view",{staticClass:"all-price lg flex"},[i("v-uni-text",[e._v("合计：")]),i("v-uni-view",{staticClass:"primary"},[i("price-format",{attrs:{weight:"500","first-size":36,"second-size":36,price:e.orderInfo.total_amount}})],1)],1),i("v-uni-button",{staticClass:"btn br60 white",attrs:{size:"md","hover-class":"none"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.onSubmitOrder.apply(void 0,arguments)}}},[e._v("提交订单")])],1)],1),e.showLoading?i("loading-view",{attrs:{"background-color":"transparent",size:50}}):e._e(),e.isFirstLoading?i("loading-view"):e._e()],1)},r=[]},3345:function(e,t,i){"use strict";i.r(t);var n=i("f79d"),o=i.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){i.d(t,e,(function(){return n[e]}))}(r);t["default"]=o.a},3427:function(e,t,i){"use strict";i.r(t);var n=i("83ed"),o=i.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){i.d(t,e,(function(){return n[e]}))}(r);t["default"]=o.a},"45ee":function(e,t,i){var n=i("24fb");t=n(!1),t.push([e.i,".price-format[data-v-0a5a34e0]{font-family:Avenir,SourceHanSansCN,PingFang SC,Arial,Hiragino Sans GB,Microsoft YaHei,sans-serif}",""]),e.exports=t},"461a":function(e,t,i){"use strict";i("7a82");var n=i("ee27").default;Object.defineProperty(t,"__esModule",{value:!0}),t.alipay=function(e,t){if("66"==t)location.href=e;else{var i=document.createElement("div");console.log(e),i.innerHTML=e,document.body.appendChild(i),document.forms[0].submit()}},t.wxpay=function(e){if((0,r.isWeixinClient)())return o.default.wxPay(e);console.log(e),location.href=e};var o=n(i("06f8")),r=i("b08d")},"46d6":function(e,t,i){var n=i("24fb");t=n(!1),t.push([e.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.shop-title[data-v-705e9a38]{height:%?80?%;flex:1;min-width:0}.shop-title .tag[data-v-705e9a38]{background:linear-gradient(267deg,#ff2c3c,#f52e99);border-radius:%?6?%;padding:%?5?% %?9?%}',""]),e.exports=t},4704:function(e,t,i){"use strict";var n=i("921a"),o=i.n(n);o.a},4837:function(e,t,i){"use strict";i.d(t,"b",(function(){return o})),i.d(t,"c",(function(){return r})),i.d(t,"a",(function(){return n}));var n={uIcon:i("90f3").default},o=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("v-uni-view",{staticClass:"u-checkbox",style:[e.checkboxStyle]},[i("v-uni-view",{staticClass:"u-checkbox__icon-wrap",class:[e.iconClass],style:[e.iconStyle],on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.toggle.apply(void 0,arguments)}}},[i("u-icon",{staticClass:"u-checkbox__icon-wrap__icon",attrs:{name:"checkbox-mark",size:e.checkboxIconSize,color:e.iconColor}})],1),i("v-uni-view",{staticClass:"u-checkbox__label",style:{fontSize:e.$u.addUnit(e.labelSize)},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.onClickLabel.apply(void 0,arguments)}}},[e._t("default")],2)],1)},r=[]},"4d9f":function(e,t,i){"use strict";i("7a82");var n=i("ee27").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=n(i("f07e")),r=n(i("c964"));i("a9e3"),i("99af"),i("d81d"),i("c740"),i("14d9"),i("e9c4"),i("4de4"),i("d3b7");var a=i("c60f"),s=i("98a1"),c={name:"order-shop",props:{item:{type:[Object,Array]},teamId:{type:[Number,void 0]},bargainLaunchId:{type:[Number,void 0]},orderType:{type:Number},invoice:{type:[Object,Array]}},data:function(){return{active:0,remark:"",showCoupon:!1,showDelivery:!1,delivery_type:this.item.delivery_type,delivery_type_index:0,usableCoupon:[],unusableCoupon:[],usableCouponL:0,unusableCouponL:0,isRequest:!1,couponId:"",invoiceInfo:{}}},computed:{getCurrentShopInvoice:function(){var e=this.invoiceInfo;return e.name?"".concat(0==e.type?"普通发票":"专用发票"," - ").concat(0==e.header_type?"个人":"企业"," - ").concat(e.name):"不开发票"}},methods:{openLocation:function(e){uni.openLocation({latitude:Number(e.latitude),longitude:Number(e.longitude),name:e.name,address:e.address_detail})},onShowCoupon:function(){this.isRequest?this.showCoupon=!0:(uni.showLoading({title:"加载中..."}),this.getCoupon())},getCoupon:function(){var e=this;return(0,r.default)((0,o.default)().mark((function t(){var i,n,r,s,c,u,d;return(0,o.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return i=e.item,n=i.goods,r=i.shop_id,s=n.map((function(e){var t=e.item_id,i=e.num,n=e.goods_id;return{item_id:t,num:i,goods_id:n}})),t.next=4,(0,a.getOrderCoupon)({goods:s,shop_id:r});case 4:c=t.sent,u=c.data,d=c.code,1==d&&(e.isRequest=!0,e.usableCoupon=u.suit,e.usableCouponL=u.suit.length,e.unusableCoupon=u.un_suit,e.unusableCouponL=u.un_suit.length,e.showCoupon=!0,uni.hideLoading());case 8:case"end":return t.stop()}}),t)})))()},chengeDelivery:function(){this.showDelivery=!0},changeDeliveryType:function(){var e=this;this.delivery_type_index=this.item.delivery_types_arr.findIndex((function(t){return t.delivery_type===e.delivery_type})),this.$emit("changeDeliveryType",this.delivery_type)},handleInvoice:function(e){this.$Router.push({path:"/bundle/pages/invoice/invoice",query:{shop_id:e,invoice:JSON.stringify(this.invoiceInfo),type:s.invoiceType["SETTLEMENT"]}})},onSelectCoupon:function(e){this.couponId=e,this.$emit("changecoupon",e),this.showCoupon=!1}},watch:{remark:function(e){this.$emit("changeremark",{shop_id:this.item.shop_id,remark:e})},invoice:function(e){var t=this,i=e.filter((function(e){return e.shop_id==t.item.shop_id}));i.length?this.invoiceInfo=i[0]:this.invoiceInfo={}}}};t.default=c},5783:function(e,t,i){"use strict";i.r(t);var n=i("7c97"),o=i("19bf");for(var r in o)["default"].indexOf(r)<0&&function(e){i.d(t,e,(function(){return o[e]}))}(r);i("4704");var a=i("f0c5"),s=Object(a["a"])(o["default"],n["b"],n["c"],!1,null,"33ccce82",null,!1,n["a"],void 0);t["default"]=s.exports},"5bed":function(e,t,i){var n=i("46d6");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[e.i,n,""]]),n.locals&&(e.exports=n.locals);var o=i("4f06").default;o("50d1694e",n,!0,{sourceMap:!1,shadowMode:!1})},6011:function(e,t,i){"use strict";i("7a82"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,i("14d9");var n={data:function(){return{}},components:{},props:{list:{type:Array,default:function(){return[]}},link:{type:Boolean,default:!1},isJumpGoods:{type:Boolean,default:!1}},created:function(){var e=this;setTimeout((function(){console.log(e.list)}),700)},methods:{jumpGoods:function(e){this.isJumpGoods&&this.$Router.push({path:"/pages/goods_details/goods_details?id=",query:{id:e.goods_id}})}}};t.default=n},6017:function(e,t,i){"use strict";var n=i("66cc"),o=i.n(n);o.a},"601a":function(e,t,i){var n=i("9367");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[e.i,n,""]]),n.locals&&(e.exports=n.locals);var o=i("4f06").default;o("4db31b98",n,!0,{sourceMap:!1,shadowMode:!1})},6021:function(e,t,i){"use strict";var n=i("64b1"),o=i.n(n);o.a},"64b1":function(e,t,i){var n=i("6e35");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[e.i,n,""]]),n.locals&&(e.exports=n.locals);var o=i("4f06").default;o("3d1e497c",n,!0,{sourceMap:!1,shadowMode:!1})},"66cc":function(e,t,i){var n=i("ae16");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[e.i,n,""]]),n.locals&&(e.exports=n.locals);var o=i("4f06").default;o("3ee5a69c",n,!0,{sourceMap:!1,shadowMode:!1})},"6d79":function(e,t,i){"use strict";i("7a82"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,i("a9e3"),i("acd8");var n={data:function(){return{priceSlice:{}}},components:{},props:{firstSize:{type:[String,Number],default:28},secondSize:{type:[String,Number],default:28},color:{type:String},weight:{type:[String,Number],default:400},price:{type:[String,Number],default:""},showSubscript:{type:Boolean,default:!0},subscriptSize:{type:[String,Number],default:28},lineThrough:{type:Boolean,default:!1}},created:function(){this.priceFormat()},watch:{price:function(e){this.priceFormat()}},methods:{priceFormat:function(){var e=this.price,t={};null!==e&&""!==e&&void 0!==e?(e=parseFloat(e),e=String(e).split("."),t.first=e[0],t.second=e[1],this.priceSlice=t):this.priceSlice={first:0}}}};t.default=n},"6e35":function(e,t,i){var n=i("24fb");t=n(!1),t.push([e.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.u-image[data-v-1bf07c9a]{position:relative;transition:opacity .5s ease-in-out}.u-image__image[data-v-1bf07c9a]{width:100%;height:100%}.u-image__loading[data-v-1bf07c9a], .u-image__error[data-v-1bf07c9a]{position:absolute;top:0;left:0;width:100%;height:100%;display:flex;flex-direction:row;align-items:center;justify-content:center;background-color:#f3f4f6;color:#909399;font-size:%?46?%}',""]),e.exports=t},"717e":function(e,t,i){var n=i("24fb"),o=i("1de5"),r=i("c9e1");t=n(!1);var a=o(r);t.push([e.i,".coupon-order .coupon-list[data-v-33ccce82]{padding:%?20?% %?24?%}.coupon-order .coupon-item[data-v-33ccce82]{position:relative;height:%?200?%;background-image:url("+a+");background-size:100% 100%}.coupon-order .coupon-item .price[data-v-33ccce82]{width:%?200?%}.coupon-order .btn[data-v-33ccce82]{margin:%?10?% %?20?%;height:%?82?%}",""]),e.exports=t},"76a8":function(e,t,i){"use strict";var n=i("29d6"),o=i.n(n);o.a},7844:function(e,t,i){"use strict";i.d(t,"b",(function(){return o})),i.d(t,"c",(function(){return r})),i.d(t,"a",(function(){return n}));var n={uIcon:i("90f3").default},o=function(){var e=this,t=e.$createElement,i=e._self._c||t;return e.show?i("v-uni-view",{staticClass:"u-tag",class:[e.disabled?"u-disabled":"","u-size-"+e.size,"u-shape-"+e.shape,"u-mode-"+e.mode+"-"+e.type],style:[e.customStyle],on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.clickTag.apply(void 0,arguments)}}},[e._v(e._s(e.text)),i("v-uni-view",{staticClass:"u-icon-wrap",on:{click:function(t){t.stopPropagation(),arguments[0]=t=e.$handleEvent(t)}}},[e.closeable?i("u-icon",{staticClass:"u-close-icon",style:[e.iconStyle],attrs:{size:"22",color:e.closeIconColor,name:"close"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.close.apply(void 0,arguments)}}}):e._e()],1)],1):e._e()},r=[]},7998:function(e,t,i){var n=i("ab4e");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[e.i,n,""]]),n.locals&&(e.exports=n.locals);var o=i("4f06").default;o("7f1256af",n,!0,{sourceMap:!1,shadowMode:!1})},"7c97":function(e,t,i){"use strict";i.d(t,"b",(function(){return o})),i.d(t,"c",(function(){return r})),i.d(t,"a",(function(){return n}));var n={priceFormat:i("fefe").default,uCheckbox:i("dc83").default},o=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("v-uni-view",{staticClass:"coupon-order flex-col"},[i("v-uni-scroll-view",{staticStyle:{"background-color":"#F6F6F6"},style:{height:0==e.type?"750rpx":"852rpx"},attrs:{"scroll-y":"true"}},[i("v-uni-view",{staticClass:"coupon-list"},e._l(e.coupons,(function(t,n){return i("v-uni-view",{key:n,staticClass:"m-b-20 bg-white",on:{click:function(i){arguments[0]=i=e.$handleEvent(i),e.onSelect(t.id)}}},[i("v-uni-view",{staticClass:"coupon-item flex"},[i("v-uni-view",{staticClass:"price white flex-col col-center"},[i("v-uni-view",{staticClass:"xl"},[i("price-format",{attrs:{"subscript-size":34,"first-size":60,"second-size":50,price:t.money,weight:500}})],1),i("v-uni-view",[e._v(e._s(t.condition_type_desc))])],1),i("v-uni-view",{staticClass:"flex row-between flex-1"},[i("v-uni-view",{staticClass:"info m-l-20"},[i("v-uni-view",{staticClass:"bold md bold m-b-10 line-1"},[e._v(e._s(t.coupon_name))]),i("v-uni-view",{staticClass:"xxs lighter m-b-10"},[e._v(e._s(t.user_time_desc))]),i("v-uni-view",{staticClass:"xxs lighter"},[e._v(e._s(t.use_scene_desc))])],1),0==e.type?i("u-checkbox",{attrs:{shape:"circle",value:e.selectId==t.id}}):e._e()],1)],1),t.use_goods_desc&&1==e.type?i("v-uni-view",{staticClass:"xs",staticStyle:{padding:"14rpx 20rpx"}},[e._v(e._s(t.use_goods_desc))]):e._e()],1)})),1),0==e.coupons.length?i("v-uni-view",{staticClass:"flex-col col-center",staticStyle:{"padding-top":"50rpx"}},[i("v-uni-image",{staticClass:"img-null",attrs:{src:"/static/images/coupon_null.png"}}),i("v-uni-text",{staticClass:"muted"},[e._v("暂无优惠券～")])],1):e._e()],1),0==e.type?i("v-uni-view",[i("v-uni-view",{staticClass:"bg-primary btn white flex row-center br60 mb10 lg",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.onConfirmCoupon.apply(void 0,arguments)}}},[e._v("确定")])],1):e._e()],1)},r=[]},8219:function(e,t,i){"use strict";i.r(t);var n=i("7844"),o=i("abdf");for(var r in o)["default"].indexOf(r)<0&&function(e){i.d(t,e,(function(){return o[e]}))}(r);i("073a");var a=i("f0c5"),s=Object(a["a"])(o["default"],n["b"],n["c"],!1,null,"1cd62f78",null,!1,n["a"],void 0);t["default"]=s.exports},"822c":function(e,t,i){"use strict";i("7a82"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n={data:function(){return{}},props:{type:{type:String,default:"fixed"},backgroundColor:{type:String,default:"#fff"}},methods:{}};t.default=n},"83ed":function(e,t,i){"use strict";i("7a82");var n=i("ee27").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,i("a9e3"),i("498a");var o=n(i("e4e0")),r={name:"u-input",mixins:[o.default],props:{value:{type:[String,Number],default:""},type:{type:String,default:"text"},inputAlign:{type:String,default:"left"},placeholder:{type:String,default:"请输入内容"},disabled:{type:Boolean,default:!1},maxlength:{type:[Number,String],default:140},placeholderStyle:{type:String,default:"color: #c0c4cc;"},confirmType:{type:String,default:"done"},customStyle:{type:Object,default:function(){return{}}},fixed:{type:Boolean,default:!1},focus:{type:Boolean,default:!1},passwordIcon:{type:Boolean,default:!0},border:{type:Boolean,default:!1},borderColor:{type:String,default:"#dcdfe6"},autoHeight:{type:Boolean,default:!0},selectOpen:{type:Boolean,default:!1},height:{type:[Number,String],default:""},clearable:{type:Boolean,default:!0},cursorSpacing:{type:[Number,String],default:0},selectionStart:{type:[Number,String],default:-1},selectionEnd:{type:[Number,String],default:-1},trim:{type:Boolean,default:!0},showConfirmbar:{type:Boolean,default:!0}},data:function(){return{defaultValue:this.value,inputHeight:70,textareaHeight:100,validateState:!1,focused:!1,showPassword:!1,lastValue:""}},watch:{value:function(e,t){this.defaultValue=e,e!=t&&"select"==this.type&&this.handleInput({detail:{value:e}})}},computed:{inputMaxlength:function(){return Number(this.maxlength)},getStyle:function(){var e={};return e.minHeight=this.height?this.height+"rpx":"textarea"==this.type?this.textareaHeight+"rpx":this.inputHeight+"rpx",e=Object.assign(e,this.customStyle),e},getCursorSpacing:function(){return Number(this.cursorSpacing)},uSelectionStart:function(){return String(this.selectionStart)},uSelectionEnd:function(){return String(this.selectionEnd)}},created:function(){this.$on("on-form-item-error",this.onFormItemError)},methods:{handleInput:function(e){var t=this,i=e.detail.value;this.trim&&(i=this.$u.trim(i)),this.$emit("input",i),this.defaultValue=i,setTimeout((function(){t.dispatch("u-form-item","on-form-change",i)}),40)},handleBlur:function(e){var t=this;setTimeout((function(){t.focused=!1}),100),this.$emit("blur",e.detail.value),setTimeout((function(){t.dispatch("u-form-item","on-form-blur",e.detail.value)}),40)},onFormItemError:function(e){this.validateState=e},onFocus:function(e){this.focused=!0,this.$emit("focus")},onConfirm:function(e){this.$emit("confirm",e.detail.value)},onClear:function(e){this.$emit("input","")},inputClick:function(){this.$emit("click")}}};t.default=r},8713:function(e,t,i){"use strict";var n=i("14b0"),o=i.n(n);o.a},"8aa9":function(e,t,i){"use strict";i.r(t);var n=i("fb1d"),o=i.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){i.d(t,e,(function(){return n[e]}))}(r);t["default"]=o.a},"8fed":function(e,t,i){"use strict";var n=i("e2db"),o=i.n(n);o.a},"921a":function(e,t,i){var n=i("717e");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[e.i,n,""]]),n.locals&&(e.exports=n.locals);var o=i("4f06").default;o("bee9835a",n,!0,{sourceMap:!1,shadowMode:!1})},9367:function(e,t,i){var n=i("24fb");t=n(!1),t.push([e.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.order-shop .order-goods[data-v-3b597fe7]{border-top:1px solid #e5e5e5}.order-shop .item[data-v-3b597fe7]{height:%?88?%;padding:0 %?24?%}.order-shop .item .invoice[data-v-3b597fe7]{display:flex}.order-shop .item .invoice--text[data-v-3b597fe7]{width:%?300?%;text-align:right}.delivery-item[data-v-3b597fe7]{padding:%?24?% %?14?%;margin:0 %?40?% %?30?%;border-radius:%?10?%}.pickCard[data-v-3b597fe7]{padding:%?24?%;height:%?100?%;background-color:#ffefe9;margin:0 %?24?%;border-radius:%?20?%}.pickCard .text[data-v-3b597fe7]{font-size:%?24?%}',""]),e.exports=t},"95a6":function(e,t,i){"use strict";i.r(t);var n=i("ac23"),o=i("8aa9");for(var r in o)["default"].indexOf(r)<0&&function(e){i.d(t,e,(function(){return o[e]}))}(r);i("2698");var a=i("f0c5"),s=Object(a["a"])(o["default"],n["b"],n["c"],!1,null,"705e9a38",null,!1,n["a"],void 0);t["default"]=s.exports},"9a2b":function(e,t,i){"use strict";i.r(t);var n=i("6011"),o=i.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){i.d(t,e,(function(){return n[e]}))}(r);t["default"]=o.a},"9df3":function(e,t,i){"use strict";i.r(t);var n=i("2e12"),o=i("1b46");for(var r in o)["default"].indexOf(r)<0&&function(e){i.d(t,e,(function(){return o[e]}))}(r);i("8713");var a=i("f0c5"),s=Object(a["a"])(o["default"],n["b"],n["c"],!1,null,"67d49443",null,!1,n["a"],void 0);t["default"]=s.exports},a712:function(e,t,i){"use strict";i.d(t,"b",(function(){return o})),i.d(t,"c",(function(){return r})),i.d(t,"a",(function(){return n}));var n={uLoading:i("8fc0").default},o=function(){var e=this.$createElement,t=this._self._c||e;return t("v-uni-view",{class:"loading "+("flex"==this.type?"flex":""),style:{backgroundColor:this.backgroundColor}},[t("u-loading",{attrs:{mode:"flower",size:60}})],1)},r=[]},a876:function(e,t,i){"use strict";i.r(t);var n=i("4d9f"),o=i.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){i.d(t,e,(function(){return n[e]}))}(r);t["default"]=o.a},ab4e:function(e,t,i){var n=i("24fb");t=n(!1),t.push([e.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.u-checkbox[data-v-0b68f884]{display:inline-flex;align-items:center;overflow:hidden;-webkit-user-select:none;user-select:none;line-height:1.8}.u-checkbox__icon-wrap[data-v-0b68f884]{color:#606266;flex:none;display:-webkit-flex;display:flex;flex-direction:row;align-items:center;justify-content:center;box-sizing:border-box;width:%?42?%;height:%?42?%;color:transparent;text-align:center;transition-property:color,border-color,background-color;font-size:20px;border:1px solid #c8c9cc;transition-duration:.2s}.u-checkbox__icon-wrap--circle[data-v-0b68f884]{border-radius:100%}.u-checkbox__icon-wrap--square[data-v-0b68f884]{border-radius:%?6?%}.u-checkbox__icon-wrap--checked[data-v-0b68f884]{color:#fff;background-color:#ff2c3c;border-color:#ff2c3c}.u-checkbox__icon-wrap--disabled[data-v-0b68f884]{background-color:#ebedf0;border-color:#c8c9cc}.u-checkbox__icon-wrap--disabled--checked[data-v-0b68f884]{color:#c8c9cc!important}.u-checkbox__label[data-v-0b68f884]{word-wrap:break-word;margin-left:%?10?%;margin-right:%?24?%;color:#606266;font-size:%?30?%}.u-checkbox__label--disabled[data-v-0b68f884]{color:#c8c9cc}',""]),e.exports=t},aba1:function(e,t,i){var n=i("24fb");t=n(!1),t.push([e.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.u-input[data-v-2f408484]{position:relative;flex:1;display:flex;flex-direction:row}.u-input__input[data-v-2f408484]{font-size:%?28?%;color:#303133;flex:1}.u-input__textarea[data-v-2f408484]{width:auto;font-size:%?28?%;color:#303133;padding:%?10?% 0;line-height:normal;flex:1}.u-input--border[data-v-2f408484]{border-radius:%?6?%;border-radius:4px;border:1px solid #dcdfe6}.u-input--error[data-v-2f408484]{border-color:#fa3534!important}.u-input__right-icon__item[data-v-2f408484]{margin-left:%?10?%}.u-input__right-icon--select[data-v-2f408484]{transition:-webkit-transform .4s;transition:transform .4s;transition:transform .4s,-webkit-transform .4s}.u-input__right-icon--select--reverse[data-v-2f408484]{-webkit-transform:rotate(-180deg);transform:rotate(-180deg)}',""]),e.exports=t},abdf:function(e,t,i){"use strict";i.r(t);var n=i("1cbc"),o=i.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){i.d(t,e,(function(){return n[e]}))}(r);t["default"]=o.a},ac23:function(e,t,i){"use strict";i.d(t,"b",(function(){return o})),i.d(t,"c",(function(){return r})),i.d(t,"a",(function(){return n}));var n={uIcon:i("90f3").default},o=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("v-uni-view",{staticClass:"shop-title flex",on:{click:function(t){t.stopPropagation(),arguments[0]=t=e.$handleEvent(t),e.toShop.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"shop-name line-1 bold"},[e._v(e._s(e.shop.shop_name||e.shop.name||e.name))]),e.isLink?i("u-icon",{staticClass:"m-l-10 m-r-20",attrs:{name:"arrow-right",size:"28"}}):e._e()],1)},r=[]},ae16:function(e,t,i){var n=i("24fb");t=n(!1),t.push([e.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.order-goods .item[data-v-594a3e18]{padding:%?20?% %?24?%}.order-goods .item .vip-price[data-v-594a3e18]{margin:0 %?10?%;background-color:#ffe9ba;line-height:%?30?%;border-radius:%?6?%;overflow:hidden}.order-goods .item .vip-price .price-name[data-v-594a3e18]{background-color:#101010;padding:%?3?% %?10?%;color:#ffd4b7;position:relative;overflow:hidden}.order-goods .item .vip-price .price-name[data-v-594a3e18]::after{content:"";display:block;width:%?20?%;height:%?20?%;position:absolute;right:%?-15?%;background-color:#ffe9ba;border-radius:50%;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%);box-sizing:border-box}.order-goods .goods-footer[data-v-594a3e18]{height:%?70?%;align-items:flex-start;padding:0 %?24?%}.order-goods .goods-footer .plain[data-v-594a3e18]{border:1px solid #999;height:%?52?%;line-height:%?52?%;font-size:%?26?%}',""]),e.exports=t},af8d:function(e,t,i){"use strict";i.r(t);var n=i("2322"),o=i.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){i.d(t,e,(function(){return n[e]}))}(r);t["default"]=o.a},b0dc:function(e,t,i){"use strict";i("7a82");var n=i("ee27").default;Object.defineProperty(t,"__esModule",{value:!0}),t.cancelOrder=function(e){return o.default.post("order/cancel",{id:e})},t.confirmOrder=function(e){return o.default.post("order/confirm",{id:e})},t.delOrder=function(e){return o.default.post("order/del",{id:e})},t.getOrderDetail=function(e){return o.default.get("order/getOrderDetail",{params:{id:e}})},t.getOrderList=function(e){return o.default.get("order/lists",{params:e})},t.getPayResult=function(e){return o.default.get("order/pay_result",{params:e})},t.getwechatSyncCheck=function(e){return o.default.get("order/wechatSyncCheck",{params:e})},t.getwxReceiveDetail=function(e){return o.default.get("order/wxReceiveDetail",{params:e})},t.orderBuy=function(e){return o.default.post("order/submitOrder",e)},t.orderInfo=function(e){return o.default.post("order/settlement",e)},t.orderTraces=function(e){return o.default.get("order/orderTraces",{params:{id:e}})};var o=n(i("3b33"));i("b08d")},b1d4:function(e,t,i){var n=i("24fb");t=n(!1),t.push([e.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.u-tag[data-v-1cd62f78]{box-sizing:border-box;align-items:center;border-radius:%?6?%;display:inline-block}.u-size-default[data-v-1cd62f78]{font-size:%?22?%;padding:%?6?% %?12?%}.u-size-mini[data-v-1cd62f78]{font-size:%?20?%;padding:%?1?% %?6?%}.u-mode-light-primary[data-v-1cd62f78]{background-color:#ecf5ff;color:#ff2c3c;border:1px solid #a0cfff}.u-mode-light-success[data-v-1cd62f78]{background-color:#dbf1e1;color:#19be6b;border:1px solid #71d5a1}.u-mode-light-error[data-v-1cd62f78]{background-color:#fef0f0;color:#fa3534;border:1px solid #fab6b6}.u-mode-light-warning[data-v-1cd62f78]{background-color:#fdf6ec;color:#f90;border:1px solid #fcbd71}.u-mode-light-info[data-v-1cd62f78]{background-color:#f4f4f5;color:#909399;border:1px solid #c8c9cc}.u-mode-dark-primary[data-v-1cd62f78]{background-color:#ff2c3c;color:#fff}.u-mode-dark-success[data-v-1cd62f78]{background-color:#19be6b;color:#fff}.u-mode-dark-error[data-v-1cd62f78]{background-color:#fa3534;color:#fff}.u-mode-dark-warning[data-v-1cd62f78]{background-color:#f90;color:#fff}.u-mode-dark-info[data-v-1cd62f78]{background-color:#909399;color:#fff}.u-mode-plain-primary[data-v-1cd62f78]{background-color:#fff;color:#ff2c3c;border:1px solid #ff2c3c}.u-mode-plain-success[data-v-1cd62f78]{background-color:#fff;color:#19be6b;border:1px solid #19be6b}.u-mode-plain-error[data-v-1cd62f78]{background-color:#fff;color:#fa3534;border:1px solid #fa3534}.u-mode-plain-warning[data-v-1cd62f78]{background-color:#fff;color:#f90;border:1px solid #f90}.u-mode-plain-info[data-v-1cd62f78]{background-color:#fff;color:#909399;border:1px solid #909399}.u-disabled[data-v-1cd62f78]{opacity:.55}.u-shape-circle[data-v-1cd62f78]{border-radius:%?100?%}.u-shape-circleRight[data-v-1cd62f78]{border-radius:0 %?100?% %?100?% 0}.u-shape-circleLeft[data-v-1cd62f78]{border-radius:%?100?% 0 0 %?100?%}.u-close-icon[data-v-1cd62f78]{margin-left:%?14?%;font-size:%?22?%;color:#19be6b}.u-icon-wrap[data-v-1cd62f78]{display:inline-flex;-webkit-transform:scale(.86);transform:scale(.86)}',""]),e.exports=t},b503:function(e,t,i){"use strict";i.r(t);var n=i("ddd2"),o=i("3427");for(var r in o)["default"].indexOf(r)<0&&function(e){i.d(t,e,(function(){return o[e]}))}(r);i("76a8");var a=i("f0c5"),s=Object(a["a"])(o["default"],n["b"],n["c"],!1,null,"2f408484",null,!1,n["a"],void 0);t["default"]=s.exports},b83e:function(e,t,i){var n=i("24fb");t=n(!1),t.push([e.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.loading[data-v-061dd044]{position:fixed;top:0;left:0;width:100vw;height:100vh;z-index:9999;display:flex;justify-content:center;align-items:center}.loading.flex[data-v-061dd044]{position:static;flex:1;width:100%}.loading .loading-img[data-v-061dd044]{width:%?100?%;height:%?100?%}',""]),e.exports=t},ba4b:function(e,t,i){"use strict";i.r(t);var n=i("1522"),o=i("af8d");for(var r in o)["default"].indexOf(r)<0&&function(e){i.d(t,e,(function(){return o[e]}))}(r);i("6021");var a=i("f0c5"),s=Object(a["a"])(o["default"],n["b"],n["c"],!1,null,"1bf07c9a",null,!1,n["a"],void 0);t["default"]=s.exports},bd6f:function(e,t,i){"use strict";i.r(t);var n=i("6d79"),o=i.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){i.d(t,e,(function(){return n[e]}))}(r);t["default"]=o.a},c13e:function(e,t,i){"use strict";i.r(t);var n=i("822c"),o=i.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){i.d(t,e,(function(){return n[e]}))}(r);t["default"]=o.a},c29c:function(e,t,i){"use strict";i.d(t,"b",(function(){return o})),i.d(t,"c",(function(){return r})),i.d(t,"a",(function(){return n}));var n={uImage:i("ba4b").default,uTag:i("8219").default,priceFormat:i("fefe").default},o=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("v-uni-view",{staticClass:"order-goods"},e._l(e.list,(function(t,n){return i("v-uni-view",{key:n,staticClass:"item-wrap"},[i("v-uni-view",{staticClass:"item flex",on:{click:function(i){arguments[0]=i=e.$handleEvent(i),e.jumpGoods(t)}}},[i("v-uni-view",{staticClass:"goods-img"},[i("u-image",{attrs:{width:"180rpx","border-radius":"10rpx",height:"180rpx","lazy-load":!0,src:t.image_str||t.image}})],1),i("v-uni-view",{staticClass:"goods-info m-l-20 flex-1"},[i("v-uni-view",{staticClass:"goods-name line-2 m-b-10"},[t.people_num?i("u-tag",{staticClass:"m-r-10",attrs:{text:t.people_num+"人团",size:"mini",type:"primary",mode:"plain"}}):e._e(),e._v(e._s(t.goods_name||t.name))],1),i("v-uni-view",{staticClass:"goods-spec xs muted m-b-20"},[e._v(e._s(t.spec_value||t.spec_value_str))]),i("v-uni-view",{staticClass:"flex row-between"},[i("v-uni-view",{staticClass:"goods-price"},[i("v-uni-view",{staticClass:"primary flex"},[t.is_seckill?i("price-format",{attrs:{weight:"500","subscript-size":24,"first-size":32,"second-size":24,price:t.original_price||t.goods_price}}):i("price-format",{attrs:{weight:"500","subscript-size":24,"first-size":32,"second-size":24,price:t.price||t.goods_price}}),t.is_member?i("v-uni-view",{staticClass:"vip-price flex"},[i("v-uni-view",{staticClass:"price-name xxs"},[e._v("会员价")]),i("v-uni-view",{staticStyle:{padding:"0 10rpx"}},[i("price-format",{attrs:{price:t.member_amount,"first-size":22,"second-size":22,"subscript-size":22,weight:500,color:"#7B3200"}})],1)],1):e._e(),t.team_price?i("v-uni-view",{staticClass:"vip-price flex"},[i("v-uni-view",{staticClass:"price-name xxs"},[e._v("拼团价")]),i("v-uni-view",{staticStyle:{padding:"0 10rpx"}},[i("price-format",{attrs:{price:t.team_price,"first-size":22,"second-size":22,"subscript-size":22,weight:500,color:"#7B3200"}})],1)],1):e._e(),t.is_seckill?i("v-uni-view",{staticClass:"vip-price flex"},[i("v-uni-view",{staticClass:"price-name xxs"},[e._v("秒杀价")]),i("v-uni-view",{staticStyle:{padding:"0 10rpx"}},[i("price-format",{attrs:{price:t.price,"first-size":22,"second-size":22,"subscript-size":22,weight:500,color:"#7B3200"}})],1)],1):e._e()],1)],1),i("v-uni-view",{staticClass:"goods-num sm"},[e._v("x"+e._s(t.num||t.goods_num||t.count))])],1)],1)],1),e.link&&t.comment_btn||t.refund_btn?i("v-uni-view",{staticClass:"goods-footer flex"},[i("v-uni-view",{staticClass:"flex-1"}),t.comment_btn?i("router-link",{staticClass:"m-r-20",attrs:{to:{path:"/bundle/pages/goods_reviews/goods_reviews",query:{id:t.id}}}},[i("v-uni-button",{staticClass:"plain br60",attrs:{size:"xs","hover-class":"none"}},[e._v("评价晒图")])],1):e._e(),t.refund_btn?i("router-link",{attrs:{to:{path:"/bundle/pages/apply_refund/apply_refund",query:{id:t.id,order_id:t.order_id,item_id:t.item_id}}}},[i("v-uni-button",{staticClass:"plain br60",attrs:{size:"xs","hover-class":"none"}},[e._v("申请退款")])],1):e._e()],1):e._e()],1)})),1)},r=[]},c495:function(e,t,i){var n=i("45ee");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[e.i,n,""]]),n.locals&&(e.exports=n.locals);var o=i("4f06").default;o("54b253da",n,!0,{sourceMap:!1,shadowMode:!1})},c60f:function(e,t,i){"use strict";i("7a82");var n=i("ee27").default;Object.defineProperty(t,"__esModule",{value:!0}),t.cancelIntegralOrder=function(e){return o.default.post("integral_order/cancel",{id:e})},t.closeBargainOrder=function(e){return o.default.get("bargain/closeBargain",{params:e})},t.confirmIntegralOrder=function(e){return o.default.post("integral_order/confirm",{id:e})},t.delIntegralOrder=function(e){return o.default.post("integral_order/del",{id:e})},t.getActivityGoodsLists=function(e){return o.default.get("activity_area/activityGoodsList",{params:e})},t.getBargainActivityDetail=function(e){return o.default.get("bargain/bargainDetail",{params:e})},t.getBargainActivityList=function(e){return o.default.get("bargain/orderList",{params:e})},t.getBargainDetail=function(e){return o.default.get("bargain/detail",{params:e})},t.getBargainList=function(e){return o.default.get("bargain/lists",{params:e})},t.getBargainNumber=function(){return o.default.get("bargain/barginNumber")},t.getBargainPost=function(e){return o.default.get("share/shareBargain",{params:e})},t.getCoupon=function(e){return o.default.post("coupon/getCoupon",{coupon_id:e})},t.getCouponList=function(e){return o.default.get("coupon/getCouponList",{params:e})},t.getGroupList=function(e){return o.default.get("team/activity",{params:e})},t.getIntegralGoods=function(e){return o.default.get("integral_goods/lists",{params:e})},t.getIntegralGoodsDetail=function(e){return o.default.get("integral_goods/detail",{params:e})},t.getIntegralOrder=function(e){return o.default.get("integral_order/lists",{params:e})},t.getIntegralOrderDetail=function(e){return o.default.get("integral_order/detail",{params:{id:e}})},t.getIntegralOrderTraces=function(e){return o.default.get("integral_order/orderTraces",{params:{id:e}})},t.getMyCoupon=function(e){return o.default.get("coupon/myCouponList",{params:e})},t.getOrderCoupon=function(e){return o.default.post("coupon/getBuyCouponList",e)},t.getSeckillGoods=function(e){return o.default.get("seckill_goods/getSeckillGoods",{params:e})},t.getSeckillTime=function(){return o.default.get("seckill_goods/getSeckillTime")},t.getSignLists=function(){return o.default.get("sign/lists")},t.getSignRule=function(){return o.default.get("sign/rule")},t.getTeamInfo=function(e){return o.default.get("team/teamInfo",{params:e})},t.getUserGroup=function(e){return o.default.get("team/record",{params:e})},t.helpBargain=function(e){return o.default.post("bargain/knife",e)},t.integralSettlement=function(e){return o.default.get("integral_order/settlement",{params:e})},t.integralSubmitOrder=function(e){return o.default.post("integral_order/submitOrder",e)},t.launchBargain=function(e){return o.default.post("bargain/sponsor",e)},t.teamBuy=function(e){return o.default.post("team/buy",e)},t.teamCheck=function(e){return o.default.post("team/check",e)},t.teamKaiTuan=function(e){return o.default.post("team/kaituan",e)},t.userSignIn=function(){return o.default.get("sign/sign")};var o=n(i("3b33"));i("b08d")},c653:function(e,t,i){"use strict";i("7a82");var n=i("ee27").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=n(i("f07e")),r=n(i("c964")),a=n(i("d0ff"));i("c740"),i("a434"),i("99af"),i("d3b7"),i("14d9"),i("4de4"),i("e9c4"),i("159b"),i("d81d"),i("ac1f"),i("5319");var s=i("b0dc"),c=i("c60f"),u=i("20b7"),d=(i("461a"),{data:function(){return{isFirstLoading:!0,showLoading:!1,address:{contact:"",telephone:"",province:"",district:"",address:"",city:""},orderInfo:{},shopLists:[],teamLists:{delivery_types_arr:[]},addressId:"",useIntegral:0,userRemark:[],couponId:[],teamId:void 0,carts:[],type:"",goodsType:1,goods:"",bargainLaunchId:-1,invoiceArr:[]}},onLoad:function(e){var t=this;uni.$on("selectaddress",(function(e){t.addressId=e.id,t.orderBuyFun()})),uni.$on("invoice",(function(e){var i=t.invoiceArr.findIndex((function(t){return t.shop_id==e.shop_id}));1==e.del&&t.invoiceArr.length?t.invoiceArr.splice(i,1):-1==i?t.invoiceArr=[].concat((0,a.default)(t.invoiceArr),[e]):t.invoiceArr.splice(i,1,e)}));var i=this.$Route.query.data,n=i.goods,o=i.carts,r=i.teamId,s=i.foundId,c=i.type,u=i.goodsType;console.log("this.$Route.query",this.$Route.query),this.goods=n,this.bargainLaunchId=n[0].bargain_launch_id||-1,this.carts=o||[],this.type=c,this.goodsType=u,this.teamId=r,this.foundId=s||"",this.orderBuyFun()},onUnload:function(){uni.$off("selectaddress"),uni.$off("payment"),uni.$off("invoice")},computed:{deliveryType:function(){if(!this.teamId)return this.shopLists.every((function(e){return 2==e.delivery_type}))}},methods:{changeRemark:function(e){var t=this.userRemark.findIndex((function(t){return t.shop_id==e.shop_id}));-1==t?this.userRemark.push(e):this.userRemark[t].remark=e.remark,this.userRemark=this.userRemark.filter((function(e){return e.remark}))},changeCoupon:function(e,t){this.couponId[t]=e,this.orderBuyFun()},changeDeliveryType:function(e,t){t.delivery_type=e;for(var i=0;i<this.goods.length;i++){var n=this.goods[i];t.shop_id==n.shop_id&&(this.goods[i].delivery_type=e,console.log(this.goods[i].delivery_type,this.shopLists))}this.orderBuyFun()},getAuthMsg:function(){return new Promise((function(e){(0,u.getMnpNotice)({scene:1}).then((function(t){1==t.code?uni.requestSubscribeMessage({tmplIds:t.data,fail:function(e){console.log(e.errMsg)},complete:function(){e()}}):e()}))}))},onSubmitOrder:function(){var e=this;uni.showModal({title:"温馨提示",content:"是否确认下单?",confirmColor:"#FF2C3C",success:function(){var t=(0,r.default)((0,o.default)().mark((function t(i){var n;return(0,o.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:n=i.confirm,n&&(e.showLoading=!0,e.orderBuyFun("submit"));case 2:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()})},orderBuyFun:function(){var e=arguments,t=this;return(0,r.default)((0,o.default)().mark((function i(){var n,r,a,u,d,l,f,p,v,h,g,m,b,_,y;return(0,o.default)().wrap((function(i){while(1)switch(i.prev=i.next){case 0:if(n=e.length>0&&void 0!==e[0]?e[0]:"info",r=t.userRemark,t.useIntegral,a=t.carts,u=t.goods,t.bargainLaunchId,d=t.couponId,t.shopLists,l={goods:JSON.stringify(u),address_id:t.addressId||0,cart_id:a.join(),coupon_id:d.filter((function(e){return e})),bargain_launch_id:-1==t.bargainLaunchId?"":t.bargainLaunchId,goods_type:t.goodsType},t.teamId&&"info"==n&&(delete l.goods,l.action="info",l.item_id=t.goods[0].item_id,l.delivery_type=t.goods[0].delivery_type,l.count=t.goods[0].num,l.goods_id=t.goods[0].goods_id,l.team_id=t.teamId),t.teamId&&"submit"==n&&(console.log(t.goods[0],t.shopLists),l.action="buy",l.item_id=t.goods[0].item_id,l.delivery_type=t.goods[0].delivery_type,l.count=t.goods[0].num,l.goods_id=t.goods[0].goods_id,l.team_id=t.foundId),t.teamId||"submit"!=n||(console.log("this.goods",t.goods,t.shopLists),l.delivery_type=t.shopLists[0].delivery_type||0,t.shopLists.forEach((function(e,t){u.map((function(t){t.shop_id==e.shop_id&&(t.delivery_type=e.delivery_type)}))})),l.goods=JSON.stringify(u),l.remark=r.length?JSON.stringify(r):"",l.invoice=JSON.stringify(t.invoiceArr)),"info"!=n){i.next=19;break}if(!t.teamId){i.next=13;break}return i.next=10,(0,c.teamKaiTuan)(l);case 10:i.t1=i.sent,i.next=16;break;case 13:return i.next=15,(0,s.orderInfo)(l);case 15:i.t1=i.sent;case 16:i.t0=i.t1,i.next=29;break;case 19:if(!t.teamId){i.next=25;break}return i.next=22,(0,c.teamKaiTuan)(l);case 22:i.t2=i.sent,i.next=28;break;case 25:return i.next=27,(0,s.orderBuy)(l);case 27:i.t2=i.sent;case 28:i.t0=i.t2;case 29:if(f=i.t0,p=f.data,v=f.code,h=f.msg,"抱歉,库存不足"==h&&setTimeout((function(){uni.navigateBack(1)}),500),1===v){i.next=36;break}return i.abrupt("return",t.showLoading=!1);case 36:if("info"!=n){i.next=46;break}g=p.shop,m=p.address,t.address=m,t.shopLists=g,t.teamLists=g,t.orderInfo=p,console.log("orderData",p),t.$nextTick((function(){t.isFirstLoading=!1})),i.next=62;break;case 46:if("submit"!=n){i.next=62;break}t.showLoading=!1,b=p.shop,t.shopLists=b,console.log("orderData2",p),_="",y=p.type,i.t3=y,i.next="order"===i.t3?56:"trade"===i.t3?58:60;break;case 56:return _=p.order_id,i.abrupt("break",60);case 58:return _=p.trade_id,i.abrupt("break",60);case 60:uni.$on("payment",(function(e){setTimeout((function(){e.result?(console.log("Jason",t),t.$Router.replace({path:"/pages/pay_result/pay_result",query:{id:e.order_id,from:e.from}})):t.$Router.replace({path:"/bundle/pages/user_order/user_order"})}),1e3)})),uni.navigateTo({url:"/pages/payment/payment?from=".concat(y,"&order_id=").concat(_)});case 62:case"end":return i.stop()}}),i)})))()}},watch:{address:function(e){this.addressId=e.id}}});t.default=d},c9e1:function(e,t,i){e.exports=i.p+"static/images/coupon_bg.png"},d5b0:function(e,t,i){"use strict";i.d(t,"b",(function(){return n})),i.d(t,"c",(function(){return o})),i.d(t,"a",(function(){}));var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("v-uni-text",{class:(e.lineThrough?"line-through":"")+" price-format",style:{color:e.color,"font-weight":e.weight}},[e.showSubscript?i("v-uni-text",{style:{"font-size":e.subscriptSize+"rpx","margin-right":"2rpx"}},[e._v("¥")]):e._e(),i("v-uni-text",{style:{"font-size":e.firstSize+"rpx","margin-right":"1rpx"}},[e._v(e._s(e.priceSlice.first))]),e.priceSlice.second?i("v-uni-text",{style:{"font-size":e.secondSize+"rpx"}},[e._v("."+e._s(e.priceSlice.second))]):e._e()],1)},o=[]},d9ab:function(e,t,i){"use strict";i.r(t);var n=i("c29c"),o=i("9a2b");for(var r in o)["default"].indexOf(r)<0&&function(e){i.d(t,e,(function(){return o[e]}))}(r);i("6017");var a=i("f0c5"),s=Object(a["a"])(o["default"],n["b"],n["c"],!1,null,"594a3e18",null,!1,n["a"],void 0);t["default"]=s.exports},dc83:function(e,t,i){"use strict";i.r(t);var n=i("4837"),o=i("3345");for(var r in o)["default"].indexOf(r)<0&&function(e){i.d(t,e,(function(){return o[e]}))}(r);i("e32c");var a=i("f0c5"),s=Object(a["a"])(o["default"],n["b"],n["c"],!1,null,"0b68f884",null,!1,n["a"],void 0);t["default"]=s.exports},ddd2:function(e,t,i){"use strict";i.d(t,"b",(function(){return o})),i.d(t,"c",(function(){return r})),i.d(t,"a",(function(){return n}));var n={uIcon:i("90f3").default},o=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("v-uni-view",{staticClass:"u-input",class:{"u-input--border":e.border,"u-input--error":e.validateState},style:{padding:"0 "+(e.border?20:0)+"rpx",borderColor:e.borderColor,textAlign:e.inputAlign},on:{click:function(t){t.stopPropagation(),arguments[0]=t=e.$handleEvent(t),e.inputClick.apply(void 0,arguments)}}},["textarea"==e.type?i("v-uni-textarea",{staticClass:"u-input__input u-input__textarea",style:[e.getStyle],attrs:{value:e.defaultValue,placeholder:e.placeholder,placeholderStyle:e.placeholderStyle,disabled:e.disabled,maxlength:e.inputMaxlength,fixed:e.fixed,focus:e.focus,autoHeight:e.autoHeight,"selection-end":e.uSelectionEnd,"selection-start":e.uSelectionStart,"cursor-spacing":e.getCursorSpacing,"show-confirm-bar":e.showConfirmbar},on:{input:function(t){arguments[0]=t=e.$handleEvent(t),e.handleInput.apply(void 0,arguments)},blur:function(t){arguments[0]=t=e.$handleEvent(t),e.handleBlur.apply(void 0,arguments)},focus:function(t){arguments[0]=t=e.$handleEvent(t),e.onFocus.apply(void 0,arguments)},confirm:function(t){arguments[0]=t=e.$handleEvent(t),e.onConfirm.apply(void 0,arguments)}}}):i("v-uni-input",{staticClass:"u-input__input",style:[e.getStyle],attrs:{type:"password"==e.type?"text":e.type,value:e.defaultValue,password:"password"==e.type&&!e.showPassword,placeholder:e.placeholder,placeholderStyle:e.placeholderStyle,disabled:e.disabled||"select"===e.type,maxlength:e.inputMaxlength,focus:e.focus,confirmType:e.confirmType,"cursor-spacing":e.getCursorSpacing,"selection-end":e.uSelectionEnd,"selection-start":e.uSelectionStart,"show-confirm-bar":e.showConfirmbar},on:{focus:function(t){arguments[0]=t=e.$handleEvent(t),e.onFocus.apply(void 0,arguments)},blur:function(t){arguments[0]=t=e.$handleEvent(t),e.handleBlur.apply(void 0,arguments)},input:function(t){arguments[0]=t=e.$handleEvent(t),e.handleInput.apply(void 0,arguments)},confirm:function(t){arguments[0]=t=e.$handleEvent(t),e.onConfirm.apply(void 0,arguments)}}}),i("v-uni-view",{staticClass:"u-input__right-icon u-flex"},[e.clearable&&""!=e.value&&e.focused?i("v-uni-view",{staticClass:"u-input__right-icon__clear u-input__right-icon__item",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.onClear.apply(void 0,arguments)}}},[i("u-icon",{attrs:{size:"32",name:"close-circle-fill",color:"#c0c4cc"}})],1):e._e(),e.passwordIcon&&"password"==e.type?i("v-uni-view",{staticClass:"u-input__right-icon__clear u-input__right-icon__item"},[i("u-icon",{attrs:{size:"32",name:e.showPassword?"eye-fill":"eye",color:"#c0c4cc"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.showPassword=!e.showPassword}}})],1):e._e(),"select"==e.type?i("v-uni-view",{staticClass:"u-input__right-icon--select u-input__right-icon__item",class:{"u-input__right-icon--select--reverse":e.selectOpen}},[i("u-icon",{attrs:{name:"arrow-down-fill",size:"26",color:"#c0c4cc"}})],1):e._e()],1)],1)},r=[]},e2db:function(e,t,i){var n=i("b83e");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[e.i,n,""]]),n.locals&&(e.exports=n.locals);var o=i("4f06").default;o("fbf8b9f0",n,!0,{sourceMap:!1,shadowMode:!1})},e32c:function(e,t,i){"use strict";var n=i("7998"),o=i.n(n);o.a},e4e0:function(e,t,i){"use strict";function n(e,t,i){this.$children.map((function(o){e===o.$options.name?o.$emit.apply(o,[t].concat(i)):n.apply(o,[e,t].concat(i))}))}i("7a82"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,i("d81d"),i("99af");var o={methods:{dispatch:function(e,t,i){var n=this.$parent||this.$root,o=n.$options.name;while(n&&(!o||o!==e))n=n.$parent,n&&(o=n.$options.name);n&&n.$emit.apply(n,[t].concat(i))},broadcast:function(e,t,i){n.call(this,e,t,i)}}};t.default=o},ee17:function(e,t,i){"use strict";var n=i("c495"),o=i.n(n);o.a},f79d:function(e,t,i){"use strict";i("7a82"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,i("a9e3"),i("14d9"),i("d81d");var n={name:"u-checkbox",props:{name:{type:[String,Number],default:""},shape:{type:String,default:""},value:{type:Boolean,default:!1},disabled:{type:[String,Boolean],default:""},labelDisabled:{type:[String,Boolean],default:""},activeColor:{type:String,default:""},iconSize:{type:[String,Number],default:""},labelSize:{type:[String,Number],default:""},size:{type:[String,Number],default:""}},data:function(){return{parentDisabled:!1,newParams:{}}},created:function(){this.parent=this.$u.$parent.call(this,"u-checkbox-group"),this.parent&&this.parent.children.push(this)},computed:{isDisabled:function(){return""!==this.disabled?this.disabled:!!this.parent&&this.parent.disabled},isLabelDisabled:function(){return""!==this.labelDisabled?this.labelDisabled:!!this.parent&&this.parent.labelDisabled},checkboxSize:function(){return this.size?this.size:this.parent?this.parent.size:34},checkboxIconSize:function(){return this.iconSize?this.iconSize:this.parent?this.parent.iconSize:20},elActiveColor:function(){return this.activeColor?this.activeColor:this.parent?this.parent.activeColor:"primary"},elShape:function(){return this.shape?this.shape:this.parent?this.parent.shape:"square"},iconStyle:function(){var e={};return this.elActiveColor&&this.value&&!this.isDisabled&&(e.borderColor=this.elActiveColor,e.backgroundColor=this.elActiveColor),e.width=this.$u.addUnit(this.checkboxSize),e.height=this.$u.addUnit(this.checkboxSize),e},iconColor:function(){return this.value?"#ffffff":"transparent"},iconClass:function(){var e=[];return e.push("u-checkbox__icon-wrap--"+this.elShape),1==this.value&&e.push("u-checkbox__icon-wrap--checked"),this.isDisabled&&e.push("u-checkbox__icon-wrap--disabled"),this.value&&this.isDisabled&&e.push("u-checkbox__icon-wrap--disabled--checked"),e.join(" ")},checkboxStyle:function(){var e={};return this.parent&&this.parent.width&&(e.width=this.parent.width,e.flex="0 0 ".concat(this.parent.width)),this.parent&&this.parent.wrap&&(e.width="100%",e.flex="0 0 100%"),e}},methods:{onClickLabel:function(){this.isLabelDisabled||this.isDisabled||this.setValue()},toggle:function(){this.isDisabled||this.setValue()},emitEvent:function(){var e=this;this.$emit("change",{value:!this.value,name:this.name}),setTimeout((function(){e.parent&&e.parent.emitEvent&&e.parent.emitEvent()}),80)},setValue:function(){var e=0;if(this.parent&&this.parent.children&&this.parent.children.map((function(t){t.value&&e++})),1==this.value)this.emitEvent(),this.$emit("input",!this.value);else{if(this.parent&&e>=this.parent.max)return this.$u.toast("最多可选".concat(this.parent.max,"项"));this.emitEvent(),this.$emit("input",!this.value)}}}};t.default=n},fb1d:function(e,t,i){"use strict";i("7a82"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,i("14d9");var n={name:"shop-title",options:{virtualHost:!0},props:{name:{type:String},shop:{type:Object},isLink:{type:Boolean,default:!0}},data:function(){return{}},methods:{toShop:function(){var e=this.isLink,t=this.shop;e&&this.$Router.push({path:"/pages/store_index/store_index",query:{id:t.shop_id||t.id}})}}};t.default=n},fb35:function(e,t,i){var n=i("24fb");t=n(!1),t.push([e.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.confirm-order .confirm-con[data-v-67d49443]{overflow:hidden;padding-bottom:calc(%?120?% + env(safe-area-inset-bottom))}.confirm-order .confirm-con .address[data-v-67d49443]{min-height:%?164?%;padding:0 %?24?%;border-radius:%?14?%;margin:%?20?% %?20?% 0}.confirm-order .confirm-con .img-line[data-v-67d49443]{height:1.5px;width:100%;display:block}.confirm-order .price[data-v-67d49443]{padding:%?28?% %?20?%}.confirm-order .price .item[data-v-67d49443]:not(:last-of-type){margin-bottom:%?20?%}.confirm-order .contain[data-v-67d49443]{border-radius:%?14?%;margin:%?20?% %?20?% 0;background-color:#fff;overflow:hidden}.confirm-order .radio-group[data-v-67d49443]{display:block}.confirm-order .footer[data-v-67d49443]{position:fixed;bottom:0;left:0;right:0;z-index:99;height:%?100?%;padding:0 %?30?%;box-sizing:initial;padding-bottom:env(safe-area-inset-bottom)}.confirm-order .footer .btn[data-v-67d49443]{background:linear-gradient(90deg,#f95f2f,#ff2c3c);padding:0 %?50?%}',""]),e.exports=t},fd55:function(e,t,i){"use strict";i.d(t,"b",(function(){return o})),i.d(t,"c",(function(){return r})),i.d(t,"a",(function(){return n}));var n={shopTitle:i("95a6").default,orderGoods:i("d9ab").default,uIcon:i("90f3").default,priceFormat:i("fefe").default,uInput:i("b503").default,uModal:i("8d42").default,uCheckbox:i("dc83").default,uPopup:i("5cc5").default,tabs:i("741a").default,tab:i("5652").default,couponOrder:i("5783").default},o=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("v-uni-view",{staticClass:"order-shop"},[i("v-uni-view",{staticClass:"m-l-20"},[i("shop-title",{attrs:{shop:{shop_name:e.item.shop_name},"is-link":!1}})],1),i("v-uni-view",{staticClass:"order-goods"},[i("order-goods",{attrs:{list:e.item.goods}})],1),0==e.orderType?i("v-uni-view",{staticClass:"item flex row-between",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.onShowCoupon.apply(void 0,arguments)}}},[i("v-uni-view",[e._v("店铺优惠券")]),i("v-uni-view",{staticClass:"flex"},[e.item.discount_amount?i("v-uni-text",{staticClass:"primary"},[e._v("-￥"+e._s(e.item.discount_amount))]):i("v-uni-text",{staticClass:"muted"},[e._v("请选择")]),i("u-icon",{staticClass:"m-l-10",attrs:{name:"arrow-right muted",size:"24"}})],1)],1):e._e(),i("v-uni-view",{staticClass:"item flex",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.chengeDelivery.apply(void 0,arguments)}}},[i("v-uni-view",[e._v("配送方式")]),i("v-uni-view",{staticClass:"flex-1 m-l-20 muted"},[1==e.item.delivery_type?[e._v("快递费"),i("price-format",{attrs:{price:e.item.shipping_price}})]:e._e()],2),i("v-uni-view",[e._v(e._s(e.item.delivery_types_arr[e.delivery_type_index].delivery_type_text)),i("u-icon",{staticClass:"m-l-10",attrs:{name:"arrow-right muted",size:"24"}})],1)],1),"线下自提"==e.item.delivery_types_arr[e.delivery_type_index].delivery_type_text?i("v-uni-view",{staticClass:"flex pickCard",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.openLocation.apply(void 0,arguments)}}},[i("v-uni-view",[i("v-uni-image",{staticClass:"icon-md m-r-20",attrs:{src:"/static/images/icon_address.png"}})],1),i("v-uni-view",{staticClass:"text"},[i("v-uni-view",[e._v(e._s(e.item.province)+" "+e._s(e.item.city)+e._s(e.item.district)+" "+e._s(e.item.address))]),i("v-uni-view",{staticClass:"muted"},[e._v("营业时间:"+e._s(e.item.run_start_time)+" ~ "+e._s(e.item.run_end_time))])],1)],1):e._e(),e.item.open_invoice?i("v-uni-view",{staticClass:"item flex row-between col-top m-t-20",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.handleInvoice(e.item.shop_id)}}},[i("v-uni-view",[e._v("开具发票")]),i("v-uni-view",{staticClass:"invoice col-top"},[i("v-uni-text",{staticClass:"muted invoice--text"},[e._v(e._s(e.getCurrentShopInvoice))]),i("u-icon",{staticClass:"m-t-10 m-l-10",attrs:{name:"arrow-right muted",size:"24"}})],1)],1):e._e(),i("v-uni-view",{staticClass:"item flex row-between"},[i("v-uni-view",[e._v("买家留言")]),i("u-input",{staticClass:"flex-1 m-l-20",attrs:{clearable:!1,placeholder:"请添加备注（150字以内）"},model:{value:e.remark,callback:function(t){e.remark=t},expression:"remark"}})],1),i("v-uni-view",{staticClass:"item flex row-right"},[e._v("共"+e._s(e.item.total_num||e.item.goods&&e.item.goods[0].count)+"件，合计："),i("price-format",{attrs:{weight:"500",color:e.colorConfig.primary,"first-size":36,"second-size":36,price:e.item.total_amount||e.item.goods&&e.item.goods[0].team_price}})],1),i("u-modal",{attrs:{title:"请选择配送方式",showCancelButton:!0,"confirm-color":e.colorConfig.primary,"z-index":9999},on:{confirm:function(t){arguments[0]=t=e.$handleEvent(t),e.changeDeliveryType.apply(void 0,arguments)}},model:{value:e.showDelivery,callback:function(t){e.showDelivery=t},expression:"showDelivery"}},e._l(e.item.delivery_types_arr,(function(t,n){return i("v-uni-view",{key:n,staticClass:"delivery nr"},[i("v-uni-view",{staticClass:"flex row-between bg-body delivery-item",on:{click:function(i){arguments[0]=i=e.$handleEvent(i),e.delivery_type=t.delivery_type}}},[i("v-uni-text",[e._v(e._s(t.delivery_type_text))]),i("u-checkbox",{attrs:{value:t.delivery_type==e.delivery_type,shape:"circle"}})],1)],1)})),1),0==e.orderType?i("u-popup",{attrs:{"border-radius":"14",mode:"bottom",closeable:!0,"safe-area-inset-bottom":!0},model:{value:e.showCoupon,callback:function(t){e.showCoupon=t},expression:"showCoupon"}},[i("v-uni-view",{staticClass:"p-30"},[i("v-uni-view",{staticClass:"title"},[e._v("优惠券")])],1),e.showCoupon?i("v-uni-view",[i("tabs",{attrs:{current:e.active,"bar-width":100,"is-scroll":!1}},[i("tab",{attrs:{name:"可使用优惠券 ("+e.usableCoupon.length+")"}},[i("coupon-order",{attrs:{list:e.usableCoupon,type:0,"coupon-id":e.couponId},on:{change:function(t){arguments[0]=t=e.$handleEvent(t),e.onSelectCoupon.apply(void 0,arguments)}}})],1),i("tab",{attrs:{name:"不可用优惠券 ("+e.unusableCoupon.length+")"}},[i("coupon-order",{attrs:{list:e.unusableCoupon,type:1},on:{change:function(t){arguments[0]=t=e.$handleEvent(t),e.showCoupon=!1}}})],1)],1)],1):e._e()],1):e._e()],1)},r=[]},fefe:function(e,t,i){"use strict";i.r(t);var n=i("d5b0"),o=i("bd6f");for(var r in o)["default"].indexOf(r)<0&&function(e){i.d(t,e,(function(){return o[e]}))}(r);i("ee17");var a=i("f0c5"),s=Object(a["a"])(o["default"],n["b"],n["c"],!1,null,"0a5a34e0",null,!1,n["a"],void 0);t["default"]=s.exports}}]);