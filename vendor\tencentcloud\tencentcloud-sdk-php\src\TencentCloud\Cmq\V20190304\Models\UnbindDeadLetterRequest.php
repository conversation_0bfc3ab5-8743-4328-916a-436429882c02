<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Cmq\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * UnbindDeadLetter请求参数结构体
 *
 * @method string getQueueName() 获取死信策略源队列名称，调用本接口会清空该队列的死信队列策略。
 * @method void setQueueName(string $QueueName) 设置死信策略源队列名称，调用本接口会清空该队列的死信队列策略。
 */
class UnbindDeadLetterRequest extends AbstractModel
{
    /**
     * @var string 死信策略源队列名称，调用本接口会清空该队列的死信队列策略。
     */
    public $QueueName;

    /**
     * @param string $QueueName 死信策略源队列名称，调用本接口会清空该队列的死信队列策略。
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("QueueName",$param) and $param["QueueName"] !== null) {
            $this->QueueName = $param["QueueName"];
        }
    }
}
