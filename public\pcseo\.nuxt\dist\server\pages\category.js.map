{"version": 3, "file": "pages/category.js", "sources": ["webpack:///./components/price-formate.vue?b158", "webpack:///./components/price-formate.vue?4c8e", "webpack:///./components/price-formate.vue", "webpack:///./components/price-formate.vue?3e0e", "webpack:///./components/price-formate.vue?2b64", "webpack:///./components/price-formate.vue?8e8e", "webpack:///./components/price-formate.vue?32b6", "webpack:///./components/null-data.vue?48f8", "webpack:///./components/null-data.vue?97fe", "webpack:///./components/null-data.vue?fba4", "webpack:///./components/null-data.vue?cbf9", "webpack:///./components/null-data.vue", "webpack:///./components/null-data.vue?da63", "webpack:///./components/null-data.vue?475d", "webpack:///./utils/tools.js", "webpack:///./components/goods-list.vue?c658", "webpack:///./components/goods-list.vue?fc8c", "webpack:///./components/goods-list.vue?0eb4", "webpack:///./components/goods-list.vue?0d98", "webpack:///./components/goods-list.vue", "webpack:///./components/goods-list.vue?d834", "webpack:///./components/goods-list.vue?12b6", "webpack:///./static/images/goods_null.png", "webpack:///./pages/category.vue?3052", "webpack:///./pages/category.vue?6c3f", "webpack:///./pages/category.vue?55fa", "webpack:///./pages/category.vue?4346", "webpack:///./pages/category.vue", "webpack:///./pages/category.vue?3467", "webpack:///./pages/category.vue?8374"], "sourcesContent": ["// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../node_modules/css-loader/dist/cjs.js??ref--3-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--3-oneOf-1-2!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./price-formate.vue?vue&type=style&index=0&lang=css&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"3181fc86\", content, true, context)\n};", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('span',{class:(_vm.lineThrough ? 'line-through' : '') + 'price-format',style:({ color: _vm.color, 'font-weight': _vm.weight })},[_vm._ssrNode(((_vm.showSubscript)?(\"<span\"+(_vm._ssrStyle(null,{\n            'font-size': _vm.subscriptSize + 'px',\n            'margin-right': '1px',\n        }, null))+\">¥</span>\"):\"<!---->\")+\" <span\"+(_vm._ssrStyle(null,{ 'font-size': _vm.firstSize + 'px', 'margin-right': '1px' }, null))+\">\"+_vm._ssrEscape(_vm._s(_vm.priceSlice.first))+\"</span> \"+((_vm.priceSlice.second)?(\"<span\"+(_vm._ssrStyle(null,{ 'font-size': _vm.secondSize + 'px' }, null))+\">\"+_vm._ssrEscape(\".\"+_vm._s(_vm.priceSlice.second))+\"</span>\"):\"<!---->\"))])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nexport default {\n    data() {\n        return {\n            priceSlice: {},\n        }\n    },\n    components: {},\n    props: {\n        firstSize: {\n            type: Number,\n            default: 14,\n        },\n        secondSize: {\n            type: Number,\n            default: 14,\n        },\n        color: {\n            type: String,\n        },\n        weight: {\n            type: [String, Number],\n            default: 400,\n        },\n        price: {\n            type: [String, Number],\n            default: '',\n        },\n        showSubscript: {\n            type: Boolean,\n            default: true,\n        },\n        subscriptSize: {\n            type: Number,\n            default: 14,\n        },\n        lineThrough: {\n            type: Boolean,\n            default: false,\n        },\n    },\n    created() {\n        this.priceFormat()\n    },\n    watch: {\n        price(val) {\n            this.priceFormat()\n        },\n    },\n    methods: {\n        priceFormat() {\n            let { price } = this\n            let priceSlice = {}\n            if (price !== null) {\n                price = parseFloat(price)\n                price = String(price).split('.')\n                priceSlice.first = price[0]\n                priceSlice.second = price[1]\n                this.priceSlice = priceSlice\n            }\n        },\n    },\n}\n", "import mod from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./price-formate.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./price-formate.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./price-formate.vue?vue&type=template&id=0c4d5c85&\"\nimport script from \"./price-formate.vue?vue&type=script&lang=js&\"\nexport * from \"./price-formate.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./price-formate.vue?vue&type=style&index=0&lang=css&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  null,\n  \"7ae24710\"\n  \n)\n\nexport default component.exports", "export * from \"-!../node_modules/vue-style-loader/index.js??ref--3-oneOf-1-0!../node_modules/css-loader/dist/cjs.js??ref--3-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--3-oneOf-1-2!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./price-formate.vue?vue&type=style&index=0&lang=css&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".price-format{display:flex;align-items:baseline}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/sass-resources-loader/lib/loader.js??ref--7-oneOf-1-4!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./null-data.vue?vue&type=style&index=0&id=93598fb0&lang=scss&scoped=true&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"12a18d22\", content, true, context)\n};", "export * from \"-!../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/sass-resources-loader/lib/loader.js??ref--7-oneOf-1-4!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./null-data.vue?vue&type=style&index=0&id=93598fb0&lang=scss&scoped=true&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".null-data[data-v-93598fb0]{padding:100px}.null-data .img-null[data-v-93598fb0]{width:150px;height:150px}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"bg-white flex-col col-center null-data\"},[_vm._ssrNode(\"<img\"+(_vm._ssrAttr(\"src\",_vm.img))+\" alt class=\\\"img-null\\\"\"+(_vm._ssrStyle(null,_vm.imgStyle, null))+\" data-v-93598fb0> <div class=\\\"muted mt8\\\" data-v-93598fb0>\"+_vm._ssrEscape(_vm._s(_vm.text))+\"</div>\")])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n\nexport default {\n    components: {},\n    props: {\n        img: {\n            type: String,\n        },\n        text: {\n            type: String,\n            default: '暂无数据',\n        },\n        imgStyle: {\n            type: String,\n            default: '',\n        },\n    },\n    methods: {},\n}\n", "import mod from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./null-data.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./null-data.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./null-data.vue?vue&type=template&id=93598fb0&scoped=true&\"\nimport script from \"./null-data.vue?vue&type=script&lang=js&\"\nexport * from \"./null-data.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./null-data.vue?vue&type=style&index=0&id=93598fb0&lang=scss&scoped=true&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  \"93598fb0\",\n  \"728f99de\"\n  \n)\n\nexport default component.exports", "\n//节流\nexport const trottle = (func, time = 1000, context) => {\n\tlet previous = new Date(0).getTime()\n\treturn function(...args) {\n\t\tlet now = new Date().getTime()\n\t\tif (now - previous > time) {\n\t\t\tfunc.apply(context, args)\n\t\t\tprevious = now\n\t\t}\n\t}\n}\n\n\n//获取url后的参数  以对象返回\nexport function strToParams(str) {\n\tvar newparams = {}\n\tfor (let item of str.split('&')) {\n\t\tnewparams[item.split('=')[0]] = item.split('=')[1]\n\t}\n\treturn newparams\n}\n\n//对象参数转为以？&拼接的字符\nexport function paramsToStr(params) {\n\tlet p = '';\n\tif (typeof params == 'object') {\n\t\tp = '?'\n\t\tfor (let props in params) {\n\t\t\tp += `${props}=${params[props]}&`\n\t\t}\n\t\tp = p.slice(0, -1)\n\t}\n\treturn p\n}\n\n/**\n * @description 复制到剪切板\n * @param value { String } 复制内容\n * @return { Promise } resolve | reject\n */\n export const copyClipboard = (value) => {\n    const elInput = document.createElement('input')\n\n    elInput.setAttribute('value', value)\n    document.body.appendChild(elInput)\n    elInput.select()\n\n    try{\n        if(document.execCommand('copy'))\n            return Promise.resolve()\n        else\n            throw new Error()\n    } catch(err) {\n        return Promise.reject(err)\n    } finally {\n        document.body.removeChild(elInput)\n    }\n}", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/sass-resources-loader/lib/loader.js??ref--7-oneOf-1-4!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./goods-list.vue?vue&type=style&index=0&id=060944d1&lang=scss&scoped=true&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"1469a4e1\", content, true, context)\n};", "export * from \"-!../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/sass-resources-loader/lib/loader.js??ref--7-oneOf-1-4!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./goods-list.vue?vue&type=style&index=0&id=060944d1&lang=scss&scoped=true&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".goods-list[data-v-060944d1]{align-items:stretch}.goods-list .goods-item[data-v-060944d1]{display:block;box-sizing:border-box;width:224px;height:310px;margin-bottom:16px;padding:12px 12px 16px;border-radius:4px;transition:all .2s}.goods-list .goods-item[data-v-060944d1]:hover{transform:translateY(-8px);box-shadow:0 0 6px rgba(0,0,0,.1)}.goods-list .goods-item .goods-img[data-v-060944d1]{width:200px;height:200px}.goods-list .goods-item .name[data-v-060944d1]{margin-bottom:10px;height:40px;line-height:20px}.goods-list .goods-item .seckill .btn[data-v-060944d1]{padding:4px 12px;border-radius:4px;border:1px solid transparent}.goods-list .goods-item .seckill .btn.not-start[data-v-060944d1]{border-color:#ff2c3c;color:#ff2c3c;background-color:transparent}.goods-list .goods-item .seckill .btn.end[data-v-060944d1]{background-color:#e5e5e5;color:#fff}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"goods-list flex flex-wrap\"},_vm._l((_vm.list),function(item,index){return _c('nuxt-link',{key:index,staticClass:\"goods-item bg-white\",style:({ marginRight: (index + 1) % _vm.num == 0 ? 0 : '14px' }),attrs:{\"to\":(\"/goods_details/\" + (item.id||item.goods_id))}},[_c('el-image',{staticClass:\"goods-img\",attrs:{\"lazy\":\"\",\"src\":item.image||item.goods_image,\"alt\":\"\"}}),_vm._v(\" \"),_c('div',{staticClass:\"name line-2\"},[_vm._v(_vm._s(item.name||item.goods_name))]),_vm._v(\" \"),(_vm.type == 'seckill')?_c('div',{staticClass:\"seckill flex row-between\"},[_c('div',{staticClass:\"primary flex\"},[_vm._v(\"\\n                秒杀价\\n                \"),_c('price-formate',{attrs:{\"price\":item.seckill_price,\"first-size\":18}})],1),_vm._v(\" \"),_c('div',{class:['btn bg-primary white', {'not-start' : _vm.status == 0, end: _vm.status == 2}]},[_vm._v(_vm._s(_vm.getSeckillText)+\"\\n            \")])]):_c('div',{staticClass:\"flex row-between flex-wrap\"},[_c('div',{staticClass:\"price flex col-baseline\"},[_c('div',{staticClass:\"primary m-r-8\"},[_c('price-formate',{attrs:{\"price\":item.min_price || item.price,\"first-size\":16}})],1),_vm._v(\" \"),_c('div',{staticClass:\"muted sm line-through\"},[_c('price-formate',{attrs:{\"price\":item.market_price}})],1)]),_vm._v(\" \"),_c('div',{staticClass:\"muted xs\"},[_vm._v(_vm._s(item.sales_total || item.sales_sum || 0)+\"人购买\")])])],1)}),1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nexport default {\n    props: {\n        list: {\n            type: Array,\n            default: () => [],\n        },\n        num: {\n            type: Number,\n            default: 5,\n        },\n        type: {\n            type: String,\n        },\n        status: {\n            type: Number,\n        },\n    },\n    watch: {\n        list: {\n            immediate: true,\n            handler: function (val) {},\n        },\n    },\n    computed: {\n        getSeckillText() {\n            switch (this.status) {\n                case 0:\n                    return \"未开始\";\n                case 1:\n                    return \"立即抢购\";\n                case 2:\n                    return \"已结束\";\n            }\n        },\n    },\n};\n", "import mod from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./goods-list.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./goods-list.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./goods-list.vue?vue&type=template&id=060944d1&scoped=true&\"\nimport script from \"./goods-list.vue?vue&type=script&lang=js&\"\nexport * from \"./goods-list.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./goods-list.vue?vue&type=style&index=0&id=060944d1&lang=scss&scoped=true&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  \"060944d1\",\n  \"606a8712\"\n  \n)\n\nexport default component.exports\n\n/* nuxt-component-imports */\ninstallComponents(component, {PriceFormate: require('/Users/<USER>/Desktop/vue/pc/components/price-formate.vue').default})\n", "module.exports = __webpack_public_path__ + \"img/goods_null.38f1689.png\";", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/sass-resources-loader/lib/loader.js??ref--7-oneOf-1-4!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./category.vue?vue&type=style&index=0&id=7bc86c9d&lang=scss&scoped=true&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"08388cce\", content, true, context)\n};", "export * from \"-!../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/sass-resources-loader/lib/loader.js??ref--7-oneOf-1-4!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./category.vue?vue&type=style&index=0&id=7bc86c9d&lang=scss&scoped=true&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".category[data-v-7bc86c9d]{padding:16px 0}.category .category-hd .category-wrap[data-v-7bc86c9d]{padding:0 16px}.category .category-hd .category-con[data-v-7bc86c9d]{border-bottom:1px dashed #e5e5e5;align-items:flex-start;padding-top:16px}.category .category-hd .category-con .name[data-v-7bc86c9d]{flex:none}.category .category-hd .category-con .item[data-v-7bc86c9d]{margin-bottom:16px;width:84px;margin-left:14px;cursor:pointer}.category .category-hd .category-con .item.active[data-v-7bc86c9d],.category .category-hd .category-con .item[data-v-7bc86c9d]:hover{color:#ff2c3c}.category .category-hd .sort[data-v-7bc86c9d]{padding:15px 16px}.category .category-hd .sort .sort-name .item[data-v-7bc86c9d]{margin-right:30px;cursor:pointer}.category .category-hd .sort .sort-name .item.active[data-v-7bc86c9d]{color:#ff2c3c}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"category\"},[_vm._ssrNode(\"<div class=\\\"category-hd bg-white\\\" data-v-7bc86c9d><div class=\\\"category-wrap\\\" data-v-7bc86c9d><div class=\\\"category-con flex\\\" data-v-7bc86c9d><div class=\\\"name muted\\\" data-v-7bc86c9d>一级分类：</div> <div class=\\\"category-list flex flex-wrap lighter\\\" data-v-7bc86c9d>\"+(_vm._ssrList((_vm.categoryOne),function(item,index){return (\"<div\"+(_vm._ssrClass(null,[\n                            'item line1',\n                            { active: _vm.oneIndex == index } ]))+\" data-v-7bc86c9d>\"+_vm._ssrEscape(\"\\n                        \"+_vm._s(item.name)+\"\\n                    \")+\"</div>\")}))+\"</div></div> <div class=\\\"category-con flex\\\" data-v-7bc86c9d><div class=\\\"name muted\\\" data-v-7bc86c9d>二级分类：</div> <div class=\\\"category-list flex flex-wrap lighter\\\" data-v-7bc86c9d><div\"+(_vm._ssrClass(null,['item line1', { active: _vm.twoIndex === '' }]))+\" data-v-7bc86c9d>\\n                        全部\\n                    </div> \"+(_vm._ssrList((_vm.categoryTwo),function(item,index){return (\"<div\"+(_vm._ssrClass(null,[\n                            'item line1',\n                            { active: _vm.twoIndex === index } ]))+\" data-v-7bc86c9d>\"+_vm._ssrEscape(\"\\n                        \"+_vm._s(item.name)+\"\\n                    \")+\"</div>\")}))+\"</div></div> <div class=\\\"category-con flex\\\" data-v-7bc86c9d><div class=\\\"name muted\\\" data-v-7bc86c9d>三级分类：</div> <div class=\\\"category-list flex flex-wrap lighter\\\" data-v-7bc86c9d><div\"+(_vm._ssrClass(null,[\n                            'item line1',\n                            { active: _vm.threeIndex === '' } ]))+\" data-v-7bc86c9d>\\n                        全部\\n                    </div> \"+(_vm._ssrList((_vm.categoryThree),function(item,index){return (\"<div\"+(_vm._ssrClass(null,[\n                            'item line1',\n                            { active: _vm.threeIndex === index } ]))+\" data-v-7bc86c9d>\"+_vm._ssrEscape(\"\\n                        \"+_vm._s(item.name)+\"\\n                    \")+\"</div>\")}))+\"</div></div></div> <div class=\\\"sort m-b-16 flex bg-white\\\" data-v-7bc86c9d><div class=\\\"title muted\\\" data-v-7bc86c9d>排序方式：</div> <div class=\\\"sort-name m-l-16 flex lighter\\\" data-v-7bc86c9d><div\"+(_vm._ssrClass(null,['item', { active: _vm.sortType == '' }]))+\" data-v-7bc86c9d>\\n                    综合\\n                </div> <div\"+(_vm._ssrClass(null,['item', { active: _vm.sortType == 'price' }]))+\" data-v-7bc86c9d>\\n                    价格\\n                    <i class=\\\"el-icon-arrow-down\\\"\"+(_vm._ssrStyle(null,null, { display: (_vm.priceSort == 'desc') ? '' : 'none' }))+\" data-v-7bc86c9d></i> <i class=\\\"el-icon-arrow-up\\\"\"+(_vm._ssrStyle(null,null, { display: (_vm.priceSort == 'asc') ? '' : 'none' }))+\" data-v-7bc86c9d></i></div> <div\"+(_vm._ssrClass(null,['item', { active: _vm.sortType == 'sales_sum' }]))+\" data-v-7bc86c9d>\\n                    销量\\n                    <i class=\\\"el-icon-arrow-down\\\"\"+(_vm._ssrStyle(null,null, { display: (_vm.saleSort == 'desc') ? '' : 'none' }))+\" data-v-7bc86c9d></i> <i class=\\\"el-icon-arrow-up\\\"\"+(_vm._ssrStyle(null,null, { display: (_vm.saleSort == 'asc') ? '' : 'none' }))+\" data-v-7bc86c9d></i></div></div></div></div> \"),(_vm.isHasGoods)?_vm._ssrNode(\"<div data-v-7bc86c9d>\",\"</div>\",[_c('goods-list',{attrs:{\"list\":_vm.goodsList}}),_vm._ssrNode(\" \"),(_vm.count)?_vm._ssrNode(\"<div class=\\\"pagination flex row-center\\\" style=\\\"padding-bottom: 38px\\\" data-v-7bc86c9d>\",\"</div>\",[_c('el-pagination',{attrs:{\"background\":\"\",\"hide-on-single-page\":\"\",\"layout\":\"prev, pager, next\",\"total\":_vm.count,\"prev-text\":\"上一页\",\"next-text\":\"下一页\",\"page-size\":20},on:{\"current-change\":_vm.changePage}})],1):_vm._e()],2):_c('null-data',{attrs:{\"img\":require('@/static/images/goods_null.png'),\"text\":\"暂无商品~\"}})],2)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { trottle } from '~/utils/tools'\nexport default {\n    head() {\n        return {\n            title: this.$store.getters.headTitle,\n            link: [\n                {\n                    rel: 'icon',\n                    type: 'image/x-icon',\n                    href: this.$store.getters.favicon,\n                },\n            ],\n        }\n    },\n    watchQuery: true,\n    async asyncData({ query, $get }) {\n        let { data } = await $get('pc/category')\n        return {\n            categoryList: data,\n        }\n    },\n    data() {\n        return {\n            count: 0,\n            oneIndex: 0,\n            twoIndex: '',\n            threeIndex: '',\n            categoryOne: [],\n            categoryTwo: [],\n            categoryThree: [],\n            sortType: '',\n            saleSort: 'desc',\n            priceSort: 'desc',\n            page: '',\n            goodsList: [],\n            cateId: 0,\n            isHasGoods: true,\n        }\n    },\n    created() {\n        this.changeSortType = trottle(this.changeSortType, 500, this)\n    },\n    methods: {\n        changeData(id) {\n            const { categoryList } = this\n            this.setIndex(id)\n            this.categoryOne = categoryList\n            this.categoryTwo = categoryList[this.oneIndex]\n                ? categoryList[this.oneIndex].sons\n                : []\n\n            this.categoryThree = this.categoryTwo[this.twoIndex]\n                ? this.categoryTwo[this.twoIndex].sons\n                : []\n\n            this.setCateId(id)\n            this.getGoods()\n        },\n        setCateId(id) {\n            if (\n                this.twoIndex == '' &&\n                this.threeIndex == '' &&\n                this.oneIndex !== ''\n            ) {\n                this.cateId = this.categoryOne[this.oneIndex].id\n            }\n            if (this.threeIndex == '' && this.twoIndex !== '') {\n                this.cateId = this.categoryTwo[this.twoIndex].id\n            }\n            if (id) {\n                this.cateId = id\n            }\n        },\n        setIndex(id) {\n            const { categoryList } = this\n            categoryList.some((oitem, oindex) => {\n                if (oitem.id === id) {\n                    this.oneIndex = oindex\n                    this.twoIndex = ''\n                    this.threeIndex = ''\n                    return true\n                }\n                return (\n                    oitem.sons &&\n                    oitem.sons.some((witem, windex) => {\n                        if (witem.id === id) {\n                            this.oneIndex = oindex\n                            this.twoIndex = windex\n                            this.threeIndex = ''\n                            return true\n                        }\n                        return (\n                            witem.sons &&\n                            witem.sons.some((titem, tindex) => {\n                                if (titem.id === id) {\n                                    this.oneIndex = oindex\n                                    this.twoIndex = windex\n                                    this.threeIndex = tindex\n                                    return true\n                                }\n                            })\n                        )\n                    })\n                )\n            })\n        },\n        clickAllTwo() {\n            this.twoIndex = ''\n            this.threeIndex = ''\n            this.changeData()\n        },\n        clickAll() {\n            this.threeIndex = ''\n            this.changeData()\n        },\n        changeSortType(type) {\n            this.sortType = type\n            switch (type) {\n                case 'price':\n                    if (this.priceSort == 'asc') {\n                        this.priceSort = 'desc'\n                    } else if (this.priceSort == 'desc') {\n                        this.priceSort = 'asc'\n                    }\n                    break\n                case 'sales_sum':\n                    if (this.saleSort == 'asc') {\n                        this.saleSort = 'desc'\n                    } else if (this.saleSort == 'desc') {\n                        this.saleSort = 'asc'\n                    }\n                    break\n                default:\n            }\n            this.getGoods()\n        },\n        changePage(current) {\n            this.page = current\n            this.getGoods()\n        },\n        async getGoods() {\n            const { priceSort, sortType, saleSort } = this\n            const params = {\n                page_size: 20,\n                page_no: this.page,\n                platform_cate_id: this.cateId,\n            }\n            switch (sortType) {\n                case 'price':\n                    params.sort_by_price = priceSort\n                    break\n                case 'sales_sum':\n                    params.sort_by_sales = saleSort\n                    break\n            }\n            const {\n                data: { lists, count },\n            } = await this.$get('goods/getGoodsList', {\n                params,\n            })\n            this.goodsList = lists\n            if (!lists.length) {\n                this.isHasGoods = false\n            } else {\n                this.isHasGoods = true\n            }\n            this.count = count\n        },\n    },\n    watch: {\n        categoryList: {\n            immediate: true,\n            handler(value) {\n                const { id } = this.$route.query\n                this.changeData(Number(id))\n            },\n        },\n    },\n}\n", "import mod from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./category.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./category.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./category.vue?vue&type=template&id=7bc86c9d&scoped=true&\"\nimport script from \"./category.vue?vue&type=script&lang=js&\"\nexport * from \"./category.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./category.vue?vue&type=style&index=0&id=7bc86c9d&lang=scss&scoped=true&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  \"7bc86c9d\",\n  \"50bbd690\"\n  \n)\n\nexport default component.exports\n\n/* nuxt-component-imports */\ninstallComponents(component, {GoodsList: require('/Users/<USER>/Desktop/vue/pc/components/goods-list.vue').default,NullData: require('/Users/<USER>/Desktop/vue/pc/components/null-data.vue').default})\n"], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACXA;AACA;AACA;AACA;AACA;AACA;;;;;ACLA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AADA;AAGA;AACA;AAAA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AADA;AAGA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AA5BA;AACA;AAgCA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAJA;AAKA;AACA;AACA;AAAA;AAAA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAZA;AAhDA;;AC1BA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACvBA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACNA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACXA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACNA;AACA;AACA;;;;;ACFA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AADA;AAGA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AARA;AAaA;AAfA;;ACRA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACtBA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AAEA;AACA;AACA;AAAA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AAAA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAIA;AACA;AACA;AACA;AACA;AACA;;;;;;;AC1DA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACXA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACNA;AACA;AACA;;;;;ACFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AADA;AAGA;AACA;AADA;AAZA;AAgBA;AACA;AACA;AACA;AAFA;AADA;AAMA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AAAA;AACA;AANA;AAQA;AACA;AAXA;AAvBA;;AC/BA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AC1BA;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACXA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACNA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACVA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAHA;AAHA;AAUA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AACA;AADA;AAGA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAdA;AAgBA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AAIA;AAIA;AACA;AACA;AACA;AAAA;AACA;AAKA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAfA;AACA;AAgBA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAHA;AACA;AAIA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AANA;AACA;AAOA;AACA;AAAA;AAAA;AAAA;AADA;AAGA;AADA;AAGA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AA9HA;AA+HA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AACA;AACA;AACA;AANA;AADA;AAxKA;;AC1IA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;A", "sourceRoot": ""}