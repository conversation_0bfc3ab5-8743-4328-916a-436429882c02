<?php

namespace app\common\websocket;

use think\facade\Log;
use think\facade\Cache;

/**
 * WebSocket全局管理器
 * 用于管理用户对用户聊天的全局连接状态
 */
class GlobalWebSocketManager
{
    protected static $instance = null;
    protected $cache;
    protected $prefix = 'global_ws_';

    private function __construct()
    {
        $this->cache = Cache::store('redis');
    }

    public static function getInstance()
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * 注册用户连接
     * @param int $user_id 用户ID
     * @param int $fd 连接标识符
     * @param string $type 连接类型 user|user_chat
     * @param array $user_info 用户信息
     */
    public function registerUserConnection($user_id, $fd, $type, $user_info = [])
    {
        try {
            // 存储用户连接信息
            $connection_key = $this->prefix . 'user_' . $user_id;
            $connection_data = [
                'user_id' => $user_id,
                'fd' => $fd,
                'type' => $type,
                'nickname' => $user_info['nickname'] ?? '',
                'avatar' => $user_info['avatar'] ?? '',
                'connect_time' => time(),
                'last_active' => time()
            ];
            
            $this->cache->set($connection_key, json_encode($connection_data), 3600); // 1小时过期

            // 添加到在线用户列表
            $online_key = $this->prefix . 'online_users';
            $this->cache->sAdd($online_key, $user_id);

            // 存储fd到用户的映射
            $fd_key = $this->prefix . 'fd_' . $fd;
            $this->cache->set($fd_key, $user_id, 3600);

            Log::info("用户连接注册成功: user_id={$user_id}, fd={$fd}, type={$type}");
        } catch (\Exception $e) {
            Log::error("注册用户连接失败: " . $e->getMessage());
        }
    }

    /**
     * 移除用户连接
     * @param int $user_id 用户ID
     * @param int $fd 连接标识符
     */
    public function removeUserConnection($user_id, $fd)
    {
        try {
            // 移除用户连接信息
            $connection_key = $this->prefix . 'user_' . $user_id;
            $this->cache->delete($connection_key);

            // 从在线用户列表移除
            $online_key = $this->prefix . 'online_users';
            $this->cache->sRem($online_key, $user_id);

            // 移除fd映射
            $fd_key = $this->prefix . 'fd_' . $fd;
            $this->cache->delete($fd_key);

            Log::info("用户连接移除成功: user_id={$user_id}, fd={$fd}");
        } catch (\Exception $e) {
            Log::error("移除用户连接失败: " . $e->getMessage());
        }
    }

    /**
     * 获取用户连接信息
     * @param int $user_id 用户ID
     * @return array|null
     */
    public function getUserConnection($user_id)
    {
        try {
            $connection_key = $this->prefix . 'user_' . $user_id;
            $data = $this->cache->get($connection_key);
            return $data ? json_decode($data, true) : null;
        } catch (\Exception $e) {
            Log::error("获取用户连接信息失败: " . $e->getMessage());
            return null;
        }
    }

    /**
     * 根据fd获取用户ID
     * @param int $fd 连接标识符
     * @return int|null
     */
    public function getUserIdByFd($fd)
    {
        try {
            $fd_key = $this->prefix . 'fd_' . $fd;
            return $this->cache->get($fd_key);
        } catch (\Exception $e) {
            Log::error("根据fd获取用户ID失败: " . $e->getMessage());
            return null;
        }
    }

    /**
     * 检查用户是否在线
     * @param int $user_id 用户ID
     * @return bool
     */
    public function isUserOnline($user_id)
    {
        try {
            $online_key = $this->prefix . 'online_users';
            return $this->cache->sIsMember($online_key, $user_id);
        } catch (\Exception $e) {
            Log::error("检查用户在线状态失败: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 获取所有在线用户
     * @return array
     */
    public function getOnlineUsers()
    {
        try {
            $online_key = $this->prefix . 'online_users';
            return $this->cache->sMembers($online_key) ?: [];
        } catch (\Exception $e) {
            Log::error("获取在线用户列表失败: " . $e->getMessage());
            return [];
        }
    }

    /**
     * 更新用户活跃时间
     * @param int $user_id 用户ID
     */
    public function updateUserActiveTime($user_id)
    {
        try {
            $connection = $this->getUserConnection($user_id);
            if ($connection) {
                $connection['last_active'] = time();
                $connection_key = $this->prefix . 'user_' . $user_id;
                $this->cache->set($connection_key, json_encode($connection), 3600);
            }
        } catch (\Exception $e) {
            Log::error("更新用户活跃时间失败: " . $e->getMessage());
        }
    }

    /**
     * 清理过期连接
     */
    public function cleanExpiredConnections()
    {
        try {
            $online_users = $this->getOnlineUsers();
            $current_time = time();
            
            foreach ($online_users as $user_id) {
                $connection = $this->getUserConnection($user_id);
                if ($connection && ($current_time - $connection['last_active']) > 3600) {
                    // 超过1小时未活跃，清理连接
                    $this->removeUserConnection($user_id, $connection['fd']);
                    Log::info("清理过期连接: user_id={$user_id}");
                }
            }
        } catch (\Exception $e) {
            Log::error("清理过期连接失败: " . $e->getMessage());
        }
    }

    /**
     * 广播消息给所有在线用户
     * @param array $message 消息内容
     * @param array $exclude_users 排除的用户ID列表
     */
    public function broadcastToAllUsers($message, $exclude_users = [])
    {
        try {
            $online_users = $this->getOnlineUsers();
            $broadcast_count = 0;
            
            foreach ($online_users as $user_id) {
                if (!in_array($user_id, $exclude_users)) {
                    $connection = $this->getUserConnection($user_id);
                    if ($connection && isset($connection['fd'])) {
                        // 这里需要调用WebSocket服务器的推送方法
                        // 具体实现需要根据实际的WebSocket服务器架构
                        $broadcast_count++;
                    }
                }
            }
            
            Log::info("广播消息完成: 发送给{$broadcast_count}个用户");
        } catch (\Exception $e) {
            Log::error("广播消息失败: " . $e->getMessage());
        }
    }

    /**
     * 获取用户聊天状态统计
     * @return array
     */
    public function getChatStatistics()
    {
        try {
            $online_users = $this->getOnlineUsers();
            $user_chat_count = 0;
            $regular_user_count = 0;
            
            foreach ($online_users as $user_id) {
                $connection = $this->getUserConnection($user_id);
                if ($connection) {
                    if ($connection['type'] === 'user_chat') {
                        $user_chat_count++;
                    } else {
                        $regular_user_count++;
                    }
                }
            }
            
            return [
                'total_online' => count($online_users),
                'user_chat_online' => $user_chat_count,
                'regular_user_online' => $regular_user_count
            ];
        } catch (\Exception $e) {
            Log::error("获取聊天统计失败: " . $e->getMessage());
            return [
                'total_online' => 0,
                'user_chat_online' => 0,
                'regular_user_online' => 0
            ];
        }
    }
}
