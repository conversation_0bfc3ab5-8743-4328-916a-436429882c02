/**
 * Shop WebSocket Notification System
 * 用于商家前台接收实时通知
 */
class ShopWebSocketNotification {
    constructor(options = {}) {
        this.shopId = options.shopId || null;
        this.wsUrl = options.wsUrl || 'wss://kefu.huohanghang.cn:9282';
        this.backupUrls = options.backupUrls || [
            'wss://www.huohanghang.cn:9282',
            'wss://127.0.0.1:9282',
            'wss://localhost:9282'
        ];
        this.reconnectDelay = options.reconnectDelay || 3000;
        this.maxReconnectAttempts = options.maxReconnectAttempts || 10;
        this.heartbeatInterval = options.heartbeatInterval || 30000;
        this.onNotification = options.onNotification || this.defaultNotificationHandler;
        this.onStatusChange = options.onStatusChange || null;
        this.debug = options.debug || false;
        
        this.ws = null;
        this.reconnectAttempts = 0;
        this.heartbeatTimer = null;
        this.currentUrlIndex = 0;
        this.isManualClose = false;
    }
    
    /**
     * 初始化并连接 WebSocket
     */
    init() {
        if (!this.shopId) {
            console.error('[ShopWebSocket] 未设置商家ID');
            return false;
        }
        
        this.connect();
        return true;
    }
    
    /**
     * 连接 WebSocket
     */
    connect() {
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            this.log('已经连接，无需重复连接');
            return;
        }
        
        this.isManualClose = false;
        const url = this.getCurrentUrl();
        this.log(`尝试连接到: ${url}`);
        this.updateStatus('connecting');
        
        try {
            this.ws = new WebSocket(url);
            this.setupEventHandlers();
        } catch (e) {
            this.log(`创建 WebSocket 连接失败: ${e.message}`, 'error');
            this.handleConnectionFailure();
        }
    }
    
    /**
     * 设置 WebSocket 事件处理器
     */
    setupEventHandlers() {
        this.ws.onopen = () => {
            this.log('WebSocket 连接成功');
            this.updateStatus('connected');
            this.reconnectAttempts = 0;
            this.currentUrlIndex = 0;
            
            // 启动心跳
            this.startHeartbeat();
            
            // 绑定商家
            setTimeout(() => this.bindShop(), 500);
        };
        
        this.ws.onmessage = (event) => {
            try {
                const data = JSON.parse(event.data);
                this.handleMessage(data);
            } catch (e) {
                this.log(`解析消息失败: ${event.data}`, 'error');
            }
        };
        
        this.ws.onerror = (error) => {
            this.log(`WebSocket 错误: ${error}`, 'error');
            this.updateStatus('error');
        };
        
        this.ws.onclose = () => {
            this.log('WebSocket 连接已关闭');
            this.updateStatus('disconnected');
            this.stopHeartbeat();
            
            if (!this.isManualClose) {
                this.handleReconnect();
            }
        };
    }
    
    /**
     * 处理接收到的消息
     */
    handleMessage(data) {
        switch (data.type) {
            case 'connect':
                this.log(`服务器连接确认: ${data.message}`);
                break;
                
            case 'bind_success':
                this.log(`商家绑定成功: shop_id = ${data.shop_id}`);
                this.updateStatus('bound');
                break;
                
            case 'notification':
                this.log(`收到通知: ${data.data.title}`);
                this.onNotification(data.data);
                break;
                
            case 'heartbeat':
                // 心跳响应
                break;
                
            case 'error':
                this.log(`服务器错误: ${data.message}`, 'error');
                break;
                
            default:
                this.log(`收到未知消息类型: ${data.type}`);
        }
    }
    
    /**
     * 绑定商家
     */
    bindShop() {
        if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
            this.log('WebSocket 未连接，无法绑定商家', 'error');
            return;
        }
        
        const message = {
            type: 'shop_online',
            shop_id: this.shopId
        };
        
        this.ws.send(JSON.stringify(message));
        this.log(`发送商家绑定请求: shop_id = ${this.shopId}`);
    }
    
    /**
     * 启动心跳
     */
    startHeartbeat() {
        this.stopHeartbeat();
        this.heartbeatTimer = setInterval(() => {
            if (this.ws && this.ws.readyState === WebSocket.OPEN) {
                this.ws.send(JSON.stringify({ type: 'heartbeat' }));
            }
        }, this.heartbeatInterval);
    }
    
    /**
     * 停止心跳
     */
    stopHeartbeat() {
        if (this.heartbeatTimer) {
            clearInterval(this.heartbeatTimer);
            this.heartbeatTimer = null;
        }
    }
    
    /**
     * 处理重连
     */
    handleReconnect() {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            this.log('已达到最大重连次数，停止重连', 'error');
            this.updateStatus('failed');
            return;
        }
        
        this.reconnectAttempts++;
        this.log(`${this.reconnectDelay/1000} 秒后尝试重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
        
        setTimeout(() => {
            this.connect();
        }, this.reconnectDelay);
    }
    
    /**
     * 处理连接失败
     */
    handleConnectionFailure() {
        // 尝试下一个备用URL
        if (this.currentUrlIndex < this.backupUrls.length) {
            this.currentUrlIndex++;
            this.log(`尝试备用地址 ${this.currentUrlIndex}/${this.backupUrls.length}`);
            this.connect();
        } else {
            // 所有URL都失败了，重置并处理重连
            this.currentUrlIndex = 0;
            this.handleReconnect();
        }
    }
    
    /**
     * 获取当前要连接的URL
     */
    getCurrentUrl() {
        if (this.currentUrlIndex === 0) {
            return this.wsUrl;
        }
        return this.backupUrls[this.currentUrlIndex - 1];
    }
    
    /**
     * 断开连接
     */
    disconnect() {
        this.isManualClose = true;
        if (this.ws) {
            this.ws.close();
            this.ws = null;
        }
        this.stopHeartbeat();
        this.updateStatus('disconnected');
    }
    
    /**
     * 更新连接状态
     */
    updateStatus(status) {
        if (this.onStatusChange) {
            this.onStatusChange(status);
        }
    }
    
    /**
     * 默认的通知处理器
     */
    defaultNotificationHandler(notification) {
        console.log('[ShopWebSocket] 收到通知:', notification);
        
        // 如果浏览器支持，显示系统通知
        if ('Notification' in window && Notification.permission === 'granted') {
            new Notification(notification.title, {
                body: notification.content,
                icon: notification.icon || '/favicon.ico',
                tag: `notification-${notification.id}`,
                requireInteraction: true
            }).onclick = function() {
                if (notification.url) {
                    window.location.href = notification.url;
                }
                this.close();
            };
        }
        
        // 播放通知声音
        try {
            const audio = new Audio('https://jcstatics.jiaqingfu.com.cnuploads/audio/tomsg.mp3');
            audio.play().catch(e => console.log('播放声音失败:', e));
        } catch (e) {
            console.log('创建音频对象失败:', e);
        }
    }
    
    /**
     * 请求通知权限
     */
    static requestNotificationPermission() {
        if ('Notification' in window && Notification.permission === 'default') {
            Notification.requestPermission().then(permission => {
                console.log('通知权限:', permission);
            });
        }
    }
    
    /**
     * 日志输出
     */
    log(message, type = 'info') {
        if (!this.debug && type !== 'error') {
            return;
        }
        
        const timestamp = new Date().toLocaleTimeString();
        const prefix = `[ShopWebSocket ${timestamp}]`;
        
        switch (type) {
            case 'error':
                console.error(`${prefix} ${message}`);
                break;
            case 'warn':
                console.warn(`${prefix} ${message}`);
                break;
            default:
                console.log(`${prefix} ${message}`);
        }
    }
}

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ShopWebSocketNotification;
}
