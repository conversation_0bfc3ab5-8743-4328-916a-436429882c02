<?php
namespace app\api\controller;

use app\common\basics\Api;
use app\api\logic\AdLogic;
use app\common\server\JsonServer;
use think\facade\Db;

class Ad extends Api
{
    public $like_not_need_login = ['lists','getPositionList','newIndexAd','getWxCustomerService'];

    /**
     * 获取广告列表
     */
    public function lists()
    {
        $pid = $this->request->get('pid');
        $ad_sn = $this->request->get('ad_sn');
        $name = $this->request->get('name');
        $terminal = $this->request->get('terminal', '1');
        if ($pid || $ad_sn || $name) {
            $list = AdLogic::lists($pid, $terminal,$ad_sn,$name);
        } else {
            $list = [];
        }
        return JsonServer::success('', $list);
    }
    //获取企业微信客服配置
    public function getWxCustomerService()
    {
        $data=Db::name('ad')->field('remark as appid,link')->where('id',226)->find();
        $data['link']=$data['link']?explode(',', $data['link']):[];
        $data['appid']=$data['appid'];
        
        return JsonServer::success('', $data);
    }

    /**
     * 新首页,获取首页的广告图
     */
    public function newIndexAd()
    {
        $pid = $this->request->get('pid');
        $terminal = $this->request->get('terminal', '1');
        $list = AdLogic::lists2($pid, $terminal);
        return JsonServer::success('', $list);
    }


    /**
     * Notes:获取广告位列表
     * @param $terminal
     * @return array|\think\Model|null
     * @author: cjhao 2021/4/20 11:04
     */
    public function getPositionList(){

        $terminal = $this->request->get('terminal', '1');
        $list = AdLogic::getPositionList($terminal);

        return JsonServer::success('', $list);
    }
}