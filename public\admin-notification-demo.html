<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员通知触发器 - 使用示例</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: #fff;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #333;
            margin-top: 0;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"], textarea, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        textarea {
            height: 80px;
            resize: vertical;
        }
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: #45a049;
        }
        button.system {
            background-color: #2196F3;
        }
        button.system:hover {
            background-color: #0b7dda;
        }
        button.error {
            background-color: #f44336;
        }
        button.error:hover {
            background-color: #d32f2f;
        }
        button.warning {
            background-color: #FF9800;
        }
        button.warning:hover {
            background-color: #e68900;
        }
        button.info {
            background-color: #9C27B0;
        }
        button.info:hover {
            background-color: #7b1fa2;
        }
        .status {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 4px;
            margin-left: 10px;
            font-size: 12px;
        }
        .status.connected {
            background-color: #4CAF50;
            color: white;
        }
        .status.disconnected {
            background-color: #f44336;
            color: white;
        }
        .status.connecting {
            background-color: #FFC107;
            color: black;
        }
        .log-container {
            margin-top: 20px;
            border: 1px solid #ddd;
            padding: 10px;
            height: 200px;
            overflow-y: auto;
            background-color: #f9f9f9;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }
        .log-entry {
            margin-bottom: 2px;
            word-wrap: break-word;
        }
        .code-example {
            background-color: #f4f4f4;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
            font-family: monospace;
            font-size: 14px;
            overflow-x: auto;
        }
        .row {
            display: flex;
            margin: 0 -10px;
        }
        .col {
            flex: 1;
            padding: 0 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>管理员通知触发器 - 使用示例 
            <span id="status" class="status disconnected">未连接</span>
        </h1>

        <div class="form-group">
            <label>快速发送通知</label>
            <button onclick="sendQuickNotification('system')">系统通知</button>
            <button onclick="sendQuickNotification('error')" class="error">错误通知</button>
            <button onclick="sendQuickNotification('warning')" class="warning">警告通知</button>
            <button onclick="sendQuickNotification('info')" class="info">信息通知</button>
        </div>

        <hr>

        <div class="row">
            <div class="col">
                <div class="form-group">
                    <label for="title">通知标题</label>
                    <input type="text" id="title" value="测试通知" placeholder="请输入通知标题">
                </div>
            </div>
            <div class="col">
                <div class="form-group">
                    <label for="type">通知类型</label>
                    <select id="type">
                        <option value="admin_notification">默认通知</option>
                        <option value="system_notification">系统通知</option>
                        <option value="error_notification">错误通知</option>
                        <option value="warning_notification">警告通知</option>
                        <option value="info_notification">信息通知</option>
                    </select>
                </div>
            </div>
        </div>

        <div class="form-group">
            <label for="content">通知内容</label>
            <textarea id="content" placeholder="请输入通知内容">这是一条测试通知消息，用于验证通知系统是否正常工作。</textarea>
        </div>

        <div class="form-group">
            <label for="url">跳转链接（可选）</label>
            <input type="text" id="url" placeholder="https://example.com" value="">
        </div>

        <div class="form-group">
            <button onclick="sendCustomNotification()">发送自定义通知</button>
            <button onclick="checkConnectionStatus()" class="system">检查连接状态</button>
            <button onclick="reconnectWebSocket()" class="warning">重新连接</button>
            <button onclick="clearLog()" class="info">清空日志</button>
        </div>

        <div class="log-container" id="log"></div>

        <hr>

        <h3>使用方法</h3>
        
        <h4>1. 引入脚本文件</h4>
        <div class="code-example">
&lt;script src="/static/admin/js/admin-notification-trigger.js"&gt;&lt;/script&gt;
        </div>

        <h4>2. 基本使用</h4>
        <div class="code-example">
// 发送系统通知
AdminNotificationTrigger.sendSystemNotification('系统维护', '系统将在今晚进行维护，请提前保存工作。');

// 发送错误通知
AdminNotificationTrigger.sendErrorNotification('操作失败', '用户数据同步失败，请检查网络连接。');

// 发送自定义通知
AdminNotificationTrigger.sendNotification({
    type: 'admin_notification',
    title: '新订单提醒',
    content: '您有一个新的订单需要处理',
    url: '/admin/order/detail/123',
    icon: 1
});
        </div>

        <h4>3. 高级用法</h4>
        <div class="code-example">
// 初始化时指定管理员信息
AdminNotificationTrigger.init({
    adminId: 1,
    nickname: '超级管理员',
    token: 'your_admin_token'
}).then(() => {
    console.log('通知系统初始化成功');
}).catch(err => {
    console.error('初始化失败:', err);
});

// 检查连接状态
const status = AdminNotificationTrigger.getConnectionStatus();
console.log('连接状态:', status);

// 手动重连
AdminNotificationTrigger.reconnect();
        </div>

        <h4>4. Promise支持</h4>
        <div class="code-example">
AdminNotificationTrigger.sendSystemNotification('测试', '这是测试消息')
    .then(() => {
        console.log('通知发送成功');
    })
    .catch(err => {
        console.error('通知发送失败:', err);
    });
        </div>
    </div>

    <!-- 引入通知触发器 -->
    <script src="/static/admin/js/admin-notification-trigger.js"></script>

    <script>
        // 页面脚本
        const statusEl = document.getElementById('status');
        const logEl = document.getElementById('log');

        // 更新连接状态显示
        function updateStatus() {
            const status = AdminNotificationTrigger.getConnectionStatus();
            statusEl.textContent = getStatusText(status);
            statusEl.className = `status ${status}`;
        }

        function getStatusText(status) {
            const statusMap = {
                'connected': '已连接',
                'connecting': '连接中',
                'disconnected': '未连接',
                'closing': '断开中',
                'closed': '已关闭'
            };
            return statusMap[status] || status;
        }

        // 添加日志
        function addLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = 'log-entry';
            logEntry.textContent = `[${timestamp}] ${message}`;
            logEl.appendChild(logEntry);
            logEl.scrollTop = logEl.scrollHeight;
        }

        // 快速发送通知
        function sendQuickNotification(type) {
            const notifications = {
                system: {
                    title: '系统通知',
                    content: '系统运行正常，所有服务已启动。',
                    method: 'sendSystemNotification'
                },
                error: {
                    title: '错误警报',
                    content: '检测到系统异常，请立即处理！',
                    method: 'sendErrorNotification'
                },
                warning: {
                    title: '警告提醒',
                    content: '磁盘空间不足，建议清理无用文件。',
                    method: 'sendWarningNotification'
                },
                info: {
                    title: '信息提示',
                    content: '今日访问量已达到新高，系统表现良好。',
                    method: 'sendInfoNotification'
                }
            };

            const notification = notifications[type];
            if (!notification) return;

            addLog(`发送${notification.title}...`);
            
            AdminNotificationTrigger[notification.method](notification.title, notification.content)
                .then(() => {
                    addLog(`${notification.title}发送成功`);
                })
                .catch(err => {
                    addLog(`${notification.title}发送失败: ${err.message}`, 'error');
                });
        }

        // 发送自定义通知
        function sendCustomNotification() {
            const title = document.getElementById('title').value;
            const content = document.getElementById('content').value;
            const type = document.getElementById('type').value;
            const url = document.getElementById('url').value;

            if (!title || !content) {
                addLog('请填写标题和内容', 'error');
                return;
            }

            const notification = {
                type: type,
                title: title,
                content: content,
                url: url,
                icon: getIconByType(type)
            };

            addLog(`发送自定义通知: ${title}`);
            
            AdminNotificationTrigger.sendNotification(notification)
                .then(() => {
                    addLog('自定义通知发送成功');
                })
                .catch(err => {
                    addLog(`自定义通知发送失败: ${err.message}`, 'error');
                });
        }

        // 根据类型获取图标
        function getIconByType(type) {
            const iconMap = {
                'system_notification': 1,
                'error_notification': 2,
                'warning_notification': 3,
                'info_notification': 4,
                'admin_notification': 0
            };
            return iconMap[type] || 0;
        }

        // 检查连接状态
        function checkConnectionStatus() {
            const status = AdminNotificationTrigger.getConnectionStatus();
            addLog(`当前连接状态: ${getStatusText(status)}`);
            updateStatus();
        }

        // 重新连接
        function reconnectWebSocket() {
            addLog('正在重新连接...');
            AdminNotificationTrigger.reconnect()
                .then(() => {
                    addLog('重连成功');
                    updateStatus();
                })
                .catch(err => {
                    addLog(`重连失败: ${err.message}`, 'error');
                    updateStatus();
                });
        }

        // 清空日志
        function clearLog() {
            logEl.innerHTML = '';
        }

        // 定期更新状态
        setInterval(updateStatus, 2000);

        // 初始化
        updateStatus();
        addLog('页面加载完成，通知系统正在初始化...');
    </script>
</body>
</html>