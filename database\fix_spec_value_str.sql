-- =====================================================
-- spec_value_str字段修复SQL脚本
-- 用于修复goods_item表中可能存在的spec_value_str字段问题
-- =====================================================

-- 1. 检查当前数据状态
SELECT 
    '数据检查报告' as report_type,
    COUNT(*) as total_items,
    COUNT(CASE WHEN spec_value_str IS NULL OR spec_value_str = '' THEN 1 END) as empty_spec_value_str,
    COUNT(CASE WHEN spec_value_str IS NOT NULL AND spec_value_str != '' THEN 1 END) as valid_spec_value_str
FROM ls_goods_item;

-- 2. 查找有问题的记录
SELECT 
    gi.id,
    gi.goods_id,
    gi.spec_value_ids,
    gi.spec_value_str,
    g.name as goods_name,
    g.spec_type
FROM ls_goods_item gi
LEFT JOIN ls_goods g ON gi.goods_id = g.id
WHERE gi.spec_value_str IS NULL OR gi.spec_value_str = ''
ORDER BY gi.goods_id, gi.id;

-- 3. 修复单规格商品的spec_value_str字段
UPDATE ls_goods_item gi
INNER JOIN ls_goods g ON gi.goods_id = g.id
SET gi.spec_value_str = '默认'
WHERE (gi.spec_value_str IS NULL OR gi.spec_value_str = '')
  AND g.spec_type = 1;

-- 4. 修复多规格商品的spec_value_str字段
-- 这个需要根据spec_value_ids来重建spec_value_str
UPDATE ls_goods_item gi
SET gi.spec_value_str = (
    SELECT GROUP_CONCAT(gsv.value ORDER BY gsv.spec_id SEPARATOR ',')
    FROM ls_goods_spec_value gsv
    WHERE FIND_IN_SET(gsv.id, gi.spec_value_ids) > 0
      AND gsv.goods_id = gi.goods_id
)
WHERE (gi.spec_value_str IS NULL OR gi.spec_value_str = '')
  AND gi.spec_value_ids IS NOT NULL 
  AND gi.spec_value_ids != '';

-- 5. 处理仍然为空的记录（设置为默认值）
UPDATE ls_goods_item 
SET spec_value_str = '默认'
WHERE spec_value_str IS NULL OR spec_value_str = '';

-- 6. 验证修复结果
SELECT 
    '修复后数据检查' as report_type,
    COUNT(*) as total_items,
    COUNT(CASE WHEN spec_value_str IS NULL OR spec_value_str = '' THEN 1 END) as empty_spec_value_str,
    COUNT(CASE WHEN spec_value_str IS NOT NULL AND spec_value_str != '' THEN 1 END) as valid_spec_value_str
FROM ls_goods_item;

-- 7. 显示修复后的示例数据
SELECT 
    gi.id,
    gi.goods_id,
    gi.spec_value_ids,
    gi.spec_value_str,
    g.name as goods_name,
    g.spec_type
FROM ls_goods_item gi
LEFT JOIN ls_goods g ON gi.goods_id = g.id
ORDER BY gi.goods_id, gi.id
LIMIT 10;

-- =====================================================
-- 预防性措施：添加约束和索引
-- =====================================================

-- 8. 确保spec_value_str字段不为NULL（如果需要）
-- ALTER TABLE ls_goods_item MODIFY COLUMN spec_value_str varchar(255) NOT NULL DEFAULT '';

-- 9. 添加索引以提高查询性能（如果不存在）
-- CREATE INDEX idx_goods_item_spec_value_str ON ls_goods_item(spec_value_str);
-- CREATE INDEX idx_goods_item_goods_id_spec ON ls_goods_item(goods_id, spec_value_str);

-- =====================================================
-- 数据完整性检查
-- =====================================================

-- 10. 检查是否还有孤立的商品项（商品已删除但item还存在）
SELECT 
    gi.id,
    gi.goods_id,
    gi.spec_value_str,
    'orphaned_item' as issue_type
FROM ls_goods_item gi
LEFT JOIN ls_goods g ON gi.goods_id = g.id
WHERE g.id IS NULL;

-- 11. 检查规格值ID与规格值字符串是否匹配
SELECT 
    gi.id,
    gi.goods_id,
    gi.spec_value_ids,
    gi.spec_value_str,
    GROUP_CONCAT(gsv.value ORDER BY gsv.spec_id SEPARATOR ',') as calculated_spec_value_str,
    'mismatch' as issue_type
FROM ls_goods_item gi
LEFT JOIN ls_goods_spec_value gsv ON FIND_IN_SET(gsv.id, gi.spec_value_ids) > 0 AND gsv.goods_id = gi.goods_id
WHERE gi.spec_value_ids IS NOT NULL 
  AND gi.spec_value_ids != ''
  AND gi.spec_value_str != ''
GROUP BY gi.id, gi.goods_id, gi.spec_value_ids, gi.spec_value_str
HAVING gi.spec_value_str != calculated_spec_value_str
LIMIT 10;

-- =====================================================
-- 使用说明
-- =====================================================
/*
使用步骤：
1. 备份数据库（重要！）
2. 执行查询1-2，查看当前数据状态
3. 执行更新语句3-5，修复数据
4. 执行查询6-7，验证修复结果
5. 可选：执行查询10-11，进行完整性检查

注意事项：
- 在生产环境执行前请先在测试环境验证
- 建议在低峰期执行
- 执行前务必备份数据库
- 如果数据量很大，可以分批执行更新操作
*/
