<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>可拖动排序的图片上传组件演示</title>
    <link rel="stylesheet" href="/static/lib/layui/css/layui.css">
    <link rel="stylesheet" href="/static/common/css/sortable-image-upload.css">
    <style>
        body {
            padding: 20px;
            background-color: #f5f5f5;
            font-family: Arial, sans-serif;
        }
        .demo-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .demo-title {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 24px;
        }
        .demo-section {
            margin-bottom: 40px;
        }
        .section-title {
            font-size: 18px;
            color: #333;
            margin-bottom: 15px;
            border-left: 4px solid #1890ff;
            padding-left: 10px;
        }
        .feature-list {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        .feature-list ul {
            margin: 0;
            padding-left: 20px;
        }
        .feature-list li {
            margin-bottom: 8px;
            color: #666;
        }
        .upload-demo {
            border: 1px solid #e6e6e6;
            border-radius: 8px;
            padding: 20px;
            background: #fafafa;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #333;
        }
        .result-display {
            margin-top: 20px;
            padding: 15px;
            background: #f0f8ff;
            border-radius: 6px;
            border: 1px solid #d6e4ff;
        }
        .result-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #1890ff;
        }
        .result-content {
            font-family: monospace;
            font-size: 12px;
            color: #666;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1 class="demo-title">可拖动排序的图片上传组件演示</h1>
        
        <div class="demo-section">
            <h2 class="section-title">功能特性</h2>
            <div class="feature-list">
                <ul>
                    <li>✅ 支持多图片上传</li>
                    <li>✅ 拖拽排序功能</li>
                    <li>✅ 图片预览功能</li>
                    <li>✅ 序号显示</li>
                    <li>✅ 删除确认</li>
                    <li>✅ 响应式设计</li>
                    <li>✅ 兼容原有上传组件</li>
                </ul>
            </div>
        </div>

        <div class="demo-section">
            <h2 class="section-title">基础演示</h2>
            <div class="upload-demo">
                <form class="layui-form">
                    <div class="form-group">
                        <label class="form-label">商品轮播图（最多5张）：</label>
                        <div class="like-upload-image">
                            <div class="upload-image-elem">
                                <a class="add-upload-image" id="demo-upload">+ 添加图片</a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <button type="button" class="layui-btn" onclick="getUploadedImages()">
                            获取上传结果
                        </button>
                        <button type="button" class="layui-btn layui-btn-primary" onclick="clearImages()">
                            清空图片
                        </button>
                    </div>
                </form>
                
                <div class="result-display" id="result-display" style="display: none;">
                    <div class="result-title">上传结果：</div>
                    <div class="result-content" id="result-content"></div>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h2 class="section-title">使用说明</h2>
            <div class="feature-list">
                <h4>1. 引入必要文件：</h4>
                <pre><code>&lt;link rel="stylesheet" href="/static/common/css/sortable-image-upload.css"&gt;
&lt;script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"&gt;&lt;/script&gt;
&lt;script src="/static/common/js/sortable-image-upload.js"&gt;&lt;/script&gt;</code></pre>
                
                <h4>2. HTML结构：</h4>
                <pre><code>&lt;div class="like-upload-image"&gt;
    &lt;div class="upload-image-elem"&gt;
        &lt;a class="add-upload-image" id="your-upload-btn"&gt;+ 添加图片&lt;/a&gt;
    &lt;/div&gt;
&lt;/div&gt;</code></pre>
                
                <h4>3. JavaScript调用：</h4>
                <pre><code>$(document).on("click", "#your-upload-btn", function () {
    like.sortableImageUpload({
        limit: 5,
        field: "images[]",
        that: $(this),
        content: '/admin/file/lists?type=10',
        sortable: true,
        showOrder: true
    });
});</code></pre>
            </div>
        </div>
    </div>

    <script src="/static/lib/layui/layui.js"></script>
    <script src="/static/admin/js/jquery.min.js"></script>
    <script src="/static/admin/js/function.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
    <script src="/static/common/js/sortable-image-upload.js"></script>

    <script>
        layui.use(['layer'], function(){
            var layer = layui.layer;
            
            // 模拟图片上传功能（演示用）
            $(document).on("click", "#demo-upload", function () {
                // 模拟选择图片
                var mockImages = [
                    '/static/common/image/default/goods_image.png',
                    '/static/common/image/default/shop_admin_logo.png',
                    '/static/common/image/default/platform_admin_logo.png'
                ];
                
                // 随机选择1-3张图片
                var selectedCount = Math.floor(Math.random() * 3) + 1;
                var selectedImages = mockImages.slice(0, selectedCount);
                
                // 模拟添加图片
                var that = $(this);
                selectedImages.forEach(function(url, index) {
                    setTimeout(function() {
                        addMockImage(url, that);
                    }, index * 200);
                });
                
                // 初始化排序功能
                setTimeout(function() {
                    initMockSortable(that);
                }, selectedImages.length * 200 + 100);
            });
            
            // 删除上传文件功能
            like.delUpload();
        });
        
        // 模拟添加图片
        function addMockImage(url, that) {
            var template = '<div class="sortable-image-item" data-url="' + url + '">';
            template += '<div class="image-wrapper">';
            template += '<img src="' + url + '" alt="img" class="uploaded-image">';
            template += '<input type="hidden" name="images[]" value="' + url + '">';
            template += '<div class="image-controls">';
            template += '<span class="image-order">1</span>';
            template += '<div class="image-actions">';
            template += '<button type="button" class="btn-preview" title="预览"><i class="layui-icon layui-icon-search"></i></button>';
            template += '<button type="button" class="btn-delete" title="删除"><i class="layui-icon layui-icon-delete"></i></button>';
            template += '</div>';
            template += '</div>';
            template += '<div class="drag-handle" title="拖动排序"><i class="layui-icon layui-icon-slider"></i></div>';
            template += '</div>';
            template += '</div>';
            
            that.parent().before(template);
        }
        
        // 初始化模拟排序
        function initMockSortable(that) {
            var container = that.closest('.like-upload-image').parent();
            
            if (container.find('.sortable-container').length === 0) {
                var sortableContainer = $('<div class="sortable-container"></div>');
                container.find('.sortable-image-item').appendTo(sortableContainer);
                container.prepend(sortableContainer);
                
                if (typeof Sortable !== 'undefined') {
                    new Sortable(sortableContainer[0], {
                        animation: 150,
                        handle: '.drag-handle',
                        ghostClass: 'sortable-ghost',
                        chosenClass: 'sortable-chosen',
                        dragClass: 'sortable-drag',
                        onEnd: function(evt) {
                            updateMockOrder(container);
                        }
                    });
                }
            }
            
            updateMockOrder(container);
        }
        
        // 更新序号
        function updateMockOrder(container) {
            container.find('.sortable-image-item').each(function(index) {
                $(this).find('.image-order').text(index + 1);
            });
        }
        
        // 获取上传结果
        function getUploadedImages() {
            var images = [];
            $('input[name="images[]"]').each(function() {
                images.push($(this).val());
            });
            
            var result = {
                count: images.length,
                images: images
            };
            
            $('#result-content').text(JSON.stringify(result, null, 2));
            $('#result-display').show();
        }
        
        // 清空图片
        function clearImages() {
            $('.sortable-container').remove();
            $('.sortable-image-item').remove();
            $('#result-display').hide();
        }
    </script>
</body>
</html>
