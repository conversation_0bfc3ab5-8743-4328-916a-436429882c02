<?php
namespace app\admin\logic;

use app\common\basics\Logic;
use app\common\model\MassMessageLimit;
use app\common\model\ShopPurchaserAllocation;
use app\common\model\Shop;
use think\facade\Db;

/**
 * 采购人员分配逻辑层
 * Class PurchaserAllocationLogic
 * @package app\admin\logic
 */
class PurchaserAllocationLogic extends Logic
{
    /**
     * 为商家自动分配采购人员
     * @param int $shopId 商家ID
     * @param int $tierLevel 商家等级
     * @return bool
     */
    public static function autoAllocatePurchasers($shopId, $tierLevel)
    {
        try {
            // 获取分配配置
            $config = MassMessageLimit::getPurchaserAllocationConfig($tierLevel);
            if ($config['total_count'] <= 0) {
                return true; // 无需分配
            }
            
            // 清除现有分配
            ShopPurchaserAllocation::clearShopAllocation($shopId);
            
            $allocatedUsers = [];
            
            // 分配1级用户
            if ($config['level1_count'] > 0) {
                $level1Users = ShopPurchaserAllocation::getAvailablePurchasers($shopId, 1, $config['level1_count']);
                $allocatedUsers = array_merge($allocatedUsers, array_column($level1Users, 'id'));
            }
            
            // 分配2级用户
            if ($config['level2_count'] > 0) {
                $level2Users = ShopPurchaserAllocation::getAvailablePurchasers($shopId, 2, $config['level2_count']);
                $allocatedUsers = array_merge($allocatedUsers, array_column($level2Users, 'id'));
            }
            
            // 分配3级用户
            if ($config['level3_count'] > 0) {
                $level3Users = ShopPurchaserAllocation::getAvailablePurchasers($shopId, 3, $config['level3_count']);
                $allocatedUsers = array_merge($allocatedUsers, array_column($level3Users, 'id'));
            }
            
            // 执行分配
            if (!empty($allocatedUsers)) {
                return ShopPurchaserAllocation::allocatePurchasers($shopId, $allocatedUsers, $tierLevel);
            }
            
            return true;
            
        } catch (\Exception $e) {
            self::$error = $e->getMessage();
            return false;
        }
    }

    /**
     * 检查并执行年度重新分配
     * @return bool
     */
    public static function checkAndReAllocate()
    {
        try {
            $currentYear = date('Y');
            $lastYear = $currentYear - 1;
            
            // 获取去年有分配记录但今年没有的商家
            $shopsNeedReAllocation = Db::query("
                SELECT DISTINCT s.id, s.tier_level 
                FROM ls_shop s 
                INNER JOIN ls_shop_purchaser_allocation spa ON s.id = spa.shop_id 
                WHERE spa.allocation_year = ? 
                AND s.id NOT IN (
                    SELECT DISTINCT shop_id 
                    FROM ls_shop_purchaser_allocation 
                    WHERE allocation_year = ? AND status = 1
                )
                AND s.del = 0
            ", [$lastYear, $currentYear]);
            
            foreach ($shopsNeedReAllocation as $shop) {
                self::autoAllocatePurchasers($shop['id'], $shop['tier_level']);
            }
            
            return true;
            
        } catch (\Exception $e) {
            self::$error = $e->getMessage();
            return false;
        }
    }

    /**
     * 获取商家采购人员列表
     * @param int $shopId 商家ID
     * @param int $page 页码
     * @param int $limit 每页数量
     * @return array
     */
    public static function getShopPurchaserList($shopId, $page = 1, $limit = 20)
    {
        $list = ShopPurchaserAllocation::getShopPurchaserList($shopId);
        
        // 分页处理
        $total = count($list);
        $offset = ($page - 1) * $limit;
        $list = array_slice($list, $offset, $limit);
        
        // 添加等级名称
        foreach ($list as &$item) {
            $item['level_name'] = self::getUserLevelName($item['user_level']);
        }
        
        return [
            'list' => $list,
            'total' => $total,
            'page' => $page,
            'limit' => $limit
        ];
    }

    /**
     * 获取用户等级名称
     * @param int $level
     * @return string
     */
    public static function getUserLevelName($level)
    {
        $names = [
            1 => '低活跃度',
            2 => '中活跃度',
            3 => '高活跃度'
        ];
        
        return $names[$level] ?? '未知等级';
    }

    /**
     * 获取商家分配统计
     * @param int $shopId 商家ID
     * @return array
     */
    public static function getShopAllocationStats($shopId)
    {
        $stats = ShopPurchaserAllocation::getShopAllocationStats($shopId);
        
        // 获取商家等级和配置
        $shop = Shop::find($shopId);
        if ($shop) {
            $config = MassMessageLimit::getPurchaserAllocationConfig($shop->tier_level);
            $stats['config'] = $config;
            $stats['tier_level'] = $shop->tier_level;
            $stats['tier_name'] = MassMessageLimit::getTierLevelName($shop->tier_level);
        }
        
        return $stats;
    }

    /**
     * 手动重新分配采购人员
     * @param int $shopId 商家ID
     * @return bool
     */
    public static function manualReAllocate($shopId)
    {
        $shop = Shop::find($shopId);
        if (!$shop) {
            self::$error = '商家不存在';
            return false;
        }
        
        return self::autoAllocatePurchasers($shopId, $shop->tier_level);
    }

    /**
     * 获取分配配置列表（用于后台管理）
     * @return array
     */
    public static function getAllocationConfigList()
    {
        $list = MassMessageLimit::getList();
        
        foreach ($list as &$item) {
            $item['tier_name'] = MassMessageLimit::getTierLevelName($item['tier_level']);
            
            // 计算各等级分配数量
            $totalCount = $item['total_purchaser_count'];
            $item['level1_count'] = intval($totalCount * $item['level1_percent'] / 100);
            $item['level2_count'] = intval($totalCount * $item['level2_percent'] / 100);
            $item['level3_count'] = intval($totalCount * $item['level3_percent'] / 100);
        }
        
        return $list;
    }

    /**
     * 更新分配配置
     * @param array $data
     * @return bool
     */
    public static function updateAllocationConfig($data)
    {
        // 验证数据
        if (!isset($data['id']) || !isset($data['tier_level'])) {
            self::$error = '参数错误';
            return false;
        }
        
        // 验证百分比总和
        $totalPercent = ($data['level1_percent'] ?? 0) + ($data['level2_percent'] ?? 0) + ($data['level3_percent'] ?? 0);
        if (abs($totalPercent - 100) > 0.01) {
            self::$error = '各等级用户占比总和必须为100%';
            return false;
        }
        
        return MassMessageLimit::edit($data);
    }
}
