{layout name="layout2" /}
<style>
    .layui-form-label {
        color: #6a6f6c;
        width: 100px;
    }
    .layui-input-block {
        margin-left: 130px;
    }
    .tips{
        color: red;
    }
    .goods{
        display: none;
    }
</style>
<div class="layui-form" lay-filter="layuiadmin-form-user_level" id="layuiadmin-form-user_level" style="padding: 20px 30px 0 0;">
    <div class="layui-form-item">
        <label class="layui-form-label"><span class="tips">*</span>参与日期</label>
        <div class="layui-input-inline">
            <input type="text" class="layui-input" id="start_end" placeholder="选择日期" autocomplete="off" name="start_end">
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label"><span class="tips">*</span>参与时段</label>
        <div class="layui-input-inline">
            <select name="seckill_id">
                <option value="">选择时段</option>
                {foreach $seckill as $item}
                <option value="{$item.id}">{$item.time}</option>
                {/foreach}
            </select>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label"><span class="tips">*</span>秒杀商品</label>
        <div class="layui-input-block">
            <a class="layui-btn layui-btn-normal select-goods" >选择秒杀商品</a>
        </div>
    </div>
    <div class="layui-form-item goods">
        <label class="layui-form-label"></label>
        <div class="layui-input-block ">
            <table id="goods_list" class="layui-table" lay-size="sm">
                <colgroup>
                    <col width="40px">
                </colgroup>
                <thead>
                <tr style="background-color: #f3f5f9">
                    <th style="width: 120px;text-align: center">商品</th>
                    <th style="width: 120px;text-align: center">商品规格</th>
                    <th style="width: 60px;text-align: center">商品价格</th>
                    <th style="width: 40px;text-align: center">秒杀价格</th>
                </tr>
                </thead>
                <tbody>
                </tbody>
            </table>
        </div>
    </div>
    <div class="layui-form-item layui-hide">
        <input type="button" lay-submit lay-filter="add-seckill-submit" id="add-seckill-submit" value="确认">
    </div>
</div>
<style>
    .layui-form-label {
        width: 100px;
    }
    .layui-input-block {
        margin-left: 130px;
    }
</style>
<script>
    layui.config({
        version:"{$front_version}",
        base: '/static/lib/' //静态资源所在路径
    }).use(['form', 'laydate'], function(){
        var $ = layui.$
            ,form = layui.form
            ,laydate = layui.laydate
            ,goods_ids = [];

        laydate.render({
            elem: '#start_end' //指定元素
            ,range: '~'
        });

        $(document).on('click','.select-goods',function () {
            layer.open({
                type: 2
                ,title: '选择商品'
                ,content: '{:url("common/selectGoodsTwo")}'
                ,area: ['90%', '90%']
                ,btn: ['确认', '取消']
                ,yes: function(index, layero){
                    var data = window["layui-layer-iframe" + index].callbackdata();
                    if(data.length){
                        goods_ids = [];
                        $('#goods_list tbody').empty();
                    }
                    data.forEach(function(item,index,arr) {
                        var image = item.image;
                        for(var i = 0; item.goods_item.length > i;i++){
                            var goods_item = item.goods_item[i];
                            if(goods_ids.indexOf(goods_item.id) == -1) {

                                goods_ids.push(goods_item.id);

                                if(item.goods_item[i].image) {
                                    image = item.goods_item[i].image;
                                }
                                var goods_html = '<tr>\n' +
                                    '                    <td style="text-align: left"><img class="image-show" width="80px" height="80px" src="'+image +'">'+item.name+'</td>\n' +
                                    '                    <td style="text-align: center">'+goods_item.spec_value_str+'</td>\n' +
                                    '                    <td style="text-align: center">'+goods_item.price+'</td>\n' +
                                    '                    <td style="width: 40px;">  ' +
                                    '<input type="hidden" name="goods_id[]" value='+ item.id+'>'+
                                    '<input type="hidden" name="item_id[]" value='+ item.goods_item[i].id+'>'+
                                    '<input type="number"  lay-verify="required" lay-reqtext="请输入秒杀价格" name=price[] autocomplete="off" class="layui-input"></td>\n' +
                                    '                </tr>';
                                $('#goods_list').append(goods_html);
                            }
                        }
                    })
                    $('.goods').show();

                }

            })
        })
        //图片放大
        $(document).on('click', '.image-show', function () {
            var src = $(this).attr('src');
            like.showImg(src,350);
        });

    })

</script>