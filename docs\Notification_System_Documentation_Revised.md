# 通知系统技术文档 (修正版)

**版本**: 1.1
**最后更新**: 2025-07-10

## 目录
1.  [概述](#1-概述)
2.  [架构图](#2-架构图)
3.  [WebSocket 处理器](#3-websocket-处理器)
4.  [核心服务](#4-核心服务)
5.  [数据流](#5-数据流)
6.  [事件类型](#6-事件类型)
7.  [部署要求](#7-部署要求)
8.  [待办/未来增强功能](#8-待办未来增强功能)

---

### 1. 概述

通知系统建立在WebSocket架构之上，旨在为管理员和店铺提供实时更新。它并行使用两个独立的WebSocket基础设施来处理不同类型的事件：

*   **Swoole**: 用于处理管理员和客服相关的通知，其逻辑由 `app/common/server/swoole/CombinedHandler.php` 文件定义。
*   **GatewayWorker**: 用于处理店铺和用户相关的事件（如订单更新），其逻辑由 `app/common/server/gateway/Events.php` 文件定义。

这两个系统独立运行，没有直接的委托关系。

### 2. 架构图

```
+---------------------------+       +---------------------------------+
|      Swoole Server        |       |      GatewayWorker Server       |
| (For Admins & Support)    |       | (For Shops & Users)             |
+---------------------------+       +---------------------------------+
|   - CombinedHandler.php   |       |   - Events.php                  |
|   - Binds to 'admin_group'|       |   - Binds to 'shop_group_{id}'  |
+-------------+-------------+       +------------------+--------------+
              |                                        |
+-------------v-------------+       +------------------v--------------+
|   NotificationService.php   |       |      Business Logic (e.g.,     |
|   (adminNotify method)      |       |      OrderController)           |
+---------------------------+       +---------------------------------+
```

### 3. WebSocket 处理器

#### 3.1 Swoole (管理员通知)

*   **处理器**: `app/common/server/swoole/CombinedHandler.php`
*   **描述**: 此处理器负责处理面向管理员的通知，例如系统警报或新的客服工单。
*   **注意**: 当前实现将所有管理员通知**广播**到 `'admin_group'` 组，并**忽略** `target_user_ids` 参数。定点推送功能尚未实现。

#### 3.2 GatewayWorker (店铺通知)

*   **处理器**: `app/common/server/gateway/Events.php`
*   **描述**: 此处理器处理与店铺活动相关的事件，例如新订单或库存更新。
*   **端口**: `8282` (**待验证**)。此端口号在文档中被提及，但无法在代码库中得到确认。需要检查实际的服务器部署启动脚本来核实。

### 4. 核心服务

*   **`NotificationService.php`**:
    *   **位置**: `app/common/service/NotificationService.php`
    *   **职责**: 这是一个核心服务类，封装了发送通知的逻辑。它提供了 `adminNotify` 和 `shopNotify` 等方法，供业务逻辑层调用以触发WebSocket事件。

### 5. 数据流

1.  **管理员通知**:
    *   业务逻辑 (例如 `app/admin/controller/Notification.php`) 调用 `NotificationService::adminNotify()`。
    *   `adminNotify()` 通过Redis发布/订阅机制将消息推送到一个频道。
    *   `CombinedHandler.php` (Swoole) 订阅该频道，接收到消息后，将其广播给 `'admin_group'` 中的所有管理员客户端。

2.  **店铺通知**:
    *   业务逻辑 (例如订单处理模块) 调用 `NotificationService::shopNotify()`。
    *   `shopNotify()` 直接与GatewayWorker的`Gateway`类交互，将消息推送到特定的店铺群组 (例如 `'shop_group_123'`)。
    *   `Events.php` (GatewayWorker) 处理客户端的连接、消息和断开连接事件。

### 6. 事件类型

*   `admin_notification`: 通用的管理员通知。
*   `shop_new_order`: 发送给店铺的新订单通知。
*   `user_message`: 用户发送给客服的消息。
*   `support_reply`: 客服回复给用户的消息。

### 7. 部署要求

*   **PHP**: >= 7.2
*   **框架**: ThinkPHP 6.x
*   **扩展**:
    *   `swoole`: 用于运行管理员通知服务器。
    *   `redis`: 用于消息队列和发布/订阅。
*   **依赖**:
    *   `workerman/gateway-worker`: 用于运行店铺通知服务器。

### 8. 待办/未来增强功能

*   **实现对特定管理员的定点通知**:
    *   **当前状态**: 系统目前将所有管理员通知广播到 `'admin_group'` 组。`NotificationService.php` 中的 `adminNotify` 方法虽然接受 `target_user_ids` 参数，但该参数在 `CombinedHandler.php` 的推送逻辑中被忽略。
    *   **所需修改**:
        1.  修改 `CombinedHandler.php` 中的推送逻辑，使其能够根据 `target_user_ids` 查找并向特定的管理员客户端 `fd` 推送消息，而不是向固定的群组广播。
        2.  可能需要调整 `NotificationService.php` 以更好地与新的推送逻辑集成。