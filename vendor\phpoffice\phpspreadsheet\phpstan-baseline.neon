parameters:
	ignoreErrors:
		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Calculation\\:\\:\\$returnArrayAsType has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Calculation.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Calculation\\:\\:\\$branchPruningEnabled has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Calculation.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Calculation\\:\\:\\$cellStack has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Calculation.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Calculation\\:\\:\\$cyclicFormulaCell has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Calculation.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Calculation\\:\\:\\$localeFunctions has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Calculation.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Calculation\\:\\:\\$phpSpreadsheetFunctions has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Calculation.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Calculation\\:\\:\\$controlFunctions has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Calculation.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Calculation\\:\\:\\$spreadsheet \\(PhpOffice\\\\PhpSpreadsheet\\\\Spreadsheet\\) does not accept PhpOffice\\\\PhpSpreadsheet\\\\Spreadsheet\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Calculation.php

		-
			message: "#^Argument of an invalid type array\\<int, string\\>\\|false supplied for foreach, only iterables are supported\\.$#"
			count: 3
			path: src/PhpSpreadsheet/Calculation/Calculation.php

		-
			message: "#^Parameter \\#3 \\$formula of static method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Calculation\\:\\:translateSeparator\\(\\) expects string, string\\|null given\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Calculation/Calculation.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Calculation\\:\\:\\$functionReplaceFromExcel has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Calculation.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Calculation\\:\\:\\$functionReplaceToLocale has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Calculation.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Calculation\\:\\:_translateFormulaToLocale\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Calculation.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Calculation\\:\\:_translateFormulaToLocale\\(\\) has parameter \\$formula with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Calculation.php

		-
			message: "#^Parameter \\#1 \\$str of function preg_quote expects string, int\\|string given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Calculation.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Calculation\\:\\:\\$functionReplaceFromLocale has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Calculation.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Calculation\\:\\:\\$functionReplaceToExcel has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Calculation.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Calculation\\:\\:_translateFormulaToEnglish\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Calculation.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Calculation\\:\\:_translateFormulaToEnglish\\(\\) has parameter \\$formula with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Calculation.php

		-
			message: "#^Parameter \\#1 \\$str of function trim expects string, int\\|string given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Calculation.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Calculation\\:\\:localeFunc\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Calculation.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Calculation\\:\\:localeFunc\\(\\) has parameter \\$function with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Calculation.php

		-
			message: "#^Cannot call method getCell\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\Worksheet\\|null\\.$#"
			count: 6
			path: src/PhpSpreadsheet/Calculation/Calculation.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Calculation\\:\\:\\$operatorAssociativity has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Calculation.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Calculation\\:\\:\\$comparisonOperators has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Calculation.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Calculation\\:\\:\\$operatorPrecedence has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Calculation.php

		-
			message: "#^Strict comparison using \\=\\=\\= between mixed and null will always evaluate to false\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Calculation/Calculation.php

		-
			message: "#^Parameter \\#1 \\$haystack of function stripos expects string, float\\|int\\|string given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Calculation.php

		-
			message: "#^Parameter \\#1 \\$haystack of function strpos expects string, float\\|int\\|string given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Calculation.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Calculation\\:\\:dataTestReference\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Calculation.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Calculation\\:\\:dataTestReference\\(\\) has parameter \\$operandData with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Calculation.php

		-
			message: "#^Cannot call method getTitle\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\Worksheet\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Calculation.php

		-
			message: "#^Cannot call method getColumn\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Cell\\\\Cell\\|null\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Calculation/Calculation.php

		-
			message: "#^Parameter \\#1 \\$str of function trim expects string, null given\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Calculation/Calculation.php

		-
			message: "#^Cannot call method getCoordinate\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Cell\\\\Cell\\|null\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Calculation/Calculation.php

		-
			message: "#^Cannot call method getRow\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Cell\\\\Cell\\|null\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Calculation/Calculation.php

		-
			message: "#^Cannot call method has\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Collection\\\\Cells\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Calculation.php

		-
			message: "#^Parameter \\#1 \\$parent of method PhpOffice\\\\PhpSpreadsheet\\\\Cell\\\\Cell\\:\\:attach\\(\\) expects PhpOffice\\\\PhpSpreadsheet\\\\Collection\\\\Cells, PhpOffice\\\\PhpSpreadsheet\\\\Collection\\\\Cells\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Calculation.php

		-
			message: "#^Cannot call method attach\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Cell\\\\Cell\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Calculation.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Calculation\\:\\:validateBinaryOperand\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Calculation.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Calculation\\:\\:validateBinaryOperand\\(\\) has parameter \\$operand with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Calculation.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Calculation\\:\\:validateBinaryOperand\\(\\) has parameter \\$stack with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Calculation.php

		-
			message: "#^Parameter \\#1 \\$textValue of static method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\StringHelper\\:\\:strCaseReverse\\(\\) expects string, string\\|null given\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Calculation/Calculation.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Calculation\\:\\:raiseFormulaError\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Calculation.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Calculation\\:\\:raiseFormulaError\\(\\) has parameter \\$errorMessage with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Calculation.php

		-
			message: "#^Cannot call method cellExists\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\Worksheet\\|null\\.$#"
			count: 4
			path: src/PhpSpreadsheet/Calculation/Calculation.php

		-
			message: "#^Parameter \\#2 \\$worksheet of static method PhpOffice\\\\PhpSpreadsheet\\\\DefinedName\\:\\:resolveName\\(\\) expects PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\Worksheet, PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\Worksheet\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Calculation.php

		-
			message: "#^Cannot call method getHighestRow\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\Worksheet\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Calculation.php

		-
			message: "#^Cannot call method getHighestColumn\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\Worksheet\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Calculation.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Calculation\\:\\:getUnusedBranchStoreKey\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Calculation.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Calculation\\:\\:getTokensAsString\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Calculation.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Calculation\\:\\:getTokensAsString\\(\\) has parameter \\$tokens with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Calculation.php

		-
			message: "#^Parameter \\#2 \\$field of static method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Database\\\\DCountA\\:\\:evaluate\\(\\) expects int\\|string, int\\|string\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Database.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Database\\:\\:DMAX\\(\\) should return float but returns float\\|string\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Database.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Database\\:\\:DMIN\\(\\) should return float but returns float\\|string\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Database.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Database\\:\\:DPRODUCT\\(\\) should return float\\|string but returns float\\|string\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Database.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Database\\:\\:DSTDEV\\(\\) should return float\\|string but returns float\\|string\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Database.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Database\\:\\:DSTDEVP\\(\\) should return float\\|string but returns float\\|string\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Database.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Database\\:\\:DSUM\\(\\) should return float\\|string but returns float\\|string\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Database.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Database\\:\\:DVAR\\(\\) should return float\\|string but returns float\\|string\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Database.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Database\\:\\:DVARP\\(\\) should return float\\|string but returns float\\|string\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Database.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Database\\\\DatabaseAbstract\\:\\:evaluate\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Database/DatabaseAbstract.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Database\\\\DatabaseAbstract\\:\\:evaluate\\(\\) has parameter \\$criteria with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Database/DatabaseAbstract.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Database\\\\DatabaseAbstract\\:\\:evaluate\\(\\) has parameter \\$database with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Database/DatabaseAbstract.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Database\\\\DatabaseAbstract\\:\\:evaluate\\(\\) has parameter \\$field with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Database/DatabaseAbstract.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Database\\\\DatabaseAbstract\\:\\:buildCondition\\(\\) has parameter \\$criterion with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Database/DatabaseAbstract.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Database\\\\DatabaseAbstract\\:\\:processCondition\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Database/DatabaseAbstract.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Engine\\\\Logger\\:\\:writeDebugLog\\(\\) has parameter \\$args with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Engine/Logger.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Engineering\\:\\:BITLSHIFT\\(\\) should return int\\|string but returns float\\|int\\|string\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Engineering.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Engineering\\:\\:BITRSHIFT\\(\\) should return int\\|string but returns float\\|int\\|string\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Engineering.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Engineering\\\\BesselJ\\:\\:besselj2a\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Engineering/BesselJ.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Engineering\\\\BesselJ\\:\\:besselj2b\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Engineering/BesselJ.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Engineering\\\\BesselK\\:\\:besselK2\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Engineering/BesselK.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Engineering\\\\BitWise\\:\\:validateBitwiseArgument\\(\\) never returns int so it can be removed from the return typehint\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Engineering/BitWise.php

		-
			message: "#^Parameter \\#1 \\$number of function floor expects float, float\\|int\\|string given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Engineering/BitWise.php

		-
			message: "#^Parameter \\#1 \\$number of function floor expects float, float\\|int\\<0, 281474976710655\\>\\|string given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Engineering/BitWise.php

		-
			message: "#^Parameter \\#1 \\$power of method Complex\\\\Complex\\:\\:pow\\(\\) expects float\\|int, float\\|int\\|string given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Engineering/ComplexFunctions.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Engineering\\\\ConvertBase\\:\\:validateValue\\(\\) has parameter \\$value with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Engineering/ConvertBase.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Engineering\\\\ConvertBase\\:\\:validatePlaces\\(\\) has parameter \\$places with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Engineering/ConvertBase.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Engineering\\\\ConvertUOM\\:\\:getUOMDetails\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Engineering/ConvertUOM.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Engineering\\\\ConvertUOM\\:\\:resolveTemperatureSynonyms\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Engineering/ConvertUOM.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Engineering\\\\Erf\\:\\:\\$twoSqrtPi has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Engineering/Erf.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Engineering\\\\Erf\\:\\:erfValue\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Engineering/Erf.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Engineering\\\\Erf\\:\\:erfValue\\(\\) has parameter \\$value with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Engineering/Erf.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Engineering\\\\ErfC\\:\\:\\$oneSqrtPi has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Engineering/ErfC.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Engineering\\\\ErfC\\:\\:erfcValue\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Engineering/ErfC.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Engineering\\\\ErfC\\:\\:erfcValue\\(\\) has parameter \\$value with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Engineering/ErfC.php

		-
			message: "#^Parameter \\#1 \\$callback of function set_error_handler expects \\(callable\\(int, string, string, int, array\\)\\: bool\\)\\|null, array\\('PhpOffice\\\\\\\\PhpSpreadsheet\\\\\\\\Calculation\\\\\\\\Exception', 'errorHandlerCallback'\\) given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/ExceptionHandler.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Financial\\:\\:ISPMT\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Financial.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Financial\\:\\:ISPMT\\(\\) has parameter \\$args with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Financial.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Financial\\:\\:NPV\\(\\) has parameter \\$args with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Financial.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Financial\\\\CashFlow\\\\Constant\\\\Periodic\\\\Interest\\:\\:schedulePayment\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Financial/CashFlow/Constant/Periodic/Interest.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Financial\\\\CashFlow\\\\Constant\\\\Periodic\\\\Interest\\:\\:rateNextGuess\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Financial/CashFlow/Constant/Periodic/Interest.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Financial\\\\CashFlow\\\\Constant\\\\Periodic\\\\Interest\\:\\:rateNextGuess\\(\\) has parameter \\$futureValue with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Financial/CashFlow/Constant/Periodic/Interest.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Financial\\\\CashFlow\\\\Constant\\\\Periodic\\\\Interest\\:\\:rateNextGuess\\(\\) has parameter \\$numberOfPeriods with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Financial/CashFlow/Constant/Periodic/Interest.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Financial\\\\CashFlow\\\\Constant\\\\Periodic\\\\Interest\\:\\:rateNextGuess\\(\\) has parameter \\$payment with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Financial/CashFlow/Constant/Periodic/Interest.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Financial\\\\CashFlow\\\\Constant\\\\Periodic\\\\Interest\\:\\:rateNextGuess\\(\\) has parameter \\$presentValue with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Financial/CashFlow/Constant/Periodic/Interest.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Financial\\\\CashFlow\\\\Constant\\\\Periodic\\\\Interest\\:\\:rateNextGuess\\(\\) has parameter \\$rate with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Financial/CashFlow/Constant/Periodic/Interest.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Financial\\\\CashFlow\\\\Constant\\\\Periodic\\\\Interest\\:\\:rateNextGuess\\(\\) has parameter \\$type with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Financial/CashFlow/Constant/Periodic/Interest.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Financial\\\\CashFlow\\\\Constant\\\\Periodic\\\\InterestAndPrincipal\\:\\:\\$interest has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Financial/CashFlow/Constant/Periodic/InterestAndPrincipal.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Financial\\\\CashFlow\\\\Constant\\\\Periodic\\\\InterestAndPrincipal\\:\\:\\$principal has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Financial/CashFlow/Constant/Periodic/InterestAndPrincipal.php

		-
			message: "#^Binary operation \"\\-\" between float\\|string and 0\\|float results in an error\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Financial/CashFlow/Constant/Periodic/InterestAndPrincipal.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Financial\\\\CashFlow\\\\Variable\\\\Periodic\\:\\:presentValue\\(\\) has parameter \\$args with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Financial/CashFlow/Variable/Periodic.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Financial\\\\Depreciation\\:\\:validateCost\\(\\) has parameter \\$cost with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Financial/Depreciation.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Financial\\\\Depreciation\\:\\:validateSalvage\\(\\) has parameter \\$salvage with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Financial/Depreciation.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Financial\\\\Depreciation\\:\\:validateLife\\(\\) has parameter \\$life with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Financial/Depreciation.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Financial\\\\Depreciation\\:\\:validatePeriod\\(\\) has parameter \\$period with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Financial/Depreciation.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Financial\\\\Depreciation\\:\\:validateMonth\\(\\) has parameter \\$month with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Financial/Depreciation.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Financial\\\\Depreciation\\:\\:validateFactor\\(\\) has parameter \\$factor with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Financial/Depreciation.php

		-
			message: "#^Strict comparison using \\=\\=\\= between string and null will always evaluate to false\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/FormulaParser.php

		-
			message: "#^Cannot call method setValue\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\FormulaToken\\|null\\.$#"
			count: 5
			path: src/PhpSpreadsheet/Calculation/FormulaParser.php

		-
			message: "#^Cannot call method setTokenSubType\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\FormulaToken\\|null\\.$#"
			count: 5
			path: src/PhpSpreadsheet/Calculation/FormulaParser.php

		-
			message: "#^Cannot call method getTokenType\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\FormulaToken\\|null\\.$#"
			count: 9
			path: src/PhpSpreadsheet/Calculation/FormulaParser.php

		-
			message: "#^Strict comparison using \\=\\=\\= between PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\FormulaToken and null will always evaluate to false\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/FormulaParser.php

		-
			message: "#^Cannot call method getTokenSubType\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\FormulaToken\\|null\\.$#"
			count: 4
			path: src/PhpSpreadsheet/Calculation/FormulaParser.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Functions\\:\\:isMatrixValue\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Functions.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Functions\\:\\:isMatrixValue\\(\\) has parameter \\$idx with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Functions.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Functions\\:\\:isValue\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Functions.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Functions\\:\\:isValue\\(\\) has parameter \\$idx with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Functions.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Functions\\:\\:isCellValue\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Functions.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Functions\\:\\:isCellValue\\(\\) has parameter \\$idx with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Functions.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Functions\\:\\:ifCondition\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Functions.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Functions\\:\\:ifCondition\\(\\) has parameter \\$condition with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Functions.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Functions\\:\\:operandSpecialHandling\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Functions.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Functions\\:\\:operandSpecialHandling\\(\\) has parameter \\$operand with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Functions.php

		-
			message: "#^Cannot call method getCell\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\Worksheet\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Functions.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Internal\\\\MakeMatrix\\:\\:make\\(\\) has parameter \\$args with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Internal/MakeMatrix.php

		-
			message: "#^Call to function is_string\\(\\) with null will always evaluate to false\\.$#"
			count: 3
			path: src/PhpSpreadsheet/Calculation/Logical/Operations.php

		-
			message: "#^Result of && is always false\\.$#"
			count: 3
			path: src/PhpSpreadsheet/Calculation/Logical/Operations.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\LookupRef\\:\\:OFFSET\\(\\) should return array\\|string but returns array\\|int\\|string\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/LookupRef.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\LookupRef\\:\\:CHOOSE\\(\\) has parameter \\$chooseArgs with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/LookupRef.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\LookupRef\\\\Address\\:\\:sheetName\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/LookupRef/Address.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\LookupRef\\\\ExcelMatch\\:\\:matchFirstValue\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/LookupRef/ExcelMatch.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\LookupRef\\\\ExcelMatch\\:\\:matchFirstValue\\(\\) has parameter \\$lookupArray with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/LookupRef/ExcelMatch.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\LookupRef\\\\ExcelMatch\\:\\:matchFirstValue\\(\\) has parameter \\$lookupValue with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/LookupRef/ExcelMatch.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\LookupRef\\\\ExcelMatch\\:\\:matchLargestValue\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/LookupRef/ExcelMatch.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\LookupRef\\\\ExcelMatch\\:\\:matchLargestValue\\(\\) has parameter \\$keySet with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/LookupRef/ExcelMatch.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\LookupRef\\\\ExcelMatch\\:\\:matchLargestValue\\(\\) has parameter \\$lookupArray with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/LookupRef/ExcelMatch.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\LookupRef\\\\ExcelMatch\\:\\:matchLargestValue\\(\\) has parameter \\$lookupValue with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/LookupRef/ExcelMatch.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\LookupRef\\\\ExcelMatch\\:\\:matchSmallestValue\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/LookupRef/ExcelMatch.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\LookupRef\\\\ExcelMatch\\:\\:matchSmallestValue\\(\\) has parameter \\$lookupArray with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/LookupRef/ExcelMatch.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\LookupRef\\\\ExcelMatch\\:\\:matchSmallestValue\\(\\) has parameter \\$lookupValue with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/LookupRef/ExcelMatch.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\LookupRef\\\\ExcelMatch\\:\\:validateLookupValue\\(\\) has parameter \\$lookupValue with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/LookupRef/ExcelMatch.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\LookupRef\\\\ExcelMatch\\:\\:validateMatchType\\(\\) has parameter \\$matchType with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/LookupRef/ExcelMatch.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\LookupRef\\\\ExcelMatch\\:\\:validateLookupArray\\(\\) has parameter \\$lookupArray with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/LookupRef/ExcelMatch.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\LookupRef\\\\ExcelMatch\\:\\:prepareLookupArray\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/LookupRef/ExcelMatch.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\LookupRef\\\\ExcelMatch\\:\\:prepareLookupArray\\(\\) has parameter \\$lookupArray with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/LookupRef/ExcelMatch.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\LookupRef\\\\ExcelMatch\\:\\:prepareLookupArray\\(\\) has parameter \\$matchType with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/LookupRef/ExcelMatch.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\LookupRef\\\\Lookup\\:\\:verifyResultVector\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/LookupRef/Lookup.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\LookupRef\\\\Lookup\\:\\:verifyResultVector\\(\\) has parameter \\$resultVector with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/LookupRef/Lookup.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\LookupRef\\\\LookupBase\\:\\:validateIndexLookup\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/LookupRef/LookupBase.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\LookupRef\\\\LookupBase\\:\\:validateIndexLookup\\(\\) has parameter \\$index_number with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/LookupRef/LookupBase.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\LookupRef\\\\LookupBase\\:\\:validateIndexLookup\\(\\) has parameter \\$lookup_array with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/LookupRef/LookupBase.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\LookupRef\\\\LookupBase\\:\\:checkMatch\\(\\) has parameter \\$notExactMatch with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/LookupRef/LookupBase.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\LookupRef\\\\Matrix\\:\\:extractRowValue\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/LookupRef/Matrix.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\LookupRef\\\\Offset\\:\\:extractRequiredCells\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/LookupRef/Offset.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\LookupRef\\\\Offset\\:\\:extractWorksheet\\(\\) has parameter \\$cellAddress with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/LookupRef/Offset.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\LookupRef\\\\Offset\\:\\:adjustEndCellColumnForWidth\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/LookupRef/Offset.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\LookupRef\\\\Offset\\:\\:adjustEndCellColumnForWidth\\(\\) has parameter \\$columns with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/LookupRef/Offset.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\LookupRef\\\\Offset\\:\\:adjustEndCellColumnForWidth\\(\\) has parameter \\$width with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/LookupRef/Offset.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\LookupRef\\\\Offset\\:\\:adustEndCellRowForHeight\\(\\) has parameter \\$endCellRow with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/LookupRef/Offset.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\LookupRef\\\\Offset\\:\\:adustEndCellRowForHeight\\(\\) has parameter \\$height with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/LookupRef/Offset.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\LookupRef\\\\Offset\\:\\:adustEndCellRowForHeight\\(\\) has parameter \\$rows with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/LookupRef/Offset.php

		-
			message: "#^Parameter \\#1 \\$columnAddress of static method PhpOffice\\\\PhpSpreadsheet\\\\Cell\\\\Coordinate\\:\\:columnIndexFromString\\(\\) expects string, string\\|null given\\.$#"
			count: 3
			path: src/PhpSpreadsheet/Calculation/LookupRef/RowColumnInformation.php

		-
			message: "#^Parameter \\#1 \\$low of function range expects float\\|int\\|string, string\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/LookupRef/RowColumnInformation.php

		-
			message: "#^Parameter \\#2 \\$high of function range expects float\\|int\\|string, string\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/LookupRef/RowColumnInformation.php

		-
			message: "#^Parameter \\#2 \\$cmp_function of function uasort expects callable\\(mixed, mixed\\)\\: int, array\\('self', 'vlookupSort'\\) given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/LookupRef/VLookup.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\LookupRef\\\\VLookup\\:\\:vlookupSort\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/LookupRef/VLookup.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\LookupRef\\\\VLookup\\:\\:vlookupSort\\(\\) has parameter \\$a with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/LookupRef/VLookup.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\LookupRef\\\\VLookup\\:\\:vlookupSort\\(\\) has parameter \\$b with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/LookupRef/VLookup.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\LookupRef\\\\VLookup\\:\\:vLookupSearch\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/LookupRef/VLookup.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\LookupRef\\\\VLookup\\:\\:vLookupSearch\\(\\) has parameter \\$column with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/LookupRef/VLookup.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\LookupRef\\\\VLookup\\:\\:vLookupSearch\\(\\) has parameter \\$lookupArray with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/LookupRef/VLookup.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\LookupRef\\\\VLookup\\:\\:vLookupSearch\\(\\) has parameter \\$lookupValue with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/LookupRef/VLookup.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\LookupRef\\\\VLookup\\:\\:vLookupSearch\\(\\) has parameter \\$notExactMatch with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/LookupRef/VLookup.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\:\\:MAXIFS\\(\\) should return float but returns float\\|string\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\:\\:MINIFS\\(\\) should return float but returns float\\|string\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\Averages\\:\\:filterArguments\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Averages.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\Averages\\:\\:filterArguments\\(\\) has parameter \\$args with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Averages.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\Averages\\:\\:modeCalc\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Averages.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\Averages\\:\\:modeCalc\\(\\) has parameter \\$data with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Averages.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\Percentiles\\:\\:percentileFilterValues\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Percentiles.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\Percentiles\\:\\:rankFilterValues\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Percentiles.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\Trends\\:\\:checkTrendArrays\\(\\) has parameter \\$array1 with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Trends.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\Trends\\:\\:checkTrendArrays\\(\\) has parameter \\$array2 with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Trends.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\Trends\\:\\:GROWTH\\(\\) should return array\\<float\\> but returns array\\<int, array\\<int, array\\<int, mixed\\>\\>\\>\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Trends.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\Trends\\:\\:TREND\\(\\) should return array\\<float\\> but returns array\\<int, array\\<int, array\\<int, mixed\\>\\>\\>\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Trends.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\Conditional\\:\\:SUMIF\\(\\) should return float\\|string but returns float\\|string\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Conditional.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\Conditional\\:\\:buildConditionSet\\(\\) has parameter \\$args with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Conditional.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\Conditional\\:\\:buildConditionSetForValueRange\\(\\) has parameter \\$args with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Conditional.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\Conditional\\:\\:buildConditions\\(\\) has parameter \\$args with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Conditional.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\Conditional\\:\\:buildDatabase\\(\\) has parameter \\$args with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Conditional.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\Conditional\\:\\:buildDatabaseWithValueRange\\(\\) has parameter \\$args with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Conditional.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\Conditional\\:\\:buildDataSet\\(\\) has parameter \\$args with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Conditional.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\Distributions\\\\Beta\\:\\:\\$logBetaCacheP has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Distributions/Beta.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\Distributions\\\\Beta\\:\\:\\$logBetaCacheQ has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Distributions/Beta.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\Distributions\\\\Beta\\:\\:\\$logBetaCacheResult has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Distributions/Beta.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\Distributions\\\\ChiSquared\\:\\:pchisq\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Distributions/ChiSquared.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\Distributions\\\\ChiSquared\\:\\:pchisq\\(\\) has parameter \\$chi2 with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Distributions/ChiSquared.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\Distributions\\\\ChiSquared\\:\\:pchisq\\(\\) has parameter \\$degrees with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Distributions/ChiSquared.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\Distributions\\\\ChiSquared\\:\\:gammp\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Distributions/ChiSquared.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\Distributions\\\\ChiSquared\\:\\:gammp\\(\\) has parameter \\$n with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Distributions/ChiSquared.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\Distributions\\\\ChiSquared\\:\\:gammp\\(\\) has parameter \\$x with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Distributions/ChiSquared.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\Distributions\\\\ChiSquared\\:\\:gser\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Distributions/ChiSquared.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\Distributions\\\\ChiSquared\\:\\:gser\\(\\) has parameter \\$n with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Distributions/ChiSquared.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\Distributions\\\\ChiSquared\\:\\:gser\\(\\) has parameter \\$x with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Distributions/ChiSquared.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\Distributions\\\\ChiSquared\\:\\:gcf\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Distributions/ChiSquared.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\Distributions\\\\ChiSquared\\:\\:gcf\\(\\) has parameter \\$n with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Distributions/ChiSquared.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\Distributions\\\\ChiSquared\\:\\:gcf\\(\\) has parameter \\$x with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Distributions/ChiSquared.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\Distributions\\\\Normal\\:\\:inverseNcdf\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Distributions/Normal.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\Distributions\\\\Normal\\:\\:inverseNcdf\\(\\) has parameter \\$p with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Distributions/Normal.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\Distributions\\\\GammaBase\\:\\:calculateDistribution\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Distributions/GammaBase.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\Distributions\\\\GammaBase\\:\\:calculateInverse\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Distributions/GammaBase.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\Distributions\\\\GammaBase\\:\\:\\$logGammaCacheResult has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Distributions/GammaBase.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\Distributions\\\\GammaBase\\:\\:\\$logGammaCacheX has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Distributions/GammaBase.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\Distributions\\\\GammaBase\\:\\:logGamma1\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Distributions/GammaBase.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\Distributions\\\\GammaBase\\:\\:logGamma2\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Distributions/GammaBase.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\Distributions\\\\GammaBase\\:\\:logGamma3\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Distributions/GammaBase.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\Distributions\\\\GammaBase\\:\\:logGamma4\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Distributions/GammaBase.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\Distributions\\\\NewtonRaphson\\:\\:\\$callback has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Distributions/NewtonRaphson.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\Distributions\\\\NewtonRaphson\\:\\:execute\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Distributions/NewtonRaphson.php

		-
			message: "#^Binary operation \"\\-\" between float\\|string and float\\|int\\|\\(string&numeric\\) results in an error\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Distributions/StandardNormal.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\MaxMinBase\\:\\:datatypeAdjustmentAllowStrings\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/MaxMinBase.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\MaxMinBase\\:\\:datatypeAdjustmentAllowStrings\\(\\) has parameter \\$value with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/MaxMinBase.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\VarianceBase\\:\\:datatypeAdjustmentAllowStrings\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/VarianceBase.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\VarianceBase\\:\\:datatypeAdjustmentAllowStrings\\(\\) has parameter \\$value with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/VarianceBase.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\VarianceBase\\:\\:datatypeAdjustmentBooleans\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/VarianceBase.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\VarianceBase\\:\\:datatypeAdjustmentBooleans\\(\\) has parameter \\$value with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/VarianceBase.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\TextData\\:\\:TRIMNONPRINTABLE\\(\\) should return string but returns string\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/TextData.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\TextData\\:\\:CONCATENATE\\(\\) has parameter \\$args with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/TextData.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\TextData\\:\\:SEARCHSENSITIVE\\(\\) should return string but returns int\\|string\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/TextData.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\TextData\\:\\:SEARCHINSENSITIVE\\(\\) should return string but returns int\\|string\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/TextData.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Token\\\\Stack\\:\\:getStackItem\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Token/Stack.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Token\\\\Stack\\:\\:getStackItem\\(\\) has parameter \\$onlyIf with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Token/Stack.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Token\\\\Stack\\:\\:getStackItem\\(\\) has parameter \\$onlyIfNot with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Token/Stack.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Token\\\\Stack\\:\\:getStackItem\\(\\) has parameter \\$reference with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Token/Stack.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Token\\\\Stack\\:\\:getStackItem\\(\\) has parameter \\$storeKey with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Token/Stack.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Token\\\\Stack\\:\\:getStackItem\\(\\) has parameter \\$type with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Token/Stack.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Token\\\\Stack\\:\\:getStackItem\\(\\) has parameter \\$value with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Token/Stack.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Cell\\\\Cell\\:\\:\\$formulaAttributes has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Cell/Cell.php

		-
			message: "#^Elseif branch is unreachable because previous condition is always true\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Cell/Cell.php

		-
			message: "#^Parameter \\#2 \\$format of static method PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\NumberFormat\\:\\:toFormattedString\\(\\) expects string, string\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Cell/Cell.php

		-
			message: "#^Unreachable statement \\- code above always terminates\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Cell/Cell.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Cell\\\\Cell\\:\\:getFormulaAttributes\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Cell/Cell.php

		-
			message: "#^Parameter \\#2 \\$str of function explode expects string, array\\<int, string\\>\\|string given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Cell/Coordinate.php

		-
			message: "#^Parameter \\#4 \\$currentRow of static method PhpOffice\\\\PhpSpreadsheet\\\\Cell\\\\Coordinate\\:\\:validateRange\\(\\) expects int, string given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Cell/Coordinate.php

		-
			message: "#^Parameter \\#5 \\$endRow of static method PhpOffice\\\\PhpSpreadsheet\\\\Cell\\\\Coordinate\\:\\:validateRange\\(\\) expects int, string given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Cell/Coordinate.php

		-
			message: "#^Call to an undefined method object\\:\\:getHashCode\\(\\)\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Cell/Coordinate.php

		-
			message: "#^Parameter \\#1 \\$input of function array_chunk expects array, array\\<int, string\\>\\|false given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Cell/Coordinate.php

		-
			message: "#^Parameter \\#1 \\$textValue of static method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\StringHelper\\:\\:substring\\(\\) expects string, string\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Cell/DataType.php

		-
			message: "#^Parameter \\#2 \\$alpha of method PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Axis\\:\\:setShadowColor\\(\\) expects int, int\\|string given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Axis.php

		-
			message: "#^Parameter \\#1 \\$blur of method PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Axis\\:\\:setShadowBlur\\(\\) expects float, float\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Axis.php

		-
			message: "#^Parameter \\#1 \\$angle of method PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Axis\\:\\:setShadowAngle\\(\\) expects int, int\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Axis.php

		-
			message: "#^Parameter \\#1 \\$distance of method PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Axis\\:\\:setShadowDistance\\(\\) expects float, float\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Axis.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Chart\\:\\:\\$title \\(PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Title\\) does not accept PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Title\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Chart.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Chart\\:\\:\\$legend \\(PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Legend\\) does not accept PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Legend\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Chart.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Chart\\:\\:\\$xAxisLabel \\(PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Title\\) does not accept PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Title\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Chart.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Chart\\:\\:\\$yAxisLabel \\(PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Title\\) does not accept PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Title\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Chart.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Chart\\:\\:\\$plotArea \\(PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\PlotArea\\) does not accept PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\PlotArea\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Chart.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Chart\\:\\:\\$xAxis \\(PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Axis\\) does not accept PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Axis\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Chart.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Chart\\:\\:\\$yAxis \\(PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Axis\\) does not accept PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Axis\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Chart.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Chart\\:\\:\\$majorGridlines \\(PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\GridLines\\) does not accept PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\GridLines\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Chart.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Chart\\:\\:\\$minorGridlines \\(PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\GridLines\\) does not accept PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\GridLines\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Chart.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Chart\\:\\:\\$worksheet \\(PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\Worksheet\\) does not accept PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\Worksheet\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Chart.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Chart\\:\\:setTopLeftXOffset\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Chart.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Chart\\:\\:setTopLeftXOffset\\(\\) has parameter \\$xOffset with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Chart.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Chart\\:\\:getTopLeftXOffset\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Chart.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Chart\\:\\:setTopLeftYOffset\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Chart.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Chart\\:\\:setTopLeftYOffset\\(\\) has parameter \\$yOffset with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Chart.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Chart\\:\\:getTopLeftYOffset\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Chart.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Chart\\:\\:setBottomRightCell\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Chart.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Chart\\:\\:setBottomRightCell\\(\\) has parameter \\$cell with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Chart.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Chart\\:\\:setBottomRightXOffset\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Chart.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Chart\\:\\:setBottomRightXOffset\\(\\) has parameter \\$xOffset with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Chart.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Chart\\:\\:getBottomRightXOffset\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Chart.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Chart\\:\\:setBottomRightYOffset\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Chart.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Chart\\:\\:setBottomRightYOffset\\(\\) has parameter \\$yOffset with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Chart.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Chart\\:\\:getBottomRightYOffset\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Chart.php

		-
			message: "#^Strict comparison using \\=\\=\\= between PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\DataSeriesValues and null will always evaluate to false\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Chart/DataSeries.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\DataSeriesValues\\:\\:\\$dataTypeValues has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/DataSeriesValues.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\DataSeriesValues\\:\\:\\$dataSource \\(string\\) does not accept string\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/DataSeriesValues.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\DataSeriesValues\\:\\:\\$fillColor \\(array\\<string\\>\\|string\\) does not accept array\\<string\\>\\|string\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/DataSeriesValues.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\DataSeriesValues\\:\\:refresh\\(\\) has parameter \\$flatten with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/DataSeriesValues.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\GridLines\\:\\:\\$objectState has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/GridLines.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\GridLines\\:\\:\\$lineProperties has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/GridLines.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\GridLines\\:\\:\\$shadowProperties has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/GridLines.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\GridLines\\:\\:\\$glowProperties has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/GridLines.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\GridLines\\:\\:\\$softEdges has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/GridLines.php

		-
			message: "#^Parameter \\#1 \\$color of method PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\GridLines\\:\\:setGlowColor\\(\\) expects string, string\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/GridLines.php

		-
			message: "#^Parameter \\#2 \\$alpha of method PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\GridLines\\:\\:setGlowColor\\(\\) expects int, int\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/GridLines.php

		-
			message: "#^Parameter \\#3 \\$colorType of method PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\GridLines\\:\\:setGlowColor\\(\\) expects string, string\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/GridLines.php

		-
			message: "#^Parameter \\#1 \\$angle of method PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\GridLines\\:\\:setShadowAngle\\(\\) expects int, int\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/GridLines.php

		-
			message: "#^Parameter \\#1 \\$distance of method PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\GridLines\\:\\:setShadowDistance\\(\\) expects float, float\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/GridLines.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Legend\\:\\:\\$positionXLref has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Legend.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Legend\\:\\:\\$layout \\(PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Layout\\) does not accept PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Layout\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Legend.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\PlotArea\\:\\:\\$layout \\(PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Layout\\) does not accept PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Layout\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/PlotArea.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Properties\\:\\:getTrueAlpha\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Properties.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Properties\\:\\:getTrueAlpha\\(\\) has parameter \\$alpha with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Properties.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Properties\\:\\:setColorProperties\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Properties.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Properties\\:\\:setColorProperties\\(\\) has parameter \\$alpha with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Properties.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Properties\\:\\:setColorProperties\\(\\) has parameter \\$color with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Properties.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Properties\\:\\:setColorProperties\\(\\) has parameter \\$colorType with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Properties.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Properties\\:\\:getLineStyleArrowSize\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Properties.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Properties\\:\\:getLineStyleArrowSize\\(\\) has parameter \\$arrayKaySelector with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Properties.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Properties\\:\\:getLineStyleArrowSize\\(\\) has parameter \\$arraySelector with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Properties.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Properties\\:\\:getShadowPresetsMap\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Properties.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Properties\\:\\:getShadowPresetsMap\\(\\) has parameter \\$presetsOption with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Properties.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Properties\\:\\:getArrayElementsValue\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Properties.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Properties\\:\\:getArrayElementsValue\\(\\) has parameter \\$elements with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Properties.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Properties\\:\\:getArrayElementsValue\\(\\) has parameter \\$properties with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Properties.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Renderer\\\\JpGraph\\:\\:\\$width has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Renderer/JpGraph.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Renderer\\\\JpGraph\\:\\:\\$height has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Renderer/JpGraph.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Renderer\\\\JpGraph\\:\\:\\$colourSet has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Renderer/JpGraph.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Renderer\\\\JpGraph\\:\\:\\$markSet has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Renderer/JpGraph.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Renderer\\\\JpGraph\\:\\:\\$chart has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Renderer/JpGraph.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Renderer\\\\JpGraph\\:\\:\\$graph has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Renderer/JpGraph.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Renderer\\\\JpGraph\\:\\:\\$plotColour has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Renderer/JpGraph.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Renderer\\\\JpGraph\\:\\:\\$plotMark has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Renderer/JpGraph.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Renderer\\\\JpGraph\\:\\:formatPointMarker\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Renderer/JpGraph.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Renderer\\\\JpGraph\\:\\:formatPointMarker\\(\\) has parameter \\$markerID with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Renderer/JpGraph.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Renderer\\\\JpGraph\\:\\:formatPointMarker\\(\\) has parameter \\$seriesPlot with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Renderer/JpGraph.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Renderer\\\\JpGraph\\:\\:formatDataSetLabels\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Renderer/JpGraph.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Renderer\\\\JpGraph\\:\\:formatDataSetLabels\\(\\) has parameter \\$datasetLabels with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Renderer/JpGraph.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Renderer\\\\JpGraph\\:\\:formatDataSetLabels\\(\\) has parameter \\$groupID with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Renderer/JpGraph.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Renderer\\\\JpGraph\\:\\:formatDataSetLabels\\(\\) has parameter \\$labelCount with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Renderer/JpGraph.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Renderer\\\\JpGraph\\:\\:formatDataSetLabels\\(\\) has parameter \\$rotation with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Renderer/JpGraph.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Renderer\\\\JpGraph\\:\\:percentageSumCalculation\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Renderer/JpGraph.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Renderer\\\\JpGraph\\:\\:percentageSumCalculation\\(\\) has parameter \\$groupID with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Renderer/JpGraph.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Renderer\\\\JpGraph\\:\\:percentageSumCalculation\\(\\) has parameter \\$seriesCount with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Renderer/JpGraph.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Renderer\\\\JpGraph\\:\\:percentageAdjustValues\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Renderer/JpGraph.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Renderer\\\\JpGraph\\:\\:percentageAdjustValues\\(\\) has parameter \\$dataValues with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Renderer/JpGraph.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Renderer\\\\JpGraph\\:\\:percentageAdjustValues\\(\\) has parameter \\$sumValues with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Renderer/JpGraph.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Renderer\\\\JpGraph\\:\\:getCaption\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Renderer/JpGraph.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Renderer\\\\JpGraph\\:\\:getCaption\\(\\) has parameter \\$captionElement with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Renderer/JpGraph.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Renderer\\\\JpGraph\\:\\:renderCartesianPlotArea\\(\\) has parameter \\$type with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Renderer/JpGraph.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Renderer\\\\JpGraph\\:\\:renderPlotLine\\(\\) has parameter \\$combination with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Renderer/JpGraph.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Renderer\\\\JpGraph\\:\\:renderPlotLine\\(\\) has parameter \\$dimensions with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Renderer/JpGraph.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Renderer\\\\JpGraph\\:\\:renderPlotLine\\(\\) has parameter \\$filled with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Renderer/JpGraph.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Renderer\\\\JpGraph\\:\\:renderPlotLine\\(\\) has parameter \\$groupID with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Renderer/JpGraph.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Renderer\\\\JpGraph\\:\\:renderPlotBar\\(\\) has parameter \\$dimensions with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Renderer/JpGraph.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Renderer\\\\JpGraph\\:\\:renderPlotBar\\(\\) has parameter \\$groupID with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Renderer/JpGraph.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Renderer\\\\JpGraph\\:\\:renderPlotScatter\\(\\) has parameter \\$bubble with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Renderer/JpGraph.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Renderer\\\\JpGraph\\:\\:renderPlotScatter\\(\\) has parameter \\$groupID with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Renderer/JpGraph.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Renderer\\\\JpGraph\\:\\:renderPlotRadar\\(\\) has parameter \\$groupID with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Renderer/JpGraph.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Renderer\\\\JpGraph\\:\\:renderPlotContour\\(\\) has parameter \\$groupID with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Renderer/JpGraph.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Renderer\\\\JpGraph\\:\\:renderPlotStock\\(\\) has parameter \\$groupID with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Renderer/JpGraph.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Renderer\\\\JpGraph\\:\\:renderAreaChart\\(\\) has parameter \\$dimensions with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Renderer/JpGraph.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Renderer\\\\JpGraph\\:\\:renderAreaChart\\(\\) has parameter \\$groupCount with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Renderer/JpGraph.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Renderer\\\\JpGraph\\:\\:renderLineChart\\(\\) has parameter \\$dimensions with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Renderer/JpGraph.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Renderer\\\\JpGraph\\:\\:renderLineChart\\(\\) has parameter \\$groupCount with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Renderer/JpGraph.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Renderer\\\\JpGraph\\:\\:renderBarChart\\(\\) has parameter \\$dimensions with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Renderer/JpGraph.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Renderer\\\\JpGraph\\:\\:renderBarChart\\(\\) has parameter \\$groupCount with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Renderer/JpGraph.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Renderer\\\\JpGraph\\:\\:renderScatterChart\\(\\) has parameter \\$groupCount with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Renderer/JpGraph.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Renderer\\\\JpGraph\\:\\:renderBubbleChart\\(\\) has parameter \\$groupCount with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Renderer/JpGraph.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Renderer\\\\JpGraph\\:\\:renderPieChart\\(\\) has parameter \\$dimensions with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Renderer/JpGraph.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Renderer\\\\JpGraph\\:\\:renderPieChart\\(\\) has parameter \\$doughnut with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Renderer/JpGraph.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Renderer\\\\JpGraph\\:\\:renderPieChart\\(\\) has parameter \\$groupCount with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Renderer/JpGraph.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Renderer\\\\JpGraph\\:\\:renderPieChart\\(\\) has parameter \\$multiplePlots with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Renderer/JpGraph.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Renderer\\\\JpGraph\\:\\:renderRadarChart\\(\\) has parameter \\$groupCount with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Renderer/JpGraph.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Renderer\\\\JpGraph\\:\\:renderStockChart\\(\\) has parameter \\$groupCount with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Renderer/JpGraph.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Renderer\\\\JpGraph\\:\\:renderContourChart\\(\\) has parameter \\$dimensions with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Renderer/JpGraph.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Renderer\\\\JpGraph\\:\\:renderContourChart\\(\\) has parameter \\$groupCount with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Renderer/JpGraph.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Renderer\\\\JpGraph\\:\\:renderCombinationChart\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Renderer/JpGraph.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Renderer\\\\JpGraph\\:\\:renderCombinationChart\\(\\) has parameter \\$dimensions with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Renderer/JpGraph.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Renderer\\\\JpGraph\\:\\:renderCombinationChart\\(\\) has parameter \\$groupCount with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Renderer/JpGraph.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Renderer\\\\JpGraph\\:\\:renderCombinationChart\\(\\) has parameter \\$outputDestination with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Renderer/JpGraph.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Title\\:\\:\\$layout \\(PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Layout\\) does not accept PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Layout\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Chart/Title.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Collection\\\\Cells\\:\\:getParent\\(\\) should return PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\Worksheet but returns PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\Worksheet\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Collection/Cells.php

		-
			message: "#^Parameter \\#1 \\$string of function substr expects string, string\\|false given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Collection/Cells.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Collection\\\\Cells\\:\\:getCurrentCoordinate\\(\\) should return string but returns string\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Collection/Cells.php

		-
			message: "#^Parameter \\#1 \\$str of function sscanf expects string, string\\|null given\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Collection/Cells.php

		-
			message: "#^Parameter \\#1 \\$columnIndex of static method PhpOffice\\\\PhpSpreadsheet\\\\Cell\\\\Coordinate\\:\\:stringFromColumnIndex\\(\\) expects int, int\\|false given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Collection/Cells.php

		-
			message: "#^Possibly invalid array key type \\(array\\|string\\)\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Collection/Cells.php

		-
			message: "#^Cannot call method detach\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Cell\\\\Cell\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Collection/Cells.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Collection\\\\Memory\\:\\:\\$cache has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Collection/Memory.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\DefinedName\\:\\:\\$worksheet \\(PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\Worksheet\\) does not accept PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\Worksheet\\|null\\.$#"
			count: 2
			path: src/PhpSpreadsheet/DefinedName.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\DefinedName\\:\\:\\$scope \\(PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\Worksheet\\) does not accept PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\Worksheet\\|null\\.$#"
			count: 3
			path: src/PhpSpreadsheet/DefinedName.php

		-
			message: "#^Parameter \\#1 \\$namedRange of method PhpOffice\\\\PhpSpreadsheet\\\\Spreadsheet\\:\\:addNamedRange\\(\\) expects PhpOffice\\\\PhpSpreadsheet\\\\NamedRange, \\$this\\(PhpOffice\\\\PhpSpreadsheet\\\\DefinedName\\) given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/DefinedName.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Helper\\\\Html\\:\\:\\$colourMap has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Helper/Html.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Helper\\\\Html\\:\\:\\$face has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Helper/Html.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Helper\\\\Html\\:\\:\\$size has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Helper/Html.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Helper\\\\Html\\:\\:\\$color has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Helper/Html.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Helper\\\\Html\\:\\:\\$bold has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Helper/Html.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Helper\\\\Html\\:\\:\\$italic has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Helper/Html.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Helper\\\\Html\\:\\:\\$underline has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Helper/Html.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Helper\\\\Html\\:\\:\\$superscript has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Helper/Html.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Helper\\\\Html\\:\\:\\$subscript has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Helper/Html.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Helper\\\\Html\\:\\:\\$strikethrough has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Helper/Html.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Helper\\\\Html\\:\\:\\$startTagCallbacks has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Helper/Html.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Helper\\\\Html\\:\\:\\$endTagCallbacks has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Helper/Html.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Helper\\\\Html\\:\\:\\$stack has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Helper/Html.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Helper\\\\Html\\:\\:\\$stringData has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Helper/Html.php

		-
			message: "#^Parameter \\#1 \\$text of method PhpOffice\\\\PhpSpreadsheet\\\\RichText\\\\ITextElement\\:\\:setText\\(\\) expects string, string\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Helper/Html.php

		-
			message: "#^Cannot call method setName\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Helper/Html.php

		-
			message: "#^Cannot call method setSize\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Helper/Html.php

		-
			message: "#^Cannot call method setColor\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Helper/Html.php

		-
			message: "#^Cannot call method setBold\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Helper/Html.php

		-
			message: "#^Cannot call method setItalic\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Helper/Html.php

		-
			message: "#^Cannot call method setUnderline\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Helper/Html.php

		-
			message: "#^Cannot call method setSuperscript\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Helper/Html.php

		-
			message: "#^Cannot call method setSubscript\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Helper/Html.php

		-
			message: "#^Cannot call method setStrikethrough\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Helper/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Helper\\\\Html\\:\\:startFontTag\\(\\) has parameter \\$tag with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Helper/Html.php

		-
			message: "#^Parameter \\#1 \\$function of function call_user_func expects callable\\(\\)\\: mixed, array\\(\\$this\\(PhpOffice\\\\PhpSpreadsheet\\\\Helper\\\\Html\\), mixed\\) given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Helper/Html.php

		-
			message: "#^Parameter \\#1 \\$directory of class RecursiveDirectoryIterator constructor expects string, string\\|false given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Helper/Sample.php

		-
			message: "#^Parameter \\#3 \\$subject of function str_replace expects array\\|string, string\\|false given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Helper/Sample.php

		-
			message: "#^Parameter \\#1 \\$path of function pathinfo expects string, array\\|string given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Helper/Sample.php

		-
			message: "#^Parameter \\#3 \\$subject of function str_replace expects array\\|string, string\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Helper/Sample.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Helper\\\\Sample\\:\\:getSamples\\(\\) should return array\\<array\\<string\\>\\> but returns array\\<string, array\\<string, array\\|string\\>\\>\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Helper/Sample.php

		-
			message: "#^Parameter \\#1 \\$filename of function unlink expects string, string\\|false given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Helper/Sample.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Helper\\\\Sample\\:\\:log\\(\\) has parameter \\$message with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Helper/Sample.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\IOFactory\\:\\:\\$readers has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/IOFactory.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\IOFactory\\:\\:\\$writers has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/IOFactory.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\BaseReader\\:\\:\\$fileHandle has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/BaseReader.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\BaseReader\\:\\:getSecurityScanner\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/BaseReader.php

		-
			message: "#^Cannot call method getNamespaces\\(\\) on SimpleXMLElement\\|false\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Ods.php

		-
			message: "#^Cannot call method children\\(\\) on SimpleXMLElement\\|false\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Ods.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Ods\\:\\:listWorksheetNames\\(\\) should return array\\<string\\> but returns array\\<int, string\\|null\\>\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Ods.php

		-
			message: "#^Cannot call method getElementsByTagNameNS\\(\\) on DOMElement\\|null\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Reader/Ods.php

		-
			message: "#^Parameter \\#1 \\$element of method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Ods\\:\\:scanElementForText\\(\\) expects DOMNode, DOMElement\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Ods.php

		-
			message: "#^Cannot call method getAttributeNS\\(\\) on DOMElement\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Ods.php

		-
			message: "#^Parameter \\#1 \\$settings of method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Ods\\:\\:lookForActiveSheet\\(\\) expects DOMElement, DOMElement\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Ods.php

		-
			message: "#^Parameter \\#1 \\$settings of method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Ods\\:\\:lookForSelectedCells\\(\\) expects DOMElement, DOMElement\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Ods.php

		-
			message: "#^Cannot call method setSelectedCellByColumnAndRow\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\Worksheet\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Ods.php

		-
			message: "#^Cannot call method getNamedItem\\(\\) on DOMNamedNodeMap\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Ods.php

		-
			message: "#^If condition is always true\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Ods.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Ods\\\\PageSettings\\:\\:\\$officeNs has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Ods/PageSettings.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Ods\\\\PageSettings\\:\\:\\$stylesNs has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Ods/PageSettings.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Ods\\\\PageSettings\\:\\:\\$stylesFo has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Ods/PageSettings.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Ods\\\\PageSettings\\:\\:\\$pageLayoutStyles has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Ods/PageSettings.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Ods\\\\PageSettings\\:\\:\\$masterStylesCrossReference has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Ods/PageSettings.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Ods\\\\PageSettings\\:\\:\\$masterPrintStylesCrossReference has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Ods/PageSettings.php

		-
			message: "#^Cannot call method getElementsByTagNameNS\\(\\) on DOMElement\\|null\\.$#"
			count: 3
			path: src/PhpSpreadsheet/Reader/Ods/PageSettings.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Ods\\\\Properties\\:\\:\\$spreadsheet has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Ods/Properties.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Ods\\\\Properties\\:\\:load\\(\\) has parameter \\$namespacesMeta with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Ods/Properties.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Ods\\\\Properties\\:\\:setMetaProperties\\(\\) has parameter \\$namespacesMeta with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Ods/Properties.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Ods\\\\Properties\\:\\:setMetaProperties\\(\\) has parameter \\$propertyName with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Ods/Properties.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Ods\\\\Properties\\:\\:setUserDefinedProperty\\(\\) has parameter \\$propertyValue with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Ods/Properties.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Ods\\\\Properties\\:\\:setUserDefinedProperty\\(\\) has parameter \\$propertyValueAttributes with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Ods/Properties.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Security\\\\XmlScanner\\:\\:\\$callback has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Security/XmlScanner.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Security\\\\XmlScanner\\:\\:\\$libxmlDisableEntityLoaderValue has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Security/XmlScanner.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Security\\\\XmlScanner\\:\\:__construct\\(\\) has parameter \\$pattern with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Security/XmlScanner.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Security\\\\XmlScanner\\:\\:getInstance\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Security/XmlScanner.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Security\\\\XmlScanner\\:\\:threadSafeLibxmlDisableEntityLoaderAvailability\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Security/XmlScanner.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Security\\\\XmlScanner\\:\\:toUtf8\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Security/XmlScanner.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Security\\\\XmlScanner\\:\\:toUtf8\\(\\) has parameter \\$xml with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Security/XmlScanner.php

		-
			message: "#^Parameter \\#2 \\$subject of function preg_match expects string, array\\<int, string\\>\\|string\\|false given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Security/XmlScanner.php

		-
			message: "#^Call to an undefined method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Escher\\|PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Escher\\\\DgContainer\\|PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Escher\\\\DgContainer\\\\SpgrContainer\\|PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Escher\\\\DgContainer\\\\SpgrContainer\\\\SpContainer\\|PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Escher\\\\DggContainer\\|PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Escher\\\\DggContainer\\\\BstoreContainer\\|PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Escher\\\\DggContainer\\\\BstoreContainer\\\\BSE\\:\\:getDgContainer\\(\\)\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xls.php

		-
			message: "#^Call to an undefined method object\\:\\:getNestingLevel\\(\\)\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xls.php

		-
			message: "#^Call to an undefined method object\\:\\:getStartCoordinates\\(\\)\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Reader/Xls.php

		-
			message: "#^Call to an undefined method object\\:\\:getEndCoordinates\\(\\)\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xls.php

		-
			message: "#^Call to an undefined method object\\:\\:getStartOffsetX\\(\\)\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xls.php

		-
			message: "#^Call to an undefined method object\\:\\:getStartOffsetY\\(\\)\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xls.php

		-
			message: "#^Call to an undefined method object\\:\\:getEndOffsetX\\(\\)\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xls.php

		-
			message: "#^Call to an undefined method object\\:\\:getEndOffsetY\\(\\)\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xls.php

		-
			message: "#^Parameter \\#2 \\$startRow of static method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Xls\\:\\:getDistanceY\\(\\) expects int, string given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xls.php

		-
			message: "#^Parameter \\#4 \\$endRow of static method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Xls\\:\\:getDistanceY\\(\\) expects int, string given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xls.php

		-
			message: "#^Parameter \\#2 \\$row of static method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Xls\\:\\:sizeRow\\(\\) expects int, string given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xls.php

		-
			message: "#^Call to an undefined method object\\:\\:getOPT\\(\\)\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xls.php

		-
			message: "#^Call to an undefined method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Escher\\|PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Escher\\\\DgContainer\\|PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Escher\\\\DgContainer\\\\SpgrContainer\\|PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Escher\\\\DgContainer\\\\SpgrContainer\\\\SpContainer\\|PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Escher\\\\DggContainer\\|PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Escher\\\\DggContainer\\\\BstoreContainer\\|PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Escher\\\\DggContainer\\\\BstoreContainer\\\\BSE\\:\\:getDggContainer\\(\\)\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xls.php

		-
			message: "#^If condition is always true\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xls.php

		-
			message: "#^Parameter \\#2 \\$row of method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\IReadFilter\\:\\:readCell\\(\\) expects int, string given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xls.php

		-
			message: "#^Negated boolean expression is always false\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xls.php

		-
			message: "#^Parameter \\#1 \\$block of method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xls\\:\\:makeKey\\(\\) expects int, float given\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Reader/Xls.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xls\\:\\:\\$data \\(string\\) does not accept string\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xls.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xls\\:\\:\\$summaryInformation \\(string\\) does not accept string\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xls.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xls\\:\\:\\$documentSummaryInformation \\(string\\) does not accept string\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xls.php

		-
			message: "#^Parameter \\#3 \\$subject of function str_replace expects array\\|string, int\\|string\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xls.php

		-
			message: "#^Parameter \\#1 \\$pValue of method PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\Worksheet\\:\\:setShowSummaryBelow\\(\\) expects bool, int given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xls.php

		-
			message: "#^Parameter \\#1 \\$pValue of method PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\Worksheet\\:\\:setShowSummaryRight\\(\\) expects bool, int given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xls.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xls\\:\\:includeCellRangeFiltered\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xls.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xls\\:\\:includeCellRangeFiltered\\(\\) has parameter \\$cellRangeAddress with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xls.php

		-
			message: "#^Parameter \\#1 \\$type of method PhpOffice\\\\PhpSpreadsheet\\\\Cell\\\\DataValidation\\:\\:setType\\(\\) expects string, int\\|string given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xls.php

		-
			message: "#^Parameter \\#1 \\$errorStyle of method PhpOffice\\\\PhpSpreadsheet\\\\Cell\\\\DataValidation\\:\\:setErrorStyle\\(\\) expects string, int\\|string given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xls.php

		-
			message: "#^Parameter \\#1 \\$operator of method PhpOffice\\\\PhpSpreadsheet\\\\Cell\\\\DataValidation\\:\\:setOperator\\(\\) expects string, int\\|string given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xls.php

		-
			message: "#^Unreachable statement \\- code above always terminates\\.$#"
			count: 8
			path: src/PhpSpreadsheet/Reader/Xls.php

		-
			message: "#^Cannot access offset 1 on array\\|false\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xls.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xls\\:\\:parseRichText\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xls.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xls\\:\\:parseRichText\\(\\) has parameter \\$is with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xls.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xls\\\\Color\\\\BIFF5\\:\\:\\$map has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xls/Color/BIFF5.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xls\\\\Color\\\\BIFF8\\:\\:\\$map has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xls/Color/BIFF8.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xls\\\\Color\\\\BuiltIn\\:\\:\\$map has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xls/Color/BuiltIn.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xls\\\\ErrorCode\\:\\:\\$map has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xls/ErrorCode.php

		-
			message: "#^Parameter \\#1 \\$input of function array_values expects array, array\\|false given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xls/MD5.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xls\\\\MD5\\:\\:f\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xls/MD5.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xls\\\\MD5\\:\\:g\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xls/MD5.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xls\\\\MD5\\:\\:h\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xls/MD5.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xls\\\\MD5\\:\\:i\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xls/MD5.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xls\\\\MD5\\:\\:step\\(\\) has parameter \\$func with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xls/MD5.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xls\\\\MD5\\:\\:rotate\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xls/MD5.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xls\\\\RC4\\:\\:\\$s has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xls/RC4.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xls\\\\RC4\\:\\:\\$i has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xls/RC4.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xls\\\\RC4\\:\\:\\$j has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xls/RC4.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\:\\:castToBoolean\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\:\\:castToBoolean\\(\\) has parameter \\$c with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\:\\:castToError\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\:\\:castToError\\(\\) has parameter \\$c with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\:\\:castToString\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\:\\:castToString\\(\\) has parameter \\$c with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\:\\:castToFormula\\(\\) has parameter \\$c with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\:\\:castToFormula\\(\\) has parameter \\$calculatedValue with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\:\\:castToFormula\\(\\) has parameter \\$castBaseType with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\:\\:castToFormula\\(\\) has parameter \\$cellDataType with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\:\\:castToFormula\\(\\) has parameter \\$r with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\:\\:castToFormula\\(\\) has parameter \\$sharedFormulas with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\:\\:castToFormula\\(\\) has parameter \\$value with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\:\\:getFromZipArchive\\(\\) should return string but returns string\\|false\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Negated boolean expression is always true\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Argument of an invalid type array\\<int, string\\>\\|false supplied for foreach, only iterables are supported\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Parameter \\#1 \\$worksheetName of method PhpOffice\\\\PhpSpreadsheet\\\\Spreadsheet\\:\\:getSheetByName\\(\\) expects string, array\\|string given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Cannot access offset 0 on array\\<int, string\\>\\|false\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Cannot call method addChart\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\Worksheet\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Cannot access property \\$r on SimpleXMLElement\\|null\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Cannot call method setName\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Cannot call method setSize\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Cannot call method setColor\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Cannot call method setBold\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Cannot call method setItalic\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Cannot call method setSuperscript\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Cannot call method setSubscript\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Cannot call method setUnderline\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font\\|null\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Cannot call method setStrikethrough\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\:\\:getArrayItem\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\:\\:getArrayItem\\(\\) has parameter \\$array with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\:\\:getArrayItem\\(\\) has parameter \\$key with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\:\\:dirAdd\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\:\\:dirAdd\\(\\) has parameter \\$add with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\:\\:dirAdd\\(\\) has parameter \\$base with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\:\\:toCSSArray\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\:\\:toCSSArray\\(\\) has parameter \\$style with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Parameter \\#1 \\$fontSizeInPoints of static method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Font\\:\\:fontSizeToPixels\\(\\) expects int, string given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Parameter \\#1 \\$haystack of function strpos expects string, int\\|string given\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Parameter \\#3 \\$subject of function str_replace expects array\\|string, int\\|string given\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Parameter \\#1 \\$sizeInInch of static method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Font\\:\\:inchSizeToPixels\\(\\) expects int, string given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Parameter \\#1 \\$sizeInCm of static method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Font\\:\\:centimeterSizeToPixels\\(\\) expects int, string given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\:\\:stripWhiteSpaceFromStyleString\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\:\\:stripWhiteSpaceFromStyleString\\(\\) has parameter \\$string with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\:\\:boolean\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\:\\:boolean\\(\\) has parameter \\$value with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\:\\:readFormControlProperties\\(\\) has parameter \\$dir with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\:\\:readFormControlProperties\\(\\) has parameter \\$docSheet with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\:\\:readFormControlProperties\\(\\) has parameter \\$fileWorksheet with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\:\\:readPrinterSettings\\(\\) has parameter \\$dir with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\:\\:readPrinterSettings\\(\\) has parameter \\$docSheet with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\:\\:readPrinterSettings\\(\\) has parameter \\$fileWorksheet with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\AutoFilter\\:\\:\\$worksheet has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/AutoFilter.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\AutoFilter\\:\\:\\$worksheetXml has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/AutoFilter.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\AutoFilter\\:\\:readAutoFilter\\(\\) has parameter \\$autoFilterRange with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/AutoFilter.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\AutoFilter\\:\\:readAutoFilter\\(\\) has parameter \\$xmlSheet with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/AutoFilter.php

		-
			message: "#^Parameter \\#1 \\$pOperator of method PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\AutoFilter\\\\Column\\\\Rule\\:\\:setRule\\(\\) expects string, null given\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Reader/Xlsx/AutoFilter.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\BaseParserClass\\:\\:boolean\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/BaseParserClass.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\BaseParserClass\\:\\:boolean\\(\\) has parameter \\$value with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/BaseParserClass.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\Chart\\:\\:readColor\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/Chart.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\Chart\\:\\:readColor\\(\\) has parameter \\$background with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/Chart.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\Chart\\:\\:readColor\\(\\) has parameter \\$color with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/Chart.php

		-
			message: "#^Parameter \\#1 \\$position of class PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Legend constructor expects string, bool\\|float\\|int\\|string\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/Chart.php

		-
			message: "#^Parameter \\#3 \\$overlay of class PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Legend constructor expects bool, bool\\|float\\|int\\|string\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/Chart.php

		-
			message: "#^Parameter \\#6 \\$displayBlanksAs of class PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Chart constructor expects string, bool\\|float\\|int\\|string\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/Chart.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\Chart\\:\\:chartTitle\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/Chart.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\Chart\\:\\:chartLayoutDetails\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/Chart.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\Chart\\:\\:chartLayoutDetails\\(\\) has parameter \\$chartDetail with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/Chart.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\Chart\\:\\:chartLayoutDetails\\(\\) has parameter \\$namespacesChartMeta with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/Chart.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\Chart\\:\\:chartDataSeries\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/Chart.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\Chart\\:\\:chartDataSeries\\(\\) has parameter \\$chartDetail with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/Chart.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\Chart\\:\\:chartDataSeries\\(\\) has parameter \\$namespacesChartMeta with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/Chart.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\Chart\\:\\:chartDataSeries\\(\\) has parameter \\$plotType with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/Chart.php

		-
			message: "#^Parameter \\#3 \\$plotOrder of class PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\DataSeries constructor expects array\\<int\\>, array\\<int\\|string, bool\\|float\\|int\\|string\\|null\\> given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/Chart.php

		-
			message: "#^Parameter \\#7 \\$plotDirection of class PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\DataSeries constructor expects string\\|null, bool\\|float\\|int\\|string\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/Chart.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\Chart\\:\\:chartDataSeriesValueSet\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/Chart.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\Chart\\:\\:chartDataSeriesValueSet\\(\\) has parameter \\$marker with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/Chart.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\Chart\\:\\:chartDataSeriesValueSet\\(\\) has parameter \\$namespacesChartMeta with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/Chart.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\Chart\\:\\:chartDataSeriesValueSet\\(\\) has parameter \\$seriesDetail with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/Chart.php

		-
			message: "#^Parameter \\#4 \\$pointCount of class PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\DataSeriesValues constructor expects int, null given\\.$#"
			count: 4
			path: src/PhpSpreadsheet/Reader/Xlsx/Chart.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\Chart\\:\\:chartDataSeriesValues\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/Chart.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\Chart\\:\\:chartDataSeriesValues\\(\\) has parameter \\$dataType with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/Chart.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\Chart\\:\\:chartDataSeriesValues\\(\\) has parameter \\$seriesValueSet with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/Chart.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\Chart\\:\\:chartDataSeriesValuesMultiLevel\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/Chart.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\Chart\\:\\:chartDataSeriesValuesMultiLevel\\(\\) has parameter \\$dataType with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/Chart.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\Chart\\:\\:chartDataSeriesValuesMultiLevel\\(\\) has parameter \\$seriesValueSet with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/Chart.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\Chart\\:\\:parseRichText\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/Chart.php

		-
			message: "#^Cannot call method getFont\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\RichText\\\\Run\\|null\\.$#"
			count: 12
			path: src/PhpSpreadsheet/Reader/Xlsx/Chart.php

		-
			message: "#^Cannot call method setName\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/Chart.php

		-
			message: "#^Cannot call method setSize\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/Chart.php

		-
			message: "#^Cannot call method setColor\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/Chart.php

		-
			message: "#^Cannot call method setBold\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/Chart.php

		-
			message: "#^Cannot call method setItalic\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/Chart.php

		-
			message: "#^Cannot call method setSuperscript\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/Chart.php

		-
			message: "#^Cannot call method setSubscript\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/Chart.php

		-
			message: "#^Cannot call method setUnderline\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font\\|null\\.$#"
			count: 3
			path: src/PhpSpreadsheet/Reader/Xlsx/Chart.php

		-
			message: "#^Cannot call method setStrikethrough\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font\\|null\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Reader/Xlsx/Chart.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\Chart\\:\\:readChartAttributes\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/Chart.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\Chart\\:\\:readChartAttributes\\(\\) has parameter \\$chartDetail with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/Chart.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\ColumnAndRowAttributes\\:\\:\\$worksheet has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/ColumnAndRowAttributes.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\ColumnAndRowAttributes\\:\\:\\$worksheetXml has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/ColumnAndRowAttributes.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\ColumnAndRowAttributes\\:\\:isFilteredColumn\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/ColumnAndRowAttributes.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\ColumnAndRowAttributes\\:\\:isFilteredColumn\\(\\) has parameter \\$columnCoordinate with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/ColumnAndRowAttributes.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\ColumnAndRowAttributes\\:\\:readColumnAttributes\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/ColumnAndRowAttributes.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\ColumnAndRowAttributes\\:\\:readColumnAttributes\\(\\) has parameter \\$readDataOnly with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/ColumnAndRowAttributes.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\ColumnAndRowAttributes\\:\\:readColumnRangeAttributes\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/ColumnAndRowAttributes.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\ColumnAndRowAttributes\\:\\:readColumnRangeAttributes\\(\\) has parameter \\$readDataOnly with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/ColumnAndRowAttributes.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\ColumnAndRowAttributes\\:\\:isFilteredRow\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/ColumnAndRowAttributes.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\ColumnAndRowAttributes\\:\\:isFilteredRow\\(\\) has parameter \\$rowCoordinate with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/ColumnAndRowAttributes.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\ColumnAndRowAttributes\\:\\:readRowAttributes\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/ColumnAndRowAttributes.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\ColumnAndRowAttributes\\:\\:readRowAttributes\\(\\) has parameter \\$readDataOnly with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/ColumnAndRowAttributes.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\ConditionalStyles\\:\\:\\$worksheet has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/ConditionalStyles.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\ConditionalStyles\\:\\:\\$worksheetXml has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/ConditionalStyles.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\ConditionalStyles\\:\\:\\$dxfs has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/ConditionalStyles.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\ConditionalStyles\\:\\:readConditionalStyles\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/ConditionalStyles.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\ConditionalStyles\\:\\:readConditionalStyles\\(\\) has parameter \\$xmlSheet with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/ConditionalStyles.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\ConditionalStyles\\:\\:setConditionalStyles\\(\\) has parameter \\$xmlExtLst with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/ConditionalStyles.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\ConditionalStyles\\:\\:readStyleRules\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/ConditionalStyles.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\ConditionalStyles\\:\\:readStyleRules\\(\\) has parameter \\$cfRules with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/ConditionalStyles.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\ConditionalStyles\\:\\:readStyleRules\\(\\) has parameter \\$extLst with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/ConditionalStyles.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\ConditionalStyles\\:\\:readDataBarOfConditionalRule\\(\\) has parameter \\$cfRule with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/ConditionalStyles.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\ConditionalStyles\\:\\:readDataBarOfConditionalRule\\(\\) has parameter \\$conditionalFormattingRuleExtensions with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/ConditionalStyles.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\ConditionalStyles\\:\\:readDataBarExtLstOfConditionalRule\\(\\) has parameter \\$cfRule with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/ConditionalStyles.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\ConditionalStyles\\:\\:readDataBarExtLstOfConditionalRule\\(\\) has parameter \\$conditionalFormattingRuleExtensions with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/ConditionalStyles.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\DataValidations\\:\\:\\$worksheet has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/DataValidations.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\DataValidations\\:\\:\\$worksheetXml has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/DataValidations.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\Hyperlinks\\:\\:\\$worksheet has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/Hyperlinks.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\Hyperlinks\\:\\:\\$hyperlinks has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/Hyperlinks.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\PageSetup\\:\\:\\$worksheet has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/PageSetup.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\PageSetup\\:\\:\\$worksheetXml has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/PageSetup.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\PageSetup\\:\\:load\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/PageSetup.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\PageSetup\\:\\:pageSetup\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/PageSetup.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\SheetViewOptions\\:\\:\\$worksheet has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/SheetViewOptions.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\SheetViewOptions\\:\\:\\$worksheetXml has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/SheetViewOptions.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\Styles\\:\\:\\$styles has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/Styles.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\Styles\\:\\:\\$cellStyles has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/Styles.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\Styles\\:\\:\\$styleXml has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/Styles.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\Styles\\:\\:setStyleBaseData\\(\\) has parameter \\$cellStyles with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/Styles.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\Styles\\:\\:setStyleBaseData\\(\\) has parameter \\$styles with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/Styles.php

		-
			message: "#^Static property PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\Styles\\:\\:\\$theme \\(PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\Theme\\) does not accept PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\Theme\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/Styles.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\Styles\\:\\:readStyle\\(\\) has parameter \\$style with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/Styles.php

		-
			message: "#^Parameter \\#2 \\$alignmentXml of static method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\Styles\\:\\:readAlignmentStyle\\(\\) expects SimpleXMLElement, object given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/Styles.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\Styles\\:\\:readProtectionLocked\\(\\) has parameter \\$style with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/Styles.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\Styles\\:\\:readProtectionHidden\\(\\) has parameter \\$style with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/Styles.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\Styles\\:\\:readColor\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/Styles.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\Styles\\:\\:readColor\\(\\) has parameter \\$background with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/Styles.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\Styles\\:\\:readColor\\(\\) has parameter \\$color with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/Styles.php

		-
			message: "#^Parameter \\#1 \\$hexColourValue of static method PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Color\\:\\:changeBrightness\\(\\) expects string, string\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/Styles.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\Styles\\:\\:dxfs\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/Styles.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\Styles\\:\\:dxfs\\(\\) has parameter \\$readDataOnly with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/Styles.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\Styles\\:\\:styles\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/Styles.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\Styles\\:\\:getArrayItem\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/Styles.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\Styles\\:\\:getArrayItem\\(\\) has parameter \\$array with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/Styles.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\Styles\\:\\:getArrayItem\\(\\) has parameter \\$key with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/Styles.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xml\\:\\:\\$fileContents has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xml.php

		-
			message: "#^Parameter \\#1 \\$haystack of function strpos expects string, string\\|false given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xml.php

		-
			message: "#^Parameter \\#2 \\$subject of function preg_match expects string, string\\|false given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xml.php

		-
			message: "#^Parameter \\#1 \\$textValue of static method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\StringHelper\\:\\:convertEncoding\\(\\) expects string, string\\|false given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xml.php

		-
			message: "#^Parameter \\#2 \\$cmp_function of function uksort expects callable\\(\\(int\\|string\\), \\(int\\|string\\)\\)\\: int, array\\('self', 'cellReverseSort'\\) given\\.$#"
			count: 4
			path: src/PhpSpreadsheet/ReferenceHelper.php

		-
			message: "#^Parameter \\#2 \\$cmp_function of function uksort expects callable\\(\\(int\\|string\\), \\(int\\|string\\)\\)\\: int, array\\('self', 'cellSort'\\) given\\.$#"
			count: 4
			path: src/PhpSpreadsheet/ReferenceHelper.php

		-
			message: "#^Parameter \\#1 \\$index of method PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\RowDimension\\:\\:setRowIndex\\(\\) expects int, string given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/ReferenceHelper.php

		-
			message: "#^Parameter \\#3 \\$subject of function str_replace expects array\\|string, string\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/ReferenceHelper.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\RichText\\\\Run\\:\\:\\$font \\(PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font\\) does not accept PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/RichText/Run.php

		-
			message: "#^Result of && is always false\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Settings.php

		-
			message: "#^Strict comparison using \\=\\=\\= between int and null will always evaluate to false\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Settings.php

		-
			message: "#^Negated boolean expression is always false\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Settings.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Settings\\:\\:getHttpClient\\(\\) should return Psr\\\\Http\\\\Client\\\\ClientInterface but returns Psr\\\\Http\\\\Client\\\\ClientInterface\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Settings.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Settings\\:\\:getRequestFactory\\(\\) should return Psr\\\\Http\\\\Message\\\\RequestFactoryInterface but returns Psr\\\\Http\\\\Message\\\\RequestFactoryInterface\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Settings.php

		-
			message: "#^Parameter \\#1 \\$unixTimestamp of static method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Date\\:\\:timestampToExcel\\(\\) expects int, float\\|int\\|string given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Date.php

		-
			message: "#^Parameter \\#1 \\$string of function substr expects string, int given\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Shared/Date.php

		-
			message: "#^Parameter \\#1 \\$excelFormatCode of static method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Date\\:\\:isDateTimeFormatCode\\(\\) expects string, string\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Date.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Date\\:\\:\\$possibleDateFormatCharacters has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Date.php

		-
			message: "#^Parameter \\#1 \\$fp of function fread expects resource, resource\\|false given\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Shared/Drawing.php

		-
			message: "#^Parameter \\#1 \\$fp of function feof expects resource, resource\\|false given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Drawing.php

		-
			message: "#^Parameter \\#2 \\$data of function unpack expects string, string\\|false given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Drawing.php

		-
			message: "#^Cannot access offset 1 on array\\|false\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Drawing.php

		-
			message: "#^Parameter \\#1 \\$x_size of function imagecreatetruecolor expects int, float\\|int given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Drawing.php

		-
			message: "#^Parameter \\#2 \\$y_size of function imagecreatetruecolor expects int, float\\|int given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Drawing.php

		-
			message: "#^Parameter \\#1 \\$im of function imagecolorallocate expects resource, resource\\|false given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Drawing.php

		-
			message: "#^Parameter \\#2 \\$red of function imagecolorallocate expects int, float\\|int given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Drawing.php

		-
			message: "#^Parameter \\#3 \\$green of function imagecolorallocate expects int, float\\|int given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Drawing.php

		-
			message: "#^Parameter \\#4 \\$blue of function imagecolorallocate expects int, float\\|int given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Drawing.php

		-
			message: "#^Parameter \\#1 \\$im of function imagesetpixel expects resource, resource\\|false given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Drawing.php

		-
			message: "#^Parameter \\#3 \\$y of function imagesetpixel expects int, float\\|int given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Drawing.php

		-
			message: "#^Parameter \\#4 \\$col of function imagesetpixel expects int, int\\|false given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Drawing.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Drawing\\:\\:imagecreatefrombmp\\(\\) should return GdImage\\|resource but returns resource\\|false\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Drawing.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Escher\\\\DgContainer\\:\\:\\$spgrContainer has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Escher/DgContainer.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Escher\\\\DgContainer\\:\\:getDgId\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Escher/DgContainer.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Escher\\\\DgContainer\\:\\:setDgId\\(\\) has parameter \\$value with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Escher/DgContainer.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Escher\\\\DgContainer\\:\\:getLastSpId\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Escher/DgContainer.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Escher\\\\DgContainer\\:\\:setLastSpId\\(\\) has parameter \\$value with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Escher/DgContainer.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Escher\\\\DgContainer\\:\\:getSpgrContainer\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Escher/DgContainer.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Escher\\\\DgContainer\\:\\:setSpgrContainer\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Escher/DgContainer.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Escher\\\\DgContainer\\:\\:setSpgrContainer\\(\\) has parameter \\$spgrContainer with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Escher/DgContainer.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Escher\\\\DgContainer\\\\SpgrContainer\\:\\:getChildren\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Escher/DgContainer/SpgrContainer.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Font\\:\\:\\$autoSizeMethods has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Font.php

		-
			message: "#^Parameter \\#2 \\$defaultFont of static method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Drawing\\:\\:pixelsToCellDimension\\(\\) expects PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font, PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Font.php

		-
			message: "#^Parameter \\#1 \\$size of function imagettfbbox expects float, float\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Font.php

		-
			message: "#^Cannot access offset 0 on array\\|false\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Font.php

		-
			message: "#^Cannot access offset 2 on array\\|false\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Font.php

		-
			message: "#^Cannot access offset 4 on array\\|false\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Font.php

		-
			message: "#^Cannot access offset 6 on array\\|false\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Font.php

		-
			message: "#^Unreachable statement \\- code above always terminates\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Font.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\JAMA\\\\EigenvalueDecomposition\\:\\:\\$e has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/JAMA/EigenvalueDecomposition.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\JAMA\\\\EigenvalueDecomposition\\:\\:\\$cdivi has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/JAMA/EigenvalueDecomposition.php

		-
			message: "#^Else branch is unreachable because previous condition is always true\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/JAMA/LUDecomposition.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\JAMA\\\\LUDecomposition\\:\\:getDoublePivot\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/JAMA/LUDecomposition.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\JAMA\\\\Matrix\\:\\:__construct\\(\\) has parameter \\$args with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/JAMA/Matrix.php

		-
			message: "#^Unreachable statement \\- code above always terminates\\.$#"
			count: 19
			path: src/PhpSpreadsheet/Shared/JAMA/Matrix.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\JAMA\\\\Matrix\\:\\:getMatrix\\(\\) has parameter \\$args with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/JAMA/Matrix.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\JAMA\\\\Matrix\\:\\:plus\\(\\) has parameter \\$args with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/JAMA/Matrix.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\JAMA\\\\Matrix\\:\\:plusEquals\\(\\) has parameter \\$args with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/JAMA/Matrix.php

		-
			message: "#^Call to function is_string\\(\\) with float\\|int will always evaluate to false\\.$#"
			count: 5
			path: src/PhpSpreadsheet/Shared/JAMA/Matrix.php

		-
			message: "#^Result of && is always false\\.$#"
			count: 10
			path: src/PhpSpreadsheet/Shared/JAMA/Matrix.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\JAMA\\\\Matrix\\:\\:minus\\(\\) has parameter \\$args with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/JAMA/Matrix.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\JAMA\\\\Matrix\\:\\:minusEquals\\(\\) has parameter \\$args with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/JAMA/Matrix.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\JAMA\\\\Matrix\\:\\:arrayTimes\\(\\) has parameter \\$args with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/JAMA/Matrix.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\JAMA\\\\Matrix\\:\\:arrayTimesEquals\\(\\) has parameter \\$args with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/JAMA/Matrix.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\JAMA\\\\Matrix\\:\\:arrayRightDivide\\(\\) has parameter \\$args with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/JAMA/Matrix.php

		-
			message: "#^Parameter \\#3 \\$c of method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\JAMA\\\\Matrix\\:\\:set\\(\\) expects float\\|int\\|null, string given\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Shared/JAMA/Matrix.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\JAMA\\\\Matrix\\:\\:arrayRightDivideEquals\\(\\) has parameter \\$args with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/JAMA/Matrix.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\JAMA\\\\Matrix\\:\\:arrayLeftDivide\\(\\) has parameter \\$args with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/JAMA/Matrix.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\JAMA\\\\Matrix\\:\\:arrayLeftDivideEquals\\(\\) has parameter \\$args with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/JAMA/Matrix.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\JAMA\\\\Matrix\\:\\:times\\(\\) has parameter \\$args with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/JAMA/Matrix.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\JAMA\\\\Matrix\\:\\:power\\(\\) has parameter \\$args with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/JAMA/Matrix.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\JAMA\\\\Matrix\\:\\:concat\\(\\) has parameter \\$args with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/JAMA/Matrix.php

		-
			message: "#^Parameter \\#1 \\$str of function trim expects string, float\\|int given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/JAMA/Matrix.php

		-
			message: "#^Left side of && is always true\\.$#"
			count: 4
			path: src/PhpSpreadsheet/Shared/JAMA/SingularValueDecomposition.php

		-
			message: "#^If condition is always true\\.$#"
			count: 7
			path: src/PhpSpreadsheet/Shared/JAMA/SingularValueDecomposition.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\OLE\\:\\:getStream\\(\\) should return resource but returns resource\\|false\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/OLE.php

		-
			message: "#^Parameter \\#2 \\$data of function unpack expects string, string\\|false given\\.$#"
			count: 3
			path: src/PhpSpreadsheet/Shared/OLE.php

		-
			message: "#^Parameter \\#1 \\$string of function substr expects string, string\\|false given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/OLE.php

		-
			message: "#^Parameter \\#1 \\$No of class PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\OLE\\\\PPS constructor expects int, null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/OLE.php

		-
			message: "#^Parameter \\#2 \\$name of class PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\OLE\\\\PPS constructor expects string, null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/OLE.php

		-
			message: "#^Parameter \\#3 \\$type of class PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\OLE\\\\PPS constructor expects int, null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/OLE.php

		-
			message: "#^Parameter \\#4 \\$prev of class PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\OLE\\\\PPS constructor expects int, null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/OLE.php

		-
			message: "#^Parameter \\#5 \\$next of class PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\OLE\\\\PPS constructor expects int, null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/OLE.php

		-
			message: "#^Parameter \\#6 \\$dir of class PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\OLE\\\\PPS constructor expects int, null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/OLE.php

		-
			message: "#^Parameter \\#9 \\$data of class PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\OLE\\\\PPS constructor expects string, null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/OLE.php

		-
			message: "#^Parameter \\#1 \\$oleTimestamp of static method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\OLE\\:\\:OLE2LocalDate\\(\\) expects string, string\\|false given\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Shared/OLE.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\OLE\\:\\:getData\\(\\) should return string but returns string\\|false\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/OLE.php

		-
			message: "#^Cannot access offset 3 on array\\|false\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/OLE.php

		-
			message: "#^Cannot access offset 4 on array\\|false\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/OLE.php

		-
			message: "#^Cannot access offset 1 on array\\|false\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/OLE.php

		-
			message: "#^Cannot access offset 2 on array\\|false\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/OLE.php

		-
			message: "#^Parameter \\#1 \\$var of function count expects array\\|Countable, string given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/OLE/ChainedBlockStream.php

		-
			message: "#^Parameter \\#3 \\$length of function array_slice expects int\\|null, float given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/OLE/PPS.php

		-
			message: "#^Parameter \\#2 \\$offset of function array_slice expects int, float given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/OLE/PPS.php

		-
			message: "#^Parameter \\#1 \\$No of method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\OLE\\\\PPS\\:\\:__construct\\(\\) expects int, null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/OLE/PPS/File.php

		-
			message: "#^Parameter \\#4 \\$prev of method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\OLE\\\\PPS\\:\\:__construct\\(\\) expects int, null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/OLE/PPS/File.php

		-
			message: "#^Parameter \\#5 \\$next of method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\OLE\\\\PPS\\:\\:__construct\\(\\) expects int, null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/OLE/PPS/File.php

		-
			message: "#^Parameter \\#6 \\$dir of method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\OLE\\\\PPS\\:\\:__construct\\(\\) expects int, null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/OLE/PPS/File.php

		-
			message: "#^Parameter \\#1 \\$No of method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\OLE\\\\PPS\\:\\:__construct\\(\\) expects int, null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/OLE/PPS/Root.php

		-
			message: "#^Parameter \\#4 \\$prev of method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\OLE\\\\PPS\\:\\:__construct\\(\\) expects int, null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/OLE/PPS/Root.php

		-
			message: "#^Parameter \\#5 \\$next of method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\OLE\\\\PPS\\:\\:__construct\\(\\) expects int, null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/OLE/PPS/Root.php

		-
			message: "#^Parameter \\#6 \\$dir of method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\OLE\\\\PPS\\:\\:__construct\\(\\) expects int, null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/OLE/PPS/Root.php

		-
			message: "#^Parameter \\#9 \\$data of method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\OLE\\\\PPS\\:\\:__construct\\(\\) expects string, null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/OLE/PPS/Root.php

		-
			message: "#^Parameter \\#1 \\$iSBDcnt of method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\OLE\\\\PPS\\\\Root\\:\\:saveHeader\\(\\) expects int, float given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/OLE/PPS/Root.php

		-
			message: "#^Parameter \\#2 \\$iBBcnt of method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\OLE\\\\PPS\\\\Root\\:\\:saveHeader\\(\\) expects int, float given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/OLE/PPS/Root.php

		-
			message: "#^Parameter \\#3 \\$iPPScnt of method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\OLE\\\\PPS\\\\Root\\:\\:saveHeader\\(\\) expects int, float given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/OLE/PPS/Root.php

		-
			message: "#^Parameter \\#1 \\$iStBlk of method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\OLE\\\\PPS\\\\Root\\:\\:saveBigData\\(\\) expects int, float given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/OLE/PPS/Root.php

		-
			message: "#^Parameter \\#1 \\$iSbdSize of method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\OLE\\\\PPS\\\\Root\\:\\:saveBbd\\(\\) expects int, float given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/OLE/PPS/Root.php

		-
			message: "#^Parameter \\#2 \\$iBsize of method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\OLE\\\\PPS\\\\Root\\:\\:saveBbd\\(\\) expects int, float given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/OLE/PPS/Root.php

		-
			message: "#^Parameter \\#3 \\$iPpsCnt of method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\OLE\\\\PPS\\\\Root\\:\\:saveBbd\\(\\) expects int, float given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/OLE/PPS/Root.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\OLERead\\:\\:\\$data has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/OLERead.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\OLERead\\:\\:\\$wrkbook has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/OLERead.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\OLERead\\:\\:\\$summaryInformation has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/OLERead.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\OLERead\\:\\:\\$documentSummaryInformation has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/OLERead.php

		-
			message: "#^Parameter \\#1 \\$data of static method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\OLERead\\:\\:getInt4d\\(\\) expects string, string\\|false given\\.$#"
			count: 8
			path: src/PhpSpreadsheet/Shared/OLERead.php

		-
			message: "#^Parameter \\#1 \\$string of function substr expects string, string\\|false given\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Shared/OLERead.php

		-
			message: "#^Strict comparison using \\=\\=\\= between int and null will always evaluate to false\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/OLERead.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\StringHelper\\:\\:sanitizeUTF8\\(\\) should return string but returns string\\|false\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/StringHelper.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\StringHelper\\:\\:formatNumber\\(\\) should return string but returns array\\|string\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/StringHelper.php

		-
			message: "#^Parameter \\#3 \\$subject of function str_replace expects array\\|string, float given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/StringHelper.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\StringHelper\\:\\:countCharacters\\(\\) should return int but returns int\\|false\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/StringHelper.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\StringHelper\\:\\:mbIsUpper\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/StringHelper.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\StringHelper\\:\\:mbIsUpper\\(\\) has parameter \\$character with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/StringHelper.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\StringHelper\\:\\:mbStrSplit\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/StringHelper.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\StringHelper\\:\\:mbStrSplit\\(\\) has parameter \\$string with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/StringHelper.php

		-
			message: "#^Parameter \\#1 \\$string of function strlen expects string, float given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/StringHelper.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Trend\\\\BestFit\\:\\:\\$goodnessOfFit has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Trend/BestFit.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Trend\\\\BestFit\\:\\:\\$stdevOfResiduals has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Trend/BestFit.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Trend\\\\BestFit\\:\\:\\$covariance has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Trend/BestFit.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Trend\\\\BestFit\\:\\:\\$correlation has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Trend/BestFit.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Trend\\\\BestFit\\:\\:\\$SSRegression has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Trend/BestFit.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Trend\\\\BestFit\\:\\:\\$SSResiduals has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Trend/BestFit.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Trend\\\\BestFit\\:\\:\\$DFResiduals has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Trend/BestFit.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Trend\\\\BestFit\\:\\:\\$f has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Trend/BestFit.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Trend\\\\BestFit\\:\\:\\$slope has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Trend/BestFit.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Trend\\\\BestFit\\:\\:\\$slopeSE has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Trend/BestFit.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Trend\\\\BestFit\\:\\:\\$intersect has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Trend/BestFit.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Trend\\\\BestFit\\:\\:\\$intersectSE has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Trend/BestFit.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Trend\\\\BestFit\\:\\:\\$xOffset has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Trend/BestFit.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Trend\\\\BestFit\\:\\:\\$yOffset has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Trend/BestFit.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Trend\\\\BestFit\\:\\:getError\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Trend/BestFit.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Trend\\\\BestFit\\:\\:getBestFitType\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Trend/BestFit.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Trend\\\\BestFit\\:\\:calculateGoodnessOfFit\\(\\) has parameter \\$const with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Trend/BestFit.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Trend\\\\BestFit\\:\\:calculateGoodnessOfFit\\(\\) has parameter \\$meanX with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Trend/BestFit.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Trend\\\\BestFit\\:\\:calculateGoodnessOfFit\\(\\) has parameter \\$meanY with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Trend/BestFit.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Trend\\\\BestFit\\:\\:calculateGoodnessOfFit\\(\\) has parameter \\$sumX with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Trend/BestFit.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Trend\\\\BestFit\\:\\:calculateGoodnessOfFit\\(\\) has parameter \\$sumX2 with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Trend/BestFit.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Trend\\\\BestFit\\:\\:calculateGoodnessOfFit\\(\\) has parameter \\$sumXY with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Trend/BestFit.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Trend\\\\BestFit\\:\\:calculateGoodnessOfFit\\(\\) has parameter \\$sumY with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Trend/BestFit.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Trend\\\\BestFit\\:\\:calculateGoodnessOfFit\\(\\) has parameter \\$sumY2 with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Trend/BestFit.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Trend\\\\BestFit\\:\\:sumSquares\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Trend/BestFit.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Trend\\\\PolynomialBestFit\\:\\:getCoefficients\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Trend/PolynomialBestFit.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Trend\\\\PolynomialBestFit\\:\\:getCoefficients\\(\\) has parameter \\$dp with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Trend/PolynomialBestFit.php

		-
			message: "#^Parameter \\#2 \\.\\.\\.\\$args of function array_merge expects array, float given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Trend/PolynomialBestFit.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Trend\\\\Trend\\:\\:calculate\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Trend/Trend.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Trend\\\\Trend\\:\\:calculate\\(\\) has parameter \\$const with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Trend/Trend.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Trend\\\\Trend\\:\\:calculate\\(\\) has parameter \\$trendType with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Trend/Trend.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Trend\\\\Trend\\:\\:calculate\\(\\) has parameter \\$xValues with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Trend/Trend.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Trend\\\\Trend\\:\\:calculate\\(\\) has parameter \\$yValues with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Trend/Trend.php

		-
			message: "#^Parameter \\#1 \\$order of class PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Trend\\\\PolynomialBestFit constructor expects int, string given\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Shared/Trend/Trend.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\XMLWriter\\:\\:\\$debugEnabled has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/XMLWriter.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\XMLWriter\\:\\:\\$tempFileName \\(string\\) does not accept string\\|false\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/XMLWriter.php

		-
			message: "#^Parameter \\#1 \\$uri of method XMLWriter\\:\\:openUri\\(\\) expects string, string\\|false given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/XMLWriter.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\XMLWriter\\:\\:getData\\(\\) should return string but returns string\\|false\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/XMLWriter.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Spreadsheet\\:\\:\\$workbookViewVisibilityValues has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Spreadsheet.php

		-
			message: "#^Call to function is_array\\(\\) with string will always evaluate to false\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Spreadsheet.php

		-
			message: "#^Unreachable statement \\- code above always terminates\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Spreadsheet.php

		-
			message: "#^Strict comparison using \\=\\=\\= between PhpOffice\\\\PhpSpreadsheet\\\\Spreadsheet and null will always evaluate to false\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Spreadsheet.php

		-
			message: "#^Parameter \\#1 \\$worksheet of method PhpOffice\\\\PhpSpreadsheet\\\\Spreadsheet\\:\\:getIndex\\(\\) expects PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\Worksheet, PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\Worksheet\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Spreadsheet.php

		-
			message: "#^Cannot call method getTitle\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\Worksheet\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Spreadsheet.php

		-
			message: "#^Strict comparison using \\=\\=\\= between string and null will always evaluate to false\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Spreadsheet.php

		-
			message: "#^Comparison operation \"\\<\\=\" between int\\<min, \\-1\\> and 1000 is always true\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Spreadsheet.php

		-
			message: "#^Result of \\|\\| is always true\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Spreadsheet.php

		-
			message: "#^Call to an undefined method PhpOffice\\\\PhpSpreadsheet\\\\Spreadsheet\\|PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Style\\:\\:getSharedComponent\\(\\)\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/Alignment.php

		-
			message: "#^Parameter \\#1 \\$parent of method PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Supervisor\\:\\:bindParent\\(\\) expects PhpOffice\\\\PhpSpreadsheet\\\\Spreadsheet\\|PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Style, \\$this\\(PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Border\\) given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/Border.php

		-
			message: "#^Call to an undefined method PhpOffice\\\\PhpSpreadsheet\\\\Spreadsheet\\|PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Style\\:\\:getSharedComponent\\(\\)\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/Border.php

		-
			message: "#^Call to an undefined method PhpOffice\\\\PhpSpreadsheet\\\\Spreadsheet\\|PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Style\\:\\:getStyleArray\\(\\)\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/Border.php

		-
			message: "#^Parameter \\#1 \\$parent of method PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Supervisor\\:\\:bindParent\\(\\) expects PhpOffice\\\\PhpSpreadsheet\\\\Spreadsheet\\|PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Style, \\$this\\(PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Borders\\) given\\.$#"
			count: 10
			path: src/PhpSpreadsheet/Style/Borders.php

		-
			message: "#^Call to an undefined method PhpOffice\\\\PhpSpreadsheet\\\\Spreadsheet\\|PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Style\\:\\:getSharedComponent\\(\\)\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/Borders.php

		-
			message: "#^Call to an undefined method PhpOffice\\\\PhpSpreadsheet\\\\Spreadsheet\\|PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Style\\:\\:getSharedComponent\\(\\)\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/Color.php

		-
			message: "#^Call to an undefined method PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Border\\|PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Fill\\:\\:getEndColor\\(\\)\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/Color.php

		-
			message: "#^Call to an undefined method PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Border\\|PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Fill\\:\\:getStartColor\\(\\)\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/Color.php

		-
			message: "#^Call to an undefined method PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Border\\|PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Fill\\:\\:getColor\\(\\)\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/Color.php

		-
			message: "#^Call to an undefined method PhpOffice\\\\PhpSpreadsheet\\\\Spreadsheet\\|PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Style\\:\\:getStyleArray\\(\\)\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/Color.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Color\\:\\:getColourComponent\\(\\) should return int\\|string but returns float\\|int\\|string\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/Color.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Conditional\\:\\:\\$condition \\(array\\<string\\>\\) does not accept array\\<bool\\|float\\|int\\|string\\>\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/Conditional.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Conditional\\:\\:\\$style \\(PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Style\\) does not accept PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Style\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/Conditional.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\ConditionalFormatting\\\\ConditionalDataBar\\:\\:setShowValue\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/ConditionalFormatting/ConditionalDataBar.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\ConditionalFormatting\\\\ConditionalDataBar\\:\\:setMinimumConditionalFormatValueObject\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/ConditionalFormatting/ConditionalDataBar.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\ConditionalFormatting\\\\ConditionalDataBar\\:\\:setMaximumConditionalFormatValueObject\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/ConditionalFormatting/ConditionalDataBar.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\ConditionalFormatting\\\\ConditionalDataBar\\:\\:setConditionalFormattingRuleExt\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/ConditionalFormatting/ConditionalDataBar.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\ConditionalFormatting\\\\ConditionalDataBarExtension\\:\\:getXmlAttributes\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/ConditionalFormatting/ConditionalDataBarExtension.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\ConditionalFormatting\\\\ConditionalDataBarExtension\\:\\:getXmlElements\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/ConditionalFormatting/ConditionalDataBarExtension.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\ConditionalFormatting\\\\ConditionalDataBarExtension\\:\\:setMaximumConditionalFormatValueObject\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/ConditionalFormatting/ConditionalDataBarExtension.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\ConditionalFormatting\\\\ConditionalDataBarExtension\\:\\:setMinimumConditionalFormatValueObject\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/ConditionalFormatting/ConditionalDataBarExtension.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\ConditionalFormatting\\\\ConditionalFormatValueObject\\:\\:\\$type has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/ConditionalFormatting/ConditionalFormatValueObject.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\ConditionalFormatting\\\\ConditionalFormatValueObject\\:\\:\\$value has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/ConditionalFormatting/ConditionalFormatValueObject.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\ConditionalFormatting\\\\ConditionalFormatValueObject\\:\\:\\$cellFormula has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/ConditionalFormatting/ConditionalFormatValueObject.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\ConditionalFormatting\\\\ConditionalFormatValueObject\\:\\:__construct\\(\\) has parameter \\$type with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/ConditionalFormatting/ConditionalFormatValueObject.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\ConditionalFormatting\\\\ConditionalFormatValueObject\\:\\:__construct\\(\\) has parameter \\$value with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/ConditionalFormatting/ConditionalFormatValueObject.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\ConditionalFormatting\\\\ConditionalFormatValueObject\\:\\:setType\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/ConditionalFormatting/ConditionalFormatValueObject.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\ConditionalFormatting\\\\ConditionalFormatValueObject\\:\\:setValue\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/ConditionalFormatting/ConditionalFormatValueObject.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\ConditionalFormatting\\\\ConditionalFormatValueObject\\:\\:setCellFormula\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/ConditionalFormatting/ConditionalFormatValueObject.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\ConditionalFormatting\\\\ConditionalFormattingRuleExtension\\:\\:\\$id has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/ConditionalFormatting/ConditionalFormattingRuleExtension.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\ConditionalFormatting\\\\ConditionalFormattingRuleExtension\\:\\:__construct\\(\\) has parameter \\$id with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/ConditionalFormatting/ConditionalFormattingRuleExtension.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\ConditionalFormatting\\\\ConditionalFormattingRuleExtension\\:\\:generateUuid\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/ConditionalFormatting/ConditionalFormattingRuleExtension.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\ConditionalFormatting\\\\ConditionalFormattingRuleExtension\\:\\:parseExtLstXml\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/ConditionalFormatting/ConditionalFormattingRuleExtension.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\ConditionalFormatting\\\\ConditionalFormattingRuleExtension\\:\\:parseExtLstXml\\(\\) has parameter \\$extLstXml with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/ConditionalFormatting/ConditionalFormattingRuleExtension.php

		-
			message: "#^Cannot access property \\$minLength on SimpleXMLElement\\|null\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Style/ConditionalFormatting/ConditionalFormattingRuleExtension.php

		-
			message: "#^Cannot access property \\$maxLength on SimpleXMLElement\\|null\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Style/ConditionalFormatting/ConditionalFormattingRuleExtension.php

		-
			message: "#^Cannot access property \\$border on SimpleXMLElement\\|null\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Style/ConditionalFormatting/ConditionalFormattingRuleExtension.php

		-
			message: "#^Cannot access property \\$gradient on SimpleXMLElement\\|null\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Style/ConditionalFormatting/ConditionalFormattingRuleExtension.php

		-
			message: "#^Cannot access property \\$direction on SimpleXMLElement\\|null\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Style/ConditionalFormatting/ConditionalFormattingRuleExtension.php

		-
			message: "#^Cannot access property \\$negativeBarBorderColorSameAsPositive on SimpleXMLElement\\|null\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Style/ConditionalFormatting/ConditionalFormattingRuleExtension.php

		-
			message: "#^Cannot access property \\$axisPosition on SimpleXMLElement\\|null\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Style/ConditionalFormatting/ConditionalFormattingRuleExtension.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\ConditionalFormatting\\\\ConditionalFormattingRuleExtension\\:\\:parseExtDataBarElementChildrenFromXml\\(\\) has parameter \\$ns with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/ConditionalFormatting/ConditionalFormattingRuleExtension.php

		-
			message: "#^Offset 'rgb' does not exist on SimpleXMLElement\\|null\\.$#"
			count: 4
			path: src/PhpSpreadsheet/Style/ConditionalFormatting/ConditionalFormattingRuleExtension.php

		-
			message: "#^Offset 'theme' does not exist on SimpleXMLElement\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/ConditionalFormatting/ConditionalFormattingRuleExtension.php

		-
			message: "#^Offset 'tint' does not exist on SimpleXMLElement\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/ConditionalFormatting/ConditionalFormattingRuleExtension.php

		-
			message: "#^Parameter \\#1 \\$parent of method PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Supervisor\\:\\:bindParent\\(\\) expects PhpOffice\\\\PhpSpreadsheet\\\\Spreadsheet\\|PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Style, \\$this\\(PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Fill\\) given\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Style/Fill.php

		-
			message: "#^Call to an undefined method PhpOffice\\\\PhpSpreadsheet\\\\Spreadsheet\\|PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Style\\:\\:getSharedComponent\\(\\)\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/Fill.php

		-
			message: "#^Parameter \\#1 \\$parent of method PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Supervisor\\:\\:bindParent\\(\\) expects PhpOffice\\\\PhpSpreadsheet\\\\Spreadsheet\\|PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Style, \\$this\\(PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font\\) given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/Font.php

		-
			message: "#^Call to an undefined method PhpOffice\\\\PhpSpreadsheet\\\\Spreadsheet\\|PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Style\\:\\:getSharedComponent\\(\\)\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/Font.php

		-
			message: "#^Call to an undefined method PhpOffice\\\\PhpSpreadsheet\\\\Spreadsheet\\|PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Style\\:\\:getSharedComponent\\(\\)\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/NumberFormat.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\NumberFormat\\:\\:\\$builtInFormatCode \\(int\\|false\\) does not accept bool\\|int\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/NumberFormat.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\NumberFormat\\\\DateFormatter\\:\\:format\\(\\) has parameter \\$value with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/NumberFormat/DateFormatter.php

		-
			message: "#^Parameter \\#2 \\$callback of function preg_replace_callback expects callable\\(\\)\\: mixed, array\\('self', 'setLowercaseCallback'\\) given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/NumberFormat/DateFormatter.php

		-
			message: "#^Parameter \\#3 \\$subject of function preg_replace_callback expects array\\|string, string\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/NumberFormat/DateFormatter.php

		-
			message: "#^Parameter \\#2 \\$str of function explode expects string, string\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/NumberFormat/DateFormatter.php

		-
			message: "#^Parameter \\#2 \\$replace of function str_replace expects array\\|string, int given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/NumberFormat/DateFormatter.php

		-
			message: "#^Parameter \\#2 \\$callback of function preg_replace_callback expects callable\\(\\)\\: mixed, array\\('self', 'escapeQuotesCallback'\\) given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/NumberFormat/DateFormatter.php

		-
			message: "#^Parameter \\#3 \\$subject of function preg_replace expects array\\|string, string\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/NumberFormat/DateFormatter.php

		-
			message: "#^Parameter \\#1 \\$format of method DateTime\\:\\:format\\(\\) expects string, string\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/NumberFormat/DateFormatter.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\NumberFormat\\\\DateFormatter\\:\\:setLowercaseCallback\\(\\) has parameter \\$matches with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/NumberFormat/DateFormatter.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\NumberFormat\\\\DateFormatter\\:\\:escapeQuotesCallback\\(\\) has parameter \\$matches with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/NumberFormat/DateFormatter.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\NumberFormat\\\\Formatter\\:\\:splitFormatCompare\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/NumberFormat/Formatter.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\NumberFormat\\\\Formatter\\:\\:splitFormatCompare\\(\\) has parameter \\$cond with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/NumberFormat/Formatter.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\NumberFormat\\\\Formatter\\:\\:splitFormatCompare\\(\\) has parameter \\$dfcond with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/NumberFormat/Formatter.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\NumberFormat\\\\Formatter\\:\\:splitFormatCompare\\(\\) has parameter \\$dfval with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/NumberFormat/Formatter.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\NumberFormat\\\\Formatter\\:\\:splitFormatCompare\\(\\) has parameter \\$val with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/NumberFormat/Formatter.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\NumberFormat\\\\Formatter\\:\\:splitFormatCompare\\(\\) has parameter \\$value with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/NumberFormat/Formatter.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\NumberFormat\\\\Formatter\\:\\:splitFormat\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/NumberFormat/Formatter.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\NumberFormat\\\\Formatter\\:\\:splitFormat\\(\\) has parameter \\$sections with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/NumberFormat/Formatter.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\NumberFormat\\\\Formatter\\:\\:splitFormat\\(\\) has parameter \\$value with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/NumberFormat/Formatter.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\NumberFormat\\\\Formatter\\:\\:toFormattedString\\(\\) should return string but returns float\\|int\\|string\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/NumberFormat/Formatter.php

		-
			message: "#^Parameter \\#3 \\$subject of function preg_replace expects array\\|string, string\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/NumberFormat/Formatter.php

		-
			message: "#^Parameter \\#2 \\$subject of function preg_split expects string, string\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/NumberFormat/Formatter.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\NumberFormat\\\\PercentageFormatter\\:\\:format\\(\\) has parameter \\$value with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/NumberFormat/PercentageFormatter.php

		-
			message: "#^Parameter \\#1 \\$format of function sprintf expects string, string\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/NumberFormat/PercentageFormatter.php

		-
			message: "#^Call to an undefined method PhpOffice\\\\PhpSpreadsheet\\\\Spreadsheet\\|PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Style\\:\\:getSharedComponent\\(\\)\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/Protection.php

		-
			message: "#^Call to an undefined method PhpOffice\\\\PhpSpreadsheet\\\\Spreadsheet\\|PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Style\\:\\:getCellXfByIndex\\(\\)\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/Style.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Style\\:\\:getParent\\(\\) should return PhpOffice\\\\PhpSpreadsheet\\\\Spreadsheet but returns PhpOffice\\\\PhpSpreadsheet\\\\Spreadsheet\\|PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Style\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/Style.php

		-
			message: "#^Cannot call method getCell\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\Worksheet\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Worksheet/BaseDrawing.php

		-
			message: "#^Cannot call method getDrawingCollection\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\Worksheet\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Worksheet/BaseDrawing.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\BaseDrawing\\:\\:\\$shadow \\(PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\Drawing\\\\Shadow\\) does not accept PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\Drawing\\\\Shadow\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Worksheet/BaseDrawing.php

		-
			message: "#^Cannot call method getHashCode\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\Worksheet\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Worksheet/BaseDrawing.php

		-
			message: "#^Class PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\CellIterator implements generic interface Iterator but does not specify its types\\: TKey, TValue$#"
			count: 1
			path: src/PhpSpreadsheet/Worksheet/CellIterator.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\CellIterator\\:\\:adjustForExistingOnlyRange\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Worksheet/CellIterator.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\Column\\:\\:\\$parent \\(PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\Worksheet\\) does not accept PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\Worksheet\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Worksheet/Column.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\CellIterator\\:\\:\\$worksheet \\(PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\Worksheet\\) does not accept PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\Worksheet\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Worksheet/ColumnCellIterator.php

		-
			message: "#^Class PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\ColumnIterator implements generic interface Iterator but does not specify its types\\: TKey, TValue$#"
			count: 1
			path: src/PhpSpreadsheet/Worksheet/ColumnIterator.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\Drawing\\\\Shadow\\:\\:\\$color \\(PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Color\\) does not accept PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Color\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Worksheet/Drawing/Shadow.php

		-
			message: "#^Class PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\Iterator implements generic interface Iterator but does not specify its types\\: TKey, TValue$#"
			count: 1
			path: src/PhpSpreadsheet/Worksheet/Iterator.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\PageSetup\\:\\:\\$pageOrder has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Worksheet/PageSetup.php

		-
			message: "#^Strict comparison using \\=\\=\\= between int\\<min, \\-1\\> and null will always evaluate to false\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Worksheet/PageSetup.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\PageSetup\\:\\:getPrintArea\\(\\) should return string but returns string\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Worksheet/PageSetup.php

		-
			message: "#^Parameter \\#2 \\$str of function explode expects string, string\\|null given\\.$#"
			count: 5
			path: src/PhpSpreadsheet/Worksheet/PageSetup.php

		-
			message: "#^Parameter \\#1 \\$value of method PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\PageSetup\\:\\:setFirstPageNumber\\(\\) expects int, null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Worksheet/PageSetup.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\Row\\:\\:\\$worksheet \\(PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\Worksheet\\) does not accept PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\Worksheet\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Worksheet/Row.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\CellIterator\\:\\:\\$worksheet \\(PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\Worksheet\\) does not accept PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\Worksheet\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Worksheet/RowCellIterator.php

		-
			message: "#^Class PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\RowIterator implements generic interface Iterator but does not specify its types\\: TKey, TValue$#"
			count: 1
			path: src/PhpSpreadsheet/Worksheet/RowIterator.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\SheetView\\:\\:\\$sheetViewTypes has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Worksheet/SheetView.php

		-
			message: "#^Strict comparison using \\=\\=\\= between int\\<min, 0\\> and null will always evaluate to false\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Worksheet/SheetView.php

		-
			message: "#^Strict comparison using \\=\\=\\= between string and null will always evaluate to false\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Worksheet/SheetView.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\Worksheet\\:\\:\\$drawingCollection with generic class ArrayObject does not specify its types\\: TKey, TValue$#"
			count: 1
			path: src/PhpSpreadsheet/Worksheet/Worksheet.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\Worksheet\\:\\:\\$chartCollection with generic class ArrayObject does not specify its types\\: TKey, TValue$#"
			count: 1
			path: src/PhpSpreadsheet/Worksheet/Worksheet.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\Worksheet\\:\\:\\$parent \\(PhpOffice\\\\PhpSpreadsheet\\\\Spreadsheet\\) does not accept PhpOffice\\\\PhpSpreadsheet\\\\Spreadsheet\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Worksheet/Worksheet.php

		-
			message: "#^Parameter \\#1 \\$pIndex of class PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\RowDimension constructor expects int, null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Worksheet/Worksheet.php

		-
			message: "#^Parameter \\#1 \\$pIndex of class PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\ColumnDimension constructor expects string, null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Worksheet/Worksheet.php

		-
			message: "#^Parameter \\#1 \\$pRange of class PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\AutoFilter constructor expects string, null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Worksheet/Worksheet.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\Worksheet\\:\\:getDrawingCollection\\(\\) return type with generic class ArrayObject does not specify its types\\: TKey, TValue$#"
			count: 1
			path: src/PhpSpreadsheet/Worksheet/Worksheet.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\Worksheet\\:\\:getChartCollection\\(\\) return type with generic class ArrayObject does not specify its types\\: TKey, TValue$#"
			count: 1
			path: src/PhpSpreadsheet/Worksheet/Worksheet.php

		-
			message: "#^Parameter \\#1 \\$input of function array_splice expects array, ArrayObject&iterable\\<PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Chart\\> given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Worksheet/Worksheet.php

		-
			message: "#^Strict comparison using \\=\\=\\= between string and null will always evaluate to false\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Worksheet/Worksheet.php

		-
			message: "#^Parameter \\#1 \\$range of static method PhpOffice\\\\PhpSpreadsheet\\\\Cell\\\\Coordinate\\:\\:rangeDimension\\(\\) expects string, string\\|false given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Worksheet/Worksheet.php

		-
			message: "#^Parameter \\#2 \\$format of static method PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\NumberFormat\\:\\:toFormattedString\\(\\) expects string, string\\|null given\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Worksheet/Worksheet.php

		-
			message: "#^Parameter \\#3 \\$rotation of static method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Font\\:\\:calculateColumnWidth\\(\\) expects int, int\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Worksheet/Worksheet.php

		-
			message: "#^If condition is always true\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Worksheet/Worksheet.php

		-
			message: "#^Left side of && is always true\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Worksheet/Worksheet.php

		-
			message: "#^Cannot call method renameCalculationCacheForWorksheet\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Calculation\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Worksheet/Worksheet.php

		-
			message: "#^Parameter \\#2 \\$start of function substr expects int, int\\<0, max\\>\\|false given\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Worksheet/Worksheet.php

		-
			message: "#^Result of && is always true\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Worksheet/Worksheet.php

		-
			message: "#^Parameter \\#1 \\$pRange of method PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\AutoFilter\\:\\:setRange\\(\\) expects string, null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Worksheet/Worksheet.php

		-
			message: "#^Parameter \\#1 \\$row of method PhpOffice\\\\PhpSpreadsheet\\\\Collection\\\\Cells\\:\\:removeRow\\(\\) expects string, int given\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Worksheet/Worksheet.php

		-
			message: "#^Cannot call method getValue\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Cell\\\\Cell\\|null\\.$#"
			count: 4
			path: src/PhpSpreadsheet/Worksheet/Worksheet.php

		-
			message: "#^Cannot call method getCalculatedValue\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Cell\\\\Cell\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Worksheet/Worksheet.php

		-
			message: "#^Cannot call method getXfIndex\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Cell\\\\Cell\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Worksheet/Worksheet.php

		-
			message: "#^Right side of && is always true\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Worksheet/Worksheet.php

		-
			message: "#^Cannot call method getHashCode\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\Worksheet\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Worksheet/Worksheet.php

		-
			message: "#^Cannot call method getWorksheet\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\DefinedName\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Worksheet/Worksheet.php

		-
			message: "#^Cannot call method getValue\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\DefinedName\\|null\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Worksheet/Worksheet.php

		-
			message: "#^Cannot call method rangeToArray\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\Worksheet\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Worksheet/Worksheet.php

		-
			message: "#^Call to function array_key_exists\\(\\) with int and array\\('none' \\=\\> 'none', 'dashDot' \\=\\> '1px dashed', 'dashDotDot' \\=\\> '1px dotted', 'dashed' \\=\\> '1px dashed', 'dotted' \\=\\> '1px dotted', 'double' \\=\\> '3px double', 'hair' \\=\\> '1px solid', 'medium' \\=\\> '2px solid', \\.\\.\\.\\) will always evaluate to false\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:getSheetIndex\\(\\) should return int but returns int\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:generateMeta\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:generateMeta\\(\\) has parameter \\$desc with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:generateMeta\\(\\) has parameter \\$val with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:generateSheetPrep\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:generateSheetStarts\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:generateSheetStarts\\(\\) has parameter \\$rowMin with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:generateSheetStarts\\(\\) has parameter \\$sheet with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:generateSheetTags\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:generateSheetTags\\(\\) has parameter \\$row with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:generateSheetTags\\(\\) has parameter \\$tbodyStart with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:generateSheetTags\\(\\) has parameter \\$theadEnd with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:generateSheetTags\\(\\) has parameter \\$theadStart with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Parameter \\#1 \\$string of function htmlspecialchars expects string, string\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Cannot access offset 'mime' on array\\|false\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Parameter \\#1 \\$im of function imagepng expects resource, GdImage\\|resource given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Parameter \\#1 \\$str of function base64_encode expects string, string\\|false given\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Ternary operator condition is always true\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Parameter \\#3 \\$use_include_path of function fopen expects bool, int given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Parameter \\#2 \\$length of function fread expects int, int\\|false given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Cannot access offset 0 on array\\|false\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Cannot access offset 1 on array\\|false\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Parameter \\#1 \\$vAlign of method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:mapVAlign\\(\\) expects string, string\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Parameter \\#1 \\$hAlign of method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:mapHAlign\\(\\) expects string, string\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Parameter \\#1 \\$borderStyle of method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:mapBorderStyle\\(\\) expects int, string given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:generateHTMLFooter\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:generateTableTagInline\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:generateTableTagInline\\(\\) has parameter \\$id with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:generateTableTag\\(\\) has parameter \\$html with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:generateTableTag\\(\\) has parameter \\$id with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:generateTableTag\\(\\) has parameter \\$sheetIndex with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:generateTableFooter\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:generateRowCellCss\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:generateRowCellCss\\(\\) has parameter \\$cellAddress with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:generateRowCellCss\\(\\) has parameter \\$colNum with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:generateRowCellCss\\(\\) has parameter \\$pRow with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:generateRowCellDataValueRich\\(\\) has parameter \\$cell with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:generateRowCellDataValueRich\\(\\) has parameter \\$cellData with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Parameter \\#1 \\$pStyle of method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:createCSSStyleFont\\(\\) expects PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font, PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Cannot call method getSuperscript\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Cannot call method getSubscript\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:generateRowCellDataValue\\(\\) has parameter \\$cell with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:generateRowCellDataValue\\(\\) has parameter \\$cellData with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:generateRowCellData\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:generateRowCellData\\(\\) has parameter \\$cell with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:generateRowCellData\\(\\) has parameter \\$cellType with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:generateRowCellData\\(\\) has parameter \\$cssClass with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:generateRowIncludeCharts\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:generateRowIncludeCharts\\(\\) has parameter \\$coordinate with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:generateRowSpans\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:generateRowSpans\\(\\) has parameter \\$colSpan with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:generateRowSpans\\(\\) has parameter \\$html with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:generateRowSpans\\(\\) has parameter \\$rowSpan with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:generateRowWriteCell\\(\\) has parameter \\$cellData with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:generateRowWriteCell\\(\\) has parameter \\$cellType with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:generateRowWriteCell\\(\\) has parameter \\$colNum with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:generateRowWriteCell\\(\\) has parameter \\$colSpan with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:generateRowWriteCell\\(\\) has parameter \\$coordinate with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:generateRowWriteCell\\(\\) has parameter \\$cssClass with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:generateRowWriteCell\\(\\) has parameter \\$html with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:generateRowWriteCell\\(\\) has parameter \\$pRow with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:generateRowWriteCell\\(\\) has parameter \\$rowSpan with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:generateRowWriteCell\\(\\) has parameter \\$sheetIndex with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:calculateSpansOmitRows\\(\\) has parameter \\$candidateSpannedRow with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:calculateSpansOmitRows\\(\\) has parameter \\$sheet with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:calculateSpansOmitRows\\(\\) has parameter \\$sheetIndex with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Negated boolean expression is always false\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Ods.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Ods\\\\Cell\\\\Style\\:\\:\\$writer has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Ods/Cell/Style.php

		-
			message: "#^If condition is always true\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Writer/Ods/Cell/Style.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Ods\\\\Content\\:\\:\\$formulaConvertor has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Ods/Content.php

		-
			message: "#^Parameter \\#2 \\$value of method XMLWriter\\:\\:writeAttribute\\(\\) expects string, int given\\.$#"
			count: 4
			path: src/PhpSpreadsheet/Writer/Ods/Content.php

		-
			message: "#^Parameter \\#2 \\$value of method XMLWriter\\:\\:writeAttribute\\(\\) expects string, int\\<2, max\\> given\\.$#"
			count: 3
			path: src/PhpSpreadsheet/Writer/Ods/Content.php

		-
			message: "#^Parameter \\#1 \\$range of static method PhpOffice\\\\PhpSpreadsheet\\\\Cell\\\\Coordinate\\:\\:splitRange\\(\\) expects string, string\\|false given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Ods/Content.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Ods\\\\Formula\\:\\:\\$definedNames has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Ods/Formula.php

		-
			message: "#^Parameter \\#1 \\$content of method XMLWriter\\:\\:text\\(\\) expects string, int given\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Writer/Ods/Settings.php

		-
			message: "#^Strict comparison using \\=\\=\\= between int and null will always evaluate to false\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Pdf/Dompdf.php

		-
			message: "#^Parameter \\#2 \\$str of function fwrite expects string, string\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Pdf/Dompdf.php

		-
			message: "#^Strict comparison using \\=\\=\\= between null and int will always evaluate to false\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Pdf/Mpdf.php

		-
			message: "#^Strict comparison using \\=\\=\\= between int and null will always evaluate to false\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Pdf/Tcpdf.php

		-
			message: "#^Cannot call method getHashCode\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls.php

		-
			message: "#^Parameter \\#1 \\$font of method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Xls\\\\Workbook\\:\\:addFont\\(\\) expects PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font, PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls.php

		-
			message: "#^Offset 'startCoordinates' does not exist on array\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls.php

		-
			message: "#^Offset 'startOffsetX' does not exist on array\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls.php

		-
			message: "#^Offset 'startOffsetY' does not exist on array\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls.php

		-
			message: "#^Offset 'endCoordinates' does not exist on array\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls.php

		-
			message: "#^Offset 'endOffsetX' does not exist on array\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls.php

		-
			message: "#^Offset 'endOffsetY' does not exist on array\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls.php

		-
			message: "#^Parameter \\#1 \\$data of method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Escher\\\\DggContainer\\\\BstoreContainer\\\\BSE\\\\Blip\\:\\:setData\\(\\) expects string, string\\|false given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls.php

		-
			message: "#^Parameter \\#1 \\$im of function imagepng expects resource, resource\\|false given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls.php

		-
			message: "#^Parameter \\#1 \\$im of function imagepng expects resource, GdImage\\|resource given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls.php

		-
			message: "#^Parameter \\#1 \\$blipType of method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Escher\\\\DggContainer\\\\BstoreContainer\\\\BSE\\:\\:setBlipType\\(\\) expects int, int\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Xls\\\\BIFFwriter\\:\\:writeEof\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls/BIFFwriter.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Xls\\\\Escher\\:\\:\\$object has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls/Escher.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Xls\\\\Escher\\:\\:\\$data has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls/Escher.php

		-
			message: "#^If condition is always true\\.$#"
			count: 3
			path: src/PhpSpreadsheet/Writer/Xls/Escher.php

		-
			message: "#^Elseif condition is always true\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls/Escher.php

		-
			message: "#^Parameter \\#1 \\$fontName of static method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Font\\:\\:getCharsetFromFontName\\(\\) expects string, string\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls/Font.php

		-
			message: "#^If condition is always false\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Writer/Xls/Font.php

		-
			message: "#^Parameter \\#1 \\$bold of static method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Xls\\\\Font\\:\\:mapBold\\(\\) expects bool, bool\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls/Font.php

		-
			message: "#^Parameter \\#1 \\$underline of static method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Xls\\\\Font\\:\\:mapUnderline\\(\\) expects string, string\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls/Font.php

		-
			message: "#^Parameter \\#1 \\$textValue of static method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\StringHelper\\:\\:UTF8toBIFF8UnicodeShort\\(\\) expects string, string\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls/Font.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Xls\\\\Parser\\:\\:\\$spreadsheet has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls/Parser.php

		-
			message: "#^Unreachable statement \\- code above always terminates\\.$#"
			count: 5
			path: src/PhpSpreadsheet/Writer/Xls/Parser.php

		-
			message: "#^Parameter \\#3 \\$subject of function preg_replace expects array\\|string, string\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls/Parser.php

		-
			message: "#^Parameter \\#3 \\$subject of function str_replace expects array\\|string, string\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls/Parser.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Xls\\\\Parser\\:\\:advance\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls/Parser.php

		-
			message: "#^Offset 'left' does not exist on \\(array&nonEmpty\\)\\|string\\.$#"
			count: 6
			path: src/PhpSpreadsheet/Writer/Xls/Parser.php

		-
			message: "#^Offset 'right' does not exist on \\(array&nonEmpty\\)\\|string\\.$#"
			count: 5
			path: src/PhpSpreadsheet/Writer/Xls/Parser.php

		-
			message: "#^Offset 'value' does not exist on \\(array&nonEmpty\\)\\|string\\.$#"
			count: 7
			path: src/PhpSpreadsheet/Writer/Xls/Parser.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Xls\\\\Workbook\\:\\:\\$colors has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls/Workbook.php

		-
			message: "#^Cannot call method getTitle\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\Worksheet\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls/Workbook.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Xls\\\\Workbook\\:\\:writeAllDefinedNamesBiff8\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls/Workbook.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Xls\\\\Workbook\\:\\:writeSupbookInternal\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls/Workbook.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Xls\\\\Workbook\\:\\:writeExternalsheetBiff8\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls/Workbook.php

		-
			message: "#^Cannot access offset 'encoding' on array\\|false\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls/Workbook.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Xls\\\\Workbook\\:\\:writeMsoDrawingGroup\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls/Workbook.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Xls\\\\Workbook\\:\\:\\$escher \\(PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Escher\\) does not accept PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Escher\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls/Workbook.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Xls\\\\Worksheet\\:\\:\\$colors has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls/Worksheet.php

		-
			message: "#^Cannot call method getHashCode\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls/Worksheet.php

		-
			message: "#^Parameter \\#4 \\$isError of method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Xls\\\\Worksheet\\:\\:writeBoolErr\\(\\) expects bool, int given\\.$#"
			count: 3
			path: src/PhpSpreadsheet/Writer/Xls/Worksheet.php

		-
			message: "#^Parameter \\#2 \\$pieces of function implode expects array, array\\<int, string\\>\\|false given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls/Worksheet.php

		-
			message: "#^Parameter \\#3 \\$subject of function preg_replace expects array\\|string, string\\|null given\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Writer/Xls/Worksheet.php

		-
			message: "#^Parameter \\#2 \\$subject of function preg_match expects string, string\\|null given\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Writer/Xls/Worksheet.php

		-
			message: "#^Parameter \\#2 \\$subject of function preg_match_all expects string, string\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls/Worksheet.php

		-
			message: "#^Parameter \\#1 \\$coordinates of static method PhpOffice\\\\PhpSpreadsheet\\\\Cell\\\\Coordinate\\:\\:indexesFromString\\(\\) expects string, string\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls/Worksheet.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Xls\\\\Worksheet\\:\\:\\$activePane \\(int\\) does not accept int\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls/Worksheet.php

		-
			message: "#^Parameter \\#5 \\$width of method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Xls\\\\Worksheet\\:\\:positionImage\\(\\) expects int, float given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls/Worksheet.php

		-
			message: "#^Parameter \\#6 \\$height of method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Xls\\\\Worksheet\\:\\:positionImage\\(\\) expects int, float given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls/Worksheet.php

		-
			message: "#^Parameter \\#1 \\$im of function imagesx expects resource, GdImage\\|resource given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls/Worksheet.php

		-
			message: "#^Parameter \\#1 \\$im of function imagesy expects resource, GdImage\\|resource given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls/Worksheet.php

		-
			message: "#^Parameter \\#1 \\$im of function imagecolorat expects resource, GdImage\\|resource given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls/Worksheet.php

		-
			message: "#^Parameter \\#1 \\$im of function imagecolorsforindex expects resource, GdImage\\|resource given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls/Worksheet.php

		-
			message: "#^Parameter \\#2 \\$col of function imagecolorsforindex expects int, int\\|false given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls/Worksheet.php

		-
			message: "#^Parameter \\#1 \\$ascii of function chr expects int, float given\\.$#"
			count: 3
			path: src/PhpSpreadsheet/Writer/Xls/Worksheet.php

		-
			message: "#^Parameter \\#2 \\$length of function fread expects int, int\\|false given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls/Worksheet.php

		-
			message: "#^Parameter \\#1 \\$string of function strlen expects string, string\\|false given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls/Worksheet.php

		-
			message: "#^Parameter \\#2 \\$data of function unpack expects string, string\\|false given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls/Worksheet.php

		-
			message: "#^Cannot access offset 'ident' on array\\|false\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls/Worksheet.php

		-
			message: "#^Parameter \\#1 \\$string of function substr expects string, string\\|false given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls/Worksheet.php

		-
			message: "#^Cannot access offset 'sa' on array\\|false\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls/Worksheet.php

		-
			message: "#^Cannot access offset 1 on array\\|false\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Writer/Xls/Worksheet.php

		-
			message: "#^Cannot access offset 2 on array\\|false\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Writer/Xls/Worksheet.php

		-
			message: "#^Cannot access offset 'comp' on array\\|false\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls/Worksheet.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Xls\\\\Worksheet\\:\\:\\$escher \\(PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Escher\\) does not accept PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Escher\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls/Worksheet.php

		-
			message: "#^Parameter \\#1 \\$textRotation of static method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Xls\\\\Xf\\:\\:mapTextRotation\\(\\) expects int, int\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls/Xf.php

		-
			message: "#^Possibly invalid array key type array\\|string\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx.php

		-
			message: "#^Parameter \\#1 \\$path of function dirname expects string, array\\|string\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx.php

		-
			message: "#^Argument of an invalid type array\\|null supplied for foreach, only iterables are supported\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx.php

		-
			message: "#^Parameter \\#1 \\$path of function basename expects string, array\\|string\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx.php

		-
			message: "#^Parameter \\#1 \\$function of function call_user_func expects callable\\(\\)\\: mixed, string given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Xlsx\\:\\:\\$pathNames has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Xlsx\\\\Chart\\:\\:\\$calculateCellValues has no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx/Chart.php

		-
			message: "#^Parameter \\#2 \\$value of method XMLWriter\\:\\:writeAttribute\\(\\) expects string, int given\\.$#"
			count: 45
			path: src/PhpSpreadsheet/Writer/Xlsx/Chart.php

		-
			message: "#^Strict comparison using \\=\\=\\= between PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\PlotArea and null will always evaluate to false\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx/Chart.php

		-
			message: "#^Argument of an invalid type array\\|string supplied for foreach, only iterables are supported\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx/Chart.php

		-
			message: "#^Parameter \\#2 \\$value of method XMLWriter\\:\\:writeAttribute\\(\\) expects string, string\\|null given\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Writer/Xlsx/Chart.php

		-
			message: "#^Parameter \\#2 \\$yAxisLabel of method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Xlsx\\\\Chart\\:\\:writeValueAxis\\(\\) expects PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Title, PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Title\\|null given\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Writer/Xlsx/Chart.php

		-
			message: "#^Parameter \\#4 \\$id1 of method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Xlsx\\\\Chart\\:\\:writeValueAxis\\(\\) expects string, int\\|string given\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Writer/Xlsx/Chart.php

		-
			message: "#^Parameter \\#5 \\$id2 of method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Xlsx\\\\Chart\\:\\:writeValueAxis\\(\\) expects string, int\\|string given\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Writer/Xlsx/Chart.php

		-
			message: "#^Parameter \\#7 \\$xAxis of method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Xlsx\\\\Chart\\:\\:writeValueAxis\\(\\) expects PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Axis, PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Axis\\|null given\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Writer/Xlsx/Chart.php

		-
			message: "#^Parameter \\#8 \\$majorGridlines of method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Xlsx\\\\Chart\\:\\:writeValueAxis\\(\\) expects PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\GridLines, PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\GridLines\\|null given\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Writer/Xlsx/Chart.php

		-
			message: "#^Parameter \\#9 \\$minorGridlines of method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Xlsx\\\\Chart\\:\\:writeValueAxis\\(\\) expects PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\GridLines, PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\GridLines\\|null given\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Writer/Xlsx/Chart.php

		-
			message: "#^Parameter \\#2 \\$xAxisLabel of method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Xlsx\\\\Chart\\:\\:writeCategoryAxis\\(\\) expects PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Title, PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Title\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx/Chart.php

		-
			message: "#^Parameter \\#3 \\$id1 of method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Xlsx\\\\Chart\\:\\:writeCategoryAxis\\(\\) expects string, int\\|string given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx/Chart.php

		-
			message: "#^Parameter \\#4 \\$id2 of method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Xlsx\\\\Chart\\:\\:writeCategoryAxis\\(\\) expects string, int\\|string given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx/Chart.php

		-
			message: "#^Parameter \\#6 \\$yAxis of method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Xlsx\\\\Chart\\:\\:writeCategoryAxis\\(\\) expects PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Axis, PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Axis\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx/Chart.php

		-
			message: "#^Part \\$xAxis\\-\\>getShadowProperty\\('effect'\\) \\(array\\|int\\|string\\|null\\) of encapsed string cannot be cast to string\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx/Chart.php

		-
			message: "#^Parameter \\#2 \\$value of method XMLWriter\\:\\:writeAttribute\\(\\) expects string, array\\|int\\|string given\\.$#"
			count: 8
			path: src/PhpSpreadsheet/Writer/Xlsx/Chart.php

		-
			message: "#^Part \\$xAxis\\-\\>getShadowProperty\\(\\['color', 'type'\\]\\) \\(array\\|int\\|string\\|null\\) of encapsed string cannot be cast to string\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx/Chart.php

		-
			message: "#^Parameter \\#2 \\$value of method XMLWriter\\:\\:writeAttribute\\(\\) expects string, array\\|int\\|string\\|null given\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Writer/Xlsx/Chart.php

		-
			message: "#^Else branch is unreachable because previous condition is always true\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx/Chart.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Xlsx\\\\Chart\\:\\:getChartType\\(\\) never returns string so it can be removed from the return typehint\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx/Chart.php

		-
			message: "#^Strict comparison using \\=\\=\\= between PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\DataSeries and null will always evaluate to false\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx/Chart.php

		-
			message: "#^Cannot call method getFillColor\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\DataSeriesValues\\|false\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx/Chart.php

		-
			message: "#^Cannot call method getDataValues\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\DataSeriesValues\\|false\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx/Chart.php

		-
			message: "#^Parameter \\#1 \\$plotSeriesValues of method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Xlsx\\\\Chart\\:\\:writeBubbles\\(\\) expects PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\DataSeriesValues, PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\DataSeriesValues\\|false given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx/Chart.php

		-
			message: "#^Strict comparison using \\=\\=\\= between PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\DataSeriesValues and null will always evaluate to false\\.$#"
			count: 3
			path: src/PhpSpreadsheet/Writer/Xlsx/Chart.php

		-
			message: "#^Parameter \\#1 \\$rawTextData of method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\XMLWriter\\:\\:writeRawData\\(\\) expects array\\<string\\>\\|string\\|null, int given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx/Chart.php

		-
			message: "#^Parameter \\#2 \\$value of method XMLWriter\\:\\:writeAttribute\\(\\) expects string, float\\|int given\\.$#"
			count: 4
			path: src/PhpSpreadsheet/Writer/Xlsx/Chart.php

		-
			message: "#^Parameter \\#2 \\$value of method XMLWriter\\:\\:writeAttribute\\(\\) expects string, float given\\.$#"
			count: 6
			path: src/PhpSpreadsheet/Writer/Xlsx/Chart.php

		-
			message: "#^Parameter \\#1 \\$string of function substr expects string, int given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx/Comments.php

		-
			message: "#^Parameter \\#2 \\$content of method XMLWriter\\:\\:writeElement\\(\\) expects string\\|null, int given\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Writer/Xlsx/Comments.php

		-
			message: "#^Parameter \\#1 \\$arr1 of function array_diff expects array, array\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx/ContentTypes.php

		-
			message: "#^Cannot access offset 2 on array\\|false\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx/ContentTypes.php

		-
			message: "#^Parameter \\#2 \\$content of method XMLWriter\\:\\:writeElement\\(\\) expects string\\|null, int given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx/DocProps.php

		-
			message: "#^Parameter \\#2 \\$value of method XMLWriter\\:\\:writeAttribute\\(\\) expects string, int given\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Writer/Xlsx/DocProps.php

		-
			message: "#^Parameter \\#1 \\$index of method PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\Worksheet\\:\\:getChartByIndex\\(\\) expects string, int given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx/Drawing.php

		-
			message: "#^Parameter \\#2 \\$pChart of method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Xlsx\\\\Drawing\\:\\:writeChart\\(\\) expects PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Chart, PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Chart\\|false given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx/Drawing.php

		-
			message: "#^Parameter \\#2 \\$content of method XMLWriter\\:\\:writeElement\\(\\) expects string\\|null, int given\\.$#"
			count: 12
			path: src/PhpSpreadsheet/Writer/Xlsx/Drawing.php

		-
			message: "#^Parameter \\#2 \\$value of method XMLWriter\\:\\:writeAttribute\\(\\) expects string, int given\\.$#"
			count: 8
			path: src/PhpSpreadsheet/Writer/Xlsx/Drawing.php

		-
			message: "#^Parameter \\#2 \\$value of method XMLWriter\\:\\:writeAttribute\\(\\) expects string, int\\<0, max\\> given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx/Drawing.php

		-
			message: "#^Parameter \\#4 \\$pTarget of method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Xlsx\\\\Rels\\:\\:writeRelationship\\(\\) expects string, array\\|string\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx/Rels.php

		-
			message: "#^Parameter \\#2 \\$pId of method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Xlsx\\\\Rels\\:\\:writeRelationship\\(\\) expects int, string given\\.$#"
			count: 4
			path: src/PhpSpreadsheet/Writer/Xlsx/Rels.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Xlsx\\\\Rels\\:\\:writeUnparsedRelationship\\(\\) has parameter \\$relationship with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx/Rels.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Xlsx\\\\Rels\\:\\:writeUnparsedRelationship\\(\\) has parameter \\$type with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx/Rels.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Xlsx\\\\Rels\\:\\:writeDrawingHyperLink\\(\\) has parameter \\$i with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx/Rels.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Xlsx\\\\Rels\\:\\:writeDrawingHyperLink\\(\\) has parameter \\$objWriter with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx/Rels.php

		-
			message: "#^Parameter \\#2 \\$value of method XMLWriter\\:\\:writeAttribute\\(\\) expects string, int\\<0, max\\> given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx/StringTable.php

		-
			message: "#^Instanceof between string and PhpOffice\\\\PhpSpreadsheet\\\\RichText\\\\RichText will always evaluate to false\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx/StringTable.php

		-
			message: "#^Instanceof between \\*NEVER\\* and PhpOffice\\\\PhpSpreadsheet\\\\RichText\\\\RichText will always evaluate to false\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx/StringTable.php

		-
			message: "#^Cannot call method getName\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font\\|null\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Writer/Xlsx/StringTable.php

		-
			message: "#^Parameter \\#2 \\$value of method XMLWriter\\:\\:writeAttribute\\(\\) expects string, string\\|null given\\.$#"
			count: 5
			path: src/PhpSpreadsheet/Writer/Xlsx/StringTable.php

		-
			message: "#^Cannot call method getBold\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font\\|null\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Writer/Xlsx/StringTable.php

		-
			message: "#^Cannot call method getItalic\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font\\|null\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Writer/Xlsx/StringTable.php

		-
			message: "#^Cannot call method getSubscript\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font\\|null\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Writer/Xlsx/StringTable.php

		-
			message: "#^Cannot call method getSuperscript\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font\\|null\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Writer/Xlsx/StringTable.php

		-
			message: "#^Cannot call method getStrikethrough\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font\\|null\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Writer/Xlsx/StringTable.php

		-
			message: "#^Cannot call method getColor\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx/StringTable.php

		-
			message: "#^Cannot call method getSize\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx/StringTable.php

		-
			message: "#^Parameter \\#2 \\$value of method XMLWriter\\:\\:writeAttribute\\(\\) expects string, float\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx/StringTable.php

		-
			message: "#^Cannot call method getUnderline\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font\\|null\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Writer/Xlsx/StringTable.php

		-
			message: "#^Parameter \\#1 \\$text of method PhpOffice\\\\PhpSpreadsheet\\\\RichText\\\\RichText\\:\\:createTextRun\\(\\) expects string, string\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx/StringTable.php

		-
			message: "#^Parameter \\#2 \\$value of method XMLWriter\\:\\:writeAttribute\\(\\) expects string, int given\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Writer/Xlsx/StringTable.php

		-
			message: "#^Parameter \\#2 \\$value of method XMLWriter\\:\\:writeAttribute\\(\\) expects string, int given\\.$#"
			count: 22
			path: src/PhpSpreadsheet/Writer/Xlsx/Style.php

		-
			message: "#^Parameter \\#2 \\$pNumberFormat of method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Xlsx\\\\Style\\:\\:writeNumFmt\\(\\) expects PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\NumberFormat, PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\NumberFormat\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx/Style.php

		-
			message: "#^Parameter \\#2 \\$pFont of method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Xlsx\\\\Style\\:\\:writeFont\\(\\) expects PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font, PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx/Style.php

		-
			message: "#^Parameter \\#2 \\$pFill of method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Xlsx\\\\Style\\:\\:writeFill\\(\\) expects PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Fill, PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Fill\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx/Style.php

		-
			message: "#^Parameter \\#2 \\$pBorders of method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Xlsx\\\\Style\\:\\:writeBorder\\(\\) expects PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Borders, PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Borders\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx/Style.php

		-
			message: "#^Parameter \\#2 \\$value of method XMLWriter\\:\\:writeAttribute\\(\\) expects string, int\\<0, max\\> given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx/Style.php

		-
			message: "#^Cannot call method getStyle\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Conditional\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx/Style.php

		-
			message: "#^Parameter \\#2 \\$value of method XMLWriter\\:\\:writeAttribute\\(\\) expects string, string\\|null given\\.$#"
			count: 9
			path: src/PhpSpreadsheet/Writer/Xlsx/Style.php

		-
			message: "#^Parameter \\#2 \\$value of method XMLWriter\\:\\:writeAttribute\\(\\) expects string, string\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx/Worksheet.php

		-
			message: "#^Parameter \\#2 \\$value of method XMLWriter\\:\\:writeAttribute\\(\\) expects string, float given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx/Style.php

		-
			message: "#^Comparison operation \"\\<\" between int\\<min, \\-1\\> and 0 is always true\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Writer/Xlsx/Style.php

		-
			message: "#^Parameter \\#2 \\$value of method XMLWriter\\:\\:writeAttribute\\(\\) expects string, int\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx/Style.php

		-
			message: "#^Parameter \\#2 \\$value of method XMLWriter\\:\\:writeAttribute\\(\\) expects string, int\\<1, max\\> given\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Writer/Xlsx/Style.php

		-
			message: "#^Result of \\|\\| is always true\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx/Style.php

		-
			message: "#^Parameter \\#2 \\$value of method XMLWriter\\:\\:writeAttribute\\(\\) expects string, int given\\.$#"
			count: 7
			path: src/PhpSpreadsheet/Writer/Xlsx/Workbook.php

		-
			message: "#^Parameter \\#3 \\$pStringTable of method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Xlsx\\\\Worksheet\\:\\:writeSheetData\\(\\) expects array\\<string\\>, array\\<string\\>\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx/Worksheet.php

		-
			message: "#^Parameter \\#2 \\$value of method XMLWriter\\:\\:writeAttribute\\(\\) expects string, int given\\.$#"
			count: 19
			path: src/PhpSpreadsheet/Writer/Xlsx/Worksheet.php

		-
			message: "#^Parameter \\#2 \\$value of method XMLWriter\\:\\:writeAttribute\\(\\) expects string, int\\<1, max\\> given\\.$#"
			count: 7
			path: src/PhpSpreadsheet/Writer/Xlsx/Worksheet.php

		-
			message: "#^Parameter \\#2 \\$value of method XMLWriter\\:\\:writeAttribute\\(\\) expects string, int\\<0, max\\> given\\.$#"
			count: 3
			path: src/PhpSpreadsheet/Writer/Xlsx/Worksheet.php

		-
			message: "#^Parameter \\#2 \\$value of method XMLWriter\\:\\:writeAttribute\\(\\) expects string, int\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx/Worksheet.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Xlsx\\\\Worksheet\\:\\:writeAttributeIf\\(\\) has parameter \\$condition with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx/Worksheet.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Xlsx\\\\Worksheet\\:\\:writeElementIf\\(\\) has parameter \\$condition with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx/Worksheet.php

		-
			message: "#^Parameter \\#3 \\$namespace of method XMLWriter\\:\\:startElementNs\\(\\) expects string, null given\\.$#"
			count: 8
			path: src/PhpSpreadsheet/Writer/Xlsx/Worksheet.php

		-
			message: "#^If condition is always true\\.$#"
			count: 6
			path: src/PhpSpreadsheet/Writer/Xlsx/Worksheet.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Xlsx\\\\Worksheet\\:\\:writeDataBarElements\\(\\) has parameter \\$dataBar with no typehint specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx/Worksheet.php

		-
			message: "#^Parameter \\#4 \\$val of static method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Xlsx\\\\Worksheet\\:\\:writeAttributeIf\\(\\) expects string, int given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx/Worksheet.php

		-
			message: "#^Parameter \\#2 \\$content of method XMLWriter\\:\\:writeElement\\(\\) expects string\\|null, int\\|string given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx/Worksheet.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Xlsx\\\\Xlfn\\:\\:addXlfn\\(\\) should return string but returns string\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx/Xlfn.php

		-
			message: "#^Unreachable statement \\- code above always terminates\\.$#"
			count: 1
			path: tests/PhpSpreadsheetTests/Calculation/Engine/RangeTest.php

		-
			message: "#^Call to static method PHPUnit\\\\Framework\\\\Assert\\:\\:assertSame\\(\\) with arguments PhpOffice\\\\PhpSpreadsheet\\\\Cell\\\\Cell, null and 'should get exact…' will always evaluate to false\\.$#"
			count: 1
			path: tests/PhpSpreadsheetTests/Collection/CellsTest.php

		-
			message: "#^Cannot call method getParent\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Cell\\\\Cell\\|null\\.$#"
			count: 1
			path: tests/PhpSpreadsheetTests/Collection/CellsTest.php

		-
			message: "#^Cannot call method getCoordinate\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Cell\\\\Cell\\|null\\.$#"
			count: 1
			path: tests/PhpSpreadsheetTests/Collection/CellsTest.php

		-
			message: "#^Parameter \\#1 \\$row of method PhpOffice\\\\PhpSpreadsheet\\\\Collection\\\\Cells\\:\\:getHighestColumn\\(\\) expects string\\|null, int given\\.$#"
			count: 3
			path: tests/PhpSpreadsheetTests/Collection/CellsTest.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheetTests\\\\Functional\\\\ColumnWidthTest\\:\\:testReadColumnWidth\\(\\) has parameter \\$format with no typehint specified\\.$#"
			count: 1
			path: tests/PhpSpreadsheetTests/Functional/ColumnWidthTest.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheetTests\\\\Functional\\\\CommentsTest\\:\\:testComments\\(\\) has parameter \\$format with no typehint specified\\.$#"
			count: 1
			path: tests/PhpSpreadsheetTests/Functional/CommentsTest.php

		-
			message: "#^Parameter \\#1 \\$condition of method PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Conditional\\:\\:addCondition\\(\\) expects string, float given\\.$#"
			count: 2
			path: tests/PhpSpreadsheetTests/Functional/ConditionalStopIfTrueTest.php

		-
			message: "#^Parameter \\#1 \\$im of function imagecolorallocate expects resource, resource\\|false given\\.$#"
			count: 1
			path: tests/PhpSpreadsheetTests/Functional/DrawingImageHyperlinkTest.php

		-
			message: "#^Parameter \\#1 \\$im of function imagestring expects resource, resource\\|false given\\.$#"
			count: 1
			path: tests/PhpSpreadsheetTests/Functional/DrawingImageHyperlinkTest.php

		-
			message: "#^Parameter \\#6 \\$col of function imagestring expects int, int\\|false given\\.$#"
			count: 1
			path: tests/PhpSpreadsheetTests/Functional/DrawingImageHyperlinkTest.php

		-
			message: "#^Parameter \\#1 \\$value of method PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\MemoryDrawing\\:\\:setImageResource\\(\\) expects GdImage\\|resource, resource\\|false given\\.$#"
			count: 1
			path: tests/PhpSpreadsheetTests/Functional/DrawingImageHyperlinkTest.php

		-
			message: "#^Cannot call method getUrl\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Cell\\\\Hyperlink\\|null\\.$#"
			count: 1
			path: tests/PhpSpreadsheetTests/Functional/DrawingImageHyperlinkTest.php

		-
			message: "#^Cannot call method getPageSetup\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\Worksheet\\|null\\.$#"
			count: 5
			path: tests/PhpSpreadsheetTests/Functional/PrintAreaTest.php

		-
			message: "#^Cannot access offset 'size' on array\\(0 \\=\\> int, 1 \\=\\> int, 2 \\=\\> int, 3 \\=\\> int, 4 \\=\\> int, 5 \\=\\> int, 6 \\=\\> int, 7 \\=\\> int, \\.\\.\\.\\)\\|false\\.$#"
			count: 2
			path: tests/PhpSpreadsheetTests/Functional/StreamTest.php

		-
			message: "#^Parameter \\#1 \\$fp of function fstat expects resource, resource\\|false given\\.$#"
			count: 1
			path: tests/PhpSpreadsheetTests/Functional/StreamTest.php

		-
			message: "#^Parameter \\#1 \\$filename of method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\IWriter\\:\\:save\\(\\) expects resource\\|string, resource\\|false given\\.$#"
			count: 1
			path: tests/PhpSpreadsheetTests/Functional/StreamTest.php

		-
			message: "#^Parameter \\#1 \\$expected of static method PHPUnit\\\\Framework\\\\Assert\\:\\:assertInstanceOf\\(\\) expects class\\-string\\<object\\>, string given\\.$#"
			count: 3
			path: tests/PhpSpreadsheetTests/IOFactoryTest.php

		-
			message: "#^Cannot call method getValue\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\NamedFormula\\|null\\.$#"
			count: 5
			path: tests/PhpSpreadsheetTests/NamedFormulaTest.php

		-
			message: "#^Cannot call method getValue\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\NamedRange\\|null\\.$#"
			count: 5
			path: tests/PhpSpreadsheetTests/NamedRangeTest.php

		-
			message: "#^Unreachable statement \\- code above always terminates\\.$#"
			count: 1
			path: tests/PhpSpreadsheetTests/Reader/Ods/OdsTest.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheetTests\\\\Reader\\\\Security\\\\XmlScannerTest\\:\\:testValidXML\\(\\) has parameter \\$libxmlDisableEntityLoader with no typehint specified\\.$#"
			count: 1
			path: tests/PhpSpreadsheetTests/Reader/Security/XmlScannerTest.php

		-
			message: "#^Argument of an invalid type array\\<int, string\\>\\|false supplied for foreach, only iterables are supported\\.$#"
			count: 3
			path: tests/PhpSpreadsheetTests/Reader/Security/XmlScannerTest.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheetTests\\\\Reader\\\\Security\\\\XmlScannerTest\\:\\:testInvalidXML\\(\\) has parameter \\$libxmlDisableEntityLoader with no typehint specified\\.$#"
			count: 1
			path: tests/PhpSpreadsheetTests/Reader/Security/XmlScannerTest.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheetTests\\\\Reader\\\\Xlsx\\\\AutoFilterTest\\:\\:getWorksheetInstance\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: tests/PhpSpreadsheetTests/Reader/Xlsx/AutoFilterTest.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheetTests\\\\Reader\\\\Xlsx\\\\AutoFilterTest\\:\\:getXMLInstance\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: tests/PhpSpreadsheetTests/Reader/Xlsx/AutoFilterTest.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheetTests\\\\Reader\\\\Xlsx\\\\AutoFilterTest\\:\\:getXMLInstance\\(\\) has parameter \\$ref with no typehint specified\\.$#"
			count: 1
			path: tests/PhpSpreadsheetTests/Reader/Xlsx/AutoFilterTest.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheetTests\\\\Reader\\\\Xlsx\\\\AutoFilterTest\\:\\:getAutoFilterInstance\\(\\) has no return typehint specified\\.$#"
			count: 1
			path: tests/PhpSpreadsheetTests/Reader/Xlsx/AutoFilterTest.php

		-
			message: "#^Cannot call method setMinimumConditionalFormatValueObject\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\ConditionalFormatting\\\\ConditionalDataBar\\|null\\.$#"
			count: 1
			path: tests/PhpSpreadsheetTests/Reader/Xlsx/ConditionalFormattingDataBarXlsxTest.php

		-
			message: "#^Cannot call method getMinimumConditionalFormatValueObject\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\ConditionalFormatting\\\\ConditionalDataBar\\|null\\.$#"
			count: 13
			path: tests/PhpSpreadsheetTests/Reader/Xlsx/ConditionalFormattingDataBarXlsxTest.php

		-
			message: "#^Cannot call method getMaximumConditionalFormatValueObject\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\ConditionalFormatting\\\\ConditionalDataBar\\|null\\.$#"
			count: 13
			path: tests/PhpSpreadsheetTests/Reader/Xlsx/ConditionalFormattingDataBarXlsxTest.php

		-
			message: "#^Cannot call method getColor\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\ConditionalFormatting\\\\ConditionalDataBar\\|null\\.$#"
			count: 5
			path: tests/PhpSpreadsheetTests/Reader/Xlsx/ConditionalFormattingDataBarXlsxTest.php

		-
			message: "#^Cannot call method getConditionalFormattingRuleExt\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\ConditionalFormatting\\\\ConditionalDataBar\\|null\\.$#"
			count: 8
			path: tests/PhpSpreadsheetTests/Reader/Xlsx/ConditionalFormattingDataBarXlsxTest.php

		-
			message: "#^Cannot call method getShowValue\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\ConditionalFormatting\\\\ConditionalDataBar\\|null\\.$#"
			count: 1
			path: tests/PhpSpreadsheetTests/Reader/Xlsx/ConditionalFormattingDataBarXlsxTest.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheetTests\\\\Reader\\\\Xml\\\\XmlTest\\:\\:testInvalidSimpleXML\\(\\) has parameter \\$filename with no typehint specified\\.$#"
			count: 1
			path: tests/PhpSpreadsheetTests/Reader/Xml/XmlTest.php

		-
			message: "#^Argument of an invalid type array\\<int, string\\>\\|false supplied for foreach, only iterables are supported\\.$#"
			count: 1
			path: tests/PhpSpreadsheetTests/Reader/Xml/XmlTest.php

		-
			message: "#^Parameter \\#1 \\$currencyCode of static method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\StringHelper\\:\\:setCurrencyCode\\(\\) expects string, null given\\.$#"
			count: 1
			path: tests/PhpSpreadsheetTests/Shared/StringHelperTest.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheetTests\\\\SpreadsheetTest\\:\\:testGetSheetByName\\(\\) has parameter \\$index with no typehint specified\\.$#"
			count: 1
			path: tests/PhpSpreadsheetTests/SpreadsheetTest.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheetTests\\\\SpreadsheetTest\\:\\:testGetSheetByName\\(\\) has parameter \\$sheetName with no typehint specified\\.$#"
			count: 1
			path: tests/PhpSpreadsheetTests/SpreadsheetTest.php

		-
			message: "#^Parameter \\#1 \\$condition of method PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Conditional\\:\\:addCondition\\(\\) expects string, float given\\.$#"
			count: 2
			path: tests/PhpSpreadsheetTests/Style/ConditionalTest.php

		-
			message: "#^Parameter \\#1 \\$conditions of method PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Conditional\\:\\:setConditions\\(\\) expects array\\<string\\>\\|bool\\|float\\|int\\|string, array\\<int, float\\> given\\.$#"
			count: 1
			path: tests/PhpSpreadsheetTests/Style/ConditionalTest.php

		-
			message: "#^Parameter \\#2 \\$pValue of method PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\AutoFilter\\\\Column\\:\\:setAttribute\\(\\) expects string, int given\\.$#"
			count: 1
			path: tests/PhpSpreadsheetTests/Worksheet/AutoFilter/ColumnTest.php

		-
			message: "#^Parameter \\#1 \\$im of function imagecolorallocate expects resource, resource\\|false given\\.$#"
			count: 1
			path: tests/PhpSpreadsheetTests/Worksheet/DrawingTest.php

		-
			message: "#^Parameter \\#1 \\$im of function imagestring expects resource, resource\\|false given\\.$#"
			count: 1
			path: tests/PhpSpreadsheetTests/Worksheet/DrawingTest.php

		-
			message: "#^Parameter \\#6 \\$col of function imagestring expects int, int\\|false given\\.$#"
			count: 1
			path: tests/PhpSpreadsheetTests/Worksheet/DrawingTest.php

		-
			message: "#^Parameter \\#1 \\$value of method PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\MemoryDrawing\\:\\:setImageResource\\(\\) expects GdImage\\|resource, resource\\|false given\\.$#"
			count: 1
			path: tests/PhpSpreadsheetTests/Worksheet/DrawingTest.php

		-
			message: "#^Parameter \\#2 \\$rowIndex of class PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\RowCellIterator constructor expects int, string given\\.$#"
			count: 1
			path: tests/PhpSpreadsheetTests/Worksheet/RowCellIterator2Test.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheetTests\\\\Writer\\\\Html\\\\CallbackTest\\:\\:yellowBody\\(\\) should return string but returns string\\|null\\.$#"
			count: 1
			path: tests/PhpSpreadsheetTests/Writer/Html/CallbackTest.php

		-
			message: "#^Parameter \\#1 \\$haystack of function strpos expects string, string\\|false given\\.$#"
			count: 1
			path: tests/PhpSpreadsheetTests/Writer/Html/CallbackTest.php

		-
			message: "#^Cannot call method getColor\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font\\|null\\.$#"
			count: 3
			path: tests/PhpSpreadsheetTests/Writer/Html/GridlinesTest.php

		-
			message: "#^Cannot call method setSuperScript\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font\\|null\\.$#"
			count: 1
			path: tests/PhpSpreadsheetTests/Writer/Html/GridlinesTest.php

		-
			message: "#^Cannot call method setSubScript\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font\\|null\\.$#"
			count: 3
			path: tests/PhpSpreadsheetTests/Writer/Html/GridlinesTest.php

		-
			message: "#^Cannot call method setBold\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font\\|null\\.$#"
			count: 3
			path: tests/PhpSpreadsheetTests/Writer/Html/HtmlCommentsTest.php

		-
			message: "#^Cannot call method getDrawingCollection\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\Worksheet\\|null\\.$#"
			count: 4
			path: tests/PhpSpreadsheetTests/Writer/Xlsx/UnparsedDataCloneTest.php

		-
			message: "#^Cannot call method getCell\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\Worksheet\\|null\\.$#"
			count: 4
			path: tests/PhpSpreadsheetTests/Writer/Xlsx/UnparsedDataCloneTest.php

		-
			message: "#^Parameter \\#1 \\$data of function simplexml_load_string expects string, string\\|false given\\.$#"
			count: 2
			path: tests/PhpSpreadsheetTests/Writer/Xlsx/UnparsedDataTest.php

		-
			message: "#^Cannot access property \\$pageSetup on SimpleXMLElement\\|false\\.$#"
			count: 1
			path: tests/PhpSpreadsheetTests/Writer/Xlsx/UnparsedDataTest.php

		-
			message: "#^Cannot access property \\$sheetProtection on SimpleXMLElement\\|false\\.$#"
			count: 5
			path: tests/PhpSpreadsheetTests/Writer/Xlsx/UnparsedDataTest.php

		-
			message: "#^Argument of an invalid type SimpleXMLElement\\|null supplied for foreach, only iterables are supported\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xml/Properties.php

		-
			message: "#^Argument of an invalid type SimpleXMLElement\\|null supplied for foreach, only iterables are supported\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xml/Style.php

		-
			message: "#^Binary operation \"/\" between int\\|string and 360 results in an error\\.$#"
			count: 3
			path: src/PhpSpreadsheet/Calculation/DateTimeExcel/YearFrac.php

		-
			message: "#^Binary operation \"/\" between int\\|string and 365 results in an error\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/DateTimeExcel/YearFrac.php

		-
			message: "#^Binary operation \"/\" between int\\|string and \\(float\\|int\\) results in an error\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/DateTimeExcel/YearFrac.php

		-
			message: "#^Binary operation \"/\" between float\\|string and float\\|string results in an error\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Calculation/Financial/Securities/Price.php

		-
			message: "#^Binary operation \"/\" between float\\|int\\|string and float\\|int\\|string results in an error\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Calculation/MathTrig/Combinations.php

		-
			message: "#^Binary operation \"/\" between float\\|int\\|string and float\\|int results in an error\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/MathTrig/Factorial.php

		-
			message: "#^Binary operation \"/\" between float\\|int\\|string and float\\|int\\|string results in an error\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Permutations.php

		-
			message: "#^Offset '(percentage|value)' does not exist on SimpleXMLElement|null\\.$#"
			count: 3
			path: src/PhpSpreadsheet/Reader/Gnumeric/PageSetup.php

