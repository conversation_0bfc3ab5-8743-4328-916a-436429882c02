<?php



namespace app\common\enum;

/**
 * 退款订单相关 枚举类型
 * Class OrderRefundEnum
 * <AUTHOR>
 * @package app\common\enum
 */
class OrderRefundEnum
{
    //退款状态refund_status
    const REFUND_STATUS_ING = 0;//退款中
    const REFUND_STATUS_COMPLETE = 1;//退款完成
    const REFUND_STATUS_FAIL = 2;//退款失败
    const REFUND_STATUS_ABNORMAL = 3;//退款异常



    //退款方式
    const REFUND_WAY_ORIGINAL = 1;//原路退回
    const REFUND_WAY_BALANCE = 2;//退回余额
}