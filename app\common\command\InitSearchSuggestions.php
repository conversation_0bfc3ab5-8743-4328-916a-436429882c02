<?php

namespace app\common\command;

use app\common\library\MeiliSearch;
use app\common\model\goods\Goods;
use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\facade\Db;
use think\facade\Log;

/**
 * 初始化搜索候选词索引
 */
class InitSearchSuggestions extends Command
{
    protected function configure()
    {
        $this->setName('init:search_suggestions')
            ->setDescription('初始化搜索候选词索引');
    }

    protected function execute(Input $input, Output $output)
    {
        try {
            $output->writeln('开始初始化搜索候选词索引...');

            // 初始化MeiliSearch客户端
            $meili = new MeiliSearch();

            // 删除旧索引（如果存在）
            $output->writeln('删除旧索引...');
            $meili->deleteIndex('search_suggestions');

            // 创建新索引
            $output->writeln('创建新索引...');
            $settings = [
                'searchableAttributes' => [
                    'text',
                    'tags'
                ],
                'sortableAttributes' => [
                    'weight',
                    'popularity'
                ],
                'rankingRules' => [
                    'words',
                    'typo',
                    'proximity',
                    'attribute',
                    'sort',
                    'exactness'
                ]
            ];
            $meili->createIndex('search_suggestions', $settings);

            // 生成候选词
            $output->writeln('生成候选词...');
            $allSuggestions = $this->generateSuggestions($output); // Pass output for logging within generate

            // 导入候选词
            if (!empty($allSuggestions)) {
                $output->writeln('导入 ' . count($allSuggestions) . ' 个候选词到索引...');
                // Assuming importDocuments handles batching if necessary, or we implement batching here.
                // MeiliSearch PHP SDK's addDocuments can take an array of documents.
                $response = $meili->importDocuments('search_suggestions', $allSuggestions, 'id');

                if (isset($response['taskUid'])) {
                    $output->writeln("成功提交导入任务，任务ID：{$response['taskUid']}");
                } else {
                    $output->writeln("导入失败：" . json_encode($response));
                }
            } else {
                $output->writeln('没有生成任何候选词。');
            }

            $output->writeln('搜索候选词索引初始化完成！');
            return true;
        } catch (\Exception $e) {
            Log::write('初始化搜索候选词索引异常: ' . $e->getMessage() . ' Trace: ' . $e->getTraceAsString());
            $output->writeln('初始化搜索候选词索引异常: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 生成搜索候选词
     * @param Output $output
     * @return array
     */
    protected function generateSuggestions(Output $output): array
    {
        $suggestionsMap = []; // Use map to handle uniqueness by md5 ID

        // 1. 从商品名称中提取
        $goodsQuery = Goods::where([
            ['del', '=', 0],
            ['status', '=', 1],
            ['audit_status', '=', 1]
        ])->field('id, name, sales_actual, sales_virtual')->cursor(); // Use cursor for large datasets

        $output->writeln('处理商品名称...');
        $processedGoodsCount = 0;
        foreach ($goodsQuery as $item) {
            $processedGoodsCount++;
            $text = trim($item['name']);
            if (empty($text) || mb_strlen($text, 'UTF-8') < 2) continue;

            $id = md5($text);
            $suggestionsMap[$id] = [
                'id' => $id,
                'text' => $text,
                'tags' => 'product_name',
                'weight' => 90, // Adjusted weight
                'popularity' => (int)$item['sales_actual'] + (int)$item['sales_virtual'],
                'goods_id' => (int)$item['id']
            ];
            if ($processedGoodsCount % 500 == 0) {
                $output->writeln("已处理 {$processedGoodsCount} 个商品...");
            }
        }
        $output->writeln("商品名称处理完成，共 {$processedGoodsCount} 个。");

        // 2. 添加分类名称
        $categories = Db::name('goods_category')
            ->field('id, name')
            ->where('del', 0)
            ->where('name', '<>', '')
            ->distinct(true)
            ->select()
            ->toArray();

        $output->writeln('处理分类名称...');
        foreach ($categories as $category) {
            $text = trim($category['name']);
            if (empty($text) || mb_strlen($text, 'UTF-8') < 2) continue;

            // Calculate popularity for categories (e.g., number of goods in category)
            $categoryPopularity = Goods::where(function ($query) use ($category) {
                $query->where('first_cate_id', $category['id'])
                      ->whereOr('second_cate_id', $category['id'])
                      ->whereOr('third_cate_id', $category['id']);
            })->count();
            $id = md5($text);
            // Prioritize if a more popular version (e.g. product name) already exists
            if (!isset($suggestionsMap[$id]) || (isset($suggestionsMap[$id]['popularity']) && $categoryPopularity > $suggestionsMap[$id]['popularity'])) {
                 $suggestionsMap[$id] = [
                    'id' => $id,
                    'text' => $text,
                    'tags' => 'category',
                    'weight' => 70,
                    'popularity' => $categoryPopularity,
                    'goods_id' => 0
                ];
            }
        }
        $output->writeln("分类名称处理完成。");

        // 3. 添加品牌名称
        $brands = Db::name('goods_brand')
            ->field('id, name')
            ->where('del', 0)
            ->where('name', '<>', '')
            ->distinct(true)
            ->select()
            ->toArray();

        $output->writeln('处理品牌名称...');
        foreach ($brands as $brand) {
            $text = trim($brand['name']);
            if (empty($text) || mb_strlen($text, 'UTF-8') < 2) continue;

            // Calculate popularity for brands
            $brandPopularity = Goods::where('brand_id', $brand['id'])->count();
            $id = md5($text);
            if (!isset($suggestionsMap[$id]) || (isset($suggestionsMap[$id]['popularity']) && $brandPopularity > $suggestionsMap[$id]['popularity'])) {
                $suggestionsMap[$id] = [
                    'id' => $id,
                    'text' => $text,
                    'tags' => 'brand',
                    'weight' => 75,
                    'popularity' => $brandPopularity,
                    'goods_id' => 0
                ];
            }
        }
        $output->writeln("品牌名称处理完成。");

        // 4. 添加热门搜索词
        $hotKeywords = Db::name('search_record')
            ->field('keyword, COUNT(*) as search_count')
            ->where('del', 0)
            ->group('keyword') // 使用 group 而不是 groupBy
            ->order('search_count DESC') // 使用 order 而不是 orderBy
            ->limit(500) // Increased limit for more suggestions
            ->select()
            ->toArray();

        $output->writeln('处理热门搜索词...');
        foreach ($hotKeywords as $item) {
            $text = trim($item['keyword']);
            if (empty($text) || mb_strlen($text, 'UTF-8') < 2) continue;

            $id = md5($text);
             if (!isset($suggestionsMap[$id]) || (isset($suggestionsMap[$id]['popularity']) && (int)$item['search_count'] > $suggestionsMap[$id]['popularity'])) {
                $suggestionsMap[$id] = [
                    'id' => $id,
                    'text' => $text,
                    'tags' => 'popular_search',
                    'weight' => 100, // Highest weight for popular searches
                    'popularity' => (int)$item['search_count'],
                    'goods_id' => 0
                ];
            }
        }
        $output->writeln("热门搜索词处理完成。");

        return array_values($suggestionsMap); // Return as indexed array
    }
}
