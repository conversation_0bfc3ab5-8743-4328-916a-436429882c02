<?php
// +----------------------------------------------------------------------
// | LikeShop有特色的全开源社交分销电商系统
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | 商业用途务必购买系统授权，以免引起不必要的法律纠纷
// | 禁止对系统程序代码以任何目的，任何形式的再发布
// | 微信公众号：好象科技
// | 访问官网：http://www.likeshop.net
// | 访问社区：http://bbs.likeshop.net
// | 访问手册：http://doc.likeshop.net
// | 好象科技开发团队 版权所有 拥有最终解释权
// +----------------------------------------------------------------------
// | Author: LikeShopTeam
// +----------------------------------------------------------------------

namespace app\api\controller;

use app\api\logic\ShopFollowLogic;
use app\common\basics\Api;
use app\api\logic\PcLogic;
use app\common\server\JsonServer;
use app\api\validate\ChangeUserInfo;
use think\exception\ValidateException;

class Pc extends Api
{
    public $like_not_need_login = ['articleDetail','commonData','goodsList','category'];

    /**
     * @notes PC公共数据
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2021/10/27 10:31 上午
     */
    public function commonData(){
        return JsonServer::success('',PcLogic::commonData($this->user_id));
    }

    /**
     * @notes 商品列表
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2021/10/27 11:26 上午
     */
    public function goodsList(){
        $type = $this->request->get('type',1);
        $sort_type = $this->request->get('sort_type','');
        $sort = $this->request->get('sort','');
        $name = $this->request->get('name','');
        $category_id = $this->request->get('category_id','');
        $shop_id = $this->request->get('shop_id','');
        $list = PcLogic::goodsList($this->page_no,$this->page_size,$name,$category_id,$shop_id,$type,$sort_type,$sort);
        return JsonServer::success('',$list);
    }

    public function changeUserInfo(){
        try{
            $post = $this->request->post();
            $post['user_id'] = $this->user_id;
            validate(ChangeUserInfo::class)->check($post);
        }catch(ValidateException $e) {
            return JsonServer::error($e->getError());
        }
        $result = PcLogic::changeUserInfo($post);
        if($result === true) {
            return JsonServer::success('保存成功');
        }
        return JsonServer::error($result);
    }


    /**
     * @notes PC商品分类
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2021/10/26 6:19 下午
     */
    public function category(){
        $cateogry = PcLogic::categoryThirdTree();
        return JsonServer::success('获取成功', $cateogry);
    }

    /**
     * @notes 文章详情
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2021/10/26 6:40 下午
     */
    public function articleDetail(){
        $id = $this->request->get('id');
        return JsonServer::success('获取成功', PcLogic::articleDetail($id));
    }

    /**
     * @notes PC我的店铺收藏列表
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2021/10/28 5:09 下午
     */
    public function shopFollowList()
    {
        $get = $this->request->get();
        $get['user_id'] = $this->user_id;
        $get['page_no'] = $this->page_no;
        $get['page_size'] = $this->page_size;

        $data = PcLogic::shopFollowList($get);
        return JsonServer::success('', $data);
    }
}