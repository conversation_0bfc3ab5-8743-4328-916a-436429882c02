{layout name="layout2" /}
<style>
  .layui-form-label {
    color: #6a6f6c;
    width: 140px;
  }
  .layui-input-block{
    margin-left:170px;
  }
  .reqRed::before {
    content: '*';
    color: red;
  }

  /* 广告位信息展示样式 */
  .ad-position-info {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    margin-bottom: 20px;
  }

  .ad-position-info img {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  }

  .ad-position-details h4 {
    font-weight: 600;
    color: #2d3436;
    font-size: 16px;
    margin: 0 0 5px 0;
  }

  .ad-position-details p {
    color: #636e72;
    font-size: 14px;
    margin: 0;
  }

  /* 提示信息样式 */
  .tips-info {
    color: #999;
    font-size: 12px;
    line-height: 1.5;
    margin-top: 5px;
  }

  .tips-info i {
    color: #ff9800;
  }

  .count-tips {
    color: #667eea;
    font-size: 12px;
    margin-top: 5px;
  }

  .count-tips i {
    color: #667eea;
  }
</style>

<div class="layui-form" lay-filter="layuiadmin-form-config" id="layuiadmin-form-config" style="padding: 20px 30px 0 0;">

  <!-- 广告位信息展示 -->
  <div class="layui-form-item">
    <label class="layui-form-label">广告位信息：</label>
    <div class="layui-input-block">
      <div class="ad-position-info" id="ad-position-info">
        <img id="ad-position-image" src="" alt="广告位图">
        <div class="ad-position-details">
          <h4 id="ad-position-name">加载中...</h4>
          <p id="ad-position-desc">正在获取广告位信息...</p>
        </div>
      </div>
    </div>
  </div>

  <!-- 广告标题 -->
  <div class="layui-form-item">
    <label class="layui-form-label reqRed">广告标题：</label>
    <div class="layui-input-inline">
      <input type="text" name="title" lay-verify="required" lay-verType="tips" placeholder="请输入广告标题" autocomplete="off" class="layui-input">
    </div>
  </div>

  <!-- 广告图片 -->
  <div class="layui-form-item">
    <label class="layui-form-label reqRed">广告图片：</label>
    <div class="layui-input-inline">
      <div class="like-upload-image" id="ad-image-upload">
        <div class="upload-image-elem">
          <a class="add-upload-image">+ 添加图片</a>
        </div>
      </div>
    </div>
  </div>

  <!-- 图片提示信息 -->
  <div class="layui-form-item">
    <label class="layui-form-label"></label>
    <div class="layui-input-block">
      <div class="tips-info" id="image-size-tips">
        <i class="layui-icon layui-icon-tips"></i>
        建议上传符合广告位尺寸要求的图片，以获得最佳显示效果
      </div>
      <div class="count-tips" id="image-count-tips" style="display: none;">
        <i class="layui-icon layui-icon-picture"></i>
        根据您的购买数量，最多可上传 <span id="max-upload-count">1</span> 张图片
      </div>
    </div>
  </div>

  <!-- 广告链接 -->
  <div class="layui-form-item">
    <label class="layui-form-label">广告链接：</label>
    <div class="layui-input-inline">
      <input type="text" name="link" id="link" placeholder="请输入广告链接URL" autocomplete="off" class="layui-input">
    </div>
    <div class="layui-input-inline">
      <button type="button" class="layui-btn layui-btn-normal" id="select-ad-link">
        <i class="layui-icon layui-icon-link"></i> 选择链接
      </button>
    </div>
  </div>

  <!-- 广告描述 -->
  <div class="layui-form-item layui-form-text">
    <label class="layui-form-label">广告描述：</label>
    <div class="layui-input-block">
      <textarea name="description" placeholder="请输入广告描述（可选）" class="layui-textarea" style="min-height: 80px;"></textarea>
    </div>
  </div>

  <!-- 隐藏字段 -->
  <input type="hidden" name="buy_log_id" id="config-buy-log-id">

  <!-- 提交按钮 -->
  <div class="layui-form-item layui-hide">
    <input type="button" lay-submit lay-filter="config-submit" id="config-submit" value="确认">
  </div>
</div>

<script>
  layui.config({
    version: "{$front_version}",
    base: '/static/lib' //静态资源所在路径
  }).use(['form', 'layer'], function(){
    var $ = layui.$, form = layui.form;

    // 获取URL参数
    function getUrlParam(name) {
      var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
      var r = window.location.search.substr(1).match(reg);
      if (r != null) return unescape(r[2]);
      return null;
    }

    // 获取购买记录ID
    var buyLogId = getUrlParam('id');
    if (buyLogId) {
      $('#config-buy-log-id').val(buyLogId);

      // 加载广告位信息
      loadAdPositionInfo(buyLogId);
    }

    // 加载广告位信息
    function loadAdPositionInfo(id) {
      $.ajax({
        url: "{:url('shop/decoration.ad/getAdOrderInfo')}",
        type: 'GET',
        data: { id: id },
        success: function(response) {
          if (response.code === 1 && response.data) {
            var data = response.data;

            // 填充广告位信息
            if (data.ad_position_info) {
              $('#ad-position-name').text(data.ad_position_info.name || '未知广告位');
              $('#ad-position-desc').text('广告位ID: ' + (data.ad_position_info.id || '-'));

              // 处理广告位图片
              if (data.ad_position_info.image) {
                $('#ad-position-image').attr('src', data.ad_position_info.image).show();
              } else {
                $('#ad-position-image').hide();
              }

              // 更新图片尺寸提示
              if (data.ad_position_info.width && data.ad_position_info.height) {
                $('#image-size-tips').html(
                  '<i class="layui-icon layui-icon-tips"></i> ' +
                  '建议上传图片尺寸：' + data.ad_position_info.width + 'px × ' + data.ad_position_info.height + 'px'
                );
              }
            }

            // 更新图片数量提示
            var uploadLimit = 1; // 默认1张
            if (data.ad_buynums) {
              uploadLimit = parseInt(data.ad_buynums) || 1;
            }
            $('#max-upload-count').text(uploadLimit);
            if (uploadLimit > 1) {
              $('#image-count-tips').show();
            }

            // 保存上传限制到全局变量
            window.currentUploadLimit = uploadLimit;
          } else {
            layer.msg(response.msg || '获取广告位信息失败', {icon: 2});
          }
        },
        error: function() {
          layer.msg('网络错误，请重试', {icon: 2});
        }
      });
    }

    // 图片上传功能
    like.delUpload();
    $(document).on("click", ".add-upload-image", function () {
      var uploadLimit = window.currentUploadLimit || 1;
      like.imageUpload({
        limit: uploadLimit,
        field: "image",
        that: $(this),
        content: '/shop/file/lists?type=10'
      });
    });

    // 选择链接功能
    $(document).on('click', '#select-ad-link', function () {
      var link = $('#link').val();
      parent.layer.open({
        type: 2,
        area: ['90%', '90%'],
        title: "选择链接",
        content: '{:url("decoration.ad/select_link")}?link=' + link,
        btn: ['确定', '取消'],
        yes: function(index, layero) {
          // 获取iframe窗口对象
          var iframeWindow = parent.window['layui-layer-iframe' + index];
          if (iframeWindow && iframeWindow.getSelectedLink) {
            var selectedLink = iframeWindow.getSelectedLink();
            if (selectedLink) {
              $('#link').val(selectedLink);
              parent.layer.close(index);
            } else {
              parent.layer.msg('请选择一个链接');
            }
          } else {
            parent.layer.msg('获取选择的链接失败');
          }
        }
      });
    });

    // 表单提交
    form.on('submit(config-submit)', function(data) {
      var field = data.field;

      // 验证必填字段
      if (!field.title || !field.title.trim()) {
        layer.msg('请输入广告标题', {icon: 2});
        return false;
      }

      // 检查图片是否已上传
      var uploadedImages = $('.like-upload-image .upload-image-div');
      if (uploadedImages.length === 0) {
        layer.msg('请上传广告图片', {icon: 2});
        return false;
      }

      // 手动收集图片数据
      var imageValues = [];
      uploadedImages.each(function() {
        var imageInput = $(this).find('input[name="image"]');
        if (imageInput.length > 0 && imageInput.val()) {
          imageValues.push(imageInput.val());
        }
      });

      if (imageValues.length === 0) {
        layer.msg('请上传广告图片', {icon: 2});
        return false;
      }

      // 将图片数据添加到表单数据中
      field.image = imageValues.join(',');

      // 获取链接值
      field.link = $('#link').val();

      // 显示加载动画
      var loading = layer.load(1);

      // 发送配置请求
      $.ajax({
        url: "{:url('shop/decoration.ad/configAdContent')}",
        type: 'POST',
        data: field,
        success: function(response) {
          layer.close(loading);
          if (response.code === 1) {
            layer.msg('配置成功！', {icon: 1}, function() {
              // 关闭弹窗并刷新父页面列表
              var index = parent.layer.getFrameIndex(window.name);
              parent.layer.close(index);
              parent.layui.table.reload('ad-buy-log-table');
            });
          } else {
            layer.msg(response.msg || '配置失败', {icon: 2});
          }
        },
        error: function() {
          layer.close(loading);
          layer.msg('网络错误，请重试', {icon: 2});
        }
      });

      return false;
    });
  });
</script>
