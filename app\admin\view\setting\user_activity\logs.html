<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>用户活跃度日志</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="__ADMIN__/css/layui.css" media="all">
    <link rel="stylesheet" href="__ADMIN__/css/admin.css" media="all">
</head>
<body>
<div class="layui-fluid">
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-header">
                    <h3>用户活跃度日志</h3>
                </div>
                <div class="layui-card-body">
                    <!-- 搜索条件 -->
                    <div class="layui-form layui-row layui-col-space10" style="margin-bottom: 15px;">
                        <div class="layui-col-md3">
                            <input type="text" name="user_id" placeholder="用户ID" class="layui-input">
                        </div>
                        <div class="layui-col-md3">
                            <select name="activity_type" lay-search="">
                                <option value="">选择活动类型</option>
                                <option value="purchaser_login">采购商登录</option>
                                <option value="publish_demand">发布采购信息</option>
                                <option value="chat">用户聊天</option>
                                <option value="purchase">购买商品</option>
                            </select>
                        </div>
                        <div class="layui-col-md2">
                            <input type="text" name="start_time" placeholder="开始时间" class="layui-input" id="start-time">
                        </div>
                        <div class="layui-col-md2">
                            <input type="text" name="end_time" placeholder="结束时间" class="layui-input" id="end-time">
                        </div>
                        <div class="layui-col-md2">
                            <button class="layui-btn" lay-submit lay-filter="search">搜索</button>
                            <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                        </div>
                    </div>
                    
                    <!-- 数据表格 -->
                    <table class="layui-hide" id="logs-table" lay-filter="logs-table"></table>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="__ADMIN__/js/layui.js"></script>
<script>
layui.use(['table', 'form', 'laydate'], function(){
    var table = layui.table;
    var form = layui.form;
    var laydate = layui.laydate;
    
    // 初始化日期选择器
    laydate.render({
        elem: '#start-time',
        type: 'datetime'
    });
    
    laydate.render({
        elem: '#end-time',
        type: 'datetime'
    });
    
    // 初始化表格
    var tableIns = table.render({
        elem: '#logs-table',
        url: '{:url("logs")}',
        page: true,
        limit: 20,
        limits: [10, 20, 50, 100],
        cols: [[
            {field: 'id', title: 'ID', width: 80, sort: true},
            {field: 'user_info', title: '用户信息', width: 150, templet: function(d){
                if(d.user_info) {
                    return '<div>' + (d.user_info.nickname || '未设置昵称') + '</div>' +
                           '<div style="color: #999; font-size: 12px;">' + (d.user_info.mobile || '') + '</div>';
                }
                return '<span style="color: #999;">用户已删除</span>';
            }},
            {field: 'activity_type_desc', title: '活动类型', width: 120},
            {field: 'score_change_text', title: '积分变化', width: 100, templet: function(d){
                var color = d.score_change > 0 ? '#5FB878' : (d.score_change < 0 ? '#FF5722' : '#999');
                return '<span style="color: ' + color + '; font-weight: bold;">' + d.score_change_text + '</span>';
            }},
            {field: 'before_score', title: '变化前积分', width: 110},
            {field: 'after_score', title: '变化后积分', width: 110},
            {field: 'level_change_text', title: '等级变化', width: 150, templet: function(d){
                if(d.before_level != d.after_level) {
                    return '<span style="color: #5FB878;">' + d.level_change_text + '</span>';
                }
                return d.level_change_text;
            }},
            {field: 'remark', title: '备注', width: 120},
            {field: 'create_time', title: '创建时间', width: 160, sort: true}
        ]]
    });
    
    // 搜索功能
    form.on('submit(search)', function(data){
        var field = data.field;
        // 重新加载表格
        tableIns.reload({
            where: field,
            page: {
                curr: 1
            }
        });
        return false;
    });
    
    // 重置功能
    form.on('reset', function(){
        tableIns.reload({
            where: {},
            page: {
                curr: 1
            }
        });
    });
});
</script>
</body>
</html>
