(window.webpackJsonp=window.webpackJsonp||[]).push([[25,6],{441:function(e,t,r){var content=r(444);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[e.i,content,""]]),content.locals&&(e.exports=content.locals);(0,r(14).default)("3181fc86",content,!0,{sourceMap:!1})},442:function(e,t,r){"use strict";r.r(t);r(437),r(80),r(272);var o={data:function(){return{priceSlice:{}}},components:{},props:{firstSize:{type:Number,default:14},secondSize:{type:Number,default:14},color:{type:String},weight:{type:[String,Number],default:400},price:{type:[String,Number],default:""},showSubscript:{type:Boolean,default:!0},subscriptSize:{type:Number,default:14},lineThrough:{type:Boolean,default:!1}},created:function(){this.priceFormat()},watch:{price:function(e){this.priceFormat()}},methods:{priceFormat:function(){var e=this.price,t={};null!==e&&(e=parseFloat(e),e=String(e).split("."),t.first=e[0],t.second=e[1],this.priceSlice=t)}}},d=(r(443),r(9)),component=Object(d.a)(o,(function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("span",{class:(e.lineThrough?"line-through":"")+"price-format",style:{color:e.color,"font-weight":e.weight}},[e.showSubscript?r("span",{style:{"font-size":e.subscriptSize+"px","margin-right":"1px"}},[e._v("¥")]):e._e(),e._v(" "),r("span",{style:{"font-size":e.firstSize+"px","margin-right":"1px"}},[e._v(e._s(e.priceSlice.first))]),e._v(" "),e.priceSlice.second?r("span",{style:{"font-size":e.secondSize+"px"}},[e._v("."+e._s(e.priceSlice.second))]):e._e()])}),[],!1,null,null,null);t.default=component.exports},443:function(e,t,r){"use strict";r(441)},444:function(e,t,r){var o=r(13)(!1);o.push([e.i,".price-format{display:flex;align-items:baseline}",""]),e.exports=o},480:function(e,t,r){var content=r(504);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[e.i,content,""]]),content.locals&&(e.exports=content.locals);(0,r(14).default)("ed2618a4",content,!0,{sourceMap:!1})},503:function(e,t,r){"use strict";r(480)},504:function(e,t,r){var o=r(13)(!1);o.push([e.i,".address-list[data-v-425028d8]  .el-dialog__body{height:460px;overflow-y:auto}.address-list .list[data-v-425028d8]{margin:0 auto;width:800px}.address-list .list .item[data-v-425028d8]{position:relative;cursor:pointer;height:100px;padding:16px 150px 16px 20px;border:1px solid hsla(0,0%,89.8%,.89804);border-radius:2px}.address-list .list .item.active[data-v-425028d8]{border-color:#ff2c3c}.address-list .list .item .oprate[data-v-425028d8]{position:absolute;right:20px;bottom:9px}.address-list .dialog-footer[data-v-425028d8]{text-align:center}.address-list .dialog-footer .el-button[data-v-425028d8]{width:160px}",""]),e.exports=o},520:function(e,t,r){var content=r(564);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[e.i,content,""]]),content.locals&&(e.exports=content.locals);(0,r(14).default)("19522337",content,!0,{sourceMap:!1})},556:function(e,t,r){"use strict";r.r(t);var o=r(6),d=(r(51),{components:{},props:{value:{type:Boolean,default:!1}},data:function(){return{showDialog:!1,showAddressAdd:!1,addressList:[],selectId:0,editId:""}},methods:{getAddress:function(){var e=this;return Object(o.a)(regeneratorRuntime.mark((function t(){var r,code,data;return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,e.$get("user_address/lists");case 2:r=t.sent,code=r.code,data=r.data,1==code&&(e.addressList=data);case 6:case"end":return t.stop()}}),t)})))()},setDefault:function(e){var t=this;return Object(o.a)(regeneratorRuntime.mark((function r(){var o,code,d;return regeneratorRuntime.wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return r.next=2,t.$post("user_address/setDefault",{id:e});case 2:o=r.sent,code=o.code,o.data,d=o.msg,1==code&&(t.$message({message:d,type:"success"}),t.getAddress());case 7:case"end":return r.stop()}}),r)})))()},onConfirm:function(){this.$emit("confirm",this.selectId),this.showDialog=!1}},watch:{value:function(e){this.showDialog=e,1==e&&this.getAddress()},showDialog:function(e){this.$emit("input",e)}}}),n=(r(503),r(9)),component=Object(n.a)(d,(function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"address-list"},[r("el-dialog",{attrs:{title:"更换地址",visible:e.showDialog,width:"900px"},on:{"update:visible":function(t){e.showDialog=t}}},[r("div",{staticClass:"list black"},e._l(e.addressList,(function(t,o){return r("div",{key:o,class:["item m-b-16",{active:t.id==e.selectId}],on:{click:function(r){e.selectId=t.id}}},[r("div",[r("span",{staticClass:"weigth-500"},[e._v(e._s(t.contact))]),e._v("\n                    "+e._s(t.telephone)+"\n                    "),t.is_default?r("el-tag",{attrs:{size:"mini",type:"warning",effect:"dark"}},[e._v("默认")]):e._e()],1),e._v(" "),r("div",{staticClass:"lighter m-t-8"},[e._v("\n                    "+e._s(t.province)+" "+e._s(t.city)+" "+e._s(t.district)+"\n                    "+e._s(t.address)+"\n                ")]),e._v(" "),r("div",{staticClass:"oprate lighter flex"},[r("div",{staticClass:"m-r-16",on:{click:function(r){r.stopPropagation(),e.editId=t.id,e.showAddressAdd=!0}}},[e._v("\n                        修改\n                    ")]),e._v(" "),r("div",{on:{click:function(r){return r.stopPropagation(),e.setDefault(t.id)}}},[e._v("设为默认")])])])})),0),e._v(" "),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{attrs:{type:"primary"},on:{click:e.onConfirm}},[e._v("确认")]),e._v(" "),r("el-button",{on:{click:function(t){e.showDialog=!1}}},[e._v("取消")])],1)]),e._v(" "),r("address-add",{attrs:{aid:e.editId},on:{success:e.getAddress},model:{value:e.showAddressAdd,callback:function(t){e.showAddressAdd=t},expression:"showAddressAdd"}})],1)}),[],!1,null,"425028d8",null);t.default=component.exports;installComponents(component,{AddressAdd:r(497).default})},563:function(e,t,r){"use strict";r(520)},564:function(e,t,r){var o=r(13)(!1);o.push([e.i,'.confirm-order[data-v-30d8e464]{padding:16px 0}.confirm-order .title[data-v-30d8e464]{padding:12px 20px;font-weight:700}.confirm-order .title>i[data-v-30d8e464]{cursor:pointer}.confirm-order .contact[data-v-30d8e464]{padding:10px 20px 22px}.confirm-order .contact-item[data-v-30d8e464]{display:flex;align-items:center;height:36px}.confirm-order .contact-item-label[data-v-30d8e464]{width:72px;color:#999}.confirm-order .order-hd .address[data-v-30d8e464]{padding:10px 20px 22px}.confirm-order .order-hd .address .address-con[data-v-30d8e464]{position:relative;cursor:pointer;width:800px;height:100px;padding:16px 150px 16px 20px;border:1px solid #ff2c3c;border-radius:2px}.confirm-order .order-hd .address .address-con:hover .oprate[data-v-30d8e464]{display:flex}.confirm-order .order-hd .address .address-con .oprate[data-v-30d8e464]{display:none;position:absolute;right:20px;bottom:9px}.confirm-order .order-hd .address .address-add[data-v-30d8e464]{cursor:pointer;width:320px;height:100px;border:1px dashed hsla(0,0%,89.8%,.89804)}.confirm-order .order-con .shop[data-v-30d8e464]{padding:0 20px}.confirm-order .order-con .shop .shop-name[data-v-30d8e464]{height:40px;background-color:#f6f6f6;line-height:40px}.confirm-order .order-con .goods[data-v-30d8e464]{border-bottom:1px dashed hsla(0,0%,89.8%,.89804)}.confirm-order .order-con .goods .goods-hd[data-v-30d8e464]{height:40px;padding:0 20px}.confirm-order .order-con .goods .goods-list .item[data-v-30d8e464]{padding:10px 0}.confirm-order .order-con .goods .goods-list .item-disabled[data-v-30d8e464]{position:relative}.confirm-order .order-con .goods .goods-list .item-disabled[data-v-30d8e464]:before{z-index:9;position:absolute;top:0;left:0;bottom:0;right:0;height:100%;display:block;content:"";background-color:hsla(0,0%,100%,.5)}.confirm-order .order-con .goods .info[data-v-30d8e464]{width:500px}.confirm-order .order-con .goods .info .pictrue[data-v-30d8e464]{margin-right:10px}.confirm-order .order-con .goods .info .pictrue .el-image[data-v-30d8e464]{width:72px;height:72px}.confirm-order .order-con .goods .info .name[data-v-30d8e464]{margin-bottom:10px}.confirm-order .order-con .goods .info .delivery-support[data-v-30d8e464]{font-size:12px;padding:4px 15px;border-radius:60px;margin-left:20px;background-color:#f4f4f4;color:#666}.confirm-order .order-con .goods .price[data-v-30d8e464]{width:200px}.confirm-order .order-con .goods .num[data-v-30d8e464]{width:250px}.confirm-order .order-con .goods .money[data-v-30d8e464]{width:200px}.confirm-order .order-con .input .textarea[data-v-30d8e464]{margin:0 20px;width:1000px}.confirm-order .order-con .integral .check-box[data-v-30d8e464]{padding:0 20px 12px}.confirm-order .order-footer[data-v-30d8e464]{margin-top:2px;padding:25px 20px;justify-content:flex-end}.confirm-order .order-footer .btn[data-v-30d8e464]{width:152px;height:44px;border-radius:6px;cursor:pointer}',""]),e.exports=o},650:function(e,t,r){"use strict";r.r(t);r(22),r(19),r(28),r(20),r(29);var o=r(10),d=r(6),n=(r(51),r(190),r(65),r(21),r(80),r(99),r(11));function c(object,e){var t=Object.keys(object);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(object);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(object,e).enumerable}))),t.push.apply(t,r)}return t}function l(e){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?c(Object(source),!0).forEach((function(t){Object(o.a)(e,t,source[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(source)):c(Object(source)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(source,t))}))}return e}var f={head:function(){return{title:this.$store.getters.headTitle,link:[{rel:"icon",type:"image/x-icon",href:this.$store.getters.favicon}]}},asyncData:function(e){return Object(d.a)(regeneratorRuntime.mark((function t(){var r,o,d,n,c,l;return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return r=e.query,o=e.$post,e.$get,d=JSON.parse(decodeURIComponent(r.data)),n=d.goods,c=(c=d.carts)||[],l={},t.next=6,o("order/settlement",{goods:JSON.stringify(n),cart_id:c.join()}).then((function(e){var code=e.code,data=e.data;e.msg;1==code&&(l.orderInfo=data,l.address=null==data?void 0:data.address,l.userRemark=data.shop.map((function(e){return{shop_id:e.shop_id,remark:""}})),l.selecteCoupon=data.shop.map((function(){return""})))}));case 6:return t.abrupt("return",l);case 7:case"end":return t.stop()}}),t)})))()},data:function(){return{orderInfo:{},address:{},carts:[],active:0,userRemark:[],selecteCoupon:[],showAddress:!1,showAddressAdd:!1,addressId:"",editId:"",isEdit:!1,shopPage:1}},watch:{address:{handler:function(e){this.addressId=e.id},immediate:!0}},methods:l(l({},Object(n.b)(["getPublicData"])),{},{editAddress:function(e){this.editId=e,this.showAddressAdd=!0},changeAddress:function(e){this.addressId=e,this.orderBuy()},submitOrder:function(){var e=this;return Object(d.a)(regeneratorRuntime.mark((function t(){var r,o,d,n,c,data;return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return r=e.carts,o=e.goods,d=e.selecteCoupon,n={goods:JSON.stringify(o),address_id:e.addressId,cart_id:r.join(),coupon_id:d.filter((function(e){return e})),distribution_type:0,remark:e.userRemark.length?JSON.stringify(e.userRemark):""},t.next=4,e.$post("order/submit_order",n);case 4:c=t.sent,data=c.data,1==c.code&&(e.getPublicData(),e.$router.replace({path:"/payment",query:{id:data.trade_id,from:data.type}}));case 8:case"end":return t.stop()}}),t)})))()},orderBuy:function(){var e=this;return Object(d.a)(regeneratorRuntime.mark((function t(){var r,data,address;return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return console.log(e.selecteCoupon),t.next=3,e.$post("order/settlement",{goods:JSON.stringify(e.goods),address_id:e.addressId,cart_id:e.carts.join(),coupon_id:e.selecteCoupon.filter((function(e){return e}))});case 3:r=t.sent,data=r.data,1==r.code&&(address=data.address,e.orderInfo=data,e.address=address);case 7:case"end":return t.stop()}}),t)})))()}}),created:function(){var e=JSON.parse(decodeURIComponent(this.$route.query.data)),t=e.goods,r=e.type,o=e.carts;this.goods=t,this.type=r,this.carts=o||[]}},v=(r(563),r(9)),component=Object(v.a)(f,(function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"confirm-order"},[r("div",{staticClass:"order-hd bg-white m-b-16"},[r("div",[r("div",{staticClass:"title lg"},[e._v("收货地址")]),e._v(" "),r("div",{staticClass:"address flex row-between"},[r("div",{directives:[{name:"show",rawName:"v-show",value:e.address.contact,expression:"address.contact"}],staticClass:"address-con"},[r("div",[r("span",{staticClass:"weight-500"},[e._v(e._s(e.address.contact))]),e._v("\n                        "+e._s(e.address.telephone)+"\n                        "),e.address.is_default?r("el-tag",{attrs:{size:"mini",type:"warning",effect:"dark"}},[e._v("默认")]):e._e()],1),e._v(" "),r("div",{staticClass:"lighter m-t-8"},[e._v("\n                        "+e._s(e.address.province)+" "+e._s(e.address.city)+"\n                        "+e._s(e.address.district)+"\n                        "+e._s(e.address.address)+"\n                    ")]),e._v(" "),r("div",{staticClass:"oprate primary flex"},[r("div",{staticClass:"m-r-16",on:{click:function(t){return e.editAddress(e.address.id)}}},[e._v("\n                            修改\n                        ")]),e._v(" "),r("div",{on:{click:function(t){e.showAddress=!0}}},[e._v("更换地址")])])]),e._v(" "),r("div",{staticClass:"address-add flex row-center",on:{click:function(t){return e.editAddress("")}}},[e._v("\n                    + 添加地址\n                ")])])])]),e._v(" "),r("div",{staticClass:"order-con bg-white"},[r("div",{staticClass:"goods m-b-16"},[r("div",{staticClass:"title lg"},[e._v("商品明细")]),e._v(" "),e._m(0),e._v(" "),r("div",{staticClass:"shop"},e._l(e.orderInfo.shop,(function(t,o){return r("div",{key:o,staticClass:"shop-item flex-col flex-1"},[r("div",{staticClass:"shop-name p-l-10 m-b-10 flex flex-1",staticStyle:{width:"1140px"}},[e._v("\n                        "+e._s(t.shop_name)+"\n                    ")]),e._v(" "),r("div",{staticClass:"goods-list flex flex-1",staticStyle:{width:"1140px"}},e._l(t.goods,(function(t,o){return r("div",{key:o,class:["flex","item"]},[r("div",{staticClass:"info flex"},[r("div",{staticClass:"pictrue flex-none"},[r("el-image",{attrs:{src:t.image}})],1),e._v(" "),r("div",[r("div",{staticClass:"name line-2"},[e._v("\n                                        "+e._s(t.name)+"\n                                    ")]),e._v(" "),r("div",{staticClass:"muted m-t-10 xs"},[e._v("\n                                        "+e._s(t.spec_value)+"\n                                    ")])])]),e._v(" "),r("div",{staticClass:"price flex row-center"},[r("price-formate",{attrs:{price:t.price}})],1),e._v(" "),r("div",{staticClass:"num text-center"},[e._v("\n                                "+e._s(t.num)+"\n                            ")]),e._v(" "),r("div",{staticClass:"money flex row-center"},[r("price-formate",{attrs:{price:t.sum_price}})],1)])})),0),e._v(" "),r("div",{staticClass:"flex flex-1 col-top m-t-20 row-between",staticStyle:{width:"1140px"}},[r("div",{staticClass:"flex"},[r("div",{staticClass:"remark flex flex-1 m-r-10"},[r("div",{staticStyle:{width:"70px"}},[e._v("买家备注：")]),e._v(" "),r("div",{staticClass:"textarea",staticStyle:{width:"280px"}},[r("el-input",{attrs:{size:"small",placeholder:"选填，给商家备注留言，100字以内",resize:"none"},model:{value:e.userRemark[o].remark,callback:function(t){e.$set(e.userRemark[o],"remark",t)},expression:"userRemark[index].remark"}})],1)]),e._v(" "),0==e.orderInfo.order_type?r("div",{staticClass:"coupon flex flex-1 m-r-10"},[r("div",[e._v("店铺优惠：")]),e._v(" "),r("el-select",{attrs:{size:"small",placeholder:"请选择"},on:{change:e.orderBuy},model:{value:e.selecteCoupon[o],callback:function(t){e.$set(e.selecteCoupon,o,t)},expression:"selecteCoupon[index]"}},[r("el-option",{attrs:{label:"不使用",value:0}}),e._v(" "),e._l(t.coupon_list,(function(e){return r("el-option",{key:e.value,attrs:{label:e.coupon_name,value:e.id}})}))],2)],1):e._e(),e._v(" "),r("div",{staticClass:"remark flex flex-1"},[r("div",[e._v("配送方式：")]),e._v(" "),r("el-select",{attrs:{size:"small",placeholder:"请选择"},model:{value:t.distribution_type,callback:function(r){e.$set(t,"distribution_type",r)},expression:"item.distribution_type"}},[r("el-option",{attrs:{label:"快递",value:0}})],1)],1)]),e._v(" "),r("div",{staticClass:"flex-col"},[t.discount_amount?r("div",{staticClass:"flex coupon m-b-10 flex-1 row-right"},[r("div",[e._v("优惠：")]),e._v(" "),r("div",[e._v("-￥"+e._s(t.discount_amount))])]):e._e(),e._v(" "),r("div",{staticClass:"flex remark m-b-10 flex-1 row-right"},[r("div",[e._v("运费：")]),e._v(" "),r("div",[e._v("￥"+e._s(t.shipping_price))])]),e._v(" "),r("div",{staticClass:"flex m-b-20 flex-1 row-right"},[r("span",{staticClass:"m-r-10"},[e._v("店铺合计")]),e._v(" "),r("price-formate",{attrs:{color:"#FF2C3C",price:t.total_amount,firstSize:17,subscriptSize:12,secondSize:12}})],1)])])])})),0)])]),e._v(" "),r("div",{staticClass:"order-footer flex bg-white"},[r("div",{staticClass:"flex col-center"},[r("div",{staticClass:"money flex m-r-16"},[r("div",{staticClass:"lighter"},[e._v("实付金额：")]),e._v(" "),r("div",{staticClass:"primary",staticStyle:{"font-size":"20px"}},[r("span",{staticClass:"xxs"},[e._v("¥")]),e._v(e._s(e.orderInfo.total_amount)+"\n                ")])]),e._v(" "),r("div",{staticClass:"white bg-primary lg btn flex row-center",on:{click:e.submitOrder}},[e._v("\n                去结算\n            ")])])]),e._v(" "),r("address-add",{attrs:{aid:e.editId},on:{success:e.orderBuy},model:{value:e.showAddressAdd,callback:function(t){e.showAddressAdd=t},expression:"showAddressAdd"}}),e._v(" "),r("address-list",{on:{confirm:e.changeAddress},model:{value:e.showAddress,callback:function(t){e.showAddress=t},expression:"showAddress"}})],1)}),[function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"goods-hd flex lighter"},[r("div",{staticClass:"info text-center"},[e._v("商品信息")]),e._v(" "),r("div",{staticClass:"price text-center"},[e._v("单价")]),e._v(" "),r("div",{staticClass:"num text-center"},[e._v("数量")]),e._v(" "),r("div",{staticClass:"money text-center"},[e._v("合计")])])}],!1,null,"30d8e464",null);t.default=component.exports;installComponents(component,{PriceFormate:r(442).default,AddressAdd:r(497).default,AddressList:r(556).default})}}]);