<?php
namespace app\admin\controller\user;

use app\common\basics\AdminBase;
use app\admin\logic\PurchaserPackageLogic;
use app\common\server\JsonServer;

/**
 * 采购套餐控制器
 * Class PurchaserPackage
 * @package app\admin\controller\user
 */
class PurchaserPackage extends AdminBase
{
    /**
     * @notes 套餐列表
     * @return \think\response\View
     */
    public function index()
    {
        if ($this->request->isAjax()) {
            $params = $this->request->get();
            $data = PurchaserPackageLogic::lists($params);
            return JsonServer::success('获取成功', $data);
        }
        return view('user/purchaser_package/index');
    }

    /**
     * @notes 添加套餐
     * @return \think\response\View
     */
    public function add()
    {
        if ($this->request->isPost()) {
            $params = $this->request->post();

            // 手动验证
            if (empty($params['name'])) {
                return JsonServer::error('套餐名称不能为空');
            }
            if (empty($params['purchaser_count']) || !is_numeric($params['purchaser_count']) || $params['purchaser_count'] <= 0) {
                return JsonServer::error('分配人数必须为大于0的整数');
            }
            if (empty($params['price']) || !is_numeric($params['price']) || $params['price'] <= 0) {
                return JsonServer::error('套餐价格必须为大于0的数字');
            }
            if (!isset($params['status']) || !in_array($params['status'], [0, 1])) {
                return JsonServer::error('状态值不正确');
            }
            if (empty($params['sort']) || !is_numeric($params['sort']) || $params['sort'] < 0) {
                return JsonServer::error('排序必须为非负整数');
            }

            if (PurchaserPackageLogic::add($params)) {
                return JsonServer::success('添加成功');
            }
            return JsonServer::error(PurchaserPackageLogic::getError());
        }
        return view('user/purchaser_package/add');
    }

    /**
     * @notes 编辑套餐
     * @return \think\response\View
     */
    public function edit()
    {
        if ($this->request->isPost()) {
            $params = $this->request->post();

            // 手动验证
            if (empty($params['id'])) {
                return JsonServer::error('参数错误: ID不能为空');
            }
            if (empty($params['name'])) {
                return JsonServer::error('套餐名称不能为空');
            }
            if (empty($params['purchaser_count']) || !is_numeric($params['purchaser_count']) || $params['purchaser_count'] <= 0) {
                return JsonServer::error('分配人数必须为大于0的整数');
            }
            if (empty($params['price']) || !is_numeric($params['price']) || $params['price'] <= 0) {
                return JsonServer::error('套餐价格必须为大于0的数字');
            }
            if (!isset($params['status']) || !in_array($params['status'], [0, 1])) {
                return JsonServer::error('状态值不正确');
            }
            if (empty($params['sort']) || !is_numeric($params['sort']) || $params['sort'] < 0) {
                return JsonServer::error('排序必须为非负整数');
            }

            if (PurchaserPackageLogic::edit($params)) {
                return JsonServer::success('编辑成功');
            }
            return JsonServer::error(PurchaserPackageLogic::getError());
        }
        $id = $this->request->get('id');
        if (empty($id)) {
            return JsonServer::error('参数错误: ID不能为空');
        }
        $detail = PurchaserPackageLogic::detail($id);
        return view('user/purchaser_package/edit', ['detail' => $detail]);
    }

    /**
     * @notes 删除套餐
     */
    public function del()
    {
        $id = $this->request->post('id');
        if (empty($id)) {
            return JsonServer::error('参数错误: ID不能为空');
        }
        if (PurchaserPackageLogic::del($id)) {
             return JsonServer::success('删除成功');
        }
        return JsonServer::error(PurchaserPackageLogic::getError());
    }

    /**
     * @notes 修改状态
     */
    public function status()
    {
        $params = $this->request->post();
        if (empty($params['id'])) {
            return JsonServer::error('参数错误: ID不能为空');
        }
        if (!isset($params['status']) || !in_array($params['status'], [0, 1])) {
            return JsonServer::error('参数错误: 状态值不正确');
        }

        if (PurchaserPackageLogic::status($params)) {
             return JsonServer::success('操作成功');
        }
         return JsonServer::error(PurchaserPackageLogic::getError());
    }

    /**
     * @notes 购买记录
     * @return \think\response\View
     */
    public function records()
    {
        if ($this->request->isAjax()) {
            $params = $this->request->get();
            $data = PurchaserPackageLogic::records($params);
            return JsonServer::success('获取成功', $data['lists'], $data['count']);
        }
        $package_id = $this->request->get('package_id');
        return view('user/purchaser_package/records', ['package_id' => $package_id]);
    }
}
