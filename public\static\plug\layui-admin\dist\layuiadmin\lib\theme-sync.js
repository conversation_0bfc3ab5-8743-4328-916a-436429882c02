/**
 * LayuiAdmin iframe主题同步脚本
 * 用于iframe内容页面与父页面的主题同步
 * 优化版本 - 解决主题切换时iframe颜色不变的问题
 */

(function() {
  'use strict';

  let isInitialized = false;

  // 主题同步功能
  function initThemeSync() {
    if (isInitialized) return;
    isInitialized = true;

    // 从localStorage获取父页面的主题设置
    const savedTheme = localStorage.getItem('layuiadmin-theme') || 'light';
    applyTheme(savedTheme);

    // 监听父页面的主题变化（通过localStorage）
    window.addEventListener('storage', function(e) {
      if (e.key === 'layuiadmin-theme') {
        applyTheme(e.newValue || 'light');
      }
    });

    // 监听父页面的消息（用于实时主题同步）
    window.addEventListener('message', function(e) {
      if (e.data && e.data.type === 'theme-change') {
        applyTheme(e.data.theme);
      }
      // 响应父页面的主题请求
      if (e.data && e.data.type === 'request-theme') {
        const currentTheme = localStorage.getItem('layuiadmin-theme') || 'light';
        try {
          e.source.postMessage({
            type: 'theme-response',
            theme: currentTheme
          }, '*');
        } catch (err) {
          console.log('无法响应主题请求:', err);
        }
      }
    });

    // 如果是在iframe中，尝试从父页面获取当前主题
    if (window.parent !== window) {
      try {
        // 向父页面请求当前主题
        window.parent.postMessage({
          type: 'request-theme'
        }, '*');
      } catch (e) {
        console.log('无法与父页面通信:', e);
      }
    }

    // 定期检查主题状态（作为备用机制）
    setInterval(function() {
      const currentTheme = localStorage.getItem('layuiadmin-theme') || 'light';
      const bodyTheme = document.body.getAttribute('data-theme') || 'light';
      if (currentTheme !== bodyTheme) {
        applyTheme(currentTheme);
      }
    }, 1000);
  }

  // 应用主题
  function applyTheme(theme) {
    const body = document.body;
    const html = document.documentElement;

    // 移除所有主题类
    body.removeAttribute('data-theme');
    html.removeAttribute('data-theme');

    // 设置新主题
    if (theme && theme !== 'light') {
      body.setAttribute('data-theme', theme);
      html.setAttribute('data-theme', theme);
    }

    // 强制重新渲染页面样式
    body.style.display = 'none';
    body.offsetHeight; // 触发重排
    body.style.display = '';

    // 更新所有可能的元素
    updateElementStyles(theme);

    // 触发自定义事件，允许页面监听主题变化
    const event = new CustomEvent('themeChanged', {
      detail: { theme: theme }
    });
    document.dispatchEvent(event);

    console.log('主题已应用:', theme);
  }

  // 更新元素样式
  function updateElementStyles(theme) {
    // 强制更新所有使用CSS变量的元素
    const elements = document.querySelectorAll('*');
    elements.forEach(function(el) {
      if (el.style && el.style.getPropertyValue) {
        // 触发样式重新计算
        const computedStyle = window.getComputedStyle(el);
        if (computedStyle.backgroundColor || computedStyle.color) {
          el.style.opacity = '0.999';
          setTimeout(function() {
            el.style.opacity = '';
          }, 1);
        }
      }
    });
  }

  // 获取当前主题
  function getCurrentTheme() {
    return localStorage.getItem('layuiadmin-theme') || 'light';
  }

  // 设置主题（仅在父页面中使用）
  function setTheme(theme) {
    if (window.parent === window) {
      // 这是父页面
      localStorage.setItem('layuiadmin-theme', theme);
      applyTheme(theme);

      // 向所有iframe发送主题变化消息
      const iframes = document.querySelectorAll('.layadmin-iframe');
      iframes.forEach(iframe => {
        try {
          iframe.contentWindow.postMessage({
            type: 'theme-change',
            theme: theme
          }, '*');
        } catch (e) {
          console.log('无法向iframe发送消息:', e);
        }
      });
    }
  }

  // 初始化
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initThemeSync);
  } else {
    initThemeSync();
  }

  // 导出API
  window.LayuiTheme = {
    init: initThemeSync,
    apply: applyTheme,
    get: getCurrentTheme,
    set: setTheme
  };

})();
