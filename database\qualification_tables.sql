-- 资质管理相关表结构

-- ----------------------------
-- Table structure for ls_qualification (资质表)
-- ----------------------------
DROP TABLE IF EXISTS `ls_qualification`;
CREATE TABLE `ls_qualification` (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '证件名称',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '文字描述',
  `valid_days` int(11) UNSIGNED NULL DEFAULT 0 COMMENT '有效期天数，0表示永久有效',
  `status` tinyint(1) UNSIGNED NULL DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  `sort` int(11) UNSIGNED NULL DEFAULT 255 COMMENT '排序',
  `del` tinyint(1) UNSIGNED NULL DEFAULT 0 COMMENT '是否删除:1-是;0-否',
  `create_time` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `update_time` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_status_del` (`status`, `del`) USING BTREE,
  INDEX `idx_name` (`name`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '资质管理表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ls_goods_category_qualification (分类资质关联表)
-- ----------------------------
DROP TABLE IF EXISTS `ls_goods_category_qualification`;
CREATE TABLE `ls_goods_category_qualification` (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `category_id` int(11) UNSIGNED NOT NULL COMMENT '分类ID',
  `qualification_id` int(11) UNSIGNED NOT NULL COMMENT '资质ID',
  `create_time` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_category_qualification` (`category_id`, `qualification_id`) USING BTREE,
  INDEX `idx_category_id` (`category_id`) USING BTREE,
  INDEX `idx_qualification_id` (`qualification_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '分类资质关联表' ROW_FORMAT = Dynamic;

-- 插入一些示例资质数据
INSERT INTO `ls_qualification` (`name`, `description`, `valid_days`, `status`, `sort`, `del`, `create_time`, `update_time`) VALUES
('营业执照', '企业营业执照，证明企业合法经营资格', 0, 1, 1, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('食品经营许可证', '食品类商品销售必需的许可证', 1095, 1, 2, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('药品经营许可证', '药品类商品销售必需的许可证', 1825, 1, 3, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('医疗器械经营许可证', '医疗器械类商品销售必需的许可证', 1825, 1, 4, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('化妆品生产许可证', '化妆品类商品销售必需的许可证', 1825, 1, 5, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('3C认证证书', '强制性产品认证证书', 1095, 1, 6, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('商标注册证', '商标使用权证明', 3650, 1, 7, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('质量管理体系认证', 'ISO9001等质量管理体系认证', 1095, 1, 8, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());
