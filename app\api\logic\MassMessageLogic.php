<?php
namespace app\api\logic;

use app\common\basics\Logic;
use app\common\model\shop\Shop;
use think\facade\Db;

/**
 * 群发信息限制逻辑层
 * Class MassMessageLogic
 * @package app\api\logic
 */
class MassMessageLogic extends Logic
{
    /**
     * 检查商家今日群发信息是否超出限制
     * @param int $shopId 商家ID
     * @return bool
     */
    public static function checkMassMessageLimit($shopId)
    {
        $shop = Shop::find($shopId);
        if (!$shop) {
            return false;
        }

        // 获取商家等级
        $tierLevel = $shop->tier_level;
        // 获取群发限制配置
        $config = Db::name('mass_message_limit')
            ->where('tier_level', $tierLevel)
            ->find();

        if (!$config) {
            return false; // 没有配置，无法检查限制
        }

        $dailyLimit = $config['daily_limit'];
        $today = date('Y-m-d');
        // 统计今日已发送的群发信息数量
        $sentCount = Db::name('mass_message_record')
            ->where('shop_id', $shopId)
            ->whereTime('create_time', 'today')
            ->count();

        return $sentCount < $dailyLimit;
    }

    /**
     * 记录群发信息发送
     * @param int $shopId 商家ID
     * @param int $messageId 消息ID
     * @return bool
     */
    public static function recordMassMessage($shopId, $messageId)
    {
        $data = [
            'shop_id' => $shopId,
            'message_id' => $messageId,
            'create_time' => time()
        ];

        return Db::name('mass_message_record')->insert($data) !== false;
    }

    /**
     * 获取商家今日群发信息限制和已发送数量
     * @param int $shopId 商家ID
     * @return array
     */
    public static function getMassMessageStats($shopId)
    {
        $shop = Shop::find($shopId);
        if (!$shop) {
            return ['limit' => 0, 'sent' => 0, 'remaining' => 0];
        }

        // 获取商家等级
        $tierLevel = $shop->tier_level;
        // 获取群发限制配置
        $config = Db::name('mass_message_limit')
            ->where('tier_level', $tierLevel)
            ->find();

        $dailyLimit = $config ? $config['daily_limit'] : 0;
        $sentCount = Db::name('mass_message_record')
            ->where('shop_id', $shopId)
            ->whereTime('create_time', 'today')
            ->count();

        return [
            'limit' => $dailyLimit,
            'sent' => $sentCount,
            'remaining' => max(0, $dailyLimit - $sentCount)
        ];
    }
}
