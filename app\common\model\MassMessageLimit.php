<?php
namespace app\common\model;

use app\common\basics\Models;

/**
 * 群发信息限制配置模型
 * Class MassMessageLimit
 * @package app\common\model
 */
class MassMessageLimit extends Models
{
    /**
     * 获取群发信息限制配置列表
     * @return array
     */
    public static function getList()
    {
        return self::order('tier_level', 'asc')->select()->toArray();
    }

    /**
     * 添加群发信息限制配置
     * @param array $data
     * @return bool
     */
    public static function add($data)
    {
        // 验证百分比总和
        if (!self::validatePercentages($data)) {
            return false;
        }

        $data['create_time'] = time();
        $data['update_time'] = time();
        return self::create($data)->getPk() !== false;
    }

    /**
     * 编辑群发信息限制配置
     * @param array $data
     * @return bool
     */
    public static function edit($data)
    {
        // 验证百分比总和
        if (!self::validatePercentages($data)) {
            return false;
        }

        $id = $data['id'];
        unset($data['id']);
        $data['update_time'] = time();
        return self::update($data, ['id' => $id]) !== false;
    }

    /**
     * 删除群发信息限制配置
     * @return bool
     */
    public function delete(): bool
    {
        return parent::delete();
    }

    /**
     * 获取单个群发信息限制配置信息
     * @param int $id
     * @return array
     */
    public static function getInfo($id)
    {
        return self::find($id)->toArray();
    }

    /**
     * 根据商家等级获取配置
     * @param int $tierLevel
     * @return array|null
     */
    public static function getConfigByTierLevel($tierLevel)
    {
        return self::where('tier_level', $tierLevel)->find();
    }

    /**
     * 验证百分比总和是否为100
     * @param array $data
     * @return bool
     */
    private static function validatePercentages($data)
    {
        $total = ($data['level1_percent'] ?? 0) + ($data['level2_percent'] ?? 0) + ($data['level3_percent'] ?? 0);
        return abs($total - 100) < 0.01; // 允许小数点误差
    }

    /**
     * 获取商家等级名称
     * @param int $tierLevel
     * @return string
     */
    public static function getTierLevelName($tierLevel)
    {
        $names = [
            0 => '0元入驻',
            1 => '商家会员',
            2 => '实力厂商'
        ];

        return $names[$tierLevel] ?? '未知等级';
    }

    /**
     * 批量删除
     * @param mixed $ids
     * @return bool
     */
    public static function deleteAll($ids)
    {
        return self::destroy($ids) !== false;
    }

    /**
     * 获取采购人员分配配置
     * @param int $tierLevel
     * @return array
     */
    public static function getPurchaserAllocationConfig($tierLevel)
    {
        $config = self::getConfigByTierLevel($tierLevel);
        if (!$config) {
            return [
                'total_count' => 0,
                'level1_count' => 0,
                'level2_count' => 0,
                'level3_count' => 0
            ];
        }

        $totalCount = $config['total_purchaser_count'];
        return [
            'total_count' => $totalCount,
            'level1_count' => intval($totalCount * $config['level1_percent'] / 100),
            'level2_count' => intval($totalCount * $config['level2_percent'] / 100),
            'level3_count' => intval($totalCount * $config['level3_percent'] / 100)
        ];
    }
}
