{"name": "symfony/cache-contracts", "type": "library", "description": "Generic abstractions related to caching", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "homepage": "https://symfony.com", "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "require": {"php": ">=7.1.3", "psr/cache": "^1.0"}, "suggest": {"symfony/cache-implementation": ""}, "autoload": {"psr-4": {"Symfony\\Contracts\\Cache\\": ""}}, "minimum-stability": "dev", "extra": {"branch-alias": {"dev-master": "1.1-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}}