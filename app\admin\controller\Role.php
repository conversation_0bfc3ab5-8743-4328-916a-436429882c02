<?php



namespace app\admin\controller;


use app\admin\logic\RoleLogic;
use app\admin\validate\RoleValidate;
use app\common\basics\AdminBase;
use app\common\server\JsonServer;

class Role extends AdminBase
{
    /**
     * Notes: 列表
     * <AUTHOR> 10:34)
     * @return string|\think\response\Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function lists()
    {
        if ($this->request->isAjax()) {
            $get = $this->request->get();
            return JsonServer::success('', RoleLogic::lists($get));
        }
        return view();
    }


    /**
     * Notes: 添加
     * <AUTHOR> 10:34)
     * @return string|\think\response\Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function add()
    {
        if ($this->request->isAjax()) {
            $post = $this->request->post();
            (new RoleValidate())->goCheck('add');
            $result = RoleLogic::addRole($post);
            if ($result !== true) {
                return JsonServer::error(RoleLogic::getError() ?: '操作失败');
            }
            return JsonServer::success('操作成功');
        }
        return view('',[
            'auth_tree' => json_encode(RoleLogic::authTree(), true)
        ]);
    }


    /**
     * Notes: 编辑
     * @param string $role_id
     * <AUTHOR> 10:34)
     * @return string|\think\response\Json
     * @throws \think\Exception
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function edit($role_id = '')
    {
        if ($this->request->isAjax()) {
            $post = $this->request->post();
            (new RoleValidate())->goCheck('edit');
            $result = RoleLogic::editRole($post);
            if ($result !== true) {
                return JsonServer::error(RoleLogic::getError() ?: '操作失败');
            }
            return JsonServer::success('操作成功');
        }
        $auth_tree = RoleLogic::authTree($role_id);

        return view('', [
            'info' => RoleLogic::roleInfo($role_id),
            'auth_tree' => json_encode($auth_tree),
        ]);
    }

    /**
     * Notes: 删除
     * @param $role_id
     * <AUTHOR> 10:35)
     * @return \think\response\Json
     * @throws \think\Exception
     */
    public function del($id)
    {
        if ($this->request->isAjax()) {
            (new RoleValidate())->goCheck('del');
            RoleLogic::delRole($id);
            return JsonServer::success('删除成功');
        }
    }
}