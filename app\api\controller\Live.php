<?php
// +----------------------------------------------------------------------
// | LikeShop有特色的全开源社交分销电商系统
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | 商业用途务必购买系统授权，以免引起不必要的法律纠纷
// | 禁止对系统程序代码以任何目的，任何形式的再发布
// | 微信公众号：好象科技
// | 访问官网：http://www.likeshop.net
// | 访问社区：http://bbs.likeshop.net
// | 访问手册：http://doc.likeshop.net
// | 好象科技开发团队 版权所有 拥有最终解释权
// +----------------------------------------------------------------------
// | Author: LikeShopTeam
// +----------------------------------------------------------------------

namespace app\api\controller;

use app\api\logic\LiveLogic;
use app\common\basics\Api;
use app\common\server\JsonServer;


/**
 * 直播
 * Class Live
 * @package app\api\controller
 */
class Live extends Api
{

    public $like_not_need_login = ['lists', 'shopLive'];


    /**
     * @notes 直播列表
     * @return \think\response\Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date 2023/2/17 17:28
     */
    public function lists()
    {
        $data = LiveLogic::lists($this->page_no, $this->page_size);
        return JsonServer::success('', $data);
    }


    /**
     * @notes 商家直播间
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2023/2/17 17:28
     */
    public function shopLive()
    {
        $shopId = $this->request->get('shop_id');
        $data = LiveLogic::shopLive($shopId);
        return JsonServer::success('', $data);
    }

}