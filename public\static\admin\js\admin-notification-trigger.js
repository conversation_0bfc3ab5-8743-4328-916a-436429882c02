/**
 * 管理员通知触发器 - 公共方法
 * 基于WebSocket实现的后台通知系统
 * 
 * 使用方法：
 * 1. 引入此文件到页面
 * 2. 调用 AdminNotificationTrigger.sendNotification() 发送通知
 * 3. 调用 AdminNotificationTrigger.init() 初始化连接（可选）
 */

window.AdminNotificationTrigger = (function () {
    'use strict';

    // 配置参数
    const CONFIG = {
        // WebSocket服务器地址
        wsUrl: typeof webSocketUrl !== 'undefined' ? webSocketUrl :
            (window.location.protocol === 'https:' ? 'wss://' : 'ws://') + 'kefu.huohanghang.cn',

        // 音频文件配置
        audioFiles: {
            system: '/static/audio/tomsg.mp3',
            error: '/static/audio/tomsg.mp3',
            warning: '/static/audio/tomsg.mp3',
            info: '/static/audio/tomsg.mp3',
            success: '/static/audio/tomsg.mp3',
            default: '/static/audio/tomsg.mp3'
        },
        // audioFiles: {
        //     system: '/static/audio/system_notification.mp3',
        //     error: '/static/audio/error_notification.mp3',
        //     warning: '/static/audio/warning_notification.mp3',
        //     info: '/static/audio/info_notification.mp3',
        //     success: '/static/audio/success_notification.mp3',
        //     default: '/static/audio/default_notification.mp3'
        // },

        // 默认管理员信息
        defaultAdminId: 1,
        defaultNickname: '系统管理员',
        defaultToken: 'system_token',

        // 连接配置
        reconnectInterval: 5000, // 重连间隔（毫秒）
        maxReconnectAttempts: 5, // 最大重连次数
        heartbeatInterval: 15000, // 心跳间隔（毫秒）

        // 调试模式
        debug: true
    };

    // 私有变量
    let socket = null;
    let reconnectAttempts = 0;
    let heartbeatTimer = null;
    let isConnecting = false;

    /**
     * 日志输出
     * @param {string} message 消息内容
     * @param {string} type 日志类型：info, warn, error
     */
    function log(message, type = 'info') {
        if (!CONFIG.debug) return;

        const timestamp = new Date().toLocaleTimeString();
        const prefix = '[AdminNotificationTrigger]';

        switch (type) {
            case 'error':
                console.error(`${prefix} [${timestamp}] ${message}`);
                break;
            case 'warn':
                console.warn(`${prefix} [${timestamp}] ${message}`);
                break;
            default:
                console.log(`${prefix} [${timestamp}] ${message}`);
        }
    }

    /**
     * 构建WebSocket连接URL
     * @param {Object} options 连接参数
     * @returns {string} 完整的WebSocket URL
     */
    function buildWebSocketUrl(options = {}) {
        const adminId = options.adminId || CONFIG.defaultAdminId;
        const nickname = options.nickname || CONFIG.defaultNickname;
        const token = options.token || CONFIG.defaultToken;

        const params = new URLSearchParams({
            admin_id: adminId,
            nickname: nickname,
            token: token,
            type: 'admin',
            client: '5',
            t: Date.now()
        });

        return `${CONFIG.wsUrl}?${params.toString()}`;
    }

    /**
     * 启动心跳
     */
    function startHeartbeat() {
        if (heartbeatTimer) {
            clearInterval(heartbeatTimer);
        }

        heartbeatTimer = setInterval(() => {
            if (socket && socket.readyState === WebSocket.OPEN) {
                const pingData = {
                    event: 'ping',
                    data: {
                        timestamp: Date.now()
                    }
                };
                socket.send(JSON.stringify(pingData));
                log('发送心跳包');
            }
        }, CONFIG.heartbeatInterval);
    }

    /**
     * 停止心跳
     */
    function stopHeartbeat() {
        if (heartbeatTimer) {
            clearInterval(heartbeatTimer);
            heartbeatTimer = null;
        }
    }

    /**
     * 连接WebSocket
     * @param {Object} options 连接选项
     * @returns {Promise} 连接Promise
     */
    function connect(options = {}) {
        return new Promise((resolve, reject) => {
            if (socket && socket.readyState === WebSocket.OPEN) {
                log('WebSocket已连接');
                resolve(socket);
                return;
            }

            if (isConnecting) {
                log('正在连接中，请稍候...');
                reject(new Error('正在连接中'));
                return;
            }

            isConnecting = true;
            const url = buildWebSocketUrl(options);

            log(`正在连接WebSocket: ${url}`);

            try {
                socket = new WebSocket(url);

                socket.onopen = (event) => {
                    log('WebSocket连接成功');
                    isConnecting = false;
                    reconnectAttempts = 0;
                    startHeartbeat();
                    resolve(socket);
                };

                socket.onmessage = (event) => {
                    try {
                        const data = JSON.parse(event.data);
                        log(`收到消息: ${JSON.stringify(data)}`);

                        // 处理特定消息类型
                        if (data.event === 'pong') {
                            log('收到心跳响应');
                        } else if (data.event === 'login') {
                            log('登录成功: ' + (data.data?.msg || ''));
                        }
                    } catch (e) {
                        log(`收到非JSON消息: ${event.data}`);
                    }
                };

                socket.onclose = (event) => {
                    log(`WebSocket连接关闭: 代码=${event.code}, 原因=${event.reason || 'N/A'}`);
                    isConnecting = false;
                    stopHeartbeat();
                    socket = null;

                    // 自动重连
                    if (reconnectAttempts < CONFIG.maxReconnectAttempts) {
                        reconnectAttempts++;
                        log(`准备重连 (${reconnectAttempts}/${CONFIG.maxReconnectAttempts})`);
                        setTimeout(() => {
                            connect(options).catch(err => {
                                log(`重连失败: ${err.message}`, 'error');
                            });
                        }, CONFIG.reconnectInterval);
                    } else {
                        log('达到最大重连次数，停止重连', 'warn');
                    }
                };

                socket.onerror = (error) => {
                    log(`WebSocket错误: ${error.message || 'Unknown error'}`, 'error');
                    isConnecting = false;
                    reject(error);
                };

            } catch (e) {
                log(`创建WebSocket失败: ${e.message}`, 'error');
                isConnecting = false;
                reject(e);
            }
        });
    }

    /**
     * 断开WebSocket连接
     */
    function disconnect() {
        if (socket) {
            stopHeartbeat();
            socket.close();
            socket = null;
            log('WebSocket连接已断开');
        }
    }

    /**
     * 播放通知音效
     * @param {string} type 通知类型
     */
    function playNotificationSound(type = 'default') {
        if (CONFIG.muteSound) {
            log('音效已静音');
            return;
        }

        try {
            // 根据通知类型选择音频文件
            let audioFile = CONFIG.audioFiles[type] || CONFIG.audioFiles.default;

            // 如果没有配置音频文件，使用默认的
            if (!audioFile) {
                audioFile = '/static/audio/notification.mp3';
            }

            const audio = new Audio(audioFile);
            audio.volume = CONFIG.soundVolume || 0.7;

            const playPromise = audio.play();

            if (playPromise !== undefined) {
                playPromise.then(() => {
                    log(`播放音效成功: ${type}`);
                }).catch(error => {
                    log(`播放音效失败: ${error.message}`, 'warn');
                    // 浏览器可能阻止自动播放，这是正常的
                });
            }
        } catch (error) {
            log(`音效播放异常: ${error.message}`, 'warn');
        }
    }

    /**
     * 发送消息到WebSocket
     * @param {Object} data 消息数据
     * @returns {Promise} 发送Promise
     */
    function sendMessage(data) {
        return new Promise((resolve, reject) => {
            if (!socket || socket.readyState !== WebSocket.OPEN) {
                reject(new Error('WebSocket未连接'));
                return;
            }

            try {
                const message = JSON.stringify(data);
                socket.send(message);
                log(`发送消息: ${message}`);

                // 播放发送音效（可选）
                if (data.event === 'admin_notification' && data.data) {
                    const notificationType = data.data.type || 'default';
                    const soundType = notificationType.replace('_notification', '');
                    playNotificationSound(soundType);
                }

                resolve(true);
            } catch (e) {
                log(`发送消息失败: ${e.message}`, 'error');
                reject(e);
            }
        })

        // 公共API
        return {
            /**
             * 初始化通知触发器
             * @param {Object} options 初始化选项
             * @returns {Promise} 初始化Promise
             */
            init: function (options = {}) {
                // 合并配置
                Object.assign(CONFIG, options);

                log('初始化管理员通知触发器');
                return connect(options);
            },

            /**
             * 发送通知给所有管理员
             * @param {Object} notification 通知内容
             * @returns {Promise} 发送Promise
             */
            sendNotification: function (notification) {
                const defaultNotification = {
                    type: 'admin_notification',
                    title: '系统通知',
                    content: '您有一条新消息',
                    url: '',
                    icon: 0,
                    timestamp: Date.now()
                };

                // 合并通知参数
                const finalNotification = Object.assign({}, defaultNotification, notification);

                log(`准备发送通知: ${JSON.stringify(finalNotification)}`);

                // 构建WebSocket消息
                const message = {
                    event: 'admin_notification',
                    data: finalNotification
                };

                // 如果WebSocket未连接，先连接
                if (!socket || socket.readyState !== WebSocket.OPEN) {
                    log('WebSocket未连接，正在建立连接...');
                    return connect().then(() => {
                        return sendMessage(message);
                    });
                }

                return sendMessage(message);
            },

            /**
             * 发送系统通知
             * @param {string} title 通知标题
             * @param {string} content 通知内容
             * @param {string} url 跳转链接（可选）
             * @returns {Promise} 发送Promise
             */
            sendSystemNotification: function (title, content, url = '') {
                return this.sendNotification({
                    type: 'system_notification',
                    title: title,
                    content: content,
                    url: url,
                    icon: 1
                });
            },

            /**
             * 发送错误通知
             * @param {string} title 通知标题
             * @param {string} content 通知内容
             * @param {string} url 跳转链接（可选）
             * @returns {Promise} 发送Promise
             */
            sendErrorNotification: function (title, content, url = '') {
                return this.sendNotification({
                    type: 'error_notification',
                    title: title,
                    content: content,
                    url: url,
                    icon: 2
                });
            },

            /**
             * 发送警告通知
             * @param {string} title 通知标题
             * @param {string} content 通知内容
             * @param {string} url 跳转链接（可选）
             * @returns {Promise} 发送Promise
             */
            sendWarningNotification: function (title, content, url = '') {
                return this.sendNotification({
                    type: 'warning_notification',
                    title: title,
                    content: content,
                    url: url,
                    icon: 3
                });
            },

            /**
             * 发送信息通知
             * @param {string} title 通知标题
             * @param {string} content 通知内容
             * @param {string} url 跳转链接（可选）
             * @returns {Promise} 发送Promise
             */
            sendInfoNotification: function (title, content, url = '') {
                return this.sendNotification({
                    type: 'info_notification',
                    title: title,
                    content: content,
                    url: url,
                    icon: 4
                });
            },

            /**
             * 获取连接状态
             * @returns {string} 连接状态
             */
            getConnectionStatus: function () {
                if (!socket) return 'disconnected';

                switch (socket.readyState) {
                    case WebSocket.CONNECTING:
                        return 'connecting';
                    case WebSocket.OPEN:
                        return 'connected';
                    case WebSocket.CLOSING:
                        return 'closing';
                    case WebSocket.CLOSED:
                        return 'closed';
                    default:
                        return 'unknown';
                }
            },

            /**
             * 手动断开连接
             */
            disconnect: disconnect,

            /**
             * 手动重连
             * @param {Object} options 连接选项
             * @returns {Promise} 连接Promise
             */
            reconnect: function (options = {}) {
                disconnect();
                reconnectAttempts = 0;
                return connect(options);
            },

            /**
             * 设置调试模式
             * @param {boolean} enabled 是否启用调试
             */
            setDebug: function (enabled) {
                CONFIG.debug = !!enabled;
            }
        };
    }) ();

    // 自动初始化（如果页面已加载完成）
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function () {
            // 延迟初始化，给其他脚本时间设置必要的变量
            setTimeout(() => {
                AdminNotificationTrigger.init().catch(err => {
                    console.warn('AdminNotificationTrigger自动初始化失败:', err.message);
                });
            }, 1000);
        });
    } else {
        // 页面已加载完成，延迟初始化
        setTimeout(() => {
            AdminNotificationTrigger.init().catch(err => {
                console.warn('AdminNotificationTrigger自动初始化失败:', err.message);
            });
        }, 1000);
    }