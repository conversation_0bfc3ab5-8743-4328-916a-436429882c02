<?php
namespace app\admin\logic\shop;

use app\common\basics\Logic;
use app\common\model\Kefu;
use app\common\model\Shop;
use app\common\model\ShopAdmin;
use think\facade\Db;

/**
 * 客服批量操作逻辑层
 * Class CustomerServiceBatchLogic
 * @package app\admin\logic\shop
 */
class CustomerServiceBatchLogic extends Logic
{
    /**
     * 获取表格数据（符合like.tableLists格式）
     * @param array $get 请求参数
     * @return array
     */
    public static function getListsForTable($get = [])
    {
        $where = [];
        
        // 搜索条件
        if (!empty($get['shop_name'])) {
            $where[] = ['s.name', 'like', '%' . $get['shop_name'] . '%'];
        }
        
        if (!empty($get['kefu_name'])) {
            $where[] = ['k.nickname', 'like', '%' . $get['kefu_name'] . '%'];
        }
        
        if (isset($get['status']) && $get['status'] !== '') {
            $where[] = ['k.disable', '=', $get['status']];
        }
        
        $list = Db::name('kefu')
            ->alias('k')
            ->leftJoin('ls_shop s', 's.id = k.shop_id')
            ->leftJoin('ls_shop_admin sa', 'sa.id = k.admin_id')
            ->where($where)
            ->where('k.del', 0)
            ->where('s.del', 0)
            ->field('k.*, s.name as shop_name, s.tier_level, sa.account as admin_account')
            ->order('k.create_time desc')
            ->select()
            ->toArray();
        
        // 处理数据
        foreach ($list as &$item) {
            $item['status_text'] = $item['disable'] == 0 ? '启用' : '禁用';
            $item['tier_level_text'] = self::getTierLevelName($item['tier_level']);
            $item['create_time_text'] = date('Y-m-d H:i:s', $item['create_time']);
        }
        
        return [
            'count' => count($list),
            'lists' => $list
        ];
    }

    /**
     * 批量分配客服
     * @param array $data
     * @return bool
     */
    public static function batchAssignKefu($data)
    {
        try {
            $shopIds = $data['shop_ids'];
            $kefuIds = $data['kefu_ids'];
            
            Db::startTrans();
            
            foreach ($shopIds as $shopId) {
                foreach ($kefuIds as $kefuId) {
                    // 检查客服是否已经分配给该商家
                    $exists = Db::name('kefu')
                        ->where('id', $kefuId)
                        ->where('shop_id', $shopId)
                        ->where('del', 0)
                        ->find();

                    if (!$exists) {
                        // 复制客服到新商家
                        $kefuInfo = Db::name('kefu')->where('id', $kefuId)->find();
                        if ($kefuInfo) {
                            unset($kefuInfo['id']);
                            $kefuInfo['shop_id'] = $shopId;
                            $kefuInfo['create_time'] = time();
                            $kefuInfo['update_time'] = time();

                            Db::name('kefu')->insert($kefuInfo);
                        }
                    }
                }
            }
            
            Db::commit();
            return true;
            
        } catch (\Exception $e) {
            Db::rollback();
            self::$error = $e->getMessage();
            return false;
        }
    }

    /**
     * 批量移除客服
     * @param array $data
     * @return bool
     */
    public static function batchRemoveKefu($data)
    {
        try {
            $shopIds = $data['shop_ids'];
            $kefuIds = $data['kefu_ids'];
            
            Db::startTrans();
            
            foreach ($shopIds as $shopId) {
                foreach ($kefuIds as $kefuId) {
                    // 检查是否是该商家的最后一个客服
                    $kefuCount = Db::name('kefu')
                        ->where('shop_id', $shopId)
                        ->where('del', 0)
                        ->count();
                    
                    if ($kefuCount <= 1) {
                        self::$error = "商家 {$shopId} 至少需要保留一个客服";
                        Db::rollback();
                        return false;
                    }
                    
                    // 软删除客服
                    Db::name('kefu')
                        ->where('id', $kefuId)
                        ->where('shop_id', $shopId)
                        ->update(['del' => 1, 'update_time' => time()]);
                }
            }
            
            Db::commit();
            return true;
            
        } catch (\Exception $e) {
            Db::rollback();
            self::$error = $e->getMessage();
            return false;
        }
    }

    /**
     * 批量设置客服状态
     * @param array $data
     * @return bool
     */
    public static function batchSetKefuStatus($data)
    {
        try {
            $kefuIds = $data['kefu_ids'];
            $status = $data['status'];
            
            Db::startTrans();
            
            foreach ($kefuIds as $kefuId) {
                if ($status == 1) { // 禁用
                    // 检查是否是商家的最后一个启用客服
                    $kefu = Db::name('kefu')->where('id', $kefuId)->find();
                    if ($kefu) {
                        $enabledCount = Db::name('kefu')
                            ->where('shop_id', $kefu['shop_id'])
                            ->where('disable', 0)
                            ->where('del', 0)
                            ->count();
                        
                        if ($enabledCount <= 1) {
                            self::$error = "商家至少需要保留一个启用的客服";
                            Db::rollback();
                            return false;
                        }
                    }
                }
                
                Db::name('kefu')
                    ->where('id', $kefuId)
                    ->update(['disable' => $status, 'update_time' => time()]);
            }
            
            Db::commit();
            return true;
            
        } catch (\Exception $e) {
            Db::rollback();
            self::$error = $e->getMessage();
            return false;
        }
    }

    /**
     * 获取商家选项
     * @return array
     */
    public static function getShopOptions()
    {
        return Db::name('shop')
            ->where('del', 0)
            ->field('id, name, tier_level')
            ->order('create_time desc')
            ->select()
            ->toArray();
    }

    /**
     * 获取客服选项
     * @return array
     */
    public static function getKefuOptions()
    {
        return Db::name('kefu')
            ->alias('k')
            ->leftJoin('ls_shop s', 's.id = k.shop_id')
            ->leftJoin('ls_shop_admin sa', 'sa.id = k.admin_id')
            ->where('k.del', 0)
            ->where('s.del', 0)
            ->field('k.id, k.nickname, sa.account, s.name as shop_name')
            ->order('k.create_time desc')
            ->select()
            ->toArray();
    }

    /**
     * 获取商家客服列表
     * @param int $shopId
     * @return array
     */
    public static function getShopKefuList($shopId)
    {
        return Db::name('kefu')
            ->where('shop_id', $shopId)
            ->where('del', 0)
            ->field('id, nickname, account, disable')
            ->order('create_time desc')
            ->select()
            ->toArray();
    }

    /**
     * 导出客服数据
     * @param array $get
     * @return array|false
     */
    public static function exportKefuData($get)
    {
        try {
            $data = self::getListsForTable($get);
            
            // 这里可以实现具体的导出逻辑
            // 比如生成Excel文件等
            
            return [
                'file_url' => '', // 导出文件的URL
                'file_name' => '客服数据_' . date('YmdHis') . '.xlsx'
            ];
            
        } catch (\Exception $e) {
            self::$error = $e->getMessage();
            return false;
        }
    }

    /**
     * 获取客服统计信息
     * @return array
     */
    public static function getKefuStats()
    {
        $totalKefu = Db::name('kefu')->where('del', 0)->count();
        $enabledKefu = Db::name('kefu')->where('del', 0)->where('disable', 0)->count();
        $disabledKefu = Db::name('kefu')->where('del', 0)->where('disable', 1)->count();
        
        $shopWithKefu = Db::name('kefu')
            ->where('del', 0)
            ->group('shop_id')
            ->count();
        
        $totalShop = Db::name('shop')->where('del', 0)->count();
        
        return [
            'total_kefu' => $totalKefu,
            'enabled_kefu' => $enabledKefu,
            'disabled_kefu' => $disabledKefu,
            'shop_with_kefu' => $shopWithKefu,
            'total_shop' => $totalShop,
            'coverage_rate' => $totalShop > 0 ? round(($shopWithKefu / $totalShop) * 100, 2) : 0
        ];
    }

    /**
     * 获取客服详情
     * @param int $id
     * @return array|null
     */
    public static function getKefuDetail($id)
    {
        $info = Db::name('kefu')
            ->alias('k')
            ->leftJoin('ls_shop s', 's.id = k.shop_id')
            ->leftJoin('ls_shop_admin sa', 'sa.id = k.admin_id')
            ->where('k.id', $id)
            ->where('k.del', 0)
            ->field('k.*, s.name as shop_name, s.tier_level, sa.account as admin_account')
            ->find();

        if ($info && $info['avatar']) {
            $info['avatar'] = \app\common\server\UrlServer::getFileUrl($info['avatar']);
        }

        return $info;
    }

    /**
     * 编辑客服
     * @param array $data
     * @return bool
     */
    public static function editKefu($data)
    {
        try {
            $updateData = [
                'nickname' => $data['nickname'],
                'account' => $data['account'],
                'disable' => $data['disable'],
                'sort' => $data['sort'] ?? 0,
                'update_time' => time()
            ];

            if (isset($data['avatar'])) {
                $updateData['avatar'] = $data['avatar'];
            }

            $result = Db::name('kefu')
                ->where('id', $data['id'])
                ->update($updateData);

            return $result !== false;

        } catch (\Exception $e) {
            self::$error = $e->getMessage();
            return false;
        }
    }

    /**
     * 获取商家等级名称
     * @param int $tierLevel
     * @return string
     */
    private static function getTierLevelName($tierLevel)
    {
        $names = [
            0 => '0元入驻',
            1 => '商家会员',
            2 => '实力厂商'
        ];

        return $names[$tierLevel] ?? '未知等级';
    }
}
