<?php


namespace app\shopapi\controller;

use app\common\basics\ShopApi;
use app\common\server\ConfigServer;
use app\common\server\JsonServer;

class Config extends ShopApi
{
    public $like_not_need_login = ["getPolicyAgreement"];

    public function getPolicyAgreement()
    {
        $config =  [
            1 => ConfigServer::get('policy', 'service',''),
            2 => ConfigServer::get('policy', 'privacy', ''),
            3 => ConfigServer::get('policy', 'after_sale', ''),
        ];

        return JsonServer::success('', $config);
    }
}