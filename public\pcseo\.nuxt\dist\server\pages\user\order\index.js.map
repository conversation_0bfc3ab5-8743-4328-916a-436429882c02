{"version": 3, "file": "pages/user/order/index.js", "sources": ["webpack:///./components/price-formate.vue?b158", "webpack:///./components/price-formate.vue?4c8e", "webpack:///./components/price-formate.vue", "webpack:///./components/price-formate.vue?3e0e", "webpack:///./components/price-formate.vue?2b64", "webpack:///./components/price-formate.vue?8e8e", "webpack:///./components/price-formate.vue?32b6", "webpack:///./components/null-data.vue?48f8", "webpack:///./components/null-data.vue?97fe", "webpack:///./components/null-data.vue?fba4", "webpack:///./components/null-data.vue?cbf9", "webpack:///./components/null-data.vue", "webpack:///./components/null-data.vue?da63", "webpack:///./components/null-data.vue?475d", "webpack:///./components/count-down.vue?4f61", "webpack:///./utils/parseTime.js", "webpack:///./components/count-down.vue", "webpack:///./components/count-down.vue?a8c1", "webpack:///./components/count-down.vue?1b2a", "webpack:///./components/deliver-search.vue?9a2d", "webpack:///./components/deliver-search.vue?f82a", "webpack:///./components/deliver-search.vue?a04e", "webpack:///./components/deliver-search.vue?6097", "webpack:///./components/deliver-search.vue", "webpack:///./components/deliver-search.vue?b76a", "webpack:///./components/deliver-search.vue?bc14", "webpack:///./components/order-list.vue?b218", "webpack:///./static/images/order_null.png", "webpack:///./components/order-list.vue?1855", "webpack:///./components/order-list.vue?fdfd", "webpack:///./pages/user/order/index.vue?e5df", "webpack:///./components/order-list.vue?fad9", "webpack:///./components/order-list.vue", "webpack:///./components/order-list.vue?1db2", "webpack:///./components/order-list.vue?3346", "webpack:///./pages/user/order/index.vue?77d1", "webpack:///./pages/user/order/index.vue?44a4", "webpack:///./pages/user/order/index.vue?a42e", "webpack:///./pages/user/order/index.vue", "webpack:///./pages/user/order/index.vue?2ca2", "webpack:///./pages/user/order/index.vue?8a3f"], "sourcesContent": ["// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../node_modules/css-loader/dist/cjs.js??ref--3-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--3-oneOf-1-2!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./price-formate.vue?vue&type=style&index=0&lang=css&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"3181fc86\", content, true, context)\n};", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('span',{class:(_vm.lineThrough ? 'line-through' : '') + 'price-format',style:({ color: _vm.color, 'font-weight': _vm.weight })},[_vm._ssrNode(((_vm.showSubscript)?(\"<span\"+(_vm._ssrStyle(null,{\n            'font-size': _vm.subscriptSize + 'px',\n            'margin-right': '1px',\n        }, null))+\">¥</span>\"):\"<!---->\")+\" <span\"+(_vm._ssrStyle(null,{ 'font-size': _vm.firstSize + 'px', 'margin-right': '1px' }, null))+\">\"+_vm._ssrEscape(_vm._s(_vm.priceSlice.first))+\"</span> \"+((_vm.priceSlice.second)?(\"<span\"+(_vm._ssrStyle(null,{ 'font-size': _vm.secondSize + 'px' }, null))+\">\"+_vm._ssrEscape(\".\"+_vm._s(_vm.priceSlice.second))+\"</span>\"):\"<!---->\"))])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nexport default {\n    data() {\n        return {\n            priceSlice: {},\n        }\n    },\n    components: {},\n    props: {\n        firstSize: {\n            type: Number,\n            default: 14,\n        },\n        secondSize: {\n            type: Number,\n            default: 14,\n        },\n        color: {\n            type: String,\n        },\n        weight: {\n            type: [String, Number],\n            default: 400,\n        },\n        price: {\n            type: [String, Number],\n            default: '',\n        },\n        showSubscript: {\n            type: Boolean,\n            default: true,\n        },\n        subscriptSize: {\n            type: Number,\n            default: 14,\n        },\n        lineThrough: {\n            type: Boolean,\n            default: false,\n        },\n    },\n    created() {\n        this.priceFormat()\n    },\n    watch: {\n        price(val) {\n            this.priceFormat()\n        },\n    },\n    methods: {\n        priceFormat() {\n            let { price } = this\n            let priceSlice = {}\n            if (price !== null) {\n                price = parseFloat(price)\n                price = String(price).split('.')\n                priceSlice.first = price[0]\n                priceSlice.second = price[1]\n                this.priceSlice = priceSlice\n            }\n        },\n    },\n}\n", "import mod from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./price-formate.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./price-formate.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./price-formate.vue?vue&type=template&id=0c4d5c85&\"\nimport script from \"./price-formate.vue?vue&type=script&lang=js&\"\nexport * from \"./price-formate.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./price-formate.vue?vue&type=style&index=0&lang=css&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  null,\n  \"7ae24710\"\n  \n)\n\nexport default component.exports", "export * from \"-!../node_modules/vue-style-loader/index.js??ref--3-oneOf-1-0!../node_modules/css-loader/dist/cjs.js??ref--3-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--3-oneOf-1-2!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./price-formate.vue?vue&type=style&index=0&lang=css&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".price-format{display:flex;align-items:baseline}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/sass-resources-loader/lib/loader.js??ref--7-oneOf-1-4!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./null-data.vue?vue&type=style&index=0&id=93598fb0&lang=scss&scoped=true&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"12a18d22\", content, true, context)\n};", "export * from \"-!../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/sass-resources-loader/lib/loader.js??ref--7-oneOf-1-4!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./null-data.vue?vue&type=style&index=0&id=93598fb0&lang=scss&scoped=true&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".null-data[data-v-93598fb0]{padding:100px}.null-data .img-null[data-v-93598fb0]{width:150px;height:150px}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"bg-white flex-col col-center null-data\"},[_vm._ssrNode(\"<img\"+(_vm._ssrAttr(\"src\",_vm.img))+\" alt class=\\\"img-null\\\"\"+(_vm._ssrStyle(null,_vm.imgStyle, null))+\" data-v-93598fb0> <div class=\\\"muted mt8\\\" data-v-93598fb0>\"+_vm._ssrEscape(_vm._s(_vm.text))+\"</div>\")])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n\nexport default {\n    components: {},\n    props: {\n        img: {\n            type: String,\n        },\n        text: {\n            type: String,\n            default: '暂无数据',\n        },\n        imgStyle: {\n            type: String,\n            default: '',\n        },\n    },\n    methods: {},\n}\n", "import mod from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./null-data.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./null-data.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./null-data.vue?vue&type=template&id=93598fb0&scoped=true&\"\nimport script from \"./null-data.vue?vue&type=script&lang=js&\"\nexport * from \"./null-data.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./null-data.vue?vue&type=style&index=0&id=93598fb0&lang=scss&scoped=true&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  \"93598fb0\",\n  \"728f99de\"\n  \n)\n\nexport default component.exports", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return (_vm.time >= 0)?_c('div',[_c('client-only',[(_vm.isSlot)?_vm._t(\"default\"):_c('span',[_vm._v(_vm._s(_vm.formateTime))])],2)],1):_vm._e()}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "\n\nconst SECOND = 1000;\nconst MINUTE = 60 * SECOND;\nconst HOUR = 60 * MINUTE;\nconst DAY = 24 * HOUR;\nexport function parseTimeData(time) {\n    const days = Math.floor(time / DAY);\n    const hours = sliceTwo(Math.floor((time % DAY) / HOUR));\n    const minutes = sliceTwo(Math.floor((time % HOUR) / MINUTE));\n    const seconds = sliceTwo(Math.floor((time % MINUTE) / SECOND));\n    return {\n        days: days,\n        hours: hours,\n        minutes: minutes,\n        seconds: seconds,\n    };\n}\n\nfunction sliceTwo(str) {\n    return (0 + str.toString()).slice(-2)\n}\n\nexport  function parseFormat(format, timeData) {\n    let days = timeData.days;\n    let hours = timeData.hours, minutes = timeData.minutes, seconds = timeData.seconds\n    if (format.indexOf('dd') !== -1) {\n        format = format.replace('dd', days);\n    }\n    if (format.indexOf('hh') !== -1) {\n        format = format.replace('hh', sliceTwo(hours) );\n    }\n    if (format.indexOf('mm') !== -1) {\n        format = format.replace('mm', sliceTwo(minutes));\n    }\n    if (format.indexOf('ss') !== -1) {\n        format = format.replace('ss', sliceTwo(seconds));\n    }\n    return format\n}", "//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { parseTimeData, parseFormat } from '~/utils/parseTime'\nexport default {\n    components: {},\n    props: {\n        isSlot: {\n            type: Boolean,\n            default: false,\n        },\n        time: {\n            type: Number,\n            default: 0,\n        },\n        format: {\n            type: String,\n            default: 'hh:mm:ss',\n        },\n        autoStart: {\n            type: Boolean,\n            default: true,\n        },\n    },\n    watch: {\n        time: {\n            immediate: true,\n            handler(value) {\n                if (value) {\n                    this.reset()\n                }\n            },\n        },\n    },\n    data() {\n        return {\n            timeObj: {},\n            formateTime: 0,\n        }\n    },\n    created() {},\n    computed: {},\n    methods: {\n        createTimer(fn) {\n            return setTimeout(fn, 100)\n        },\n        isSameSecond(time1, time2) {\n            return Math.floor(time1) === Math.floor(time2)\n        },\n        start() {\n            if (this.counting) {\n                return\n            }\n            this.counting = true\n            this.endTime = Date.now() + this.remain * 1000\n            this.setTimer()\n        },\n        setTimer() {\n            this.tid = this.createTimer(() => {\n                let remain = this.getRemain()\n                if (!this.isSameSecond(remain, this.remain) || remain === 0) {\n                    this.setRemain(remain)\n                }\n                if (this.remain !== 0) {\n                    this.setTimer()\n                }\n            })\n        },\n        getRemain() {\n            return Math.max(this.endTime - Date.now(), 0)\n        },\n        pause() {\n            this.counting = false\n            clearTimeout(this.tid)\n        },\n        reset() {\n            this.pause()\n            this.remain = this.time\n            this.setRemain(this.remain)\n            if (this.autoStart) {\n                this.start()\n            }\n        },\n        setRemain(remain) {\n            const { format } = this\n            this.remain = remain\n            const timeData = parseTimeData(remain)\n            this.formateTime = parseFormat(format, timeData)\n            this.$emit('change', timeData)\n            if (remain === 0) {\n                this.pause()\n                this.$emit('finish')\n            }\n        },\n    },\n}\n", "import mod from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./count-down.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./count-down.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./count-down.vue?vue&type=template&id=2fbaab86&\"\nimport script from \"./count-down.vue?vue&type=script&lang=js&\"\nexport * from \"./count-down.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  \n}\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  null,\n  \"4090b4e2\"\n  \n)\n\nexport default component.exports", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/sass-resources-loader/lib/loader.js??ref--7-oneOf-1-4!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./deliver-search.vue?vue&type=style&index=0&id=79dec466&lang=scss&scoped=true&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"db2946c2\", content, true, context)\n};", "export * from \"-!../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/sass-resources-loader/lib/loader.js??ref--7-oneOf-1-4!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./deliver-search.vue?vue&type=style&index=0&id=79dec466&lang=scss&scoped=true&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".deliver-search-container .deliver-box .deliver-recode-box[data-v-79dec466]{padding:10px 20px;background-color:#f2f2f2}.deliver-search-container .deliver-box .deliver-recode-box .recode-img[data-v-79dec466]{position:relative;width:72px;height:72px}.deliver-search-container .deliver-box .deliver-recode-box .recode-img .float-count[data-v-79dec466]{position:absolute;bottom:0;height:20px;width:100%;background-color:rgba(0,0,0,.5);color:#fff;font-size:12px}.deliver-search-container .deliver-box .deliver-recode-box .recode-info-container[data-v-79dec466]{flex:1}.deliver-search-container .deliver-box .deliver-recode-box .recode-info-container .recode-label[data-v-79dec466]{width:70px}.deliver-search-container .deliver-box .deliver-recode-box .recode-info-container .copy-btn[data-v-79dec466]{height:20px;min-width:42px;border:1px solid #ff2c3c;font-size:12px;margin-left:8px;border-radius:60px;cursor:pointer}.deliver-search-container .deliver-box .deliver-recode-box .recode-info-container .copy-btn[data-v-79dec466]:hover{background-color:#fff}.deliver-search-container .deliver-box .deliver-flow-box[data-v-79dec466]{padding-left:15px}.deliver-search-container .deliver-box .time-line-title[data-v-79dec466]{font-weight:500px;font-size:16px;margin-bottom:10px}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"deliver-search-container\"},[_c('el-dialog',{attrs:{\"visible\":_vm.showDialog,\"top\":\"30vh\",\"width\":\"900px\",\"title\":\"物流查询\"},on:{\"update:visible\":function($event){_vm.showDialog=$event}}},[_c('div',{staticClass:\"deliver-box\"},[_c('div',{staticClass:\"deliver-recode-box flex\"},[_c('div',{staticClass:\"recode-img\"},[_c('el-image',{staticStyle:{\"width\":\"100%\",\"height\":\"100%\"},attrs:{\"fit\":\"cover\",\"src\":_vm.deliverOrder.image}}),_vm._v(\" \"),_c('div',{staticClass:\"float-count flex row-center\"},[_vm._v(\"共\"+_vm._s(_vm.deliverOrder.count)+\"件商品\")])],1),_vm._v(\" \"),_c('div',{staticClass:\"recode-info-container m-l-10\"},[_c('div',{staticClass:\"flex\"},[_c('div',{staticClass:\"recode-label\"},[_vm._v(\"物流状态：\")]),_vm._v(\" \"),_c('div',{staticClass:\"primary lg\",staticStyle:{\"font-weight\":\"500\"}},[_vm._v(_vm._s(_vm.deliverOrder.tips))])]),_vm._v(\" \"),_c('div',{staticClass:\"flex\",staticStyle:{\"margin\":\"6px 0\"}},[_c('div',{staticClass:\"recode-label\"},[_vm._v(\"快递公司：\")]),_vm._v(\" \"),_c('div',[_vm._v(_vm._s(_vm.deliverOrder.shipping_name))])]),_vm._v(\" \"),_c('div',{staticClass:\"flex\"},[_c('div',{staticClass:\"recode-label\"},[_vm._v(\"快递单号：\")]),_vm._v(\" \"),_c('div',[_vm._v(_vm._s(_vm.deliverOrder.invoice_no))]),_vm._v(\" \"),_c('div',{staticClass:\"copy-btn primary flex row-center\",on:{\"click\":_vm.onCopy}},[_vm._v(\"复制\")])])])]),_vm._v(\" \"),_c('div',{staticClass:\"deliver-flow-box m-t-16\"},[_c('el-timeline',[(_vm.deliverFinish.tips)?_c('el-timeline-item',[_c('div',[_c('div',{staticClass:\"flex lg\"},[_c('div',{staticClass:\"m-r-8\",staticStyle:{\"font-weight\":\"500\"}},[_vm._v(\"\\n                                    \"+_vm._s(_vm.deliverTake.contacts)+\"\\n                                \")]),_vm._v(\" \"),_c('div',{staticStyle:{\"font-weight\":\"500\"}},[_vm._v(_vm._s(_vm.deliverTake.mobile))])]),_vm._v(\" \"),_c('div',{staticClass:\"lighter m-t-8\"},[_vm._v(_vm._s(_vm.deliverTake.address))])])]):_vm._e(),_vm._v(\" \"),(_vm.deliverFinish.tips)?_c('el-timeline-item',{attrs:{\"timestamp\":_vm.deliverFinish.time}},[_c('div',{staticClass:\"time-line-title\"},[_vm._v(_vm._s(_vm.deliverFinish.title))]),_vm._v(\" \"),_c('div',[_vm._v(_vm._s(_vm.deliverFinish.tips))])]):_vm._e(),_vm._v(\" \"),(_vm.delivery.traces && _vm.delivery.traces.length)?_c('el-timeline-item',{attrs:{\"timestamp\":_vm.delivery.time}},[_c('div',{staticClass:\"time-line-title m-b-8\"},[_vm._v(_vm._s(_vm.delivery.title))]),_vm._v(\" \"),_vm._l((_vm.delivery.traces),function(item,index){return _c('el-timeline-item',{key:index,attrs:{\"timestamp\":item[0]}},[_c('div',{staticClass:\"muted\"},[_vm._v(_vm._s(item[1]))])])})],2):_vm._e(),_vm._v(\" \"),(_vm.deliverShipment.tips)?_c('el-timeline-item',{attrs:{\"timestamp\":_vm.deliverShipment.time}},[_c('div',{staticClass:\"time-line-title\"},[_vm._v(_vm._s(_vm.deliverShipment.title))]),_vm._v(\" \"),_c('div',[_vm._v(_vm._s(_vm.deliverShipment.tips))])]):_vm._e(),_vm._v(\" \"),(_vm.deliverBuy.tips)?_c('el-timeline-item',{attrs:{\"timestamp\":_vm.deliverBuy.time}},[_c('div',{staticClass:\"time-line-title\"},[_vm._v(_vm._s(_vm.deliverBuy.title))]),_vm._v(\" \"),_c('div',[_vm._v(_vm._s(_vm.deliverBuy.tips))])]):_vm._e()],1)],1)])])],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nexport default {\n    props: {\n        value: {\n            type: Boolean,\n            default: false,\n        },\n        aid: {\n            type: Number | String\n        }\n    },\n    data() {\n        return {\n            showDialog: false,\n            deliverBuy: {},\n            delivery: {},\n            deliverFinish: {},\n            deliverOrder: {},\n            deliverShipment: {},\n            deliverTake: {},\n            timeLineArray: []\n        }\n    },\n    watch: {\n        value(val) {\n            console.log(val, 'val')\n            this.showDialog = val;\n        },\n        showDialog(val) {\n            if(val) {\n                if(this.aid) {\n                    this.timeLineArray = []\n                    this.getDeliverTraces();\n                }\n            }\n            this.$emit(\"input\", val);\n        },\n    },\n    methods: {\n        async getDeliverTraces() {\n            let data = {\n                id: this.aid\n            }\n            let res = await this.$get(\"order/orderTraces\", {params: data});\n            if(res.code == 1) {\n                let {buy, delivery, finish, order, shipment, take} = res.data\n                this.deliverBuy = buy;\n                this.delivery = delivery;\n                this.deliverFinish = finish;\n                this.deliverOrder = order;\n                this.deliverShipment = shipment;\n                this.deliverTake = take;\n                this.timeLineArray.push(this.deliverFinish);\n                this.timeLineArray.push(this.delivery);\n                this.timeLineArray.push(this.deliverShipment);\n                this.timeLineArray.push(this.deliverBuy);\n                console.log(this.timeLineArray)\n            }\n        },\n        onCopy() {\n            // this.deliverOrder.invoice_no;\n            let oInput = document.createElement('input');\n            oInput.value = this.deliverOrder.invoice_no;\n            document.body.appendChild(oInput);\n            oInput.select();\n            document.execCommand(\"Copy\");\n            this.$message.success(\"复制成功\");\n            oInput.remove();\n        }\n    }\n}\n", "import mod from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./deliver-search.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./deliver-search.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./deliver-search.vue?vue&type=template&id=79dec466&scoped=true&\"\nimport script from \"./deliver-search.vue?vue&type=script&lang=js&\"\nexport * from \"./deliver-search.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./deliver-search.vue?vue&type=style&index=0&id=79dec466&lang=scss&scoped=true&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  \"79dec466\",\n  \"0d71d492\"\n  \n)\n\nexport default component.exports", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/sass-resources-loader/lib/loader.js??ref--7-oneOf-1-4!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./order-list.vue?vue&type=style&index=0&id=b84fcb6a&lang=scss&scoped=true&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"355a4d8a\", content, true, context)\n};", "module.exports = __webpack_public_path__ + \"img/order_null.ce12c76.png\";", "export * from \"-!../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/sass-resources-loader/lib/loader.js??ref--7-oneOf-1-4!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./order-list.vue?vue&type=style&index=0&id=b84fcb6a&lang=scss&scoped=true&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".order-list[data-v-b84fcb6a]{padding:0 16px}.order-list .item[data-v-b84fcb6a]{margin-bottom:20px}.order-list .item .item-hd[data-v-b84fcb6a]{height:40px;border:1px solid #e5e5e5;background:#f2f2f2;padding:0 20px}.order-list .item .item-hd .status[data-v-b84fcb6a]{width:100px;text-align:right}.order-list .item .item-con[data-v-b84fcb6a]{box-shadow:0 3px 4px rgba(0,0,0,.08);align-items:stretch}.order-list .item .item-con .goods[data-v-b84fcb6a]{padding:17px 0;width:560px}.order-list .item .item-con .goods .goods-item[data-v-b84fcb6a]{padding:10px 20px}.order-list .item .item-con .goods .goods-item .goods-img[data-v-b84fcb6a]{flex:none;margin-right:10px;width:72px;height:72px}.order-list .item .item-con .goods .goods-item .goods-name[data-v-b84fcb6a]{width:100%}.order-list .item .item-con .goods .goods-item .goods-name .num[data-v-b84fcb6a]{padding:0 42px}.order-list .item .item-con .pay-price[data-v-b84fcb6a]{width:200px;border-left:1px solid #e5e5e5;border-right:1px solid #e5e5e5}.order-list .item .item-con .operate[data-v-b84fcb6a]{width:185px}.order-list .item .item-con .operate>div[data-v-b84fcb6a]{cursor:pointer}.order-list .item .item-con .operate .btn[data-v-b84fcb6a]{width:120px;height:32px;border-radius:2px}.order-list .item .item-con .operate .btn.plain[data-v-b84fcb6a]{border:1px solid #ff2c3c}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../../../node_modules/sass-resources-loader/lib/loader.js??ref--7-oneOf-1-4!../../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=style&index=0&lang=scss&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../../../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"8a7e8b7a\", content, true, context)\n};", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"order-list\"},[_vm._l((_vm.list),function(item){return _vm._ssrNode(\"<div class=\\\"item m-b-16\\\" data-v-b84fcb6a>\",\"</div>\",[_vm._ssrNode(\"<div class=\\\"item-hd flex\\\" data-v-b84fcb6a>\",\"</div>\",[_c('nuxt-link',{staticClass:\"flex-1 lighter sm line-1 m-r-20\",staticStyle:{\"min-width\":\"0\"},attrs:{\"to\":(\"/shop_street_detail?id=\" + (item.shop.id))}},[_vm._v(\"\\n                \"+_vm._s(item.shop.name)+\"\\n            \")]),_vm._ssrNode(\" <div class=\\\"flex-1 lighter sm\\\" data-v-b84fcb6a>\"+_vm._ssrEscape(\"\\n                下单时间：\"+_vm._s(item.create_time)+\"\\n            \")+\"</div> <div class=\\\"flex-1 lighter sm\\\" data-v-b84fcb6a>\"+_vm._ssrEscape(\"\\n                订单编号：\"+_vm._s(item.order_sn)+\"\\n            \")+\"</div> <div\"+(_vm._ssrClass(null,['status sm', { primary: item.order_status == 0 }]))+\" data-v-b84fcb6a>\"+_vm._ssrEscape(\"\\n                \"+_vm._s(_vm.getOrderStatus(item.order_status))+\"\\n            \")+\"</div>\")],2),_vm._ssrNode(\" \"),_vm._ssrNode(\"<div class=\\\"item-con flex\\\" data-v-b84fcb6a>\",\"</div>\",[_vm._ssrNode(\"<div class=\\\"goods\\\" data-v-b84fcb6a>\",\"</div>\",_vm._l((item.order_goods),function(goods,index){return _c('nuxt-link',{key:index,staticClass:\"goods-item flex\",attrs:{\"to\":(\"/goods_details/\" + (goods.goods_id))}},[_c('el-image',{staticClass:\"goods-img\",attrs:{\"src\":goods.image,\"alt\":\"\"}}),_vm._v(\" \"),_c('div',{staticClass:\"goods-info flex-1\"},[_c('div',{staticClass:\"goods-name m-b-8 flex row-between\"},[_c('div',{staticClass:\"line1\",staticStyle:{\"width\":\"350px\"}},[(goods.is_seckill)?_c('el-tag',{attrs:{\"size\":\"mini\",\"effect\":\"plain\"}},[_vm._v(\"秒杀\")]):_vm._e(),_vm._v(\"\\n                                \"+_vm._s(goods.goods_name)+\"\\n                            \")],1),_vm._v(\" \"),_c('div',{staticClass:\"num\"},[_vm._v(\"x\"+_vm._s(goods.goods_num))])]),_vm._v(\" \"),_c('div',{staticClass:\"sm muted m-b-8\"},[_vm._v(\"\\n                            \"+_vm._s(goods.spec_value)+\"\\n                        \")]),_vm._v(\" \"),_c('div',{staticClass:\"primary\"},[_c('price-formate',{attrs:{\"price\":goods.goods_price}})],1)])],1)}),1),_vm._ssrNode(\" \"),_vm._ssrNode(\"<div class=\\\"pay-price flex-col col-center row-center\\\" style=\\\"padding-left: 30px\\\" data-v-b84fcb6a>\",\"</div>\",[_vm._ssrNode(\"<div class=\\\"lighter\\\" data-v-b84fcb6a>\"+_vm._ssrEscape(\"共\"+_vm._s(item.goods_count)+\"件商品\")+\"</div> \"),_vm._ssrNode(\"<div class=\\\"lighter m-t-8 flex\\\" data-v-b84fcb6a>\",\"</div>\",[_vm._ssrNode(\"\\n                    付款金额：\\n                    \"),_vm._ssrNode(\"<span class=\\\"primary\\\" data-v-b84fcb6a>\",\"</span>\",[_c('price-formate',{attrs:{\"price\":item.order_amount,\"subscript-size\":12,\"first-size\":16,\"second-size\":12}})],1)],2)],2),_vm._ssrNode(\" \"),_vm._ssrNode(\"<div class=\\\"operate flex-col col-center row-center sm\\\" data-v-b84fcb6a>\",\"</div>\",[(item.pay_btn)?_c('nuxt-link',{staticClass:\"btn m-b-16 bg-primary flex row-center white sm\",attrs:{\"to\":(\"/payment?id=\" + (item.id) + \"&from=order\")}},[_c('span',{staticClass:\"m-r-8\"},[_vm._v(\"去付款\")]),_vm._v(\" \"),(_vm.getCancelTime(item.order_cancel_time) > 0)?_c('count-down',{attrs:{\"time\":_vm.getCancelTime(item.order_cancel_time),\"format\":\"hh:mm:ss\"},on:{\"finish\":function($event){return _vm.$emit('refresh')}}}):_vm._e()],1):_vm._e(),_vm._ssrNode(\" \"+((item.take_btn)?(\"<div class=\\\"btn m-b-10 primary flex row-center sm plain\\\" data-v-b84fcb6a>\\n                    确认收货\\n                </div>\"):\"<!---->\")+\" \"+((item.delivery_btn)?(\"<div class=\\\"m-b-10 muted flex row-center sm\\\" data-v-b84fcb6a>\\n                    物流查询\\n                </div>\"):\"<!---->\")+\" \"+((item.cancel_btn)?(\"<div class=\\\"m-b-10 muted row-center sm\\\" data-v-b84fcb6a>\\n                    取消订单\\n                </div>\"):\"<!---->\")+\" \"+((item.del_btn)?(\"<div class=\\\"m-b-10 muted row-center sm\\\" data-v-b84fcb6a>\\n                    删除订单\\n                </div>\"):\"<!---->\")+\" \"),_c('nuxt-link',{staticClass:\"lighter\",attrs:{\"to\":(\"/user/order/detail?id=\" + (item.id))}},[_c('span',[_vm._v(\"查看详情\")])])],2)],2)],2)}),_vm._ssrNode(\" \"),_c('deliver-search',{attrs:{\"aid\":_vm.aid},model:{value:(_vm.showDeliver),callback:function ($$v) {_vm.showDeliver=$$v},expression:\"showDeliver\"}})],2)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nexport default {\n    props: {\n        list: {\n            type: Array,\n            default: () => [],\n        },\n    },\n    data() {\n        return {\n            showDeliver: false,\n            aid: -1,\n        }\n    },\n    created() {\n        console.log(this.list)\n    },\n    methods: {\n        handleOrder(type, id) {\n            this.type = type\n            this.orderId = id\n            this.$confirm(this.getTipsText(type), {\n                title: '温馨提示',\n                center: true,\n                confirmButtonText: '确定',\n                cancelButtonText: '取消',\n                callback: (action) => {\n                    if (action == 'confirm') {\n                        this.postOrder()\n                    }\n                },\n            })\n        },\n        async postOrder() {\n            const { type, orderId } = this\n            let url = ''\n            switch (type) {\n                case 0:\n                    url = 'order/cancel'\n                    break\n                case 1:\n                    url = 'order/del'\n                    break\n                case 2:\n                    url = 'order/confirm'\n                    break\n            }\n            let { code, data, msg } = await this.$post(url, { id: orderId })\n            if (code == 1) {\n                this.$message({\n                    message: msg,\n                    type: 'success',\n                })\n                this.$emit('refresh')\n            }\n        },\n        getTipsText(type) {\n            switch (type) {\n                case 0:\n                    return '确认取消订单吗？'\n                case 1:\n                    return '确认删除订单吗?'\n                case 2:\n                    return '确认收货吗?'\n            }\n        },\n        showDeliverDialog(id) {\n            console.log('showDeliverDialog')\n            this.aid = id\n            this.showDeliver = true\n        },\n    },\n    computed: {\n        getOrderStatus() {\n            return (status) => {\n                let text = ''\n                switch (status) {\n                    case 0:\n                        text = '待支付'\n                        break\n                    case 1:\n                        text = '待发货'\n                        break\n                    case 2:\n                        text = '待收货'\n                        break\n                    case 3:\n                        text = '已完成'\n                        break\n                    case 4:\n                        text = '订单已关闭'\n                        break\n                }\n                return text\n            }\n        },\n        getCancelTime() {\n            return (time) => time - Date.now() / 1000\n        },\n    },\n}\n", "import mod from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./order-list.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./order-list.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./order-list.vue?vue&type=template&id=b84fcb6a&scoped=true&\"\nimport script from \"./order-list.vue?vue&type=script&lang=js&\"\nexport * from \"./order-list.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./order-list.vue?vue&type=style&index=0&id=b84fcb6a&lang=scss&scoped=true&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  \"b84fcb6a\",\n  \"13897902\"\n  \n)\n\nexport default component.exports\n\n/* nuxt-component-imports */\ninstallComponents(component, {PriceFormate: require('/Users/<USER>/Desktop/vue/pc/components/price-formate.vue').default,CountDown: require('/Users/<USER>/Desktop/vue/pc/components/count-down.vue').default,DeliverSearch: require('/Users/<USER>/Desktop/vue/pc/components/deliver-search.vue').default})\n", "export * from \"-!../../../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../../../node_modules/sass-resources-loader/lib/loader.js??ref--7-oneOf-1-4!../../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=style&index=0&lang=scss&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../../../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".user-order{padding:20px 0}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"user-order\"},[_c('el-tabs',{on:{\"tab-click\":_vm.handleClick},model:{value:(_vm.activeName),callback:function ($$v) {_vm.activeName=$$v},expression:\"activeName\"}},_vm._l((_vm.order),function(item,index){return _c('el-tab-pane',{key:index,attrs:{\"label\":item.name,\"name\":item.type}},[(item.list.length)?[_c('order-list',{attrs:{\"list\":item.list},on:{\"refresh\":_vm.getOrderList}}),_vm._v(\" \"),(item.count)?_c('div',{staticClass:\"pagination flex row-center\"},[_c('el-pagination',{attrs:{\"hide-on-single-page\":\"\",\"background\":\"\",\"layout\":\"prev, pager, next\",\"total\":item.count,\"prev-text\":\"上一页\",\"next-text\":\"下一页\",\"page-size\":10},on:{\"current-change\":_vm.changePage}})],1):_vm._e()]:_c('null-data',{attrs:{\"img\":require('~/static/images/order_null.png'),\"text\":\"暂无订单~\"}})],2)}),1)],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nexport default {\n    head() {\n        return {\n            title: this.$store.getters.headTitle,\n            link: [\n                {\n                    rel: 'icon',\n                    type: 'image/x-icon',\n                    href: this.$store.getters.favicon,\n                },\n            ],\n        }\n    },\n    layout: 'user',\n    async asyncData({ $get, $post }) {\n        const {\n            data: { list, count },\n            code,\n        } = await $get('order/lists', {\n            params: {\n                page_size: 10,\n            },\n        })\n        if (code == 1) {\n            return {\n                orderList: { list, count },\n            }\n        }\n    },\n    components: {},\n    data() {\n        return {\n            activeName: 'all',\n            order: [\n                {\n                    type: 'all',\n                    list: [],\n                    name: '全部',\n                    count: 0,\n                    page: 1,\n                },\n                {\n                    type: 'pay',\n                    list: [],\n                    name: '待付款',\n                    count: 0,\n                    page: 1,\n                },\n                {\n                    type: 'delivery',\n                    list: [],\n                    name: '待收货',\n                    count: 0,\n                    page: 1,\n                },\n                {\n                    type: 'finish',\n                    list: [],\n                    name: '已完成',\n                    count: 0,\n                    page: 1,\n                },\n                {\n                    type: 'close',\n                    list: [],\n                    name: '已关闭',\n                    count: 0,\n                    page: 1,\n                },\n            ],\n        }\n    },\n    methods: {\n        handleClick() {\n            this.getOrderList()\n        },\n        async getOrderList() {\n            const { activeName, order } = this\n            const item = order.find((item) => item.type == activeName)\n            const {\n                data: { list, count },\n                code,\n            } = await this.$get('order/lists', {\n                params: {\n                    page_size: 10,\n                    page_no: item.page,\n                    type: activeName,\n                },\n            })\n            if (code == 1) {\n                this.orderList = { list, count }\n            }\n        },\n        changePage(val) {\n            this.order.some((item) => {\n                if (item.type == this.activeName) {\n                    item.page = val\n                }\n            })\n            this.getOrderList()\n        },\n    },\n    watch: {\n        orderList: {\n            immediate: true,\n            handler(val) {\n                this.order.some((item) => {\n                    if (item.type == this.activeName) {\n                        Object.assign(item, val)\n                        console.log(item)\n                        return true\n                    }\n                })\n            },\n        },\n    },\n}\n", "import mod from \"-!../../../node_modules/babel-loader/lib/index.js??ref--2-0!../../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/babel-loader/lib/index.js??ref--2-0!../../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./index.vue?vue&type=template&id=4f7657ba&\"\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./index.vue?vue&type=style&index=0&lang=scss&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  null,\n  \"441c529f\"\n  \n)\n\nexport default component.exports\n\n/* nuxt-component-imports */\ninstallComponents(component, {OrderList: require('/Users/<USER>/Desktop/vue/pc/components/order-list.vue').default,NullData: require('/Users/<USER>/Desktop/vue/pc/components/null-data.vue').default})\n"], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACXA;AACA;AACA;AACA;AACA;AACA;;;;;ACLA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AADA;AAGA;AACA;AAAA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AADA;AAGA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AA5BA;AACA;AAgCA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAJA;AAKA;AACA;AACA;AAAA;AAAA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAZA;AAhDA;;AC1BA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACvBA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACNA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACXA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACNA;AACA;AACA;;;;;ACFA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AADA;AAGA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AARA;AAaA;AAfA;;ACRA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACvBA;AACA;AACA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAJA;AAMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;;ACvCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAbA;AAkBA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAPA;AADA;AACA;AASA;AACA;AACA;AACA;AAFA;AAIA;AACA;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AApDA;AAtCA;;ACXA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;ACrBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACXA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACNA;AACA;AACA;;;;;ACFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AADA;AALA;AACA;AAQA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AARA;AAUA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AAdA;AAeA;AACA;AACA;AACA;AADA;AAGA;AAAA;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AA/BA;AArCA;;ACvFA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;ACvBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;ACXA;;;;;;;;ACAA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACNA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACXA;AACA;AACA;;;;;ACFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAFA;AADA;AACA;AAKA;AACA;AACA;AACA;AAFA;AAIA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATA;AAWA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AATA;AACA;AAUA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AAAA;AACA;AANA;AAQA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAtDA;AAuDA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AAfA;AACA;AAgBA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AA3BA;AAvEA;;AChIA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AC1BA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACNA;AACA;AACA;;;;;ACFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAHA;AAHA;AAUA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAFA;AAIA;AACA;AADA;AADA;AACA;AAIA;AACA;AACA;AAAA;AAAA;AAAA;AADA;AAGA;AACA;AACA;AAAA;AACA;AAAA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AALA;AAQA;AACA;AACA;AACA;AACA;AALA;AAQA;AACA;AACA;AACA;AACA;AALA;AAQA;AACA;AACA;AACA;AACA;AALA;AAQA;AACA;AACA;AACA;AACA;AALA;AA/BA;AAwCA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAFA;AAIA;AACA;AACA;AACA;AAHA;AADA;AACA;AAMA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AA7BA;AA8BA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAXA;AADA;AAtGA;;ACpCA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;A", "sourceRoot": ""}