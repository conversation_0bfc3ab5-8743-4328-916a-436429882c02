{layout name="layout2" /}

<div class="layui-card layui-form" style="box-shadow:none;">
    <div class="layui-card-body">
        <div class="layui-form-item">
            <label class="layui-form-label"><span style="color:red;">*</span>审核结果</label>
            <div class="layui-input-block">
                <input type="radio" name="status" value="1" title="审核通过" lay-filter="audit_status">
                <input type="radio" name="status" value="2" title="审核拒绝" checked lay-filter="audit_status">
            </div>
        </div>

        <div class="layui-form-item" id="contract-container">
            <label class="layui-form-label">合同文件</label>
            <div class="layui-input-block">
                <a href="{$docs}" target="_blank" class="layui-btn layui-btn-sm layui-btn-normal">查看合同文件</a>
            </div>
        </div>

        <div class="layui-form-item layui-form-text" id="remark-container">
            <label for="remark" class="layui-form-label"><span style="color:red;" class="required-mark">*</span>审核理由</label>
            <div class="layui-input-block">
                <textarea name="remark" id="remark" placeholder="请输入拒绝理由" class="layui-textarea"></textarea>
            </div>
            <div class="layui-form-mid layui-word-aux">审核拒绝时，理由必填</div>
        </div>

        <button class="layui-btn layui-hide" lay-submit lay-filter="depositAuditSubmit" id="depositAuditSubmit">提交</button>
    </div>
</div>

<script>
    layui.use(['form'], function(){
        var form = layui.form;
        
        // 监听审核状态变化
        form.on('radio(audit_status)', function(data){
            if(data.value == '1') {
                // 通过，理由可选
                $('.required-mark').hide();
            } else {
                // 拒绝，理由必填
                $('.required-mark').show();
            }
        });
        
        // 表单提交前验证
        form.verify({
            remark: function(value) {
                var status = $('input[name="status"]:checked').val();
                if(status == '2' && !value) {
                    return '拒绝时必须填写理由';
                }
            }
        });
        
        // 表单提交
        form.on('submit(depositAuditSubmit)', function(data){
            return true;
        });
    });
</script>
