{layout name="layout1" /}
<div class="wrapper">
    <div class="layui-card">
        <div class="layui-card-body">
            <div class="layui-collapse like-layui-collapse" lay-accordion="" style="border:1px dashed #c4c4c4">
                <div class="layui-colla-item">
                    <h2 class="layui-colla-title like-layui-colla-title" style="background-color: #fff">操作提示</h2>
                    <div class="layui-colla-content layui-show">
                        <p>*平台需要先设置好秒杀时段，商家可以选择需要参与的秒杀时段。</p>
                        <p>*删除时段会移除所有参与该时段的商品，请谨慎操作。</p>
                    </div>
                </div>
            </div>
            <button class="layui-btn layui-btn-normal layui-btn-sm" style="margin-top:15px;" id="add_time">新增秒杀时段</button>
            <table id="seckill-lists" lay-filter="seckill-lists"></table>
            <script type="text/html" id="time-operation">
                <a class="layui-btn {$view_theme_color} layui-btn-sm" lay-event="edit_time">编辑</a>
                <a class="layui-btn layui-btn-danger layui-btn-sm" lay-event="del_time">删除时段</a>
            </script>
        </div>

    </div>
</div>
<style>
    .layui-table-cell {
        height: auto;
    }
</style>
<script>
    layui.config({
        version:"{$front_version}",
        base: '/static/lib/' //静态资源所在路径
    }).use(['table','form'], function(){
        var $ = layui.$
            ,form = layui.form
            ,table = layui.table
            ,element = layui.element;

        $('#add_time').click(function() {
            var index = layer.open({
                type: 2
                ,title: '新增秒杀时间段'
                ,content: '{:url("seckill.seckill_time/addTime")}'
                ,area: ['60%', '60%']
                ,btn: ['保存', '取消']
                ,maxmin: true
                ,yes: function(index, layero){
                    var iframeWindow = window['layui-layer-iframe'+ index]
                        ,submitID = 'add-submit'
                        ,submit = layero.find('iframe').contents().find('#'+ submitID);
                    //监听提交
                    iframeWindow.layui.form.on('submit('+ submitID +')', function(data){
                        var field = data.field;
                        like.ajax({
                            url:'{:url("seckill.seckill_time/addTime")}',
                            data:field,
                            type:"post",
                            success:function(res)
                            {
                                if(res.code == 1)
                                {
                                    layui.layer.msg(res.msg, {
                                        offset: '15px'
                                        , icon: 1
                                        , time: 1000
                                    });
                                    layer.close(index); //关闭弹层
                                    table.reload('seckill-lists'); //数据刷新
                                }
                            }
                        });
                    });

                    submit.trigger('click');
                }
            });
        });

        table.render({
            id:'seckill-lists'
            ,elem: '#seckill-lists'
            ,url: '{:url("seckill.seckill_time/timeLists")}'  //模拟接口
            ,cols: [[
                {field: 'time',align: 'center',title: '秒杀时段'}
                ,{field: 'start_time',align: 'center', title:'开始时间'}
                ,{field: 'end_time',align: 'center', title:'结束时间'}
                ,{fixed: 'right', title: '操作',width:320, align: 'center',  toolbar: '#time-operation'}
            ]]
            ,page:true
            ,text: {none: '暂无数据！'}
            ,parseData: function(res){ //将原始数据解析成 table 组件所规定的数据
                return {
                    "code":res.code,
                    "msg":res.msg,
                    "count": res.data.count, //解析数据长度
                    "data": res.data.list, //解析数据列表
                };
            },
            response: {
                statusCode: 1
            }
            ,done: function(res, curr, count){
                // 解决操作栏因为内容过多换行问题
                $(".layui-table-main tr").each(function (index, val) {
                    $($(".layui-table-fixed-l .layui-table-body tbody tr")[index]).height($(val).height());
                    $($(".layui-table-fixed-r .layui-table-body tbody tr")[index]).height($(val).height());
                });
            }
        });

        //监听工具条
        table.on('tool(seckill-lists)', function(obj){
            var id = obj.data.id;
            if(obj.event === 'del_time'){
                var time = obj.data.time;
                layer.confirm('确定删除秒杀时段：'+'<span style="color: red">'+time+'</span>'+'。该秒杀时段的商品将会全部移除，请谨慎操作。', function(index){
                    like.ajax({
                        url:'{:url("seckill.seckill_time/delTime")}',
                        data:{id:id},
                        type:"post",
                        success:function(res)
                        {
                            if(res.code == 1)
                            {
                                layui.layer.msg(res.msg, {
                                    offset: '15px'
                                    , icon: 1
                                    , time: 1000
                                });
                                layer.close(index); //关闭弹层
                                table.reload('seckill-lists'); //数据刷新
                            }
                        }
                    });
                    layer.close(index);
                })
            }

            if(obj.event === 'edit_time'){
                layer.open({
                    type: 2
                    ,title: '编辑秒杀时间段'
                    ,content: '{:url("seckill.seckill_time/editTime")}?id='+id
                    ,area: ['60%', '60%']
                    ,btn: ['保存', '取消']
                    ,maxmin: true
                    ,yes: function(index, layero){
                        var iframeWindow = window['layui-layer-iframe'+ index]
                            ,submitID = 'edit-submit'
                            ,submit = layero.find('iframe').contents().find('#'+ submitID);
                        //监听提交
                        iframeWindow.layui.form.on('submit('+ submitID +')', function(data){
                            var field = data.field;
                            like.ajax({
                                url:'{:url("seckill.seckill_time/editTime")}',
                                data:field,
                                type:"post",
                                success:function(res)
                                {
                                    if(res.code == 1)
                                    {
                                        layui.layer.msg(res.msg, {
                                            offset: '15px'
                                            , icon: 1
                                            , time: 1000
                                        });
                                        layer.close(index); //关闭弹层
                                        table.reload('seckill-lists'); //数据刷新
                                    }
                                }
                            });
                        });

                        submit.trigger('click');
                    }
                });
            }

        });
    });
</script>