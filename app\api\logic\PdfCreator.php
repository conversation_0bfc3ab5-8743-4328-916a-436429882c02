<?php
namespace app\api\logic;

use app\common\basics\Logic;
use Exception;

/**
 * PDF创建器
 * 用于创建包含签名和日期的PDF文件
 * Class PdfCreator
 * @package app\api\logic
 */
class PdfCreator extends Logic
{
    /**
     * @var string 模板PDF文件路径
     */
    protected $templatePath;

    /**
     * @var string 签名图片路径
     */
    protected $signaturePath;

    /**
     * @var string 输出PDF路径
     */
    protected $outputPath;

    /**
     * @var array 签名位置坐标
     */
    protected $signaturePosition = [
        'x' => 400,  // 默认X坐标
        'y' => 680,  // 默认Y坐标
        'width' => 80, // 默认宽度
        'height' => 40 // 默认高度
    ];

    /**
     * @var array 日期位置坐标
     */
    protected $datePosition = [
        'x' => 400,  // 默认X坐标
        'y' => 720,  // 默认Y坐标
    ];

    /**
     * 构造函数
     * @param string $templatePath 模板PDF文件路径
     */
    public function __construct($templatePath = '')
    {
        if (!empty($templatePath)) {
            $this->setTemplatePath($templatePath);
        }
    }

    /**
     * 设置模板PDF文件路径
     * @param string $templatePath
     * @return $this
     * @throws Exception
     */
    public function setTemplatePath($templatePath)
    {
        if (!file_exists($templatePath)) {
            throw new Exception('模板PDF文件不存在: ' . $templatePath);
        }
        $this->templatePath = $templatePath;
        return $this;
    }

    /**
     * 设置签名图片路径
     * @param string $signaturePath
     * @return $this
     * @throws Exception
     */
    public function setSignaturePath($signaturePath)
    {
        if (!file_exists($signaturePath)) {
            throw new Exception('签名图片不存在: ' . $signaturePath);
        }
        $this->signaturePath = $signaturePath;
        return $this;
    }

    /**
     * 设置输出PDF路径
     * @param string $outputPath
     * @return $this
     */
    public function setOutputPath($outputPath)
    {
        $this->outputPath = $outputPath;
        return $this;
    }

    /**
     * 设置签名位置
     * @param float $x X坐标
     * @param float $y Y坐标
     * @param float|null $width 宽度
     * @param float|null $height 高度
     * @return $this
     */
    public function setSignaturePosition($x, $y, $width = null, $height = null)
    {
        $this->signaturePosition['x'] = $x;
        $this->signaturePosition['y'] = $y;

        if ($width !== null) {
            $this->signaturePosition['width'] = $width;
        }

        if ($height !== null) {
            $this->signaturePosition['height'] = $height;
        }

        return $this;
    }

    /**
     * 设置日期位置
     * @param float $x X坐标
     * @param float $y Y坐标
     * @return $this
     */
    public function setDatePosition($x, $y)
    {
        $this->datePosition['x'] = $x;
        $this->datePosition['y'] = $y;
        return $this;
    }

    /**
     * 创建PDF文件，添加签名和日期
     * @param string $dateFormat 日期格式，默认为Y-m-d
     * @return string 处理后的PDF文件路径
     * @throws Exception
     */
    public function create($dateFormat = 'Y-m-d')
    {
        // 检查必要参数
        if (empty($this->templatePath)) {
            throw new Exception('未设置模板PDF文件路径');
        }

        if (empty($this->signaturePath)) {
            throw new Exception('未设置签名图片路径');
        }

        if (empty($this->outputPath)) {
            // 如果未设置输出路径，则生成一个临时路径
            $pathInfo = pathinfo($this->templatePath);
            $this->outputPath = $pathInfo['dirname'] . '/' . $pathInfo['filename'] . '_signed.' . $pathInfo['extension'];
        }

        try {
            // 由于服务器上没有安装PDF处理工具，我们将创建一个包含原始PDF、签名图片和日期的ZIP文件
            // 首先复制原始PDF
            copy($this->templatePath, $this->outputPath);

            // 创建一个HTML文件，用于显示签名和日期
            $htmlPath = dirname($this->outputPath) . '/signature_' . uniqid() . '.html';

            // 获取签名图片的相对路径
            $signatureRelativePath = 'signature_' . uniqid() . '.png';
            $signatureOutputPath = dirname($this->outputPath) . '/' . $signatureRelativePath;

            // 复制签名图片
            copy($this->signaturePath, $signatureOutputPath);

            // 创建HTML内容
            $html = '<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>签名和日期</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            position: relative;
            width: 100%;
            height: 100vh;
        }
        .pdf-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }
        .signature {
            position: absolute;
            left: ' . $this->signaturePosition['x'] . 'px;
            top: ' . $this->signaturePosition['y'] . 'px;
            width: ' . $this->signaturePosition['width'] . 'px;
            height: ' . $this->signaturePosition['height'] . 'px;
            z-index: 10;
        }
        .date {
            position: absolute;
            left: ' . $this->datePosition['x'] . 'px;
            top: ' . $this->datePosition['y'] . 'px;
            font-size: 14px;
            font-family: Arial, sans-serif;
            z-index: 10;
        }
    </style>
</head>
<body>
    <div class="pdf-container">
        <iframe src="' . basename($this->outputPath) . '" width="100%" height="100%" frameborder="0"></iframe>
    </div>
    <img class="signature" src="' . $signatureRelativePath . '" alt="签名">
    <div class="date">' . date($dateFormat) . '</div>
</body>
</html>';
            file_put_contents($htmlPath, $html);

            // 创建一个包含所有文件的ZIP文件
            $zipPath = dirname($this->outputPath) . '/signed_pdf_' . uniqid() . '.zip';
            $zip = new \ZipArchive();
            if ($zip->open($zipPath, \ZipArchive::CREATE) === TRUE) {
                $zip->addFile($this->outputPath, basename($this->outputPath));
                $zip->addFile($signatureOutputPath, basename($signatureOutputPath));
                $zip->addFile($htmlPath, basename($htmlPath));
                $zip->close();

                // 更新输出路径为ZIP文件路径
                $this->outputPath = $zipPath;
            } else {
                throw new Exception('无法创建ZIP文件');
            }

            // 清理临时文件
            @unlink($signatureOutputPath);
            @unlink($htmlPath);

            // 返回输出路径
            return $this->outputPath;
        } catch (Exception $e) {
            throw new Exception('创建PDF文件时出错: ' . $e->getMessage());
        }
    }
}
