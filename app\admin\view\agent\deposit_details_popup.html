{layout name="layout1" /}
<style>
    .layui-card-body {
        padding: 15px;
    }
    .layui-form-label {
        width: 120px;
        text-align: right;
        padding: 9px 15px 9px 0;
        margin-bottom: 0;
        font-weight: 500;
    }
    .layui-table-tool-temp {
        padding-right: 0;
    }
    .layui-btn-container {
        margin-bottom: 10px;
    }
    .info-item {
        margin-bottom: 10px;
        line-height: 30px;
        display: flex;
    }
    .info-label {
        width: 120px;
        text-align: right;
        padding-right: 15px;
        font-weight: 500;
    }
    .info-content {
        flex: 1;
    }
    .info-section {
        background-color: #f8f8f8;
        padding: 15px;
        border-radius: 4px;
        margin-bottom: 20px;
        border: 1px solid #e6e6e6;
    }
    .section-title {
        font-size: 16px;
        font-weight: bold;
        margin-bottom: 15px;
        padding-bottom: 10px;
        border-bottom: 1px solid #e6e6e6;
    }
</style>
    <div class="layui-fluid">
        <div class="layui-card">
            <div class="layui-card-body">
                <!-- 保证金信息区域 -->
                <div class="info-section">
                    <div class="section-title">保证金信息</div>

                    <div class="info-item">
                        <div class="info-label">用户信息：</div>
                        <div class="info-content">{$user.nickname|default=''} ({$user.sn|default=''}) {$user.mobile|default=''}</div>
                    </div>

                    <div class="info-item">
                        <div class="info-label">保证金金额：</div>
                        <div class="info-content">{$deposit.amount|default='0.00'} 元</div>
                    </div>

                    <div class="info-item">
                        <div class="info-label">当前余额：</div>
                        <div class="info-content">{$deposit.current_balance|default='0.00'} 元</div>
                    </div>

                    <div class="info-item">
                        <div class="info-label">支付时间：</div>
                        <div class="info-content">{$deposit.payment_date|date='Y-m-d H:i:s'|default=''}</div>
                    </div>

                    <div class="info-item">
                        <div class="info-label">状态：</div>
                        <div class="info-content">
                            {if $deposit.status == 0}未支付
                            {elseif $deposit.status == 1}已支付
                            {elseif $deposit.status == 2}
                                {if isset($deposit.refund_request_time) && $deposit.refund_request_time > 0 && isset($deposit.refund_publicity_end_time) && $deposit.refund_publicity_end_time > 0 && $deposit.refund_request_time < $deposit.refund_publicity_end_time}
                                退款中(公示期)
                                {else}
                                公示期结束(可退)
                                {/if}
                            {elseif $deposit.status == 3}退款申请中
                            {elseif $deposit.status == 4}已退款
                            {elseif $deposit.status == 5}退款失败
                            {else}未知状态
                            {/if}
                        </div>
                    </div>
                </div>

                <!-- 保证金明细区域 -->
                <div class="info-section">
                    <div class="section-title">保证金明细</div>

                    <!-- 搜索表单 -->
                    <div class="layui-form layui-form-pane" style="margin-bottom: 15px;">
                        <div class="layui-form-item">
                            <div class="layui-inline">
                                <label class="layui-form-label">变动类型</label>
                                <div class="layui-input-inline">
                                    <select name="change_type" lay-filter="change_type">
                                        <option value="">全部</option>
                                        <option value="1">缴纳</option>
                                        <option value="2">增加</option>
                                        <option value="3">扣除</option>
                                        <option value="4">退还</option>
                                    </select>
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label">变动日期</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="start_date" id="start_date" placeholder="开始日期" autocomplete="off" class="layui-input">
                                </div>
                                <div class="layui-form-mid">-</div>
                                <div class="layui-input-inline">
                                    <input type="text" name="end_date" id="end_date" placeholder="结束日期" autocomplete="off" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-inline">
                                <button class="layui-btn" lay-submit lay-filter="search">搜索</button>
                                <button class="layui-btn layui-btn-primary" lay-submit lay-filter="reset">重置</button>
                            </div>
                        </div>
                    </div>

                    <!-- 操作按钮 -->
                    <div class="layui-btn-container" style="margin-bottom: 15px;">
                        <button class="layui-btn layui-btn-normal" id="adjust-deposit">调整保证金</button>
                    </div>

                    <!-- 数据表格 -->
                    <table id="detail-lists" lay-filter="detail-lists"></table>
                </div>

                <script type="text/html" id="change-type-tpl">
                    {{# if(d.change_type == 1){ }}
                    <span class="layui-badge layui-bg-blue">缴纳</span>
                    {{# } else if(d.change_type == 2){ }}
                    <span class="layui-badge layui-bg-green">增加</span>
                    {{# } else if(d.change_type == 3){ }}
                    <span class="layui-badge layui-bg-orange">扣除</span>
                    {{# } else if(d.change_type == 4){ }}
                    <span class="layui-badge layui-bg-gray">退还</span>
                    {{# } else { }}
                    <span class="layui-badge layui-bg-black">未知</span>
                    {{# } }}
                </script>

                <script type="text/html" id="deposit-change-tpl">
                    {{# if(d.deposit_change > 0){ }}
                    <span style="color: green;">+{{ d.deposit_change }}</span>
                    {{# } else { }}
                    <span style="color: red;">{{ d.deposit_change }}</span>
                    {{# } }}
                </script>
            </div>
        </div>
    </div>

    <script>
        layui.use(['table', 'form', 'laydate', 'layer'], function(){
            var table = layui.table;
            var form = layui.form;
            var laydate = layui.laydate;
            var layer = layui.layer;
            var $ = layui.$;

            // 日期选择器
            laydate.render({
                elem: '#start_date'
            });
            laydate.render({
                elem: '#end_date'
            });

            // 表格渲染
            table.render({
                elem: '#detail-lists',
                url: '{:url("agent.agent/depositDetails")}',
                method: 'post',
                where: {
                    deposit_id: '{$deposit_id}',
                    user_id: '{$user_id}'
                },
                page: true,
                cols: [[
                    {field: 'id', title: 'ID', width: 80},
                    {field: 'sn', title: '明细单号', width: 180},
                    {field: 'change_type', title: '变动类型', width: 100, templet: '#change-type-tpl'},
                    {field: 'deposit_change', title: '变动金额', width: 120, templet: '#deposit-change-tpl'},
                    {field: 'reason', title: '变动原因', width: 200},
                    {field: 'remark', title: '备注', width: 200},
                    {field: 'change_date', title: '变动日期', width: 120},
                    {field: 'created_at', title: '创建时间', width: 180}
                ]],
                response: {
                    statusName: 'code',
                    statusCode: 1,
                    msgName: 'msg',
                    countName: 'count',
                    dataName: 'lists'
                }
            });

            // 搜索
            form.on('submit(search)', function(data){
                var field = data.field;
                field.deposit_id = '{$deposit_id}';
                field.user_id = '{$user_id}';

                table.reload('detail-lists', {
                    where: field
                });
                return false;
            });

            // 重置
            form.on('submit(reset)', function(){
                $('#start_date').val('');
                $('#end_date').val('');
                $('select[name=change_type]').val('');
                form.render('select');

                table.reload('detail-lists', {
                    where: {
                        deposit_id: '{$deposit_id}',
                        user_id: '{$user_id}',
                        change_type: '',
                        start_date: '',
                        end_date: ''
                    }
                });
                return false;
            });

            // 调整保证金
            $('#adjust-deposit').click(function(){
                var index = parent.layer.getFrameIndex(window.name); // 获取窗口索引
                parent.layer.close(index); // 关闭当前弹窗

                // 打开调整保证金弹窗
                parent.layer.open({
                    type: 2,
                    title: '调整代理保证金',
                    area: ['600px', '500px'],
                    content: '{:url("agent.agent/adjustDeposit")}?deposit_id={$deposit_id}&user_id={$user_id}&is_popup=1',
                    maxmin: true
                });
            });
        });
    </script>
