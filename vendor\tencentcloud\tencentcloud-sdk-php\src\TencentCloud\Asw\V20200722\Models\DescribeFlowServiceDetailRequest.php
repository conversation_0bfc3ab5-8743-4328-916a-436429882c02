<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Asw\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * DescribeFlowServiceDetail请求参数结构体
 *
 * @method string getFlowServiceResource() 获取状态机所属服务资源名
 * @method void setFlowServiceResource(string $FlowServiceResource) 设置状态机所属服务资源名
 */
class DescribeFlowServiceDetailRequest extends AbstractModel
{
    /**
     * @var string 状态机所属服务资源名
     */
    public $FlowServiceResource;

    /**
     * @param string $FlowServiceResource 状态机所属服务资源名
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("FlowServiceResource",$param) and $param["FlowServiceResource"] !== null) {
            $this->FlowServiceResource = $param["FlowServiceResource"];
        }
    }
}
