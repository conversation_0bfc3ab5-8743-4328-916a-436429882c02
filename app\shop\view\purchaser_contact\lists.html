{layout name="layout1" /}
<style>
    .layui-table-cell {
        height: auto;
    }
    .contact-info {
        display: flex;
        align-items: center;
    }
    .contact-info img {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        margin-right: 10px;
    }
    .contact-details {
        flex: 1;
    }
    .contact-details p {
        margin: 2px 0;
        font-size: 12px;
    }
    .purchaser-content {
        max-width: 200px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
    .source-tag {
        padding: 2px 6px;
        border-radius: 3px;
        font-size: 11px;
        color: #fff;
    }
    .source-web { background-color: #1E9FFF; }
    .source-app { background-color: #FF5722; }
    .source-mini { background-color: #009688; }
    .source-h5 { background-color: #FFB800; }
</style>

<div class="wrapper">
    <div class="layui-card">
        <!--操作提示-->
        <div class="layui-card-body">
            <div class="layui-collapse like-layui-collapse" lay-accordion="" style="border:1px dashed #c4c4c4">
                <div class="layui-colla-item">
                    <h2 class="layui-colla-title like-layui-colla-title" style="background-color: #fff">操作提示</h2>
                    <div class="layui-colla-content layui-show">
                        <p>*此页面显示用户联系您发布的采购信息的记录。</p>
                        <p>*可以查看联系用户的基本信息、联系时间、来源等详细信息。</p>
                        <p>*支持按时间范围、来源等条件筛选联系记录。</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-card-body">
            <!--搜索条件-->
            <div class="layui-form">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">联系来源：</label>
                        <div class="layui-input-inline">
                            <select name="source" id="source">
                                <option value="">全部来源</option>
                                <option value="web">网页</option>
                                <option value="app">应用</option>
                                <option value="mini">小程序</option>
                                <option value="h5">H5页面</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">联系时间：</label>
                        <div class="layui-input-inline">
                            <input type="text" name="start_time" id="start_time" placeholder="开始时间" 
                                   autocomplete="off" class="layui-input">
                        </div>
                        <div class="layui-form-mid">-</div>
                        <div class="layui-input-inline">
                            <input type="text" name="end_time" id="end_time" placeholder="结束时间" 
                                   autocomplete="off" class="layui-input">
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">用户搜索：</label>
                        <div class="layui-input-inline">
                            <input type="text" name="keyword" id="keyword" placeholder="用户昵称/手机号" 
                                   autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn layui-btn-sm layuiadmin-btn-unit {$view_theme_color}" 
                                lay-submit lay-filter="contact-search">查询</button>
                        <button class="layui-btn layui-btn-sm layui-btn-primary layuiadmin-btn-unit" 
                                lay-submit lay-filter="contact-clear-search">清空查询</button>
                    </div>
                </div>
            </div>

            <div style="padding-bottom: 10px;">
                <button class="layui-btn layui-btn-sm layuiadmin-btn-unit layui-btn-normal" data-type="stats">
                    <i class="layui-icon layui-icon-chart"></i>联系统计
                </button>
                <button class="layui-btn layui-btn-sm layuiadmin-btn-unit layui-btn-warm" data-type="export">
                    <i class="layui-icon layui-icon-export"></i>导出记录
                </button>
            </div>

            <table id="contact_lists" lay-filter="contact_lists"></table>

            <!-- 联系用户信息模板 -->
            <script type="text/html" id="contact-user-info">
                <div class="contact-info">
                    <img src="{{d.avatar || '/static/images/default_avatar.png'}}" alt="头像">
                    <div class="contact-details">
                        <p><strong>{{d.nickname || '未知用户'}}</strong></p>
                        <p>{{d.mobile || '未绑定手机'}}</p>
                    </div>
                </div>
            </script>

            <!-- 采购内容模板 -->
            <script type="text/html" id="purchaser-content">
                <div class="purchaser-content" title="{{d.purchaser_content}}">
                    {{d.purchaser_content || '暂无内容'}}
                </div>
            </script>

            <!-- 联系来源模板 -->
            <script type="text/html" id="contact-source">
                {{# if(d.source === 'web') { }}
                    <span class="source-tag source-web">网页</span>
                {{# } else if(d.source === 'app') { }}
                    <span class="source-tag source-app">应用</span>
                {{# } else if(d.source === 'mini') { }}
                    <span class="source-tag source-mini">小程序</span>
                {{# } else if(d.source === 'h5') { }}
                    <span class="source-tag source-h5">H5页面</span>
                {{# } else { }}
                    <span class="source-tag" style="background-color: #999;">{{d.source}}</span>
                {{# } }}
            </script>

            <!-- 操作按钮模板 -->
            <script type="text/html" id="contact-operation">
                <a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="detail">查看详情</a>
            </script>
        </div>
    </div>
</div>

<script>
    layui.use(['table', 'form', 'laydate'], function () {
        var $ = layui.$
            , form = layui.form
            , table = layui.table
            , laydate = layui.laydate;

        // 初始化日期选择器
        laydate.render({
            elem: '#start_time',
            type: 'datetime'
        });
        laydate.render({
            elem: '#end_time',
            type: 'datetime'
        });

        // 列表
        like.tableLists('#contact_lists', '{:url("purchaser_contact/lists")}', [
            {type: 'numbers', title: '序号', width: 60}
            , {field: 'user_info', width: 200, title: '联系用户', templet: '#contact-user-info'}
            , {field: 'purchaser_content', width: 250, title: '采购内容', templet: '#purchaser-content'}
            , {field: 'source', width: 100, title: '联系来源', align: 'center', templet: '#contact-source'}
            , {field: 'contact_time_text', width: 160, title: '联系时间', align: 'center'}
            , {field: 'ip', width: 120, title: 'IP地址', align: 'center'}
            , {fixed: 'right', title: '操作', width: 120, align: 'center', toolbar: '#contact-operation'}
        ]);

        //监听搜索
        form.on('submit(contact-search)', function (data) {
            var field = data.field;
            //执行重载
            table.reload('contact_lists', {
                where: field,
                page: {curr: 1},
            });
        });

        //清空查询
        form.on('submit(contact-clear-search)', function () {
            $('#keyword').val('');
            $('#source').val('');
            $('#start_time').val('');
            $('#end_time').val('');
            form.render('select');
            //刷新列表
            table.reload('contact_lists', {
                where: [],
                page: {curr: 1},
            });
        });

        //事件
        var active = {
            stats: function () {
                layer.open({
                    type: 2
                    , title: '联系统计'
                    , content: '{:url("purchaser_contact/stats")}'
                    , area: ['90%', '90%']
                    , btn: ['关闭']
                    , yes: function (index) {
                        layer.close(index);
                    }
                })
            },
            export: function () {
                var searchParams = {};
                $('.layui-form input, .layui-form select').each(function() {
                    var name = $(this).attr('name');
                    var value = $(this).val();
                    if (name && value) {
                        searchParams[name] = value;
                    }
                });
                
                like.ajax({
                    url: '{:url("purchaser_contact/export")}',
                    data: searchParams,
                    type: "get",
                    success: function (res) {
                        if (res.code == 1) {
                            layer.msg('导出成功', {icon: 1});
                            // 这里可以添加下载逻辑
                        } else {
                            layer.msg(res.msg, {icon: 2});
                        }
                    }
                });
            }
        };
        
        $('.layui-btn.layuiadmin-btn-unit').on('click', function () {
            var type = $(this).data('type');
            active[type] ? active[type].call(this) : '';
        });

        // 监听表格工具条
        table.on('tool(contact_lists)', function (obj) {
            if (obj.event === 'detail') {
                var id = obj.data.id;
                layer.open({
                    type: 2
                    , title: '联系记录详情'
                    , content: '{:url("purchaser_contact/detail")}?id=' + id
                    , area: ['80%', '80%']
                    , btn: ['关闭']
                    , yes: function (index) {
                        layer.close(index);
                    }
                })
            }
        });
    });
</script>
