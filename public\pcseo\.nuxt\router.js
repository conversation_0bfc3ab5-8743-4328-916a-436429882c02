import Vue from 'vue'
import Router from 'vue-router'
import { normalizeURL, decode } from 'ufo'
import { interopDefault } from './utils'
import scrollBehavior from './router.scrollBehavior.js'

const _20cc4777 = () => interopDefault(import('../pages/category.vue' /* webpackChunkName: "pages/category" */))
const _53a8f1e6 = () => interopDefault(import('../pages/confirm_order.vue' /* webpackChunkName: "pages/confirm_order" */))
const _c053a18a = () => interopDefault(import('../pages/get_coupons.vue' /* webpackChunkName: "pages/get_coupons" */))
const _94f6dea6 = () => interopDefault(import('../pages/help_center/index.vue' /* webpackChunkName: "pages/help_center/index" */))
const _ddf8d1b8 = () => interopDefault(import('../pages/news_list/index.vue' /* webpackChunkName: "pages/news_list/index" */))
const _b747eac6 = () => interopDefault(import('../pages/payment.vue' /* webpackChunkName: "pages/payment" */))
const _764a8346 = () => interopDefault(import('../pages/seckill.vue' /* webpackChunkName: "pages/seckill" */))
const _bd7583c0 = () => interopDefault(import('../pages/shop_cart.vue' /* webpackChunkName: "pages/shop_cart" */))
const _228b20fa = () => interopDefault(import('../pages/shop_street.vue' /* webpackChunkName: "pages/shop_street" */))
const _c0a4f686 = () => interopDefault(import('../pages/shop_street_detail.vue' /* webpackChunkName: "pages/shop_street_detail" */))
const _1a495868 = () => interopDefault(import('../pages/special_area.vue' /* webpackChunkName: "pages/special_area" */))
const _a3014a16 = () => interopDefault(import('../pages/store_settled/index.vue' /* webpackChunkName: "pages/store_settled/index" */))
const _2956a4c6 = () => interopDefault(import('../pages/account/forget_pwd.vue' /* webpackChunkName: "pages/account/forget_pwd" */))
const _38939dde = () => interopDefault(import('../pages/account/login.vue' /* webpackChunkName: "pages/account/login" */))
const _bebd7384 = () => interopDefault(import('../pages/account/register.vue' /* webpackChunkName: "pages/account/register" */))
const _905be49c = () => interopDefault(import('../pages/help_center/help_center_detail.vue' /* webpackChunkName: "pages/help_center/help_center_detail" */))
const _760c0378 = () => interopDefault(import('../pages/news_list/news_list_detail.vue' /* webpackChunkName: "pages/news_list/news_list_detail" */))
const _8c81d984 = () => interopDefault(import('../pages/store_settled/detail.vue' /* webpackChunkName: "pages/store_settled/detail" */))
const _2351673e = () => interopDefault(import('../pages/store_settled/record.vue' /* webpackChunkName: "pages/store_settled/record" */))
const _3572766c = () => interopDefault(import('../pages/user/address/index.vue' /* webpackChunkName: "pages/user/address/index" */))
const _246752a1 = () => interopDefault(import('../pages/user/after_sales/index.vue' /* webpackChunkName: "pages/user/after_sales/index" */))
const _2d1d9a0e = () => interopDefault(import('../pages/user/collection.vue' /* webpackChunkName: "pages/user/collection" */))
const _4b5eaf42 = () => interopDefault(import('../pages/user/coupons.vue' /* webpackChunkName: "pages/user/coupons" */))
const _320e85da = () => interopDefault(import('../pages/user/evaluation/index.vue' /* webpackChunkName: "pages/user/evaluation/index" */))
const _830626f4 = () => interopDefault(import('../pages/user/order/index.vue' /* webpackChunkName: "pages/user/order/index" */))
const _24454444 = () => interopDefault(import('../pages/user/profile.vue' /* webpackChunkName: "pages/user/profile" */))
const _2a6c52bc = () => interopDefault(import('../pages/user/user_wallet.vue' /* webpackChunkName: "pages/user/user_wallet" */))
const _46e7110e = () => interopDefault(import('../pages/user/after_sales/after_sale_details.vue' /* webpackChunkName: "pages/user/after_sales/after_sale_details" */))
const _a39ccce2 = () => interopDefault(import('../pages/user/after_sales/apply_result.vue' /* webpackChunkName: "pages/user/after_sales/apply_result" */))
const _63c2098e = () => interopDefault(import('../pages/user/after_sales/apply_sale.vue' /* webpackChunkName: "pages/user/after_sales/apply_sale" */))
const _2f441141 = () => interopDefault(import('../pages/user/evaluation/evaluate.vue' /* webpackChunkName: "pages/user/evaluation/evaluate" */))
const _ad189866 = () => interopDefault(import('../pages/user/order/detail.vue' /* webpackChunkName: "pages/user/order/detail" */))
const _75e8abdb = () => interopDefault(import('../pages/goods_details/_id.vue' /* webpackChunkName: "pages/goods_details/_id" */))
const _6e7ea3ea = () => interopDefault(import('../pages/goods_list/_type.vue' /* webpackChunkName: "pages/goods_list/_type" */))
const _0f6a21ae = () => interopDefault(import('../pages/index.vue' /* webpackChunkName: "pages/index" */))

const emptyFn = () => {}

Vue.use(Router)

export const routerOptions = {
  mode: 'history',
  base: '/pc/',
  linkActiveClass: 'nuxt-link-active',
  linkExactActiveClass: 'nuxt-link-exact-active',
  scrollBehavior,

  routes: [{
    path: "/category",
    component: _20cc4777,
    name: "category"
  }, {
    path: "/confirm_order",
    component: _53a8f1e6,
    name: "confirm_order"
  }, {
    path: "/get_coupons",
    component: _c053a18a,
    name: "get_coupons"
  }, {
    path: "/help_center",
    component: _94f6dea6,
    name: "help_center"
  }, {
    path: "/news_list",
    component: _ddf8d1b8,
    name: "news_list"
  }, {
    path: "/payment",
    component: _b747eac6,
    name: "payment"
  }, {
    path: "/seckill",
    component: _764a8346,
    name: "seckill"
  }, {
    path: "/shop_cart",
    component: _bd7583c0,
    name: "shop_cart"
  }, {
    path: "/shop_street",
    component: _228b20fa,
    name: "shop_street"
  }, {
    path: "/shop_street_detail",
    component: _c0a4f686,
    name: "shop_street_detail"
  }, {
    path: "/special_area",
    component: _1a495868,
    name: "special_area"
  }, {
    path: "/store_settled",
    component: _a3014a16,
    name: "store_settled"
  }, {
    path: "/account/forget_pwd",
    component: _2956a4c6,
    name: "account-forget_pwd"
  }, {
    path: "/account/login",
    component: _38939dde,
    name: "account-login"
  }, {
    path: "/account/register",
    component: _bebd7384,
    name: "account-register"
  }, {
    path: "/help_center/help_center_detail",
    component: _905be49c,
    name: "help_center-help_center_detail"
  }, {
    path: "/news_list/news_list_detail",
    component: _760c0378,
    name: "news_list-news_list_detail"
  }, {
    path: "/store_settled/detail",
    component: _8c81d984,
    name: "store_settled-detail"
  }, {
    path: "/store_settled/record",
    component: _2351673e,
    name: "store_settled-record"
  }, {
    path: "/user/address",
    component: _3572766c,
    name: "user-address"
  }, {
    path: "/user/after_sales",
    component: _246752a1,
    name: "user-after_sales"
  }, {
    path: "/user/collection",
    component: _2d1d9a0e,
    name: "user-collection"
  }, {
    path: "/user/coupons",
    component: _4b5eaf42,
    name: "user-coupons"
  }, {
    path: "/user/evaluation",
    component: _320e85da,
    name: "user-evaluation"
  }, {
    path: "/user/order",
    component: _830626f4,
    name: "user-order"
  }, {
    path: "/user/profile",
    component: _24454444,
    name: "user-profile"
  }, {
    path: "/user/user_wallet",
    component: _2a6c52bc,
    name: "user-user_wallet"
  }, {
    path: "/user/after_sales/after_sale_details",
    component: _46e7110e,
    name: "user-after_sales-after_sale_details"
  }, {
    path: "/user/after_sales/apply_result",
    component: _a39ccce2,
    name: "user-after_sales-apply_result"
  }, {
    path: "/user/after_sales/apply_sale",
    component: _63c2098e,
    name: "user-after_sales-apply_sale"
  }, {
    path: "/user/evaluation/evaluate",
    component: _2f441141,
    name: "user-evaluation-evaluate"
  }, {
    path: "/user/order/detail",
    component: _ad189866,
    name: "user-order-detail"
  }, {
    path: "/goods_details/:id?",
    component: _75e8abdb,
    name: "goods_details-id"
  }, {
    path: "/goods_list/:type?",
    component: _6e7ea3ea,
    name: "goods_list-type"
  }, {
    path: "/",
    component: _0f6a21ae,
    name: "index"
  }],

  fallback: false
}

export function createRouter (ssrContext, config) {
  const base = (config._app && config._app.basePath) || routerOptions.base
  const router = new Router({ ...routerOptions, base  })

  // TODO: remove in Nuxt 3
  const originalPush = router.push
  router.push = function push (location, onComplete = emptyFn, onAbort) {
    return originalPush.call(this, location, onComplete, onAbort)
  }

  const resolve = router.resolve.bind(router)
  router.resolve = (to, current, append) => {
    if (typeof to === 'string') {
      to = normalizeURL(to)
    }
    return resolve(to, current, append)
  }

  return router
}
