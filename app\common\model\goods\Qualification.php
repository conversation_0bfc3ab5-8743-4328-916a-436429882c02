<?php

namespace app\common\model\goods;

use app\common\basics\Models;

/**
 * 资质管理模型
 * Class Qualification
 * @package app\common\model\goods
 */
class Qualification extends Models
{
    /**
     * 关联分类资质关联表
     */
    public function categoryQualifications()
    {
        return $this->hasMany(GoodsCategoryQualification::class, 'qualification_id', 'id');
    }

    /**
     * 关联分类表（通过中间表）
     */
    public function categories()
    {
        return $this->belongsToMany(GoodsCategory::class, GoodsCategoryQualification::class, 'category_id', 'qualification_id');
    }

    /**
     * 获取有效期描述
     */
    public function getValidDaysTextAttr($value, $data)
    {
        $validDays = isset($data['valid_days']) ? intval($data['valid_days']) : 0;
        if ($validDays == 0) {
            return '永久有效';
        }
        return $validDays . '天';
    }

    /**
     * 获取状态描述
     */
    public function getStatusTextAttr($value, $data)
    {
        $status = isset($data['status']) ? intval($data['status']) : 0;
        return $status == 1 ? '启用' : '禁用';
    }

    /**
     * 获取是否必传描述
     */
    public function getIsRequiredTextAttr($value, $data)
    {
        $isRequired = isset($data['is_required']) ? intval($data['is_required']) : 0;
        return $isRequired == 1 ? '必传' : '非必传';
    }
}
