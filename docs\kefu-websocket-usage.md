# 客服端WebSocket使用指南

## 概述

为了确保客服发送消息时包含正确的 `chat_type` 参数，我们创建了专门的客服端WebSocket管理器。

## 问题说明

之前客服发送消息时连接URL格式为：
```
wss://kefu.huohanghang.cn/?token=826ffd6db4a1328299d5e7373f12d00c&type=kefu&client=2&shop_id=86
```

虽然连接参数正确，但发送消息时可能缺少 `chat_type` 参数，导致后端无法正确区分聊天类型。

## 解决方案

### 1. 引入客服专用WebSocket管理器

```html
<!-- 在客服端页面中引入 -->
<script src="/static/js/kefu-websocket.js"></script>
```

### 2. 初始化连接

```javascript
// 客服端初始化
const kefuWS = new KefuWebSocket({
    url: 'wss://kefu.huohanghang.cn',
    token: 'your_kefu_token_here',
    shop_id: 86, // 商家ID
    client: 2,   // 客服端标识
    reconnectInterval: 5000,
    maxReconnectAttempts: 5,
    heartbeatInterval: 30000
});
```

### 3. 监听事件

```javascript
// 监听连接成功
kefuWS.on('connected', () => {
    console.log('客服WebSocket连接成功');
});

// 监听登录成功
kefuWS.on('login', (data) => {
    console.log('客服登录成功:', data);
});

// 监听聊天消息
kefuWS.on('message', (data) => {
    console.log('收到聊天消息:', data);
    // data.chat_type 会自动设置为 'kefu_chat'
    displayMessage(data);
});

// 监听错误
kefuWS.on('error', (error) => {
    console.error('WebSocket错误:', error);
});

// 监听连接断开
kefuWS.on('disconnected', () => {
    console.log('WebSocket连接断开');
});
```

### 4. 发送消息

```javascript
// 客服回复用户消息
kefuWS.sendMessage(userId, 'user', '您好，有什么可以帮您的？', 1);

// 发送图片消息
kefuWS.sendMessage(userId, 'user', 'image_url_here', 2);

// 发送语音消息
kefuWS.sendMessage(userId, 'user', 'voice_url_here', 4, 30); // 30秒语音

// 发送商品消息
kefuWS.sendMessage(userId, 'user', goodsId, 3);
```

### 5. 转接功能

```javascript
// 转接用户到其他客服
kefuWS.transferUser(userId, targetKefuId);

// 监听转接事件
kefuWS.on('transfer', (data) => {
    console.log('收到转接通知:', data);
});
```

## 消息类型说明

```javascript
const MessageTypes = {
    TEXT: 1,    // 文本消息
    IMAGE: 2,   // 图片消息
    GOODS: 3,   // 商品消息
    VOICE: 4,   // 语音消息
    VIDEO: 5,   // 视频消息
    ORDER: 6    // 订单消息
};
```

## 关键特性

### 1. 自动添加chat_type参数
所有通过 `sendMessage` 方法发送的消息都会自动添加 `chat_type: 'kefu_chat'` 参数。

### 2. 向后兼容
继续使用 `type=kefu` 连接参数，保持与现有系统的兼容性。

### 3. 自动重连
连接断开时会自动尝试重连，最多重连5次。

### 4. 消息队列
连接断开期间的消息会被缓存，连接恢复后自动发送。

### 5. 心跳机制
自动发送心跳包保持连接活跃。

## 完整示例

```javascript
// 客服聊天页面示例
class KefuChatPage {
    constructor() {
        this.kefuWS = null;
        this.currentUserId = null;
        this.init();
    }
    
    init() {
        this.initWebSocket();
        this.bindEvents();
    }
    
    initWebSocket() {
        this.kefuWS = new KefuWebSocket({
            url: 'wss://kefu.huohanghang.cn',
            token: this.getKefuToken(),
            shop_id: this.getShopId(),
            client: 2
        });
        
        // 监听消息
        this.kefuWS.on('message', (data) => {
            this.handleMessage(data);
        });
        
        // 监听连接状态
        this.kefuWS.on('connected', () => {
            this.updateConnectionStatus('已连接');
        });
        
        this.kefuWS.on('disconnected', () => {
            this.updateConnectionStatus('连接断开');
        });
    }
    
    handleMessage(data) {
        // 处理接收到的消息
        this.displayMessage(data);
        
        // 如果是用户发来的消息，可以自动回复
        if (data.from_type === 'user') {
            this.showUserMessage(data);
        }
    }
    
    sendReply(message) {
        if (this.currentUserId && message.trim()) {
            this.kefuWS.sendMessage(this.currentUserId, 'user', message, 1);
            this.displayMyMessage(message);
        }
    }
    
    // 其他方法...
    getKefuToken() {
        // 获取客服token
        return localStorage.getItem('kefu_token') || '';
    }
    
    getShopId() {
        // 获取商家ID
        return parseInt(localStorage.getItem('shop_id')) || 0;
    }
}

// 初始化客服聊天页面
const kefuChat = new KefuChatPage();
```

## 注意事项

1. **确保引入正确的脚本文件**：使用 `/static/js/kefu-websocket.js` 而不是通用的用户聊天WebSocket。

2. **正确设置参数**：确保 `token`、`shop_id`、`client` 参数正确。

3. **消息格式**：所有消息都会自动包含 `chat_type: 'kefu_chat'` 参数。

4. **错误处理**：监听 `error` 事件处理连接错误。

5. **资源清理**：页面卸载时调用 `kefuWS.close()` 关闭连接。

## 迁移指南

如果现有客服端使用的是其他WebSocket实现，可以按以下步骤迁移：

1. 引入新的 `kefu-websocket.js` 文件
2. 替换WebSocket初始化代码
3. 更新消息发送方法调用
4. 更新事件监听器
5. 测试功能是否正常

这样就能确保客服发送的所有消息都包含正确的 `chat_type` 参数了。
