<?php



namespace app\common\model;


use app\common\basics\Models;

/**
 * 管理员模型
 * Class Admin
 * <AUTHOR>
 * @package app\common\model
 */
class Admin extends Models
{

    /**
     * Notes: 获取器-格式化登录时间
     * @param $value
     * <AUTHOR> 16:30)
     * @return false|string
     */
    public function getLoginTimeAttr($value)
    {
        return empty($value) ?  '' : date('Y-m-d H:i:s', $value);
    }

}