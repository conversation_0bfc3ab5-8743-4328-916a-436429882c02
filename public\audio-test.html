<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>通知音效测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .audio-test {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #fafafa;
        }
        .audio-test h3 {
            margin: 0 0 10px 0;
            color: #555;
        }
        .audio-test p {
            margin: 5px 0;
            color: #666;
            font-size: 14px;
        }
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        button:hover {
            background: #45a049;
        }
        button.error { background: #f44336; }
        button.error:hover { background: #d32f2f; }
        button.warning { background: #ff9800; }
        button.warning:hover { background: #e68900; }
        button.info { background: #2196F3; }
        button.info:hover { background: #0b7dda; }
        button.success { background: #4CAF50; }
        button.success:hover { background: #45a049; }
        .status {
            margin-top: 20px;
            padding: 10px;
            border-radius: 5px;
            background: #e3f2fd;
            border-left: 4px solid #2196F3;
        }
        .volume-control {
            margin: 20px 0;
            text-align: center;
        }
        .volume-control input {
            width: 200px;
            margin: 0 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔊 通知音效测试</h1>
        
        <div class="volume-control">
            <label>音量控制: </label>
            <input type="range" id="volumeSlider" min="0" max="1" step="0.1" value="0.7">
            <span id="volumeValue">70%</span>
        </div>

        <div class="audio-test">
            <h3>🔵 系统通知音效</h3>
            <p>用于：系统维护、更新、一般系统消息</p>
            <p>特征：清脆、专业、中性</p>
            <button onclick="testAudio('system')">播放测试</button>
            <button onclick="testNotification('system')">测试通知</button>
        </div>

        <div class="audio-test">
            <h3>🔴 错误通知音效</h3>
            <p>用于：系统错误、操作失败、异常情况</p>
            <p>特征：警示、紧急、但不刺耳</p>
            <button class="error" onclick="testAudio('error')">播放测试</button>
            <button class="error" onclick="testNotification('error')">测试通知</button>
        </div>

        <div class="audio-test">
            <h3>🟡 警告通知音效</h3>
            <p>用于：资源不足、需要注意的情况</p>
            <p>特征：提醒、中等紧急程度</p>
            <button class="warning" onclick="testAudio('warning')">播放测试</button>
            <button class="warning" onclick="testNotification('warning')">测试通知</button>
        </div>

        <div class="audio-test">
            <h3>🔵 信息通知音效</h3>
            <p>用于：统计信息、一般提示、友好消息</p>
            <p>特征：轻柔、友好、不打扰</p>
            <button class="info" onclick="testAudio('info')">播放测试</button>
            <button class="info" onclick="testNotification('info')">测试通知</button>
        </div>

        <div class="audio-test">
            <h3>🟢 成功通知音效</h3>
            <p>用于：操作成功、任务完成、积极消息</p>
            <p>特征：愉悦、积极、令人舒适</p>
            <button class="success" onclick="testAudio('success')">播放测试</button>
            <button class="success" onclick="testNotification('success')">测试通知</button>
        </div>

        <div class="audio-test">
            <h3>⚪ 默认通知音效</h3>
            <p>用于：一般管理员通知、备用音效</p>
            <p>特征：中性、通用</p>
            <button onclick="testAudio('default')">播放测试</button>
            <button onclick="testNotification('default')">测试通知</button>
        </div>

        <div class="status" id="status">
            <strong>状态：</strong>准备测试音效
        </div>

        <div style="margin-top: 30px; padding: 20px; background: #f0f0f0; border-radius: 5px;">
            <h3>📝 使用说明</h3>
            <ul>
                <li><strong>播放测试</strong>：直接播放音效文件</li>
                <li><strong>测试通知</strong>：模拟完整的通知流程（音效+弹窗）</li>
                <li>调整音量滑块来测试不同音量效果</li>
                <li>如果听不到声音，请检查浏览器是否允许自动播放</li>
                <li>建议在安静环境中测试音效质量</li>
            </ul>
        </div>
    </div>

    <script>
        // 音频文件配置
        const audioFiles = {
            system: '/static/audio/system_notification.mp3',
            error: '/static/audio/error_notification.mp3',
            warning: '/static/audio/warning_notification.mp3',
            info: '/static/audio/info_notification.mp3',
            success: '/static/audio/success_notification.mp3',
            default: '/static/audio/default_notification.mp3'
        };

        // 音量控制
        const volumeSlider = document.getElementById('volumeSlider');
        const volumeValue = document.getElementById('volumeValue');
        const status = document.getElementById('status');

        volumeSlider.addEventListener('input', function() {
            const volume = Math.round(this.value * 100);
            volumeValue.textContent = volume + '%';
        });

        // 更新状态
        function updateStatus(message, type = 'info') {
            status.innerHTML = `<strong>状态：</strong>${message}`;
            status.className = 'status ' + type;
        }

        // 测试音频播放
        function testAudio(type) {
            const audioFile = audioFiles[type];
            if (!audioFile) {
                updateStatus(`音频文件未配置: ${type}`, 'error');
                return;
            }

            updateStatus(`正在播放 ${type} 音效...`);

            try {
                const audio = new Audio(audioFile);
                audio.volume = volumeSlider.value;
                
                audio.onloadstart = () => updateStatus(`加载音频: ${type}`);
                audio.oncanplay = () => updateStatus(`准备播放: ${type}`);
                audio.onplay = () => updateStatus(`正在播放: ${type}`, 'success');
                audio.onended = () => updateStatus(`播放完成: ${type}`, 'success');
                audio.onerror = (e) => updateStatus(`播放失败: ${type} - ${e.message}`, 'error');

                const playPromise = audio.play();
                
                if (playPromise !== undefined) {
                    playPromise.catch(error => {
                        updateStatus(`播放被阻止: ${error.message}`, 'warning');
                        console.warn('音频播放被浏览器阻止，这是正常的安全策略');
                    });
                }
            } catch (error) {
                updateStatus(`音频播放异常: ${error.message}`, 'error');
            }
        }

        // 测试完整通知
        function testNotification(type) {
            // 先播放音效
            testAudio(type);

            // 然后显示通知弹窗
            const messages = {
                system: { title: '系统通知', content: '系统运行正常，所有服务已启动' },
                error: { title: '错误警报', content: '检测到系统异常，请立即处理！' },
                warning: { title: '警告提醒', content: '磁盘空间不足，建议清理无用文件' },
                info: { title: '信息提示', content: '今日访问量已达到新高，系统表现良好' },
                success: { title: '操作成功', content: '数据备份已完成，系统运行稳定' },
                default: { title: '管理员通知', content: '您有一条新的消息需要查看' }
            };

            const message = messages[type] || messages.default;
            
            // 延迟显示弹窗，让音效先播放
            setTimeout(() => {
                if (confirm(`${message.title}\n\n${message.content}\n\n点击确定关闭通知`)) {
                    updateStatus(`通知已确认: ${type}`, 'success');
                }
            }, 500);
        }

        // 页面加载完成后的提示
        document.addEventListener('DOMContentLoaded', function() {
            updateStatus('页面加载完成，可以开始测试音效');
            
            // 检查音频文件是否存在
            setTimeout(checkAudioFiles, 1000);
        });

        // 检查音频文件是否存在
        function checkAudioFiles() {
            let checkedCount = 0;
            let existingFiles = [];
            let missingFiles = [];

            Object.keys(audioFiles).forEach(type => {
                const audio = new Audio();
                audio.oncanplay = () => {
                    existingFiles.push(type);
                    checkedCount++;
                    if (checkedCount === Object.keys(audioFiles).length) {
                        showFileStatus(existingFiles, missingFiles);
                    }
                };
                audio.onerror = () => {
                    missingFiles.push(type);
                    checkedCount++;
                    if (checkedCount === Object.keys(audioFiles).length) {
                        showFileStatus(existingFiles, missingFiles);
                    }
                };
                audio.src = audioFiles[type];
            });
        }

        function showFileStatus(existing, missing) {
            if (missing.length === 0) {
                updateStatus(`所有音频文件就绪 (${existing.length}个)`, 'success');
            } else {
                updateStatus(`缺少音频文件: ${missing.join(', ')} | 可用: ${existing.join(', ')}`, 'warning');
            }
        }
    </script>
</body>
</html>