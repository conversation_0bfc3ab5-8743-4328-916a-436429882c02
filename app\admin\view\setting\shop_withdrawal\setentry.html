{layout name="layout1" /}

<style>
    .layui-form-label { width: 100px; }
</style>

<div class="wrapper" >
    <div class="layui-card">
        <div class="layui-card-body">
            <div class="layui-collapse like-layui-collapse" style="border:1px dashed #c4c4c4">
                <div class="layui-colla-item">
                    <h2 class="layui-colla-title like-layui-colla-title" style="background-color: #fff">操作提示</h2>
                    <div class="layui-colla-content layui-show">
                        <p>商家入驻费用设置。</p>
                    </div>
                </div>
            </div>

            <div class="layui-form" style="margin-top: 20px;">


                <div class="layui-form-item">
                    <label class="layui-form-label">商家会员年费：</label>
                    <div class="layui-input-inline">
                        <input type="text" name="entry_fee"
                               onkeyup="value=value.replace(/^\D*(\d*(?:\.\d{0,2})?).*$/g, '$1')"
                               value="{$detail.entry_fee}" autocomplete="off" class="layui-input">
                        <div class="layui-form-mid layui-word-aux" style="white-space:nowrap;">商家入驻费用，一年一缴。</div>
                    </div>
                    <div class="layui-form-mid">元</div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label">实力厂商年费：</label>
                    <div class="layui-input-inline">
                        <input type="text" name="ins_fee"
                               onkeyup="value=value.replace(/^\D*(\d*(?:\.\d{0,2})?).*$/g, '$1')"
                               value="{$detail.ins_fee}" autocomplete="off" class="layui-input">
                        <div class="layui-form-mid layui-word-aux" style="white-space:nowrap;">(单独购买的费用) 验厂验商验品牌，一年一缴。</div>
                    </div>
                    <div class="layui-form-mid">元</div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">组合年费：</label>
                    <div class="layui-input-inline">
                        <input type="text" name="zins_fee"
                               onkeyup="value=value.replace(/^\D*(\d*(?:\.\d{0,2})?).*$/g, '$1')"
                               value="{$detail.zins_fee}" autocomplete="off" class="layui-input">
                        <div class="layui-form-mid layui-word-aux" style="white-space:nowrap;">(组合购买的费用) 商家会员加实力厂商，一年一缴。</div>
                    </div>
                    <div class="layui-form-mid">元</div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">入驻保证金：</label>
                    <div class="layui-input-inline">
                        <input type="text" name="depositAmount"
                               onkeyup="value=value.replace(/^\D*(\d*(?:\.\d{0,2})?).*$/g, '$1')"
                               value="{$detail.depositAmount}" autocomplete="off" class="layui-input">
                        <div class="layui-form-mid layui-word-aux" style="white-space:nowrap;">商家入驻所缴纳的保证金。数值为0时不需要缴纳</div>
                    </div>
                    <div class="layui-form-mid">元</div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">入驻集采购商家保证金：</label>
                    <div class="layui-input-inline">
                        <input type="text" name="procurementDeposit"
                               onkeyup="value=value.replace(/^\D*(\d*(?:\.\d{0,2})?).*$/g, '$1')"
                               value="{$detail.procurementDeposit}" autocomplete="off" class="layui-input">
                        <div class="layui-form-mid layui-word-aux" style="white-space:nowrap;">商家入驻集采购所缴纳的保证金。数值为0时不需要缴纳</div>
                    </div>
                    <div class="layui-form-mid">元</div>
                </div>
                <div class="layui-form-item" >
                    <lable class="layui-form-label" >提前通知商家：</lable>
                    <div class="layui-inline">
                        <input type="number" min="0" name="msg_day" value="{$detail.msg_day}" class="layui-input" step="0.01"  min="0" onkeyup="this.value= this.value.match(/\d+(\.\d{0,2})?/) ? this.value.match(/\d+(\.\d{0,2})?/)[0] : ''">
                        <div class="layui-form-mid layui-word-aux" style="white-space:nowrap;">提前多少天通知商家缴费</div>
                    </div>
                    <div class="layui-inline">
                        天
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">0元入驻权益图：</label>
                    <div class="layui-input-block" id="freeImageContainer">
                        <div class="like-upload-image">
                            <input type="hidden" name="free_shop_image" value="{$detail.free_shop_image|default=''}">
                            {if !empty($detail.free_shop_image)}
                            <div class="upload-image-div">
                                <img src="{$detail.free_shop_image}" alt="">
                                <div class="del-upload-btn">x</div>
                            </div>
                            {/if}
                            <div class="upload-image-elem"><a class="add-upload-image" id="freeImage"> + 添加图片</a></div>
                        </div>
                        <div class="layui-form-mid layui-word-aux" style="white-space:nowrap;">0元入驻商家权益展示图片</div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">普通商家权益图：</label>
                    <div class="layui-input-block" id="normalImageContainer">
                        <div class="like-upload-image">
                            <input type="hidden" name="normal_shop_image" value="{$detail.normal_shop_image|default=''}">
                            {if !empty($detail.normal_shop_image)}
                            <div class="upload-image-div">
                                <img src="{$detail.normal_shop_image}" alt="">
                                <div class="del-upload-btn">x</div>
                            </div>
                            {/if}
                            <div class="upload-image-elem"><a class="add-upload-image" id="normalImage"> + 添加图片</a></div>
                        </div>
                        <div class="layui-form-mid layui-word-aux" style="white-space:nowrap;">商家会员权益展示图片</div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">实力厂商权益图：</label>
                    <div class="layui-input-block" id="seniorImageContainer">
                        <div class="like-upload-image">
                            <input type="hidden" name="senior_shop_image" value="{$detail.senior_shop_image|default=''}">
                            {if !empty($detail.senior_shop_image)}
                            <div class="upload-image-div">
                                <img src="{$detail.senior_shop_image}" alt="">
                                <div class="del-upload-btn">x</div>
                            </div>
                            {/if}
                            <div class="upload-image-elem"><a class="add-upload-image" id="seniorImage"> + 添加图片</a></div>
                        </div>
                        <div class="layui-form-mid layui-word-aux" style="white-space:nowrap;">实力厂商权益展示图片</div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">保证金退款警告图：</label>
                    <div class="layui-input-block" id="refundWarningImageContainer">
                        <div class="like-upload-image">
                            <input type="hidden" name="refund_warning_image" value="{$detail.refund_warning_image|default=''}">
                            {if !empty($detail.refund_warning_image)}
                            <div class="upload-image-div">
                                <img src="{$detail.refund_warning_image}" alt="">
                                <div class="del-upload-btn">x</div>
                            </div>
                            {/if}
                            <div class="upload-image-elem"><a class="add-upload-image" id="refundWarningImage"> + 添加图片</a></div>
                        </div>
                        <div class="layui-form-mid layui-word-aux" style="white-space:nowrap;">商家保证金退款时显示的警告提示图片</div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">保证金公示期：</label>
                    <div class="layui-input-inline">
                        <input type="number" min="0" name="deposit_publicity_days" value="{$detail.deposit_publicity_days|default=7}" class="layui-input" step="1" min="0" onkeyup="this.value= this.value.match(/\d+/) ? this.value.match(/\d+/)[0] : ''">
                        <div class="layui-form-mid layui-word-aux" style="white-space:nowrap;">商家保证金退款的公示期天数</div>
                    </div>
                    <div class="layui-form-mid">天</div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-input-block">
                        <button class="layui-btn layui-btn-normal" lay-submit lay-filter="addForm">确定</button>
                    </div>
                </div>
            </div>

        </div>
    </div>
</div>

<script>
    layui.use(["form"], function(){
        var form = layui.form;
        var $ = layui.$;

        // 图片上传
        like.delUpload();

        // 0元入驻权益图
        $(document).on("click", "#freeImage", function () {
            like.imageUpload({
                limit: 1,
                field: "free_shop_image",
                that: $(this)
            });
        });

        // 普通商家权益图
        $(document).on("click", "#normalImage", function () {
            like.imageUpload({
                limit: 1,
                field: "normal_shop_image",
                that: $(this)
            });
        });

        // 高级商家权益图
        $(document).on("click", "#seniorImage", function () {
            like.imageUpload({
                limit: 1,
                field: "senior_shop_image",
                that: $(this)
            });
        });

        // 保证金退款警告图
        $(document).on("click", "#refundWarningImage", function () {
            like.imageUpload({
                limit: 1,
                field: "refund_warning_image",
                that: $(this)
            });
        });

        form.on('submit(addForm)', function(data) {
            var min = data.field.min_withdrawal_money;
            var max = data.field.max_withdrawal_money;

            if(parseFloat(min) > parseFloat(max)) {
                layui.layer.msg('最低提现金额不可大于最高提现金额', {
                    offset: '15px'
                    , icon: 2
                    , time: 1000
                });
                return false;
            }

            like.ajax({
                url:'{:url("setting.ShopWithdrawal/setshopentry")}',
                data: data.field,
                type:"post",
                success:function(res) {
                    if(res.code === 1) {
                        layui.layer.msg(res.msg, {
                            offset: '15px'
                            , icon: 1
                            , time: 1000
                        });
                    }
                }
            });
        });

    });
</script>