<?php



namespace app\shop\logic\goods;


use app\common\basics\Logic;
use app\common\model\goods\Supplier;


/**
 * 供应商
 * Class SupplierLogic
 * @package app\admin\logic
 */
class SupplierLogic extends Logic
{

    /**
     * Notes: 列表
     * @param $shop_id
     * @param $get
     * <AUTHOR> 10:53)
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function lists($shop_id, $get)
    {
        $where[] = ['del', '=', 0];
        $where[] = ['shop_id', '=', $shop_id];
        if(isset($get['keyword']) && $get['keyword']){
            $where[] = ['name','like','%'.$get['keyword'].'%'];
        }

        $result = Supplier::where($where)
            ->paginate([
                'list_rows'=> $get['limit'],
                'page'=> $get['page']
            ]);

        return ['count' => $result->total(), 'lists' => $result->getCollection()];
    }


    /**
     * Notes: 添加
     * @param $post
     * <AUTHOR> 10:54)
     * @return Supplier|\think\Model
     */
    public static function add($shop_id, $post)
    {
        return Supplier::create([
            'shop_id'  => $shop_id,
            'name'     => $post['name'],
            'contact'  => $post['contact'],
            'mobile'   => $post['mobile'],
            'address'  => $post['address'],
            'remark'   => $post['remark'] ?? '',
        ]);
    }


    /**
     * Notes: 编辑
     * @param $post
     * <AUTHOR> 10:54)
     * @return Supplier
     */
    public static function edit($shop_id, $post)
    {
        return Supplier::update([
            'name'     => $post['name'],
            'contact'  => $post['contact'],
            'mobile'   => $post['mobile'],
            'address'  => $post['address'],
            'remark'   => $post['remark'] ?? '',
        ], ['id' => $post['id'], 'shop_id' => $shop_id]);
    }


    /**
     * Notes: 删除
     * @param $id
     * <AUTHOR> 10:54)
     * @return Supplier
     */
    public static function del($shop_id, $id)
    {
        return Supplier::update(['del' => 1], ['id' => $id, 'shop_id' => $shop_id]);
    }

}