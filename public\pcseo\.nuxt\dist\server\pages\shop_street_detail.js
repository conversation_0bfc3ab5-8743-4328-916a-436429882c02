exports.ids = [37,11,14,17];
exports.modules = {

/***/ 136:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(139);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(4).default
module.exports.__inject__ = function (context) {
  add("3181fc86", content, true, context)
};

/***/ }),

/***/ 137:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/price-formate.vue?vue&type=template&id=0c4d5c85&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('span',{class:(_vm.lineThrough ? 'line-through' : '') + 'price-format',style:({ color: _vm.color, 'font-weight': _vm.weight })},[_vm._ssrNode(((_vm.showSubscript)?("<span"+(_vm._ssrStyle(null,{
            'font-size': _vm.subscriptSize + 'px',
            'margin-right': '1px',
        }, null))+">¥</span>"):"<!---->")+" <span"+(_vm._ssrStyle(null,{ 'font-size': _vm.firstSize + 'px', 'margin-right': '1px' }, null))+">"+_vm._ssrEscape(_vm._s(_vm.priceSlice.first))+"</span> "+((_vm.priceSlice.second)?("<span"+(_vm._ssrStyle(null,{ 'font-size': _vm.secondSize + 'px' }, null))+">"+_vm._ssrEscape("."+_vm._s(_vm.priceSlice.second))+"</span>"):"<!---->"))])}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/price-formate.vue?vue&type=template&id=0c4d5c85&

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/price-formate.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
/* harmony default export */ var price_formatevue_type_script_lang_js_ = ({
  data() {
    return {
      priceSlice: {}
    };
  },

  components: {},
  props: {
    firstSize: {
      type: Number,
      default: 14
    },
    secondSize: {
      type: Number,
      default: 14
    },
    color: {
      type: String
    },
    weight: {
      type: [String, Number],
      default: 400
    },
    price: {
      type: [String, Number],
      default: ''
    },
    showSubscript: {
      type: Boolean,
      default: true
    },
    subscriptSize: {
      type: Number,
      default: 14
    },
    lineThrough: {
      type: Boolean,
      default: false
    }
  },

  created() {
    this.priceFormat();
  },

  watch: {
    price(val) {
      this.priceFormat();
    }

  },
  methods: {
    priceFormat() {
      let {
        price
      } = this;
      let priceSlice = {};

      if (price !== null) {
        price = parseFloat(price);
        price = String(price).split('.');
        priceSlice.first = price[0];
        priceSlice.second = price[1];
        this.priceSlice = priceSlice;
      }
    }

  }
});
// CONCATENATED MODULE: ./components/price-formate.vue?vue&type=script&lang=js&
 /* harmony default export */ var components_price_formatevue_type_script_lang_js_ = (price_formatevue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(1);

// CONCATENATED MODULE: ./components/price-formate.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(138)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  components_price_formatevue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  null,
  "7ae24710"
  
)

/* harmony default export */ var price_formate = __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 138:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_3_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_3_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_3_oneOf_1_2_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_price_formate_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(136);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_3_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_3_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_3_oneOf_1_2_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_price_formate_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_3_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_3_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_3_oneOf_1_2_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_price_formate_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_3_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_3_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_3_oneOf_1_2_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_price_formate_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_3_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_3_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_3_oneOf_1_2_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_price_formate_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 139:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(3);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".price-format{display:flex;align-items:baseline}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 140:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(142);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(4).default
module.exports.__inject__ = function (context) {
  add("12a18d22", content, true, context)
};

/***/ }),

/***/ 141:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_sass_resources_loader_lib_loader_js_ref_7_oneOf_1_4_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_null_data_vue_vue_type_style_index_0_id_93598fb0_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(140);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_sass_resources_loader_lib_loader_js_ref_7_oneOf_1_4_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_null_data_vue_vue_type_style_index_0_id_93598fb0_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_sass_resources_loader_lib_loader_js_ref_7_oneOf_1_4_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_null_data_vue_vue_type_style_index_0_id_93598fb0_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_sass_resources_loader_lib_loader_js_ref_7_oneOf_1_4_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_null_data_vue_vue_type_style_index_0_id_93598fb0_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_sass_resources_loader_lib_loader_js_ref_7_oneOf_1_4_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_null_data_vue_vue_type_style_index_0_id_93598fb0_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 142:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(3);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".null-data[data-v-93598fb0]{padding:100px}.null-data .img-null[data-v-93598fb0]{width:150px;height:150px}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 143:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/null-data.vue?vue&type=template&id=93598fb0&scoped=true&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:"bg-white flex-col col-center null-data"},[_vm._ssrNode("<img"+(_vm._ssrAttr("src",_vm.img))+" alt class=\"img-null\""+(_vm._ssrStyle(null,_vm.imgStyle, null))+" data-v-93598fb0> <div class=\"muted mt8\" data-v-93598fb0>"+_vm._ssrEscape(_vm._s(_vm.text))+"</div>")])}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/null-data.vue?vue&type=template&id=93598fb0&scoped=true&

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/null-data.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
/* harmony default export */ var null_datavue_type_script_lang_js_ = ({
  components: {},
  props: {
    img: {
      type: String
    },
    text: {
      type: String,
      default: '暂无数据'
    },
    imgStyle: {
      type: String,
      default: ''
    }
  },
  methods: {}
});
// CONCATENATED MODULE: ./components/null-data.vue?vue&type=script&lang=js&
 /* harmony default export */ var components_null_datavue_type_script_lang_js_ = (null_datavue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(1);

// CONCATENATED MODULE: ./components/null-data.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(141)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  components_null_datavue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  "93598fb0",
  "728f99de"
  
)

/* harmony default export */ var null_data = __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 145:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "b", function() { return trottle; });
/* unused harmony export strToParams */
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "a", function() { return paramsToStr; });
/* unused harmony export copyClipboard */
//节流
const trottle = (func, time = 1000, context) => {
  let previous = new Date(0).getTime();
  return function (...args) {
    let now = new Date().getTime();

    if (now - previous > time) {
      func.apply(context, args);
      previous = now;
    }
  };
}; //获取url后的参数  以对象返回

function strToParams(str) {
  var newparams = {};

  for (let item of str.split('&')) {
    newparams[item.split('=')[0]] = item.split('=')[1];
  }

  return newparams;
} //对象参数转为以？&拼接的字符

function paramsToStr(params) {
  let p = '';

  if (typeof params == 'object') {
    p = '?';

    for (let props in params) {
      p += `${props}=${params[props]}&`;
    }

    p = p.slice(0, -1);
  }

  return p;
}
/**
 * @description 复制到剪切板
 * @param value { String } 复制内容
 * @return { Promise } resolve | reject
 */

const copyClipboard = value => {
  const elInput = document.createElement('input');
  elInput.setAttribute('value', value);
  document.body.appendChild(elInput);
  elInput.select();

  try {
    if (document.execCommand('copy')) return Promise.resolve();else throw new Error();
  } catch (err) {
    return Promise.reject(err);
  } finally {
    document.body.removeChild(elInput);
  }
};

/***/ }),

/***/ 146:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(151);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(4).default
module.exports.__inject__ = function (context) {
  add("1469a4e1", content, true, context)
};

/***/ }),

/***/ 150:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_sass_resources_loader_lib_loader_js_ref_7_oneOf_1_4_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_goods_list_vue_vue_type_style_index_0_id_060944d1_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(146);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_sass_resources_loader_lib_loader_js_ref_7_oneOf_1_4_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_goods_list_vue_vue_type_style_index_0_id_060944d1_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_sass_resources_loader_lib_loader_js_ref_7_oneOf_1_4_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_goods_list_vue_vue_type_style_index_0_id_060944d1_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_sass_resources_loader_lib_loader_js_ref_7_oneOf_1_4_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_goods_list_vue_vue_type_style_index_0_id_060944d1_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_sass_resources_loader_lib_loader_js_ref_7_oneOf_1_4_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_goods_list_vue_vue_type_style_index_0_id_060944d1_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 151:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(3);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".goods-list[data-v-060944d1]{align-items:stretch}.goods-list .goods-item[data-v-060944d1]{display:block;box-sizing:border-box;width:224px;height:310px;margin-bottom:16px;padding:12px 12px 16px;border-radius:4px;transition:all .2s}.goods-list .goods-item[data-v-060944d1]:hover{transform:translateY(-8px);box-shadow:0 0 6px rgba(0,0,0,.1)}.goods-list .goods-item .goods-img[data-v-060944d1]{width:200px;height:200px}.goods-list .goods-item .name[data-v-060944d1]{margin-bottom:10px;height:40px;line-height:20px}.goods-list .goods-item .seckill .btn[data-v-060944d1]{padding:4px 12px;border-radius:4px;border:1px solid transparent}.goods-list .goods-item .seckill .btn.not-start[data-v-060944d1]{border-color:#ff2c3c;color:#ff2c3c;background-color:transparent}.goods-list .goods-item .seckill .btn.end[data-v-060944d1]{background-color:#e5e5e5;color:#fff}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 154:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/goods-list.vue?vue&type=template&id=060944d1&scoped=true&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:"goods-list flex flex-wrap"},_vm._l((_vm.list),function(item,index){return _c('nuxt-link',{key:index,staticClass:"goods-item bg-white",style:({ marginRight: (index + 1) % _vm.num == 0 ? 0 : '14px' }),attrs:{"to":("/goods_details/" + (item.id||item.goods_id))}},[_c('el-image',{staticClass:"goods-img",attrs:{"lazy":"","src":item.image||item.goods_image,"alt":""}}),_vm._v(" "),_c('div',{staticClass:"name line-2"},[_vm._v(_vm._s(item.name||item.goods_name))]),_vm._v(" "),(_vm.type == 'seckill')?_c('div',{staticClass:"seckill flex row-between"},[_c('div',{staticClass:"primary flex"},[_vm._v("\n                秒杀价\n                "),_c('price-formate',{attrs:{"price":item.seckill_price,"first-size":18}})],1),_vm._v(" "),_c('div',{class:['btn bg-primary white', {'not-start' : _vm.status == 0, end: _vm.status == 2}]},[_vm._v(_vm._s(_vm.getSeckillText)+"\n            ")])]):_c('div',{staticClass:"flex row-between flex-wrap"},[_c('div',{staticClass:"price flex col-baseline"},[_c('div',{staticClass:"primary m-r-8"},[_c('price-formate',{attrs:{"price":item.min_price || item.price,"first-size":16}})],1),_vm._v(" "),_c('div',{staticClass:"muted sm line-through"},[_c('price-formate',{attrs:{"price":item.market_price}})],1)]),_vm._v(" "),_c('div',{staticClass:"muted xs"},[_vm._v(_vm._s(item.sales_total || item.sales_sum || 0)+"人购买")])])],1)}),1)}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/goods-list.vue?vue&type=template&id=060944d1&scoped=true&

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/goods-list.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
/* harmony default export */ var goods_listvue_type_script_lang_js_ = ({
  props: {
    list: {
      type: Array,
      default: () => []
    },
    num: {
      type: Number,
      default: 5
    },
    type: {
      type: String
    },
    status: {
      type: Number
    }
  },
  watch: {
    list: {
      immediate: true,
      handler: function (val) {}
    }
  },
  computed: {
    getSeckillText() {
      switch (this.status) {
        case 0:
          return "未开始";

        case 1:
          return "立即抢购";

        case 2:
          return "已结束";
      }
    }

  }
});
// CONCATENATED MODULE: ./components/goods-list.vue?vue&type=script&lang=js&
 /* harmony default export */ var components_goods_listvue_type_script_lang_js_ = (goods_listvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(1);

// CONCATENATED MODULE: ./components/goods-list.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(150)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  components_goods_listvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  "060944d1",
  "606a8712"
  
)

/* harmony default export */ var goods_list = __webpack_exports__["default"] = (component.exports);

/* nuxt-component-imports */
installComponents(component, {PriceFormate: __webpack_require__(137).default})


/***/ }),

/***/ 156:
/***/ (function(module, exports, __webpack_require__) {

module.exports = __webpack_require__.p + "img/goods_null.38f1689.png";

/***/ }),

/***/ 169:
/***/ (function(module, exports, __webpack_require__) {

module.exports = __webpack_require__.p + "img/coupons_img_receive.d691393.png";

/***/ }),

/***/ 170:
/***/ (function(module, exports, __webpack_require__) {

module.exports = __webpack_require__.p + "img/bg_coupon_s.3f57cfd.png";

/***/ }),

/***/ 171:
/***/ (function(module, exports, __webpack_require__) {

module.exports = __webpack_require__.p + "img/bg_coupon.b22691e.png";

/***/ }),

/***/ 222:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(277);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(4).default
module.exports.__inject__ = function (context) {
  add("54772eb0", content, true, context)
};

/***/ }),

/***/ 276:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_sass_resources_loader_lib_loader_js_ref_7_oneOf_1_4_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_shop_street_detail_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(222);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_sass_resources_loader_lib_loader_js_ref_7_oneOf_1_4_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_shop_street_detail_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_sass_resources_loader_lib_loader_js_ref_7_oneOf_1_4_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_shop_street_detail_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_sass_resources_loader_lib_loader_js_ref_7_oneOf_1_4_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_shop_street_detail_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_sass_resources_loader_lib_loader_js_ref_7_oneOf_1_4_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_shop_street_detail_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 277:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(3);
var ___CSS_LOADER_GET_URL_IMPORT___ = __webpack_require__(34);
var ___CSS_LOADER_URL_IMPORT_0___ = __webpack_require__(170);
var ___CSS_LOADER_URL_IMPORT_1___ = __webpack_require__(171);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
var ___CSS_LOADER_URL_REPLACEMENT_0___ = ___CSS_LOADER_GET_URL_IMPORT___(___CSS_LOADER_URL_IMPORT_0___);
var ___CSS_LOADER_URL_REPLACEMENT_1___ = ___CSS_LOADER_GET_URL_IMPORT___(___CSS_LOADER_URL_IMPORT_1___);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".shop{width:210px;padding:15px}.shop .logo-img{width:62px;height:62px;border-radius:50%;overflow:hidden}.shop .el-rate__icon{font-size:16px}.h-item{width:82px;height:24px;margin-right:30px;cursor:pointer}.h-item-x{border-radius:100px;background-color:#ff2c3c;color:#fff}.search{width:240px}.search ::v-deep .el-input{width:240px;border-radius:10px}.shop-details{margin-top:10px}.shop-details .left .l-border{padding-bottom:27px;border-bottom:1px solid #eee;margin-bottom:27px}.shop-details .left .desc{color:#101010;font-size:12px}.shop-details .left .desc-b{color:#fff;font-size:12px}.shop-details .left .desc-n{color:#fff;font-size:18px;color:#101010;font-size:14px}.shop-details .left .left-btn{width:82px;height:29px;border-radius:4px;border:1px solid #bbb}.shop-details .left .left-shop{background-color:#fff;padding:20px 15px;width:210px;height:364px}.shop-details .left .r-color-h{background-color:#a4adb3;color:#fff}.shop-details .left .l-tips{padding:1px 2px}.shop-details .left .l-fen{width:210px;height:44px;line-height:20px;color:#101010;font-size:14px;text-align:center;cursor:pointer}.shop-details .left .l-fen-select{color:#ff2c3c}.shop-details .right{width:961px}.shop-details .right .coupon-list{background-color:#fff;padding:20px 0;margin:0 20px;border-bottom:1px solid #eee}.shop-details .right .coupon-list .coupons-more{cursor:pointer}.shop-details .right .coupon-list .swiper-item-c{width:760px;flex-wrap:nowrap;overflow:hidden}.shop-details .right .coupon-list .swiper-item-zk{width:770px;flex-wrap:wrap}.shop-details .right .shop-list{background-color:#fff;height:360px;padding:10px 20px 0}.shop-details .right .shop-list .shop-item{width:200px;height:298px;background-color:#fff;margin-right:12px}.shop-details .right .shop-list .shop-item .name{color:#101010;font-size:14px;text-align:left;margin-bottom:18px}.shop-details .sort{padding:16px 16px 0}.shop-details .sort .sort-name .item{margin-right:30px;cursor:pointer}.shop-details .sort .sort-name .item.active{color:#ff2c3c}.shop-details .swiper-item{width:672px}.shop-details .item{margin-bottom:20px;margin-right:16px;position:relative;cursor:pointer}.shop-details .item .coupon-button{background-color:#f2f2f2;width:240px;height:30px;padding:0 8px}.shop-details .item .info{padding:0 10px;background:url(" + ___CSS_LOADER_URL_REPLACEMENT_0___ + ") no-repeat;width:240px;height:80px;background-size:100%}.shop-details .item .info.gray{background-image:url(" + ___CSS_LOADER_URL_REPLACEMENT_1___ + ")}.shop-details .item .info .info-hd{overflow:hidden}.shop-details .item .tips{position:relative;background-color:#f2f2f2;height:30px;padding:0 8px}.shop-details .item .tips .tips-con{width:100%;left:0;background-color:#f2f2f2;position:absolute;top:30px;padding:10px;z-index:99}.shop-details .item .receice{position:absolute;top:0;right:0;width:58px;height:45px}.shop-details .item .choose{position:absolute;top:0;right:0;background-color:#ffe72c;color:#ff2c3c;padding:1px 5px}.shop-details .more{position:absolute;bottom:20px;cursor:pointer;right:30px}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 346:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/shop_street_detail.vue?vue&type=template&id=1bbb4a36&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{},[_vm._ssrNode("<div class=\"bg-white\">","</div>",[(_vm.shopInfo.banner)?_vm._ssrNode("<div class=\"flex flex-1 row-center col-center\" style=\"width: 100%; height: 150px;\">","</div>",[_c('el-image',{staticStyle:{"height":"100%","width":"100%","max-width":"1920px"},attrs:{"src":_vm.shopInfo.banner,"fit":"cover"}})],1):_vm._e(),_vm._ssrNode(" "),_vm._ssrNode("<div class=\"wrapper1180 flex flex-1 col-center row-between\" style=\"height: 40px\">","</div>",[_vm._ssrNode("<div"+(_vm._ssrClass("h-item flex row-center",_vm.xuanIndex =='' ? 'h-item-x':''))+">\n                店铺首页\n            </div> "),_vm._ssrNode("<div class=\"flex row-left flex-1\">","</div>",[_vm._ssrNode("<div"+(_vm._ssrClass("h-item flex row-center",_vm.xuanIndex =='all' ? 'h-item-x':''))+">\n                    全部商品\n                </div> "),_c('swiper',{ref:"mySwiper",staticClass:"swiper flex row-left",staticStyle:{"width":"672px","display":"flex","justify-content":"flex-start","margin":"0"},attrs:{"options":_vm.swiperOptions}},_vm._l((_vm.goodsClassListGroup),function(itemd,indexd){return _c('swiper-slide',{key:indexd,staticClass:"swiper-item flex row-left"},[_c('div',{staticClass:"flex"},_vm._l((itemd),function(item,index){return _c('div',{key:index},[_c('div',{staticClass:"h-item flex row-center",class:_vm.xuanIndex == item.id ? 'h-item-x':'',on:{"click":function($event){return _vm.changeXuan(item.id)}}},[_vm._v("\n                                    "+_vm._s(item.name)+"\n                                ")])])}),0)])}),1)],2),_vm._ssrNode(" "),_vm._ssrNode("<div class=\"search\">","</div>",[_c('el-input',{attrs:{"placeholder":"店铺搜索","size":"mini"},on:{"change":_vm.search},model:{value:(_vm.keyword),callback:function ($$v) {_vm.keyword=$$v},expression:"keyword"}})],1)],2)],2),_vm._ssrNode(" "),_vm._ssrNode("<div class=\"shop-details flex col-top wrapper1180 flex-1\">","</div>",[_vm._ssrNode("<div class=\"left\">","</div>",[_vm._ssrNode("<div class=\"shop bg-white\">","</div>",[_vm._ssrNode("<div class=\"shop-logo flex-col col-center\">","</div>",[_c('el-image',{staticClass:"logo-img",attrs:{"src":_vm.shopInfo.logo}}),_vm._ssrNode(" "),_vm._ssrNode("<div class=\"m-t-10\">","</div>",[(_vm.shopInfo.type == 1)?_c('el-tag',{attrs:{"size":"mini"}},[_vm._v("自营")]):_vm._e(),_vm._ssrNode(" <span class=\"weight-500\">"+_vm._ssrEscape(_vm._s(_vm.shopInfo.name))+"</span>")],2),_vm._ssrNode(" <div class=\"xs muted m-t-10 line-5\">"+_vm._ssrEscape("\n                        "+_vm._s(_vm.shopInfo.intro)+"\n                    ")+"</div>")],2),_vm._ssrNode(" <div class=\"flex m-t-30\"><div class=\"flex-1 text-center\"><div class=\"xxl m-b-10\">"+_vm._ssrEscape(_vm._s(_vm.shopInfo.on_sale_count))+"</div> <div>全部商品</div></div> <div class=\"flex-1 text-center\"><div class=\"xxl m-b-10\">"+_vm._ssrEscape(_vm._s(_vm.shopInfo.visited_num))+"</div> <div>关注人数</div></div></div> "),_c('el-divider'),_vm._ssrNode(" "),_vm._ssrNode("<div class=\"flex xs m-b-16\">","</div>",[_vm._ssrNode("<div class=\"m-r-12\">店铺星级</div> "),_vm._ssrNode("<div class=\"m-t-5\">","</div>",[_c('el-rate',{attrs:{"disabled":""},model:{value:(_vm.shopInfo.star),callback:function ($$v) {_vm.$set(_vm.shopInfo, "star", $$v)},expression:"shopInfo.star"}})],1)],2),_vm._ssrNode(" <div class=\"flex xs m-b-16\"><div class=\"m-r-12\">店铺评分</div> <div>"+_vm._ssrEscape(_vm._s(_vm.shopInfo.score)+"分")+"</div></div> "),_vm._ssrNode("<div class=\"flex row-center row-between\">","</div>",[_vm._ssrNode("<div class=\"flex row-center\">","</div>",[_c('el-button',{attrs:{"size":"mini"},on:{"click":_vm.shopFollow}},[_vm._v(_vm._s(_vm.shopInfo.shop_follow_status == 1 ? '已关注' : '关注店铺'))])],1),_vm._ssrNode(" "),_c('el-popover',{attrs:{"placement":"bottom","width":"200","trigger":"hover"}},[_c('div',[_c('el-image',{staticStyle:{"width":"100%"},attrs:{"src":_vm.shopInfo.customer_image}})],1),_vm._v(" "),_c('div',{staticClass:"xs lighter text-center",attrs:{"slot":"reference"},slot:"reference"},[_c('i',{staticClass:"el-icon-chat-dot-round nr"}),_vm._v(" "),_c('span',[_vm._v("联系客服")])])])],2)],2),_vm._ssrNode(" <div class=\"m-t-10 bg-white\"><div"+(_vm._ssrClass("l-fen flex row-center",_vm.gClassId == ''?'l-fen-select':''))+">\n                    全部商品\n                </div> "+(_vm._ssrList((_vm.goodsClassList),function(item,index){return ("<div><div"+(_vm._ssrClass("l-fen flex row-center",_vm.gClassId == item.id?'l-fen-select':''))+(_vm._ssrStyle(null,null, { display: (index < 4) ? '' : 'none' }))+">"+_vm._ssrEscape("\n                        "+_vm._s(item.name)+"\n                    ")+"</div></div>")}))+"</div>")],2),_vm._ssrNode(" "),_vm._ssrNode("<div class=\"right m-l-15\">","</div>",[(_vm.couponsList.length && _vm.xuanIndex == '')?_vm._ssrNode("<div class=\"bg-white\">","</div>",[_vm._ssrNode("<div class=\"coupon-list\">","</div>",[_vm._ssrNode("<div class=\"m-b-10 flex row-between\"><div>\n                            领券\n                        </div> "+((_vm.couponsList.length > 3)?("<div class=\"flex row-center coupons-more\"><div class=\"m-r-5\">\n                                更多\n                            </div> <i"+(_vm._ssrClass(null,_vm.more?'el-icon-arrow-up':'el-icon-arrow-down'))+"></i></div>"):"<!---->")+"</div> "),_vm._ssrNode("<div"+(_vm._ssrClass("flex",_vm.more? 'swiper-item-zk':'swiper-item-c'))+">","</div>",_vm._l((_vm.couponsList),function(item,index){return _vm._ssrNode("<div class=\"item\">","</div>",[_vm._ssrNode("<div"+(_vm._ssrClass(null,[
                              'info white',
                              { gray: item.is_get } ]))+">","</div>",[_vm._ssrNode("<div class=\"info-hd flex\">","</div>",[_vm._ssrNode("<div>","</div>",[_c('price-formate',{attrs:{"price":item.money,"first-size":38,"second-size":38}})],1),_vm._ssrNode(" <div class=\"m-l-8 flex1\"><div class=\"line1\">"+_vm._ssrEscape(_vm._s(item.name))+"</div> <div class=\"xs line1\">"+_vm._ssrEscape(_vm._s(item.condition_type_desc)+"\n                                        ")+"</div></div>")],2),_vm._ssrNode(" <div class=\"info-time xs\">"+_vm._ssrEscape(_vm._s(item.user_time_desc))+"</div>")],2),_vm._ssrNode(" <div class=\"flex row-between coupon-button\"><div class=\"tips-con xs lighter\">"+_vm._ssrEscape("\n                                    "+_vm._s(item.use_scene_desc)+"\n                                ")+"</div> "+((!item.is_get)?("<div class=\"primary sm\">\n                                    立即领取\n                                </div>"):"<!---->")+"</div> "+((item.is_get)?("<img"+(_vm._ssrAttr("src",__webpack_require__(169)))+" alt class=\"receice\">"):"<!---->"))],2)}),0)],2)]):_vm._e(),_vm._ssrNode(" "),(_vm.recommend && _vm.xuanIndex == '')?_vm._ssrNode("<div class=\"shop-list\">","</div>",[_vm._ssrNode("<div class=\"m-b-10\">\n                    店铺推荐\n                </div> "),_c('el-carousel',{attrs:{"arrow":"never","indicator-position":"outside","trigger":"click","height":"300px","autoplay":false}},_vm._l((_vm.recommend),function(itemd,indexd){return _c('el-carousel-item',{key:indexd},[_c('div',{staticClass:"flex"},_vm._l((itemd),function(itemg,indexg){return _c('div',{key:indexg,staticClass:"shop-item"},[_c('nuxt-link',{attrs:{"to":("/goods_details/" + (itemg.id))}},[_c('div',{},[_c('div',{},[_c('el-image',{staticStyle:{"height":"200px","width":"200px"},attrs:{"src":itemg.image}})],1),_vm._v(" "),_c('div',{staticClass:"name m-l-10 line-1"},[_vm._v("\n                                            "+_vm._s(itemg.name)+"\n                                        ")]),_vm._v(" "),_c('div',{staticClass:"m-l-10 flex"},[_c('div',{staticClass:"primary m-r-8"},[_c('price-formate',{attrs:{"price":itemg.min_price,"first-size":16}})],1),_vm._v(" "),_c('div',{staticClass:"muted sm line-through"},[_c('price-formate',{attrs:{"price":itemg.market_price}})],1)])])])],1)}),0)])}),1)],2):_vm._e(),_vm._ssrNode(" "),_vm._ssrNode("<div"+(_vm._ssrClass(null,_vm.xuanIndex == ''? 'm-t-10':''))+">","</div>",[_vm._ssrNode("<div id=\"goods-sort\" class=\"sort m-b-16 flex bg-white col-top\"><div class=\"sort-title\">排序方式：</div> <div class=\"sort-name m-l-16 flex\"><div"+(_vm._ssrClass(null,['item', { active: _vm.sortType == '' }]))+">\n                            综合\n                        </div> <div"+(_vm._ssrClass(null,['item', { active: _vm.sortType == 'price' }]))+">\n                            价格\n                            <i class=\"el-icon-arrow-down\""+(_vm._ssrStyle(null,null, { display: (_vm.priceSort == 'desc') ? '' : 'none' }))+"></i> <i class=\"el-icon-arrow-up\""+(_vm._ssrStyle(null,null, { display: (_vm.priceSort == 'asc') ? '' : 'none' }))+"></i></div> <div"+(_vm._ssrClass(null,['item', { active: _vm.sortType == 'sales_sum' }]))+">\n                            销量\n                            <i class=\"el-icon-arrow-down\""+(_vm._ssrStyle(null,null, { display: (_vm.saleSort == 'desc') ? '' : 'none' }))+"></i> <i class=\"el-icon-arrow-up\""+(_vm._ssrStyle(null,null, { display: (_vm.saleSort == 'asc') ? '' : 'none' }))+"></i></div></div></div> "),(_vm.goodsList.length)?[_c('goods-list',{attrs:{"list":_vm.goodsList}}),_vm._ssrNode(" "),(_vm.count)?_vm._ssrNode("<div class=\"pagination flex m-t-30 row-center\" style=\"padding-bottom: 38px\">","</div>",[_c('el-pagination',{attrs:{"background":"","layout":"prev, pager, next","total":_vm.count,"prev-text":"上一页","next-text":"下一页","hide-on-single-page":"","page-size":20},on:{"current-change":_vm.changePage}})],1):_vm._e()]:_c('null-data',{attrs:{"img":__webpack_require__(156),"text":"暂无商品~"}})],2)],2)],2)],2)}
var staticRenderFns = []


// CONCATENATED MODULE: ./pages/shop_street_detail.vue?vue&type=template&id=1bbb4a36&

// EXTERNAL MODULE: ./utils/tools.js
var tools = __webpack_require__(145);

// EXTERNAL MODULE: ./node_modules/element-ui/lib/element-ui.common.js
var element_ui_common = __webpack_require__(14);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/shop_street_detail.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//


/* harmony default export */ var shop_street_detailvue_type_script_lang_js_ = ({
  head() {
    return {
      title: this.$store.getters.headTitle,
      link: [{
        rel: 'icon',
        type: 'image/x-icon',
        href: this.$store.getters.favicon
      }]
    };
  },

  layout: "street",
  components: {},

  async asyncData({
    $get,
    query
  }) {
    // 店铺信息
    const shopData = await $get("shop/getShopInfo", {
      params: {
        shop_id: query.id
      }
    });

    if (shopData.code == 1) {
      if (shopData.data.goods_list.length > 0) {
        var num = [];

        for (var i = 0; i < Math.ceil(shopData.data.goods_list.length / 4); i++) {
          var start = i * 4;
          var end = start + 4;
          num.push(shopData.data.goods_list.slice(start, end));
        }
      }
    }

    console.log('num', num); // 获取优惠券

    const coupon = await $get("coupon/getCouponList", {
      params: {
        shop_id: query.id
      }
    }); // 商品分类

    const goodsClass = await $get("shop_goods_category/getShopGoodsCategory", {
      params: {
        shop_id: query.id
      }
    });

    if (goodsClass.code == 1) {
      if (goodsClass.data.length > 0) {
        var group = [];

        for (var i = 0; i < Math.ceil(goodsClass.data.length / 6); i++) {
          var start = i * 6;
          var end = start + 6;
          group.push(goodsClass.data.slice(start, end));
        }
      }
    }

    console.log('group', group);
    return {
      recommend: num,
      // 推荐列表
      shopInfo: shopData.data,
      // 商家信息
      goodsClassList: goodsClass.data,
      // 商品分类列表
      goodsClassListGroup: group,
      // 商品分类切割列表
      couponsList: coupon.data.lists // 优惠券列表

    };
  },

  data() {
    return {
      goodsClassListGroup: [],
      recommend: [],
      couponsList: [],
      gClassId: '',
      shopInfo: [],
      goodsClassList: [],
      swiperOptions: {
        pagination: {
          el: '.swiper-pagination',
          clickable: true
        },
        navigation: {
          nextEl: '.swiper-button-next',
          prevEl: '.swiper-button-prev'
        },
        preventClicks: true,
        slidesPerView: 'auto',
        autoplay: true
      },
      sortType: "",
      saleSort: "desc",
      priceSort: "desc",
      page: 1,
      count: 0,
      goodsList: [],
      more: false,
      keyword: '',
      xuanIndex: ''
    };
  },

  created() {
    this.getGoods();
    this.changeSortType = Object(tools["b" /* trottle */])(this.changeSortType, 500, this);
  },

  methods: {
    // 搜索商品
    search() {
      this.getGoods(); // 搜索后跳转到商品列表位置

      if (this.xuanIndex == '') {
        setTimeout(() => {
          document.getElementById('goods-sort').scrollIntoView();
        }, 500);
      }
    },

    // 顶部商品分类选择事件
    changeXuan(id) {
      this.xuanIndex = id;

      if (id == 'all') {
        this.gClassId = '';
        this.getGoods();
      } else {
        this.gClassId = id;
        this.getGoods();
      }
    },

    // 店铺信息
    async getShopData() {
      const shopData = await this.$get("shop/getShopInfo", {
        params: {
          shop_id: this.$route.query.id
        }
      });

      if (shopData.code == 1) {
        this.shopInfo = shopData.data; // 切割推荐列表

        if (shopData.data.goods_list.length > 0) {
          var num = [];

          for (var i = 0; i < Math.ceil(shopData.data.goods_list.length / 4); i++) {
            var start = i * 4;
            var end = start + 4;
            num.push(shopData.data.goods_list.slice(start, end));
          }
        }

        console.log('num', num);
        this.recommend = num;
      }
    },

    // 关注店铺
    shopFollow() {
      const a = this.$post("shop_follow/changeStatus", {
        shop_id: this.$route.query.id
      });
      this.getShopData();
    },

    // 领取优惠券
    async hqCoupon(id) {
      const {
        msg,
        code
      } = await this.$post("coupon/getCoupon", {
        coupon_id: id
      });

      if (code == 1) {
        this.$message({
          message: msg,
          type: "success"
        });
      }

      this.getCouponList();
    },

    // 获取优惠券列表
    async getCouponList() {
      const coupon = await this.$get("coupon/getCouponList", {
        params: {
          shop_id: this.$route.query.id
        }
      });
    },

    // 点击商品分类
    getClassGoods(id) {
      this.gClassId = id;

      if (id == '') {
        this.xuanIndex = 'all';
      } else {
        this.xuanIndex = id;
      }

      this.getGoods();
    },

    changeSortType(type) {
      this.sortType = type;

      switch (type) {
        case "price":
          if (this.priceSort == "asc") {
            this.priceSort = "desc";
          } else if (this.priceSort == "desc") {
            this.priceSort = "asc";
          }

          break;

        case "sales_sum":
          if (this.saleSort == "asc") {
            this.saleSort = "desc";
          } else if (this.saleSort == "desc") {
            this.saleSort = "asc";
          }

          break;

        default:
      }

      this.getGoods();
    },

    changePage(current) {
      this.page = current;
      this.getGoods();
    },

    async getGoods() {
      const {
        name
      } = this.$route.query;
      const {
        priceSort,
        sortType,
        saleSort
      } = this;
      let sort = "";

      switch (sortType) {
        case "price":
          sort = priceSort;
          break;

        case "sales_sum":
          sort = saleSort;
          break;
      }

      const {
        data: {
          list,
          count
        }
      } = await this.$get("pc/goodsList", {
        params: {
          page_size: 20,
          page_no: this.page,
          sort_type: sortType,
          sort,
          category_id: this.gClassId,
          shop_id: this.$route.query.id,
          name: this.keyword
        }
      });
      this.count = count;
      this.goodsList = list;
    }

  }
});
// CONCATENATED MODULE: ./pages/shop_street_detail.vue?vue&type=script&lang=js&
 /* harmony default export */ var pages_shop_street_detailvue_type_script_lang_js_ = (shop_street_detailvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(1);

// CONCATENATED MODULE: ./pages/shop_street_detail.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(276)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  pages_shop_street_detailvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  null,
  "3b0bc896"
  
)

/* harmony default export */ var shop_street_detail = __webpack_exports__["default"] = (component.exports);

/* nuxt-component-imports */
installComponents(component, {PriceFormate: __webpack_require__(137).default,GoodsList: __webpack_require__(154).default,NullData: __webpack_require__(143).default})


/***/ })

};;
//# sourceMappingURL=shop_street_detail.js.map