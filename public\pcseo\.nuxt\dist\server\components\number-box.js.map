{"version": 3, "file": "components/number-box.js", "sources": ["webpack:///./components/number-box.vue?64ab", "webpack:///./components/number-box.vue?e931", "webpack:///./components/number-box.vue?4591", "webpack:///./components/number-box.vue?c46e", "webpack:///./components/number-box.vue", "webpack:///./components/number-box.vue?9baa", "webpack:///./components/number-box.vue?bc32"], "sourcesContent": ["// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/sass-resources-loader/lib/loader.js??ref--7-oneOf-1-4!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./number-box.vue?vue&type=style&index=0&id=1d9d8f36&lang=scss&scoped=true&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"663bee12\", content, true, context)\n};", "export * from \"-!../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/sass-resources-loader/lib/loader.js??ref--7-oneOf-1-4!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./number-box.vue?vue&type=style&index=0&id=1d9d8f36&lang=scss&scoped=true&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".number-box[data-v-1d9d8f36]{display:inline-flex;align-items:center}.number-box .number-input[data-v-1d9d8f36]{position:relative;text-align:center;padding:0;margin:0 6px;align-items:center;justify-content:center}.number-box .minus[data-v-1d9d8f36],.number-box .plus[data-v-1d9d8f36]{width:32px;display:flex;justify-content:center;align-items:center;cursor:pointer}.number-box .plus[data-v-1d9d8f36]{border-radius:0 2px 2px 0}.number-box .minus[data-v-1d9d8f36]{border-radius:2px 0 0 2px}.number-box .disabled[data-v-1d9d8f36]{color:#c8c9cc!important;background:#f7f8fa!important}.number-box .input-disabled[data-v-1d9d8f36]{color:#c8c9cc!important;background-color:#f2f3f5!important}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"number-box\"},[_vm._ssrNode(\"<div\"+(_vm._ssrClass(null,{ minus: true, disabled: _vm.disabled || _vm.inputVal <= _vm.min }))+(_vm._ssrStyle(null,{\n            background: _vm.bgColor,\n            height: _vm.inputHeight + 'px',\n            color: _vm.color,\n        }, null))+\" data-v-1d9d8f36><div\"+(_vm._ssrStyle(null,{ fontSize: _vm.size + 'px' }, null))+\" data-v-1d9d8f36>-</div></div> <input\"+(_vm._ssrAttr(\"disabled\",_vm.disabledInput || _vm.disabled))+\" type=\\\"text\\\"\"+(_vm._ssrAttr(\"value\",(_vm.inputVal)))+(_vm._ssrClass(null,{ 'number-input': true, 'input-disabled': _vm.disabled }))+(_vm._ssrStyle(null,{\n            color: _vm.color,\n            fontSize: _vm.size + 'px',\n            background: _vm.bgColor,\n            height: _vm.inputHeight + 'px',\n            width: _vm.inputWidth + 'px',\n        }, null))+\" data-v-1d9d8f36> <div\"+(_vm._ssrClass(\"plus\",{ disabled: _vm.disabled || _vm.inputVal >= _vm.max }))+(_vm._ssrStyle(null,{\n            background: _vm.bgColor,\n            height: _vm.inputHeight + 'px',\n            color: _vm.color,\n        }, null))+\" data-v-1d9d8f36><div\"+(_vm._ssrStyle(null,{ fontSize: _vm.size + 'px' }, null))+\" data-v-1d9d8f36>+</div></div>\")])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nexport default {\n    components: {},\n    props: {\n        // 预显示的数字\n        value: {\n            type: Number,\n            default: 1,\n        },\n        // 背景颜色\n        bgColor: {\n            type: String,\n            default: ' #F2F3F5',\n        },\n        // 最小值\n        min: {\n            type: Number,\n            default: 0,\n        },\n        // 最大值\n        max: {\n            type: Number,\n            default: 99999,\n        },\n        // 步进值，每次加或减的值\n        step: {\n            type: Number,\n            default: 1,\n        },\n        // 是否禁用加减操作\n        disabled: {\n            type: Boolean,\n            default: false,\n        },\n        // input的字体大小，单位px\n        size: {\n            type: [Number, String],\n            default: 14,\n        },\n        // input宽度，单位px\n        inputWidth: {\n            type: [Number, String],\n            default: 64,\n        },\n        //字体颜色\n        color: {\n            type: String,\n            default: '#333',\n        },\n        // input高度，单位px\n        inputHeight: {\n            type: [Number, String],\n            default: 32,\n        },\n        // index索引，用于列表中使用，让用户知道是哪个numberbox发生了变化，一般使用for循环出来的index值即可\n        index: {\n            type: [Number, String],\n            default: '',\n        },\n        // 是否禁用输入框，与disabled作用于输入框时，为OR的关系，即想要禁用输入框，又可以加减的话\n        // 设置disabled为false，disabledInput为true即可\n        disabledInput: {\n            type: Boolean,\n            default: false,\n        },\n\n        // 是否只能输入大于或等于0的整数(正整数)\n        positiveInteger: {\n            type: Boolean,\n            default: true,\n        },\n        asyncChange: {\n            type: Boolean,\n            default: false,\n        },\n    },\n    watch: {\n        value(v1, v2) {\n            if (!this.changeFromInner) {\n                this.inputVal = v1\n                this.$nextTick(function () {\n                    this.changeFromInner = false\n                })\n            }\n        },\n        inputVal(v1, v2) {\n            if (v1 == '') return\n            let value = 0\n            let tmp = /^(?:-?\\d+|-?\\d{1,3}(?:,\\d{3})+)?(?:\\.\\d+)?$/.test(v1)\n            if (tmp && v1 >= this.min && v1 <= this.max) value = v1\n            else value = v2\n            if (this.positiveInteger) {\n                if (v1 < 0 || String(v1).indexOf('.') !== -1) {\n                    value = v2\n                    this.$nextTick(() => {\n                        this.inputVal = v2\n                    })\n                }\n            }\n            if (this.asyncChange) {\n                return\n            }\n            // 发出change事件\n            this.handleChange(value, 'change')\n        },\n    },\n    data() {\n        return {\n            inputVal: 1, // 输入框中的值，不能直接使用props中的value，因为应该改变props的状态\n            timer: null, // 用作长按的定时器\n            changeFromInner: false, // 值发生变化，是来自内部还是外部\n            innerChangeTimer: null, // 内部定时器\n        }\n    },\n    created() {\n        this.inputVal = Number(this.value)\n    },\n    computed: {},\n    methods: {\n        btnTouchStart(callback) {\n            this[callback]()\n        },\n        minus() {\n            this.computeVal('minus')\n        },\n        plus() {\n            this.computeVal('plus')\n        },\n        calcPlus(num1, num2) {\n            let baseNum, baseNum1, baseNum2\n            try {\n                baseNum1 = num1.toString().split('.')[1].length\n            } catch (e) {\n                baseNum1 = 0\n            }\n            try {\n                baseNum2 = num2.toString().split('.')[1].length\n            } catch (e) {\n                baseNum2 = 0\n            }\n            baseNum = Math.pow(10, Math.max(baseNum1, baseNum2))\n            let precision = baseNum1 >= baseNum2 ? baseNum1 : baseNum2\n            return ((num1 * baseNum + num2 * baseNum) / baseNum).toFixed(\n                precision\n            )\n        },\n        calcMinus(num1, num2) {\n            let baseNum, baseNum1, baseNum2\n            try {\n                baseNum1 = num1.toString().split('.')[1].length\n            } catch (e) {\n                baseNum1 = 0\n            }\n            try {\n                baseNum2 = num2.toString().split('.')[1].length\n            } catch (e) {\n                baseNum2 = 0\n            }\n            baseNum = Math.pow(10, Math.max(baseNum1, baseNum2))\n            let precision = baseNum1 >= baseNum2 ? baseNum1 : baseNum2\n            return ((num1 * baseNum - num2 * baseNum) / baseNum).toFixed(\n                precision\n            )\n        },\n        computeVal(type) {\n            if (this.disabled) return\n            let value = 0\n            // 减\n            if (type === 'minus') {\n                value = this.calcMinus(this.inputVal, this.step)\n            } else if (type === 'plus') {\n                value = this.calcPlus(this.inputVal, this.step)\n            }\n            // 判断是否小于最小值和大于最大值\n            if (value < this.min || value > this.max) {\n                return\n            }\n            if (this.asyncChange) {\n                this.$emit('change', value)\n            } else {\n                this.inputVal = value\n                this.handleChange(value, type)\n            }\n        },\n        // 处理用户手动输入的情况\n        onBlur(event) {\n            let val = 0\n            let value = event.target.value\n\n            console.log(value)\n            if (!/(^\\d+$)/.test(value)) {\n                val = this.min\n            } else {\n                val = +value\n            }\n            if (val > this.max) {\n                val = this.max\n            } else if (val < this.min) {\n                val = this.min\n            }\n            this.$nextTick(() => {\n                this.inputVal = val\n            })\n            this.handleChange(val, 'blur')\n        },\n        // 输入框获得焦点事件\n        onFocus() {\n            this.$emit('focus')\n        },\n        handleChange(value, type) {\n            if (this.disabled) return\n            // 清除定时器，避免造成混乱\n            if (this.innerChangeTimer) {\n                clearTimeout(this.innerChangeTimer)\n                this.innerChangeTimer = null\n            }\n            this.changeFromInner = true\n            this.innerChangeTimer = setTimeout(() => {\n                this.changeFromInner = false\n            }, 150)\n            this.$emit('input', Number(value))\n            this.$emit(type, {\n                value: Number(value),\n                index: this.index,\n            })\n        },\n    },\n}\n", "import mod from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./number-box.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./number-box.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./number-box.vue?vue&type=template&id=1d9d8f36&scoped=true&\"\nimport script from \"./number-box.vue?vue&type=script&lang=js&\"\nexport * from \"./number-box.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./number-box.vue?vue&type=style&index=0&id=1d9d8f36&lang=scss&scoped=true&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  \"1d9d8f36\",\n  \"284477ee\"\n  \n)\n\nexport default component.exports"], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACXA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACNA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;AChBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AACA;AACA;AAFA;AAKA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AApEA;AAyEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AA7BA;AACA;AA6BA;AACA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AACA;AALA;AAMA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AAGA;AACA;AAAA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AAGA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAEA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AA5GA;AArHA;;AC5CA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;A", "sourceRoot": ""}