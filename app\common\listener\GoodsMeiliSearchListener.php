<?php
declare (strict_types = 1);

namespace app\common\listener;

use app\common\library\MeiliSearch;
use app\common\model\goods\Goods;
use think\facade\Log;

/**
 * 商品MeiliSearch监听器
 * 用于在商品增删改操作后自动同步数据到MeiliSearch
 */
class GoodsMeiliSearchListener
{
    /**
     * 处理事件
     * @param Goods $goods
     * @return void
     */
    public function handle($goods)
    {
        // 记录事件处理开始
        Log::info('GoodsMeiliSearchListener处理事件开始，商品ID：' . ($goods->id ?? 'unknown'));

        try {
            // 检查参数类型
            if (!$goods instanceof Goods) {
                Log::error('GoodsMeiliSearchListener接收到非Goods类型参数：' . gettype($goods));
                return;
            }

            // 获取当前正在处理的事件名称
            $backtrace = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 3);
            $eventName = '';

            foreach ($backtrace as $trace) {
                if (isset($trace['class']) && $trace['class'] === 'think\\Event' && isset($trace['function']) && $trace['function'] === 'trigger') {
                    // 尝试从调用栈中获取事件名称
                    $eventName = $trace['args'][0] ?? '';
                    break;
                }
            }

            Log::info('GoodsMeiliSearchListener检测到事件：' . $eventName);

            // 根据事件名称调用相应的处理方法
            if ($eventName === 'GoodsCreate') {
                $this->onGoodsCreate($goods);
            } elseif ($eventName === 'GoodsUpdate') {
                $this->onGoodsUpdate($goods);
            } elseif ($eventName === 'GoodsDelete') {
                $this->onGoodsDelete($goods);
            } else {
                // 默认当作更新处理
                Log::info('GoodsMeiliSearchListener未识别事件类型，默认作为更新处理');
                $this->onGoodsUpdate($goods);
            }
        } catch (\Exception $e) {
            Log::error('GoodsMeiliSearchListener处理事件异常：' . $e->getMessage());
        }

        // 记录事件处理结束
        Log::info('GoodsMeiliSearchListener处理事件结束');
    }

    /**
     * 处理商品创建事件
     * @param Goods $goods
     */
    public function onGoodsCreate(Goods $goods)
    {
        $this->syncGoodsToMeiliSearch($goods);
    }

    /**
     * 处理商品更新事件
     * @param Goods $goods
     */
    public function onGoodsUpdate(Goods $goods)
    {
        $this->syncGoodsToMeiliSearch($goods);
    }

    /**
     * 处理商品删除事件
     * @param Goods $goods
     */
    public function onGoodsDelete(Goods $goods)
    {
        // 记录删除事件
        Log::info('处理商品删除事件 - 商品ID：' . $goods->id . ', del: ' . $goods->del);

        // 只有在商品真正被删除时才从MeiliSearch中删除文档
        if ($goods->del == 1) {
            Log::info('商品已被删除，从MeiliSearch中删除 - 商品ID：' . $goods->id);
            $this->deleteGoodsFromMeiliSearch($goods);
        } else {
            Log::info('商品未被标记为删除，跳过从MeiliSearch中删除操作 - 商品ID：' . $goods->id);
        }
    }

    /**
     * 同步商品数据到MeiliSearch
     * @param Goods $goods
     */
    protected function syncGoodsToMeiliSearch(Goods $goods)
    {
        try {
            // 记录商品状态
            Log::info('商品同步状态检查 - 商品ID：' . $goods->id . ', status: ' . $goods->status . ', del: ' . $goods->del . ', audit_status: ' . $goods->audit_status);

            // 只同步上架的商品
            if ($goods->status != 1 || $goods->del != 0 || $goods->audit_status != 1) {
                Log::info('商品不符合同步条件，准备从MeiliSearch中删除 - 商品ID：' . $goods->id);

                // 检查是否是真正需要删除的情况（例如，商品被彻底删除或下架）
                if ($goods->del == 1) {
                    Log::info('商品已被删除，从MeiliSearch中删除 - 商品ID：' . $goods->id);
                    $this->deleteGoodsFromMeiliSearch($goods);
                } else {
                    // 对于其他状态变更，不立即删除，而是记录日志
                    Log::info('商品状态变更但未删除，暂不从MeiliSearch中删除 - 商品ID：' . $goods->id .
                        ', status: ' . $goods->status .
                        ', audit_status: ' . $goods->audit_status);

                    // 这里可以实现一个更新商品状态的方法，而不是直接删除
                    // 例如：$this->updateGoodsStatusInMeiliSearch($goods);

                    // 为了避免频繁删除操作，我们不再调用删除方法
                    // $this->deleteGoodsFromMeiliSearch($goods);
                }
                return;
            }

            // 初始化MeiliSearch客户端
            $meili = new MeiliSearch();

            // 准备商品数据
            $goodsData = $goods->toArray();

            // 只保留需要的字段
            $fields = [
                'id', 'name', 'image', 'remark', 'content', 'split_word', 'shop_id',
                'first_cate_id', 'second_cate_id', 'third_cate_id', 'brand_id',
                'min_price', 'market_price', 'sales_actual', 'sales_virtual',
                'is_hot', 'is_recommend', 'create_time', 'update_time'
            ];

            $data = [];
            foreach ($fields as $field) {
                if (isset($goodsData[$field])) {
                    $data[$field] = $goodsData[$field];
                }
            }

            // 更新MeiliSearch中的商品数据
            Log::info('准备更新商品到MeiliSearch - 商品ID：' . $goods->id);
            $response = $meili->updateDocumentsInIndex('goods', [$data]);

            if (!$response || isset($response['error'])) {
                Log::error('同步商品到MeiliSearch失败：' . json_encode($response));
            } else {
                Log::info('同步商品到MeiliSearch成功，商品ID：' . $goods->id . ', 任务ID：' . ($response['taskUid'] ?? 'unknown'));
            }
        } catch (\Exception $e) {
            Log::error('同步商品到MeiliSearch异常：' . $e->getMessage());
        }
    }

    /**
     * 从MeiliSearch中删除商品
     * @param Goods $goods
     */
    protected function deleteGoodsFromMeiliSearch(Goods $goods)
    {
        try {
            // 添加更多日志记录
            Log::info('准备从MeiliSearch删除商品 - 商品ID：' . $goods->id . ', 状态检查: status=' . $goods->status . ', del=' . $goods->del . ', audit_status=' . $goods->audit_status);

            // 检查是否需要执行删除操作
            // 这里可以添加更多的条件判断，例如只有在商品被彻底删除时才从索引中删除
            $shouldDelete = true;

            // 检查是否存在于索引中
            $meili = new MeiliSearch();

            // 尝试搜索文档，检查是否存在
            try {
                // 使用精确搜索查询商品ID
                $searchResult = $meili->searchInIndex('goods', '');

                // 检查索引是否存在文档
                if (!$searchResult || isset($searchResult['error']) ||
                    (isset($searchResult['estimatedTotalHits']) && $searchResult['estimatedTotalHits'] == 0)) {
                    Log::info('商品索引为空或不存在，无需删除 - 商品ID：' . $goods->id);
                    $shouldDelete = false;
                } else {
                    // 索引存在文档，但我们不确定是否包含当前商品
                    // 为安全起见，我们假设商品存在并执行删除操作
                    Log::info('商品索引存在文档，将执行删除操作 - 商品ID：' . $goods->id);
                }
            } catch (\Exception $e) {
                Log::info('检查商品在MeiliSearch中是否存在时出错，假设不存在 - 商品ID：' . $goods->id . ', 错误：' . $e->getMessage());
                $shouldDelete = false;
            }

            if (!$shouldDelete) {
                Log::info('跳过从MeiliSearch删除商品操作 - 商品ID：' . $goods->id);
                return;
            }

            // 执行删除操作
            Log::info('执行从MeiliSearch删除商品操作 - 商品ID：' . $goods->id);
            $response = $meili->deleteDocuments('goods', [$goods->id]);

            if (!$response || isset($response['error'])) {
                Log::error('从MeiliSearch删除商品失败：' . json_encode($response) . ' - 商品ID：' . $goods->id);
            } else {
                Log::info('从MeiliSearch删除商品成功，商品ID：' . $goods->id . ', 任务ID：' . ($response['taskUid'] ?? 'unknown'));

                // 添加延迟，确保删除操作完成
                sleep(1);
            }
        } catch (\Exception $e) {
            Log::error('从MeiliSearch删除商品异常：' . $e->getMessage() . ' - 商品ID：' . $goods->id);
        }
    }
}
