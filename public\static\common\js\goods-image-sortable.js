/**
 * 商品轮播图拖拽排序功能
 * 兼容现有的上传组件，添加拖拽排序支持
 */

(function($) {
    'use strict';

    // 初始化轮播图排序功能
    function initGoodsImageSortable() {
        // 为轮播图容器添加排序功能
        var goodsImageContainer = $('#goodsImageContainer .like-upload-image');
        
        if (goodsImageContainer.length && typeof Sortable !== 'undefined') {
            new Sortable(goodsImageContainer[0], {
                animation: 150,
                ghostClass: 'sortable-ghost',
                chosenClass: 'sortable-chosen',
                dragClass: 'sortable-drag',
                handle: '.upload-image-div',
                filter: '.upload-image-elem', // 排除添加按钮
                onEnd: function(evt) {
                    updateImageOrder();
                }
            });
        }
    }

    // 更新图片序号和表单字段
    function updateImageOrder() {
        var container = $('#goodsImageContainer .like-upload-image');
        var images = container.find('.upload-image-div');
        
        images.each(function(index) {
            $(this).attr('data-order', index + 1);
        });
        
        // 确保添加按钮在最后
        var addButton = container.find('.upload-image-elem');
        if (addButton.length) {
            container.append(addButton);
        }
    }

    // 监听图片上传完成事件
    function bindImageUploadEvents() {
        // 监听DOM变化，当有新图片添加时重新初始化排序
        var observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList') {
                    var target = $(mutation.target);
                    if (target.hasClass('like-upload-image') && target.closest('#goodsImageContainer').length) {
                        setTimeout(function() {
                            updateImageOrder();
                            addImageControls();
                        }, 100);
                    }
                }
            });
        });

        // 观察轮播图容器的变化
        var goodsImageContainer = document.getElementById('goodsImageContainer');
        if (goodsImageContainer) {
            observer.observe(goodsImageContainer, {
                childList: true,
                subtree: true
            });
        }
    }

    // 为图片添加控制按钮
    function addImageControls() {
        $('#goodsImageContainer .upload-image-div').each(function() {
            var $this = $(this);
            
            // 如果已经有控制按钮，跳过
            if ($this.find('.image-controls').length) {
                return;
            }
            
            // 添加控制按钮
            var controls = $('<div class="image-controls">' +
                '<button type="button" class="btn-preview" title="预览">' +
                    '<i class="layui-icon layui-icon-search"></i>' +
                '</button>' +
                '<button type="button" class="btn-move-up" title="上移">' +
                    '<i class="layui-icon layui-icon-up"></i>' +
                '</button>' +
                '<button type="button" class="btn-move-down" title="下移">' +
                    '<i class="layui-icon layui-icon-down"></i>' +
                '</button>' +
            '</div>');
            
            $this.append(controls);
        });
    }

    // 绑定控制按钮事件
    function bindControlEvents() {
        // 预览图片
        $(document).on('click', '#goodsImageContainer .btn-preview', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            var imgSrc = $(this).closest('.upload-image-div').find('img').attr('src');
            if (typeof layer !== 'undefined') {
                layer.photos({
                    photos: {
                        "title": "图片预览",
                        "data": [{
                            "src": imgSrc
                        }]
                    },
                    anim: 5
                });
            }
        });

        // 上移图片
        $(document).on('click', '#goodsImageContainer .btn-move-up', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            var current = $(this).closest('.upload-image-div');
            var prev = current.prev('.upload-image-div');
            
            if (prev.length) {
                current.insertBefore(prev);
                updateImageOrder();
            }
        });

        // 下移图片
        $(document).on('click', '#goodsImageContainer .btn-move-down', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            var current = $(this).closest('.upload-image-div');
            var next = current.next('.upload-image-div');
            
            if (next.length) {
                current.insertAfter(next);
                updateImageOrder();
            }
        });
    }

    // 处理编辑页面的数据回显
    function handleEditPageData() {
        // 检查是否是编辑页面（有goods_info数据）
        if (typeof goods_info !== 'undefined' && goods_info.base && goods_info.base.goods_image) {
            setTimeout(function() {
                updateImageOrder();
                addImageControls();
            }, 500);
        }
    }

    // 初始化函数
    function init() {
        // 等待页面加载完成
        $(document).ready(function() {
            setTimeout(function() {
                initGoodsImageSortable();
                bindImageUploadEvents();
                bindControlEvents();
                handleEditPageData();
            }, 1000);
        });
    }

    // 自动初始化
    init();

    // 暴露到全局
    window.GoodsImageSortable = {
        init: init,
        updateOrder: updateImageOrder,
        addControls: addImageControls
    };

})(jQuery);

// 添加必要的CSS样式
if (!document.getElementById('goods-image-sortable-styles')) {
    var style = document.createElement('style');
    style.id = 'goods-image-sortable-styles';
    style.textContent = `
        /* 图片控制按钮样式 */
        .image-controls {
            position: absolute;
            top: 5px;
            right: 5px;
            display: flex;
            flex-direction: column;
            gap: 2px;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .upload-image-div:hover .image-controls {
            opacity: 1;
        }
        
        .image-controls button {
            width: 24px;
            height: 24px;
            border: none;
            border-radius: 3px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            transition: all 0.3s ease;
        }
        
        .image-controls button:hover {
            background: rgba(0, 0, 0, 0.9);
            transform: scale(1.1);
        }
        
        .btn-preview:hover {
            background: #1890ff !important;
        }
        
        .btn-move-up:hover {
            background: #52c41a !important;
        }
        
        .btn-move-down:hover {
            background: #faad14 !important;
        }
        
        /* 拖拽状态样式 */
        .sortable-ghost {
            opacity: 0.5;
            background: #f0f8ff;
            border: 2px dashed #1890ff;
        }
        
        .sortable-chosen {
            transform: rotate(2deg);
        }
        
        .sortable-drag {
            transform: rotate(2deg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        
        /* 序号显示 */
        .sortable-upload-container .upload-image-div[data-order]::before {
            content: attr(data-order);
            position: absolute;
            top: 5px;
            left: 5px;
            background: #1890ff;
            color: white;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 11px;
            font-weight: bold;
            z-index: 1;
        }
        
        /* 拖拽提示 */
        .upload-image-div {
            cursor: move;
            transition: all 0.3s ease;
        }
        
        .upload-image-div:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
    `;
    document.head.appendChild(style);
}
