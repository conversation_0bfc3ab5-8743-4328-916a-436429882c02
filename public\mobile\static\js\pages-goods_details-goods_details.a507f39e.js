(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-goods_details-goods_details","bundle-pages-bargain_process-bargain_process~bundle-pages-invite_fans-invite_fans"],{"06b6":function(t,e,i){"use strict";var n=i("b0c4"),a=i.n(n);a.a},"0743":function(t,e,i){var n=i("ae1d");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=i("4f06").default;a("00e3c65c",n,!0,{sourceMap:!1,shadowMode:!1})},"087d":function(t,e,i){"use strict";i.r(e);var n=i("22f5"),a=i.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(r);e["default"]=a.a},"0955":function(t,e,i){"use strict";i.r(e);var n=i("5c51"),a=i.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(r);e["default"]=a.a},"0a76":function(t,e,i){"use strict";var n=i("356a"),a=i.n(n);a.a},"0d14":function(t,e,i){var n=i("24fb");e=n(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.u-numberbox[data-v-1d01409a]{display:inline-flex;align-items:center}.u-number-input[data-v-1d01409a]{position:relative;text-align:center;padding:0;margin:0 %?6?%;display:flex;flex-direction:row;align-items:center;justify-content:center}.u-icon-plus[data-v-1d01409a],\n.u-icon-minus[data-v-1d01409a]{width:%?60?%;display:flex;flex-direction:row;justify-content:center;align-items:center}.u-icon-plus[data-v-1d01409a]{border-radius:0 %?8?% %?8?% 0}.u-icon-minus[data-v-1d01409a]{border-radius:%?8?% 0 0 %?8?%}.u-icon-disabled[data-v-1d01409a]{color:#c8c9cc!important;background:#f7f8fa!important}.u-input-disabled[data-v-1d01409a]{color:#c8c9cc!important;background-color:#f2f3f5!important}',""]),t.exports=e},"0da2":function(t,e,i){"use strict";var n=i("4955"),a=i.n(n);a.a},1045:function(t,e,i){"use strict";i.r(e);var n=i("d801"),a=i.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(r);e["default"]=a.a},1203:function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return r})),i.d(e,"a",(function(){return n}));var n={uWaterfall:i("d0a7").default,goodsList:i("2d92").default},a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return t.hasData?i("mescroll-body",{ref:"mescrollRef",attrs:{up:t.upOption,down:t.downOption},on:{init:function(e){arguments[0]=e=t.$handleEvent(e),t.mescrollInit.apply(void 0,arguments)},down:function(e){arguments[0]=e=t.$handleEvent(e),t.downCallback.apply(void 0,arguments)},up:function(e){arguments[0]=e=t.$handleEvent(e),t.upCallback.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"goods-column"},[i("v-uni-scroll-view",{attrs:{"scroll-x":"true"}},[i("v-uni-view",{staticClass:"column-wrap"},t._l(t.columnList,(function(e,n){return i("v-uni-view",{key:n,staticClass:"item flex-col m-r-50 muted",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.changeActive(n)}}},[i("v-uni-view",{staticClass:"xxl normal title",class:{bold:t.active==n}},[t._v(t._s(e.name))]),i("v-uni-view",{staticClass:"m-t-8 xs text-center",class:{normal:t.active==n}},[t._v(t._s(e.remark))]),t.active==n?i("v-uni-view",{staticClass:"line br60"}):t._e()],1)})),1)],1)],1),i("v-uni-view",{staticClass:"goods"},[i("u-waterfall",{ref:"uWaterfall",attrs:{"add-time":20},scopedSlots:t._u([{key:"left",fn:function(t){var e=t.leftList;return[i("v-uni-view",{staticStyle:{padding:"0 9rpx 0 30rpx"}},[i("goods-list",{attrs:{width:"336rpx",type:"waterfall",list:e}})],1)]}},{key:"right",fn:function(t){var e=t.rightList;return[i("v-uni-view",{staticStyle:{padding:"0 30rpx 0 9rpx"}},[i("goods-list",{attrs:{width:"336rpx",type:"waterfall",list:e}})],1)]}}],null,!1,2662770354),model:{value:t.goodsList,callback:function(e){t.goodsList=e},expression:"goodsList"}})],1)],1):t._e()},r=[]},1231:function(t,e,i){t.exports=i.p+"static/images/received.png"},"125a":function(t,e,i){var n=i("2286");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=i("4f06").default;a("5679431c",n,!0,{sourceMap:!1,shadowMode:!1})},1377:function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=i("b08d"),a={name:"float-tab",data:function(){return{showMore:!1,top:0}},mounted:function(){var t=this;(0,n.getRect)(".tab-img",!1,this).then((function(e){t.height=e.height,console.log(t.height)}))},methods:{onChange:function(){this.showMore=!this.showMore}},watch:{showMore:function(t){this.top=t?-this.height:0}}};e.default=a},1400:function(t,e,i){"use strict";i.r(e);var n=i("9873"),a=i("48d0");for(var r in a)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(r);var o=i("f0c5"),s=Object(o["a"])(a["default"],n["b"],n["c"],!1,null,"5024cd44",null,!1,n["a"],void 0);e["default"]=s.exports},"159d":function(t,e,i){var n=i("90d7");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=i("4f06").default;a("13608fe5",n,!0,{sourceMap:!1,shadowMode:!1})},1927:function(t,e,i){"use strict";i.r(e);var n=i("d7e8"),a=i("79d0");for(var r in a)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(r);i("0a76");var o=i("f0c5"),s=Object(o["a"])(a["default"],n["b"],n["c"],!1,null,"01eafde1",null,!1,n["a"],void 0);e["default"]=s.exports},"1b85":function(t,e,i){"use strict";i("7a82");var n=i("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("a9e3"),i("acd8");var a=n(i("be37")),r=n(i("1400")),o=n(i("3178")),s=n(i("eb32")),c=n(i("2669")),d={name:"share-poster",components:{lPainter:a.default,lPainterImage:r.default,lPainterText:o.default,lPainterView:s.default,lPainterQrcode:c.default},props:{config:{type:Object,default:function(){return{}}},shareId:{type:[Number,String],default:""},qrcode:{type:[String],default:""},pagePath:{type:String,default:""},link:{type:String,default:""},type:{type:String,default:"goods"}},data:function(){return{}},computed:{price:function(){var t=this.config.price;return console.log(t),void 0==t?{}:(t=String(parseFloat(t)).split("."),{prev:t[0],next:t[1]?".".concat(t[1]):""})},marketPrice:function(){return"￥".concat(parseFloat(this.config.marketPrice))}},methods:{handleSuccess:function(t){this.$emit("success",t)},handleFail:function(t){this.$emit("fail")}}};e.default=d},"1c59":function(t,e,i){"use strict";var n=i("6d61"),a=i("6566");n("Set",(function(t){return function(){return t(this,arguments.length?arguments[0]:void 0)}}),a)},"1c75":function(t,e,i){"use strict";var n=i("7a54"),a=i.n(n);a.a},"1de5":function(t,e,i){"use strict";t.exports=function(t,e){return e||(e={}),t=t&&t.__esModule?t.default:t,"string"!==typeof t?t:(/^['"].*['"]$/.test(t)&&(t=t.slice(1,-1)),e.hash&&(t+=e.hash),/["'() \t\n]/.test(t)||e.needQuotes?'"'.concat(t.replace(/"/g,'\\"').replace(/\n/g,"\\n"),'"'):t)}},"21ab":function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return r})),i.d(e,"a",(function(){return n}));var n={uIcon:i("90f3").default,uPopup:i("5cc5").default,couponList:i("78b4").default},a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"get-coupon",style:[t.wrapStyle]},[t.list.length?i("v-uni-view",{staticClass:"coupons flex bg-white",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.showCoupon=!0}}},[i("v-uni-view",{staticClass:"flex-1 flex"},[i("v-uni-view",{staticClass:"con flex flex-1"},t._l(t.list,(function(e,n){return i("v-uni-view",{key:n,staticClass:"coupons-item m-r-20"},[n<2?i("v-uni-view",{staticClass:"flex xs"},[i("v-uni-view",{staticClass:"line-1"},[t._v(t._s(e.condition_type_desc))])],1):t._e()],1)})),1),i("v-uni-text",{staticStyle:{color:"red"}},[t._v("领券")]),i("u-icon",{staticStyle:{color:"red"},attrs:{name:"arrow-right"}})],1)],1):t._e(),i("u-popup",{attrs:{closeable:!0,mode:"bottom","border-radius":"14"},model:{value:t.showCoupon,callback:function(e){t.showCoupon=e},expression:"showCoupon"}},[i("v-uni-view",[i("v-uni-view",{staticClass:"p-30"},[i("v-uni-view",{staticClass:"title md bold"},[t._v("领券")])],1),i("v-uni-view",{staticClass:"content bg-body"},[i("v-uni-scroll-view",{staticStyle:{height:"700rpx"},attrs:{"scroll-y":"true"}},[i("coupon-list",{attrs:{list:t.list,"btn-type":3},on:{refresh:function(e){arguments[0]=e=t.$handleEvent(e),t.getGoodsCouponFun.apply(void 0,arguments)}}})],1)],1)],1)],1)],1)},r=[]},2266:function(t,e,i){"use strict";var n=i("0366"),a=i("c65b"),r=i("825a"),o=i("0d51"),s=i("e95a"),c=i("07fa"),d=i("3a9b"),u=i("9a1f"),l=i("35a1"),f=i("2a62"),h=TypeError,p=function(t,e){this.stopped=t,this.result=e},v=p.prototype;t.exports=function(t,e,i){var g,m,b,w,x,y,k,S=i&&i.that,A=!(!i||!i.AS_ENTRIES),z=!(!i||!i.IS_RECORD),C=!(!i||!i.IS_ITERATOR),_=!(!i||!i.INTERRUPTED),O=n(e,S),I=function(t){return g&&f(g,"normal",t),new p(!0,t)},T=function(t){return A?(r(t),_?O(t[0],t[1],I):O(t[0],t[1])):_?O(t,I):O(t)};if(z)g=t.iterator;else if(C)g=t;else{if(m=l(t),!m)throw new h(o(t)+" is not iterable");if(s(m)){for(b=0,w=c(t);w>b;b++)if(x=T(t[b]),x&&d(v,x))return x;return new p(!1)}g=u(t,m)}y=z?t.next:g.next;while(!(k=a(y,g)).done){try{x=T(k.value)}catch(P){f(g,"throw",P)}if("object"==typeof x&&x&&d(v,x))return x}return new p(!1)}},2286:function(t,e,i){var n=i("24fb");e=n(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.root[data-v-a36128d0]{position:relative;width:%?750?%;height:300px;overflow:hidden}.posterImg[data-v-a36128d0],\n.video[data-v-a36128d0],\n.box[data-v-a36128d0]{display:flex;width:%?750?%;height:300px;position:absolute}.video[data-v-a36128d0]{margin-left:-2000px}.box[data-v-a36128d0]{justify-content:center;align-items:center}.playIcon[data-v-a36128d0]{width:%?100?%;height:%?100?%}',""]),t.exports=e},"22f5":function(t,e,i){"use strict";i("7a82");var n=i("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=n(i("f07e")),r=n(i("c964")),o=n(i("f3f3"));i("a9e3"),i("99af"),i("14d9"),i("d3b7");var s=i("20b7"),c=i("fe5f"),d=i("26cb"),u=n(i("e087")),l={components:{poster:u.default},props:{value:{type:Boolean},shareId:{type:[String,Number],default:""},config:{type:Object,default:function(){return{}}},pagePath:{type:String,default:""},type:{type:String,default:"goods"}},data:function(){return{poster:"",enablePoster:!1,showPoster:!1,showTips:!1,mnpQrcode:""}},computed:(0,o.default)((0,o.default)({},(0,d.mapGetters)(["inviteCode","userInfo"])),{},{getLink:function(){return"".concat(c.baseURL).concat(c.basePath,"/").concat(this.pagePath,"?id=").concat(this.shareId,"&invite_code=").concat(this.inviteCode)},showshare:{get:function(){return this.value},set:function(t){this.$emit("input",t)}}}),watch:{showPoster:function(t){t||(this.enablePoster=!1)}},methods:{getPoster:function(){var t=this;return(0,r.default)((0,a.default)().mark((function e(){return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t.isLogin){e.next=2;break}return e.abrupt("return",t.$Router.push("/pages/login/login"));case 2:uni.showLoading({title:"正在生成中"}),t.enablePoster=!0;case 4:case"end":return e.stop()}}),e)})))()},getMnpQrcode:function(){var t=this;return new Promise((function(e,i){(0,s.apiMnpQrCode)({id:t.shareId,url:t.pagePath,type:t.type}).then((function(t){e(t)})).catch((function(){i()}))}))},getTtQrCode:function(){var t=this;return new Promise((function(e,i){var n=uni.getSystemInfoSync();(0,s.apiTtQrCode)({appname:TtAppNameEnum[n.appName],id:t.shareId,page:t.pagePath}).then((function(t){e(t)})).catch((function(){uni.hideLoading()}))}))},handleSuccess:function(t){this.poster=t,uni.hideLoading(),this.showPoster=!0,this.showshare=!1},handleFail:function(){var t=this;uni.hideLoading({success:function(){t.$toast({title:"生成失败"})}})},shareWx:function(){this.showTips=!0,this.showshare=!1},savePoster:function(){var t=this;return(0,r.default)((0,a.default)().mark((function e(){return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:uni.saveImageToPhotosAlbum({filePath:t.poster,success:function(e){t.showPoster=!1,t.$toast({title:"保存成功",icon:"success"})},fail:function(e){t.$toast({title:"保存失败"}),console.log(e)}});case 1:case"end":return e.stop()}}),e)})))()}}};e.default=l},2399:function(t,e,i){"use strict";var n=i("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.children=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};e.indexKey;return{inject:(0,o.default)({},t,{default:null}),watch:{el:{handler:function(t,e){JSON.stringify(t)!=JSON.stringify(e)&&this.bindRelation()},deep:!0,immediate:!0},src:{handler:function(t,e){t!=e&&this.bindRelation()},immediate:!0},text:{handler:function(t,e){t!=e&&this.bindRelation()},immediate:!0},css:{handler:function(t,e){t!=e&&(this.el.css="object"==(0,r.default)(t)?t:t&&Object.assign.apply(Object,(0,a.default)(s(t)))||{})},immediate:!0},replace:{handler:function(t,e){JSON.stringify(t)!=JSON.stringify(e)&&this.bindRelation()},deep:!0,immediate:!0}},created:function(){var e=this;Object.defineProperty(this,"parent",{get:function(){return e[t]}}),Object.defineProperty(this,"index",{get:function(){var t,i;return e.bindRelation(),null===(t=e.parent)||void 0===t||null===(i=t.el.views)||void 0===i?void 0:i.indexOf(e.el)}}),this.el.type=this.type},beforeDestroy:function(){var t=this;this.parent&&(this.parent.el.views=this.parent.el.views.filter((function(e){return e._uid!==t._uid})))},methods:{bindRelation:function(){var t,e,i,n,r=this;(this.el._uid||(this.el._uid=this._uid),["text","qrcode"].includes(this.type))&&(this.el.text=(null===(t=this.$slots)||void 0===t||null===(e=t.default)||void 0===e||null===(i=e[0])||void 0===i?void 0:i.text)||(null===(n=this.text)||void 0===n?void 0:n.replace(/\\n/g,"\n")));if("text"==this.type&&this.replace&&(this.el.replace=this.replace),"image"==this.type&&(this.el.src=this.src),this.parent){var o=this.parent.el.views||[];-1!==o.indexOf(this.el)?this.parent.el.views=o.map((function(t){return t._uid==r._uid?r.el:t})):this.parent.el.views=[].concat((0,a.default)(o),[this.el])}}},mounted:function(){this.bindRelation()}}},e.parent=function(t){return{provide:function(){return(0,o.default)({},t,this)},data:function(){return{el:{css:{},views:[]}}},watch:{css:{handler:function(t){var e,i;this.canvasId&&(this.el.css="object"==(0,r.default)(t)?t:t&&Object.assign.apply(Object,(0,a.default)(s(t)))||{},this.canvasWidth=(null===(e=this.el.css)||void 0===e?void 0:e.width)||this.canvasWidth,this.canvasHeight=(null===(i=this.el.css)||void 0===i?void 0:i.height)||this.canvasHeight)},immediate:!0}}}};var a=n(i("d0ff")),r=n(i("0122")),o=n(i("fc11"));i("d81d"),i("4de4"),i("d3b7"),i("ac1f"),i("00b4"),i("5319"),i("e9c4"),i("7a82"),i("c975"),i("caad6"),i("99af");i("7f84");var s=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return t.split(";").filter((function(t){return t&&!/^[\n\s]+$/.test(t)})).map((function(t){var e,i,n=t.split(":");return(0,o.default)({},n[0].replace(/-([a-z])/g,(function(){return arguments[1].toUpperCase()})).replace(/\s+/g,""),(null===n||void 0===n||null===(e=n[1])||void 0===e||null===(i=e.replace(/^\s+/,""))||void 0===i?void 0:i.replace(/\s+$/,""))||"")}))}},"23f2":function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){}));var n=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view")},a=[]},2669:function(t,e,i){"use strict";i.r(e);var n=i("23f2"),a=i("ffe8");for(var r in a)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(r);var o=i("f0c5"),s=Object(o["a"])(a["default"],n["b"],n["c"],!1,null,"70de87fc",null,!1,n["a"],void 0);e["default"]=s.exports},2875:function(t,e,i){"use strict";i.r(e);var n=i("a712"),a=i("c13e");for(var r in a)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(r);i("8fed");var o=i("f0c5"),s=Object(o["a"])(a["default"],n["b"],n["c"],!1,null,"061dd044",null,!1,n["a"],void 0);e["default"]=s.exports},"2ab4":function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){}));var n=function(){var t=this.$createElement,e=this._self._c||t;return this.show?e("v-uni-view",{staticClass:"u-loading",class:"circle"==this.mode?"u-loading-circle":"u-loading-flower",style:[this.cricleStyle]}):this._e()},a=[]},"2cf8":function(t,e,i){"use strict";var n=i("159d"),a=i.n(n);a.a},"2f9a":function(t,e,i){"use strict";i.r(e);var n=i("ec1d"),a=i.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(r);e["default"]=a.a},"2fb7":function(t,e,i){"use strict";i.r(e);var n=i("1b85"),a=i.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(r);e["default"]=a.a},3178:function(t,e,i){"use strict";i.r(e);var n=i("a51c"),a=i("c4b5");for(var r in a)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(r);var o=i("f0c5"),s=Object(o["a"])(a["default"],n["b"],n["c"],!1,null,"5f2f7706",null,!1,n["a"],void 0);e["default"]=s.exports},3249:function(t,e,i){"use strict";i("7a82");var n=i("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("99af");var a=n(i("f07e")),r=n(i("c964")),o=n(i("bde1")),s=i("9953"),c={mixins:[o.default],props:{autoGetData:{type:Boolean,default:!0}},data:function(){return{goodsList:[],active:0,columnList:[],upOption:{auto:!1,empty:{icon:"/static/images/goods_null.png",tip:"暂无商品"},toTop:{bottom:"300rpx"}},downOption:{use:!1,isLock:!0},hasData:!0}},mounted:function(){this.autoGetData&&this.getData()},methods:{getData:function(){var t=this;return(0,r.default)((0,a.default)().mark((function e(){return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.getGoodsColumnFun();case 2:t.$refs.uWaterfall&&t.$refs.uWaterfall.clear(),t.mescroll.resetUpScroll();case 4:case"end":return e.stop()}}),e)})))()},changeActive:function(t){this.active=t,this.$refs.uWaterfall.clear(),this.mescroll.resetUpScroll()},upCallback:function(t){var e=this,i=this.columnList,n=this.active,a=t.num,r=t.size;if(i.length){var o=i[n].id;(0,s.getGoodsListColumn)({page_size:r,page_no:a,column_id:o}).then((function(i){var n=i.data,a=n.lists,r=a.length,o=!!n.more;1==t.num&&(e.goodsList=[]),e.goodsList=e.goodsList.concat(a),e.mescroll.endSuccess(r,o)}))}},getGoodsColumnFun:function(){var t=this;return(0,r.default)((0,a.default)().mark((function e(){var i,n,r;return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,(0,s.getGoodsColumn)();case 2:i=e.sent,n=i.data,r=i.code,1==r&&(t.columnList=n,t.hasData=!!n.length);case 6:case"end":return e.stop()}}),e)})))()}}};e.default=c},"356a":function(t,e,i){var n=i("eca1");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=i("4f06").default;a("511a9b1b",n,!0,{sourceMap:!1,shadowMode:!1})},"356b":function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("a9e3");var n={name:"u-loading",props:{mode:{type:String,default:"circle"},color:{type:String,default:"#c7c7c7"},size:{type:[String,Number],default:"34"},show:{type:Boolean,default:!0}},computed:{cricleStyle:function(){var t={};return t.width=this.size+"rpx",t.height=this.size+"rpx","circle"==this.mode&&(t.borderColor="#e4e4e4 #e4e4e4 #e4e4e4 ".concat(this.color?this.color:"#c7c7c7")),t}}};e.default=n},"36c5":function(t,e,i){"use strict";var n=i("d7b0"),a=i.n(n);a.a},"37a5":function(t,e,i){"use strict";i.r(e);var n=i("666a"),a=i.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(r);e["default"]=a.a},"37dc1":function(t,e,i){"use strict";i.r(e);var n=i("c03f"),a=i.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(r);e["default"]=a.a},"3cd5":function(t,e,i){"use strict";i.r(e);var n=i("6777"),a=i.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(r);e["default"]=a.a},"3e48":function(t,e,i){"use strict";i("7a82");var n=i("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=n(i("f07e")),r=n(i("c964"));i("a9e3"),i("99af"),i("fb6a"),i("14d9"),i("a434"),i("e9c4"),i("c740");var o={name:"u-waterfall",props:{value:{type:Array,required:!0,default:function(){return[]}},addTime:{type:[Number,String],default:200},idKey:{type:String,default:"id"}},data:function(){return{leftList:[],rightList:[],tempList:[],children:[]}},watch:{copyFlowList:function(t,e){var i=Array.isArray(e)&&e.length>0?e.length:0;this.tempList=this.tempList.concat(this.cloneData(t.slice(i))),this.splitData()}},mounted:function(){this.tempList=this.cloneData(this.copyFlowList),this.splitData()},computed:{copyFlowList:function(){return this.cloneData(this.value)}},methods:{splitData:function(){var t=this;return(0,r.default)((0,a.default)().mark((function e(){var i,n,r;return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t.tempList.length){e.next=2;break}return e.abrupt("return");case 2:return e.next=4,t.$uGetRect("#u-left-column");case 4:return i=e.sent,e.next=7,t.$uGetRect("#u-right-column");case 7:if(n=e.sent,r=t.tempList[0],r){e.next=11;break}return e.abrupt("return");case 11:i.height<n.height?t.leftList.push(r):i.height>n.height?t.rightList.push(r):t.leftList.length<=t.rightList.length?t.leftList.push(r):t.rightList.push(r),t.tempList.splice(0,1),t.tempList.length&&setTimeout((function(){t.splitData()}),t.addTime);case 14:case"end":return e.stop()}}),e)})))()},cloneData:function(t){return JSON.parse(JSON.stringify(t))},clear:function(){this.leftList=[],this.rightList=[],this.$emit("input",[]),this.tempList=[]},remove:function(t){var e=this,i=-1;i=this.leftList.findIndex((function(i){return i[e.idKey]==t})),-1!=i?this.leftList.splice(i,1):(i=this.rightList.findIndex((function(i){return i[e.idKey]==t})),-1!=i&&this.rightList.splice(i,1)),i=this.value.findIndex((function(i){return i[e.idKey]==t})),-1!=i&&this.$emit("input",this.value.splice(i,1))},modify:function(t,e,i){var n=this,a=-1;if(a=this.leftList.findIndex((function(e){return e[n.idKey]==t})),-1!=a?this.leftList[a][e]=i:(a=this.rightList.findIndex((function(e){return e[n.idKey]==t})),-1!=a&&(this.rightList[a][e]=i)),a=this.value.findIndex((function(e){return e[n.idKey]==t})),-1!=a){var r=this.cloneData(this.value);r[a][e]=i,this.$emit("input",r)}}}};e.default=o},"40fd":function(t,e,i){var n=i("24fb");e=n(!1),e.push([t.i,".cu-progress[data-v-e51d8c58]{background-color:#e5e5e5;height:%?6?%;width:%?90?%;position:relative}.cu-progress .cu-progress-bar[data-v-e51d8c58]{height:100%;width:%?30?%;position:absolute;left:0}",""]),t.exports=e},"416f":function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=i("2399"),a={name:"lime-painter-qrcode",mixins:[(0,n.children)("painter")],props:{css:[String,Object],text:String},data:function(){return{type:"qrcode",el:{css:{},text:null}}}};e.default=a},4185:function(t,e,i){var n=i("701d");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=i("4f06").default;a("e2d009ca",n,!0,{sourceMap:!1,shadowMode:!1})},"481e":function(t,e,i){"use strict";i.r(e);var n=i("ac51"),a=i.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(r);e["default"]=a.a},"48d0":function(t,e,i){"use strict";i.r(e);var n=i("98b4"),a=i.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(r);e["default"]=a.a},4955:function(t,e,i){var n=i("e8a9");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=i("4f06").default;a("9c016ae0",n,!0,{sourceMap:!1,shadowMode:!1})},"4aa8":function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=i("2399"),a={name:"lime-painter-view",mixins:[(0,n.children)("painter"),(0,n.parent)("painter")],props:{css:[String,Object]},data:function(){return{type:"view",el:{css:{},views:[]}}}};e.default=a},"4e13":function(t,e,i){"use strict";i.r(e);var n=i("df3c"),a=i.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(r);e["default"]=a.a},"4e38":function(t,e,i){"use strict";i.r(e);var n=i("cb18"),a=i.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(r);e["default"]=a.a},"4f21":function(t,e,i){var n,a,r,o=i("62f5").default;i("6c57"),i("d3b7"),i("a4d3"),i("e01a"),i("d28b"),i("3ca3"),i("ddb0"),i("d9e2"),i("d401"),i("14d9"),i("ac1f"),i("00b4"),i("acd8"),i("5319"),i("e9c4"),i("466d"),i("2ca0"),i("c975"),i("498a"),i("baa5"),i("7a82"),i("159b"),i("b64b"),i("d81d"),i("caad6"),i("2532"),i("13d5"),i("fb6a"),i("cb29"),i("4de4"),i("07ac"),function(i,s){"object"==o(e)&&"undefined"!=typeof t?s(e):(a=[e],n=s,r="function"===typeof n?n.apply(e,a):n,void 0===r||(t.exports=r))}(0,(function(t){"use strict";
/*! *****************************************************************************
      Copyright (c) Microsoft Corporation.
  
      Permission to use, copy, modify, and/or distribute this software for any
      purpose with or without fee is hereby granted.
  
      THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
      REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
      AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
      INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
      LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
      OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
      PERFORMANCE OF THIS SOFTWARE.
      ***************************************************************************** */var e,i=function(){return(i=Object.assign||function(t){for(var e,i=1,n=arguments.length;i<n;i++)for(var a in e=arguments[i])Object.prototype.hasOwnProperty.call(e,a)&&(t[a]=e[a]);return t}).apply(this,arguments)};function n(t,e,i,n){return new(i||(i=Promise))((function(a,r){function o(t){try{c(n.next(t))}catch(t){r(t)}}function s(t){try{c(n.throw(t))}catch(t){r(t)}}function c(t){var e;t.done?a(t.value):(e=t.value,e instanceof i?e:new i((function(t){t(e)}))).then(o,s)}c((n=n.apply(t,e||[])).next())}))}function a(t,e){var i,n,a,r,o={label:0,sent:function(){if(1&a[0])throw a[1];return a[1]},trys:[],ops:[]};return r={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(r[Symbol.iterator]=function(){return this}),r;function s(r){return function(s){return function(r){if(i)throw new TypeError("Generator is already executing.");for(;o;)try{if(i=1,n&&(a=2&r[0]?n.return:r[0]?n.throw||((a=n.return)&&a.call(n),0):n.next)&&!(a=a.call(n,r[1])).done)return a;switch(n=0,a&&(r=[2&r[0],a.value]),r[0]){case 0:case 1:a=r;break;case 4:return o.label++,{value:r[1],done:!1};case 5:o.label++,n=r[1],r=[0];continue;case 7:r=o.ops.pop(),o.trys.pop();continue;default:if(a=o.trys,!((a=a.length>0&&a[a.length-1])||6!==r[0]&&2!==r[0])){o=0;continue}if(3===r[0]&&(!a||r[1]>a[0]&&r[1]<a[3])){o.label=r[1];break}if(6===r[0]&&o.label<a[1]){o.label=a[1],a=r;break}if(a&&o.label<a[2]){o.label=a[2],o.ops.push(r);break}a[2]&&o.ops.pop(),o.trys.pop();continue}r=e.call(t,o)}catch(t){r=[6,t],n=0}finally{i=a=0}if(5&r[0])throw r[1];return{value:r[0]?r[1]:void 0,done:!0}}([r,s])}}}var r={upx2px:function(t){return window.innerWidth/750*t},getSystemInfoSync:function(){return{screenWidth:window.innerWidth}},getImageInfo:function(t){var e=t.src,i=t.success,n=t.fail,a=new Image;a.onload=function(){i({width:this.naturalWidth,height:this.naturalHeight,path:this.src,src:e})},a.onerror=n,a.src=e}},s="object"==("undefined"===typeof swan?"undefined":o(swan))?"mp-baidu":"object"==("undefined"===typeof tt?"undefined":o(tt))?"mp-toutao":"object"==("undefined"===typeof plus?"undefined":o(plus))?"plus":"object"==("undefined"===typeof window?"undefined":o(window))?"undefined"==typeof uni||"undefined"!=typeof uni&&!(null===uni||void 0===uni?void 0:uni.addInterceptor)?"web":"h5":"mp-weixin",c="mp-weixin"==s?wx:"mp-toutao"==s?tt:"undefined"!=typeof uni?uni.upx2px?uni:Object.assign(uni,r):"undefined"!=typeof window?r:uni;if(!c.upx2px){var d=(null!==(e=c.getSystemInfoSync().screenWidth)&&void 0!==e?e:375)/750;c.upx2px=function(t){return d*t}}function u(t){return/^-?\d+(\.\d+)?$/.test(t)}function l(t,e,i){if("number"==typeof t)return t;if(u(t))return 1*t;if("string"==typeof t){var n=/^-?([0-9]+)?([.]{1}[0-9]+){0,1}(em|rpx|px|%)$/g.exec(t);if(!t||!n)return 0;var a=n[3];t=parseFloat(t);var r=0;return"rpx"===a?r=c.upx2px(t):"px"===a?r=1*t:"%"===a&&e?r=t*l(e)/100:"em"===a&&e&&(r=t*l(e||14)),i?1*r.toFixed(2):Math.round(r)}return 0}function f(t){var e=this;return new Promise((function(i,r){return n(e,void 0,void 0,(function(){var e,n;return a(this,(function(a){switch(a.label){case 0:if(e=t,"plus"!=s&&!/^mp/.test(s)||!/data:image\/(\w+);base64,(.*)/.test(t))return[3,4];a.label=1;case 1:return a.trys.push([1,3,,4]),[4,(o=t,new Promise((function(t,e){var i=/data:image\/(\w+);base64,(.*)/.exec(o)||[],n=i[1];if(i[2],/^mp/.test(s)){var a=c.getFileSystemManager();n||(console.error("ERROR_BASE64SRC_PARSE"),e(new Error("ERROR_BASE64SRC_PARSE")));var r=(new Date).getTime(),d=c.env.USER_DATA_PATH+"/"+r+"."+n;a.writeFile({filePath:d,data:o.replace(/^data:\S+\/\S+;base64,/,""),encoding:"base64",success:function(){t(d)},fail:function(t){console.error("获取base64图片失败",JSON.stringify(t)),e(t)}})}else if("plus"!=s)e(new Error("not support"));else{var u=o.split(",")[0].match(/data\:\S+\/(\S+);/);u?u=u[1]:e(new Error("base64 error"));var l=Date.now()+"."+u,f="_doc/uniapp_temp/"+l;if(!function(t,e){for(var i=t.split("."),n=e.split("."),a=!1,r=0;r<n.length;r++){var o=i[r]-n[r];if(0!==o){a=o>0;break}}return a}("Android"===plus.os.name?"1.9.9.80627":"1.9.9.80472",plus.runtime.innerVersion))return void plus.io.resolveLocalFileSystemURL("_doc",(function(i){i.getDirectory("uniapp_temp",{create:!0,exclusive:!1},(function(i){i.getFile(l,{create:!0,exclusive:!1},(function(i){i.createWriter((function(i){var n;i.onwrite=function(){t(f)},i.onerror=e,i.seek(0),i.writeAsBinary((n=o.split(","))[n.length-1])}),e)}),e)}),e)}),e);var h=new plus.nativeObj.Bitmap(l);h.loadBase64Data(o,(function(){h.save(f,{},(function(){h.clear(),t(f)}),(function(t){h.clear(),e(t)}))}),(function(t){h.clear(),e(t)}))}})))];case 2:return e=a.sent(),[3,4];case 3:return n=a.sent(),console.log(n),[3,4];case 4:return c.getImageInfo({src:e,success:function(t){return i(t)},fail:function(t){return r(t)}}),[2]}var o}))}))}))}function h(t){for(var e=[],i=[],n=0,a=t.substring(0,t.length-1).split("%,");n<a.length;n++){var r=a[n];e.push(r.substring(0,r.lastIndexOf(" ")).trim()),i.push(r.substring(r.lastIndexOf(" "),r.length)/100)}return{colors:e,percents:i}}function p(t,e,i){return e in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}function v(){return(v=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var i=arguments[e];for(var n in i)Object.prototype.hasOwnProperty.call(i,n)&&(t[n]=i[n])}return t}).apply(this,arguments)}var g=0,m={left:null,top:null,width:null,height:null},b=function(){function t(t,e,i,n){var a=this;p(this,"id",g++),p(this,"style",{left:null,top:null,width:null,height:null}),p(this,"computedStyle",{}),p(this,"children",{}),p(this,"layoutBox",v({},m)),p(this,"contentSize",v({},m,{maxLineHeight:0})),p(this,"clientSize",v({},m)),p(this,"borderSize",v({},m)),p(this,"offsetSize",v({},m)),this.ctx=n,this.root=i,e&&(this.parent=e),this.name=t.name||t.type,this.attributes=this.getAttributes(t);var r=this.getComputedStyle(t,null==e?void 0:e.computedStyle);this.isAbsolute="absolute"==r.position,this.isFixed="fixed"==r.position,Object.keys(r).forEach((function(t){Object.defineProperty(a.style,t,{configurable:!0,enumerable:!0,get:function(){return r[t]},set:function(e){r[t]=e}})}));var o={contentSize:v({},this.contentSize),clientSize:v({},this.clientSize),borderSize:v({},this.borderSize),offsetSize:v({},this.offsetSize)};Object.keys(o).forEach((function(t){Object.keys(a[t]).forEach((function(e){Object.defineProperty(a[t],e,{configurable:!0,enumerable:!0,get:function(){return o[t][e]},set:function(i){o[t][e]=i}})}))})),this.computedStyle=this.style}var e=t.prototype;return e.add=function(t){t.parent=this,this.children[t.id]=t},e.remove=function(t){var e=this;t?this.children[t.id]&&(t.remove(),delete this.children[t.id]):Object.keys(this.children).forEach((function(t){e.children[t].remove(),delete e.children[t]}))},e.getChildren=function(){var t=this;return Object.keys(this.children).map((function(e){return t.children[e]}))},e.getComputedStyle=function(t,e){var i=["color","fontSize","lineHeight","verticalAlign","fontWeight","textAlign"],n=t.css,a=void 0===n?{}:n,r=t.type,s=void 0===r?"view":r,c={};if(e)for(var d=0;d<i.length;d++){var f=i[d];(a[f]||e[f])&&(a[f]=a[f]||e[f])}for(var h=function(){var t=v[p],e=a[t];if(/^(box)?shadow$/i.test(t)){var i=e.split(" ").map((function(t){return/^\d/.test(t)?l(t):t}));return c.boxShadow=i,"continue"}if(/^border/i.test(t)&&!/radius$/i.test(t)){var n,r=t.match(/^border([BTRLa-z]+)?/)[0],d=t.match(/[W|S|C][a-z]+/),f=e.replace(/([\(,])\s+|\s+([\),])/g,"$1$2").split(" ").map((function(t){return/^\d/.test(t)?l(t,"",!0):t}));return c[r]=((n={})[r+"Width"]=(u(f[0])?f[0]:0)||1,n[r+"Style"]=f[1]||"solid",n[r+"Color"]=f[2]||"black",n),1==f.length&&d&&(c[r][r+d[0]]=f[0]),"continue"}if(/^background(color)?$/i.test(t))return c.backgroundColor=e,"continue";if(/^objectPosition$/i.test(t))return c[t]=e.split(" "),"continue";if(/padding|margin|radius/i.test(t)){var h=/radius$/i.test(t),g=h?"borderRadius":t.match(/[a-z]+/)[0],m=[0,0,0,0].map((function(t,e){return h?["borderTopLeftRadius","borderTopRightRadius","borderBottomRightRadius","borderBottomLeftRadius"][e]:[g+"Top",g+"Right",g+"Bottom",g+"Left"][e]}));if("padding"===t||"margin"===t||/^(border)?radius$/i.test(t)){var b,w=(null==e?void 0:e.split(" ").map((function(e){return/^\d+(rpx|px)?$/.test(e)?l(e):"margin"!=t&&/auto/.test(e)?0:e}),[]))||[0],x=h?"borderRadius":t,y=w[0],k=w[1],S=w[2],A=w[3];c[x]=((b={})[m[0]]="auto"==y?0:y,b[m[1]]=u(k)?k:y,b[m[2]]="auto"==(u(S)?S:y)?0:u(S)?S:y,b[m[3]]=u(A)?A:k||y,b)}else{var z;"object"==o(c[g])||(c[g]=((z={})[m[0]]=c[g]||0,z[m[1]]=c[g]||0,z[m[2]]=c[g]||0,z[m[3]]=c[g]||0,z)),c[g][t]="margin"==g&&"auto"==e||/%$/.test(e)?e:l(e)}return"continue"}if(/^transform$/i.test(t))return c[t]={},e.replace(/([a-zA-Z]+)\(([0-9,-\.%rpxdeg\s]+)\)/g,(function(e,i,n){var r=n.split(",").map((function(t){return t.replace(/(^\s*)|(\s*$)/g,"")})),o=function(t,e){return t.includes("deg")?1*t:e&&!/%$/.test(e)?l(t,e):t};i.includes("matrix")?c[t][i]=r.map((function(t){return 1*t})):i.includes("rotate")?c[t][i]=1*n.match(/^-?\d+(\.\d+)?/)[0]:/[X, Y]/.test(i)?c[t][i]=/[X]/.test(i)?o(r[0],a.width):o(r[0],a.height):(c[t][i+"X"]=o(r[0],a.width),c[t][i+"Y"]=o(r[1]||r[0],a.height))})),"continue";/^left|top$/i.test(t)&&!["absolute","fixed"].includes(a.position)?c[t]=0:c[t]=/^[\d\.]+(px|rpx)?$/.test(e)?l(e):/em$/.test(e)&&"text"==s?l(e,a.fontSize):e},p=0,v=Object.keys(a);p<v.length;p++)h();return c},e.setPosition=function(t,e){var i={left:"width",top:"height",right:"width",bottom:"height"};Object.keys(i).forEach((function(n){["right","bottom"].includes(n)&&void 0!==t.style[n]?t.style["right"==n?"left":"top"]=e[i[n]]-t.offsetSize[i[n]]-l(t.style[n],e[i[n]]):t.style[n]=l(t.style[n],e[i[n]])}))},e.getAttributes=function(t){var e=t.attributes||{};return(null!=t&&t.url||null!=t&&t.src)&&(e.src=e.src||t.url||(null==t?void 0:t.src)),t.replace&&(e.replace=t.replace),null!=t&&t.text&&(e.text=t.text),e},e.getOffsetSize=function(t,e,i){void 0===i&&(i="offsetSize");var n=e||{},a=n.margin,r=(a=void 0===a?{}:a).marginLeft,o=void 0===r?0:r,s=a.marginTop,c=void 0===s?0:s,d=a.marginRight,u=void 0===d?0:d,l=a.marginBottom,f=void 0===l?0:l,h=n.padding,p=(h=void 0===h?{}:h).paddingLeft,v=void 0===p?0:p,g=h.paddingTop,m=void 0===g?0:g,b=h.paddingRight,w=void 0===b?0:b,x=h.paddingBottom,y=void 0===x?0:x,k=n.border,S=(k=void 0===k?{}:k).borderWidth,A=void 0===S?0:S,z=n.borderTop,C=(z=void 0===z?{}:z).borderTopWidth,_=void 0===C?A:C,O=n.borderBottom,I=(O=void 0===O?{}:O).borderBottomWidth,T=void 0===I?A:I,P=n.borderRight,M=(P=void 0===P?{}:P).borderRightWidth,j=void 0===M?A:M,L=n.borderLeft,B=(L=void 0===L?{}:L).borderLeftWidth,N=void 0===B?A:B;return"contentSize"==i&&(this[i].left=t.left+o+v+N,this[i].top=t.top+c+m+_,this[i].width=t.width,this[i].height=t.height),"clientSize"==i&&(this[i].left=t.left+o+N,this[i].top=t.top+c+_,this[i].width=t.width+v+w,this[i].height=t.height+m+y),"borderSize"==i&&(this[i].left=t.left+o+N/2,this[i].top=t.top+c+_/2,this[i].width=t.width+v+w+N/2+j/2,this[i].height=t.height+m+y+T/2+_/2),"offsetSize"==i&&(this[i].left=t.left,this[i].top=t.top,this[i].width=t.width+v+w+N+j+o+u,this[i].height=t.height+m+y+T+_+f+c),this[i]},e.layoutBoxUpdate=function(t,e,i,n){if(void 0===i&&(i=""),"border-box"==(null==e?void 0:e.boxSizing)){var a=e||{},r=a.border,o=(r=void 0===r?{}:r).borderWidth,s=void 0===o?0:o,c=a.borderTop,d=(c=void 0===c?{}:c).borderTopWidth,u=void 0===d?s:d,l=a.borderBottom,f=(l=void 0===l?{}:l).borderBottomWidth,h=void 0===f?s:f,p=a.borderRight,v=(p=void 0===p?{}:p).borderRightWidth,g=void 0===v?s:v,m=a.borderLeft,b=(m=void 0===m?{}:m).borderLeftWidth,w=void 0===b?s:b,x=a.padding,y=(x=void 0===x?{}:x).paddingTop,k=void 0===y?0:y,S=x.paddingRight,A=void 0===S?0:S,z=x.paddingBottom,C=void 0===z?0:z,_=x.paddingLeft,O=void 0===_?0:_;"width"==i&&(t.width-=O+A+g+w),"height"!=i||n||(t.height-=k+C+u+h)}this.layoutBox.contentSize=this.getOffsetSize(t,e,"contentSize"),this.layoutBox.clientSize=this.getOffsetSize(t,e,"clientSize"),this.layoutBox.borderSize=this.getOffsetSize(t,e,"borderSize"),this.layoutBox.offsetSize=this.getOffsetSize(t,e,"offsetSize"),this.layoutBox=Object.assign({},this.layoutBox,this.layoutBox.borderSize)},e.getBoxPosition=function(t){var e=this.computedStyle,i=this.getChildren(),n=e.verticalAlign,a=e.left,r=void 0===a?0:a,o=e.top,s=void 0===o?0:o,c=v({},this.contentSize,{left:r,top:s}),d=this.contentSize.top-this.offsetSize.top,u=this.contentSize.left-this.offsetSize.left,l=0;if("bottom"==n&&this.contentSize.maxLineHeight?l=this.contentSize.maxLineHeight-this.contentSize.height:"middle"==n&&this.contentSize.maxLineHeight&&(l=(this.contentSize.maxLineHeight-this.contentSize.height)/2),c.top+=l,i.length){r+=u,s+=d;for(var f=null,h=null,p=!1,g=0;g<i.length;g++){var m=i[g];if(m.isAbsolute||m.isFixed)m.isAbsolute?(m.setPosition(m,c),m.style.left+=r,m.style.top+=s,m.getBoxPosition()):(m.setPosition(m,this.root),m.getBoxPosition());else if(0==g)m.style.left+=r,m.style.top+=s,m.getBoxPosition(),f=m,h=m;else{var b,w,x,y;(null==(b=h)?void 0:b.offsetSize.height)<(null==(w=f)?void 0:w.offsetSize.height)&&(h=f);var k,S,A,z,C=(null==(x=f)?void 0:x.offsetSize.left)+(null==(y=f)?void 0:y.offsetSize.width)+m.offsetSize.width>c.left+c.width+u;if(this.getBoxState(f,m)||C)m.style.left+=r,(null==(k=f)?void 0:k.offsetSize.height)>=(null==(S=h)?void 0:S.offsetSize.height)?m.style.top+=f.offsetSize.top+f.offsetSize.height||0:m.style.top+=(null==(A=h)?void 0:A.offsetSize.top)+(null==(z=h)?void 0:z.offsetSize.height)||0,m.getBoxPosition(),f=m,h=m,p=!0;else m.style.left+=f.offsetSize.left+f.offsetSize.width,m.style.top+=p?f.offsetSize.top:s,m.getBoxPosition(),f=m}}this.layoutBoxUpdate(c,e)}else this.layoutBoxUpdate(c,e);return this.layoutBox},e.setMaxLineHeight=function(t,e,i){for(var n=t;n>=0&&!e[n].contentSize.maxLineHeight;)e[n].contentSize.maxLineHeight=i,n--},e.getBoxState=function(t,e){return"view"==e.name&&"inline-block"!==e.style.display||"view"==(null==t?void 0:t.name)&&"inline-block"!==(null==t?void 0:t.style.display)||"block"==e.style.display||"block"==(null==t?void 0:t.style.display)},e.getBoxHieght=function(){var t,e=this,i=this.name,n=this.computedStyle,a=this.attributes,r=this.parent,o=this.getChildren(),s=n.top,c=n.bottom,d=n.height,u=void 0===d?0:d,f=n.fontSize,h=void 0===f?14:f,p=n.position,g=n.lineHeight,m=void 0===g?"1.4em":g;n.lineClamp;var b=v({},this.contentSize);if("image"==i&&null==u){var w=a.width,x=a.height;a.mode,b.height=Math.round(b.width*x/w)||0,this.layoutBoxUpdate(b,n,"height")}else if(u)if(o.length){var y=null,k=0;o.forEach((function(t,i){var n,a,r=i==o.length-1;t.getBoxHieght();var s=(null==(n=y)?void 0:n.offsetSize.left)+(null==(a=y)?void 0:a.offsetSize.width)+t.offsetSize.width>b.left+b.width,c=e.getBoxState(y,t);if(s||c){if(s){for(var d=i-1;d>=0&&!o[d].contentSize.maxLineHeight;)k<o[d].contentSize.height&&(k=o[d].contentSize.height),d--;e.setMaxLineHeight(i-1,o,k),k=0}}else if(r){for(var u=i;u>=0&&!o[u].contentSize.maxLineHeight;)k<o[u].contentSize.height&&(k=o[u].contentSize.height),u--;e.setMaxLineHeight(i,o,k),k=0}y=t}))}else this.layoutBoxUpdate(b,n,"height");else{var S=0;if(null!==s&&(this.isAbsolute||this.isFixed&&r.contentSize.height)){var A="absolute"==p?r.contentSize.height:this.root.height;S=A-(/%$/.test(s)?l(s,A):s)-(/%$/.test(c)?l(c,A):c)}if("text"==i)m=l(m,h),b.height=S||this.attributes.lines*m,this.layoutBoxUpdate(b,n,"height",!0);else if(o.length){var z=0,C=null,_=0;b.height=o.reduce((function(t,i,n){var a=n==o.length-1;if(i.isAbsolute||i.isFixed)return a?t+z:t;i.getBoxHieght();var r=e.getBoxState(C,i),s=_+i.offsetSize.width>b.width;if(s||r){var c,d,u=0;return s||C&&("view"!==(null==(c=C)?void 0:c.name)||"inline-block"==(null==(d=C)?void 0:d.style.display))?(a&&(e.setMaxLineHeight(n-1,o,z),z+=i.offsetSize.height),u=t+z,z=i.offsetSize.height,_=i.offsetSize.width,C=i,u):(_=0,z=0,t+i.offsetSize.height)}return _+=i.offsetSize.width,z=Math.max(z,i.offsetSize.height)||0,a?(e.setMaxLineHeight(n,o,z),t+z):(C=i,t)}),0),S&&(b.height=S),this.layoutBoxUpdate(b,n)}else S&&(b.height=S),this.layoutBoxUpdate(b,n,"height")}if(n.borderRadius&&null!=(t=this.borderSize)&&t.width)for(var O in n.borderRadius)Object.hasOwnProperty.call(n.borderRadius,O)&&(n.borderRadius[O]=l(n.borderRadius[O],this.borderSize.width));return this.layoutBox},e.contrastSize=function(t,e,i){var n=t;return i&&(n=Math.min(n,i)),e&&(n=Math.max(n,e)),n},e.measureText=function(t,e){var i=this.ctx.measureText(t);return{width:i.width,fontHeight:(i.actualBoundingBoxAscent||.7*e)+1}},e.getBoxWidth=function(){var t,e=this,i=this.name,n=this.computedStyle,a=this.attributes,r=this.parent,o=void 0===r?{}:r,s=this.ctx,c=this.getChildren(),d=n.left,u=void 0===d?0:d;n.top;var f=n.right,h=n.width,p=void 0===h?0:h,v=n.minWidth,g=n.maxWidth,m=n.height,b=void 0===m?0:m,w=n.fontSize,x=void 0===w?14:w,y=n.fontWeight,k=n.fontFamily,S=n.textStyle,A=n.position,z=n.display,C=n.lineClamp,_=n.padding,O=void 0===_?{}:_,I=n.margin,T=void 0===I?{}:I,P=n.border,M=(P=void 0===P?{}:P).borderWidth,j=void 0===M?0:M,L=n.borderRight,B=(L=void 0===L?{}:L).borderRightWidth,N=void 0===B?j:B,D=n.borderLeft,X=(D=void 0===D?{}:D).borderLeftWidth,R=void 0===X?j:X;if(/%$/.test(p)&&o.contentSize.width&&(p=l(p,o.contentSize.width,!0)),/%$/.test(b)&&o.contentSize.height&&(b=l(b,o.contentSize.height)),/%$/.test(v)&&o.contentSize.width&&(v=l(v,o.contentSize.width,!0)),/%$/.test(g)&&o.contentSize.width&&(g=l(g,o.contentSize.width,!0)),n.padding&&null!=(t=o.contentSize)&&t.width)for(var H in n.padding)Object.hasOwnProperty.call(n.padding,H)&&(n.padding[H]=l(n.padding[H],o.contentSize.width));var F=O.paddingRight,V=void 0===F?0:F,W=O.paddingLeft,G=void 0===W?0:W;if(n.margin&&[n.margin.marginLeft,n.margin.marginRight].includes("auto"))if(p){var q=o.contentSize.width-p-V-G-R-N||0;n.margin.marginLeft==n.margin.marginRight?n.margin.marginLeft=n.margin.marginRight=q/2:"auto"==n.margin.marginLeft?n.margin.marginLeft=q:n.margin.marginRight=q}else n.margin.marginLeft=n.margin.marginRight=0;var E=T.marginRight,Z=void 0===E?0:E,U=T.marginLeft,J={width:p,height:b,left:0,top:0},Q=G+V+R+N+(void 0===U?0:U)+Z;if("text"==i&&!this.attributes.widths){var Y=a.text||"";s.save(),s.setFonts({fontFamily:k,fontSize:x,fontWeight:y,textStyle:S}),Y.split("\n").map((function(t){var i=t.split("").map((function(t){return e.measureText(t,x).width}));e.attributes.fontHeight=e.measureText(t,x).fontHeight,e.attributes.widths||(e.attributes.widths=[]),e.attributes.widths.push({widths:i,total:i.reduce((function(t,e){return t+e}),0)})})),s.restore()}if("image"==i&&null==p){var K=a.width,$=a.height;J.width=this.contrastSize(Math.round(K*b/$)||0,v,g),this.layoutBoxUpdate(J,n,"width")}else if(p)c.length?(this.layoutBoxUpdate(J,n,"width"),c.forEach((function(t){t.getBoxWidth()}))):this.layoutBoxUpdate(J,n,"width");else{var tt=0;if((this.isAbsolute||this.isFixed)&&o.contentSize.width){var et="absolute"==A?o.contentSize.width:this.root.width;tt=et-(/%$/.test(u)?l(u,et):u)-(/%$/.test(f)?l(f,et):f)}if("text"==i){var it=this.attributes.widths,nt=Math.max.apply(Math,it.map((function(t){return t.total})));o&&o.contentSize.width>0&&(nt>o.contentSize.width||"block"==z)&&!this.isAbsolute&&!this.isFixed&&(nt=o.contentSize.width-Q),J.width=tt||this.contrastSize(nt,v,g),this.layoutBoxUpdate(J,n,"width")}else if("view"!=i||!o||"inline-block"===z||this.isAbsolute||this.isFixed)if(c.length){for(var at=0,rt=null,ot=0;c.length>ot;){var st=c[ot],ct=ot==c.length-1,dt=this.getBoxState(rt,st);if(!st.isFixed&&!st.isAbsolute)if(!rt||dt){var ut=st.getBoxWidth();at=Math.max(at,ut.width)||0,rt=st}else if(rt.offsetSize.left+rt.offsetSize.width+st.offsetSize.width<o.contentSize.width&&ot!==c.length-1)at+=st.getBoxWidth().width,rt=st;else{var lt=st.getBoxWidth();ct?at+=lt.width:at=o.contentSize.width,rt=null}ot++}J.width=tt||this.contrastSize(Math.ceil(at),v,g),c.forEach((function(t){"block"!=t.style.display||"text"!=t.name||t.isFixed||t.isAbsolute||t.style.width||(t.style.width=J.width,t.getBoxWidth())})),this.layoutBoxUpdate(J,n,"width")}else J.width=tt,this.layoutBoxUpdate(J,n,"width");else J.width=this.contrastSize(o.contentSize.width-Q,v,g),this.layoutBoxUpdate(J,n),c.length&&c.forEach((function(t){t.getBoxWidth()}))}if("text"==i&&!this.attributes.lines){var ft=this.attributes.widths.length;this.attributes.widths.forEach((function(t){return t.widths.reduce((function(t,e,i){return t+e>J.width?(ft++,e):t+e}),0)})),ft=C&&ft>C?C:ft,this.attributes.lines=ft}return this.layoutBox},e.layout=function(){return this.getBoxWidth(),this.getBoxHieght(),this.getBoxPosition(),this.offsetSize},t}(),w=function(){var t,e,i,n,a,r,o=[0,11,15,19,23,27,31,16,18,20,22,24,26,28,20,22,24,24,26,28,28,22,24,24,26,26,28,28,24,24,26,26,26,28,28,24,26,26,26,28,28],s=[3220,1468,2713,1235,3062,1890,2119,1549,2344,2936,1117,2583,1330,2470,1667,2249,2028,3780,481,4011,142,3098,831,3445,592,2517,1776,2234,1951,2827,1070,2660,1345,3177],c=[30660,29427,32170,30877,26159,25368,27713,26998,21522,20773,24188,23371,17913,16590,20375,19104,13663,12392,16177,14854,9396,8579,11994,11245,5769,5054,7399,6608,1890,597,3340,2107],d=[1,0,19,7,1,0,16,10,1,0,13,13,1,0,9,17,1,0,34,10,1,0,28,16,1,0,22,22,1,0,16,28,1,0,55,15,1,0,44,26,2,0,17,18,2,0,13,22,1,0,80,20,2,0,32,18,2,0,24,26,4,0,9,16,1,0,108,26,2,0,43,24,2,2,15,18,2,2,11,22,2,0,68,18,4,0,27,16,4,0,19,24,4,0,15,28,2,0,78,20,4,0,31,18,2,4,14,18,4,1,13,26,2,0,97,24,2,2,38,22,4,2,18,22,4,2,14,26,2,0,116,30,3,2,36,22,4,4,16,20,4,4,12,24,2,2,68,18,4,1,43,26,6,2,19,24,6,2,15,28,4,0,81,20,1,4,50,30,4,4,22,28,3,8,12,24,2,2,92,24,6,2,36,22,4,6,20,26,7,4,14,28,4,0,107,26,8,1,37,22,8,4,20,24,12,4,11,22,3,1,115,30,4,5,40,24,11,5,16,20,11,5,12,24,5,1,87,22,5,5,41,24,5,7,24,30,11,7,12,24,5,1,98,24,7,3,45,28,15,2,19,24,3,13,15,30,1,5,107,28,10,1,46,28,1,15,22,28,2,17,14,28,5,1,120,30,9,4,43,26,17,1,22,28,2,19,14,28,3,4,113,28,3,11,44,26,17,4,21,26,9,16,13,26,3,5,107,28,3,13,41,26,15,5,24,30,15,10,15,28,4,4,116,28,17,0,42,26,17,6,22,28,19,6,16,30,2,7,111,28,17,0,46,28,7,16,24,30,34,0,13,24,4,5,121,30,4,14,47,28,11,14,24,30,16,14,15,30,6,4,117,30,6,14,45,28,11,16,24,30,30,2,16,30,8,4,106,26,8,13,47,28,7,22,24,30,22,13,15,30,10,2,114,28,19,4,46,28,28,6,22,28,33,4,16,30,8,4,122,30,22,3,45,28,8,26,23,30,12,28,15,30,3,10,117,30,3,23,45,28,4,31,24,30,11,31,15,30,7,7,116,30,21,7,45,28,1,37,23,30,19,26,15,30,5,10,115,30,19,10,47,28,15,25,24,30,23,25,15,30,13,3,115,30,2,29,46,28,42,1,24,30,23,28,15,30,17,0,115,30,10,23,46,28,10,35,24,30,19,35,15,30,17,1,115,30,14,21,46,28,29,19,24,30,11,46,15,30,13,6,115,30,14,23,46,28,44,7,24,30,59,1,16,30,12,7,121,30,12,26,47,28,39,14,24,30,22,41,15,30,6,14,121,30,6,34,47,28,46,10,24,30,2,64,15,30,17,4,122,30,29,14,46,28,49,10,24,30,24,46,15,30,4,18,122,30,13,32,46,28,48,14,24,30,42,32,15,30,20,4,117,30,40,7,47,28,43,22,24,30,10,67,15,30,19,6,118,30,18,31,47,28,34,34,24,30,20,61,15,30],u=[255,0,1,25,2,50,26,198,3,223,51,238,27,104,199,75,4,100,224,14,52,141,239,129,28,193,105,248,200,8,76,113,5,138,101,47,225,36,15,33,53,147,142,218,240,18,130,69,29,181,194,125,106,39,249,185,201,154,9,120,77,228,114,166,6,191,139,98,102,221,48,253,226,152,37,179,16,145,34,136,54,208,148,206,143,150,219,189,241,210,19,92,131,56,70,64,30,66,182,163,195,72,126,110,107,58,40,84,250,133,186,61,202,94,155,159,10,21,121,43,78,212,229,172,115,243,167,87,7,112,192,247,140,128,99,13,103,74,222,237,49,197,254,24,227,165,153,119,38,184,180,124,17,68,146,217,35,32,137,46,55,63,209,91,149,188,207,205,144,135,151,178,220,252,190,97,242,86,211,171,20,42,93,158,132,60,57,83,71,109,65,162,31,45,67,216,183,123,164,118,196,23,73,236,127,12,111,246,108,161,59,82,41,157,85,170,251,96,134,177,187,204,62,90,203,89,95,176,156,169,160,81,11,245,22,235,122,117,44,215,79,174,213,233,230,231,173,232,116,214,244,234,168,80,88,175],l=[1,2,4,8,16,32,64,128,29,58,116,232,205,135,19,38,76,152,45,90,180,117,234,201,143,3,6,12,24,48,96,192,157,39,78,156,37,74,148,53,106,212,181,119,238,193,159,35,70,140,5,10,20,40,80,160,93,186,105,210,185,111,222,161,95,190,97,194,153,47,94,188,101,202,137,15,30,60,120,240,253,231,211,187,107,214,177,127,254,225,223,163,91,182,113,226,217,175,67,134,17,34,68,136,13,26,52,104,208,189,103,206,129,31,62,124,248,237,199,147,59,118,236,197,151,51,102,204,133,23,46,92,184,109,218,169,79,158,33,66,132,21,42,84,168,77,154,41,82,164,85,170,73,146,57,114,228,213,183,115,230,209,191,99,198,145,63,126,252,229,215,179,123,246,241,255,227,219,171,75,150,49,98,196,149,55,110,220,165,87,174,65,130,25,50,100,200,141,7,14,28,56,112,224,221,167,83,166,81,162,89,178,121,242,249,239,195,155,43,86,172,69,138,9,18,36,72,144,61,122,244,245,247,243,251,235,203,139,11,22,44,88,176,125,250,233,207,131,27,54,108,216,173,71,142,0],f=[],h=[],p=[],v=[],g=[],m=2;function b(t,e){var i;t>e&&(i=t,t=e,e=i),i=e,i*=e,i+=e,i>>=1,v[i+=t]=1}function w(t,i){var n;for(p[t+e*i]=1,n=-2;n<2;n++)p[t+n+e*(i-2)]=1,p[t-2+e*(i+n+1)]=1,p[t+2+e*(i+n)]=1,p[t+n+1+e*(i+2)]=1;for(n=0;n<2;n++)b(t-1,i+n),b(t+1,i-n),b(t-n,i-1),b(t+n,i+1)}function x(t){for(;t>=255;)t=((t-=255)>>8)+(255&t);return t}var y=[];function k(t,e,i,n){var a,r,o;for(a=0;a<n;a++)f[i+a]=0;for(a=0;a<e;a++){if(255!=(o=u[f[t+a]^f[i]]))for(r=1;r<n;r++)f[i+r-1]=f[i+r]^l[x(o+y[n-r])];else for(r=i;r<i+n;r++)f[r]=f[r+1];f[i+n-1]=255==o?0:l[x(o+y[0])]}}function S(t,e){var i;return t>e&&(i=t,t=e,e=i),i=e,i+=e*e,i>>=1,v[i+=t]}function A(t){var i,n,a,r;switch(t){case 0:for(n=0;n<e;n++)for(i=0;i<e;i++)i+n&1||S(i,n)||(p[i+n*e]^=1);break;case 1:for(n=0;n<e;n++)for(i=0;i<e;i++)1&n||S(i,n)||(p[i+n*e]^=1);break;case 2:for(n=0;n<e;n++)for(a=0,i=0;i<e;i++,a++)3==a&&(a=0),a||S(i,n)||(p[i+n*e]^=1);break;case 3:for(r=0,n=0;n<e;n++,r++)for(3==r&&(r=0),a=r,i=0;i<e;i++,a++)3==a&&(a=0),a||S(i,n)||(p[i+n*e]^=1);break;case 4:for(n=0;n<e;n++)for(a=0,r=n>>1&1,i=0;i<e;i++,a++)3==a&&(a=0,r=!r),r||S(i,n)||(p[i+n*e]^=1);break;case 5:for(r=0,n=0;n<e;n++,r++)for(3==r&&(r=0),a=0,i=0;i<e;i++,a++)3==a&&(a=0),(i&n&1)+!(!a|!r)||S(i,n)||(p[i+n*e]^=1);break;case 6:for(r=0,n=0;n<e;n++,r++)for(3==r&&(r=0),a=0,i=0;i<e;i++,a++)3==a&&(a=0),(i&n&1)+(a&&a==r)&1||S(i,n)||(p[i+n*e]^=1);break;case 7:for(r=0,n=0;n<e;n++,r++)for(3==r&&(r=0),a=0,i=0;i<e;i++,a++)3==a&&(a=0),(a&&a==r)+(i+n&1)&1||S(i,n)||(p[i+n*e]^=1)}}function z(t){var e,i=0;for(e=0;e<=t;e++)g[e]>=5&&(i+=3+g[e]-5);for(e=3;e<t-1;e+=2)g[e-2]==g[e+2]&&g[e+2]==g[e-1]&&g[e-1]==g[e+1]&&3*g[e-1]==g[e]&&(0==g[e-3]||e+3>t||3*g[e-3]>=4*g[e]||3*g[e+3]>=4*g[e])&&(i+=40);return i}function C(){var t,i,n,a,r,o=0,s=0;for(i=0;i<e-1;i++)for(t=0;t<e-1;t++)(p[t+e*i]&&p[t+1+e*i]&&p[t+e*(i+1)]&&p[t+1+e*(i+1)]||!(p[t+e*i]||p[t+1+e*i]||p[t+e*(i+1)]||p[t+1+e*(i+1)]))&&(o+=3);for(i=0;i<e;i++){for(g[0]=0,n=a=t=0;t<e;t++)(r=p[t+e*i])==a?g[n]++:g[++n]=1,s+=(a=r)?1:-1;o+=z(n)}s<0&&(s=-s);var c=s,d=0;for(c+=c<<2,c<<=1;c>e*e;)c-=e*e,d++;for(o+=10*d,t=0;t<e;t++){for(g[0]=0,n=a=i=0;i<e;i++)(r=p[t+e*i])==a?g[n]++:g[++n]=1,a=r;o+=z(n)}return o}var _=null;return{api:{get ecclevel(){return m},set ecclevel(t){m=t},get size(){return _size},set size(t){_size=t},get canvas(){return _},set canvas(t){_=t},getFrame:function(g){return function(g){var z,_,O,I,T,P,M,j;I=g.length,t=0;do{if(t++,O=4*(m-1)+16*(t-1),i=d[O++],n=d[O++],a=d[O++],r=d[O],I<=(O=a*(i+n)+n-3+(t<=9)))break}while(t<40);for(e=17+4*t,T=a+(a+r)*(i+n)+n,I=0;I<T;I++)h[I]=0;for(f=g.slice(0),I=0;I<e*e;I++)p[I]=0;for(I=0;I<(e*(e+1)+1)/2;I++)v[I]=0;for(I=0;I<3;I++){for(O=0,_=0,1==I&&(O=e-7),2==I&&(_=e-7),p[_+3+e*(O+3)]=1,z=0;z<6;z++)p[_+z+e*O]=1,p[_+e*(O+z+1)]=1,p[_+6+e*(O+z)]=1,p[_+z+1+e*(O+6)]=1;for(z=1;z<5;z++)b(_+z,O+1),b(_+1,O+z+1),b(_+5,O+z),b(_+z+1,O+5);for(z=2;z<4;z++)p[_+z+e*(O+2)]=1,p[_+2+e*(O+z+1)]=1,p[_+4+e*(O+z)]=1,p[_+z+1+e*(O+4)]=1}if(t>1)for(I=o[t],_=e-7;;){for(z=e-7;z>I-3&&(w(z,_),!(z<I));)z-=I;if(_<=I+9)break;w(6,_-=I),w(_,6)}for(p[8+e*(e-8)]=1,_=0;_<7;_++)b(7,_),b(e-8,_),b(7,_+e-7);for(z=0;z<8;z++)b(z,7),b(z+e-8,7),b(z,e-8);for(z=0;z<9;z++)b(z,8);for(z=0;z<8;z++)b(z+e-8,8),b(8,z);for(_=0;_<7;_++)b(8,_+e-7);for(z=0;z<e-14;z++)1&z?(b(8+z,6),b(6,8+z)):(p[8+z+6*e]=1,p[6+e*(8+z)]=1);if(t>6)for(I=s[t-7],O=17,z=0;z<6;z++)for(_=0;_<3;_++,O--)1&(O>11?t>>O-12:I>>O)?(p[5-z+e*(2-_+e-11)]=1,p[2-_+e-11+e*(5-z)]=1):(b(5-z,2-_+e-11),b(2-_+e-11,5-z));for(_=0;_<e;_++)for(z=0;z<=_;z++)p[z+e*_]&&b(z,_);for(T=f.length,P=0;P<T;P++)h[P]=f.charCodeAt(P);if(f=h.slice(0),T>=(z=a*(i+n)+n)-2&&(T=z-2,t>9&&T--),P=T,t>9){for(f[P+2]=0,f[P+3]=0;P--;)I=f[P],f[P+3]|=255&I<<4,f[P+2]=I>>4;f[2]|=255&T<<4,f[1]=T>>4,f[0]=64|T>>12}else{for(f[P+1]=0,f[P+2]=0;P--;)I=f[P],f[P+2]|=255&I<<4,f[P+1]=I>>4;f[1]|=255&T<<4,f[0]=64|T>>4}for(P=T+3-(t<10);P<z;)f[P++]=236,f[P++]=17;for(y[0]=1,P=0;P<r;P++){for(y[P+1]=1,M=P;M>0;M--)y[M]=y[M]?y[M-1]^l[x(u[y[M]]+P)]:y[M-1];y[0]=l[x(u[y[0]]+P)]}for(P=0;P<=r;P++)y[P]=u[y[P]];for(O=z,_=0,P=0;P<i;P++)k(_,a,O,r),_+=a,O+=r;for(P=0;P<n;P++)k(_,a+1,O,r),_+=a+1,O+=r;for(_=0,P=0;P<a;P++){for(M=0;M<i;M++)h[_++]=f[P+M*a];for(M=0;M<n;M++)h[_++]=f[i*a+P+M*(a+1)]}for(M=0;M<n;M++)h[_++]=f[i*a+P+M*(a+1)];for(P=0;P<r;P++)for(M=0;M<i+n;M++)h[_++]=f[z+P+M*r];for(f=h,z=_=e-1,O=T=1,j=(a+r)*(i+n)+n,P=0;P<j;P++)for(I=f[P],M=0;M<8;M++,I<<=1){128&I&&(p[z+e*_]=1);do{T?z--:(z++,O?0!=_?_--:(O=!O,6==(z-=2)&&(z--,_=9)):_!=e-1?_++:(O=!O,6==(z-=2)&&(z--,_-=8))),T=!T}while(S(z,_))}for(f=p.slice(0),I=0,_=3e4,O=0;O<8&&(A(O),(z=C())<_&&(_=z,I=O),7!=I);O++)p=f.slice(0);for(I!=O&&A(I),_=c[I+(m-1<<3)],O=0;O<8;O++,_>>=1)1&_&&(p[e-1-O+8*e]=1,O<6?p[8+e*O]=1:p[8+e*(O+1)]=1);for(O=0;O<7;O++,_>>=1)1&_&&(p[8+e*(e-7+O)]=1,O?p[6-O+8*e]=1:p[7+8*e]=1);return p}(g)},utf16to8:function(t){var e,i,n,a;for(e="",n=t.length,i=0;i<n;i++)(a=t.charCodeAt(i))>=1&&a<=127?e+=t.charAt(i):a>2047?(e+=String.fromCharCode(224|a>>12&15),e+=String.fromCharCode(128|a>>6&63),e+=String.fromCharCode(128|a>>0&63)):(e+=String.fromCharCode(192|a>>6&31),e+=String.fromCharCode(128|a>>0&63));return e},draw:function(t,i,n,a,r){i.drawView(n,a);var o=i.ctx,s=n.contentSize,c=s.width,d=s.height;a.borderRadius,a.backgroundColor;var u=a.color,l=void 0===u?"#000000":u;a.border;var f=n.contentSize.left-n.borderSize.left,h=n.contentSize.top-n.borderSize.top;if(m=r||m,o){o.save(),i.setOpacity(a);var p=i.setTransform(n,a),v=p.x,g=p.y;v+=f,g+=h;var b=Math.min(c,d);t=this.utf16to8(t);var w=this.getFrame(t),x=b/e;o.setFillStyle(l);for(var y=0;y<e;y++)for(var k=0;k<e;k++)w[k*e+y]&&o.fillRect(v+x*y,g+x*k,x,x);o.restore(),i.setBorder(n,a)}else console.warn("No canvas provided to draw QR code in!")}}}}(),x=function(){function t(t,e){var n,a,r=this,o=t.id,c=t.context,d=t.canvas,u=t.pixelRatio,l=t.width,f=t.height;this.count=0,this.isDraw=!0,this.id=o,this.canvas=d,this.pixelRatio=1*u.toFixed(2),this.width=l,this.height=f,this.platform=s,this.isRate=!1,this.component=e,this.ctx=(a=this,(n=c).setFonts=function(t){var e=t.fontFamily,i=void 0===e?"sans-serif":e,r=t.fontSize,o=void 0===r?14:r,s=t.fontWeight,c=void 0===s?"normal":s,d=t.textStyle,u=void 0===d?"normal":d;"mp-toutao"==a.platform&&(c="bold"==c?"bold":"",u="italic"==u?"italic":""),n.font=u+" "+c+" "+o+"px "+i},n.draw?n:Object.assign(n,{setStrokeStyle:function(t){n.strokeStyle=t},setLineWidth:function(t){n.lineWidth=t},setLineCap:function(t){n.lineCap=t},setFillStyle:function(t){n.fillStyle=t},setFontSize:function(t){n.font=String(t)+"px sans-serif"},setGlobalAlpha:function(t){n.globalAlpha=t},setLineJoin:function(t){n.lineJoin=t},setTextAlign:function(t){n.textAlign=t},setMiterLimit:function(t){n.miterLimit=t},setShadow:function(t,e,i,a){n.shadowOffsetX=t,n.shadowOffsetY=e,n.shadowBlur=i,n.shadowColor=a},setTextBaseline:function(t){n.textBaseline=t},createCircularGradient:function(){},draw:function(){}})),this.sleep=1e3/30,this.progress=0,this.root={width:l,height:f,fontSizeRate:1};var h=i({},this.size);Object.defineProperty(this,"size",{configurable:!0,set:function(t){Object.keys(t).forEach((function(e){h[e]=t[e],r.root[e]=t[e]}))},get:function(){return h}}),this.init()}return t.prototype.init=function(){this.canvas.height&&(this.canvas.height=this.root.height*this.pixelRatio,this.canvas.width=this.root.width*this.pixelRatio,this.ctx.scale(this.pixelRatio,this.pixelRatio))},t.prototype.clear=function(){this.ctx.clearRect(0,0,this.root.width,this.root.height)},t.prototype.roundRect=function(t,e,i,n,a,r,o){if(void 0===r&&(r=!1),void 0===o&&(o=!1),!(a<0)){var s=this.ctx;if(s.beginPath(),a){var c=a||{},d=c.borderTopLeftRadius,u=void 0===d?a||0:d,l=c.borderTopRightRadius,f=void 0===l?a||0:l,h=c.borderBottomRightRadius,p=void 0===h?a||0:h,v=c.borderBottomLeftRadius,g=void 0===v?a||0:v;s.arc(t+i-p,e+n-p,p,0,.5*Math.PI),s.lineTo(t+g,e+n),s.arc(t+g,e+n-g,g,.5*Math.PI,Math.PI),s.lineTo(t,e+u),s.arc(t+u,e+u,u,Math.PI,1.5*Math.PI),s.lineTo(t+i-f,e),s.arc(t+i-f,e+f,f,1.5*Math.PI,2*Math.PI),s.lineTo(t+i,e+n-p)}else s.rect(t,e,i,n);s.closePath(),o&&s.stroke(),r&&s.fill()}},t.prototype.setTransform=function(t,e){var i=e.transform,n=e.transformOrigin,a=void 0===n?"center center":n,r=e.position,o=this.ctx,s=i||{},c=s.scaleX,d=void 0===c?1:c,f=s.scaleY,h=void 0===f?1:f,p=s.translateX,v=void 0===p?0:p,g=s.translateY,m=void 0===g?0:g,b=s.rotate,w=void 0===b?0:b,x=s.skewX,y=void 0===x?0:x,k=s.skewY,S=void 0===k?0:k,A=t.left,z=t.top,C=t.width,_=t.height;if(!["absolute","fixed"].includes(r))return{x:A,y:z,w:C,h:_};v=l(v,C)||0,m=l(m,_)||0;var O={top:l("0%",1),center:l("50%",1,!0),bottom:l("100%",1)},I={left:l("0%",1),center:l("50%",1,!0),right:l("100%",1)};a=a.split(" ").filter((function(t,e){return e<2})).reduce((function(t,e){if(/\d+/.test(e)){var i=l(e,1,!0)/(/px|rpx$/.test(e)?u(t.x)?_:C:1);return u(t.x)?Object.assign(t,{y:i}):Object.assign(t,{x:i})}return u(I[e])&&!u(t.x)?Object.assign(t,{x:I[e]}):Object.assign(t,{y:O[e]||.5})}),{}),o.scale(d,h);var T={x:C*(d>0?1:-1)*a.x+(A+v)/d,y:_*(h>0?1:-1)*a.y+(z+m)/h};return o.translate(T.x,T.y),w&&o.rotate(w*Math.PI/180),(y||S)&&o.transform(1,Math.tan(S*Math.PI/180),Math.tan(y*Math.PI/180),1,0,0),{x:-C*a.x,y:-_*a.y,w:C,h:_}},t.prototype.setBackground=function(t,e,i,n,a){var r=this.ctx;t&&"transparent"!=t?function(t){return!(!t||!t.startsWith("linear")&&!t.startsWith("radial"))}(t)?function(t,e,i,n,a,r){t.startsWith("linear")?function(t,e,i,n,a,r){for(var o=function(t,e,i,n,a){void 0===n&&(n=0),void 0===a&&(a=0);var r=t.match(/([-]?\d{1,3})deg/),o=r&&r[1]?parseFloat(r[1]):0;if(o>=360&&(o-=360),o<0&&(o+=360),0===(o=Math.round(o)))return{x0:Math.round(e/2)+n,y0:i+a,x1:Math.round(e/2)+n,y1:a};if(180===o)return{x0:Math.round(e/2)+n,y0:a,x1:Math.round(e/2)+n,y1:i+a};if(90===o)return{x0:n,y0:Math.round(i/2)+a,x1:e+n,y1:Math.round(i/2)+a};if(270===o)return{x0:e+n,y0:Math.round(i/2)+a,x1:n,y1:Math.round(i/2)+a};var s=Math.round(180*Math.asin(e/Math.sqrt(Math.pow(e,2)+Math.pow(i,2)))/Math.PI);if(o===s)return{x0:n,y0:i+a,x1:e+n,y1:a};if(o===180-s)return{x0:n,y0:a,x1:e+n,y1:i+a};if(o===180+s)return{x0:e+n,y0:a,x1:n,y1:i+a};if(o===360-s)return{x0:e+n,y0:i+a,x1:n,y1:a};var c,d=0,u=0,l=0,f=0;if(o<s||o>180-s&&o<180||o>180&&o<180+s||o>360-s){var h=o*Math.PI/180,p=o<s||o>360-s?i/2:-i/2,v=Math.tan(h)*p,g=o<s||o>180-s&&o<180?e/2-v:-e/2-v;d=-(l=v+(c=Math.pow(Math.sin(h),2)*g)),u=-(f=p+c/Math.tan(h))}(o>s&&o<90||o>90&&o<90+s||o>180+s&&o<270||o>270&&o<360-s)&&(h=(90-o)*Math.PI/180,v=o>s&&o<90||o>90&&o<90+s?e/2:-e/2,p=Math.tan(h)*v,g=o>s&&o<90||o>270&&o<360-s?i/2-p:-i/2-p,d=-(l=v+(c=Math.pow(Math.sin(h),2)*g)/Math.tan(h)),u=-(f=p+c));return d=Math.round(d+e/2)+n,u=Math.round(i/2-u)+a,l=Math.round(l+e/2)+n,f=Math.round(i/2-f)+a,{x0:d,y0:u,x1:l,y1:f}}(a,t,e,i,n),s=o.x0,c=o.y0,d=o.x1,u=o.y1,l=r.createLinearGradient(s,c,d,u),f=a.match(/linear-gradient\((.+)\)/)[1],p=h(f.substring(f.indexOf(",")+1)),v=0;v<p.colors.length;v++)l.addColorStop(p.percents[v],p.colors[v]);r.setFillStyle(l)}(e,i,n,a,t,r):t.startsWith("radial")&&function(t,e,i,n,a,r){for(var o=h(a.match(/radial-gradient\((.+)\)/)[1]),s=Math.round(t/2)+i,c=Math.round(e/2)+n,d=r.createRadialGradient(s,c,0,s,c,Math.max(t,e)/2),u=0;u<o.colors.length;u++)d.addColorStop(o.percents[u],o.colors[u]);r.setFillStyle(d)}(e,i,n,a,t,r)}(t,e,i,n,a,r):r.setFillStyle(t):["mp-toutiao","mp-baidu"].includes(this.platform)?r.setFillStyle("transparent"):r.setFillStyle("rgba(0,0,0,0)")},t.prototype.setShadow=function(t){var e=t.boxShadow,i=void 0===e?[]:e,n=this.ctx;if(i.length){var a=i[0],r=i[1],o=i[2],s=i[3];n.setShadow(a,r,o,s)}},t.prototype.setBorder=function(t,e){var i=this,n=this.ctx,a=t.width,r=t.height,o=e.border,s=e.borderBottom,c=e.borderTop,d=e.borderRight,u=e.borderLeft,l=e.borderRadius,f=o||{},h=f.borderWidth,p=void 0===h?0:h,v=f.borderStyle,g=f.borderColor,m=s||{},b=m.borderBottomWidth,w=void 0===b?p:b,x=m.borderBottomStyle,y=void 0===x?v:x,k=m.borderBottomColor,S=void 0===k?g:k,A=c||{},z=A.borderTopWidth,C=void 0===z?p:z,_=A.borderTopStyle,O=void 0===_?v:_,I=A.borderTopColor,T=void 0===I?g:I,P=d||{},M=P.borderRightWidth,j=void 0===M?p:M,L=P.borderRightStyle,B=void 0===L?v:L,N=P.borderRightColor,D=void 0===N?g:N,X=u||{},R=X.borderLeftWidth,H=void 0===R?p:R,F=X.borderLeftStyle,V=void 0===F?v:F,W=X.borderLeftColor,G=void 0===W?g:W,q=l||{},E=q.borderTopLeftRadius,Z=void 0===E?l||0:E,U=q.borderTopRightRadius,J=void 0===U?l||0:U,Q=q.borderBottomRightRadius,Y=void 0===Q?l||0:Q,K=q.borderBottomLeftRadius,$=void 0===K?l||0:K;if(s||u||c||d||o){var tt=function(t,e,a){"dashed"==e?/mp/.test(i.platform)?n.setLineDash([Math.ceil(4*t/3),Math.ceil(4*t/3)]):n.setLineDash([Math.ceil(6*t),Math.ceil(6*t)]):"dotted"==e&&n.setLineDash([t,t]),n.setStrokeStyle(a)},et=function(t,e,i,a,r,o,s,c,d,u,l,f,h,p){n.save(),n.setLineWidth(f),tt(f,h,p),n.beginPath(),n.arc(t,e,s,Math.PI*d,Math.PI*u),n.lineTo(i,a),n.arc(r,o,c,Math.PI*u,Math.PI*l),n.stroke(),n.restore()};n.save(),this.setOpacity(e);var it=this.setTransform(t,e),nt=it.x,at=it.y;o&&(n.setLineWidth(p),tt(p,v,g),this.roundRect(nt,at,a,r,l,!1,!!g),n.restore()),s&&et(nt+a-Y,at+r-Y,nt+$,at+r,nt+$,at+r-$,Y,$,.25,.5,.75,w,y,S),u&&et(nt+$,at+r-$,nt,at+Z,nt+Z,at+Z,$,Z,.75,1,1.25,H,V,G),c&&et(nt+Z,at+Z,nt+a-J,at,nt+a-J,at+J,Z,J,1.25,1.5,1.75,C,O,T),d&&et(nt+a-J,at+J,nt+a,at+r-Y,nt+a-Y,at+r-Y,J,Y,1.75,2,.25,j,B,D)}},t.prototype.setOpacity=function(t){var e=t.opacity,i=void 0===e?1:e;this.ctx.setGlobalAlpha(i)},t.prototype.drawPattern=function(t,e,i){return n(this,void 0,void 0,(function(){var r=this;return a(this,(function(o){return[2,new Promise((function(o,s){var c=r,d=c.ctx,u=c.canvas,l=e.width,f=e.height,h=i||{},p=h.borderRadius,v=void 0===p?0:p,g=h.backgroundColor,m=void 0===g?"transparent":g,b=h.backgroundImage,w=h.backgroundRepeat,x=void 0===w?"repeat":w;d.save(),r.setOpacity(i);var y=r.setTransform(e,i),k=y.x,S=y.y;r.setShadow(i),r.setBackground(m,l,f,k,S),r.roundRect(k,S,l,f,v,!0,!1);var A=function(t){var n=d.createPattern(t.src,x);d.setFillStyle(n),r.roundRect(k,S,l,f,v,!0,!1),r.setBorder(e,i),d.restore(),o()};if(b)if(u.createImage||"web"==r.platform){var z=null;(z=u.createImage?u.createImage():new Image).onload=function(){t.src=z,A(t)},z.onerror=function(){return n(r,void 0,void 0,(function(){return a(this,(function(e){return console.log("createImage fail: "+JSON.stringify(t)),o(),[2]}))}))},z.src=t.path}else A(t)}))]}))}))},t.prototype.drawView=function(t,e,i,n,a){void 0===i&&(i=!0),void 0===n&&(n=!0),void 0===a&&(a=!0);var r=this.ctx,o=t.width,s=t.height,c=e||{},d=c.borderRadius,u=void 0===d?0:d,l=c.backgroundColor,f=void 0===l?"transparent":l;r.save(),this.setOpacity(e);var h=this.setTransform(t,e),p=h.x,v=h.y;a&&this.setShadow(e),i&&this.setBackground(f,o,s,p,v),this.roundRect(p,v,o,s,u,i,!1),r.restore(),n&&this.setBorder(t,e)},t.prototype.drawImage=function(t,e,i,r){return void 0===e&&(e={}),void 0===i&&(i={}),void 0===r&&(r=!0),n(this,void 0,void 0,(function(){var o=this;return a(this,(function(s){switch(s.label){case 0:return[4,new Promise((function(s,c){return n(o,void 0,void 0,(function(){var o,c,d,u,h,p,v,g,m,b,w,x,y,k,S,A,z,C,_,O,I,T,P,M,j,L,B,N=this;return a(this,(function(D){switch(D.label){case 0:return i.boxShadow&&this.drawView(e,Object.assign(i,{backgroundColor:i.backgroundColor||i.boxShadow&&(i.backgroundColor||"#ffffff")}),!0,!1,!0),c=(o=this).ctx,d=o.sleep,u=o.canvas,h=i.borderRadius,p=void 0===h?0:h,v=i.backgroundColor,g=void 0===v?"transparent":v,m=i.objectFit,b=void 0===m?"fill":m,w=i.objectPosition,x=e.width,y=e.height,k=e.left,S=e.top,c.save(),A=e.contentSize.left-e.borderSize.left,z=e.contentSize.top-e.borderSize.top,r||(this.setOpacity(i),C=this.setTransform(e,i),_=C.x,O=C.y,this.setBackground(g,x,y,k,S),k=_,S=O,this.roundRect(k,S,x,y,p,!!p,!1)),k+=A,S+=z,c.clip(),I=function(t){if("fill"!==b){var i=function(t,e,i){var n=t.objectFit,a=t.objectPosition,r=e.width/e.height,o=i.width/i.height,s=1;"contain"==n&&r>=o||"cover"==n&&r<o?s=e.height/i.height:("contain"==n&&r<o||"cover"==n&&r>=o)&&(s=e.width/i.width);var c=i.width*s,d=i.height*s,u=/^\d+px|rpx$/.test(null==a?void 0:a[0])?l(null==a?void 0:a[0],e.width):(e.width-c)*(/%$/.test(null==a?void 0:a[0])?l(null==a?void 0:a[0],1,!0):{left:0,center:.5,right:1}[(null==a?void 0:a[0])||"center"]),f=/^\d+px|rpx$/.test(null==a?void 0:a[1])?l(null==a?void 0:a[1],e.height):(e.height-d)*(/%$/.test(null==a?void 0:a[1])?l(null==a?void 0:a[1],1,!0):{top:0,center:.5,bottom:1}[(null==a?void 0:a[1])||"center"]),h=function(t,e){return[(t-u)/s,(e-f)/s]},p=h(0,0),v=p[0],g=p[1],m=h(e.width,e.height),b=m[0],w=m[1];return{sx:Math.max(v,0),sy:Math.max(g,0),sw:Math.min(b-v,i.width),sh:Math.min(w-g,i.height),dx:Math.max(u,0),dy:Math.max(f,0),dw:Math.min(c,e.width),dh:Math.min(d,e.height)}}({objectFit:b,objectPosition:w},e.contentSize,t),n=i.sx,a=i.sy,r=i.sh,o=i.sw,s=i.dx,d=i.dy,u=i.dh,f=i.dw;"mp-baidu"==N.platform?c.drawImage(t.src,s+k,d+S,f,u,n,a,o,r):c.drawImage(t.src,n,a,o,r,s+k,d+S,f,u)}else c.drawImage(t.src,k,S,x,y)},T=function(){c.restore(),N.drawView(e,i,!1,!0,!1),setTimeout(s,d)},P=function(t){if(u.createImage||"web"==N.platform){var e=null;(e=u.createImage?u.createImage():new Image).onload=function(){t.src=e,I(t),T()},e.onerror=function(){return n(N,void 0,void 0,(function(){return a(this,(function(e){return console.log("createImage fail: "+JSON.stringify(t)),s(!0),[2]}))}))},e.src=t.path}else I(t),T()},"string"!=typeof t?[3,2]:[4,f(t)];case 1:return M=D.sent(),j=M.path,L=M.width,B=M.height,P({path:j,src:j,width:L,height:B}),[3,3];case 2:P(t),D.label=3;case 3:return[2]}}))}))}))];case 1:return s.sent(),[2]}}))}))},t.prototype.drawText=function(t,e,i,n){this.drawView(e,i);var a=this.ctx,r=e.borderSize,o=e.contentSize,s=o.width,c=o.height,d=o.left-r.left,u=o.top-r.top,f=i.color,h=void 0===f?"#000000":f,p=i.lineHeight,v=void 0===p?"1.4em":p,g=i.fontSize,m=void 0===g?14:g,b=i.fontWeight,w=i.fontFamily,x=i.textStyle,y=i.textAlign,k=void 0===y?"left":y,S=i.verticalAlign,A=void 0===S?"middle":S;i.backgroundColor;var z=i.lineClamp,C=i.textDecoration;if(v=l(v,m),t){a.save(),this.setOpacity(i);var _=this.setTransform(e,i),O=_.x,I=_.y;switch(O+=d,I+=u,a.setFonts({fontFamily:w,fontSize:m,fontWeight:b,textStyle:x}),a.setTextBaseline("middle"),a.setTextAlign(k),a.setFillStyle(h),I+=m/2,k){case"left":break;case"center":O+=.5*s;break;case"right":O+=s}var T=n.lines*v,P=Math.ceil((c-T)/2);switch(P<0&&(P=0),A){case"top":break;case"middle":I+=P;break;case"bottom":I+=2*P}var M=(v-n.fontHeight)/2,j=function(t,e,i){var r=t;switch(k){case"left":t=t,r+=i;break;case"center":r=(t-=i/2)+i;break;case"right":r=t,t-=i}C&&(a.setLineWidth(m/13),a.beginPath(),e-=M,/\bunderline\b/.test(C)&&(a.moveTo(t,e-.5*n.fontHeight),a.lineTo(r,e-.5*n.fontHeight)),/\boverline\b/.test(C)&&(a.moveTo(t,e-1.5*n.fontHeight),a.lineTo(r,e-1.5*n.fontHeight)),/\bline-through\b/.test(C)&&(a.moveTo(t,e-n.fontHeight),a.lineTo(r,e-n.fontHeight)),a.closePath(),a.setStrokeStyle(h),a.stroke())};if(1==n.widths.lenght&&n.widths[0].total<=o.width)return a.fillText(t,O,I+M),j(O,I+=v,n.widths[0].total),a.restore(),void this.setBorder(e,i);for(var L=t.split(""),B=I,N=O,D="",X=0,R=0;R<=L.length;R++){var H=L[R]||"",F="\n"===H,V=""==H,W=D+(H=F?"":H),G=a.measureText(W).width;if(X>=z)break;if(N=O,(G=G)>o.width||F||V){if(X++,D=V&&G<=o.width?W:D,X===z&&G>s){for(;a.measureText(D+"...").width>o.width&&!(D.length<=1);)D=D.substring(0,D.length-1);D+="..."}if(a.fillText(D,N,I+M),j(N,I+=v,G),D=H,I+v>B+c)break}else D=W}a.restore()}},t.prototype.source=function(t){var e;return n(this,void 0,void 0,(function(){var i,n;return a(this,(function(a){switch(a.label){case 0:if("{}"==JSON.stringify(t))return[2];if(!t.type)for(i in t.type="view",t.css=t.css||{},t)["views","children","type","css"].includes(i)||(t.css[i]=t[i],delete t[i]);return(null===(e=null==t?void 0:t.css)||void 0===e?void 0:e.width)||(t.css?t.css.width=this.root.width:t.css={width:this.root.width}),[4,this.create(t)];case 1:return n=a.sent(),this.size=n.layout(),this.node=n,[2,this.size]}}))}))},t.prototype.create=function(t,e){var i,r,o,s;return n(this,void 0,void 0,(function(){var n,c,d,u,l,h,p,v,g,m,w,x;return a(this,(function(a){switch(a.label){case 0:if("image"==t.type&&!t.src&&!t.url||"qrcode"==t.type&&!t.text)return[2];if("none"==(null===(i=null==t?void 0:t.css)||void 0===i?void 0:i.display))return console.error("element display none"),[2];a.label=1;case 1:return a.trys.push([1,4,,5]),"image"==t.type||"view"==t.type&&(null===(r=t.css)||void 0===r?void 0:r.backgroundImage)?(n=null,c=/url\((.+)\)/,t.css.backgroundImage&&(null===(o=c.exec(t.css.backgroundImage))||void 0===o?void 0:o[1])&&(n=null===(s=c.exec(t.css.backgroundImage))||void 0===s?void 0:s[1]),[4,f(t.src||n)]):[3,3];case 2:d=a.sent(),u=d.width,l=d.height,h=d.path,["mp-weixin","mp-baidu","mp-qq","mp-toutiao"].includes(this.platform)&&(h=/^\.|^\/(?=[^\/])/.test(t.src||n)?"/"+h:h),t.attributes=Object.assign(t.attributes||{},{width:u,height:l,path:h,src:h,naturalSrc:t.src||n}),a.label=3;case 3:return[3,5];case 4:return p=a.sent(),console.log(p),[2];case 5:if(this.count+=1,v=new b(t,e,this.root,this.ctx),!(g=t.views||t.children))return[3,9];m=0,a.label=6;case 6:return m<g.length?(w=g[m],[4,this.create(w,v)]):[3,9];case 7:(x=a.sent())&&v.add(x),a.label=8;case 8:return m++,[3,6];case 9:return[2,v]}}))}))},t.prototype.drawNode=function(t){return n(this,void 0,void 0,(function(){var e,i,n,r,o,s,c,d,u,l,f,h;return a(this,(function(a){switch(a.label){case 0:return e=t.layoutBox,i=t.computedStyle,n=t.attributes,r=t.name,o=t.children,s=t.attributes,c=s.src,d=s.text,"view"!==r?[3,4]:c?[4,this.drawPattern(n,e,i)]:[3,2];case 1:return a.sent(),[3,3];case 2:this.drawView(e,i),a.label=3;case 3:return[3,7];case 4:return"image"===r&&c?[4,this.drawImage(n,e,i,!1)]:[3,6];case 5:return a.sent(),[3,7];case 6:"text"===r?this.drawText(d,e,i,n):"qrcode"===r&&(null==w?void 0:w.api)&&w.api.draw(d,this,e,i),a.label=7;case 7:if(this.progress+=1,!o)return[2];u=Object.values?Object.values(o):Object.keys(o).map((function(t){return o[t]})),l=0,f=u,a.label=8;case 8:return l<f.length?(h=f[l],[4,this.drawNode(h)]):[3,11];case 9:a.sent(),a.label=10;case 10:return l++,[3,8];case 11:return[2]}}))}))},t.prototype.render=function(){return n(this,void 0,void 0,(function(){return a(this,(function(t){switch(t.label){case 0:return this.init(),[4,(e=30,void 0===e&&(e=0),new Promise((function(t){return setTimeout(t,e)})))];case 1:return t.sent(),[4,this.drawNode(this.node)];case 2:return t.sent(),[2]}var e}))}))},t.prototype.listen=function(t,e){var i=this;if("progressChange"==t){var n=0;Object.defineProperty(this,"progress",{configurable:!0,set:function(t){n=t,e(t/i.count)},get:function(){return n||0}})}},t.prototype.save=function(t){try{var e=t||{},i=e.fileType,n=void 0===i?"png":i,a=e.quality,r=void 0===a?1:a;return this.canvas.toDataURL("image/"+n,r)}catch(t){return console.log("image cross domain"),t}},t}();"web"==s&&(window.Painter=x),t.Painter=x,Object.defineProperty(t,"__esModule",{value:!0})}))},"4fadd":function(t,e,i){"use strict";var n=i("d039"),a=i("861d"),r=i("c6b6"),o=i("d86b"),s=Object.isExtensible,c=n((function(){s(1)}));t.exports=c||o?function(t){return!!a(t)&&((!o||"ArrayBuffer"!==r(t))&&(!s||s(t)))}:s},"4ff2":function(t,e,i){"use strict";i.r(e);var n=i("56f7"),a=i.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(r);e["default"]=a.a},"4ffd3":function(t,e,i){var n=i("24fb");e=n(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.share-popup .share-tab[data-v-41a26444]{margin:%?40?% 0 %?140?%}.share-popup .share-tab .share-icon[data-v-41a26444]{width:%?100?%;height:%?100?%}.share-popup .cancel[data-v-41a26444]{height:%?98?%}.share-poster .share-img[data-v-41a26444]{width:%?640?%;border-radius:%?12?%}.share-poster .save-btn[data-v-41a26444]{background-color:#ff2c3c;color:#fff;margin-top:%?20?%}.share-tips .share-arrow[data-v-41a26444]{width:%?140?%;height:%?250?%;float:right;margin:%?15?% %?31?% 0 0}',""]),t.exports=e},"528e":function(t,e){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAr4AAADIBAMAAAD8Tf+zAAAAHlBMVEUAAADv7+/k5OTn5+fl5eXm5ubk5OTk5OTl5eX///8aoTc0AAAACHRSTlMAEDBAn6Dv8NSwICYAAAIkSURBVHja7dlREQIxDEXRAoMXpCABCUhAAhZwi4bM5G073bMSzsc2uRnfA77feb/Bly9fvnz58uVb9X3xjfpeP3yTvuPBN+p75xv1vfGN+l748vV/4Ot9M59tuF+8+SZ9n/ZjfYcvX758+fIt+urr4flXX7e/6Q989TP9ly9f/wfvm/mMb9d+oa9HffV1fYcvX758+fLlu5av+0V4v3C/sB/rO3z1yTm++jpf/we+3jfz2Y77hftF1Nf9Qt/hy5cvX758q776enj+1dftb/oDX/1M/+XL1//B+2Y+49u1X+jrUV99Xd/hy5cvX758+a7l634R3i/cL+zH+g5ffXKOr77O1/+Br/fNfLbjfuF+EfV1v9B3+PLly5cv36qvvh6ef/V1+5v+wFc/03/58vV/8L6Zz/h27Rf6etRXX9d3+PLly5cv36qvvh6ef/V1+5v+wFc/03/58vV/8L6Zz/h27Rf6etRXX9d3+PLly5cvX75r+bpfhPcL9wv7sb7DV5+c46uv8/V/4Ot9M5/tuF+4X0R93S/0Hb58+fLly7fqq6+H51993f6mP/DVz/Rfvnz9H7xv5jO+XfuFvh711df1Hb58+fLly5fvWr7uF+H9wv3Cfqzv8NUn5/jq63z9H/h638xnO+4X7hdRX/cLfYcvX758+fKt+urr4flXX7e/6Q989TP9ly9f/wfvm/mMb9d+oa9HffV1fYcvX758+fLly/dE3x8kOVDtActPQAAAAABJRU5ErkJggg=="},"564a":function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return r})),i.d(e,"a",(function(){return n}));var n={priceFormat:i("fefe").default,uIcon:i("90f3").default},a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{staticClass:"coupon-list"},t._l(t.list,(function(e,a){return n("v-uni-view",{key:a,staticClass:"m-t-20"},[n("v-uni-view",{class:"coupon-item flex "+(1==t.btnType||2==t.btnType?"gray":"")},[e.is_get?n("img",{staticClass:"received",attrs:{src:i("1231"),alt:""}}):t._e(),n("v-uni-view",{staticClass:"price white flex-col col-center"},[n("v-uni-view",{staticClass:"xl"},[n("price-format",{attrs:{"first-size":60,"second-size":50,"subscript-size":34,price:e.money,weight:500}})],1),n("v-uni-view",{staticClass:"sm text-center"},[t._v(t._s(e.condition_type_desc))])],1),n("v-uni-view",{staticClass:"info m-l-20"},[n("v-uni-view",{staticClass:"lg m-b-20"},[t._v(t._s(e.coupon_name))]),n("v-uni-view",{staticClass:"xs lighter m-b-20"},[t._v(t._s(e.user_time_desc))]),n("v-uni-view",{staticClass:"xs lighter"},[t._v(t._s(e.use_scene_desc))])],1),n("v-uni-button",{directives:[{name:"show",rawName:"v-show",value:!(1==t.btnType||2==t.btnType),expression:"!(btnType == 1 || btnType == 2)"}],class:"btn br60 white xs "+("去使用"==t.getBtn(e)?"plain":""),attrs:{type:"primary"},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.onHandle(e.id,e)}}},[t._v(t._s(t.getBtn(e)))]),e.is_get?n("v-uni-image",{staticClass:"receive",attrs:{src:"/static/images/coupon_receive.png"}}):t._e()],1),e.use_goods_desc?n("v-uni-view",{staticClass:"bg-white",staticStyle:{padding:"14rpx 20rpx"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onShowTips(a)}}},[n("v-uni-view",{staticClass:"flex row-between"},[n("v-uni-view",{staticClass:"xs"},[t._v("使用说明")]),n("u-icon",{class:t.showTips[a]?"rotate":"",attrs:{name:"arrow-down"}})],1),n("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:t.showTips[a],expression:"showTips[index]"}],staticClass:"m-t-10 xs"},[t._v(t._s(e.use_goods_desc))])],1):t._e()],1)})),1)},r=[]},"56f7":function(t,e,i){"use strict";i("7a82");var n=i("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=n(i("54f8"));i("c975"),i("caad6"),i("2532"),i("ac1f"),i("466d"),i("841c"),i("5319"),i("e25e"),i("14d9"),i("99af"),i("acd8");var r=uni.getSystemInfoSync(),o=r.windowWidth,s=(r.platform,i("dc60")),c={name:"parser",data:function(){return{uid:this._uid,showAm:"",nodes:[]}},props:{html:String,autopause:{type:Boolean,default:!0},autoscroll:Boolean,autosetTitle:{type:Boolean,default:!0},domain:String,lazyLoad:Boolean,selectable:Boolean,tagStyle:Object,showWithAnimation:Boolean,useAnchor:Boolean},watch:{html:function(t){this.setContent(t)}},created:function(){this.imgList=[],this.imgList.each=function(t){for(var e=0,i=this.length;e<i;e++)this.setItem(e,t(this[e],e,this))},this.imgList.setItem=function(t,e){if(void 0!=t&&e){if(0==e.indexOf("http")&&this.includes(e)){for(var i,n=e.split("://")[0],a=n.length;i=e[a];a++){if("/"==i&&"/"!=e[a-1]&&"/"!=e[a+1])break;n+=Math.random()>.5?i.toUpperCase():i}return n+=e.substr(a),this[t]=n}if(this[t]=e,e.includes("data:image")){var r=e.match(/data:image\/(\S+?);(\S+?),(.+)/);if(!r)return}}}},mounted:function(){this.document=document.getElementById("rtf"+this._uid),this.html&&this.setContent(this.html)},beforeDestroy:function(){this._observer&&this._observer.disconnect(),this.imgList.each((function(t){})),clearInterval(this._timer)},methods:{setContent:function(t,e){var i=this;if(t){var n=document.createElement("div");e?this.rtf?this.rtf.appendChild(n):this.rtf=n:(this.rtf&&this.rtf.parentNode.removeChild(this.rtf),this.rtf=n),n.innerHTML=this._handleHtml(t,e);for(var r,c=this.rtf.getElementsByTagName("style"),d=0;r=c[d++];)r.innerHTML=r.innerHTML.replace(/body/g,"#rtf"+this._uid),r.setAttribute("scoped","true");!this._observer&&this.lazyLoad&&IntersectionObserver&&(this._observer=new IntersectionObserver((function(t){for(var e,n=0;e=t[n++];)e.isIntersecting&&(e.target.src=e.target.getAttribute("data-src"),e.target.removeAttribute("data-src"),i._observer.unobserve(e.target))}),{rootMargin:"500px 0px 500px 0px"}));var u=this,l=this.rtf.getElementsByTagName("title");l.length&&this.autosetTitle&&uni.setNavigationBarTitle({title:l[0].innerText});var f=function(t){var e=t.getAttribute("src");i.domain&&e&&("/"==e[0]?"/"==e[1]?t.src=(i.domain.includes("://")?i.domain.split("://")[0]:"")+":"+e:t.src=i.domain+e:e.includes("://")||0==e.indexOf("data:")||(t.src=i.domain+"/"+e))};this.imgList.length=0;for(var h,p=this.rtf.getElementsByTagName("img"),v=0,g=0;h=p[v];v++)parseInt(h.style.width||h.getAttribute("width"))>o&&(h.style.height="auto"),f(h),h.hasAttribute("ignore")||"A"==h.parentElement.nodeName||(h.i=g++,u.imgList.push(h.getAttribute("original-src")||h.src||h.getAttribute("data-src")),h.onclick=function(t){t.stopPropagation();var e=!0;this.ignore=function(){return e=!1},u.$emit("imgtap",this),e&&uni.previewImage({current:this.i,urls:u.imgList})}),h.onerror=function(){s.errorImg&&(u.imgList[this.i]=this.src=s.errorImg),u.$emit("error",{source:"img",target:this})},u.lazyLoad&&this._observer&&h.src&&0!=h.i&&(h.setAttribute("data-src",h.src),h.removeAttribute("src"),this._observer.observe(h));var m,b=this.rtf.getElementsByTagName("a"),w=(0,a.default)(b);try{for(w.s();!(m=w.n()).done;){var x=m.value;x.onclick=function(t){t.stopPropagation();var e=!0,i=this.getAttribute("href");if(u.$emit("linkpress",{href:i,ignore:function(){return e=!1}}),e&&i)if("#"==i[0])u.useAnchor&&u.navigateTo({id:i.substr(1)});else{if(0==i.indexOf("http")||0==i.indexOf("//"))return!0;uni.navigateTo({url:i})}return!1}}}catch(L){w.e(L)}finally{w.f()}var y=this.rtf.getElementsByTagName("video");u.videoContexts=y;for(var k,S=0;k=y[S++];)f(k),k.style.maxWidth="100%",k.onerror=function(){u.$emit("error",{source:"video",target:this})},k.onplay=function(){if(u.autopause)for(var t,e=0;t=u.videoContexts[e++];)t!=this&&t.pause()};var A,z,C=this.rtf.getElementsByTagName("audio"),_=(0,a.default)(C);try{for(_.s();!(A=_.n()).done;){var O=A.value;f(O),O.onerror=function(){u.$emit("error",{source:"audio",target:this})}}}catch(L){_.e(L)}finally{_.f()}if(this.autoscroll){var I,T=this.rtf.getElementsByTagName("table"),P=(0,a.default)(T);try{for(P.s();!(I=P.n()).done;){var M=I.value,j=document.createElement("div");j.style.overflow="scroll",M.parentNode.replaceChild(j,M),j.appendChild(M)}}catch(L){P.e(L)}finally{P.f()}}e||this.document.appendChild(this.rtf),this.$nextTick((function(){i.nodes=[1],i.$emit("load")})),setTimeout((function(){return i.showAm=""}),500),clearInterval(this._timer),this._timer=setInterval((function(){i.rect=i.rtf.getBoundingClientRect(),i.rect.height==z&&(i.$emit("ready",i.rect),clearInterval(i._timer)),z=i.rect.height}),350),this.showWithAnimation&&!e&&(this.showAm="animation:_show .5s")}else this.rtf&&!e&&this.rtf.parentNode.removeChild(this.rtf)},getText:function(){arguments.length>0&&void 0!==arguments[0]||this.nodes;var t="";return t=this.rtf.innerText,t},in:function(t){t.page&&t.selector&&t.scrollTop&&(this._in=t)},navigateTo:function(t){var e=this;if(!this.useAnchor)return t.fail&&t.fail("Anchor is disabled");var i=uni.createSelectorQuery().in(this._in?this._in.page:this).select((this._in?this._in.selector:"#_top")+(t.id?"".concat(" ","#").concat(t.id,",").concat(this._in?this._in.selector:"#_top").concat(" ",".").concat(t.id):"")).boundingClientRect();this._in?i.select(this._in.selector).scrollOffset().select(this._in.selector).boundingClientRect():i.selectViewport().scrollOffset(),i.exec((function(i){if(!i[0])return t.fail&&t.fail("Label not found");var n=i[1].scrollTop+i[0].top-(i[2]?i[2].top:0)+(t.offset||0);e._in?e._in.page[e._in.scrollTop]=n:uni.pageScrollTo({scrollTop:n,duration:300}),t.success&&t.success()}))},getVideoContext:function(t){if(!t)return this.videoContexts;for(var e=this.videoContexts.length;e--;)if(this.videoContexts[e].id==t)return this.videoContexts[e]},_handleHtml:function(t,e){if(!e){var i="<style scoped>@keyframes _show{0%{opacity:0}100%{opacity:1}}img{max-width:100%}";for(var n in s.userAgentStyles)i+="".concat(n,"{").concat(s.userAgentStyles[n],"}");for(n in this.tagStyle)i+="".concat(n,"{").concat(this.tagStyle[n],"}");i+="</style>",t=i+t}return t.includes("rpx")&&(t=t.replace(/[0-9.]+\s*rpx/g,(function(t){return parseFloat(t)*o/750+"px"}))),t}}};e.default=c},"5a65":function(t,e,i){var n=i("ae3a");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=i("4f06").default;a("57cd83f1",n,!0,{sourceMap:!1,shadowMode:!1})},"5bd1":function(t,e,i){"use strict";i.r(e);var n=i("cef3"),a=i("37a5");for(var r in a)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(r);i("8a9a");var o=i("f0c5"),s=Object(o["a"])(a["default"],n["b"],n["c"],!1,null,"4338634c",null,!1,n["a"],void 0);e["default"]=s.exports},"5c51":function(t,e,i){"use strict";i("7a82");var n=i("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=n(i("f07e")),r=n(i("c964"));i("a9e3");var o=i("c60f"),s={name:"get-coupon",props:{goodsId:{type:[Number,String]},shopId:{type:[Number,String]},type:{type:String},wrapStyle:Object},data:function(){return{showCoupon:!1,list:[]}},watch:{goodsId:function(t){this.getGoodsCouponFun()},shopId:function(){this.getGoodsCouponFun()}},methods:{getGoodsCouponFun:function(){var t=this;return(0,r.default)((0,a.default)().mark((function e(){var i,n,r;return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return console.log(t.goodsId),e.next=3,(0,o.getCouponList)({goods_id:t.goodsId,shop_id:t.shopId});case 3:i=e.sent,n=i.data,r=i.code,console.log(n),1==r&&(t.list=n.lists);case 8:case"end":return e.stop()}}),e)})))()}}};e.default=s},"5ca1":function(t,e,i){var n=i("24fb");e=n(!1),e.push([t.i,".swiper-wrap[data-v-7e5db0e3]{width:100%;height:%?750?%;position:relative}.swiper-wrap .swiper[data-v-7e5db0e3]{width:100%;height:100%;position:relative}.swiper-wrap .swiper .slide-image[data-v-7e5db0e3]{width:100%;height:100%}.swiper-wrap .dots[data-v-7e5db0e3]{position:absolute;right:%?24?%;bottom:%?24?%;display:flex;height:%?34?%;padding:0 %?15?%}.swiper-wrap .video-wrap[data-v-7e5db0e3]{width:100%;height:100%;position:relative;overflow:hidden}.swiper-wrap .my-video[data-v-7e5db0e3]{width:100%;height:100%}.swiper-wrap .icon-play[data-v-7e5db0e3]{width:%?90?%;height:%?90?%;position:absolute;top:50%;left:50%;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%);z-index:999}",""]),t.exports=e},6062:function(t,e,i){"use strict";i("1c59")},"63db":function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return r})),i.d(e,"a",(function(){return n}));var n={uImage:i("ba4b").default,uIcon:i("90f3").default,goodsList:i("2d92").default,cuProgress:i("70f3").default},a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"goods-shop"},[i("v-uni-view",{staticClass:"title"},[i("router-link",{staticClass:"flex-none",attrs:{to:{path:"/pages/store_index/store_index",query:{id:t.shop.id}}}},[i("v-uni-view",{staticClass:"p-20 flex",staticStyle:{"background-color":"#f7f8fb","border-radius":"20rpx"}},[i("u-image",{attrs:{width:"100rpx",height:"100rpx","border-radius":"50%",src:t.shop.logo}}),i("v-uni-view",{staticClass:"flex-1 shop-name m-l-20 m-r-20"},[i("v-uni-view",{staticClass:"flex"},[i("v-uni-view",{staticClass:"bold lg line-1"},[t._v(t._s(t.shop.name))])],1),i("v-uni-view",{staticClass:"xs m-t-10"},[t._v("在售商品 "+t._s(t.shop.goods_on_sale)+"件")])],1),i("v-uni-view",{staticClass:"primary btn br60 sm"},[t._v("进店")])],1)],1)],1),t.list.length?i("v-uni-view",[i("v-uni-view",{staticClass:"m-20 flex"},[t._v("店铺推荐"),i("router-link",{staticClass:"flex-none",staticStyle:{"margin-left":"auto"},attrs:{to:{path:"/bundle/pages/shop_search/shop_search",query:{id:t.shop.id,is_recommend:1}}}},[i("v-uni-view",{staticClass:"flex"},[i("v-uni-view",{staticClass:"lighter xs"},[t._v("查看全部")]),i("u-icon",{attrs:{name:"arrow-right"}})],1)],1)],1),i("v-uni-scroll-view",{staticStyle:{"white-space":"nowrap"},attrs:{"scroll-x":"true","scroll-with-animation":"true"},on:{scroll:function(e){arguments[0]=e=t.$handleEvent(e),t.scrollBarChange.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"goods"},[i("goods-list",{attrs:{type:"row",list:t.list}})],1)],1),t.list.length>3?i("v-uni-view",{staticClass:"flex row-center progress"},[i("cu-progress",{attrs:{"progress-bar-color":t.colorConfig.primary,left:t.progressPer}})],1):t._e()],1):t._e()],1)},r=[]},"64ea":function(t,e,i){"use strict";i.r(e);var n=i("d9bd"),a=i.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(r);e["default"]=a.a},6566:function(t,e,i){"use strict";var n=i("7c73"),a=i("edd0"),r=i("6964"),o=i("0366"),s=i("19aa"),c=i("7234"),d=i("2266"),u=i("c6d2"),l=i("4754"),f=i("2626"),h=i("83ab"),p=i("f183").fastKey,v=i("69f3"),g=v.set,m=v.getterFor;t.exports={getConstructor:function(t,e,i,u){var l=t((function(t,a){s(t,f),g(t,{type:e,index:n(null),first:void 0,last:void 0,size:0}),h||(t.size=0),c(a)||d(a,t[u],{that:t,AS_ENTRIES:i})})),f=l.prototype,v=m(e),b=function(t,e,i){var n,a,r=v(t),o=w(t,e);return o?o.value=i:(r.last=o={index:a=p(e,!0),key:e,value:i,previous:n=r.last,next:void 0,removed:!1},r.first||(r.first=o),n&&(n.next=o),h?r.size++:t.size++,"F"!==a&&(r.index[a]=o)),t},w=function(t,e){var i,n=v(t),a=p(e);if("F"!==a)return n.index[a];for(i=n.first;i;i=i.next)if(i.key===e)return i};return r(f,{clear:function(){var t=v(this),e=t.first;while(e)e.removed=!0,e.previous&&(e.previous=e.previous.next=void 0),e=e.next;t.first=t.last=void 0,t.index=n(null),h?t.size=0:this.size=0},delete:function(t){var e=v(this),i=w(this,t);if(i){var n=i.next,a=i.previous;delete e.index[i.index],i.removed=!0,a&&(a.next=n),n&&(n.previous=a),e.first===i&&(e.first=n),e.last===i&&(e.last=a),h?e.size--:this.size--}return!!i},forEach:function(t){var e,i=v(this),n=o(t,arguments.length>1?arguments[1]:void 0);while(e=e?e.next:i.first){n(e.value,e.key,this);while(e&&e.removed)e=e.previous}},has:function(t){return!!w(this,t)}}),r(f,i?{get:function(t){var e=w(this,t);return e&&e.value},set:function(t,e){return b(this,0===t?0:t,e)}}:{add:function(t){return b(this,t=0===t?0:t,t)}}),h&&a(f,"size",{configurable:!0,get:function(){return v(this).size}}),l},setStrong:function(t,e,i){var n=e+" Iterator",a=m(e),r=m(n);u(t,e,(function(t,e){g(this,{type:n,target:t,state:a(t),kind:e,last:void 0})}),(function(){var t=r(this),e=t.kind,i=t.last;while(i&&i.removed)i=i.previous;return t.target&&(t.last=i=i?i.next:t.state.first)?l("keys"===e?i.key:"values"===e?i.value:[i.key,i.value],!1):(t.target=void 0,l(void 0,!0))}),i?"entries":"values",!i,!0),f(e)}}},"65c2":function(t,e,i){"use strict";i.r(e);var n=i("ad6f"),a=i("7c75");for(var r in a)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(r);i("9415");var o=i("f0c5"),s=Object(o["a"])(a["default"],n["b"],n["c"],!1,null,"130bc95c",null,!1,n["a"],void 0);e["default"]=s.exports},"666a":function(t,e,i){"use strict";i("7a82");var n=i("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=n(i("f07e")),r=n(i("c964")),o=n(i("f3f3"));i("4de4"),i("d3b7"),i("d81d"),i("4e82"),i("ac1f"),i("5319"),i("5b81"),i("14d9"),i("caad6"),i("d401"),i("25f0");var s=i("9953"),c=i("8516"),d=i("c60f"),u=i("26cb"),l=i("ea4b"),f=(n(i("8ffc")),i("b08d")),h=(getApp(),{data:function(){return{active:0,isTouchStart:!1,topArr:[],isFirstLoading:!0,isNull:!1,isGroup:0,showSpec:!1,showCoupon:!1,showShareBtn:!1,showCommission:!0,popupType:"",swiperList:[],goodsDetail:{},goodsType:0,checkedGoods:{},comment:{},countTime:0,team:{},teamFound:[],navStyle:{backBg:.4,backgroundBg:0,backColor:"rgba(256,256,256,1)"},id:"",shop:{},distribution:{}}},onLoad:function(){var t=this.$Route.query;if(t&&t.scene){var e=(0,f.strToParams)(decodeURIComponent(t.scene));t.id=e.id}this.id=t.id,this.getGoodsDetailFun(),this.getCartNum()},onPageScroll:function(t){var e=uni.upx2px(500),i=t.scrollTop,n=i/e;if(this.navStyle.backgroundBg=n,this.navStyle.backBg=.4*(.5-n),this.navStyle.backColor=n<.5?"rgba(256,256,256,"+2*(.5-n)+")":"rgba(0,0,0,"+2*(n-.5)+")",this.isTouchStart){var a=this.topArr.map((function(t,e){return{index:e,top:t}})).filter((function(t){return t.top<=i}));if(a.length){var r=a.sort((function(t,e){return e.top-t.top}))[0].index;if(this.active==r)return;this.active=r}}},methods:(0,o.default)((0,o.default)({},(0,u.mapActions)(["getCartNum","wxShare"])),{},{changeActive:function(t){this.isTouchStart=!1,uni.pageScrollTo({scrollTop:this.topArr[t],duration:200})},getRectInfo:function(){var t=this;this.topArr.length||((0,f.getRect)("#goods").then((function(e){t.topArr[0]=e.top-t.navHeight})),(0,f.getRect)("#evaluation").then((function(e){t.topArr[1]=e.top-t.navHeight})),(0,f.getRect)("#details").then((function(e){t.topArr[2]=e.top-t.navHeight})))},getGoodsDetailFun:function(){var t=this;return(0,r.default)((0,a.default)().mark((function e(){var i,n,r,o,c,d,u,l,h,p,v,g,m,b,w;return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,(0,s.getGoodsDetail)({goods_id:t.id});case 2:i=e.sent,n=i.data,r=i.code,console.log("detail",n),1==r?(o=n.goods_image,c=n.content,d=n.comment,n.like,u=n.activity,l=n.shop,h=n.distribution,p=u.info,v=u.type,u.team,g=u.found,m=u.end_time,console.log(u),b=m?m-Date.now()/1e3:p?p.activity_end_time-Date.now()/1e3:0,g&&(g=(0,f.arraySlice)(g,[],2)),t.distribution=h||{},t.shop=l,n.content=c.replaceAll("width: 750px",""),t.goodsDetail=n,t.swiperList=o,t.comment=d||{},t.countTime=b,t.goodsType=v,t.team=p||{},t.teamFound=g||[],w={shareTitle:t.team.share_title||n.name,shareImage:n.image||"",shareDesc:t.team.share_intro||n.remark},t.wxShare(w),t.$nextTick((function(){uni.pageScrollTo({scrollTop:0,duration:0}),t.isFirstLoading=!1,t.getRectInfo()}))):(t.isNull=!0,t.isFirstLoading=!1);case 7:case"end":return e.stop()}}),e)})))()},collectGoodsFun:function(){var t=this;return(0,r.default)((0,a.default)().mark((function e(){var i,n,r;return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t.isLogin){e.next=2;break}return e.abrupt("return",t.$Router.push("/pages/login/login"));case 2:return t.goodsDetail.is_collect,e.next=5,(0,c.collectGoods)({goods_id:t.id});case 5:i=e.sent,n=i.msg,r=i.code,1==r&&(t.$toast({title:n}),t.goodsDetail.is_collect=!t.goodsDetail.is_collect);case 9:case"end":return e.stop()}}),e)})))()},showCouponFun:function(){if(!this.isLogin)return(0,l.toLogin)();this.showCoupon=!0},onChangeGoods:function(t){this.checkedGoods=t.detail},showSpecFun:function(t,e){2==this.goodsType&&[2,3].includes(t)?(this.isGroup=1,this.foundId=e):(this.isGroup=0,this.foundId=""),this.popupType=t,this.showSpec=!0},onBuy:function(t){if(!this.isLogin)return this.$Router.push("/pages/login/login");var e=t.detail,i=e.id,n=e.goodsNum,a={goods:[{item_id:i,num:n,goods_id:this.id,shop_id:this.shop.id,delivery_type:this.goodsDetail.default_delivery_type}],type:"buy",goodsType:this.goodsDetail.type};this.showSpec=!1,2==this.goodsType&&(a.teamId=this.team.id),this.foundId&&(a.foundId=this.foundId),console.log(a,2222),this.$Router.push({path:"/pages/confirm_order/confirm_order",query:{data:a}})},onConfirm:function(t){var e=this;this.team.team_id;(0,d.teamCheck)({team_id:this.foundId,goods_id:t.detail.goods_id,item_id:t.detail.id,count:t.detail.goodsNum}).then((function(i){1==i.code&&e.onBuy(t)}))},onAddCart:function(t){var e=this;return(0,r.default)((0,a.default)().mark((function i(){var n,r,o,c,d,u,l;return(0,a.default)().wrap((function(i){while(1)switch(i.prev=i.next){case 0:if(e.isLogin){i.next=2;break}return i.abrupt("return",e.$Router.push("/pages/login/login"));case 2:if(2!=e.goodsType){i.next=6;break}return n={goods:[{item_id:t.detail.id,num:t.detail.goodsNum,delivery_type:e.goodsDetail.default_delivery_type,goods_id:e.id,shop_id:e.shop.id}],type:"buy",goodsType:e.goodsDetail.type},e.$Router.push({path:"/pages/confirm_order/confirm_order",query:{data:n}}),i.abrupt("return");case 6:return r=t.detail,o=r.id,c=r.goodsNum,i.next=9,(0,s.addCart)({item_id:o,goods_num:c});case 9:d=i.sent,u=d.code,d.data,l=d.msg,1==u&&(e.getCartNum(),e.$toast({title:l,icon:"success"}),e.showSpec=!1);case 14:case"end":return i.stop()}}),i)})))()},handleConsult:function(){uni.makePhoneCall({phoneNumber:this.shop.mobile.toString()})}}),onShareAppMessage:function(){var t=this;return(0,r.default)((0,a.default)().mark((function e(){var i,n,r;return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return i=t.goodsDetail,n=t.inviteCode,r=t.team,e.abrupt("return",{title:r.share_title||i.name,imageUrl:i.image,path:"/pages/goods_details/goods_details?id="+t.id+"&invite_code="+n});case 2:case"end":return e.stop()}}),e)})))()},computed:(0,o.default)((0,o.default)({},(0,u.mapGetters)(["cartNum","inviteCode","sysInfo","appConfig","userInfo"])),{},{btnText:function(){var t=this.goodsType;switch(t){case 1:return{red:"立即抢购",yellow:""};case 2:return{red:"立即开团",yellow:"单独购买"};default:return{red:"立即购买",yellow:"加入购物车"}}},getTeamCountTime:function(){return function(t){return t-Date.now()/1e3}},navHeight:function(){return this.sysInfo.navHeight},enableCommission:function(){var t=this.goodsType,e=this.distribution,i=e.earnings,n=e.is_show;return 0==t&&i>0&&1==n}})});e.default=h},6777:function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("a9e3");var n=uni.getSystemInfoSync(),a={},r={name:"u-navbar",props:{height:{type:[String,Number],default:""},backIconColor:{type:String,default:"#606266"},backBg:{type:String,default:"transparent"},backIconName:{type:String,default:"nav-back"},backIconSize:{type:[String,Number],default:"42"},backText:{type:String,default:""},backTextStyle:{type:Object,default:function(){return{color:"#606266"}}},title:{type:String,default:""},titleWidth:{type:[String,Number],default:"250"},titleColor:{type:String,default:"#606266"},titleBold:{type:Boolean,default:!1},titleSize:{type:[String,Number],default:32},isBack:{type:[Boolean,String],default:!0},background:{type:Object,default:function(){return{background:"#ffffff"}}},isFixed:{type:Boolean,default:!0},immersive:{type:Boolean,default:!1},borderBottom:{type:Boolean,default:!0},zIndex:{type:[String,Number],default:""},customBack:{type:Function,default:null}},data:function(){return{menuButtonInfo:a,statusBarHeight:n.statusBarHeight,isHome:!1}},computed:{navbarInnerStyle:function(){var t={};return t.height=this.navbarHeight+"px",t},navbarStyle:function(){var t={};return t.zIndex=this.zIndex?this.zIndex:this.$u.zIndex.navbar,Object.assign(t,this.background),t},titleStyle:function(){var t={};return t.left=(n.windowWidth-uni.upx2px(this.titleWidth))/2+"px",t.right=(n.windowWidth-uni.upx2px(this.titleWidth))/2+"px",t.width=uni.upx2px(this.titleWidth)+"px",t},navbarHeight:function(){return this.height?this.height:44}},created:function(){var t=getCurrentPages().length;1==t&&(this.isHome=!0)},methods:{goBack:function(){"function"===typeof this.customBack?this.customBack.bind(this.$u.$parent.call(this))():this.isHome?uni.switchTab({url:"/pages/index/index"}):uni.navigateBack()}}};e.default=r},"699c":function(t,e,i){"use strict";var n=i("f323"),a=i.n(n);a.a},"6bd5":function(t,e,i){"use strict";i.r(e);var n=i("e03b"),a=i.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(r);e["default"]=a.a},"6c57":function(t,e,i){"use strict";var n=i("23e7"),a=i("da84");n({global:!0,forced:a.globalThis!==a},{globalThis:a})},"6d61":function(t,e,i){"use strict";var n=i("23e7"),a=i("da84"),r=i("e330"),o=i("94ca"),s=i("cb2d"),c=i("f183"),d=i("2266"),u=i("19aa"),l=i("1626"),f=i("7234"),h=i("861d"),p=i("d039"),v=i("1c7e"),g=i("d44e"),m=i("7156");t.exports=function(t,e,i){var b=-1!==t.indexOf("Map"),w=-1!==t.indexOf("Weak"),x=b?"set":"add",y=a[t],k=y&&y.prototype,S=y,A={},z=function(t){var e=r(k[t]);s(k,t,"add"===t?function(t){return e(this,0===t?0:t),this}:"delete"===t?function(t){return!(w&&!h(t))&&e(this,0===t?0:t)}:"get"===t?function(t){return w&&!h(t)?void 0:e(this,0===t?0:t)}:"has"===t?function(t){return!(w&&!h(t))&&e(this,0===t?0:t)}:function(t,i){return e(this,0===t?0:t,i),this})},C=o(t,!l(y)||!(w||k.forEach&&!p((function(){(new y).entries().next()}))));if(C)S=i.getConstructor(e,t,b,x),c.enable();else if(o(t,!0)){var _=new S,O=_[x](w?{}:-0,1)!==_,I=p((function(){_.has(1)})),T=v((function(t){new y(t)})),P=!w&&p((function(){var t=new y,e=5;while(e--)t[x](e,e);return!t.has(-0)}));T||(S=e((function(t,e){u(t,k);var i=m(new y,t,S);return f(e)||d(e,i[x],{that:i,AS_ENTRIES:b}),i})),S.prototype=k,k.constructor=S),(I||P)&&(z("delete"),z("has"),b&&z("get")),(P||O)&&z(x),w&&k.clear&&delete k.clear}return A[t]=S,n({global:!0,constructor:!0,forced:S!==y},A),g(S,t),w||i.setStrong(S,t,b),S}},"6e83":function(t,e,i){"use strict";var n=i("125a"),a=i.n(n);a.a},"701d":function(t,e,i){var n=i("24fb");e=n(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.goods-column .column-wrap[data-v-67ba699e]{display:inline-block;white-space:nowrap;padding:%?30?%}.goods-column .column-wrap .item[data-v-67ba699e]{display:inline-block;position:relative;overflow:hidden}.goods-column .column-wrap .item .title[data-v-67ba699e]{position:relative;z-index:1}.goods-column .column-wrap .item .line[data-v-67ba699e]{position:absolute;top:%?32?%;left:0;width:%?144?%;height:%?12?%;background:linear-gradient(90deg,#ff2c3c,rgba(255,44,60,0))}',""]),t.exports=e},"70f3":function(t,e,i){"use strict";i.r(e);var n=i("b27d"),a=i("739b");for(var r in a)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(r);i("aa10");var o=i("f0c5"),s=Object(o["a"])(a["default"],n["b"],n["c"],!1,null,"e51d8c58",null,!1,n["a"],void 0);e["default"]=s.exports},7226:function(t,e,i){"use strict";i.r(e);var n=i("7bab"),a=i("2f9a");for(var r in a)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(r);i("985a");var o=i("f0c5"),s=Object(o["a"])(a["default"],n["b"],n["c"],!1,null,"38cbd707",null,!1,n["a"],void 0);e["default"]=s.exports},"739b":function(t,e,i){"use strict";i.r(e);var n=i("8a46"),a=i.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(r);e["default"]=a.a},7579:function(t,e,i){"use strict";i.r(e);var n=i("1203"),a=i("ba81");for(var r in a)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(r);i("8af8");var o=i("f0c5"),s=Object(o["a"])(a["default"],n["b"],n["c"],!1,null,"67ba699e",null,!1,n["a"],void 0);e["default"]=s.exports},7853:function(t,e,i){"use strict";i.r(e);var n=i("3e48"),a=i.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(r);e["default"]=a.a},"78b4":function(t,e,i){"use strict";i.r(e);var n=i("564a"),a=i("4e13");for(var r in a)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(r);i("c971");var o=i("f0c5"),s=Object(o["a"])(a["default"],n["b"],n["c"],!1,null,"fa6b581a",null,!1,n["a"],void 0);e["default"]=s.exports},"79d0":function(t,e,i){"use strict";i.r(e);var n=i("b98a"),a=i.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(r);e["default"]=a.a},"7a54":function(t,e,i){var n=i("aa36");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=i("4f06").default;a("4f941e16",n,!0,{sourceMap:!1,shadowMode:!1})},"7ab8":function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return r})),i.d(e,"a",(function(){return n}));var n={uIcon:i("90f3").default},a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"u-numberbox"},[i("v-uni-view",{staticClass:"u-icon-minus",class:{"u-icon-disabled":t.disabled||t.inputVal<=t.min},style:{background:t.bgColor,height:t.inputHeight+"rpx",color:t.color},on:{touchstart:function(e){e.stopPropagation(),e.preventDefault(),arguments[0]=e=t.$handleEvent(e),t.btnTouchStart("minus")},touchend:function(e){e.stopPropagation(),e.preventDefault(),arguments[0]=e=t.$handleEvent(e),t.clearTimer.apply(void 0,arguments)}}},[i("u-icon",{attrs:{name:"minus",size:t.size}})],1),i("v-uni-input",{staticClass:"u-number-input",class:{"u-input-disabled":t.disabled},style:{color:t.color,fontSize:t.size+"rpx",background:t.bgColor,height:t.inputHeight+"rpx",width:t.inputWidth+"rpx"},attrs:{disabled:t.disabledInput||t.disabled,"cursor-spacing":t.getCursorSpacing,type:"number"},on:{blur:function(e){arguments[0]=e=t.$handleEvent(e),t.onBlur.apply(void 0,arguments)},focus:function(e){arguments[0]=e=t.$handleEvent(e),t.onFocus.apply(void 0,arguments)}},model:{value:t.inputVal,callback:function(e){t.inputVal=e},expression:"inputVal"}}),i("v-uni-view",{staticClass:"u-icon-plus",class:{"u-icon-disabled":t.disabled||t.inputVal>=t.max},style:{background:t.bgColor,height:t.inputHeight+"rpx",color:t.color},on:{touchstart:function(e){e.stopPropagation(),e.preventDefault(),arguments[0]=e=t.$handleEvent(e),t.btnTouchStart("plus")},touchend:function(e){e.stopPropagation(),e.preventDefault(),arguments[0]=e=t.$handleEvent(e),t.clearTimer.apply(void 0,arguments)}}},[i("u-icon",{attrs:{name:"plus",size:t.size}})],1)],1)},r=[]},"7b9c":function(t,e,i){var n=i("24fb");e=n(!1),e.push([t.i,".lime-painter[data-v-64f1d435], .lime-painter__canvas[data-v-64f1d435]{\nwidth:100%;\n}",""]),t.exports=e},"7bab":function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){}));var n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"u-countdown"},[t.showDays&&(t.hideZeroDay||!t.hideZeroDay&&"00"!=t.d)?i("v-uni-view",{staticClass:"u-countdown-item",style:[t.itemStyle]},[i("v-uni-view",{staticClass:"u-countdown-time",style:{fontSize:t.fontSize+"rpx",color:t.color}},[t._v(t._s(t.d))])],1):t._e(),t.showDays&&(t.hideZeroDay||!t.hideZeroDay&&"00"!=t.d)?i("v-uni-view",{style:{fontSize:t.separatorSize+"rpx","margin-right":"6rpx",color:t.separatorColor}},[t._v("天")]):t._e(),t.showHours?i("v-uni-view",{staticClass:"u-countdown-item",style:[t.itemStyle]},[i("v-uni-view",{staticClass:"u-countdown-time",style:{fontSize:t.fontSize+"rpx",color:t.color}},[t._v(t._s(t.h))])],1):t._e(),t.showHours?i("v-uni-view",{staticClass:"u-countdown-colon",style:{fontSize:t.separatorSize+"rpx",color:t.separatorColor,paddingBottom:"colon"==t.separator?"4rpx":0}},[t._v(t._s("colon"==t.separator?":":"时"))]):t._e(),t.showMinutes?i("v-uni-view",{staticClass:"u-countdown-item",style:[t.itemStyle]},[i("v-uni-view",{staticClass:"u-countdown-time",style:{fontSize:t.fontSize+"rpx",color:t.color}},[t._v(t._s(t.i))])],1):t._e(),t.showMinutes?i("v-uni-view",{staticClass:"u-countdown-colon",style:{fontSize:t.separatorSize+"rpx",color:t.separatorColor,paddingBottom:"colon"==t.separator?"4rpx":0}},[t._v(t._s("colon"==t.separator?":":"分"))]):t._e(),t.showSeconds?i("v-uni-view",{staticClass:"u-countdown-item",staticStyle:{width:"36rpx"},style:[t.itemStyle]},[i("v-uni-view",{staticClass:"u-countdown-time",style:{fontSize:t.fontSize+"rpx",color:t.color}},[t._v(t._s(t.s))])],1):t._e(),t.showSeconds&&"zh"==t.separator?i("v-uni-view",{staticClass:"u-countdown-colon",style:{fontSize:t.separatorSize+"rpx",color:t.separatorColor,paddingBottom:"colon"==t.separator?"4rpx":0}},[t._v("秒")]):t._e()],1)},a=[]},"7bd9":function(t,e,i){var n=i("5ca1");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=i("4f06").default;a("54a6f7f6",n,!0,{sourceMap:!1,shadowMode:!1})},"7c75":function(t,e,i){"use strict";i.r(e);var n=i("1377"),a=i.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(r);e["default"]=a.a},"7f84":function(t,e,i){"use strict";i("7a82");var n=i("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.base64ToPath=f,e.compareVersion=function(t,e){t=t.split("."),e=e.split(".");var i=Math.max(t.length,e.length);while(t.length<i)t.push("0");while(e.length<i)e.push("0");for(var n=0;n<i;n++){var a=parseInt(t[n],10),r=parseInt(e[n],10);if(a>r)return 1;if(a<r)return-1}return 0},e.getImageInfo=function(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return new Promise(function(){var i=(0,r.default)((0,a.default)().mark((function i(n,r){var o;return(0,a.default)().wrap((function(i){while(1)switch(i.prev=i.next){case 0:if(/^\.|^\/(?=[^\/])/,!s.test(t)||!e){i.next=5;break}return i.next=4,h(t);case 4:t=i.sent;case 5:if(!c(t)){i.next=15;break}if(!d&&u[t]){i.next=14;break}return o=t,i.next=10,f(t);case 10:t=i.sent,u[o]=t,i.next=15;break;case 14:t=u[t];case 15:u[t]&&u[t].errMsg?n(u[t]):uni.getImageInfo({src:t,success:function(e){e.path=e.path.replace(/^\./,window.location.origin),e.naturalSrc=t,d?n(e):(u[t]=e,n(u[t]))},fail:function(e){n({path:t}),console.error("getImageInfo:fail ".concat(t," failed ").concat(JSON.stringify(e)))}});case 16:case"end":return i.stop()}}),i)})));return function(t,e){return i.apply(this,arguments)}}())},e.isBase64=void 0,e.isNumber=l,e.pathToBase64=h,e.sleep=function(t){return new Promise((function(e){return setTimeout(e,t)}))},e.toPx=function t(e,i){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if("number"===typeof e)return e;if(l(e))return 1*e;if("string"===typeof e){var a=/^-?([0-9]+)?([.]{1}[0-9]+){0,1}(em|rpx|px|%)$/g,r=a.exec(e);if(!e||!r)return 0;var o=r[3];e=parseFloat(e);var s=0;return"rpx"===o?s=uni.upx2px(e):"px"===o?s=1*e:"%"===o?s=e*t(i)/100:"em"===o&&(s=e*t(i||14)),n?1*s.toFixed(2):Math.round(s)}return 0};var a=n(i("f07e")),r=n(i("c964")),o=n(i("d0af"));i("ac1f"),i("00b4"),i("d3b7"),i("caad6"),i("acd8"),i("14d9"),i("e25e"),i("c19f"),i("ace4"),i("c975"),i("5cc6"),i("907a"),i("9a8c"),i("a975"),i("735e"),i("c1ac"),i("d139"),i("3a7b"),i("986a"),i("1d02"),i("d5d6"),i("82f8"),i("e91f"),i("60bd"),i("5f96"),i("3280"),i("3fcc"),i("ca91"),i("25a1"),i("cd26"),i("3c5d"),i("2954"),i("649e"),i("219c"),i("b39a"),i("72f7"),i("5319"),i("d401"),i("81b2"),i("0eb6"),i("b7ef"),i("8bd4"),i("3ca3"),i("ddb0"),i("2b3d"),i("9861"),i("e9c4"),i("d9e2"),i("99af");var s=/^(http|\/\/)/,c=function(t){return/^data:image\/(\w+);base64/.test(t)};e.isBase64=c;var d=["devtools"].includes(uni.getSystemInfoSync().platform),u={};function l(t){return/^-?\d+(\.\d+)?$/.test(t)}function f(t){var e=/data:image\/(\w+);base64,(.*)/.exec(t)||[],i=(0,o.default)(e,3);i[1],i[2];return new Promise((function(e,i){for(var n=t.split(",")[0].split(":")[1].split(";")[0],a=atob(t.split(",")[1]),r=new ArrayBuffer(a.length),o=new Uint8Array(r),s=0;s<a.length;s++)o[s]=a.charCodeAt(s);e(URL.createObjectURL(new Blob([o],{type:n})))}))}function h(t){return/^data:/.test(t)?t:new Promise((function(e,i){var n=function(){var n=new Image;n.setAttribute("crossOrigin","Anonymous"),n.onload=function(){var t=document.createElement("canvas");t.width=this.naturalWidth,t.height=this.naturalHeight,t.getContext("2d").drawImage(n,0,0);var i=t.toDataURL("image/png");e(i),t.height=t.width=0},n.src=t,n.onerror=function(e){console.error("urlToBase64 error: ".concat(t),JSON.stringify(e)),i(new Error("urlToBase64 error"))}},a=function(t){var n=new FileReader;n.onload=function(t){e(t.target.result)},n.readAsDataURL(t),n.onerror=function(t){console.error("blobToBase64 error:",JSON.stringify(t)),i(new Error("blobToBase64 error"))}},r="function"===typeof FileReader;if(s.test(t)&&r){window.URL=window.URL||window.webkitURL;var o=new XMLHttpRequest;o.open("get",t,!0),o.timeout=2e3,o.responseType="blob",o.onload=function(){200==this.status?a(this.response):n()},o.onreadystatechange=function(){0===this.status&&console.error("图片跨域了，得后端处理咯")},o.send()}else/^blob/.test(t)&&r?a(t):n()}))}},8052:function(t,e,i){"use strict";i.r(e);var n=i("21ab"),a=i("0955");for(var r in a)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(r);i("e3e9");var o=i("f0c5"),s=Object(o["a"])(a["default"],n["b"],n["c"],!1,null,"55c7a2c3",null,!1,n["a"],void 0);e["default"]=s.exports},"80a3":function(t,e,i){"use strict";i.r(e);var n=i("356b"),a=i.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(r);e["default"]=a.a},"822c":function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n={data:function(){return{}},props:{type:{type:String,default:"fixed"},backgroundColor:{type:String,default:"#fff"}},methods:{}};e.default=n},8257:function(t,e,i){t.exports=i.p+"static/images/seckill-bg.png"},8278:function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return r})),i.d(e,"a",(function(){return n}));var n={uPopup:i("5cc5").default},a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",[i("u-popup",{staticClass:"share-popup",attrs:{mode:"bottom","border-radius":"14",closeable:!0,"safe-area-inset-bottom":!0,"mask-close-able":!1},model:{value:t.showshare,callback:function(e){t.showshare=e},expression:"showshare"}},[i("v-uni-view",{staticClass:"flex row-center md bold m-t-30 m-b-30"},[t._v("分享至")]),i("v-uni-view",{staticClass:"flex row-around share-tab"},[i("v-uni-view",{staticClass:"flex-col col--center",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.getPoster.apply(void 0,arguments)}}},[i("v-uni-image",{staticClass:"share-icon",attrs:{mode:"widthFix",src:"/static/images/icon_generate_poster.png"}}),i("v-uni-view",{staticClass:"m-t-10"},[t._v("生成海报")])],1),i("v-uni-view",{attrs:{oclass:"flex-col col--center"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.shareWx.apply(void 0,arguments)}}},[i("v-uni-image",{staticClass:"share-icon",attrs:{src:"/static/images/icon_wechat.png"}}),i("v-uni-view",{staticClass:"m-t-10"},[t._v("微信好友")])],1)],1),i("v-uni-view",{staticClass:"flex row-center bg-body cancel xl",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.showshare=!1}}},[t._v("取消")])],1),i("u-popup",{staticClass:"share-poster",attrs:{mode:"center",closeable:!0,"safe-area-inset-bottom":!0},model:{value:t.showPoster,callback:function(e){t.showPoster=e},expression:"showPoster"}},[i("img",{staticStyle:{width:"640rpx"},attrs:{src:t.poster}}),i("v-uni-button",{staticClass:"flex row-center save-btn",attrs:{size:"lg"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.savePoster.apply(void 0,arguments)}}},[t._v("长按保存图片到相册")])],1),i("u-popup",{staticClass:"share-tips",attrs:{"custom-style":{background:"none"},mode:"top"},model:{value:t.showTips,callback:function(e){t.showTips=e},expression:"showTips"}},[i("v-uni-view",{staticStyle:{overflow:"hidden"}},[i("v-uni-image",{staticClass:"share-arrow",attrs:{src:"/static/images/share_arrow.png"}}),i("v-uni-view",{staticClass:"white",staticStyle:{"text-align":"center","margin-top":"280rpx"}},[i("v-uni-view",{staticClass:"bold lg"},[t._v("立即分享给好友吧")]),i("v-uni-view",{staticClass:"sm m-t-10"},[t._v("点击屏幕右上角将本页面分享给好友")])],1)],1)],1),t.enablePoster?i("poster",{attrs:{type:t.type,"share-id":t.shareId,"page-path":t.pagePath,config:t.config,qrcode:t.mnpQrcode,link:t.getLink},on:{success:function(e){arguments[0]=e=t.$handleEvent(e),t.handleSuccess.apply(void 0,arguments)},fail:function(e){arguments[0]=e=t.$handleEvent(e),t.handleFail.apply(void 0,arguments)}}}):t._e()],1)},r=[]},8551:function(t,e,i){var n=i("92ea");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=i("4f06").default;a("fd46f924",n,!0,{sourceMap:!1,shadowMode:!1})},"85c6":function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return r})),i.d(e,"a",(function(){return n}));var n={uIcon:i("90f3").default},a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{},[i("v-uni-view",{staticClass:"u-navbar",class:{"u-navbar-fixed":t.isFixed,"u-border-bottom":t.borderBottom},style:[t.navbarStyle]},[i("v-uni-view",{staticClass:"u-status-bar",style:{height:t.statusBarHeight+"px"}}),i("v-uni-view",{staticClass:"u-navbar-inner",style:[t.navbarInnerStyle]},[t.isBack?i("v-uni-view",{staticClass:"u-back-wrap",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.goBack.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"u-icon-wrap",style:{backgroundColor:t.backBg,borderRadius:"50%",padding:"8rpx"}},[i("u-icon",{attrs:{name:t.isHome?"home":t.backIconName,color:t.backIconColor,size:t.backIconSize}})],1),t.backText?i("v-uni-view",{staticClass:"u-icon-wrap u-back-text u-line-1",style:[t.backTextStyle]},[t._v(t._s(t.backText))]):t._e()],1):t._e(),t.title?i("v-uni-view",{staticClass:"u-navbar-content-title",style:[t.titleStyle]},[i("v-uni-view",{staticClass:"u-title u-line-1",style:{color:t.titleColor,fontSize:t.titleSize+"rpx",fontWeight:t.titleBold?"bold":"normal"}},[t._v(t._s(t.title))])],1):t._e(),i("v-uni-view",{staticClass:"u-slot-content"},[t._t("default")],2),i("v-uni-view",{staticClass:"u-slot-right"},[t._t("right")],2)],1)],1),t.isFixed&&!t.immersive?i("v-uni-view",{staticClass:"u-navbar-placeholder",style:{width:"100%",height:Number(t.navbarHeight)+t.statusBarHeight+"px"}}):t._e()],1)},r=[]},8810:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){}));var n=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",[this._t("default")],2)},a=[]},"8a46":function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("a9e3");var n={data:function(){return{currentSwiper:0,offset:0,barLeft:0}},components:{},props:{progressBarColor:{type:String,default:"#01B55B"},progressWidth:{type:Number,default:90},progressHeight:{type:Number,default:6},progressColor:{type:String,default:"#E5E5E5"},left:{type:Number,default:0},barWidth:{type:Number,default:30}},watch:{left:function(t){this.barLeft=t/100*this.offset}},beforeMount:function(){this.offset=this.progressWidth-this.barWidth},destroyed:function(){},methods:{}};e.default=n},"8a91":function(t,e,i){"use strict";i.r(e);var n=i("d87b3"),a=i("4e38");for(var r in a)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(r);i("d082");var o=i("f0c5"),s=Object(o["a"])(a["default"],n["b"],n["c"],!1,null,"7e5db0e3",null,!1,n["a"],void 0);e["default"]=s.exports},"8a9a":function(t,e,i){"use strict";var n=i("c826"),a=i.n(n);a.a},"8aee":function(t,e,i){var n=i("b9d8");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=i("4f06").default;a("901755d2",n,!0,{sourceMap:!1,shadowMode:!1})},"8af8":function(t,e,i){"use strict";var n=i("4185"),a=i.n(n);a.a},"8fc0":function(t,e,i){"use strict";i.r(e);var n=i("2ab4"),a=i("80a3");for(var r in a)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(r);i("1c75");var o=i("f0c5"),s=Object(o["a"])(a["default"],n["b"],n["c"],!1,null,"bf7076f2",null,!1,n["a"],void 0);e["default"]=s.exports},"8fed":function(t,e,i){"use strict";var n=i("e2db"),a=i.n(n);a.a},9052:function(t,e,i){"use strict";i.r(e);var n=i("fcf0"),a=i("481e");for(var r in a)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(r);i("6e83");var o=i("f0c5"),s=Object(o["a"])(a["default"],n["b"],n["c"],!1,null,"a36128d0",null,!1,n["a"],void 0);e["default"]=s.exports},"90d7":function(t,e,i){var n=i("24fb");e=n(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.goods-shop .title[data-v-05f2119a]{padding:%?20?%}.goods-shop .title .shop-name[data-v-05f2119a]{width:%?300?%}.goods-shop .title .shop-name .store-tag[data-v-05f2119a]{width:36px;background:linear-gradient(267deg,#ff2c3c,#f52e99);border-radius:%?6?%;padding:%?4?% %?9?%}.goods-shop .title .btn[data-v-05f2119a]{border:1px solid #ff2c3c;padding:%?6?% %?28?%}.goods-shop .progress[data-v-05f2119a]{padding-bottom:%?20?%}.goods-shop .goods[data-v-05f2119a]{display:inline-block;padding:%?20?%}',""]),t.exports=e},"92ad":function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){}));var n=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{staticClass:"u-waterfall"},[e("v-uni-view",{staticClass:"u-column",attrs:{id:"u-left-column"}},[this._t("left",null,{leftList:this.leftList})],2),e("v-uni-view",{staticClass:"u-column",attrs:{id:"u-right-column"}},[this._t("right",null,{rightList:this.rightList})],2)],1)},a=[]},"92ea":function(t,e,i){var n=i("24fb");e=n(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.u-countdown[data-v-38cbd707]{display:inline-flex;align-items:center}.u-countdown-item[data-v-38cbd707]{display:flex;flex-direction:row;align-items:center;justify-content:center;padding:%?2?%;border-radius:%?6?%;white-space:nowrap;-webkit-transform:translateZ(0);transform:translateZ(0)}.u-countdown-time[data-v-38cbd707]{margin:0;padding:0}.u-countdown-colon[data-v-38cbd707]{display:flex;flex-direction:row;justify-content:center;padding:0 %?5?%;line-height:1;align-items:center;padding-bottom:%?4?%}.u-countdown-scale[data-v-38cbd707]{-webkit-transform:scale(.9);transform:scale(.9);-webkit-transform-origin:center center;transform-origin:center center}',""]),t.exports=e},9415:function(t,e,i){"use strict";var n=i("8aee"),a=i.n(n);a.a},9446:function(t,e,i){var n=i("24fb");e=n(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.u-navbar[data-v-6d93ee5a]{width:100%}.u-navbar-fixed[data-v-6d93ee5a]{position:fixed;left:0;right:0;top:0;z-index:991}.u-status-bar[data-v-6d93ee5a]{width:100%}.u-navbar-inner[data-v-6d93ee5a]{width:100%;display:flex;flex-direction:row;justify-content:space-between;position:relative;align-items:center}.u-back-wrap[data-v-6d93ee5a]{display:flex;flex-direction:row;align-items:center;flex:1;flex-grow:0;padding:%?14?% %?14?% %?14?% %?24?%}.u-back-text[data-v-6d93ee5a]{padding-left:%?4?%;font-size:%?30?%}.u-navbar-content-title[data-v-6d93ee5a]{display:flex;flex-direction:row;align-items:center;justify-content:center;flex:1;position:absolute;left:0;right:0;height:%?60?%;text-align:center;flex-shrink:0}.u-navbar-centent-slot[data-v-6d93ee5a]{flex:1}.u-title[data-v-6d93ee5a]{line-height:%?60?%;font-size:%?32?%;flex:1}.u-navbar-right[data-v-6d93ee5a]{flex:1;display:flex;flex-direction:row;align-items:center;justify-content:flex-end}.u-slot-content[data-v-6d93ee5a]{flex:1;display:flex;flex-direction:row;align-items:center;position:relative}',""]),t.exports=e},9530:function(t,e,i){"use strict";var n=i("d720"),a=i.n(n);a.a},"953f":function(t,e,i){t.exports=i.p+"static/images/temp-bg.png"},9720:function(t,e,i){var n=i("24fb"),a=i("1de5"),r=i("c9e1"),o=i("528e");e=n(!1);var s=a(r),c=a(o);e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.coupon-list[data-v-fa6b581a]{padding:0 %?24?%;overflow:hidden}.coupon-list .coupon-item[data-v-fa6b581a]{position:relative;height:%?200?%;background-image:url('+s+");background-size:100% 100%}.coupon-list .coupon-item.gray[data-v-fa6b581a]{background-image:url("+c+")}.coupon-list .coupon-item.gray .btn.plain[data-v-fa6b581a]{color:#ccc}.coupon-list .coupon-item .price[data-v-fa6b581a]{width:%?200?%}.coupon-list .coupon-item .btn[data-v-fa6b581a]{line-height:%?52?%;height:%?52?%;position:absolute;right:%?20?%;bottom:%?20?%;width:%?120?%;text-align:center;padding:0}.coupon-list .coupon-item .btn.plain[data-v-fa6b581a]{background-color:#fff;color:#ff2c3c;border:1px solid currentColor}.coupon-list .coupon-item .receive[data-v-fa6b581a]{position:absolute;right:%?30?%;top:%?0?%;width:%?99?%;height:%?77?%}.coupon-list .icon[data-v-fa6b581a]{transition:all .4s}.coupon-list .rotate[data-v-fa6b581a]{-webkit-transform:rotate(-180deg);transform:rotate(-180deg)}.coupon-list .received[data-v-fa6b581a]{position:absolute;top:0;right:%?50?%;width:%?70?%;height:%?50?%}",""]),t.exports=e},"985a":function(t,e,i){"use strict";var n=i("8551"),a=i.n(n);a.a},9873:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){}));var n=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view")},a=[]},"98b4":function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=i("2399"),a={name:"lime-painter-image",mixins:[(0,n.children)("painter")],props:{css:[String,Object],src:String},data:function(){return{type:"image",el:{css:{},src:null}}}};e.default=a},a1ed:function(t,e,i){"use strict";i.r(e);var n=i("4aa8"),a=i.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(r);e["default"]=a.a},a51c:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){}));var n=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-text",{staticStyle:{opacity:"0"}},[this._t("default")],2)},a=[]},a712:function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return r})),i.d(e,"a",(function(){return n}));var n={uLoading:i("8fc0").default},a=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{class:"loading "+("flex"==this.type?"flex":""),style:{backgroundColor:this.backgroundColor}},[e("u-loading",{attrs:{mode:"flower",size:60}})],1)},r=[]},aa10:function(t,e,i){"use strict";var n=i("d093"),a=i.n(n);a.a},aa36:function(t,e,i){var n=i("24fb");e=n(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.u-loading-circle[data-v-bf7076f2]{display:inline-flex;vertical-align:middle;width:%?28?%;height:%?28?%;background:0 0;border-radius:50%;border:2px solid;border-color:#e5e5e5 #e5e5e5 #e5e5e5 #8f8d8e;-webkit-animation:u-circle-data-v-bf7076f2 1s linear infinite;animation:u-circle-data-v-bf7076f2 1s linear infinite}.u-loading-flower[data-v-bf7076f2]{width:20px;height:20px;display:inline-block;vertical-align:middle;-webkit-animation:a 1s steps(12) infinite;animation:u-flower-data-v-bf7076f2 1s steps(12) infinite;background:transparent url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMjAiIGhlaWdodD0iMTIwIiB2aWV3Qm94PSIwIDAgMTAwIDEwMCI+PHBhdGggZmlsbD0ibm9uZSIgZD0iTTAgMGgxMDB2MTAwSDB6Ii8+PHJlY3Qgd2lkdGg9IjciIGhlaWdodD0iMjAiIHg9IjQ2LjUiIHk9IjQwIiBmaWxsPSIjRTlFOUU5IiByeD0iNSIgcnk9IjUiIHRyYW5zZm9ybT0idHJhbnNsYXRlKDAgLTMwKSIvPjxyZWN0IHdpZHRoPSI3IiBoZWlnaHQ9IjIwIiB4PSI0Ni41IiB5PSI0MCIgZmlsbD0iIzk4OTY5NyIgcng9IjUiIHJ5PSI1IiB0cmFuc2Zvcm09InJvdGF0ZSgzMCAxMDUuOTggNjUpIi8+PHJlY3Qgd2lkdGg9IjciIGhlaWdodD0iMjAiIHg9IjQ2LjUiIHk9IjQwIiBmaWxsPSIjOUI5OTlBIiByeD0iNSIgcnk9IjUiIHRyYW5zZm9ybT0icm90YXRlKDYwIDc1Ljk4IDY1KSIvPjxyZWN0IHdpZHRoPSI3IiBoZWlnaHQ9IjIwIiB4PSI0Ni41IiB5PSI0MCIgZmlsbD0iI0EzQTFBMiIgcng9IjUiIHJ5PSI1IiB0cmFuc2Zvcm09InJvdGF0ZSg5MCA2NSA2NSkiLz48cmVjdCB3aWR0aD0iNyIgaGVpZ2h0PSIyMCIgeD0iNDYuNSIgeT0iNDAiIGZpbGw9IiNBQkE5QUEiIHJ4PSI1IiByeT0iNSIgdHJhbnNmb3JtPSJyb3RhdGUoMTIwIDU4LjY2IDY1KSIvPjxyZWN0IHdpZHRoPSI3IiBoZWlnaHQ9IjIwIiB4PSI0Ni41IiB5PSI0MCIgZmlsbD0iI0IyQjJCMiIgcng9IjUiIHJ5PSI1IiB0cmFuc2Zvcm09InJvdGF0ZSgxNTAgNTQuMDIgNjUpIi8+PHJlY3Qgd2lkdGg9IjciIGhlaWdodD0iMjAiIHg9IjQ2LjUiIHk9IjQwIiBmaWxsPSIjQkFCOEI5IiByeD0iNSIgcnk9IjUiIHRyYW5zZm9ybT0icm90YXRlKDE4MCA1MCA2NSkiLz48cmVjdCB3aWR0aD0iNyIgaGVpZ2h0PSIyMCIgeD0iNDYuNSIgeT0iNDAiIGZpbGw9IiNDMkMwQzEiIHJ4PSI1IiByeT0iNSIgdHJhbnNmb3JtPSJyb3RhdGUoLTE1MCA0NS45OCA2NSkiLz48cmVjdCB3aWR0aD0iNyIgaGVpZ2h0PSIyMCIgeD0iNDYuNSIgeT0iNDAiIGZpbGw9IiNDQkNCQ0IiIHJ4PSI1IiByeT0iNSIgdHJhbnNmb3JtPSJyb3RhdGUoLTEyMCA0MS4zNCA2NSkiLz48cmVjdCB3aWR0aD0iNyIgaGVpZ2h0PSIyMCIgeD0iNDYuNSIgeT0iNDAiIGZpbGw9IiNEMkQyRDIiIHJ4PSI1IiByeT0iNSIgdHJhbnNmb3JtPSJyb3RhdGUoLTkwIDM1IDY1KSIvPjxyZWN0IHdpZHRoPSI3IiBoZWlnaHQ9IjIwIiB4PSI0Ni41IiB5PSI0MCIgZmlsbD0iI0RBREFEQSIgcng9IjUiIHJ5PSI1IiB0cmFuc2Zvcm09InJvdGF0ZSgtNjAgMjQuMDIgNjUpIi8+PHJlY3Qgd2lkdGg9IjciIGhlaWdodD0iMjAiIHg9IjQ2LjUiIHk9IjQwIiBmaWxsPSIjRTJFMkUyIiByeD0iNSIgcnk9IjUiIHRyYW5zZm9ybT0icm90YXRlKC0zMCAtNS45OCA2NSkiLz48L3N2Zz4=) no-repeat;background-size:100%}@-webkit-keyframes u-flower-data-v-bf7076f2{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}@keyframes u-flower-data-v-bf7076f2{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}@-webkit-keyframes u-circle-data-v-bf7076f2{0%{-webkit-transform:rotate(0);transform:rotate(0)}100%{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}',""]),t.exports=e},ac51:function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("e25e"),i("a9e3");var n={computed:{posterUrl:function(){return this.poster?this.poster:this.url+"?x-oss-process=video/snapshot,t_"+parseInt(1e3*this.currentTime)+",f_jpg,w_800,m_fast"}},created:function(){this.videoId=Date.now()+Math.ceil(1e7*Math.random())+""},mounted:function(){this.VideoContext=uni.createVideoContext(this.videoId,this)},methods:{fullscreenchange:function(t){this.state=t.detail.fullScreen},timeupdate:function(t){this.duration=t.detail.duration,this.currentTime=t.detail.currentTime}},watch:{state:function(t,e){var i=this;t?(this.VideoContext.play(),setTimeout((function(){i.VideoContext.requestFullScreen({direction:i.direction})}),10)):this.VideoContext.pause()}},data:function(){return{VideoContext:{},state:!1,currentTime:0,duration:0,videoId:""}},props:{poster:{type:[String,Boolean],default:function(){return""}},url:{type:String,default:function(){return""}},direction:{type:Number,default:function(){return 0}},width:{type:String,default:function(){return"750rpx"}},height:{type:String,default:function(){return"450rpx"}}}};e.default=n},ac99:function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("a9e3");var n=i("2399"),a={name:"lime-painter-text",mixins:[(0,n.children)("painter")],props:{css:[String,Object],text:[String,Number],replace:Object},data:function(){return{type:"text",el:{css:{},text:null}}}};e.default=a},ad6f:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){}));var n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"float-tab ~column"},[i("v-uni-navigator",{staticClass:"tab-img",style:{top:3*t.top+"px"},attrs:{"hover-class":"none","open-type":"switchTab",url:"/pages/index/index"}},[i("v-uni-image",{staticClass:"tab-icon",attrs:{src:"/static/images/icon_float_home.png"}})],1),i("v-uni-navigator",{staticClass:"tab-img",style:{top:2*t.top+"px"},attrs:{"hover-class":"none","open-type":"navigate",url:"/bundle/pages/chat/chat"}},[i("v-uni-image",{staticClass:"tab-icon",attrs:{src:"/static/images/icon_float_help.png"}})],1),i("v-uni-navigator",{staticClass:"tab-img",style:{top:t.top+"px"},attrs:{"hover-class":"none","open-type":"switchTab",url:"/pages/shop_cart/shop_cart"}},[i("v-uni-image",{staticClass:"tab-icon",attrs:{src:"/static/images/icon_float_cart.png"}})],1),i("v-uni-image",{staticClass:"tab-img",staticStyle:{"z-index":"99"},style:{transform:"rotateZ("+(t.showMore?135:0)+"deg)"},attrs:{src:"/static/images/icon_float_more.png"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onChange.apply(void 0,arguments)}}})],1)},a=[]},ae1d:function(t,e,i){var n=i("24fb");e=n(!1),e.push([t.i,"@-webkit-keyframes _show-data-v-6d8b3c66{0%{opacity:0}100%{opacity:1}}@keyframes _show-data-v-6d8b3c66{0%{opacity:0}100%{opacity:1}}\n\n\n\n",""]),t.exports=e},ae3a:function(t,e,i){var n=i("24fb");e=n(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.get-coupon .coupons .text[data-v-55c7a2c3]{width:%?100?%;flex:none}.get-coupon .coupons .con[data-v-55c7a2c3]{width:%?400?%}.get-coupon .coupons .coupons-item[data-v-55c7a2c3]{overflow:hidden}.get-coupon .coupons .coupons-item > uni-view[data-v-55c7a2c3]{position:relative;height:%?40?%;line-height:%?40?%;padding:0 %?18?%;border-radius:%?6?%;box-sizing:border-box;background-color:#ff2c3c;color:#fff;white-space:nowrap;overflow:hidden}.get-coupon .coupons .coupons-item > uni-view[data-v-55c7a2c3]::after, .get-coupon .coupons .coupons-item > uni-view[data-v-55c7a2c3]::before{content:"";display:block;width:%?20?%;height:%?20?%;position:absolute;left:%?-14?%;background-color:#fff;border-radius:50%;border:1px solid currentColor;box-sizing:border-box}.get-coupon .coupons .coupons-item > uni-view[data-v-55c7a2c3]::after{right:%?-14?%;left:auto}',""]),t.exports=e},b0c4:function(t,e,i){var n=i("c9cb");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=i("4f06").default;a("2e5acf72",n,!0,{sourceMap:!1,shadowMode:!1})},b27d:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){}));var n=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{staticClass:"cu-progress",style:"height: "+this.progressHeight+"; width: "+this.progressWidth+";background-color: "+this.progressColor},[e("v-uni-view",{staticClass:"cu-progress-bar",style:"background-color: "+this.progressBarColor+";left: "+this.barLeft+"rpx; width:"+this.barWidth+"rpx"})],1)},a=[]},b83e:function(t,e,i){var n=i("24fb");e=n(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.loading[data-v-061dd044]{position:fixed;top:0;left:0;width:100vw;height:100vh;z-index:9999;display:flex;justify-content:center;align-items:center}.loading.flex[data-v-061dd044]{position:static;flex:1;width:100%}.loading .loading-img[data-v-061dd044]{width:%?100?%;height:%?100?%}',""]),t.exports=e},b98a:function(t,e,i){"use strict";i("7a82");var n=i("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("a9e3"),i("e9c4"),i("fb6a");var a=i("20b7"),r=n(i("8ffc")),o={name:"BubbleTips",props:{discharge:{type:Boolean,default:!1},top:{type:String,default:"40rpx"},left:{type:String,default:"20rpx"},updateTime:{type:Number,default:3e5}},data:function(){return{index:r.default.get("currentIndex")||0,list:[],currentList:[],timer:null,showBubble:!1}},watch:{index:function(t,e){var i=this;if(!(this.index-this.list.length>=0))return this.timer&&(clearInterval(this.timer),this.timer=null),void this.fadeUpBubble();this.showBubble=!1;var n=setTimeout((function(){r.default.set("currentIndex",0),i.timer&&(clearInterval(i.timer),i.timer=null),i.fadeUpBubble(),clearTimeout(n)}),2e3)},discharge:function(){if(this.discharge)return r.default.set("currentIndex",this.index),clearInterval(this.timer),this.timer=null,!1;var t=r.default.get("currentInex")||this.list.length;t-this.list.length<0&&(this.timer&&(setInterval(this.timer),this.timer=null),this.fadeUpBubble())}},methods:{getBubbleListsFunc:function(){var t=this;(0,a.getBubbleLists)().then((function(e){if(e){t.list=e.data.lists;var i=1e3*e.data.time;r.default.set("bubbleList",JSON.stringify(t.list),300),r.default.set("requestTime",i),t.timer&&(clearInterval(t.timer),t.timer=null),t.fadeUpBubble()}}))},fadeUpBubble:function(){var t=this,e=r.default.get("requestTime"),i=new Date;if(this.showBubble=!0,this.index=r.default.get("currentIndex")||0,this.list=r.default.get("bubbleList")?JSON.parse(r.default.get("bubbleList")):[],i.getTime()-e>=this.updateTime)return this.getBubbleListsFunc(),void r.default.set("currentIndex",0,300);this.timer=setInterval((function(){t.currentList=t.list.slice(t.index,t.index+1),r.default.set("currentIndex",++t.index)}),4e3)}},created:function(){var t=r.default.get("currentIndex")||0,e=r.default.get("requestTime"),i=new Date,n=r.default.get("bubbleList")?JSON.parse(r.default.get("bubbleList")):[];n.length<=0?(this.getBubbleListsFunc(),r.default.set("currentIndex",0)):t-n.length>=0?(r.default.set("currentIndex",0),this.timer&&(clearInterval(this.timer),this.timer=null),this.fadeUpBubble()):i.getTime()-e>=this.updateTime?(this.getBubbleListsFunc(),r.default.set("currentIndex",0)):(this.timer&&(clearInterval(this.timer),this.timer=null),this.fadeUpBubble())},onLoad:function(){},destroyed:function(){this.timer&&(clearInterval(this.timer),this.timer=null)}};e.default=o},b9d8:function(t,e,i){var n=i("24fb");e=n(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.float-tab[data-v-130bc95c]{position:fixed;right:%?16?%;bottom:%?200?%;width:%?96?%;height:%?96?%;z-index:777}.float-tab .tab-img[data-v-130bc95c]{width:100%;height:100%;position:absolute;transition:all .5s}.float-tab .tab-img .tab-icon[data-v-130bc95c]{width:100%;height:100%}',""]),t.exports=e},ba81:function(t,e,i){"use strict";i.r(e);var n=i("3249"),a=i.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(r);e["default"]=a.a},baa5:function(t,e,i){"use strict";var n=i("23e7"),a=i("e58c");n({target:"Array",proto:!0,forced:a!==[].lastIndexOf},{lastIndexOf:a})},bb2f:function(t,e,i){"use strict";var n=i("d039");t.exports=!n((function(){return Object.isExtensible(Object.preventExtensions({}))}))},bde1:function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n={data:function(){return{mescroll:null}},onPullDownRefresh:function(){this.mescroll&&this.mescroll.onPullDownRefresh()},onPageScroll:function(t){this.mescroll&&this.mescroll.onPageScroll(t)},onReachBottom:function(){this.mescroll&&this.mescroll.onReachBottom()},methods:{mescrollInit:function(t){console.log(t),this.mescroll=t,this.mescrollInitByRef()},mescrollInitByRef:function(){if(!this.mescroll||!this.mescroll.resetUpScroll){var t=this.$refs.mescrollRef;t&&(this.mescroll=t.mescroll)}},downCallback:function(){var t=this;this.mescroll.optUp.use?this.mescroll.resetUpScroll():setTimeout((function(){t.mescroll.endSuccess()}),500)},upCallback:function(){var t=this;setTimeout((function(){t.mescroll.endErr()}),500)}},mounted:function(){this.mescrollInitByRef()}},a=n;e.default=a},be37:function(t,e,i){"use strict";i.r(e);var n=i("f48a"),a=i("64ea");for(var r in a)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(r);i("9530");var o=i("f0c5"),s=Object(o["a"])(a["default"],n["b"],n["c"],!1,null,"64f1d435",null,!1,n["a"],void 0);e["default"]=s.exports},c03f:function(t,e,i){"use strict";i("7a82");var n=i("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=n(i("d0ff"));i("a9e3"),i("d3b7"),i("159b"),i("99af"),i("fb6a"),i("4de4"),i("d81d"),i("e9c4"),i("caad6"),i("2532"),i("c975"),i("14d9"),i("6062"),i("3ca3"),i("ddb0"),i("baa5");var r={data:function(){return{shop:{},checkedGoods:{stock:0},outOfStock:[],specList:[],disable:[],goodsNum:1,showPop:!1}},props:{show:{type:Boolean},goods:{type:Object},showAdd:{type:Boolean,default:!0},showBuy:{type:Boolean,default:!0},showConfirm:{type:Boolean,default:!1},redBtnText:{type:String,default:"立即购买"},yellowBtnText:{type:String,default:"加入购物车"},showStock:{type:Boolean,default:!0},group:{type:Boolean,default:!1},isSeckill:{type:Boolean,default:!1},type:{type:Number,default:1},showComfirm:{type:Boolean,default:!1}},computed:{specValueText:function(){var t,e,i,n=this,a=null===(t=this.checkedGoods.spec_value_ids)||void 0===t?void 0:t.split(","),r="";return a&&a.forEach((function(t,e){""==t&&(r+=n.specList[e].name+",")})),0!=(null===(e=this.checkedGoods)||void 0===e?void 0:e.stock)&&""==r?"已选择 ".concat(this.checkedGoods.spec_value_str," ").concat(this.goodsNum," 件"):0!=(null===(i=this.checkedGoods)||void 0===i?void 0:i.stock)&&r.length?"请选择 ".concat(r.slice(0,r.length-1)):"库存不足"}},watch:{goods:function(t){console.log(t,12345),this.specList=t.goods_spec||[];var e=t.goods_item||[];this.shop=t.shop||{},this.outOfStock=e.filter((function(t){return 0==t.stock}));var i=e.filter((function(t){return 0!=t.stock}));0!=i.length?(i[0].spec_value_ids_arr=i[0].spec_value_ids.split(","),this.checkedGoods=i[0]):(e[0].spec_value_ids_arr=[],this.disable=e.map((function(t){return t.spec_value_ids.split(",")})),this.checkedGoods=e[0])},specList:function(t){var e=this,i=this.goods.goods_item.filter((function(t){return e.checkedGoods.spec_value_ids===t.spec_value_ids}));if(0!=i.length){var n=JSON.parse(JSON.stringify(i[0]));n.spec_value_ids_arr=n.spec_value_ids.split(","),this.checkedGoods=n,this.checkedGoods.stock<this.goodsNum&&(this.goodsNum=this.checkedGoods.stock),this.$emit("change",{detail:this.checkedGoods})}var a=this.checkedGoods.spec_value_ids_arr,r=this.outOfStock,o=this.getArrResult(a,r);this.disable=this.getOutOfStockArr(o,a)},show:function(t){this.showPop=t}},created:function(){console.log("spec")},methods:{isDisable:function(t){if(this.disable.includes(t))return!0;var e=this.checkedGoods,i=this.goods.goods_item.filter((function(t){return 0==e.stock&&e.spec_value_ids===t.spec_value_ids}));return i.length?e.spec_value_ids_arr.includes(t+""):void 0},onClose:function(){this.$emit("close")},onClick:function(t){var e=this.checkedGoods,i=this.goodsNum;return-1!=this.specValueText.indexOf("请选择")?this.$toast({title:this.specValueText}):0==e.stock?this.$toast({title:"当前选择库存不足"}):(e.goodsNum=i,e.type=this.type,console.log("checkedGoods",e),void this.$emit(t,{detail:e}))},choseSpecItem:function(t,e){var i=this.specList[t].spec_value[e].id,n=this.checkedGoods.spec_value_ids_arr;i==n[t]?n[t]="":n[t]=i,this.checkedGoods.spec_value_ids_arr=n,this.checkedGoods.spec_value_ids=n.join(","),console.log(this.checkedGoods),this.specList=(0,a.default)(this.specList)},getOutOfStockArr:function(t,e){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];return t.forEach((function(t){t.num>=e.length-1&&i.push.apply(i,(0,a.default)(t.different))})),i},getArrIdentical:function(t,e){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0;return t.forEach((function(t){e.forEach((function(e){t==e&&(n+=1,i.push(t))}))})),{num:n,different:this.getArrDifference((0,a.default)(new Set(i)).map(Number),e.map(Number)),identical:(0,a.default)(new Set(i))}},getArrResult:function(t,e){var i=this,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];return e.forEach((function(e){var a=i.getArrIdentical(t,e.spec_value_ids.split(","));void 0!=a&&0!=a.length&&n.push(a)})),n},getArrDifference:function(t,e){return t.concat(e).filter((function(t,e,i){return i.indexOf(t)==i.lastIndexOf(t)}))},previewImage:function(t){uni.previewImage({current:t,urls:[t]})}}};e.default=r},c13e:function(t,e,i){"use strict";i.r(e);var n=i("822c"),a=i.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(r);e["default"]=a.a},c29e:function(t,e,i){"use strict";var n=i("db1f"),a=i.n(n);a.a},c4b5:function(t,e,i){"use strict";i.r(e);var n=i("ac99"),a=i.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(r);e["default"]=a.a},c60f:function(t,e,i){"use strict";i("7a82");var n=i("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.cancelIntegralOrder=function(t){return a.default.post("integral_order/cancel",{id:t})},e.closeBargainOrder=function(t){return a.default.get("bargain/closeBargain",{params:t})},e.confirmIntegralOrder=function(t){return a.default.post("integral_order/confirm",{id:t})},e.delIntegralOrder=function(t){return a.default.post("integral_order/del",{id:t})},e.getActivityGoodsLists=function(t){return a.default.get("activity_area/activityGoodsList",{params:t})},e.getBargainActivityDetail=function(t){return a.default.get("bargain/bargainDetail",{params:t})},e.getBargainActivityList=function(t){return a.default.get("bargain/orderList",{params:t})},e.getBargainDetail=function(t){return a.default.get("bargain/detail",{params:t})},e.getBargainList=function(t){return a.default.get("bargain/lists",{params:t})},e.getBargainNumber=function(){return a.default.get("bargain/barginNumber")},e.getBargainPost=function(t){return a.default.get("share/shareBargain",{params:t})},e.getCoupon=function(t){return a.default.post("coupon/getCoupon",{coupon_id:t})},e.getCouponList=function(t){return a.default.get("coupon/getCouponList",{params:t})},e.getGroupList=function(t){return a.default.get("team/activity",{params:t})},e.getIntegralGoods=function(t){return a.default.get("integral_goods/lists",{params:t})},e.getIntegralGoodsDetail=function(t){return a.default.get("integral_goods/detail",{params:t})},e.getIntegralOrder=function(t){return a.default.get("integral_order/lists",{params:t})},e.getIntegralOrderDetail=function(t){return a.default.get("integral_order/detail",{params:{id:t}})},e.getIntegralOrderTraces=function(t){return a.default.get("integral_order/orderTraces",{params:{id:t}})},e.getMyCoupon=function(t){return a.default.get("coupon/myCouponList",{params:t})},e.getOrderCoupon=function(t){return a.default.post("coupon/getBuyCouponList",t)},e.getSeckillGoods=function(t){return a.default.get("seckill_goods/getSeckillGoods",{params:t})},e.getSeckillTime=function(){return a.default.get("seckill_goods/getSeckillTime")},e.getSignLists=function(){return a.default.get("sign/lists")},e.getSignRule=function(){return a.default.get("sign/rule")},e.getTeamInfo=function(t){return a.default.get("team/teamInfo",{params:t})},e.getUserGroup=function(t){return a.default.get("team/record",{params:t})},e.helpBargain=function(t){return a.default.post("bargain/knife",t)},e.integralSettlement=function(t){return a.default.get("integral_order/settlement",{params:t})},e.integralSubmitOrder=function(t){return a.default.post("integral_order/submitOrder",t)},e.launchBargain=function(t){return a.default.post("bargain/sponsor",t)},e.teamBuy=function(t){return a.default.post("team/buy",t)},e.teamCheck=function(t){return a.default.post("team/check",t)},e.teamKaiTuan=function(t){return a.default.post("team/kaituan",t)},e.userSignIn=function(){return a.default.get("sign/sign")};var a=n(i("3b33"));i("b08d")},c61c:function(t,e,i){var n=i("24fb"),a=i("1de5"),r=i("fd7f"),o=i("953f"),s=i("8257");e=n(!1);var c=a(r),d=a(o),u=a(s);e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */uni-page-body[data-v-4338634c]{\n  /* 定义一些主题色及基础样式 */font-family:PingFang SC,Arial,Hiragino Sans GB,Microsoft YaHei,sans-serif;font-size:%?28?%;color:#333;padding-bottom:env(safe-area-inset-bottom);background-color:#f6f6f6}body.?%PAGE?%[data-v-4338634c]{background-color:#f6f6f6}.bold[data-v-4338634c]{font-weight:700}\n/* 定义字体颜色 */.primary[data-v-4338634c]{color:#ff2c3c}.bg-primary[data-v-4338634c]{background-color:#ff2c3c}.bg-white[data-v-4338634c]{background-color:#fff}.bg-body[data-v-4338634c]{background-color:#f6f6f6}.bg-gray[data-v-4338634c]{background-color:#e5e5e5}.black[data-v-4338634c]{color:#101010}.white[data-v-4338634c]{color:#fff}.normal[data-v-4338634c]{color:#333}.lighter[data-v-4338634c]{color:#666}.muted[data-v-4338634c]{color:#999}\n/* 定义字体大小 */.xxl[data-v-4338634c]{font-size:%?36?%}.xl[data-v-4338634c]{font-size:%?34?%}.lg[data-v-4338634c]{font-size:%?32?%}.md[data-v-4338634c]{font-size:%?30?%}.nr[data-v-4338634c]{font-size:%?28?%}.sm[data-v-4338634c]{font-size:%?26?%}.xs[data-v-4338634c]{font-size:%?24?%}.xxs[data-v-4338634c]{font-size:%?22?%}\n/* 定义常用外边距 */.ml5[data-v-4338634c]{margin-left:%?5?%}.ml10[data-v-4338634c]{margin-left:%?10?%}.ml20[data-v-4338634c]{margin-left:%?20?%}.ml30[data-v-4338634c]{margin-left:%?30?%}.mr5[data-v-4338634c]{margin-right:%?5?%}.mr10[data-v-4338634c]{margin-right:%?10?%}.mr20[data-v-4338634c]{margin-right:%?20?%}.mr30[data-v-4338634c]{margin-right:%?30?%}.mt5[data-v-4338634c]{margin-top:%?5?%}.mt10[data-v-4338634c]{margin-top:%?10?%}.mt20[data-v-4338634c]{margin-top:%?20?%}.mt30[data-v-4338634c]{margin-top:%?30?%}.mb5[data-v-4338634c]{margin-bottom:%?5?%}.mb10[data-v-4338634c]{margin-bottom:%?10?%}.mb20[data-v-4338634c]{margin-bottom:%?20?%}.mb30[data-v-4338634c]{margin-bottom:%?30?%}\n/* 定义常用的弹性布局 */.flex1[data-v-4338634c]{flex:1}.flexnone[data-v-4338634c]{flex:none}.wrap[data-v-4338634c]{flex-wrap:wrap}.row[data-v-4338634c]{display:flex;align-items:center}.row-center[data-v-4338634c]{display:flex;align-items:center;justify-content:center}.row-end[data-v-4338634c]{display:flex;align-items:center;justify-content:flex-end}.row-between[data-v-4338634c]{display:flex;align-items:center;justify-content:space-between}.row-around[data-v-4338634c]{display:flex;align-items:center;justify-content:space-around}.column[data-v-4338634c]{display:flex;flex-direction:column;justify-content:center}.column-center[data-v-4338634c]{display:flex;flex-direction:column;align-items:center;justify-content:center}.column-around[data-v-4338634c]{display:flex;flex-direction:column;align-items:center;justify-content:space-around}.column-end[data-v-4338634c]{display:flex;flex-direction:column;align-items:center;justify-content:flex-end}.column-between[data-v-4338634c]{display:flex;flex-direction:column;align-items:center;justify-content:space-between}\n/* 超出隐藏 */.line1[data-v-4338634c]{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.line-1[data-v-4338634c]{word-break:break-all;display:-webkit-box;-webkit-line-clamp:1;-webkit-box-orient:vertical;overflow:hidden}.line2[data-v-4338634c]{word-break:break-all;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical;overflow:hidden}\n/* 中划线 */.line-through[data-v-4338634c]{text-decoration:line-through}\n/* br60 */.br60[data-v-4338634c]{border-radius:%?60?%}\n/* 初始化按钮 */uni-page-body uni-button[data-v-4338634c]{padding:0;margin:0;background-color:initial;font-weight:400;font-size:%?28?%;overflow:unset;margin-left:0;margin-right:0}uni-page-body uni-button[data-v-4338634c]::after{border:none}uni-button[type=primary][data-v-4338634c]{background-color:#ff2c3c}.button-hover[type=primary][data-v-4338634c]{background-color:#ff2c3c}uni-button[disabled][type=primary][data-v-4338634c]{background-color:#ff2c3c}\n/* 按钮大小 */uni-button[size="xs"][data-v-4338634c]{line-height:%?58?%;height:%?58?%;font-size:%?26?%;padding:0 %?30?%}uni-button[size="sm"][data-v-4338634c]{line-height:%?62?%;height:%?62?%;font-size:%?28?%;padding:0 %?30?%}uni-button[size="md"][data-v-4338634c]{line-height:%?70?%;height:%?70?%;font-size:%?30?%;padding:0 %?30?%}uni-button[size="lg"][data-v-4338634c]{line-height:%?80?%;height:%?80?%;font-size:%?32?%;padding:0 %?30?%}.icon-xs[data-v-4338634c]{min-height:%?28?%;min-width:%?28?%;height:%?28?%;width:%?28?%;vertical-align:middle}.icon-sm[data-v-4338634c]{min-height:%?30?%;min-width:%?30?%;height:%?30?%;width:%?30?%;vertical-align:middle}.icon[data-v-4338634c]{min-height:%?34?%;min-width:%?34?%;height:%?34?%;width:%?34?%;vertical-align:middle}.icon-md[data-v-4338634c]{min-height:%?44?%;min-width:%?44?%;height:%?44?%;width:%?44?%;vertical-align:middle}.icon-lg[data-v-4338634c]{min-height:%?52?%;min-width:%?52?%;height:%?52?%;width:%?52?%;vertical-align:middle}.icon-xl[data-v-4338634c]{min-height:%?64?%;min-width:%?64?%;height:%?64?%;width:%?64?%;vertical-align:middle}.icon-xxl[data-v-4338634c]{min-height:%?120?%;min-width:%?120?%;height:%?120?%;width:%?120?%;vertical-align:middle}.img-null[data-v-4338634c]{width:%?300?%;height:%?300?%}\n/* 隐藏滚动条 */[data-v-4338634c]::-webkit-scrollbar{width:0;height:0;color:transparent}.goods-details[data-v-4338634c]{padding-bottom:calc(%?120?% + env(safe-area-inset-bottom))}.goods-details .seckill .price[data-v-4338634c]{width:%?504?%;height:100%;background-size:100%}.goods-details .seckill .down[data-v-4338634c]{flex:1}.goods-details .group[data-v-4338634c]{width:100%;background-size:100%}.goods-details .group .group-num[data-v-4338634c]{border:1px solid #fff;border-radius:%?4?%}.goods-details .group .group-num .group-icon[data-v-4338634c]{background:#fff;padding:%?3?% %?7?%}.goods-details .group .down[data-v-4338634c]{height:100%;background-color:#fff5e1;padding:0 %?20?%}.goods-details .goods-info[data-v-4338634c]{position:relative;padding:%?20?%}.goods-details .goods-info .info-header .price[data-v-4338634c]{align-items:baseline}.goods-details .goods-info .name[data-v-4338634c]{flex:1}.goods-details .goods-info .icon-share[data-v-4338634c]{width:%?134?%;height:%?60?%}.goods-details .goods-info .vip-price[data-v-4338634c]{margin:0 %?24?%;background-color:#ffe9ba;line-height:%?36?%;border-radius:%?6?%;overflow:hidden}.goods-details .goods-info .vip-price .price-name[data-v-4338634c]{background-color:#101010;padding:%?3?% %?12?%;color:#ffd4b7;position:relative;overflow:hidden}.goods-details .goods-info .vip-price .price-name[data-v-4338634c]::after{content:"";display:block;width:%?20?%;height:%?20?%;position:absolute;right:%?-15?%;background-color:#ffe9ba;border-radius:50%;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%);box-sizing:border-box}.goods-details .details-null[data-v-4338634c]{padding-top:%?140?%;margin-bottom:%?100?%}.goods-details .spec[data-v-4338634c]{padding:%?24?% %?24?%}.goods-details .spec .text[data-v-4338634c]{width:%?100?%}.goods-details .evaluation .title[data-v-4338634c]{height:%?100?%;border-bottom:1px solid #e5e5e5;padding:0 %?24?%}.goods-details .evaluation .con[data-v-4338634c]{padding:%?30?% %?24?%}.goods-details .evaluation .user-info .avatar[data-v-4338634c]{width:%?60?%;height:%?60?%;border-radius:50%}.goods-details .details .title[data-v-4338634c]{line-height:%?88?%;text-align:center}.goods-details .details > .content[data-v-4338634c]{padding:0 %?20?% %?20?%}.goods-details .details > .content[data-v-4338634c]  uni-image{vertical-align:middle}.goods-details .details > .content[data-v-4338634c]  img{vertical-align:middle}.goods-details .footer[data-v-4338634c]{height:%?100?%;position:fixed;bottom:0;left:0;right:0;box-sizing:initial;padding-bottom:env(safe-area-inset-bottom)}.goods-details .footer .btn[data-v-4338634c]{width:%?100?%;height:%?100?%;position:relative;line-height:1.3}.goods-details .footer .cart-num[data-v-4338634c]{position:absolute;left:%?60?%;top:%?6?%}.goods-details .footer .add-cart[data-v-4338634c],\n.goods-details .footer .right-buy[data-v-4338634c],\n.goods-details .footer .consult-btn[data-v-4338634c]{flex:1;text-align:center;padding:%?16?% 0}.goods-details .footer .add-cart[data-v-4338634c]{background-color:#ffa630}.goods-details .footer .right-buy[data-v-4338634c]{background-color:#ff2c3c}.goods-details .footer .consult-btn[data-v-4338634c]{background:linear-gradient(90deg,#ff8e00 0,#ff2c3c)}.goods-details .group-play .title[data-v-4338634c]{padding:%?20?% %?28?%;border-bottom:1px solid #e5e5e5}.goods-details .group-play .steps[data-v-4338634c]{padding:%?20?% %?28?%}.goods-details .group-play .steps .step[data-v-4338634c]{flex:none}.goods-details .group-play .steps .line[data-v-4338634c]{flex:1;border:1px dashed #999;margin:0 %?20?%}.goods-details .group-play .steps .number[data-v-4338634c]{border:%?1?% solid #707070;width:%?28?%;height:%?28?%;border-radius:50%;line-height:%?28?%;text-align:center;margin-right:%?6?%}.goods-details .group-list .group-item[data-v-4338634c]{padding:%?20?% %?24?%}.goods-details .group-list .group-item[data-v-4338634c]:not(:last-of-type){border-bottom:1px solid #e5e5e5}.goods-details .group-list .group-item .group-btn[data-v-4338634c]{background:linear-gradient(90deg,#f95f2f,#ff2c3c);height:%?58?%;padding-left:%?28?%;padding-right:%?28?%;margin-left:%?30?%;box-shadow:0 %?6?% %?12?% rgba(249,47,138,.4)}.goods-details .share-money[data-v-4338634c]{position:fixed;left:%?20?%;bottom:calc(%?130?% + env(safe-area-inset-bottom));-webkit-transform:scale(0);transform:scale(0);transition:all .3s}.goods-details .share-money.show[data-v-4338634c]{-webkit-transform:scale(1);transform:scale(1)}.goods-details .share-money .share-close[data-v-4338634c]{width:%?34?%;height:%?34?%;background:#a7a7a7;border-radius:50%}.goods-details .share-money .share-con[data-v-4338634c]{background:url('+c+');width:%?241?%;height:%?208?%;background-size:100%;padding-top:%?20?%;text-align:center}.round-box[data-v-4338634c]{border-radius:%?20?%}.m-x-20[data-v-4338634c]{margin-left:%?20?%;margin-right:%?20?%}.bg-vip[data-v-4338634c]{background:url(data:image/png;base64,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) no-repeat;background-size:contain;height:%?240?%;padding:%?30?% %?24?%}.bg-vip .vip-name[data-v-4338634c]{font-size:%?24?%;background-color:#e0a356;width:%?150?%;border-radius:%?25?%;text-align:center;line-height:%?30?%;padding:%?5?% 0;margin-bottom:%?10?%;position:relative}.bg-vip .vip-name[data-v-4338634c]::before{position:absolute;content:"";top:%?-7?%;right:%?10?%;width:0;height:0;border-top:50px solid #e0a356;border-right:50px solid transparent;border-left:50px solid transparent;-webkit-transform:scale(.15);transform:scale(.15)}.bg-team[data-v-4338634c]{display:flex;background:url('+d+") no-repeat;background-size:contain;height:%?240?%;padding:%?30?% %?24?%}.bg-team .tip[data-v-4338634c]{font-size:%?24?%;color:#000;background-color:#fff;padding:%?5?% %?10?%;border-radius:%?10?%;margin-right:%?10?%}.bg-team__price[data-v-4338634c]{color:#fff;flex:1;display:flex;justify-content:space-between;padding:0 %?20?%}.bg-team__time[data-v-4338634c]{display:flex;flex-direction:column;align-items:center;width:%?240?%}.bg-seckill[data-v-4338634c]{display:flex;background:url("+u+") no-repeat;background-size:contain;height:%?240?%;padding:%?30?% %?24?%}.bg-seckill .tip[data-v-4338634c]{font-size:%?24?%;color:#000;background-color:#fff;padding:%?5?% %?10?%;border-radius:%?10?%;margin-right:%?10?%}.bg-seckill__price[data-v-4338634c]{color:#fff;flex:1;display:flex;justify-content:space-between;padding:0 %?20?%}.bg-seckill__time[data-v-4338634c]{display:flex;flex-direction:column;align-items:center;width:%?240?%}",""]),t.exports=e},c826:function(t,e,i){var n=i("c61c");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=i("4f06").default;a("42ace00e",n,!0,{sourceMap:!1,shadowMode:!1})},c971:function(t,e,i){"use strict";var n=i("eeda"),a=i.n(n);a.a},c9cb:function(t,e,i){var n=i("24fb");e=n(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.spec-contain[data-v-23bc8afc]{border-radius:%?10?% %?10?% 0 0;overflow:hidden;position:relative}.spec-contain .close[data-v-23bc8afc]{position:absolute;right:%?10?%;top:%?10?%}.spec-contain .header[data-v-23bc8afc]{padding:%?30?%;padding-right:%?70?%;align-items:flex-start;border:1px solid #e5e5e5}.spec-contain .spec-main[data-v-23bc8afc]{padding:0 %?24?%}.spec-contain .spec-main .spec-list[data-v-23bc8afc]{padding:%?30?% %?20?%}.spec-contain .spec-main .spec-list .spec-item[data-v-23bc8afc]{line-height:%?54?%;padding:0 %?30?%;background-color:#f4f4f4;border-radius:%?60?%;margin:0 %?20?% %?20?% 0;border:1px solid #f4f4f4}.spec-contain .spec-main .spec-list .spec-item.checked[data-v-23bc8afc]{background-color:#ffe9eb;color:#ff2c3c;border-color:currentColor}.spec-contain .spec-main .spec-list .spec-item.spec-disabled[data-v-23bc8afc]{position:relative}.spec-contain .spec-main .spec-list .spec-item.spec-disabled[data-v-23bc8afc]::after{content:"缺货";position:absolute;right:%?-20?%;top:%?-24?%;color:#fff;width:%?70?%;height:%?36?%;text-align:center;line-height:%?36?%;font-size:%?22?%;border-radius:%?20?% %?20?% %?20?% 0;background-color:#ff2c3c}.spec-contain .spec-main .spec-list .spec-item.unspec-disabled[data-v-23bc8afc]::after{background-color:grey}.spec-contain .disabled[data-v-23bc8afc]{opacity:.4}.spec-contain .btns[data-v-23bc8afc]{height:%?120?%;padding:0 %?30?%;margin-top:%?80?%}.spec-contain .btns .add-cart[data-v-23bc8afc]{background-color:#ff9e1e}.spec-contain .btns .btn[data-v-23bc8afc]{margin:0 %?10?%;flex:1}.spec-contain .vip-price[data-v-23bc8afc]{margin:0 %?24?%;background-color:#ffe9ba;line-height:%?36?%;border-radius:%?6?%;overflow:hidden}.spec-contain .vip-price .price-name[data-v-23bc8afc]{background-color:#101010;padding:%?3?% %?12?%;color:#ffd4b7;position:relative;overflow:hidden}.spec-contain .vip-price .price-name[data-v-23bc8afc]::after{content:"";display:block;width:%?20?%;height:%?20?%;position:absolute;right:%?-15?%;background-color:#ffe9ba;border-radius:50%;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%);box-sizing:border-box}',""]),t.exports=e},c9e1:function(t,e,i){t.exports=i.p+"static/images/coupon_bg.png"},cb18:function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("a9e3"),i("d81d");getApp();var n={data:function(){return{currentSwiper:0,urls:[],showPlay:!0,showControls:!1}},props:{imgUrls:{type:Array,default:function(){return[]}},circular:{type:Boolean,default:!0},interval:{type:Number,default:3e3},duration:{type:Number,default:500},video:{type:String},autoplay:{type:Boolean,default:!0},borderRadius:{type:[Number,String],default:0}},watch:{imgUrls:{handler:function(t){this.urls=t.map((function(t){return{url:t.uri||t.image}}))},immediate:!0}},methods:{swiperChange:function(t){this.currentSwiper=t.detail.current},previewImage:function(t){uni.previewImage({current:t,urls:this.imgUrls.map((function(t){return t.uri}))})}},computed:{poster:function(){return this.urls[0]?this.urls[0].url:""},swiperLength:function(){var t=this.urls.length;return this.video?++t:t}}};e.default=n},cb29:function(t,e,i){"use strict";var n=i("23e7"),a=i("81d5"),r=i("44d2");n({target:"Array",proto:!0},{fill:a}),r("fill")},cef3:function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return r})),i.d(e,"a",(function(){return n}));var n={uNavbar:i("e131").default,tabs:i("741a").default,tab:i("5652").default,bubbleTips:i("1927").default,productSwiper:i("8a91").default,priceFormat:i("fefe").default,uCountDown:i("7226").default,getCoupon:i("8052").default,uIcon:i("90f3").default,uImage:i("ba4b").default,goodsShop:i("d102").default,uParse:i("e0c3").default,uBadge:i("321b").default,goodsColumn:i("7579").default,specPopup:i("e696").default,sharePopup:i("dabc").default,floatTab:i("65c2").default,loadingView:i("2875").default},a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"goods-details"},[i("u-navbar",{attrs:{id:"navbar","border-bottom":!1,background:{background:"rgba(256,256,256, "+t.navStyle.backgroundBg+")"},"back-bg":"rgba(0,0,0,"+t.navStyle.backBg+")","back-icon-color":t.navStyle.backColor,immersive:!0}},[t.navStyle.backgroundBg>.1?i("tabs",{style:{width:"100%",opacity:t.navStyle.backgroundBg},attrs:{"sticky-bg-color":"transparent","show-bar":!0,"bar-width":60,"is-scroll":!1,current:t.active,"bg-color":"transparent"},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.changeActive.apply(void 0,arguments)}}},[i("tab",{attrs:{name:"商品"}}),i("tab",{attrs:{name:"评价"}}),i("tab",{attrs:{name:"详情"}})],1):t._e()],1),t.isNull?i("v-uni-view",[i("v-uni-view",{staticClass:"details-null flex-col col-center"},[i("v-uni-image",{staticClass:"img-null",attrs:{src:"/static/images/goods_null.png"}}),i("v-uni-view",{staticClass:"xs muted"},[t._v("该商品已下架或不存在，去逛逛别的吧~")])],1),i("goods-column")],1):i("v-uni-view",{staticClass:"contain",attrs:{id:"goods"},on:{touchstart:function(e){arguments[0]=e=t.$handleEvent(e),t.isTouchStart=!0}}},[i("bubble-tips",{attrs:{top:"200rpx"}}),i("product-swiper",{attrs:{imgUrls:t.swiperList,video:t.goodsDetail.video}}),1==t.goodsType?i("v-uni-view",{staticClass:"seckill bg-seckill"},[i("v-uni-view",{staticClass:"bg-seckill__price"},[i("v-uni-view",[i("v-uni-view",{staticStyle:{color:"white"}},[t._v("限时秒杀")]),i("v-uni-view",{staticClass:"flex col-baseline"},[i("v-uni-view",[i("price-format",{attrs:{"first-size":46,"second-size":32,"subscript-size":32,price:t.checkedGoods.price||t.goodsDetail.price,weight:500}})],1),t.checkedGoods.market_price||t.goodsDetail.market_price?i("v-uni-view",{staticClass:"m-l-10"},[i("price-format",{attrs:{"subscript-size":30,"line-through":!0,"first-size":30,"second-size":30,price:t.checkedGoods.market_price||t.goodsDetail.market_price}})],1):t._e()],1)],1)],1),i("v-uni-view",{staticClass:"bg-seckill__time"},[i("v-uni-text",{staticClass:"xs",staticStyle:{color:"white"}},[t._v("距离活动结束")]),i("u-count-down",{attrs:{timestamp:t.countTime,color:"black","bg-color":"#fff","separator-color":"#fff","font-size":"24",height:"36","separator-size":"26",separator:"zh"},on:{end:function(e){arguments[0]=e=t.$handleEvent(e),t.getGoodsDetailFun.apply(void 0,arguments)}}})],1)],1):t._e(),2==t.goodsType?i("v-uni-view",{staticClass:"group bg-team"},[i("v-uni-view",{staticClass:"bg-team__price"},[i("v-uni-view",[i("v-uni-view",{staticStyle:{color:"white"}},[t._v("拼团优惠")]),i("v-uni-view",[i("v-uni-view",{staticClass:"flex col-baseline"},[i("v-uni-text",{staticClass:"tip"},[t._v(t._s(t.team.people_num||0)+"人团")]),i("price-format",{attrs:{"subscript-size":32,"first-size":46,"second-size":32,price:t.team.team_min_price||0,weight:500}}),i("v-uni-text",{staticClass:"xs"},[t._v("起")]),t.checkedGoods.market_price||t.goodsDetail.market_price?i("v-uni-view",{staticClass:"m-l-10"},[i("price-format",{attrs:{"subscript-size":30,"line-through":!0,"first-size":30,"second-size":30,price:t.checkedGoods.market_price||t.goodsDetail.market_price}})],1):t._e()],1)],1)],1)],1),i("v-uni-view",{staticClass:"bg-team__time"},[i("v-uni-text",{staticClass:"xs",staticStyle:{color:"white"}},[t._v("距离活动结束")]),i("u-count-down",{attrs:{timestamp:t.countTime,color:"black","bg-color":"#fff","separator-color":"#fff","font-size":"24",height:"36","separator-size":"26",separator:"zh"},on:{end:function(e){arguments[0]=e=t.$handleEvent(e),t.getGoodsDetailFun.apply(void 0,arguments)}}})],1)],1):t._e(),!t.goodsType&&t.goodsDetail.member_price?i("v-uni-view",{staticClass:"goods-info bg-white bg-vip"},[i("v-uni-view",{staticClass:"vip-name"},[i("v-uni-view",{staticClass:"line1"},[t._v(t._s(t.userInfo.level_name)+"价")])],1),i("v-uni-view",{staticClass:"flex col-baseline",staticStyle:{padding:"0 11rpx"}},[i("price-format",{attrs:{price:t.checkedGoods.member_price,"first-size":48,"second-size":48,"subscript-size":42,weight:500,color:"#E0A356"}}),i("v-uni-text",{staticStyle:{color:"#e0a356"}},[t._v("起")]),t.checkedGoods.market_price||t.goodsDetail.market_price?i("v-uni-view",{staticClass:"m-l-10"},[i("price-format",{attrs:{color:"gray","subscript-size":30,"line-through":!0,"first-size":30,"second-size":30,price:t.checkedGoods.market_price||t.goodsDetail.market_price}})],1):t._e()],1)],1):t._e(),i("v-uni-view",{staticClass:"goods-info bg-white round-box m-x-20",style:{"margin-top":0!=t.goodsType||t.goodsDetail.member_price?"-90rpx":"30rpx"}},[0!=t.goodsType||t.goodsDetail.member_price?t._e():i("v-uni-view",{staticClass:"info-header flex"},[i("v-uni-view",{staticClass:"price flex flex-1"},[i("v-uni-view",{staticClass:"primary m-r-10"},[i("price-format",{attrs:{"first-size":46,"second-size":32,"subscript-size":32,price:t.goodsDetail.min_price,weight:500}})],1),i("v-uni-view",{staticClass:"line-through muted md"},[0!=t.goodsDetail.market_price?i("price-format",{attrs:{price:t.goodsDetail.market_price}}):t._e()],1)],1)],1),i("get-coupon",{attrs:{"wrap-style":{"margin-top":"20rpx"},"goods-id":t.goodsDetail.id}}),i("v-uni-view",{staticClass:"flex m-t-20"},[i("v-uni-view",{staticClass:"name lg bold"},[t._v(t._s(t.goodsDetail.name))]),i("v-uni-view",{staticClass:"row-between",staticStyle:{"margin-left":"auto",width:"20%"}},[i("v-uni-view",{staticClass:"column",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.showShareBtn=!0}}},[i("u-icon",{attrs:{name:"share",size:"36"}}),i("v-uni-text",{staticClass:"m-t-5 muted xxs"},[t._v("分享")])],1),i("v-uni-view",{staticClass:"column",staticStyle:{"margin-left":"10rpx"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.collectGoodsFun.apply(void 0,arguments)}}},[1==t.goodsDetail.is_collect?[i("u-icon",{attrs:{name:"star-fill",color:"#ff2c3c",size:"36"}})]:[i("u-icon",{attrs:{name:"star",size:"36"}})],i("v-uni-text",{staticClass:"m-t-5 muted xxs"},[t._v("收藏")])],2)],1)],1),i("v-uni-view",{staticClass:"flex row-between xs lighter",staticStyle:{padding:"20rpx 0"}},[t.goodsDetail.is_show_stock?i("v-uni-text",[t._v("库存: "+t._s(t.checkedGoods.stock||t.goodsDetail.stock))]):t._e(),i("v-uni-text",[t._v("销量: "+t._s(t.goodsDetail.sales_sum))]),i("v-uni-text",[t._v("浏览量: "+t._s(t.goodsDetail.clicks))])],1)],1),2==t.goodsType?i("v-uni-view",{staticClass:"group-play bg-white mt20 round-box m-x-20"},[i("v-uni-view",{staticClass:"title"},[t._v("拼团玩法")]),i("v-uni-view",{staticClass:"steps row"},[i("v-uni-view",{staticClass:"row step"},[i("v-uni-view",{staticClass:"number xxs"},[t._v("1")]),i("v-uni-view",{staticClass:"sm"},[t._v("开团/参团")])],1),i("v-uni-view",{staticClass:"line"}),i("v-uni-view",{staticClass:"row step"},[i("v-uni-view",{staticClass:"number xxs"},[t._v("2")]),i("v-uni-view",{staticClass:"sm"},[t._v("团满即成新团")])],1),i("v-uni-view",{staticClass:"line"}),i("v-uni-view",{staticClass:"row step"},[i("v-uni-view",{staticClass:"number xxs"},[t._v("3")]),i("v-uni-view",{staticClass:"sm"},[t._v("满员发货")])],1)],1)],1):t._e(),t.teamFound.length?i("v-uni-view",{staticClass:"group-play bg-white mt20 round-box m-x-20"},[i("v-uni-view",{staticClass:"title"},[t._v("正在拼团中")]),t.teamFound.length?i("v-uni-swiper",{staticClass:"mt20 bg-white",staticStyle:{height:"240rpx"},attrs:{autoplay:"true",vertical:"true",circular:"true",interval:5e3}},t._l(t.teamFound,(function(e,n){return i("v-uni-swiper-item",{key:n},[i("v-uni-view",{staticClass:"group-list"},t._l(e,(function(e,n){return i("v-uni-view",{key:n,staticClass:"group-item bg-white row-between"},[i("v-uni-view",{staticClass:"row",staticStyle:{"max-width":"280rpx"}},[i("u-image",{attrs:{src:e.avatar,width:"80rpx",height:"80rpx","border-radius":"50%"}}),i("v-uni-view",{staticClass:"ml20 line1 normal"},[t._v(t._s(e.nickname))])],1),i("v-uni-view",{staticClass:"row ml20",staticStyle:{flex:"none"}},[i("v-uni-view",{staticClass:"column-center"},[i("v-uni-text",{staticClass:"sm normal"},[t._v("还差"),i("v-uni-text",{staticClass:"primary"},[t._v(t._s(e.people-e.join))]),t._v("人成团")],1),i("v-uni-view",{staticClass:"muted xs"},[t._v("剩余"),i("u-count-down",{attrs:{timestamp:t.getTeamCountTime(e.invalid_time),"separator-color":"#999",color:"#999","separator-size":24,"font-size":24,"bg-color":"transparent"},on:{end:function(e){arguments[0]=e=t.$handleEvent(e),t.getGoodsDetailFun.apply(void 0,arguments)}}})],1)],1),i("v-uni-view",{staticClass:"group-btn br60 white row-center",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.showSpecFun(3,e.id)}}},[t._v("去参团")])],1)],1)})),1)],1)})),1):t._e()],1):t._e(),t.goodsType?t._e():i("v-uni-view",{staticClass:"spec flex bg-white m-t-20 round-box m-x-20",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.showSpecFun(0)}}},[i("v-uni-view",{staticClass:"text muted"},[t._v("已选")]),i("v-uni-view",{staticClass:"line-1 m-r-20 flex-1"},[t._v(t._s(t.checkedGoods.spec_value_str||"默认"))]),i("u-icon",{attrs:{name:"arrow-right"}})],1),t.comment.one&&t.comment.one.nickname?i("v-uni-view",{staticClass:"evaluation bg-white m-t-20 round-box m-x-20",attrs:{id:"evaluation"}},[i("router-link",{attrs:{to:{path:"/bundle/pages/all_comments/all_comments",query:{id:t.goodsDetail.id}}}},[i("v-uni-view",{staticClass:"title flex row-between"},[i("v-uni-view",[i("v-uni-text",{staticClass:"balck md m-r-10"},[t._v("用户评价")])],1),i("v-uni-view",{staticClass:"flex"},[i("v-uni-text",{staticClass:"lighter xs"},[t._v("查看全部")]),i("u-icon",{attrs:{name:"arrow-right"}})],1)],1)],1),i("v-uni-view",{staticClass:"con"},[i("v-uni-view",{staticClass:"user-info flex"},[i("v-uni-image",{staticClass:"avatar m-r-20",attrs:{src:t.comment.one.avatar}}),i("v-uni-view",{staticClass:"user-name md m-r-10"},[t._v(t._s(t.comment.one.nickname))])],1),t.comment.one.comment?i("v-uni-view",{staticClass:"dec m-t-20"},[t._v(t._s(t.comment.one.comment))]):t._e()],1)],1):t._e(),1==t.appConfig.shop_hide_goods?i("v-uni-view",{staticClass:"goods-shop m-t-20 bg-white round-box m-x-20"},[i("goods-shop",{attrs:{shop:t.shop}})],1):t._e(),i("v-uni-view",{staticClass:"details m-t-20 bg-white round-box m-x-20",attrs:{id:"details"}},[i("v-uni-view",{staticClass:"title lg"},[t._v("商品详情")]),i("v-uni-view",{staticClass:"content"},[i("u-parse",{attrs:{html:t.goodsDetail.content,"lazy-load":!0,"show-with-animation":!0}})],1)],1),i("v-uni-view",{staticClass:"footer flex bg-white fixed"},[i("router-link",{attrs:{to:{path:"/bundle/pages/chat/chat",query:{shop_id:t.shop.id,goods_id:t.id}}}},[i("v-uni-view",{staticClass:"btn flex-col col-center row-center"},[i("v-uni-image",{staticClass:"icon-md",attrs:{src:"/static/images/icon_contact.png"}}),i("v-uni-text",{staticClass:"xxs lighter"},[t._v("客服")])],1)],1),i("router-link",{attrs:{"nav-type":"pushTab",to:"/pages/shop_cart/shop_cart"}},[i("v-uni-view",{staticClass:"flex-col btn cart col-center row-center"},[i("v-uni-image",{staticClass:"icon-md",attrs:{src:"/static/images/icon_cart.png"}}),i("v-uni-text",{staticClass:"xxs lighter"},[t._v("购物车")]),t.cartNum?i("u-badge",{attrs:{bgColor:t.colorConfig.primary,offset:[8,10],count:t.cartNum}}):t._e()],1)],1),t.shop.is_pay?[t.btnText.yellow&&0==t.goodsDetail.type?i("v-uni-view",{staticClass:"add-cart br60 white m-r-10 md m-l-20",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.showSpecFun(1)}}},[t._v(t._s(t.btnText.yellow))]):t._e(),i("v-uni-view",{staticClass:"right-buy br60 white m-r-20 m-l-10 md",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.showSpecFun(2)}}},[t._v(t._s(t.btnText.red))])]:i("v-uni-view",{staticClass:"consult-btn br60 white m-r-20 md m-l-20",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.handleConsult.apply(void 0,arguments)}}},[t._v("咨询商家")])],2)],1),i("spec-popup",{attrs:{show:t.showSpec,"is-seckill":1==t.goodsType,goods:t.goodsDetail,"show-add":1==t.popupType||0==t.popupType,"show-buy":2==t.popupType||0==t.popupType,"show-stock":1==t.goodsDetail.is_show_stock,"show-confirm":3==t.popupType,type:t.goodsDetail.type,group:!!t.isGroup,"red-btn-text":t.btnText.red,"yellow-btn-text":t.btnText.yellow},on:{close:function(e){arguments[0]=e=t.$handleEvent(e),t.showSpec=!1},buynow:function(e){arguments[0]=e=t.$handleEvent(e),t.onBuy.apply(void 0,arguments)},addcart:function(e){arguments[0]=e=t.$handleEvent(e),t.onAddCart.apply(void 0,arguments)},change:function(e){arguments[0]=e=t.$handleEvent(e),t.onChangeGoods.apply(void 0,arguments)},confirm:function(e){arguments[0]=e=t.$handleEvent(e),t.onConfirm.apply(void 0,arguments)}}}),i("share-popup",{attrs:{"share-id":t.id,pagePath:"pages/goods_details/goods_details",type:"1",config:{avatar:t.userInfo.avatar,nickname:t.userInfo.nickname,image:t.goodsDetail.poster||t.goodsDetail.image,price:t.goodsDetail.min_price,marketPrice:t.goodsDetail.market_price,name:t.team.share_title||t.goodsDetail.name}},model:{value:t.showShareBtn,callback:function(e){t.showShareBtn=e},expression:"showShareBtn"}}),i("share-popup",{attrs:{show:t.showShareBtn,"goods-id":t.id,"img-url":t.goodsDetail.image,summary:t.goodsDetail.remark,"share-title":t.goodsDetail.name},on:{close:function(e){arguments[0]=e=t.$handleEvent(e),t.showShareBtn=!1}}}),i("float-tab"),i("v-uni-view",{staticClass:"share-money",class:{show:t.showCommission&&t.enableCommission}},[i("v-uni-view",{staticClass:"row-end"},[i("v-uni-view",{staticClass:"share-close row-center",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.showCommission=!1}}},[i("u-icon",{attrs:{name:"close",size:"16",color:"#fff"}})],1)],1),i("v-uni-view",{staticClass:"share-con mt10",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.showShareBtn=!0}}},[i("v-uni-view",{staticClass:"primary",staticStyle:{"font-size":"45rpx"}},[t._v(t._s(t.distribution.earnings)),i("v-uni-text",{staticClass:"xs"},[t._v("元")])],1),i("v-uni-view",{staticClass:"lighter xxs"},[t._v("好友下单最高可赚")])],1)],1),t.isFirstLoading?i("loading-view"):t._e()],1)},r=[]},cf54:function(t,e,i){"use strict";i.r(e);var n=i("7ab8"),a=i("1045");for(var r in a)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(r);i("c29e");var o=i("f0c5"),s=Object(o["a"])(a["default"],n["b"],n["c"],!1,null,"1d01409a",null,!1,n["a"],void 0);e["default"]=s.exports},d082:function(t,e,i){"use strict";var n=i("7bd9"),a=i.n(n);a.a},d093:function(t,e,i){var n=i("40fd");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=i("4f06").default;a("405740a1",n,!0,{sourceMap:!1,shadowMode:!1})},d0a7:function(t,e,i){"use strict";i.r(e);var n=i("92ad"),a=i("7853");for(var r in a)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(r);i("0da2");var o=i("f0c5"),s=Object(o["a"])(a["default"],n["b"],n["c"],!1,null,"7664bcb0",null,!1,n["a"],void 0);e["default"]=s.exports},d102:function(t,e,i){"use strict";i.r(e);var n=i("63db"),a=i("6bd5");for(var r in a)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(r);i("2cf8");var o=i("f0c5"),s=Object(o["a"])(a["default"],n["b"],n["c"],!1,null,"05f2119a",null,!1,n["a"],void 0);e["default"]=s.exports},d720:function(t,e,i){var n=i("7b9c");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=i("4f06").default;a("5a6ac1e0",n,!0,{sourceMap:!1,shadowMode:!1})},d7b0:function(t,e,i){var n=i("4ffd3");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=i("4f06").default;a("0473c676",n,!0,{sourceMap:!1,shadowMode:!1})},d7e8:function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return r})),i.d(e,"a",(function(){return n}));var n={uImage:i("ba4b").default},a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"bubble-tips-container",style:{top:t.top,left:t.left}},t._l(t.currentList,(function(e){return i("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:t.showBubble,expression:"showBubble"}],key:e.id,staticClass:"bubble-content row"},[i("u-image",{staticClass:"bubble-img",attrs:{width:"50rpx",height:"50rpx",src:e.user.avatar,"border-radius":"50%"}}),i("v-uni-view",{staticClass:"xs"},[t._v(t._s(e.template))])],1)})),1)},r=[]},d801:function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("a9e3"),i("ac1f"),i("00b4"),i("c975"),i("d401"),i("d3b7"),i("25f0");var n={name:"u-number-box",props:{value:{type:Number,default:1},bgColor:{type:String,default:"#F2F3F5"},min:{type:Number,default:0},max:{type:Number,default:99999},step:{type:Number,default:1},disabled:{type:Boolean,default:!1},size:{type:[Number,String],default:26},color:{type:String,default:"#323233"},inputWidth:{type:[Number,String],default:80},inputHeight:{type:[Number,String],default:50},index:{type:[Number,String],default:""},disabledInput:{type:Boolean,default:!1},cursorSpacing:{type:[Number,String],default:100},longPress:{type:Boolean,default:!0},pressTime:{type:[Number,String],default:250},positiveInteger:{type:Boolean,default:!0}},watch:{value:function(t,e){if(console.log(t,e),!this.changeFromInner){if(this.inputVal==t)return;this.inputVal=t,this.$nextTick((function(){this.changeFromInner=!1}))}},inputVal:function(t,e){var i=this;if(""!=t){var n=0,a=this.$u.test.number(t);n=a&&t>=this.min&&t<=this.max?t:e,this.positiveInteger&&(t<0||-1!==String(t).indexOf("."))&&(n=e,this.$nextTick((function(){i.inputVal=e}))),this.isFistVal||this.handleChange(n,"change")}}},data:function(){return{inputVal:1,timer:null,changeFromInner:!1,innerChangeTimer:null,isFistVal:!0}},created:function(){this.inputVal=Number(this.value),this.isFistVal=!1},computed:{getCursorSpacing:function(){return Number(uni.upx2px(this.cursorSpacing))}},methods:{btnTouchStart:function(t){var e=this;this[t](),this.longPress&&(clearInterval(this.timer),this.timer=null,this.timer=setInterval((function(){e[t]()}),this.pressTime))},clearTimer:function(){var t=this;this.$nextTick((function(){clearInterval(t.timer),t.timer=null}))},minus:function(){this.computeVal("minus")},plus:function(){this.computeVal("plus")},calcPlus:function(t,e){var i,n,a;try{n=t.toString().split(".")[1].length}catch(o){n=0}try{a=e.toString().split(".")[1].length}catch(o){a=0}i=Math.pow(10,Math.max(n,a));var r=n>=a?n:a;return((t*i+e*i)/i).toFixed(r)},calcMinus:function(t,e){var i,n,a;try{n=t.toString().split(".")[1].length}catch(o){n=0}try{a=e.toString().split(".")[1].length}catch(o){a=0}i=Math.pow(10,Math.max(n,a));var r=n>=a?n:a;return((t*i-e*i)/i).toFixed(r)},computeVal:function(t){if(uni.hideKeyboard(),!this.disabled){var e=0;"minus"===t?e=this.calcMinus(this.inputVal,this.step):"plus"===t&&(e=this.calcPlus(this.inputVal,this.step)),e<this.min||e>this.max||(this.inputVal=e,this.handleChange(e,t))}},onBlur:function(t){var e=this,i=0,n=t.detail.value;/(^\d+$)/.test(n)&&0!=n[0]||(i=this.min),i=+n,i>this.max?i=this.max:i<this.min&&(i=this.min),this.$nextTick((function(){e.inputVal=i})),this.handleChange(i,"blur")},onFocus:function(){this.$emit("focus")},handleChange:function(t,e){var i=this;this.disabled||(this.innerChangeTimer&&(clearTimeout(this.innerChangeTimer),this.innerChangeTimer=null),this.changeFromInner=!0,this.innerChangeTimer=setTimeout((function(){i.changeFromInner=!1}),150),this.$emit("input",Number(t)),this.$emit(e,{value:Number(t),index:this.index}))}}};e.default=n},d86b:function(t,e,i){"use strict";var n=i("d039");t.exports=n((function(){if("function"==typeof ArrayBuffer){var t=new ArrayBuffer(8);Object.isExtensible(t)&&Object.defineProperty(t,"a",{value:8})}}))},d87b3:function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return r})),i.d(e,"a",(function(){return n}));var n={jVideo:i("9052").default,uImage:i("ba4b").default},a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"swiper-wrap"},[i("v-uni-swiper",{ref:"swiper",staticClass:"swiper",attrs:{autoplay:t.autoplay,circular:t.circular,interval:t.interval,duration:t.duration},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.swiperChange.apply(void 0,arguments)}}},[t.video?i("v-uni-swiper-item",[i("v-uni-view",{staticClass:"video-wrap"},[i("j-video",{attrs:{url:t.video,height:"750rpx",width:"750rpx",poster:t.poster}})],1)],1):t._e(),t._l(t.urls,(function(e,n){return[i("v-uni-swiper-item",{key:n+"_0",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.previewImage(n)}}},[i("u-image",{attrs:{width:"100%",height:"750rpx",src:e.url,borderRadius:t.borderRadius}})],1)]}))],2),i("v-uni-view",{staticClass:"dots black sm bg-white br60"},[t._v(t._s(t.currentSwiper+1)+"/"+t._s(t.swiperLength))])],1)},r=[]},d9bd:function(t,e,i){"use strict";i("7a82");var n=i("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=n(i("f07e")),r=n(i("c964"));i("a9e3"),i("99af"),i("e9c4"),i("ac1f"),i("d3b7"),i("d9e2"),i("d401");var o=i("7f84"),s=i("2399"),c=i("4f21"),d={name:"lime-painter",mixins:[(0,s.parent)("painter")],props:{board:Object,pathType:{type:String},fileType:{type:String,default:"png"},quality:{type:Number,default:1},css:[String,Object],width:[Number,String],height:[Number,String],pixelRatio:Number,customStyle:String,isCanvasToTempFilePath:Boolean,sleep:{type:Number,default:1e3/30},beforeDelay:{type:Number,default:100},afterDelay:{type:Number,default:100}},data:function(){return{use2dCanvas:!1,canvasHeight:150,canvasWidth:null,isDrawIng:!1,isPC:!1,inited:!1,name:"view",progress:0}},computed:{canvasId:function(){return"l-painter".concat(this._uid)},size:function(){if(this.boardWidth&&this.boardHeight)return"width:".concat(this.boardWidth,"px; height: ").concat(this.boardHeight,"px;")},dpr:function(){return this.pixelRatio||uni.getSystemInfoSync().pixelRatio},boardWidth:function(){var t,e=this.width,i=void 0===e?0:e,n=this.canvasWidth,a=void 0===n?0:n,r=(null===(t=this.board)||void 0===t?void 0:t.css)||this.board||{},s=r.width,c=void 0===s?0:s;return Math.max((0,o.toPx)(i||c),(0,o.toPx)(a))},boardHeight:function(){var t,e=this.height,i=void 0===e?0:e,n=this.canvasHeight,a=void 0===n?0:n,r=(null===(t=this.board)||void 0===t?void 0:t.css)||this.board||{},s=r.height,c=void 0===s?0:s;return Math.max((0,o.toPx)(i||c),(0,o.toPx)(a))}},watch:{canvasWidth:function(t){var e;!this.el.css||null!==(e=this.el.css)&&void 0!==e&&e.width||(this.el.css.width=t)},size:function(t){}},mounted:function(){var t=this;this.$nextTick((function(){setTimeout((function(){t.board?t.$watch("board",t.watchRender,{deep:!0,immediate:!0}):t.el.views.length&&t.$watch("el",t.watchRender,{deep:!0,immediate:!0})}),30)}))},methods:{watchRender:function(t){var e=this;return(0,r.default)((0,a.default)().mark((function i(){return(0,a.default)().wrap((function(i){while(1)switch(i.prev=i.next){case 0:if(e.progress=0,"{}"!==JSON.stringify(t)&&t){i.next=3;break}return i.abrupt("return");case 3:clearTimeout(e.rendertimer),e.rendertimer=setTimeout((function(){e.render(t)}),e.beforeDelay);case 5:case"end":return i.stop()}}),i)})))()},setFilePath:function(t,e){var i=this;return(0,r.default)((0,a.default)().mark((function n(){var r,s;return(0,a.default)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(r=t,s=i.pathType,"base64"!=s||(0,o.isBase64)(t)){n.next=8;break}return n.next=5,(0,o.pathToBase64)(t);case 5:r=n.sent,n.next=12;break;case 8:if("url"!=s||!(0,o.isBase64)(t)){n.next=12;break}return n.next=11,(0,o.base64ToPath)(t);case 11:r=n.sent;case 12:return e&&i.$emit("success",r),n.abrupt("return",r);case 14:case"end":return n.stop()}}),n)})))()},getParentWeith:function(){var t=this;uni.createSelectorQuery().in(this).select(".lime-painter").boundingClientRect().exec((function(e){t.canvasWidth=Math.ceil(e[0].width),t.canvasHeight=e[0].height}))},update:function(t,e){var i=this;return(0,r.default)((0,a.default)().mark((function n(){return(0,a.default)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return i.painter=null,i.isDrawIng=!1,n.next=4,new Promise((function(t){return i.$nextTick(t)}));case 4:return n.next=6,(0,o.sleep)(200);case 6:return n.abrupt("return",i.render(t,e));case 7:case"end":return n.stop()}}),n)})))()},render:function(){var t=arguments,e=this;return(0,r.default)((0,a.default)().mark((function i(){var n,s,d,u,l,f,h,p,v,g,m,b,w;return(0,a.default)().wrap((function(i){while(1)switch(i.prev=i.next){case 0:if(d=t.length>0&&void 0!==t[0]?t[0]:{},u=t.length>1&&void 0!==t[1]&&t[1],!e.isDrawIng){i.next=4;break}return i.abrupt("return",e.update(d,u));case 4:return e.isDrawIng=!0,"{}"!=JSON.stringify(d),i.next=8,e.getContext();case 8:if(l=i.sent,f=e.use2dCanvas,h=e.boardWidth,p=e.boardHeight,v=e.canvas,g=e.afterDelay,!f||v){i.next=12;break}return i.abrupt("return",Promise.reject(new Error("render: fail canvas has not been created")));case 12:return e.boundary={top:0,left:0,width:h,height:p},e.painter||(e.painter=new c.Painter({context:l,canvas:v,width:h,height:p,pixelRatio:e.dpr},e)),e.painter.listen("progressChange",(function(t){e.$emit("progress",t)})),i.next=17,e.painter.source(d);case 17:return m=i.sent,b=m.width,w=m.height,e.canvasHeight=(0,o.toPx)(null===(n=e.el.css)||void 0===n?void 0:n.height)||w,e.canvasWidth=(0,o.toPx)(null===(s=e.el.css)||void 0===s?void 0:s.width)||b,e.boundary.height=e.canvasHeight,e.boundary.width=e.canvasWidth,i.next=26,(0,o.sleep)(e.sleep);case 26:return i.next=28,e.painter.render();case 28:return i.next=30,new Promise((function(t){return e.$nextTick(t)}));case 30:if(f||u){i.next=33;break}return i.next=33,e.canvasDraw();case 33:if(!g||!f){i.next=36;break}return i.next=36,(0,o.sleep)(g);case 36:return e.$emit("done"),e.isCanvasToTempFilePath&&!u&&e.isDrawIng&&e.canvasToTempFilePath().then(function(){var t=(0,r.default)((0,a.default)().mark((function t(i){return(0,a.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:e.$emit("success",i.tempFilePath);case 1:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()).catch((function(t){e.$emit("fail",new Error(JSON.stringify(t)))})),e.isDrawIng=!1,i.abrupt("return",Promise.resolve({ctx:l,draw:e.painter,node:e.node}));case 40:case"end":return i.stop()}}),i)})))()},custom:function(t){var e=this;return(0,r.default)((0,a.default)().mark((function i(){var n,r,o;return(0,a.default)().wrap((function(i){while(1)switch(i.prev=i.next){case 0:return i.next=2,e.render({},!0);case 2:return n=i.sent,r=n.ctx,o=n.draw,r.save(),i.next=8,t(r,o);case 8:return r.restore(),i.abrupt("return",Promise.resolve(!0));case 10:case"end":return i.stop()}}),i)})))()},single:function(){var t=arguments,e=this;return(0,r.default)((0,a.default)().mark((function i(){var n,r;return(0,a.default)().wrap((function(i){while(1)switch(i.prev=i.next){case 0:return n=t.length>0&&void 0!==t[0]?t[0]:{},i.next=3,e.render(n,!0);case 3:return r=i.sent,i.abrupt("return",Promise.resolve(r));case 5:case"end":return i.stop()}}),i)})))()},canvasDraw:function(){var t=this,e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return new Promise((function(i,n){return t.ctx.draw(e,(function(){return setTimeout((function(){return i()}),t.afterDelay)}))}))},getContext:function(){var t=this;return(0,r.default)((0,a.default)().mark((function e(){var i,n,r;return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!t.ctx||!t.inited){e.next=2;break}return e.abrupt("return",Promise.resolve(t.ctx));case 2:return t.getParentWeith(),i=t.type,t.use2dCanvas,n=t.dpr,t.boardWidth,t.boardHeight,r=function(){return new Promise((function(e){uni.createSelectorQuery().in(t).select("#".concat(t.canvasId)).boundingClientRect().exec((function(i){if(i){var a=uni.createCanvasContext(t.canvasId,t);t.inited||(t.inited=!0,t.use2dCanvas=!1,t.canvas=i),t.isPC&&a.scale(1/n,1/n),t.ctx=a,e(t.ctx)}}))}))},e.abrupt("return",r());case 8:return e.abrupt("return",new Promise((function(e){uni.createSelectorQuery().in(t).select("#".concat(t.canvasId)).node().exec((function(n){var a=n[0].node;a||(t.use2dCanvas=!1,e(t.getContext()));var r=a.getContext(i);t.inited||(t.inited=!0,t.use2dCanvas=!0,t.canvas=a),t.ctx=r,e(t.ctx)}))})));case 9:case"end":return e.stop()}}),e)})))()},canvasToTempFilePath:function(){var t=this,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},i=this.use2dCanvas,n=this.canvasId,o=this.dpr,s=this.fileType,c=this.quality;return new Promise((function(d,u){var l=t.boundary||t,f=l.top,h=void 0===f?0:f,p=l.left,v=void 0===p?0:p,g=l.width,m=l.height,b=g*o,w=m*o,x=function(){var e=(0,r.default)((0,a.default)().mark((function e(i){var n;return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.setFilePath(i.tempFilePath);case 2:n=e.sent,d(Object.assign(i,{tempFilePath:n}));case 4:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),y=Object.assign({x:v,y:h,width:g,height:m,destWidth:b,destHeight:w,canvasId:n,fileType:s,quality:c,success:x,fail:u},e);i&&(delete y.canvasId,y.canvas=t.canvas),uni.canvasToTempFilePath(y,t)}))}}};e.default=d},dabc:function(t,e,i){"use strict";i.r(e);var n=i("8278"),a=i("087d");for(var r in a)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(r);i("36c5");var o=i("f0c5"),s=Object(o["a"])(a["default"],n["b"],n["c"],!1,null,"41a26444",null,!1,n["a"],void 0);e["default"]=s.exports},db1f:function(t,e,i){var n=i("0d14");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=i("4f06").default;a("5049f481",n,!0,{sourceMap:!1,shadowMode:!1})},dc60:function(t,e){var i={errorImg:null,filter:null,highlight:null,onText:null,entities:{quot:'"',apos:"'",semi:";",nbsp:" ",ensp:" ",emsp:" ",ndash:"–",mdash:"—",middot:"·",lsquo:"‘",rsquo:"’",ldquo:"“",rdquo:"”",bull:"•",hellip:"…"},blankChar:n(" , ,\t,\r,\n,\f"),boolAttrs:n("allowfullscreen,autoplay,autostart,controls,ignore,loop,muted"),blockTags:n("address,article,aside,body,caption,center,cite,footer,header,html,nav,pre,section"),ignoreTags:n("area,base,canvas,frame,iframe,input,link,map,meta,param,script,source,style,svg,textarea,title,track,wbr"),richOnlyTags:n("a,colgroup,fieldset,legend"),selfClosingTags:n("area,base,br,col,circle,ellipse,embed,frame,hr,img,input,line,link,meta,param,path,polygon,rect,source,track,use,wbr"),trustTags:n("a,abbr,ad,audio,b,blockquote,br,code,col,colgroup,dd,del,dl,dt,div,em,fieldset,h1,h2,h3,h4,h5,h6,hr,i,img,ins,label,legend,li,ol,p,q,source,span,strong,sub,sup,table,tbody,td,tfoot,th,thead,tr,title,ul,video"),userAgentStyles:{address:"font-style:italic",big:"display:inline;font-size:1.2em",blockquote:"background-color:#f6f6f6;border-left:3px solid #dbdbdb;color:#6c6c6c;padding:5px 0 5px 10px",caption:"display:table-caption;text-align:center",center:"text-align:center",cite:"font-style:italic",dd:"margin-left:40px",mark:"background-color:yellow",pre:"font-family:monospace;white-space:pre;overflow:scroll",s:"text-decoration:line-through",small:"display:inline;font-size:0.8em",u:"text-decoration:underline"}};function n(t){for(var e=Object.create(null),i=t.split(","),n=i.length;n--;)e[i[n]]=!0;return e}t.exports=i},df3c:function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("a9e3"),i("d81d"),i("14d9");var n=i("c60f"),a={data:function(){return{showTips:[]}},props:{list:{type:Array,default:function(){return[]}},btnType:{type:Number}},watch:{list:{handler:function(t){var e=t.map((function(t){return 0}));this.showTips=e},immediate:!0,deep:!0}},computed:{getBtn:function(){var t=this;return function(e){var i="";return i=e.is_get?e.can_continue_get?"继续领取":"去使用":0==t.btnType?"去使用":"领取",i}}},methods:{onHandle:function(t,e){this.id=t;var i=this.getBtn(e);switch(i){case"去使用":0==this.btnType?uni.redirectTo({url:"/pages/store_index/store_index?id=".concat(e.shop_id)}):uni.redirectTo({url:"/pages/store_index/store_index?id=".concat(e.shop.id)});break;case"领取":this.getCouponFun(),e.is_get=1,e.can_continue_get=0;break;case"继续领取":this.getCouponFun(),e.can_continue_get=0;break}},onShowTips:function(t){var e=this.showTips;this.showTips[t]=e[t]?0:1,this.showTips=Object.assign([],this.showTips)},getCouponFun:function(){var t=this;if(!this.isLogin)return this.$Router.push("/pages/login/login");(0,n.getCoupon)(this.id).then((function(e){1==e.code&&t.$toast({title:e.msg})}))}}};e.default=a},e03b:function(t,e,i){"use strict";i("7a82");var n=i("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("a9e3");var a=n(i("f3f3")),r=i("26cb"),o=i("b08d"),s={data:function(){return{progressPer:0,list:[]}},props:{shop:{type:Object,default:function(){return{}}}},computed:(0,a.default)({},(0,r.mapGetters)(["appConfig"])),watch:{shop:{handler:function(t){var e=this;this.list=t.goods_list||[],this.$nextTick((function(){(0,o.getRect)(".goods-shop",!1,e).then((function(t){e.rectWidth=t.width}))}))},immediate:!0,deep:!0}},methods:{scrollBarChange:function(t){var e=this.progressPer,i=t.detail,n=i.scrollLeft,a=i.scrollWidth;e=n/(a-this.rectWidth)*100,e=Number(e.toFixed(0)),this.progressPer=e}}};e.default=s},e087:function(t,e,i){"use strict";i.r(e);var n=i("e536"),a=i("2fb7");for(var r in a)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(r);var o=i("f0c5"),s=Object(o["a"])(a["default"],n["b"],n["c"],!1,null,"151331ee",null,!1,n["a"],void 0);e["default"]=s.exports},e0c3:function(t,e,i){"use strict";i.r(e);var n=i("f79f"),a=i("4ff2");for(var r in a)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(r);i("e6ae");var o=i("f0c5"),s=Object(o["a"])(a["default"],n["b"],n["c"],!1,null,"6d8b3c66",null,!1,n["a"],void 0);e["default"]=s.exports},e131:function(t,e,i){"use strict";i.r(e);var n=i("85c6"),a=i("3cd5");for(var r in a)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(r);i("699c");var o=i("f0c5"),s=Object(o["a"])(a["default"],n["b"],n["c"],!1,null,"6d93ee5a",null,!1,n["a"],void 0);e["default"]=s.exports},e2db:function(t,e,i){var n=i("b83e");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=i("4f06").default;a("fbf8b9f0",n,!0,{sourceMap:!1,shadowMode:!1})},e3e9:function(t,e,i){"use strict";var n=i("5a65"),a=i.n(n);a.a},e536:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){}));var n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",[i("l-painter",{attrs:{css:"width: 640rpx; padding-bottom: 35rpx; background-color: #ffffff; box-shadow: 0 20rpx 58rpx rgba(0,0,0,.15);border-radius: 10rpx;",isCanvasToTempFilePath:!0,"custom-style":"position: fixed; left: 200%"},on:{success:function(e){arguments[0]=e=t.$handleEvent(e),t.handleSuccess.apply(void 0,arguments)},fail:function(e){arguments[0]=e=t.$handleEvent(e),t.handleFail.apply(void 0,arguments)}}},[i("l-painter-view",{attrs:{css:"padding-left: 40rpx;"}},[i("l-painter-image",{attrs:{src:t.config.avatar,css:"margin-top: 15rpx; width: 72rpx;  height: 72rpx; border-radius: 50%;"}}),i("l-painter-view",{attrs:{css:"margin-top: 30rpx; padding-left: 20rpx; display: inline-block"}},[i("l-painter-text",{attrs:{text:"来自"+t.config.nickname+"的分享",css:"display: block; padding-bottom: 10rpx; color: #333333; font-size: 28rpx;line-clamp:1;width: 100%;"}})],1),i("l-painter-image",{attrs:{src:t.config.image,css:"object-fit: cover; object-position: center; width: 560rpx; height: 560rpx;margin-top: 15rpx;"}}),i("l-painter-view",{attrs:{css:"margin-top: 30rpx;"}},[i("l-painter-view",{attrs:{css:"display: "+("1"==t.type?"inline-block":"none")+"; width: 400rpx;"}},[i("l-painter-view",{attrs:{css:"vertical-align: bottom; color: #FF2C3C; font-size: 30rpx; line-height: 1em;"}},[i("l-painter-text",{attrs:{text:"￥",css:"vertical-align: bottom;font-size: 28rpx;"}}),i("l-painter-text",{attrs:{text:t.price.prev,css:"vertical-align: bottom; font-size: 38rpx;"}}),i("l-painter-text",{attrs:{text:t.price.next,css:"vertical-align: bottom; font-size: 30rpx;"}}),i("l-painter-text",{attrs:{text:t.marketPrice,css:"vertical-align: bottom; padding-left: 10rpx;font-size: 28rpx; font-weight: normal; text-decoration: line-through; color: #999999"}})],1),i("l-painter-view",{attrs:{css:"margin-top:30rpx;"}},[i("l-painter-text",{attrs:{css:"line-clamp: 2; color: #333333; line-height: 1.5em;font-size: 30rpx; width: 378rpx; padding-right:22rpx; box-sizing: border-box",text:t.config.name}})],1)],1),i("l-painter-view",{attrs:{css:"display: "+("2"==t.type?"inline-block":"none")+"; width: 400rpx;"}},[i("l-painter-view",[i("l-painter-text",{attrs:{css:"line-clamp: 2; color: #FF2C3C; line-height: 1.5em;font-size: 32rpx; width: 375rpx; padding-right:22rpx; box-sizing: border-box",text:"我正在参与砍价 还差一步"}})],1),i("l-painter-view",{attrs:{css:"margin-top:8rpx;"}},[i("l-painter-text",{attrs:{css:"line-clamp: 2; color: #F95F2F; line-height: 1.5em;font-size: 24rpx; width: 378rpx; padding-right:22rpx; box-sizing: border-box",text:"帮忙砍一刀"}})],1),i("l-painter-view",{attrs:{css:"margin-top:8rpx;"}},[i("l-painter-text",{attrs:{css:"line-clamp: 2; color: #333333; line-height: 1.5em;font-size: 28rpx; width: 378rpx; padding-right:22rpx; box-sizing: border-box",text:t.config.name}})],1)],1),i("l-painter-view",{attrs:{css:"display: inline-block;"}},[i("l-painter-qrcode",{attrs:{css:"width: 168rpx; height: 168rpx;",text:t.link}}),i("l-painter-text",{attrs:{text:"长按识别二维码",css:"display: block; padding-top: 10rpx; color: #999999;font-size: 24rpx;"}})],1)],1)],1)],1)],1)},a=[]},e696:function(t,e,i){"use strict";i.r(e);var n=i("f840"),a=i("37dc1");for(var r in a)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(r);i("06b6");var o=i("f0c5"),s=Object(o["a"])(a["default"],n["b"],n["c"],!1,null,"23bc8afc",null,!1,n["a"],void 0);e["default"]=s.exports},e6ae:function(t,e,i){"use strict";var n=i("0743"),a=i.n(n);a.a},e8a9:function(t,e,i){var n=i("24fb");e=n(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.u-waterfall[data-v-7664bcb0]{display:flex;flex-direction:row;flex-direction:row;align-items:flex-start}.u-column[data-v-7664bcb0]{display:flex;flex-direction:row;flex:1;flex-direction:column;height:auto}.u-image[data-v-7664bcb0]{width:100%}',""]),t.exports=e},eb32:function(t,e,i){"use strict";i.r(e);var n=i("8810"),a=i("a1ed");for(var r in a)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(r);var o=i("f0c5"),s=Object(o["a"])(a["default"],n["b"],n["c"],!1,null,"1328a9e4",null,!1,n["a"],void 0);e["default"]=s.exports},ec1d:function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("a9e3");var n={name:"u-count-down",props:{timestamp:{type:[Number,String],default:0},autoplay:{type:Boolean,default:!0},separator:{type:String,default:"colon"},separatorSize:{type:[Number,String],default:30},separatorColor:{type:String,default:"#303133"},color:{type:String,default:"#303133"},fontSize:{type:[Number,String],default:30},bgColor:{type:String,default:"#fff"},height:{type:[Number,String],default:"auto"},showBorder:{type:Boolean,default:!1},borderColor:{type:String,default:"#303133"},showSeconds:{type:Boolean,default:!0},showMinutes:{type:Boolean,default:!0},showHours:{type:Boolean,default:!0},showDays:{type:Boolean,default:!0},hideZeroDay:{type:Boolean,default:!1}},watch:{timestamp:function(t,e){this.clearTimer(),this.start()}},data:function(){return{d:"00",h:"00",i:"00",s:"00",timer:null,seconds:0}},computed:{itemStyle:function(){var t={};return this.height&&(t.height=this.height+"rpx"),this.showBorder&&(t.borderStyle="solid",t.borderColor=this.borderColor,t.borderWidth="1px"),this.bgColor&&(t.backgroundColor=this.bgColor),t},letterStyle:function(){var t={};return this.fontSize&&(t.fontSize=this.fontSize+"rpx"),this.color&&(t.color=this.color),t}},mounted:function(){this.autoplay&&this.timestamp&&this.start()},methods:{start:function(){var t=this;this.clearTimer(),this.timestamp<=0||(this.seconds=Number(this.timestamp),this.formatTime(this.seconds),this.timer=setInterval((function(){if(t.seconds--,t.$emit("change",t.seconds),t.seconds<0)return t.end();t.formatTime(t.seconds)}),1e3))},formatTime:function(t){t<=0&&this.end();var e,i=0,n=0,a=0;i=Math.floor(t/86400),e=Math.floor(t/3600)-24*i;var r=null;r=this.showDays?e:Math.floor(t/3600),n=Math.floor(t/60)-60*e-24*i*60,a=Math.floor(t)-24*i*60*60-60*e*60-60*n,r=r<10?"0"+r:r,n=n<10?"0"+n:n,a=a<10?"0"+a:a,i=i<10?"0"+i:i,this.d=i,this.h=r,this.i=n,this.s=a},end:function(){this.clearTimer(),this.$emit("end",{})},reset:function(){this.clearTimer(),this.seconds=Number(this.timestamp),this.s=this.timestamp,console.log(this.s)},clearTimer:function(){this.timer&&(clearInterval(this.timer),this.timer=null)}},beforeDestroy:function(){clearInterval(this.timer),this.timer=null}};e.default=n},eca1:function(t,e,i){var n=i("24fb");e=n(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */uni-page-body[data-v-01eafde1]{\n  /* 定义一些主题色及基础样式 */font-family:PingFang SC,Arial,Hiragino Sans GB,Microsoft YaHei,sans-serif;font-size:%?28?%;color:#333;padding-bottom:env(safe-area-inset-bottom);background-color:#f6f6f6}body.?%PAGE?%[data-v-01eafde1]{background-color:#f6f6f6}.bold[data-v-01eafde1]{font-weight:700}\n/* 定义字体颜色 */.primary[data-v-01eafde1]{color:#ff2c3c}.bg-primary[data-v-01eafde1]{background-color:#ff2c3c}.bg-white[data-v-01eafde1]{background-color:#fff}.bg-body[data-v-01eafde1]{background-color:#f6f6f6}.bg-gray[data-v-01eafde1]{background-color:#e5e5e5}.black[data-v-01eafde1]{color:#101010}.white[data-v-01eafde1]{color:#fff}.normal[data-v-01eafde1]{color:#333}.lighter[data-v-01eafde1]{color:#666}.muted[data-v-01eafde1]{color:#999}\n/* 定义字体大小 */.xxl[data-v-01eafde1]{font-size:%?36?%}.xl[data-v-01eafde1]{font-size:%?34?%}.lg[data-v-01eafde1]{font-size:%?32?%}.md[data-v-01eafde1]{font-size:%?30?%}.nr[data-v-01eafde1]{font-size:%?28?%}.sm[data-v-01eafde1]{font-size:%?26?%}.xs[data-v-01eafde1]{font-size:%?24?%}.xxs[data-v-01eafde1]{font-size:%?22?%}\n/* 定义常用外边距 */.ml5[data-v-01eafde1]{margin-left:%?5?%}.ml10[data-v-01eafde1]{margin-left:%?10?%}.ml20[data-v-01eafde1]{margin-left:%?20?%}.ml30[data-v-01eafde1]{margin-left:%?30?%}.mr5[data-v-01eafde1]{margin-right:%?5?%}.mr10[data-v-01eafde1]{margin-right:%?10?%}.mr20[data-v-01eafde1]{margin-right:%?20?%}.mr30[data-v-01eafde1]{margin-right:%?30?%}.mt5[data-v-01eafde1]{margin-top:%?5?%}.mt10[data-v-01eafde1]{margin-top:%?10?%}.mt20[data-v-01eafde1]{margin-top:%?20?%}.mt30[data-v-01eafde1]{margin-top:%?30?%}.mb5[data-v-01eafde1]{margin-bottom:%?5?%}.mb10[data-v-01eafde1]{margin-bottom:%?10?%}.mb20[data-v-01eafde1]{margin-bottom:%?20?%}.mb30[data-v-01eafde1]{margin-bottom:%?30?%}\n/* 定义常用的弹性布局 */.flex1[data-v-01eafde1]{flex:1}.flexnone[data-v-01eafde1]{flex:none}.wrap[data-v-01eafde1]{flex-wrap:wrap}.row[data-v-01eafde1]{display:flex;align-items:center}.row-center[data-v-01eafde1]{display:flex;align-items:center;justify-content:center}.row-end[data-v-01eafde1]{display:flex;align-items:center;justify-content:flex-end}.row-between[data-v-01eafde1]{display:flex;align-items:center;justify-content:space-between}.row-around[data-v-01eafde1]{display:flex;align-items:center;justify-content:space-around}.column[data-v-01eafde1]{display:flex;flex-direction:column;justify-content:center}.column-center[data-v-01eafde1]{display:flex;flex-direction:column;align-items:center;justify-content:center}.column-around[data-v-01eafde1]{display:flex;flex-direction:column;align-items:center;justify-content:space-around}.column-end[data-v-01eafde1]{display:flex;flex-direction:column;align-items:center;justify-content:flex-end}.column-between[data-v-01eafde1]{display:flex;flex-direction:column;align-items:center;justify-content:space-between}\n/* 超出隐藏 */.line1[data-v-01eafde1]{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.line-1[data-v-01eafde1]{word-break:break-all;display:-webkit-box;-webkit-line-clamp:1;-webkit-box-orient:vertical;overflow:hidden}.line2[data-v-01eafde1]{word-break:break-all;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical;overflow:hidden}\n/* 中划线 */.line-through[data-v-01eafde1]{text-decoration:line-through}\n/* br60 */.br60[data-v-01eafde1]{border-radius:%?60?%}\n/* 初始化按钮 */uni-page-body uni-button[data-v-01eafde1]{padding:0;margin:0;background-color:initial;font-weight:400;font-size:%?28?%;overflow:unset;margin-left:0;margin-right:0}uni-page-body uni-button[data-v-01eafde1]::after{border:none}uni-button[type=primary][data-v-01eafde1]{background-color:#ff2c3c}.button-hover[type=primary][data-v-01eafde1]{background-color:#ff2c3c}uni-button[disabled][type=primary][data-v-01eafde1]{background-color:#ff2c3c}\n/* 按钮大小 */uni-button[size="xs"][data-v-01eafde1]{line-height:%?58?%;height:%?58?%;font-size:%?26?%;padding:0 %?30?%}uni-button[size="sm"][data-v-01eafde1]{line-height:%?62?%;height:%?62?%;font-size:%?28?%;padding:0 %?30?%}uni-button[size="md"][data-v-01eafde1]{line-height:%?70?%;height:%?70?%;font-size:%?30?%;padding:0 %?30?%}uni-button[size="lg"][data-v-01eafde1]{line-height:%?80?%;height:%?80?%;font-size:%?32?%;padding:0 %?30?%}.icon-xs[data-v-01eafde1]{min-height:%?28?%;min-width:%?28?%;height:%?28?%;width:%?28?%;vertical-align:middle}.icon-sm[data-v-01eafde1]{min-height:%?30?%;min-width:%?30?%;height:%?30?%;width:%?30?%;vertical-align:middle}.icon[data-v-01eafde1]{min-height:%?34?%;min-width:%?34?%;height:%?34?%;width:%?34?%;vertical-align:middle}.icon-md[data-v-01eafde1]{min-height:%?44?%;min-width:%?44?%;height:%?44?%;width:%?44?%;vertical-align:middle}.icon-lg[data-v-01eafde1]{min-height:%?52?%;min-width:%?52?%;height:%?52?%;width:%?52?%;vertical-align:middle}.icon-xl[data-v-01eafde1]{min-height:%?64?%;min-width:%?64?%;height:%?64?%;width:%?64?%;vertical-align:middle}.icon-xxl[data-v-01eafde1]{min-height:%?120?%;min-width:%?120?%;height:%?120?%;width:%?120?%;vertical-align:middle}.img-null[data-v-01eafde1]{width:%?300?%;height:%?300?%}\n/* 隐藏滚动条 */[data-v-01eafde1]::-webkit-scrollbar{width:0;height:0;color:transparent}.bubble-tips-container[data-v-01eafde1]{position:fixed;z-index:98}.bubble-tips-container .bubble-content[data-v-01eafde1]{padding:%?4?% %?20?% %?4?% %?10?%;background-color:rgba(0,0,0,.7);color:#fff;border-radius:%?120?%}.bubble-tips-container .bubble-content .bubble-img[data-v-01eafde1]{width:%?50?%;height:%?50?%;border-radius:50%;margin-right:%?10?%}',""]),t.exports=e},eeda:function(t,e,i){var n=i("9720");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=i("4f06").default;a("3d5b7a3a",n,!0,{sourceMap:!1,shadowMode:!1})},f183:function(t,e,i){"use strict";var n=i("23e7"),a=i("e330"),r=i("d012"),o=i("861d"),s=i("1a2d"),c=i("9bf2").f,d=i("241c"),u=i("057f"),l=i("4fadd"),f=i("90e3"),h=i("bb2f"),p=!1,v=f("meta"),g=0,m=function(t){c(t,v,{value:{objectID:"O"+g++,weakData:{}}})},b=t.exports={enable:function(){b.enable=function(){},p=!0;var t=d.f,e=a([].splice),i={};i[v]=1,t(i).length&&(d.f=function(i){for(var n=t(i),a=0,r=n.length;a<r;a++)if(n[a]===v){e(n,a,1);break}return n},n({target:"Object",stat:!0,forced:!0},{getOwnPropertyNames:u.f}))},fastKey:function(t,e){if(!o(t))return"symbol"==typeof t?t:("string"==typeof t?"S":"P")+t;if(!s(t,v)){if(!l(t))return"F";if(!e)return"E";m(t)}return t[v].objectID},getWeakData:function(t,e){if(!s(t,v)){if(!l(t))return!0;if(!e)return!1;m(t)}return t[v].weakData},onFreeze:function(t){return h&&p&&l(t)&&!s(t,v)&&m(t),t}};r[v]=!0},f323:function(t,e,i){var n=i("9446");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=i("4f06").default;a("2ed29858",n,!0,{sourceMap:!1,shadowMode:!1})},f48a:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){}));var n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return t.canvasId&&t.size?i("v-uni-view",{staticClass:"lime-painter",style:t.size+t.customStyle},[t.use2dCanvas?i("v-uni-canvas",{staticClass:"lime-painter__canvas",style:t.size,attrs:{id:t.canvasId,type:"2d"}}):i("v-uni-canvas",{staticClass:"lime-painter__canvas",style:t.size,attrs:{"canvas-id":t.canvasId,id:t.canvasId,width:t.boardWidth*t.dpr,height:t.boardHeight*t.dpr}}),t._t("default")],2):t._e()},a=[]},f79f:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){}));var n=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",[this.nodes.length?this._e():this._t("default"),e("v-uni-view",{style:this.showAm+(this.selectable?";user-select:text;-webkit-user-select:text":""),attrs:{id:"_top"}},[e("div",{attrs:{id:"rtf"+this.uid}})])],2)},a=[]},f840:function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return r})),i.d(e,"a",(function(){return n}));var n={uPopup:i("5cc5").default,uImage:i("ba4b").default,priceFormat:i("fefe").default,uNumberBox:i("cf54").default},a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("u-popup",{attrs:{mode:"bottom","border-radius":"14",closeable:!0,"safe-area-inset-bottom":!0},on:{close:function(e){arguments[0]=e=t.$handleEvent(e),t.onClose.apply(void 0,arguments)}},model:{value:t.showPop,callback:function(e){t.showPop=e},expression:"showPop"}},[i("v-uni-view",{staticClass:"bg-white spec-contain"},[i("v-uni-view",{staticClass:"header flex"},[i("u-image",{staticClass:"m-r-20",attrs:{width:"160rpx",height:"160rpx","border-radius":"10rpx",src:t.checkedGoods.image},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.previewImage(t.checkedGoods.image)}}}),i("v-uni-view",{staticClass:"goods-info"},[i("v-uni-view",{staticClass:"primary flex"},[i("price-format",{attrs:{"first-size":46,"second-size":32,"subscript-size":32,price:t.group?t.checkedGoods.team_price:t.checkedGoods.price,weight:500}}),t.group||t.isSeckill||!t.checkedGoods.member_price?t._e():i("v-uni-view",{staticClass:"vip-price flex"},[i("v-uni-view",{staticClass:"price-name xxs"},[t._v("会员价")]),i("v-uni-view",{staticStyle:{padding:"0 11rpx"}},[i("price-format",{attrs:{price:t.checkedGoods.member_price,"first-size":22,"second-size":22,"subscript-size":22,weight:500,color:"#7B3200"}})],1)],1)],1),t.showStock?i("v-uni-view",{staticClass:"sm"},[t._v("库存："+t._s(t.checkedGoods.stock)+"件")]):t._e(),i("v-uni-view",{staticClass:"sm m-t-10"},[i("v-uni-text",[t._v(t._s(t.specValueText))])],1)],1)],1),i("v-uni-view",{staticClass:"spec-main",staticStyle:{height:"550rpx"}},[i("v-uni-scroll-view",{staticStyle:{"max-height":"500rpx"},attrs:{"scroll-y":"true"}},[i("v-uni-view",{staticClass:"spec-list"},t._l(t.specList,(function(e,n){return i("v-uni-view",{key:n,staticClass:"spec"},[i("v-uni-view",{staticClass:"name m-b-20"},[t._v(t._s(e.name)),i("v-uni-text",{staticClass:"primary xs m-l-20"},[t._v(t._s(""==t.checkedGoods.spec_value_ids_arr[n]?"请选择"+e.name:""))])],1),i("v-uni-view",{staticClass:"flex flex-wrap"},t._l(e.spec_value,(function(e,a){return i("v-uni-view",{key:a,class:"spec-item sm "+(t.checkedGoods.spec_value_ids_arr[n]==e.id?"checked ":"")+(t.checkedGoods.spec_value_ids_arr[n]!=e.id&&t.isDisable(e.id)?"unspec-disabled ":"")+(t.isDisable(e.id)?"spec-disabled":""),on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.choseSpecItem(n,a)}}},[t._v(t._s(e.value))])})),1)],1)})),1)],1),i("v-uni-view",{staticClass:"good-num flex row-between m-l-20 m-r-20"},[i("v-uni-view",{staticClass:"label"},[t._v("数量")]),t.show?i("u-number-box",{attrs:{min:1,max:t.checkedGoods.stock},model:{value:t.goodsNum,callback:function(e){t.goodsNum=e},expression:"goodsNum"}}):t._e()],1)],1),t.shop.is_pay?i("v-uni-view",{staticClass:"btns flex row-between bg-white",class:-1!=t.specValueText.indexOf("请选择")?"disabled":""},[0!=t.checkedGoods.stock?[t.showAdd&&0==t.type?i("v-uni-button",{staticClass:"add-cart br60 white btn",attrs:{size:"lg"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onClick("addcart")}}},[t._v(t._s(t.yellowBtnText))]):t._e(),t.showBuy?i("v-uni-button",{staticClass:"bg-primary br60 white btn",attrs:{size:"lg"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onClick("buynow")}}},[t._v(t._s(t.redBtnText))]):t._e(),t.showConfirm?i("v-uni-button",{staticClass:"bg-primary br60 white btn",attrs:{size:"lg"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onClick("confirm")}}},[t._v("确定")]):t._e()]:i("v-uni-button",{staticClass:"bg-gray br60 white btn",attrs:{size:"lg"}},[t._v("缺货")])],2):t._e(),t.showComfirm?i("v-uni-view",{staticClass:"btns flex row-between bg-white",class:-1!=t.specValueText.indexOf("请选择")?"disabled":""},[0!=t.checkedGoods.stock?[t.showAdd&&0==t.type?i("v-uni-button",{staticClass:"add-cart br60 white btn",attrs:{size:"lg"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onClick("addcart")}}},[t._v(t._s(t.yellowBtnText))]):t._e(),t.showBuy?i("v-uni-button",{staticClass:"bg-primary br60 white btn",attrs:{size:"lg"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onClick("buynow")}}},[t._v(t._s(t.redBtnText))]):t._e(),t.showConfirm?i("v-uni-button",{staticClass:"bg-primary br60 white btn",attrs:{size:"lg"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onClick("confirm")}}},[t._v("确定")]):t._e()]:i("v-uni-button",{staticClass:"bg-gray br60 white btn",attrs:{size:"lg"}},[t._v("缺货")])],2):t._e()],1)],1)},r=[]},fcf0:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){}));var n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"root",style:{width:t.width,height:t.height}},[i("v-uni-image",{staticClass:"posterImg",style:{width:t.width,height:t.height},attrs:{src:t.posterUrl}}),i("v-uni-view",{staticClass:"box",style:{width:t.width,height:t.height},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.state=!t.state}}},[i("v-uni-image",{staticClass:"playIcon",attrs:{src:"/static/images/icon_play.png",mode:"widthFix"}})],1),i("v-uni-video",{staticClass:"video",style:{height:t.height,width:t.state?"750rpx":"1rpx"},attrs:{id:t.videoId,src:t.url,"show-mute-btn":!0},on:{pause:function(e){arguments[0]=e=t.$handleEvent(e),t.state=0},timeupdate:function(e){arguments[0]=e=t.$handleEvent(e),t.timeupdate.apply(void 0,arguments)},fullscreenchange:function(e){arguments[0]=e=t.$handleEvent(e),t.fullscreenchange.apply(void 0,arguments)}}})],1)},a=[]},fd7f:function(t,e){t.exports="data:image/png;base64,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"},ffe8:function(t,e,i){"use strict";i.r(e);var n=i("416f"),a=i.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(r);e["default"]=a.a}}]);