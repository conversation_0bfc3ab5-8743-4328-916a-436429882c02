<?php
namespace app\api\logic;

use app\common\basics\Logic;
use app\common\model\goods\GoodsCategory;
use app\common\server\UrlServer;
use think\facade\Db;

class GoodsCategoryLogic extends Logic
{
    /**
     * 获取平台一级分类
     */
    public static function getLevelOneList($is_have=0)
    {
        $where = [
            'del' => 0, // 未删除
            'api_visible' => 1, // API显示
            'pid' => 0
        ];
        //
        $list = GoodsCategory::field('id,name,image,bg_image')
            ->withAttr('bg_image', function ($value, $data) {
                if (!empty($value)) {
                    return UrlServer::getFileUrl($value);
                }
                return $value;
            })
            ->where($where)
            ->order('sort', 'asc')
            ->select()
            ->toArray();
        if ($is_have == 1) {
            $goods_ids = Db::name('jcai_activity')
                ->where([
                    ['audit', '=', 1],
                    ['status', '=', 1],
                    ['del', '=', 0],
                ])->column('goods_id');

            if (empty($goods_ids)) {
                return [];
            }
            $cate_ids = Db::name('goods')
                ->whereIn('id', $goods_ids)
                ->column('first_cate_id');

            $list = array_filter($list, function ($item) use ($cate_ids) {
                return empty($cate_ids) ? false : in_array($item['id'], $cate_ids);
            });
            sort($list);
        }
        return $list;
    }

    /**
     * 获取一级分类下的后代分类
     */
    public static function getListByLevelOne($id)
    {
        $where = [
            'del' => 0, // 未删除
            'api_visible' => 1, // API显示
            'pid' => $id
        ];

        $list = GoodsCategory::field('id,name,image')
            ->where($where)
            ->order('sort', 'asc')
            ->select()
            ->toArray();

        foreach($list as &$item) {
            $where = [
                'del' => 0, // 未删除
                'api_visible' => 1, // API显示
                'pid' => $item['id']
            ];
            $item['children'] =  GoodsCategory::field('id,name,image')
                ->where($where)
                ->order('sort', 'asc')
                ->select()
                ->toArray();
        }

        return $list;
    }
}