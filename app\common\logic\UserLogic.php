    public static function applyAgentDepositRefund($user_id)
    {
        $depositOrder = Db::name('agent_merchantfees')
            ->where('user_id', $user_id)
            ->where('status', 'in', [1, 2]) // 1已支付(公示期), 2公示期结束(可退)
            ->order('id', 'desc') // 获取最新的有效保证金订单
            ->find();

        if (!$depositOrder) {
            return '未找到有效的保证金支付记录';
        }

        if ($depositOrder['status'] == 1) { // 状态为1：公示期
            // 获取配置的申请后公示期天数
            $config = ConfigServer::get('agent_setting', '', [
                'deposit_publicity_period_days' => 0,
                'refund_publicity_period_days' => 90
            ]);

            // 计算公示期结束时间
            $publicityPeriodEnd = $depositOrder['payment_date'] + ($config['refund_publicity_period_days'] * 86400); // 86400秒=1天

            if (time() < $publicityPeriodEnd) {
                $endDate = date('Y-m-d H:i:s', $publicityPeriodEnd);
                return '保证金仍在公示期，请于 ' . $endDate . ' 后再申请退款';
            }

            // 公示期已过，但状态未更新，自动更新为可退状态
            Db::name('agent_merchantfees')->where('id', $depositOrder['id'])->update([
                'status' => 2,
                'publicity_period_end_time' => time()
            ]);
            $depositOrder['status'] = 2;
        }

        if ($depositOrder['status'] != 2) { // 状态必须是2：公示期结束(可退)
            return '保证金状态异常，无法申请退款';
        }