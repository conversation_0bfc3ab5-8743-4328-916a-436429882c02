<?php
declare (strict_types = 1);

namespace app\model\agent;

use app\common\basics\Models;
use app\common\model\agent\AgentMerchantfees;
use app\common\model\user\User;
use think\facade\Db;

/**
 * 代理保证金明细表模型
 * @mixin \think\Model
 */
class AgentDepositDetails extends Models
{
    /**
     * 关联代理用户模型
     */
    public function user()
    {
        return $this->hasOne(User::class, 'id', 'user_id');
    }

    /**
     * 关联代理保证金模型
     */
    public function deposit()
    {
        return $this->hasOne(AgentMerchantfees::class, 'id', 'deposit_id');
    }

    /**
     * 获取变动类型文本
     * @param int $type 变动类型：1-缴纳 2-增加 3-扣除 4-退还
     * @return string 变动类型文本
     */
    public static function getChangeTypeText($type)
    {
        $types = [
            1 => '缴纳',
            2 => '增加',
            3 => '扣除',
            4 => '退还'
        ];
        return $types[$type] ?? '未知';
    }
}
