{layout name="layout1" /}

<div class="wrapper">
    <div class="layui-card layui-form">
        <div class="layui-card-body">
            <div class="layui-collapse" style="border:1px dashed #c4c4c4">
                <div class="layui-colla-item">
                    <h2 class="layui-colla-title like-layui-colla-title" style="background-color: #fff">操作提示</h2>
                    <div class="layui-colla-content layui-show tips">
                        *移动端金刚区样式设置
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-card-body">
            <div class="layui-form-item">
                <label class="layui-form-label" style="width:85px;">金刚区样式：</label>
                <div class="layui-input-block">
                    <input type="radio" name="shop_street_hide" value="1" title="带框" {if $shop_street_hide==1}checked{/if}>
                    <input type="radio" name="shop_street_hide" value="0" title="无框" {if $shop_street_hide==0}checked{/if}>
                </div>
<!--                <div class="layui-form-mid layui-word-aux">设置关闭店铺街功能时，用户进入店铺街会显示暂无数据，方便微信小程序提交审核</div>-->
            </div>

                <div class="layui-form-item">
                    <lable class="layui-form-label">金刚区底图:</lable>
                    <div class="layui-input-block">
                        <div class="like-upload-image">
                            {if $backgroupimage}
                            <div class="upload-image-div">
                                <img src="{$backgroupimage}" alt="img">
                                <input type="hidden" name="ad" value="{$backgroupimage}">
                                <div class="del-upload-btn">x</div>
                            </div>
                            <div class="upload-image-elem" style="display:none;"><a class="add-upload-image" id="image"> + 添加图片</a></div>
                            {else}
                            <div class="upload-image-elem"><a class="add-upload-image" id="image"> + 添加图片</a></div>
                            {/if}
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <lable class="layui-form-label"></lable>
                    <div class="layui-input-block">
                        <span class="layui-word-aux">支持gif</span>
                    </div>
                </div>
            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button class="layui-btn layui-btn-normal" lay-submit lay-filter="addBtn">确定</button>
                </div>
            </div>

        </div>
    </div>
</div>

<script>
    layui.use(["form"], function () {
        var form = layui.form;
        // 图片上传
        like.delUpload();
        $(document).on("click", "#image", function () {
            like.imageUpload({
                limit: 1,
                field: "ad",
                that: $(this)
            });
        });


        form.on('submit(addBtn)', function(data){
            like.ajax({
                url: "{:url('decoration.MenuDecorate/setting')}",
                data: data.field,
                type: "POST",
                success:function(res) {
                    if(res.code === 1) {
                        layui.layer.msg(res.msg);
                        setTimeout(function () {
                            location.reload()
                        }, 500);
                    }
                }
            });

        });
    })

</script>