(window.webpackJsonp=window.webpackJsonp||[]).push([[39],{570:function(t,e,n){var content=n(625);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,n(17).default)("1db19fd6",content,!0,{sourceMap:!1})},624:function(t,e,n){"use strict";n(570)},625:function(t,e,n){var r=n(16)(!1);r.push([t.i,".content{padding:30px;max-width:1200px;margin:0 auto;background-color:#fff}",""]),t.exports=r},704:function(t,e,n){"use strict";n.r(e);var r=n(9),o=(n(53),{head:function(){return{title:this.$store.getters.headTitle,link:[{rel:"icon",type:"image/x-icon",href:this.$store.getters.favicon}]}},layout:"void",asyncData:function(t){return Object(r.a)(regeneratorRuntime.mark((function e(){var n,r,content,o,data,c,f;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(n=t.$get,1!=(r=t.query).type){e.next=9;break}return e.next=4,n("policy/service");case 4:o=e.sent,data=o.data,content=data.content,e.next=15;break;case 9:if(2!=r.type){e.next=15;break}return e.next=12,n("policy/privacy");case 12:c=e.sent,f=c.data,content=f.content;case 15:return e.abrupt("return",{content:content});case 16:case"end":return e.stop()}}),e)})))()},data:function(){return{content:""}}}),c=(n(624),n(8)),component=Object(c.a)(o,(function(){var t=this,e=t._self._c;return e("div",{staticClass:"content"},[e("div",{domProps:{innerHTML:t._s(t.content)}})])}),[],!1,null,null,null);e.default=component.exports}}]);