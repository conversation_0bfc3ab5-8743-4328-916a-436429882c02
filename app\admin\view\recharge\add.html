{layout name="layout2" /}
<div class="layui-form" lay-filter="">
    <div class="layui-tab">
        <div class="layui-form-item layui-col-sm6  layui-col-md4">
            <label class="layui-form-label"><font color="red">*</font>充值金额</label>
            <div class="layui-input-block">
                <input type="number" name="money" lay-verify="required" lay-verType="tips" placeholder="请输入充值金额" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item layui-col-sm6  layui-col-md4">
            <label class="layui-form-label">赠送金额</label>
            <div class="layui-input-block">
                <input type="number" name="give_money" lay-verType="tips" placeholder="请输入赠送金额" autocomplete="off" class="layui-input">
            </div>
        </div>

        <div class="layui-form-item layui-col-sm6  layui-col-md4">
            <label class="layui-form-label">排序</label>
            <div class="layui-input-block">
                <input type="number"  name="sort" value="100"  placeholder="请输入排序" class="layui-input">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">推荐</label>
            <div class="layui-input-inline">
                <input type="radio" name="is_recommend" value="1" title="是" >
                <input type="radio" name="is_recommend" value="0" title="否" checked>
            </div>
        </div>
        <div class="layui-form-item layui-hide">
            <input type="button" lay-submit lay-filter="add-recharge-submit" id="add-recharge-submit" value="确认">
        </div>
    </div>
</div>
<script>
    layui.config({
        version:"{$front_version}",
        base: '/static/lib/' //静态资源所在路径
    }).use(['form'], function(){
        var $ = layui.$
            ,form = layui.form
    });
</script>
