{layout name="layout1" /}

<div class="wrapper">
    <div class="layui-card">
        <!-- 操作提示 -->
        <div class="layui-card-body">
            <div class="layui-collapse" style="border:1px dashed #c4c4c4">
                <div class="layui-colla-item">
                    <h2 class="layui-colla-title like-layui-colla-title">操作提示</h2>
                    <div class="layui-colla-content layui-show">
                        <p>*商家提交入驻申请后，提交保证金。</p>
                        <p>*保证金。</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 搜索区域 -->
        <div class="layui-card-body layui-form">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label for="name" class="layui-form-label">商家名称：</label>
                    <div class="layui-input-inline">
                        <input type="text" id="name" name="name" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label for="nickname" class="layui-form-label" style="width:85px;">联系人名称：</label>
                    <div class="layui-input-inline">
                        <input type="text" id="nickname" name="nickname" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label for="status" class="layui-form-label">审核状态：</label>
                    <div class="layui-input-inline">
                        <select id="status" name="status" lay-filter="status">
                            <option value="">全部</option>
                            <option value="0">待审核</option>
                            <option value="1">已通过</option>
                            <option value="2">未通过</option>
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">申请时间：</label>
                    <div class="layui-inline" style="margin-right:0;">
                        <div class="layui-input-inline" >
                            <input type="text" id="apply_start_time" name="apply_start_time" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline"> - </div>
                    <div class="layui-inline" style="margin-right:0;">
                        <div class="layui-input-inline" style="margin-right:0;">
                            <input type="text" id="apply_end_time" name="apply_end_time" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                </div>
                <div class="layui-inline">
                    <a class="layui-btn layui-btn-sm layui-btn-normal" lay-submit lay-filter="search">搜索</a>
                    <a class="layui-btn layui-btn-sm layui-btn-primary" lay-submit lay-filter="clear-search">重置</a>
                </div>
            </div>
        </div>

        <!-- 主体区域 -->
        <div class="layui-tab layui-tab-card" lay-filter="like-tabs">

            <div class="layui-tab-content" style="padding: 0 15px;">
                <table id="like-table-lists" lay-filter="like-table-lists"></table>
                <script type="text/html" id="table-license">
                    {{#  layui.each(d.license, function(index, item){ }}
                        <img src="{{item}}" alt="资质" style="width:50px;height:50px;margin:0 3px;">
                    {{#  }); }}
                </script>
                <script type="text/html" id="table-status">
                    {{#  if(d.status == 0){ }}
                    <span style="color: #FFB800;">待审核</span>
                    {{#  } else if(d.status == 1){ }}
                    <span style="color: #5FB878;">已通过</span>
                    {{#  } else if(d.status == 2){ }}
                    <span style="color: #FF5722;">未通过</span>
                    {{#  } }}
                </script>
                <script type="text/html" id="table-pay-status">
                    {{#  if(d.pay_status == 0){ }}
                    <span style="color: #FFB800;">未支付</span>
                    {{#  } else if(d.pay_status == 1){ }}
                    <span style="color: #5FB878;">已支付</span>
                    {{#  } else if(d.pay_status == 2){ }}
                    <span style="color: #1E9FFF;">退款中</span>
                    {{#  } else if(d.pay_status == 3){ }}
                    <span style="color: #FF5722;">已退款</span>
                    {{#  } }}
                </script>
                <script type="text/html" id="table-refund-status">
                    <span style="color: {{d.refund_status_color ? d.refund_status_color : '#999'}};">{{d.refund_status_display ? d.refund_status_display : '未知状态'}}</span>
                </script>
                <script type="text/html" id="table-publicity-time">
                    {{#  if(d.refund_status == 1 && d.refund_publicity_end_time){ }}
                        {{#  var current_time = new Date().getTime(); }}
                        {{#  var end_time = new Date(d.refund_publicity_end_time).getTime(); }}
                        {{#  if(current_time < end_time){ }}
                            <span style="color: #FFB800;">{{d.refund_publicity_end_time}}</span>
                            <br><small style="color: #999;">公示期进行中</small>
                        {{#  } else { }}
                            <span style="color: #1E9FFF;">{{d.refund_publicity_end_time}}</span>
                            <br><small style="color: #1E9FFF;">公示期已结束</small>
                        {{#  } }}
                    {{#  } else if(d.refund_status == 1){ }}
                        <span style="color: #999;">-</span>
                    {{#  } else { }}
                        <span style="color: #999;">-</span>
                    {{#  } }}
                </script>
                <script type="text/html" id="table-operation">
                    <a class="layui-btn layui-btn-normal layui-btn-sm" lay-event="detail">明细</a>
                    {{#  if(d.status == 0){ }}
                    <a class="layui-btn layui-btn-primary layui-btn-sm" lay-event="audit">审核</a>
                    {{#  } }}
                    {{#  if(d.status == 1 && d.pay_status == 1 && d.refund_status == 0){ }}
                    <a class="layui-btn layui-btn-warm layui-btn-sm" lay-event="edit">扣减</a>
                    <a class="layui-btn layui-btn-danger layui-btn-sm" lay-event="refund_close">退费</a>
                    {{#  } }}
                    {{#  if(d.can_refund && d.refund_status == 1){ }}
                    <a class="layui-btn layui-btn-danger layui-btn-sm" lay-event="process_refund">处理退款</a>
                    {{#  } }}
                    {{#  if(d.refund_status == 2 && d.refund_time>0){ }}
                    <a class="layui-btn layui-btn-normal layui-btn-sm" lay-event="refund_detail">退款详情</a>
                    {{#  } }}
                    {{#  if(d.refund_status == 1 && d.refund_time>0){ }}
                    <a class="layui-btn layui-btn-normal layui-btn-sm" lay-event="refund_detail">退款详情</a>
                    {{#  } }}
                </script>
            </div>
        </div>

    </div>
</div>

<script>
    layui.use(["table", "form", "element", "laydate"], function(){
        var table   = layui.table;
        var form    = layui.form;
        var element = layui.element;
        var laydate = layui.laydate;


        laydate.render({type:"datetime", elem:"#apply_start_time", trigger:"click"});
        laydate.render({type:"datetime", elem:"#apply_end_time", trigger:"click"});


        like.tableLists("#like-table-lists", "{:url()}", [
            {field:"id", width:60, title:"ID"}
            ,{field:"name", width:180, title:"商家名称"}
            ,{field:"deposit_amount", width:100, align:"center", title:"缴纳金额"}
            ,{field:"current_balance", width:120, align:"center", title:"当前剩余保证金"}
            ,{field:"payment_method", width:100, align:"center", title:"缴纳方式"}
            ,{field:"status", width:100, align:"center", title:"审核状态", templet:"#table-status"}
            ,{field:"pay_status", width:100, align:"center", title:"支付状态", templet:"#table-pay-status"}
            ,{field:"refund_status", width:150, align:"center", title:"退款状态", templet:"#table-refund-status"}
            ,{field:"refund_publicity_end_time", width:170, align:"center", title:"公示期结束时间", templet:"#table-publicity-time"}
            ,{field:"remark", width:150, title:"审核理由"}
            ,{field:"created_at", width:170, title:"缴纳时间"}
            ,{field:"updated_at", width:170, align:"center", title:"变动时间"}
            ,{title:"操作", width:280, align:"center", fixed:"right", toolbar:"#table-operation"}
        ]);


        var active = {
            detail: function(obj) {
                layer.open({
                    type: 2
                    ,title: "保证金增减明细"
                    ,content: "{:url('shop.Apply/depositdetail')}?id=" + obj.data.shop_id
                    ,area: ["600px", "500px"]

                });
            },
            audit: function(obj) {
                layer.open({
                    type: 2
                    ,title: "保证金审核"
                    ,content: "{:url('shop.Apply/depositAudit')}?id=" + obj.data.id
                    ,area: ["500px", "400px"]
                    ,btn: ["确定", "取消"]
                    ,yes: function(index, layero){
                        var iframeWindow = window["layui-layer-iframe" + index];
                        var submit = layero.find("iframe").contents().find("#depositAuditSubmit");
                        iframeWindow.layui.form.on("submit(depositAuditSubmit)", function(data){
                            data.field["id"] = obj.data.id;
                            like.ajax({
                                url: "{:url('shop.Apply/depositAudit')}",
                                data: data.field,
                                type: "POST",
                                success:function(res) {
                                    if(res.code === 1) {
                                        layui.layer.msg(res.msg);
                                        layer.close(index);
                                        table.reload("like-table-lists", {
                                            where: {},
                                            page: { cur: 1 }
                                        });
                                    } else {
                                        layui.layer.msg(res.msg);
                                    }
                                }
                            });
                        });
                        submit.trigger("click");
                    }
                });
            },

            refund_close: function(obj) {
                layer.open({
                    type: 2
                    ,title: "退费关店确认"
                    ,content: "{:url('shop.Apply/refundAndCloseShop')}?id=" + obj.data.id
                    ,area: ["600px", "500px"]
                    ,btn: ["确定", "取消"]
                    ,yes: function(index, layero){
                        var iframeWindow = window["layui-layer-iframe" + index];
                        var submit = layero.find("iframe").contents().find("#refund_close_submit");
                        iframeWindow.layui.form.on("submit(refund_close_submit)", function(data){
                            like.ajax({
                                url: "{:url('shop.Apply/refundAndCloseShop')}",
                                data: data.field,
                                type: "POST",
                                success:function(res) {
                                    if(res.code === 1) {
                                        layui.layer.msg(res.msg);
                                        layer.close(index);
                                        table.reload("like-table-lists", {
                                            where: {},
                                            page: { cur: 1 }
                                        });
                                    } else {
                                        layui.layer.msg(res.msg);
                                    }
                                }
                            });
                        });
                        submit.trigger("click");
                    }
                });
            },
            refund_detail: function(obj) {
                layer.open({
                    type: 2
                    ,title: "退款详情"
                    ,content: "{:url('shop.Apply/refundDetail')}?id=" + obj.data.id
                    ,area: ["600px", "600px"]
                });
            },
            process_refund: function(obj) {
                layer.confirm("确定处理此退款申请吗？公示期已结束，可以进行退款操作。", {
                    title: '处理退款确认',
                    icon: 3
                }, function(index) {
                    layer.open({
                        type: 2
                        ,title: "处理退款"
                        ,content: "{:url('shop.Apply/processRefund')}?id=" + obj.data.id
                        ,area: ["700px", "600px"]
                        ,btn: ["确定", "取消"]
                        ,yes: function(refundIndex, layero){
                            var iframeWindow = window["layui-layer-iframe" + refundIndex];
                            var submit = layero.find("iframe").contents().find("#process_refund_submit");
                            iframeWindow.layui.form.on("submit(process_refund_submit)", function(data){
                                like.ajax({
                                    url: "{:url('shop.Apply/processRefund')}",
                                    data: data.field,
                                    type: "POST",
                                    success:function(res) {
                                        if(res.code === 1) {
                                            layui.layer.msg(res.msg);
                                            layer.close(refundIndex);
                                            layer.close(index);
                                            table.reload("like-table-lists", {
                                                where: {},
                                                page: { cur: 1 }
                                            });
                                        } else {
                                            layui.layer.msg(res.msg);
                                        }
                                    }
                                });
                            });
                            submit.trigger("click");
                        }
                    });
                });
            },
            del: function(obj) {
                layer.confirm("确定删除入驻申请："+obj.data.name, function(index) {
                    like.ajax({
                        url: "{:url('shop.Apply/del')}",
                        data: {id: obj.data.id},
                        type: "POST",
                        success: function (res) {
                            if (res.code === 1) {
                                active.totalCount();
                                layui.layer.msg(res.msg);
                                layer.close(index);
                                obj.del();
                            }
                        }
                    });
                    layer.close(index);
                })
            },
            edit: function(obj){
            var id = obj.data.id;
            layer.open({
                type: 2
                ,title: '保证金调整'
                ,content: '{:url("shop.Apply/editMoney")}?id='+id
                ,area: ['600px', '500px']
                ,btn: ['确定', '取消']
                ,yes: function(index, layero){
                    var iframeWindow = window['layui-layer-iframe'+ index]
                        ,submit = layero.find('iframe').contents().find('#deposit_adjust_submit');
                    //监听提交
                    iframeWindow.layui.form.on('submit(deposit_adjust_submit)', function(data){
                        var field = data.field;
                        $.ajax({
                            url:'{:url("shop.Apply/editMoney")}',
                            data:field,
                            type:"post",
                            success:function(res)
                            {
                                if(res.code == 1)
                                {
                                    layui.layer.msg(res.msg, {
                                        offset: '15px'
                                        , icon: 1
                                        , time: 1000
                                    });
                                    layer.close(index); //关闭弹层
                                    table.reload('like-table-lists'); //数据刷新
                                }else{
                                    layer.msg(res.msg, {
                                        offset: '15px'
                                        , icon: 2
                                        , time: 1000
                                    });
                                }
                            }
                        });
                    });
                    submit.trigger('click');
                }
            })
        },
            totalCount: function() {
                like.ajax({
                    url: '{:url("shop.Apply/totalCount")}',
                    data: {},
                    type: "GET",
                    success: function (res) {
                        if (res.code === 1) {
                            $(".layui-tab-title li[lay-id=1]").html("待审核(" + res.data.stay + ")");
                            $(".layui-tab-title li[lay-id=2]").html("审核通过(" + res.data.ok + ")");
                            $(".layui-tab-title li[lay-id=3]").html("审核拒绝(" + res.data.refuse + ")");
                        }
                    }
                });
            }
        };
        like.eventClick(active);


        element.on("tab(like-tabs)", function(){
            var type = this.getAttribute("lay-id");
            table.reload("like-table-lists", {
                where: {type: type},
                page: { cur: 1 }
            });
        });


        form.on("submit(search)", function(data){
            data.field["type"] = $(".layui-tab-title li.layui-this").attr("lay-id");
            table.reload("like-table-lists", {
                where: data.field,
                page: {
                    curr: 1
                }
            });
        });


        form.on("submit(clear-search)", function(){
            $("#name").val("");
            $("#nickname").val("");
            $("#status").val("");
            $("#apply_start_time").val("");
            $("#apply_end_time").val("");

            var type = $(".layui-tab-title li.layui-this").attr("lay-id");

            form.render("select");
            table.reload("like-table-lists", {
                where: {type: type},
                page: {
                    curr: 1
                }
            });
        });

    })
</script>
