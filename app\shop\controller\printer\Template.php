<?php


namespace app\shop\controller\printer;

use app\common\basics\ShopBase;
use app\shop\logic\printer\TemplateLogic;
use app\shop\validate\printer\TemplateValidate;
use app\common\server\JsonServer;

/**
 * 小票模板控制器
 * Class Template
 * @package app\admin\controller\printer
 */
class Template extends ShopBase
{
    /**
     * @notes 编辑小票模板
     * @return \think\response\Json|\think\response\View
     * <AUTHOR>
     * @date 2022/1/19 16:45
     */
    public function edit()
    {
        if($this->request->isAjax()){
            $post = $this->request->post();
            (new TemplateValidate())->goCheck();
            $res = TemplateLogic::edit($post, $this->shop_id);
            if (true === $res) {
                return JsonServer::success('操作成功');
            }
            return JsonServer::error($res);
        }
        return view('', [
            'detail' => TemplateLogic::getDetail($this->shop_id),
        ]);
    }


}