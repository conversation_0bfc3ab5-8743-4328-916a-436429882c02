(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-goods_search-goods_search"],{"03cd":function(t,e,s){"use strict";s.d(e,"b",(function(){return i})),s.d(e,"c",(function(){return n})),s.d(e,"a",(function(){return o}));var o={uSearch:s("5744").default,sortNav:s("3055").default,mescrollUni:s("5403").default,goodsList:s("c574").default},i=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("v-uni-view",{staticClass:"goods-search flex-col"},[s("v-uni-view",{staticClass:"header-wrap"},[s("v-uni-view",{staticClass:"search"},[s("u-search",{attrs:{focus:t.showHistory,"bg-color":"#F4F4F4"},on:{focus:function(e){arguments[0]=e=t.$handleEvent(e),t.showHistory=!0},search:function(e){arguments[0]=e=t.$handleEvent(e),t.onSearch.apply(void 0,arguments)}},model:{value:t.keyword,callback:function(e){t.keyword=e},expression:"keyword"}})],1),s("sort-nav",{directives:[{name:"show",rawName:"v-show",value:!t.showHistory,expression:"!showHistory"}],model:{value:t.sortConfig,callback:function(e){t.sortConfig=e},expression:"sortConfig"}})],1),s("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:t.showHistory,expression:"showHistory"}],staticClass:"search-content bg-white"},[s("v-uni-scroll-view",{staticStyle:{height:"100%"},attrs:{"scroll-y":!0}},[t.hotList.length?s("v-uni-view",{staticClass:"search-words"},[s("v-uni-view",{staticClass:"title"},[t._v("热门搜索")]),s("v-uni-view",{staticClass:"words flex flex-wrap"},t._l(t.hotList,(function(e,o){return s("v-uni-view",{key:o,staticClass:"item br60  m-r-20 m-b-20 lighter sm line-1",on:{click:function(s){arguments[0]=s=t.$handleEvent(s),t.onChangeKeyword(e)}}},[t._v(t._s(e))])})),1)],1):t._e(),t.historyList.length?s("v-uni-view",{staticClass:"search-words"},[s("v-uni-view",{staticClass:"title flex row-between"},[s("v-uni-view",[t._v("历史搜索")]),s("v-uni-view",{staticClass:"xs muted m-r-20",staticStyle:{padding:"10rpx 20rpx"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.clearSearchFun.apply(void 0,arguments)}}},[t._v("清空")])],1),s("v-uni-view",{staticClass:"words flex flex-wrap"},t._l(t.historyList,(function(e,o){return s("v-uni-view",{key:o,staticClass:"item br60  m-r-20 m-b-20 lighter sm line-1",on:{click:function(s){arguments[0]=s=t.$handleEvent(s),t.onChangeKeyword(e)}}},[t._v(t._s(e))])})),1)],1):t._e()],1)],1),s("v-uni-view",{staticClass:"content"},[s("mescroll-uni",{ref:"mescrollRef",attrs:{up:t.upOption,down:{auto:!1},fixed:!1},on:{init:function(e){arguments[0]=e=t.$handleEvent(e),t.mescrollInit.apply(void 0,arguments)},down:function(e){arguments[0]=e=t.$handleEvent(e),t.downCallback.apply(void 0,arguments)},up:function(e){arguments[0]=e=t.$handleEvent(e),t.upCallback.apply(void 0,arguments)}}},[s("v-uni-view",{staticClass:"goods-list"},[s("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:"double"==t.sortConfig.goodsType,expression:"sortConfig.goodsType == 'double'"}],staticClass:"double"},[s("goods-list",{attrs:{type:"double",list:t.goodsList}})],1),s("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:"one"==t.sortConfig.goodsType,expression:"sortConfig.goodsType == 'one'"}],staticClass:"one"},[s("goods-list",{attrs:{list:t.goodsList,type:"one"}})],1)],1)],1)],1)],1)},n=[]},"99f8":function(t,e,s){var o=s("fd05");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[t.i,o,""]]),o.locals&&(t.exports=o.locals);var i=s("4f06").default;i("3e4b15ca",o,!0,{sourceMap:!1,shadowMode:!1})},ae81:function(t,e,s){"use strict";s.r(e);var o=s("03cd"),i=s("b197");for(var n in i)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return i[t]}))}(n);s("e0e4");var a=s("f0c5"),r=Object(a["a"])(i["default"],o["b"],o["c"],!1,null,"52c26951",null,!1,o["a"],void 0);e["default"]=r.exports},b197:function(t,e,s){"use strict";s.r(e);var o=s("df8f"),i=s.n(o);for(var n in o)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return o[t]}))}(n);e["default"]=i.a},df8f:function(t,e,s){"use strict";s("7a82");var o=s("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,s("99af");var i=o(s("f3f3")),n=s("b550"),a=s("26cb"),r=s("753f"),c=s("a5ae"),d=o(s("53f3")),l={mixins:[d.default],data:function(){return{upOption:{auto:!1,empty:{icon:"/static/images/goods_null.png",tip:"暂无商品"}},keyword:"",status:r.loadingType.LOADING,page:1,sortConfig:{goodsType:"double",priceSort:"",saleSort:""},goodsList:[],showHistory:!1,hotList:[],historyList:[]}},watch:{keyword:function(t,e){t||this.id||(this.showHistory=!0)},showHistory:function(t){t&&this.getSearchpageFun()},"sortConfig.saleSort":function(){this.onSearch()},"sortConfig.priceSort":function(){this.onSearch()}},onLoad:function(t){this.onSearch=(0,c.trottle)(this.onSearch,500,this),this.init(t)},computed:(0,i.default)({},(0,a.mapGetters)(["sysInfo"])),methods:{downCallback:function(){this.onRefresh()},upCallback:function(t){var e=this,s=t.num,o=t.size,i=(this.goodsList,this.keyword),a=this.sortConfig,r=a.priceSort,c=a.saleSort,d={page_size:o,page_no:s,platform_cate_id:1==this.type?this.id:"",brand_id:0==this.type?this.id:"",keyword:i,sort_by_price:r,sort_by_sales:c};(0,n.getGoodsList)(d).then((function(s){var o=s.data;1==t.num&&(e.goodsList=[]);var i=o.lists,n=i.length,a=!!o.more;e.goodsList=e.goodsList.concat(i),e.mescroll.endSuccess(n,a)})).catch((function(){e.mescroll.endErr()}))},onChange:function(t){this.keyword=t.value},clearSearchFun:function(){var t=this;(0,n.clearSearch)().then((function(e){1==e.code&&t.getSearchpageFun()}))},init:function(t){var e=this,s=this.$Route.query,o=s.id,i=s.name,n=s.type;this.type=n,o?(uni.setNavigationBarTitle({title:i}),this.id=o,this.$nextTick((function(){e.onRefresh()}))):(uni.setNavigationBarTitle({title:"商品搜索"}),this.showHistory=!0)},getSearchpageFun:function(){var t=this;(0,n.getSearchpage)().then((function(e){if(1==e.code){var s=e.data,o=s.history_lists,i=s.hot_lists;t.hotList=i,t.historyList=o}}))},onClear:function(){this.id&&this.onRefresh()},onSearch:function(){var t=this;this.showHistory=!1,this.$nextTick((function(){t.onRefresh()}))},onRefresh:function(){this.goodsList=[],this.mescroll.resetUpScroll()},onChangeKeyword:function(t){this.keyword=t,this.showHistory=!1,this.onRefresh()}}};e.default=l},e0e4:function(t,e,s){"use strict";var o=s("99f8"),i=s.n(o);i.a},fd05:function(t,e,s){var o=s("24fb");e=o(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */uni-page-body[data-v-52c26951]{height:100%;padding:0}.goods-search[data-v-52c26951]{height:100%;position:relative}.goods-search .header-wrap[data-v-52c26951]{position:relative;z-index:999}.goods-search .header-wrap .search[data-v-52c26951]{box-shadow:0 3px 6px rgba(0,0,0,.03);position:relative;z-index:1}.goods-search .search-content[data-v-52c26951]{position:absolute;width:100%;height:100%;padding-top:%?100?%;z-index:100}.goods-search .search-content .search-words[data-v-52c26951]{padding-left:%?24?%;padding-bottom:%?20?%}.goods-search .search-content .search-words .title[data-v-52c26951]{padding:%?26?% 0}.goods-search .search-content .search-words .words .item[data-v-52c26951]{line-height:%?52?%;height:%?52?%;padding:0 %?24?%;background-color:#f5f5f5}.goods-search .content[data-v-52c26951]{flex:1;min-height:0}.goods-search .content .goods-list[data-v-52c26951]{overflow:hidden}',""]),t.exports=e}}]);