<?php



namespace app\admin\logic\goods;


use app\common\basics\Logic;
use app\common\model\goods\Goods;
use app\common\model\goods\GoodsColumn;

/**
 * 商品栏目-逻辑
 * Class GoodsColumnLogic
 * @package app\admin\logic
 */
class ColumnLogic extends Logic
{

    /**
     * Notes: 列表
     * @param $get
     * <AUTHOR> 10:53)
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function lists($get)
    {
        $result = GoodsColumn::where(['del' =>0])
            ->order('sort')
            ->paginate([
                'list_rows'=> $get['limit'],
                'page'=> $get['page']
            ]);

        return ['count' => $result->total(), 'lists' => $result->getCollection()];
    }


    /**
     * Notes: 添加
     * @param $post
     * @return GoodsColumn|\think\Model
     *<AUTHOR> 10:54)
     */
    public static function add($post)
    {
        return GoodsColumn::create([
            'name'     => $post['name'],
            'image'     => $post['image'],
            'remark'   => $post['remark'] ?? '',
            'status'   => isset($post['status']) && $post['status'] == 'on' ? 1 : 0,
        ]);
    }


    /**
     * Notes: 编辑
     * @param $post
     * @return GoodsColumn
     *<AUTHOR> 10:54)
     */
    public static function edit($post)
    {
        return GoodsColumn::update([
            'name'     => $post['name'],
            'image'     => $post['image'],
            'remark'   => $post['remark'] ?? '',
            'status'   => isset($post['status']) && $post['status'] == 'on' ? 1 : 0,
        ], ['id' => $post['id']]);
    }


    /**
     * Notes: 删除
     * @param $id
     * <AUTHOR> 2:51)
     * @return bool
     */
    public static function del($id)
    {
        //栏目删除,则栏目商品都删除
        GoodsColumn::update(['del' => 1], ['id' => $id]);
        Goods::whereFindInSet('column_ids', $id)->update(['column_ids' => '']);
        return true;
    }

    /**
     * 列表（不分页）
     */
    public static function getList()
    {
        return GoodsColumn::where(['del' => 0])->order('sort', 'desc')->column('id,name');
    }
}