<?php



namespace app\common\server;


use app\common\enum\IntegralOrderEnum;
use app\common\enum\PayEnum;
use app\common\logic\IntegralOrderRefundLogic;
use app\common\logic\PaymentLogic;
use app\common\logic\PayNotifyLogic;
use app\common\model\Client_;
use app\common\model\integral\IntegralOrder;
use app\common\model\Pay;
use EasyWeChat\Factory;
use EasyWeChat\Payment\Application;
use app\common\model\order\OrderLog;
use app\common\model\order\Order;
use app\common\model\order\OrderTrade;
use Endroid\QrCode\QrCode;
use http\Client;
use think\facade\Db;
use think\Exception;

/**
 * Class WeChatPayServer
 * @package app\common\server
 */
class WeChatPayServer
{

    protected static $error = '未知错误';
    protected static $return_code = 0;

    /**
     * @notes 错误信息
     * @return string
     * <AUTHOR>
     * @date 2021/7/13 6:34 下午
     */
    public static function getError()
    {

        return self::$error;
    }

    /**
     * @notes 返回状态码
     * @return int
     * <AUTHOR>
     * @date 2021/7/13 6:34 下午
     */
    public static function getReturnCode()
    {

        return self::$return_code;
    }


    /**
     * @notes 微信统一下单
     * @param $from
     * @param $order
     * @param $order_source
     * @return array|false|string
     * <AUTHOR>
     * @date 2021/7/13 6:34 下午
     * @throws \EasyWeChat\Kernel\Exceptions\InvalidConfigException
     * @throws \Endroid\QrCode\Exception\InvalidWriterException
     * @throws \GuzzleHttp\Exception\GuzzleException
     * @throws \EasyWeChat\Kernel\Exceptions\InvalidArgumentException
     */
    public static function unifiedOrder($from, $order, $order_source, $post = [])
    {

        try {
            $wechat_config = self::getWeChatConfig($order, $order_source, $post);
            $auth = $wechat_config['auth'];
            $config = $wechat_config['config'];
            $notify_url = $wechat_config['notify_url'];
            //jsapi需要验证openID
            $check_source = [Client_::mnp, Client_::oa];
            if (!$auth && in_array($order_source, $check_source)) {
                throw new Exception('授权信息失效');
            }

            $app = Factory::payment($config);

            $attributes = self::getAttributes($from, $order, $order_source, $auth, $notify_url);

            $result = $app->order->unify($attributes);

            if ($result['return_code'] == 'SUCCESS' && $result['result_code'] == 'SUCCESS') {

                //小程序,公众号
                if (in_array($order_source, [Client_::mnp, Client_::oa])) {
                    $data = $app->jssdk->bridgeConfig($result['prepay_id'], false);
                }

                //app客户端
                if (in_array($order_source, [Client_::ios, Client_::android])) {
                    $data = $app->jssdk->appConfig($result['prepay_id'], false);
                }

                //pc端
                if ($order_source == Client_::pc) {
                    $data = self::getNativeCode($result, $order);
                }

                //h5(非微信环境)
                if ($order_source == Client_::h5) {
                    $redirect_url = request()->domain() . '/mobile/pages/user_order/user_order';
                    $redirect_url = urlencode($redirect_url);
                    $data = $result['mweb_url'] . '&redirect_url=' . $redirect_url;
                }
                return $data;
            } else {
                if (isset($result['return_code']) && $result['return_code'] == 'FAIL') {

                    throw new Exception($result['return_msg']);
                }
                if (isset($result['err_code_des'])) {

                    throw new Exception($result['err_code_des']);
                }
                throw new Exception('未知原因');
            }
        } catch (Exception $e) {
            self::$error = '支付失败:' . $e->getMessage();
            return false;
        }
    }

    /**
     * @notes NATIVE 支付二维码
     * @param $result
     * @param $order
     * @return string
     * <AUTHOR>
     * @date 2021/7/13 6:34 下午
     */
    public static function getNativeCode($result, $order)
    {

        $save_dir = 'uploads/pay_code/';
        $qr_src = md5($order['order_sn'] . mt_rand(10000, 99999)) . '.png';
        $code_url = ROOT_PATH . '/' . $save_dir . $qr_src;

        $qrCode = new QrCode();
        $qrCode->setText($result['code_url']);
        $qrCode->setSize(200);
        $qrCode->setWriterByName('png');
        !file_exists($save_dir) && mkdir($save_dir, 777, true);
        $qrCode->writeFile($code_url);

        //生成base64临时图片
        if ($fp = fopen($code_url, "rb", 0)) {
            $gambar = fread($fp, filesize($code_url));
            fclose($fp);
            $base64 = chunk_split(base64_encode($gambar));
            $base64 = 'data:image/png;base64,' . $base64;
        }
        //删除文件
        if (strstr($code_url, $save_dir)) {
            unlink($code_url);
        }

        return $base64;
    }


    /**
     * @notes 支付参数
     * @param $from
     * @param $order
     * @param $order_source
     * @param $auth
     * @param $notify_url
     * @return array
     * <AUTHOR>
     * @date 2021/7/13 6:34 下午
     */
    public static function getAttributes($from, $order, $order_source, $auth, $notify_url)
    {

        switch ($from) {

            case 'trade':
                $attributes = [
                    'trade_type' => 'JSAPI',
                    'body' => '商品总订单',
                    'total_fee' => $order['order_amount'] * 100, // 单位：分
                    'notify_url' => $notify_url,
                    'openid' => $auth['openid'] ?? '',
                    'attach' => 'trade'
                ];
                break;
            case 'order':
                $attributes = [
                    'trade_type' => 'JSAPI',
                    'body' => '商品子订单',
                    'total_fee' => $order['order_amount'] * 100, // 单位：分
                    'notify_url' => $notify_url,
                    'openid' => $auth['openid'] ?? '',
                    'attach' => 'order'
                ];
                break;
            case 'AdOrder':
                $attributes = [
                    'trade_type' => 'JSAPI',
                    'body' => '广告位购买订单',
                    'total_fee' => $order['order_amount'] * 100, // 单位：分
                    'notify_url' => $notify_url,
                    'openid' => $auth['openid'] ?? '',
                    'attach' => 'AdOrder'
                ];
                break;
            case 'recharge':
                $attributes = [
                    'trade_type' => 'JSAPI',
                    'body' => '充值',
                    'total_fee' => $order['order_amount'] * 100, // 单位：分
                    'notify_url' => $notify_url,
                    'openid' => $auth['openid'] ?? '',
                    'attach' => 'recharge'
                ];
                break;
            case 'bondcharge':
                $attributes = [
                    'trade_type' => 'JSAPI',
                    'body' => '商家保证金',
                    'total_fee' => $order['order_amount'] * 100, // 单位：分
                    'notify_url' => $notify_url,
                    'openid' => $auth['openid'] ?? '',
                    'attach' => 'bondcharge'
                ];
                break;
            case 'ruzhucharge':
                $attributes = [
                    'trade_type' => 'JSAPI',
                    'body' => '入驻费检验费',
                    'total_fee' => $order['amount'] * 100, // 单位：分
                    'notify_url' => $notify_url,
                    'openid' => $auth['openid'] ?? '',
                    'attach' => 'ruzhucharge'
                ];
                break;
            case 'rejcharge':
                $attributes = [
                    'trade_type' => 'JSAPI',
                    'body' => '充值集采购会员',
                    'total_fee' => $order['order_amount'] * 100, // 单位：分
                    'notify_url' => $notify_url,
                    'openid' => $auth['openid'] ?? '',
                    'attach' => 'rejcharge'
                ];
                break;
            case 'agent':
                $attributes = [
                    'trade_type' => 'JSAPI',
                    'body' => '招商顾问保证金',
                    'total_fee' => $order['amount'] * 100, // 单位：分
                    'notify_url' => $notify_url,
                    'openid' => $auth['openid'] ?? '',
                    'attach' => 'agent'
                ];
                break;
            case 'integral':
                $attributes = [
                    'trade_type' => 'JSAPI',
                    'body' => '积分商城',
                    'total_fee' => $order['order_amount'] * 100, // 单位：分
                    'notify_url' => $notify_url,
                    'openid' => $auth['openid'] ?? '',
                    'attach' => 'integral'
                ];
                break;
            case 'ShopTierUpgrade':
                $attributes = [
                    'trade_type' => 'JSAPI',
                    'body' => '商家等级升级',
                    'total_fee' => $order['order_amount'] * 100, // 单位：分
                    'notify_url' => $notify_url,
                    'openid' => $auth['openid'] ?? '',
                    'attach' => 'ruzhucharge'
                ];
                break;
            case 'PurchaserPackage':
                $attributes = [
                    'trade_type' => 'JSAPI',
                    'body' => '采购套餐购买',
                    'total_fee' => $order['order_amount'] * 100, // 单位：分
                    'notify_url' => $notify_url,
                    'openid' => $auth['openid'] ?? '',
                    'attach' => 'PurchaserPackage'
                ];
                break;
        }

        //app支付类型
        if ($order_source == Client_::android || $order_source == Client_::ios) {
            $attributes['trade_type'] = 'APP';
        }

        //NATIVE模式设置
        if ($order_source == Client_::pc) {
            $attributes['trade_type'] = 'NATIVE';
            $attributes['product_id'] = $order['order_sn'];
            $attributes['openid'] = "";
        }

        //h5支付类型
        if ($order_source == Client_::h5) {
            $attributes['trade_type'] = 'MWEB';
        }

        //修改微信统一下单,订单编号 -> 支付回调时截取前面的单号 18个
        //修改原因:回调时使用了不同的回调地址,导致跨客户端支付时(例如小程序,公众号)可能出现201,商户订单号重复错误
        if ($from == 'trade') {
            $attributes['out_trade_no'] = $order['t_sn'] . $attributes['trade_type'] . $order_source;
        } else {
            $attributes['out_trade_no'] = $order['order_sn'] . $attributes['trade_type'] . $order_source;
        }
        return $attributes;
    }


    /**
     * @notes 获取微信配置
     * @param $order
     * @param $order_source
     * @return array
     * <AUTHOR>
     * @date 2021/7/13 6:35 下午
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     * @throws Exception
     */
    public static function getWeChatConfig($order, $order_source, $post = [])
    {

        $pay_config = WeChatServer::getPayConfigBySource($order_source);


        if (!empty($post)) {
            if (isset($post['admin_id'])) {

                $where[] = ['id', '=', $post['admin_id']];
                $account = Db::name('shop_admin')->where($where)->value('account');
                $user_id = Db::name('user')->where('mobile', $account)->value('id');
                $auth = Db::name('user_auth')->where('user_id', $user_id)->find();
            } else {
                $where[] = ['user_id', '=', $order['user_id']];
                $where[] = ['client', '=', $order_source];
                $auth = Db::name('user_auth')->where($where)->find();
            }
        } else {
            $where[] = ['user_id', '=', $order['user_id']];
            $where[] = ['client', '=', $order_source];
            $auth = Db::name('user_auth')->where($where)->find();
        }

        $data = [
            'auth' => $auth,
            'config' => $pay_config['config'],
            'notify_url' => $pay_config['notify_url'],
            'order_source' => $order_source,
        ];
        return $data;
    }

    /**
     * @notes 添加订单日志表
     * @param $order_id
     * @param $user_id
     * @param $shop_id
     * @return array
     * <AUTHOR>
     * @date 2021/7/13 6:35 下午
     */
    public static function getOrderLogData($order_id, $user_id, $shop_id)
    {

        $order_log_data = [];
        $order_log_data['type'] = 0;
        $order_log_data['channel'] = 101;
        $order_log_data['order_id'] = $order_id;
        $order_log_data['handle_id'] = $user_id;
        $order_log_data['shop_id'] = $shop_id;
        $order_log_data['content'] = 105;
        $order_log_data['create_time'] = time();

        return $order_log_data;
    }

    /**
     * @notes 支付回调
     * @param $config
     * <AUTHOR>
     * @date 2021/7/13 6:35 下午
     * @throws \EasyWeChat\Kernel\Exceptions\Exception
     */
    public static function notify($config)
    {
        try {
            // 记录回调开始
            Db::name('log')->insert(['type' => 'wechat_notify_start', 'log' => '微信支付回调开始', 'creat_time' => date('Y-m-d H:i:s')]);

            $app = new Application($config);
            $response = $app->handlePaidNotify(function ($message, $fail) {
                // 记录回调消息
                Db::name('log')->insert(['type' => 'wechat_notify_message', 'log' => json_encode($message), 'creat_time' => date('Y-m-d H:i:s')]);
             
                //            // 用户是否支付成功
                if ($message['result_code'] === 'SUCCESS') {
                    // 记录完整回调数据
                    Db::name('log')->insert(['type' => 'callback_data', 'log' => json_encode($message), 'creat_time' => date('Y-m-d H:i:s')]);

                    $extra['transaction_id'] = $message['transaction_id'];

                    // 检查attach参数是否存在
                    if (isset($message['attach'])) {
                        $attach = $message['attach'];
                    } else {
                        // 如果没有attach参数，尝试从订单号判断类型
                        Db::name('log')->insert(['type' => 'attach_missing', 'log' => '回调数据缺少attach参数，尝试从订单号判断类型', 'creat_time' => date('Y-m-d H:i:s')]);

                        // 检查订单号是否包含JSAPI
                        if (strpos($message['out_trade_no'], 'JSAPI') !== false) {
                            // 提取JSAPI前面的部分作为真实订单号
                            $real_order_sn = substr($message['out_trade_no'], 0, strpos($message['out_trade_no'], 'JSAPI'));
                            Db::name('log')->insert(['type' => 'order_extract', 'log' => '从订单号提取的真实订单号：' . $real_order_sn, 'creat_time' => date('Y-m-d H:i:s')]);

                            // 尝试在agent_merchantfees表中查找订单
                            $agent_order = Db::name('agent_merchantfees')->where(['order_sn' => $real_order_sn])->find();
                            if ($agent_order) {
                                $attach = 'agent';
                                $message['out_trade_no'] = $real_order_sn;
                                Db::name('log')->insert(['type' => 'attach_detect', 'log' => '检测到代理订单，设置attach为agent', 'creat_time' => date('Y-m-d H:i:s')]);
                            } else {
                                // 如果找不到代理订单，尝试其他类型
                                // 这里可以添加其他类型的检查逻辑
                                $attach = 'unknown';
                                Db::name('log')->insert(['type' => 'attach_detect', 'log' => '无法确定订单类型，设置attach为unknown', 'creat_time' => date('Y-m-d H:i:s')]);
                            }
                        } else {
                            // 如果订单号不包含JSAPI，使用默认处理
                            $attach = 'unknown';
                            Db::name('log')->insert(['type' => 'attach_detect', 'log' => '订单号不包含JSAPI，设置attach为unknown', 'creat_time' => date('Y-m-d H:i:s')]);
                        }
                    }

                    // 记录原始订单号和attach值
                    Db::name('log')->insert(['type' => 'order_debug', 'log' => '原始订单号：' . $message['out_trade_no'] . '，attach值：' . $attach, 'creat_time' => date('Y-m-d H:i:s')]);
                    $original_order_no = $message['out_trade_no'];
                    $message['out_trade_no'] = mb_substr($message['out_trade_no'], 0, 18);
                    switch ($attach) {
                        case 'trade':
                            $order_trade = OrderTrade::where('t_sn', $message['out_trade_no'])->find();
                            $order = Order::where('trade_id', $order_trade['id'])->find();
                            if (!$order || $order['pay_status'] >= PayEnum::ISPAID) {
                                return true;
                            }
                            PayNotifyLogic::handle('trade', $message['out_trade_no'], $extra);
                            break;
                        case 'order':
                            $order = Db::name('order')->where(['order_sn' => $message['out_trade_no']])->find();
                            if (!$order || $order['pay_status'] >= PayEnum::ISPAID) {
                                return true;
                            }
                            PayNotifyLogic::handle('order', $message['out_trade_no'], $extra);
                            break;
                        case 'recharge':
                            $order = Db::name('recharge_order')->where(['order_sn' => $message['out_trade_no']])->find();
                            Db::name('log')->insert(['log' => json_encode($message), 'creat_time' => date('Y-m-d H:i:s')]);
                            if (!$order || $order['pay_status'] >= PayEnum::ISPAID) {
                                return true;
                            }
                            PayNotifyLogic::handle('recharge', $message['out_trade_no'], $extra);
                            break;

                        case 'AdOrder':
                            $order = Db::name('ad_order')->where(['order_sn' => $message['out_trade_no']])->find();
                            Db::name('log')->insert(['log' => json_encode($message), 'creat_time' => date('Y-m-d H:i:s')]);
                            if (!$order || $order['status'] >= PayEnum::ISPAID) {
                                return true;
                            }
                            PayNotifyLogic::handle('AdOrder', $message['out_trade_no'], $extra);
                            break;
                        case 'ruzhucharge':
                            $order = Db::name('shop_merchantfees')->where(['order_sn' => $message['out_trade_no']])->find();
                            Db::name('log')->insert(['type' => 'ruzhucharge', 'log' => json_encode($message), 'creat_time' => date('Y-m-d H:i:s')]);
                            if (!$order || $order['status'] >= PayEnum::ISPAID) {
                                return true;
                            }
                            PayNotifyLogic::handle('ruzhucharge', $message['out_trade_no'], $extra);
                            break;
                        case 'bondcharge':
                            $order = Db::name('shop_deposit')->where(['order_sn' => $message['out_trade_no']])->find();
                            Db::name('log')->insert(['type' => 'bondcharge', 'log' => json_encode($message), 'creat_time' => date('Y-m-d H:i:s')]);
                            if (!$order || $order['status'] >= PayEnum::ISPAID) {
                                return true;
                            }
                            PayNotifyLogic::handle('bondcharge', $message['out_trade_no'], $extra);
                            break;
                        case 'rejcharge':
                            $order = Db::name('jcai_order')->where(['order_sn' => $message['out_trade_no']])->find();
                            Db::name('log')->insert(['type' => 'rejcharge', 'log' => json_encode($message), 'creat_time' => date('Y-m-d H:i:s')]);
                            if (!$order || $order['pay_status'] >= PayEnum::ISPAID) {
                                return true;
                            }
                            PayNotifyLogic::handle('rejcharge', $message['out_trade_no'], $extra);
                            break;
                        case 'agent':
                            $order = Db::name('agent_merchantfees')->where(['order_sn' => $message['out_trade_no']])->find();
                            Db::name('log')->insert(['type' => 'agent', 'log' => json_encode($message), 'creat_time' => date('Y-m-d H:i:s')]);
                            if (!$order || $order['status'] >= PayEnum::ISPAID) {
                                return true;
                            }
                            PayNotifyLogic::handle('agent', $message['out_trade_no'], $extra);
                            break;
                        case 'agent_deposit_detail':
                            // 代理保证金明细支付回调
                            $detail = Db::name('agent_deposit_details')->where(['sn' => $message['out_trade_no']])->find();
                            Db::name('log')->insert(['type' => 'agent_deposit_detail', 'log' => json_encode($message), 'creat_time' => date('Y-m-d H:i:s')]);
                            if (!$detail || $detail['pay_status'] >= PayEnum::ISPAID) {
                                return true;
                            }
                            PayNotifyLogic::handle('agent_deposit_detail', $message['out_trade_no'], $extra);
                            break;
                        case 'integral':
                            // 积分商城订单
                            $order = IntegralOrder::where(['order_sn' => $message['out_trade_no']])->find();
                            if (!$order || $order['refund_status'] == IntegralOrderEnum::IS_REFUND) {
                                // 没有订单记录 或者 订单已发生退款 中断后续操作
                                return true;
                            }
                            if ($order['order_status'] == IntegralOrderEnum::ORDER_STATUS_DOWN) {
                                // 收到支付回调时，订单已被关闭, 则进行退款操作
                                IntegralOrderRefundLogic::refundOrderAmount($order['id']);
                                return true;
                            }
                            if ($order['pay_status'] >= PayEnum::ISPAID) {
                                return true;
                            }
                            PayNotifyLogic::handle('integral', $message['out_trade_no'], $extra);
                            break;
                        case 'PurchaserPackage':
                            // 采购套餐订单
                            $order = Db::name('purchaser_package_order')->where(['order_sn' => $message['out_trade_no']])->find();
                            Db::name('log')->insert(['type' => 'PurchaserPackage', 'log' => json_encode($message), 'creat_time' => date('Y-m-d H:i:s')]);
                            if (!$order || $order['pay_status'] >= PayEnum::ISPAID) {
                                return true;
                            }
                            PayNotifyLogic::handle('PurchaserPackage', $message['out_trade_no'], $extra);
                            break;
                        case 'unknown':
                            // 处理未知类型的支付回调
                            Db::name('log')->insert(['type' => 'unknown_payment', 'log' => '未知类型的支付回调，订单号：' . $message['out_trade_no'], 'creat_time' => date('Y-m-d H:i:s')]);

                            // 尝试在各个订单表中查找订单
                            $found = false;

                            // 检查agent_merchantfees表
                            $agent_order = Db::name('agent_merchantfees')->where('order_sn', 'like', '%' . $message['out_trade_no'] . '%')->find();
                            if ($agent_order) {
                                Db::name('log')->insert(['type' => 'order_found', 'log' => '在agent_merchantfees表中找到订单：' . json_encode($agent_order), 'creat_time' => date('Y-m-d H:i:s')]);
                                PayNotifyLogic::handle('agent', $agent_order['order_sn'], $extra);
                                $found = true;
                            }

                            // 检查shop_merchantfees表（商家等级升级和商家入驻费）
                            if (!$found) {
                                $shop_order = Db::name('shop_merchantfees')->where('order_sn', $message['out_trade_no'])->find();
                                if ($shop_order) {
                                    // 统一使用 ruzhucharge 回调处理商家入驻费和等级升级
                                    Db::name('log')->insert(['type' => 'order_found', 'log' => '在shop_merchantfees表中找到商家订单：' . json_encode($shop_order), 'creat_time' => date('Y-m-d H:i:s')]);
                                    PayNotifyLogic::handle('ruzhucharge', $shop_order['order_sn'], $extra);
                                    $found = true;
                                }
                            }

                            // 检查agent_deposit_details表
                            if (!$found) {
                                $agent_deposit_detail = Db::name('agent_deposit_details')->where('sn', 'like', '%' . $message['out_trade_no'] . '%')->find();
                                if ($agent_deposit_detail) {
                                    Db::name('log')->insert(['type' => 'order_found', 'log' => '在agent_deposit_details表中找到订单：' . json_encode($agent_deposit_detail), 'creat_time' => date('Y-m-d H:i:s')]);
                                    PayNotifyLogic::handle('agent_deposit_detail', $agent_deposit_detail['sn'], $extra);
                                    $found = true;
                                }
                            }

                            if (!$found) {
                                Db::name('log')->insert(['type' => 'order_not_found', 'log' => '在所有订单表中都未找到匹配的订单', 'creat_time' => date('Y-m-d H:i:s')]);
                            }
                            break;
                    }
                } elseif ($message['result_code'] === 'FAIL') {
                    // 用户支付失败
                    Db::name('log')->insert(['type' => 'payment_fail', 'log' => '支付失败，订单号：' . $message['out_trade_no'], 'creat_time' => date('Y-m-d H:i:s')]);
                }
                return true; // 返回处理完成
            });
            $response->send();
        } catch (\Exception $e) {
            // 记录详细的异常信息
            $error_info = [
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ];
            Db::name('log')->insert([
                'type' => 'payment_exception',
                'log' => json_encode($error_info),
                'creat_time' => date('Y-m-d H:i:s')
            ]);
        }
    }

    /*
     * 测试
     */
    public static function test()
    {
        $message = Db::name('log')->where(['id' => ['=', 367]])->value('log');
        $message = json_decode($message, true);
        //        $order = Db::name('agent_merchantfees')->where(['order_sn' => $message['out_trade_no']])->find();
        //        Db::name('log')->insert(['log'=>json_encode($message),'creat_time'=>date('Y-m-d H:i:s')]);
        //        if (!$order || $order['status'] >= PayEnum::ISPAID) {
        //            return true;
        //        }
        $message['out_trade_no'] = mb_substr($message['out_trade_no'], 0, 18);

        PayNotifyLogic::handle('rejcharge', $message['out_trade_no']);
    }

    /**
     * 测试代理支付回调
     *
     * @param string $order_sn 订单号
     * @return bool|string
     */
    public static function testAgentPayment($order_sn)
    {
        // 记录测试开始
        Db::name('log')->insert([
            'type' => 'test_agent_payment',
            'log' => "开始测试代理支付回调，订单号：{$order_sn}",
            'creat_time' => date('Y-m-d H:i:s')
        ]);

        // 查询订单信息
        $order = Db::name('agent_merchantfees')->where(['order_sn' => $order_sn])->find();
        if (!$order) {
            $error_msg = "订单不存在：{$order_sn}";
            Db::name('log')->insert([
                'type' => 'test_agent_payment_error',
                'log' => $error_msg,
                'creat_time' => date('Y-m-d H:i:s')
            ]);
            return $error_msg;
        }

        // 记录订单信息
        Db::name('log')->insert([
            'type' => 'test_agent_payment',
            'log' => "订单信息：" . json_encode($order),
            'creat_time' => date('Y-m-d H:i:s')
        ]);

        // 构造交易ID
        $transaction_id = 'TEST_' . date('YmdHis') . rand(1000, 9999);
        $extra = ['transaction_id' => $transaction_id];

        // 调用支付回调处理
        $result = PayNotifyLogic::handle('agent_merchantfees', $order_sn, $extra);

        // 记录处理结果
        Db::name('log')->insert([
            'type' => 'test_agent_payment',
            'log' => "处理结果：" . (is_string($result) ? $result : json_encode($result)),
            'creat_time' => date('Y-m-d H:i:s')
        ]);

        return $result;
    }
    /**
     * @notes 退款
     * @param $config
     * @param $data
     * @return array|\EasyWeChat\Kernel\Support\Collection|false|object|\Psr\Http\Message\ResponseInterface|string
     * <AUTHOR>
     * @date 2021/7/13 6:35 下午
     * @throws \EasyWeChat\Kernel\Exceptions\InvalidConfigException
     */
    public static function refund($config, $data)
    {
        if (!empty($data["transaction_id"])) {
            // 确保配置中包含必要的参数
            if (empty($config['app_id'])) {
                // 记录错误日志
                Db::name('log')->insert([
                    'type' => 'refund_error',
                    'log' => '退款配置缺少app_id参数',
                    'creat_time' => date('Y-m-d H:i:s')
                ]);
                return ['return_code' => 'FAIL', 'return_msg' => '退款配置缺少app_id参数'];
            }

            // 记录退款请求日志
            Db::name('log')->insert([
                'type' => 'refund_request',
                'log' => json_encode([
                    'config' => $config,
                    'data' => $data
                ]),
                'creat_time' => date('Y-m-d H:i:s')
            ]);

            try {
                $app = Factory::payment($config);
                $result = $app->refund->byTransactionId(
                    $data['transaction_id'],
                    $data['refund_sn'],
                    $data['total_fee'],
                    $data['refund_fee']
                );

                // 记录退款响应日志
                Db::name('log')->insert([
                    'type' => 'refund_response',
                    'log' => json_encode($result),
                    'creat_time' => date('Y-m-d H:i:s')
                ]);

                return $result;
            } catch (\Exception $e) {
                // 记录退款异常日志
                Db::name('log')->insert([
                    'type' => 'refund_exception',
                    'log' => $e->getMessage() . "\n" . $e->getTraceAsString(),
                    'creat_time' => date('Y-m-d H:i:s')
                ]);

                return ['return_code' => 'FAIL', 'return_msg' => $e->getMessage()];
            }
        } else {
            return ['return_code' => 'FAIL', 'return_msg' => '缺少交易号'];
        }
    }
}
