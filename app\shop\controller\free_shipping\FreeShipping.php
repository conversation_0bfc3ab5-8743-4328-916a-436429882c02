<?php


namespace app\shop\controller\free_shipping;

use app\common\basics\ShopBase;
use app\common\server\JsonServer;
use app\shop\logic\free_shipping\FreeShippingLogic;

class FreeShipping extends ShopBase
{
    public function index()
    {
        // 保存设置
        if ($this->request->isPost()) {
            $params = $this->request->post();
            $params['shop_id'] = $this->shop_id;
            $result = FreeShippingLogic::index($params);
            if ($result) {
                return JsonServer::success('保存成功');
            }
            return JsonServer::error(FreeShippingLogic::getError());
        }
        // 显示设置页
        $data = FreeShippingLogic::getData($this->shop_id);
        return view('', $data);
    }
}