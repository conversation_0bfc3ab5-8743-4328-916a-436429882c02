<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Cr\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * 短信模板信息
 *
 * @method string getTemplateId() 获取短信模板ID
 * @method void setTemplateId(string $TemplateId) 设置短信模板ID
 * @method string getTemplateName() 获取短信模板名称
 * @method void setTemplateName(string $TemplateName) 设置短信模板名称
 */
class SmsTemplate extends AbstractModel
{
    /**
     * @var string 短信模板ID
     */
    public $TemplateId;

    /**
     * @var string 短信模板名称
     */
    public $TemplateName;

    /**
     * @param string $TemplateId 短信模板ID
     * @param string $TemplateName 短信模板名称
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("TemplateId",$param) and $param["TemplateId"] !== null) {
            $this->TemplateId = $param["TemplateId"];
        }

        if (array_key_exists("TemplateName",$param) and $param["TemplateName"] !== null) {
            $this->TemplateName = $param["TemplateName"];
        }
    }
}
