(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-webview-webview"],{"1f43":function(t,n,e){"use strict";e.r(n);var u=e("74a6"),i=e("c891");for(var r in i)["default"].indexOf(r)<0&&function(t){e.d(n,t,(function(){return i[t]}))}(r);var a=e("f0c5"),c=Object(a["a"])(i["default"],u["b"],u["c"],!1,null,"7105ff4e",null,!1,u["a"],void 0);n["default"]=c.exports},"74a6":function(t,n,e){"use strict";e.d(n,"b",(function(){return u})),e.d(n,"c",(function(){return i})),e.d(n,"a",(function(){}));var u=function(){var t=this.$createElement,n=this._self._c||t;return n("v-uni-view",{staticClass:"page-body"},[n("v-uni-web-view",{attrs:{src:this.url}})],1)},i=[]},"8fcb":function(t,n,e){"use strict";e("7a82"),Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;n.default={data:function(){return{url:""}},onLoad:function(t){this.url=this.$Route.query.url},methods:{}}},c891:function(t,n,e){"use strict";e.r(n);var u=e("8fcb"),i=e.n(u);for(var r in u)["default"].indexOf(r)<0&&function(t){e.d(n,t,(function(){return u[t]}))}(r);n["default"]=i.a}}]);