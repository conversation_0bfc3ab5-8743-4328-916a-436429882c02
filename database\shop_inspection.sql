-- 创建商家实地检验记录表
CREATE TABLE `ls_shop_inspection` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `shop_id` int(11) NOT NULL COMMENT '商家ID',
  `staff_id` int(11) NOT NULL COMMENT '检验人员ID',
  `images` text COMMENT '图片列表，JSON格式存储',
  `videos` text COMMENT '视频列表，JSON格式存储',
  `description` text COMMENT '检验说明',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '检验状态：0-待审核；1-已通过；2-未通过',
  `reason` varchar(255) DEFAULT NULL COMMENT '未通过原因',
  `create_time` int(11) NOT NULL COMMENT '创建时间（上传时间）',
  `update_time` int(11) NOT NULL COMMENT '更新时间',
  `audit_time` int(11) DEFAULT NULL COMMENT '审核时间',
  `audit_user_id` int(11) DEFAULT NULL COMMENT '审核人ID',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除：0-否；1-是',
  PRIMARY KEY (`id`),
  KEY `idx_shop_id` (`shop_id`),
  KEY `idx_staff_id` (`staff_id`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商家实地检验记录表';
