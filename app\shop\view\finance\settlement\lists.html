{layout name="layout1" /}

<div class="wrapper">
    <div class="layui-card">
        <div class="layui-card-body">
        <!-- 结算汇总 -->
        <h2 >结算汇总</h2>
        <div style="margin: 0 20px">
            <div class="layui-row layui-col-space15">
                <div class="layui-col-sm6 layui-col-md3" >
                    <div class="layui-card" style="box-shadow:none;">
                        <div class="layui-card-header" style="border-bottom:0;">已结算成交订单数</div>
                        <div class="layui-card-body"><p>{$statistics.settleOrederNum}</p></div>
                    </div>
                </div>
                <div class="layui-col-sm6 layui-col-md3">
                    <div class="layui-card" style="box-shadow:none;">
                        <div class="layui-card-header" style="border-bottom:0;">已结算营业额</div>
                        <div class="layui-card-body"><p>￥{$statistics.settleOrederAmount}</p></div>
                    </div>
                </div>
                <div class="layui-col-sm6 layui-col-md3">
                    <div class="layui-card" style="box-shadow:none;">
                        <div class="layui-card-header" style="border-bottom:0;">待结算营业额</div>
                        <div class="layui-card-body"><p>￥{$statistics.settleOrederAmountWait}</p></div>
                    </div>
                </div>
                <div class="layui-col-sm6 layui-col-md3">
                    <div class="layui-card" style="box-shadow:none;">
                        <div class="layui-card-header" style="border-bottom:0;">已结算分销佣金金额</div>
                        <div class="layui-card-body"><p>￥{$statistics.settleDistributionAmount}</p></div>
                    </div>
                </div>
            </div>
            <div class="layui-row layui-col-space15">
                <div class="layui-col-sm6 layui-col-md3" >
                    <div class="layui-card" style="box-shadow:none;">
                        <div class="layui-card-header" style="border-bottom:0;">已结算入账金额</div>
                        <div class="layui-card-body"><p>￥{$statistics.settleWithdrawalAmount}</p></div>
                    </div>
                </div>
                <div class="layui-col-sm6 layui-col-md3">
                    <div class="layui-card" style="box-shadow:none;">
                        <div class="layui-card-header" style="border-bottom:0;">已结算交易服务费</div>
                        <div class="layui-card-body"><p>￥{$statistics.settlePoundageAmount}</p></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 结算管理 -->
        <h2 style="padding:20px;">结算管理</h2>
        <div class="layui-card-body layui-form">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">结算时间：</label>
                    <div class="layui-inline" style="margin-right:0;">
                        <div class="layui-input-inline" >
                            <input type="text" id="start_time" name="start_time" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">至</div>
                    <div class="layui-inline" style="margin-right:0;">
                        <div class="layui-input-inline" style="margin-right:0;">
                            <input type="text" id="end_time" name="end_time" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                </div>

                <div class="layui-inline">
                    <a class="layui-btn layui-btn-sm layui-btn-normal" lay-submit lay-filter="search">搜索</a>
                    <a class="layui-btn layui-btn-sm layui-btn-primary" lay-submit lay-filter="clear-search">重置</a>
                    <a class="layui-btn layui-btn-sm layui-btn-primary" lay-submit lay-filter="data-export">导出</a>
                </div>
            </div>
        </div>

        <!-- 主体区域 -->
            <button type="button" class="layui-btn layui-btn-normal layui-btn-sm layEvent" lay-event="add">对账结算</button>
            <table id="like-table-lists" lay-filter="like-table-lists"></table>
            <script type="text/html" id="table-operation">
                <a class="layui-btn layui-btn-sm layui-btn-primary" lay-event="detail">批次详情</a>
            </script>
        </div>
    </div>
</div>

<script>
    layui.use(["table", "form", "laydate"], function(){
        var table   = layui.table;
        var form    = layui.form;
        var laydate = layui.laydate;

        laydate.render({type:"datetime", elem:"#start_time", trigger:"click"});
        laydate.render({type:"datetime", elem:"#end_time", trigger:"click"});

        like.tableLists("#like-table-lists", "{:url()}", [
            {field:"id", width:60, title:"ID"}
            ,{field:"settle_sn", width:200, align:"center", title:"结算批次号", templet:"#table-storeInfo"}
            ,{field:"deal_order_count", width:160, align:"center",title:"已结算成交订单数"}
            ,{field:"business_money", width:160, align:"center", title:"已结算营业额"}
            ,{field:"refund_order_money", width:160, align:"center", title:"退款订单金额"}
            ,{field:"after_sales_money", width:160, align:"center", title:"售后退款金额"}
            ,{field:"distribution_money", width:160, align:"center", title:"已结算分销佣金金额"}
            ,{field:"entry_account_money", width:160, align:"center", title:"已结算入账金额"}
            ,{field:"create_time", width:160, align:"center", title:"结算时间"}
            ,{title:"操作", width:100, align:"center", fixed:"right", toolbar:"#table-operation"}
        ]);


        var active = {
            add: function() {
                layer.confirm('是否进行结算操作？', {}, function(){
                    like.ajax({
                        url: "{:url('finance.Settlement/add')}",
                        data: {},
                        type: "POST",
                        success:function(res) {
                            if(res.code === 1) {
                                layer.msg(res.msg);
                                table.reload("like-table-lists", {
                                    where: {},
                                    page: { cur: 1 }
                                });
                            }
                        }
                    });
                });
            },
            detail: function (obj) {
                layer.open({
                    type: 2
                    ,title: "批次详情"
                    ,content: "{:url('finance.Settlement/detail')}?settle_id="+obj.data.id
                    ,area: ["90%", "90%"]
                });
            }
        };
        like.eventClick(active);

        /**
         * 立即搜索
         */
        form.on("submit(search)", function(data){
            table.reload("like-table-lists", {
                where: data.field,
                page: {
                    curr: 1
                }
            });
        });

        /**
         * 重置搜索
         */
        form.on("submit(clear-search)", function(){
            $("#start_time").val("");
            $("#end_time").val("");
            table.reload("like-table-lists", {
                where: {},
                page: {
                    curr: 1
                }
            });
        });


        // 导出
        form.on('submit(data-export)', function (data) {
            var field = data.field;
            like.ajax({
                url: '{:url("finance.Settlement/export")}'
                , data: field
                , type: 'get'
                , success: function (res) {
                    if (res.code == 1) {
                        window.location.href = res.data.url;
                    }
                }
            });
        });

    })
</script>