exports.ids = [47,14];
exports.modules = {

/***/ 140:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(142);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(4).default
module.exports.__inject__ = function (context) {
  add("12a18d22", content, true, context)
};

/***/ }),

/***/ 141:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_sass_resources_loader_lib_loader_js_ref_7_oneOf_1_4_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_null_data_vue_vue_type_style_index_0_id_93598fb0_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(140);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_sass_resources_loader_lib_loader_js_ref_7_oneOf_1_4_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_null_data_vue_vue_type_style_index_0_id_93598fb0_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_sass_resources_loader_lib_loader_js_ref_7_oneOf_1_4_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_null_data_vue_vue_type_style_index_0_id_93598fb0_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_sass_resources_loader_lib_loader_js_ref_7_oneOf_1_4_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_null_data_vue_vue_type_style_index_0_id_93598fb0_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_sass_resources_loader_lib_loader_js_ref_7_oneOf_1_4_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_null_data_vue_vue_type_style_index_0_id_93598fb0_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 142:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(3);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".null-data[data-v-93598fb0]{padding:100px}.null-data .img-null[data-v-93598fb0]{width:150px;height:150px}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 143:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/null-data.vue?vue&type=template&id=93598fb0&scoped=true&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:"bg-white flex-col col-center null-data"},[_vm._ssrNode("<img"+(_vm._ssrAttr("src",_vm.img))+" alt class=\"img-null\""+(_vm._ssrStyle(null,_vm.imgStyle, null))+" data-v-93598fb0> <div class=\"muted mt8\" data-v-93598fb0>"+_vm._ssrEscape(_vm._s(_vm.text))+"</div>")])}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/null-data.vue?vue&type=template&id=93598fb0&scoped=true&

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/null-data.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
/* harmony default export */ var null_datavue_type_script_lang_js_ = ({
  components: {},
  props: {
    img: {
      type: String
    },
    text: {
      type: String,
      default: '暂无数据'
    },
    imgStyle: {
      type: String,
      default: ''
    }
  },
  methods: {}
});
// CONCATENATED MODULE: ./components/null-data.vue?vue&type=script&lang=js&
 /* harmony default export */ var components_null_datavue_type_script_lang_js_ = (null_datavue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(1);

// CONCATENATED MODULE: ./components/null-data.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(141)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  components_null_datavue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  "93598fb0",
  "728f99de"
  
)

/* harmony default export */ var null_data = __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 234:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(308);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(4).default
module.exports.__inject__ = function (context) {
  add("3973d46b", content, true, context)
};

/***/ }),

/***/ 306:
/***/ (function(module, exports, __webpack_require__) {

module.exports = __webpack_require__.p + "img/profit_null.05cb92f.png";

/***/ }),

/***/ 307:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_sass_resources_loader_lib_loader_js_ref_7_oneOf_1_4_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_collection_vue_vue_type_style_index_0_id_0ad89564_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(234);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_sass_resources_loader_lib_loader_js_ref_7_oneOf_1_4_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_collection_vue_vue_type_style_index_0_id_0ad89564_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_sass_resources_loader_lib_loader_js_ref_7_oneOf_1_4_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_collection_vue_vue_type_style_index_0_id_0ad89564_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_sass_resources_loader_lib_loader_js_ref_7_oneOf_1_4_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_collection_vue_vue_type_style_index_0_id_0ad89564_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_sass_resources_loader_lib_loader_js_ref_7_oneOf_1_4_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_collection_vue_vue_type_style_index_0_id_0ad89564_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 308:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(3);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".user-collection-container[data-v-0ad89564]{width:980px;padding:10px 10px 60px}.user-collection-container[data-v-0ad89564]  .el-tabs__header{margin-left:10px}.user-collection-container[data-v-0ad89564]  .el-tabs .el-tabs__nav-scroll{padding:0}.user-collection-container .user-collection-content .goods[data-v-0ad89564]{width:180px;height:260px;margin-right:15px;margin-bottom:30px;float:left}.user-collection-container .user-collection-content .goods-image[data-v-0ad89564]{width:180px;height:180px;cursor:pointer;position:relative}.user-collection-container .user-collection-content .goods-image:hover .goods-image-wrap[data-v-0ad89564]{opacity:1}.user-collection-container .user-collection-content .goods-image-wrap[data-v-0ad89564]{left:0;bottom:0;width:180px;height:26px;padding:8px 0;color:#fff;text-align:center;position:absolute;opacity:0;transition:opacity .2s linear;background-color:rgba(0,0,0,.2)}.user-collection-container .user-collection-content .goods-image-wrap>div[data-v-0ad89564]{width:90px;cursor:pointer}.user-collection-container .user-collection-content .goods-image-wrap>div[data-v-0ad89564]:first-child{border-right:1px solid #fff}.user-collection-container .user-collection-content .goods[data-v-0ad89564]:nth-child(5n){margin-right:0}.user-collection-container .user-collection-content .goods-name[data-v-0ad89564]{height:36px;color:#101010}.user-collection-container .user-collection-content .shop[data-v-0ad89564]{padding:20px 0;border-bottom:1px solid #e5e5e5}.user-collection-container .user-collection-content .shop .shop-item:hover .shop-wrap[data-v-0ad89564]{opacity:1}.user-collection-container .user-collection-content .shop .shop-item[data-v-0ad89564]{width:148px;height:220px;background-size:cover;background-position:50%;padding:10px;border-radius:6px;position:relative}.user-collection-container .user-collection-content .shop .shop-item .shop-wrap[data-v-0ad89564]{top:0;left:0;position:absolute;width:148px;height:26px;padding:8px 0;color:#fff;opacity:0;text-align:center;transition:opacity .2s linear;background-color:rgba(0,0,0,.2)}.user-collection-container .user-collection-content .shop .shop-item .shop-wrap>div[data-v-0ad89564]{width:74px;cursor:pointer}.user-collection-container .user-collection-content .shop .shop-item .shop-wrap>div[data-v-0ad89564]:first-child{border-right:1px solid #fff}.user-collection-container .user-collection-content .shop .shop-item .shop-info[data-v-0ad89564]{border-radius:6px;padding:18px 15px}.user-collection-container .user-collection-content .shop .shop-item .shop-info .logo[data-v-0ad89564]{width:70px;height:70px;border-radius:50%;margin-top:-45px}.user-collection-container .user-collection-content .shop[data-v-0ad89564]:last-child{border-bottom:0}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 358:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/user/collection.vue?vue&type=template&id=0ad89564&scoped=true&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:"user-collection-container"},[_vm._ssrNode("<div class=\"user-collection-content\" data-v-0ad89564>","</div>",[_c('el-tabs',{staticClass:"mt10",on:{"tab-click":_vm.handleClick},model:{value:(_vm.activeName),callback:function ($$v) {_vm.activeName=$$v},expression:"activeName"}},_vm._l((_vm.userCollection),function(item,index){return _c('el-tab-pane',{key:index,attrs:{"label":item.name,"name":item.type+''}},[(index == _vm.activeName)?_c('div',[(_vm.userCollection[_vm.activeName].lists.length)?[(_vm.activeName == 0)?_vm._l((item.lists),function(item2,index2){return _c('div',{key:index2,staticClass:"goods",on:{"click":function($event){$event.stopPropagation();return _vm.$router.push({
                                            path: '/goods_details/'+item2.id
                                        })}}},[_c('div',{staticClass:"goods-image"},[_c('el-image',{staticStyle:{"width":"180px","height":"180px"},attrs:{"src":item2.image,"fit":"fit"}}),_vm._v(" "),_c('div',{staticClass:"goods-image-wrap flex"},[_c('div',{on:{"click":function($event){$event.stopPropagation();return _vm.cancelColl(item2.id)}}},[_vm._v("取消收藏")]),_vm._v(" "),_c('div',{on:{"click":function($event){$event.stopPropagation();return _vm.$router.push({
                                            path: '/shop_street_detail',
                                            query: {
                                                id: item2.shop_id
                                            }
                                        })}}},[_vm._v("进入店铺")])])],1),_vm._v(" "),_c('div',{staticClass:"goods-name m-t-10"},[_c('div',{staticClass:"line-2"},[_vm._v(_vm._s(item2.name))])]),_vm._v(" "),_c('div',{staticClass:"m-t-14"},[_c('span',{staticClass:"primary xl"},[_vm._v("¥"+_vm._s(item2.min_price))])])])}):_vm._e(),_vm._v(" "),(_vm.activeName == 1)?_vm._l((item.lists),function(item2,index2){return _c('div',{key:index2,staticClass:"shop flex",staticStyle:{"width":"100%"}},[_c('div',{staticClass:"shop-item flex-col row-right",style:({
                                'background-image': ("url(" + (item2.cover) + ")"),
                            })},[_c('div',{staticClass:"shop-wrap xs flex"},[_c('div',{on:{"click":function($event){$event.stopPropagation();return _vm.cancelColl(item2.id)}}},[_vm._v("取消收藏")]),_vm._v(" "),_c('div',{on:{"click":function($event){$event.stopPropagation();return _vm.$router.push({
                                            path: '/shop_street_detail',
                                            query: {
                                                id: item2.shop_id
                                            }
                                        })}}},[_vm._v("进入店铺")])]),_vm._v(" "),_c('div',{staticClass:"bg-white shop-info text-center"},[_c('el-image',{staticClass:"logo",attrs:{"src":item2.logo}}),_vm._v(" "),_c('div',{staticClass:"m-t-12 line-1 lg"},[_vm._v("\n                                            "+_vm._s(item2.name)+"\n                                        ")])],1)]),_vm._v(" "),(item2.goods_list.length >= 1)?_c('div',{staticClass:"flex-1 m-l-20",staticStyle:{"width":"100%"}},[_c('div',{staticClass:"shop-title flex row-between"},[_c('span',[_vm._v("店铺推荐")]),_vm._v(" "),_c('div',{staticClass:"pointer",on:{"click":function($event){$event.stopPropagation();return _vm.$router.push({
                                            path: '/shop_street_detail',
                                            query: {
                                                id: item2.shop_id
                                            }
                                        })}}},[_vm._v("\n                                            进入店铺"),_c('i',{staticClass:"el-icon-arrow-right"})])]),_vm._v(" "),_c('div',{staticClass:"m-t-20 flex"},_vm._l((item2.goods_list),function(item3,index3){return _c('div',{key:index3,staticClass:"m-r-16",on:{"click":function($event){$event.stopPropagation();return _vm.$router.push({
                                            path: '/goods_details/'+item3.id
                                        })}}},[_c('el-image',{staticStyle:{"width":"150px","height":"150px"},attrs:{"src":item3.image,"fit":"fit"}}),_vm._v(" "),_c('div',{staticClass:"primary flex row-center m-t-10"},[_vm._v("\n                                                ¥"+_vm._s(item3.min_price)+"\n                                            ")])],1)}),0)]):_vm._e()])}):_vm._e()]:[_c('null-data',{attrs:{"img":__webpack_require__(306),"text":"暂无收藏~"}})]],2):_vm._e()])}),1)],1)])}
var staticRenderFns = []


// CONCATENATED MODULE: ./pages/user/collection.vue?vue&type=template&id=0ad89564&scoped=true&

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/user/collection.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
/* harmony default export */ var collectionvue_type_script_lang_js_ = ({
  head() {
    return {
      title: this.$store.getters.headTitle,
      link: [{
        rel: "icon",
        type: "image/x-icon",
        href: this.$store.getters.favicon
      }]
    };
  },

  layout: "user",

  data() {
    return {
      activeName: 0,
      recodeList: {},
      userCollection: [{
        type: 0,
        lists: [],
        name: "商品",
        count: 0,
        page: 1
      }, {
        type: 1,
        lists: [],
        name: "店铺",
        count: 0,
        page: 1
      }]
    };
  },

  fetch() {
    this.handleClick();
  },

  mounted() {
    this.getRecodeList();
  },

  methods: {
    handleClick() {
      this.getRecodeList();
    },

    changePage(val) {
      this.userCollection.some(item => {
        if (item.type == this.activeName) {
          item.page = val;
        }
      });
      this.getRecodeList();
    },

    async getRecodeList() {
      const {
        activeName,
        userCollection
      } = this;
      const item = userCollection.find(item => item.type == activeName);
      const {
        data: {
          lists,
          count
        },
        code
      } = activeName == 0 ? await this.$get("goods_collect/lists", {
        params: {
          page_size: 10,
          page_no: item.page
        }
      }) : await this.$get("pc/shopFollowList", {
        params: {
          page_size: 10,
          page_no: item.page
        }
      });

      if (code == 1) {
        this.recodeList = {
          lists,
          count
        };
      }
    },

    async cancelColl(id) {
      const {
        code,
        msg
      } = this.activeName == 0 ? await this.$post("goods_collect/changeStatus", {
        goods_id: id
      }) : await this.$post("shop_follow/changeStatus", {
        shop_id: id
      });

      if (code == 1) {
        this.$message.success("取消成功");
      }

      this.getRecodeList();
    }

  },
  watch: {
    recodeList: {
      immediate: true,

      handler(val) {
        console.log("val:", val);
        this.userCollection.some(item => {
          if (item.type == this.activeName) {
            Object.assign(item, val);
            return true;
          }
        });
      }

    }
  }
});
// CONCATENATED MODULE: ./pages/user/collection.vue?vue&type=script&lang=js&
 /* harmony default export */ var user_collectionvue_type_script_lang_js_ = (collectionvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(1);

// CONCATENATED MODULE: ./pages/user/collection.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(307)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  user_collectionvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  "0ad89564",
  "a89fb480"
  
)

/* harmony default export */ var collection = __webpack_exports__["default"] = (component.exports);

/* nuxt-component-imports */
installComponents(component, {NullData: __webpack_require__(143).default})


/***/ })

};;
//# sourceMappingURL=collection.js.map