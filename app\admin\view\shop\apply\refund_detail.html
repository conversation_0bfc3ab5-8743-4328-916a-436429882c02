{layout name="layout2" /}
<style>
    .layui-form-label{
        width: 120px;
    }
    .status-success {
        color: #5FB878;
        font-weight: bold;
    }
    .status-processing {
        color: #FFB800;
        font-weight: bold;
    }
    .status-failed {
        color: #FF5722;
        font-weight: bold;
    }
</style>
<div class="layui-form" lay-filter="refund_detail" id="layuiadmin-form-refund-detail" style="padding: 20px 30px 0 0;">
    <input type="hidden" value="{$refund.id}" name="refund_id">
    <div class="layui-fluid">
        <div class="layui-card">
            <div class="layui-card-header">退款详情</div>
            <div class="layui-card-body">
                <div class="layui-form-item">
                    <label class="layui-form-label">退款单号：</label>
                    <div class="layui-input-inline" style="width: 300px;">
                        <label class="layui-form-mid">{$refund.refund_sn}</label>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">商家名称：</label>
                    <div class="layui-input-inline">
                        <label class="layui-form-mid">{$refund.shop_name}</label>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">退款金额：</label>
                    <div class="layui-input-inline">
                        <label class="layui-form-mid">{$refund.refund_amount}</label>
                    </div>
                    <label class="layui-form-mid">元</label>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">原支付方式：</label>
                    <div class="layui-input-inline">
                        <label class="layui-form-mid">{$refund.payment_method}</label>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">退款状态：</label>
                    <div class="layui-input-inline">
                        {if $refund.refund_status == 0}
                        <label class="layui-form-mid status-processing">退款处理中</label>
                        {elseif $refund.refund_status == 1}
                        <label class="layui-form-mid status-success">退款成功</label>
                        {elseif $refund.refund_status == 2}
                        <label class="layui-form-mid status-failed">退款失败</label>
                        {/if}
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">退款时间：</label>
                    <div class="layui-input-inline" style="width: 300px;">
                        <label class="layui-form-mid">{$refund.created_at}</label>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">备注：</label>
                    <div class="layui-input-inline" style="width: 300px;">
                        <label class="layui-form-mid">{$refund.remark}</label>
                    </div>
                </div>
                
                {if $refund.refund_status == 2}
                <div class="layui-form-item">
                    <label class="layui-form-label">失败原因：</label>
                    <div class="layui-input-block" style="width: 400px;">
                        <pre style="background-color: #f2f2f2; padding: 10px; border-radius: 5px;">{$refund.refund_msg}</pre>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-input-block">
                        <button type="button" class="layui-btn layui-btn-danger" id="retry_refund">重新退款</button>
                    </div>
                </div>
                {/if}
            </div>
        </div>
    </div>
</div>
<script>
    layui.config({
        version:"{$front_version}",
        base: '/static/lib/' //静态资源所在路径
    }).use(['form'], function(){
        var $ = layui.$, form = layui.form;
        
        // 重新退款按钮点击事件
        $('#retry_refund').on('click', function() {
            layer.confirm('确定要重新发起退款吗？', {
                btn: ['确定', '取消']
            }, function(index) {
                // 发起重新退款请求
                like.ajax({
                    url: "{:url('shop.Apply/retryRefund')}",
                    data: {refund_id: $('input[name="refund_id"]').val()},
                    type: "POST",
                    success: function(res) {
                        if (res.code === 1) {
                            layer.msg(res.msg, {icon: 1});
                            setTimeout(function() {
                                // 刷新页面
                                window.location.reload();
                            }, 1500);
                        } else {
                            layer.msg(res.msg, {icon: 2});
                        }
                    }
                });
                layer.close(index);
            });
        });
    });
</script>
