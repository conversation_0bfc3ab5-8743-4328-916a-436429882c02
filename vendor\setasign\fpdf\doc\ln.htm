<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Ln</title>
<link type="text/css" rel="stylesheet" href="../fpdf.css">
</head>
<body>
<h1>Ln</h1>
<code>Ln([<b>float</b> h])</code>
<h2>Description</h2>
Performs a line break. The current abscissa goes back to the left margin and the ordinate
increases by the amount passed in parameter.
<h2>Parameters</h2>
<dl class="param">
<dt><code>h</code></dt>
<dd>
The height of the break.
<br>
By default, the value equals the height of the last printed cell.
</dd>
</dl>
<h2>See also</h2>
<a href="cell.htm">Cell</a>
<hr style="margin-top:1.5em">
<div style="text-align:center"><a href="index.htm">Index</a></div>
</body>
</html>
