<?php

namespace app\shopapi\logic;

use app\common\basics\Logic;
use app\common\enum\FileEnum;
use app\common\model\File;
use app\common\model\shop\ShopDeposit;
use app\common\server\ConfigServer;
use app\common\server\FileServer;
use app\common\server\UrlServer;
use app\common\server\storage\Driver as StorageDriver;
use think\Exception;
use think\facade\Log;
use think\facade\Db;

class ShopDepositLogic extends Logic
{
    /**
     * 上传保证金合同
     * @param array $post 包含shop_id和文件信息
     * @return bool
     */
    public static function uploadContract($post)
    {
        try {
            // 检查是否有shop_id
            if (empty($post['shop_id'])) {
                self::$error = '商家ID不能为空';
                return false;
            }

            // 上传文件，使用不限制文件类型的方法
            $save_path = 'uploads/deposit_contract/' . $post['shop_id'];

            try {
                // 获取上传文件
                $file = request()->file('file');
                if (empty($file)) {
                    self::$error = '未找到上传文件';
                    return false;
                }
                //限制文件类型只能是doc,pdf,jpg,png,gif,否则提示文件格式不正确
                $file_ext = $file->extension();
                if (!in_array($file_ext, ['doc','docx','png','jpg','gif','pdf'])) {
                    self::$error = '文件格式不正确';
                    return false;
                }
                // 使用不进行文件类型验证的方法上传
                $config = [
                    'default' => ConfigServer::get('storage', 'default', 'local'),
                    'engine'  => ConfigServer::get('storage_engine')
                ];

                if (empty($config['engine']['local'])) {
                    $config['engine']['local'] = [];
                }

                $StorageDriver = new StorageDriver($config);
                $StorageDriver->setUploadFileNoValidate('file');

                if (!$StorageDriver->upload($save_path)) {
                    self::$error = '上传失败: ' . $StorageDriver->getError();
                    return false;
                }

                // 文件上传路径
                $fileName = $StorageDriver->getFileName();
                // 文件信息
                $fileInfo = $StorageDriver->getFileInfo();

                // 信息
                $fileData = [
                    'name'        => $fileInfo['name'],
                    'type'        => FileEnum::OTHER_TYPE,
                    'uri'         => $save_path . '/' . str_replace("\\","/", $fileName),
                    'create_time' => time(),
                    'size'        => $fileInfo['size'],                   
                    'shop_id'     => $post['shop_id']
                ];

                File::insert($fileData);
            } catch (\Exception $e) {
                self::$error = '文件上传失败: ' . $e->getMessage();
                return false;
            }
            // 查找商家保证金记录
            $deposit = ShopDeposit::where('shop_id', $post['shop_id'])->findOrEmpty();

            if ($deposit->isEmpty()) {
                // 如果不存在保证金记录，创建一个新记录
                $data = [
                    'shop_id' => $post['shop_id'],
                    'order_sn' => createSn('shop_deposit', 'order_sn'),
                    'docs' => $fileData['uri'], // 使用docs字段存储合同文件路径
                    'status' => 0, // 待审核
                    'deposit_amount' => ConfigServer::get('shop_entry', 'depositAmount'), // 初始保证金金额为0
                    'payment_method' => '', // 支付方式暂时为空
                    'pay_status' => 0 // 未支付状态
                ];
                $deposit['id']=Db::name('shop_deposit')->insertGetId($data);
            } else {
                // 更新现有记录
                $updateData = [
                    'docs' => $fileData['uri'], // 使用docs字段存储合同文件路径
                    'status' => 0 // 重置为待审核状态
                ];
                ShopDeposit::update($updateData, ['id' => $deposit['id']]);
            }
            /*
             * 返回当前信息
            from: bondcharge
            order_id: 8
            pay_way: 1
            client: 5*/

            $data = [
                'order_id' => $deposit['id'],
                'pay_way' => 1,
                'amount' => ConfigServer::get('shop_entry', 'depositAmount'),
                'client' => 2
            ];
            return $data;
        } catch (Exception $e) {
            Log::error('Upload deposit contract error: ' . $e->getMessage());
            self::$error = $e->getMessage();
            return false;
        }
    }
}
