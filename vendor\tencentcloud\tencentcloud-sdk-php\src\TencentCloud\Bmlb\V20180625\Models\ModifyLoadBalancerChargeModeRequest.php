<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Bmlb\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * ModifyLoadBalancerChargeMode请求参数结构体
 *
 * @method string getLoadBalancerId() 获取负载均衡实例ID。
 * @method void setLoadBalancerId(string $LoadBalancerId) 设置负载均衡实例ID。
 * @method string getPayMode() 获取计费方式。flow或bandwidth。
 * @method void setPayMode(string $PayMode) 设置计费方式。flow或bandwidth。
 * @method array getListenerSet() 获取监听器信息，当计费方式选为 bandwidth 且此负载均衡实例下存在监听器时需填入此字段，可以自定义每个监听器带宽上限。
 * @method void setListenerSet(array $ListenerSet) 设置监听器信息，当计费方式选为 bandwidth 且此负载均衡实例下存在监听器时需填入此字段，可以自定义每个监听器带宽上限。
 */
class ModifyLoadBalancerChargeModeRequest extends AbstractModel
{
    /**
     * @var string 负载均衡实例ID。
     */
    public $LoadBalancerId;

    /**
     * @var string 计费方式。flow或bandwidth。
     */
    public $PayMode;

    /**
     * @var array 监听器信息，当计费方式选为 bandwidth 且此负载均衡实例下存在监听器时需填入此字段，可以自定义每个监听器带宽上限。
     */
    public $ListenerSet;

    /**
     * @param string $LoadBalancerId 负载均衡实例ID。
     * @param string $PayMode 计费方式。flow或bandwidth。
     * @param array $ListenerSet 监听器信息，当计费方式选为 bandwidth 且此负载均衡实例下存在监听器时需填入此字段，可以自定义每个监听器带宽上限。
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("LoadBalancerId",$param) and $param["LoadBalancerId"] !== null) {
            $this->LoadBalancerId = $param["LoadBalancerId"];
        }

        if (array_key_exists("PayMode",$param) and $param["PayMode"] !== null) {
            $this->PayMode = $param["PayMode"];
        }

        if (array_key_exists("ListenerSet",$param) and $param["ListenerSet"] !== null) {
            $this->ListenerSet = [];
            foreach ($param["ListenerSet"] as $key => $value){
                $obj = new ModifyLoadBalancerChargeModeListener();
                $obj->deserialize($value);
                array_push($this->ListenerSet, $obj);
            }
        }
    }
}
