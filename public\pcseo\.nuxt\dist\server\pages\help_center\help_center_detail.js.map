{"version": 3, "file": "pages/help_center/help_center_detail.js", "sources": ["webpack:///./pages/help_center/help_center_detail.vue?ee2f", "webpack:///./pages/help_center/help_center_detail.vue?ec6e", "webpack:///./pages/help_center/help_center_detail.vue?1085", "webpack:///./pages/help_center/help_center_detail.vue?c721", "webpack:///./pages/help_center/help_center_detail.vue", "webpack:///./pages/help_center/help_center_detail.vue?44de", "webpack:///./pages/help_center/help_center_detail.vue?9b0f"], "sourcesContent": ["// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../../node_modules/sass-resources-loader/lib/loader.js??ref--7-oneOf-1-4!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./help_center_detail.vue?vue&type=style&index=0&id=b24e7310&lang=scss&scoped=true&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"6c4f4ede\", content, true, context)\n};", "export * from \"-!../../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../../node_modules/sass-resources-loader/lib/loader.js??ref--7-oneOf-1-4!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./help_center_detail.vue?vue&type=style&index=0&id=b24e7310&lang=scss&scoped=true&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".news-details-container .nav-container[data-v-b24e7310]{padding:15px 16px}.news-details-container .content-box[data-v-b24e7310]{display:flex;flex-direction:row}.news-details-container .content-box .news-detail-box[data-v-b24e7310]{background-color:#fff;width:900px}.news-details-container .content-box .news-detail-box .content-header[data-v-b24e7310]{margin:0 20px;padding:20px 0;border-bottom:1px solid #e5e5e5}.news-details-container .content-box .news-detail-box .content-header .news-detail-title[data-v-b24e7310]{color:#222;font-size:24px;font-weight:500;margin-bottom:43px}.news-details-container .content-box .news-detail-box .content-html-box[data-v-b24e7310]{padding:24px 20px}.news-details-container .content-box .news-detail-box .content-html-box>div[data-v-b24e7310]{width:100%;overflow:hidden}.news-details-container .content-box .news-detail-box .content-html-box>div[data-v-b24e7310]  img{width:100%}.news-details-container .content-box .recommend-box[data-v-b24e7310]{width:264px}.news-details-container .content-box .recommend-box .recommend-box-header[data-v-b24e7310]{padding:15px 10px;border-bottom:1px solid #e5e5e5}.news-details-container .content-box .recommend-box .recommend-box-header .primary-line[data-v-b24e7310]{margin-right:10px;background-color:#ff2c3c;width:4px;height:20px}.news-details-container .content-box .recommend-box .recommend-box-content .recommend-list-container .recommend-list-item[data-v-b24e7310]{padding:10px;cursor:pointer}.news-details-container .content-box .recommend-box .recommend-box-content .recommend-list-container .recommend-list-item .goods-info[data-v-b24e7310]{margin-top:8px}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"news-details-container mt16\"},[_vm._ssrNode(\"<div class=\\\"nav-container flex\\\" data-v-b24e7310>\",\"</div>\",[_vm._ssrNode(\"<div class=\\\"nr\\\" style=\\\"width: 70px\\\" data-v-b24e7310>当前位置：</div> \"),_c('el-breadcrumb',{staticStyle:{\"flex\":\"1\"},attrs:{\"separator\":\"/\"}},[_c('el-breadcrumb-item',{attrs:{\"to\":{ path: '/' }}},[_vm._v(\"首页\")]),_vm._v(\" \"),_c('el-breadcrumb-item',[_c('nuxt-link',{attrs:{\"to\":\"/help_center\"}},[_vm._v(\"帮助中心\")])],1),_vm._v(\" \"),_c('el-breadcrumb-item',{staticClass:\"line1\",staticStyle:{\"max-width\":\"800px\"}},[_vm._v(_vm._s(_vm.detailsObj.title))])],1)],2),_vm._ssrNode(\" \"),_vm._ssrNode(\"<div class=\\\"content-box\\\" data-v-b24e7310>\",\"</div>\",[_vm._ssrNode(\"<div class=\\\"news-detail-box m-r-16\\\" data-v-b24e7310><div class=\\\"content-header bg-white\\\" data-v-b24e7310><div class=\\\"news-detail-title\\\" data-v-b24e7310>\"+_vm._ssrEscape(\"\\n                    \"+_vm._s(_vm.detailsObj.title)+\"\\n                \")+\"</div> <div class=\\\"flex\\\" data-v-b24e7310><div class=\\\"sm muted\\\" data-v-b24e7310>\"+_vm._ssrEscape(\"\\n                        发布时间：\"+_vm._s(_vm.detailsObj.create_time)+\"\\n                    \")+\"</div> <div class=\\\"flex\\\" style=\\\"margin-left: 40px\\\" data-v-b24e7310><i class=\\\"el-icon-view muted\\\" data-v-b24e7310></i> <div class=\\\"muted\\\" style=\\\"margin-left: 3px;\\\" data-v-b24e7310>\"+_vm._ssrEscape(_vm._s(_vm.detailsObj.visit)+\" 人浏览\")+\"</div></div></div></div> <div class=\\\"content-html-box bg-white\\\" data-v-b24e7310><div data-v-b24e7310>\"+(_vm._s(_vm.detailsObj.content))+\"</div></div></div> \"),_vm._ssrNode(\"<div class=\\\"recommend-box\\\" data-v-b24e7310>\",\"</div>\",[_vm._ssrNode(\"<div class=\\\"recommend-box-header flex bg-white\\\" data-v-b24e7310><div class=\\\"primary-line\\\" data-v-b24e7310></div> <div class=\\\"xxl\\\" style=\\\"font-weight: 500\\\" data-v-b24e7310>为您推荐</div></div> \"),_vm._ssrNode(\"<div class=\\\"recommend-box-content bg-white\\\" data-v-b24e7310>\",\"</div>\",[_vm._ssrNode(\"<div class=\\\"recommend-list-container\\\" data-v-b24e7310>\",\"</div>\",_vm._l((_vm.recommend_list),function(item){return _vm._ssrNode(\"<div class=\\\"recommend-list-item\\\" data-v-b24e7310>\",\"</div>\",[_c('el-image',{staticStyle:{\"width\":\"244px\",\"height\":\"183px\",\"border-radius\":\"6px\"},attrs:{\"fit\":\"cover\",\"src\":item.image}}),_vm._ssrNode(\" <div class=\\\"goods-info\\\" data-v-b24e7310><div class=\\\"line2 goods-name\\\" data-v-b24e7310>\"+_vm._ssrEscape(_vm._s(item.title))+\"</div> <div class=\\\"flex\\\" style=\\\"margin-top: 10px\\\" data-v-b24e7310><i class=\\\"el-icon-view muted\\\" data-v-b24e7310></i> <div class=\\\"muted xs\\\" style=\\\"margin-left: 4px;\\\" data-v-b24e7310>\"+_vm._ssrEscape(_vm._s(item.visit)+\" 人浏览\")+\"</div></div></div>\")],2)}),0)])],2)],2)],2)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nexport default {\n    head() {\n        return {\n            title: this.$store.getters.headTitle,\n            link: [{\n                rel: \"icon\",\n                type: \"image/x-icon\",\n                href: this.$store.getters.favicon\n            }],\n        };\n    },\n    async asyncData({\n        $get,\n        $post,\n        query\n    }) {\n        let detailsObj = {};\n        let recommend_list = [];\n        let res = await $get(\"help/detail\", {\n            params: {\n                id: query.id,\n                client: 2\n            }\n        });\n        if (res.code == 1) {\n            detailsObj = res.data;\n            recommend_list = res.data.recommend_list\n        }\n        return {\n            detailsObj,\n            recommend_list,\n        };\n    },\n    watchQuery: ['id'],\n    data() {\n        return {\n            recommend_list: []\n        }\n    },\n    mounted() {\n        console.log('route', this.$route)\n        // this.getNewsDetails(this.$route.query.id);\n    },\n    methods: {\n        async getNewsDetails(id) {\n            let res = await this.$get(\"help/detail\", {params: {id: id, client: 2}});\n            if(res.code == 1) {\n                this.detailsObj = res.data;\n                this.recommend_list = res.data.recommend_list\n            }\n        },\n        toOther(id) {\n            this.$router.push('/help_center/help_center_detail?id=' + id)\n        }\n    }\n}\n", "import mod from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./help_center_detail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./help_center_detail.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./help_center_detail.vue?vue&type=template&id=b24e7310&scoped=true&\"\nimport script from \"./help_center_detail.vue?vue&type=script&lang=js&\"\nexport * from \"./help_center_detail.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./help_center_detail.vue?vue&type=style&index=0&id=b24e7310&lang=scss&scoped=true&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  \"b24e7310\",\n  \"a41757ea\"\n  \n)\n\nexport default component.exports"], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACXA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACNA;AACA;AACA;;;;;ACFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAHA;AAFA;AAQA;AACA;AAAA;AACA;AACA;AACA;AAHA;AAKA;AACA;AACA;AACA;AACA;AACA;AAFA;AADA;AACA;AAKA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AAFA;AAIA;AACA;AAAA;AACA;AAAA;AACA;AACA;AADA;AAGA;AACA;AAAA;AACA;AAEA;AACA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAXA;AA3CA;;AC3DA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;A", "sourceRoot": ""}