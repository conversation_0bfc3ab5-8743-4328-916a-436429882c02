<?php

namespace app\admin\logic\content;

use app\common\basics\Logic;
use app\common\model\content\LearningCenter;
use Exception;

class LearningCenterLogic extends Logic
{
    /**
     * 获取学习中心列表
     * @param $get
     * @return array
     */
    public static function lists($get)
    {
        try {
            $where = [
                ['del', '=', 0]
            ];

            if (!empty($get['title']) and $get['title'])
                $where[] = ['title', 'like', '%'.$get['title'].'%'];

            if (!empty($get['category_id']) and is_numeric($get['category_id']))
                $where[] = ['category_id', '=', $get['category_id']];

            $model = new LearningCenter();
            $lists = $model->field(true)
                ->where($where)
                ->with(['category'])
                ->order('sort', 'asc')
                ->paginate([
                    'page'      => $get['page'],
                    'list_rows' => $get['limit'],
                    'var_page'  => 'page'
                ])
                ->toArray();

            foreach ($lists['data'] as &$item) {
                $item['category']  = $item['category']['name'] ?? '未知';
                $item['is_show']   = $item['is_show'] ? '显示' : '隐藏';
            }

            return ['count'=>$lists['total'], 'lists'=>$lists['data']];
        } catch (Exception $e) {
            return ['error'=>$e->getMessage()];
        }
    }

    /**
     * @Notes: 学习中心详细
     * @Author: 系统
     * @param $id
     * @return array
     */
    public static function detail($id)
    {
        $model = new LearningCenter();
        return $model->field(true)->findOrEmpty($id)->toArray();
    }

    /**
     * @Notes: 添加学习中心
     * @Author: 系统
     * @param $post
     * @return bool
     */
    public static function add($post)
    {
        try {
            LearningCenter::create([
                'category_id' => $post['category_id'],
                'title'       => $post['title'],
                'image'       => $post['image'] ?? '',
                'video'       => $post['video'] ?? '',
                'images'      => $post['images'] ?? [],
                'content'     => $post['content'] ?? '',
                'visit'       => 0,
                'likes'       => 0,
                'sort'        => $post['sort'] ?? 0,
                'is_show'     => $post['is_show']
            ]);

            return true;
        } catch (\Exception $e) {
            static::$error = $e->getMessage();
            return false;
        }
    }

    /**
     * @Notes: 编辑学习中心
     * @Author: 系统
     * @param $post
     * @return bool
     */
    public static function edit($post)
    {
        try {
            $model = new LearningCenter();
            $model->where(['id'=>$post['id']])->update([
                'category_id' => $post['category_id'],
                'title'       => $post['title'],
                'image'       => $post['image'] ?? '',
                'video'       => $post['video'] ?? '',
                'images'      => $post['images'] ?? [],
                'content'     => $post['content'] ?? '',
                'sort'        => $post['sort'] ?? 0,
                'is_show'     => $post['is_show']
            ]);

            return true;
        } catch (\Exception $e) {
            static::$error = $e->getMessage();
            return false;
        }
    }

    /**
     * @Notes: 删除学习中心
     * @Author: 系统
     * @param $id
     * @return bool
     */
    public static function del($id)
    {
        try {
            $model = new LearningCenter();
            $model->where(['id'=>$id])->update(['del'=>1]);
            return true;
        } catch (\Exception $e) {
            static::$error = $e->getMessage();
            return false;
        }
    }

    /**
     * @Notes: 设置学习中心状态
     * @Author: 系统
     * @param $post
     * @return bool
     */
    public static function setStatus($post)
    {
        try {
            $model = new LearningCenter();
            $model->where(['id'=>$post['id']])->update(['is_show'=>$post['status']]);
            return true;
        } catch (\Exception $e) {
            static::$error = $e->getMessage();
            return false;
        }
    }
}