<?php


namespace app\shopapi\logic;

use app\common\basics\Logic;
use app\common\model\shop\ShopAdmin;
use app\common\model\shop\ShopAdminAuth;
use app\common\model\ShopSession;
use app\common\model\ShopSession as SessionModel;
use app\common\server\UrlServer;
use app\common\server\WeChatServer;
use EasyWeChat\Factory;
use Exception;
use think\Db;
use think\facade\Config;
use think\facade\Cache;

/**
 * 商家移动端管理员登录逻辑
 * Class LoginLogic
 * @package app\shopapi\logic
 */
class LoginLogic extends Logic
{

    /**
     * @notes 账号密码登录
     * @param $params
     * @return mixed
     * @throws \think\Exception
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date 2021/11/9 16:37
     */
    public static function accountLogin($params)
    {
        $adminModel = new ShopAdmin();

        $admin = $adminModel->alias('a')
            ->join('shop s', 's.id = a.shop_id')
            ->field(['a.id', 'a.account', 'a.name', 'role_id', 'shop_id', 's.name' => 'shop_name', 's.logo' => 'shop_logo'])
            ->where(['a.account' => $params['account'], 'a.del' => 0])
            ->findOrEmpty()->toArray();

        $admin['shop_logo'] = UrlServer::getFileUrl($admin['shop_logo']);

        // 确保 client 参数存在
        $client = isset($params['client']) ? $params['client'] : 2; // 默认为H5
        $admin['token'] = self::createSession($admin['id'], $client);

        //登录信息更新
        $adminModel->where(['account' => $params['account']])->update([
            'login_ip' => request()->ip(),
            'login_time' => time()
        ]);
        return $admin;
    }



    /**
     * @notes 退出登录
     * @param $user_id
     * @param $client
     * @return SessionModel
     * <AUTHOR>
     * @date 2021/11/9 16:37
     */
    public static function logout($user_id, $client)
    {
        $time = time();
        $token = (new ShopSession())
            ->where(['admin_id' => $user_id, 'client' => $client])
            ->value('token');

        Cache::delete($token);

        return (new ShopSession())
            ->where(['admin_id' => $user_id, 'client' => $client])
            ->update(['update_time' => $time, 'expire_time' => $time]);
    }


    /**
     * @notes 创建会话
     * @param $admin_id
     * @param $client
     * @return string
     * <AUTHOR>
     * @date 2021/11/9 16:38
     */
    public static function createSession($admin_id, $client)
    {
        //清除之前缓存
        $token = SessionModel::where(['admin_id' => $admin_id, 'client' => $client])
            ->value('token');
        if ($token) {
            Cache::delete($token);
        }

        $result = SessionModel::where(['admin_id' => $admin_id, 'client' => $client])
            ->findOrEmpty();

        $time = time();
        $expire_time = $time + Config::get('project.token_expire_time');

        // 新token
        $token = md5($admin_id . $client . $time);

        $shop_amdin = ShopAdmin::where(['id' => $admin_id])->findOrEmpty();

        $data = [
            'shop_id' => $shop_amdin['shop_id'],
            'admin_id' => $admin_id,
            'token' => $token,
            'client' => $client,
            'update_time' => $time,
            'expire_time' => $expire_time,
        ];

        if ($result->isEmpty()) {
            SessionModel::create($data);
        } else {
            SessionModel::where(['admin_id' => $admin_id, 'client' => $client])
                ->update($data);
        }

        //更新登录信息
        $login_ip = request()->ip();
        ShopAdmin::where(['id' => $admin_id])
            ->update(['login_time' => $time, 'login_ip' => $login_ip]);

        // 获取最新的用户信息
        $admin_info = ShopAdmin::alias('a')
            ->join('shop_session s', 'a.id=s.admin_id')
            ->where(['s.token' => $token])
            ->field('a.*,s.token,s.client')
            ->find();
        $admin_info = $admin_info ? $admin_info->toArray() : [];

        //创建新的缓存
        $ttl = 0 + Config::get('project.token_expire_time');
        Cache::set($token, $admin_info, $ttl);

        return $token;
    }

    /**
     * 获取微信授权URL
     * @param $url
     * @param string $type 操作类型：login 或 bind
     * @return string
     */
    public static function codeUrl($url, $type = 'login')
    {
        // 处理URL中的hash部分

        $urlParts = explode('#', $url);
        $baseUrl = $urlParts[0];
        $hashPart = isset($urlParts[1]) ? $urlParts[1] : '';

        // 确保baseUrl不包含查询参数
        if (strpos($baseUrl, '?') !== false) {
            $urlComponents = parse_url($baseUrl);
            $baseUrl = $urlComponents['scheme'] . '://' . $urlComponents['host'];
            if (isset($urlComponents['path'])) {
                $baseUrl .= $urlComponents['path'];
            }
        }

        // 生成一个唯一的state
        $state = md5(time() . rand(10000, 99999));

        // 如果有hash部分，将其存储在缓存中，以便后续使用
        if (!empty($hashPart)) {
            Cache::set($state . '_hash', $hashPart, 600); // 缓存10分钟
        }
        // 将操作类型存入缓存，与 state 关联
        Cache::set($state . '_type', $type, 600); // 缓存10分钟

         $config = WeChatServer::getOaConfig();
         $app = Factory::officialAccount($config);

         // 直接使用EasyWeChat的方法构建授权URL
         $response = $app->oauth->scopes(['snsapi_userinfo'])->redirect($baseUrl);

         // 获取目标URL并手动替换state参数
         $targetUrl = $response->getTargetUrl();

         $targetUrl = preg_replace('/state=([^&]*)/', 'state=' . $state, $targetUrl);

         // 关键修改：将原始hash部分追加到URL末尾

         if (!empty($hashPart)) {
             $targetUrl .= '#' . $hashPart;
         }

         return $targetUrl;
     }

     /**
     * 微信公众号登录
     * @param $post
     * @return array|string
     * @throws \think\Exception
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
     public static function oaLogin($post)
     {
        try {
            // 记录请求参数
            trace('微信登录请求参数: ' . json_encode($post), 'info');

            // 微信调用
            $config = WeChatServer::getOaConfig();
            $app = Factory::officialAccount($config);
            $oauth = $app->oauth;

            // 获取 OAuth 授权结果用户信息
            try {
                // 记录请求参数
                trace('微信授权请求参数: ' . json_encode($post), 'info');

                // 检查code是否已被处理过
                $codeKey = 'wx_code_' . $post['code'];
                if (Cache::get($codeKey)) {
                    trace('微信授权码已被使用: ' . $post['code'], 'error');
                    throw new Exception('微信授权码已被使用，请重新授权');
                }

                // 标记code已被使用
                Cache::set($codeKey, true, 600); // 缓存10分钟

                // 先获取access_token
                $response = $oauth->getAccessToken($post['code']);
                trace('获取access_token响应: ' . json_encode($response), 'info');

                // 使用access_token获取用户信息
                $user = $oauth->user($response);
                $userOriginal = $user->getOriginal();
                trace('获取到的微信用户信息: ' . json_encode($userOriginal), 'info');

                // 检查微信配置
                $config = WeChatServer::getOaConfig();
                trace('微信公众号配置: ' . json_encode($config), 'info');

            } catch (Exception $e) {
                trace('获取微信用户信息异常: ' . $e->getMessage(), 'error');
                throw new Exception('获取微信用户信息失败: ' . $e->getMessage());
            }

            // 从缓存获取操作类型
            $type = Cache::get($post['state'] . '_type') ?? 'login';
            Cache::delete($post['state'] . '_type'); // 用完即删
            trace('操作类型: ' . $type, 'info');

            // 尝试获取 hash 并清理缓存
            $hash = Cache::get($post['state'] . '_hash');
            if ($hash) {
                Cache::delete($post['state'] . '_hash');
                trace('获取到hash: ' . $hash, 'info');
            }

            // 获取openid
            $openid = $user->getId();
            trace('openid: ' . $openid, 'info');

            // 尝试获取unionid
            $unionid = $userOriginal['unionid'] ?? '';


            // 根据操作类型处理
            if ($type === 'login') {
                // 登录逻辑
                trace('执行登录逻辑', 'info');

                // 查找是否有绑定了该openid的管理员账号
                $auth = ShopAdminAuth::where(['openid' => $openid])->findOrEmpty();

                if (!$auth->isEmpty()) {
                    // 找到绑定了该openid的管理员账号  关联店铺信息表shop

                    $admin = ShopAdmin::where(['id' => $auth['admin_id'], 'del' => 0])->findOrEmpty();

                    if (!$admin->isEmpty()) {
                        // 用户存在，创建会话并返回用户信息
                        trace('微信登录成功，admin_id: ' . $admin['id'], 'info');
                        $adminData = $admin->toArray();

                        // 确保 client 参数存在
                        $client = isset($post['client']) ? $post['client'] : 2; // 默认为H5
                        $adminData['token'] = self::createSession($admin['id'], $client);
//                        $adminData['shop_logo'] = $adminData['shop_logo']?UrlServer::getFileUrl($adminData['shop_logo']):'';
                        $adminData['status'] = 'success'; // 标记登录成功
                        if ($hash) {
                            $adminData['hash'] = $hash; // 将 hash 返回给前端
                        }
                        return $adminData;
                    }
                }

                // 用户不存在，返回需要绑定的提示和 openid
                trace('微信未绑定商家账号', 'info');
                return [
                    'status' => 'unbind',
                    'unionid' => $unionid,
                    'openid' => $openid,
                    'nickname' => $user->getNickname(),
                    'avatar' => $user->getAvatar()
                ];

            } elseif ($type === 'bind') {
                // 绑定逻辑
                trace('执行绑定逻辑', 'info');
                // 绑定操作需要用户已登录，获取当前登录用户ID
                $current_admin_id = $post['admin_id'] ?? 0; // 前端需要在调用绑定接口时传递当前登录的 admin_id
                if (!$current_admin_id) {
                    trace('绑定微信需要先登录账号', 'error');
                    throw new Exception('绑定微信需要先登录账号');
                }

                // 检查该 openid 是否已被其他账号绑定
                $existingAuth = ShopAdminAuth::where(['openid' => $openid])
                                         ->findOrEmpty();
                if (!$existingAuth->isEmpty() && $existingAuth['admin_id'] != $current_admin_id) {
                    trace('该微信已绑定其他账号', 'error');
                    throw new Exception('该微信已绑定其他账号');
                }

                // 更新当前用户的 openid
                trace('更新用户openid, admin_id: ' . $current_admin_id, 'info');
                ShopAdmin::where('id', $current_admin_id)->update(['oa_openid' => $openid]);

                // 重新生成 token，因为用户信息已更新
                $admin = ShopAdmin::find($current_admin_id);
                $adminData = $admin->toArray();

                // 确保 client 参数存在
                $client = isset($post['client']) ? $post['client'] : 2; // 默认为H5
                $adminData['token'] = self::createSession($admin['id'], $client);
                $adminData['shop_logo'] = $adminData['shop_logo']?UrlServer::getFileUrl($adminData['shop_logo']):'';
                $adminData['status'] = 'bind_success'; // 标记绑定成功
                if ($hash) {
                    $adminData['hash'] = $hash; // 将 hash 返回给前端
                }
                return $adminData;
            } else {
                trace('无效的操作类型: ' . $type, 'error');
                throw new Exception('无效的操作类型');
            }
        } catch (Exception $e) {
            trace('微信登录/绑定异常: ' . $e->getMessage(), 'error');
            return $e->getMessage();
        }
     }


     /**
     * 绑定微信
     * @param $admin_id
     * @param $post
     * @return bool|string
     */
     public static function bindWechat($admin_id, $post)
     {
         try {
             // 记录请求参数
             trace('绑定微信请求参数: admin_id=' . $admin_id . ', post=' . json_encode($post), 'info');

             if (empty($admin_id)) {
                 trace('绑定微信失败: 管理员ID为空', 'error');
                 return '管理员ID不能为空';
             }

             if (empty($post['code'])) {
                 trace('绑定微信失败: code参数为空', 'error');
                 return '微信授权code不能为空';
             }

             if (empty($post['state'])) {
                 trace('绑定微信失败: state参数为空', 'error');
                 return '微信授权state不能为空';
             }

             // 获取微信信息
             $config = WeChatServer::getOaConfig();
             $app = Factory::officialAccount($config);

             // 记录微信配置
             trace('绑定微信配置: ' . json_encode($config), 'info');

             try {
                 $response = $app
                     ->oauth
                     ->scopes(['snsapi_userinfo'])
                     ->getAccessToken($post['code']);

                 // 记录access_token响应
                 trace('绑定获取access_token响应: ' . json_encode($response), 'info');
             } catch (Exception $e) {
                 trace('获取access_token异常: ' . $e->getMessage(), 'error');
                 return '获取微信授权失败: ' . $e->getMessage();
             }

             if (!isset($response['openid']) || empty($response['openid'])) {
                 trace('获取openID失败: ' . json_encode($response), 'error');
                 throw new Exception('获取openID失败');
             }

             try {
                 $userObj = $app->oauth->user($response);
                 $user = $userObj->getOriginal();

                 // 记录用户信息
                 trace('绑定获取用户信息: ' . json_encode($user), 'info');

                 // 检查微信配置
                 trace('微信公众号配置: ' . json_encode($config), 'info');
             } catch (Exception $e) {
                 trace('获取用户信息异常: ' . $e->getMessage(), 'error');
                 return '获取微信用户信息失败: ' . $e->getMessage();
             }
             // 检查openid是否已被其他管理员绑定
             $exists = ShopAdminAuth::where(['openid' => $user['openid']])
                 ->where('admin_id', '<>', $admin_id) // 修正了这里的语法错误 '<->' 改为 '<>'
                 ->findOrEmpty();

             if (!$exists->isEmpty()) {
                 trace('该微信已被其他管理员绑定: openid=' . $user['openid'] . ', 已绑定admin_id=' . $exists['admin_id'], 'error');
                 return '该微信已被其他管理员绑定';
             }

             // 获取管理员信息
             $admin = ShopAdmin::where(['id' => $admin_id, 'del' => 0])->findOrEmpty();
             if ($admin->isEmpty()) {
                 trace('管理员不存在或已被删除: admin_id=' . $admin_id, 'error');
                 return '管理员不存在或已被删除';
             }

             // 更新或创建微信授权信息
             $auth = ShopAdminAuth::where(['admin_id' => $admin_id])
                 ->findOrEmpty();

             $time = time();
             $authData = [
                 'admin_id' => $admin_id,
                 'shop_id' => $admin['shop_id'],
                 'openid' => $user['openid'],
                 'update_time' => $time,
                 'client' => isset($post['client']) ? $post['client'] : 2, // 默认为H5
             ];

             // 添加unionid到授权数据（如果存在）
             if (isset($user['unionid']) && !empty($user['unionid'])) {
                 $authData['unionid'] = $user['unionid'];
                 trace('更新unionid: ' . $user['unionid'], 'info');
             }

             // 同时更新ShopAdmin表中的openid
             ShopAdmin::where(['id' => $admin_id])->update([
                 'oa_openid' => $user['openid']
             ]);
             trace('更新ShopAdmin表的openid: admin_id=' . $admin_id . ', openid=' . $user['openid'], 'info');

             if ($auth->isEmpty()) {
                 // 创建新的授权记录
                 $authData['create_time'] = $time;
                 $result = ShopAdminAuth::create($authData);
                 trace('创建新的授权记录: ' . json_encode($authData) . ', 结果: ' . json_encode($result), 'info');
             } else {
                 // 更新现有授权记录
                 $result = ShopAdminAuth::where(['id' => $auth['id']])->update($authData);
                 trace('更新现有授权记录: id=' . $auth['id'] . ', 数据: ' . json_encode($authData) . ', 结果: ' . $result, 'info');
             }

             trace('微信绑定成功: admin_id=' . $admin_id . ', openid=' . $user['openid'], 'info');
             return true;
         } catch (Exception $e) {
             trace('绑定微信异常: ' . $e->getMessage() . "\n" . $e->getTraceAsString(), 'error');
             return $e->getMessage();
         }
     }

     /**
     * 解绑微信
     * @param $admin_id
     * @return bool
     */
     public static function unbindWechat($admin_id)
     {
         return ShopAdminAuth::where(['admin_id' => $admin_id])->delete();
     }

     /**
     * 账号密码登录并绑定微信
     * @param $params
     * @return array|string
     */
     public static function bindAccountWithOpenid($params)
     {
         try {
             trace('账号密码登录并绑定微信: ' . json_encode($params), 'info');

             // 验证账号密码
             $adminModel = new ShopAdmin();
             $admin = $adminModel->alias('a')
                 ->join('shop s', 's.id = a.shop_id')
                 ->field(['a.id', 'a.account', 'a.name', 'a.password', 'a.salt', 'role_id', 'shop_id', 's.name' => 'shop_name', 's.logo' => 'shop_logo'])
                 ->where(['a.account' => $params['account'], 'a.del' => 0])
                 ->findOrEmpty();

             if ($admin->isEmpty()) {
                 trace('账号不存在: ' . $params['account'], 'error');
                 return '账号不存在';
             }

             // 验证密码
             $password = create_password($params['password'], $admin['salt']);
             if ($password !== $admin['password']) {
                 trace('密码错误', 'error');
                 return '密码错误';
             }

             // 检查该 openid 是否已被其他账号绑定
             $existingAuth = ShopAdminAuth::where(['openid' => $params['openid']])
                                     ->findOrEmpty();

             if (!$existingAuth->isEmpty() && $existingAuth['admin_id'] != $admin['id']) {
                 trace('该微信已绑定其他账号', 'error');
                 return '该微信已绑定其他账号';
             }

             // 更新当前用户的 openid
             trace('更新用户openid, admin_id: ' . $admin['id'], 'info');
             ShopAdmin::where('id', $admin['id'])->update([
                 'oa_openid' => $params['openid']
             ]);

             // 更新或创建微信授权信息
             $auth = ShopAdminAuth::where(['admin_id' => $admin['id']])
                 ->findOrEmpty();

             $time = time();
             $authData = [
                 'admin_id' => $admin['id'],
                 'shop_id' => $admin['shop_id'],
                 'openid' => $params['openid'],
                 'update_time' => $time,
                 'client' => isset($params['client']) ? $params['client'] : 2, // 默认为H5
             ];

             // 如果有unionid，也保存
             if (!empty($params['unionid'])) {
                 $authData['unionid'] = $params['unionid'];
             }

             if ($auth->isEmpty()) {
                 // 创建新的授权记录
                 $authData['create_time'] = $time;
                 ShopAdminAuth::create($authData);
             } else {
                 // 更新现有授权记录
                 ShopAdminAuth::where(['id' => $auth['id']])->update($authData);
             }

             // 生成登录信息
             $adminData = $admin->toArray();
             unset($adminData['password']);
             unset($adminData['salt']);

             // 确保 client 参数存在
             $client = isset($params['client']) ? $params['client'] : 2; // 默认为H5
             $adminData['token'] = self::createSession($admin['id'], $client);
             $adminData['shop_logo'] = UrlServer::getFileUrl($adminData['shop_logo']);
             $adminData['status'] = 'bind_success'; // 标记绑定成功

             trace('账号密码登录并绑定微信成功: ' . $admin['id'], 'info');
             return $adminData;

         } catch (Exception $e) {
             trace('账号密码登录并绑定微信异常: ' . $e->getMessage(), 'error');
             return $e->getMessage();
         }
     }

     /**
     * 获取用户信息
     * @param $admin_id
     * @return array
     */
     public static function getUser($admin_id)
     {
         // 获取管理员信息
         $admin = ShopAdmin::alias('a')
             ->join('ls_shop s', 's.id = a.shop_id')
             ->field(['a.id', 'a.account', 'a.name', 'a.role_id', 'a.shop_id', 's.name' => 'shop_name', 's.logo' => 'shop_logo'])
             ->where(['a.id' => $admin_id, 'a.del' => 0])
             ->findOrEmpty()->toArray();

         if (empty($admin)) {
             return [];
         }

         // 处理返回数据
         $admin['shop_logo'] = UrlServer::getFileUrl($admin['shop_logo']);

         // 检查是否绑定了微信
         $auth = ShopAdminAuth::where(['admin_id' => $admin_id])->findOrEmpty();
         $admin['is_bind_wechat'] = !$auth->isEmpty();

         return $admin;
     }
}


