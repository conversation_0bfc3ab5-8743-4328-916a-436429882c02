(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["bundle-pages-shop_search-shop_search"],{"0d4a":function(t,e,n){"use strict";n.r(e);var i=n("c537"),s=n.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);e["default"]=s.a},"359c":function(t,e,n){var i=n("fac9");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var s=n("4f06").default;s("740bb5d4",i,!0,{sourceMap:!1,shadowMode:!1})},"3cd5":function(t,e,n){"use strict";n.r(e);var i=n("6777"),s=n.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);e["default"]=s.a},"4c5b":function(t,e,n){"use strict";n.d(e,"b",(function(){return s})),n.d(e,"c",(function(){return o})),n.d(e,"a",(function(){return i}));var i={uNavbar:n("e131").default,uSearch:n("cef9").default,uSticky:n("6d4b").default,sortNav:n("da19").default,goodsList:n("2d92").default},s=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",[n("u-navbar",{attrs:{"border-bottom":!1,"back-bg":"rgba(255, 255, 255, 0.45)","is-fixed":!0}},[n("v-uni-view",{staticClass:"store-search flex-1"},[n("u-search",{attrs:{shape:"round",placeholder:"搜索店内商品","wrap-bg-color":"transparent"},on:{search:function(e){arguments[0]=e=t.$handleEvent(e),t.refresh.apply(void 0,arguments)}},model:{value:t.keyword,callback:function(e){t.keyword=e},expression:"keyword"}})],1)],1),n("v-uni-view",{staticClass:"goods-display flex bg-body"},[n("v-uni-view",{staticClass:"category-row flex-1"},[n("u-sticky",{attrs:{"bg-color":"rgba(255, 255, 255, 0)",enable:t.enableFix,"offset-top":t.navHeight,"h5-nav-height":0}},[n("sort-nav",{attrs:{"show-type":!1},model:{value:t.sortConfig,callback:function(e){t.sortConfig=e},expression:"sortConfig"}})],1),n("mescroll-body",{ref:"mescrollRef",attrs:{height:t.meScrollH,up:t.upOption,down:{use:!1}},on:{init:function(e){arguments[0]=e=t.$handleEvent(e),t.mescrollInit.apply(void 0,arguments)},down:function(e){arguments[0]=e=t.$handleEvent(e),t.downCallback.apply(void 0,arguments)},up:function(e){arguments[0]=e=t.$handleEvent(e),t.upCallback.apply(void 0,arguments)}}},[n("v-uni-view",{staticClass:"bg-white"},[n("goods-list",{attrs:{list:t.goodsList}})],1)],1)],1)],1)],1)},o=[]},6004:function(t,e,n){"use strict";var i=n("359c"),s=n.n(i);s.a},6777:function(t,e,n){"use strict";n("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("a9e3");var i=uni.getSystemInfoSync(),s={},o={name:"u-navbar",props:{height:{type:[String,Number],default:""},backIconColor:{type:String,default:"#606266"},backBg:{type:String,default:"transparent"},backIconName:{type:String,default:"nav-back"},backIconSize:{type:[String,Number],default:"42"},backText:{type:String,default:""},backTextStyle:{type:Object,default:function(){return{color:"#606266"}}},title:{type:String,default:""},titleWidth:{type:[String,Number],default:"250"},titleColor:{type:String,default:"#606266"},titleBold:{type:Boolean,default:!1},titleSize:{type:[String,Number],default:32},isBack:{type:[Boolean,String],default:!0},background:{type:Object,default:function(){return{background:"#ffffff"}}},isFixed:{type:Boolean,default:!0},immersive:{type:Boolean,default:!1},borderBottom:{type:Boolean,default:!0},zIndex:{type:[String,Number],default:""},customBack:{type:Function,default:null}},data:function(){return{menuButtonInfo:s,statusBarHeight:i.statusBarHeight,isHome:!1}},computed:{navbarInnerStyle:function(){var t={};return t.height=this.navbarHeight+"px",t},navbarStyle:function(){var t={};return t.zIndex=this.zIndex?this.zIndex:this.$u.zIndex.navbar,Object.assign(t,this.background),t},titleStyle:function(){var t={};return t.left=(i.windowWidth-uni.upx2px(this.titleWidth))/2+"px",t.right=(i.windowWidth-uni.upx2px(this.titleWidth))/2+"px",t.width=uni.upx2px(this.titleWidth)+"px",t},navbarHeight:function(){return this.height?this.height:44}},created:function(){var t=getCurrentPages().length;1==t&&(this.isHome=!0)},methods:{goBack:function(){"function"===typeof this.customBack?this.customBack.bind(this.$u.$parent.call(this))():this.isHome?uni.switchTab({url:"/pages/index/index"}):uni.navigateBack()}}};e.default=o},"699c":function(t,e,n){"use strict";var i=n("f323"),s=n.n(i);s.a},"6d4b":function(t,e,n){"use strict";n.r(e);var i=n("f8be"),s=n("844e");for(var o in s)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return s[t]}))}(o);n("6004");var a=n("f0c5"),r=Object(a["a"])(s["default"],i["b"],i["c"],!1,null,"e9923850",null,!1,i["a"],void 0);e["default"]=r.exports},"844e":function(t,e,n){"use strict";n.r(e);var i=n("b1fc"),s=n.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);e["default"]=s.a},"85c6":function(t,e,n){"use strict";n.d(e,"b",(function(){return s})),n.d(e,"c",(function(){return o})),n.d(e,"a",(function(){return i}));var i={uIcon:n("90f3").default},s=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{},[n("v-uni-view",{staticClass:"u-navbar",class:{"u-navbar-fixed":t.isFixed,"u-border-bottom":t.borderBottom},style:[t.navbarStyle]},[n("v-uni-view",{staticClass:"u-status-bar",style:{height:t.statusBarHeight+"px"}}),n("v-uni-view",{staticClass:"u-navbar-inner",style:[t.navbarInnerStyle]},[t.isBack?n("v-uni-view",{staticClass:"u-back-wrap",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.goBack.apply(void 0,arguments)}}},[n("v-uni-view",{staticClass:"u-icon-wrap",style:{backgroundColor:t.backBg,borderRadius:"50%",padding:"8rpx"}},[n("u-icon",{attrs:{name:t.isHome?"home":t.backIconName,color:t.backIconColor,size:t.backIconSize}})],1),t.backText?n("v-uni-view",{staticClass:"u-icon-wrap u-back-text u-line-1",style:[t.backTextStyle]},[t._v(t._s(t.backText))]):t._e()],1):t._e(),t.title?n("v-uni-view",{staticClass:"u-navbar-content-title",style:[t.titleStyle]},[n("v-uni-view",{staticClass:"u-title u-line-1",style:{color:t.titleColor,fontSize:t.titleSize+"rpx",fontWeight:t.titleBold?"bold":"normal"}},[t._v(t._s(t.title))])],1):t._e(),n("v-uni-view",{staticClass:"u-slot-content"},[t._t("default")],2),n("v-uni-view",{staticClass:"u-slot-right"},[t._t("right")],2)],1)],1),t.isFixed&&!t.immersive?n("v-uni-view",{staticClass:"u-navbar-placeholder",style:{width:"100%",height:Number(t.navbarHeight)+t.statusBarHeight+"px"}}):t._e()],1)},o=[]},9446:function(t,e,n){var i=n("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.u-navbar[data-v-6d93ee5a]{width:100%}.u-navbar-fixed[data-v-6d93ee5a]{position:fixed;left:0;right:0;top:0;z-index:991}.u-status-bar[data-v-6d93ee5a]{width:100%}.u-navbar-inner[data-v-6d93ee5a]{width:100%;display:flex;flex-direction:row;justify-content:space-between;position:relative;align-items:center}.u-back-wrap[data-v-6d93ee5a]{display:flex;flex-direction:row;align-items:center;flex:1;flex-grow:0;padding:%?14?% %?14?% %?14?% %?24?%}.u-back-text[data-v-6d93ee5a]{padding-left:%?4?%;font-size:%?30?%}.u-navbar-content-title[data-v-6d93ee5a]{display:flex;flex-direction:row;align-items:center;justify-content:center;flex:1;position:absolute;left:0;right:0;height:%?60?%;text-align:center;flex-shrink:0}.u-navbar-centent-slot[data-v-6d93ee5a]{flex:1}.u-title[data-v-6d93ee5a]{line-height:%?60?%;font-size:%?32?%;flex:1}.u-navbar-right[data-v-6d93ee5a]{flex:1;display:flex;flex-direction:row;align-items:center;justify-content:flex-end}.u-slot-content[data-v-6d93ee5a]{flex:1;display:flex;flex-direction:row;align-items:center;position:relative}',""]),t.exports=e},b1fc:function(t,e,n){"use strict";n("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("a9e3"),n("2c3e"),n("e25e");var i={name:"u-sticky",props:{offsetTop:{type:[Number,String],default:0},index:{type:[Number,String],default:""},enable:{type:Boolean,default:!0},h5NavHeight:{type:[Number,String],default:44},bgColor:{type:String,default:"#ffffff"},zIndex:{type:[Number,String],default:""}},data:function(){return{fixed:!1,height:"auto",stickyTop:0,elClass:this.$u.guid(),left:0,width:"auto"}},watch:{offsetTop:function(t){this.initObserver()},enable:function(t){0==t?(this.fixed=!1,this.disconnectObserver("contentObserver")):this.initObserver()}},computed:{uZIndex:function(){return this.zIndex?this.zIndex:this.$u.zIndex.sticky}},mounted:function(){this.initObserver()},methods:{initObserver:function(){var t=this;if(this.enable){var e="string"==typeof this.offsetTop?parseInt(this.offsetTop):uni.upx2px(this.offsetTop);this.stickyTop=0!=this.offsetTop?e+this.h5NavHeight:this.h5NavHeight,this.disconnectObserver("contentObserver"),this.$nextTick((function(){t.$uGetRect("."+t.elClass).then((function(e){t.height=e.height,t.left=e.left,t.width=e.width,t.$nextTick((function(){t.observeContent()}))}))}))}},observeContent:function(){var t=this;this.disconnectObserver("contentObserver");var e=this.createIntersectionObserver({thresholds:[.95,.98,1]});e.relativeToViewport({top:-this.stickyTop}),e.observe("."+this.elClass,(function(e){t.enable&&t.setFixed(e.boundingClientRect.top)})),this.contentObserver=e},setFixed:function(t){var e=t<this.stickyTop;e?this.$emit("fixed",this.index):this.fixed&&this.$emit("unfixed",this.index),this.fixed=e},disconnectObserver:function(t){var e=this[t];e&&e.disconnect()}},beforeDestroy:function(){this.disconnectObserver("contentObserver")}};e.default=i},bde1:function(t,e,n){"use strict";n("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i={data:function(){return{mescroll:null}},onPullDownRefresh:function(){this.mescroll&&this.mescroll.onPullDownRefresh()},onPageScroll:function(t){this.mescroll&&this.mescroll.onPageScroll(t)},onReachBottom:function(){this.mescroll&&this.mescroll.onReachBottom()},methods:{mescrollInit:function(t){console.log(t),this.mescroll=t,this.mescrollInitByRef()},mescrollInitByRef:function(){if(!this.mescroll||!this.mescroll.resetUpScroll){var t=this.$refs.mescrollRef;t&&(this.mescroll=t.mescroll)}},downCallback:function(){var t=this;this.mescroll.optUp.use?this.mescroll.resetUpScroll():setTimeout((function(){t.mescroll.endSuccess()}),500)},upCallback:function(){var t=this;setTimeout((function(){t.mescroll.endErr()}),500)}},mounted:function(){this.mescrollInitByRef()}},s=i;e.default=s},c537:function(t,e,n){"use strict";n("7a82");var i=n("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("99af");var s=i(n("f3f3")),o=i(n("f07e")),a=i(n("c964")),r=n("26cb"),c=n("9953"),u=i(n("bde1")),l={mixins:[u.default],data:function(){return{id:"",is_recommend:"",keyword:"",sortConfig:{priceSort:"",saleSort:""},goodsList:[],enableFix:!0,upOption:{auto:!1,empty:{icon:"/static/images/goods_null.png",tip:"暂无商品"}},active:""}},onLoad:function(){var t=this;return(0,a.default)((0,o.default)().mark((function e(){return(0,o.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.id=t.$Route.query.id,t.is_recommend=t.$Route.query.is_recommend,console.log(t.id),t.mescroll.resetUpScroll();case 4:case"end":return e.stop()}}),e)})))()},onHide:function(){this.enableFix=!1},onShow:function(){this.enableFix=!0},methods:{refresh:function(){this.goodsList=[],this.mescroll.resetUpScroll()},upCallback:function(t){var e=this,n=(this.goodsList,this.keyword),i=this.sortConfig,s=i.priceSort,o=i.saleSort,a=t.num,r=t.size;(0,c.getGoodsList)({page_size:r,page_no:a,shop_id:this.id,is_recommend:1==this.is_recommend?1:"",sort_by_price:s,sort_by_sales:o,keyword:n}).then((function(n){var i=n.data,s=i.lists,o=s.length,a=!!i.more;1==t.num&&(e.goodsList=[]),e.goodsList=e.goodsList.concat(s),e.mescroll.endSuccess(o,a)}))}},computed:(0,s.default)((0,s.default)({},(0,r.mapGetters)(["sysInfo"])),{},{navHeight:function(){return this.sysInfo.navHeight+"px"}}),watch:{sortConfig:{deep:!0,handler:function(t){console.log(t),this.refresh()}}}};e.default=l},e131:function(t,e,n){"use strict";n.r(e);var i=n("85c6"),s=n("3cd5");for(var o in s)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return s[t]}))}(o);n("699c");var a=n("f0c5"),r=Object(a["a"])(s["default"],i["b"],i["c"],!1,null,"6d93ee5a",null,!1,i["a"],void 0);e["default"]=r.exports},f323:function(t,e,n){var i=n("9446");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var s=n("4f06").default;s("2ed29858",i,!0,{sourceMap:!1,shadowMode:!1})},f8be:function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return s})),n.d(e,"a",(function(){}));var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{},[n("v-uni-view",{staticClass:"u-sticky-wrap",class:[t.elClass],style:{height:t.fixed?t.height+"px":"auto",backgroundColor:t.bgColor}},[n("v-uni-view",{staticClass:"u-sticky",style:{position:t.fixed?"fixed":"static",top:t.stickyTop+"px",left:t.left+"px",width:"auto"==t.width?"auto":t.width+"px",zIndex:t.uZIndex}},[t._t("default")],2)],1)],1)},s=[]},fac9:function(t,e,n){var i=n("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.u-sticky[data-v-e9923850]{z-index:9999999999}',""]),t.exports=e},fcd2:function(t,e,n){"use strict";n.r(e);var i=n("4c5b"),s=n("0d4a");for(var o in s)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return s[t]}))}(o);var a=n("f0c5"),r=Object(a["a"])(s["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);e["default"]=r.exports}}]);