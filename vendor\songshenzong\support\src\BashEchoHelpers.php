<?php

use <PERSON><PERSON><PERSON>\Support\BashEcho;

if (!function_exists('echoRed')) {
    /**
     * @param $string
     */
    function echoRed($string)
    {
        BashEcho::echoRed($string);
    }
}

if (!function_exists('echoGreen')) {
    /**
     * @param $string
     */
    function echoGreen($string)
    {
        BashEcho::echoGreen($string);
    }
}

if (!function_exists('echoBrown')) {
    /**
     * @param $string
     */
    function echoBrown($string)
    {
        BashEcho::echoBrown($string);
    }
}

if (!function_exists('echoBlue')) {

    /**
     * @param $string
     */
    function echoBlue($string)
    {
        BashEcho::echoBlue($string);
    }
}

if (!function_exists('echoPurple')) {
    /**
     * @param $string
     */
    function echoPurple($string)
    {
        BashEcho::echoPurple($string);
    }
}

if (!function_exists('echoCyan')) {
    /**
     * @param $string
     */
    function echoCyan($string)
    {
        BashEcho::echoCyan($string);
    }
}
