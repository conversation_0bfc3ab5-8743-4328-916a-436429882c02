<?php


namespace app\shopapi\http\middleware;


use app\common\model\shop\ShopAdmin;
use app\common\model\user\User;
use app\common\model\Session;
use app\shopapi\validate\TokenValidate;
use think\exception\ValidateException;
use think\facade\Cache;
use think\facade\Db;

class Login
{
    /**
     * 登录验证
     * @param $request
     * @param \Closure $next
     * @return mixed|\think\response\Redirect
     */
    public function handle($request, \Closure $next)
    {
        //允许跨域调用
        header('Access-Control-Allow-Origin: *');
        header("Access-Control-Allow-Headers: Authorization, Sec-Fetch-Mode, DNT, X-Mx-ReqToken, Keep-Alive, User-Agent, If-Match, If-None-Match, If-Unmodified-Since, X-Requested-With, If-Modified-Since, Cache-Control, Content-Type, Accept-Language, Origin, Accept-Encoding,Access-Token,token");
        header('Access-Control-Allow-Methods: GET, POST, PATCH, PUT, DELETE');
        header('Access-Control-Max-Age: 1728000');
        header('Access-Control-Allow-Credentials:true');

        if (strtoupper($request->method()) == "OPTIONS") {
            return response();
        }

        // 过滤前后空格
        $request->filter(['trim']);

        $token = $request->header('token');

        // 无需登录
        if (empty($token) && $this->isNotNeedLogin($request)) {
            return $next($request);
        }


        //token验证，并生成缓存
        $validateError = '';
        try {
            validate(TokenValidate::class)->check(['token' => $token]);

            // 检查并清理被注销或冻结用户的token
            $this->checkAndClearInvalidUserTokens();

            $adminInfo = (new ShopAdmin())->alias('a')
                ->join('shop_session ss', 'a.id=ss.admin_id')
                ->join('shop s', 's.id = a.shop_id')
                ->where(['ss.token' => $token])
                ->field('a.*,ss.token,ss.client,s.name as shop_name')
                ->hidden(['password'])
                ->findOrEmpty();
            $adminInfo = $adminInfo ? $adminInfo->toArray() : [];
            // 设置缓存
            cache($token, $adminInfo);
            // 设置用户信息
            $request->admin_info = $adminInfo;
            return $next($request);
        } catch (ValidateException $e) {
            $validateError = $e->getError();
        }

        //无需要登录，带token的情况
        if ($this->isNotNeedLogin($request) && $token) {
            return $next($request);
        }

        //登录失败
        $result = array(
            'code' => -1,
            'show' => 1,
            'msg' => $validateError,
            'data' => []
        );
        return json($result);


    }


    /**
     * @notes 是否需要登录
     * @param $request
     * @return bool // false-需要; true-不需要
     * <AUTHOR>
     * @date 2021/11/10 11:10
     */
    private function isNotNeedLogin($request)
    {
        $controllerObj = invoke('\\app\\shopapi\\controller\\' . $request->controller());
        $data = $controllerObj->like_not_need_login;
        if (empty($data)) {
            return false;
        }
        return in_array($request->action(), $data);
    }

    /**
     * @notes 检查并清理被注销或冻结用户的token
     * @return void
     * <AUTHOR>
     * @date 2024/12/19
     */
    private function checkAndClearInvalidUserTokens()
    {
        try {
            // 查找所有被注销或冻结的用户
            $invalidUsers = User::where(function($query) {
                $query->where('user_delete', 1)  // 用户已注销
                      ->whereOr('disable', 1);    // 用户被冻结
            })->column('id');

            if (!empty($invalidUsers)) {
                // 获取这些用户的所有token
                $invalidTokens = Session::whereIn('user_id', $invalidUsers)->column('token');

                if (!empty($invalidTokens)) {
                    // 清除数据库中的session记录
                    Session::whereIn('user_id', $invalidUsers)->delete();

                    // 清除缓存中的token
                    foreach ($invalidTokens as $token) {
                        Cache::delete($token);
                    }

                    // 记录日志
                    trace('清理了 ' . count($invalidTokens) . ' 个无效用户的token', 'info');
                }
            }
        } catch (\Exception $e) {
            // 清理token失败不影响正常流程，记录错误日志即可
            trace('清理无效用户token失败: ' . $e->getMessage(), 'error');
        }
    }

}
