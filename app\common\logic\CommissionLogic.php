<?php

namespace app\common\logic;

use app\common\basics\Logic;
use app\common\server\ConfigServer;
use think\facade\Db;

/**
 * 佣金计算逻辑
 * Class CommissionLogic
 * @package app\common\logic
 */
class CommissionLogic extends Logic
{
    /**
     * 商品类型常量
     */
    const GOODS_TYPE_NORMAL = 1;           // 普通商品
    const GOODS_TYPE_JCAI = 2;             // 集采商品
    const GOODS_TYPE_JCAI_CROWDFUNDING = 3; // 集采众筹商品

    /**
     * 获取商品类型
     * @param int $goods_id 商品ID
     * @return int 商品类型
     */
    public static function getGoodsType($goods_id)
    {
        // 获取商品基本信息
        $goods = Db::name('goods')->field('join_jc')->where('id', $goods_id)->find();
        if (!$goods) {
            return self::GOODS_TYPE_NORMAL;
        }

        // 如果不是集采商品，直接返回普通商品
        if ($goods['join_jc'] != 1) {
            return self::GOODS_TYPE_NORMAL;
        }

        // 检查是否参与集采众筹活动
        $jcaiActivity = Db::name('jcai_activity')
            ->where([
                ['goods_id', '=', $goods_id],
                ['del', '=', 0],
                ['status', '=', 1],
                ['audit', '=', 1]
            ])
            ->find();

        if ($jcaiActivity) {
            return self::GOODS_TYPE_JCAI_CROWDFUNDING;
        }

        return self::GOODS_TYPE_JCAI;
    }

    /**
     * 获取佣金比例
     * @param int $goods_type 商品类型
     * @return float 佣金比例（百分比）
     */
    public static function getCommissionRatio($goods_type)
    {
        switch ($goods_type) {
            case self::GOODS_TYPE_NORMAL:
                return (float)ConfigServer::get('transaction', 'normal_goods_commission_ratio', 0);
            case self::GOODS_TYPE_JCAI:
                return (float)ConfigServer::get('transaction', 'jcai_goods_commission_ratio', 0);
            case self::GOODS_TYPE_JCAI_CROWDFUNDING:
                return (float)ConfigServer::get('transaction', 'jcai_crowdfunding_commission_ratio', 0);
            default:
                return 0;
        }
    }

    /**
     * 计算单个商品的佣金
     * @param int $goods_id 商品ID
     * @param float $goods_amount 商品金额
     * @return array 返回佣金信息
     */
    public static function calculateGoodsCommission($goods_id, $goods_amount)
    {
        $goods_type = self::getGoodsType($goods_id);
        $commission_ratio = self::getCommissionRatio($goods_type);
        $commission_amount = $goods_amount * ($commission_ratio / 100);

        return [
            'goods_type' => $goods_type,
            'commission_ratio' => $commission_ratio,
            'commission_amount' => $commission_amount,
            'merchant_amount' => $goods_amount - $commission_amount
        ];
    }

    /**
     * 计算订单佣金
     * @param int $order_id 订单ID
     * @param bool $consider_refund 是否考虑退款影响
     * @return array 返回订单佣金详情
     */
    public static function calculateOrderCommission($order_id, $consider_refund = true)
    {
        // 获取订单商品信息
        $orderGoods = Db::name('order_goods')
            ->field('id,goods_id,total_pay_price,refund_status')
            ->where('order_id', $order_id)
            ->select()
            ->toArray();

        $total_commission = 0;
        $total_merchant_amount = 0;
        $goods_commission_details = [];

        foreach ($orderGoods as $goods) {
            // 计算实际应收佣金的金额
            $actual_amount = $goods['total_pay_price'];

            // 如果考虑退款且商品已退款，则需要扣除退款金额
            if ($consider_refund && $goods['refund_status'] == 3) { // 3表示退款成功
                // 获取退款金额
                $refund_amount = self::getGoodsRefundAmount($goods['id']);
                $actual_amount = $goods['total_pay_price'] - $refund_amount;
                $actual_amount = max(0, $actual_amount); // 确保不为负数
            }

            $commission_info = self::calculateGoodsCommission($goods['goods_id'], $actual_amount);

            $total_commission += $commission_info['commission_amount'];
            $total_merchant_amount += $commission_info['merchant_amount'];

            $goods_commission_details[] = [
                'order_goods_id' => $goods['id'],
                'goods_id' => $goods['goods_id'],
                'goods_amount' => $goods['total_pay_price'],
                'actual_amount' => $actual_amount,
                'refund_status' => $goods['refund_status'],
                'goods_type' => $commission_info['goods_type'],
                'commission_ratio' => $commission_info['commission_ratio'],
                'commission_amount' => $commission_info['commission_amount'],
                'merchant_amount' => $commission_info['merchant_amount']
            ];
        }

        return [
            'order_id' => $order_id,
            'total_commission' => $total_commission,
            'total_merchant_amount' => $total_merchant_amount,
            'goods_details' => $goods_commission_details
        ];
    }

    /**
     * 更新订单商品的佣金比例
     * @param int $order_id 订单ID
     * @return bool
     */
    public static function updateOrderGoodsCommission($order_id)
    {
        try {
            $commission_info = self::calculateOrderCommission($order_id);
            
            foreach ($commission_info['goods_details'] as $detail) {
                Db::name('order_goods')
                    ->where('id', $detail['order_goods_id'])
                    ->update([
                        'commission_ratio' => $detail['commission_ratio']
                    ]);
            }
            
            return true;
        } catch (\Exception $e) {
            self::$error = $e->getMessage();
            return false;
        }
    }

    /**
     * 获取商品退款金额
     * @param int $order_goods_id 订单商品ID
     * @return float 退款金额
     */
    public static function getGoodsRefundAmount($order_goods_id)
    {
        // 查询该订单商品的退款金额
        $refund_amount = Db::name('after_sale')
            ->where([
                ['order_goods_id', '=', $order_goods_id],
                ['status', '=', 5], // 5表示退款成功
                ['del', '=', 0]
            ])
            ->sum('refund_price');

        return (float)$refund_amount;
    }

    /**
     * 计算退款对佣金的影响
     * @param int $order_id 订单ID
     * @param float $refund_amount 退款金额
     * @return array 返回佣金调整信息
     */
    public static function calculateRefundCommissionAdjustment($order_id, $refund_amount)
    {
        // 获取订单的原始佣金信息
        $original_commission = self::calculateOrderCommission($order_id, false);

        // 获取考虑退款后的佣金信息
        $adjusted_commission = self::calculateOrderCommission($order_id, true);

        $commission_adjustment = $original_commission['total_commission'] - $adjusted_commission['total_commission'];

        return [
            'order_id' => $order_id,
            'refund_amount' => $refund_amount,
            'original_commission' => $original_commission['total_commission'],
            'adjusted_commission' => $adjusted_commission['total_commission'],
            'commission_adjustment' => $commission_adjustment, // 需要退还给商家的佣金
            'original_merchant_amount' => $original_commission['total_merchant_amount'],
            'adjusted_merchant_amount' => $adjusted_commission['total_merchant_amount']
        ];
    }

    /**
     * 获取商品类型描述
     * @param int $goods_type 商品类型
     * @return string
     */
    public static function getGoodsTypeDesc($goods_type)
    {
        $desc = [
            self::GOODS_TYPE_NORMAL => '普通商品',
            self::GOODS_TYPE_JCAI => '集采商品',
            self::GOODS_TYPE_JCAI_CROWDFUNDING => '集采众筹商品'
        ];

        return $desc[$goods_type] ?? '未知类型';
    }
}
