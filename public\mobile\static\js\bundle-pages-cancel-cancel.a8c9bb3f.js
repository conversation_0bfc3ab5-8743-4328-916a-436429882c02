(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["bundle-pages-cancel-cancel"],{"0c5b":function(e,t,i){"use strict";var n=i("9a30"),a=i.n(n);a.a},"1d4d":function(e,t,i){"use strict";var n=i("f1ff"),a=i.n(n);a.a},"212e":function(e,t,i){var n=i("24fb");t=n(!1),t.push([e.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.u-loading-circle[data-v-bf7076f2]{display:inline-flex;vertical-align:middle;width:%?28?%;height:%?28?%;background:0 0;border-radius:50%;border:2px solid;border-color:#e5e5e5 #e5e5e5 #e5e5e5 #8f8d8e;-webkit-animation:u-circle-data-v-bf7076f2 1s linear infinite;animation:u-circle-data-v-bf7076f2 1s linear infinite}.u-loading-flower[data-v-bf7076f2]{width:20px;height:20px;display:inline-block;vertical-align:middle;-webkit-animation:a 1s steps(12) infinite;animation:u-flower-data-v-bf7076f2 1s steps(12) infinite;background:transparent url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMjAiIGhlaWdodD0iMTIwIiB2aWV3Qm94PSIwIDAgMTAwIDEwMCI+PHBhdGggZmlsbD0ibm9uZSIgZD0iTTAgMGgxMDB2MTAwSDB6Ii8+PHJlY3Qgd2lkdGg9IjciIGhlaWdodD0iMjAiIHg9IjQ2LjUiIHk9IjQwIiBmaWxsPSIjRTlFOUU5IiByeD0iNSIgcnk9IjUiIHRyYW5zZm9ybT0idHJhbnNsYXRlKDAgLTMwKSIvPjxyZWN0IHdpZHRoPSI3IiBoZWlnaHQ9IjIwIiB4PSI0Ni41IiB5PSI0MCIgZmlsbD0iIzk4OTY5NyIgcng9IjUiIHJ5PSI1IiB0cmFuc2Zvcm09InJvdGF0ZSgzMCAxMDUuOTggNjUpIi8+PHJlY3Qgd2lkdGg9IjciIGhlaWdodD0iMjAiIHg9IjQ2LjUiIHk9IjQwIiBmaWxsPSIjOUI5OTlBIiByeD0iNSIgcnk9IjUiIHRyYW5zZm9ybT0icm90YXRlKDYwIDc1Ljk4IDY1KSIvPjxyZWN0IHdpZHRoPSI3IiBoZWlnaHQ9IjIwIiB4PSI0Ni41IiB5PSI0MCIgZmlsbD0iI0EzQTFBMiIgcng9IjUiIHJ5PSI1IiB0cmFuc2Zvcm09InJvdGF0ZSg5MCA2NSA2NSkiLz48cmVjdCB3aWR0aD0iNyIgaGVpZ2h0PSIyMCIgeD0iNDYuNSIgeT0iNDAiIGZpbGw9IiNBQkE5QUEiIHJ4PSI1IiByeT0iNSIgdHJhbnNmb3JtPSJyb3RhdGUoMTIwIDU4LjY2IDY1KSIvPjxyZWN0IHdpZHRoPSI3IiBoZWlnaHQ9IjIwIiB4PSI0Ni41IiB5PSI0MCIgZmlsbD0iI0IyQjJCMiIgcng9IjUiIHJ5PSI1IiB0cmFuc2Zvcm09InJvdGF0ZSgxNTAgNTQuMDIgNjUpIi8+PHJlY3Qgd2lkdGg9IjciIGhlaWdodD0iMjAiIHg9IjQ2LjUiIHk9IjQwIiBmaWxsPSIjQkFCOEI5IiByeD0iNSIgcnk9IjUiIHRyYW5zZm9ybT0icm90YXRlKDE4MCA1MCA2NSkiLz48cmVjdCB3aWR0aD0iNyIgaGVpZ2h0PSIyMCIgeD0iNDYuNSIgeT0iNDAiIGZpbGw9IiNDMkMwQzEiIHJ4PSI1IiByeT0iNSIgdHJhbnNmb3JtPSJyb3RhdGUoLTE1MCA0NS45OCA2NSkiLz48cmVjdCB3aWR0aD0iNyIgaGVpZ2h0PSIyMCIgeD0iNDYuNSIgeT0iNDAiIGZpbGw9IiNDQkNCQ0IiIHJ4PSI1IiByeT0iNSIgdHJhbnNmb3JtPSJyb3RhdGUoLTEyMCA0MS4zNCA2NSkiLz48cmVjdCB3aWR0aD0iNyIgaGVpZ2h0PSIyMCIgeD0iNDYuNSIgeT0iNDAiIGZpbGw9IiNEMkQyRDIiIHJ4PSI1IiByeT0iNSIgdHJhbnNmb3JtPSJyb3RhdGUoLTkwIDM1IDY1KSIvPjxyZWN0IHdpZHRoPSI3IiBoZWlnaHQ9IjIwIiB4PSI0Ni41IiB5PSI0MCIgZmlsbD0iI0RBREFEQSIgcng9IjUiIHJ5PSI1IiB0cmFuc2Zvcm09InJvdGF0ZSgtNjAgMjQuMDIgNjUpIi8+PHJlY3Qgd2lkdGg9IjciIGhlaWdodD0iMjAiIHg9IjQ2LjUiIHk9IjQwIiBmaWxsPSIjRTJFMkUyIiByeD0iNSIgcnk9IjUiIHRyYW5zZm9ybT0icm90YXRlKC0zMCAtNS45OCA2NSkiLz48L3N2Zz4=) no-repeat;background-size:100%}@-webkit-keyframes u-flower-data-v-bf7076f2{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}@keyframes u-flower-data-v-bf7076f2{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}@-webkit-keyframes u-circle-data-v-bf7076f2{0%{-webkit-transform:rotate(0);transform:rotate(0)}100%{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}',""]),e.exports=t},3917:function(e,t,i){"use strict";i.r(t);var n=i("508a"),a=i.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){i.d(t,e,(function(){return n[e]}))}(o);t["default"]=a.a},"448d":function(e,t,i){var n=i("24fb");t=n(!1),t.push([e.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.u-checkbox[data-v-0b68f884]{display:inline-flex;align-items:center;overflow:hidden;-webkit-user-select:none;user-select:none;line-height:1.8}.u-checkbox__icon-wrap[data-v-0b68f884]{color:#606266;flex:none;display:-webkit-flex;display:flex;flex-direction:row;align-items:center;justify-content:center;box-sizing:border-box;width:%?42?%;height:%?42?%;color:transparent;text-align:center;transition-property:color,border-color,background-color;font-size:20px;border:1px solid #c8c9cc;transition-duration:.2s}.u-checkbox__icon-wrap--circle[data-v-0b68f884]{border-radius:100%}.u-checkbox__icon-wrap--square[data-v-0b68f884]{border-radius:%?6?%}.u-checkbox__icon-wrap--checked[data-v-0b68f884]{color:#fff;background-color:#ff2c3c;border-color:#ff2c3c}.u-checkbox__icon-wrap--disabled[data-v-0b68f884]{background-color:#ebedf0;border-color:#c8c9cc}.u-checkbox__icon-wrap--disabled--checked[data-v-0b68f884]{color:#c8c9cc!important}.u-checkbox__label[data-v-0b68f884]{word-wrap:break-word;margin-left:%?10?%;margin-right:%?24?%;color:#606266;font-size:%?30?%}.u-checkbox__label--disabled[data-v-0b68f884]{color:#c8c9cc}',""]),e.exports=t},"4db5":function(e,t,i){"use strict";i("7a82");var n=i("ee27").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,i("ac1f"),i("5319"),i("5b81");var a=n(i("f07e")),o=n(i("c964")),c=i("1524"),r=(n(i("dab8")),i("2eec")),s={data:function(){return{showPopup:!1,isAgree:!1,loading:!1,AgreemenName:""}},methods:{getPolicyAgreement:function(){var e=this;(0,r.getuserProto)().then((function(t){e.AgreemenName=t.name}))},handleCancel:function(){var e=this;return(0,o.default)((0,a.default)().mark((function t(){var i,n;return(0,a.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e.isAgree){t.next=2;break}return t.abrupt("return",e.$toast({title:"请先阅读并同意《用户注销协议》"}));case 2:return e.loading=!0,t.next=5,(0,c.apiuserDeletecheck)();case 5:if(i=t.sent,n=i.data,console.log(n.pass),1!==n.pass){t.next=12;break}return t.next=11,(0,c.apiuserDelete)();case 11:t.sent;case 12:e.$Router.replaceAll({path:"/bundle/pages/cancel_result/cancel_result",query:{pass:n.pass}});case 13:case"end":return t.stop()}}),t)})))()}},onLoad:function(){this.getPolicyAgreement()}};t.default=s},"508a":function(e,t,i){"use strict";i("7a82"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,i("a9e3"),i("14d9"),i("d81d");var n={name:"u-checkbox",props:{name:{type:[String,Number],default:""},shape:{type:String,default:""},value:{type:Boolean,default:!1},disabled:{type:[String,Boolean],default:""},labelDisabled:{type:[String,Boolean],default:""},activeColor:{type:String,default:""},iconSize:{type:[String,Number],default:""},labelSize:{type:[String,Number],default:""},size:{type:[String,Number],default:""}},data:function(){return{parentDisabled:!1,newParams:{}}},created:function(){this.parent=this.$u.$parent.call(this,"u-checkbox-group"),this.parent&&this.parent.children.push(this)},computed:{isDisabled:function(){return""!==this.disabled?this.disabled:!!this.parent&&this.parent.disabled},isLabelDisabled:function(){return""!==this.labelDisabled?this.labelDisabled:!!this.parent&&this.parent.labelDisabled},checkboxSize:function(){return this.size?this.size:this.parent?this.parent.size:34},checkboxIconSize:function(){return this.iconSize?this.iconSize:this.parent?this.parent.iconSize:20},elActiveColor:function(){return this.activeColor?this.activeColor:this.parent?this.parent.activeColor:"primary"},elShape:function(){return this.shape?this.shape:this.parent?this.parent.shape:"square"},iconStyle:function(){var e={};return this.elActiveColor&&this.value&&!this.isDisabled&&(e.borderColor=this.elActiveColor,e.backgroundColor=this.elActiveColor),e.width=this.$u.addUnit(this.checkboxSize),e.height=this.$u.addUnit(this.checkboxSize),e},iconColor:function(){return this.value?"#ffffff":"transparent"},iconClass:function(){var e=[];return e.push("u-checkbox__icon-wrap--"+this.elShape),1==this.value&&e.push("u-checkbox__icon-wrap--checked"),this.isDisabled&&e.push("u-checkbox__icon-wrap--disabled"),this.value&&this.isDisabled&&e.push("u-checkbox__icon-wrap--disabled--checked"),e.join(" ")},checkboxStyle:function(){var e={};return this.parent&&this.parent.width&&(e.width=this.parent.width,e.flex="0 0 ".concat(this.parent.width)),this.parent&&this.parent.wrap&&(e.width="100%",e.flex="0 0 100%"),e}},methods:{onClickLabel:function(){this.isLabelDisabled||this.isDisabled||this.setValue()},toggle:function(){this.isDisabled||this.setValue()},emitEvent:function(){var e=this;this.$emit("change",{value:!this.value,name:this.name}),setTimeout((function(){e.parent&&e.parent.emitEvent&&e.parent.emitEvent()}),80)},setValue:function(){var e=0;if(this.parent&&this.parent.children&&this.parent.children.map((function(t){t.value&&e++})),1==this.value)this.emitEvent(),this.$emit("input",!this.value);else{if(this.parent&&e>=this.parent.max)return this.$u.toast("最多可选".concat(this.parent.max,"项"));this.emitEvent(),this.$emit("input",!this.value)}}}};t.default=n},"5e52":function(e,t,i){"use strict";i.r(t);var n=i("7684"),a=i("66a0");for(var o in a)["default"].indexOf(o)<0&&function(e){i.d(t,e,(function(){return a[e]}))}(o);i("94ab");var c=i("f0c5"),r=Object(c["a"])(a["default"],n["b"],n["c"],!1,null,"217807ed",null,!1,n["a"],void 0);t["default"]=r.exports},"66a0":function(e,t,i){"use strict";i.r(t);var n=i("4db5"),a=i.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){i.d(t,e,(function(){return n[e]}))}(o);t["default"]=a.a},7684:function(e,t,i){"use strict";i.d(t,"b",(function(){return a})),i.d(t,"c",(function(){return o})),i.d(t,"a",(function(){return n}));var n={uPopup:i("5676").default,uIcon:i("6976").default,uCheckbox:i("e6a5").default,uLoading:i("c1c1").default},a=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("v-uni-view",[i("v-uni-view",{staticClass:"cancel"},[i("v-uni-view",{staticClass:"lg"},[e._v("为保证你的账号安全，在你提交的注销申请生效前，需同时满足以下条件：")]),i("v-uni-view",{staticClass:"lg m-t-40"},[i("v-uni-view",[e._v("1、账号处于安全状态")]),i("v-uni-view",{staticClass:"muted sm"},[e._v("账号当前为有效账号，非冻结状态。")])],1),i("v-uni-view",{staticClass:"lg m-t-40"},[i("v-uni-view",[e._v("2、账号内财产已结清，交易已完成")]),i("v-uni-view",{staticClass:"muted sm"},[e._v("账号下所有关联业务的资产及预期收益（包括现金、余额、佣金、积分、优惠券）均已结清、退款、清空或自愿放弃，所有交易已完成或自愿放弃。")])],1),i("v-uni-button",{staticClass:"btn",attrs:{type:"primary"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.showPopup=!0}}},[e._v("申请注销")])],1),i("u-popup",{attrs:{mode:"bottom","border-radius":"14",closeable:!1,"safe-area-inset-bottom":!0,"mask-close-able":!0},model:{value:e.showPopup,callback:function(t){e.showPopup=t},expression:"showPopup"}},[i("v-uni-view",{staticClass:"popup_header"},[i("v-uni-text",[e._v("提示")]),i("u-icon",{attrs:{name:"close",color:"#666666"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.showPopup=!1}}})],1),i("v-uni-view",{staticClass:"popup_body"},[i("v-uni-view",[e._v("注销后，"),i("v-uni-text",{staticClass:"tip"},[e._v("你的账号信息将永久清空无法恢复")])],1),i("v-uni-view",{staticClass:"popup_body_container"},[i("v-uni-view",[e._v("·你的个人相关数据将会被清空且无法恢复")]),i("v-uni-view",{staticClass:"m-t-10"},[e._v("·你账号内剩余的余额、资产也将无法恢复")])],1),i("v-uni-view",{staticClass:"m-t-30"},[i("u-checkbox",{attrs:{"active-color":"#ff2c3c",shape:"circle","label-disabled":!0},model:{value:e.isAgree,callback:function(t){e.isAgree=t},expression:"isAgree"}},[i("v-uni-view",{staticClass:"sm flex"},[e._v("我已阅读并同意"),i("router-link",{attrs:{"data-theme":"",to:"/bundle/pages/server_explan/server_explan?type=4"}},[i("v-uni-view",{staticClass:"agreement",style:{color:"#ff2c3c"}},[e._v("《 用户注销协议 》")])],1)],1)],1)],1),i("v-uni-view",{staticClass:"flex row-around m-t-30 btn-pop"},[i("v-uni-button",{staticClass:"btn_cancel",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.showPopup=!1}}},[e._v("取消")]),i("v-uni-button",{staticClass:"btn_comfirm",style:{"background-color":e.isAgree?" #ff2c3c":"#CFCFCF"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.handleCancel.apply(void 0,arguments)}}},[i("u-loading",{attrs:{mode:"circle",show:e.loading}}),i("v-uni-text",{staticClass:"m-l-10"},[e._v("确认注销")])],1)],1)],1)],1)],1)},o=[]},"8ad1":function(e,t,i){"use strict";i("7a82"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,i("a9e3");var n={name:"u-loading",props:{mode:{type:String,default:"circle"},color:{type:String,default:"#c7c7c7"},size:{type:[String,Number],default:"34"},show:{type:Boolean,default:!0}},computed:{cricleStyle:function(){var e={};return e.width=this.size+"rpx",e.height=this.size+"rpx","circle"==this.mode&&(e.borderColor="#e4e4e4 #e4e4e4 #e4e4e4 ".concat(this.color?this.color:"#c7c7c7")),e}}};t.default=n},9392:function(e,t,i){var n=i("24fb");t=n(!1),t.push([e.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */uni-page-body[data-v-217807ed]{height:100%;background-color:#fff}body.?%PAGE?%[data-v-217807ed]{background-color:#fff}uni-page-body .cancel[data-v-217807ed]{height:100%;padding:%?42?%}uni-page-body .btn[data-v-217807ed]{height:%?80?%;line-height:%?80?%;position:fixed;bottom:env(safe-area-inset-bottom);bottom:constant(safe-area-inset-bottom);left:%?42?%;right:%?42?%}uni-page-body .popup_header[data-v-217807ed]{display:flex;align-items:center;justify-content:space-between;height:%?100?%;padding:0 %?30?%;font-weight:500;font-size:%?26?%;background-color:#fff}uni-page-body .popup_body[data-v-217807ed]{padding:%?42?%;font-size:%?26?%}uni-page-body .popup_body .tip[data-v-217807ed]{font-weight:500}uni-page-body .popup_body_container[data-v-217807ed]{margin-top:%?30?%;background-color:#f7f7f7;padding:%?42?%}uni-page-body .popup_body .btn-pop[data-v-217807ed]{height:%?80?%;line-height:%?80?%}uni-page-body .popup_body .btn_comfirm[data-v-217807ed]{border-radius:%?50?%;width:%?250?%;height:%?80?%;line-height:%?80?%;color:#fff}uni-page-body .popup_body .btn_cancel[data-v-217807ed]{border-radius:%?50?%;width:%?250?%;height:%?80?%;line-height:%?80?%;border:1px solid #e6e6e6}',""]),e.exports=t},"94ab":function(e,t,i){"use strict";var n=i("e717"),a=i.n(n);a.a},"9a30":function(e,t,i){var n=i("448d");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[e.i,n,""]]),n.locals&&(e.exports=n.locals);var a=i("4f06").default;a("62f4fc91",n,!0,{sourceMap:!1,shadowMode:!1})},c1c1:function(e,t,i){"use strict";i.r(t);var n=i("cf72"),a=i("e50a");for(var o in a)["default"].indexOf(o)<0&&function(e){i.d(t,e,(function(){return a[e]}))}(o);i("1d4d");var c=i("f0c5"),r=Object(c["a"])(a["default"],n["b"],n["c"],!1,null,"bf7076f2",null,!1,n["a"],void 0);t["default"]=r.exports},cf72:function(e,t,i){"use strict";i.d(t,"b",(function(){return n})),i.d(t,"c",(function(){return a})),i.d(t,"a",(function(){}));var n=function(){var e=this.$createElement,t=this._self._c||e;return this.show?t("v-uni-view",{staticClass:"u-loading",class:"circle"==this.mode?"u-loading-circle":"u-loading-flower",style:[this.cricleStyle]}):this._e()},a=[]},e50a:function(e,t,i){"use strict";i.r(t);var n=i("8ad1"),a=i.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){i.d(t,e,(function(){return n[e]}))}(o);t["default"]=a.a},e6a5:function(e,t,i){"use strict";i.r(t);var n=i("ff7c"),a=i("3917");for(var o in a)["default"].indexOf(o)<0&&function(e){i.d(t,e,(function(){return a[e]}))}(o);i("0c5b");var c=i("f0c5"),r=Object(c["a"])(a["default"],n["b"],n["c"],!1,null,"0b68f884",null,!1,n["a"],void 0);t["default"]=r.exports},e717:function(e,t,i){var n=i("9392");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[e.i,n,""]]),n.locals&&(e.exports=n.locals);var a=i("4f06").default;a("44cca8aa",n,!0,{sourceMap:!1,shadowMode:!1})},f1ff:function(e,t,i){var n=i("212e");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[e.i,n,""]]),n.locals&&(e.exports=n.locals);var a=i("4f06").default;a("5e1ac5de",n,!0,{sourceMap:!1,shadowMode:!1})},ff7c:function(e,t,i){"use strict";i.d(t,"b",(function(){return a})),i.d(t,"c",(function(){return o})),i.d(t,"a",(function(){return n}));var n={uIcon:i("6976").default},a=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("v-uni-view",{staticClass:"u-checkbox",style:[e.checkboxStyle]},[i("v-uni-view",{staticClass:"u-checkbox__icon-wrap",class:[e.iconClass],style:[e.iconStyle],on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.toggle.apply(void 0,arguments)}}},[i("u-icon",{staticClass:"u-checkbox__icon-wrap__icon",attrs:{name:"checkbox-mark",size:e.checkboxIconSize,color:e.iconColor}})],1),i("v-uni-view",{staticClass:"u-checkbox__label",style:{fontSize:e.$u.addUnit(e.labelSize)},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.onClickLabel.apply(void 0,arguments)}}},[e._t("default")],2)],1)},o=[]}}]);