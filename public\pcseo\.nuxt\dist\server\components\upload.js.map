{"version": 3, "file": "components/upload.js", "sources": ["webpack:///./components/upload.vue?d4ec", "webpack:///./components/upload.vue?cda5", "webpack:///./components/upload.vue?8307", "webpack:///./components/upload.vue?42a2", "webpack:///./components/upload.vue", "webpack:///./components/upload.vue?2a5d", "webpack:///./components/upload.vue?5689"], "sourcesContent": ["// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/sass-resources-loader/lib/loader.js??ref--7-oneOf-1-4!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./upload.vue?vue&type=style&index=0&id=05db7967&lang=scss&scoped=true&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"05ffbf2f\", content, true, context)\n};", "export * from \"-!../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/sass-resources-loader/lib/loader.js??ref--7-oneOf-1-4!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./upload.vue?vue&type=style&index=0&id=05db7967&lang=scss&scoped=true&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".v-upload .el-upload--picture-card[data-v-05db7967]{width:76px;height:76px;line-height:76px}.v-upload .el-upload-list--picture-card .el-upload-list__item[data-v-05db7967]{width:76px;height:76px}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"v-upload\"},[_c('el-upload',{attrs:{\"list-type\":\"picture-card\",\"action\":_vm.url + '/api/file/formimage',\"limit\":_vm.limit,\"on-success\":_vm.success,\"on-error\":_vm.error,\"on-remove\":_vm.remove,\"on-change\":_vm.onChange,\"headers\":{ token: _vm.$store.state.token },\"auto-upload\":_vm.autoUpload}},[(_vm.isSlot)?_vm._t(\"default\"):_c('div',[_c('div',{staticClass:\"muted xs\"},[_vm._v(\"上传图片\")])])],2)],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport config from '~/config/app'\nexport default {\n    components: {},\n    props: {\n        limit: {\n            type: Number,\n            default: 1,\n        },\n        isSlot: {\n            type: Boolean,\n            default: false,\n        },\n        autoUpload: {\n            type: Boolean,\n            default: true,\n        },\n        onChange: {\n            type: Function,\n            default: () => {},\n        },\n    },\n    watch: {},\n    data() {\n        return {\n            url: config.baseUrl,\n        }\n    },\n    created() {},\n    computed: {},\n    methods: {\n        success(res, file, fileList) {\n            if (!this.autoUpload) {\n                return\n            }\n            this.$message({\n                message: '上传成功',\n                type: 'success',\n            })\n            this.$emit('success', fileList)\n        },\n        remove(file, fileList) {\n            this.$emit('remove', fileList)\n        },\n        error(res) {\n            this.$message({\n                message: '上传失败，请重新上传',\n                type: 'error',\n            })\n        },\n    },\n}\n", "import mod from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./upload.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./upload.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./upload.vue?vue&type=template&id=05db7967&scoped=true&\"\nimport script from \"./upload.vue?vue&type=script&lang=js&\"\nexport * from \"./upload.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./upload.vue?vue&type=style&index=0&id=05db7967&lang=scss&scoped=true&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  \"05db7967\",\n  \"388748c3\"\n  \n)\n\nexport default component.exports"], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACXA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACNA;AACA;AACA;;;;;;;;ACFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAbA;AAkBA;AACA;AAAA;AACA;AACA;AADA;AAGA;AACA;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAFA;AAIA;AACA;AApBA;AA5BA;;ACvBA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;A", "sourceRoot": ""}