<?php
namespace app\shopapi\logic;

use app\common\basics\Logic;
use think\facade\Db;

/**
 * 行业数据分析 - 逻辑
 * Class IndustryAnalysisLogic
 * @package app\shopapi\logic
 */
class IndustryAnalysisLogic extends Logic
{
    /**
     * @notes 获取行业概览数据
     * @param int $shop_id 商家ID
     * @param array $params 查询参数
     * @return array
     */
    public static function getIndustryOverview(int $shop_id, array $params = [])
    {
        $startDate = $params['start_date'] ?? date('Y-m-d', strtotime('-30 days'));
        $endDate = $params['end_date'] ?? date('Y-m-d');

        // 获取总体数据
        $overview = [
            'total_sales' => 0,
            'total_orders' => 0,
            'total_customers' => 0,
            'average_order_value' => 0,
        ];

        // 查询销售数据
        $salesData = Db::name('order')
            ->where('shop_id', '=', $shop_id)
            ->where('create_time', '>=', $startDate)
            ->where('create_time', '<=', $endDate)
            ->field([
                'COUNT(*) as total_orders',
                'COUNT(DISTINCT user_id) as total_customers',
                'SUM(order_amount) as total_sales'
            ])
            ->find();

        if ($salesData) {
            $overview['total_sales'] = round($salesData['total_sales'], 2);
            $overview['total_orders'] = $salesData['total_orders'];
            $overview['total_customers'] = $salesData['total_customers'];
            $overview['average_order_value'] = $salesData['total_orders'] > 0 
                ? round($salesData['total_sales'] / $salesData['total_orders'], 2) 
                : 0;
        }

        // 计算环比增长
        $previousStartDate = date('Y-m-d', strtotime($startDate . ' -30 days'));
        $previousEndDate = date('Y-m-d', strtotime($endDate . ' -30 days'));
        
        $previousData = Db::name('order')
            ->where('shop_id', '=', $shop_id)
            ->where('create_time', '>=', $previousStartDate)
            ->where('create_time', '<=', $previousEndDate)
            ->field([
                'COUNT(*) as total_orders',
                'COUNT(DISTINCT user_id) as total_customers',
                'SUM(order_amount) as total_sales'
            ])
            ->find();

        $overview['growth'] = [
            'sales' => self::calculateGrowth($previousData['total_sales'] ?? 0, $salesData['total_sales'] ?? 0),
            'orders' => self::calculateGrowth($previousData['total_orders'] ?? 0, $salesData['total_orders'] ?? 0),
            'customers' => self::calculateGrowth($previousData['total_customers'] ?? 0, $salesData['total_customers'] ?? 0)
        ];

        return [
            'overview' => $overview,
            'time_range' => [
                'start_date' => $startDate,
                'end_date' => $endDate
            ]
        ];
    }

    /**
     * @notes 获取销售趋势数据
     * @param int $shop_id 商家ID
     * @param array $params 查询参数
     * @return array
     */
    public static function getSalesTrend(int $shop_id, array $params = [])
    {
        $startDate = $params['start_date'] ?? date('Y-m-d', strtotime('-30 days'));
        $endDate = $params['end_date'] ?? date('Y-m-d');
        $groupBy = $params['group_by'] ?? 'day'; // day, week, month
         // 根据分组类型设置SQL日期格式
         $dateFormat = '%Y-%m-%d'; 
         switch ($groupBy) {
             case 'week':
                 $dateFormat = '%Y-%u';
                 break;
             case 'month':
                 $dateFormat = '%Y-%m';
                 break;
         }
         

              
        // 获取销售趋势数据
        $trends = Db::name('order')
            ->where('shop_id', '=', $shop_id)
            ->where('create_time', '>=', $startDate)
            ->where('create_time', '<=', $endDate)
            ->field([
                "DATE_FORMAT(create_time, '{$dateFormat}') as date",
                'COUNT(*) as order_count',
                'SUM(order_amount) as sales_amount',
                'COUNT(DISTINCT user_id) as customer_count'
            ])
            ->group('date')
            ->order('date ASC')
            ->select()
            ->toArray();

        return [
            'trends' => $trends,
            'time_range' => [
                'start_date' => $startDate,
                'end_date' => $endDate
            ],
            'group_by' => $groupBy
        ];
    }

    /**
     * @notes 获取品类分析数据
     * @param int $shop_id 商家ID
     * @param array $params 查询参数
     * @return array
     */
    public static function getCategoryAnalysis(int $shop_id, array $params = [])
    {
        $startDate = $params['start_date'] ?? date('Y-m-d', strtotime('-30 days'));
        $endDate = $params['end_date'] ?? date('Y-m-d');

        // 获取品类销售数据
        $categoryData = Db::name('order_goods')
            ->alias('og')
            ->join('order o', 'og.order_id = o.id')
            ->join('goods g', 'og.goods_id = g.id')
            ->join('goods_category gc', 'g.category_id = gc.id')
            ->where('o.shop_id', '=', $shop_id)
            ->where('o.create_time', '>=', $startDate)
            ->where('o.create_time', '<=', $endDate)
            ->field([
                'gc.id as category_id',
                'gc.name as category_name',
                'COUNT(DISTINCT o.id) as order_count',
                'SUM(og.total_price) as sales_amount',
                'COUNT(DISTINCT o.user_id) as customer_count',
                'SUM(og.quantity) as sales_volume'
            ])
            ->group('gc.id')
            ->order('sales_amount DESC')
            ->select()
            ->toArray();

        // 计算品类占比
        $totalSales = array_sum(array_column($categoryData, 'sales_amount'));
        foreach ($categoryData as &$category) {
            $category['sales_proportion'] = $totalSales > 0 
                ? round($category['sales_amount'] / $totalSales * 100, 2) 
                : 0;
        }

        return [
            'categories' => $categoryData,
            'time_range' => [
                'start_date' => $startDate,
                'end_date' => $endDate
            ]
        ];
    }

    /**
     * @notes 获取客户画像数据
     * @param int $shop_id 商家ID
     * @param array $params 查询参数
     * @return array
     */
    public static function getCustomerDemographics(int $shop_id, array $params = [])
    {
        $startDate = $params['start_date'] ?? date('Y-m-d', strtotime('-30 days'));
        $endDate = $params['end_date'] ?? date('Y-m-d');

        // 获取年龄分布
        $ageDistribution = Db::name('user_profile')
            ->alias('up')
            ->join('order o', 'up.user_id = o.user_id')
            ->where('o.shop_id', '=', $shop_id)
            ->where('o.create_time', '>=', $startDate)
            ->where('o.create_time', '<=', $endDate)
            ->field([
                'CASE 
                    WHEN age < 18 THEN "18岁以下"
                    WHEN age BETWEEN 18 AND 24 THEN "18-24岁"
                    WHEN age BETWEEN 25 AND 34 THEN "25-34岁"
                    WHEN age BETWEEN 35 AND 44 THEN "35-44岁"
                    WHEN age >= 45 THEN "45岁以上"
                END as age_group',
                'COUNT(DISTINCT up.user_id) as user_count'
            ])
            ->group('age_group')
            ->select()
            ->toArray();

        // 获取性别分布
        $genderDistribution = Db::name('user_profile')
            ->alias('up')
            ->join('order o', 'up.user_id = o.user_id')
            ->where('o.shop_id', '=', $shop_id)
            ->where('o.create_time', '>=', $startDate)
            ->where('o.create_time', '<=', $endDate)
            ->field([
                'up.gender',
                'COUNT(DISTINCT up.user_id) as user_count'
            ])
            ->group('up.gender')
            ->select()
            ->toArray();

        // 获取地域分布(省份维度)
        $regionDistribution = Db::name('user_profile')
            ->alias('up')
            ->join('order o', 'up.user_id = o.user_id')
            ->where('o.shop_id', '=', $shop_id)
            ->where('o.create_time', '>=', $startDate)
            ->where('o.create_time', '<=', $endDate)
            ->field([
                'up.province',
                'COUNT(DISTINCT up.user_id) as user_count'
            ])
            ->group('up.province')
            ->order('user_count DESC')
            ->limit(10)
            ->select()
            ->toArray();

        // 获取消费能力分布
        $spendingPowerDistribution = Db::name('order')
            ->where('shop_id', '=', $shop_id)
            ->where('create_time', '>=', $startDate)
            ->where('create_time', '<=', $endDate)
            ->field([
                'user_id',
                'SUM(order_amount) as total_amount'
            ])
            ->group('user_id')
            ->select()
            ->map(function ($item) {
                if ($item['total_amount'] < 100) {
                    return ['spending_level' => '低消费', 'amount' => $item['total_amount']];
                } elseif ($item['total_amount'] < 500) {
                    return ['spending_level' => '中等消费', 'amount' => $item['total_amount']];
                } else {
                    return ['spending_level' => '高消费', 'amount' => $item['total_amount']];
                }
            })
            ->group('spending_level')
            ->column('COUNT(*)', 'spending_level');

        return [
            'age_distribution' => $ageDistribution,
            'gender_distribution' => $genderDistribution,
            'region_distribution' => $regionDistribution,
            'spending_power_distribution' => $spendingPowerDistribution,
            'time_range' => [
                'start_date' => $startDate,
                'end_date' => $endDate
            ]
        ];
    }

    /**
     * @notes 获取热销商品排行
     * @param int $shop_id 商家ID
     * @param array $params 查询参数
     * @return array
     */
    public static function getTopSellingProducts(int $shop_id, array $params = [])
    {
        $startDate = $params['start_date'] ?? date('Y-m-d', strtotime('-30 days'));
        $endDate = $params['end_date'] ?? date('Y-m-d');
        $limit = $params['limit'] ?? 10;

        // 获取热销商品列表
        $topProducts = Db::name('order_goods')
            ->alias('og')
            ->join('order o', 'og.order_id = o.id')
            ->join('goods g', 'og.goods_id = g.id')
            ->where('o.shop_id', '=', $shop_id)
            ->where('o.create_time', '>=', $startDate)
            ->where('o.create_time', '<=', $endDate)
            ->field([
                'g.id as goods_id',
                'g.name as goods_name',
                'g.image as goods_image',
                'SUM(og.quantity) as total_sales_volume',
                'SUM(og.total_price) as total_sales_amount',
                'COUNT(DISTINCT o.user_id) as buyer_count'
            ])
            ->group('g.id')
            ->order('total_sales_amount DESC')
            ->limit($limit)
            ->select()
            ->toArray();

        // 计算商品转化率
        foreach ($topProducts as &$product) {
            $viewCount = Db::name('goods_view_log')
                ->where('goods_id', '=', $product['goods_id'])
                ->where('create_time', '>=', $startDate)
                ->where('create_time', '<=', $endDate)
                ->count('DISTINCT user_id');
                
            $product['conversion_rate'] = $viewCount > 0 
                ? round($product['buyer_count'] / $viewCount * 100, 2) 
                : 0;
        }

        return [
            'top_products' => $topProducts,
            'time_range' => [
                'start_date' => $startDate,
                'end_date' => $endDate
            ]
        ];
    }

    /**
     * @notes 获取竞品分析数据
     * @param int $shop_id 商家ID
     * @param array $params 查询参数
     * @return array
     */
    public static function getCompetitorAnalysis(int $shop_id, array $params = [])
    {
        $startDate = $params['start_date'] ?? date('Y-m-d', strtotime('-30 days'));
        $endDate = $params['end_date'] ?? date('Y-m-d');
        $categoryId = $params['category_id'] ?? null;

        // 获取同行业商家销售排名
        $industryRanking = Db::name('order')
            ->alias('o')
            ->join('shop s', 'o.shop_id = s.id')
            ->where('o.create_time', '>=', $startDate)
            ->where('o.create_time', '<=', $endDate)
            ->when($categoryId, function ($query) use ($categoryId) {
                $query->where('s.main_category_id', '=', $categoryId);
            })
            ->field([
                's.id as shop_id',
                's.name as shop_name',
                'COUNT(o.id) as order_count',
                'SUM(o.order_amount) as total_amount',
                'COUNT(DISTINCT o.user_id) as customer_count'
            ])
            ->group('s.id')
            ->order('total_amount DESC')
            ->limit(10)
            ->select()
            ->toArray();

        // 计算市场份额
        $totalMarketAmount = array_sum(array_column($industryRanking, 'total_amount'));
        foreach ($industryRanking as &$shop) {
            $shop['market_share'] = $totalMarketAmount > 0 
                ? round($shop['total_amount'] / $totalMarketAmount * 100, 2) 
                : 0;
        }

        // 获取价格区间分布
        $priceDistribution = Db::name('goods')
            ->alias('g')
            ->join('shop s', 'g.shop_id = s.id')
            ->where('s.main_category_id', '=', $categoryId)
            ->field([
                'CASE 
                    WHEN g.min_price < 50 THEN "0-50元"
                    WHEN g.min_price < 100 THEN "50-100元"
                    WHEN g.min_price < 200 THEN "100-200元"
                    WHEN g.min_price < 500 THEN "200-500元"
                    ELSE "500元以上"
                END as price_range',
                'COUNT(*) as goods_count'
            ])
            ->group('price_range')
            ->select()
            ->toArray();

        return [
            'industry_ranking' => $industryRanking,
            'price_distribution' => $priceDistribution,
            'time_range' => [
                'start_date' => $startDate,
                'end_date' => $endDate
            ]
        ];
    }

    /**
     * @notes 获取区域分析数据
     * @param int $shop_id 商家ID
     * @param array $params 查询参数
     * @return array
     */
    public static function getRegionAnalysis(int $shop_id, array $params = [])
    {
        $startDate = $params['start_date'] ?? date('Y-m-d', strtotime('-30 days'));
        $endDate = $params['end_date'] ?? date('Y-m-d');

        // 获取省份维度数据
        $provinceData = Db::name('order')
            ->alias('o')
            ->join('user_address ua', 'o.address_id = ua.id')
            ->where('o.shop_id', '=', $shop_id)
            ->where('o.create_time', '>=', $startDate)
            ->where('o.create_time', '<=', $endDate)
            ->field([
                'ua.province',
                'COUNT(*) as order_count',
                'SUM(o.order_amount) as sales_amount',
                'COUNT(DISTINCT o.user_id) as customer_count'
            ])
            ->group('ua.province')
            ->order('sales_amount DESC')
            ->select()
            ->toArray();

        // 获取城市维度TOP10
        $cityData = Db::name('order')
            ->alias('o')
            ->join('user_address ua', 'o.address_id = ua.id')
            ->where('o.shop_id', '=', $shop_id)
            ->where('o.create_time', '>=', $startDate)
            ->where('o.create_time', '<=', $endDate)
            ->field([
                'ua.city',
                'COUNT(*) as order_count',
                'SUM(o.order_amount) as sales_amount',
                'COUNT(DISTINCT o.user_id) as customer_count'
            ])
            ->group('ua.city')
            ->order('sales_amount DESC')
            ->limit(10)
            ->select()
            ->toArray();

        return [
            'province_data' => $provinceData,
            'city_data' => $cityData,
            'time_range' => [
                'start_date' => $startDate,
                'end_date' => $endDate
            ]
        ];
    }

    /**
     * @notes 获取行业排行数据
     * @param int $shop_id 商家ID
     * @param array $params 查询参数
     * @return array
     */
    public static function getIndustryRanking(int $shop_id, array $params = [])
    {
        $startDate = $params['start_date'] ?? date('Y-m-d', strtotime('-30 days'));
        $endDate = $params['end_date'] ?? date('Y-m-d');

        // 获取商家所属行业
        $shopIndustry = Db::name('shop')
            ->where('id', '=', $shop_id)
            ->value('main_category_id');

        // 获取行业内商家排名
        $rankings = Db::name('order')
            ->alias('o')
            ->join('shop s', 'o.shop_id = s.id')
            ->where('s.main_category_id', '=', $shopIndustry)
            ->where('o.create_time', '>=', $startDate)
            ->where('o.create_time', '<=', $endDate)
            ->field([
                's.id as shop_id',
                's.name as shop_name',
                'COUNT(*) as order_count',
                'SUM(o.order_amount) as sales_amount',
                'COUNT(DISTINCT o.user_id) as customer_count',
                'AVG(o.order_amount) as average_order_value'
            ])
            ->group('s.id')
            ->order('sales_amount DESC')
            ->select()
            ->toArray();

        // 计算当前商家排名
        $currentRank = 0;
        foreach ($rankings as $index => $rank) {
            if ($rank['shop_id'] == $shop_id) {
                $currentRank = $index + 1;
                break;
            }
        }

        return [
            'rankings' => $rankings,
            'current_rank' => $currentRank,
            'total_shops' => count($rankings),
            'time_range' => [
                'start_date' => $startDate,
                'end_date' => $endDate
            ]
        ];
    }

    /**
     * @notes 导出行业分析数据
     * @param int $shop_id 商家ID
     * @param array $params 查询参数
     * @return bool|string
     */
    public static function exportData(int $shop_id, array $params = [])
    {
        try {
            // 获取所有数据
            $data = [
                'overview' => self::getIndustryOverview($shop_id, $params),
                'sales_trend' => self::getSalesTrend($shop_id, $params),
                'category_analysis' => self::getCategoryAnalysis($shop_id, $params),
                'region_analysis' => self::getRegionAnalysis($shop_id, $params),
                'industry_ranking' => self::getIndustryRanking($shop_id, $params)
            ];

            // 创建Excel文件
            $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
            
            // 概览数据
            $sheet = $spreadsheet->getActiveSheet();
            $sheet->setTitle('行业概览');
            $sheet->setCellValue('A1', '指标');
            $sheet->setCellValue('B1', '数值');
            $sheet->setCellValue('C1', '环比增长');
            
            $row = 2;
            $overview = $data['overview']['overview'];
            $sheet->setCellValue('A'.$row, '总销售额');
            $sheet->setCellValue('B'.$row, $overview['total_sales']);
            $sheet->setCellValue('C'.$row, $overview['growth']['sales'].'%');
            
            // ... 添加其他sheet和数据 ...

            // 保存文件
            $filename = 'industry_analysis_'.date('YmdHis').'.xlsx';
            $writer = \PhpOffice\PhpSpreadsheet\IOFactory::createWriter($spreadsheet, 'Xlsx');
            $filepath = runtime_path('export/'.$filename);
            $writer->save($filepath);

            return $filepath;
        } catch (\Exception $e) {
            return $e->getMessage();
        }
    }

    /**
     * @notes 计算环比增长率
     * @param float $previous 上期数值
     * @param float $current 本期数值
     * @return float
     */
    private static function calculateGrowth($previous, $current)
    {
        if ($previous == 0) {
            return $current > 0 ? 100 : 0;
        }
        return round(($current - $previous) / $previous * 100, 2);
    }
}
