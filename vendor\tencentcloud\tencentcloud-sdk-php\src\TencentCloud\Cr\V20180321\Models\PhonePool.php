<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Cr\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * 号码组信息
 *
 * @method string getPoolId() 获取号码组ID
 * @method void setPoolId(string $PoolId) 设置号码组ID
 * @method string getPoolName() 获取号码组名称
 * @method void setPoolName(string $PoolName) 设置号码组名称
 */
class PhonePool extends AbstractModel
{
    /**
     * @var string 号码组ID
     */
    public $PoolId;

    /**
     * @var string 号码组名称
     */
    public $PoolName;

    /**
     * @param string $PoolId 号码组ID
     * @param string $PoolName 号码组名称
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("PoolId",$param) and $param["PoolId"] !== null) {
            $this->PoolId = $param["PoolId"];
        }

        if (array_key_exists("PoolName",$param) and $param["PoolName"] !== null) {
            $this->PoolName = $param["PoolName"];
        }
    }
}
