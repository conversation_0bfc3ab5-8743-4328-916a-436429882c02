<?php

namespace app\common\logic;

use app\common\basics\Logic;
use app\common\enum\ShopWithdrawEnum;
use app\common\server\ConfigServer;
use think\facade\Db;
use app\common\server\UrlServer;
class SettingLogic extends Logic
{
    static function getShopWithdraw()
    {
        $detail['withdrawal_type']              = ConfigServer::get('shop_withdrawal', 'withdrawal_type', [ ShopWithdrawEnum::TYPE_BANK ]);

        foreach ($detail['withdrawal_type'] as &$withdrawal_type) {
            $withdrawal_type = intval($withdrawal_type);
        }
        $detail['depositAmount']=ConfigServer::get('shop_entry', 'depositAmount');
        $detail['deposit_doc']=Db::name('help')->where('id',14)->value('document_path');
        $detail['deposit_doc']=$detail['deposit_doc']?UrlServer::getFileUrl($detail['deposit_doc']):''; 
        $detail['deposit_text']="请下载合同签署盖章后";
        $detail['min_withdrawal_money']         = ConfigServer::get('shop_withdrawal', 'min_withdrawal_money', 0);
        $detail['max_withdrawal_money']         = ConfigServer::get('shop_withdrawal', 'max_withdrawal_money', 0);
        $detail['withdrawal_service_charge']    = ConfigServer::get('shop_withdrawal', 'withdrawal_service_charge', 0);

        return $detail;
    }
    static function getShopentry()
    {
        $detail['entry_fee']         = ConfigServer::get('shop_entry', 'entry_fee', 0);
        $detail['zins_fee']         = ConfigServer::get('shop_entry', 'zins_fee', 0);
        $detail['ins_fee']         = ConfigServer::get('shop_entry', 'ins_fee', 0);
        $detail['msg_day']         = ConfigServer::get('shop_entry', 'msg_day', 1);
        $detail['depositAmount']         = ConfigServer::get('shop_entry', 'depositAmount', 1);
        $detail['procurementDeposit']         = ConfigServer::get('shop_entry', 'procurementDeposit', 1);
        $detail['free_shop_image'] = ConfigServer::get('shop_entry', 'free_shop_image', '');
        $detail['normal_shop_image'] = ConfigServer::get('shop_entry', 'normal_shop_image', '');
        $detail['senior_shop_image'] = ConfigServer::get('shop_entry', 'senior_shop_image', '');
        $detail['refund_warning_image'] = ConfigServer::get('shop_entry', 'refund_warning_image', '');
        $detail['deposit_publicity_days'] = ConfigServer::get('shop_entry', 'deposit_publicity_days', 7);
        return $detail;
    }

    static function getShopentry2()
    {
        $detail['ad']         = ConfigServer::get('popup', 'ad', 0);
        $detail['date_string']         = ConfigServer::get('popup', 'date_string', 0);
        $detail['date_link']         = ConfigServer::get('popup', 'date_link', 0);
        return $detail;
    }

    static function setShopWithdraw($data)
    {
        ConfigServer::set('shop_withdrawal', 'withdrawal_type', array_values($data['withdrawal_type'] ?? []));
        ConfigServer::set('shop_withdrawal', 'min_withdrawal_money', $data['min_withdrawal_money'] ?? 0);
        ConfigServer::set('shop_withdrawal', 'max_withdrawal_money', $data['max_withdrawal_money'] ?? 0);
        ConfigServer::set('shop_withdrawal', 'withdrawal_service_charge', $data['withdrawal_service_charge'] ?? 0);
    }
    static function setshopentry($data)
    {
        ConfigServer::set('shop_entry', 'entry_fee', $data['entry_fee'] ?? 0);
        ConfigServer::set('shop_entry', 'msg_day', $data['msg_day'] ?? 0);
        ConfigServer::set('shop_entry', 'zins_fee', $data['zins_fee'] ?? 0);
        ConfigServer::set('shop_entry', 'ins_fee', $data['ins_fee'] ?? 0);
        ConfigServer::set('shop_entry', 'depositAmount', $data['depositAmount'] ?? 0);
        ConfigServer::set('shop_entry', 'procurementDeposit', $data['procurementDeposit'] ?? 0);
        ConfigServer::set('shop_entry', 'free_shop_image', $data['free_shop_image'] ?? '');
        ConfigServer::set('shop_entry', 'normal_shop_image', $data['normal_shop_image'] ?? '');
        ConfigServer::set('shop_entry', 'senior_shop_image', $data['senior_shop_image'] ?? '');
        ConfigServer::set('shop_entry', 'refund_warning_image', $data['refund_warning_image'] ?? '');
        ConfigServer::set('shop_entry', 'deposit_publicity_days', $data['deposit_publicity_days'] ?? 7);
    }

    static function setshopentry2($data)
    {
        ConfigServer::set('popup', 'ad', $data['ad'] ?? 0);
        ConfigServer::set('popup', 'date_string', $data['date_string'] ?? 0);
        ConfigServer::set('popup', 'date_link', $data['date_link'] ?? 0);

    }
}