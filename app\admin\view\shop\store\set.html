{layout name="layout2" /}
<style>
    .layui-form-item .layui-form-label { width: 95px; }
    .layui-form-item .layui-input-inline { width: 240px; }
</style>

<div class="layui-card layui-form" style="box-shadow:none;">

    <div class="layui-card-body">
        <div class="layui-form-item">
            <label class="layui-form-label">推荐商家：</label>
            <div class="layui-input-inline">
                <input type="radio" name="is_recommend" value="1" title="是" {if $detail.is_recommend}checked{/if}>
                <input type="radio" name="is_recommend" value="0" title="否" {if !$detail.is_recommend}checked{/if}>
                <div class="layui-form-mid layui-word-aux" style="white-space:nowrap;">商城首页会显示推荐的商家</div>
            </div>
        </div>

        <div class="layui-form-item" {if !$detail.yan_fee } style="display: none"{/if}>
            <label for="cid" class="layui-form-label">检验徽章：</label>
            <div class="layui-input-inline">
                <select name="yan_level" id="cid" lay-verType="tips" lay-verify="cid" switch-tab="0">
                    <option value=""></option>
                    {volist name="levels" id="vo"}
                    <option value="{$vo.id}" {if $vo.id == $detail.yan_level}selected{/if}><src>{$vo.name}</option>
                    {/volist}
                </select>
            </div>
        </div>

        <div class="layui-form-item" {if !$detail.yan_fee || $detail.yan_time > 0} style="display: none"{/if}>
            <label class="layui-form-label">检验人员：</label>
            <div class="layui-input-inline">
                <button type="button" class="layui-btn layui-btn-normal" id="assign_staff_btn">
                    {if $detail.f_user > 0}修改检验人员{else}派遣检验人员{/if}
                </button>
                <div class="layui-form-mid layui-word-aux" style="white-space:nowrap;">
                    {if $detail.f_user > 0}
                        当前检验人员：{$staff_nickname}
                    {else}
                        为商家分配检验人员
                    {/if}
                </div>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">分销功能：</label>
            <div class="layui-input-inline">
                <input type="radio" name="is_distribution" value="1" title="是" {if $detail.is_distribution}checked{/if}>
                <input type="radio" name="is_distribution" value="0" title="否" {if !$detail.is_distribution}checked{/if}>
                <div class="layui-form-mid layui-word-aux" style="white-space:nowrap;">开启或关闭分销功能，关闭后商家不参与分销推广</div>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">支付功能：</label>
            <div class="layui-input-inline">
                <input type="radio" name="is_pay" value="1" title="开启" {if $detail.is_pay}checked{/if}>
                <input type="radio" name="is_pay" value="0" title="关闭" {if !$detail.is_pay}checked{/if}>
                <div class="layui-form-mid layui-word-aux" style="white-space:nowrap;">默认开启,关闭时商品详情则显示咨询商家按钮</div>
            </div>
        </div>
        <div class="layui-form-item">
            <label for="weight" class="layui-form-label"><span style="color:red;">*</span>排序权重：</label>
            <div class="layui-input-inline">
                <input type="number" name="weight" id="weight" value="{$detail.weight}" lay-verType="tips" lay-verify="require|number"
                       switch-tab="0" autocomplete="off" class="layui-input">
                <div class="layui-form-mid layui-word-aux" style="white-space:nowrap;">商品排序权重，数字越小排序越前，权重越大</div>
            </div>
        </div>
    </div>


    <div class="layui-form-item layui-hide">
        <input type="button" lay-submit lay-filter="addSubmit" id="addSubmit" value="确认">
    </div>
</div>

<script>
    layui.use(['form', 'layer'], function() {
        var form = layui.form;
        var layer = layui.layer;
        var $ = layui.jquery;

        // 派遣检验人员按钮点击事件
        $('#assign_staff_btn').on('click', function() {
            // 派遣检验人员
            layer.open({
                type: 1,
                title: '派遣检验人员',
                area: ['500px', '400px'],
                content: '<div class="layui-card-body" style="padding: 20px;">' +
                    '<div class="layui-form">' +
                    '<div class="layui-form-item">' +
                    '<label class="layui-form-label">搜索条件：</label>' +
                    '<div class="layui-input-inline">' +
                    '<select id="search_type" lay-filter="search_type">' +
                    '<option value="mobile">手机号</option>' +
                    '<option value="nickname">昵称</option>' +
                    '</select>' +
                    '</div>' +
                    '<div class="layui-input-inline">' +
                    '<input type="text" id="search_value" placeholder="请输入搜索内容" autocomplete="off" class="layui-input">' +
                    '</div>' +
                    '<button type="button" class="layui-btn layui-btn-normal" id="search_staff">搜索</button>' +
                    '</div>' +
                    '<div id="staff_list" style="margin-top: 20px;"></div>' +
                    '</div>' +
                    '</div>',
                success: function(layero, index) {
                    form.render('select');

                    // 搜索检验人员
                    $(layero).find('#search_staff').on('click', function() {
                        var search_type = $(layero).find('#search_type').val();
                        var search_value = $(layero).find('#search_value').val();

                        if (!search_value) {
                            layer.msg('请输入搜索内容');
                            return;
                        }

                        // 发送请求搜索检验人员
                        var searchUrl = "{:url('shop.Store/searchStaff')}";
                        like.ajax({
                            url: searchUrl,
                            data: {
                                search_type: search_type,
                                search_value: search_value
                            },
                            type: "GET",
                            success: function(res) {
                                if (res.code === 1) {
                                    var html = '';
                                    if (res.data.length > 0) {
                                        html += '<table class="layui-table">';
                                        html += '<thead><tr><th>ID</th><th>昵称</th><th>手机号</th><th>操作</th></tr></thead>';
                                        html += '<tbody>';

                                        $.each(res.data, function(index, item) {
                                            html += '<tr>';
                                            html += '<td>' + item.id + '</td>';
                                            html += '<td>' + item.nickname + '</td>';
                                            html += '<td>' + item.mobile + '</td>';
                                            html += '<td><button class="layui-btn layui-btn-xs layui-btn-normal select-staff" data-id="' + item.id + '" data-nickname="' + item.nickname + '">选择</button></td>';
                                            html += '</tr>';
                                        });

                                        html += '</tbody></table>';
                                    } else {
                                        html = '<div class="layui-text" style="text-align: center; padding: 20px 0;">未找到相关人员</div>';
                                    }

                                    $(layero).find('#staff_list').html(html);

                                    // 选择检验人员
                                    $(layero).find('.select-staff').on('click', function() {
                                        var staff_id = $(this).data('id');
                                        var staff_nickname = $(this).data('nickname');

                                        // 确认选择
                                        layer.confirm('确定选择 ' + staff_nickname + ' 作为检验人员？', {
                                            btn: ['确定', '取消']
                                        }, function() {
                                            // 发送请求分配检验人员
                                            var assignUrl = "{:url('shop.Store/assignStaff')}";
                                            like.ajax({
                                                url: assignUrl,
                                                data: {
                                                    shop_id: {$detail.id},
                                                    staff_id: staff_id
                                                },
                                                type: "POST",
                                                success: function(res) {
                                                    if (res.code === 1) {
                                                        layer.msg(res.msg, {icon: 1});
                                                        layer.close(index);
                                                        // 刷新父页面
                                                        parent.layui.table.reload('like-table-lists');
                                                        // 关闭当前设置窗口
                                                        parent.layer.closeAll();
                                                    } else {
                                                        layer.msg(res.msg, {icon: 2});
                                                    }
                                                }
                                            });
                                        });
                                    });
                                } else {
                                    layer.msg(res.msg, {icon: 2});
                                }
                            }
                        });
                    });
                }
            });
        });
    });
</script>