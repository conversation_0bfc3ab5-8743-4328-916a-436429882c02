<?php
namespace app\api\logic;

use app\common\basics\Logic;
use Exception;
use setasign\Fpdi\Fpdi;
use think\facade\Log;

/**
 * 使用GD库和FPDI处理PDF文件
 * 用于在PDF文件中添加签名和日期
 * Class GdPdfProcessor
 * @package app\api\logic
 */
class GdPdfProcessor extends Logic
{
    /**
     * @var string PDF文件路径
     */
    protected $pdfPath;

    /**
     * @var string 签名图片路径
     */
    protected $signaturePath;

    /**
     * @var string 输出PDF路径
     */
    protected $outputPath;

    /**
     * @var array 签名位置坐标
     */
    protected $signaturePosition = [
        'x' => 400,  // 默认X坐标
        'y' => 680,  // 默认Y坐标
        'width' => 80, // 默认宽度
        'height' => 40 // 默认高度
    ];

    /**
     * @var array 日期位置坐标
     */
    protected $datePosition = [
        'x' => 400,  // 默认X坐标
        'y' => 720,  // 默认Y坐标
    ];

    /**
     * 构造函数
     * @param string $pdfPath PDF文件路径
     */
    public function __construct($pdfPath = '')
    {
        if (!empty($pdfPath)) {
            $this->setPdfPath($pdfPath);
        }
    }

    /**
     * 设置PDF文件路径
     * @param string $pdfPath
     * @return $this
     * @throws Exception
     */
    public function setPdfPath($pdfPath)
    {
        if (!file_exists($pdfPath)) {
            throw new Exception('PDF文件不存在: ' . $pdfPath);
        }
        $this->pdfPath = $pdfPath;
        return $this;
    }

    /**
     * 设置签名图片路径
     * @param string $signaturePath
     * @return $this
     * @throws Exception
     */
    public function setSignaturePath($signaturePath)
    {
        if (!file_exists($signaturePath)) {
            throw new Exception('签名图片不存在: ' . $signaturePath);
        }
        $this->signaturePath = $signaturePath;
        return $this;
    }

    /**
     * 设置输出PDF路径
     * @param string $outputPath
     * @return $this
     */
    public function setOutputPath($outputPath)
    {
        $this->outputPath = $outputPath;
        return $this;
    }

    /**
     * 设置签名位置
     * @param float $x X坐标
     * @param float $y Y坐标
     * @param float|null $width 宽度
     * @param float|null $height 高度
     * @return $this
     */
    public function setSignaturePosition($x, $y, $width = null, $height = null)
    {
        $this->signaturePosition['x'] = $x;
        $this->signaturePosition['y'] = $y;

        if ($width !== null) {
            $this->signaturePosition['width'] = $width;
        }

        if ($height !== null) {
            $this->signaturePosition['height'] = $height;
        }

        return $this;
    }

    /**
     * 设置日期位置
     * @param float $x X坐标
     * @param float $y Y坐标
     * @return $this
     */
    public function setDatePosition($x, $y)
    {
        $this->datePosition['x'] = $x;
        $this->datePosition['y'] = $y;
        return $this;
    }

    /**
     * 处理PDF文件，添加签名和日期
     * @param int $pageNumber 页码，从1开始
     * @param string $dateFormat 日期格式，默认为Y-m-d
     * @return string 处理后的PDF文件路径
     * @throws Exception
     */
    public function process($pageNumber = 1, $dateFormat = 'Y-m-d')
    {
        // 检查必要参数
        if (empty($this->pdfPath)) {
            throw new Exception('未设置PDF文件路径');
        }

        if (empty($this->signaturePath)) {
            throw new Exception('未设置签名图片路径');
        }

        if (empty($this->outputPath)) {
            // 如果未设置输出路径，则生成一个临时路径
            $pathInfo = pathinfo($this->pdfPath);
            $this->outputPath = $pathInfo['dirname'] . '/' . $pathInfo['filename'] . '_signed.' . $pathInfo['extension'];
        }

        try {
            // 记录处理开始
            Log::info('开始处理PDF文件: ' . $this->pdfPath);
            Log::info('签名图片路径: ' . $this->signaturePath);
            Log::info('输出PDF路径: ' . $this->outputPath);
            Log::info('签名位置: ' . json_encode($this->signaturePosition));
            Log::info('日期位置: ' . json_encode($this->datePosition));

            // 检查签名图片是否存在
            if (!file_exists($this->signaturePath)) {
                Log::error('签名图片文件不存在: ' . $this->signaturePath);
                throw new Exception('签名图片文件不存在: ' . $this->signaturePath);
            }

            // 检查图片文件大小
            $fileSize = filesize($this->signaturePath);
            Log::info('签名图片文件大小: ' . $fileSize . ' 字节');
            if ($fileSize <= 0) {
                Log::error('签名图片文件为空: ' . $this->signaturePath . ', 大小: ' . $fileSize);
                throw new Exception('签名图片文件为空: ' . $this->signaturePath . ', 大小: ' . $fileSize);
            }

            // 获取图片信息
            $imageInfo = getimagesize($this->signaturePath);
            if (!$imageInfo) {
                Log::error('无法获取图片信息: ' . $this->signaturePath);
                throw new Exception('无法获取图片信息: ' . $this->signaturePath);
            }
            Log::info('签名图片信息: ' . json_encode($imageInfo));

            // 创建FPDI实例
            Log::info('创建FPDI实例');
            $pdf = new Fpdi();

            // 设置一些基本属性
            $pdf->SetAutoPageBreak(false);
            $pdf->SetCompression(true);

            // 获取页数
            Log::info('获取PDF页数');
            $pageCount = $pdf->setSourceFile($this->pdfPath);
            Log::info('PDF页数: ' . $pageCount);

            // 循环处理每一页
            for ($i = 1; $i <= $pageCount; $i++) {
                Log::info('处理第 ' . $i . ' 页');
                // 导入页面
                $templateId = $pdf->importPage($i);
                $size = $pdf->getTemplateSize($templateId);
                $orientation = ($size['width'] > $size['height']) ? 'L' : 'P';
                Log::info('页面尺寸: ' . json_encode($size) . ', 方向: ' . $orientation);

                // 添加页面
                $pdf->AddPage($orientation, [$size['width'], $size['height']]);
                $pdf->useTemplate($templateId);

                // 只在指定页面添加签名和日期
                if ($i == $pageNumber) {
                    Log::info('在第 ' . $i . ' 页添加签名和日期');

                    // 使用GD库处理图片
                    try {
                        // 确定图片类型
                        $imageType = '';
                        $gdImage = null;

                        switch ($imageInfo['mime']) {
                            case 'image/jpeg':
                            case 'image/jpg':
                                $imageType = 'JPG';
                                Log::info('图片类型: JPG');
                                $gdImage = imagecreatefromjpeg($this->signaturePath);
                                break;
                            case 'image/png':
                                $imageType = 'PNG';
                                Log::info('图片类型: PNG');
                                $gdImage = imagecreatefrompng($this->signaturePath);
                                break;
                            default:
                                Log::error('不支持的图片格式: ' . $imageInfo['mime']);
                                throw new Exception('不支持的图片格式: ' . $imageInfo['mime']);
                        }

                        if (!$gdImage) {
                            Log::error('无法加载图片: ' . $this->signaturePath);
                            throw new Exception('无法加载图片: ' . $this->signaturePath);
                        }

                        // 创建临时图片文件
                        $tempImagePath = dirname($this->outputPath) . '/' . uniqid() . '.' . strtolower($imageType);
                        Log::info('临时图片路径: ' . $tempImagePath);

                        // 调整图片大小
                        $newWidth = $this->signaturePosition['width'];
                        $newHeight = $this->signaturePosition['height'];
                        Log::info('调整图片大小: ' . $newWidth . 'x' . $newHeight);
                        $newImage = imagecreatetruecolor($newWidth, $newHeight);

                        // 保持PNG透明度
                        if ($imageType == 'PNG') {
                            Log::info('保持PNG透明度');
                            imagealphablending($newImage, false);
                            imagesavealpha($newImage, true);
                            $transparent = imagecolorallocatealpha($newImage, 255, 255, 255, 127);
                            imagefilledrectangle($newImage, 0, 0, $newWidth, $newHeight, $transparent);
                        }

                        // 调整图片大小
                        Log::info('调整图片大小');
                        imagecopyresampled(
                            $newImage,
                            $gdImage,
                            0,
                            0,
                            0,
                            0,
                            $newWidth,
                            $newHeight,
                            imagesx($gdImage),
                            imagesy($gdImage)
                        );

                        // 保存调整后的图片
                        Log::info('保存调整后的图片: ' . $tempImagePath);
                        if ($imageType == 'JPG') {
                            imagejpeg($newImage, $tempImagePath, 100);
                        } else {
                            imagepng($newImage, $tempImagePath, 9);
                        }

                        // 检查临时图片是否保存成功
                        if (!file_exists($tempImagePath)) {
                            Log::error('保存临时图片失败: ' . $tempImagePath);
                            throw new Exception('保存临时图片失败: ' . $tempImagePath);
                        }

                        $tempFileSize = filesize($tempImagePath);
                        Log::info('临时图片文件大小: ' . $tempFileSize . ' 字节');
                        if ($tempFileSize <= 0) {
                            Log::error('临时图片文件为空: ' . $tempImagePath . ', 大小: ' . $tempFileSize);
                            throw new Exception('临时图片文件为空: ' . $tempImagePath . ', 大小: ' . $tempFileSize);
                        }

                        // 释放资源
                        imagedestroy($gdImage);
                        imagedestroy($newImage);

                        // 添加图片到PDF
                        Log::info('添加图片到PDF: ' . $tempImagePath);
                        Log::info('图片位置: X=' . $this->signaturePosition['x'] . ', Y=' . $this->signaturePosition['y'] . ', 宽度=' . $this->signaturePosition['width'] . ', 高度=' . $this->signaturePosition['height']);
                        $pdf->Image(
                            $tempImagePath,
                            $this->signaturePosition['x'],
                            $this->signaturePosition['y'],
                            $this->signaturePosition['width'],
                            $this->signaturePosition['height'],
                            $imageType
                        );

                        // 清理临时文件
                        Log::info('清理临时文件: ' . $tempImagePath);
                        @unlink($tempImagePath);

                    } catch (Exception $e) {
                        Log::error('处理图片时出错: ' . $e->getMessage());
                        throw new Exception('处理图片时出错: ' . $e->getMessage());
                    }

                    // 添加当前日期
                    Log::info('添加当前日期');
                    $pdf->SetFont('Arial', '', 10);
                    $pdf->SetXY($this->datePosition['x'], $this->datePosition['y']);

                    // 使用简单的日期格式，避免中文
                    $dateFormat = str_replace('年', '-', $dateFormat);
                    $dateFormat = str_replace('月', '-', $dateFormat);
                    $dateFormat = str_replace('日', '', $dateFormat);
                    $date = date($dateFormat);
                    Log::info('日期: ' . $date);
                    $pdf->Write(0, $date);
                }
            }

            // 输出到文件
            Log::info('输出PDF到文件: ' . $this->outputPath);
            $pdf->Output('F', $this->outputPath);

            // 检查输出文件是否存在
            if (!file_exists($this->outputPath)) {
                Log::error('输出PDF文件不存在: ' . $this->outputPath);
                throw new Exception('输出PDF文件不存在: ' . $this->outputPath);
            }

            $outputFileSize = filesize($this->outputPath);
            Log::info('输出PDF文件大小: ' . $outputFileSize . ' 字节');
            if ($outputFileSize <= 0) {
                Log::error('输出PDF文件为空: ' . $this->outputPath . ', 大小: ' . $outputFileSize);
                throw new Exception('输出PDF文件为空: ' . $this->outputPath . ', 大小: ' . $outputFileSize);
            }

            Log::info('PDF处理完成');

            return $this->outputPath;
        } catch (Exception $e) {
            throw new Exception('处理PDF文件时出错: ' . $e->getMessage());
        }
    }
}
