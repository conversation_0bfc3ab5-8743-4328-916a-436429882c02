<?php

namespace app\common\model\shop;

use app\common\model\BaseModel as Model;
use think\model\concern\SoftDelete;

class ShopRefundAddress extends Model
{
    use SoftDelete;
    
    protected $deleteTime = 'delete_time';
    
    /**
     * 获取省市区名称
     * @param $value
     * @param $data
     * @return string
     */
    public function getProvinceNameAttr($value, $data)
    {
        return \app\common\model\dev\DevRegion::where('id', $data['province'])->value('name') ?? '';
    }
    
    /**
     * 获取省市区名称
     * @param $value
     * @param $data
     * @return string
     */
    public function getCityNameAttr($value, $data)
    {
        return \app\common\model\dev\DevRegion::where('id', $data['city'])->value('name') ?? '';
    }
    
    /**
     * 获取省市区名称
     * @param $value
     * @param $data
     * @return string
     */
    public function getDistrictNameAttr($value, $data)
    {
        return \app\common\model\dev\DevRegion::where('id', $data['district'])->value('name') ?? '';
    }
    
    /**
     * 获取完整地址
     * @param $value
     * @param $data
     * @return string
     */
    public function getFullAddressAttr($value, $data)
    {
        return $this->getProvinceNameAttr('', $data) . ' ' . 
               $this->getCityNameAttr('', $data) . ' ' . 
               $this->getDistrictNameAttr('', $data) . ' ' . 
               $data['address'];
    }
}