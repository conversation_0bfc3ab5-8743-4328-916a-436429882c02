{layout name="layout1" /}
<div class="wrapper">
    <div class="layui-card">
        <!-- 操作提示 -->
        <div class="layui-card-body">
            <div class="layui-collapse" style="border:1px dashed #c4c4c4">
                <div class="layui-colla-item">
                    <h2 class="layui-colla-title like-layui-colla-title">操作提示</h2>
                    <div class="layui-colla-content layui-show">
                        <p>* 此处列出了所有广告位的购买记录。</p>
                        <p>* 您可以根据订单号、广告位名称、购买用户、支付状态、订单状态和创建时间进行搜索。</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 搜索区域 -->
        <div class="layui-card-body layui-form">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">订单号:</label>
                    <div class="layui-input-inline">
                        <input type="text" id="order_sn" name="order_sn" placeholder="请输入订单号" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">广告位:</label>
                    <div class="layui-input-inline">
                        <input type="text" id="position_name" name="position_name" placeholder="请输入广告位名称" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">购买用户:</label>
                    <div class="layui-input-inline">
                        <input type="text" id="user_nickname" name="user_nickname" placeholder="请输入用户昵称" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">支付状态:</label>
                    <div class="layui-input-inline">
                        <select name="pay_status" id="pay_status">
                            <option value="">全部</option>
                            {foreach $payStatus as $key => $item}
                            <option value="{$key}">{$item}</option>
                            {/foreach}
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">订单状态:</label>
                    <div class="layui-input-inline">
                        <select name="order_status" id="order_status">
                            <option value="">全部</option>
                            {foreach $orderStatus as $key => $item}
                            <option value="{$key}">{$item}</option>
                            {/foreach}
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">支付方式:</label>
                    <div class="layui-input-inline">
                        <select name="pay_way" id="pay_way">
                            <option value="">全部</option>
                            {foreach $payWay as $key => $item}
                            <option value="{$key}">{$item}</option>
                            {/foreach}
                        </select>
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                 <div class="layui-inline">
                    <label class="layui-form-label">创建时间:</label>
                    <div class="layui-input-inline">
                        <input type="text" name="start_time" class="layui-input" id="start_time" placeholder="开始时间" autocomplete="off">
                    </div>
                    <div class="layui-input-inline" style="margin-right: 5px;width: 20px;">
                        <label class="layui-form-mid">至</label>
                    </div>
                    <div class="layui-input-inline">
                        <input type="text" name="end_time" class="layui-input" id="end_time" placeholder="结束时间" autocomplete="off">
                    </div>
                </div>
                <div class="layui-inline">
                    <a class="layui-btn layui-btn-sm layui-btn-normal" lay-submit lay-filter="search">
                        <i class="layui-icon layui-icon-search"></i>搜索
                    </a>
                    <a class="layui-btn layui-btn-sm layui-btn-primary" lay-submit lay-filter="clear-search">
                         <i class="layui-icon layui-icon-refresh"></i>重置
                    </a>
                    <!-- 可选：如果需要导出功能 -->
                    <!-- <a class="layui-btn layui-btn-sm layui-btn-primary" lay-submit lay-filter="data-export">导出</a> -->
                </div>
            </div>
        </div>

        <!-- 主体区域 -->
        <div class="layui-card-body">
             <table id="buyListsTable" lay-filter="buyListsTable"></table>
        </div>
    </div>
</div>

<!-- 如果需要操作列，可以取消注释下面的脚本模板 -->
<!--
<script type="text/html" id="toolBar">
    <a class="layui-btn layui-btn-xs" lay-event="detail">查看</a>
</script>
-->
<script>
    layui.use(["table", "element", "laydate",'form'], function(){
        var $ = layui.$;
        var table = layui.table;
        var form = layui.form;
        var laydate = layui.laydate;
        var element = layui.element; // 添加 element 模块

        //日期时间范围
        laydate.render({
            elem: '#start_time'
            , type: 'datetime'
            , theme: '#1E9FFF' // 保持主题一致
        });

        laydate.render({
            elem: '#end_time'
            , type: 'datetime'
            , theme: '#1E9FFF' // 保持主题一致
        });

        var terminal = 1; // 保留此变量，假设它有特定用途
        like.tableLists('#buyListsTable', '{:url()}', [
            {field: 'id', title: 'ID', width: 80, sort: true}
            , {field: 'order_sn', title: '订单号', width: 180}
            , {field: 'position_name', title: '广告位名称', minWidth: 150}
            , {field: 'user_nickname', title: '购买用户', width: 120}
            , {field: 'ad_price', title: '支付金额', width: 100, sort: true}
            , {field: 'pay_way_desc', title: '支付方式', width: 100}
            , {field: 'pay_status_desc', title: '支付状态', width: 100, templet: function(d){ return d.status == 1 ? '<span class="layui-badge layui-bg-green">'+d.pay_status_desc+'</span>' : '<span class="layui-badge layui-bg-orange">'+d.pay_status_desc+'</span>';}} // 注意: pay_status 判断基于 status 字段
            // , {field: 'order_status_desc', title: '订单状态', width: 100, templet: function(d){ return d.order_status == 1 ? '<span class="layui-badge layui-bg-blue">'+d.order_status_desc+'</span>' : (d.order_status == 2 ? '<span class="layui-badge">'+d.order_status_desc+'</span>' : '<span class="layui-badge layui-bg-orange">'+d.order_status_desc+'</span>')}} // ad_order 表似乎没有 order_status
            // --- 修改开始: 替换支付时间和创建时间列 ---
            , {field: 'duration_desc', title: '购买时长', width: 100} // 替换支付时间
            , {field: 'validity_period', title: '投放时间', width: 200} // 替换创建时间
            // --- 修改结束 ---
            // 如果需要操作列，取消下一行的注释，并确保 #toolBar 脚本存在
            // ,{title:"操作", width:100, align:"center", fixed:"right", toolbar:"#toolBar"}
        ],{terminal:terminal});


        // 搜索事件 (注意: 时间范围搜索仍基于 create_time, 前端字段名是 start_time/end_time)
        form.on('submit(search)', function(data){
            // 如果后端时间搜索已改为基于投放时间，这里需要传递不同的参数名
            table.reload('buyListsTable', {
                where: data.field, // data.field 包含 start_time 和 end_time
                page: {
                    curr: 1
                }
            });
            return false;
        });

        // 重置事件 (保持不变)
        form.on('submit(clear-search)', function(data){
            // ... 清空表单 ...
            form.render();
            table.reload('buyListsTable', {
                where: {},
                page: {
                    curr: 1
                }
            });
            return false;
        });

        // 可选：导出功能 (如果添加了导出按钮)
        /*
        form.on('submit(data-export)', function (data) {
            var field = data.field;
            // 构造导出URL，可能需要后端配合
            var exportUrl = '{:url("exportBuyLists")}?' + $.param(field); // 假设导出接口为 exportBuyLists
            // 使用 like.ajax 或直接跳转
            like.ajax({
                 url: '{:url("exportBuyLists")}' // 替换为实际的导出处理URL
                 , data: field
                 , type: 'get' // 或 'post'
                 , success: function (res) {
                     if (res.code == 1 && res.data.url) {
                         window.location.href = res.data.url; // 跳转到生成的导出文件链接
                     } else {
                         layer.msg(res.msg || '导出失败');
                     }
                 }
             });
            // 或者简单地构建URL并跳转（如果后端直接生成文件下载）
            // window.location.href = exportUrl;
            return false; // 阻止表单跳转
        });
        */

        // 如果需要监听工具条事件 (需要取消表格列定义中的操作列注释和 #toolBar 脚本注释)
        /*
        var active = {
            detail: function(obj){
                // 查看详情逻辑
                layer.open({
                    type: 2,
                    title: '订单详情',
                    content: '{:url("orderDetails")}?id=' + obj.data.id, // 假设有订单详情页
                    area: ['800px', '600px']
                });
            }
        };
        // 假设 like.tool 是处理表格工具栏事件的辅助函数
        // like.tool('buyListsTable', active);
        // 或者使用 layui 原生方式:
        table.on('tool(buyListsTable)', function(obj){
            var data = obj.data; //获得当前行数据
            var layEvent = obj.event; //获得 lay-event 对应的值（比如 'detail'）

            if(layEvent === 'detail'){
                 layer.open({
                    type: 2,
                    title: '订单详情',
                    content: '{:url("orderDetails")}?id=' + data.id, // 假设有订单详情页
                    area: ['800px', '600px']
                });
            }
            // 可以添加其他 lay-event 的处理
        });
        */
    });
</script>