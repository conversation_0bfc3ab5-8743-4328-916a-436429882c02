#!/bin/bash

# kshop项目自动化部署脚本
# 用法: ./deploy.sh [test|production] [version_tag]

set -e  # 遇到错误立即退出

# 配置变量
PROJECT_NAME="kshop"
REPO_URL="https://github.com/your-username/kshop.git"  # 请替换为实际的仓库地址
BASE_DIR="/var/www"
TEST_DIR="$BASE_DIR/${PROJECT_NAME}-test"
PROD_DIR="$BASE_DIR/${PROJECT_NAME}-production"
RELEASES_DIR="$BASE_DIR/${PROJECT_NAME}-releases"
BACKUP_DIR="$BASE_DIR/${PROJECT_NAME}-backups"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查参数
if [ $# -lt 1 ]; then
    echo "用法: $0 [test|production|rollback] [version_tag]"
    echo "示例:"
    echo "  $0 test                    # 部署到测试环境"
    echo "  $0 production v1.0.1       # 部署指定版本到正式环境"
    echo "  $0 rollback v1.0.0         # 回退到指定版本"
    exit 1
fi

ENVIRONMENT=$1
VERSION_TAG=${2:-"latest"}

# 创建必要的目录
create_directories() {
    log_info "创建必要的目录..."
    sudo mkdir -p "$RELEASES_DIR"
    sudo mkdir -p "$BACKUP_DIR"
    sudo chown -R www-data:www-data "$BASE_DIR"
}

# 备份当前版本
backup_current() {
    local target_dir=$1
    local backup_name="${PROJECT_NAME}-backup-$(date +%Y%m%d_%H%M%S)"
    
    if [ -d "$target_dir" ]; then
        log_info "备份当前版本到 $BACKUP_DIR/$backup_name"
        sudo cp -r "$target_dir" "$BACKUP_DIR/$backup_name"
        
        # 只保留最近10个备份
        cd "$BACKUP_DIR"
        sudo ls -t | tail -n +11 | sudo xargs -r rm -rf
    fi
}

# 部署到测试环境
deploy_test() {
    log_info "开始部署到测试环境..."
    
    # 备份当前测试版本
    backup_current "$TEST_DIR"
    
    # 克隆或更新代码
    if [ -d "$TEST_DIR" ]; then
        log_info "更新测试环境代码..."
        cd "$TEST_DIR"
        sudo git fetch origin
        sudo git reset --hard origin/develop
    else
        log_info "克隆代码到测试环境..."
        sudo git clone -b develop "$REPO_URL" "$TEST_DIR"
    fi
    
    # 安装依赖和配置
    setup_environment "$TEST_DIR"
    
    log_info "测试环境部署完成！"
}

# 部署到正式环境
deploy_production() {
    log_info "开始部署到正式环境..."
    
    # 检查版本标签
    if [ "$VERSION_TAG" = "latest" ]; then
        log_error "正式环境部署必须指定版本标签！"
        exit 1
    fi
    
    # 备份当前正式版本
    backup_current "$PROD_DIR"
    
    # 创建版本发布目录
    local release_dir="$RELEASES_DIR/$VERSION_TAG"
    
    if [ ! -d "$release_dir" ]; then
        log_info "创建版本 $VERSION_TAG 的发布目录..."
        sudo git clone -b "$VERSION_TAG" "$REPO_URL" "$release_dir"
        setup_environment "$release_dir"
    fi
    
    # 切换正式环境到新版本
    log_info "切换正式环境到版本 $VERSION_TAG..."
    sudo rm -f "$PROD_DIR"
    sudo ln -sf "$release_dir" "$PROD_DIR"
    
    # 重启服务
    restart_services
    
    log_info "正式环境部署完成！当前版本: $VERSION_TAG"
}

# 版本回退
rollback() {
    local target_version=$2
    
    if [ -z "$target_version" ]; then
        log_error "回退操作必须指定目标版本！"
        exit 1
    fi
    
    local release_dir="$RELEASES_DIR/$target_version"
    
    if [ ! -d "$release_dir" ]; then
        log_error "版本 $target_version 不存在！"
        log_info "可用版本:"
        ls -1 "$RELEASES_DIR" 2>/dev/null || echo "无可用版本"
        exit 1
    fi
    
    log_info "回退正式环境到版本 $target_version..."
    
    # 备份当前版本
    backup_current "$PROD_DIR"
    
    # 切换到目标版本
    sudo rm -f "$PROD_DIR"
    sudo ln -sf "$release_dir" "$PROD_DIR"
    
    # 重启服务
    restart_services
    
    log_info "回退完成！当前版本: $target_version"
}

# 环境配置
setup_environment() {
    local target_dir=$1
    
    log_info "配置环境: $target_dir"
    cd "$target_dir"
    
    # 安装Composer依赖
    if [ -f "composer.json" ]; then
        log_info "安装Composer依赖..."
        sudo composer install --no-dev --optimize-autoloader
    fi
    
    # 设置权限
    sudo chown -R www-data:www-data "$target_dir"
    sudo chmod -R 755 "$target_dir"
    sudo chmod -R 777 "$target_dir/runtime"
    sudo chmod -R 777 "$target_dir/public/uploads"
    
    # 复制环境配置文件
    if [ ! -f ".env" ] && [ -f ".env.example" ]; then
        sudo cp .env.example .env
        log_warn "请检查并配置 .env 文件"
    fi
}

# 重启服务
restart_services() {
    log_info "重启相关服务..."
    
    # 重启PHP-FPM
    sudo systemctl restart php7.4-fpm 2>/dev/null || sudo systemctl restart php8.0-fpm 2>/dev/null || true
    
    # 重启Nginx
    sudo systemctl restart nginx
    
    # 清理缓存
    if [ -d "$PROD_DIR/runtime" ]; then
        sudo rm -rf "$PROD_DIR/runtime/cache/*"
    fi
}

# 主逻辑
main() {
    create_directories
    
    case $ENVIRONMENT in
        "test")
            deploy_test
            ;;
        "production")
            deploy_production
            ;;
        "rollback")
            rollback "$@"
            ;;
        *)
            log_error "未知的环境: $ENVIRONMENT"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
