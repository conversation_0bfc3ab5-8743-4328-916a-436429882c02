{"version": 3, "file": "pages/shop_cart.js", "sources": ["webpack:///./components/number-box.vue?64ab", "webpack:///./components/number-box.vue?e931", "webpack:///./components/number-box.vue?4591", "webpack:///./components/number-box.vue?c46e", "webpack:///./components/number-box.vue", "webpack:///./components/number-box.vue?9baa", "webpack:///./components/number-box.vue?bc32", "webpack:///./pages/shop_cart.vue?06b8", "webpack:///./static/images/icon_cart_del.png", "webpack:///./static/images/cart_null.png", "webpack:///./pages/shop_cart.vue?ddc0", "webpack:///./pages/shop_cart.vue?b8e3", "webpack:///./pages/shop_cart.vue?8f7e", "webpack:///./pages/shop_cart.vue", "webpack:///./pages/shop_cart.vue?4ab3", "webpack:///./pages/shop_cart.vue?e99c"], "sourcesContent": ["// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/sass-resources-loader/lib/loader.js??ref--7-oneOf-1-4!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./number-box.vue?vue&type=style&index=0&id=1d9d8f36&lang=scss&scoped=true&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"663bee12\", content, true, context)\n};", "export * from \"-!../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/sass-resources-loader/lib/loader.js??ref--7-oneOf-1-4!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./number-box.vue?vue&type=style&index=0&id=1d9d8f36&lang=scss&scoped=true&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".number-box[data-v-1d9d8f36]{display:inline-flex;align-items:center}.number-box .number-input[data-v-1d9d8f36]{position:relative;text-align:center;padding:0;margin:0 6px;align-items:center;justify-content:center}.number-box .minus[data-v-1d9d8f36],.number-box .plus[data-v-1d9d8f36]{width:32px;display:flex;justify-content:center;align-items:center;cursor:pointer}.number-box .plus[data-v-1d9d8f36]{border-radius:0 2px 2px 0}.number-box .minus[data-v-1d9d8f36]{border-radius:2px 0 0 2px}.number-box .disabled[data-v-1d9d8f36]{color:#c8c9cc!important;background:#f7f8fa!important}.number-box .input-disabled[data-v-1d9d8f36]{color:#c8c9cc!important;background-color:#f2f3f5!important}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"number-box\"},[_vm._ssrNode(\"<div\"+(_vm._ssrClass(null,{ minus: true, disabled: _vm.disabled || _vm.inputVal <= _vm.min }))+(_vm._ssrStyle(null,{\n            background: _vm.bgColor,\n            height: _vm.inputHeight + 'px',\n            color: _vm.color,\n        }, null))+\" data-v-1d9d8f36><div\"+(_vm._ssrStyle(null,{ fontSize: _vm.size + 'px' }, null))+\" data-v-1d9d8f36>-</div></div> <input\"+(_vm._ssrAttr(\"disabled\",_vm.disabledInput || _vm.disabled))+\" type=\\\"text\\\"\"+(_vm._ssrAttr(\"value\",(_vm.inputVal)))+(_vm._ssrClass(null,{ 'number-input': true, 'input-disabled': _vm.disabled }))+(_vm._ssrStyle(null,{\n            color: _vm.color,\n            fontSize: _vm.size + 'px',\n            background: _vm.bgColor,\n            height: _vm.inputHeight + 'px',\n            width: _vm.inputWidth + 'px',\n        }, null))+\" data-v-1d9d8f36> <div\"+(_vm._ssrClass(\"plus\",{ disabled: _vm.disabled || _vm.inputVal >= _vm.max }))+(_vm._ssrStyle(null,{\n            background: _vm.bgColor,\n            height: _vm.inputHeight + 'px',\n            color: _vm.color,\n        }, null))+\" data-v-1d9d8f36><div\"+(_vm._ssrStyle(null,{ fontSize: _vm.size + 'px' }, null))+\" data-v-1d9d8f36>+</div></div>\")])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nexport default {\n    components: {},\n    props: {\n        // 预显示的数字\n        value: {\n            type: Number,\n            default: 1,\n        },\n        // 背景颜色\n        bgColor: {\n            type: String,\n            default: ' #F2F3F5',\n        },\n        // 最小值\n        min: {\n            type: Number,\n            default: 0,\n        },\n        // 最大值\n        max: {\n            type: Number,\n            default: 99999,\n        },\n        // 步进值，每次加或减的值\n        step: {\n            type: Number,\n            default: 1,\n        },\n        // 是否禁用加减操作\n        disabled: {\n            type: Boolean,\n            default: false,\n        },\n        // input的字体大小，单位px\n        size: {\n            type: [Number, String],\n            default: 14,\n        },\n        // input宽度，单位px\n        inputWidth: {\n            type: [Number, String],\n            default: 64,\n        },\n        //字体颜色\n        color: {\n            type: String,\n            default: '#333',\n        },\n        // input高度，单位px\n        inputHeight: {\n            type: [Number, String],\n            default: 32,\n        },\n        // index索引，用于列表中使用，让用户知道是哪个numberbox发生了变化，一般使用for循环出来的index值即可\n        index: {\n            type: [Number, String],\n            default: '',\n        },\n        // 是否禁用输入框，与disabled作用于输入框时，为OR的关系，即想要禁用输入框，又可以加减的话\n        // 设置disabled为false，disabledInput为true即可\n        disabledInput: {\n            type: Boolean,\n            default: false,\n        },\n\n        // 是否只能输入大于或等于0的整数(正整数)\n        positiveInteger: {\n            type: Boolean,\n            default: true,\n        },\n        asyncChange: {\n            type: Boolean,\n            default: false,\n        },\n    },\n    watch: {\n        value(v1, v2) {\n            if (!this.changeFromInner) {\n                this.inputVal = v1\n                this.$nextTick(function () {\n                    this.changeFromInner = false\n                })\n            }\n        },\n        inputVal(v1, v2) {\n            if (v1 == '') return\n            let value = 0\n            let tmp = /^(?:-?\\d+|-?\\d{1,3}(?:,\\d{3})+)?(?:\\.\\d+)?$/.test(v1)\n            if (tmp && v1 >= this.min && v1 <= this.max) value = v1\n            else value = v2\n            if (this.positiveInteger) {\n                if (v1 < 0 || String(v1).indexOf('.') !== -1) {\n                    value = v2\n                    this.$nextTick(() => {\n                        this.inputVal = v2\n                    })\n                }\n            }\n            if (this.asyncChange) {\n                return\n            }\n            // 发出change事件\n            this.handleChange(value, 'change')\n        },\n    },\n    data() {\n        return {\n            inputVal: 1, // 输入框中的值，不能直接使用props中的value，因为应该改变props的状态\n            timer: null, // 用作长按的定时器\n            changeFromInner: false, // 值发生变化，是来自内部还是外部\n            innerChangeTimer: null, // 内部定时器\n        }\n    },\n    created() {\n        this.inputVal = Number(this.value)\n    },\n    computed: {},\n    methods: {\n        btnTouchStart(callback) {\n            this[callback]()\n        },\n        minus() {\n            this.computeVal('minus')\n        },\n        plus() {\n            this.computeVal('plus')\n        },\n        calcPlus(num1, num2) {\n            let baseNum, baseNum1, baseNum2\n            try {\n                baseNum1 = num1.toString().split('.')[1].length\n            } catch (e) {\n                baseNum1 = 0\n            }\n            try {\n                baseNum2 = num2.toString().split('.')[1].length\n            } catch (e) {\n                baseNum2 = 0\n            }\n            baseNum = Math.pow(10, Math.max(baseNum1, baseNum2))\n            let precision = baseNum1 >= baseNum2 ? baseNum1 : baseNum2\n            return ((num1 * baseNum + num2 * baseNum) / baseNum).toFixed(\n                precision\n            )\n        },\n        calcMinus(num1, num2) {\n            let baseNum, baseNum1, baseNum2\n            try {\n                baseNum1 = num1.toString().split('.')[1].length\n            } catch (e) {\n                baseNum1 = 0\n            }\n            try {\n                baseNum2 = num2.toString().split('.')[1].length\n            } catch (e) {\n                baseNum2 = 0\n            }\n            baseNum = Math.pow(10, Math.max(baseNum1, baseNum2))\n            let precision = baseNum1 >= baseNum2 ? baseNum1 : baseNum2\n            return ((num1 * baseNum - num2 * baseNum) / baseNum).toFixed(\n                precision\n            )\n        },\n        computeVal(type) {\n            if (this.disabled) return\n            let value = 0\n            // 减\n            if (type === 'minus') {\n                value = this.calcMinus(this.inputVal, this.step)\n            } else if (type === 'plus') {\n                value = this.calcPlus(this.inputVal, this.step)\n            }\n            // 判断是否小于最小值和大于最大值\n            if (value < this.min || value > this.max) {\n                return\n            }\n            if (this.asyncChange) {\n                this.$emit('change', value)\n            } else {\n                this.inputVal = value\n                this.handleChange(value, type)\n            }\n        },\n        // 处理用户手动输入的情况\n        onBlur(event) {\n            let val = 0\n            let value = event.target.value\n\n            console.log(value)\n            if (!/(^\\d+$)/.test(value)) {\n                val = this.min\n            } else {\n                val = +value\n            }\n            if (val > this.max) {\n                val = this.max\n            } else if (val < this.min) {\n                val = this.min\n            }\n            this.$nextTick(() => {\n                this.inputVal = val\n            })\n            this.handleChange(val, 'blur')\n        },\n        // 输入框获得焦点事件\n        onFocus() {\n            this.$emit('focus')\n        },\n        handleChange(value, type) {\n            if (this.disabled) return\n            // 清除定时器，避免造成混乱\n            if (this.innerChangeTimer) {\n                clearTimeout(this.innerChangeTimer)\n                this.innerChangeTimer = null\n            }\n            this.changeFromInner = true\n            this.innerChangeTimer = setTimeout(() => {\n                this.changeFromInner = false\n            }, 150)\n            this.$emit('input', Number(value))\n            this.$emit(type, {\n                value: Number(value),\n                index: this.index,\n            })\n        },\n    },\n}\n", "import mod from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./number-box.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./number-box.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./number-box.vue?vue&type=template&id=1d9d8f36&scoped=true&\"\nimport script from \"./number-box.vue?vue&type=script&lang=js&\"\nexport * from \"./number-box.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./number-box.vue?vue&type=style&index=0&id=1d9d8f36&lang=scss&scoped=true&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  \"1d9d8f36\",\n  \"284477ee\"\n  \n)\n\nexport default component.exports", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/sass-resources-loader/lib/loader.js??ref--7-oneOf-1-4!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./shop_cart.vue?vue&type=style&index=0&id=750ce260&lang=scss&scoped=true&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"3f92fe51\", content, true, context)\n};", "module.exports = \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASCAMAAABhEH5lAAAAP1BMVEUAAACAgIBsbGxsbGxubm5xcXFwcHBubm5vb29ubm5vb29ubm5ubm5wcHBwcHBvb29wcHBvb29vb29wcHBwcHCw1+evAAAAFHRSTlMAAhooOj9AQUdITFhoj5/F29zj5uF9dOwAAABQSURBVBjTY2DADrgERMBAiAMuJMQGodkE4EIiaAx+ERTAj6oIwcQqxCjMwMnHwMtNIyEGHpAQOzOqI5hYCLhLiBUmwioICxtBmAeRQgcFAABfsQZNXvKDKwAAAABJRU5ErkJggg==\"", "module.exports = __webpack_public_path__ + \"img/cart_null.f9179fd.png\";", "export * from \"-!../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/sass-resources-loader/lib/loader.js??ref--7-oneOf-1-4!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./shop_cart.vue?vue&type=style&index=0&id=750ce260&lang=scss&scoped=true&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".shop-cart[data-v-750ce260]{padding:24px 0}.shop-cart .cart-list[data-v-750ce260]{min-height:600px}.shop-cart .cart-list .cart-hd[data-v-750ce260]{height:50px;color:#101010;padding:10px;margin-bottom:10px}.shop-cart .cart-list .cart-con[data-v-750ce260]{padding:0 10px}.shop-cart .cart-list .cart-con .shop[data-v-750ce260]{padding:20px 10px;border-bottom:1px solid #d7d7d7}.shop-cart .cart-list .cart-con .item[data-v-750ce260]{padding:20px 10px;border-bottom:1px dashed #e5e5e5}.shop-cart .cart-list .cart-con .item[data-v-750ce260]:last-child{border-bottom:0}.shop-cart .cart-list .check-box[data-v-750ce260]{padding-left:10px;width:40px}.shop-cart .cart-list .info[data-v-750ce260]{width:450px}.shop-cart .cart-list .info .pictrue[data-v-750ce260]{margin-right:10px}.shop-cart .cart-list .info .pictrue img[data-v-750ce260]{width:72px;height:72px}.shop-cart .cart-list .info .name[data-v-750ce260]{margin-bottom:10px}.shop-cart .cart-list .price[data-v-750ce260]{width:150px}.shop-cart .cart-list .num[data-v-750ce260]{width:250px}.shop-cart .cart-list .money[data-v-750ce260]{width:150px}.shop-cart .cart-list .delete-btn[data-v-750ce260]{cursor:pointer}.shop-cart .cart-footer[data-v-750ce260]{padding:20px}.shop-cart .cart-footer .total .btn[data-v-750ce260]{width:152px;height:50px;cursor:pointer;border-radius:4px}.shop-cart .data-null[data-v-750ce260]{text-align:center;padding-top:170px}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"shop-cart\"},[_vm._ssrNode(\"<div class=\\\"cart-list\\\" data-v-750ce260>\",\"</div>\",[_vm._ssrNode(\"<div\"+(_vm._ssrStyle(null,null, { display: (!_vm.isDataNull) ? '' : 'none' }))+\" data-v-750ce260>\",\"</div>\",[_vm._ssrNode(\"<div class=\\\"cart-hd flex bg-white\\\" data-v-750ce260>\",\"</div>\",[_vm._ssrNode(\"<div class=\\\"check-box\\\" data-v-750ce260>\",\"</div>\",[_c('el-checkbox',{on:{\"change\":function($event){return _vm.onBoxClick($event, 3, '')}},model:{value:(_vm.isSelectedAll),callback:function ($$v) {_vm.isSelectedAll=$$v},expression:\"isSelectedAll\"}},[_vm._v(\"全选\")])],1),_vm._ssrNode(\" <div class=\\\"info flex row-center\\\" data-v-750ce260>商品信息</div> <div class=\\\"price flex row-center\\\" data-v-750ce260>单价</div> <div class=\\\"num flex row-center\\\" data-v-750ce260>数量</div> <div class=\\\"money flex row-center\\\" data-v-750ce260>合计</div> <div class=\\\"operate flex row-center\\\" data-v-750ce260>操作</div>\")],2),_vm._ssrNode(\" \"),_vm._ssrNode(\"<div class=\\\"cart-con bg-white\\\" data-v-750ce260>\",\"</div>\",_vm._l((_vm.shopCartList),function(item,index){return _vm._ssrNode(\"<div class=\\\"m-b-10 bg-white\\\" data-v-750ce260>\",\"</div>\",[_vm._ssrNode(\"<div class=\\\"flex shop\\\" data-v-750ce260>\",\"</div>\",[_c('el-checkbox',{attrs:{\"value\":item.is_selected == 1},on:{\"change\":function($event){return _vm.onBoxClick($event, 1, index)}}}),_vm._ssrNode(\" <div class=\\\"xs normal m-l-10\\\" data-v-750ce260>\"+_vm._ssrEscape(\"\\n                            \"+_vm._s(item.shop.shop_name)+\"\\n                        \")+\"</div>\")],2),_vm._ssrNode(\" \"),_vm._l((item.cart),function(item2,index2){return _vm._ssrNode(\"<div class=\\\"item flex\\\" data-v-750ce260>\",\"</div>\",[_vm._ssrNode(\"<div class=\\\"check-box\\\" data-v-750ce260>\",\"</div>\",[_c('el-checkbox',{attrs:{\"value\":item2.selected == 1},on:{\"change\":function($event){return _vm.onBoxClick($event, 2, item2.cart_id)}}})],1),_vm._ssrNode(\" \"),_c('nuxt-link',{staticClass:\"info flex\",attrs:{\"to\":'/goods_details/' + item2.goods_id}},[_c('div',{staticClass:\"pictrue flexnone\"},[_c('img',{attrs:{\"src\":item2.image,\"alt\":\"\"}})]),_vm._v(\" \"),_c('div',[_c('div',{staticClass:\"name line2\"},[_vm._v(\"\\n                                    \"+_vm._s(item2.goods_name)+\"\\n                                \")]),_vm._v(\" \"),_c('div',{staticClass:\"muted\"},[_vm._v(\"\\n                                    \"+_vm._s(item2.spec_value_str)+\"\\n                                \")])])]),_vm._ssrNode(\" <div class=\\\"price flex row-center\\\" data-v-750ce260>\"+_vm._ssrEscape(\"\\n                            ¥\"+_vm._s(item2.price)+\"\\n                        \")+\"</div> \"),_vm._ssrNode(\"<div class=\\\"num flex row-center\\\" data-v-750ce260>\",\"</div>\",[_c('number-box',{attrs:{\"min\":1,\"async-change\":\"\"},on:{\"change\":function($event){return _vm.changeShopCartCount(\n                                        $event,\n                                        item2.cart_id\n                                    )}},model:{value:(item2.goods_num),callback:function ($$v) {_vm.$set(item2, \"goods_num\", $$v)},expression:\"item2.goods_num\"}})],1),_vm._ssrNode(\" <div class=\\\"money flex row-center\\\" data-v-750ce260>\"+_vm._ssrEscape(\"\\n                            ¥\"+_vm._s(item2.sub_price)+\"\\n                        \")+\"</div> \"),_c('el-popconfirm',{attrs:{\"title\":\"确定删除该商品吗？\",\"confirm-button-text\":\"确定\",\"cancel-button-text\":\"取消\",\"icon\":\"el-icon-info\",\"icon-color\":\"red\"},on:{\"confirm\":function($event){return _vm.goodsDelete(item2.cart_id)}}},[_c('div',{staticClass:\"operate flex row-center delete-btn\",attrs:{\"slot\":\"reference\"},slot:\"reference\"},[_c('img',{attrs:{\"src\":require(\"static/images/icon_cart_del.png\")}})])])],2)})],2)}),0),_vm._ssrNode(\" \"),_vm._ssrNode(\"<div class=\\\"cart-footer flex row-between bg-white\\\" data-v-750ce260>\",\"</div>\",[_vm._ssrNode(\"<div class=\\\"lighter flex\\\" data-v-750ce260>\",\"</div>\",[_vm._ssrNode(\"<div class=\\\"check-box\\\" data-v-750ce260>\",\"</div>\",[_c('el-checkbox',{on:{\"change\":function($event){return _vm.onBoxClick($event, 3, '')}},model:{value:(_vm.isSelectedAll),callback:function ($$v) {_vm.isSelectedAll=$$v},expression:\"isSelectedAll\"}},[_vm._v(\"全选\")])],1),_vm._ssrNode(\" <div style=\\\"margin: 0 24px\\\" data-v-750ce260></div> \"),_c('el-popconfirm',{attrs:{\"title\":\"确定删除选中商品吗？\",\"confirm-button-text\":\"确定\",\"cancel-button-text\":\"取消\",\"icon\":\"el-icon-info\",\"icon-color\":\"red\"},on:{\"confirm\":_vm.deleteSelectedGoods}},[_c('div',{staticClass:\"xs normal\",staticStyle:{\"cursor\":\"pointer\"},attrs:{\"slot\":\"reference\"},slot:\"reference\"},[_vm._v(\"\\n                            删除选中商品\\n                        \")])]),_vm._ssrNode(\" \"),_c('el-popconfirm',{attrs:{\"title\":\"确定清空吗？\",\"confirm-button-text\":\"确定\",\"cancel-button-text\":\"取消\",\"icon\":\"el-icon-info\",\"icon-color\":\"red\"},on:{\"confirm\":_vm.deleteAlldGoods}},[_c('div',{staticClass:\"m-l-14 xs muted\",staticStyle:{\"cursor\":\"pointer\"},attrs:{\"slot\":\"reference\"},slot:\"reference\"},[_vm._v(\"\\n                            清空购物车\\n                        \")])])],2),_vm._ssrNode(\" <div class=\\\"total flex\\\" data-v-750ce260><div class=\\\"flex m-r-14\\\" data-v-750ce260><div class=\\\"xs\\\" data-v-750ce260>\"+_vm._ssrEscape(\"已选\"+_vm._s(_vm.selected)+\"件商品\")+\"</div> <div class=\\\"primary m-l-20\\\" style=\\\"font-size: 22px\\\" data-v-750ce260>\"+_vm._ssrEscape(\"\\n                            ¥\"+_vm._s(_vm.totalAmount)+\"\\n                        \")+\"</div></div> <div class=\\\"white lg btn flex row-center\\\"\"+(_vm._ssrStyle(null,{\n                            background:\n                                _vm.selected == 0 ? '#A4ADB3' : '#FF2C3C',\n                        }, null))+\" data-v-750ce260>\\n                        去结算\\n                    </div></div>\")],2)],2),_vm._ssrNode(\" \"),_vm._ssrNode(\"<div class=\\\"column-center data-null\\\"\"+(_vm._ssrStyle(null,null, { display: (_vm.isDataNull) ? '' : 'none' }))+\" data-v-750ce260>\",\"</div>\",[_vm._ssrNode(\"<img\"+(_vm._ssrAttr(\"src\",require(\"@/static/images/cart_null.png\")))+\" style=\\\"width: 150px; height: 150px\\\" data-v-750ce260> <div class=\\\"muted xs m-t-10\\\" data-v-750ce260>购物车是空的～</div> \"),_vm._ssrNode(\"<div class=\\\"m-t-30\\\" data-v-750ce260>\",\"</div>\",[_c('el-button',{attrs:{\"round\":\"\",\"type\":\"primary\",\"size\":\"medium\"},on:{\"click\":_vm.toIndex}},[_vm._v(\"去逛逛～\")])],1)],2)],2)])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { mapActions } from 'vuex'\nexport default {\n    head() {\n        return {\n            title: this.$store.getters.headTitle,\n            link: [\n                {\n                    rel: 'icon',\n                    type: 'image/x-icon',\n                    href: this.$store.getters.favicon,\n                },\n            ],\n        }\n    },\n    data() {\n        return {\n            shopCartList: [],\n            totalAmount: 0,\n            totalNum: 0,\n            isDataNull: false,\n        }\n    },\n    mounted() {},\n    computed: {\n        // 是否全选\n        isSelectedAll: {\n            get() {\n                if (!this.shopCartList.length) return false\n                if (this.allInvalid()) return false\n                let index = this.shopCartList.findIndex(\n                    (item) => item.is_selected == 0\n                )\n                return index == -1 ? true : false\n            },\n            set(val) {\n                return val\n            },\n        },\n        // 已经选择的数量\n        selected: {\n            get() {\n                return this.shopCartList.reduce((pre, item) => {\n                    return pre.concat(item.cart.filter((i) => i.selected == 1))\n                }, []).length\n            },\n        },\n    },\n    methods: {\n        ...mapActions(['getPublicData']),\n        async getCartList() {\n            let res = await this.$get('cart/lists')\n            if (res.code == 1) {\n                this.shopCartList = Object.assign([], res.data.lists)\n                this.totalAmount = res.data.total_amount\n                this.totalNum = res.data.total_num\n\n                if (this.shopCartList.length > 0) {\n                    this.isDataNull = false\n                } else {\n                    this.isDataNull = true\n                }\n            }\n        },\n\n        // 更改选中状态 type为1选中店铺/2选中商品/3全选\n        onBoxClick(e, type, number) {\n            let cartId = []\n            switch (type) {\n                case 1:\n                    cartId = this.shopCartList[number].cart.map(\n                        (item) => item.cart_id\n                    )\n                    break\n                case 2:\n                    cartId.push(number)\n                    break\n                case 3:\n                    cartId = this.shopCartList.reduce((pre, item) => {\n                        return pre.concat(item.cart.map((i) => i.cart_id))\n                    }, cartId)\n                    break\n            }\n            this.changeSelected(cartId, e)\n        },\n        cartInvalid(item) {\n            return item.goods_status == 0 || item.goods_del != 0 ? true : false\n        },\n        shopInvalid(item) {\n            return item.cart.every((citem) => this.cartInvalid(citem))\n        },\n        allInvalid() {\n            return this.shopCartList.every((item) => this.shopInvalid(item))\n        },\n\n        // 选中/取消选中购物车\n        async changeSelected(id, selected) {\n            let res = await this.$post('cart/selected', {\n                cart_id: id,\n                selected: selected,\n            })\n            if (res.code == 1) {\n                this.getCartList()\n            }\n        },\n\n        // 修改购物车商品数量\n        async changeShopCartCount(number, cartId) {\n            let res = await this.$post('cart/change', {\n                cart_id: cartId,\n                goods_num: number,\n            })\n            if (res.code == 1) {\n                this.getCartList()\n                this.getPublicData()\n            }\n        },\n        // 删除购物车商品\n        async goodsDelete(cartId) {\n            let res = await this.$post('cart/del', {\n                cart_id: cartId,\n            })\n            if (res.code == 1) {\n                this.getPublicData()\n                this.getCartList()\n                this.$message({\n                    message: '删除商品成功',\n                    type: 'success',\n                })\n            }\n        },\n        // 删除选中购物车\n        deleteSelectedGoods() {\n            let selectedGoodsArr = this.shopCartList.reduce((pre, item) => {\n                return pre.concat(item.cart.filter((i) => i.selected == 1))\n            }, [])\n            if (selectedGoodsArr.length <= 0) {\n                this.$message({\n                    message: '没有选择商品',\n                    type: 'error',\n                })\n                return\n            }\n            let cartIdArr = selectedGoodsArr.map((item) => item.cart_id)\n            this.goodsDelete(cartIdArr)\n        },\n        // 清空购物车\n        deleteAlldGoods() {\n            let allGoodsArr = this.shopCartList.reduce((pre, item) => {\n                return pre.concat(item.cart.filter((i) => i.cart_id))\n            }, [])\n            if (allGoodsArr.length <= 0) {\n                this.$message({\n                    message: '没有商品',\n                    type: 'error',\n                })\n                return\n            }\n            let cartIdArr = allGoodsArr.map((item) => item.cart_id)\n            this.goodsDelete(cartIdArr)\n        },\n        getSelectCart() {\n            const { shopCartList } = this\n            return shopCartList.reduce((pre, item) => {\n                return pre.concat(\n                    item.cart\n                        .filter((i) => i.selected && !this.cartInvalid(i))\n                        .map((i) => i.cart_id)\n                )\n            }, [])\n        },\n        // 去购买商品\n        toOrderBuy() {\n            let { shopCartList } = this\n            let goods = []\n            let carts = this.getSelectCart()\n            if (carts.length == 0) return this.$message.err('您还没有选择商品哦')\n            // 处理出商品数组数据\n            shopCartList.forEach((item) => {\n                if (item.cart.length != 0) {\n                    item.cart.forEach((el, i) => {\n                        // 选中的商品才能进入\n                        if (el.selected == 1) {\n                            goods.push({\n                                item_id: el.item_id,\n                                num: el.goods_num,\n                                goods_id: el.goods_id,\n                                shop_id: item.shop.shop_id,\n                            })\n                        }\n                    })\n                }\n            })\n            const params = {\n                carts: carts,\n                goods: goods,\n                type: 'cart',\n            }\n            this.$router.push({\n                path: '/confirm_order',\n                query: {\n                    data: encodeURIComponent(JSON.stringify(params)),\n                },\n            })\n        },\n        toIndex() {\n            this.$router.push('/')\n        },\n    },\n\n    created() {\n        this.getCartList()\n    },\n}\n", "import mod from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./shop_cart.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./shop_cart.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./shop_cart.vue?vue&type=template&id=750ce260&scoped=true&\"\nimport script from \"./shop_cart.vue?vue&type=script&lang=js&\"\nexport * from \"./shop_cart.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./shop_cart.vue?vue&type=style&index=0&id=750ce260&lang=scss&scoped=true&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  \"750ce260\",\n  \"6f479227\"\n  \n)\n\nexport default component.exports\n\n/* nuxt-component-imports */\ninstallComponents(component, {NumberBox: require('/Users/<USER>/Desktop/vue/pc/components/number-box.vue').default})\n"], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACXA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACNA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;AChBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AACA;AACA;AAFA;AAKA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AApEA;AAyEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AA7BA;AACA;AA6BA;AACA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AACA;AALA;AAMA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AAGA;AACA;AAAA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AAGA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAEA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AA5GA;AArHA;;AC5CA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;ACvBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;ACXA;;;;;;;ACAA;;;;;;;;ACAA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACNA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACRA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAHA;AAHA;AAUA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAJA;AAMA;AACA;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AAAA;AACA;AACA;AACA;AAZA;AAaA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AANA;AAhBA;AAwBA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AAGA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAbA;AACA;AAcA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAFA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAFA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AADA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AACA;AACA;AAKA;AACA;AACA;AAAA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAJA;AAMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAHA;AAKA;AACA;AACA;AACA;AADA;AAFA;AAMA;AACA;AAAA;AACA;AACA;AACA;AAhKA;AACA;AAiKA;AACA;AACA;AACA;AAnNA;;ACjMA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;A", "sourceRoot": ""}