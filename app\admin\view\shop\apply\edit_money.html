{layout name="layout2" /}
<style>
    .layui-form-label{
        width: 120px;
    }
    .reqRed::before {
        content: '*';
        color: red;
        margin-right: 5px;
    }
</style>
<div class="layui-form" lay-filter="adjust" id="layuiadmin-form-user" style="padding: 20px 30px 0 0;">
    <input type="hidden" value="{$deposit.id}" name="deposit_id">
    <input type="hidden" value="{$deposit.shop_id}" name="shop_id">
    <div class="layui-fluid">
        <div class="layui-card">
            <div class="layui-card-header">保证金调整</div>
            <div class="layui-card-body">
                <div class="layui-form-item">
                    <label class="layui-form-label">商家名称：</label>
                    <div class="layui-input-inline">
                        <label class="layui-form-mid">{$deposit.shop_name}</label>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">当前保证金余额：</label>
                    <div class="layui-input-inline">
                        <label class="layui-form-mid">{$deposit.current_amount}</label>
                    </div>
                    <label class="layui-form-mid">元</label>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label reqRed">变动类型：</label>
                    <div class="layui-input-block">
                        <input type="radio" name="change_type" value="2" title="增加保证金" checked>
                        <input type="radio" name="change_type" value="3" title="扣减保证金">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label reqRed">变动金额：</label>
                    <div class="layui-input-inline">
                        <input type="number" min="0.01" step="0.01" name="amount" value="" lay-verify="required|number|min_value" lay-vertype="tips" placeholder="请输入变动金额" autocomplete="off" class="layui-input">
                    </div>
                    <label class="layui-form-mid">元</label>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label reqRed">变动原因：</label>
                    <div class="layui-input-block">
                        <textarea type="text" name="reason" lay-verify="required" lay-vertype="tips" placeholder="请输入变动原因" autocomplete="off" class="layui-textarea" style="width: 50%;"></textarea>
                        <div class="layui-form-mid layui-word-aux" style="margin-top: 5px;">不超过100字</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="layui-form-item layui-hide">
        <input type="button" lay-submit lay-filter="deposit_adjust_submit" id="deposit_adjust_submit" value="确认">
    </div>
</div>
<script>
    layui.config({
        version:"{$front_version}",
        base: '/static/lib/' //静态资源所在路径
    }).use(['form', 'element'], function(){
        var $ = layui.$, form = layui.form;

        // 自定义验证规则
        form.verify({
            min_value: function(value) {
                if (value <= 0) {
                    return '变动金额必须大于0';
                }
            }
        });
    })
</script>
