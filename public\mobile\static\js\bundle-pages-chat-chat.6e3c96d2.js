(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["bundle-pages-chat-chat"],{"01c2":function(t,e,i){"use strict";i("7a82");var n=i("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=n(i("0ead")),o={name:"emoji",data:function(){return{emoji:a.default}},methods:{handleClick:function(t){this.$emit("input","[".concat(t,"]"))}}};e.default=o},"0a6e":function(t,e,i){"use strict";i("7a82");var n=i("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("99af"),i("e9c4");var a=n(i("276c")),o=n(i("e954")),s=n(i("fc11")),r=i("a5ae"),c=function(){function t(e,i){(0,a.default)(this,t),(0,s.default)(this,"events",{connect:null,close:null,message:null,error:null,open:null}),this.connected=!1,this.error=!1,this.url="".concat(e).concat((0,r.paramsToStr)(i)),this.socketTask={},this.reconnectLock=!0,this.reconnectTimeout=null,this.reconnectNums=0,this.timeout=1e4,this.clientTimeout=null,this.serverTimeout=null}return(0,o.default)(t,[{key:"addEvent",value:function(t,e){this.events[t]=e}},{key:"dispatch",value:function(t,e){var i=this.events[t];i&&i(e)}},{key:"connect",value:function(){this.connected||(this.dispatch("connect"),this.socketTask=uni.connectSocket({url:this.url,complete:function(){}}),this.socketTask.onOpen(this.onOpen.bind(this)),this.socketTask.onError(this.onError.bind(this)),this.socketTask.onMessage(this.onMessage.bind(this)),this.socketTask.onClose(this.onClose.bind(this)))}},{key:"close",value:function(){this.reconnectLock=!1,clearTimeout(this.clientTimeout),clearTimeout(this.serverTimeout),this.socketTask.close&&this.socketTask.close()}},{key:"reconnect",value:function(){var t=this;this.reconnectLock&&(this.reconnectNums>=5||(this.reconnectNums++,this.reconnectLock=!1,clearTimeout(this.reconnectTimeout),this.reconnectTimeout=setTimeout((function(){t.connect(),t.reconnectLock=!0}),4e3)))}},{key:"start",value:function(){var t=this;clearTimeout(this.clientTimeout),clearTimeout(this.serverTimeout),this.clientTimeout=setTimeout((function(){t.send({event:"ping"}),t.serverTimeout=setTimeout((function(){t.socketTask.close()}),t.timeout)}),this.timeout)}},{key:"reset",value:function(){this.reconnectNums=0,this.start()}},{key:"send",value:function(t){if(this.connected){var e=JSON.stringify(t);this.socketTask.send({data:e})}}},{key:"onOpen",value:function(){this.connected=!0,this.start(),this.dispatch("open")}},{key:"onError",value:function(t){this.error=!0,this.connected=!1,this.dispatch("error")}},{key:"onMessage",value:function(t){var e=t.data;this.dispatch("message",JSON.parse(e)),this.reset()}},{key:"onClose",value:function(t){this.dispatch("close"),this.connected=!1,this.reconnect()}}]),t}(),u=c;e.default=u},"0ead":function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;e.default=["em-smile","em-laughing","em-blush","em-smiley","em-relaxed","em-smirk","em-heart_eyes","em-kissing_heart","em-kissing_closed_eyes","em-flushed","em-relieved","em-satisfied","em-grin","em-wink","em-stuck_out_tongue_winking_eye","em-stuck_out_tongue_closed_eyes","em-grinning","em-kissing","em-kissing_smiling_eyes","em-stuck_out_tongue","em-sleeping","em-worried","em-frowning","em-anguished","em-open_mouth","em-grimacing","em-confused","em-hushed","em-expressionless","em-unamused","em-sweat_smile","em-sweat","em-disappointed_relieved","em-weary","em-pensive","em-disappointed","em-confounded","em-fearful","em-cold_sweat","em-persevere","em-cry","em-sob","em-joy","em-astonished","em-scream","em-tired_face","em-angry","em-rage","em-triumph","em-sleepy","em-yum","em-mask","em-dizzy_face","em-sunglasses","em-imp","em-smiling_imp","em-neutral_face","em-no_mouth","em-innocent","em-alien","em-heart","em-broken_heart","em-hankey","em-thumbsup","em-thumbsdown","em-ok_hand","em-facepunch","em-fist","em-v","em-point_up","em-point_down","em-point_left","em-point_right","em-pray"]},"1d4d":function(t,e,i){"use strict";var n=i("f1ff"),a=i.n(n);a.a},"1e2e":function(t,e,i){var n=i("3096");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=i("4f06").default;a("ccafd518",n,!0,{sourceMap:!1,shadowMode:!1})},"212e":function(t,e,i){var n=i("24fb");e=n(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.u-loading-circle[data-v-bf7076f2]{display:inline-flex;vertical-align:middle;width:%?28?%;height:%?28?%;background:0 0;border-radius:50%;border:2px solid;border-color:#e5e5e5 #e5e5e5 #e5e5e5 #8f8d8e;-webkit-animation:u-circle-data-v-bf7076f2 1s linear infinite;animation:u-circle-data-v-bf7076f2 1s linear infinite}.u-loading-flower[data-v-bf7076f2]{width:20px;height:20px;display:inline-block;vertical-align:middle;-webkit-animation:a 1s steps(12) infinite;animation:u-flower-data-v-bf7076f2 1s steps(12) infinite;background:transparent url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMjAiIGhlaWdodD0iMTIwIiB2aWV3Qm94PSIwIDAgMTAwIDEwMCI+PHBhdGggZmlsbD0ibm9uZSIgZD0iTTAgMGgxMDB2MTAwSDB6Ii8+PHJlY3Qgd2lkdGg9IjciIGhlaWdodD0iMjAiIHg9IjQ2LjUiIHk9IjQwIiBmaWxsPSIjRTlFOUU5IiByeD0iNSIgcnk9IjUiIHRyYW5zZm9ybT0idHJhbnNsYXRlKDAgLTMwKSIvPjxyZWN0IHdpZHRoPSI3IiBoZWlnaHQ9IjIwIiB4PSI0Ni41IiB5PSI0MCIgZmlsbD0iIzk4OTY5NyIgcng9IjUiIHJ5PSI1IiB0cmFuc2Zvcm09InJvdGF0ZSgzMCAxMDUuOTggNjUpIi8+PHJlY3Qgd2lkdGg9IjciIGhlaWdodD0iMjAiIHg9IjQ2LjUiIHk9IjQwIiBmaWxsPSIjOUI5OTlBIiByeD0iNSIgcnk9IjUiIHRyYW5zZm9ybT0icm90YXRlKDYwIDc1Ljk4IDY1KSIvPjxyZWN0IHdpZHRoPSI3IiBoZWlnaHQ9IjIwIiB4PSI0Ni41IiB5PSI0MCIgZmlsbD0iI0EzQTFBMiIgcng9IjUiIHJ5PSI1IiB0cmFuc2Zvcm09InJvdGF0ZSg5MCA2NSA2NSkiLz48cmVjdCB3aWR0aD0iNyIgaGVpZ2h0PSIyMCIgeD0iNDYuNSIgeT0iNDAiIGZpbGw9IiNBQkE5QUEiIHJ4PSI1IiByeT0iNSIgdHJhbnNmb3JtPSJyb3RhdGUoMTIwIDU4LjY2IDY1KSIvPjxyZWN0IHdpZHRoPSI3IiBoZWlnaHQ9IjIwIiB4PSI0Ni41IiB5PSI0MCIgZmlsbD0iI0IyQjJCMiIgcng9IjUiIHJ5PSI1IiB0cmFuc2Zvcm09InJvdGF0ZSgxNTAgNTQuMDIgNjUpIi8+PHJlY3Qgd2lkdGg9IjciIGhlaWdodD0iMjAiIHg9IjQ2LjUiIHk9IjQwIiBmaWxsPSIjQkFCOEI5IiByeD0iNSIgcnk9IjUiIHRyYW5zZm9ybT0icm90YXRlKDE4MCA1MCA2NSkiLz48cmVjdCB3aWR0aD0iNyIgaGVpZ2h0PSIyMCIgeD0iNDYuNSIgeT0iNDAiIGZpbGw9IiNDMkMwQzEiIHJ4PSI1IiByeT0iNSIgdHJhbnNmb3JtPSJyb3RhdGUoLTE1MCA0NS45OCA2NSkiLz48cmVjdCB3aWR0aD0iNyIgaGVpZ2h0PSIyMCIgeD0iNDYuNSIgeT0iNDAiIGZpbGw9IiNDQkNCQ0IiIHJ4PSI1IiByeT0iNSIgdHJhbnNmb3JtPSJyb3RhdGUoLTEyMCA0MS4zNCA2NSkiLz48cmVjdCB3aWR0aD0iNyIgaGVpZ2h0PSIyMCIgeD0iNDYuNSIgeT0iNDAiIGZpbGw9IiNEMkQyRDIiIHJ4PSI1IiByeT0iNSIgdHJhbnNmb3JtPSJyb3RhdGUoLTkwIDM1IDY1KSIvPjxyZWN0IHdpZHRoPSI3IiBoZWlnaHQ9IjIwIiB4PSI0Ni41IiB5PSI0MCIgZmlsbD0iI0RBREFEQSIgcng9IjUiIHJ5PSI1IiB0cmFuc2Zvcm09InJvdGF0ZSgtNjAgMjQuMDIgNjUpIi8+PHJlY3Qgd2lkdGg9IjciIGhlaWdodD0iMjAiIHg9IjQ2LjUiIHk9IjQwIiBmaWxsPSIjRTJFMkUyIiByeD0iNSIgcnk9IjUiIHRyYW5zZm9ybT0icm90YXRlKC0zMCAtNS45OCA2NSkiLz48L3N2Zz4=) no-repeat;background-size:100%}@-webkit-keyframes u-flower-data-v-bf7076f2{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}@keyframes u-flower-data-v-bf7076f2{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}@-webkit-keyframes u-circle-data-v-bf7076f2{0%{-webkit-transform:rotate(0);transform:rotate(0)}100%{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}',""]),t.exports=e},3096:function(t,e,i){var n=i("24fb");e=n(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.u-image[data-v-1bf07c9a]{position:relative;transition:opacity .5s ease-in-out}.u-image__image[data-v-1bf07c9a]{width:100%;height:100%}.u-image__loading[data-v-1bf07c9a], .u-image__error[data-v-1bf07c9a]{position:absolute;top:0;left:0;width:100%;height:100%;display:flex;flex-direction:row;align-items:center;justify-content:center;background-color:#f3f4f6;color:#909399;font-size:%?46?%}',""]),t.exports=e},"33a0":function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){return n}));var n={uLoading:i("c1c1").default,priceFormat:i("a272").default,emoji:i("dab2").default,uIcon:i("6976").default,uImage:i("f919").default},a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{staticClass:"chat flex-col"},[n("v-uni-view",{staticClass:"content",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.showEmoji=!1}}},[n("v-uni-scroll-view",{staticStyle:{height:"100%"},attrs:{"scroll-y":!0,"scroll-top":t.scrollTop,"scroll-into-view":t.intoView},on:{scrolltoupper:function(e){arguments[0]=e=t.$handleEvent(e),t.scrollToupper.apply(void 0,arguments)}}},["loading"==t.pageStatus?n("v-uni-view",{staticClass:"loading flex row-center"},[n("u-loading",{attrs:{mode:"flower",size:"40"}})],1):t._e(),n("v-uni-view",{staticClass:"chat-lists"},t._l(t.recoreds,(function(e,i){return n("v-uni-view",{key:e.id,staticClass:"chat-item",class:{right:"user"==e.from_type,left:"kefu"==e.from_type,visibility:t.showIndex>i},attrs:{id:"chat-item_"+e.id}},[1==e.type?[t.timeFormat(e,i)?n("v-uni-view",{staticClass:"text-center m-b-30 white"},[n("v-uni-view",{staticClass:"chat-tips xs"},[t._v(t._s(t.timeFormat(e,i)))])],1):t._e(),n("v-uni-view",{staticClass:"chat-info"},[n("v-uni-image",{staticClass:"avatar",attrs:{src:t.$getImageUri(e.from_avatar)}}),1==e.msg_type?n("v-uni-view",{staticClass:"text-box"},[n("v-uni-rich-text",{attrs:{nodes:t.replaceEmoji(e.msg),space:"nbsp"}})],1):t._e(),2==e.msg_type?n("v-uni-view",{staticClass:"image-box"},[n("v-uni-image",{staticClass:"image",attrs:{mode:"widthFix",src:t.$getImageUri(e.msg)},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.previewImage(t.$getImageUri(e.msg))}}})],1):t._e(),3==e.msg_type?n("v-uni-view",{staticClass:"goods m-r-20 goods-box"},[n("v-uni-view",{staticClass:"goods-img m-r-20"},[n("v-uni-image",{staticStyle:{width:"140rpx",height:"140rpx"},attrs:{src:t.$getImageUri(e.goods.image)}})],1),n("v-uni-view",{staticClass:"goods-info flex-1"},[n("v-uni-view",{staticClass:"line-2"},[t._v(t._s(e.goods.name))]),n("v-uni-view",{staticClass:"flex m-t-10 row-between"},[n("price-format",{attrs:{color:t.colorConfig.primary,"subscript-size":26,"first-size":38,"second-size":26,price:e.goods.min_price}})],1)],1)],1):t._e()],1)]:[n("v-uni-view",{staticClass:"text-center white"},[n("v-uni-view",{staticClass:"muted xs"},[t._v(t._s(e.msg))])],1)]],2)})),1),t.isError?n("v-uni-view",{staticClass:"error"},[n("v-uni-view",{staticClass:"error-msg text-center xs"},[t._v(t._s(t.errorMsg))])],1):t._e(),n("v-uni-view",{attrs:{id:"bottom"}})],1)],1),n("v-uni-view",{staticClass:"footer",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.showGoods=!1}}},[n("v-uni-view",{staticClass:"footer-input flex"},[n("v-uni-view",{staticClass:"album",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.uploadFile.apply(void 0,arguments)}}},[n("v-uni-image",{staticClass:"icon",attrs:{src:i("87ed")}})],1),n("v-uni-view",{staticClass:"input-contain flex"},[n("v-uni-input",{staticClass:"text-area",attrs:{"confirm-type":"send",maxlength:"-1"},on:{focus:function(e){arguments[0]=e=t.$handleEvent(e),t.scrollToBottom.apply(void 0,arguments)},confirm:function(e){arguments[0]=e=t.$handleEvent(e),t.sendText.apply(void 0,arguments)}},model:{value:t.msg,callback:function(e){t.msg=e},expression:"msg"}}),n("v-uni-image",{staticClass:"icon",attrs:{src:i("a7f5")},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.handleEmojiShow.apply(void 0,arguments)}}})],1),n("v-uni-button",{staticClass:"send-btn",attrs:{size:"sm"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.sendText.apply(void 0,arguments)}}},[t._v("发送")])],1),n("v-uni-view",{staticClass:"emoji-wrap",class:{"emoji-show":t.showEmoji}},[n("v-uni-scroll-view",{staticStyle:{height:"100%"},attrs:{"scroll-y":"true"}},[n("emoji",{on:{input:function(e){arguments[0]=e=t.$handleEvent(e),t.handleEmojiInput.apply(void 0,arguments)}}})],1)],1)],1),t.showGoods?n("v-uni-view",{staticClass:"goods"},[n("v-uni-view",{staticClass:"close",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.showGoods=!1}}},[n("u-icon",{attrs:{name:"close-circle-fill",color:"#ccc",size:"40"}})],1),n("v-uni-view",{staticClass:"goods-img m-r-20"},[n("u-image",{attrs:{width:"140rpx",height:"140rpx",src:t.goodsInfo.image}})],1),n("v-uni-view",{staticClass:"goods-info flex-1"},[n("v-uni-view",{staticClass:"line-2"},[t._v(t._s(t.goodsInfo.name))]),n("v-uni-view",{staticClass:"flex m-t-10 row-between"},[n("price-format",{attrs:{color:t.colorConfig.primary,"subscript-size":26,"first-size":38,"second-size":26,price:t.goodsInfo.min_price}}),n("v-uni-view",{staticClass:"send-btn",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.sendGoods.apply(void 0,arguments)}}},[t._v("发送链接")])],1)],1)],1):t._e()],1)},o=[]},3493:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){}));var n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"emoji"},[i("v-uni-view",{staticClass:"emoji-lists"},t._l(t.emoji,(function(e,n){return i("v-uni-view",{key:n,staticClass:"emoji-item",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.handleClick(e)}}},[i("v-uni-text",{staticClass:"em",class:e})],1)})),1)],1)},a=[]},"3ab4":function(t,e,i){"use strict";i.r(e);var n=i("4f01"),a=i.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);e["default"]=a.a},"3b7b":function(t,e,i){"use strict";i.r(e);var n=i("33a0"),a=i("bb9c");for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);i("3d30");var s=i("f0c5"),r=Object(s["a"])(a["default"],n["b"],n["c"],!1,null,"63ea1534",null,!1,n["a"],void 0);e["default"]=r.exports},"3d30":function(t,e,i){"use strict";var n=i("8eac"),a=i.n(n);a.a},"3f30":function(t,e,i){"use strict";i.r(e);var n=i("4219"),a=i.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);e["default"]=a.a},4219:function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("a9e3");var n={name:"u-image",props:{src:{type:String,default:""},mode:{type:String,default:"aspectFill"},width:{type:[String,Number],default:"100%"},height:{type:[String,Number],default:"auto"},shape:{type:String,default:"square"},borderRadius:{type:[String,Number],default:0},lazyLoad:{type:Boolean,default:!0},showMenuByLongpress:{type:Boolean,default:!0},loadingIcon:{type:String,default:"photo"},errorIcon:{type:String,default:"error-circle"},showLoading:{type:Boolean,default:!0},showError:{type:Boolean,default:!0},fade:{type:Boolean,default:!0},webp:{type:Boolean,default:!1},duration:{type:[String,Number],default:500},bgColor:{type:String,default:"#f3f4f6"}},data:function(){return{isError:!1,loading:!0,opacity:1,durationTime:this.duration,backgroundStyle:{}}},watch:{src:{immediate:!0,handler:function(t){t?this.isError=!1:(this.isError=!0,this.loading=!1)}}},computed:{wrapStyle:function(){var t={};return t.width=this.$u.addUnit(this.width),t.height=this.$u.addUnit(this.height),t.borderRadius="circle"==this.shape?"50%":this.$u.addUnit(this.borderRadius),t.overflow=this.borderRadius>0?"hidden":"visible",this.fade&&(t.opacity=this.opacity,t.transition="opacity ".concat(Number(this.durationTime)/1e3,"s ease-in-out")),t}},methods:{onClick:function(){this.$emit("click")},onErrorHandler:function(t){this.loading=!1,this.isError=!0,this.$emit("error",t)},onLoadHandler:function(){var t=this;if(this.loading=!1,this.isError=!1,this.$emit("load"),!this.fade)return this.removeBgColor();this.opacity=0,this.durationTime=0,setTimeout((function(){t.durationTime=t.duration,t.opacity=1,setTimeout((function(){t.removeBgColor()}),t.durationTime)}),50)},removeBgColor:function(){this.backgroundStyle={backgroundColor:"transparent"}}}};e.default=n},"4f01":function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("a9e3"),i("acd8");var n={data:function(){return{priceSlice:{}}},components:{},props:{firstSize:{type:[String,Number],default:28},secondSize:{type:[String,Number],default:28},color:{type:String},weight:{type:[String,Number],default:400},price:{type:[String,Number],default:""},showSubscript:{type:Boolean,default:!0},subscriptSize:{type:[String,Number],default:28},lineThrough:{type:Boolean,default:!1}},created:function(){this.priceFormat()},watch:{price:function(t){this.priceFormat()}},methods:{priceFormat:function(){var t=this.price,e={};null!==t&&""!==t&&void 0!==t?(t=parseFloat(t),t=String(t).split("."),e.first=t[0],e.second=t[1],this.priceSlice=e):this.priceSlice={first:0}}}};e.default=n},6944:function(t,e,i){var n=i("24fb");e=n(!1),e.push([t.i,".price-format[data-v-0a5a34e0]{font-family:Avenir,SourceHanSansCN,PingFang SC,Arial,Hiragino Sans GB,Microsoft YaHei,sans-serif}",""]),t.exports=e},7365:function(t,e,i){var n=i("24fb");e=n(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.emoji-lists[data-v-67769de1]{display:flex;flex-wrap:wrap;padding:%?20?%}.emoji-lists .emoji-item[data-v-67769de1]{width:12.5%;text-align:center;padding:%?10?% 0;-webkit-transform:scale(1.2);transform:scale(1.2)}',""]),t.exports=e},7760:function(t,e,i){"use strict";var n=i("bc17"),a=i.n(n);a.a},8158:function(t,e,i){"use strict";var n=i("e6f3"),a=i.n(n);a.a},"86f6":function(t,e,i){"use strict";i("7a82");var n=i("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=n(i("d0ff")),o=n(i("d0af")),s=n(i("f07e")),r=n(i("c964"));i("ac1f"),i("5319"),i("d3b7"),i("3c65"),i("14d9");var c=n(i("0a6e")),u=i("1524"),d=i("2eec"),l=i("b550"),f=i("a5ae"),h=i("f3c2"),m=(i("26cb"),{data:function(){return{pageStatus:"loading",scrollTop:"",intoView:"",page:1,msg:"",socket:{},kefu:{},showEmoji:!1,recoreds:[],errorMsg:"",goodsInfo:{},isError:!1,showGoods:!1,showIndex:-1}},computed:{timeFormat:function(){var t=this;return function(e,i){var n=(0,h.timeFormatChat)(e.create_time_stamp);return i&&e.create_time_stamp-t.recoreds[i-1].create_time_stamp<300&&!e.show_time&&(n=""),n}},replaceEmoji:function(){return function(t){return t.replace(/\[em-([a-z_]+)\]/g,'<span class="em em-$1"></span>')}},$getImageUri:function(){var t=this;return function(e){return t.$store.state.app.config.base_domain+e}}},watch:{kefu:function(t){t.id&&this.setTitle(t.nickname)}},methods:{init:function(){var t=this;this.shopId=this.$Route.query.shop_id||0,this.goodsId=this.$Route.query.goods_id,this.socket=new c.default(this.appConfig.ws_domain,{token:this.$store.getters.token,type:"user",client:f.client,shop_id:this.shopId}),this.socket.addEvent("connect",(function(){t.setTitle("连接中...")})),this.socket.addEvent("open",(function(){t.setTitle(t.kefu.nickname),t.isError=!1})),this.socket.addEvent("message",(function(e){switch(e.event){case"login":t.loginEvent(e.data);break;case"chat":t.chatEvent(e.data);break;case"transfer":t.transferEvent(e.data);break;case"error":t.errorEvent(e.data);break}})),this.socket.addEvent("error",(function(e){t.setTitle("连接失败")}))},showTips:function(t){var e=this;t?uni.showModal({title:"温馨提示",content:t,success:function(t){t.confirm?e.$Router.replace({path:"/bundle/pages/contact_offical/contact_offical?id=".concat(e.shopId)}):t.cancel&&e.$Router.back()}}):setTimeout((function(){e.$Router.replace({path:"/bundle/pages/contact_offical/contact_offical?id=".concat(e.shopId)})}),200)},getConfig:function(){return(0,d.getChatConfig)({shop_id:this.shopId}).then((function(t){return Promise.resolve(t)})).catch((function(){return Promise.reject()}))},getData:function(){var t=this;return(0,r.default)((0,s.default)().mark((function e(){var i;return(0,s.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,t.getConfig();case 3:if(i=e.sent,0!=i.code){e.next=6;break}return e.abrupt("return",t.showTips(i.msg));case 6:return e.next=8,t.getChatRecord();case 8:if(t.getGoods(),t.scrollToBottom(),t.kefu.id){e.next=13;break}return t.setTitle("客服不在线"),e.abrupt("return");case 13:t.socket.connect(),e.next=18;break;case 16:e.prev=16,e.t0=e["catch"](0);case 18:case"end":return e.stop()}}),e,null,[[0,16]])})))()},getGoods:function(){var t=this;this.goodsId&&(0,l.getGoodsDetail)({goods_id:this.goodsId}).then((function(e){1==e.code&&(t.goodsInfo=e.data,t.kefu.id&&(t.showGoods=!0))}))},previewImage:function(t){uni.previewImage({urls:[t]})},uploadFile:function(){var t=this;return(0,r.default)((0,s.default)().mark((function e(){var i,n,a,r,c;return(0,s.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,uni.chooseImage({count:1});case 2:if(i=e.sent,n=(0,o.default)(i,2),a=n[0],r=n[1],!a){e.next=8;break}return e.abrupt("return");case 8:return uni.showLoading({title:"上传中..."}),e.prev=9,e.next=12,(0,f.uploadFile)(r.tempFilePaths[0]);case 12:c=e.sent,t.send(c.base_uri,2),uni.hideLoading(),e.next=21;break;case 17:e.prev=17,e.t0=e["catch"](9),t.$toast({title:"上传失败，请稍后再试"}),uni.hideLoading();case 21:case"end":return e.stop()}}),e,null,[[9,17]])})))()},sendText:function(){this.msg&&(this.send(this.msg,1),this.msg="")},sendGoods:function(){this.showGoods=!1,this.send(this.goodsId,3)},getChatRecord:function(){var t=this;return(0,r.default)((0,s.default)().mark((function e(){var i,n,o,r,c,d,l,f;return(0,s.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(i=t.page,n=t.pageStatus,"finish"!=n){e.next=3;break}return e.abrupt("return");case 3:return e.next=5,(0,u.chatRecord)({shop_id:t.shopId,page_no:i});case 5:o=e.sent,1==o.code&&(c=0,t.page++,d=o.data,l=d.kefu,f=d.record,t.kefu=l,t.showIndex=f.list.length,t.recoreds.length&&(c=t.recoreds[0].id,t.recoreds[0].show_time=!0),(r=t.recoreds).unshift.apply(r,(0,a.default)(f.list)),t.$nextTick((function(){f.more||(t.pageStatus="finish"),t.scrollToItem(c),t.showIndex=-1})));case 7:case"end":return e.stop()}}),e)})))()},send:function(t,e){this.socket.send({event:"chat",data:{msg:t,msg_type:e,to_id:this.kefu.id,to_type:"kefu"}})},handleEmojiShow:function(){var t=this;this.showEmoji=!this.showEmoji,this.showEmoji&&setTimeout((function(){t.scrollToBottom()}),300)},scrollToupper:function(){this.getChatRecord()},scrollToBottom:function(){var t=this;this.intoView="bottom",this.$nextTick((function(){t.intoView=""}))},scrollToItem:function(t){var e=this;this.intoView="chat-item_".concat(t),this.$nextTick((function(){e.intoView=""}))},handleEmojiInput:function(t){this.msg=this.msg+t},chatEvent:function(t){var e=this;this.isError=!1,"kefu"==t.from_type&&uni.vibrateLong({success:function(){console.log("success")}}),t.shop_id==this.shopId&&(this.recoreds.push(t),this.$nextTick((function(){(0,f.getRect)("#bottom").then((function(i){i.bottom<1e3&&e.scrollToItem(t.id)}))})))},errorEvent:function(t){var e=this;this.errorMsg=t.msg,this.isError=!0,this.$nextTick((function(){e.scrollToBottom()}))},loginEvent:function(t){this.socket.send({event:"user_online",data:{kefu_id:this.kefu.id}})},transferEvent:function(t){this.kefu=t},setTitle:function(t){uni.setNavigationBarTitle({title:t})}},onLoad:function(){var t=this;return(0,r.default)((0,s.default)().mark((function e(){return(0,s.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.scrollToupper=(0,f.debounce)(t.scrollToupper,500,t),t.init(),t.getData();case 3:case"end":return e.stop()}}),e)})))()},onUnload:function(){this.socket.close()},onReady:function(){}});e.default=m},"87ed":function(t,e){t.exports="data:image/png;base64,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"},"8ad1":function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("a9e3");var n={name:"u-loading",props:{mode:{type:String,default:"circle"},color:{type:String,default:"#c7c7c7"},size:{type:[String,Number],default:"34"},show:{type:Boolean,default:!0}},computed:{cricleStyle:function(){var t={};return t.width=this.size+"rpx",t.height=this.size+"rpx","circle"==this.mode&&(t.borderColor="#e4e4e4 #e4e4e4 #e4e4e4 ".concat(this.color?this.color:"#c7c7c7")),t}}};e.default=n},"8eac":function(t,e,i){var n=i("fc7c");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=i("4f06").default;a("0c16c571",n,!0,{sourceMap:!1,shadowMode:!1})},a272:function(t,e,i){"use strict";i.r(e);var n=i("e2ba"),a=i("3ab4");for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);i("8158");var s=i("f0c5"),r=Object(s["a"])(a["default"],n["b"],n["c"],!1,null,"0a5a34e0",null,!1,n["a"],void 0);e["default"]=r.exports},a7f5:function(t,e){t.exports="data:image/png;base64,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"},bb9c:function(t,e,i){"use strict";i.r(e);var n=i("86f6"),a=i.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);e["default"]=a.a},bc17:function(t,e,i){var n=i("7365");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=i("4f06").default;a("5eb598f4",n,!0,{sourceMap:!1,shadowMode:!1})},c1c1:function(t,e,i){"use strict";i.r(e);var n=i("cf72"),a=i("e50a");for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);i("1d4d");var s=i("f0c5"),r=Object(s["a"])(a["default"],n["b"],n["c"],!1,null,"bf7076f2",null,!1,n["a"],void 0);e["default"]=r.exports},c529:function(t,e,i){"use strict";var n=i("1e2e"),a=i.n(n);a.a},c7ac:function(t,e,i){"use strict";i.r(e);var n=i("01c2"),a=i.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);e["default"]=a.a},cf72:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){}));var n=function(){var t=this.$createElement,e=this._self._c||t;return this.show?e("v-uni-view",{staticClass:"u-loading",class:"circle"==this.mode?"u-loading-circle":"u-loading-flower",style:[this.cricleStyle]}):this._e()},a=[]},dab2:function(t,e,i){"use strict";i.r(e);var n=i("3493"),a=i("c7ac");for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);i("7760");var s=i("f0c5"),r=Object(s["a"])(a["default"],n["b"],n["c"],!1,null,"67769de1",null,!1,n["a"],void 0);e["default"]=r.exports},e2ba:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){}));var n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-text",{class:(t.lineThrough?"line-through":"")+" price-format",style:{color:t.color,"font-weight":t.weight}},[t.showSubscript?i("v-uni-text",{style:{"font-size":t.subscriptSize+"rpx","margin-right":"2rpx"}},[t._v("¥")]):t._e(),i("v-uni-text",{style:{"font-size":t.firstSize+"rpx","margin-right":"1rpx"}},[t._v(t._s(t.priceSlice.first))]),t.priceSlice.second?i("v-uni-text",{style:{"font-size":t.secondSize+"rpx"}},[t._v("."+t._s(t.priceSlice.second))]):t._e()],1)},a=[]},e50a:function(t,e,i){"use strict";i.r(e);var n=i("8ad1"),a=i.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);e["default"]=a.a},e6f3:function(t,e,i){var n=i("6944");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=i("4f06").default;a("66e034c8",n,!0,{sourceMap:!1,shadowMode:!1})},f1ff:function(t,e,i){var n=i("212e");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=i("4f06").default;a("5e1ac5de",n,!0,{sourceMap:!1,shadowMode:!1})},f3c2:function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.timeFormatChat=e.timeFormat=void 0,i("a9e3"),i("d401"),i("d3b7"),i("25f0"),i("ac1f"),i("4d63"),i("c607"),i("2c3e"),i("5319"),i("4d90");var n=["星期日","星期一","星期二","星期三","星期四","星期五","星期六"],a=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"yyyy-mm-dd";t||(t=Number(new Date)),10==t.toString().length&&(t*=1e3);var i,n=new Date(t),a={"y+":n.getFullYear().toString(),"m+":(n.getMonth()+1).toString(),"d+":n.getDate().toString(),"h+":n.getHours().toString(),"M+":n.getMinutes().toString(),"s+":n.getSeconds().toString()};for(var o in a)i=new RegExp("("+o+")").exec(e),i&&(e=e.replace(i[1],1==i[1].length?a[o]:a[o].padStart(i[1].length,"0")));return e};e.timeFormat=a;e.timeFormatChat=function(t){10==t.toString().length&&(t*=1e3);var e=new Date(t),i=a(t,"yyyy年mm月dd日 hh:MM");return r(e)?i=a(t,"hh:MM"):c(e)?i=n[e.getDay()]+a(t," hh:MM"):o(e)&&(i=a(t,"mm月dd日 hh:MM")),i};var o=function(t){var e=new Date;return t.getYear()==e.getYear()},s=function(t){var e=new Date;return o(t)&&t.getMonth()==e.getMonth()},r=function(t){var e=new Date;return s(t)&&t.getDate()==e.getDate()},c=function(t){var e=new Date;return!!s(t)&&(e.getDay()-t.getDay()>0&&e.getDate()-t.getDate()<7||void 0)}},f743:function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){return n}));var n={uIcon:i("6976").default},a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"u-image",style:[t.wrapStyle,t.backgroundStyle],on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onClick.apply(void 0,arguments)}}},[t.isError?t._e():i("v-uni-image",{staticClass:"u-image__image",style:{borderRadius:"circle"==t.shape?"50%":t.$u.addUnit(t.borderRadius)},attrs:{src:t.src,mode:t.mode,"lazy-load":t.lazyLoad},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.onErrorHandler.apply(void 0,arguments)},load:function(e){arguments[0]=e=t.$handleEvent(e),t.onLoadHandler.apply(void 0,arguments)}}}),t.showLoading&&t.loading?i("v-uni-view",{staticClass:"u-image__loading",style:{borderRadius:"circle"==t.shape?"50%":t.$u.addUnit(t.borderRadius),backgroundColor:this.bgColor}},[t.$slots.loading?t._t("loading"):i("u-icon",{attrs:{name:t.loadingIcon,width:t.width,height:t.height}})],2):t._e(),t.showError&&t.isError&&!t.loading?i("v-uni-view",{staticClass:"u-image__error",style:{borderRadius:"circle"==t.shape?"50%":t.$u.addUnit(t.borderRadius)}},[t.$slots.error?t._t("error"):i("u-icon",{attrs:{name:t.errorIcon,width:t.width,height:t.height}})],2):t._e()],1)},o=[]},f919:function(t,e,i){"use strict";i.r(e);var n=i("f743"),a=i("3f30");for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);i("c529");var s=i("f0c5"),r=Object(s["a"])(a["default"],n["b"],n["c"],!1,null,"1bf07c9a",null,!1,n["a"],void 0);e["default"]=r.exports},fc7c:function(t,e,i){var n=i("24fb");e=n(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */uni-page-body[data-v-63ea1534]{pading:0;height:100%}.chat[data-v-63ea1534]{height:100%}.chat .goods[data-v-63ea1534]{display:flex;position:fixed;width:%?600?%;right:%?20?%;bottom:calc(%?120?% + env(safe-area-inset-bottom));border-radius:%?14?%;background:#fff;padding:%?20?%}.chat .goods .close[data-v-63ea1534]{position:absolute;left:%?-20?%;top:%?-20?%}.chat .goods .send-btn[data-v-63ea1534]{padding:%?8?% %?22?%}.chat .content[data-v-63ea1534]{transition:all .3s;flex:1;min-height:0}.chat .content .loading[data-v-63ea1534]{padding:%?20?%;height:40px}.chat .content .chat-lists[data-v-63ea1534]{padding:0 %?20?% %?30?%;overflow:hidden;position:relative}.chat .content .chat-lists .chat-tips[data-v-63ea1534]{padding:%?4?% %?20?%;border-radius:%?21?%;display:inline-block;text-align:center;background-color:rgba(0,0,0,.2)}.chat .content .chat-lists .chat-item[data-v-63ea1534]{padding-top:%?30?%}.chat .content .chat-lists .chat-item.visibility[data-v-63ea1534]{visibility:hidden}.chat .content .chat-lists .chat-item .chat-info[data-v-63ea1534]{display:flex;align-items:flex-start}.chat .content .chat-lists .chat-item.right .chat-info[data-v-63ea1534]{flex-direction:row-reverse}.chat .content .chat-lists .chat-item.right .chat-info .text-box[data-v-63ea1534]{background-color:#ed5349;color:#fff}.chat .content .chat-lists .chat-item .avatar[data-v-63ea1534]{width:%?78?%;height:%?78?%;border-radius:%?14?%;flex:none}.chat .content .chat-lists .chat-item .text-box[data-v-63ea1534]{max-width:%?500?%;min-width:%?80?%;background-color:#fff;border-radius:%?14?%;padding:%?16?% %?20?%;margin:0 %?20?%;word-break:break-word;line-height:%?40?%}.chat .content .chat-lists .chat-item .image-box[data-v-63ea1534]{max-width:%?300?%;margin:0 %?20?%}.chat .content .chat-lists .chat-item .image-box .image[data-v-63ea1534]{max-width:100%}.chat .content .chat-lists .chat-item .goods-box[data-v-63ea1534]{position:static;width:%?510?%}.chat .error[data-v-63ea1534]{padding:0 %?30?% %?30?%}.chat .error .error-msg[data-v-63ea1534]{color:#bbb;word-break:break-word}.chat .footer[data-v-63ea1534]{background:#f2f2f2;padding-bottom:env(safe-area-inset-bottom)}.chat .footer .footer-input[data-v-63ea1534]{height:%?100?%;padding:0 %?20?%}.chat .footer .footer-input .icon[data-v-63ea1534]{width:%?52?%;height:%?52?%}.chat .footer .footer-input .input-contain[data-v-63ea1534]{margin:0 %?20?%;background-color:#fff;height:%?68?%;border-radius:%?60?%;flex:1;overflow:hidden;padding:0 %?10?% 0 %?30?%}.chat .footer .footer-input .input-contain .text-area[data-v-63ea1534]{flex:1;height:%?100?%;word-break:break-all}.chat .emoji-wrap[data-v-63ea1534]{height:0;transition:all .3s}.chat .emoji-wrap.emoji-show[data-v-63ea1534]{height:200px}.chat .send-btn[data-v-63ea1534]{padding:0 %?25?%;color:#fff;background-color:#ed5349;border-radius:%?60?%}',""]),t.exports=e}}]);