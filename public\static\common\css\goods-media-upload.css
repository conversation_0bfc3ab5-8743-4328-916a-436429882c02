/**
 * 商品媒体上传组件样式
 * 支持图片和视频混合上传，拖拽排序
 */

/* 主容器 */
.goods-media-upload {
    border: 1px solid #e6e6e6;
    border-radius: 8px;
    background: #fafafa;
    padding: 20px;
}

/* 上传区域 */
.media-upload-area {
    text-align: center;
    padding: 20px;
    border: 2px dashed #d9d9d9;
    border-radius: 8px;
    background: white;
    margin-bottom: 20px;
    transition: all 0.3s ease;
}

.media-upload-area:hover {
    border-color: #1890ff;
    background: #f0f8ff;
}

.upload-buttons {
    margin-bottom: 15px;
}

.upload-buttons .layui-btn {
    margin: 0 10px;
    border-radius: 6px;
}

.upload-tips {
    color: #666;
    font-size: 13px;
    line-height: 1.5;
}

/* 媒体预览容器 */
.media-preview-container {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    min-height: 120px;
}

.media-preview-container:empty::before {
    content: '暂无媒体文件，请点击上方按钮添加';
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 120px;
    color: #999;
    font-size: 14px;
    border: 1px dashed #e6e6e6;
    border-radius: 6px;
    background: white;
}

/* 媒体项 */
.media-item {
    position: relative;
    width: 120px;
    height: 120px;
    border-radius: 8px;
    overflow: hidden;
    background: white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    cursor: move;
    transition: all 0.3s ease;
}

.media-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.media-item.is-cover::after {
    content: '封面';
    position: absolute;
    top: 5px;
    left: 5px;
    background: #ff4d4f;
    color: white;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 10px;
    font-weight: bold;
    z-index: 3;
}

/* 媒体内容 */
.media-content {
    position: relative;
    width: 100%;
    height: 100%;
}

.media-content img,
.media-content video {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
}

/* 视频播放按钮 */
.video-play-btn {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 40px;
    height: 40px;
    background: rgba(0, 0, 0, 0.7);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 16px;
    z-index: 2;
}

/* 媒体控制层 */
.media-controls {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 2;
}

.media-item:hover .media-controls {
    opacity: 1;
}

/* 媒体序号 */
.media-order {
    position: absolute;
    top: 5px;
    right: 5px;
    background: #1890ff;
    color: white;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 11px;
    font-weight: bold;
}

/* 媒体操作按钮 */
.media-actions {
    position: absolute;
    bottom: 5px;
    right: 5px;
    display: flex;
    gap: 5px;
}

.media-actions button {
    width: 24px;
    height: 24px;
    border: none;
    border-radius: 4px;
    background: rgba(255, 255, 255, 0.9);
    color: #666;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    font-size: 12px;
}

.media-actions button:hover {
    background: #fff;
    color: #333;
    transform: scale(1.1);
}

.btn-delete:hover {
    background: #ff4d4f !important;
    color: white !important;
}

.btn-preview:hover {
    background: #1890ff !important;
    color: white !important;
}

.btn-set-cover:hover {
    background: #52c41a !important;
    color: white !important;
}

/* 拖动手柄 */
.drag-handle {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 20px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: move;
    font-size: 12px;
}

.drag-handle:hover {
    background: rgba(0, 0, 0, 0.8);
}

/* Sortable.js 相关样式 */
.sortable-ghost {
    opacity: 0.5;
    background: #f0f8ff;
    border: 2px dashed #1890ff;
}

.sortable-chosen {
    transform: rotate(3deg);
}

.sortable-drag {
    transform: rotate(3deg);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

/* 媒体类型标识 */
.media-type-badge {
    position: absolute;
    top: 5px;
    left: 5px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 10px;
    z-index: 3;
}

.media-type-badge.image {
    background: #52c41a;
}

.media-type-badge.video {
    background: #722ed1;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .media-item {
        width: 100px;
        height: 100px;
    }
    
    .media-preview-container {
        gap: 10px;
    }
    
    .upload-buttons .layui-btn {
        margin: 5px;
        font-size: 12px;
        padding: 5px 10px;
    }
    
    .media-actions button {
        width: 20px;
        height: 20px;
        font-size: 10px;
    }
}

/* 加载状态 */
.media-item.loading {
    position: relative;
}

.media-item.loading::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #1890ff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    z-index: 4;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 错误状态 */
.media-item.error {
    border: 2px solid #ff4d4f;
}

.media-item.error .media-controls {
    background: rgba(255, 77, 79, 0.8);
}

/* 成功状态 */
.media-item.success::before {
    content: '✓';
    position: absolute;
    top: 5px;
    right: 5px;
    width: 16px;
    height: 16px;
    background: #52c41a;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    font-weight: bold;
    z-index: 3;
}

/* 拖拽提示 */
.drag-over {
    border-color: #1890ff !important;
    background: #f0f8ff !important;
}

.drag-over::after {
    content: '松开鼠标完成排序';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: #1890ff;
    color: white;
    padding: 8px 16px;
    border-radius: 4px;
    font-size: 12px;
    z-index: 10;
}
