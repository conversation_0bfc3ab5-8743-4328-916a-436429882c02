<?php


namespace app\admin\controller\community;


use app\admin\logic\community\CommunityExhibitionLogic;
use app\admin\logic\community\CommunityCategoryLogic;
use app\admin\validate\community\CommunityArticleValidate;
use app\common\basics\AdminBase;
use app\common\enum\CommunityArticleEnum;
use app\common\server\ConfigServer;
use app\common\server\JsonServer;

/**
 * 种草社区文章
 * Class CommunityArticle
 * @package app\admin\controller\community
 */
class CommunityExhibition extends AdminBase
{


    /**
     * @notes 文章列表
     * @return \think\response\Json|\think\response\View
     * <AUTHOR>
     * @date 2022/5/10 11:08
     */
    public function lists()
    {
        if ($this->request->isAjax()) {
            $get = $this->request->get();
            $lists = CommunityExhibitionLogic::lists($get);
            return JsonServer::success("获取成功", $lists);
        }
        return view('', [
            'status' => CommunityArticleEnum::getStatusDesc()
        ]);
    }

    public function add()
    {
        if ($this->request->isAjax()) {
            $post = $this->request->post();
            $start_time=$post['start_time'] = strtotime($post['start_time']);
            $end_time=$post['end_time'] = strtotime($post['end_time']);
            if($start_time >= $end_time){

                return JsonServer::error('开始时间不能大于结束时间');
            }
            if($end_time<time()){

                return JsonServer::error('结束时间不能小于当前时间');
            }
            if($post['user_id'] == 0){

                return JsonServer::error('请选择负责人');
            }
            $res = CommunityExhibitionLogic::add($post);
            if ($res === false) {
                $error = CommunityExhibitionLogic::getError() ?: '新增失败';
                return JsonServer::error($error);
            }
            return JsonServer::success('新增成功');
        }

        return view('', [
            'category' => [],
            'tx_map_key' => ConfigServer::get('map', 'tx_map_key')
        ]);
    }

    /***
     * @Notes: 编辑文章
     * @Author: 张无忌
     */
    public function edit()
    {
        if ($this->request->isAjax()) {
            $post = $this->request->post();
            $start_time=$post['start_time'] = strtotime($post['start_time']);
            $end_time=$post['end_time'] = strtotime($post['end_time']);
            if($start_time >= $end_time){
                return JsonServer::error('开始时间不能大于结束时间');
            }
            if($end_time<time()){
                return JsonServer::error('结束时间不能小于当前时间');
            }
            $res = CommunityExhibitionLogic::edit($post);
            if ($res === false) {
                $error = CommunityExhibitionLogic::getError() ?: '编辑失败';
                return JsonServer::error($error);
            }
            return JsonServer::success('编辑成功');
        }

        $id = $this->request->get('id');
        return view('', [
            'detail'   => CommunityExhibitionLogic::detail($id),
            'tx_map_key' => ConfigServer::get('map', 'tx_map_key')
        ]);
    }
    /**
     * @notes 审核文章
     * @return \think\response\Json|\think\response\View
     * <AUTHOR>
     * @date 2022/5/10 17:45
     */
    public function audit()
    {
        if ($this->request->isAjax()) {
            $post = $this->request->post();
            (new CommunityArticleValidate())->goCheck('audit', $post);
            $result = CommunityExhibitionLogic::audit($post);
            if (false === $result) {
                return JsonServer::error(CommunityExhibitionLogic::getError() ?: '操作失败');
            }
            return JsonServer::success('编辑成功');
        }
        $id = $this->request->get('id');
        return view('', [
            'detail' => CommunityExhibitionLogic::detail($id)
        ]);
    }



    /**
     * @notes 文章详情
     * @return \think\response\Json|\think\response\View
     * <AUTHOR>
     * @date 2022/5/10 19:05
     */
    public function detail()
    {



        if ($this->request->isAjax()) {
            $get = $this->request->get();
            $result = CommunityExhibitionLogic::getRelationData($get);
            return JsonServer::success('', $result);
        }
        $id = $this->request->get('id');
        return view('', [
            'detail' => CommunityExhibitionLogic::detail($id)
        ]);
    }


    /**
     * @notes 社区配置
     * @return \think\response\Json|\think\response\View
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date 2022/4/28 16:16
     */
    public function setting()
    {
        if ($this->request->isAjax()) {
            $post = $this->request->post();
            CommunityExhibitionLogic::setConfig($post);
            return JsonServer::success('操作成功');
        }
        $config = CommunityExhibitionLogic::getConfigform();
        return view('', ['config' => $config]);
    }


    /**
     * @notes 用户列表
     * @return \think\response\Json|\think\response\View
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date 2021/9/3 11:50
     */
    public function userLists()
    {
        if ($this->request->isPost()) {
            $params = $this->request->post();
            $lists = CommunityExhibitionLogic::getUserLists($params);
            return JsonServer::success('', $lists);
        }
        return view();
    }
    /**
     * @notes 删除文章
     * @return \think\response\Json|void
     * <AUTHOR>
     * @date 2022/5/10 16:46
     */
    public function del()
    {
        if ($this->request->isAjax()) {
            (new CommunityArticleValidate())->goCheck('id');
            $id = $this->request->post('id');
            $result = CommunityExhibitionLogic::del($id);
            if (false === $result) {
                return JsonServer::error(CommunityExhibitionLogic::getError() ?: '删除失败');
            }
            return JsonServer::success('删除成功');
        }
    }





}