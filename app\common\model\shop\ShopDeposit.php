<?php

namespace app\common\model\shop;

use app\common\basics\Models;

class ShopDeposit extends Models
{
    /**
     * @Notes: 关联商家账号模型
     */
    public function admin()
    {
        return $this->hasOne('Shop', 'id', 'shop_id');
    }

    /**
     * @notes 关联明细表
     * @return \think\model\relation\HasMany
     * <AUTHOR>
     * @date 2021/7/13 6:47 下午
     */
    public function details()
    {
        return $this->hasMany('Shop_Deposit_Details', 'deposit_id', 'id')->order('id desc');
    }
}