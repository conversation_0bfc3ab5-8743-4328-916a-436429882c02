<?php

namespace app\common\model\shop;

use app\common\basics\Models;

/**
 * 商家等级升级记录模型
 * Class ShopTierUpgrade
 * @package app\common\model\shop
 */
class ShopTierUpgrade extends Models
{
    protected $name = 'shop_tier_upgrade';

    /**
     * 关联商家模型
     */
    public function shop()
    {
        return $this->belongsTo(Shop::class, 'shop_id', 'id');
    }

    /**
     * 关联用户模型
     */
    public function user()
    {
        return $this->belongsTo(\app\common\model\user\User::class, 'user_id', 'id');
    }

    /**
     * 获取器 - 原等级名称
     */
    public function getFromTierNameAttr($value, $data)
    {
        $names = [
            0 => '0元入驻',
            1 => '商家会员',
            2 => '实力厂商',
        ];
        return $names[$data['from_tier']] ?? '未知等级';
    }

    /**
     * 获取器 - 目标等级名称
     */
    public function getToTierNameAttr($value, $data)
    {
        $names = [
            0 => '0元入驻',
            1 => '商家会员',
            2 => '实力厂商',
        ];
        return $names[$data['to_tier']] ?? '未知等级';
    }

    /**
     * 获取器 - 升级类型文本
     */
    public function getUpgradeTypeTextAttr($value, $data)
    {
        $types = [
            0 => '新入驻',
            1 => '等级升级',
            2 => '续费',
        ];
        return $types[$data['upgrade_type']] ?? '未知类型';
    }

    /**
     * 获取器 - 支付状态文本
     */
    public function getPaymentStatusTextAttr($value, $data)
    {
        $statusTexts = [
            0 => '待支付',
            1 => '已支付',
        ];
        return $statusTexts[$data['payment_status']] ?? '未知状态';
    }

    /**
     * 获取器 - 审核状态文本
     */
    public function getAuditStatusTextAttr($value, $data)
    {
        $statusTexts = [
            0 => '待审核',
            1 => '已通过',
            2 => '已拒绝',
        ];
        return $statusTexts[$data['audit_status']] ?? '未知状态';
    }
}
