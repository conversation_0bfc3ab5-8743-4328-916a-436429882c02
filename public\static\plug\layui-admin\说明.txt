layuiAdmin std（iframe版）

# 关于产品
layuiAdmin std 是一款由 layui 官方出品的高质量付费产品，可用于快速开发通用型后台管理系统。

# 运行说明：
直接运行入口文件即可： src/views/index.html


# 目录说明：
dist/  经过打包压缩后的文件，一般用于正式环境使用
src/   源代码，一般用于开发环境

# 源码构建
项目可采用 gulp 构建，gulpfile.js 是任务脚本，package.json 是任务配置文件
step1：确保你的电脑已经安装好了 Node.js
step2: 命令行安装 gulp：npm install gulp -g
step3：切换到 layuiAdmin 项目根目录（即 gulpfile.js 所在目录），命令行安装任务所依赖的包：npm install
安装完成后，即可直接执行命令：gulp
即可完成 src 到 dist 目录的构建


# 官网地址：
http://www.layui.com/admin/


# 版权须知
layuiAdmin 受国家计算机软件著作权保护，
未经官网正规渠道授权擅自公开产品源文件、
以及直接对产品二次出售的，
我们将保留追究法律责任的权利。

© 2018 layui.com 版权所有

