(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["bundle-pages-goods_seckill-goods_seckill"],{1522:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){return r}));var r={uIcon:i("90f3").default},n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"u-image",style:[t.wrapStyle,t.backgroundStyle],on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onClick.apply(void 0,arguments)}}},[t.isError?t._e():i("v-uni-image",{staticClass:"u-image__image",style:{borderRadius:"circle"==t.shape?"50%":t.$u.addUnit(t.borderRadius)},attrs:{src:t.src,mode:t.mode,"lazy-load":t.lazyLoad},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.onErrorHandler.apply(void 0,arguments)},load:function(e){arguments[0]=e=t.$handleEvent(e),t.onLoadHandler.apply(void 0,arguments)}}}),t.showLoading&&t.loading?i("v-uni-view",{staticClass:"u-image__loading",style:{borderRadius:"circle"==t.shape?"50%":t.$u.addUnit(t.borderRadius),backgroundColor:this.bgColor}},[t.$slots.loading?t._t("loading"):i("u-icon",{attrs:{name:t.loadingIcon,width:t.width,height:t.height}})],2):t._e(),t.showError&&t.isError&&!t.loading?i("v-uni-view",{staticClass:"u-image__error",style:{borderRadius:"circle"==t.shape?"50%":t.$u.addUnit(t.borderRadius)}},[t.$slots.error?t._t("error"):i("u-icon",{attrs:{name:t.errorIcon,width:t.width,height:t.height}})],2):t._e()],1)},a=[]},"1b11":function(t,e,i){var r=i("f041");r.__esModule&&(r=r.default),"string"===typeof r&&(r=[[t.i,r,""]]),r.locals&&(t.exports=r.locals);var n=i("4f06").default;n("d79bc53a",r,!0,{sourceMap:!1,shadowMode:!1})},2322:function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("a9e3");var r={name:"u-image",props:{src:{type:String,default:""},mode:{type:String,default:"aspectFill"},width:{type:[String,Number],default:"100%"},height:{type:[String,Number],default:"auto"},shape:{type:String,default:"square"},borderRadius:{type:[String,Number],default:0},lazyLoad:{type:Boolean,default:!0},showMenuByLongpress:{type:Boolean,default:!0},loadingIcon:{type:String,default:"photo"},errorIcon:{type:String,default:"error-circle"},showLoading:{type:Boolean,default:!0},showError:{type:Boolean,default:!0},fade:{type:Boolean,default:!0},webp:{type:Boolean,default:!1},duration:{type:[String,Number],default:500},bgColor:{type:String,default:"#f3f4f6"}},data:function(){return{isError:!1,loading:!0,opacity:1,durationTime:this.duration,backgroundStyle:{}}},watch:{src:{immediate:!0,handler:function(t){t?this.isError=!1:(this.isError=!0,this.loading=!1)}}},computed:{wrapStyle:function(){var t={};return t.width=this.$u.addUnit(this.width),t.height=this.$u.addUnit(this.height),t.borderRadius="circle"==this.shape?"50%":this.$u.addUnit(this.borderRadius),t.overflow=this.borderRadius>0?"hidden":"visible",this.fade&&(t.opacity=this.opacity,t.transition="opacity ".concat(Number(this.durationTime)/1e3,"s ease-in-out")),t}},methods:{onClick:function(){this.$emit("click")},onErrorHandler:function(t){this.loading=!1,this.isError=!0,this.$emit("error",t)},onLoadHandler:function(){var t=this;if(this.loading=!1,this.isError=!1,this.$emit("load"),!this.fade)return this.removeBgColor();this.opacity=0,this.durationTime=0,setTimeout((function(){t.durationTime=t.duration,t.opacity=1,setTimeout((function(){t.removeBgColor()}),t.durationTime)}),50)},removeBgColor:function(){this.backgroundStyle={backgroundColor:"transparent"}}}};e.default=r},"45ee":function(t,e,i){var r=i("24fb");e=r(!1),e.push([t.i,".price-format[data-v-0a5a34e0]{font-family:Avenir,SourceHanSansCN,PingFang SC,Arial,Hiragino Sans GB,Microsoft YaHei,sans-serif}",""]),t.exports=e},"578c":function(t,e,i){"use strict";i.r(e);var r=i("e71b"),n=i("ee9d");for(var a in n)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(a);i("f893");var o=i("f0c5"),s=Object(o["a"])(n["default"],r["b"],r["c"],!1,null,"5a2d1aa7",null,!1,r["a"],void 0);e["default"]=s.exports},6021:function(t,e,i){"use strict";var r=i("64b1"),n=i.n(r);n.a},"64b1":function(t,e,i){var r=i("6e35");r.__esModule&&(r=r.default),"string"===typeof r&&(r=[[t.i,r,""]]),r.locals&&(t.exports=r.locals);var n=i("4f06").default;n("3d1e497c",r,!0,{sourceMap:!1,shadowMode:!1})},"6d79":function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("a9e3"),i("acd8");var r={data:function(){return{priceSlice:{}}},components:{},props:{firstSize:{type:[String,Number],default:28},secondSize:{type:[String,Number],default:28},color:{type:String},weight:{type:[String,Number],default:400},price:{type:[String,Number],default:""},showSubscript:{type:Boolean,default:!0},subscriptSize:{type:[String,Number],default:28},lineThrough:{type:Boolean,default:!1}},created:function(){this.priceFormat()},watch:{price:function(t){this.priceFormat()}},methods:{priceFormat:function(){var t=this.price,e={};null!==t&&""!==t&&void 0!==t?(t=parseFloat(t),t=String(t).split("."),e.first=t[0],e.second=t[1],this.priceSlice=e):this.priceSlice={first:0}}}};e.default=r},"6e35":function(t,e,i){var r=i("24fb");e=r(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.u-image[data-v-1bf07c9a]{position:relative;transition:opacity .5s ease-in-out}.u-image__image[data-v-1bf07c9a]{width:100%;height:100%}.u-image__loading[data-v-1bf07c9a], .u-image__error[data-v-1bf07c9a]{position:absolute;top:0;left:0;width:100%;height:100%;display:flex;flex-direction:row;align-items:center;justify-content:center;background-color:#f3f4f6;color:#909399;font-size:%?46?%}',""]),t.exports=e},"89cb":function(t,e,i){var r=i("24fb");e=r(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.swiper-wrap[data-v-5a2d1aa7]{overflow:hidden;box-sizing:initial}.swiper-wrap .swiper-con[data-v-5a2d1aa7]{position:relative;height:100%;overflow:hidden;-webkit-transform:translateY(0);transform:translateY(0)}.swiper-wrap .swiper[data-v-5a2d1aa7]{width:100%;height:100%;position:relative}.swiper-wrap .swiper .slide-image[data-v-5a2d1aa7]{height:100%}.swiper-wrap .dots[data-v-5a2d1aa7]{position:absolute;left:50%;-webkit-transform:translateX(-50%);transform:translateX(-50%);bottom:%?20?%;display:flex}.swiper-wrap .dots .dot[data-v-5a2d1aa7]{width:%?8?%;height:%?8?%;border-radius:50%;margin-right:%?10?%;background-color:#fff}.swiper-wrap .dots .dot.active[data-v-5a2d1aa7]{width:%?16?%;border-radius:%?8?%;background-color:#ff2c3c}',""]),t.exports=e},"8c51":function(t,e,i){"use strict";i("7a82");var r=i("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=r(i("f07e")),a=r(i("c964"));i("a9e3"),i("14d9");var o=i("9953"),s=(i("b08d"),{data:function(){return{lists:[],currentSwiper:0}},props:{pid:{type:Number},circular:{type:Boolean,default:!0},autoplay:{type:Boolean,default:!0},height:{type:String},radius:{type:String,default:"0"},padding:{type:String,default:"0rpx"},previousMargin:{type:String,default:"0rpx"},isSwiper:{type:Boolean,default:!0}},created:function(){this.getAdListFun()},watch:{pid:function(t){this.getAdListFun()}},methods:{getAdListFun:function(){var t=this;return(0,a.default)((0,n.default)().mark((function e(){var i,r,a;return(0,n.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,(0,o.getAdList)({pid:t.pid,terminal:1});case 2:i=e.sent,r=i.code,a=i.data,1==r&&(t.lists=a);case 6:case"end":return e.stop()}}),e)})))()},swiperChange:function(t){this.currentSwiper=t.detail.current},goPage:function(t){var e=t.link,i=t.link_type,r=t.params,n=t.is_tab;switch(i){case 1:case 2:n?this.$Router.pushTab({path:e}):this.$Router.push({path:e,query:r});break;case 3:this.$Router.push({path:"/pages/webview/webview",query:{url:e}});break}}}});e.default=s},9238:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){return r}));var r={adSwipers:i("578c").default,uImage:i("ba4b").default,priceFormat:i("fefe").default},n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("mescroll-body",{ref:"mescrollRef",attrs:{up:t.upOption},on:{init:function(e){arguments[0]=e=t.$handleEvent(e),t.mescrollInit.apply(void 0,arguments)},down:function(e){arguments[0]=e=t.$handleEvent(e),t.downCallback.apply(void 0,arguments)},up:function(e){arguments[0]=e=t.$handleEvent(e),t.upCallback.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"goods-seckill"},[i("v-uni-view",{staticClass:"banner"},[i("ad-swipers",{attrs:{pid:20,height:"340rpx"}})],1),i("v-uni-view",{staticClass:"time-list"},[i("v-uni-scroll-view",{staticStyle:{height:"120rpx","white-space":"nowrap"},attrs:{"scroll-into-view":"item-"+(t.active-2),"scroll-x":"true","scroll-with-animation":"true"}},t._l(t.seckillTime,(function(e,r){return i("v-uni-view",{key:r,staticClass:"time-item flex-col row-center col-center",class:{active:r==t.active},attrs:{id:"item-"+r},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.exchangeTime(r)}}},[i("v-uni-view",{class:"xl bold time"},[t._v(t._s(e.start_time))]),i("v-uni-view",{class:"sm br60 state "+(2===e.status?"muted":"")},[t._v(t._s(e.tips))])],1)})),1)],1),i("v-uni-view",{staticClass:"goods-list"},t._l(t.seckillGoods,(function(e,r){return i("router-link",{key:r,attrs:{to:{path:"/pages/goods_details/goods_details",query:{id:e.goods_id}}}},[i("v-uni-view",{staticClass:"goods-item flex bg-white"},[i("u-image",{attrs:{width:"180rpx",height:"180rpx","border-radius":"10rpx",src:e.goods_image}}),i("v-uni-view",{staticClass:"goods-info m-l-20"},[i("v-uni-view",{staticClass:"goods-name line-2 m-b-10",staticStyle:{width:"490rpx"}},[t._v(t._s(e.goods_name))]),i("v-uni-label",{staticClass:"sale-info xs primary br60"},[t._v("已抢"+t._s(e.seckill_total)+"件")]),i("v-uni-view",{staticClass:"info-footer flex row-between m-t-5"},[i("v-uni-view",{staticClass:"price"},[i("price-format",{staticClass:"m-r-10",attrs:{price:e.seckill_price,color:t.colorConfig.primary,"first-ize":34,"second-ize":26,"subscript-ize":26}}),i("price-format",{staticClass:"line-through",attrs:{price:e.goods_min_price,color:t.colorConfig.muted,"first-ize":24,"second-ize":24,"subscript-ize":24}})],1),i("v-uni-button",{class:"br60 white "+(2==t.currentStatus?" bg-gray":1==t.currentStatus?"primary-btn":"border-btn"),attrs:{size:"sm"}},[t._v(t._s(2==t.currentStatus?"已结束":1==t.currentStatus?"立即抢购":"未开始"))])],1)],1)],1)],1)})),1)],1)],1)},a=[]},a3fe:function(t,e,i){"use strict";i.r(e);var r=i("d731"),n=i.n(r);for(var a in r)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return r[t]}))}(a);e["default"]=n.a},af8d:function(t,e,i){"use strict";i.r(e);var r=i("2322"),n=i.n(r);for(var a in r)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return r[t]}))}(a);e["default"]=n.a},ba4b:function(t,e,i){"use strict";i.r(e);var r=i("1522"),n=i("af8d");for(var a in n)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(a);i("6021");var o=i("f0c5"),s=Object(o["a"])(n["default"],r["b"],r["c"],!1,null,"1bf07c9a",null,!1,r["a"],void 0);e["default"]=s.exports},bd6f:function(t,e,i){"use strict";i.r(e);var r=i("6d79"),n=i.n(r);for(var a in r)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return r[t]}))}(a);e["default"]=n.a},bde1:function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var r={data:function(){return{mescroll:null}},onPullDownRefresh:function(){this.mescroll&&this.mescroll.onPullDownRefresh()},onPageScroll:function(t){this.mescroll&&this.mescroll.onPageScroll(t)},onReachBottom:function(){this.mescroll&&this.mescroll.onReachBottom()},methods:{mescrollInit:function(t){console.log(t),this.mescroll=t,this.mescrollInitByRef()},mescrollInitByRef:function(){if(!this.mescroll||!this.mescroll.resetUpScroll){var t=this.$refs.mescrollRef;t&&(this.mescroll=t.mescroll)}},downCallback:function(){var t=this;this.mescroll.optUp.use?this.mescroll.resetUpScroll():setTimeout((function(){t.mescroll.endSuccess()}),500)},upCallback:function(){var t=this;setTimeout((function(){t.mescroll.endErr()}),500)}},mounted:function(){this.mescrollInitByRef()}},n=r;e.default=n},c24b:function(t,e,i){"use strict";var r=i("1b11"),n=i.n(r);n.a},c495:function(t,e,i){var r=i("45ee");r.__esModule&&(r=r.default),"string"===typeof r&&(r=[[t.i,r,""]]),r.locals&&(t.exports=r.locals);var n=i("4f06").default;n("54b253da",r,!0,{sourceMap:!1,shadowMode:!1})},c60f:function(t,e,i){"use strict";i("7a82");var r=i("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.cancelIntegralOrder=function(t){return n.default.post("integral_order/cancel",{id:t})},e.closeBargainOrder=function(t){return n.default.get("bargain/closeBargain",{params:t})},e.confirmIntegralOrder=function(t){return n.default.post("integral_order/confirm",{id:t})},e.delIntegralOrder=function(t){return n.default.post("integral_order/del",{id:t})},e.getActivityGoodsLists=function(t){return n.default.get("activity_area/activityGoodsList",{params:t})},e.getBargainActivityDetail=function(t){return n.default.get("bargain/bargainDetail",{params:t})},e.getBargainActivityList=function(t){return n.default.get("bargain/orderList",{params:t})},e.getBargainDetail=function(t){return n.default.get("bargain/detail",{params:t})},e.getBargainList=function(t){return n.default.get("bargain/lists",{params:t})},e.getBargainNumber=function(){return n.default.get("bargain/barginNumber")},e.getBargainPost=function(t){return n.default.get("share/shareBargain",{params:t})},e.getCoupon=function(t){return n.default.post("coupon/getCoupon",{coupon_id:t})},e.getCouponList=function(t){return n.default.get("coupon/getCouponList",{params:t})},e.getGroupList=function(t){return n.default.get("team/activity",{params:t})},e.getIntegralGoods=function(t){return n.default.get("integral_goods/lists",{params:t})},e.getIntegralGoodsDetail=function(t){return n.default.get("integral_goods/detail",{params:t})},e.getIntegralOrder=function(t){return n.default.get("integral_order/lists",{params:t})},e.getIntegralOrderDetail=function(t){return n.default.get("integral_order/detail",{params:{id:t}})},e.getIntegralOrderTraces=function(t){return n.default.get("integral_order/orderTraces",{params:{id:t}})},e.getMyCoupon=function(t){return n.default.get("coupon/myCouponList",{params:t})},e.getOrderCoupon=function(t){return n.default.post("coupon/getBuyCouponList",t)},e.getSeckillGoods=function(t){return n.default.get("seckill_goods/getSeckillGoods",{params:t})},e.getSeckillTime=function(){return n.default.get("seckill_goods/getSeckillTime")},e.getSignLists=function(){return n.default.get("sign/lists")},e.getSignRule=function(){return n.default.get("sign/rule")},e.getTeamInfo=function(t){return n.default.get("team/teamInfo",{params:t})},e.getUserGroup=function(t){return n.default.get("team/record",{params:t})},e.helpBargain=function(t){return n.default.post("bargain/knife",t)},e.integralSettlement=function(t){return n.default.get("integral_order/settlement",{params:t})},e.integralSubmitOrder=function(t){return n.default.post("integral_order/submitOrder",t)},e.launchBargain=function(t){return n.default.post("bargain/sponsor",t)},e.teamBuy=function(t){return n.default.post("team/buy",t)},e.teamCheck=function(t){return n.default.post("team/check",t)},e.teamKaiTuan=function(t){return n.default.post("team/kaituan",t)},e.userSignIn=function(){return n.default.get("sign/sign")};var n=r(i("3b33"));i("b08d")},d5b0:function(t,e,i){"use strict";i.d(e,"b",(function(){return r})),i.d(e,"c",(function(){return n})),i.d(e,"a",(function(){}));var r=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-text",{class:(t.lineThrough?"line-through":"")+" price-format",style:{color:t.color,"font-weight":t.weight}},[t.showSubscript?i("v-uni-text",{style:{"font-size":t.subscriptSize+"rpx","margin-right":"2rpx"}},[t._v("¥")]):t._e(),i("v-uni-text",{style:{"font-size":t.firstSize+"rpx","margin-right":"1rpx"}},[t._v(t._s(t.priceSlice.first))]),t.priceSlice.second?i("v-uni-text",{style:{"font-size":t.secondSize+"rpx"}},[t._v("."+t._s(t.priceSlice.second))]):t._e()],1)},n=[]},d731:function(t,e,i){"use strict";i("7a82");var r=i("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("99af"),i("d3b7"),i("c740");var n=r(i("f07e")),a=r(i("c964")),o=i("c60f"),s=r(i("bde1")),u={mixins:[s.default],data:function(){return{active:0,upOption:{auto:!1,empty:{use:!0,icon:"/static/images/goods_null.png",tip:"暂无秒杀商品～"}},seckillTime:[],seckillGoods:[]}},methods:{downCallback:function(t){var e=this;return(0,a.default)((0,n.default)().mark((function t(){return(0,n.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.seckillGoods=[],t.next=3,e.getSeckillTimeFun();case 3:e.mescroll.resetUpScroll();case 4:case"end":return t.stop()}}),t)})))()},upCallback:function(t){var e=this,i=t.num,r=t.size,n=this.seckillTime,a=this.active;if(!n.length)return this.mescroll.endSuccess(0,!1);var s=n[a].id;(0,o.getSeckillGoods)({page_no:i,page_size:r,seckill_id:s}).then((function(t){var r=t.data.lists,n=!!t.data.more,a=r.length;1==i&&(e.seckillGoods=[]),e.seckillGoods=e.seckillGoods.concat(r),e.mescroll.endSuccess(a,n)})).catch((function(){e.mescroll.endErr()}))},getSeckillTimeFun:function(){var t=this;return new Promise((function(e){(0,o.getSeckillTime)().then((function(i){var r=i.code,n=i.data;if(1==r){var a=n.findIndex((function(t){return 1==t.status}));-1==a&&(a=n.findIndex((function(t){return 0==t.status}))),-1==a&&(a=n.length-1),t.seckillTime=n,t.$nextTick((function(){this.active=a,e()}))}}))}))},exchangeTime:function(t){this.active=t,this.seckillGoods=[],this.mescroll.resetUpScroll()}},computed:{currentStatus:function(){var t=this.active,e=this.seckillTime;return e[t]&&e[t].status}}};e.default=u},e07b:function(t,e,i){"use strict";i.r(e);var r=i("9238"),n=i("a3fe");for(var a in n)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(a);i("c24b");var o=i("f0c5"),s=Object(o["a"])(n["default"],r["b"],r["c"],!1,null,"30ff04e8",null,!1,r["a"],void 0);e["default"]=s.exports},e71b:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){return r}));var r={uImage:i("ba4b").default},n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return t.lists.length?i("v-uni-view",{staticClass:"swiper-wrap",style:{height:t.height,padding:t.padding}},[i("v-uni-view",{staticClass:"swiper-con",style:{borderRadius:t.radius}},[t.isSwiper?[i("v-uni-swiper",{staticClass:"swiper",attrs:{autoplay:t.autoplay,circular:t.circular,"previous-margin":t.previousMargin,"display-multiple-items":"1"},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.swiperChange.apply(void 0,arguments)}}},t._l(t.lists,(function(e,r){return i("v-uni-swiper-item",{key:r},[i("v-uni-view",{staticStyle:{width:"100%",height:"100%"},attrs:{"data-item":e},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.goPage(e)}}},[i("u-image",{attrs:{mode:"aspectFill",width:"calc(100% - "+t.previousMargin+")",height:"100%","border-radius":t.radius,src:e.image}})],1)],1)})),1),t.lists.length>1?i("v-uni-view",{staticClass:"dots"},t._l(t.lists,(function(e,r){return i("v-uni-view",{key:r,class:"dot "+(r==t.currentSwiper?"active":"")})})),1):t._e()]:t._e(),t._l(t.lists,(function(e,r){return[r<1?i("v-uni-view",{key:r,staticStyle:{width:"100%",height:"100%"},attrs:{"data-item":e},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.goPage(e)}}},[i("u-image",{attrs:{mode:"aspectFill",width:"calc(100% - "+t.previousMargin+")",height:"100%","border-radius":t.radius,src:e.image}})],1):t._e()]}))],2)],1):t._e()},a=[]},edb9:function(t,e,i){var r=i("89cb");r.__esModule&&(r=r.default),"string"===typeof r&&(r=[[t.i,r,""]]),r.locals&&(t.exports=r.locals);var n=i("4f06").default;n("0a552a2c",r,!0,{sourceMap:!1,shadowMode:!1})},ee17:function(t,e,i){"use strict";var r=i("c495"),n=i.n(r);n.a},ee9d:function(t,e,i){"use strict";i.r(e);var r=i("8c51"),n=i.n(r);for(var a in r)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return r[t]}))}(a);e["default"]=n.a},f041:function(t,e,i){var r=i("24fb");e=r(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.bg-gray[data-v-30ff04e8]{background-color:#ccc!important}.goods-seckill .time-list .time-item[data-v-30ff04e8]{display:inline-flex;width:%?160?%;height:100%}.goods-seckill .time-list .time-item.active .time[data-v-30ff04e8]{color:#ff2c3c}.goods-seckill .time-list .time-item.active .state[data-v-30ff04e8]{color:#fff;background-color:#ff2c3c}.goods-seckill .time-list .time-item .state[data-v-30ff04e8]{padding:0 %?10?%}.goods-seckill .endtime-box[data-v-30ff04e8]{height:%?100?%}.goods-seckill .endtime-box .line[data-v-30ff04e8]{width:%?100?%;height:%?2?%;background-color:#ccc}.goods-seckill .goods-list .goods-item[data-v-30ff04e8]{padding:%?30?%}.goods-seckill .goods-list .goods-item .goods-info[data-v-30ff04e8]{flex:1;width:%?470?%}.goods-seckill .goods-list .goods-item .goods-info .sale-info[data-v-30ff04e8]{padding:%?4?% %?16?%;background-color:#ffe9eb}.goods-seckill .goods-list .goods-item .goods-info .info-footer .btn[data-v-30ff04e8]{padding:0 %?30?%}.primary-btn[data-v-30ff04e8]{padding:0 %?30?%;background:linear-gradient(270deg,#ff2c3c,#f95f2f)}.border-btn[data-v-30ff04e8]{padding:0 %?30?%;border:1px solid #ff2c3c;color:#ff2c3c}',""]),t.exports=e},f893:function(t,e,i){"use strict";var r=i("edb9"),n=i.n(r);n.a},fefe:function(t,e,i){"use strict";i.r(e);var r=i("d5b0"),n=i("bd6f");for(var a in n)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(a);i("ee17");var o=i("f0c5"),s=Object(o["a"])(n["default"],r["b"],r["c"],!1,null,"0a5a34e0",null,!1,r["a"],void 0);e["default"]=s.exports}}]);