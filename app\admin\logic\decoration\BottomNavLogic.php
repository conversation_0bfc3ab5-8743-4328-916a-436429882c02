<?php

namespace app\admin\logic\decoration;
use app\common\basics\Logic;
use think\facade\Db;

class BottomNavLogic extends Logic{

    /**
     * Notes:获取底部导航
     * @param $get
     * @author: cjhao 2021/4/22 11:45
     */
    public static function lists(){
        $where[] = ['del','=',0];

        $lists = Db::name('bottom_nav')
                ->where($where)
                ->select();

        return ['lists'=>$lists];
    }

    /**
     * Notes:获取底部导航
     * @param $id
     * @return array|\think\Model|null
     * @author: cjhao 2021/4/22 15:00
     */
    public static function getBootomNav($id){
        return Db::name('bottom_nav')
                ->where(['id'=>$id,'del'=>0])
                ->find();
    }

    /**
     * Notes:更新底部导航
     * @param $post
     * @return int
     * @author: cjhao 2021/4/22 15:00
     */
    public static function edit($post){
        return Db::name('bottom_nav')
                ->where(['id'=>$post['id']])
                ->update($post);
    }
}