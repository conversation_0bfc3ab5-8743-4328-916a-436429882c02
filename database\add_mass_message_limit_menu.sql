-- 添加群发信息限制配置菜单
INSERT INTO `ls_dev_auth` (`type`, `system`, `pid`, `name`, `icon`, `uri`, `sort`, `disable`, `del`) 
VALUES 
(1, 0, 0, '群发信息限制配置', 'layui-icon-set', '', 50, 0, 0);

-- 获取刚插入的菜单ID
SET @menu_id = LAST_INSERT_ID();

-- 添加群发信息限制配置子菜单
INSERT INTO `ls_dev_auth` (`type`, `system`, `pid`, `name`, `icon`, `uri`, `sort`, `disable`, `del`) 
VALUES 
(1, 0, @menu_id, '配置列表', 'layui-icon-app', 'massmessagelimitconfig/index', 50, 0, 0);

-- 添加群发信息限制配置权限
INSERT INTO `ls_dev_auth` (`type`, `system`, `pid`, `name`, `icon`, `uri`, `sort`, `disable`, `del`) 
VALUES 
(2, 0, @menu_id, '添加配置', '', 'massmessagelimitconfig/add', 50, 0, 0),
(2, 0, @menu_id, '编辑配置', '', 'massmessagelimitconfig/edit', 50, 0, 0),
(2, 0, @menu_id, '删除配置', '', 'massmessagelimitconfig/delete', 50, 0, 0);
