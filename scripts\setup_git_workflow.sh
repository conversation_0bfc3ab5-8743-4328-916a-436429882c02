#!/bin/bash

# Git工作流初始化脚本
# 用法: ./setup_git_workflow.sh

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查Git仓库
check_git_repo() {
    if [ ! -d ".git" ]; then
        log_error "当前目录不是Git仓库！"
        echo "请先初始化Git仓库："
        echo "  git init"
        echo "  git remote add origin <repository_url>"
        exit 1
    fi
}

# 创建分支结构
setup_branches() {
    log_step "设置Git分支结构..."
    
    # 确保在master分支
    if ! git show-ref --verify --quiet refs/heads/master; then
        log_info "创建master分支..."
        git checkout -b master
    else
        git checkout master
    fi
    
    # 创建develop分支
    if ! git show-ref --verify --quiet refs/heads/develop; then
        log_info "创建develop分支..."
        git checkout -b develop master
    else
        log_info "develop分支已存在"
        git checkout develop
    fi
    
    # 推送分支到远程
    log_info "推送分支到远程仓库..."
    git push -u origin master || log_warn "master分支推送失败，可能已存在"
    git push -u origin develop || log_warn "develop分支推送失败，可能已存在"
}

# 设置Git钩子
setup_git_hooks() {
    log_step "设置Git钩子..."
    
    local hooks_dir=".git/hooks"
    
    # 创建pre-commit钩子
    cat > "$hooks_dir/pre-commit" << 'EOF'
#!/bin/bash
# Pre-commit hook for kshop project

echo "🔍 执行提交前检查..."

# 检查PHP语法错误
if command -v php >/dev/null 2>&1; then
    echo "检查PHP语法..."
    for file in $(git diff --cached --name-only --diff-filter=ACM | grep '\.php$'); do
        if [ -f "$file" ]; then
            php -l "$file"
            if [ $? -ne 0 ]; then
                echo "❌ PHP语法错误: $file"
                exit 1
            fi
        fi
    done
fi

# 检查敏感信息
echo "检查敏感信息..."
if git diff --cached --name-only | xargs grep -l "password\|secret\|key" 2>/dev/null; then
    echo "⚠️  检测到可能包含敏感信息的文件，请确认："
    git diff --cached --name-only | xargs grep -n "password\|secret\|key" 2>/dev/null || true
    read -p "确认继续提交？(y/N): " -n 1 -r
    echo ""
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "❌ 提交已取消"
        exit 1
    fi
fi

echo "✅ 提交前检查通过"
EOF

    chmod +x "$hooks_dir/pre-commit"
    
    # 创建commit-msg钩子
    cat > "$hooks_dir/commit-msg" << 'EOF'
#!/bin/bash
# Commit message hook for kshop project

commit_regex='^(feat|fix|docs|style|refactor|test|chore)(\(.+\))?: .{1,50}'

if ! grep -qE "$commit_regex" "$1"; then
    echo "❌ 提交信息格式不正确！"
    echo ""
    echo "正确格式: type(scope): description"
    echo ""
    echo "类型 (type):"
    echo "  feat:     新功能"
    echo "  fix:      修复bug"
    echo "  docs:     文档更新"
    echo "  style:    代码格式调整"
    echo "  refactor: 代码重构"
    echo "  test:     测试相关"
    echo "  chore:    构建/工具相关"
    echo ""
    echo "示例:"
    echo "  feat(user): 添加用户登录功能"
    echo "  fix(order): 修复订单状态更新问题"
    echo "  docs: 更新API文档"
    exit 1
fi
EOF

    chmod +x "$hooks_dir/commit-msg"
    
    log_info "Git钩子设置完成"
}

# 创建.gitignore文件
setup_gitignore() {
    log_step "检查.gitignore文件..."
    
    if [ ! -f ".gitignore" ]; then
        log_info "创建.gitignore文件..."
        cat > .gitignore << 'EOF'
# IDE
/.idea
/.vscode
*.swp
*.swo

# Logs
*.log
/runtime/*
!/runtime/index.html

# Environment
.env
.env.local
.env.production

# Dependencies
/vendor/
/node_modules/

# Uploads
/public/uploads/*
!/public/uploads/index.html

# Cache
/runtime/cache/*
/runtime/log/*
/runtime/temp/*

# Config
/config/install.lock

# Build
/public/upgrade/*
!/public/uploads/version.json

# OS
.DS_Store
Thumbs.db

# Backup
*.bak
*.backup

# Version control
.yoyo/
EOF
    else
        log_info ".gitignore文件已存在"
    fi
}

# 创建工作流文档
create_workflow_docs() {
    log_step "创建工作流文档..."
    
    mkdir -p docs
    
    cat > docs/git-workflow.md << 'EOF'
# Git工作流程

## 分支说明

- `master`: 正式版分支，对应生产环境
- `develop`: 测试版分支，对应测试环境
- `feature/*`: 功能开发分支

## 开发流程

### 1. 功能开发

```bash
# 从develop分支创建功能分支
git checkout develop
git pull origin develop
git checkout -b feature/new-feature

# 开发完成后提交
git add .
git commit -m "feat(module): 添加新功能"
git push origin feature/new-feature

# 合并到develop分支
git checkout develop
git merge feature/new-feature
git push origin develop
```

### 2. 测试环境部署

```bash
# 部署到测试环境
./scripts/deploy.sh test
```

### 3. 正式环境发布

```bash
# 测试通过后，同步到正式环境
./scripts/sync_test_to_prod.sh v1.0.1
```

### 4. 版本管理

```bash
# 查看当前版本
./scripts/version_manager.sh current

# 列出所有版本
./scripts/version_manager.sh list

# 版本回退
./scripts/version_manager.sh rollback

# 清理旧版本
./scripts/version_manager.sh cleanup
```

## 提交信息规范

格式: `type(scope): description`

类型:
- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建/工具相关

示例:
- `feat(user): 添加用户登录功能`
- `fix(order): 修复订单状态更新问题`
- `docs: 更新API文档`

## 注意事项

1. 不要直接在master分支开发
2. 功能开发完成后及时删除feature分支
3. 正式环境发布前必须在测试环境验证
4. 重要更新前做好数据备份
5. 遵循提交信息规范
EOF

    log_info "工作流文档已创建: docs/git-workflow.md"
}

# 设置脚本权限
setup_script_permissions() {
    log_step "设置脚本执行权限..."
    
    chmod +x scripts/*.sh
    
    log_info "脚本权限设置完成"
}

# 显示完成信息
show_completion_info() {
    echo ""
    echo "=================================="
    echo "🎉 Git工作流设置完成！"
    echo "=================================="
    echo ""
    echo "📋 已创建的分支:"
    echo "  - master (正式版)"
    echo "  - develop (测试版)"
    echo ""
    echo "🔧 已设置的工具:"
    echo "  - Git钩子 (pre-commit, commit-msg)"
    echo "  - 部署脚本 (scripts/deploy.sh)"
    echo "  - 同步脚本 (scripts/sync_test_to_prod.sh)"
    echo "  - 版本管理 (scripts/version_manager.sh)"
    echo ""
    echo "📖 文档:"
    echo "  - docs/git-workflow.md"
    echo ""
    echo "🚀 下一步操作:"
    echo "1. 修改scripts/deploy.sh中的仓库地址"
    echo "2. 修改scripts/sync_test_to_prod.sh中的仓库地址"
    echo "3. 根据实际情况调整服务器路径配置"
    echo "4. 开始使用工作流进行开发"
    echo ""
    echo "💡 快速开始:"
    echo "  git checkout develop              # 切换到开发分支"
    echo "  ./scripts/deploy.sh test          # 部署测试环境"
    echo "  ./scripts/sync_test_to_prod.sh    # 同步到正式环境"
    echo "=================================="
}

# 主函数
main() {
    log_info "开始设置Git工作流..."
    
    check_git_repo
    setup_branches
    setup_git_hooks
    setup_gitignore
    create_workflow_docs
    setup_script_permissions
    show_completion_info
    
    log_info "Git工作流设置完成！"
}

# 执行主函数
main "$@"
