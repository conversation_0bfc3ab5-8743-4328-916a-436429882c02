{"version": 3, "sources": ["webpack:///webpack/bootstrap", "webpack:///./src/styles/element-variables.scss?5e2d", "webpack:///./src/store/modules/user.js", "webpack:///./src/store/modules/app.js", "webpack:///./src/store/modules/index.js", "webpack:///./src/store/getters.js", "webpack:///./src/store/index.js", "webpack:///./src/App.vue?d7a1", "webpack:///./src/App.vue", "webpack:///src/App.vue", "webpack:///./src/App.vue?4c4f", "webpack:///./src/App.vue?3746", "webpack:///./src/plugins/element.js", "webpack:///./src/permission.js", "webpack:///./src/main.js", "webpack:///./src/router/index.js", "webpack:///./src/api/app.js", "webpack:///./src/plugins/axios.js", "webpack:///./src/App.vue?7615", "webpack:///./src/utils/util.js", "webpack:///./src/config.js"], "names": ["webpackJsonpCallback", "data", "moduleId", "chunkId", "chunkIds", "moreModules", "executeModules", "i", "resolves", "length", "Object", "prototype", "hasOwnProperty", "call", "installedChunks", "push", "modules", "parentJsonpFunction", "shift", "deferredModules", "apply", "checkDeferredModules", "result", "deferredModule", "fulfilled", "j", "depId", "splice", "__webpack_require__", "s", "installedModules", "installedCssChunks", "jsonpScriptSrc", "p", "exports", "module", "l", "e", "promises", "cssChunks", "Promise", "resolve", "reject", "href", "fullhref", "existingLinkTags", "document", "getElementsByTagName", "tag", "dataHref", "getAttribute", "rel", "existingStyleTags", "linkTag", "createElement", "type", "onload", "onerror", "event", "request", "target", "src", "err", "Error", "code", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "head", "append<PERSON><PERSON><PERSON>", "then", "installedChunkData", "promise", "onScriptComplete", "script", "charset", "timeout", "nc", "setAttribute", "error", "clearTimeout", "chunk", "errorType", "realSrc", "message", "name", "undefined", "setTimeout", "all", "m", "c", "d", "getter", "o", "defineProperty", "enumerable", "get", "r", "Symbol", "toStringTag", "value", "t", "mode", "__esModule", "ns", "create", "key", "bind", "n", "object", "property", "oe", "console", "jsonpArray", "window", "oldJsonpFunction", "slice", "state", "token", "userInfo", "mutations", "SET_USER_INFO", "SET_TOKEN", "clearUserInfo", "actions", "login", "commit", "form", "api<PERSON><PERSON><PERSON>", "catch", "getUserInfo", "apiKefuDetail", "logout", "user", "config", "setConfig", "app", "shopId", "shop_id", "baseUrl", "base_domain", "copyright", "company_name", "wsUrl", "ws_domain", "<PERSON><PERSON>", "use", "Vuex", "vuexLocal", "VuexPersistence", "storage", "localStorage", "Store", "getters", "plugins", "plugin", "render", "_vm", "this", "_c", "_self", "attrs", "routerAlive", "_e", "staticRenderFns", "provide", "reload", "methods", "created", "apiGetConfig", "favicon", "component", "Element", "loginPath", "defaultPath", "whiteList", "NProgress", "configure", "showSpinner", "router", "beforeEach", "to", "from", "next", "start", "meta", "title", "query", "store", "path", "includes", "redirect", "fullPath", "after<PERSON>ach", "done", "OverlayScrollbarsComponent", "productionTip", "h", "App", "$mount", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "routes", "base", "process", "post", "client", "apiChatUserList", "params", "apiChatHistory", "apiServiceList", "apiReplyList", "apiUserInfo", "apiUserOrder", "eventResponse", "success", "show", "msg", "Message", "log", "throttle", "dispatch", "replace", "baseURL", "configs", "headers", "_axios", "axios", "interceptors", "response", "Plugin", "install", "options", "defineProperties", "$axios", "fn", "time", "previousTime", "Date", "getTime", "args", "nowTime", "debounce", "delay", "fun", "self", "arguments", "objectToQuery", "_result", "Array", "isArray", "for<PERSON>ach", "_value", "join", "version"], "mappings": "aACE,SAASA,EAAqBC,GAQ7B,IAPA,IAMIC,EAAUC,EANVC,EAAWH,EAAK,GAChBI,EAAcJ,EAAK,GACnBK,EAAiBL,EAAK,GAIHM,EAAI,EAAGC,EAAW,GACpCD,EAAIH,EAASK,OAAQF,IACzBJ,EAAUC,EAASG,GAChBG,OAAOC,UAAUC,eAAeC,KAAKC,EAAiBX,IAAYW,EAAgBX,IACpFK,EAASO,KAAKD,EAAgBX,GAAS,IAExCW,EAAgBX,GAAW,EAE5B,IAAID,KAAYG,EACZK,OAAOC,UAAUC,eAAeC,KAAKR,EAAaH,KACpDc,EAAQd,GAAYG,EAAYH,IAG/Be,GAAqBA,EAAoBhB,GAE5C,MAAMO,EAASC,OACdD,EAASU,OAATV,GAOD,OAHAW,EAAgBJ,KAAKK,MAAMD,EAAiBb,GAAkB,IAGvDe,IAER,SAASA,IAER,IADA,IAAIC,EACIf,EAAI,EAAGA,EAAIY,EAAgBV,OAAQF,IAAK,CAG/C,IAFA,IAAIgB,EAAiBJ,EAAgBZ,GACjCiB,GAAY,EACRC,EAAI,EAAGA,EAAIF,EAAed,OAAQgB,IAAK,CAC9C,IAAIC,EAAQH,EAAeE,GACG,IAA3BX,EAAgBY,KAAcF,GAAY,GAE3CA,IACFL,EAAgBQ,OAAOpB,IAAK,GAC5Be,EAASM,EAAoBA,EAAoBC,EAAIN,EAAe,KAItE,OAAOD,EAIR,IAAIQ,EAAmB,GAGnBC,EAAqB,CACxB,IAAO,GAMJjB,EAAkB,CACrB,IAAO,GAGJK,EAAkB,GAGtB,SAASa,EAAe7B,GACvB,OAAOyB,EAAoBK,EAAI,OAAS,GAAG9B,IAAUA,GAAW,IAAM,CAAC,iBAAiB,WAAW,iBAAiB,YAAYA,GAAW,MAI5I,SAASyB,EAAoB1B,GAG5B,GAAG4B,EAAiB5B,GACnB,OAAO4B,EAAiB5B,GAAUgC,QAGnC,IAAIC,EAASL,EAAiB5B,GAAY,CACzCK,EAAGL,EACHkC,GAAG,EACHF,QAAS,IAUV,OANAlB,EAAQd,GAAUW,KAAKsB,EAAOD,QAASC,EAAQA,EAAOD,QAASN,GAG/DO,EAAOC,GAAI,EAGJD,EAAOD,QAKfN,EAAoBS,EAAI,SAAuBlC,GAC9C,IAAImC,EAAW,GAIXC,EAAY,CAAC,iBAAiB,EAAE,iBAAiB,GAClDR,EAAmB5B,GAAUmC,EAASvB,KAAKgB,EAAmB5B,IACzB,IAAhC4B,EAAmB5B,IAAkBoC,EAAUpC,IACtDmC,EAASvB,KAAKgB,EAAmB5B,GAAW,IAAIqC,SAAQ,SAASC,EAASC,GAIzE,IAHA,IAAIC,EAAO,QAAU,GAAGxC,IAAUA,GAAW,IAAM,CAAC,iBAAiB,WAAW,iBAAiB,YAAYA,GAAW,OACpHyC,EAAWhB,EAAoBK,EAAIU,EACnCE,EAAmBC,SAASC,qBAAqB,QAC7CxC,EAAI,EAAGA,EAAIsC,EAAiBpC,OAAQF,IAAK,CAChD,IAAIyC,EAAMH,EAAiBtC,GACvB0C,EAAWD,EAAIE,aAAa,cAAgBF,EAAIE,aAAa,QACjE,GAAe,eAAZF,EAAIG,MAAyBF,IAAaN,GAAQM,IAAaL,GAAW,OAAOH,IAErF,IAAIW,EAAoBN,SAASC,qBAAqB,SACtD,IAAQxC,EAAI,EAAGA,EAAI6C,EAAkB3C,OAAQF,IAAK,CAC7CyC,EAAMI,EAAkB7C,GACxB0C,EAAWD,EAAIE,aAAa,aAChC,GAAGD,IAAaN,GAAQM,IAAaL,EAAU,OAAOH,IAEvD,IAAIY,EAAUP,SAASQ,cAAc,QACrCD,EAAQF,IAAM,aACdE,EAAQE,KAAO,WACfF,EAAQG,OAASf,EACjBY,EAAQI,QAAU,SAASC,GAC1B,IAAIC,EAAUD,GAASA,EAAME,QAAUF,EAAME,OAAOC,KAAOjB,EACvDkB,EAAM,IAAIC,MAAM,qBAAuB5D,EAAU,cAAgBwD,EAAU,KAC/EG,EAAIE,KAAO,wBACXF,EAAIH,QAAUA,SACP5B,EAAmB5B,GAC1BkD,EAAQY,WAAWC,YAAYb,GAC/BX,EAAOoB,IAERT,EAAQV,KAAOC,EAEf,IAAIuB,EAAOrB,SAASC,qBAAqB,QAAQ,GACjDoB,EAAKC,YAAYf,MACfgB,MAAK,WACPtC,EAAmB5B,GAAW,MAMhC,IAAImE,EAAqBxD,EAAgBX,GACzC,GAA0B,IAAvBmE,EAGF,GAAGA,EACFhC,EAASvB,KAAKuD,EAAmB,QAC3B,CAEN,IAAIC,EAAU,IAAI/B,SAAQ,SAASC,EAASC,GAC3C4B,EAAqBxD,EAAgBX,GAAW,CAACsC,EAASC,MAE3DJ,EAASvB,KAAKuD,EAAmB,GAAKC,GAGtC,IACIC,EADAC,EAAS3B,SAASQ,cAAc,UAGpCmB,EAAOC,QAAU,QACjBD,EAAOE,QAAU,IACb/C,EAAoBgD,IACvBH,EAAOI,aAAa,QAASjD,EAAoBgD,IAElDH,EAAOZ,IAAM7B,EAAe7B,GAG5B,IAAI2E,EAAQ,IAAIf,MAChBS,EAAmB,SAAUd,GAE5Be,EAAOhB,QAAUgB,EAAOjB,OAAS,KACjCuB,aAAaJ,GACb,IAAIK,EAAQlE,EAAgBX,GAC5B,GAAa,IAAV6E,EAAa,CACf,GAAGA,EAAO,CACT,IAAIC,EAAYvB,IAAyB,SAAfA,EAAMH,KAAkB,UAAYG,EAAMH,MAChE2B,EAAUxB,GAASA,EAAME,QAAUF,EAAME,OAAOC,IACpDiB,EAAMK,QAAU,iBAAmBhF,EAAU,cAAgB8E,EAAY,KAAOC,EAAU,IAC1FJ,EAAMM,KAAO,iBACbN,EAAMvB,KAAO0B,EACbH,EAAMnB,QAAUuB,EAChBF,EAAM,GAAGF,GAEVhE,EAAgBX,QAAWkF,IAG7B,IAAIV,EAAUW,YAAW,WACxBd,EAAiB,CAAEjB,KAAM,UAAWK,OAAQa,MAC1C,MACHA,EAAOhB,QAAUgB,EAAOjB,OAASgB,EACjC1B,SAASqB,KAAKC,YAAYK,GAG5B,OAAOjC,QAAQ+C,IAAIjD,IAIpBV,EAAoB4D,EAAIxE,EAGxBY,EAAoB6D,EAAI3D,EAGxBF,EAAoB8D,EAAI,SAASxD,EAASkD,EAAMO,GAC3C/D,EAAoBgE,EAAE1D,EAASkD,IAClC1E,OAAOmF,eAAe3D,EAASkD,EAAM,CAAEU,YAAY,EAAMC,IAAKJ,KAKhE/D,EAAoBoE,EAAI,SAAS9D,GACX,qBAAX+D,QAA0BA,OAAOC,aAC1CxF,OAAOmF,eAAe3D,EAAS+D,OAAOC,YAAa,CAAEC,MAAO,WAE7DzF,OAAOmF,eAAe3D,EAAS,aAAc,CAAEiE,OAAO,KAQvDvE,EAAoBwE,EAAI,SAASD,EAAOE,GAEvC,GADU,EAAPA,IAAUF,EAAQvE,EAAoBuE,IAC/B,EAAPE,EAAU,OAAOF,EACpB,GAAW,EAAPE,GAA8B,kBAAVF,GAAsBA,GAASA,EAAMG,WAAY,OAAOH,EAChF,IAAII,EAAK7F,OAAO8F,OAAO,MAGvB,GAFA5E,EAAoBoE,EAAEO,GACtB7F,OAAOmF,eAAeU,EAAI,UAAW,CAAET,YAAY,EAAMK,MAAOA,IACtD,EAAPE,GAA4B,iBAATF,EAAmB,IAAI,IAAIM,KAAON,EAAOvE,EAAoB8D,EAAEa,EAAIE,EAAK,SAASA,GAAO,OAAON,EAAMM,IAAQC,KAAK,KAAMD,IAC9I,OAAOF,GAIR3E,EAAoB+E,EAAI,SAASxE,GAChC,IAAIwD,EAASxD,GAAUA,EAAOmE,WAC7B,WAAwB,OAAOnE,EAAO,YACtC,WAA8B,OAAOA,GAEtC,OADAP,EAAoB8D,EAAEC,EAAQ,IAAKA,GAC5BA,GAIR/D,EAAoBgE,EAAI,SAASgB,EAAQC,GAAY,OAAOnG,OAAOC,UAAUC,eAAeC,KAAK+F,EAAQC,IAGzGjF,EAAoBK,EAAI,SAGxBL,EAAoBkF,GAAK,SAAShD,GAA2B,MAApBiD,QAAQjC,MAAMhB,GAAYA,GAEnE,IAAIkD,EAAaC,OAAO,gBAAkBA,OAAO,iBAAmB,GAChEC,EAAmBF,EAAWjG,KAAK2F,KAAKM,GAC5CA,EAAWjG,KAAOf,EAClBgH,EAAaA,EAAWG,QACxB,IAAI,IAAI5G,EAAI,EAAGA,EAAIyG,EAAWvG,OAAQF,IAAKP,EAAqBgH,EAAWzG,IAC3E,IAAIU,EAAsBiG,EAI1B/F,EAAgBJ,KAAK,CAAC,EAAE,kBAEjBM,K,gECzQTc,EAAOD,QAAU,CAAC,QAAU,Y,sFCQ5B,MAAMkF,EAAQ,CACVC,MAAO,GACPC,SAAU,IAKRC,EAAW,CAEbC,cAAe,CAACJ,EAAOnH,KACnBmH,EAAME,SAAWrH,GAGrBwH,UAAW,CAACL,EAAOC,KACfD,EAAMC,MAAQA,GAIlBK,cAAgBN,IACZA,EAAME,SAAW,KAMnBK,EAAU,CACZC,OAAM,OAACC,GAASC,GACZ,OAAO,IAAItF,QAAQ,CAACC,EAASC,KACzBqF,eAASD,GAAMzD,KAAKpE,IAChB4H,EAAO,YAAa5H,EAAKoH,OACzB5E,EAAQxC,KACT+H,MAAMlE,IACLpB,EAAOoB,QAKnBmE,aAAY,OAACJ,IACT,OAAO,IAAIrF,QAAQ,CAACC,EAASC,KACzBwF,iBAAgB7D,KAAKpE,IACjB4H,EAAO,gBAAiB5H,GACxBwC,EAAQxC,KACT+H,MAAMlE,UAMjBqE,QAAO,OAACN,IACJ,OAAO,IAAIrF,QAAQ,CAACC,EAASC,KACzBmF,EAAO,YAAa,IACpBA,EAAO,iBACPpF,QAON2F,EAAO,CACThB,QACAG,YACAI,WAGWS,QCvEf,MAAMhB,EAAQ,CACViB,OAAQ,IAINd,EAAY,CACde,UAAUlB,EAAOnH,GACbmH,EAAMiB,OAASpI,IAKjB0H,EAAU,GAIVS,EAAO,CACThB,MAAK,EACLG,UAAS,EACTI,QAAOA,GAGIS,QCtBA,GACXA,OACAG,OCLW,GAEXlB,MAAQD,GAAUA,EAAMgB,KAAKf,OAAS,KAEtCmB,OAAQpB,IAAK,aAAwB,QAAnB,EAAAA,EAAMgB,KAAKd,gBAAQ,aAAnB,EAAqBmB,UAAW,GAElDnB,SAAUF,GAASA,EAAMgB,KAAKd,SAC9BoB,QAAUtB,GAASA,EAAMmB,IAAIF,OAAOM,YACpCC,UAAWxB,GAASA,EAAMmB,IAAIF,OAAOQ,aACrCC,MAAO1B,GAAUA,EAAMmB,IAAIF,OAAOU,WCJtCC,aAAIC,IAAIC,QAER,MAAMC,EAAY,IAAIC,OAAgB,CAClC3C,IAAK,WACL4C,QAASpC,OAAOqC,aAChBtI,QAAS,CAAC,UAGC,WAAIkI,OAAKK,MAAM,CAC1BvI,QAASA,EACZwI,QAASA,EACNC,QAAS,CAACN,EAAUO,W,oCChBxB,W,2DCAIC,G,UAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,MAAM,CAAC,GAAK,QAAQ,CAAEJ,EAAIK,YAAaH,EAAG,eAAeF,EAAIM,MAAM,KAE5IC,EAAkB,G,YCOP,GACflK,OACA,OACAgK,iBAGAG,UACA,OACAC,qBAGAC,SACAD,SACA,oBACA,oBACA,wBAIAE,UACAC,0BACA,kCACA,iDACA,EACAC,sBAGAA,iCACAA,aACAA,qBACA3H,kCCvC8T,I,wBCQ1T4H,EAAY,eACd,EACAf,EACAQ,GACA,EACA,KACA,KACA,MAIa,EAAAO,E,+DChBf1B,aAAIC,IAAI0B,K,yBCER,MAAMC,EAAY,SACZC,EAAc,IACdC,EAAY,CAAC,SACnBC,IAAUC,UAAU,CAAEC,aAAa,IAEnCC,OAAOC,WAAW,CAACC,EAAIC,EAAMC,KAAS,MAClCP,IAAUQ,SAEH,QAAP,EAAAH,EAAGI,YAAI,aAAP,EAASC,SAAU3I,SAAS2I,MAAQL,EAAGI,KAAKC,OAC5C,IAAI,MAAEpE,GAAU+D,EAAGM,MAChBrE,GACCsE,OAAM9D,OAAO,YAAaR,GAE9BA,EAAQA,GAASsE,OAAMnC,QAAQnC,MAC3BA,EACI+D,EAAGQ,OAAShB,EACZU,EAAK,CAAEM,KAAMf,IAEbS,IAGAR,EAAUe,SAAST,EAAGhG,MAEtBkG,IAEAA,EAAK,CAAEM,KAAMhB,EAAWc,MAAO,CAAEI,SAAUV,EAAGW,cAO1Db,OAAOc,UAAU,KACbjB,IAAUkB,S,oCCzBdjD,aAAI0B,UAAU,qBAAsBwB,QAEpClD,aAAIX,OAAO8D,eAAgB,EAE3B,IAAInD,aAAI,CACNkC,cACAS,aACAhC,OAAQyC,GAAKA,EAAEC,KACdC,OAAO,S,kCCrBV,4BAGAtD,aAAIC,IAAIsD,QAER,MAAMC,EAAS,CACX,CACIZ,KAAM,IACNE,SAAU,cAEd,CACIF,KAAM,IACNxG,KAAM,QACNoG,KAAM,CACFC,MAAO,MAEXf,UAAW,IAAM,iDAErB,CACIkB,KAAM,SACNxG,KAAM,QACNoG,KAAM,CACFC,MAAO,MAEXf,UAAW,IAAM,kDAInBQ,EAAS,IAAIqB,OAAU,CACzBlG,KAAM,UACNoG,KAAMC,SACNF,WAGWtB,U,kCClCf,kUAGO,MAAMnD,EAAW9H,GAAQ0D,OAAQgJ,KAAK,yBAA0B,IAAI1M,EAAM2M,OAAQ,IAQ5EC,EAAkBC,GAAUnJ,OAAQoC,IAAI,qBAAsB,CAAC+G,WAG/DC,EAAiBD,GAAUnJ,OAAQoC,IAAI,uBAAwB,CAAC+G,WAGhEE,EAAiBF,GAAUnJ,OAAQoC,IAAI,uBAAwB,CAAC+G,WAIhEG,EAAeH,GAAUnJ,OAAQoC,IAAI,sBAAuB,CAAC+G,WAI7DI,EAAcJ,GAAUnJ,OAAQoC,IAAI,yBAA0B,CAAC+G,WAG/DK,EAAeL,GAAUnJ,OAAQoC,IAAI,sBAAuB,CAAC+G,WAM7D5E,EAAgB,IAAMvE,OAAQoC,IAAI,0BAGlCyE,EAAe,IAAM7G,OAAQoC,IAAI,yB,yDCrC9C,iGAWA,MAAMqH,EAAgB,CAErBC,SAAQ,KAAEC,EAAI,IAAEC,EAAG,KAAEtN,IAEpB,OADW,EAAPqN,GAAUE,qBAAQ,CAAEjK,KAAM,UAAW4B,QAASoI,IAC3CtN,GAGR6E,OAAM,KAAEwI,EAAI,IAAEC,IAGb,OAFW,EAAPD,GAAUE,qBAAQ,CAAEjK,KAAM,QAAS4B,QAASoI,IAChDxG,QAAQ0G,IAAI,YACLjL,QAAQE,UAGhBoJ,SAAU4B,eAAS,KACZ/B,OAAMgC,SAAS,UACfzC,OAAO0C,QAAQ,UACdpL,QAAQE,YAMjB,IAAI2F,EAAS,CACTwF,QAAU,GAAEC,OAAQD,QACpBE,QAAS,CACL,eAAgB,qBAIxB,MAAMC,EAASC,IAAMzH,OAAO6B,GAE5B2F,EAAOE,aAAavK,QAAQsF,KACxB,SAAUZ,GAEZ,MAAMhB,EAAQsE,OAAMnC,QAAQnC,MAGtB,OADFA,IAAOgB,EAAO0F,QAAQ,SAAW1G,GACxBgB,KAEX,SAAUvD,GAGN,OAAOtC,QAAQE,OAAOoC,MAK9BkJ,EAAOE,aAAaC,SAASlF,KACzB,SAAUkF,GAEZ,MAAM,KAAEnK,GAASmK,EAASlO,KAC1B,OAAO+D,GACN,KAAK,EAAG,OAAOoJ,EAAcC,QAAQc,EAASlO,MAC9C,KAAK,EAAG,OAAOmN,EAActI,MAAMqJ,EAASlO,MAC5C,KAAM,EAAG,OAAOmN,EAActB,SAASqC,EAASlO,UAG/C,SAAU6E,GAGN,OADN0I,qBAAQ,CAAEjK,KAAM,QAAS4B,QAAS,eACrB3C,QAAQE,OAAOoC,MAI9BsJ,OAAOC,QAAU,SAAUrF,EAAKsF,GAC5BtF,EAAIiF,MAAQD,EACZ/G,OAAOgH,MAAQD,EACftN,OAAO6N,iBAAiBvF,EAAIrI,UAAW,CACnCsN,MAAO,CACHlI,MACI,OAAOiI,IAGfQ,OAAQ,CACJzI,MACI,OAAOiI,OAMvBhF,aAAIC,IAAImF,QAEOJ,U,qBC7Ff7L,EAAOD,QAAU,CAAC,QAAU,Y,sKCMrB,MAAMwL,EAAW,CACpBe,EACAC,EAAO,OAEP,IAAIC,EAAe,IAAIC,KAAK,GAAGC,UAC/B,MAAO,IAAIC,KACP,IAAIC,GAAU,IAAIH,MAAOC,UACzB,GAAIE,EAAUJ,EAAeD,EAEzB,OADAC,EAAeI,EACRN,EAAGK,KAYf,SAASE,EAASC,EAAOC,GAC/B,IAAIR,EAAO,KACX,OAAO,WACN,IAAIS,EAAOtF,KACP6E,GAAM3J,aAAa2J,GACvBA,EAAOpJ,WAAW,IACV4J,EAAI9N,MAAM+N,EAAMC,WACrBH,IAWE,MA8CMI,EAAiBpP,IAC1B,IAAIqP,EAAU,GAEd,IAAK,IAAI7I,KAAOxG,EAAM,CAClB,IAAIkG,EAAQlG,EAAKwG,GACb8I,MAAMC,QAAQrJ,GACdA,EAAMsJ,QAAQC,IACVJ,EAAQvO,KAAK0F,EAAM,IAAMiJ,KAG7BJ,EAAQvO,KAAK0F,EAAM,IAAMN,GAIjC,OAAOmJ,EAAQK,KAAK,O,kCCtGT,QAEXC,QAAS,iBACT/B,QAAgD", "file": "js/app.d1997adf.js", "sourcesContent": [" \t// install a JSONP callback for chunk loading\n \tfunction webpackJsonpCallback(data) {\n \t\tvar chunkIds = data[0];\n \t\tvar moreModules = data[1];\n \t\tvar executeModules = data[2];\n\n \t\t// add \"moreModules\" to the modules object,\n \t\t// then flag all \"chunkIds\" as loaded and fire callback\n \t\tvar moduleId, chunkId, i = 0, resolves = [];\n \t\tfor(;i < chunkIds.length; i++) {\n \t\t\tchunkId = chunkIds[i];\n \t\t\tif(Object.prototype.hasOwnProperty.call(installedChunks, chunkId) && installedChunks[chunkId]) {\n \t\t\t\tresolves.push(installedChunks[chunkId][0]);\n \t\t\t}\n \t\t\tinstalledChunks[chunkId] = 0;\n \t\t}\n \t\tfor(moduleId in moreModules) {\n \t\t\tif(Object.prototype.hasOwnProperty.call(moreModules, moduleId)) {\n \t\t\t\tmodules[moduleId] = moreModules[moduleId];\n \t\t\t}\n \t\t}\n \t\tif(parentJsonpFunction) parentJsonpFunction(data);\n\n \t\twhile(resolves.length) {\n \t\t\tresolves.shift()();\n \t\t}\n\n \t\t// add entry modules from loaded chunk to deferred list\n \t\tdeferredModules.push.apply(deferredModules, executeModules || []);\n\n \t\t// run deferred modules when all chunks ready\n \t\treturn checkDeferredModules();\n \t};\n \tfunction checkDeferredModules() {\n \t\tvar result;\n \t\tfor(var i = 0; i < deferredModules.length; i++) {\n \t\t\tvar deferredModule = deferredModules[i];\n \t\t\tvar fulfilled = true;\n \t\t\tfor(var j = 1; j < deferredModule.length; j++) {\n \t\t\t\tvar depId = deferredModule[j];\n \t\t\t\tif(installedChunks[depId] !== 0) fulfilled = false;\n \t\t\t}\n \t\t\tif(fulfilled) {\n \t\t\t\tdeferredModules.splice(i--, 1);\n \t\t\t\tresult = __webpack_require__(__webpack_require__.s = deferredModule[0]);\n \t\t\t}\n \t\t}\n\n \t\treturn result;\n \t}\n\n \t// The module cache\n \tvar installedModules = {};\n\n \t// object to store loaded CSS chunks\n \tvar installedCssChunks = {\n \t\t\"app\": 0\n \t}\n\n \t// object to store loaded and loading chunks\n \t// undefined = chunk not loaded, null = chunk preloaded/prefetched\n \t// Promise = chunk loading, 0 = chunk loaded\n \tvar installedChunks = {\n \t\t\"app\": 0\n \t};\n\n \tvar deferredModules = [];\n\n \t// script path function\n \tfunction jsonpScriptSrc(chunkId) {\n \t\treturn __webpack_require__.p + \"js/\" + ({}[chunkId]||chunkId) + \".\" + {\"chunk-449349ae\":\"96d1a4c1\",\"chunk-7de4568f\":\"992e49e4\"}[chunkId] + \".js\"\n \t}\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n \t// This file contains only the entry chunk.\n \t// The chunk loading function for additional chunks\n \t__webpack_require__.e = function requireEnsure(chunkId) {\n \t\tvar promises = [];\n\n\n \t\t// mini-css-extract-plugin CSS loading\n \t\tvar cssChunks = {\"chunk-449349ae\":1,\"chunk-7de4568f\":1};\n \t\tif(installedCssChunks[chunkId]) promises.push(installedCssChunks[chunkId]);\n \t\telse if(installedCssChunks[chunkId] !== 0 && cssChunks[chunkId]) {\n \t\t\tpromises.push(installedCssChunks[chunkId] = new Promise(function(resolve, reject) {\n \t\t\t\tvar href = \"css/\" + ({}[chunkId]||chunkId) + \".\" + {\"chunk-449349ae\":\"d5ad41b1\",\"chunk-7de4568f\":\"71bfa63a\"}[chunkId] + \".css\";\n \t\t\t\tvar fullhref = __webpack_require__.p + href;\n \t\t\t\tvar existingLinkTags = document.getElementsByTagName(\"link\");\n \t\t\t\tfor(var i = 0; i < existingLinkTags.length; i++) {\n \t\t\t\t\tvar tag = existingLinkTags[i];\n \t\t\t\t\tvar dataHref = tag.getAttribute(\"data-href\") || tag.getAttribute(\"href\");\n \t\t\t\t\tif(tag.rel === \"stylesheet\" && (dataHref === href || dataHref === fullhref)) return resolve();\n \t\t\t\t}\n \t\t\t\tvar existingStyleTags = document.getElementsByTagName(\"style\");\n \t\t\t\tfor(var i = 0; i < existingStyleTags.length; i++) {\n \t\t\t\t\tvar tag = existingStyleTags[i];\n \t\t\t\t\tvar dataHref = tag.getAttribute(\"data-href\");\n \t\t\t\t\tif(dataHref === href || dataHref === fullhref) return resolve();\n \t\t\t\t}\n \t\t\t\tvar linkTag = document.createElement(\"link\");\n \t\t\t\tlinkTag.rel = \"stylesheet\";\n \t\t\t\tlinkTag.type = \"text/css\";\n \t\t\t\tlinkTag.onload = resolve;\n \t\t\t\tlinkTag.onerror = function(event) {\n \t\t\t\t\tvar request = event && event.target && event.target.src || fullhref;\n \t\t\t\t\tvar err = new Error(\"Loading CSS chunk \" + chunkId + \" failed.\\n(\" + request + \")\");\n \t\t\t\t\terr.code = \"CSS_CHUNK_LOAD_FAILED\";\n \t\t\t\t\terr.request = request;\n \t\t\t\t\tdelete installedCssChunks[chunkId]\n \t\t\t\t\tlinkTag.parentNode.removeChild(linkTag)\n \t\t\t\t\treject(err);\n \t\t\t\t};\n \t\t\t\tlinkTag.href = fullhref;\n\n \t\t\t\tvar head = document.getElementsByTagName(\"head\")[0];\n \t\t\t\thead.appendChild(linkTag);\n \t\t\t}).then(function() {\n \t\t\t\tinstalledCssChunks[chunkId] = 0;\n \t\t\t}));\n \t\t}\n\n \t\t// JSONP chunk loading for javascript\n\n \t\tvar installedChunkData = installedChunks[chunkId];\n \t\tif(installedChunkData !== 0) { // 0 means \"already installed\".\n\n \t\t\t// a Promise means \"currently loading\".\n \t\t\tif(installedChunkData) {\n \t\t\t\tpromises.push(installedChunkData[2]);\n \t\t\t} else {\n \t\t\t\t// setup Promise in chunk cache\n \t\t\t\tvar promise = new Promise(function(resolve, reject) {\n \t\t\t\t\tinstalledChunkData = installedChunks[chunkId] = [resolve, reject];\n \t\t\t\t});\n \t\t\t\tpromises.push(installedChunkData[2] = promise);\n\n \t\t\t\t// start chunk loading\n \t\t\t\tvar script = document.createElement('script');\n \t\t\t\tvar onScriptComplete;\n\n \t\t\t\tscript.charset = 'utf-8';\n \t\t\t\tscript.timeout = 120;\n \t\t\t\tif (__webpack_require__.nc) {\n \t\t\t\t\tscript.setAttribute(\"nonce\", __webpack_require__.nc);\n \t\t\t\t}\n \t\t\t\tscript.src = jsonpScriptSrc(chunkId);\n\n \t\t\t\t// create error before stack unwound to get useful stacktrace later\n \t\t\t\tvar error = new Error();\n \t\t\t\tonScriptComplete = function (event) {\n \t\t\t\t\t// avoid mem leaks in IE.\n \t\t\t\t\tscript.onerror = script.onload = null;\n \t\t\t\t\tclearTimeout(timeout);\n \t\t\t\t\tvar chunk = installedChunks[chunkId];\n \t\t\t\t\tif(chunk !== 0) {\n \t\t\t\t\t\tif(chunk) {\n \t\t\t\t\t\t\tvar errorType = event && (event.type === 'load' ? 'missing' : event.type);\n \t\t\t\t\t\t\tvar realSrc = event && event.target && event.target.src;\n \t\t\t\t\t\t\terror.message = 'Loading chunk ' + chunkId + ' failed.\\n(' + errorType + ': ' + realSrc + ')';\n \t\t\t\t\t\t\terror.name = 'ChunkLoadError';\n \t\t\t\t\t\t\terror.type = errorType;\n \t\t\t\t\t\t\terror.request = realSrc;\n \t\t\t\t\t\t\tchunk[1](error);\n \t\t\t\t\t\t}\n \t\t\t\t\t\tinstalledChunks[chunkId] = undefined;\n \t\t\t\t\t}\n \t\t\t\t};\n \t\t\t\tvar timeout = setTimeout(function(){\n \t\t\t\t\tonScriptComplete({ type: 'timeout', target: script });\n \t\t\t\t}, 120000);\n \t\t\t\tscript.onerror = script.onload = onScriptComplete;\n \t\t\t\tdocument.head.appendChild(script);\n \t\t\t}\n \t\t}\n \t\treturn Promise.all(promises);\n \t};\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"/kefu/\";\n\n \t// on error function for async loading\n \t__webpack_require__.oe = function(err) { console.error(err); throw err; };\n\n \tvar jsonpArray = window[\"webpackJsonp\"] = window[\"webpackJsonp\"] || [];\n \tvar oldJsonpFunction = jsonpArray.push.bind(jsonpArray);\n \tjsonpArray.push = webpackJsonpCallback;\n \tjsonpArray = jsonpArray.slice();\n \tfor(var i = 0; i < jsonpArray.length; i++) webpackJsonpCallback(jsonpArray[i]);\n \tvar parentJsonpFunction = oldJsonpFunction;\n\n\n \t// add entry module to deferred list\n \tdeferredModules.push([0,\"chunk-vendors\"]);\n \t// run deferred modules when ready\n \treturn checkDeferredModules();\n", "// extracted by mini-css-extract-plugin\nmodule.exports = {\"primary\":\"#4073FA\"};", "/**\n * 用户\n */\n\n\nimport {apiLogin, apiKefuDetail} from '@/api/app'\n\n\n// State\nconst state = {\n    token: '',\n    userInfo: {},\n}\n\n\n// Mutations\nconst mutations= {\n    // 设置用户信息\n    SET_USER_INFO: (state, data) => {\n        state.userInfo = data;\n    },\n\n    SET_TOKEN: (state, token) => {\n        state.token = token\n    },\n\n    // 清除用户信息\n    clearUserInfo: (state) => {\n        state.userInfo = {};\n    }\n}\n\n\n// Actions\nconst actions = {\n    login({commit}, form) {\n        return new Promise((resolve, reject) => {\n            apiLogin(form).then(data => {\n                commit('SET_TOKEN', data.token)\n                resolve(data)\n            }).catch(err => {\n                reject(err)\n            })\n        })\n    },\n\n    getUserInfo({commit}) {\n        return new Promise((resolve, reject) => {\n            apiKefuDetail().then(data => {\n                commit('SET_USER_INFO', data)\n                resolve(data)\n            }).catch(err => {\n                // reject(err)\n            })\n        })\n    },\n\n    logout({commit}) {\n        return new Promise((resolve, reject) => {\n            commit('SET_TOKEN', '')\n            commit('clearUserInfo')\n            resolve()\n        })\n    }\n\n}\n\n\nconst user = {\n    state,\n    mutations,\n    actions\n}\n\nexport default user\n", "import { apiLogin, apiKefuDetail } from '@/api/app'\n\n// State\nconst state = {\n    config: {},\n}\n\n// Mutations\nconst mutations = {\n    setConfig(state, data) {\n        state.config = data\n    }\n}\n\n// Actions\nconst actions = {\n    \n}\n\nconst user = {\n    state,\n    mutations,\n    actions,\n}\n\nexport default user\n", "\nimport user from \"./user\";\nimport app from \"./app\";\nexport default {\n    user,\n    app\n};\n\n", "export default {\n    // 获取Token\n    token: (state) => state.user.token || null,\n    // 店铺id\n    shopId:(state) => state.user.userInfo?.shop_id || 0,\n    // 用户信息\n    userInfo: state => state.user.userInfo,\n    baseUrl:  state => state.app.config.base_domain,\n    copyright: state => state.app.config.company_name,\n    wsUrl: state =>  state.app.config.ws_domain\n}\n", "import Vue from 'vue'\nimport Vuex from 'vuex'\nimport VuexPersistence from 'vuex-persist'\nimport modules from './modules'\nimport getters from './getters'\nVue.use(Vuex)\n\nconst vuexLocal = new VuexPersistence({\n    key: 'vuexbase', // 这里可以自定义存入localStorage的键名，默认vuex\n    storage: window.localStorage,\n    modules: ['user'],\n})\n\nexport default new Vuex.Store({\n    modules: modules,\n\tgetters: getters,\n    plugins: [vuexLocal.plugin],\n})\n", "export * from \"-!../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../node_modules/cache-loader/dist/cjs.js??ref--1-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./App.vue?vue&type=style&index=0&id=49b2e1b2&prod&lang=scss&\"", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{attrs:{\"id\":\"app\"}},[(_vm.routerAlive)?_c('router-view'):_vm._e()],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n    <div id=\"app\">\n        <router-view v-if=\"routerAlive\" />\n    </div>\n</template>\n\n<script>\n\nimport { apiGetConfig } from '@/api/app'\nexport default {\n    data() {\n        return {\n            routerAlive: true\n        }\n    },\n    provide() {\n        return {\n            reload: this.reload\n        }\n    },\n    methods: {\n        reload() {\n            this.routerAlive = false\n            this.$nextTick(() => {\n                this.routerAlive = true\n            })\n        },\n    },\n    created(){\n        apiGetConfig().then((data) => {\n            this.$store.commit('setConfig', data)\n            let favicon = document.querySelector('link[rel=\"icon\"]')\n            if (favicon) {\n                favicon.href = data.web_favicon\n                return\n            }\n            favicon = document.createElement('link')\n            favicon.rel = 'icon'\n            favicon.href = data.web_favicon\n            document.head.appendChild(favicon)\n        })\n    }\n}\n</script>\n\n<style lang=\"scss\">\n@import './styles/index.scss';\n#app {\n    min-width: 1200px;\n}\n</style>\n", "import mod from \"-!../node_modules/cache-loader/dist/cjs.js??ref--13-0!../node_modules/thread-loader/dist/cjs.js!../node_modules/babel-loader/lib/index.js!../node_modules/cache-loader/dist/cjs.js??ref--1-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./App.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../node_modules/cache-loader/dist/cjs.js??ref--13-0!../node_modules/thread-loader/dist/cjs.js!../node_modules/babel-loader/lib/index.js!../node_modules/cache-loader/dist/cjs.js??ref--1-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./App.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./App.vue?vue&type=template&id=49b2e1b2&\"\nimport script from \"./App.vue?vue&type=script&lang=js&\"\nexport * from \"./App.vue?vue&type=script&lang=js&\"\nimport style0 from \"./App.vue?vue&type=style&index=0&id=49b2e1b2&prod&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "import Vue from 'vue'\nimport Element from 'element-ui'\nimport '../styles/element-variables.scss'\nVue.use(Element)", "import router from './router'\nimport store from './store'\nimport NProgress from 'nprogress'\n\n\nconst loginPath = '/login'\nconst defaultPath = '/'\nconst whiteList = ['login']\nNProgress.configure({ showSpinner: false }) \n\nrouter.beforeEach((to, from, next) => {\n    NProgress.start()\n    // 设置文档title\n    to.meta?.title && (document.title = to.meta.title)\n    let { token } = to.query\n    if(token) {\n        store.commit('SET_TOKEN', token)\n    }\n    token = token || store.getters.token;\n    if (token) {\n        if (to.path === loginPath) {\n            next({ path: defaultPath })\n        } else {\n            next();\n        }\n    } else {\n        if (whiteList.includes(to.name)) {\n            // 在免登录白名单，直接进入\n            next()\n        } else {\n            next({ path: loginPath, query: { redirect: to.fullPath } })\n        }\n    }\n\n})\n\n\nrouter.afterEach(() => {\n    NProgress.done()\n})\n", "import Vue from 'vue'\nimport './plugins/axios'\nimport App from './App.vue'\nimport router from './router'\nimport store from './store'\nimport './plugins/axios'\nimport './plugins/element'\nimport './permission'\nimport './styles/emoji.css'\n\n// global registration OverlayScrollbars\nimport 'overlayscrollbars/css/OverlayScrollbars.css'\nimport { OverlayScrollbarsComponent } from 'overlayscrollbars-vue'\nVue.component('overlay-scrollbars', OverlayScrollbarsComponent)\n\nVue.config.productionTip = false\n\nnew Vue({\n  router,\n  store,\n  render: h => h(App)\n}).$mount('#app')\n", "import Vue from 'vue'\nimport VueRouter from 'vue-router'\n\nVue.use(VueRouter)\n\nconst routes = [\n    {\n        path: '*',\n        redirect: '/error/404',\n    },\n    {\n        path: '/',\n        name: 'index',\n        meta: {\n            title: '客服',\n        },\n        component: () => import('@/views/window'),\n    },\n    {\n        path: '/login',\n        name: 'login',\n        meta: {\n            title: '登录',\n        },\n        component: () => import('@/views/account/login'),\n    },\n]\n\nconst router = new VueRouter({\n    mode: 'history',\n    base: process.env.BASE_URL,\n    routes,\n})\n\nexport default router\n", "import request from '@/plugins/axios'\n\n// 登录\nexport const apiLogin = data => request.post('/kefuapi/account/login', {...data, client: 5})\n\n// 退出登录\nexport const apiLogout = () => request.post('/kefuapi/account/logout')\n\n\n\n// 用户列表\nexport const apiChatUserList = params => request.get('/kefuapi/chat/user', {params})\n\n// 聊天记录\nexport const apiChatHistory = params => request.get('/kefuapi/chat/record', {params})\n\n// 客服列表\nexport const apiServiceList = params => request.get('/kefuapi/chat/online', {params})\n\n\n// 快捷回复\nexport const apiReplyList = params => request.get('/kefuapi/chat/reply', {params})\n\n\n// 获取用户详情\nexport const apiUserInfo = params => request.get('/kefuapi/chat/userInfo', {params})\n\n// 获取订单列表\nexport const apiUserOrder = params => request.get('/kefuapi/chat/order', {params})\n\n// 文件上传\nexport const apiUploadFile = data => request.post('/kefuapi/file/formImage', data)\n\n// 获取客服详情\nexport const apiKefuDetail = () => request.get('/kefuapi/chat/kefuInfo')\n\n// 获取配置信息\nexport const apiGetConfig = () => request.get('/kefuapi/chat/config')\n", "'use strict'\n\nimport Vue from 'vue'\nimport store from '@/store'\nimport axios from 'axios'\nimport router from '@/router'\nimport configs from '@/config'\nimport { Message } from 'element-ui'\nimport { throttle } from '@/utils/util'\n\n// 事件集\nconst eventResponse = {\n\t// 成功\n\tsuccess({ show, msg, data }) {\n\t\tif (show * 1) Message({ type: 'success', message: msg });\n\t\treturn data;\n\t},\n\t// 失败\n\terror({ show, msg }) {\n\t\tif (show * 1) Message({ type: 'error', message: msg });\n\t\tconsole.log(\"Error...\");\n\t\treturn Promise.reject();\n\t},\n\t// 重定向\n\tredirect: throttle(() => {\n        store.dispatch('logout')\n        router.replace('/login')\n\t\treturn Promise.reject();\n\t}),\n}\n\n\n\nlet config = {\n    baseURL: `${configs.baseURL}`,\n    headers: {\n        'content-type': 'application/json',\n    },\n}\n\nconst _axios = axios.create(config)\n\n_axios.interceptors.request.use(\n    function (config) {\n        // Do something before request is sent\n\t\tconst token = store.getters.token;\n\t\t// header参入Token\n\t\tif (token) config.headers['token'] = token;\n        return config\n    },\n    function (error) {\n        // Do something with request error\n\t\t\n        return Promise.reject(error)\n    }\n)\n\n// Add a response interceptor\n_axios.interceptors.response.use(\n    function (response) {\n        // Do something with response data\n\t\tconst { code } = response.data;\n\t\tswitch(code) {\n\t\t\tcase 1: return eventResponse.success(response.data)\n\t\t\tcase 0: return eventResponse.error(response.data)\n\t\t\tcase -1: return eventResponse.redirect(response.data)\n\t\t}\n    },\n    function (error) {\n        // Do something with response error\n\t\tMessage({ type: 'error', message: '系统错误，请稍候再试' });\n        return Promise.reject(error)\n    }\n)\n\nPlugin.install = function (Vue, options) {\n    Vue.axios = _axios\n    window.axios = _axios\n    Object.defineProperties(Vue.prototype, {\n        axios: {\n            get() {\n                return _axios\n            },\n        },\n        $axios: {\n            get() {\n                return _axios\n            },\n        },\n    })\n}\n\nVue.use(Plugin)\n\nexport default _axios\n", "// extracted by mini-css-extract-plugin\nmodule.exports = {\"primary\":\"#4073FA\"};", "\n/**\n * @description 节流\n * @param fn { Function } 方法\n * @param time { Number } 间隔时间\n * @return { Function } 节流处理的后的方法\n */\nexport const throttle = (\n    fn,\n    time = 1000\n) => {\n    let previousTime = new Date(0).getTime();\n    return (...args ) => {\n        let nowTime = new Date().getTime();\n        if (nowTime - previousTime > time) {\n            previousTime = nowTime;\n            return fn(args);\n        }\n    }\n}\n\n\n/**\n * @description 防抖\n * @param fn { Function } 方法\n * @param time { Number } 间隔时间\n * @return { Function } 防抖处理的后的方法\n */\nexport function debounce(delay, fun) {\n\tlet time = null\n\treturn function () {\n\t\tlet self = this\n\t\tif (time) clearTimeout(time)\n\t\ttime = setTimeout(() => {\n\t\t\treturn fun.apply(self, arguments)\n\t\t}, delay)\n\t}\n}\n\n\n\n/**\n * @description 数组扁平化\n * @param arr { Array } 扁平化对象\n * @return { Array } 扁平化后的数组\n */\nexport const flatten = (arr) => {\n    return arr.reduce((result, item) => {\n        return result.concat(Array.isArray(item) ? flatten(item) : item);\n    }, []);\n}\n\n\n\n\n/**\n * @description 获取不重复的id\n * @param length { Number } id的长度\n * @return { String } id\n */\nexport const getNonDuplicateID = (length = 8) => {\n    let idStr = Date.now().toString(36)\n    idStr += Math.random().toString(36).substr(3, length)\n    return idStr\n}\n\n\n/**\n * @description 复制到剪切板\n * @param value { String } 复制内容\n * @return { Promise } resolve | reject\n */\nexport const copyClipboard = (value) => {\n    const elInput = document.createElement('input')\n\n    elInput.setAttribute('value', value)\n    document.body.appendChild(elInput)\n    elInput.select()\n\n    try {\n        if (document.execCommand('copy'))\n            return Promise.resolve()\n        else\n            throw new Error()\n    } catch (err) {\n        return Promise.reject(err)\n    } finally {\n        document.body.removeChild(elInput)\n    }\n}\n\n\nexport const objectToQuery = (data) => {\n    let _result = []\n\n    for (let key in data) {\n        let value = data[key]\n        if (Array.isArray(value)) {\n            value.forEach(_value => {\n                _result.push(key + \"=\" + _value)\n            })\n        } else {\n            _result.push(key + '=' + value)\n        }\n    }\n\n    return _result.join('&')\n}\n", "// 开发环境域名\n\nconst host_development = 'https://likeshopb2b2c.yixiangonline.com'\n\nexport default {\n    // 版本\n    version: '2.2.4.20230616',\n    baseURL: process.env.NODE_ENV == 'production' ? '' : host_development,\n    // ChatWss: 'wss://likeshopb2b2cchat.yixiangonline.com'\n}\n\n\n"], "sourceRoot": ""}