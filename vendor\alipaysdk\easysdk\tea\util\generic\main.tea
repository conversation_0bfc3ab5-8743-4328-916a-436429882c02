import EasySDKKernel;

type @kernel = EasySDKKernel

init(kernel: EasySDKKernel) {
  @kernel = kernel;
}

model AlipayOpenApiGenericResponse {
  httpBody: string(name='http_body', description='响应原始字符串'),
  code: string(name='code'),
  msg: string(name='msg'),
  subCode: string(name='sub_code'),
  subMsg: string(name='sub_msg')
}

model AlipayOpenApiGenericSDKResponse {
  body: string(name='body', description='订单信息，字符串形式')
}

api execute(method: string, textParams: map[string]string, bizParams: map[string]any): AlipayOpenApiGenericResponse {
  var systemParams: map[string]string = {
    method = method,
    app_id = @kernel.getConfig("appId"),
    timestamp = @kernel.getTimestamp(),
    format = 'json',
    version = '1.0',
    alipay_sdk = @kernel.getSdkVersion(),
    charset = 'UTF-8',
    sign_type = @kernel.getConfig("signType"),
    app_cert_sn = @kernel.getMerchantCertSN(),
    alipay_root_cert_sn = @kernel.getAlipayRootCertSN()
  };

  __request.protocol = @kernel.getConfig("protocol");
  __request.method = 'POST';
  __request.pathname = '/gateway.do';

  __request.headers = {
    host = @kernel.getConfig("gatewayHost"),
    content-type = 'application/x-www-form-urlencoded;charset=utf-8'
  };

  __request.query = @kernel.sortMap({
    sign = @kernel.sign(systemParams, bizParams, textParams, @kernel.getConfig("merchantPrivateKey")),
    ... systemParams,
    ... textParams
  });

  __request.body = @kernel.toUrlEncodedRequestBody(bizParams);
} returns {
  var respMap: map[string]any = @kernel.readAsJson(__response, method);
  if (@kernel.isCertMode()) {
    if (@kernel.verify(respMap, @kernel.extractAlipayPublicKey(@kernel.getAlipayCertSN(respMap)))) {
      return @kernel.toRespModel(respMap);
    }
  } else {
    if (@kernel.verify(respMap, @kernel.getConfig("alipayPublicKey"))) {
      return @kernel.toRespModel(respMap);
    }
  }

  throw {
    message = '验签失败，请检查支付宝公钥设置是否正确。'
  }

} runtime {
  ignoreSSL = @kernel.getConfig("ignoreSSL"),
  httpProxy = @kernel.getConfig("httpProxy"),
  connectTimeout = 15000,
  readTimeout = 15000,
  retry = {
    maxAttempts = 0
  }
}


api fileExecute(method: string, textParams: map[string]string, bizParams: map[string]any, fileParams: map[string]string): AlipayOpenApiGenericResponse {
  var systemParams: map[string]string = {
    method = method,
    app_id = @kernel.getConfig("appId"),
    timestamp = @kernel.getTimestamp(),
    format = 'json',
    version = '1.0',
    alipay_sdk = @kernel.getSdkVersion(),
    charset = 'UTF-8',
    sign_type = @kernel.getConfig("signType"),
    app_cert_sn = @kernel.getMerchantCertSN(),
    alipay_root_cert_sn = @kernel.getAlipayRootCertSN()
  };

    var boundary = @kernel.getRandomBoundary();

  __request.protocol = @kernel.getConfig("protocol");
  __request.method = 'POST';
  __request.pathname = '/gateway.do';

  __request.headers = {
    host = @kernel.getConfig("gatewayHost"),
    content-type = @kernel.concatStr('multipart/form-data;charset=utf-8;boundary=', boundary)
  };

  __request.query = @kernel.sortMap({
    sign = @kernel.sign(systemParams, bizParams, textParams, @kernel.getConfig("merchantPrivateKey")),
    ... systemParams,
    ... textParams
  });

  __request.body = @kernel.toMultipartRequestBody(textParams, fileParams, boundary);
} returns {
  var respMap: map[string]any = @kernel.readAsJson(__response, method);
  if (@kernel.isCertMode()) {
    if (@kernel.verify(respMap, @kernel.extractAlipayPublicKey(@kernel.getAlipayCertSN(respMap)))) {
      return @kernel.toRespModel(respMap);
    }
  } else {
    if (@kernel.verify(respMap, @kernel.getConfig("alipayPublicKey"))) {
      return @kernel.toRespModel(respMap);
    }
  }

  throw {
    message = '验签失败，请检查支付宝公钥设置是否正确。'
  }

} runtime {
  ignoreSSL = @kernel.getConfig("ignoreSSL"),
  httpProxy = @kernel.getConfig("httpProxy"),
  connectTimeout = 100000,
  readTimeout = 100000,
  retry = {
    maxAttempts = 0
  }
}

api sdkExecute(method: string, textParams: map[string]string, bizParams: map[string]any): AlipayOpenApiGenericSDKResponse {
  var systemParams: map[string]string = {
    method = method,
    app_id = @kernel.getConfig("appId"),
    timestamp = @kernel.getTimestamp(),
    format = 'json',
    version = '1.0',
    alipay_sdk = @kernel.getSdkVersion(),
    charset = 'UTF-8',
    sign_type = @kernel.getConfig("signType"),
    app_cert_sn = @kernel.getMerchantCertSN(),
    alipay_root_cert_sn = @kernel.getAlipayRootCertSN()
  };

  var sign =  @kernel.sign(systemParams, bizParams, textParams, @kernel.getConfig('merchantPrivateKey'));

  var response: map[string]string = {
    body = @kernel.generateOrderString(systemParams, bizParams, textParams, sign)
  };
  return response;
}

