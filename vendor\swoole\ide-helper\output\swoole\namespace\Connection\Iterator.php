<?php

namespace Swoole\Connection;

class Iterator implements \Iterator, \ArrayAccess, \Countable
{

    public function __construct()
    {
    }

    public function __destruct()
    {
    }

    /**
     * @return mixed
     */
    public function rewind()
    {
    }

    /**
     * @return mixed
     */
    public function next()
    {
    }

    /**
     * @return mixed
     */
    public function current()
    {
    }

    /**
     * @return mixed
     */
    public function key()
    {
    }

    /**
     * @return mixed
     */
    public function valid()
    {
    }

    /**
     * @return mixed
     */
    public function count()
    {
    }

    /**
     * @return mixed
     */
    public function offsetExists($fd)
    {
    }

    /**
     * @return mixed
     */
    public function offsetGet($fd)
    {
    }

    /**
     * @return mixed
     */
    public function offsetSet($fd, $value)
    {
    }

    /**
     * @return mixed
     */
    public function offsetUnset($fd)
    {
    }


}
