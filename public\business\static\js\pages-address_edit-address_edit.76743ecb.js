(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-address_edit-address_edit"],{"0ece":function(e,t,i){"use strict";i.r(t);var a=i("c0e5"),n=i("a896");for(var r in n)["default"].indexOf(r)<0&&function(e){i.d(t,e,(function(){return n[e]}))}(r);i("6c89");var s=i("828b"),d=Object(s["a"])(n["default"],a["b"],a["c"],!1,null,"1ffd16a3",null,!1,a["a"],void 0);t["default"]=d.exports},1851:function(e,t,i){"use strict";var a=i("8bdb"),n=i("84d6"),r=i("1cb5");a({target:"Array",proto:!0},{fill:n}),r("fill")},"333a":function(e,t,i){"use strict";i.r(t);var a=i("7af1"),n=i("ef1d");for(var r in n)["default"].indexOf(r)<0&&function(e){i.d(t,e,(function(){return n[e]}))}(r);i("5934");var s=i("828b"),d=Object(s["a"])(n["default"],a["b"],a["c"],!1,null,"1c8c3e6a",null,!1,a["a"],void 0);t["default"]=d.exports},"455c":function(e,t,i){"use strict";i("6a54");var a=i("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=a(i("2634")),r=a(i("9b1b")),s=a(i("2fdc")),d=i("92f2"),o=i("9f89"),l=a(i("4147")),u={data:function(){return{id:0,type:1,refund_address:{nickname:"",mobile:"",province_id:"",city_id:"",district_id:"",address:""},region:"请选择",defaultRegion:["广东省","广州市","番禺区"],showRegion:!1,lists:[]}},onLoad:function(e){this.lists=l.default;try{var t=this.$Route.query.type,i=this.$Route.query.id;if(t){this.type=t;var a=this.shopInfo;if(1==t){for(var n in this.region=a.province_name+" "+a.province_name+" "+a.district_name,a)this.$set(this.refund_address,n,a[n]);uni.setNavigationBarTitle({title:"编辑商家地址"})}else if(2==t){for(var r in this.region=a.refund_address.province_name+" "+a.refund_address.province_name+" "+a.refund_address.district_name,a.refund_address)this.$set(this.refund_address,r,a.refund_address[r]);uni.setNavigationBarTitle({title:"编辑退货地址"})}}i&&(this.type=3,this.id=i,this.getAddressFunc(i))}catch(s){console.log(s)}},methods:{onSubmit:function(){var e=this;return(0,s.default)((0,n.default)().mark((function t(){var i;return(0,n.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(console.log(3==e.type),i={nickname:2==e.type?e.refund_address.nickname:"",id:3==e.type?e.id:"",mobile:1!=e.type?e.refund_address.mobile:"",consignee:3==e.type?e.refund_address.nickname:"",province:3==e.type?e.refund_address.province_id:"",city:3==e.type?e.refund_address.city_id:"",district:3==e.type?e.refund_address.district_id:"",province_id:3!=e.type?e.refund_address.province_id:"",city_id:3!=e.type?e.refund_address.city_id:"",district_id:3!=e.type?e.refund_address.district_id:"",address:e.refund_address.address},2!=e.type&&3!=e.type){t.next=9;break}if(e.refund_address.nickname){t.next=5;break}return t.abrupt("return",e.$toast({title:"请填写收货人姓名"}));case 5:if(!(!e.refund_address.nickname.length>=20)){t.next=7;break}return t.abrupt("return",e.$toast({title:"输入的收货人长度不得大于20位"}));case 7:if(e.refund_address.mobile){t.next=9;break}return t.abrupt("return",e.$toast({title:"请填写手机号码"}));case 9:if(e.region){t.next=11;break}return t.abrupt("return",e.$toast({title:"请选择地区"}));case 11:if(e.refund_address.address){t.next=13;break}return t.abrupt("return",e.$toast({title:"请填写小区、街道、门牌号等信息"}));case 13:if(2!=e.type){t.next=18;break}return t.next=16,(0,d.apiSetShopInfo)({refund_address:i});case 16:t.next=26;break;case 18:if(1!=e.type){t.next=23;break}return t.next=21,(0,d.apiSetShopInfo)((0,r.default)({},i));case 21:t.next=26;break;case 23:if(3!=e.type){t.next=26;break}return t.next=26,(0,o.apiOrderEditAddress)((0,r.default)({},i));case 26:e.$refs.uToast.show({title:"设置成功",type:"success"}),setTimeout((function(){e.$Router.back()}),1e3);case 28:case"end":return t.stop()}}),t)})))()},getAddressFunc:function(e){var t=this;return(0,s.default)((0,n.default)().mark((function i(){var a;return(0,n.default)().wrap((function(i){while(1)switch(i.prev=i.next){case 0:return i.next=2,(0,o.apiOrderGetAddress)({id:e});case 2:a=i.sent,t.refund_address.nickname=a.consignee,t.refund_address.mobile=a.mobile,t.refund_address.province_id=a.province,t.refund_address.city_id=a.city,t.refund_address.district_id=a.district,t.refund_address.address=a.address,t.region=a.region[0]+" "+a.region[1]+" "+a.region[2];case 10:case"end":return i.stop()}}),i)})))()},regionChange:function(e){console.log(e),this.refund_address.province_id=e[0].value,this.refund_address.city_id=e[1].value,this.refund_address.district_id=e[2].value,this.region=e[0].label+" "+e[1].label+" "+e[2].label}}};t.default=u},5934:function(e,t,i){"use strict";var a=i("a56d"),n=i.n(a);n.a},"61bd":function(e,t,i){var a=i("c86c");t=a(!1),t.push([e.i,"@charset \"UTF-8\";\n/* 颜色变量 */\n/** S Font's size **/\n/** E Font's size **/[data-v-1ffd16a3]:export{red_theme:#ff2c3c;orange_theme:#f7971e;pink_theme:#fa444d;gold_theme:#e0a356;blue_theme:#2f80ed;green_theme:#2ec840}.u-select__action[data-v-1ffd16a3]{position:relative;line-height:%?70?%;height:%?70?%}.u-select__action__icon[data-v-1ffd16a3]{position:absolute;right:%?20?%;top:50%;transition:-webkit-transform .4s;transition:transform .4s;transition:transform .4s,-webkit-transform .4s;-webkit-transform:translateY(-50%);transform:translateY(-50%);z-index:1}.u-select__action__icon--reverse[data-v-1ffd16a3]{-webkit-transform:rotate(-180deg) translateY(50%);transform:rotate(-180deg) translateY(50%)}.u-select__hader__title[data-v-1ffd16a3]{color:#606266}.u-select--border[data-v-1ffd16a3]{border-radius:%?6?%;border-radius:4px;border:1px solid #dcdfe6}.u-select__header[data-v-1ffd16a3]{display:flex;flex-direction:row;align-items:center;justify-content:space-between;height:%?80?%;padding:0 %?40?%}.u-select__body[data-v-1ffd16a3]{width:100%;height:%?500?%;overflow:hidden;background-color:#fff}.u-select__body__picker-view[data-v-1ffd16a3]{height:100%;box-sizing:border-box}.u-select__body__picker-view__item[data-v-1ffd16a3]{display:flex;flex-direction:row;align-items:center;justify-content:center;font-size:%?32?%;color:#303133;padding:0 %?8?%}",""]),e.exports=t},"6c89":function(e,t,i){"use strict";var a=i("9ee0"),n=i.n(a);n.a},"7af1":function(e,t,i){"use strict";i.d(t,"b",(function(){return n})),i.d(t,"c",(function(){return r})),i.d(t,"a",(function(){return a}));var a={uIcon:i("15cd").default,uSelect:i("0ece").default,uToast:i("341e").default},n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("v-uni-view",[i("v-uni-view",{staticClass:"address-edit"},[2==e.type||3==e.type?[i("v-uni-view",{staticClass:"form-item bb flex"},[i("v-uni-view",{staticClass:"label"},[e._v("收货人")]),i("v-uni-input",{staticClass:"m-l-10",attrs:{name:"nickname",type:"text",placeholder:"请填写收货人姓名"},model:{value:e.refund_address.nickname,callback:function(t){e.$set(e.refund_address,"nickname",t)},expression:"refund_address.nickname"}})],1),i("v-uni-view",{staticClass:"form-item bb flex"},[i("v-uni-view",{staticClass:"label"},[e._v("联系方式")]),i("v-uni-input",{staticClass:"m-l-10",attrs:{name:"mobile",maxlength:"11",type:"number",placeholder:"请填写手机号码"},model:{value:e.refund_address.mobile,callback:function(t){e.$set(e.refund_address,"mobile",t)},expression:"refund_address.mobile"}})],1)]:e._e(),i("v-uni-view",{on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.showRegion=!0}}},[i("v-uni-view",{staticClass:"form-item bb flex"},[i("v-uni-view",{staticClass:"label"},[e._v("省/市/区")]),i("v-uni-view",{staticClass:"flex flex-1 row-between m-l-10",staticStyle:{width:"100%"}},[i("v-uni-input",{staticClass:"m-r-10",attrs:{name:"region",disabled:!0,type:"text",value:"请选择"},model:{value:e.region,callback:function(t){e.region=t},expression:"region"}}),i("u-icon",{attrs:{color:"#707070",name:"arrow-down"}})],1)],1)],1),i("v-uni-view",{staticClass:"form-item flex col-top bb"},[i("v-uni-view",{staticClass:"label"},[e._v("详细地址")]),i("v-uni-input",{attrs:{name:"address",placeholder:"请填写小区、街道、门牌号等信息"},model:{value:e.refund_address.address,callback:function(t){e.$set(e.refund_address,"address",t)},expression:"refund_address.address"}})],1)],2),i("v-uni-button",{staticClass:"my-btn md white flex br60 row-center",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.onSubmit.apply(void 0,arguments)}}},[e._v("完成")]),i("u-select",{attrs:{"confirm-color":"#101010","cancel-color":"#999",mode:"mutil-column-auto",list:e.lists},on:{confirm:function(t){arguments[0]=t=e.$handleEvent(t),e.regionChange.apply(void 0,arguments)}},model:{value:e.showRegion,callback:function(t){e.showRegion=t},expression:"showRegion"}}),i("u-toast",{ref:"uToast"})],1)},r=[]},"92f2":function(e,t,i){"use strict";i("6a54");var a=i("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.getIncompleteInfo=t.apiStatisticsVisit=t.apiStatisticsTrading=t.apiStatisticsGoodslist=t.apiSetShopInfo=t.apiIndex=void 0;var n=a(i("be47"));t.apiIndex=function(){return n.default.get("Statistics/workbench")};t.apiSetShopInfo=function(e){return n.default.post("shop/shopSet",e)};t.apiStatisticsGoodslist=function(){return n.default.get("Statistics/goodslist")};t.apiStatisticsTrading=function(){return n.default.get("Statistics/trading")};t.apiStatisticsVisit=function(){return n.default.get("Statistics/visit")};t.getIncompleteInfo=function(){return n.default.get("shop/getIncompleteInfo")}},"9ee0":function(e,t,i){var a=i("61bd");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[e.i,a,""]]),a.locals&&(e.exports=a.locals);var n=i("967d").default;n("0a57e4bc",a,!0,{sourceMap:!1,shadowMode:!1})},"9f89":function(e,t,i){"use strict";i("6a54");var a=i("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.apiVerificationOrderList=t.apiVerificationOrderDetail=t.apiVerificationOrderConfirm=t.apiOrderLogistics=t.apiOrderList=t.apiOrderGetAddress=t.apiOrderExpress=t.apiOrderEditAddress=t.apiOrderDetail=t.apiOrderDelivery=t.apiOrderDelete=t.apiOrderConfirmpay=t.apiOrderConfirm=t.apiOrderClose=void 0;var n=a(i("be47"));t.apiOrderList=function(e){return n.default.get("order/lists",{params:e})};t.apiOrderDetail=function(e){return n.default.get("order/detail",{params:e})};t.apiOrderClose=function(e){return n.default.post("order/cancel",e)};t.apiOrderConfirm=function(e){return n.default.post("order/confirm",e)};t.apiOrderDelivery=function(e){return n.default.post("order/delivery",e)};t.apiOrderExpress=function(e){return n.default.get("order/getExpress",{params:e})};t.apiOrderLogistics=function(e){return n.default.get("order/logistics",{params:e})};t.apiOrderGetAddress=function(e){return n.default.get("order/getAddress",{params:e})};t.apiOrderDelete=function(e){return n.default.post("order/del",e)};t.apiOrderEditAddress=function(e){return n.default.post("order/editAddress",e)};t.apiVerificationOrderList=function(e){return n.default.get("verification/lists",{params:e})};t.apiVerificationOrderDetail=function(e){return n.default.get("verification/detail",{params:e})};t.apiVerificationOrderConfirm=function(e){return n.default.post("verification/confirm",e)};t.apiOrderConfirmpay=function(e){return n.default.post("order/confirmPay",e)}},a56d:function(e,t,i){var a=i("bbde");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[e.i,a,""]]),a.locals&&(e.exports=a.locals);var n=i("967d").default;n("a30976a8",a,!0,{sourceMap:!1,shadowMode:!1})},a896:function(e,t,i){"use strict";i.r(t);var a=i("ba7d"),n=i.n(a);for(var r in a)["default"].indexOf(r)<0&&function(e){i.d(t,e,(function(){return a[e]}))}(r);t["default"]=n.a},ba7d:function(e,t,i){"use strict";i("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,i("64aa"),i("1851"),i("aa9c"),i("fd3c");var a={props:{list:{type:Array,default:function(){return[]}},border:{type:Boolean,default:!0},value:{type:Boolean,default:!1},cancelColor:{type:String,default:"#606266"},confirmColor:{type:String,default:"#2979ff"},zIndex:{type:[String,Number],default:0},safeAreaInsetBottom:{type:Boolean,default:!1},maskCloseAble:{type:Boolean,default:!0},defaultValue:{type:Array,default:function(){return[0]}},mode:{type:String,default:"single-column"},valueName:{type:String,default:"value"},labelName:{type:String,default:"label"},childName:{type:String,default:"children"},title:{type:String,default:""},cancelText:{type:String,default:"取消"},confirmText:{type:String,default:"确认"}},data:function(){return{defaultSelector:[0],columnData:[],selectValue:[],lastSelectIndex:[],columnNum:0,moving:!1}},watch:{value:{immediate:!0,handler:function(e){var t=this;e&&setTimeout((function(){return t.init()}),10)}}},computed:{uZIndex:function(){return this.zIndex?this.zIndex:this.$u.zIndex.popup}},methods:{pickstart:function(){},pickend:function(){},init:function(){this.setColumnNum(),this.setDefaultSelector(),this.setColumnData(),this.setSelectValue()},setDefaultSelector:function(){this.defaultSelector=this.defaultValue.length==this.columnNum?this.defaultValue:Array(this.columnNum).fill(0),this.lastSelectIndex=this.$u.deepClone(this.defaultSelector)},setColumnNum:function(){if("single-column"==this.mode)this.columnNum=1;else if("mutil-column"==this.mode)this.columnNum=this.list.length;else if("mutil-column-auto"==this.mode){var e=1,t=this.list;while(t[0][this.childName])t=t[0]?t[0][this.childName]:{},e++;this.columnNum=e}},setColumnData:function(){var e=[];if(this.selectValue=[],"mutil-column-auto"==this.mode)for(var t=this.list[this.defaultSelector.length?this.defaultSelector[0]:0],i=0;i<this.columnNum;i++)0==i?(e[i]=this.list,t=t[this.childName]):(e[i]=t,t=t[this.defaultSelector[i]][this.childName]);else"single-column"==this.mode?e[0]=this.list:e=this.list;this.columnData=e},setSelectValue:function(){for(var e=null,t=0;t<this.columnNum;t++){e=this.columnData[t][this.defaultSelector[t]];var i={value:e?e[this.valueName]:null,label:e?e[this.labelName]:null};e&&e.extra&&(i.extra=e.extra),this.selectValue.push(i)}},columnChange:function(e){var t=this,i=null,a=e.detail.value;if(this.selectValue=[],"mutil-column-auto"==this.mode){this.lastSelectIndex.map((function(e,t){e!=a[t]&&(i=t)})),this.defaultSelector=a;for(var n=i+1;n<this.columnNum;n++)this.columnData[n]=this.columnData[n-1][n-1==i?a[i]:0][this.childName],this.defaultSelector[n]=0;a.map((function(e,i){var n=t.columnData[i][a[i]],r={value:n?n[t.valueName]:null,label:n?n[t.labelName]:null};n&&void 0!==n.extra&&(r.extra=n.extra),t.selectValue.push(r)})),this.lastSelectIndex=a}else if("single-column"==this.mode){var r=this.columnData[0][a[0]],s={value:r?r[this.valueName]:null,label:r?r[this.labelName]:null};r&&void 0!==r.extra&&(s.extra=r.extra),this.selectValue.push(s)}else"mutil-column"==this.mode&&a.map((function(e,i){var n=t.columnData[i][a[i]],r={value:n?n[t.valueName]:null,label:n?n[t.labelName]:null};n&&void 0!==n.extra&&(r.extra=n.extra),t.selectValue.push(r)}))},close:function(){this.$emit("input",!1)},getResult:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;e&&this.$emit(e,this.selectValue),this.close()},selectHandler:function(){this.$emit("click")}}};t.default=a},bbde:function(e,t,i){var a=i("c86c");t=a(!1),t.push([e.i,"@charset \"UTF-8\";\n/* 颜色变量 */\n/** S Font's size **/\n/** E Font's size **/[data-v-1c8c3e6a]:export{red_theme:#ff2c3c;orange_theme:#f7971e;pink_theme:#fa444d;gold_theme:#e0a356;blue_theme:#2f80ed;green_theme:#2ec840}.address-edit[data-v-1c8c3e6a]{padding-top:%?20?%}.address-edit .bb[data-v-1c8c3e6a]{border-bottom:1px solid #f8f8f8}.address-edit .form-item[data-v-1c8c3e6a]{padding:%?30?%;background-color:#fff}.address-edit .form-item .label[data-v-1c8c3e6a]{width:%?150?%;color:#101010;font-size:%?28?%;font-weight:500}.address-edit .form-item uni-input[data-v-1c8c3e6a]{text-align:right;height:100%;flex:1}.my-btn[data-v-1c8c3e6a]{height:%?88?%;margin:%?30?% %?26?%;margin-top:%?40?%;text-align:center;background-color:#40affa}",""]),e.exports=t},c0e5:function(e,t,i){"use strict";i.d(t,"b",(function(){return n})),i.d(t,"c",(function(){return r})),i.d(t,"a",(function(){return a}));var a={uPopup:i("c983").default},n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("v-uni-view",{staticClass:"u-select"},[i("u-popup",{attrs:{maskCloseAble:e.maskCloseAble,mode:"bottom",popup:!1,length:"auto",safeAreaInsetBottom:e.safeAreaInsetBottom,"z-index":e.uZIndex},on:{close:function(t){arguments[0]=t=e.$handleEvent(t),e.close.apply(void 0,arguments)}},model:{value:e.value,callback:function(t){e.value=t},expression:"value"}},[i("v-uni-view",{staticClass:"u-select"},[i("v-uni-view",{staticClass:"u-select__header",on:{touchmove:function(t){t.stopPropagation(),t.preventDefault(),arguments[0]=t=e.$handleEvent(t)}}},[i("v-uni-view",{staticClass:"u-select__header__cancel u-select__header__btn",style:{color:e.cancelColor},attrs:{"hover-class":"u-hover-class","hover-stay-time":150},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.getResult("cancel")}}},[e._v(e._s(e.cancelText))]),i("v-uni-view",{staticClass:"u-select__header__title"},[e._v(e._s(e.title))]),i("v-uni-view",{staticClass:"u-select__header__confirm u-select__header__btn",style:{color:e.moving?e.cancelColor:e.confirmColor},attrs:{"hover-class":"u-hover-class","hover-stay-time":150},on:{touchmove:function(t){t.stopPropagation(),arguments[0]=t=e.$handleEvent(t)},click:function(t){t.stopPropagation(),arguments[0]=t=e.$handleEvent(t),e.getResult("confirm")}}},[e._v(e._s(e.confirmText))])],1),i("v-uni-view",{staticClass:"u-select__body"},[i("v-uni-picker-view",{staticClass:"u-select__body__picker-view",attrs:{value:e.defaultSelector},on:{change:function(t){arguments[0]=t=e.$handleEvent(t),e.columnChange.apply(void 0,arguments)},pickstart:function(t){arguments[0]=t=e.$handleEvent(t),e.pickstart.apply(void 0,arguments)},pickend:function(t){arguments[0]=t=e.$handleEvent(t),e.pickend.apply(void 0,arguments)}}},e._l(e.columnData,(function(t,a){return i("v-uni-picker-view-column",{key:a},e._l(t,(function(t,a){return i("v-uni-view",{key:a,staticClass:"u-select__body__picker-view__item"},[i("v-uni-view",{staticClass:"u-line-1"},[e._v(e._s(t[e.labelName]))])],1)})),1)})),1)],1)],1)],1)],1)},r=[]},ef1d:function(e,t,i){"use strict";i.r(t);var a=i("455c"),n=i.n(a);for(var r in a)["default"].indexOf(r)<0&&function(e){i.d(t,e,(function(){return a[e]}))}(r);t["default"]=n.a}}]);