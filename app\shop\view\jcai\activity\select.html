{layout name="layout1" /}

<style>
    .layui-table-cell {
        height: auto;
        line-height: 28px;
        padding: 6px 15px;
        position: relative;
        box-sizing: border-box;
    }
    .layui-table img {
        max-width: 100%;
    }
</style>

<div class="wrapper">
    <div class="layui-card">
        <!-- 搜索区域 -->
        <div class="layui-card-body layui-form">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label for="name" class="layui-form-label">商品名称：</label>
                    <div class="layui-inline" style="margin-right:0;">
                        <div class="layui-input-inline" >
                            <input type="text" id="name" name="name" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                </div>
                <div class="layui-inline">
                    <a class="layui-btn layui-btn-sm layui-btn-normal" lay-submit lay-filter="search">搜索</a>
                    <a class="layui-btn layui-btn-sm layui-btn-primary" lay-submit lay-filter="clear-search">重置</a>
                </div>
            </div>
        </div>

        <!-- 主体区域 -->
        <div class="layui-card-body">
            <table id="like-table-lists" lay-filter="like-table-lists"></table>
            <script type="text/html" id="table-activity">
                {{#  if(d.is_activity=="正常"){ }}正常 {{#  } }}
                {{#  if(d.is_activity!="正常"){ }}<span style="color:red;">{{d.is_activity}}</span> {{#  } }}
            </script>

            <script type="text/html" id="table-image">
                {{#  if(d.image){ }}
                <img src="{{d.image}}" alt="商品图片" class="layui-circle" style="width:50px;height:50px;cursor:pointer;" onclick="showBigImage(this)">
                {{#  } else { }}
                <img src="__STATIC__/common/img/default_goods_image.png" alt="默认图片" class="layui-circle" style="width:50px;height:50px;">
                {{#  } }}
            </script>

            <script type="text/html" id="table-goods">
                <div style="text-align:left;">{{d.name}}</div>
            </script>
        </div>
    </div>
</div>

<script>
    layui.use(["table", "form"], function(){
        var table   = layui.table;
        var form    = layui.form;

        like.tableLists("#like-table-lists", "{:url()}", [
            {type:"radio", fixed:"left", width:50}
            ,{field:"image", title:"商品图片", width:120, align:"center", templet:"#table-image"}
            ,{field:"name", title:"商品名称", width:200,templet:"#table-goods"}
            ,{field:"market_price", width:100, align:"center",title:"最高价"}
            ,{field:"min_price", width:100, align:"center",title:"最低价"}
            ,{field:"market_price", width:100, align:"center",title:"市场价"}
            ,{field:"stock", width:150, align:"center", title:"库存"}
            ,{field:"activity", width:150, align:"center", title:"活动", templet:"#table-activity"}
        ]);

        form.on("submit(search)", function(data){
            table.reload("like-table-lists", {
                where: data.field,
                page: {
                    curr: 1
                }
            });
        });

        form.on("submit(clear-search)", function(){
            $("#name").val("");
            $("#cid").val("");
            table.reload("like-table-lists", {
                where: {},
                page: {
                    curr: 1
                }
            });
        });

    });

    // 图片放大功能
    window.showBigImage = function(obj) {
        var src = obj.src;
        var img = new Image();
        img.src = src;
        img.onload = function() {
            // 计算合适的尺寸，限制最大宽度为400px，最大高度为400px
            var maxWidth = 400;
            var maxHeight = 400;
            var width = img.width;
            var height = img.height;

            // 保持图片比例
            if (width > maxWidth || height > maxHeight) {
                var ratio = Math.min(maxWidth / width, maxHeight / height);
                width = Math.floor(width * ratio);
                height = Math.floor(height * ratio);
            }

            // 创建一个新的图层来显示大图
            layer.open({
                type: 1,
                title: false,
                closeBtn: 1,
                shadeClose: true,
                area: [width + 'px', height + 'px'],
                content: '<div style="text-align:center;"><img src="' + src + '" style="width:100%;height:100%;" /></div>'
            });
        }
    }

    /**
     * 获取选择数据
     * @returns {*}
     */
    var callbackData = function () {
        var data = layui.table.checkStatus('like-table-lists').data;
        if (data[0]['is_activity'] !== '正常') {
            layer.msg("此商品正在参与其他活动,换一个吧", {time:1000});
            return [];
        }
        var index = parent.layer.getFrameIndex(window.name);
        parent.layer.close(index);
        return data;
    }
</script>