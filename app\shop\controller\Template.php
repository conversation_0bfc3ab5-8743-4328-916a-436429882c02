<?php

namespace app\shop\controller;

use app\common\basics\Controller;

/**
 * 模板下载控制器
 * Class Template
 * @package app\shop\controller
 */
class Template extends Controller
{
    /**
     * 下载授权书模板
     * @return \think\response\View|\think\response\Json
     */
    public function authorizationTemplate()
    {
        try {
            $templatePath = public_path() . 'static/template/authorization_template.html';
            
            if (!file_exists($templatePath)) {
                return $this->fail('模板文件不存在');
            }
            
            // 读取模板内容
            $content = file_get_contents($templatePath);
            
            // 设置下载头
            header('Content-Type: text/html; charset=utf-8');
            header('Content-Disposition: attachment; filename="授权书模板.html"');
            header('Content-Length: ' . strlen($content));
            
            echo $content;
            exit;
            
        } catch (\Exception $e) {
            return $this->fail('下载失败：' . $e->getMessage());
        }
    }
    
    /**
     * 预览授权书模板
     * @return \think\response\View
     */
    public function previewAuthorization()
    {
        $templatePath = public_path() . 'static/template/authorization_template.html';
        
        if (!file_exists($templatePath)) {
            return view('error', ['message' => '模板文件不存在']);
        }
        
        // 直接显示模板内容
        $content = file_get_contents($templatePath);
        echo $content;
        exit;
    }
    
    /**
     * 获取模板列表
     * @return \think\response\Json
     */
    public function getTemplateList()
    {
        $templates = [
            [
                'name' => '授权书模板',
                'type' => 'authorization',
                'description' => '商家授权书模板，用于授权他人代理店铺操作',
                'download_url' => url('template/authorizationTemplate'),
                'preview_url' => url('template/previewAuthorization'),
                'file_size' => '约 5KB',
                'format' => 'HTML'
            ]
        ];
        
        return $this->success('获取成功', $templates);
    }
}
