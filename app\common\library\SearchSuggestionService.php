<?php
namespace app\common\library;

use think\facade\Db;
use think\facade\Cache;
use think\facade\Log;

/**
 * 搜索候选词服务类
 * 集成阿里云NLP服务，提供更智能的搜索候选词功能
 */
class SearchSuggestionService
{
    // /**
    //  * 阿里云NLP服务实例
    //  */
    // private $aliNlp;

    /**
     * 构造函数
     */
    public function __construct()
    {
        // $this->aliNlp = new AliNlpService(); // AliNlpService is no longer used
    }

    // /**
    //  * 高亮处理关键词
    //  * @param string $text 要处理的文本
    //  * @param string $keyword 要高亮的关键词
    //  * @return string 处理后的文本
    //  */
    // protected function highlightKeyword($text, $keyword)
    // {
    //     // 限制文本长度，最多15个字符
    //     $shortText = mb_strlen($text, 'UTF-8') > 15 ? mb_substr($text, 0, 15, 'UTF-8') : $text;

    //     // 如果有空格，只取第一部分
    //     if (strpos($shortText, ' ') !== false) {
    //         $parts = explode(' ', $shortText);
    //         $shortText = $parts[0];

    //         // 如果第一部分太短，尝试添加第二部分
    //         if (count($parts) > 1 && mb_strlen($shortText, 'UTF-8') < 5) {
    //             $shortText .= ' ' . $parts[1];
    //         }
    //     }

    //     // 高亮处理关键词
    //     $pattern = '/(' . preg_quote($keyword, '/') . ')/iu';
    //     $replacement = '<b style="color: red;">$1</b>';

    //     // 确保关键词被高亮显示
    //     if (mb_stripos($shortText, $keyword) !== false) {
    //         return preg_replace($pattern, $replacement, $shortText);
    //     } else {
    //         // 如果关键词不在文本中，强制添加并高亮
    //         return '<b style="color: red;">' . $keyword . '</b>' . $shortText;
    //     }
    // }

    /**
     * 获取搜索候选词
     * @param string $keyword 搜索关键词
     * @param int $limit 返回数量限制
     * @return array 候选词列表
     */
    public function getSuggestions($keyword, $limit = 10)
    {
        if (empty($keyword)) {
            return [];
        }

        // 检查缓存
        $cacheKey = 'search_suggestions_v2_meili_' . md5($keyword . '_' . $limit); // Added limit to cache key for more specific caching
        $cachedResult = Cache::get($cacheKey);
        if ($cachedResult) {
            // MeiliSearch results are expected to be already sliced by limit from MeiliSearch->getSearchSuggestions
            return $cachedResult; 
        }

        $uniqueSuggestions = [];
        try {
            $meili = new \app\common\library\MeiliSearch();
            // MeiliSearch->getSearchSuggestions is expected to return an array of ['title' => 'highlighted', 'name' => 'original']
            // and handle sorting and limit internally.
            $uniqueSuggestions = $meili->getSearchSuggestions($keyword, $limit);
        } catch (\Exception $e) {
            Log::error('MeiliSearch getSearchSuggestions failed: ' . $e->getMessage());
            // Fallback or empty result if MeiliSearch fails
            // For now, we return empty as per the task to primarily rely on MeiliSearch
            // Optionally, here could be a fallback to a simpler, less resource-intensive local method if desired in future.
            return [];
        }
        
        // 缓存结果（1小时）
        // $uniqueSuggestions should already be limited by MeiliSearch
        Cache::set($cacheKey, $uniqueSuggestions, 3600);

        return $uniqueSuggestions; // Already limited by MeiliSearch
    }

    // /**
    //  * 获取中心词
    //  * @param string $keyword 搜索关键词
    //  * @return array 中心词列表
    //  */
    // private function getCenterWords($keyword)
    // {
    //     try {
    //         // 使用阿里云中心词提取-中文电商API
    //         $keywordResult = $this->aliNlp->keywordExtractEcom($keyword);

    //         if (isset($keywordResult['keywords']) && !empty($keywordResult['keywords'])) {
    //             return $keywordResult['keywords'];
    //         }

    //         // 如果中心词提取失败，尝试使用命名实体识别
    //         $nerResult = $this->aliNlp->nerEcom($keyword);

    //         if (isset($nerResult['words']) && !empty($nerResult['words'])) {
    //             return $nerResult['words'];
    //         }

    //         // 如果实体识别也失败，尝试使用分词
    //         $segmentResult = $this->aliNlp->segment($keyword);

    //         if (isset($segmentResult['words']) && !empty($segmentResult['words'])) {
    //             // 过滤掉长度小于2的词
    //             return array_filter($segmentResult['words'], function($word) {
    //                 return mb_strlen($word, 'UTF-8') >= 2;
    //             });
    //         }
    //     } catch (\Exception $e) {
    //         Log::error('获取中心词失败: ' . $e->getMessage());
    //     }

    //     return [];
    // }

    // /**
    //  * 基于中心词获取候选词
    //  * @param string $keyword 原始搜索关键词
    //  * @param array $centerWords 中心词列表
    //  * @param int $limit 返回数量限制
    //  * @return array 候选词列表
    //  */
    // private function getCenterWordBasedSuggestions($keyword, $centerWords, $limit)
    // {
    //     $suggestions = [];
    //     $categoryWords = [];

    //     // 首先尝试识别品类词
    //     try {
    //         // 使用分词来提取品类词
    //         $segmentResult = $this->aliNlp->segment($keyword);
    //         if (isset($segmentResult['words']) && !empty($segmentResult['words'])) {
    //             foreach ($segmentResult['words'] as $word) {
    //                 if (mb_strlen($word, 'UTF-8') >= 2) {
    //                     $categoryWords[] = $word;
    //                 }
    //             }
    //         }

    //         // 使用命名实体识别
    //         $nerResult = $this->aliNlp->nerEcom($keyword);
    //         if (isset($nerResult['words']) && !empty($nerResult['words'])) {
    //             foreach ($nerResult['words'] as $word) {
    //                 if (mb_strlen($word, 'UTF-8') >= 2) {
    //                     $categoryWords[] = $word;
    //                 }
    //             }
    //         }
    //     } catch (\Exception $e) {
    //         Log::error('识别品类词失败: ' . $e->getMessage());
    //     }

    //     // 如果找到品类词，优先使用品类词
    //     if (!empty($categoryWords)) {
    //         foreach ($categoryWords as $category) {
    //             // 生成"关键词+品类词"组合
    //             $combinedKeyword = $keyword . $category;

    //             // 使用辅助方法高亮处理关键词
    //             $highlighted = $this->highlightKeyword($combinedKeyword, $keyword);

    //             // 验证组合是否有效（能搜索到商品）
    //             $count = Db::name('goods')
    //                 ->where(function($query) use ($combinedKeyword) {
    //                     $query->whereOr('name', 'like', "%{$combinedKeyword}%")
    //                           ->whereOr('split_word', 'like', "%{$combinedKeyword}%");
    //                 })
    //                 ->where('del', 0)
    //                 ->where('status', 1)
    //                 ->count();

    //             if ($count > 0) {
    //                 $suggestions[] = [
    //                     'title' => $highlighted,
    //                     'name' => $combinedKeyword
    //                 ];
    //             }

    //             // 生成"品类词+关键词"组合
    //             $combinedKeyword = $category . $keyword;

    //             // 使用辅助方法高亮处理关键词
    //             $highlighted = $this->highlightKeyword($combinedKeyword, $keyword);

    //             // 验证组合是否有效（能搜索到商品）
    //             $count = Db::name('goods')
    //                 ->where(function($query) use ($combinedKeyword) {
    //                     $query->whereOr('name', 'like', "%{$combinedKeyword}%")
    //                           ->whereOr('split_word', 'like', "%{$combinedKeyword}%");
    //                 })
    //                 ->where('del', 0)
    //                 ->where('status', 1)
    //                 ->count();

    //             if ($count > 0) {
    //                 $suggestions[] = [
    //                     'title' => $highlighted,
    //                     'name' => $combinedKeyword
    //                 ];
    //             }

    //             // 如果已经有足够的候选词，就停止查询
    //             if (count($suggestions) >= $limit) {
    //                 break;
    //             }

    //             // 查询包含该品类词的商品
    //             $goods = Db::name('goods')
    //                 ->where('name', 'like', "%{$category}%")
    //                 ->where('status', 1)
    //                 ->field('name, split_word')
    //                 ->limit(5)
    //                 ->select()
    //                 ->toArray();

    //             foreach ($goods as $item) {
    //                 // 提取商品名称中的关键部分，不加省略号
    //                 $nameWords = explode(' ', $item['name']);
    //                 $shortName = $nameWords[0];

    //                 // 使用辅助方法高亮处理关键词
    //                 $highlighted = $this->highlightKeyword($shortName, $keyword);

    //                 // 检查是否已存在相同的候选词
    //                 $exists = false;
    //                 foreach ($suggestions as $suggestion) {
    //                     if (isset($suggestion['name']) && $suggestion['name'] === $shortName) {
    //                         $exists = true;
    //                         break;
    //                     }
    //                 }

    //                 if (!$exists) {
    //                     $suggestions[] = [
    //                         'title' => $highlighted,
    //                         'name' => $shortName
    //                     ];
    //                 }

    //                 // 如果商品有分词字段，尝试组合生成更多候选词
    //                 if (!empty($item['split_word'])) {
    //                     $splitWords = explode(',', $item['split_word']);
    //                     foreach ($centerWords as $centerWord) {
    //                         foreach ($splitWords as $splitWord) {
    //                             if (mb_strlen($splitWord, 'UTF-8') >= 2 && $splitWord !== $centerWord) {
    //                                 // 生成组合词
    //                                 $combination = $centerWord . $splitWord;

    //                                 // 使用辅助方法高亮处理关键词
    //                                 $highlighted = $this->highlightKeyword($combination, $keyword);

    //                                 // 检查是否已存在相同的候选词
    //                                 $exists = false;
    //                                 foreach ($suggestions as $suggestion) {
    //                                     if (isset($suggestion['name']) && $suggestion['name'] === $combination) {
    //                                         $exists = true;
    //                                         break;
    //                                     }
    //                                 }

    //                                 if (!$exists) {
    //                                     $suggestions[] = [
    //                                         'title' => $highlighted,
    //                                         'name' => $combination
    //                                     ];
    //                                 }

    //                                 // 另一种组合
    //                                 $combination = $splitWord . $centerWord;

    //                                 // 使用辅助方法高亮处理关键词
    //                                 $highlighted = $this->highlightKeyword($combination, $keyword);

    //                                 // 检查是否已存在相同的候选词
    //                                 $exists = false;
    //                                 foreach ($suggestions as $suggestion) {
    //                                     if (isset($suggestion['name']) && $suggestion['name'] === $combination) {
    //                                         $exists = true;
    //                                         break;
    //                                     }
    //                                 }

    //                                 if (!$exists) {
    //                                     $suggestions[] = [
    //                                         'title' => $highlighted,
    //                                         'name' => $combination
    //                                     ];
    //                                 }
    //                             }
    //                         }
    //                     }
    //                 }
    //             }

    //             // 如果已经有足够的候选词，就停止查询
    //             if (count($suggestions) >= $limit) {
    //                 break;
    //             }
    //         }
    //     }

    //     // 如果品类词相关候选词不足，使用中心词
    //     if (count($suggestions) < $limit) {
    //         foreach ($centerWords as $centerWord) {
    //             // 生成"关键词+中心词"组合
    //             $combinedKeyword = $keyword . $centerWord;

    //             // 使用辅助方法高亮处理关键词
    //             $highlighted = $this->highlightKeyword($combinedKeyword, $keyword);

    //             // 验证组合是否有效（能搜索到商品）
    //             $count = Db::name('goods')
    //                 ->where(function($query) use ($combinedKeyword) {
    //                     $query->whereOr('name', 'like', "%{$combinedKeyword}%")
    //                           ->whereOr('split_word', 'like', "%{$combinedKeyword}%");
    //                 })
    //                 ->where('del', 0)
    //                 ->where('status', 1)
    //                 ->count();

    //             if ($count > 0) {
    //                 $suggestions[] = [
    //                     'title' => $highlighted,
    //                     'name' => $combinedKeyword
    //                 ];
    //             }

    //             // 生成"中心词+关键词"组合
    //             $combinedKeyword = $centerWord . $keyword;

    //             // 使用辅助方法高亮处理关键词
    //             $highlighted = $this->highlightKeyword($combinedKeyword, $keyword);

    //             // 验证组合是否有效（能搜索到商品）
    //             $count = Db::name('goods')
    //                 ->where(function($query) use ($combinedKeyword) {
    //                     $query->whereOr('name', 'like', "%{$combinedKeyword}%")
    //                           ->whereOr('split_word', 'like', "%{$combinedKeyword}%");
    //                 })
    //                 ->where('del', 0)
    //                 ->where('status', 1)
    //                 ->count();

    //             if ($count > 0) {
    //                 $suggestions[] = [
    //                     'title' => $highlighted,
    //                     'name' => $combinedKeyword
    //                 ];
    //             }

    //             // 如果已经有足够的候选词，就停止查询
    //             if (count($suggestions) >= $limit) {
    //                 break;
    //             }
    //         }
    //     }

    //     // 如果候选词仍然不足，添加一些常见的属性词组合
    //     if (count($suggestions) < $limit) {
    //         $commonAttributes = [
    //             '男', '女', '童', '男款', '女款',
    //             '夏', '春', '秋', '冬',
    //             '修身', '宽松', '直筒',
    //             '休闲', '运动', '商务',
    //             '新款', '经典', '时尚'
    //         ];

    //         foreach ($commonAttributes as $attr) {
    //             $combinedKeyword = $keyword . $attr;

    //             // 使用辅助方法高亮处理关键词
    //             $highlighted = $this->highlightKeyword($combinedKeyword, $keyword);

    //             // 验证组合是否有效（能搜索到商品）
    //             $count = Db::name('goods')
    //                 ->where(function($query) use ($combinedKeyword) {
    //                     $query->whereOr('name', 'like', "%{$combinedKeyword}%")
    //                           ->whereOr('split_word', 'like', "%{$combinedKeyword}%");
    //                 })
    //                 ->where('del', 0)
    //                 ->where('status', 1)
    //                 ->count();

    //             if ($count > 0) {
    //                 $suggestions[] = [
    //                     'title' => $highlighted,
    //                     'name' => $combinedKeyword
    //                 ];

    //                 // 如果已经有足够的候选词，就停止添加
    //                 if (count($suggestions) >= $limit) {
    //                     break;
    //                 }
    //             }
    //         }
    //     }

    //     // 确保所有候选词都能找到对应的商品
    //     $validSuggestions = [];
    //     foreach ($suggestions as $suggestion) {
    //         // 获取候选词文本
    //         $text = is_array($suggestion) ? ($suggestion['name'] ?? '') : $suggestion;

    //         if (empty($text)) {
    //             continue;
    //         }

    //         // 验证候选词是否有对应商品
    //         $count = Db::name('goods')
    //             ->where(function($query) use ($text) {
    //                 $query->whereOr('name', 'like', "%{$text}%")
    //                       ->whereOr('split_word', 'like', "%{$text}%");
    //             })
    //             ->where('del', 0)
    //             ->where('status', 1)
    //             ->count();

    //         if ($count > 0) {
    //             $validSuggestions[] = $suggestion;
    //         }
    //     }

    //     return $validSuggestions;
    // }

    // /**
    //  * 从数据库中获取匹配的商品名称
    //  * @param string $keyword 搜索关键词
    //  * @param int $limit 返回数量限制
    //  * @return array 候选词列表
    //  */
    // private function getDbSuggestions($keyword, $limit)
    // {
    //     $suggestions = [];

    //     // 从商品表中查询匹配的商品名称
    //     $goods = Db::name('goods')
    //         ->where('name', 'like', "%{$keyword}%")
    //         ->where('status', 1)
    //         ->field('name, split_word')
    //         ->limit($limit * 2)
    //         ->select()
    //         ->toArray();

    //     // 提取商品名称中的关键属性
    //     $attributes = $this->extractAttributes($goods, $keyword);

    //     // 如果找到了属性，生成候选词
    //     if (!empty($attributes)) {
    //         foreach ($attributes as $attr) {
    //             $combinedKeyword = $keyword . $attr;

    //             // 使用辅助方法高亮处理关键词
    //             $highlighted = $this->highlightKeyword($combinedKeyword, $keyword);

    //             $suggestions[] = [
    //                 'title' => $highlighted,
    //                 'name' => $combinedKeyword
    //             ];

    //             // 如果已经有足够的候选词，就停止生成
    //             if (count($suggestions) >= $limit) {
    //                 break;
    //             }
    //         }
    //     }

    //     // 如果候选词不足，添加商品名称中的关键部分
    //     if (count($suggestions) < $limit) {
    //         foreach ($goods as $item) {
    //             // 提取商品名称中的关键部分，不加省略号
    //             $nameWords = explode(' ', $item['name']);
    //             $shortName = $nameWords[0];

    //             // 如果商品名称中包含关键词，确保关键词在短名称中
    //             if (mb_strpos($shortName, $keyword) === false && mb_strpos($item['name'], $keyword) !== false) {
    //                 $shortName = $keyword . mb_substr($item['name'], mb_strpos($item['name'], $keyword) + mb_strlen($keyword), 5, 'UTF-8');
    //             }

    //             // 使用辅助方法高亮处理关键词
    //             $highlighted = $this->highlightKeyword($shortName, $keyword);

    //             // 检查是否已存在相同的候选词
    //             $exists = false;
    //             foreach ($suggestions as $suggestion) {
    //                 if ($suggestion['name'] === $shortName) {
    //                     $exists = true;
    //                     break;
    //                 }
    //             }

    //             if (!$exists) {
    //                 $suggestions[] = [
    //                     'title' => $highlighted,
    //                     'name' => $shortName
    //                 ];
    //             }

    //             // 如果已经有足够的候选词，就停止添加
    //             if (count($suggestions) >= $limit) {
    //                 break;
    //             }
    //         }
    //     }

    //     return $suggestions;
    // }

    // /**
    //  * 提取商品属性
    //  * @param array $goods 商品数据
    //  * @param string $keyword 搜索关键词
    //  * @return array 属性列表
    //  */
    // private function extractAttributes($goods, $keyword)
    // {
    //     $attributes = [];
    //     $attributeCount = [];

    //     // 常见的商品属性词
    //     $commonAttributes = [
    //         '男', '女', '童', '男款', '女款', '中性', '情侣',
    //         '夏', '春', '秋', '冬', '四季',
    //         '短袖', '长袖', '无袖', '七分袖', '九分袖',
    //         '修身', '宽松', '直筒', '显瘦', '大码', '小码',
    //         '休闲', '正装', '运动', '商务', '职业', '日常',
    //         '纯棉', '真丝', '羊毛', '牛仔', '麻', '皮',
    //         '套装', '单件', '外套', '内搭', '打底',
    //         '新款', '经典', '复古', '时尚', '简约', '奢华',
    //         '儿童', '青少年', '成人', '中老年', '孕妇', '妈妈'
    //     ];

    //     // 从商品名称中提取属性
    //     foreach ($goods as $item) {
    //         // 使用分词字段
    //         if (!empty($item['split_word'])) {
    //             $words = explode(',', $item['split_word']);
    //             foreach ($words as $word) {
    //                 if (!empty($word) && mb_strlen($word, 'UTF-8') >= 1 && mb_strpos($keyword, $word) === false) {
    //                     if (!isset($attributeCount[$word])) {
    //                         $attributeCount[$word] = 0;
    //                     }
    //                     $attributeCount[$word]++;
    //                 }
    //             }
    //         }

    //         // 从商品名称中提取常见属性
    //         foreach ($commonAttributes as $attr) {
    //             if (mb_strpos($item['name'], $attr) !== false && mb_strpos($keyword, $attr) === false) {
    //                 if (!isset($attributeCount[$attr])) {
    //                     $attributeCount[$attr] = 0;
    //                 }
    //                 $attributeCount[$attr]++;
    //             }
    //         }
    //     }

    //     // 按出现频率排序
    //     arsort($attributeCount);

    //     // 取前20个属性
    //     $attributes = array_slice(array_keys($attributeCount), 0, 20);

    //     return $attributes;
    // }

    // /**
    //  * 使用阿里云分词API，提取关键词并组合
    //  * @param string $keyword 搜索关键词
    //  * @param int $limit 返回数量限制
    //  * @return array 候选词列表
    //  */
    // private function getWordSuggestions($keyword, $limit)
    // {
    //     $suggestions = [];

    //     try {
    //         // 使用阿里云分词API
    //         $segmentResult = $this->aliNlp->segment($keyword);

    //         if (isset($segmentResult['words']) && !empty($segmentResult['words'])) {
    //             $words = $segmentResult['words'];

    //             // 使用分词结果查询商品
    //             foreach ($words as $word) {
    //                 if (mb_strlen($word, 'UTF-8') >= 2) { // 只使用长度大于等于2的词
    //                     // 生成"关键词+分词"组合
    //                     $combinedKeyword = $keyword . $word;

    //                     // 使用辅助方法高亮处理关键词
    //                     $highlighted = $this->highlightKeyword($combinedKeyword, $keyword);

    //                     // 验证组合是否有效（能搜索到商品）
    //                     $count = Db::name('goods')
    //                         ->where(function($query) use ($combinedKeyword) {
    //                             $query->whereOr('name', 'like', "%{$combinedKeyword}%")
    //                                   ->whereOr('split_word', 'like', "%{$combinedKeyword}%");
    //                         })
    //                         ->where('del', 0)
    //                         ->where('status', 1)
    //                         ->count();

    //                     if ($count > 0) {
    //                         $suggestions[] = [
    //                             'title' => $highlighted,
    //                             'name' => $combinedKeyword
    //                         ];
    //                     }

    //                     // 如果已经有足够的候选词，就停止查询
    //                     if (count($suggestions) >= $limit) {
    //                         break;
    //                     }
    //                 }
    //             }

    //             // 如果候选词不足，尝试组合分词结果
    //             if (count($suggestions) < $limit && count($words) >= 2) {
    //                 for ($i = 0; $i < count($words) - 1; $i++) {
    //                     for ($j = $i + 1; $j < count($words); $j++) {
    //                         $combinedWord = $words[$i] . $words[$j];

    //                         // 使用辅助方法高亮处理关键词
    //                         $highlighted = $this->highlightKeyword($combinedWord, $keyword);

    //                         // 验证组合是否有效（能搜索到商品）
    //                         $count = Db::name('goods')
    //                             ->where(function($query) use ($combinedWord) {
    //                                 $query->whereOr('name', 'like', "%{$combinedWord}%")
    //                                       ->whereOr('split_word', 'like', "%{$combinedWord}%");
    //                             })
    //                             ->where('del', 0)
    //                             ->where('status', 1)
    //                             ->count();

    //                         if ($count > 0) {
    //                             $suggestions[] = [
    //                                 'title' => $highlighted,
    //                                 'name' => $combinedWord
    //                             ];
    //                         }

    //                         // 如果已经有足够的候选词，就停止查询
    //                         if (count($suggestions) >= $limit) {
    //                             break 2;
    //                         }
    //                     }
    //                 }
    //             }

    //             // 如果候选词仍然不足，添加一些常见的属性词组合
    //             if (count($suggestions) < $limit) {
    //                 $commonAttributes = [
    //                     '男', '女', '童', '男款', '女款',
    //                     '夏', '春', '秋', '冬',
    //                     '修身', '宽松', '直筒',
    //                     '休闲', '运动', '商务',
    //                     '新款', '经典', '时尚'
    //                 ];

    //                 foreach ($commonAttributes as $attr) {
    //                     $combinedKeyword = $keyword . $attr;

    //                     // 使用辅助方法高亮处理关键词
    //                     $highlighted = $this->highlightKeyword($combinedKeyword, $keyword);

    //                     // 验证组合是否有效（能搜索到商品）
    //                     $count = Db::name('goods')
    //                         ->where(function($query) use ($combinedKeyword) {
    //                             $query->whereOr('name', 'like', "%{$combinedKeyword}%")
    //                                   ->whereOr('split_word', 'like', "%{$combinedKeyword}%");
    //                         })
    //                         ->where('del', 0)
    //                         ->where('status', 1)
    //                         ->count();

    //                     if ($count > 0) {
    //                         $suggestions[] = [
    //                             'title' => $highlighted,
    //                             'name' => $combinedKeyword
    //                         ];

    //                         // 如果已经有足够的候选词，就停止添加
    //                         if (count($suggestions) >= $limit) {
    //                             break;
    //                         }
    //                     }
    //                 }
    //             }
    //         }
    //     } catch (\Exception $e) {
    //         Log::error('获取分词候选词失败: ' . $e->getMessage());
    //     }

    //     return $suggestions;
    // }

    // /**
    //  * 使用阿里云命名实体识别-电商API，识别商品属性
    //  * @param string $keyword 搜索关键词
    //  * @param int $limit 返回数量限制
    //  * @return array 候选词列表
    //  */
    // private function getNerSuggestions($keyword, $limit)
    // {
    //     $suggestions = [];

    //     try {
    //         // 使用阿里云命名实体识别-电商API
    //         $nerResult = $this->aliNlp->nerEcom($keyword);

    //         if (isset($nerResult['words']) && !empty($nerResult['words'])) {
    //             $entities = $nerResult['words'];

    //             // 使用实体结果生成候选词
    //             foreach ($entities as $entity) {
    //                 // 生成"关键词+实体"组合
    //                 $combinedKeyword = $keyword . $entity;

    //                 // 使用辅助方法高亮处理关键词
    //                 $highlighted = $this->highlightKeyword($combinedKeyword, $keyword);

    //                 // 验证组合是否有效（能搜索到商品）
    //                 $count = Db::name('goods')
    //                     ->where(function($query) use ($combinedKeyword) {
    //                         $query->whereOr('name', 'like', "%{$combinedKeyword}%")
    //                               ->whereOr('split_word', 'like', "%{$combinedKeyword}%");
    //                     })
    //                     ->where('del', 0)
    //                     ->where('status', 1)
    //                     ->count();

    //                 if ($count > 0) {
    //                     $suggestions[] = [
    //                         'title' => $highlighted,
    //                         'name' => $combinedKeyword
    //                     ];
    //                 }

    //                 // 生成"实体+关键词"组合
    //                 $combinedKeyword = $entity . $keyword;

    //                 // 使用辅助方法高亮处理关键词
    //                 $highlighted = $this->highlightKeyword($combinedKeyword, $keyword);

    //                 // 验证组合是否有效（能搜索到商品）
    //                 $count = Db::name('goods')
    //                     ->where(function($query) use ($combinedKeyword) {
    //                         $query->whereOr('name', 'like', "%{$combinedKeyword}%")
    //                               ->whereOr('split_word', 'like', "%{$combinedKeyword}%");
    //                     })
    //                     ->where('del', 0)
    //                     ->where('status', 1)
    //                     ->count();

    //                 if ($count > 0) {
    //                     $suggestions[] = [
    //                         'title' => $highlighted,
    //                         'name' => $combinedKeyword
    //                     ];
    //                 }

    //                 // 如果已经有足够的候选词，就停止查询
    //                 if (count($suggestions) >= $limit) {
    //                     break;
    //                 }
    //             }

    //             // 如果候选词不足，尝试使用实体查询商品
    //             if (count($suggestions) < $limit) {
    //                 foreach ($entities as $entity) {
    //                     $goods = Db::name('goods')
    //                         ->where('name', 'like', "%{$entity}%")
    //                         ->where('status', 1)
    //                         ->field('name')
    //                         ->limit(5)
    //                         ->select()
    //                         ->toArray();

    //                     foreach ($goods as $item) {
    //                         // 提取商品名称中的关键部分，不加省略号
    //                         $nameWords = explode(' ', $item['name']);
    //                         $shortName = $nameWords[0];

    //                         // 使用辅助方法高亮处理关键词
    //                         $highlighted = $this->highlightKeyword($shortName, $keyword);

    //                         // 检查是否已存在相同的候选词
    //                         $exists = false;
    //                         foreach ($suggestions as $suggestion) {
    //                             if ($suggestion['name'] === $shortName) {
    //                                 $exists = true;
    //                                 break;
    //                             }
    //                         }

    //                         if (!$exists) {
    //                             $suggestions[] = [
    //                                 'title' => $highlighted,
    //                                 'name' => $shortName
    //                             ];
    //                         }

    //                         // 如果已经有足够的候选词，就停止添加
    //                         if (count($suggestions) >= $limit) {
    //                             break 2;
    //                         }
    //                     }
    //                 }
    //             }
    //         }
    //     } catch (\Exception $e) {
    //         Log::error('获取命名实体候选词失败: ' . $e->getMessage());
    //     }

    //     return $suggestions;
    // }


}
