<?php

namespace app\shop\controller;

use app\common\basics\ShopBase;
use app\common\server\JsonServer;
use app\shop\logic\AlipayLogic;
use app\shop\validate\AlipayValidate;

class Alipay extends ShopBase
{
    function lists()
    {
        if ($this->request->isAjax()) {
            $get = $this->request->get();
            $lists = AlipayLogic::lists($get, $this->shop_id);
            return JsonServer::success('获取成功', $lists);
        }
        
        return view();
    }
    
    function detail()
    {
        return view('', [
            'detail'    => AlipayLogic::detail($this->request->get('id')),
        ]);
    }
    
    function add()
    {
        if ($this->request->isAjax()) {
            (new AlipayValidate())->goCheck('add');
            $post = $this->request->post();
            $res = AlipayLogic::add($post, $this->shop_id);
            if ($res === false) {
                $error = AlipayLogic::getError() ?: '新增失败';
                return JsonServer::error($error);
            }
            return JsonServer::success('新增成功');
        }
        
        return view();
    }
    
    function edit()
    {
        if ($this->request->isAjax()) {
            (new AlipayValidate())->goCheck('edit');
            $post = $this->request->post();
            $res = AlipayLogic::edit($post, $this->shop_id);
            if ($res === false) {
                $error = AlipayLogic::getError() ?: '编辑失败';
                return JsonServer::error($error);
            }
            return JsonServer::success('编辑成功');
        }
        
        $id = $this->request->get('id');
        return view('', [
            'detail' => AlipayLogic::detail($id)
        ]);
    }
    
    function del()
    {
        if ($this->request->isAjax()) {
            (new AlipayValidate())->goCheck('id');
            $id = $this->request->post('id');
            $res = AlipayLogic::del($id, $this->shop_id);
            if ($res === false) {
                $error = AlipayLogic::getError() ?: '删除失败';
                return JsonServer::error($error);
            }
            return JsonServer::success('删除成功');
        }
        
        return JsonServer::error('异常');
    }
}