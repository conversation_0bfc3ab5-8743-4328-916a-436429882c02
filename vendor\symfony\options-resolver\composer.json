{"name": "symfony/options-resolver", "type": "library", "description": "Provides an improved replacement for the array_replace PHP function", "keywords": ["options", "config", "configuration"], "homepage": "https://symfony.com", "license": "MIT", "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "require": {"php": ">=7.1.3"}, "autoload": {"psr-4": {"Symfony\\Component\\OptionsResolver\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "minimum-stability": "dev"}