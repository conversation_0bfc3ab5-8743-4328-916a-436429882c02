<?php

namespace app\common\logic;

use app\common\basics\Logic;
use app\common\enum\ShopTierEnum;
use app\common\model\shop\Shop;
use app\common\model\shop\ShopTierConfig;
use app\common\model\shop\ShopMerchantfees;
use app\common\server\ConfigServer;
use think\facade\Db;
use Exception;

/**
 * 商家等级逻辑类
 */
class ShopTierLogic extends Logic
{
    /**
     * 获取所有等级配置
     */
    public static function getTierConfigs()
    {
        $configs = ShopTierConfig::where('status', 1)
            ->order('sort_order', 'asc')
            ->select()
            ->toArray();

        // 更新价格为系统配置中的价格
        foreach ($configs as &$config) {
            $config['tier_price'] = self::getTierPrice($config['tier_level']);
        }

        return $configs;
    }

    /**
     * 获取指定等级配置
     */
    public static function getTierConfig($tierLevel)
    {
        return ShopTierConfig::where('tier_level', $tierLevel)
            ->where('status', 1)
            ->find();
    }

    /**
     * 获取等级费用（从系统配置中获取）
     */
    public static function getTierPrice($tierLevel)
    {
        switch ($tierLevel) {
            case ShopTierEnum::TIER_FREE: // 0元入驻
                return 0;
            case ShopTierEnum::TIER_MEMBER: // 商家会员
                return ConfigServer::get('shop_entry', 'entry_fee', 0);
            case ShopTierEnum::TIER_PREMIUM: // 实力厂商
                return ConfigServer::get('shop_entry', 'ins_fee', 0);
            default:
                return 0;
        }
    }

    /**
     * 根据等级代码获取配置
     */
    public static function getTierConfigByCode($tierCode)
    {
        return ShopTierConfig::where('tier_code', $tierCode)
            ->where('status', 1)
            ->find();
    }

    /**
     * 检查商家等级权限
     */
    public static function checkPermission($shopId, $permission)
    {
        $shop = Shop::find($shopId);
        if (!$shop) {
            return false;
        }

        $tierConfig = self::getTierConfig($shop->tier_level);
        if (!$tierConfig) {
            return false;
        }

        $features = $tierConfig->features;
        return isset($features[$permission]) && $features[$permission];
    }

    /**
     * 获取商家等级限制
     */
    public static function getTierLimits($shopId)
    {
        $shop = Shop::find($shopId);
        if (!$shop) {
            return [];
        }

        $tierConfig = self::getTierConfig($shop->tier_level);
        if (!$tierConfig) {
            return [];
        }

        return $tierConfig->limits;
    }

    /**
     * 创建等级支付订单
     */
    public static function createTierOrder($userId, $tierLevel)
    {
        try {
            // 检查用户是否已有申请记录
            $existApply = \app\common\model\shop\ShopApply::where([
                'user_id' => $userId,
                'del' => 0,
                'audit_status' => ['in', [1, 2]] // 待审核或已通过
            ])->find();

            if ($existApply) {
                self::$error = '您已有入驻申请记录，请勿重复申请';
                return false;
            }

            // 0元入驻无需支付
            if ($tierLevel == ShopTierEnum::TIER_FREE) {
                return [
                    'tier_level' => $tierLevel,
                    'need_payment' => false,
                    'tier_name' => ShopTierEnum::getTierName($tierLevel)
                ];
            }

            // 获取等级配置
            $tierConfig = self::getTierConfig($tierLevel);
            if (!$tierConfig) {
                self::$error = '等级配置不存在';
                return false;
            }

            // 检查是否已有未支付的订单
            $existOrder = ShopMerchantfees::where([
                'user_id' => $userId,
                'tier_level' => $tierLevel,
                'tier_type' => ShopTierEnum::TYPE_NEW_ENTRY,
                'status' => 0 // 未支付
            ])->find();

            if ($existOrder) {
                return [
                    'tier_level' => $tierLevel,
                    'need_payment' => true,
                    'order_sn' => $existOrder->order_sn,
                    'amount' => $existOrder->amount,
                    'tier_name' => $tierConfig->tier_name
                ];
            }

            // 获取等级费用
            $tierPrice = self::getTierPrice($tierLevel);

            // 创建新的支付订单
            $orderSn = createSn('shop_merchantfees', 'order_sn');
            $orderData = [
                'user_id' => $userId,
                'shop_id' => 0,
                'order_sn' => $orderSn,
                'amount' => $tierPrice,
                'feetype' => $tierLevel == ShopTierEnum::TIER_PREMIUM ? 1 : 0, // 兼容原有逻辑
                'tier_level' => $tierLevel,
                'tier_type' => ShopTierEnum::TYPE_NEW_ENTRY,
                'status' => 0, // 待支付
                'created_at' => date('Y-m-d H:i:s')
            ];

            ShopMerchantfees::create($orderData);

            return [
                'tier_level' => $tierLevel,
                'need_payment' => true,
                'order_sn' => $orderSn,
                'amount' => $tierPrice,
                'tier_name' => $tierConfig->tier_name
            ];

        } catch (Exception $e) {
            self::$error = $e->getMessage();
            return false;
        }
    }

    /**
     * 创建等级升级订单
     */
    public static function createUpgradeOrder($shopId, $targetTierLevel, $userId = 0)
    {
        try {
            $shop = Shop::find($shopId);
            if (!$shop) {
                self::$error = '商家不存在';
                return false;
            }

            if ($shop->tier_level >= $targetTierLevel) {
                self::$error = '目标等级不能低于或等于当前等级';
                return false;
            }

            $targetConfig = self::getTierConfig($targetTierLevel);
            if (!$targetConfig) {
                self::$error = '目标等级配置不存在';
                return false;
            }

            // 直接使用目标等级的费用作为升级费用
            $upgradePrice = self::getTierPrice($targetTierLevel);

            if ($upgradePrice <= 0) {
                self::$error = '升级费用配置错误，请检查商家入驻费用设置';
                return false;
            }

            // 创建升级订单
            $orderSn = createSn('shop_merchantfees', 'order_sn');
            $orderData = [
                'user_id' => $userId,
                'shop_id' => $shopId,
                'order_sn' => $orderSn,
                'amount' => $upgradePrice,
                'feetype' => $targetTierLevel == ShopTierEnum::TIER_PREMIUM ? 1 : 0,
                'tier_level' => $targetTierLevel,
                'tier_type' => ShopTierEnum::TYPE_UPGRADE,
                'status' => 0, // 待支付
                'created_at' => date('Y-m-d H:i:s')
            ];
            ShopMerchantfees::create($orderData);

            return [
                'order_sn' => $orderSn,
                'amount' => $upgradePrice,
                'target_tier_level' => $targetTierLevel,
                'target_tier_name' => $targetConfig->tier_name,
            ];

        } catch (Exception $e) {
            self::$error = $e->getMessage();
            return false;
        }
    }

    /**
     * 处理等级支付回调
     */
    public static function handleTierPayment($orderSn)
    {
        try {
            Db::startTrans();

            $order = ShopMerchantfees::where('order_sn', $orderSn)->find();
            if (!$order) {
                throw new Exception('订单不存在');
            }

            if ($order->status != 0) {
                throw new Exception('订单状态异常');
            }

            // 更新订单状态
            $order->status = 1; // 已支付
            $order->payment_date = time();
            $order->save();

            if ($order->tier_type == ShopTierEnum::TYPE_UPGRADE) {
                // 处理等级升级
                $shop = Shop::find($order->shop_id);
                if ($shop) {
                    $shop->tier_level = $order->tier_level;
                    $shop->tier_expire_time = time() + (365 * 24 * 3600); // 1年后到期
                    $shop->save();
                }
            } else {
                // 处理新入驻支付（标记预付费状态）
                $shopApply = \app\common\model\shop\ShopApply::where('user_id', $order->user_id)
                    ->where('del', 0)
                    ->order('id', 'desc')
                    ->find();
                
                if ($shopApply) {
                    $shopApply->is_prepaid = 1;
                    $shopApply->target_tier_level = $order->tier_level;
                    $shopApply->save();
                }
            }

            Db::commit();
            return true;

        } catch (Exception $e) {
            Db::rollback();
            self::$error = $e->getMessage();
            return false;
        }
    }

    /**
     * 检查等级是否过期
     */
    public static function checkTierExpired($shopId)
    {
        $shop = Shop::find($shopId);
        if (!$shop || $shop->tier_level == ShopTierEnum::TIER_FREE) {
            return false; // 0元入驻不会过期
        }

        return $shop->tier_expire_time > 0 && $shop->tier_expire_time < time();
    }

    /**
     * 获取商家可升级的等级列表
     */
    public static function getUpgradableTiers($shopId)
    {
        $shop = Shop::find($shopId);
        if (!$shop) {
            return [];
        }

        $allTiers = self::getTierConfigs();
        $upgradableTiers = [];

        foreach ($allTiers as $tier) {
            if ($tier['tier_level'] > $shop->tier_level) {
                // 直接使用目标等级的费用作为升级费用
                $tierPrice = self::getTierPrice($tier['tier_level']);

                if ($tierPrice > 0) {
                    $tier['upgrade_price'] = $tierPrice;
                    $tier['tier_price'] = $tierPrice;
                    $upgradableTiers[] = $tier;
                }
            }
        }

        return $upgradableTiers;
    }
}
