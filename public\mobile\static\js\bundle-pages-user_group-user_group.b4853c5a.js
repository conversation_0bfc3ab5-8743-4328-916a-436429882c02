(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["bundle-pages-user_group-user_group"],{2044:function(t,e,a){"use strict";var n=a("47c3"),i=a.n(n);i.a},2681:function(t,e,a){"use strict";a.r(e);var n=a("6e84"),i=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(r);e["default"]=i.a},4316:function(t,e,a){"use strict";a("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("a9e3");var n={props:{i:Number,index:{type:Number,default:function(){return 0}}},data:function(){return{downOption:{auto:!1},upOption:{auto:!1},isInit:!1}},watch:{index:function(t){this.i!==t||this.isInit||(this.isInit=!0,this.mescroll&&this.mescroll.triggerDownScroll())}},methods:{mescrollInitByRef:function(){if(!this.mescroll||!this.mescroll.resetUpScroll){var t=this.$refs.mescrollRef||this.$refs["mescrollRef"+this.i];t&&(this.mescroll=t.mescroll)}},mescrollInit:function(t){this.mescroll=t,this.mescrollInitByRef&&this.mescrollInitByRef(),this.i===this.index&&(this.isInit=!0,this.mescroll.triggerDownScroll())}}},i=n;e.default=i},"47c3":function(t,e,a){var n=a("4e07");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var i=a("4f06").default;i("69b7b185",n,!0,{sourceMap:!1,shadowMode:!1})},"4e07":function(t,e,a){var n=a("24fb");e=n(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */uni-page-body[data-v-699c4dfa]{\n  /* 定义一些主题色及基础样式 */font-family:PingFang SC,Arial,Hiragino Sans GB,Microsoft YaHei,sans-serif;font-size:%?28?%;color:#333;padding-bottom:env(safe-area-inset-bottom);background-color:#f6f6f6}body.?%PAGE?%[data-v-699c4dfa]{background-color:#f6f6f6}.bold[data-v-699c4dfa]{font-weight:700}\n/* 定义字体颜色 */.primary[data-v-699c4dfa]{color:#ff2c3c}.bg-primary[data-v-699c4dfa]{background-color:#ff2c3c}.bg-white[data-v-699c4dfa]{background-color:#fff}.bg-body[data-v-699c4dfa]{background-color:#f6f6f6}.bg-gray[data-v-699c4dfa]{background-color:#e5e5e5}.black[data-v-699c4dfa]{color:#101010}.white[data-v-699c4dfa]{color:#fff}.normal[data-v-699c4dfa]{color:#333}.lighter[data-v-699c4dfa]{color:#666}.muted[data-v-699c4dfa]{color:#999}\n/* 定义字体大小 */.xxl[data-v-699c4dfa]{font-size:%?36?%}.xl[data-v-699c4dfa]{font-size:%?34?%}.lg[data-v-699c4dfa]{font-size:%?32?%}.md[data-v-699c4dfa]{font-size:%?30?%}.nr[data-v-699c4dfa]{font-size:%?28?%}.sm[data-v-699c4dfa]{font-size:%?26?%}.xs[data-v-699c4dfa]{font-size:%?24?%}.xxs[data-v-699c4dfa]{font-size:%?22?%}\n/* 定义常用外边距 */.ml5[data-v-699c4dfa]{margin-left:%?5?%}.ml10[data-v-699c4dfa]{margin-left:%?10?%}.ml20[data-v-699c4dfa]{margin-left:%?20?%}.ml30[data-v-699c4dfa]{margin-left:%?30?%}.mr5[data-v-699c4dfa]{margin-right:%?5?%}.mr10[data-v-699c4dfa]{margin-right:%?10?%}.mr20[data-v-699c4dfa]{margin-right:%?20?%}.mr30[data-v-699c4dfa]{margin-right:%?30?%}.mt5[data-v-699c4dfa]{margin-top:%?5?%}.mt10[data-v-699c4dfa]{margin-top:%?10?%}.mt20[data-v-699c4dfa]{margin-top:%?20?%}.mt30[data-v-699c4dfa]{margin-top:%?30?%}.mb5[data-v-699c4dfa]{margin-bottom:%?5?%}.mb10[data-v-699c4dfa]{margin-bottom:%?10?%}.mb20[data-v-699c4dfa]{margin-bottom:%?20?%}.mb30[data-v-699c4dfa]{margin-bottom:%?30?%}\n/* 定义常用的弹性布局 */.flex1[data-v-699c4dfa]{flex:1}.flexnone[data-v-699c4dfa]{flex:none}.wrap[data-v-699c4dfa]{flex-wrap:wrap}.row[data-v-699c4dfa]{display:flex;align-items:center}.row-center[data-v-699c4dfa]{display:flex;align-items:center;justify-content:center}.row-end[data-v-699c4dfa]{display:flex;align-items:center;justify-content:flex-end}.row-between[data-v-699c4dfa]{display:flex;align-items:center;justify-content:space-between}.row-around[data-v-699c4dfa]{display:flex;align-items:center;justify-content:space-around}.column[data-v-699c4dfa]{display:flex;flex-direction:column;justify-content:center}.column-center[data-v-699c4dfa]{display:flex;flex-direction:column;align-items:center;justify-content:center}.column-around[data-v-699c4dfa]{display:flex;flex-direction:column;align-items:center;justify-content:space-around}.column-end[data-v-699c4dfa]{display:flex;flex-direction:column;align-items:center;justify-content:flex-end}.column-between[data-v-699c4dfa]{display:flex;flex-direction:column;align-items:center;justify-content:space-between}\n/* 超出隐藏 */.line1[data-v-699c4dfa]{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.line-1[data-v-699c4dfa]{word-break:break-all;display:-webkit-box;-webkit-line-clamp:1;-webkit-box-orient:vertical;overflow:hidden}.line2[data-v-699c4dfa]{word-break:break-all;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical;overflow:hidden}\n/* 中划线 */.line-through[data-v-699c4dfa]{text-decoration:line-through}\n/* br60 */.br60[data-v-699c4dfa]{border-radius:%?60?%}\n/* 初始化按钮 */uni-page-body uni-button[data-v-699c4dfa]{padding:0;margin:0;background-color:initial;font-weight:400;font-size:%?28?%;overflow:unset;margin-left:0;margin-right:0}uni-page-body uni-button[data-v-699c4dfa]::after{border:none}uni-button[type=primary][data-v-699c4dfa]{background-color:#ff2c3c}.button-hover[type=primary][data-v-699c4dfa]{background-color:#ff2c3c}uni-button[disabled][type=primary][data-v-699c4dfa]{background-color:#ff2c3c}\n/* 按钮大小 */uni-button[size="xs"][data-v-699c4dfa]{line-height:%?58?%;height:%?58?%;font-size:%?26?%;padding:0 %?30?%}uni-button[size="sm"][data-v-699c4dfa]{line-height:%?62?%;height:%?62?%;font-size:%?28?%;padding:0 %?30?%}uni-button[size="md"][data-v-699c4dfa]{line-height:%?70?%;height:%?70?%;font-size:%?30?%;padding:0 %?30?%}uni-button[size="lg"][data-v-699c4dfa]{line-height:%?80?%;height:%?80?%;font-size:%?32?%;padding:0 %?30?%}.icon-xs[data-v-699c4dfa]{min-height:%?28?%;min-width:%?28?%;height:%?28?%;width:%?28?%;vertical-align:middle}.icon-sm[data-v-699c4dfa]{min-height:%?30?%;min-width:%?30?%;height:%?30?%;width:%?30?%;vertical-align:middle}.icon[data-v-699c4dfa]{min-height:%?34?%;min-width:%?34?%;height:%?34?%;width:%?34?%;vertical-align:middle}.icon-md[data-v-699c4dfa]{min-height:%?44?%;min-width:%?44?%;height:%?44?%;width:%?44?%;vertical-align:middle}.icon-lg[data-v-699c4dfa]{min-height:%?52?%;min-width:%?52?%;height:%?52?%;width:%?52?%;vertical-align:middle}.icon-xl[data-v-699c4dfa]{min-height:%?64?%;min-width:%?64?%;height:%?64?%;width:%?64?%;vertical-align:middle}.icon-xxl[data-v-699c4dfa]{min-height:%?120?%;min-width:%?120?%;height:%?120?%;width:%?120?%;vertical-align:middle}.img-null[data-v-699c4dfa]{width:%?300?%;height:%?300?%}\n/* 隐藏滚动条 */[data-v-699c4dfa]::-webkit-scrollbar{width:0;height:0;color:transparent}.group-list[data-v-699c4dfa]{min-height:calc(100vh - %?80?%);padding:0 %?20?%;overflow:hidden}.group-list .item[data-v-699c4dfa]{border-radius:%?10?%;position:relative}.group-list .item .group-header[data-v-699c4dfa]{height:%?80?%;padding:0 %?24?%;border-bottom:1px dotted #e5e5e5}.group-list .item .team-chief[data-v-699c4dfa]{position:absolute;z-index:100;top:%?105?%;padding:%?4?% %?20?%;border-radius:0 %?60?% %?60?% 0;background:linear-gradient(87deg,#f95f2f,#ff2c3c)}.group-list .item .all-price[data-v-699c4dfa]{text-align:right;padding:0 %?24?% %?20?%}.group-list .item .group-footer[data-v-699c4dfa]{height:%?100?%;border-top:1px solid #e5e5e5;padding:0 %?24?%}.group-list .item .group-footer .btn[data-v-699c4dfa]{width:%?244?%}',""]),t.exports=e},"5fb7":function(t,e,a){"use strict";a.r(e);var n=a("c423"),i=a("8629");for(var r in i)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(r);var o=a("f0c5"),d=Object(o["a"])(i["default"],n["b"],n["c"],!1,null,"c6aed4b4",null,!1,n["a"],void 0);e["default"]=d.exports},"6e84":function(t,e,a){"use strict";a("7a82");var n=a("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("a9e3"),a("99af");var i=a("c60f"),r=n(a("bde1")),o=n(a("4316")),d={mixins:[r.default,o.default],data:function(){return{groupList:[],downOption:{auto:!1},upOption:{auto:!0,noMoreSize:1,empty:{icon:"/static/images/goods_null.png",tip:"暂无拼团~",fixed:!0}}}},props:{groupType:{type:Number}},mounted:function(){},methods:{upCallback:function(t){this.$getUserGroup(t.num,t.size)},$getUserGroup:function(){var t=this,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10,n=this.groupType;(0,i.getUserGroup)({type:n,page_no:e,page_size:a}).then((function(a){if(a){var n=a.data.list,i=n.length,r=!!a.data.more;1==e&&(t.lists=[]),console.log(r),t.groupList=t.lists.concat(n),t.mescroll.endSuccess(i,r)}})).catch((function(){t.mescroll.endErr()}))}},computed:{getGroupStatus:function(){return function(t){var e="";switch(t){case 0:e="拼团中";break;case 1:e="拼团成功";break;case 2:e="拼团失败";break}return e}},getTeamCountTime:function(){return function(t){return t.count_time=Math.min(t.found_end_time,t.end_time)-Date.now()/1e3}}}};e.default=d},"84f3":function(t,e,a){"use strict";a("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("c740");var n=a("98a1"),i={data:function(){return{active:"",group:[{name:"全部",type:n.groupType.ALL,isShow:!0},{name:"拼团中",type:n.groupType.PROGESS,isShow:!1},{name:"拼团成功",type:n.groupType.SUCCESS,isShow:!1},{name:"拼团失败",type:n.groupType.FAIL,isShow:!1}]}},onLoad:function(t){var e=this.group,a=t.type||n.groupType.ALL,i=e.findIndex((function(t){return t.type==a}));this.changeShow(i)},methods:{changeShow:function(t){-1!=t&&(this.active=t,this.group[t].isShow=!0)}}};e.default=i},8629:function(t,e,a){"use strict";a.r(e);var n=a("84f3"),i=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(r);e["default"]=i.a},9769:function(t,e,a){"use strict";a.r(e);var n=a("c0ff"),i=a("2681");for(var r in i)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(r);a("2044");var o=a("f0c5"),d=Object(o["a"])(i["default"],n["b"],n["c"],!1,null,"699c4dfa",null,!1,n["a"],void 0);e["default"]=d.exports},c0ff:function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return r})),a.d(e,"a",(function(){return n}));var n={mescrollUni:a("0bbb").default,orderGoods:a("d9ab").default,priceFormat:a("fefe").default},i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("mescroll-uni",{ref:"mescrollRef",attrs:{top:"80rpx",down:t.downOption,up:t.upOption},on:{down:function(e){arguments[0]=e=t.$handleEvent(e),t.downCallback.apply(void 0,arguments)},up:function(e){arguments[0]=e=t.$handleEvent(e),t.upCallback.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"group-list"},t._l(t.groupList,(function(e,n){return a("v-uni-navigator",{key:n,staticClass:"item bg-white mt20",attrs:{"hover-class":"none",url:"/bundle/pages/order_details/order_details?id="+e.order_id}},[a("v-uni-view",{staticClass:"group-header row-between"},[a("v-uni-view",[e.team_end_time?a("v-uni-view",[t._v(t._s(e.team_end_time))]):a("v-uni-view",[t.getTeamCountTime(e)>=0?a("v-uni-view",{staticClass:"row"},[a("v-uni-view",{staticClass:"sm mr10"},[t._v(t._s(e.shop_name))])],1):t._e()],1)],1),a("v-uni-view",{class:2==e.status?"muted":"primary"},[t._v(t._s(t.getGroupStatus(e.status)))])],1),a("v-uni-view",{staticClass:"group-con"},[e.identity_text?a("v-uni-view",{staticClass:"team-chief xs white"},[t._v(t._s(e.identity_text))]):t._e(),a("order-goods",{attrs:{team:{need:e.need},list:[{people_num:e.people_num,name:e.name,spec_value_str:e.spec_value_str,image:e.image,goods_num:e.count,goods_id:e.goods_id,goods_price:e.order_amount}]}}),a("v-uni-view",{staticClass:"all-price row-end"},[a("v-uni-text",{staticClass:"muted xs"},[t._v("共"+t._s(e.count)+"件商品，总金额：")]),a("price-format",{attrs:{"show-subscript":!0,"subscript-size":30,"first-size":30,"second-size":30,price:e.order_amount}})],1)],1),0==e.pay_status?a("v-uni-view",{staticClass:"group-footer row"},[a("v-uni-view",{staticStyle:{flex:"1"}}),a("v-uni-view",[0==e.pay_status?a("v-uni-navigator",{attrs:{"hover-class":"none",url:"/pages/order_details/order_details?id="+e.order_id}},[a("v-uni-button",{staticClass:"br60 lighter btn",attrs:{size:"sm",type:"primary","hover-class":"none"}},[t._v("去付款")])],1):t._e()],1)],1):t._e()],1)})),1)],1)},r=[]},c423:function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return r})),a.d(e,"a",(function(){return n}));var n={tabs:a("741a").default,tab:a("5652").default,groupList:a("9769").default},i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{staticClass:"user-group"},[a("tabs",{attrs:{active:t.active,isScroll:!1},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.changeShow.apply(void 0,arguments)}}},t._l(t.group,(function(e,n){return a("tab",{key:n,attrs:{name:e.name}},[e.isShow?a("group-list",{ref:"group"+e.type,refInFor:!0,attrs:{groupType:e.type}}):t._e()],1)})),1)],1)},r=[]},c60f:function(t,e,a){"use strict";a("7a82");var n=a("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.cancelIntegralOrder=function(t){return i.default.post("integral_order/cancel",{id:t})},e.closeBargainOrder=function(t){return i.default.get("bargain/closeBargain",{params:t})},e.confirmIntegralOrder=function(t){return i.default.post("integral_order/confirm",{id:t})},e.delIntegralOrder=function(t){return i.default.post("integral_order/del",{id:t})},e.getActivityGoodsLists=function(t){return i.default.get("activity_area/activityGoodsList",{params:t})},e.getBargainActivityDetail=function(t){return i.default.get("bargain/bargainDetail",{params:t})},e.getBargainActivityList=function(t){return i.default.get("bargain/orderList",{params:t})},e.getBargainDetail=function(t){return i.default.get("bargain/detail",{params:t})},e.getBargainList=function(t){return i.default.get("bargain/lists",{params:t})},e.getBargainNumber=function(){return i.default.get("bargain/barginNumber")},e.getBargainPost=function(t){return i.default.get("share/shareBargain",{params:t})},e.getCoupon=function(t){return i.default.post("coupon/getCoupon",{coupon_id:t})},e.getCouponList=function(t){return i.default.get("coupon/getCouponList",{params:t})},e.getGroupList=function(t){return i.default.get("team/activity",{params:t})},e.getIntegralGoods=function(t){return i.default.get("integral_goods/lists",{params:t})},e.getIntegralGoodsDetail=function(t){return i.default.get("integral_goods/detail",{params:t})},e.getIntegralOrder=function(t){return i.default.get("integral_order/lists",{params:t})},e.getIntegralOrderDetail=function(t){return i.default.get("integral_order/detail",{params:{id:t}})},e.getIntegralOrderTraces=function(t){return i.default.get("integral_order/orderTraces",{params:{id:t}})},e.getMyCoupon=function(t){return i.default.get("coupon/myCouponList",{params:t})},e.getOrderCoupon=function(t){return i.default.post("coupon/getBuyCouponList",t)},e.getSeckillGoods=function(t){return i.default.get("seckill_goods/getSeckillGoods",{params:t})},e.getSeckillTime=function(){return i.default.get("seckill_goods/getSeckillTime")},e.getSignLists=function(){return i.default.get("sign/lists")},e.getSignRule=function(){return i.default.get("sign/rule")},e.getTeamInfo=function(t){return i.default.get("team/teamInfo",{params:t})},e.getUserGroup=function(t){return i.default.get("team/record",{params:t})},e.helpBargain=function(t){return i.default.post("bargain/knife",t)},e.integralSettlement=function(t){return i.default.get("integral_order/settlement",{params:t})},e.integralSubmitOrder=function(t){return i.default.post("integral_order/submitOrder",t)},e.launchBargain=function(t){return i.default.post("bargain/sponsor",t)},e.teamBuy=function(t){return i.default.post("team/buy",t)},e.teamCheck=function(t){return i.default.post("team/check",t)},e.teamKaiTuan=function(t){return i.default.post("team/kaituan",t)},e.userSignIn=function(){return i.default.get("sign/sign")};var i=n(a("3b33"));a("b08d")}}]);