-- 添加资质管理菜单到权限表

-- 插入资质管理菜单项
INSERT INTO `ls_dev_auth` (`type`, `system`, `pid`, `name`, `icon`, `uri`, `sort`, `disable`, `create_time`, `update_time`, `del`) 
VALUES (1, 0, 29, '资质管理', 'layui-icon-certificate', 'goods.qualification/lists', 110, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0);

-- 获取刚插入的资质管理菜单ID（假设为新的ID）
-- 注意：实际执行时需要根据实际插入的ID来调整下面的pid值

-- 插入资质管理的子菜单项
INSERT INTO `ls_dev_auth` (`type`, `system`, `pid`, `name`, `icon`, `uri`, `sort`, `disable`, `create_time`, `update_time`, `del`) 
VALUES 
-- 资质列表
(2, 0, (SELECT id FROM ls_dev_auth WHERE name = '资质管理' AND pid = 29 AND del = 0 LIMIT 1), '资质列表', '', 'goods.qualification/lists', 1, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0),
-- 新增资质
(2, 0, (SELECT id FROM ls_dev_auth WHERE name = '资质管理' AND pid = 29 AND del = 0 LIMIT 1), '新增资质', '', 'goods.qualification/add', 2, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0),
-- 编辑资质
(2, 0, (SELECT id FROM ls_dev_auth WHERE name = '资质管理' AND pid = 29 AND del = 0 LIMIT 1), '编辑资质', '', 'goods.qualification/edit', 3, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0),
-- 删除资质
(2, 0, (SELECT id FROM ls_dev_auth WHERE name = '资质管理' AND pid = 29 AND del = 0 LIMIT 1), '删除资质', '', 'goods.qualification/del', 4, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0),
-- 资质状态切换
(2, 0, (SELECT id FROM ls_dev_auth WHERE name = '资质管理' AND pid = 29 AND del = 0 LIMIT 1), '资质状态切换', '', 'goods.qualification/status', 5, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0),
-- 搜索资质
(2, 0, (SELECT id FROM ls_dev_auth WHERE name = '资质管理' AND pid = 29 AND del = 0 LIMIT 1), '搜索资质', '', 'goods.qualification/search', 6, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0);
