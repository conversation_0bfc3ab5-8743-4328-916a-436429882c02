<?php
namespace app\admin\logic;

use app\common\basics\Logic;
use app\common\model\MassMessageLimit;
use app\admin\logic\PurchaserAllocationLogic;
use think\facade\Db;

/**
 * 群发信息限制配置逻辑层
 * Class MassMessageLimitLogic
 * @package app\admin\logic
 */
class MassMessageLimitLogic extends Logic
{
    /**
     * 获取群发信息限制配置列表
     * @return array
     */
    public static function getList()
    {
        $list = MassMessageLimit::getList();

        // 添加等级名称和计算分配数量
        foreach ($list as &$item) {
            $item['tier_name'] = MassMessageLimit::getTierLevelName($item['tier_level']);

            // 计算各等级分配数量
            $totalCount = $item['total_purchaser_count'] ?? 0;
            $item['level1_count'] = intval($totalCount * ($item['level1_percent'] ?? 0) / 100);
            $item['level2_count'] = intval($totalCount * ($item['level2_percent'] ?? 0) / 100);
            $item['level3_count'] = intval($totalCount * ($item['level3_percent'] ?? 0) / 100);
        }

        return $list;
    }

    /**
     * 获取表格数据（符合like.tableLists格式）
     * @param array $get 请求参数
     * @return array
     */
    public static function getListsForTable($get = [])
    {
        $list = self::getList();

        return [
            'count' => count($list),
            'lists' => $list
        ];
    }

    /**
     * 添加群发信息限制配置
     * @param array $data
     * @return bool
     */
    public static function add($data)
    {
        // 验证百分比总和
        if (!self::validatePercentages($data)) {
            self::$error = '各等级用户占比总和必须为100%';
            return false;
        }

        // 检查商家等级是否已存在
        if (MassMessageLimit::getConfigByTierLevel($data['tier_level'])) {
            self::$error = '该商家等级配置已存在';
            return false;
        }

        return MassMessageLimit::add($data);
    }

    /**
     * 编辑群发信息限制配置
     * @param array $data
     * @return bool
     */
    public static function edit($data)
    {
        // 验证百分比总和
        if (!self::validatePercentages($data)) {
            self::$error = '各等级用户占比总和必须为100%';
            return false;
        }

        return MassMessageLimit::edit($data);
    }

    /**
     * 删除群发信息限制配置
     * @param int $id
     * @return bool
     */
    public static function delete($id)
    {
        return Db::name('mass_message_limit')->where('id', $id)->delete();
    }

    /**
     * 获取单个群发信息限制配置信息
     * @param int $id
     * @return array
     */
    public static function getInfo($id)
    {
        $info = MassMessageLimit::getInfo($id);
        if ($info) {
            $info['tier_name'] = MassMessageLimit::getTierLevelName($info['tier_level']);

            // 计算各等级分配数量
            $totalCount = $info['total_purchaser_count'] ?? 0;
            $info['level1_count'] = intval($totalCount * ($info['level1_percent'] ?? 0) / 100);
            $info['level2_count'] = intval($totalCount * ($info['level2_percent'] ?? 0) / 100);
            $info['level3_count'] = intval($totalCount * ($info['level3_percent'] ?? 0) / 100);
        }

        return $info;
    }

    /**
     * 验证百分比总和是否为100
     * @param array $data
     * @return bool
     */
    private static function validatePercentages($data)
    {
        $total = ($data['level1_percent'] ?? 0) + ($data['level2_percent'] ?? 0) + ($data['level3_percent'] ?? 0);
        return abs($total - 100) < 0.01; // 允许小数点误差
    }

    /**
     * 获取商家等级选项
     * @return array
     */
    public static function getTierLevelOptions()
    {
        return [
            ['value' => 0, 'label' => '0元入驻'],
            ['value' => 1, 'label' => '商家会员'],
            ['value' => 2, 'label' => '实力厂商']
        ];
    }

    /**
     * 批量重新分配所有商家的采购人员
     * @return bool
     */
    public static function reAllocateAllShops()
    {
        return PurchaserAllocationLogic::checkAndReAllocate();
    }
}
