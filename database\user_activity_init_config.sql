-- 用户活跃度系统初始化配置
-- 插入默认配置项到ls_config表

-- 系统设置
INSERT INTO `ls_config` (`type`, `name`, `value`, `create_time`, `update_time`) VALUES
('user_activity', 'is_enabled', '1', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('user_activity', 'login_check_days', '7', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('user_activity', 'chat_daily_limit', '10', UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 积分规则
INSERT INTO `ls_config` (`type`, `name`, `value`, `create_time`, `update_time`) VALUES
('user_activity', 'purchaser_login_score', '10', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('user_activity', 'publish_demand_score', '20', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('user_activity', 'chat_score', '1', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('user_activity', 'purchase_score', '20', UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 等级设置
INSERT INTO `ls_config` (`type`, `name`, `value`, `create_time`, `update_time`) VALUES
('user_activity', 'level_1_score', '100', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('user_activity', 'level_2_score', '300', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('user_activity', 'level_3_score', '500', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('user_activity', 'level_4_score', '1000', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('user_activity', 'level_5_score', '2000', UNIX_TIMESTAMP(), UNIX_TIMESTAMP());
