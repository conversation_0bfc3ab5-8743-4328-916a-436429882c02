<?php



namespace app\shop\logic;


use app\common\basics\Logic;
use app\common\model\shop\ShopAuth;
use app\common\model\shop\ShopRole;
use app\common\model\shop\ShopRoleAuthIndex;
use think\facade\Db;

class RoleLogic extends Logic
{

    /**
     * Notes: 角色列表
     * <AUTHOR> 10:35)
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function lists($shop_id, $get)
    {
        $relationModel = new ShopRoleAuthIndex();
        $result = $relationModel->alias('r')
            ->join('dev_shop_auth m', 'r.menu_auth_id=m.id')
            ->where(['m.del' => 0])
            ->order(['sort' => 'desc'])
            ->field(['m.name' => 'name', 'r.role_id' => 'role_id'])
            ->select();
        $role_id_menu_auth_names = [];

        foreach ($result as $k => $v) {
            if (isset($role_id_menu_auth_names[$v['role_id']])) {
                $role_id_menu_auth_names[$v['role_id']] .= $v['name'] . ',';
            } else {
                $role_id_menu_auth_names[$v['role_id']] = $v['name'] . ',';
            }
        }

        $lists = ShopRole::where(['del' => 0, 'shop_id' => $shop_id])
            ->paginate([
                'list_rows'=> $get['limit'],
                'page'=> $get['page']
            ]);

        foreach ($lists as $k => $v) {
            $lists[$k]['auth_ids']=Db::name('shop_role_auth_index')->where(['role_id' => $v['id']])->column('menu_auth_id');
            $lists[$k]['auth_str'] = isset($role_id_menu_auth_names[$v['id']]) ? $role_id_menu_auth_names[$v['id']] : '';
            $lists[$k]['auth_str'] = rtrim($lists[$k]['auth_str'], ',');
        }

        return ['lists' => $lists->getCollection(), 'count' => $lists->total(),'more'=>is_more($lists->total(),$get['page'],$get['limit'])];
    }

    /**
     * Notes: 详情
     * @param $role_id
     * <AUTHOR> 10:35)
     * @return array|\think\Model|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function roleInfo($role_id)
    {
        return ShopRole::where(['id' => $role_id])->field(['id', 'name', 'desc'])->find();
    }


    /**
     * Notes: 添加
     * @param $post
     * <AUTHOR> 10:35)
     * @return bool
     */
    public static function addRole($shop_id, $post)
    {
        $data = [
            'name' => $post['name'],
            'desc' => $post['desc'],
            'shop_id' => $shop_id,
            'create_time' => time(),
        ];

        try {
            Db::startTrans();

            $roleModel = new ShopRole();
            $roleAuthIndexModel = new ShopRoleAuthIndex();

            $role_id = $roleModel->insertGetId($data);

            $data = [];
            $post['auth_ids'] = empty($post['auth_ids'])?[]:$post['auth_ids'];
            foreach ($post['auth_ids'] as $k => $v) {
                $data[] = [
                    'role_id' => $role_id,
                    'menu_auth_id' => $v,
                ];
            }
            $roleAuthIndexModel->insertAll($data);
            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            self::$error = $e->getMessage();
            return false;
        }
    }


    /**
     * Notes: 编辑
     * @param $post
     * <AUTHOR> 10:36)
     * @return bool
     */
    public static function editRole($shop_id, $post)
    {
        $data = [
            'name' => $post['name'],
            'desc' => $post['desc'],
            'update_time' => time(),
        ];
        try {
            Db::startTrans();

            $roleModel = new ShopRole();
            $roleAuthIndexModel = new ShopRoleAuthIndex();

            $roleModel->where(['del' => 0, 'id' => $post['id'], 'shop_id' => $shop_id])->update($data);
            $roleAuthIndexModel->where(['role_id' => $post['id']])->delete();

            $data = [];
            $post['auth_ids'] = empty($post['auth_ids'])?[]:$post['auth_ids'];
            foreach ($post['auth_ids'] as $k => $v) {
                $data[] = [
                    'role_id' => $post['id'],
                    'menu_auth_id' => $v,
                ];
            }
            $roleAuthIndexModel->insertAll($data);

            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            self::$error = $e->getMessage();
            return false;
        }
    }


    /**
     * Notes: 删除
     * @param $role_id
     * <AUTHOR> 10:36)
     * @return ShopRole
     */
    public static function delRole($shop_id, $role_id)
    {
        return ShopRole::where([
            'del' => 0,
            'id' => $role_id,
            'shop_id' => $shop_id
        ])->update(['del' => 1, 'update_time' => time()]);
    }


    /**
     * Notes: 获取菜单权限树
     * @param string $role_id
     * @param int $shop_tier_level 商家等级：0=0元入驻,1=商家会员,2=实力厂商
     * <AUTHOR> 10:36)
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function authTree($role_id = '', $shop_tier_level = 0)
    {
        $lists = ShopAuth::where(['disable' => 0, 'del' => 0])->select();

        // 根据商家类型过滤权限菜单
        $lists = self::filterAuthByMerchantType($lists, $shop_tier_level);

        $pids = ShopAuth::where(['disable' => 0, 'type' => 1, 'del' => 0])->column('pid');

        foreach ($lists as $k => $v) {
            $lists[$k]['spread'] = in_array($v['id'], $pids) ? true : false;
        }

        $menu_auth_ids = [];
        if ($role_id) {
            $menu_auth_ids = ShopRoleAuthIndex::where(['role_id' => $role_id])
                ->column('menu_auth_id');
        }

        return self::authListToTree($lists, 0, $menu_auth_ids);
    }


    /**
     * Notes: 列表结构转换成树形结构
     * @param $lists
     * @param int $pid
     * @param array $menu_auth_ids
     * <AUTHOR> 10:36)
     * @return array
     */
    public static function authListToTree($lists, $pid = 0, $menu_auth_ids = [])
    {
        $tree = [];
        foreach ($lists as $k => $v) {
            if ($v['pid'] == $pid) {
                $temp['id'] = $v['id'];
                $temp['field'] = 'auth_ids[' . $v['id'] . ']';
                $temp['title'] = $v['name'];
                $temp['children'] = self::authListToTree($lists, $v['id'], $menu_auth_ids);
                $temp['checked'] = in_array($v['id'], $menu_auth_ids) && empty($temp['children']) ? true : false;
                $temp['spread'] = $v['spread'];
                $tree[] = $temp;
            }
        }
        return $tree;
    }

    /**
     * Notes: 根据商家类型过滤权限菜单
     * @param array $lists 权限列表
     * @param int $shop_tier_level 商家等级
     * <AUTHOR>
     * @return array
     */
    private static function filterAuthByMerchantType($lists, $shop_tier_level)
    {
        $filtered_lists = [];

        foreach ($lists as $auth) {
            // 检查商家类型权限
            if (isset($auth['merchant_types']) && !empty($auth['merchant_types'])) {
                $allowed_types = explode(',', $auth['merchant_types']);
                // 如果当前商家类型不在允许的类型中，跳过此权限
                if (!in_array((string)$shop_tier_level, $allowed_types)) {
                    continue;
                }
            }

            $filtered_lists[] = $auth;
        }

        return $filtered_lists;
    }

}