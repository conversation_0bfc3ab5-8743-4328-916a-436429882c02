<?php
namespace app\common\listener\websocket;

use app\common\websocket\CombinedHandler;
use think\facade\Log;

class NotificationListener
{
    /**
     * @var CombinedHandler
     */
    protected $handler;

    public function __construct(CombinedHandler $handler)
    {
        $this->handler = $handler;
    }

    /**
     * 事件监听处理
     *
     * @return bool
     */
    public function handle($data): bool
    {
        if (is_string($data)) {
            $data = json_decode($data, true);
        }

        Log::info('收到Redis订阅消息: ' . json_encode($data));

        if (isset($data['event']) && $data['event'] === 'admin_notification' && isset($data['data'])) {
            $notificationData = $data['data'];
            $this->handler->sendNotificationToAdmin(
                $notificationData['title'],
                $notificationData['content'],
                $notificationData['type'] ?? 'admin_notification',
                $notificationData['url'] ?? '',
                $notificationData['icon'] ?? 0
            );
            return true;
        }
        
        return false;
    }
}