<?php

declare(strict_types=1);

namespace app\common\server;

use think\facade\Db;
use think\facade\Log;
use AlibabaCloud\SDK\Alinlp\V20200629\Alinlp;
use AlibabaCloud\SDK\Alinlp\V20200629\Models\GetWsChGeneralRequest;
use Darabonba\OpenApi\Models\Config;

class WordUpdateServer
{

    // API请求超时时间
    protected $timeout = 5;

    /**
     * 启动分词更新服务
     */
    public function start()
    {
        while (true) {
            try {
                // 获取一个待分词的商品
                $goods = Db::name('goods')
                    ->field('id, name, remark') // 添加 remark 字段
                    ->where('del', 0)
                    ->where('status', 1)
                    ->where('is_analyzed', 0)
                    ->order('id', 'asc')
                    ->find();

                if ($goods) {
                    $this->processGoods($goods);
                    // 每秒处理一个商品
                    sleep(1);
                } else {
                    // 没有待处理的商品，等待10秒再检查
                    Log::info('No goods to process, waiting for 10 seconds...');
                    sleep(10);
                }
            } catch (\Exception $e) {
                Log::error('Word update error: ' . $e->getMessage());
                sleep(5); // 出错时等待5秒再重试
            }
        }
    }

    /**
     * 处理单个商品
     * @param array $goods
     */
    protected function processGoods(array $goods)
    {
        Db::startTrans();
        try {
            // 调用分词API (合并name和remark进行分词)
            $textToAnalyze = $goods['name'] . (isset($goods['remark']) && !empty($goods['remark']) ? ' ' . $goods['remark'] : '');
            $keywords = $this->callSplitApi($textToAnalyze);

            // 保存分词结果
            $this->saveKeywords($goods['id'], $keywords);

            // 标记为已分词
            Db::name('goods')
                ->where('id', $goods['id'])
                ->update(['is_analyzed' => 1]);

            Db::commit();
            Log::info("Processed goods: {$goods['id']} - {$goods['name']}");
        } catch (\Exception $e) {
            Db::rollback();
            Log::error("Error processing goods {$goods['id']}: " . $e->getMessage());
            echo "Error: " . $e->getMessage() . "\n";
        }
    }

    /**
     * 调用分词API
     * @param string $text
     * @return array
     */
    protected function callSplitApi(string $text): array
    {
        $config                  = new Config();
        $config->accessKeyId     = 'LTAI5tPpqw9JoKAZKrY4k14Q';
        $config->accessKeySecret = '******************************';
        $config->regionId        = "cn-hangzhou";
        $client                  = new Alinlp($config);
        $request                 = new GetWsChGeneralRequest();
        $request->serviceCode    = 'alinlp';
        $request->text           = $text;

        try {
            $response = $client->GetWsChGeneral($request);
            $json_string = json_encode($response->body, JSON_UNESCAPED_UNICODE);
            $data = json_decode($json_string, true);
            $keywords = json_decode($data['data'], true);
            return $keywords['result'];
        } catch (\Exception $e) {
            Log::error("API call error: " . $e->getMessage());
            // 返回空数组，避免方法没有返回值
            return [];
        }
    }

    /**
     * 保存分词结果
     * @param int $goodsId
     * @param array $keywords
     */
    protected function saveKeywords(int $goodsId, array $keywords)
    {
        // 收集有效的分词结果
        $validWords = [];

        foreach ($keywords as $keyword) {
            if (empty(trim($keyword['word'])) || empty($keyword['tags'])) {
                continue;
            }

            // 添加到有效分词列表
            $validWords[] = $keyword['word'];

            // 保存到word表
            $wordId = Db::name('word')
                ->insertGetId([
                    'word' => $keyword['word'],
                    'tags' => implode(',',$keyword['tags']),
                    'goods_id' => $goodsId,
                    'created_at' => date('Y-m-d H:i:s')
                ]);

            // 保存关联关系到product_word表
            Db::name('product_word')->insert([
                'goods_id' => $goodsId,
                'word_id' => $wordId,
                'created_at' => date('Y-m-d H:i:s')
            ]);
        }

        // 将分词结果保存到goods表的split_word字段
        $splitWordStr = !empty($validWords) ? implode(',', $validWords) : '';
        Db::name('goods')
            ->where('id', $goodsId)
            ->update(['split_word' => $splitWordStr]);

        Log::info("Updated goods split_word: {$goodsId} - " . ($splitWordStr ?: '(empty)'));
    }
}