<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API通知测试工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: #fff;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"], textarea, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        textarea {
            height: 100px;
            resize: vertical;
        }
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        button:hover {
            background-color: #45a049;
        }
        .response {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            background-color: #f9f9f9;
            border-left: 4px solid #4CAF50;
            display: none;
        }
        .error {
            border-left-color: #f44336;
        }
        .log-container {
            margin-top: 20px;
            padding: 15px;
            background-color: #f9f9f9;
            border-radius: 4px;
            max-height: 300px;
            overflow-y: auto;
        }
        .log-entry {
            margin-bottom: 5px;
            padding: 5px;
            border-bottom: 1px solid #eee;
        }
        .log-entry.info {
            color: #0066cc;
        }
        .log-entry.success {
            color: #4CAF50;
        }
        .log-entry.error {
            color: #f44336;
        }
        .tabs {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 1px solid #ddd;
        }
        .tab {
            padding: 10px 20px;
            cursor: pointer;
            background-color: #f1f1f1;
            margin-right: 5px;
            border-radius: 4px 4px 0 0;
        }
        .tab.active {
            background-color: #4CAF50;
            color: white;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>API通知测试工具</h1>
        
        <div class="tabs">
            <div class="tab active" data-tab="admin-notification">管理员通知</div>
            <div class="tab" data-tab="websocket-api">WebSocket API</div>
            <div class="tab" data-tab="test-notification">测试通知</div>
        </div>
        
        <div class="tab-content active" id="admin-notification">
            <form id="admin-notification-form">
                <div class="form-group">
                    <label for="title">通知标题</label>
                    <input type="text" id="title" name="title" value="API测试通知" required>
                </div>
                
                <div class="form-group">
                    <label for="content">通知内容</label>
                    <textarea id="content" name="content" required>这是一条通过API发送的测试通知，请查收！</textarea>
                </div>
                
                <div class="form-group">
                    <label for="type">通知类型</label>
                    <select id="type" name="type">
                        <option value="admin_notification">管理员通知</option>
                        <option value="system">系统通知</option>
                        <option value="personal">个人通知</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="url">跳转URL（可选）</label>
                    <input type="text" id="url" name="url" placeholder="点击通知后跳转的URL">
                </div>
                
                <div class="form-group">
                    <label for="icon">通知图标</label>
                    <select id="icon" name="icon">
                        <option value="0">默认图标</option>
                        <option value="1">成功图标</option>
                        <option value="2">错误图标</option>
                        <option value="3">警告图标</option>
                        <option value="4">信息图标</option>
                    </select>
                </div>
                
                <button type="submit">发送通知</button>
            </form>
            
            <div class="response" id="admin-response"></div>
        </div>
        
        <div class="tab-content" id="websocket-api">
            <form id="websocket-api-form">
                <div class="form-group">
                    <label for="ws-title">通知标题</label>
                    <input type="text" id="ws-title" name="ws-title" value="WebSocket API测试通知" required>
                </div>
                
                <div class="form-group">
                    <label for="ws-content">通知内容</label>
                    <textarea id="ws-content" name="ws-content" required>这是一条通过WebSocket API发送的测试通知，请查收！</textarea>
                </div>
                
                <div class="form-group">
                    <label for="ws-type">通知类型</label>
                    <select id="ws-type" name="ws-type">
                        <option value="admin_notification">管理员通知</option>
                        <option value="system">系统通知</option>
                        <option value="personal">个人通知</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="ws-url">跳转URL（可选）</label>
                    <input type="text" id="ws-url" name="ws-url" placeholder="点击通知后跳转的URL">
                </div>
                
                <div class="form-group">
                    <label for="ws-icon">通知图标</label>
                    <select id="ws-icon" name="ws-icon">
                        <option value="0">默认图标</option>
                        <option value="1">成功图标</option>
                        <option value="2">错误图标</option>
                        <option value="3">警告图标</option>
                        <option value="4">信息图标</option>
                    </select>
                </div>
                
                <button type="submit">发送通知</button>
            </form>
            
            <div class="response" id="websocket-response"></div>
        </div>
        
        <div class="tab-content" id="test-notification">
            <form id="test-notification-form">
                <div class="form-group">
                    <label for="test-title">通知标题</label>
                    <input type="text" id="test-title" name="test-title" value="测试通知" required>
                </div>
                
                <div class="form-group">
                    <label for="test-content">通知内容</label>
                    <textarea id="test-content" name="test-content" required>这是一条测试通知，请查收！</textarea>
                </div>
                
                <div class="form-group">
                    <label for="test-type">通知类型</label>
                    <select id="test-type" name="test-type">
                        <option value="admin_notification">管理员通知</option>
                        <option value="system_notification">系统通知</option>
                        <option value="custom_notification">自定义通知</option>
                        <option value="error_notification">错误通知</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="test-url">跳转URL（可选）</label>
                    <input type="text" id="test-url" name="test-url" placeholder="点击通知后跳转的URL">
                </div>
                
                <div class="form-group">
                    <label for="test-icon">通知图标</label>
                    <select id="test-icon" name="test-icon">
                        <option value="0">默认图标</option>
                        <option value="1">成功图标</option>
                        <option value="2">错误图标</option>
                        <option value="3">警告图标</option>
                        <option value="4">信息图标</option>
                    </select>
                </div>
                
                <button type="submit">发送通知</button>
            </form>
            
            <div class="response" id="test-response"></div>
        </div>
        
        <div class="log-container">
            <h3>操作日志</h3>
            <div id="log"></div>
        </div>
    </div>
    
    <script>
        // 添加日志函数
        function addLog(message, type = 'info') {
            const logContainer = document.getElementById('log');
            const logEntry = document.createElement('div');
            logEntry.className = 'log-entry ' + type;
            logEntry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logContainer.insertBefore(logEntry, logContainer.firstChild);
        }
        
        // 切换标签页
        document.querySelectorAll('.tab').forEach(tab => {
            tab.addEventListener('click', function() {
                // 移除所有标签页的active类
                document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
                // 移除所有内容区的active类
                document.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));
                
                // 添加当前标签页的active类
                this.classList.add('active');
                // 显示对应的内容区
                const tabId = this.getAttribute('data-tab');
                document.getElementById(tabId).classList.add('active');
            });
        });
        
        // 显示响应信息
        function showResponse(element, message, isError) {
            element.textContent = message;
            element.style.display = 'block';
            
            if (isError) {
                element.classList.add('error');
            } else {
                element.classList.remove('error');
            }
        }
        
        // 管理员通知表单提交
        document.getElementById('admin-notification-form').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const title = document.getElementById('title').value;
            const content = document.getElementById('content').value;
            const type = document.getElementById('type').value;
            const url = document.getElementById('url').value;
            const icon = document.getElementById('icon').value;
            
            if (!title || !content) {
                showResponse(document.getElementById('admin-response'), '标题和内容不能为空', true);
                return;
            }
            
            addLog(`准备发送管理员通知: ${title}`);
            
            // 发送请求
            fetch('/api/notification/sendToAdmin', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    title: title,
                    content: content,
                    type: type,
                    url: url,
                    icon: parseInt(icon)
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 1) {
                    showResponse(document.getElementById('admin-response'), data.msg || '通知发送成功', false);
                    addLog(`通知发送成功: ${data.msg}`, 'success');
                } else {
                    showResponse(document.getElementById('admin-response'), data.msg || '通知发送失败', true);
                    addLog(`通知发送失败: ${data.msg}`, 'error');
                }
            })
            .catch(error => {
                showResponse(document.getElementById('admin-response'), '请求出错: ' + error.message, true);
                addLog(`请求出错: ${error.message}`, 'error');
            });
        });
        
        // WebSocket API表单提交
        document.getElementById('websocket-api-form').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const title = document.getElementById('ws-title').value;
            const content = document.getElementById('ws-content').value;
            const type = document.getElementById('ws-type').value;
            const url = document.getElementById('ws-url').value;
            const icon = document.getElementById('ws-icon').value;
            
            if (!title || !content) {
                showResponse(document.getElementById('websocket-response'), '标题和内容不能为空', true);
                return;
            }
            
            addLog(`准备发送WebSocket API通知: ${title}`);
            
            // 发送请求
            fetch('/api/websocket/sendAdminNotification', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    title: title,
                    content: content,
                    type: type,
                    url: url,
                    icon: parseInt(icon)
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 1) {
                    showResponse(document.getElementById('websocket-response'), data.msg || '通知发送成功', false);
                    addLog(`通知发送成功: ${data.msg}`, 'success');
                } else {
                    showResponse(document.getElementById('websocket-response'), data.msg || '通知发送失败', true);
                    addLog(`通知发送失败: ${data.msg}`, 'error');
                }
            })
            .catch(error => {
                showResponse(document.getElementById('websocket-response'), '请求出错: ' + error.message, true);
                addLog(`请求出错: ${error.message}`, 'error');
            });
        });
        
        // 测试通知表单提交
        document.getElementById('test-notification-form').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const title = document.getElementById('test-title').value;
            const content = document.getElementById('test-content').value;
            const type = document.getElementById('test-type').value;
            const url = document.getElementById('test-url').value;
            const icon = document.getElementById('test-icon').value;
            
            if (!title || !content) {
                showResponse(document.getElementById('test-response'), '标题和内容不能为空', true);
                return;
            }
            
            addLog(`准备发送测试通知: ${title}`);
            
            // 发送请求
            fetch(`/api/notification/testNotification?title=${encodeURIComponent(title)}&content=${encodeURIComponent(content)}&type=${encodeURIComponent(type)}&url=${encodeURIComponent(url)}&icon=${icon}`, {
                method: 'GET'
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 1) {
                    showResponse(document.getElementById('test-response'), data.msg || '通知发送成功', false);
                    addLog(`通知发送成功: ${data.msg}`, 'success');
                } else {
                    showResponse(document.getElementById('test-response'), data.msg || '通知发送失败', true);
                    addLog(`通知发送失败: ${data.msg}`, 'error');
                }
            })
            .catch(error => {
                showResponse(document.getElementById('test-response'), '请求出错: ' + error.message, true);
                addLog(`请求出错: ${error.message}`, 'error');
            });
        });
        
        // 页面加载完成
        window.addEventListener('load', function() {
            addLog('页面加载完成，可以开始测试通知功能');
        });
    </script>
</body>
</html>
