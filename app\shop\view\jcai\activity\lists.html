{layout name="layout1" /}

<div class="wrapper">
    <div class="layui-card">
        <!-- 操作提示 -->
        <div class="layui-card-body">
            <div class="layui-collapse" style="border:1px dashed #c4c4c4">
                <div class="layui-colla-item">
                    <h2 class="layui-colla-title like-layui-colla-title">操作提示</h2>
                    <div class="layui-colla-content layui-show">
                        <p>*商家提交需要参与集采众筹活动的商品，平台审核通过之后即可参与集采众筹活动。</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 搜索区域 -->
        <div class="layui-card-body layui-form">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label for="name" class="layui-form-label">商品名称：</label>
                    <div class="layui-inline" style="margin-right:0;">
                        <div class="layui-input-inline" >
                            <input type="text" id="name" name="name" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                </div>
                <div class="layui-inline">
                    <label for="status" class="layui-form-label">活动状态：</label>
                    <div class="layui-inline" style="margin-right:0;">
                        <div class="layui-input-inline" >
                            <select id="status" name="status">
                                <option value="">全选</option>
                                <option value="1">活动中</option>
                                <option value="0">已停止</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="layui-inline">
                    <label for="datetime" class="layui-form-label">活动时间：</label>
                    <div class="layui-inline" style="margin-right:0;">
                        <div class="layui-input-inline" >
                            <input type="text" id="datetime" name="datetime" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                </div>
                <div class="layui-inline">
                    <a class="layui-btn layui-btn-sm layui-btn-normal" lay-submit lay-filter="search">搜索</a>
                    <a class="layui-btn layui-btn-sm layui-btn-primary" lay-submit lay-filter="clear-search">重置</a>
                </div>
            </div>
        </div>

        <!-- 主体区域 -->
        <div class="layui-card-body">
            <div class="layui-tab layui-tab-card" lay-filter="like-tab">
                <ul class="layui-tab-title">
                    <li lay-id="0" class="layui-this">全部商品({$statistics.total})</li>
                    <li lay-id="1">待审核商品({$statistics.stayAudit})</li>
                    <li lay-id="2">审核通过商品({$statistics.adoptAudit})</li>
                    <li lay-id="3">审核拒绝商品({$statistics.refuseAudit})</li>
                </ul>
                <div class="layui-tab-content" style="padding:20px;">
                    <button type="button" class="layui-btn layui-btn-normal layui-btn-sm layEvent" lay-event="add">新增集采众筹商品</button>
                    <table id="like-table-lists" lay-filter="like-table-lists"></table>
                    <script type="text/html" id="table-goods">
                        <img src="{{d.goods.image}}" alt="图" style="width:50px;height:50px;">
                        <div class="layui-inline">{{d.goods.name}}</div>
                    </script>
                    <script type="text/html" id="table-teamAmount">
                       {{d.team_min_price}} ~ {{d.team_max_price}}
                    </script>
                    <script type="text/html" id="table-teamData">
                        <p>集采众筹数量：{{d.team_count}}个</p>
                        <p>集采数量：{{d.success_found}}个</p>
                        <p>集采人数：{{d.join_found}}人</p>
                    </script>
                    <script type="text/html" id="table-teamTime">
                        {{d.activity_start_time}} ~ {{d.activity_end_time}}
                    </script>
                    <script type="text/html" id="table-operation">
                        <a class="layui-btn layui-btn-sm layui-btn-primary" lay-event="detail">详情</a>
                        <a class="layui-btn layui-btn-sm layui-btn-primary" lay-event="record">集采众筹记录</a>
                        <a class="layui-btn layui-btn-sm layui-btn-normal" lay-event="edit">编辑</a>
                        <br/>
                        {{#  if(d.status == 1  && (d.audit == 0 || d.audit == 1)){ }}
                            <a class="layui-btn layui-btn-sm layui-btn-warm" lay-event="stop" style="margin-top:5px;">停止活动</a>
                        {{#  } }}
                        {{#  if(d.status == 0 && d.audit == 1){ }}
                            <a class="layui-btn layui-btn-sm layui-btn-normal" lay-event="open" style="margin-top:5px;">启动活动</a>
                        {{#  } }}
                        <a class="layui-btn layui-btn-sm layui-btn-danger" lay-event="del" style="margin-top:5px;">移除商品</a>
                    </script>
                </div>
            </div>
        </div>

    </div>
</div>

<script>
    layui.use(["table", "form", "element", "laydate"], function(){
        var table   = layui.table;
        var form    = layui.form;
        var element = layui.element;
        var laydate = layui.laydate;

        laydate.render({elem:"#datetime", range: true, trigger:"click"});

        like.tableLists("#like-table-lists", "{:url()}", [
            {field:"goodsInfo", width:250, title:"商品", templet:"#table-goods"}
            ,{field:"teamAmount", width:180, align:"center",title:"集采众筹价", templet:"#table-teamAmount"}
            ,{field:"teamData", width:180, title:"集采众筹数据", templet:"#table-teamData"}
            ,{field:"activityTime", width:280, align:"center", title:"活动时间", templet:"#table-teamTime"}
            ,{field:"status_text", width:90, align:"center", title:"活动状态"}
            ,{field:"audit_text", width:90, align:"center", title:"审核状态"}
            ,{field:"explain", width:200, align:"center", title:"审核说明"}
            ,{title:"操作", width:250, align:"center", fixed:"right", toolbar:"#table-operation"}
        ]);


        var active = {
            add: function() {
                layer.open({
                    type: 2
                    ,title: "新增集采众筹商品"
                    ,content: "{:url('jcai.Activity/add')}"
                    ,area: ["80%", "90%"]
                    ,btn: ["确定", "取消"]
                    ,yes: function(index, layero){
                        var iframeWindow = window["layui-layer-iframe" + index];
                        var submit = layero.find("iframe").contents().find("#addSubmit");
                        iframeWindow.layui.form.on("submit(addSubmit)", function(data){
                            like.ajax({
                                url: "{:url('jcai.Activity/add')}",
                                data: data.field,
                                type: "POST",
                                success:function(res) {
                                    if(res.code === 1) {
                                        layui.layer.msg(res.msg);
                                        layer.close(index);
                                        active.statistics();
                                        var type = $(".layui-tab-title li.layui-this").attr("lay-id");
                                        table.reload("like-table-lists", {
                                            where: {type: type},
                                            page: { cur: 1 }
                                        });
                                    }
                                }
                            });
                        });
                        submit.trigger("click");
                    }
                });
            },
            edit: function(obj) {
                if (obj.data.status !== 0 && obj.data.audit !== 2) {
                    layui.layer.msg("请停止活动,再编辑");
                    return false;
                }
                layer.open({
                    type: 2
                    ,title: "编辑集采众筹商品"
                    ,content: "{:url('jcai.Activity/edit')}?id="+obj.data.id
                    ,area: ["80%", "90%"]
                    ,btn: ["确定", "取消"]
                    ,yes: function(index, layero){
                        var iframeWindow = window["layui-layer-iframe" + index];
                        var submit = layero.find("iframe").contents().find("#addSubmit");
                        iframeWindow.layui.form.on("submit(addSubmit)", function(data){
                            data.field['id'] = obj.data.id;
                            like.ajax({
                                url: "{:url('jcai.Activity/edit')}",
                                data: data.field,
                                type: "POST",
                                success:function(res) {
                                    if(res.code === 1) {
                                        layui.layer.msg(res.msg);
                                        layer.close(index);
                                        active.statistics();
                                        var type = $(".layui-tab-title li.layui-this").attr("lay-id");
                                        table.reload("like-table-lists", {
                                            where: {type: type},
                                            page: { cur: 1 }
                                        });
                                    }
                                }
                            });
                        });
                        submit.trigger("click");
                    }
                });
            },
            detail: function (obj) {
                layer.open({
                    type: 2
                    ,title: "集采众筹商品详情"
                    ,content: "{:url('jcai.Activity/detail')}?id="+obj.data.id
                    ,area: ["60%", "80%"]
                });
            },
            record: function (obj) {
                layer.open({
                    type: 2
                    ,title: "集采众筹记录"
                    ,content: "{:url('jcai.Found/lists')}?team_activity_id="+obj.data.id
                    ,area: ["90%", "90%"]
                });
            },
            stop: function (obj) {
                layer.confirm("确定停止活动吗？", function(index) {
                    like.ajax({
                        url: "{:url('jcai.Activity/stop')}",
                        data: {id: obj.data.id},
                        type: "POST",
                        success: function (res) {
                            if (res.code === 1) {
                                layui.layer.msg(res.msg);
                                layer.close(index);
                                var type = $(".layui-tab-title li.layui-this").attr("lay-id");
                                table.reload('like-table-lists', {
                                    where: {type: type},
                                    page: {cur: 1}
                                })
                            }
                        }
                    });
                    layer.close(index);
                })
            },
            open: function (obj) {
                layer.confirm("确定启动活动吗？", function(index) {
                    like.ajax({
                        url: "{:url('jcai.Activity/open')}",
                        data: {id: obj.data.id},
                        type: "POST",
                        success: function (res) {
                            if (res.code === 1) {
                                layui.layer.msg(res.msg);
                                layer.close(index);
                                var type = $(".layui-tab-title li.layui-this").attr("lay-id");
                                table.reload('like-table-lists', {
                                    where: {type: type},
                                    page: {cur: 1}
                                })
                            }
                        }
                    });
                    layer.close(index);
                })
            },
            del: function(obj) {
                if (obj.data.status !== 0 && obj.data.audit !== 2) {
                    layui.layer.msg("请停止活动,再删除");
                    return false;
                }
                layer.confirm("确定删除活动吗？", function(index) {
                    like.ajax({
                        url: "{:url('jcai.Activity/del')}",
                        data: {id: obj.data.id},
                        type: "POST",
                        success: function (res) {
                            if (res.code === 1) {
                                active.statistics();
                                layui.layer.msg(res.msg);
                                layer.close(index);
                                obj.del();
                            }
                        }
                    });
                    layer.close(index);
                })
            },
            statistics: function () {
                like.ajax({
                    url: "{:url('jcai.Activity/statistics')}",
                    data: {},
                    type: "GET",
                    success:function(res) {
                        if(res.code === 1) {
                            $(".layui-tab-title li[lay-id=0]").html("全部商品("+res.data.total+")");
                            $(".layui-tab-title li[lay-id=1]").html("待审核商品("+res.data.stayAudit+")");
                            $(".layui-tab-title li[lay-id=2]").html("审核通过商品("+res.data.adoptAudit+")");
                            $(".layui-tab-title li[lay-id=3]").html("审核拒绝商品("+res.data.refuseAudit+")");
                        }
                    }
                });
            }
        };
        like.eventClick(active);


        element.on("tab(like-tab)", function(){
            var type = this.getAttribute("lay-id");
            active.statistics();
            table.reload("like-table-lists", {
                where: {type: type},
                page: {
                    curr: 1
                }
            });
        });


        form.on("submit(search)", function(data){
            table.reload("like-table-lists", {
                where: data.field,
                page: {
                    curr: 1
                }
            });
        });


        form.on("submit(clear-search)", function(){
            $("#name").val("");
            $("#datetime").val("");
            $("#status").val("");
            form.render("select");
            table.reload("like-table-lists", {
                where: {},
                page: {
                    curr: 1
                }
            });
        });


    })
</script>