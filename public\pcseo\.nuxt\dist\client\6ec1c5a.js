(window.webpackJsonp=window.webpackJsonp||[]).push([[52,9,11,18],{437:function(e,t,r){"use strict";var n=r(17),o=r(2),d=r(3),l=r(136),c=r(27),v=r(18),f=r(271),m=r(52),h=r(135),_=r(270),x=r(5),y=r(98).f,C=r(44).f,w=r(26).f,D=r(438),S=r(439).trim,k="Number",T=o.Number,O=T.prototype,N=o.TypeError,E=d("".slice),F=d("".charCodeAt),$=function(e){var t=_(e,"number");return"bigint"==typeof t?t:I(t)},I=function(e){var t,r,n,o,d,l,c,code,v=_(e,"number");if(h(v))throw N("Cannot convert a Symbol value to a number");if("string"==typeof v&&v.length>2)if(v=S(v),43===(t=F(v,0))||45===t){if(88===(r=F(v,2))||120===r)return NaN}else if(48===t){switch(F(v,1)){case 66:case 98:n=2,o=49;break;case 79:case 111:n=8,o=55;break;default:return+v}for(l=(d=E(v,2)).length,c=0;c<l;c++)if((code=F(d,c))<48||code>o)return NaN;return parseInt(d,n)}return+v};if(l(k,!T(" 0o1")||!T("0b1")||T("+0x1"))){for(var j,z=function(e){var t=arguments.length<1?0:T($(e)),r=this;return m(O,r)&&x((function(){D(r)}))?f(Object(t),r,z):t},M=n?y(T):"MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,EPSILON,MAX_SAFE_INTEGER,MIN_SAFE_INTEGER,isFinite,isInteger,isNaN,isSafeInteger,parseFloat,parseInt,fromString,range".split(","),R=0;M.length>R;R++)v(T,j=M[R])&&!v(z,j)&&w(z,j,C(T,j));z.prototype=O,O.constructor=z,c(o,k,z)}},438:function(e,t,r){var n=r(3);e.exports=n(1..valueOf)},439:function(e,t,r){var n=r(3),o=r(33),d=r(16),l=r(440),c=n("".replace),v="["+l+"]",f=RegExp("^"+v+v+"*"),m=RegExp(v+v+"*$"),h=function(e){return function(t){var r=d(o(t));return 1&e&&(r=c(r,f,"")),2&e&&(r=c(r,m,"")),r}};e.exports={start:h(1),end:h(2),trim:h(3)}},440:function(e,t){e.exports="\t\n\v\f\r                　\u2028\u2029\ufeff"},441:function(e,t,r){var content=r(444);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[e.i,content,""]]),content.locals&&(e.exports=content.locals);(0,r(14).default)("3181fc86",content,!0,{sourceMap:!1})},442:function(e,t,r){"use strict";r.r(t);r(437),r(80),r(272);var n={data:function(){return{priceSlice:{}}},components:{},props:{firstSize:{type:Number,default:14},secondSize:{type:Number,default:14},color:{type:String},weight:{type:[String,Number],default:400},price:{type:[String,Number],default:""},showSubscript:{type:Boolean,default:!0},subscriptSize:{type:Number,default:14},lineThrough:{type:Boolean,default:!1}},created:function(){this.priceFormat()},watch:{price:function(e){this.priceFormat()}},methods:{priceFormat:function(){var e=this.price,t={};null!==e&&(e=parseFloat(e),e=String(e).split("."),t.first=e[0],t.second=e[1],this.priceSlice=t)}}},o=(r(443),r(9)),component=Object(o.a)(n,(function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("span",{class:(e.lineThrough?"line-through":"")+"price-format",style:{color:e.color,"font-weight":e.weight}},[e.showSubscript?r("span",{style:{"font-size":e.subscriptSize+"px","margin-right":"1px"}},[e._v("¥")]):e._e(),e._v(" "),r("span",{style:{"font-size":e.firstSize+"px","margin-right":"1px"}},[e._v(e._s(e.priceSlice.first))]),e._v(" "),e.priceSlice.second?r("span",{style:{"font-size":e.secondSize+"px"}},[e._v("."+e._s(e.priceSlice.second))]):e._e()])}),[],!1,null,null,null);t.default=component.exports},443:function(e,t,r){"use strict";r(441)},444:function(e,t,r){var n=r(13)(!1);n.push([e.i,".price-format{display:flex;align-items:baseline}",""]),e.exports=n},449:function(e,t,r){"use strict";r.r(t);r(437),r(81),r(61),r(24),r(100),r(80),r(99);var n=6e4,o=36e5,d=24*o;function l(e){return(0+e.toString()).slice(-2)}var c={components:{},props:{isSlot:{type:Boolean,default:!1},time:{type:Number,default:0},format:{type:String,default:"hh:mm:ss"},autoStart:{type:Boolean,default:!0}},watch:{time:{immediate:!0,handler:function(e){e&&this.reset()}}},data:function(){return{timeObj:{},formateTime:0}},created:function(){},computed:{},methods:{createTimer:function(e){return setTimeout(e,100)},isSameSecond:function(e,t){return Math.floor(e)===Math.floor(t)},start:function(){this.counting||(this.counting=!0,this.endTime=Date.now()+1e3*this.remain,this.setTimer())},setTimer:function(){var e=this;this.tid=this.createTimer((function(){var t=e.getRemain();e.isSameSecond(t,e.remain)&&0!==t||e.setRemain(t),0!==e.remain&&e.setTimer()}))},getRemain:function(){return Math.max(this.endTime-Date.now(),0)},pause:function(){this.counting=!1,clearTimeout(this.tid)},reset:function(){this.pause(),this.remain=this.time,this.setRemain(this.remain),this.autoStart&&this.start()},setRemain:function(e){var t=this.format;this.remain=e;var time,r=(time=e,{days:Math.floor(time/d),hours:l(Math.floor(time%d/o)),minutes:l(Math.floor(time%o/n)),seconds:l(Math.floor(time%n/1e3))});this.formateTime=function(e,t){var r=t.days,n=t.hours,o=t.minutes,d=t.seconds;return-1!==e.indexOf("dd")&&(e=e.replace("dd",r)),-1!==e.indexOf("hh")&&(e=e.replace("hh",l(n))),-1!==e.indexOf("mm")&&(e=e.replace("mm",l(o))),-1!==e.indexOf("ss")&&(e=e.replace("ss",l(d))),e}(t,r),this.$emit("change",r),0===e&&(this.pause(),this.$emit("finish"))}}},v=r(9),component=Object(v.a)(c,(function(){var e=this,t=e.$createElement,r=e._self._c||t;return e.time>=0?r("div",[r("client-only",[e.isSlot?e._t("default"):r("span",[e._v(e._s(e.formateTime))])],2)],1):e._e()}),[],!1,null,null,null);t.default=component.exports},450:function(e,t,r){"use strict";r.d(t,"b",(function(){return o})),r.d(t,"a",(function(){return d}));var n=r(34);r(80),r(272),r(101),r(61),r(24),r(38),r(62),r(45),r(19),r(63),r(64),r(46);var o=function(e){var time=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1e3,t=arguments.length>2?arguments[2]:void 0,r=new Date(0).getTime();return function(){var n=(new Date).getTime();if(n-r>time){for(var o=arguments.length,d=new Array(o),l=0;l<o;l++)d[l]=arguments[l];e.apply(t,d),r=n}}};function d(e){var p="";if("object"==Object(n.a)(e)){for(var t in p="?",e)p+="".concat(t,"=").concat(e[t],"&");p=p.slice(0,-1)}return p}},475:function(e,t,r){var content=r(493);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[e.i,content,""]]),content.locals&&(e.exports=content.locals);(0,r(14).default)("db2946c2",content,!0,{sourceMap:!1})},492:function(e,t,r){"use strict";r(475)},493:function(e,t,r){var n=r(13)(!1);n.push([e.i,".deliver-search-container .deliver-box .deliver-recode-box[data-v-79dec466]{padding:10px 20px;background-color:#f2f2f2}.deliver-search-container .deliver-box .deliver-recode-box .recode-img[data-v-79dec466]{position:relative;width:72px;height:72px}.deliver-search-container .deliver-box .deliver-recode-box .recode-img .float-count[data-v-79dec466]{position:absolute;bottom:0;height:20px;width:100%;background-color:rgba(0,0,0,.5);color:#fff;font-size:12px}.deliver-search-container .deliver-box .deliver-recode-box .recode-info-container[data-v-79dec466]{flex:1}.deliver-search-container .deliver-box .deliver-recode-box .recode-info-container .recode-label[data-v-79dec466]{width:70px}.deliver-search-container .deliver-box .deliver-recode-box .recode-info-container .copy-btn[data-v-79dec466]{height:20px;min-width:42px;border:1px solid #ff2c3c;font-size:12px;margin-left:8px;border-radius:60px;cursor:pointer}.deliver-search-container .deliver-box .deliver-recode-box .recode-info-container .copy-btn[data-v-79dec466]:hover{background-color:#fff}.deliver-search-container .deliver-box .deliver-flow-box[data-v-79dec466]{padding-left:15px}.deliver-search-container .deliver-box .time-line-title[data-v-79dec466]{font-weight:500px;font-size:16px;margin-bottom:10px}",""]),e.exports=n},502:function(e,t,r){"use strict";r.r(t);var n=r(6),o=(r(51),r(437),{props:{value:{type:Boolean,default:!1},aid:{type:Number|String}},data:function(){return{showDialog:!1,deliverBuy:{},delivery:{},deliverFinish:{},deliverOrder:{},deliverShipment:{},deliverTake:{},timeLineArray:[]}},watch:{value:function(e){console.log(e,"val"),this.showDialog=e},showDialog:function(e){e&&this.aid&&(this.timeLineArray=[],this.getDeliverTraces()),this.$emit("input",e)}},methods:{getDeliverTraces:function(){var e=this;return Object(n.a)(regeneratorRuntime.mark((function t(){var data,r,n,o,d,l,c,v,f;return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return data={id:e.aid},t.next=3,e.$get("order/orderTraces",{params:data});case 3:1==(r=t.sent).code&&(n=r.data,o=n.buy,d=n.delivery,l=n.finish,c=n.order,v=n.shipment,f=n.take,e.deliverBuy=o,e.delivery=d,e.deliverFinish=l,e.deliverOrder=c,e.deliverShipment=v,e.deliverTake=f,e.timeLineArray.push(e.deliverFinish),e.timeLineArray.push(e.delivery),e.timeLineArray.push(e.deliverShipment),e.timeLineArray.push(e.deliverBuy),console.log(e.timeLineArray));case 5:case"end":return t.stop()}}),t)})))()},onCopy:function(){var e=document.createElement("input");e.value=this.deliverOrder.invoice_no,document.body.appendChild(e),e.select(),document.execCommand("Copy"),this.$message.success("复制成功"),e.remove()}}}),d=(r(492),r(9)),component=Object(d.a)(o,(function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"deliver-search-container"},[r("el-dialog",{attrs:{visible:e.showDialog,top:"30vh",width:"900px",title:"物流查询"},on:{"update:visible":function(t){e.showDialog=t}}},[r("div",{staticClass:"deliver-box"},[r("div",{staticClass:"deliver-recode-box flex"},[r("div",{staticClass:"recode-img"},[r("el-image",{staticStyle:{width:"100%",height:"100%"},attrs:{fit:"cover",src:e.deliverOrder.image}}),e._v(" "),r("div",{staticClass:"float-count flex row-center"},[e._v("共"+e._s(e.deliverOrder.count)+"件商品")])],1),e._v(" "),r("div",{staticClass:"recode-info-container m-l-10"},[r("div",{staticClass:"flex"},[r("div",{staticClass:"recode-label"},[e._v("物流状态：")]),e._v(" "),r("div",{staticClass:"primary lg",staticStyle:{"font-weight":"500"}},[e._v(e._s(e.deliverOrder.tips))])]),e._v(" "),r("div",{staticClass:"flex",staticStyle:{margin:"6px 0"}},[r("div",{staticClass:"recode-label"},[e._v("快递公司：")]),e._v(" "),r("div",[e._v(e._s(e.deliverOrder.shipping_name))])]),e._v(" "),r("div",{staticClass:"flex"},[r("div",{staticClass:"recode-label"},[e._v("快递单号：")]),e._v(" "),r("div",[e._v(e._s(e.deliverOrder.invoice_no))]),e._v(" "),r("div",{staticClass:"copy-btn primary flex row-center",on:{click:e.onCopy}},[e._v("复制")])])])]),e._v(" "),r("div",{staticClass:"deliver-flow-box m-t-16"},[r("el-timeline",[e.deliverFinish.tips?r("el-timeline-item",[r("div",[r("div",{staticClass:"flex lg"},[r("div",{staticClass:"m-r-8",staticStyle:{"font-weight":"500"}},[e._v("\n                                    "+e._s(e.deliverTake.contacts)+"\n                                ")]),e._v(" "),r("div",{staticStyle:{"font-weight":"500"}},[e._v(e._s(e.deliverTake.mobile))])]),e._v(" "),r("div",{staticClass:"lighter m-t-8"},[e._v(e._s(e.deliverTake.address))])])]):e._e(),e._v(" "),e.deliverFinish.tips?r("el-timeline-item",{attrs:{timestamp:e.deliverFinish.time}},[r("div",{staticClass:"time-line-title"},[e._v(e._s(e.deliverFinish.title))]),e._v(" "),r("div",[e._v(e._s(e.deliverFinish.tips))])]):e._e(),e._v(" "),e.delivery.traces&&e.delivery.traces.length?r("el-timeline-item",{attrs:{timestamp:e.delivery.time}},[r("div",{staticClass:"time-line-title m-b-8"},[e._v(e._s(e.delivery.title))]),e._v(" "),e._l(e.delivery.traces,(function(t,n){return r("el-timeline-item",{key:n,attrs:{timestamp:t[0]}},[r("div",{staticClass:"muted"},[e._v(e._s(t[1]))])])}))],2):e._e(),e._v(" "),e.deliverShipment.tips?r("el-timeline-item",{attrs:{timestamp:e.deliverShipment.time}},[r("div",{staticClass:"time-line-title"},[e._v(e._s(e.deliverShipment.title))]),e._v(" "),r("div",[e._v(e._s(e.deliverShipment.tips))])]):e._e(),e._v(" "),e.deliverBuy.tips?r("el-timeline-item",{attrs:{timestamp:e.deliverBuy.time}},[r("div",{staticClass:"time-line-title"},[e._v(e._s(e.deliverBuy.title))]),e._v(" "),r("div",[e._v(e._s(e.deliverBuy.tips))])]):e._e()],1)],1)])])],1)}),[],!1,null,"79dec466",null);t.default=component.exports},552:function(e,t,r){var content=r(639);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[e.i,content,""]]),content.locals&&(e.exports=content.locals);(0,r(14).default)("0031dfb8",content,!0,{sourceMap:!1})},638:function(e,t,r){"use strict";r(552)},639:function(e,t,r){var n=r(13)(!1);n.push([e.i,".order-detail[data-v-295bca44]{padding:0 16px 20px}.order-detail .detail-hd[data-v-295bca44]{padding:14px 5px;border-bottom:1px solid #e5e5e5}.order-detail .address[data-v-295bca44]{padding:16px 0}.order-detail .address>div[data-v-295bca44]{margin-bottom:10px}.order-detail .address-item[data-v-295bca44]{display:flex}.order-detail .address-item-label[data-v-295bca44]{width:70px;text-align:justify;-moz-text-align-last:justify;text-align-last:justify}.order-detail .detail-con .title[data-v-295bca44]{height:40px;background:#f2f2f2;border:1px solid #e5e5e5;padding:0 20px}.order-detail .detail-con .goods .goods-hd[data-v-295bca44],.order-detail .detail-con .goods .goods-list[data-v-295bca44]{padding:10px 20px;border:1px solid #e5e5e5;border-top:0 solid #e5e5e5}.order-detail .detail-con .goods .goods-hd .goods-item[data-v-295bca44],.order-detail .detail-con .goods .goods-list .goods-item[data-v-295bca44]{padding:10px 0}.order-detail .detail-con .goods .goods-hd .goods-item .goods-name[data-v-295bca44],.order-detail .detail-con .goods .goods-list .goods-item .goods-name[data-v-295bca44]{line-height:1.5}.order-detail .detail-con .goods .info .goods-img[data-v-295bca44]{width:72px;height:72px;margin-right:10px}.order-detail .detail-con .goods .num[data-v-295bca44],.order-detail .detail-con .goods .price[data-v-295bca44],.order-detail .detail-con .goods .total[data-v-295bca44]{width:150px}.order-detail .detail-footer[data-v-295bca44]{padding:25px 20px;justify-content:flex-end}.order-detail .detail-footer .money>div[data-v-295bca44]{text-align:right}.order-detail .detail-footer .money>div[data-v-295bca44]:first-of-type{width:80px}.order-detail .detail-footer .money>div[data-v-295bca44]:last-of-type{width:120px;display:flex;justify-content:flex-end}.order-detail .detail-footer .oprate-btn .btn[data-v-295bca44]{width:152px;height:44px;cursor:pointer;border-radius:2px}.order-detail .detail-footer .oprate-btn .btn.plain[data-v-295bca44],.order-detail .qr-container[data-v-295bca44]{border:1px solid hsla(0,0%,89.8%,.89804)}.order-detail .qr-container[data-v-295bca44]{width:120px;height:120px;padding:6px;border-radius:6px}",""]),e.exports=n},680:function(e,t,r){"use strict";r.r(t);var n=r(6),o=(r(81),r(51),r(450),{head:function(){return{title:this.$store.getters.headTitle,link:[{rel:"icon",type:"image/x-icon",href:this.$store.getters.favicon}]}},layout:"user",asyncData:function(e){return Object(n.a)(regeneratorRuntime.mark((function t(){var r,n,o,data;return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return r=e.$get,n=e.query,t.next=3,r("order/getOrderDetail",{params:{id:n.id}});case 3:if(o=t.sent,data=o.data,1!=o.code){t.next=8;break}return t.abrupt("return",{orderDetail:data,id:n.id});case 8:case"end":return t.stop()}}),t)})))()},data:function(){return{orderDetail:{},showDeliverPop:!1}},mounted:function(){2===this.orderDetail.delivery_type&&this.creatQrCode(this.orderDetail.pickup_code)},methods:{getOrderDetail:function(){var e=this;return Object(n.a)(regeneratorRuntime.mark((function t(){var r,data;return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,e.$get("order/getOrderDetail",{params:{id:e.id}});case 2:r=t.sent,data=r.data,1==r.code&&(e.orderDetail=data);case 6:case"end":return t.stop()}}),t)})))()},handleOrder:function(e){var t=this;this.type=e,this.$confirm(this.getTipsText(e),{title:"温馨提示",center:!0,confirmButtonText:"确定",cancelButtonText:"取消",width:"300px",callback:function(e){"confirm"==e&&t.postOrder()}})},postOrder:function(){var e=this;return Object(n.a)(regeneratorRuntime.mark((function t(){var r,n,o,d,code,l;return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:r=e.type,n=e.id,o="",t.t0=r,t.next=0===t.t0?5:1===t.t0?7:2===t.t0?9:11;break;case 5:return o="order/cancel",t.abrupt("break",11);case 7:return o="order/del",t.abrupt("break",11);case 9:return o="order/confirm",t.abrupt("break",11);case 11:return t.next=13,e.$post(o,{id:n});case 13:d=t.sent,code=d.code,d.data,l=d.msg,1==code&&(e.$message({message:l,type:"success"}),1==r?setTimeout((function(){e.$router.go(-1)}),1500):e.getOrderDetail());case 18:case"end":return t.stop()}}),t)})))()},getTipsText:function(e){switch(e){case 0:return"确认取消订单吗？";case 1:return"确认删除订单吗?";case 2:return"确认收货吗?"}}},computed:{getOrderStatus:function(){return function(e){var text="";switch(e){case 0:text="待支付";break;case 1:text="待发货";break;case 2:text="待收货";break;case 3:text="已完成";break;case 4:text="订单已关闭"}return text}},getCancelTime:function(){return function(time){return time-Date.now()/1e3}}}}),d=(r(638),r(9)),component=Object(d.a)(o,(function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"order-detail"},[r("div",{staticClass:"detail-hd row-between"},[r("div",{staticClass:"lg"},[e._v("订单详情")]),e._v(" "),r("div",{class:["status lg",{primary:0==e.orderDetail.order_status}]},[e._v("\n            "+e._s(e.orderDetail.order_status_desc)+"\n        ")])]),e._v(" "),r("div",{staticClass:"address"},[r("div",{staticClass:"address-item"},[r("div",{staticClass:"lighter address-item-label"},[e._v("收件人：")]),e._v(" "),r("div",[e._v(e._s(e.orderDetail.consignee))])]),e._v(" "),r("div",{staticClass:"address-item"},[r("div",{staticClass:"lighter address-item-label"},[e._v("联系方式：")]),e._v(" "),r("div",[e._v(e._s(e.orderDetail.mobile))])]),e._v(" "),r("div",{staticClass:"address-item"},[r("div",{staticClass:"lighter address-item-label"},[e._v("收货地址：")]),e._v(" "),r("div",[e._v(e._s(e.orderDetail.delivery_address))])])]),e._v(" "),r("div",{staticClass:"detail-con"},[r("div",{staticClass:"title flex"},[r("nuxt-link",{staticClass:"flex-1 lighter sm line-1 m-r-20",staticStyle:{"min-width":"0"},attrs:{to:"/shop_street_detail?id="+e.orderDetail.shop.id}},[e._v("\n                "+e._s(e.orderDetail.shop.name)+"\n            ")]),e._v(" "),r("div",{staticClass:"flex-1 lighter sm"},[e._v("\n                下单时间："+e._s(e.orderDetail.create_time)+"\n            ")]),e._v(" "),r("div",{staticClass:"flex-1 lighter sm"},[e._v("\n                订单编号："+e._s(e.orderDetail.order_sn)+"\n            ")]),e._v(" "),r("div",{class:["status sm",{primary:0==e.orderDetail.order_status}]},[e._v("\n                "+e._s(e.getOrderStatus(e.orderDetail.order_status))+"\n            ")])],1),e._v(" "),r("div",{staticClass:"goods"},[e._m(0),e._v(" "),r("div",{staticClass:"goods-list"},e._l(e.orderDetail.order_goods,(function(t,n){return r("div",{key:n,staticClass:"goods-item flex"},[r("nuxt-link",{staticClass:"info flex flex-1",attrs:{to:"/goods_details/"+t.goods_id}},[r("el-image",{staticClass:"goods-img",attrs:{src:t.image,alt:""}}),e._v(" "),r("div",{staticClass:"goods-info flex-1"},[r("div",{staticClass:"goods-name line-2"},[t.is_seckill?r("el-tag",{attrs:{size:"mini",effect:"plain"}},[e._v("秒杀")]):e._e(),e._v("\n                                "+e._s(t.goods_name)+"\n                            ")],1),e._v(" "),r("div",{staticClass:"sm lighter m-t-8"},[e._v("\n                                "+e._s(t.spec_value)+"\n                            ")])])],1),e._v(" "),r("div",{staticClass:"price flex row-center"},[r("price-formate",{attrs:{price:t.goods_price}})],1),e._v(" "),r("div",{staticClass:"num flex row-center"},[e._v(e._s(t.goods_num))]),e._v(" "),r("div",{staticClass:"total flex row-center"},[r("price-formate",{attrs:{price:t.sum_price}})],1)],1)})),0)]),e._v(" "),e.orderDetail.user_remark?r("div",{staticClass:"m-t-16"},[r("span",{staticClass:"lighter m-r-8"},[e._v("买家留言：")]),e._v(" "),r("span",[e._v(e._s(e.orderDetail.user_remark))])]):e._e()]),e._v(" "),r("div",{staticClass:"detail-footer flex"},[r("div",[r("div",{staticClass:"flex-col",staticStyle:{"align-items":"flex-end"}},[r("div",{staticClass:"money flex m-b-8"},[r("div",{staticClass:"lighter"},[e._v("商品总价：")]),e._v(" "),r("div",[r("price-formate",{attrs:{price:e.orderDetail.goods_price}})],1)]),e._v(" "),r("div",{staticClass:"money flex m-b-8"},[r("div",{staticClass:"lighter"},[e._v("运费：")]),e._v(" "),r("div",[r("price-formate",{attrs:{price:e.orderDetail.shipping_price}})],1)]),e._v(" "),0!=e.orderDetail.discount_amount?r("div",{staticClass:"money flex m-b-16"},[r("div",{staticClass:"lighter"},[e._v("优惠券：")]),e._v(" "),r("div",[e._v("\n                        -\n                        "),r("price-formate",{attrs:{price:e.orderDetail.discount_amount}})],1)]):e._e(),e._v(" "),r("div",{staticClass:"money flex"},[r("div",{staticClass:"lighter"},[e._v("实付金额：")]),e._v(" "),r("div",{staticClass:"primary"},[r("price-formate",{attrs:{price:e.orderDetail.order_amount,"subscript-size":14,"first-size":28,"second-size":28}})],1)])]),e._v(" "),r("div",{staticClass:"oprate-btn flex row-right m-t-16"},[e.orderDetail.cancel_btn?r("div",{staticClass:"btn plain flex row-center lighter",on:{click:function(t){return e.handleOrder(0)}}},[e._v("\n                    取消订单\n                ")]):e._e(),e._v(" "),e.orderDetail.delivery_btn?r("div",{staticClass:"btn plain flex row-center m-l-8 lighter",on:{click:function(t){e.showDeliverPop=!0}}},[e._v("\n                    物流查询\n                ")]):e._e(),e._v(" "),e.orderDetail.take_btn?r("div",{staticClass:"btn bg-primary flex row-center white m-l-8",on:{click:function(t){return e.handleOrder(2)}}},[e._v("\n                    确认收货\n                ")]):e._e(),e._v(" "),e.orderDetail.del_btn?r("div",{staticClass:"btn plain flex row-center lighter m-l-8",on:{click:function(t){return e.handleOrder(1)}}},[e._v("\n                    删除订单\n                ")]):e._e(),e._v(" "),e.orderDetail.pay_btn?r("nuxt-link",{staticClass:"btn bg-primary flex row-center white m-l-8",attrs:{to:"/payment?id="+e.orderDetail.id+"&from=order"}},[r("span",{staticClass:"mr8"},[e._v("去付款")]),e._v(" "),e.getCancelTime(e.orderDetail.order_cancel_time)>0?r("count-down",{attrs:{time:e.getCancelTime(e.orderDetail.order_cancel_time),format:"hh:mm:ss"},on:{finish:e.getOrderDetail}}):e._e()],1):e._e()],1)])]),e._v(" "),r("deliver-search",{attrs:{aid:e.id},model:{value:e.showDeliverPop,callback:function(t){e.showDeliverPop=t},expression:"showDeliverPop"}})],1)}),[function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"goods-hd lighter flex"},[r("div",{staticClass:"info flex-1"},[e._v("商品信息")]),e._v(" "),r("div",{staticClass:"price flex row-center"},[e._v("单价")]),e._v(" "),r("div",{staticClass:"num flex row-center"},[e._v("数量")]),e._v(" "),r("div",{staticClass:"total flex row-center"},[e._v("合计")])])}],!1,null,"295bca44",null);t.default=component.exports;installComponents(component,{PriceFormate:r(442).default,CountDown:r(449).default,DeliverSearch:r(502).default})}}]);