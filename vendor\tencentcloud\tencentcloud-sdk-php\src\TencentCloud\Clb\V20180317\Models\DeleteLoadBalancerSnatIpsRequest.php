<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Clb\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * DeleteLoadBalancerSnatIps请求参数结构体
 *
 * @method string getLoadBalancerId() 获取负载均衡唯一ID，例如：lb-12345678。
 * @method void setLoadBalancerId(string $LoadBalancerId) 设置负载均衡唯一ID，例如：lb-12345678。
 * @method array getIps() 获取删除SnatIp地址数组。
 * @method void setIps(array $Ips) 设置删除SnatIp地址数组。
 */
class DeleteLoadBalancerSnatIpsRequest extends AbstractModel
{
    /**
     * @var string 负载均衡唯一ID，例如：lb-12345678。
     */
    public $LoadBalancerId;

    /**
     * @var array 删除SnatIp地址数组。
     */
    public $Ips;

    /**
     * @param string $LoadBalancerId 负载均衡唯一ID，例如：lb-12345678。
     * @param array $Ips 删除SnatIp地址数组。
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("LoadBalancerId",$param) and $param["LoadBalancerId"] !== null) {
            $this->LoadBalancerId = $param["LoadBalancerId"];
        }

        if (array_key_exists("Ips",$param) and $param["Ips"] !== null) {
            $this->Ips = $param["Ips"];
        }
    }
}
