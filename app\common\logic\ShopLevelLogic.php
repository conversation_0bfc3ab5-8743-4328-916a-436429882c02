<?php

namespace app\common\logic;

use app\common\basics\Logic;
use app\common\model\shop\Shop;
use app\common\model\shop\ShopLevelConfig;
use app\common\model\shop\ShopLevelUpgrade;
use app\common\model\shop\ShopMerchantfees;
use app\common\server\ConfigServer;
use think\facade\Db;
use Exception;

/**
 * 商家等级逻辑类
 */
class ShopLevelLogic extends Logic
{
    // 等级常量
    const LEVEL_FREE = 0;      // 0元入驻
    const LEVEL_MEMBER = 1;    // 商家会员
    const LEVEL_PREMIUM = 2;   // 实力厂商

    /**
     * 获取所有等级配置
     */
    public static function getLevelConfigs()
    {
        return ShopLevelConfig::where('status', 1)
            ->order('sort', 'asc')
            ->select()
            ->toArray();
    }

    /**
     * 获取指定等级配置
     */
    public static function getLevelConfig($level)
    {
        return ShopLevelConfig::where('level', $level)
            ->where('status', 1)
            ->find();
    }

    /**
     * 检查商家等级权限
     */
    public static function checkPermission($shopId, $permission)
    {
        $shop = Shop::find($shopId);
        if (!$shop) {
            return false;
        }

        $levelConfig = self::getLevelConfig($shop->level);
        if (!$levelConfig) {
            return false;
        }

        $features = json_decode($levelConfig->features, true) ?: [];
        return isset($features[$permission]) && $features[$permission];
    }

    /**
     * 获取商家等级限制
     */
    public static function getLevelLimits($shopId)
    {
        $shop = Shop::find($shopId);
        if (!$shop) {
            return [];
        }

        $levelConfig = self::getLevelConfig($shop->level);
        if (!$levelConfig) {
            return [];
        }

        return json_decode($levelConfig->limits, true) ?: [];
    }

    /**
     * 创建等级升级订单
     */
    public static function createUpgradeOrder($shopId, $targetLevel, $userId = 0)
    {
        try {
            $shop = Shop::find($shopId);
            if (!$shop) {
                self::$error = '商家不存在';
                return false;
            }

            if ($shop->level >= $targetLevel) {
                self::$error = '目标等级不能低于或等于当前等级';
                return false;
            }

            $targetConfig = self::getLevelConfig($targetLevel);
            if (!$targetConfig) {
                self::$error = '目标等级配置不存在';
                return false;
            }

            $currentConfig = self::getLevelConfig($shop->level);
            $upgradePrice = $targetConfig->price - ($currentConfig ? $currentConfig->price : 0);

            if ($upgradePrice <= 0) {
                self::$error = '升级费用计算错误';
                return false;
            }

            Db::startTrans();

            // 创建升级记录
            $orderSn = createSn('shop_level_upgrade', 'order_sn');
            $upgradeData = [
                'shop_id' => $shopId,
                'from_level' => $shop->level,
                'to_level' => $targetLevel,
                'order_sn' => $orderSn,
                'amount' => $upgradePrice,
                'status' => 0, // 待支付
                'create_time' => time(),
                'update_time' => time(),
            ];
            $upgradeId = ShopLevelUpgrade::insertGetId($upgradeData);

            // 创建支付订单
            $merchantFeeData = [
                'user_id' => $userId,
                'shop_id' => $shopId,
                'order_sn' => $orderSn,
                'amount' => $upgradePrice,
                'target_level' => $targetLevel,
                'upgrade_type' => 1, // 等级升级
                'status' => 0, // 待支付
                'created_at' => date('Y-m-d H:i:s'),
            ];
            ShopMerchantfees::create($merchantFeeData);

            Db::commit();

            return [
                'upgrade_id' => $upgradeId,
                'order_sn' => $orderSn,
                'amount' => $upgradePrice,
                'target_level' => $targetLevel,
                'target_level_name' => $targetConfig->name,
            ];

        } catch (Exception $e) {
            Db::rollback();
            self::$error = $e->getMessage();
            return false;
        }
    }

    /**
     * 处理等级升级支付回调
     */
    public static function handleUpgradePayment($orderSn)
    {
        try {
            Db::startTrans();

            // 更新升级记录
            $upgrade = ShopLevelUpgrade::where('order_sn', $orderSn)->find();
            if (!$upgrade) {
                throw new Exception('升级记录不存在');
            }

            if ($upgrade->status != 0) {
                throw new Exception('升级记录状态异常');
            }

            $upgrade->status = 1; // 已支付
            $upgrade->pay_time = time();
            $upgrade->effect_time = time();
            $upgrade->expire_time = time() + (self::getLevelConfig($upgrade->to_level)->duration ?? 31536000);
            $upgrade->update_time = time();
            $upgrade->save();

            // 更新商家等级
            $shop = Shop::find($upgrade->shop_id);
            $shop->level = $upgrade->to_level;
            $shop->level_upgrade_time = time();
            $shop->level_expire_time = $upgrade->expire_time;
            $shop->save();

            // 更新支付订单状态
            ShopMerchantfees::where('order_sn', $orderSn)->update(['status' => 1]);

            Db::commit();
            return true;

        } catch (Exception $e) {
            Db::rollback();
            self::$error = $e->getMessage();
            return false;
        }
    }

    /**
     * 检查等级是否过期
     */
    public static function checkLevelExpired($shopId)
    {
        $shop = Shop::find($shopId);
        if (!$shop || $shop->level == self::LEVEL_FREE) {
            return false; // 0元入驻不会过期
        }

        return $shop->level_expire_time > 0 && $shop->level_expire_time < time();
    }

    /**
     * 降级过期商家
     */
    public static function downgradeExpiredShops()
    {
        $expiredShops = Shop::where('level', '>', self::LEVEL_FREE)
            ->where('level_expire_time', '>', 0)
            ->where('level_expire_time', '<', time())
            ->select();

        foreach ($expiredShops as $shop) {
            $shop->level = self::LEVEL_FREE;
            $shop->level_expire_time = 0;
            $shop->save();

            // 记录降级日志
            Db::name('log')->insert([
                'type' => 'shop_level_downgrade',
                'log' => "商家ID:{$shop->id} 等级过期自动降级为0元入驻",
                'creat_time' => date('Y-m-d H:i:s')
            ]);
        }

        return count($expiredShops);
    }

    /**
     * 获取等级名称
     */
    public static function getLevelName($level)
    {
        $names = [
            self::LEVEL_FREE => '0元入驻',
            self::LEVEL_MEMBER => '商家会员',
            self::LEVEL_PREMIUM => '实力厂商',
        ];

        return $names[$level] ?? '未知等级';
    }

    /**
     * 获取商家可升级的等级列表
     */
    public static function getUpgradableLevels($shopId)
    {
        $shop = Shop::find($shopId);
        if (!$shop) {
            return [];
        }

        $allLevels = self::getLevelConfigs();
        $upgradableLevels = [];

        foreach ($allLevels as $level) {
            if ($level['level'] > $shop->level) {
                $currentConfig = self::getLevelConfig($shop->level);
                $level['upgrade_price'] = $level['price'] - ($currentConfig ? $currentConfig->price : 0);
                $upgradableLevels[] = $level;
            }
        }

        return $upgradableLevels;
    }
}
