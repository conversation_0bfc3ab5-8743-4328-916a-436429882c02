<?php

namespace app\shop\logic;

use app\common\basics\Logic;
use app\common\model\shop\ShopRefundAddress;
use think\facade\Db;

class RefundAddressLogic extends Logic
{
    /**
     * @notes 获取退货地址列表
     * @param int $shop_id
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function lists($shop_id)
    {
        $lists = ShopRefundAddress::where('shop_id', $shop_id)
            ->field('id, contact, mobile, province, city, district, address, is_default, create_time')
            ->order('is_default desc, id desc')
            ->select()
            ->toArray();
            
        return $lists;
    }
    
    /**
     * @notes 添加退货地址
     * @param array $params
     * @return bool
     */
    public static function add($params)
    {
        Db::startTrans();
        try {
            // 如果设为默认地址，则将其他地址设为非默认
            if (isset($params['is_default']) && $params['is_default'] == 1) {
                ShopRefundAddress::where('shop_id', $params['shop_id'])
                    ->update(['is_default' => 0]);
            }
            
            ShopRefundAddress::create($params);
            
            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            self::setError($e->getMessage());
            return false;
        }
    }
    
    /**
     * @notes 获取退货地址详情
     * @param int $id
     * @param int $shop_id
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function detail($id, $shop_id)
    {
        $detail = ShopRefundAddress::where([
                ['id', '=', $id],
                ['shop_id', '=', $shop_id]
            ])
            ->find();
            
        if (empty($detail)) {
            self::setError('地址不存在或无权限查看');
            return [];
        }
        
        return $detail->toArray();
    }
    
    /**
     * @notes 编辑退货地址
     * @param array $params
     * @return bool
     */
    public static function edit($params)
    {
        Db::startTrans();
        try {
            // 检查地址是否存在且属于该商家
            $address = ShopRefundAddress::where([
                    ['id', '=', $params['id']],
                    ['shop_id', '=', $params['shop_id']]
                ])
                ->find();
                
            if (empty($address)) {
                throw new \Exception('地址不存在或无权限编辑');
            }
            
            // 如果设为默认地址，则将其他地址设为非默认
            if (isset($params['is_default']) && $params['is_default'] == 1) {
                ShopRefundAddress::where('shop_id', $params['shop_id'])
                    ->update(['is_default' => 0]);
            }
            
            $address->save($params);
            
            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            self::setError($e->getMessage());
            return false;
        }
    }
    
    /**
     * @notes 删除退货地址
     * @param int $id
     * @param int $shop_id
     * @return bool
     */
    public static function del($id, $shop_id)
    {
        try {
            // 检查地址是否存在且属于该商家
            $address = ShopRefundAddress::where([
                    ['id', '=', $id],
                    ['shop_id', '=', $shop_id]
                ])
                ->find();
                
            if (empty($address)) {
                throw new \Exception('地址不存在或无权限删除');
            }
            
            $address->delete();
            
            return true;
        } catch (\Exception $e) {
            self::setError($e->getMessage());
            return false;
        }
    }
    
    /**
     * @notes 设置默认退货地址
     * @param int $id
     * @param int $shop_id
     * @return bool
     */
    public static function setDefault($id, $shop_id)
    {
        Db::startTrans();
        try {
            // 检查地址是否存在且属于该商家
            $address = ShopRefundAddress::where([
                    ['id', '=', $id],
                    ['shop_id', '=', $shop_id]
                ])
                ->find();
                
            if (empty($address)) {
                throw new \Exception('地址不存在或无权限设置');
            }
            
            // 将所有地址设为非默认
            ShopRefundAddress::where('shop_id', $shop_id)
                ->update(['is_default' => 0]);
                
            // 将当前地址设为默认
            $address->is_default = 1;
            $address->save();
            
            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            self::setError($e->getMessage());
            return false;
        }
    }
}