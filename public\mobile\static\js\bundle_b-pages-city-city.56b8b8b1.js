(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["bundle_b-pages-city-city"],{"070b":function(t,i,e){"use strict";e("7a82");var n=e("ee27").default;Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0;var a=n(e("f07e")),o=n(e("c964")),c=n(e("f3f3"));e("c740"),e("4de4"),e("d3b7"),e("3c65"),e("b64b"),e("e25e"),e("ac1f"),e("d81d");var r=e("26cb"),s=e("9953"),l=e("b08d"),u=n(e("8ffc")),d={data:function(){return{isLoading:!1,cityList:[],labelList:[],historyList:[],hotList:[{name:"北京市",gcj02_lat:"39.929986",gcj02_lng:"116.395645",id:110100},{name:"上海市",gcj02_lat:"31.249162",gcj02_lng:"121.487899",id:310100},{name:"广州市",gcj02_lat:"23.120049",gcj02_lng:"113.30765",id:440100},{name:"深圳市",gcj02_lat:"22.546054",gcj02_lng:"114.025974",id:440300},{name:"重庆市",gcj02_lat:"29.544606",gcj02_lng:"106.530635",id:110100},{name:"成都市",gcj02_lat:"30.679943",gcj02_lng:"104.067923",id:510100},{name:"杭州市",gcj02_lat:"30.259244",gcj02_lng:"120.219375",id:110100},{name:"苏州市",gcj02_lat:"31.317987",gcj02_lng:"120.619907",id:320500},{name:"武汉市",gcj02_lat:"30.581084",gcj02_lng:"114.3162",id:420100},{name:"沈阳市",gcj02_lat:"41.808645",gcj02_lng:"123.432791",id:210100}],isFirstLoading:!0,touchmove:!1,touchmoveIndex:0}},onLoad:function(){this.getCityListsFunc(),this.historyList=u.default.get("HISTORY")||[]},onPageScroll:function(t){var i=t.scrollTop,e=this.anchor,n=e.findIndex((function(t){return t>=i})),a=-1!==n;a&&!this.touchmmove&&(this.touchmoveIndex=n)},methods:(0,c.default)((0,c.default)((0,c.default)({},(0,r.mapActions)(["initLocationFunc"])),(0,r.mapMutations)(["setCityInfo"])),{},{reLocationFunc:function(){var t=this;return(0,o.default)((0,a.default)().mark((function i(){return(0,a.default)().wrap((function(i){while(1)switch(i.prev=i.next){case 0:return t.isLoading=!0,i.next=3,t.initLocationFunc();case 3:t.isLoading=!1;case 4:case"end":return i.stop()}}),i)})))()},chooseCity:function(t){try{this.setCityInfo(t),this.$Router.back(1,{success:function(){var t=(0,l.currentPage)(),i=t.onLoad,e=t.options;i&&i((0,c.default)((0,c.default)({},e),{},{refresh:!0}))}}),console.log(t);var i=this.historyList,e=i.filter((function(i){return i.name==t.name})),n=0===e.length;n&&(i.unshift(t),u.default.set("HISTORY",i))}catch(a){console.log(a),(0,l.toast)({title:"选择有误，请联系管理员"})}},getCityListsFunc:function(){var t=this;(0,s.getCityLists)().then((function(i){var e;1==i.code&&(t.cityList=i.data,t.labelList=Object.keys(null!==(e=i.data)&&void 0!==e?e:{}),t.setRect());t.isFirstLoading=!1}))},onTouchMove:function(t){var i=parseInt(t.changedTouches[0].clientY),e=this.labelList.length,n=parseInt(this.sidebar.height/e),a=Math.floor((i-this.sidebar.top)/n);a<0?a=0:a>e-1&&(a=e-1),this.touchmoveIndex!=a&&(this.touchmove=!0,this.touchmoveIndex=a,uni.pageScrollTo({duration:0,scrollTop:this.anchor[a]}))},onTouchStop:function(){this.touchmove=!1,this.scrollToAnchorIndex=null},setRect:function(){var t=this;return(0,o.default)((0,a.default)().mark((function i(){var e,n;return(0,a.default)().wrap((function(i){while(1)switch(i.prev=i.next){case 0:return i.next=2,t.$nextTick();case 2:e=uni.createSelectorQuery().selectAll(".city-bar__sidebar"),e.boundingClientRect((function(i){t.sidebar={height:i[0].height,top:i[0].top}})).exec(),n=uni.createSelectorQuery().selectAll(".anchor"),n.boundingClientRect((function(i){i.top=parseInt(i.top),t.anchor=i.map((function(t){return parseInt(t.top)}))})).exec();case 6:case"end":return i.stop()}}),i)})))()}}),computed:(0,c.default)({},(0,r.mapGetters)(["cityInfo"]))};i.default=d},"11a9":function(t,i,e){"use strict";e.r(i);var n=e("ec16"),a=e("577f");for(var o in a)["default"].indexOf(o)<0&&function(t){e.d(i,t,(function(){return a[t]}))}(o);e("7782");var c=e("f0c5"),r=Object(c["a"])(a["default"],n["b"],n["c"],!1,null,"0563a689",null,!1,n["a"],void 0);i["default"]=r.exports},"1c75":function(t,i,e){"use strict";var n=e("7a54"),a=e.n(n);a.a},2875:function(t,i,e){"use strict";e.r(i);var n=e("a712"),a=e("c13e");for(var o in a)["default"].indexOf(o)<0&&function(t){e.d(i,t,(function(){return a[t]}))}(o);e("8fed");var c=e("f0c5"),r=Object(c["a"])(a["default"],n["b"],n["c"],!1,null,"061dd044",null,!1,n["a"],void 0);i["default"]=r.exports},"2ab4":function(t,i,e){"use strict";e.d(i,"b",(function(){return n})),e.d(i,"c",(function(){return a})),e.d(i,"a",(function(){}));var n=function(){var t=this.$createElement,i=this._self._c||t;return this.show?i("v-uni-view",{staticClass:"u-loading",class:"circle"==this.mode?"u-loading-circle":"u-loading-flower",style:[this.cricleStyle]}):this._e()},a=[]},"356b":function(t,i,e){"use strict";e("7a82"),Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0,e("a9e3");var n={name:"u-loading",props:{mode:{type:String,default:"circle"},color:{type:String,default:"#c7c7c7"},size:{type:[String,Number],default:"34"},show:{type:Boolean,default:!0}},computed:{cricleStyle:function(){var t={};return t.width=this.size+"rpx",t.height=this.size+"rpx","circle"==this.mode&&(t.borderColor="#e4e4e4 #e4e4e4 #e4e4e4 ".concat(this.color?this.color:"#c7c7c7")),t}}};i.default=n},"577f":function(t,i,e){"use strict";e.r(i);var n=e("070b"),a=e.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){e.d(i,t,(function(){return n[t]}))}(o);i["default"]=a.a},7295:function(t,i,e){var n=e("24fb");i=n(!1),i.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.city[data-v-0563a689]{padding:%?12?% 0}.city-title[data-v-0563a689]{color:#aaa;padding:%?12?% %?34?%}.city .city-current[data-v-0563a689]{color:#575757;padding:0 %?36?%}.city .city-current .reselect[data-v-0563a689]{color:#528fff;padding:%?26?% %?10?%}.city-list[data-v-0563a689]{color:#575757;padding:0 %?40?%;display:flex;flex-wrap:wrap}.city-list-item[data-v-0563a689]{width:25%;height:%?96?%;line-height:%?96?%;padding-left:%?34?%}.city-bar__sidebar[data-v-0563a689]{position:fixed;top:50%;right:0;display:flex;flex-direction:column;text-align:center;-webkit-transform:translateY(-50%);transform:translateY(-50%);-webkit-user-select:none;user-select:none;z-index:99}.city-bar__index[data-v-0563a689]{font-weight:500;padding:%?8?% %?18?%;font-size:%?22?%;line-height:1}.city-list-alert[data-v-0563a689]{position:fixed;width:%?120?%;height:%?120?%;right:%?90?%;top:50%;margin-top:%?-60?%;border-radius:%?24?%;font-size:%?50?%;color:#fff;background-color:rgba(0,0,0,.65);display:flex;justify-content:center;align-items:center;padding:0;z-index:9999999}.city-list-alert uni-text[data-v-0563a689]{line-height:%?50?%}',""]),t.exports=i},7782:function(t,i,e){"use strict";var n=e("eedf"),a=e.n(n);a.a},"7a54":function(t,i,e){var n=e("aa36");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=e("4f06").default;a("4f941e16",n,!0,{sourceMap:!1,shadowMode:!1})},"80a3":function(t,i,e){"use strict";e.r(i);var n=e("356b"),a=e.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){e.d(i,t,(function(){return n[t]}))}(o);i["default"]=a.a},"822c":function(t,i,e){"use strict";e("7a82"),Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0;var n={data:function(){return{}},props:{type:{type:String,default:"fixed"},backgroundColor:{type:String,default:"#fff"}},methods:{}};i.default=n},"8fc0":function(t,i,e){"use strict";e.r(i);var n=e("2ab4"),a=e("80a3");for(var o in a)["default"].indexOf(o)<0&&function(t){e.d(i,t,(function(){return a[t]}))}(o);e("1c75");var c=e("f0c5"),r=Object(c["a"])(a["default"],n["b"],n["c"],!1,null,"bf7076f2",null,!1,n["a"],void 0);i["default"]=r.exports},"8fed":function(t,i,e){"use strict";var n=e("e2db"),a=e.n(n);a.a},a712:function(t,i,e){"use strict";e.d(i,"b",(function(){return a})),e.d(i,"c",(function(){return o})),e.d(i,"a",(function(){return n}));var n={uLoading:e("8fc0").default},a=function(){var t=this.$createElement,i=this._self._c||t;return i("v-uni-view",{class:"loading "+("flex"==this.type?"flex":""),style:{backgroundColor:this.backgroundColor}},[i("u-loading",{attrs:{mode:"flower",size:60}})],1)},o=[]},aa36:function(t,i,e){var n=e("24fb");i=n(!1),i.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.u-loading-circle[data-v-bf7076f2]{display:inline-flex;vertical-align:middle;width:%?28?%;height:%?28?%;background:0 0;border-radius:50%;border:2px solid;border-color:#e5e5e5 #e5e5e5 #e5e5e5 #8f8d8e;-webkit-animation:u-circle-data-v-bf7076f2 1s linear infinite;animation:u-circle-data-v-bf7076f2 1s linear infinite}.u-loading-flower[data-v-bf7076f2]{width:20px;height:20px;display:inline-block;vertical-align:middle;-webkit-animation:a 1s steps(12) infinite;animation:u-flower-data-v-bf7076f2 1s steps(12) infinite;background:transparent url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMjAiIGhlaWdodD0iMTIwIiB2aWV3Qm94PSIwIDAgMTAwIDEwMCI+PHBhdGggZmlsbD0ibm9uZSIgZD0iTTAgMGgxMDB2MTAwSDB6Ii8+PHJlY3Qgd2lkdGg9IjciIGhlaWdodD0iMjAiIHg9IjQ2LjUiIHk9IjQwIiBmaWxsPSIjRTlFOUU5IiByeD0iNSIgcnk9IjUiIHRyYW5zZm9ybT0idHJhbnNsYXRlKDAgLTMwKSIvPjxyZWN0IHdpZHRoPSI3IiBoZWlnaHQ9IjIwIiB4PSI0Ni41IiB5PSI0MCIgZmlsbD0iIzk4OTY5NyIgcng9IjUiIHJ5PSI1IiB0cmFuc2Zvcm09InJvdGF0ZSgzMCAxMDUuOTggNjUpIi8+PHJlY3Qgd2lkdGg9IjciIGhlaWdodD0iMjAiIHg9IjQ2LjUiIHk9IjQwIiBmaWxsPSIjOUI5OTlBIiByeD0iNSIgcnk9IjUiIHRyYW5zZm9ybT0icm90YXRlKDYwIDc1Ljk4IDY1KSIvPjxyZWN0IHdpZHRoPSI3IiBoZWlnaHQ9IjIwIiB4PSI0Ni41IiB5PSI0MCIgZmlsbD0iI0EzQTFBMiIgcng9IjUiIHJ5PSI1IiB0cmFuc2Zvcm09InJvdGF0ZSg5MCA2NSA2NSkiLz48cmVjdCB3aWR0aD0iNyIgaGVpZ2h0PSIyMCIgeD0iNDYuNSIgeT0iNDAiIGZpbGw9IiNBQkE5QUEiIHJ4PSI1IiByeT0iNSIgdHJhbnNmb3JtPSJyb3RhdGUoMTIwIDU4LjY2IDY1KSIvPjxyZWN0IHdpZHRoPSI3IiBoZWlnaHQ9IjIwIiB4PSI0Ni41IiB5PSI0MCIgZmlsbD0iI0IyQjJCMiIgcng9IjUiIHJ5PSI1IiB0cmFuc2Zvcm09InJvdGF0ZSgxNTAgNTQuMDIgNjUpIi8+PHJlY3Qgd2lkdGg9IjciIGhlaWdodD0iMjAiIHg9IjQ2LjUiIHk9IjQwIiBmaWxsPSIjQkFCOEI5IiByeD0iNSIgcnk9IjUiIHRyYW5zZm9ybT0icm90YXRlKDE4MCA1MCA2NSkiLz48cmVjdCB3aWR0aD0iNyIgaGVpZ2h0PSIyMCIgeD0iNDYuNSIgeT0iNDAiIGZpbGw9IiNDMkMwQzEiIHJ4PSI1IiByeT0iNSIgdHJhbnNmb3JtPSJyb3RhdGUoLTE1MCA0NS45OCA2NSkiLz48cmVjdCB3aWR0aD0iNyIgaGVpZ2h0PSIyMCIgeD0iNDYuNSIgeT0iNDAiIGZpbGw9IiNDQkNCQ0IiIHJ4PSI1IiByeT0iNSIgdHJhbnNmb3JtPSJyb3RhdGUoLTEyMCA0MS4zNCA2NSkiLz48cmVjdCB3aWR0aD0iNyIgaGVpZ2h0PSIyMCIgeD0iNDYuNSIgeT0iNDAiIGZpbGw9IiNEMkQyRDIiIHJ4PSI1IiByeT0iNSIgdHJhbnNmb3JtPSJyb3RhdGUoLTkwIDM1IDY1KSIvPjxyZWN0IHdpZHRoPSI3IiBoZWlnaHQ9IjIwIiB4PSI0Ni41IiB5PSI0MCIgZmlsbD0iI0RBREFEQSIgcng9IjUiIHJ5PSI1IiB0cmFuc2Zvcm09InJvdGF0ZSgtNjAgMjQuMDIgNjUpIi8+PHJlY3Qgd2lkdGg9IjciIGhlaWdodD0iMjAiIHg9IjQ2LjUiIHk9IjQwIiBmaWxsPSIjRTJFMkUyIiByeD0iNSIgcnk9IjUiIHRyYW5zZm9ybT0icm90YXRlKC0zMCAtNS45OCA2NSkiLz48L3N2Zz4=) no-repeat;background-size:100%}@-webkit-keyframes u-flower-data-v-bf7076f2{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}@keyframes u-flower-data-v-bf7076f2{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}@-webkit-keyframes u-circle-data-v-bf7076f2{0%{-webkit-transform:rotate(0);transform:rotate(0)}100%{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}',""]),t.exports=i},b83e:function(t,i,e){var n=e("24fb");i=n(!1),i.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.loading[data-v-061dd044]{position:fixed;top:0;left:0;width:100vw;height:100vh;z-index:9999;display:flex;justify-content:center;align-items:center}.loading.flex[data-v-061dd044]{position:static;flex:1;width:100%}.loading .loading-img[data-v-061dd044]{width:%?100?%;height:%?100?%}',""]),t.exports=i},c13e:function(t,i,e){"use strict";e.r(i);var n=e("822c"),a=e.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){e.d(i,t,(function(){return n[t]}))}(o);i["default"]=a.a},e2db:function(t,i,e){var n=e("b83e");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=e("4f06").default;a("fbf8b9f0",n,!0,{sourceMap:!1,shadowMode:!1})},ec16:function(t,i,e){"use strict";e.d(i,"b",(function(){return a})),e.d(i,"c",(function(){return o})),e.d(i,"a",(function(){return n}));var n={uIcon:e("90f3").default,uLoading:e("8fc0").default,loadingView:e("2875").default},a=function(){var t=this,i=t.$createElement,e=t._self._c||i;return e("v-uni-view",{staticClass:"city"},[e("v-uni-view",{staticClass:"city-title xs"},[t._v("当前定位城市")]),e("v-uni-view",{staticClass:"city-current bg-white flex row-between"},[e("v-uni-view",[e("u-icon",{attrs:{name:"map-fill",size:"34"}}),t.isLoading?e("v-uni-text",{staticClass:"m-l-8 nr"},[t._v("定位中...")]):e("v-uni-text",{staticClass:"m-l-8 nr"},[t._v(t._s(t.cityInfo.name||"城市"))])],1),e("v-uni-view",{staticClass:"reselect nr flex",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.reLocationFunc.apply(void 0,arguments)}}},[t.isLoading?e("u-loading",{attrs:{mode:"flower",color:"#528FFF",size:"28"}}):t._e(),e("v-uni-text",{staticClass:"m-l-8"},[t._v("重新定位")])],1)],1),t.historyList.length?e("v-uni-view",{staticClass:"city-title xs"},[t._v("历史浏览城市")]):t._e(),e("v-uni-view",{staticClass:"city-list bg-white"},[t._l(t.historyList,(function(i,n){return[e("v-uni-view",{key:n+"_0",staticClass:"city-list-item line-1 nr",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.chooseCity(i)}}},[t._v(t._s(i.name))])]}))],2),e("v-uni-view",{staticClass:"city-title xs"},[t._v("热门城市")]),e("v-uni-view",{staticClass:"city-list bg-white"},[t._l(t.hotList,(function(i,n){return[e("v-uni-view",{key:n+"_0",staticClass:"city-list-item line-1 nr",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.chooseCity(i)}}},[t._v(t._s(i.name))])]}))],2),t._l(t.cityList,(function(i,n){return e("v-uni-view",{key:n},[e("v-uni-view",{staticClass:"city-title anchor xs"},[t._v(t._s(n))]),e("v-uni-view",{staticClass:"city-list bg-white"},[t._l(i,(function(i,n){return[e("v-uni-view",{key:n+"_0",staticClass:"city-list-item line-1 nr",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.chooseCity(i)}}},[t._v(t._s(i.name))])]}))],2)],1)})),e("v-uni-view",{staticClass:"city-bar__sidebar",on:{touchstart:function(i){i.stopPropagation(),i.preventDefault(),arguments[0]=i=t.$handleEvent(i),t.onTouchMove.apply(void 0,arguments)},touchmove:function(i){i.stopPropagation(),i.preventDefault(),arguments[0]=i=t.$handleEvent(i),t.onTouchMove.apply(void 0,arguments)},touchend:function(i){i.stopPropagation(),i.preventDefault(),arguments[0]=i=t.$handleEvent(i),t.onTouchStop.apply(void 0,arguments)},touchcancel:function(i){i.stopPropagation(),i.preventDefault(),arguments[0]=i=t.$handleEvent(i),t.onTouchStop.apply(void 0,arguments)}}},t._l(t.labelList,(function(i,n){return e("v-uni-view",{key:n,staticClass:"city-bar__index",style:{color:t.touchmoveIndex===n?"#528FFF":""}},[t._v(t._s(i))])})),1),t.touchmove&&t.labelList[t.touchmoveIndex+""]?e("v-uni-view",{staticClass:"city-list-alert"},[e("v-uni-text",[t._v(t._s(t.labelList[t.touchmoveIndex]))])],1):t._e(),t.isFirstLoading?e("loading-view"):t._e()],2)},o=[]},eedf:function(t,i,e){var n=e("7295");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=e("4f06").default;a("0f694646",n,!0,{sourceMap:!1,shadowMode:!1})}}]);