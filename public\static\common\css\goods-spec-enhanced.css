/**
 * 商品规格增强版样式
 * 解决输入框交互问题，提升用户体验
 * <AUTHOR> Assistant
 * @version 2.0
 */

/* 基础输入框增强样式 */
.spec-enhanced-input {
    position: relative;
    z-index: 1;
    transition: all 0.3s ease;
    border: 1px solid #e6e6e6;
    background-color: #fff !important;
    pointer-events: auto !important;
    user-select: text !important;
}

.spec-enhanced-input:hover {
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
}

.spec-enhanced-input:focus,
.spec-input-focused {
    border-color: #1890ff !important;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
    outline: none !important;
    background-color: #fff !important;
    z-index: 10;
}

/* 确保输入框可编辑 */
.spec-input-field {
    pointer-events: auto !important;
    user-select: text !important;
    -webkit-user-select: text !important;
    -moz-user-select: text !important;
    -ms-user-select: text !important;
    cursor: text !important;
    background-color: #fff !important;
    position: relative;
    z-index: 1;
}

.spec-input-field:disabled,
.spec-input-field[readonly] {
    pointer-events: none !important;
    background-color: #f5f5f5 !important;
    cursor: not-allowed !important;
}

/* 表格输入框特殊处理 */
.spec-lists-table .spec-input-field {
    width: 100% !important;
    min-width: 80px;
    height: 32px;
    line-height: 32px;
    padding: 0 8px;
    font-size: 12px;
    border-radius: 2px;
}

/* 错误状态样式 */
.layui-form-danger.spec-input-field {
    border-color: #ff5722 !important;
    background-color: #fff2f0 !important;
}

.layui-form-danger.spec-input-field:focus {
    box-shadow: 0 0 0 2px rgba(255, 87, 34, 0.2) !important;
}

/* 表格容器优化 */
.spec-lists-table {
    position: relative;
    z-index: 1;
    table-layout: auto;
    width: 100%;
    border-collapse: collapse;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    background: #fff;
}

.spec-lists-table td {
    position: relative;
    padding: 12px 8px;
    vertical-align: middle;
    border-bottom: 1px solid #f0f0f0;
    min-width: 80px;
}

.spec-lists-table td:first-child {
    width: 90px;
    min-width: 90px;
}

.spec-lists-table th {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
    font-weight: 600;
    text-align: center;
    padding: 16px 8px;
    border-bottom: 2px solid #dee2e6;
    color: #495057;
    font-size: 13px;
    white-space: nowrap;
}

/* 表格输入框特殊处理 */
.spec-lists-table .layui-input {
    width: 100% !important;
    min-width: 70px;
    height: 36px;
    line-height: 36px;
    padding: 0 10px;
    font-size: 13px;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
    transition: all 0.3s ease;
    background-color: #fff;
}

.spec-lists-table .layui-input:hover {
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
}

.spec-lists-table .layui-input:focus {
    border-color: #1890ff !important;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
    outline: none !important;
}

/* 表格行悬停效果 */
.spec-lists-table tbody tr:hover {
    background-color: #f8f9fa;
}

.spec-lists-table tbody tr:hover td {
    border-bottom-color: #e9ecef;
}

/* 确保图片列有足够宽度 */
.spec-lists-table colgroup col:first-child {
    width: 90px;
}

/* 批量设置区域优化 */
.batch-div {
    margin-bottom: 20px;
    padding: 16px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 8px;
    border: 1px solid #dee2e6;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);
}

.batch-spec-title {
    font-weight: 600;
    color: #495057;
    margin-right: 15px;
    font-size: 14px;
}

.batch-spec-content {
    display: inline-block;
    padding: 8px 12px;
    margin: 4px 6px;
    background-color: #fff;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 13px;
    font-weight: 500;
    user-select: none;
}

.batch-spec-content:hover {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    border-color: #1890ff;
    color: #1890ff;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
}

.batch-spec-content.active {
    background: linear-gradient(135deg, #1890ff 0%, #0d7ed9 100%);
    border-color: #1890ff;
    color: #fff;
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
}

/* 视频管理增强样式 */
.upload-video-div {
    position: relative;
    display: inline-block;
    margin: 10px 0;
}

.video-controls {
    position: absolute;
    bottom: 10px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 8px;
    background: rgba(0, 0, 0, 0.7);
    padding: 8px;
    border-radius: 4px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.upload-video-div:hover .video-controls {
    opacity: 1;
}

.video-controls .layui-btn {
    font-size: 12px;
    padding: 4px 8px;
    height: auto;
    line-height: 1.4;
}

.video-delete-btn {
    background-color: #ff4757 !important;
    border-color: #ff4757 !important;
}

.video-replace-btn {
    background-color: #5352ed !important;
    border-color: #5352ed !important;
}

/* 图片上传区域优化 */
.goods-spec-img-div {
    min-height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 8px;
}

.goods-spec-img-div .upload-image-elem {
    width: 100%;
    text-align: center;
    position: relative;
}

.goods-spec-img-div .add-upload-image {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 70px;
    height: 70px;
    border: 2px dashed #d9d9d9;
    border-radius: 8px;
    color: #666;
    text-decoration: none;
    font-size: 11px;
    transition: all 0.3s ease;
    background: #fafafa;
    flex-direction: column;
    gap: 4px;
}

.goods-spec-img-div .add-upload-image:before {
    content: "+";
    font-size: 24px;
    line-height: 1;
    font-weight: 300;
}

.goods-spec-img-div .add-upload-image:hover {
    border-color: #1890ff;
    color: #1890ff;
    background: #f0f8ff;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
}

/* 已上传图片的样式 */
.goods-spec-img-div .upload-image-div {
    position: relative;
    display: inline-block;
}

.goods-spec-img-div .upload-image-div img {
    width: 70px;
    height: 70px;
    object-fit: cover;
    border-radius: 8px;
    border: 1px solid #e8e8e8;
    transition: all 0.3s ease;
}

.goods-spec-img-div .upload-image-div:hover img {
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    transform: scale(1.02);
}

/* 规格项目管理优化 */
.goods-spec-div {
    position: relative;
    margin-bottom: 15px;
    padding: 15px;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    background-color: #fff;
}

.goods-spec-del-x {
    position: absolute;
    top: -8px;
    right: -8px;
    width: 20px;
    height: 20px;
    line-height: 18px;
    text-align: center;
    background-color: #ff4757;
    color: #fff;
    border-radius: 50%;
    cursor: pointer;
    font-size: 12px;
    text-decoration: none;
    z-index: 10;
    transition: all 0.2s ease;
}

.goods-spec-del-x:hover {
    background-color: #ff3838;
    transform: scale(1.1);
}

/* 规格值输入框优化 */
.goods-spec-value {
    position: relative;
    margin: 4px;
}

.goods-spec-value-input {
    text-align: center;
    font-size: 12px;
    height: 32px;
    line-height: 32px;
}

.goods-spec-value-del-x {
    position: absolute;
    top: -6px;
    right: -6px;
    width: 16px;
    height: 16px;
    line-height: 14px;
    text-align: center;
    background-color: #ff4757;
    color: #fff;
    border-radius: 50%;
    cursor: pointer;
    font-size: 10px;
    text-decoration: none;
    z-index: 5;
}

/* 响应式优化 */
@media (max-width: 768px) {
    .spec-lists-table {
        font-size: 11px;
    }
    
    .spec-lists-table .spec-input-field {
        min-width: 60px;
        font-size: 11px;
        padding: 0 4px;
    }
    
    .batch-div {
        padding: 8px;
    }
    
    .batch-spec-content {
        font-size: 11px;
        padding: 3px 6px;
    }
}

/* 防止layui表格固定列遮挡 */
.layui-table-fixed {
    z-index: 1 !important;
}

.layui-table-fixed .layui-table-body {
    z-index: 1 !important;
}

/* 弹窗层级优化 */
.layui-layer {
    z-index: 19891014 !important;
}

.layui-layer-shade {
    z-index: 19891013 !important;
}

/* 确保表单元素可交互 */
.layui-form .spec-input-field {
    pointer-events: auto !important;
}

/* 加载状态样式 */
.spec-loading {
    position: relative;
    pointer-events: none;
    opacity: 0.6;
}

.spec-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 16px;
    height: 16px;
    margin: -8px 0 0 -8px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #1890ff;
    border-radius: 50%;
    animation: spec-spin 1s linear infinite;
}

@keyframes spec-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 成功状态样式 */
.spec-success {
    border-color: #52c41a !important;
    background-color: #f6ffed !important;
}

.spec-success:focus {
    box-shadow: 0 0 0 2px rgba(82, 196, 26, 0.2) !important;
}
