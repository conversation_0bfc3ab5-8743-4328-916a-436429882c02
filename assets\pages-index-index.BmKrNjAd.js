import{f as t,c as e,w as a,i as o,o as n,a as i,b as s,x as r,y as p}from"./index-B6kWyIrN.js";import{_ as l}from"./uv-button.Bb8r9WJv.js";import{o as c,a as m,b as u,c as y,r as d}from"./uv-icon.D6fiO-QB.js";import{_ as f}from"./zb-popover.CqttEQzx.js";const x={__name:"index",setup(x){c((function(t){})),m((function(){}));const b=[{text:"选项一"},{text:"选项二"},{text:"选项三"}];async function g(t){console.log(t)}return u((function(){})),y((function(){})),(c,m)=>{const u=d(t("uv-button"),l),y=p,x=d(t("zb-popover"),f),_=o;return n(),e(_,{class:"content"},{default:a((()=>[i(u,{type:"primary",text:"确定"}),i(u,{type:"primary",plain:!0,text:"镂空"}),i(u,{type:"primary",plain:!0,hairline:!0,text:"细边"}),i(u,{type:"primary",disabled:c.disabled,text:"禁用"},null,8,["disabled"]),i(u,{type:"primary",loading:"",loadingText:"加载中"}),i(u,{type:"primary",icon:"map",text:"图标按钮"}),i(u,{type:"primary",shape:"circle",text:"按钮形状"}),i(u,{onClick:m[0]||(m[0]=t=>async function(){console.log(1),r({url:"wss://kefu.huohanghang.cn/?token=866eedcb201943d2bfc5c7ad8a7fa685&type=kefu&client=2&shop_id=49",complete:t=>{console.log(t)}})}()),text:"渐变色按钮",color:"linear-gradient(to right, rgb(66, 83, 216), rgb(213, 51, 186))"}),i(u,{type:"primary",size:"small",text:"大小尺寸"}),i(x,{placement:"bottom-start",options:b,ref:"Popover1",onHandleClick:g,class:"item-popover"},{default:a((()=>[i(y,{class:"mini-btn",type:"primary",size:"mini"},{default:a((()=>[s("浅色风格")])),_:1})])),_:1},512)])),_:1})}}};export{x as default};
