{layout name="layout1" /}
<style>
    .stats-card {
        margin-bottom: 15px;
    }
    .stats-item {
        text-align: center;
        padding: 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 8px;
        margin-bottom: 15px;
    }
    .stats-item.blue {
        background: linear-gradient(135deg, #1E9FFF 0%, #1890ff 100%);
    }
    .stats-item.green {
        background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
    }
    .stats-item.orange {
        background: linear-gradient(135deg, #fa8c16 0%, #d46b08 100%);
    }
    .stats-item.red {
        background: linear-gradient(135deg, #f5222d 0%, #cf1322 100%);
    }
    .stats-number {
        font-size: 36px;
        font-weight: bold;
        margin-bottom: 5px;
    }
    .stats-label {
        font-size: 14px;
        opacity: 0.9;
    }
    .chart-container {
        height: 300px;
        margin-top: 15px;
    }
    .source-stats {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        margin-top: 15px;
    }
    .source-item {
        padding: 10px 15px;
        border-radius: 5px;
        color: white;
        text-align: center;
        flex: 1;
        min-width: 100px;
    }
</style>

<div class="wrapper">
    <div class="layui-card">
        <div class="layui-card-header">
            <h3>联系统计</h3>
        </div>
        <div class="layui-card-body">
            <div id="stats-content">
                <!-- 内容将通过AJAX加载 -->
                <div class="layui-text-center" style="padding: 50px;">
                    <i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop" style="font-size: 30px;"></i>
                    <p>加载中...</p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    layui.use(['form'], function () {
        var $ = layui.$;
        
        // 加载统计数据
        like.ajax({
            url: '{:url("purchaser_contact/stats")}',
            type: "get",
            success: function (res) {
                if (res.code == 1) {
                    renderStats(res.data);
                } else {
                    $('#stats-content').html('<div class="layui-text-center" style="padding: 50px;"><p style="color: #ff5722;">' + res.msg + '</p></div>');
                }
            },
            error: function() {
                $('#stats-content').html('<div class="layui-text-center" style="padding: 50px;"><p style="color: #ff5722;">加载失败，请重试</p></div>');
            }
        });
        
        function renderStats(data) {
            var html = `
                <div class="layui-row">
                    <div class="layui-col-md3">
                        <div class="stats-item blue">
                            <div class="stats-number">${data.total_contacts || 0}</div>
                            <div class="stats-label">总联系次数</div>
                        </div>
                    </div>
                    <div class="layui-col-md3">
                        <div class="stats-item green">
                            <div class="stats-number">${data.today_contacts || 0}</div>
                            <div class="stats-label">今日联系</div>
                        </div>
                    </div>
                    <div class="layui-col-md3">
                        <div class="stats-item orange">
                            <div class="stats-number">${data.this_week_contacts || 0}</div>
                            <div class="stats-label">本周联系</div>
                        </div>
                    </div>
                    <div class="layui-col-md3">
                        <div class="stats-item red">
                            <div class="stats-number">${data.this_month_contacts || 0}</div>
                            <div class="stats-label">本月联系</div>
                        </div>
                    </div>
                </div>
                
                <div class="layui-card stats-card">
                    <div class="layui-card-header">联系来源分布</div>
                    <div class="layui-card-body">
                        <div class="source-stats">
                            ${renderSourceStats(data.source_stats || [])}
                        </div>
                    </div>
                </div>
                
                <div class="layui-card stats-card">
                    <div class="layui-card-header">最近7天联系趋势</div>
                    <div class="layui-card-body">
                        <div id="daily-chart" class="chart-container"></div>
                    </div>
                </div>
            `;
            
            $('#stats-content').html(html);
            
            // 渲染图表
            if (data.daily_stats && data.daily_stats.length > 0) {
                renderDailyChart(data.daily_stats);
            }
        }
        
        function renderSourceStats(sourceStats) {
            if (!sourceStats || sourceStats.length === 0) {
                return '<p class="layui-text-center">暂无数据</p>';
            }
            
            var colors = ['#1E9FFF', '#FF5722', '#009688', '#FFB800', '#9C27B0'];
            var html = '';
            
            sourceStats.forEach(function(item, index) {
                var color = colors[index % colors.length];
                html += `
                    <div class="source-item" style="background-color: ${color};">
                        <div style="font-size: 18px; font-weight: bold;">${item.count}</div>
                        <div style="font-size: 12px;">${item.source_text}</div>
                    </div>
                `;
            });
            
            return html;
        }
        
        function renderDailyChart(dailyStats) {
            // 这里可以使用 ECharts 或其他图表库来渲染图表
            // 为了简化，这里只显示一个简单的表格
            var html = '<table class="layui-table">';
            html += '<thead><tr><th>日期</th><th>联系次数</th></tr></thead>';
            html += '<tbody>';
            
            dailyStats.forEach(function(item) {
                html += `<tr><td>${item.date}</td><td>${item.count}</td></tr>`;
            });
            
            html += '</tbody></table>';
            
            $('#daily-chart').html(html);
        }
    });
</script>
