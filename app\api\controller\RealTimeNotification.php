<?php

namespace app\api\controller;

use app\common\basics\Api;
use think\facade\Request;
use think\facade\Log;
use think\facade\Cache;

/**
 * 实时WebSocket通知推送控制器
 * 直接与Swoole WebSocket服务器通信，实现真正的实时推送
 */
class RealTimeNotification extends Api
{
    public $like_not_need_login = ['pushNotification', 'testConnection', 'getActiveConnections'];

    /**
     * 实时推送WebSocket通知
     * @ApiTitle (实时推送WebSocket通知)
     * @ApiSummary (直接向活跃的WebSocket连接推送实时通知)
     * @ApiMethod (GET|POST)
     * @ApiParams (
     *   {"name":"target", "type":"string", "require":false, "desc":"推送目标：admin, shop, all，默认为'admin'"},
     *   {"name":"title", "type":"string", "require":false, "desc":"通知标题，默认为'实时通知'"},
     *   {"name":"content", "type":"string", "require":false, "desc":"通知内容，默认为'这是一条实时WebSocket通知'"},
     *   {"name":"type", "type":"string", "require":false, "desc":"通知类型，默认为'admin_notification'"},
     *   {"name":"url", "type":"string", "require":false, "desc":"点击通知跳转的URL，默认为空"},
     *   {"name":"icon", "type":"integer", "require":false, "desc":"通知图标：0-默认，1-成功，2-错误，3-警告，4-信息，默认为1"}
     * )
     * @ApiReturn ({"code":1,"msg":"实时通知推送成功","data":{}})
     */
    public function pushNotification()
    {
        // 获取请求参数
        $target = Request::param('target', 'admin');
        $title = Request::param('title', '实时通知');
        $content = Request::param('content', '这是一条实时WebSocket通知');
        $type = Request::param('type', 'admin_notification');
        $url = Request::param('url', '');
        $icon = Request::param('icon', 1, 'intval');

        Log::info('实时WebSocket通知推送请求: ' . json_encode([
            'target' => $target,
            'title' => $title,
            'content' => $content,
            'type' => $type,
            'url' => $url,
            'icon' => $icon
        ], JSON_UNESCAPED_UNICODE));

        try {
            $result = [];
            
            if ($target === 'admin' || $target === 'all') {
                $result['admin'] = $this->pushToAdminRealTime($title, $content, $type, $url, $icon);
            }
            
            if ($target === 'shop' || $target === 'all') {
                $result['shop'] = $this->pushToShopRealTime($title, $content, $type, $url, $icon);
            }

            return \app\common\server\JsonServer::success('实时通知推送完成', $result);

        } catch (\Exception $e) {
            Log::error('实时通知推送失败: ' . $e->getMessage());
            return \app\common\server\JsonServer::error('实时通知推送失败: ' . $e->getMessage());
        }
    }

    /**
     * 实时推送给admin
     */
    private function pushToAdminRealTime($title, $content, $type, $url, $icon)
    {
        try {
            // 构建通知数据
            $notificationData = [
                'type' => $type,
                'title' => $title,
                'content' => $content,
                'url' => $url,
                'icon' => $icon,
                'timestamp' => time()
            ];

            // 构建符合Redis订阅处理器期望的最终消息格式
            $wsMessage = [
                'event' => 'admin_notification', // 与NotificationListener中的事件匹配
                'data' => $notificationData
            ];

            $messageJson = json_encode($wsMessage, JSON_UNESCAPED_UNICODE);
            Log::info('准备通过Redis发布/订阅推送admin通知: ' . $messageJson);

            // 使用Redis发布/订阅
            $redis = Cache::store('redis');
            $publishResult = $redis->publish('admin_notification', $messageJson);

            $success = $publishResult > 0;
            Log::info("Redis发布到 'admin_notifications' 频道: " . ($success ? "成功，{$publishResult}个订阅者收到" : "失败，没有订阅者"));

            return [
                'success' => $success,
                'method' => 'redis_publish_subscribe',
                'subscribers' => $publishResult,
                'notification_data' => $notificationData
            ];

        } catch (\Exception $e) {
            Log::error('实时推送admin通知异常: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }


    /**
     * 推送给shop
     */
    private function pushToShopRealTime($title, $content, $type, $url, $icon)
    {
        // 类似admin的实现，但针对shop连接
        return [
            'success' => true,
            'message' => 'Shop推送功能待实现'
        ];
    }

    /**
     * 获取活跃的admin连接
     */
    private function getActiveAdminConnections()
    {
        try {
            $redis = Cache::store('redis');
            $prefix = config('default.websocket_prefix', 'socket_');
            
            $connections = [];
            
            // 查找admin连接
            $adminKeys = $redis->keys($prefix . 'admin_*');
            if (is_array($adminKeys)) {
                foreach ($adminKeys as $key) {
                    $value = $redis->get($key);
                    if ($value) {
                        $connections[] = [
                            'key' => $key,
                            'value' => $value,
                            'type' => 'admin'
                        ];
                    }
                }
            }
            
            return [
                'count' => count($connections),
                'connections' => $connections
            ];
            
        } catch (\Exception $e) {
            Log::error('获取活跃admin连接失败: ' . $e->getMessage());
            return [
                'count' => 0,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 测试连接状态
     * @ApiTitle (测试WebSocket连接状态)
     * @ApiSummary (测试与Swoole WebSocket服务器的连接状态)
     * @ApiMethod (GET)
     * @ApiReturn ({"code":1,"msg":"连接测试完成","data":{}})
     */
    public function testConnection()
    {
        try {
            $swooleHost = env('SWOOLE_HOST', '127.0.0.1');
            $swoolePort = env('SWOOLE_PORT', 20211);
            
            // 测试TCP连接
            $client = new \Swoole\Client(SWOOLE_SOCK_TCP);
            $connected = $client->connect($swooleHost, $swoolePort, 1);
            
            if ($connected) {
                $client->close();
                $connectionStatus = [
                    'swoole_server' => [
                        'status' => 'connected',
                        'host' => $swooleHost,
                        'port' => $swoolePort,
                        'message' => 'Swoole服务器连接正常'
                    ]
                ];
            } else {
                $connectionStatus = [
                    'swoole_server' => [
                        'status' => 'failed',
                        'host' => $swooleHost,
                        'port' => $swoolePort,
                        'error' => $client->errMsg,
                        'message' => 'Swoole服务器连接失败'
                    ]
                ];
            }
            
            // 测试Redis连接
            try {
                $redis = Cache::store('redis');
                $redis->ping();
                $connectionStatus['redis'] = [
                    'status' => 'connected',
                    'message' => 'Redis连接正常'
                ];
            } catch (\Exception $e) {
                $connectionStatus['redis'] = [
                    'status' => 'failed',
                    'error' => $e->getMessage(),
                    'message' => 'Redis连接失败'
                ];
            }
            
            // 获取活跃连接信息
            $connectionStatus['active_connections'] = $this->getActiveAdminConnections();
            
            return \app\common\server\JsonServer::success('连接测试完成', $connectionStatus);
            
        } catch (\Exception $e) {
            Log::error('连接测试失败: ' . $e->getMessage());
            return \app\common\server\JsonServer::error('连接测试失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取活跃连接信息
     * @ApiTitle (获取活跃WebSocket连接)
     * @ApiSummary (获取当前活跃的WebSocket连接信息)
     * @ApiMethod (GET)
     * @ApiReturn ({"code":1,"msg":"获取成功","data":{}})
     */
    public function getActiveConnections()
    {
        try {
            $data = [
                'admin' => $this->getActiveAdminConnections(),
                'timestamp' => time(),
                'server_info' => [
                    'host' => env('SWOOLE_HOST', '127.0.0.1'),
                    'port' => env('SWOOLE_PORT', 20211)
                ]
            ];

            return \app\common\server\JsonServer::success('获取活跃连接成功', $data);
        } catch (\Exception $e) {
            Log::error('获取活跃连接失败: ' . $e->getMessage());
            return \app\common\server\JsonServer::error('获取活跃连接失败: ' . $e->getMessage());
        }
    }
}