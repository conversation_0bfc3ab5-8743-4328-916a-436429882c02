<?php

if (!class_exists('Pimple\Container')) {
    require_once __DIR__ . '/../../../src/Pimple/Container.php';
} else {
    echo "pimple-c extension detected, using...\n\n";
}

$time = microtime(true);


$service = function ($arg) { return "I'm a service"; };

for ($i=0; $i<10000; $i++) {

$p = new Pimple\Container;
$p['my_service'] = $service;

$a = $p['my_service'];
$b = $p['my_service'];

}

echo microtime(true) - $time;
?>
