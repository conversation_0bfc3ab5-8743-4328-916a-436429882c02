<?php

namespace app\shopapi\controller;

use app\common\basics\ShopApi;
use app\common\server\JsonServer;
use app\shopapi\logic\LoginLogic;

class User extends ShopApi
{
    
    /**
     * @notes 获取用户信息
     * @return \think\response\Json
     * <AUTHOR>
     * 
     * @date 2023/11/15 15:49
     */
    public function getUser()
    {
        $user_info = LoginLogic::getUser($this->admin_id);
        return JsonServer::success('获取成功', $user_info);
    }
}
