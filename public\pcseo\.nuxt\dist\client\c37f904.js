(window.webpackJsonp=window.webpackJsonp||[]).push([[13,18],{437:function(t,e,r){"use strict";var o=r(17),n=r(2),l=r(3),c=r(136),d=r(27),f=r(18),m=r(271),h=r(52),v=r(135),x=r(270),_=r(5),y=r(98).f,w=r(44).f,N=r(26).f,S=r(438),k=r(439).trim,I="Number",E=n.Number,C=E.prototype,z=n.TypeError,F=l("".slice),T=l("".charCodeAt),A=function(t){var e=x(t,"number");return"bigint"==typeof e?e:M(e)},M=function(t){var e,r,o,n,l,c,d,code,f=x(t,"number");if(v(f))throw z("Cannot convert a Symbol value to a number");if("string"==typeof f&&f.length>2)if(f=k(f),43===(e=T(f,0))||45===e){if(88===(r=T(f,2))||120===r)return NaN}else if(48===e){switch(T(f,1)){case 66:case 98:o=2,n=49;break;case 79:case 111:o=8,n=55;break;default:return+f}for(c=(l=F(f,2)).length,d=0;d<c;d++)if((code=T(l,d))<48||code>n)return NaN;return parseInt(l,o)}return+f};if(c(I,!E(" 0o1")||!E("0b1")||E("+0x1"))){for(var O,R=function(t){var e=arguments.length<1?0:E(A(t)),r=this;return h(C,r)&&_((function(){S(r)}))?m(Object(e),r,R):e},V=o?y(E):"MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,EPSILON,MAX_SAFE_INTEGER,MIN_SAFE_INTEGER,isFinite,isInteger,isNaN,isSafeInteger,parseFloat,parseInt,fromString,range".split(","),j=0;V.length>j;j++)f(E,O=V[j])&&!f(R,O)&&N(R,O,w(E,O));R.prototype=C,C.constructor=R,d(n,I,R)}},438:function(t,e,r){var o=r(3);t.exports=o(1..valueOf)},439:function(t,e,r){var o=r(3),n=r(33),l=r(16),c=r(440),d=o("".replace),f="["+c+"]",m=RegExp("^"+f+f+"*"),h=RegExp(f+f+"*$"),v=function(t){return function(e){var r=l(n(e));return 1&t&&(r=d(r,m,"")),2&t&&(r=d(r,h,"")),r}};t.exports={start:v(1),end:v(2),trim:v(3)}},440:function(t,e){t.exports="\t\n\v\f\r                　\u2028\u2029\ufeff"},441:function(t,e,r){var content=r(444);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,r(14).default)("3181fc86",content,!0,{sourceMap:!1})},442:function(t,e,r){"use strict";r.r(e);r(437),r(80),r(272);var o={data:function(){return{priceSlice:{}}},components:{},props:{firstSize:{type:Number,default:14},secondSize:{type:Number,default:14},color:{type:String},weight:{type:[String,Number],default:400},price:{type:[String,Number],default:""},showSubscript:{type:Boolean,default:!0},subscriptSize:{type:Number,default:14},lineThrough:{type:Boolean,default:!1}},created:function(){this.priceFormat()},watch:{price:function(t){this.priceFormat()}},methods:{priceFormat:function(){var t=this.price,e={};null!==t&&(t=parseFloat(t),t=String(t).split("."),e.first=t[0],e.second=t[1],this.priceSlice=e)}}},n=(r(443),r(9)),component=Object(n.a)(o,(function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("span",{class:(t.lineThrough?"line-through":"")+"price-format",style:{color:t.color,"font-weight":t.weight}},[t.showSubscript?r("span",{style:{"font-size":t.subscriptSize+"px","margin-right":"1px"}},[t._v("¥")]):t._e(),t._v(" "),r("span",{style:{"font-size":t.firstSize+"px","margin-right":"1px"}},[t._v(t._s(t.priceSlice.first))]),t._v(" "),t.priceSlice.second?r("span",{style:{"font-size":t.secondSize+"px"}},[t._v("."+t._s(t.priceSlice.second))]):t._e()])}),[],!1,null,null,null);e.default=component.exports},443:function(t,e,r){"use strict";r(441)},444:function(t,e,r){var o=r(13)(!1);o.push([t.i,".price-format{display:flex;align-items:baseline}",""]),t.exports=o},451:function(t,e,r){var content=r(456);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,r(14).default)("1469a4e1",content,!0,{sourceMap:!1})},455:function(t,e,r){"use strict";r(451)},456:function(t,e,r){var o=r(13)(!1);o.push([t.i,".goods-list[data-v-060944d1]{align-items:stretch}.goods-list .goods-item[data-v-060944d1]{display:block;box-sizing:border-box;width:224px;height:310px;margin-bottom:16px;padding:12px 12px 16px;border-radius:4px;transition:all .2s}.goods-list .goods-item[data-v-060944d1]:hover{transform:translateY(-8px);box-shadow:0 0 6px rgba(0,0,0,.1)}.goods-list .goods-item .goods-img[data-v-060944d1]{width:200px;height:200px}.goods-list .goods-item .name[data-v-060944d1]{margin-bottom:10px;height:40px;line-height:20px}.goods-list .goods-item .seckill .btn[data-v-060944d1]{padding:4px 12px;border-radius:4px;border:1px solid transparent}.goods-list .goods-item .seckill .btn.not-start[data-v-060944d1]{border-color:#ff2c3c;color:#ff2c3c;background-color:transparent}.goods-list .goods-item .seckill .btn.end[data-v-060944d1]{background-color:#e5e5e5;color:#fff}",""]),t.exports=o},462:function(t,e,r){"use strict";r.r(e);r(437);var o={props:{list:{type:Array,default:function(){return[]}},num:{type:Number,default:5},type:{type:String},status:{type:Number}},watch:{list:{immediate:!0,handler:function(t){}}},computed:{getSeckillText:function(){switch(this.status){case 0:return"未开始";case 1:return"立即抢购";case 2:return"已结束"}}}},n=(r(455),r(9)),component=Object(n.a)(o,(function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",{staticClass:"goods-list flex flex-wrap"},t._l(t.list,(function(e,o){return r("nuxt-link",{key:o,staticClass:"goods-item bg-white",style:{marginRight:(o+1)%t.num==0?0:"14px"},attrs:{to:"/goods_details/"+(e.id||e.goods_id)}},[r("el-image",{staticClass:"goods-img",attrs:{lazy:"",src:e.image||e.goods_image,alt:""}}),t._v(" "),r("div",{staticClass:"name line-2"},[t._v(t._s(e.name||e.goods_name))]),t._v(" "),"seckill"==t.type?r("div",{staticClass:"seckill flex row-between"},[r("div",{staticClass:"primary flex"},[t._v("\n                秒杀价\n                "),r("price-formate",{attrs:{price:e.seckill_price,"first-size":18}})],1),t._v(" "),r("div",{class:["btn bg-primary white",{"not-start":0==t.status,end:2==t.status}]},[t._v(t._s(t.getSeckillText)+"\n            ")])]):r("div",{staticClass:"flex row-between flex-wrap"},[r("div",{staticClass:"price flex col-baseline"},[r("div",{staticClass:"primary m-r-8"},[r("price-formate",{attrs:{price:e.min_price||e.price,"first-size":16}})],1),t._v(" "),r("div",{staticClass:"muted sm line-through"},[r("price-formate",{attrs:{price:e.market_price}})],1)]),t._v(" "),r("div",{staticClass:"muted xs"},[t._v(t._s(e.sales_total||e.sales_sum||0)+"人购买")])])],1)})),1)}),[],!1,null,"060944d1",null);e.default=component.exports;installComponents(component,{PriceFormate:r(442).default})}}]);