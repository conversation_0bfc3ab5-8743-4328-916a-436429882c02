{layout name="layout1" /}

<div class="wrapper">
    <div class="layui-card">
        <!-- 搜索区域 -->
        <div class="layui-card-body layui-form">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label for="order_sn" class="layui-form-label">订单编号：</label>
                    <div class="layui-inline" style="margin-right:0;">
                        <div class="layui-input-inline" >
                            <input type="text" id="order_sn" name="order_sn" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                </div>

                <div class="layui-inline">
                    <label for="nickname" class="layui-form-label">购买人：</label>
                    <div class="layui-inline" style="margin-right:0;">
                        <div class="layui-input-inline" >
                            <input type="text" id="nickname" name="nickname" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                </div>
                <div class="layui-inline">
                    <label for="datetime" class="layui-form-label">支付时间：</label>
                    <div class="layui-inline" style="margin-right:0;">
                        <div class="layui-input-inline" >
                            <input type="text" id="datetime" name="datetime" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                </div>
                <div class="layui-inline">
                    <a class="layui-btn layui-btn-sm layui-btn-normal" lay-submit lay-filter="search">搜索</a>
                    <a class="layui-btn layui-btn-sm layui-btn-primary" lay-submit lay-filter="clear-search">重置</a>
                </div>
            </div>
        </div>

        <!-- 主体区域 -->
        <div class="layui-card-body">
            <div class="layui-tab layui-tab-card" lay-filter="like-tab">
                <ul class="layui-tab-title">
                    <li lay-id="100" class="layui-this">全部集采众筹({$recordStatistics.total})</li>
                    <li lay-id="0">集采众筹中({$recordStatistics.stayStatus})</li>
                    <li lay-id="1">集采众筹成功({$recordStatistics.successStatus})</li>
                    <li lay-id="2">集采众筹失败({$recordStatistics.failStatus})</li>
                </ul>
                <div class="layui-tab-content" style="padding:20px;">
                    <table id="like-table-lists" lay-filter="like-table-lists"></table>

                    <script type="text/html" id="table-operation">
                        <a class="layui-btn layui-btn-sm layui-btn-primary" lay-event="viewOrder">查看订单</a>
                    </script>
                </div>
            </div>
        </div>

    </div>
</div>

<script>
    layui.use(["table", "form", "element", "laydate"], function(){
        var table   = layui.table;
        var form    = layui.form;
        var element = layui.element;
        var laydate = layui.laydate;

        laydate.render({elem:"#datetime", range: true, trigger:"click"});

        like.tableLists("#like-table-lists", "{:url()}?jcai_activity_id={$jcai_activity_id}", [
            {field:"id", width:60, align:"center", title:"ID"}
            ,{field:"order_sn", width:180, align:"center", title:"订单编号"}
            ,{title:"购买人", width:200, templet: function(d){
                return '<img src="'+d.avatar+'" alt="图" style="width:50px;height:50px;"><div class="layui-inline"><p>用户编号：'+d.sn+'</p><p>用户昵称：'+d.nickname+'</p></div>';
            }}
            ,{title:"商品信息", width:200, templet: function(d){
                return '<img src="'+d.image+'" alt="图" style="width:50px;height:50px;"><div class="layui-inline"><p>'+d.goods_name+'</p><p>'+d.spec_value+'</p></div>';
            }}
            ,{field:"goods_num", width:80, align:"center", title:"购买数量"}
            ,{field:"order_amount", width:100, align:"center", title:"订单总额"}
            ,{field:"pay_time", width:160, align:"center", title:"支付时间"}
            ,{field:"shipping_status_text", width:100, align:"center", title:"发货状态"}
            ,{field:"status_text", width:100, align:"center", title:"众筹状态"}
            ,{title:"操作", width:100, align:"center", fixed:"right", toolbar:"#table-operation"}
        ]);


        var active = {
            viewOrder: function (obj) {
                layer.open({
                    type: 2
                    ,title: "订单详情"
                    ,content: "{:url('order.Order/detail')}?id="+obj.data.order_id
                    ,area: ["80%", "90%"]
                });
            },
            statistics: function () {
                like.ajax({
                    url: "{:url('jcai.Activity/recordStatistics')}?id={$jcai_activity_id}&shop_id={$shop_id}",
                    data: {},
                    type: "GET",
                    success:function(res) {
                        if(res.code === 1) {
                            $(".layui-tab-title li[lay-id=100]").html("全部集采众筹("+res.data.total+")");
                            $(".layui-tab-title li[lay-id=0]").html("集采众筹中("+res.data.stayStatus+")");
                            $(".layui-tab-title li[lay-id=1]").html("集采众筹成功("+res.data.successStatus+")");
                            $(".layui-tab-title li[lay-id=2]").html("集采众筹失败("+res.data.failStatus+")");
                        }
                    }
                });
            }
        };
        like.eventClick(active);


        element.on("tab(like-tab)", function(){
            active.statistics();
            var type = this.getAttribute("lay-id");
            table.reload("like-table-lists", {
                where: {type: type},
                page: {
                    curr: 1
                }
            });
        });


        form.on("submit(search)", function(data){
            table.reload("like-table-lists", {
                where: data.field,
                page: {
                    curr: 1
                }
            });
        });


        form.on("submit(clear-search)", function(){
            $("#order_sn").val("");
            $("#nickname").val("");
            $("#datetime").val("");
            table.reload("like-table-lists", {
                where: {},
                page: {
                    curr: 1
                }
            });
        });


    })
</script>