<?php

namespace app\admin\logic\goods;

use app\common\model\goods\Qualification as QualificationModel;

/**
 * 资质管理逻辑层
 * Class QualificationLogic
 * @package app\admin\logic\goods
 */
class QualificationLogic
{
    /**
     * 获取资质列表
     */
    public static function lists($get)
    {
        $where = ['del' => 0];

        // 搜索条件
        if (!empty($get['name'])) {
            $where[] = ['name', 'like', '%' . $get['name'] . '%'];
        }
        if (isset($get['status']) && $get['status'] !== '') {
            $where['status'] = $get['status'];
        }

        $result = QualificationModel::where($where)
            ->order('sort', 'asc')
            ->order('id', 'desc')
            ->paginate([
                'list_rows' => $get['limit'] ?? 15,
                'page' => $get['page'] ?? 1
            ]);

        // 处理数据
        $lists = $result->getCollection();
        foreach ($lists as $item) {
            $item['valid_days_text'] = $item['valid_days'] == 0 ? '永久有效' : $item['valid_days'] . '天';
            $item['status_text'] = $item['status'] == 1 ? '启用' : '禁用';
            $item['create_time'] = is_numeric($item['create_time']) && $item['create_time'] > 0
                ? date('Y-m-d H:i:s', $item['create_time'])
                : $item['create_time'];
        }

        return ['count' => $result->total(), 'lists' => $lists];
    }

    /**
     * 搜索资质（用于分类关联）
     */
    public static function search($keyword = '')
    {
        // $where = ['del' => 0, 'status' => 1];

        if (!empty($keyword) && is_string($keyword)) {
            $where[] = ['name', 'like', '%' . trim($keyword) . '%'];
        }

        $lists = QualificationModel::where($where)
            ->field('id,name,description,valid_days')
            ->order('sort', 'asc')
            ->limit(20)
            ->select();

        // 转换为数组并利用模型访问器
        $result = [];
        foreach ($lists as $item) {
            $result[] = [
                'id' => $item['id'],
                'name' => $item['name'],
                'description' => $item['description'],
                'valid_days' => $item['valid_days'],
                'valid_days_text' => $item['valid_days_text'] // 使用模型访问器
            ];
        }

        return $result;
    }

    /**
     * 获取分类树数据（用于绑定）
     */
    public static function getCategoryTree($qualificationId)
    {
        // 获取所有分类
        $categories = \app\common\model\goods\GoodsCategory::where('del', 0)
            ->field('id,name,pid,level')
            ->order('sort', 'asc')
            ->select()
            ->toArray();

        // 调试信息
        \think\facade\Log::info('分类数据查询结果: ' . json_encode($categories));

        // 获取已绑定的分类ID
        $boundCategoryIds = \app\common\model\goods\GoodsCategoryQualification::where('qualification_id', $qualificationId)
            ->column('category_id');

        \think\facade\Log::info('已绑定分类ID: ' . json_encode($boundCategoryIds));

        // 如果没有分类数据，返回空数组
        if (empty($categories)) {
            return [];
        }

        // 构建树形结构
        $tree = self::buildCategoryTree($categories, 0, $boundCategoryIds);

        \think\facade\Log::info('构建的树形结构: ' . json_encode($tree));

        return $tree;
    }

    /**
     * 构建分类树
     */
    private static function buildCategoryTree($categories, $pid = 0, $boundIds = [])
    {
        $tree = [];
        foreach ($categories as $category) {
            if ($category['pid'] == $pid) {
                $node = [
                    'id' => $category['id'],
                    'title' => $category['name'],
                    'checked' => in_array($category['id'], $boundIds),
                    'spread' => true, // 默认展开
                ];

                // 递归获取子分类
                $children = self::buildCategoryTree($categories, $category['id'], $boundIds);
                if (!empty($children)) {
                    $node['children'] = $children;
                }

                $tree[] = $node;
            }
        }
        return $tree;
    }

    /**
     * 保存绑定关系
     */
    public static function saveBinding($qualificationId, $categoryIds)
    {
        try {
            // 删除原有绑定关系
            \app\common\model\goods\GoodsCategoryQualification::where('qualification_id', $qualificationId)->delete();

            // 添加新的绑定关系
            if (!empty($categoryIds)) {
                $data = [];
                foreach ($categoryIds as $categoryId) {
                    $data[] = [
                        'qualification_id' => $qualificationId,
                        'category_id' => $categoryId,
                        'create_time' => time()
                    ];
                }
                \app\common\model\goods\GoodsCategoryQualification::insertAll($data);
            }

            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 添加资质
     */
    public static function add($post)
    {
        $post['create_time'] = time();
        $post['update_time'] = time();
        unset($post['file']);
        return QualificationModel::create($post);
    }

    /**
     * 编辑资质
     */
    public static function edit($post)
    {
        $post['update_time'] = time();
        unset($post['file']);
        return QualificationModel::where('id', $post['id'])->update($post);
    }

    /**
     * 资质详情
     */
    public static function detail($id)
    {
        return QualificationModel::where(['id' => $id, 'del' => 0])->find();
    }

    /**
     * 删除资质
     */
    public static function del($id)
    {
        return QualificationModel::where('id', $id)->update(['del' => 1, 'update_time' => time()]);
    }

    /**
     * 状态切换
     */
    public static function status($post)
    {
        $data = [
            $post['field'] => $post['value'],
            'update_time' => time()
        ];
        return QualificationModel::where('id', $post['id'])->update($data);
    }

    /**
     * 获取所有启用的资质
     */
    public static function getAllEnabled()
    {
        return QualificationModel::where(['del' => 0, 'status' => 1])
            ->field('id,name,description,valid_days')
            ->order('sort', 'asc')
            ->select()
            ->toArray();
    }

    /**
     * 切换是否必传状态
     */
    public static function switchRequired($id, $is_required)
    {
        $data = [
            'is_required' => intval($is_required),
            'update_time' => time()
        ];

        return QualificationModel::where('id', $id)->update($data);
    }
}
