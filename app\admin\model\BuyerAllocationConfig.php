<?php

namespace app\admin\model;

use app\common\model\BaseModel;

class BuyerAllocationConfig extends BaseModel
{
    protected $name = 'buyer_allocation_config';

    /**
     * @notes 获取配置列表
     * @param $params
     * @return array
     */
    public static function lists($params): array
    {
        $model = new static();
        if (isset($params['tier_level']) && $params['tier_level'] !== '') {
            $model->where('tier_level', '=', $params['tier_level']);
        }

        $lists = $model->order('tier_level', 'asc')
            ->paginate([
                'list_rows' => $params['limit'],
                'page' => $params['page']
            ])->toArray();

        return [
            'count' => $lists['total'],
            'lists' => $lists['data']
        ];
    }
}
