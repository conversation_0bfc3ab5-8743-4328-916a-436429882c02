<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Layui视频上传测试</title>
    <link rel="stylesheet" href="/static/plug/layui-admin/dist/layuiadmin/layui/css/layui.css">
    <style>
        body { padding: 20px; font-family: Arial, sans-serif; }
        .container { max-width: 800px; margin: 0 auto; }
        .video-container { 
            border: 2px dashed #ccc; 
            padding: 20px; 
            margin: 20px 0; 
            border-radius: 8px;
            background: #f9f9f9;
        }
        .like-upload-video { 
            text-align: center; 
            padding: 20px;
            background: #fff;
            border-radius: 4px;
        }
        .upload-video-a { 
            display: inline-block;
            padding: 10px 20px;
            background: #1890ff;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .upload-video-a:hover { background: #40a9ff; }
        
        .show-video { 
            background: #fff;
            padding: 20px;
            border-radius: 4px;
            margin-top: 10px;
            position: relative;
        }
        .goods-video-del-x {
            position: absolute;
            top: 5px;
            right: 5px;
            background: #ff4d4f;
            color: white;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            text-align: center;
            line-height: 20px;
            cursor: pointer;
            font-size: 12px;
        }
        .goods-video-del-x:hover { background: #ff7875; }
        
        .test-log { 
            background: #f0f0f0; 
            padding: 15px; 
            margin: 20px 0; 
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .success { color: #52c41a; }
        .error { color: #ff4d4f; }
        .info { color: #1890ff; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎥 Layui视频上传测试</h1>
        
        <div class="video-container" id="videoContainer">
            <div class="like-upload-video">
                <a class="upload-video-a" id="video">+ 添加视频</a>
            </div>
        </div>
        
        <div style="text-align: center; margin: 20px 0;">
            <button class="layui-btn layui-btn-normal" onclick="testVideoUpload()">🧪 测试视频上传功能</button>
            <button class="layui-btn" onclick="checkState()" style="background: #52c41a; color: white;">🔍 检查状态</button>
            <button class="layui-btn" onclick="clearLog()" style="background: #666; color: white;">🧹 清空日志</button>
        </div>
        
        <div class="test-log" id="testLog">等待测试开始...</div>
    </div>

    <script src="/static/plug/layui-admin/dist/layuiadmin/layui/layui.js"></script>
    <script>
        // 日志函数
        function log(message, type) {
            type = type || 'info';
            var timestamp = new Date().toLocaleTimeString();
            var logElement = document.getElementById('testLog');
            var className = type === 'success' ? 'success' : (type === 'error' ? 'error' : 'info');
            logElement.innerHTML += '<span class="' + className + '">[' + timestamp + '] ' + message + '</span>\n';
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        // 检查当前状态
        function checkState() {
            log('=== 当前状态检查 ===', 'info');
            
            var container = document.getElementById('videoContainer');
            var uploadDiv = container.querySelector('.like-upload-video, .upload-video-div');
            var showVideo = container.querySelector('.show-video');
            
            log('容器子元素数量: ' + container.children.length, 'info');
            log('上传按钮存在: ' + (uploadDiv ? '是' : '否'), uploadDiv ? 'success' : 'error');
            log('视频显示区域存在: ' + (showVideo ? '是' : '否'), showVideo ? 'info' : 'success');
            
            if (uploadDiv) {
                log('上传按钮可见: ' + (uploadDiv.style.display !== 'none' ? '是' : '否'), 
                    uploadDiv.style.display !== 'none' ? 'success' : 'error');
                log('上传按钮类名: ' + uploadDiv.className, 'info');
            }
            
            log('=== 状态检查完成 ===', 'info');
        }
        
        // 清空日志
        function clearLog() {
            document.getElementById('testLog').innerHTML = '日志已清空\n';
        }
        
        // 测试视频上传功能
        function testVideoUpload() {
            log('开始测试视频上传功能', 'info');
            
            // 检查like对象是否存在
            if (typeof like === 'undefined') {
                log('❌ like对象不存在，无法测试', 'error');
                return;
            }
            
            // 检查videoUpload方法是否存在
            if (typeof like.videoUpload !== 'function') {
                log('❌ like.videoUpload方法不存在，无法测试', 'error');
                return;
            }
            
            log('✅ like对象和videoUpload方法都存在', 'success');
            
            // 初始化视频上传
            try {
                like.videoUpload('#video', '/shop/Upload/video');
                log('✅ 视频上传初始化成功', 'success');
            } catch (e) {
                log('❌ 视频上传初始化失败: ' + e.message, 'error');
            }
        }
        
        // 初始化layui
        layui.use(['layer', 'upload'], function() {
            var layer = layui.layer;
            var upload = layui.upload;
            
            // 将layui模块暴露到全局
            window.layer = layer;
            window.upload = upload;
            
            log('✅ Layui模块加载完成', 'success');
            
            // 加载like模块
            layui.use(['like'], function() {
                var like = layui.like;
                window.like = like;
                
                log('✅ Like模块加载完成', 'success');
                
                // 自动初始化视频上传
                testVideoUpload();
                
                // 初始状态检查
                setTimeout(function() {
                    checkState();
                }, 500);
                
            }, function() {
                log('❌ Like模块加载失败，尝试手动加载', 'error');
                
                // 手动加载like.js
                var script = document.createElement('script');
                script.src = '/static/plug/layui-admin/dist/layuiadmin/modules/like.js';
                script.onload = function() {
                    log('✅ Like.js手动加载成功', 'success');
                };
                script.onerror = function() {
                    log('❌ Like.js手动加载失败', 'error');
                };
                document.head.appendChild(script);
            });
        });
        
        // 监听DOM变化
        var observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList') {
                    log('🔍 检测到DOM变化: ' + mutation.addedNodes.length + '个节点添加, ' + 
                        mutation.removedNodes.length + '个节点删除', 'info');
                }
            });
        });
        
        observer.observe(document.getElementById('videoContainer'), {
            childList: true,
            subtree: true
        });
        
        log('🔧 页面初始化完成', 'success');
    </script>
</body>
</html>
