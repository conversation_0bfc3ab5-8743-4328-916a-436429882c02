<?php
namespace  app\admin\controller\wechat;

use app\common\basics\AdminBase;
use app\admin\logic\wechat\OaLogic;
use app\common\server\JsonServer;
use app\common\server\ConfigServer;

class Oa extends AdminBase
{
    /**
     * 公众号设置
     */
    public function setOa()
    {
        if($this->request->isAjax()){
            $post = $this->request->post();
            if(isset($post['qr_code']) && !empty($post['qr_code'])) {
                $domain = $this->request->domain();
                $post['qr_code'] = str_replace($domain, '', $post['qr_code']);
            }else{
                $post['qr_code'] = '';
            }
            OaLogic::setOa($post);
            return JsonServer::success('设置成功');
        }
        $oa = OaLogic::getOa();
        return view('setoa', ['oa' => $oa]);
    }

    /**
     * 菜单管理
     */
    public function oaMenu()
    {
        $wechat_menu = ConfigServer::get('menu', 'wechat_menu',[]);
        return view('oamenu', ['menu' => $wechat_menu]);
    }

    /**
     * 发布菜单
     */
    public function pulishMenu()
    {
        $menu = $this->request->post('button');
        if(empty($menu)){
            return JsonServer::error('请设置菜单');
        }
        $result = OaLogic::pulishMenu($menu);
        if($result){
            return JsonServer::success('菜单发布成功');
        }
        return JsonServer::error(OaLogic::getError());
    }

    /**
     * 进采购群功能配置页面
     */
    public function purchaseGroup()
    {
        // 获取现有的进采购群回复配置
        $reply = \app\common\model\wechat\WechatReply::where([
            'keyword' => '进采购群',
            'del' => 0
        ])->find();

        return view('purchase_group', [
            'reply' => $reply ? $reply->toArray() : []
        ]);
    }

    /**
     * 保存进采购群配置
     */
    public function savePurchaseGroup()
    {
        $post = $this->request->post();

        try {
            // 查找现有记录
            $reply = \app\common\model\wechat\WechatReply::where([
                'keyword' => '进采购群',
                'del' => 0
            ])->find();

            $data = [
                'name' => '进采购群回复',
                'keyword' => '进采购群',
                'reply_type' => 'text',
                'matching_type' => 1, // 全匹配
                'content_type' => 3, // 文本+图片
                'content' => $post['content'],
                'message_count' => 2,
                'second_content' => '采购群二维码',
                'second_content_type' => 2, // 图片
                'second_image_url' => $post['second_image_url'],
                'status' => $post['status'],
                'sort' => 1,
                'update_time' => time()
            ];

            if ($reply) {
                // 更新现有记录
                $result = $reply->save($data);
            } else {
                // 创建新记录
                $data['create_time'] = time();
                $data['del'] = 0;
                $result = \app\common\model\wechat\WechatReply::create($data);
            }

            if ($result) {
                return JsonServer::success('保存成功');
            } else {
                return JsonServer::error('保存失败');
            }

        } catch (\Exception $e) {
            return JsonServer::error('保存失败：' . $e->getMessage());
        }
    }
}