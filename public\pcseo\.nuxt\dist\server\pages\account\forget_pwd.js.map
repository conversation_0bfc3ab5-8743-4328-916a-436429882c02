{"version": 3, "file": "pages/account/forget_pwd.js", "sources": ["webpack:///./components/count-down.vue?4f61", "webpack:///./utils/parseTime.js", "webpack:///./components/count-down.vue", "webpack:///./components/count-down.vue?a8c1", "webpack:///./components/count-down.vue?1b2a", "webpack:///./utils/type.js", "webpack:///./pages/account/forget_pwd.vue?28f8", "webpack:///./pages/account/forget_pwd.vue?0b0a", "webpack:///./pages/account/forget_pwd.vue?a977", "webpack:///./pages/account/forget_pwd.vue?ae1b", "webpack:///./pages/account/forget_pwd.vue", "webpack:///./pages/account/forget_pwd.vue?c8eb", "webpack:///./pages/account/forget_pwd.vue?35b6"], "sourcesContent": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return (_vm.time >= 0)?_c('div',[_c('client-only',[(_vm.isSlot)?_vm._t(\"default\"):_c('span',[_vm._v(_vm._s(_vm.formateTime))])],2)],1):_vm._e()}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "\n\nconst SECOND = 1000;\nconst MINUTE = 60 * SECOND;\nconst HOUR = 60 * MINUTE;\nconst DAY = 24 * HOUR;\nexport function parseTimeData(time) {\n    const days = Math.floor(time / DAY);\n    const hours = sliceTwo(Math.floor((time % DAY) / HOUR));\n    const minutes = sliceTwo(Math.floor((time % HOUR) / MINUTE));\n    const seconds = sliceTwo(Math.floor((time % MINUTE) / SECOND));\n    return {\n        days: days,\n        hours: hours,\n        minutes: minutes,\n        seconds: seconds,\n    };\n}\n\nfunction sliceTwo(str) {\n    return (0 + str.toString()).slice(-2)\n}\n\nexport  function parseFormat(format, timeData) {\n    let days = timeData.days;\n    let hours = timeData.hours, minutes = timeData.minutes, seconds = timeData.seconds\n    if (format.indexOf('dd') !== -1) {\n        format = format.replace('dd', days);\n    }\n    if (format.indexOf('hh') !== -1) {\n        format = format.replace('hh', sliceTwo(hours) );\n    }\n    if (format.indexOf('mm') !== -1) {\n        format = format.replace('mm', sliceTwo(minutes));\n    }\n    if (format.indexOf('ss') !== -1) {\n        format = format.replace('ss', sliceTwo(seconds));\n    }\n    return format\n}", "//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { parseTimeData, parseFormat } from '~/utils/parseTime'\nexport default {\n    components: {},\n    props: {\n        isSlot: {\n            type: Boolean,\n            default: false,\n        },\n        time: {\n            type: Number,\n            default: 0,\n        },\n        format: {\n            type: String,\n            default: 'hh:mm:ss',\n        },\n        autoStart: {\n            type: Boolean,\n            default: true,\n        },\n    },\n    watch: {\n        time: {\n            immediate: true,\n            handler(value) {\n                if (value) {\n                    this.reset()\n                }\n            },\n        },\n    },\n    data() {\n        return {\n            timeObj: {},\n            formateTime: 0,\n        }\n    },\n    created() {},\n    computed: {},\n    methods: {\n        createTimer(fn) {\n            return setTimeout(fn, 100)\n        },\n        isSameSecond(time1, time2) {\n            return Math.floor(time1) === Math.floor(time2)\n        },\n        start() {\n            if (this.counting) {\n                return\n            }\n            this.counting = true\n            this.endTime = Date.now() + this.remain * 1000\n            this.setTimer()\n        },\n        setTimer() {\n            this.tid = this.createTimer(() => {\n                let remain = this.getRemain()\n                if (!this.isSameSecond(remain, this.remain) || remain === 0) {\n                    this.setRemain(remain)\n                }\n                if (this.remain !== 0) {\n                    this.setTimer()\n                }\n            })\n        },\n        getRemain() {\n            return Math.max(this.endTime - Date.now(), 0)\n        },\n        pause() {\n            this.counting = false\n            clearTimeout(this.tid)\n        },\n        reset() {\n            this.pause()\n            this.remain = this.time\n            this.setRemain(this.remain)\n            if (this.autoStart) {\n                this.start()\n            }\n        },\n        setRemain(remain) {\n            const { format } = this\n            this.remain = remain\n            const timeData = parseTimeData(remain)\n            this.formateTime = parseFormat(format, timeData)\n            this.$emit('change', timeData)\n            if (remain === 0) {\n                this.pause()\n                this.$emit('finish')\n            }\n        },\n    },\n}\n", "import mod from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./count-down.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./count-down.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./count-down.vue?vue&type=template&id=2fbaab86&\"\nimport script from \"./count-down.vue?vue&type=script&lang=js&\"\nexport * from \"./count-down.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  \n}\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  null,\n  \"4090b4e2\"\n  \n)\n\nexport default component.exports", "export const client = 5\n\nexport const loginType = {\n    SMS: 0,\n    ACCOUNT: 1\n}\n\n\n// 短信发送\nexport const SMSType = {\n    // 注册\n    REGISTER: 'ZCYZ',\n    // 找回密码\n    FINDPWD: 'ZHMM',\n    // 登陆\n    LOGIN: 'YZMDL',\n    // 商家申请入驻\n    SJSQYZ: 'SJSQYZ',\n    // 更换手机号\n    CHANGE_MOBILE: 'BGSJHM',\n    // 绑定手机号\n    BIND: 'BDSJHM'\n}\n\nexport const FieldType = {\n    NONE: '',\n    SEX: 'sex',\n    NICKNAME: 'nickname',\n    AVATAR: 'avatar',\n    MOBILE: 'mobile'\n}\n\n\n// 售后状态\nexport const AfterSaleType = {\n    // 售后申请 \n    NORMAL: 'normal',\n    // 处理中\n    HANDLING: 'apply',\n    // 已处理\n    FINISH: 'finish'\n}\n", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../../node_modules/sass-resources-loader/lib/loader.js??ref--7-oneOf-1-4!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./forget_pwd.vue?vue&type=style&index=0&id=11daf7c4&lang=scss&scoped=true&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"91c90b32\", content, true, context)\n};", "export * from \"-!../../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../../node_modules/sass-resources-loader/lib/loader.js??ref--7-oneOf-1-4!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./forget_pwd.vue?vue&type=style&index=0&id=11daf7c4&lang=scss&scoped=true&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".forget-pwd-container[data-v-11daf7c4]{flex:1}.forget-pwd-container .forget-pwd-box[data-v-11daf7c4]{padding-top:40px;padding-bottom:55px;width:880px;border:1px solid #e5e5e5}.forget-pwd-container .forget-pwd-box .forget-pwd-title[data-v-11daf7c4]{font-size:24px}.forget-pwd-container .forget-pwd-box .form-box .forget-form-item[data-v-11daf7c4]{margin-top:24px}.forget-pwd-container .forget-pwd-box .form-box .form-input[data-v-11daf7c4]{width:400px}.forget-pwd-container .forget-pwd-box .form-box .verify-code-img[data-v-11daf7c4]{width:100px;height:40px;margin-left:26px;background-color:red}.forget-pwd-container .forget-pwd-box .form-box .sms-btn[data-v-11daf7c4]{margin-left:16px;height:40px;width:120px}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"forget-pwd-container flex-col row-center col-center\"},[_vm._ssrNode(\"<div class=\\\"forget-pwd-box flex-col col-center bg-white\\\" data-v-11daf7c4>\",\"</div>\",[_vm._ssrNode(\"<div class=\\\"forget-pwd-title\\\" data-v-11daf7c4>忘记密码</div> \"),_c('el-form',{staticClass:\"form-box flex-col\"},[_c('div',{staticClass:\"forget-form-item\"},[_c('el-input',{staticClass:\"form-input\",attrs:{\"placeholder\":\"请输入手机号码\"},model:{value:(_vm.telephone),callback:function ($$v) {_vm.telephone=$$v},expression:\"telephone\"}},[_c('i',{staticClass:\"el-icon-user\",staticStyle:{\"font-size\":\"18px\"},attrs:{\"slot\":\"prepend\"},slot:\"prepend\"})])],1),_vm._v(\" \"),_c('div',{staticClass:\"forget-form-item flex\"},[_c('el-input',{staticClass:\"form-input\",staticStyle:{\"width\":\"264px\"},attrs:{\"placeholder\":\"短信验证码\"},model:{value:(_vm.smsCode),callback:function ($$v) {_vm.smsCode=$$v},expression:\"smsCode\"}},[_c('i',{staticClass:\"el-icon-lock\",staticStyle:{\"font-size\":\"18px\"},attrs:{\"slot\":\"prepend\"},slot:\"prepend\"})]),_vm._v(\" \"),_c('el-button',{staticClass:\"sms-btn\",on:{\"click\":_vm.sendSMSCode}},[(_vm.canSend)?_c('div',[_vm._v(\"获取验证码\")]):_c('count-down',{attrs:{\"time\":60,\"format\":\"ss秒\",\"autoStart\":\"\"},on:{\"finish\":function($event){_vm.canSend = true}}})],1)],1),_vm._v(\" \"),_c('div',{staticClass:\"forget-form-item\"},[_c('el-input',{attrs:{\"placeholder\":\"请输入密码 (数字与字母自由组合)\",\"show-password\":\"\"},model:{value:(_vm.password),callback:function ($$v) {_vm.password=$$v},expression:\"password\"}},[_c('i',{staticClass:\"el-icon-more-outline\",staticStyle:{\"font-size\":\"18px\"},attrs:{\"slot\":\"prepend\"},slot:\"prepend\"})])],1),_vm._v(\" \"),_c('div',{staticClass:\"forget-form-item\"},[_c('el-input',{attrs:{\"placeholder\":\"再次输入密码\",\"show-password\":\"\"},model:{value:(_vm.againPwd),callback:function ($$v) {_vm.againPwd=$$v},expression:\"againPwd\"}},[_c('i',{staticClass:\"el-icon-key\",staticStyle:{\"font-size\":\"18px\"},attrs:{\"slot\":\"prepend\"},slot:\"prepend\"})])],1),_vm._v(\" \"),_c('div',{staticClass:\"flex-col\",staticStyle:{\"margin-top\":\"46px\"}},[_c('el-button',{attrs:{\"type\":\"primary\"},on:{\"click\":_vm.forgetFun}},[_vm._v(\"确定\")])],1)])],2)])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { SMSType, client } from '@/utils/type'\nimport CountDown from '~/components/count-down'\nexport default {\n    head() {\n        return {\n            title: this.$store.getters.headTitle,\n            link: [\n                {\n                    rel: 'icon',\n                    type: 'image/x-icon',\n                    href: this.$store.getters.favicon,\n                },\n            ],\n        }\n    },\n    layout: 'main',\n    components: {\n        CountDown,\n    },\n    data() {\n        return {\n            telephone: '',\n            smsCode: '',\n            canSend: true,\n            password: '',\n            againPwd: '',\n        }\n    },\n    methods: {\n        async forgetFun() {\n            if (!this.telephone) {\n                this.$message({\n                    message: '请输入手机号码',\n                    type: 'error',\n                })\n                return\n            } else if (!this.smsCode) {\n                this.$message({\n                    message: '请输入验证码',\n                    type: 'error',\n                })\n                return\n            } else if (!this.password) {\n                this.$message({\n                    message: '请输入密码信息',\n                    type: 'error',\n                })\n                return\n            } else if (this.password != this.againPwd) {\n                this.$message({\n                    message: '两次输入密码不一致',\n                    type: 'error',\n                })\n                return\n            }\n            let res = await this.$post('login_password/forget', {\n                mobile: this.telephone,\n                code: this.smsCode,\n                password: this.password,\n                repassword: this.againPwd,\n                client: client,\n            })\n            if (res.code == 1) {\n                this.$message({\n                    message: '修改成功',\n                    type: 'success',\n                })\n                let time = setTimeout(() => {\n                    this.$router.replace('/account/login')\n                    clearTimeout(time)\n                }, 1000)\n            }\n        },\n        async sendSMSCode() {\n            if (!this.telephone) {\n                this.$message({\n                    message: '请输入手机号码',\n                    type: 'error',\n                })\n                return\n            }\n            let res = await this.$post('sms/send', {\n                mobile: this.telephone,\n                key: SMSType.FINDPWD,\n            })\n            if (res.code == 1) {\n                this.canSend = false\n                this.$message({\n                    message: '发送成功',\n                    type: 'success',\n                })\n            }\n        },\n    },\n}\n", "import mod from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./forget_pwd.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./forget_pwd.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./forget_pwd.vue?vue&type=template&id=11daf7c4&scoped=true&\"\nimport script from \"./forget_pwd.vue?vue&type=script&lang=js&\"\nexport * from \"./forget_pwd.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./forget_pwd.vue?vue&type=style&index=0&id=11daf7c4&lang=scss&scoped=true&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  \"11daf7c4\",\n  \"76962ec2\"\n  \n)\n\nexport default component.exports\n\n/* nuxt-component-imports */\ninstallComponents(component, {CountDown: require('/Users/<USER>/Desktop/vue/pc/components/count-down.vue').default})\n"], "mappings": ";;;;;;;;;;;AAAA;AACA;AACA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAJA;AAMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;;ACvCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAbA;AAkBA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAPA;AADA;AACA;AASA;AACA;AACA;AACA;AAFA;AAIA;AACA;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AApDA;AAtCA;;ACXA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACrBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;AACA;AAFA;AACA;AAMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAZA;AAeA;AACA;AACA;AACA;AACA;AACA;AALA;AACA;AASA;AACA;AACA;AACA;AACA;AACA;AACA;AANA;;;;;;;AClCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACXA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACNA;AACA;AACA;;;;;;;;;;;ACFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAHA;AAHA;AAUA;AACA;AAAA;AACA;AACA;AADA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AALA;AAOA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AALA;AACA;AAMA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAAA;AACA;AACA;AAFA;AACA;AAGA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAjEA;AA1BA;;ACjFA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;A", "sourceRoot": ""}