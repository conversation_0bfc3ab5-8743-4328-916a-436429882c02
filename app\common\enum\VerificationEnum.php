<?php


namespace app\common\enum;

/**
 * 自提核销
 * Class VerificationEnum
 * @package app\common\enum
 */
class VerificationEnum
{
    //操作人类型
    const TYPE_SYSTEM = 0;//系统
    const TYPE_SHOP = 1;//管理员

    //操作人类型
    public static function getVerificationScene($status = true)
    {
        $desc = [
            self::TYPE_SYSTEM => '系统',
            self::TYPE_SHOP => '商家',
        ];
        if ($status === true) {
            return $desc;
        }
        return $desc[$status] ?? '未知';
    }
}