<?php

namespace app\api\controller;

use app\common\basics\Api;
use app\common\enum\CommunityArticleEnum;
use app\common\model\community\CommunityArticle;
use think\facade\Log;

/**
 * 微信回调处理
 * Class WechatCallback
 * @package app\api\controller
 */
class WechatCallback extends Api
{
    /**
     * @notes 接口白名单，无需登录
     */
    public $like_not_need_login = ['securityResult'];

    /**
     * @notes 接收内容安全审核结果回调
     * <AUTHOR> @date 2023/09/15
     */
    public function securityResult()
    {
        $post = file_get_contents('php://input');
        $data = json_decode($post, true);

        if (empty($data)) {
            Log::error('微信内容安全回调：空请求');
            return 'empty request';
        }

        Log::info('微信内容安全回调：' . $post);

        // 简单的验证
        if (!isset($data['ToUserName']) || !isset($data['FromUserName']) || !isset($data['CreateTime']) || !isset($data['MsgType'])) {
            Log::error('微信内容安全回调：无效的请求格式');
            return 'invalid request';
        }

        if ($data['MsgType'] == 'event' && isset($data['Event']) && $data['Event'] == 'wxa_media_check') {
            $traceId = $data['trace_id'] ?? '';
            $result = $data['result'] ?? [];
            $suggest = $result['suggest'] ?? 'pass';

            if (empty($traceId)) {
                Log::error('微信内容安全回调：缺少trace_id');
                return 'missing trace_id';
            }
            
            // 使用trace_id来查找文章
            $article = CommunityArticle::whereRaw("FIND_IN_SET('{$traceId}', audit_trace_id)")
                                        ->where('status', CommunityArticleEnum::STATUS_WAIT)
                                        ->find();

            if ($article) {
                if ($suggest == 'risky') {
                    // 审核不通过
                    $article->status = CommunityArticleEnum::STATUS_FAIL;
                    $article->audit_remark = '图片内容安全审核不通过：' . ($result['label'] ?? '未知原因');
                } else {
                    // 图片审核通过，但我们还需要确认所有图片都已通过。
                    // 这是一个简化的处理，实际场景可能需要更复杂的逻辑来跟踪所有图片的审核状态。
                    // 这里我们假设一张图片不通过，则整篇文章不通过。
                    $article->status = CommunityArticleEnum::STATUS_SUCCESS;
                }
                $article->audit_time = time();
                $article->save();
                Log::info('微信内容安全回调：文章ID ' . $article->id . ' 状态已更新为 ' . $article->status);
            } else {
                 Log::warning('微信内容安全回调：根据trace_id未找到待审核的文章: ' . $traceId);
            }
        }
        
        return 'success';
    }
}