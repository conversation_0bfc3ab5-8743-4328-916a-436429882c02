(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["bundle_b-pages-community_search-community_search~bundle_b-pages-community_topic-community_topic"],{"125f":function(t,i,e){var n=e("ef09");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=e("4f06").default;a("2bd7fd7c",n,!0,{sourceMap:!1,shadowMode:!1})},1315:function(t,i,e){"use strict";var n=e("be2e"),a=e.n(n);a.a},"607d":function(t,i,e){"use strict";e.r(i);var n=e("6223"),a=e.n(n);for(var s in n)["default"].indexOf(s)<0&&function(t){e.d(i,t,(function(){return n[t]}))}(s);i["default"]=a.a},6223:function(t,i,e){"use strict";e("7a82"),Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0;var n={props:{list:{type:Array,default:function(){return[]}},width:{type:String,default:"347rpx"},type:{type:String}},data:function(){return{}}};i.default=n},"6eea":function(t,i,e){var n=e("24fb");i=n(!1),i.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.u-wrap[data-v-a968d7f2]{background-color:#eee;overflow:hidden}.u-lazy-item[data-v-a968d7f2]{width:100%;-webkit-transform:transition3d(0,0,0);transform:transition3d(0,0,0);will-change:transform;display:block;max-height:240px!important}',""]),t.exports=i},8762:function(t,i,e){var n=e("24fb");i=n(!1),i.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.community-list--item[data-v-24fb8771]{border-radius:%?14?%}.community-list--item .community-img[data-v-24fb8771]{width:%?336?%;position:relative}.community-list--item .community-img .works[data-v-24fb8771]{width:100%;height:100%;z-index:10;border-radius:%?14?%;background-color:rgba(0,0,0,.4);position:absolute}.community-list--item .community-index[data-v-24fb8771]{width:%?240?%;position:relative}.community-list--item .community-index .wrap[data-v-24fb8771]{width:100%;height:100%;z-index:10;border-radius:%?14?%;background-color:rgba(0,0,0,.4);position:absolute;padding-top:%?140?%}.community-list--item .community-index .wrap .index-title[data-v-24fb8771]{width:%?210?%}.community-list--item .community-index .wrap .index-name[data-v-24fb8771]{width:%?160?%}.community-list--item .community-info[data-v-24fb8771]{padding:%?10?%}.community-list--item .community-info .community-title[data-v-24fb8771]{font-size:%?28?%;line-height:%?40?%;color:#333}.community-list--item .community-info .user-name[data-v-24fb8771]{color:#999;font-size:%?24?%;margin:0 %?10?%}.community-list--item .community-info .likes uni-image[data-v-24fb8771]{width:%?32?%;height:%?32?%;vertical-align:middle}',""]),t.exports=i},8783:function(t,i,e){"use strict";e.r(i);var n=e("d5c4"),a=e("607d");for(var s in a)["default"].indexOf(s)<0&&function(t){e.d(i,t,(function(){return a[t]}))}(s);e("1315");var r=e("f0c5"),o=Object(r["a"])(a["default"],n["b"],n["c"],!1,null,"24fb8771",null,!1,n["a"],void 0);i["default"]=o.exports},"8fb5":function(t,i,e){"use strict";e("7a82"),Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0,e("a9e3");var n={name:"u-lazy-load",props:{index:{type:[Number,String]},image:{type:String,default:""},imgMode:{type:String,default:"widthFix"},loadingImg:{type:String,default:"data:image/png;base64,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"},errorImg:{type:String,default:"data:image/png;base64,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"},threshold:{type:[Number,String],default:100},duration:{type:[Number,String],default:500},effect:{type:String,default:"ease-in-out"},isEffect:{type:Boolean,default:!0},borderRadius:{type:[Number,String],default:0},height:{type:[Number,String],default:"450"}},data:function(){return{isShow:!1,opacity:1,time:this.duration,loadStatus:"",isError:!1,elIndex:this.$u.guid()}},computed:{getThreshold:function(){var t=uni.upx2px(Math.abs(this.threshold));return this.threshold<0?-t:t},imgHeight:function(){return this.$u.addUnit(this.height)}},created:function(){this.observer={}},watch:{isShow:function(t){var i=this;this.isEffect&&(this.time=0,this.opacity=0,setTimeout((function(){i.time=i.duration,i.opacity=1}),30))},image:function(t){t?(this.init(),this.isError=!1):this.isError=!0}},methods:{init:function(){this.isError=!1,this.loadStatus=""},clickImg:function(){0==this.isShow||this.isError,this.$emit("click",this.index)},imgLoaded:function(){""==this.loadStatus?this.loadStatus="lazyed":"lazyed"==this.loadStatus&&(this.loadStatus="loaded",this.$emit("load",this.index))},errorImgLoaded:function(){this.$emit("error",this.index)},loadError:function(){this.isError=!0},disconnectObserver:function(t){var i=this[t];i&&i.disconnect()}},beforeDestroy:function(){},mounted:function(){var t=this;this.$nextTick((function(){uni.$once("uOnReachBottom",(function(){t.isShow||(t.isShow=!0)}))})),setTimeout((function(){t.disconnectObserver("contentObserver");var i=uni.createIntersectionObserver(t);i.relativeToViewport({bottom:t.getThreshold}).observe(".u-lazy-item-"+t.elIndex,(function(i){i.intersectionRatio>0&&(t.isShow=!0,t.disconnectObserver("contentObserver"))})),t.contentObserver=i}),30)}};i.default=n},9325:function(t,i,e){"use strict";e.r(i);var n=e("8fb5"),a=e.n(n);for(var s in n)["default"].indexOf(s)<0&&function(t){e.d(i,t,(function(){return n[t]}))}(s);i["default"]=a.a},9337:function(t,i,e){"use strict";var n=e("125f"),a=e.n(n);a.a},"95fd":function(t,i,e){"use strict";e.d(i,"b",(function(){return n})),e.d(i,"c",(function(){return a})),e.d(i,"a",(function(){}));var n=function(){var t=this.$createElement,i=this._self._c||t;return i("v-uni-view",{staticClass:"u-waterfall"},[i("v-uni-view",{staticClass:"u-column",attrs:{id:"u-left-column"}},[this._t("left",null,{leftList:this.leftList})],2),i("v-uni-view",{staticClass:"u-column",attrs:{id:"u-right-column"}},[this._t("right",null,{rightList:this.rightList})],2)],1)},a=[]},"9b8d":function(t,i,e){"use strict";e.r(i);var n=e("dcc5"),a=e("9325");for(var s in a)["default"].indexOf(s)<0&&function(t){e.d(i,t,(function(){return a[t]}))}(s);e("efe4");var r=e("f0c5"),o=Object(r["a"])(a["default"],n["b"],n["c"],!1,null,"a968d7f2",null,!1,n["a"],void 0);i["default"]=o.exports},b871:function(t,i,e){"use strict";e.r(i);var n=e("ba65"),a=e.n(n);for(var s in n)["default"].indexOf(s)<0&&function(t){e.d(i,t,(function(){return n[t]}))}(s);i["default"]=a.a},ba65:function(t,i,e){"use strict";e("7a82");var n=e("ee27").default;Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0;var a=n(e("f07e")),s=n(e("c964"));e("a9e3"),e("99af"),e("fb6a"),e("14d9"),e("a434"),e("e9c4"),e("c740");var r={name:"u-waterfall",props:{value:{type:Array,required:!0,default:function(){return[]}},addTime:{type:[Number,String],default:200},idKey:{type:String,default:"id"}},data:function(){return{leftList:[],rightList:[],tempList:[],children:[]}},watch:{copyFlowList:function(t,i){var e=Array.isArray(i)&&i.length>0?i.length:0;this.tempList=this.tempList.concat(this.cloneData(t.slice(e))),this.splitData()}},mounted:function(){this.tempList=this.cloneData(this.copyFlowList),this.splitData()},computed:{copyFlowList:function(){return this.cloneData(this.value)}},methods:{splitData:function(){var t=this;return(0,s.default)((0,a.default)().mark((function i(){var e,n,s;return(0,a.default)().wrap((function(i){while(1)switch(i.prev=i.next){case 0:if(t.tempList.length){i.next=2;break}return i.abrupt("return");case 2:return i.next=4,t.$uGetRect("#u-left-column");case 4:return e=i.sent,i.next=7,t.$uGetRect("#u-right-column");case 7:if(n=i.sent,s=t.tempList[0],s){i.next=11;break}return i.abrupt("return");case 11:e.height<n.height?t.leftList.push(s):e.height>n.height?t.rightList.push(s):t.leftList.length<=t.rightList.length?t.leftList.push(s):t.rightList.push(s),t.tempList.splice(0,1),t.tempList.length&&setTimeout((function(){t.splitData()}),t.addTime);case 14:case"end":return i.stop()}}),i)})))()},cloneData:function(t){return JSON.parse(JSON.stringify(t))},clear:function(){this.leftList=[],this.rightList=[],this.$emit("input",[]),this.tempList=[]},remove:function(t){var i=this,e=-1;e=this.leftList.findIndex((function(e){return e[i.idKey]==t})),-1!=e?this.leftList.splice(e,1):(e=this.rightList.findIndex((function(e){return e[i.idKey]==t})),-1!=e&&this.rightList.splice(e,1)),e=this.value.findIndex((function(e){return e[i.idKey]==t})),-1!=e&&this.$emit("input",this.value.splice(e,1))},modify:function(t,i,e){var n=this,a=-1;if(a=this.leftList.findIndex((function(i){return i[n.idKey]==t})),-1!=a?this.leftList[a][i]=e:(a=this.rightList.findIndex((function(i){return i[n.idKey]==t})),-1!=a&&(this.rightList[a][i]=e)),a=this.value.findIndex((function(i){return i[n.idKey]==t})),-1!=a){var s=this.cloneData(this.value);s[a][i]=e,this.$emit("input",s)}}}};i.default=r},bb4f:function(t,i,e){"use strict";e.r(i);var n=e("95fd"),a=e("b871");for(var s in a)["default"].indexOf(s)<0&&function(t){e.d(i,t,(function(){return a[t]}))}(s);e("9337");var r=e("f0c5"),o=Object(r["a"])(a["default"],n["b"],n["c"],!1,null,"7664bcb0",null,!1,n["a"],void 0);i["default"]=o.exports},be2e:function(t,i,e){var n=e("8762");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=e("4f06").default;a("169a6294",n,!0,{sourceMap:!1,shadowMode:!1})},cfb4:function(t,i,e){var n=e("6eea");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=e("4f06").default;a("bd416cd4",n,!0,{sourceMap:!1,shadowMode:!1})},d5c4:function(t,i,e){"use strict";e.d(i,"b",(function(){return a})),e.d(i,"c",(function(){return s})),e.d(i,"a",(function(){return n}));var n={uLazyLoad:e("9b8d").default,uImage:e("f919").default},a=function(){var t=this,i=t.$createElement,e=t._self._c||i;return e("v-uni-view",{},["waterfall"==t.type?e("v-uni-view",{staticClass:"community-list"},t._l(t.list,(function(i,n){return e("router-link",{key:n,attrs:{to:"/bundle_b/pages/community_detail/community_detail?id="+i.id}},[e("v-uni-view",{staticClass:"community-list--item bg-white m-t-20"},[e("v-uni-view",{staticClass:"community-img"},[e("u-lazy-load",{attrs:{threshold:"0","border-radius":"10",image:i.image,index:n}})],1),e("v-uni-view",{staticClass:"community-info"},[e("v-uni-view",{staticClass:"community-title line-2"},[t._v(t._s(i.content))]),e("v-uni-view",{staticClass:"m-t-10 flex"},[e("u-image",{attrs:{width:"50",height:"50","border-radius":"50%",src:i.avatar}}),e("v-uni-view",{staticClass:"user-name flex-1 line-2"},[t._v(t._s(i.nickname))]),e("v-uni-view",{staticClass:"likes"},[e("v-uni-image",{attrs:{src:i.is_like?"/static/images/icon_collection_s.png":"/static/images/icon_likes.png"}}),e("v-uni-text",{staticClass:"xs muted m-l-6"},[t._v(t._s(i.like))])],1)],1)],1)],1)],1)})),1):t._e(),"works"==t.type?e("v-uni-view",{staticClass:"community-list"},t._l(t.list,(function(i,n){return e("router-link",{key:n,attrs:{to:"/bundle_b/pages/community_detail/community_detail?id="+i.id}},[e("v-uni-view",{staticClass:"community-list--item bg-white m-t-20"},[e("v-uni-view",{staticClass:"community-img"},[0===i.status||2===i.status?e("v-uni-view",{staticClass:"works flex row-center "},[e("v-uni-view",{staticClass:"text-center nr white"},[e("v-uni-view",[t._v(t._s(i.status_desc))]),e("v-uni-view",{staticClass:"m-t-10"},[t._v(t._s(i.audit_remark_desc))])],1)],1):t._e(),e("u-lazy-load",{attrs:{threshold:"0","border-radius":"10",image:i.image,index:n}})],1),e("v-uni-view",{staticClass:"community-info"},[e("v-uni-view",{staticClass:"community-title line-2"},[t._v(t._s(i.content))]),e("v-uni-view",{staticClass:"m-t-20 flex"},[e("v-uni-view",{staticClass:"user-name flex-1 line-2"},[t._v(t._s(i.create_time))]),e("v-uni-view",{staticClass:"likes"},[e("v-uni-image",{attrs:{src:i.is_like?"/static/images/icon_collection_s.png":"/static/images/icon_likes.png"}}),e("v-uni-text",{staticClass:"xs muted m-l-6"},[t._v(t._s(i.like))])],1)],1)],1)],1)],1)})),1):t._e(),"index"==t.type?e("v-uni-view",{staticClass:"community-list flex"},t._l(t.list,(function(i,n){return e("router-link",{key:n,staticClass:"community-list--item bg-white m-r-20",attrs:{to:"/bundle_b/pages/community_detail/community_detail?id="+i.id}},[e("v-uni-view",{staticClass:"community-index"},[e("v-uni-view",{staticClass:"wrap white sm p-l-10"},[e("v-uni-view",{staticClass:"index-title line-1"},[t._v(t._s(i.content))]),e("v-uni-view",{staticClass:"flex m-t-10"},[e("u-lazy-load",{attrs:{threshold:"0","border-radius":"10",image:i.image,index:n}}),e("v-uni-view",{staticClass:"index-name line-1 m-l-6"},[t._v(t._s(i.nickname))])],1)],1),e("u-image",{attrs:{width:"240",height:"240",src:i.image,borderRadius:"14"}})],1)],1)})),1):t._e()],1)},s=[]},dcc5:function(t,i,e){"use strict";e.d(i,"b",(function(){return n})),e.d(i,"c",(function(){return a})),e.d(i,"a",(function(){}));var n=function(){var t=this,i=t.$createElement,e=t._self._c||i;return e("v-uni-view",{staticClass:"u-wrap",class:"u-lazy-item-"+t.elIndex,style:{opacity:Number(t.opacity),borderRadius:t.borderRadius+"rpx",transition:"opacity "+t.time/1e3+"s ease-in-out"}},[e("v-uni-view",{class:"u-lazy-item-"+t.elIndex},[t.isError?e("v-uni-image",{staticClass:"u-lazy-item error",style:{borderRadius:t.borderRadius+"rpx",height:t.imgHeight},attrs:{src:t.errorImg,mode:t.imgMode},on:{load:function(i){arguments[0]=i=t.$handleEvent(i),t.errorImgLoaded.apply(void 0,arguments)},click:function(i){arguments[0]=i=t.$handleEvent(i),t.clickImg.apply(void 0,arguments)}}}):e("v-uni-image",{staticClass:"u-lazy-item",style:{borderRadius:t.borderRadius+"rpx",height:t.imgHeight},attrs:{src:t.isShow?t.image:t.loadingImg,mode:t.imgMode},on:{load:function(i){arguments[0]=i=t.$handleEvent(i),t.imgLoaded.apply(void 0,arguments)},error:function(i){arguments[0]=i=t.$handleEvent(i),t.loadError.apply(void 0,arguments)},click:function(i){arguments[0]=i=t.$handleEvent(i),t.clickImg.apply(void 0,arguments)}}})],1)],1)},a=[]},ef09:function(t,i,e){var n=e("24fb");i=n(!1),i.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.u-waterfall[data-v-7664bcb0]{display:flex;flex-direction:row;flex-direction:row;align-items:flex-start}.u-column[data-v-7664bcb0]{display:flex;flex-direction:row;flex:1;flex-direction:column;height:auto}.u-image[data-v-7664bcb0]{width:100%}',""]),t.exports=i},efe4:function(t,i,e){"use strict";var n=e("cfb4"),a=e.n(n);a.a}}]);