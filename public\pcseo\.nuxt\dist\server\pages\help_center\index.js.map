{"version": 3, "file": "pages/help_center/index.js", "sources": ["webpack:///./utils/tools.js", "webpack:///./components/ad-item.vue?d653", "webpack:///./components/ad-item.vue?050b", "webpack:///./components/ad-item.vue?179d", "webpack:///./components/ad-item.vue?6bc0", "webpack:///./components/ad-item.vue", "webpack:///./components/ad-item.vue?8feb", "webpack:///./components/ad-item.vue?7330", "webpack:///./static/images/news_null.png", "webpack:///./pages/help_center/index.vue?4173", "webpack:///./pages/help_center/index.vue?d9a4", "webpack:///./pages/help_center/index.vue?49ba", "webpack:///./pages/help_center/index.vue?34ce", "webpack:///./pages/help_center/index.vue", "webpack:///./pages/help_center/index.vue?5fd8", "webpack:///./pages/help_center/index.vue?98fa"], "sourcesContent": ["\n//节流\nexport const trottle = (func, time = 1000, context) => {\n\tlet previous = new Date(0).getTime()\n\treturn function(...args) {\n\t\tlet now = new Date().getTime()\n\t\tif (now - previous > time) {\n\t\t\tfunc.apply(context, args)\n\t\t\tprevious = now\n\t\t}\n\t}\n}\n\n\n//获取url后的参数  以对象返回\nexport function strToParams(str) {\n\tvar newparams = {}\n\tfor (let item of str.split('&')) {\n\t\tnewparams[item.split('=')[0]] = item.split('=')[1]\n\t}\n\treturn newparams\n}\n\n//对象参数转为以？&拼接的字符\nexport function paramsToStr(params) {\n\tlet p = '';\n\tif (typeof params == 'object') {\n\t\tp = '?'\n\t\tfor (let props in params) {\n\t\t\tp += `${props}=${params[props]}&`\n\t\t}\n\t\tp = p.slice(0, -1)\n\t}\n\treturn p\n}\n\n/**\n * @description 复制到剪切板\n * @param value { String } 复制内容\n * @return { Promise } resolve | reject\n */\n export const copyClipboard = (value) => {\n    const elInput = document.createElement('input')\n\n    elInput.setAttribute('value', value)\n    document.body.appendChild(elInput)\n    elInput.select()\n\n    try{\n        if(document.execCommand('copy'))\n            return Promise.resolve()\n        else\n            throw new Error()\n    } catch(err) {\n        return Promise.reject(err)\n    } finally {\n        document.body.removeChild(elInput)\n    }\n}", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/sass-resources-loader/lib/loader.js??ref--7-oneOf-1-4!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./ad-item.vue?vue&type=style&index=0&id=368017b1&lang=scss&scoped=true&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"532bec65\", content, true, context)\n};", "export * from \"-!../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/sass-resources-loader/lib/loader.js??ref--7-oneOf-1-4!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./ad-item.vue?vue&type=style&index=0&id=368017b1&lang=scss&scoped=true&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".ad-item[data-v-368017b1]{width:100%;height:100%;cursor:pointer}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"ad-item\",on:{\"click\":function($event){$event.stopPropagation();return _vm.goPage(_vm.item)}}},[_c('el-image',{staticStyle:{\"width\":\"100%\",\"height\":\"100%\"},attrs:{\"src\":_vm.item.image,\"fit\":\"cover\"}})],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n\nimport { paramsToStr } from \"~/utils/tools\";\nexport default {\n    components: {},\n    props: {\n        item: {\n            type: Object,\n            default: () => ({}),\n        },\n    },\n    methods: {\n        goPage(item) {\n            let { link_type, link, params } = item;\n            switch (link_type) {\n                case 3:\n                    window.open(item.link);\n                    break;\n                default:\n                    if ([\"/goods_details\"].includes(link)) {\n                        link += `/${params.id}`;\n                    } else {\n                        link += paramsToStr(params);\n                    }\n                    this.$router.push({\n                        path: link,\n                    });\n            }\n        },\n    },\n};\n", "import mod from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./ad-item.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./ad-item.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./ad-item.vue?vue&type=template&id=368017b1&scoped=true&\"\nimport script from \"./ad-item.vue?vue&type=script&lang=js&\"\nexport * from \"./ad-item.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./ad-item.vue?vue&type=style&index=0&id=368017b1&lang=scss&scoped=true&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  \"368017b1\",\n  \"6dd301aa\"\n  \n)\n\nexport default component.exports", "module.exports = __webpack_public_path__ + \"img/news_null.856b3f3.png\";", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../../node_modules/sass-resources-loader/lib/loader.js??ref--7-oneOf-1-4!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=style&index=0&id=3fd04516&lang=scss&scoped=true&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"2315121f\", content, true, context)\n};", "export * from \"-!../../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../../node_modules/sass-resources-loader/lib/loader.js??ref--7-oneOf-1-4!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=style&index=0&id=3fd04516&lang=scss&scoped=true&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".help-center-container .help-center-banner[data-v-3fd04516]{margin-top:16px}.help-center-container .help-center-box[data-v-3fd04516]{margin-top:16px;display:flex;flex-direction:row}.help-center-container .help-center-box .help-center-aside[data-v-3fd04516]{width:160px;padding:20px 30px}.help-center-container .help-center-box .help-center-aside .nav li[data-v-3fd04516]{margin:10px 0;padding:0 30px;cursor:pointer}.help-center-container .help-center-box .help-center-aside .nav .active-item[data-v-3fd04516]{padding-left:27px;color:#ff2c3c;border-left:3px solid #ff2c3c}.help-center-container .help-center-box .article-lists-container[data-v-3fd04516]{width:1004px;display:flex;flex-direction:column;justify-content:space-between}.help-center-container .help-center-box .article-lists-container .article-item[data-v-3fd04516]{margin:0 20px;padding:15px 0;border-bottom:1px solid #e5e5e5;cursor:pointer}.help-center-container .help-center-box .article-lists-container .article-item .article-name[data-v-3fd04516]{margin-bottom:11px;margin-top:13px;max-width:720px}.help-center-container .help-center-box .article-lists-container .help-center-pagination[data-v-3fd04516]{padding-top:38px;margin-bottom:30px}.help-center-container .help-center-box .article-lists-container .data-null[data-v-3fd04516]{padding-top:150px}[data-v-3fd04516] .el-pagination.is-background .btn-next,[data-v-3fd04516] .el-pagination.is-background .btn-prev,[data-v-3fd04516] .el-pagination.is-background .el-pager li{background:#fff;padding:0 10px}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"help-center-container\"},[_vm._ssrNode(\"<div class=\\\"help-center-banner\\\" data-v-3fd04516>\",\"</div>\",[_c('client-only',[_c('swiper',{ref:\"mySwiper\",attrs:{\"options\":_vm.swiperOptions}},_vm._l((_vm.bannerList),function(item,index){return _c('swiper-slide',{key:index,staticClass:\"swiper-item\"},[_c('ad-item',{attrs:{\"item\":item}})],1)}),1)],1)],1),_vm._ssrNode(\" \"),_vm._ssrNode(\"<div class=\\\"help-center-box\\\" data-v-3fd04516>\",\"</div>\",[_vm._ssrNode(\"<div class=\\\"help-center-aside bg-white\\\" data-v-3fd04516><ul class=\\\"nav flex-col col-center\\\" data-v-3fd04516><li\"+(_vm._ssrClass(\"flex\",{'active-item': _vm.currentId <= 0}))+\" data-v-3fd04516>全部</li> \"+(_vm._ssrList((_vm.categoryList),function(item){return (\"<li\"+(_vm._ssrClass(\"flex\",{'active-item': item.id == _vm.currentId}))+\" data-v-3fd04516>\"+_vm._ssrEscape(_vm._s(item.name))+\"</li>\")}))+\"</ul></div> \"),_vm._ssrNode(\"<div class=\\\"article-lists-container m-l-16 bg-white\\\" data-v-3fd04516>\",\"</div>\",[_vm._ssrNode(\"<div\"+(_vm._ssrStyle(null,null, { display: (!_vm.dataNull) ? '' : 'none' }))+\" data-v-3fd04516>\",\"</div>\",[_vm._ssrNode(\"<div data-v-3fd04516>\",\"</div>\",_vm._l((_vm.articleList),function(item){return _c('nuxt-link',{key:item.id,staticClass:\"article-item flex row-between bg-white\",attrs:{\"to\":'/help_center/help_center_detail?id=' + item.id}},[_c('div',[_c('div',{staticClass:\"lg article-name line2\"},[_vm._v(_vm._s(item.title))]),_vm._v(\" \"),_c('div',{staticClass:\"lighter\"},[_vm._v(_vm._s(item.intro))]),_vm._v(\" \"),_c('div',{staticClass:\"flex\",staticStyle:{\"margin-top\":\"56px\"}},[_c('div',{staticClass:\"sm muted\"},[_vm._v(\"发布时间：\"+_vm._s(item.create_time))]),_vm._v(\" \"),_c('div',{staticClass:\"flex m-l-16\"},[_c('i',{staticClass:\"el-icon-view muted\"}),_vm._v(\" \"),_c('div',{staticClass:\"muted\",staticStyle:{\"margin-left\":\"3px\"}},[_vm._v(_vm._s(item.visit)+\" 人浏览\")])])])]),_vm._v(\" \"),_c('el-image',{staticStyle:{\"width\":\"200px\",\"height\":\"150px\",\"border-radius\":\"6px\"},attrs:{\"fit\":\"cover\",\"src\":item.image}})],1)}),1),_vm._ssrNode(\" \"),_vm._ssrNode(\"<div class=\\\"help-center-pagination row-center\\\" data-v-3fd04516>\",\"</div>\",[_c('el-pagination',{attrs:{\"background\":\"\",\"hide-on-single-page\":\"\",\"layout\":\"prev, pager, next\",\"total\":_vm.count,\"prev-text\":\"上一页\",\"next-text\":\"下一页\",\"page-size\":10},on:{\"current-change\":_vm.changePage}})],1)],2),_vm._ssrNode(\" <div class=\\\"data-null column-center\\\"\"+(_vm._ssrStyle(null,null, { display: (_vm.dataNull) ? '' : 'none' }))+\" data-v-3fd04516><img\"+(_vm._ssrAttr(\"src\",require(\"static/images/news_null.png\")))+\" style=\\\"width: 150px;height: 150px;\\\" data-v-3fd04516> <div class=\\\"xs muted\\\" data-v-3fd04516>\\n                    暂无消息记录～\\n                </div></div>\")],2)],2)],2)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nexport default {\n    head() {\n        return {\n            title: this.$store.getters.headTitle,\n            link: [{\n                rel: \"icon\",\n                type: \"image/x-icon\",\n                href: this.$store.getters.favicon\n            }],\n        };\n    },\n    async asyncData({\n        $get,\n        $post\n    }) {\n        let categoryList = [];\n        let currentId = 0;\n        let articleList = [];\n        let count = 0;\n        let dataNull = true;\n        const banner = $get(\"ad/lists\", {\n            params: {\n                pid: 29,\n                terminal: 2\n            }\n        });\n        let res = await $get(\"help/category\");\n        const {\n            data: bannerList\n        } = await banner;\n        if (res.code == 1) {\n            categoryList = res.data;\n            currentId = 0\n            let listsRes = await $get(\"help/lists\", {\n                params: {\n                    cid: currentId,\n                    page_size: 10\n                }\n            });\n            if (listsRes.code == 1) {\n                articleList = listsRes.data.list;\n                count = listsRes.data.count\n                if (count <= 0) {\n                    dataNull = true;\n                } else {\n                    dataNull = false\n                }\n            }\n        }\n        return {\n            categoryList,\n            articleList,\n            count,\n            currentId,\n            bannerList,\n            dataNull,\n        }\n    },\n    data() {\n        return {\n            categoryList: [],\n            articleList: [],\n            currentId: -1,\n            count: 0,\n            swiperOptions: {\n                width: 1180,\n            }\n        }\n    },\n    mounted() {\n        console.log(this.articleList, 'articleList')\n    },\n    methods: {\n        async changePage(current) {\n            let res = await this.$get(\"help/lists\", {\n                params: {\n                    cid: this.currentId,\n                    page_no: current,\n                    page_size: 10\n                }\n            });\n            if (res.code == 1) {\n                this.articleList = res.data.list;\n                if (this.articleList.length <= 0) {\n                    dataNull = true;\n                } else {\n                    dataNull = false\n                }\n            }\n        },\n        changeList(id) {\n            this.currentId = id;\n            this.changePage(1)\n        }\n    }\n}\n", "import mod from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./index.vue?vue&type=template&id=3fd04516&scoped=true&\"\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./index.vue?vue&type=style&index=0&id=3fd04516&lang=scss&scoped=true&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  \"3fd04516\",\n  \"385c2498\"\n  \n)\n\nexport default component.exports\n\n/* nuxt-component-imports */\ninstallComponents(component, {AdItem: require('/Users/<USER>/Desktop/vue/pc/components/ad-item.vue').default})\n"], "mappings": ";;;;;;;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AAEA;AACA;AACA;AAAA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AAAA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAIA;AACA;AACA;AACA;AACA;AACA;;;;;;;AC1DA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACXA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACNA;AACA;AACA;;;;;;;;ACFA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAFA;AADA;AAMA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AADA;AAVA;AAcA;AACA;AAlBA;AARA;;ACRA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;ACvBA;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACXA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACNA;AACA;AACA;;;;;ACFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAHA;AAFA;AAQA;AACA;AAAA;AACA;AACA;AAFA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAFA;AADA;AAMA;AACA;AACA;AADA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAFA;AADA;AACA;AAKA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AANA;AAQA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AADA;AALA;AASA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAHA;AADA;AACA;AAMA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAtBA;AAxEA;;ACxDA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;A", "sourceRoot": ""}