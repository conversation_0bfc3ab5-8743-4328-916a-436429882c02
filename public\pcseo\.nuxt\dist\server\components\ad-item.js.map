{"version": 3, "file": "components/ad-item.js", "sources": ["webpack:///./utils/tools.js", "webpack:///./components/ad-item.vue?d653", "webpack:///./components/ad-item.vue?050b", "webpack:///./components/ad-item.vue?179d", "webpack:///./components/ad-item.vue?6bc0", "webpack:///./components/ad-item.vue", "webpack:///./components/ad-item.vue?8feb", "webpack:///./components/ad-item.vue?7330"], "sourcesContent": ["\n//节流\nexport const trottle = (func, time = 1000, context) => {\n\tlet previous = new Date(0).getTime()\n\treturn function(...args) {\n\t\tlet now = new Date().getTime()\n\t\tif (now - previous > time) {\n\t\t\tfunc.apply(context, args)\n\t\t\tprevious = now\n\t\t}\n\t}\n}\n\n\n//获取url后的参数  以对象返回\nexport function strToParams(str) {\n\tvar newparams = {}\n\tfor (let item of str.split('&')) {\n\t\tnewparams[item.split('=')[0]] = item.split('=')[1]\n\t}\n\treturn newparams\n}\n\n//对象参数转为以？&拼接的字符\nexport function paramsToStr(params) {\n\tlet p = '';\n\tif (typeof params == 'object') {\n\t\tp = '?'\n\t\tfor (let props in params) {\n\t\t\tp += `${props}=${params[props]}&`\n\t\t}\n\t\tp = p.slice(0, -1)\n\t}\n\treturn p\n}\n\n/**\n * @description 复制到剪切板\n * @param value { String } 复制内容\n * @return { Promise } resolve | reject\n */\n export const copyClipboard = (value) => {\n    const elInput = document.createElement('input')\n\n    elInput.setAttribute('value', value)\n    document.body.appendChild(elInput)\n    elInput.select()\n\n    try{\n        if(document.execCommand('copy'))\n            return Promise.resolve()\n        else\n            throw new Error()\n    } catch(err) {\n        return Promise.reject(err)\n    } finally {\n        document.body.removeChild(elInput)\n    }\n}", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/sass-resources-loader/lib/loader.js??ref--7-oneOf-1-4!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./ad-item.vue?vue&type=style&index=0&id=368017b1&lang=scss&scoped=true&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"532bec65\", content, true, context)\n};", "export * from \"-!../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/sass-resources-loader/lib/loader.js??ref--7-oneOf-1-4!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./ad-item.vue?vue&type=style&index=0&id=368017b1&lang=scss&scoped=true&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".ad-item[data-v-368017b1]{width:100%;height:100%;cursor:pointer}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"ad-item\",on:{\"click\":function($event){$event.stopPropagation();return _vm.goPage(_vm.item)}}},[_c('el-image',{staticStyle:{\"width\":\"100%\",\"height\":\"100%\"},attrs:{\"src\":_vm.item.image,\"fit\":\"cover\"}})],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n\nimport { paramsToStr } from \"~/utils/tools\";\nexport default {\n    components: {},\n    props: {\n        item: {\n            type: Object,\n            default: () => ({}),\n        },\n    },\n    methods: {\n        goPage(item) {\n            let { link_type, link, params } = item;\n            switch (link_type) {\n                case 3:\n                    window.open(item.link);\n                    break;\n                default:\n                    if ([\"/goods_details\"].includes(link)) {\n                        link += `/${params.id}`;\n                    } else {\n                        link += paramsToStr(params);\n                    }\n                    this.$router.push({\n                        path: link,\n                    });\n            }\n        },\n    },\n};\n", "import mod from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./ad-item.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./ad-item.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./ad-item.vue?vue&type=template&id=368017b1&scoped=true&\"\nimport script from \"./ad-item.vue?vue&type=script&lang=js&\"\nexport * from \"./ad-item.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./ad-item.vue?vue&type=style&index=0&id=368017b1&lang=scss&scoped=true&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  \"368017b1\",\n  \"6dd301aa\"\n  \n)\n\nexport default component.exports"], "mappings": ";;;;;;;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AAEA;AACA;AACA;AAAA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AAAA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAIA;AACA;AACA;AACA;AACA;AACA;;;;;;;AC1DA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACXA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACNA;AACA;AACA;;;;;;;;ACFA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAFA;AADA;AAMA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AADA;AAVA;AAcA;AACA;AAlBA;AARA;;ACRA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;A", "sourceRoot": ""}