<?php
// +----------------------------------------------------------------------
// | likeshop100%开源免费商用商城系统
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | 开源版本可自由商用，可去除界面版权logo
// | 商业版本务必购买商业授权，以免引起法律纠纷
// | 禁止对系统程序代码以任何目的，任何形式的再发布
// | gitee下载：https://gitee.com/likeshop_gitee
// | github下载：https://github.com/likeshop-github
// | 访问官网：https://www.likeshop.cn
// | 访问社区：https://home.likeshop.cn
// | 访问手册：http://doc.likeshop.cn
// | 微信公众号：likeshop技术社区
// | likeshop团队 版权所有 拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeshopTeam
// +----------------------------------------------------------------------

namespace app\common\cache;


class ExportCache extends CacheBase
{

    protected $uniqid = '';

    public function __construct($key = '')
    {
        parent::__construct($key);
        //以微秒计的当前时间，生成一个唯一的 ID,以tagname为前缀
        $this->uniqid = md5(uniqid($this->name,true).mt_rand());
    }


    /**
     * @notes 获取缓存目录
     * @return string
     * <AUTHOR>
     * @date 2022/4/21 11:17
     */
    public function getSrc()
    {
        return app()->getRootPath() . 'runtime/file/export/'.date('Y-m').'/'.$this->uniqid.'/';
    }


    /**
     * @notes 设置文件路径缓存地址
     * @param $fileName
     * @return string
     * <AUTHOR>
     * @date 2021/7/28 17:36
     */
    public function setFile($fileName)
    {
        $src = $this->getSrc();
        $key = md5($src . $fileName) . time();
        $this->cacheSet($key, ['src' => $src, 'name' => $fileName], 300);
        return $key;
    }


    /**
     * @notes 获取文件
     * @param $key
     * @return mixed
     * <AUTHOR>
     * @date 2022/4/21 11:44
     */
    public function getFile($key)
    {
        return $this->cacheGet($key);
    }

    /**
     * @notes 设置标签
     * <AUTHOR>
     * @date 2022/4/21 11:45
     */
    public function setTag()
    {
        return 'export';
    }

    public function setData()
    {
    }

}