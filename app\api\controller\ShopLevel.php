<?php

namespace app\api\controller;

use app\common\basics\Api;
use app\api\logic\PayLogic;
use app\common\enum\ClientEnum;
use app\common\enum\PayEnum;
use app\common\logic\ShopTierLogic;
use app\common\enum\ShopTierEnum;
use app\common\server\JsonServer;
use app\common\validate\ShopLevelValidate;
use think\facade\Db;

/**
 * 商家等级相关接口
 */
class ShopLevel extends Api
{
    /**
     * 获取等级配置列表
     */
    public function levelConfigs()
    {
        $configs = ShopLevelLogic::getLevelConfigs();
        
        // 格式化返回数据
        foreach ($configs as &$config) {
            $config['features'] = json_decode($config['features'], true) ?: [];
            $config['limits'] = json_decode($config['limits'], true) ?: [];
            $config['price_text'] = $config['price'] > 0 ? '¥' . $config['price'] : '免费';
        }

        return JsonServer::success('获取成功', $configs);
    }

    /**
     * 选择等级并创建支付订单
     */
    public function selectLevel()
    {
        $post = $this->request->post();
        
        try {
            // 验证参数
            validate(ShopLevelValidate::class)->scene('select')->check($post);
            
            $targetLevel = intval($post['target_level']);
            $userId = $this->user_id;

            // 检查用户是否已有申请记录
            $existApply = \app\common\model\shop\ShopApply::where([
                'user_id' => $userId,
                'del' => 0,
                'audit_status' => ['in', [1, 2]] // 待审核或已通过
            ])->find();

            if ($existApply) {
                return JsonServer::error('您已有入驻申请记录，请勿重复申请');
            }

            // 0元入驻直接返回，无需支付
            if ($targetLevel == 0) {
                return JsonServer::success('0元入驻无需支付，请直接填写入驻信息', [
                    'target_level' => $targetLevel,
                    'need_payment' => false
                ]);
            }

            // 获取等级配置
            $levelConfig = ShopLevelLogic::getLevelConfig($targetLevel);
            if (!$levelConfig) {
                return JsonServer::error('等级配置不存在');
            }

            // 检查是否已有未支付的订单
            $existOrder = \app\common\model\shop\ShopMerchantfees::where([
                'user_id' => $userId,
                'target_level' => $targetLevel,
                'upgrade_type' => 0, // 新入驻
                'status' => 0 // 未支付
            ])->find();

            if ($existOrder) {
                // 返回现有订单信息
                return JsonServer::success('已有待支付订单', [
                    'target_level' => $targetLevel,
                    'need_payment' => true,
                    'order_sn' => $existOrder->order_sn,
                    'amount' => $existOrder->amount,
                    'level_name' => $levelConfig->name
                ]);
            }

            // 创建新的支付订单
            $orderSn = createSn('shop_merchantfees', 'order_sn');
            $orderData = [
                'user_id' => $userId,
                'shop_id' => 0,
                'order_sn' => $orderSn,
                'amount' => $levelConfig->price,
                'target_level' => $targetLevel,
                'upgrade_type' => 0, // 新入驻
                'status' => 0, // 待支付
                'created_at' => date('Y-m-d H:i:s')
            ];

            \app\common\model\shop\ShopMerchantfees::create($orderData);

            return JsonServer::success('订单创建成功', [
                'target_level' => $targetLevel,
                'need_payment' => true,
                'order_sn' => $orderSn,
                'amount' => $levelConfig->price,
                'level_name' => $levelConfig->name
            ]);

        } catch (\Exception $e) {
            return JsonServer::error($e->getMessage());
        }
    }

    /**
     * 创建支付订单
     */
    public function createPayment()
    {
        $post = $this->request->post();
        
        try {
            validate(ShopLevelValidate::class)->scene('payment')->check($post);
            
            $orderSn = $post['order_sn'];
            $payWay = intval($post['pay_way'] ?? PayEnum::WECHAT_PAY);
            $from = intval($post['from'] ?? ClientEnum::h5);

            // 查找订单
            $order = \app\common\model\shop\ShopMerchantfees::where('order_sn', $orderSn)->find();
            if (!$order) {
                return JsonServer::error('订单不存在');
            }

            if ($order->status != 0) {
                return JsonServer::error('订单状态异常');
            }

            if ($order->user_id != $this->user_id) {
                return JsonServer::error('订单不属于当前用户');
            }

            // 创建支付
            switch ($payWay) {
                case PayEnum::WECHAT_PAY:
                    $payInfo = PayLogic::wechatPay($orderSn, 'ruzhucharge', $from);
                    break;
                case PayEnum::ALI_PAY:
                    $payInfo = PayLogic::aliPay($orderSn, 'ruzhucharge', $from);
                    break;
                default:
                    return JsonServer::error('不支持的支付方式');
            }

            if ($payInfo === false) {
                return JsonServer::error(PayLogic::getError() ?: '创建支付失败');
            }

            return JsonServer::success('支付创建成功', $payInfo);

        } catch (\Exception $e) {
            return JsonServer::error($e->getMessage());
        }
    }

    /**
     * 获取用户支付状态
     */
    public function getPaymentStatus()
    {
        $orderSn = $this->request->get('order_sn');
        if (!$orderSn) {
            return JsonServer::error('订单号不能为空');
        }

        $order = \app\common\model\shop\ShopMerchantfees::where([
            'order_sn' => $orderSn,
            'user_id' => $this->user_id
        ])->find();

        if (!$order) {
            return JsonServer::error('订单不存在');
        }

        $levelConfig = ShopLevelLogic::getLevelConfig($order->target_level);

        return JsonServer::success('获取成功', [
            'order_sn' => $order->order_sn,
            'amount' => $order->amount,
            'target_level' => $order->target_level,
            'level_name' => $levelConfig ? $levelConfig->name : '未知等级',
            'status' => $order->status,
            'status_text' => $order->status == 1 ? '已支付' : '待支付',
            'created_at' => $order->created_at
        ]);
    }

    /**
     * 商家等级升级
     */
    public function upgrade()
    {
        $post = $this->request->post();
        
        try {
            validate(ShopLevelValidate::class)->scene('upgrade')->check($post);
            
            $shopId = $this->getShopId();
            if (!$shopId) {
                return JsonServer::error('商家信息获取失败');
            }

            $targetLevel = intval($post['target_level']);
            
            $result = ShopLevelLogic::createUpgradeOrder($shopId, $targetLevel, $this->user_id);
            if ($result === false) {
                return JsonServer::error(ShopLevelLogic::getError());
            }

            return JsonServer::success('升级订单创建成功', $result);

        } catch (\Exception $e) {
            return JsonServer::error($e->getMessage());
        }
    }

    /**
     * 获取可升级等级列表
     */
    public function getUpgradableLevels()
    {
        $shopId = $this->getShopId();
        if (!$shopId) {
            return JsonServer::error('商家信息获取失败');
        }

        $levels = ShopLevelLogic::getUpgradableLevels($shopId);
        return JsonServer::success('获取成功', $levels);
    }

    /**
     * 获取商家ID（从session或其他方式）
     */
    private function getShopId()
    {
        // 这里需要根据实际情况获取商家ID
        // 可能从session、用户表关联等方式获取
        $user = \app\common\model\User::find($this->user_id);
        return $user ? $user->shop_id : 0;
    }
}
