{layout name="layout2" /}
<div class="layui-form" lay-filter="layuiadmin-form-admin" id="layuiadmin-form-admin" style="padding: 20px 30px 0 0;">
    <input type="hidden" name="id" value="{$info.id}">

    <div class="layui-form-item">
        <label class="layui-form-label">商家等级</label>
        <div class="layui-input-inline">
            <select name="tier_level" lay-verify="required" lay-vertype="tips" disabled>
                <option value="">请选择商家等级</option>
                {volist name="tier_options" id="option"}
                <option value="{$option.value}" {if $info.tier_level == $option.value}selected{/if}>{$option.label}</option>
                {/volist}
            </select>
        </div>
        <div class="layui-form-mid layui-word-aux">商家等级不可修改</div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label">每日群发限制</label>
        <div class="layui-input-inline">
            <input type="number" name="daily_limit" lay-verify="required|number" lay-vertype="tips"
                   placeholder="请输入每日群发限制数量" autocomplete="off" class="layui-input" min="0"
                   value="{$info.daily_limit|default=''}">
        </div>
        <div class="layui-form-mid layui-word-aux">该等级商家每天可发送的群发信息数量</div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label">采购人员总数</label>
        <div class="layui-input-inline">
            <input type="number" name="total_purchaser_count" lay-verify="required|number" lay-vertype="tips"
                   placeholder="请输入采购人员总数" autocomplete="off" class="layui-input" min="0"
                   value="{$info.total_purchaser_count|default=''}">
        </div>
        <div class="layui-form-mid layui-word-aux">该等级商家可分配的采购人员总数</div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label">低活跃度占比</label>
        <div class="layui-input-inline">
            <input type="number" name="level1_percent" lay-verify="required|number" lay-vertype="tips"
                   placeholder="请输入百分比" autocomplete="off" class="layui-input" min="0" max="100" step="0.01"
                   value="{$info.level1_percent|default=''}">
        </div>
        <div class="layui-form-mid layui-word-aux">活跃度积分 < {$info.level2_min_score|default='101'} 的用户占比(%)</div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label">中活跃度占比</label>
        <div class="layui-input-inline">
            <input type="number" name="level2_percent" lay-verify="required|number" lay-vertype="tips"
                   placeholder="请输入百分比" autocomplete="off" class="layui-input" min="0" max="100" step="0.01"
                   value="{$info.level2_percent|default=''}">
        </div>
        <div class="layui-form-mid layui-word-aux">活跃度积分 {$info.level2_min_score|default='101'}-{$info.level3_min_score|default='500'} 的用户占比(%)</div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label">高活跃度占比</label>
        <div class="layui-input-inline">
            <input type="number" name="level3_percent" lay-verify="required|number" lay-vertype="tips"
                   placeholder="请输入百分比" autocomplete="off" class="layui-input" min="0" max="100" step="0.01"
                   value="{$info.level3_percent|default=''}">
        </div>
        <div class="layui-form-mid layui-word-aux">活跃度积分 >= {$info.level3_min_score|default='501'} 的用户占比(%)</div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label">活跃度积分设置</label>
        <div class="layui-input-inline" style="width: 100px;">
            <input type="number" name="level1_min_score" lay-verify="required|number" lay-vertype="tips"
                   placeholder="最低分" autocomplete="off" class="layui-input" min="0" readonly
                   value="{$info.level1_min_score|default='0'}">
        </div>
        <div class="layui-form-mid">-</div>
        <div class="layui-input-inline" style="width: 100px;">
            <input type="number" name="level2_min_score" lay-verify="required|number" lay-vertype="tips"
                   placeholder="中等分" autocomplete="off" class="layui-input" min="1"
                   value="{$info.level2_min_score|default='101'}">
        </div>
        <div class="layui-form-mid">-</div>
        <div class="layui-input-inline" style="width: 100px;">
            <input type="number" name="level3_min_score" lay-verify="required|number" lay-vertype="tips"
                   placeholder="高等分" autocomplete="off" class="layui-input" min="1"
                   value="{$info.level3_min_score|default='501'}">
        </div>
        <div class="layui-form-mid layui-word-aux">设置各等级的活跃度积分阈值</div>
    </div>

    <div class="layui-form-item">
        <div class="layui-input-block">
            <button class="layui-btn" lay-submit lay-filter="editSubmit" id="editSubmit">立即提交</button>
            <button type="reset" class="layui-btn layui-btn-primary">重置</button>
        </div>
    </div>
</div>
<script>
    layui.config({
        version: "{$front_version}",
        base: '/static/lib/'
    }).use(['form'], function () {
        var form = layui.form;
        var $ = layui.jquery;

        // 监听百分比输入，实时计算总和
        $('input[name="level1_percent"], input[name="level2_percent"], input[name="level3_percent"]').on('input', function() {
            var level1 = parseFloat($('input[name="level1_percent"]').val()) || 0;
            var level2 = parseFloat($('input[name="level2_percent"]').val()) || 0;
            var level3 = parseFloat($('input[name="level3_percent"]').val()) || 0;
            var total = level1 + level2 + level3;

            // 显示总和提示
            var tips = '当前总和：' + total.toFixed(2) + '%';
            if (Math.abs(total - 100) > 0.01) {
                tips += ' (需要等于100%)';
            } else {
                tips += ' ✓';
            }

            // 更新提示信息
            $(this).siblings('.layui-form-mid').html(tips);
        });

        // 监听提交
        form.on('submit(editSubmit)', function(data) {
            // 验证百分比总和
            var level1 = parseFloat(data.field.level1_percent) || 0;
            var level2 = parseFloat(data.field.level2_percent) || 0;
            var level3 = parseFloat(data.field.level3_percent) || 0;
            var total = level1 + level2 + level3;

            if (Math.abs(total - 100) > 0.01) {
                layer.msg('各等级用户占比总和必须为100%，当前总和为：' + total.toFixed(2) + '%', {icon: 2});
                return false;
            }

            like.ajax({
                url: '{:url("edit")}',
                data: data.field,
                type: "post",
                success: function(res) {
                    if (res.code == 1) {
                        layer.msg(res.msg, {icon: 1, time: 1000}, function() {
                            var index = parent.layer.getFrameIndex(window.name);
                            parent.layer.close(index);
                        });
                    } else {
                        layer.msg(res.msg, {icon: 2, time: 2000});
                    }
                }
            });
            return false;
        });
    });
</script>
