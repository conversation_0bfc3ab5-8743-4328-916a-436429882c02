<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Cdb\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * 可回档时间范围
 *
 * @method string getBegin() 获取实例可回档开始时间，时间格式：2016-10-29 01:06:04
 * @method void setBegin(string $Begin) 设置实例可回档开始时间，时间格式：2016-10-29 01:06:04
 * @method string getEnd() 获取实例可回档结束时间，时间格式：2016-11-02 11:44:47
 * @method void setEnd(string $End) 设置实例可回档结束时间，时间格式：2016-11-02 11:44:47
 */
class RollbackTimeRange extends AbstractModel
{
    /**
     * @var string 实例可回档开始时间，时间格式：2016-10-29 01:06:04
     */
    public $Begin;

    /**
     * @var string 实例可回档结束时间，时间格式：2016-11-02 11:44:47
     */
    public $End;

    /**
     * @param string $Begin 实例可回档开始时间，时间格式：2016-10-29 01:06:04
     * @param string $End 实例可回档结束时间，时间格式：2016-11-02 11:44:47
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("Begin",$param) and $param["Begin"] !== null) {
            $this->Begin = $param["Begin"];
        }

        if (array_key_exists("End",$param) and $param["End"] !== null) {
            $this->End = $param["End"];
        }
    }
}
