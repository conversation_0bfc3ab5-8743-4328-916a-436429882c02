<?php return array (
  'root' => 
  array (
    'pretty_version' => '1.0.0+no-version-set',
    'version' => '1.0.0.0',
    'aliases' => 
    array (
    ),
    'reference' => NULL,
    'name' => 'topthink/think',
  ),
  'versions' => 
  array (
    'adbario/php-dot-notation' => 
    array (
      'pretty_version' => '2.2.0',
      'version' => '*******',
      'aliases' => 
      array (
      ),
      'reference' => 'eee4fc81296531e6aafba4c2bbccfc5adab1676e',
    ),
    'alibabacloud/alinlp-20200629' => 
    array (
      'pretty_version' => '3.1.0',
      'version' => '3.1.0.0',
      'aliases' => 
      array (
      ),
      'reference' => '352bda8c22f09775f8964e3e8ae27b5a24c0571f',
    ),
    'alibabacloud/client' => 
    array (
      'pretty_version' => '1.5.31',
      'version' => '1.5.31.0',
      'aliases' => 
      array (
      ),
      'reference' => '19224d92fe27ab8ef501d77d4891e7660bc023c1',
    ),
    'alibabacloud/credentials' => 
    array (
      'pretty_version' => '1.2.0',
      'version' => '1.2.0.0',
      'aliases' => 
      array (
      ),
      'reference' => 'ebcda2e628180b4df235b46a86e1d014c561f5d9',
    ),
    'alibabacloud/darabonba-openapi' => 
    array (
      'pretty_version' => '0.2.13',
      'version' => '0.2.13.0',
      'aliases' => 
      array (
      ),
      'reference' => '0213396384e2c064eefd614f3dd53636a63f987f',
    ),
    'alibabacloud/endpoint-util' => 
    array (
      'pretty_version' => '0.1.1',
      'version' => '0.1.1.0',
      'aliases' => 
      array (
      ),
      'reference' => 'f3fe88a25d8df4faa3b0ae14ff202a9cc094e6c5',
    ),
    'alibabacloud/gateway-spi' => 
    array (
      'pretty_version' => '1.0.0',
      'version' => '1.0.0.0',
      'aliases' => 
      array (
      ),
      'reference' => '7440f77750c329d8ab252db1d1d967314ccd1fcb',
    ),
    'alibabacloud/openapi-util' => 
    array (
      'pretty_version' => '0.2.1',
      'version' => '0.2.1.0',
      'aliases' => 
      array (
      ),
      'reference' => 'f31f7bcd835e08ca24b6b8ba33637eb4eceb093a',
    ),
    'alibabacloud/tea' => 
    array (
      'pretty_version' => '3.1.22',
      'version' => '3.1.22.0',
      'aliases' => 
      array (
      ),
      'reference' => 'f9c9b2c927253a1c23a5381cc655e41311be7f65',
    ),
    'alibabacloud/tea-fileform' => 
    array (
      'pretty_version' => '0.3.4',
      'version' => '0.3.4.0',
      'aliases' => 
      array (
      ),
      'reference' => '4bf0c75a045c8115aa8cb1a394bd08d8bb833181',
    ),
    'alibabacloud/tea-utils' => 
    array (
      'pretty_version' => '0.2.21',
      'version' => '********',
      'aliases' => 
      array (
      ),
      'reference' => '5039e45714c6456186d267f5d81a4b260a652495',
    ),
    'alibabacloud/tea-xml' => 
    array (
      'pretty_version' => '0.2.4',
      'version' => '*******',
      'aliases' => 
      array (
      ),
      'reference' => '3e0c000bf536224eebbac913c371bef174c0a16a',
    ),
    'alipaysdk/easysdk' => 
    array (
      'pretty_version' => '2.2.0',
      'version' => '*******',
      'aliases' => 
      array (
      ),
      'reference' => '7a1cfa83c7e140bded957498ea072c77611e6480',
    ),
    'aliyuncs/oss-sdk-php' => 
    array (
      'pretty_version' => 'v2.4.2',
      'version' => '*******',
      'aliases' => 
      array (
      ),
      'reference' => '0c9d902c33847c07efc66c4cdf823deaea8fc2b6',
    ),
    'bacon/bacon-qr-code' => 
    array (
      'pretty_version' => '2.0.4',
      'version' => '*******',
      'aliases' => 
      array (
      ),
      'reference' => 'f73543ac4e1def05f1a70bcd1525c8a157a1ad09',
    ),
    'carbonphp/carbon-doctrine-types' => 
    array (
      'pretty_version' => '2.1.0',
      'version' => '*******',
      'aliases' => 
      array (
      ),
      'reference' => '99f76ffa36cce3b70a4a6abce41dba15ca2e84cb',
    ),
    'clagiordano/weblibs-configmanager' => 
    array (
      'pretty_version' => 'v1.1.0',
      'version' => '*******',
      'aliases' => 
      array (
      ),
      'reference' => 'ecf584f5b3a27929175ff0abdba52f0131bef795',
    ),
    'danielstjules/stringy' => 
    array (
      'pretty_version' => '3.1.0',
      'version' => '3.1.0.0',
      'aliases' => 
      array (
      ),
      'reference' => 'df24ab62d2d8213bbbe88cc36fc35a4503b4bd7e',
    ),
    'dasprid/enum' => 
    array (
      'pretty_version' => '1.0.3',
      'version' => '1.0.3.0',
      'aliases' => 
      array (
      ),
      'reference' => '5abf82f213618696dda8e3bf6f64dd042d8542b2',
    ),
    'dragonmantank/cron-expression' => 
    array (
      'pretty_version' => 'v3.3.1',
      'version' => '3.3.1.0',
      'aliases' => 
      array (
      ),
      'reference' => 'be85b3f05b46c39bbc0d95f6c071ddff669510fa',
    ),
    'easywechat-composer/easywechat-composer' => 
    array (
      'pretty_version' => '1.4.0',
      'version' => '1.4.0.0',
      'aliases' => 
      array (
      ),
      'reference' => '93cfce1ec842b9a5b1b0791a52afd18b833f114a',
    ),
    'endroid/qr-code' => 
    array (
      'pretty_version' => '3.9.6',
      'version' => '3.9.6.0',
      'aliases' => 
      array (
      ),
      'reference' => '9cdd4f5d609bfc8811ca4a62b4d23eb16976242f',
    ),
    'ezyang/htmlpurifier' => 
    array (
      'pretty_version' => 'v4.14.0',
      'version' => '4.14.0.0',
      'aliases' => 
      array (
      ),
      'reference' => '12ab42bd6e742c70c0a52f7b82477fcd44e64b75',
    ),
    'guzzlehttp/command' => 
    array (
      'pretty_version' => '1.0.0',
      'version' => '1.0.0.0',
      'aliases' => 
      array (
      ),
      'reference' => '2aaa2521a8f8269d6f5dfc13fe2af12c76921034',
    ),
    'guzzlehttp/guzzle' => 
    array (
      'pretty_version' => '6.5.5',
      'version' => '6.5.5.0',
      'aliases' => 
      array (
      ),
      'reference' => '9d4290de1cfd701f38099ef7e183b64b4b7b0c5e',
    ),
    'guzzlehttp/guzzle-services' => 
    array (
      'pretty_version' => '1.1.3',
      'version' => '1.1.3.0',
      'aliases' => 
      array (
      ),
      'reference' => '9e3abf20161cbf662d616cbb995f2811771759f7',
    ),
    'guzzlehttp/promises' => 
    array (
      'pretty_version' => '1.4.1',
      'version' => '1.4.1.0',
      'aliases' => 
      array (
      ),
      'reference' => '8e7d04f1f6450fef59366c399cfad4b9383aa30d',
    ),
    'guzzlehttp/psr7' => 
    array (
      'pretty_version' => '1.8.2',
      'version' => '1.8.2.0',
      'aliases' => 
      array (
      ),
      'reference' => 'dc960a912984efb74d0a90222870c72c87f10c91',
    ),
    'hhxsv5/php-sse' => 
    array (
      'pretty_version' => 'v2.0.2',
      'version' => '2.0.2.0',
      'aliases' => 
      array (
      ),
      'reference' => '4244e4e8b416103f585cafcf813cff81061c5752',
    ),
    'khanamiryan/qrcode-detector-decoder' => 
    array (
      'pretty_version' => '1.0.5.1',
      'version' => '1.0.5.1',
      'aliases' => 
      array (
      ),
      'reference' => 'b96163d4f074970dfe67d4185e75e1f4541b30ca',
    ),
    'league/flysystem' => 
    array (
      'pretty_version' => '1.0.70',
      'version' => '1.0.70.0',
      'aliases' => 
      array (
      ),
      'reference' => '585824702f534f8d3cf7fab7225e8466cc4b7493',
    ),
    'league/flysystem-cached-adapter' => 
    array (
      'pretty_version' => '1.1.0',
      'version' => '*******',
      'aliases' => 
      array (
      ),
      'reference' => 'd1925efb2207ac4be3ad0c40b8277175f99ffaff',
    ),
    'lizhichao/one-sm' => 
    array (
      'pretty_version' => '1.10',
      'version' => '********',
      'aliases' => 
      array (
      ),
      'reference' => '687a012a44a5bfd4d9143a0234e1060543be455a',
    ),
    'maennchen/zipstream-php' => 
    array (
      'pretty_version' => '2.1.0',
      'version' => '*******',
      'aliases' => 
      array (
      ),
      'reference' => 'c4c5803cc1f93df3d2448478ef79394a5981cc58',
    ),
    'markbaker/complex' => 
    array (
      'pretty_version' => '3.0.1',
      'version' => '*******',
      'aliases' => 
      array (
      ),
      'reference' => 'ab8bc271e404909db09ff2d5ffa1e538085c0f22',
    ),
    'markbaker/matrix' => 
    array (
      'pretty_version' => '3.0.0',
      'version' => '*******',
      'aliases' => 
      array (
      ),
      'reference' => 'c66aefcafb4f6c269510e9ac46b82619a904c576',
    ),
    'meilisearch/meilisearch-php' => 
    array (
      'pretty_version' => 'v1.11.0',
      'version' => '********',
      'aliases' => 
      array (
      ),
      'reference' => '4dd127cb87848f7a7b28e83bb355b4b86d329867',
    ),
    'monolog/monolog' => 
    array (
      'pretty_version' => '2.2.0',
      'version' => '*******',
      'aliases' => 
      array (
      ),
      'reference' => '1cb1cde8e8dd0f70cc0fe51354a59acad9302084',
    ),
    'mtdowling/cron-expression' => 
    array (
      'replaced' => 
      array (
        0 => '^1.0',
      ),
    ),
    'mtdowling/jmespath.php' => 
    array (
      'pretty_version' => '2.6.0',
      'version' => '2.6.0.0',
      'aliases' => 
      array (
      ),
      'reference' => '42dae2cbd13154083ca6d70099692fef8ca84bfb',
    ),
    'myclabs/php-enum' => 
    array (
      'pretty_version' => '1.7.7',
      'version' => '1.7.7.0',
      'aliases' => 
      array (
      ),
      'reference' => 'd178027d1e679832db9f38248fcc7200647dc2b7',
    ),
    'nesbot/carbon' => 
    array (
      'pretty_version' => '2.73.0',
      'version' => '2.73.0.0',
      'aliases' => 
      array (
      ),
      'reference' => '9228ce90e1035ff2f0db84b40ec2e023ed802075',
    ),
    'nette/php-generator' => 
    array (
      'pretty_version' => 'v3.5.4',
      'version' => '3.5.4.0',
      'aliases' => 
      array (
      ),
      'reference' => '59bb35ed6e8da95854fbf7b7d47dce6156b42915',
    ),
    'nette/utils' => 
    array (
      'pretty_version' => 'v3.2.3',
      'version' => '3.2.3.0',
      'aliases' => 
      array (
      ),
      'reference' => '5c36cc1ba9bb6abb8a9e425cf054e0c3fd5b9822',
    ),
    'open-smf/connection-pool' => 
    array (
      'pretty_version' => 'v1.0.16',
      'version' => '1.0.16.0',
      'aliases' => 
      array (
      ),
      'reference' => 'f70e47dbf56f1869d3207e15825cf38810b865e0',
    ),
    'overtrue/socialite' => 
    array (
      'pretty_version' => '2.0.23',
      'version' => '2.0.23.0',
      'aliases' => 
      array (
      ),
      'reference' => '0bc60597b589592243f074a4d9016aabd2e9cfb2',
    ),
    'overtrue/wechat' => 
    array (
      'pretty_version' => '4.4.1',
      'version' => '4.4.1.0',
      'aliases' => 
      array (
      ),
      'reference' => 'a31939c7393a192d1095c280ee3be254bb38e279',
    ),
    'php-http/async-client-implementation' => 
    array (
      'provided' => 
      array (
        0 => '*',
      ),
    ),
    'php-http/client-implementation' => 
    array (
      'provided' => 
      array (
        0 => '*',
      ),
    ),
    'php-http/discovery' => 
    array (
      'pretty_version' => '1.20.0',
      'version' => '1.20.0.0',
      'aliases' => 
      array (
      ),
      'reference' => '82fe4c73ef3363caed49ff8dd1539ba06044910d',
    ),
    'phpoffice/phpspreadsheet' => 
    array (
      'pretty_version' => '1.19.0',
      'version' => '1.19.0.0',
      'aliases' => 
      array (
      ),
      'reference' => 'a9ab55bfae02eecffb3df669a2e19ba0e2f04bbf',
    ),
    'pimple/pimple' => 
    array (
      'pretty_version' => 'v3.2.3',
      'version' => '3.2.3.0',
      'aliases' => 
      array (
      ),
      'reference' => '9e403941ef9d65d20cba7d54e29fe906db42cf32',
    ),
    'psr/cache' => 
    array (
      'pretty_version' => '1.0.1',
      'version' => '1.0.1.0',
      'aliases' => 
      array (
      ),
      'reference' => 'd11b50ad223250cf17b86e38383413f5a6764bf8',
    ),
    'psr/cache-implementation' => 
    array (
      'provided' => 
      array (
        0 => '1.0|2.0',
      ),
    ),
    'psr/clock' => 
    array (
      'pretty_version' => '1.0.0',
      'version' => '1.0.0.0',
      'aliases' => 
      array (
      ),
      'reference' => 'e41a24703d4560fd0acb709162f73b8adfc3aa0d',
    ),
    'psr/clock-implementation' => 
    array (
      'provided' => 
      array (
        0 => '1.0',
      ),
    ),
    'psr/container' => 
    array (
      'pretty_version' => '1.0.0',
      'version' => '1.0.0.0',
      'aliases' => 
      array (
      ),
      'reference' => 'b7ce3b176482dbbc1245ebf52b181af44c2cf55f',
    ),
    'psr/event-dispatcher-implementation' => 
    array (
      'provided' => 
      array (
        0 => '1.0',
      ),
    ),
    'psr/http-client' => 
    array (
      'pretty_version' => '1.0.1',
      'version' => '1.0.1.0',
      'aliases' => 
      array (
      ),
      'reference' => '2dfb5f6c5eff0e91e20e913f8c5452ed95b86621',
    ),
    'psr/http-client-implementation' => 
    array (
      'provided' => 
      array (
        0 => '*',
      ),
    ),
    'psr/http-factory' => 
    array (
      'pretty_version' => '1.0.1',
      'version' => '1.0.1.0',
      'aliases' => 
      array (
      ),
      'reference' => '12ac7fcd07e5b077433f5f2bee95b3a771bf61be',
    ),
    'psr/http-factory-implementation' => 
    array (
      'provided' => 
      array (
        0 => '*',
      ),
    ),
    'psr/http-message' => 
    array (
      'pretty_version' => '1.0.1',
      'version' => '1.0.1.0',
      'aliases' => 
      array (
      ),
      'reference' => 'f6561bf28d520154e4b0ec72be95418abe6d9363',
    ),
    'psr/http-message-implementation' => 
    array (
      'provided' => 
      array (
        0 => '1.0',
        1 => '*',
      ),
    ),
    'psr/log' => 
    array (
      'pretty_version' => '1.1.3',
      'version' => '1.1.3.0',
      'aliases' => 
      array (
      ),
      'reference' => '0f73288fd15629204f9d42b7055f72dacbe811fc',
    ),
    'psr/log-implementation' => 
    array (
      'provided' => 
      array (
        0 => '1.0.0',
      ),
    ),
    'psr/simple-cache' => 
    array (
      'pretty_version' => '1.0.1',
      'version' => '1.0.1.0',
      'aliases' => 
      array (
      ),
      'reference' => '408d5eafb83c57f6365a3ca330ff23aa4a5fa39b',
    ),
    'psr/simple-cache-implementation' => 
    array (
      'provided' => 
      array (
        0 => '1.0',
      ),
    ),
    'qcloud/cos-sdk-v5' => 
    array (
      'pretty_version' => 'v2.2.0',
      'version' => '*******',
      'aliases' => 
      array (
      ),
      'reference' => 'e67ad8143695192ee206bcbcafc78c08da92c621',
    ),
    'qiniu/php-sdk' => 
    array (
      'pretty_version' => 'v7.3.0',
      'version' => '7.3.0.0',
      'aliases' => 
      array (
      ),
      'reference' => '0a461e13b09545b23df361843c6a65fdd3a26426',
    ),
    'ralouphie/getallheaders' => 
    array (
      'pretty_version' => '3.0.3',
      'version' => '3.0.3.0',
      'aliases' => 
      array (
      ),
      'reference' => '120b605dfeb996808c31b6477290a714d356e822',
    ),
    'rmccue/requests' => 
    array (
      'pretty_version' => 'v1.8.1',
      'version' => '1.8.1.0',
      'aliases' => 
      array (
      ),
      'reference' => '82e6936366eac3af4d836c18b9d8c31028fe4cd5',
    ),
    'setasign/fpdf' => 
    array (
      'pretty_version' => '1.8.6',
      'version' => '1.8.6.0',
      'aliases' => 
      array (
      ),
      'reference' => '0838e0ee4925716fcbbc50ad9e1799b5edfae0a0',
    ),
    'setasign/fpdi' => 
    array (
      'pretty_version' => 'v2.6.2',
      'version' => '2.6.2.0',
      'aliases' => 
      array (
      ),
      'reference' => '9e013b376939c0d4029f54150d2a16f3c67a5797',
    ),
    'songshenzong/support' => 
    array (
      'pretty_version' => '2.0.5',
      'version' => '2.0.5.0',
      'aliases' => 
      array (
      ),
      'reference' => '34973c04ffcf226e503f1c3a69d30ac49f7621f6',
    ),
    'stechstudio/backoff' => 
    array (
      'pretty_version' => '1.2',
      'version' => '1.2.0.0',
      'aliases' => 
      array (
      ),
      'reference' => '816e46107a6be2e1072ba0ff2cb26034872dfa49',
    ),
    'swoole/ide-helper' => 
    array (
      'pretty_version' => '4.7.1',
      'version' => '4.7.1.0',
      'aliases' => 
      array (
      ),
      'reference' => '918a98b5b264425fdb59461d9bbd7f9b504ead71',
    ),
    'symfony/cache' => 
    array (
      'pretty_version' => 'v4.4.22',
      'version' => '4.4.22.0',
      'aliases' => 
      array (
      ),
      'reference' => '0da1df9b1a31f328f1711b5cd922c38a15c5fc74',
    ),
    'symfony/cache-contracts' => 
    array (
      'pretty_version' => 'v1.1.10',
      'version' => '1.1.10.0',
      'aliases' => 
      array (
      ),
      'reference' => '8d5489c10ef90aa7413e4921fc3c0520e24cbed7',
    ),
    'symfony/cache-implementation' => 
    array (
      'provided' => 
      array (
        0 => '1.0|2.0',
      ),
    ),
    'symfony/deprecation-contracts' => 
    array (
      'pretty_version' => 'v2.5.4',
      'version' => '2.5.4.0',
      'aliases' => 
      array (
      ),
      'reference' => '605389f2a7e5625f273b53960dc46aeaf9c62918',
    ),
    'symfony/event-dispatcher' => 
    array (
      'pretty_version' => 'v4.4.20',
      'version' => '4.4.20.0',
      'aliases' => 
      array (
      ),
      'reference' => 'c352647244bd376bf7d31efbd5401f13f50dad0c',
    ),
    'symfony/event-dispatcher-contracts' => 
    array (
      'pretty_version' => 'v1.1.9',
      'version' => '1.1.9.0',
      'aliases' => 
      array (
      ),
      'reference' => '84e23fdcd2517bf37aecbd16967e83f0caee25a7',
    ),
    'symfony/event-dispatcher-implementation' => 
    array (
      'provided' => 
      array (
        0 => '1.1',
      ),
    ),
    'symfony/finder' => 
    array (
      'pretty_version' => 'v4.4.30',
      'version' => '4.4.30.0',
      'aliases' => 
      array (
      ),
      'reference' => '70362f1e112280d75b30087c7598b837c1b468b6',
    ),
    'symfony/http-foundation' => 
    array (
      'pretty_version' => 'v4.4.22',
      'version' => '4.4.22.0',
      'aliases' => 
      array (
      ),
      'reference' => '1a6f87ef99d05b1bf5c865b4ef7992263e1cb081',
    ),
    'symfony/inflector' => 
    array (
      'pretty_version' => 'v4.4.25',
      'version' => '4.4.25.0',
      'aliases' => 
      array (
      ),
      'reference' => 'fc695ab721136b27aa84427a0fa80189761266ef',
    ),
    'symfony/mime' => 
    array (
      'pretty_version' => 'v4.4.22',
      'version' => '4.4.22.0',
      'aliases' => 
      array (
      ),
      'reference' => '36f2e59c90762bb09170553130a4dc1934cada58',
    ),
    'symfony/options-resolver' => 
    array (
      'pretty_version' => 'v4.4.25',
      'version' => '4.4.25.0',
      'aliases' => 
      array (
      ),
      'reference' => '2e607d627c70aa56284a02d34fc60dfe3a9a352e',
    ),
    'symfony/polyfill-ctype' => 
    array (
      'pretty_version' => 'v1.23.0',
      'version' => '1.23.0.0',
      'aliases' => 
      array (
      ),
      'reference' => '46cd95797e9df938fdd2b03693b5fca5e64b01ce',
    ),
    'symfony/polyfill-intl-idn' => 
    array (
      'pretty_version' => 'v1.22.1',
      'version' => '1.22.1.0',
      'aliases' => 
      array (
      ),
      'reference' => '2d63434d922daf7da8dd863e7907e67ee3031483',
    ),
    'symfony/polyfill-intl-normalizer' => 
    array (
      'pretty_version' => 'v1.22.1',
      'version' => '1.22.1.0',
      'aliases' => 
      array (
      ),
      'reference' => '43a0283138253ed1d48d352ab6d0bdb3f809f248',
    ),
    'symfony/polyfill-mbstring' => 
    array (
      'pretty_version' => 'v1.22.0',
      'version' => '1.22.0.0',
      'aliases' => 
      array (
      ),
      'reference' => 'f377a3dd1fde44d37b9831d68dc8dea3ffd28e13',
    ),
    'symfony/polyfill-php72' => 
    array (
      'pretty_version' => 'v1.22.0',
      'version' => '1.22.0.0',
      'aliases' => 
      array (
      ),
      'reference' => 'cc6e6f9b39fe8075b3dabfbaf5b5f645ae1340c9',
    ),
    'symfony/polyfill-php80' => 
    array (
      'pretty_version' => 'v1.22.0',
      'version' => '1.22.0.0',
      'aliases' => 
      array (
      ),
      'reference' => 'dc3063ba22c2a1fd2f45ed856374d79114998f91',
    ),
    'symfony/process' => 
    array (
      'pretty_version' => 'v5.4.47',
      'version' => '5.4.47.0',
      'aliases' => 
      array (
      ),
      'reference' => '5d1662fb32ebc94f17ddb8d635454a776066733d',
    ),
    'symfony/property-access' => 
    array (
      'pretty_version' => 'v4.4.25',
      'version' => '4.4.25.0',
      'aliases' => 
      array (
      ),
      'reference' => '3af7c21b4128eadbace0800b51054a81bff896c6',
    ),
    'symfony/psr-http-message-bridge' => 
    array (
      'pretty_version' => 'v2.1.0',
      'version' => '*******',
      'aliases' => 
      array (
      ),
      'reference' => '81db2d4ae86e9f0049828d9343a72b9523884e5d',
    ),
    'symfony/service-contracts' => 
    array (
      'pretty_version' => 'v1.1.9',
      'version' => '1.1.9.0',
      'aliases' => 
      array (
      ),
      'reference' => 'b776d18b303a39f56c63747bcb977ad4b27aca26',
    ),
    'symfony/translation' => 
    array (
      'pretty_version' => 'v5.4.45',
      'version' => '5.4.45.0',
      'aliases' => 
      array (
      ),
      'reference' => '98f26acc99341ca4bab345fb14d7b1d7cb825bed',
    ),
    'symfony/translation-contracts' => 
    array (
      'pretty_version' => 'v2.5.4',
      'version' => '2.5.4.0',
      'aliases' => 
      array (
      ),
      'reference' => '450d4172653f38818657022252f9d81be89ee9a8',
    ),
    'symfony/translation-implementation' => 
    array (
      'provided' => 
      array (
        0 => '2.3',
      ),
    ),
    'symfony/var-dumper' => 
    array (
      'pretty_version' => 'v4.4.18',
      'version' => '4.4.18.0',
      'aliases' => 
      array (
      ),
      'reference' => '4f31364bbc8177f2a6dbc125ac3851634ebe2a03',
    ),
    'symfony/var-exporter' => 
    array (
      'pretty_version' => 'v4.4.22',
      'version' => '4.4.22.0',
      'aliases' => 
      array (
      ),
      'reference' => 'ef3054c7e878fe0837ef9ac2c5ecfddfd27dd9e9',
    ),
    'tencentcloud/tencentcloud-sdk-php' => 
    array (
      'pretty_version' => '3.0.389',
      'version' => '3.0.389.0',
      'aliases' => 
      array (
      ),
      'reference' => 'c53cc9cd36061c0f90abf89682f209f2248dd8ff',
    ),
    'topthink/framework' => 
    array (
      'pretty_version' => 'v6.0.7',
      'version' => '6.0.7.0',
      'aliases' => 
      array (
      ),
      'reference' => 'db8fe22520a9660dd5e4c87e304034ac49e39270',
    ),
    'topthink/think' => 
    array (
      'pretty_version' => '1.0.0+no-version-set',
      'version' => '1.0.0.0',
      'aliases' => 
      array (
      ),
      'reference' => NULL,
    ),
    'topthink/think-captcha' => 
    array (
      'pretty_version' => 'v3.0.3',
      'version' => '3.0.3.0',
      'aliases' => 
      array (
      ),
      'reference' => '1eef3717c1bcf4f5bbe2d1a1c704011d330a8b55',
    ),
    'topthink/think-helper' => 
    array (
      'pretty_version' => 'v3.1.4',
      'version' => '3.1.4.0',
      'aliases' => 
      array (
      ),
      'reference' => 'c28d37743bda4a0455286ca85b17b5791d626e10',
    ),
    'topthink/think-multi-app' => 
    array (
      'pretty_version' => 'v1.0.14',
      'version' => '1.0.14.0',
      'aliases' => 
      array (
      ),
      'reference' => 'ccaad7c2d33f42cb1cc2a78d6610aaec02cea4c3',
    ),
    'topthink/think-orm' => 
    array (
      'pretty_version' => 'v2.0.36',
      'version' => '2.0.36.0',
      'aliases' => 
      array (
      ),
      'reference' => 'f48dc09050f25029d41a66bfc9c3c403e4f82024',
    ),
    'topthink/think-queue' => 
    array (
      'pretty_version' => 'v3.0.12',
      'version' => '3.0.12.0',
      'aliases' => 
      array (
      ),
      'reference' => '48adee0298a363f497b8ba07628d5b63cf020868',
    ),
    'topthink/think-swoole' => 
    array (
      'pretty_version' => 'v3.1.3',
      'version' => '3.1.3.0',
      'aliases' => 
      array (
      ),
      'reference' => 'df78b1f6eb6cd8f45f49ab7b0d4cc65595181504',
    ),
    'topthink/think-template' => 
    array (
      'pretty_version' => 'v2.0.8',
      'version' => '2.0.8.0',
      'aliases' => 
      array (
      ),
      'reference' => 'abfc293f74f9ef5127b5c416310a01fe42e59368',
    ),
    'topthink/think-trace' => 
    array (
      'pretty_version' => 'v1.4',
      'version' => '1.4.0.0',
      'aliases' => 
      array (
      ),
      'reference' => '9a9fa8f767b6c66c5a133ad21ca1bc96ad329444',
    ),
    'topthink/think-view' => 
    array (
      'pretty_version' => 'v1.0.14',
      'version' => '1.0.14.0',
      'aliases' => 
      array (
      ),
      'reference' => 'edce0ae2c9551ab65f9e94a222604b0dead3576d',
    ),
    'webmozart/assert' => 
    array (
      'pretty_version' => '1.11.0',
      'version' => '********',
      'aliases' => 
      array (
      ),
      'reference' => '11cb2199493b2f8a3b53e7f19068fc6aac760991',
    ),
    'workerman/gateway-worker' => 
    array (
      'pretty_version' => 'v3.1.18',
      'version' => '3.1.18.0',
      'aliases' => 
      array (
      ),
      'reference' => '8d371770cb0dbd8166b94d6049a6a497c13476df',
    ),
    'workerman/workerman' => 
    array (
      'pretty_version' => 'v4.1.17',
      'version' => '4.1.17.0',
      'aliases' => 
      array (
      ),
      'reference' => 'bb9e4b0c3fc3931a2ae38a1fb7a3ac09246efae9',
    ),
    'xin/container' => 
    array (
      'pretty_version' => '2.0.1',
      'version' => '2.0.1.0',
      'aliases' => 
      array (
      ),
      'reference' => '97bb67f87dd851545938a1f2fe0ffbd379e3ff81',
    ),
    'xin/helper' => 
    array (
      'pretty_version' => '1.0.0',
      'version' => '1.0.0.0',
      'aliases' => 
      array (
      ),
      'reference' => '02a58132dae2aea2d1c0b8e66f55125969224747',
    ),
    'yansongda/pay' => 
    array (
      'pretty_version' => 'v2.10.5',
      'version' => '2.10.5.0',
      'aliases' => 
      array (
      ),
      'reference' => 'f7d93ed784de4ca09d3386d28139c724ddd526fc',
    ),
    'yansongda/supports' => 
    array (
      'pretty_version' => 'v2.2.0',
      'version' => '*******',
      'aliases' => 
      array (
      ),
      'reference' => 'de9a8d38b0461ddf9c12f27390dad9a40c9b4e3b',
    ),
    'yly-openapi/yly-openapi-sdk' => 
    array (
      'pretty_version' => 'v1.0.3',
      'version' => '1.0.3.0',
      'aliases' => 
      array (
      ),
      'reference' => 'f4ca7a2296fcb7001eb154dcba1cc389837c9a27',
    ),
  ),
);
