{layout name="layout1" /}
<style>
    .layui-table-cell { height: auto; }
    .layui-input-block {
        line-height: 38px;
    }
</style>
<div class="layui-form" style="margin-left: 50px;margin-top: 15px;margin-bottom:30px;">
  <input type="hidden" name="id" value="{$id}" />
  <div class="layui-form-item" style="margin-bottom:0;">
    <label class="layui-form-label">提现审核：</label>
    <div class="layui-input-block">
      <input type="radio" name="review_status" value="1" title="审核通过">
      <input type="radio" name="review_status" value="2" title="审核拒绝" checked>
    </div>
  </div>
  <div class="layui-form-item" style="margin-bottom:0;">
    <label class="layui-form-label"><font color="red">*</font>审核备注：</label>
    <div class="layui-input-block">
      <textarea name="description" placeholder="请输入内容" class="layui-textarea" style="width:380px;height:120px;"></textarea>
    </div>
  </div>

  <div class="layui-form-item layui-hide">
    <input type="button" lay-submit lay-filter="addSubmit" id="addSubmit" value="确认">
  </div>
</div>

<script>
    layui.config({
        version:"{$front_version}",
        base: '/static/plug/layui-admin/dist/layuiadmin/' //静态资源所在路径
    }).extend({
        index: 'lib/index' //主入口模块
    }).use(['index','table','like'], function(){
        var $ = layui.$
            ,form = layui.form
            ,table = layui.table
            ,like = layui.like;
      // 确认
      form.on('submit(confirm)', function(data){
        console.log(data.field)
          // 审核
          like.ajax({
              url:'{:url("activity_area.goods/audit")}',
              data:{'id':data.field.id,'review_status':data.field.review_status,'description': data.field.description},
              type:"post",
              success:function(res)
              {
                if(res.code == 1)
                {
                  layui.layer.msg(res.msg, {offset: '15px', icon: 1, time: 1000}, function(){
                    parent.location.reload();
                  });
                }
              }
          });
      });
      // 返回
      form.on('submit(cancel)', function(data){
        var index = parent.layer.getFrameIndex(window.name); 
        parent.layer.close(index);
        return false; //阻止表单跳转。如果需要表单跳转，去掉这段即可。
      });
    });
</script>