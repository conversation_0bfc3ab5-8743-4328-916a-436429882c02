/* 现代化商品表单样式系统 */

/* CSS变量定义 */
:root {
    --primary-color: #4f46e5;
    --primary-light: #6366f1;
    --primary-dark: #3730a3;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    --info-color: #3b82f6;
    
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;
    
    --border-radius: 8px;
    --border-radius-lg: 12px;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    
    --transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 重置和基础样式 */
* {
    box-sizing: border-box;
}

/* 主容器 */
.goods-form-modern {
    max-width: 1200px;
    margin: 0 auto;
    background: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
}

/* 步骤导航 */
.form-steps {
    display: flex;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
    padding: 0;
    margin: 0;
    list-style: none;
    position: relative;
}

.form-step {
    flex: 1;
    position: relative;
}

.form-step-link {
    display: flex;
    align-items: center;
    padding: 20px 24px;
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    transition: var(--transition);
    font-weight: 500;
    position: relative;
}

.form-step-link:hover {
    color: rgba(255, 255, 255, 0.9);
    background: rgba(255, 255, 255, 0.1);
}

.form-step.active .form-step-link {
    color: white;
    background: rgba(255, 255, 255, 0.15);
}

.form-step.completed .form-step-link {
    color: rgba(255, 255, 255, 0.9);
}

.step-icon {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    font-size: 12px;
    transition: var(--transition);
}

.form-step.active .step-icon {
    background: white;
    color: var(--primary-color);
}

.form-step.completed .step-icon {
    background: var(--success-color);
    color: white;
}

.step-title {
    font-size: 14px;
    font-weight: 500;
}

/* 表单内容区域 */
.form-content {
    padding: 32px;
}

.form-section {
    margin-bottom: 40px;
}

.section-header {
    display: flex;
    align-items: center;
    margin-bottom: 24px;
    padding-bottom: 12px;
    border-bottom: 2px solid var(--gray-100);
}

.section-icon {
    width: 32px;
    height: 32px;
    border-radius: var(--border-radius);
    background: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    font-size: 16px;
}

.section-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--gray-800);
    margin: 0;
}

.section-description {
    color: var(--gray-500);
    font-size: 14px;
    margin: 4px 0 0 0;
}

/* 表单字段 */
.form-field {
    margin-bottom: 24px;
}

.form-label {
    display: block;
    font-size: 14px;
    font-weight: 500;
    color: var(--gray-700);
    margin-bottom: 8px;
}

.form-label.required::after {
    content: '*';
    color: var(--error-color);
    margin-left: 4px;
}

.form-input {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid var(--gray-200);
    border-radius: var(--border-radius);
    font-size: 14px;
    transition: var(--transition);
    background: white;
}

.form-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.form-input.error {
    border-color: var(--error-color);
}

.form-input.success {
    border-color: var(--success-color);
}

.form-textarea {
    min-height: 100px;
    resize: vertical;
}

.form-select {
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 12px center;
    background-repeat: no-repeat;
    background-size: 16px;
    padding-right: 40px;
}

/* 表单提示 */
.form-help {
    font-size: 12px;
    color: var(--gray-500);
    margin-top: 6px;
    display: flex;
    align-items: center;
}

.form-help-icon {
    margin-right: 4px;
    font-size: 14px;
}

.form-error {
    color: var(--error-color);
}

.form-success {
    color: var(--success-color);
}

/* 网格布局 */
.form-grid {
    display: grid;
    gap: 24px;
}

.form-grid-2 {
    grid-template-columns: repeat(2, 1fr);
}

.form-grid-3 {
    grid-template-columns: repeat(3, 1fr);
}

.form-grid-4 {
    grid-template-columns: repeat(4, 1fr);
}

/* 响应式 */
@media (max-width: 768px) {
    .form-grid-2,
    .form-grid-3,
    .form-grid-4 {
        grid-template-columns: 1fr;
    }
    
    .form-steps {
        flex-direction: column;
    }
    
    .form-content {
        padding: 20px;
    }
    
    .step-title {
        display: none;
    }
}

/* 按钮样式 */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 12px 24px;
    border: none;
    border-radius: var(--border-radius);
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition);
    gap: 8px;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-secondary {
    background: var(--gray-100);
    color: var(--gray-700);
}

.btn-secondary:hover {
    background: var(--gray-200);
}

.btn-success {
    background: var(--success-color);
    color: white;
}

.btn-danger {
    background: var(--error-color);
    color: white;
}

.btn-sm {
    padding: 8px 16px;
    font-size: 12px;
}

.btn-lg {
    padding: 16px 32px;
    font-size: 16px;
}

/* 卡片样式 */
.card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    overflow: hidden;
}

.card-header {
    padding: 16px 20px;
    background: var(--gray-50);
    border-bottom: 1px solid var(--gray-200);
    font-weight: 500;
}

.card-body {
    padding: 20px;
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.fade-in {
    animation: fadeIn 0.3s ease-out;
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

/* 加载状态 */
.loading {
    position: relative;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid var(--gray-300);
    border-top-color: var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* 图片上传组件 */
.image-upload {
    border: 2px dashed var(--gray-300);
    border-radius: var(--border-radius-lg);
    padding: 24px;
    text-align: center;
    transition: var(--transition);
    background: var(--gray-50);
}

.image-upload:hover {
    border-color: var(--primary-color);
    background: rgba(79, 70, 229, 0.05);
}

.image-upload.dragover {
    border-color: var(--primary-color);
    background: rgba(79, 70, 229, 0.1);
    transform: scale(1.02);
}

.image-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 16px;
    margin-top: 16px;
}

.image-item {
    position: relative;
    aspect-ratio: 1;
    border-radius: var(--border-radius);
    overflow: hidden;
    background: var(--gray-100);
    cursor: move;
    transition: var(--transition);
    group: image-item;
}

.image-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.image-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: var(--transition);
    gap: 8px;
}

.image-item:hover .image-overlay {
    opacity: 1;
}

.image-action {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.9);
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
    color: var(--gray-700);
}

.image-action:hover {
    background: white;
    transform: scale(1.1);
}

.image-action.delete {
    background: rgba(239, 68, 68, 0.9);
    color: white;
}

.image-action.delete:hover {
    background: var(--error-color);
}

.image-badge {
    position: absolute;
    top: 8px;
    left: 8px;
    background: var(--primary-color);
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: 600;
}

.image-badge.main {
    background: var(--error-color);
}

.image-add {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    aspect-ratio: 1;
    border: 2px dashed var(--gray-300);
    border-radius: var(--border-radius);
    background: var(--gray-50);
    cursor: pointer;
    transition: var(--transition);
    color: var(--gray-500);
}

.image-add:hover {
    border-color: var(--primary-color);
    background: rgba(79, 70, 229, 0.05);
    color: var(--primary-color);
}

.image-add-icon {
    font-size: 24px;
    margin-bottom: 8px;
}

.image-add-text {
    font-size: 12px;
    font-weight: 500;
}

/* 拖拽排序状态 */
.sortable-ghost {
    opacity: 0.5;
    transform: rotate(5deg);
}

.sortable-chosen {
    transform: scale(1.05);
    z-index: 999;
}

.sortable-drag {
    transform: rotate(5deg) scale(1.05);
    box-shadow: var(--shadow-lg);
    z-index: 999;
}

/* 规格管理 */
.spec-selector {
    display: flex;
    gap: 16px;
    margin-bottom: 24px;
}

.spec-option {
    flex: 1;
    padding: 16px;
    border: 2px solid var(--gray-200);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    text-align: center;
}

.spec-option:hover {
    border-color: var(--primary-color);
    background: rgba(79, 70, 229, 0.05);
}

.spec-option.active {
    border-color: var(--primary-color);
    background: rgba(79, 70, 229, 0.1);
    color: var(--primary-color);
}

.spec-option-icon {
    font-size: 24px;
    margin-bottom: 8px;
}

.spec-option-title {
    font-weight: 500;
    margin-bottom: 4px;
}

.spec-option-desc {
    font-size: 12px;
    color: var(--gray-500);
}

.spec-builder {
    background: var(--gray-50);
    border-radius: var(--border-radius);
    padding: 20px;
    margin-top: 16px;
}

.spec-item {
    background: white;
    border-radius: var(--border-radius);
    padding: 16px;
    margin-bottom: 16px;
    box-shadow: var(--shadow-sm);
    position: relative;
}

.spec-item-header {
    display: flex;
    align-items: center;
    justify-content: between;
    margin-bottom: 12px;
}

.spec-item-title {
    font-weight: 500;
    color: var(--gray-800);
}

.spec-item-remove {
    background: none;
    border: none;
    color: var(--gray-400);
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: var(--transition);
}

.spec-item-remove:hover {
    color: var(--error-color);
    background: rgba(239, 68, 68, 0.1);
}

.spec-values {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.spec-value {
    background: var(--gray-100);
    border: 1px solid var(--gray-200);
    border-radius: 16px;
    padding: 6px 12px;
    font-size: 12px;
    position: relative;
    display: flex;
    align-items: center;
    gap: 6px;
}

.spec-value-remove {
    background: none;
    border: none;
    color: var(--gray-400);
    cursor: pointer;
    font-size: 10px;
    padding: 0;
    width: 14px;
    height: 14px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.spec-value-remove:hover {
    color: var(--error-color);
    background: rgba(239, 68, 68, 0.1);
}

.spec-add-value {
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 16px;
    padding: 6px 12px;
    font-size: 12px;
    cursor: pointer;
    transition: var(--transition);
}

.spec-add-value:hover {
    background: var(--primary-dark);
}
