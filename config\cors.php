<?php

return [
    // 允许跨域访问的域名，* 表示所有域名
    'allow_origin'        => ['*'],
    // 是否允许发送Cookie
    'allow_credentials'   => true,
    // 允许请求的方法
    'allow_methods'       => 'GET, POST, PUT, DELETE, OPTIONS',
    // 允许携带的Header信息
    'allow_headers'       => 'Authorization, Content-Type, If-Match, If-Modified-Since, If-None-Match, If-Unmodified-Since, X-CSRF-TOKEN, X-Requested-With',
    // 暴露给JS获取的Header信息
    'expose_headers'      => '',
    // 缓存OPTIONS请求响应时间，单位：秒
    'max_age'             => 86400,
    // 指定允许访问的请求头
    'allowed_headers'     => 'Authorization, Content-Type',
];
