{"version": 3, "sources": ["webpack:///./node_modules/core-js/internals/delete-property-or-throw.js", "webpack:///./src/views/window/index.vue", "webpack:///./src/views/window/components/TheWindowHeader.vue", "webpack:///src/views/window/components/TheWindowHeader.vue", "webpack:///./src/views/window/components/TheWindowHeader.vue?fbd0", "webpack:///./src/views/window/components/TheWindowHeader.vue?34fd", "webpack:///./src/views/window/components/TheWindowMain.vue", "webpack:///./src/components/ChatMessage/index.vue", "webpack:///src/components/ChatMessage/index.vue", "webpack:///./src/components/ChatMessage/index.vue?271d", "webpack:///./src/components/ChatMessage/index.vue?4178", "webpack:///./src/views/window/components/QuickReply.vue", "webpack:///src/views/window/components/QuickReply.vue", "webpack:///./src/views/window/components/QuickReply.vue?33a5", "webpack:///./src/views/window/components/QuickReply.vue?635c", "webpack:///./src/utils/enum.js", "webpack:///./src/components/Morebar/index.vue", "webpack:///src/components/Morebar/index.vue", "webpack:///./src/components/Morebar/index.vue?12f9", "webpack:///./src/components/Morebar/index.vue?fd3f", "webpack:///./src/components/ChatContent/index.vue", "webpack:///src/components/ChatContent/index.vue", "webpack:///./src/components/ChatContent/index.vue?50df", "webpack:///./src/components/ChatContent/index.vue?9647", "webpack:///./src/utils/emojiArr.js", "webpack:///./src/utils/date.js", "webpack:///src/views/window/components/TheWindowMain.vue", "webpack:///./src/views/window/components/TheWindowMain.vue?7d1d", "webpack:///./src/views/window/components/TheWindowMain.vue?7013", "webpack:///./src/views/window/components/TheWindowAsideLeft.vue", "webpack:///./src/components/ChatUserItem/index.vue", "webpack:///src/components/ChatUserItem/index.vue", "webpack:///./src/components/ChatUserItem/index.vue?0ff6", "webpack:///./src/components/ChatUserItem/index.vue?1979", "webpack:///./src/components/LoadingMore/index.vue", "webpack:///src/components/LoadingMore/index.vue", "webpack:///./src/components/LoadingMore/index.vue?ab39", "webpack:///./src/components/LoadingMore/index.vue?97a9", "webpack:///src/views/window/components/TheWindowAsideLeft.vue", "webpack:///./src/views/window/components/TheWindowAsideLeft.vue?379c", "webpack:///./src/views/window/components/TheWindowAsideLeft.vue?d24c", "webpack:///./src/views/window/components/TheWindowAsideRight.vue", "webpack:///src/views/window/components/TheWindowAsideRight.vue", "webpack:///./src/views/window/components/TheWindowAsideRight.vue?e5aa", "webpack:///./src/views/window/components/TheWindowAsideRight.vue?4046", "webpack:///./src/components/PromptTone/index.vue", "webpack:///src/components/PromptTone/index.vue", "webpack:///./src/components/PromptTone/index.vue?3aa2", "webpack:///./src/components/PromptTone/index.vue?f985", "webpack:///./src/utils/socket.js", "webpack:///src/views/window/index.vue", "webpack:///./src/views/window/index.vue?cd61", "webpack:///./src/views/window/index.vue?c329", "webpack:///./src/components/Morebar/index.vue?7583", "webpack:///./node_modules/core-js/modules/es.array.unshift.js", "webpack:///./src/views/window/index.vue?405a", "webpack:///./src/components/ChatContent/index.vue?83c3", "webpack:///./src/views/window/components/TheWindowHeader.vue?4ef0", "webpack:///./src/views/window/components/TheWindowAsideRight.vue?15ec", "webpack:///./src/assets/images/biaoqing.png", "webpack:///./src/components/ChatMessage/index.vue?6461", "webpack:///./src/views/window/components/TheWindowAsideLeft.vue?ac58", "webpack:///./src/views/window/components/TheWindowMain.vue?a81d", "webpack:///./src/components/ChatUserItem/index.vue?17f9", "webpack:///./src/views/window/components/QuickReply.vue?00d6", "webpack:///./src/assets/audio/prompt_tone.mp3"], "names": ["tryToString", "$TypeError", "TypeError", "module", "exports", "O", "P", "render", "_vm", "this", "_c", "_self", "staticClass", "attrs", "isStatus", "ref", "sessionID", "on", "changeSession", "userInfo", "id", "staticRenderFns", "_v", "_s", "nickname", "model", "value", "isTC", "callback", "$$v", "expression", "staticStyle", "$event", "onLine", "offLine", "slot", "avatar", "status", "handleLogout", "name", "inject", "props", "data", "watch", "computed", "methods", "params", "path", "query", "console", "component", "scrollbars", "autoHide", "overflowBehavior", "x", "callbacks", "onScroll", "handlerMoreHistory", "toId", "pagination", "getHistoryMore", "_e", "_l", "historyList", "item", "index", "key", "timeFormat", "from_type", "imageURL", "from_avatar", "msg_type", "msg", "emoji", "class", "<PERSON><PERSON><PERSON><PERSON>", "require", "uploadURL", "token", "$store", "getters", "beforeImageUpload", "sendMessageImage", "isShowReply", "showKefu", "kefuLists", "length", "transfer", "nativeOn", "handleKeydown", "apply", "arguments", "type", "indexOf", "_k", "keyCode", "preventDefault", "onSendMessage", "<PERSON><PERSON><PERSON><PERSON>", "selectReply", "showReply", "$slots", "_t", "default", "visible", "getLists", "keyword", "handleScroll", "replyLists", "stopPropagation", "handleSelect", "title", "content", "total", "page", "$set", "size", "get", "set", "created", "apiReplyList", "page_no", "page_size", "E_Msg", "TEXT", "IMAGE", "GOODS", "E_MsgEvent", "CHAT", "PING", "ERROR", "NOTICE", "USER_ONLINE", "TRANSFER", "E_Load", "LOAD", "NORMAL", "EMPTY", "directives", "rawName", "LoadMap", "$emit", "validator", "Object", "MsgMap", "domProps", "$options", "filters", "textToHtml", "goods", "image", "min_price", "weekArr", "dateTime", "fmt", "Number", "Date", "toString", "ret", "date", "opt", "getFullYear", "getMonth", "getDate", "getHours", "getMinutes", "getSeconds", "k", "RegExp", "exec", "replace", "padStart", "timeFormatChat", "isToday", "isThisWeak", "getDay", "isThisYear", "now", "getYear", "isThis<PERSON><PERSON><PERSON>", "components", "ChatMessage", "QuickReply", "Morebar", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fromId", "more", "scrollbar", "showE<PERSON>ji", "handler", "immediate", "timeFmt", "mounted", "scrollTop", "to_id", "max", "position", "code", "receiveMessage", "getChatHistory", "apiChatHistory", "user_id", "then", "resolve", "catch", "reject", "getKefuLists", "apiServiceList", "kefu_id", "tabsActiveIndex", "handleSearch", "searchValue", "TabsMap", "getUserList", "SessionList", "is_read", "current", "update_time", "online", "changeCurrent", "active", "Boolean", "isRead", "getTime", "getMessage", "message", "time", "badge", "msgType", "load", "disabled", "ChatUserItem", "LoadingMore", "USER_LIST", "GROUP_CHAT", "user", "group", "apiChatUserList", "finally", "transferEvenr", "useronlineEvent", "messageEvenr", "shop_id", "setUser", "sn", "level_name", "mobile", "total_order_amount", "birthday", "client_desc", "create_time", "getUserOrder", "refresh", "orderSn", "orderLists", "order_goods", "gitem", "gindex", "goods_name", "spec_value", "goods_price", "goods_num", "order_type_text", "order_sn", "order_status", "order_status_text", "order_amount", "USER", "ORDER", "KefuLists", "getUserInfo", "apiUserInfo", "apiUserOrder", "src", "play", "Socket", "constructor", "ws", "event", "objectToQuery", "serve", "reconnectLock", "reconnectTimeout", "reconnectNums", "timeout", "clientTimeout", "serverTimeout", "init", "WebSocket", "onopen", "onOpen", "bind", "onerror", "onError", "onmessage", "onMessage", "onclose", "onClose", "log", "start", "open", "e", "error", "reset", "reconnect", "close", "send", "JSON", "stringify", "clearTimeout", "setTimeout", "TheWindowHeader", "TheWindowAsideLeft", "TheWindowAsideRight", "TheWindowMain", "PromptTone", "provide", "sendMessage", "closeChatServe", "reChatServe", "<PERSON><PERSON><PERSON><PERSON>", "to_type", "initChatServe", "client", "_this", "$", "toObject", "lengthOfArrayLike", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deletePropertyOrThrow", "doesNotExceedSafeInteger", "INCORRECT_RESULT", "unshift", "properErrorOnNonWritableLength", "defineProperty", "writable", "FORCED", "target", "proto", "arity", "forced", "len", "argCount", "to", "j"], "mappings": "kHACA,IAAIA,EAAc,EAAQ,QAEtBC,EAAaC,UAEjBC,EAAOC,QAAU,SAAUC,EAAGC,GAC5B,WAAYD,EAAEC,GAAI,MAAML,EAAW,0BAA4BD,EAAYM,GAAK,OAASN,EAAYK,M,2FCNvG,IAAIE,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,MAAM,CAACE,YAAY,UAAU,CAACF,EAAG,SAAS,CAACE,YAAY,iBAAiB,CAACF,EAAG,oBAAoB,CAACG,MAAM,CAAC,SAAWL,EAAIM,aAAa,GAAGJ,EAAG,UAAU,CAACE,YAAY,kBAAkB,CAACF,EAAG,QAAQ,CAACE,YAAY,sBAAsB,CAACF,EAAG,wBAAwB,CAACK,IAAI,OAAOF,MAAM,CAAC,QAAUL,EAAIQ,WAAWC,GAAG,CAAC,OAAST,EAAIU,kBAAkB,GAAGR,EAAG,OAAO,CAACE,YAAY,eAAe,CAACF,EAAG,kBAAkB,CAACK,IAAI,OAAOF,MAAM,CAAC,QAAQL,EAAIQ,UAAU,UAAUR,EAAIW,SAASC,OAAO,GAAGV,EAAG,QAAQ,CAACE,YAAY,uBAAuB,CAACF,EAAG,yBAAyB,CAACG,MAAM,CAAC,QAAQL,EAAIQ,cAAc,OAAON,EAAG,aAAa,CAACK,IAAI,mBAAmB,IAE9tBM,EAAkB,GCFlBd,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACJ,EAAIc,GAAG,WAAWZ,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,MAAM,CAACE,YAAY,sBAAsB,CAACF,EAAG,OAAO,CAACE,YAAY,SAAS,CAACJ,EAAIc,GAAGd,EAAIe,GAAGf,EAAIW,SAASK,aAAad,EAAG,aAAa,CAACE,YAAY,cAAcC,MAAM,CAAC,UAAY,QAAQ,MAAQ,GAAG,MAAQ,KAAK,QAAU,SAAS,eAAe,WAAWY,MAAM,CAACC,MAAOlB,EAAImB,KAAMC,SAAS,SAAUC,GAAMrB,EAAImB,KAAKE,GAAKC,WAAW,SAAS,CAACpB,EAAG,MAAM,GAAG,CAACA,EAAG,MAAM,CAACE,YAAY,mBAAmBmB,YAAY,CAAC,OAAS,WAAWd,GAAG,CAAC,MAAQ,SAASe,GAAQ,OAAOxB,EAAIyB,QAAO,MAAS,CAACvB,EAAG,OAAO,CAACE,YAAY,SAASmB,YAAY,CAAC,QAAU,QAAQ,QAAU,MAAM,gBAAgB,MAAM,OAAS,OAAO,MAAQ,OAAO,mBAAmB,aAAavB,EAAIc,GAAG,UAAUZ,EAAG,MAAM,CAACE,YAAY,mBAAmBmB,YAAY,CAAC,OAAS,WAAWd,GAAG,CAAC,MAAQ,SAASe,GAAQ,OAAOxB,EAAI0B,SAAQ,MAAU,CAACxB,EAAG,OAAO,CAACE,YAAY,SAASmB,YAAY,CAAC,QAAU,QAAQ,QAAU,MAAM,gBAAgB,MAAM,OAAS,OAAO,MAAQ,OAAO,mBAAmB,aAAavB,EAAIc,GAAG,YAAYZ,EAAG,MAAM,CAACqB,YAAY,CAAC,SAAW,YAAYlB,MAAM,CAAC,KAAO,aAAaI,GAAG,CAAC,MAAQ,SAASe,GAAQxB,EAAImB,MAAQnB,EAAImB,OAAOQ,KAAK,aAAa,CAACzB,EAAG,YAAY,CAACG,MAAM,CAAC,KAAO,eAAe,IAAML,EAAIW,SAASiB,UAAyB,GAAd5B,EAAI6B,OAAiB3B,EAAG,MAAM,CAACqB,YAAY,CAAC,SAAW,WAAW,IAAM,OAAO,KAAO,OAAO,OAAS,OAAO,MAAQ,OAAO,gBAAgB,MAAM,mBAAmB,aAAarB,EAAG,MAAM,CAACqB,YAAY,CAAC,SAAW,WAAW,IAAM,OAAO,KAAO,OAAO,OAAS,OAAO,MAAQ,OAAO,gBAAgB,MAAM,mBAAmB,cAAc,MAAM,GAAGrB,EAAG,gBAAgB,CAACG,MAAM,CAAC,MAAQ,UAAUI,GAAG,CAAC,QAAUT,EAAI8B,eAAe,CAAC5B,EAAG,MAAM,CAACE,YAAY,cAAcC,MAAM,CAAC,KAAO,aAAasB,KAAK,aAAa,CAACzB,EAAG,IAAI,CAACE,YAAY,kBAAkBF,EAAG,OAAO,CAACE,YAAY,SAAS,CAACJ,EAAIc,GAAG,aAAa,MAE/8DD,EAAkB,G,wBC6GP,GACfkB,uBAEAC,iDAEAC,OACA3B,kBAEA4B,OACA,OACAL,UACAV,UAIAgB,OACA7B,YACA,gBAIA8B,aACA,8BAGAC,YACA,2BACAP,eACA,WACA,2BACAQ,UAEA,uBACA,mBACAC,cACAC,aAIAf,WACA,iBACAgB,2BACA,oBAGA,cACA,cAGAf,WACA,aACA,sBAEA,cACA,aACAe,kBCtKuX,I,wBCQnXC,EAAY,eACd,EACA,EACA,GACA,EACA,KACA,WACA,MAIa,EAAAA,E,QCnBX3C,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,qBAAqB,CAACK,IAAI,YAAYH,YAAY,kBAAkBC,MAAM,CAAC,QAAU,CACtLsC,WAAY,CAAEC,SAAU,UACxBC,iBAAkB,CAAEC,EAAG,UACvBC,UAAW,CAAEC,SAAUhD,EAAIiD,uBAC3B,CAAEjD,EAAIkD,KAAMhD,EAAG,UAAU,CAACG,MAAM,CAAC,OAASL,EAAImD,WAAWtB,QAAQpB,GAAG,CAAC,OAAST,EAAIoD,kBAAkBpD,EAAIqD,KAAKrD,EAAIsD,GAAItD,EAAIuD,aAAa,SAASC,EAAKC,GAAO,OAAOvD,EAAG,MAAM,CAACwD,IAAIF,EAAK5C,GAAGP,MAAM,CAAC,GAAKmD,EAAK5C,KAAK,CAAEZ,EAAI2D,WAAWH,EAAMC,GAAQvD,EAAG,MAAM,CAACE,YAAY,4BAA4B,CAACJ,EAAIc,GAAGd,EAAIe,GAAGf,EAAI2D,WAAWH,EAAMC,OAAWzD,EAAIqD,KAAyB,SAAnBG,EAAKI,UAAsB1D,EAAG,MAAM,CAACE,YAAY,gCAAgC,CAACF,EAAG,eAAe,CAACG,MAAM,CAAC,OAASL,EAAI6D,SAAWL,EAAKM,cAAc,CAAC5D,EAAG,eAAe,CAACG,MAAM,CAAC,KAAO,MAAM,KAAOmD,EAAKO,SAAS,QAAUP,EAAKQ,IAAI,SAAWhE,EAAI6D,UAAUlC,KAAK,SAAS,IAAI,GAAGzB,EAAG,MAAM,CAACE,YAAY,+BAA+B,CAACF,EAAG,eAAe,CAACG,MAAM,CAAC,OAASL,EAAI6D,SAAWL,EAAKM,cAAc,CAAC5D,EAAG,eAAe,CAACG,MAAM,CAAC,KAAO,KAAK,KAAOmD,EAAKO,SAAS,QAAUP,EAAKQ,IAAI,SAAWhE,EAAI6D,UAAUlC,KAAK,QAAQ,IAAI,QAAS3B,EAAIkD,KAAuFlD,EAAIqD,KAArF,CAACnD,EAAG,WAAW,CAACqB,YAAY,CAAC,OAAS,QAAQlB,MAAM,CAAC,YAAc,eAAwB,GAAGH,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,aAAa,CAACG,MAAM,CAAC,UAAY,MAAM,MAAQ,GAAG,MAAQ,MAAM,QAAU,QAAQ,UAAYL,EAAIkD,OAAO,CAAChD,EAAG,MAAM,CAACE,YAAY,iBAAiBmB,YAAY,CAAC,IAAM,QAAQvB,EAAIsD,GAAItD,EAAIiE,OAAO,SAAST,EAAKC,GAAO,OAAOvD,EAAG,OAAO,CAACwD,IAAID,EAAMS,MAAO,MAAKV,KAAQjC,YAAY,CAAC,YAAY,QAAQd,GAAG,CAAC,MAAQ,SAASe,GAAQ,OAAOxB,EAAImE,UAAUX,UAAY,GAAGtD,EAAG,aAAa,CAACE,YAAY,OAAOC,MAAM,CAAC,KAAO,YAAY,OAAS,OAAO,QAAU,KAAK,UAAY,UAAUsB,KAAK,aAAa,CAACzB,EAAG,MAAM,CAACE,YAAY,OAAOmB,YAAY,CAAC,aAAa,SAAS,CAACrB,EAAG,MAAM,CAACqB,YAAY,CAAC,OAAS,OAAO,MAAQ,OAAO,OAAS,WAAWlB,MAAM,CAAC,IAAM+D,EAAQ,gBAAwC,GAAGlE,EAAG,aAAa,CAACE,YAAY,OAAOC,MAAM,CAAC,KAAO,YAAY,OAAS,OAAO,QAAU,KAAK,UAAY,UAAUsB,KAAK,aAAa,CAACzB,EAAG,YAAY,CAACG,MAAM,CAAC,OAASL,EAAIqE,UAAU,OAAS,iCAAiC,QAAU,CAAEC,MAAOtE,EAAIuE,OAAOC,QAAQF,OAAQ,kBAAiB,EAAM,gBAAgBtE,EAAIyE,kBAAkB,aAAazE,EAAI0E,iBAAiB,UAAY1E,EAAIkD,OAAO,CAAChD,EAAG,IAAI,CAACE,YAAY,iDAAiD,GAAGF,EAAG,aAAa,CAACE,YAAY,OAAOC,MAAM,CAAC,KAAO,YAAY,OAAS,OAAO,QAAU,OAAO,UAAY,UAAUsB,KAAK,aAAa,CAACzB,EAAG,IAAI,CAACE,YAAY,4CAA4CK,GAAG,CAAC,MAAQT,EAAI2E,iBAAiBzE,EAAG,aAAa,CAACG,MAAM,CAAC,UAAY,MAAM,MAAQ,GAAG,MAAQ,MAAM,QAAU,SAAS,UAAYL,EAAIkD,MAAMjC,MAAM,CAACC,MAAOlB,EAAI4E,SAAUxD,SAAS,SAAUC,GAAMrB,EAAI4E,SAASvD,GAAKC,WAAW,aAAa,CAACpB,EAAG,MAAM,CAACA,EAAG,eAAe,CAACE,YAAY,eAAemB,YAAY,CAAC,OAAS,UAAU,CAAEvB,EAAI6E,UAAUC,OAAQ5E,EAAG,MAAM,CAACE,YAAY,aAAaJ,EAAIsD,GAAItD,EAAI6E,WAAW,SAASrB,EAAKC,GAAO,OAAOvD,EAAG,MAAM,CAACwD,IAAID,EAAMrD,YAAY,wBAAwBmB,YAAY,CAAC,OAAS,WAAWd,GAAG,CAAC,MAAQ,SAASe,GAAQ,OAAOxB,EAAI+E,SAASvB,MAAS,CAACtD,EAAG,MAAM,CAACqB,YAAY,CAAC,MAAQ,OAAO,OAAS,OAAO,gBAAgB,OAAOlB,MAAM,CAAC,IAAMmD,EAAK5B,OAAO,IAAM,MAAM1B,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAACJ,EAAIc,GAAGd,EAAIe,GAAGyC,EAAKxC,kBAAiB,GAAGd,EAAG,MAAM,CAACE,YAAY,YAAY,CAACJ,EAAIc,GAAG,gBAAgB,GAAGZ,EAAG,aAAa,CAACE,YAAY,OAAOC,MAAM,CAAC,KAAO,YAAY,OAAS,OAAO,QAAU,KAAK,UAAY,UAAUsB,KAAK,aAAa,CAACzB,EAAG,IAAI,CAACE,YAAY,8BAA8BK,GAAG,CAAC,MAAQ,SAASe,GAAQxB,EAAI4E,UAAY5E,EAAI4E,gBAAgB,IAAI,GAAG1E,EAAG,WAAW,CAACE,YAAY,mBAAmBC,MAAM,CAAC,KAAO,WAAW,YAAc,SAAS2E,SAAS,CAAC,QAAU,CAAC,SAASxD,GAAQ,OAAOxB,EAAIiF,cAAcC,MAAM,KAAMC,YAAY,SAAS3D,GAAQ,IAAIA,EAAO4D,KAAKC,QAAQ,QAAQrF,EAAIsF,GAAG9D,EAAO+D,QAAQ,QAAQ,GAAG/D,EAAOkC,IAAI,SAAS,OAAO,KAAKlC,EAAOgE,mBAAoB,MAAQ,SAAShE,GAAQ,OAAIA,EAAO4D,KAAKC,QAAQ,QAAQrF,EAAIsF,GAAG9D,EAAO+D,QAAQ,QAAQ,GAAG/D,EAAOkC,IAAI,SAAgB,KAAY1D,EAAIyF,cAAcP,MAAM,KAAMC,aAAalE,MAAM,CAACC,MAAOlB,EAAI0F,cAAetE,SAAS,SAAUC,GAAMrB,EAAI0F,cAAcrE,GAAKC,WAAW,mBAAmBpB,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,YAAY,CAACG,MAAM,CAAC,KAAO,UAAU,KAAO,QAAQ,UAAYL,EAAI0F,gBAAkB1F,EAAIkD,KAAK,SAAU,GAAOzC,GAAG,CAAC,MAAQT,EAAIyF,gBAAgB,CAACzF,EAAIc,GAAG,SAAS,IAAI,GAAGZ,EAAG,cAAc,CAACO,GAAG,CAAC,OAAST,EAAI2F,aAAa1E,MAAM,CAACC,MAAOlB,EAAI4F,UAAWxE,SAAS,SAAUC,GAAMrB,EAAI4F,UAAUvE,GAAKC,WAAW,gBAAgB,IAEr4IT,EAAkB,GCNlBd,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAAEJ,EAAI6F,OAAO,MAAO3F,EAAG,MAAM,CAACE,YAAY,uCAAuC,CAACJ,EAAI8F,GAAG,OAAO,GAAG9F,EAAIqD,KAAKnD,EAAG,YAAY,CAACE,YAAY,iBAAiBC,MAAM,CAAC,KAAO,GAAG,IAAML,EAAI4B,UAAW5B,EAAI6F,OAAO,OAAQ3F,EAAG,MAAM,CAACE,YAAY,wCAAwC,CAACJ,EAAI8F,GAAG,QAAQ,GAAG9F,EAAIqD,MAAM,IAElZxC,EAAkB,GCiBP,GACfkB,mBAEAE,OACAL,QACAwD,YACAW,cCzB8V,ICQ1V,G,UAAY,eACd,EACA,EACA,GACA,EACA,KACA,WACA,OAIa,I,QCnBXhG,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,YAAY,CAACG,MAAM,CAAC,MAAQ,OAAO,QAAUL,EAAIgG,QAAQ,MAAQ,QAAQ,IAAM,QAAQvF,GAAG,CAAC,iBAAiB,SAASe,GAAQxB,EAAIgG,QAAQxE,KAAU,CAACtB,EAAG,WAAW,CAACG,MAAM,CAAC,KAAO,QAAQ,YAAc,eAAe,cAAc,kBAAkB2E,SAAS,CAAC,MAAQ,SAASxD,GAAQ,OAAIA,EAAO4D,KAAKC,QAAQ,QAAQrF,EAAIsF,GAAG9D,EAAO+D,QAAQ,QAAQ,GAAG/D,EAAOkC,IAAI,SAAgB,KAAY1D,EAAIiG,SAASf,MAAM,KAAMC,aAAalE,MAAM,CAACC,MAAOlB,EAAIkG,QAAS9E,SAAS,SAAUC,GAAMrB,EAAIkG,QAAQ7E,GAAKC,WAAW,aAAapB,EAAG,qBAAqB,CAACE,YAAY,qBAAqBC,MAAM,CAAC,QAAU,CACvqBsC,WAAY,CAAEC,SAAU,UACxBC,iBAAkB,CAAEC,EAAG,UACvBC,UAAW,CAAEC,SAAUhD,EAAImG,iBAC3B,CAAEnG,EAAIoG,WAAWtB,OAAQ5E,EAAG,MAAM,CAACE,YAAY,eAAeJ,EAAIsD,GAAItD,EAAIoG,YAAY,SAAS5C,EAAKC,GAAO,OAAOvD,EAAG,MAAM,CAACwD,IAAID,EAAMrD,YAAY,aAAaK,GAAG,CAAC,MAAQ,SAASe,GAAiC,OAAzBA,EAAO6E,kBAAyBrG,EAAIsG,aAAa9C,MAAS,CAACtD,EAAG,MAAM,CAACE,YAAY,gCAAgC,CAACJ,EAAIc,GAAG,IAAId,EAAIe,GAAGyC,EAAK+C,OAAO,OAAOrG,EAAG,MAAM,CAACE,YAAY,mCAAmC,CAACJ,EAAIc,GAAG,IAAId,EAAIe,GAAGyC,EAAKgD,SAAS,YAAW,GAAGtG,EAAG,WAAW,CAACG,MAAM,CAAC,aAAa,QAAQ,GAAyB,EAArBL,EAAImD,WAAWsD,MAASvG,EAAG,gBAAgB,CAACqB,YAAY,CAAC,aAAa,UAAUlB,MAAM,CAAC,OAAS,oBAAoB,MAAQL,EAAImD,WAAWsD,MAAM,eAAezG,EAAImD,WAAWuD,MAAMjG,GAAG,CAAC,qBAAqB,SAASe,GAAQ,OAAOxB,EAAI2G,KAAK3G,EAAImD,WAAY,OAAQ3B,IAAS,sBAAsB,SAASA,GAAQ,OAAOxB,EAAI2G,KAAK3G,EAAImD,WAAY,OAAQ3B,IAAS,iBAAiB,SAASA,GAAQ,OAAOxB,EAAIiG,eAAejG,EAAIqD,MAAM,IAAI,IAE56BxC,EAAkB,G,YCoDP,GACfkB,kBAEAE,OACAf,OACAkE,aACAW,aAIA7D,OACA,OACAkE,cACAF,WAGA/C,YACAyD,QACAF,OACAD,WAIArE,UACA4D,SACAa,MACA,mBAEAC,OACA,yBAIA3E,OACA6D,WACA,qBAGAe,YAEA1E,SACA8D,iBAEAF,WACA,SAAAW,OAAAF,QAAAD,GAAA,gBACAO,gBACAd,qBACAe,UACAC,cACA,SAEA,0BAGAZ,gBACA,+BACA,mBClHkX,ICQ9W,G,UAAY,eACd,EACA,EACA,GACA,EACA,KACA,WACA,OAIa,I,QCbR,MAAMa,EAAQ,CACjBC,KAAM,EACNC,MAAO,EACVC,MAAO,GAIKC,EAAa,CACtBC,KAAM,OACNC,KAAM,OACNC,MAAO,QACPC,OAAQ,SACRC,YAAa,cACbC,SAAU,YAKDC,EAAS,CAClBC,KAAM,EACNC,OAAQ,EACRN,MAAO,EACPO,MAAO,GC5BX,IAAIlI,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,WAAW,CAACF,EAAG,MAAM,CAACgI,WAAW,CAAC,CAACnG,KAAK,OAAOoG,QAAQ,SAASjH,MAAOlB,EAAIoI,QAAQ,YAAcpI,EAAI6B,OAAQP,WAAW,iCAAiCb,GAAG,CAAC,MAAQ,SAASe,GAAQ,OAAOxB,EAAIqI,MAAM,aAAa,CAACrI,EAAI8F,GAAG,UAAS,WAAW,MAAO,CAAC5F,EAAG,IAAI,CAACE,YAAY,kBAAkB,CAACJ,EAAIc,GAAG,iBAAgB,GAAGZ,EAAG,MAAM,CAACgI,WAAW,CAAC,CAACnG,KAAK,OAAOoG,QAAQ,SAASjH,MAAOlB,EAAIoI,QAAQ,UAAYpI,EAAI6B,OAAQP,WAAW,+BAA+Bb,GAAG,CAAC,MAAQ,SAASe,GAAQ,OAAOxB,EAAIqI,MAAM,aAAa,CAACrI,EAAI8F,GAAG,QAAO,WAAW,MAAO,CAAC5F,EAAG,IAAI,CAACE,YAAY,sCAAqC,GAAGF,EAAG,MAAM,CAACgI,WAAW,CAAC,CAACnG,KAAK,OAAOoG,QAAQ,SAASjH,MAAOlB,EAAIoI,QAAQ,WAAapI,EAAI6B,OAAQP,WAAW,gCAAgCb,GAAG,CAAC,MAAQ,SAASe,GAAQ,OAAOxB,EAAIqI,MAAM,cAAc,CAACrI,EAAI8F,GAAG,SAAQ,WAAW,MAAO,CAAC5F,EAAG,IAAI,CAACE,YAAY,iBAAiB,CAACJ,EAAIc,GAAG,iBAAgB,GAAGZ,EAAG,MAAM,CAACgI,WAAW,CAAC,CAACnG,KAAK,OAAOoG,QAAQ,SAASjH,MAAOlB,EAAIoI,QAAQ,WAAapI,EAAI6B,OAAQP,WAAW,gCAAgCb,GAAG,CAAC,MAAQ,SAASe,GAAQ,OAAOxB,EAAIqI,MAAM,cAAc,CAACrI,EAAI8F,GAAG,SAAQ,WAAW,MAAO,CAAC5F,EAAG,IAAI,CAACE,YAAY,iBAAiB,CAACJ,EAAIc,GAAG,yBAAwB,MAEzwCD,EAAkB,GCqCP,GACfkB,eAEAE,OACAJ,QACAuD,qBACAhB,WAEAkE,cACA,SAIA,OAHAC,2BACA,mBAEA,KAKArG,OACA,OACAkG,0BAAA,OC3D8V,ICQ1V,G,UAAY,eACd,EACA,EACA,GACA,EACA,KACA,WACA,OAIa,I,QCnBXrI,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAAEJ,EAAIwI,OAAO,UAAYxI,EAAIoF,KAAM,CAAClF,EAAG,MAAM,CAACuI,SAAS,CAAC,UAAYzI,EAAIe,GAAGf,EAAI0I,SAASC,QAAQC,WAAW5I,EAAIwG,cAAexG,EAAIwI,OAAO,WAAaxI,EAAIoF,KAAM,CAAClF,EAAG,WAAW,CAACG,MAAM,CAAC,IAAML,EAAI6D,SAAS7D,EAAIwG,YAAaxG,EAAIwI,OAAO,WAAaxI,EAAIoF,KAAM,CAAClF,EAAG,MAAM,CAACE,YAAY,sBAAsB,CAACF,EAAG,MAAM,CAACE,YAAY,sBAAsB,CAACF,EAAG,WAAW,CAACqB,YAAY,CAAC,MAAQ,OAAO,OAAS,QAAQlB,MAAM,CAAC,IAAML,EAAI6D,SAAW7D,EAAI6I,MAAMC,UAAU,GAAG5I,EAAG,MAAM,GAAG,CAACA,EAAG,MAAM,CAACE,YAAY,wBAAwB,CAACJ,EAAIc,GAAG,IAAId,EAAIe,GAAGf,EAAI6I,MAAM9G,MAAM,OAAO7B,EAAG,MAAM,CAACE,YAAY,yBAAyB,CAACJ,EAAIc,GAAG,KAAKd,EAAIe,GAAGf,EAAI6I,MAAME,WAAW,YAAY/I,EAAIqD,MAAM,IAE/vBxC,EAAkB,GCkCP,GACfkB,mBAEAE,OACAmD,MACAA,qBACAhB,YAGAoC,SACApB,qBACAhB,YAGAP,UACAuB,cAIAlD,OACA,OACAsG,yBAAA,MAIAG,SACAC,cAGA,OAFAnG,eAEA,kEAIAL,UACAyG,QACA,mCCvE8V,ICQ1V,G,UAAY,eACd,EACA,EACA,GACA,EACA,KACA,WACA,OAIa,I,oBCnBA,GACd,WACA,cACA,WACA,YACA,aACA,WACA,gBACA,mBACA,yBACA,aACA,cACA,eACA,UACA,UACA,kCACA,kCACA,cACA,aACA,0BACA,sBACA,cACA,aACA,cACA,eACA,gBACA,eACA,cACA,YACA,oBACA,cACA,iBACA,WACA,2BACA,WACA,aACA,kBACA,gBACA,aACA,gBACA,eACA,SACA,SACA,SACA,gBACA,YACA,gBACA,WACA,UACA,aACA,YACA,SACA,UACA,gBACA,gBACA,SACA,iBACA,kBACA,cACA,cACA,WACA,WACA,kBACA,YACA,cACA,gBACA,aACA,eACA,UACA,OACA,cACA,gBACA,gBACA,iBACA,W,YC1ED,MAAMG,EAAU,CAAC,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,OAS9CrF,EAAa,CAACsF,EAAUC,EAAM,gBAErCD,IAAUA,EAAWE,OAAO,IAAIC,OAEH,IAA9BH,EAASI,WAAWvE,SAAcmE,GAAY,KAClD,IACIK,EADAC,EAAO,IAAIH,KAAKH,GAEhBO,EAAM,CACT,KAAMD,EAAKE,cAAcJ,WACzB,MAAOE,EAAKG,WAAa,GAAGL,WAC5B,KAAME,EAAKI,UAAUN,WACrB,KAAME,EAAKK,WAAWP,WACtB,KAAME,EAAKM,aAAaR,WACxB,KAAME,EAAKO,aAAaT,YAEzB,IAAK,IAAIU,KAAKP,EACbF,EAAM,IAAIU,OAAO,IAAMD,EAAI,KAAKE,KAAKf,GACjCI,IACHJ,EAAMA,EAAIgB,QAAQZ,EAAI,GAAqB,GAAjBA,EAAI,GAAGxE,OAAc0E,EAAIO,GAAKP,EAAIO,GAAGI,SAASb,EAAI,GAAGxE,OAAQ,OAGzF,OAAOoE,GASKkB,EAAkBnB,IACI,IAA9BA,EAASI,WAAWvE,SAAcmE,GAAY,KAClD,IAAIM,EAAO,IAAIH,KAAKH,GAChBC,EAAMvF,EAAWsF,EAAU,qBAS/B,OAPIoB,EAAQd,GACXL,EAAMvF,EAAWsF,EAAU,SACjBqB,EAAWf,GACrBL,EAAMF,EAAQO,EAAKgB,UAAY5G,EAAWsF,EAAU,UAC1CuB,EAAWjB,KACrBL,EAAMvF,EAAWsF,EAAU,iBAErBC,GAOFsB,EAAcjB,IACnB,MAAMkB,EAAM,IAAIrB,KAChB,OAAOG,EAAKmB,WAAaD,EAAIC,WAKxBC,EAAepB,IACpB,MAAMkB,EAAM,IAAIrB,KAChB,OAAOoB,EAAWjB,IAASA,EAAKG,YAAce,EAAIf,YAM7CW,EAAWd,IAChB,MAAMkB,EAAM,IAAIrB,KAChB,OAAOuB,EAAYpB,IAASA,EAAKI,WAAac,EAAId,WAI7CW,EAAcf,IACnB,MAAMkB,EAAM,IAAIrB,KAChB,QAAIuB,EAAYpB,KACXkB,EAAIF,SAAWhB,EAAKgB,SAAW,GAAKE,EAAId,UAAYJ,EAAKI,UAAY,QAAzE,ICiHa,OACf5H,qBAEA6I,YACAC,cACAC,aACAC,UACAC,eAGA/I,OACAiB,MACAkC,qBACAW,YAGAkF,QACA7F,qBACAW,aAIA/D,8BAEAE,OACA,OAEAwD,iBAEAnC,eAEAqC,aAEAzC,YACAyD,QACAF,OACA7E,mBACA4E,QACAyE,SAGAC,eACA9G,mDAEAQ,aACAD,YAEAwG,aACAnH,SAEAhB,0BAIAd,OACAe,MACAmI,WACA,uBAEAC,cAGA1G,YACA,yBAGAxC,UACAuB,aACA,cACA,6BAWA,OATAF,GACAD,oBACA,wCACA,MACA,cAEA+H,MAGA,IAGA1H,WACA,qCAGAkD,UACA,wCAEA,aAEA,kEAGAyE,UACA,qDAGAnJ,SAGA,yBACA,gBAAAoJ,GAAA,SACA,SACA,oDACA,mCACA,uBACA,8DACA,+BACA,yBAGAhJ,4BAIAkC,cACA,YACA,oBAKAR,aACA,6BACA1B,gBAEAwC,iBACA,4BACA,6BAMAQ,iBACA,qBACA,oDACA,kBACAzB,uBACAD,mBACA2H,kBAEA,sBAEA,UAAAC,WAAAC,GAAA,wBACA,aACA,+BAQAlH,uBAAAmH,OAAA3J,MAAA8B,IACA,uCAEA,kBACAA,eACAD,oBACA2H,mBAOAI,kBACArJ,eACA,0CACA,yBAEA,UAAAkJ,WAAAC,GAAA,wBACA,aACA,oBACA,iCAQAG,kBACA,KACA,2BACA,WAAAnF,OAAAF,OAAAwE,GAAA,gBACA,IAEA,iCACAc,gBACAC,UACAhF,UACAC,cAEAgF,SACA,iDACA,8BACA,kCACA,gCACApE,YACAA,WACAqE,OAEAC,UACA,kCACAC,WAQA,uBACA,wDACA,iCACA,6BACA,+BACA,0BAIA1G,eACA,sBAMA,uBACA,wBACA,uBACA,wBACA,wBACA,mCACA,oBACA/E,gCAEA,oBACA,8BACA6B,wCAOAgC,qBAGA,2BAKA,OAHA,GACA,uCAEA,GAGA6H,eACAC,0BACA,oBAIAxH,YACA,sBACAkH,kBACAO,eAEA,oBCndqX,KCQjX,I,UAAY,eACd,GACA,EACA,GACA,EACA,KACA,WACA,OAIa,M,QCnBXzM,GAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,UAAU,CAACE,YAAY,qBAAqBC,MAAM,CAAC,SAAU,GAAMY,MAAM,CAACC,MAAOlB,EAAIyM,gBAAiBrL,SAAS,SAAUC,GAAMrB,EAAIyM,gBAAgBpL,GAAKC,WAAW,oBAAoB,CAACpB,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,WAAW,CAACG,MAAM,CAAC,KAAO,QAAQ,YAAc,YAAY,WAAY,GAAM2E,SAAS,CAAC,MAAQ,SAASxD,GAAQ,OAAIA,EAAO4D,KAAKC,QAAQ,QAAQrF,EAAIsF,GAAG9D,EAAO+D,QAAQ,QAAQ,GAAG/D,EAAOkC,IAAI,SAAgB,KAAY1D,EAAI0M,aAAaxH,MAAM,KAAMC,aAAalE,MAAM,CAACC,MAAOlB,EAAI2M,YAAavL,SAAS,SAAUC,GAAMrB,EAAI2M,YAAYtL,GAAKC,WAAW,kBAAkB,GAAGpB,EAAG,cAAc,CAACE,YAAY,kBAAkBC,MAAM,CAAC,MAAQ,OAAO,KAAOL,EAAI4M,QAAQ,eAAe,CAAC1M,EAAG,eAAe,CAACE,YAAY,kCAAkC,CAACF,EAAG,eAAe,CAACO,GAAG,CAAC,KAAOT,EAAI6M,aAAa5L,MAAM,CAACC,MAAOlB,EAAImD,WAAWtB,OAAQT,SAAS,SAAUC,GAAMrB,EAAI2G,KAAK3G,EAAImD,WAAY,SAAU9B,IAAMC,WAAW,sBAAsBtB,EAAIsD,GAAItD,EAAI8M,YAAY,SAAS,SAAStJ,EAAKC,GAAO,OAAOvD,EAAG,iBAAiB,CAACwD,IAAIF,EAAK5C,GAAGP,MAAM,CAAC,UAAUmD,EAAKuJ,QAAQ,KAAOvJ,EAAKxC,SAAS,OAASwC,EAAK5B,OAAO,QAAU4B,EAAKQ,IAAI,OAAShE,EAAIgN,UAAYxJ,EAAKyI,QAAQ,KAAOzI,EAAKyJ,YAAY,OAASzJ,EAAK0J,OAAO,WAAW1J,EAAKO,UAAUiB,SAAS,CAAC,MAAQ,SAASxD,GAAQ,OAAOxB,EAAImN,cAAc3J,EAAMC,UAAa,IAAI,IAAI,GAAGvD,EAAG,cAAc,CAACG,MAAM,CAAC,MAAQ,GAAG,KAAOL,EAAI4M,QAAQ,cAAc,SAAW,OAAO,IAE58C/L,GAAkB,GCFlBd,I,UAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACgE,MAAM,CAAC,iBAAkB,CAAE,yBAA0BlE,EAAIoN,UAAW,CAAClN,EAAG,MAAM,CAACE,YAAY,eAAe8D,MAAM,CAAE,uBAAwBlE,EAAIkN,SAAU,CAAChN,EAAG,WAAW,CAACG,MAAM,CAAC,SAAS,GAAG,OAASgN,QAAQrN,EAAIsN,UAAU,CAACpN,EAAG,YAAY,CAACG,MAAM,CAAC,KAAO,GAAG,IAAML,EAAI6D,SAAW7D,EAAI4B,WAAW,IAAI,GAAG1B,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,MAAM,CAACE,YAAY,8BAA8B,CAACJ,EAAIc,GAAGd,EAAIe,GAAGf,EAAI+B,SAAS7B,EAAG,MAAM,CAACE,YAAY,2BAA2B,CAACJ,EAAIc,GAAGd,EAAIe,GAAGf,EAAIuN,cAAcrN,EAAG,MAAM,CAACE,YAAY,wBAAwB,CAACF,EAAG,MAAM,CAACE,YAAY,kCAAkCqI,SAAS,CAAC,UAAYzI,EAAIe,GAAGf,EAAIwN,uBAEvuB3M,GAAkB,GCyCP,IACfkB,oBAEAE,OACAL,QACAwD,YACAW,YAGAhE,MACAqD,YACAW,YAGA0H,SACArI,YACAW,YAGA2H,MACAtI,YACAW,YAGA4H,OACAvI,YACAW,WAGAqH,QACAhI,aACAW,YAEAmH,QACA9H,aAEAkI,QACAlI,uBAEAwI,SACAxI,cAGAhD,UACAmL,UACA,sDAEAC,aACA,qBACA,yFACA,kBACA,oBAGA3J,WACA,sCClG8V,MCQ1V,I,UAAY,eACd,GACA,GACA,IACA,EACA,KACA,WACA,OAIa,M,QCnBX9D,GAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACgI,WAAW,CAAC,CAACnG,KAAK,kBAAkBoG,QAAQ,oBAAoBjH,MAAOlB,EAAI6N,KAAMvM,WAAW,SAASlB,YAAY,eAAeC,MAAM,CAAC,2BAA2B,KAAK,2BAA2BL,EAAI8N,WAAW,CAAC9N,EAAI8F,GAAG,WAAW5F,EAAG,UAAU,CAACG,MAAM,CAAC,OAASL,EAAI6B,WAAW,IAE1VhB,GAAkB,GCgBP,IAEfoB,OACAf,OACAkE,uBAGAwF,YACAG,WAEA7I,OACA,UAIAE,UACA0L,WACA,wDAEAjM,QACAgF,MACA,mBAEAC,OACA,yBAIAzE,SACAwL,OACA,sBACA,sBCjD8V,MCO1V,GAAY,eACd,GACA,GACA,IACA,EACA,KACA,WACA,MAIa,M,QCwCA,IACf9L,0BAEA6I,YACAmD,gBACAC,gBAGA/L,OACA+K,SACA5H,qBACAhB,aAGApC,gBACAE,OACA,OAEAyK,eAEAF,mBAEAG,uBACAqB,cACAC,iBAGA/K,YACAyD,QACAF,OACA7E,oBAIAiL,aACAqB,QACAC,YAQArH,UAEA,+CACA,4CACA,wCACA,uCAIA3E,aACA,4BAEAC,SAIAwK,cACA,2BACA,WAAAjG,OAAAF,SAAA7E,GAAA,gBACA,eACAwM,gBACApH,UACAC,YACAlG,mCAEAkL,SACA,IACA,uBAEAhK,mBACA,gEACA,MACA,gCAGA,mCACA,oBACA,SACA,qCAGAiK,QAEAC,UAEA,mCACAC,OAEAiC,mBAMA5B,eACA,uBACA,iCACA,yBACA,oBAKA6B,iBACA,2BAMA,OALA,0BACA,sBACAhI,aACAkH,mBAKA,sDACA,yBAEA,wBACA,uBACAA,kBAMAe,mBACA,UACA,sBACAjI,aACAkH,gCAGA,iBAKAgB,gBACA,yCACA,oBACA,qBACA,oBACA,wBADA,GAeA,IAXA,OAEA,0DACA,gDACA,UACA,yBACA,cACAvM,gBAIA,qBACA,2BAKA,YAJA,kBACA+J,qBACAyC,sBAIA,kDAMAvB,mBACA,+BACA,YACA,kBACAlB,kBACAyC,sBAGA,kDAIAC,WACA,sCACAnL,0BAEA,KACA,sDAEA,oCC1P0X,MCQtX,I,UAAY,eACd,GACA,GACA,IACA,EACA,KACA,WACA,OAIa,M,QCnBXzD,GAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,uBAAuB,CAACF,EAAG,UAAU,CAACG,MAAM,CAAC,SAAU,GAAMY,MAAM,CAACC,MAAOlB,EAAIyM,gBAAiBrL,SAAS,SAAUC,GAAMrB,EAAIyM,gBAAgBpL,GAAKC,WAAW,oBAAoB,CAACpB,EAAG,cAAc,CAACG,MAAM,CAAC,MAAQ,OAAO,KAAOL,EAAI4M,QAAQ,UAAU,CAAC1M,EAAG,MAAM,CAACE,YAAY,eAAe,CAAEJ,EAAIW,SAASC,GAAIV,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,oBAAoB,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgBC,MAAM,CAAC,IAAML,EAAIW,SAASiB,UAAU1B,EAAG,MAAM,CAACE,YAAY,eAAe,CAACJ,EAAIc,GAAG,IAAId,EAAIe,GAAGf,EAAIW,SAASK,UAAU,SAASd,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,OAAO,CAACE,YAAY,SAAS,CAACJ,EAAIc,GAAG,WAAWZ,EAAG,OAAO,CAACF,EAAIc,GAAG,IAAId,EAAIe,GAAGf,EAAIW,SAASiO,SAAS1O,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,OAAO,CAACE,YAAY,SAAS,CAACJ,EAAIc,GAAG,WAAWZ,EAAG,OAAO,CAACF,EAAIc,GAAGd,EAAIe,GAAGf,EAAIW,SAASkO,iBAAiB3O,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,OAAO,CAACE,YAAY,SAAS,CAACJ,EAAIc,GAAG,WAAWZ,EAAG,OAAO,CAACF,EAAIc,GAAGd,EAAIe,GAAGf,EAAIW,SAASmO,aAAa5O,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,OAAO,CAACE,YAAY,SAAS,CAACJ,EAAIc,GAAG,WAAWZ,EAAG,OAAO,CAACF,EAAIc,GAAGd,EAAIe,GAAGf,EAAIW,SAASoO,yBAAyB7O,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,OAAO,CAACE,YAAY,SAAS,CAACJ,EAAIc,GAAG,SAASZ,EAAG,OAAO,CAACF,EAAIc,GAAGd,EAAIe,GAAGf,EAAIW,SAASqO,eAAe9O,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,OAAO,CAACE,YAAY,SAAS,CAACJ,EAAIc,GAAG,WAAWZ,EAAG,OAAO,CAACF,EAAIc,GAAGd,EAAIe,GAAGf,EAAIW,SAASsO,kBAAkB/O,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,OAAO,CAACE,YAAY,SAAS,CAACJ,EAAIc,GAAG,WAAWZ,EAAG,OAAO,CAACF,EAAIc,GAAGd,EAAIe,GAAGf,EAAIW,SAASuO,oBAAoBhP,EAAG,WAAW,CAACG,MAAM,CAAC,aAAa,QAAQ,KAAKH,EAAG,cAAc,CAACG,MAAM,CAAC,MAAQ,OAAO,KAAOL,EAAI4M,QAAQ,WAAW,CAAC1M,EAAG,eAAe,CAACE,YAAY,eAAemB,YAAY,CAAC,OAAS,UAAU,CAAEvB,EAAIkD,KAAMhD,EAAG,eAAe,CAACO,GAAG,CAAC,KAAOT,EAAImP,cAAclO,MAAM,CAACC,MAAOlB,EAAI6B,OAAQT,SAAS,SAAUC,GAAMrB,EAAI6B,OAAOR,GAAKC,WAAW,WAAW,CAACpB,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,MAAM,CAACE,YAAY,cAAc,CAACF,EAAG,WAAW,CAACG,MAAM,CAAC,KAAO,QAAQ,YAAc,YAAY2E,SAAS,CAAC,MAAQ,SAASxD,GAAQ,OAAIA,EAAO4D,KAAKC,QAAQ,QAAQrF,EAAIsF,GAAG9D,EAAO+D,QAAQ,QAAQ,GAAG/D,EAAOkC,IAAI,SAAgB,KAAY1D,EAAIoP,QAAQlK,MAAM,KAAMC,aAAalE,MAAM,CAACC,MAAOlB,EAAIqP,QAASjO,SAAS,SAAUC,GAAMrB,EAAIqP,QAAQhO,GAAKC,WAAW,aAActB,EAAIsP,WAAWxK,OAAQ5E,EAAG,MAAM,CAACE,YAAY,cAAcJ,EAAIsD,GAAItD,EAAIsP,YAAY,SAAS9L,EAAKC,GAAO,OAAOvD,EAAG,MAAM,CAACwD,IAAID,EAAMrD,YAAY,cAAc,CAACJ,EAAIsD,GAAIE,EAAK+L,aAAa,SAASC,EAAMC,GAAQ,OAAOvP,EAAG,MAAM,CAACwD,IAAI+L,EAAOrP,YAAY,4BAA4B,CAACF,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,WAAW,CAACqB,YAAY,CAAC,MAAQ,OAAO,OAAS,QAAQlB,MAAM,CAAC,IAAMmP,EAAM1G,UAAU,GAAG5I,EAAG,MAAM,CAACE,YAAY,eAAemB,YAAY,CAAC,YAAY,MAAM,CAACrB,EAAG,MAAM,CAACE,YAAY,qBAAqB,CAACJ,EAAIc,GAAG,IAAId,EAAIe,GAAGyO,EAAME,YAAY,OAAOxP,EAAG,MAAM,CAACE,YAAY,eAAe,CAACJ,EAAIc,GAAG,IAAId,EAAIe,GAAGyO,EAAMG,YAAY,OAAOzP,EAAG,MAAM,CAACE,YAAY,gCAAgC,CAACF,EAAG,MAAM,CAACE,YAAY,MAAM,CAACJ,EAAIc,GAAG,KAAKd,EAAIe,GAAGyO,EAAMI,aAAa,OAAO1P,EAAG,MAAM,CAACE,YAAY,SAAS,CAACJ,EAAIc,GAAG,KAAKd,EAAIe,GAAGyO,EAAMK,WAAW,gBAAe3P,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,UAAU,CAACF,EAAG,OAAO,CAACE,YAAY,SAAS,CAACJ,EAAIc,GAAG,WAAWZ,EAAG,OAAO,CAACF,EAAIc,GAAGd,EAAIe,GAAGyC,EAAKsM,sBAAsB5P,EAAG,MAAM,CAACE,YAAY,UAAU,CAACF,EAAG,OAAO,CAACE,YAAY,SAAS,CAACJ,EAAIc,GAAG,WAAWZ,EAAG,OAAO,CAACF,EAAIc,GAAGd,EAAIe,GAAGyC,EAAKuM,eAAe7P,EAAG,MAAM,CAACE,YAAY,UAAU,CAACF,EAAG,OAAO,CAACE,YAAY,SAAS,CAACJ,EAAIc,GAAG,WAAWZ,EAAG,OAAO,CAACE,YAAY,eAAe8D,MAAM,CAC7hH,WAEI,GADAV,EAAKwM,eAEV,CAAChQ,EAAIc,GAAGd,EAAIe,GAAGyC,EAAKyM,wBAAwB/P,EAAG,MAAM,CAACE,YAAY,UAAU,CAACF,EAAG,OAAO,CAACE,YAAY,SAAS,CAACJ,EAAIc,GAAG,WAAWZ,EAAG,OAAO,CAACF,EAAIc,GAAGd,EAAIe,GAAGyC,EAAK0M,mBAAmBhQ,EAAG,MAAM,CAACE,YAAY,UAAU,CAACF,EAAG,OAAO,CAACE,YAAY,SAAS,CAACJ,EAAIc,GAAG,WAAWZ,EAAG,OAAO,CAACF,EAAIc,GAAGd,EAAIe,GAAGyC,EAAK0L,qBAAqB,MAAK,GAAGlP,EAAIqD,MAAM,OAAOnD,EAAG,WAAW,CAACG,MAAM,CAAC,aAAa,QAAQ,IAAI,IAAI,IAAI,IAEvbQ,GAAkB,GCiLP,IACfkB,2BACA6I,YACAoD,gBAEA/L,OACAiB,MACAkC,uBAIApD,gBACAE,OACA,OAEAuK,mBAEAG,uBACAuD,SACAC,YAGAf,WACA1O,YACA2O,cAEA1K,YACAyL,aACA3J,OACA7E,qBAGAM,OACAe,QACA,YACA,sBACA,mBACA,gBACA,GAIA,mBACA,qBAJA,mBAOAb,SACA+M,UACA,YACA,sBACA,mBACA,qBAEAkB,cACAC,gBACAtE,oBACA,SACA,mBAGAkD,eACA,yBACAqB,gBACAvJ,kBACAgF,kBACA8D,wBACA,SACA,YACA,gCACA,wBACA,oBACA,SACA,8BAMAhJ,UAEA,4CCvQ2X,MCQvX,I,UAAY,eACd,GACA,GACA,IACA,EACA,KACA,WACA,OAIa,M,QCnBXhH,GAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,QAAQ,CAACK,IAAI,WAAWF,MAAM,CAAC,UAAW,IAAQ,CAACH,EAAG,SAAS,CAACG,MAAM,CAAC,IAAML,EAAIyQ,UAEnJ5P,GAAkB,GCOP,IAEfoB,OACAwO,KACArL,cACAhB,WACA2B,oBAIA7D,OACA,UAIAG,SACAqO,OAAA,MACA,wDC1B8V,MCO1V,GAAY,eACd,GACA,GACA,IACA,EACA,KACA,KACA,MAIa,M,QCZf,MAAMC,GACFC,aAAY,GAACC,EAAE,OAAEvO,KAAWwO,IACxB7Q,KAAK4Q,GAAKA,EAAM,IAAGE,eAAczO,GACjCrC,KAAK+Q,MAAQ,KACb/Q,KAAK6Q,MAAQA,EACb7Q,KAAKgR,eAAgB,EAC3BhR,KAAKiR,iBAAmB,KACxBjR,KAAKkR,cAAgB,EAErBlR,KAAKmR,QAAU,IACfnR,KAAKoR,cAAgB,KACrBpR,KAAKqR,cAAgB,KACfrR,KAAKsR,OAGTA,OACQtR,KAAK4Q,KACL5Q,KAAK+Q,MAAQ,IAAIQ,UAAUvR,KAAK4Q,IAEhC5Q,KAAK+Q,MAAMS,OAASxR,KAAKyR,OAAOC,KAAK1R,MACrCA,KAAK+Q,MAAMY,QAAU3R,KAAK4R,QAAQF,KAAK1R,MACvCA,KAAK+Q,MAAMc,UAAY7R,KAAK8R,UAAUJ,KAAK1R,MAC3CA,KAAK+Q,MAAMgB,QAAU/R,KAAKgS,QAAQN,KAAK1R,OAI/CyR,SACIjP,QAAQyP,IAAI,QACZjS,KAAKkS,QACLlS,KAAK6Q,MAAMsB,MAAQnS,KAAK6Q,MAAMsB,OAGlCP,QAAQQ,GACJ5P,QAAQyP,IAAI,SACZjS,KAAK6Q,MAAMwB,OAASrS,KAAK6Q,MAAMwB,MAAMD,GAGzCN,UAAU7P,GACNO,QAAQyP,IAAI,WACZjS,KAAKsS,QACLtS,KAAK6Q,MAAMrD,SAAWxN,KAAK6Q,MAAMrD,QAAQvL,GAG7C+P,UACIxP,QAAQyP,IAAI,SACZjS,KAAKuS,YACLvS,KAAK6Q,MAAM2B,OAASxS,KAAK6Q,MAAM2B,QAInCC,KAAKxQ,GACDjC,KAAK+Q,MAAM0B,KAAKC,KAAKC,UAAU1Q,IAGnCqQ,QACFtS,KAAKkR,cAAgB,EACrBlR,KAAKkS,QAIHK,YACGvS,KAAKgR,gBAGVxO,QAAQyP,IAAIjS,KAAKkR,eAEblR,KAAKkR,eAAiB,IAI1BlR,KAAKkR,gBACLlR,KAAKgR,eAAgB,EAErB4B,aAAa5S,KAAKiR,kBAClBjR,KAAKiR,iBAAmB4B,WAAW,KAClC7S,KAAKsR,OACLtR,KAAKgR,eAAgB,GACnB,OAEJkB,QACCU,aAAa5S,KAAKoR,eAClBwB,aAAa5S,KAAKqR,eAClBrR,KAAKoR,cAAgByB,WAAW,KAC/B7S,KAAKyS,KAAK,CACT5B,MAAO,SAER7Q,KAAKqR,cAAgBwB,WAAW,KAC/B7S,KAAK+Q,MAAMyB,SACTxS,KAAKmR,UACNnR,KAAKmR,SAENqB,QACFxS,KAAKgR,eAAgB,EACrB4B,aAAa5S,KAAKoR,eAClBwB,aAAa5S,KAAKqR,eAClBrR,KAAK+Q,MAAMyB,OAASxS,KAAK+Q,MAAMyB,SAIlB9B,UCvDA,IACf5O,cAEA6I,YACAmI,kBACAC,sBACAC,uBACAC,iBACAC,eAGAjR,OACA,OACA1B,aACAF,cAIA8B,aACA,uDAGAgR,UACA,OACAC,6BACAX,eACAY,mCACAC,+BAIA,sBAGA,yBACA,sBAEAC,gBACA,uBAEAnR,YACA,gCAKAgR,eACA,qBACAI,kBACA,KAOAf,UACA,uBACA5B,QACA5O,UAKAoR,iBACA,0BAIAC,cACA,yBAMAG,gBACA,2BACA,aACA,yBACA7C,cAEAvO,QACAgC,iBACAc,YACAuO,SACAjF,qBAGA0D,OACAwB,cACAzH,KAGAsB,cAAAvL,IAAA,MACA,YAAA4O,EAAA5O,QAAA,kBAEA,UACA,eACA0R,mCACAA,mCACA,uDACA,MACA,gBACAA,wBACA,MACA,iBACAA,uBACA,MACA,eACAnR,6CACA,MACA,sBACAmR,sCACA,MACA,mBACAA,oCACA,QAKAtB,SACAjG,KAGAoG,QACAmB,oBASAlT,iBACA+B,eACA,oBC5L8V,MCQ1V,I,UAAY,eACd,GACA1C,EACAc,GACA,EACA,KACA,WACA,OAIa,gB,6CCnBf,W,oCCCA,IAAIgT,EAAI,EAAQ,QACZC,EAAW,EAAQ,QACnBC,EAAoB,EAAQ,QAC5BC,EAAiB,EAAQ,QACzBC,EAAwB,EAAQ,QAChCC,EAA2B,EAAQ,QAGnCC,EAAqC,IAAlB,GAAGC,QAAQ,GAG9BC,EAAiC,WACnC,IAEE9L,OAAO+L,eAAe,GAAI,SAAU,CAAEC,UAAU,IAASH,UACzD,MAAO9B,GACP,OAAOA,aAAiB5S,YAIxB8U,EAASL,IAAqBE,IAIlCR,EAAE,CAAEY,OAAQ,QAASC,OAAO,EAAMC,MAAO,EAAGC,OAAQJ,GAAU,CAE5DJ,QAAS,SAAiB5Q,GACxB,IAAI3D,EAAIiU,EAAS7T,MACb4U,EAAMd,EAAkBlU,GACxBiV,EAAW3P,UAAUL,OACzB,GAAIgQ,EAAU,CACZZ,EAAyBW,EAAMC,GAC/B,IAAI/K,EAAI8K,EACR,MAAO9K,IAAK,CACV,IAAIgL,EAAKhL,EAAI+K,EACT/K,KAAKlK,EAAGA,EAAEkV,GAAMlV,EAAEkK,GACjBkK,EAAsBpU,EAAGkV,GAEhC,IAAK,IAAIC,EAAI,EAAGA,EAAIF,EAAUE,IAC5BnV,EAAEmV,GAAK7P,UAAU6P,GAEnB,OAAOhB,EAAenU,EAAGgV,EAAMC,O,sFC1CrC,W,oCCAA,W,oCCAA,W,+GCAA,W,mECAAnV,EAAOC,QAAU,8yG,kCCAjB,W,kCCAA,W,kCCAA,W,kCCAA,W,kCCAA,W,4CCAAD,EAAOC,QAAU,IAA0B", "file": "js/chunk-449349ae.96d1a4c1.js", "sourcesContent": ["'use strict';\nvar tryToString = require('../internals/try-to-string');\n\nvar $TypeError = TypeError;\n\nmodule.exports = function (O, P) {\n  if (!delete O[P]) throw $TypeError('Cannot delete property ' + tryToString(P) + ' of ' + tryToString(O));\n};\n", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"window-contain\"},[_c('div',{staticClass:\"window\"},[_c('header',{staticClass:\"window-header\"},[_c('the-window-header',{attrs:{\"isStatus\":_vm.isStatus}})],1),_c('section',{staticClass:\"window-content\"},[_c('aside',{staticClass:\"window-aside--left\"},[_c('the-window-aside-left',{ref:\"user\",attrs:{\"current\":_vm.sessionID},on:{\"change\":_vm.changeSession}})],1),_c('main',{staticClass:\"window-main\"},[_c('the-window-main',{ref:\"chat\",attrs:{\"to-id\":_vm.sessionID,\"from-id\":_vm.userInfo.id}})],1),_c('aside',{staticClass:\"window-aside--right\"},[_c('the-window-aside-right',{attrs:{\"to-id\":_vm.sessionID}})],1)])]),_c('PromptTone',{ref:\"promptToneRef\"})],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"window__header\"},[_c('div',{staticClass:\"window-title\"},[_vm._v(\"客服工作台\")]),_c('div',{staticClass:\"window-widget\"},[_c('div',{staticClass:\"widget-item m-r-20\"},[_c('span',{staticClass:\"m-r-6\"},[_vm._v(_vm._s(_vm.userInfo.nickname))]),_c('el-popover',{staticClass:\"line-status\",attrs:{\"placement\":\"right\",\"title\":\"\",\"width\":\"60\",\"trigger\":\"manual\",\"popper-class\":\"on-line\"},model:{value:(_vm.isTC),callback:function ($$v) {_vm.isTC=$$v},expression:\"isTC\"}},[_c('div',{},[_c('div',{staticClass:\"flex text-center\",staticStyle:{\"cursor\":\"pointer\"},on:{\"click\":function($event){return _vm.onLine(true)}}},[_c('span',{staticClass:\"m-r-10\",staticStyle:{\"display\":\"block\",\"content\":\"' '\",\"border-radius\":\"50%\",\"height\":\"10px\",\"width\":\"10px\",\"background-color\":\"#00c24c\"}}),_vm._v(\" 在线 \")]),_c('div',{staticClass:\"flex text-center\",staticStyle:{\"cursor\":\"pointer\"},on:{\"click\":function($event){return _vm.offLine(false)}}},[_c('span',{staticClass:\"m-r-10\",staticStyle:{\"display\":\"block\",\"content\":\"' '\",\"border-radius\":\"50%\",\"height\":\"10px\",\"width\":\"10px\",\"background-color\":\"#c5c5c5\"}}),_vm._v(\" 离线 \")])]),_c('div',{staticStyle:{\"position\":\"relative\"},attrs:{\"slot\":\"reference\"},on:{\"click\":function($event){_vm.isTC = !_vm.isTC}},slot:\"reference\"},[_c('el-avatar',{attrs:{\"icon\":\"el-icon-user\",\"src\":_vm.userInfo.avatar}}),(_vm.status == false)?_c('div',{staticStyle:{\"position\":\"absolute\",\"top\":\"30px\",\"left\":\"30px\",\"height\":\"10px\",\"width\":\"10px\",\"border-radius\":\"50%\",\"background-color\":\"#c5c5c5\"}}):_c('div',{staticStyle:{\"position\":\"absolute\",\"top\":\"30px\",\"left\":\"30px\",\"height\":\"10px\",\"width\":\"10px\",\"border-radius\":\"50%\",\"background-color\":\"#00c24c\"}})],1)])],1),_c('el-popconfirm',{attrs:{\"title\":\"确定退出吗？\"},on:{\"confirm\":_vm.handleLogout}},[_c('div',{staticClass:\"widget-item\",attrs:{\"slot\":\"reference\"},slot:\"reference\"},[_c('i',{staticClass:\"el-icon-right\"}),_c('span',{staticClass:\"m-l-6\"},[_vm._v(\"退出\")])])])],1)])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n    <div class=\"window__header\">\r\n        <!-- Title -->\r\n        <div class=\"window-title\">客服工作台</div>\r\n\r\n        <!-- Widget -->\r\n        <div class=\"window-widget\">\r\n            <!-- User -->\r\n            <div class=\"widget-item m-r-20\">\r\n                <span class=\"m-r-6\">{{ userInfo.nickname }}</span>\r\n\r\n                <el-popover\r\n                    placement=\"right\"\r\n                    title=\"\"\r\n                    width=\"60\"\r\n                    trigger=\"manual\"\r\n                    class=\"line-status\"\r\n                    popper-class=\"on-line\"\r\n                    v-model=\"isTC\"\r\n                >\r\n                    <div class=\"\">\r\n                        <div\r\n                            class=\"flex text-center\"\r\n                            style=\"cursor: pointer\"\r\n                            @click=\"onLine(true)\"\r\n                        >\r\n                            <span\r\n                                class=\"m-r-10\"\r\n                                style=\"\r\n                                    display: block;\r\n                                    content: ' ';\r\n                                    border-radius: 50%;\r\n                                    height: 10px;\r\n                                    width: 10px;\r\n                                    background-color: #00c24c;\r\n                                \"\r\n                            ></span>\r\n                            在线\r\n                        </div>\r\n                        <div\r\n                            class=\"flex text-center\"\r\n                            style=\"cursor: pointer\"\r\n                            @click=\"offLine(false)\"\r\n                        >\r\n                            <span\r\n                                class=\"m-r-10\"\r\n                                style=\"\r\n                                    display: block;\r\n                                    content: ' ';\r\n                                    border-radius: 50%;\r\n                                    height: 10px;\r\n                                    width: 10px;\r\n                                    background-color: #c5c5c5;\r\n                                \"\r\n                            ></span>\r\n                            离线\r\n                        </div>\r\n                    </div>\r\n                    <div\r\n                        class=\"\"\r\n                        style=\"position: relative\"\r\n                        slot=\"reference\"\r\n                        @click=\"isTC = !isTC\"\r\n                    >\r\n                        <el-avatar\r\n                            icon=\"el-icon-user\"\r\n                            :src=\"userInfo.avatar\"\r\n                        ></el-avatar>\r\n                        <div\r\n                            v-if=\"status == false\"\r\n                            style=\"\r\n                                position: absolute;\r\n                                top: 30px;\r\n                                left: 30px;\r\n                                height: 10px;\r\n                                width: 10px;\r\n                                border-radius: 50%;\r\n                                background-color: #c5c5c5;\r\n                            \"\r\n                        ></div>\r\n                        <div\r\n                            v-else\r\n                            style=\"\r\n                                position: absolute;\r\n                                top: 30px;\r\n                                left: 30px;\r\n                                height: 10px;\r\n                                width: 10px;\r\n                                border-radius: 50%;\r\n                                background-color: #00c24c;\r\n                            \"\r\n                        ></div>\r\n                    </div>\r\n                </el-popover>\r\n            </div>\r\n\r\n            <!-- Logout -->\r\n            <el-popconfirm title=\"确定退出吗？\" @confirm=\"handleLogout\">\r\n                <div class=\"widget-item\" slot=\"reference\">\r\n                    <i class=\"el-icon-right\"></i>\r\n                    <span class=\"m-l-6\">退出</span>\r\n                </div>\r\n            </el-popconfirm>\r\n        </div>\r\n    </div>\r\n</template>\r\n\r\n\r\n<script>\r\nimport { mapGetters, mapActions } from 'vuex'\r\n\r\nexport default {\r\n    name: 'TheWindowHeader',\r\n\r\n    inject: ['closeChatServe', 'reChatServe', 'reload'],\r\n\r\n    props: {\r\n        isStatus: Boolean,\r\n    },\r\n    data() {\r\n        return {\r\n            status: false,\r\n            isTC: false,\r\n        }\r\n    },\r\n\r\n    watch: {\r\n        isStatus(val) {\r\n            this.status = val\r\n        },\r\n    },\r\n\r\n    computed: {\r\n        ...mapGetters(['userInfo']),\r\n    },\r\n\r\n    methods: {\r\n        ...mapActions(['logout']),\r\n        handleLogout() {\r\n            const params = {}\r\n            if (this.userInfo.shop_id != 0) {\r\n                params.type = 1\r\n            }\r\n            this.logout().then((res) => {\r\n                this.$router.push({\r\n                    path: '/login',\r\n                    query: params,\r\n                })\r\n            })\r\n        },\r\n        onLine(val) {\r\n            if (!this.status && val) {\r\n                console.log('reChatServe')\r\n                this.reChatServe()\r\n            }\r\n\r\n            this.status = val\r\n            this.isTC = false\r\n        },\r\n\r\n        offLine(val) {\r\n            if (this.status) {\r\n                this.closeChatServe()\r\n            }\r\n            this.status = val\r\n            this.isTC = false\r\n            console.log(val)\r\n        },\r\n    },\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.window__header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    height: 100%;\r\n    padding: 0 20px;\r\n    background-color: $--color-primary;\r\n    color: #ffffff;\r\n\r\n    .window-title {\r\n        font-size: 16px;\r\n    }\r\n\r\n    .window-widget {\r\n        display: flex;\r\n        align-items: center;\r\n\r\n        .widget-item {\r\n            display: flex;\r\n            align-items: center;\r\n            cursor: pointer;\r\n\r\n            &:nth-child(n + 2) {\r\n                margin-left: 20px;\r\n            }\r\n        }\r\n    }\r\n\r\n    .on-line {\r\n        display: block;\r\n        content: ' ';\r\n        border-radius: 50%;\r\n        height: 10px;\r\n        width: 10px;\r\n        background-color: #00c24c;\r\n    }\r\n\r\n    .off-line {\r\n        display: block;\r\n        content: ' ';\r\n        border-radius: 50%;\r\n        height: 10px;\r\n        width: 10px;\r\n        background-color: #c5c5c5;\r\n    }\r\n}\r\n</style>\r\n", "import mod from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./TheWindowHeader.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./TheWindowHeader.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./TheWindowHeader.vue?vue&type=template&id=1e1d3a65&scoped=true&\"\nimport script from \"./TheWindowHeader.vue?vue&type=script&lang=js&\"\nexport * from \"./TheWindowHeader.vue?vue&type=script&lang=js&\"\nimport style0 from \"./TheWindowHeader.vue?vue&type=style&index=0&id=1e1d3a65&prod&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"1e1d3a65\",\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"window__main\"},[_c('overlay-scrollbars',{ref:\"scrollbar\",staticClass:\"history-contain\",attrs:{\"options\":{\n            scrollbars: { autoHide: 'scroll' },\n            overflowBehavior: { x: 'hidden' },\n            callbacks: { onScroll: _vm.handlerMoreHistory },\n        }}},[(_vm.toId)?_c('morebar',{attrs:{\"status\":_vm.pagination.status},on:{\"onmore\":_vm.getHistoryMore}}):_vm._e(),_vm._l((_vm.historyList),function(item,index){return _c('div',{key:item.id,attrs:{\"id\":item.id}},[(_vm.timeFormat(item, index))?_c('div',{staticClass:\"text-center muted p-t-10\"},[_vm._v(_vm._s(_vm.timeFormat(item, index)))]):_vm._e(),(item.from_type === 'user')?_c('div',{staticClass:\"message-contain message--his\"},[_c('chat-message',{attrs:{\"avatar\":_vm.imageURL + item.from_avatar}},[_c('chat-content',{attrs:{\"slot\":\"his\",\"type\":item.msg_type,\"content\":item.msg,\"imageURL\":_vm.imageURL},slot:\"his\"})],1)],1):_c('div',{staticClass:\"message-contain message--my\"},[_c('chat-message',{attrs:{\"avatar\":_vm.imageURL + item.from_avatar}},[_c('chat-content',{attrs:{\"slot\":\"my\",\"type\":item.msg_type,\"content\":item.msg,\"imageURL\":_vm.imageURL},slot:\"my\"})],1)],1)])}),(!_vm.toId)?[_c('el-empty',{staticStyle:{\"height\":\"100%\"},attrs:{\"description\":\"请选择聊天用户\"}})]:_vm._e()],2),_c('div',{staticClass:\"editor-contain\"},[_c('div',{staticClass:\"editor__widget\"},[_c('el-popover',{attrs:{\"placement\":\"top\",\"title\":\"\",\"width\":\"240\",\"trigger\":\"click\",\"disabled\":!_vm.toId}},[_c('div',{staticClass:\"flex flex-wrap\",staticStyle:{\"gap\":\"4px\"}},_vm._l((_vm.emoji),function(item,index){return _c('span',{key:index,class:`em ${item} `,staticStyle:{\"font-size\":\"20px\"},on:{\"click\":function($event){return _vm.sendEmoji(item)}}})}),0),_c('el-tooltip',{staticClass:\"item\",attrs:{\"slot\":\"reference\",\"effect\":\"dark\",\"content\":\"表情\",\"placement\":\"bottom\"},slot:\"reference\"},[_c('div',{staticClass:\"flex\",staticStyle:{\"margin-top\":\"-2px\"}},[_c('img',{staticStyle:{\"height\":\"20px\",\"width\":\"20px\",\"cursor\":\"pointer\"},attrs:{\"src\":require(\"@/assets/images/biaoqing.png\")}})])])],1),_c('el-tooltip',{staticClass:\"item\",attrs:{\"slot\":\"reference\",\"effect\":\"dark\",\"content\":\"图片\",\"placement\":\"bottom\"},slot:\"reference\"},[_c('el-upload',{attrs:{\"action\":_vm.uploadURL,\"accept\":\".jpg, .jpeg, .png, .JPG, .JPEG\",\"headers\":{ token: _vm.$store.getters.token },\"show-file-list\":false,\"before-upload\":_vm.beforeImageUpload,\"on-success\":_vm.sendMessageImage,\"disabled\":!_vm.toId}},[_c('i',{staticClass:\"widget-item el-icon-picture-outline-round\"})])],1),_c('el-tooltip',{staticClass:\"item\",attrs:{\"slot\":\"reference\",\"effect\":\"dark\",\"content\":\"快捷回复\",\"placement\":\"bottom\"},slot:\"reference\"},[_c('i',{staticClass:\"widget-item el-icon-chat-line-round m-b-4\",on:{\"click\":_vm.isShowReply}})]),_c('el-popover',{attrs:{\"placement\":\"top\",\"title\":\"\",\"width\":\"120\",\"trigger\":\"manual\",\"disabled\":!_vm.toId},model:{value:(_vm.showKefu),callback:function ($$v) {_vm.showKefu=$$v},expression:\"showKefu\"}},[_c('div',[_c('el-scrollbar',{staticClass:\"ls-scrollbar\",staticStyle:{\"height\":\"120px\"}},[(_vm.kefuLists.length)?_c('div',{staticClass:\"kefu-list\"},_vm._l((_vm.kefuLists),function(item,index){return _c('div',{key:index,staticClass:\"kefu-item flex m-b-10\",staticStyle:{\"cursor\":\"pointer\"},on:{\"click\":function($event){return _vm.transfer(item)}}},[_c('img',{staticStyle:{\"width\":\"20px\",\"height\":\"20px\",\"border-radius\":\"50%\"},attrs:{\"src\":item.avatar,\"alt\":\"\"}}),_c('div',{staticClass:\"line-1 m-l-8 xs\"},[_vm._v(_vm._s(item.nickname))])])}),0):_c('div',{staticClass:\"muted xs\"},[_vm._v(\"暂无可转接客服\")])])],1),_c('el-tooltip',{staticClass:\"item\",attrs:{\"slot\":\"reference\",\"effect\":\"dark\",\"content\":\"转线\",\"placement\":\"bottom\"},slot:\"reference\"},[_c('i',{staticClass:\"widget-item el-icon-refresh\",on:{\"click\":function($event){_vm.showKefu = !_vm.showKefu}}})])],1)],1),_c('el-input',{staticClass:\"editor__textarea\",attrs:{\"type\":\"textarea\",\"placeholder\":\"请输入内容\"},nativeOn:{\"keydown\":[function($event){return _vm.handleKeydown.apply(null, arguments)},function($event){if(!$event.type.indexOf('key')&&_vm._k($event.keyCode,\"enter\",13,$event.key,\"Enter\"))return null;$event.preventDefault();}],\"keyup\":function($event){if(!$event.type.indexOf('key')&&_vm._k($event.keyCode,\"enter\",13,$event.key,\"Enter\"))return null;return _vm.onSendMessage.apply(null, arguments)}},model:{value:(_vm.editorContent),callback:function ($$v) {_vm.editorContent=$$v},expression:\"editorContent\"}}),_c('div',{staticClass:\"editor__action\"},[_c('el-button',{attrs:{\"type\":\"primary\",\"size\":\"small\",\"disabled\":!_vm.editorContent || !_vm.toId,\"loading\":false},on:{\"click\":_vm.onSendMessage}},[_vm._v(\"发送\")])],1)],1),_c('quick-reply',{on:{\"select\":_vm.selectReply},model:{value:(_vm.showReply),callback:function ($$v) {_vm.showReply=$$v},expression:\"showReply\"}})],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"chat-message\"},[(_vm.$slots['my'])?_c('div',{staticClass:\"message-contain message-contain--my\"},[_vm._t(\"my\")],2):_vm._e(),_c('el-avatar',{staticClass:\"message-avatar\",attrs:{\"size\":40,\"src\":_vm.avatar}}),(_vm.$slots['his'])?_c('div',{staticClass:\"message-contain message-contain--his\"},[_vm._t(\"his\")],2):_vm._e()],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n    <div class=\"chat-message\">\n        <!-- My message -->\n        <div class=\"message-contain message-contain--my\" v-if=\"$slots['my']\">\n            <slot name=\"my\"></slot>\n        </div>\n\n        <!-- Avatar -->\n        <el-avatar class=\"message-avatar\" :size=\"40\" :src=\"avatar\"></el-avatar>\n\n        <!-- His message -->\n        <div class=\"message-contain message-contain--his\" v-if=\"$slots['his']\">\n            <slot name=\"his\"></slot>\n        </div>\n    </div>\n</template>\n\n\n<script>\nexport default {\n    name: 'ChatMessage',\n\n    props: {\n        avatar: {\n            type: String,\n            default: ''\n        }\n    }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.chat-message {\n    display: inline-flex;\n\n    .message-avatar {\n        min-width: 40px;\n    }\n\n    .message-contain {\n        padding: 10px 12px;\n        border-radius: 8px;\n        max-width: 1em * 32;\n    }\n\n    .message-contain--my {\n        margin-right: 10px;\n        background-color: $--color-primary;\n        color: #FFFFFF;\n    }\n\n    .message-contain--his {\n        margin-left: 10px;\n        border: 1px solid $--border-color-base;\n    }\n}\n</style>\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./index.vue?vue&type=template&id=83579a84&scoped=true&\"\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=83579a84&prod&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"83579a84\",\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"quick-reply\"},[_c('el-dialog',{attrs:{\"title\":\"快捷回复\",\"visible\":_vm.visible,\"width\":\"800px\",\"top\":\"30vh\"},on:{\"update:visible\":function($event){_vm.visible=$event}}},[_c('el-input',{attrs:{\"size\":\"small\",\"placeholder\":\"请输入快捷标题关键字搜索\",\"prefix-icon\":\"el-icon-search\"},nativeOn:{\"keyup\":function($event){if(!$event.type.indexOf('key')&&_vm._k($event.keyCode,\"enter\",13,$event.key,\"Enter\"))return null;return _vm.getLists.apply(null, arguments)}},model:{value:(_vm.keyword),callback:function ($$v) {_vm.keyword=$$v},expression:\"keyword\"}}),_c('overlay-scrollbars',{staticClass:\"scrollbars-contain\",attrs:{\"options\":{\n                    scrollbars: { autoHide: 'scroll' },\n                    overflowBehavior: { x: 'hidden' },\n                    callbacks: { onScroll: _vm.handleScroll },\n                }}},[(_vm.replyLists.length)?_c('div',{staticClass:\"reply-lists\"},_vm._l((_vm.replyLists),function(item,index){return _c('div',{key:index,staticClass:\"reply-item\",on:{\"click\":function($event){$event.stopPropagation();return _vm.handleSelect(item)}}},[_c('div',{staticClass:\"reply-item__title weight-500\"},[_vm._v(\" \"+_vm._s(item.title)+\" \")]),_c('div',{staticClass:\"reply-item__content muted m-t-5\"},[_vm._v(\" \"+_vm._s(item.content)+\" \")])])}),0):_c('el-empty',{attrs:{\"image-size\":100}})],1),(_vm.pagination.total*1)?_c('el-pagination',{staticStyle:{\"text-align\":\"center\"},attrs:{\"layout\":\"prev, pager, next\",\"total\":_vm.pagination.total,\"current-page\":_vm.pagination.page},on:{\"update:currentPage\":function($event){return _vm.$set(_vm.pagination, \"page\", $event)},\"update:current-page\":function($event){return _vm.$set(_vm.pagination, \"page\", $event)},\"current-change\":function($event){return _vm.getLists()}}}):_vm._e()],1)],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n    <div class=\"quick-reply\">\n        <el-dialog\n            title=\"快捷回复\"\n            :visible.sync=\"visible\"\n            width=\"800px\"\n            top=\"30vh\"\n        >\n            <el-input\n                size=\"small\"\n                placeholder=\"请输入快捷标题关键字搜索\"\n                prefix-icon=\"el-icon-search\"\n                v-model=\"keyword\"\n                @keyup.enter.native=\"getLists\"\n            >\n            </el-input>\n            <overlay-scrollbars\n                class=\"scrollbars-contain\"\n                :options=\"{\n                    scrollbars: { autoHide: 'scroll' },\n                    overflowBehavior: { x: 'hidden' },\n                    callbacks: { onScroll: handleScroll },\n                }\"\n            >\n                <div class=\"reply-lists\" v-if=\"replyLists.length\">\n                    <div\n                        class=\"reply-item\"\n                        v-for=\"(item, index) in replyLists\"\n                        :key=\"index\"\n                        @click.stop=\"handleSelect(item)\"\n                    >\n                        <div class=\"reply-item__title weight-500\">\n                            {{ item.title }}\n                        </div>\n                        <div class=\"reply-item__content muted m-t-5\">\n                            {{ item.content }}\n                        </div>\n                    </div>\n                </div>\n                <el-empty v-else :image-size=\"100\"></el-empty>\n            </overlay-scrollbars>\n\n\t\t\t<el-pagination\n\t\t\t    layout=\"prev, pager, next\"\n\t\t\t    :total=\"pagination.total\"\n\t\t\t\t:current-page.sync=\"pagination.page\"\n\t\t\t\t@current-change=\"getLists()\" \n\t\t\t\tstyle=\"text-align: center\"\n\t\t\t\tv-if=\"pagination.total*1\"\n\t\t\t>\n\t\t\t  </el-pagination>\n        </el-dialog>\n    </div>\n</template>\n\n\n<script>\nimport { apiReplyList } from '@/api/app'\nexport default {\n    name: 'QuickReply',\n\n    props: {\n        value: {\n            type: Boolean,\n            default: false,\n        },\n    },\n\n    data() {\n        return {\n            replyLists: [],\n            keyword: '',\n\t\t\t\n\t\t\t/* 分页器 */\n\t\t\tpagination: {\n\t\t\t\tsize: 10,\n\t\t\t\tpage: 1,\n\t\t\t\ttotal: 0,\n\t\t\t},\n        }\n    },\n    computed: {\n        visible: {\n            get() {\n                return this.value\n            },\n            set(val) {\n                this.$emit('input', val)\n            },\n        },\n    },\n    watch: {\n        visible(val) {\n            if (val) this.getLists()\n        }\n    },\n    created() {},\n\n    methods: {\n        handleScroll() {},\n        // 获取快捷回复列表\n        getLists() {\n\t\t\tlet { size, page, total} = this.pagination\n            apiReplyList({\n                keyword: this.keyword,\n\t\t\t\tpage_no: page,\n\t\t\t\tpage_size: size\n            }).then((res) => {\n\t\t\t\t// console.log('getLists', res)\n                this.replyLists = res.list\n            })\n        },\n        handleSelect(item) {\n            this.$emit('select', item.content)\n            this.visible = false\n        },\n    },\n}\n</script>\n\n\n<style lang=\"scss\" scoped>\n.quick-reply {\n    .scrollbars-contain {\n        height: 400px;\n        .reply-lists {\n            padding: 10px 0;\n            .reply-item {\n                border-radius: 4px;\n                background: #f9f9f9;\n                padding: 10px 16px;\n                font-size: 12px;\n                color: #333;\n                cursor: pointer;\n                &:not(:last-of-type) {\n                    margin-bottom: 10px;\n                }\n            }\n        }\n    }\n}\n</style>\n", "import mod from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./QuickReply.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./QuickReply.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./QuickReply.vue?vue&type=template&id=394c9cca&scoped=true&\"\nimport script from \"./QuickReply.vue?vue&type=script&lang=js&\"\nexport * from \"./QuickReply.vue?vue&type=script&lang=js&\"\nimport style0 from \"./QuickReply.vue?vue&type=style&index=0&id=394c9cca&prod&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"394c9cca\",\n  null\n  \n)\n\nexport default component.exports", "/**\n * 枚举\n */\n\n\n/* 消息类型 */\nexport const E_Msg = {\n    TEXT: 1,\n    IMAGE: 2,\n\tGOODS: 3,\n}\n\n/* 消息Event */\nexport const E_MsgEvent = {\n    CHAT: 'chat',\n    PING: 'ping',\n    ERROR: 'error',\n    NOTICE: 'notice',\n    USER_ONLINE: 'user_online',\n    TRANSFER: 'transfer',\n\n}\n\n/* 加载状态 */\nexport const E_Load = {\n    LOAD: 1,\n    NORMAL: 2,\n    ERROR: 3,\n    EMPTY: 0,\n}", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"morebar\"},[_c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.LoadMap['NORMAL'] === _vm.status),expression:\"LoadMap['NORMAL'] === status\"}],on:{\"click\":function($event){return _vm.$emit('onmore')}}},[_vm._t(\"normal\",function(){return [_c('i',{staticClass:\"status--normal\"},[_vm._v(\"_加载更多_\")])]})],2),_c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.LoadMap['LOAD'] === _vm.status),expression:\"LoadMap['LOAD'] === status\"}],on:{\"click\":function($event){return _vm.$emit('onload')}}},[_vm._t(\"load\",function(){return [_c('i',{staticClass:\"el-icon-loading status--load\"})]})],2),_c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.LoadMap['EMPTY'] === _vm.status),expression:\"LoadMap['EMPTY'] === status\"}],on:{\"click\":function($event){return _vm.$emit('onempty')}}},[_vm._t(\"empty\",function(){return [_c('i',{staticClass:\"status--empty\"},[_vm._v(\"没有更多了~\")])]})],2),_c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.LoadMap['ERROR'] === _vm.status),expression:\"LoadMap['ERROR'] === status\"}],on:{\"click\":function($event){return _vm.$emit('onerror')}}},[_vm._t(\"error\",function(){return [_c('i',{staticClass:\"status--error\"},[_vm._v(\"发生了一点错误，请重新加载!\")])]})],2)])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n    <div class=\"morebar\">\n        <!-- Normal -->\n        <div v-show=\"LoadMap['NORMAL'] === status\" @click=\"$emit('onmore')\">\n            <slot name=\"normal\">\n                <i class=\"status--normal\">_加载更多_</i>\n            </slot>\n        </div>\n        <!-- Loading -->\n        <div v-show=\"LoadMap['LOAD'] === status\" @click=\"$emit('onload')\">\n            <slot name=\"load\">\n                <i class=\"el-icon-loading status--load\"></i>\n            </slot>\n        </div>\n        <!-- Empty -->\n        <div v-show=\"LoadMap['EMPTY'] === status\" @click=\"$emit('onempty')\">\n            <slot name=\"empty\">\n                <i class=\"status--empty\">没有更多了~</i>\n            </slot>\n        </div>\n        <!-- Error -->\n        <div v-show=\"LoadMap['ERROR'] === status\" @click=\"$emit('onerror')\">\n            <slot name=\"error\"> \n                <i class=\"status--error\">发生了一点错误，请重新加载!</i>\n            </slot>\n        </div>\n    </div>\n</template>\n\n\n<script>\n/**\n * 加载更多组件\n * @prop {String} status 状态 E_Load\n * @event on-more 点击加载更多 (无参数) \n */\n\nimport {E_Load} from '@/utils/enum'\n\nexport default {\n    name: 'Morebar',\n\n    props: {\n        status: {\n            type: [String, Number],\n            require: true,\n            // validator: value => E_Load[value] !== undefined\n            validator: value => {\n                let valid = false\n                Object.keys(E_Load).forEach(key => {\n                    if (E_Load[key] === value) valid = true\n                })\n                return valid\n            }\n        }\n    },\n\n    data() {\n        return {\n            LoadMap: Object.freeze({...E_Load})\n        }\n    },\n}\n</script>\n\n\n<style lang=\"scss\" scoped>\n.morebar {\n    display: flex;\n    justify-content: center;\n    padding: 10px 0;\n    font-size: 12px;\n\n    .status--normal {\n        color: $--color-primary;\n        cursor: pointer;\n    }\n\n    .status--load {\n        color: $--color-primary;\n        font-size: 16px;\n    }\n\n    .status--empty {\n        color: $--color-text-regular;\n    }\n\n    .status--error {\n        color: $--color-danger;\n    }\n\n}\n</style>", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./index.vue?vue&type=template&id=82d53720&scoped=true&\"\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=82d53720&prod&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"82d53720\",\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"chat-content\"},[(_vm.MsgMap['TEXT'] === _vm.type)?[_c('div',{domProps:{\"innerHTML\":_vm._s(_vm.$options.filters.textToHtml(_vm.content))}})]:(_vm.MsgMap['IMAGE'] === _vm.type)?[_c('el-image',{attrs:{\"src\":_vm.imageURL+_vm.content}})]:(_vm.MsgMap['GOODS'] === _vm.type)?[_c('div',{staticClass:\"flex goods-message\"},[_c('div',{staticClass:\"goods-image m-r-10\"},[_c('el-image',{staticStyle:{\"width\":\"80px\",\"height\":\"80px\"},attrs:{\"src\":_vm.imageURL + _vm.goods.image}})],1),_c('div',{},[_c('div',{staticClass:\"goods-name nr line-2\"},[_vm._v(\" \"+_vm._s(_vm.goods.name)+\" \")]),_c('div',{staticClass:\"goods-price m-t-10 xs\"},[_vm._v(\" ￥\"+_vm._s(_vm.goods.min_price)+\" \")])])])]:_vm._e()],2)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n    <div class=\"chat-content\">\n        <!-- Text Message -->\n        <template v-if=\"MsgMap['TEXT'] === type\">\n            <div v-html=\"$options.filters.textToHtml(content)\"></div>\n        </template>\n\n        <!-- Image Message -->\n        <template v-else-if=\"MsgMap['IMAGE'] === type\">\n            <el-image :src=\"imageURL+content\"></el-image>\n        </template>\n\t\t\n\t\t<!-- Image Message -->\n\t\t<template v-else-if=\"MsgMap['GOODS'] === type\">\n\t\t\t<div class=\"flex goods-message\">\n\t\t\t\t<div class=\"goods-image m-r-10\">\n\t\t\t\t\t<el-image :src=\"imageURL + goods.image\" style=\"width: 80px;height: 80px;\"></el-image>\n\t\t\t\t</div>\n\t\t\t\t<div class=\"\">\n\t\t\t\t\t<div class=\"goods-name nr line-2\">\n\t\t\t\t\t\t{{ goods.name }}\n\t\t\t\t\t</div>\n\t\t\t\t\t<div class=\"goods-price m-t-10 xs\" >\n\t\t\t\t\t\t￥{{ goods.min_price }}\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t</template>\n\t\t\n    </div>\n</template>\n\n\n<script>\nimport {E_Msg} from '@/utils/enum'\n\nexport default {\n    name: 'ChatContent',\n\n    props: {\n        type: {\n            type: [String, Number],\n            require: true\n        },\n\n        content: {\n            type: [String, Number],\n            require: true\n        },\n\t\t\n\t\timageURL: {\n\t\t    type: String,\n\t\t}\n    },\n\n    data() {\n        return {\n            MsgMap: Object.freeze({...E_Msg}),\n        }\n    },\n\n    filters: {\n        textToHtml(content) {\n            console.log(content)\n\t\t\t\n\t\t\treturn content.replace(/\\[em-([a-z_]+)\\]/g, `<span class=\"em em-$1\"></span>`)\n        }\n    },\n\t\n\tcomputed: {\n\t\tgoods() {\n\t\t\t return JSON.parse(this.content)\n\t\t}\n\t}\n}\n</script>\n\n\n<style lang=\"scss\" scoped>\n\t.chat-content {\n\t\t.goods-message {\n\t\t\t.goods-image {\n\t\t\t\tmin-width: 80px;\n\t\t\t\tmin-height: 80px;\n\t\t\t}\n\t\t\t\n\t\t\t.goods-price {\n\t\t\t\tcolor: #FF2C3C;\n\t\t\t}\n\t\t}\n\t}\n</style>", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./index.vue?vue&type=template&id=557db40e&scoped=true&\"\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=557db40e&prod&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"557db40e\",\n  null\n  \n)\n\nexport default component.exports", "export default [\r\n\t'em-smile',\n\t'em-laughing',\n\t'em-blush',\n\t'em-smiley',\n\t'em-relaxed',\n\t'em-smirk',\n\t'em-heart_eyes',\n\t'em-kissing_heart',\n\t'em-kissing_closed_eyes',\n\t'em-flushed',\n\t'em-relieved',\n\t'em-satisfied',\n\t'em-grin',\n\t'em-wink',\n\t'em-stuck_out_tongue_winking_eye',\n\t'em-stuck_out_tongue_closed_eyes',\n\t'em-grinning',\n\t'em-kissing',\n\t'em-kissing_smiling_eyes',\n\t'em-stuck_out_tongue',\n\t'em-sleeping',\n\t'em-worried',\n\t'em-frowning',\n\t'em-anguished',\n\t'em-open_mouth',\n\t'em-grimacing',\n\t'em-confused',\n\t'em-hushed',\n\t'em-expressionless',\n\t'em-unamused',\n\t'em-sweat_smile',\n\t'em-sweat',\n\t'em-disappointed_relieved',\n\t'em-weary',\n\t'em-pensive',\n\t'em-disappointed',\n\t'em-confounded',\n\t'em-fearful',\n\t'em-cold_sweat',\n\t'em-persevere',\n\t'em-cry',\n\t'em-sob',\n\t'em-joy',\n\t'em-astonished',\n\t'em-scream',\n\t'em-tired_face',\n\t'em-angry',\n\t'em-rage',\n\t'em-triumph',\n\t'em-sleepy',\n\t'em-yum',\n\t'em-mask',\n\t'em-dizzy_face',\n\t'em-sunglasses',\n\t'em-imp',\n\t'em-smiling_imp',\n\t'em-neutral_face',\n\t'em-no_mouth',\n\t'em-innocent',\n\t'em-alien',\n\t'em-heart',\n\t'em-broken_heart',\n\t'em-hankey',\n\t'em-thumbsup',\n\t'em-thumbsdown',\n\t'em-ok_hand',\n\t'em-facepunch',\n\t'em-fist',\n\t'em-v',\n\t'em-point_up',\n\t'em-point_down',\n\t'em-point_left',\n\t'em-point_right',\n\t'em-pray'\r\n];", "const weekArr = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']\n\n/**\n * @description 时间格式化\n * @param dateTime { number } 时间错\n * @param fmt { string } 时间格式\n * @return { string }\n */\n// yyyy:mm:dd|yyyy:mm|yyyy年mm月dd日|yyyy年mm月dd日 hh时MM分等,可自定义组合\nexport const timeFormat = (dateTime, fmt = 'yyyy-mm-dd') => {\n\t// 如果为null,则格式化当前时间\n\tif (!dateTime) dateTime = Number(new Date());\n\t// 如果dateTime长度为10或者13，则为秒和毫秒的时间戳，如果超过13位，则为其他的时间格式\n\tif (dateTime.toString().length == 10) dateTime *= 1000;\n\tlet date = new Date(dateTime);\n\tlet ret;\n\tlet opt = {\n\t\t'y+': date.getFullYear().toString(), // 年\n\t\t'm+': (date.getMonth() + 1).toString(), // 月\n\t\t'd+': date.getDate().toString(), // 日\n\t\t'h+': date.getHours().toString(), // 时\n\t\t'M+': date.getMinutes().toString(), // 分\n\t\t's+': date.getSeconds().toString() // 秒\n\t};\n\tfor (let k in opt) {\n\t\tret = new RegExp('(' + k + ')').exec(fmt);\n\t\tif (ret) {\n\t\t\tfmt = fmt.replace(ret[1], ret[1].length == 1 ? opt[k] : opt[k].padStart(ret[1].length, '0'));\n\t\t}\n\t}\n\treturn fmt;\n}\n\n/**\n * @description 聊天记录专用时间格式化\n * @param dateTime { number } 时间错\n * @return { string }\n */\n\nexport const timeFormatChat = (dateTime) => {\n\tif (dateTime.toString().length == 10) dateTime *= 1000;\n\tlet date = new Date(dateTime);\n\tlet fmt = timeFormat(dateTime, 'yyyy年mm月dd日 hh:MM')\n\n\tif (isToday(date)) {\n\t\tfmt = timeFormat(dateTime, 'hh:MM')\n\t} else if (isThisWeak(date)) {\n\t\tfmt = weekArr[date.getDay()] + timeFormat(dateTime, ' hh:MM')\n\t} else if (isThisYear(date)) {\n\t\tfmt = timeFormat(dateTime, 'mm月dd日 hh:MM')\n\t}\n\treturn fmt\n\n\n\n}\n\n// 是否是今年\nconst isThisYear = (date) => {\n\tconst now = new Date()\n\treturn date.getYear() == now.getYear()\n}\n\n\n// 是否是今月\nconst isThisMonth = (date) => {\n\tconst now = new Date()\n\treturn isThisYear(date) && date.getMonth() == now.getMonth()\n}\n\n\n\n// 是否是今天\nconst isToday = (date) => {\n\tconst now = new Date()\n\treturn isThisMonth(date) && date.getDate() == now.getDate()\n}\n// 是否本周\n// 只考虑同月份的情况\nconst isThisWeak = (date) => {\n\tconst now = new Date()\n\tif (isThisMonth(date)) {\n\t\tif (now.getDay() - date.getDay() > 0 && now.getDate() - date.getDate() < 7) {\n\t\t\treturn true\n\t\t}\n\t} else {\n\t\treturn false\n\t}\n\n}", "<template>\r\n    <div class=\"window__main\">\r\n        <!-- History views -->\r\n        <overlay-scrollbars\r\n            class=\"history-contain\"\r\n            :options=\"{\r\n                scrollbars: { autoHide: 'scroll' },\r\n                overflowBehavior: { x: 'hidden' },\r\n                callbacks: { onScroll: handlerMoreHistory },\r\n            }\"\r\n            ref=\"scrollbar\"\r\n        >\r\n            <!-- History Loading Status -->\r\n            <morebar v-if=\"toId\" :status=\"pagination.status\" @onmore=\"getHistoryMore\"></morebar>\r\n\r\n            <!-- Hisstory Content -->\r\n            <div v-for=\"(item, index) in historyList\" :key=\"item.id\" :id=\"item.id\">\r\n                <div\r\n                    class=\"text-center muted p-t-10\"\r\n                    v-if=\"timeFormat(item, index)\"\r\n                >{{ timeFormat(item, index) }}</div>\r\n                <!-- His message -->\r\n                <div class=\"message-contain message--his\" v-if=\"item.from_type === 'user'\">\r\n                    <chat-message :avatar=\"imageURL + item.from_avatar\">\r\n                        <chat-content\r\n                            slot=\"his\"\r\n                            :type=\"item.msg_type\"\r\n                            :content=\"item.msg\"\r\n                            :imageURL=\"imageURL\"\r\n                        />\r\n                    </chat-message>\r\n                </div>\r\n\r\n                <!-- Mine message -->\r\n                <div class=\"message-contain message--my\" v-else>\r\n                    <chat-message :avatar=\"imageURL + item.from_avatar\">\r\n                        <chat-content\r\n                            slot=\"my\"\r\n                            :type=\"item.msg_type\"\r\n                            :content=\"item.msg\"\r\n                            :imageURL=\"imageURL\"\r\n                        />\r\n                    </chat-message>\r\n                </div>\r\n            </div>\r\n\r\n            <!-- No Session -->\r\n            <template v-if=\"!toId\">\r\n                <el-empty description=\"请选择聊天用户\" style=\"height: 100%\"></el-empty>\r\n            </template>\r\n        </overlay-scrollbars>\r\n\r\n        <!-- Editor -->\r\n        <div class=\"editor-contain\">\r\n            <!-- Widget -->\r\n            <div class=\"editor__widget\">\r\n                <!-- 发送表情 -->\r\n                <el-popover placement=\"top\" title width=\"240\" trigger=\"click\" :disabled=\"!toId\">\r\n                    <div class=\"flex flex-wrap\" style=\"gap: 4px\">\r\n                        <span\r\n                            :class=\"`em ${item} `\"\r\n                            v-for=\"(item, index) in emoji\"\r\n                            :key=\"index\"\r\n                            @click=\"sendEmoji(item)\"\r\n                            style=\"font-size: 20px\"\r\n                        ></span>\r\n                    </div>\r\n                    <el-tooltip\r\n                        slot=\"reference\"\r\n                        class=\"item\"\r\n                        effect=\"dark\"\r\n                        content=\"表情\"\r\n                        placement=\"bottom\"\r\n                    >\r\n                        <div class=\"flex\" style=\"margin-top: -2px;\">\r\n                            <img\r\n                                src=\"@/assets/images/biaoqing.png\"\r\n                                style=\"height: 20px; width: 20px;cursor: pointer;\"\r\n                            />\r\n                        </div>\r\n                    </el-tooltip>\r\n                </el-popover>\r\n                <!-- 选择图片 -->\r\n                <el-tooltip\r\n                    slot=\"reference\"\r\n                    class=\"item\"\r\n                    effect=\"dark\"\r\n                    content=\"图片\"\r\n                    placement=\"bottom\"\r\n                >\r\n                    <el-upload\r\n                        :action=\"uploadURL\"\r\n                        accept=\".jpg, .jpeg, .png, .JPG, .JPEG\"\r\n                        :headers=\"{ token: $store.getters.token }\"\r\n                        :show-file-list=\"false\"\r\n                        :before-upload=\"beforeImageUpload\"\r\n                        :on-success=\"sendMessageImage\"\r\n                        :disabled=\"!toId\"\r\n                    >\r\n                        <i class=\"widget-item el-icon-picture-outline-round\"></i>\r\n                    </el-upload>\r\n                </el-tooltip>\r\n                <!-- 快捷回复 -->\r\n                <el-tooltip\r\n                    slot=\"reference\"\r\n                    class=\"item\"\r\n                    effect=\"dark\"\r\n                    content=\"快捷回复\"\r\n                    placement=\"bottom\"\r\n                >\r\n                    <i class=\"widget-item el-icon-chat-line-round m-b-4\" @click=\"isShowReply\"></i>\r\n                </el-tooltip>\r\n                <!-- 切换客服 -->\r\n                <el-popover\r\n                    placement=\"top\"\r\n                    title\r\n                    width=\"120\"\r\n                    trigger=\"manual\"\r\n                    v-model=\"showKefu\"\r\n                    :disabled=\"!toId\"\r\n                >\r\n                    <div>\r\n                        <el-scrollbar class=\"ls-scrollbar\" style=\"height: 120px\">\r\n                            <div class=\"kefu-list\" v-if=\"kefuLists.length\">\r\n                                <div\r\n                                    class=\"kefu-item flex m-b-10\"\r\n                                    style=\"cursor: pointer;\"\r\n                                    v-for=\"(item, index) in kefuLists\"\r\n                                    :key=\"index\"\r\n                                    @click=\"transfer(item)\"\r\n                                >\r\n                                    <img\r\n                                        style=\"width: 20px; height: 20px;border-radius: 50%;\"\r\n                                        :src=\"item.avatar\"\r\n                                        alt\r\n                                    />\r\n                                    <div class=\"line-1 m-l-8 xs\">{{ item.nickname }}</div>\r\n                                </div>\r\n                            </div>\r\n                            <div v-else class=\"muted xs\">暂无可转接客服</div>\r\n                        </el-scrollbar>\r\n                    </div>\r\n                    <el-tooltip\r\n                        slot=\"reference\"\r\n                        class=\"item\"\r\n                        effect=\"dark\"\r\n                        content=\"转线\"\r\n                        placement=\"bottom\"\r\n                    >\r\n                        <i class=\"widget-item el-icon-refresh\" @click=\"showKefu = !showKefu\"></i>\r\n                    </el-tooltip>\r\n                </el-popover>\r\n            </div>\r\n\r\n            <!-- Editor -->\r\n            <el-input\r\n                class=\"editor__textarea\"\r\n                v-model=\"editorContent\"\r\n                type=\"textarea\"\r\n                placeholder=\"请输入内容\"\r\n                @keydown.native=\"handleKeydown\"\r\n                @keydown.native.enter.prevent\r\n                @keyup.native.enter=\"onSendMessage\"\r\n            />\r\n\r\n            <!-- Footer -->\r\n            <div class=\"editor__action\">\r\n                <el-button\r\n                    type=\"primary\"\r\n                    size=\"small\"\r\n                    :disabled=\"!editorContent || !toId\"\r\n                    :loading=\"false\"\r\n                    @click=\"onSendMessage\"\r\n                >发送</el-button>\r\n            </div>\r\n        </div>\r\n\r\n        <!-- 快捷回复 -->\r\n        <quick-reply v-model=\"showReply\" @select=\"selectReply\" />\r\n    </div>\r\n</template>\r\n\r\n\r\n<script>\r\nimport ChatMessage from '@/components/ChatMessage'\r\nimport QuickReply from './QuickReply'\r\nimport { apiChatHistory, apiUploadFile, apiServiceList } from '@/api/app'\r\nimport { E_Msg, E_MsgEvent, E_Load } from '@/utils/enum'\r\nimport Morebar from '@/components/Morebar'\r\nimport ChatContent from '@/components/ChatContent'\r\nimport config from '@/config'\r\n\r\nimport emojiArr from '@/utils/emojiArr.js'\r\nimport { debounce } from '@/utils/util.js'\r\nimport { timeFormatChat } from '@/utils/date'\r\nexport default {\r\n    name: 'TheWindowMain',\r\n\r\n    components: {\r\n        ChatMessage,\r\n        QuickReply,\r\n        Morebar,\r\n        ChatContent,\r\n    },\r\n\r\n    props: {\r\n        toId: {\r\n            type: [String, Number],\r\n            default: '',\r\n        },\r\n\r\n        fromId: {\r\n            type: [String, Number],\r\n            default: '',\r\n        },\r\n    },\r\n\r\n    inject: ['sendMessage', 'send'],\r\n\r\n    data() {\r\n        return {\r\n            // 编辑内容\r\n            editorContent: '',\r\n            // 历史消息列表\r\n            historyList: [],\r\n            // 显示快捷回复\r\n            showReply: false,\r\n            /* 分页器 */\r\n            pagination: {\r\n                size: 15,\r\n                page: 1,\r\n                status: E_Load['NORMAL'],\r\n                total: 0,\r\n                more: true,\r\n            },\r\n            /* 滚动条 */\r\n            scrollbar: null,\r\n            uploadURL: config.baseURL + '/kefuapi/file/formImage',\r\n\r\n            kefuLists: [],\r\n            showKefu: false,\r\n\r\n            showEmoji: false, // 表情输入框\r\n            emoji: [],\r\n\r\n            handlerMoreHistory: null,\r\n        }\r\n    },\r\n\r\n    watch: {\r\n        toId: {\r\n            handler(id) {\r\n                this.changeSession(id)\r\n            },\r\n            immediate: true,\r\n        },\r\n\r\n        showKefu(val) {\r\n            if (val) this.getKefuLists()\r\n        },\r\n    },\r\n    computed: {\r\n        timeFormat() {\r\n            return (item, index) => {\r\n                let timeFmt = timeFormatChat(item.create_time_stamp)\r\n                if (\r\n                    index &&\r\n                    item.create_time_stamp -\r\n                    this.historyList[index - 1].create_time_stamp <\r\n                    300 &&\r\n                    !item.show_time\r\n                ) {\r\n                    timeFmt = ''\r\n                }\r\n\r\n                return timeFmt\r\n            }\r\n        },\r\n        imageURL() {\r\n            return this.$store.getters.baseUrl\r\n        }\r\n    },\r\n    created() {\r\n        this.$on('message', this.receiveMessage)\r\n\r\n        this.emoji = emojiArr\r\n\r\n        this.handlerMoreHistory = debounce(300, this.loadHistoryMore)\r\n    },\r\n\r\n    mounted() {\r\n        this.scrollbar = this.$refs['scrollbar'].osInstance()\r\n    },\r\n\r\n    methods: {\r\n        // 出顶加载更多\r\n\r\n        async loadHistoryMore(e) {\r\n            const { scrollTop } = e.target\r\n            if (scrollTop < 20) {\r\n                let id = this.historyList[0] ? this.historyList[0].id : ''\r\n                const topEl = document.getElementById(id)\r\n                this.pagination.page++\r\n                this.historyList[0] && (this.historyList[0].show_time = true)\r\n                await this.getChatHistory(this.toId)\r\n                this.scrollbar.scroll(topEl)\r\n            }\r\n\r\n            console.log('scrollTop', scrollTop)\r\n        },\r\n\r\n        // 打开快捷回复\r\n        isShowReply() {\r\n            if (this.toId) {\r\n                this.showReply = true\r\n            }\r\n        },\r\n\r\n        // 发送表情\r\n        sendEmoji(item) {\r\n            this.editorContent += `[${item}]`\r\n            console.log(item)\r\n        },\r\n        handleKeydown(event) {\r\n            if (event.shiftKey && event.keyCode == 13) {\r\n                this.editorContent += '\\r\\n'\r\n            }\r\n        },\r\n        /**\r\n         * 点击发送消息\r\n         */\r\n        onSendMessage(event) {\r\n            if(event.shiftKey) return\r\n            if(!this.toId) return this.$message.error('请选择聊天用户')\r\n            this.sendMessage({\r\n                msg: this.editorContent,\r\n                msg_type: E_Msg['TEXT'],\r\n                to_id: this.toId,\r\n            })\r\n            this.editorContent = \"\"\r\n\r\n            const { max, position } = this.scrollbar.scroll()\r\n            if (max.y - position.y <= 50) {\r\n                this.scrollbar.scroll('100%')\r\n            }\r\n\r\n        },\r\n\r\n        /**\r\n         * 发送图片消息\r\n         */\r\n        sendMessageImage({ code, data, msg }) {\r\n            if (code !== 1) return this.$message.error(msg)\r\n\r\n            this.sendMessage({\r\n                msg: data.base_uri,\r\n                msg_type: E_Msg['IMAGE'],\r\n                to_id: this.toId,\r\n            })\r\n        },\r\n\r\n        /**\r\n         * 收到消息\r\n         */\r\n        receiveMessage(message) {\r\n            console.log(message)\r\n            if (message.to_id == this.toId || message.from_id == this.toId) {\r\n                this.historyList.push(message)\r\n            }\r\n            const { max, position } = this.scrollbar.scroll()\r\n            if (max.y - position.y <= 50) {\r\n                this.$nextTick(() => {\r\n                    this.scrollbar.scroll('100%')\r\n                })\r\n            }\r\n        },\r\n\r\n        /**\r\n         * 获取历史消息\r\n         */\r\n        getChatHistory(id) {\r\n            if (!id) return\r\n            return new Promise((resolve, reject) => {\r\n                const { size, page, more } = this.pagination\r\n                if (!more) return\r\n\r\n                this.pagination.status = E_Load['LOAD']\r\n                apiChatHistory({\r\n                    user_id: id,\r\n                    page_no: page,\r\n                    page_size: size,\r\n                })\r\n                    .then((data) => {\r\n                        this.historyList = [...data.list, ...this.historyList]\r\n                        this.pagination.total = data.count\r\n                        this.pagination.more = !!(data.more * 1)\r\n                        this.pagination.status = !!(data.more * 1)\r\n                            ? E_Load['NORMAL']\r\n                            : E_Load['EMPTY']\r\n                        resolve(data)\r\n                    })\r\n                    .catch((err) => {\r\n                        this.pagination.status = E_Load['ERROR']\r\n                        reject(err)\r\n                    })\r\n            })\r\n        },\r\n\r\n        /**\r\n         * 获取更多记录信息\r\n         */\r\n        async getHistoryMore() {\r\n            const topEl = document.getElementById(this.historyList[0].id)\r\n            this.historyList[0].show_time = true\r\n            this.pagination.page++\r\n            await this.getChatHistory(this.toId)\r\n            this.scrollbar.scroll(topEl)\r\n        },\r\n\r\n        // 选择快捷回复\r\n        selectReply(msg) {\r\n            this.editorContent = msg\r\n        },\r\n\r\n        /**\r\n         * 切换会话\r\n         */\r\n        async changeSession(id) {\r\n            this.pagination.size = 15\r\n            this.pagination.page = 1\r\n            this.pagination.total = 0\r\n            this.pagination.more = true\r\n            this.pagination.status = E_Load['NORMAL']\r\n            this.historyList = []\r\n            id && (await this.getChatHistory(id))\r\n\r\n            this.$nextTick(() => {\r\n                this.scrollbar.scroll('100%')\r\n                console.log(this.scrollbar.scroll())\r\n            })\r\n        },\r\n\r\n        /**\r\n         * 上传图片之前\r\n         */\r\n        beforeImageUpload(file) {\r\n            // const isJPG = file.type === 'image/jpeg';\r\n            // const isPNG = file.type === 'image/png';\r\n            const isLt2M = file.size / 1024 / 1024 < 2\r\n\r\n            if (!isLt2M) {\r\n                this.$message.error('上传图片大小不能超过 2MB!')\r\n            }\r\n            return isLt2M\r\n        },\r\n\r\n        getKefuLists() {\r\n            apiServiceList().then((res) => {\r\n                this.kefuLists = res\r\n            })\r\n        },\r\n\r\n        transfer(item) {\r\n            this.send('transfer', {\r\n                user_id: this.toId, // 用户id\r\n                kefu_id: item.id, // 新客服id\r\n            })\r\n            this.showKefu = false\r\n        },\r\n    },\r\n}\r\n</script>\r\n\r\n\r\n<style lang=\"scss\" scoped>\r\n.window__main {\r\n    display: flex;\r\n    flex-direction: column;\r\n    width: 100%;\r\n    height: 100%;\r\n\r\n    /* 历史消息容器 */\r\n    .history-contain {\r\n        flex: 1;\r\n        padding: 0 20px;\r\n        width: 100%;\r\n\r\n        .history-status {\r\n            padding: 10px;\r\n            font-size: 12px;\r\n            text-align: center;\r\n\r\n            &--load {\r\n                color: $--color-primary;\r\n            }\r\n\r\n            &--more {\r\n                color: $--color-primary;\r\n                cursor: pointer;\r\n            }\r\n\r\n            &--empty {\r\n                color: $--color-text-regular;\r\n            }\r\n        }\r\n\r\n        .message-contain {\r\n            padding: 10px 0;\r\n        }\r\n\r\n        .message--my {\r\n            display: flex;\r\n            justify-content: flex-end;\r\n        }\r\n\r\n        .message--his {\r\n            display: flex;\r\n            justify-content: flex-start;\r\n        }\r\n    }\r\n\r\n    /* 编辑器容器 */\r\n    .editor-contain {\r\n        display: flex;\r\n        flex-direction: column;\r\n        height: 180px;\r\n        padding: 0 10px 20px 10px;\r\n        border-top: 1px solid $--border-color-base;\r\n\r\n        // 小部件\r\n        .editor__widget {\r\n            display: flex;\r\n            align-items: center;\r\n            height: 40px;\r\n            gap: 12px;\r\n\r\n            .widget-item {\r\n                font-size: 20px;\r\n                color: $--color-text-secondary;\r\n                cursor: pointer;\r\n            }\r\n        }\r\n\r\n        // 编辑框\r\n        .editor__textarea {\r\n            flex: 1;\r\n\r\n            &::v-deep .el-textarea__inner {\r\n                height: 100%;\r\n                padding: 0;\r\n                border: none;\r\n            }\r\n        }\r\n\r\n        // 行为\r\n        .editor__action {\r\n            display: flex;\r\n            justify-content: flex-end;\r\n            padding-top: 10px;\r\n        }\r\n    }\r\n}\r\n</style>\r\n", "import mod from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./TheWindowMain.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./TheWindowMain.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./TheWindowMain.vue?vue&type=template&id=845fab14&scoped=true&\"\nimport script from \"./TheWindowMain.vue?vue&type=script&lang=js&\"\nexport * from \"./TheWindowMain.vue?vue&type=script&lang=js&\"\nimport style0 from \"./TheWindowMain.vue?vue&type=style&index=0&id=845fab14&prod&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"845fab14\",\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('el-tabs',{staticClass:\"window__aside-left\",attrs:{\"stretch\":true},model:{value:(_vm.tabsActiveIndex),callback:function ($$v) {_vm.tabsActiveIndex=$$v},expression:\"tabsActiveIndex\"}},[_c('div',{staticClass:\"chat-search\"},[_c('el-input',{attrs:{\"size\":\"small\",\"placeholder\":\"请输入用户昵称搜索\",\"clearable\":true},nativeOn:{\"keyup\":function($event){if(!$event.type.indexOf('key')&&_vm._k($event.keyCode,\"enter\",13,$event.key,\"Enter\"))return null;return _vm.handleSearch.apply(null, arguments)}},model:{value:(_vm.searchValue),callback:function ($$v) {_vm.searchValue=$$v},expression:\"searchValue\"}})],1),_c('el-tab-pane',{staticClass:\"chat-user__list\",attrs:{\"label\":\"用户列表\",\"name\":_vm.TabsMap['USER_LIST']}},[_c('el-scrollbar',{staticClass:\"ls-scrollbar scrollbar-wrapper\"},[_c('loading-more',{on:{\"load\":_vm.getUserList},model:{value:(_vm.pagination.status),callback:function ($$v) {_vm.$set(_vm.pagination, \"status\", $$v)},expression:\"pagination.status\"}},_vm._l((_vm.SessionList['user']),function(item,index){return _c('chat-user-item',{key:item.id,attrs:{\"is-read\":item.is_read,\"name\":item.nickname,\"avatar\":item.avatar,\"message\":item.msg,\"active\":_vm.current === item.user_id,\"time\":item.update_time,\"online\":item.online,\"msg-type\":item.msg_type},nativeOn:{\"click\":function($event){return _vm.changeCurrent(item, index)}}})}),1)],1)],1),_c('el-tab-pane',{attrs:{\"label\":\"\",\"name\":_vm.TabsMap['GROUP_CHAT'],\"disabled\":\"\"}})],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{class:['chat-user-item', { 'chat-user-item--active': _vm.active }]},[_c('div',{staticClass:\"chat__avatar\",class:{ 'chat__avatar--online': _vm.online }},[_c('el-badge',{attrs:{\"is-dot\":\"\",\"hidden\":<PERSON><PERSON><PERSON>(_vm.isRead)}},[_c('el-avatar',{attrs:{\"size\":38,\"src\":_vm.imageURL + _vm.avatar}})],1)],1),_c('div',{staticClass:\"chat__msg\"},[_c('div',{staticClass:\"chat__msg-info\"},[_c('div',{staticClass:\"user-name nr normal line-1\"},[_vm._v(_vm._s(_vm.name))]),_c('div',{staticClass:\"msg-time xs muted m-l-5\"},[_vm._v(_vm._s(_vm.getTime))])]),_c('div',{staticClass:\"chat__msg-content xs\"},[_c('div',{staticClass:\"msg-content muted line-1 flex-1\",domProps:{\"innerHTML\":_vm._s(_vm.getMessage)}})])])])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n    <div :class=\"['chat-user-item', { 'chat-user-item--active': active }]\">\n        <!-- Avatar -->\n        <div class=\"chat__avatar\" :class=\"{ 'chat__avatar--online': online }\">\n            <el-badge is-dot :hidden=\"Boolean(isRead)\">\n                <el-avatar :size=\"38\" :src=\"imageURL + avatar\"></el-avatar>\n            </el-badge>\n        </div>\n\n        <div class=\"chat__msg\">\n            <!-- User -->\n            <div class=\"chat__msg-info\">\n                <div class=\"user-name nr normal line-1\">{{ name }}</div>\n                <div class=\"msg-time xs muted m-l-5\">{{ getTime }}</div>\n            </div>\n\n            <!-- Message -->\n            <div class=\"chat__msg-content xs\">\n                <div class=\"msg-content muted line-1 flex-1\" v-html=\"getMessage\"></div>\n\n                <!-- <el-badge\n                  class=\"msg-badge\"\n                  v-show=\"!badge\"\n                  :value=\"''\"\n                />-->\n            </div>\n        </div>\n    </div>\n</template>\n\n\n<script>\n/**\n * 聊天用户组件\n * 聊天窗口列表固有组件，其宽度可随父容器适应\n * @param {String} avatar 头像\n * @param {String} name 用户昵称\n * @param {String} message 消息内容\n * @param {String} time 消息时间\n * @param {Number} badge 消息数量\n * @param {Boolean} active 活跃\n */\nimport { timeFormatChat } from '@/utils/date'\nexport default {\n    name: 'ChatUserItem',\n\n    props: {\n        avatar: {\n            type: String,\n            default: ''\n        },\n\n        name: {\n            type: String,\n            default: ''\n        },\n\n        message: {\n            type: String,\n            default: ''\n        },\n\n        time: {\n            type: String,\n            default: ''\n        },\n\n        badge: {\n            type: Number,\n            default: 0\n        },\n\n        active: {\n            type: Boolean,\n            default: true\n        },\n        online: {\n            type: Number\n        },\n        isRead: {\n            type: [Boolean, Number],\n        },\n        msgType: {\n            type: Number\n        },\n    },\n    computed: {\n        getTime() {\n            return this.time ? timeFormatChat(new Date(this.time).getTime()) : ''\n        },\n        getMessage() {\n            switch (this.msgType) {\n                case 1: return this.message.replace(/\\[em-([a-z_]+)\\]/g, `<span class=\"em em-$1\"></span>`)\n                case 2: return '图片'\n                case 3: return '商品'\n            }\n        },\n        imageURL() {\n            return this.$store.getters.baseUrl\n        }\n    }\n\n}\n</script>\n\n\n<style lang=\"scss\" scoped>\n.chat-user-item {\n    display: flex;\n    align-items: center;\n    padding: 0 10px;\n    height: 70px;\n    cursor: pointer;\n\n    &:hover {\n        // background-color: #F5F5F5;\n        // box-shadow: inset 0 1px 1px 0 rgba(0, 0, 0, 0.2);\n    }\n\n    &--active {\n        background-color: #f5f5f5;\n    }\n\n    .chat__avatar {\n        display: flex;\n        align-items: center;\n        height: 38px;\n        margin-right: 10px;\n        opacity: 0.5;\n        &--online {\n            opacity: 1;\n        }\n        ::v-deep .el-badge__content.is-dot {\n            height: 10px;\n            width: 10px;\n            box-sizing: content-box;\n        }\n    }\n\n    .chat__msg {\n        flex: 1;\n        display: flex;\n        flex-direction: column;\n        height: 38px;\n        overflow: hidden;\n\n        &-info {\n            display: flex;\n            align-items: center;\n            justify-content: space-between;\n\n            .user-name {\n                flex: 1;\n                white-space: nowrap;\n                text-overflow: ellipsis;\n            }\n\n            .msg-time {\n            }\n        }\n\n        &-content {\n            flex: 1;\n            display: flex;\n            align-items: center;\n\n            .msg-badge {\n                margin-left: auto;\n                line-height: 0;\n            }\n        }\n    }\n}\n</style>", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./index.vue?vue&type=template&id=326917c9&scoped=true&\"\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=326917c9&prod&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"326917c9\",\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{directives:[{name:\"infinite-scroll\",rawName:\"v-infinite-scroll\",value:(_vm.load),expression:\"load\"}],staticClass:\"loading-more\",attrs:{\"infinite-scroll-distance\":\"50\",\"infinite-scroll-disabled\":_vm.disabled}},[_vm._t(\"default\"),_c('morebar',{attrs:{\"status\":_vm.status}})],2)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n    <div class=\"loading-more\" infinite-scroll-distance=\"50\" v-infinite-scroll=\"load\" :infinite-scroll-disabled=\"disabled\">\n        <slot></slot>\n        <morebar :status=\"status\">\n        </morebar>\n    </div>\n</template>\n\n\n<script>\n/**\n * 分页组件\n * @prop {String} value 状态 E_Load\n * @event on-more 点击加载更多 (无参数) \n */\n\nimport {E_Load} from '@/utils/enum'\nimport Morebar from '@/components/Morebar'\nexport default {\n\n    props: {\n        value: {\n            type: [String, Number]\n        }\n    },\n    components: {\n        Morebar\n    },\n    data() {\n        return {\n            \n        }\n    },\n    computed: {\n        disabled() {\n            return this.status == E_Load['LOAD'] || this.status == E_Load['EMPTY']\n        },\n        status: {\n            get() {\n                return this.value\n            },\n            set(val) {\n                this.$emit('input', val)\n            }\n        }\n    },\n    methods: {\n        load() {\n            this.status = E_Load['LOAD']\n            this.$emit('load')\n        }\n    }\n}\n</script>\n\n\n<style lang=\"scss\" scoped>\n\n</style>", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./index.vue?vue&type=template&id=f5dab428&scoped=true&\"\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"f5dab428\",\n  null\n  \n)\n\nexport default component.exports", "<template>\n    <el-tabs\n        v-model=\"tabsActiveIndex\"\n        :stretch=\"true\"\n        class=\"window__aside-left\"\n    >\n        <!-- 搜索 -->\n        <div class=\"chat-search\">\n            <el-input\n                v-model=\"searchValue\"\n                size=\"small\"\n                placeholder=\"请输入用户昵称搜索\"\n                :clearable=\"true\"\n                @keyup.enter.native=\"handleSearch\"\n            />\n        </div>\n\n        <!-- 用户列表 -->\n        <el-tab-pane\n            class=\"chat-user__list\"\n            label=\"用户列表\"\n            :name=\"TabsMap['USER_LIST']\"\n        >\n            <el-scrollbar class=\"ls-scrollbar scrollbar-wrapper\">\n                <loading-more v-model=\"pagination.status\" @load=\"getUserList\">\n                    <chat-user-item\n                        v-for=\"(item, index) in SessionList['user']\"\n                        :key=\"item.id\"\n                        :is-read=\"item.is_read\"\n                        :name=\"item.nickname\"\n                        :avatar=\"item.avatar\"\n                        :message=\"item.msg\"\n                        :active=\"current === item.user_id\"\n                        :time=\"item.update_time\"\n                        :online=\"item.online\"\n                        :msg-type=\"item.msg_type\"\n                        @click.native=\"changeCurrent(item, index)\"\n                    />\n                </loading-more>\n            </el-scrollbar>\n        </el-tab-pane>\n\n        <!-- 群聊列表 -->\n        <el-tab-pane\n            label=\"\"\n            :name=\"TabsMap['GROUP_CHAT']\"\n            disabled\n        ></el-tab-pane>\n    </el-tabs>\n</template>\n\n\n<script>\nimport ChatUserItem from '@/components/ChatUserItem'\nimport { apiChatUserList } from '@/api/app'\nimport LoadingMore from '@/components/LoadingMore'\nimport { E_Load } from '@/utils/enum'\nimport { mapGetters } from 'vuex'\nexport default {\n    name: 'TheWindowAsideLeft',\n\n    components: {\n        ChatUserItem,\n        LoadingMore,\n    },\n\n    props: {\n        current: {\n            type: [String, Number],\n            require: true,\n        },\n    },\n    inject: ['send'],\n    data() {\n        return {\n            /* 搜素内容 */\n            searchValue: '',\n            /* Tabs 索引 */\n            tabsActiveIndex: '',\n            /* Tabs 映射 */\n            TabsMap: Object.freeze({\n                USER_LIST: '1',\n                GROUP_CHAT: '2',\n            }),\n            /* 分页器 */\n            pagination: {\n                size: 15,\n                page: 1,\n                status: E_Load['NORMAL'],\n            },\n\n            /* 会话列表 */\n            SessionList: {\n                user: [],\n                group: [],\n            },\n\n            /* 当前会话 */\n            // sessionCurrent: '',\n        }\n    },\n\n    created() {\n        /* 初始化Tabs选项 */\n        this.tabsActiveIndex = this.TabsMap['USER_LIST']\n        this.$on('useronline', this.useronlineEvent)\n        this.$on('transfer', this.transferEvenr)\n        this.$on('message', this.messageEvenr)\n\n        // this.getUserList()\n    },\n    computed: {\n        ...mapGetters(['shopId']),\n    },\n    methods: {\n        /**\n         * 获取用户列表\n         */\n        getUserList() {\n            return new Promise((resolve, reject) => {\n                const { size, page, status } = this.pagination\n                if (status == E_Load['EMPTY']) return\n                apiChatUserList({\n                    page_no: page,\n                    page_size: size,\n                    nickname: this.searchValue.trim(),\n                })\n                    .then((data) => {\n                        if(!data) return\n                        this.pagination.page++\n                        // 防止列表出现重复的用户\n                        data.list.forEach((item) => {\n                            let index = this.SessionList.user.findIndex(uitem => uitem.user_id ==  item.user_id)\n                            if(index == -1) {\n                                this.SessionList.user.push(item)\n                            }\n                        })\n                        this.pagination.status = E_Load['NORMAL']\n                        this.$nextTick(() => {\n                            if (!data.more) {\n                                this.pagination.status = E_Load['EMPTY']\n                            }\n                        })\n                        resolve(data)\n                    })\n                    .catch((err) => {\n\n                        this.pagination.status = E_Load['NORMAL']\n                        reject(err)\n                    })\n                    .finally(() => {})\n            })\n        },\n        /**\n         * 搜索用户列表\n         */\n        handleSearch() {\n            this.pagination.page = 1\n            this.pagination.status = E_Load['LOAD']\n            this.SessionList.user = []\n            this.getUserList()\n        },\n        /**\n         * 转接事件\n         */\n        transferEvenr(data) {\n            if (data.status == 'get_success') {\n                this.setUser(data.user)\n                this.$notify.success({\n                    title: '转接通知',\n                    message: `您有新的用户`,\n                })\n                return\n            }\n\n            this.SessionList.user = this.SessionList.user.filter((item) => {\n                return item.user_id != this.current\n            })\n            this.$emit('change', '')\n            this.$message.success({\n                message: `转接成功`,\n            })\n        },\n        /**\n         * 用户上下线事件\n         */\n        useronlineEvent(data) {\n            if (data.online) {\n                this.$notify.success({\n                    title: '上线通知',\n                    message: `用户（${data.nickname}）上线`,\n                })\n            }\n            this.setUser(data)\n        },\n        /**\n         * 消息事件\n         */\n        messageEvenr(data) {\n            let index = this.SessionList.user.findIndex((item) => {\n                if(data.from_type == 'user') {\n                    return data.from_id == item.user_id\n                }else if(data.from_type == 'kefu') {\n                    return data.to_id == item.user_id\n                }\n            })\n            if (index != -1) {\n                // 更新用户列表聊天\n                this.$set(this.SessionList.user[index], 'msg_type', data.msg_type)\n                this.$set(this.SessionList.user[index], 'msg', data.msg)\n                this.$set(\n                    this.SessionList.user[index],\n                    'update_time',\n                    data.update_time\n                )\n            }\n\n            if (data.from_type == 'user') {\n                if (data.from_id == this.current) {\n                    this.send('read', {\n                        user_id: this.current,\n                        shop_id: this.shopId,\n                    })\n                    return\n                }\n                this.$set(this.SessionList.user[index], 'is_read', 0)\n            }\n        },\n        /**\n         * 选中用户改变\n         */\n        changeCurrent(item, index) {\n            this.$emit('change', item.user_id)\n            if (!item.is_read) {\n                this.send('read', {\n                    user_id: item.user_id,\n                    shop_id: this.shopId,\n                })\n\n                this.$set(this.SessionList.user[index], 'is_read', 1)\n            }\n        },\n        // 改变用户上下线的状态\n        setUser(data) {\n            let index = this.SessionList.user.findIndex(\n                (item) => data.user_id == item.user_id\n            )\n            if (index != -1) {\n                this.$set(this.SessionList.user[index], 'online', data.online)\n            } else {\n                this.SessionList.user.unshift(data)\n            }\n        },\n    },\n}\n</script>\n\n\n<style lang=\"scss\" scoped>\n.window__aside-left {\n    display: flex;\n    flex-direction: column;\n    height: 100%;\n    overflow: hidden;\n\n    .chat-search {\n        padding: 0 10px 10px 10px;\n    }\n\n    &::v-deep .el-tabs__content {\n        flex: 1;\n        display: flex;\n        flex-direction: column;\n    }\n\n    .chat-user__list {\n        flex: 1;\n        display: flex;\n    }\n\n    .scrollbar-wrapper {\n        flex: 1;\n        height: 630px;\n    }\n}\n</style>\n", "import mod from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./TheWindowAsideLeft.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./TheWindowAsideLeft.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./TheWindowAsideLeft.vue?vue&type=template&id=41c061d2&scoped=true&\"\nimport script from \"./TheWindowAsideLeft.vue?vue&type=script&lang=js&\"\nexport * from \"./TheWindowAsideLeft.vue?vue&type=script&lang=js&\"\nimport style0 from \"./TheWindowAsideLeft.vue?vue&type=style&index=0&id=41c061d2&prod&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"41c061d2\",\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"window__aside-right\"},[_c('el-tabs',{attrs:{\"stretch\":true},model:{value:(_vm.tabsActiveIndex),callback:function ($$v) {_vm.tabsActiveIndex=$$v},expression:\"tabsActiveIndex\"}},[_c('el-tab-pane',{attrs:{\"label\":\"用户资料\",\"name\":_vm.TabsMap['USER']}},[_c('div',{staticClass:\"tab-content\"},[(_vm.userInfo.id)?_c('div',{staticClass:\"user-info\"},[_c('div',{staticClass:\"info-header flex\"},[_c('img',{staticClass:\"avatar m-r-10\",attrs:{\"src\":_vm.userInfo.avatar}}),_c('div',{staticClass:\"name line-2\"},[_vm._v(\" \"+_vm._s(_vm.userInfo.nickname)+\" \")])]),_c('div',{staticClass:\"info-item\"},[_c('span',{staticClass:\"label\"},[_vm._v(\"用户编号：\")]),_c('span',[_vm._v(\" \"+_vm._s(_vm.userInfo.sn))])]),_c('div',{staticClass:\"info-item\"},[_c('span',{staticClass:\"label\"},[_vm._v(\"用户等级：\")]),_c('span',[_vm._v(_vm._s(_vm.userInfo.level_name))])]),_c('div',{staticClass:\"info-item\"},[_c('span',{staticClass:\"label\"},[_vm._v(\"手机号码：\")]),_c('span',[_vm._v(_vm._s(_vm.userInfo.mobile))])]),_c('div',{staticClass:\"info-item\"},[_c('span',{staticClass:\"label\"},[_vm._v(\"累计消费：\")]),_c('span',[_vm._v(_vm._s(_vm.userInfo.total_order_amount))])]),_c('div',{staticClass:\"info-item\"},[_c('span',{staticClass:\"label\"},[_vm._v(\"生日：\")]),_c('span',[_vm._v(_vm._s(_vm.userInfo.birthday))])]),_c('div',{staticClass:\"info-item\"},[_c('span',{staticClass:\"label\"},[_vm._v(\"注册来源：\")]),_c('span',[_vm._v(_vm._s(_vm.userInfo.client_desc))])]),_c('div',{staticClass:\"info-item\"},[_c('span',{staticClass:\"label\"},[_vm._v(\"注册时间：\")]),_c('span',[_vm._v(_vm._s(_vm.userInfo.create_time))])])]):_c('el-empty',{attrs:{\"image-size\":100}})],1)]),_c('el-tab-pane',{attrs:{\"label\":\"订单信息\",\"name\":_vm.TabsMap['ORDER']}},[_c('el-scrollbar',{staticClass:\"ls-scrollbar\",staticStyle:{\"height\":\"670px\"}},[(_vm.toId)?_c('loading-more',{on:{\"load\":_vm.getUserOrder},model:{value:(_vm.status),callback:function ($$v) {_vm.status=$$v},expression:\"status\"}},[_c('div',{staticClass:\"tab-content\"},[_c('div',{staticClass:\"order-info\"},[_c('el-input',{attrs:{\"size\":\"small\",\"placeholder\":\"请输入订单号搜索\"},nativeOn:{\"keyup\":function($event){if(!$event.type.indexOf('key')&&_vm._k($event.keyCode,\"enter\",13,$event.key,\"Enter\"))return null;return _vm.refresh.apply(null, arguments)}},model:{value:(_vm.orderSn),callback:function ($$v) {_vm.orderSn=$$v},expression:\"orderSn\"}}),(_vm.orderLists.length)?_c('div',{staticClass:\"order-list\"},_vm._l((_vm.orderLists),function(item,index){return _c('div',{key:index,staticClass:\"order-item\"},[_vm._l((item.order_goods),function(gitem,gindex){return _c('div',{key:gindex,staticClass:\"order-goods flex col-top\"},[_c('div',{staticClass:\"flex-none\"},[_c('el-image',{staticStyle:{\"width\":\"68px\",\"height\":\"68px\"},attrs:{\"src\":gitem.image}})],1),_c('div',{staticClass:\"flex-1 m-l-8\",staticStyle:{\"min-width\":\"0\"}},[_c('div',{staticClass:\"goods-name line-1\"},[_vm._v(\" \"+_vm._s(gitem.goods_name)+\" \")]),_c('div',{staticClass:\"muted m-t-5\"},[_vm._v(\" \"+_vm._s(gitem.spec_value)+\" \")]),_c('div',{staticClass:\"price flex row-between m-t-5\"},[_c('div',{staticClass:\"nr\"},[_vm._v(\" ￥\"+_vm._s(gitem.goods_price)+\" \")]),_c('div',{staticClass:\"muted\"},[_vm._v(\" x\"+_vm._s(gitem.goods_num)+\" \")])])])])}),_c('div',{staticClass:\"order-con\"},[_c('div',{staticClass:\"m-t-15\"},[_c('span',{staticClass:\"muted\"},[_vm._v(\"订单类型：\")]),_c('span',[_vm._v(_vm._s(item.order_type_text))])]),_c('div',{staticClass:\"m-t-15\"},[_c('span',{staticClass:\"muted\"},[_vm._v(\"订单编号：\")]),_c('span',[_vm._v(_vm._s(item.order_sn))])]),_c('div',{staticClass:\"m-t-15\"},[_c('span',{staticClass:\"muted\"},[_vm._v(\"订单状态：\")]),_c('span',{staticClass:\"order-status\",class:{\n                                                    'wait-pay':\n                                                        item.order_status ==\n                                                        0,\n                                                }},[_vm._v(_vm._s(item.order_status_text))])]),_c('div',{staticClass:\"m-t-15\"},[_c('span',{staticClass:\"muted\"},[_vm._v(\"订单金额：\")]),_c('span',[_vm._v(_vm._s(item.order_amount))])]),_c('div',{staticClass:\"m-t-15\"},[_c('span',{staticClass:\"muted\"},[_vm._v(\"下单时间：\")]),_c('span',[_vm._v(_vm._s(item.create_time))])])])],2)}),0):_vm._e()],1)])]):_c('el-empty',{attrs:{\"image-size\":100}})],1)],1)],1)],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n    <div class=\"window__aside-right\">\n        <!-- Tabs 面板 -->\n        <el-tabs v-model=\"tabsActiveIndex\" :stretch=\"true\">\n            <!-- 用户资料 -->\n            <el-tab-pane label=\"用户资料\" :name=\"TabsMap['USER']\">\n                <div class=\"tab-content\">\n                    <div class=\"user-info\" v-if=\"userInfo.id\">\n                        <div class=\"info-header flex\">\n                            <img class=\"avatar m-r-10\" :src=\"userInfo.avatar\" />\n                            <div class=\"name line-2\">\n                                {{ userInfo.nickname }}\n                            </div>\n                        </div>\n                        <div class=\"info-item\">\n                            <span class=\"label\">用户编号：</span>\n                            <span> {{ userInfo.sn }}</span>\n                        </div>\n                        <div class=\"info-item\">\n                            <span class=\"label\">用户等级：</span>\n                            <span>{{ userInfo.level_name }}</span>\n                        </div>\n                        <div class=\"info-item\">\n                            <span class=\"label\">手机号码：</span>\n                            <span>{{ userInfo.mobile }}</span>\n                        </div>\n                        <div class=\"info-item\">\n                            <span class=\"label\">累计消费：</span>\n                            <span>{{ userInfo.total_order_amount }}</span>\n                        </div>\n                        <div class=\"info-item\">\n                            <span class=\"label\">生日：</span>\n                            <span>{{ userInfo.birthday }}</span>\n                        </div>\n                        <div class=\"info-item\">\n                            <span class=\"label\">注册来源：</span>\n                            <span>{{ userInfo.client_desc }}</span>\n                        </div>\n                        <div class=\"info-item\">\n                            <span class=\"label\">注册时间：</span>\n                            <span>{{ userInfo.create_time }}</span>\n                        </div>\n                    </div>\n                    <el-empty v-else :image-size=\"100\"></el-empty>\n                </div>\n            </el-tab-pane>\n\n            <!-- 订单信息 -->\n            <el-tab-pane label=\"订单信息\" :name=\"TabsMap['ORDER']\">\n                <el-scrollbar class=\"ls-scrollbar\" style=\"height: 670px\">\n                    <loading-more\n                        v-model=\"status\"\n                        @load=\"getUserOrder\"\n                        v-if=\"toId\"\n                    >\n                        <div class=\"tab-content\">\n                            <div class=\"order-info\">\n                                <el-input\n                                    v-model=\"orderSn\"\n                                    size=\"small\"\n                                    placeholder=\"请输入订单号搜索\"\n                                    @keyup.enter.native=\"refresh\"\n                                ></el-input>\n                                <div\n                                    class=\"order-list\"\n                                    v-if=\"orderLists.length\"\n                                >\n                                    <div\n                                        class=\"order-item\"\n                                        v-for=\"(item, index) in orderLists\"\n                                        :key=\"index\"\n                                    >\n                                        <div\n                                            class=\"order-goods flex col-top\"\n                                            v-for=\"(\n                                                gitem, gindex\n                                            ) in item.order_goods\"\n                                            :key=\"gindex\"\n                                        >\n                                            <div class=\"flex-none\">\n                                                <el-image\n                                                    :src=\"gitem.image\"\n                                                    style=\"\n                                                        width: 68px;\n                                                        height: 68px;\n                                                    \"\n                                                ></el-image>\n                                            </div>\n                                            <div\n                                                class=\"flex-1 m-l-8\"\n                                                style=\"min-width: 0\"\n                                            >\n                                                <div class=\"goods-name line-1\">\n                                                    {{ gitem.goods_name }}\n                                                </div>\n                                                <div class=\"muted m-t-5\">\n                                                    {{ gitem.spec_value }}\n                                                </div>\n\n                                                <div\n                                                    class=\"\n                                                        price\n                                                        flex\n                                                        row-between\n                                                        m-t-5\n                                                    \"\n                                                >\n                                                    <div class=\"nr\">\n                                                        ￥{{\n                                                            gitem.goods_price\n                                                        }}\n                                                    </div>\n                                                    <div class=\"muted\">\n                                                        x{{ gitem.goods_num }}\n                                                    </div>\n                                                </div>\n                                            </div>\n                                        </div>\n                                        <div class=\"order-con\">\n                                            <div class=\"m-t-15\">\n                                                <span class=\"muted\"\n                                                    >订单类型：</span\n                                                >\n                                                <span>{{\n                                                    item.order_type_text\n                                                }}</span>\n                                            </div>\n                                            <div class=\"m-t-15\">\n                                                <span class=\"muted\"\n                                                    >订单编号：</span\n                                                >\n                                                <span>{{ item.order_sn }}</span>\n                                            </div>\n                                            <div class=\"m-t-15\">\n                                                <span class=\"muted\"\n                                                    >订单状态：</span\n                                                >\n                                                <span\n                                                    class=\"order-status\"\n                                                    :class=\"{\n                                                        'wait-pay':\n                                                            item.order_status ==\n                                                            0,\n                                                    }\"\n                                                    >{{\n                                                        item.order_status_text\n                                                    }}</span\n                                                >\n                                            </div>\n                                            <div class=\"m-t-15\">\n                                                <span class=\"muted\"\n                                                    >订单金额：</span\n                                                >\n                                                <span>{{\n                                                    item.order_amount\n                                                }}</span>\n                                            </div>\n                                            <div class=\"m-t-15\">\n                                                <span class=\"muted\"\n                                                    >下单时间：</span\n                                                >\n                                                <span>{{\n                                                    item.create_time\n                                                }}</span>\n                                            </div>\n                                        </div>\n                                    </div>\n                                </div>\n                            </div>\n                        </div>\n                    </loading-more>\n                    <el-empty v-else :image-size=\"100\"></el-empty>\n                </el-scrollbar>\n            </el-tab-pane>\n        </el-tabs>\n    </div>\n</template>\n\n\n<script>\nimport { apiUserInfo, apiUserOrder, apiServiceList } from '@/api/app'\nimport LoadingMore from '@/components/LoadingMore'\nimport { E_Load } from '@/utils/enum'\nexport default {\n    name: 'TheWindowAsideRight',\n    components: {\n        LoadingMore,\n    },\n    props: {\n        toId: {\n            type: [Number, String],\n        },\n    },\n\n    inject: ['send'],\n    data() {\n        return {\n            /* Tabs 索引 */\n            tabsActiveIndex: '',\n            /* Tabs 映射 */\n            TabsMap: Object.freeze({\n                USER: '1',\n                ORDER: '2',\n            }),\n            // 订单编号\n            orderSn: '',\n            userInfo: {},\n            orderLists: [],\n\n            showKefu: false,\n            KefuLists: [],\n            page: 1,\n            status: E_Load['NORMAL'],\n        }\n    },\n    watch: {\n        toId(val) {\n            this.page = 1\n            this.status = E_Load['LOAD']\n            this.orderLists = []\n            this.orderSn = ''\n            if (!val) {\n                this.userInfo = {}\n                return\n            }\n            this.getUserInfo()\n            this.getUserOrder()\n        },\n    },\n    methods: {\n        refresh() {\n            this.page = 1\n            this.status = E_Load['LOAD']\n            this.orderLists = []\n            this.getUserOrder()\n        },\n        getUserInfo() {\n            apiUserInfo({\n                user_id: this.toId,\n            }).then((data) => {\n                this.userInfo = data\n            })\n        },\n        getUserOrder() {\n            if (this.status == E_Load['EMPTY']) return\n            apiUserOrder({\n                page_no: this.page,\n                user_id: this.toId,\n                order_sn: this.orderSn,\n            }).then((data) => {\n                this.page++\n                this.orderLists.push(...data.list)\n                this.status = E_Load['NORMAL']\n                this.$nextTick(() => {\n                    if (!data.more) {\n                        this.status = E_Load['EMPTY']\n                    }\n                })\n            })\n        },\n    },\n    created() {\n        /* 初始化Tabs选项 */\n        this.tabsActiveIndex = this.TabsMap['USER']\n    },\n}\n</script>\n\n\n<style lang=\"scss\" scoped>\n.window__aside-right {\n    overflow: hidden;\n    .tab-content {\n        padding: 0 10px;\n        .user-info {\n            .info-header {\n                background: #f5f5f5;\n                padding: 10px;\n                .avatar {\n                    width: 38px;\n                    height: 38px;\n                    border-radius: 50%;\n                }\n            }\n            .info-item {\n                padding: 0 10px;\n                font-size: 12px;\n                margin-top: 16px;\n                .label {\n                    width: 60px;\n                    text-align: right;\n                    color: #666;\n                    display: inline-block;\n                }\n            }\n        }\n        .order-info {\n            .order-list {\n                font-size: 12px;\n                .order-item {\n                    padding: 20px 0;\n                    &:not(:last-of-type) {\n                        border-bottom: 1px solid #f5f5f5;\n                    }\n                    .order-status {\n                        padding: 2px 9px;\n                        border-radius: 12px;\n                        background: #ebf1ff;\n                        color: #4073fa;\n                        &.wait-pay {\n                            color: #ff2c3c;\n                            background: rgba(255, 44, 60, 0.08);\n                        }\n                    }\n                }\n            }\n        }\n    }\n}\n</style>", "import mod from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./TheWindowAsideRight.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./TheWindowAsideRight.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./TheWindowAsideRight.vue?vue&type=template&id=672a51d6&scoped=true&\"\nimport script from \"./TheWindowAsideRight.vue?vue&type=script&lang=js&\"\nexport * from \"./TheWindowAsideRight.vue?vue&type=script&lang=js&\"\nimport style0 from \"./TheWindowAsideRight.vue?vue&type=style&index=0&id=672a51d6&prod&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"672a51d6\",\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('audio',{ref:\"audioRef\",attrs:{\"controls\":false}},[_c('source',{attrs:{\"src\":_vm.src}})])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n    <audio ref=\"audioRef\" :controls=\"false\" >\n        <source :src=\"src\" >\n    </audio>\n</template>\n\n\n<script>\n\nexport default {\n\n    props: {\n        src: {\n            type: [String],\n            require: true,\n            default: require('@/assets/audio/prompt_tone.mp3')\n        },\n    },\n\n    data() {\n      return {\n\n      }\n    },\n\tmethods: {\n        play() {\n            this.$refs.audioRef?.play()\n        }\n    }\n}\n</script>\n\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./index.vue?vue&type=template&id=993e940c&\"\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "/**\n * Socket\n */\n\nimport {objectToQuery} from '@/utils/util'\n\nclass Socket {\n    constructor({ws, params, ...event}) {\n        this.ws = ws + `?${objectToQuery(params)}`\n        this.serve = null\n        this.event = event\n        this.reconnectLock = true\n\t\tthis.reconnectTimeout = null\n\t\tthis.reconnectNums = 0\n\t\t// 心跳\n\t\tthis.timeout = 10000\n\t\tthis.clientTimeout = null\n\t\tthis.serverTimeout = null\n        this.init()\n    }\n\n    init() {\n        if (this.ws) {\n            this.serve = new WebSocket(this.ws)\n            /* 钩子方法 */\n            this.serve.onopen = this.onOpen.bind(this)\n            this.serve.onerror = this.onError.bind(this)\n            this.serve.onmessage = this.onMessage.bind(this)\n            this.serve.onclose = this.onClose.bind(this)\n        }\n    }\n\n    onOpen() {\n        console.log('Open')\n        this.start()\n        this.event.open && this.event.open()\n    }\n\n    onError(e) {\n        console.log('Error')\n        this.event.error && this.event.error(e)\n    }\n\n    onMessage(data) {\n        console.log('Message')\n        this.reset()\n        this.event.message && this.event.message(data)\n    }\n\n    onClose() {\n        console.log('Close')\n        this.reconnect()\n        this.event.close && this.event.close()\n    }\n\n\n    send(data) {\n        this.serve.send(JSON.stringify(data))\n    }\n\n    reset() {\n\t\tthis.reconnectNums = 0\n\t\tthis.start()\n\t}\n\n\n    reconnect() {\n\t\tif (!this.reconnectLock) {\n\t\t\treturn\n\t\t}\n\t\tconsole.log(this.reconnectNums)\n\t\t// 重连次数过多，断开不重连\n\t\tif (this.reconnectNums >= 5) {\n\t\t\treturn\n\t\t}\n\n\t\tthis.reconnectNums++\n\t\tthis.reconnectLock = false\n\t\t// 延迟重连请求过多\n\t\tclearTimeout(this.reconnectTimeout)\n\t\tthis.reconnectTimeout = setTimeout(() => {\n\t\t\tthis.init()\n\t\t\tthis.reconnectLock = true\n\t\t}, 4000)\n\t}\n\tstart() {\n\t\tclearTimeout(this.clientTimeout)\n\t\tclearTimeout(this.serverTimeout)\n\t\tthis.clientTimeout = setTimeout(() => {\n\t\t\tthis.send({\n\t\t\t\tevent: 'ping'\n\t\t\t})\n\t\t\tthis.serverTimeout = setTimeout(() => {\n\t\t\t\tthis.serve.close()\n\t\t\t}, this.timeout)\n\t\t}, this.timeout)\n\t}\n    close() {\n\t\tthis.reconnectLock = false\n\t\tclearTimeout(this.clientTimeout)\n\t\tclearTimeout(this.serverTimeout)\n\t\tthis.serve.close && this.serve.close()\n\t}\n}\n\nexport default Socket\n", "<template>\n    <div class=\"window-contain\">\n        <div class=\"window\">\n            <!-- Header -->\n            <header class=\"window-header\">\n                <the-window-header :isStatus=\"isStatus\"></the-window-header>\n            </header>\n\n            <section class=\"window-content\">\n                <!-- Aside Left -->\n                <aside class=\"window-aside--left\">\n                    <the-window-aside-left\n                        :current=\"sessionID\"\n                        @change=\"changeSession\"\n                        ref=\"user\"\n                    ></the-window-aside-left>\n                </aside>\n\n                <!-- Main -->\n                <main class=\"window-main\">\n                    <the-window-main\n                        :to-id=\"sessionID\"\n                        :from-id=\"userInfo.id\"\n                        ref=\"chat\"\n                    ></the-window-main>\n                </main>\n\n                <!-- Aside Right -->\n                <aside class=\"window-aside--right\">\n                    <the-window-aside-right :to-id=\"sessionID\"></the-window-aside-right>\n                </aside>\n            </section>\n        </div>\n        <PromptTone ref=\"promptToneRef\" />\n    </div>\n</template>\n\n\n<script>\nimport TheWindowHeader from './components/TheWindowHeader'\nimport TheWindowMain from './components/TheWindowMain'\nimport TheWindowAsideLeft from './components/TheWindowAsideLeft'\nimport TheWindowAsideRight from './components/TheWindowAsideRight'\nimport PromptTone from '@/components/PromptTone/index'\n\nimport Socket from '@/utils/socket'\nimport config from '@/config'\nimport { E_Msg, E_MsgEvent } from '@/utils/enum'\nimport { mapGetters, mapActions } from 'vuex'\n\nexport default {\n    name: 'Window',\n\n    components: {\n        TheWindowHeader,\n        TheWindowAsideLeft,\n        TheWindowAsideRight,\n        TheWindowMain,\n        PromptTone\n    },\n\n    data() {\n        return {\n            sessionID: '',\n\t\t\tisStatus: false,\n        }\n    },\n\n    computed: {\n        ...mapGetters(['token', 'shopId', 'userInfo', 'wsUrl']),\n    },\n\n    provide() {\n        return {\n            sendMessage: this.sendMessage,\n            send: this.send,\n\t\t\tcloseChatServe: this.closeChatServe,\n\t\t\treChatServe: this.reChatServe,\n        }\n    },\n\n    async created() {\n\t\t\n\t\t\n        await this.getUserInfo()\n        await this.initChatServe()\n    },\n    beforeDestroy() {\n        this.closeChatServe()\n    },\n    methods: {\n        ...mapActions(['getUserInfo']),\n\n        /**\n         * 发送消息\n         */\n        sendMessage(data) {\n            this.send(E_MsgEvent['CHAT'], {\n                to_type: 'user',\n                ...data,\n            })\n        },\n\n        /**\n         * 通用发送\n         */\n        send(event, data) {\n            this.socketServe.send({\n                event,\n                data: data,\n            })\n        },\n\t\t\n\t\t// 关闭连接\n\t\tcloseChatServe() {\n\t\t\tthis.socketServe.close()\n\t\t},\n\t\t\n\t\t// 重新连接\n\t\treChatServe() {\n\t\t\tthis.socketServe.init()\n\t\t},\n\n        /**\n         * 初始化聊天服务\n         */\n        initChatServe() {\n            return new Promise((resolve, reject) => {\n                const _this = this\n                this.socketServe = new Socket({\n                    ws: this.wsUrl,\n\n                    params: {\n                        token: this.token,\n                        type: 'kefu',\n                        client: 5,\n                        shop_id: this.shopId,\n                    },\n\n                    open() {\n\t\t\t\t\t\t_this.isStatus = true\n                        resolve()\n                    },\n\n                    message({ data }) {\n                        const {event, data: content} = JSON.parse(data) || {}\n                        \n                        switch(event) {\n                            case E_MsgEvent['CHAT']:\n                                _this.$refs['chat'].$emit('message', content)\n                                _this.$refs['user'].$emit('message', content)\n                                _this.$refs.promptToneRef?.play()\n                                break\n                            case E_MsgEvent['ERROR']:\n                                _this.$message.error(content.msg)\n                                break\n                            case E_MsgEvent['NOTICE']:\n                                _this.$message.info(content.msg)\n                                break\n                            case E_MsgEvent['PING']:\n                                console.log('===============心跳============')\n                                break\n                            case E_MsgEvent['USER_ONLINE']:\n                                _this.$refs['user'].$emit('useronline', content)\n                                break\n                             case E_MsgEvent['TRANSFER']:\n                            _this.$refs['user'].$emit('transfer', content)\n                                break\n                                \n                        }\n                    },\n\n                    error(e) {\n                        reject()\n                    },\n\n                    close() {\n\t\t\t\t\t\t_this.isStatus = false\n\t\t\t\t\t},\n                })\n            })\n        },\n\n        /**\n         * 切换会话\n         */\n        changeSession(userID) {\n            console.log(userID)\n            this.sessionID = userID\n        },\n    }\n}\n</script>\n\n\n<style lang=\"scss\" scoped>\n.window-contain {\n    $--window-width: 1200px;\n    $--window-height: 800px;\n\n    $--window-header-height: 60px;\n    $--window-aside-l-width: 240px;\n    $--window-aside-r-width: 240px;\n\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    padding-top: 50px;\n    width: 100vw;\n    min-height: 800px;\n    min-width: 1200px;\n    .window {\n        display: flex;\n        flex-direction: column;\n        width: $--window-width;\n        height: $--window-height;\n        background-color: #ffffff;\n\n        &-header {\n            height: $--window-header-height;\n        }\n\n        &-content {\n            flex: 1;\n            display: flex;\n            flex-direction: row;\n\n            .window-aside {\n                &--left {\n                    width: $--window-aside-l-width;\n                    border-right: 1px solid $--border-color-base;\n                }\n\n                &--right {\n                    width: $--window-aside-r-width;\n                    border-left: 1px solid $--border-color-base;\n                }\n            }\n\n            .window-main {\n                flex: 1;\n            }\n        }\n    }\n}\n</style>\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./index.vue?vue&type=template&id=28dad484&scoped=true&\"\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=28dad484&prod&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"28dad484\",\n  null\n  \n)\n\nexport default component.exports", "export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=style&index=0&id=82d53720&prod&lang=scss&scoped=true&\"", "'use strict';\nvar $ = require('../internals/export');\nvar toObject = require('../internals/to-object');\nvar lengthOfArrayLike = require('../internals/length-of-array-like');\nvar setArrayLength = require('../internals/array-set-length');\nvar deletePropertyOrThrow = require('../internals/delete-property-or-throw');\nvar doesNotExceedSafeInteger = require('../internals/does-not-exceed-safe-integer');\n\n// IE8-\nvar INCORRECT_RESULT = [].unshift(0) !== 1;\n\n// V8 ~ Chrome < 71 and Safari <= 15.4, FF < 23 throws InternalError\nvar properErrorOnNonWritableLength = function () {\n  try {\n    // eslint-disable-next-line es/no-object-defineproperty -- safe\n    Object.defineProperty([], 'length', { writable: false }).unshift();\n  } catch (error) {\n    return error instanceof TypeError;\n  }\n};\n\nvar FORCED = INCORRECT_RESULT || !properErrorOnNonWritableLength();\n\n// `Array.prototype.unshift` method\n// https://tc39.es/ecma262/#sec-array.prototype.unshift\n$({ target: 'Array', proto: true, arity: 1, forced: FORCED }, {\n  // eslint-disable-next-line no-unused-vars -- required for `.length`\n  unshift: function unshift(item) {\n    var O = toObject(this);\n    var len = lengthOfArrayLike(O);\n    var argCount = arguments.length;\n    if (argCount) {\n      doesNotExceedSafeInteger(len + argCount);\n      var k = len;\n      while (k--) {\n        var to = k + argCount;\n        if (k in O) O[to] = O[k];\n        else deletePropertyOrThrow(O, to);\n      }\n      for (var j = 0; j < argCount; j++) {\n        O[j] = arguments[j];\n      }\n    } return setArrayLength(O, len + argCount);\n  }\n});\n", "export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=style&index=0&id=28dad484&prod&lang=scss&scoped=true&\"", "export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=style&index=0&id=557db40e&prod&lang=scss&scoped=true&\"", "export * from \"-!../../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./TheWindowHeader.vue?vue&type=style&index=0&id=1e1d3a65&prod&lang=scss&scoped=true&\"", "export * from \"-!../../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./TheWindowAsideRight.vue?vue&type=style&index=0&id=672a51d6&prod&lang=scss&scoped=true&\"", "module.exports = \"data:image/png;base64,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\"", "export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=style&index=0&id=83579a84&prod&lang=scss&scoped=true&\"", "export * from \"-!../../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./TheWindowAsideLeft.vue?vue&type=style&index=0&id=41c061d2&prod&lang=scss&scoped=true&\"", "export * from \"-!../../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./TheWindowMain.vue?vue&type=style&index=0&id=845fab14&prod&lang=scss&scoped=true&\"", "export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=style&index=0&id=326917c9&prod&lang=scss&scoped=true&\"", "export * from \"-!../../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./QuickReply.vue?vue&type=style&index=0&id=394c9cca&prod&lang=scss&scoped=true&\"", "module.exports = __webpack_public_path__ + \"media/prompt_tone.7cb9a9a5.mp3\";"], "sourceRoot": ""}