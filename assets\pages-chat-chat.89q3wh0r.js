var t,e,o,s,i,a;import{o as n,c as l,w as r,b as c,t as u,h as d,a as h,n as p,d as m,N as f,q as y,i as g,P as v,Q as b,f as _,j as k,F as C,k as w,I as x,S,J as $,K as I,B as T,A as z,R as j,T as A,r as B,U as P,V as N,W as D,X as V,Y as F,Z as O,_ as R,a0 as H}from"./index-B6kWyIrN.js";import{_ as E,r as Y,d as X,o as U}from"./uv-icon.D6fiO-QB.js";import{_ as K}from"./z-paging.X4eWfR5C.js";const M=E({__name:"chat-item",props:{item:{type:Object,default:()=>({time:"",icon:"",name:"",content:"",isMe:!1})}},setup:t=>(e,o)=>{const s=f,i=y,a=g;return n(),l(a,{class:"chat-item"},{default:r((()=>[t.item.time&&t.item.time.length?(n(),l(s,{key:0,class:"chat-time"},{default:r((()=>[c(u(t.item.time),1)])),_:1})):d("",!0),h(a,{class:p({"chat-container":!0,"chat-location-me":"kefu"!=t.item.to_type})},{default:r((()=>[h(a,{class:"chat-icon-container"},{default:r((()=>[h(i,{class:"chat-icon",src:t.item.from_avatar,mode:"aspectFill"},null,8,["src"])])),_:1}),h(a,{class:"chat-content-container"},{default:r((()=>[h(s,{class:p({"chat-user-name":!0,"chat-location-me":"kefu"!=t.item.to_type})},{default:r((()=>[c(u(t.item.from_nickname),1)])),_:1},8,["class"]),h(a,{class:"chat-text-container-super",style:m([{justifyContent:"kefu"==t.item.to_type?"flex-end":"flex-start"}])},{default:r((()=>[1==t.item.msg_type?(n(),l(a,{key:0,class:p({"chat-text-container":!0,"chat-text-container-me":"kefu"!=t.item.to_type})},{default:r((()=>[h(s,{class:p({"chat-text":!0,"chat-text-me":"kefu"!=t.item.to_type})},{default:r((()=>[c(u(t.item.msg),1)])),_:1},8,["class"])])),_:1},8,["class"])):d("",!0),2==t.item.msg_type?(n(),l(i,{key:1,src:t.item.msg,class:"margin-top-10",style:{width:"400rpx","max-height":"300rpx"},mode:"aspectFill"},null,8,["src"])):d("",!0)])),_:1},8,["style"])])),_:1})])),_:1},8,["class"])])),_:1})}},[["__scopeId","data-v-580f068b"]]);const L=E({name:"chat-input-bar",data:()=>({msg:"",emojisArr:["😊","😁","😀","😃","😣","😞","😩","😫","😲","😟","😦","😜","😳","😋","😥","😰","🤠","😎","😇","😉","😭","😈","😕","😏","😘","😤","😡","😅","😬","😺","😻","😽","😼","🙈","🙉","🙊","🔥","👍","👎","👌","✌️","🙏","💪","👻"],focus:!1,emojiType:""}),methods:{chooseImage(){let t=this;v({count:9,sizeType:["original","compressed"],success:async function(e){t.$emit("chooseImage",e)}})},updateKeyboardHeightChange(t){t.height>0&&(this.emojiType="emoji")},hidedKeyboard(){"keyboard"===this.emojiType&&(this.emojiType="")},emojiChange(){this.$emit("emojiTypeChange",this.emojiType),"keyboard"===this.emojiType?this.focus=!0:(this.focus=!1,b()),this.emojiType=this.emojiType&&"emoji"!==this.emojiType?"emoji":"keyboard"},emojiClick(t){this.msg+=t},sendClick(){this.msg.length&&(this.$emit("send",this.msg,1),this.msg="")}}},[["render",function(t,e,o,s,i,a){const d=Y(_("uv-icon"),X),p=g,v=x,b=y,$=f,I=S;return n(),l(p,{class:"chat-input-bar-container"},{default:r((()=>[h(p,{class:"chat-input-bar"},{default:r((()=>[h(p,{onClick:e[0]||(e[0]=t=>a.chooseImage()),class:"margin-right-20"},{default:r((()=>[h(d,{name:"photo",size:"30"})])),_:1}),h(p,{class:"chat-input-container"},{default:r((()=>[h(v,{focus:i.focus,class:"chat-input",modelValue:i.msg,"onUpdate:modelValue":e[1]||(e[1]=t=>i.msg=t),"adjust-position":!1,"confirm-type":"send",type:"text",placeholder:"请输入内容",onConfirm:a.sendClick},null,8,["focus","modelValue","onConfirm"])])),_:1}),h(p,{class:"emoji-container"},{default:r((()=>[h(b,{class:"emoji-img",src:`/static/${i.emojiType||"emoji"}.png`,onClick:a.emojiChange},null,8,["src","onClick"])])),_:1}),h(p,{class:"chat-input-send",onClick:a.sendClick},{default:r((()=>[h($,{class:"chat-input-send-text"},{default:r((()=>[c("发送")])),_:1})])),_:1},8,["onClick"])])),_:1}),h(p,{class:"emoji-panel-container",style:m([{height:"keyboard"===i.emojiType?"400rpx":"0px"}])},{default:r((()=>[h(I,{"scroll-y":"",style:{height:"100%",flex:"1"}},{default:r((()=>[h(p,{class:"emoji-panel"},{default:r((()=>[(n(!0),k(C,null,w(i.emojisArr,((t,e)=>(n(),l($,{class:"emoji-panel-text",key:e,onClick:e=>a.emojiClick(t)},{default:r((()=>[c(u(t),1)])),_:2},1032,["onClick"])))),128))])),_:1})])),_:1})])),_:1},8,["style"])])),_:1})}],["__scopeId","data-v-be13e2c9"]]);const Z=E({name:"uv-search",emits:["click","input","change","clear","search","custom","focus","blur","clickIcon","update:modelValue"],mixins:[$,I,{props:{value:{type:[String,Number],default:""},modelValue:{type:[String,Number],default:""},shape:{type:String,default:"round"},bgColor:{type:String,default:"#f2f2f2"},placeholder:{type:String,default:"请输入关键字"},clearabled:{type:Boolean,default:!0},focus:{type:Boolean,default:!1},showAction:{type:Boolean,default:!0},actionStyle:{type:Object,default:()=>({})},actionText:{type:String,default:"搜索"},inputAlign:{type:String,default:"left"},inputStyle:{type:Object,default:()=>({})},disabled:{type:Boolean,default:!1},borderColor:{type:String,default:"transparent"},searchIconColor:{type:String,default:"#909399"},color:{type:String,default:"#606266"},placeholderColor:{type:String,default:"#909399"},searchIcon:{type:String,default:"search"},searchIconSize:{type:[Number,String],default:22},margin:{type:String,default:"0"},animation:{type:Boolean,default:!1},maxlength:{type:[String,Number],default:-1},height:{type:[String,Number],default:32},label:{type:[String,Number,null],default:null},boxStyle:{type:[String,Object],default:()=>({})},...null==(e=null==(t=uni.$uv)?void 0:t.props)?void 0:e.search}}],data(){return{keyword:"",showClear:!1,show:!1,focused:this.focus}},created(){this.keyword=this.modelValue},watch:{value(t){this.keyword=t},modelValue(t){this.keyword=t}},computed:{showActionBtn(){return!this.animation&&this.showAction}},methods:{keywordChange(){this.$emit("input",this.keyword),this.$emit("update:modelValue",this.keyword),this.$emit("change",this.keyword)},inputChange(t){this.keyword=t.detail.value,this.keywordChange()},clear(){this.keyword="",this.$nextTick((()=>{this.$emit("clear")})),this.keywordChange()},search(t){this.$emit("search",t.detail.value);try{b()}catch(e){}},custom(){this.$emit("custom",this.keyword);try{b()}catch(t){}},getFocus(){this.focused=!0,this.animation&&this.showAction&&(this.show=!0),this.$emit("focus",this.keyword)},blur(){setTimeout((()=>{this.focused=!1}),100),this.show=!1,this.$emit("blur",this.keyword)},clickHandler(){this.disabled&&this.$emit("click")},clickIcon(){this.$emit("clickIcon")}}},[["render",function(t,e,o,s,i,a){const y=g,v=Y(_("uv-icon"),X),b=x,k=f;return n(),l(y,{class:"uv-search",onClick:a.clickHandler,style:m([{margin:t.margin},t.$uv.addStyle(t.customStyle)])},{default:r((()=>[h(y,{class:"uv-search__content",style:m([{backgroundColor:t.bgColor,borderRadius:"round"==t.shape?"100px":"4px",borderColor:t.borderColor},t.$uv.addStyle(t.boxStyle)])},{default:r((()=>[t.disabled?(n(),l(y,{key:0,class:"uv-search__content__disabled"})):d("",!0),T(t.$slots,"prefix",{},(()=>[h(y,{class:"uv-search__content__icon"},{default:r((()=>[h(v,{onClick:a.clickIcon,size:t.searchIconSize,name:t.searchIcon,color:t.searchIconColor?t.searchIconColor:t.color},null,8,["onClick","size","name","color"])])),_:1})]),!0),h(b,{"confirm-type":"search",onBlur:a.blur,value:i.keyword,onConfirm:a.search,onInput:a.inputChange,disabled:t.disabled,onFocus:a.getFocus,focus:t.focus,maxlength:t.maxlength,"placeholder-class":"uv-search__content__input--placeholder",placeholder:t.placeholder,"placeholder-style":`color: ${t.placeholderColor}`,class:"uv-search__content__input",type:"text",style:m([{textAlign:t.inputAlign,color:t.color,backgroundColor:t.bgColor,height:t.$uv.addUnit(t.height)},t.inputStyle])},null,8,["onBlur","value","onConfirm","onInput","disabled","onFocus","focus","maxlength","placeholder","placeholder-style","style"]),i.keyword&&t.clearabled&&i.focused?(n(),l(y,{key:1,class:"uv-search__content__icon uv-search__content__close",onClick:a.clear},{default:r((()=>[h(v,{name:"close",size:"11",color:"#ffffff",customStyle:"line-height: 12px"})])),_:1},8,["onClick"])):d("",!0),T(t.$slots,"suffix",{},void 0,!0)])),_:3},8,["style"]),h(k,{style:m([t.actionStyle]),class:p(["uv-search__action",[(a.showActionBtn||i.show)&&"uv-search__action--active"]]),onClick:z(a.custom,["stop","prevent"])},{default:r((()=>[c(u(t.actionText),1)])),_:1},8,["style","class","onClick"])])),_:3},8,["onClick","style"])}],["__scopeId","data-v-b3baced2"]]);class Q{constructor(t,e){this.options=t,this.animation=j({...t}),this.currentStepAnimates={},this.next=0,this.$=e}_nvuePushAnimates(t,e){let o=this.currentStepAnimates[this.next],s={};if(s=o||{styles:{},config:{}},W.includes(t)){s.styles.transform||(s.styles.transform="");let o="";"rotate"===t&&(o="deg"),s.styles.transform+=`${t}(${e+o}) `}else s.styles[t]=`${e}`;this.currentStepAnimates[this.next]=s}_animateRun(t={},e={}){let o=this.$.$refs.ani.ref;if(o)return new Promise(((s,i)=>{nvueAnimation.transition(o,{styles:t,...e},(t=>{s()}))}))}_nvueNextAnimate(t,e=0,o){let s=t[e];if(s){let{styles:i,config:a}=s;this._animateRun(i,a).then((()=>{e+=1,this._nvueNextAnimate(t,e,o)}))}else this.currentStepAnimates={},"function"==typeof o&&o(),this.isEnd=!0}step(t={}){return this.animation.step(t),this}run(t){this.$.animationData=this.animation.export(),this.$.timer=setTimeout((()=>{"function"==typeof t&&t()}),this.$.durationTime)}}const W=["matrix","matrix3d","rotate","rotate3d","rotateX","rotateY","rotateZ","scale","scale3d","scaleX","scaleY","scaleZ","skew","skewX","skewY","translate","translate3d","translateX","translateY","translateZ"];function q(t,e){if(e)return clearTimeout(e.timer),new Q(t,e)}W.concat(["opacity","backgroundColor"],["width","height","left","right","top","bottom"]).forEach((t=>{Q.prototype[t]=function(...e){return this.animation[t](...e),this}}));const J=E({name:"uv-transition",mixins:[$,I],emits:["click","change"],props:{show:{type:Boolean,default:!1},mode:{type:[Array,String,null],default:()=>"fade"},duration:{type:[String,Number],default:300},timingFunction:{type:String,default:"ease-out"},customClass:{type:String,default:""},cellChild:{type:Boolean,default:!1}},data:()=>({isShow:!1,transform:"",opacity:1,animationData:{},durationTime:300,config:{}}),watch:{show:{handler(t){t?this.open():this.isShow&&this.close()},immediate:!0}},computed:{transformStyles(){const t={transform:this.transform,opacity:this.opacity,...this.$uv.addStyle(this.customStyle),"transition-duration":this.duration/1e3+"s"};return this.$uv.addStyle(t,"string")}},created(){this.config={duration:this.duration,timingFunction:this.timingFunction,transformOrigin:"50% 50%",delay:0},this.durationTime=this.duration},methods:{init(t={}){t.duration&&(this.durationTime=t.duration),this.animation=q(Object.assign(this.config,t),this)},onClick(){this.$emit("click",{detail:this.isShow})},step(t,e={}){if(this.animation){for(let e in t)try{"object"==typeof t[e]?this.animation[e](...t[e]):this.animation[e](t[e])}catch(o){console.error(`方法 ${e} 不存在`)}return this.animation.step(e),this}},run(t){this.animation&&this.animation.run(t)},open(){clearTimeout(this.timer),this.transform="",this.isShow=!0;let{opacity:t,transform:e}=this.styleInit(!1);void 0!==t&&(this.opacity=t),this.transform=e,this.$nextTick((()=>{this.timer=setTimeout((()=>{this.animation=q(this.config,this),this.tranfromInit(!1).step(),this.animation.run(),this.opacity=1,this.$emit("change",{detail:this.isShow}),this.transform=""}),20)}))},close(t){this.animation&&this.tranfromInit(!0).step().run((()=>{this.isShow=!1,this.animationData=null,this.animation=null;let{opacity:t,transform:e}=this.styleInit(!1);this.opacity=t||1,this.transform=e,this.$emit("change",{detail:this.isShow})}))},styleInit(t){let e={transform:""},o=(t,o)=>{"fade"===o?e.opacity=this.animationType(t)[o]:e.transform+=this.animationType(t)[o]+" "};return"string"==typeof this.mode?o(t,this.mode):this.mode.forEach((e=>{o(t,e)})),e},tranfromInit(t){let e=(t,e)=>{let o=null;"fade"===e?o=t?0:1:(o=t?"-100%":"0","zoom-in"===e&&(o=t?.8:1),"zoom-out"===e&&(o=t?1.2:1),"slide-right"===e&&(o=t?"100%":"0"),"slide-bottom"===e&&(o=t?"100%":"0")),this.animation[this.animationMode()[e]](o)};return"string"==typeof this.mode?e(t,this.mode):this.mode.forEach((o=>{e(t,o)})),this.animation},animationType:t=>({fade:t?1:0,"slide-top":`translateY(${t?"0":"-100%"})`,"slide-right":`translateX(${t?"0":"100%"})`,"slide-bottom":`translateY(${t?"0":"100%"})`,"slide-left":`translateX(${t?"0":"-100%"})`,"zoom-in":`scaleX(${t?1:.8}) scaleY(${t?1:.8})`,"zoom-out":`scaleX(${t?1:1.2}) scaleY(${t?1:1.2})`}),animationMode:()=>({fade:"opacity","slide-top":"translateY","slide-right":"translateX","slide-bottom":"translateY","slide-left":"translateX","zoom-in":"scale","zoom-out":"scale"}),toLine:t=>t.replace(/([A-Z])/g,"-$1").toLowerCase()}},[["render",function(t,e,o,s,i,a){const c=g;return i.isShow?(n(),l(c,{key:0,ref:"ani",animation:i.animationData,class:p(o.customClass),style:m(a.transformStyles),onClick:a.onClick},{default:r((()=>[T(t.$slots,"default")])),_:3},8,["animation","class","style","onClick"])):d("",!0)}]]);const G=E({name:"uv-overlay",emits:["click"],mixins:[$,I,{props:{show:{type:Boolean,default:!1},zIndex:{type:[String,Number],default:10070},duration:{type:[String,Number],default:300},opacity:{type:[String,Number],default:.5},...null==(s=null==(o=uni.$uv)?void 0:o.props)?void 0:s.overlay}}],watch:{show(t){document.querySelector("body").style.overflow=t?"hidden":""}},computed:{overlayStyle(){const t={position:"fixed",top:0,left:0,right:0,zIndex:this.zIndex,bottom:0,"background-color":`rgba(0, 0, 0, ${this.opacity})`};return this.$uv.deepMerge(t,this.$uv.addStyle(this.customStyle))}},methods:{clickHandler(){this.$emit("click")},clear(){}}},[["render",function(t,e,o,s,i,a){const c=Y(_("uv-transition"),J);return n(),l(c,{show:t.show,mode:"fade","custom-class":"uv-overlay",duration:t.duration,"custom-style":a.overlayStyle,onClick:a.clickHandler,onTouchmove:z(a.clear,["stop","prevent"])},{default:r((()=>[T(t.$slots,"default",{},void 0,!0)])),_:3},8,["show","duration","custom-style","onClick","onTouchmove"])}],["__scopeId","data-v-9cc2c1b3"]]);const tt=E({name:"uv-status-bar",mixins:[$,I,{props:{bgColor:{type:String,default:"transparent"}}}],data:()=>({}),computed:{style(){const t={};return t.height=this.$uv.addUnit(this.$uv.sys().statusBarHeight,"px"),this.bgColor&&(this.bgColor.indexOf("gradient")>-1?t.backgroundImage=this.bgColor:t.background=this.bgColor),this.$uv.deepMerge(t,this.$uv.addStyle(this.customStyle))}}},[["render",function(t,e,o,s,i,a){const c=g;return n(),l(c,{style:m([a.style]),class:"uv-status-bar"},{default:r((()=>[T(t.$slots,"default",{},void 0,!0)])),_:3},8,["style"])}],["__scopeId","data-v-08fe2518"]]);const et=E({name:"uv-safe-bottom",mixins:[$,I],data:()=>({safeAreaBottomHeight:0,isNvue:!1}),computed:{style(){return this.$uv.deepMerge({},this.$uv.addStyle(this.customStyle))}},mounted(){}},[["render",function(t,e,o,s,i,a){const r=g;return n(),l(r,{class:p(["uv-safe-bottom",[!i.isNvue&&"uv-safe-area-inset-bottom"]]),style:m([a.style])},null,8,["style","class"])}],["__scopeId","data-v-8065622b"]]);const ot=E({name:"uv-popup",components:{keypress:{name:"Keypress",props:{disable:{type:Boolean,default:!1}},mounted(){const t={esc:["Esc","Escape"],tab:"Tab",enter:"Enter",space:[" ","Spacebar"],up:["Up","ArrowUp"],left:["Left","ArrowLeft"],right:["Right","ArrowRight"],down:["Down","ArrowDown"],delete:["Backspace","Delete","Del"]};document.addEventListener("keyup",(e=>{if(this.disable)return;const o=Object.keys(t).find((o=>{const s=e.key,i=t[o];return i===s||Array.isArray(i)&&i.includes(s)}));o&&setTimeout((()=>{this.$emit(o,{})}),0)}))},render:()=>{}}},mixins:[$,I],emits:["change","maskClick"],props:{mode:{type:String,default:"center"},duration:{type:[String,Number],default:300},zIndex:{type:[String,Number],default:997},bgColor:{type:String,default:"#ffffff"},safeArea:{type:Boolean,default:!0},overlay:{type:Boolean,default:!0},closeOnClickOverlay:{type:Boolean,default:!0},overlayOpacity:{type:[Number,String],default:.4},overlayStyle:{type:[Object,String],default:""},safeAreaInsetBottom:{type:Boolean,default:!0},safeAreaInsetTop:{type:Boolean,default:!1},closeable:{type:Boolean,default:!1},closeIconPos:{type:String,default:"top-right"},zoom:{type:Boolean,default:!0},round:{type:[Number,String],default:0},...null==(a=null==(i=uni.$uv)?void 0:i.props)?void 0:a.popup},watch:{type:{handler:function(t){this.config[t]&&this[this.config[t]](!0)},immediate:!0},isDesktop:{handler:function(t){this.config[t]&&this[this.config[this.mode]](!0)},immediate:!0},showPopup(t){document.getElementsByTagName("body")[0].style.overflow=t?"hidden":"visible"}},data(){return{ani:[],showPopup:!1,showTrans:!1,popupWidth:0,popupHeight:0,config:{top:"top",bottom:"bottom",center:"center",left:"left",right:"right",message:"top",dialog:"center",share:"bottom"},transitionStyle:{position:"fixed",left:0,right:0},maskShow:!0,mkclick:!0,popupClass:this.isDesktop?"fixforpc-top":"top",direction:""}},computed:{isDesktop(){return this.popupWidth>=500&&this.popupHeight>=500},bg(){return""===this.bgColor||"none"===this.bgColor||this.$uv.getPx(this.round)>0?"transparent":this.bgColor},contentStyle(){const t={};if(this.bgColor&&(t.backgroundColor=this.bg),this.round){const e=this.$uv.addUnit(this.round),o=this.direction?this.direction:this.mode;t.backgroundColor=this.bgColor,"top"===o?(t.borderBottomLeftRadius=e,t.borderBottomRightRadius=e):"bottom"===o?(t.borderTopLeftRadius=e,t.borderTopRightRadius=e):"center"===o&&(t.borderRadius=e)}return this.$uv.deepMerge(t,this.$uv.addStyle(this.customStyle))}},unmounted(){this.setH5Visible()},created(){this.messageChild=null,this.clearPropagation=!1},methods:{setH5Visible(){document.getElementsByTagName("body")[0].style.overflow="visible"},closeMask(){this.maskShow=!1},clear(t){t.stopPropagation(),this.clearPropagation=!0},open(t){if(this.showPopup)return;if(t&&-1!==["top","center","bottom","left","right","message","dialog","share"].indexOf(t)?this.direction=t:t=this.mode,!this.config[t])return this.$uv.error(`缺少类型：${t}`);this[this.config[t]](),this.$emit("change",{show:!0,type:t})},close(t){this.showTrans=!1,this.$emit("change",{show:!1,type:this.mode}),clearTimeout(this.timer),this.timer=setTimeout((()=>{this.showPopup=!1}),300)},touchstart(){this.clearPropagation=!1},onTap(){this.clearPropagation?this.clearPropagation=!1:(this.$emit("maskClick"),this.closeOnClickOverlay&&this.close())},top(t){this.popupClass=this.isDesktop?"fixforpc-top":"top",this.ani=["slide-top"],this.transitionStyle={position:"fixed",zIndex:this.zIndex,left:0,right:0,backgroundColor:this.bg},t||(this.showPopup=!0,this.showTrans=!0,this.$nextTick((()=>{this.messageChild&&"message"===this.mode&&this.messageChild.timerClose()})))},bottom(t){this.popupClass="bottom",this.ani=["slide-bottom"],this.transitionStyle={position:"fixed",zIndex:this.zIndex,left:0,right:0,bottom:0,backgroundColor:this.bg},t||(this.showPopup=!0,this.showTrans=!0)},center(t){this.popupClass="center",this.ani=this.zoom?["zoom-in","fade"]:["fade"],this.transitionStyle={position:"fixed",zIndex:this.zIndex,display:"flex",flexDirection:"column",bottom:0,left:0,right:0,top:0,justifyContent:"center",alignItems:"center"},t||(this.showPopup=!0,this.showTrans=!0)},left(t){this.popupClass="left",this.ani=["slide-left"],this.transitionStyle={position:"fixed",zIndex:this.zIndex,left:0,bottom:0,top:0,backgroundColor:this.bg,display:"flex",flexDirection:"column"},t||(this.showPopup=!0,this.showTrans=!0)},right(t){this.popupClass="right",this.ani=["slide-right"],this.transitionStyle={position:"fixed",zIndex:this.zIndex,bottom:0,right:0,top:0,backgroundColor:this.bg,display:"flex",flexDirection:"column"},t||(this.showPopup=!0,this.showTrans=!0)}}},[["render",function(t,e,o,s,i,a){const c=Y(_("uv-overlay"),G),u=Y(_("uv-status-bar"),tt),f=Y(_("uv-safe-bottom"),et),y=Y(_("uv-icon"),X),v=g,b=Y(_("uv-transition"),J),k=A("keypress");return i.showPopup?(n(),l(v,{key:0,class:p(["uv-popup",[i.popupClass,a.isDesktop?"fixforpc-z-index":""]]),style:m([{zIndex:o.zIndex}])},{default:r((()=>[h(v,{onTouchstart:a.touchstart},{default:r((()=>[i.maskShow&&o.overlay?(n(),l(c,{key:"1",show:i.showTrans,duration:o.duration,"custom-style":o.overlayStyle,opacity:o.overlayOpacity,zIndex:o.zIndex,onClick:a.onTap},null,8,["show","duration","custom-style","opacity","zIndex","onClick"])):d("",!0),h(b,{key:"2",mode:i.ani,name:"content","custom-style":i.transitionStyle,duration:o.duration,show:i.showTrans,onClick:a.onTap},{default:r((()=>[h(v,{class:p(["uv-popup__content",[i.popupClass]]),style:m([a.contentStyle]),onClick:a.clear},{default:r((()=>[o.safeAreaInsetTop?(n(),l(u,{key:0})):d("",!0),T(t.$slots,"default",{},void 0,!0),o.safeAreaInsetBottom?(n(),l(f,{key:1})):d("",!0),o.closeable?(n(),l(v,{key:2,onClick:z(a.close,["stop"]),class:p(["uv-popup__content__close",["uv-popup__content__close--"+o.closeIconPos]]),"hover-class":"uv-popup__content__close--hover","hover-stay-time":"150"},{default:r((()=>[h(y,{name:"close",color:"#909399",size:"18",bold:""})])),_:1},8,["onClick","class"])):d("",!0)])),_:3},8,["style","class","onClick"])])),_:3},8,["mode","custom-style","duration","show","onClick"])])),_:3},8,["onTouchstart"]),i.maskShow?(n(),l(k,{key:0,onEsc:a.onTap},null,8,["onEsc"])):d("",!0)])),_:3},8,["class","style"])):d("",!0)}],["__scopeId","data-v-db11292f"]]),st=E({__name:"chat",setup(t){const e=B(null),o=B(null),s=B([]),i=B(""),a=B({}),p=B(null),m=B(null),f=B([]),v=B(null),b=B(!1),x=B("");U((function(t){i.value=t.userid,P("addChatRecordData"),N("addChatRecordData",(t=>{!function(t){e.value.addChatRecordData(t)}(t)})),async function(t){const{code:e,data:o}=await D({user_id:t});1==e&&(a.value=o,V({title:o.nickname}))}(t.userid)}));const S=(t,e)=>{const o={page_no:t,page_size:e,keyword:x.value};F(o).then((t=>{console.log(t),v.value.complete(t.data.list),console.log(f.value)})).catch((t=>{v.value.complete(!1)}))},$=(t,o)=>{const s={user_id:i.value,page_no:t,page_size:o};O(s).then((t=>{e.value.complete(t.data.list.reverse())})).catch((t=>{e.value.complete(!1)}))},I=t=>{o.value.updateKeyboardHeightChange(t)},T=()=>{o.value.hidedKeyboard()},z=(t,e)=>{let o={msg:t,msg_type:e,to_id:i.value,to_type:"user"};R().globalData.socket.send({event:"chat",data:o})};async function j(t){console.log(t);for(var e=0;e<t.tempFilePaths.length;e++){A(t.tempFilePaths[e])}}async function A(t){const{code:e,data:o}=await H(t);1==e&&z(o.uri,2)}return(t,i)=>{const A=Y(_("chat-item"),M),B=g,P=Y(_("chat-input-bar"),L),N=Y(_("z-paging"),K),D=Y(_("uv-search"),Z),V=Y(_("uv-popup"),ot),F=y;return n(),l(B,{class:"content"},{default:r((()=>[h(N,{ref_key:"chatpaging",ref:e,modelValue:s.value,"onUpdate:modelValue":i[2]||(i[2]=t=>s.value=t),"use-chat-record-mode":"","safe-area-inset-bottom":"","bottom-bg-color":"#FFFFFF",onQuery:$,onKeyboardHeightChange:I,onHidedKeyboard:T},{bottom:r((()=>[h(B,{class:"display-flex",style:{padding:"10rpx 0"}},{default:r((()=>[h(B,{onClick:i[0]||(i[0]=t=>(b.value=!0,void m.value.open())),class:"label"},{default:r((()=>[c(" 快捷回复 ")])),_:1}),h(B,{onClick:i[1]||(i[1]=t=>p.value.open()),class:"label"},{default:r((()=>[c(" 用户资料 ")])),_:1}),h(B,{class:"label"},{default:r((()=>[c(" 订单信息 ")])),_:1})])),_:1}),h(P,{ref_key:"inputBar",ref:o,onChooseImage:j,onSend:z},null,512)])),default:r((()=>[(n(!0),k(C,null,w(s.value,((t,e)=>(n(),l(B,{key:e,style:{position:"relative"}},{default:r((()=>[h(B,{style:{transform:"scaleY(-1)"}},{default:r((()=>[h(A,{item:t},null,8,["item"])])),_:2},1024)])),_:2},1024)))),128))])),_:1},8,["modelValue"]),h(V,{mode:"bottom",round:"10",ref_key:"ScriptPopup",ref:m},{default:r((()=>[h(B,{style:{height:"70vh"}},{default:r((()=>[h(B,{style:{padding:"20rpx"},class:""},{default:r((()=>[h(D,{placeholder:"请输入搜索内容",modelValue:x.value,"onUpdate:modelValue":i[3]||(i[3]=t=>x.value=t)},null,8,["modelValue"]),b.value?(n(),l(N,{key:0,ref_key:"scriptPaging",ref:v,fixed:!1,modelValue:f.value,"onUpdate:modelValue":i[4]||(i[4]=t=>f.value=t),onQuery:S},{default:r((()=>[(n(!0),k(C,null,w(f.value,((e,o)=>(n(),l(B,{class:"item",key:o,onClick:s=>t.itemClick(e,o)},{default:r((()=>[h(B,{class:"item-title"},{default:r((()=>[c(u(e.title),1)])),_:2},1024),h(B,{class:"item-detail"},{default:r((()=>[c(u(e.detail),1)])),_:2},1024),h(B,{class:"item-line"})])),_:2},1032,["onClick"])))),128))])),_:1},8,["modelValue"])):d("",!0)])),_:1})])),_:1})])),_:1},512),h(V,{mode:"bottom",round:"10",ref_key:"userPopup",ref:p},{default:r((()=>[h(B,{style:{height:"30vh"}},{default:r((()=>[h(B,{class:"padding-about-20 padding-top-30 font-size-26"},{default:r((()=>[h(B,{class:"display-flex align-items"},{default:r((()=>[h(F,{src:a.value.avatar,style:{width:"100rpx",height:"100rpx"},mode:"aspectFill"},null,8,["src"]),h(B,{class:"font-size-32 margin-left-20 font-weight-bold"},{default:r((()=>[c(u(a.value.nickname),1)])),_:1})])),_:1}),h(B,{class:"margin-top-10"},{default:r((()=>[c(" 用户编号： "+u(a.value.sn),1)])),_:1}),h(B,{class:"margin-top-10"},{default:r((()=>[c(" 手机号码："+u(a.value.mobile),1)])),_:1}),h(B,{class:"margin-top-10"},{default:r((()=>[c(" 累计消费："+u(a.value.total_order_amount),1)])),_:1}),h(B,{class:"margin-top-10"},{default:r((()=>[c(" 注册时间："+u(a.value.create_time),1)])),_:1})])),_:1})])),_:1})])),_:1},512)])),_:1})}}},[["__scopeId","data-v-db4216d9"]]);export{st as default};
