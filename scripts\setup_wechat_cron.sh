#!/bin/bash

# 微信公众号关注用户同步定时任务设置脚本
# 每3分钟执行一次同步

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

echo "设置微信公众号关注用户同步定时任务..."

# 检查是否已存在相同的定时任务
CRON_JOB="*/3 * * * * cd $PROJECT_DIR && php think wechat:followers >> /var/log/wechat_followers.log 2>&1"
EXISTING_CRON=$(crontab -l 2>/dev/null | grep "wechat:followers" || true)

if [ -n "$EXISTING_CRON" ]; then
    echo "定时任务已存在："
    echo "$EXISTING_CRON"
    echo ""
    read -p "是否要更新现有的定时任务？(y/n): " -n 1 -r
    echo ""
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        # 删除现有的定时任务
        crontab -l 2>/dev/null | grep -v "wechat:followers" | crontab -
        echo "已删除现有的定时任务"
    else
        echo "保持现有的定时任务不变"
        exit 0
    fi
fi

# 添加新的定时任务
(crontab -l 2>/dev/null; echo "$CRON_JOB") | crontab -

echo "定时任务设置完成！"
echo "任务内容：$CRON_JOB"
echo ""
echo "日志文件：/var/log/wechat_followers.log"
echo ""
echo "可以使用以下命令查看定时任务："
echo "crontab -l"
echo ""
echo "可以使用以下命令查看日志："
echo "tail -f /var/log/wechat_followers.log"
echo ""
echo "手动执行同步命令："
echo "cd $PROJECT_DIR && php think wechat:followers"
