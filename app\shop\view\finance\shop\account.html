{layout name="layout1" /}

<div class="wrapper" >
    <div class="layui-card">
        <!-- 提示 -->
        <div class="layui-card-body">
            <div class="layui-collapse like-layui-collapse" style="baccount:1px dashed #c4c4c4">
                <div class="layui-colla-item">
                    <h2 class="layui-colla-title like-layui-colla-title" style="background-color: #fff">操作提示</h2>
                    <div class="layui-colla-content layui-show">
                        <p>*查看商家账户余额流水记录。</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 搜索 -->
        <div class="layui-card-body layui-form">
            <div class="layui-form-item">
                <div class="layui-row">
                    <div class="layui-inline">
                        <label class="layui-form-label">明细类型:</label>
                        <div class="layui-input-block">
                            <select name="search_key" id="search_key">
                                <option value="">全部</option>
                                <option value="settle">结算入账</option>
                                <option value="withdrawal">商家提现</option>
                                <option value="withdrawal_stay">商家提现中</option>
                                <option value="withdrawal_error">商家提现失败</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">记录时间:</label>
                        <div class="layui-input-inline">
                            <div class="layui-input-inline">
                                <input type="text" id="start_time" name="start_time" class="layui-input"  autocomplete="off">
                            </div>
                        </div>
                        <div class="layui-input-inline" style="margin-right: 5px;width: 20px;">
                            <label class="layui-form-mid">至</label>
                        </div>
                        <div class="layui-input-inline">
                            <input type="text" id="end_time" name="end_time" class="layui-input" autocomplete="off">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <div class="layui-btn-group">
                            <button type="button" day="1" class="layui-btn layui-btn-sm layui-btn-primary day">今天</button>
                            <button type="button" day="-1" class="layui-btn layui-btn-sm layui-btn-primary day">昨天</button>
                            <button type="button" day="7" class="layui-btn layui-btn-sm layui-btn-primary day">近7天</button>
                            <button type="button" day="30" class="layui-btn layui-btn-sm layui-btn-primary day">近30天</button>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn layui-btn-sm layui-btn-normal" lay-submit lay-filter="account-search">查询</button>
                        <button class="layui-btn layui-btn-sm layui-btn-primary" lay-submit lay-filter="account-clear-search">重置</button>
                        <button class="layui-btn layui-btn-sm layui-btn-primary" lay-submit lay-filter="data-export">导出</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 列表 -->
        <div class="layui-tab-item layui-show">
            <div class="layui-card">
                <div class="layui-card-body">
                    <table id="like-table-lists" lay-filter="like-table-lists"></table>
                    <script type="text/html" id="shop">
                        <div style="text-align: left;">
                            <img src="{{ d.logo }}" style="height:80px;width:80px;margin-right:5px;" class="layui-col-md4">
                            <p>商家编号:{{d.id}}</p>
                            <p>商家名称:{{d.name}}</p>
                            <p>商家类型:{{d.type}}</p>
                        </div>
                    </script>
                </div>
            </div>
        </div>

    </div>
</div>


<script>
    layui.use(['form'], function(){
        var $ = layui.$
            ,form = layui.form
            ,table = layui.table
            ,laydate = layui.laydate;

        laydate.render({elem:'#start_time' ,type:'datetime', theme:'#1E9FFF'});
        laydate.render({elem:'#end_time' ,type:'datetime', theme:'#1E9FFF'});

        like.tableLists("#like-table-lists", "{:url()}", [
            {field: 'shop', title: '商家信息', align: 'center',templet:'#shop',width:300}
            ,{field: 'log_sn', title: '明细流水号', align: 'center',width:180}
            ,{field: 'source_sn', title: '来源单号', align: 'center',width:180}
            ,{field: 'source_type', title: '明细类型', align: 'center',width:120}
            ,{field: 'change_amount', title: '变动金额', align: 'center',width:120}
            ,{field: 'left_amount', title: '剩余金额', align: 'center',width:120}
            ,{field: 'create_time', title: '记录时间', align: 'center',width:160}
        ]);

        /**
         * 监听搜索
         */
        form.on('submit(account-search)', function (data) {
            table.reload('like-table-lists', {
                where: data.field,
                page: {
                    curr: 1
                }
            });
        });

        /**
         * 清空查询
         */
        form.on('submit(account-clear-search)', function () {
            $('#shop_name').val('');
            $('#start_time').val('');
            $('#end_time').val('');
            $('#search_key').val('');
            form.render('select');
            $(".day.layui-btn-normal").addClass("layui-btn-primary");
            $("button.day.layui-btn-normal").removeClass("layui-btn-normal");
            table.reload('like-table-lists', {
                where: [],
                page: {
                    curr: 1
                }
            });
        });

        // 导出
        form.on('submit(data-export)', function (data) {
            var field = data.field;
            like.ajax({
                url: '{:url("finance.shop/export")}'
                , data: field
                , type: 'get'
                , success: function (res) {
                    if (res.code == 1) {
                        window.location.href = res.data.url;
                    }
                }
            });
        });


        $(document).on("click", ".day", function () {
            var day   = parseInt($(this).attr("day"));
            var start_time = "";
            var end_time   = "";

            switch (day) {
                case 1:
                    start_time = "{$dateTime.today[0]}";
                    end_time   = "{$dateTime.today[1]}";
                    break;
                case -1:
                    start_time = "{$dateTime.yesterday[0]}";
                    end_time   = "{$dateTime.yesterday[1]}";
                    console.log(start_time);
                    break;
                case 7:
                    start_time = "{$dateTime.days_ago7[0]}";
                    end_time   = "{$dateTime.days_ago7[1]}";
                    break;
                case 30:
                    start_time = "{$dateTime.days_ago30[0]}";
                    end_time   = "{$dateTime.days_ago30[1]}";
                    break;
            }

            $(this).siblings().removeClass('layui-btn-normal');
            $(this).siblings().addClass('layui-btn-primary');
            $(this).removeClass("layui-btn-primary");
            $(this).addClass('layui-btn-normal');

            $("#start_time").val(start_time);
            $("#end_time").val(end_time);
        })

    });
</script>