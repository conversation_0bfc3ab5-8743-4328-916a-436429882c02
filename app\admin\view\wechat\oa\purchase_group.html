{layout name="layout1" /}
<div class="wrapper">
    <div class="layui-card">
        <div class="layui-card-header">
            <h3>进采购群功能配置</h3>
        </div>
        <div class="layui-card-body">
            <div class="layui-collapse like-layui-collapse" lay-accordion="" style="border:1px dashed #c4c4c4">
                <div class="layui-colla-item">
                    <h2 class="layui-colla-title like-layui-colla-title" style="background-color: #fff">功能说明</h2>
                    <div class="layui-colla-content layui-show">
                        <p>* 配置用户点击"进采购群"菜单或发送"进采购群"关键词时的自动回复</p>
                        <p>* 系统将发送两条消息：第一条是欢迎文本，第二条是采购群二维码图片</p>
                        <p>* 请确保已在微信公众号菜单中配置了相应的关键词</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="layui-form" lay-filter="purchase-group-form">
            <div class="layui-card-body">
                <div class="layui-form-item">
                    <label class="layui-form-label">关键词：</label>
                    <div class="layui-input-block">
                        <input type="text" name="keyword" value="{$reply.keyword|default='进采购群'}" 
                               placeholder="请输入关键词" autocomplete="off" class="layui-input" readonly>
                        <div class="layui-form-mid layui-word-aux">此关键词需要在微信菜单中配置</div>
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <label class="layui-form-label">欢迎文本：</label>
                    <div class="layui-input-block">
                        <textarea name="content" placeholder="请输入欢迎文本" class="layui-textarea" rows="4">{$reply.content|default='欢迎加入我们的采购群！我们有专业的采购团队为您提供优质的商品和服务。请扫描下方二维码进群，享受更多采购优惠和专业指导。'}</textarea>
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <label class="layui-form-label">二维码图片：</label>
                    <div class="layui-input-block">
                        <div class="upload-image-container">
                            <input type="hidden" name="second_image_url" value="{$reply.second_image_url|default=''}">
                            <div class="upload-btn" id="upload_qrcode" {if !empty($reply.second_image_url)}style="display:none;"{/if}>
                                <i class="layui-icon layui-icon-upload"></i>
                                <p>点击上传二维码图片</p>
                                <p class="upload-tips">建议尺寸：300x300像素，格式：JPG/PNG，大小不超过2MB</p>
                            </div>
                            <div class="image-preview" {if empty($reply.second_image_url)}style="display:none;"{/if}>
                                <img src="{$reply.second_image_url|default=''}" alt="二维码预览" style="max-width: 200px; max-height: 200px;">
                                <div class="image-actions">
                                    <button type="button" class="layui-btn layui-btn-sm" onclick="changeQrcode()">更换图片</button>
                                    <button type="button" class="layui-btn layui-btn-sm layui-btn-danger" onclick="removeQrcode()">删除图片</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <label class="layui-form-label">启用状态：</label>
                    <div class="layui-input-block">
                        <input type="radio" name="status" value="1" title="启用" {if empty($reply.status) || $reply.status == 1}checked{/if}>
                        <input type="radio" name="status" value="0" title="停用" {if !empty($reply.status) && $reply.status == 0}checked{/if}>
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <div class="layui-input-block">
                        <button class="layui-btn {$view_theme_color}" lay-submit lay-filter="save-purchase-group">保存配置</button>
                        <button type="button" class="layui-btn layui-btn-primary" onclick="testPurchaseGroup()">测试功能</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.upload-image-container {
    border: 1px dashed #d2d2d2;
    border-radius: 4px;
    padding: 20px;
    text-align: center;
    min-height: 120px;
    position: relative;
}
.upload-btn {
    cursor: pointer;
    color: #666;
    transition: color 0.3s;
}
.upload-btn:hover {
    color: #1890ff;
}
.upload-btn i {
    font-size: 48px;
    display: block;
    margin-bottom: 10px;
}
.upload-tips {
    font-size: 12px;
    color: #999;
    margin-top: 5px;
}
.image-preview img {
    border-radius: 4px;
    margin-bottom: 10px;
    border: 1px solid #e6e6e6;
}
.image-actions {
    margin-top: 10px;
}
.image-actions button {
    margin: 0 5px;
}
</style>

<script>
layui.config({
    version:"{$front_version}",
    base: '/static/lib/'
}).use(['form', 'upload', 'layer'], function(){
    var $ = layui.$
        ,form = layui.form
        ,upload = layui.upload
        ,layer = layui.layer;

    // 二维码图片上传
    upload.render({
        elem: '#upload_qrcode',
        url: '{:url("upload/image")}',
        accept: 'images',
        size: 2048, // 2MB
        done: function(res){
            if(res.code == 1){
                $('input[name="second_image_url"]').val(res.data.url);
                $('.image-preview img').attr('src', res.data.url);
                $('#upload_qrcode').hide();
                $('.image-preview').show();
                layer.msg('上传成功');
            } else {
                layer.msg(res.msg || '上传失败');
            }
        },
        error: function(){
            layer.msg('上传失败');
        }
    });

    // 表单提交
    form.on('submit(save-purchase-group)', function(data){
        like.ajax({
            url: '{:url("wechat.oa/savePurchaseGroup")}',
            data: data.field,
            type: 'post',
            success: function(res){
                if(res.code == 1){
                    layer.msg('保存成功', {icon: 1});
                } else {
                    layer.msg(res.msg || '保存失败', {icon: 2});
                }
            }
        });
        return false;
    });
});

// 更换二维码
function changeQrcode() {
    $('#upload_qrcode').show();
    $('.image-preview').hide();
}

// 删除二维码
function removeQrcode() {
    $('input[name="second_image_url"]').val('');
    $('#upload_qrcode').show();
    $('.image-preview').hide();
}

// 测试功能
function testPurchaseGroup() {
    layer.open({
        type: 1,
        title: '测试说明',
        content: '<div style="padding: 20px;">' +
                '<p>测试步骤：</p>' +
                '<ol>' +
                '<li>确保已保存当前配置</li>' +
                '<li>在微信公众号中发送"进采购群"关键词</li>' +
                '<li>或点击配置了该关键词的菜单按钮</li>' +
                '<li>系统将自动发送欢迎文本和二维码图片</li>' +
                '</ol>' +
                '<p style="color: #ff5722;">注意：请确保微信公众号已正确配置并且二维码图片已上传</p>' +
                '</div>',
        area: ['500px', '300px']
    });
}
</script>
