<?php



namespace app\api\server;


use app\api\logic\DistributionLogic;
use app\api\logic\LoginLogic;
use app\common\model\user\User;
use app\common\model\Client_;
use app\common\model\user\UserAuth;
use app\common\server\storage\Driver as StorageDriver;
use app\common\server\UrlServer;
use app\common\server\ConfigServer;
use think\facade\Db;
use think\Exception;


class UserServer
{

    /**
     * User: 意象信息科技 lr
     * Desc: 通过小程序创建用户信息
     * @param $response
     * @param $client
     * @return array|\PDOStatement|string|\think\Model|null
     * @throws Exception
     */
    public static function createUser($response, $client)
    {
        $user_info = [];
        try {
            $openid = $response['openid'];
            $unionid = $response['unionid'] ?? '';
            $avatar_url = $response['headimgurl'] ?? '';
            $nickname = $response['nickname'] ?? '';

            Db::startTrans();

            // 获取存储引擎
            $config = [
                'default' => ConfigServer::get('storage', 'default', 'local'),
                'engine'  => ConfigServer::get('storage_engine')
            ];

            $time   = time(); //创建时间
            $avatar = '';     //头像路径

            if (empty($avatar_url)) {
                $avatar = ConfigServer::get('website', 'user_image', '');
            } else {
                $avatar = $avatar_url;
            }

            $data = [
                'nickname'          => $nickname,
                'sn'                => create_user_sn(),
                'avatar'            => $avatar,
                'create_time'       => $time,
                'distribution_code' => generate_invite_code(),//分销邀请码
                'is_distribution'   => DistributionLogic::isDistributionMember(),
                'client'            => $client,
                'is_new_user'       => 1
            ];

            if (empty($nickname)) {
                $data['nickname'] = '用户'.$data['sn'];
            }

            $user = User::create($data);
            $user_id = $user->id;

            $data = [
                'user_id' => $user_id,
                'openid' => $openid,
                'create_time' => $time,
                'unionid' => $unionid,
                'client' => $client,
            ];

            UserAuth::create($data);

            //生成会员分销扩展表
            DistributionLogic::createUserDistribution($user_id);

            // 生成分销基础信息表
            \app\common\logic\DistributionLogic::add($user_id);
    
            //注册赠送
            LoginLogic::registerAward($user_id);

            Db::commit();

            $user_info = User::field(['id', 'nickname', 'avatar', 'level', 'disable', 'distribution_code','is_new_user'])
                ->where(['id' => $user_id])
                ->find();
            if (empty($user_info['avatar'])) {
                $user_info['avatar'] = UrlServer::getFileUrl(ConfigServer::get('website', 'user_image', ''));
            } else {
                $user_info['avatar'] = UrlServer::getFileUrl($user_info['avatar']);
            }

        } catch (Exception $e) {
            Db::rollback();
            throw new Exception($e->getMessage());
        }

        return $user_info;
    }

    /**
     * 更新用户信息
     * @param $response
     * @param $client
     * @param $user_id
     * @return array|\PDOStatement|string|\think\Model|null
     */
    public static function updateUser($response, $client, $user_id)
    {
        $time = time();
        try {

            $openid = $response['openid'];
            $unionid = $response['unionid'] ?? '';
            $avatar_url = $response['headimgurl'] ?? '';
            $nickname = $response['nickname'] ?? '';

            Db::startTrans();

            //ios,android
            if (in_array($client, [Client_::ios, Client_::android])) {
                UserAuth::where(['openid' => $openid])
                    ->update(['client' => $client]);
            }

            //用户已存在，但是无该端的授权信息，保存数据
            $user_auth_id = UserAuth::where(['user_id' => $user_id, 'openid' => $openid])
                ->value('id');

            if (empty($user_auth_id)) {
                $data = [
                    'create_time' => $time,
                    'openid' => $openid,
                    'unionid' => $unionid,
                    'user_id' => $user_id,
                    'client' => $client,
                ];
                UserAuth::create($data);
            }

            $user_info = User::alias('u')
                ->field(['u.nickname', 'u.avatar', 'u.level', 'u.id', 'au.unionid'])
                ->join('user_auth au', 'u.id=au.user_id')
                ->where(['au.openid' => $openid])
                ->find();

            //无头像需要更新头像
            if (empty($user_info['avatar']) && !empty($avatar_url)) {
                $data['avatar'] = $avatar_url;
                $data['update_time'] = $time;
                $data['nickname'] = $nickname;
                User::where(['id' => $user_info['id']])
                    ->update($data);
            }

            //之前无unionid需要更新
            if (empty($user_info['unionid']) && isset($unionid)) {
                $data = [];
                $data['unionid'] = $unionid;
                $data['update_time'] = $time;
                UserAuth::where(['user_id' => $user_info['id']])
                    ->update($data);
            }

            $user_info = User::where(['id' => $user_info['id']])
                ->field(['id', 'nickname', 'avatar', 'level', 'disable', 'distribution_code','is_new_user'])
                ->find();

            if (empty($user_info['avatar'])) {
                $user_info['avatar'] = UrlServer::getFileUrl(ConfigServer::get('website', 'user_image', ''));
            } else {
                $user_info['avatar'] = UrlServer::getFileUrl($user_info['avatar']);
            }
            Db::commit();

        } catch (Exception $e) {
            Db::rollback();
            throw new Exception($e->getMessage());
        }

        return $user_info;
    }
}