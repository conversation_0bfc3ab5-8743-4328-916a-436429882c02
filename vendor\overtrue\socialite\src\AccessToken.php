<?php

/*
 * This file is part of the overtrue/socialite.
 *
 * (c) overtrue <<EMAIL>>
 *
 * This source file is subject to the MIT license that is bundled
 * with this source code in the file LICENSE.
 */

namespace Overtrue\Socialite;

use ArrayAccess;
use InvalidArgumentException;
use JsonSerializable;

/**
 * Class AccessToken.
 */
class AccessToken implements AccessTokenInterface, ArrayAccess, JsonSerializable
{
    use HasAttributes;

    /**
     * AccessToken constructor.
     *
     * @param array $attributes
     */
    public function __construct(array $attributes)
    {
        if (empty($attributes['access_token'])) {
            throw new InvalidArgumentException('The key "access_token" could not be empty.');
        }

        $this->attributes = $attributes;
    }

    /**
     * Return the access token string.
     *
     * @return string
     */
    public function getToken()
    {
        return $this->getAttribute('access_token');
    }

    /**
     * Return the refresh token string.
     *
     * @return string
     */
    public function getRefreshToken()
    {
        return $this->getAttribute('refresh_token');
    }

    /**
     * Set refresh token into this object.
     *
     * @param string $token
     */
    public function setRefreshToken($token)
    {
        $this->setAttribute('refresh_token', $token);
    }

    /**
     * {@inheritdoc}
     */
    public function __toString()
    {
        return strval($this->getAttribute('access_token', ''));
    }

    /**
     * {@inheritdoc}
     */
    public function jsonSerialize()
    {
        return $this->getToken();
    }
}
