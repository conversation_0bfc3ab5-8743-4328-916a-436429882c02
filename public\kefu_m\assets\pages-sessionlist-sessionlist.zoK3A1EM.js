import{r as e,$ as a,a as s,l as t,b as l,c as o,d as i,e as n,f as d,w as c,o as r,g as u,h as p,t as f,i as m,j as g,k as _,F as b,m as k,n as v,p as h,q as y,s as x,u as w}from"./index-KqVIYTFB.js";import{_ as z,o as j,a as F,b as S,c as C,r as V,d as q}from"./uni-app.es.elp5fm4t.js";import{_ as D}from"./zb-popover.DS7TbkBw.js";import{_ as H}from"./z-paging.DXcm7bPn.js";const I=z({__name:"sessionlist",setup(z){const I=[{text:"退出登录"}],P=e(null),Q=e([]),U=e({});e(0);const $=e(!0);async function A(e){console.log(e);const{code:a,data:s}=await k();1==a&&(t.clear(),v({url:"/pages/login/login"}))}e([{text:"删除",style:{backgroundColor:"#f56c6c"}}]),j((function(e){a("updateSessionlist"),s("updateSessionlist",(e=>{B()})),console.log(e.token),e.token&&(t.set("token",e.token),l("updateSocket"),$.value=!1),async function(){const{code:e,data:a}=await o();1==e&&(U.value=a,t.set("serveinfo",a))}()})),F((function(){}));const B=(e,a)=>{i({page_no:e,page_size:a,nickname:""}).then((e=>{P.value.complete(e.data.list)})).catch((e=>{P.value.complete(!1)}))};return S((function(){})),C((function(){})),(e,a)=>{const s=h,t=y,l=V(n("uv-icon"),q),o=V(n("zb-popover"),D),i=V(n("z-paging"),H);return r(),d(i,{ref_key:"paging",ref:P,modelValue:Q.value,"onUpdate:modelValue":a[0]||(a[0]=e=>Q.value=e),onQuery:B},{default:c((()=>[$.value?(r(),d(t,{key:0,class:"",style:{width:"100%",height:"170rpx",overflow:"hidden"}},{default:c((()=>[u(t,{class:"background"},{default:c((()=>[u(t,{style:{padding:"20rpx"}},{default:c((()=>[u(t,{class:"display-flex align-items"},{default:c((()=>[u(s,{src:U.value.avatar,style:{width:"120rpx",height:"120rpx"},class:"border-radius",mode:"aspectFill"},null,8,["src"]),u(t,{class:"display-flex file-1 space-between"},{default:c((()=>[u(t,{class:"file-1 margin-left-20"},{default:c((()=>[u(t,{style:{"-webkit-line-clamp":"1"},class:"font-size-36 webkit-line-clamp color-333 font-weight-bold"},{default:c((()=>[p(" Hi，"+f(U.value.nickname||"-"),1)])),_:1}),u(t,{class:"display-flex flex-end"},{default:c((()=>[u(o,{placement:"left-start",options:I,ref:"Popover1",onSelect:A,class:"item-popover"},{default:c((()=>[u(l,{name:"setting",size:"25"})])),_:1},512)])),_:1})])),_:1})])),_:1})])),_:1})])),_:1})])),_:1})])),_:1})):m("",!0),u(t,{class:"padding-about-20"},{default:c((()=>[(r(!0),g(b,null,_(Q.value,((e,a)=>(r(),d(t,{key:a,onClick:a=>function(e){console.log(e),x("/pages/chat/chat?userid="+e.user_id+"&name="+e.nickname);let a={user_id:e.user_id+"",kefu_id:U.value.id+"",shop_id:U.value.shop_id+""};w().globalData.socket.send({event:"read",data:a})}(e),class:"display-flex align-items",style:{padding:"20rpx 0","border-bottom":"1rpx solid #F5F5F5"}},{default:c((()=>[u(t,{style:{width:"100rpx",height:"100rpx"},class:"position-relative"},{default:c((()=>[u(s,{src:e.avatar,style:{width:"100rpx",height:"100rpx"},class:"border-radius-12",mode:""},null,8,["src"]),e.is_read?m("",!0):(r(),d(t,{key:0,class:"position-absolute transform-translate-center",style:{top:"4%",right:"-15%"}},{default:c((()=>[u(t,{class:"border-radius",style:{border:"solid #f56c6c 10rpx"}})])),_:1}))])),_:2},1024),u(t,{class:"file-1 margin-left-20"},{default:c((()=>[u(t,{class:"display-flex space-between align-items"},{default:c((()=>[u(t,{class:"font-size-28 font-weight-bold"},{default:c((()=>[p(f(e.nickname),1)])),_:2},1024),u(t,{class:"font-size-22 color-999"},{default:c((()=>[p(f(e.create_time),1)])),_:2},1024)])),_:2},1024),u(t,{style:{"-webkit-line-clamp":"1"},class:"font-size-26 webkit-line-clamp margin-top-10"},{default:c((()=>[p(f(e.msg),1)])),_:2},1024)])),_:2},1024)])),_:2},1032,["onClick"])))),128))])),_:1})])),_:1},8,["modelValue"])}}},[["__scopeId","data-v-d3d48e3b"]]);export{I as default};
