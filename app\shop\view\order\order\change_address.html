{layout name="layout2" /}
<style>
    .div-flex {
        display: flex;
        align-items: center;
        justify-content: left;
    }

    .layui-form-label {
        width: 100px;
    }

    .width-160 {
        width: 200px;
    }

    .layui-table th {
        text-align: center;
    }

    .table-margin {
        margin-left: 50px;
        margin-right: 50px;
        text-align: center;
    }

    .image {
        height: 80px;
        width: 80px;
    }

</style>

<div class="layui-card-body wrapper">
    <!--基本信息-->
    <div class="layui-form" lay-filter="layuiadmin-form-change_address" id="layuiadmin-form-change_address" >
        <input type="hidden" name="order_id" id="order_id" value="{$id}">

        <div class="layui-form-item">
            <label class="layui-form-label">请选择地址：</label>
            <div class="layui-input-inline">
                <select name="province" lay-filter="first_category" lay-verify="custom_required"
                        lay-verType="tips" switch-tab="0" verify-msg="请选择分类">
                    <option value="">请选择省份或直辖市</option>
                </select>
            </div>
            <div class="layui-input-inline">
                <select name="city" lay-filter="second_category">
                    <option value="">请选择城市</option>
                </select>
            </div>
            <div class="layui-input-inline">
                <select name="district" lay-filter="third_category">
                    <option value="">请选择区县</option>
                </select>
            </div>
        </div>


        <div class="layui-form-item div-flex select-address">
            <label class="layui-form-label ">详细地址:</label>
            <div class="layui-input-inline">
                <input type="text" name="address" id="address" required  lay-verify="required" placeholder="请输入详细地址" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label ">收件人:</label>
            <div class="layui-input-inline">
                <input type="text" name="consignee" id="consignee" required  lay-verify="required" placeholder="请输入收件人" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label ">收件人手机号:</label>
            <div class="layui-input-inline">
                <input type="text" name="mobile" id="mobile" required  lay-verify="required|phone"  placeholder="请输入手机号码" autocomplete="off" class="layui-input">
            </div>
        </div>

        <div class="layui-form-item div-flex ">
            <div class="layui-input-block ">
                <input type="button" class="layui-btn layui-btn-sm layui-btn-normal width_160" lay-submit lay-filter="send" id="send" value="修改">
                <button type="button" class="layui-btn layui-btn-sm layui-btn-primary width_160 " id="back">返回</button>
            </div>
        </div>
    </div>
</div>


<script type="text/javascript">
    //注意：选项卡 依赖 element 模块，否则无法进行功能性操作

    layui.config({
        version:"{$front_version}",
        base: '/static/lib/' //静态资源所在路径
    }).use(['form'], function(){
        var $ = layui.$
            , form = layui.form;

        //主图放大
        $(document).on('click', '.image-show', function () {
            var src = $(this).attr('src');
            like.showImg(src,600);
        });


        form.on('radio(express)', function (data) {
            var checked = data.value;
            if (checked == 1) {
                $('.select-express').show();
            } else {
                $('.select-express').hide();
            }
        });

        $('#back').click(function () {
            var index=parent.layer.getFrameIndex(window.name); //获取当前窗口的name
            parent.layer.close(index);
            parent.layui.table.reload('order-lists');
            return true;
        });

        //***************************************分类联动start***************************************//
        var info = {$info | raw};
        var categorys = {$address_tree | raw};
        //收件人
        $('#consignee').val(info.consignee);
        //手机号
        $('#mobile').val(info.mobile);
        //详细地址
        $('#address').val(info.address);

        setSelectFirst(info.province);
        setSelectSecond(info.city,info.province);
        setSelectThird(info.district,info.city);

        function setSelectFirst(default_id) {
            var category_select_html = '<option value="">请选择省份</option>';
            for (var i in categorys) {
                if (categorys[i]['parent_id'] == 100000) {
                    category_select_html += '<option value="' + categorys[i]['id'] + '">' + categorys[i]['name'] + '</option>';
                }
            }
            $('select[name="province"]').html(category_select_html);
            $('select[name="province"]').val(default_id);
            form.render('select');
        }
        function setSelectSecond(default_id, pid) {
            pid = pid === undefined ? $('select[name="province"]').val() : pid;
            $('select[name="city"]').html('<option value="">请选择城市</option>');
            $('select[name="district"]').html('<option value="">请选择区县</option>');
            var category_select_html = '<option value="">请选择城市</option>';
            for (var i in categorys) {
                if (categorys[i]['parent_id'] == pid) {
                    category_select_html += '<option value="' + categorys[i]['id'] + '">' + categorys[i]['name'] + '</option>';
                }
            }
            $('select[name="city"]').html(category_select_html);
            $('select[name="city"]').val(default_id);
            form.render('select');
        }
        function setSelectThird(default_id, pid) {
            pid = pid === undefined ? $('select[name="city"]').val() : pid;
            $('select[name="district"]').html('<option value="">请选择区县</option>');
            var first_category_id = $('select[name="province"]').val();
            var category_select_html = '<option value="">请选择区县</option>';
            for (var i in categorys) {
                if (categorys[i]['parent_id'] == pid) {
                    category_select_html += '<option value="' + categorys[i]['id'] + '">' + categorys[i]['name'] + '</option>';
                }
            }
            $('select[name="district"]').html(category_select_html);
            $('select[name="district"]').val(default_id);
            form.render('select');
        }

        form.on('select(first_category)', function (data) {
            setSelectSecond(info.city, data.value);
        });
        form.on('select(second_category)', function (data) {
            setSelectThird(info.district, data.value);
        });
        //***************************************分类联动end***************************************//

        //发货
        form.on('submit(send)', function (data) {
            var field = data.field;
            like.ajax({
                url: '{:url("order.order/change_address_post")}'
                , data: field
                , type: 'post'
                , success: function (res) {
                    if (res.code == 1) {
                        layui.layer.msg(res.msg, {
                            offset: '15px'
                            , icon: 1
                            , time: 1000
                        },function () {
                            var index = parent.layer.getFrameIndex(window.name);
                            parent.location.reload();
                            parent.layer.close(index);
                        });
                    }
                },
            });
        })

    });
</script>