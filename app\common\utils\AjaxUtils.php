<?php
namespace app\common\utils;

use think\response\Json;

/**
 * Ajax工具类
 * Class AjaxUtils
 * @package app\common\utils
 */
class AjaxUtils
{
    /**
     * 返回成功响应
     * @param mixed $data 响应数据
     * @param string $msg 响应消息
     * @param int $code 响应码
     * @return Json
     */
    public static function success($data = [], string $msg = '操作成功', int $code = 200): Json
    {
        return json([
            'code' => $code,
            'msg' => $msg,
            'data' => $data
        ]);
    }

    /**
     * 返回错误响应
     * @param string $msg 错误消息
     * @param int $code 错误码
     * @param mixed $data 响应数据
     * @return Json
     */
    public static function error(string $msg = '操作失败', int $code = 400, $data = []): J<PERSON>
    {
        return json([
            'code' => $code,
            'msg' => $msg,
            'data' => $data
        ]);
    }
}
