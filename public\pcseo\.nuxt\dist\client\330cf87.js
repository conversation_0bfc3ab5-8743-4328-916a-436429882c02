(window.webpackJsonp=window.webpackJsonp||[]).push([[6],{480:function(t,e,d){var content=d(504);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,d(14).default)("ed2618a4",content,!0,{sourceMap:!1})},503:function(t,e,d){"use strict";d(480)},504:function(t,e,d){var o=d(13)(!1);o.push([t.i,".address-list[data-v-425028d8]  .el-dialog__body{height:460px;overflow-y:auto}.address-list .list[data-v-425028d8]{margin:0 auto;width:800px}.address-list .list .item[data-v-425028d8]{position:relative;cursor:pointer;height:100px;padding:16px 150px 16px 20px;border:1px solid hsla(0,0%,89.8%,.89804);border-radius:2px}.address-list .list .item.active[data-v-425028d8]{border-color:#ff2c3c}.address-list .list .item .oprate[data-v-425028d8]{position:absolute;right:20px;bottom:9px}.address-list .dialog-footer[data-v-425028d8]{text-align:center}.address-list .dialog-footer .el-button[data-v-425028d8]{width:160px}",""]),t.exports=o},556:function(t,e,d){"use strict";d.r(e);var o=d(6),r=(d(51),{components:{},props:{value:{type:Boolean,default:!1}},data:function(){return{showDialog:!1,showAddressAdd:!1,addressList:[],selectId:0,editId:""}},methods:{getAddress:function(){var t=this;return Object(o.a)(regeneratorRuntime.mark((function e(){var d,code,data;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,t.$get("user_address/lists");case 2:d=e.sent,code=d.code,data=d.data,1==code&&(t.addressList=data);case 6:case"end":return e.stop()}}),e)})))()},setDefault:function(t){var e=this;return Object(o.a)(regeneratorRuntime.mark((function d(){var o,code,r;return regeneratorRuntime.wrap((function(d){for(;;)switch(d.prev=d.next){case 0:return d.next=2,e.$post("user_address/setDefault",{id:t});case 2:o=d.sent,code=o.code,o.data,r=o.msg,1==code&&(e.$message({message:r,type:"success"}),e.getAddress());case 7:case"end":return d.stop()}}),d)})))()},onConfirm:function(){this.$emit("confirm",this.selectId),this.showDialog=!1}},watch:{value:function(t){this.showDialog=t,1==t&&this.getAddress()},showDialog:function(t){this.$emit("input",t)}}}),n=(d(503),d(9)),component=Object(n.a)(r,(function(){var t=this,e=t.$createElement,d=t._self._c||e;return d("div",{staticClass:"address-list"},[d("el-dialog",{attrs:{title:"更换地址",visible:t.showDialog,width:"900px"},on:{"update:visible":function(e){t.showDialog=e}}},[d("div",{staticClass:"list black"},t._l(t.addressList,(function(e,o){return d("div",{key:o,class:["item m-b-16",{active:e.id==t.selectId}],on:{click:function(d){t.selectId=e.id}}},[d("div",[d("span",{staticClass:"weigth-500"},[t._v(t._s(e.contact))]),t._v("\n                    "+t._s(e.telephone)+"\n                    "),e.is_default?d("el-tag",{attrs:{size:"mini",type:"warning",effect:"dark"}},[t._v("默认")]):t._e()],1),t._v(" "),d("div",{staticClass:"lighter m-t-8"},[t._v("\n                    "+t._s(e.province)+" "+t._s(e.city)+" "+t._s(e.district)+"\n                    "+t._s(e.address)+"\n                ")]),t._v(" "),d("div",{staticClass:"oprate lighter flex"},[d("div",{staticClass:"m-r-16",on:{click:function(d){d.stopPropagation(),t.editId=e.id,t.showAddressAdd=!0}}},[t._v("\n                        修改\n                    ")]),t._v(" "),d("div",{on:{click:function(d){return d.stopPropagation(),t.setDefault(e.id)}}},[t._v("设为默认")])])])})),0),t._v(" "),d("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[d("el-button",{attrs:{type:"primary"},on:{click:t.onConfirm}},[t._v("确认")]),t._v(" "),d("el-button",{on:{click:function(e){t.showDialog=!1}}},[t._v("取消")])],1)]),t._v(" "),d("address-add",{attrs:{aid:t.editId},on:{success:t.getAddress},model:{value:t.showAddressAdd,callback:function(e){t.showAddressAdd=e},expression:"showAddressAdd"}})],1)}),[],!1,null,"425028d8",null);e.default=component.exports;installComponents(component,{AddressAdd:d(497).default})}}]);