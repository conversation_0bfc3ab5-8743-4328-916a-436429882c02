<?php

namespace app\common\listener\websocket;


use app\common\model\kefu\ChatRelation;

/**
 * 已读状态
 * Class Read
 * @package app\common\listener\websocket
 */
class Read
{
    /**
     * 处理已读事件
     * @param $params
     */
    public function handle($params)
    {
        $this->is_read($params);
    }

    /**
     * 标记消息为已读
     * @param $params
     */
    public function is_read($params)
    {
        try {
        $user_id = $params['data']['user_id'] ?? 0;
        $kefu_id = $params['data']['kefu_id'] ?? 0;
        $shop_id = $params['data']['shop_id'] ?? 0;

        // 根据参数查找聊天关系
        if (!empty($kefu_id)) {
            // 客服聊天或用户对用户聊天（kefu_id字段复用存储联系人用户ID）
            $relation = ChatRelation::where(['kefu_id' => $kefu_id, 'user_id' => $user_id])->findOrEmpty();
        } else {
            // 根据shop_id查找客服聊天关系
            $relation = ChatRelation::where(['user_id' => $user_id, 'shop_id' => $shop_id])->findOrEmpty();
        }

        if (!$relation->isEmpty()) {
            
            // 修复：使用模型对象的id属性，而不是数组访问方式
            $updateCount = ChatRelation::update(['is_read' => 1], ['id' => $relation['id']]);

            // 如果有关系记录被更新，清除用户的未读消息缓存
            if ($updateCount > 0 && $user_id) {
                \app\api\controller\UserChat::clearUnreadBadgeCache($user_id);
            }
        }
        } catch (\Exception $e) {
            // 记录日志
            echo $e->getMessage();
        }
       
     
    }
}