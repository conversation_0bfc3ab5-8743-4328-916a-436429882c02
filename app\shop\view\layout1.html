<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>{:url()}</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link rel="stylesheet" href="__PUBLIC__/static/lib/layui/css/layui.css?v={$front_version}">
    <link rel="stylesheet" href="__PUBLIC__/static/admin/css/app.css">
    <link rel="stylesheet" href="__PUBLIC__/static/admin/css/like.css">
    <script src="__PUBLIC__/static/lib/layui/layui.js?v={$front_version}"></script>
    <script src="__PUBLIC__/static/admin/js/app.js"></script>
</head>
<body >
{$js_code|raw}
<script src="__PUBLIC__/static/admin/js/jquery.min.js"></script>
<script src="__PUBLIC__/static/admin/js/function.js"></script>
{__CONTENT__}

<!-- WebSocket Notification Script for Shop Panel -->
<script>
$(document).ready(function(){
    layui.use(['layer', 'jquery'], function(){
        var layer = layui.layer;
        var $ = layui.jquery;
        var notificationSoundUrl = '/uploads/audio/tomsg.mp3';
        var debugMode = true;
        var ws;
        var wsUrl = typeof webSocketUrl !== 'undefined' ? webSocketUrl : (window.location.protocol === 'https:' ? 'wss://' : 'ws://') + 'kefu.huohanghang.cn:20211';

        // 从后端PHP获取
        var shopId = {$SHOP_ID};
        var shopToken = '{$SHOP_TOKEN}';

        function connectWebSocket() {
            if (!shopId) {
                console.warn("Shop ID not available, WebSocket connection aborted.");
                // if(debugMode) layer.msg('店铺ID未找到，无法连接通知服务。', {icon: 2});
                return;
            }
            
            if (!shopToken) {
                console.warn("Shop Token not available, WebSocket connection aborted.");
                // if(debugMode) layer.msg('店铺令牌未找到，无法连接通知服务。', {icon: 2});
                return;
            }

            var fullWsUrl = wsUrl + '?client=5&type=shop&shop_id=' + shopId + '&token=' + shopToken;
            if(debugMode) console.log('正在连接到店铺WebSocket:', fullWsUrl);

            ws = new WebSocket(fullWsUrl);

            ws.onopen = function() {
                if(debugMode) console.log('店铺WebSocket连接已建立。');
            };

            ws.onmessage = function(event) {
                if(debugMode) console.log('收到店铺WebSocket消息:', event.data);
                try {
                    var response = JSON.parse(event.data);
                    var eventType = response.event || response.type; // 兼容两种格式
                    var messageData = response.data;

                    if (eventType === 'notification' || eventType === 'shop_notification') {
                        if (messageData && messageData.title && messageData.content) {
                            showShopNotification(
                                messageData.title,
                                messageData.content,
                                messageData.notification_type,
                                messageData.url || '',
                                messageData.icon
                            );
                        }
                    } else if (eventType === 'connect' && (response.message || messageData.msg)) {
                        if(debugMode) layer.msg(response.message || messageData.msg, {icon:1, time: 2000});
                    } else if (eventType === 'error' && (response.message || messageData.msg)) {
                        // layer.msg('通知错误: ' + (response.message || messageData.msg), {icon: 2});
                    } else if (eventType === 'pong' || eventType === 'heartbeat') {
                        if(debugMode) console.log('从服务器收到Pong响应。');
                    }
                } catch (e) {
                    if(debugMode) console.error('处理店铺WebSocket消息时出错:', e);
                }
            };

            ws.onerror = function(error) {
                if(debugMode) console.error('店铺WebSocket错误:', error);
                // layer.msg('店铺通知服务连接错误。', {icon: 2});
            };

            ws.onclose = function(event) {
                if(debugMode) console.log('店铺WebSocket连接已关闭。代码:', event.code, '原因:', event.reason);
                // 可选: 延迟后尝试重连
                // setTimeout(connectWebSocket, 7000);
            };
        }

        // 初始化WebSocket连接
        connectWebSocket();

        // 心跳: 每15秒发送一次ping (与swoole.php配置一致)
        setInterval(function() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                ws.send(JSON.stringify({ event: 'ping', data: { timestamp: Date.now() } }));
            }
        }, 15000);


        // --- 通知显示逻辑 (与管理后台适配) ---
        window.muteShopNotificationSound = true; 
        $(document).on('click', function() { 
            if (window.muteShopNotificationSound) {
                enableShopNotificationSound();
            }
        });
        
        function showShopNotification(title, content, type, url, icon) {
            if (debugMode) {
                console.log('Showing shop notification:', { title: title, content: content, type: type, url: url, icon: icon });
            }
            var layIcon = 0;
            if (icon === 'success' || icon === 1 || type === 'success_notification') layIcon = 1;
            else if (icon === 'error' || icon === 2 || type === 'error_notification') layIcon = 2;
            else if (icon === 'warning' || icon === 3 || type === 'warning_notification') layIcon = 3;
            else if (icon === 'info' || icon === 4 || type === 'info_notification') layIcon = 4;

            layer.open({
                type: 1,
                title: title || '店铺通知',
                content: '<div style="padding: 20px;">' + (content || '您有一条新的消息') + '</div>',
                shade: 0,
                offset: 'rb', 
                anim: 2, 
                time: 7000, // Longer display time for shops
                closeBtn: 1,
                success: function(layero, index){
                    playShopNotificationSound();
                    if (url) {
                        layero.css('cursor', 'pointer');
                        layero.on('click', function(){
                            window.open(url, '_blank'); // Or use internal navigation if appropriate
                            layer.close(index);
                        });
                    }
                }
            });
        }

        var shopAudioContext;
        var shopAudioBuffer;

        function initShopAudioContext() {
            try {
                window.AudioContext = window.AudioContext || window.webkitAudioContext;
                if (window.AudioContext) {
                    shopAudioContext = new AudioContext();
                    var unlockShopAudio = function() {
                        if (shopAudioContext.state === 'suspended') {
                            shopAudioContext.resume();
                        }
                        document.body.removeEventListener('click', unlockShopAudio);
                        document.body.removeEventListener('touchend', unlockShopAudio);
                    };
                    document.body.addEventListener('click', unlockShopAudio, false);
                    document.body.addEventListener('touchend', unlockShopAudio, false);

                    var request = new XMLHttpRequest();
                    request.open('GET', notificationSoundUrl, true);
                    request.responseType = 'arraybuffer';
                    request.onload = function() {
                        shopAudioContext.decodeAudioData(request.response, function(buffer) {
                            shopAudioBuffer = buffer;
                        }, function(e){ console.error("Error decoding shop audio data " + e.err); });
                    }
                    request.send();
                }
            } catch(e) {
                console.warn('Web Audio API is not supported or shop notification sound could not be initialized.');
            }
        }
        initShopAudioContext();

        function playShopNotificationSound() {
            if (!window.muteShopNotificationSound && shopAudioBuffer && shopAudioContext && shopAudioContext.state === 'running') {
                var source = shopAudioContext.createBufferSource();
                source.buffer = shopAudioBuffer;
                source.connect(shopAudioContext.destination);
                source.start(0);
            } else if (window.muteShopNotificationSound && debugMode) {
                console.log("Shop notification sound is muted or audio context not ready.");
            }
        }

        function enableShopNotificationSound() {
            window.muteShopNotificationSound = false;
            if (shopAudioContext && shopAudioContext.state === 'suspended') {
                shopAudioContext.resume();
            }
            if(debugMode) console.log('Shop notification sound enabled.');
            // layer.msg('店铺通知声音已开启', {icon:1, time:1500});
        }
        
        // Make functions globally available if needed
        window.showShopNotification = showShopNotification;
    });
});
</script>