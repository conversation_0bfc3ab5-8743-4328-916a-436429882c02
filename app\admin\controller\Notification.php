<?php
namespace app\admin\controller;

use app\common\basics\AdminBase;
use app\common\model\Notification as NotificationModel;
use app\common\service\NotificationService;
use think\facade\Db;

class Notification extends AdminBase
{
    protected $service;

    public function __construct()
    {
        parent::__construct();
        $this->service = new NotificationService();
    }

    /**
     * @notes Notification List for Admin Management
     */
    public function lists()
    {
        if ($this->request->isAjax()) {
            $page = $this->request->param('page', 1, 'intval');
            $limit = $this->request->param('limit', 15, 'intval');
            
            $filters = [
                'target_type' => $this->request->param('target_type/s', ''),
                'notification_type' => $this->request->param('notification_type/s', ''),
                'date_range' => $this->request->param('date_range/a', []), // Expects an array [start_date, end_date]
                'title' => $this->request->param('title/s', ''),
            ];

            // Remove empty filters
            $filters = array_filter($filters, function($value) {
                return $value !== '' && $value !== null && (is_array($value) ? !empty($value) : true);
            });

            $result = $this->service->getManagedNotifications($filters, $page, $limit);
            
            return $this->success('获取成功', [
                'count' => $result->total(),
                'list' => $result->items(),
            ]);
        }
        return $this->fetch();
    }

    /**
     * @notes Get Notification Detail
     */
    public function detail($id)
    {
        $notification = NotificationModel::find($id);
        if (!$notification) {
            return $this->error('通知不存在');
        }
        // You might want to format or add related data here if needed
        return $this->success('获取成功', $notification);
    }

    /**
     * @notes Delete Notifications
     */
    public function delete()
    {
        $ids = $this->request->param('ids/a');
        if (empty($ids)) {
            return $this->error('请选择要删除的通知');
        }

        Db::startTrans();
        try {
            // First delete related reads to avoid foreign key issues if not using ON DELETE CASCADE (though we are)
            Db::name('notification_reads')->whereIn('notification_id', $ids)->delete();
            $deletedCount = NotificationModel::destroy($ids);
            Db::commit();

            if ($deletedCount) {
                return $this->success('删除成功');
            } else {
                return $this->error('删除失败或没有找到要删除的通知');
            }
        } catch (\Exception $e) {
            Db::rollback();
            return $this->error('删除失败: ' . $e->getMessage());
        }
    }
}
