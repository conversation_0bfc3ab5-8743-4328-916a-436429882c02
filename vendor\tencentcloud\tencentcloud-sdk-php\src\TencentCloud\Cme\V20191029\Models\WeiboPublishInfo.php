<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Cme\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * 微博发布信息。
 *
 * @method string getTitle() 获取视频发布标题。
 * @method void setTitle(string $Title) 设置视频发布标题。
 * @method string getDescription() 获取视频发布描述信息。
 * @method void setDescription(string $Description) 设置视频发布描述信息。
 * @method string getVisible() 获取微博可见性，可取值为：
<li>Public：公开，所有人可见；</li>
<li>Private：私有，仅自己可见。</li>

默认为 Public，所有人可见。
 * @method void setVisible(string $Visible) 设置微博可见性，可取值为：
<li>Public：公开，所有人可见；</li>
<li>Private：私有，仅自己可见。</li>

默认为 Public，所有人可见。
 */
class WeiboPublishInfo extends AbstractModel
{
    /**
     * @var string 视频发布标题。
     */
    public $Title;

    /**
     * @var string 视频发布描述信息。
     */
    public $Description;

    /**
     * @var string 微博可见性，可取值为：
<li>Public：公开，所有人可见；</li>
<li>Private：私有，仅自己可见。</li>

默认为 Public，所有人可见。
     */
    public $Visible;

    /**
     * @param string $Title 视频发布标题。
     * @param string $Description 视频发布描述信息。
     * @param string $Visible 微博可见性，可取值为：
<li>Public：公开，所有人可见；</li>
<li>Private：私有，仅自己可见。</li>

默认为 Public，所有人可见。
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("Title",$param) and $param["Title"] !== null) {
            $this->Title = $param["Title"];
        }

        if (array_key_exists("Description",$param) and $param["Description"] !== null) {
            $this->Description = $param["Description"];
        }

        if (array_key_exists("Visible",$param) and $param["Visible"] !== null) {
            $this->Visible = $param["Visible"];
        }
    }
}
