{layout name="layout2" /}

<!-- 现代化样式和脚本 -->
<link rel="stylesheet" href="/static/shop/css/goods-modern.css">
<link href="__PUBLIC__/static/lib/layui/layeditor/layedit.css" rel="stylesheet"/>
<script src="__PUBLIC__/static/lib/layui/layeditor/index.js"></script>
<script src="__PUBLIC__/static/lib/layui/layeditor/ace/ace.js"></script>
<script src="__PUBLIC__/static/js/Sortable.min.js"></script>

<!-- 商品表单容器 -->
<div class="goods-form-modern">
    <!-- 步骤导航 -->
    <ul class="form-steps">
        <li class="form-step active" data-step="1">
            <a href="#" class="form-step-link">
                <div class="step-icon">1</div>
                <div class="step-title">基础信息</div>
            </a>
        </li>
        <li class="form-step" data-step="2">
            <a href="#" class="form-step-link">
                <div class="step-icon">2</div>
                <div class="step-title">商品图片</div>
            </a>
        </li>
        <li class="form-step" data-step="3">
            <a href="#" class="form-step-link">
                <div class="step-icon">3</div>
                <div class="step-title">规格设置</div>
            </a>
        </li>
        <li class="form-step" data-step="4">
            <a href="#" class="form-step-link">
                <div class="step-icon">4</div>
                <div class="step-title">详情内容</div>
            </a>
        </li>
        <li class="form-step" data-step="5">
            <a href="#" class="form-step-link">
                <div class="step-icon">5</div>
                <div class="step-title">销售设置</div>
            </a>
        </li>
    </ul>

    <!-- 表单内容 -->
    <form class="layui-form" id="goodsForm" method="post">
        <div class="form-content">
            <!-- 步骤1: 基础信息 -->
            <div class="form-step-content active" data-step="1">
                <div class="form-section">
                    <div class="section-header">
                        <div class="section-icon">
                            <i class="layui-icon layui-icon-form"></i>
                        </div>
                        <div>
                            <h3 class="section-title">商品基础信息</h3>
                            <p class="section-description">填写商品的基本信息，包括名称、分类、编码等</p>
                        </div>
                    </div>

                    <div class="form-grid form-grid-2">
                        <!-- 商品名称 -->
                        <div class="form-field">
                            <label class="form-label required">商品名称</label>
                            <input type="text" name="name" class="form-input" 
                                   placeholder="请输入商品名称，建议3-64个字符" 
                                   maxlength="64" required>
                            <div class="form-help">
                                <i class="layui-icon layui-icon-tips form-help-icon"></i>
                                商品名称将显示在商品列表和详情页，建议突出商品特色
                            </div>
                        </div>

                        <!-- 商品编码 -->
                        <div class="form-field">
                            <label class="form-label">商品编码</label>
                            <input type="text" name="code" class="form-input" 
                                   placeholder="留空系统将自动生成">
                            <div class="form-help">
                                <i class="layui-icon layui-icon-tips form-help-icon"></i>
                                用于内部管理，可以是SKU、条码等
                            </div>
                        </div>
                    </div>

                    <!-- 平台分类 -->
                    <div class="form-field">
                        <label class="form-label required">平台分类</label>
                        <div class="form-grid form-grid-3">
                            <select name="first_cate_id" class="form-input form-select" required>
                                <option value="">选择一级分类</option>
                            </select>
                            <select name="second_cate_id" class="form-input form-select">
                                <option value="">选择二级分类</option>
                            </select>
                            <select name="third_cate_id" class="form-input form-select">
                                <option value="">选择三级分类</option>
                            </select>
                        </div>
                        <div class="form-help">
                            <i class="layui-icon layui-icon-tips form-help-icon"></i>
                            请选择商品所属的平台分类，将影响商品在前台的展示位置
                        </div>
                    </div>

                    <div class="form-grid form-grid-2">
                        <!-- 店铺分类 -->
                        <div class="form-field">
                            <label class="form-label">店铺分类</label>
                            <select name="shop_cate_id" class="form-input form-select">
                                <option value="">请选择店铺分类</option>
                            </select>
                            <div class="form-help">
                                <i class="layui-icon layui-icon-tips form-help-icon"></i>
                                店铺内商品的分类管理
                            </div>
                        </div>

                        <!-- 商品品牌 -->
                        <div class="form-field">
                            <label class="form-label">商品品牌</label>
                            <select name="brand_id" class="form-input form-select">
                                <option value="">请选择品牌</option>
                            </select>
                        </div>
                    </div>

                    <!-- 商品卖点 -->
                    <div class="form-field">
                        <label class="form-label">商品卖点</label>
                        <input type="text" name="remark" class="form-input" 
                               placeholder="请输入商品卖点，突出商品特色和优势" 
                               maxlength="60">
                        <div class="form-help">
                            <i class="layui-icon layui-icon-tips form-help-icon"></i>
                            商品卖点将显示在商品列表中，建议控制在30字以内
                        </div>
                    </div>
                </div>
            </div>

            <!-- 步骤2: 商品图片 -->
            <div class="form-step-content" data-step="2">
                <div class="form-section">
                    <div class="section-header">
                        <div class="section-icon">
                            <i class="layui-icon layui-icon-picture"></i>
                        </div>
                        <div>
                            <h3 class="section-title">商品图片</h3>
                            <p class="section-description">上传商品图片，支持拖拽排序，第一张为主图</p>
                        </div>
                    </div>

                    <div class="form-field">
                        <label class="form-label required">商品图片</label>
                        <div class="image-upload" id="imageUploadArea">
                            <div class="upload-placeholder">
                                <i class="layui-icon layui-icon-upload" style="font-size: 48px; color: var(--gray-400);"></i>
                                <p style="margin: 16px 0 8px; color: var(--gray-600); font-weight: 500;">点击或拖拽上传图片</p>
                                <p style="margin: 0; color: var(--gray-500); font-size: 12px;">支持 JPG、PNG 格式，建议尺寸 800×800 像素</p>
                            </div>
                        </div>
                        
                        <div class="image-gallery" id="imageGallery">
                            <!-- 动态生成的图片将显示在这里 -->
                        </div>
                        
                        <div class="form-help">
                            <i class="layui-icon layui-icon-tips form-help-icon"></i>
                            最多上传6张图片，第一张为主图（封面图），支持拖拽排序
                        </div>
                    </div>

                    <!-- 商品视频 -->
                    <div class="form-field">
                        <label class="form-label">商品视频</label>
                        <div class="image-upload" id="videoUploadArea">
                            <div class="upload-placeholder">
                                <i class="layui-icon layui-icon-video" style="font-size: 48px; color: var(--gray-400);"></i>
                                <p style="margin: 16px 0 8px; color: var(--gray-600); font-weight: 500;">点击上传商品视频</p>
                                <p style="margin: 0; color: var(--gray-500); font-size: 12px;">支持 MP4 格式，文件大小限制 4MB 以内</p>
                            </div>
                        </div>
                        <div id="videoPreview" style="display: none;">
                            <!-- 视频预览将显示在这里 -->
                        </div>
                        <div class="form-help">
                            <i class="layui-icon layui-icon-tips form-help-icon"></i>
                            商品视频将在商品详情页面播放，有助于提升转化率
                        </div>
                    </div>
                </div>
            </div>

            <!-- 步骤3: 规格设置 -->
            <div class="form-step-content" data-step="3">
                <div class="form-section">
                    <div class="section-header">
                        <div class="section-icon">
                            <i class="layui-icon layui-icon-template-1"></i>
                        </div>
                        <div>
                            <h3 class="section-title">规格设置</h3>
                            <p class="section-description">设置商品的规格类型和价格库存信息</p>
                        </div>
                    </div>

                    <!-- 规格类型选择 -->
                    <div class="form-field">
                        <label class="form-label required">规格类型</label>
                        <div class="spec-selector">
                            <div class="spec-option active" data-type="single">
                                <div class="spec-option-icon">
                                    <i class="layui-icon layui-icon-form"></i>
                                </div>
                                <div class="spec-option-title">统一规格</div>
                                <div class="spec-option-desc">商品只有一种规格</div>
                            </div>
                            <div class="spec-option" data-type="multiple">
                                <div class="spec-option-icon">
                                    <i class="layui-icon layui-icon-template-1"></i>
                                </div>
                                <div class="spec-option-title">多规格</div>
                                <div class="spec-option-desc">商品有多种规格组合</div>
                            </div>
                        </div>
                        <input type="hidden" name="spec_type" value="1">
                    </div>

                    <!-- 统一规格设置 -->
                    <div id="singleSpecSection" class="spec-section">
                        <div class="card">
                            <div class="card-header">统一规格设置</div>
                            <div class="card-body">
                                <div class="form-grid form-grid-3">
                                    <div class="form-field">
                                        <label class="form-label">市场价</label>
                                        <input type="number" name="one_market_price" class="form-input" 
                                               placeholder="0.00" step="0.01" min="0">
                                    </div>
                                    <div class="form-field">
                                        <label class="form-label required">销售价</label>
                                        <input type="number" name="one_price" class="form-input" 
                                               placeholder="0.00" step="0.01" min="0.01" required>
                                    </div>
                                    <div class="form-field">
                                        <label class="form-label">成本价</label>
                                        <input type="number" name="one_chengben_price" class="form-input" 
                                               placeholder="0.00" step="0.01" min="0">
                                    </div>
                                </div>
                                
                                <div class="form-grid form-grid-4">
                                    <div class="form-field">
                                        <label class="form-label required">库存</label>
                                        <input type="number" name="one_stock" class="form-input" 
                                               placeholder="0" min="0" required>
                                    </div>
                                    <div class="form-field">
                                        <label class="form-label">重量(kg)</label>
                                        <input type="number" name="one_weight" class="form-input" 
                                               placeholder="0.00" step="0.01" min="0">
                                    </div>
                                    <div class="form-field">
                                        <label class="form-label">体积(m³)</label>
                                        <input type="number" name="one_volume" class="form-input" 
                                               placeholder="0.000" step="0.001" min="0">
                                    </div>
                                    <div class="form-field">
                                        <label class="form-label">商品条码</label>
                                        <input type="text" name="one_bar_code" class="form-input" 
                                               placeholder="商品条码">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 多规格设置 -->
                    <div id="multipleSpecSection" class="spec-section" style="display: none;">
                        <div class="spec-builder">
                            <div class="form-field">
                                <button type="button" class="btn btn-primary" id="addSpecBtn">
                                    <i class="layui-icon layui-icon-add-1"></i>
                                    添加规格项
                                </button>
                                <div class="form-help">
                                    <i class="layui-icon layui-icon-tips form-help-icon"></i>
                                    最多支持3个规格项，如：颜色、尺寸、款式等
                                </div>
                            </div>
                            
                            <div id="specItems">
                                <!-- 动态生成的规格项将显示在这里 -->
                            </div>
                            
                            <div id="specTable" style="display: none;">
                                <!-- 规格组合表格将显示在这里 -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 步骤4: 详情内容 -->
            <div class="form-step-content" data-step="4">
                <div class="form-section">
                    <div class="section-header">
                        <div class="section-icon">
                            <i class="layui-icon layui-icon-edit"></i>
                        </div>
                        <div>
                            <h3 class="section-title">商品详情</h3>
                            <p class="section-description">编写详细的商品描述，支持富文本编辑</p>
                        </div>
                    </div>

                    <div class="form-field">
                        <label class="form-label">商品详情</label>
                        <div class="editor-container">
                            <textarea name="content" id="content" class="form-textarea"
                                      placeholder="请输入商品详情描述..."
                                      style="height: 400px;"></textarea>
                        </div>
                        <div class="form-help">
                            <i class="layui-icon layui-icon-tips form-help-icon"></i>
                            详细描述商品的特点、功能、使用方法等信息，支持图片、视频等富媒体内容
                        </div>
                    </div>
                </div>
            </div>

            <!-- 步骤5: 销售设置 -->
            <div class="form-step-content" data-step="5">
                <div class="form-section">
                    <div class="section-header">
                        <div class="section-icon">
                            <i class="layui-icon layui-icon-set"></i>
                        </div>
                        <div>
                            <h3 class="section-title">销售设置</h3>
                            <p class="section-description">设置商品的销售相关参数</p>
                        </div>
                    </div>

                    <!-- 库存管理 -->
                    <div class="card">
                        <div class="card-header">库存管理</div>
                        <div class="card-body">
                            <div class="form-grid form-grid-2">
                                <div class="form-field">
                                    <label class="form-label">库存预警</label>
                                    <input type="number" name="stock_warn" class="form-input"
                                           placeholder="预警数量" min="0" value="0">
                                    <div class="form-help">
                                        <i class="layui-icon layui-icon-tips form-help-icon"></i>
                                        库存低于此值时系统将发出预警，0表示不预警
                                    </div>
                                </div>

                                <div class="form-field">
                                    <label class="form-label">库存显示</label>
                                    <div style="padding-top: 12px;">
                                        <input type="radio" name="is_show_stock" value="1" title="显示库存">
                                        <input type="radio" name="is_show_stock" value="0" title="隐藏库存" checked>
                                    </div>
                                    <div class="form-help">
                                        <i class="layui-icon layui-icon-tips form-help-icon"></i>
                                        控制商品详情页面是否显示剩余库存数量
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 配送设置 -->
                    <div class="card">
                        <div class="card-header">配送设置</div>
                        <div class="card-body">
                            <div class="form-field">
                                <label class="form-label">配送方式</label>
                                <div style="padding-top: 12px;">
                                    <input type="checkbox" name="delivery_type[]" value="1" lay-skin="primary" title="快递发货" checked>
                                    <input type="checkbox" name="delivery_type[]" value="3" lay-skin="primary" title="线下自提">
                                </div>
                                <div class="form-help">
                                    <i class="layui-icon layui-icon-tips form-help-icon"></i>
                                    选择商品支持的配送方式，可多选
                                </div>
                            </div>

                            <div class="form-field">
                                <label class="form-label required">运费设置</label>
                                <div style="padding-top: 12px;">
                                    <div style="margin-bottom: 12px;">
                                        <input type="radio" name="express_type" value="1" title="包邮" checked>
                                    </div>
                                    <div style="margin-bottom: 12px; display: flex; align-items: center; gap: 12px;">
                                        <input type="radio" name="express_type" value="2" title="统一运费">
                                        <input type="number" name="express_money" class="form-input"
                                               placeholder="运费金额" style="width: 120px;" step="0.01" min="0">
                                        <span>元</span>
                                    </div>
                                    <div style="display: flex; align-items: center; gap: 12px;">
                                        <input type="radio" name="express_type" value="3" title="运费模板">
                                        <select name="express_template_id" class="form-input form-select" style="width: 200px;">
                                            <option value="">请选择运费模板</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 其他设置 -->
                    <div class="card">
                        <div class="card-header">其他设置</div>
                        <div class="card-body">
                            <div class="form-grid form-grid-2">
                                <div class="form-field">
                                    <label class="form-label">会员价</label>
                                    <div style="padding-top: 12px;">
                                        <input type="radio" name="is_member" value="1" title="参与会员价" checked>
                                        <input type="radio" name="is_member" value="0" title="不参与会员价">
                                    </div>
                                    <div class="form-help">
                                        <i class="layui-icon layui-icon-tips form-help-icon"></i>
                                        根据会员等级自动打折
                                    </div>
                                </div>

                                <div class="form-field">
                                    <label class="form-label">商品排序</label>
                                    <input type="number" name="sort" class="form-input"
                                           placeholder="排序值" min="0" value="255">
                                    <div class="form-help">
                                        <i class="layui-icon layui-icon-tips form-help-icon"></i>
                                        数值越小排序越靠前
                                    </div>
                                </div>
                            </div>

                            <div class="form-grid form-grid-2">
                                <div class="form-field">
                                    <label class="form-label">店内推荐</label>
                                    <div style="padding-top: 12px;">
                                        <input type="radio" name="is_recommend" value="1" title="推荐">
                                        <input type="radio" name="is_recommend" value="0" title="不推荐" checked>
                                    </div>
                                </div>

                                <div class="form-field">
                                    <label class="form-label required">销售状态</label>
                                    <div style="padding-top: 12px;">
                                        <input type="radio" name="status" value="1" title="立即上架">
                                        <input type="radio" name="status" value="0" title="放入仓库" checked>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 操作按钮 -->
            <div class="form-actions" style="padding: 24px 32px; border-top: 1px solid var(--gray-200); background: var(--gray-50);">
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <button type="button" class="btn btn-secondary" id="prevBtn" style="display: none;">
                        <i class="layui-icon layui-icon-left"></i>
                        上一步
                    </button>
                    <div></div>
                    <div style="display: flex; gap: 12px;">
                        <button type="button" class="btn btn-secondary" id="saveBtn">
                            <i class="layui-icon layui-icon-file"></i>
                            保存草稿
                        </button>
                        <button type="button" class="btn btn-primary" id="nextBtn">
                            下一步
                            <i class="layui-icon layui-icon-right"></i>
                        </button>
                        <button type="submit" class="btn btn-success" id="submitBtn" style="display: none;">
                            <i class="layui-icon layui-icon-ok"></i>
                            提交商品
                        </button>
                    </div>
                </div>
            </div>

            <!-- 隐藏字段 -->
            <input type="hidden" name="goods_id">
            <input type="hidden" name="type" value="0">
            <input type="hidden" name="image" id="mainImageInput">
        </div>
    </form>
</div>
