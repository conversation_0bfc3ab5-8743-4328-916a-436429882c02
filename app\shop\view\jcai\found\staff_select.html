<div id="staffSelectModal" style="display: none;">
    <div class="layui-card">
        <div class="layui-card-body">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">搜索：</label>
                    <div class="layui-input-inline">
                        <input type="text" id="staffSearch" placeholder="请输入手机号或昵称" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <button class="layui-btn" id="searchStaff">搜索</button>
                </div>
            </div>
            <table id="staffTable" lay-filter="staffTable"></table>
        </div>
    </div>
</div>

<script>
layui.use(['table', 'form'], function(){
    var table = layui.table;
    var form = layui.form;

    // 初始化表格
    table.render({
        elem: '#staffTable',
        url: '{:url("Shop/staffList")}',
        cols: [[
            {type: 'radio'},
            {field: 'nickname', title: '昵称', width: 150},
            {field: 'mobile', title: '手机号', width: 150},
            {field: 'realname', title: '真实姓名', width: 150}
        ]],
        page: true,
        limit: 10,
        limits: [10, 20, 30]
    });

    // 搜索事件
    $('#searchStaff').on('click', function(){
        var keyword = $('#staffSearch').val();
        table.reload('staffTable', {
            where: {
                keyword: keyword
            }
        });
    });

    // 监听行单选
    table.on('radio(staffTable)', function(obj){
        var data = obj.data;
        // 这里可以添加选择后的处理逻辑
        console.log(data);
    });
});
</script>
