<?php

namespace app\admin\controller\content;

use app\admin\logic\content\LearningCenterCategoryLogic;
use app\admin\logic\content\LearningCenterLogic;
use app\admin\validate\content\LearningCenterValidate;
use app\common\basics\AdminBase;
use app\common\server\JsonServer;

class LearningCenterController extends AdminBase
{
    /**
     * @NOTES: 学习中心列表
     * @author: Trae
     */
    public function lists()
    {
        if ($this->request->isAjax()) {
            $get = $this->request->get();
            $lists = LearningCenterLogic::lists($get);
            return JsonServer::success("获取成功", $lists);
        }

        return view('', [
            'category' => LearningCenterCategoryLogic::getCategory()
        ]);
    }

    /**
     * @NOTES: 添加学习中心内容
     * @author: Trae
     */
    public function add()
    {
        if ($this->request->isAjax()) {
            (new LearningCenterValidate())->goCheck('add');
            $post = $this->request->post();
            $res = LearningCenterLogic::add($post);
            if ($res === false) {
                $error = LearningCenterLogic::getError() ?: '新增失败';
                return JsonServer::error($error);
            }
            return JsonServer::success('新增成功');
        }

        return view('', [
            'category' => LearningCenterCategoryLogic::getCategory()
        ]);
    }

    /**
     * @NOTES: 编辑学习中心内容
     * @author: Trae
     */
    public function edit()
    {
        if ($this->request->isAjax()) {
            (new LearningCenterValidate())->goCheck('edit');
            $post = $this->request->post();
            $res = LearningCenterLogic::edit($post);
            if ($res === false) {
                $error = LearningCenterLogic::getError() ?: '编辑失败';
                return JsonServer::error($error);
            }
            return JsonServer::success('编辑成功');
        }

        $id = $this->request->get('id');
        return view('', [
            'detail'   => LearningCenterLogic::detail($id),
            'category' => LearningCenterCategoryLogic::getCategory()
        ]);
    }

    /**
     * @NOTES: 删除学习中心内容
     * @author: Trae
     */
    public function del()
    {
        if ($this->request->isAjax()) {
            (new LearningCenterValidate())->goCheck('id');
            $id = $this->request->post('id');
            $res = LearningCenterLogic::del($id);
            if ($res === false) {
                $error = LearningCenterLogic::getError() ?: '删除失败';
                return JsonServer::error($error);
            }
            return JsonServer::success('删除成功');
        }

        return JsonServer::error('异常');
    }

    /**
     * @Notes: 切换显示状态
     * @Author: Trae
     */
    public function switchShow()
    {
        if ($this->request->isAjax()) {
            (new LearningCenterValidate())->goCheck('id');
            $id = $this->request->post('id');
            $res = LearningCenterLogic::switchShow($id);
            if ($res === false) {
                $error = LearningCenterLogic::getError() ?: '操作失败';
                return JsonServer::error($error);
            }
            return JsonServer::success('操作成功');
        }

        return JsonServer::error('异常');
    }
}