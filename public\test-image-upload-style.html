<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片上传样式修复测试</title>
    <link rel="stylesheet" href="/static/admin/css/like.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #fafafa;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #4e8bff;
            padding-bottom: 10px;
        }
        .layui-form-item {
            margin-bottom: 15px;
        }
        .layui-form-label {
            display: inline-block;
            width: 100px;
            font-weight: bold;
            color: #333;
            text-align: right;
            padding-right: 10px;
        }
        .layui-input-block {
            margin-left: 120px;
        }
        .form-label-asterisk {
            color: #ff4d4f;
            margin-right: 4px;
        }
        .comparison {
            display: flex;
            gap: 20px;
            margin: 20px 0;
        }
        .comparison > div {
            flex: 1;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .comparison .before {
            background: #fff2f0;
            border-color: #ffccc7;
        }
        .comparison .after {
            background: #f6ffed;
            border-color: #b7eb8f;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .status.success {
            background: #f6ffed;
            color: #52c41a;
            border: 1px solid #b7eb8f;
        }
        .status.error {
            background: #fff2f0;
            color: #ff4d4f;
            border: 1px solid #ffccc7;
        }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            padding: 5px 0;
            border-bottom: 1px solid #eee;
        }
        .feature-list li:before {
            content: "✅ ";
            color: #52c41a;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🖼️ 图片上传样式修复测试</h1>
        <p>这个页面用于测试修复后的图片上传区域样式，确保"+ 添加图片"按钮显示正常。</p>
        
        <div class="status success">
            ✅ 样式修复已完成！图片上传区域现在应该显示正常。
        </div>
        
        <div class="test-section">
            <h3>🎯 修复后的图片上传区域</h3>
            <div class="layui-form-item">
                <label class="layui-form-label"><span class="form-label-asterisk">*</span>商品图片：</label>
                <div class="layui-input-block" id="goodsImageContainer">
                    <div class="like-upload-image sortable-upload-container">
                        <div class="upload-image-elem">
                            <a class="add-upload-image" id="goodsimage"> + 添加图片</a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label"></label>
                <span style="color: #a3a3a3;font-size: 12px">建议尺寸：800*800像素，最多上传6张，第一张为主图（封面图），支持拖拽排序</span>
            </div>
        </div>
        
        <div class="test-section">
            <h3>🔧 修复内容说明</h3>
            <div class="comparison">
                <div class="before">
                    <h4>❌ 修复前的问题</h4>
                    <ul>
                        <li>按钮位置偏移，显示在框外</li>
                        <li>字体太小，难以看清</li>
                        <li>没有背景色，不够醒目</li>
                        <li>缺少悬停效果</li>
                        <li>边框样式不统一</li>
                    </ul>
                </div>
                <div class="after">
                    <h4>✅ 修复后的改进</h4>
                    <ul class="feature-list">
                        <li>按钮居中显示，位置准确</li>
                        <li>字体大小适中，清晰可读</li>
                        <li>添加背景色和边框，更加醒目</li>
                        <li>悬停时有颜色变化和缩放效果</li>
                        <li>统一的圆角和过渡动画</li>
                        <li>改善了整体视觉体验</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>📝 技术细节</h3>
            <h4>修复的CSS样式：</h4>
            <pre style="background: #f8f9fa; padding: 15px; border-radius: 4px; overflow-x: auto;"><code>/* 修复前 */
.add-upload-image { 
    top: 58px; 
    right: -10%; 
    width: 100px; 
    font-size: 8px; 
}

/* 修复后 */
.add-upload-image { 
    top: 50%; 
    left: 50%; 
    transform: translate(-50%, -50%); 
    width: 70px; 
    font-size: 12px; 
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid #4e8bff;
    transition: all 0.3s ease;
}

.add-upload-image:hover {
    background: #4e8bff;
    color: white;
    transform: translate(-50%, -50%) scale(1.05);
}</code></pre>
        </div>
        
        <div class="test-section">
            <h3>🎨 其他改进</h3>
            <ul class="feature-list">
                <li>上传区域添加了背景色和圆角</li>
                <li>悬停时边框颜色会变化</li>
                <li>背景图标大小优化</li>
                <li>整体视觉效果更加现代化</li>
            </ul>
        </div>
        
        <div class="test-section">
            <h3>🧪 测试其他上传区域</h3>
            
            <div class="layui-form-item">
                <label class="layui-form-label">分享海报：</label>
                <div class="layui-input-block" id="posterContainer">
                    <div class="like-upload-image">
                        <div class="upload-image-elem">
                            <a class="add-upload-image" id="poster"> + 添加图片</a>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">规格图片：</label>
                <div class="layui-input-block">
                    <div class="like-upload-image">
                        <div class="upload-image-elem">
                            <a class="add-upload-image"> + 添加图片</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="status success">
            🎉 修复完成！现在所有的图片上传区域都应该显示正常了。
        </div>
    </div>

    <script>
        // 添加点击效果演示
        document.querySelectorAll('.add-upload-image').forEach(function(btn) {
            btn.addEventListener('click', function(e) {
                e.preventDefault();
                alert('图片上传功能演示\n\n在实际使用中，这里会打开文件选择对话框。\n\n当前按钮样式已修复：\n✅ 居中显示\n✅ 字体清晰\n✅ 悬停效果\n✅ 视觉美观');
            });
        });
        
        // 页面加载完成提示
        window.addEventListener('load', function() {
            console.log('🎨 图片上传样式修复测试页面已加载');
            console.log('✅ 所有样式修复已应用');
        });
    </script>
</body>
</html>
