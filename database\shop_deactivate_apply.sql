CREATE TABLE `la_shop_deactivate_apply` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `shop_id` int(11) unsigned NOT NULL COMMENT '店铺ID',
  `admin_id` int(11) unsigned NOT NULL COMMENT '管理员ID',
  `reason` varchar(500) NOT NULL DEFAULT '' COMMENT '注销原因',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态：0待审核 1已通过 2已拒绝',
  `audit_time` int(11) unsigned DEFAULT NULL COMMENT '审核时间',
  `audit_remark` varchar(255) DEFAULT '' COMMENT '审核备注',
  `create_time` int(11) unsigned NOT NULL COMMENT '创建时间',
  `ip` varchar(15) NOT NULL DEFAULT '' COMMENT 'IP地址',
  PRIMARY KEY (`id`),
  KEY `idx_shop_id` (`shop_id`),
  KEY `idx_admin_id` (`admin_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='店铺注销申请表';

CREATE TABLE `la_shop_operation_log` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `shop_id` int(11) unsigned NOT NULL COMMENT '店铺ID',
  `admin_id` int(11) unsigned NOT NULL COMMENT '管理员ID',
  `action` varchar(100) NOT NULL COMMENT '操作行为',
  `ip` varchar(15) NOT NULL DEFAULT '' COMMENT 'IP地址',
  `create_time` int(11) unsigned NOT NULL COMMENT '创建时间',
  `data` text COMMENT '操作数据',
  PRIMARY KEY (`id`),
  KEY `idx_shop_id` (`shop_id`),
  KEY `idx_admin_id` (`admin_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='店铺操作日志表';