{layout name="layout1" /}
<div class="wrapper">
    <div class="layui-card">
        <!-- 操作提示 -->
        <div class="layui-card-body">
            <div class="layui-collapse" style="border:1px dashed #c4c4c4">
                <div class="layui-colla-item">
                    <h2 class="layui-colla-title like-layui-colla-title">操作提示</h2>
                    <div class="layui-colla-content layui-show">
                        <p>*商家提交需要参与活动专区的商品，平台审核通过之后即可参与活动。</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 搜索区域 -->
        <div class="layui-card-body layui-form">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">商家名称:</label>
                    <div class="layui-input-block">
                        <input type="text" name="shop_name" id="shop_name" placeholder="请输入商家名称"
                               autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">商品名称:</label>
                    <div class="layui-input-block">
                        <input type="text" name="goods_name" id="goods_name" placeholder="请输入商品名称"
                               autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">活动专区:</label>
                    <div class="layui-input-block">
                        <select name="activity_area" id="activity_area">
                            <option value="">全部</option>
                            {foreach $activity_area as $item => $val}
                            <option value="{$val.id}">{$val.name}</option>
                            {/foreach}
                        </select>
                    </div>
                </div>
                <div class="layui-inline" style="margin-left: 20px;">
                    <button class="layui-btn layui-btn-sm layuiadmin-btn-ad {$view_theme_color}" lay-submit
                            lay-filter="list-search">查询
                    </button>
                    <button class="layui-btn layui-btn-sm layuiadmin-btn-ad layui-btn-primary " lay-submit
                            lay-filter="list-clear-search">清空查询
                    </button>
                </div>
            </div>
        </div>

        <!-- 主体区域 -->
        <div class="layui-card-body">
            <div class="layui-tab layui-tab-card" lay-filter="lists-tab">
                <ul class="layui-tab-title">
                    <li class="layui-this" data-type="1">活动商品({$num.audit_pass})</li>
                    <li data-type="0">待审核商品({$num.unaudit})</li>
                    <li data-type="2">审核拒绝商品({$num.audit_refund})</li>
                </ul>
                <div class="layui-card-body">
                    <!--表格-->
                    <table id="lists" lay-filter="lists"></table>
                    <script type="text/html" id="lists-operation">
                        <a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="detail"><i
                                class="layui-icon"></i>详情</a>
                        {{# if('待审核' == d.audit_status){ }}
                        <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="audit"><i
                                class="layui-icon"></i>审核</a>
                        {{# } }}
                        {{# if('审核通过' == d.audit_status){ }}
                        <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="violation"><i class="layui-icon"></i>违规重审</a>
                        {{# } }}
                    </script>
                </div>
            </div>
        </div>

    </div>


</div>

<script>
    layui.use(['table'], function () {
        var form = layui.form
            , table = layui.table,
            type = 1
            , element = layui.element;

        //监听搜索
        form.on('submit(list-search)', function (data) {
            var field = data.field;
            //执行重载
            table.reload('lists', {
                where: field
            });
        });
        //清空查询
        form.on('submit(list-clear-search)', function () {
            $('#shop_name').val('');
            $('#goods_name').val('');
            $('#activity_area').val('');

            form.render('select');
            //刷新列表
            table.reload('lists', {
                where: [],
                page: {
                    curr: 1
                },
            });
        });

        element.on('tab(lists-tab)', function (data) {
            type = $(this).attr('data-type');
            table.reload('lists', {
                where: {type: type}
            });
        });

        //事件
        var active = {};
        $('.layui-btn.layuiadmin-btn-handle').on('click', function () {
            var type = $(this).data('type');
            active[type] ? active[type].call(this) : '';
        });


        like.tableLists('#lists', '{:url("activity_area.goods/lists")}', [
            {type: 'numbers', title: '序号', align: 'center'}
            , {field: 'shop_name', title: '商家名称', align: "center"}
            , {field: 'name', title: '商品名称', align: "center"}
            , {field: 'min_price', title: '商品最低价格', align: "center"}
            , {field: 'max_price', title: '商品最高价格', align: "center"}
            , {field: 'activity_area_name', title: '参与专区', align: "center"}
            , {field: 'audit_status', title: '审核状态', align: "center"}
            , {field: 'audit_remark', title: '审核说明', align: "center"}
            , {title: '操作', align: 'center', fixed: 'right', toolbar: '#lists-operation'}
        ], {type: type});


        //监听工具条
        table.on('tool(lists)', function (obj) {
            if (obj.event === 'violation') {
                var id = obj.data.id;
                console.log(obj.data)
                layer.open({
                    type: 2
                    , title: '违规重审'
                    , content: '{:url("activity_area.goods/violation")}?id=' + id
                    , area: ['60%', '60%']
                    , btn: ['确定', '取消']
                    , yes: function (index, layero) {
                        var iframeWindow = window['layui-layer-iframe' + index]
                            , submitID = 'addSubmit'
                            , submit = layero.find('iframe').contents().find('#' + submitID);

                        //监听提交
                        iframeWindow.layui.form.on('submit(' + submitID + ')', function (data) {
                            var field = data.field;
                            like.ajax({
                                url: '{:url("activity_area.goods/violation")}',
                                data: field,
                                type: "post",
                                success: function (res) {
                                    if (res.code == 1) {
                                        layui.layer.msg(res.msg, {
                                            offset: '15px'
                                            , icon: 1
                                            , time: 1000
                                        });
                                        layer.close(index);
                                        table.reload('lists');
                                        window.location.reload();
                                    }
                                }
                            });
                        });

                        submit.trigger('click');
                    }
                })
            } else if (obj.event === 'detail') {
                var id = obj.data.id;
                layer.open({
                    type: 2
                    , title: '活动专区商品详情'
                    , content: '{:url("activity_area.goods/detail")}?id=' + id
                    , area: ['90%', '90%']
                    , btn: ['确定', '取消']
                    , yes: function (index, layero) {
                        var iframeWindow = window['layui-layer-iframe' + index]
                            , submitID = 'edit-submit'
                            , submit = layero.find('iframe').contents().find('#' + submitID);
                        submit.trigger('click');
                    }
                })
            } else if (obj.event === 'audit') {
                var id = obj.data.id;
                console.log(obj.data)
                layer.open({
                    type: 2
                    , title: '审核'
                    , content: '{:url("activity_area.goods/audit")}?id=' + id
                    , area: ['60%', '60%']
                    , btn: ['确定', '取消']
                    , yes: function (index, layero) {
                        var iframeWindow = window['layui-layer-iframe' + index]
                            , submitID = 'addSubmit'
                            , submit = layero.find('iframe').contents().find('#' + submitID);

                        //监听提交
                        iframeWindow.layui.form.on('submit(' + submitID + ')', function (data) {
                            var field = data.field;
                            like.ajax({
                                url: '{:url("activity_area.goods/audit")}',
                                data: field,
                                type: "post",
                                success: function (res) {
                                    if (res.code == 1) {
                                        layui.layer.msg(res.msg, {
                                            offset: '15px'
                                            , icon: 1
                                            , time: 1000
                                        });
                                        layer.close(index);
                                        table.reload('lists');
                                        window.location.reload();
                                    }
                                }
                            });
                        });

                        submit.trigger('click');
                    }
                })
            }
        });
    });
</script>