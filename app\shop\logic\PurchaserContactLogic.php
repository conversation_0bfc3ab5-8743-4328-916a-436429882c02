<?php
namespace app\shop\logic;

use app\common\basics\Logic;
use app\common\model\PurchaserContactRecord;
use app\common\model\community\CommunityArticle;
use app\common\model\user\User;
use app\common\server\UrlServer;
use think\facade\Db;

/**
 * 商家后台采购商联系记录业务逻辑
 * Class PurchaserContactLogic
 * @package app\shop\logic
 */
class PurchaserContactLogic extends Logic
{
    /**
     * 获取商家相关的联系记录
     * @param int $shopId 商家ID
     * @param array $params 查询参数
     * @return array|false
     */
    public static function getShopContactRecords($shopId, $params = [])
    {
        try {
            $page = $params['page'] ?? 1;
            $limit = $params['limit'] ?? 15;
            
            // 构建查询条件
            $where = [];
            
            // 通过community_article表关联，找到属于该商家的采购商被联系记录
            $subQuery = CommunityArticle::where('user_id', $shopId)->column('id');
            if (empty($subQuery)) {
                return [
                    'count' => 0,
                    'lists' => [],
                    'page' => $page,
                    'limit' => $limit
                ];
            }
            
            $where[] = ['pcr.purchaser_id', 'in', $subQuery];
            
            // 时间范围筛选
            if (!empty($params['start_time'])) {
                $where[] = ['pcr.contact_time', '>=', strtotime($params['start_time'])];
            }
            if (!empty($params['end_time'])) {
                $where[] = ['pcr.contact_time', '<=', strtotime($params['end_time'])];
            }
            
            // 联系来源筛选
            if (!empty($params['source'])) {
                $where[] = ['pcr.source', '=', $params['source']];
            }
            
            // 用户筛选
            if (!empty($params['user_id'])) {
                $where[] = ['pcr.user_id', '=', $params['user_id']];
            }
            
            $model = PurchaserContactRecord::alias('pcr')
                ->leftJoin('user u', 'pcr.user_id = u.id')
                ->leftJoin('community_article ca', 'pcr.purchaser_id = ca.id')
                ->where($where)
                ->field('pcr.*,u.nickname,u.avatar,u.mobile,ca.content as purchaser_content,ca.image as purchaser_image');
            
            $count = $model->count();
            $lists = $model->page($page, $limit)
                ->order('pcr.contact_time desc')
                ->select()
                ->toArray();
            
            foreach ($lists as &$item) {
                $item['contact_time_text'] = date('Y-m-d H:i:s', $item['contact_time']);
                $item['avatar'] = UrlServer::getFileUrl($item['avatar']);
                $item['purchaser_image'] = UrlServer::getFileUrl($item['purchaser_image']);
                $item['source_text'] = self::getSourceText($item['source']);
            }
            
            return [
                'count' => $count,
                'lists' => $lists,
                'page' => $page,
                'limit' => $limit
            ];
            
        } catch (\Exception $e) {
            self::$error = '获取失败：' . $e->getMessage();
            return false;
        }
    }

    /**
     * 获取联系记录详情
     * @param int $id 记录ID
     * @param int $shopId 商家ID
     * @return array|false
     */
    public static function getContactDetail($id, $shopId)
    {
        try {
            // 验证记录是否属于该商家
            $record = PurchaserContactRecord::alias('pcr')
                ->leftJoin('user u', 'pcr.user_id = u.id')
                ->leftJoin('community_article ca', 'pcr.purchaser_id = ca.id')
                ->where([
                    'pcr.id' => $id,
                    'ca.user_id' => $shopId
                ])
                ->field('pcr.*,u.nickname,u.avatar,u.mobile,u.create_time as user_create_time,ca.content as purchaser_content,ca.image as purchaser_image,ca.wxqrcode')
                ->findOrEmpty();
            
            if ($record->isEmpty()) {
                self::$error = '记录不存在或无权限查看';
                return false;
            }
            
            $data = $record->toArray();
            $data['contact_time_text'] = date('Y-m-d H:i:s', $data['contact_time']);
            $data['avatar'] = UrlServer::getFileUrl($data['avatar']);
            $data['purchaser_image'] = UrlServer::getFileUrl($data['purchaser_image']);
            $data['wxqrcode'] = UrlServer::getFileUrl($data['wxqrcode']);
            $data['source_text'] = self::getSourceText($data['source']);
            $data['user_create_time_text'] = date('Y-m-d H:i:s', $data['user_create_time']);
            
            return $data;
            
        } catch (\Exception $e) {
            self::$error = '获取详情失败：' . $e->getMessage();
            return false;
        }
    }

    /**
     * 获取商家联系统计
     * @param int $shopId 商家ID
     * @param array $params 查询参数
     * @return array|false
     */
    public static function getShopContactStats($shopId, $params = [])
    {
        try {
            // 获取商家的采购商ID列表
            $purchaserIds = CommunityArticle::where('user_id', $shopId)->column('id');
            
            if (empty($purchaserIds)) {
                return [
                    'total_contacts' => 0,
                    'today_contacts' => 0,
                    'this_week_contacts' => 0,
                    'this_month_contacts' => 0,
                    'source_stats' => [],
                    'daily_stats' => []
                ];
            }
            
            $baseQuery = PurchaserContactRecord::where('purchaser_id', 'in', $purchaserIds);
            
            // 基础统计
            $stats = [
                'total_contacts' => $baseQuery->count(),
                'today_contacts' => $baseQuery->where('contact_time', '>=', strtotime('today'))->count(),
                'this_week_contacts' => $baseQuery->where('contact_time', '>=', strtotime('this week'))->count(),
                'this_month_contacts' => $baseQuery->where('contact_time', '>=', strtotime('first day of this month'))->count(),
            ];
            
            // 来源统计
            $sourceStats = PurchaserContactRecord::where('purchaser_id', 'in', $purchaserIds)
                ->field('source, count(*) as count')
                ->group('source')
                ->select()
                ->toArray();
            
            foreach ($sourceStats as &$item) {
                $item['source_text'] = self::getSourceText($item['source']);
            }
            $stats['source_stats'] = $sourceStats;
            
            // 最近7天每日统计
            $dailyStats = [];
            for ($i = 6; $i >= 0; $i--) {
                $date = date('Y-m-d', strtotime("-{$i} days"));
                $startTime = strtotime($date);
                $endTime = $startTime + 86400 - 1;
                
                $count = PurchaserContactRecord::where('purchaser_id', 'in', $purchaserIds)
                    ->where('contact_time', 'between', [$startTime, $endTime])
                    ->count();
                
                $dailyStats[] = [
                    'date' => $date,
                    'count' => $count
                ];
            }
            $stats['daily_stats'] = $dailyStats;
            
            return $stats;
            
        } catch (\Exception $e) {
            self::$error = '获取统计失败：' . $e->getMessage();
            return false;
        }
    }

    /**
     * 导出联系记录
     * @param int $shopId 商家ID
     * @param array $params 查询参数
     * @return array|false
     */
    public static function exportShopContactRecords($shopId, $params = [])
    {
        try {
            // 获取所有记录（不分页）
            $params['page'] = 1;
            $params['limit'] = 10000; // 设置一个较大的限制
            
            $result = self::getShopContactRecords($shopId, $params);
            
            if ($result === false) {
                return false;
            }
            
            // 这里可以实现具体的导出逻辑，比如生成Excel文件
            // 暂时返回数据，具体导出功能可以后续完善
            return [
                'filename' => '联系记录_' . date('YmdHis') . '.csv',
                'data' => $result['lists']
            ];
            
        } catch (\Exception $e) {
            self::$error = '导出失败：' . $e->getMessage();
            return false;
        }
    }

    /**
     * 获取来源文本
     * @param string $source
     * @return string
     */
    private static function getSourceText($source)
    {
        $sourceMap = [
            'web' => '网页',
            'app' => '应用',
            'mini' => '小程序',
            'h5' => 'H5页面'
        ];
        
        return $sourceMap[$source] ?? $source;
    }
}
