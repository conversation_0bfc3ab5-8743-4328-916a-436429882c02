import{v as e,o as a,c as t,w as l,a as s,n as i,b as o,t as n,d as r,i as c,r as u,l as d,$ as f,e as p,f as m,g,h,j as v,k as y,F as _,m as b,p as x,q as k,u as w,s as C}from"./index-B6kWyIrN.js";import{_ as S,o as z,a as N,b as j,c as B,r as F,d as T}from"./uv-icon.D6fiO-QB.js";import{_ as V}from"./zb-popover.CqttEQzx.js";import{_ as $}from"./z-paging.X4eWfR5C.js";const I=S({name:"u-switch",props:{loading:{type:Boolean,default:!1},firstText:{type:[Number,String],default:"在线"},lastText:{type:[Number,String],default:"离线"},disabled:{type:Boolean,default:!1},activeColor:{type:String,default:"#f2f2f2"},inactiveColor:{type:String,default:"#ffffff"},value:{type:[Number,String],default:0},vibrateShort:{type:Boolean,default:!0},activeValue:{type:[Number,String,Boolean],default:!0},inactiveValue:{type:[Number,String,Boolean],default:!1},size:{type:Number,default:36}},data:()=>({}),computed:{switchStyle(){let e={};return e.fontSize=this.size+"rpx",e.backgroundColor=this.value?this.activeColor:this.inactiveColor,e},loadingColor(){return this.value?this.activeColor:null}},methods:{onClick(){this.disabled||this.loading||(this.vibrateShort&&e(),this.$emit("input",1==this.value?0:1),this.$nextTick((()=>{this.$emit("change",{value:this.value})})))}}},[["render",function(e,u,d,f,p,m){const g=c;return a(),t(g,{class:"switch",onClick:m.onClick,style:r([m.switchStyle])},{default:l((()=>[s(g,{class:"switch--bg flex row-around"},{default:l((()=>[s(g,{class:"switch--text sm flex row-left p-l-20"},{default:l((()=>[s(g,{class:i([1==d.value?"ani2":"ani1","primary"])},{default:l((()=>[o(n(d.firstText),1)])),_:1},8,["class"]),s(g,{class:i([0==d.value?"ani2":"ani1","lighter"])},{default:l((()=>[o(n(d.lastText),1)])),_:1},8,["class"])])),_:1}),s(g,{class:i(["switch--slider br60",1==d.value?"open":"close"])},null,8,["class"])])),_:1})])),_:1},8,["onClick","style"])}],["__scopeId","data-v-fea543b1"]]),q=S({__name:"sessionlist",setup(e){const i=[{text:"退出登录"}],r=u(null),S=u([]),q=u({});u(0);const H=u(!0);async function P(e){console.log(e);const{code:a,data:t}=await b();1==a&&(d.clear(),x({url:"/pages/login/login"}))}async function Q(e){q.value.online=1==e.value?0:1}u([{text:"删除",style:{backgroundColor:"#f56c6c"}}]),z((function(e){e.token&&(d.set("token",e.token),f("updateSocket"),H.value=!1),async function(){const{code:e,data:a}=await p();1==e&&(q.value=a,d.set("serveinfo",a))}()})),N((function(){}));const U=(e,a)=>{g({page_no:e,page_size:a,nickname:""}).then((e=>{r.value.complete(e.data.list)})).catch((e=>{r.value.complete(!1)}))};return j((function(){})),B((function(){})),(e,u)=>{const d=k,f=c,p=F(m("b-switch"),I),g=F(m("uv-icon"),T),b=F(m("zb-popover"),V),x=F(m("z-paging"),$);return a(),t(x,{ref_key:"paging",ref:r,modelValue:S.value,"onUpdate:modelValue":u[0]||(u[0]=e=>S.value=e),onQuery:U},{default:l((()=>[H.value?(a(),t(f,{key:0,class:"",style:{width:"100%",height:"170rpx",overflow:"hidden"}},{default:l((()=>[s(f,{class:"background"},{default:l((()=>[s(f,{style:{padding:"20rpx"}},{default:l((()=>[s(f,{class:"display-flex align-items"},{default:l((()=>[s(d,{src:q.value.avatar,style:{width:"120rpx",height:"120rpx"},class:"border-radius",mode:"aspectFill"},null,8,["src"]),s(f,{class:"display-flex file-1 space-between"},{default:l((()=>[s(f,{class:"file-1 margin-left-20"},{default:l((()=>[s(f,{style:{"-webkit-line-clamp":"1"},class:"font-size-36 webkit-line-clamp color-333 font-weight-bold"},{default:l((()=>[o(" Hi，"+n(q.value.nickname||"-"),1)])),_:1}),s(f,{class:"margin-top-10"},{default:l((()=>[s(p,{onChange:Q,value:q.value.online},null,8,["value"])])),_:1})])),_:1}),s(b,{placement:"left-start",options:i,ref:"Popover1",onSelect:P,class:"item-popover"},{default:l((()=>[s(g,{name:"setting"})])),_:1},512)])),_:1})])),_:1})])),_:1})])),_:1})])),_:1})):h("",!0),s(f,{class:"padding-about-20"},{default:l((()=>[(a(!0),v(_,null,y(S.value,((e,i)=>(a(),t(f,{key:i,onClick:a=>w(C)("/pages/chat/chat?userid="+e.user_id),class:"display-flex align-items",style:{padding:"20rpx 0","border-bottom":"1rpx solid #F5F5F5"}},{default:l((()=>[s(f,{style:{width:"100rpx",height:"100rpx"},class:"position-relative"},{default:l((()=>[s(d,{src:e.avatar,style:{width:"100rpx",height:"100rpx"},class:"border-radius-12",mode:""},null,8,["src"]),e.is_read?(a(),t(f,{key:0,class:"position-absolute transform-translate-center",style:{top:"4%",right:"-15%"}},{default:l((()=>[s(f,{class:"border-radius",style:{border:"solid #f56c6c 10rpx"}})])),_:1})):h("",!0)])),_:2},1024),s(f,{class:"file-1 margin-left-20"},{default:l((()=>[s(f,{class:"display-flex space-between align-items"},{default:l((()=>[s(f,{class:"font-size-28 font-weight-bold"},{default:l((()=>[o(n(e.nickname),1)])),_:2},1024),s(f,{class:"font-size-22 color-999"},{default:l((()=>[o(n(e.create_time),1)])),_:2},1024)])),_:2},1024),s(f,{style:{"-webkit-line-clamp":"1"},class:"font-size-26 webkit-line-clamp margin-top-10"},{default:l((()=>[o(n(e.msg),1)])),_:2},1024)])),_:2},1024)])),_:2},1032,["onClick"])))),128))])),_:1})])),_:1},8,["modelValue"])}}},[["__scopeId","data-v-67ad5293"]]);export{q as default};
