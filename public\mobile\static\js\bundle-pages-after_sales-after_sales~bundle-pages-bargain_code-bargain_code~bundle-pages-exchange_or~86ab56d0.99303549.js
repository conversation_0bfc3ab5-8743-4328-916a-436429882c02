(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["bundle-pages-after_sales-after_sales~bundle-pages-bargain_code-bargain_code~bundle-pages-exchange_or~86ab56d0"],{"017b":function(t,e,n){"use strict";var i=n("048f"),a=n.n(i);a.a},"048a":function(t,e,n){"use strict";n.r(e);var i=n("2344"),a=n.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(r);e["default"]=a.a},"048f":function(t,e,n){var i=n("5c72");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var a=n("4f06").default;a("14f65568",i,!0,{sourceMap:!1,shadowMode:!1})},1080:function(t,e,n){"use strict";n.r(e);var i=n("8670"),a=n("bd8c");for(var r in a)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(r);n("017b");var o=n("f0c5"),s=Object(o["a"])(a["default"],i["b"],i["c"],!1,null,"e9923850",null,!1,i["a"],void 0);e["default"]=s.exports},2036:function(t,e,n){var i=n("5c10");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var a=n("4f06").default;a("25f04d8a",i,!0,{sourceMap:!1,shadowMode:!1})},2344:function(t,e,n){"use strict";n("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("a9e3");var i={name:"u-badge",props:{type:{type:String,default:"error"},size:{type:String,default:"default"},isDot:{type:Boolean,default:!1},count:{type:[Number,String]},overflowCount:{type:Number,default:99},showZero:{type:Boolean,default:!1},offset:{type:Array,default:function(){return[20,20]}},absolute:{type:Boolean,default:!0},fontSize:{type:[String,Number],default:"24"},color:{type:String,default:"#ffffff"},bgColor:{type:String,default:""},isCenter:{type:Boolean,default:!1}},computed:{boxStyle:function(){var t={};return this.isCenter?(t.top=0,t.right=0,t.transform="translateY(-50%) translateX(50%)"):(t.top=this.offset[0]+"rpx",t.right=this.offset[1]+"rpx",t.transform="translateY(0) translateX(0)"),"mini"==this.size&&(t.transform=t.transform+" scale(0.8)"),t},showText:function(){return this.isDot?"":this.count>this.overflowCount?"".concat(this.overflowCount,"+"):this.count},show:function(){return 0!=this.count||0!=this.showZero}}};e.default=i},"271f":function(t,e,n){"use strict";n("7a82");var i=n("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=i(n("f07e")),r=i(n("c964"));n("a9e3"),n("d81d"),n("ac1f"),n("d3b7"),n("159b");var o=n("a5ae"),s={name:"tabs",props:{isScroll:{type:Boolean,default:!0},current:{type:[Number,String],default:0},height:{type:[String,Number],default:80},fontSize:{type:[String,Number],default:28},duration:{type:[String,Number],default:.3},activeColor:{type:String,default:"#FF2C3C"},inactiveColor:{type:String,default:"#333"},barWidth:{type:[String,Number],default:40},barHeight:{type:[String,Number],default:4},gutter:{type:[String,Number],default:30},bgColor:{type:String,default:"#ffffff"},name:{type:String,default:"name"},count:{type:String,default:"count"},offset:{type:Array,default:function(){return[5,20]}},bold:{type:Boolean,default:!0},activeItemStyle:{type:Object,default:function(){return{}}},showBar:{type:Boolean,default:!0},barStyle:{type:Object,default:function(){return{}}},itemWidth:{type:[Number,String],default:"auto"},isFixed:{type:Boolean,default:!1},top:{type:[Number,String],default:0},width:{type:[Number,String],default:"100%"},stickyBgColor:{type:String,default:"#ffffff"},borderRadius:{type:[Number,String],default:0},async:{type:Boolean,default:!1}},provide:function(){return{tabs:this}},data:function(){return{list:[],scrollLeft:0,tabQueryInfo:[],componentWidth:0,scrollBarLeft:0,parentLeft:0,id:"cu-tab",currentIndex:this.current,barFirstTimeMove:!0,isAsync:!1}},watch:{list:function(t,e){var n=this;this.barFirstTimeMove||t.length===e.length||(this.currentIndex=0),this.$nextTick((function(){n.init()}))},current:{immediate:!0,handler:function(t,e){var n=this;this.$nextTick((function(){n.currentIndex=t,n.scrollByIndex()}))}},async:{immediate:!0,handler:function(t,e){this.isAsync=t}}},computed:{tabBarStyle:function(){var t={width:this.barWidth+"rpx",transform:"translate(".concat(this.scrollBarLeft,"px, -100%)"),"transition-duration":"".concat(this.barFirstTimeMove?0:this.duration,"s"),"background-color":this.activeColor,height:this.barHeight+"rpx",opacity:this.barFirstTimeMove?0:1,"border-radius":"".concat(this.barHeight/2,"px")};return Object.assign(t,this.barStyle),t},tabItemStyle:function(){var t=this;return function(e){var n={height:t.height+"rpx","line-height":t.height+"rpx","font-size":t.fontSize+"rpx",padding:t.isScroll?"0 ".concat(t.gutter,"rpx"):"",flex:t.isScroll?"auto":"1",width:"".concat(t.itemWidth,"rpx")};return e==t.currentIndex&&t.bold&&(n.fontWeight="bold"),e==t.currentIndex?(n.color=t.activeColor,n=Object.assign(n,t.activeItemStyle)):n.color=t.inactiveColor,n}}},methods:{updateTabs:function(){this.list=this.childrens.map((function(t){var e=t.name,n=t.dot,i=t.active,a=t.inited,r=t.updateRender;return{name:e,dot:n,active:i,inited:a,updateRender:r}})),this.$nextTick((function(){this.init()}))},init:function(){var t=this;return(0,r.default)((0,a.default)().mark((function e(){var n;return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,(0,o.getRect)("#"+t.id,!1,t);case 2:n=e.sent,t.parentLeft=n.left,t.componentWidth=n.width,t.getTabRect();case 6:case"end":return e.stop()}}),e)})))()},clickTab:function(t){var e=this;this.$emit("change",t),this.isAsync||t!=this.currentIndex&&this.$nextTick((function(){e.currentIndex=t,e.scrollByIndex()}))},getTabRect:function(){for(var t=uni.createSelectorQuery().in(this),e=0;e<this.list.length;e++)t.select("#tab-item-".concat(e)).fields({size:!0,rect:!0});t.exec(function(t){this.tabQueryInfo=t,this.scrollByIndex()}.bind(this))},scrollByIndex:function(){var t=this,e=this.tabQueryInfo[this.currentIndex];if(e){var n=e.width,i=e.left-this.parentLeft,a=i-(this.componentWidth-n)/2;this.scrollLeft=a<0?0:a;var r=e.left+e.width/2-this.parentLeft;this.scrollBarLeft=r-uni.upx2px(this.barWidth)/2,1==this.barFirstTimeMove&&setTimeout((function(){t.barFirstTimeMove=!1}),100),this.childrens.forEach((function(e,n){var i=n===t.currentIndex;i===e.active&&e.inited||e.updateRender(i,t)}))}}},created:function(){this.childrens=[]},mounted:function(){this.updateTabs()}};e.default=s},"431a":function(t,e,n){var i=n("816d");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var a=n("4f06").default;a("5fc1f068",i,!0,{sourceMap:!1,shadowMode:!1})},"501a":function(t,e,n){"use strict";n.r(e);var i=n("271f"),a=n.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(r);e["default"]=a.a},"520f":function(t,e,n){"use strict";n.r(e);var i=n("d57a"),a=n("bf91");for(var r in a)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(r);n("b532");var o=n("f0c5"),s=Object(o["a"])(a["default"],i["b"],i["c"],!1,null,"1ce6e490",null,!1,i["a"],void 0);e["default"]=s.exports},"5c10":function(t,e,n){var i=n("24fb");e=i(!1),e.push([t.i,".tab.active[data-v-1ce6e490]{height:auto}.tab.inactive[data-v-1ce6e490]{height:0;overflow:visible}",""]),t.exports=e},"5c72":function(t,e,n){var i=n("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.u-sticky[data-v-e9923850]{z-index:9999999999}',""]),t.exports=e},7131:function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"c",(function(){return r})),n.d(e,"a",(function(){return i}));var i={uSticky:n("1080").default,uBadge:n("c93e").default},a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{staticClass:"tabs"},[n("u-sticky",{attrs:{enable:t.isFixed,"bg-color":t.stickyBgColor,"offset-top":t.top,"h5-nav-height":0}},[n("v-uni-view",{style:{background:t.bgColor,"border-radius":t.borderRadius,width:t.width,margin:"0 auto"},attrs:{id:t.id}},[n("v-uni-scroll-view",{staticClass:"scroll-view",style:{height:t.height+"rpx"},attrs:{"scroll-x":!0,"scroll-left":t.scrollLeft,"scroll-with-animation":!0}},[n("v-uni-view",{staticClass:"scroll-box",class:{"tabs-scorll-flex":!t.isScroll}},[t._l(t.list,(function(e,i){return n("v-uni-view",{key:i,staticClass:"tab-item line1",style:[t.tabItemStyle(i)],attrs:{id:"tab-item-"+i},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.clickTab(i)}}},[n("u-badge",{attrs:{count:e[t.count]||e["dot"]||0,offset:t.offset,size:"mini"}}),t._v(t._s(e[t.name]||e["name"]))],1)})),t.showBar?n("v-uni-view",{staticClass:"tab-bar",style:[t.tabBarStyle]}):t._e()],2)],1)],1)],1),n("v-uni-view",{staticClass:"tab-content"},[n("v-uni-view",[t._t("default")],2)],1)],1)},r=[]},"72d8":function(t,e,n){"use strict";var i=n("431a"),a=n.n(i);a.a},"7c34":function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return a})),n.d(e,"a",(function(){}));var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return t.show?n("v-uni-view",{staticClass:"u-badge",class:[t.isDot?"u-badge-dot":"","mini"==t.size?"u-badge-mini":"",t.type?"u-badge--bg--"+t.type:""],style:[{top:t.offset[0]+"rpx",right:t.offset[1]+"rpx",fontSize:t.fontSize+"rpx",position:t.absolute?"absolute":"static",color:t.color,backgroundColor:t.bgColor},t.boxStyle]},[t._v(t._s(t.showText))]):t._e()},a=[]},"816d":function(t,e,n){var i=n("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.u-badge[data-v-629aeef1]{display:inline-flex;justify-content:center;align-items:center;line-height:%?24?%;padding:%?4?% %?8?%;border-radius:%?100?%;z-index:9}.u-badge--bg--primary[data-v-629aeef1]{background-color:#ff2c3c}.u-badge--bg--error[data-v-629aeef1]{background-color:#fa3534}.u-badge--bg--success[data-v-629aeef1]{background-color:#19be6b}.u-badge--bg--info[data-v-629aeef1]{background-color:#909399}.u-badge--bg--warning[data-v-629aeef1]{background-color:#f90}.u-badge-dot[data-v-629aeef1]{height:%?16?%;width:%?16?%;border-radius:%?100?%;line-height:1}.u-badge-mini[data-v-629aeef1]{-webkit-transform:scale(.8);transform:scale(.8);-webkit-transform-origin:center center;transform-origin:center center}.u-info[data-v-629aeef1]{background-color:#909399;color:#fff}',""]),t.exports=e},8670:function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return a})),n.d(e,"a",(function(){}));var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{},[n("v-uni-view",{staticClass:"u-sticky-wrap",class:[t.elClass],style:{height:t.fixed?t.height+"px":"auto",backgroundColor:t.bgColor}},[n("v-uni-view",{staticClass:"u-sticky",style:{position:t.fixed?"fixed":"static",top:t.stickyTop+"px",left:t.left+"px",width:"auto"==t.width?"auto":t.width+"px",zIndex:t.uZIndex}},[t._t("default")],2)],1)],1)},a=[]},"9ad5":function(t,e,n){"use strict";n.r(e);var i=n("7131"),a=n("501a");for(var r in a)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(r);n("fad5");var o=n("f0c5"),s=Object(o["a"])(a["default"],i["b"],i["c"],!1,null,"a8fee2ac",null,!1,i["a"],void 0);e["default"]=s.exports},b3c1:function(t,e,n){"use strict";n("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("a9e3"),n("14d9");var i={props:{dot:{type:Boolean},name:{type:[Number,String],value:""}},inject:["tabs"],data:function(){return{active:!1,shouldShow:!1,shouldRender:!1}},created:function(){this.tabs.childrens.push(this)},mounted:function(){this.update()},methods:{getComputedName:function(){return""!==this.data.name?this.data.name:this.index},updateRender:function(t,e){this.inited=this.inited||t,this.active=t,this.shouldRender=this.inited,this.shouldShow=t},update:function(){this.tabs&&this.tabs.updateTabs()}},computed:{changeData:function(){var t=this.dot,e=this.info;return{dot:t,info:e}}},watch:{changeData:function(t){this.update()},name:function(t){this.update()}}};e.default=i},b532:function(t,e,n){"use strict";var i=n("2036"),a=n.n(i);a.a},bd8c:function(t,e,n){"use strict";n.r(e);var i=n("e4b1"),a=n.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(r);e["default"]=a.a},bf91:function(t,e,n){"use strict";n.r(e);var i=n("b3c1"),a=n.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(r);e["default"]=a.a},c781:function(t,e,n){var i=n("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */[data-v-a8fee2ac]::-webkit-scrollbar,[data-v-a8fee2ac]::-webkit-scrollbar,[data-v-a8fee2ac]::-webkit-scrollbar{display:none;width:0!important;height:0!important;-webkit-appearance:none;background:transparent}.scroll-box[data-v-a8fee2ac]{height:100%;position:relative}.tab-fixed[data-v-a8fee2ac]{position:-webkit-sticky;position:sticky;top:0;width:100%}uni-scroll-view[data-v-a8fee2ac]  ::-webkit-scrollbar{display:none;width:0!important;height:0!important;-webkit-appearance:none;background:transparent}.scroll-view[data-v-a8fee2ac]{width:100%;white-space:nowrap;position:relative}.tab-item[data-v-a8fee2ac]{position:relative;display:inline-block;text-align:center;transition-property:background-color,color}.tab-bar[data-v-a8fee2ac]{position:absolute;bottom:%?6?%}.tabs-scorll-flex[data-v-a8fee2ac]{display:flex;justify-content:space-between}',""]),t.exports=e},c93e:function(t,e,n){"use strict";n.r(e);var i=n("7c34"),a=n("048a");for(var r in a)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(r);n("72d8");var o=n("f0c5"),s=Object(o["a"])(a["default"],i["b"],i["c"],!1,null,"629aeef1",null,!1,i["a"],void 0);e["default"]=s.exports},d57a:function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return a})),n.d(e,"a",(function(){}));var i=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{class:{active:this.active,inactive:!this.active,tab:!0},style:this.shouldShow?"":"display: none;"},[this.shouldRender?this._t("default"):this._e()],2)},a=[]},e4b1:function(t,e,n){"use strict";n("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("a9e3"),n("2c3e"),n("e25e");var i={name:"u-sticky",props:{offsetTop:{type:[Number,String],default:0},index:{type:[Number,String],default:""},enable:{type:Boolean,default:!0},h5NavHeight:{type:[Number,String],default:44},bgColor:{type:String,default:"#ffffff"},zIndex:{type:[Number,String],default:""}},data:function(){return{fixed:!1,height:"auto",stickyTop:0,elClass:this.$u.guid(),left:0,width:"auto"}},watch:{offsetTop:function(t){this.initObserver()},enable:function(t){0==t?(this.fixed=!1,this.disconnectObserver("contentObserver")):this.initObserver()}},computed:{uZIndex:function(){return this.zIndex?this.zIndex:this.$u.zIndex.sticky}},mounted:function(){this.initObserver()},methods:{initObserver:function(){var t=this;if(this.enable){var e="string"==typeof this.offsetTop?parseInt(this.offsetTop):uni.upx2px(this.offsetTop);this.stickyTop=0!=this.offsetTop?e+this.h5NavHeight:this.h5NavHeight,this.disconnectObserver("contentObserver"),this.$nextTick((function(){t.$uGetRect("."+t.elClass).then((function(e){t.height=e.height,t.left=e.left,t.width=e.width,t.$nextTick((function(){t.observeContent()}))}))}))}},observeContent:function(){var t=this;this.disconnectObserver("contentObserver");var e=this.createIntersectionObserver({thresholds:[.95,.98,1]});e.relativeToViewport({top:-this.stickyTop}),e.observe("."+this.elClass,(function(e){t.enable&&t.setFixed(e.boundingClientRect.top)})),this.contentObserver=e},setFixed:function(t){var e=t<this.stickyTop;e?this.$emit("fixed",this.index):this.fixed&&this.$emit("unfixed",this.index),this.fixed=e},disconnectObserver:function(t){var e=this[t];e&&e.disconnect()}},beforeDestroy:function(){this.disconnectObserver("contentObserver")}};e.default=i},f375:function(t,e,n){var i=n("c781");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var a=n("4f06").default;a("59049762",i,!0,{sourceMap:!1,shadowMode:!1})},fad5:function(t,e,n){"use strict";var i=n("f375"),a=n.n(i);a.a}}]);