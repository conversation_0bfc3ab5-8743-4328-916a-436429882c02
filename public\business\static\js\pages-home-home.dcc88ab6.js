(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-home-home"],{"3fcd":function(e,n,t){"use strict";t("6a54");var r=t("f5bd").default;Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0,t("5c47"),t("0506");var a=r(t("4dbe")),i={data:function(){return{}},methods:{},onLoad:function(){var e=window.navigator.userAgent.toLowerCase();/micromessenger/.test(e)&&a.default.miniProgram.getEnv((function(e){e.miniprogram&&a.default.miniProgram.switchTab({url:"/pages/Home/home/<USER>"})}))}};n.default=i},8594:function(e,n,t){"use strict";t.r(n);var r=t("b411"),a=t("89ef");for(var i in a)["default"].indexOf(i)<0&&function(e){t.d(n,e,(function(){return a[e]}))}(i);var u=t("828b"),o=Object(u["a"])(a["default"],r["b"],r["c"],!1,null,"0ae14138",null,!1,r["a"],void 0);n["default"]=o.exports},"89ef":function(e,n,t){"use strict";t.r(n);var r=t("3fcd"),a=t.n(r);for(var i in r)["default"].indexOf(i)<0&&function(e){t.d(n,e,(function(){return r[e]}))}(i);n["default"]=a.a},b411:function(e,n,t){"use strict";t.d(n,"b",(function(){return r})),t.d(n,"c",(function(){return a})),t.d(n,"a",(function(){}));var r=function(){var e=this.$createElement,n=this._self._c||e;return n("v-uni-view",[n("v-uni-image",{attrs:{src:"",mode:""}})],1)},a=[]}}]);