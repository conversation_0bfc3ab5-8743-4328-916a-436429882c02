<?php



namespace app\common\enum;


class ChatMsgEnum
{

    const TYPE_TEXT = 1; // 文本
    const TYPE_IMG = 2; // 图片
    const TYPE_GOODS = 3; // 商品
    const TYPE_VOICE = 4; // 语音消息
    const TYPE_VIDEO = 5; // 视频消息
    const TYPE_ORDER = 6; // 订单消息


    public static function getMsgType($type = true)
    {
        $desc = [
            self::TYPE_TEXT => '文本',
            self::TYPE_IMG => '图片',
            self::TYPE_GOODS => '商品',
            self::TYPE_VOICE => '语音',
            self::TYPE_VIDEO => '视频',
            self::TYPE_ORDER => '订单',
        ];
        if ($type === true) {
            return $desc;
        }
        return $desc[$type] ?? '';
    }

}