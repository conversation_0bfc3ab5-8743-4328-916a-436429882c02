<?php

namespace app\api\controller;

use app\admin\logic\WechatMerchantTransferLogic;
use app\common\basics\Api;
use app\common\model\WithdrawApply;
use app\api\controller\Notification; // 引入Notification控制器
use app\api\controller\Websocket; // 引入Websocket控制器
use think\facade\Request; // 引入Request类
use think\facade\Log; // 引入日志类
use app\common\server\ConfigServer;


/**
 * 测试
 * Class Test
 * @package app\api\controller
 */
class Test extends Api
{
    public $like_not_need_login = ['test', 'testSseNotification', 'testWebsocketNotification', 'generateUrlLink', 'addCommentReply'];

    public function test()
    {
        $withdraw = WithdrawApply::where('id', 111)->findOrEmpty()->toArray();
//        $result = WechatMerchantTransferLogic::transfer($withdraw);
        $result = WechatMerchantTransferLogic::details($withdraw);
        halt($result);

    }

    /**
     * 测试SSE通知接口
     * @ApiTitle (测试SSE通知)
     * @ApiSummary (用于测试向后台推送SSE通知)
     * @ApiMethod (GET)
     * @ApiParams (
     *   {"name":"title", "type":"string", "require":false, "desc":"通知标题，默认为'测试SSE通知'"},
     *   {"name":"content", "type":"string", "require":false, "desc":"通知内容，默认为'这是一条来自测试接口的通知'"},
     *   {"name":"type", "type":"string", "require":false, "desc":"通知类型，默认为'test_notification'"},
     *   {"name":"url", "type":"string", "require":false, "desc":"点击通知跳转的URL，默认为空"},
     *   {"name":"icon", "type":"integer", "require":false, "desc":"通知图标，默认为0"}
     * )
     * @ApiReturn ({"code":1,"msg":"成功","data":null})
     */
    public function testSseNotification()
    {
        $title = Request::get('title', '测试SSE通知');
        $content = Request::get('content', '这是一条来自测试接口的通知');
        $type = Request::get('type', 'test_notification');
        $url = Request::get('url', '');
        $icon = Request::get('icon', 0, 'intval');

        $notification = new Notification(app()); // 传入app()对象
        $notification->sendToAdmin([
            'title' => $title,
            'content' => $content,
            'url' => $url,
            'type' => $type,
            'icon' => $icon
        ]);

        return \app\common\server\JsonServer::success('测试SSE通知已发送');
    }

    /**
     * 测试WebSocket通知接口
     * @ApiTitle (测试WebSocket通知)
     * @ApiSummary (用于测试向admin和shop推送WebSocket通知)
     * @ApiMethod (GET|POST)
     * @ApiParams (
     *   {"name":"target", "type":"string", "require":false, "desc":"推送目标：admin, shop, all，默认为'all'"},
     *   {"name":"message", "type":"string", "require":false, "desc":"自定义消息内容，默认为'这是一条WebSocket测试通知'"},
     *   {"name":"title", "type":"string", "require":false, "desc":"通知标题，默认为'WebSocket测试通知'"},
     *   {"name":"type", "type":"string", "require":false, "desc":"消息类型：order, chat, system, test_notification，默认为'test_notification'"},
     *   {"name":"url", "type":"string", "require":false, "desc":"点击通知跳转的URL，默认为空"},
     *   {"name":"icon", "type":"integer", "require":false, "desc":"通知图标：0-默认，1-成功，2-错误，3-警告，4-信息，默认为0"}
     * )
     * @ApiReturn ({"code":1,"msg":"WebSocket通知测试完成","data":{"push_results":{},"total_connections":0,"test_data":{}}})
     */
    public function testWebsocketNotification()
    {
        // 获取请求参数，支持GET和POST
        $target = Request::param('target', 'all');
        $message = Request::param('message', '这是一条WebSocket测试通知');
        $title = Request::param('title', 'WebSocket测试通知');
        $type = Request::param('type', 'test_notification');
        $url = Request::param('url', '');
        $icon = Request::param('icon', 0, 'intval');

        // 记录测试请求日志
        Log::info('WebSocket通知测试请求: ' . json_encode([
            'target' => $target,
            'title' => $title,
            'message' => $message,
            'type' => $type,
            'url' => $url,
            'icon' => $icon
        ], JSON_UNESCAPED_UNICODE));

        // 参数验证
        $validTargets = ['admin', 'shop', 'all'];
        if (!in_array($target, $validTargets)) {
            return \app\common\server\JsonServer::error('无效的target参数，支持的值：' . implode(', ', $validTargets));
        }

        $validTypes = ['order', 'chat', 'system', 'test_notification'];
        if (!in_array($type, $validTypes)) {
            return \app\common\server\JsonServer::error('无效的type参数，支持的值：' . implode(', ', $validTypes));
        }

        if ($icon < 0 || $icon > 4) {
            return \app\common\server\JsonServer::error('无效的icon参数，支持的值：0-4');
        }

        // 构建测试数据，根据消息类型进行增强
        $testData = $this->buildTestDataByType($title, $message, $type, $url, $icon);

        // 初始化推送结果
        $pushResults = [];
        $totalConnections = 0;
      
        try {
            // 创建Websocket控制器实例
            $websocketController = new Websocket(app());
            
            // 同时尝试通过Notification控制器发送（作为备用方案）
            $notificationController = new Notification(app());

            // 根据target参数决定推送目标
            if ($target === 'admin' || $target === 'all') {
                // 推送给admin
                $adminResult = $this->pushToAdmin($websocketController, $testData);

                $pushResults['admin'] = $adminResult;
                $totalConnections += $adminResult['connections'];
            }

            if ($target === 'shop' || $target === 'all') {
                // 推送给shop
                $shopResult = $this->pushToShop($websocketController, $testData);
                $pushResults['shop'] = $shopResult;
                $totalConnections += $shopResult['connections'];
            }

            // 构建响应数据
            $responseData = [
                'push_results' => $pushResults,
                'total_connections' => $totalConnections,
                'test_data' => $testData
            ];

            Log::info('WebSocket通知测试完成: ' . json_encode($responseData, JSON_UNESCAPED_UNICODE));

            return \app\common\server\JsonServer::success('WebSocket通知测试完成', $responseData);

        } catch (\Exception $e) {
            Log::error('WebSocket通知测试失败: ' . $e->getMessage());
            return \app\common\server\JsonServer::error('WebSocket通知测试失败: ' . $e->getMessage());
        }
    }

    /**
     * 推送通知给admin
     * @param Websocket $websocketController
     * @param array $testData
     * @return array
     */
    private function pushToAdmin($websocketController, $testData)
    {
        try {
            Log::info('开始推送admin通知: ' . json_encode($testData, JSON_UNESCAPED_UNICODE));
            
            // 构建通知数据
            $adminNotificationData = [
                'title' => $testData['title'],
                'content' => $testData['content'],
                'type' => $testData['type'],
                'url' => $testData['url'],
                'icon' => $testData['icon']
            ];

            // 方法1: 直接通过CombinedHandler推送（最直接的方法）
            $eventResult = $this->pushViaCombinedHandler('admin', $testData);
            
            // 方法2: 通过WebSocket控制器推送（备用方法）
            $websocketResult = $this->pushViaWebsocketController('admin_group', $testData);
            
            // 方法3: 通过Redis推送（备用方案）
            $redisResult = $this->pushViaRedis('admin_notifications', $testData);
            
            // 获取连接数统计
            $connectionCount = $this->getAdminConnectionCount();
            
            // 判断推送结果
            if ($eventResult['success'] || $websocketResult['success'] || $redisResult['success']) {
                Log::info('Admin通知推送成功 - Event: ' . ($eventResult['success'] ? '成功' : '失败') . 
                         ', WebSocket: ' . ($websocketResult['success'] ? '成功' : '失败') . 
                         ', Redis: ' . ($redisResult['success'] ? '成功' : '失败'));
                
                return [
                    'success' => true,
                    'connections' => $connectionCount,
                    'message' => "已推送给admin模块 (连接数: {$connectionCount})",
                    'methods' => [
                        'event' => $eventResult,
                        'websocket' => $websocketResult,
                        'redis' => $redisResult
                    ],
                    'push_details' => [
                        'event_success' => $eventResult['success'],
                        'websocket_success' => $websocketResult['success'],
                        'redis_success' => $redisResult['success'],
                        'total_methods' => ($eventResult['success'] ? 1 : 0) + ($websocketResult['success'] ? 1 : 0) + ($redisResult['success'] ? 1 : 0)
                    ]
                ];
            } else {
                Log::error('Admin通知推送失败 - Event: ' . $eventResult['error'] . 
                          ', WebSocket: ' . $websocketResult['error'] . 
                          ', Redis: ' . $redisResult['error']);
                
                return [
                    'success' => false,
                    'connections' => $connectionCount,
                    'message' => '推送给admin失败',
                    'error' => 'Event: ' . $eventResult['error'] . '; WebSocket: ' . $websocketResult['error'] . '; Redis: ' . $redisResult['error'],
                    'methods' => [
                        'event' => $eventResult,
                        'websocket' => $websocketResult,
                        'redis' => $redisResult
                    ]
                ];
            }
        } catch (\Exception $e) {
            Log::error('推送给admin异常: ' . $e->getMessage());
            return [
                'success' => false,
                'connections' => 0,
                'message' => '推送给admin异常: ' . $e->getMessage(),
                'error' => $e->getMessage(),
                'method' => 'exception'
            ];
        }
    }

    /**
     * 通过现有WebSocket控制器推送消息
     * @param string $group 目标组名
     * @param array $testData 测试数据
     * @return array
     */
    private function pushViaWebsocketController($group, $testData)
    {
        try {
            Log::info('尝试通过WebSocket控制器推送到组: ' . $group);
            
            // 构建通知数据
            $notificationData = [
                'title' => $testData['title'],
                'content' => $testData['content'],
                'type' => $testData['type'],
                'url' => $testData['url'],
                'icon' => $testData['icon']
            ];

            // 创建Websocket控制器实例
            $websocketController = new Websocket(app());
            
            // 临时设置POST数据
            $originalPost = $_POST;
            $_POST = $notificationData;
            
            $result = null;
            
            // 根据目标组选择推送方法
            if ($group === 'admin_group') {
                $result = $websocketController->sendAdminNotification();
            } else if ($group === 'shop_group') {
                // 为shop设置receive_type
                $_POST['receive_type'] = 2; // 商家
                $_POST['user_id'] = 0; // 系统通知
                $result = $websocketController->sendNotification();
            }
            
            // 恢复原始POST数据
            $_POST = $originalPost;
            
            // 检查推送结果
            if ($result && method_exists($result, 'getData')) {
                $resultData = $result->getData();
                if (isset($resultData['code']) && $resultData['code'] == 1) {
                    Log::info('WebSocket控制器推送成功');
                    return [
                        'success' => true,
                        'error' => null,
                        'method' => 'websocket_controller',
                        'group' => $group,
                        'message_sent' => $notificationData
                    ];
                } else {
                    $errorMsg = isset($resultData['msg']) ? $resultData['msg'] : '未知错误';
                    Log::warning('WebSocket控制器推送失败: ' . $errorMsg);
                    return [
                        'success' => false,
                        'error' => 'WebSocket控制器推送失败: ' . $errorMsg,
                        'method' => 'websocket_controller'
                    ];
                }
            } else {
                Log::warning('WebSocket控制器返回无效结果');
                return [
                    'success' => false,
                    'error' => 'WebSocket控制器返回无效结果',
                    'method' => 'websocket_controller'
                ];
            }
            
        } catch (\Exception $e) {
            Log::error('WebSocket控制器推送异常: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => 'WebSocket控制器推送异常: ' . $e->getMessage(),
                'method' => 'websocket_controller'
            ];
        }
    }

    /**
     * 通过Redis推送消息
     * @param string $channel Redis频道名
     * @param array $testData 测试数据
     * @return array
     */
    private function pushViaRedis($channel, $testData)
    {
        try {
            $redis = \think\facade\Cache::store('redis');
            
            // 构建Redis消息格式
            $redisMessage = [
                'event' => 'admin_notification',
                'data' => [
                    'type' => $testData['type'],
                    'title' => $testData['title'],
                    'content' => $testData['content'],
                    'url' => $testData['url'],
                    'icon' => $testData['icon'],
                    'timestamp' => $testData['timestamp'],
                    'test_mode' => true,
                    'test_id' => $testData['test_id']
                ]
            ];

            $messageJson = json_encode($redisMessage, JSON_UNESCAPED_UNICODE);
            
            Log::info('尝试通过Redis推送到频道: ' . $channel);
            
            // 方法1: 发布到Redis频道
            $publishResult = false;
            if (method_exists($redis, 'publish')) {
                $publishResult = $redis->publish($channel, $messageJson);
                Log::info('Redis发布结果: ' . ($publishResult ? '成功' : '失败'));
            }
            
            // 方法2: 存储到Redis键（备用方案）
            $setResult = $redis->set('latest_' . $channel, $messageJson, 300); // 5分钟过期
            Log::info('Redis存储结果: ' . ($setResult ? '成功' : '失败'));
            
            // 方法3: 添加到通知队列
            $queueKey = $channel . '_queue';
            $notificationKey = $channel . '_' . time() . '_' . mt_rand(1000, 9999);
            $redis->set($notificationKey, $messageJson, 300);
            
            // 获取现有队列
            $queueData = $redis->get($queueKey);
            $queue = [];
            if (!empty($queueData)) {
                $decoded = json_decode($queueData, true);
                if (is_array($decoded)) {
                    $queue = $decoded;
                }
            }
            
            // 添加到队列
            $queue[] = $notificationKey;
            
            // 限制队列长度
            if (count($queue) > 50) {
                $oldKey = array_shift($queue);
                if ($oldKey) {
                    $redis->delete($oldKey);
                }
            }
            
            // 更新队列
            $queueResult = $redis->set($queueKey, json_encode($queue, JSON_UNESCAPED_UNICODE), 300);
            Log::info('Redis队列更新结果: ' . ($queueResult ? '成功' : '失败') . ', 队列长度: ' . count($queue));
            
            // 判断整体推送结果
            $success = $publishResult || $setResult || $queueResult;
            
            return [
                'success' => $success,
                'error' => $success ? null : 'Redis推送失败',
                'method' => 'redis',
                'channel' => $channel,
                'results' => [
                    'publish' => $publishResult,
                    'set' => $setResult,
                    'queue' => $queueResult,
                    'queue_length' => count($queue)
                ],
                'message_sent' => $redisMessage
            ];
            
        } catch (\Exception $e) {
            Log::error('Redis推送异常: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => 'Redis推送异常: ' . $e->getMessage(),
                'method' => 'redis'
            ];
        }
    }

    /**
     * 推送通知给shop
     * @param Websocket $websocketController
     * @param array $testData
     * @return array
     */
    private function pushToShop($websocketController, $testData)
    {
        try {
            Log::info('开始推送shop通知: ' . json_encode($testData, JSON_UNESCAPED_UNICODE));
            
            // 方法1: 通过WebSocket控制器推送（主要方法）
            $websocketResult = $this->pushViaWebsocketController('shop_group', $testData);
            
            // 方法2: 通过Redis推送（备用方案）
            $redisResult = $this->pushViaRedis('shop_notifications', $testData);
            
            // 获取连接数统计
            $connectionCount = $this->getShopConnectionCount();
            
            // 判断推送结果
            if ($websocketResult['success'] || $redisResult['success']) {
                Log::info('Shop通知推送成功 - WebSocket: ' . ($websocketResult['success'] ? '成功' : '失败') . 
                         ', Redis: ' . ($redisResult['success'] ? '成功' : '失败'));
                
                return [
                    'success' => true,
                    'connections' => $connectionCount,
                    'message' => "已推送给shop模块 (连接数: {$connectionCount})",
                    'methods' => [
                        'websocket' => $websocketResult,
                        'redis' => $redisResult
                    ],
                    'push_details' => [
                        'websocket_success' => $websocketResult['success'],
                        'redis_success' => $redisResult['success'],
                        'total_methods' => ($websocketResult['success'] ? 1 : 0) + ($redisResult['success'] ? 1 : 0)
                    ]
                ];
            } else {
                Log::error('Shop通知推送失败 - WebSocket: ' . $websocketResult['error'] . 
                          ', Redis: ' . $redisResult['error']);
                
                return [
                    'success' => false,
                    'connections' => $connectionCount,
                    'message' => '推送给shop失败',
                    'error' => 'WebSocket: ' . $websocketResult['error'] . '; Redis: ' . $redisResult['error'],
                    'methods' => [
                        'websocket' => $websocketResult,
                        'redis' => $redisResult
                    ]
                ];
            }
        } catch (\Exception $e) {
            Log::error('推送给shop异常: ' . $e->getMessage());
            return [
                'success' => false,
                'connections' => 0,
                'message' => '推送给shop异常: ' . $e->getMessage(),
                'error' => $e->getMessage(),
                'method' => 'exception'
            ];
        }
    }

    /**
     * 获取admin连接数统计
     * @return int
     */
    private function getAdminConnectionCount()
    {
        try {
            // 通过Redis检查admin连接数
            $redis = \think\facade\Cache::store('redis');
            $activeConnections = 0;
            
            // 获取WebSocket前缀配置
            $prefix = config('default.websocket_prefix', 'socket_');
            
            // 方法1: 检查admin_*键中的连接
            $adminKeys = $redis->keys($prefix . 'admin_*');
            if (is_array($adminKeys)) {
                foreach ($adminKeys as $key) {
                    $value = $redis->get($key);
                    if ($value && is_numeric($value) && $value > 0) {
                        $activeConnections++;
                    }
                }
            }
            
            // 方法2: 如果上面没找到，检查fd_*键中的admin类型连接
            if ($activeConnections === 0) {
                $fdKeys = $redis->keys($prefix . 'fd_*');
                if (is_array($fdKeys)) {
                    foreach ($fdKeys as $fdKey) {
                        $fdData = $redis->get($fdKey);
                        if ($fdData) {
                            $data = json_decode($fdData, true);
                            if ($data && isset($data['type']) && $data['type'] === 'admin') {
                                $activeConnections++;
                            }
                        }
                    }
                }
            }
            
            Log::info("检测到 {$activeConnections} 个admin连接");
            return $activeConnections;
        } catch (\Exception $e) {
            Log::warning('获取admin连接数失败: ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * 获取shop连接数统计
     * @return int
     */
    private function getShopConnectionCount()
    {
        try {
            // 通过Redis检查shop连接数
            $redis = \think\facade\Cache::store('redis');
            $activeConnections = 0;
            
            // 获取WebSocket前缀配置
            $prefix = config('default.websocket_prefix', 'socket_');
            
            // 方法1: 检查shop_*键中的连接
            $shopKeys = $redis->keys($prefix . 'shop_*');
            if (is_array($shopKeys)) {
                foreach ($shopKeys as $key) {
                    $value = $redis->get($key);
                    if ($value && is_numeric($value) && $value > 0) {
                        $activeConnections++;
                    }
                }
            }
            
            // 方法2: 如果上面没找到，检查kefu相关的连接
            if ($activeConnections === 0) {
                $kefuKeys = $redis->keys($prefix . 'kefu_*');
                if (is_array($kefuKeys)) {
                    foreach ($kefuKeys as $key) {
                        $value = $redis->get($key);
                        if ($value && is_numeric($value) && $value > 0) {
                            $activeConnections++;
                        }
                    }
                }
            }
            
            // 方法3: 如果还没找到，检查fd_*键中的shop/kefu类型连接
            if ($activeConnections === 0) {
                $fdKeys = $redis->keys($prefix . 'fd_*');
                if (is_array($fdKeys)) {
                    foreach ($fdKeys as $fdKey) {
                        $fdData = $redis->get($fdKey);
                        if ($fdData) {
                            $data = json_decode($fdData, true);
                            if ($data && isset($data['type']) && ($data['type'] === 'shop' || $data['type'] === 'kefu')) {
                                $activeConnections++;
                            }
                        }
                    }
                }
            }
            
            Log::info("检测到 {$activeConnections} 个shop连接");
            return $activeConnections;
        } catch (\Exception $e) {
            Log::warning('获取shop连接数失败: ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * 直接通过CombinedHandler推送通知
     * @param string $target 目标类型 (admin/shop)
     * @param array $testData 测试数据
     * @return array
     */
    private function pushViaCombinedHandler($target, $testData)
    {
        try {
            Log::info('尝试通过CombinedHandler推送通知: ' . $target);
            
            // 检查CombinedHandler是否存在
            if (!class_exists('\app\common\websocket\CombinedHandler')) {
                return [
                    'success' => false,
                    'error' => 'CombinedHandler类不存在',
                    'method' => 'combined_handler'
                ];
            }
            
            // 尝试获取WebSocket服务器实例
            // 这里我们通过静态方法或全局变量来获取实例
            $handlerInstance = $this->getWebSocketHandlerInstance();
            
            if (!$handlerInstance) {
                // 如果无法获取实例，尝试通过Redis直接推送
                return $this->pushViaDirectRedis($target, $testData);
            }
            
            if ($target === 'admin') {
                // 使用CombinedHandler的sendNotificationToAdmin方法
                $result = $handlerInstance->sendNotificationToAdmin(
                    $testData['title'],
                    $testData['content'],
                    $testData['type'],
                    $testData['url'],
                    $testData['icon']
                );
                
                if ($result) {
                    Log::info('CombinedHandler admin通知推送成功');
                    return [
                        'success' => true,
                        'error' => null,
                        'method' => 'combined_handler',
                        'target' => 'admin',
                        'data_sent' => $testData
                    ];
                } else {
                    return [
                        'success' => false,
                        'error' => 'CombinedHandler推送失败',
                        'method' => 'combined_handler'
                    ];
                }
            } else {
                // 对于shop，我们需要使用不同的方法
                // 暂时返回不支持
                return [
                    'success' => false,
                    'error' => 'Shop推送暂不支持CombinedHandler方法',
                    'method' => 'combined_handler'
                ];
            }
            
        } catch (\Exception $e) {
            Log::error('CombinedHandler推送异常: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => 'CombinedHandler推送异常: ' . $e->getMessage(),
                'method' => 'combined_handler'
            ];
        }
    }

    /**
     * 获取WebSocket处理器实例
     * @return mixed|null
     */
    private function getWebSocketHandlerInstance()
    {
        try {
            // 尝试从容器中获取WebSocket处理器实例
            if (function_exists('app') && app()->has('websocket.handler')) {
                return app('websocket.handler');
            }
            
            // 尝试从全局变量获取
            if (isset($GLOBALS['websocket_handler'])) {
                return $GLOBALS['websocket_handler'];
            }
            
            // 如果都获取不到，返回null
            return null;
        } catch (\Exception $e) {
            Log::warning('获取WebSocket处理器实例失败: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * 直接通过Redis推送到WebSocket服务器
     * @param string $target 目标类型
     * @param array $testData 测试数据
     * @return array
     */
    private function pushViaDirectRedis($target, $testData)
    {
        try {
            Log::info('尝试通过直接Redis推送: ' . $target);
            
            $redis = \think\facade\Cache::store('redis');
            
            // 构建符合CombinedHandler期望的消息格式
            $message = [
                'event' => 'notification',
                'data' => [
                    'type' => $testData['type'],
                    'title' => $testData['title'],
                    'content' => $testData['content'],
                    'url' => $testData['url'],
                    'icon' => $testData['icon'],
                    'timestamp' => $testData['timestamp'],
                    'test_mode' => true,
                    'test_id' => $testData['test_id']
                ]
            ];
            
            $messageJson = json_encode($message, JSON_UNESCAPED_UNICODE);
            
            // 方法1: 发布到特定频道
            $channelName = $target === 'admin' ? 'admin_notifications' : 'shop_notifications';
            $publishResult = false;
            if (method_exists($redis, 'publish')) {
                $publishResult = $redis->publish($channelName, $messageJson);
                Log::info('Redis发布结果: ' . ($publishResult ? '成功' : '失败') . ', 频道: ' . $channelName);
            }
            
            // 方法2: 存储到特定键，供WebSocket服务器轮询
            $keyName = 'websocket_' . $target . '_notification_' . time();
            $setResult = $redis->set($keyName, $messageJson, 300); // 5分钟过期
            Log::info('Redis存储结果: ' . ($setResult ? '成功' : '失败') . ', 键: ' . $keyName);
            
            // 方法3: 添加到通知队列
            $queueKey = 'websocket_' . $target . '_queue';
            $redis->lpush($queueKey, $messageJson);
            $redis->expire($queueKey, 300); // 5分钟过期
            Log::info('Redis队列推送完成: ' . $queueKey);
            
            $success = $publishResult || $setResult;
            
            return [
                'success' => $success,
                'error' => $success ? null : 'Redis直接推送失败',
                'method' => 'direct_redis',
                'target' => $target,
                'results' => [
                    'publish' => $publishResult,
                    'set' => $setResult,
                    'queue' => true
                ],
                'message_sent' => $message
            ];
            
        } catch (\Exception $e) {
            Log::error('Redis直接推送异常: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => 'Redis直接推送异常: ' . $e->getMessage(),
                'method' => 'direct_redis'
            ];
        }
    }

    /**
     * 根据消息类型构建测试数据
     * @param string $title 原始标题
     * @param string $message 原始消息
     * @param string $type 消息类型
     * @param string $url 原始URL
     * @param int $icon 原始图标
     * @return array
     */
    private function buildTestDataByType($title, $message, $type, $url, $icon)
    {
        // 基础测试数据
        $testData = [
            'title' => $title,
            'content' => $message,
            'type' => $type,
            'url' => $url,
            'icon' => $icon,
            'timestamp' => time(),
            'test_mode' => true,
            'test_id' => uniqid('test_')
        ];

        // 根据消息类型增强内容
        switch ($type) {
            case 'order':
                // 订单相关通知
                if ($title === 'WebSocket测试通知') {
                    $testData['title'] = '订单状态更新通知';
                }
                if ($message === '这是一条WebSocket测试通知') {
                    $testData['content'] = '您有新的订单需要处理，订单号：TEST' . date('YmdHis');
                }
                if (empty($url)) {
                    $testData['url'] = '/admin/order/index';
                }
                if ($icon === 0) {
                    $testData['icon'] = 1; // 成功图标
                }
                // 添加订单相关的额外数据
                $testData['order_data'] = [
                    'order_id' => 'TEST' . date('YmdHis'),
                    'order_status' => 'pending',
                    'amount' => '99.99',
                    'customer' => '测试用户'
                ];
                break;

            case 'chat':
                // 聊天相关通知
                if ($title === 'WebSocket测试通知') {
                    $testData['title'] = '新消息通知';
                }
                if ($message === '这是一条WebSocket测试通知') {
                    $testData['content'] = '您收到了一条新的客服消息，请及时回复';
                }
                if (empty($url)) {
                    $testData['url'] = '/admin/chat/index';
                }
                if ($icon === 0) {
                    $testData['icon'] = 4; // 信息图标
                }
                // 添加聊天相关的额外数据
                $testData['chat_data'] = [
                    'chat_id' => 'CHAT' . date('YmdHis'),
                    'from_user' => '测试用户',
                    'message_type' => 'text',
                    'unread_count' => 1
                ];
                break;

            case 'system':
                // 系统通知
                if ($title === 'WebSocket测试通知') {
                    $testData['title'] = '系统维护通知';
                }
                if ($message === '这是一条WebSocket测试通知') {
                    $testData['content'] = '系统将于今晚进行维护升级，预计耗时30分钟';
                }
                if (empty($url)) {
                    $testData['url'] = '/admin/system/notice';
                }
                if ($icon === 0) {
                    $testData['icon'] = 3; // 警告图标
                }
                // 添加系统相关的额外数据
                $testData['system_data'] = [
                    'notice_id' => 'SYS' . date('YmdHis'),
                    'priority' => 'high',
                    'maintenance_time' => date('Y-m-d H:i:s', time() + 3600),
                    'affected_modules' => ['订单系统', '支付系统']
                ];
                break;

            case 'test_notification':
            default:
                // 默认测试通知，保持原有内容
                if ($icon === 0) {
                    $testData['icon'] = 0; // 默认图标
                }
                // 添加测试相关的额外数据
                $testData['test_data'] = [
                    'test_type' => 'websocket_notification',
                    'test_time' => date('Y-m-d H:i:s'),
                    'test_environment' => config('app.app_debug') ? 'development' : 'production',
                    'test_version' => '1.0.0'
                ];
                break;
        }

        // 记录构建的测试数据
        Log::info('构建的测试数据: ' . json_encode($testData, JSON_UNESCAPED_UNICODE));

        return $testData;
    }

    /**
     * 获取加密URLLink（调用微信官方API）
     * @ApiTitle (获取加密URLLink)
     * @ApiSummary (获取小程序 URL Link，适用于短信、邮件、网页、微信内等拉起小程序的业务场景)
     * @ApiMethod (POST)
     * @ApiParams (
     *   {"name":"appid", "type":"string", "require":false, "desc":"小程序AppID，不填则使用配置文件中的默认值"},
     *   {"name":"secret", "type":"string", "require":false, "desc":"小程序AppSecret，不填则使用配置文件中的默认值"},
     *   {"name":"path", "type":"string", "require":false, "desc":"通过 URL Link 进入的小程序页面路径，必须是已经发布的小程序存在的页面，不可携带 query。path 为空时会跳转小程序主页"},
     *   {"name":"query", "type":"string", "require":false, "desc":"通过 URL Link 进入小程序时的query，最大1024个字符，只支持数字，大小写英文以及部分特殊字符：!#$&'()*+,/:;=?@-._~%"},
     *   {"name":"expire_type", "type":"number", "require":false, "desc":"默认值0.小程序 URL Link 失效类型，失效时间：0，失效间隔天数：1"},
     *   {"name":"expire_time", "type":"number", "require":false, "desc":"到期失效的 URL Link 的失效时间，为 Unix 时间戳。生成的到期失效 URL Link 在该时间前有效。最长有效期为30天。expire_type 为 0 必填"},
     *   {"name":"expire_interval", "type":"number", "require":false, "desc":"到期失效的URL Link的失效间隔天数。生成的到期失效URL Link在该间隔时间到达前有效。最长间隔天数为30天。expire_type 为 1 必填"},
     *   {"name":"env_version", "type":"string", "require":false, "desc":"默认值\"release\"。要打开的小程序版本。正式版为 \"release\"，体验版为\"trial\"，开发版为\"develop\"，仅在微信外打开时生效"},
     *   {"name":"cloud_base", "type":"object", "require":false, "desc":"云开发静态网站自定义 H5 配置参数，可配置中转的云开发 H5 页面。不填默认用官方 H5 页面"}
     * )
     * @ApiReturn ({"errcode":0,"errmsg":"ok","url_link":"生成的小程序 URL Link"})
     */
    public function generateUrlLink()
    {
        // 获取请求参数 - 支持JSON和表单两种格式
        $requestData = $this->getRequestData();

        $appid = 'wx0b0f5aa20564c385';
        $secret = '6dd20440db1148fee4737a5c779c57b6';
        $path = $requestData['path'] ?? '';
        $query = $requestData['query'] ?? '';
        $expireType = intval($requestData['expire_type'] ?? 0);
        $expireTime = intval($requestData['expire_time'] ?? 0);
        $expireInterval = intval($requestData['expire_interval'] ?? 0);
        $envVersion = $requestData['env_version'] ?? 'release';
        $cloudBase = $requestData['cloud_base'] ?? null;

        // 记录请求日志
        Log::info('generateUrlLink请求: ' . json_encode([
            'appid' => $appid,
            'path' => $path,
            'query' => $query,
            'expire_type' => $expireType,
            'expire_time' => $expireTime,
            'expire_interval' => $expireInterval,
            'env_version' => $envVersion,
            'cloud_base' => $cloudBase
        ], JSON_UNESCAPED_UNICODE));
           
        try {
            // 验证必要参数
            if (empty($appid) || empty($secret)) {
                return $this->returnWechatResponse(40001, 'invalid credential access_token isinvalid or not latest');
            }

            // 参数验证
            $validation = $this->validateWechatUrlLinkParams($path, $query, $expireType, $expireTime, $expireInterval, $envVersion, $cloudBase);
            if ($validation['errcode'] !== 0) {
                return $this->returnWechatResponse($validation['errcode'], $validation['errmsg']);
            }

            // 1. 获取access_token
            $accessToken = $this->getWechatAccessToken($appid, $secret);
            if (!$accessToken) {
                return $this->returnWechatResponse(40001, 'invalid credential access_token isinvalid or not latest');
            }

            // 2. 调用微信官方generateUrlLink API
            $result = $this->callWechatGenerateUrlLink($accessToken, $path, $query, $expireType, $expireTime, $expireInterval, $envVersion, $cloudBase);

            Log::info('generateUrlLink成功: ' . json_encode($result, JSON_UNESCAPED_UNICODE));

            return $this->returnWechatResponse($result['errcode'], $result['errmsg'], $result);

        } catch (\Exception $e) {
            Log::error('generateUrlLink失败: ' . $e->getMessage());
            return $this->returnWechatResponse(-1, 'system error');
        }
    }

    /**
     * 根据类型生成URL
     * @param string $url 原始URL
     * @param string $type 链接类型
     * @param int $expireTime 过期时间
     * @param array $extraParams 额外参数
     * @param string $domain 域名
     * @param string $secretKey 密钥
     * @return array
     */
    private function generateUrlByType($url, $type, $expireTime, $extraParams, $domain, $secretKey)
    {
        $currentTime = time();
        $expireTimestamp = $expireTime > 0 ? $currentTime + $expireTime : 0;

        // 基础返回数据
        $result = [
            'original_url' => $url,
            'type' => $type,
            'expire_time' => $expireTimestamp,
            'generated_at' => $currentTime,
            'generated_url' => '',
            'short_code' => '',
            'signature' => ''
        ];

        switch ($type) {
            case 'simple':
                $result = array_merge($result, $this->generateSimpleUrl($url, $extraParams, $domain));
                break;

            case 'signed':
                $result = array_merge($result, $this->generateSignedUrl($url, $expireTimestamp, $extraParams, $domain, $secretKey));
                break;

            case 'short':
                $result = array_merge($result, $this->generateShortUrl($url, $expireTimestamp, $extraParams, $domain));
                break;

            case 'temporary':
                $result = array_merge($result, $this->generateTemporaryUrl($url, $expireTimestamp, $extraParams, $domain, $secretKey));
                break;
        }

        return $result;
    }

    /**
     * 生成简单URL
     * @param string $url
     * @param array $extraParams
     * @param string $domain
     * @return array
     */
    private function generateSimpleUrl($url, $extraParams, $domain)
    {
        // 确保URL是完整的
        if (!preg_match('/^https?:\/\//', $url)) {
            $url = rtrim($domain, '/') . '/' . ltrim($url, '/');
        }

        // 添加额外参数
        if (!empty($extraParams)) {
            $separator = strpos($url, '?') !== false ? '&' : '?';
            $url .= $separator . http_build_query($extraParams);
        }

        return [
            'generated_url' => $url
        ];
    }

    /**
     * 生成签名URL
     * @param string $url
     * @param int $expireTimestamp
     * @param array $extraParams
     * @param string $domain
     * @param string $secretKey
     * @return array
     */
    private function generateSignedUrl($url, $expireTimestamp, $extraParams, $domain, $secretKey)
    {
        // 确保URL是完整的
        if (!preg_match('/^https?:\/\//', $url)) {
            $url = rtrim($domain, '/') . '/' . ltrim($url, '/');
        }

        // 构建签名参数
        $signParams = [
            'timestamp' => time(),
            'nonce' => uniqid(),
        ];

        if ($expireTimestamp > 0) {
            $signParams['expires'] = $expireTimestamp;
        }

        // 合并额外参数
        $allParams = array_merge($extraParams, $signParams);

        // 生成签名
        $signature = $this->generateSignature($url, $allParams, $secretKey);
        $allParams['signature'] = $signature;

        // 构建最终URL
        $separator = strpos($url, '?') !== false ? '&' : '?';
        $finalUrl = $url . $separator . http_build_query($allParams);

        return [
            'generated_url' => $finalUrl,
            'signature' => $signature
        ];
    }

    /**
     * 获取请求数据（支持JSON和表单格式）
     * @return array
     */
    private function getRequestData()
    {
        $contentType = Request::header('content-type', '');

        if (strpos($contentType, 'application/json') !== false) {
            // JSON格式请求
            $input = file_get_contents('php://input');
            $data = json_decode($input, true);
            return is_array($data) ? $data : [];
        } else {
            // 表单格式请求
            return Request::param();
        }
    }

    /**
     * 验证微信URL链接参数（完全按照微信规范）
     * @param string $path
     * @param string $query
     * @param int $expireType
     * @param int $expireTime
     * @param int $expireInterval
     * @param string $envVersion
     * @param mixed $cloudBase
     * @return array
     */
    private function validateWechatUrlLinkParams($path, $query, $expireType, $expireTime, $expireInterval, $envVersion, $cloudBase)
    {
        // 验证path格式 - 不可携带query
        if (!empty($path)) {
            if (strpos($path, '?') !== false) {
                return ['errcode' => 40165, 'errmsg' => 'invalid weapp pagepath'];
            }
            // path必须以/开头
            if (!preg_match('/^\/[a-zA-Z0-9\/_-]*$/', $path)) {
                return ['errcode' => 40165, 'errmsg' => 'invalid weapp pagepath'];
            }
        }

        // 验证query格式和长度
        if (!empty($query)) {
            if (strlen($query) > 1024) {
                return ['errcode' => 40212, 'errmsg' => 'invalid query'];
            }
            // 只支持数字，大小写英文以及部分特殊字符：!#$&'()*+,/:;=?@-._~%
            if (!preg_match('/^[a-zA-Z0-9!#$&\'()*+,\/:;=?@._~%-]*$/', $query)) {
                return ['errcode' => 40212, 'errmsg' => 'invalid query'];
            }
        }

        // 验证失效类型
        if (!in_array($expireType, [0, 1])) {
            return ['errcode' => 85401, 'errmsg' => 'time limit between 1min and 30days'];
        }

        // 验证失效时间
        if ($expireType === 0 && $expireTime > 0) {
            $currentTime = time();
            $minExpireTime = $currentTime + 60; // 至少1分钟后
            $maxExpireTime = $currentTime + (30 * 24 * 3600); // 最多30天后

            if ($expireTime < $minExpireTime || $expireTime > $maxExpireTime) {
                return ['errcode' => 85401, 'errmsg' => 'time limit between 1min and 30days'];
            }
        }

        // 验证失效间隔
        if ($expireType === 1 && $expireInterval > 0) {
            if ($expireInterval < 1 || $expireInterval > 30) {
                return ['errcode' => 85401, 'errmsg' => 'time limit between 1min and 30days'];
            }
        }

        // 验证环境版本
        $validEnvVersions = ['release', 'trial', 'develop'];
        if (!in_array($envVersion, $validEnvVersions)) {
            return ['errcode' => 85402, 'errmsg' => 'invalid env_version'];
        }

        // 验证云开发配置
        if ($cloudBase !== null) {
            if (!is_array($cloudBase)) {
                return ['errcode' => 85088, 'errmsg' => '未开通云开发'];
            }
            if (!isset($cloudBase['env']) || empty($cloudBase['env'])) {
                return ['errcode' => 85088, 'errmsg' => '未开通云开发'];
            }
            // 验证云开发path不可携带query
            if (isset($cloudBase['path']) && !empty($cloudBase['path']) && strpos($cloudBase['path'], '?') !== false) {
                return ['errcode' => 85088, 'errmsg' => '未开通云开发'];
            }
            // 验证云开发query长度
            if (isset($cloudBase['query']) && strlen($cloudBase['query']) > 1024) {
                return ['errcode' => 85088, 'errmsg' => '未开通云开发'];
            }
        }

        return ['errcode' => 0, 'errmsg' => 'ok'];
    }

    /**
     * 获取微信access_token
     * @param string $appid
     * @param string $secret
     * @return string|false
     */
    private function getWechatAccessToken($appid, $secret)
    {
        try {
            // 检查缓存中是否有有效的access_token
            $cacheKey = 'wechat_access_token_' . $appid;
            $cachedToken = cache($cacheKey);

            if ($cachedToken) {
                Log::info('使用缓存的access_token');
                return $cachedToken;
            }
            
            // 调用微信API获取access_token
            $url = 'https://api.weixin.qq.com/cgi-bin/token';
            $params = [
                'grant_type' => 'client_credential',
                'appid' => $appid,
                'secret' => $secret
            ];

            $response = $this->httpGet($url, $params);
            $result = json_decode($response, true);

            if (isset($result['access_token'])) {
                // 缓存access_token，有效期7000秒（微信官方是7200秒，提前200秒过期）
                cache($cacheKey, $result['access_token'], 7000);
                Log::info('获取新的access_token成功');
                return $result['access_token'];
            } else {
                Log::error('获取access_token失败: ' . json_encode($result, JSON_UNESCAPED_UNICODE));
                return false;
            }
        } catch (\Exception $e) {
            Log::error('获取access_token异常: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 调用微信官方generateUrlLink API
     * @param string $accessToken
     * @param string $path
     * @param string $query
     * @param int $expireType
     * @param int $expireTime
     * @param int $expireInterval
     * @param string $envVersion
     * @param mixed $cloudBase
     * @return array
     */
    private function callWechatGenerateUrlLink($accessToken, $path, $query, $expireType, $expireTime, $expireInterval, $envVersion, $cloudBase)
    {
        try {
            $url = 'https://api.weixin.qq.com/wxa/generate_urllink?access_token=' . $accessToken;

            // 构建请求数据
            $data = [];

            if (!empty($path)) {
                $data['path'] = $path;
            }

            if (!empty($query)) {
                $data['query'] = $query;
            }

            if ($expireType !== null) {
                $data['expire_type'] = $expireType;
            }

            if ($expireType === 0 && $expireTime > 0) {
                $data['expire_time'] = $expireTime;
            }

            if ($expireType === 1 && $expireInterval > 0) {
                $data['expire_interval'] = $expireInterval;
            }

            if (!empty($envVersion)) {
                $data['env_version'] = $envVersion;
            }

            if (is_array($cloudBase) && !empty($cloudBase)) {
                $data['cloud_base'] = $cloudBase;
            }

            Log::info('调用微信generateUrlLink API: ' . json_encode($data, JSON_UNESCAPED_UNICODE));

            $response = $this->httpPost($url, json_encode($data, JSON_UNESCAPED_UNICODE));
            $result = json_decode($response, true);

            if (!$result) {
                return ['errcode' => -1, 'errmsg' => 'system error'];
            }

            return $result;
        } catch (\Exception $e) {
            Log::error('调用微信generateUrlLink API异常: ' . $e->getMessage());
            return ['errcode' => -1, 'errmsg' => 'system error'];
        }
    }



    /**
     * HTTP GET请求
     * @param string $url
     * @param array $params
     * @return string
     */
    private function httpGet($url, $params = [])
    {
        if (!empty($params)) {
            $url .= '?' . http_build_query($params);
        }

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($error) {
            throw new \Exception('HTTP GET请求失败: ' . $error);
        }

        if ($httpCode !== 200) {
            throw new \Exception('HTTP GET请求失败，状态码: ' . $httpCode);
        }

        return $response;
    }

    /**
     * HTTP POST请求
     * @param string $url
     * @param string $data
     * @return string
     */
    private function httpPost($url, $data)
    {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Content-Length: ' . strlen($data)
        ]);
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($error) {
            throw new \Exception('HTTP POST请求失败: ' . $error);
        }

        if (!in_array($httpCode, [200, 201])) {
            throw new \Exception('HTTP POST请求失败，状态码: ' . $httpCode);
        }

        return $response;
    }

    /**
     * 返回微信风格的响应
     * @param int $errcode
     * @param string $errmsg
     * @param array $data
     * @return \think\Response
     */
    private function returnWechatResponse($errcode, $errmsg, $data = [])
    {
        $response = [
            'errcode' => $errcode,
            'errmsg' => $errmsg
        ];

        // 成功时合并额外数据（排除errcode和errmsg）
        if ($errcode === 0 && !empty($data)) {
            foreach ($data as $key => $value) {
                if (!in_array($key, ['errcode', 'errmsg'])) {
                    $response[$key] = $value;
                }
            }
        }

        return response($response, 200, [], 'json');
    }

    /**
     * 创建微信小程序评论回复
     * @ApiTitle (创建评论回复)
     * @ApiSummary (调用微信小程序API创建评论回复)
     * @ApiMethod (POST)
     * @ApiParams (
     *   {"name":"appid", "type":"string", "require":false, "desc":"小程序AppID，不填则使用配置文件中的默认值"},
     *   {"name":"secret", "type":"string", "require":false, "desc":"小程序AppSecret，不填则使用配置文件中的默认值"},
     *   {"name":"commentId", "type":"string", "require":true, "desc":"评价的 id"},
     *   {"name":"content", "type":"string", "require":true, "desc":"评论的内容"}
     * )
     * @ApiReturn ({"errcode":0,"errmsg":"ok","success":true})
     */
    public function addCommentReply()
    {
        // 获取请求参数 - 支持JSON和表单两种格式
        $requestData = $this->getRequestData();

        $appid = $requestData['appid'] ?? 'wx0b0f5aa20564c385';
        $secret = $requestData['secret'] ?? '6dd20440db1148fee4737a5c779c57b6';
        $commentId = $requestData['commentId'] ?? '';
        $content = $requestData['content'] ?? '';

        // 记录请求日志
        Log::info('addCommentReply请求: ' . json_encode([
            'appid' => $appid,
            'commentId' => $commentId,
            'content' => $content
        ], JSON_UNESCAPED_UNICODE));

        try {
            // 验证必要参数
            if (empty($appid) || empty($secret)) {
                return $this->returnWechatResponse(40001, 'invalid credential access_token isinvalid or not latest');
            }

            if (empty($commentId)) {
                return $this->returnWechatResponse(40003, 'invalid openid');
            }

            if (empty($content)) {
                return $this->returnWechatResponse(40035, 'invalid parameter');
            }

            // 验证content长度（一般评论内容不应过长）
            if (strlen($content) > 600) {
                return $this->returnWechatResponse(40035, 'invalid parameter');
            }

            // 1. 获取access_token
            $accessToken = $this->getWechatAccessToken($appid, $secret);
            if (!$accessToken) {
                return $this->returnWechatResponse(40001, 'invalid credential access_token isinvalid or not latest');
            }

            // 2. 调用微信官方评论回复API
            $result = $this->callWechatCommentReplyAdd($accessToken, $commentId, $content);

            Log::info('addCommentReply成功: ' . json_encode($result, JSON_UNESCAPED_UNICODE));

            return $this->returnWechatResponse($result['errcode'], $result['errmsg'], $result);

        } catch (\Exception $e) {
            Log::error('addCommentReply失败: ' . $e->getMessage());
            return $this->returnWechatResponse(-1, 'system error');
        }
    }

    /**
     * 调用微信官方评论回复API
     * @param string $accessToken
     * @param string $commentId
     * @param string $content
     * @return array
     */
    private function callWechatCommentReplyAdd($accessToken, $commentId, $content)
    {
        try {
            $url = 'https://api.weixin.qq.com/wxaapi/comment/reply/add?access_token=' . $accessToken;

            // 构建请求数据
            $data = [
                'commentId' => $commentId,
                'content' => $content
            ];

            Log::info('调用微信评论回复API: ' . json_encode($data, JSON_UNESCAPED_UNICODE));

            $response = $this->httpPost($url, json_encode($data, JSON_UNESCAPED_UNICODE));
            $result = json_decode($response, true);

            if (!$result) {
                return ['errcode' => -1, 'errmsg' => 'system error'];
            }

            // 如果微信API返回成功，确保包含success字段
            if ($result['errcode'] === 0) {
                $result['success'] = true;
            }

            return $result;
        } catch (\Exception $e) {
            Log::error('调用微信评论回复API异常: ' . $e->getMessage());
            return ['errcode' => -1, 'errmsg' => 'system error'];
        }
    }
}
