<?php
namespace app\common\model;

use app\common\basics\Models;
use think\facade\Db;
use app\common\server\UrlServer;

/**
 * 商家采购人员分配模型
 * Class ShopPurchaserAllocation
 * @package app\common\model
 */
class ShopPurchaserAllocation extends Models
{
    /**
     * 获取商家采购人员分配列表
     * @param int $shopId 商家ID
     * @param int $year 年份
     * @return array
     */
    public static function getShopPurchaserList($shopId, $year = null,$get=[])
    {
        $where = [
            ['spa.shop_id', '=', $shopId],
            ['spa.status', '=', 1]
        ];

        if ($year) {
            $where[] = ['spa.allocation_year', '=', $year];
        } else {
            $where[] = ['spa.allocation_year', '=', date('Y')];
        }
        if(!empty($get['keyword'])){
            $where[] = ['u.nickname|u.mobile', 'like', '%' . $get['keyword'] . '%'];
        }
        $data=self::alias('spa')
            ->leftJoin('user u', 'spa.user_id = u.id')
            ->where($where)
            ->field('spa.*, u.nickname, u.avatar, u.mobile, u.activity_score,u.user_delete')
            ->order('spa.user_level asc, spa.user_score desc')
            ->select()
            ->toArray();
            foreach($data as $k=>&$v){
                $v['avatar'] = UrlServer::getFileUrl($v['avatar']);
            }

        return  $data;
    }

    /**
     * 检查用户是否已分配给商家
     * @param int $shopId 商家ID
     * @param int $userId 用户ID
     * @param int $year 年份
     * @return bool
     */
    public static function isUserAllocated($shopId, $userId, $year = null)
    {
        $year = $year ?: date('Y');
        
        return self::where([
            ['shop_id', '=', $shopId],
            ['user_id', '=', $userId],
            ['allocation_year', '=', $year],
            ['status', '=', 1]
        ])->count() > 0;
    }

    /**
     * 分配采购人员给商家
     * @param int $shopId 商家ID
     * @param array $userIds 用户ID数组
     * @param int $shopTierLevel 商家等级
     * @return bool
     */
    public static function allocatePurchasers($shopId, $userIds, $shopTierLevel)
    {
        $year = date('Y');
        $time = time();
        $data = [];
        
        foreach ($userIds as $userId) {
            // 检查是否已分配
            if (self::isUserAllocated($shopId, $userId, $year)) {
                continue;
            }
            
            // 获取用户信息
            $user = Db::name('user')->where('id', $userId)->find();
            if (!$user || $user['shop_id'] == $shopId) {
                // 跳过不存在的用户或商家自己的用户
                continue;
            }
            
            // 根据活跃度积分确定等级
            $userLevel = self::getUserLevelByScore($user['activity_score']);
            
            $data[] = [
                'shop_id' => $shopId,
                'user_id' => $userId,
                'user_level' => $userLevel,
                'user_score' => $user['activity_score'],
                'allocation_year' => $year,
                'allocation_time' => $time,
                'shop_tier_level' => $shopTierLevel,
                'status' => 1,
                'create_time' => $time,
                'update_time' => $time
            ];
        }
        
        if (empty($data)) {
            return true;
        }
        
        return self::insertAll($data) !== false;
    }

    /**
     * 根据积分获取用户等级
     * @param int $score 活跃度积分
     * @return int
     */
    public static function getUserLevelByScore($score)
    {
        if ($score >= 501) {
            return 3; // 高活跃度
        } elseif ($score >= 101) {
            return 2; // 中活跃度
        } else {
            return 1; // 低活跃度
        }
    }

    /**
     * 获取可分配的采购人员
     * @param int $excludeShopId 排除的商家ID
     * @param int $level 用户等级
     * @param int $limit 数量限制
     * @return array
     */
    public static function getAvailablePurchasers($excludeShopId, $level, $limit)
    {
        $year = date('Y');
        
        // 根据等级确定积分范围
        $scoreWhere = '';
        switch ($level) {
            case 1:
                $scoreWhere = 'u.activity_score < 101';
                break;
            case 2:
                $scoreWhere = 'u.activity_score >= 101 AND u.activity_score < 501';
                break;
            case 3:
                $scoreWhere = 'u.activity_score >= 501';
                break;
        }
        
        // 获取未分配的采购人员
        $allocatedUserIds = self::where([
            ['allocation_year', '=', $year],
            ['status', '=', 1]
        ])->column('user_id');
        
        $where = [
            ['u.is_purchaser', '=', 1],
            ['u.disable', '=', 0],
            ['u.del', '=', 0],
            ['u.shop_id', '<>', $excludeShopId]
        ];
        
        if (!empty($allocatedUserIds)) {
            $where[] = ['u.id', 'not in', $allocatedUserIds];
        }
        
        $users = Db::name('user')
            ->alias('u')
            ->where($where)
            ->whereRaw($scoreWhere)
            ->field('u.id, u.nickname, u.activity_score')
            ->order('u.activity_score desc')
            ->limit($limit)
            ->select()
            ->toArray();
        
        // 如果采购人员不足，用普通用户补充
        if (count($users) < $limit) {
            $needCount = $limit - count($users);
            $existUserIds = array_column($users, 'id');
            
            $normalWhere = [
                ['u.is_purchaser', '=', 0],
                ['u.disable', '=', 0],
                ['u.del', '=', 0],
                ['u.shop_id', '<>', $excludeShopId]
            ];
            
            if (!empty($allocatedUserIds)) {
                $normalWhere[] = ['u.id', 'not in', array_merge($allocatedUserIds, $existUserIds)];
            } else {
                $normalWhere[] = ['u.id', 'not in', $existUserIds];
            }
            
            $normalUsers = Db::name('user')
                ->alias('u')
                ->where($normalWhere)
                ->whereRaw($scoreWhere)
                ->field('u.id, u.nickname, u.activity_score')
                ->order('u.activity_score desc')
                ->limit($needCount)
                ->select()
                ->toArray();
            
            $users = array_merge($users, $normalUsers);
        }
        
        return $users;
    }

    /**
     * 清除商家的分配记录
     * @param int $shopId 商家ID
     * @param int $year 年份
     * @return bool
     */
    public static function clearShopAllocation($shopId, $year = null)
    {
        $year = $year ?: date('Y');
        
        return self::where([
            ['shop_id', '=', $shopId],
            ['allocation_year', '=', $year]
        ])->update(['status' => 0, 'update_time' => time()]) !== false;
    }

    /**
     * 获取商家分配统计
     * @param int $shopId 商家ID
     * @param int $year 年份
     * @return array
     */
    public static function getShopAllocationStats($shopId, $year = null)
    {
        $year = $year ?: date('Y');
        
        $stats = self::where([
            ['shop_id', '=', $shopId],
            ['allocation_year', '=', $year],
            ['status', '=', 1]
        ])
        ->field('user_level, count(*) as count')
        ->group('user_level')
        ->select()
        ->toArray();
        
        $result = [
            'total' => 0,
            'level1' => 0,
            'level2' => 0,
            'level3' => 0
        ];
        
        foreach ($stats as $stat) {
            $result['total'] += $stat['count'];
            $result['level' . $stat['user_level']] = $stat['count'];
        }
        
        return $result;
    }
}
