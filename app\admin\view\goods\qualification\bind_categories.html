{layout name="layout1" /}
<div class="wrapper">
    <div class="layui-card">
     
        <div class="layui-card-body">
            <div class="layui-row">
                <div class="layui-col-md12">
                    <div style="border: 1px solid #e6e6e6; padding: 15px; border-radius: 4px; background: #fafafa;">
                        <div style="margin-bottom: 10px;">
                            <span style="font-weight: bold; color: #333;">资质信息：</span>
                            <span style="color: #666;">{$qualification.description}</span>
                        </div>
                        <div style="margin-bottom: 10px; padding: 8px; background: #f0f9ff; border: 1px solid #bae7ff; border-radius: 4px; font-size: 12px; color: #1890ff;">
                            <i class="layui-icon layui-icon-tips" style="margin-right: 5px;"></i>
                            <strong>操作说明：</strong>
                            选中子分类时，父分类会显示为半选状态（虚选），表示该主类下有子类绑定了此资质。
                            只有完全选中的分类才会被保存。
                        </div>
                        <div style="margin-bottom: 15px;">
                            <button type="button" class="layui-btn layui-btn-sm" id="expand-all">全部展开</button>
                            <button type="button" class="layui-btn layui-btn-sm layui-btn-primary" id="collapse-all">全部收起</button>
                            <button type="button" class="layui-btn layui-btn-sm layui-btn-warm" id="check-all">全选</button>
                            <button type="button" class="layui-btn layui-btn-sm layui-btn-primary" id="uncheck-all">全不选</button>
                            <button type="button" class="layui-btn layui-btn-sm layui-btn-normal" id="save-binding" style="float: right;">
                                <i class="layui-icon layui-icon-ok"></i> 保存绑定
                            </button>
                        </div>
                        <div id="category-tree" style="max-height: 400px; overflow-y: auto; border: 1px solid #e6e6e6; background: white; border-radius: 4px;">
                            <div style="text-align: center; color: #999; padding: 20px;">正在加载分类数据...</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.category-tree-container {
    font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
}

.category-item {
    position: relative;
    border-bottom: 1px solid #f0f0f0;
    transition: background-color 0.2s;
}

.category-item:hover {
    background-color: #f8f9fa;
}

.category-item:last-child {
    border-bottom: none;
}

.category-content {
    display: flex;
    align-items: center;
    padding: 8px 0;
    cursor: pointer;
}

.category-indent {
    width: 20px;
    height: 20px;
    display: inline-block;
}

.category-toggle {
    width: 20px;
    height: 20px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: #666;
    font-size: 12px;
    margin-right: 5px;
}

.category-toggle:hover {
    color: #1890ff;
}

.category-toggle.empty {
    cursor: default;
}

.category-checkbox {
    margin-right: 8px;
    transform: scale(1.1);
}

/* 半选状态样式 */
.category-checkbox.indeterminate {
    position: relative;
    background-color: #e6f7ff;
    border-color: #1890ff;
}

.category-checkbox.indeterminate:after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 8px;
    height: 2px;
    background-color: #1890ff;
    transform: translate(-50%, -50%);
    border-radius: 1px;
    z-index: 1;
}

.category-checkbox.indeterminate:checked:after {
    display: none;
}

/* 半选状态的标签样式 */
.category-item.has-indeterminate > .category-content > .category-label {
    color: #1890ff;
    font-style: italic;
}

.category-item.has-indeterminate > .category-content > .category-label:after {
    content: ' (部分子类已绑定)';
    color: #999;
    font-size: 11px;
    font-style: normal;
}

.category-label {
    flex: 1;
    font-size: 14px;
    color: #333;
    user-select: none;
}

.category-children {
    display: none;
}

.category-children.expanded {
    display: block;
}

.category-item.expanded > .category-content > .category-toggle {
    transform: rotate(90deg);
}

.category-count {
    color: #999;
    font-size: 12px;
    margin-left: 5px;
}
</style>

<script>
layui.config({
    version: "{$front_version}",
    base: '/static/lib'
}).use(['layer'], function(){
    var layer = layui.layer;
    var $ = layui.$;

    var qualificationId = {$qualification.id};

    // 加载分类数据
    function loadCategories() {
        like.ajax({
            url: '{:url("goods.qualification/getCategoriesTree")}',
            data: {
                qualification_id: qualificationId
            },
            type: 'get',
            success: function(res) {
                if (res.code === 1) {
                    renderCategoryTree(res.data);
                } else {
                    layer.msg(res.msg || '加载分类失败', {icon: 2});
                }
            },
            error: function() {
                layer.msg('网络错误，请重试', {icon: 2});
            }
        });
    }

    // 渲染分类树
    function renderCategoryTree(data) {
        if (!data || data.length === 0) {
            $('#category-tree').html('<div style="text-align: center; color: #999; padding: 20px;">暂无分类数据</div>');
            return;
        }

        var html = '<div class="category-tree-container" style="padding: 10px;">';

        function buildCategoryHtml(categories, level) {
            level = level || 0;
            categories.forEach(function(category) {
                var hasChildren = category.children && category.children.length > 0;
                var checked = category.checked ? 'checked' : '';
                var indent = '';

                // 生成缩进
                for (var i = 0; i < level; i++) {
                    indent += '<span class="category-indent"></span>';
                }

                html += '<div class="category-item" data-id="' + category.id + '">';
                html += '<div class="category-content" style="padding-left: ' + (level * 20) + 'px;">';

                // 展开/收起图标
                if (hasChildren) {
                    html += '<span class="category-toggle"><i class="layui-icon layui-icon-right"></i></span>';
                } else {
                    html += '<span class="category-toggle empty"></span>';
                }

                // checkbox
                html += '<input type="checkbox" class="category-checkbox" name="category_ids[]" value="' + category.id + '" ' + checked + '>';

                // 分类名称
                html += '<span class="category-label">' + category.title;
                if (hasChildren) {
                    html += '<span class="category-count">(' + category.children.length + '个子分类)</span>';
                }
                html += '</span>';

                html += '</div>';

                // 子分类
                if (hasChildren) {
                    html += '<div class="category-children">';
                    buildCategoryHtml(category.children, level + 1);
                    html += '</div>';
                }

                html += '</div>';
            });
        }

        buildCategoryHtml(data);
        html += '</div>';

        $('#category-tree').html(html);

        // 初始化半选状态
        initializeIndeterminateState();

        // 绑定展开/收起事件
        $('#category-tree').on('click', '.category-toggle:not(.empty)', function(e) {
            e.stopPropagation();
            var $item = $(this).closest('.category-item');
            var $children = $item.find('> .category-children');

            if ($children.hasClass('expanded')) {
                $children.removeClass('expanded');
                $item.removeClass('expanded');
            } else {
                $children.addClass('expanded');
                $item.addClass('expanded');
            }
        });

        // 绑定标签点击事件
        $('#category-tree').on('click', '.category-label', function(e) {
            e.stopPropagation();
            var $checkbox = $(this).siblings('.category-checkbox');
            $checkbox.prop('checked', !$checkbox.prop('checked')).trigger('change');
        });

        // 绑定checkbox变化事件 - 实现级联选择
        $('#category-tree').on('change', '.category-checkbox', function() {
            var $this = $(this);
            var $item = $this.closest('.category-item');
            var isChecked = $this.prop('checked');

            // 清除当前项的半选状态
            $this.removeClass('indeterminate');

            // 如果选中，则选中所有子级
            if (isChecked) {
                $item.find('.category-children .category-checkbox').prop('checked', true).removeClass('indeterminate');
            } else {
                // 如果取消选中，则取消所有子级
                $item.find('.category-children .category-checkbox').prop('checked', false).removeClass('indeterminate');
            }

            // 检查父级状态
            updateParentCheckbox($item);
        });

        // 更新父级checkbox状态
        function updateParentCheckbox($item) {
            var $parent = $item.parent().closest('.category-item');
            if ($parent.length === 0) return;

            var $parentCheckbox = $parent.find('> .category-content .category-checkbox');
            var $siblings = $item.siblings('.category-item');
            var $allSiblings = $siblings.add($item);

            var checkedCount = 0;
            var indeterminateCount = 0;
            var totalCount = $allSiblings.length;

            $allSiblings.each(function() {
                var $childCheckbox = $(this).find('> .category-content .category-checkbox');
                if ($childCheckbox.prop('checked')) {
                    checkedCount++;
                } else if ($childCheckbox.hasClass('indeterminate')) {
                    indeterminateCount++;
                }
            });

            // 清除父级的半选状态和CSS类
            $parentCheckbox.removeClass('indeterminate');
            $parent.removeClass('has-indeterminate');

            if (checkedCount === totalCount) {
                // 所有子级都选中，选中父级
                $parentCheckbox.prop('checked', true);
            } else if (checkedCount === 0 && indeterminateCount === 0) {
                // 所有子级都未选中，取消父级
                $parentCheckbox.prop('checked', false);
            } else {
                // 部分选中或有半选状态，设置父级为半选状态
                $parentCheckbox.prop('checked', false).addClass('indeterminate');
                $parent.addClass('has-indeterminate');
            }

            // 递归更新上级
            updateParentCheckbox($parent);
        }

        // 初始化半选状态
        function initializeIndeterminateState() {
            // 从最深层开始，向上检查每个父级的状态
            $('#category-tree .category-item').each(function() {
                var $item = $(this);
                var $checkbox = $item.find('> .category-content .category-checkbox');
                var $children = $item.find('> .category-children .category-item');

                if ($children.length > 0) {
                    var checkedCount = 0;
                    var indeterminateCount = 0;
                    var totalCount = $children.length;

                    $children.each(function() {
                        var $childCheckbox = $(this).find('> .category-content .category-checkbox');
                        if ($childCheckbox.prop('checked')) {
                            checkedCount++;
                        } else if ($childCheckbox.hasClass('indeterminate')) {
                            indeterminateCount++;
                        }
                    });

                    // 清除当前的半选状态和CSS类
                    $checkbox.removeClass('indeterminate');
                    $item.removeClass('has-indeterminate');

                    if (checkedCount === totalCount) {
                        // 所有子级都选中，选中父级
                        $checkbox.prop('checked', true);
                    } else if (checkedCount === 0 && indeterminateCount === 0) {
                        // 所有子级都未选中，取消父级
                        $checkbox.prop('checked', false);
                    } else {
                        // 部分选中或有半选状态，设置父级为半选状态
                        $checkbox.prop('checked', false).addClass('indeterminate');
                        $item.addClass('has-indeterminate');
                    }
                }
            });
        }
    }

    // 全部展开
    $('#expand-all').click(function() {
        $('#category-tree .category-children').addClass('expanded');
        $('#category-tree .category-item').addClass('expanded');
    });

    // 全部收起
    $('#collapse-all').click(function() {
        $('#category-tree .category-children').removeClass('expanded');
        $('#category-tree .category-item').removeClass('expanded');
    });

    // 全选
    $('#check-all').click(function() {
        $('#category-tree .category-checkbox').prop('checked', true).removeClass('indeterminate');
        $('#category-tree .category-item').removeClass('has-indeterminate');
    });

    // 全不选
    $('#uncheck-all').click(function() {
        $('#category-tree .category-checkbox').prop('checked', false).removeClass('indeterminate');
        $('#category-tree .category-item').removeClass('has-indeterminate');
    });

    // 保存绑定
    $('#save-binding').click(function() {
        // 获取所有真正选中的checkbox值（排除半选状态）
        var categoryIds = [];
        $('#category-tree .category-checkbox:checked').each(function() {
            var $this = $(this);
            // 只保存真正选中的（非半选状态）
            if (!$this.hasClass('indeterminate')) {
                var categoryId = parseInt($this.val());
                if (categoryId) {
                    categoryIds.push(categoryId);
                }
            }
        });

        console.log('选中的分类ID:', categoryIds);

        if (categoryIds.length === 0) {
            layer.msg('请至少选择一个分类', {icon: 2});
            return;
        }

        var loadIndex = layer.load(2);
        like.ajax({
            url: '{:url("goods.qualification/saveBinding")}',
            data: {
                id: qualificationId,
                category_ids: categoryIds
            },
            type: 'post',
            success: function(res) {
                layer.close(loadIndex);
                if (res.code === 1) {
                    layer.msg('绑定成功', {icon: 1});
                    // 关闭弹窗并刷新父页面
                    setTimeout(function(){
                        var index = parent.layer.getFrameIndex(window.name);
                        parent.layer.close(index);
                        parent.location.reload();
                    }, 1000);
                } else {
                    layer.msg(res.msg || '绑定失败', {icon: 2});
                }
            },
            error: function() {
                layer.close(loadIndex);
                layer.msg('网络错误，请重试', {icon: 2});
            }
        });
    });

    // 初始化加载
    loadCategories();
});
</script>
