{layout name="layout1" /}
<style> .layui-table-cell { height: auto; } </style>
<div class="wrapper">
    <div class="layui-card">
        <!-- 操作提示 -->
        {if !$bargain_id}
        <div class="layui-card-body">
            <div class="layui-collapse like-layui-collapse" style="border:1px dashed #c4c4c4">
                <div class="layui-colla-item">
                    <h2 class="layui-colla-title like-layui-colla-title" style="background-color: #fff">操作提示</h2>
                    <div class="layui-colla-content layui-show">
                        <p>*砍价列表，查看帮砍人数，信息，砍价订单等信息；</p>
                    </div>
                </div>
            </div>
        </div>
        {/if}

        <!-- 搜索模块 -->
        <div class="layui-tab layui-tab-card" lay-filter="tab-all">
            <div class="layui-card-body layui-form">
                <div class="layui-form-item">
                    <div class="layui-row">
                        <div class="layui-inline">
                            <label for="bargain_sn" class="layui-form-label">砍价编号：</label>
                            <div class="layui-input-block">
                                <input type="text" id="bargain_sn" name="bargain_sn" placeholder="请输入" autocomplete="off" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label for="goods_name" class="layui-form-label">商品名称：</label>
                            <div class="layui-input-block">
                                <input type="text" id="goods_name" name="goods_name" placeholder="请输入" autocomplete="off" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label for="status" class="layui-form-label">砍价状态：</label>
                            <div class="layui-input-inline">
                                <select name="status" id="status" >
                                    <option value="">全部</option>
                                    <option value="0">砍价中</option>
                                    <option value="1">砍价成功</option>
                                    <option value="2">砍价失败</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="layui-row">
                        <div class="layui-inline">
                            <label for="keyword_type" class="layui-form-label">发起用户：</label>
                            <div class="layui-input-block">
                                <select id="keyword_type" name="keyword_type" >
                                    <option value="">请选择</option>
                                    <option value="sn">会员编号</option>
                                    <option value="nickname">用户昵称</option>
                                    <option value="mobile">手机号码</option>
                                </select>
                            </div>
                        </div>
                        <div class="layui-inline">
                            <input type="text" id="keyword" name="keyword" placeholder="请输入" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-row">
                        <div class="layui-inline">
                            <label class="layui-form-label">发起时间：</label>
                            <div class="layui-inline">
                                <input type="text" id="launch_start_time" name="launch_start_time" placeholder="开始时间" autocomplete="off" class="layui-input">
                            </div>
                            <div class="layui-inline"> - </div>
                            <div class="layui-inline">
                                <input type="text" id="launch_end_time" name="launch_end_time" placeholder="结束时间" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <button class="layui-btn layui-btn-sm  layuiadmin-btn-article {$view_theme_color}" lay-submit lay-filter="search">
                                <i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>
                            </button>
                            <button class="layui-btn layui-btn-sm layui-btn-primary layuiadmin-btn-article }" lay-submit lay-filter="clear-search">重置</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 数据表格 -->
        <div class="layui-card-body">
            <table id="table-lists" lay-filter="table-lists"></table>
            <script type="text/html" id="table-user">
                {{#  if((d.user) == null){ }}
                        会员信息缺失
                {{#  } else { }}
                <img src="{{d.user.avatar}}" style="height:80px;width: 80px" class="image-show">
                <div class="layui-input-inline"  style="text-align: left">
                    <p>会员编号:{{d.user.sn}}</p>
                    <p>用户昵称:{{d.user.nickname}}</p>
                    <p>会员等级:{{d.user.level.name ?? '无'}}</p>
                </div>
                {{#  } }}
            </script>
            <script type="text/html" id="table-goods">
                <div>
                    <img src="{{d.goods_image}}" alt="图片" style="width:60px;height:60px;float:left;" class="image-show">
                    <div class="layui-inline">
                        <div >{{d.goods_snap.name}}</div>
                        <div>{{d.goods_snap.spec_value_str}}&nbsp; ￥{{d.goods_snap.price}}</div>
                    </div>
                </div>
            </script>
            <script type="text/html" id="table-start_time">
                <div style="width:100px;height:60px;white-space:normal;">
                    {{d.launch_start_time}}
                </div>
            </script>
            <script type="text/html" id="table-end_time">
                <div style="width:100px;height:60px;white-space:normal;">
                    {{d.launch_end_time}}
                </div>
            </script>

            <script type="text/html" id="operation">
                <a class="layui-btn layui-btn-normal layui-btn-sm " lay-event="detail">详情</a>
            </script>
        </div>
    </div>
</div>

<script>
    layui.config({
        base: '/static/plug/layui-admin/dist/layuiadmin/' //静态资源所在路径
    }).extend({
        index: 'lib/index' //主入口模块
    }).use(['index','table','like', 'laydate'], function(){
        var $ = layui.$
            ,form = layui.form
            ,table = layui.table
            ,like = layui.like
            ,laydate = layui.laydate;

        // 渲染数据表格
        table.render({
            elem: '#table-lists'
            ,url: '{:url("bargain.Bargain/launch")}?bargain_id={$bargain_id}'
            ,cols: [[
                {field: 'id', title: '序号',width:60, align:"center"}
                ,{field: 'bargain_sn', title: '砍价编号',width:160, align:"center"}
                ,{field: 'user', title: '发起用户',width:300, templet: '#table-user'}
                ,{field: 'goods', title: '砍价商品',width:250, templet: '#table-goods'}
                ,{field: 'launchStartTime', title: '发起时间', width:120, align: 'center', templet: '#table-start_time'}
                ,{field: 'launchEndTime', title: '结束时间', width:120, align: 'center', templet: '#table-end_time'}
                ,{field: 'help_number', title: '已砍价次数', width:120, align: 'center'}
                ,{field: 'current_price', title: '当前价格', width:120, align: 'center'}
                ,{field: 'status', title: '砍价状态', width:100, align: 'center'}
                ,{fixed: 'right', title: '操作', align: 'center', toolbar: '#operation',width:100}
            ]]
            ,page:true
            ,text: {none: '暂无数据！'}
            ,response: {
                statusCode: 1
            }
            ,parseData: function(res){
                return {
                    "code":res.code,
                    "msg":res.msg,
                    "count": res.data.count,
                    "data": res.data.lists
                };
            }
            ,done: function(res, curr, count){
                $(".layui-table-main tr").each(function (index, val) {
                    $($(".layui-table-fixed-l .layui-table-body tbody tr")[index]).height($(val).height());
                    $($(".layui-table-fixed-r .layui-table-body tbody tr")[index]).height($(val).height());
                });
            }
        });

        //事件
        var active = {
            detail: function (obj) {
                layer.open({
                    type: 2
                    ,title: '砍价详情'
                    ,content: '{:url("bargain.Bargain/detail")}?id='+obj.data.id
                    ,area: ['90%','90%']
                });
            }
        };

        // 监听表格右侧工具条
        table.on('tool(table-lists)', function(obj){
            var type = obj.event;
            active[type] ? active[type].call(this, obj) : '';
        });

        // 切换状态
        form.on('switch(switch-status)',function (obj) {
            var id = obj.elem.attributes['data-id'].nodeValue;
            var fields = obj.elem.attributes['data-field'].nodeValue;
            var status = this.checked ? 1 : 0;
            var data = {"id":id, "field":fields, "status":status};
            active['switchStatus'] ? active['switchStatus'].call(this, data) : '';
        });

        // 绑定点击按钮事件
        $('.layui-btn.layuiadmin-btn').on('click', function(){
            var type = $(this).data('type');
            active[type] ? active[type].call(this) : '';
        });

        //图片放大
        $(document).on('click', '.image-show', function () {
            var src = $(this).attr('src');
            like.showImg(src);
        });
        
        // 开始时间
        laydate.render({
            type: 'datetime'
            ,elem: '#launch_start_time'
            ,trigger: 'click'
            ,done: function (value, date, endDate) {
                var startDate = new Date(value).getTime();
                var endTime = new Date($('#launch_end_time').val()).getTime();
                if (endTime < startDate) {
                    layer.msg('结束时间不能小于开始时间');
                    $('#launch_start_time').val($('#launch_end_time').val());
                }
            }
        });
        laydate.render({
            type: 'datetime'
            ,elem: '#launch_end_time'
            ,trigger: 'click'
            ,done: function (value, date, endDate) {
                var startDate = new Date($('#launch_start_time').val()).getTime();
                var endTime = new Date(value).getTime();
                if (endTime < startDate) {
                    layer.msg('结束时间不能小于开始时间');
                    $('#launch_end_time').val($('#launch_start_time').val());
                }
            }
        });

        //监听搜索
        form.on('submit(search)', function(data){
            var field = data.field;
            table.reload('table-lists', {
                where: field,
                page: { curr: 1 }
            });
        });

        // 监听重置搜素
        form.on('submit(clear-search)', function(){
            $('#bargain_sn').val('');
            $('#goods_name').val('');
            $('#status').val('');
            $('#keyword_type').val('');
            $('#keyword').val('');
            $('#launch_start_time').val('');
            $('#launch_end_time').val('');
            form.render('select');
            table.reload('table-lists', { where: [] });
        });

        $(document).on('click', '.day', function () {
            var value = parseInt($(this).attr('day'));
            var toDay = Times.getDay(value, false);
            var curDay = Times.getDay(0);

            $("#launch_start_time").val(toDay + ' 00:00:00');
            $("#launch_end_time").val(curDay + ' 00:00:00');
        })

    });
</script>