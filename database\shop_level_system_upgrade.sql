-- 商家等级体系升级SQL脚本
-- 执行前请备份相关表数据

-- 1. 修改ls_shop表，新增等级相关字段
ALTER TABLE `ls_shop` 
ADD COLUMN `level` tinyint(1) NOT NULL DEFAULT 0 COMMENT '商家等级：0=0元入驻，1=商家会员，2=实力厂商' AFTER `type`,
ADD COLUMN `level_expire_time` int(11) NOT NULL DEFAULT 0 COMMENT '等级到期时间' AFTER `expire_time`,
ADD COLUMN `level_upgrade_time` int(11) NOT NULL DEFAULT 0 COMMENT '等级升级时间' AFTER `level_expire_time`,
ADD COLUMN `level_features` text COMMENT '等级特权JSON配置' AFTER `level_upgrade_time`;

-- 2. 修改ls_shop_apply表，支持新的申请流程
ALTER TABLE `ls_shop_apply`
ADD COLUMN `target_level` tinyint(1) NOT NULL DEFAULT 0 COMMENT '目标等级：0=0元入驻，1=商家会员，2=实力厂商' AFTER `cid`,
ADD COLUMN `pre_paid` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否预付费：0=未支付，1=已支付' AFTER `target_level`,
ADD COLUMN `payment_order_sn` varchar(32) DEFAULT NULL COMMENT '支付订单号' AFTER `pre_paid`,
ADD COLUMN `payment_time` int(11) DEFAULT NULL COMMENT '支付时间' AFTER `payment_order_sn`;

-- 3. 修改ls_shop_merchantfees表，新增等级字段
ALTER TABLE `ls_shop_merchantfees`
ADD COLUMN `target_level` tinyint(1) NOT NULL DEFAULT 1 COMMENT '目标等级：1=商家会员，2=实力厂商' AFTER `feetype`,
ADD COLUMN `upgrade_type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '升级类型：0=新入驻，1=等级升级' AFTER `target_level`;

-- 4. 创建商家等级配置表
CREATE TABLE `ls_shop_level_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `level` tinyint(1) NOT NULL COMMENT '等级：0=0元入驻，1=商家会员，2=实力厂商',
  `name` varchar(50) NOT NULL COMMENT '等级名称',
  `price` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '等级价格',
  `duration` int(11) NOT NULL DEFAULT 31536000 COMMENT '有效期（秒），默认1年',
  `features` text COMMENT '功能特权JSON配置',
  `limits` text COMMENT '限制配置JSON',
  `description` text COMMENT '等级描述',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：0=禁用，1=启用',
  `create_time` int(11) NOT NULL,
  `update_time` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `level` (`level`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商家等级配置表';

-- 5. 创建商家等级升级记录表
CREATE TABLE `ls_shop_level_upgrade` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `shop_id` int(11) NOT NULL COMMENT '商家ID',
  `from_level` tinyint(1) NOT NULL COMMENT '原等级',
  `to_level` tinyint(1) NOT NULL COMMENT '目标等级',
  `order_sn` varchar(32) NOT NULL COMMENT '订单号',
  `amount` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '升级费用',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '状态：0=待支付，1=已支付，2=已生效',
  `pay_time` int(11) DEFAULT NULL COMMENT '支付时间',
  `effect_time` int(11) DEFAULT NULL COMMENT '生效时间',
  `expire_time` int(11) DEFAULT NULL COMMENT '到期时间',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `create_time` int(11) NOT NULL,
  `update_time` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_shop_id` (`shop_id`),
  KEY `idx_order_sn` (`order_sn`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商家等级升级记录表';

-- 6. 插入默认等级配置数据
INSERT INTO `ls_shop_level_config` (`level`, `name`, `price`, `duration`, `features`, `limits`, `description`, `sort`, `create_time`, `update_time`) VALUES
(0, '0元入驻', 0.00, 31536000, '{"goods_limit":50,"basic_decoration":true,"basic_service":true}', '{"goods_count":50,"decoration_templates":["basic"],"marketing_tools":[]}', '免费注册，享受基础功能', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(1, '商家会员', 1000.00, 31536000, '{"goods_limit":500,"advanced_decoration":true,"marketing_tools":true,"data_analysis":true}', '{"goods_count":500,"decoration_templates":["basic","advanced"],"marketing_tools":["coupon","discount"]}', '付费会员，享受更多营销工具和数据分析', 2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(2, '实力厂商', 3000.00, 31536000, '{"goods_unlimited":true,"full_decoration":true,"all_marketing_tools":true,"advanced_analysis":true,"factory_certification":true,"vip_service":true}', '{"goods_count":-1,"decoration_templates":["basic","advanced","premium"],"marketing_tools":["coupon","discount","group_buy","flash_sale"]}', '最高等级，包含验厂认证和VIP服务', 3, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());
