<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商品管理增强功能测试页面</title>
    <link rel="stylesheet" href="/static/lib/layui/css/layui.css">
    <link rel="stylesheet" href="/static/common/css/goods-spec-enhanced.css">
    <style>
        body { padding: 20px; background-color: #f5f5f5; }
        .test-section { 
            background: #fff; 
            margin: 20px 0; 
            padding: 20px; 
            border-radius: 6px; 
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .test-title { 
            font-size: 18px; 
            font-weight: bold; 
            color: #333; 
            margin-bottom: 15px;
            border-bottom: 2px solid #1890ff;
            padding-bottom: 8px;
        }
        .test-result { 
            margin: 10px 0; 
            padding: 8px 12px; 
            border-radius: 4px; 
        }
        .test-success { background-color: #f6ffed; border: 1px solid #b7eb8f; color: #52c41a; }
        .test-error { background-color: #fff2f0; border: 1px solid #ffccc7; color: #ff4d4f; }
        .test-info { background-color: #e6f7ff; border: 1px solid #91d5ff; color: #1890ff; }
    </style>
</head>
<body>
    <div class="layui-container">
        <h1 style="text-align: center; color: #333; margin-bottom: 30px;">
            🛠️ 商品管理增强功能测试页面
        </h1>

        <!-- 输入框测试 -->
        <div class="test-section">
            <div class="test-title">📝 输入框增强测试</div>
            <div class="layui-form">
                <div class="layui-form-item">
                    <label class="layui-form-label">统一规格价格：</label>
                    <div class="layui-input-block">
                        <input type="number" name="one_price" class="layui-input spec-input-field" 
                               placeholder="请输入价格" step="0.01" min="0" data-field="one_price" tabindex="0">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">统一规格库存：</label>
                    <div class="layui-input-block">
                        <input type="number" name="one_stock" class="layui-input spec-input-field" 
                               placeholder="请输入库存" min="0" data-field="one_stock" tabindex="0">
                    </div>
                </div>
            </div>
            
            <!-- 多规格表格测试 -->
            <div style="margin-top: 20px;">
                <h4>多规格表格测试：</h4>
                <table class="layui-table spec-lists-table">
                    <thead>
                        <tr>
                            <th>规格</th>
                            <th>价格</th>
                            <th>库存</th>
                            <th>重量</th>
                            <th>条码</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>红色 / S</td>
                            <td><input type="number" class="layui-input spec-input-field" name="price[]" 
                                     step="0.01" min="0" data-field="price" tabindex="0" placeholder="销售价"></td>
                            <td><input type="number" class="layui-input spec-input-field" name="stock[]" 
                                     min="0" data-field="stock" tabindex="0" placeholder="库存数量"></td>
                            <td><input type="number" class="layui-input spec-input-field" name="weight[]" 
                                     step="0.001" min="0" data-field="weight" tabindex="0" placeholder="重量(kg)"></td>
                            <td><input type="text" class="layui-input spec-input-field" name="bar_code[]" 
                                     maxlength="50" data-field="bar_code" tabindex="0" placeholder="商品条码"></td>
                        </tr>
                        <tr>
                            <td>蓝色 / M</td>
                            <td><input type="number" class="layui-input spec-input-field" name="price[]" 
                                     step="0.01" min="0" data-field="price" tabindex="0" placeholder="销售价"></td>
                            <td><input type="number" class="layui-input spec-input-field" name="stock[]" 
                                     min="0" data-field="stock" tabindex="0" placeholder="库存数量"></td>
                            <td><input type="number" class="layui-input spec-input-field" name="weight[]" 
                                     step="0.001" min="0" data-field="weight" tabindex="0" placeholder="重量(kg)"></td>
                            <td><input type="text" class="layui-input spec-input-field" name="bar_code[]" 
                                     maxlength="50" data-field="bar_code" tabindex="0" placeholder="商品条码"></td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <div id="inputTestResult" class="test-result test-info">
                正在测试输入框功能...
            </div>
        </div>

        <!-- 视频管理测试 -->
        <div class="test-section">
            <div class="test-title">🎥 视频管理测试</div>
            <div class="layui-form-item">
                <label class="layui-form-label">商品视频：</label>
                <div class="layui-input-block" id="videoContainer">
                    <div class="like-upload-video">
                        <div class="upload-image-elem">
                            <a class="add-upload-video" id="video"> + 添加视频</a>
                        </div>
                    </div>
                    <!-- 视频预览区域 -->
                    <div id="videoPreviewArea" style="display: none;">
                        <div class="video-preview-container">
                            <video id="videoPreview" controls style="max-width: 300px; max-height: 200px; border-radius: 4px;">
                                您的浏览器不支持视频播放
                            </video>
                            <div class="video-info">
                                <span id="videoFileName" class="video-file-name"></span>
                                <span id="videoFileSize" class="video-file-size"></span>
                            </div>
                            <div class="video-actions">
                                <button type="button" class="layui-btn layui-btn-sm layui-btn-normal" id="replaceVideoBtn">
                                    <i class="layui-icon layui-icon-refresh"></i> 重新选择
                                </button>
                                <button type="button" class="layui-btn layui-btn-sm layui-btn-danger" id="deleteVideoBtn">
                                    <i class="layui-icon layui-icon-delete"></i> 删除视频
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div style="margin-top: 15px;">
                <button type="button" class="layui-btn layui-btn-sm" onclick="testVideoUpload()">
                    模拟视频上传
                </button>
                <button type="button" class="layui-btn layui-btn-sm layui-btn-primary" onclick="testVideoDelete()">
                    测试删除功能
                </button>
            </div>
            
            <div id="videoTestResult" class="test-result test-info">
                点击"模拟视频上传"测试视频管理功能
            </div>
        </div>

        <!-- 交互测试 -->
        <div class="test-section">
            <div class="test-title">🖱️ 交互增强测试</div>
            <div class="layui-row layui-col-space15">
                <div class="layui-col-md6">
                    <button type="button" class="layui-btn" onclick="testModal()">测试弹窗层级</button>
                    <button type="button" class="layui-btn layui-btn-primary" onclick="testTableFixed()">测试表格固定列</button>
                </div>
                <div class="layui-col-md6">
                    <button type="button" class="layui-btn layui-btn-normal" onclick="testScrollToElement()">测试滚动定位</button>
                    <button type="button" class="layui-btn layui-btn-warm" onclick="testFormSubmit()">测试表单提交</button>
                </div>
            </div>
            
            <div id="interactionTestResult" class="test-result test-info">
                点击上方按钮测试各项交互功能
            </div>
        </div>

        <!-- 测试结果汇总 -->
        <div class="test-section">
            <div class="test-title">📊 测试结果汇总</div>
            <div id="testSummary">
                <div class="test-result test-info">等待测试完成...</div>
            </div>
        </div>
    </div>

    <!-- 引入必要的脚本 -->
    <script src="/static/lib/layui/layui.js"></script>
    <script src="/static/common/js/goods-spec-enhanced.js"></script>
    <script src="/static/common/js/video-manager-enhanced.js"></script>
    <script src="/static/common/js/form-interaction-enhanced.js"></script>

    <script>
        // 测试结果记录
        var testResults = {
            inputEnhanced: false,
            videoManager: false,
            interaction: false
        };

        // 页面加载完成后开始测试
        layui.use(['layer', 'form'], function() {
            var layer = layui.layer;
            var form = layui.form;

            // 延迟执行测试，确保增强脚本加载完成
            setTimeout(function() {
                runInputTests();
                updateTestSummary();
            }, 1000);
        });

        // 输入框测试
        function runInputTests() {
            var $inputs = $('.spec-input-field');
            var enhancedCount = 0;
            var totalCount = $inputs.length;

            $inputs.each(function() {
                var $input = $(this);
                if ($input.data('enhanced') && $input.hasClass('spec-enhanced-input')) {
                    enhancedCount++;
                }
            });

            var result = enhancedCount === totalCount;
            testResults.inputEnhanced = result;

            var $resultDiv = $('#inputTestResult');
            if (result) {
                $resultDiv.removeClass('test-info test-error').addClass('test-success');
                $resultDiv.html('✅ 输入框增强测试通过 (' + enhancedCount + '/' + totalCount + ' 个输入框已增强)');
            } else {
                $resultDiv.removeClass('test-info test-success').addClass('test-error');
                $resultDiv.html('❌ 输入框增强测试失败 (' + enhancedCount + '/' + totalCount + ' 个输入框已增强)');
            }
        }

        // 模拟视频上传
        function testVideoUpload() {
            var mockVideoData = {
                url: 'data:video/mp4;base64,AAAAIGZ0eXBpc29tAAACAGlzb21pc28yYXZjMW1wNDEAAAAIZnJlZQAAAr1tZGF0',
                filename: 'test-video.mp4',
                size: 1024000
            };

            // 触发视频上传事件
            $(document).trigger('videoUploaded', mockVideoData);

            // 检查视频管理功能
            setTimeout(function() {
                var $previewArea = $('#videoPreviewArea');
                var isVisible = $previewArea.is(':visible');
                var hasControls = $('#deleteVideoBtn').length > 0 && $('#replaceVideoBtn').length > 0;

                testResults.videoManager = isVisible && hasControls;

                var $resultDiv = $('#videoTestResult');
                if (testResults.videoManager) {
                    $resultDiv.removeClass('test-info test-error').addClass('test-success');
                    $resultDiv.html('✅ 视频管理测试通过 (预览区域已显示，控制按钮已加载)');
                } else {
                    $resultDiv.removeClass('test-info test-success').addClass('test-error');
                    $resultDiv.html('❌ 视频管理测试失败 (预览: ' + isVisible + ', 控制按钮: ' + hasControls + ')');
                }

                updateTestSummary();
            }, 500);
        }

        // 测试视频删除
        function testVideoDelete() {
            if ($('#videoPreviewArea').is(':visible')) {
                $('#deleteVideoBtn').click();

                // 延迟检查删除后的状态
                setTimeout(function() {
                    var $addButton = $('#videoContainer .like-upload-video');
                    var $previewArea = $('#videoPreviewArea');

                    var addButtonVisible = $addButton.is(':visible');
                    var previewHidden = !$previewArea.is(':visible');

                    var $resultDiv = $('#videoTestResult');
                    if (addButtonVisible && previewHidden) {
                        $resultDiv.removeClass('test-info test-error').addClass('test-success');
                        $resultDiv.html('✅ 视频删除测试通过 (添加按钮已恢复显示)');
                    } else {
                        $resultDiv.removeClass('test-info test-success').addClass('test-error');
                        $resultDiv.html('❌ 视频删除测试失败 (添加按钮: ' + addButtonVisible + ', 预览隐藏: ' + previewHidden + ')');
                    }
                }, 1000);
            } else {
                layer.msg('请先模拟视频上传', {icon: 2});
            }
        }

        // 测试弹窗层级
        function testModal() {
            layer.open({
                type: 1,
                title: '测试弹窗',
                content: '<div style="padding: 20px;">这是一个测试弹窗，检查是否被正确显示</div>',
                success: function() {
                    testResults.interaction = true;
                    updateInteractionResult('弹窗层级测试通过');
                    updateTestSummary();
                }
            });
        }

        // 测试表格固定列
        function testTableFixed() {
            updateInteractionResult('表格固定列功能已优化，z-index已调整');
        }

        // 测试滚动定位
        function testScrollToElement() {
            $('html, body').animate({scrollTop: 0}, 300);
            setTimeout(function() {
                updateInteractionResult('滚动定位功能正常');
            }, 500);
        }

        // 测试表单提交
        function testFormSubmit() {
            updateInteractionResult('表单提交增强功能已加载');
        }

        // 更新交互测试结果
        function updateInteractionResult(message) {
            var $resultDiv = $('#interactionTestResult');
            $resultDiv.removeClass('test-info test-error').addClass('test-success');
            $resultDiv.html('✅ ' + message);
        }

        // 更新测试汇总
        function updateTestSummary() {
            var passedTests = 0;
            var totalTests = 3;

            if (testResults.inputEnhanced) passedTests++;
            if (testResults.videoManager) passedTests++;
            if (testResults.interaction) passedTests++;

            var $summary = $('#testSummary');
            var percentage = Math.round((passedTests / totalTests) * 100);

            var summaryHtml = '<div class="test-result ' + 
                (passedTests === totalTests ? 'test-success' : 'test-info') + '">';
            summaryHtml += '<strong>测试进度：' + passedTests + '/' + totalTests + ' (' + percentage + '%)</strong><br>';
            summaryHtml += '输入框增强：' + (testResults.inputEnhanced ? '✅ 通过' : '⏳ 待测试') + '<br>';
            summaryHtml += '视频管理：' + (testResults.videoManager ? '✅ 通过' : '⏳ 待测试') + '<br>';
            summaryHtml += '交互增强：' + (testResults.interaction ? '✅ 通过' : '⏳ 待测试');
            summaryHtml += '</div>';

            $summary.html(summaryHtml);
        }

        // 开启调试模式
        setTimeout(function() {
            if (window.GoodsSpecEnhanced) {
                GoodsSpecEnhanced.config.debug = true;
            }
            if (window.VideoManagerEnhanced) {
                VideoManagerEnhanced.config.debug = true;
            }
            if (window.FormInteractionEnhanced) {
                FormInteractionEnhanced.config.debug = true;
            }
        }, 2000);
    </script>
</body>
</html>
