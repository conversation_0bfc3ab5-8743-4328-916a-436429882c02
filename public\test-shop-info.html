<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商家后台信息显示测试</title>
    <link rel="stylesheet" href="/static/lib/layui/css/layui.css">
    <link rel="stylesheet" href="/static/admin/css/app.css">
    <style>
        /* Global Styles */
        body {
            background-color: #f4f6f8 !important;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
        }
        .layui-card {
            border-radius: 8px;
            box-shadow: 0 2px 12px 0 rgba(0,0,0,0.06);
            border: 1px solid #e6e6e6;
        }
        .layui-card-header {
            font-weight: 600;
            font-size: 16px;
            color: #333;
            background-color: #fff;
            border-bottom: 1px solid #f0f0f0;
        }
        .layui-header {
            box-shadow: 0 2px 4px rgba(0,0,0,.08);
        }
        .layui-side-menu .layui-nav-item a:hover {
            background-color: #f6f6f6;
        }
        .layui-logo {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 60px;
        }
        .layui-logo img {
            max-height: 40px;
            max-width: 100%;
        }
        
        /* 商家信息显示样式 */
        .shop-info-display {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .shop-logo {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            object-fit: cover;
            border: 2px solid #fff;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            flex-shrink: 0;
        }
        
        .shop-info-display a {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .shop-info-display cite {
            font-style: normal;
            font-weight: 500;
            color: #fff;
        }
        
        /* 侧边栏商家信息样式 */
        .shop-info-sidebar {
            padding: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 10px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            gap: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        
        .shop-logo-container {
            flex-shrink: 0;
        }
        
        .shop-logo-sidebar {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid rgba(255,255,255,0.3);
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        }
        
        .shop-name-container {
            flex: 1;
            min-width: 0;
        }
        
        .shop-name {
            color: #fff;
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 4px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        .shop-id {
            color: rgba(255,255,255,0.8);
            font-size: 12px;
            font-weight: 400;
        }
        
        /* 当没有logo时的样式调整 */
        .shop-info-sidebar:not(:has(.shop-logo-container)) {
            justify-content: center;
            text-align: center;
        }
        
        /* 测试页面特殊样式 */
        .test-container {
            padding: 20px;
        }
        
        .test-section {
            background: white;
            padding: 20px;
            margin-bottom: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .test-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }
        
        .demo-header {
            background: #393D49;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
        }
        
        .demo-sidebar {
            width: 200px;
            background: #2F4056;
            min-height: 400px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-section">
            <div class="test-title">商家后台头部信息显示测试</div>
            
            <!-- 模拟头部导航 -->
            <div class="demo-header">
                <div>左侧内容</div>
                <ul class="layui-nav layui-layout-right" style="background: transparent;">
                    <!-- 有logo的商家 -->
                    <li class="layui-nav-item shop-info-display">
                        <img src="https://picsum.photos/64/64?random=1" class="shop-logo" alt="测试商家">
                        <a href="javascript:">
                            <cite>管理员（测试商家有限公司）</cite>
                            <span class="layui-nav-more"></span>
                        </a>
                    </li>
                </ul>
            </div>
        </div>
        
        <div class="test-section">
            <div class="test-title">商家后台侧边栏信息显示测试</div>
            
            <div style="display: flex; gap: 20px;">
                <!-- 有logo的商家侧边栏 -->
                <div class="demo-sidebar">
                    <div class="layui-logo">
                        <img src="/static/common/image/default/logo.png" style="height:20px;width: 77px">
                    </div>
                    
                    <div class="shop-info-sidebar">
                        <div class="shop-logo-container">
                            <img src="https://picsum.photos/96/96?random=2" class="shop-logo-sidebar" alt="测试商家">
                        </div>
                        <div class="shop-name-container">
                            <div class="shop-name">测试商家有限公司</div>
                            <div class="shop-id">ID: 10001</div>
                        </div>
                    </div>
                </div>
                
                <!-- 没有logo的商家侧边栏 -->
                <div class="demo-sidebar">
                    <div class="layui-logo">
                        <img src="/static/common/image/default/logo.png" style="height:20px;width: 77px">
                    </div>
                    
                    <div class="shop-info-sidebar">
                        <div class="shop-name-container">
                            <div class="shop-name">无Logo商家</div>
                            <div class="shop-id">ID: 10002</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <div class="test-title">功能说明</div>
            <ul style="line-height: 1.8; color: #666;">
                <li><strong>头部显示：</strong>在商家后台头部右侧显示商家logo（如果有）和商家名称</li>
                <li><strong>侧边栏显示：</strong>在左侧菜单下方显示商家信息卡片，包含logo、商家名称和ID</li>
                <li><strong>响应式设计：</strong>logo采用圆形设计，支持不同尺寸的图片</li>
                <li><strong>优雅降级：</strong>如果商家没有设置logo，则不显示logo部分</li>
                <li><strong>视觉效果：</strong>使用渐变背景和阴影效果，提升视觉体验</li>
            </ul>
        </div>
    </div>

    <script src="/static/lib/layui/layui.js"></script>
    <script>
        layui.use(['element'], function(){
            var element = layui.element;
        });
    </script>
</body>
</html>
