<?php

use app\common\server\ConfigServer;
use app\common\server\UrlServer;
use think\facade\Db;
use app\common\model\user\User;
use app\common\model\goods\Goods;
use think\facade\Log;
use think\facade\Validate;
use think\facade\Cache;
use AlibabaCloud\SDK\Alinlp\V20200629\Alinlp;
use AlibabaCloud\SDK\Alinlp\V20200629\Models\GetWsChGeneralRequest;
use Darabonba\OpenApi\Models\Config;

/**
 * Notes: 生成随机长度字符串
 * @param $length
 * <AUTHOR> 10:36)
 * @return string|null
 * 1
 */
function getRandChar($length)
{
    $str = null;
    $strPol = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789abcdefghijklmnopqrstuvwxyz";
    $max = strlen($strPol) - 1;

    for ($i = 0;
         $i < $length;
         $i++) {
        $str .= $strPol[rand(0, $max)];
    }

    return $str;
}

/**
 * Notes: 生成密码
 * @param $plaintext
 * @param $salt
 * <AUTHOR> 15:30)
 * @return string
 */
function generatePassword($plaintext, $salt)
{
    $salt = md5('y' . $salt . 'x');
    $salt .= '2021';
    return md5($plaintext . $salt);
}


/**
 * Notes: 大写字母
 * <AUTHOR> 15:55)
 * @return array
 */
function getCapital()
{
    return  range('A','Z');
}

/**
 * 线性结构转换成树形结构
 * @param array $data 线性结构数组
 * @param string $sub_key_name 自动生成子数组名
 * @param string $id_name 数组id名
 * @param string $parent_id_name 数组祖先id名
 * @param int $parent_id 此值请勿给参数
 * @return array
 */
function linear_to_tree($data, $sub_key_name = 'sub', $id_name = 'id', $parent_id_name = 'pid', $parent_id = 0)
{
  $tree = [];
  foreach ($data as $row) {
    if ($row[$parent_id_name] == $parent_id) {
      $temp = $row;
      $temp[$sub_key_name] = linear_to_tree($data, $sub_key_name, $id_name, $parent_id_name, $row[$id_name]);
      $tree[] = $temp;
    }
  }
  return $tree;
}

/**
 * User: 意象信息科技 lr
 * Desc: 下载文件
 * @param $url 文件url
 * @param $save_dir 保存目录
 * @param $file_name 文件名
 * @return string
 */
function download_file($url, $save_dir, $file_name)
{
    if (!file_exists($save_dir)) {
        mkdir($save_dir, 0775, true);
    }
    $file_src = $save_dir . $file_name;
    file_exists($file_src) && unlink($file_src);
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 30);
//    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    $file = curl_exec($ch);
    curl_close($ch);
    $resource = fopen($file_src, 'a');
    fwrite($resource, $file);
    fclose($resource);
    if (filesize($file_src) == 0) {
        unlink($file_src);
        return '';
    }
    return $file_src;
}

/**
 * 生成会员码
 * @return 会员码
 */
function create_user_sn($prefix = '', $length = 8)
{
    $rand_str = '';
    for ($i = 0; $i < $length; $i++) {
        $rand_str .= mt_rand(0, 9);
    }
    $sn = $prefix . $rand_str;
    $user = User::where(['sn' => $sn])->findOrEmpty();
    if (!$user->isEmpty()) {
        return create_user_sn($prefix, $length);
    }
    return $sn;
}

//生成用户邀请码
function generate_invite_code()
{
    $letter_all = range('A', 'Z');
    shuffle($letter_all);
    //排除I、O字母
    $letter_array = array_diff($letter_all, ['I', 'O', 'D']);
    //排除1、0
    $num_array = range('2', '9');
    shuffle($num_array);

    $pattern = array_merge($num_array, $letter_array, $num_array);
    shuffle($pattern);
    $pattern = array_values($pattern);

    $code = '';
    for ($i = 0; $i < 6; $i++) {
        $code .= $pattern[mt_rand(0, count($pattern) - 1)];
    }

    $code = strtoupper($code);
    $check = User::where('distribution_code', $code)->findOrEmpty();
    if (!$check->isEmpty()) {
        return generate_invite_code();
    }
    return $code;
}

/**
 * User: 意象信息科技 lr
 * Desc: 数组成功拼装
 * @param string $msg
 * @param array $data
 * @param int $code
 * @param int $show
 * @return array
 */
function data_success($msg = '', $data = [], $code = 1, $show = 1)
{
    $result = [
        'code' => $code,
        'msg' => $msg,
        'data' => $data,
        'show' => $show,
    ];
    return $result;
}

/**
 * User: 意象信息科技 lr
 * Desc: 组装失败数据
 * @param string $msg
 * @param array $data
 * @param int $code
 * @param int $show
 * @return array
 */
function data_error($msg = '', $data = [], $code = 0, $show = 1)
{
    $result = [
        'code' => $code,
        'msg' => $msg,
        'data' => $data,
        'show' => $show,
    ];
    return $result;
}

/**
 * User: 意象信息科技 cjh
 * Desc: 返回是否有下一页
 * @param $count (总记录数)
 * @param $page (当前页码)
 * @param $size (每页记录数)
 * @return int
 */
function is_more($count, $page, $size)
{
    $more = 0;

    $last_page = ceil($count / $size);      //总页数、也是最后一页

    if ($last_page && $last_page > $page) {
        $more = 1;
    }
    return $more;
}

/**
 * User: 意象信息科技 lr
 * Desc：生成密码密文
 * @param $plaintext string 明文
 * @param $salt string 密码盐
 * @return string
 */
function create_password($plaintext, $salt)
{
    $salt = md5('y' . $salt . 'x');
    $salt .= '2021';
    return md5($plaintext . $salt);
}

/**
 * User: 意象信息科技 mjf
 * Desc: 用时间生成订单编号
 * @param $table
 * @param $field
 * @param string $prefix
 * @param int $rand_suffix_length
 * @param array $pool
 * @return string
 * @throws \think\db\exception\DataNotFoundException
 * @throws \think\db\exception\DbException
 * @throws \think\db\exception\ModelNotFoundException
 */
function createSn($table, $field, $prefix = '', $rand_suffix_length = 4, $pool = [])
{
    $suffix = '';
    for ($i = 0; $i < $rand_suffix_length; $i++) {
        if (empty($pool)) {
            $suffix .= rand(0, 9);
        } else {
            $suffix .= $pool[array_rand($pool)];
        }
    }
    $sn = $prefix . date('YmdHis') . $suffix;
    if (Db::name($table)->where($field, $sn)->find()) {
        return createSn($table, $field, $prefix, $rand_suffix_length, $pool);
    }
    return $sn;
}

/**
 * User: 意象信息科技 lr
 * Desc: 表单多维数据转换
 * 例：
 * 转换前：{"x":0,"a":[1,2,3],"b":[11,22,33],"c":[111,222,3333,444],"d":[1111,2222,3333]}
 * 转换为：[{"a":1,"b":11,"c":111,"d":1111},{"a":2,"b":22,"c":222,"d":2222},{"a":3,"b":33,"c":3333,"d":3333}]
 * @param $arr array 表单二维数组
 * @param $fill boolean fill为false，返回数据长度取最短，反之取最长，空值自动补充
 * @return array
 */
function form_to_linear($arr, $fill = false)
{
    $keys = [];
    $count = $fill ? 0 : PHP_INT_MAX;
    foreach ($arr as $k => $v) {
        if (is_array($v)) {
            $keys[] = $k;
            $count = $fill ? max($count, count($v)) : min($count, count($v));
        }
    }
    if (empty($keys)) {
        return [];
    }
    $data = [];
    for ($i = 0; $i < $count; $i++) {
        foreach ($keys as $v) {
            $data[$i][$v] = isset($arr[$v][$i]) ? $arr[$v][$i] : null;
        }
    }
    return $data;
}

/**
 * note 生成验证码
 * @param int $length 验证码长度
 * @return string
 */
function create_sms_code($length = 4)
{
    $code = '';
    for ($i = 0; $i < $length; $i++) {
        $code .= rand(0, 9);
    }
    return $code;
}

/**
 * 生成商品编码
 * 8位
 */
function create_goods_code($shop_id)
{
    $code =  mt_rand(10000000, 99999999);
    $goods = Goods::where([
        'code' => $code,
        'shop_id' => $shop_id,
        'del' => 0
    ])->findOrEmpty();
    if($goods->isEmpty()) {
        return $code;
    }
    create_goods_code($shop_id);
}

/**
 * 图片去除域名
 */
function clearDomain($x)
{
    if(is_array($x)) {
        $newX = [];
        foreach($x as $v) {
            $newX[] = trim(UrlServer::setFileUrl($v));
        }
        return $newX;
    }
    return  trim(UrlServer::setFileUrl($x));
}

/*
 * 生成优惠券码 排除1、0、I、O相似的数字和字母
 */
function create_coupon_code()
{
    $letter_all = range('A', 'Z');
    shuffle($letter_all);
    //排除I、O字母
    $letter_array = array_diff($letter_all, ['I', 'O']);
    //随机获取四位字母
    $letter = array_rand(array_flip($letter_array), 4);
    //排除1、0
    $num_array = range('2', '9');
    shuffle($num_array);
    //获取随机六位数字
    $num = array_rand(array_flip($num_array), 6);
    $code = implode('', array_merge($letter, $num));
    do {
        $exist_code =\app\common\model\coupon\CouponList::where(['del' => 0, 'coupon_code' => $code])->find();
    } while ($exist_code);
    return $code;
}

/**
 * 浮点数去除无效的0
 */
function clearZero($float)
{
    if($float == intval($float)) {
        return intval($float);
    }else if($float == sprintf('%.1f', $float)) {
        return sprintf('%.1f', $float);
    }
    return $float;
}

/**
 * 是否在cli模式
 */
if (!function_exists('is_cli')) {
    function is_cli()
    {
        return preg_match("/cli/i", php_sapi_name()) ? true : false;
    }
}

function real_path()
{
    if (substr(strtolower(PHP_OS), 0, 3) == 'win') {
        $ini = ini_get_all();
        $path = $ini['extension_dir']['local_value'];
        $php_path = str_replace('\\', '/', $path);
        $php_path = str_replace(array('/ext/', '/ext'), array('/', '/'), $php_path);
        $real_path = $php_path . 'php.exe';
    } else {
        $real_path = PHP_BINDIR . '/php';
    }
    if (strpos($real_path, 'ephp.exe') !== FALSE) {
        $real_path = str_replace('ephp.exe', 'php.exe', $real_path);
    }
    return $real_path;
}

/**
 * 是否为移动端
 */
function is_mobile()
{
    if (isset($_SERVER['HTTP_X_WAP_PROFILE'])) {
        return true;
    }
    if (isset($_SERVER['HTTP_VIA'])) {
        return stristr($_SERVER['HTTP_VIA'], "wap") ? true : false;
    }
    if (isset($_SERVER['HTTP_USER_AGENT'])) {
        $clientkeywords = array('nokia', 'sony', 'ericsson', 'mot', 'samsung', 'htc', 'sgh', 'lg', 'sharp', 'sie-', 'philips', 'panasonic', 'alcatel', 'lenovo', 'iphone', 'ipod', 'blackberry', 'meizu', 'android', 'netfront', 'symbian', 'ucweb', 'windowsce', 'palm', 'operamini', 'operamobi', 'openwave', 'nexusone', 'cldc', 'midp', 'wap', 'mobile');
        if (preg_match("/(" . implode('|', $clientkeywords) . ")/i", strtolower($_SERVER['HTTP_USER_AGENT']))) {
            return true;
        }
    }
    if (isset($_SERVER['HTTP_ACCEPT'])) {
        if ((strpos($_SERVER['HTTP_ACCEPT'], 'vnd.wap.wml') !== false) && (strpos($_SERVER['HTTP_ACCEPT'], 'textml') === false || (strpos($_SERVER['HTTP_ACCEPT'], 'vnd.wap.wml') < strpos($_SERVER['HTTP_ACCEPT'], 'textml')))) {
            return true;
        }
    }
    return false;
}

/**
 * Notes:判断文件是否存在（远程和本地文件）
 * @param $file string 完整的文件链接
 * @return bool
 */
function check_file_exists($file)
{
    //远程文件
    if ('http' == strtolower(substr($file, 0, 4))) {

        $header = get_headers($file, true);

        return isset($header[0]) && (strpos($header[0], '200') || strpos($header[0], '304'));

    } else {

        return file_exists($file);
    }
}

/**
 * 将图片切成圆角
 */
function rounded_corner($src_img)
{
    $w = imagesx($src_img);//微信头像宽度 正方形的
    $h = imagesy($src_img);//微信头像宽度 正方形的
    $w = min($w, $h);
    $h = $w;
    $img = imagecreatetruecolor($w, $h);
    //这一句一定要有
    imagesavealpha($img, true);
    //拾取一个完全透明的颜色,最后一个参数127为全透明
    $bg = imagecolorallocatealpha($img, 255, 255, 255, 127);
    imagefill($img, 0, 0, $bg);
    $r = $w / 2; //圆半径
//    $y_x = $r; //圆心X坐标
//    $y_y = $r; //圆心Y坐标
    for ($x = 0; $x < $w; $x++) {
        for ($y = 0; $y < $h; $y++) {
            $rgbColor = imagecolorat($src_img, $x, $y);
            if (((($x - $r) * ($x - $r) + ($y - $r) * ($y - $r)) < ($r * $r))) {
                imagesetpixel($img, $x, $y, $rgbColor);
            }
        }
    }
    unset($src_img);
    return $img;
}

/**
 * Notes:去掉名称中的表情
 * @param $str
 * @return string|string[]|null
 * @author: cjhao 2021/3/29 15:56
 */
function filterEmoji($str)
{
    $str = preg_replace_callback(
        '/./u',
        function (array $match) {
            return strlen($match[0]) >= 4 ? '' : $match[0];
        },
        $str);
    return $str;
}

/***
 * 生成海报自动适应标题
 * @param $size
 * @param int $angle
 * @param $fontfile
 * @param $string
 * @param $width
 * @param $height
 * @param $bg_height
 * @return string
 */
function auto_adapt($size, $angle = 0, $fontfile, $string, $width, $height, $bg_height)
{
    $content = "";
    // 将字符串拆分成一个个单字 保存到数组 letter 中
    for ($i = 0; $i < mb_strlen($string); $i++) {
        $letters[] = mb_substr($string, $i, 1);
    }

    foreach ($letters as $letter) {
        $str = $content . " " . $letter;
        $box = imagettfbbox($size, $angle, $fontfile, $str);

        $total_height = $box[1] + $height;
        if ($bg_height[1] - $total_height < $size) {
            break;
        }
        //右下角X位置,判断拼接后的字符串是否超过预设的宽度
        if (($box[2] > $width) && ($content !== "")) {
            if ($bg_height[1] - $total_height < $size * 2) {
                break;
            }
            $content .= "\n";
        }
        $content .= $letter;
    }
    return $content;
}

/**
 * Notes:生成一个范围内的随机浮点数
 * @param int $min 最小值
 * @param int $max 最大值
 * @return float|int 返回随机数
 */
function random_float($min = 0, $max = 1)
{
    return $min + mt_rand() / mt_getrandmax() * ($max - $min);
}


/**
 * Notes: 获取文件扩展名
 * @param $file
 * <AUTHOR> 18:03)
 * @return mixed
 */
if (!function_exists('get_extension')) {
    function get_extension($file)
    {
        return pathinfo($file, PATHINFO_EXTENSION);
    }
}


/**
 * Notes: 遍历指定目录下的文件(目标目录,排除文件)
 * @param $dir 目标文件
 * @param string $exclude_file 要排除的文件
 * @param string $target_suffix 指定后缀
 * <AUTHOR> 18:04)
 * @return array|bool
 */

if (!function_exists('get_scandir')) {
    function get_scandir($dir, $exclude_file = '', $target_suffix = '')
    {
        if (!file_exists($dir)) {
            return [];
        }

        if (empty(trim($dir))) {
            return false;
        }

        $files = scandir($dir);
        $res = [];
        foreach ($files as $item) {
            if ($item == "." || $item == ".." || $item == $exclude_file) {
                continue;
            }
            if (!empty($target_suffix)) {
                if (get_extension($item) == $target_suffix) {
                    $res[] = $item;
                }
            } else {
                $res[] = $item;
            }
        }

        if (empty($item)) {
            return false;
        }
        return $res;
    }
}



/**
 * Notes: 解压压缩包
 * @param $file 压缩包路径
 * @param $save_dir 保存路径
 * <AUTHOR> 18:11)
 * @return bool
 */
if (!function_exists('unzip')) {
    function unzip($file, $save_dir)
    {
        if (!file_exists($file)) {
            return false;
        }
        $zip = new \ZipArchive();
        if ($zip->open($file) !== TRUE) {//中文文件名要使用ANSI编码的文件格式
            return false;
        }
        $zip->extractTo($save_dir);
        $zip->close();
        return true;
    }
}


/**
 * Notes: 删除目标目录
 * @param $path
 * @param $delDir
 * <AUTHOR> 18:19)
 * @return bool
 */
if (!function_exists('del_target_dir')) {
    function del_target_dir($path, $delDir)
    {
        //没找到，不处理
        if (!file_exists($path)) {
            return false;
        }

        $handle = opendir($path);
        if ($handle) {
            while (false !== ($item = readdir($handle))) {
                if ($item != "." && $item != "..") {
                    if (is_dir("$path/$item")) {
                        del_target_dir("$path/$item", $delDir);
                    } else {
                        unlink("$path/$item");
                    }
                }
            }
            closedir($handle);
            if ($delDir) {
                return rmdir($path);
            }
        } else {
            if (file_exists($path)) {
                return unlink($path);
            }
            return false;
        }
    }
}


/**
 * Notes: 获取本地版本数据
 * @return mixed
 * <AUTHOR> 18:18)
 */
if (!function_exists('local_version')) {
    function local_version()
    {
        if(!file_exists('./upgrade/')) {
            // 若文件夹不存在，先创建文件夹
            mkdir('./upgrade/', 0777, true);
        }
        if(!file_exists('./upgrade/version.json')) {
            // 获取本地版本号
            $version = config('project.version');
            $data = ['version' => $version];
            $src = './upgrade/version.json';
            // 新建文件
            file_put_contents($src, json_encode($data, JSON_UNESCAPED_UNICODE));
        }

        $json_string = file_get_contents('./upgrade/version.json');
        // 用参数true把JSON字符串强制转成PHP数组
        $data = json_decode($json_string, true);
        return $data;
    }
}


/**
 * Notes: 获取ip
 * <AUTHOR> 10:19)
 * @return array|false|mixed|string
 */
if (!function_exists('get_client_ip')) {
    function get_client_ip()
    {
        if (!isset($_SERVER)) {
            return getenv('SERVER_ADDR');
        }

        if($_SERVER['SERVER_ADDR']) {
            return $_SERVER['SERVER_ADDR'];
        }

        return $_SERVER['LOCAL_ADDR'];
    }
}


/**
 * @notes 友好时间提示
 * @param $time
 * @return false|string
 * <AUTHOR>
 * @date 2022/5/6 9:45
 */
function friend_date($time)
{
    if (empty($time)) {
        return false;
    }

    $d = time() - intval($time);

    $ld = $time - mktime(0, 0, 0, 0, 0, date('Y'));  //年
    $md = $time - mktime(0, 0, 0, date('m'), 0, date('Y'));  //月
    $byd = $time - mktime(0, 0, 0, date('m'), date('d') - 2, date('Y'));  //前天
    $yd = $time - mktime(0, 0, 0, date('m'), date('d') - 1, date('Y'));  //昨天
    $dd = $time - mktime(0, 0, 0, date('m'), date('d'), date('Y'));  //今天
    $td = $time - mktime(0, 0, 0, date('m'), date('d') + 1, date('Y'));  //明天

    $timeDay = date('d', $time);
    $nowDay =  date('d', time());

    if (0 == $d) {
        return '刚刚';
    }

    switch ($d) {
        case $d < $td :
            $fdate = '后天' . date('H:i', $time);
            break;
        case  $d < 0 && $timeDay == $nowDay:
            $fdate = '今天' . date('H:i', $time);
            break;
        case  $d < 0:
            $fdate = '明天' . date('H:i', $time);
            break;
        case  $d < 60:
            $fdate = $d . '秒前';
            break;
        case  $d < 3600:
            $fdate = floor($d / 60) . '分钟前';
            break;
        case  $d < $dd :
            $fdate = floor($d / 3600) . '小时前';
            break;
        case  $d < $yd :
            $fdate = '昨天' . date('H:i', $time);
            break;
        case  $d < $byd :
            $fdate = '前天' . date('H:i', $time);
            break;
        case  $d < $md :
            $fdate = date('m月d日 H:i', $time);
            break;
        case  $d < $ld :
            $fdate = date('m月d日', $time);
            break;
        default :
            $fdate = date('Y年m月d日', $time);
            break;
    }
    return $fdate;
}


/**
 * @notes 生成随机数字
 * @param $table
 * @param string $field
 * @param int $length
 * @param string $prefix
 * @return string
 * <AUTHOR>
 * @date 2022/11/1 15:51
 */
function create_rand_number($table, $field = 'sn', $length = 8, $prefix = '')
{
    $rand_str = '';
    for ($i = 0; $i < $length; $i++) {
        $rand_str .= mt_rand(0, 9);
    }
    $sn = $prefix . $rand_str;
    if (Db::name($table)->where($field, $sn)->find()) {
        return create_rand_number($table, $field, $length, $prefix);
    }
    return $sn;
}


/**
 * @notes 文本超出隐藏
 * @param $string
 * @param $length
 * @return mixed|string
 * <AUTHOR>
 * @date 2022/10/31 14:14
 */
function text_out_hidden($string, $length)
{
    if (mb_strlen($string) > $length) {
        $string = mb_substr($string, 0, $length) . '..';
    }
    return $string;
}



/**
 * @notes Html中的图片绝对路径转为相对路径
 * @param $content
 * @return array|string|string[]
 * <AUTHOR>
 * @date 2022/3/28 3:27 下午
 */
function HtmlSetImage($content)
{
    $domain = UrlServer::getFileUrl('/');

    preg_match_all("/<\s*img\s+[^>]*?src\s*=\s*(\'|\")(.*?)\\1[^>]*?\/?\s*>/i",$content,$matches);
    if(!empty($matches)){
        $imgurl = $matches[2];
        foreach($imgurl as $val){
            $setVal = str_replace($domain, '', $val);
            $content = str_replace($val,$setVal,$content);
        }
    }

    return $content;
}


/**
 * @notes Html中的图片相对路径转为绝对路径
 * @param $content
 * <AUTHOR>
 * @date 2022/3/28 3:35 下午
 */
function HtmlGetImage($content)
{
    $domain = UrlServer::getFileUrl('/');

    preg_match_all("/<\s*img\s+[^>]*?src\s*=\s*(\'|\")(.*?)\\1[^>]*?\/?\s*>/i",$content,$matches);
    if(!empty($matches)){
        $imgurl = $matches[2];
        foreach($imgurl as $val){
            if (strstr($val, 'http://'))  continue;
            if (strstr($val, 'https://')) continue;
            $content = str_replace($val,$domain.$val,$content);
        }
    }

    return $content;
}

function check_is_image($image) : bool
{

    try {
        if (function_exists('exif_imagetype')) {
            $ImageType =  exif_imagetype($image);
        } else {
            $info = getimagesize($image);
            $ImageType = $info ? $info[2] : false;
        }
    } catch (\Exception $e) {
        return false;
    }

    return in_array($ImageType, [1, 2, 3, 6]);
}

function check_is_video($video) : bool
{
    $type = mime_content_type($video);

    return strpos($type, 'video') !== false;
}

function check_is_audio($audio) : bool
{
    // 检查文件是否存在
    if (!file_exists($audio)) {
        return false;
    }

    // 获取文件扩展名
    $ext = strtolower(pathinfo($audio, PATHINFO_EXTENSION));
    
    // 允许的音频扩展名
    $allowedAudioExts = ['mp3', 'wav', 'aac', 'flac', 'amr', 'm4a', 'wma', 'ogg', 'ape', 'mid', 'midi'];

    // 首先通过扩展名检查
    if (in_array($ext, $allowedAudioExts)) {
        return true;
    }

    // 如果扩展名检查失败，尝试使用 mime_content_type
    try {
        $type = mime_content_type($audio);
        if ($type && strpos($type, 'audio') !== false) {
            return true;
        }
    } catch (\Exception $e) {
        // mime_content_type 失败，继续其他检查
    }

    // 如果 mime_content_type 不可用或失败，尝试使用 finfo
    if (function_exists('finfo_open')) {
        try {
            $finfo = finfo_open(FILEINFO_MIME_TYPE);
            $type = finfo_file($finfo, $audio);
            finfo_close($finfo);

            if ($type && strpos($type, 'audio') !== false) {
                return true;
            }
        } catch (\Exception $e) {
            // finfo 也失败了
        }
    }

    return false;
}


//处理昵称
function replaceNickname($nickname)
{
    //过滤emoji 特殊符号
    $str = preg_replace_callback( '/./u',
        function (array $match) {
            if (Validate::isChs($match[0])) {
                return $match[0];
            }

            return strlen($match[0]) >= 4 ? '' : (ctype_graph($match[0]) ? $match[0] : '');
        },
        $nickname);

    return str_replace(' ', '', trim($str));
}


/**
 * 获取百度API访问令牌
 * @param bool $force_refresh 是否强制刷新令牌
 * @return array 包含访问令牌的结果数组
 */
function word_filter_access_token($force_refresh = false){
    try {
        // 从数据库获取百度API配置
        $baidupai = Db::name('config')->where('type', 'baiduapi')->column('value', 'name');

        // 检查必要的配置是否存在
        if (empty($baidupai['appid']) || empty($baidupai['secret'])) {
            \think\facade\Log::error('百度API配置缺失: appid或secret未设置');
            return ds_callback(false, '百度API配置缺失');
        }

        $appid = $baidupai['appid'];
        $secret = $baidupai['secret'];
        $access_token = isset($baidupai['word_filter_access_token']) ? $baidupai['word_filter_access_token'] : '';
        $times = time();
        $access_token_expire = isset($baidupai['word_filter_access_token_expire']) ? $baidupai['word_filter_access_token_expire'] : 0;

        // 如果令牌不存在、已过期或强制刷新
        if ($force_refresh || !$access_token || $access_token_expire < $times) {
            // 设置请求超时(5秒)

            // 请求新的访问令牌
            $res = http_request(
                'https://aip.baidubce.com/oauth/2.0/token',
                'POST',
                [
                    'grant_type' => 'client_credentials',
                    'client_id' => $appid,
                    'client_secret' => $secret,
                ],
                [],
                false,
                5 // 5秒超时
            );

            $res = json_decode($res, true);

            // 检查API返回错误
            if (isset($res['error'])) {
                \think\facade\Log::error('获取百度API令牌失败: ' . ($res['error_description'] ?? '未知错误'));
                return ds_callback(false, $res['error_description'] ?? '获取访问令牌失败');
            }

            // 检查返回的令牌是否有效
            if (empty($res['access_token']) || empty($res['expires_in'])) {
                \think\facade\Log::error('百度API返回的令牌无效: ' . json_encode($res));
                return ds_callback(false, '获取到的访问令牌无效');
            }

            $access_token = $res['access_token'];
            $expires_in = $res['expires_in'];

            // 更新数据库中的令牌和过期时间
            Db::name('config')->where('name', 'word_filter_access_token')->update(['value' => $access_token]);
            Db::name('config')->where('name', 'word_filter_access_token_expire')->update(['value' => $times + $expires_in]);

            // 记录令牌刷新日志
            \think\facade\Log::info('百度API令牌已刷新，有效期: ' . date('Y-m-d H:i:s', $times + $expires_in));
        }

        return ds_callback(true, '', $access_token);

    } catch (\Exception $e) {
        // 记录异常日志
        \think\facade\Log::error('获取百度API令牌异常: ' . $e->getMessage());
        return ds_callback(false, '获取访问令牌时发生异常: ' . $e->getMessage());
    }
}
/**
 * 敏感词过滤
 * @param string $text 需要过滤的文本
 * @return array 过滤结果
 */
function word_filter($text) {
    // 初始化返回数据
    $data = [
        'text' => $text,
        'if_sensitive' => false,
        'sensitive_msg' => [],
        'sensitive_word' => []
    ];

    // 本地敏感词过滤 - 添加常见敏感词列表
    $local_sensitive_words = [
        '赌博', '博彩', '色情', '暴力', '恐怖', '毒品', '政治', '反动', '法轮功',
        '黄色', '黄片', '成人', '情色', '裸聊', '约炮', '一夜情', '嫖娼', '卖淫',
        '吸毒', '贩毒', '枪支', '弹药', '爆炸物', '炸弹', '自杀', '自残',
        '赌场', '博彩', '六合彩', '私彩', '时时彩', '北京赛车', '幸运飞艇',
        '伟哥', '催情', '春药', '壮阳', '避孕', '代孕', '流产', '堕胎'
    ];

    // 本地敏感词检测
    foreach ($local_sensitive_words as $word) {
        if (mb_stripos($text, $word) !== false) {
            $data['if_sensitive'] = true;
            $data['sensitive_msg'][] = "包含敏感词: {$word}";
            $data['sensitive_word'][] = $word;
            $data['text'] = str_replace($word, '**', $data['text']);
        }
    }

    // 如果本地检测已发现敏感内容，直接返回结果
    if ($data['if_sensitive']) {
        $data['sensitive_word'] = implode(',', $data['sensitive_msg']);
        return $data;
    }

    // 获取百度API访问令牌
    $res = word_filter_access_token();
    if (!$res['code']) {
        return $res;
    }

    $access_token = $res['data'];

    // 设置重试次数
    $max_retries = 2; // 最多重试2次
    $retry_count = 0;

    while ($retry_count <= $max_retries) {
        try {
            // 调用百度API进行内容审核
            $res = http_request(
                'https://aip.baidubce.com/rest/2.0/solution/v1/text_censor/v2/user_defined?access_token='.$access_token,
                'POST',
                ['text' => $text],
                ['Content-Type: application/x-www-form-urlencoded'],
                false,
                5 // 5秒超时
            );

            $res = json_decode($res, true);

            // 检查API返回错误
            if (isset($res['error_code'])) {
                // 如果是令牌过期错误，尝试刷新令牌
                if ($res['error_code'] == 110 || $res['error_code'] == 111) {
                    $token_res = word_filter_access_token(true); // 强制刷新令牌
                    if ($token_res['code']) {
                        $access_token = $token_res['data'];
                        $retry_count++;
                        continue;
                    }
                }
                return ds_callback(false, $res['error_msg']);
            }

            // 处理审核结果
            if (isset($res['conclusionType']) && $res['conclusionType'] == 2) {
                $data['if_sensitive'] = true;

                if (isset($res['data']) && is_array($res['data'])) {
                    foreach ($res['data'] as $val) {
                        if (isset($val['msg'])) {
                            $data['sensitive_msg'][] = $val['msg'];
                        }

                        if (isset($val['hits']) && is_array($val['hits'])) {
                            foreach ($val['hits'] as $v) {
                                if (isset($v['words']) && is_array($v['words'])) {
                                    $data['sensitive_word'] = array_merge($data['sensitive_word'], $v['words']);

                                    // 替换敏感词为**
                                    foreach ($v['words'] as $word) {
                                        $data['text'] = str_replace($word, '**', $data['text']);
                                    }
                                }
                            }
                        }
                    }
                }

                if (!empty($data['sensitive_msg'])) {
                    $data['sensitive_word'] = implode(',', $data['sensitive_msg']);
                }
            }

            // 成功处理，跳出循环
            break;

        } catch (\Exception $e) {
            // 记录错误日志
            \think\facade\Log::error('敏感词过滤API调用失败: ' . $e->getMessage());
            $retry_count++;

            // 最后一次重试失败，返回本地过滤结果
            if ($retry_count > $max_retries) {
                return $data;
            }

            // 等待一段时间后重试
            usleep(500000); // 500毫秒
        }
    }

    return $data;
}

/**
 * CURL请求
 * @param string $url 请求url地址
 * @param string $method 请求方法 get post
 * @param null|array $postfields post数据数组
 * @param array $headers 请求header信息
 * @param bool $debug 调试开启 默认false
 * @param int $timeout 请求超时时间(秒)，0表示不限制
 * @return mixed
 */
function http_request($url, $method = "GET", $postfields = null, $headers = array(), $debug = false, $timeout = 0) {
    $method = strtoupper($method);
    $ci = curl_init();
    /* Curl settings */
    curl_setopt($ci, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
    curl_setopt($ci, CURLOPT_CONNECTTIMEOUT, $timeout > 0 ? $timeout : 30); /* 连接超时时间 */
    curl_setopt($ci, CURLOPT_TIMEOUT, $timeout > 0 ? $timeout : 30); /* 请求超时时间 */
    curl_setopt($ci, CURLOPT_RETURNTRANSFER, true);

    switch ($method) {
        case "POST":
            curl_setopt($ci, CURLOPT_POST, true);
            if (!empty($postfields)) {
                $tmpdatastr = is_array($postfields) ? http_build_query($postfields) : $postfields;
                curl_setopt($ci, CURLOPT_POSTFIELDS, $tmpdatastr);
            }
            break;
        default:
            curl_setopt($ci, CURLOPT_CUSTOMREQUEST, $method); /* //设置请求方式 */
            break;
    }
    $ssl = preg_match('/^https:\/\//i', $url) ? TRUE : FALSE;
    curl_setopt($ci, CURLOPT_URL, $url);
    if ($ssl) {
        curl_setopt($ci, CURLOPT_SSL_VERIFYPEER, FALSE); // https请求 不验证证书和hosts
        curl_setopt($ci, CURLOPT_SSL_VERIFYHOST, FALSE); // 不从证书中检查SSL加密算法是否存在
    }
    //curl_setopt($ci, CURLOPT_HEADER, true); /*启用时会将头文件的信息作为数据流输出*/
    curl_setopt($ci, CURLOPT_FOLLOWLOCATION, 1);
    curl_setopt($ci, CURLOPT_MAXREDIRS, 2); /* 指定最多的HTTP重定向的数量，这个选项是和CURLOPT_FOLLOWLOCATION一起使用的 */
    curl_setopt($ci, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ci, CURLINFO_HEADER_OUT, true);
    /* curl_setopt($ci, CURLOPT_COOKIE, $Cookiestr); * *COOKIE带过去** */
    $response = curl_exec($ci);

    // 检查是否有错误发生
    if ($response === false) {
        $error = curl_error($ci);
        $errno = curl_errno($ci);
        \think\facade\Log::error("CURL请求错误: [{$errno}] {$error}, URL: {$url}");
    }

    if ($debug) {
        $requestinfo = curl_getinfo($ci);
        $http_code = curl_getinfo($ci, CURLINFO_HTTP_CODE);

        echo "=====post data======\r\n";
        var_dump($postfields);
        echo "=====info===== \r\n";
        print_r($requestinfo);
        echo "=====http code=====\r\n";
        print_r($http_code);
        echo "=====response=====\r\n";
        print_r($response);
    }
    curl_close($ci);
    return $response;
}

/**
 * 敏感图过滤
 * @param type $text
 * @return boolean
 */
function image_filter($img_url) {
    $data=array();
    $data['if_sensitive']=false;

    $res=word_filter_access_token();

    if(!$res['code']){
        return $res;
    }
    $access_token=$res['data'];
    $image=imgToBase64($img_url);
    if(empty($image)){
        return ds_callback(false, 'image empty');
    }
    $res=http_request('https://aip.baidubce.com/rest/2.0/solution/v1/img_censor/v2/user_defined?access_token='.$access_token,'POST',array(
        'image'=> $image['content']
    ),array(
        'Content-Type: application/x-www-form-urlencoded'
    ));
    $res = json_decode($res, true);
    if(isset($res['error_code'])){
        return ds_callback(false, $res['error_msg']);
    }

    if($res['conclusionType']==2){
        $data['if_sensitive']=true;
        $data['sensitive_msg']=array();
        foreach($res['data'] as $val){
            $data['sensitive_msg'][]=$val['msg'];
        }
    }

    return ds_callback(true, '', $data);
}
/**
 * 规范数据返回函数
 * @param unknown $code
 * @param unknown $msg
 * @param unknown $data
 * @return multitype:unknown
 */
function ds_callback($code, $msg = '', $data = array()) {
    return array('code' => $code, 'msg' => $msg, 'data' => $data);
}

/**
 * 获取图片的Base64编码(不支持url)
 *
 * @param $img_file 传入本地图片地址
 *
 * @return string
 */
function imgToBase64($img_file) {
    $data=array();
    $img_base64 = '';
    if (file_exists($img_file)) {
        $app_img_file = $img_file; // 图片路径
        $img_info = getimagesize($app_img_file); // 取得图片的大小，类型等

        //echo '<pre>' . print_r($img_info, true) . '</pre><br>';
        $fp = fopen($app_img_file, "r"); // 图片是否可读权限

        if ($fp) {
            $filesize = filesize($app_img_file);
            $content = fread($fp, $filesize);
            $file_content = base64_encode($content); // base64编码
            switch ($img_info[2]) {           //判读图片类型
                case 1: $img_type = "gif";
                    break;
                case 2: $img_type = "jpg";
                    break;
                case 3: $img_type = "png";
                    break;
            }

            $img_base64 = 'data:image/' . $img_type . ';base64,' . $file_content;//合成图片的base64编码

        }
        fclose($fp);
        $data=array(
            'type'=>$img_type,
            'content'=>$file_content,
            'result'=>$img_base64
        );
    }


    return $data; //返回图片的base64
}
/**
 * 敏感视频过滤
 * @param type $text
 * @return boolean
 */
function video_filter($video_url,$id=0) {
    $data=array();
    $data['if_sensitive']=false;
    $res=word_filter_access_token();

    if(!$res['code']){
        return $res;
    }
    $access_token=$res['data'];

    $res=http_request('https://aip.baidubce.com/rest/2.0/solution/v1/video_censor/v2/user_defined?access_token='.$access_token,'POST',array(
        'videoUrl'=> $video_url,
        'extId'=>$id,
        'name'=> 'test'
    ),array(
        'Content-Type: application/x-www-form-urlencoded',
        'Accept: application/json'
    ));

    if(!$res){
        return $res;
    }
    $res = json_decode($res, true);

    if($res['conclusion']=='不合规'){
        return ds_callback(1101, $res['conclusionTypeGroupInfos'][0]['msg']);
    }
    if($res['conclusionType']==2){
        $data['if_sensitive']=true;
        $data['sensitive_msg']=array();
        foreach($res['data'] as $val){
            $data['sensitive_msg'][]=$val['msg'];
        }
    }
    return ds_callback(true, '', $data);
}
function search($keyword,$offset=0,$limit=20)
{
    $addwhere['q'] = $keyword;
    $addwhere['offset'] = $offset;
    $addwhere['limit'] = $limit;
    $addwhere['attributesToHighlight'] = ['title','description'];
    $result =  curlPost('http://127.0.0.1:7700/indexes/movies/search',json_encode($addwhere,JSON_UNESCAPED_UNICODE));
    return json_decode($result,true);
}


function curlPost($url = '', $postData = '', $options = array())
{
    if (is_array($postData)) {
        $postData = http_build_query($postData);
    }
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30); //设置cURL允许执行的最长秒数
    if (!empty($options)) {
        curl_setopt_array($ch, $options);
    }
    //https请求 不验证证书和host
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    $data = curl_exec($ch);
    curl_close($ch);
    return $data;
}

function findTopSponsor($userId) {
    while (true) {
        // 查询当前用户的上级ID
        $result=Db::name('agent_relationships')->where(['user_id'=>['=',$userId]])->find();

        if ($result) {

            $sponsorId = $result['sponsor_id'];
            $agent=Db::name('agent')->where(['user_id'=>['=',$sponsorId]])->value('user_id');
            // 检查上级是否是代理
            if ($agent) {
                return $agent; // 返回代理ID
            } else {
                $userId = $sponsorId; // 继续查找上一级的上级
            }
        } else {
            return 0; // 没有上级，返回0
        }
    }
}


function segment($text)
{
    // 检查缓存中是否已有分词结果
    $cacheKey = 'search_segment:' . md5($text);
    if (Cache::has($cacheKey)) {
        return Cache::get($cacheKey);
    }

    $res=word_filter_access_token();

    if(!$res['code']){
        return $res;
    }
    $access_token=$res['data'];
    $url = "https://aip.baidubce.com/rpc/2.0/nlp/v1/txt_keywords_extraction?access_token={$access_token}";
    $curl = curl_init();
    $data = array(
        'text' => [$text]
    );
//    var_dump(json_encode($data));die;
    curl_setopt_array($curl, array(
        CURLOPT_URL => $url,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_SSL_VERIFYPEER  => false,
        CURLOPT_SSL_VERIFYHOST  => false,
        CURLOPT_CUSTOMREQUEST => 'POST',
        CURLOPT_POSTFIELDS => json_encode($data),
        CURLOPT_HTTPHEADER => array(
            'Content-Type: application/x-www-form-urlencoded'
        ),

    ));
    $response = curl_exec($curl);
    curl_close($curl);
    // 解析 API 返回结果
    $result = json_decode($response, true);

    if (isset($result['error_code'])) {
        return 0;
    }
    // 提取分词结果中的关键词
    $words = array_column($result['results'], 'word');

    // 缓存分词结果（缓存 1 小时）
    Cache::set($cacheKey, $words);
    return $words;
}


/*
 * 阿里云分词
 */
function Alisegment($text){
    // 检查缓存中是否已有该关键词的分词结果
    $cacheKey = 'alisegment_' . md5($text);
    $cachedResult = Cache::get($cacheKey);
    if ($cachedResult) {
        return $cachedResult;
    }

    // 如果关键词为空，直接返回空数组
    if (empty($text)) {
        return ['words' => []];
    }

    try {
        // 禁用SSL验证（注意：生产环境中应该使用正确的SSL证书而不是禁用验证）
        $originalVerifyPeer = ini_get('curl.cainfo');
        $originalVerifyHost = ini_get('curl.capath');

        // 临时禁用SSL验证
        ini_set('curl.cainfo', '');
        ini_set('curl.capath', '');

        // 检查必要的类是否存在
        if (!class_exists('AlibabaCloud\SDK\Alinlp\V20200629\Alinlp') || !class_exists('Darabonba\OpenApi\Models\Config')) {
            throw new \Exception('阿里云NLP SDK未安装或类不存在');
        }

        // 配置阿里云NLP服务
        $config = new Config([
            'accessKeyId' => 'LTAI5tPpqw9JoKAZKrY4k14Q',
            'accessKeySecret' => '******************************',
            'regionId' => 'cn-hangzhou'
        ]);

        $client = new Alinlp($config);
        $request = new GetWsChGeneralRequest();
        $request->serviceCode = 'alinlp';
        $request->text = $text;
        $word = [];
        // 调用阿里云NLP服务进行分词
        $response = $client->GetWsChGeneral($request);
        $json_string = json_encode($response->body, JSON_UNESCAPED_UNICODE);
        $data = json_decode($json_string, true);
        $keywords = json_decode($data['data'], true);

        // 处理分词结果
        if (is_array($keywords) && isset($keywords['result'])) {
            foreach ($keywords['result'] as $val) {
                if (!in_array($val['word'], $word)) {
                    $word[] = $val['word'];
                }
            }
        }

        $result = ['words' => $word];

        // 缓存分词结果（1小时）
        Cache::set($cacheKey, $result, 3600);

        // 恢复原始SSL设置
        ini_set('curl.cainfo', $originalVerifyPeer);
        ini_set('curl.capath', $originalVerifyHost);

        return $result;
    } catch (\Exception $e) {
        // 记录错误日志
        \think\facade\Log::error('阿里云分词API调用失败: ' . $e->getMessage());

        // 恢复原始SSL设置
        ini_set('curl.cainfo', $originalVerifyPeer);
        ini_set('curl.capath', $originalVerifyHost);

        // 如果分词失败，尝试简单的字符分割
        $simpleWords = [];
        $len = mb_strlen($text, 'UTF-8');
        for ($i = 0; $i < $len; $i++) {
            $char = mb_substr($text, $i, 1, 'UTF-8');
            if (preg_match('/[\x{4e00}-\x{9fa5}]/u', $char)) { // 仅处理中文字符
                $simpleWords[] = $char;
            }
        }

        // 如果有空格，也按空格分割
        $spaceWords = preg_split('/\s+/', $text);
        if (count($spaceWords) > 1) {
            $simpleWords = array_merge($simpleWords, $spaceWords);
        }

        $result = ['words' => array_unique(array_filter($simpleWords))];

        // 缓存分词结果（1小时）
        Cache::set($cacheKey, $result, 3600);

        return $result;
    } catch (\Exception $outerE) {
        // 如果整个过程失败，记录错误并返回简单分词结果
        \think\facade\Log::error('Alisegment函数执行失败: ' . $outerE->getMessage());

        // 恢复原始SSL设置
        if (isset($originalVerifyPeer)) {
            ini_set('curl.cainfo', $originalVerifyPeer);
        }
        if (isset($originalVerifyHost)) {
            ini_set('curl.capath', $originalVerifyHost);
        }

        // 返回简单的分词结果
        $simpleWords = preg_split('/\s+/', $text, -1, PREG_SPLIT_NO_EMPTY);
        if (empty($simpleWords)) {
            $simpleWords = [$text];
        }

        $result = ['words' => $simpleWords];

        // 缓存结果（10分钟，避免频繁重试失败的API）
        Cache::set($cacheKey, $result, 600);

        return $result;
    }
}

/*
 * 命名实体识别-电商
 */
function AlinlpNerEcom($text){
    // 检查缓存中是否已有该关键词的分词结果
    $cacheKey = 'alinlp_ner_ecom_' . md5($text);
    $cachedResult = Cache::get($cacheKey);
    if ($cachedResult) {
        return $cachedResult;
    }

    // 如果关键词为空，直接返回空数组
    if (empty($text)) {
        return ['words' => []];
    }

    // 禁用SSL验证（注意：生产环境中应该使用正确的SSL证书而不是禁用验证）
    $originalVerifyPeer = ini_get('curl.cainfo');
    $originalVerifyHost = ini_get('curl.capath');

    // 临时禁用SSL验证
    ini_set('curl.cainfo', '');
    ini_set('curl.capath', '');

    // 配置阿里云NLP服务
    $config = new Config([
        'accessKeyId' => 'LTAI5tPpqw9JoKAZKrY4k14Q',
        'accessKeySecret' => '******************************',
        'regionId' => 'cn-hangzhou'
    ]);

    $client = new Alinlp($config);
    $request = new GetWsChGeneralRequest();
    $request->serviceCode = 'alinlp';
    $request->text = $text;
    $word = [];

    try {
        // 调用阿里云NLP服务进行分词
        $response = $client->getNerChEcom($request);
        $json_string = json_encode($response->body, JSON_UNESCAPED_UNICODE);
        $data = json_decode($json_string, true);
        $keywords = json_decode($data['data'], true);

        // 处理分词结果
        if (is_array($keywords) && isset($keywords['result'])) {
            foreach ($keywords['result'] as $val) {
                if (!in_array($val['word'], $word)) {
                    $word[] = $val['word'];
                }
            }
        }

        $result = ['words' => $word];

        // 缓存分词结果（1小时）
        Cache::set($cacheKey, $result, 3600);

        // 恢复原始SSL设置
        ini_set('curl.cainfo', $originalVerifyPeer);
        ini_set('curl.capath', $originalVerifyHost);

        return $result;
    } catch (\Exception $e) {
        // 记录错误日志
        \think\facade\Log::error('阿里云命名实体识别API调用失败: ' . $e->getMessage());

        // 恢复原始SSL设置
        ini_set('curl.cainfo', $originalVerifyPeer);
        ini_set('curl.capath', $originalVerifyHost);

        // 如果API调用失败，返回空结果
        $result = ['words' => []];

        // 缓存空结果（10分钟，避免频繁重试失败的API）
        Cache::set($cacheKey, $result, 600);

        return $result;
    }
}

//生成随机中文昵称
function create_nickname(){
    $surnames = ["赵", "钱", "孙", "李", "周", "吴", "郑", "王", "冯", "陈", "褚", "卫", "蒋", "沈", "韩", "杨", "朱", "秦", "尤", "许", "何", "吕", "施", "张", "孔", "曹", "严", "华", "金", "魏", "陶", "姜"];
    $givennames = ["伟", "芳", "娜", "秀英", "敏", "静", "丽", "强", "磊", "军", "洋", "勇", "艳", "杰", "娟", "涛", "明", "超", "秀兰", "霞", "平", "刚", "桂英", "丹", "文", "芳", "海", "燕", "波", "宁", "瑞", "雪", "梅", "琳", "云", "飞", "祥", "宇", "琪", "萌", "晨", "曦", "阳", "光", "月", "星", "辰"];

    $surname = $surnames[array_rand($surnames)];
    $name_length = rand(1, 2);
    $givenname = '';
    for ($i = 0; $i < $name_length; $i++) {
        $givenname .= $givennames[array_rand($givennames)];
    }

    return $surname . $givenname;
}
/**
 * @notes 生成完整的url
 * @param $url
 * @return string
 */
function create_full_url($url)
{
    if (empty($url)) {
        return '';
    }
    if (strstr($url, 'http://') || strstr($url, 'https://')) {
        return $url;
    }
    return UrlServer::getFileUrl($url);
}

