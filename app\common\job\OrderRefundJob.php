<?php

namespace app\common\job;

use think\queue\Job;
use think\facade\Log;
use app\common\model\order\Order;
use app\common\enum\PayEnum;
use app\common\logic\OrderRefundLogic;

/**
 * 订单退款队列任务
 * Class OrderRefundJob
 * @package app\common\job
 */
class OrderRefundJob
{
    /**
     * 执行订单退款任务
     * @param Job $job 队列任务对象
     * @param array $data 任务数据 ['order_id' => 订单ID, 'user_id' => 用户ID, 'refund_amount' => 退款金额]
     */
    public function fire(Job $job, $data)
    {
        // 记录任务开始执行
        Log::info('OrderRefundJob 任务开始执行', [
            'job_id' => $job->getJobId(),
            'attempts' => $job->attempts(),
            'data' => $data
        ]);
        
        try {
            // 验证数据
            if (empty($data['order_id']) || empty($data['user_id']) || !isset($data['refund_amount'])) {
                Log::error('OrderRefundJob 参数错误: ' . json_encode($data, JSON_UNESCAPED_UNICODE));
                $job->delete(); // 参数错误直接删除任务
                return;
            }

            $order_id = $data['order_id'];
            $user_id = $data['user_id'];
            $refund_amount = $data['refund_amount'];

            Log::info("OrderRefundJob 开始处理订单退款: order_id={$order_id}, user_id={$user_id}, refund_amount={$refund_amount}, attempts={$job->attempts()}");

            // 获取订单信息
            $order = Order::where('id', $order_id)->find();
            if (!$order) {
                Log::error("OrderRefundJob 订单不存在: order_id={$order_id}");
                $job->delete(); // 订单不存在，删除任务
                return;
            }

            // 检查订单是否已支付
            if ($order['pay_status'] != PayEnum::ISPAID) {
                Log::info("OrderRefundJob 订单未支付，无需退款: order_id={$order_id}");
                $job->delete(); // 未支付订单，删除任务
                return;
            }

            // 执行退款
            $result = self::processRefund($order, $refund_amount);

            if ($result['success']) {
                Log::info("OrderRefundJob 退款成功: order_id={$order_id}");
                $job->delete(); // 退款成功，删除任务
            } else {
                Log::error("OrderRefundJob 退款失败: order_id={$order_id}, error={$result['message']}");
                
                // 检查重试次数
                if ($job->attempts() >= 3) {
                    Log::error("OrderRefundJob 达到最大重试次数，任务失败: order_id={$order_id}");
                    
                    // 记录退款失败
                    OrderRefundLogic::addErrorRefund($order->toArray(), $result['message']);
                    
                    $job->delete(); // 达到最大重试次数，删除任务
                } else {
                    $job->release(60); // 延迟60秒后重试
                }
            }

        } catch (\Exception $e) {
            Log::error('OrderRefundJob 执行异常: ' . $e->getMessage() . ', 数据: ' . json_encode($data, JSON_UNESCAPED_UNICODE));
            
            // 检查重试次数
            if ($job->attempts() >= 3) {
                Log::error("OrderRefundJob 异常达到最大重试次数: " . $e->getMessage());
                
                // 记录退款失败
                if (!empty($order)) {
                    OrderRefundLogic::addErrorRefund($order->toArray(), $e->getMessage());
                }
                
                $job->delete(); // 达到最大重试次数，删除任务
            } else {
                $job->release(60); // 延迟60秒后重试
            }
        }
    }

    /**
     * 任务失败处理
     * @param array $data 任务数据
     */
    public function failed($data)
    {
        Log::error('OrderRefundJob 最终执行失败: ' . json_encode($data, JSON_UNESCAPED_UNICODE));
    }

    /**
     * 处理退款
     * @param Order $order 订单对象
     * @param float $refund_amount 退款金额
     * @return array
     */
    private static function processRefund($order, $refund_amount)
    {
        try {
            // 更新订单退款状态
            OrderRefundLogic::cancelOrderRefundUpdate($order->toArray());
            
            // 执行退款
            OrderRefundLogic::refund($order->toArray(), $order['order_amount'], $refund_amount);
            
            return ['success' => true, 'message' => '退款成功'];
            
        } catch (\Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
}
