<?php
header('Content-Type: text/event-stream');
header('Cache-Control: no-cache');
header('Connection: keep-alive');

// 禁用缓存
ob_end_clean();
ob_implicit_flush(1);

function sendSSE($data, $event = null, $id = null) {
    if ($id) {
        echo "id: $id\n";
    }
    if ($event) {
        echo "event: $event\n";
    }
    echo "data: " . json_encode($data) . "\n\n";
    flush();
}

// 模拟实时数据推送
$counter = 0;
while (true) {
    $counter++;
    
    // 发送消息
    sendSSE([
        'time' => date('Y-m-d H:i:s'),
        'count' => $counter,
        'message' => '这是一条实时消息'
    ], 'message', $counter);
    
    // 每2秒发送一条消息
    sleep(2);
    
    // 发送10条消息后结束
    if ($counter >= 10) {
        break;
    }
}
