{layout name="layout1" /}
<div class="layui-form">
    <input type="hidden" name="goods_id" value="{$goods_id}" />
    <div class="layui-form-item" style="margin-top: 15px;">
        <label class="layui-form-label">顶部标签</label>
        <div class="layui-input-block" style="width: 380px;">
            <div id="goodsLabelTop"></div>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label"></label>
        <span style="color: #a3a3a3;font-size: 9px">顶部标签(集采众筹,拼单集采,新品上新,行家严选)，可多选</span>
    </div>
    <div class="layui-form-item" style="margin-top: 15px;">
        <label class="layui-form-label">普通标签</label>
        <div class="layui-input-block" style="width: 380px;">
            <div id="goodsLabel"></div>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label"></label>
        <span style="color: #a3a3a3;font-size: 9px">普通标签(年度榜,收藏榜,推荐榜)，可多选</span>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">虚拟销量</label>
        <div class="layui-input-block" style="width: 380px;">
            <input type="number" name="sales_virtual" value="{$goods_detail.sales_virtual}" min="0" class="layui-input" />
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">虚拟浏览量</label>
        <div class="layui-input-block" style="width: 380px;">
            <input type="number" name="clicks_virtual" value="{$goods_detail.clicks_virtual}" min="0" class="layui-input" />
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">排序权重</label>
        <div class="layui-input-block" style="width: 380px;">
            <input type="number" name="sort_weight" value="{$goods_detail.sort_weight}" min="0" class="layui-input" />
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label"></label>
        <span style="color: #a3a3a3;font-size: 9px">商品排序权重，数字越小排序越前，权重越大</span>
    </div>
    <div class="layui-form-item layui-hide">
        <input type="button" lay-submit lay-filter="setinfo-submit" id="setinfo-submit" value="确认">
    </div>
</div>
<script>
    layui.config({
        base: '/static/lib/'
    }).extend({
        xmSelect: 'xmSelect/xm-select'
    }).use(['xmSelect', 'jquery'], function (){
        var xmSelect = layui.xmSelect;
        var $ = layui.jquery;
        var column_list = '{$column_list|raw}';
        var goodsLabelTopValue = '{$goods_detail.goods_label_top | raw}';
        var goodsLabelValue = '{$goods_detail.goods_label | raw}';

        // 顶部标签选择器
        var xmInsTop = xmSelect.render({
            el: '#goodsLabelTop',
            language: 'zn',
            data: JSON.parse(column_list),
            prop: {
                value: 'id'
            },
            name: 'goods_label_top',
            initValue: JSON.parse(goodsLabelTopValue)
        });

        // 普通标签选择器
        var xmInsLabel = xmSelect.render({
            el: '#goodsLabel',
            language: 'zn',
            data: JSON.parse(column_list),
            prop: {
                value: 'id'
            },
            name: 'goods_label',
            initValue: JSON.parse(goodsLabelValue)
        });
    });
</script>