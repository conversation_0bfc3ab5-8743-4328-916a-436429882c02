{"version": 3, "file": "pages/user/collection.js", "sources": ["webpack:///./components/null-data.vue?48f8", "webpack:///./components/null-data.vue?97fe", "webpack:///./components/null-data.vue?fba4", "webpack:///./components/null-data.vue?cbf9", "webpack:///./components/null-data.vue", "webpack:///./components/null-data.vue?da63", "webpack:///./components/null-data.vue?475d", "webpack:///./pages/user/collection.vue?2e5f", "webpack:///./static/images/profit_null.png", "webpack:///./pages/user/collection.vue?23e2", "webpack:///./pages/user/collection.vue?71ef", "webpack:///./pages/user/collection.vue?2621", "webpack:///./pages/user/collection.vue", "webpack:///./pages/user/collection.vue?686b", "webpack:///./pages/user/collection.vue?c7b6"], "sourcesContent": ["// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/sass-resources-loader/lib/loader.js??ref--7-oneOf-1-4!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./null-data.vue?vue&type=style&index=0&id=93598fb0&lang=scss&scoped=true&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"12a18d22\", content, true, context)\n};", "export * from \"-!../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/sass-resources-loader/lib/loader.js??ref--7-oneOf-1-4!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./null-data.vue?vue&type=style&index=0&id=93598fb0&lang=scss&scoped=true&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".null-data[data-v-93598fb0]{padding:100px}.null-data .img-null[data-v-93598fb0]{width:150px;height:150px}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"bg-white flex-col col-center null-data\"},[_vm._ssrNode(\"<img\"+(_vm._ssrAttr(\"src\",_vm.img))+\" alt class=\\\"img-null\\\"\"+(_vm._ssrStyle(null,_vm.imgStyle, null))+\" data-v-93598fb0> <div class=\\\"muted mt8\\\" data-v-93598fb0>\"+_vm._ssrEscape(_vm._s(_vm.text))+\"</div>\")])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n\nexport default {\n    components: {},\n    props: {\n        img: {\n            type: String,\n        },\n        text: {\n            type: String,\n            default: '暂无数据',\n        },\n        imgStyle: {\n            type: String,\n            default: '',\n        },\n    },\n    methods: {},\n}\n", "import mod from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./null-data.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./null-data.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./null-data.vue?vue&type=template&id=93598fb0&scoped=true&\"\nimport script from \"./null-data.vue?vue&type=script&lang=js&\"\nexport * from \"./null-data.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./null-data.vue?vue&type=style&index=0&id=93598fb0&lang=scss&scoped=true&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  \"93598fb0\",\n  \"728f99de\"\n  \n)\n\nexport default component.exports", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../../node_modules/sass-resources-loader/lib/loader.js??ref--7-oneOf-1-4!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./collection.vue?vue&type=style&index=0&id=0ad89564&lang=scss&scoped=true&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"3973d46b\", content, true, context)\n};", "module.exports = __webpack_public_path__ + \"img/profit_null.05cb92f.png\";", "export * from \"-!../../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../../node_modules/sass-resources-loader/lib/loader.js??ref--7-oneOf-1-4!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./collection.vue?vue&type=style&index=0&id=0ad89564&lang=scss&scoped=true&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".user-collection-container[data-v-0ad89564]{width:980px;padding:10px 10px 60px}.user-collection-container[data-v-0ad89564]  .el-tabs__header{margin-left:10px}.user-collection-container[data-v-0ad89564]  .el-tabs .el-tabs__nav-scroll{padding:0}.user-collection-container .user-collection-content .goods[data-v-0ad89564]{width:180px;height:260px;margin-right:15px;margin-bottom:30px;float:left}.user-collection-container .user-collection-content .goods-image[data-v-0ad89564]{width:180px;height:180px;cursor:pointer;position:relative}.user-collection-container .user-collection-content .goods-image:hover .goods-image-wrap[data-v-0ad89564]{opacity:1}.user-collection-container .user-collection-content .goods-image-wrap[data-v-0ad89564]{left:0;bottom:0;width:180px;height:26px;padding:8px 0;color:#fff;text-align:center;position:absolute;opacity:0;transition:opacity .2s linear;background-color:rgba(0,0,0,.2)}.user-collection-container .user-collection-content .goods-image-wrap>div[data-v-0ad89564]{width:90px;cursor:pointer}.user-collection-container .user-collection-content .goods-image-wrap>div[data-v-0ad89564]:first-child{border-right:1px solid #fff}.user-collection-container .user-collection-content .goods[data-v-0ad89564]:nth-child(5n){margin-right:0}.user-collection-container .user-collection-content .goods-name[data-v-0ad89564]{height:36px;color:#101010}.user-collection-container .user-collection-content .shop[data-v-0ad89564]{padding:20px 0;border-bottom:1px solid #e5e5e5}.user-collection-container .user-collection-content .shop .shop-item:hover .shop-wrap[data-v-0ad89564]{opacity:1}.user-collection-container .user-collection-content .shop .shop-item[data-v-0ad89564]{width:148px;height:220px;background-size:cover;background-position:50%;padding:10px;border-radius:6px;position:relative}.user-collection-container .user-collection-content .shop .shop-item .shop-wrap[data-v-0ad89564]{top:0;left:0;position:absolute;width:148px;height:26px;padding:8px 0;color:#fff;opacity:0;text-align:center;transition:opacity .2s linear;background-color:rgba(0,0,0,.2)}.user-collection-container .user-collection-content .shop .shop-item .shop-wrap>div[data-v-0ad89564]{width:74px;cursor:pointer}.user-collection-container .user-collection-content .shop .shop-item .shop-wrap>div[data-v-0ad89564]:first-child{border-right:1px solid #fff}.user-collection-container .user-collection-content .shop .shop-item .shop-info[data-v-0ad89564]{border-radius:6px;padding:18px 15px}.user-collection-container .user-collection-content .shop .shop-item .shop-info .logo[data-v-0ad89564]{width:70px;height:70px;border-radius:50%;margin-top:-45px}.user-collection-container .user-collection-content .shop[data-v-0ad89564]:last-child{border-bottom:0}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"user-collection-container\"},[_vm._ssrNode(\"<div class=\\\"user-collection-content\\\" data-v-0ad89564>\",\"</div>\",[_c('el-tabs',{staticClass:\"mt10\",on:{\"tab-click\":_vm.handleClick},model:{value:(_vm.activeName),callback:function ($$v) {_vm.activeName=$$v},expression:\"activeName\"}},_vm._l((_vm.userCollection),function(item,index){return _c('el-tab-pane',{key:index,attrs:{\"label\":item.name,\"name\":item.type+''}},[(index == _vm.activeName)?_c('div',[(_vm.userCollection[_vm.activeName].lists.length)?[(_vm.activeName == 0)?_vm._l((item.lists),function(item2,index2){return _c('div',{key:index2,staticClass:\"goods\",on:{\"click\":function($event){$event.stopPropagation();return _vm.$router.push({\n                                            path: '/goods_details/'+item2.id\n                                        })}}},[_c('div',{staticClass:\"goods-image\"},[_c('el-image',{staticStyle:{\"width\":\"180px\",\"height\":\"180px\"},attrs:{\"src\":item2.image,\"fit\":\"fit\"}}),_vm._v(\" \"),_c('div',{staticClass:\"goods-image-wrap flex\"},[_c('div',{on:{\"click\":function($event){$event.stopPropagation();return _vm.cancelColl(item2.id)}}},[_vm._v(\"取消收藏\")]),_vm._v(\" \"),_c('div',{on:{\"click\":function($event){$event.stopPropagation();return _vm.$router.push({\n                                            path: '/shop_street_detail',\n                                            query: {\n                                                id: item2.shop_id\n                                            }\n                                        })}}},[_vm._v(\"进入店铺\")])])],1),_vm._v(\" \"),_c('div',{staticClass:\"goods-name m-t-10\"},[_c('div',{staticClass:\"line-2\"},[_vm._v(_vm._s(item2.name))])]),_vm._v(\" \"),_c('div',{staticClass:\"m-t-14\"},[_c('span',{staticClass:\"primary xl\"},[_vm._v(\"¥\"+_vm._s(item2.min_price))])])])}):_vm._e(),_vm._v(\" \"),(_vm.activeName == 1)?_vm._l((item.lists),function(item2,index2){return _c('div',{key:index2,staticClass:\"shop flex\",staticStyle:{\"width\":\"100%\"}},[_c('div',{staticClass:\"shop-item flex-col row-right\",style:({\n                                'background-image': (\"url(\" + (item2.cover) + \")\"),\n                            })},[_c('div',{staticClass:\"shop-wrap xs flex\"},[_c('div',{on:{\"click\":function($event){$event.stopPropagation();return _vm.cancelColl(item2.id)}}},[_vm._v(\"取消收藏\")]),_vm._v(\" \"),_c('div',{on:{\"click\":function($event){$event.stopPropagation();return _vm.$router.push({\n                                            path: '/shop_street_detail',\n                                            query: {\n                                                id: item2.shop_id\n                                            }\n                                        })}}},[_vm._v(\"进入店铺\")])]),_vm._v(\" \"),_c('div',{staticClass:\"bg-white shop-info text-center\"},[_c('el-image',{staticClass:\"logo\",attrs:{\"src\":item2.logo}}),_vm._v(\" \"),_c('div',{staticClass:\"m-t-12 line-1 lg\"},[_vm._v(\"\\n                                            \"+_vm._s(item2.name)+\"\\n                                        \")])],1)]),_vm._v(\" \"),(item2.goods_list.length >= 1)?_c('div',{staticClass:\"flex-1 m-l-20\",staticStyle:{\"width\":\"100%\"}},[_c('div',{staticClass:\"shop-title flex row-between\"},[_c('span',[_vm._v(\"店铺推荐\")]),_vm._v(\" \"),_c('div',{staticClass:\"pointer\",on:{\"click\":function($event){$event.stopPropagation();return _vm.$router.push({\n                                            path: '/shop_street_detail',\n                                            query: {\n                                                id: item2.shop_id\n                                            }\n                                        })}}},[_vm._v(\"\\n                                            进入店铺\"),_c('i',{staticClass:\"el-icon-arrow-right\"})])]),_vm._v(\" \"),_c('div',{staticClass:\"m-t-20 flex\"},_vm._l((item2.goods_list),function(item3,index3){return _c('div',{key:index3,staticClass:\"m-r-16\",on:{\"click\":function($event){$event.stopPropagation();return _vm.$router.push({\n                                            path: '/goods_details/'+item3.id\n                                        })}}},[_c('el-image',{staticStyle:{\"width\":\"150px\",\"height\":\"150px\"},attrs:{\"src\":item3.image,\"fit\":\"fit\"}}),_vm._v(\" \"),_c('div',{staticClass:\"primary flex row-center m-t-10\"},[_vm._v(\"\\n                                                ¥\"+_vm._s(item3.min_price)+\"\\n                                            \")])],1)}),0)]):_vm._e()])}):_vm._e()]:[_c('null-data',{attrs:{\"img\":require('~/static/images/profit_null.png'),\"text\":\"暂无收藏~\"}})]],2):_vm._e()])}),1)],1)])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nexport default {\n    head() {\n        return {\n            title: this.$store.getters.headTitle,\n            link: [\n                {\n                    rel: \"icon\",\n                    type: \"image/x-icon\",\n                    href: this.$store.getters.favicon,\n                },\n            ],\n        };\n    },\n    layout: \"user\",\n    data() {\n        return {\n            activeName: 0,\n            recodeList: {},\n            userCollection: [\n                {\n                    type: 0,\n                    lists: [],\n                    name: \"商品\",\n                    count: 0,\n                    page: 1,\n                },\n                {\n                    type: 1,\n                    lists: [],\n                    name: \"店铺\",\n                    count: 0,\n                    page: 1,\n                },\n            ],\n        };\n    },\n    fetch() {\n        this.handleClick();\n    },\n    mounted() {\n        this.getRecodeList();\n    },\n    methods: {\n        handleClick() {\n            this.getRecodeList();\n        },\n\n        changePage(val) {\n            this.userCollection.some((item) => {\n                if (item.type == this.activeName) {\n                    item.page = val;\n                }\n            });\n            this.getRecodeList();\n        },\n\n        async getRecodeList() {\n            const { activeName, userCollection } = this;\n            const item = userCollection.find((item) => item.type == activeName);\n            const {\n                data: { lists, count },\n                code,\n            } =\n                activeName == 0\n                    ? await this.$get(\"goods_collect/lists\", {\n                          params: {\n                              page_size: 10,\n                              page_no: item.page,\n                          },\n                      })\n                    : await this.$get(\"pc/shopFollowList\", {\n                          params: {\n                              page_size: 10,\n                              page_no: item.page,\n                          },\n                      });\n            if (code == 1) {\n                this.recodeList = { lists, count };\n            }\n        },\n\n        async cancelColl(id) {\n            const { code, msg } =\n                this.activeName == 0\n                    ? await this.$post(\"goods_collect/changeStatus\", {\n                          goods_id: id,\n                      })\n                    : await this.$post(\"shop_follow/changeStatus\", {\n                          shop_id: id,\n                      });\n\n            if (code == 1) {\n                this.$message.success(\"取消成功\");\n            }\n            this.getRecodeList();\n        },\n    },\n    watch: {\n        recodeList: {\n            immediate: true,\n            handler(val) {\n                console.log(\"val:\", val);\n                this.userCollection.some((item) => {\n                    if (item.type == this.activeName) {\n                        Object.assign(item, val);\n                        return true;\n                    }\n                });\n            },\n        },\n    },\n};\n", "import mod from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./collection.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./collection.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./collection.vue?vue&type=template&id=0ad89564&scoped=true&\"\nimport script from \"./collection.vue?vue&type=script&lang=js&\"\nexport * from \"./collection.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./collection.vue?vue&type=style&index=0&id=0ad89564&lang=scss&scoped=true&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  \"0ad89564\",\n  \"a89fb480\"\n  \n)\n\nexport default component.exports\n\n/* nuxt-component-imports */\ninstallComponents(component, {NullData: require('/Users/<USER>/Desktop/vue/pc/components/null-data.vue').default})\n"], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACXA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACNA;AACA;AACA;;;;;ACFA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AADA;AAGA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AARA;AAaA;AAfA;;ACRA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;ACvBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;ACXA;;;;;;;;ACAA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACNA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;ACvBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAHA;AAHA;AAUA;AACA;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AALA;AAQA;AACA;AACA;AACA;AACA;AALA;AAXA;AAoBA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAFA;AAMA;AACA;AACA;AAFA;AADA;AAOA;AACA;AACA;AAFA;AADA;AACA;AAKA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAGA;AADA;AAIA;AADA;AACA;AAGA;AACA;AACA;AACA;AAAA;AACA;AACA;AAtDA;AAuDA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAXA;AADA;AAjGA;;AC7GA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;A", "sourceRoot": ""}