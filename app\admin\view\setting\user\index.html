{layout name="layout1" /}
<style>
    .layui-form-label {
        width: 120px;
        text-align: left;
        padding-left:30px;
    }
    .invite_appoint_user{
        display: none;
    }
</style>
<div class="wrapper">
    <!-- 面板 -->
    <div class="layui-panel">
        <!-- 表单 -->
        <form class="layui-form">
            <!-- 字段集区块 -->
            <fieldset class="layui-elem-field layui-field-title">
                <legend>邀请下级</legend>
                <div class="layui-field-box">
                    <div class="layui-form-item">
                        <label class="layui-form-label">邀请下级</label>
                        <div class="layui-input-block">
                            <input type="radio" name="is_open" value="0" title="关闭" {if $config.is_open == 0}checked{/if}>
                            <input type="radio" name="is_open" value="1" title="开启" {if $config.is_open == 1}checked{/if}>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label"></label>
                        <div class="layui-input-block layui-word-aux">
                            关闭功能后用户之间不能建立新的上下级关系。
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">邀请下级资格</label>
                        <div class="layui-input-block">
                            <input type="radio" lay-filter="qualifications" lay-skin="primary" name="qualifications" value="1" title="全部用户" >
                            <input type="radio" lay-filter="qualifications" lay-skin="primary" name="qualifications" value="2" title="分销会员" >
                        </div>
                        <div class="layui-input-block invite_appoint_user">
                            {foreach $user_level as $key => $vo}
                            <input type="checkbox" lay-skin="primary" name="invite_appoint_user[{$vo.id}]"  title="{$vo.name}" {if in_array($vo.id, $config.invite_appoint_user)}checked{/if}>
                            {/foreach}
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label"></label>
                        <div class="layui-input-block layui-word-aux">
                            勾选全部用户可以邀请表示系统所有用户都有邀请下级的资格
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">成为下级的条件</label>
                        <div class="layui-input-block">
                            <input type="radio" name="condition" value="1" title="邀请码" {if $config.condition == 1}checked{/if}>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label"></label>
                        <div class="layui-input-block layui-word-aux">
                            用户登录后首次绑定邀请码建立上下级关系。包括扫码，点击分享链接，输入邀请码等场景。
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">自定义邀请海报：</label>
                        <div class="layui-input-block">
                            <div class="like-upload-image">
                                {if !empty($config.poster)}
                                <div class="upload-image-div">
                                    <img src="{$config.poster}" alt="img" style="background-color:#EEEEEE;height: 80px;width:auto">
                                    <input name="poster" type="hidden" value="{$config.poster}">
                                    <div class="del-upload-btn">x</div>
                                </div>
                                <div class="upload-image-elem" style="display:none;"><a class="add-upload-image poster"> + 添加图片</a></div>
                                {else}
                                <div class="upload-image-elem"><a class="add-upload-image poster"> + 添加图片</a></div>
                                {/if}
                            </div>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label"></label>
                        <label class=" layui-form-mid layui-word-aux">自定义分销推广海报图片，建议尺寸：800*800像素</label>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label"></label>
                        <div class="layui-input-block">
                            <button class="layui-btn layui-bg-blue layui-btn layui-btn-sm" lay-submit lay-filter="*">保存设置</button>
                        </div>
                    </div>
                </div>
            </fieldset>
        </form>
    </div>
</div>


<script>

    layui.config({
        version: "{$front_version}",
        base: '/static/lib/'
    }).use(['element', 'form'], function () {
        var $ = layui.$
            , form = layui.form
            , layer = layui.layer;

        var qualifications = {:json_encode($config.qualifications)};
        if(Array.isArray(qualifications)){
            if(qualifications.indexOf("1") > -1){
                $("input[name=qualifications][value='1']").prop('checked',"true");
            }else{
                $("input[name=qualifications][value='2']").prop('checked',"true");
                $('.invite_appoint_user').show();
            }

        }else{
            var select = qualifications[Object.keys(qualifications)[0]];
            $("input[name=qualifications][value="+select+"]").prop('checked',"true");
            if(2 == select){
                $('.invite_appoint_user').show();
            }
        }
        form.render();
        layui.form.on('radio(qualifications)', function(data){
            var value = data.value;
            if(1 == value){
                $('.invite_appoint_user').hide();
            }
            if(2 == value){
                $('.invite_appoint_user').show();
            }
        })

        like.delUpload();
        // 自定义邀请海报
        $(document).on("click", ".poster", function () {
            like.imageUpload({
                limit: 1,
                field: "poster",
                that: $(this)
            });
        });

        form.on('submit(*)', function (data) {
            like.ajax({
                url: '{:url("setting.user/set")}'
                , data: data.field
                , type: 'post'
                , success: function (res) {
                    if (res.code == 1) {
                        layer.msg(res.msg);
                    }
                }
            });
            return false; //阻止表单跳转
        });

    });
</script>