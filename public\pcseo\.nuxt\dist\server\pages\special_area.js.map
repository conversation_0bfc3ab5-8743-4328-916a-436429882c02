{"version": 3, "file": "pages/special_area.js", "sources": ["webpack:///./components/price-formate.vue?b158", "webpack:///./components/price-formate.vue?4c8e", "webpack:///./components/price-formate.vue", "webpack:///./components/price-formate.vue?3e0e", "webpack:///./components/price-formate.vue?2b64", "webpack:///./components/price-formate.vue?8e8e", "webpack:///./components/price-formate.vue?32b6", "webpack:///./components/null-data.vue?48f8", "webpack:///./components/null-data.vue?97fe", "webpack:///./components/null-data.vue?fba4", "webpack:///./components/null-data.vue?cbf9", "webpack:///./components/null-data.vue", "webpack:///./components/null-data.vue?da63", "webpack:///./components/null-data.vue?475d", "webpack:///./components/goods-list.vue?c658", "webpack:///./components/goods-list.vue?fc8c", "webpack:///./components/goods-list.vue?0eb4", "webpack:///./components/goods-list.vue?0d98", "webpack:///./components/goods-list.vue", "webpack:///./components/goods-list.vue?d834", "webpack:///./components/goods-list.vue?12b6", "webpack:///./static/images/goods_null.png", "webpack:///./pages/special_area.vue?316a", "webpack:///./pages/special_area.vue?1964", "webpack:///./pages/special_area.vue?89b0", "webpack:///./static/images/special_area_bg.jpg", "webpack:///./pages/special_area.vue?9aa1", "webpack:///./pages/special_area.vue", "webpack:///./pages/special_area.vue?7297", "webpack:///./pages/special_area.vue?e0b7"], "sourcesContent": ["// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../node_modules/css-loader/dist/cjs.js??ref--3-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--3-oneOf-1-2!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./price-formate.vue?vue&type=style&index=0&lang=css&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"3181fc86\", content, true, context)\n};", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('span',{class:(_vm.lineThrough ? 'line-through' : '') + 'price-format',style:({ color: _vm.color, 'font-weight': _vm.weight })},[_vm._ssrNode(((_vm.showSubscript)?(\"<span\"+(_vm._ssrStyle(null,{\n            'font-size': _vm.subscriptSize + 'px',\n            'margin-right': '1px',\n        }, null))+\">¥</span>\"):\"<!---->\")+\" <span\"+(_vm._ssrStyle(null,{ 'font-size': _vm.firstSize + 'px', 'margin-right': '1px' }, null))+\">\"+_vm._ssrEscape(_vm._s(_vm.priceSlice.first))+\"</span> \"+((_vm.priceSlice.second)?(\"<span\"+(_vm._ssrStyle(null,{ 'font-size': _vm.secondSize + 'px' }, null))+\">\"+_vm._ssrEscape(\".\"+_vm._s(_vm.priceSlice.second))+\"</span>\"):\"<!---->\"))])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nexport default {\n    data() {\n        return {\n            priceSlice: {},\n        }\n    },\n    components: {},\n    props: {\n        firstSize: {\n            type: Number,\n            default: 14,\n        },\n        secondSize: {\n            type: Number,\n            default: 14,\n        },\n        color: {\n            type: String,\n        },\n        weight: {\n            type: [String, Number],\n            default: 400,\n        },\n        price: {\n            type: [String, Number],\n            default: '',\n        },\n        showSubscript: {\n            type: Boolean,\n            default: true,\n        },\n        subscriptSize: {\n            type: Number,\n            default: 14,\n        },\n        lineThrough: {\n            type: Boolean,\n            default: false,\n        },\n    },\n    created() {\n        this.priceFormat()\n    },\n    watch: {\n        price(val) {\n            this.priceFormat()\n        },\n    },\n    methods: {\n        priceFormat() {\n            let { price } = this\n            let priceSlice = {}\n            if (price !== null) {\n                price = parseFloat(price)\n                price = String(price).split('.')\n                priceSlice.first = price[0]\n                priceSlice.second = price[1]\n                this.priceSlice = priceSlice\n            }\n        },\n    },\n}\n", "import mod from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./price-formate.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./price-formate.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./price-formate.vue?vue&type=template&id=0c4d5c85&\"\nimport script from \"./price-formate.vue?vue&type=script&lang=js&\"\nexport * from \"./price-formate.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./price-formate.vue?vue&type=style&index=0&lang=css&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  null,\n  \"7ae24710\"\n  \n)\n\nexport default component.exports", "export * from \"-!../node_modules/vue-style-loader/index.js??ref--3-oneOf-1-0!../node_modules/css-loader/dist/cjs.js??ref--3-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--3-oneOf-1-2!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./price-formate.vue?vue&type=style&index=0&lang=css&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".price-format{display:flex;align-items:baseline}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/sass-resources-loader/lib/loader.js??ref--7-oneOf-1-4!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./null-data.vue?vue&type=style&index=0&id=93598fb0&lang=scss&scoped=true&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"12a18d22\", content, true, context)\n};", "export * from \"-!../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/sass-resources-loader/lib/loader.js??ref--7-oneOf-1-4!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./null-data.vue?vue&type=style&index=0&id=93598fb0&lang=scss&scoped=true&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".null-data[data-v-93598fb0]{padding:100px}.null-data .img-null[data-v-93598fb0]{width:150px;height:150px}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"bg-white flex-col col-center null-data\"},[_vm._ssrNode(\"<img\"+(_vm._ssrAttr(\"src\",_vm.img))+\" alt class=\\\"img-null\\\"\"+(_vm._ssrStyle(null,_vm.imgStyle, null))+\" data-v-93598fb0> <div class=\\\"muted mt8\\\" data-v-93598fb0>\"+_vm._ssrEscape(_vm._s(_vm.text))+\"</div>\")])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n\nexport default {\n    components: {},\n    props: {\n        img: {\n            type: String,\n        },\n        text: {\n            type: String,\n            default: '暂无数据',\n        },\n        imgStyle: {\n            type: String,\n            default: '',\n        },\n    },\n    methods: {},\n}\n", "import mod from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./null-data.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./null-data.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./null-data.vue?vue&type=template&id=93598fb0&scoped=true&\"\nimport script from \"./null-data.vue?vue&type=script&lang=js&\"\nexport * from \"./null-data.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./null-data.vue?vue&type=style&index=0&id=93598fb0&lang=scss&scoped=true&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  \"93598fb0\",\n  \"728f99de\"\n  \n)\n\nexport default component.exports", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/sass-resources-loader/lib/loader.js??ref--7-oneOf-1-4!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./goods-list.vue?vue&type=style&index=0&id=060944d1&lang=scss&scoped=true&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"1469a4e1\", content, true, context)\n};", "export * from \"-!../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/sass-resources-loader/lib/loader.js??ref--7-oneOf-1-4!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./goods-list.vue?vue&type=style&index=0&id=060944d1&lang=scss&scoped=true&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".goods-list[data-v-060944d1]{align-items:stretch}.goods-list .goods-item[data-v-060944d1]{display:block;box-sizing:border-box;width:224px;height:310px;margin-bottom:16px;padding:12px 12px 16px;border-radius:4px;transition:all .2s}.goods-list .goods-item[data-v-060944d1]:hover{transform:translateY(-8px);box-shadow:0 0 6px rgba(0,0,0,.1)}.goods-list .goods-item .goods-img[data-v-060944d1]{width:200px;height:200px}.goods-list .goods-item .name[data-v-060944d1]{margin-bottom:10px;height:40px;line-height:20px}.goods-list .goods-item .seckill .btn[data-v-060944d1]{padding:4px 12px;border-radius:4px;border:1px solid transparent}.goods-list .goods-item .seckill .btn.not-start[data-v-060944d1]{border-color:#ff2c3c;color:#ff2c3c;background-color:transparent}.goods-list .goods-item .seckill .btn.end[data-v-060944d1]{background-color:#e5e5e5;color:#fff}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"goods-list flex flex-wrap\"},_vm._l((_vm.list),function(item,index){return _c('nuxt-link',{key:index,staticClass:\"goods-item bg-white\",style:({ marginRight: (index + 1) % _vm.num == 0 ? 0 : '14px' }),attrs:{\"to\":(\"/goods_details/\" + (item.id||item.goods_id))}},[_c('el-image',{staticClass:\"goods-img\",attrs:{\"lazy\":\"\",\"src\":item.image||item.goods_image,\"alt\":\"\"}}),_vm._v(\" \"),_c('div',{staticClass:\"name line-2\"},[_vm._v(_vm._s(item.name||item.goods_name))]),_vm._v(\" \"),(_vm.type == 'seckill')?_c('div',{staticClass:\"seckill flex row-between\"},[_c('div',{staticClass:\"primary flex\"},[_vm._v(\"\\n                秒杀价\\n                \"),_c('price-formate',{attrs:{\"price\":item.seckill_price,\"first-size\":18}})],1),_vm._v(\" \"),_c('div',{class:['btn bg-primary white', {'not-start' : _vm.status == 0, end: _vm.status == 2}]},[_vm._v(_vm._s(_vm.getSeckillText)+\"\\n            \")])]):_c('div',{staticClass:\"flex row-between flex-wrap\"},[_c('div',{staticClass:\"price flex col-baseline\"},[_c('div',{staticClass:\"primary m-r-8\"},[_c('price-formate',{attrs:{\"price\":item.min_price || item.price,\"first-size\":16}})],1),_vm._v(\" \"),_c('div',{staticClass:\"muted sm line-through\"},[_c('price-formate',{attrs:{\"price\":item.market_price}})],1)]),_vm._v(\" \"),_c('div',{staticClass:\"muted xs\"},[_vm._v(_vm._s(item.sales_total || item.sales_sum || 0)+\"人购买\")])])],1)}),1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nexport default {\n    props: {\n        list: {\n            type: Array,\n            default: () => [],\n        },\n        num: {\n            type: Number,\n            default: 5,\n        },\n        type: {\n            type: String,\n        },\n        status: {\n            type: Number,\n        },\n    },\n    watch: {\n        list: {\n            immediate: true,\n            handler: function (val) {},\n        },\n    },\n    computed: {\n        getSeckillText() {\n            switch (this.status) {\n                case 0:\n                    return \"未开始\";\n                case 1:\n                    return \"立即抢购\";\n                case 2:\n                    return \"已结束\";\n            }\n        },\n    },\n};\n", "import mod from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./goods-list.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./goods-list.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./goods-list.vue?vue&type=template&id=060944d1&scoped=true&\"\nimport script from \"./goods-list.vue?vue&type=script&lang=js&\"\nexport * from \"./goods-list.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./goods-list.vue?vue&type=style&index=0&id=060944d1&lang=scss&scoped=true&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  \"060944d1\",\n  \"606a8712\"\n  \n)\n\nexport default component.exports\n\n/* nuxt-component-imports */\ninstallComponents(component, {PriceFormate: require('/Users/<USER>/Desktop/vue/pc/components/price-formate.vue').default})\n", "module.exports = __webpack_public_path__ + \"img/goods_null.38f1689.png\";", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/sass-resources-loader/lib/loader.js??ref--7-oneOf-1-4!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./special_area.vue?vue&type=style&index=0&id=5f15828c&lang=scss&scoped=true&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"ad05104a\", content, true, context)\n};", "export * from \"-!../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/sass-resources-loader/lib/loader.js??ref--7-oneOf-1-4!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./special_area.vue?vue&type=style&index=0&id=5f15828c&lang=scss&scoped=true&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_GET_URL_IMPORT___ = require(\"../node_modules/css-loader/dist/runtime/getUrl.js\");\nvar ___CSS_LOADER_URL_IMPORT_0___ = require(\"../static/images/special_area_bg.jpg\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\nvar ___CSS_LOADER_URL_REPLACEMENT_0___ = ___CSS_LOADER_GET_URL_IMPORT___(___CSS_LOADER_URL_IMPORT_0___);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".special-area .header-top[data-v-5f15828c]{margin:16px 0}.special-area .header-img[data-v-5f15828c]{width:1180px;height:240px;background:url(\" + ___CSS_LOADER_URL_REPLACEMENT_0___ + \")}.special-area .header-img .header-img-tile .max-title[data-v-5f15828c]{margin-top:65px;font-size:32px;text-align:center}.special-area .header-img .header-img-tile .min-title[data-v-5f15828c]{padding:5px 27px;font-size:14px;text-align:center;border-radius:100px;background-color:#ba7af6}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "module.exports = __webpack_public_path__ + \"img/special_area_bg.cc0dd5c.jpg\";", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"special-area\"},[_vm._ssrNode(\"<div class=\\\"header-top\\\" data-v-5f15828c>\",\"</div>\",[_c('el-breadcrumb',{staticStyle:{\"flex\":\"1\"},attrs:{\"separator\":\"/\"}},[_c('el-breadcrumb-item',{attrs:{\"to\":{ path: '/' }}},[_vm._v(\"首页\")]),_vm._v(\" \"),_c('el-breadcrumb-item',{staticClass:\"line1\",staticStyle:{\"max-width\":\"800px\"}},[_vm._v(_vm._s(_vm.name))])],1)],1),_vm._ssrNode(\" <div class=\\\"m-b-20 header-img\\\" data-v-5f15828c><div class=\\\"header-img-tile flex-col col-center row-center\\\" data-v-5f15828c><div class=\\\"max-title white\\\" data-v-5f15828c>\"+_vm._ssrEscape(\"\\n                \"+_vm._s(_vm.name)+\"\\n            \")+\"</div> <div class=\\\"m-t-22 min-title white\\\" data-v-5f15828c>\"+_vm._ssrEscape(\"\\n                \"+_vm._s(_vm.title)+\"\\n            \")+\"</div></div></div> \"),(_vm.goodsList.length)?[_c('goods-list',{attrs:{\"list\":_vm.goodsList}}),_vm._ssrNode(\" \"),(_vm.count)?_vm._ssrNode(\"<div class=\\\"pagination flex m-t-30 row-center\\\" style=\\\"padding-bottom: 38px\\\" data-v-5f15828c>\",\"</div>\",[_c('el-pagination',{attrs:{\"background\":\"\",\"layout\":\"prev, pager, next\",\"total\":_vm.count,\"prev-text\":\"上一页\",\"next-text\":\"下一页\",\"hide-on-single-page\":\"\",\"page-size\":20},on:{\"current-change\":_vm.changePage}})],1):_vm._e()]:_c('null-data',{attrs:{\"img\":require('~/static/images/goods_null.png'),\"text\":\"暂无商品~\"}})],2)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nexport default {\n    head() {\n        return {\n            title: this.$store.getters.headTitle,\n            link: [{\n                rel: 'icon',\n                type: 'image/x-icon',\n                href: this.$store.getters.favicon,\n            }, ],\n        }\n    },\n\n    async asyncData({ query }) {\n\n        return {\n           name: query.name,\n           title: query.title,\n        }\n    },\n\n    data() {\n        return {\n            goodsList: [],\n            page: 1,\n            count: 0,\n        }\n    },\n\n    methods: {\n        // 获取商品\n        async getGoods() {\n            const id = this.$route.query.id\n            let {\n                code,\n                data: {\n                    list,\n                    count\n                }\n            } = await this.$get(\"activity_area/activityGoodsList\", {\n                params: {\n                    id: id,\n                    page_no: this.page,\n                    page_size: 20,\n                },\n            })\n\n            if (code == 1) {\n                this.goodsList = list\n                this.count = count\n            }\n        },\n\n        changePage(current) {\n            this.page = current;\n            this.getGoods();\n        },\n    },\n\n    created() {\n        this.getGoods()\n    },\n\n}\n", "import mod from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./special_area.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./special_area.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./special_area.vue?vue&type=template&id=5f15828c&scoped=true&\"\nimport script from \"./special_area.vue?vue&type=script&lang=js&\"\nexport * from \"./special_area.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./special_area.vue?vue&type=style&index=0&id=5f15828c&lang=scss&scoped=true&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  \"5f15828c\",\n  \"73cbae65\"\n  \n)\n\nexport default component.exports\n\n/* nuxt-component-imports */\ninstallComponents(component, {GoodsList: require('/Users/<USER>/Desktop/vue/pc/components/goods-list.vue').default,NullData: require('/Users/<USER>/Desktop/vue/pc/components/null-data.vue').default})\n"], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACXA;AACA;AACA;AACA;AACA;AACA;;;;;ACLA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AADA;AAGA;AACA;AAAA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AADA;AAGA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AA5BA;AACA;AAgCA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAJA;AAKA;AACA;AACA;AAAA;AAAA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAZA;AAhDA;;AC1BA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACvBA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACNA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACXA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACNA;AACA;AACA;;;;;ACFA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AADA;AAGA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AARA;AAaA;AAfA;;ACRA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;ACvBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACXA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACNA;AACA;AACA;;;;;ACFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AADA;AAGA;AACA;AADA;AAZA;AAgBA;AACA;AACA;AACA;AAFA;AADA;AAMA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AAAA;AACA;AANA;AAQA;AACA;AAXA;AAvBA;;AC/BA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AC1BA;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACXA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACTA;;;;;;;;;;;;ACAA;AACA;AACA;;;;;ACFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAHA;AAFA;AAQA;AACA;AACA;AAAA;AAAA;AAEA;AACA;AACA;AAFA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AAHA;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAFA;AAFA;AAOA;AACA;AACA;AACA;AAHA;AADA;AACA;AAOA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AA5BA;AACA;AA6BA;AACA;AACA;AACA;AA7DA;;ACjCA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;A", "sourceRoot": ""}