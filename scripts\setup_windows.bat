@echo off
chcp 65001 >nul
echo 🚀 设置kshop项目部署脚本...
echo.

echo 📁 检查脚本目录...
if not exist "scripts" (
    echo ❌ scripts目录不存在！
    pause
    exit /b 1
)

echo 📋 可用的部署脚本:
echo   - deploy.sh              (主部署脚本)
echo   - sync_test_to_prod.sh   (测试版同步到正式版)
echo   - version_manager.sh     (版本管理)
echo   - setup_git_workflow.sh  (Git工作流初始化)
echo.

echo 💡 在Windows环境下使用方法:
echo.
echo 1. 安装Git Bash (如果还没有安装)
echo 2. 在项目根目录右键选择 "Git Bash Here"
echo 3. 运行以下命令:
echo.
echo    # 初始化工作流
echo    bash scripts/setup_git_workflow.sh
echo.
echo    # 部署到测试环境
echo    bash scripts/deploy.sh test
echo.
echo    # 同步到正式环境
echo    bash scripts/sync_test_to_prod.sh
echo.
echo    # 版本管理
echo    bash scripts/version_manager.sh list
echo.

echo 🔧 配置提醒:
echo 1. 修改scripts/deploy.sh中的仓库地址
echo 2. 修改scripts/sync_test_to_prod.sh中的仓库地址
echo 3. 根据服务器实际情况调整路径配置
echo.

echo ✅ 设置完成！
echo.
echo 📖 详细文档请查看: README-deployment.md
echo.
pause
