<?php

namespace app\common\websocket;


class Parser
{
    /**
     * @notes 数组数据转json
     * @param string $event
     * @param $data
     * @return false|string
     * <AUTHOR>
     * @date 2021/12/29 18:27
     */
    public function encode(string $event, $data)
    {
        return json_encode(['event' => $event, 'data' => $data]);
    }

    /**
     * @notes json转数组数据
     * @param $data
     * @return array
     * <AUTHOR>
     * @date 2021/12/29 18:28
     */
    public function decode($data)
    {
        // 检查输入数据是否为空
        if (empty($data)) {
            return [
                'event' => null,
                'data' => null,
            ];
        }

        $result = json_decode($data, true);

        // 检查JSON解析是否成功
        if (json_last_error() !== JSON_ERROR_NONE) {
            // JSON解析失败，记录错误并返回默认值
            \think\facade\Log::error("WebSocket消息JSON解析失败: " . json_last_error_msg() . ", 原始数据: " . $data);
            return [
                'event' => null,
                'data' => null,
            ];
        }

        // 确保返回的数据结构正确
        return [
            'event' => isset($result['event']) && is_string($result['event']) ? $result['event'] : null,
            'data' => $result['data'] ?? null,
        ];
    }

}
