<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Apigateway\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * 服务列表展示
 *
 * @method integer getTotalCount() 获取服务列表总数。
注意：此字段可能返回 null，表示取不到有效值。
 * @method void setTotalCount(integer $TotalCount) 设置服务列表总数。
注意：此字段可能返回 null，表示取不到有效值。
 * @method array getServiceSet() 获取服务列表详情。
注意：此字段可能返回 null，表示取不到有效值。
 * @method void setServiceSet(array $ServiceSet) 设置服务列表详情。
注意：此字段可能返回 null，表示取不到有效值。
 */
class ServicesStatus extends AbstractModel
{
    /**
     * @var integer 服务列表总数。
注意：此字段可能返回 null，表示取不到有效值。
     */
    public $TotalCount;

    /**
     * @var array 服务列表详情。
注意：此字段可能返回 null，表示取不到有效值。
     */
    public $ServiceSet;

    /**
     * @param integer $TotalCount 服务列表总数。
注意：此字段可能返回 null，表示取不到有效值。
     * @param array $ServiceSet 服务列表详情。
注意：此字段可能返回 null，表示取不到有效值。
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("TotalCount",$param) and $param["TotalCount"] !== null) {
            $this->TotalCount = $param["TotalCount"];
        }

        if (array_key_exists("ServiceSet",$param) and $param["ServiceSet"] !== null) {
            $this->ServiceSet = [];
            foreach ($param["ServiceSet"] as $key => $value){
                $obj = new Service();
                $obj->deserialize($value);
                array_push($this->ServiceSet, $obj);
            }
        }
    }
}
