# 设计文档

## 概述

基于现有的WebSocket通知系统，创建一个专门的测试API接口，用于验证向admin和shop模块推送WebSocket通知的功能。该接口将集成到现有的API测试框架中，利用已有的WebSocket处理器和通知机制。

## 架构

### 现有系统分析

通过代码分析发现，系统已经具备以下WebSocket基础设施：

1. **WebSocket处理器**：
   - `CombinedHandler.php` - 组合处理器，同时处理客服和管理员通知
   - `AdminNotificationHandler.php` - 专门的管理员通知处理器
   - `Websocket.php` - WebSocket API控制器

2. **通知机制**：
   - 支持Redis发布订阅模式
   - 支持GatewayWorker推送
   - 已有SSE（Server-Sent Events）通知支持
   - 现有的`Notification.php`控制器提供通知API

3. **测试基础**：
   - 现有`Test.php`控制器已包含SSE通知测试
   - 现有`Websocket.php`控制器提供WebSocket通知API

### 设计方案

在现有`Test.php`控制器中添加WebSocket通知测试方法，复用现有的通知推送机制。

## 组件和接口

### 1. API接口设计

**接口路径**: `/api/test/testWebsocketNotification`
**请求方法**: GET/POST
**功能**: 测试WebSocket通知推送

#### 请求参数

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| target | string | 否 | "all" | 推送目标：admin, shop, all |
| message | string | 否 | "这是一条WebSocket测试通知" | 自定义消息内容 |
| title | string | 否 | "WebSocket测试通知" | 通知标题 |
| type | string | 否 | "test_notification" | 消息类型：order, chat, system, test_notification |
| url | string | 否 | "" | 点击通知跳转的URL |
| icon | integer | 否 | 0 | 通知图标：0-默认，1-成功，2-错误，3-警告，4-信息 |

#### 响应格式

```json
{
    "code": 1,
    "msg": "WebSocket通知测试完成",
    "data": {
        "push_results": {
            "admin": {
                "success": true,
                "connections": 2,
                "message": "已推送给2个管理员连接"
            },
            "shop": {
                "success": true,
                "connections": 1,
                "message": "已推送给1个商家连接"
            }
        },
        "total_connections": 3,
        "test_data": {
            "title": "WebSocket测试通知",
            "content": "这是一条WebSocket测试通知",
            "type": "test_notification",
            "timestamp": 1640995200
        }
    }
}
```

### 2. 核心组件

#### WebSocketNotificationTester类

负责执行WebSocket通知推送测试的核心逻辑：

```php
class WebSocketNotificationTester
{
    public function testAdminNotification($data);
    public function testShopNotification($data);
    public function getConnectionStats();
    public function validateNotificationData($data);
}
```

#### 推送机制集成

利用现有的推送机制：

1. **Redis推送**：通过Redis队列和发布订阅
2. **GatewayWorker推送**：直接推送到WebSocket连接组
3. **Swoole推送**：通过CombinedHandler直接推送

### 3. 消息类型支持

根据需求支持不同类型的测试消息：

- **order**: 订单相关通知测试
- **chat**: 聊天相关通知测试  
- **system**: 系统通知测试
- **test_notification**: 通用测试通知

## 数据模型

### 通知数据结构

```php
$notificationData = [
    'event' => 'admin_notification', // 或 'shop_notification'
    'data' => [
        'type' => $type,
        'title' => $title,
        'content' => $content,
        'url' => $url,
        'icon' => $icon,
        'timestamp' => time(),
        'test_mode' => true, // 标识为测试消息
        'test_id' => uniqid('test_'), // 测试消息唯一标识
    ]
];
```

### 推送结果数据结构

```php
$pushResult = [
    'target' => 'admin|shop|all',
    'success' => true|false,
    'connections' => 0, // 推送的连接数
    'message' => '推送结果描述',
    'error' => '错误信息（如果有）',
    'method' => 'redis|gateway|swoole', // 使用的推送方法
];
```

## 错误处理

### 错误类型

1. **参数验证错误**：无效的target、type等参数
2. **连接错误**：WebSocket服务不可用
3. **推送失败**：Redis、GatewayWorker或Swoole推送失败
4. **权限错误**：未授权访问（生产环境）

### 错误响应格式

```json
{
    "code": 0,
    "msg": "错误描述",
    "data": {
        "error_type": "validation|connection|push|permission",
        "error_details": "详细错误信息",
        "suggestions": ["建议解决方案1", "建议解决方案2"]
    }
}
```

### 错误处理策略

1. **参数验证**：在接口入口进行严格验证
2. **连接检查**：测试前检查WebSocket服务状态
3. **多重推送**：尝试多种推送方式确保成功
4. **详细日志**：记录所有推送尝试和结果
5. **友好提示**：提供具体的错误原因和解决建议

## 测试策略

### 单元测试

1. **参数验证测试**：测试各种参数组合
2. **推送逻辑测试**：测试不同推送方式
3. **错误处理测试**：测试各种错误场景
4. **数据格式测试**：验证消息格式正确性

### 集成测试

1. **端到端测试**：从API调用到WebSocket接收
2. **多连接测试**：测试多个admin和shop连接
3. **并发测试**：测试同时推送多个通知
4. **故障恢复测试**：测试服务异常后的恢复

### 性能测试

1. **推送延迟测试**：测量通知推送延迟
2. **连接数测试**：测试大量连接下的性能
3. **内存使用测试**：监控内存使用情况
4. **CPU使用测试**：监控CPU使用情况

## 安全考虑

### 访问控制

1. **开发环境**：允许无限制访问用于开发测试
2. **测试环境**：需要基本的身份验证
3. **生产环境**：严格限制访问，仅允许管理员使用

### 频率限制

1. **IP限制**：每个IP每分钟最多10次请求
2. **用户限制**：每个用户每分钟最多5次请求
3. **全局限制**：系统每秒最多处理20个测试请求

### 数据安全

1. **敏感信息过滤**：避免在测试消息中包含敏感信息
2. **日志脱敏**：记录日志时过滤敏感数据
3. **临时数据清理**：定期清理测试产生的临时数据

## 监控和日志

### 日志记录

1. **请求日志**：记录所有测试请求的参数和结果
2. **推送日志**：记录每次推送的详细信息
3. **错误日志**：记录所有错误和异常情况
4. **性能日志**：记录推送延迟和系统性能指标

### 监控指标

1. **成功率**：推送成功的比例
2. **响应时间**：API响应时间
3. **连接数**：当前WebSocket连接数
4. **错误率**：各类错误的发生率

### 告警机制

1. **推送失败告警**：连续推送失败时触发告警
2. **性能告警**：响应时间超过阈值时告警
3. **连接异常告警**：WebSocket连接数异常时告警
4. **错误率告警**：错误率超过阈值时告警