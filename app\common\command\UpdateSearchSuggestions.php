<?php
namespace app\common\command;

use app\common\library\MeiliSearch;
use app\common\library\AliNlpService;
use app\common\model\goods\Goods;
//use app\common\model\SearchRecord;
use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\facade\Db;
use think\facade\Log;

/**
 * 更新商品分词和搜索候选词索引
 * 定时任务，用于定期更新商品分词和搜索候选词索引
 */
class UpdateSearchSuggestions extends Command
{
    /**
     * 配置指令
     */
    protected function configure()
    {
        $this->setName('update_search_suggestions')
            ->setDescription('更新商品分词和搜索候选词索引');
    }

    // 批量处理的商品数量
    protected $batchSize = 100;

    // 批量导入的候选词数量
    protected $importBatchSize = 1000;

    // 不使用缓存，直接处理所有数据
    protected function execute(Input $input, Output $output)
    {
        // 设置错误报告级别，显示所有错误
        error_reporting(E_ALL);
        ini_set('display_errors', 1);

        try {
            $startTime = microtime(true);
            $output->writeln('开始更新搜索候选词索引...');

            // 初始化MeiliSearch客户端
            $meili = new MeiliSearch();

            // 检查索引是否存在
            $indexInfo = $meili->getIndex('search_suggestions');
            if (!$indexInfo || isset($indexInfo['error'])) {
                $output->writeln('搜索候选词索引不存在，正在创建...');

                // 创建新索引，明确指定主键为id
                $response = $meili->createIndex('search_suggestions', ['primaryKey' => 'id']);

                if (isset($response['error'])) {
                    $output->writeln('创建索引失败：' . json_encode($response));
                    return false;
                }

                // 更新索引设置
                $settings = [
                    'searchableAttributes' => [
                        'text',
                        'tags'
                    ],
                    'sortableAttributes' => [
                        'weight',
                        'popularity'
                    ],
                    'rankingRules' => [
                        'words',
                        'typo',
                        'proximity',
                        'attribute',
                        'sort',
                        'exactness'
                    ]
                ];
                $response = $meili->updateIndexSettings('search_suggestions', $settings);

                if (isset($response['error'])) {
                    $output->writeln('创建索引失败：' . json_encode($response));
                    return false;
                }

                $output->writeln('索引创建成功，等待索引就绪...');
                // 等待索引就绪
                sleep(2);
            }

            // 初始化阿里云NLP服务 (仍用于更新 goods.split_word)
            $aliNlp = new AliNlpService();
            $output->writeln('已初始化阿里云NLP服务用于更新商品库分词');

            // --- 时间戳管理 ---
            $timestampFile = dirname(__FILE__) . '/last_run_update_search_suggestions.timestamp';
            $lastRunTime = 0;
            if (file_exists($timestampFile)) {
                $lastRunTime = (int)file_get_contents($timestampFile);
            }
            $currentRunTime = time();
            $output->writeln("上次运行时间: " . ($lastRunTime ? date('Y-m-d H:i:s', $lastRunTime) : '从未'));
            // --- END 时间戳管理 ---

            // 获取需要更新分词的商品数据 (例如，过去7天内更新过但split_word为空或旧的)
            $goodsToUpdateSplitWord = Goods::where('del', 0)
                ->where('status', 1)
                ->where('audit_status', 1)
                ->where(function($query) use ($lastRunTime) {
                    $query->where('update_time', '>', $lastRunTime - (7 * 86400))
                          ->whereOr(function($q) {
                              $q->whereNull('split_word')
                                ->whereOr('split_word', '');
                          });
                })
                ->field('id, name, split_word, update_time') // Only fields needed for split_word update
                ->select()
                ->toArray();

            $output->writeln("共有 " . count($goodsToUpdateSplitWord) . " 个商品需要检查/更新分词 (基于split_word或7天更新周期)");

            // 更新商品分词 (这部分逻辑保持，但其输出不直接生成suggestions)
            $updatedSplitWordCount = 0;
            // ... (NLP限流逻辑可以保留或调整)
            foreach ($goodsToUpdateSplitWord as $item) {
                 // Check if split_word is empty or if the item's update_time is more than 7 days ago
                $itemUpdateTime = is_numeric($item['update_time']) ? (int)$item['update_time'] : 0;
                if (empty($item['split_word']) || ($currentRunTime - $itemUpdateTime > 7 * 86400) || $itemUpdateTime > $lastRunTime) {
                    try {
                        // Simplified: Using existing NLP logic for split_word update
                        $segmentResult = $aliNlp->segment($item['name']);
                        $nerResult = $aliNlp->nerEcom($item['name']);
                        $words = [];
                        if (isset($segmentResult['words']) && is_array($segmentResult['words'])) $words = array_merge($words, $segmentResult['words']);
                        if (isset($nerResult['words']) && is_array($nerResult['words'])) $words = array_merge($words, $nerResult['words']);
                        $words = array_unique(array_filter($words, function($word) {
                            return is_string($word) && mb_strlen($word, 'UTF-8') >= 2;
                        }));
                        $splitWord = implode(',', $words);
                        Db::name('goods')->where('id', $item['id'])->update([
                            'split_word' => $splitWord,
                            'update_time' => $currentRunTime // Touch update_time
                        ]);
                        $updatedSplitWordCount++;
                         if ($updatedSplitWordCount % 100 == 0) {
                            $output->writeln("已更新 {$updatedSplitWordCount} 个商品的分词...");
                        }
                    } catch (\Exception $e) {
                        Log::error("更新商品分词失败 [ID: {$item['id']}]: " . $e->getMessage());
                    }
                }
            }
            $output->writeln("商品库分词更新完成！共更新 {$updatedSplitWordCount} 个商品。");

            // --- 生成候选词 ---
            $output->writeln("开始生成和更新候选词 (基于时间戳: > " . date('Y-m-d H:i:s', $lastRunTime) . ")");
            $suggestionsMap = [];

            // 1. 新增/更新的商品名称
            $recentGoods = Goods::where([
                ['del', '=', 0],
                ['status', '=', 1],
                ['audit_status', '=', 1],
            ])
            ->where('update_time', '>', $lastRunTime)
            ->field('id, name, sales_actual, sales_virtual')
            ->cursor();

            $output->writeln("处理最近更新的商品名称...");
            $processedRecentGoods = 0;
            foreach ($recentGoods as $item) {
                $processedRecentGoods++;
                $text = trim($item['name']);
                if (empty($text) || mb_strlen($text, 'UTF-8') < 2) continue;
                $id = md5($text);
                $suggestionsMap[$id] = [
                    'id' => $id,
                    'text' => $text,
                    'tags' => 'product_name',
                    'weight' => 90,
                    'popularity' => (int)$item['sales_actual'] + (int)$item['sales_virtual'],
                    'goods_id' => (int)$item['id']
                ];
            }
            $output->writeln("最近商品名称处理完成: {$processedRecentGoods} 个。");

            // 2. 新增的分类名称
            $recentCategories = Db::name('goods_category')
                ->field('id, name')
                ->where('del', 0)
                ->where('name', '<>', '')
                ->where('create_time', '>', $lastRunTime) // Assuming create_time field exists
                ->distinct(true)
                ->select()
                ->toArray();

            $output->writeln('处理最近新增的分类名称...');
            foreach ($recentCategories as $category) {
                $text = trim($category['name']);
                if (empty($text) || mb_strlen($text, 'UTF-8') < 2) continue;
                $categoryPopularity = Goods::where('first_cate_id', $category['id'])
                                    ->whereOr('second_cate_id', $category['id'])
                                    ->whereOr('third_cate_id', $category['id'])
                                    ->count();
                $id = md5($text);
                if (!isset($suggestionsMap[$id]) || $categoryPopularity > ($suggestionsMap[$id]['popularity'] ?? 0)) {
                    $suggestionsMap[$id] = [
                        'id' => $id,
                        'text' => $text,
                        'tags' => 'category',
                        'weight' => 70,
                        'popularity' => $categoryPopularity,
                        'goods_id' => 0
                    ];
                }
            }
             $output->writeln("最近分类名称处理完成。");

            // 3. 新增的品牌名称
            $recentBrands = Db::name('goods_brand')
                ->field('id, name')
                ->where('del', 0)
                ->where('name', '<>', '')
                ->where('create_time', '>', $lastRunTime) // Assuming create_time field exists
                ->distinct(true)
                ->select()
                ->toArray();
            $output->writeln('处理最近新增的品牌名称...');
            foreach ($recentBrands as $brand) {
                $text = trim($brand['name']);
                if (empty($text) || mb_strlen($text, 'UTF-8') < 2) continue;
                $brandPopularity = Goods::where('brand_id', $brand['id'])->count();
                $id = md5($text);
                 if (!isset($suggestionsMap[$id]) || $brandPopularity > ($suggestionsMap[$id]['popularity'] ?? 0)) {
                    $suggestionsMap[$id] = [
                        'id' => $id,
                        'text' => $text,
                        'tags' => 'brand',
                        'weight' => 75,
                        'popularity' => $brandPopularity,
                        'goods_id' => 0
                    ];
                }
            }
            $output->writeln("最近品牌名称处理完成。");

            // 4. 最近的热门搜索词
            $recentHotKeywords = Db::name('search_record')
                ->field('keyword, COUNT(*) as search_count')
                ->where('del', 0)
                ->where('update_time', '>', $lastRunTime) // Focus on recently active search terms
                ->group('keyword')
                ->order('search_count DESC')
                ->limit(200) // Limit processing for updates
                ->select()
                ->toArray();

            $output->writeln('处理最近热门搜索词...');
            foreach ($recentHotKeywords as $item) {
                $text = trim($item['keyword']);
                if (empty($text) || mb_strlen($text, 'UTF-8') < 2) continue;
                $id = md5($text);
                if (!isset($suggestionsMap[$id]) || (int)$item['search_count'] > ($suggestionsMap[$id]['popularity'] ?? 0)) {
                    $suggestionsMap[$id] = [
                        'id' => $id,
                        'text' => $text,
                        'tags' => 'popular_search',
                        'weight' => 100,
                        'popularity' => (int)$item['search_count'],
                        'goods_id' => 0
                    ];
                }
            }
            $output->writeln("最近热门搜索词处理完成。");

            $finalSuggestions = array_values($suggestionsMap);

            // 导入候选词
            if (!empty($finalSuggestions)) {
                $totalSuggestions = count($finalSuggestions);
                $output->writeln("准备导入/更新 {$totalSuggestions} 个候选词到索引...");

                $batchSize = 500;
                $totalBatches = ceil($totalSuggestions / $batchSize);
                $output->writeln("将分 {$totalBatches} 批导入/更新");

                for ($i = 0; $i < $totalBatches; $i++) {
                    $batch = array_slice($finalSuggestions, $i * $batchSize, $batchSize);
                    $output->writeln("处理第 " . ($i + 1) . "/{$totalBatches} 批，共 " . count($batch) . " 个候选词");
                    try {
                        // importDocuments with primary key 'id' will add new documents or update existing ones
                        $response = $meili->importDocuments('search_suggestions', $batch, 'id');
                        if (isset($response['taskUid'])) {
                            $output->writeln("批次导入/更新成功，任务ID：{$response['taskUid']}");
                        } else {
                            $output->writeln("批次导入/更新失败：" . json_encode($response));
                        }
                        if ($i < $totalBatches - 1) usleep(200000); // 0.2s pause
                    } catch (\Exception $e) {
                        $output->writeln("批次导入/更新异常: " . $e->getMessage());
                    }
                }
            } else {
                $output->writeln("没有新的或更新的候选词需要导入");
            }

            // 更新时间戳文件
            file_put_contents($timestampFile, $currentRunTime);
            $output->writeln("时间戳已更新为: " . date('Y-m-d H:i:s', $currentRunTime));

            $endTime = microtime(true);
            $executionTime = round($endTime - $startTime, 2);
            $output->writeln("搜索候选词索引更新完成！执行时间: {$executionTime} 秒");
            return true;
        } catch (\Exception $e) {
            Log::error('更新搜索候选词索引异常: ' . $e->getMessage() . ' Trace: ' . $e->getTraceAsString());
            $output->writeln('更新搜索候选词索引异常: ' . $e->getMessage());
            $output->writeln('异常位置: ' . $e->getFile() . ':' . $e->getLine());
            $output->writeln('堆栈跟踪:');
            $output->writeln($e->getTraceAsString());
            return false;
        }
    }
}
