<?php


namespace app\kefuapi\controller;

use app\common\basics\KefuBase;
use app\kefuapi\logic\LoginLogic;
use app\common\server\JsonServer;
use app\kefuapi\validate\LoginValidate;

/**
 * 客服账号登录
 * Class Account
 * @package app\shopapi\controller
 */
class Account extends KefuBase
{

    public $like_not_need_login = ['login'];


    /**
     * @notes 账号密码登录
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2021/11/9 16:21
     */
    public function login()
    {
        $post = $this->request->post();
        (new LoginValidate())->goCheck();
        $result = LoginLogic::accountLogin($post);
        return JsonServer::success('登录成功', $result);
    }



    /**
     * @notes 退出登录
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2021/11/9 15:49
     */
    public function logout()
    {
        LoginLogic::logout($this->kefu_id, $this->client);
        return JsonServer::success();
    }


}