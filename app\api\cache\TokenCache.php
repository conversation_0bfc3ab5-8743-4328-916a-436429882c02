<?php



namespace app\api\cache;


use app\common\cache\CacheBase;
use think\facade\Db;
use think\facade\Config;

class TokenCache extends CacheBase
{

    public function setTag()
    {
        return 'token';
    }

    /**
     * 子类实现查出数据
     * @return mixed
     */
    public function setData()
    {
        // 检查当前token是否为永久token
        $session = Db::name('session')
            ->where(['token' => $this->extend['token']])
            ->find();

        $time = time();

        // 如果是永久token（过期时间为2147483647），则不刷新过期时间
        if ($session && $session['expire_time'] == 2147483647) {
            // 只更新最后访问时间，不修改过期时间
            Db::name('session')
                ->where(['token' => $this->extend['token']])
                ->update(['update_time' => $time]);
        } else {
            // 对于非永久token，保持原有逻辑
            $expire_time = $time + Config::get('project.token_expire_time');
            Db::name('session')
                ->where(['token' => $this->extend['token']])
                ->update(['update_time' => $time, 'expire_time' => $expire_time]);
        }

        //返回用户信息
        $user_info = Db::name('user')->alias('u')
            ->join('session s', 'u.id=s.user_id')
            ->where(['s.token' => $this->extend['token']])
            ->field('u.*,s.token,s.client')
            ->find();
        return $user_info;
    }
}