@echo off
chcp 65001 >nul
echo ========================================
echo 商品管理增强功能部署脚本
echo ========================================
echo.

:: 设置颜色
set "GREEN=[92m"
set "RED=[91m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "NC=[0m"

:: 检查文件是否存在
echo %BLUE%正在检查增强文件...%NC%

set "files_missing=0"

if not exist "public\static\common\css\goods-spec-enhanced.css" (
    echo %RED%❌ 缺少文件: goods-spec-enhanced.css%NC%
    set /a files_missing+=1
) else (
    echo %GREEN%✅ 找到文件: goods-spec-enhanced.css%NC%
)

if not exist "public\static\common\js\goods-spec-enhanced.js" (
    echo %RED%❌ 缺少文件: goods-spec-enhanced.js%NC%
    set /a files_missing+=1
) else (
    echo %GREEN%✅ 找到文件: goods-spec-enhanced.js%NC%
)

if not exist "public\static\common\js\video-manager-enhanced.js" (
    echo %RED%❌ 缺少文件: video-manager-enhanced.js%NC%
    set /a files_missing+=1
) else (
    echo %GREEN%✅ 找到文件: video-manager-enhanced.js%NC%
)

if not exist "public\static\common\js\form-interaction-enhanced.js" (
    echo %RED%❌ 缺少文件: form-interaction-enhanced.js%NC%
    set /a files_missing+=1
) else (
    echo %GREEN%✅ 找到文件: form-interaction-enhanced.js%NC%
)

if not exist "public\test-goods-enhanced.html" (
    echo %RED%❌ 缺少文件: test-goods-enhanced.html%NC%
    set /a files_missing+=1
) else (
    echo %GREEN%✅ 找到文件: test-goods-enhanced.html%NC%
)

echo.

if %files_missing% gtr 0 (
    echo %RED%❌ 部署失败: 缺少 %files_missing% 个必要文件%NC%
    echo %YELLOW%请确保所有增强文件都已正确创建%NC%
    pause
    exit /b 1
)

:: 检查模板文件修改
echo %BLUE%正在检查模板文件修改...%NC%

if not exist "app\shop\view\goods\goods\add.html" (
    echo %RED%❌ 缺少文件: app\shop\view\goods\goods\add.html%NC%
    set /a files_missing+=1
) else (
    findstr /c:"goods-spec-enhanced.css" "app\shop\view\goods\goods\add.html" >nul
    if errorlevel 1 (
        echo %YELLOW%⚠️  add.html 中未找到 CSS 引用%NC%
    ) else (
        echo %GREEN%✅ add.html CSS 引用正常%NC%
    )
    
    findstr /c:"goods-spec-enhanced.js" "app\shop\view\goods\goods\add.html" >nul
    if errorlevel 1 (
        echo %YELLOW%⚠️  add.html 中未找到 JS 引用%NC%
    ) else (
        echo %GREEN%✅ add.html JS 引用正常%NC%
    )
)

if not exist "app\shop\view\goods\goods\goods_base.html" (
    echo %RED%❌ 缺少文件: app\shop\view\goods\goods\goods_base.html%NC%
) else (
    findstr /c:"videoPreviewArea" "app\shop\view\goods\goods\goods_base.html" >nul
    if errorlevel 1 (
        echo %YELLOW%⚠️  goods_base.html 中未找到视频预览区域%NC%
    ) else (
        echo %GREEN%✅ goods_base.html 视频预览区域正常%NC%
    )
)

if not exist "app\shop\view\goods\goods\goods_spec.html" (
    echo %RED%❌ 缺少文件: app\shop\view\goods\goods\goods_spec.html%NC%
) else (
    findstr /c:"spec-input-field" "app\shop\view\goods\goods\goods_spec.html" >nul
    if errorlevel 1 (
        echo %YELLOW%⚠️  goods_spec.html 中未找到增强输入框类%NC%
    ) else (
        echo %GREEN%✅ goods_spec.html 输入框增强正常%NC%
    )
)

echo.

:: 创建备份
echo %BLUE%正在创建配置备份...%NC%
if not exist "backup" mkdir backup
if not exist "backup\%date:~0,10%" mkdir "backup\%date:~0,10%"

copy "app\shop\view\goods\goods\add.html" "backup\%date:~0,10%\add.html.bak" >nul 2>&1
copy "app\shop\view\goods\goods\goods_base.html" "backup\%date:~0,10%\goods_base.html.bak" >nul 2>&1
copy "app\shop\view\goods\goods\goods_spec.html" "backup\%date:~0,10%\goods_spec.html.bak" >nul 2>&1

echo %GREEN%✅ 备份已创建到 backup\%date:~0,10%\%NC%
echo.

:: 检查权限
echo %BLUE%正在检查文件权限...%NC%
icacls "public\static\common\css\goods-spec-enhanced.css" >nul 2>&1
if errorlevel 1 (
    echo %YELLOW%⚠️  CSS 文件权限可能有问题%NC%
) else (
    echo %GREEN%✅ CSS 文件权限正常%NC%
)

icacls "public\static\common\js\goods-spec-enhanced.js" >nul 2>&1
if errorlevel 1 (
    echo %YELLOW%⚠️  JS 文件权限可能有问题%NC%
) else (
    echo %GREEN%✅ JS 文件权限正常%NC%
)

echo.

:: 生成部署报告
echo %BLUE%正在生成部署报告...%NC%
echo ======================================== > deployment-report.txt
echo 商品管理增强功能部署报告 >> deployment-report.txt
echo 部署时间: %date% %time% >> deployment-report.txt
echo ======================================== >> deployment-report.txt
echo. >> deployment-report.txt
echo 已部署文件: >> deployment-report.txt
echo - public/static/common/css/goods-spec-enhanced.css >> deployment-report.txt
echo - public/static/common/js/goods-spec-enhanced.js >> deployment-report.txt
echo - public/static/common/js/video-manager-enhanced.js >> deployment-report.txt
echo - public/static/common/js/form-interaction-enhanced.js >> deployment-report.txt
echo - public/test-goods-enhanced.html >> deployment-report.txt
echo. >> deployment-report.txt
echo 已修改文件: >> deployment-report.txt
echo - app/shop/view/goods/goods/add.html >> deployment-report.txt
echo - app/shop/view/goods/goods/goods_base.html >> deployment-report.txt
echo - app/shop/view/goods/goods/goods_spec.html >> deployment-report.txt
echo. >> deployment-report.txt
echo 功能特性: >> deployment-report.txt
echo - ✅ 输入框编辑增强 >> deployment-report.txt
echo - ✅ 视频删除管理功能 >> deployment-report.txt
echo - ✅ 页面交互优化 >> deployment-report.txt
echo - ✅ 表单验证增强 >> deployment-report.txt
echo - ✅ 响应式设计优化 >> deployment-report.txt
echo. >> deployment-report.txt

echo %GREEN%✅ 部署报告已生成: deployment-report.txt%NC%
echo.

:: 显示测试说明
echo %BLUE%========================================%NC%
echo %GREEN%🎉 商品管理增强功能部署完成！%NC%
echo %BLUE%========================================%NC%
echo.
echo %YELLOW%📋 接下来的步骤:%NC%
echo.
echo %BLUE%1. 测试功能:%NC%
echo    访问: http://your-domain/test-goods-enhanced.html
echo    进行功能测试验证
echo.
echo %BLUE%2. 清理缓存:%NC%
echo    清理浏览器缓存和服务器缓存
echo    确保新文件被正确加载
echo.
echo %BLUE%3. 生产环境测试:%NC%
echo    在商品添加/编辑页面测试以下功能:
echo    - 输入框是否可正常编辑
echo    - 视频上传后是否显示删除按钮
echo    - 弹窗是否正常显示（不被遮挡）
echo    - 表格操作按钮是否可正常点击
echo.
echo %BLUE%4. 性能监控:%NC%
echo    监控页面加载时间和交互响应
echo    如有性能问题，可调整配置参数
echo.
echo %BLUE%5. 问题反馈:%NC%
echo    如遇到问题，请检查浏览器控制台
echo    开启调试模式获取详细日志
echo.
echo %GREEN%✨ 祝您使用愉快！%NC%
echo.

pause
