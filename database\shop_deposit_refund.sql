-- 在ls_shop_deposit表中添加退款相关字段
ALTER TABLE `ls_shop_deposit`
ADD COLUMN `refund_status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '退款状态：0未申请 1申请中 2已退款 3拒绝退款' AFTER `pay_status`,
ADD COLUMN `refund_apply_time` datetime NULL COMMENT '退款申请时间' AFTER `refund_status`,
ADD COLUMN `refund_reason` varchar(255) NULL COMMENT '退款申请原因' AFTER `refund_apply_time`,
ADD COLUMN `refund_time` datetime NULL COMMENT '退款时间' AFTER `refund_reason`,
ADD COLUMN `refund_remark` varchar(255) NULL COMMENT '退款备注' AFTER `refund_time`,
ADD COLUMN `refund_admin_id` int(11) NULL COMMENT '处理退款的管理员ID' AFTER `refund_remark`,
ADD COLUMN `refund_publicity_start_time` datetime NULL COMMENT '退款公示期开始时间' AFTER `refund_admin_id`,
ADD COLUMN `refund_publicity_end_time` datetime NULL COMMENT '退款公示期结束时间' AFTER `refund_publicity_start_time`;
