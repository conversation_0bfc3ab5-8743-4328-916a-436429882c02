<?php

namespace app\shop\logic\kefu;

use app\common\basics\Logic;
use think\facade\Db;
use app\common\model\kefu\KefuLang;

class KefuLangLogic extends Logic
{
    /**
     * 获取话术列表
     * @param int $shop_id 商家ID
     * @param int $limit 每页数量
     * @param int $page 页码
     * @return array
     */
    public static function lists($shop_id, $limit = 10, $page = 1)
    {
        $list = KefuLang::where(['shop_id'=>$shop_id])->order('sort asc')->paginate([
            'list_rows' => $limit,
            'page'      => $page,
        ]);
       return ['count' => $list->total(), 'lists' => $list->getCollection(),'more'=>is_more($list->total(),$page,$limit)];
    }
    /**
     * @notes 获取话术
     * @param $id
     * @return array|\think\Model|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date 2021/11/29 16:02
     */
    public static function detail(int $shop_id,int $id){
        return KefuLang::where(['id'=>$id,'shop_id'=>$shop_id])->find()->toArray();
    }
    /**
     * 添加话术
     * @param int $shop_id 商家ID
     * @param array $data 话术数据
     * @return bool
     */
    public static function add($shop_id, $data)
    {
        $data['shop_id'] = $shop_id;
        $data['create_time'] = time();
        
        // --- 修改开始: 使用正确的表名 kefu_lang ---
        $result = Db::name('kefu_lang')->insert($data);
        // --- 修改结束 ---
        return $result !== false;
    }
    
    /**
     * 编辑话术
     * @param array $data 话术数据
     * @return bool
     */
    public static function edit($data)
    {
        $id = $data['id'];
        unset($data['id']);
        $data['update_time'] = time();
        
        // --- 修改开始: 使用正确的表名 kefu_lang ---
        $result = Db::name('kefu_lang')
        // --- 修改结束 ---
            ->where('id', $id)
            ->update($data);
            
        return $result !== false;
    }
    
    /**
     * 删除话术
     * @param int $shop_id 商家ID
     * @param int $id 话术ID
     * @return bool
     */
    public static function del($shop_id, $id)
    {
        // --- 修改开始: 使用正确的表名 kefu_lang ---
        $result = Db::name('kefu_lang')
        // --- 修改结束 ---
            ->where([
                ['id', '=', $id],
                ['shop_id', '=', $shop_id]
            ])
            ->delete();
            
        return $result !== false;
    }
    
    /**
     * 获取话术分组
     * @param int $shop_id 商家ID
     * @return array
     */
    public static function getGroups($shop_id)
    {
        $groups = Db::name('shop_kefu_lang_group') // 此处表名可能也需要确认
            ->where('shop_id', $shop_id)
            ->order('id desc')
            ->select()
            ->toArray();
            
        return $groups;
    }
    
    /**
     * 添加话术分组
     * @param array $data 分组数据
     * @return bool
     */
    public static function addGroup($data)
    {
        $data['create_time'] = time();
        
        $result = Db::name('shop_kefu_lang_group')->insert($data); // 此处表名可能也需要确认
        return $result !== false;
    }
}