{layout name="layout2" /}
<style>
    .div-flex {
        display: flex;
        align-items: center;
        justify-content: left;
    }
    .width-160 {
        width: 200px;
    }
    .image {
        height: 60px;
        width: 60px;
        margin-right: 5px;
    }
</style>

<div class="layui-card-body">
    <!--基本信息-->
    <div class="layui-form" lay-filter="layuiadmin-form-order" id="layuiadmin-form-order">
        <input type="hidden" id="article_id" name="id" value="{$detail.id}">

        <div class="layui-form-item">
            <fieldset class="layui-elem-field layui-field-title">
                <legend>商家信息</legend>
            </fieldset>
        </div>

        <div class="layui-form-item div-flex">
            <label class="layui-form-label ">商家名称:</label>
            <div class="width-160">{$detail.name}</div>

        </div>

        <div class="layui-form-item">
            <fieldset class="layui-elem-field layui-field-title">
                <legend>保证金明细</legend>
            </fieldset>
        </div>
        <div class="layui-form-item">
            <div class="layui-tab layui-tab-card" lay-filter="tab-all">
                <ul class="layui-tab-title">
                    <li data-type='comment' class="layui-this">扣除</li>
                    <li data-type='like' >补缴</li>
                </ul>
                <div class="layui-tab-item layui-show">
                    <div class="layui-card-body">
                        <table id="like-table-lists" lay-filter="like-table-lists"></table>
                        <script type="text/html" id="table-userInfo">
                            <div class="layui-inline" style="text-align:left;">
                                <p>用户编号：{{d.sn}}</p>
                                <p>用户昵称：{{d.nickname}}</p>
                            </div>
                        </script>
                    </div>
                </div>
            </div>
        </div>

    </div>
</div>

<script type="text/javascript">
    layui.config({
        version: "{$front_version}",
        base: '/static/lib/'
    }).use(['form', 'table', 'element'], function () {
        var $ = layui.$;
        var table = layui.table;
        var element = layui.element;

        //主图放大
        $(document).on('click', '.image-show', function () {
            var src = $(this).attr('src');
            like.showImg(src, 400);
        });

        //获取列表
        getList('comment');
        //切换列表
        element.on('tab(tab-all)', function (data) {
            var type = $(this).attr('data-type');
            getList(type);
        });

        function getList(type) {
            var cols = [
                {field: 'deposit_change', title: '保证金变动', align: 'center'}
                , {field: 'reason', title: '保证金变动原因', align: 'center'}
                , {field: 'change_date', title: '变动时间', align: 'center'}
            ];
            // if (type === 'comment') {
            //     cols = [
            //         {field: 'user', title: '评论用户', align: 'center', templet: "#table-userInfo"}
            //         , {field: 'comment', title: '评论内容', align: 'center'}
            //         , {field: 'create_time', title: '评价时间', align: 'center'}
            //     ];
            // }
            var id = $('#article_id').val();
            like.tableLists("#like-table-lists", '{:url("shop.Apply/depositdetail")}?type='+ type + '&id=' + id, cols);
        }


    });
</script>