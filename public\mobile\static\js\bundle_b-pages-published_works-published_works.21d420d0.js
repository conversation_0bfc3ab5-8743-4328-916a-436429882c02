(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["bundle_b-pages-published_works-published_works","bundle-pages-apply_refund-apply_refund~bundle-pages-goods_reviews-goods_reviews~bundle-pages-input_e~810d541f"],{"01b0":function(t,e,n){"use strict";n("7a82");var i=n("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=i(n("f07e")),o=i(n("c964"));n("a9e3"),n("d81d"),n("d3b7"),n("14d9"),n("ac1f"),n("00b4"),n("caad6"),n("a434"),n("5319");var r={name:"u-upload",props:{showUploadList:{type:Boolean,default:!0},action:{type:String,default:""},maxCount:{type:[String,Number],default:52},showProgress:{type:Boolean,default:!0},disabled:{type:Boolean,default:!1},imageMode:{type:String,default:"aspectFill"},header:{type:Object,default:function(){return{}}},formData:{type:Object,default:function(){return{}}},name:{type:String,default:"file"},sizeType:{type:Array,default:function(){return["original","compressed"]}},sourceType:{type:Array,default:function(){return["album","camera"]}},previewFullImage:{type:Boolean,default:!0},multiple:{type:Boolean,default:!0},deletable:{type:Boolean,default:!0},maxSize:{type:[String,Number],default:Number.MAX_VALUE},fileList:{type:Array,default:function(){return[]}},uploadText:{type:String,default:"选择图片"},autoUpload:{type:Boolean,default:!0},showTips:{type:Boolean,default:!0},customBtn:{type:Boolean,default:!1},width:{type:[String,Number],default:200},height:{type:[String,Number],default:200},delBgColor:{type:String,default:"#fa3534"},delColor:{type:String,default:"#ffffff"},delIcon:{type:String,default:"close"},toJson:{type:Boolean,default:!0},beforeUpload:{type:Function,default:null},beforeRemove:{type:Function,default:null},limitType:{type:Array,default:function(){return["png","jpg","jpeg","webp","gif","image"]}},index:{type:[Number,String],default:""}},mounted:function(){},data:function(){return{lists:[],isInCount:!0,uploading:!1}},watch:{fileList:{immediate:!0,handler:function(t){var e=this;t.map((function(t){var n=e.lists.some((function(e){return e.url==t.url}));1==e.maxCount&&1===e.lists.length||void 0!==t.url&&!n&&e.lists.push({url:t.url,error:!1,progress:100})}))}},lists:function(t){this.$emit("on-list-change",t,this.index)}},methods:{clear:function(){this.lists=[]},reUpload:function(){this.uploadFile()},selectFile:function(){var t=this;if(!this.disabled){this.name;var e=this.maxCount,n=this.multiple,i=this.maxSize,a=this.sizeType,o=this.lists,r=(this.camera,this.compressed,this.maxDuration,this.sourceType),s=null,c=e-o.length;s=new Promise((function(t,e){uni.chooseImage({count:n?c>9?9:c:1,sourceType:r,sizeType:a,success:t,fail:e})})),s.then((function(a){var r=t.lists.length;a.tempFiles.map((function(a,r){if(t.checkFileExt(a)&&(n||!(r>=1)))if(a.size>i)t.$emit("on-oversize",a,t.lists,t.index),t.showToast("超出允许的文件大小");else{if(e<=o.length)return t.$emit("on-exceed",a,t.lists,t.index),void t.showToast("超出最大允许的文件个数");o.push({url:a.path,progress:0,error:!1,file:a})}})),t.$emit("on-choose-complete",t.lists,t.index),t.autoUpload&&t.uploadFile(r)})).catch((function(e){t.$emit("on-choose-fail",e)}))}},showToast:function(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];(this.showTips||e)&&uni.showToast({title:t,icon:"none"})},upload:function(){this.uploadFile()},retry:function(t){this.lists[t].progress=0,this.lists[t].error=!1,this.lists[t].response=null,uni.showLoading({title:"重新上传"}),this.uploadFile(t)},uploadFile:function(){var t=arguments,e=this;return(0,o.default)((0,a.default)().mark((function n(){var i,o,r;return(0,a.default)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(i=t.length>0&&void 0!==t[0]?t[0]:0,!e.disabled){n.next=3;break}return n.abrupt("return");case 3:if(!e.uploading){n.next=5;break}return n.abrupt("return");case 5:if(!(i>=e.lists.length)){n.next=8;break}return e.$emit("on-uploaded",e.lists,e.index),n.abrupt("return");case 8:if(100!=e.lists[i].progress){n.next=11;break}return 0==e.autoUpload&&e.uploadFile(i+1),n.abrupt("return");case 11:if(!e.beforeUpload||"function"!==typeof e.beforeUpload){n.next=22;break}if(o=e.beforeUpload.bind(e.$u.$parent.call(e))(i,e.lists),!o||"function"!==typeof o.then){n.next=18;break}return n.next=16,o.then((function(t){})).catch((function(t){return e.uploadFile(i+1)}));case 16:n.next=22;break;case 18:if(!1!==o){n.next=22;break}return n.abrupt("return",e.uploadFile(i+1));case 22:if(e.action){n.next=25;break}return e.showToast("请配置上传地址",!0),n.abrupt("return");case 25:e.lists[i].error=!1,e.uploading=!0,r=uni.uploadFile({url:e.action,filePath:e.lists[i].url,name:e.name,formData:e.formData,header:e.header,success:function(t){var n=e.toJson&&e.$u.test.jsonString(t.data)?JSON.parse(t.data):t.data;[200,201,204].includes(t.statusCode)?(e.lists[i].response=n,e.lists[i].progress=100,e.lists[i].error=!1,e.$emit("on-success",n,i,e.lists,e.index)):e.uploadError(i,n)},fail:function(t){e.uploadError(i,t)},complete:function(t){uni.hideLoading(),e.uploading=!1,e.uploadFile(i+1),e.$emit("on-change",t,i,e.lists,e.index)}}),r.onProgressUpdate((function(t){t.progress>0&&(e.lists[i].progress=t.progress,e.$emit("on-progress",t,i,e.lists,e.index))}));case 29:case"end":return n.stop()}}),n)})))()},uploadError:function(t,e){this.lists[t].progress=0,this.lists[t].error=!0,this.lists[t].response=null,this.$emit("on-error",e,t,this.lists,this.index),this.showToast("上传失败，请重试")},deleteItem:function(t){var e=this;uni.showModal({title:"提示",content:"您确定要删除此项吗？",success:function(){var n=(0,o.default)((0,a.default)().mark((function n(i){var o;return(0,a.default)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(!i.confirm){n.next=12;break}if(!e.beforeRemove||"function"!==typeof e.beforeRemove){n.next=11;break}if(o=e.beforeRemove.bind(e.$u.$parent.call(e))(t,e.lists),!o||"function"!==typeof o.then){n.next=8;break}return n.next=6,o.then((function(n){e.handlerDeleteItem(t)})).catch((function(t){e.showToast("已终止移除")}));case 6:n.next=9;break;case 8:!1===o?e.showToast("已终止移除"):e.handlerDeleteItem(t);case 9:n.next=12;break;case 11:e.handlerDeleteItem(t);case 12:case"end":return n.stop()}}),n)})));return function(t){return n.apply(this,arguments)}}()})},handlerDeleteItem:function(t){this.lists[t].process<100&&this.lists[t].process>0&&"undefined"!=typeof this.lists[t].uploadTask&&this.lists[t].uploadTask.abort(),this.lists.splice(t,1),this.$forceUpdate(),this.$emit("on-remove",t,this.lists,this.index),this.showToast("移除成功")},remove:function(t){t>=0&&t<this.lists.length&&(this.lists.splice(t,1),this.$emit("on-list-change",this.lists,this.index))},doPreviewImage:function(t,e){var n=this;if(this.previewFullImage){var i=this.lists.map((function(t){return t.url||t.path}));uni.previewImage({urls:i,current:t,success:function(){n.$emit("on-preview",t,n.lists,n.index)},fail:function(){uni.showToast({title:"预览图片失败",icon:"none"})}})}},checkFileExt:function(t){var e,n;return n=t.name.replace(/.+\./,"").toLowerCase(),e=this.limitType.some((function(t){return t.toLowerCase()===n})),e||this.showToast("不允许选择".concat(n,"格式的文件")),e}}};e.default=r},"05fc":function(t,e,n){"use strict";n.r(e);var i=n("a744"),a=n.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);e["default"]=a.a},"0a04":function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"c",(function(){return o})),n.d(e,"a",(function(){return i}));var i={uUpload:n("1697").default,uIcon:n("6976").default,uImage:n("f919").default},a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{staticClass:"container",style:{"padding-bottom":t.pageHeight+"px"}},[n("v-uni-view",{staticClass:"main"},[n("v-uni-view",{staticClass:"uploader-container flex wrap"},[n("u-upload",{ref:"upload",attrs:{action:t.action,header:{token:t.token,version:t.version},deletable:!0,"max-count":9,multiple:!0,"custom-btn":!0,width:160,height:160,fileList:t.fileList,"show-progress":!1},on:{"on-change":function(e){arguments[0]=e=t.$handleEvent(e),t.change.apply(void 0,arguments)},"on-remove":function(e){arguments[0]=e=t.$handleEvent(e),t.remove.apply(void 0,arguments)}}},[n("v-uni-view",{staticClass:"uplader-upload",attrs:{slot:"addBtn","hover-class":"slot-btn__hover","hover-stay-time":"150"},slot:"addBtn"},[n("u-icon",{attrs:{size:"56",color:"#b1b1b1",name:"camera"}})],1)],1)],1),n("v-uni-view",{staticClass:"content"},[n("v-uni-textarea",{attrs:{id:"",maxlength:"999",placeholder:"说说你的购物体验和心得，我们都很期待呢~","disable-default-padding":!0,"show-confirm-bar":!1},on:{focus:function(e){arguments[0]=e=t.$handleEvent(e),t.handleFocus.apply(void 0,arguments)},blur:function(e){arguments[0]=e=t.$handleEvent(e),t.pageHeight=0}},model:{value:t.formData.content,callback:function(e){t.$set(t.formData,"content",e)},expression:"formData.content"}}),n("v-uni-view",{staticClass:"muted text-right"},[t._v(t._s(t.formData.content.length)+"/999")])],1),n("v-uni-view",{staticClass:"item flex row-between recommend",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.showRecommend.apply(void 0,arguments)}}},[n("v-uni-view",{staticClass:"flex nr normal"},[n("v-uni-image",{staticClass:"image m-r-10",attrs:{src:"/bundle_b/static/icon_recommend.png"}}),0===t.formData.shop.length&&0===t.formData.goods.length?n("v-uni-text",[t._v("宝贝/店铺")]):t._e(),0!==t.formData.shop.length?n("v-uni-text",[t._v("店铺")]):t._e(),0!==t.formData.goods.length?n("v-uni-text",[t._v("宝贝")]):t._e()],1),n("v-uni-view",{staticClass:"nr flex"},[0===t.formData.shop.length&&0===t.formData.goods.length?n("v-uni-text",{staticClass:"muted m-r-10"},[t._v("选择购买过的商品")]):t._e(),0!==t.formData.goods.length?[t._l(t.formData.goods,(function(e,i){return[i<=2?n("u-image",{key:i+"_0",staticClass:"m-l-6",attrs:{src:e.image,width:"58",height:"58"}}):t._e()]}))]:t._e(),0!==t.formData.shop.length?[t._l(t.formData.shop,(function(e,i){return[i<=2?n("u-image",{key:i+"_0",staticClass:"m-l-6",attrs:{src:e.logo,width:"58",height:"58"}}):t._e()]}))]:t._e(),n("u-icon",{staticClass:"m-l-10",attrs:{name:"arrow-right",size:"22",color:"#707070"}})],2)],1),n("v-uni-view",{staticClass:"item flex row-between topic",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.showTopicPopup=!0}}},[n("v-uni-view",{staticClass:"flex nr normal"},[n("v-uni-image",{staticClass:"image m-r-10",attrs:{src:"/bundle_b/static/icon_topic.png"}}),n("v-uni-text",[t._v("话题")])],1),n("v-uni-view",{staticClass:"nr flex"},[t.formData.topic_id?n("v-uni-view",{staticClass:"tags primary-tags m-r-10"},[n("v-uni-text",[t._v("#"+t._s(t.formData.topic_id.name))])],1):t._e(),n("u-icon",{attrs:{name:"arrow-right",size:"22",color:"#707070"}})],1)],1),n("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:!t.formData.topic_id,expression:"!formData.topic_id"}],staticClass:"tags m-t-10 gary-tags"},[t._l(t.recommendTopic,(function(e){return[n("v-uni-text",{key:e.id+"_0",on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.handleTopic(e)}}},[t._v("#"+t._s(e.name))])]}))],2)],1),n("v-uni-view",{staticClass:"footer"},[n("v-uni-button",{staticClass:"br60 white btn lg",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onSubmit.apply(void 0,arguments)}}},[t._v(t._s(t.formData.id?"编辑":"发布"))])],1),n("recommend",{attrs:{shop:t.formData.shop,goods:t.formData.goods},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.handleRecommend.apply(void 0,arguments)}},model:{value:t.showRecommendPopup,callback:function(e){t.showRecommendPopup=e},expression:"showRecommendPopup"}}),n("topic",{on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.handleTopic.apply(void 0,arguments)}},model:{value:t.showTopicPopup,callback:function(e){t.showTopicPopup=e},expression:"showTopicPopup"}})],1)},o=[]},"0b4b":function(t,e,n){"use strict";var i=n("815f"),a=n.n(i);a.a},"0caa":function(t,e,n){"use strict";n.r(e);var i=n("5205"),a=n.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);e["default"]=a.a},"0e2d":function(t,e,n){var i=n("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.bb[data-v-e89802c2]{border-bottom:1px solid #f6f6f6}.content-wrapper[data-v-e89802c2]{height:%?900?%}.recommend-footer[data-v-e89802c2]{width:100%;height:%?100?%;padding:0 %?30?%;box-shadow:0 %?-4?% %?10?% rgba(0,0,0,.1)}.recommend-footer .btn[data-v-e89802c2]{width:100%;height:%?82?%;font-size:%?32?%;line-height:%?82?%}',""]),t.exports=e},"0f29":function(t,e,n){"use strict";var i=n("a616"),a=n.n(i);a.a},"130e":function(t,e,n){"use strict";n("7a82");var i=n("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=i(n("f07e")),o=i(n("c964")),r=i(n("d0ff"));n("4de4"),n("d3b7"),n("c740"),n("99af"),n("a434");var s=n("2625"),c=i(n("53f3")),u=i(n("9ba6")),l={mixins:[c.default,u.default],props:{value:{type:[Object,Array]}},data:function(){return{keyword:"",hideRight:!0,height:"",upOption:{empty:{icon:"/static/images/goods_null.png",tip:"暂无店铺 ",fixed:!0,top:"200rpx"}},tabsList:[{label:"全部",type:"all"},{label:"已购店铺",type:"buy"}],currentTabs:0,lists:[],selectData:[]}},computed:{getCurrentSelect:function(){var t=this;return function(e){return t.selectData.filter((function(t){return t.id==e.id})).length?"/bundle_b/static/icon_select.png":"/bundle_b/static/icon_unselect.png"}}},watch:{value:function(t){this.selectData=t}},methods:{handleCancel:function(){this.keyword="",this.mescroll.resetUpScroll()},changeTabs:function(t){this.currentTabs=t,uni.showLoading({title:"加载中"}),this.mescroll.resetUpScroll()},selectCurrentGoods:function(t){var e=this.selectData.findIndex((function(e){return e.id==t.id}));-1===e?this.selectData=[].concat((0,r.default)(this.selectData),[t]):this.selectData.splice(e,1),this.$emit("change",this.selectData)},upCallback:function(t){var e=this;return(0,o.default)((0,a.default)().mark((function n(){var i,o,c;return(0,a.default)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:i=e.currentTabs,o=t.num,c=t.size,(0,s.getCommunityShop)({keyword:e.keyword,type:e.tabsList[i].type,page_no:o,page_size:c}).then((function(t){uni.hideLoading(),(1==o||e.keyword)&&(e.lists=[]);var n=!!t.data.more;e.lists=[].concat((0,r.default)(e.lists),(0,r.default)(t.data.list)),e.mescroll.endSuccess(t.data.list.length,n)})).catch((function(t){e.mescroll.endErr(),uni.hideLoading()}));case 4:case"end":return n.stop()}}),n)})))()}}};e.default=l},"140e4":function(t,e,n){"use strict";n("7a82");var i=n("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=i(n("f07e")),o=i(n("c964")),r=n("2625"),s=i(n("53f3")),c=i(n("9ba6")),u={mixins:[s.default,c.default],name:"topic",props:{value:{type:Boolean}},data:function(){return{keyword:"",hideRight:!0,menuCurrentIndex:0,topicData:[]}},computed:{show:{get:function(){return this.value},set:function(t){var e=this;this.$nextTick((function(){e.mescroll.resetUpScroll()})),this.$emit("input",t)}}},methods:{handleCancel:function(){this.keyword="",this.mescroll.resetUpScroll()},selectMenu:function(t){this.menuCurrentIndex=t},selectTopic:function(t){this.$emit("input",!1),this.$emit("change",t)},unSelectTopic:function(){this.$emit("input",!1),this.$emit("change","")},upCallback:function(t){var e=this;return(0,o.default)((0,a.default)().mark((function t(){return(0,a.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:(0,r.getCommunityTopicLists)({name:e.keyword}).then((function(t){e.topicData=t.data,e.mescroll.endSuccess(10,0)})).catch((function(t){e.mescroll.endErr()}));case 1:case"end":return t.stop()}}),t)})))()}}};e.default=u},1697:function(t,e,n){"use strict";n.r(e);var i=n("6019"),a=n("ed04");for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);n("fa19");var r=n("f0c5"),s=Object(r["a"])(a["default"],i["b"],i["c"],!1,null,"e9cc6eb0",null,!1,i["a"],void 0);e["default"]=s.exports},"1e2e":function(t,e,n){var i=n("3096");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var a=n("4f06").default;a("ccafd518",i,!0,{sourceMap:!1,shadowMode:!1})},"23a9":function(t,e,n){"use strict";n.r(e);var i=n("140e4"),a=n.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);e["default"]=a.a},2625:function(t,e,n){"use strict";n("7a82");var i=n("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.apiCommunityAdd=function(t){return a.default.post("community/addArticle",t)},e.apiCommunityClearSearchHistory=function(){return a.default.post("community_search/clear")},e.apiCommunityCommentAdd=function(t){return a.default.post("community_comment/add",t)},e.apiCommunityCommentLike=function(t){return a.default.post("community/giveLike",t)},e.apiCommunityDel=function(t){return a.default.post("community/delArticle",t)},e.apiCommunityEdit=function(t){return a.default.post("community/editArticle",t)},e.apiCommunityFollow=function(t){return a.default.post("community/follow",t)},e.apiCommunitySetSetting=function(t){return a.default.post("community_user/setSetting",t)},e.getCommunityArticleLists=function(t){return a.default.get("community/articleLists",{params:t})},e.getCommunityCate=function(){return a.default.get("community/cate")},e.getCommunityCommentChildLists=function(t){return a.default.get("community_comment/commentChild",{params:t})},e.getCommunityCommentLists=function(t){return a.default.get("community_comment/lists",{params:t})},e.getCommunityDetail=function(t){return a.default.get("community/detail",{params:t})},e.getCommunityFollow=function(t){return a.default.get("community/followArticle",{params:t})},e.getCommunityGoods=function(t){return a.default.get("community/goods",{params:t})},e.getCommunityGoodsLists=function(t){return a.default.get("community/relationGoods",{params:t})},e.getCommunityLikeLists=function(t){return a.default.get("community/likeLists",{params:t})},e.getCommunityRecommendTopic=function(){return a.default.get("community/recommendTopic")},e.getCommunitySearchHistory=function(){return a.default.get("community_search/lists")},e.getCommunitySetting=function(){return a.default.get("community_user/getSetting")},e.getCommunityShop=function(t){return a.default.get("community/shop",{params:t})},e.getCommunityShopLists=function(t){return a.default.get("community/relationShop",{params:t})},e.getCommunityTopicArticle=function(t){return a.default.get("community/topicArticle",{params:t})},e.getCommunityTopicLists=function(t){return a.default.get("community/topicLists",{params:t})},e.getCommunityUserCenter=function(t){return a.default.get("community_user/center",{params:t})},e.getCommunityWorksLists=function(t){return a.default.get("community/worksLists",{params:t})};var a=i(n("2774"))},"2faf":function(t,e,n){"use strict";n.r(e);var i=n("3c99"),a=n("bdee");for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);n("f396");var r=n("f0c5"),s=Object(r["a"])(a["default"],i["b"],i["c"],!1,null,"d1da54e0",null,!1,i["a"],void 0);e["default"]=s.exports},"2feb":function(t,e,n){"use strict";n("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("a9e3");var i={name:"u-line-progress",props:{round:{type:Boolean,default:!0},type:{type:String,default:""},activeColor:{type:String,default:"#19be6b"},inactiveColor:{type:String,default:"#ececec"},percent:{type:Number,default:0},showPercent:{type:Boolean,default:!0},height:{type:[Number,String],default:28},striped:{type:Boolean,default:!1},stripedActive:{type:Boolean,default:!1}},data:function(){return{}},computed:{progressStyle:function(){var t={};return t.width=this.percent+"%",this.activeColor&&(t.backgroundColor=this.activeColor),t}},methods:{}};e.default=i},3096:function(t,e,n){var i=n("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.u-image[data-v-1bf07c9a]{position:relative;transition:opacity .5s ease-in-out}.u-image__image[data-v-1bf07c9a]{width:100%;height:100%}.u-image__loading[data-v-1bf07c9a], .u-image__error[data-v-1bf07c9a]{position:absolute;top:0;left:0;width:100%;height:100%;display:flex;flex-direction:row;align-items:center;justify-content:center;background-color:#f3f4f6;color:#909399;font-size:%?46?%}',""]),t.exports=e},"38d3":function(t,e,n){"use strict";n.r(e);var i=n("d5c8"),a=n("507a");for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);n("6494");var r=n("f0c5"),s=Object(r["a"])(a["default"],i["b"],i["c"],!1,null,"6e515196",null,!1,i["a"],void 0);e["default"]=s.exports},"3ab4":function(t,e,n){"use strict";n.r(e);var i=n("4f01"),a=n.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);e["default"]=a.a},"3c99":function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"c",(function(){return o})),n.d(e,"a",(function(){return i}));var i={uSearch:n("5744").default,mescrollUni:n("5403").default,uImage:n("f919").default,priceFormat:n("a272").default},a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{staticClass:"goods-box"},[n("v-uni-view",{staticClass:"search"},[n("u-search",{attrs:{hideRight:!0,"show-action":!0,"action-text":"取消",animation:!0,placeholder:"请输入搜索内容",height:"64"},on:{focus:function(e){arguments[0]=e=t.$handleEvent(e),t.hideRight=!1},blur:function(e){arguments[0]=e=t.$handleEvent(e),t.hideRight=!0},custom:function(e){arguments[0]=e=t.$handleEvent(e),t.handleCancel.apply(void 0,arguments)},change:function(e){arguments[0]=e=t.$handleEvent(e),t.mescroll.resetUpScroll()}},model:{value:t.keyword,callback:function(e){t.keyword=e},expression:"keyword"}})],1),n("v-uni-view",{staticClass:"tab-control"},t._l(t.tabsList,(function(e,i){return n("v-uni-view",{key:i,class:{active:t.currentTabs==i},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.changeTabs(i)}}},[t._v(t._s(e.label))])})),1),n("mescroll-uni",{ref:"mescrollRef",attrs:{top:"0",height:"620rpx",down:t.downOption,up:t.upOption},on:{init:function(e){arguments[0]=e=t.$handleEvent(e),t.mescrollInit.apply(void 0,arguments)},down:function(e){arguments[0]=e=t.$handleEvent(e),t.downCallback.apply(void 0,arguments)},up:function(e){arguments[0]=e=t.$handleEvent(e),t.upCallback.apply(void 0,arguments)}}},[t._l(t.lists,(function(e,i){return[n("v-uni-view",{key:i+"_0",staticClass:"goods-item flex row-between",on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.selectCurrentGoods(e)}}},[n("u-image",{attrs:{src:e.image,width:"160",height:"160"}}),n("v-uni-view",{staticClass:"m-l-20 flex-1"},[n("v-uni-view",{staticClass:"goods-name line-1 m-b-12 nr normal"},[t._v(t._s(e.goods_name))]),n("v-uni-view",{staticClass:"m-b-16 muted sm"},[t._v(t._s(e.shop_name))]),n("price-format",{attrs:{"subscript-size":32,"first-size":32,"second-size":32,price:e.goods_price,color:t.colorConfig.primary}})],1),n("v-uni-image",{attrs:{src:t.getCurrentSelect(e)}})],1)]}))],2)],1)},o=[]},"3f30":function(t,e,n){"use strict";n.r(e);var i=n("4219"),a=n.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);e["default"]=a.a},4219:function(t,e,n){"use strict";n("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("a9e3");var i={name:"u-image",props:{src:{type:String,default:""},mode:{type:String,default:"aspectFill"},width:{type:[String,Number],default:"100%"},height:{type:[String,Number],default:"auto"},shape:{type:String,default:"square"},borderRadius:{type:[String,Number],default:0},lazyLoad:{type:Boolean,default:!0},showMenuByLongpress:{type:Boolean,default:!0},loadingIcon:{type:String,default:"photo"},errorIcon:{type:String,default:"error-circle"},showLoading:{type:Boolean,default:!0},showError:{type:Boolean,default:!0},fade:{type:Boolean,default:!0},webp:{type:Boolean,default:!1},duration:{type:[String,Number],default:500},bgColor:{type:String,default:"#f3f4f6"}},data:function(){return{isError:!1,loading:!0,opacity:1,durationTime:this.duration,backgroundStyle:{}}},watch:{src:{immediate:!0,handler:function(t){t?this.isError=!1:(this.isError=!0,this.loading=!1)}}},computed:{wrapStyle:function(){var t={};return t.width=this.$u.addUnit(this.width),t.height=this.$u.addUnit(this.height),t.borderRadius="circle"==this.shape?"50%":this.$u.addUnit(this.borderRadius),t.overflow=this.borderRadius>0?"hidden":"visible",this.fade&&(t.opacity=this.opacity,t.transition="opacity ".concat(Number(this.durationTime)/1e3,"s ease-in-out")),t}},methods:{onClick:function(){this.$emit("click")},onErrorHandler:function(t){this.loading=!1,this.isError=!0,this.$emit("error",t)},onLoadHandler:function(){var t=this;if(this.loading=!1,this.isError=!1,this.$emit("load"),!this.fade)return this.removeBgColor();this.opacity=0,this.durationTime=0,setTimeout((function(){t.durationTime=t.duration,t.opacity=1,setTimeout((function(){t.removeBgColor()}),t.durationTime)}),50)},removeBgColor:function(){this.backgroundStyle={backgroundColor:"transparent"}}}};e.default=i},"4b4b":function(t,e,n){var i=n("6ca1");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var a=n("4f06").default;a("31d0bf01",i,!0,{sourceMap:!1,shadowMode:!1})},"4be1":function(t,e,n){var i=n("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.u-upload[data-v-e9cc6eb0]{display:flex;flex-direction:row;flex-wrap:wrap;align-items:center}.u-list-item[data-v-e9cc6eb0]{width:%?200?%;height:%?200?%;margin:%?9?%;background:#f4f5f6;position:relative;display:flex;align-items:center;justify-content:center}.u-preview-wrap[data-v-e9cc6eb0]{border:1px solid #ebecee}.u-add-wrap[data-v-e9cc6eb0]{flex-direction:column;color:#606266;font-size:%?26?%}.u-add-tips[data-v-e9cc6eb0]{margin-top:%?20?%;line-height:%?40?%}.u-add-wrap__hover[data-v-e9cc6eb0]{background-color:#ebecee}.u-preview-image[data-v-e9cc6eb0]{display:block;width:100%;height:100%}.u-delete-icon[data-v-e9cc6eb0]{position:absolute;top:%?-15?%;right:%?-15?%;z-index:10;background-color:#fa3534;border-radius:%?100?%;width:%?34?%;height:%?34?%;display:flex;flex-direction:row;align-items:center;justify-content:center}.u-icon[data-v-e9cc6eb0]{display:flex;flex-direction:row;align-items:center;justify-content:center}.u-progress[data-v-e9cc6eb0]{position:absolute;bottom:%?10?%;left:%?8?%;right:%?8?%;z-index:9;width:auto}.u-error-btn[data-v-e9cc6eb0]{color:#fff;background-color:#fa3534;font-size:%?20?%;padding:4px 0;text-align:center;position:absolute;bottom:0;left:0;right:0;z-index:9;line-height:1}',""]),t.exports=e},"4d5e":function(t,e,n){var i=n("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */uni-page-body[data-v-78acf6de]{background-color:#fff}body.?%PAGE?%[data-v-78acf6de]{background-color:#fff}.container .main[data-v-78acf6de]{padding:%?20?%;background-color:#fff}.container .main .uploader-container .uplader-upload[data-v-78acf6de]{position:relative;width:%?160?%;height:%?160?%;line-height:%?160?%;text-align:center;margin:%?11?%;border:2px solid #f0f0f0;border-radius:%?14?%;background-color:#f0f0f0}.container .main .uploader-container .uplader-upload > uni-view[data-v-78acf6de]{color:#b1b1b1}.container .main .content[data-v-78acf6de]{padding:%?20?%;margin:%?10?% 0;border-radius:%?10?%}.container .main .content uni-textarea[data-v-78acf6de]{width:100%;min-height:%?230?%}.container .main .item[data-v-78acf6de]{height:%?88?%}.container .main .item .image[data-v-78acf6de]{width:%?40?%;height:%?40?%}.container .main .recommend[data-v-78acf6de]{border-bottom:1px solid #f2f2f2}.container .main .tags uni-text[data-v-78acf6de]{display:inline-block;margin-bottom:%?10?%;margin-right:%?20?%;border-radius:%?26?%;padding:%?8?% %?24?%}.container .main .primary-tags uni-text[data-v-78acf6de]{color:#ff2c3c;margin-bottom:0;background:rgba(255,44,60,.1)}.container .main .gary-tags uni-text[data-v-78acf6de]{color:#666;background:#f4f4f4}.container .footer[data-v-78acf6de]{left:0;bottom:%?50?%;width:100%;padding:0 %?24?%;position:fixed}.container .footer .btn[data-v-78acf6de]{height:%?84?%;line-height:%?84?%;background-color:rgba(255,44,60,.8)}',""]),t.exports=e},"4f01":function(t,e,n){"use strict";n("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("a9e3"),n("acd8");var i={data:function(){return{priceSlice:{}}},components:{},props:{firstSize:{type:[String,Number],default:28},secondSize:{type:[String,Number],default:28},color:{type:String},weight:{type:[String,Number],default:400},price:{type:[String,Number],default:""},showSubscript:{type:Boolean,default:!0},subscriptSize:{type:[String,Number],default:28},lineThrough:{type:Boolean,default:!1}},created:function(){this.priceFormat()},watch:{price:function(t){this.priceFormat()}},methods:{priceFormat:function(){var t=this.price,e={};null!==t&&""!==t&&void 0!==t?(t=parseFloat(t),t=String(t).split("."),e.first=t[0],e.second=t[1],this.priceSlice=e):this.priceSlice={first:0}}}};e.default=i},"507a":function(t,e,n){"use strict";n.r(e);var i=n("130e"),a=n.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);e["default"]=a.a},5205:function(t,e,n){"use strict";n("7a82");var i=n("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=i(n("2faf")),o=i(n("38d3")),r={name:"recommend",components:{Goods:a.default,Shop:o.default},props:{value:{type:Boolean},goods:{type:[Object,Array]},shop:{type:[Object,Array]}},data:function(){return{list:[{name:"宝贝"},{name:"店铺"}],active:0,goodsData:[],shopData:[],isAsync:!0}},computed:{show:{get:function(){return this.value},set:function(t){this.$emit("input",t)}}},watch:{goods:function(t){this.active=0,this.goodsData=t,console.log(this.goodsData)},shop:function(t){this.active=1,this.shopData=t}},methods:{changeTabs:function(t){if(0!=this.goodsData.length||0!=this.shopData.length)return this.$toast({title:"不能同时选择宝贝/店铺"});this.isAsync=!1,this.active=t,this.isAsync=!0},handleGoods:function(t){this.goodsData=t},handleShop:function(t){this.shopData=t},confirm:function(){0==this.active?this.$emit("change",{type:0,data:this.goodsData}):this.$emit("change",{type:1,data:this.shopData}),this.$emit("input",!1)}}};e.default=r},5404:function(t,e,n){"use strict";var i=n("8962"),a=n.n(i);a.a},5744:function(t,e,n){"use strict";n.r(e);var i=n("b413"),a=n("f9f3");for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);n("f5a5");var r=n("f0c5"),s=Object(r["a"])(a["default"],i["b"],i["c"],!1,null,"3c66e606",null,!1,i["a"],void 0);e["default"]=s.exports},"574d":function(t,e,n){"use strict";n("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("a9e3");var i={name:"u-search",props:{shape:{type:String,default:"round"},bgColor:{type:String,default:"#f2f2f2"},placeholder:{type:String,default:"请输入关键字"},clearabled:{type:Boolean,default:!0},focus:{type:Boolean,default:!1},showAction:{type:Boolean,default:!0},actionStyle:{type:Object,default:function(){return{}}},actionText:{type:String,default:"搜索"},inputAlign:{type:String,default:"left"},disabled:{type:Boolean,default:!1},animation:{type:Boolean,default:!1},borderColor:{type:String,default:"none"},value:{type:String,default:""},height:{type:[Number,String],default:64},inputStyle:{type:Object,default:function(){return{}}},maxlength:{type:[Number,String],default:"-1"},searchIconColor:{type:String,default:""},color:{type:String,default:"#606266"},placeholderColor:{type:String,default:"#909399"},margin:{type:String},searchIcon:{type:String,default:"search"},wrapBgColor:{type:String,default:"#fff"},hideRight:{type:Boolean,default:!1}},data:function(){return{keyword:"",showClear:!1,show:!1,focused:this.focus}},watch:{keyword:function(t){this.$emit("input",t),this.$emit("change",t)},value:{immediate:!0,handler:function(t){this.keyword=t}}},computed:{showActionBtn:function(){return!(this.animation||!this.showAction)},borderStyle:function(){return this.borderColor?"1px solid ".concat(this.borderColor):"none"}},methods:{inputChange:function(t){this.keyword=t.detail.value},clear:function(){var t=this;this.keyword="",this.$nextTick((function(){t.$emit("clear")}))},search:function(t){this.$emit("search",t.detail.value);try{uni.hideKeyboard()}catch(t){}},custom:function(){this.$emit("custom",this.keyword);try{uni.hideKeyboard()}catch(t){}},getFocus:function(){this.focused=!0,this.animation&&this.showAction&&(this.show=!0),this.$emit("focus",this.keyword)},blur:function(){var t=this;setTimeout((function(){t.focused=!1}),100),this.show=!1,this.$emit("blur",this.keyword)},clickHandler:function(){this.disabled&&this.$emit("click")}}};e.default=i},6019:function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"c",(function(){return o})),n.d(e,"a",(function(){return i}));var i={uIcon:n("6976").default,uLineProgress:n("df7e").default},a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return t.disabled?t._e():n("v-uni-view",{staticClass:"u-upload"},[t._l(t.lists,(function(e,i){return t.showUploadList?n("v-uni-view",{key:i,staticClass:"u-list-item u-preview-wrap",style:{width:t.$u.addUnit(t.width),height:t.$u.addUnit(t.height)}},[t.deletable?n("v-uni-view",{staticClass:"u-delete-icon",style:{background:t.delBgColor},on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.deleteItem(i)}}},[n("u-icon",{staticClass:"u-icon",attrs:{name:t.delIcon,size:"20",color:t.delColor}})],1):t._e(),t.showProgress&&e.progress>0&&!e.error?n("u-line-progress",{staticClass:"u-progress",attrs:{"show-percent":!1,height:"16",percent:e.progress}}):t._e(),e.error?n("v-uni-view",{staticClass:"u-error-btn",on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.retry(i)}}},[t._v("点击重试")]):t._e(),e.isImage?t._e():n("v-uni-image",{staticClass:"u-preview-image",attrs:{src:e.url||e.path,mode:t.imageMode},on:{click:function(n){n.stopPropagation(),arguments[0]=n=t.$handleEvent(n),t.doPreviewImage(e.url||e.path,i)}}})],1):t._e()})),t._t("file",null,{file:t.lists}),t.maxCount>t.lists.length?n("v-uni-view",{staticStyle:{display:"inline-block"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.selectFile.apply(void 0,arguments)}}},[t._t("addBtn"),t.customBtn?t._e():n("v-uni-view",{staticClass:"u-list-item u-add-wrap",style:{width:t.$u.addUnit(t.width),height:t.$u.addUnit(t.height)},attrs:{"hover-class":"u-add-wrap__hover","hover-stay-time":"150"}},[n("u-icon",{staticClass:"u-add-btn",attrs:{name:"plus",size:"40"}}),n("v-uni-view",{staticClass:"u-add-tips"},[t._v(t._s(t.uploadText))])],1)],2):t._e()],2)},o=[]},"62ed":function(t,e,n){"use strict";n("7a82");var i=n("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=i(n("f07e")),o=i(n("c964")),r=i(n("d0ff"));n("4de4"),n("d3b7"),n("c740"),n("99af"),n("a434");var s=n("2625"),c=i(n("53f3")),u=i(n("9ba6")),l={mixins:[c.default,u.default],props:{value:{type:[Object,Array]}},data:function(){return{keyword:"",hideRight:!0,height:"",upOption:{empty:{icon:"/static/images/goods_null.png",tip:"暂无商品！",fixed:!0,top:"0"}},tabsList:[{label:"全部",type:"all"},{label:"已购宝贝",type:"buy"}],currentTabs:0,lists:[],selectData:[]}},computed:{getCurrentSelect:function(){var t=this;return function(e){return t.selectData.filter((function(t){return t.goods_id==e.goods_id||t.id==e.goods_id})).length?"/bundle_b/static/icon_select.png":"/bundle_b/static/icon_unselect.png"}}},watch:{value:{handler:function(t){console.log(t),this.selectData=t},immediate:!0}},methods:{handleCancel:function(){this.keyword="",this.mescroll.resetUpScroll()},changeTabs:function(t){this.currentTabs=t,uni.showLoading({title:"加载中"}),this.mescroll.resetUpScroll()},selectCurrentGoods:function(t){var e=this.selectData.findIndex((function(e){return e.goods_id==t.goods_id||e.id==t.goods_id}));-1===e?this.selectData=[].concat((0,r.default)(this.selectData),[t]):this.selectData.splice(e,1),this.$emit("change",this.selectData)},upCallback:function(t){var e=this;return(0,o.default)((0,a.default)().mark((function n(){var i,o,c;return(0,a.default)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:i=e.currentTabs,o=t.num,c=t.size,(0,s.getCommunityGoods)({keyword:e.keyword,type:e.tabsList[i].type,page_no:o,page_size:c}).then((function(t){uni.hideLoading(),(1==o||e.keyword)&&(e.lists=[]);var n=!!t.data.more;e.lists=[].concat((0,r.default)(e.lists),(0,r.default)(t.data.list)),e.mescroll.endSuccess(t.data.list.length,n)})).catch((function(t){e.mescroll.endErr(),uni.hideLoading()}));case 4:case"end":return n.stop()}}),n)})))()}}};e.default=l},6494:function(t,e,n){"use strict";var i=n("f8f2"),a=n.n(i);a.a},6944:function(t,e,n){var i=n("24fb");e=i(!1),e.push([t.i,".price-format[data-v-0a5a34e0]{font-family:Avenir,SourceHanSansCN,PingFang SC,Arial,Hiragino Sans GB,Microsoft YaHei,sans-serif}",""]),t.exports=e},"6c2f":function(t,e,n){var i=n("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.u-progress[data-v-90eaadc2]{overflow:hidden;height:15px;display:inline-flex;align-items:center;width:100%;border-radius:%?100?%}.u-active[data-v-90eaadc2]{width:0;height:100%;align-items:center;display:flex;flex-direction:row;justify-items:flex-end;justify-content:space-around;font-size:%?20?%;color:#fff;transition:all .4s ease}.u-striped[data-v-90eaadc2]{background-image:linear-gradient(45deg,hsla(0,0%,100%,.15) 25%,transparent 0,transparent 50%,hsla(0,0%,100%,.15) 0,hsla(0,0%,100%,.15) 75%,transparent 0,transparent);background-size:39px 39px}.u-striped-active[data-v-90eaadc2]{-webkit-animation:progress-stripes-data-v-90eaadc2 2s linear infinite;animation:progress-stripes-data-v-90eaadc2 2s linear infinite}@-webkit-keyframes progress-stripes-data-v-90eaadc2{0%{background-position:0 0}100%{background-position:39px 0}}@keyframes progress-stripes-data-v-90eaadc2{0%{background-position:0 0}100%{background-position:39px 0}}',""]),t.exports=e},"6ca1":function(t,e,n){var i=n("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.bb[data-v-5989a3f9]{border-bottom:1px solid #f6f6f6}.content-wrapper[data-v-5989a3f9]{height:%?900?%}.content-wrapper .sticky[data-v-5989a3f9]{width:100vw}.content-wrapper .sticky-title[data-v-5989a3f9]{padding:%?24?% 0;text-align:center}.content-wrapper .container[data-v-5989a3f9]{height:%?712?%;display:flex}.content-wrapper .container .left-menu[data-v-5989a3f9]{width:%?160?%}.content-wrapper .container .left-menu .submenu[data-v-5989a3f9]{height:%?90?%;line-height:%?90?%;text-align:center;font-size:%?26?%}.content-wrapper .container .left-menu .active[data-v-5989a3f9]{font-weight:500;color:#ff2c3c;position:relative;background-color:rgba(255,44,60,.1)}.content-wrapper .container .left-menu .active[data-v-5989a3f9]::before{content:"";width:%?6?%;height:%?30?%;position:absolute;left:%?10?%;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%);background-color:#ff2c3c}.content-wrapper .container .right-content[data-v-5989a3f9]{width:100%}.content-wrapper .container .right-content .tags[data-v-5989a3f9]{padding:%?20?%}.content-wrapper .container .right-content .tags .tags-item[data-v-5989a3f9]{margin-bottom:%?30?%}.content-wrapper .container .right-content .tags uni-image[data-v-5989a3f9]{width:%?120?%;height:%?120?%;border-radius:50%;position:relative}.content-wrapper .container .right-content .tags uni-image[data-v-5989a3f9]::after{content:"";color:#fff;font-size:%?50?%;font-weight:500;text-align:center;line-height:%?120?%;width:%?120?%;height:%?120?%;position:absolute;border-radius:50%;left:0;top:0;background:rgba(0,0,0,.1) url(/bundle_b/static/icon_tags.png) no-repeat 50%;background-size:%?40?%}',""]),t.exports=e},7169:function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return a})),n.d(e,"a",(function(){}));var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{staticClass:"u-progress",style:{borderRadius:t.round?"100rpx":0,height:t.height+"rpx",backgroundColor:t.inactiveColor}},[n("v-uni-view",{staticClass:"u-active",class:[t.type?"u-type-"+t.type+"-bg":"",t.striped?"u-striped":"",t.striped&&t.stripedActive?"u-striped-active":""],style:[t.progressStyle]},[t.$slots.default||t.$slots.$default?t._t("default"):t.showPercent?[t._v(t._s(t.percent+"%"))]:t._e()],2)],1)},a=[]},7399:function(t,e,n){"use strict";var i=n("4b4b"),a=n.n(i);a.a},"7f80":function(t,e,n){var i=n("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.u-search[data-v-3c66e606]{display:flex;flex-direction:row;align-items:center;flex:1;padding:%?15?% %?20?%}.u-content[data-v-3c66e606]{display:flex;flex-direction:row;align-items:center;padding:0 %?18?%;flex:1}.u-clear-icon[data-v-3c66e606]{display:flex;flex-direction:row;align-items:center}.u-input[data-v-3c66e606]{flex:1;font-size:%?28?%;line-height:1;margin:0 %?10?%;color:#909399}.u-close-wrap[data-v-3c66e606]{width:%?40?%;height:100%;display:flex;flex-direction:row;align-items:center;justify-content:center;border-radius:50%}.u-placeholder-class[data-v-3c66e606]{color:#909399}.u-action[data-v-3c66e606]{font-size:%?28?%;color:#303133;width:0;overflow:hidden;transition:all .3s;white-space:nowrap;text-align:center}.u-action-active[data-v-3c66e606]{width:%?80?%;margin-left:%?10?%}',""]),t.exports=e},8158:function(t,e,n){"use strict";var i=n("e6f3"),a=n.n(i);a.a},"815f":function(t,e,n){var i=n("6c2f");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var a=n("4f06").default;a("366b7ebf",i,!0,{sourceMap:!1,shadowMode:!1})},8962:function(t,e,n){var i=n("0e2d");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var a=n("4f06").default;a("a574c38c",i,!0,{sourceMap:!1,shadowMode:!1})},"8f3f":function(t,e,n){"use strict";n.r(e);var i=n("0a04"),a=n("05fc");for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);n("0f29");var r=n("f0c5"),s=Object(r["a"])(a["default"],i["b"],i["c"],!1,null,"78acf6de",null,!1,i["a"],void 0);e["default"]=s.exports},9045:function(t,e,n){var i=n("e40a");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var a=n("4f06").default;a("45bd3d4b",i,!0,{sourceMap:!1,shadowMode:!1})},9595:function(t,e,n){"use strict";n.r(e);var i=n("2feb"),a=n.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);e["default"]=a.a},9739:function(t,e,n){"use strict";n.r(e);var i=n("f9f5"),a=n("0caa");for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);n("5404");var r=n("f0c5"),s=Object(r["a"])(a["default"],i["b"],i["c"],!1,null,"e89802c2",null,!1,i["a"],void 0);e["default"]=s.exports},"9ba6":function(t,e,n){"use strict";n("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("a9e3");var i={props:{i:Number,index:{type:Number,default:function(){return 0}}},data:function(){return{downOption:{auto:!1},upOption:{auto:!1},isInit:!1}},watch:{index:function(t){this.i!==t||this.isInit||(this.isInit=!0,this.mescroll&&this.mescroll.triggerDownScroll())}},methods:{mescrollInitByRef:function(){if(!this.mescroll||!this.mescroll.resetUpScroll){var t=this.$refs.mescrollRef||this.$refs["mescrollRef"+this.i];t&&(this.mescroll=t.mescroll)}},mescrollInit:function(t){this.mescroll=t,this.mescrollInitByRef&&this.mescrollInitByRef(),this.i===this.index&&(this.isInit=!0,this.mescroll.triggerDownScroll())}}},a=i;e.default=a},a272:function(t,e,n){"use strict";n.r(e);var i=n("e2ba"),a=n("3ab4");for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);n("8158");var r=n("f0c5"),s=Object(r["a"])(a["default"],i["b"],i["c"],!1,null,"0a5a34e0",null,!1,i["a"],void 0);e["default"]=s.exports},a616:function(t,e,n){var i=n("4d5e");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var a=n("4f06").default;a("34aac901",i,!0,{sourceMap:!1,shadowMode:!1})},a744:function(t,e,n){"use strict";n("7a82");var i=n("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=i(n("f3f3"));n("4de4"),n("d3b7"),n("e9c4"),n("14d9"),n("a434"),n("d81d");var o=i(n("dab8")),r=n("f287"),s=n("a5ae"),c=n("2625"),u=i(n("9739")),l=i(n("d300")),d={components:{Recommend:u.default,Topic:l.default},data:function(){return{action:"",token:"",version:r.version,showRecommendPopup:!1,showTopicPopup:!1,pageHeight:"",fileList:[],formData:{id:"",image:[],content:"",shop:[],goods:[],topic_id:""},recommendTopic:[]}},mounted:function(){this.action=r.baseURL+"/api/file/formimage",this.token=o.default.getters.token,this.$toast=(0,s.trottle)(this.$toast,3e3,this)},onLoad:function(){var t=this.$Route.query;t.id&&(this.formData.id=t.id,this.initCommunityDetail()),this.initRecommendTopic()},methods:{initCommunityDetail:function(){var t=this;try{(0,c.getCommunityDetail)({id:this.formData.id}).then((function(e){if(1==e.code){var n=e.data,i=n.id,a=n.content,o=n.shop_data,r=n.goods_data,s=n.topic,c=n.images;t.formData.id=i,t.formData.content=a,t.formData.shop=o,t.formData.goods=r,t.formData.topic_id=s,t.formData.image=c.filter((function(t){return t.url=t.image,1})),t.fileList=JSON.parse(JSON.stringify(t.formData.image))}}))}catch(e){this.$nextTick((function(){t.isFirstLoading=!1}))}},initRecommendTopic:function(){var t=this;(0,c.getCommunityRecommendTopic)().then((function(e){t.recommendTopic=e.data}))},change:function(t){this.$toast({title:JSON.parse(t.data).msg}),1==JSON.parse(t.data).code&&this.formData.image.push({url:JSON.parse(t.data).data.uri})},remove:function(t){this.formData.image.splice(t,1)},showRecommend:function(){this.showRecommendPopup=!0},handleRecommend:function(t){var e=t.type,n=t.data;0==e?(this.formData.goods=n,this.formData.shop=[]):(this.formData.goods=[],this.formData.shop=n)},handleTopic:function(t){this.formData.topic_id=t},onSubmit:function(){var t=(0,a.default)({},this.formData);t.goods=t.goods.map((function(t){return t.goods_id})),t.shop=t.shop.map((function(t){return t.id})),null!=t.topic_id&&(t.topic_id=this.formData.topic_id.id),t.image=this.formData.image.map((function(t){return t.url})),t.id?this.handleCommunityEdit(t):this.handleCommunityAdd(t)},handleCommunityAdd:function(t){var e=this;(0,c.apiCommunityAdd)((0,a.default)({},t)).then((function(t){e.$toast({title:t.msg}),1==t.code&&setTimeout((function(){return e.$Router.back()}),500)}))},handleCommunityEdit:function(t){var e=this;(0,c.apiCommunityEdit)((0,a.default)({},t)).then((function(t){e.$toast({title:t.msg}),1==t.code&&setTimeout((function(){return e.$Router.back()}),500)}))},handleFocus:function(t){this.pageHeight=1.5*t.detail.height,setTimeout((function(){uni.pageScrollTo({scrollTop:160})}),50)}}};e.default=d},aec1:function(t,e,n){var i=n("4be1");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var a=n("4f06").default;a("692a8240",i,!0,{sourceMap:!1,shadowMode:!1})},afc7:function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"c",(function(){return o})),n.d(e,"a",(function(){return i}));var i={uPopup:n("5676").default,uSticky:n("1080").default,uSearch:n("5744").default,mescrollUni:n("5403").default,uImage:n("f919").default},a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("u-popup",{attrs:{mode:"bottom",closeable:!0,"border-radius":"14","safe-area-inset-bottom":!0,duration:100},model:{value:t.show,callback:function(e){t.show=e},expression:"show"}},[n("v-uni-view",{staticClass:"content-wrapper"},[n("u-sticky",[n("v-uni-view",{staticClass:"sticky"},[n("v-uni-view",{staticClass:"sticky-title xl normal bold"},[t._v("话题")]),n("u-search",{attrs:{hideRight:!0,"show-action":!0,"action-text":"取消",animation:!0,placeholder:"请输入搜索内容",height:"64"},on:{focus:function(e){arguments[0]=e=t.$handleEvent(e),t.hideRight=!1},blur:function(e){arguments[0]=e=t.$handleEvent(e),t.hideRight=!0},custom:function(e){arguments[0]=e=t.$handleEvent(e),t.handleCancel.apply(void 0,arguments)},change:function(e){arguments[0]=e=t.$handleEvent(e),t.mescroll.resetUpScroll()}},model:{value:t.keyword,callback:function(e){t.keyword=e},expression:"keyword"}})],1)],1),n("v-uni-view",{staticClass:"container"},[n("v-uni-scroll-view",{staticClass:"left-menu",attrs:{"scroll-y":"true"}},[t._l(t.topicData,(function(e,i){return[n("v-uni-view",{key:i+"_0",staticClass:"submenu normal",class:{active:i===t.menuCurrentIndex},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.selectMenu(i)}}},[t._v(t._s(e.name))])]}))],2),n("v-uni-view",{staticClass:"right-content"},[n("mescroll-uni",{ref:"mescrollRef",attrs:{top:"0",height:"712rpx",bgColor:"#f5f5f5",down:t.downOption,up:t.upOption},on:{init:function(e){arguments[0]=e=t.$handleEvent(e),t.mescrollInit.apply(void 0,arguments)},down:function(e){arguments[0]=e=t.$handleEvent(e),t.downCallback.apply(void 0,arguments)},up:function(e){arguments[0]=e=t.$handleEvent(e),t.upCallback.apply(void 0,arguments)}}},[n("v-uni-view",{staticClass:"tags"},[n("v-uni-view",{staticClass:"tags-item flex",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.unSelectTopic.apply(void 0,arguments)}}},[n("u-image",{attrs:{width:"120",height:"120",src:"/bundle_b/static/icon_unselect_tags.png",borderRadius:"50%"}}),n("v-uni-text",{staticClass:"m-l-16 nr bold normal"},[t._v("不添加任何话题")])],1),t.topicData[t.menuCurrentIndex]?[t._l(t.topicData[t.menuCurrentIndex].topic,(function(e,i){return[n("v-uni-view",{key:i+"_0",staticClass:"tags-item flex",on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.selectTopic(e)}}},[n("v-uni-image",{attrs:{src:e.image,mode:"aspectFill"}}),n("v-uni-view",{staticClass:"m-l-16"},[n("v-uni-view",{staticClass:"nr bold normal"},[t._v(t._s(e.name))]),n("v-uni-view",{staticClass:"m-t-10 xxs muted"},[t._v(t._s(e.click)+"人在看")])],1)],1)]}))]:t._e()],2)],1)],1)],1)],1)],1)},o=[]},b413:function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"c",(function(){return o})),n.d(e,"a",(function(){return i}));var i={uIcon:n("6976").default},a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{staticClass:"u-search",style:{margin:t.margin,backgroundColor:t.wrapBgColor},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.clickHandler.apply(void 0,arguments)}}},[n("v-uni-view",{staticClass:"u-content",style:{backgroundColor:t.bgColor,borderRadius:"round"==t.shape?"100rpx":"10rpx",border:t.borderStyle,height:t.height+"rpx"}},[n("v-uni-view",{staticClass:"u-icon-wrap"},[n("u-icon",{staticClass:"u-clear-icon",attrs:{size:30,name:t.searchIcon,color:t.searchIconColor?t.searchIconColor:t.color}})],1),n("v-uni-input",{staticClass:"u-input",style:[{textAlign:t.inputAlign,color:t.color,backgroundColor:t.bgColor},t.inputStyle],attrs:{"confirm-type":"search",value:t.value,disabled:t.disabled,focus:t.focus,maxlength:t.maxlength,"placeholder-class":"u-placeholder-class",placeholder:t.placeholder,"placeholder-style":"color: "+t.placeholderColor,type:"text"},on:{blur:function(e){arguments[0]=e=t.$handleEvent(e),t.blur.apply(void 0,arguments)},confirm:function(e){arguments[0]=e=t.$handleEvent(e),t.search.apply(void 0,arguments)},input:function(e){arguments[0]=e=t.$handleEvent(e),t.inputChange.apply(void 0,arguments)},focus:function(e){arguments[0]=e=t.$handleEvent(e),t.getFocus.apply(void 0,arguments)}}}),t.keyword&&t.clearabled&&t.focused?n("v-uni-view",{staticClass:"u-close-wrap",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.clear.apply(void 0,arguments)}}},[n("u-icon",{staticClass:"u-clear-icon",attrs:{name:"close-circle-fill",size:"34",color:"#c0c4cc"}})],1):t._e()],1),t.hideRight?n("v-uni-view",{staticClass:"u-action",class:[t.showActionBtn||t.show?"u-action-active":""],style:[t.actionStyle],on:{click:function(e){e.stopPropagation(),e.preventDefault(),arguments[0]=e=t.$handleEvent(e),t.custom.apply(void 0,arguments)}}},[t._v(t._s(t.actionText))]):t._e()],1)},o=[]},bd01:function(t,e,n){var i=n("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.goods-box[data-v-6e515196]{width:100%;height:%?700?%}.goods-box .search[data-v-6e515196]{width:100%;height:%?90?%}.goods-box .tab-control[data-v-6e515196]{border-top:1px solid #f6f6f6;border-bottom:1px solid #f6f6f6}.goods-box .tab-control uni-view[data-v-6e515196]{width:%?200?%;height:%?90?%;text-align:center;line-height:%?90?%;display:inline-block;color:#333;transition:all .2s}.goods-box .tab-control .active[data-v-6e515196]{color:#ff2c3c}.goods-box .goods-item[data-v-6e515196]{padding:%?20?%;border-bottom:1px solid #f6f6f6}.goods-box .goods-item .goods-name[data-v-6e515196]{width:%?460?%}.goods-box .goods-item uni-image[data-v-6e515196]{width:%?34?%;height:%?34?%}',""]),t.exports=e},bdee:function(t,e,n){"use strict";n.r(e);var i=n("62ed"),a=n.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);e["default"]=a.a},c529:function(t,e,n){"use strict";var i=n("1e2e"),a=n.n(i);a.a},d300:function(t,e,n){"use strict";n.r(e);var i=n("afc7"),a=n("23a9");for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);n("7399");var r=n("f0c5"),s=Object(r["a"])(a["default"],i["b"],i["c"],!1,null,"5989a3f9",null,!1,i["a"],void 0);e["default"]=s.exports},d5c8:function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"c",(function(){return o})),n.d(e,"a",(function(){return i}));var i={uSearch:n("5744").default,mescrollUni:n("5403").default,uImage:n("f919").default},a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{staticClass:"goods-box"},[n("v-uni-view",{staticClass:"search"},[n("u-search",{attrs:{hideRight:!0,"show-action":!0,"action-text":"取消",animation:!0,placeholder:"请输入搜索内容",height:"64"},on:{focus:function(e){arguments[0]=e=t.$handleEvent(e),t.hideRight=!1},blur:function(e){arguments[0]=e=t.$handleEvent(e),t.hideRight=!0},custom:function(e){arguments[0]=e=t.$handleEvent(e),t.handleCancel.apply(void 0,arguments)},change:function(e){arguments[0]=e=t.$handleEvent(e),t.mescroll.resetUpScroll()}},model:{value:t.keyword,callback:function(e){t.keyword=e},expression:"keyword"}})],1),n("v-uni-view",{staticClass:"tab-control"},t._l(t.tabsList,(function(e,i){return n("v-uni-view",{key:i,class:{active:t.currentTabs==i},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.changeTabs(i)}}},[t._v(t._s(e.label))])})),1),n("mescroll-uni",{ref:"mescrollRef",attrs:{top:"0",height:"620rpx",down:t.downOption,up:t.upOption},on:{init:function(e){arguments[0]=e=t.$handleEvent(e),t.mescrollInit.apply(void 0,arguments)},down:function(e){arguments[0]=e=t.$handleEvent(e),t.downCallback.apply(void 0,arguments)},up:function(e){arguments[0]=e=t.$handleEvent(e),t.upCallback.apply(void 0,arguments)}}},[t._l(t.lists,(function(e,i){return[n("v-uni-view",{key:i+"_0",staticClass:"goods-item flex row-between",on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.selectCurrentGoods(e)}}},[n("u-image",{attrs:{src:e.logo,width:"160",height:"160"}}),n("v-uni-view",{staticClass:"m-l-20 flex-1"},[n("v-uni-view",{staticClass:"goods-name line-1 m-b-12 nr normal"},[t._v(t._s(e.name))])],1),n("v-uni-image",{attrs:{src:t.getCurrentSelect(e)}})],1)]}))],2)],1)},o=[]},df7e:function(t,e,n){"use strict";n.r(e);var i=n("7169"),a=n("9595");for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);n("0b4b");var r=n("f0c5"),s=Object(r["a"])(a["default"],i["b"],i["c"],!1,null,"90eaadc2",null,!1,i["a"],void 0);e["default"]=s.exports},e2ba:function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return a})),n.d(e,"a",(function(){}));var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-text",{class:(t.lineThrough?"line-through":"")+" price-format",style:{color:t.color,"font-weight":t.weight}},[t.showSubscript?n("v-uni-text",{style:{"font-size":t.subscriptSize+"rpx","margin-right":"2rpx"}},[t._v("¥")]):t._e(),n("v-uni-text",{style:{"font-size":t.firstSize+"rpx","margin-right":"1rpx"}},[t._v(t._s(t.priceSlice.first))]),t.priceSlice.second?n("v-uni-text",{style:{"font-size":t.secondSize+"rpx"}},[t._v("."+t._s(t.priceSlice.second))]):t._e()],1)},a=[]},e40a:function(t,e,n){var i=n("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.goods-box[data-v-d1da54e0]{width:100%;height:%?700?%}.goods-box .search[data-v-d1da54e0]{width:100%;height:%?90?%}.goods-box .tab-control[data-v-d1da54e0]{border-top:1px solid #f6f6f6;border-bottom:1px solid #f6f6f6}.goods-box .tab-control uni-view[data-v-d1da54e0]{width:%?200?%;height:%?90?%;text-align:center;line-height:%?90?%;display:inline-block;color:#333;transition:all .2s}.goods-box .tab-control .active[data-v-d1da54e0]{color:#ff2c3c}.goods-box .goods-item[data-v-d1da54e0]{padding:%?20?%;border-bottom:1px solid #f6f6f6}.goods-box .goods-item .goods-name[data-v-d1da54e0]{width:%?460?%}.goods-box .goods-item uni-image[data-v-d1da54e0]{width:%?34?%;height:%?34?%}',""]),t.exports=e},e6f3:function(t,e,n){var i=n("6944");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var a=n("4f06").default;a("66e034c8",i,!0,{sourceMap:!1,shadowMode:!1})},ed04:function(t,e,n){"use strict";n.r(e);var i=n("01b0"),a=n.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);e["default"]=a.a},f05d:function(t,e,n){var i=n("7f80");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var a=n("4f06").default;a("7f5f35b1",i,!0,{sourceMap:!1,shadowMode:!1})},f396:function(t,e,n){"use strict";var i=n("9045"),a=n.n(i);a.a},f5a5:function(t,e,n){"use strict";var i=n("f05d"),a=n.n(i);a.a},f743:function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"c",(function(){return o})),n.d(e,"a",(function(){return i}));var i={uIcon:n("6976").default},a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{staticClass:"u-image",style:[t.wrapStyle,t.backgroundStyle],on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onClick.apply(void 0,arguments)}}},[t.isError?t._e():n("v-uni-image",{staticClass:"u-image__image",style:{borderRadius:"circle"==t.shape?"50%":t.$u.addUnit(t.borderRadius)},attrs:{src:t.src,mode:t.mode,"lazy-load":t.lazyLoad},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.onErrorHandler.apply(void 0,arguments)},load:function(e){arguments[0]=e=t.$handleEvent(e),t.onLoadHandler.apply(void 0,arguments)}}}),t.showLoading&&t.loading?n("v-uni-view",{staticClass:"u-image__loading",style:{borderRadius:"circle"==t.shape?"50%":t.$u.addUnit(t.borderRadius),backgroundColor:this.bgColor}},[t.$slots.loading?t._t("loading"):n("u-icon",{attrs:{name:t.loadingIcon,width:t.width,height:t.height}})],2):t._e(),t.showError&&t.isError&&!t.loading?n("v-uni-view",{staticClass:"u-image__error",style:{borderRadius:"circle"==t.shape?"50%":t.$u.addUnit(t.borderRadius)}},[t.$slots.error?t._t("error"):n("u-icon",{attrs:{name:t.errorIcon,width:t.width,height:t.height}})],2):t._e()],1)},o=[]},f8f2:function(t,e,n){var i=n("bd01");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var a=n("4f06").default;a("559845be",i,!0,{sourceMap:!1,shadowMode:!1})},f919:function(t,e,n){"use strict";n.r(e);var i=n("f743"),a=n("3f30");for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);n("c529");var r=n("f0c5"),s=Object(r["a"])(a["default"],i["b"],i["c"],!1,null,"1bf07c9a",null,!1,i["a"],void 0);e["default"]=s.exports},f9f3:function(t,e,n){"use strict";n.r(e);var i=n("574d"),a=n.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);e["default"]=a.a},f9f5:function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"c",(function(){return o})),n.d(e,"a",(function(){return i}));var i={uPopup:n("5676").default,tabs:n("9ad5").default,tab:n("520f").default},a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("u-popup",{attrs:{mode:"bottom",closeable:!0,"border-radius":"14","safe-area-inset-bottom":!0,duration:100},model:{value:t.show,callback:function(e){t.show=e},expression:"show"}},[n("v-uni-view",{staticClass:"content-wrapper"},[n("tabs",{attrs:{current:t.active,height:"100","is-scroll":!1,width:"400rpx",showBar:!1,async:t.isAsync},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.changeTabs.apply(void 0,arguments)}}},[n("tab",{attrs:{name:"宝贝"}},[n("goods",{attrs:{i:0,index:t.active},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.handleGoods.apply(void 0,arguments)}},model:{value:t.goodsData,callback:function(e){t.goodsData=e},expression:"goodsData"}})],1),n("tab",{attrs:{name:"店铺"}},[n("shop",{attrs:{i:1,index:t.active},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.handleShop.apply(void 0,arguments)}},model:{value:t.shopData,callback:function(e){t.shopData=e},expression:"shopData"}})],1)],1)],1),n("v-uni-view",{staticClass:"recommend-footer flex"},[n("v-uni-button",{staticClass:"white br60 bg-primary btn",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.confirm.apply(void 0,arguments)}}},[t._v("确认")])],1)],1)},o=[]},fa19:function(t,e,n){"use strict";var i=n("aec1"),a=n.n(i);a.a}}]);