<?php



namespace app\common\model\bargain;


use app\common\basics\Models;
use app\common\model\goods\GoodsItem;

/**
 * 砍价活动 商品SKU模型
 * Class BargainItem
 * <AUTHOR>
 * @package app\common\model
 */
class BargainItem extends Models
{
    /**
     * @notes 关联商品规格
     * @return \think\model\relation\HasOne
     * <AUTHOR>
     * @date 2021/7/13 6:40 下午
     */
    public function goodsItem()
    {

        return $this->hasOne(GoodsItem::class, 'id', 'item_id');
    }
}