{layout name="layout1" /}
<style>
    .layui-table-cell { height: auto; }
    input::-webkit-outer-spin-button,
    input::-webkit-inner-spin-button
    {
        -webkit-appearance: none;
    }
    input[type="number"]
    {
        -moz - appearance
        :
        textfield;
    }
</style>
<div class="wrapper">
    <div class="layui-card">
        <!-- 操作提示 -->
        <div class="layui-card-body">
            <div class="layui-collapse like-layui-collapse" style="border:1px dashed #c4c4c4">
                <div class="layui-colla-item">
                    <h2 class="layui-colla-title like-layui-colla-title" style="background-color: #fff">操作提示</h2>
                    <div class="layui-colla-content layui-show">
                        <p>*砍价活动的通用设置。</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 设置 -->
        <div class="layui-card-body layui-form">
            <div class="layui-form-item" style="margin-bottom: 0;">
                <label class="layui-form-label" style="width:200px;">砍价成功下单有效时长：</label>
                <div class="layui-input-inline">
                    <input type="number" name="payment_limit_time" value="{$payment_limit_time}" onkeyup="value=value.replace(/[^\d]/g,'')" autocomplete="off" class="layui-input">
                </div>
                <div class="layui-form-mid layui-word-aux">分钟</div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label"></label>
                <div class="layui-input-block">
                    <div class="layui-form-mid layui-word-aux">砍价成功后，在设置的有效时长内可以提交订单，为空或不填表示无时间限制</div>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label"></label>
                <div class="layui-input-block">
                    <button class="layui-btn layui-btn-sm layuiadmin-btn {$view_theme_color}" lay-submit lay-filter="addSubmit">确定</button>
                </div>
            </div>

        </div>
    </div>
</div>

<script>
    layui.config({
        base: '/static/plug/layui-admin/dist/layuiadmin/'
    }).extend({
        index: 'lib/index' //主入口模块
    }).use(['index', 'like'], function(){
        var $ = layui.$
            ,form = layui.form
            ,like = layui.like;

        form.on('submit(addSubmit)', function(data){
            like.ajax({
                url:'{:url("bargain.Bargain/set")}',
                data: data.field,
                type:"post",
                success:function(res) {
                    if(res.code === 1) {
                        layui.layer.msg(res.msg, { offset:'15px', icon:1, time:1000 });
                    } else {
                        layui.layer.msg(res.msg, { offset:'15px', icon:2, time:1000 });
                    }
                }
            });
            return false;
        });

        //事件
        var active = {
            // 切换状态
            set: function (obj) {

                like.ajax({
                    url:'{:url("TeamSet/set")}',
                    data:{},
                    type:"post",
                    success:function(res) {
                        if(res.code === 1) {
                            layui.layer.msg(res.msg, { offset:'15px', icon:1, time:1000 });
                        } else {
                            layui.layer.msg(res.msg, { offset:'15px', icon:2, time:1000 });
                        }
                    }
                });
            }

        };

        // 绑定点击按钮事件
        $('.layui-btn.layuiadmin-btn').on('click', function(){
            var type = $(this).data('type');
            active[type] ? active[type].call(this) : '';
        });

    });
</script>