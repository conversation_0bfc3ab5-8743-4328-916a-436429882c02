# 管理员通知系统使用说明

## 概述

基于WebSocket的实时管理员通知系统，支持前端JavaScript和后端PHP两种方式发送通知。

## 文件结构

```
├── public/static/admin/js/admin-notification-trigger.js  # 前端通知触发器
├── public/admin-notification-demo.html                   # 使用示例页面
├── app/common/service/AdminNotificationService.php       # 后端通知服务
├── app/api/controller/AdminNotification.php              # API控制器
└── docs/admin-notification-system.md                     # 本文档
```

## 前端使用方法

### 1. 引入脚本文件

```html
<script src="/static/admin/js/admin-notification-trigger.js"></script>
```

### 2. 基本使用

```javascript
// 发送系统通知
AdminNotificationTrigger.sendSystemNotification('系统维护', '系统将在今晚进行维护，请提前保存工作。');

// 发送错误通知
AdminNotificationTrigger.sendErrorNotification('操作失败', '用户数据同步失败，请检查网络连接。');

// 发送警告通知
AdminNotificationTrigger.sendWarningNotification('磁盘空间', '磁盘空间不足，建议清理无用文件。');

// 发送信息通知
AdminNotificationTrigger.sendInfoNotification('访问统计', '今日访问量已达到新高。');

// 发送自定义通知
AdminNotificationTrigger.sendNotification({
    type: 'admin_notification',
    title: '新订单提醒',
    content: '您有一个新的订单需要处理',
    url: '/admin/order/detail/123',
    icon: 1
});
```

### 3. 高级用法

```javascript
// 初始化时指定管理员信息
AdminNotificationTrigger.init({
    adminId: 1,
    nickname: '超级管理员',
    token: 'your_admin_token'
}).then(() => {
    console.log('通知系统初始化成功');
}).catch(err => {
    console.error('初始化失败:', err);
});

// 检查连接状态
const status = AdminNotificationTrigger.getConnectionStatus();
console.log('连接状态:', status);

// 手动重连
AdminNotificationTrigger.reconnect();

// 设置调试模式
AdminNotificationTrigger.setDebug(true);
```

### 4. Promise支持

```javascript
AdminNotificationTrigger.sendSystemNotification('测试', '这是测试消息')
    .then(() => {
        console.log('通知发送成功');
    })
    .catch(err => {
        console.error('通知发送失败:', err);
    });
```

## 后端使用方法

### 1. 基本使用

```php
use app\common\service\AdminNotificationService;

// 发送系统通知
AdminNotificationService::sendSystemNotification('系统维护', '系统将在今晚进行维护');

// 发送错误通知
AdminNotificationService::sendErrorNotification('数据库错误', '数据库连接失败');

// 发送警告通知
AdminNotificationService::sendWarningNotification('磁盘空间', '磁盘空间不足');

// 发送信息通知
AdminNotificationService::sendInfoNotification('统计报告', '今日访问量创新高');

// 发送成功通知
AdminNotificationService::sendSuccessNotification('备份完成', '数据备份已完成');

// 发送自定义通知
AdminNotificationService::sendNotification(
    '新订单',
    '您有一个新的订单需要处理',
    'admin',
    '/admin/order/detail/123',
    1,
    ['order_id' => 123, 'customer' => '张三']
);
```

### 2. 批量发送

```php
$notifications = [
    [
        'title' => '系统通知1',
        'content' => '内容1',
        'type' => 'system'
    ],
    [
        'title' => '错误通知2',
        'content' => '内容2',
        'type' => 'error',
        'url' => '/admin/error/detail'
    ]
];

$results = AdminNotificationService::sendBatchNotifications($notifications);
```

### 3. 获取统计信息

```php
// 获取今日通知历史
$history = AdminNotificationService::getNotificationHistory();

// 获取指定日期的通知历史
$history = AdminNotificationService::getNotificationHistory('2024-01-15');

// 获取通知统计
$stats = AdminNotificationService::getNotificationStats();

// 清理过期记录
AdminNotificationService::cleanExpiredNotifications();
```

## API接口

### 1. 发送测试通知

```
GET /api/admin_notification/testNotification
```

参数：
- `title`: 通知标题（可选，默认：测试通知）
- `content`: 通知内容（可选，默认：这是一条测试通知消息）
- `type`: 通知类型（可选，默认：admin）
- `url`: 跳转链接（可选）
- `icon`: 图标（可选）

### 2. 发送系统通知

```
POST /api/admin_notification/sendSystemNotification
```

参数：
- `title`: 通知标题
- `content`: 通知内容（必填）
- `url`: 跳转链接（可选）

### 3. 发送错误通知

```
POST /api/admin_notification/sendErrorNotification
```

参数：
- `title`: 通知标题
- `content`: 通知内容（必填）
- `url`: 跳转链接（可选）

### 4. 批量发送通知

```
POST /api/admin_notification/sendBatchNotifications
```

参数：
```json
{
    "notifications": [
        {
            "title": "通知标题1",
            "content": "通知内容1",
            "type": "system",
            "url": "/admin/page1"
        },
        {
            "title": "通知标题2",
            "content": "通知内容2",
            "type": "error"
        }
    ]
}
```

### 5. 获取通知历史

```
GET /api/admin_notification/getNotificationHistory?date=2024-01-15
```

### 6. 获取通知统计

```
GET /api/admin_notification/getNotificationStats?date=2024-01-15
```

### 7. 获取待处理通知

```
GET /api/admin_notification/getPendingNotifications
```

### 8. 发送快速通知

```
GET /api/admin_notification/sendQuickNotification?type=system
```

支持的类型：`system`, `error`, `warning`, `info`, `success`

## 通知类型

| 类型 | 说明 | 图标 | 颜色 |
|------|------|------|------|
| `admin_notification` | 默认通知 | 0 | 默认 |
| `system_notification` | 系统通知 | 1 | 蓝色 |
| `error_notification` | 错误通知 | 2 | 红色 |
| `warning_notification` | 警告通知 | 3 | 橙色 |
| `info_notification` | 信息通知 | 4 | 紫色 |
| `success_notification` | 成功通知 | 1 | 绿色 |

## 配置说明

### WebSocket配置

在 `config/swoole.php` 中配置WebSocket服务器：

```php
'websocket' => [
    'enable' => true,
    'handler' => CombinedHandler::class,
    'ping_interval' => 15000,
    'ping_timeout' => 45000,
    // ...
]
```

### 前端配置

在页面中设置管理员变量：

```html
<script>
    var ADMIN_ID = 1;
    var ADMIN_NICKNAME = '管理员';
    var ADMIN_TOKEN = 'admin_token';
</script>
```

## 故障排除

### 1. 通知收不到

检查项目：
1. WebSocket服务器是否启动：`php think swoole:server start`
2. 管理员变量是否设置：检查 `ADMIN_ID` 等变量
3. 浏览器控制台是否有错误信息
4. WebSocket连接状态：调用 `AdminNotificationTrigger.getConnectionStatus()`

### 2. 连接失败

可能原因：
1. WebSocket服务器未启动
2. 端口被占用或防火墙阻止
3. HTTPS页面连接WS协议的安全限制
4. 域名解析问题

### 3. 调试方法

```javascript
// 启用调试模式
AdminNotificationTrigger.setDebug(true);

// 检查连接状态
console.log('连接状态:', AdminNotificationTrigger.getConnectionStatus());

// 手动测试连接
AdminNotificationTrigger.init().then(() => {
    console.log('连接成功');
}).catch(err => {
    console.error('连接失败:', err);
});
```

## 示例页面

访问 `/admin-notification-demo.html` 查看完整的使用示例和测试界面。

## 注意事项

1. 确保WebSocket服务器正常运行
2. 在HTTPS环境下需要使用WSS协议
3. 管理员变量必须正确设置
4. 建议在生产环境中关闭调试模式
5. 定期清理过期的通知记录
6. 监控WebSocket连接状态和通知发送成功率