<?php
namespace app\admin\validate;

use think\Validate;

/**
 * 采购套餐验证
 * Class PurchaserPackageValidate
 * @package app\admin\validate
 */
class PurchaserPackageValidate extends Validate
{
    protected $rule = [
        'id'              => 'require|integer',
        'name'            => 'require|max:255',
        'purchaser_count' => 'require|integer|gt:0',
        'price'           => 'require|float|gt:0',
        'status'          => 'require|in:0,1',
        'sort'            => 'require|integer',
    ];

    protected $message = [
        'id.require'              => 'ID不能为空',
        'id.integer'              => 'ID必须为整数',
        'name.require'            => '套餐名称不能为空',
        'name.max'                => '套餐名称不能超过255个字符',
        'purchaser_count.require' => '分配人数不能为空',
        'purchaser_count.integer' => '分配人数必须为整数',
        'purchaser_count.gt'      => '分配人数必须大于0',
        'price.require'           => '套餐价格不能为空',
        'price.float'             => '套餐价格必须为数字',
        'price.gt'                => '套餐价格必须大于0',
        'status.require'          => '状态不能为空',
        'status.in'               => '状态值不正确',
        'sort.require'            => '排序不能为空',
        'sort.integer'            => '排序必须为整数',
    ];

    protected $scene = [
        'add'  => ['name', 'purchaser_count', 'price', 'status', 'sort'],
        'edit' => ['id', 'name', 'purchaser_count', 'price', 'status', 'sort'],
    ];
}
