<?php



namespace app\admin\logic;

use app\common\basics\Logic;
use app\common\model\JcaiTemplate;
use think\facade\Db;
use app\common\server\ConfigServer;

class JcaiLogic extends Logic
{

    public static function templatelists(){
        $type_text=['月卡','季卡','年卡','两年','终身'];
        $typestext=['集采会员','集采商家会员'];
        $list = JcaiTemplate::where(['del'=>0])->order(['id' => 'asc','give_type'])->select()->toArray();
        foreach ($list as &$item){
            $item['r_money']= $item['money'];#优惠完的金额
            $item['h_money'] = $item['money'];#优惠完的金额
            $item['money'] && $item['money'] = '￥'.$item['money'];
            $item['give_type_text']=$type_text[$item['give_type']];
            $item['types_text']=$typestext[$item['types']];
//            $item['give_mone    y'] && $item['give_money'] = '￥'.$item['give_money'];
        }
        return $list;
    }

    public static function getRechargeConfig(){
        $config =  [
            'open_racharge'  => ConfigServer::get('recharge','open_racharge',0),
            'give_growth'    => ConfigServer::get('recharge', 'give_growth', 0),
            'min_money'      => ConfigServer::get('recharge', 'min_money', 0),
        ];
        return [$config];
    }

    public static function add($post){
        try{
            // 判断充值金额是否已存在
            $recharge_template = JcaiTemplate::where([
                'del' =>0,
                'types' => $post['types'],
                'give_type' => $post['give_type']
            ])->findOrEmpty();
            if(!$recharge_template->isEmpty()) {
                throw new \think\Exception('该会员的方案已存在');
            }

            $new = time();
            $add_data = [
                'money'         => $post['money'],
                'give_type'    => $post['give_type'],
                'types'    => $post['types'],
                'create_time'   => $new,
                'update_time'   => $new,
            ];
            JcaiTemplate::create($add_data);
            return true;
        }catch(\Exception $e) {
            self::$error = $e->getMessage();
            return false;
        }
    }

    public static function changeTableValue($table,$pk_name,$pk_value,$field,$field_value){
        //允许修改的字段
        $allow_field = [
            'is_show','sort','status','is_new','is_best','is_like','is_recommend'
        ];
        if(!in_array($field,$allow_field)){
            return false;
        }
        if(is_array($pk_value)){
            $where[] = [$pk_name,'in',$pk_value];
        }else{
            $where[] = [$pk_name,'=',$pk_value];
        }

        $data= [
            $field          => $field_value,
            'update_time'   => time(),
        ];

        return Db::name($table)->where($where)->update($data);
    }


    public static function getJcaiTemplate($id){
        return Db::name('jcai_template')->where(['id'=>$id])->find();
    }

    public static function edit($post){
        try{
            // 判断充值金额是否已存在
            $recharge_template = JcaiTemplate::where([
                ['del', '=', 0],
                ['types', '=', $post['types']],
                ['give_type', '=', $post['give_type']],
                ['id', '<>', $post['id']],
            ])->findOrEmpty();
            if(!$recharge_template->isEmpty()) {
                throw new \think\Exception('该会员的方案已存在');
            }
            $new = time();
            $update_data = [
                'id'            => $post['id'],
                'money'         => $post['money'],
                'give_type'    => $post['give_type'],
                'types'    => $post['types'],
                'update_time'   => $new,
            ];
            JcaiTemplate::update($update_data);
            return true;
        }catch(\Exception $e) {
            self::$error = $e->getMessage();
            return false;
        }
    }

    public static function del($id){
        return Db::name('jcai_template')->where(['id'=>$id])->update(['update_time'=>time(),'del'=>1]);
    }

    public static function setRecharge($post){
        ConfigServer::set('recharge','open_racharge',$post['open_racharge']);
        ConfigServer::set('recharge','give_growth',$post['give_growth']);
        ConfigServer::set('recharge','min_money',$post['min_money']);
    }
}
