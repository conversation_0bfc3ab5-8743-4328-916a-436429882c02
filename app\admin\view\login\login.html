<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{$view_env_name}{$config.name}</title>
    <link rel="shortcut icon" href="{$storageUrl}{$config.web_favicon}"/>
    <link rel="stylesheet" href="__PUBLIC__/static/lib/layui/css/layui.css">
    <link rel="stylesheet" type="text/css" href="__PUBLIC__/static/common/css/login.css"/>
    <!-- 引入Google字体和图标 -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="admin-login">
<!-- 背景动态效果 -->
<div class="background-effects">
    <!-- 动画粒子 -->
    <div class="particles-container">
        <div class="particle particle-1"></div>
        <div class="particle particle-2"></div>
        <div class="particle particle-3"></div>
        <div class="particle particle-4"></div>
        <div class="particle particle-5"></div>
        <div class="particle particle-6"></div>
        <div class="particle particle-7"></div>
        <div class="particle particle-8"></div>
    </div>

    <!-- 流动光效 -->
    <div class="flowing-lights">
        <div class="light-beam beam-1"></div>
        <div class="light-beam beam-2"></div>
        <div class="light-beam beam-3"></div>
    </div>

    <!-- 网格背景 -->
    <div class="grid-background">
        <div class="grid-line grid-vertical"></div>
        <div class="grid-line grid-horizontal"></div>
    </div>

    <!-- 渐变光晕 -->
    <div class="gradient-orbs">
        <div class="orb orb-1"></div>
        <div class="orb orb-2"></div>
        <div class="orb orb-3"></div>
    </div>
</div>

<div class="login-container">
    <!-- 顶部Logo区域 -->
    <div class="header-logo">
        <img src="{$storageUrl}{$config.login_logo}" alt="Logo" class="logo-img"/>
    </div>

    <!-- 主登录区域 -->
    <div class="login-main">
        <!-- 左侧装饰区域 -->
        <div class="login-left">
            <div class="left-content">
                <div class="welcome-text">
                    <h1>欢迎回来</h1>
                    <p>登录您的管理账户，开始高效的工作体验</p>
                </div>
                <div class="feature-list">
                    <div class="feature-item">
                        <i class="fas fa-shield-alt"></i>
                        <span>安全可靠</span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-rocket"></i>
                        <span>高效便捷</span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-chart-line"></i>
                        <span>数据洞察</span>
                    </div>
                </div>
                <!-- 高端装饰元素 -->
                <div class="decorative-elements">
                    <!-- 几何图形组合 -->
                    <div class="geometric-shapes">
                        <div class="shape shape-1"></div>
                        <div class="shape shape-2"></div>
                        <div class="shape shape-3"></div>
                        <div class="shape shape-4"></div>
                        <div class="shape shape-5"></div>
                    </div>

                    <!-- 数据可视化图表 -->
                    <div class="data-visualization">
                        <div class="chart-container">
                            <div class="chart-bar" style="height: 60%; animation-delay: 0.1s;"></div>
                            <div class="chart-bar" style="height: 80%; animation-delay: 0.2s;"></div>
                            <div class="chart-bar" style="height: 45%; animation-delay: 0.3s;"></div>
                            <div class="chart-bar" style="height: 90%; animation-delay: 0.4s;"></div>
                            <div class="chart-bar" style="height: 70%; animation-delay: 0.5s;"></div>
                            <div class="chart-bar" style="height: 55%; animation-delay: 0.6s;"></div>
                        </div>
                        <div class="chart-label">数据洞察</div>
                    </div>

                    <!-- 科技感线条 -->
                    <div class="tech-lines">
                        <div class="line line-1"></div>
                        <div class="line line-2"></div>
                        <div class="line line-3"></div>
                    </div>

                    <!-- 浮动图标 -->
                    <div class="floating-icons">
                        <div class="floating-icon icon-1">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <div class="floating-icon icon-2">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="floating-icon icon-3">
                            <i class="fas fa-cog"></i>
                        </div>
                        <div class="floating-icon icon-4">
                            <i class="fas fa-rocket"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 右侧登录表单 -->
        <div class="login-right">
            <div class="login-form-container">
                <div class="form-header animate-slide-up">
                    <h2 class="form-title">{$config.login_title}</h2>
                    <p class="form-subtitle">请输入您的登录凭据</p>
                </div>

                <form class="login-form layui-form">
                    <!-- 账号输入框 -->
                    <div class="input-group animate-slide-up" style="animation-delay: 0.1s;">
                        <label class="input-label">账号</label>
                        <div class="input-wrapper">
                            <i class="fas fa-user input-icon"></i>
                            <input type="text"
                                   name="account"
                                   lay-verify="required"
                                   lay-vertype="tips"
                                   class="form-input"
                                   placeholder="请输入您的账号"
                                   value=""/>
                        </div>
                    </div>

                    <!-- 密码输入框 -->
                    <div class="input-group animate-slide-up" style="animation-delay: 0.2s;">
                        <label class="input-label">密码</label>
                        <div class="input-wrapper">
                            <i class="fas fa-lock input-icon"></i>
                            <input type="password"
                                   name="password"
                                   lay-verify="required"
                                   lay-vertype="tips"
                                   class="form-input"
                                   placeholder="请输入您的密码"/>
                            <i class="fas fa-eye-slash password-toggle" onclick="togglePassword()"></i>
                        </div>
                    </div>

                    <!-- 记住账号选项 -->
                    <div class="form-options animate-slide-up" style="animation-delay: 0.3s;">
                        <label class="checkbox-wrapper">

                            <span class="checkmark"> <input type="checkbox"
                                   name="remember_account"
                                   id="remember_account"
                                   {notempty name="account"}checked=""{/notempty}> </span>
                            <span class="checkbox-text">记住账号</span>
                        </label>
                    </div>

                    <!-- 登录按钮 -->
                    <button type="submit"
                            id="login"
                            lay-filter="login"
                            class="login-btn animate-slide-up"
                            style="animation-delay: 0.4s;"
                            lay-submit>
                        <span class="btn-text">登录</span>
                        <i class="fas fa-arrow-right btn-icon"></i>
                    </button>
                </form>
            </div>
        </div>
    </div>

    <!-- 底部版权信息 -->
    <footer class="login-footer">
        <div class="footer-content">
            <span>{$config.company_name}</span>
            <span class="separator">|</span>
            <a href="{$config.link}" target="_blank" class="footer-link">{$config.number}</a>
        </div>
    </footer>
</div>

</body>

<script src="__PUBLIC__/static/lib/layui/layui.js"></script>
<script src="__PUBLIC__/static/admin/js/jquery.min.js"></script>
<script src="__PUBLIC__/static/admin/js/function.js"></script>

<script>
    // 防止在iframe中加载
    if (self != top) {
        parent.window.location.replace(window.location.href);
    }

    // 初始化layui表单
    layui.use('form', function(){
        var form = layui.form;
        form.on('submit(login)', function (obj) {
            login(obj);
            return false; // 阻止表单跳转
        });
    });

    // 登录函数
    function login(obj) {
        // 显示加载状态
        const loginBtn = document.getElementById('login');
        const btnText = loginBtn.querySelector('.btn-text');
        const btnIcon = loginBtn.querySelector('.btn-icon');

        loginBtn.disabled = true;
        btnText.textContent = '登录中...';
        btnIcon.className = 'fas fa-spinner fa-spin btn-icon';

        like.ajax({
            url: '{:url("login/login")}',
            data: obj.field,
            type: 'post',
            success: function (res) {
                if (res.code == 1) {
                    btnText.textContent = '登录成功';
                    btnIcon.className = 'fas fa-check btn-icon';

                    layer.msg(res.msg, {
                        offset: '15px',
                        icon: 1,
                        time: 1000
                    }, function () {
                        location.href = '../';
                    });
                } else {
                    // 恢复按钮状态
                    loginBtn.disabled = false;
                    btnText.textContent = '登录';
                    btnIcon.className = 'fas fa-arrow-right btn-icon';
                }
                $('#captcha').attr('src', '{:captcha_src()}?t=' + new Date().getTime());
            },
            error: function() {
                // 恢复按钮状态
                loginBtn.disabled = false;
                btnText.textContent = '登录';
                btnIcon.className = 'fas fa-arrow-right btn-icon';
            }
        });
    }

    // 密码显示/隐藏切换
    function togglePassword() {
        const passwordInput = document.querySelector('input[name="password"]');
        const toggleIcon = document.querySelector('.password-toggle');

        if (passwordInput.type === 'password') {
            passwordInput.type = 'text';
            toggleIcon.className = 'fas fa-eye password-toggle';
        } else {
            passwordInput.type = 'password';
            toggleIcon.className = 'fas fa-eye-slash password-toggle';
        }
    }

    // 输入框焦点效果和复选框处理
    document.addEventListener('DOMContentLoaded', function() {
        const inputs = document.querySelectorAll('.form-input');

        inputs.forEach(input => {
            input.addEventListener('focus', function() {
                this.parentElement.classList.add('focused');
            });

            input.addEventListener('blur', function() {
                if (!this.value) {
                    this.parentElement.classList.remove('focused');
                }
            });

            // 如果输入框有值，保持焦点样式
            if (input.value) {
                input.parentElement.classList.add('focused');
            }
        });

        // 自定义复选框点击处理
        const checkboxWrapper = document.querySelector('.checkbox-wrapper');
        const checkbox = document.querySelector('#remember_account');

        if (checkboxWrapper && checkbox) {
            checkboxWrapper.addEventListener('click', function(e) {
                e.preventDefault();
                checkbox.checked = !checkbox.checked;

                // 触发change事件以便layui能够获取到值
                const event = new Event('change', { bubbles: true });
                checkbox.dispatchEvent(event);
            });
        }
    });

    // 键盘事件绑定
    like.keyUpClick('[name="account"]', '#login');
    like.keyUpClick('[name="password"]', '#login');
    like.keyUpClick('[name="code"]', '#login');

</script>
</html>