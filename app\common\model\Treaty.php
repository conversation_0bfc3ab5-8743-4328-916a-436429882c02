<?php


namespace app\common\model;


use app\common\basics\Models;

/**
 * 协议模型
 * Class Treaty
 * @package app\common\model
 */
class Treaty extends Models
{
    public function setContentAttr($value,$data)
    {
        $content = $data['content'];
        if (!empty($content)) {
            $content = HtmlSetImage($content);
        }
        return $content;
    }

    public function getContentAttr($value,$data)
    {
        $content = $data['content'];
        if (!empty($content)) {
            $content = HtmlGetImage($content);
        }
        return $content;
    }
}