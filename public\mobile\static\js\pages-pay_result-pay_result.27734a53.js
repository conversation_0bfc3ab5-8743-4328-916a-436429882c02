(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-pay_result-pay_result"],{"343a":function(e,t,a){var n=a("7c88");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[e.i,n,""]]),n.locals&&(e.exports=n.locals);var r=a("4f06").default;r("4f0bbf29",n,!0,{sourceMap:!1,shadowMode:!1})},"3be1":function(e,t,a){"use strict";a.r(t);var n=a("fb26"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a},4555:function(e,t,a){"use strict";a.d(t,"b",(function(){return n})),a.d(t,"c",(function(){return r})),a.d(t,"a",(function(){}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("v-uni-view",{staticClass:"pay-result"},[a("v-uni-view",{staticClass:"contain bg-white"},[a("v-uni-view",{staticClass:"header flex-col col-center"},[a("v-uni-view",[a("v-uni-image",{staticClass:"tips-icon",attrs:{src:"/static/images/icon_success.png"}})],1),"线下支付"==e.payInfo.pay_way?a("v-uni-view",{staticClass:"xl m-t-20"},[e._v("订单创建成功")]):a("v-uni-view",{staticClass:"xl m-t-20"},[e._v("订单支付成功")])],1),a("v-uni-view",{staticClass:"info"},[a("v-uni-view",{staticClass:"order-num flex row-between m-t-20"},[a("v-uni-view",[e._v("订单编号")]),a("v-uni-view",[e._v(e._s(e.payInfo.order_sn))])],1),e.payInfo.pay_time&&"线下支付"!=e.payInfo.pay_way?a("v-uni-view",{staticClass:"order-time flex row-between m-t-20"},[a("v-uni-view",[e._v("付款时间")]),a("v-uni-view",[e._v(e._s(e.payInfo.pay_time))])],1):e._e(),e.payInfo.pay_time&&"线下支付"==e.payInfo.pay_way?a("v-uni-view",{staticClass:"order-time flex row-between m-t-20"},[a("v-uni-view",[e._v("下单时间")]),a("v-uni-view",[e._v(e._s(e.payInfo.pay_time))])],1):e._e(),a("v-uni-view",{staticClass:"order-pay-type flex row-between m-t-20"},[a("v-uni-view",[e._v("支付方式")]),a("v-uni-view",[e._v(e._s(e.payInfo.pay_way))])],1),a("v-uni-view",{staticClass:"order-pay-money flex row-between m-t-20"},[a("v-uni-view",[e._v("支付金额")]),a("v-uni-view",[e._v(e._s(e.payInfo.total_amount))])],1)],1),a("v-uni-view",{staticClass:"line m-t-40"}),a("v-uni-view",{staticClass:"m-t-40 flex-col row-center"},[a("router-link",{attrs:{to:e.getPagesUrl,navType:"replace"}},[a("v-uni-button",{staticClass:"br60",attrs:{type:"primary",size:"lg"}},[e._v("查看订单")])],1),a("router-link",{attrs:{navType:"pushTab",to:"/pages/index/index"}},[a("v-uni-button",{staticClass:"br60 plain primary m-t-30",attrs:{size:"lg"}},[e._v("返回首页")])],1)],1)],1)],1)},r=[]},"48d4":function(e,t,a){"use strict";a.r(t);var n=a("4555"),r=a("3be1");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);a("ed70");var s=a("f0c5"),o=Object(s["a"])(r["default"],n["b"],n["c"],!1,null,"69a7a1e8",null,!1,n["a"],void 0);t["default"]=o.exports},"7c88":function(e,t,a){var n=a("24fb");t=n(!1),t.push([e.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.pay-result .contain[data-v-69a7a1e8]{border-radius:%?10?%;padding:0 %?30?% %?40?%;position:relative;margin:%?78?% %?20?% 0}.pay-result .contain .tips-icon[data-v-69a7a1e8]{width:%?112?%;height:%?112?%}.pay-result .contain .header[data-v-69a7a1e8]{position:absolute;left:50%;-webkit-transform:translateX(-50%);transform:translateX(-50%);top:%?-50?%}.pay-result .contain .info[data-v-69a7a1e8]{padding-top:%?180?%}.pay-result .contain .line[data-v-69a7a1e8]{border-top:1px solid #e5e5e5}.pay-result .contain .plain[data-v-69a7a1e8]{border:1px solid #ff2c3c}',""]),e.exports=t},c5e4:function(e,t,a){"use strict";a("7a82");var n=a("ee27").default;Object.defineProperty(t,"__esModule",{value:!0}),t.cancelOrder=function(e){return r.default.post("order/cancel",{id:e})},t.confirmOrder=function(e){return r.default.post("order/confirm",{id:e})},t.delOrder=function(e){return r.default.post("order/del",{id:e})},t.getOrderDetail=function(e){return r.default.get("order/getOrderDetail",{params:{id:e}})},t.getOrderList=function(e){return r.default.get("order/lists",{params:e})},t.getPayResult=function(e){return r.default.get("order/pay_result",{params:e})},t.getwechatSyncCheck=function(e){return r.default.get("order/wechatSyncCheck",{params:e})},t.getwxReceiveDetail=function(e){return r.default.get("order/wxReceiveDetail",{params:e})},t.orderBuy=function(e){return r.default.post("order/submitOrder",e)},t.orderInfo=function(e){return r.default.post("order/settlement",e)},t.orderTraces=function(e){return r.default.get("order/orderTraces",{params:{id:e}})};var r=n(a("2774"));a("a5ae")},ed70:function(e,t,a){"use strict";var n=a("343a"),r=a.n(n);r.a},fb26:function(e,t,a){"use strict";a("7a82"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=a("c5e4"),r={data:function(){return{payInfo:{},from:""}},onLoad:function(){var e=this.$Route.query;console.log(e),this.id=e.id,this.from=e.from,this.getOrderResultFun()},methods:{getOrderResultFun:function(){var e=this;(0,n.getPayResult)({id:this.id,from:this.from}).then((function(t){1==t.code&&(e.payInfo=t.data)}))}},computed:{getPagesUrl:function(){switch(this.from){case"integral":return"/bundle/pages/exchange_order/exchange_order";default:return"/bundle/pages/user_order/user_order"}}}};t.default=r}}]);