(window.webpackJsonp=window.webpackJsonp||[]).push([[30,17],{477:function(t,e,o){var content=o(480);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,o(17).default)("7c52e05d",content,!0,{sourceMap:!1})},478:function(t,e,o){"use strict";o.r(e);o(473);var n={data:function(){return{priceSlice:{}}},components:{},props:{firstSize:{type:Number,default:14},secondSize:{type:Number,default:14},color:{type:String},weight:{type:[String,Number],default:400},price:{type:[String,Number],default:""},showSubscript:{type:Boolean,default:!0},subscriptSize:{type:Number,default:14},lineThrough:{type:<PERSON>olean,default:!1}},created:function(){this.priceFormat()},watch:{price:function(t){this.priceFormat()}},methods:{priceFormat:function(){var t=this.price,e={};null!==t&&(t=String(t).split("."),e.first=t[0],e.second=t[1],this.priceSlice=e)}}},r=(o(479),o(8)),component=Object(r.a)(n,(function(){var t=this,e=t._self._c;return e("span",{class:(t.lineThrough?"line-through":"")+"price-format",style:{color:t.color,"font-weight":t.weight}},[t.showSubscript?e("span",{style:{"font-size":t.subscriptSize+"px","margin-right":"1px"}},[t._v("¥")]):t._e(),t._v(" "),e("span",{style:{"font-size":t.firstSize+"px","margin-right":"1px"}},[t._v(t._s(t.priceSlice.first))]),t._v(" "),t.priceSlice.second?e("span",{style:{"font-size":t.secondSize+"px"}},[t._v("."+t._s(t.priceSlice.second))]):t._e()])}),[],!1,null,null,null);e.default=component.exports},479:function(t,e,o){"use strict";o(477)},480:function(t,e,o){var n=o(16)(!1);n.push([t.i,".price-format{display:flex;align-items:baseline}",""]),t.exports=n},481:function(t,e,o){var content=o(483);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,o(17).default)("4bccbd40",content,!0,{sourceMap:!1})},482:function(t,e,o){"use strict";o(481)},483:function(t,e,o){var n=o(16)(!1);n.push([t.i,".null-data[data-v-93598fb0]{padding:100px}.null-data .img-null[data-v-93598fb0]{width:150px;height:150px}",""]),t.exports=n},484:function(t,e,o){"use strict";o.r(e);var n={components:{},props:{img:{type:String},text:{type:String,default:"暂无数据"},imgStyle:{type:String,default:""}},methods:{}},r=(o(482),o(8)),component=Object(r.a)(n,(function(){var t=this,e=t._self._c;return e("div",{staticClass:"bg-white flex-col col-center null-data"},[e("img",{staticClass:"img-null",style:t.imgStyle,attrs:{src:t.img,alt:""}}),t._v(" "),e("div",{staticClass:"muted mt8"},[t._v(t._s(t.text))])])}),[],!1,null,"93598fb0",null);e.default=component.exports},485:function(t,e,o){"use strict";o.r(e);o(473),o(86),o(62),o(12),o(107),o(40),o(106);var n=6e4,r=36e5,c=24*r;function l(t){return(0+t.toString()).slice(-2)}var d={components:{},props:{isSlot:{type:Boolean,default:!1},time:{type:Number,default:0},format:{type:String,default:"hh:mm:ss"},autoStart:{type:Boolean,default:!0}},watch:{time:{immediate:!0,handler:function(t){t&&this.reset()}}},data:function(){return{timeObj:{},formateTime:0}},created:function(){},computed:{},methods:{createTimer:function(t){return setTimeout(t,100)},isSameSecond:function(t,e){return Math.floor(t)===Math.floor(e)},start:function(){this.counting||(this.counting=!0,this.endTime=Date.now()+1e3*this.remain,this.setTimer())},setTimer:function(){var t=this;this.tid=this.createTimer((function(){var e=t.getRemain();t.isSameSecond(e,t.remain)&&0!==e||t.setRemain(e),0!==t.remain&&t.setTimer()}))},getRemain:function(){return Math.max(this.endTime-Date.now(),0)},pause:function(){this.counting=!1,clearTimeout(this.tid)},reset:function(){this.pause(),this.remain=this.time,this.setRemain(this.remain),this.autoStart&&this.start()},setRemain:function(t){var e=this.format;this.remain=t;var time,o=(time=t,{days:Math.floor(time/c),hours:l(Math.floor(time%c/r)),minutes:l(Math.floor(time%r/n)),seconds:l(Math.floor(time%n/1e3))});this.formateTime=function(t,e){var o=e.days,n=e.hours,r=e.minutes,c=e.seconds;return-1!==t.indexOf("dd")&&(t=t.replace("dd",o)),-1!==t.indexOf("hh")&&(t=t.replace("hh",l(n))),-1!==t.indexOf("mm")&&(t=t.replace("mm",l(r))),-1!==t.indexOf("ss")&&(t=t.replace("ss",l(c))),t}(e,o),this.$emit("change",o),0===t&&(this.pause(),this.$emit("finish"))}}},m=o(8),component=Object(m.a)(d,(function(){var t=this,e=t._self._c;return t.time>=0?e("div",[e("client-only",[t.isSlot?t._t("default"):e("span",[t._v(t._s(t.formateTime))])],2)],1):t._e()}),[],!1,null,null,null);e.default=component.exports},504:function(t,e,o){t.exports=o.p+"img/news_null.856b3f3.png"},507:function(t,e,o){var content=o(518);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,o(17).default)("c10e5ca6",content,!0,{sourceMap:!1})},517:function(t,e,o){"use strict";o(507)},518:function(t,e,o){var n=o(16)(!1);n.push([t.i,".number-box[data-v-1d9d8f36]{display:inline-flex;align-items:center}.number-box .number-input[data-v-1d9d8f36]{position:relative;text-align:center;padding:0;margin:0 6px;align-items:center;justify-content:center}.number-box .minus[data-v-1d9d8f36],.number-box .plus[data-v-1d9d8f36]{width:32px;display:flex;justify-content:center;align-items:center;cursor:pointer}.number-box .plus[data-v-1d9d8f36]{border-radius:0 2px 2px 0}.number-box .minus[data-v-1d9d8f36]{border-radius:2px 0 0 2px}.number-box .disabled[data-v-1d9d8f36]{color:#c8c9cc!important;background:#f7f8fa!important}.number-box .input-disabled[data-v-1d9d8f36]{color:#c8c9cc!important;background-color:#f2f3f5!important}",""]),t.exports=n},528:function(t,e,o){var content=o(551);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,o(17).default)("8604166e",content,!0,{sourceMap:!1})},535:function(t,e,o){"use strict";o.r(e);o(473),o(40),o(12),o(107),o(516),o(86);var n={components:{},props:{value:{type:Number,default:1},bgColor:{type:String,default:" #F2F3F5"},min:{type:Number,default:0},max:{type:Number,default:99999},step:{type:Number,default:1},disabled:{type:Boolean,default:!1},size:{type:[Number,String],default:14},inputWidth:{type:[Number,String],default:64},color:{type:String,default:"#333"},inputHeight:{type:[Number,String],default:32},index:{type:[Number,String],default:""},disabledInput:{type:Boolean,default:!1},positiveInteger:{type:Boolean,default:!0},asyncChange:{type:Boolean,default:!1}},watch:{value:function(t,e){this.changeFromInner||(this.inputVal=t,this.$nextTick((function(){this.changeFromInner=!1})))},inputVal:function(t,e){var o=this;if(""!=t){var n=0;n=/^(?:-?\d+|-?\d{1,3}(?:,\d{3})+)?(?:\.\d+)?$/.test(t)&&t>=this.min&&t<=this.max?t:e,this.positiveInteger&&(t<0||-1!==String(t).indexOf("."))&&(n=e,this.$nextTick((function(){o.inputVal=e}))),this.asyncChange||this.handleChange(n,"change")}}},data:function(){return{inputVal:1,timer:null,changeFromInner:!1,innerChangeTimer:null}},created:function(){this.inputVal=Number(this.value)},computed:{},methods:{btnTouchStart:function(t){this[t]()},minus:function(){this.computeVal("minus")},plus:function(){this.computeVal("plus")},calcPlus:function(t,e){var o,n,r;try{n=t.toString().split(".")[1].length}catch(t){n=0}try{r=e.toString().split(".")[1].length}catch(t){r=0}return((t*(o=Math.pow(10,Math.max(n,r)))+e*o)/o).toFixed(n>=r?n:r)},calcMinus:function(t,e){var o,n,r;try{n=t.toString().split(".")[1].length}catch(t){n=0}try{r=e.toString().split(".")[1].length}catch(t){r=0}return((t*(o=Math.pow(10,Math.max(n,r)))-e*o)/o).toFixed(n>=r?n:r)},computeVal:function(t){if(!this.disabled){var e=0;"minus"===t?e=this.calcMinus(this.inputVal,this.step):"plus"===t&&(e=this.calcPlus(this.inputVal,this.step)),e<this.min||e>this.max||(this.asyncChange?this.$emit("change",e):(this.inputVal=e,this.handleChange(e,t)))}},onBlur:function(t){var e=this,o=0,n=t.target.value;console.log(n),(o=/(^\d+$)/.test(n)?+n:this.min)>this.max?o=this.max:o<this.min&&(o=this.min),this.$nextTick((function(){e.inputVal=o})),this.handleChange(o,"blur")},onFocus:function(){this.$emit("focus")},handleChange:function(t,e){var o=this;this.disabled||(this.innerChangeTimer&&(clearTimeout(this.innerChangeTimer),this.innerChangeTimer=null),this.changeFromInner=!0,this.innerChangeTimer=setTimeout((function(){o.changeFromInner=!1}),150),this.$emit("input",Number(t)),this.$emit(e,{value:Number(t),index:this.index}))}}},r=(o(517),o(8)),component=Object(r.a)(n,(function(){var t=this,e=t._self._c;return e("div",{staticClass:"number-box"},[e("div",{class:{minus:!0,disabled:t.disabled||t.inputVal<=t.min},style:{background:t.bgColor,height:t.inputHeight+"px",color:t.color},on:{click:function(e){return e.stopPropagation(),e.preventDefault(),t.btnTouchStart("minus")}}},[e("div",{style:{fontSize:t.size+"px"}},[t._v("-")])]),t._v(" "),e("input",{directives:[{name:"model",rawName:"v-model",value:t.inputVal,expression:"inputVal"}],class:{"number-input":!0,"input-disabled":t.disabled},style:{color:t.color,fontSize:t.size+"px",background:t.bgColor,height:t.inputHeight+"px",width:t.inputWidth+"px"},attrs:{disabled:t.disabledInput||t.disabled,type:"text"},domProps:{value:t.inputVal},on:{blur:t.onBlur,focus:t.onFocus,input:function(e){e.target.composing||(t.inputVal=e.target.value)}}}),t._v(" "),e("div",{staticClass:"plus",class:{disabled:t.disabled||t.inputVal>=t.max},style:{background:t.bgColor,height:t.inputHeight+"px",color:t.color},on:{click:function(e){return e.stopPropagation(),e.preventDefault(),t.btnTouchStart("plus")}}},[e("div",{style:{fontSize:t.size+"px"}},[t._v("+")])])])}),[],!1,null,"1d9d8f36",null);e.default=component.exports},550:function(t,e,o){"use strict";o(528)},551:function(t,e,o){var n=o(16)(!1);n.push([t.i,".comment-list .comment-con>.item[data-v-4e1720b8]{padding:20px;border-bottom:1px dashed #e5e5e5;align-items:flex-start}.comment-list .comment-con>.item .avatar img[data-v-4e1720b8]{border-radius:50%;width:44px;height:44px}.comment-list .comment-con>.item .comment-imglist[data-v-4e1720b8]{margin-top:10px}.comment-list .comment-con>.item .comment-imglist .item[data-v-4e1720b8]{width:80px;height:80px;margin-right:6px}.comment-list .comment-con>.item .reply[data-v-4e1720b8]{background-color:#f2f2f2;align-items:flex-start;padding:10px}",""]),t.exports=n},597:function(t,e,o){var content=o(691);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,o(17).default)("7d1df6ed",content,!0,{sourceMap:!1})},604:function(t,e,o){"use strict";o.r(e);var n=o(9),r=(o(53),o(473),{components:{},props:{list:{type:Array,default:function(){return[]}},type:Number,goodsId:[String,Number]},data:function(){return{commentList:[],count:0,page:1}},created:function(){this.getCommentList()},methods:{getCommentList:function(){var t=this;return Object(n.a)(regeneratorRuntime.mark((function e(){var o,data;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,t.$get("goods_comment/lists",{params:{type:t.type,goods_id:t.goodsId,page_size:10,page_no:t.page}});case 2:o=e.sent,data=o.data,1==o.code&&(t.commentList=data.lists,t.count=data.count);case 6:case"end":return e.stop()}}),e)})))()},changePage:function(t){this.page=t,this.getCommentList()}}}),c=(o(550),o(8)),component=Object(c.a)(r,(function(){var t=this,e=t._self._c;return e("div",{staticClass:"comment-list"},[e("div",{staticClass:"comment-con"},[t.commentList.length?[t._l(t.commentList,(function(o,n){return e("div",{key:n,staticClass:"item flex"},[e("div",{staticClass:"avatar m-r-8"},[e("img",{attrs:{src:o.avatar,alt:""}})]),t._v(" "),e("div",{staticClass:"content flex-1"},[e("div",[t._v(t._s(o.nickname))]),t._v(" "),e("div",{staticClass:"lighter",staticStyle:{margin:"5px 0 10px"}},[e("span",[t._v(t._s(o.create_time))]),t._v(" "),e("span",[t._v("|")]),t._v(" "),e("span",[t._v("规格："+t._s(o.spec_value_str))])]),t._v(" "),e("div",[t._v("\n                        "+t._s(o.comment)+"\n                    ")]),t._v(" "),e("div",{staticClass:"comment-imglist flex"},t._l(o.image,(function(img,t){return e("div",{key:t,staticClass:"item"},[e("el-image",{staticStyle:{height:"100%",width:"100%"},attrs:{"preview-src-list":o.image,src:img,fit:"contain"}})],1)})),0),t._v(" "),o.reply?e("div",{staticClass:"flex reply m-t-16"},[e("div",{staticClass:"primary flex-none"},[t._v("商家回复：")]),t._v(" "),e("div",{staticClass:"lighter"},[t._v("\n                            "+t._s(o.reply)+"\n                        ")])]):t._e()])])})),t._v(" "),t.count?e("div",{staticClass:"pagination flex row-center",staticStyle:{padding:"38px 0"}},[e("el-pagination",{attrs:{background:"","hide-on-single-page":"",layout:"prev, pager, next",total:t.count,"page-size":10},on:{"current-change":t.changePage}})],1):t._e()]:e("null-data",{attrs:{img:o(504),text:"暂无评价~"}})],2)])}),[],!1,null,"4e1720b8",null);e.default=component.exports;installComponents(component,{NullData:o(484).default})},687:function(t,e){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACUAAAAkCAMAAAA5HAOUAAAAQlBMVEUAAAD/IDD/KDj/Kjr/LDz/KTn/Kzv/Kjr/Kzv/LDz/Kzv/Kzv/Kzv/LDz/Kzv/LDz/LDz/Kzv/Kzv/LDz/LDv/LDyPingBAAAAFXRSTlMAECAwQFBfYHCAj5+gr7C/wNDf7/B6g4n4AAAAvUlEQVQ4y8XUyRKDIBAEUBZlUYxs8/+/mmiMWtQwkFzS51cFtF0y9v9w3oE0gG4iCa/Illo3tTaQgT2Gvnl6q0S+YIEjC4EGODPUz4uXiviZQk0JbkmTEkVJao6AJM7qrM4kIJLM1TYV2a+Yp5E/CggUCp9KeK6jfPUmqyzfRzTW1FguFEu5WochR8yBGEafspgyXcr+ph5db/TEh0aU19o3VHb71oXLuNq6D/ocANcBuxcztviHSGu+/Kc9AXSSLqTq6c2LAAAAAElFTkSuQmCC"},688:function(t,e){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACUAAAAkCAMAAAA5HAOUAAAAS1BMVEUAAABQUFBQUFBVVVVUVFRTU1NTU1NVVVVUVFRUVFRUVFRVVVVVVVVUVFRVVVVUVFRUVFRVVVVVVVVVVVVVVVVUVFRUVFRVVVVVVVUmEHPwAAAAGHRSTlMAECAwQFBfYHCAj5CfoK+wv8DP0N/g7/AGrtdjAAABEUlEQVQ4y8WUy5aDIBBEeUQeUVTUwP3/L53FaJIR1MxsxhX2udBdRakQ//9I+QFkwV5CGkBfUSNty3gBOR5SZtz55IlGiIZ0qqBnEEKISH8C3chKCCFU5nbcb9kG8iz1nsrcE/P2NpPuRu1MMt0CEJ8HyAiwdOZpnUsAefA/zNR+yADJbW4/gqvard3wWG9Ck9SxbJXW+4pMhybKibiuZqYjamLeTpCZrg515FcbnfE1yJPfVTXV6FlodoVSqErF1lD29IQyDnFfimUwPqM87b7UlsH2tbn+WBpW1dL0vZGrO6E+qu4SQOrUsSAzAtHaCIymTvUJcvj+hkKG1JdUAGb7yr2doZxLOL8Ltfbul/+0Lw1XEXqaPu71AAAAAElFTkSuQmCC"},690:function(t,e,o){"use strict";o(597)},691:function(t,e,o){var n=o(16)(!1);n.push([t.i,".goods-details{padding:16px 0 44px}.goods-details .goods-info .goods-swiper{width:400px;border-radius:4px}.goods-details .goods-info .goods-swiper .swiper{margin:10px 0;padding:0 25px;--swiper-navigation-size:15px;--swiper-navigation-color:#888}.goods-details .goods-info .goods-swiper .swiper .swiper-button-next,.goods-details .goods-info .goods-swiper .swiper .swiper-button-prev{top:0;width:25px;height:100%;margin-top:0;background-size:12px 22px}.goods-details .goods-info .goods-swiper .swiper .swiper-button-prev{left:0}.goods-details .goods-info .goods-swiper .swiper .swiper-button-next{right:0}.goods-details .goods-info .goods-swiper .swiper .swiper-item{cursor:pointer;height:66px;width:66px;border:2px solid transparent}.goods-details .goods-info .goods-swiper .swiper .swiper-item~.swiper-item{margin-left:10px}.goods-details .goods-info .goods-swiper .swiper .swiper-item.active{border-color:#ff2c3c}.goods-details .goods-info .goods-swiper .current-img{width:100%;height:400px;padding:15px}.goods-details .goods-info .info-wrap{min-height:486px;min-width:504px;border-radius:4px;padding:20px}.goods-details .goods-info .info-wrap .name{font-size:20px}.goods-details .goods-info .info-wrap .seckill{background-color:#ff2c3c;padding:6px 10px}.goods-details .goods-info .info-wrap .seckill .count-down .item{width:30px;height:30px;background:rgba(0,0,0,.3);text-align:center;line-height:30px;border-radius:4px}.goods-details .goods-info .info-wrap .price-wrap{background:#f6f6f6;background-size:100%;padding:16px}.goods-details .goods-info .info-wrap .price-wrap .member-price{background-color:#482406;color:#fdebd5;border-radius:100px 100px 100px 0;padding:0 6px}.goods-details .goods-info .info-wrap .sales-click{border-bottom:1px dashed hsla(0,0%,89.8%,.89804);line-height:28px;padding:6px}.goods-details .goods-info .info-wrap .sales-click>div:first-of-type{border-right:1px solid hsla(0,0%,89.8%,.89804)}.goods-details .goods-info .info-wrap .spec-wrap{margin-top:20px}.goods-details .goods-info .info-wrap .spec-wrap .spec{align-items:flex-start}.goods-details .goods-info .info-wrap .spec-wrap .spec .spec-name{margin-right:20px;margin-top:6px;flex:none}.goods-details .goods-info .info-wrap .spec-wrap .spec .spec-item{padding:0 20px;line-height:32px;border:1px solid hsla(0,0%,89.8%,.89804);border-radius:2px;margin-right:10px;margin-bottom:10px;cursor:pointer}.goods-details .goods-info .info-wrap .spec-wrap .spec .spec-item.active{color:#ff2c3c;background-color:#ffeeef;border-color:currentColor}.goods-details .goods-info .info-wrap .goods-num{margin-bottom:30px}.goods-details .goods-info .info-wrap .goods-num .num{margin-right:20px}.goods-details .goods-info .info-wrap .goods-btns .btn{margin-right:14px;text-align:center;width:120px;font-size:16px}.goods-details .goods-info .info-wrap .goods-btns .btn.collection{width:146px;line-height:42px;border:1px solid hsla(0,0%,89.8%,.89804);background-color:#fff;border-radius:4px;cursor:pointer;color:#666}.goods-details .goods-info .info-wrap .goods-btns .btn.collection:hover{color:#ff2c3c}.goods-details .goods-info .info-wrap .goods-btns .btn.collection .start-icon{width:18.5px;height:18px}.goods-details .goods-info .shop{width:210px;padding:16px}.goods-details .goods-info .shop .logo-img{width:62px;height:62px;border-radius:50%;overflow:hidden}.goods-details .goods-info .shop .el-rate__icon{font-size:16px}.goods-details .details-wrap{align-items:stretch}.goods-details .details-wrap .details{padding:10px 0;overflow:hidden}.goods-details .details-wrap .details .rich-text{padding:0 10px;width:100%;overflow:hidden}.goods-details .details-wrap .details .rich-text img{width:100%;display:block}.goods-details .details-wrap .details .rich-text p{margin:0}.goods-details .details-wrap .details .evaluation .evaluation-hd{height:80px;margin:0 10px}.goods-details .details-wrap .details .evaluation .evaluation-hd .rate{height:60px;width:220px;border-right:1px solid #e5e5e5;padding-left:10px;margin-right:40px}.goods-details .details-wrap .details .evaluation .evaluation-tab{margin:16px 20px}.goods-details .details-wrap .details .evaluation .evaluation-tab .item{border-radius:2px;cursor:pointer;height:32px;padding:6px 20px;color:#666;background-color:#f2f2f2;margin-right:10px}.goods-details .details-wrap .details .evaluation .evaluation-tab .item.active{color:#fff;background-color:#ff2c3c}.goods-details .mobile-code{width:210px;margin-left:16px}.goods-details .mobile-code .title{border-bottom:1px solid hsla(0,0%,89.8%,.89804);height:55px}.goods-details .goods-like{width:210px;border-right:1px solid hsla(0,0%,89.8%,.89804)}.goods-details .goods-like .title{border-bottom:1px solid hsla(0,0%,89.8%,.89804);height:55px}.goods-details .goods-like .goods-list .item{padding:10px;display:block}.goods-details .goods-like .goods-list .item .goods-img{width:190px;height:190px;margin-bottom:10px}",""]),t.exports=n},730:function(t,e,o){"use strict";o.r(e);o(66),o(29),o(473),o(26),o(20),o(25),o(30),o(31);var n=o(532),r=o(10),c=o(9),l=(o(53),o(86),o(12),o(21),o(62),o(191),o(505),o(108),o(13)),d=(o(102),o(689));function m(object,t){var e=Object.keys(object);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(object);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(object,t).enumerable}))),e.push.apply(e,o)}return e}function h(t){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?m(Object(source),!0).forEach((function(e){Object(r.a)(t,e,source[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(source)):m(Object(source)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(source,e))}))}return t}var f={components:{VueQr:o.n(d).a},head:function(){return{title:this.$store.getters.headTitle,link:[{rel:"icon",type:"image/x-icon",href:this.$store.getters.favicon}]}},asyncData:function(t){return Object(c.a)(regeneratorRuntime.mark((function e(){var o,n,r,c,data,code;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return o=t.params,n=t.$get,r=t.app,e.next=3,n("goods/getGoodsDetail",{params:{goods_id:o.id}});case 3:return c=e.sent,data=c.data,code=c.code,c.msg,0==code&&setTimeout((function(){return r.router.back()}),1500),e.abrupt("return",{goodsDetails:data,goodsImage:data.goods_image,activity:data.activity,shop:data.shop});case 9:case"end":return e.stop()}}),e)})))()},data:function(){return{goodsDetails:{},goodsImage:[],activity:{},shop:{goods_list:[]},swiperOptions:{pagination:{el:".swiper-pagination",clickable:!0},navigation:{nextEl:".swiper-button-next",prevEl:".swiper-button-prev"},preventClicks:!0,slidesPerView:"auto"},active:"0",commentActive:0,swiperIndex:0,checkedGoods:{},comment:{},goodsNum:1,goodsSpec:[],id:"",timeData:{}}},created:function(){this.id=this.$route.params.id,this.getComment(this.id)},methods:h(h({},Object(l.b)(["getPublicData"])),{},{onClickSlide:function(t){this.swiperIndex=t},onChoseSpecItem:function(t,e){var o=this.goodsSpec;o.forEach((function(o){o.spec_value&&o.id==t&&o.spec_value.forEach((function(t){t.checked=0,t.id==e&&(t.checked=1)}))})),this.goodsSpec=Object(n.a)(o)},onAddCart:function(){var t=this;return Object(c.a)(regeneratorRuntime.mark((function e(){var o,n,r,code,c;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return o=t.goodsNum,n=t.checkedGoods.id,e.next=3,t.$post("cart/add",{item_id:n,goods_num:o});case 3:r=e.sent,code=r.code,r.data,c=r.msg,1==code&&(t.getPublicData(),t.$message({message:c,type:"success"}));case 8:case"end":return e.stop()}}),e)})))()},changeShopFollow:function(){var t=this;return Object(c.a)(regeneratorRuntime.mark((function e(){var o,code,n;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,t.$post("shop_follow/changeStatus",{shop_id:t.shop.id});case 2:o=e.sent,code=o.code,n=o.msg,1==code&&(t.$message({message:n,type:"success"}),t.getGoodsDetail());case 6:case"end":return e.stop()}}),e)})))()},onBuyNow:function(){var t=this.goodsNum,e=[{item_id:this.checkedGoods.id,num:t,goods_id:this.id,shop_id:this.shop.id}];this.$router.push({path:"/confirm_order",query:{data:encodeURIComponent(JSON.stringify({goods:e,type:"buy"}))}})},getGoodsDetail:function(){var t=this;return Object(c.a)(regeneratorRuntime.mark((function e(){var o,data;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,t.$get("goods/getGoodsDetail",{params:{goods_id:t.id}});case 2:o=e.sent,data=o.data,1==o.code&&(t.goodsDetails=data,t.shop=data.shop);case 6:case"end":return e.stop()}}),e)})))()},onCollectionGoods:function(){var t=this;return Object(c.a)(regeneratorRuntime.mark((function e(){var o,code,n;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,t.$post("goods_collect/changeStatus",{goods_id:t.id});case 2:o=e.sent,o.data,code=o.code,n=o.msg,1==code&&(t.$message({message:n,type:"success"}),t.getGoodsDetail());case 7:case"end":return e.stop()}}),e)})))()},getComment:function(){var t=this;return Object(c.a)(regeneratorRuntime.mark((function e(){var o,data;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,t.$get("/goods_comment/category",{params:{goods_id:t.id}});case 2:o=e.sent,data=o.data,1==o.code&&(t.comment=data,t.commentActive=data.comment[0].id);case 6:case"end":return e.stop()}}),e)})))()},onChangeDate:function(t){var e={};for(var o in t)"milliseconds"!==o&&(e[o]=("0"+t[o]).slice(-2));this.timeData=e}}),watch:{goodsSpec:{immediate:!0,handler:function(t){var e=this.goodsDetails.goods_item,o=[];if(t.forEach((function(t){t.spec_value&&t.spec_value.forEach((function(t){t.checked&&o.push(t.id)}))})),o.length){var n=o.join(","),r=e.findIndex((function(t){return t.spec_value_ids==n}));-1==r&&(r=0),this.checkedGoods=e[r],console.log(this.checkedGoods)}}},goodsDetails:{immediate:!0,handler:function(t){t.goods_spec&&(t.goods_spec.forEach((function(t){t.spec_value.forEach((function(t,e){t.checked=0==e?1:0}))})),this.goodsSpec=Object(n.a)(t.goods_spec))}}},computed:h(h({countTime:function(){var t=this.activity.end_time;return t?t-Date.now()/1e3:0}},Object(l.d)(["config"])),{},{mobileLink:function(){var t;return"".concat(null===(t=this.config)||void 0===t?void 0:t.base_domain,"mobile/pages/goods_details/goods_details?id=").concat(this.id)},shopLink:function(){var t;return"".concat(null===(t=this.config)||void 0===t?void 0:t.base_domain,"mobile/pages/store_index/store_index?id=").concat(this.shop.id)}})},v=(o(690),o(8)),component=Object(v.a)(f,(function(){var t=this,e=t._self._c;return t.goodsDetails.id?e("div",{staticClass:"goods-details"},[e("div",{staticClass:"goods-info flex col-stretch"},[e("div",{staticClass:"goods-swiper bg-white flex-col"},[e("el-image",{staticClass:"current-img",attrs:{"preview-src-list":t.goodsImage.map((function(t){return t.uri})),src:t.goodsImage[t.swiperIndex].uri}}),t._v(" "),e("client-only",[e("swiper",{ref:"mySwiper",staticClass:"swiper",attrs:{options:t.swiperOptions}},[t._l(t.goodsImage,(function(o,n){return e("swiper-slide",{key:n,class:{"swiper-item":!0,active:n===t.swiperIndex}},[e("div",{staticStyle:{width:"100%",height:"100%"},on:{mouseover:function(e){t.swiperIndex=n}}},[e("el-image",{staticStyle:{width:"100%",height:"100%"},attrs:{src:o.uri}})],1)])})),t._v(" "),e("div",{staticClass:"swiper-button-prev",attrs:{slot:"button-prev"},slot:"button-prev"}),t._v(" "),e("div",{staticClass:"swiper-button-next",attrs:{slot:"button-next"},slot:"button-next"})],2)],1)],1),t._v(" "),e("div",{staticClass:"info-wrap bg-white flex-1"},[e("div",{staticClass:"name weight-500 m-b-16"},[t._v("\n                "+t._s(t.goodsDetails.name)+"\n            ")]),t._v(" "),1==t.activity.type?e("div",{staticClass:"seckill flex white"},[e("div",{staticClass:"xxl"},[t._v("限时秒杀")]),t._v(" "),e("div",{staticClass:"flex"},[e("div",{staticClass:"white m-r-16"},[t._v("距离结束还有")]),t._v(" "),e("count-down",{attrs:{time:t.countTime,"is-slot":!0},on:{change:t.onChangeDate}},[e("div",{staticClass:"flex row-center count-down xxl"},[e("div",{staticClass:"item white"},[t._v("\n                                "+t._s(t.timeData.hours)+"\n                            ")]),t._v(" "),e("div",{staticClass:"white",staticStyle:{margin:"0 4px"}},[t._v(":")]),t._v(" "),e("div",{staticClass:"item white"},[t._v("\n                                "+t._s(t.timeData.minutes)+"\n                            ")]),t._v(" "),e("div",{staticClass:"white",staticStyle:{margin:"0 4px"}},[t._v(":")]),t._v(" "),e("div",{staticClass:"item white"},[t._v("\n                                "+t._s(t.timeData.seconds)+"\n                            ")])])])],1)]):t._e(),t._v(" "),e("div",{staticClass:"price-wrap lighter"},[e("div",{staticClass:"flex row-between"},[e("div",{staticClass:"price"},[Number(t.checkedGoods.market_price||t.goodsDetails.market_price)?e("div",{staticClass:"flex"},[t._v("\n                            "+t._s(1==t.activity.type?"日常价":"原价")+"\n                            "),e("span",{staticClass:"m-l-20"},[e("price-formate",{attrs:{price:t.checkedGoods.market_price||t.goodsDetails.market_price,weight:400}})],1)]):t._e(),t._v(" "),e("div",{staticClass:"flex m-t-10"},[t._v("\n                            "+t._s(1==t.activity.type?"秒杀价":"价格")+"\n                            "),e("div",{staticClass:"primary m-l-20"},[e("price-formate",{attrs:{price:t.checkedGoods.price||t.goodsDetails.min_price,"subscript-size":16,"first-size":26,"second-size":16}})],1),t._v(" "),1!=t.activity.type&&(t.checkedGoods.member_price||t.goodsDetails.member_price)?e("div",{staticClass:"member-price m-l-10 flex xs"},[t._v("\n                                会员价\n                                "),e("price-formate",{attrs:{price:t.checkedGoods.member_price||t.goodsDetails.member_price,weight:400}})],1):t._e()])]),t._v(" "),e("div",{staticClass:"rate flex-col row-right"},[e("div",{staticClass:"primary",staticStyle:{"font-size":"20px"}},[t._v("\n                            "+t._s(t.comment.percent)+"\n                        ")]),t._v(" "),e("div",{staticClass:"lighter"},[t._v("好评率")])])])]),t._v(" "),e("div",{staticClass:"sales-click muted sm"},[e("span",{staticClass:"m-6"},[t._v("\n                    销量："+t._s(t.goodsDetails.sales_sum)+"件\n                ")]),t._v("\n                ｜\n                "),e("span",{staticClass:"m-6"},[t._v("\n                    浏览量："+t._s(t.goodsDetails.clicks)+"次\n                ")])]),t._v(" "),e("div",{staticClass:"spec-wrap"},t._l(t.goodsSpec,(function(o,n){return e("div",{key:n,staticClass:"spec flex m-b-16"},[e("div",{staticClass:"lighter spec-name"},[t._v(t._s(o.name))]),t._v(" "),e("div",{staticClass:"spec-list flex flex-wrap"},t._l(o.spec_value,(function(n,r){return e("div",{key:r,class:["spec-item lighter",{active:n.checked}],on:{click:function(e){return t.onChoseSpecItem(o.id,n.id)}}},[t._v("\n                            "+t._s(n.value)+"\n                        ")])})),0)])})),0),t._v(" "),e("div",{staticClass:"goods-num flex"},[e("div",{staticClass:"num lighter"},[t._v("数量")]),t._v(" "),e("number-box",{attrs:{min:1,max:t.checkedGoods.stock},model:{value:t.goodsNum,callback:function(e){t.goodsNum=e},expression:"goodsNum"}}),t._v(" "),t.goodsDetails.is_show_stock?e("div",{staticClass:"m-l-10"},[t._v("\n                    库存: "+t._s(t.checkedGoods.stock)+"\n                ")]):t._e()],1),t._v(" "),e("div",{staticClass:"goods-btns flex lg"},[e("el-button",{staticClass:"btn white",attrs:{type:"primary"},on:{click:t.onBuyNow}},[t._v("\n                    立即购买\n                ")]),t._v(" "),0==t.goodsDetails.type&&1!=t.activity.type?e("el-button",{staticClass:"btn addcart",attrs:{type:"primary",plain:""},on:{click:t.onAddCart}},[t._v("\n                    加入购物车\n                ")]):t._e(),t._v(" "),e("div",{staticClass:"btn collection flex row-center",on:{click:t.onCollectionGoods}},[e("img",{staticClass:"start-icon m-r-8",attrs:{src:t.goodsDetails.is_collect?o(687):o(688)}}),t._v(" "),e("span",[t._v(t._s(t.goodsDetails.is_collect?"取消收藏":"收藏商品"))])])],1)]),t._v(" "),e("div",{staticClass:"shop m-l-16 bg-white"},[e("div",{staticClass:"shop-logo flex-col col-center"},[e("el-image",{staticClass:"logo-img",attrs:{src:t.shop.logo}}),t._v(" "),e("nuxt-link",{staticClass:"m-t-10",attrs:{to:"/shop_street_detail?id=".concat(t.shop.id)}},[1==t.shop.type?e("el-tag",{attrs:{size:"mini"}},[t._v("自营")]):t._e(),t._v(" "),e("span",{staticClass:"weight-500"},[t._v(t._s(t.shop.name))])],1),t._v(" "),e("div",{staticClass:"xs muted m-t-10 line-5"},[t._v("\n                    "+t._s(t.shop.intro)+"\n                ")])],1),t._v(" "),e("div",{staticClass:"flex m-t-20"},[e("div",{staticClass:"flex-1 text-center"},[e("div",{staticClass:"xxl m-b-10"},[t._v(t._s(t.shop.goods_on_sale))]),t._v(" "),e("div",{staticClass:"xs"},[t._v("全部商品")])]),t._v(" "),e("div",{staticClass:"flex-1 text-center"},[e("div",{staticClass:"xxl m-b-10"},[t._v(t._s(t.shop.follow_num))]),t._v(" "),e("div",{staticClass:"xs"},[t._v("关注人数")])])]),t._v(" "),e("el-divider"),t._v(" "),e("div",{staticClass:"flex xs m-b-16"},[e("div",{staticClass:"m-r-12"},[t._v("店铺星级")]),t._v(" "),e("div",{staticClass:"m-t-5"},[e("el-rate",{attrs:{disabled:""},model:{value:t.shop.star,callback:function(e){t.$set(t.shop,"star",e)},expression:"shop.star"}})],1)]),t._v(" "),e("div",{staticClass:"flex xs m-b-16"},[e("div",{staticClass:"m-r-12"},[t._v("店铺评分")]),t._v(" "),e("div",{},[t._v(t._s(t.shop.score)+"分")])]),t._v(" "),e("div",[e("el-button",{attrs:{type:"primary",size:"mini"},on:{click:function(e){return t.$router.push("/shop_street_detail?id=".concat(t.shop.id))}}},[t._v("进入店铺")]),t._v(" "),e("el-button",{attrs:{size:"mini"},on:{click:t.changeShopFollow}},[t._v(t._s(1==t.shop.shop_follow_status?"已关注":"关注店铺"))])],1),t._v(" "),e("div",{staticClass:"flex m-t-30"},[e("el-popover",{attrs:{placement:"top",width:"200",trigger:"hover"}},[e("div",[e("el-image",{staticStyle:{width:"100%"},attrs:{src:t.shop.customer_image}})],1),t._v(" "),e("div",{staticClass:"xs lighter text-center cursor-pointer m-r-24",attrs:{slot:"reference"},slot:"reference"},[e("i",{staticClass:"el-icon-chat-dot-round nr"}),t._v(" "),e("span",[t._v("联系客服")])])]),t._v(" "),e("el-popover",{attrs:{placement:"top",trigger:"hover"}},[e("div",[e("vue-qr",{staticClass:"bicode",staticStyle:{width:"100px",height:"100px"},attrs:{logoScale:20,margin:0,dotScale:1,text:t.shopLink}})],1),t._v(" "),e("div",{staticClass:"xs lighter cursor-pointer text-center",attrs:{slot:"reference"},slot:"reference"},[e("i",{staticClass:"el-icon-mobile-phone nr"}),t._v(" "),e("span",[t._v("访问店铺")])])])],1)],1)]),t._v(" "),e("div",{staticClass:"details-wrap flex m-t-16"},[t.shop.goods_list.length?e("div",{staticClass:"goods-like"},[e("div",{staticClass:"title bg-white flex p-l-15"},[t._v("店铺推荐")]),t._v(" "),e("div",{staticClass:"goods-list"},[t._l(t.shop.goods_list,(function(o,n){return[n<5?e("nuxt-link",{key:n,staticClass:"item bg-white",attrs:{to:"/goods_details/".concat(o.id)}},[e("el-image",{staticClass:"goods-img",attrs:{src:o.image}}),t._v(" "),e("div",{staticClass:"goods-name line-2"},[t._v("\n                            "+t._s(o.name)+"\n                        ")]),t._v(" "),e("div",{staticClass:"price flex m-t-8"},[e("div",{staticClass:"primary m-r-8"},[e("price-formate",{attrs:{price:o.min_price,"first-size":16}})],1),t._v(" "),e("div",{staticClass:"muted sm line-through"},[e("price-formate",{attrs:{price:o.market_price}})],1)])],1):t._e()]}))],2)]):t._e(),t._v(" "),e("div",{staticClass:"details bg-white flex-1"},[e("el-tabs",{model:{value:t.active,callback:function(e){t.active=e},expression:"active"}},[e("el-tab-pane",{attrs:{label:"商品详情"}},[e("div",{staticClass:"rich-text",domProps:{innerHTML:t._s(t.goodsDetails.content)}})]),t._v(" "),e("el-tab-pane",{attrs:{label:"商品评价"}},[e("div",{staticClass:"evaluation"},[e("div",{staticClass:"evaluation-hd flex"},[e("div",{staticClass:"rate flex"},[e("div",{staticClass:"lighter m-r-8"},[t._v("好评率")]),t._v(" "),e("div",{staticClass:"primary",staticStyle:{"font-size":"30px"}},[t._v("\n                                    "+t._s(t.goodsDetails.comment.percent)+"\n                                ")])]),t._v(" "),e("div",{staticClass:"score flex"},[e("span",{staticClass:"m-r-8 lighter"},[t._v("评分")]),t._v(" "),e("el-rate",{attrs:{value:t.goodsDetails.comment.goods_comment,disabled:"","allow-half":""}})],1)]),t._v(" "),e("div",{staticClass:"evaluation-tab flex"},t._l(t.comment.comment,(function(o,n){return e("div",{key:n,class:["item",{active:t.commentActive==o.id}],on:{click:function(e){t.commentActive=o.id}}},[t._v("\n                                "+t._s(o.name)+"("+t._s(o.count)+")\n                            ")])})),0)]),t._v(" "),e("div",[t._l(t.comment.comment,(function(o,n){return[t.commentActive==o.id?e("comment-list",{key:n,attrs:{"goods-id":t.id,type:o.id}}):t._e()]}))],2)])],1)],1),t._v(" "),e("div",{staticClass:"mobile-code"},[e("div",{staticClass:"bg-white p-b-16"},[e("div",{staticClass:"title flex row-center"},[t._v("手机端访问")]),t._v(" "),e("div",{staticClass:"flex-col col-center m-t-20"},[e("vue-qr",{staticClass:"bicode",staticStyle:{width:"100px",height:"100px"},attrs:{logoScale:20,margin:0,dotScale:1,text:t.mobileLink}}),t._v(" "),e("div",{staticClass:"lighter m-t-10"},[t._v("公众号H5")])],1)])])])]):t._e()}),[],!1,null,null,null);e.default=component.exports;installComponents(component,{CountDown:o(485).default,PriceFormate:o(478).default,NumberBox:o(535).default,CommentList:o(604).default})}}]);