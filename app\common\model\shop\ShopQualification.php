<?php

namespace app\common\model\shop;

use app\common\basics\Models;
use app\common\model\goods\Qualification;

/**
 * 商家资质模型
 * Class ShopQualification
 * @package app\common\model\shop
 */
class ShopQualification extends Models
{
    /**
     * 关联资质表
     */
    public function qualification()
    {
        return $this->belongsTo(Qualification::class, 'qualification_id', 'id');
    }

    /**
     * 关联商家表
     */
    public function shop()
    {
        return $this->belongsTo(\app\common\model\Shop::class, 'shop_id', 'id');
    }

    /**
     * 获取状态描述
     */
    public function getStatusTextAttr($value, $data)
    {
        $status = isset($data['status']) ? intval($data['status']) : 0;
        $statusMap = [
            0 => '待审核',
            1 => '审核通过',
            2 => '审核拒绝'
        ];
        return $statusMap[$status] ?? '未知状态';
    }

    /**
     * 获取过期时间描述
     */
    public function getExpireTimeTextAttr($value, $data)
    {
        $expireTime = isset($data['expire_time']) ? intval($data['expire_time']) : 0;
        if ($expireTime == 0) {
            return '永久有效';
        }
        return date('Y-m-d H:i:s', $expireTime);
    }

    /**
     * 检查是否过期
     */
    public function getIsExpiredAttr($value, $data)
    {
        $expireTime = isset($data['expire_time']) ? intval($data['expire_time']) : 0;
        if ($expireTime == 0) {
            return false; // 永久有效
        }
        return time() > $expireTime;
    }

    /**
     * 获取审核时间描述
     */
    public function getAuditTimeTextAttr($value, $data)
    {
        $auditTime = isset($data['audit_time']) ? intval($data['audit_time']) : 0;
        if ($auditTime == 0) {
            return '未审核';
        }
        return date('Y-m-d H:i:s', $auditTime);
    }
}
