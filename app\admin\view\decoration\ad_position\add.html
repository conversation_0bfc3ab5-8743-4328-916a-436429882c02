{layout name="layout2" /}
<style>
    .tips{
        color: red;
    }
</style>
<div class="layui-form" lay-filter="layuiadmin-form" id="layuiadmin-form" style="padding: 20px 30px 0 0;">
    <div class="layui-form-item">
        <label class="layui-form-label"><span class="tips">*</span>广告位：</label>
        <div class="layui-input-inline">
            <input type="text" name="name" lay-vertype="tips" autocomplete="off" class="layui-input">
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label" style="white-space:nowrap;">广告位尺寸：</label>
        <div class="layui-input-inline">
            <input type="text" name="size" class="layui-input">
            <div class=" layui-form-mid layui-word-aux" style="white-space:nowrap;">广告位尺寸，填写格式：300*300，单位：px,用于提醒需要上传的广告尺寸</div>
        </div>
    </div>

    <div class="layui-form-item">
        <lable class="layui-form-label" style="white-space:nowrap;">切换时间:</lable>
        <div class="layui-input-inline">
            <input type="number" min="0" name="lun_time" value="3"  class="layui-input" step="0.01"  min="0" onkeyup="this.value= this.value.match(/\d+(\.\d{0,2})?/) ? this.value.match(/\d+(\.\d{0,2})?/)[0] : ''">
        </div>
        <div class="layui-form-mid layui-word-aux" style="white-space:nowrap;margin-left:140px;">单位秒</div>
    </div>

    <div class="layui-form-item">
        <lable class="layui-form-label" style="white-space:nowrap;">广告数量:</lable>
        <div class="layui-input-inline">
            <input type="number" min="1" name="lun_time" value=""  class="layui-input" step="0.01"  min="0" onkeyup="this.value= this.value.match(/\d+(\.\d{0,2})?/) ? this.value.match(/\d+(\.\d{0,2})?/)[0] : ''">
        </div>
        <div class="layui-form-mid layui-word-aux" style="white-space:nowrap;margin-left:140px;">广告位可展示图片数量</div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label" style="white-space:nowrap;">位置指示图：</label>
        <div class="layui-input-block">
            <div class="like-upload-image">
                <div class="upload-image-elem"><a class="add-upload-image"> + 添加图片</a></div>
            </div>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label" style="white-space:nowrap;">广告位状态：</label>
        <div class="layui-input-inline">
            <input type="radio" name="status" value=0 title="停用" checked>
            <input type="radio" name="status" value=1 title="启用">
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label" style="white-space:nowrap;">出售状态：</label>
        <div class="layui-input-inline">
            <input type="radio" name="is_buy" value='0' title="不出售" checked>
            <input type="radio" name="is_buy" value='1' title="出售">
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label" style="white-space:nowrap;">计费周期：</label>
        <div class="layui-input-inline">
            <input type="radio" name="billing_cycle" value='0' title="天" checked>
            <input type="radio" name="billing_cycle" value='1' title="周">
            <input type="radio" name="billing_cycle" value='2' title="月">
            <input type="radio" name="billing_cycle" value='3' title="年">
        </div>
    </div>
    <div class="layui-form-item">
        <lable class="layui-form-label" style="white-space:nowrap;">广告费用:</lable>
        <div class="layui-input-inline">
            <input type="number" min="0" name="ad_fee"  class="layui-input" step="0.01"  min="0" onkeyup="this.value= this.value.match(/\d+(\.\d{0,2})?/) ? this.value.match(/\d+(\.\d{0,2})?/)[0] : ''">
        </div>
        <div class="layui-form-mid layui-word-aux" style="white-space:nowrap;margin-left:140px;">费用为所选周期的总费用</div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label">位置标识X：</label>
        <div class="layui-input-inline">
            <input type="text" name="ad_sn" lay-vertype="tips" required="required" autocomplete="off" class="layui-input">
            <div class="layui-form-mid layui-word-aux" >定位使用，添加后尽量不要修改</div>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">位置标识Y：</label>
        <div class="layui-input-inline">
            <input type="text" name="ad_yn" lay-vertype="tips" required="required" autocomplete="off" class="layui-input">
            <div class="layui-form-mid layui-word-aux" >定位使用，添加后尽量不要修改</div>
        </div>
    </div>
    <div class="layui-form-item layui-hide">
        <input type="button" lay-submit lay-filter="addSubmit" id="addSubmit" value="确认">
    </div>
</div>
<script>
    layui.use(["table", "laydate","form"], function(){
        var table   = layui.table;
        var element = layui.element;
        var form = layui.form;
        var laydate = layui.laydate;

        laydate.render({type:'datetime',elem:'#start_time',trigger:'click'});
        laydate.render({type:'datetime',elem:'#end_time',trigger:'click'});


        like.delUpload();
        $(document).on("click", ".add-upload-image", function () {
            like.imageUpload({
                limit: 1,
                field: "image",
                that: $(this)
            });
        })
    })
</script>