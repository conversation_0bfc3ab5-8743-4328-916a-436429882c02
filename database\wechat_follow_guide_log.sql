-- ----------------------------
-- Table structure for ls_wechat_follow_guide_log
-- ----------------------------
DROP TABLE IF EXISTS `ls_wechat_follow_guide_log`;
CREATE TABLE `ls_wechat_follow_guide_log` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `show_date` date NOT NULL COMMENT '展示日期',
  `show_count` int(11) DEFAULT '1' COMMENT '当日展示次数',
  `create_time` int(11) DEFAULT NULL,
  `update_time` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `idx_user_date` (`user_id`, `show_date`) USING BTREE,
  KEY `idx_user_id` (`user_id`) USING BTREE,
  KEY `idx_show_date` (`show_date`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='微信关注引导展示记录表';
