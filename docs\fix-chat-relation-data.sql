-- 修复聊天关系表中的错误数据
-- 问题：用户对用户聊天记录中，kefu_id字段存储了错误的客服ID，应该存储联系人用户ID

-- 1. 查看有问题的数据
SELECT 
    cr.id,
    cr.shop_id,
    cr.user_id,
    cr.kefu_id,
    cr.nickname,
    cr.create_time,
    cr.update_time,
    '检查是否存在对应用户' as check_type,
    u.id as user_exists
FROM ls_chat_relation cr
LEFT JOIN ls_user u ON u.id = cr.kefu_id
WHERE cr.shop_id = 0 AND u.id IS NULL;

-- 2. 修复错误数据的SQL脚本
-- 对于shop_id=0但kefu_id不是有效用户ID的记录，从聊天记录中找到正确的联系人

UPDATE ls_chat_relation cr
SET kefu_id = (
    SELECT CASE 
        WHEN chat.from_id = cr.user_id THEN chat.to_id
        ELSE chat.from_id
    END
    FROM ls_chat_record chat
    WHERE chat.shop_id = 0
    AND (chat.from_id = cr.user_id OR chat.to_id = cr.user_id)
    AND chat.from_type = 'user' 
    AND chat.to_type = 'user'
    AND (
        (chat.from_id = cr.user_id AND chat.to_id != cr.user_id) OR
        (chat.to_id = cr.user_id AND chat.from_id != cr.user_id)
    )
    ORDER BY chat.id DESC
    LIMIT 1
),
nickname = (
    SELECT u.nickname
    FROM ls_user u
    WHERE u.id = (
        SELECT CASE 
            WHEN chat.from_id = cr.user_id THEN chat.to_id
            ELSE chat.from_id
        END
        FROM ls_chat_record chat
        WHERE chat.shop_id = 0
        AND (chat.from_id = cr.user_id OR chat.to_id = cr.user_id)
        AND chat.from_type = 'user' 
        AND chat.to_type = 'user'
        AND (
            (chat.from_id = cr.user_id AND chat.to_id != cr.user_id) OR
            (chat.to_id = cr.user_id AND chat.from_id != cr.user_id)
        )
        ORDER BY chat.id DESC
        LIMIT 1
    )
),
avatar = (
    SELECT u.avatar
    FROM ls_user u
    WHERE u.id = (
        SELECT CASE 
            WHEN chat.from_id = cr.user_id THEN chat.to_id
            ELSE chat.from_id
        END
        FROM ls_chat_record chat
        WHERE chat.shop_id = 0
        AND (chat.from_id = cr.user_id OR chat.to_id = cr.user_id)
        AND chat.from_type = 'user' 
        AND chat.to_type = 'user'
        AND (
            (chat.from_id = cr.user_id AND chat.to_id != cr.user_id) OR
            (chat.to_id = cr.user_id AND chat.from_id != cr.user_id)
        )
        ORDER BY chat.id DESC
        LIMIT 1
    )
)
WHERE cr.shop_id = 0 
AND NOT EXISTS (
    SELECT 1 FROM ls_user u WHERE u.id = cr.kefu_id
);

-- 3. 验证修复结果
SELECT 
    cr.id,
    cr.shop_id,
    cr.user_id,
    cr.kefu_id,
    cr.nickname,
    cr.avatar,
    u.nickname as contact_user_nickname,
    u.avatar as contact_user_avatar,
    '修复后验证' as check_type
FROM ls_chat_relation cr
LEFT JOIN ls_user u ON u.id = cr.kefu_id
WHERE cr.shop_id = 0;

-- 4. 删除无法修复的错误记录（如果没有对应的聊天记录）
DELETE FROM ls_chat_relation 
WHERE shop_id = 0 
AND NOT EXISTS (
    SELECT 1 FROM ls_user u WHERE u.id = kefu_id
)
AND NOT EXISTS (
    SELECT 1 FROM ls_chat_record chat 
    WHERE chat.shop_id = 0 
    AND (chat.from_id = user_id OR chat.to_id = user_id)
    AND chat.from_type = 'user' 
    AND chat.to_type = 'user'
);

-- 5. 针对具体的错误记录（ID=57）进行修复
-- 根据聊天记录，用户81和用户80聊天，所以kefu_id应该是80
UPDATE ls_chat_relation 
SET 
    kefu_id = 80,
    nickname = (SELECT nickname FROM ls_user WHERE id = 80),
    avatar = (SELECT avatar FROM ls_user WHERE id = 80)
WHERE id = 57 AND shop_id = 0 AND user_id = 81;

-- 6. 验证特定记录的修复
SELECT 
    cr.*,
    u.nickname as contact_nickname,
    u.avatar as contact_avatar
FROM ls_chat_relation cr
LEFT JOIN ls_user u ON u.id = cr.kefu_id
WHERE cr.id = 57;
