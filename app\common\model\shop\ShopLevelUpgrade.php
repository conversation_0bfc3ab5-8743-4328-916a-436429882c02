<?php

namespace app\common\model\shop;

use app\common\basics\Models;

/**
 * 商家等级升级记录模型
 */
class ShopLevelUpgrade extends Models
{
    protected $name = 'shop_level_upgrade';

    /**
     * 关联商家模型
     */
    public function shop()
    {
        return $this->belongsTo(Shop::class, 'shop_id', 'id');
    }

    /**
     * 获取器 - 原等级名称
     */
    public function getFromLevelNameAttr($value, $data)
    {
        $names = [
            0 => '0元入驻',
            1 => '商家会员',
            2 => '实力厂商',
        ];
        return $names[$data['from_level']] ?? '未知等级';
    }

    /**
     * 获取器 - 目标等级名称
     */
    public function getToLevelNameAttr($value, $data)
    {
        $names = [
            0 => '0元入驻',
            1 => '商家会员',
            2 => '实力厂商',
        ];
        return $names[$data['to_level']] ?? '未知等级';
    }

    /**
     * 获取器 - 状态文本
     */
    public function getStatusTextAttr($value, $data)
    {
        $statusTexts = [
            0 => '待支付',
            1 => '已支付',
            2 => '已生效',
        ];
        return $statusTexts[$data['status']] ?? '未知状态';
    }

    /**
     * 获取器 - 支付时间
     */
    public function getPayTimeAttr($value)
    {
        return $value ? date('Y-m-d H:i:s', $value) : null;
    }

    /**
     * 获取器 - 生效时间
     */
    public function getEffectTimeAttr($value)
    {
        return $value ? date('Y-m-d H:i:s', $value) : null;
    }

    /**
     * 获取器 - 到期时间
     */
    public function getExpireTimeAttr($value)
    {
        return $value ? date('Y-m-d H:i:s', $value) : null;
    }

    /**
     * 获取器 - 创建时间
     */
    public function getCreateTimeAttr($value)
    {
        return date('Y-m-d H:i:s', $value);
    }

    /**
     * 获取器 - 更新时间
     */
    public function getUpdateTimeAttr($value)
    {
        return date('Y-m-d H:i:s', $value);
    }

    /**
     * 获取器 - 是否已过期
     */
    public function getIsExpiredAttr($value, $data)
    {
        if (!$data['expire_time'] || $data['status'] != 2) {
            return false;
        }
        return $data['expire_time'] < time();
    }
}
