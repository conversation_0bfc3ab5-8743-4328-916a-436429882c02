<?php

namespace app\common\model\jcai;


use think\Model;

/**
 * 集采众筹
 * Class Jcai
 * @package app\common\model
 */
class Jcai extends Model
{
    const STATUS_WAIT_SUCCESS = 0;
    const STATUS_SUCCESS = 1;
    const STATUS_ERROR = 2;

    //集采众筹状态
    public static function getStatusDesc($type)
    {
        $desc = [
            self::STATUS_WAIT_SUCCESS => '集采众筹中',
            self::STATUS_SUCCESS => '集采众筹成功',
            self::STATUS_ERROR => '集采众筹失败',
        ];

        if ($type === true){
            return $desc;
        }
        return $desc[$type] ?? '';
    }
}