{layout name="layout2" /}
<div class="layui-form" lay-filter="layuiadmin-form-user" id="layuiadmin-form-user" style="padding: 20px 30px 0 0;">

    <div class="layui-form-item">
        <label class="layui-form-label" style="width: 180px">选择会员标签：</label>
        <div class="layui-input-inline" style="width: 420px">
            <div id="tagList"></div>
        </div>
    </div>
    <div class="layui-form-item layui-hide">
        <input type="button" lay-submit lay-filter="set_tag-submit" id="set_tag-submit" value="确认">
    </div>
</div>
<script>
    layui.config({
        version:"{$front_version}",
        base: '/static/lib/' //静态资源所在路径
    }).extend({
        xmSelect: 'xmSelect/xm-select'
    }).use(['xmSelect', 'form'], function(){
        var $ = layui.$,form = layui.form ;
        var xmSelect = layui.xmSelect;
        var tag_list = '{$tag_list|raw}';

        var xmIns = xmSelect.render({
            el: '#tagList',
            language: 'zn',
            data: JSON.parse(tag_list),
            prop: {
                value: 'id'
            }
        })
    })
</script>