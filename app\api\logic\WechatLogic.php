<?php
namespace app\api\logic;

use app\common\basics\Logic;
use app\common\model\wechat\Wechat;
use app\common\model\wechat\WechatReply;
use app\common\server\WeChatServer;
use think\facade\Db;
use EasyWeChat\Kernel\Exceptions\Exception;
use EasyWeChat\Kernel\Messages\Text;
use EasyWeChat\Kernel\Messages\Image;
use EasyWeChat\Factory;

class WechatLogic extends Logic
{
    public static function index($params)
    {
        // Token验证 将微信转过来的数据原样返回
        if(isset($params['echostr'])) {
            echo $params['echostr'];
            exit;
        }

        // 接收原始的POST数据
        $post = file_get_contents('php://input');
        $data = json_decode($post, true);
    //    Db::name('log')->insert(['log' => '微信内容安全回调1：' . $post, 'type' => 'wechat_callback']);
        // 优先处理内容安全回调
        if (isset($data['Event']) && $data['Event'] == 'wxa_media_check') {
            Db::name('log')->insert(['log' => '微信内容安全回调：' . $post, 'type' => 'wechat_callback']);
            self::handleMediaCheckCallback($data);
            // 直接响应，不进入后续的EasyWeChat处理流程
            echo 'success';
            exit;
        }
        
        // 获取公众号配置
        $config = WechatServer::getOaConfig();
        $app = Factory::officialAccount($config);

        $app->server->push(function ($message) {
            switch ($message['MsgType']) { // 消息类型
                case WeChat::msg_type_event: // 回复事件
                    switch ($message['Event']) {
                        case WeChat::msg_event_subscribe: // 关注事件
                            $reply_content = WechatReply::where(['reply_type' => WeChat::msg_event_subscribe, 'status' => 1, 'del' => 0])
                                ->value('content');
                            //关注回复空的话，找默认回复
                            if (empty($reply_content)) {
                                $reply_content = WechatReply::where(['reply_type' => WeChat::msg_type_default, 'status' => 1, 'del' => 0])
                                    ->value('content');
                            }
                            if ($reply_content) {
                                $text = new Text($reply_content);
                                return $text;
                            }
                            break;
                        case WeChat::msg_event_click: // 点击事件
                            $reply_data = self::getKeyWordContent($message['EventKey']);
                            if ($reply_data) {
                                // 检查是否为多条消息
                                if ($reply_data['message_count'] > 1) {
                                    // 异步发送多条消息
                                    self::sendMultipleMessagesAsync($reply_data, $message['FromUserName']);
                                    // 返回第一条消息
                                    return new Text($reply_data['content']);
                                } else {
                                    // 单条文本消息
                                    $text = new Text($reply_data['content']);
                                    return $text;
                                }
                            }
                            break;
                    }

                case WeChat::msg_type_text://消息类型
                    // 获取关键字内容
                    $reply_data = self::getKeyWordContent($message['Content']);
                    if (!empty($reply_data)) {
                        // 检查是否为多条消息
                        if ($reply_data['message_count'] > 1) {
                            // 异步发送多条消息
                            self::sendMultipleMessagesAsync($reply_data, $message['FromUserName']);
                            // 返回第一条消息
                            return new Text($reply_data['content']);
                        } else {
                            // 单条文本消息
                            $text = new Text($reply_data['content']);
                            return $text;
                        }
                    } else {
                        // 获取默认内容
                        $reply_content = self::getDefaultReplyContent();
                        if ($reply_content) {
                            $text = new Text($reply_content);
                            return $text;
                        }
                    }
                    break;
            }
        });
        $response = $app->server->serve();
        $response->send();
    }

    /**
     * 获取微信配置
     * @param $url
     * @return array|string
     * @throws \EasyWeChat\Kernel\Exceptions\InvalidConfigException
     * @throws \EasyWeChat\Kernel\Exceptions\RuntimeException
     * @throws \Psr\SimpleCache\InvalidArgumentException
     */
    public static function jsConfig($url)
    {
        $config = WeChatServer::getOaConfig();
        $app = Factory::officialAccount($config);
        $url = urldecode($url);
        $app->jssdk->setUrl($url);
        $apis = ['onMenuShareTimeline', 'onMenuShareAppMessage', 'onMenuShareQQ', 'onMenuShareWeibo', 'onMenuShareQZone', 'openLocation', 'getLocation', 'chooseWXPay', 'updateAppMessageShareData', 'updateTimelineShareData', 'openAddress','closeWindow'];
        try {
            $data = $app->jssdk->getConfigArray($apis, $debug = false, $beta = false);
            return data_success('', $data);
        } catch (Exception $e) {

            return data_error('公众号配置出错' . $e->getMessage());
        }
    }


    /**
     * @notes 获取关键词内容
     * @param $keyword
     * @return mixed|array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date 2022/8/3 17:51
     */
    public static function getKeyWordContent($keyword)
    {
        $reply_list = WechatReply::where(['reply_type' => WeChat::msg_type_text, 'status' => 1, 'del' => 0])
            ->order('sort asc')
            ->select();
        $reply_data = null;
        foreach ($reply_list as $reply) {
            $matched = false;
            switch ($reply['matching_type']) {
                case 1://全匹配
                    if ($reply['keyword'] === $keyword) {
                        $matched = true;
                    }
                    break;
                case 2://模糊匹配
                    if (stripos($reply['keyword'], $keyword) !== false) {
                        $matched = true;
                    }
                    break;
            }
            if($matched) {
                $reply_data = $reply->toArray();
                break; // 得到回复数据，中止循环
            }
        }
        return $reply_data;
    }


    /**
     * @notes 获取默认回复内容
     * @return mixed
     * <AUTHOR>
     * @date 2022/8/3 17:51
     */
    public static function getDefaultReplyContent()
    {
        return WechatReply::where(['reply_type' => WeChat::msg_type_default, 'status' => 1, 'del' => 0])
            ->value('content');
    }

    /**
     * @notes 发送多条消息（立即发送第二条消息）
     * @param array $reply_data 回复数据
     * @param string $openid 用户openid
     * @return void
     * <AUTHOR>
     * @date 2024/12/28
     */
    public static function sendMultipleMessagesAsync($reply_data, $openid)
    {
        if (empty($reply_data) || empty($openid)) {
            return;
        }

        // 立即发送第二条消息
        if ($reply_data['message_count'] > 1 && !empty($reply_data['second_content_type'])) {
            try {
                // 使用客服消息接口立即发送第二条消息
                self::sendSecondMessage($reply_data, $openid);
            } catch (\Exception $e) {
                // 记录错误但不影响第一条消息的发送
                trace('发送第二条消息失败: ' . $e->getMessage(), 'error');
            }
        }
    }

    /**
     * @notes 发送第二条消息
     * @param array $reply_data 回复数据
     * @param string $openid 用户openid
     * @return void
     * <AUTHOR>
     * @date 2024/12/28
     */
    public static function sendSecondMessage($reply_data, $openid)
    {
        try {
            $config = WeChatServer::getOaConfig();
            $app = Factory::officialAccount($config);

            trace('开始发送第二条消息，类型: ' . $reply_data['second_content_type'] . ', openid: ' . $openid, 'info');

            switch ($reply_data['second_content_type']) {
                case WeChat::content_type_text:
                    if (!empty($reply_data['second_content'])) {
                        $result = $app->customer_service->message(new Text($reply_data['second_content']))->to($openid)->send();
                        trace('文本消息发送结果: ' . json_encode($result), 'info');
                    }
                    break;
                case WeChat::content_type_image:
                    if (!empty($reply_data['second_image_url'])) {
                        trace('准备发送图片，URL: ' . $reply_data['second_image_url'], 'info');

                        // 上传图片获取media_id
                        $media_id = self::uploadImageToWechat($reply_data['second_image_url']);
                        trace('图片上传结果，media_id: ' . $media_id, 'info');

                        if ($media_id) {
                            $result = $app->customer_service->message(new Image($media_id))->to($openid)->send();
                            trace('图片消息发送结果: ' . json_encode($result), 'info');
                        } else {
                            trace('图片上传失败，无法发送图片消息', 'error');
                        }
                    }
                    break;
            }
        } catch (\Exception $e) {
            // 记录详细错误日志
            trace('发送第二条消息异常: ' . $e->getMessage() . ', 文件: ' . $e->getFile() . ', 行号: ' . $e->getLine(), 'error');
        }
    }

    /**
     * @notes 上传图片到微信服务器获取media_id
     * @param string $image_url 图片URL
     * @return string|false media_id或false
     * <AUTHOR>
     * @date 2024/12/28
     */
    public static function uploadImageToWechat($image_url)
    {
        try {
            $config = WeChatServer::getOaConfig();
            $app = Factory::officialAccount($config);

            trace('开始上传图片到微信，原始URL: ' . $image_url, 'info');

            // 处理图片路径
            $image_path = '';
            if (strpos($image_url, 'http') === 0) {
                // 完整URL，需要下载到本地
                $image_path = self::downloadImage($image_url);
                trace('下载远程图片到本地: ' . $image_path, 'info');
            } else {
                // 相对路径，转换为绝对路径
                $image_path = app()->getRootPath() . 'public' . $image_url;
                trace('本地图片路径: ' . $image_path, 'info');
            }

            if (!$image_path || !file_exists($image_path)) {
                trace('图片文件不存在: ' . $image_path, 'error');
                return false;
            }

            // 检查文件大小（微信限制2MB）
            $fileSize = filesize($image_path);
            if ($fileSize > 2 * 1024 * 1024) {
                trace('图片文件过大: ' . $fileSize . ' bytes', 'error');
                return false;
            }

            trace('准备上传图片，文件大小: ' . $fileSize . ' bytes', 'info');

            // 使用临时素材接口上传图片
            $result = $app->media->upload('image', $image_path);

            trace('微信上传图片结果: ' . json_encode($result), 'info');

            if (isset($result['media_id'])) {
                trace('图片上传成功，media_id: ' . $result['media_id'], 'info');
                return $result['media_id'];
            }

            trace('图片上传失败，未获取到media_id', 'error');
            return false;
        } catch (\Exception $e) {
            // 记录详细错误日志
            trace('上传图片到微信异常: ' . $e->getMessage() . ', 文件: ' . $e->getFile() . ', 行号: ' . $e->getLine(), 'error');
            return false;
        }
    }

    /**
     * @notes 下载远程图片到本地
     * @param string $url 图片URL
     * @return string|false 本地文件路径或false
     * <AUTHOR>
     * @date 2024/12/28
     */
    private static function downloadImage($url)
    {
        try {
            $temp_dir = app()->getRootPath() . 'runtime/temp/';
            if (!is_dir($temp_dir)) {
                mkdir($temp_dir, 0755, true);
            }

            $filename = md5($url) . '.jpg';
            $local_path = $temp_dir . $filename;

            $content = file_get_contents($url);
            if ($content !== false) {
                file_put_contents($local_path, $content);
                return $local_path;
            }

            return false;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * @notes 处理媒体内容安全检查回调
     * @param array $data
     */
    public static function handleMediaCheckCallback(array $data)
    {
        $traceId = $data['trace_id'] ?? '';
        if (empty($traceId)) {
            Db::name('log')->insert(['log' => '微信内容安全回调：缺少trace_id', 'type' => 'wechat_callback_error']);
            return;
        }

        $result = $data['result'] ?? [];
        $suggest = $result['suggest'] ?? 'pass';

        // 使用trace_id来查找文章
        $article = \app\common\model\community\CommunityArticle::whereRaw("FIND_IN_SET('{$traceId}', audit_trace_id)")
                                    ->where('status', \app\common\enum\CommunityArticleEnum::STATUS_WAIT)
                                    ->find();

        if ($article) {
            if ($suggest == 'risky') {
                // 审核不通过
                $article->status = \app\common\enum\CommunityArticleEnum::STATUS_FAIL;
                $article->audit_remark = '图片内容安全审核不通过：' . ($result['label'] ?? '未知原因');
            } else {
                // 图片审核通过，但我们还需要确认所有图片都已通过。
                // 这是一个简化的处理，实际场景可能需要更复杂的逻辑来跟踪所有图片的审核状态。
                // 这里我们假设一张图片不通过，则整篇文章不通过。
                $article->status = \app\common\enum\CommunityArticleEnum::STATUS_SUCCESS;
            }
            $article->audit_time = time();
            $article->save();
            Db::name('log')->insert(['log' => '微信内容安全回调：文章ID ' . $article->id . ' 状态已更新为 ' . $article->status, 'type' => 'wechat_callback']);
        } else {
             Db::name('log')->insert(['log' => '微信内容安全回调：根据trace_id未找到待审核的文章: ' . $traceId, 'type' => 'wechat_callback_warning']);
        }
    }

}