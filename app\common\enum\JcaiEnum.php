<?php


namespace app\common\enum;


class JcaiEnum
{
    /**
     * 众筹的状态
     */
    const TEAM_STATUS_END  = 2;
    const TEAM_STATUS_START = 1; //进行中
    const TEAM_STATUS_STOP  = 0; //已停止

    /**
     * 审核状态
     */
    const TEAM_AUDIT_WAIT    = 0; //等待审核
    const TEAM_AUDIT_SUCCESS = 1; //审核通过
    const TEAM_AUDIT_REFUSE  = 2; //审核拒绝

    /**
     * 众筹状态
     */
    const TEAM_STATUS_CONDUCT = 0; //进行中
    const TEAM_STATUS_SUCCESS = 1; //众筹成功
    const TEAM_STATUS_FAIL    = 2; //众筹失败

    /**
     * @Notes: 获取众筹活动状态
     * @Author: 张无忌
     * @param $type
     * @return array|mixed|string
     */
    public static function getTeamStatusDesc($type)
    {
        $desc = [
            self::TEAM_STATUS_START => '进行中',
            self::TEAM_STATUS_STOP  => '已停止',
            self::TEAM_STATUS_END  => '已结束',
        ];

        if ($type === true){
            return $desc;
        }
        return $desc[$type] ?? '';
    }

    /**
     * @Notes: 审核状态
     * @Author: 张无忌
     * @param $type
     * @return array|mixed|string
     */
    public static function getTeamAuditDesc($type)
    {
        $desc = [
            self::TEAM_AUDIT_WAIT    => '待审核',
            self::TEAM_AUDIT_SUCCESS => '审核通过',
            self::TEAM_AUDIT_REFUSE  => '审核拒绝',
        ];

        if ($type === true){
            return $desc;
        }
        return $desc[$type] ?? '';
    }
    /**
     * @Notes: 众筹状态
     * @Author: 张无忌
     * @param $type
     * @return array|mixed|string
     */
    public static function getStatusDesc($type)
    {
        $desc = [
            self::TEAM_STATUS_CONDUCT => '集众筹中',
            self::TEAM_STATUS_SUCCESS => '集众筹成功',
            self::TEAM_STATUS_FAIL    => '集众筹失败',
        ];

        if ($type === true){
            return $desc;
        }
        return $desc[$type] ?? '';
    }

}