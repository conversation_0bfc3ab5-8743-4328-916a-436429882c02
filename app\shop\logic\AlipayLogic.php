<?php

namespace app\shop\logic;

use app\common\basics\Logic;
use app\common\model\shop\ShopAlipay;

class AlipayLogic extends Logic
{
    static function lists($get, $shop_id)
    {
        try {
            $model = new ShopAlipay();
            $lists = $model->field(true)
                ->where(['del' => 0, 'shop_id'=>$shop_id])
                ->order('id', 'desc')
                ->paginate([
                    'page'      => $get['page'] ?? 1,
                    'list_rows' => $get['limit'] ?? 20,
                    'var_page'  => 'page'
                ])->toArray();
            
            return [ 'count'=>$lists['total'], 'lists'=>$lists['data'] ];
        } catch (\Exception $e) {
            return [ 'error'=>$e->getMessage() ];
        }
    }
    
    static function detail($id)
    {
        $model = new ShopAlipay();
        return $model->field(true)->findOrEmpty($id);
    }
    
    static function add($post, $shop_id)
    {
        try {
            ShopAlipay::create([
                'shop_id'   => $shop_id,
                'account'   => $post['account'],
                'username'  => $post['username'],
                'del'       => 0,
            ]);
            
            return true;
        } catch (\Exception $e) {
            static::$error = $e->getMessage();
            return false;
        }
    }
    
    static function edit($post, $shop_id)
    {
        try {
            ShopAlipay::update([
                'account'   => $post['account'],
                'username'  => $post['username'],
                'del'       => 0,
            ], [ 'id' => $post['id'], 'shop_id' => $shop_id ]);
            
            return true;
        } catch (\Exception $e) {
            static::$error = $e->getMessage();
            return false;
        }
    }
    
    static function del($id, $shop_id)
    {
        try {
            ShopAlipay::update([
                'del'         => 1,
                'update_time' => time()
            ], [ 'id' => $id, 'shop_id' => $shop_id ]);
            
            return true;
        } catch (\Exception $e) {
            static::$error = $e->getMessage();
            return false;
        }
    }
    
    static function getAlipayByShopId($shop_id)
    {
        try {
            $model = new ShopAlipay();
            return $model->field(true)
                ->where(['del' => 0, 'shop_id'=>$shop_id])
                ->order('id', 'desc')
                ->select()->toArray();
        } catch (\Exception $e) {
            return ['error'=>$e->getMessage()];
        }
    }
}