import{y as t,o as e,f as o,w as s,g as a,z as i,A as p,B as r,C as l,D as n,j as h,F as c,k as d,h as b,t as m,E as x,q as f}from"./index-KqVIYTFB.js";import{_ as g}from"./uni-app.es.elp5fm4t.js";const u="zb-fade-zoom-enter zb-fade-zoom-enter-active",v="zb-fade-zoom-enter-to zb-fade-zoom-enter-active",w="zb-fade-zoom-leave zb-fade-zoom-leave-active",z="zb-fade-zoom-leave-to zb-fade-zoom-leave-active";const y=g({props:{options:{type:Array,default:()=>[]},placement:{type:String,default:"bottom-start"},bgColor:{type:String},theme:{type:String,default:"light"},actionsDirection:{type:String,default:"vertical"}},name:"Popover",watch:{show:{handler(t){t?this.vueEnter():this.vueLeave()},immediate:!0}},data:()=>({show:!1,inited:!1,classes:"",display:!1,duration:100,popoverStyle:{},arrowOldStyle:{}}),computed:{bgStyleColor(){return this.bgColor?this.bgColor:"light"===this.theme?"white":"dark"===this.theme?"#4a4a4a":void 0},mergeStyle(){return{transitionDuration:`${this.duration}ms`,transitionTimingFunction:"ease-out",...this.popoverStyle}},arrowStyle(){return{...this.arrowOldStyle}}},mounted(){window.addEventListener("click",(()=>{this.show=!1}))},methods:{handleClick(){this.show?this.show=!1:this.show=!0,this.$emit("handleClick",this.show)},close(){this.show=!1},actionAction(t){this.$emit("select",t),this.show=!1},sleep:t=>new Promise((e=>{setTimeout((()=>{e()}),t)})),vueEnter(){this.inited=!0,this.getPosition(),this.classes=u,this.$nextTick((async()=>{await this.sleep(30),this.classes=v}))},vueLeave(){this.classes=w,this.$nextTick((async()=>{this.classes=z,await this.sleep(120),this.inited=!1}))},preventEvent(t){t&&"function"==typeof t.stopPropagation&&t.stopPropagation()},getPosition(){return new Promise((e=>{this.$nextTick((()=>{t().in(this).selectAll(".zb-button-popover,.zb-popover").boundingClientRect((async t=>{let{left:o,bottom:s,right:a,top:i,width:p,height:r}=t[0],l=t[1],n={},h={};switch(this.placement){case"top":l.width>p?n.left=`-${(l.width-p)/2}px`:n.left=Math.abs(l.width-p)/2+"px",n.bottom=`${r+8}px`,h.left=l.width/2-6+"px";break;case"top-start":n.left="0px",n.bottom=`${r+8}px`,h.left="16px";break;case"top-end":n.right="0px",n.bottom=`${r+8}px`,h.right="16px";break;case"bottom":l.width>p?n.left=`-${(l.width-p)/2}px`:n.left=Math.abs(l.width-p)/2+"px",n.top=`${r+8}px`,h.left=l.width/2-6+"px";break;case"bottom-start":n.top=`${r+8}px`,n.left="0px",h.left="16px";break;case"bottom-end":n.top=`${r+8}px`,n.right="0px",h.right="16px";break;case"right":n.left=`${p+8}px`,l.height>r?n.top=`-${(l.height-r)/2}px`:n.top=`${Math.abs((l.height-r)/2)}px`,h.top=l.height/2-6+"px";break;case"right-start":n.left=`${p+8}px`,n.top="0px",h.top="8px";break;case"right-end":n.left=`${p+8}px`,n.bottom="0px",h.bottom="8px";break;case"left":n.right=`${p+8}px`,l.height>r?n.top=`-${(l.height-r)/2}px`:n.top=`${Math.abs((l.height-r)/2)}px`,h.top=l.height/2-6+"px";break;case"left-start":n.right=`${p+8}px`,n.top="0px",h.top="8px";break;case"left-end":n.right=`${p+8}px`,n.bottom="0px",h.bottom="8px"}this.popoverStyle=n,this.arrowOldStyle=h,e()})).exec()}))}))},noop(t){this.preventEvent(t)}}},[["render",function(t,g,u,v,w,z){const y=f;return e(),o(y,{class:"zbPopover",style:n({"--theme-bg-color":z.bgStyleColor})},{default:s((()=>[a(y,{onClick:i(z.handleClick,["stop"]),class:"zb-button-popover"},{default:s((()=>[p(t.$slots,"default",{},void 0,!0)])),_:3},8,["onClick"]),r(a(y,{class:l(["zb-popover",[w.classes,`zb-popover-${u.placement}`]]),ref:"zb-transition",style:n([z.mergeStyle]),onTouchmove:z.noop},{default:s((()=>[a(y,{class:l(["zb-popover-arrow",[{zb_popper__up:0===u.placement.indexOf("bottom"),zb_popper__arrow:0===u.placement.indexOf("top"),zb_popper__right:0===u.placement.indexOf("right"),zb_popper__left:0===u.placement.indexOf("left")}]]),style:n([z.arrowStyle])},null,8,["style","class"]),p(t.$slots,"content",{},(()=>[a(y,{class:l([{horizontal__action:"horizontal"===u.actionsDirection}])},{default:s((()=>[(e(!0),h(c,null,d(u.options,((t,p)=>(e(),o(y,{onClick:i((e=>z.actionAction(t)),["stop"]),class:l(["zb-popover__action",[{dark__action:"dark"===u.theme}]]),key:p},{default:s((()=>[a(y,{class:"zb-popover__action-text"},{default:s((()=>[b(m(t.text),1)])),_:2},1024)])),_:2},1032,["onClick","class"])))),128))])),_:1},8,["class"])]),!0)])),_:3},8,["class","style","onTouchmove"]),[[x,w.inited]])])),_:3},8,["style"])}],["__scopeId","data-v-6f2199bb"]]);export{y as _};
