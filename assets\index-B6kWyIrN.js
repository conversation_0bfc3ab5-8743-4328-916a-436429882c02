function __vite__mapDeps(indexes) {
  if (!__vite__mapDeps.viteFileDeps) {
    __vite__mapDeps.viteFileDeps = ["assets/pages-sessionlist-sessionlist.DvKfqeTR.js","assets/uv-icon.D6fiO-QB.js","assets/uv-icon-eRg02KLo.css","assets/zb-popover.CqttEQzx.js","assets/zb-popover-D7F3h8RV.css","assets/z-paging.X4eWfR5C.js","assets/z-paging-8X5vRC30.css","assets/sessionlist-7_QHE5el.css","assets/pages-index-index.BmKrNjAd.js","assets/uv-button.Bb8r9WJv.js","assets/uv-button-CO4O5kZo.css","assets/pages-login-login.C8lsodj-.js","assets/login-aXdU6o6a.css","assets/pages-chat-chat.89q3wh0r.js","assets/chat-CA1MNYKU.css"]
  }
  return indexes.map((i) => __vite__mapDeps.viteFileDeps[i])
}
var e=Object.defineProperty,t=(t,n,o)=>(((t,n,o)=>{n in t?e(t,n,{enumerable:!0,configurable:!0,writable:!0,value:o}):t[n]=o})(t,"symbol"!=typeof n?n+"":n,o),o);!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))t(e);new MutationObserver((e=>{for(const n of e)if("childList"===n.type)for(const e of n.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&t(e)})).observe(document,{childList:!0,subtree:!0})}function t(e){if(e.ep)return;e.ep=!0;const t=function(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerPolicy&&(t.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?t.credentials="include":"anonymous"===e.crossOrigin?t.credentials="omit":t.credentials="same-origin",t}(e);fetch(e.href,t)}}();const n={},o=function(e,t,o){let r=Promise.resolve();if(t&&t.length>0){const e=document.getElementsByTagName("link"),i=document.querySelector("meta[property=csp-nonce]"),s=(null==i?void 0:i.nonce)||(null==i?void 0:i.getAttribute("nonce"));r=Promise.all(t.map((t=>{if((t=function(e){return"/serve/"+e}(t))in n)return;n[t]=!0;const r=t.endsWith(".css"),i=r?'[rel="stylesheet"]':"";if(!!o)for(let n=e.length-1;n>=0;n--){const o=e[n];if(o.href===t&&(!r||"stylesheet"===o.rel))return}else if(document.querySelector(`link[href="${t}"]${i}`))return;const a=document.createElement("link");return a.rel=r?"stylesheet":"modulepreload",r||(a.as="script",a.crossOrigin=""),a.href=t,s&&a.setAttribute("nonce",s),document.head.appendChild(a),r?new Promise(((e,n)=>{a.addEventListener("load",e),a.addEventListener("error",(()=>n(new Error(`Unable to preload CSS for ${t}`))))})):void 0})))}return r.then((()=>e())).catch((e=>{const t=new Event("vite:preloadError",{cancelable:!0});if(t.payload=e,window.dispatchEvent(t),!t.defaultPrevented)throw e}))};
/**
* @vue/shared v3.4.21
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/
function r(e,t){const n=new Set(e.split(","));return t?e=>n.has(e.toLowerCase()):e=>n.has(e)}const i={},s=[],a=()=>{},l=()=>!1,c=e=>111===e.charCodeAt(0)&&110===e.charCodeAt(1)&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),u=e=>e.startsWith("onUpdate:"),f=Object.assign,d=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},p=Object.prototype.hasOwnProperty,h=(e,t)=>p.call(e,t),g=Array.isArray,m=e=>"[object Map]"===T(e),v=e=>"[object Set]"===T(e),y=e=>"function"==typeof e,b=e=>"string"==typeof e,w=e=>"symbol"==typeof e,_=e=>null!==e&&"object"==typeof e,x=e=>(_(e)||y(e))&&y(e.then)&&y(e.catch),S=Object.prototype.toString,T=e=>S.call(e),C=e=>"[object Object]"===T(e),k=e=>b(e)&&"NaN"!==e&&"-"!==e[0]&&""+parseInt(e,10)===e,E=r(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),$=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},O=/-(\w)/g,L=$((e=>e.replace(O,((e,t)=>t?t.toUpperCase():"")))),A=/\B([A-Z])/g,P=$((e=>e.replace(A,"-$1").toLowerCase())),j=$((e=>e.charAt(0).toUpperCase()+e.slice(1))),R=$((e=>e?`on${j(e)}`:"")),M=(e,t)=>!Object.is(e,t),I=(e,t)=>{for(let n=0;n<e.length;n++)e[n](t)},N=(e,t,n)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,value:n})},B=e=>{const t=parseFloat(e);return isNaN(t)?e:t};let F;const D=()=>F||(F="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{});function H(e){if(g(e)){const t={};for(let n=0;n<e.length;n++){const o=e[n],r=b(o)?q(o):H(o);if(r)for(const e in r)t[e]=r[e]}return t}if(b(e)||_(e))return e}const V=/;(?![^(]*\))/g,W=/:([^]+)/,z=/\/\*[^]*?\*\//g;function q(e){const t={};return e.replace(z,"").split(V).forEach((e=>{if(e){const n=e.split(W);n.length>1&&(t[n[0].trim()]=n[1].trim())}})),t}function U(e){let t="";if(b(e))t=e;else if(g(e))for(let n=0;n<e.length;n++){const o=U(e[n]);o&&(t+=o+" ")}else if(_(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const G=r("itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly");function X(e){return!!e||""===e}const Y=e=>b(e)?e:null==e?"":g(e)||_(e)&&(e.toString===S||!y(e.toString))?JSON.stringify(e,Z,2):String(e),Z=(e,t)=>t&&t.__v_isRef?Z(e,t.value):m(t)?{[`Map(${t.size})`]:[...t.entries()].reduce(((e,[t,n],o)=>(e[J(t,o)+" =>"]=n,e)),{})}:v(t)?{[`Set(${t.size})`]:[...t.values()].map((e=>J(e)))}:w(t)?J(t):!_(t)||g(t)||C(t)?t:String(t),J=(e,t="")=>{var n;return w(e)?`Symbol(${null!=(n=e.description)?n:t})`:e},K=["ad","ad-content-page","ad-draw","audio","button","camera","canvas","checkbox","checkbox-group","cover-image","cover-view","editor","form","functional-page-navigator","icon","image","input","label","live-player","live-pusher","map","movable-area","movable-view","navigator","official-account","open-data","picker","picker-view","picker-view-column","progress","radio","radio-group","rich-text","scroll-view","slider","swiper","swiper-item","switch","text","textarea","video","view","web-view","location-picker","location-view"].map((e=>"uni-"+e)),Q=["list-view","list-item","sticky-section","sticky-header","cloud-db-element"].map((e=>"uni-"+e)),ee=["list-item"].map((e=>"uni-"+e));function te(e){if(-1!==ee.indexOf(e))return!1;const t="uni-"+e.replace("v-uni-","");return-1!==K.indexOf(t)||-1!==Q.indexOf(t)}const ne=/^([a-z-]+:)?\/\//i,oe=/^data:.*,.*/,re="onShow",ie="onLoad",se="onReady",ae="onReachBottom";function le(e){return 0===e.indexOf("/")}function ce(e){return le(e)?e:"/"+e}function ue(e,t=null){let n;return(...o)=>(e&&(n=e.apply(t,o),e=null),n)}function fe(e,t){e=e||{},b(t)&&(t={errMsg:t}),/:ok$/.test(t.errMsg)?y(e.success)&&e.success(t):y(e.fail)&&e.fail(t),y(e.complete)&&e.complete(t)}let de;function pe(){return de||(de=function(){if("undefined"!=typeof globalThis)return globalThis;if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;function e(){return this}return void 0!==e()?e():new Function("return this")()}(),de)}function he(e){return e&&(e.appContext?e.proxy:e)}function ge(e){if(!e)return;let t=e.type.name;for(;t&&te(P(t));)t=(e=e.parent).type.name;return e.proxy}function me(e){return 1===e.nodeType}function ve(e){const t=pe();if(t&&t.UTSJSONObject&&e instanceof t.UTSJSONObject){const n={};return t.UTSJSONObject.keys(e).forEach((t=>{n[t]=e[t]})),H(n)}if(e instanceof Map){const t={};return e.forEach(((e,n)=>{t[n]=e})),H(t)}if(b(e))return q(e);if(g(e)){const t={};for(let n=0;n<e.length;n++){const o=e[n],r=b(o)?q(o):ve(o);if(r)for(const e in r)t[e]=r[e]}return t}return H(e)}function ye(e){let t="";const n=pe();if(n&&n.UTSJSONObject&&e instanceof n.UTSJSONObject)n.UTSJSONObject.keys(e).forEach((n=>{e[n]&&(t+=n+" ")}));else if(e instanceof Map)e.forEach(((e,n)=>{e&&(t+=n+" ")}));else if(g(e))for(let o=0;o<e.length;o++){const n=ye(e[o]);n&&(t+=n+" ")}else t=U(e);return t.trim()}function be(e){return L(e.substring(5))}const we=ue((e=>{e=e||(e=>e.tagName.startsWith("UNI-"));const t=HTMLElement.prototype,n=t.setAttribute;t.setAttribute=function(t,o){if(t.startsWith("data-")&&e(this)){(this.__uniDataset||(this.__uniDataset={}))[be(t)]=o}n.call(this,t,o)};const o=t.removeAttribute;t.removeAttribute=function(t){this.__uniDataset&&t.startsWith("data-")&&e(this)&&delete this.__uniDataset[be(t)],o.call(this,t)}}));function _e(e){return f({},e.dataset,e.__uniDataset)}const xe=new RegExp("\"[^\"]+\"|'[^']+'|url\\([^)]+\\)|(\\d*\\.?\\d+)[r|u]px","g");function Se(e){return{passive:e}}function Te(e){const{id:t,offsetTop:n,offsetLeft:o}=e;return{id:t,dataset:_e(e),offsetTop:n,offsetLeft:o}}function Ce(e){try{return decodeURIComponent(""+e)}catch(t){}return""+e}function ke(e={}){const t={};return Object.keys(e).forEach((n=>{try{t[n]=Ce(e[n])}catch(o){t[n]=e[n]}})),t}const Ee=/\+/g;function $e(e){const t={};if(""===e||"?"===e)return t;const n=("?"===e[0]?e.slice(1):e).split("&");for(let o=0;o<n.length;++o){const e=n[o].replace(Ee," ");let r=e.indexOf("="),i=Ce(r<0?e:e.slice(0,r)),s=r<0?null:Ce(e.slice(r+1));if(i in t){let e=t[i];g(e)||(e=t[i]=[e]),e.push(s)}else t[i]=s}return t}function Oe(e,t,{clearTimeout:n,setTimeout:o}){let r;const i=function(){n(r);const i=()=>e.apply(this,arguments);r=o(i,t)};return i.cancel=function(){n(r)},i}class Le{constructor(e,t){this.id=e,this.listener={},this.emitCache=[],t&&Object.keys(t).forEach((e=>{this.on(e,t[e])}))}emit(e,...t){const n=this.listener[e];if(!n)return this.emitCache.push({eventName:e,args:t});n.forEach((e=>{e.fn.apply(e.fn,t)})),this.listener[e]=n.filter((e=>"once"!==e.type))}on(e,t){this._addListener(e,"on",t),this._clearCache(e)}once(e,t){this._addListener(e,"once",t),this._clearCache(e)}off(e,t){const n=this.listener[e];if(n)if(t)for(let o=0;o<n.length;)n[o].fn===t&&(n.splice(o,1),o--),o++;else delete this.listener[e]}_clearCache(e){for(let t=0;t<this.emitCache.length;t++){const n=this.emitCache[t],o=e?n.eventName===e?e:null:n.eventName;if(!o)continue;"number"!=typeof this.emit.apply(this,[o,...n.args])?(this.emitCache.splice(t,1),t--):this.emitCache.pop()}}_addListener(e,t,n){(this.listener[e]||(this.listener[e]=[])).push({fn:n,type:t})}}const Ae=["onInit","onLoad","onShow","onHide","onUnload","onBackPress","onPageScroll","onTabItemTap","onReachBottom","onPullDownRefresh","onShareTimeline","onShareAppMessage","onShareChat","onAddToFavorites","onSaveExitState","onNavigationBarButtonTap","onNavigationBarSearchInputClicked","onNavigationBarSearchInputChanged","onNavigationBarSearchInputConfirmed","onNavigationBarSearchInputFocusChanged"];const Pe=["onShow","onHide","onLaunch","onError","onThemeChange","onPageNotFound","onUnhandledRejection","onExit","onInit","onLoad","onReady","onUnload","onResize","onBackPress","onPageScroll","onTabItemTap","onReachBottom","onPullDownRefresh","onShareTimeline","onAddToFavorites","onShareAppMessage","onShareChat","onSaveExitState","onNavigationBarButtonTap","onNavigationBarSearchInputClicked","onNavigationBarSearchInputChanged","onNavigationBarSearchInputConfirmed","onNavigationBarSearchInputFocusChanged"];const je=[];const Re=ue(((e,t)=>t(e))),Me=function(){};Me.prototype={_id:1,on:function(e,t,n){var o=this.e||(this.e={});return(o[e]||(o[e]=[])).push({fn:t,ctx:n,_id:this._id}),this._id++},once:function(e,t,n){var o=this;function r(){o.off(e,r),t.apply(n,arguments)}return r._=t,this.on(e,r,n)},emit:function(e){for(var t=[].slice.call(arguments,1),n=((this.e||(this.e={}))[e]||[]).slice(),o=0,r=n.length;o<r;o++)n[o].fn.apply(n[o].ctx,t);return this},off:function(e,t){var n=this.e||(this.e={}),o=n[e],r=[];if(o&&t){for(var i=o.length-1;i>=0;i--)if(o[i].fn===t||o[i].fn._===t||o[i]._id===t){o.splice(i,1);break}r=o}return r.length?n[e]=r:delete n[e],this}};var Ie=Me;const Ne={black:"rgba(0,0,0,0.4)",white:"rgba(255,255,255,0.4)"};function Be(e,t,n){if(b(t)&&t.startsWith("@")){let r=e[t.replace("@","")]||t;switch(n){case"titleColor":r="black"===r?"#000000":"#ffffff";break;case"borderStyle":r=(o=r)&&o in Ne?Ne[o]:o}return r}var o;return t}function Fe(e,t={},n="light"){const o=t[n],r={};return void 0!==o&&e?(Object.keys(e).forEach((i=>{const s=e[i];r[i]=C(s)?Fe(s,t,n):g(s)?s.map((e=>C(e)?Fe(e,t,n):Be(o,e))):Be(o,s,i)})),r):e}
/**
* @dcloudio/uni-h5-vue v3.4.21
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let De,He;class Ve{constructor(e=!1){this.detached=e,this._active=!0,this.effects=[],this.cleanups=[],this.parent=De,!e&&De&&(this.index=(De.scopes||(De.scopes=[])).push(this)-1)}get active(){return this._active}run(e){if(this._active){const t=De;try{return De=this,e()}finally{De=t}}}on(){De=this}off(){De=this.parent}stop(e){if(this._active){let t,n;for(t=0,n=this.effects.length;t<n;t++)this.effects[t].stop();for(t=0,n=this.cleanups.length;t<n;t++)this.cleanups[t]();if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].stop(!0);if(!this.detached&&this.parent&&!e){const e=this.parent.scopes.pop();e&&e!==this&&(this.parent.scopes[this.index]=e,e.index=this.index)}this.parent=void 0,this._active=!1}}}function We(e){return new Ve(e)}class ze{constructor(e,t,n,o){this.fn=e,this.trigger=t,this.scheduler=n,this.active=!0,this.deps=[],this._dirtyLevel=4,this._trackId=0,this._runnings=0,this._shouldSchedule=!1,this._depsLength=0,function(e,t=De){t&&t.active&&t.effects.push(e)}(this,o)}get dirty(){if(2===this._dirtyLevel||3===this._dirtyLevel){this._dirtyLevel=1,Je();for(let e=0;e<this._depsLength;e++){const t=this.deps[e];if(t.computed&&(t.computed.value,this._dirtyLevel>=4))break}1===this._dirtyLevel&&(this._dirtyLevel=0),Ke()}return this._dirtyLevel>=4}set dirty(e){this._dirtyLevel=e?4:0}run(){if(this._dirtyLevel=0,!this.active)return this.fn();let e=Xe,t=He;try{return Xe=!0,He=this,this._runnings++,qe(this),this.fn()}finally{Ue(this),this._runnings--,He=t,Xe=e}}stop(){var e;this.active&&(qe(this),Ue(this),null==(e=this.onStop)||e.call(this),this.active=!1)}}function qe(e){e._trackId++,e._depsLength=0}function Ue(e){if(e.deps.length>e._depsLength){for(let t=e._depsLength;t<e.deps.length;t++)Ge(e.deps[t],e);e.deps.length=e._depsLength}}function Ge(e,t){const n=e.get(t);void 0!==n&&t._trackId!==n&&(e.delete(t),0===e.size&&e.cleanup())}let Xe=!0,Ye=0;const Ze=[];function Je(){Ze.push(Xe),Xe=!1}function Ke(){const e=Ze.pop();Xe=void 0===e||e}function Qe(){Ye++}function et(){for(Ye--;!Ye&&nt.length;)nt.shift()()}function tt(e,t,n){if(t.get(e)!==e._trackId){t.set(e,e._trackId);const n=e.deps[e._depsLength];n!==t?(n&&Ge(n,e),e.deps[e._depsLength++]=t):e._depsLength++}}const nt=[];function ot(e,t,n){Qe();for(const o of e.keys()){let n;o._dirtyLevel<t&&(null!=n?n:n=e.get(o)===o._trackId)&&(o._shouldSchedule||(o._shouldSchedule=0===o._dirtyLevel),o._dirtyLevel=t),o._shouldSchedule&&(null!=n?n:n=e.get(o)===o._trackId)&&(o.trigger(),o._runnings&&!o.allowRecurse||2===o._dirtyLevel||(o._shouldSchedule=!1,o.scheduler&&nt.push(o.scheduler)))}et()}const rt=(e,t)=>{const n=new Map;return n.cleanup=e,n.computed=t,n},it=new WeakMap,st=Symbol(""),at=Symbol("");function lt(e,t,n){if(Xe&&He){let t=it.get(e);t||it.set(e,t=new Map);let o=t.get(n);o||t.set(n,o=rt((()=>t.delete(n)))),tt(He,o)}}function ct(e,t,n,o,r,i){const s=it.get(e);if(!s)return;let a=[];if("clear"===t)a=[...s.values()];else if("length"===n&&g(e)){const e=Number(o);s.forEach(((t,n)=>{("length"===n||!w(n)&&n>=e)&&a.push(t)}))}else switch(void 0!==n&&a.push(s.get(n)),t){case"add":g(e)?k(n)&&a.push(s.get("length")):(a.push(s.get(st)),m(e)&&a.push(s.get(at)));break;case"delete":g(e)||(a.push(s.get(st)),m(e)&&a.push(s.get(at)));break;case"set":m(e)&&a.push(s.get(st))}Qe();for(const l of a)l&&ot(l,4);et()}const ut=r("__proto__,__v_isRef,__isVue"),ft=new Set(Object.getOwnPropertyNames(Symbol).filter((e=>"arguments"!==e&&"caller"!==e)).map((e=>Symbol[e])).filter(w)),dt=pt();function pt(){const e={};return["includes","indexOf","lastIndexOf"].forEach((t=>{e[t]=function(...e){const n=tn(this);for(let t=0,r=this.length;t<r;t++)lt(n,0,t+"");const o=n[t](...e);return-1===o||!1===o?n[t](...e.map(tn)):o}})),["push","pop","shift","unshift","splice"].forEach((t=>{e[t]=function(...e){Je(),Qe();const n=tn(this)[t].apply(this,e);return et(),Ke(),n}})),e}function ht(e){const t=tn(this);return lt(t,0,e),t.hasOwnProperty(e)}class gt{constructor(e=!1,t=!1){this._isReadonly=e,this._isShallow=t}get(e,t,n){const o=this._isReadonly,r=this._isShallow;if("__v_isReactive"===t)return!o;if("__v_isReadonly"===t)return o;if("__v_isShallow"===t)return r;if("__v_raw"===t)return n===(o?r?qt:zt:r?Wt:Vt).get(e)||Object.getPrototypeOf(e)===Object.getPrototypeOf(n)?e:void 0;const i=g(e);if(!o){if(i&&h(dt,t))return Reflect.get(dt,t,n);if("hasOwnProperty"===t)return ht}const s=Reflect.get(e,t,n);return(w(t)?ft.has(t):ut(t))?s:(o||lt(e,0,t),r?s:cn(s)?i&&k(t)?s:s.value:_(s)?o?Yt(s):Gt(s):s)}}class mt extends gt{constructor(e=!1){super(!1,e)}set(e,t,n,o){let r=e[t];if(!this._isShallow){const t=Kt(r);if(Qt(n)||Kt(n)||(r=tn(r),n=tn(n)),!g(e)&&cn(r)&&!cn(n))return!t&&(r.value=n,!0)}const i=g(e)&&k(t)?Number(t)<e.length:h(e,t),s=Reflect.set(e,t,n,o);return e===tn(o)&&(i?M(n,r)&&ct(e,"set",t,n):ct(e,"add",t,n)),s}deleteProperty(e,t){const n=h(e,t);e[t];const o=Reflect.deleteProperty(e,t);return o&&n&&ct(e,"delete",t,void 0),o}has(e,t){const n=Reflect.has(e,t);return w(t)&&ft.has(t)||lt(e,0,t),n}ownKeys(e){return lt(e,0,g(e)?"length":st),Reflect.ownKeys(e)}}class vt extends gt{constructor(e=!1){super(!0,e)}set(e,t){return!0}deleteProperty(e,t){return!0}}const yt=new mt,bt=new vt,wt=new mt(!0),_t=e=>e,xt=e=>Reflect.getPrototypeOf(e);function St(e,t,n=!1,o=!1){const r=tn(e=e.__v_raw),i=tn(t);n||(M(t,i)&&lt(r,0,t),lt(r,0,i));const{has:s}=xt(r),a=o?_t:n?rn:on;return s.call(r,t)?a(e.get(t)):s.call(r,i)?a(e.get(i)):void(e!==r&&e.get(t))}function Tt(e,t=!1){const n=this.__v_raw,o=tn(n),r=tn(e);return t||(M(e,r)&&lt(o,0,e),lt(o,0,r)),e===r?n.has(e):n.has(e)||n.has(r)}function Ct(e,t=!1){return e=e.__v_raw,!t&&lt(tn(e),0,st),Reflect.get(e,"size",e)}function kt(e){e=tn(e);const t=tn(this);return xt(t).has.call(t,e)||(t.add(e),ct(t,"add",e,e)),this}function Et(e,t){t=tn(t);const n=tn(this),{has:o,get:r}=xt(n);let i=o.call(n,e);i||(e=tn(e),i=o.call(n,e));const s=r.call(n,e);return n.set(e,t),i?M(t,s)&&ct(n,"set",e,t):ct(n,"add",e,t),this}function $t(e){const t=tn(this),{has:n,get:o}=xt(t);let r=n.call(t,e);r||(e=tn(e),r=n.call(t,e)),o&&o.call(t,e);const i=t.delete(e);return r&&ct(t,"delete",e,void 0),i}function Ot(){const e=tn(this),t=0!==e.size,n=e.clear();return t&&ct(e,"clear",void 0,void 0),n}function Lt(e,t){return function(n,o){const r=this,i=r.__v_raw,s=tn(i),a=t?_t:e?rn:on;return!e&&lt(s,0,st),i.forEach(((e,t)=>n.call(o,a(e),a(t),r)))}}function At(e,t,n){return function(...o){const r=this.__v_raw,i=tn(r),s=m(i),a="entries"===e||e===Symbol.iterator&&s,l="keys"===e&&s,c=r[e](...o),u=n?_t:t?rn:on;return!t&&lt(i,0,l?at:st),{next(){const{value:e,done:t}=c.next();return t?{value:e,done:t}:{value:a?[u(e[0]),u(e[1])]:u(e),done:t}},[Symbol.iterator](){return this}}}}function Pt(e){return function(...t){return"delete"!==e&&("clear"===e?void 0:this)}}function jt(){const e={get(e){return St(this,e)},get size(){return Ct(this)},has:Tt,add:kt,set:Et,delete:$t,clear:Ot,forEach:Lt(!1,!1)},t={get(e){return St(this,e,!1,!0)},get size(){return Ct(this)},has:Tt,add:kt,set:Et,delete:$t,clear:Ot,forEach:Lt(!1,!0)},n={get(e){return St(this,e,!0)},get size(){return Ct(this,!0)},has(e){return Tt.call(this,e,!0)},add:Pt("add"),set:Pt("set"),delete:Pt("delete"),clear:Pt("clear"),forEach:Lt(!0,!1)},o={get(e){return St(this,e,!0,!0)},get size(){return Ct(this,!0)},has(e){return Tt.call(this,e,!0)},add:Pt("add"),set:Pt("set"),delete:Pt("delete"),clear:Pt("clear"),forEach:Lt(!0,!0)};return["keys","values","entries",Symbol.iterator].forEach((r=>{e[r]=At(r,!1,!1),n[r]=At(r,!0,!1),t[r]=At(r,!1,!0),o[r]=At(r,!0,!0)})),[e,n,t,o]}const[Rt,Mt,It,Nt]=jt();function Bt(e,t){const n=t?e?Nt:It:e?Mt:Rt;return(t,o,r)=>"__v_isReactive"===o?!e:"__v_isReadonly"===o?e:"__v_raw"===o?t:Reflect.get(h(n,o)&&o in t?n:t,o,r)}const Ft={get:Bt(!1,!1)},Dt={get:Bt(!1,!0)},Ht={get:Bt(!0,!1)},Vt=new WeakMap,Wt=new WeakMap,zt=new WeakMap,qt=new WeakMap;function Ut(e){return e.__v_skip||!Object.isExtensible(e)?0:function(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}((e=>T(e).slice(8,-1))(e))}function Gt(e){return Kt(e)?e:Zt(e,!1,yt,Ft,Vt)}function Xt(e){return Zt(e,!1,wt,Dt,Wt)}function Yt(e){return Zt(e,!0,bt,Ht,zt)}function Zt(e,t,n,o,r){if(!_(e))return e;if(e.__v_raw&&(!t||!e.__v_isReactive))return e;const i=r.get(e);if(i)return i;const s=Ut(e);if(0===s)return e;const a=new Proxy(e,2===s?o:n);return r.set(e,a),a}function Jt(e){return Kt(e)?Jt(e.__v_raw):!(!e||!e.__v_isReactive)}function Kt(e){return!(!e||!e.__v_isReadonly)}function Qt(e){return!(!e||!e.__v_isShallow)}function en(e){return Jt(e)||Kt(e)}function tn(e){const t=e&&e.__v_raw;return t?tn(t):e}function nn(e){return Object.isExtensible(e)&&N(e,"__v_skip",!0),e}const on=e=>_(e)?Gt(e):e,rn=e=>_(e)?Yt(e):e;class sn{constructor(e,t,n,o){this.getter=e,this._setter=t,this.dep=void 0,this.__v_isRef=!0,this.__v_isReadonly=!1,this.effect=new ze((()=>e(this._value)),(()=>ln(this,2===this.effect._dirtyLevel?2:3))),this.effect.computed=this,this.effect.active=this._cacheable=!o,this.__v_isReadonly=n}get value(){const e=tn(this);return e._cacheable&&!e.effect.dirty||!M(e._value,e._value=e.effect.run())||ln(e,4),an(e),e.effect._dirtyLevel>=2&&ln(e,2),e._value}set value(e){this._setter(e)}get _dirty(){return this.effect.dirty}set _dirty(e){this.effect.dirty=e}}function an(e){var t;Xe&&He&&(e=tn(e),tt(He,null!=(t=e.dep)?t:e.dep=rt((()=>e.dep=void 0),e instanceof sn?e:void 0)))}function ln(e,t=4,n){const o=(e=tn(e)).dep;o&&ot(o,t)}function cn(e){return!(!e||!0!==e.__v_isRef)}function un(e){return fn(e,!1)}function fn(e,t){return cn(e)?e:new dn(e,t)}class dn{constructor(e,t){this.__v_isShallow=t,this.dep=void 0,this.__v_isRef=!0,this._rawValue=t?e:tn(e),this._value=t?e:on(e)}get value(){return an(this),this._value}set value(e){const t=this.__v_isShallow||Qt(e)||Kt(e);e=t?e:tn(e),M(e,this._rawValue)&&(this._rawValue=e,this._value=t?e:on(e),ln(this,4))}}function pn(e){return cn(e)?e.value:e}const hn={get:(e,t,n)=>pn(Reflect.get(e,t,n)),set:(e,t,n,o)=>{const r=e[t];return cn(r)&&!cn(n)?(r.value=n,!0):Reflect.set(e,t,n,o)}};function gn(e){return Jt(e)?e:new Proxy(e,hn)}function mn(e,t,n,o){try{return o?e(...o):e()}catch(r){yn(r,t,n)}}function vn(e,t,n,o){if(y(e)){const r=mn(e,t,n,o);return r&&x(r)&&r.catch((e=>{yn(e,t,n)})),r}const r=[];for(let i=0;i<e.length;i++)r.push(vn(e[i],t,n,o));return r}function yn(e,t,n,o=!0){const r=t?t.vnode:null;if(t){let o=t.parent;const r=t.proxy,i=`https://vuejs.org/error-reference/#runtime-${n}`;for(;o;){const t=o.ec;if(t)for(let n=0;n<t.length;n++)if(!1===t[n](e,r,i))return;o=o.parent}const s=t.appContext.config.errorHandler;if(s)return void mn(s,null,10,[e,r,i])}bn(e,n,r,o)}function bn(e,t,n,o=!0){console.error(e)}let wn=!1,_n=!1;const xn=[];let Sn=0;const Tn=[];let Cn=null,kn=0;const En=Promise.resolve();let $n=null;function On(e){const t=$n||En;return e?t.then(this?e.bind(this):e):t}function Ln(e){xn.length&&xn.includes(e,wn&&e.allowRecurse?Sn+1:Sn)||(null==e.id?xn.push(e):xn.splice(function(e){let t=Sn+1,n=xn.length;for(;t<n;){const o=t+n>>>1,r=xn[o],i=Rn(r);i<e||i===e&&r.pre?t=o+1:n=o}return t}(e.id),0,e),An())}function An(){wn||_n||(_n=!0,$n=En.then(In))}function Pn(e,t,n=(wn?Sn+1:0)){for(;n<xn.length;n++){const t=xn[n];if(t&&t.pre){if(e&&t.id!==e.uid)continue;xn.splice(n,1),n--,t()}}}function jn(e){if(Tn.length){const e=[...new Set(Tn)].sort(((e,t)=>Rn(e)-Rn(t)));if(Tn.length=0,Cn)return void Cn.push(...e);for(Cn=e,kn=0;kn<Cn.length;kn++)Cn[kn]();Cn=null,kn=0}}const Rn=e=>null==e.id?1/0:e.id,Mn=(e,t)=>{const n=Rn(e)-Rn(t);if(0===n){if(e.pre&&!t.pre)return-1;if(t.pre&&!e.pre)return 1}return n};function In(e){_n=!1,wn=!0,xn.sort(Mn);try{for(Sn=0;Sn<xn.length;Sn++){const e=xn[Sn];e&&!1!==e.active&&mn(e,null,14)}}finally{Sn=0,xn.length=0,jn(),wn=!1,$n=null,(xn.length||Tn.length)&&In()}}function Nn(e,t,...n){if(e.isUnmounted)return;const o=e.vnode.props||i;let r=n;const s=t.startsWith("update:"),a=s&&t.slice(7);if(a&&a in o){const e=`${"modelValue"===a?"model":a}Modifiers`,{number:t,trim:s}=o[e]||i;s&&(r=n.map((e=>b(e)?e.trim():e))),t&&(r=n.map(B))}let l,c=o[l=R(t)]||o[l=R(L(t))];!c&&s&&(c=o[l=R(P(t))]),c&&vn(c,e,6,Bn(e,c,r));const u=o[l+"Once"];if(u){if(e.emitted){if(e.emitted[l])return}else e.emitted={};e.emitted[l]=!0,vn(u,e,6,Bn(e,u,r))}}function Bn(e,t,n){if(1!==n.length)return n;if(y(t)){if(t.length<2)return n}else if(!t.find((e=>e.length>=2)))return n;const o=n[0];if(o&&h(o,"type")&&h(o,"timeStamp")&&h(o,"target")&&h(o,"currentTarget")&&h(o,"detail")){const t=e.proxy,o=t.$gcd(t,!0);o&&n.push(o)}return n}function Fn(e,t,n=!1){const o=t.emitsCache,r=o.get(e);if(void 0!==r)return r;const i=e.emits;let s={},a=!1;if(!y(e)){const o=e=>{const n=Fn(e,t,!0);n&&(a=!0,f(s,n))};!n&&t.mixins.length&&t.mixins.forEach(o),e.extends&&o(e.extends),e.mixins&&e.mixins.forEach(o)}return i||a?(g(i)?i.forEach((e=>s[e]=null)):f(s,i),_(e)&&o.set(e,s),s):(_(e)&&o.set(e,null),null)}function Dn(e,t){return!(!e||!c(t))&&(t=t.slice(2).replace(/Once$/,""),h(e,t[0].toLowerCase()+t.slice(1))||h(e,P(t))||h(e,t))}let Hn=null,Vn=null;function Wn(e){const t=Hn;return Hn=e,Vn=e&&e.type.__scopeId||null,t}function zn(e,t=Hn,n){if(!t)return e;if(e._n)return e;const o=(...n)=>{o._d&&Jr(-1);const r=Wn(t);let i;try{i=e(...n)}finally{Wn(r),o._d&&Jr(1)}return i};return o._n=!0,o._c=!0,o._d=!0,o}function qn(e){const{type:t,vnode:n,proxy:o,withProxy:r,props:i,propsOptions:[s],slots:a,attrs:l,emit:c,render:f,renderCache:d,data:p,setupState:h,ctx:g,inheritAttrs:m}=e;let v,y;const b=Wn(e);try{if(4&n.shapeFlag){const e=r||o,t=e;v=fi(f.call(t,e,d,i,h,p,g)),y=l}else{const e=t;0,v=fi(e.length>1?e(i,{attrs:l,slots:a,emit:c}):e(i,null)),y=t.props?l:Un(l)}}catch(_){Gr.length=0,yn(_,e,1),v=ai(qr)}let w=v;if(y&&!1!==m){const e=Object.keys(y),{shapeFlag:t}=w;e.length&&7&t&&(s&&e.some(u)&&(y=Gn(y,s)),w=li(w,y))}return n.dirs&&(w=li(w),w.dirs=w.dirs?w.dirs.concat(n.dirs):n.dirs),n.transition&&(w.transition=n.transition),v=w,Wn(b),v}const Un=e=>{let t;for(const n in e)("class"===n||"style"===n||c(n))&&((t||(t={}))[n]=e[n]);return t},Gn=(e,t)=>{const n={};for(const o in e)u(o)&&o.slice(9)in t||(n[o]=e[o]);return n};function Xn(e,t,n){const o=Object.keys(t);if(o.length!==Object.keys(e).length)return!0;for(let r=0;r<o.length;r++){const i=o[r];if(t[i]!==e[i]&&!Dn(n,i))return!0}return!1}function Yn(e,t){return Kn("components",e,!0,t)||e}const Zn=Symbol.for("v-ndc");function Jn(e){return b(e)?Kn("components",e,!1)||e:e||Zn}function Kn(e,t,n=!0,o=!1){const r=Hn||yi;if(r){const n=r.type;if("components"===e){const e=Oi(n,!1);if(e&&(e===t||e===L(t)||e===j(L(t))))return n}const i=Qn(r[e]||n[e],t)||Qn(r.appContext[e],t);return!i&&o?n:i}}function Qn(e,t){return e&&(e[t]||e[L(t)]||e[j(L(t))])}const eo=e=>e.__isSuspense;const to=Symbol.for("v-scx");function no(e,t){return io(e,null,t)}const oo={};function ro(e,t,n){return io(e,t,n)}function io(e,t,{immediate:n,deep:o,flush:r,once:s,onTrack:l,onTrigger:c}=i){if(t&&s){const e=t;t=(...t)=>{e(...t),k()}}const u=yi,f=e=>!0===o?e:lo(e,!1===o?1:void 0);let p,h,m=!1,v=!1;if(cn(e)?(p=()=>e.value,m=Qt(e)):Jt(e)?(p=()=>f(e),m=!0):g(e)?(v=!0,m=e.some((e=>Jt(e)||Qt(e))),p=()=>e.map((e=>cn(e)?e.value:Jt(e)?f(e):y(e)?mn(e,u,2):void 0))):p=y(e)?t?()=>mn(e,u,2):()=>(h&&h(),vn(e,u,3,[w])):a,t&&o){const e=p;p=()=>lo(e())}let b,w=e=>{h=T.onStop=()=>{mn(e,u,4),h=T.onStop=void 0}};if(Ci){if(w=a,t?n&&vn(t,u,3,[p(),v?[]:void 0,w]):p(),"sync"!==r)return a;{const e=Sr(to);b=e.__watcherHandles||(e.__watcherHandles=[])}}let _=v?new Array(e.length).fill(oo):oo;const x=()=>{if(T.active&&T.dirty)if(t){const e=T.run();(o||m||(v?e.some(((e,t)=>M(e,_[t]))):M(e,_)))&&(h&&h(),vn(t,u,3,[e,_===oo?void 0:v&&_[0]===oo?[]:_,w]),_=e)}else T.run()};let S;x.allowRecurse=!!t,"sync"===r?S=x:"post"===r?S=()=>Nr(x,u&&u.suspense):(x.pre=!0,u&&(x.id=u.uid),S=()=>Ln(x));const T=new ze(p,a,S),C=De,k=()=>{T.stop(),C&&d(C.effects,T)};return t?n?x():_=T.run():"post"===r?Nr(T.run.bind(T),u&&u.suspense):T.run(),b&&b.push(k),k}function so(e,t,n){const o=this.proxy,r=b(e)?e.includes(".")?ao(o,e):()=>o[e]:e.bind(o,o);let i;y(t)?i=t:(i=t.handler,n=t);const s=xi(this),a=io(r,i.bind(o),n);return s(),a}function ao(e,t){const n=t.split(".");return()=>{let t=e;for(let e=0;e<n.length&&t;e++)t=t[n[e]];return t}}function lo(e,t,n=0,o){if(!_(e)||e.__v_skip)return e;if(t&&t>0){if(n>=t)return e;n++}if((o=o||new Set).has(e))return e;if(o.add(e),cn(e))lo(e.value,t,n,o);else if(g(e))for(let r=0;r<e.length;r++)lo(e[r],t,n,o);else if(v(e)||m(e))e.forEach((e=>{lo(e,t,n,o)}));else if(C(e))for(const r in e)lo(e[r],t,n,o);return e}function co(e,t){if(null===Hn)return e;const n=$i(Hn)||Hn.proxy,o=e.dirs||(e.dirs=[]);for(let r=0;r<t.length;r++){let[e,s,a,l=i]=t[r];e&&(y(e)&&(e={mounted:e,updated:e}),e.deep&&lo(s),o.push({dir:e,instance:n,value:s,oldValue:void 0,arg:a,modifiers:l}))}return e}function uo(e,t,n,o){const r=e.dirs,i=t&&t.dirs;for(let s=0;s<r.length;s++){const a=r[s];i&&(a.oldValue=i[s].value);let l=a.dir[o];l&&(Je(),vn(l,n,8,[e.el,a,e,t]),Ke())}}const fo=Symbol("_leaveCb"),po=Symbol("_enterCb");const ho=[Function,Array],go={mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:ho,onEnter:ho,onAfterEnter:ho,onEnterCancelled:ho,onBeforeLeave:ho,onLeave:ho,onAfterLeave:ho,onLeaveCancelled:ho,onBeforeAppear:ho,onAppear:ho,onAfterAppear:ho,onAppearCancelled:ho},mo={name:"BaseTransition",props:go,setup(e,{slots:t}){const n=bi(),o=function(){const e={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return Wo((()=>{e.isMounted=!0})),Uo((()=>{e.isUnmounting=!0})),e}();return()=>{const r=t.default&&xo(t.default(),!0);if(!r||!r.length)return;let i=r[0];if(r.length>1)for(const e of r)if(e.type!==qr){i=e;break}const s=tn(e),{mode:a}=s;if(o.isLeaving)return bo(i);const l=wo(i);if(!l)return bo(i);const c=yo(l,s,o,n);_o(l,c);const u=n.subTree,f=u&&wo(u);if(f&&f.type!==qr&&!ni(l,f)){const e=yo(f,s,o,n);if(_o(f,e),"out-in"===a)return o.isLeaving=!0,e.afterLeave=()=>{o.isLeaving=!1,!1!==n.update.active&&(n.effect.dirty=!0,n.update())},bo(i);"in-out"===a&&l.type!==qr&&(e.delayLeave=(e,t,n)=>{vo(o,f)[String(f.key)]=f,e[fo]=()=>{t(),e[fo]=void 0,delete c.delayedLeave},c.delayedLeave=n})}return i}}};function vo(e,t){const{leavingVNodes:n}=e;let o=n.get(t.type);return o||(o=Object.create(null),n.set(t.type,o)),o}function yo(e,t,n,o){const{appear:r,mode:i,persisted:s=!1,onBeforeEnter:a,onEnter:l,onAfterEnter:c,onEnterCancelled:u,onBeforeLeave:f,onLeave:d,onAfterLeave:p,onLeaveCancelled:h,onBeforeAppear:m,onAppear:v,onAfterAppear:y,onAppearCancelled:b}=t,w=String(e.key),_=vo(n,e),x=(e,t)=>{e&&vn(e,o,9,t)},S=(e,t)=>{const n=t[1];x(e,t),g(e)?e.every((e=>e.length<=1))&&n():e.length<=1&&n()},T={mode:i,persisted:s,beforeEnter(t){let o=a;if(!n.isMounted){if(!r)return;o=m||a}t[fo]&&t[fo](!0);const i=_[w];i&&ni(e,i)&&i.el[fo]&&i.el[fo](),x(o,[t])},enter(e){let t=l,o=c,i=u;if(!n.isMounted){if(!r)return;t=v||l,o=y||c,i=b||u}let s=!1;const a=e[po]=t=>{s||(s=!0,x(t?i:o,[e]),T.delayedLeave&&T.delayedLeave(),e[po]=void 0)};t?S(t,[e,a]):a()},leave(t,o){const r=String(e.key);if(t[po]&&t[po](!0),n.isUnmounting)return o();x(f,[t]);let i=!1;const s=t[fo]=n=>{i||(i=!0,o(),x(n?h:p,[t]),t[fo]=void 0,_[r]===e&&delete _[r])};_[r]=e,d?S(d,[t,s]):s()},clone:e=>yo(e,t,n,o)};return T}function bo(e){if(Eo(e))return(e=li(e)).children=null,e}function wo(e){return Eo(e)?e.children?e.children[0]:void 0:e}function _o(e,t){6&e.shapeFlag&&e.component?_o(e.component.subTree,t):128&e.shapeFlag?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function xo(e,t=!1,n){let o=[],r=0;for(let i=0;i<e.length;i++){let s=e[i];const a=null==n?s.key:String(n)+String(null!=s.key?s.key:i);s.type===Wr?(128&s.patchFlag&&r++,o=o.concat(xo(s.children,t,a))):(t||s.type!==qr)&&o.push(null!=a?li(s,{key:a}):s)}if(r>1)for(let i=0;i<o.length;i++)o[i].patchFlag=-2;return o}
/*! #__NO_SIDE_EFFECTS__ */function So(e,t){return y(e)?(()=>f({name:e.name},t,{setup:e}))():e}const To=e=>!!e.type.__asyncLoader
/*! #__NO_SIDE_EFFECTS__ */;function Co(e){y(e)&&(e={loader:e});const{loader:t,loadingComponent:n,errorComponent:o,delay:r=200,timeout:i,suspensible:s=!0,onError:a}=e;let l,c=null,u=0;const f=()=>{let e;return c||(e=c=t().catch((e=>{if(e=e instanceof Error?e:new Error(String(e)),a)return new Promise(((t,n)=>{a(e,(()=>t((u++,c=null,f()))),(()=>n(e)),u+1)}));throw e})).then((t=>e!==c&&c?c:(t&&(t.__esModule||"Module"===t[Symbol.toStringTag])&&(t=t.default),l=t,t))))};return So({name:"AsyncComponentWrapper",__asyncLoader:f,get __asyncResolved(){return l},setup(){const e=yi;if(l)return()=>ko(l,e);const t=t=>{c=null,yn(t,e,13,!o)};if(s&&e.suspense||Ci)return f().then((t=>()=>ko(t,e))).catch((e=>(t(e),()=>o?ai(o,{error:e}):null)));const a=un(!1),u=un(),d=un(!!r);return r&&setTimeout((()=>{d.value=!1}),r),null!=i&&setTimeout((()=>{if(!a.value&&!u.value){const e=new Error(`Async component timed out after ${i}ms.`);t(e),u.value=e}}),i),f().then((()=>{a.value=!0,e.parent&&Eo(e.parent.vnode)&&(e.parent.effect.dirty=!0,Ln(e.parent.update))})).catch((e=>{t(e),u.value=e})),()=>a.value&&l?ko(l,e):u.value&&o?ai(o,{error:u.value}):n&&!d.value?ai(n):void 0}})}function ko(e,t){const{ref:n,props:o,children:r,ce:i}=t.vnode,s=ai(e,o,r);return s.ref=n,s.ce=i,delete t.vnode.ce,s}const Eo=e=>e.type.__isKeepAlive;class $o{constructor(e){this.max=e,this._cache=new Map,this._keys=new Set,this._max=parseInt(e,10)}get(e){const{_cache:t,_keys:n,_max:o}=this,r=t.get(e);if(r)n.delete(e),n.add(e);else if(n.add(e),o&&n.size>o){const e=n.values().next().value;this.pruneCacheEntry(t.get(e)),this.delete(e)}return r}set(e,t){this._cache.set(e,t)}delete(e){this._cache.delete(e),this._keys.delete(e)}forEach(e,t){this._cache.forEach(e.bind(t))}}const Oo={name:"KeepAlive",__isKeepAlive:!0,props:{include:[String,RegExp,Array],exclude:[String,RegExp,Array],max:[String,Number],matchBy:{type:String,default:"name"},cache:Object},setup(e,{slots:t}){const n=bi(),o=n.ctx;if(!o.renderer)return()=>{const e=t.default&&t.default();return e&&1===e.length?e[0]:e};const r=e.cache||new $o(e.max);r.pruneCacheEntry=s;let i=null;function s(t){var o;!i||!ni(t,i)||"key"===e.matchBy&&t.key!==i.key?(Io(o=t),u(o,n,a,!0)):i&&Io(i)}const a=n.suspense,{renderer:{p:l,m:c,um:u,o:{createElement:f}}}=o,d=f("div");function p(t){r.forEach(((n,o)=>{const i=Bo(n,e.matchBy);!i||t&&t(i)||(r.delete(o),s(n))}))}o.activate=(e,t,n,o,r)=>{const i=e.component;if(i.ba){const e=i.isDeactivated;i.isDeactivated=!1,I(i.ba),i.isDeactivated=e}c(e,t,n,0,a),l(i.vnode,e,t,n,i,a,o,e.slotScopeIds,r),Nr((()=>{i.isDeactivated=!1,i.a&&I(i.a);const t=e.props&&e.props.onVnodeMounted;t&&gi(t,i.parent,e)}),a)},o.deactivate=e=>{const t=e.component;t.bda&&Fo(t.bda),c(e,d,null,1,a),Nr((()=>{t.bda&&t.bda.forEach((e=>e.__called=!1)),t.da&&I(t.da);const n=e.props&&e.props.onVnodeUnmounted;n&&gi(n,t.parent,e),t.isDeactivated=!0}),a)},ro((()=>[e.include,e.exclude,e.matchBy]),(([e,t])=>{e&&p((t=>Ao(e,t))),t&&p((e=>!Ao(t,e)))}),{flush:"post",deep:!0});let h=null;const g=()=>{null!=h&&r.set(h,No(n.subTree))};return Wo(g),qo(g),Uo((()=>{r.forEach(((t,o)=>{r.delete(o),s(t);const{subTree:i,suspense:a}=n,l=No(i);if(t.type!==l.type||"key"===e.matchBy&&t.key!==l.key);else{l.component.bda&&I(l.component.bda),Io(l);const e=l.component.da;e&&Nr(e,a)}}))})),()=>{if(h=null,!t.default)return null;const n=t.default(),o=n[0];if(n.length>1)return i=null,n;if(!ti(o)||!(4&o.shapeFlag)&&!eo(o.type))return i=null,o;let s=No(o);const a=s.type,l=Bo(s,e.matchBy),{include:c,exclude:u}=e;if(c&&(!l||!Ao(c,l))||u&&l&&Ao(u,l))return i=s,o;const f=null==s.key?a:s.key,d=r.get(f);return s.el&&(s=li(s),eo(o.type)&&(o.ssContent=s)),h=f,d&&(s.el=d.el,s.component=d.component,s.transition&&_o(s,s.transition),s.shapeFlag|=512),s.shapeFlag|=256,i=s,eo(o.type)?o:s}}},Lo=Oo;function Ao(e,t){return g(e)?e.some((e=>Ao(e,t))):b(e)?e.split(",").includes(t):"[object RegExp]"===T(e)&&e.test(t)}function Po(e,t){Ro(e,"a",t)}function jo(e,t){Ro(e,"da",t)}function Ro(e,t,n=yi){const o=e.__wdc||(e.__wdc=()=>{let t=n;for(;t;){if(t.isDeactivated)return;t=t.parent}return e()});if(o.__called=!1,Do(t,o,n),n){let e=n.parent;for(;e&&e.parent;)Eo(e.parent.vnode)&&Mo(o,t,n,e),e=e.parent}}function Mo(e,t,n,o){const r=Do(t,e,o,!0);Go((()=>{d(o[t],r)}),n)}function Io(e){e.shapeFlag&=-257,e.shapeFlag&=-513}function No(e){return eo(e.type)?e.ssContent:e}function Bo(e,t){if("name"===t){const t=e.type;return Oi(To(e)?t.__asyncResolved||{}:t)}return String(e.key)}function Fo(e){for(let t=0;t<e.length;t++){const n=e[t];n.__called||(n(),n.__called=!0)}}function Do(e,t,n=yi,o=!1){if(n){if(r=e,Ae.indexOf(r)>-1&&n.$pageInstance){if(n.type.__reserved)return;if(n!==n.$pageInstance&&(n=n.$pageInstance,function(e){return["onLoad","onShow"].indexOf(e)>-1}(e))){const o=n.proxy;vn(t.bind(o),n,e,"onLoad"===e?[o.$page.options]:[])}}const i=n[e]||(n[e]=[]),s=t.__weh||(t.__weh=(...o)=>{if(n.isUnmounted)return;Je();const r=xi(n),i=vn(t,n,e,o);return r(),Ke(),i});return o?i.unshift(s):i.push(s),s}var r}const Ho=e=>(t,n=yi)=>(!Ci||"sp"===e)&&Do(e,((...e)=>t(...e)),n),Vo=Ho("bm"),Wo=Ho("m"),zo=Ho("bu"),qo=Ho("u"),Uo=Ho("bum"),Go=Ho("um"),Xo=Ho("sp"),Yo=Ho("rtg"),Zo=Ho("rtc");function Jo(e,t=yi){Do("ec",e,t)}function Ko(e,t,n,o){let r;const i=n&&n[o];if(g(e)||b(e)){r=new Array(e.length);for(let n=0,o=e.length;n<o;n++)r[n]=t(e[n],n,void 0,i&&i[n])}else if("number"==typeof e){r=new Array(e);for(let n=0;n<e;n++)r[n]=t(n+1,n,void 0,i&&i[n])}else if(_(e))if(e[Symbol.iterator])r=Array.from(e,((e,n)=>t(e,n,void 0,i&&i[n])));else{const n=Object.keys(e);r=new Array(n.length);for(let o=0,s=n.length;o<s;o++){const s=n[o];r[o]=t(e[s],s,o,i&&i[o])}}else r=[];return n&&(n[o]=r),r}function Qo(e,t,n={},o,r){if(Hn.isCE||Hn.parent&&To(Hn.parent)&&Hn.parent.isCE)return"default"!==t&&(n.name=t),ai("slot",n,o&&o());let i=e[t];i&&i._c&&(i._d=!1),Yr();const s=i&&er(i(n)),a=ei(Wr,{key:n.key||s&&s.key||`_${t}`},s||(o?o():[]),s&&1===e._?64:-2);return!r&&a.scopeId&&(a.slotScopeIds=[a.scopeId+"-s"]),i&&i._c&&(i._d=!0),a}function er(e){return e.some((e=>!ti(e)||e.type!==qr&&!(e.type===Wr&&!er(e.children))))?e:null}const tr=e=>{if(!e)return null;if(Ti(e)){return $i(e)||e.proxy}return tr(e.parent)},nr=f(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>tr(e.parent),$root:e=>tr(e.root),$emit:e=>e.emit,$options:e=>ur(e),$forceUpdate:e=>e.f||(e.f=(e=>function(){e.effect.dirty=!0,Ln(e.update)})(e)),$nextTick:e=>e.n||(e.n=On.bind(e.proxy)),$watch:e=>so.bind(e)}),or=(e,t)=>e!==i&&!e.__isScriptSetup&&h(e,t),rr={get({_:e},t){const{ctx:n,setupState:o,data:r,props:s,accessCache:a,type:l,appContext:c}=e;let u;if("$"!==t[0]){const l=a[t];if(void 0!==l)switch(l){case 1:return o[t];case 2:return r[t];case 4:return n[t];case 3:return s[t]}else{if(or(o,t))return a[t]=1,o[t];if(r!==i&&h(r,t))return a[t]=2,r[t];if((u=e.propsOptions[0])&&h(u,t))return a[t]=3,s[t];if(n!==i&&h(n,t))return a[t]=4,n[t];sr&&(a[t]=0)}}const f=nr[t];let d,p;return f?("$attrs"===t&&lt(e,0,t),f(e)):(d=l.__cssModules)&&(d=d[t])?d:n!==i&&h(n,t)?(a[t]=4,n[t]):(p=c.config.globalProperties,h(p,t)?p[t]:void 0)},set({_:e},t,n){const{data:o,setupState:r,ctx:s}=e;return or(r,t)?(r[t]=n,!0):o!==i&&h(o,t)?(o[t]=n,!0):!h(e.props,t)&&(("$"!==t[0]||!(t.slice(1)in e))&&(s[t]=n,!0))},has({_:{data:e,setupState:t,accessCache:n,ctx:o,appContext:r,propsOptions:s}},a){let l;return!!n[a]||e!==i&&h(e,a)||or(t,a)||(l=s[0])&&h(l,a)||h(o,a)||h(nr,a)||h(r.config.globalProperties,a)},defineProperty(e,t,n){return null!=n.get?e._.accessCache[t]=0:h(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function ir(e){return g(e)?e.reduce(((e,t)=>(e[t]=null,e)),{}):e}let sr=!0;function ar(e){const t=ur(e),n=e.proxy,o=e.ctx;sr=!1,t.beforeCreate&&lr(t.beforeCreate,e,"bc");const{data:r,computed:i,methods:s,watch:l,provide:c,inject:u,created:f,beforeMount:d,mounted:p,beforeUpdate:h,updated:m,activated:v,deactivated:b,beforeDestroy:w,beforeUnmount:x,destroyed:S,unmounted:T,render:C,renderTracked:k,renderTriggered:E,errorCaptured:$,serverPrefetch:O,expose:L,inheritAttrs:A,components:P,directives:j,filters:R}=t;if(u&&function(e,t,n=a){g(e)&&(e=hr(e));for(const o in e){const n=e[o];let r;r=_(n)?"default"in n?Sr(n.from||o,n.default,!0):Sr(n.from||o):Sr(n),cn(r)?Object.defineProperty(t,o,{enumerable:!0,configurable:!0,get:()=>r.value,set:e=>r.value=e}):t[o]=r}}(u,o,null),s)for(const a in s){const e=s[a];y(e)&&(o[a]=e.bind(n))}if(r){const t=r.call(n,n);_(t)&&(e.data=Gt(t))}if(sr=!0,i)for(const g in i){const e=i[g],t=y(e)?e.bind(n,n):y(e.get)?e.get.bind(n,n):a,r=!y(e)&&y(e.set)?e.set.bind(n):a,s=Li({get:t,set:r});Object.defineProperty(o,g,{enumerable:!0,configurable:!0,get:()=>s.value,set:e=>s.value=e})}if(l)for(const a in l)cr(l[a],o,n,a);if(c){const e=y(c)?c.call(n):c;Reflect.ownKeys(e).forEach((t=>{xr(t,e[t])}))}function M(e,t){g(t)?t.forEach((t=>e(t.bind(n)))):t&&e(t.bind(n))}if(f&&lr(f,e,"c"),M(Vo,d),M(Wo,p),M(zo,h),M(qo,m),M(Po,v),M(jo,b),M(Jo,$),M(Zo,k),M(Yo,E),M(Uo,x),M(Go,T),M(Xo,O),g(L))if(L.length){const t=e.exposed||(e.exposed={});L.forEach((e=>{Object.defineProperty(t,e,{get:()=>n[e],set:t=>n[e]=t})}))}else e.exposed||(e.exposed={});C&&e.render===a&&(e.render=C),null!=A&&(e.inheritAttrs=A),P&&(e.components=P),j&&(e.directives=j);const I=e.appContext.config.globalProperties.$applyOptions;I&&I(t,e,n)}function lr(e,t,n){vn(g(e)?e.map((e=>e.bind(t.proxy))):e.bind(t.proxy),t,n)}function cr(e,t,n,o){const r=o.includes(".")?ao(n,o):()=>n[o];if(b(e)){const n=t[e];y(n)&&ro(r,n)}else if(y(e))ro(r,e.bind(n));else if(_(e))if(g(e))e.forEach((e=>cr(e,t,n,o)));else{const o=y(e.handler)?e.handler.bind(n):t[e.handler];y(o)&&ro(r,o,e)}}function ur(e){const t=e.type,{mixins:n,extends:o}=t,{mixins:r,optionsCache:i,config:{optionMergeStrategies:s}}=e.appContext,a=i.get(t);let l;return a?l=a:r.length||n||o?(l={},r.length&&r.forEach((e=>fr(l,e,s,!0))),fr(l,t,s)):l=t,_(t)&&i.set(t,l),l}function fr(e,t,n,o=!1){const{mixins:r,extends:i}=t;i&&fr(e,i,n,!0),r&&r.forEach((t=>fr(e,t,n,!0)));for(const s in t)if(o&&"expose"===s);else{const o=dr[s]||n&&n[s];e[s]=o?o(e[s],t[s]):t[s]}return e}const dr={data:pr,props:vr,emits:vr,methods:mr,computed:mr,beforeCreate:gr,created:gr,beforeMount:gr,mounted:gr,beforeUpdate:gr,updated:gr,beforeDestroy:gr,beforeUnmount:gr,destroyed:gr,unmounted:gr,activated:gr,deactivated:gr,errorCaptured:gr,serverPrefetch:gr,components:mr,directives:mr,watch:function(e,t){if(!e)return t;if(!t)return e;const n=f(Object.create(null),e);for(const o in t)n[o]=gr(e[o],t[o]);return n},provide:pr,inject:function(e,t){return mr(hr(e),hr(t))}};function pr(e,t){return t?e?function(){return f(y(e)?e.call(this,this):e,y(t)?t.call(this,this):t)}:t:e}function hr(e){if(g(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function gr(e,t){return e?[...new Set([].concat(e,t))]:t}function mr(e,t){return e?f(Object.create(null),e,t):t}function vr(e,t){return e?g(e)&&g(t)?[...new Set([...e,...t])]:f(Object.create(null),ir(e),ir(null!=t?t:{})):t}function yr(){return{app:null,config:{isNativeTag:l,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let br=0;function wr(e,t){return function(n,o=null){y(n)||(n=f({},n)),null==o||_(o)||(o=null);const r=yr(),i=new WeakSet;let s=!1;const a=r.app={_uid:br++,_component:n,_props:o,_container:null,_context:r,_instance:null,version:Pi,get config(){return r.config},set config(e){},use:(e,...t)=>(i.has(e)||(e&&y(e.install)?(i.add(e),e.install(a,...t)):y(e)&&(i.add(e),e(a,...t))),a),mixin:e=>(r.mixins.includes(e)||r.mixins.push(e),a),component:(e,t)=>t?(r.components[e]=t,a):r.components[e],directive:(e,t)=>t?(r.directives[e]=t,a):r.directives[e],mount(i,l,c){if(!s){const u=ai(n,o);return u.appContext=r,!0===c?c="svg":!1===c&&(c=void 0),l&&t?t(u,i):e(u,i,c),s=!0,a._container=i,i.__vue_app__=a,a._instance=u.component,$i(u.component)||u.component.proxy}},unmount(){s&&(e(null,a._container),delete a._container.__vue_app__)},provide:(e,t)=>(r.provides[e]=t,a),runWithContext(e){const t=_r;_r=a;try{return e()}finally{_r=t}}};return a}}let _r=null;function xr(e,t){if(yi){let n=yi.provides;const o=yi.parent&&yi.parent.provides;o===n&&(n=yi.provides=Object.create(o)),n[e]=t,"app"===yi.type.mpType&&yi.appContext.app.provide(e,t)}else;}function Sr(e,t,n=!1){const o=yi||Hn;if(o||_r){const r=o?null==o.parent?o.vnode.appContext&&o.vnode.appContext.provides:o.parent.provides:_r._context.provides;if(r&&e in r)return r[e];if(arguments.length>1)return n&&y(t)?t.call(o&&o.proxy):t}}function Tr(e,t,n,o){const[r,s]=e.propsOptions;let a,l=!1;if(t)for(let i in t){if(E(i))continue;const c=t[i];let u;r&&h(r,u=L(i))?s&&s.includes(u)?(a||(a={}))[u]=c:n[u]=c:Dn(e.emitsOptions,i)||i in o&&c===o[i]||(o[i]=c,l=!0)}if(s){const t=tn(n),o=a||i;for(let i=0;i<s.length;i++){const a=s[i];n[a]=Cr(r,t,a,o[a],e,!h(o,a))}}return l}function Cr(e,t,n,o,r,i){const s=e[n];if(null!=s){const e=h(s,"default");if(e&&void 0===o){const e=s.default;if(s.type!==Function&&!s.skipFactory&&y(e)){const{propsDefaults:i}=r;if(n in i)o=i[n];else{const s=xi(r);o=i[n]=e.call(null,t),s()}}else o=e}s[0]&&(i&&!e?o=!1:!s[1]||""!==o&&o!==P(n)||(o=!0))}return o}function kr(e,t,n=!1){const o=t.propsCache,r=o.get(e);if(r)return r;const a=e.props,l={},c=[];let u=!1;if(!y(e)){const o=e=>{u=!0;const[n,o]=kr(e,t,!0);f(l,n),o&&c.push(...o)};!n&&t.mixins.length&&t.mixins.forEach(o),e.extends&&o(e.extends),e.mixins&&e.mixins.forEach(o)}if(!a&&!u)return _(e)&&o.set(e,s),s;if(g(a))for(let s=0;s<a.length;s++){const e=L(a[s]);Er(e)&&(l[e]=i)}else if(a)for(const i in a){const e=L(i);if(Er(e)){const t=a[i],n=l[e]=g(t)||y(t)?{type:t}:f({},t);if(n){const t=Lr(Boolean,n.type),o=Lr(String,n.type);n[0]=t>-1,n[1]=o<0||t<o,(t>-1||h(n,"default"))&&c.push(e)}}}const d=[l,c];return _(e)&&o.set(e,d),d}function Er(e){return"$"!==e[0]&&!E(e)}function $r(e){if(null===e)return"null";if("function"==typeof e)return e.name||"";if("object"==typeof e){return e.constructor&&e.constructor.name||""}return""}function Or(e,t){return $r(e)===$r(t)}function Lr(e,t){return g(t)?t.findIndex((t=>Or(t,e))):y(t)&&Or(t,e)?0:-1}const Ar=e=>"_"===e[0]||"$stable"===e,Pr=e=>g(e)?e.map(fi):[fi(e)],jr=(e,t,n)=>{if(t._n)return t;const o=zn(((...e)=>Pr(t(...e))),n);return o._c=!1,o},Rr=(e,t,n)=>{const o=e._ctx;for(const r in e){if(Ar(r))continue;const n=e[r];if(y(n))t[r]=jr(0,n,o);else if(null!=n){const e=Pr(n);t[r]=()=>e}}},Mr=(e,t)=>{const n=Pr(t);e.slots.default=()=>n};function Ir(e,t,n,o,r=!1){if(g(e))return void e.forEach(((e,i)=>Ir(e,t&&(g(t)?t[i]:t),n,o,r)));if(To(o)&&!r)return;const s=4&o.shapeFlag?$i(o.component)||o.component.proxy:o.el,a=r?null:s,{i:l,r:c}=e,u=t&&t.r,f=l.refs===i?l.refs={}:l.refs,p=l.setupState;if(null!=u&&u!==c&&(b(u)?(f[u]=null,h(p,u)&&(p[u]=null)):cn(u)&&(u.value=null)),y(c))mn(c,l,12,[a,f]);else{const t=b(c),o=cn(c);if(t||o){const i=()=>{if(e.f){const n=t?h(p,c)?p[c]:f[c]:c.value;r?g(n)&&d(n,s):g(n)?n.includes(s)||n.push(s):t?(f[c]=[s],h(p,c)&&(p[c]=f[c])):(c.value=[s],e.k&&(f[e.k]=c.value))}else t?(f[c]=a,h(p,c)&&(p[c]=a)):o&&(c.value=a,e.k&&(f[e.k]=a))};a?(i.id=-1,Nr(i,n)):i()}}}const Nr=function(e,t){var n;t&&t.pendingBranch?g(e)?t.effects.push(...e):t.effects.push(e):(g(n=e)?Tn.push(...n):Cn&&Cn.includes(n,n.allowRecurse?kn+1:kn)||Tn.push(n),An())};function Br(e){return function(e,t){D().__VUE__=!0;const{insert:n,remove:o,patchProp:r,forcePatchProp:l,createElement:c,createText:u,createComment:d,setText:p,setElementText:g,parentNode:m,nextSibling:v,setScopeId:y=a,insertStaticContent:b}=e,w=(e,t,n,o=null,r=null,i=null,s,a=null,l=!!t.dynamicChildren)=>{if(e===t)return;e&&!ni(e,t)&&(o=te(e),Z(e,r,i,!0),e=null),-2===t.patchFlag&&(l=!1,t.dynamicChildren=null);const{type:c,ref:u,shapeFlag:f}=t;switch(c){case zr:_(e,t,n,o);break;case qr:S(e,t,n,o);break;case Ur:null==e&&T(t,n,o,s);break;case Wr:F(e,t,n,o,r,i,s,a,l);break;default:1&f?$(e,t,n,o,r,i,s,a,l):6&f?H(e,t,n,o,r,i,s,a,l):(64&f||128&f)&&c.process(e,t,n,o,r,i,s,a,l,re)}null!=u&&r&&Ir(u,e&&e.ref,i,t||e,!t)},_=(e,t,o,r)=>{if(null==e)n(t.el=u(t.children),o,r);else{const n=t.el=e.el;t.children!==e.children&&p(n,t.children)}},S=(e,t,o,r)=>{null==e?n(t.el=d(t.children||""),o,r):t.el=e.el},T=(e,t,n,o)=>{[e.el,e.anchor]=b(e.children,t,n,o,e.el,e.anchor)},C=({el:e,anchor:t},o,r)=>{let i;for(;e&&e!==t;)i=v(e),n(e,o,r),e=i;n(t,o,r)},k=({el:e,anchor:t})=>{let n;for(;e&&e!==t;)n=v(e),o(e),e=n;o(t)},$=(e,t,n,o,r,i,s,a,l)=>{"svg"===t.type?s="svg":"math"===t.type&&(s="mathml"),null==e?O(t,n,o,r,i,s,a,l):R(e,t,r,i,s,a,l)},O=(e,t,o,i,s,a,l,u)=>{let f,d;const{props:p,shapeFlag:h,transition:m,dirs:v}=e;if(f=e.el=c(e.type,a,p&&p.is,p),8&h?g(f,e.children):16&h&&j(e.children,f,null,i,s,Fr(e,a),l,u),v&&uo(e,null,i,"created"),A(f,e,e.scopeId,l,i),p){for(const t in p)"value"===t||E(t)||r(f,t,null,p[t],a,e.children,i,s,ee);"value"in p&&r(f,"value",null,p.value,a),(d=p.onVnodeBeforeMount)&&gi(d,i,e)}Object.defineProperty(f,"__vueParentComponent",{value:i,enumerable:!1}),v&&uo(e,null,i,"beforeMount");const y=function(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}(s,m);y&&m.beforeEnter(f),n(f,t,o),((d=p&&p.onVnodeMounted)||y||v)&&Nr((()=>{d&&gi(d,i,e),y&&m.enter(f),v&&uo(e,null,i,"mounted")}),s)},A=(e,t,n,o,r)=>{if(n&&y(e,n),o)for(let i=0;i<o.length;i++)y(e,o[i]);if(r){if(t===r.subTree){const t=r.vnode;A(e,t,t.scopeId,t.slotScopeIds,r.parent)}}},j=(e,t,n,o,r,i,s,a,l=0)=>{for(let c=l;c<e.length;c++){const l=e[c]=a?di(e[c]):fi(e[c]);w(null,l,t,n,o,r,i,s,a)}},R=(e,t,n,o,s,a,c)=>{const u=t.el=e.el;let{patchFlag:f,dynamicChildren:d,dirs:p}=t;f|=16&e.patchFlag;const h=e.props||i,m=t.props||i;let v;if(n&&Dr(n,!1),(v=m.onVnodeBeforeUpdate)&&gi(v,n,t,e),p&&uo(t,e,n,"beforeUpdate"),n&&Dr(n,!0),d?M(e.dynamicChildren,d,u,n,o,Fr(t,s),a):c||U(e,t,u,null,n,o,Fr(t,s),a,!1),f>0){if(16&f)B(u,t,h,m,n,o,s);else if(2&f&&h.class!==m.class&&r(u,"class",null,m.class,s),4&f&&r(u,"style",h.style,m.style,s),8&f){const i=t.dynamicProps;for(let t=0;t<i.length;t++){const a=i[t],c=h[a],f=m[a];(f!==c||"value"===a||l&&l(u,a))&&r(u,a,c,f,s,e.children,n,o,ee)}}1&f&&e.children!==t.children&&g(u,t.children)}else c||null!=d||B(u,t,h,m,n,o,s);((v=m.onVnodeUpdated)||p)&&Nr((()=>{v&&gi(v,n,t,e),p&&uo(t,e,n,"updated")}),o)},M=(e,t,n,o,r,i,s)=>{for(let a=0;a<t.length;a++){const l=e[a],c=t[a],u=l.el&&(l.type===Wr||!ni(l,c)||70&l.shapeFlag)?m(l.el):n;w(l,c,u,null,o,r,i,s,!0)}},B=(e,t,n,o,s,a,c)=>{if(n!==o){if(n!==i)for(const i in n)E(i)||i in o||r(e,i,n[i],null,c,t.children,s,a,ee);for(const i in o){if(E(i))continue;const u=o[i],f=n[i];(u!==f&&"value"!==i||l&&l(e,i))&&r(e,i,f,u,c,t.children,s,a,ee)}"value"in o&&r(e,"value",n.value,o.value,c)}},F=(e,t,o,r,i,s,a,l,c)=>{const f=t.el=e?e.el:u(""),d=t.anchor=e?e.anchor:u("");let{patchFlag:p,dynamicChildren:h,slotScopeIds:g}=t;g&&(l=l?l.concat(g):g),null==e?(n(f,o,r),n(d,o,r),j(t.children||[],o,d,i,s,a,l,c)):p>0&&64&p&&h&&e.dynamicChildren?(M(e.dynamicChildren,h,o,i,s,a,l),(null!=t.key||i&&t===i.subTree)&&Hr(e,t,!0)):U(e,t,o,d,i,s,a,l,c)},H=(e,t,n,o,r,i,s,a,l)=>{t.slotScopeIds=a,null==e?512&t.shapeFlag?r.ctx.activate(t,n,o,s,l):V(t,n,o,r,i,s,l):W(e,t,l)},V=(e,t,n,o,r,s,a)=>{const l=e.component=function(e,t,n){const o=e.type,r=(t?t.appContext:e.appContext)||mi,s={uid:vi++,vnode:e,type:o,parent:t,appContext:r,root:null,next:null,subTree:null,effect:null,update:null,scope:new Ve(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(r.provides),accessCache:null,renderCache:[],components:null,directives:null,propsOptions:kr(o,r),emitsOptions:Fn(o,r),emit:null,emitted:null,propsDefaults:i,inheritAttrs:o.inheritAttrs,ctx:i,data:i,props:i,attrs:i,slots:i,refs:i,setupState:i,setupContext:null,attrsProxy:null,slotsProxy:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,bda:null,da:null,ba:null,a:null,rtg:null,rtc:null,ec:null,sp:null};s.ctx={_:s},s.root=t?t.root:s,s.emit=Nn.bind(null,s),s.$pageInstance=t&&t.$pageInstance,e.ce&&e.ce(s);return s}(e,o,r);if(Eo(e)&&(l.ctx.renderer=re),function(e,t=!1){t&&_i(t);const{props:n,children:o}=e.vnode,r=Ti(e);(function(e,t,n,o=!1){const r={},i={};N(i,oi,1),e.propsDefaults=Object.create(null),Tr(e,t,r,i);for(const s in e.propsOptions[0])s in r||(r[s]=void 0);n?e.props=o?r:Xt(r):e.type.props?e.props=r:e.props=i,e.attrs=i})(e,n,r,t),((e,t)=>{if(32&e.vnode.shapeFlag){const n=t._;n?(e.slots=tn(t),N(t,"_",n)):Rr(t,e.slots={})}else e.slots={},t&&Mr(e,t);N(e.slots,oi,1)})(e,o);const i=r?function(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=nn(new Proxy(e.ctx,rr));const{setup:o}=n;if(o){const n=e.setupContext=o.length>1?function(e){const t=t=>{e.exposed=t||{}};return{get attrs(){return function(e){return e.attrsProxy||(e.attrsProxy=new Proxy(e.attrs,{get:(t,n)=>(lt(e,0,"$attrs"),t[n])}))}(e)},slots:e.slots,emit:e.emit,expose:t}}(e):null,r=xi(e);Je();const i=mn(o,e,0,[e.props,n]);if(Ke(),r(),x(i)){if(i.then(Si,Si),t)return i.then((n=>{ki(e,n,t)})).catch((t=>{yn(t,e,0)}));e.asyncDep=i}else ki(e,i,t)}else Ei(e,t)}(e,t):void 0;t&&_i(!1)}(l),l.asyncDep){if(r&&r.registerDep(l,z),!e.el){const e=l.subTree=ai(qr);S(null,e,t,n)}}else z(l,e,t,n,r,s,a)},W=(e,t,n)=>{const o=t.component=e.component;if(function(e,t,n){const{props:o,children:r,component:i}=e,{props:s,children:a,patchFlag:l}=t,c=i.emitsOptions;if(t.dirs||t.transition)return!0;if(!(n&&l>=0))return!(!r&&!a||a&&a.$stable)||o!==s&&(o?!s||Xn(o,s,c):!!s);if(1024&l)return!0;if(16&l)return o?Xn(o,s,c):!!s;if(8&l){const e=t.dynamicProps;for(let t=0;t<e.length;t++){const n=e[t];if(s[n]!==o[n]&&!Dn(c,n))return!0}}return!1}(e,t,n)){if(o.asyncDep&&!o.asyncResolved)return void q(o,t,n);o.next=t,function(e){const t=xn.indexOf(e);t>Sn&&xn.splice(t,1)}(o.update),o.effect.dirty=!0,o.update()}else t.el=e.el,o.vnode=t},z=(e,t,n,o,r,i,s)=>{const l=()=>{if(e.isMounted){let{next:t,bu:n,u:o,parent:a,vnode:c}=e;{const n=Vr(e);if(n)return t&&(t.el=c.el,q(e,t,s)),void n.asyncDep.then((()=>{e.isUnmounted||l()}))}let u,f=t;Dr(e,!1),t?(t.el=c.el,q(e,t,s)):t=c,n&&I(n),(u=t.props&&t.props.onVnodeBeforeUpdate)&&gi(u,a,t,c),Dr(e,!0);const d=qn(e),p=e.subTree;e.subTree=d,w(p,d,m(p.el),te(p),e,r,i),t.el=d.el,null===f&&function({vnode:e,parent:t},n){for(;t;){const o=t.subTree;if(o.suspense&&o.suspense.activeBranch===e&&(o.el=e.el),o!==e)break;(e=t.vnode).el=n,t=t.parent}}(e,d.el),o&&Nr(o,r),(u=t.props&&t.props.onVnodeUpdated)&&Nr((()=>gi(u,a,t,c)),r)}else{let s;const{el:a,props:l}=t,{bm:c,m:u,parent:f}=e,d=To(t);if(Dr(e,!1),c&&I(c),!d&&(s=l&&l.onVnodeBeforeMount)&&gi(s,f,t),Dr(e,!0),a&&se){const n=()=>{e.subTree=qn(e),se(a,e.subTree,e,r,null)};d?t.type.__asyncLoader().then((()=>!e.isUnmounted&&n())):n()}else{const s=e.subTree=qn(e);w(null,s,n,o,e,r,i),t.el=s.el}if(u&&Nr(u,r),!d&&(s=l&&l.onVnodeMounted)){const e=t;Nr((()=>gi(s,f,e)),r)}(256&t.shapeFlag||f&&To(f.vnode)&&256&f.vnode.shapeFlag)&&(e.ba&&Fo(e.ba),e.a&&Nr(e.a,r)),e.isMounted=!0,t=n=o=null}},c=e.effect=new ze(l,a,(()=>Ln(u)),e.scope),u=e.update=()=>{c.dirty&&c.run()};u.id=e.uid,Dr(e,!0),u()},q=(e,t,n)=>{t.component=e;const o=e.vnode.props;e.vnode=t,e.next=null,function(e,t,n,o){const{props:r,attrs:i,vnode:{patchFlag:s}}=e,a=tn(r),[l]=e.propsOptions;let c=!1;if(!(o||s>0)||16&s){let o;Tr(e,t,r,i)&&(c=!0);for(const i in a)t&&(h(t,i)||(o=P(i))!==i&&h(t,o))||(l?!n||void 0===n[i]&&void 0===n[o]||(r[i]=Cr(l,a,i,void 0,e,!0)):delete r[i]);if(i!==a)for(const e in i)t&&h(t,e)||(delete i[e],c=!0)}else if(8&s){const n=e.vnode.dynamicProps;for(let o=0;o<n.length;o++){let s=n[o];if(Dn(e.emitsOptions,s))continue;const u=t[s];if(l)if(h(i,s))u!==i[s]&&(i[s]=u,c=!0);else{const t=L(s);r[t]=Cr(l,a,t,u,e,!1)}else u!==i[s]&&(i[s]=u,c=!0)}}c&&ct(e,"set","$attrs")}(e,t.props,o,n),((e,t,n)=>{const{vnode:o,slots:r}=e;let s=!0,a=i;if(32&o.shapeFlag){const e=t._;e?n&&1===e?s=!1:(f(r,t),n||1!==e||delete r._):(s=!t.$stable,Rr(t,r)),a=t}else t&&(Mr(e,t),a={default:1});if(s)for(const i in r)Ar(i)||null!=a[i]||delete r[i]})(e,t.children,n),Je(),Pn(e),Ke()},U=(e,t,n,o,r,i,s,a,l=!1)=>{const c=e&&e.children,u=e?e.shapeFlag:0,f=t.children,{patchFlag:d,shapeFlag:p}=t;if(d>0){if(128&d)return void X(c,f,n,o,r,i,s,a,l);if(256&d)return void G(c,f,n,o,r,i,s,a,l)}8&p?(16&u&&ee(c,r,i),f!==c&&g(n,f)):16&u?16&p?X(c,f,n,o,r,i,s,a,l):ee(c,r,i,!0):(8&u&&g(n,""),16&p&&j(f,n,o,r,i,s,a,l))},G=(e,t,n,o,r,i,a,l,c)=>{t=t||s;const u=(e=e||s).length,f=t.length,d=Math.min(u,f);let p;for(p=0;p<d;p++){const o=t[p]=c?di(t[p]):fi(t[p]);w(e[p],o,n,null,r,i,a,l,c)}u>f?ee(e,r,i,!0,!1,d):j(t,n,o,r,i,a,l,c,d)},X=(e,t,n,o,r,i,a,l,c)=>{let u=0;const f=t.length;let d=e.length-1,p=f-1;for(;u<=d&&u<=p;){const o=e[u],s=t[u]=c?di(t[u]):fi(t[u]);if(!ni(o,s))break;w(o,s,n,null,r,i,a,l,c),u++}for(;u<=d&&u<=p;){const o=e[d],s=t[p]=c?di(t[p]):fi(t[p]);if(!ni(o,s))break;w(o,s,n,null,r,i,a,l,c),d--,p--}if(u>d){if(u<=p){const e=p+1,s=e<f?t[e].el:o;for(;u<=p;)w(null,t[u]=c?di(t[u]):fi(t[u]),n,s,r,i,a,l,c),u++}}else if(u>p)for(;u<=d;)Z(e[u],r,i,!0),u++;else{const h=u,g=u,m=new Map;for(u=g;u<=p;u++){const e=t[u]=c?di(t[u]):fi(t[u]);null!=e.key&&m.set(e.key,u)}let v,y=0;const b=p-g+1;let _=!1,x=0;const S=new Array(b);for(u=0;u<b;u++)S[u]=0;for(u=h;u<=d;u++){const o=e[u];if(y>=b){Z(o,r,i,!0);continue}let s;if(null!=o.key)s=m.get(o.key);else for(v=g;v<=p;v++)if(0===S[v-g]&&ni(o,t[v])){s=v;break}void 0===s?Z(o,r,i,!0):(S[s-g]=u+1,s>=x?x=s:_=!0,w(o,t[s],n,null,r,i,a,l,c),y++)}const T=_?function(e){const t=e.slice(),n=[0];let o,r,i,s,a;const l=e.length;for(o=0;o<l;o++){const l=e[o];if(0!==l){if(r=n[n.length-1],e[r]<l){t[o]=r,n.push(o);continue}for(i=0,s=n.length-1;i<s;)a=i+s>>1,e[n[a]]<l?i=a+1:s=a;l<e[n[i]]&&(i>0&&(t[o]=n[i-1]),n[i]=o)}}i=n.length,s=n[i-1];for(;i-- >0;)n[i]=s,s=t[s];return n}(S):s;for(v=T.length-1,u=b-1;u>=0;u--){const e=g+u,s=t[e],d=e+1<f?t[e+1].el:o;0===S[u]?w(null,s,n,d,r,i,a,l,c):_&&(v<0||u!==T[v]?Y(s,n,d,2):v--)}}},Y=(e,t,o,r,i=null)=>{const{el:s,type:a,transition:l,children:c,shapeFlag:u}=e;if(6&u)return void Y(e.component.subTree,t,o,r);if(128&u)return void e.suspense.move(t,o,r);if(64&u)return void a.move(e,t,o,re);if(a===Wr){n(s,t,o);for(let e=0;e<c.length;e++)Y(c[e],t,o,r);return void n(e.anchor,t,o)}if(a===Ur)return void C(e,t,o);if(2!==r&&1&u&&l)if(0===r)l.beforeEnter(s),n(s,t,o),Nr((()=>l.enter(s)),i);else{const{leave:e,delayLeave:r,afterLeave:i}=l,a=()=>n(s,t,o),c=()=>{e(s,(()=>{a(),i&&i()}))};r?r(s,a,c):c()}else n(s,t,o)},Z=(e,t,n,o=!1,r=!1)=>{const{type:i,props:s,ref:a,children:l,dynamicChildren:c,shapeFlag:u,patchFlag:f,dirs:d}=e;if(null!=a&&Ir(a,null,n,e,!0),256&u)return void t.ctx.deactivate(e);const p=1&u&&d,h=!To(e);let g;if(h&&(g=s&&s.onVnodeBeforeUnmount)&&gi(g,t,e),6&u)Q(e.component,n,o);else{if(128&u)return void e.suspense.unmount(n,o);p&&uo(e,null,t,"beforeUnmount"),64&u?e.type.remove(e,t,n,r,re,o):c&&(i!==Wr||f>0&&64&f)?ee(c,t,n,!1,!0):(i===Wr&&384&f||!r&&16&u)&&ee(l,t,n),o&&J(e)}(h&&(g=s&&s.onVnodeUnmounted)||p)&&Nr((()=>{g&&gi(g,t,e),p&&uo(e,null,t,"unmounted")}),n)},J=e=>{const{type:t,el:n,anchor:r,transition:i}=e;if(t===Wr)return void K(n,r);if(t===Ur)return void k(e);const s=()=>{o(n),i&&!i.persisted&&i.afterLeave&&i.afterLeave()};if(1&e.shapeFlag&&i&&!i.persisted){const{leave:t,delayLeave:o}=i,r=()=>t(n,s);o?o(e.el,s,r):r()}else s()},K=(e,t)=>{let n;for(;e!==t;)n=v(e),o(e),e=n;o(t)},Q=(e,t,n)=>{const{bum:o,scope:r,update:i,subTree:s,um:a}=e;o&&I(o),r.stop(),i&&(i.active=!1,Z(s,e,t,n)),a&&Nr(a,t),Nr((()=>{e.isUnmounted=!0}),t),t&&t.pendingBranch&&!t.isUnmounted&&e.asyncDep&&!e.asyncResolved&&e.suspenseId===t.pendingId&&(t.deps--,0===t.deps&&t.resolve())},ee=(e,t,n,o=!1,r=!1,i=0)=>{for(let s=i;s<e.length;s++)Z(e[s],t,n,o,r)},te=e=>6&e.shapeFlag?te(e.component.subTree):128&e.shapeFlag?e.suspense.next():v(e.anchor||e.el);let ne=!1;const oe=(e,t,n)=>{null==e?t._vnode&&Z(t._vnode,null,null,!0):w(t._vnode||null,e,t,null,null,null,n),ne||(ne=!0,Pn(),jn(),ne=!1),t._vnode=e},re={p:w,um:Z,m:Y,r:J,mt:V,mc:j,pc:U,pbc:M,n:te,o:e};let ie,se;t&&([ie,se]=t(re));return{render:oe,hydrate:ie,createApp:wr(oe,ie)}}(e)}function Fr({type:e,props:t},n){return"svg"===n&&"foreignObject"===e||"mathml"===n&&"annotation-xml"===e&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function Dr({effect:e,update:t},n){e.allowRecurse=t.allowRecurse=n}function Hr(e,t,n=!1){const o=e.children,r=t.children;if(g(o)&&g(r))for(let i=0;i<o.length;i++){const e=o[i];let t=r[i];1&t.shapeFlag&&!t.dynamicChildren&&((t.patchFlag<=0||32===t.patchFlag)&&(t=r[i]=di(r[i]),t.el=e.el),n||Hr(e,t)),t.type===zr&&(t.el=e.el)}}function Vr(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:Vr(t)}const Wr=Symbol.for("v-fgt"),zr=Symbol.for("v-txt"),qr=Symbol.for("v-cmt"),Ur=Symbol.for("v-stc"),Gr=[];let Xr=null;function Yr(e=!1){Gr.push(Xr=e?null:[])}let Zr=1;function Jr(e){Zr+=e}function Kr(e){return e.dynamicChildren=Zr>0?Xr||s:null,Gr.pop(),Xr=Gr[Gr.length-1]||null,Zr>0&&Xr&&Xr.push(e),e}function Qr(e,t,n,o,r,i){return Kr(si(e,t,n,o,r,i,!0))}function ei(e,t,n,o,r){return Kr(ai(e,t,n,o,r,!0))}function ti(e){return!!e&&!0===e.__v_isVNode}function ni(e,t){return e.type===t.type&&e.key===t.key}const oi="__vInternal",ri=({key:e})=>null!=e?e:null,ii=({ref:e,ref_key:t,ref_for:n})=>("number"==typeof e&&(e=""+e),null!=e?b(e)||cn(e)||y(e)?{i:Hn,r:e,k:t,f:!!n}:e:null);function si(e,t=null,n=null,o=0,r=null,i=(e===Wr?0:1),s=!1,a=!1){const l={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&ri(t),ref:t&&ii(t),scopeId:Vn,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetAnchor:null,staticCount:0,shapeFlag:i,patchFlag:o,dynamicProps:r,dynamicChildren:null,appContext:null,ctx:Hn};return a?(pi(l,n),128&i&&e.normalize(l)):n&&(l.shapeFlag|=b(n)?8:16),Zr>0&&!s&&Xr&&(l.patchFlag>0||6&i)&&32!==l.patchFlag&&Xr.push(l),l}const ai=function(e,t=null,n=null,o=0,r=null,i=!1){e&&e!==Zn||(e=qr);if(ti(e)){const o=li(e,t,!0);return n&&pi(o,n),Zr>0&&!i&&Xr&&(6&o.shapeFlag?Xr[Xr.indexOf(e)]=o:Xr.push(o)),o.patchFlag|=-2,o}s=e,y(s)&&"__vccOpts"in s&&(e=e.__vccOpts);var s;if(t){t=function(e){return e?en(e)||oi in e?f({},e):e:null}(t);let{class:e,style:n}=t;e&&!b(e)&&(t.class=ye(e)),_(n)&&(en(n)&&!g(n)&&(n=f({},n)),t.style=ve(n))}const a=b(e)?1:eo(e)?128:(e=>e.__isTeleport)(e)?64:_(e)?4:y(e)?2:0;return si(e,t,n,o,r,a,i,!0)};function li(e,t,n=!1){const{props:o,ref:r,patchFlag:i,children:s}=e,a=t?hi(o||{},t):o;return{__v_isVNode:!0,__v_skip:!0,type:e.type,props:a,key:a&&ri(a),ref:t&&t.ref?n&&r?g(r)?r.concat(ii(t)):[r,ii(t)]:ii(t):r,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:s,target:e.target,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==Wr?-1===i?16:16|i:i,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:e.transition,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&li(e.ssContent),ssFallback:e.ssFallback&&li(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce}}function ci(e=" ",t=0){return ai(zr,null,e,t)}function ui(e="",t=!1){return t?(Yr(),ei(qr,null,e)):ai(qr,null,e)}function fi(e){return null==e||"boolean"==typeof e?ai(qr):g(e)?ai(Wr,null,e.slice()):"object"==typeof e?di(e):ai(zr,null,String(e))}function di(e){return null===e.el&&-1!==e.patchFlag||e.memo?e:li(e)}function pi(e,t){let n=0;const{shapeFlag:o}=e;if(null==t)t=null;else if(g(t))n=16;else if("object"==typeof t){if(65&o){const n=t.default;return void(n&&(n._c&&(n._d=!1),pi(e,n()),n._c&&(n._d=!0)))}{n=32;const o=t._;o||oi in t?3===o&&Hn&&(1===Hn.slots._?t._=1:(t._=2,e.patchFlag|=1024)):t._ctx=Hn}}else y(t)?(t={default:t,_ctx:Hn},n=32):(t=String(t),64&o?(n=16,t=[ci(t)]):n=8);e.children=t,e.shapeFlag|=n}function hi(...e){const t={};for(let n=0;n<e.length;n++){const o=e[n];for(const e in o)if("class"===e)t.class!==o.class&&(t.class=ye([t.class,o.class]));else if("style"===e)t.style=ve([t.style,o.style]);else if(c(e)){const n=t[e],r=o[e];!r||n===r||g(n)&&n.includes(r)||(t[e]=n?[].concat(n,r):r)}else""!==e&&(t[e]=o[e])}return t}function gi(e,t,n,o=null){vn(e,t,7,[n,o])}const mi=yr();let vi=0;let yi=null;const bi=()=>yi||Hn;let wi,_i;{const e=D(),t=(t,n)=>{let o;return(o=e[t])||(o=e[t]=[]),o.push(n),e=>{o.length>1?o.forEach((t=>t(e))):o[0](e)}};wi=t("__VUE_INSTANCE_SETTERS__",(e=>yi=e)),_i=t("__VUE_SSR_SETTERS__",(e=>Ci=e))}const xi=e=>{const t=yi;return wi(e),e.scope.on(),()=>{e.scope.off(),wi(t)}},Si=()=>{yi&&yi.scope.off(),wi(null)};function Ti(e){return 4&e.vnode.shapeFlag}let Ci=!1;function ki(e,t,n){y(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:_(t)&&(e.setupState=gn(t)),Ei(e,n)}function Ei(e,t,n){const o=e.type;e.render||(e.render=o.render||a);{const t=xi(e);Je();try{ar(e)}finally{Ke(),t()}}}function $i(e){if(e.exposed)return e.exposeProxy||(e.exposeProxy=new Proxy(gn(nn(e.exposed)),{get:(t,n)=>n in t?t[n]:n in nr?nr[n](e):void 0,has:(e,t)=>t in e||t in nr}))}function Oi(e,t=!0){return y(e)?e.displayName||e.name:e.name||t&&e.__name}const Li=(e,t)=>{const n=function(e,t,n=!1){let o,r;const i=y(e);return i?(o=e,r=a):(o=e.get,r=e.set),new sn(o,r,i||!r,n)}(e,0,Ci);return n};function Ai(e,t,n){const o=arguments.length;return 2===o?_(t)&&!g(t)?ti(t)?ai(e,null,[t]):ai(e,t):ai(e,null,t):(o>3?n=Array.prototype.slice.call(arguments,2):3===o&&ti(n)&&(n=[n]),ai(e,t,n))}const Pi="3.4.21",ji="undefined"!=typeof document?document:null,Ri=ji&&ji.createElement("template"),Mi={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,o)=>{const r="svg"===t?ji.createElementNS("http://www.w3.org/2000/svg",e):"mathml"===t?ji.createElementNS("http://www.w3.org/1998/Math/MathML",e):n?ji.createElement(e,{is:n}):ji.createElement(e);return"select"===e&&o&&null!=o.multiple&&r.setAttribute("multiple",o.multiple),r},createText:e=>ji.createTextNode(e),createComment:e=>ji.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>ji.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,o,r,i){const s=n?n.previousSibling:t.lastChild;if(r&&(r===i||r.nextSibling))for(;t.insertBefore(r.cloneNode(!0),n),r!==i&&(r=r.nextSibling););else{Ri.innerHTML="svg"===o?`<svg>${e}</svg>`:"mathml"===o?`<math>${e}</math>`:e;const r=Ri.content;if("svg"===o||"mathml"===o){const e=r.firstChild;for(;e.firstChild;)r.appendChild(e.firstChild);r.removeChild(e)}t.insertBefore(r,n)}return[s?s.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},Ii="transition",Ni=Symbol("_vtc"),Bi=(e,{slots:t})=>Ai(mo,function(e){const t={};for(const f in e)f in Fi||(t[f]=e[f]);if(!1===e.css)return t;const{name:n="v",type:o,duration:r,enterFromClass:i=`${n}-enter-from`,enterActiveClass:s=`${n}-enter-active`,enterToClass:a=`${n}-enter-to`,appearFromClass:l=i,appearActiveClass:c=s,appearToClass:u=a,leaveFromClass:d=`${n}-leave-from`,leaveActiveClass:p=`${n}-leave-active`,leaveToClass:h=`${n}-leave-to`}=e,g=function(e){if(null==e)return null;if(_(e))return[Vi(e.enter),Vi(e.leave)];{const t=Vi(e);return[t,t]}}(r),m=g&&g[0],v=g&&g[1],{onBeforeEnter:y,onEnter:b,onEnterCancelled:w,onLeave:x,onLeaveCancelled:S,onBeforeAppear:T=y,onAppear:C=b,onAppearCancelled:k=w}=t,E=(e,t,n)=>{zi(e,t?u:a),zi(e,t?c:s),n&&n()},$=(e,t)=>{e._isLeaving=!1,zi(e,d),zi(e,h),zi(e,p),t&&t()},O=e=>(t,n)=>{const r=e?C:b,s=()=>E(t,e,n);Di(r,[t,s]),qi((()=>{zi(t,e?l:i),Wi(t,e?u:a),Hi(r)||Gi(t,o,m,s)}))};return f(t,{onBeforeEnter(e){Di(y,[e]),Wi(e,i),Wi(e,s)},onBeforeAppear(e){Di(T,[e]),Wi(e,l),Wi(e,c)},onEnter:O(!1),onAppear:O(!0),onLeave(e,t){e._isLeaving=!0;const n=()=>$(e,t);Wi(e,d),document.body.offsetHeight,Wi(e,p),qi((()=>{e._isLeaving&&(zi(e,d),Wi(e,h),Hi(x)||Gi(e,o,v,n))})),Di(x,[e,n])},onEnterCancelled(e){E(e,!1),Di(w,[e])},onAppearCancelled(e){E(e,!0),Di(k,[e])},onLeaveCancelled(e){$(e),Di(S,[e])}})}(e),t);Bi.displayName="Transition";const Fi={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String};Bi.props=f({},go,Fi);const Di=(e,t=[])=>{g(e)?e.forEach((e=>e(...t))):e&&e(...t)},Hi=e=>!!e&&(g(e)?e.some((e=>e.length>1)):e.length>1);function Vi(e){const t=(e=>{const t=b(e)?Number(e):NaN;return isNaN(t)?e:t})(e);return t}function Wi(e,t){t.split(/\s+/).forEach((t=>t&&e.classList.add(t))),(e[Ni]||(e[Ni]=new Set)).add(t)}function zi(e,t){t.split(/\s+/).forEach((t=>t&&e.classList.remove(t)));const n=e[Ni];n&&(n.delete(t),n.size||(e[Ni]=void 0))}function qi(e){requestAnimationFrame((()=>{requestAnimationFrame(e)}))}let Ui=0;function Gi(e,t,n,o){const r=e._endId=++Ui,i=()=>{r===e._endId&&o()};if(n)return setTimeout(i,n);const{type:s,timeout:a,propCount:l}=function(e,t){const n=window.getComputedStyle(e),o=e=>(n[e]||"").split(", "),r=o("transitionDelay"),i=o("transitionDuration"),s=Xi(r,i),a=o("animationDelay"),l=o("animationDuration"),c=Xi(a,l);let u=null,f=0,d=0;t===Ii?s>0&&(u=Ii,f=s,d=i.length):"animation"===t?c>0&&(u="animation",f=c,d=l.length):(f=Math.max(s,c),u=f>0?s>c?Ii:"animation":null,d=u?u===Ii?i.length:l.length:0);const p=u===Ii&&/\b(transform|all)(,|$)/.test(o("transitionProperty").toString());return{type:u,timeout:f,propCount:d,hasTransform:p}}(e,t);if(!s)return o();const c=s+"end";let u=0;const f=()=>{e.removeEventListener(c,d),i()},d=t=>{t.target===e&&++u>=l&&f()};setTimeout((()=>{u<l&&f()}),a+1),e.addEventListener(c,d)}function Xi(e,t){for(;e.length<t.length;)e=e.concat(e);return Math.max(...t.map(((t,n)=>Yi(t)+Yi(e[n]))))}function Yi(e){return"auto"===e?0:1e3*Number(e.slice(0,-1).replace(",","."))}const Zi=Symbol("_vod"),Ji=Symbol("_vsh"),Ki={beforeMount(e,{value:t},{transition:n}){e[Zi]="none"===e.style.display?"":e.style.display,n&&t?n.beforeEnter(e):Qi(e,t)},mounted(e,{value:t},{transition:n}){n&&t&&n.enter(e)},updated(e,{value:t,oldValue:n},{transition:o}){!t!=!n&&(o?t?(o.beforeEnter(e),Qi(e,!0),o.enter(e)):o.leave(e,(()=>{Qi(e,!1)})):Qi(e,t))},beforeUnmount(e,{value:t}){Qi(e,t)}};function Qi(e,t){e.style.display=t?e[Zi]:"none",e[Ji]=!t}const es=Symbol(""),ts=/(^|;)\s*display\s*:/;const ns=/\s*!important$/;function os(e,t,n){if(g(n))n.forEach((n=>os(e,t,n)));else if(null==n&&(n=""),n=ps(n),t.startsWith("--"))e.setProperty(t,n);else{const o=function(e,t){const n=is[t];if(n)return n;let o=L(t);if("filter"!==o&&o in e)return is[t]=o;o=j(o);for(let r=0;r<rs.length;r++){const n=rs[r]+o;if(n in e)return is[t]=n}return t}(e,t);ns.test(n)?e.setProperty(P(o),n.replace(ns,""),"important"):e[o]=n}}const rs=["Webkit","Moz","ms"],is={};const{unit:ss,unitRatio:as,unitPrecision:ls}={unit:"rem",unitRatio:10/320,unitPrecision:5},cs=(us=ss,fs=as,ds=ls,e=>e.replace(xe,((e,t)=>{if(!t)return e;if(1===fs)return`${t}${us}`;const n=function(e,t){const n=Math.pow(10,t+1),o=Math.floor(e*n);return 10*Math.round(o/10)/n}(parseFloat(t)*fs,ds);return 0===n?"0":`${n}${us}`})));var us,fs,ds;const ps=e=>b(e)?cs(e):e,hs="http://www.w3.org/1999/xlink";const gs=Symbol("_vei");function ms(e,t,n,o,r=null){const i=e[gs]||(e[gs]={}),s=i[t];if(o&&s)s.value=o;else{const[n,a]=function(e){let t;if(vs.test(e)){let n;for(t={};n=e.match(vs);)e=e.slice(0,e.length-n[0].length),t[n[0].toLowerCase()]=!0}return[":"===e[2]?e.slice(3):P(e.slice(2)),t]}(t);if(o){const s=i[t]=function(e,t){const n=e=>{if(e._vts){if(e._vts<=n.attached)return}else e._vts=Date.now();const o=t&&t.proxy,r=o&&o.$nne,{value:i}=n;if(r&&g(i)){const n=ws(e,i);for(let o=0;o<n.length;o++){const i=n[o];vn(i,t,5,i.__wwe?[e]:r(e))}}else vn(ws(e,n.value),t,5,r&&!i.__wwe?r(e,i,t):[e])};return n.value=e,n.attached=(()=>ys||(bs.then((()=>ys=0)),ys=Date.now()))(),n}(o,r);!function(e,t,n,o){e.addEventListener(t,n,o)}(e,n,s,a)}else s&&(!function(e,t,n,o){e.removeEventListener(t,n,o)}(e,n,s,a),i[t]=void 0)}}const vs=/(?:Once|Passive|Capture)$/;let ys=0;const bs=Promise.resolve();function ws(e,t){if(g(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map((e=>{const t=t=>!t._stopped&&e&&e(t);return t.__wwe=e.__wwe,t}))}return t}const _s=e=>111===e.charCodeAt(0)&&110===e.charCodeAt(1)&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123;const xs=["ctrl","shift","alt","meta"],Ss={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&0!==e.button,middle:e=>"button"in e&&1!==e.button,right:e=>"button"in e&&2!==e.button,exact:(e,t)=>xs.some((n=>e[`${n}Key`]&&!t.includes(n)))},Ts=(e,t)=>{const n=e._withMods||(e._withMods={}),o=t.join(".");return n[o]||(n[o]=(n,...o)=>{for(let e=0;e<t.length;e++){const o=Ss[t[e]];if(o&&o(n,t))return}return e(n,...o)})},Cs=f({patchProp:(e,t,n,o,r,i,s,a,l)=>{if(0===t.indexOf("change:"))return function(e,t,n,o=null){if(!n||!o)return;const r=t.replace("change:",""),{attrs:i}=o,s=i[r],a=(e.__wxsProps||(e.__wxsProps={}))[r];if(a===s)return;e.__wxsProps[r]=s;const l=o.proxy;On((()=>{n(s,a,l.$gcd(l,!0),l.$gcd(l,!1))}))}(e,t,o,s);const f="svg"===r;"class"===t?function(e,t,n){const{__wxsAddClass:o,__wxsRemoveClass:r}=e;r&&r.length&&(t=(t||"").split(/\s+/).filter((e=>-1===r.indexOf(e))).join(" "),r.length=0),o&&o.length&&(t=(t||"")+" "+o.join(" "));const i=e[Ni];i&&(t=(t?[t,...i]:[...i]).join(" ")),null==t?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}(e,o,f):"style"===t?function(e,t,n){const o=e.style,r=b(n);let i=!1;if(n&&!r){if(t)if(b(t))for(const e of t.split(";")){const t=e.slice(0,e.indexOf(":")).trim();null==n[t]&&os(o,t,"")}else for(const e in t)null==n[e]&&os(o,e,"");for(const e in n)"display"===e&&(i=!0),os(o,e,n[e])}else if(r){if(t!==n){const e=o[es];e&&(n+=";"+e),o.cssText=n,i=ts.test(n)}}else t&&e.removeAttribute("style");Zi in e&&(e[Zi]=i?o.display:"",e[Ji]&&(o.display="none"));const{__wxsStyle:s}=e;if(s)for(const a in s)os(o,a,s[a])}(e,n,o):c(t)?u(t)||ms(e,t,0,o,s):("."===t[0]?(t=t.slice(1),1):"^"===t[0]?(t=t.slice(1),0):function(e,t,n,o){if(o)return"innerHTML"===t||"textContent"===t||!!(t in e&&_s(t)&&y(n));if("spellcheck"===t||"draggable"===t||"translate"===t)return!1;if("form"===t)return!1;if("list"===t&&"INPUT"===e.tagName)return!1;if("type"===t&&"TEXTAREA"===e.tagName)return!1;if("width"===t||"height"===t){const t=e.tagName;if("IMG"===t||"VIDEO"===t||"CANVAS"===t||"SOURCE"===t)return!1}if(_s(t)&&b(n))return!1;return t in e}(e,t,o,f))?function(e,t,n,o,r,i,s){if("innerHTML"===t||"textContent"===t)return o&&s(o,r,i),void(e[t]=null==n?"":n);const a=e.tagName;if("value"===t&&"PROGRESS"!==a&&!a.includes("-")){const o=null==n?"":n;return("OPTION"===a?e.getAttribute("value")||"":e.value)===o&&"_value"in e||(e.value=o),null==n&&e.removeAttribute(t),void(e._value=n)}let l=!1;if(""===n||null==n){const o=typeof e[t];"boolean"===o?n=X(n):null==n&&"string"===o?(n="",l=!0):"number"===o&&(n=0,l=!0)}try{e[t]=n}catch(c){}l&&e.removeAttribute(t)}(e,t,o,i,s,a,l):("true-value"===t?e._trueValue=o:"false-value"===t&&(e._falseValue=o),function(e,t,n,o,r){if(o&&t.startsWith("xlink:"))null==n?e.removeAttributeNS(hs,t.slice(6,t.length)):e.setAttributeNS(hs,t,n);else{const o=G(t);null==n||o&&!X(n)?e.removeAttribute(t):e.setAttribute(t,o?"":n)}}(e,t,o,f))},forcePatchProp:(e,t)=>0===t.indexOf("change:")||("class"===t&&e.__wxsClassChanged?(e.__wxsClassChanged=!1,!0):!("style"!==t||!e.__wxsStyleChanged)&&(e.__wxsStyleChanged=!1,!0))},Mi);let ks;const Es=(...e)=>{const t=(ks||(ks=Br(Cs))).createApp(...e),{mount:n}=t;return t.mount=e=>{const o=function(e){if(b(e)){return document.querySelector(e)}return e}
/*!
  * vue-router v4.3.0
  * (c) 2024 Eduardo San Martin Morote
  * @license MIT
  */(e);if(!o)return;const r=t._component;y(r)||r.render||r.template||(r.template=o.innerHTML),o.innerHTML="";const i=n(o,!1,function(e){if(e instanceof SVGElement)return"svg";if("function"==typeof MathMLElement&&e instanceof MathMLElement)return"mathml"}(o));return o instanceof Element&&(o.removeAttribute("v-cloak"),o.setAttribute("data-v-app","")),i},t};const $s="undefined"!=typeof document;const Os=Object.assign;function Ls(e,t){const n={};for(const o in t){const r=t[o];n[o]=Ps(r)?r.map(e):e(r)}return n}const As=()=>{},Ps=Array.isArray,js=/#/g,Rs=/&/g,Ms=/\//g,Is=/=/g,Ns=/\?/g,Bs=/\+/g,Fs=/%5B/g,Ds=/%5D/g,Hs=/%5E/g,Vs=/%60/g,Ws=/%7B/g,zs=/%7C/g,qs=/%7D/g,Us=/%20/g;function Gs(e){return encodeURI(""+e).replace(zs,"|").replace(Fs,"[").replace(Ds,"]")}function Xs(e){return Gs(e).replace(Bs,"%2B").replace(Us,"+").replace(js,"%23").replace(Rs,"%26").replace(Vs,"`").replace(Ws,"{").replace(qs,"}").replace(Hs,"^")}function Ys(e){return null==e?"":function(e){return Gs(e).replace(js,"%23").replace(Ns,"%3F")}(e).replace(Ms,"%2F")}function Zs(e){try{return decodeURIComponent(""+e)}catch(t){}return""+e}const Js=/\/$/;function Ks(e,t,n="/"){let o,r={},i="",s="";const a=t.indexOf("#");let l=t.indexOf("?");return a<l&&a>=0&&(l=-1),l>-1&&(o=t.slice(0,l),i=t.slice(l+1,a>-1?a:t.length),r=e(i)),a>-1&&(o=o||t.slice(0,a),s=t.slice(a,t.length)),o=function(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),o=e.split("/"),r=o[o.length-1];".."!==r&&"."!==r||o.push("");let i,s,a=n.length-1;for(i=0;i<o.length;i++)if(s=o[i],"."!==s){if(".."!==s)break;a>1&&a--}return n.slice(0,a).join("/")+"/"+o.slice(i).join("/")}(null!=o?o:t,n),{fullPath:o+(i&&"?")+i+s,path:o,query:r,hash:Zs(s)}}function Qs(e,t){return t&&e.toLowerCase().startsWith(t.toLowerCase())?e.slice(t.length)||"/":e}function ea(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function ta(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!na(e[n],t[n]))return!1;return!0}function na(e,t){return Ps(e)?oa(e,t):Ps(t)?oa(t,e):e===t}function oa(e,t){return Ps(t)?e.length===t.length&&e.every(((e,n)=>e===t[n])):1===e.length&&e[0]===t}var ra,ia,sa,aa;function la(e){if(!e)if($s){const t=document.querySelector("base");e=(e=t&&t.getAttribute("href")||"/").replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return"/"!==e[0]&&"#"!==e[0]&&(e="/"+e),e.replace(Js,"")}(ia=ra||(ra={})).pop="pop",ia.push="push",(aa=sa||(sa={})).back="back",aa.forward="forward",aa.unknown="";const ca=/^[^#]+#/;function ua(e,t){return e.replace(ca,"#")+t}const fa=()=>({left:window.scrollX,top:window.scrollY});function da(e){let t;if("el"in e){const n=e.el,o="string"==typeof n&&n.startsWith("#"),r="string"==typeof n?o?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!r)return;t=function(e,t){const n=document.documentElement.getBoundingClientRect(),o=e.getBoundingClientRect();return{behavior:t.behavior,left:o.left-n.left-(t.left||0),top:o.top-n.top-(t.top||0)}}(r,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(null!=t.left?t.left:window.scrollX,null!=t.top?t.top:window.scrollY)}function pa(e,t){return(history.state?history.state.position-t:-1)+e}const ha=new Map;function ga(e,t){const{pathname:n,search:o,hash:r}=t,i=e.indexOf("#");if(i>-1){let t=r.includes(e.slice(i))?e.slice(i).length:1,n=r.slice(t);return"/"!==n[0]&&(n="/"+n),Qs(n,"")}return Qs(n,e)+o+r}function ma(e,t,n,o=!1,r=!1){return{back:e,current:t,forward:n,replaced:o,position:window.history.length,scroll:r?fa():null}}function va(e){const{history:t,location:n}=window,o={value:ga(e,n)},r={value:t.state};function i(o,i,s){const a=e.indexOf("#"),l=a>-1?(n.host&&document.querySelector("base")?e:e.slice(a))+o:location.protocol+"//"+location.host+e+o;try{t[s?"replaceState":"pushState"](i,"",l),r.value=i}catch(c){console.error(c),n[s?"replace":"assign"](l)}}return r.value||i(o.value,{back:null,current:o.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0),{location:o,state:r,push:function(e,n){const s=Os({},r.value,t.state,{forward:e,scroll:fa()});i(s.current,s,!0),i(e,Os({},ma(o.value,e,null),{position:s.position+1},n),!1),o.value=e},replace:function(e,n){i(e,Os({},t.state,ma(r.value.back,e,r.value.forward,!0),n,{position:r.value.position}),!0),o.value=e}}}function ya(e){const t=va(e=la(e)),n=function(e,t,n,o){let r=[],i=[],s=null;const a=({state:i})=>{const a=ga(e,location),l=n.value,c=t.value;let u=0;if(i){if(n.value=a,t.value=i,s&&s===l)return void(s=null);u=c?i.position-c.position:0}else o(a);r.forEach((e=>{e(n.value,l,{delta:u,type:ra.pop,direction:u?u>0?sa.forward:sa.back:sa.unknown})}))};function l(){const{history:e}=window;e.state&&e.replaceState(Os({},e.state,{scroll:fa()}),"")}return window.addEventListener("popstate",a),window.addEventListener("beforeunload",l,{passive:!0}),{pauseListeners:function(){s=n.value},listen:function(e){r.push(e);const t=()=>{const t=r.indexOf(e);t>-1&&r.splice(t,1)};return i.push(t),t},destroy:function(){for(const e of i)e();i=[],window.removeEventListener("popstate",a),window.removeEventListener("beforeunload",l)}}}(e,t.state,t.location,t.replace);const o=Os({location:"",base:e,go:function(e,t=!0){t||n.pauseListeners(),history.go(e)},createHref:ua.bind(null,e)},t,n);return Object.defineProperty(o,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(o,"state",{enumerable:!0,get:()=>t.state.value}),o}function ba(e){return"string"==typeof e||"symbol"==typeof e}const wa={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0},_a=Symbol("");var xa,Sa;function Ta(e,t){return Os(new Error,{type:e,[_a]:!0},t)}function Ca(e,t){return e instanceof Error&&_a in e&&(null==t||!!(e.type&t))}(Sa=xa||(xa={}))[Sa.aborted=4]="aborted",Sa[Sa.cancelled=8]="cancelled",Sa[Sa.duplicated=16]="duplicated";const ka={sensitive:!1,strict:!1,start:!0,end:!0},Ea=/[.+*?^${}()[\]/\\]/g;function $a(e,t){let n=0;for(;n<e.length&&n<t.length;){const o=t[n]-e[n];if(o)return o;n++}return e.length<t.length?1===e.length&&80===e[0]?-1:1:e.length>t.length?1===t.length&&80===t[0]?1:-1:0}function Oa(e,t){let n=0;const o=e.score,r=t.score;for(;n<o.length&&n<r.length;){const e=$a(o[n],r[n]);if(e)return e;n++}if(1===Math.abs(r.length-o.length)){if(La(o))return 1;if(La(r))return-1}return r.length-o.length}function La(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const Aa={type:0,value:""},Pa=/[a-zA-Z0-9_]/;function ja(e,t,n){const o=function(e,t){const n=Os({},ka,t),o=[];let r=n.start?"^":"";const i=[];for(const l of e){const e=l.length?[]:[90];n.strict&&!l.length&&(r+="/");for(let t=0;t<l.length;t++){const o=l[t];let s=40+(n.sensitive?.25:0);if(0===o.type)t||(r+="/"),r+=o.value.replace(Ea,"\\$&"),s+=40;else if(1===o.type){const{value:e,repeatable:n,optional:c,regexp:u}=o;i.push({name:e,repeatable:n,optional:c});const f=u||"[^/]+?";if("[^/]+?"!==f){s+=10;try{new RegExp(`(${f})`)}catch(a){throw new Error(`Invalid custom RegExp for param "${e}" (${f}): `+a.message)}}let d=n?`((?:${f})(?:/(?:${f}))*)`:`(${f})`;t||(d=c&&l.length<2?`(?:/${d})`:"/"+d),c&&(d+="?"),r+=d,s+=20,c&&(s+=-8),n&&(s+=-20),".*"===f&&(s+=-50)}e.push(s)}o.push(e)}if(n.strict&&n.end){const e=o.length-1;o[e][o[e].length-1]+=.7000000000000001}n.strict||(r+="/?"),n.end?r+="$":n.strict&&(r+="(?:/|$)");const s=new RegExp(r,n.sensitive?"":"i");return{re:s,score:o,keys:i,parse:function(e){const t=e.match(s),n={};if(!t)return null;for(let o=1;o<t.length;o++){const e=t[o]||"",r=i[o-1];n[r.name]=e&&r.repeatable?e.split("/"):e}return n},stringify:function(t){let n="",o=!1;for(const r of e){o&&n.endsWith("/")||(n+="/"),o=!1;for(const e of r)if(0===e.type)n+=e.value;else if(1===e.type){const{value:i,repeatable:s,optional:a}=e,l=i in t?t[i]:"";if(Ps(l)&&!s)throw new Error(`Provided param "${i}" is an array but it is not repeatable (* or + modifiers)`);const c=Ps(l)?l.join("/"):l;if(!c){if(!a)throw new Error(`Missing required param "${i}"`);r.length<2&&(n.endsWith("/")?n=n.slice(0,-1):o=!0)}n+=c}}return n||"/"}}}(function(e){if(!e)return[[]];if("/"===e)return[[Aa]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(e){throw new Error(`ERR (${n})/"${c}": ${e}`)}let n=0,o=n;const r=[];let i;function s(){i&&r.push(i),i=[]}let a,l=0,c="",u="";function f(){c&&(0===n?i.push({type:0,value:c}):1===n||2===n||3===n?(i.length>1&&("*"===a||"+"===a)&&t(`A repeatable param (${c}) must be alone in its segment. eg: '/:ids+.`),i.push({type:1,value:c,regexp:u,repeatable:"*"===a||"+"===a,optional:"*"===a||"?"===a})):t("Invalid state to consume buffer"),c="")}function d(){c+=a}for(;l<e.length;)if(a=e[l++],"\\"!==a||2===n)switch(n){case 0:"/"===a?(c&&f(),s()):":"===a?(f(),n=1):d();break;case 4:d(),n=o;break;case 1:"("===a?n=2:Pa.test(a)?d():(f(),n=0,"*"!==a&&"?"!==a&&"+"!==a&&l--);break;case 2:")"===a?"\\"==u[u.length-1]?u=u.slice(0,-1)+a:n=3:u+=a;break;case 3:f(),n=0,"*"!==a&&"?"!==a&&"+"!==a&&l--,u="";break;default:t("Unknown state")}else o=n,n=4;return 2===n&&t(`Unfinished custom RegExp for param "${c}"`),f(),s(),r}(e.path),n),r=Os(o,{record:e,parent:t,children:[],alias:[]});return t&&!r.record.aliasOf==!t.record.aliasOf&&t.children.push(r),r}function Ra(e,t){const n=[],o=new Map;function r(e,n,o){const a=!o,l=function(e){return{path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:void 0,beforeEnter:e.beforeEnter,props:Ia(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}}}(e);l.aliasOf=o&&o.record;const c=Fa(t,e),u=[l];if("alias"in e){const t="string"==typeof e.alias?[e.alias]:e.alias;for(const e of t)u.push(Os({},l,{components:o?o.record.components:l.components,path:e,aliasOf:o?o.record:l}))}let f,d;for(const t of u){const{path:u}=t;if(n&&"/"!==u[0]){const e=n.record.path,o="/"===e[e.length-1]?"":"/";t.path=n.record.path+(u&&o+u)}if(f=ja(t,n,c),o?o.alias.push(f):(d=d||f,d!==f&&d.alias.push(f),a&&e.name&&!Na(f)&&i(e.name)),l.children){const e=l.children;for(let t=0;t<e.length;t++)r(e[t],f,o&&o.children[t])}o=o||f,(f.record.components&&Object.keys(f.record.components).length||f.record.name||f.record.redirect)&&s(f)}return d?()=>{i(d)}:As}function i(e){if(ba(e)){const t=o.get(e);t&&(o.delete(e),n.splice(n.indexOf(t),1),t.children.forEach(i),t.alias.forEach(i))}else{const t=n.indexOf(e);t>-1&&(n.splice(t,1),e.record.name&&o.delete(e.record.name),e.children.forEach(i),e.alias.forEach(i))}}function s(e){let t=0;for(;t<n.length&&Oa(e,n[t])>=0&&(e.record.path!==n[t].record.path||!Da(e,n[t]));)t++;n.splice(t,0,e),e.record.name&&!Na(e)&&o.set(e.record.name,e)}return t=Fa({strict:!1,end:!0,sensitive:!1},t),e.forEach((e=>r(e))),{addRoute:r,resolve:function(e,t){let r,i,s,a={};if("name"in e&&e.name){if(r=o.get(e.name),!r)throw Ta(1,{location:e});s=r.record.name,a=Os(Ma(t.params,r.keys.filter((e=>!e.optional)).concat(r.parent?r.parent.keys.filter((e=>e.optional)):[]).map((e=>e.name))),e.params&&Ma(e.params,r.keys.map((e=>e.name)))),i=r.stringify(a)}else if(null!=e.path)i=e.path,r=n.find((e=>e.re.test(i))),r&&(a=r.parse(i),s=r.record.name);else{if(r=t.name?o.get(t.name):n.find((e=>e.re.test(t.path))),!r)throw Ta(1,{location:e,currentLocation:t});s=r.record.name,a=Os({},t.params,e.params),i=r.stringify(a)}const l=[];let c=r;for(;c;)l.unshift(c.record),c=c.parent;return{name:s,path:i,params:a,matched:l,meta:Ba(l)}},removeRoute:i,getRoutes:function(){return n},getRecordMatcher:function(e){return o.get(e)}}}function Ma(e,t){const n={};for(const o of t)o in e&&(n[o]=e[o]);return n}function Ia(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const o in e.components)t[o]="object"==typeof n?n[o]:n;return t}function Na(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function Ba(e){return e.reduce(((e,t)=>Os(e,t.meta)),{})}function Fa(e,t){const n={};for(const o in e)n[o]=o in t?t[o]:e[o];return n}function Da(e,t){return t.children.some((t=>t===e||Da(e,t)))}function Ha(e){const t={};if(""===e||"?"===e)return t;const n=("?"===e[0]?e.slice(1):e).split("&");for(let o=0;o<n.length;++o){const e=n[o].replace(Bs," "),r=e.indexOf("="),i=Zs(r<0?e:e.slice(0,r)),s=r<0?null:Zs(e.slice(r+1));if(i in t){let e=t[i];Ps(e)||(e=t[i]=[e]),e.push(s)}else t[i]=s}return t}function Va(e){let t="";for(let n in e){const o=e[n];if(n=Xs(n).replace(Is,"%3D"),null==o){void 0!==o&&(t+=(t.length?"&":"")+n);continue}(Ps(o)?o.map((e=>e&&Xs(e))):[o&&Xs(o)]).forEach((e=>{void 0!==e&&(t+=(t.length?"&":"")+n,null!=e&&(t+="="+e))}))}return t}function Wa(e){const t={};for(const n in e){const o=e[n];void 0!==o&&(t[n]=Ps(o)?o.map((e=>null==e?null:""+e)):null==o?o:""+o)}return t}const za=Symbol(""),qa=Symbol(""),Ua=Symbol(""),Ga=Symbol(""),Xa=Symbol("");function Ya(){let e=[];return{add:function(t){return e.push(t),()=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)}},list:()=>e.slice(),reset:function(){e=[]}}}function Za(e,t,n,o,r,i=(e=>e())){const s=o&&(o.enterCallbacks[r]=o.enterCallbacks[r]||[]);return()=>new Promise(((a,l)=>{const c=e=>{var i;!1===e?l(Ta(4,{from:n,to:t})):e instanceof Error?l(e):"string"==typeof(i=e)||i&&"object"==typeof i?l(Ta(2,{from:t,to:e})):(s&&o.enterCallbacks[r]===s&&"function"==typeof e&&s.push(e),a())},u=i((()=>e.call(o&&o.instances[r],t,n,c)));let f=Promise.resolve(u);e.length<3&&(f=f.then(c)),f.catch((e=>l(e)))}))}function Ja(e,t,n,o,r=(e=>e())){const i=[];for(const a of e)for(const e in a.components){let l=a.components[e];if("beforeRouteEnter"===t||a.instances[e])if("object"==typeof(s=l)||"displayName"in s||"props"in s||"__vccOpts"in s){const s=(l.__vccOpts||l)[t];s&&i.push(Za(s,n,o,a,e,r))}else{let s=l();i.push((()=>s.then((i=>{if(!i)return Promise.reject(new Error(`Couldn't resolve component "${e}" at "${a.path}"`));const s=(l=i).__esModule||"Module"===l[Symbol.toStringTag]?i.default:i;var l;a.components[e]=s;const c=(s.__vccOpts||s)[t];return c&&Za(c,n,o,a,e,r)()}))))}}var s;return i}function Ka(e){const t=Sr(Ua),n=Sr(Ga),o=Li((()=>t.resolve(pn(e.to)))),r=Li((()=>{const{matched:e}=o.value,{length:t}=e,r=e[t-1],i=n.matched;if(!r||!i.length)return-1;const s=i.findIndex(ea.bind(null,r));if(s>-1)return s;const a=el(e[t-2]);return t>1&&el(r)===a&&i[i.length-1].path!==a?i.findIndex(ea.bind(null,e[t-2])):s})),i=Li((()=>r.value>-1&&function(e,t){for(const n in t){const o=t[n],r=e[n];if("string"==typeof o){if(o!==r)return!1}else if(!Ps(r)||r.length!==o.length||o.some(((e,t)=>e!==r[t])))return!1}return!0}(n.params,o.value.params))),s=Li((()=>r.value>-1&&r.value===n.matched.length-1&&ta(n.params,o.value.params)));return{route:o,href:Li((()=>o.value.href)),isActive:i,isExactActive:s,navigate:function(n={}){return function(e){if(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)return;if(e.defaultPrevented)return;if(void 0!==e.button&&0!==e.button)return;if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}e.preventDefault&&e.preventDefault();return!0}(n)?t[pn(e.replace)?"replace":"push"](pn(e.to)).catch(As):Promise.resolve()}}}const Qa=So({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"}},useLink:Ka,setup(e,{slots:t}){const n=Gt(Ka(e)),{options:o}=Sr(Ua),r=Li((()=>({[tl(e.activeClass,o.linkActiveClass,"router-link-active")]:n.isActive,[tl(e.exactActiveClass,o.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive})));return()=>{const o=t.default&&t.default(n);return e.custom?o:Ai("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:r.value},o)}}});function el(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const tl=(e,t,n)=>null!=e?e:null!=t?t:n;function nl(e,t){if(!e)return null;const n=e(t);return 1===n.length?n[0]:n}const ol=So({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const o=Sr(Xa),r=Li((()=>e.route||o.value)),i=Sr(qa,0),s=Li((()=>{let e=pn(i);const{matched:t}=r.value;let n;for(;(n=t[e])&&!n.components;)e++;return e})),a=Li((()=>r.value.matched[s.value]));xr(qa,Li((()=>s.value+1))),xr(za,a),xr(Xa,r);const l=un();return ro((()=>[l.value,a.value,e.name]),(([e,t,n],[o,r,i])=>{t&&(t.instances[n]=e,r&&r!==t&&e&&e===o&&(t.leaveGuards.size||(t.leaveGuards=r.leaveGuards),t.updateGuards.size||(t.updateGuards=r.updateGuards))),!e||!t||r&&ea(t,r)&&o||(t.enterCallbacks[n]||[]).forEach((t=>t(e)))}),{flush:"post"}),()=>{const o=r.value,i=e.name,s=a.value,c=s&&s.components[i];if(!c)return nl(n.default,{Component:c,route:o});const u=s.props[i],f=u?!0===u?o.params:"function"==typeof u?u(o):u:null,d=Ai(c,Os({},f,t,{onVnodeUnmounted:e=>{e.component.isUnmounted&&(s.instances[i]=null)},ref:l}));return nl(n.default,{Component:d,route:o})||d}}});function rl(e){const t=Ra(e.routes,e),n=e.parseQuery||Ha,o=e.stringifyQuery||Va,r=e.history,i=Ya(),s=Ya(),a=Ya(),l=fn(wa,!0);let c=wa;$s&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const u=Ls.bind(null,(e=>""+e)),f=Ls.bind(null,Ys),d=Ls.bind(null,Zs);function p(e,i){if(i=Os({},i||l.value),"string"==typeof e){const o=Ks(n,e,i.path),s=t.resolve({path:o.path},i),a=r.createHref(o.fullPath);return Os(o,s,{params:d(s.params),hash:Zs(o.hash),redirectedFrom:void 0,href:a})}let s;if(null!=e.path)s=Os({},e,{path:Ks(n,e.path,i.path).path});else{const t=Os({},e.params);for(const e in t)null==t[e]&&delete t[e];s=Os({},e,{params:f(t)}),i.params=f(i.params)}const a=t.resolve(s,i),c=e.hash||"";a.params=u(d(a.params));const p=function(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}(o,Os({},e,{hash:(h=c,Gs(h).replace(Ws,"{").replace(qs,"}").replace(Hs,"^")),path:a.path}));var h;const g=r.createHref(p);return Os({fullPath:p,hash:c,query:o===Va?Wa(e.query):e.query||{}},a,{redirectedFrom:void 0,href:g})}function h(e){return"string"==typeof e?Ks(n,e,l.value.path):Os({},e)}function g(e,t){if(c!==e)return Ta(8,{from:t,to:e})}function m(e){return y(e)}function v(e){const t=e.matched[e.matched.length-1];if(t&&t.redirect){const{redirect:n}=t;let o="function"==typeof n?n(e):n;return"string"==typeof o&&(o=o.includes("?")||o.includes("#")?o=h(o):{path:o},o.params={}),Os({query:e.query,hash:e.hash,params:null!=o.path?{}:e.params},o)}}function y(e,t){const n=c=p(e),r=l.value,i=e.state,s=e.force,a=!0===e.replace,u=v(n);if(u)return y(Os(h(u),{state:"object"==typeof u?Os({},i,u.state):i,force:s,replace:a}),t||n);const f=n;let d;return f.redirectedFrom=t,!s&&function(e,t,n){const o=t.matched.length-1,r=n.matched.length-1;return o>-1&&o===r&&ea(t.matched[o],n.matched[r])&&ta(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}(o,r,n)&&(d=Ta(16,{to:f,from:r}),A(r,r,!0,!1)),(d?Promise.resolve(d):_(f,r)).catch((e=>Ca(e)?Ca(e,2)?e:L(e):O(e,f,r))).then((e=>{if(e){if(Ca(e,2))return y(Os({replace:a},h(e.to),{state:"object"==typeof e.to?Os({},i,e.to.state):i,force:s}),t||f)}else e=S(f,r,!0,a,i);return x(f,r,e),e}))}function b(e,t){const n=g(e,t);return n?Promise.reject(n):Promise.resolve()}function w(e){const t=R.values().next().value;return t&&"function"==typeof t.runWithContext?t.runWithContext(e):e()}function _(e,t){let n;const[o,r,a]=function(e,t){const n=[],o=[],r=[],i=Math.max(t.matched.length,e.matched.length);for(let s=0;s<i;s++){const i=t.matched[s];i&&(e.matched.find((e=>ea(e,i)))?o.push(i):n.push(i));const a=e.matched[s];a&&(t.matched.find((e=>ea(e,a)))||r.push(a))}return[n,o,r]}(e,t);n=Ja(o.reverse(),"beforeRouteLeave",e,t);for(const i of o)i.leaveGuards.forEach((o=>{n.push(Za(o,e,t))}));const l=b.bind(null,e,t);return n.push(l),I(n).then((()=>{n=[];for(const o of i.list())n.push(Za(o,e,t));return n.push(l),I(n)})).then((()=>{n=Ja(r,"beforeRouteUpdate",e,t);for(const o of r)o.updateGuards.forEach((o=>{n.push(Za(o,e,t))}));return n.push(l),I(n)})).then((()=>{n=[];for(const o of a)if(o.beforeEnter)if(Ps(o.beforeEnter))for(const r of o.beforeEnter)n.push(Za(r,e,t));else n.push(Za(o.beforeEnter,e,t));return n.push(l),I(n)})).then((()=>(e.matched.forEach((e=>e.enterCallbacks={})),n=Ja(a,"beforeRouteEnter",e,t,w),n.push(l),I(n)))).then((()=>{n=[];for(const o of s.list())n.push(Za(o,e,t));return n.push(l),I(n)})).catch((e=>Ca(e,8)?e:Promise.reject(e)))}function x(e,t,n){a.list().forEach((o=>w((()=>o(e,t,n)))))}function S(e,t,n,o,i){const s=g(e,t);if(s)return s;const a=t===wa,c=$s?history.state:{};n&&(o||a?r.replace(e.fullPath,Os({scroll:a&&c&&c.scroll},i)):r.push(e.fullPath,i)),l.value=e,A(e,t,n,a),L()}let T;function C(){T||(T=r.listen(((e,t,n)=>{if(!M.listening)return;const o=p(e),i=v(o);if(i)return void y(Os(i,{replace:!0}),o).catch(As);c=o;const s=l.value;var a,u;$s&&(a=pa(s.fullPath,n.delta),u=fa(),ha.set(a,u)),_(o,s).catch((e=>Ca(e,12)?e:Ca(e,2)?(y(e.to,o).then((e=>{Ca(e,20)&&!n.delta&&n.type===ra.pop&&r.go(-1,!1)})).catch(As),Promise.reject()):(n.delta&&r.go(-n.delta,!1),O(e,o,s)))).then((e=>{(e=e||S(o,s,!1))&&(n.delta&&!Ca(e,8)?r.go(-n.delta,!1):n.type===ra.pop&&Ca(e,20)&&r.go(-1,!1)),x(o,s,e)})).catch(As)})))}let k,E=Ya(),$=Ya();function O(e,t,n){L(e);const o=$.list();return o.length?o.forEach((o=>o(e,t,n))):console.error(e),Promise.reject(e)}function L(e){return k||(k=!e,C(),E.list().forEach((([t,n])=>e?n(e):t())),E.reset()),e}function A(t,n,o,r){const{scrollBehavior:i}=e;if(!$s||!i)return Promise.resolve();const s=!o&&function(e){const t=ha.get(e);return ha.delete(e),t}(pa(t.fullPath,0))||(r||!o)&&history.state&&history.state.scroll||null;return On().then((()=>i(t,n,s))).then((e=>e&&da(e))).catch((e=>O(e,t,n)))}const P=e=>r.go(e);let j;const R=new Set,M={currentRoute:l,listening:!0,addRoute:function(e,n){let o,r;return ba(e)?(o=t.getRecordMatcher(e),r=n):r=e,t.addRoute(r,o)},removeRoute:function(e){const n=t.getRecordMatcher(e);n&&t.removeRoute(n)},hasRoute:function(e){return!!t.getRecordMatcher(e)},getRoutes:function(){return t.getRoutes().map((e=>e.record))},resolve:p,options:e,push:m,replace:function(e){return m(Os(h(e),{replace:!0}))},go:P,back:()=>P(-1),forward:()=>P(1),beforeEach:i.add,beforeResolve:s.add,afterEach:a.add,onError:$.add,isReady:function(){return k&&l.value!==wa?Promise.resolve():new Promise(((e,t)=>{E.add([e,t])}))},install(e){e.component("RouterLink",Qa),e.component("RouterView",ol),e.config.globalProperties.$router=this,Object.defineProperty(e.config.globalProperties,"$route",{enumerable:!0,get:()=>pn(l)}),$s&&!j&&l.value===wa&&(j=!0,m(r.location).catch((e=>{})));const t={};for(const o in wa)Object.defineProperty(t,o,{get:()=>l.value[o],enumerable:!0});e.provide(Ua,this),e.provide(Ga,Xt(t)),e.provide(Xa,l);const n=e.unmount;R.add(e),e.unmount=function(){R.delete(e),R.size<1&&(c=wa,T&&T(),T=null,l.value=wa,j=!1,k=!1),n()}}};function I(e){return e.reduce(((e,t)=>e.then((()=>w(t)))),Promise.resolve())}return M}function il(){return Sr(Ga)}const sl=["{","}"];const al=/^(?:\d)+/,ll=/^(?:\w)+/;const cl=Object.prototype.hasOwnProperty,ul=(e,t)=>cl.call(e,t),fl=new class{constructor(){this._caches=Object.create(null)}interpolate(e,t,n=sl){if(!t)return[e];let o=this._caches[e];return o||(o=function(e,[t,n]){const o=[];let r=0,i="";for(;r<e.length;){let s=e[r++];if(s===t){i&&o.push({type:"text",value:i}),i="";let t="";for(s=e[r++];void 0!==s&&s!==n;)t+=s,s=e[r++];const a=s===n,l=al.test(t)?"list":a&&ll.test(t)?"named":"unknown";o.push({value:t,type:l})}else i+=s}return i&&o.push({type:"text",value:i}),o}(e,n),this._caches[e]=o),function(e,t){const n=[];let o=0;const r=Array.isArray(t)?"list":(i=t,null!==i&&"object"==typeof i?"named":"unknown");var i;if("unknown"===r)return n;for(;o<e.length;){const i=e[o];switch(i.type){case"text":n.push(i.value);break;case"list":n.push(t[parseInt(i.value,10)]);break;case"named":"named"===r&&n.push(t[i.value])}o++}return n}(o,t)}};function dl(e,t){if(!e)return;if(e=e.trim().replace(/_/g,"-"),t&&t[e])return e;if("chinese"===(e=e.toLowerCase()))return"zh-Hans";if(0===e.indexOf("zh"))return e.indexOf("-hans")>-1?"zh-Hans":e.indexOf("-hant")>-1?"zh-Hant":(n=e,["-tw","-hk","-mo","-cht"].find((e=>-1!==n.indexOf(e)))?"zh-Hant":"zh-Hans");var n;let o=["en","fr","es"];t&&Object.keys(t).length>0&&(o=Object.keys(t));const r=function(e,t){return t.find((t=>0===e.indexOf(t)))}(e,o);return r||void 0}class pl{constructor({locale:e,fallbackLocale:t,messages:n,watcher:o,formater:r}){this.locale="en",this.fallbackLocale="en",this.message={},this.messages={},this.watchers=[],t&&(this.fallbackLocale=t),this.formater=r||fl,this.messages=n||{},this.setLocale(e||"en"),o&&this.watchLocale(o)}setLocale(e){const t=this.locale;this.locale=dl(e,this.messages)||this.fallbackLocale,this.messages[this.locale]||(this.messages[this.locale]={}),this.message=this.messages[this.locale],t!==this.locale&&this.watchers.forEach((e=>{e(this.locale,t)}))}getLocale(){return this.locale}watchLocale(e){const t=this.watchers.push(e)-1;return()=>{this.watchers.splice(t,1)}}add(e,t,n=!0){const o=this.messages[e];o?n?Object.assign(o,t):Object.keys(t).forEach((e=>{ul(o,e)||(o[e]=t[e])})):this.messages[e]=t}f(e,t,n){return this.formater.interpolate(e,t,n).join("")}t(e,t,n){let o=this.message;return"string"==typeof t?(t=dl(t,this.messages))&&(o=this.messages[t]):n=t,ul(o,e)?this.formater.interpolate(o[e],n).join(""):(console.warn(`Cannot translate the value of keypath ${e}. Use the value of keypath as default.`),e)}}function hl(e,t={},n,o){if("string"!=typeof e){const n=[t,e];e=n[0],t=n[1]}"string"!=typeof e&&(e="undefined"!=typeof uni&&_f?_f():"undefined"!=typeof global&&global.getLocale?global.getLocale():"en"),"string"!=typeof n&&(n="undefined"!=typeof __uniConfig&&__uniConfig.fallbackLocale||"en");const r=new pl({locale:e,fallbackLocale:n,messages:t,watcher:o});let i=(e,t)=>{{let e=!1;i=function(t,n){const o=th().$vm;return o&&(o.$locale,e||(e=!0,function(e,t){e.$watchLocale?e.$watchLocale((e=>{t.setLocale(e)})):e.$watch((()=>e.$locale),(e=>{t.setLocale(e)}))}(o,r))),r.t(t,n)}}return i(e,t)};return{i18n:r,f:(e,t,n)=>r.f(e,t,n),t:(e,t)=>i(e,t),add:(e,t,n=!0)=>r.add(e,t,n),watch:e=>r.watchLocale(e),getLocale:()=>r.getLocale(),setLocale:e=>r.setLocale(e)}}const gl=ue((()=>"undefined"!=typeof __uniConfig&&__uniConfig.locales&&!!Object.keys(__uniConfig.locales).length));let ml;function vl(){if(!ml){let e;if(e=navigator.cookieEnabled&&window.localStorage&&localStorage.UNI_LOCALE||__uniConfig.locale||navigator.language,ml=hl(e),gl()){const t=Object.keys(__uniConfig.locales||{});t.length&&t.forEach((e=>ml.add(e,__uniConfig.locales[e]))),ml.setLocale(e)}}return ml}function yl(e,t,n){return t.reduce(((t,o,r)=>(t[e+o]=n[r],t)),{})}const bl=ue((()=>{const e="uni.async.",t=["error"];vl().add("en",yl(e,t,["The connection timed out, click the screen to try again."]),!1),vl().add("es",yl(e,t,["Se agotó el tiempo de conexión, haga clic en la pantalla para volver a intentarlo."]),!1),vl().add("fr",yl(e,t,["La connexion a expiré, cliquez sur l'écran pour réessayer."]),!1),vl().add("zh-Hans",yl(e,t,["连接服务器超时，点击屏幕重试"]),!1),vl().add("zh-Hant",yl(e,t,["連接服務器超時，點擊屏幕重試"]),!1)})),wl=ue((()=>{const e="uni.showToast.",t=["unpaired"];vl().add("en",yl(e,t,["Please note showToast must be paired with hideToast"]),!1),vl().add("es",yl(e,t,["Tenga en cuenta que showToast debe estar emparejado con hideToast"]),!1),vl().add("fr",yl(e,t,["Veuillez noter que showToast doit être associé à hideToast"]),!1),vl().add("zh-Hans",yl(e,t,["请注意 showToast 与 hideToast 必须配对使用"]),!1),vl().add("zh-Hant",yl(e,t,["請注意 showToast 與 hideToast 必須配對使用"]),!1)})),_l=ue((()=>{const e="uni.showLoading.",t=["unpaired"];vl().add("en",yl(e,t,["Please note showLoading must be paired with hideLoading"]),!1),vl().add("es",yl(e,t,["Tenga en cuenta que showLoading debe estar emparejado con hideLoading"]),!1),vl().add("fr",yl(e,t,["Veuillez noter que showLoading doit être associé à hideLoading"]),!1),vl().add("zh-Hans",yl(e,t,["请注意 showLoading 与 hideLoading 必须配对使用"]),!1),vl().add("zh-Hant",yl(e,t,["請注意 showLoading 與 hideLoading 必須配對使用"]),!1)})),xl=ue((()=>{const e="uni.chooseFile.",t=["notUserActivation"];vl().add("en",yl(e,t,["File chooser dialog can only be shown with a user activation"]),!1),vl().add("es",yl(e,t,["El cuadro de diálogo del selector de archivos solo se puede mostrar con la activación del usuario"]),!1),vl().add("fr",yl(e,t,["La boîte de dialogue du sélecteur de fichier ne peut être affichée qu'avec une activation par l'utilisateur"]),!1),vl().add("zh-Hans",yl(e,t,["文件选择器对话框只能在由用户激活时显示"]),!1),vl().add("zh-Hant",yl(e,t,["文件選擇器對話框只能在由用戶激活時顯示"]),!1)}));function Sl(e){const t=new Ie;return{on:(e,n)=>t.on(e,n),once:(e,n)=>t.once(e,n),off:(e,n)=>t.off(e,n),emit:(e,...n)=>t.emit(e,...n),subscribe(n,o,r=!1){t[r?"once":"on"](`${e}.${n}`,o)},unsubscribe(n,o){t.off(`${e}.${n}`,o)},subscribeHandler(n,o,r){t.emit(`${e}.${n}`,o,r)}}}let Tl=1;const Cl=Object.create(null);function kl(e,t){return e+"."+t}function El({id:e,name:t,args:n},o){t=kl(o,t);const r=t=>{e&&gg.publishHandler("invokeViewApi."+e,t)},i=Cl[t];i?i(n,r):r({})}const $l=f(Sl("service"),{invokeServiceMethod:(e,t,n)=>{const{subscribe:o,publishHandler:r}=gg,i=n?Tl++:0;n&&o("invokeServiceApi."+i,n,!0),r("invokeServiceApi",{id:i,name:e,args:t})}}),Ol=Se(!0);let Ll;function Al(){Ll&&(clearTimeout(Ll),Ll=null)}let Pl=0,jl=0;function Rl(e){if(Al(),1!==e.touches.length)return;const{pageX:t,pageY:n}=e.touches[0];Pl=t,jl=n,Ll=setTimeout((function(){const t=new CustomEvent("longpress",{bubbles:!0,cancelable:!0,target:e.target,currentTarget:e.currentTarget});t.touches=e.touches,t.changedTouches=e.changedTouches,e.target.dispatchEvent(t)}),350)}function Ml(e){if(!Ll)return;if(1!==e.touches.length)return Al();const{pageX:t,pageY:n}=e.touches[0];return Math.abs(t-Pl)>10||Math.abs(n-jl)>10?Al():void 0}function Il(e,t){const n=Number(e);return isNaN(n)?t:n}function Nl(){const e=__uniConfig.globalStyle||{},t=Il(e.rpxCalcMaxDeviceWidth,960),n=Il(e.rpxCalcBaseDeviceWidth,375);function o(){let e=function(){const e=/^Apple/.test(navigator.vendor)&&"number"==typeof window.orientation,t=e&&90===Math.abs(window.orientation);var n=e?Math[t?"max":"min"](screen.width,screen.height):screen.width;return Math.min(window.innerWidth,document.documentElement.clientWidth,n)||n}();e=e<=t?e:n,document.documentElement.style.fontSize=e/23.4375+"px"}o(),document.addEventListener("DOMContentLoaded",o),window.addEventListener("load",o),window.addEventListener("resize",o)}function Bl(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Fl,Dl,Hl=["top","left","right","bottom"],Vl={};function Wl(){return Dl="CSS"in window&&"function"==typeof CSS.supports?CSS.supports("top: env(safe-area-inset-top)")?"env":CSS.supports("top: constant(safe-area-inset-top)")?"constant":"":""}function zl(){if(Dl="string"==typeof Dl?Dl:Wl()){var e=[],t=!1;try{var n=Object.defineProperty({},"passive",{get:function(){t={passive:!0}}});window.addEventListener("test",null,n)}catch(a){}var o=document.createElement("div");r(o,{position:"absolute",left:"0",top:"0",width:"0",height:"0",zIndex:"-1",overflow:"hidden",visibility:"hidden"}),Hl.forEach((function(e){s(o,e)})),document.body.appendChild(o),i(),Fl=!0}else Hl.forEach((function(e){Vl[e]=0}));function r(e,t){var n=e.style;Object.keys(t).forEach((function(e){var o=t[e];n[e]=o}))}function i(t){t?e.push(t):e.forEach((function(e){e()}))}function s(e,n){var o=document.createElement("div"),s=document.createElement("div"),a=document.createElement("div"),l=document.createElement("div"),c={position:"absolute",width:"100px",height:"200px",boxSizing:"border-box",overflow:"hidden",paddingBottom:Dl+"(safe-area-inset-"+n+")"};r(o,c),r(s,c),r(a,{transition:"0s",animation:"none",width:"400px",height:"400px"}),r(l,{transition:"0s",animation:"none",width:"250%",height:"250%"}),o.appendChild(a),s.appendChild(l),e.appendChild(o),e.appendChild(s),i((function(){o.scrollTop=s.scrollTop=1e4;var e=o.scrollTop,r=s.scrollTop;function i(){this.scrollTop!==(this===o?e:r)&&(o.scrollTop=s.scrollTop=1e4,e=o.scrollTop,r=s.scrollTop,function(e){Ul.length||setTimeout((function(){var e={};Ul.forEach((function(t){e[t]=Vl[t]})),Ul.length=0,Gl.forEach((function(t){t(e)}))}),0);Ul.push(e)}(n))}o.addEventListener("scroll",i,t),s.addEventListener("scroll",i,t)}));var u=getComputedStyle(o);Object.defineProperty(Vl,n,{configurable:!0,get:function(){return parseFloat(u.paddingBottom)}})}}function ql(e){return Fl||zl(),Vl[e]}var Ul=[];var Gl=[];const Xl=Bl({get support(){return 0!=("string"==typeof Dl?Dl:Wl()).length},get top(){return ql("top")},get left(){return ql("left")},get right(){return ql("right")},get bottom(){return ql("bottom")},onChange:function(e){Wl()&&(Fl||zl(),"function"==typeof e&&Gl.push(e))},offChange:function(e){var t=Gl.indexOf(e);t>=0&&Gl.splice(t,1)}}),Yl=Ts((()=>{}),["prevent"]);function Zl(e,t){return parseInt((e.getPropertyValue(t).match(/\d+/)||["0"])[0])}function Jl(){const e=Zl(document.documentElement.style,"--window-top");return e?e+Xl.top:0}function Kl(){const e=document.documentElement.style,t=Jl(),n=Zl(e,"--window-bottom"),o=Zl(e,"--window-left"),r=Zl(e,"--window-right"),i=Zl(e,"--top-window-height");return{top:t,bottom:n?n+Xl.bottom:0,left:o?o+Xl.left:0,right:r?r+Xl.right:0,topWindowHeight:i||0}}function Ql(e){const t=document.documentElement.style;Object.keys(e).forEach((n=>{t.setProperty(n,e[n])}))}function ec(e){return Symbol(e)}function tc(e){return e.$page}function nc(e){return 0===e.tagName.indexOf("UNI-")}const oc="M1.952 18.080q-0.32-0.352-0.416-0.88t0.128-0.976l0.16-0.352q0.224-0.416 0.64-0.528t0.8 0.176l6.496 4.704q0.384 0.288 0.912 0.272t0.88-0.336l17.312-14.272q0.352-0.288 0.848-0.256t0.848 0.352l-0.416-0.416q0.32 0.352 0.32 0.816t-0.32 0.816l-18.656 18.912q-0.32 0.352-0.8 0.352t-0.8-0.32l-7.936-8.064z",rc="M15.808 0.16q-4.224 0-7.872 2.176-3.552 2.112-5.632 5.728-2.144 3.744-2.144 8.128 0 4.192 2.144 7.872 2.112 3.52 5.632 5.632 3.68 2.144 7.872 2.144 4.384 0 8.128-2.144 3.616-2.080 5.728-5.632 2.176-3.648 2.176-7.872 0-4.384-2.176-8.128-2.112-3.616-5.728-5.728-3.744-2.176-8.128-2.176zM15.136 8.672h1.728q0.128 0 0.224 0.096t0.096 0.256l-0.384 10.24q0 0.064-0.048 0.112t-0.112 0.048h-1.248q-0.096 0-0.144-0.048t-0.048-0.112l-0.384-10.24q0-0.16 0.096-0.256t0.224-0.096zM16 23.328q-0.48 0-0.832-0.352t-0.352-0.848 0.352-0.848 0.832-0.352 0.832 0.352 0.352 0.848-0.352 0.848-0.832 0.352z";function ic(e,t="#000",n=27){return ai("svg",{width:n,height:n,viewBox:"0 0 32 32"},[ai("path",{d:e,fill:t},null,8,["d","fill"])],8,["width","height"])}function sc(){{const{$pageInstance:e}=bi();return e&&hc(e.proxy)}}function ac(){const e=vd(),t=e.length;if(t)return e[t-1]}function lc(){var e;const t=null==(e=ac())?void 0:e.$page;if(t)return t.meta}function cc(){const e=lc();return e?e.id:-1}function uc(){const e=ac();if(e)return e.$vm}const fc=["navigationBar","pullToRefresh"];function dc(e,t){const n=JSON.parse(JSON.stringify(__uniConfig.globalStyle||{})),o=f({id:t},n,e);fc.forEach((t=>{o[t]=f({},n[t],e[t])}));const{navigationBar:r}=o;return r.titleText&&r.titleImage&&(r.titleText=""),o}function pc(e,t,n,o,r,i){const{id:s,route:a}=o,l=Fe(o.navigationBar,__uniConfig.themeConfig,i).titleColor;return{id:s,path:ce(a),route:a,fullPath:t,options:n,meta:o,openType:e,eventChannel:r,statusBarStyle:"#ffffff"===l?"light":"dark"}}function hc(e){var t,n;return(null==(t=e.$page)?void 0:t.id)||(null==(n=e.$basePage)?void 0:n.id)}function gc(e,t,n){if(b(e))n=t,t=e,e=uc();else if("number"==typeof e){const t=vd().find((t=>tc(t).id===e));e=t?t.$vm:uc()}if(!e)return;const o=e.$[t];return o&&((e,t)=>{let n;for(let o=0;o<e.length;o++)n=e[o](t);return n})(o,n)}function mc(e){e.preventDefault()}let vc,yc=0;function bc({onPageScroll:e,onReachBottom:t,onReachBottomDistance:n}){let o=!1,r=!1,i=!0;const s=()=>{function s(){if((()=>{const{scrollHeight:e}=document.documentElement,t=window.innerHeight,o=window.scrollY,i=o>0&&e>t&&o+t+n>=e,s=Math.abs(e-yc)>n;return!i||r&&!s?(!i&&r&&(r=!1),!1):(yc=e,r=!0,!0)})())return t&&t(),i=!1,setTimeout((function(){i=!0}),350),!0}e&&e(window.pageYOffset),t&&i&&(s()||(vc=setTimeout(s,300))),o=!1};return function(){clearTimeout(vc),o||requestAnimationFrame(s),o=!0}}function wc(e,t){if(0===t.indexOf("/"))return t;if(0===t.indexOf("./"))return wc(e,t.slice(2));const n=t.split("/"),o=n.length;let r=0;for(;r<o&&".."===n[r];r++);n.splice(0,r),t=n.join("/");const i=e.length>0?e.split("/"):[];return i.splice(i.length-r-1,r+1),ce(i.concat(n).join("/"))}function _c(e,t=!1){return t?__uniRoutes.find((t=>t.path===e||t.alias===e)):__uniRoutes.find((t=>t.path===e))}function xc(){Nl(),we(nc),window.addEventListener("touchstart",Rl,Ol),window.addEventListener("touchmove",Ml,Ol),window.addEventListener("touchend",Al,Ol),window.addEventListener("touchcancel",Al,Ol)}class Sc{constructor(e){this.$bindClass=!1,this.$bindStyle=!1,this.$vm=e,this.$el=function(e,t=!1){const{vnode:n}=e;if(me(n.el))return t?n.el?[n.el]:[]:n.el;const{subTree:o}=e;if(16&o.shapeFlag){const e=o.children.filter((e=>e.el&&me(e.el)));if(e.length>0)return t?e.map((e=>e.el)):e[0].el}return t?n.el?[n.el]:[]:n.el}(e.$),this.$el.getAttribute&&(this.$bindClass=!!this.$el.getAttribute("class"),this.$bindStyle=!!this.$el.getAttribute("style"))}selectComponent(e){if(!this.$el||!e)return;const t=Ec(this.$el.querySelector(e));return t?Tc(t,!1):void 0}selectAllComponents(e){if(!this.$el||!e)return[];const t=[],n=this.$el.querySelectorAll(e);for(let o=0;o<n.length;o++){const e=Ec(n[o]);e&&t.push(Tc(e,!1))}return t}forceUpdate(e){"class"===e?this.$bindClass?(this.$el.__wxsClassChanged=!0,this.$vm.$forceUpdate()):this.updateWxsClass():"style"===e&&(this.$bindStyle?(this.$el.__wxsStyleChanged=!0,this.$vm.$forceUpdate()):this.updateWxsStyle())}updateWxsClass(){const{__wxsAddClass:e}=this.$el;e.length&&(this.$el.className=e.join(" "))}updateWxsStyle(){const{__wxsStyle:e}=this.$el;e&&this.$el.setAttribute("style",function(e){let t="";if(!e||b(e))return t;for(const n in e){const o=e[n],r=n.startsWith("--")?n:P(n);(b(o)||"number"==typeof o)&&(t+=`${r}:${o};`)}return t}(e))}setStyle(e){return this.$el&&e?(b(e)&&(e=q(e)),C(e)&&(this.$el.__wxsStyle=e,this.forceUpdate("style")),this):this}addClass(e){if(!this.$el||!e)return this;const t=this.$el.__wxsAddClass||(this.$el.__wxsAddClass=[]);return-1===t.indexOf(e)&&(t.push(e),this.forceUpdate("class")),this}removeClass(e){if(!this.$el||!e)return this;const{__wxsAddClass:t}=this.$el;if(t){const n=t.indexOf(e);n>-1&&t.splice(n,1)}const n=this.$el.__wxsRemoveClass||(this.$el.__wxsRemoveClass=[]);return-1===n.indexOf(e)&&(n.push(e),this.forceUpdate("class")),this}hasClass(e){return this.$el&&this.$el.classList.contains(e)}getDataset(){return this.$el&&this.$el.dataset}callMethod(e,t={}){const n=this.$vm[e];y(n)?n(JSON.parse(JSON.stringify(t))):this.$vm.ownerId&&gg.publishHandler("onWxsInvokeCallMethod",{nodeId:this.$el.__id,ownerId:this.$vm.ownerId,method:e,args:t})}requestAnimationFrame(e){return window.requestAnimationFrame(e)}getState(){return this.$el&&(this.$el.__wxsState||(this.$el.__wxsState={}))}triggerEvent(e,t={}){return this.$vm.$emit(e,t),this}getComputedStyle(e){if(this.$el){const t=window.getComputedStyle(this.$el);return e&&e.length?e.reduce(((e,n)=>(e[n]=t[n],e)),{}):t}return{}}setTimeout(e,t){return window.setTimeout(e,t)}clearTimeout(e){return window.clearTimeout(e)}getBoundingClientRect(){return this.$el.getBoundingClientRect()}}function Tc(e,t=!0){if(t&&e&&(e=ge(e.$)),e&&e.$el)return e.$el.__wxsComponentDescriptor||(e.$el.__wxsComponentDescriptor=new Sc(e)),e.$el.__wxsComponentDescriptor}function Cc(e,t){return Tc(e,t)}function kc(e,t,n,o=!0){if(t){e.__instance||(e.__instance=!0,Object.defineProperty(e,"instance",{get:()=>Cc(n.proxy,!1)}));const r=function(e,t,n=!0){if(!t)return!1;if(n&&e.length<2)return!1;const o=ge(t);if(!o)return!1;const r=o.$.type;return!(!r.$wxs&&!r.$renderjs)&&o}(t,n,o);if(r)return[e,Cc(r,!1)]}}function Ec(e){if(e)return e.__vueParentComponent&&e.__vueParentComponent.proxy}function $c(e,t=!1){const{type:n,timeStamp:o,target:r,currentTarget:i}=e;let s,a;s=Te(t?r:function(e){for(;!nc(e);)e=e.parentElement;return e}(r)),a=Te(i);const l={type:n,timeStamp:o,target:s,detail:{},currentTarget:a};return e instanceof CustomEvent&&C(e.detail)&&(l.detail=e.detail),e._stopped&&(l._stopped=!0),e.type.startsWith("touch")&&(l.touches=e.touches,l.changedTouches=e.changedTouches),function(e,t){f(e,{preventDefault:()=>t.preventDefault(),stopPropagation:()=>t.stopPropagation()})}(l,e),l}function Oc(e,t){return{force:1,identifier:0,clientX:e.clientX,clientY:e.clientY-t,pageX:e.pageX,pageY:e.pageY-t}}function Lc(e,t){const n=[];for(let o=0;o<e.length;o++){const{identifier:r,pageX:i,pageY:s,clientX:a,clientY:l,force:c}=e[o];n.push({identifier:r,pageX:i,pageY:s-t,clientX:a,clientY:l-t,force:c||0})}return n}const Ac=Object.defineProperty({__proto__:null,$nne:function(e,t,n){const{currentTarget:o}=e;if(!(e instanceof Event&&o instanceof HTMLElement))return[e];const r=!nc(o);if(r)return kc(e,t,n,!1)||[e];const i=$c(e,r);if("click"===e.type)!function(e,t){const{x:n,y:o}=t,r=Jl();e.detail={x:n,y:o-r},e.touches=e.changedTouches=[Oc(t,r)]}(i,e);else if((e=>0===e.type.indexOf("mouse")||["contextmenu"].includes(e.type))(e))!function(e,t){const n=Jl();e.pageX=t.pageX,e.pageY=t.pageY-n,e.clientX=t.clientX,e.clientY=t.clientY-n,e.touches=e.changedTouches=[Oc(t,n)]}(i,e);else if((e=>"undefined"!=typeof TouchEvent&&e instanceof TouchEvent||0===e.type.indexOf("touch")||["longpress"].indexOf(e.type)>=0)(e)){const t=Jl();i.touches=Lc(e.touches,t),i.changedTouches=Lc(e.changedTouches,t)}else if((e=>!e.type.indexOf("key")&&e instanceof KeyboardEvent)(e)){["key","code"].forEach((t=>{Object.defineProperty(i,t,{get:()=>e[t]})}))}return kc(i,t,n)||[i]},createNativeEvent:$c},Symbol.toStringTag,{value:"Module"});function Pc(e){!function(e){const t=e.globalProperties;f(t,Ac),t.$gcd=Cc}(e._context.config)}let jc=1;function Rc(e){return(e||cc())+".invokeViewApi"}const Mc=f(Sl("view"),{invokeOnCallback:(e,t)=>mg.emit("api."+e,t),invokeViewMethod:(e,t,n,o)=>{const{subscribe:r,publishHandler:i}=mg,s=o?jc++:0;o&&r("invokeViewApi."+s,o,!0),i(Rc(n),{id:s,name:e,args:t},n)},invokeViewMethodKeepAlive:(e,t,n,o)=>{const{subscribe:r,unsubscribe:i,publishHandler:s}=mg,a=jc++,l="invokeViewApi."+a;return r(l,n),s(Rc(o),{id:a,name:e,args:t},o),()=>{i(l)}}});function Ic(e){gc(ac(),"onResize",e),mg.invokeOnCallback("onWindowResize",e)}function Nc(e){const t=ac();gc(th(),"onShow",e),gc(t,"onShow")}function Bc(){gc(th(),"onHide"),gc(ac(),"onHide")}const Fc=["onPageScroll","onReachBottom"];function Dc(){Fc.forEach((e=>mg.subscribe(e,function(e){return(t,n)=>{gc(parseInt(n),e,t)}}(e))))}function Hc(){!function(){const{on:e}=mg;e("onResize",Ic),e("onAppEnterForeground",Nc),e("onAppEnterBackground",Bc)}(),Dc()}function Vc(){if(this.$route){const e=this.$route.meta;return e.eventChannel||(e.eventChannel=new Le(this.$page.id)),e.eventChannel}}function Wc(e){e._context.config.globalProperties.getOpenerEventChannel=Vc}function zc(){return{path:"",query:{},scene:1001,referrerInfo:{appId:"",extraData:{}}}}function qc(e){return/^-?\d+[ur]px$/i.test(e)?e.replace(/(^-?\d+)[ur]px$/i,((e,t)=>`${Zu(parseFloat(t))}px`)):/^-?[\d\.]+$/.test(e)?`${e}px`:e||""}function Uc(e){const t=e.animation;if(!t||!t.actions||!t.actions.length)return;let n=0;const o=t.actions,r=t.actions.length;function i(){const t=o[n],s=t.option.transition,a=function(e){const t=["matrix","matrix3d","scale","scale3d","rotate3d","skew","translate","translate3d"],n=["scaleX","scaleY","scaleZ","rotate","rotateX","rotateY","rotateZ","skewX","skewY","translateX","translateY","translateZ"],o=["opacity","background-color"],r=["width","height","left","right","top","bottom"],i=e.animates,s=e.option,a=s.transition,l={},c=[];return i.forEach((e=>{let i=e.type,s=[...e.args];if(t.concat(n).includes(i))i.startsWith("rotate")||i.startsWith("skew")?s=s.map((e=>parseFloat(e)+"deg")):i.startsWith("translate")&&(s=s.map(qc)),n.indexOf(i)>=0&&(s.length=1),c.push(`${i}(${s.join(",")})`);else if(o.concat(r).includes(s[0])){i=s[0];const e=s[1];l[i]=r.includes(i)?qc(e):e}})),l.transform=l.webkitTransform=c.join(" "),l.transition=l.webkitTransition=Object.keys(l).map((e=>`${function(e){return e.replace(/[A-Z]/g,(e=>`-${e.toLowerCase()}`)).replace("webkit","-webkit")}(e)} ${a.duration}ms ${a.timingFunction} ${a.delay}ms`)).join(","),l.transformOrigin=l.webkitTransformOrigin=s.transformOrigin,l}(t);Object.keys(a).forEach((t=>{e.$el.style[t]=a[t]})),n+=1,n<r&&setTimeout(i,s.duration+s.delay)}setTimeout((()=>{i()}),0)}const Gc={props:["animation"],watch:{animation:{deep:!0,handler(){Uc(this)}}},mounted(){Uc(this)}},Xc=e=>{e.__reserved=!0;const{props:t,mixins:n}=e;return t&&t.animation||(n||(e.mixins=[])).push(Gc),Yc(e)},Yc=e=>(e.__reserved=!0,e.compatConfig={MODE:3},So(e));function Zc(e){return e.__wwe=!0,e}function Jc(e,t){return(n,o,r)=>{e.value&&t(n,function(e,t,n,o){let r;return r=Te(n),{type:t.__evName||o.type||e,timeStamp:t.timeStamp||0,target:r,currentTarget:r,detail:o}}(n,o,e.value,r||{}))}}const Kc={hoverClass:{type:String,default:"none"},hoverStopPropagation:{type:Boolean,default:!1},hoverStartTime:{type:[Number,String],default:50},hoverStayTime:{type:[Number,String],default:400}};function Qc(e){const t=un(!1);let n,o,r=!1;function i(){requestAnimationFrame((()=>{clearTimeout(o),o=setTimeout((()=>{t.value=!1}),parseInt(e.hoverStayTime))}))}function s(o){o._hoverPropagationStopped||e.hoverClass&&"none"!==e.hoverClass&&!e.disabled&&(e.hoverStopPropagation&&(o._hoverPropagationStopped=!0),r=!0,n=setTimeout((()=>{t.value=!0,r||i()}),parseInt(e.hoverStartTime)))}function a(){r=!1,t.value&&i()}function l(){a(),window.removeEventListener("mouseup",l)}return{hovering:t,binding:{onTouchstartPassive:Zc((function(e){e.touches.length>1||s(e)})),onMousedown:Zc((function(e){r||(s(e),window.addEventListener("mouseup",l))})),onTouchend:Zc((function(){a()})),onMouseup:Zc((function(){r&&l()})),onTouchcancel:Zc((function(){r=!1,t.value=!1,clearTimeout(n)}))}}}function eu(e,t){return b(t)&&(t=[t]),t.reduce(((t,n)=>(e[n]&&(t[n]=!0),t)),Object.create(null))}const tu=ec("uf"),nu=ec("ul");function ou(e,t,n){const o=sc();n&&!e||C(t)&&Object.keys(t).forEach((r=>{n?0!==r.indexOf("@")&&0!==r.indexOf("uni-")&&gg.on(`uni-${r}-${o}-${e}`,t[r]):0===r.indexOf("uni-")?gg.on(r,t[r]):e&&gg.on(`uni-${r}-${o}-${e}`,t[r])}))}function ru(e,t,n){const o=sc();n&&!e||C(t)&&Object.keys(t).forEach((r=>{n?0!==r.indexOf("@")&&0!==r.indexOf("uni-")&&gg.off(`uni-${r}-${o}-${e}`,t[r]):0===r.indexOf("uni-")?gg.off(r,t[r]):e&&gg.off(`uni-${r}-${o}-${e}`,t[r])}))}const iu=Xc({name:"Button",props:{id:{type:String,default:""},hoverClass:{type:String,default:"button-hover"},hoverStartTime:{type:[Number,String],default:20},hoverStayTime:{type:[Number,String],default:70},hoverStopPropagation:{type:Boolean,default:!1},disabled:{type:[Boolean,String],default:!1},formType:{type:String,default:""},openType:{type:String,default:""},loading:{type:[Boolean,String],default:!1},plain:{type:[Boolean,String],default:!1}},setup(e,{slots:t}){const n=un(null),o=Sr(tu,!1),{hovering:r,binding:i}=Qc(e),s=Zc(((t,r)=>{if(e.disabled)return t.stopImmediatePropagation();r&&n.value.click();const i=e.formType;if(i){if(!o)return;"submit"===i?o.submit(t):"reset"===i&&o.reset(t)}else;})),a=Sr(nu,!1);return a&&(a.addHandler(s),Uo((()=>{a.removeHandler(s)}))),function(e,t){ou(e.id,t),ro((()=>e.id),((e,n)=>{ru(n,t,!0),ou(e,t,!0)})),Go((()=>{ru(e.id,t)}))}(e,{"label-click":s}),()=>{const o=e.hoverClass,a=eu(e,"disabled"),l=eu(e,"loading"),c=eu(e,"plain"),u=o&&"none"!==o;return ai("uni-button",hi({ref:n,onClick:s,id:e.id,class:u&&r.value?o:""},u&&i,a,l,c),[t.default&&t.default()],16,["onClick","id"])}}}),su=ec("upm");function au(){return Sr(su)}function lu(e){const t=function(e){return Gt(function(e){if(history.state){const t=history.state.__type__;"redirectTo"!==t&&"reLaunch"!==t||0!==vd().length||(e.isEntry=!0,e.isQuit=!0)}return e}(JSON.parse(JSON.stringify(dc(il().meta,e)))))}(e);return xr(su,t),t}function cu(){return il()}function uu(){return history.state&&history.state.__id__||1}const fu=["original","compressed"],du=["album","camera"],pu=["GET","OPTIONS","HEAD","POST","PUT","DELETE","TRACE","CONNECT","PATCH"];function hu(e,t){return e&&-1!==t.indexOf(e)?e:t[0]}function gu(e,t){return!g(e)||0===e.length||e.find((e=>-1===t.indexOf(e)))?t:e}function mu(e){return function(){try{return e.apply(e,arguments)}catch(t){console.error(t)}}}let vu=1;const yu={};function bu(e,t,n){if("number"==typeof e){const o=yu[e];if(o)return o.keepAlive||delete yu[e],o.callback(t,n)}return t}const wu="success",_u="fail",xu="complete";function Su(e,t={},{beforeAll:n,beforeSuccess:o}={}){C(t)||(t={});const{success:r,fail:i,complete:s}=function(e){const t={};for(const n in e){const o=e[n];y(o)&&(t[n]=mu(o),delete e[n])}return t}(t),a=y(r),l=y(i),c=y(s),u=vu++;return function(e,t,n,o=!1){yu[e]={name:t,keepAlive:o,callback:n}}(u,e,(u=>{(u=u||{}).errMsg=function(e,t){return e&&-1!==e.indexOf(":fail")?t+e.substring(e.indexOf(":fail")):t+":ok"}(u.errMsg,e),y(n)&&n(u),u.errMsg===e+":ok"?(y(o)&&o(u,t),a&&r(u)):l&&i(u),c&&s(u)})),u}const Tu="success",Cu="fail",ku="complete",Eu={},$u={};function Ou(e,t){return function(n){return e(n,t)||n}}function Lu(e,t,n){let o=!1;for(let r=0;r<e.length;r++){const i=e[r];if(o)o=Promise.resolve(Ou(i,n));else{const e=i(t,n);if(x(e)&&(o=Promise.resolve(e)),!1===e)return{then(){},catch(){}}}}return o||{then:e=>e(t),catch(){}}}function Au(e,t={}){return[Tu,Cu,ku].forEach((n=>{const o=e[n];if(!g(o))return;const r=t[n];t[n]=function(e){Lu(o,e,t).then((e=>y(r)&&r(e)||e))}})),t}function Pu(e,t){const n=[];g(Eu.returnValue)&&n.push(...Eu.returnValue);const o=$u[e];return o&&g(o.returnValue)&&n.push(...o.returnValue),n.forEach((e=>{t=e(t)||t})),t}function ju(e){const t=Object.create(null);Object.keys(Eu).forEach((e=>{"returnValue"!==e&&(t[e]=Eu[e].slice())}));const n=$u[e];return n&&Object.keys(n).forEach((e=>{"returnValue"!==e&&(t[e]=(t[e]||[]).concat(n[e]))})),t}function Ru(e,t,n,o){const r=ju(e);if(r&&Object.keys(r).length){if(g(r.invoke)){return Lu(r.invoke,n).then((n=>t(Au(ju(e),n),...o)))}return t(Au(r,n),...o)}return t(n,...o)}function Mu(e,t){return(n={},...o)=>function(e){return!(!C(e)||![wu,_u,xu].find((t=>y(e[t]))))}(n)?Pu(e,Ru(e,t,n,o)):Pu(e,new Promise(((r,i)=>{Ru(e,t,f(n,{success:r,fail:i}),o)})))}function Iu(e,t,n,o={}){const r=t+":fail";let i="";return i=n?0===n.indexOf(r)?n:r+" "+n:r,delete o.errCode,bu(e,f({errMsg:i},o))}function Nu(e,t,n,o){if(o&&o.beforeInvoke){const e=o.beforeInvoke(t);if(b(e))return e}const r=function(e,t){const n=e[0];if(!t||!t.formatArgs||!C(t.formatArgs)&&C(n))return;const o=t.formatArgs,r=Object.keys(o);for(let i=0;i<r.length;i++){const t=r[i],s=o[t];if(y(s)){const o=s(e[0][t],n);if(b(o))return o}else h(n,t)||(n[t]=s)}}(t,o);if(r)return r}function Bu(e,t,n,o){return n=>{const r=Su(e,n,o),i=Nu(0,[n],0,o);return i?Iu(r,e,i):t(n,{resolve:t=>function(e,t,n){return bu(e,f(n||{},{errMsg:t+":ok"}))}(r,e,t),reject:(t,n)=>Iu(r,e,function(e){return!e||b(e)?e:e.stack?("undefined"!=typeof globalThis&&globalThis.harmonyChannel||console.error(e.message+"\n"+e.stack),e.message):e}(t),n)})}}function Fu(e,t,n,o){return Mu(e,Bu(e,t,0,o))}function Du(e,t,n,o){return function(e,t,n,o){return(...e)=>{const n=Nu(0,e,0,o);if(n)throw new Error(n);return t.apply(null,e)}}(0,t,0,o)}function Hu(e,t,n,o){return Mu(e,function(e,t,n,o){return Bu(e,t,0,o)}(e,t,0,o))}let Vu=!1,Wu=0,zu=0,qu=960,Uu=375,Gu=750;function Xu(){const{windowWidth:e,pixelRatio:t,platform:n}=function(){const e=Dd(),t=Wd(Vd(e,Hd(e)));return{platform:Md?"ios":"other",pixelRatio:window.devicePixelRatio,windowWidth:t}}();Wu=e,zu=t,Vu="ios"===n}function Yu(e,t){const n=Number(e);return isNaN(n)?t:n}const Zu=Du(0,((e,t)=>{if(0===Wu&&(Xu(),function(){const e=__uniConfig.globalStyle||{};qu=Yu(e.rpxCalcMaxDeviceWidth,960),Uu=Yu(e.rpxCalcBaseDeviceWidth,375),Gu=Yu(e.rpxCalcBaseDeviceWidth,750)}()),0===(e=Number(e)))return 0;let n=t||Wu;n=e===Gu||n<=qu?n:Uu;let o=e/750*n;return o<0&&(o=-o),o=Math.floor(o+1e-4),0===o&&(o=1!==zu&&Vu?.5:1),e<0?-o:o}));const Ju=new class{constructor(){this.$emitter=new Ie}on(e,t){return this.$emitter.on(e,t)}once(e,t){return this.$emitter.once(e,t)}off(e,t){e?this.$emitter.off(e,t):this.$emitter.e={}}emit(e,...t){this.$emitter.emit(e,...t)}},Ku=Du(0,((e,t)=>(Ju.on(e,t),()=>Ju.off(e,t)))),Qu=Du(0,((e,t)=>{g(e)||(e=e?[e]:[]),e.forEach((e=>{Ju.off(e,t)}))})),ef=Du(0,((e,...t)=>{Ju.emit(e,...t)})),tf=[.5,.8,1,1.25,1.5,2];const nf=(e,t,n,o)=>{!function(e,t,n,o,r){mg.invokeViewMethod("map."+e,{type:n,data:o},t,r)}(e,t,n,o,(e=>{o&&((e,t)=>{const n=t.errMsg||"";new RegExp("\\:\\s*fail").test(n)?e.fail&&e.fail(t):e.success&&e.success(t),e.complete&&e.complete(t)})(o,e)}))};const of={aliceblue:"#f0f8ff",antiquewhite:"#faebd7",aqua:"#00ffff",aquamarine:"#7fffd4",azure:"#f0ffff",beige:"#f5f5dc",bisque:"#ffe4c4",black:"#000000",blanchedalmond:"#ffebcd",blue:"#0000ff",blueviolet:"#8a2be2",brown:"#a52a2a",burlywood:"#deb887",cadetblue:"#5f9ea0",chartreuse:"#7fff00",chocolate:"#d2691e",coral:"#ff7f50",cornflowerblue:"#6495ed",cornsilk:"#fff8dc",crimson:"#dc143c",cyan:"#00ffff",darkblue:"#00008b",darkcyan:"#008b8b",darkgoldenrod:"#b8860b",darkgray:"#a9a9a9",darkgrey:"#a9a9a9",darkgreen:"#006400",darkkhaki:"#bdb76b",darkmagenta:"#8b008b",darkolivegreen:"#556b2f",darkorange:"#ff8c00",darkorchid:"#9932cc",darkred:"#8b0000",darksalmon:"#e9967a",darkseagreen:"#8fbc8f",darkslateblue:"#483d8b",darkslategray:"#2f4f4f",darkslategrey:"#2f4f4f",darkturquoise:"#00ced1",darkviolet:"#9400d3",deeppink:"#ff1493",deepskyblue:"#00bfff",dimgray:"#696969",dimgrey:"#696969",dodgerblue:"#1e90ff",firebrick:"#b22222",floralwhite:"#fffaf0",forestgreen:"#228b22",fuchsia:"#ff00ff",gainsboro:"#dcdcdc",ghostwhite:"#f8f8ff",gold:"#ffd700",goldenrod:"#daa520",gray:"#808080",grey:"#808080",green:"#008000",greenyellow:"#adff2f",honeydew:"#f0fff0",hotpink:"#ff69b4",indianred:"#cd5c5c",indigo:"#4b0082",ivory:"#fffff0",khaki:"#f0e68c",lavender:"#e6e6fa",lavenderblush:"#fff0f5",lawngreen:"#7cfc00",lemonchiffon:"#fffacd",lightblue:"#add8e6",lightcoral:"#f08080",lightcyan:"#e0ffff",lightgoldenrodyellow:"#fafad2",lightgray:"#d3d3d3",lightgrey:"#d3d3d3",lightgreen:"#90ee90",lightpink:"#ffb6c1",lightsalmon:"#ffa07a",lightseagreen:"#20b2aa",lightskyblue:"#87cefa",lightslategray:"#778899",lightslategrey:"#778899",lightsteelblue:"#b0c4de",lightyellow:"#ffffe0",lime:"#00ff00",limegreen:"#32cd32",linen:"#faf0e6",magenta:"#ff00ff",maroon:"#800000",mediumaquamarine:"#66cdaa",mediumblue:"#0000cd",mediumorchid:"#ba55d3",mediumpurple:"#9370db",mediumseagreen:"#3cb371",mediumslateblue:"#7b68ee",mediumspringgreen:"#00fa9a",mediumturquoise:"#48d1cc",mediumvioletred:"#c71585",midnightblue:"#191970",mintcream:"#f5fffa",mistyrose:"#ffe4e1",moccasin:"#ffe4b5",navajowhite:"#ffdead",navy:"#000080",oldlace:"#fdf5e6",olive:"#808000",olivedrab:"#6b8e23",orange:"#ffa500",orangered:"#ff4500",orchid:"#da70d6",palegoldenrod:"#eee8aa",palegreen:"#98fb98",paleturquoise:"#afeeee",palevioletred:"#db7093",papayawhip:"#ffefd5",peachpuff:"#ffdab9",peru:"#cd853f",pink:"#ffc0cb",plum:"#dda0dd",powderblue:"#b0e0e6",purple:"#800080",rebeccapurple:"#663399",red:"#ff0000",rosybrown:"#bc8f8f",royalblue:"#4169e1",saddlebrown:"#8b4513",salmon:"#fa8072",sandybrown:"#f4a460",seagreen:"#2e8b57",seashell:"#fff5ee",sienna:"#a0522d",silver:"#c0c0c0",skyblue:"#87ceeb",slateblue:"#6a5acd",slategray:"#708090",slategrey:"#708090",snow:"#fffafa",springgreen:"#00ff7f",steelblue:"#4682b4",tan:"#d2b48c",teal:"#008080",thistle:"#d8bfd8",tomato:"#ff6347",turquoise:"#40e0d0",violet:"#ee82ee",wheat:"#f5deb3",white:"#ffffff",whitesmoke:"#f5f5f5",yellow:"#ffff00",yellowgreen:"#9acd32",transparent:"#00000000"};function rf(e){let t=null;if(null!=(t=/^#([0-9|A-F|a-f]{6})$/.exec(e=e||"#000000"))){return[parseInt(t[1].slice(0,2),16),parseInt(t[1].slice(2,4),16),parseInt(t[1].slice(4),16),255]}if(null!=(t=/^#([0-9|A-F|a-f]{3})$/.exec(e))){let e=t[1].slice(0,1),n=t[1].slice(1,2),o=t[1].slice(2,3);return e=parseInt(e+e,16),n=parseInt(n+n,16),o=parseInt(o+o,16),[e,n,o,255]}if(null!=(t=/^rgb\((.+)\)$/.exec(e)))return t[1].split(",").map((function(e){return Math.min(255,parseInt(e.trim()))})).concat(255);if(null!=(t=/^rgba\((.+)\)$/.exec(e)))return t[1].split(",").map((function(e,t){return 3===t?Math.floor(255*parseFloat(e.trim())):Math.min(255,parseInt(e.trim()))}));var n=e.toLowerCase();if(h(of,n)){t=/^#([0-9|A-F|a-f]{6,8})$/.exec(of[n]);const e=parseInt(t[1].slice(0,2),16),o=parseInt(t[1].slice(2,4),16),r=parseInt(t[1].slice(4,6),16);let i=parseInt(t[1].slice(6,8),16);return i=i>=0?i:255,[e,o,r,i]}return console.error("unsupported color:"+e),[0,0,0,255]}class sf{constructor(e,t){this.type=e,this.data=t,this.colorStop=[]}addColorStop(e,t){this.colorStop.push([e,rf(t)])}}class af{constructor(e,t){this.type="pattern",this.data=e,this.colorStop=t}}class lf{constructor(e){this.width=e}}let cf=0,uf={};const ff={canvas:class{constructor(e,t){this.id=e,this.pageId=t,this.actions=[],this.path=[],this.subpath=[],this.drawingState=[],this.state={lineDash:[0,0],shadowOffsetX:0,shadowOffsetY:0,shadowBlur:0,shadowColor:[0,0,0,0],font:"10px sans-serif",fontSize:10,fontWeight:"normal",fontStyle:"normal",fontFamily:"sans-serif"}}setFillStyle(e){console.log("initCanvasContextProperty implemented.")}setStrokeStyle(e){console.log("initCanvasContextProperty implemented.")}setShadow(e,t,n,o){console.log("initCanvasContextProperty implemented.")}addColorStop(e,t){console.log("initCanvasContextProperty implemented.")}setLineWidth(e){console.log("initCanvasContextProperty implemented.")}setLineCap(e){console.log("initCanvasContextProperty implemented.")}setLineJoin(e){console.log("initCanvasContextProperty implemented.")}setLineDash(e,t){console.log("initCanvasContextProperty implemented.")}setMiterLimit(e){console.log("initCanvasContextProperty implemented.")}fillRect(e,t,n,o){console.log("initCanvasContextProperty implemented.")}strokeRect(e,t,n,o){console.log("initCanvasContextProperty implemented.")}clearRect(e,t,n,o){console.log("initCanvasContextProperty implemented.")}fill(){console.log("initCanvasContextProperty implemented.")}stroke(){console.log("initCanvasContextProperty implemented.")}scale(e,t){console.log("initCanvasContextProperty implemented.")}rotate(e){console.log("initCanvasContextProperty implemented.")}translate(e,t){console.log("initCanvasContextProperty implemented.")}setFontSize(e){console.log("initCanvasContextProperty implemented.")}fillText(e,t,n,o){console.log("initCanvasContextProperty implemented.")}setTextAlign(e){console.log("initCanvasContextProperty implemented.")}setTextBaseline(e){console.log("initCanvasContextProperty implemented.")}drawImage(e,t,n,o,r,i,s,a,l){console.log("initCanvasContextProperty implemented.")}setGlobalAlpha(e){console.log("initCanvasContextProperty implemented.")}strokeText(e,t,n,o){console.log("initCanvasContextProperty implemented.")}setTransform(e,t,n,o,r,i){console.log("initCanvasContextProperty implemented.")}draw(e=!1,t){var n=[...this.actions];this.actions=[],this.path=[],function(e,t,n,o,r){mg.invokeViewMethod(`canvas.${e}`,{type:n,data:o},t,(e=>{r&&r(e)}))}(this.id,this.pageId,"actionsChanged",{actions:n,reserve:e},t)}createLinearGradient(e,t,n,o){return new sf("linear",[e,t,n,o])}createCircularGradient(e,t,n){return new sf("radial",[e,t,n])}createPattern(e,t){if(void 0===t)console.error("Failed to execute 'createPattern' on 'CanvasContext': 2 arguments required, but only 1 present.");else{if(!(["repeat","repeat-x","repeat-y","no-repeat"].indexOf(t)<0))return new af(e,t);console.error("Failed to execute 'createPattern' on 'CanvasContext': The provided type ('"+t+"') is not one of 'repeat', 'no-repeat', 'repeat-x', or 'repeat-y'.")}}measureText(e,t){let n=0;return n=function(e,t){const n=document.createElement("canvas").getContext("2d");return n.font=t,n.measureText(e).width||0}(e,this.state.font),new lf(n)}save(){this.actions.push({method:"save",data:[]}),this.drawingState.push(this.state)}restore(){this.actions.push({method:"restore",data:[]}),this.state=this.drawingState.pop()||{lineDash:[0,0],shadowOffsetX:0,shadowOffsetY:0,shadowBlur:0,shadowColor:[0,0,0,0],font:"10px sans-serif",fontSize:10,fontWeight:"normal",fontStyle:"normal",fontFamily:"sans-serif"}}beginPath(){this.path=[],this.subpath=[],this.path.push({method:"beginPath",data:[]})}moveTo(e,t){this.path.push({method:"moveTo",data:[e,t]}),this.subpath=[[e,t]]}lineTo(e,t){0===this.path.length&&0===this.subpath.length?this.path.push({method:"moveTo",data:[e,t]}):this.path.push({method:"lineTo",data:[e,t]}),this.subpath.push([e,t])}quadraticCurveTo(e,t,n,o){this.path.push({method:"quadraticCurveTo",data:[e,t,n,o]}),this.subpath.push([n,o])}bezierCurveTo(e,t,n,o,r,i){this.path.push({method:"bezierCurveTo",data:[e,t,n,o,r,i]}),this.subpath.push([r,i])}arc(e,t,n,o,r,i=!1){this.path.push({method:"arc",data:[e,t,n,o,r,i]}),this.subpath.push([e,t])}rect(e,t,n,o){this.path.push({method:"rect",data:[e,t,n,o]}),this.subpath=[[e,t]]}arcTo(e,t,n,o,r){this.path.push({method:"arcTo",data:[e,t,n,o,r]}),this.subpath.push([n,o])}clip(){this.actions.push({method:"clip",data:[...this.path]})}closePath(){this.path.push({method:"closePath",data:[]}),this.subpath.length&&(this.subpath=[this.subpath.shift()])}clearActions(){this.actions=[],this.path=[],this.subpath=[]}getActions(){var e=[...this.actions];return this.clearActions(),e}set lineDashOffset(e){this.actions.push({method:"setLineDashOffset",data:[e]})}set globalCompositeOperation(e){this.actions.push({method:"setGlobalCompositeOperation",data:[e]})}set shadowBlur(e){this.actions.push({method:"setShadowBlur",data:[e]})}set shadowColor(e){this.actions.push({method:"setShadowColor",data:[e]})}set shadowOffsetX(e){this.actions.push({method:"setShadowOffsetX",data:[e]})}set shadowOffsetY(e){this.actions.push({method:"setShadowOffsetY",data:[e]})}set font(e){var t=this;this.state.font=e;var n=e.match(/^(([\w\-]+\s)*)(\d+\.?\d*r?px)(\/(\d+\.?\d*(r?px)?))?\s+(.*)/);if(n){var o=n[1].trim().split(/\s/),r=parseFloat(n[3]),i=n[7],s=[];o.forEach((function(e,n){["italic","oblique","normal"].indexOf(e)>-1?(s.push({method:"setFontStyle",data:[e]}),t.state.fontStyle=e):["bold","normal","lighter","bolder"].indexOf(e)>-1||/^\d+$/.test(e)?(s.push({method:"setFontWeight",data:[e]}),t.state.fontWeight=e):0===n?(s.push({method:"setFontStyle",data:["normal"]}),t.state.fontStyle="normal"):1===n&&a()})),1===o.length&&a(),o=s.map((function(e){return e.data[0]})).join(" "),this.state.fontSize=r,this.state.fontFamily=i,this.actions.push({method:"setFont",data:[`${o} ${r}px ${i}`]})}else console.warn("Failed to set 'font' on 'CanvasContext': invalid format.");function a(){s.push({method:"setFontWeight",data:["normal"]}),t.state.fontWeight="normal"}}get font(){return this.state.font}set fillStyle(e){this.setFillStyle(e)}set strokeStyle(e){this.setStrokeStyle(e)}set globalAlpha(e){e=Math.floor(255*parseFloat(e)),this.actions.push({method:"setGlobalAlpha",data:[e]})}set textAlign(e){this.actions.push({method:"setTextAlign",data:[e]})}set lineCap(e){this.actions.push({method:"setLineCap",data:[e]})}set lineJoin(e){this.actions.push({method:"setLineJoin",data:[e]})}set lineWidth(e){this.actions.push({method:"setLineWidth",data:[e]})}set miterLimit(e){this.actions.push({method:"setMiterLimit",data:[e]})}set textBaseline(e){this.actions.push({method:"setTextBaseline",data:[e]})}},map:class{constructor(e,t){this.id=e,this.pageId=t}getCenterLocation(e){nf(this.id,this.pageId,"getCenterLocation",e)}moveToLocation(e){nf(this.id,this.pageId,"moveToLocation",e)}getScale(e){nf(this.id,this.pageId,"getScale",e)}getRegion(e){nf(this.id,this.pageId,"getRegion",e)}includePoints(e){nf(this.id,this.pageId,"includePoints",e)}translateMarker(e){nf(this.id,this.pageId,"translateMarker",e)}$getAppMap(){}addCustomLayer(e){nf(this.id,this.pageId,"addCustomLayer",e)}removeCustomLayer(e){nf(this.id,this.pageId,"removeCustomLayer",e)}addGroundOverlay(e){nf(this.id,this.pageId,"addGroundOverlay",e)}removeGroundOverlay(e){nf(this.id,this.pageId,"removeGroundOverlay",e)}updateGroundOverlay(e){nf(this.id,this.pageId,"updateGroundOverlay",e)}initMarkerCluster(e){nf(this.id,this.pageId,"initMarkerCluster",e)}addMarkers(e){nf(this.id,this.pageId,"addMarkers",e)}removeMarkers(e){nf(this.id,this.pageId,"removeMarkers",e)}moveAlong(e){nf(this.id,this.pageId,"moveAlong",e)}setLocMarkerIcon(e){nf(this.id,this.pageId,"setLocMarkerIcon",e)}openMapApp(e){nf(this.id,this.pageId,"openMapApp",e)}on(e,t){nf(this.id,this.pageId,"on",{name:e,callback:t})}},video:class{constructor(e,t){this.id=e,this.pageId=t}play(){zd(this.id,this.pageId,"play")}pause(){zd(this.id,this.pageId,"pause")}stop(){zd(this.id,this.pageId,"stop")}seek(e){zd(this.id,this.pageId,"seek",{position:e})}sendDanmu(e){zd(this.id,this.pageId,"sendDanmu",e)}playbackRate(e){~tf.indexOf(e)||(e=1),zd(this.id,this.pageId,"playbackRate",{rate:e})}requestFullScreen(e={}){zd(this.id,this.pageId,"requestFullScreen",e)}exitFullScreen(){zd(this.id,this.pageId,"exitFullScreen")}showStatusBar(){zd(this.id,this.pageId,"showStatusBar")}hideStatusBar(){zd(this.id,this.pageId,"hideStatusBar")}},editor:class{constructor(e,t){this.id=e,this.pageId=t}format(e,t){this._exec("format",{name:e,value:t})}insertDivider(){this._exec("insertDivider")}insertImage(e){this._exec("insertImage",e)}insertText(e){this._exec("insertText",e)}setContents(e){this._exec("setContents",e)}getContents(e){this._exec("getContents",e)}clear(e){this._exec("clear",e)}removeFormat(e){this._exec("removeFormat",e)}undo(e){this._exec("undo",e)}redo(e){this._exec("redo",e)}blur(e){this._exec("blur",e)}getSelectionText(e){this._exec("getSelectionText",e)}scrollIntoView(e){this._exec("scrollIntoView",e)}_exec(e,t){!function(e,t,n,o){const r={options:o},i=o&&("success"in o||"fail"in o||"complete"in o);if(i){const e=String(cf++);r.callbackId=e,uf[e]=o}mg.invokeViewMethod(`editor.${e}`,{type:n,data:r},t,(({callbackId:e,data:t})=>{i&&(fe(uf[e],t),delete uf[e])}))}(this.id,this.pageId,e,t)}}};function df(e){if(e&&e.contextInfo){const{id:t,type:n,page:o}=e.contextInfo,r=ff[n];e.context=new r(t,o),delete e.contextInfo}}class pf{constructor(e,t,n,o){this._selectorQuery=e,this._component=t,this._selector=n,this._single=o}boundingClientRect(e){return this._selectorQuery._push(this._selector,this._component,this._single,{id:!0,dataset:!0,rect:!0,size:!0},e),this._selectorQuery}fields(e,t){return this._selectorQuery._push(this._selector,this._component,this._single,e,t),this._selectorQuery}scrollOffset(e){return this._selectorQuery._push(this._selector,this._component,this._single,{id:!0,dataset:!0,scrollOffset:!0},e),this._selectorQuery}context(e){return this._selectorQuery._push(this._selector,this._component,this._single,{context:!0},e),this._selectorQuery}node(e){return this._selectorQuery._push(this._selector,this._component,this._single,{node:!0},e),this._selectorQuery}}class hf{constructor(e){this._component=void 0,this._page=e,this._queue=[],this._queueCb=[]}exec(e){return function(e,t,n){const o=[];t.forEach((({component:t,selector:n,single:r,fields:i})=>{null===t?o.push(function(e){const t={};e.id&&(t.id="");e.dataset&&(t.dataset={});e.rect&&(t.left=0,t.right=0,t.top=0,t.bottom=0);e.size&&(t.width=document.documentElement.clientWidth,t.height=document.documentElement.clientHeight);if(e.scrollOffset){const e=document.documentElement,n=document.body;t.scrollLeft=e.scrollLeft||n.scrollLeft||0,t.scrollTop=e.scrollTop||n.scrollTop||0,t.scrollHeight=e.scrollHeight||n.scrollHeight||0,t.scrollWidth=e.scrollWidth||n.scrollWidth||0}return t}(i)):o.push(function(e,t,n,o,r){const i=function(e,t){if(!e)return t.$el;return e.$el}(t,e),s=i.parentElement;if(!s)return o?null:[];const{nodeType:a}=i,l=3===a||8===a;if(o){const e=l?s.querySelector(n):Ud(i,n)?i:i.querySelector(n);return e?qd(e,r):null}{let e=[];const t=(l?s:i).querySelectorAll(n);return t&&t.length&&[].forEach.call(t,(t=>{e.push(qd(t,r))})),!l&&Ud(i,n)&&e.unshift(qd(i,r)),e}}(e,t,n,r,i))})),n(o)}(this._page,this._queue,(t=>{const n=this._queueCb;t.forEach(((e,t)=>{g(e)?e.forEach(df):df(e);const o=n[t];y(o)&&o.call(this,e)})),y(e)&&e.call(this,t)})),this._nodesRef}in(e){return this._component=he(e),this}select(e){return this._nodesRef=new pf(this,this._component,e,!0)}selectAll(e){return this._nodesRef=new pf(this,this._component,e,!1)}selectViewport(){return this._nodesRef=new pf(this,null,"",!0)}_push(e,t,n,o,r){this._queue.push({component:t,selector:e,single:n,fields:o}),this._queueCb.push(r)}}const gf=Du(0,(e=>((e=he(e))&&!function(e){const t=he(e);if(t.$page)return hc(t);if(!t.$)return;{const{$pageInstance:e}=t.$;if(e)return hc(e.proxy)}const n=t.$.root.proxy;return n&&n.$page?hc(n):void 0}(e)&&(e=null),new hf(e||uc())))),mf={formatArgs:{}},vf={duration:400,timingFunction:"linear",delay:0,transformOrigin:"50% 50% 0"};class yf{constructor(e){this.actions=[],this.currentTransform={},this.currentStepAnimates=[],this.option=f({},vf,e)}_getOption(e){const t={transition:f({},this.option,e),transformOrigin:""};return t.transformOrigin=t.transition.transformOrigin,delete t.transition.transformOrigin,t}_pushAnimates(e,t){this.currentStepAnimates.push({type:e,args:t})}_converType(e){return e.replace(/[A-Z]/g,(e=>`-${e.toLowerCase()}`))}_getValue(e){return"number"==typeof e?`${e}px`:e}export(){const e=this.actions;return this.actions=[],{actions:e}}step(e){return this.currentStepAnimates.forEach((e=>{"style"!==e.type?this.currentTransform[e.type]=e:this.currentTransform[`${e.type}.${e.args[0]}`]=e})),this.actions.push({animates:Object.values(this.currentTransform),option:this._getOption(e)}),this.currentStepAnimates=[],this}}const bf=ue((()=>{const e=["opacity","backgroundColor"],t=["width","height","left","right","top","bottom"];["matrix","matrix3d","rotate","rotate3d","rotateX","rotateY","rotateZ","scale","scale3d","scaleX","scaleY","scaleZ","skew","skewX","skewY","translate","translate3d","translateX","translateY","translateZ"].concat(e,t).forEach((n=>{yf.prototype[n]=function(...o){return e.concat(t).includes(n)?this._pushAnimates("style",[this._converType(n),t.includes(n)?this._getValue(o[0]):o[0]]):this._pushAnimates(n,o),this}}))})),wf=Du(0,(e=>(bf(),new yf(e))),0,mf),_f=Du(0,(()=>{const e=th();return e&&e.$vm?e.$vm.$locale:vl().getLocale()})),xf={onUnhandledRejection:[],onPageNotFound:[],onError:[],onShow:[],onHide:[]};const Sf={formatArgs:{count(e,t){(!e||e<=0)&&(t.count=9)},sizeType(e,t){t.sizeType=gu(e,fu)},sourceType(e,t){t.sourceType=gu(e,du)},extension(e,t){if(e instanceof Array&&0===e.length)return"param extension should not be empty.";e||(t.extension=["*"])}}},Tf="json",Cf=["text","arraybuffer"],kf=encodeURIComponent;ArrayBuffer,Boolean;const Ef={formatArgs:{method(e,t){t.method=hu((e||"").toUpperCase(),pu)},data(e,t){t.data=e||""},url(e,t){t.method===pu[0]&&C(t.data)&&Object.keys(t.data).length&&(t.url=function(e,t){let n=e.split("#");const o=n[1]||"";n=n[0].split("?");let r=n[1]||"";e=n[0];const i=r.split("&").filter((e=>e)),s={};i.forEach((e=>{const t=e.split("=");s[t[0]]=t[1]}));for(const a in t)if(h(t,a)){let e=t[a];null==e?e="":C(e)&&(e=JSON.stringify(e)),s[kf(a)]=kf(e)}return r=Object.keys(s).map((e=>`${e}=${s[e]}`)).join("&"),e+(r?"?"+r:"")+(o?"#"+o:"")}(e,t.data))},header(e,t){const n=t.header=e||{};t.method!==pu[0]&&(Object.keys(n).find((e=>"content-type"===e.toLowerCase()))||(n["Content-Type"]="application/json"))},dataType(e,t){t.dataType=(e||Tf).toLowerCase()},responseType(e,t){t.responseType=(e||"").toLowerCase(),-1===Cf.indexOf(t.responseType)&&(t.responseType="text")}}},$f={formatArgs:{header(e,t){t.header=e||{}}}},Of={formatArgs:{filePath(e,t){e&&(t.filePath=Pd(e))},header(e,t){t.header=e||{}},formData(e,t){t.formData=e||{}}}},Lf={formatArgs:{header(e,t){t.header=e||{}},method(e,t){t.method=hu((e||"").toUpperCase(),pu)},protocols(e,t){b(e)&&(t.protocols=[e])}}};const Af={url:{type:String,required:!0}},Pf=(If(["slide-in-right","slide-in-left","slide-in-top","slide-in-bottom","fade-in","zoom-out","zoom-fade-out","pop-in","none"]),If(["slide-out-right","slide-out-left","slide-out-top","slide-out-bottom","fade-out","zoom-in","zoom-fade-in","pop-out","none"]),Ff("navigateTo")),jf=Ff("redirectTo"),Rf=Ff("reLaunch"),Mf={formatArgs:{delta(e,t){e=parseInt(e+"")||1,t.delta=Math.min(vd().length-1,e)}}};function If(e){return{animationType:{type:String,validator(t){if(t&&-1===e.indexOf(t))return"`"+t+"` is not supported for `animationType` (supported values are: `"+e.join("`|`")+"`)"}},animationDuration:{type:Number}}}let Nf;function Bf(){Nf=""}function Ff(e){return{formatArgs:{url:Df(e)},beforeAll:Bf}}function Df(e){return function(t,n){if(!t)return'Missing required args: "url"';const o=(t=function(e){if(0===e.indexOf("/")||0===e.indexOf("uni:"))return e;let t="";const n=vd();return n.length&&(t=tc(n[n.length-1]).route),wc(t,e)}(t)).split("?")[0],r=_c(o,!0);if(!r)return"page `"+t+"` is not found";if("navigateTo"===e||"redirectTo"===e){if(r.meta.isTabBar)return`can not ${e} a tabbar page`}else if("switchTab"===e&&!r.meta.isTabBar)return"can not switch to no-tabBar page";if("switchTab"!==e&&"preloadPage"!==e||!r.meta.isTabBar||"appLaunch"===n.openType||(t=o),r.meta.isEntry&&(t=t.replace(r.alias,"/")),n.url=function(e){if(!b(e))return e;const t=e.indexOf("?");if(-1===t)return e;const n=e.slice(t+1).trim().replace(/^(\?|#|&)/,"");if(!n)return e;e=e.slice(0,t);const o=[];return n.split("&").forEach((e=>{const t=e.replace(/\+/g," ").split("="),n=t.shift(),r=t.length>0?t.join("="):"";o.push(n+"="+encodeURIComponent(r))})),o.length?e+"?"+o.join("&"):e}(t),"unPreloadPage"!==e)if("preloadPage"!==e){if(Nf===t&&"appLaunch"!==n.openType)return`${Nf} locked`;__uniConfig.ready&&(Nf=t)}else if(r.meta.isTabBar){const e=vd(),t=r.path.slice(1);if(e.find((e=>e.route===t)))return"tabBar page `"+t+"` already exists"}}}const Hf={formatArgs:{duration:300}},Vf=(Boolean,{formatArgs:{title:"",mask:!1}}),Wf=["success","loading","none","error"],zf=(Boolean,{formatArgs:{title:"",icon(e,t){t.icon=hu(e,Wf)},image(e,t){t.image=e?Pd(e):""},duration:1500,mask:!1}});function qf(){const e=uc();if(!e)return;const t=md(),n=t.keys();for(const o of n){const e=t.get(o);e.$.__isTabBar?e.$.__isActive=!1:bd(o)}e.$.__isTabBar&&(e.$.__isVisible=!1,gc(e,"onHide"))}function Uf(e,t){return e===t.fullPath||"/"===e&&t.meta.isEntry}function Gf(e){const t=md().values();for(const n of t){const t=ud(n);if(Uf(e,t))return n.$.__isActive=!0,t.id}}const Xf=Hu("switchTab",(({url:e,tabBarText:t,isAutomatedTesting:n},{resolve:o,reject:r})=>{if(fd.handledBeforeEntryPageRoutes)return qf(),Qf({type:"switchTab",url:e,tabBarText:t,isAutomatedTesting:n},Gf(e)).then(o).catch(r);pd.push({args:{type:"switchTab",url:e,tabBarText:t,isAutomatedTesting:n},resolve:o,reject:r})}),0,Ff("switchTab"));function Yf(){const e=ac();if(!e)return;const t=ud(e);bd(xd(t.path,t.id))}const Zf=Hu("redirectTo",(({url:e,isAutomatedTesting:t},{resolve:n,reject:o})=>{if(fd.handledBeforeEntryPageRoutes)return Yf(),Qf({type:"redirectTo",url:e,isAutomatedTesting:t}).then(n).catch(o);hd.push({args:{type:"redirectTo",url:e,isAutomatedTesting:t},resolve:n,reject:o})}),0,jf);function Jf(){const e=md().keys();for(const t of e)bd(t)}const Kf=Hu("reLaunch",(({url:e,isAutomatedTesting:t},{resolve:n,reject:o})=>{if(fd.handledBeforeEntryPageRoutes)return Jf(),Qf({type:"reLaunch",url:e,isAutomatedTesting:t}).then(n).catch(o);gd.push({args:{type:"reLaunch",url:e,isAutomatedTesting:t},resolve:n,reject:o})}),0,Rf);function Qf({type:e,url:t,tabBarText:n,events:o,isAutomatedTesting:r},i){const s=th().$router,{path:a,query:l}=function(e){const[t,n]=e.split("?",2);return{path:t,query:$e(n||"")}}(t);return new Promise(((t,c)=>{const u=function(e,t){return{__id__:t||++wd,__type__:e}}(e,i);s["navigateTo"===e?"push":"replace"]({path:a,query:l,state:u,force:!0}).then((i=>{if(Ca(i))return c(i.message);if("switchTab"===e&&(s.currentRoute.value.meta.tabBarText=n),"navigateTo"===e){const e=s.currentRoute.value.meta;return e.eventChannel?o&&(Object.keys(o).forEach((t=>{e.eventChannel._addListener(t,"on",o[t])})),e.eventChannel._clearCache()):e.eventChannel=new Le(u.__id__,o),t(r?{__id__:u.__id__}:{eventChannel:e.eventChannel})}return r?t({__id__:u.__id__}):t()}))}))}function ed(){if(fd.handledBeforeEntryPageRoutes)return;fd.handledBeforeEntryPageRoutes=!0;const e=[...dd];dd.length=0,e.forEach((({args:e,resolve:t,reject:n})=>Qf(e).then(t).catch(n)));const t=[...pd];pd.length=0,t.forEach((({args:e,resolve:t,reject:n})=>(qf(),Qf(e,Gf(e.url)).then(t).catch(n))));const n=[...hd];hd.length=0,n.forEach((({args:e,resolve:t,reject:n})=>(Yf(),Qf(e).then(t).catch(n))));const o=[...gd];gd.length=0,o.forEach((({args:e,resolve:t,reject:n})=>(Jf(),Qf(e).then(t).catch(n))))}function td(e){const t=window.CSS&&window.CSS.supports;return t&&(t(e)||t.apply(window.CSS,e.split(":")))}const nd=td("--a:0"),od=td("top:env(a)"),rd=td("top:constant(a)"),id={"css.var":nd,"css.env":od,"css.constant":rd,"css.backdrop-filter":td("backdrop-filter:blur(10px)")},sd=Du(0,(e=>!h(id,e)||id[e])),ad=(()=>od?"env":rd?"constant":"")();function ld(e){var t,n;Ql({"--window-top":(n=0,ad?`calc(${n}px + ${ad}(safe-area-inset-top))`:`${n}px`),"--window-bottom":(t=0,ad?`calc(${t}px + ${ad}(safe-area-inset-bottom))`:`${t}px`)})}const cd=new Map;function ud(e){return e.$page}const fd={handledBeforeEntryPageRoutes:!1},dd=[],pd=[],hd=[],gd=[];function md(){return cd}function vd(){return yd()}function yd(){const e=[],t=cd.values();for(const n of t)n.$.__isTabBar?n.$.__isActive&&e.push(n):e.push(n);return e}function bd(e,t=!0){const n=cd.get(e);n.$.__isUnload=!0,gc(n,"onUnload"),cd.delete(e),t&&function(e){const t=Sd.get(e);t&&(Sd.delete(e),Td.pruneCacheEntry(t))}(e)}let wd=uu();function _d(e){const t=function(e){const t=au();let n=e.fullPath;return e.meta.isEntry&&-1===n.indexOf(e.meta.route)&&(n="/"+e.meta.route+n.replace("/","")),pc("navigateTo",n,{},t)}(e.$route);!function(e,t){e.route=t.route,e.$vm=e,e.$page=t,e.$mpType="page",e.$fontFamilySet=new Set,t.meta.isTabBar&&(e.$.__isTabBar=!0,e.$.__isActive=!0)}(e,t),cd.set(xd(t.path,t.id),e),1===cd.size&&setTimeout((()=>{ed()}),0)}function xd(e,t){return e+"$$"+t}const Sd=new Map,Td={get:e=>Sd.get(e),set(e,t){!function(e){const t=parseInt(e.split("$$")[1]);if(!t)return;Td.forEach(((e,n)=>{const o=parseInt(n.split("$$")[1]);o&&o>t&&(Td.delete(n),Td.pruneCacheEntry(e),On((()=>{cd.forEach(((e,t)=>{e.$.isUnmounted&&cd.delete(t)}))})))}))}(e),Sd.set(e,t)},delete(e){Sd.get(e)&&Sd.delete(e)},forEach(e){Sd.forEach(e)}};function Cd(e,t){!function(e){const t=Ed(e),{body:n}=document;$d&&n.removeAttribute($d),t&&n.setAttribute(t,""),$d=t}(e),ld(),function(e){{const t="nvue-dir-"+__uniConfig.nvue["flex-direction"];e.isNVue?(document.body.setAttribute("nvue",""),document.body.setAttribute(t,"")):(document.body.removeAttribute("nvue"),document.body.removeAttribute(t))}}(t),Ld(e,t)}function kd(e){const t=Ed(e);t&&function(e){const t=document.querySelector("uni-page-body");t&&t.setAttribute(e,"")}(t)}function Ed(e){return e.type.__scopeId}let $d,Od;function Ld(e,t){if(document.removeEventListener("touchmove",mc),Od&&document.removeEventListener("scroll",Od),t.disableScroll)return document.addEventListener("touchmove",mc);const{onPageScroll:n,onReachBottom:o}=e,r="transparent"===t.navigationBar.type;if(!(null==n?void 0:n.length)&&!(null==o?void 0:o.length)&&!r)return;const i={},s=ud(e.proxy).id;(n||r)&&(i.onPageScroll=function(e,t,n){return o=>{t&&gg.publishHandler("onPageScroll",{scrollTop:o},e),n&&gg.emit(e+".onPageScroll",{scrollTop:o})}}(s,n,r)),(null==o?void 0:o.length)&&(i.onReachBottomDistance=t.onReachBottomDistance||50,i.onReachBottom=()=>gg.publishHandler("onReachBottom",{},s)),Od=bc(i),requestAnimationFrame((()=>document.addEventListener("scroll",Od)))}function Ad(e){const{base:t}=__uniConfig.router;return 0===ce(e).indexOf(t)?ce(e):t+e}function Pd(e){const{base:t,assets:n}=__uniConfig.router;if("./"===t&&(0!==e.indexOf("./")||!e.includes("/static/")&&0!==e.indexOf("./"+(n||"assets")+"/")||(e=e.slice(1))),0===e.indexOf("/")){if(0!==e.indexOf("//"))return Ad(e.slice(1));e="https:"+e}if(ne.test(e)||oe.test(e)||0===e.indexOf("blob:"))return e;const o=yd();return o.length?Ad(wc(ud(o[o.length-1]).route,e).slice(1)):e}const jd=navigator.userAgent,Rd=/android/i.test(jd),Md=/iphone|ipad|ipod/i.test(jd),Id=jd.match(/Windows NT ([\d|\d.\d]*)/i),Nd=/Macintosh|Mac/i.test(jd),Bd=/Linux|X11/i.test(jd),Fd=Nd&&navigator.maxTouchPoints>0;function Dd(){return/^Apple/.test(navigator.vendor)&&"number"==typeof window.orientation}function Hd(e){return e&&90===Math.abs(window.orientation)}function Vd(e,t){return e?Math[t?"max":"min"](screen.width,screen.height):screen.width}function Wd(e){return Math.min(window.innerWidth,document.documentElement.clientWidth,e)||e}function zd(e,t,n,o){mg.invokeViewMethod("video."+e,{videoId:e,type:n,data:o},t)}function qd(e,t){const n={},{top:o,topWindowHeight:r}=Kl();if(t.node){const t=e.tagName.split("-")[1]||e.tagName;t&&(n.node=e.querySelector(t))}if(t.id&&(n.id=e.id),t.dataset&&(n.dataset=_e(e)),t.rect||t.size){const i=e.getBoundingClientRect();t.rect&&(n.left=i.left,n.right=i.right,n.top=i.top-o-r,n.bottom=i.bottom-o-r),t.size&&(n.width=i.width,n.height=i.height)}if(g(t.properties)&&t.properties.forEach((e=>{e=e.replace(/-([a-z])/g,(function(e,t){return t.toUpperCase()}))})),t.scrollOffset)if("UNI-SCROLL-VIEW"===e.tagName){const t=e.children[0].children[0];n.scrollLeft=t.scrollLeft,n.scrollTop=t.scrollTop,n.scrollHeight=t.scrollHeight,n.scrollWidth=t.scrollWidth}else n.scrollLeft=0,n.scrollTop=0,n.scrollHeight=0,n.scrollWidth=0;if(g(t.computedStyle)){const o=getComputedStyle(e);t.computedStyle.forEach((e=>{n[e]=o[e]}))}return t.context&&(n.contextInfo=function(e){return e.__uniContextInfo}(e)),n}function Ud(e,t){return(e.matches||e.matchesSelector||e.mozMatchesSelector||e.msMatchesSelector||e.oMatchesSelector||e.webkitMatchesSelector||function(e){const t=this.parentElement.querySelectorAll(e);let n=t.length;for(;--n>=0&&t.item(n)!==this;);return n>-1}).call(e,t)}const Gd={};function Xd(e,t){const n=Gd[e];return n?Promise.resolve(n):/^data:[a-z-]+\/[a-z-]+;base64,/.test(e)?Promise.resolve(function(e){const t=e.split(","),n=t[0].match(/:(.*?);/),o=n?n[1]:"",r=atob(t[1]);let i=r.length;const s=new Uint8Array(i);for(;i--;)s[i]=r.charCodeAt(i);return Yd(s,o)}(e)):t?Promise.reject(new Error("not find")):new Promise(((t,n)=>{const o=new XMLHttpRequest;o.open("GET",e,!0),o.responseType="blob",o.onload=function(){t(this.response)},o.onerror=n,o.send()}))}function Yd(e,t){let n;if(e instanceof File)n=e;else{t=t||e.type||"";const r=`${Date.now()}${function(e){const t=e.split("/")[1];return t?`.${t}`:""}(t)}`;try{n=new File([e],r,{type:t})}catch(o){n=e=e instanceof Blob?e:new Blob([e],{type:t}),n.name=n.name||r}}return n}function Zd(e){for(const n in Gd)if(h(Gd,n)){if(Gd[n]===e)return n}var t=(window.URL||window.webkitURL).createObjectURL(e);return Gd[t]=e,t}const Jd=zc(),Kd=zc();const Qd=Xc({name:"ResizeSensor",props:{initial:{type:Boolean,default:!1}},emits:["resize"],setup(e,{emit:t}){const n=un(null),o=function(e){return()=>{const{firstElementChild:t,lastElementChild:n}=e.value;t.scrollLeft=1e5,t.scrollTop=1e5,n.scrollLeft=1e5,n.scrollTop=1e5}}(n),r=function(e,t,n){const o=Gt({width:-1,height:-1});return ro((()=>f({},o)),(e=>t("resize",e))),()=>{const t=e.value;t&&(o.width=t.offsetWidth,o.height=t.offsetHeight,n())}}(n,t,o);return function(e,t,n,o){Po(o),Wo((()=>{t.initial&&On(n);const r=e.value;r.offsetParent!==r.parentElement&&(r.parentElement.style.position="relative"),"AnimationEvent"in window||o()}))}(n,e,r,o),()=>ai("uni-resize-sensor",{ref:n,onAnimationstartOnce:r},[ai("div",{onScroll:r},[ai("div",null,null)],40,["onScroll"]),ai("div",{onScroll:r},[ai("div",null,null)],40,["onScroll"])],40,["onAnimationstartOnce"])}});function ep(){}const tp={cursorSpacing:{type:[Number,String],default:0},showConfirmBar:{type:[Boolean,String],default:"auto"},adjustPosition:{type:[Boolean,String],default:!0},autoBlur:{type:[Boolean,String],default:!1}};function np(e,t,n){function o(e){const t=Li((()=>0===String(navigator.vendor).indexOf("Apple")));e.addEventListener("focus",(()=>{clearTimeout(undefined),document.addEventListener("click",ep,!1)}));e.addEventListener("blur",(()=>{t.value&&e.blur(),document.removeEventListener("click",ep,!1),t.value&&document.documentElement.scrollTo(document.documentElement.scrollLeft,document.documentElement.scrollTop)}))}ro((()=>t.value),(e=>e&&o(e)))}const op={src:{type:String,default:""},mode:{type:String,default:"scaleToFill"},lazyLoad:{type:[Boolean,String],default:!1},draggable:{type:Boolean,default:!1}},rp={widthFix:["offsetWidth","height",(e,t)=>e/t],heightFix:["offsetHeight","width",(e,t)=>e*t]},ip={aspectFit:["center center","contain"],aspectFill:["center center","cover"],widthFix:[,"100% 100%"],heightFix:[,"100% 100%"],top:["center top"],bottom:["center bottom"],center:["center center"],left:["left center"],right:["right center"],"top left":["left top"],"top right":["right top"],"bottom left":["left bottom"],"bottom right":["right bottom"]},sp=Xc({name:"Image",props:op,setup(e,{emit:t}){const n=un(null),o=function(e,t){const n=un(""),o=Li((()=>{let e="auto",o="";const r=ip[t.mode];return r?(r[0]&&(o=r[0]),r[1]&&(e=r[1])):(o="0% 0%",e="100% 100%"),`background-image:${n.value?'url("'+n.value+'")':"none"};background-position:${o};background-size:${e};`})),r=Gt({rootEl:e,src:Li((()=>t.src?Pd(t.src):"")),origWidth:0,origHeight:0,origStyle:{width:"",height:""},modeStyle:o,imgSrc:n});return Wo((()=>{const t=e.value;r.origWidth=t.clientWidth||0,r.origHeight=t.clientHeight||0})),r}(n,e),r=Jc(n,t),{fixSize:i}=function(e,t,n){const o=()=>{const{mode:o}=t,r=rp[o];if(!r)return;const{origWidth:i,origHeight:s}=n,a=i&&s?i/s:0;if(!a)return;const l=e.value,c=l[r[0]];c&&(l.style[r[1]]=function(e){ap&&e>10&&(e=2*Math.round(e/2));return e}(r[2](c,a))+"px")},r=()=>{const{style:t}=e.value,{origStyle:{width:o,height:r}}=n;t.width=o,t.height=r};return ro((()=>t.mode),((e,t)=>{rp[t]&&r(),rp[e]&&o()})),{fixSize:o,resetSize:r}}(n,e,o);return function(e,t,n,o,r){let i,s;const a=(t=0,n=0,o="")=>{e.origWidth=t,e.origHeight=n,e.imgSrc=o},l=l=>{if(!l)return c(),void a();i=i||new Image,i.onload=e=>{const{width:u,height:f}=i;a(u,f,l),On((()=>{o()})),i.draggable=t.draggable,s&&s.remove(),s=i,n.value.appendChild(i),c(),r("load",e,{width:u,height:f})},i.onerror=t=>{a(),c(),r("error",t,{errMsg:`GET ${e.src} 404 (Not Found)`})},i.src=l},c=()=>{i&&(i.onload=null,i.onerror=null,i=null)};ro((()=>e.src),(e=>l(e))),ro((()=>e.imgSrc),(e=>{!e&&s&&(s.remove(),s=null)})),Wo((()=>l(e.src))),Uo((()=>c()))}(o,e,n,i,r),()=>ai("uni-image",{ref:n},[ai("div",{style:o.modeStyle},null,4),rp[e.mode]?ai(Qd,{onResize:i},null,8,["onResize"]):ai("span",null,null)],512)}});const ap="Google Inc."===navigator.vendor;const lp=Se(!0),cp=[];let up=0,fp=!1;const dp=e=>cp.forEach((t=>t.userAction=e));function pp(e={userAction:!1}){if(!fp){["touchstart","touchmove","touchend","mousedown","mouseup"].forEach((e=>{document.addEventListener(e,(function(){!up&&dp(!0),up++,setTimeout((()=>{!--up&&dp(!1)}),0)}),lp)})),fp=!0}cp.push(e)}function hp(){const e=Gt({userAction:!1});return Wo((()=>{pp(e)})),Uo((()=>{!function(e){const t=cp.indexOf(e);t>=0&&cp.splice(t,1)}(e)})),{state:e}}function gp(e,t){const n=document.activeElement;if(!n)return t({});const o={};["input","textarea"].includes(n.tagName.toLowerCase())&&(o.start=n.selectionStart,o.end=n.selectionEnd),t(o)}const mp=function(){var e,t,n;e=cc(),n=gp,t=kl(e,t="getSelectedTextRange"),Cl[t]||(Cl[t]=n)};function vp(e,t,n){"number"===t&&isNaN(Number(e))&&(e="");return null==e?"":String(e)}const yp=["none","text","decimal","numeric","tel","search","email","url"],bp=f({},{name:{type:String,default:""},modelValue:{type:[String,Number]},value:{type:[String,Number]},disabled:{type:[Boolean,String],default:!1},autoFocus:{type:[Boolean,String],default:!1},focus:{type:[Boolean,String],default:!1},cursor:{type:[Number,String],default:-1},selectionStart:{type:[Number,String],default:-1},selectionEnd:{type:[Number,String],default:-1},type:{type:String,default:"text"},password:{type:[Boolean,String],default:!1},placeholder:{type:String,default:""},placeholderStyle:{type:String,default:""},placeholderClass:{type:String,default:""},maxlength:{type:[Number,String],default:140},confirmType:{type:String,default:"done"},confirmHold:{type:Boolean,default:!1},ignoreCompositionEvent:{type:Boolean,default:!0},step:{type:String,default:"0.000000000000000001"},inputmode:{type:String,default:void 0,validator:e=>!!~yp.indexOf(e)},cursorColor:{type:String,default:""}},tp),wp=["input","focus","blur","update:value","update:modelValue","update:focus","compositionstart","compositionupdate","compositionend","keyboardheightchange"];function _p(e,t,n,o){let r=null;r=Oe((n=>{t.value=vp(n,e.type)}),100,{setTimeout:setTimeout,clearTimeout:clearTimeout}),ro((()=>e.modelValue),r),ro((()=>e.value),r);const i=function(e,t){let n,o,r=0;const i=function(...i){const s=Date.now();clearTimeout(n),o=()=>{o=null,r=s,e.apply(this,i)},s-r<t?n=setTimeout(o,t-(s-r)):o()};return i.cancel=function(){clearTimeout(n),o=null},i.flush=function(){clearTimeout(n),o&&o()},i}(((e,t)=>{r.cancel(),n("update:modelValue",t.value),n("update:value",t.value),o("input",e,t)}),100);return Vo((()=>{r.cancel(),i.cancel()})),{trigger:o,triggerInput:(e,t,n)=>{r.cancel(),i(e,t),n&&i.flush()}}}function xp(e,t){hp();const n=Li((()=>e.autoFocus||e.focus));function o(){if(!n.value)return;const e=t.value;e?e.focus():setTimeout(o,100)}ro((()=>e.focus),(e=>{e?o():function(){const e=t.value;e&&e.blur()}()})),Wo((()=>{n.value&&On(o)}))}function Sp(e,t,n,o){mp();const{fieldRef:r,state:i,trigger:s}=function(e,t,n){const o=un(null),r=Jc(t,n),i=Li((()=>{const t=Number(e.selectionStart);return isNaN(t)?-1:t})),s=Li((()=>{const t=Number(e.selectionEnd);return isNaN(t)?-1:t})),a=Li((()=>{const t=Number(e.cursor);return isNaN(t)?-1:t})),l=Li((()=>{var t=Number(e.maxlength);return isNaN(t)?140:t}));let c="";c=vp(e.modelValue,e.type)||vp(e.value,e.type);const u=Gt({value:c,valueOrigin:c,maxlength:l,focus:e.focus,composing:!1,selectionStart:i,selectionEnd:s,cursor:a});return ro((()=>u.focus),(e=>n("update:focus",e))),ro((()=>u.maxlength),(e=>u.value=u.value.slice(0,e)),{immediate:!1}),{fieldRef:o,state:u,trigger:r}}(e,t,n),{triggerInput:a}=_p(e,i,n,s);xp(e,r),np(0,r);const{state:l}=function(){const e=Gt({attrs:{}});return Wo((()=>{let t=bi();for(;t;){const n=t.type.__scopeId;n&&(e.attrs[n]=""),t=t.proxy&&"page"===t.proxy.$mpType?null:t.parent}})),{state:e}}();!function(e,t){const n=Sr(tu,!1);if(!n)return;const o=bi(),r={submit(){const n=o.proxy;return[n[e],b(t)?n[t]:t.value]},reset(){b(t)?o.proxy[t]="":t.value=""}};n.addField(r),Uo((()=>{n.removeField(r)}))}("name",i),function(e,t,n,o,r,i){function s(){const n=e.value;n&&t.focus&&t.selectionStart>-1&&t.selectionEnd>-1&&"number"!==n.type&&(n.selectionStart=t.selectionStart,n.selectionEnd=t.selectionEnd)}function a(){const n=e.value;n&&t.focus&&t.selectionStart<0&&t.selectionEnd<0&&t.cursor>-1&&"number"!==n.type&&(n.selectionEnd=n.selectionStart=t.cursor)}function l(e){return"number"===e.type?null:e.selectionEnd}ro([()=>t.selectionStart,()=>t.selectionEnd],s),ro((()=>t.cursor),a),ro((()=>e.value),(function(){const c=e.value;if(!c)return;const u=function(e,o){e.stopPropagation(),y(i)&&!1===i(e,t)||(t.value=c.value,t.composing&&n.ignoreCompositionEvent||r(e,{value:c.value,cursor:l(c)},o))};function f(e){n.ignoreCompositionEvent||o(e.type,e,{value:e.data})}c.addEventListener("change",(e=>e.stopPropagation())),c.addEventListener("focus",(function(e){t.focus=!0,o("focus",e,{value:t.value}),s(),a()})),c.addEventListener("blur",(function(e){t.composing&&(t.composing=!1,u(e,!0)),t.focus=!1,o("blur",e,{value:t.value,cursor:l(e.target)})})),c.addEventListener("input",u),c.addEventListener("compositionstart",(e=>{e.stopPropagation(),t.composing=!0,f(e)})),c.addEventListener("compositionend",(e=>{e.stopPropagation(),t.composing&&(t.composing=!1,u(e)),f(e)})),c.addEventListener("compositionupdate",f)}))}(r,i,e,s,a,o);return{fieldRef:r,state:i,scopedAttrsState:l,fixDisabledColor:0===String(navigator.vendor).indexOf("Apple")&&CSS.supports("image-orientation:from-image"),trigger:s}}const Tp=f({},bp,{placeholderClass:{type:String,default:"input-placeholder"},textContentType:{type:String,default:""}}),Cp=ue((()=>{{const e=navigator.userAgent;let t="";const n=e.match(/OS\s([\w_]+)\slike/);if(n)t=n[1].replace(/_/g,".");else if(/Macintosh|Mac/i.test(e)&&navigator.maxTouchPoints>0){const n=e.match(/Version\/(\S*)\b/);n&&(t=n[1])}return!!t&&parseInt(t)>=16&&parseFloat(t)<17.2}}));function kp(e,t,n,o,r){if(t.value)if("."===e.data){if("."===t.value.slice(-1))return n.value=o.value=t.value=t.value.slice(0,-1),!1;if(t.value&&!t.value.includes("."))return t.value+=".",r&&(r.fn=()=>{n.value=o.value=t.value=t.value.slice(0,-1),o.removeEventListener("blur",r.fn)},o.addEventListener("blur",r.fn)),!1}else if("deleteContentBackward"===e.inputType&&Cp()&&"."===t.value.slice(-2,-1))return t.value=n.value=o.value=t.value.slice(0,-2),!0}const Ep=Xc({name:"Input",props:Tp,emits:["confirm",...wp],setup(e,{emit:t,expose:n}){const o=["text","number","idcard","digit","password","tel"],r=["off","one-time-code"],i=Li((()=>{let t="";switch(e.type){case"text":t="text","search"===e.confirmType&&(t="search");break;case"idcard":t="text";break;case"digit":t="number";break;default:t=o.includes(e.type)?e.type:"text"}return e.password?"password":t})),s=Li((()=>{const t=r.indexOf(e.textContentType),n=r.indexOf(P(e.textContentType));return r[-1!==t?t:-1!==n?n:0]}));let a=function(e,t){if("number"===t.value){const t=void 0===e.modelValue?e.value:e.modelValue,n=un(null!=t?t.toLocaleString():"");return ro((()=>e.modelValue),(e=>{n.value=null!=e?e.toLocaleString():""})),ro((()=>e.value),(e=>{n.value=null!=e?e.toLocaleString():""})),n}return un("")}(e,i),l={fn:null};const c=un(null),{fieldRef:u,state:f,scopedAttrsState:d,fixDisabledColor:p,trigger:h}=Sp(e,c,t,((t,n)=>{const o=t.target;if("number"===i.value){if(l.fn&&(o.removeEventListener("blur",l.fn),l.fn=null),o.validity&&!o.validity.valid){if((!a.value||!o.value)&&"-"===t.data||"-"===a.value[0]&&"deleteContentBackward"===t.inputType)return a.value="-",n.value="",l.fn=()=>{a.value=o.value=""},o.addEventListener("blur",l.fn),!1;const e=kp(t,a,n,o,l);return"boolean"==typeof e?e:(a.value=n.value=o.value="-"===a.value?"":a.value,!1)}{const e=kp(t,a,n,o,l);if("boolean"==typeof e)return e;a.value=o.value}const r=n.maxlength;if(r>0&&o.value.length>r){o.value=o.value.slice(0,r),n.value=o.value;return(void 0!==e.modelValue&&null!==e.modelValue?e.modelValue.toString():"")!==o.value}}}));ro((()=>f.value),(t=>{"number"!==e.type||"-"===a.value&&""===t||(a.value=t.toString())}));const g=["number","digit"],m=Li((()=>g.includes(e.type)?e.step:""));function v(t){if("Enter"!==t.key)return;const n=t.target;t.stopPropagation(),h("confirm",t,{value:n.value}),!e.confirmHold&&n.blur()}return n({$triggerInput:e=>{t("update:modelValue",e.value),t("update:value",e.value),f.value=e.value}}),()=>{let t=e.disabled&&p?ai("input",{key:"disabled-input",ref:u,value:f.value,tabindex:"-1",readonly:!!e.disabled,type:i.value,maxlength:f.maxlength,step:m.value,class:"uni-input-input",style:e.cursorColor?{caretColor:e.cursorColor}:{},onFocus:e=>e.target.blur()},null,44,["value","readonly","type","maxlength","step","onFocus"]):ai("input",{key:"input",ref:u,value:f.value,onInput:e=>{f.value=e.target.value.toString()},disabled:!!e.disabled,type:i.value,maxlength:f.maxlength,step:m.value,enterkeyhint:e.confirmType,pattern:"number"===e.type?"[0-9]*":void 0,class:"uni-input-input",style:e.cursorColor?{caretColor:e.cursorColor}:{},autocomplete:s.value,onKeyup:v,inputmode:e.inputmode},null,44,["value","onInput","disabled","type","maxlength","step","enterkeyhint","pattern","autocomplete","onKeyup","inputmode"]);return ai("uni-input",{ref:c},[ai("div",{class:"uni-input-wrapper"},[co(ai("div",hi(d.attrs,{style:e.placeholderStyle,class:["uni-input-placeholder",e.placeholderClass]}),[e.placeholder],16),[[Ki,!(f.value.length||"-"===a.value||a.value.includes("."))]]),"search"===e.confirmType?ai("form",{action:"",onSubmit:e=>e.preventDefault(),class:"uni-input-form"},[t],40,["onSubmit"]):t])],512)}}}),$p=Xc({name:"Refresher",props:{refreshState:{type:String,default:""},refresherHeight:{type:Number,default:0},refresherThreshold:{type:Number,default:45},refresherDefaultStyle:{type:String,default:"black"},refresherBackground:{type:String,default:"#fff"}},setup(e,{slots:t}){const n=un(null),o=Li((()=>{const t={backgroundColor:e.refresherBackground};switch(e.refreshState){case"pulling":t.height=e.refresherHeight+"px";break;case"refreshing":t.height=e.refresherThreshold+"px",t.transition="height 0.3s";break;case"":case"refresherabort":case"restore":t.height="0px",t.transition="height 0.3s"}return t})),r=Li((()=>{const t=e.refresherHeight/e.refresherThreshold;return 360*(t>1?1:t)}));return()=>{const{refreshState:i,refresherDefaultStyle:s,refresherThreshold:a}=e;return ai("div",{ref:n,style:o.value,class:"uni-scroll-view-refresher"},["none"!==s?ai("div",{class:"uni-scroll-view-refresh"},[ai("div",{class:"uni-scroll-view-refresh-inner"},["pulling"==i?ai("svg",{key:"refresh__icon",style:{transform:"rotate("+r.value+"deg)"},fill:"#2BD009",class:"uni-scroll-view-refresh__icon",width:"24",height:"24",viewBox:"0 0 24 24"},[ai("path",{d:"M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"},null),ai("path",{d:"M0 0h24v24H0z",fill:"none"},null)],4):null,"refreshing"==i?ai("svg",{key:"refresh__spinner",class:"uni-scroll-view-refresh__spinner",width:"24",height:"24",viewBox:"25 25 50 50"},[ai("circle",{cx:"50",cy:"50",r:"20",fill:"none",style:"color: #2bd009","stroke-width":"3"},null)]):null])]):null,"none"===s?ai("div",{class:"uni-scroll-view-refresher-container",style:{height:`${a}px`}},[t.default&&t.default()]):null],4)}}}),Op=Se(!0),Lp=Xc({name:"ScrollView",compatConfig:{MODE:3},props:{direction:{type:[String],default:"vertical"},scrollX:{type:[Boolean,String],default:!1},scrollY:{type:[Boolean,String],default:!1},showScrollbar:{type:[Boolean,String],default:!0},upperThreshold:{type:[Number,String],default:50},lowerThreshold:{type:[Number,String],default:50},scrollTop:{type:[Number,String],default:0},scrollLeft:{type:[Number,String],default:0},scrollIntoView:{type:String,default:""},scrollWithAnimation:{type:[Boolean,String],default:!1},enableBackToTop:{type:[Boolean,String],default:!1},refresherEnabled:{type:[Boolean,String],default:!1},refresherThreshold:{type:Number,default:45},refresherDefaultStyle:{type:String,default:"black"},refresherBackground:{type:String,default:"#fff"},refresherTriggered:{type:[Boolean,String],default:!1}},emits:["scroll","scrolltoupper","scrolltolower","refresherrefresh","refresherrestore","refresherpulling","refresherabort","update:refresherTriggered"],setup(e,{emit:t,slots:n,expose:o}){const r=un(null),i=un(null),s=un(null),a=un(null),l=Jc(r,t),{state:c,scrollTopNumber:u,scrollLeftNumber:f}=function(e){const t=Li((()=>Number(e.scrollTop)||0)),n=Li((()=>Number(e.scrollLeft)||0));return{state:Gt({lastScrollTop:t.value,lastScrollLeft:n.value,lastScrollToUpperTime:0,lastScrollToLowerTime:0,refresherHeight:0,refreshState:""}),scrollTopNumber:t,scrollLeftNumber:n}}(e),{realScrollX:d,realScrollY:p,_scrollLeftChanged:h,_scrollTopChanged:g}=function(e,t,n,o,r,i,s,a,l){let c=!1,u=0,f=!1,d=()=>{};const p=Li((()=>e.scrollX)),h=Li((()=>e.scrollY)),g=Li((()=>{let t=Number(e.upperThreshold);return isNaN(t)?50:t})),m=Li((()=>{let t=Number(e.lowerThreshold);return isNaN(t)?50:t}));function v(e,t){const n=s.value;let o=0,r="";if(e<0?e=0:"x"===t&&e>n.scrollWidth-n.offsetWidth?e=n.scrollWidth-n.offsetWidth:"y"===t&&e>n.scrollHeight-n.offsetHeight&&(e=n.scrollHeight-n.offsetHeight),"x"===t?o=n.scrollLeft-e:"y"===t&&(o=n.scrollTop-e),0===o)return;let i=a.value;i.style.transition="transform .3s ease-out",i.style.webkitTransition="-webkit-transform .3s ease-out","x"===t?r="translateX("+o+"px) translateZ(0)":"y"===t&&(r="translateY("+o+"px) translateZ(0)"),i.removeEventListener("transitionend",d),i.removeEventListener("webkitTransitionEnd",d),d=()=>x(e,t),i.addEventListener("transitionend",d),i.addEventListener("webkitTransitionEnd",d),"x"===t?n.style.overflowX="hidden":"y"===t&&(n.style.overflowY="hidden"),i.style.transform=r,i.style.webkitTransform=r}function y(e){const n=e.target;r("scroll",e,{scrollLeft:n.scrollLeft,scrollTop:n.scrollTop,scrollHeight:n.scrollHeight,scrollWidth:n.scrollWidth,deltaX:t.lastScrollLeft-n.scrollLeft,deltaY:t.lastScrollTop-n.scrollTop}),h.value&&(n.scrollTop<=g.value&&t.lastScrollTop-n.scrollTop>0&&e.timeStamp-t.lastScrollToUpperTime>200&&(r("scrolltoupper",e,{direction:"top"}),t.lastScrollToUpperTime=e.timeStamp),n.scrollTop+n.offsetHeight+m.value>=n.scrollHeight&&t.lastScrollTop-n.scrollTop<0&&e.timeStamp-t.lastScrollToLowerTime>200&&(r("scrolltolower",e,{direction:"bottom"}),t.lastScrollToLowerTime=e.timeStamp)),p.value&&(n.scrollLeft<=g.value&&t.lastScrollLeft-n.scrollLeft>0&&e.timeStamp-t.lastScrollToUpperTime>200&&(r("scrolltoupper",e,{direction:"left"}),t.lastScrollToUpperTime=e.timeStamp),n.scrollLeft+n.offsetWidth+m.value>=n.scrollWidth&&t.lastScrollLeft-n.scrollLeft<0&&e.timeStamp-t.lastScrollToLowerTime>200&&(r("scrolltolower",e,{direction:"right"}),t.lastScrollToLowerTime=e.timeStamp)),t.lastScrollTop=n.scrollTop,t.lastScrollLeft=n.scrollLeft}function b(t){h.value&&(e.scrollWithAnimation?v(t,"y"):s.value.scrollTop=t)}function w(t){p.value&&(e.scrollWithAnimation?v(t,"x"):s.value.scrollLeft=t)}function _(t){if(t){if(!/^[_a-zA-Z][-_a-zA-Z0-9:]*$/.test(t))return void console.error(`id error: scroll-into-view=${t}`);let n=i.value.querySelector("#"+t);if(n){let t=s.value.getBoundingClientRect(),o=n.getBoundingClientRect();if(p.value){let n=o.left-t.left,r=s.value.scrollLeft+n;e.scrollWithAnimation?v(r,"x"):s.value.scrollLeft=r}if(h.value){let n=o.top-t.top,r=s.value.scrollTop+n;e.scrollWithAnimation?v(r,"y"):s.value.scrollTop=r}}}}function x(e,t){a.value.style.transition="",a.value.style.webkitTransition="",a.value.style.transform="",a.value.style.webkitTransform="";let n=s.value;"x"===t?(n.style.overflowX=p.value?"auto":"hidden",n.scrollLeft=e):"y"===t&&(n.style.overflowY=h.value?"auto":"hidden",n.scrollTop=e),a.value.removeEventListener("transitionend",d),a.value.removeEventListener("webkitTransitionEnd",d)}function S(n){if(e.refresherEnabled){switch(n){case"refreshing":t.refresherHeight=e.refresherThreshold,c||(c=!0,r("refresherpulling",{},{deltaY:t.refresherHeight,dy:t.refresherHeight}),r("refresherrefresh",{},{dy:C.y-T.y}),l("update:refresherTriggered",!0));break;case"restore":case"refresherabort":c=!1,t.refresherHeight=u=0,"restore"===n&&(f=!1,r("refresherrestore",{},{dy:C.y-T.y})),"refresherabort"===n&&f&&(f=!1,r("refresherabort",{},{dy:C.y-T.y}))}t.refreshState=n}}let T={x:0,y:0},C={x:0,y:e.refresherThreshold};return Wo((()=>{On((()=>{b(n.value),w(o.value)})),_(e.scrollIntoView);let i=function(e){e.preventDefault(),e.stopPropagation(),y(e)},a=null,l=function(n){if(null===T)return;let o=n.touches[0].pageX,i=n.touches[0].pageY,l=s.value;if(Math.abs(o-T.x)>Math.abs(i-T.y))if(p.value){if(0===l.scrollLeft&&o>T.x)return void(a=!1);if(l.scrollWidth===l.offsetWidth+l.scrollLeft&&o<T.x)return void(a=!1);a=!0}else a=!1;else if(h.value)if(0===l.scrollTop&&i>T.y)a=!1,e.refresherEnabled&&!1!==n.cancelable&&n.preventDefault();else{if(l.scrollHeight===l.offsetHeight+l.scrollTop&&i<T.y)return void(a=!1);a=!0}else a=!1;if(a&&n.stopPropagation(),0===l.scrollTop&&1===n.touches.length&&S("pulling"),e.refresherEnabled&&"pulling"===t.refreshState){const o=i-T.y;0===u&&(u=i),c?(t.refresherHeight=o+e.refresherThreshold,f=!1):(t.refresherHeight=i-u,t.refresherHeight>0&&(f=!0,r("refresherpulling",n,{deltaY:o,dy:o})))}},d=function(e){1===e.touches.length&&(T={x:e.touches[0].pageX,y:e.touches[0].pageY})},g=function(n){C={x:n.changedTouches[0].pageX,y:n.changedTouches[0].pageY},t.refresherHeight>=e.refresherThreshold?S("refreshing"):S("refresherabort"),T={x:0,y:0},C={x:0,y:e.refresherThreshold}};s.value.addEventListener("touchstart",d,Op),s.value.addEventListener("touchmove",l,Se(!1)),s.value.addEventListener("scroll",i,Se(!1)),s.value.addEventListener("touchend",g,Op),Uo((()=>{s.value.removeEventListener("touchstart",d),s.value.removeEventListener("touchmove",l),s.value.removeEventListener("scroll",i),s.value.removeEventListener("touchend",g)}))})),Po((()=>{h.value&&(s.value.scrollTop=t.lastScrollTop),p.value&&(s.value.scrollLeft=t.lastScrollLeft)})),ro(n,(e=>{b(e)})),ro(o,(e=>{w(e)})),ro((()=>e.scrollIntoView),(e=>{_(e)})),ro((()=>e.refresherTriggered),(e=>{!0===e?S("refreshing"):!1===e&&S("restore")})),{realScrollX:p,realScrollY:h,_scrollTopChanged:b,_scrollLeftChanged:w}}(e,c,u,f,l,r,i,a,t),m=Li((()=>{let e="";return d.value?e+="overflow-x:auto;":e+="overflow-x:hidden;",p.value?e+="overflow-y:auto;":e+="overflow-y:hidden;",e})),v=Li((()=>{let t="uni-scroll-view";return!1===e.showScrollbar&&(t+=" uni-scroll-view-scrollbar-hidden"),t}));return o({$getMain:()=>i.value}),()=>{const{refresherEnabled:t,refresherBackground:o,refresherDefaultStyle:l,refresherThreshold:u}=e,{refresherHeight:f,refreshState:d}=c;return ai("uni-scroll-view",{ref:r},[ai("div",{ref:s,class:"uni-scroll-view"},[ai("div",{ref:i,style:m.value,class:v.value},[t?ai($p,{refreshState:d,refresherHeight:f,refresherThreshold:u,refresherDefaultStyle:l,refresherBackground:o},{default:()=>["none"==l?n.refresher&&n.refresher():null]},8,["refreshState","refresherHeight","refresherThreshold","refresherDefaultStyle","refresherBackground"]):null,ai("div",{ref:a,class:"uni-scroll-view-content"},[n.default&&n.default()],512)],6)],512)],512)}}});const Ap={ensp:" ",emsp:" ",nbsp:" "};function Pp(e,t){return function(e,{space:t,decode:n}){let o="",r=!1;for(let i of e)t&&Ap[t]&&" "===i&&(i=Ap[t]),r?(o+="n"===i?"\n":"\\"===i?"\\":"\\"+i,r=!1):"\\"===i?r=!0:o+=i;return n?o.replace(/&nbsp;/g,Ap.nbsp).replace(/&ensp;/g,Ap.ensp).replace(/&emsp;/g,Ap.emsp).replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&amp;/g,"&").replace(/&quot;/g,'"').replace(/&apos;/g,"'"):o}(e,t).split("\n")}const jp=Xc({name:"Text",props:{selectable:{type:[Boolean,String],default:!1},space:{type:String,default:""},decode:{type:[Boolean,String],default:!1}},setup(e,{slots:t}){const n=un(null);return()=>{const o=[];return t.default&&t.default().forEach((t=>{if(8&t.shapeFlag&&t.type!==qr){const n=Pp(t.children,{space:e.space,decode:e.decode}),r=n.length-1;n.forEach(((e,t)=>{(0!==t||e)&&o.push(ci(e)),t!==r&&o.push(ai("br"))}))}else o.push(t)})),ai("uni-text",{ref:n,selectable:!!e.selectable||null},[ai("span",null,o)],8,["selectable"])}}}),Rp=Xc({name:"View",props:f({},Kc),setup(e,{slots:t}){const n=un(null),{hovering:o,binding:r}=Qc(e);return()=>{const i=e.hoverClass;return i&&"none"!==i?ai("uni-view",hi({class:o.value?i:"",ref:n},r),[Qo(t,"default")],16):ai("uni-view",{ref:n},[Qo(t,"default")],512)}}});function Mp(e,t,n,o){y(t)&&Do(e,t.bind(n),o)}function Ip(e,t,n){const o=e.mpType||n.$mpType;if(o&&"component"!==o&&(Object.keys(e).forEach((o=>{if(function(e,t,n=!0){return!(n&&!y(t))&&(Pe.indexOf(e)>-1||0===e.indexOf("on"))}(o,e[o],!1)){const r=e[o];g(r)?r.forEach((e=>Mp(o,e,n,t))):Mp(o,r,n,t)}})),"page"===o)){t.__isVisible=!0;try{let e=t.attrs.__pageQuery;0,gc(n,"onLoad",e),delete t.attrs.__pageQuery;const o=n.$page;"preloadPage"!==(null==o?void 0:o.openType)&&gc(n,"onShow")}catch(r){console.error(r.message+"\n"+r.stack)}}}function Np(e,t,n){Ip(e,t,n)}function Bp(e,t,n){return e[t]=n}function Fp(e,...t){const n=this[e];return n?n(...t):(console.error(`method ${e} not found`),null)}function Dp(e){const t=e.config.errorHandler;return function(n,o,r){t&&t(n,o,r);const i=e._instance;if(!i||!i.proxy)throw n;i.onError?gc(i.proxy,"onError",n):bn(n,0,o&&o.$.vnode,!1)}}function Hp(e,t){return e?[...new Set([].concat(e,t))]:t}function Vp(e){const t=e.config;var n;t.errorHandler=Re(e,Dp),n=t.optionMergeStrategies,Pe.forEach((e=>{n[e]=Hp}));const o=t.globalProperties;o.$set=Bp,o.$applyOptions=Np,o.$callMethod=Fp,function(e){je.forEach((t=>t(e)))}(e)}function Wp(e){const t=rl({history:Up(),strict:!!__uniConfig.router.strict,routes:__uniRoutes,scrollBehavior:qp});t.beforeEach(((e,t)=>{var n;e&&t&&e.meta.isTabBar&&t.meta.isTabBar&&(n=t.meta.tabBarIndex,"undefined"!=typeof window&&(zp[n]={left:window.pageXOffset,top:window.pageYOffset}))})),e.router=t,e.use(t)}let zp=Object.create(null);const qp=(e,t,n)=>{if(n)return n;if(e&&t&&e.meta.isTabBar&&t.meta.isTabBar){const t=(o=e.meta.tabBarIndex,zp[o]);if(t)return t}return{left:0,top:0};var o};function Up(){let{routerBase:e}=__uniConfig.router;"/"===e&&(e="");const t=(n=e,(n=location.host?n||location.pathname+location.search:"").includes("#")||(n+="#"),ya(n));var n;return t.listen(((e,t,n)=>{"back"===n.direction&&function(e=1){const t=yd(),n=t.length-1,o=n-e;for(let r=n;r>o;r--){const e=ud(t[r]);bd(xd(e.path,e.id),!1)}}(Math.abs(n.delta))})),t}const Gp={install(e){Vp(e),Pc(e),Wc(e),e.config.warnHandler||(e.config.warnHandler=Xp),Wp(e)}};function Xp(e,t,n){if(t){if("PageMetaHead"===t.$.type.name)return;const e=t.$.parent;if(e&&"PageMeta"===e.type.name)return}const o=[`[Vue warn]: ${e}`];n.length&&o.push("\n",n),console.warn(...o)}const Yp={class:"uni-async-loading"},Zp=ai("i",{class:"uni-loading"},null,-1),Jp=Yc({name:"AsyncLoading",render:()=>(Yr(),ei("div",Yp,[Zp]))});function Kp(){window.location.reload()}const Qp=Yc({name:"AsyncError",setup(){bl();const{t:e}=vl();return()=>ai("div",{class:"uni-async-error",onClick:Kp},[e("uni.async.error")],8,["onClick"])}});let eh;function th(){return eh}function nh(e){eh=e,Object.defineProperty(eh.$.ctx,"$children",{get:()=>yd().map((e=>e.$vm))});const t=eh.$.appContext.app;t.component(Jp.name)||t.component(Jp.name,Jp),t.component(Qp.name)||t.component(Qp.name,Qp),function(e){e.$vm=e,e.$mpType="app";const t=un(vl().getLocale());Object.defineProperty(e,"$locale",{get:()=>t.value,set(e){t.value=e}})}(eh),function(e,t){const n=e.$options||{};n.globalData=f(n.globalData||{},t),Object.defineProperty(e,"globalData",{get:()=>n.globalData,set(e){n.globalData=e}})}(eh),Hc(),xc()}function oh(e,{clone:t,init:n,setup:o,before:r}){t&&(e=f({},e)),r&&r(e);const i=e.setup;return e.setup=(e,t)=>{const r=bi();if(n(r.proxy),o(r),i)return i(e,t)},e}function rh(e,t){return e&&(e.__esModule||"Module"===e[Symbol.toStringTag])?oh(e.default,t):oh(e,t)}function ih(e){return rh(e,{clone:!0,init:_d,setup(e){e.$pageInstance=e;const t=cu(),n=ke(t.query);e.attrs.__pageQuery=n,ud(e.proxy).options=n,e.proxy.options=n;const o=au();var r,i;return e.onReachBottom=Gt([]),e.onPageScroll=Gt([]),ro([e.onReachBottom,e.onPageScroll],(()=>{const t=ac();e.proxy===t&&Ld(e,o)}),{once:!0}),Vo((()=>{Cd(e,o)})),Wo((()=>{kd(e);const{onReady:n}=e;n&&I(n),ch(t)})),Ro((()=>{if(!e.__isVisible){Cd(e,o),e.__isVisible=!0;const{onShow:n}=e;n&&I(n),On((()=>{ch(t)}))}}),"ba",r),function(e,t){Ro(e,"bda",t)}((()=>{if(e.__isVisible&&!e.__isUnload){e.__isVisible=!1;{const{onHide:t}=e;t&&I(t)}}})),i=o.id,gg.subscribe(kl(i,"invokeViewApi"),El),Uo((()=>{!function(e){gg.unsubscribe(kl(e,"invokeViewApi")),Object.keys(Cl).forEach((t=>{0===t.indexOf(e+".")&&delete Cl[t]}))}(o.id)})),n}})}function sh(){const{windowWidth:e,windowHeight:t,screenWidth:n,screenHeight:o}=_h(),r=90===Math.abs(Number(window.orientation))?"landscape":"portrait";mg.emit("onResize",{deviceOrientation:r,size:{windowWidth:e,windowHeight:t,screenWidth:n,screenHeight:o}})}function ah(e){C(e.data)&&"WEB_INVOKE_APPSERVICE"===e.data.type&&mg.emit("onWebInvokeAppService",e.data.data,e.data.pageId)}function lh(){const{emit:e}=mg;"visible"===document.visibilityState?e("onAppEnterForeground",f({},Kd)):e("onAppEnterBackground")}function ch(e){const{tabBarText:t,tabBarIndex:n,route:o}=e.meta;t&&gc("onTabItemTap",{index:n,text:t,pagePath:o})}const uh=navigator.cookieEnabled&&(window.localStorage||window.sessionStorage)||{};let fh;function dh(){if(fh=fh||uh.__DC_STAT_UUID,!fh){fh=Date.now()+""+Math.floor(1e7*Math.random());try{uh.__DC_STAT_UUID=fh}catch(e){}}return fh}function ph(){if(!0!==__uniConfig.darkmode)return b(__uniConfig.darkmode)?__uniConfig.darkmode:"light";try{return window.matchMedia("(prefers-color-scheme: light)").matches?"light":"dark"}catch(e){return"light"}}function hh(){let e,t="0",n="",o="phone";const r=navigator.language;if(Md){e="iOS";const o=jd.match(/OS\s([\w_]+)\slike/);o&&(t=o[1].replace(/_/g,"."));const r=jd.match(/\(([a-zA-Z]+);/);r&&(n=r[1])}else if(Rd){e="Android";const o=jd.match(/Android[\s/]([\w\.]+)[;\s]/);o&&(t=o[1]);const r=jd.match(/\((.+?)\)/),i=r?r[1].split(";"):jd.split(" "),s=[/\bAndroid\b/i,/\bLinux\b/i,/\bU\b/i,/^\s?[a-z][a-z]$/i,/^\s?[a-z][a-z]-[a-z][a-z]$/i,/\bwv\b/i,/\/[\d\.,]+$/,/^\s?[\d\.,]+$/,/\bBrowser\b/i,/\bMobile\b/i];for(let e=0;e<i.length;e++){const t=i[e];if(t.indexOf("Build")>0){n=t.split("Build")[0].trim();break}let o;for(let e=0;e<s.length;e++)if(s[e].test(t)){o=!0;break}if(!o){n=t.trim();break}}}else if(Fd){if(n="iPad",e="iOS",o="pad",t=y(window.BigInt)?"14.0":"13.0",14===parseInt(t)){const e=jd.match(/Version\/(\S*)\b/);e&&(t=e[1])}}else if(Id||Nd||Bd){n="PC",e="PC",o="pc",t="0";let r=jd.match(/\((.+?)\)/)[1];if(Id){switch(e="Windows",Id[1]){case"5.1":t="XP";break;case"6.0":t="Vista";break;case"6.1":t="7";break;case"6.2":t="8";break;case"6.3":t="8.1";break;case"10.0":t="10"}const n=r&&r.match(/[Win|WOW]([\d]+)/);n&&(t+=` x${n[1]}`)}else if(Nd){e="macOS";const n=r&&r.match(/Mac OS X (.+)/)||"";t&&(t=n[1].replace(/_/g,"."),-1!==t.indexOf(";")&&(t=t.split(";")[0]))}else if(Bd){e="Linux";const n=r&&r.match(/Linux (.*)/)||"";n&&(t=n[1],-1!==t.indexOf(";")&&(t=t.split(";")[0]))}}else e="Other",t="0",o="unknown";const i=`${e} ${t}`,s=e.toLocaleLowerCase();let a="",l=String(function(){const e=navigator.userAgent,t=e.indexOf("compatible")>-1&&e.indexOf("MSIE")>-1,n=e.indexOf("Edge")>-1&&!t,o=e.indexOf("Trident")>-1&&e.indexOf("rv:11.0")>-1;if(t){new RegExp("MSIE (\\d+\\.\\d+);").test(e);const t=parseFloat(RegExp.$1);return t>6?t:6}return n?-1:o?11:-1}());if("-1"!==l)a="IE";else{const e=["Version","Firefox","Chrome","Edge{0,1}"],t=["Safari","Firefox","Chrome","Edge"];for(let n=0;n<e.length;n++){const o=e[n],r=new RegExp(`(${o})/(\\S*)\\b`);r.test(jd)&&(a=t[n],l=jd.match(r)[2])}}let c="portrait";const u=void 0===window.screen.orientation?window.orientation:window.screen.orientation.angle;return c=90===Math.abs(u)?"landscape":"portrait",{deviceBrand:void 0,brand:void 0,deviceModel:n,deviceOrientation:c,model:n,system:i,platform:s,browserName:a.toLocaleLowerCase(),browserVersion:l,language:r,deviceType:o,ua:jd,osname:e,osversion:t,theme:ph()}}const gh=Du(0,(()=>{const e=window.devicePixelRatio,t=Dd(),n=Hd(t),o=Vd(t,n),r=function(e,t){return e?Math[t?"min":"max"](screen.height,screen.width):screen.height}(t,n),i=Wd(o);let s=window.innerHeight;const a=Xl.top,l={left:Xl.left,right:i-Xl.right,top:Xl.top,bottom:s-Xl.bottom,width:i-Xl.left-Xl.right,height:s-Xl.top-Xl.bottom},{top:c,bottom:u}=Kl();return s-=c,s-=u,{windowTop:c,windowBottom:u,windowWidth:i,windowHeight:s,pixelRatio:e,screenWidth:o,screenHeight:r,statusBarHeight:a,safeArea:l,safeAreaInsets:{top:Xl.top,right:Xl.right,bottom:Xl.bottom,left:Xl.left},screenTop:r-s}}));let mh,vh=!0;function yh(){vh&&(mh=hh())}const bh=Du(0,(()=>{yh();const{deviceBrand:e,deviceModel:t,brand:n,model:o,platform:r,system:i,deviceOrientation:s,deviceType:a,osname:l,osversion:c}=mh;return f({brand:n,deviceBrand:e,deviceModel:t,devicePixelRatio:window.devicePixelRatio,deviceId:dh(),deviceOrientation:s,deviceType:a,model:o,platform:r,system:i,osName:l?l.toLocaleLowerCase():void 0,osVersion:c})})),wh=Du(0,(()=>{yh();const{theme:e,language:t,browserName:n,browserVersion:o}=mh;return f({appId:__uniConfig.appId,appName:__uniConfig.appName,appVersion:__uniConfig.appVersion,appVersionCode:__uniConfig.appVersionCode,appLanguage:_f?_f():t,enableDebug:!1,hostSDKVersion:void 0,hostPackageName:void 0,hostFontSizeSetting:void 0,hostName:n,hostVersion:o,hostTheme:e,hostLanguage:t,language:t,SDKVersion:"",theme:e,version:"",uniPlatform:"web",isUniAppX:!1,uniCompileVersion:__uniConfig.compilerVersion,uniCompilerVersion:__uniConfig.compilerVersion,uniRuntimeVersion:__uniConfig.compilerVersion},{})})),_h=Du(0,(()=>{vh=!0,yh(),vh=!1;const e=gh(),t=bh(),n=wh();vh=!0;const{ua:o,browserName:r,browserVersion:i,osname:s,osversion:a}=mh,l=f(e,t,n,{ua:o,browserName:r,browserVersion:i,uniPlatform:"web",uniCompileVersion:__uniConfig.compilerVersion,uniRuntimeVersion:__uniConfig.compilerVersion,fontSizeSetting:void 0,osName:s.toLocaleLowerCase(),osVersion:a,osLanguage:void 0,osTheme:void 0});return delete l.screenTop,delete l.enableDebug,__uniConfig.darkmode||delete l.theme,function(e){let t={};return C(e)&&Object.keys(e).sort().forEach((n=>{const o=n;t[o]=e[o]})),Object.keys(t)?t:e}(l)})),xh=!!window.navigator.vibrate,Sh=Hu("vibrateShort",((e,{resolve:t,reject:n})=>{xh&&window.navigator.vibrate(15)?t():n("vibrateLong:fail")}));const Th=Du(0,((e,t)=>{const n=typeof t,o="string"===n?t:JSON.stringify({type:n,data:t});localStorage.setItem(e,o)}));function Ch(e){const t=localStorage&&localStorage.getItem(e);if(!b(t))throw new Error("data not found");let n=t;try{const e=function(e){const t=["object","string","number","boolean","undefined"];try{const n=b(e)?JSON.parse(e):e,o=n.type;if(t.indexOf(o)>=0){const e=Object.keys(n);if(2===e.length&&"data"in n){if(typeof n.data===o)return n.data;if("object"===o&&/^\d{4}-\d{2}-\d{2}T\d{2}\:\d{2}\:\d{2}\.\d{3}Z$/.test(n.data))return new Date(n.data)}else if(1===e.length)return""}}catch(n){}}(JSON.parse(t));void 0!==e&&(n=e)}catch(o){}return n}const kh=Du(0,(e=>{try{return Ch(e)}catch(t){return""}})),Eh=Du(0,(e=>{localStorage&&localStorage.removeItem(e)})),$h=Du(0,(()=>{localStorage&&localStorage.clear()})),Oh=Hu("hideKeyboard",((e,{resolve:t,reject:n})=>{const o=document.activeElement;!o||"TEXTAREA"!==o.tagName&&"INPUT"!==o.tagName||(o.blur(),t())})),Lh={image:{jpg:"jpeg",jpe:"jpeg",pbm:"x-portable-bitmap",pgm:"x-portable-graymap",pnm:"x-portable-anymap",ppm:"x-portable-pixmap",psd:"vnd.adobe.photoshop",pic:"x-pict",rgb:"x-rgb",svg:"svg+xml",svgz:"svg+xml",tif:"tiff",xif:"vnd.xiff",wbmp:"vnd.wap.wbmp",wdp:"vnd.ms-photo",xbm:"x-xbitmap",ico:"x-icon"},video:{"3g2":"3gpp2","3gp":"3gpp",avi:"x-msvideo",f4v:"x-f4v",flv:"x-flv",jpgm:"jpm",jpgv:"jpeg",m1v:"mpeg",m2v:"mpeg",mpe:"mpeg",mpg:"mpeg",mpg4:"mpeg",m4v:"x-m4v",mkv:"x-matroska",mov:"quicktime",qt:"quicktime",movie:"x-sgi-movie",mp4v:"mp4",ogv:"ogg",smv:"x-smv",wm:"x-ms-wm",wmv:"x-ms-wmv",wmx:"x-ms-wmx",wvx:"x-ms-wvx"}};function Ah({count:e,sourceType:t,type:n,extension:o}){pp();const r=document.createElement("input");return r.type="file",function(e,t){for(const n in t)e.style[n]=t[n]}(r,{position:"absolute",visibility:"hidden",zIndex:"-999",width:"0",height:"0",top:"0",left:"0"}),r.accept=o.map((e=>{if("all"!==n){const t=e.replace(".","");return`${n}/${Lh[n][t]||t}`}return function(){const e=window.navigator.userAgent.toLowerCase().match(/MicroMessenger/i);return!(!e||"micromessenger"!==e[0])}()?".":0===e.indexOf(".")?e:`.${e}`})).join(","),e&&e>1&&(r.multiple=!0),"all"!==n&&t instanceof Array&&1===t.length&&"camera"===t[0]&&r.setAttribute("capture","camera"),r}let Ph=null;const jh=Hu("chooseImage",(({count:e,sourceType:t,extension:n},{resolve:o,reject:r})=>{xl();const{t:i}=vl();Ph&&(document.body.removeChild(Ph),Ph=null),Ph=Ah({count:e,sourceType:t,extension:n,type:"image"}),document.body.appendChild(Ph),Ph.addEventListener("change",(function(t){const n=t.target,r=[];if(n&&n.files){const t=n.files.length;for(let o=0;o<t;o++){const t=n.files[o];let i;Object.defineProperty(t,"path",{get:()=>(i=i||Zd(t),i)}),o<e&&r.push(t)}}o({get tempFilePaths(){return r.map((({path:e})=>e))},tempFiles:r})})),Ph.click(),up||console.warn(i("uni.chooseFile.notUserActivation"))}),0,Sf),Rh={esc:["Esc","Escape"],enter:["Enter"]},Mh=Object.keys(Rh);function Ih(e,{onEsc:t,onEnter:n}){const o=un(e.visible),{key:r,disable:i}=function(){const e=un(""),t=un(!1),n=n=>{if(t.value)return;const o=Mh.find((e=>-1!==Rh[e].indexOf(n.key)));o&&(e.value=o),On((()=>e.value=""))};return Wo((()=>{document.addEventListener("keyup",n)})),Uo((()=>{document.removeEventListener("keyup",n)})),{key:e,disable:t}}();return ro((()=>e.visible),(e=>o.value=e)),ro((()=>o.value),(e=>i.value=!e)),no((()=>{const{value:e}=r;"esc"===e?t&&t():"enter"===e&&n&&n()})),o}const Nh=Fu("request",(({url:e,data:t,header:n={},method:o,dataType:r,responseType:i,withCredentials:s,timeout:a=__uniConfig.networkTimeout.request},{resolve:l,reject:c})=>{let u=null;const f=function(e){const t=Object.keys(e).find((e=>"content-type"===e.toLowerCase()));if(!t)return;const n=e[t];if(0===n.indexOf("application/json"))return"json";if(0===n.indexOf("application/x-www-form-urlencoded"))return"urlencoded";return"string"}(n);if("GET"!==o)if(b(t)||t instanceof ArrayBuffer)u=t;else if("json"===f)try{u=JSON.stringify(t)}catch(m){u=t.toString()}else if("urlencoded"===f){const e=[];for(const n in t)h(t,n)&&e.push(encodeURIComponent(n)+"="+encodeURIComponent(t[n]));u=e.join("&")}else u=t.toString();const d=new XMLHttpRequest,p=new Bh(d);d.open(o,e);for(const v in n)h(n,v)&&d.setRequestHeader(v,n[v]);const g=setTimeout((function(){d.onload=d.onabort=d.onerror=null,p.abort(),c("timeout",{errCode:5})}),a);return d.responseType=i,d.onload=function(){clearTimeout(g);const e=d.status;let t="text"===i?d.responseText:d.response;if("text"===i&&"json"===r)try{t=JSON.parse(t)}catch(m){}l({data:t,statusCode:e,header:Fh(d.getAllResponseHeaders()),cookies:[]})},d.onabort=function(){clearTimeout(g),c("abort",{errCode:600003})},d.onerror=function(){clearTimeout(g),c(void 0,{errCode:5})},d.withCredentials=s,d.send(u),p}),0,Ef);class Bh{constructor(e){this._xhr=e}abort(){this._xhr&&(this._xhr.abort(),delete this._xhr)}onHeadersReceived(e){throw new Error("Method not implemented.")}offHeadersReceived(e){throw new Error("Method not implemented.")}}function Fh(e){const t={};return e.split("\n").forEach((e=>{const n=e.match(/(\S+\s*):\s*(.*)/);n&&3===n.length&&(t[n[1]]=n[2])})),t}class Dh{constructor(e){this._callbacks=[],this._xhr=e}onProgressUpdate(e){y(e)&&this._callbacks.push(e)}offProgressUpdate(e){const t=this._callbacks.indexOf(e);t>=0&&this._callbacks.splice(t,1)}abort(){this._xhr&&(this._xhr.abort(),delete this._xhr)}onHeadersReceived(e){throw new Error("Method not implemented.")}offHeadersReceived(e){throw new Error("Method not implemented.")}}const Hh=Fu("downloadFile",(({url:e,header:t={},timeout:n=__uniConfig.networkTimeout.downloadFile},{resolve:o,reject:r})=>{var i,s=new XMLHttpRequest,a=new Dh(s);return s.open("GET",e,!0),Object.keys(t).forEach((e=>{s.setRequestHeader(e,t[e])})),s.responseType="blob",s.onload=function(){clearTimeout(i);const t=s.status,n=this.response;let r;const a=s.getResponseHeader("content-disposition");if(a){const e=a.match(/filename="?(\S+)"?\b/);e&&(r=e[1])}n.name=r||function(e){const t=(e=e.split("#")[0].split("?")[0]).split("/");return t[t.length-1]}(e),o({statusCode:t,tempFilePath:Zd(n)})},s.onabort=function(){clearTimeout(i),r("abort",{errCode:600003})},s.onerror=function(){clearTimeout(i),r("",{errCode:602001})},s.onprogress=function(e){a._callbacks.forEach((t=>{var n=e.loaded,o=e.total;t({progress:Math.round(n/o*100),totalBytesWritten:n,totalBytesExpectedToWrite:o})}))},s.send(),i=setTimeout((function(){s.onprogress=s.onload=s.onabort=s.onerror=null,a.abort(),r("timeout",{errCode:5})}),n),a}),0,$f);class Vh{constructor(e){this._callbacks=[],this._xhr=e}onProgressUpdate(e){y(e)&&this._callbacks.push(e)}offProgressUpdate(e){const t=this._callbacks.indexOf(e);t>=0&&this._callbacks.splice(t,1)}abort(){this._isAbort=!0,this._xhr&&(this._xhr.abort(),delete this._xhr)}onHeadersReceived(e){throw new Error("Method not implemented.")}offHeadersReceived(e){throw new Error("Method not implemented.")}}const Wh=Fu("uploadFile",(({url:e,file:t,filePath:n,name:o,files:r,header:i={},formData:s={},timeout:a=__uniConfig.networkTimeout.uploadFile},{resolve:l,reject:c})=>{var u=new Vh;return g(r)&&r.length||(r=[{name:o,file:t,uri:n}]),Promise.all(r.map((({file:e,uri:t})=>e instanceof Blob?Promise.resolve(Yd(e)):Xd(t)))).then((function(t){var n,o=new XMLHttpRequest,f=new FormData;Object.keys(s).forEach((e=>{f.append(e,s[e])})),Object.values(r).forEach((({name:e},n)=>{const o=t[n];f.append(e||"file",o,o.name||`file-${Date.now()}`)})),o.open("POST",e),Object.keys(i).forEach((e=>{o.setRequestHeader(e,i[e])})),o.upload.onprogress=function(e){u._callbacks.forEach((t=>{var n=e.loaded,o=e.total;t({progress:Math.round(n/o*100),totalBytesSent:n,totalBytesExpectedToSend:o})}))},o.onerror=function(){clearTimeout(n),c("",{errCode:602001})},o.onabort=function(){clearTimeout(n),c("abort",{errCode:600003})},o.onload=function(){clearTimeout(n);const e=o.status;l({statusCode:e,data:o.responseText||o.response})},u._isAbort?c("abort",{errCode:600003}):(n=setTimeout((function(){o.upload.onprogress=o.onload=o.onabort=o.onerror=null,u.abort(),c("timeout",{errCode:5})}),a),o.send(f),u._xhr=o)})).catch((()=>{setTimeout((()=>{c("file error")}),0)})),u}),0,Of),zh=[],qh={open:"",close:"",error:"",message:""};class Uh{constructor(e,t,n){let o;this._callbacks={open:[],close:[],error:[],message:[]};try{const n=this._webSocket=new WebSocket(e,t);n.binaryType="arraybuffer";["open","close","error","message"].forEach((e=>{this._callbacks[e]=[],n.addEventListener(e,(t=>{const{data:n,code:o,reason:r}=t,i="message"===e?{data:n}:"close"===e?{code:o,reason:r}:{};if(this._callbacks[e].forEach((t=>{try{t(i)}catch(n){console.error(`thirdScriptError\n${n};at socketTask.on${j(e)} callback function\n`,n)}})),this===zh[0]&&qh[e]&&mg.invokeOnCallback(qh[e],i),"error"===e||"close"===e){const e=zh.indexOf(this);e>=0&&zh.splice(e,1)}}))}));["CLOSED","CLOSING","CONNECTING","OPEN","readyState"].forEach((e=>{Object.defineProperty(this,e,{get:()=>n[e]})}))}catch(r){o=r}n&&n(o,this)}send(e){const t=(e||{}).data,n=this._webSocket;try{if(n.readyState!==n.OPEN)throw fe(e,{errMsg:"sendSocketMessage:fail SocketTask.readyState is not OPEN",errCode:10002}),new Error("SocketTask.readyState is not OPEN");n.send(t),fe(e,"sendSocketMessage:ok")}catch(o){fe(e,{errMsg:`sendSocketMessage:fail ${o}`,errCode:602001})}}close(e={}){const t=this._webSocket;try{const n=e.code||1e3,o=e.reason;b(o)?t.close(n,o):t.close(n),fe(e,"closeSocket:ok")}catch(n){fe(e,`closeSocket:fail ${n}`)}}onOpen(e){this._callbacks.open.push(e)}onMessage(e){this._callbacks.message.push(e)}onError(e){this._callbacks.error.push(e)}onClose(e){this._callbacks.close.push(e)}}const Gh=Fu("connectSocket",(({url:e,protocols:t},{resolve:n,reject:o})=>new Uh(e,t,((e,t)=>{e?o(e.toString(),{errCode:600009}):(zh.push(t),n())}))),0,Lf),Xh=Hu("navigateBack",((e,{resolve:t,reject:n})=>{let o=!0;return!0===gc("onBackPress",{from:e.from||"navigateBack"})&&(o=!1),o?(th().$router.go(-e.delta),t()):n("onBackPress")}),0,Mf),Yh=Hu("navigateTo",(({url:e,events:t,isAutomatedTesting:n},{resolve:o,reject:r})=>{if(fd.handledBeforeEntryPageRoutes)return Qf({type:"navigateTo",url:e,events:t,isAutomatedTesting:n}).then(o).catch(r);dd.push({args:{type:"navigateTo",url:e,events:t,isAutomatedTesting:n},resolve:o,reject:r})}),0,Pf);const Zh={title:{type:String,default:""},icon:{default:"success",validator:e=>-1!==Wf.indexOf(e)},image:{type:String,default:""},duration:{type:Number,default:1500},mask:{type:Boolean,default:!1},visible:{type:Boolean}},Jh={light:"#fff",dark:"rgba(255,255,255,0.9)"},Kh=e=>Jh[e],Qh=So({name:"Toast",props:Zh,setup(e){wl(),_l();const{Icon:t}=function(e){const t=un(Kh(ph())),n=({theme:e})=>t.value=Kh(e);no((()=>{var t;e.visible?(t=n,__uniConfig.darkmode&&mg.on("onThemeChange",t)):function(e){mg.off("onThemeChange",e)}(n)}));return{Icon:Li((()=>{switch(e.icon){case"success":return ai(ic(oc,t.value,38),{class:"uni-toast__icon"});case"error":return ai(ic(rc,t.value,38),{class:"uni-toast__icon"});case"loading":return ai("i",{class:["uni-toast__icon","uni-loading"]},null,2);default:return null}}))}}(e),n=Ih(e,{});return()=>{const{mask:o,duration:r,title:i,image:s}=e;return ai(Bi,{name:"uni-fade"},{default:()=>[co(ai("uni-toast",{"data-duration":r},[o?ai("div",{class:"uni-mask",style:"background: transparent;",onTouchmove:Yl},null,40,["onTouchmove"]):"",s||t.value?ai("div",{class:"uni-toast"},[s?ai("img",{src:s,class:"uni-toast__icon"},null,10,["src"]):t.value,ai("p",{class:"uni-toast__content"},[i])]):ai("div",{class:"uni-sample-toast"},[ai("p",{class:"uni-simple-toast__text"},[i])])],8,["data-duration"]),[[Ki,n.value]])]})}}});let eg,tg,ng="";const og=We();function rg(e){eg?f(eg,e):(eg=Gt(f(e,{visible:!1})),On((()=>{var e,t,n;og.run((()=>{ro([()=>eg.visible,()=>eg.duration],(([e,t])=>{if(e){if(tg&&clearTimeout(tg),"onShowLoading"===ng)return;tg=setTimeout((()=>{cg("onHideToast")}),t)}else tg&&clearTimeout(tg)}))})),mg.on("onHidePopup",(()=>cg("onHidePopup"))),(e=Qh,t=eg,n=()=>{},t.onClose=(...e)=>(t.visible=!1,n.apply(null,e)),Es(So({setup:()=>()=>(Yr(),ei(e,t,null,16))}))).mount(function(e){let t=document.getElementById(e);return t||(t=document.createElement("div"),t.id=e,document.body.append(t)),t}("u-a-t"))}))),setTimeout((()=>{eg.visible=!0}),10)}const ig=Hu("showToast",((e,{resolve:t,reject:n})=>{rg(e),ng="onShowToast",t()}),0,zf),sg={icon:"loading",duration:1e8,image:""},ag=Hu("showLoading",((e,{resolve:t,reject:n})=>{f(e,sg),rg(e),ng="onShowLoading",t()}),0,Vf),lg=Hu("hideLoading",((e,{resolve:t,reject:n})=>{cg("onHideLoading"),t()}));function cg(e){const{t:t}=vl();if(!ng)return;let n="";if("onHideToast"===e&&"onShowToast"!==ng?n=t("uni.showToast.unpaired"):"onHideLoading"===e&&"onShowLoading"!==ng&&(n=t("uni.showLoading.unpaired")),n)return console.warn(n);ng="",setTimeout((()=>{eg.visible=!1}),10)}function ug(e){function t(){var t;t=e.navigationBar.titleText,document.title=t,mg.emit("onNavigationBarChange",{titleText:t})}no(t),Po(t)}const fg=Hu("setNavigationBarTitle",((e,{resolve:t,reject:n})=>{!function(e,t,n,o,r){if(!e)return r("page not found");const{navigationBar:i}=e;switch(t){case"setNavigationBarColor":const{frontColor:e,backgroundColor:t,animation:o}=n,{duration:r,timingFunc:s}=o;e&&(i.titleColor="#000000"===e?"#000000":"#ffffff"),t&&(i.backgroundColor=t),i.duration=r+"ms",i.timingFunc=s;break;case"showNavigationBarLoading":i.loading=!0;break;case"hideNavigationBarLoading":i.loading=!1;break;case"setNavigationBarTitle":const{title:a}=n;i.titleText=a}o()}(lc(),"setNavigationBarTitle",e,t,n)})),dg=Hu("pageScrollTo",(({scrollTop:e,selector:t,duration:n},{resolve:o})=>{!function(e,t,n){if(b(e)){const t=document.querySelector(e);if(t){const{top:n}=t.getBoundingClientRect();e=n+window.pageYOffset;const o=document.querySelector("uni-page-head");o&&(e-=o.offsetHeight)}}e<0&&(e=0);const o=document.documentElement,{clientHeight:r,scrollHeight:i}=o;if(e=Math.min(e,i-r),0===t)return void(o.scrollTop=document.body.scrollTop=e);if(window.scrollY===e)return;const s=t=>{if(t<=0)return void window.scrollTo(0,e);const n=e-window.scrollY;requestAnimationFrame((function(){window.scrollTo(0,window.scrollY+n/t*10),s(t-10)}))};s(t)}(t||e||0,n),o()}),0,Hf),pg=Hu("stopPullDownRefresh",((e,{resolve:t})=>{mg.invokeViewMethod("stopPullDownRefresh",{},cc()),t()})),hg=Yc({name:"Layout",setup(e,{emit:t}){const n=un(null);Ql({"--status-bar-height":"0px","--top-window-height":"0px","--window-left":"0px","--window-right":"0px","--window-margin":"0px","--tab-bar-height":"0px"});const o=function(){const e=il();return{routeKey:Li((()=>xd("/"+e.meta.route,uu()))),isTabBar:Li((()=>e.meta.isTabBar)),routeCache:Td}}(),{layoutState:r,windowState:i}=function(){cu();{const e=Gt({marginWidth:0,leftWindowWidth:0,rightWindowWidth:0});return ro((()=>e.marginWidth),(e=>Ql({"--window-margin":e+"px"}))),ro((()=>e.leftWindowWidth+e.marginWidth),(e=>{Ql({"--window-left":e+"px"})})),ro((()=>e.rightWindowWidth+e.marginWidth),(e=>{Ql({"--window-right":e+"px"})})),{layoutState:e,windowState:Li((()=>({})))}}}();!function(e,t){const n=cu();function o(){const o=document.body.clientWidth,r=yd();let i={};if(r.length>0){i=ud(r[r.length-1]).meta}else{const e=_c(n.path,!0);e&&(i=e.meta)}const s=parseInt(String((h(i,"maxWidth")?i.maxWidth:__uniConfig.globalStyle.maxWidth)||Number.MAX_SAFE_INTEGER));let a=!1;a=o>s,a&&s?(e.marginWidth=(o-s)/2,On((()=>{const e=t.value;e&&e.setAttribute("style","max-width:"+s+"px;margin:0 auto;")}))):(e.marginWidth=0,On((()=>{const e=t.value;e&&e.removeAttribute("style")})))}ro([()=>n.path],o),Wo((()=>{o(),window.addEventListener("resize",o)}))}(r,n);const s=function(e){const t=un(!1);return Li((()=>({"uni-app--showtabbar":e&&e.value,"uni-app--maxwidth":t.value})))}(!1);return()=>{const e=function(e,t,n,o,r,i){return function({routeKey:e,isTabBar:t,routeCache:n}){return ai(ol,null,{default:zn((({Component:o})=>[(Yr(),ei(Lo,{matchBy:"key",cache:n},[(Yr(),ei(Jn(o),{type:t.value?"tabBar":"",key:e.value}))],1032,["cache"]))])),_:1})}(e)}(o);return ai("uni-app",{ref:n,class:s.value},[e,!1],2)}}});const gg=f($l,{publishHandler(e,t,n){mg.subscribeHandler(e,t,n)}}),mg=f(Mc,{publishHandler(e,t,n){gg.subscribeHandler(e,t,n)}}),vg=Yc({name:"PageBody",setup(e,t){const n=un(null),o=un(null);return ro((()=>false.enablePullDownRefresh),(()=>{o.value=null}),{immediate:!0}),()=>ai(Wr,null,[!1,ai("uni-page-wrapper",hi({ref:n},o.value),[ai("uni-page-body",null,[Qo(t.slots,"default")]),null],16)])}}),yg=Yc({name:"Page",setup(e,t){let n=lu(uu());n.navigationBar;const o={};return ug(n),()=>ai("uni-page",{"data-page":n.route,style:o},[bg(t),null])}});function bg(e){return Yr(),ei(vg,{key:0},{default:zn((()=>[Qo(e.slots,"page")])),_:3})}const wg={loading:"AsyncLoading",error:"AsyncError",delay:200,timeout:6e4,suspensible:!0};window.uni={},window.wx={},window.rpx2px=Zu;const _g=Object.assign({}),xg=Object.assign;window.__uniConfig=xg({easycom:{autoscan:!0,custom:{"^uv-(.*)":"@climblee/uv-ui/components/uv-$1/uv-$1.vue"}},globalStyle:{backgroundColor:"#F8F8F8",navigationBar:{backgroundColor:"#F8F8F8",titleText:"uni-app",style:"custom",type:"default",titleColor:"#000000"},isNVue:!1},uniIdRouter:{},compilerVersion:"4.64"},{appId:"__UNI__2D0267D",appName:"KF",appVersion:"1.0.0",appVersionCode:"100",async:wg,debug:!1,networkTimeout:{request:6e4,connectSocket:6e4,uploadFile:6e4,downloadFile:6e4},sdkConfigs:{},qqMapKey:void 0,bMapKey:void 0,googleMapKey:void 0,aMapKey:void 0,aMapSecurityJsCode:void 0,aMapServiceHost:void 0,nvue:{"flex-direction":"column"},locale:"",fallbackLocale:"",locales:Object.keys(_g).reduce(((e,t)=>{const n=t.replace(/\.\/locale\/(uni-app.)?(.*).json/,"$2");return xg(e[n]||(e[n]={}),_g[t].default),e}),{}),router:{mode:"hash",base:"/serve/",assets:"assets",routerBase:"/serve/"},darkmode:!1,themeConfig:{}}),window.__uniLayout=window.__uniLayout||{};const Sg={delay:wg.delay,timeout:wg.timeout,suspensible:wg.suspensible};wg.loading&&(Sg.loadingComponent={name:"SystemAsyncLoading",render:()=>ai(Yn(wg.loading))}),wg.error&&(Sg.errorComponent={name:"SystemAsyncError",render:()=>ai(Yn(wg.error))});const Tg=()=>o((()=>import("./pages-sessionlist-sessionlist.DvKfqeTR.js")),__vite__mapDeps([0,1,2,3,4,5,6,7])).then((e=>ih(e.default||e))),Cg=Co(xg({loader:Tg},Sg)),kg=()=>o((()=>import("./pages-index-index.BmKrNjAd.js")),__vite__mapDeps([8,9,1,2,10,3,4])).then((e=>ih(e.default||e))),Eg=Co(xg({loader:kg},Sg)),$g=()=>o((()=>import("./pages-login-login.C8lsodj-.js")),__vite__mapDeps([11,1,2,9,10,12])).then((e=>ih(e.default||e))),Og=Co(xg({loader:$g},Sg)),Lg=()=>o((()=>import("./pages-chat-chat.89q3wh0r.js")),__vite__mapDeps([13,1,2,5,6,14])).then((e=>ih(e.default||e))),Ag=Co(xg({loader:Lg},Sg));function Pg(e,t){return Yr(),ei(yg,null,{page:zn((()=>[ai(e,xg({},t,{ref:"page"}),null,512)])),_:1})}window.__uniRoutes=[{path:"/",alias:"/pages/sessionlist/sessionlist",component:{setup(){const e=th(),t=e&&e.$route&&e.$route.query||{};return()=>Pg(Cg,t)}},loader:Tg,meta:{isQuit:!0,isEntry:!0,navigationBar:{titleText:"会话列表",type:"default"},isNVue:!1}},{path:"/pages/index/index",component:{setup(){const e=th(),t=e&&e.$route&&e.$route.query||{};return()=>Pg(Eg,t)}},loader:kg,meta:{navigationBar:{titleText:"uni-app",type:"default"},isNVue:!1}},{path:"/pages/login/login",component:{setup(){const e=th(),t=e&&e.$route&&e.$route.query||{};return()=>Pg(Og,t)}},loader:$g,meta:{navigationBar:{titleText:"登录",type:"default"},isNVue:!1}},{path:"/pages/chat/chat",component:{setup(){const e=th(),t=e&&e.$route&&e.$route.query||{};return()=>Pg(Ag,t)}},loader:Lg,meta:{navigationBar:{titleText:"",type:"default"},isNVue:!1}}].map((e=>(e.meta.route=(e.alias||e.path).slice(1),e)));const jg={baseURL:"",timeout:6e5};jg.baseURL="https://www.huohanghang.cn";const Rg={set(e,t){Th(e,t)},get:e=>kh(e),remove(e){Eh(e)},clear(){$h()}};const Mg=function(e,t,n){Yh({url:e,success:e=>{e.eventChannel.emit(t,n)}})},Ig=function(e){uni.$uv.toast(e)},Ng=()=>{let e=1;return e=navigator.userAgent.indexOf("Mobile")>-1?6:5,"micromessenger"==navigator.userAgent.toLowerCase().match(/MicroMessenger/i)&&(e=2),e},Bg={request:(e,t,n,o=!1)=>new Promise(((r,i)=>{o?(console.error(n,name),Wh({url:jg.baseURL+e,filePath:n,name:"file",header:{token:Rg.get("token")||""},success:e=>{let t=JSON.parse(e.data);200==t.code||1==t.code||1204==t.code?r(t):Ig(t.info)},fail:e=>{console.log(e),Ig("上传失败")}})):Nh({url:jg.baseURL+e,data:n,method:t,timeout:jg.timeout,header:{"Content-Type":"application/x-www-form-urlencoded",token:Rg.get("token")||"",UserAuth:"user"},success:e=>{1==e.data.code||100==e.data.code||2==e.data.code||4==e.data.code?r(e.data):401==e.data.code||-1==e.data.code?(Rg.remove("token"),setTimeout((()=>{uni.$uv.route({type:"redirect",url:"/pages/login/login"}),function(e){const t=vd(),n=t[t.length-1].$page.fullPath;Rg.set("losePageUrl",n),Rg.set("isLoseLogin",!0)}()}),1e3),Ig("登录状态失效")):Ig(e.data.msg)},fail(e){console.error(e),i(e)}})}))},Fg=e=>Bg.request("/kefuapi/file/formImage","POST",e,!0),Dg=e=>Bg.request("/kefuapi/account/login","POST",e),Hg=e=>Bg.request("/kefuapi/chat/user","GET",e),Vg=e=>Bg.request("/kefuapi/chat/record","GET",e),Wg=e=>Bg.request("/kefuapi/chat/kefuInfo","GET",e),zg=e=>Bg.request("/kefuapi/chat/userInfo","GET",e),qg=e=>Bg.request("/kefuapi/account/logout","GET",e),Ug=e=>Bg.request("/kefuapi/chat/reply","GET",e);class Gg{constructor(e,n){t(this,"events",{connect:null,close:null,message:null,error:null,open:null}),this.connected=!1,this.error=!1,this.url=`${e}${function(e){let t="";if("object"==typeof e){t="?";for(let n in e)t+=`${n}=${e[n]}&`;t=t.slice(0,-1)}return t}(n)}`,this.socketTask={},this.reconnectLock=!0,this.reconnectTimeout=null,this.reconnectNums=0,this.timeout=1e4,this.clientTimeout=null,this.serverTimeout=null}addEvent(e,t){this.events[e]=t}dispatch(e,t){const n=this.events[e];n&&n(t)}connect(){this.dispatch("connect"),this.socketTask=Gh({url:this.url,complete:e=>{}}),this.socketTask.onOpen(this.onOpen.bind(this)),this.socketTask.onError(this.onError.bind(this)),this.socketTask.onMessage(this.onMessage.bind(this)),this.socketTask.onClose(this.onClose.bind(this))}close(){this.reconnectLock=!1,clearTimeout(this.clientTimeout),clearTimeout(this.serverTimeout),this.socketTask.close&&this.socketTask.close()}reconnect(){this.reconnectLock&&(this.reconnectNums>=5||(this.reconnectNums++,this.reconnectLock=!1,clearTimeout(this.reconnectTimeout),this.reconnectTimeout=setTimeout((()=>{this.connect(),this.reconnectLock=!0}),4e3)))}start(){clearTimeout(this.clientTimeout),clearTimeout(this.serverTimeout),this.clientTimeout=setTimeout((()=>{this.send({event:"ping"}),this.serverTimeout=setTimeout((()=>{this.socketTask.close()}),this.timeout)}),this.timeout)}reset(){this.reconnectNums=0,this.start()}send(e){if(!this.connected)return;let t=JSON.stringify(e);this.socketTask.send({data:t})}onOpen(){this.connected=!0,this.start(),console.log("连接成功"),this.dispatch("open")}onError(e){this.error=!0,this.connected=!1,this.dispatch("error")}onMessage({data:e}){this.dispatch("message",JSON.parse(e)),this.reset()}onClose(e){this.dispatch("close"),this.connected=!1,this.reconnect()}}const Xg={globalData:{config:{},socket:{}},onLaunch:function(){this.chatconfig(),Qu("updateSocket"),Ku("updateSocket",(()=>{this.updateSocket()}))},onShow:function(){console.log("App Show")},onHide:function(){console.log("App Hide")},methods:{updateSocket(){this.init()},async chatconfig(){const{code:e,data:t}=await Bg.request("/kefuapi/chat/config","GET",n);var n;1==e&&(this.globalData.config=t,""!=Rg.get("token")&&(console.log(1,Rg.get("token")),this.updateSocket()))},init(){console.log(1),this.globalData.socket=new Gg(this.globalData.config.ws_domain+"/",{token:Rg.get("token"),type:"kefu",client:Ng(),shop_id:Rg.get("serveinfo").shop_id}),this.globalData.socket.addEvent("connect",(()=>{})),this.globalData.socket.addEvent("open",(()=>{})),this.globalData.socket.addEvent("message",(e=>{switch(e.event){case"login":this.loginEvent(e.data);break;case"chat":this.chatEvent(e.data);break;case"transfer":this.transferEvent(e.data);break;case"error":this.errorEvent(e.data)}})),this.globalData.socket.addEvent("error",(e=>{console.log("error连接失败")})),this.globalData.socket.connect()},loginEvent(e){console.log(e)},chatEvent(e){console.log(e),ef("addChatRecordData",e)},transferEvent(e){console.log(e)},errorEvent(e){console.log(e)}}};rh(Xg,{init:nh,setup(e){const t=cu(),n=()=>{var n;n=e,Object.keys(xf).forEach((e=>{xf[e].forEach((t=>{Do(e,t,n)}))}));const{onLaunch:o,onShow:r,onPageNotFound:i}=e,s=function({path:e,query:t}){return f(Jd,{path:e,query:t}),f(Kd,Jd),f({},Jd)}({path:t.path.slice(1)||__uniRoutes[0].meta.route,query:ke(t.query)});if(o&&I(o,s),r&&I(r,s),!t.matched.length){const e={notFound:!0,openType:"appLaunch",path:t.path,query:{},scene:1001};ed(),i&&I(i,e)}};return Sr(Ua).isReady().then(n),Wo((()=>{window.addEventListener("resize",Oe(sh,50,{setTimeout:setTimeout,clearTimeout:clearTimeout})),window.addEventListener("message",ah),document.addEventListener("visibilitychange",lh),function(){let e=null;try{e=window.matchMedia("(prefers-color-scheme: dark)")}catch(t){}if(e){let t=e=>{mg.emit("onThemeChange",{theme:e.matches?"dark":"light"})};e.addEventListener?e.addEventListener("change",t):e.addListener(t)}}()})),t.query},before(e){e.mpType="app";const{setup:t}=e,n=()=>(Yr(),ei(hg));e.setup=(e,o)=>{const r=t&&t(e,o);return y(r)?n:r},e.render=n}});var Yg=Object.prototype.toString;function Zg(e){return"[object Array]"===Yg.call(e)}function Jg(e,t){if(null!=e)if("object"!=typeof e&&(e=[e]),Zg(e))for(var n=0,o=e.length;n<o;n++)t.call(null,e[n],n,e);else for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.call(null,e[r],r,e)}function Kg(){let e={};function t(t,n){"object"==typeof e[n]&&"object"==typeof t?e[n]=Kg(e[n],t):e[n]="object"==typeof t?Kg({},t):t}for(let n=0,o=arguments.length;n<o;n++)Jg(arguments[n],t);return e}function Qg(e){return void 0===e}function em(e){return encodeURIComponent(e).replace(/%40/gi,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function tm(e,t,n){if(!t)return e;var o,r;if(n)o=n(t);else if(r=t,"undefined"!=typeof URLSearchParams&&r instanceof URLSearchParams)o=t.toString();else{var i=[];Jg(t,(function(e,t){null!=e&&(Zg(e)?t+="[]":e=[e],Jg(e,(function(e){!function(e){return"[object Date]"===Yg.call(e)}(e)?function(e){return null!==e&&"object"==typeof e}(e)&&(e=JSON.stringify(e)):e=e.toISOString(),i.push(em(t)+"="+em(e))})))})),o=i.join("&")}if(o){var s=e.indexOf("#");-1!==s&&(e=e.slice(0,s)),e+=(-1===e.indexOf("?")?"?":"&")+o}return e}const nm=(e,t)=>{let n={};return e.forEach((e=>{Qg(t[e])||(n[e]=t[e])})),n},om=e=>(e=>new Promise(((t,n)=>{let o=tm((r=e.baseURL,i=e.url,r&&!/^([a-z][a-z\d+\-.]*:)?\/\//i.test(i)?function(e,t){return t?e.replace(/\/+$/,"")+"/"+t.replace(/^\/+/,""):e}(r,i):i),e.params,e.paramsSerializer);var r,i;const s={url:o,header:e.header,complete:r=>{e.fullPath=o,r.config=e,r.rawData=r.data;try{let t=!1;const n=typeof e.forcedJSONParsing;"boolean"===n?t=e.forcedJSONParsing:"object"===n&&(t=(e.forcedJSONParsing.include||[]).includes(e.method)),t&&"string"==typeof r.data&&(r.data=JSON.parse(r.data))}catch(i){}!function(e,t,n){const o=n.config.validateStatus,r=n.statusCode;!r||o&&!o(r)?t(n):e(n)}(t,n,r)}};let a;if("UPLOAD"===e.method){delete s.header["content-type"],delete s.header["Content-Type"];let t={filePath:e.filePath,name:e.name};const n=["files","file","timeout","formData"];a=Wh({...s,...t,...nm(n,e)})}else if("DOWNLOAD"===e.method){const t=["timeout"];a=Hh({...s,...nm(t,e)})}else{const t=["data","method","timeout","dataType","responseType","withCredentials"];a=Nh({...s,...nm(t,e)})}e.getTask&&e.getTask(a,e)})))(e);function rm(){this.handlers=[]}rm.prototype.use=function(e,t){return this.handlers.push({fulfilled:e,rejected:t}),this.handlers.length-1},rm.prototype.eject=function(e){this.handlers[e]&&(this.handlers[e]=null)},rm.prototype.forEach=function(e){this.handlers.forEach((t=>{null!==t&&e(t)}))};const im=(e,t,n)=>{let o={};return e.forEach((e=>{Qg(n[e])?Qg(t[e])||(o[e]=t[e]):o[e]=n[e]})),o},sm={baseURL:"",header:{},method:"GET",dataType:"json",paramsSerializer:null,responseType:"text",custom:{},timeout:6e4,withCredentials:!1,validateStatus:function(e){return e>=200&&e<300},forcedJSONParsing:!0};var am=function(){function e(e,t){return null!=t&&e instanceof t}var t,n,o;try{t=Map}catch(a){t=function(){}}try{n=Set}catch(a){n=function(){}}try{o=Promise}catch(a){o=function(){}}function r(i,a,l,c,u){"object"==typeof a&&(l=a.depth,c=a.prototype,u=a.includeNonEnumerable,a=a.circular);var f=[],d=[],p="undefined"!=typeof Buffer;return void 0===a&&(a=!0),void 0===l&&(l=1/0),function i(l,h){if(null===l)return null;if(0===h)return l;var g,m;if("object"!=typeof l)return l;if(e(l,t))g=new t;else if(e(l,n))g=new n;else if(e(l,o))g=new o((function(e,t){l.then((function(t){e(i(t,h-1))}),(function(e){t(i(e,h-1))}))}));else if(r.__isArray(l))g=[];else if(r.__isRegExp(l))g=new RegExp(l.source,s(l)),l.lastIndex&&(g.lastIndex=l.lastIndex);else if(r.__isDate(l))g=new Date(l.getTime());else{if(p&&Buffer.isBuffer(l))return Buffer.from?g=Buffer.from(l):(g=new Buffer(l.length),l.copy(g)),g;e(l,Error)?g=Object.create(l):void 0===c?(m=Object.getPrototypeOf(l),g=Object.create(m)):(g=Object.create(c),m=c)}if(a){var v=f.indexOf(l);if(-1!=v)return d[v];f.push(l),d.push(g)}for(var y in e(l,t)&&l.forEach((function(e,t){var n=i(t,h-1),o=i(e,h-1);g.set(n,o)})),e(l,n)&&l.forEach((function(e){var t=i(e,h-1);g.add(t)})),l){Object.getOwnPropertyDescriptor(l,y)&&(g[y]=i(l[y],h-1));try{if("undefined"===Object.getOwnPropertyDescriptor(l,y).set)continue;g[y]=i(l[y],h-1)}catch(T){if(T instanceof TypeError)continue;if(T instanceof ReferenceError)continue}}if(Object.getOwnPropertySymbols){var b=Object.getOwnPropertySymbols(l);for(y=0;y<b.length;y++){var w=b[y];(!(x=Object.getOwnPropertyDescriptor(l,w))||x.enumerable||u)&&(g[w]=i(l[w],h-1),Object.defineProperty(g,w,x))}}if(u){var _=Object.getOwnPropertyNames(l);for(y=0;y<_.length;y++){var x,S=_[y];(x=Object.getOwnPropertyDescriptor(l,S))&&x.enumerable||(g[S]=i(l[S],h-1),Object.defineProperty(g,S,x))}}return g}(i,l)}function i(e){return Object.prototype.toString.call(e)}function s(e){var t="";return e.global&&(t+="g"),e.ignoreCase&&(t+="i"),e.multiline&&(t+="m"),t}return r.clonePrototype=function(e){if(null===e)return null;var t=function(){};return t.prototype=e,new t},r.__objToStr=i,r.__isDate=function(e){return"object"==typeof e&&"[object Date]"===i(e)},r.__isArray=function(e){return"object"==typeof e&&"[object Array]"===i(e)},r.__isRegExp=function(e){return"object"==typeof e&&"[object RegExp]"===i(e)},r.__getRegExpFlags=s,r}();function lm(e){return/^[\+-]?(\d+\.?\d*|\.\d+|\d\.\d+e\+\d+)$/.test(e)}function cm(e){switch(typeof e){case"undefined":return!0;case"string":if(0==e.replace(/(^[ \t\n\r]*)|([ \t\n\r]*$)/g,"").length)return!0;break;case"boolean":if(!e)return!0;break;case"number":if(0===e||isNaN(e))return!0;break;case"object":if(null===e||0===e.length)return!0;for(const t in e)return!1;return!0}return!1}function um(e){return"function"==typeof Array.isArray?Array.isArray(e):"[object Array]"===Object.prototype.toString.call(e)}function fm(e){return"[object Object]"===Object.prototype.toString.call(e)}function dm(e){return"function"==typeof e}const pm=Object.freeze(Object.defineProperty({__proto__:null,amount:function(e){return/^[1-9]\d*(,\d{3})*(\.\d{1,2})?$|^0\.\d{1,2}$/.test(e)},array:um,carNo:function(e){const t=/^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}(([0-9]{5}[DF]$)|([DF][A-HJ-NP-Z0-9][0-9]{4}$))/,n=/^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-HJ-NP-Z0-9]{4}[A-HJ-NP-Z0-9挂学警港澳]{1}$/;return 7===e.length?n.test(e):8===e.length&&t.test(e)},chinese:function(e){return/^[\u4e00-\u9fa5]+$/gi.test(e)},code:function(e,t=6){return new RegExp(`^\\d{${t}}$`).test(e)},contains:function(e,t){return e.indexOf(t)>=0},date:function(e){return!!e&&(lm(e)&&(e=+e),!/Invalid|NaN/.test(new Date(e).toString()))},dateISO:function(e){return/^\d{4}[\/\-](0?[1-9]|1[012])[\/\-](0?[1-9]|[12][0-9]|3[01])$/.test(e)},digits:function(e){return/^\d+$/.test(e)},email:function(e){return/^\w+((-\w+)|(\.\w+))*\@[A-Za-z0-9]+((\.|-)[A-Za-z0-9]+)*\.[A-Za-z0-9]+$/.test(e)},empty:cm,enOrNum:function(e){return/^[0-9a-zA-Z]*$/g.test(e)},func:dm,idCard:function(e){return/^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}([0-9]|X)$/.test(e)},image:function(e){const t=e.split("?")[0];return/\.(jpeg|jpg|gif|png|svg|webp|jfif|bmp|dpg)/i.test(t)},jsonString:function(e){if("string"==typeof e)try{const t=JSON.parse(e);return!("object"!=typeof t||!t)}catch(t){return!1}return!1},landline:function(e){return/^\d{3,4}-\d{7,8}(-\d{3,4})?$/.test(e)},letter:function(e){return/^[a-zA-Z]*$/.test(e)},mobile:function(e){return/^1([3589]\d|4[5-9]|6[1-2,4-7]|7[0-8])\d{8}$/.test(e)},number:lm,object:fm,promise:function(e){return fm(e)&&dm(e.then)&&dm(e.catch)},range:function(e,t){return e>=t[0]&&e<=t[1]},rangeLength:function(e,t){return e.length>=t[0]&&e.length<=t[1]},regExp:function(e){return e&&"[object RegExp]"===Object.prototype.toString.call(e)},string:function(e){return"string"==typeof e},url:function(e){return/^((https|http|ftp|rtsp|mms):\/\/)(([0-9a-zA-Z_!~*'().&=+$%-]+: )?[0-9a-zA-Z_!~*'().&=+$%-]+@)?(([0-9]{1,3}.){3}[0-9]{1,3}|([0-9a-zA-Z_!~*'()-]+.)*([0-9a-zA-Z][0-9a-zA-Z-]{0,61})?[0-9a-zA-Z].[a-zA-Z]{2,6})(:[0-9]{1,4})?((\/?)|(\/[0-9a-zA-Z_!~*'().;?:@&=+$,%#-]+)+\/?)$/.test(e)},video:function(e){return/\.(mp4|mpg|mpeg|dat|asf|avi|rm|rmvb|mov|wmv|flv|mkv|m3u8)/i.test(e)}},Symbol.toStringTag,{value:"Module"}));function hm(e,t=15){return+parseFloat(Number(e).toPrecision(t))}function gm(e){const t=e.toString().split(/[eE]/),n=(t[0].split(".")[1]||"").length-+(t[1]||0);return n>0?n:0}function mm(e){if(-1===e.toString().indexOf("e"))return Number(e.toString().replace(".",""));const t=gm(e);return t>0?hm(Number(e)*Math.pow(10,t)):Number(e)}function vm(e){(e>Number.MAX_SAFE_INTEGER||e<Number.MIN_SAFE_INTEGER)&&console.warn(`${e} 超出了精度限制，结果可能不正确`)}function ym(e,t){const[n,o,...r]=e;let i=t(n,o);return r.forEach((e=>{i=t(i,e)})),i}function bm(...e){if(e.length>2)return ym(e,bm);const[t,n]=e,o=mm(t),r=mm(n),i=gm(t)+gm(n),s=o*r;return vm(s),s/Math.pow(10,i)}function wm(...e){if(e.length>2)return ym(e,wm);const[t,n]=e,o=mm(t),r=mm(n);return vm(o),vm(r),bm(o/r,hm(Math.pow(10,gm(n)-gm(t))))}function _m(e){let t=this.$parent;for(;t;){if(!t.$options||t.$options.name===e)return t;t=t.$parent}return!1}function xm(e,t=new WeakMap){if(null===e||"object"!=typeof e)return e;if(t.has(e))return t.get(e);let n;if(e instanceof Date)n=new Date(e.getTime());else if(e instanceof RegExp)n=new RegExp(e);else if(e instanceof Map)n=new Map(Array.from(e,(([e,n])=>[e,xm(n,t)])));else if(e instanceof Set)n=new Set(Array.from(e,(e=>xm(e,t))));else if(Array.isArray(e))n=e.map((e=>xm(e,t)));else if("[object Object]"===Object.prototype.toString.call(e)){n=Object.create(Object.getPrototypeOf(e)),t.set(e,n);for(const[o,r]of Object.entries(e))n[o]=xm(r,t)}else n=Object.assign({},e);return t.set(e,n),n}function Sm(e={},t={}){if("object"!=typeof(e=xm(e))||null===e||"object"!=typeof t||null===t)return e;const n=Array.isArray(e)?e.slice():Object.assign({},e);for(const o in t){if(!t.hasOwnProperty(o))continue;const e=t[o],r=n[o];e instanceof Date?n[o]=new Date(e):e instanceof RegExp?n[o]=new RegExp(e):e instanceof Map?n[o]=new Map(e):e instanceof Set?n[o]=new Set(e):n[o]="object"==typeof e&&null!==e?Sm(r,e):e}return n}function Tm(e=null,t="yyyy-mm-dd"){let n;n=e?/^\d{10}$/.test(null==e?void 0:e.toString().trim())?new Date(1e3*e):"string"==typeof e&&/^\d+$/.test(e.trim())?new Date(Number(e)):"string"==typeof e&&e.includes("-")&&!e.includes("T")?new Date(e.replace(/-/g,"/")):new Date(e):new Date;const o={y:n.getFullYear().toString(),m:(n.getMonth()+1).toString().padStart(2,"0"),d:n.getDate().toString().padStart(2,"0"),h:n.getHours().toString().padStart(2,"0"),M:n.getMinutes().toString().padStart(2,"0"),s:n.getSeconds().toString().padStart(2,"0")};for(const r in o){const[e]=new RegExp(`${r}+`).exec(t)||[];if(e){const n="y"===r&&2===e.length?2:0;t=t.replace(e,o[r].slice(n))}}return t}function Cm(e,t="both"){return e=String(e),"both"==t?e.replace(/^\s+|\s+$/g,""):"left"==t?e.replace(/^\s*/,""):"right"==t?e.replace(/(\s*$)/g,""):"all"==t?e.replace(/\s+/g,""):e}function km(e={},t=!0,n="brackets"){const o=t?"?":"",r=[];-1==["indices","brackets","repeat","comma"].indexOf(n)&&(n="brackets");for(const i in e){const t=e[i];if(!(["",void 0,null].indexOf(t)>=0))if(t.constructor===Array)switch(n){case"indices":for(let n=0;n<t.length;n++)r.push(`${i}[${n}]=${t[n]}`);break;case"brackets":default:t.forEach((e=>{r.push(`${i}[]=${e}`)}));break;case"repeat":t.forEach((e=>{r.push(`${i}=${e}`)}));break;case"comma":let e="";t.forEach((t=>{e+=(e?",":"")+t})),r.push(`${i}=${e}`)}else r.push(`${i}=${t}`)}return r.length?o+r.join("&"):""}function Em(){var e;const t=vd(),n=null==(e=t[t.length-1])?void 0:e.route;return`/${n||""}`}String.prototype.padStart||(String.prototype.padStart=function(e,t=" "){if("[object String]"!==Object.prototype.toString.call(t))throw new TypeError("fillString must be String");const n=this;if(n.length>=e)return String(n);const o=e-n.length;let r=Math.ceil(o/t.length);for(;r>>=1;)t+=t,1===r&&(t+=t);return t.slice(0,o)+n});const $m=Object.freeze(Object.defineProperty({__proto__:null,$parent:_m,addStyle:function(e,t="object"){if(cm(e)||"object"==typeof e&&"object"===t||"string"===t&&"string"==typeof e)return e;if("object"===t){const t=(e=Cm(e)).split(";"),n={};for(let e=0;e<t.length;e++)if(t[e]){const o=t[e].split(":");n[Cm(o[0])]=Cm(o[1])}return n}let n="";for(const o in e){n+=`${o.replace(/([A-Z])/g,"-$1").toLowerCase()}:${e[o]};`}return Cm(n)},addUnit:function(e="auto",t=((e=>{return null==(e=null==(t=null==uni?void 0:uni.$uv)?void 0:t.config)?void 0:e.unit;var t})()?(e=>{return null==(e=null==(t=null==uni?void 0:uni.$uv)?void 0:t.config)?void 0:e.unit;var t})():"px")){return lm(e=String(e))?`${e}${t}`:e},deepClone:xm,deepMerge:Sm,error:function(e){},formValidate:function(e,t){const n=_m.call(e,"uv-form-item"),o=_m.call(e,"uv-form");n&&o&&o.validateField(n.prop,(()=>{}),t)},getDuration:function(e,t=!0){const n=parseInt(e);return t?/s$/.test(e)?e:e>30?`${e}ms`:`${e}s`:/ms$/.test(e)?n:/s$/.test(e)?n>30?n:1e3*n:n},getHistoryPage:function(e=0){const t=vd();return t[t.length-1+e]},getProperty:function(e,t){if(e){if("string"!=typeof t||""===t)return"";if(-1!==t.indexOf(".")){const n=t.split(".");let o=e[n[0]]||{};for(let e=1;e<n.length;e++)o&&(o=o[n[e]]);return o}return e[t]}},getPx:function(e,t=!1){return lm(e)?t?`${e}px`:Number(e):/(rpx|upx)$/.test(e)?t?`${Zu(parseInt(e))}px`:Number(Zu(parseInt(e))):t?`${parseInt(e)}px`:parseInt(e)},guid:function(e=32,t=!0,n=null){const o="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz".split(""),r=[];if(n=n||o.length,e)for(let i=0;i<e;i++)r[i]=o[0|Math.random()*n];else{let e;r[8]=r[13]=r[18]=r[23]="-",r[14]="4";for(let t=0;t<36;t++)r[t]||(e=0|16*Math.random(),r[t]=o[19==t?3&e|8:e])}return t?(r.shift(),`u${r.join("")}`):r.join("")},os:function(){return _h().platform.toLowerCase()},padZero:function(e){return`00${e}`.slice(-2)},page:Em,pages:function(){return vd()},priceFormat:function(e,t=0,n=".",o=","){e=`${e}`.replace(/[^0-9+-Ee.]/g,"");const r=isFinite(+e)?+e:0,i=isFinite(+t)?Math.abs(t):0,s=void 0===o?",":o,a=void 0===n?".":n;let l="";l=(i?function(e,t){const n=Math.pow(10,t);let o=wm(Math.round(Math.abs(bm(e,n))),n);return e<0&&0!==o&&(o=bm(o,-1)),o}(r,i)+"":`${Math.round(r)}`).split(".");const c=/(-?\d+)(\d{3})/;for(;c.test(l[0]);)l[0]=l[0].replace(c,`$1${s}$2`);return(l[1]||"").length<i&&(l[1]=l[1]||"",l[1]+=new Array(i-l[1].length+1).join("0")),l.join(a)},queryParams:km,random:function(e,t){if(e>=0&&t>0&&t>=e){const n=t-e+1;return Math.floor(Math.random()*n+e)}return 0},randomArray:function(e=[]){return e.sort((()=>Math.random()-.5))},range:function(e=0,t=0,n=0){return Math.max(e,Math.min(t,Number(n)))},setConfig:function({props:e={},config:t={},color:n={},zIndex:o={}}){const{deepMerge:r}=uni.$uv;uni.$uv.config=r(uni.$uv.config,t),uni.$uv.props=r(uni.$uv.props,e),uni.$uv.color=r(uni.$uv.color,n),uni.$uv.zIndex=r(uni.$uv.zIndex,o)},setProperty:function(e,t,n){if(!e)return;const o=function(e,t,n){if(1!==t.length)for(;t.length>1;){const r=t[0];e[r]&&"object"==typeof e[r]||(e[r]={}),t.shift(),o(e[r],t,n)}else e[t[0]]=n};if("string"!=typeof t||""===t);else if(-1!==t.indexOf(".")){const r=t.split(".");o(e,r,n)}else e[t]=n},sleep:function(e=30){return new Promise((t=>{setTimeout((()=>{t()}),e)}))},sys:function(){return _h()},timeFormat:Tm,timeFrom:function(e=null,t="yyyy-mm-dd"){null==e&&(e=Number(new Date)),10==(e=parseInt(e)).toString().length&&(e*=1e3);let n=(new Date).getTime()-e;n=parseInt(n/1e3);let o="";switch(!0){case n<300:o="刚刚";break;case n>=300&&n<3600:o=`${parseInt(n/60)}分钟前`;break;case n>=3600&&n<86400:o=`${parseInt(n/3600)}小时前`;break;case n>=86400&&n<2592e3:o=`${parseInt(n/86400)}天前`;break;default:o=!1===t?n>=2592e3&&n<31536e3?`${parseInt(n/2592e3)}个月前`:`${parseInt(n/31536e3)}年前`:Tm(e,t)}return o},toast:function(e,t=2e3){ig({title:String(e),icon:"none",duration:t})},trim:Cm,type2icon:function(e="success",t=!1){-1==["primary","info","error","warning","success"].indexOf(e)&&(e="success");let n="";switch(e){case"primary":case"info":n="info-circle";break;case"error":n="close-circle";break;case"warning":n="error-circle";break;default:n="checkmark-circle"}return t&&(n+="-fill"),n}},Symbol.toStringTag,{value:"Module"}));const Om=(new class{constructor(){this.config={type:"navigateTo",url:"",delta:1,params:{},animationType:"pop-in",animationDuration:300,intercept:!1,events:{}},this.route=this.route.bind(this)}addRootPath(e){return"/"===e[0]?e:`/${e}`}mixinParam(e,t){e=e&&this.addRootPath(e);let n="";return/.*\/.*\?.*=.*/.test(e)?(n=km(t,!1),e+`&${n}`):(n=km(t),e+n)}async route(e={},t={}){let n={};if("string"==typeof e?(n.url=this.mixinParam(e,t),n.type="navigateTo"):(n=Sm(this.config,e),n.url=this.mixinParam(e.url,e.params)),n.url!==Em())if(t.intercept&&(n.intercept=t.intercept),n.params=t,n=Sm(this.config,n),"function"==typeof n.intercept){await new Promise(((e,t)=>{n.intercept(n,e)}))&&this.openPage(n)}else this.openPage(n)}openPage(e){const{url:t,type:n,delta:o,animationType:r,animationDuration:i,events:s}=e;"navigateTo"!=e.type&&"to"!=e.type||Yh({url:t,animationType:r,animationDuration:i,events:s}),"redirectTo"!=e.type&&"redirect"!=e.type||Zf({url:t}),"switchTab"!=e.type&&"tab"!=e.type||Xf({url:t}),"reLaunch"!=e.type&&"launch"!=e.type||Kf({url:t}),"navigateBack"!=e.type&&"back"!=e.type||Xh({delta:o})}}).route;let Lm,Am=null;function Pm(e,t=500,n=!1){if(null!==Am&&clearTimeout(Am),n){const n=!Am;Am=setTimeout((()=>{Am=null}),t),n&&"function"==typeof e&&e()}else Am=setTimeout((()=>{"function"==typeof e&&e()}),t)}function jm(e,t=500,n=!0){n?Lm||(Lm=!0,"function"==typeof e&&e(),setTimeout((()=>{Lm=!1}),t)):Lm||(Lm=!0,setTimeout((()=>{Lm=!1,"function"==typeof e&&e()}),t))}const Rm={props:{customStyle:{type:[Object,String],default:()=>({})},customClass:{type:String,default:""},url:{type:String,default:""},linkType:{type:String,default:"navigateTo"}},data:()=>({}),onLoad(){this.$uv.getRect=this.$uvGetRect},created(){this.$uv.getRect=this.$uvGetRect},computed:{$uv(){var e,t;return{...$m,test:pm,route:Om,debounce:Pm,throttle:jm,unit:null==(t=null==(e=null==uni?void 0:uni.$uv)?void 0:e.config)?void 0:t.unit}},bem:()=>function(e,t,n){const o=`uv-${e}--`,r={};return t&&t.map((e=>{r[o+this[e]]=!0})),n&&n.map((e=>{this[e]?r[o+e]=this[e]:delete r[o+e]})),Object.keys(r)}},methods:{openPage(e="url"){const t=this[e];t&&uni[this.linkType]({url:t})},$uvGetRect(e,t){return new Promise((n=>{gf().in(this)[t?"selectAll":"select"](e).boundingClientRect((e=>{t&&Array.isArray(e)&&e.length&&n(e),!t&&e&&n(e)})).exec()}))},getParentData(e=""){this.parent||(this.parent={}),this.parent=this.$uv.$parent.call(this,e),this.parent.children&&-1===this.parent.children.indexOf(this)&&this.parent.children.push(this),this.parent&&this.parentData&&Object.keys(this.parentData).map((e=>{this.parentData[e]=this.parent[e]}))},preventEvent(e){e&&"function"==typeof e.stopPropagation&&e.stopPropagation()},noop(e){this.preventEvent(e)}},onReachBottom(){ef("uvOnReachBottom")},beforeDestroy(){if(this.parent&&um(this.parent.children)){const e=this.parent.children;e.map(((t,n)=>{t===this&&e.splice(n,1)}))}},unmounted(){if(this.parent&&um(this.parent.children)){const e=this.parent.children;e.map(((t,n)=>{t===this&&e.splice(n,1)}))}}},Mm={};function Im(e="rgb(0, 0, 0)",t="rgb(255, 255, 255)",n=10){const o=Nm(e,!1),r=o[0],i=o[1],s=o[2],a=Nm(t,!1),l=(a[0]-r)/n,c=(a[1]-i)/n,u=(a[2]-s)/n,f=[];for(let d=0;d<n;d++){let o=Bm(`rgb(${Math.round(l*d+r)},${Math.round(c*d+i)},${Math.round(u*d+s)})`);0===d&&(o=Bm(e)),d===n-1&&(o=Bm(t)),f.push(o)}return f}function Nm(e,t=!0){if((e=String(e).toLowerCase())&&/^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/.test(e)){if(4===e.length){let t="#";for(let n=1;n<4;n+=1)t+=e.slice(n,n+1).concat(e.slice(n,n+1));e=t}const n=[];for(let t=1;t<7;t+=2)n.push(parseInt(`0x${e.slice(t,t+2)}`));return t?`rgb(${n[0]},${n[1]},${n[2]})`:n}if(/^(rgb|RGB)/.test(e)){return e.replace(/(?:\(|\)|rgb|RGB)*/g,"").split(",").map((e=>Number(e)))}return e}function Bm(e){const t=e;if(/^(rgb|RGB)/.test(t)){const e=t.replace(/(?:\(|\)|rgb|RGB)*/g,"").split(",");let n="#";for(let t=0;t<e.length;t++){let o=Number(e[t]).toString(16);o=1==String(o).length?`0${o}`:o,"0"===o&&(o+=o),n+=o}return 7!==n.length&&(n=t),n}if(!/^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/.test(t))return t;{const e=t.replace(/#/,"").split("");if(6===e.length)return t;if(3===e.length){let t="#";for(let n=0;n<e.length;n+=1)t+=e[n]+e[n];return t}}}let Fm="none";Fm="vue3",Fm="h5";const Dm={route:Om,config:{v:"1.1.20",version:"1.1.20",type:["primary","success","info","error","warning"],color:{"uv-primary":"#2979ff","uv-warning":"#ff9900","uv-success":"#19be6b","uv-error":"#fa3534","uv-info":"#909399","uv-main-color":"#303133","uv-content-color":"#606266","uv-tips-color":"#909399","uv-light-color":"#c0c4cc"},unit:"px"},test:pm,date:Tm,...$m,colorGradient:Im,hexToRgb:Nm,rgbToHex:Bm,colorToRgba:function(e,t){e=Bm(e);let n=String(e).toLowerCase();if(n&&/^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/.test(n)){if(4===n.length){let e="#";for(let t=1;t<4;t+=1)e+=n.slice(t,t+1).concat(n.slice(t,t+1));n=e}const e=[];for(let t=1;t<7;t+=2)e.push(parseInt(`0x${n.slice(t,t+2)}`));return`rgba(${e.join(",")},${t})`}return n},http:new class{constructor(e={}){var t;t=e,"[object Object]"!==Object.prototype.toString.call(t)&&(e={},console.warn("设置全局参数必须接收一个Object")),this.config=am({...sm,...e}),this.interceptors={request:new rm,response:new rm}}setConfig(e){this.config=e(this.config)}middleware(e){e=((e,t={})=>{const n=t.method||e.method||"GET";let o={baseURL:t.baseURL||e.baseURL||"",method:n,url:t.url||"",params:t.params||{},custom:{...e.custom||{},...t.custom||{}},header:Kg(e.header||{},t.header||{})};if(o={...o,...im(["getTask","validateStatus","paramsSerializer","forcedJSONParsing"],e,t)},"DOWNLOAD"===n){const n=["timeout"];o={...o,...im(n,e,t)}}else if("UPLOAD"===n)delete o.header["content-type"],delete o.header["Content-Type"],["files","file","filePath","name","timeout","formData"].forEach((e=>{Qg(t[e])||(o[e]=t[e])})),Qg(o.timeout)&&!Qg(e.timeout)&&(o.timeout=e.timeout);else{const n=["data","timeout","dataType","responseType","withCredentials"];o={...o,...im(n,e,t)}}return o})(this.config,e);let t=[om,void 0],n=Promise.resolve(e);for(this.interceptors.request.forEach((function(e){t.unshift(e.fulfilled,e.rejected)})),this.interceptors.response.forEach((function(e){t.push(e.fulfilled,e.rejected)}));t.length;)n=n.then(t.shift(),t.shift());return n}request(e={}){return this.middleware(e)}get(e,t={}){return this.middleware({url:e,method:"GET",...t})}post(e,t,n={}){return this.middleware({url:e,data:t,method:"POST",...n})}put(e,t,n={}){return this.middleware({url:e,data:t,method:"PUT",...n})}delete(e,t,n={}){return this.middleware({url:e,data:t,method:"DELETE",...n})}connect(e,t,n={}){return this.middleware({url:e,data:t,method:"CONNECT",...n})}head(e,t,n={}){return this.middleware({url:e,data:t,method:"HEAD",...n})}options(e,t,n={}){return this.middleware({url:e,data:t,method:"OPTIONS",...n})}trace(e,t,n={}){return this.middleware({url:e,data:t,method:"TRACE",...n})}upload(e,t={}){return t.url=e,t.method="UPLOAD",this.middleware(t)}download(e,t={}){return t.url=e,t.method="DOWNLOAD",this.middleware(t)}get version(){return"3.1.0"}},debounce:Pm,throttle:jm,platform:"h5",mixin:Rm,mpMixin:Mm};uni.$uv=Dm;function Hm(){return th()}function Vm(){return Hm()&&Hm().globalData}function Wm(e,t){try{setTimeout((function(){Vm()&&(Hm().globalData[`zp_handle${e}Callback`]=t)}),1)}catch(n){}}function zm(e){return Vm()?Hm().globalData[`zp_handle${e}Callback`]:null}const qm={handleQuery:function(e){return Wm("Query",e),this},_handleQuery:function(e,t,n,o){const r=zm("Query");return r?r(e,t,n,o):[e,t,n]},handleFetchParams:function(e){return Wm("FetchParams",e),this},_handleFetchParams:function(e,t){const n=zm("FetchParams");return n?n(e,t||{}):{pageNo:e.pageNo,pageSize:e.pageSize,...t||{}}},handleFetchResult:function(e){return Wm("FetchResult",e),this},_handleFetchResult:function(e,t,n){const o=zm("FetchResult");return o&&o(e,t,n),!!o},handleLanguage2Local:function(e){return Wm("Language2Local",e),this},_handleLanguage2Local:function(e,t){const n=zm("Language2Local");return n?n(e,t):t}};qm.handleFetchParams(((e,t)=>({pageNo:e.pageNo,pageSize:e.pageSize,...t}))).handleFetchResult(((e,t)=>{e.then((e=>{t.complete(e.data.list)})).catch((e=>{t.complete(!1)}))})),Es(Xg).use(Gp).mount("#app");export{ef as $,Ts as A,Qo as B,co as C,Ki as D,Ig as E,Wr as F,Ng as G,Dg as H,Ep as I,Mm as J,Rm as K,Im as L,vd as M,jp as N,jm as O,jh as P,Oh as Q,wf as R,Lp as S,Yn as T,Qu as U,Ku as V,zg as W,fg as X,Ug as Y,Vg as Z,th as _,ai as a,Fg as a0,Th as a1,kh as a2,Zu as a3,sd as a4,_h as a5,qm as a6,hl as a7,_f as a8,ag as a9,lg as aa,pg as ab,dg as ac,Ci as ad,Do as ae,bi as af,re as ag,ie as ah,se as ai,ae as aj,ci as b,ei as c,ve as d,Wg as e,Jn as f,Hg as g,ui as h,Rp as i,Qr as j,Ko as k,Rg as l,qg as m,ye as n,Yr as o,Kf as p,sp as q,un as r,Mg as s,Y as t,pn as u,Sh as v,zn as w,Gh as x,iu as y,gf as z};
