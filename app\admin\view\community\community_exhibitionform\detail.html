{layout name="layout2" /}
<style>
    .div-flex {
        display: flex;
        align-items: center;
        justify-content: left;
    }
    .width-160 {
        width: 200px;
    }
    .image {
        height: 60px;
        width: 60px;
        margin-right: 5px;
    }
</style>

<div class="layui-card-body">
    <!--基本信息-->
    <div class="layui-form" lay-filter="layuiadmin-form-order" id="layuiadmin-form-order">
        <input type="hidden" id="article_id" name="id" value="{$detail.id}">

        <div class="layui-form-item">
            <fieldset class="layui-elem-field layui-field-title">
                <legend>用户信息</legend>
            </fieldset>
        </div>

        <div class="layui-form-item div-flex">
            <label class="layui-form-label ">昵称:</label>
            <div class="width-160">{$detail.nickname}</div>
            <label class="layui-form-label ">编号:</label>
            <div class="width-160">{$detail.sn}</div>
        </div>

        <div class="layui-form-item">
            <fieldset class="layui-elem-field layui-field-title">
                <legend>内容</legend>
            </fieldset>
        </div>

        <div class="layui-form-item div-flex">
            <label class="layui-form-label ">登记类型:</label>
            <div class="width-160">{$detail.type_id}</div>
            <label class="layui-form-label ">性别:</label>
            <div class="width-160">{$detail.sex}</div>

        </div>
        <div class="layui-form-item div-flex">
            <label class="layui-form-label ">手机号码:</label>
            <div class="width-160">{$detail.phone}</div>
            <label class="layui-form-label ">公司名称:</label>
            <div class="width-160">{$detail.company}</div>

        </div>
        <div class="layui-form-item div-flex">

            <label class="layui-form-label ">展会标题:</label>
            <div class="width-160">{$detail.title | default= ''}</div>
            <label class="layui-form-label ">提交时间:</label>
            <div class="width-160">{$detail.create_time}</div>
        </div>
        </div>



    </div>
</div>

<script type="text/javascript">
    layui.config({
        version: "{$front_version}",
        base: '/static/lib/'
    }).use(['form', 'table', 'element'], function () {
        var $ = layui.$;
        var table = layui.table;
        var element = layui.element;

        //主图放大
        $(document).on('click', '.image-show', function () {
            var src = $(this).attr('src');
            like.showImg(src, 400);
        });

        //获取列表
        getList('comment');
        //切换列表
        element.on('tab(tab-all)', function (data) {
            var type = $(this).attr('data-type');
            getList(type);
        });
        $(document).on('click', '.preview-all', function (data) {
            // var obj = data.target.dataset;
            let clickObject = data.target; //点击的对象
            let url = clickObject.src; //图片、视频 地址
            previewVideo(url);
        });
        //视频预览，传url,width,height
        function previewVideo(url, width, height) {
            width = width ? width : '65%';
            height = height ? height : '65%';
            let content = '<video width="100%" height="90%"  controls="controls" autobuffer="autobuffer"  autoplay="autoplay" loop="loop">' +
                '<source src="' + url + '" type="video/mp4"></source></video>';
            layer.open({
                type: 1,
                maxmin: true, //打开放大缩小按钮
                title: '视频播放',
                area: [width, height],
                content: content,
            });
        }
        function getList(type) {
            var cols = [
                {field: 'user', title: '用户信息', align: 'center', templet: "#table-userInfo"}
                , {field: 'create_time', title: '点赞时间', align: 'center'}
            ];
            if (type === 'comment') {
                cols = [
                    {field: 'user', title: '评论用户', align: 'center', templet: "#table-userInfo"}
                    , {field: 'comment', title: '评论内容', align: 'center'}
                    , {field: 'create_time', title: '评价时间', align: 'center'}
                ];
            }
            var id = $('#article_id').val();
            like.tableLists("#like-table-lists", '{:url("community.CommunityArticle/detail")}?type='+ type + '&id=' + id, cols);
        }


    });
</script>