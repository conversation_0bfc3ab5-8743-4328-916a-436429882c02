<?php
// 数据库配置
$host = '*************';
$user = 'kshop';
$password = 'DetPwbd6YrtMasHf';
$database = 'kshop';

// 连接数据库
$conn = new mysqli($host, $user, $password, $database);

// 检查连接
if ($conn->connect_error) {
    die("连接失败: " . $conn->connect_error);
}

echo "数据库连接成功\n";

// 检查 unionid 字段是否存在
$result = $conn->query("SHOW COLUMNS FROM ls_shop_admin LIKE 'unionid'");
if ($result->num_rows == 0) {
    echo "unionid 字段不存在，添加中...\n";
    $sql = "ALTER TABLE `ls_shop_admin` ADD COLUMN `unionid` varchar(32) DEFAULT '' COMMENT '微信unionid'";
    if ($conn->query($sql) === TRUE) {
        echo "unionid 字段添加成功\n";
    } else {
        echo "添加 unionid 字段失败: " . $conn->error . "\n";
    }
} else {
    echo "unionid 字段已存在\n";
}

// 检查 oa_openid 字段是否存在
$result = $conn->query("SHOW COLUMNS FROM ls_shop_admin LIKE 'oa_openid'");
if ($result->num_rows == 0) {
    echo "oa_openid 字段不存在，添加中...\n";
    $sql = "ALTER TABLE `ls_shop_admin` ADD COLUMN `oa_openid` varchar(32) DEFAULT '' COMMENT '微信公众号openid'";
    if ($conn->query($sql) === TRUE) {
        echo "oa_openid 字段添加成功\n";
    } else {
        echo "添加 oa_openid 字段失败: " . $conn->error . "\n";
    }
} else {
    echo "oa_openid 字段已存在\n";
}

$conn->close();
echo "操作完成\n";
?>
