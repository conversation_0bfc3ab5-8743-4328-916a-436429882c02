{layout name="layout2" /}
<style>
    .div-flex {
        display: flex;
        align-items: center;
        justify-content: left;
    }

    .width-160 {
        width: 200px;
    }

    .layui-table th {
        text-align: center;
    }

    .table-margin {
        margin-left: 50px;
        margin-right: 50px;
        text-align: center;
    }

    .image {
        height: 80px;
        width: 80px;
    }

    .mt50 {
        margin-left: 50px;
    }

</style>
<div class="layui-card-body" >
    <!--基本信息-->
    <div class="layui-form" lay-filter="layuiadmin-form-express" id="layuiadmin-form-express" >
        <input type="hidden" name="order_id" value="{$detail.id}">

        <div class="layui-form-item">
            <fieldset class="layui-elem-field layui-field-title">
                <legend>快递信息</legend>
            </fieldset>
        </div>


        {empty name="$detail.shipping"}
            <div class="layui-form-item div-flex">
                <label class="layui-form-label"></label>
                <div class="width-160">暂无物流信息</div>
            </div>
        {else/}
            <div class="layui-form-item div-flex">
                <label class="layui-form-label ">发货时间:</label>
                <div class="width-160">{$detail.shipping.create_time | default= "--"}</div>
                <label class="layui-form-label ">快递方式:</label>
                <div class="width-160">{$detail.shipping.shipping_name | default="--"}</div>
                <label class="layui-form-label ">快递单号:</label>
                <div class="width-160">{$detail.shipping.invoice_no | default= "--"}</div>
            </div>
        {/empty}

        <div class="layui-form-item">
            <fieldset class="layui-elem-field layui-field-title">
                <legend>商品信息</legend>
            </fieldset>
        </div>


        <div class="layui-form-item table-margin">
            <table class="layui-table">
                <colgroup>
                    <col width="250">
                    <col width="100">
                    <col width="200">
                    <col width="100">
                    <col width="200">
                </colgroup>
                <thead>
                <tr>
                    <th>商品名称</th>
                    <th>市场价</th>
                    <th>兑换积分</th>
                    <th>数量</th>
                    <th>运费</th>
                    <th>实付</th>
                </tr>
                </thead>
                <tbody>
                <tr>
                    <td>
                        <div style="text-align: left">
                            <div class="layui-col-md3">
                                <img src="{$detail.goods_snap.image}" class="image-show image" >
                            </div>
                            <div class="layui-col-md9">
                                <p style="margin-top: 10px">{$detail.goods_snap.name}</p>
                            </div>
                        </div>
                    </td>
                    <td>￥{$detail.goods_snap.market_price}</td>
                    <td>{$detail.goods_snap.need_integral}积分{if $detail.goods_snap.exchange_way == 2}+{$detail.goods_snap.need_money}元{/if}</td>
                    <td>{$detail.total_num}</td>
                    <td>￥{$detail.shipping_price}</td>
                    <td>{$detail.order_integral}积分{if $detail.order_amount > 0}+{$detail.order_amount}元{/if}</td>
                </tr>
                </tbody>
            </table>
        </div>

        <div class="layui-form-item">
            <fieldset class="layui-elem-field layui-field-title">
                <legend>物流轨迹</legend>
            </fieldset>
        </div>


        <div class="layui-form-item table-margin">
            <table class="layui-table">
                <colgroup>
                    <col>
                </colgroup>
                <thead>
                <tr >
                    <th colspan="3">轨迹</th>
                </tr>
                </thead>
                <tbody>
                {foreach $detail.shipping.traces as $k => $item}
                <tr>
                    {if is_array($item)}
                        {foreach $item as $k1 => $value}
                            <td>{$value}</td>
                        {/foreach}
                    {else /}
                        <td>{$item}</td>
                    {/if}
                </tr>
                {/foreach}
                </tbody>
            </table>
        </div>


        <div class="layui-form-item div-flex ">
            <div class="layui-input-block ">
                <button type="button" class="layui-btn layui-btn-sm layui-btn-primary width_160 " id="back">返回</button>
            </div>
        </div>
    </div>
</div>


<script type="text/javascript">
    //注意：选项卡 依赖 element 模块，否则无法进行功能性操作
    layui.use(['element', 'jquery','form'], function () {
        var $ = layui.$
            , form = layui.form;

        //主图放大
        $(document).on('click', '.image-show', function () {
            var src = $(this).attr('src');
            like.showImg(src,400);
        });


        $('#back').click(function () {
            var index=parent.layer.getFrameIndex(window.name); //获取当前窗口的name
            parent.layer.close(index);
            parent.layui.table.reload('order-lists');
            return true;
        });
    });
</script>