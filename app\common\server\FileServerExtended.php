<?php

namespace app\common\server;

use app\common\enum\FileEnum;
use app\common\model\File;
use app\common\server\storage\Driver as StorageDriver;
use think\Exception;
use think\facade\Log;

class FileServerExtended
{
    /**
     * 上传任意类型文件，不限制文件类型
     * @param string $save_dir 保存目录
     * @param bool $isLocal 是否只存本地
     * @return array
     */
    public static function uploadAnyFile($save_dir = 'uploads/other', $isLocal = false)
    {
        try {
            if ($isLocal == false) {
                $config = [
                    'default' => ConfigServer::get('storage', 'default', 'local'),
                    'engine'  => ConfigServer::get('storage_engine')
                ];
            } else {
                $config = [
                    'default' => 'local',
                    'engine'  => ConfigServer::get('storage_engine')
                ];
            }
            if (empty($config['engine']['local'])) {
                $config['engine']['local'] = [];
            }
            
            // 获取上传文件
            $file = request()->file('file');
            if (empty($file)) {
                throw new Exception('未找到上传文件');
            }
            
            // 手动处理文件上传，不使用验证器
            $StorageDriver = new StorageDriver($config);
            
            // 设置文件信息
            $StorageDriver->setUploadFileNoValidate('file');
            
            if (!$StorageDriver->upload($save_dir)) {
                throw new Exception('上传失败' . $StorageDriver->getError());
            }
            
            // 文件上传路径
            $fileName = $StorageDriver->getFileName();
            // 文件信息
            $fileInfo = $StorageDriver->getFileInfo();
            
            // 信息
            $data = [
                'name'        => $fileInfo['name'],
                'type'        => FileEnum::OTHER_TYPE,
                'uri'         => $save_dir . '/' . str_replace("\\","/", $fileName),
                'create_time' => time(),
                'size'        => $fileInfo['size'],
                'shop_id'     => 0
            ];
            
            File::insert($data);
            return ['上传文件成功', $data];
            
        } catch (\Exception $e) {
            $message = lang($e->getMessage()) ?? $e->getMessage();
            Log::error('Upload any file error: ' . $message);
            return ['上传文件失败:', [$message]];
        }
    }
}
