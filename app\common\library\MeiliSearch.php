<?php
namespace app\common\library;

use Meilisearch\Client;
class MeiliSearch
{
    private $host;
    private $apiKey;

    public function __construct($host=null, $apiKey=null)
    {
        // 从配置文件或环境变量中获取MeiliSearch配置
        $this->host = $host ?: config('meilisearch.host', 'http://***************:7700');
        $this->apiKey = $apiKey ?: config('meilisearch.api_key', '27bb9198372f81f8b95fb75d0252912de061fb6fa90d0ad6eb347cc051f0abe3');

        // 记录连接信息
        \think\facade\Log::info('MeiliSearch连接信息: ' . $this->host);

        // 测试连接
        $this->testConnection();
    }

    /**
     * 测试MeiliSearch连接
     */
    public function testConnection()
    {
        try {
            // 使用索引列表接口测试连接，因为health接口可能不可用
            $url = "{$this->host}/indexes";
            $headers = [
                'Authorization: Bearer ' . $this->apiKey,
            ];

            $response = $this->curlGet($url, $headers);

            if (isset($response['results']) || (isset($response['error']) && !strpos($response['error'], 'connection'))) {
                \think\facade\Log::info('MeiliSearch连接成功');
                return true;
            } else {
                \think\facade\Log::error('MeiliSearch连接失败: ' . json_encode($response));
                return false;
            }
        } catch (\Exception $e) {
            \think\facade\Log::error('MeiliSearch连接异常: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 添加文档到索引
     *
     * @param string $indexName 索引名称
     * @param array $documents 文档数组
     * @return mixed
     */
    public function addDocumentsToIndex(string $indexName, array $documents)
    {
        $url = "{$this->host}/indexes/{$indexName}/documents";
        $headers = [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $this->apiKey,
        ];
        $data = json_encode($documents);

        return $this->curlPost($url, $data, $headers);
    }

    /**
     * 在索引中搜索文档
     *
     * @param string $indexName 索引名称
     * @param string $query 查询字符串
     * @return mixed
     */
    public function searchInIndex(string $indexName, string $query)
    {
        $url = "{$this->host}/indexes/{$indexName}/search";
        $headers = [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $this->apiKey,
        ];
        $data = json_encode(['q' => $query]);

        return $this->curlPost($url, $data, $headers);
    }

    /**
     * 更新索引中的文档
     *
     * @param string $indexName 索引名称
     * @param array $documents 文档数组，每个文档应包含 'id' 字段以标识要更新的文档
     * @return mixed
     */
    public function updateDocumentsInIndex(string $indexName, array $documents) {
        $url = "{$this->host}/indexes/{$indexName}/documents/update";
        $headers = [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $this->apiKey,
        ];
        $data = json_encode($documents);

        return $this->curlPost($url, $data, $headers);
    }

    /**
     * 更新索引中的文档
     *
     * @param string $indexName 索引名称
     * @param array $documents 文档数组，每个文档应包含 'id' 字段以标识要更新的文档
     * @return mixed
     */
    public function updateSynonyms(string $indexName, array $documents) {
        $url = "{$this->host}/indexes/{$indexName}/documents/update";
        $headers = [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $this->apiKey,
        ];
        $data = json_encode($documents);

        return $this->curlPost($url, $data, $headers);
    }

    /**
     * 为索引增加同义词规则
     *
     * @param string $indexName 索引名称
     * @param array $synonyms 同义词规则数组，每个元素是一个包含"input"和"synonyms"的关联数组
     * @return mixed
     */
    public function addSynonymsToIndex(string $indexName, array $synonyms) {
        $url = "{$this->host}/indexes/{$indexName}/settings/synonyms";
        $headers = [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $this->apiKey,
        ];
        $data = json_encode($synonyms);

        return $this->curlPut($url, $data, $headers);
    }

    public function getTy(string $indexName) {
        $url = "{$this->host}/indexes/{$indexName}/settings/synonyms";
        $headers = [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $this->apiKey,
        ];
//        $data = json_encode($synonyms);

        return $this->curlPost($url, [], $headers);
    }


    /**
     * 搜索候选词
     * @param string $indexName 索引名称
     * @param string $query 搜索关键词
     * @param int $limit 返回结果数量
     * @return array
     */
    public function searchCandidates(string $indexName, string $query, int $limit = 10): array
    {
        // 使用高级搜索方法
        $searchParams = [
            'limit' => $limit,
            'attributesToHighlight' => ['*'], // 高亮匹配字段
            'attributesToRetrieve' => ['text', 'tags'], // 返回的字段
        ];

        $results = $this->advancedSearch($indexName, $query, $searchParams);

        // 格式化结果
        $candidates = [];
        if (isset($results['hits']) && !empty($results['hits'])) {
            foreach ($results['hits'] as $hit) {
                $candidates[] = [
                    'text' => $hit['text'] ?? '',
                    'tags' => $hit['tags'] ?? '',
                    'highlight' => isset($hit['_formatted']) && isset($hit['_formatted']['text'])
                        ? $hit['_formatted']['text']
                        : ($hit['text'] ?? ''), // 高亮结果
                ];
            }
        }

        return $candidates;
    }

    /**
     * 获取搜索建议
     * @param string $query 搜索关键词
     * @param int $limit 返回结果数量
     * @return array
     */
    public function getSearchSuggestions(string $query, int $limit = 10): array
    {
        if (empty($query)) {
            return [];
        }

        // 记录开始时间，用于性能分析
        $startTime = microtime(true);

        // 使用高级搜索方法
        $searchParams = [
            'limit' => $limit * 3, // 获取更多结果，以便后续筛选
            'attributesToRetrieve' => ['text', 'tags', 'goods_id'],
            'sort' => ['weight:desc', 'popularity:desc']
        ];

        $results = $this->advancedSearch('search_suggestions', $query, $searchParams);

        // 记录搜索结果，便于调试
        \think\facade\Log::info('Search suggestions results: ' . json_encode($results));

        // 格式化结果，按词性分类
        $baseWords = []; // 基础词（品牌、品类等）
        $attrWords = []; // 属性词（颜色、尺寸等）
        $funcWords = []; // 功能词
        $sceneWords = []; // 场景词
        $combinedWords = []; // 已有的组合词
        $shortWords = []; // 短词（长度<=8的词）

        if (isset($results['hits']) && !empty($results['hits'])) {
            foreach ($results['hits'] as $hit) {
                if (isset($hit['text']) && !empty($hit['text'])) {
                    $text = $hit['text'];
                    $tags = $hit['tags'] ?? '';
                    $goodsId = $hit['goods_id'] ?? 0;

                    // 过滤掉长度超过15个字符的词（这些通常是商品名称）
                    if (mb_strlen($text, 'UTF-8') > 15) {
                        continue;
                    }

                    // 短词（长度<=8的词）直接加入短词列表
                    if (mb_strlen($text, 'UTF-8') <= 8) {
                        if (!$this->arrayContainsText($shortWords, $text)) {
                            $shortWords[] = [
                                'text' => $text,
                                'tags' => $tags,
                                'goods_id' => $goodsId
                            ];
                        }
                    }

                    // 根据词性分类
                    if (strpos($tags, '组合词') !== false) {
                        // 已有的组合词（只保留长度<=10的组合词）
                        if (mb_strlen($text, 'UTF-8') <= 10 && !$this->arrayContainsText($combinedWords, $text)) {
                            $combinedWords[] = [
                                'text' => $text,
                                'tags' => $tags,
                                'goods_id' => $goodsId
                            ];
                        }
                    }
                    // 基础词（品牌、品类等）
                    else if (strpos($tags, '品牌') !== false ||
                             strpos($tags, '类别词') !== false ||
                             strpos($tags, '产品类型') !== false) {
                        if (!$this->arrayContainsText($baseWords, $text)) {
                            $baseWords[] = [
                                'text' => $text,
                                'tags' => $tags,
                                'goods_id' => $goodsId
                            ];
                        }
                    }
                    // 属性词（颜色、尺寸等）
                    else if (strpos($tags, '颜色') !== false ||
                             strpos($tags, '尺寸') !== false ||
                             strpos($tags, '规格') !== false ||
                             strpos($tags, '材质') !== false) {
                        if (!$this->arrayContainsText($attrWords, $text)) {
                            $attrWords[] = [
                                'text' => $text,
                                'tags' => $tags,
                                'goods_id' => $goodsId
                            ];
                        }
                    }
                    // 功能词
                    else if (strpos($tags, '功能') !== false ||
                             strpos($tags, '功效') !== false) {
                        if (!$this->arrayContainsText($funcWords, $text)) {
                            $funcWords[] = [
                                'text' => $text,
                                'tags' => $tags,
                                'goods_id' => $goodsId
                            ];
                        }
                    }
                    // 场景词
                    else if (strpos($tags, '场景') !== false ||
                             strpos($tags, '人群') !== false) {
                        if (!$this->arrayContainsText($sceneWords, $text)) {
                            $sceneWords[] = [
                                'text' => $text,
                                'tags' => $tags,
                                'goods_id' => $goodsId
                            ];
                        }
                    }
                }
            }
        }

        // 记录分类结果，便于调试
        \think\facade\Log::info('Categorized words: ' . json_encode([
            'baseWords' => $baseWords,
            'attrWords' => $attrWords,
            'funcWords' => $funcWords,
            'sceneWords' => $sceneWords,
            'combinedWords' => $combinedWords,
            'shortWords' => $shortWords
        ]));

        // 生成组合词
        $generatedCombinations = [];

        // 1. 如果有基础词，尝试生成组合
        if (!empty($baseWords)) {
            // 获取基础词相关的实际属性词
            $baseWordTexts = array_column($baseWords, 'text');
            $attributesByBaseWord = $this->getActualAttributesForBaseWords($baseWordTexts);

            // 生成组合词
            foreach ($baseWords as $baseWord) {
                $baseText = $baseWord['text'];
                $attributes = $attributesByBaseWord[$baseText] ?? [];

                // 如果找到了属性词，生成组合
                if (!empty($attributes)) {
                    foreach ($attributes as $attr) {
                        // 避免重复，如"男 男"
                        if (mb_strpos($baseText, $attr) !== false || mb_strpos($attr, $baseText) !== false) {
                            continue;
                        }

                        // 生成组合词
                        $combinedText = $baseText . ' ' . $attr;

                        // 只保留长度<=10的组合词
                        if (mb_strlen($combinedText, 'UTF-8') > 10) {
                            continue;
                        }

                        if (!in_array($combinedText, $generatedCombinations)) {
                            // 验证组合词是否有效（能搜索到商品）
                            if ($this->validateCombination($combinedText)) {
                                $generatedCombinations[] = $combinedText;
                            }
                        }
                    }
                }

                // 如果没有找到属性词或属性词太少，尝试与其他类型的词组合
                if (count($attributes) < 3) {
                    // 与属性词组合
                    foreach ($attrWords as $attrWord) {
                        $attrText = $attrWord['text'];
                        if (mb_strpos($baseText, $attrText) === false && mb_strpos($attrText, $baseText) === false) {
                            $combinedText = $baseText . ' ' . $attrText;

                            // 只保留长度<=10的组合词
                            if (mb_strlen($combinedText, 'UTF-8') > 10) {
                                continue;
                            }

                            if (!in_array($combinedText, $generatedCombinations)) {
                                if ($this->validateCombination($combinedText)) {
                                    $generatedCombinations[] = $combinedText;
                                }
                            }
                        }
                    }

                    // 与功能词组合
                    foreach ($funcWords as $funcWord) {
                        $funcText = $funcWord['text'];
                        if (mb_strpos($baseText, $funcText) === false && mb_strpos($funcText, $baseText) === false) {
                            $combinedText = $baseText . ' ' . $funcText;

                            // 只保留长度<=10的组合词
                            if (mb_strlen($combinedText, 'UTF-8') > 10) {
                                continue;
                            }

                            if (!in_array($combinedText, $generatedCombinations)) {
                                if ($this->validateCombination($combinedText)) {
                                    $generatedCombinations[] = $combinedText;
                                }
                            }
                        }
                    }

                    // 与场景词组合
                    foreach ($sceneWords as $sceneWord) {
                        $sceneText = $sceneWord['text'];
                        if (mb_strpos($baseText, $sceneText) === false && mb_strpos($sceneText, $baseText) === false) {
                            $combinedText = $baseText . ' ' . $sceneText;

                            // 只保留长度<=10的组合词
                            if (mb_strlen($combinedText, 'UTF-8') > 10) {
                                continue;
                            }

                            if (!in_array($combinedText, $generatedCombinations)) {
                                if ($this->validateCombination($combinedText)) {
                                    $generatedCombinations[] = $combinedText;
                                }
                            }
                        }
                    }
                }

                // 限制每个基础词生成的组合词数量
                if (count($generatedCombinations) >= $limit * 2) {
                    break;
                }
            }
        }

        // 记录生成的组合词，便于调试
        \think\facade\Log::info('Generated combinations: ' . json_encode($generatedCombinations));

        // 准备最终的候选词列表
        $finalSuggestions = [];

        // 0. 首先添加查询词本身（如果有效）
        if ($this->validateCombination($query)) {
            $finalSuggestions[] = $query;
        }

        // 1. 添加与查询词完全匹配的短词（优先级最高）
        foreach ($shortWords as $word) {
            // 完全匹配
            if ($word['text'] === $query) {
                if (!in_array($word['text'], $finalSuggestions)) {
                    $finalSuggestions[] = $word['text'];
                }
                continue;
            }

            // 前缀匹配（如搜索"男裤"，匹配"男裤子"、"男裤休闲"等）
            if (mb_strpos($word['text'], $query) === 0) {
                if (!in_array($word['text'], $finalSuggestions)) {
                    $finalSuggestions[] = $word['text'];
                    if (count($finalSuggestions) >= $limit) break;
                }
            }
        }

        // 2. 添加生成的组合词（必须包含查询词）
        foreach ($generatedCombinations as $combination) {
            // 确保组合词包含查询词
            if (mb_strpos($combination, $query) !== false && !in_array($combination, $finalSuggestions)) {
                $finalSuggestions[] = $combination;
                if (count($finalSuggestions) >= $limit) break;
            }
        }

        // 3. 添加已有的组合词（必须包含查询词）
        foreach ($combinedWords as $word) {
            // 确保组合词包含查询词
            if (mb_strpos($word['text'], $query) !== false && !in_array($word['text'], $finalSuggestions)) {
                $finalSuggestions[] = $word['text'];
                if (count($finalSuggestions) >= $limit) break;
            }
        }

        // 4. 添加其他包含查询词的短词
        foreach ($shortWords as $word) {
            // 确保短词包含查询词
            if (mb_strpos($word['text'], $query) !== false && !in_array($word['text'], $finalSuggestions)) {
                $finalSuggestions[] = $word['text'];
                if (count($finalSuggestions) >= $limit) break;
            }
        }

        // 5. 如果候选词不足，尝试从数据库中直接获取相关的短词
        if (count($finalSuggestions) < $limit) {
            $dbSuggestions = $this->getShortSuggestionsFromDB($query, $limit - count($finalSuggestions));
            foreach ($dbSuggestions as $suggestion) {
                if (!in_array($suggestion, $finalSuggestions)) {
                    $finalSuggestions[] = $suggestion;
                    if (count($finalSuggestions) >= $limit) break;
                }
            }
        }

        // 6. 如果仍然没有候选词，至少返回查询词本身
        if (empty($finalSuggestions)) {
            $finalSuggestions[] = $query;
        }

        // 7. 对候选词进行排序，确保最相关的候选词排在前面
        if (count($finalSuggestions) > 1) {
            // 创建一个临时数组，用于存储候选词及其权重
            $weightedSuggestions = [];

            foreach ($finalSuggestions as $suggestion) {
                $weight = 0;

                // 完全匹配权重最高
                if ($suggestion === $query) {
                    $weight += 100;
                }
                // 前缀匹配权重次之
                else if (mb_strpos($suggestion, $query) === 0) {
                    $weight += 80;
                }
                // 包含查询词权重再次
                else if (mb_strpos($suggestion, $query) !== false) {
                    $weight += 60;
                }

                // 短词权重高于长词
                $weight += max(0, 20 - mb_strlen($suggestion, 'UTF-8') * 2);

                // 组合词（包含空格）权重高于单个词
                if (mb_strpos($suggestion, ' ') !== false) {
                    $weight += 10;
                }

                // 查询商品数量，数量越多权重越高
                try {
                    $count = \think\facade\Db::name('goods')
                        ->where(function($q) use ($suggestion) {
                            $q->whereOr('name', 'like', '%' . $suggestion . '%')
                              ->whereOr('split_word', 'like', '%' . $suggestion . '%');
                        })
                        ->where('del', 0)
                        ->where('status', 1)
                        ->where('audit_status', 1)
                        ->count();

                    // 最多加30分
                    $weight += min(30, $count);
                } catch (\Exception $exception) {
                    // 忽略错误
                    \think\facade\Log::error('Error counting goods for suggestion: ' . $exception->getMessage());
                }

                $weightedSuggestions[] = [
                    'text' => $suggestion,
                    'weight' => $weight
                ];
            }

            // 按权重排序
            usort($weightedSuggestions, function($a, $b) {
                return $b['weight'] - $a['weight'];
            });

            // 提取排序后的候选词
            $finalSuggestions = array_column($weightedSuggestions, 'text');
        }

        // 限制数量
        $finalSuggestions = array_slice($finalSuggestions, 0, $limit);

        // 对搜索关键词进行高亮处理
        $highlightedSuggestions = $this->highlightKeywords($finalSuggestions, $query);

        // 记录执行时间
        $endTime = microtime(true);
        $executionTime = round($endTime - $startTime, 4);
        \think\facade\Log::info("getSearchSuggestions execution time: {$executionTime} seconds");

        return $highlightedSuggestions;
    }

    /**
     * 从数据库中直接获取短的搜索建议，基于词性进行智能组合
     * @param string $query 搜索关键词
     * @param int $limit 返回结果数量
     * @return array
     */
    private function getShortSuggestionsFromDB(string $query, int $limit): array
    {
        $suggestions = [];

        try {
            // 1. 首先查询查询词在word表中的记录，获取词性信息
            $queryWords = \think\facade\Db::name('word')
                ->where('word', 'like', '%' . $query . '%')
                ->field('id, word, tags, goods_id')
                ->limit(10)
                ->select()
                ->toArray();

            // 如果在word表中找到了记录
            if (!empty($queryWords)) {
                // 首先添加查询词本身（如果有商品包含该词）
                if (!in_array($query, $suggestions)) {
                    $suggestions[] = $query;
                }

                // 记录查询词的词性和商品ID
                $queryTags = [];
                $queryGoodsIds = [];
                $exactMatch = false;

                foreach ($queryWords as $word) {
                    if ($word['word'] === $query) {
                        // 完全匹配的词优先
                        $queryTags = explode(',', $word['tags']);
                        $queryGoodsIds[] = $word['goods_id'];
                        $exactMatch = true;
                    } else if (mb_strpos($word['word'], $query) === 0) {
                        // 前缀匹配次之
                        if (!$exactMatch) {
                            $queryTags = explode(',', $word['tags']);
                        }
                        $queryGoodsIds[] = $word['goods_id'];
                    } else {
                        // 其他匹配最后
                        if (!$exactMatch) {
                            $queryTags = explode(',', $word['tags']);
                        }
                        $queryGoodsIds[] = $word['goods_id'];
                    }
                }

                // 去重商品ID
                $queryGoodsIds = array_unique($queryGoodsIds);

                // 2. 根据词性确定需要查找的互补词性
                $complementaryTags = $this->getComplementaryTags($queryTags);

                // 3. 查询包含这些商品的互补词性的词
                if (!empty($complementaryTags) && !empty($queryGoodsIds)) {
                    $complementaryWords = \think\facade\Db::name('word')
                        ->whereIn('goods_id', $queryGoodsIds)
                        ->where(function($q) use ($complementaryTags) {
                            foreach ($complementaryTags as $tag) {
                                $q->whereOr('tags', 'like', '%' . $tag . '%');
                            }
                        })
                        ->where('word', '<>', $query) // 排除查询词本身
                        ->field('word, tags')
                        ->limit(30)
                        ->select()
                        ->toArray();

                    // 4. 提取互补词
                    $complementaryTexts = [];
                    foreach ($complementaryWords as $word) {
                        $wordText = $word['word'];
                        // 只保留短词
                        if (mb_strlen($wordText, 'UTF-8') >= 1 && mb_strlen($wordText, 'UTF-8') <= 6) {
                            $complementaryTexts[] = $wordText;
                        }
                    }

                    // 统计词频
                    $complementaryCounts = array_count_values($complementaryTexts);

                    // 按出现次数排序
                    arsort($complementaryCounts);

                    // 只保留出现次数最多的词
                    $topComplementaryWords = array_slice(array_keys($complementaryCounts), 0, 10);

                    // 5. 生成组合词
                    foreach ($topComplementaryWords as $complementaryWord) {
                        // 根据词性确定组合顺序
                        $isCategory = false;
                        foreach ($queryTags as $tag) {
                            if (strpos($tag, '类别') !== false || strpos($tag, '产品类型') !== false) {
                                $isCategory = true;
                                break;
                            }
                        }

                        // 如果查询词是类别词，则放在前面
                        if ($isCategory) {
                            $combinedText = $query . ' ' . $complementaryWord;
                        } else {
                            // 否则，尝试两种组合顺序，选择能找到商品的那种
                            $combinedText1 = $query . ' ' . $complementaryWord;
                            $combinedText2 = $complementaryWord . ' ' . $query;

                            if ($this->validateCombination($combinedText1)) {
                                $combinedText = $combinedText1;
                            } else if ($this->validateCombination($combinedText2)) {
                                $combinedText = $combinedText2;
                            } else {
                                // 如果两种组合都找不到商品，使用第一种
                                $combinedText = $combinedText1;
                            }
                        }

                        // 添加到候选词列表
                        if (mb_strlen($combinedText, 'UTF-8') <= 10 && !in_array($combinedText, $suggestions)) {
                            if ($this->validateCombination($combinedText)) {
                                $suggestions[] = $combinedText;
                                if (count($suggestions) >= $limit) break;
                            }
                        }
                    }
                }

                // 6. 如果还不够，添加单个互补词
                if (count($suggestions) < $limit && isset($topComplementaryWords)) {
                    foreach ($topComplementaryWords as $word) {
                        if (!in_array($word, $suggestions)) {
                            $suggestions[] = $word;
                            if (count($suggestions) >= $limit) break;
                        }
                    }
                }
            }

            // 7. 如果还不够，尝试从商品表中查询
            if (count($suggestions) < $limit) {
                $exactMatches = \think\facade\Db::name('goods')
                    ->where('name', 'like', '%' . $query . '%')
                    ->whereOr('split_word', 'like', '%' . $query . '%')
                    ->where('del', 0)
                    ->where('status', 1)
                    ->where('audit_status', 1)
                    ->field('name, split_word')
                    ->limit(20)
                    ->select()
                    ->toArray();

                if (!empty($exactMatches)) {
                    // 首先添加查询词本身（如果有商品包含该词）
                    if (!in_array($query, $suggestions)) {
                        $suggestions[] = $query;
                    }

                    // 从商品名称和分词中提取包含查询词的短词
                    foreach ($exactMatches as $item) {
                        // 处理商品名称
                        $name = $item['name'];
                        $words = explode(' ', $name);
                        foreach ($words as $word) {
                            $word = trim($word);
                            // 只保留包含查询词的短词
                            if (mb_strlen($word, 'UTF-8') >= 2 && mb_strlen($word, 'UTF-8') <= 8 &&
                                mb_strpos($word, $query) !== false && $word != $query) {
                                if (!in_array($word, $suggestions)) {
                                    $suggestions[] = $word;
                                    if (count($suggestions) >= $limit) break 2;
                                }
                            }
                        }

                        // 处理分词
                        if (!empty($item['split_word'])) {
                            $splitWords = explode(',', $item['split_word']);
                            foreach ($splitWords as $word) {
                                $word = trim($word);
                                // 只保留包含查询词的短词
                                if (mb_strlen($word, 'UTF-8') >= 2 && mb_strlen($word, 'UTF-8') <= 8 &&
                                    mb_strpos($word, $query) !== false && $word != $query) {
                                    if (!in_array($word, $suggestions)) {
                                        $suggestions[] = $word;
                                        if (count($suggestions) >= $limit) break 2;
                                    }
                                }
                            }
                        }
                    }
                }
            }

            // 8. 如果还不够，使用通用的属性词生成组合
            if (count($suggestions) < $limit && mb_strlen($query, 'UTF-8') >= 2) {
                // 通用属性词 - 按照商品类别分组
                $commonAttributesByCategory = [
                    // 服装类通用属性
                    '服装' => ['男', '女', '儿童', '成人', '中老年', '夏季', '冬季', '春季', '秋季', '黑色', '白色', '蓝色', '灰色', '休闲', '运动', '宽松', '修身'],
                    '裤子' => ['男', '女', '儿童', '成人', '中老年', '夏季', '冬季', '春季', '秋季', '黑色', '白色', '蓝色', '灰色', '休闲', '运动', '宽松', '修身', '直筒', '阔腿', '牛仔'],
                    '上衣' => ['男', '女', '儿童', '成人', '中老年', '夏季', '冬季', '春季', '秋季', '黑色', '白色', '蓝色', '灰色', '休闲', '运动', '宽松', '修身', '长袖', '短袖', '棉'],
                    '裙子' => ['女', '儿童', '成人', '中老年', '夏季', '冬季', '春季', '秋季', '黑色', '白色', '蓝色', '灰色', '休闲', '长裙', '短裙', 'A字', '百褶'],
                    '鞋子' => ['男', '女', '儿童', '成人', '中老年', '夏季', '冬季', '春季', '秋季', '黑色', '白色', '蓝色', '灰色', '休闲', '运动', '皮鞋', '帆布', '高跟'],

                    // 电子产品类通用属性
                    '电子' => ['智能', '高清', '无线', '便携', '大屏', '小巧', '黑色', '白色', '银色', '金色', '新款', '旗舰'],
                    '手机' => ['智能', '高清', '无线', '便携', '大屏', '小巧', '黑色', '白色', '银色', '金色', '新款', '旗舰', '5G', '4G', '大内存'],
                    '电脑' => ['智能', '高清', '无线', '便携', '大屏', '小巧', '黑色', '白色', '银色', '金色', '新款', '旗舰', '游戏', '办公', '轻薄'],

                    // 家居类通用属性
                    '家居' => ['简约', '现代', '北欧', '中式', '美式', '日式', '实木', '布艺', '皮质', '大号', '小号', '黑色', '白色', '原木色'],
                    '家具' => ['简约', '现代', '北欧', '中式', '美式', '日式', '实木', '布艺', '皮质', '大号', '小号', '黑色', '白色', '原木色'],

                    // 美妆类通用属性
                    '美妆' => ['保湿', '滋润', '控油', '补水', '美白', '防晒', '修复', '敏感肌', '干性', '油性', '混合性'],
                    '护肤' => ['保湿', '滋润', '控油', '补水', '美白', '防晒', '修复', '敏感肌', '干性', '油性', '混合性'],

                    // 食品类通用属性
                    '食品' => ['新鲜', '美味', '健康', '有机', '无添加', '进口', '国产', '零食', '饮料', '休闲', '营养'],
                    '零食' => ['新鲜', '美味', '健康', '有机', '无添加', '进口', '国产', '休闲', '营养', '甜味', '咸味', '辣味'],

                    // 默认通用属性
                    'default' => ['男', '女', '儿童', '成人', '中老年', '夏季', '冬季', '春季', '秋季', '黑色', '白色', '蓝色', '灰色', '新款', '热销', '推荐', '高品质']
                ];

                // 确定查询词可能属于的类别
                $possibleCategories = [];

                // 1. 首先尝试从word表中获取词性信息
                $wordInfo = \think\facade\Db::name('word')
                    ->where('word', 'like', '%' . $query . '%')
                    ->field('tags')
                    ->limit(5)
                    ->select()
                    ->toArray();

                if (!empty($wordInfo)) {
                    foreach ($wordInfo as $info) {
                        $tags = explode(',', $info['tags']);
                        foreach ($tags as $tag) {
                            // 检查标签是否包含类别信息
                            foreach (array_keys($commonAttributesByCategory) as $category) {
                                if (mb_strpos($tag, $category) !== false || mb_strpos($category, $tag) !== false) {
                                    $possibleCategories[] = $category;
                                }
                            }
                        }
                    }
                }

                // 2. 如果没有从词性中找到类别，尝试直接匹配查询词
                if (empty($possibleCategories)) {
                    foreach (array_keys($commonAttributesByCategory) as $category) {
                        if (mb_strpos($query, $category) !== false || mb_strpos($category, $query) !== false) {
                            $possibleCategories[] = $category;
                        }
                    }
                }

                // 3. 如果还是没有找到类别，使用默认类别
                if (empty($possibleCategories)) {
                    $possibleCategories[] = 'default';
                }

                // 去重
                $possibleCategories = array_unique($possibleCategories);

                // 收集所有可能的属性词
                $allAttributes = [];
                foreach ($possibleCategories as $category) {
                    if (isset($commonAttributesByCategory[$category])) {
                        $allAttributes = array_merge($allAttributes, $commonAttributesByCategory[$category]);
                    }
                }

                // 去重
                $allAttributes = array_unique($allAttributes);

                // 生成组合词
                foreach ($allAttributes as $attr) {
                    // 尝试两种组合顺序
                    $combinedText1 = $query . ' ' . $attr;
                    $combinedText2 = $attr . ' ' . $query;

                    // 检查第一种组合
                    if (mb_strlen($combinedText1, 'UTF-8') <= 10 && !in_array($combinedText1, $suggestions)) {
                        if ($this->validateCombination($combinedText1)) {
                            $suggestions[] = $combinedText1;
                            if (count($suggestions) >= $limit) break;
                        }
                    }

                    // 如果还不够，检查第二种组合
                    if (count($suggestions) < $limit && mb_strlen($combinedText2, 'UTF-8') <= 10 && !in_array($combinedText2, $suggestions)) {
                        if ($this->validateCombination($combinedText2)) {
                            $suggestions[] = $combinedText2;
                            if (count($suggestions) >= $limit) break;
                        }
                    }
                }
            }

            // 9. 如果还是没有足够的候选词，至少返回查询词本身
            if (empty($suggestions)) {
                $suggestions[] = $query;
            }
        } catch (\Exception $e) {
            \think\facade\Log::error('Error getting short suggestions from DB: ' . $e->getMessage());
            // 出错时至少返回查询词本身
            if (empty($suggestions)) {
                $suggestions[] = $query;
            }
        }

        return $suggestions;
    }



    /**
     * 根据词性获取互补词性
     * @param array $tags 词性数组
     * @return array 互补词性数组
     */
    private function getComplementaryTags(array $tags): array
    {
        // 定义词性组合规则 - 扩展支持更多商品类型
        $tagRules = [
            // 服装类
            '类别词' => ['颜色', '材质', '尺寸规格', '风格', '款式元素', '人群', '季节'],
            '产品类型' => ['颜色', '材质', '尺寸规格', '风格', '款式元素', '人群', '季节'],
            '产品类型-简单' => ['颜色', '材质', '尺寸规格', '风格', '款式元素', '人群', '季节'],
            '产品类型-统称' => ['颜色', '材质', '尺寸规格', '风格', '款式元素', '人群', '季节'],
            '服装' => ['颜色', '材质', '尺寸规格', '风格', '款式元素', '人群', '季节'],
            '裤子' => ['颜色', '材质', '尺寸规格', '风格', '款式元素', '人群', '季节'],
            '上衣' => ['颜色', '材质', '尺寸规格', '风格', '款式元素', '人群', '季节'],
            '裙子' => ['颜色', '材质', '尺寸规格', '风格', '款式元素', '人群', '季节'],
            '鞋子' => ['颜色', '材质', '尺寸规格', '风格', '款式元素', '人群', '季节'],
            '帽子' => ['颜色', '材质', '尺寸规格', '风格', '款式元素', '人群', '季节'],

            // 电子产品类
            '电子产品' => ['品牌', '功能功效', '尺寸规格', '颜色', '型号'],
            '手机' => ['品牌', '功能功效', '尺寸规格', '颜色', '型号', '内存', '存储'],
            '电脑' => ['品牌', '功能功效', '尺寸规格', '颜色', '型号', '内存', '存储', '处理器'],
            '相机' => ['品牌', '功能功效', '尺寸规格', '颜色', '型号', '像素'],

            // 家居类
            '家居' => ['材质', '颜色', '尺寸规格', '风格', '功能功效'],
            '家具' => ['材质', '颜色', '尺寸规格', '风格', '功能功效'],
            '床' => ['材质', '颜色', '尺寸规格', '风格', '功能功效'],
            '沙发' => ['材质', '颜色', '尺寸规格', '风格', '功能功效'],
            '桌子' => ['材质', '颜色', '尺寸规格', '风格', '功能功效'],
            '椅子' => ['材质', '颜色', '尺寸规格', '风格', '功能功效'],

            // 美妆类
            '美妆' => ['品牌', '功能功效', '适用人群', '成分'],
            '护肤品' => ['品牌', '功能功效', '适用人群', '成分'],
            '化妆品' => ['品牌', '功能功效', '适用人群', '成分', '颜色'],

            // 食品类
            '食品' => ['品牌', '口味', '产地', '保质期', '包装'],
            '零食' => ['品牌', '口味', '产地', '保质期', '包装'],
            '饮料' => ['品牌', '口味', '产地', '保质期', '包装'],

            // 通用属性
            '品牌' => ['类别词', '产品类型', '功能功效', '颜色', '材质'],
            '产品-品牌' => ['类别词', '产品类型', '功能功效', '颜色', '材质'],
            '颜色' => ['类别词', '产品类型', '材质', '风格'],
            '材质' => ['类别词', '产品类型', '颜色', '风格'],
            '功能功效' => ['类别词', '产品类型', '场景', '人群'],
            '场景' => ['类别词', '产品类型', '功能功效', '人群'],
            '人群' => ['类别词', '产品类型', '功能功效', '场景'],
            '季节' => ['类别词', '产品类型', '材质', '功能功效'],
            '尺寸规格' => ['类别词', '产品类型', '人群'],
            '风格' => ['类别词', '产品类型', '颜色', '材质'],
            '款式元素' => ['类别词', '产品类型', '颜色', '材质'],

            // 默认搭配
            'default' => ['颜色', '材质', '尺寸规格', '风格', '款式元素', '类别词', '产品类型', '人群', '季节', '功能功效', '场景']
        ];

        $complementaryTags = [];

        // 根据输入词性确定互补词性
        foreach ($tags as $tag) {
            // 精确匹配
            if (isset($tagRules[$tag])) {
                $complementaryTags = array_merge($complementaryTags, $tagRules[$tag]);
                continue;
            }

            // 模糊匹配 - 处理包含特定关键词的标签
            foreach ($tagRules as $ruleKey => $ruleTags) {
                if (mb_strpos($tag, $ruleKey) !== false || mb_strpos($ruleKey, $tag) !== false) {
                    $complementaryTags = array_merge($complementaryTags, $ruleTags);
                    break;
                }
            }
        }

        // 如果没有找到匹配的规则，使用默认规则
        if (empty($complementaryTags)) {
            $complementaryTags = $tagRules['default'];
        }

        // 去重
        $complementaryTags = array_unique($complementaryTags);

        return $complementaryTags;
    }

    /**
     * 检查数组中是否包含指定文本
     * @param array $array 要检查的数组
     * @param string $text 要查找的文本
     * @return bool
     */
    private function arrayContainsText(array $array, string $text): bool
    {
        foreach ($array as $item) {
            if ($item['text'] === $text) {
                return true;
            }
        }
        return false;
    }

    /**
     * 验证组合词是否有效（能搜索到商品）
     * @param string $combination 组合词
     * @return bool
     */
    private function validateCombination(string $combination): bool
    {
        // 如果组合词为空，返回false
        if (empty($combination)) {
            return false;
        }

        // 缓存验证结果，避免重复查询
        static $validationCache = [];

        // 如果已经验证过，直接返回缓存结果
        if (isset($validationCache[$combination])) {
            return $validationCache[$combination];
        }

        try {
            // 1. 首先尝试精确匹配
            $exactCount = \think\facade\Db::name('goods')
                ->where(function($query) use ($combination) {
                    $query->whereOr('name', 'like', '%' . $combination . '%')
                          ->whereOr('split_word', 'like', '%' . $combination . '%');
                })
                ->where('del', 0)
                ->where('status', 1)
                ->where('audit_status', 1)
                ->count();

            if ($exactCount > 0) {
                // 缓存结果
                $validationCache[$combination] = true;
                return true;
            }

            // 2. 如果精确匹配没有结果，尝试分词匹配
            // 将组合词拆分为单词
            $words = explode(' ', $combination);
            if (count($words) >= 2) {
                // 构建查询条件
                $whereCondition = [];
                foreach ($words as $word) {
                    $whereCondition[] = ['name|split_word', 'like', '%' . $word . '%'];
                }

                // 查询同时包含所有单词的商品
                $wordCount = \think\facade\Db::name('goods')
                    ->where($whereCondition)
                    ->where('del', 0)
                    ->where('status', 1)
                    ->where('audit_status', 1)
                    ->count();

                // 缓存结果
                $validationCache[$combination] = ($wordCount > 0);

                return $wordCount > 0;
            }

            // 3. 如果以上都没有结果，尝试在word表中查询
            $wordParts = explode(' ', $combination);
            if (count($wordParts) >= 2) {
                $wordIds = [];

                // 查询第一个词的ID
                $firstWordIds = \think\facade\Db::name('word')
                    ->where('word', 'like', '%' . $wordParts[0] . '%')
                    ->column('goods_id');

                if (!empty($firstWordIds)) {
                    $wordIds = $firstWordIds;

                    // 查询第二个词的ID
                    $secondWordIds = \think\facade\Db::name('word')
                        ->where('word', 'like', '%' . $wordParts[1] . '%')
                        ->column('goods_id');

                    // 取交集，找到同时包含两个词的商品
                    if (!empty($secondWordIds)) {
                        $wordIds = array_intersect($wordIds, $secondWordIds);
                    }
                }

                // 缓存结果
                $validationCache[$combination] = !empty($wordIds);

                return !empty($wordIds);
            }

            // 默认返回false
            $validationCache[$combination] = false;
            return false;
        } catch (\Exception $e) {
            \think\facade\Log::error('Error validating combination: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 高亮关键词
     * @param array $suggestions 候选词数组
     * @param string $query 搜索关键词
     * @return array 高亮后的候选词数组
     */
    private function highlightKeywords(array $suggestions, string $query): array
    {
        $highlighted = [];

        foreach ($suggestions as $suggestion) {
            // 使用正则表达式进行大小写不敏感的匹配
            $pattern = '/(' . preg_quote($query, '/') . ')/iu';
            $replacement = '<b style="color: red;">$1</b>';
            $highlightedText = preg_replace($pattern, $replacement, $suggestion);

            // 按照指定格式返回
            $highlighted[] = [
                'title' => $highlightedText, // 高亮后的文本
                'name' => $suggestion // 原始文本
            ];
        }

        return $highlighted;
    }





    /**
     * 获取基础词相关的实际属性词
     * @param array $baseWords 基础词数组
     * @return array 每个基础词对应的属性词数组
     */
    private function getActualAttributesForBaseWords(array $baseWords): array
    {
        if (empty($baseWords)) {
            return [];
        }

        $attributesByBaseWord = [];
        $startTime = microtime(true);

        try {
            // 查询与基础词相关的商品
            foreach ($baseWords as $baseWord) {
                // 尝试从缓存中获取属性词
                $cacheKey = 'attr_for_' . md5($baseWord);
                $cachedAttributes = \think\facade\Cache::get($cacheKey);

                if ($cachedAttributes !== null) {
                    $attributesByBaseWord[$baseWord] = $cachedAttributes;
                    continue;
                }

                // 查询包含基础词的商品
                $goodsIds = \think\facade\Db::name('goods')
                    ->where('name', 'like', '%' . $baseWord . '%')
                    ->where('del', 0)
                    ->where('status', 1)
                    ->where('audit_status', 1)
                    ->limit(30) // 限制查询数量，提高性能
                    ->column('id');

                if (empty($goodsIds)) {
                    // 如果没有找到商品，尝试使用更宽松的匹配条件
                    $goodsIds = \think\facade\Db::name('goods')
                        ->where('name', 'like', '%' . $baseWord . '%')
                        ->where('del', 0)
                        ->limit(20) // 限制查询数量，提高性能
                        ->column('id');

                    if (empty($goodsIds)) {
                        // 如果仍然没有找到商品，保存空结果到缓存并继续下一个基础词
                        \think\facade\Cache::set($cacheKey, [], 3600); // 缓存1小时
                        $attributesByBaseWord[$baseWord] = [];
                        continue;
                    }
                }

                // 从商品名称中提取属性词
                $goodsNames = \think\facade\Db::name('goods')
                    ->whereIn('id', $goodsIds)
                    ->column('name');

                $attributes = [];
                foreach ($goodsNames as $name) {
                    // 提取商品名称中的短词
                    $nameWords = explode(' ', $name);
                    foreach ($nameWords as $word) {
                        $word = trim($word);
                        if (mb_strlen($word, 'UTF-8') >= 1 && mb_strlen($word, 'UTF-8') <= 4) {
                            // 排除与基础词相同的词
                            if ($word !== $baseWord && mb_strpos($baseWord, $word) === false && mb_strpos($word, $baseWord) === false) {
                                $attributes[] = $word;
                            }
                        }
                    }

                    // 也可以尝试从商品名称中提取常见的属性词
                    $commonAttributes = ['男', '女', '儿童', '夏', '冬', '春', '秋', '黑', '白', '红', '蓝', '绿', '大', '小', '中', '长', '短', '厚', '薄', '新', '老'];
                    foreach ($commonAttributes as $attr) {
                        if (mb_strpos($name, $attr) !== false && $attr !== $baseWord) {
                            $attributes[] = $attr;
                        }
                    }
                }

                // 统计属性词出现次数
                $attributeCounts = array_count_values($attributes);

                // 按出现次数排序
                arsort($attributeCounts);

                // 只保留出现次数最多的10个属性词
                $topAttributes = array_slice(array_keys($attributeCounts), 0, 10);

                // 保存结果到缓存
                \think\facade\Cache::set($cacheKey, $topAttributes, 3600); // 缓存1小时

                // 保存结果
                $attributesByBaseWord[$baseWord] = $topAttributes;
            }
        } catch (\Exception $e) {
            // 记录错误，但不中断流程
            \think\facade\Log::error('Error getting actual attributes: ' . $e->getMessage());
        }

        // 记录执行时间
        $endTime = microtime(true);
        $executionTime = round($endTime - $startTime, 4);
        \think\facade\Log::info("getActualAttributesForBaseWords execution time: {$executionTime} seconds");

        return $attributesByBaseWord;
    }

    /**
     * 创建索引并配置设置
     *
     * @param string $indexName 索引名称
     * @param array $settings 索引设置，如可搜索字段、排序规则等
     * @return mixed
     */
    public function createIndex(string $indexName, array $settings = [])
    {
        // 创建索引
        $url = "{$this->host}/indexes";
        $headers = [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $this->apiKey,
        ];

        $data = ['uid' => $indexName];

        // 如果提供了primaryKey，添加到请求数据中
        if (isset($settings['primaryKey'])) {
            $data['primaryKey'] = $settings['primaryKey'];
            unset($settings['primaryKey']);
        }

        // 尝试使用不同的HTTP方法创建索引
        try {
            // 首先尝试使用POST方法
            $response = $this->curlPost($url, json_encode($data), $headers);

            // 如果返回错误，尝试使用PUT方法
            if (isset($response['error']) && strpos($response['error'], 'HTTP Error: 405') !== false) {
                \think\facade\Log::info('POST method failed, trying PUT method...');
                $response = $this->curlPut($url, json_encode($data), $headers);
            }

            // 如果仍然返回错误，尝试直接创建文档
            if (isset($response['error'])) {
                \think\facade\Log::info('Both POST and PUT methods failed, trying to create documents directly...');

                // 创建一个空文档，MeiliSearch会自动创建索引
                $emptyDoc = [['id' => 1, 'text' => 'init', 'tags' => 'init', 'weight' => 0, 'popularity' => 0]];
                $docResponse = $this->importDocuments($indexName, $emptyDoc, 'id');

                if (!isset($docResponse['error'])) {
                    $response = $this->getIndex($indexName);
                }
            }
        } catch (\Exception $e) {
            \think\facade\Log::error('Error creating index: ' . $e->getMessage());
            return ['error' => 'Error creating index: ' . $e->getMessage()];
        }

        // 记录响应，便于调试
        \think\facade\Log::info('Create index response: ' . json_encode($response));

        // 如果有设置，则更新索引设置
        if (!empty($settings) && !isset($response['error'])) {
            $this->updateIndexSettings($indexName, $settings);
        }

        return $response;
    }

    /**
     * 批量导入商品数据到索引
     *
     * @param string $indexName 索引名称
     * @param array $documents 商品数据数组
     * @param string|null $primaryKey 主键字段名
     * @return mixed
     */
    public function importDocuments(string $indexName, array $documents, ?string $primaryKey = null)
    {
        $url = "{$this->host}/indexes/{$indexName}/documents";

        // 如果提供了主键，添加到URL参数中
        if ($primaryKey) {
            $url .= "?primaryKey={$primaryKey}";
        }

        $headers = [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $this->apiKey,
        ];
        $data = json_encode($documents);

        return $this->curlPost($url, $data, $headers);
    }

    /**
     * 更新索引设置
     *
     * @param string $indexName 索引名称
     * @param array $settings 索引设置
     * @return mixed
     */
    public function updateIndexSettings(string $indexName, array $settings)
    {
        $url = "{$this->host}/indexes/{$indexName}/settings";
        $headers = [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $this->apiKey,
        ];
        $data = json_encode($settings);

        return $this->curlPut($url, $data, $headers);
    }

    /**
     * 获取索引信息
     *
     * @param string $indexName 索引名称
     * @return mixed
     */
    public function getIndex(string $indexName)
    {
        $url = "{$this->host}/indexes/{$indexName}";
        $headers = [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $this->apiKey,
        ];

        return $this->curlGet($url, $headers);
    }

    /**
     * 高级搜索功能
     *
     * @param string $indexName 索引名称
     * @param string $query 查询字符串
     * @param array $options 搜索选项，如过滤器、排序、分页等
     * @return mixed
     */
    public function advancedSearch(string $indexName, string $query, array $options = [])
    {
        $url = "{$this->host}/indexes/{$indexName}/search";
        $headers = [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $this->apiKey,
        ];

        $searchParams = array_merge(['q' => $query], $options);
        $data = json_encode($searchParams);

        \think\facade\Log::info('MeiliSearch请求URL: ' . $url);
        \think\facade\Log::info('MeiliSearch请求参数: ' . $data);

        $result = $this->curlPost($url, $data, $headers);

        if (isset($result['error'])) {
            // \think\facade\Log::error('MeiliSearch搜索错误: ' . json_encode($result));
        }

        return $result;
    }

    /**
     * 执行 cURL GET 请求
     *
     * @param string $url 请求 URL
     * @param array $headers 请求头
     * @return mixed
     */
    private function curlGet($url, $headers = [])
    {
        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_TIMEOUT, 60); // 设置超时时间为60秒
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // 不验证SSL证书
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false); // 不验证SSL主机
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        if (curl_errno($ch)) {
            $error = 'Curl Error: ' . curl_error($ch);
            \think\facade\Log::write($error);
            curl_close($ch);
            return ['error' => $error];
        }

        curl_close($ch);

        $decodedResponse = json_decode($response, true);

        // 如果HTTP状态码不是2xx，记录错误
        if ($httpCode < 200 || $httpCode >= 300) {
            $error = "HTTP Error: {$httpCode}, Response: " . $response;
            \think\facade\Log::write($error);
            return ['error' => $error, 'http_code' => $httpCode, 'response' => $decodedResponse];
        }

        return $decodedResponse;
    }

    /**
     * 发送PUT请求的辅助方法
     *
     * @param string $url 请求的URL
     * @param string $data 要发送的数据
     * @param array $headers HTTP头部信息
     * @return mixed
     */
    protected function curlPut($url, $data, $headers) {
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "PUT");
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 60); // 设置超时时间为60秒
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // 不验证SSL证书
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false); // 不验证SSL主机
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        if (curl_errno($ch)) {
            $error = 'Curl Error: ' . curl_error($ch);
            \think\facade\Log::write($error);
            curl_close($ch);
            return ['error' => $error];
        }

        curl_close($ch);

        $decodedResponse = json_decode($response, true);

        // 如果HTTP状态码不是2xx，记录错误
        if ($httpCode < 200 || $httpCode >= 300) {
            $error = "HTTP Error: {$httpCode}, Response: " . $response;
            \think\facade\Log::write($error);
            return ['error' => $error, 'http_code' => $httpCode, 'response' => $decodedResponse];
        }

        return $decodedResponse;
    }


    /**
     * 执行 cURL POST 请求
     *
     * @param string $url 请求 URL
     * @param string $data 请求数据
     * @param array $headers 请求头
     * @return mixed
     */
    private function curlPost($url, $data, $headers = [])
    {
        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        curl_setopt($ch, CURLOPT_TIMEOUT, 60); // 设置超时时间为60秒
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // 不验证SSL证书
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false); // 不验证SSL主机
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        if (curl_errno($ch)) {
            $error = 'Curl Error: ' . curl_error($ch);
            \think\facade\Log::write($error);
            curl_close($ch);
            return ['error' => $error];
        }

        curl_close($ch);

        $decodedResponse = json_decode($response, true);

        // 如果HTTP状态码不是2xx，记录错误
        if ($httpCode < 200 || $httpCode >= 300) {
            $error = "HTTP Error: {$httpCode}, Response: " . $response;
            \think\facade\Log::write($error);
            return ['error' => $error, 'http_code' => $httpCode, 'response' => $decodedResponse];
        }

        return $decodedResponse;
    }

    /**
     * 从索引中删除文档
     *
     * @param string $indexName 索引名称
     * @param array $documentIds 要删除的文档ID数组
     * @return mixed
     */
    public function deleteDocuments(string $indexName, array $documentIds)
    {
        // 使用新版MeiliSearch API格式
        if (count($documentIds) == 1) {
            // 如果只有一个ID，使用单文档删除API
            $url = "{$this->host}/indexes/{$indexName}/documents/{$documentIds[0]}";
            $headers = [
                'Content-Type: application/json',
                'Authorization: Bearer ' . $this->apiKey,
            ];

            return $this->curlDelete($url, $headers);
        } else {
            // 如果有多个ID，使用批量删除API
            $url = "{$this->host}/indexes/{$indexName}/documents/delete-batch";
            $headers = [
                'Content-Type: application/json',
                'Authorization: Bearer ' . $this->apiKey,
            ];
            $data = json_encode($documentIds);

            return $this->curlPost($url, $data, $headers);
        }
    }

    /**
     * 执行DELETE请求
     *
     * @param string $url 请求URL
     * @param array $headers 请求头
     * @return mixed
     */
    private function curlDelete(string $url, array $headers = [])
    {
        // 初始化cURL会话
        $ch = curl_init();

        // 设置cURL选项
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'DELETE');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 60); // 设置超时时间为60秒
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // 不验证SSL证书
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false); // 不验证SSL主机
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        if (curl_errno($ch)) {
            $error = 'Curl Error: ' . curl_error($ch);
            \think\facade\Log::write($error);
            curl_close($ch);
            return ['error' => $error];
        }

        curl_close($ch);

        // 204状态码表示成功删除
        if ($httpCode == 204) {
            return ['success' => true];
        }

        $decodedResponse = json_decode($response, true);

        // 如果HTTP状态码不是2xx，记录错误
        if ($httpCode < 200 || $httpCode >= 300) {
            $error = "HTTP Error: {$httpCode}, Response: " . $response;
            \think\facade\Log::write($error);
            return ['error' => $error, 'http_code' => $httpCode, 'response' => $decodedResponse];
        }

        return $decodedResponse;
    }

    /**
     * 删除指定的索引
     *
     * @param string $indexName 要删除的索引名称
     * @return mixed 删除成功返回响应，失败返回错误信息
     */
    public function deleteIndex($indexName) {
        // 构造删除索引的URL
        $url = "{$this->host}/indexes/{$indexName}";
        $headers = [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $this->apiKey,
        ];

        return $this->curlDelete($url, $headers);
    }


}