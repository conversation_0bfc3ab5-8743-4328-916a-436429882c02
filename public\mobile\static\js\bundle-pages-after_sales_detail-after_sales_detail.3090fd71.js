(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["bundle-pages-after_sales_detail-after_sales_detail"],{1351:function(t,e,i){"use strict";i.r(e);var a=i("b954"),n=i("a30a");for(var r in n)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(r);i("d354");var s=i("f0c5"),o=Object(s["a"])(n["default"],a["b"],a["c"],!1,null,"422f7227",null,!1,a["a"],void 0);e["default"]=o.exports},1522:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return r})),i.d(e,"a",(function(){return a}));var a={uIcon:i("90f3").default},n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"u-image",style:[t.wrapStyle,t.backgroundStyle],on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onClick.apply(void 0,arguments)}}},[t.isError?t._e():i("v-uni-image",{staticClass:"u-image__image",style:{borderRadius:"circle"==t.shape?"50%":t.$u.addUnit(t.borderRadius)},attrs:{src:t.src,mode:t.mode,"lazy-load":t.lazyLoad},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.onErrorHandler.apply(void 0,arguments)},load:function(e){arguments[0]=e=t.$handleEvent(e),t.onLoadHandler.apply(void 0,arguments)}}}),t.showLoading&&t.loading?i("v-uni-view",{staticClass:"u-image__loading",style:{borderRadius:"circle"==t.shape?"50%":t.$u.addUnit(t.borderRadius),backgroundColor:this.bgColor}},[t.$slots.loading?t._t("loading"):i("u-icon",{attrs:{name:t.loadingIcon,width:t.width,height:t.height}})],2):t._e(),t.showError&&t.isError&&!t.loading?i("v-uni-view",{staticClass:"u-image__error",style:{borderRadius:"circle"==t.shape?"50%":t.$u.addUnit(t.borderRadius)}},[t.$slots.error?t._t("error"):i("u-icon",{attrs:{name:t.errorIcon,width:t.width,height:t.height}})],2):t._e()],1)},r=[]},2322:function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("a9e3");var a={name:"u-image",props:{src:{type:String,default:""},mode:{type:String,default:"aspectFill"},width:{type:[String,Number],default:"100%"},height:{type:[String,Number],default:"auto"},shape:{type:String,default:"square"},borderRadius:{type:[String,Number],default:0},lazyLoad:{type:Boolean,default:!0},showMenuByLongpress:{type:Boolean,default:!0},loadingIcon:{type:String,default:"photo"},errorIcon:{type:String,default:"error-circle"},showLoading:{type:Boolean,default:!0},showError:{type:Boolean,default:!0},fade:{type:Boolean,default:!0},webp:{type:Boolean,default:!1},duration:{type:[String,Number],default:500},bgColor:{type:String,default:"#f3f4f6"}},data:function(){return{isError:!1,loading:!0,opacity:1,durationTime:this.duration,backgroundStyle:{}}},watch:{src:{immediate:!0,handler:function(t){t?this.isError=!1:(this.isError=!0,this.loading=!1)}}},computed:{wrapStyle:function(){var t={};return t.width=this.$u.addUnit(this.width),t.height=this.$u.addUnit(this.height),t.borderRadius="circle"==this.shape?"50%":this.$u.addUnit(this.borderRadius),t.overflow=this.borderRadius>0?"hidden":"visible",this.fade&&(t.opacity=this.opacity,t.transition="opacity ".concat(Number(this.durationTime)/1e3,"s ease-in-out")),t}},methods:{onClick:function(){this.$emit("click")},onErrorHandler:function(t){this.loading=!1,this.isError=!0,this.$emit("error",t)},onLoadHandler:function(){var t=this;if(this.loading=!1,this.isError=!1,this.$emit("load"),!this.fade)return this.removeBgColor();this.opacity=0,this.durationTime=0,setTimeout((function(){t.durationTime=t.duration,t.opacity=1,setTimeout((function(){t.removeBgColor()}),t.durationTime)}),50)},removeBgColor:function(){this.backgroundStyle={backgroundColor:"transparent"}}}};e.default=a},2649:function(t,e,i){var a=i("24fb");e=a(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.after-sales-detail[data-v-422f7227]{padding-bottom:calc(%?120?% + env(safe-area-inset-bottom))}.after-sales-detail .after-sales-header .after-sales-status[data-v-422f7227]{padding:%?48?% %?30?%;background-color:#555}.after-sales-detail .after-sales-header .after-sales-explain[data-v-422f7227]{padding:%?20?% %?30?% %?24?%}.after-sales-detail .return-goods-container[data-v-422f7227]{padding:%?20?% %?24?% %?55?%}.after-sales-detail .return-goods-container .return-goods-item[data-v-422f7227]{line-height:%?40?%}.after-sales-detail .btn-group[data-v-422f7227]{padding:%?0?% %?24?%;position:fixed;left:0;right:0;bottom:0;height:%?100?%;padding-bottom:env(safe-area-inset-bottom);box-sizing:initial}.after-sales-detail .btn-group .btn[data-v-422f7227]{padding:%?10?% %?34?%;border:1px solid #999}.after-sales-detail .btn-group .btn.primary[data-v-422f7227]{border-color:#ff2c3c}.after-sales-detail .goods-container .goods-item[data-v-422f7227]{padding:%?25?% %?24?%}.after-sales-detail .goods-container .goods-item .goods-info[data-v-422f7227]{min-width:%?500?%}.return-address-contain[data-v-422f7227]{padding:%?20?% %?24?% %?28?% %?30?%}.return-address-contain .address[data-v-422f7227]{flex:1;line-height:%?38?%}.return-address-contain .address-title[data-v-422f7227]{width:%?150?%;align-self:flex-start;line-height:%?40?%}.return-address-contain .copy-btn[data-v-422f7227]{background-color:#f4f4f4;color:#555;padding:%?3?% %?16?%;margin-left:%?12?%;border-radius:%?4?%}.tips-dialog[data-v-422f7227]{height:%?230?%;width:100%}',""]),t.exports=e},2698:function(t,e,i){"use strict";var a=i("5bed"),n=i.n(a);n.a},"45ee":function(t,e,i){var a=i("24fb");e=a(!1),e.push([t.i,".price-format[data-v-0a5a34e0]{font-family:Avenir,SourceHanSansCN,PingFang SC,Arial,Hiragino Sans GB,Microsoft YaHei,sans-serif}",""]),t.exports=e},"46d6":function(t,e,i){var a=i("24fb");e=a(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.shop-title[data-v-705e9a38]{height:%?80?%;flex:1;min-width:0}.shop-title .tag[data-v-705e9a38]{background:linear-gradient(267deg,#ff2c3c,#f52e99);border-radius:%?6?%;padding:%?5?% %?9?%}',""]),t.exports=e},"585a":function(t,e,i){var a=i("2649");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("4f06").default;n("3d2806d0",a,!0,{sourceMap:!1,shadowMode:!1})},"5bed":function(t,e,i){var a=i("46d6");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("4f06").default;n("50d1694e",a,!0,{sourceMap:!1,shadowMode:!1})},6021:function(t,e,i){"use strict";var a=i("64b1"),n=i.n(a);n.a},"64b1":function(t,e,i){var a=i("6e35");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("4f06").default;n("3d1e497c",a,!0,{sourceMap:!1,shadowMode:!1})},"6d79":function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("a9e3"),i("acd8");var a={data:function(){return{priceSlice:{}}},components:{},props:{firstSize:{type:[String,Number],default:28},secondSize:{type:[String,Number],default:28},color:{type:String},weight:{type:[String,Number],default:400},price:{type:[String,Number],default:""},showSubscript:{type:Boolean,default:!0},subscriptSize:{type:[String,Number],default:28},lineThrough:{type:Boolean,default:!1}},created:function(){this.priceFormat()},watch:{price:function(t){this.priceFormat()}},methods:{priceFormat:function(){var t=this.price,e={};null!==t&&""!==t&&void 0!==t?(t=parseFloat(t),t=String(t).split("."),e.first=t[0],e.second=t[1],this.priceSlice=e):this.priceSlice={first:0}}}};e.default=a},"6e35":function(t,e,i){var a=i("24fb");e=a(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.u-image[data-v-1bf07c9a]{position:relative;transition:opacity .5s ease-in-out}.u-image__image[data-v-1bf07c9a]{width:100%;height:100%}.u-image__loading[data-v-1bf07c9a], .u-image__error[data-v-1bf07c9a]{position:absolute;top:0;left:0;width:100%;height:100%;display:flex;flex-direction:row;align-items:center;justify-content:center;background-color:#f3f4f6;color:#909399;font-size:%?46?%}',""]),t.exports=e},"8aa9":function(t,e,i){"use strict";i.r(e);var a=i("fb1d"),n=i.n(a);for(var r in a)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(r);e["default"]=n.a},"95a6":function(t,e,i){"use strict";i.r(e);var a=i("ac23"),n=i("8aa9");for(var r in n)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(r);i("2698");var s=i("f0c5"),o=Object(s["a"])(n["default"],a["b"],a["c"],!1,null,"705e9a38",null,!1,a["a"],void 0);e["default"]=o.exports},a30a:function(t,e,i){"use strict";i.r(e);var a=i("f877"),n=i.n(a);for(var r in a)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(r);e["default"]=n.a},ac23:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return r})),i.d(e,"a",(function(){return a}));var a={uIcon:i("90f3").default},n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"shop-title flex",on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.toShop.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"shop-name line-1 bold"},[t._v(t._s(t.shop.shop_name||t.shop.name||t.name))]),t.isLink?i("u-icon",{staticClass:"m-l-10 m-r-20",attrs:{name:"arrow-right",size:"28"}}):t._e()],1)},r=[]},af8d:function(t,e,i){"use strict";i.r(e);var a=i("2322"),n=i.n(a);for(var r in a)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(r);e["default"]=n.a},b954:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return r})),i.d(e,"a",(function(){return a}));var a={shopTitle:i("95a6").default,uImage:i("ba4b").default,priceFormat:i("fefe").default,uModal:i("8d42").default},n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",[i("v-uni-view",{staticClass:"after-sales-detail"},[i("v-uni-view",{staticClass:"after-sales-header"},[i("v-uni-view",{staticClass:"after-sales-status white lg"},[t._v(t._s(t.afterSale.status_text))])],1),i("v-uni-view",{staticClass:"return-address-contain flex bg-white m-t-20"},[i("v-uni-view",{staticClass:"address-title"},[t._v("退货地址：")]),i("v-uni-view",{staticClass:"sm address flex-1"},[t._v(t._s(t.afterSale.shop.address)+", "+t._s(t.afterSale.shop.contact)+",\n\t\t\t\t"+t._s(t.afterSale.shop.mobile))]),i("v-uni-view",{staticClass:"xs copy-btn flex-none row-center",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onCopy.apply(void 0,arguments)}}},[t._v("复制")])],1),i("v-uni-view",{staticClass:"goods-container bg-white m-t-20"},[i("v-uni-view",{staticClass:"m-l-20"},[i("shop-title",{attrs:{shop:{name:t.afterSale.shop_name,id:t.afterSale.sid}}})],1),i("v-uni-view",{staticClass:"goods-item flex"},[i("v-uni-view",{staticClass:"goods-img"},[i("u-image",{attrs:{width:"180rpx",height:"180rpx","border-radius":"10rpx",src:t.afterSale.order_goods.image}})],1),i("v-uni-view",{staticClass:"goods-info flex-1 m-l-24"},[i("v-uni-view",{staticClass:"line-2"},[t._v(t._s(t.afterSale.order_goods.goods_name))]),i("v-uni-view",{staticClass:"m-t-10 xs line-1 muted"},[t._v(t._s(t.afterSale.order_goods.spec_value))]),i("v-uni-view",{staticClass:"flex row-between m-t-20"},[i("price-format",{attrs:{price:t.afterSale.order_goods.goods_price,"first-size":30,"second-size":30,"subscript-size":30}}),i("v-uni-view",[t._v("x"+t._s(t.afterSale.order_goods.goods_num))])],1)],1)],1)],1),i("v-uni-view",{staticClass:"return-goods-container bg-white m-t-20"},[i("v-uni-view",{staticClass:"return-goods-item flex sm"},[i("v-uni-view",{staticClass:"return-title"},[t._v("退款方式：")]),i("v-uni-view",{staticClass:"return-explain"},[t._v(t._s(t.afterSale.refund_type_text))])],1),i("v-uni-view",{staticClass:"return-goods-item flex sm m-t-20"},[i("v-uni-view",{staticClass:"return-title"},[t._v("退款原因：")]),i("v-uni-view",{staticClass:"return-explain"},[t._v(t._s(t.afterSale.refund_reason))])],1),i("v-uni-view",{staticClass:"return-goods-item flex sm m-t-20"},[i("v-uni-view",{staticClass:"return-title"},[t._v("退款金额：")]),i("v-uni-view",{staticClass:"return-explain primary"},[t._v("¥"+t._s(t.afterSale.refund_price))])],1),i("v-uni-view",{staticClass:"return-goods-item flex sm m-t-20"},[i("v-uni-view",{staticClass:"return-title"},[t._v("退款编号：")]),i("v-uni-view",{staticClass:"return-explain"},[t._v(t._s(t.afterSale.sn))])],1),i("v-uni-view",{staticClass:"return-goods-item flex sm m-t-20"},[i("v-uni-view",{staticClass:"return-title"},[t._v("申请时间：")]),i("v-uni-view",{staticClass:"return-explain"},[t._v(t._s(t.afterSale.create_time))])],1)],1),6!=t.afterSale.status?i("v-uni-view",{staticClass:"btn-group fixed bg-white flex row-right"},[i("v-uni-view",{staticClass:"m-r-20 btn br60",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.confirmDialog=!0}}},[t._v("撤销申请")]),i("router-link",{attrs:{to:{path:"/bundle/pages/apply_refund/apply_refund",query:{after_sale_id:t.afterSale.id,order_id:t.afterSale.order_goods.order_id,item_id:t.afterSale.order_goods.item_id}}}},[4==t.afterSale.status||1==t.afterSale.status?i("v-uni-view",{staticClass:"m-r-20 btn br60 primary"},[t._v("重新申请")]):t._e()],1),i("router-link",{attrs:{to:{path:"/bundle/pages/input_express_info/input_express_info",query:{id:t.afterSale.id}}}},[2==t.afterSale.status?i("v-uni-view",{staticClass:"m-r-20 btn br60"},[t._v("填写快递单号")]):t._e()],1)],1):t._e()],1),i("u-modal",{attrs:{"confirm-text":"确定",showCancelButton:!0,"confirm-color":t.colorConfig.primary},on:{confirm:function(e){arguments[0]=e=t.$handleEvent(e),t.cancelApplyFun.apply(void 0,arguments)}},model:{value:t.confirmDialog,callback:function(e){t.confirmDialog=e},expression:"confirmDialog"}},[i("v-uni-view",{staticClass:"flex-col col-center tips-dialog",staticStyle:{padding:"30rpx 0"}},[i("v-uni-image",{staticClass:"icon-lg",attrs:{src:"/static/images/icon_warning.png"}}),i("v-uni-view",{staticClass:"m-t-30"},[t._v("是否要撤销申请？")])],1)],1)],1)},r=[]},ba4b:function(t,e,i){"use strict";i.r(e);var a=i("1522"),n=i("af8d");for(var r in n)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(r);i("6021");var s=i("f0c5"),o=Object(s["a"])(n["default"],a["b"],a["c"],!1,null,"1bf07c9a",null,!1,a["a"],void 0);e["default"]=o.exports},bd6f:function(t,e,i){"use strict";i.r(e);var a=i("6d79"),n=i.n(a);for(var r in a)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(r);e["default"]=n.a},c495:function(t,e,i){var a=i("45ee");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("4f06").default;n("54b253da",a,!0,{sourceMap:!1,shadowMode:!1})},d354:function(t,e,i){"use strict";var a=i("585a"),n=i.n(a);n.a},d5b0:function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return n})),i.d(e,"a",(function(){}));var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-text",{class:(t.lineThrough?"line-through":"")+" price-format",style:{color:t.color,"font-weight":t.weight}},[t.showSubscript?i("v-uni-text",{style:{"font-size":t.subscriptSize+"rpx","margin-right":"2rpx"}},[t._v("¥")]):t._e(),i("v-uni-text",{style:{"font-size":t.firstSize+"rpx","margin-right":"1rpx"}},[t._v(t._s(t.priceSlice.first))]),t.priceSlice.second?i("v-uni-text",{style:{"font-size":t.secondSize+"rpx"}},[t._v("."+t._s(t.priceSlice.second))]):t._e()],1)},n=[]},ee17:function(t,e,i){"use strict";var a=i("c495"),n=i.n(a);n.a},f877:function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("99af");var a=i("8516"),n=i("b08d"),r={data:function(){return{afterSale:{shop:{},order_goods:{}},confirmDialog:!1}},onLoad:function(t){},onShow:function(){this.id=this.$Route.query.id,this.afterSaleDetailFun()},methods:{onCopy:function(){var t=this.afterSale,e=t.shop,i=e.address,a=e.contact,r=e.mobile;(0,n.copy)("".concat(i,",").concat(a,",").concat(r))},cancelApplyFun:function(){var t=this;(0,a.cancelApply)({id:this.id}).then((function(e){1==e.code&&(uni.$emit("refreshsale"),t.$toast({title:e.msg},{tab:3,url:1}))}))},afterSaleDetailFun:function(){var t=this;(0,a.afterSaleDetail)({id:this.id}).then((function(e){1==e.code&&(t.afterSale=e.data)}))}}};e.default=r},fb1d:function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("14d9");var a={name:"shop-title",options:{virtualHost:!0},props:{name:{type:String},shop:{type:Object},isLink:{type:Boolean,default:!0}},data:function(){return{}},methods:{toShop:function(){var t=this.isLink,e=this.shop;t&&this.$Router.push({path:"/pages/store_index/store_index",query:{id:e.shop_id||e.id}})}}};e.default=a},fefe:function(t,e,i){"use strict";i.r(e);var a=i("d5b0"),n=i("bd6f");for(var r in n)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(r);i("ee17");var s=i("f0c5"),o=Object(s["a"])(n["default"],a["b"],a["c"],!1,null,"0a5a34e0",null,!1,a["a"],void 0);e["default"]=o.exports}}]);