<?php


namespace app\shopapi\controller;

use app\common\basics\ShopApi;
use app\common\server\JsonServer;
use app\shopapi\logic\GoodsLogic;

/**
 * 商品管理
 */
class Goods extends ShopApi {
    /**
     * @notes 商品列表
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2021/11/10 11:16
     */
    public function lists()
    {
        $params = $this->request->param();
        $params['page_no'] = $this->page_no;
        $params['page_size'] = $this->page_size;
        $params['shop_id'] = $this->shop_id;

        $result = (new GoodsLogic())->lists($params);
        return JsonServer::success('', $result);
    }

    /**
     * @notes 操作
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2021/11/10 11:22
     */
    public function operation()
    {
      

        $result = (new GoodsLogic())->operation($this->shop_id, $this->request->post());
        if($result){
            return JsonServer::success('操作成功');
        }
        return JsonServer::error(GoodsLogic::getError());
    }

    /**
     * @notes 商品详情
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2021/11/10 14:17
     */
    public function detail()
    {
        $result = (new GoodsLogic())->detail($this->request->get('id'));
        return JsonServer::success('', $result);
    }

    /**
     * @notes 商品编辑
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2021/11/10 15:23
     */
    public function edit()
    {
        if ($this->request->isAjax() || !$this->request->isPost()) {
            return JsonServer::error("请求方式错误");
        }

        $result = (new GoodsLogic())->edit($this->request->post());
        if($result){
            return JsonServer::success('编辑成功');
        }
        return JsonServer::error(GoodsLogic::getError());
    }
}