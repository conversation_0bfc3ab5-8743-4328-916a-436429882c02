<?php

namespace app\api\controller;

use app\common\basics\Api;
use app\common\enum\OrderEnum;
use app\common\enum\PayEnum;
use app\common\model\order\Order;
use app\common\server\JsonServer;
use app\common\server\WechatMiniExpressSendSyncServer;

/**
 * 微信订单API测试控制器
 * Class WechatOrderTest
 * @package app\api\controller
 */
class WechatOrderTest extends Api
{
    /**
     * @notes 测试自动确认收货
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2024-01-01 00:00:00
     */
    public function autoConfirm()
    {
        try {
            $days = $this->request->post('days', 7);
            $time = time();
            $finish_limit = $days * 24 * 60 * 60;
            
            // 查找需要自动确认的订单
            $orders = Order::where([
                ['order_status', '=', OrderEnum::ORDER_STATUS_GOODS],
                ['pay_status', '=', OrderEnum::PAY_STATUS_PAID],
                ['del', '=', 0]
            ])->whereRaw("shipping_time+$finish_limit < $time")
              ->limit(10) // 限制测试数量
              ->select()->toArray();

            $count = 0;
            $wechat_sync_count = 0;

            foreach ($orders as $order) {
                // 更新订单状态
                Order::where(['id' => $order['id']])
                    ->update([
                        'order_status'      => OrderEnum::ORDER_STATUS_COMPLETE,
                        'update_time'       => $time,
                        'confirm_take_time' => $time,
                    ]);
                $count++;

                // 同步微信小程序确认收货信息
                if ($order['pay_way'] == PayEnum::WECHAT_PAY) {
                    try {
                        $updatedOrder = Order::where('id', $order['id'])->find()->toArray();
                        $result = WechatMiniExpressSendSyncServer::_sync_order_confirm($updatedOrder);
                        if ($result) {
                            $wechat_sync_count++;
                        }
                    } catch (\Exception $e) {
                        // 记录错误但继续处理
                    }
                }
            }

            return JsonServer::success('自动确认收货测试完成', [
                'count' => $count,
                'wechat_sync_count' => $wechat_sync_count,
                'wechat_sync' => $wechat_sync_count > 0
            ]);

        } catch (\Exception $e) {
            return JsonServer::error('自动确认收货测试失败: ' . $e->getMessage());
        }
    }

    /**
     * @notes 查询订单微信同步状态
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2024-01-01 00:00:00
     */
    public function wechatSyncCheck()
    {
        try {
            $orderId = $this->request->post('id');
            
            if (empty($orderId)) {
                return JsonServer::error('订单ID不能为空');
            }

            $order = Order::where('id', $orderId)->find();
            
            if (!$order) {
                return JsonServer::error('订单不存在');
            }

            $orderData = $order->toArray();
            
            // 获取订单状态描述
            $orderData['order_status_text'] = OrderEnum::getOrderStatus($orderData['order_status']);
            
            // 获取配送方式描述
            $deliveryTypes = [
                OrderEnum::DELIVERY_TYPE_EXPRESS => '快递配送',
                OrderEnum::DELIVERY_TYPE_VIRTUAL => '虚拟发货',
                OrderEnum::DELIVERY_TYPE_SELF => '线下自提'
            ];
            $orderData['delivery_type_text'] = $deliveryTypes[$orderData['delivery_type']] ?? '未知';

            return JsonServer::success('查询成功', $orderData);

        } catch (\Exception $e) {
            return JsonServer::error('查询失败: ' . $e->getMessage());
        }
    }

    /**
     * @notes 测试微信发货同步
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2024-01-01 00:00:00
     */
    public function testDeliverySync()
    {
        try {
            $orderId = $this->request->post('order_id');
            
            if (empty($orderId)) {
                return JsonServer::error('订单ID不能为空');
            }

            $order = Order::where('id', $orderId)->find();
            
            if (!$order) {
                return JsonServer::error('订单不存在');
            }

            $orderData = $order->toArray();
            
            // 检查是否为微信支付
            if ($orderData['pay_way'] != PayEnum::WECHAT_PAY) {
                return JsonServer::error('该订单不是微信支付，无需同步');
            }

            // 调用微信发货同步
            $result = WechatMiniExpressSendSyncServer::_sync_order($orderData);

            if ($result) {
                return JsonServer::success('微信发货信息同步成功', [
                    'wechat_sync' => true,
                    'order_id' => $orderId
                ]);
            } else {
                return JsonServer::error('微信发货信息同步失败');
            }

        } catch (\Exception $e) {
            return JsonServer::error('测试失败: ' . $e->getMessage());
        }
    }

    /**
     * @notes 测试微信确认收货同步
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2024-01-01 00:00:00
     */
    public function testConfirmSync()
    {
        try {
            $orderId = $this->request->post('order_id');
            
            if (empty($orderId)) {
                return JsonServer::error('订单ID不能为空');
            }

            $order = Order::where('id', $orderId)->find();
            
            if (!$order) {
                return JsonServer::error('订单不存在');
            }

            $orderData = $order->toArray();
            
            // 检查是否为微信支付
            if ($orderData['pay_way'] != PayEnum::WECHAT_PAY) {
                return JsonServer::error('该订单不是微信支付，无需同步');
            }

            // 调用微信确认收货同步
            $result = WechatMiniExpressSendSyncServer::_sync_order_confirm($orderData);

            if ($result) {
                return JsonServer::success('微信确认收货信息同步成功', [
                    'wechat_sync' => true,
                    'order_id' => $orderId
                ]);
            } else {
                return JsonServer::error('微信确认收货信息同步失败');
            }

        } catch (\Exception $e) {
            return JsonServer::error('测试失败: ' . $e->getMessage());
        }
    }

    /**
     * @notes 测试微信退款同步
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2024-01-01 00:00:00
     */
    public function testRefundSync()
    {
        try {
            $orderId = $this->request->post('order_id');
            $refundAmount = $this->request->post('refund_amount');
            $refundReason = $this->request->post('refund_reason', '测试退款');
            
            if (empty($orderId) || empty($refundAmount)) {
                return JsonServer::error('订单ID和退款金额不能为空');
            }

            $order = Order::where('id', $orderId)->find();
            
            if (!$order) {
                return JsonServer::error('订单不存在');
            }

            $orderData = $order->toArray();
            
            // 检查是否为微信支付
            if ($orderData['pay_way'] != PayEnum::WECHAT_PAY) {
                return JsonServer::error('该订单不是微信支付，无需同步');
            }

            // 构造退款信息
            $refundInfo = [
                'refund_amount' => $refundAmount,
                'refund_time' => time(),
                'refund_reason' => $refundReason
            ];

            // 调用微信退款同步
            $result = WechatMiniExpressSendSyncServer::_sync_order_refund($orderData, $refundInfo);

            if ($result) {
                return JsonServer::success('微信退款信息同步成功', [
                    'wechat_sync' => true,
                    'order_id' => $orderId,
                    'refund_amount' => $refundAmount
                ]);
            } else {
                return JsonServer::error('微信退款信息同步失败');
            }

        } catch (\Exception $e) {
            return JsonServer::error('测试失败: ' . $e->getMessage());
        }
    }

    /**
     * @notes 获取测试订单列表
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2024-01-01 00:00:00
     */
    public function getTestOrders()
    {
        try {
            $orders = Order::where('pay_way', PayEnum::WECHAT_PAY)
                ->where('del', 0)
                ->field('id,order_sn,order_status,pay_status,shipping_status,delivery_type,wechat_mini_express_sync,create_time')
                ->order('id desc')
                ->limit(20)
                ->select()
                ->toArray();

            foreach ($orders as &$order) {
                $order['order_status_text'] = OrderEnum::getOrderStatus($order['order_status']);
                $order['create_time_text'] = date('Y-m-d H:i:s', $order['create_time']);
                
                $deliveryTypes = [
                    OrderEnum::DELIVERY_TYPE_EXPRESS => '快递配送',
                    OrderEnum::DELIVERY_TYPE_VIRTUAL => '虚拟发货',
                    OrderEnum::DELIVERY_TYPE_SELF => '线下自提'
                ];
                $order['delivery_type_text'] = $deliveryTypes[$order['delivery_type']] ?? '未知';
                
                $syncStatus = [
                    0 => '未同步',
                    1 => '已同步',
                    2 => '同步失败'
                ];
                $order['wechat_sync_status_text'] = $syncStatus[$order['wechat_mini_express_sync']] ?? '未知';
            }

            return JsonServer::success('获取成功', $orders);

        } catch (\Exception $e) {
            return JsonServer::error('获取失败: ' . $e->getMessage());
        }
    }
}
