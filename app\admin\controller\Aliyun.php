<?php
namespace app\admin\controller;

use app\common\basics\AdminBase;
use app\common\server\ConfigServer;
use app\common\server\JsonServer;
use think\facade\View;

/**
 * 阿里云配置控制器
 * Class Aliyun
 * @package app\admin\controller
 */
class <PERSON><PERSON> extends AdminBase
{
    /**
     * 阿里云API配置
     */
    public function api()
    {
        if ($this->request->isAjax()) {
            $post = $this->request->post();

            // 保存配置
            ConfigServer::set('aliyun', 'access_key_id', $post['access_key_id'] ?? '');
            ConfigServer::set('aliyun', 'access_key_secret', $post['access_key_secret'] ?? '');

            // 记录日志在基类中已自动完成

            return JsonServer::success('保存成功');
        }

        // 获取配置
        $config = [
            'access_key_id' => ConfigServer::get('aliyun', 'access_key_id', ''),
            'access_key_secret' => ConfigServer::get('aliyun', 'access_key_secret', '')
        ];

        View::assign('config', $config);
        return View::fetch();
    }
}
