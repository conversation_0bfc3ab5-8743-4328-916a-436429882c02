<?php

namespace app\common\server;

use think\facade\Log;
use think\facade\Cache;

/**
 * 身份证OCR识别服务
 * Class IdCardOcrServer
 * @package app\common\server
 */
class IdCardOcrServer
{
    /**
     * 微信身份证OCR识别错误码映射
     */
    const ERROR_CODES = [
        -1 => '系统错误',
        40001 => 'access_token无效或已过期',
        101000 => '图片URL错误',
        101001 => '未检测到证件',
        101002 => '图片大小超过限制或解码失败',
        101003 => '市场配额不足'
    ];

    /**
     * @notes 身份证OCR识别（正面和背面）
     * @param string $frontImageUrl 身份证正面图片URL
     * @param string $backImageUrl 身份证背面图片URL
     * @return array
     */
    public static function recognizeIdCard($frontImageUrl = '', $backImageUrl = '')
    {
        $result = [
            'success' => true,
            'message' => '',
            'data' => [
                'front' => null,
                'back' => null
            ],
            'errors' => []
        ];

        // 识别正面
        if (!empty($frontImageUrl)) {
            $frontResult = self::recognizeSingle($frontImageUrl, 'Front');
            if ($frontResult['errcode'] === 0) {
                $result['data']['front'] = $frontResult;
                
                // 验证是否为正面
                if ($frontResult['type'] !== 'Front') {
                    $result['errors'][] = '请上传身份证正面照片';
                    $result['success'] = false;
                }
            } else {
                $result['errors'][] = '身份证正面识别失败: ' . self::getErrorMessage($frontResult['errcode'], $frontResult['errmsg']);
                $result['success'] = false;
            }
        }

        // 识别背面
        if (!empty($backImageUrl)) {
            $backResult = self::recognizeSingle($backImageUrl, 'Back');
            if ($backResult['errcode'] === 0) {
                $result['data']['back'] = $backResult;
                
                // 验证是否为背面
                if ($backResult['type'] !== 'Back') {
                    $result['errors'][] = '请上传身份证背面照片';
                    $result['success'] = false;
                }
                
                // 检查有效期
                if (!empty($backResult['valid_date'])) {
                    $validDate = $backResult['valid_date'];
                    if ($validDate !== '长期' && strtotime($validDate) < time()) {
                        $result['errors'][] = '身份证已过期，有效期至: ' . $validDate;
                        $result['success'] = false;
                    }
                }
            } else {
                $result['errors'][] = '身份证背面识别失败: ' . self::getErrorMessage($backResult['errcode'], $backResult['errmsg']);
                $result['success'] = false;
            }
        }

        if (!$result['success']) {
            $result['message'] = implode('; ', $result['errors']);
        } else {
            $result['message'] = '身份证识别成功';
        }

        return $result;
    }

    /**
     * @notes 单张身份证图片识别
     * @param string $imageUrl 图片URL或本地路径
     * @param string $expectedType 期望的类型 Front/Back
     * @return array
     */
    public static function recognizeSingle($imageUrl, $expectedType = 'Front')
    {
        return WechatService::idCardOcr($imageUrl, $expectedType);
    }

    /**
     * @notes 验证身份证信息
     * @param array $ocrData OCR识别结果
     * @param array $inputData 用户输入数据
     * @return array
     */
    public static function validateIdCardInfo($ocrData, $inputData)
    {
        $errors = [];
        
        // 验证正面信息
        if (isset($ocrData['front'])) {
            $frontData = $ocrData['front'];
            
            // 验证姓名
            if (!empty($frontData['name']) && !empty($inputData['name'])) {
                if ($frontData['name'] !== $inputData['name']) {
                    $errors[] = '身份证姓名与填写姓名不符';
                }
            }
            
            // 验证身份证号
            if (!empty($frontData['id']) && !empty($inputData['id_card_number'])) {
                if ($frontData['id'] !== $inputData['id_card_number']) {
                    $errors[] = '身份证号与填写身份证号不符';
                }
            }
        }
        
        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }

    /**
     * @notes 从OCR结果中提取信息
     * @param array $ocrData OCR识别结果
     * @return array
     */
    public static function extractInfo($ocrData)
    {
        $info = [
            'name' => '',
            'id_card_number' => '',
            'gender' => '',
            'nationality' => '',
            'address' => '',
            'valid_date' => ''
        ];

        // 从正面提取信息
        if (isset($ocrData['front'])) {
            $frontData = $ocrData['front'];
            $info['name'] = $frontData['name'] ?? '';
            $info['id_card_number'] = $frontData['id'] ?? '';
            $info['gender'] = $frontData['gender'] ?? '';
            $info['nationality'] = $frontData['nationality'] ?? '';
            $info['address'] = $frontData['addr'] ?? '';
        }

        // 从背面提取信息
        if (isset($ocrData['back'])) {
            $backData = $ocrData['back'];
            $info['valid_date'] = $backData['valid_date'] ?? '';
        }

        return $info;
    }

    /**
     * @notes 获取错误信息
     * @param int $errcode 错误码
     * @param string $errmsg 原始错误信息
     * @return string
     */
    private static function getErrorMessage($errcode, $errmsg = '')
    {
        if (isset(self::ERROR_CODES[$errcode])) {
            return self::ERROR_CODES[$errcode];
        }
        
        return $errmsg ?: '未知错误';
    }

    /**
     * @notes 缓存OCR结果
     * @param string $key 缓存键
     * @param array $data 数据
     * @param int $expire 过期时间（秒）
     * @return bool
     */
    public static function cacheOcrResult($key, $data, $expire = 3600)
    {
        try {
            return Cache::set('ocr_' . $key, $data, $expire);
        } catch (\Exception $e) {
            Log::error('OCR结果缓存失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * @notes 获取缓存的OCR结果
     * @param string $key 缓存键
     * @return array|null
     */
    public static function getCachedOcrResult($key)
    {
        try {
            return Cache::get('ocr_' . $key);
        } catch (\Exception $e) {
            Log::error('获取OCR缓存失败: ' . $e->getMessage());
            return null;
        }
    }
}
