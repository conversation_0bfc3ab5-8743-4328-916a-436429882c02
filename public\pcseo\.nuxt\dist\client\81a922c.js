(window.webpackJsonp=window.webpackJsonp||[]).push([[14,9,18],{437:function(t,e,n){"use strict";var r=n(17),o=n(2),c=n(3),l=n(136),d=n(27),f=n(18),m=n(271),h=n(52),v=n(135),_=n(270),x=n(5),S=n(98).f,w=n(44).f,y=n(26).f,k=n(438),C=n(439).trim,N="Number",I=o.Number,T=I.prototype,z=o.TypeError,E=c("".slice),M=c("".charCodeAt),O=function(t){var e=_(t,"number");return"bigint"==typeof e?e:A(e)},A=function(t){var e,n,r,o,c,l,d,code,f=_(t,"number");if(v(f))throw z("Cannot convert a Symbol value to a number");if("string"==typeof f&&f.length>2)if(f=C(f),43===(e=M(f,0))||45===e){if(88===(n=M(f,2))||120===n)return NaN}else if(48===e){switch(M(f,1)){case 66:case 98:r=2,o=49;break;case 79:case 111:r=8,o=55;break;default:return+f}for(l=(c=E(f,2)).length,d=0;d<l;d++)if((code=M(c,d))<48||code>o)return NaN;return parseInt(c,r)}return+f};if(l(N,!I(" 0o1")||!I("0b1")||I("+0x1"))){for(var D,F=function(t){var e=arguments.length<1?0:I(O(t)),n=this;return h(T,n)&&x((function(){k(n)}))?m(Object(e),n,F):e},L=r?S(I):"MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,EPSILON,MAX_SAFE_INTEGER,MIN_SAFE_INTEGER,isFinite,isInteger,isNaN,isSafeInteger,parseFloat,parseInt,fromString,range".split(","),R=0;L.length>R;R++)f(I,D=L[R])&&!f(F,D)&&y(F,D,w(I,D));F.prototype=T,T.constructor=F,d(o,N,F)}},438:function(t,e,n){var r=n(3);t.exports=r(1..valueOf)},439:function(t,e,n){var r=n(3),o=n(33),c=n(16),l=n(440),d=r("".replace),f="["+l+"]",m=RegExp("^"+f+f+"*"),h=RegExp(f+f+"*$"),v=function(t){return function(e){var n=c(o(e));return 1&t&&(n=d(n,m,"")),2&t&&(n=d(n,h,"")),n}};t.exports={start:v(1),end:v(2),trim:v(3)}},440:function(t,e){t.exports="\t\n\v\f\r                　\u2028\u2029\ufeff"},441:function(t,e,n){var content=n(444);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,n(14).default)("3181fc86",content,!0,{sourceMap:!1})},442:function(t,e,n){"use strict";n.r(e);n(437),n(80),n(272);var r={data:function(){return{priceSlice:{}}},components:{},props:{firstSize:{type:Number,default:14},secondSize:{type:Number,default:14},color:{type:String},weight:{type:[String,Number],default:400},price:{type:[String,Number],default:""},showSubscript:{type:Boolean,default:!0},subscriptSize:{type:Number,default:14},lineThrough:{type:Boolean,default:!1}},created:function(){this.priceFormat()},watch:{price:function(t){this.priceFormat()}},methods:{priceFormat:function(){var t=this.price,e={};null!==t&&(t=parseFloat(t),t=String(t).split("."),e.first=t[0],e.second=t[1],this.priceSlice=e)}}},o=(n(443),n(9)),component=Object(o.a)(r,(function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("span",{class:(t.lineThrough?"line-through":"")+"price-format",style:{color:t.color,"font-weight":t.weight}},[t.showSubscript?n("span",{style:{"font-size":t.subscriptSize+"px","margin-right":"1px"}},[t._v("¥")]):t._e(),t._v(" "),n("span",{style:{"font-size":t.firstSize+"px","margin-right":"1px"}},[t._v(t._s(t.priceSlice.first))]),t._v(" "),t.priceSlice.second?n("span",{style:{"font-size":t.secondSize+"px"}},[t._v("."+t._s(t.priceSlice.second))]):t._e()])}),[],!1,null,null,null);e.default=component.exports},443:function(t,e,n){"use strict";n(441)},444:function(t,e,n){var r=n(13)(!1);r.push([t.i,".price-format{display:flex;align-items:baseline}",""]),t.exports=r},449:function(t,e,n){"use strict";n.r(e);n(437),n(81),n(61),n(24),n(100),n(80),n(99);var r=6e4,o=36e5,c=24*o;function l(t){return(0+t.toString()).slice(-2)}var d={components:{},props:{isSlot:{type:Boolean,default:!1},time:{type:Number,default:0},format:{type:String,default:"hh:mm:ss"},autoStart:{type:Boolean,default:!0}},watch:{time:{immediate:!0,handler:function(t){t&&this.reset()}}},data:function(){return{timeObj:{},formateTime:0}},created:function(){},computed:{},methods:{createTimer:function(t){return setTimeout(t,100)},isSameSecond:function(t,e){return Math.floor(t)===Math.floor(e)},start:function(){this.counting||(this.counting=!0,this.endTime=Date.now()+1e3*this.remain,this.setTimer())},setTimer:function(){var t=this;this.tid=this.createTimer((function(){var e=t.getRemain();t.isSameSecond(e,t.remain)&&0!==e||t.setRemain(e),0!==t.remain&&t.setTimer()}))},getRemain:function(){return Math.max(this.endTime-Date.now(),0)},pause:function(){this.counting=!1,clearTimeout(this.tid)},reset:function(){this.pause(),this.remain=this.time,this.setRemain(this.remain),this.autoStart&&this.start()},setRemain:function(t){var e=this.format;this.remain=t;var time,n=(time=t,{days:Math.floor(time/c),hours:l(Math.floor(time%c/o)),minutes:l(Math.floor(time%o/r)),seconds:l(Math.floor(time%r/1e3))});this.formateTime=function(t,e){var n=e.days,r=e.hours,o=e.minutes,c=e.seconds;return-1!==t.indexOf("dd")&&(t=t.replace("dd",n)),-1!==t.indexOf("hh")&&(t=t.replace("hh",l(r))),-1!==t.indexOf("mm")&&(t=t.replace("mm",l(o))),-1!==t.indexOf("ss")&&(t=t.replace("ss",l(c))),t}(e,n),this.$emit("change",n),0===t&&(this.pause(),this.$emit("finish"))}}},f=n(9),component=Object(f.a)(d,(function(){var t=this,e=t.$createElement,n=t._self._c||e;return t.time>=0?n("div",[n("client-only",[t.isSlot?t._t("default"):n("span",[t._v(t._s(t.formateTime))])],2)],1):t._e()}),[],!1,null,null,null);e.default=component.exports},469:function(t,e,n){"use strict";var r=n(7),o=n(102).findIndex,c=n(186),l="findIndex",d=!0;l in[]&&Array(1).findIndex((function(){d=!1})),r({target:"Array",proto:!0,forced:d},{findIndex:function(t){return o(this,t,arguments.length>1?arguments[1]:void 0)}}),c(l)},495:function(t,e,n){var content=n(515);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,n(14).default)("08771ebd",content,!0,{sourceMap:!1})},514:function(t,e,n){"use strict";n(495)},515:function(t,e,n){var r=n(13)(!1);r.push([t.i,".seckill .seckill-header .count-down .item[data-v-784969e4]{width:38px;height:20px;background:#ff2c3c;text-align:center;line-height:20px;border-radius:4px}.seckill[data-v-784969e4]  .el-carousel__indicator .el-carousel__button{background-color:#e5e5e5}.seckill[data-v-784969e4]  .el-carousel__indicator.is-active .el-carousel__button{background-color:#ff2c3c}.seckill .goods-list .goods-item[data-v-784969e4]{width:216px}.seckill .goods-list .goods-item~.goods-item[data-v-784969e4]{margin-left:16px}.seckill .goods-list .goods-item .goods-img[data-v-784969e4]{width:100%;height:0;padding-top:100%;position:relative}.seckill .goods-list .goods-item .goods-img[data-v-784969e4]  .el-image{position:absolute;width:100%;height:100%;left:0;top:0}.seckill .goods-list .goods-item .name[data-v-784969e4]{line-height:20px;height:40px}",""]),t.exports=r},518:function(t,e,n){"use strict";n.r(e);n(61),n(469);var r={components:{},props:{list:{type:Array,default:function(){return[]}}},data:function(){return{active:-1,goodsList:[],countTime:0,timeData:{},pageSize:5}},methods:{refresh:function(){this.$emit("refreshhome")},onChangeDate:function(t){var e={};for(var n in t)"milliseconds"!==n&&(e[n]=("0"+t[n]).slice(-2));this.timeData=e}},watch:{list:{handler:function(t){var e=t.findIndex((function(t){return 1==t.status}));-1==e&&(e=t.findIndex((function(t){return 0==t.status}))),-1==e&&(e=t.length-1),this.active=e,this.goodsList=t[e].goods,this.countTime=t[e].end_time_int-Date.now()/1e3},immediate:!0}},computed:{swiperSize:function(){return console.log(Math.ceil(this.goodsList.length/this.pageSize)),Math.ceil(this.goodsList.length/this.pageSize)},getSwiperList:function(){var t=this;return function(e){return t.goodsList.slice(e*t.pageSize,(e+1)*t.pageSize)}}}},o=(n(514),n(9)),component=Object(o.a)(r,(function(){var t=this,e=t.$createElement,n=t._self._c||e;return t.goodsList.length?n("div",{staticClass:"seckill card"},[n("div",{staticClass:"seckill-header flex"},[n("div",{staticClass:"title font-size-20"},[t._v("限时秒杀")]),t._v(" "),n("div",{staticClass:"flex-1 flex"},[n("i",{staticClass:"\n                    el-icon-alarm-clock\n                    primary\n                    font-size-24\n                    m-l-20 m-r-10\n                "}),t._v(" "),n("count-down",{attrs:{time:t.countTime,"is-slot":!0},on:{change:t.onChangeDate,finish:t.refresh}},[n("div",{staticClass:"flex row-center count-down xs"},[n("div",{staticClass:"item white"},[t._v(t._s(t.timeData.hours)+"时")]),t._v(" "),n("div",{staticClass:"white",staticStyle:{margin:"0 4px"}},[t._v(":")]),t._v(" "),n("div",{staticClass:"item white"},[t._v(t._s(t.timeData.minutes)+"分")]),t._v(" "),n("div",{staticClass:"white",staticStyle:{margin:"0 4px"}},[t._v(":")]),t._v(" "),n("div",{staticClass:"item white"},[t._v(t._s(t.timeData.seconds)+"秒")])])])],1),t._v(" "),n("nuxt-link",{staticClass:"more lighter",attrs:{to:"/seckill"}},[t._v("更多 "),n("i",{staticClass:"el-icon-arrow-right"})])],1),t._v(" "),n("div",{staticClass:"seckill-list m-t-16"},[n("el-carousel",{attrs:{interval:3e3,arrow:"never",height:"320px","indicator-position":"outside"}},t._l(t.swiperSize,(function(e,r){return n("el-carousel-item",{key:r},[n("div",{staticClass:"goods-list flex"},t._l(t.getSwiperList(r),(function(e,r){return n("nuxt-link",{key:r,staticClass:"goods-item",attrs:{to:"/goods_details/"+e.goods_id}},[n("div",{staticClass:"goods-img"},[n("el-image",{attrs:{src:e.goods_image,fit:"cover",alt:""}})],1),t._v(" "),n("div",{staticClass:"name line-2 m-t-10"},[t._v("\n                            "+t._s(e.goods_name)+"\n                        ")]),t._v(" "),n("div",{staticClass:"price flex col-baseline"},[n("div",{staticClass:"primary m-r-8"},[n("price-formate",{attrs:{price:e.seckill_price,"first-size":16}})],1),t._v(" "),n("div",{staticClass:"muted sm line-through"},[n("price-formate",{attrs:{price:e.seckill_total}})],1)]),t._v(" "),n("div",{staticClass:"muted xs m-t-10"},[t._v("\n                            "+t._s(e.seckill_total)+"人购买\n                        ")])])})),1)])})),1)],1)]):t._e()}),[],!1,null,"784969e4",null);e.default=component.exports;installComponents(component,{CountDown:n(449).default,PriceFormate:n(442).default})}}]);