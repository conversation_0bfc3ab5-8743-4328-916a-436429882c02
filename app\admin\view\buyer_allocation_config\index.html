{extend name="layout"/}
{block name="content"}
<div class="layui-card">
    <div class="layui-card-header">采购人员分配配置</div>
    <div class="layui-card-body">
        <div class="layui-form toolbar">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <select name="tier_level" lay-search lay-filter="search">
                        <option value="">全部商家等级</option>
                        <option value="0">0元入驻</option>
                        <option value="1">商家会员</option>
                        <option value="2">实力厂商</option>
                    </select>
                </div>
                <div class="layui-inline">
                    <button class="layui-btn layui-btn-sm" lay-submit lay-filter="search">搜索</button>
                    <button class="layui-btn layui-btn-sm layui-btn-primary" lay-event="reset">重置</button>
                </div>
                 <div class="layui-inline">
                    <button class="layui-btn layui-btn-sm layui-btn-normal" lay-event="add">添加配置</button>
                </div>
            </div>
        </div>
        <table id="dataTable" lay-filter="dataTable"></table>
    </div>
</div>

<script type="text/html" id="toolbar">
    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
</script>

<script type="text/html" id="tierLevelTpl">
    {{#  if(d.tier_level == 0){ }}
    <span>0元入驻</span>
    {{#  } else if(d.tier_level == 1){ }}
    <span>商家会员</span>
    {{#  } else if(d.tier_level == 2){ }}
    <span>实力厂商</span>
    {{#  } }}
</script>

<script>
layui.use(['table', 'form', 'layer'], function () {
    var table = layui.table;
    var form = layui.form;
    var layer = layui.layer;
    var $ = layui.$;

    // 渲染表格
    var insTable = table.render({
        elem: '#dataTable',
        url: '{:url("index")}',
        page: true,
        limit: 15,
        limits: [15, 30, 50, 100],
        cols: [[
            {field: 'id', title: 'ID', width: 80, align: 'center'},
            {field: 'tier_level', title: '商家等级', align: 'center', templet: '#tierLevelTpl'},
            {field: 'total_buyers', title: '总采购数', align: 'center'},
            {field: 'level_1_ratio', title: '一级比例(%)', align: 'center'},
            {field: 'level_2_ratio', title: '二级比例(%)', align: 'center'},
            {field: 'level_3_ratio', title: '三级比例(%)', align: 'center'},
            {field: 'create_time', title: '创建时间', align: 'center', templet: "<div>{{layui.util.toDateString(d.create_time*1000, 'yyyy-MM-dd HH:mm:ss')}}</div>"},
            {field: 'update_time', title: '更新时间', align: 'center', templet: "<div>{{layui.util.toDateString(d.update_time*1000, 'yyyy-MM-dd HH:mm:ss')}}</div>"},
            {fixed: 'right', title: '操作', width: 160, align: 'center', toolbar: '#toolbar'}
        ]],
        done: function () {
            // 清理搜索条件
            $('button[lay-event="reset"]').click(function(){
                $('.toolbar select').val('');
                form.render('select');
                table.reload('dataTable', { where: {}, page: { curr: 1 } });
            });
        }
    });

    // 搜索
    form.on('submit(search)', function (data) {
        insTable.reload({ where: data.field, page: { curr: 1 } });
        return false;
    });

    // 工具栏事件
    $('.toolbar').on('click', '.layui-btn[lay-event]', function(){
        var type = $(this).attr('lay-event');
        if(type === 'add'){
            openForm();
        }
    });

    // 行内工具栏事件
    table.on('tool(dataTable)', function (obj) {
        var data = obj.data;
        var layEvent = obj.event;

        if (layEvent === 'edit') {
            openForm(data.id);
        } else if (layEvent === 'del') {
            layer.confirm('确定要删除此配置吗？', {icon: 3, title: '提示'}, function (index) {
                $.post('{:url("delete")}', {id: data.id}, function (res) {
                    if (res.code === 0) {
                        layer.msg(res.msg, {icon: 1});
                        obj.del();
                    } else {
                        layer.msg(res.msg, {icon: 2});
                    }
                });
                layer.close(index);
            });
        }
    });

    // 弹出表单
    function openForm(id) {
        var url = id ? '{:url("edit")}?id=' + id : '{:url("add")}';
        var title = id ? '编辑配置' : '添加配置';
        layer.open({
            type: 2,
            title: title,
            content: url,
            area: ['600px', '520px'],
            end: function() {
                insTable.reload();
            }
        });
    }
});
</script>
{/block}
