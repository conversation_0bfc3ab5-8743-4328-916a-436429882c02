{layout name="layout1" /}

<style>
    .page-table {
        margin-top: 15px;
    }
    .page-table .layui-table-cell {
        height: auto;
        padding: 8px 15px;
    }
    .edit-input {
        border: none;
        background: transparent;
        width: 100%;
        padding: 5px;
    }
    .edit-input:focus {
        border: 1px solid #1E9FFF;
        background: #fff;
        border-radius: 2px;
    }
    .operation-btn {
        margin-right: 5px;
    }
    .status-indicator {
        display: inline-block;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        margin-right: 5px;
    }
    .status-saved {
        background-color: #5FB878;
    }
    .status-editing {
        background-color: #FFB800;
    }
    .status-error {
        background-color: #FF5722;
    }
</style>

<div class="wrapper">
    <div class="layui-card">
        <!-- 操作提示 -->
        <div class="layui-card-body">
            <div class="layui-collapse like-layui-collapse" style="border:1px dashed #c4c4c4">
                <div class="layui-colla-item">
                    <h2 class="layui-colla-title like-layui-colla-title" style="background-color: #fff">操作提示</h2>
                    <div class="layui-colla-content layui-show">
                        <p>*配置广告跳转页面的路径和名称，用于广告管理中的页面选择</p>
                        <p>*页面路径支持小程序页面路径和外部链接</p>
                        <p>*Tab页设置用于标识是否为底部导航页面</p>
                        <p>*修改后会自动保存，无需手动点击保存按钮</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 工具栏 -->
        <div class="layui-card-body">
            <div class="layui-btn-group">
                <button type="button" class="layui-btn layui-btn-sm" id="add-page">
                    <i class="layui-icon layui-icon-add-1"></i> 添加页面
                </button>
                <button type="button" class="layui-btn layui-btn-warm layui-btn-sm" id="reset-config">
                    <i class="layui-icon layui-icon-refresh"></i> 重置为默认
                </button>
                <button type="button" class="layui-btn layui-btn-danger layui-btn-sm" id="clear-all">
                    <i class="layui-icon layui-icon-delete"></i> 清空所有
                </button>
            </div>

            <div style="float: right;">
                <span id="save-status">
                    <span class="status-indicator status-saved"></span>
                    <span id="status-text">已开启实时保存</span>
                </span>
            </div>
            <div style="clear: both;"></div>
        </div>

        <!-- 页面列表 -->
        <div class="layui-card-body page-table">
            <table class="layui-table" lay-skin="line">
                <thead>
                    <tr>
                        <th width="50">序号</th>
                        <th width="200">页面名称</th>
                        <th>页面路径</th>
                        <th width="100">Tab页面</th>
                        <th width="150">操作</th>
                    </tr>
                </thead>
                <tbody id="pages-tbody">
                    {volist name="mobile_pages" id="page" key="index"}
                    <tr data-index="{$index}">
                        <td>{$index + 1}</td>
                        <td>
                            <input type="text" class="edit-input page-name" value="{$page.name}" placeholder="请输入页面名称" data-field="name">
                        </td>
                        <td>
                            <input type="text" class="edit-input page-path" value="{$page.path}" placeholder="请输入页面路径" data-field="path">
                        </td>
                        <td>
                            <input type="checkbox" class="page-tab" {if condition="$page.is_tab == 1"}checked{/if} lay-skin="switch" lay-text="是|否" data-field="is_tab">
                        </td>
                        <td>
                            <button class="layui-btn layui-btn-xs layui-btn-normal operation-btn edit-btn">
                                <i class="layui-icon layui-icon-edit"></i> 编辑
                            </button>
                            <button class="layui-btn layui-btn-xs layui-btn-danger operation-btn delete-btn">
                                <i class="layui-icon layui-icon-delete"></i> 删除
                            </button>
                        </td>
                    </tr>
                    {/volist}
                </tbody>
            </table>

            <div id="empty-tip" style="text-align: center; padding: 40px; color: #999; display: none;">
                暂无页面配置，点击上方"添加页面"按钮开始配置
            </div>
        </div>
    </div>
</div>

<script>
layui.use(['form', 'layer'], function(){
    var form = layui.form;
    var layer = layui.layer;
    var $ = layui.$;

    var saveTimeout;
    var pageIndex = {$mobile_pages|count}; // 当前页面索引

    // 页面加载完成后的初始化
    $(document).ready(function() {
        console.log('页面初始化完成');
        form.render();
        updateEmptyTip();
        bindEvents();
    });

    // 绑定所有事件
    function bindEvents() {
        // 添加页面
        $('#add-page').off('click').on('click', function(){
            addNewPage();
        });

        // 重置配置
        $('#reset-config').off('click').on('click', function(){
            resetConfig();
        });

        // 清空所有
        $('#clear-all').off('click').on('click', function(){
            clearAllPages();
        });

        // 删除单个页面
        $(document).off('click', '.delete-btn').on('click', '.delete-btn', function(){
            deletePage($(this));
        });

        // 编辑按钮
        $(document).off('click', '.edit-btn').on('click', '.edit-btn', function(){
            toggleEdit($(this));
        });

        // 输入框实时保存
        $(document).off('input change', '.edit-input, .page-tab').on('input change', '.edit-input, .page-tab', function(){
            updateStatus('editing');
            autoSave();
        });

        console.log('事件绑定完成');
    }

    // 添加新页面
    function addNewPage() {
        var newRow = `
            <tr data-index="${pageIndex}">
                <td>${$('#pages-tbody tr').length + 1}</td>
                <td>
                    <input type="text" class="edit-input page-name" value="" placeholder="请输入页面名称" data-field="name">
                </td>
                <td>
                    <input type="text" class="edit-input page-path" value="" placeholder="请输入页面路径" data-field="path">
                </td>
                <td>
                    <input type="checkbox" class="page-tab" lay-skin="switch" lay-text="是|否" data-field="is_tab">
                </td>
                <td>
                    <button class="layui-btn layui-btn-xs layui-btn-normal operation-btn edit-btn">
                        <i class="layui-icon layui-icon-edit"></i> 编辑
                    </button>
                    <button class="layui-btn layui-btn-xs layui-btn-danger operation-btn delete-btn">
                        <i class="layui-icon layui-icon-delete"></i> 删除
                    </button>
                </td>
            </tr>
        `;

        $('#pages-tbody').append(newRow);
        pageIndex++;

        // 重新渲染表单元素
        form.render();
        updateEmptyTip();
        updateRowNumbers();

        layer.msg('页面添加成功', {icon: 1, time: 1000});
    }

    // 删除页面
    function deletePage($btn) {
        var $row = $btn.closest('tr');
        var pageName = $row.find('.page-name').val() || '未命名页面';

        layer.confirm('确定要删除页面 "' + pageName + '" 吗？', {
            btn: ['确定', '取消']
        }, function(index){
            $row.remove();
            updateEmptyTip();
            updateRowNumbers();
            autoSave();
            layer.close(index);
            layer.msg('删除成功', {icon: 1, time: 1000});
        });
    }

    // 切换编辑状态
    function toggleEdit($btn) {
        var $row = $btn.closest('tr');
        var $inputs = $row.find('.edit-input');
        var isEditing = $btn.hasClass('editing');

        if (isEditing) {
            // 保存编辑
            $inputs.attr('readonly', true);
            $btn.removeClass('editing').html('<i class="layui-icon layui-icon-edit"></i> 编辑');
            autoSave();
        } else {
            // 开始编辑
            $inputs.attr('readonly', false).first().focus();
            $btn.addClass('editing').html('<i class="layui-icon layui-icon-ok"></i> 保存');
        }
    }

    // 清空所有页面
    function clearAllPages() {
        layer.confirm('确定要清空所有页面配置吗？', {
            btn: ['确定', '取消']
        }, function(index){
            $('#pages-tbody').empty();
            updateEmptyTip();
            autoSave();
            layer.close(index);
            layer.msg('清空成功', {icon: 1, time: 1000});
        });
    }

    // 重置为默认配置
    function resetConfig() {
        layer.confirm('确定要重置为默认配置吗？这将清空当前所有配置。', {
            btn: ['确定', '取消']
        }, function(index){
            $.post('{:url("admin/setting.LinkConfig/reset")}', {}, function(res){
                if(res.code == 1) {
                    layer.msg('重置成功，正在刷新页面...', {icon: 1, time: 1000}, function(){
                        location.reload();
                    });
                } else {
                    layer.msg(res.msg || '重置失败', {icon: 2});
                }
            }, 'json');
            layer.close(index);
        });
    }

    // 更新空状态提示
    function updateEmptyTip() {
        var rowCount = $('#pages-tbody tr').length;
        if (rowCount === 0) {
            $('#empty-tip').show();
            $('.page-table table').hide();
        } else {
            $('#empty-tip').hide();
            $('.page-table table').show();
        }
    }

    // 更新行号
    function updateRowNumbers() {
        $('#pages-tbody tr').each(function(index) {
            $(this).find('td:first').text(index + 1);
        });
    }

    // 更新状态指示器
    function updateStatus(status) {
        var $indicator = $('.status-indicator');
        var $text = $('#status-text');

        $indicator.removeClass('status-saved status-editing status-error');

        switch(status) {
            case 'editing':
                $indicator.addClass('status-editing');
                $text.text('正在编辑...');
                break;
            case 'saving':
                $indicator.addClass('status-editing');
                $text.text('正在保存...');
                break;
            case 'saved':
                $indicator.addClass('status-saved');
                $text.text('保存成功');
                setTimeout(function() {
                    $text.text('已开启实时保存');
                }, 2000);
                break;
            case 'error':
                $indicator.addClass('status-error');
                $text.text('保存失败');
                break;
        }
    }

    // 自动保存
    function autoSave() {
        clearTimeout(saveTimeout);
        updateStatus('saving');

        saveTimeout = setTimeout(function() {
            saveConfig();
        }, 1000);
    }

    // 保存配置
    function saveConfig() {
        console.log('开始保存配置');

        var pages = [];
        $('#pages-tbody tr').each(function(){
            var $row = $(this);
            var name = $row.find('.page-name').val();
            var path = $row.find('.page-path').val();
            var is_tab = $row.find('.page-tab').is(':checked') ? 1 : 0;

            if(name && path) {
                pages.push({
                    name: name,
                    path: path,
                    is_tab: is_tab
                });
            }
        });

        console.log('收集到的页面数据:', pages);

        $.post('{:url("admin/setting.LinkConfig/save")}', {
            mobile_pages: pages
        }, function(res){
            console.log('保存响应:', res);
            if(res.code == 1) {
                updateStatus('saved');
            } else {
                updateStatus('error');
                layer.msg(res.msg || '保存失败', {icon: 2});
            }
        }, 'json').fail(function(){
            console.log('保存请求失败');
            updateStatus('error');
            layer.msg('网络错误，保存失败', {icon: 2});
        });
    }






});
</script>
