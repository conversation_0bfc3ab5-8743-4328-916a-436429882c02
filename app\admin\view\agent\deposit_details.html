{layout name="layout1" /}
<div class="wrapper">
    <div class="layui-card">
        <div class="layui-card-header">代理保证金明细</div>
        <div class="layui-card-body">
            <!-- 保证金信息区域 -->
            <div class="layui-form-item">
                <fieldset class="layui-elem-field layui-field-title">
                    <legend>保证金信息</legend>
                </fieldset>
            </div>

            <div class="layui-form layui-form-pane">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">用户信息</label>
                        <div class="layui-input-inline" style="width: 300px;">
                            <div class="layui-form-mid">{$user.nickname|default=''} ({$user.sn|default=''})
                                {$user.mobile|default=''}</div>
                        </div>
                    </div>
                </div>

                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">保证金金额</label>
                        <div class="layui-input-inline">
                            <div class="layui-form-mid">{$deposit.amount|default='0.00'} 元</div>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">当前余额</label>
                        <div class="layui-input-inline">
                            <div class="layui-form-mid">{$deposit.current_balance|default='0.00'} 元</div>
                        </div>
                    </div>
                </div>

                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">支付时间</label>
                        <div class="layui-input-inline">
                            <div class="layui-form-mid">{$deposit.payment_date|date='Y-m-d H:i:s'|default=''}</div>
                        </div>
                    </div>
                </div>

                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">状态</label>
                        <div class="layui-input-inline">
                            <div class="layui-form-mid">
                                {if $deposit.status == 0}未支付
                                {elseif $deposit.status == 1}已支付
                                {elseif $deposit.status == 2}公示期结束(可退)
                                {elseif $deposit.status == 3}退款申请中
                                {elseif $deposit.status == 4}已退款
                                {elseif $deposit.status == 5}退款失败
                                {else}未知状态
                                {/if}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 保证金明细区域 -->
            <div class="layui-form-item">
                <fieldset class="layui-elem-field layui-field-title">
                    <legend>保证金明细</legend>
                </fieldset>
            </div>

            <!-- 搜索表单 -->
            <div class="layui-form layui-form-pane" style="margin-bottom: 15px;">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">变动类型</label>
                        <div class="layui-input-inline">
                            <select name="change_type" lay-filter="change_type">
                                <option value="">全部</option>
                                <option value="1">缴纳</option>
                                <option value="2">增加</option>
                                <option value="3">扣除</option>
                                <option value="4">退还</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">变动日期</label>
                        <div class="layui-input-inline">
                            <input type="text" name="start_date" id="start_date" placeholder="开始日期" autocomplete="off"
                                class="layui-input">
                        </div>
                        <div class="layui-form-mid">-</div>
                        <div class="layui-input-inline">
                            <input type="text" name="end_date" id="end_date" placeholder="结束日期" autocomplete="off"
                                class="layui-input">
                        </div>
                    </div>
                </div>

                <div class="layui-form-item">
                    <div class="layui-inline">
                        <button class="layui-btn" lay-submit lay-filter="search">搜索</button>
                        <button class="layui-btn layui-btn-primary" lay-submit lay-filter="reset">重置</button>
                        <button class="layui-btn layui-btn-normal" id="adjust-deposit">调整保证金</button>
                    </div>
                </div>
            </div>

            <table id="detail-lists" lay-filter="detail-lists"></table>

            <script type="text/html" id="change-type-tpl">
                {{# if(d.change_type == 1){ }}
                <span class="layui-badge layui-bg-blue">缴纳</span>
                {{# } else if(d.change_type == 2){ }}
                <span class="layui-badge layui-bg-green">增加</span>
                {{# } else if(d.change_type == 3){ }}
                <span class="layui-badge layui-bg-orange">扣除</span>
                {{# } else if(d.change_type == 4){ }}
                <span class="layui-badge layui-bg-gray">退还</span>
                {{# } else { }}
                <span class="layui-badge layui-bg-black">未知</span>
                {{# } }}
            </script>

            <script type="text/html" id="deposit-change-tpl">
                {{# if(parseFloat(d.deposit_change) > 0){ }}
                <span style="color: green;">+{{d.deposit_change}}</span>
                {{# } else { }}
                <span style="color: red;">{{d.deposit_change}}</span>
                {{# } }}
            </script>
        </div>
    </div>
</div>

<script>
    layui.use(['table', 'form', 'laydate', 'layer'], function () {
        var table = layui.table;
        var form = layui.form;
        var laydate = layui.laydate;
        var layer = layui.layer;
        var $ = layui.$;

        // 日期选择器
        laydate.render({
            elem: '#start_date'
        });
        laydate.render({
            elem: '#end_date'
        });

        // 数据表格渲染
        table.render({
            elem: '#detail-lists'
            , url: '{:url("agent.agent/depositDetails")}' //数据接口
            , method: 'post'
            , where: {
                deposit_id: '{$deposit_id}',
                user_id: '{$user_id}'
            }
            , page: true //开启分页
            , limit: 10
            , limits: [10, 20, 50, 100]
            , cols: [[ //表头
                { field: 'id', title: 'ID', width: 80 },
                { field: 'sn', title: '明细单号', width: 180 },
                { field: 'change_type', title: '变动类型', width: 100, templet: '#change-type-tpl' },
                { field: 'deposit_change', title: '变动金额', width: 120, templet: '#deposit-change-tpl' },
                { field: 'reason', title: '变动原因', width: 200 },
                { field: 'remark', title: '备注', width: 200 },
                { field: 'change_date', title: '变动日期', width: 120 },
                { field: 'created_at', title: '创建时间', width: 180 }
            ]]
            , text: { none: '暂无数据！' }
            , parseData: function (res) { //将原始数据解析成 table 组件所规定的数据
                console.log('API返回数据:', res);
                // 检查返回的数据结构
                if (res.data && res.data.lists) {
                    return {
                        "code": res.code,
                        "msg": res.msg,
                        "count": res.data.count, //解析数据长度
                        "data": res.data.lists, //解析数据列表
                    };
                } else {
                    // 如果数据结构不符合预期，尝试直接使用返回的数据
                    return {
                        "code": res.code || 1,
                        "msg": res.msg || '',
                        "count": res.count || 0,
                        "data": res.lists || []
                    };
                }
            },
            response: {
                statusCode: 1
            }
            , done: function (res, curr, count) {
                console.log('表格渲染完成:', res, curr, count);
                // 解决操作栏因为内容过多换行问题
                $(".layui-table-main tr").each(function (index, val) {
                    $($(".layui-table-fixed-l .layui-table-body tbody tr")[index]).height($(val).height());
                    $($(".layui-table-fixed-r .layui-table-body tbody tr")[index]).height($(val).height());
                });
            }
            , error: function(e) {
                console.error('表格渲染错误:', e);
                layer.msg('加载数据失败，请刷新页面重试');
            }
        });

        // 搜索
        form.on('submit(search)', function (data) {
            var field = data.field;
            field.deposit_id = '{$deposit_id}';
            field.user_id = '{$user_id}';

            table.reload('detail-lists', {
                where: field
            });
            return false;
        });

        // 重置
        form.on('submit(reset)', function () {
            $('#start_date').val('');
            $('#end_date').val('');
            $('select[name=change_type]').val('');
            form.render('select');

            table.reload('detail-lists', {
                where: {
                    deposit_id: '{$deposit_id}',
                    user_id: '{$user_id}',
                    change_type: '',
                    start_date: '',
                    end_date: ''
                }
            });
            return false;
        });

        // 调整保证金
        $('#adjust-deposit').click(function () {
            // 打开调整保证金弹窗
            layer.open({
                type: 2,
                title: '调整代理保证金',
                area: ['600px', '500px'],
                content: '{:url("agent.agent/adjustDeposit")}?deposit_id={$deposit_id}&user_id={$user_id}&is_popup=1',
                maxmin: true
            });
        });
    });
</script>