<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Cr\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * 机器人对话流信息
 *
 * @method string getBotFlowId() 获取对话流ID
 * @method void setBotFlowId(string $BotFlowId) 设置对话流ID
 * @method string getBotFlowName() 获取对话流名称
 * @method void setBotFlowName(string $BotFlowName) 设置对话流名称
 * @method array getPhonePoolList() 获取号码组信息列表
 * @method void setPhonePoolList(array $PhonePoolList) 设置号码组信息列表
 */
class BotFlow extends AbstractModel
{
    /**
     * @var string 对话流ID
     */
    public $BotFlowId;

    /**
     * @var string 对话流名称
     */
    public $BotFlowName;

    /**
     * @var array 号码组信息列表
     */
    public $PhonePoolList;

    /**
     * @param string $BotFlowId 对话流ID
     * @param string $BotFlowName 对话流名称
     * @param array $PhonePoolList 号码组信息列表
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("BotFlowId",$param) and $param["BotFlowId"] !== null) {
            $this->BotFlowId = $param["BotFlowId"];
        }

        if (array_key_exists("BotFlowName",$param) and $param["BotFlowName"] !== null) {
            $this->BotFlowName = $param["BotFlowName"];
        }

        if (array_key_exists("PhonePoolList",$param) and $param["PhonePoolList"] !== null) {
            $this->PhonePoolList = [];
            foreach ($param["PhonePoolList"] as $key => $value){
                $obj = new PhonePool();
                $obj->deserialize($value);
                array_push($this->PhonePoolList, $obj);
            }
        }
    }
}
