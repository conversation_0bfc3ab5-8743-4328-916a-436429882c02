/**
 * 商品视频管理增强版
 * 解决视频删除功能缺失问题
 * <AUTHOR> Assistant
 * @version 2.0
 */

(function() {
    'use strict';
    
    // 配置选项
    var config = {
        videoContainer: '#videoContainer',
        videoPreviewArea: '#videoPreviewArea',
        videoPreview: '#videoPreview',
        videoFileName: '#videoFileName',
        videoFileSize: '#videoFileSize',
        replaceBtn: '#replaceVideoBtn',
        deleteBtn: '#deleteVideoBtn',
        addVideoBtn: '#video',
        maxFileSize: 4 * 1024 * 1024, // 4MB
        allowedFormats: ['mp4', 'avi', 'mov', 'wmv', 'flv'],
        debug: false
    };

    // 工具函数
    var utils = {
        log: function(message, data) {
            if (config.debug) {
                console.log('[VideoManager] ' + message, data || '');
            }
        },
        
        formatFileSize: function(bytes) {
            if (bytes === 0) return '0 Bytes';
            var k = 1024;
            var sizes = ['Bytes', 'KB', 'MB', 'GB'];
            var i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        },
        
        getFileExtension: function(filename) {
            return filename.split('.').pop().toLowerCase();
        },
        
        isValidVideoFormat: function(filename) {
            var ext = this.getFileExtension(filename);
            return config.allowedFormats.indexOf(ext) > -1;
        }
    };

    // 视频管理器主类
    var VideoManager = {
        // 当前视频信息
        currentVideo: {
            url: null,
            filename: null,
            size: null
        },

        // 初始化
        init: function() {
            this.bindEvents();
            this.checkExistingVideo();
            this.handleNativeVideoUpload();
            utils.log('视频管理器初始化完成');
        },

        // 绑定事件
        bindEvents: function() {
            var self = this;
            
            // 删除视频按钮
            $(document).on('click', config.deleteBtn, function() {
                self.deleteVideo();
            });
            
            // 替换视频按钮
            $(document).on('click', config.replaceBtn, function() {
                self.replaceVideo();
            });
            
            // 监听视频上传完成事件
            $(document).on('videoUploaded', function(e, data) {
                self.handleVideoUploaded(data);
            });
            
            // 监听原有的视频选择事件
            $(document).on('click', config.addVideoBtn, function() {
                self.handleVideoSelect();
            });
        },

        // 检查现有视频
        checkExistingVideo: function() {
            var $container = $(config.videoContainer);
            var $existingVideo = $container.find('.upload-video-div');

            if ($existingVideo.length > 0) {
                // 如果存在旧版视频div，显示管理界面
                var videoSrc = $existingVideo.find('video').attr('src') ||
                              $existingVideo.find('source').attr('src');

                if (videoSrc) {
                    this.showVideoPreview({
                        url: videoSrc,
                        filename: this.extractFilenameFromUrl(videoSrc),
                        size: null
                    });
                }
            } else {
                // 确保添加按钮可见
                var $addButton = $container.find('.like-upload-video');
                if ($addButton.length > 0) {
                    $addButton.show();
                } else {
                    // 如果添加按钮不存在，重新创建
                    var addButtonHtml = '<div class="like-upload-video">' +
                        '<div class="upload-image-elem">' +
                        '<a class="add-upload-video" id="video"> + 添加视频</a>' +
                        '</div>' +
                        '</div>';
                    $container.prepend(addButtonHtml);
                }

                // 确保预览区域隐藏
                $(config.videoPreviewArea).hide();
            }
        },

        // 从URL提取文件名
        extractFilenameFromUrl: function(url) {
            if (!url) return '未知文件';
            var parts = url.split('/');
            return parts[parts.length - 1] || '未知文件';
        },

        // 处理视频上传完成
        handleVideoUploaded: function(data) {
            utils.log('视频上传完成', data);

            if (data && data.url) {
                this.showVideoPreview({
                    url: data.url,
                    filename: data.filename || this.extractFilenameFromUrl(data.url),
                    size: data.size || null
                });
            }
        },

        // 处理原生视频上传完成（兼容现有系统）
        handleNativeVideoUpload: function() {
            var self = this;

            // 监听DOM变化，检测视频上传
            var observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'childList') {
                        mutation.addedNodes.forEach(function(node) {
                            if (node.nodeType === 1) {
                                var $node = $(node);

                                // 检测是否是视频相关的节点
                                if ($node.hasClass('upload-video-div') ||
                                    $node.find('.upload-video-div').length > 0 ||
                                    $node.find('video').length > 0) {

                                    utils.log('检测到视频DOM变化', node);

                                    // 延迟处理，确保DOM完全更新
                                    setTimeout(function() {
                                        self.checkAndEnhanceVideo();
                                    }, 500);
                                }
                            }
                        });
                    }
                });
            });

            // 开始观察视频容器
            var container = document.querySelector(config.videoContainer);
            if (container) {
                observer.observe(container, {
                    childList: true,
                    subtree: true,
                    attributes: true,
                    attributeOldValue: true
                });
                utils.log('开始监听视频容器DOM变化');
            }
        },

        // 检查并增强视频显示
        checkAndEnhanceVideo: function() {
            var $container = $(config.videoContainer);

            // 检查是否有原生的视频div
            var $videoDiv = $container.find('.upload-video-div');
            if ($videoDiv.length > 0) {
                utils.log('发现原生视频div，进行增强');

                // 提取视频信息
                var $video = $videoDiv.find('video');
                if ($video.length > 0) {
                    var videoSrc = $video.attr('src') || $video.find('source').attr('src');
                    if (videoSrc) {
                        // 隐藏原生div
                        $videoDiv.hide();

                        // 显示增强的预览
                        this.showVideoPreview({
                            url: videoSrc,
                            filename: this.extractFilenameFromUrl(videoSrc),
                            size: null
                        });

                        return;
                    }
                }
            }

            // 检查是否视频区域为空但应该显示添加按钮
            var $addButton = $container.find('.like-upload-video');
            var $previewArea = $(config.videoPreviewArea);

            if ($addButton.length === 0 && !$previewArea.is(':visible')) {
                utils.log('视频区域为空，恢复添加按钮');
                this.hideVideoPreview();
            }
        },

        // 处理视频选择
        handleVideoSelect: function() {
            utils.log('视频选择触发');
            // 这里可以添加额外的逻辑，比如文件格式验证
        },

        // 显示视频预览
        showVideoPreview: function(videoData) {
            this.currentVideo = videoData;

            var $container = $(config.videoContainer);
            var $previewArea = $(config.videoPreviewArea);

            // 隐藏添加按钮
            $container.find('.like-upload-video').hide();

            // 设置视频源
            $(config.videoPreview).attr('src', videoData.url);

            // 设置文件信息
            $(config.videoFileName).text(videoData.filename);
            if (videoData.size) {
                $(config.videoFileSize).text('(' + utils.formatFileSize(videoData.size) + ')');
            } else {
                $(config.videoFileSize).text('');
            }

            // 显示预览区域
            $previewArea.show();

            utils.log('视频预览显示完成', videoData);
        },

        // 隐藏视频预览
        hideVideoPreview: function() {
            var $container = $(config.videoContainer);
            var $previewArea = $(config.videoPreviewArea);

            // 确保添加按钮存在，如果不存在则重新创建
            var $addButton = $container.find('.like-upload-video');
            if ($addButton.length === 0) {
                var addButtonHtml = '<div class="like-upload-video">' +
                    '<div class="upload-image-elem">' +
                    '<a class="add-upload-video" id="video"> + 添加视频</a>' +
                    '</div>' +
                    '</div>';
                $container.prepend(addButtonHtml);
                $addButton = $container.find('.like-upload-video');
            }

            // 显示添加按钮
            $addButton.show();

            // 隐藏预览区域
            $previewArea.hide();

            // 清空视频源
            $(config.videoPreview).attr('src', '');

            // 清空当前视频信息
            this.currentVideo = {
                url: null,
                filename: null,
                size: null
            };

            utils.log('视频预览隐藏完成');
        },

        // 删除视频
        deleteVideo: function() {
            var self = this;

            layer.confirm('确定要删除这个视频吗？', {
                icon: 3,
                title: '确认删除',
                btn: ['确定', '取消']
            }, function(index) {
                // 确认删除
                self.hideVideoPreview();

                // 清除可能存在的隐藏字段
                $('input[name="video_url"]').val('');
                $('input[name="video"]').val('');

                // 移除可能存在的旧版视频div（兼容性处理）
                $(config.videoContainer).find('.upload-video-div').remove();

                // 确保添加按钮可见
                var $container = $(config.videoContainer);
                var $addButton = $container.find('.like-upload-video');
                if ($addButton.length === 0) {
                    var addButtonHtml = '<div class="like-upload-video">' +
                        '<div class="upload-image-elem">' +
                        '<a class="add-upload-video" id="video"> + 添加视频</a>' +
                        '</div>' +
                        '</div>';
                    $container.prepend(addButtonHtml);
                }
                $container.find('.like-upload-video').show();

                layer.close(index);
                layer.msg('视频已删除', {icon: 1});

                utils.log('视频删除成功');
            }, function(index) {
                // 取消删除
                layer.close(index);
            });
        },

        // 替换视频
        replaceVideo: function() {
            utils.log('触发视频替换');
            
            // 触发原有的视频选择功能
            $(config.addVideoBtn).click();
        },

        // 验证视频文件
        validateVideo: function(file) {
            var errors = [];
            
            // 检查文件格式
            if (!utils.isValidVideoFormat(file.name)) {
                errors.push('不支持的视频格式，请选择: ' + config.allowedFormats.join(', '));
            }
            
            // 检查文件大小
            if (file.size > config.maxFileSize) {
                errors.push('视频文件过大，请选择小于 ' + utils.formatFileSize(config.maxFileSize) + ' 的文件');
            }
            
            return errors;
        },

        // 获取当前视频信息
        getCurrentVideo: function() {
            return this.currentVideo;
        },

        // 设置调试模式
        setDebug: function(debug) {
            config.debug = debug;
        }
    };

    // 增强现有的视频上传功能
    var OriginalVideoUpload = {
        // 保存原有的上传成功回调
        originalCallback: null,

        // 初始化
        init: function() {
            this.enhanceExistingUpload();
            this.interceptLayuiCallbacks();
        },

        // 增强现有上传功能
        enhanceExistingUpload: function() {
            // 监听可能的上传成功事件
            $(document).on('uploadSuccess', function(e, data) {
                if (data && data.type === 'video') {
                    VideoManager.handleVideoUploaded(data);
                }
            });

            // 监听layui弹窗关闭事件
            $(document).on('layui-layer-close', function() {
                setTimeout(function() {
                    VideoManager.checkAndEnhanceVideo();
                }, 500);
            });
        },

        // 拦截layui回调
        interceptLayuiCallbacks: function() {
            // 尝试拦截layui的成功回调
            if (typeof layui !== 'undefined') {
                layui.use(['layer'], function() {
                    var layer = layui.layer;

                    // 保存原始的close方法
                    var originalClose = layer.close;

                    // 重写close方法
                    layer.close = function(index) {
                        var result = originalClose.apply(this, arguments);

                        // 延迟检查视频状态
                        setTimeout(function() {
                            VideoManager.checkAndEnhanceVideo();
                        }, 300);

                        return result;
                    };
                });
            }
        }
    };

    // 主初始化函数
    function initVideoManager() {
        utils.log('开始初始化视频管理器');

        $(document).ready(function() {
            if (typeof layui !== 'undefined') {
                layui.use(['layer'], function() {
                    VideoManager.init();
                    OriginalVideoUpload.init();

                    // 定期检查视频状态
                    setInterval(function() {
                        VideoManager.checkAndEnhanceVideo();
                    }, 2000);

                    utils.log('视频管理器初始化完成');
                });
            } else {
                // 如果layui未加载，延迟初始化
                setTimeout(initVideoManager, 1000);
            }
        });
    }

    // 暴露到全局
    window.VideoManagerEnhanced = {
        init: initVideoManager,
        VideoManager: VideoManager,
        utils: utils,
        config: config
    };

    // 自动初始化
    initVideoManager();

})();
