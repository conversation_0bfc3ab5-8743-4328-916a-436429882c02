<?php

namespace app\api\controller;

use app\common\basics\ApiBase;
use app\common\model\kefu\ChatRelation;
use app\common\server\JsonServer;
use think\facade\Db;

/**
 * 聊天关系测试控制器
 * Class ChatRelationTest
 * @package app\api\controller
 */
class ChatRelationTest extends ApiBase
{
    /**
     * 查看用户的所有聊天关系
     * @return \think\response\Json
     */
    public function getUserChatRelations()
    {
        try {
            $user_id = $this->user_id;

            // 获取所有聊天关系
            $relations = ChatRelation::where('user_id', $user_id)
                ->order('update_time desc')
                ->select()
                ->toArray();

            // 分类统计
            $kefu_relations = [];
            $user_relations = [];

            foreach ($relations as $relation) {
                if ($relation['shop_id'] > 0) {
                    // 客服聊天关系
                    $kefu_relations[] = [
                        'id' => $relation['id'],
                        'shop_id' => $relation['shop_id'],
                        'kefu_id' => $relation['kefu_id'],
                        'nickname' => $relation['nickname'],
                        'last_msg' => $relation['msg'],
                        'msg_type' => $relation['msg_type'],
                        'is_read' => $relation['is_read'],
                        'update_time' => date('Y-m-d H:i:s', $relation['update_time'])
                    ];
                } else {
                    // 用户聊天关系
                    $user_relations[] = [
                        'id' => $relation['id'],
                        'contact_user_id' => $relation['kefu_id'], // 这里存的是联系人用户ID
                        'nickname' => $relation['nickname'],
                        'last_msg' => $relation['msg'],
                        'msg_type' => $relation['msg_type'],
                        'is_read' => $relation['is_read'],
                        'update_time' => date('Y-m-d H:i:s', $relation['update_time'])
                    ];
                }
            }

            return JsonServer::success('获取成功', [
                'user_id' => $user_id,
                'total_relations' => count($relations),
                'kefu_relations' => [
                    'count' => count($kefu_relations),
                    'list' => $kefu_relations
                ],
                'user_relations' => [
                    'count' => count($user_relations),
                    'list' => $user_relations
                ]
            ]);
        } catch (\Exception $e) {
            return JsonServer::error('获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 检查特定聊天关系
     * @return \think\response\Json
     */
    public function checkChatRelation()
    {
        try {
            $contact_id = $this->request->get('contact_id/d', 0);
            $shop_id = $this->request->get('shop_id/d', 0);
            $user_id = $this->user_id;

            if (empty($contact_id)) {
                return JsonServer::error('contact_id参数不能为空');
            }

            // 查找关系记录
            $where = [
                'user_id' => $user_id,
                'kefu_id' => $contact_id,
                'shop_id' => $shop_id
            ];

            $relation = ChatRelation::where($where)->findOrEmpty();

            $result = [
                'exists' => !$relation->isEmpty(),
                'query_params' => $where,
                'relation_data' => null
            ];

            if (!$relation->isEmpty()) {
                $result['relation_data'] = [
                    'id' => $relation['id'],
                    'shop_id' => $relation['shop_id'],
                    'user_id' => $relation['user_id'],
                    'kefu_id' => $relation['kefu_id'],
                    'nickname' => $relation['nickname'],
                    'last_msg' => $relation['msg'],
                    'msg_type' => $relation['msg_type'],
                    'is_read' => $relation['is_read'],
                    'create_time' => date('Y-m-d H:i:s', $relation['create_time']),
                    'update_time' => date('Y-m-d H:i:s', $relation['update_time'])
                ];
            }

            return JsonServer::success('查询成功', $result);
        } catch (\Exception $e) {
            return JsonServer::error('查询失败: ' . $e->getMessage());
        }
    }

    /**
     * 统计聊天关系数据
     * @return \think\response\Json
     */
    public function getChatRelationStats()
    {
        try {
            $user_id = $this->user_id;

            // 统计各种类型的关系
            $stats = [
                'total_relations' => ChatRelation::where('user_id', $user_id)->count(),
                'kefu_relations' => ChatRelation::where([
                    'user_id' => $user_id,
                    ['shop_id', '>', 0]
                ])->count(),
                'user_relations' => ChatRelation::where([
                    'user_id' => $user_id,
                    'shop_id' => 0
                ])->count(),
                'unread_kefu' => ChatRelation::where([
                    'user_id' => $user_id,
                    ['shop_id', '>', 0],
                    'is_read' => 0
                ])->count(),
                'unread_user' => ChatRelation::where([
                    'user_id' => $user_id,
                    'shop_id' => 0,
                    'is_read' => 0
                ])->count()
            ];

            // 按商家分组统计客服关系
            $kefu_by_shop = ChatRelation::where([
                'user_id' => $user_id,
                ['shop_id', '>', 0]
            ])
            ->field('shop_id, COUNT(*) as count')
            ->group('shop_id')
            ->select()
            ->toArray();

            return JsonServer::success('统计成功', [
                'user_id' => $user_id,
                'stats' => $stats,
                'kefu_by_shop' => $kefu_by_shop
            ]);
        } catch (\Exception $e) {
            return JsonServer::error('统计失败: ' . $e->getMessage());
        }
    }

    /**
     * 清理重复的聊天关系（测试用）
     * @return \think\response\Json
     */
    public function cleanDuplicateRelations()
    {
        try {
            $user_id = $this->user_id;

            // 查找重复的客服关系（相同user_id, kefu_id但shop_id不同）
            $duplicates = Db::query("
                SELECT user_id, kefu_id, COUNT(*) as count, GROUP_CONCAT(id) as ids, GROUP_CONCAT(shop_id) as shop_ids
                FROM ls_chat_relation 
                WHERE user_id = ? AND shop_id > 0
                GROUP BY user_id, kefu_id 
                HAVING COUNT(*) > 1
            ", [$user_id]);

            $cleaned = 0;
            foreach ($duplicates as $duplicate) {
                $ids = explode(',', $duplicate['ids']);
                $shop_ids = explode(',', $duplicate['shop_ids']);
                
                // 保留shop_id不为0的记录，删除shop_id为0的记录
                for ($i = 1; $i < count($ids); $i++) {
                    if ($shop_ids[$i] == 0) {
                        ChatRelation::where('id', $ids[$i])->delete();
                        $cleaned++;
                    }
                }
            }

            return JsonServer::success('清理完成', [
                'user_id' => $user_id,
                'duplicates_found' => count($duplicates),
                'records_cleaned' => $cleaned,
                'duplicate_details' => $duplicates
            ]);
        } catch (\Exception $e) {
            return JsonServer::error('清理失败: ' . $e->getMessage());
        }
    }
}
