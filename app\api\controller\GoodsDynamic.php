<?php
namespace app\api\controller;

use app\common\basics\Api;
use app\api\logic\GoodsDynamicLogic;
use app\common\server\JsonServer;
use think\facade\Validate;

class GoodsDynamic extends Api
{
    public $like_not_need_login = ['getDisplayInfo'];

    /**
     * @notes 获取商品详情页动态展示信息
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2025/07/28
     */
    public function getDisplayInfo()
    {
        if ($this->request->isGet()) {
            $goodsId = $this->request->get('goods_id', '', 'trim');
            $validate = Validate::rule('goods_id', 'require|integer|gt:0');
            if (!$validate->check(['goods_id' => $goodsId])) {
                return JsonServer::error($validate->getError());
            }

            $displayInfo = GoodsDynamicLogic::getDisplayInfo($goodsId, $this->user_id);
            
            return JsonServer::success('获取成功', $displayInfo);
        } else {
            return JsonServer::error('请求方式错误');
        }
    }
}
