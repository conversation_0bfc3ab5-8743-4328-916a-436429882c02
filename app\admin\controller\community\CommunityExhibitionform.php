<?php


namespace app\admin\controller\community;


use app\admin\logic\community\CommunityExhibitionformLogic;
use app\admin\logic\community\CommunityExhibitionLogic;
use app\admin\logic\community\CommunityCategoryLogic;
use app\admin\validate\community\CommunityArticleValidate;
use app\common\basics\AdminBase;
use app\common\enum\CommunityArticleEnum;
use app\common\server\ConfigServer;
use app\common\server\JsonServer;

/**
 * 种草社区文章
 * Class CommunityArticle
 * @package app\admin\controller\community
 */
class CommunityExhibitionform extends AdminBase
{


    /**
     * @notes 文章列表
     * @return \think\response\Json|\think\response\View
     * <AUTHOR>
     * @date 2022/5/10 11:08
     */
    public function lists()
    {
        if ($this->request->isAjax()) {
            $get = $this->request->get();
            $lists = CommunityExhibitionformLogic::lists($get);
            return JsonServer::success("获取成功", $lists);
        }
        return view('', [
            'exhibition' => CommunityExhibitionLogic::getExselect()
        ]);
    }
    /**
     * @notes 导出积分明细Excel
     * @return \think\response\Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date 2022/4/24 10:20
     */
    public function export()
    {
        $params = $this->request->get();
        $result = CommunityExhibitionformLogic::integral($params, true);
        if(false === $result) {
            return JsonServer::error(CommunityExhibitionformLogic::getError() ?: '导出失败');
        }
        return JsonServer::success('', $result);
    }
    public function add()
    {
        if ($this->request->isAjax()) {
            $post = $this->request->post();
            $start_time=$post['start_time'] = strtotime($post['start_time']);
            $end_time=$post['end_time'] = strtotime($post['end_time']);
            if($start_time >= $end_time){
                return '开始时间不能大于结束时间';
            }
            if($end_time<time()){
                return '结束时间不能小于当前时间';
            }
            $res = CommunityExhibitionformLogic::add($post);
            if ($res === false) {
                $error = CommunityExhibitionformLogic::getError() ?: '新增失败';
                return JsonServer::error($error);
            }
            return JsonServer::success('新增成功');
        }

        return view('', [
            'category' => [],
            'tx_map_key' => ConfigServer::get('map', 'tx_map_key')
        ]);
    }

    /***
     * @Notes: 编辑文章
     * @Author: 张无忌
     */
    public function edit()
    {
        if ($this->request->isAjax()) {
            $post = $this->request->post();
            $start_time=$post['start_time'] = strtotime($post['start_time']);
            $end_time=$post['end_time'] = strtotime($post['end_time']);
            if($start_time >= $end_time){
                return '开始时间不能大于结束时间';
            }
            if($end_time<time()){
                return '结束时间不能小于当前时间';
            }
            $res = CommunityExhibitionLogic::edit($post);
            if ($res === false) {
                $error = CommunityExhibitionLogic::getError() ?: '编辑失败';
                return JsonServer::error($error);
            }
            return JsonServer::success('编辑成功');
        }

        $id = $this->request->get('id');
        return view('', [
            'detail'   => CommunityExhibitionLogic::detail($id),
            'tx_map_key' => ConfigServer::get('map', 'tx_map_key')
        ]);
    }
    /**
     * @notes 审核文章
     * @return \think\response\Json|\think\response\View
     * <AUTHOR>
     * @date 2022/5/10 17:45
     */
    public function audit()
    {
        if ($this->request->isAjax()) {
            $post = $this->request->post();
            (new CommunityArticleValidate())->goCheck('audit', $post);
            $result = CommunityExhibitionLogic::audit($post);
            if (false === $result) {
                return JsonServer::error(CommunityExhibitionLogic::getError() ?: '操作失败');
            }
            return JsonServer::success('编辑成功');
        }
        $id = $this->request->get('id');
        return view('', [
            'detail' => CommunityExhibitionLogic::detail($id)
        ]);
    }



    /**
     * @notes 文章详情
     * @return \think\response\Json|\think\response\View
     * <AUTHOR>
     * @date 2022/5/10 19:05
     */
    public function detail()
    {
        if ($this->request->isAjax()) {
            $get = $this->request->get();
            $result = CommunityExhibitionformLogic::getRelationData($get);
            return JsonServer::success('', $result);
        }
        $id = $this->request->get('id');
        return view('', [
            'detail' => CommunityExhibitionformLogic::detail($id)
        ]);
    }




    /**
     * @notes 删除文章
     * @return \think\response\Json|void
     * <AUTHOR>
     * @date 2022/5/10 16:46
     */
    public function del()
    {
        if ($this->request->isAjax()) {
            (new CommunityArticleValidate())->goCheck('id');
            $id = $this->request->post('id');
            $result = CommunityExhibitionLogic::del($id);
            if (false === $result) {
                return JsonServer::error(CommunityExhibitionLogic::getError() ?: '删除失败');
            }
            return JsonServer::success('删除成功');
        }
    }





}