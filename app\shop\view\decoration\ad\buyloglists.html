{layout name="layout1" /}
<style>
    /* 页面整体样式 */
    .ad-list-container {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        min-height: 100vh;
        padding: 20px;
    }

    /* 卡片样式优化 */
    .layui-card {
        border-radius: 12px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        border: none;
        backdrop-filter: blur(10px);
        background: rgba(255, 255, 255, 0.95);
        margin-bottom: 20px;
        overflow: hidden;
    }

    /* 操作提示区域美化 */
    .tips-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 12px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
    }

    .tips-card h3 {
        color: white;
        margin: 0 0 15px 0;
        font-size: 18px;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .tips-card p {
        margin: 8px 0;
        opacity: 0.9;
        font-size: 14px;
        line-height: 1.6;
    }

    /* 搜索区域美化 */
    .search-container {
        background: white;
        border-radius: 12px;
        padding: 25px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        margin-bottom: 20px;
    }

    .search-title {
        font-size: 16px;
        font-weight: 600;
        color: #333;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    /* 表单元素美化 */
    .layui-form-label {
        font-weight: 500;
        color: #555;
        background: none;
        border: none;
        padding: 0 15px 0 0;
        width: auto;
        min-width: 80px;
    }

    .layui-input, .layui-select {
        border: 2px solid #e8f0fe;
        border-radius: 8px;
        padding: 10px 15px;
        transition: all 0.3s ease;
        background: #fafbfc;
    }

    .layui-input:focus, .layui-select:focus {
        border-color: #667eea;
        background: white;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }

    /* 按钮美化 */
    .search-btn {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 8px;
        padding: 10px 20px;
        color: white;
        font-weight: 500;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    }

    .search-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
    }

    .reset-btn {
        background: #f8f9fa;
        border: 2px solid #e9ecef;
        border-radius: 8px;
        padding: 10px 20px;
        color: #6c757d;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .reset-btn:hover {
        background: #e9ecef;
        border-color: #dee2e6;
        color: #495057;
    }

    /* 表格容器美化 */
    .table-container {
        background: white;
        border-radius: 12px;
        padding: 25px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    }

    .table-title {
        font-size: 18px;
        font-weight: 600;
        color: #333;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    /* 表格样式优化 */
    .layui-table {
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
        table-layout: fixed !important; /* 固定表格布局，防止错位 */
        width: 100% !important;
    }

    .layui-table th {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        color: #495057;
        font-weight: 600;
        border: none;
        padding: 15px 8px;
        text-align: center;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .layui-table td {
        border: none;
        border-bottom: 1px solid #f1f3f4;
        padding: 12px 8px;
        vertical-align: middle;
        word-wrap: break-word;
        overflow: hidden;
    }

    /* 确保表格单元格内容不会溢出 */
    .layui-table .layui-table-cell {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        padding: 0 8px;
        line-height: 28px;
    }

    /* 对于需要换行的列，允许换行 */
    .layui-table td[data-field="ad_position_info"] .layui-table-cell,
    .layui-table td[data-field="ad_content"] .layui-table-cell {
        white-space: normal;
        line-height: 1.4;
        height: auto;
        min-height: 28px;
    }

    .layui-table tbody tr:hover {
        background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);
    }

    /* 状态标签美化 */
    .status-tag {
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 500;
        display: inline-block;
    }

    .status-pending {
        background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
        color: #856404;
    }

    .status-paid {
        background: linear-gradient(135deg, #d1ecf1 0%, #74b9ff 100%);
        color: #0c5460;
    }

    .status-cancelled {
        background: linear-gradient(135deg, #f8d7da 0%, #fd79a8 100%);
        color: #721c24;
    }

    .status-completed {
        background: linear-gradient(135deg, #d4edda 0%, #00b894 100%);
        color: #155724;
    }

    /* 图片预览优化 */
    .ad-image-preview {
        width: 60px;
        height: 60px;
        object-fit: cover;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .ad-image-preview:hover {
        transform: scale(1.1);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
    }

    /* 操作按钮美化 - 强制覆盖layui样式 */
    .layui-table .action-btn {
        padding: 8px 16px !important;
        border-radius: 20px !important;
        font-size: 12px !important;
        font-weight: 500 !important;
        transition: all 0.3s ease !important;
        border: none !important;
        cursor: pointer !important;
        display: inline-flex !important;
        align-items: center !important;
        gap: 6px !important;
        margin: 3px 2px !important;
        text-decoration: none !important;
        color: white !important;
        min-width: 80px !important;
        justify-content: center !important;
        box-sizing: border-box !important;
    }

    .layui-table .action-btn-pay {
        background: linear-gradient(135deg, #ff6b6b 0%, #ffa726 100%) !important;
        color: white !important;
        box-shadow: 0 3px 12px rgba(255, 107, 107, 0.4) !important;
    }

    .layui-table .action-btn-pay:hover {
        transform: translateY(-2px) !important;
        box-shadow: 0 6px 20px rgba(255, 107, 107, 0.6) !important;
        background: linear-gradient(135deg, #ff5252 0%, #ff9800 100%) !important;
    }

    .layui-table .action-btn-detail {
        background: linear-gradient(135deg, #42a5f5 0%, #1e88e5 100%) !important;
        color: white !important;
        box-shadow: 0 3px 12px rgba(66, 165, 245, 0.4) !important;
    }

    .layui-table .action-btn-detail:hover {
        transform: translateY(-2px) !important;
        box-shadow: 0 6px 20px rgba(66, 165, 245, 0.6) !important;
        background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%) !important;
    }

    .layui-table .action-btn-config {
        background: linear-gradient(135deg, #9c27b0 0%, #673ab7 100%) !important;
        color: white !important;
        box-shadow: 0 3px 12px rgba(156, 39, 176, 0.4) !important;
    }

    .layui-table .action-btn-config:hover {
        transform: translateY(-2px) !important;
        box-shadow: 0 6px 20px rgba(156, 39, 176, 0.6) !important;
        background: linear-gradient(135deg, #8e24aa 0%, #5e35b1 100%) !important;
    }

    /* 操作列容器样式 */
    .layui-table tbody tr td:last-child {
        text-align: center !important;
        vertical-align: middle !important;
        padding: 8px 5px !important;
    }

    .layui-table tbody tr td:last-child .layui-table-cell {
        display: flex !important;
        flex-direction: column !important;
        align-items: center !important;
        justify-content: center !important;
        gap: 4px !important;
        height: auto !important;
        line-height: normal !important;
        overflow: visible !important;
    }

    /* 强制覆盖layui默认按钮样式 */
    .layui-table .layui-btn {
        border-radius: 20px !important;
        padding: 8px 16px !important;
        font-size: 12px !important;
        margin: 2px !important;
        min-width: 80px !important;
        transition: all 0.3s ease !important;
    }

    /* 确保操作列宽度足够 */
    .layui-table th[data-field="0"],
    .layui-table td[data-field="0"] {
        min-width: 120px !important;
        width: 120px !important;
    }

    /* 修复表格行高 */
    .layui-table tbody tr {
        height: auto !important;
        min-height: 60px !important;
    }

    .layui-table tbody tr td {
        vertical-align: middle !important;
        padding: 8px 10px !important;
    }

    /* 金额显示美化 */
    .price-display {
        font-weight: 600;
        color: #e17055;
        font-size: 14px;
    }

    /* 时间显示美化 */
    .time-display {
        color: #636e72;
        font-size: 13px;
    }

    /* 剩余天数美化 */
    .days-remaining {
        background: linear-gradient(135deg, #a29bfe 0%, #6c5ce7 100%);
        color: white;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 500;
        display: inline-block;
    }

    /* 响应式优化 */
    @media (max-width: 768px) {
        .ad-list-container {
            padding: 10px;
        }

        .search-container, .table-container {
            padding: 15px;
        }

        .layui-form-item .layui-inline {
            display: block;
            margin-bottom: 15px;
        }
    }

    /* 加载动画 */
    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.9);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
    }

    .loading-spinner {
        width: 50px;
        height: 50px;
        border: 4px solid #f3f3f3;
        border-top: 4px solid #667eea;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    /* 一键配置弹窗样式优化 */
    #ad-config-modal .layui-form-label {
        font-weight: 500;
        color: #333;
        text-align: right;
    }

    #ad-config-modal .layui-input,
    #ad-config-modal .layui-textarea {
        border: 1px solid #e6e6e6;
        border-radius: 4px;
        transition: border-color 0.3s ease;
    }

    #ad-config-modal .layui-input:focus,
    #ad-config-modal .layui-textarea:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
    }

    #ad-config-modal .add-upload-image:hover {
        border-color: #667eea;
        background: #f0f4ff;
    }

    #ad-config-modal .add-upload-image:hover i {
        color: #667eea;
    }

    #ad-config-modal .add-upload-image:hover span {
        color: #667eea;
    }

    /* 上传图片区域样式修复 */
    #ad-config-modal .like-upload-image .upload-image-div {
        display: inline-block;
        position: relative;
        margin-right: 10px;
        margin-bottom: 10px;
    }

    #ad-config-modal .like-upload-image .upload-image-div img {
        width: 120px;
        height: 120px;
        object-fit: cover;
        border-radius: 6px;
        border: 1px solid #e6e6e6;
    }

    #ad-config-modal .like-upload-image .del-upload-btn {
        position: absolute;
        top: -8px;
        right: -8px;
        width: 20px;
        height: 20px;
        background: #ff4757;
        color: white;
        border-radius: 50%;
        text-align: center;
        line-height: 20px;
        font-size: 12px;
        cursor: pointer;
        font-weight: bold;
    }
</style>

<div class="ad-list-container">
    <!-- 操作提示卡片 -->
    <div class="tips-card">
        <h3>
            <i class="layui-icon layui-icon-tips" style="font-size: 20px;"></i>
            广告购买记录管理
        </h3>
        <p><i class="layui-icon layui-icon-ok-circle"></i> 此处列出了您购买的所有广告位记录，支持多条件筛选查询</p>
        <p><i class="layui-icon layui-icon-edit"></i> 您可以管理已支付且未过期的广告内容，实时查看广告效果</p>
        <p><i class="layui-icon layui-icon-rmb"></i> 待支付订单可直接在线支付，支持微信扫码支付</p>
    </div>

    <!-- 搜索区域 -->
    <div class="search-container">
        <div class="search-title">
            <i class="layui-icon layui-icon-search" style="font-size: 18px; color: #667eea;"></i>
            智能搜索筛选
        </div>
        <form class="layui-form" lay-filter="search-form">
            <div class="layui-row layui-col-space15">
                <div class="layui-col-md3">
                    <div class="layui-form-item">
                        <label class="layui-form-label">
                            <i class="layui-icon layui-icon-location"></i>
                            广告位
                        </label>
                        <div class="layui-input-block">
                            <input type="text" name="position_name" placeholder="请输入广告位名称" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                </div>
                <div class="layui-col-md3">
                    <div class="layui-form-item">
                        <label class="layui-form-label">
                            <i class="layui-icon layui-icon-flag"></i>
                            状态
                        </label>
                        <div class="layui-input-block">
                        <select name="status">
                            <option value="">全部状态</option>
                            <option value="0">未付款</option>
                            <option value="1" selected>未配置</option>
                            <option value="2">已配置</option>
                            <option value="3">已失效</option>
                        </select>
                        </div>
                    </div>
                </div>
                <div class="layui-col-md3">
                    <div class="layui-form-item">
                        <label class="layui-form-label">
                            <i class="layui-icon layui-icon-date"></i>
                            开始时间
                        </label>
                        <div class="layui-input-block">
                            <input type="text" name="start_time" class="layui-input" id="start_time" placeholder="选择开始时间" autocomplete="off">
                        </div>
                    </div>
                </div>
                <div class="layui-col-md3">
                    <div class="layui-form-item">
                        <label class="layui-form-label">
                            <i class="layui-icon layui-icon-date"></i>
                            结束时间
                        </label>
                        <div class="layui-input-block">
                            <input type="text" name="end_time" class="layui-input" id="end_time" placeholder="选择结束时间" autocomplete="off">
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-form-item" style="margin-top: 20px; text-align: center;">
                <button class="layui-btn search-btn" lay-submit lay-filter="search">
                    <i class="layui-icon layui-icon-search"></i>
                    搜索查询
                </button>
                <button type="button" class="layui-btn reset-btn" lay-submit lay-filter="clear-search">
                    <i class="layui-icon layui-icon-refresh"></i>
                    重置条件
                </button>
            </div>
        </form>
    </div>

    <!-- 表格区域 -->
    <div class="table-container">
        <div class="table-title">
            <i class="layui-icon layui-icon-table" style="font-size: 20px; color: #667eea;"></i>
            广告购买记录列表
            <span style="font-size: 14px; color: #999; font-weight: normal; margin-left: 10px;">
                实时更新，数据准确可靠
            </span>
        </div>
        <table id="ad-buy-log-table" lay-filter="ad-buy-log-table"></table>
    </div>
</div>

<!-- 表格操作列 -->
<script type="text/html" id="ad-buy-log-actions">
    {{#
        if(!d) { return '<span style="color:red;">数据错误</span>'; }

        var status = parseInt(d.status);
        var remaining_seconds = parseInt(d.remaining_seconds);
        var status_text = d.status_text || '';

        if (isNaN(status)) status = -1;
        if (isNaN(remaining_seconds)) remaining_seconds = 0;

        var buttonHtml = '';
    }}

    {{#
        if(status === 0) {
             buttonHtml = '<a class="action-btn action-btn-pay" lay-event="pay"><i class="layui-icon layui-icon-rmb"></i> 立即支付</a>';
        } else {
             buttonHtml = '<a class="action-btn action-btn-detail" lay-event="detail"><i class="layui-icon layui-icon-list"></i> 查看详情</a>';
             if(status === 1 && remaining_seconds > 0) {
                 buttonHtml += '<a class="action-btn action-btn-config" lay-event="config"><i class="layui-icon layui-icon-set"></i> 一键配置</a>';
             }
        }
    }}

    {{# return buttonHtml; }}
</script>







<!-- 引入 layui js -->
<!-- 确保已引入 qrcode.js -->
<!-- <script src="/path/to/qrcode.min.js"></script> -->

<script>
    layui.config({
        version: "{$front_version}",
        base: '/static/lib/' //静态资源所在路径
    }).use(['table', 'form', 'jquery', 'layer', 'laytpl', 'laydate'], function() {
        var table = layui.table;
        var form = layui.form;
        var $ = layui.jquery;
        var layer = layui.layer;
        var laytpl = layui.laytpl;
        var laydate = layui.laydate;
        var qrCodeInstance = null; // 用于存储二维码实例
        var checkInterval = null; // 用于存储轮询定时器

        // 确保UrlServer存在
        if (typeof UrlServer === 'undefined') {
            window.UrlServer = {
                getFileUrl: function(uri) {
                    if (!uri) return '';
                    // 如果已经是完整URL，直接返回
                    if (/^(http|https|\/\/)/.test(uri)) {
                        return uri;
                    }
                    // 如果是绝对路径，直接返回
                    if (uri.startsWith('/')) {
                        return uri;
                    }
                    // 否则添加默认前缀（根据实际项目调整）
                    return '/uploads/' + uri.replace(/^\/+/, '');
                }
            };
        }

        /**
         * 辅助函数：获取文件完整路径
         * @param {string} uri 文件相对路径或完整URL
         * @returns {string} 处理后的完整URL
         */
        laytpl.getFilePath = function(uri){
            if (!uri) return '';
            return UrlServer.getFileUrl(uri);
        }

        laydate.render({ elem: '#start_time', type: 'datetime' });
        laydate.render({ elem: '#end_time', type: 'datetime' });

        /**
         * 格式化时间，只显示到分钟
         * @param {string} timeStr 时间字符串
         * @returns {string} 格式化后的时间字符串
         */
        function formatTimeToMinute(timeStr) {
            if (!timeStr || timeStr === '-') {
                return '-';
            }

            // 匹配常见的时间格式：YYYY-MM-DD HH:mm:ss
            var timeMatch = timeStr.match(/(\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2})(:\d{2})?/);
            if (timeMatch) {
                return timeMatch[1]; // 返回不包含秒的部分
            }

            // 如果不匹配标准格式，尝试其他处理
            if (timeStr.includes(':')) {
                var parts = timeStr.split(':');
                if (parts.length >= 2) {
                    // 只保留小时和分钟部分
                    var datePart = timeStr.substring(0, timeStr.lastIndexOf(':'));
                    return datePart;
                }
            }

            return timeStr; // 如果无法处理，返回原始字符串
        }

        var tableIns = table.render({
            elem: '#ad-buy-log-table',
            url: "{:url('shop/decoration.ad/buyloglists')}",
            page: true,
            limit: 15,
            limits: [15, 30, 50, 100],
            loading: true,
            even: true,
            skin: 'line',
            size: 'lg',
            autoSort: false, // 禁用自动排序，避免列宽变化
            unresize: true, // 禁用列宽拖拽调整
            where: {
                status: '1' // 默认显示未配置状态
            },
            done: function(res, curr, count){
                console.log('Table done callback - 数据加载完成');
                console.log('Response:', res);
                console.log('Current page:', curr);
                console.log('Total count:', count);
            },
            cols: [[
                {field: 'id', title: 'ID', width: 60, align:'center', templet: function(d){
                    return '<span style="color: #667eea; font-weight: 600;">#' + d.id + '</span>';
                }},
                {field: 'order_sn', title: '订单号', width: 160, templet: function(d){
                    var sn = d.order_sn || '-';
                    return '<span style="font-family: monospace; color: #2d3436; font-weight: 500; font-size: 12px;">' + sn + '</span>';
                }},
                {
                    field: 'ad_position_info', title: '广告位信息', width: 130, templet: function(d){
                        var name = (d && d.ad_position_info && d.ad_position_info.name) ? d.ad_position_info.name : '<span style="color:red;">未知</span>';
                        return '<div style="font-weight: 500; color: #2d3436; word-break: break-all; line-height: 1.4;">' + name + '</div>';
                    }
                },
                {
                    field: 'ad_position_info', title: '广告位图', width: 80, align:'center', templet: function(d){
                        if(d && d.ad_position_info && d.ad_position_info.image) {
                            var src = d.ad_position_info.image;
                            // 处理图片路径
                            if(src && !/^(http|https|\/\/)/.test(src)) {
                                if(typeof UrlServer !== 'undefined' && UrlServer.getFileUrl) {
                                    try {
                                        src = UrlServer.getFileUrl(src);
                                    } catch(e) {
                                        console.error('处理图片路径失败:', e);
                                    }
                                } else {
                                    // 如果UrlServer不存在，使用默认处理
                                    if(!src.startsWith('/')) {
                                        src = '/uploads/' + src.replace(/^\/+/, '');
                                    }
                                }
                            }
                            return '<img src="' + src + '" alt="' + (d.ad_position_info.name || '') + '" class="ad-image-preview" lay-event="previewPositionImage" onerror="this.style.display=\'none\'; this.nextSibling.style.display=\'inline\';" />' +
                                   '<span style="display:none; color:#999;">图片加载失败</span>';
                        } else {
                            return '<span style="color:#ddd;">-</span>';
                        }
                    }
                },
                {
                    field: 'ad_content', title: '广告标题', width: 140, templet: function(d){
                        if (!d) return '<span style="color: #ddd;">-</span>';
                        var title = '';
                        if (d.ad_content && d.ad_content.title) {
                            title = d.ad_content.title;
                        } else if (d.goods_info && d.goods_info.name) {
                            title = '商品: ' + d.goods_info.name;
                        } else if (d.status == 1 && d.remaining_seconds > 0) {
                            return '<span style="color:#e17055; font-weight: 500;">未设置内容</span>';
                        } else {
                            return '<span style="color: #ddd;">-</span>';
                        }
                        return '<div style="color: #2d3436; line-height: 1.4; word-break: break-all; overflow: hidden; text-overflow: ellipsis;" title="' + title + '">' + title + '</div>';
                    }
                },
                {
                    field: 'ad_content', title: '广告图', width: 80, align:'center', templet: function(d){
                        if(d && d.ad_content && d.ad_content.image_url) {
                            var src = d.ad_content.image_url;
                            // 处理图片路径
                            if(src && !/^(http|https|\/\/)/.test(src)) {
                                if(typeof UrlServer !== 'undefined' && UrlServer.getFileUrl) {
                                    try {
                                        src = UrlServer.getFileUrl(src);
                                    } catch(e) {
                                        console.error('处理图片路径失败:', e);
                                    }
                                } else {
                                    // 如果UrlServer不存在，使用默认处理
                                    if(!src.startsWith('/')) {
                                        src = '/uploads/' + src.replace(/^\/+/, '');
                                    }
                                }
                            }
                            return '<img src="' + src + '" alt="' + (d.ad_content.title || '') + '" class="ad-image-preview" lay-event="previewAdImage" onerror="this.style.display=\'none\'; this.nextSibling.style.display=\'inline\';" />' +
                                   '<span style="display:none; color:#999;">图片加载失败</span>';
                        } else {
                            return '<span style="color:#ddd;">-</span>';
                        }
                    }
                },
                {field: 'ad_price', title: '支付金额', width: 90, align:'center', templet: function(d){
                    var price = d.ad_price !== null && d.ad_price !== undefined ? d.ad_price : '-';
                    if (price !== '-') {
                        return '<span class="price-display">¥' + price + '</span>';
                    }
                    return '<span style="color: #ddd;">-</span>';
                }},
                {field: 'status', title: '订单状态', width: 90, align:'center', templet: function(d){
                    var status = d.status || 0;
                    var text = d.status_text || '';
                    var className = '';
                    switch(parseInt(status)) {
                        case 0: className = 'status-pending'; break;
                        case 1: className = 'status-paid'; break;
                        case 2: className = 'status-cancelled'; break;
                        case 3: className = 'status-completed'; break;
                        default: className = 'status-pending';
                    }
                    return '<span class="status-tag ' + className + '">' + text + '</span>';
                }},
                {field: 'start_time_text', title: '生效时间', width: 120, templet: function(d){
                    var time = formatTimeToMinute(d.start_time_text || '-');
                    return '<span class="time-display" style="font-size: 12px;">' + time + '</span>';
                }},
                {field: 'end_time_text', title: '失效时间', width: 120, templet: function(d){
                    var time = formatTimeToMinute(d.end_time_text || '-');
                    return '<span class="time-display" style="font-size: 12px;">' + time + '</span>';
                }},
                {field: 'remaining_days', title: '剩余天数', width: 80, align:'center', templet: function(d){
                    var days = d.remaining_days;
                    if (days !== null && days !== undefined && days >= 0) {
                        return '<span class="days-remaining">' + days + ' 天</span>';
                    }
                    return '<span style="color: #ddd;">-</span>';
                }},
                {field: 'create_time_text', title: '购买时间', width: 120, templet: function(d){
                    var time = formatTimeToMinute(d.create_time_text || '-');
                    return '<span class="time-display" style="font-size: 12px;">' + time + '</span>';
                }},
                {fixed: 'right', title: '操作', toolbar: '#ad-buy-log-actions', width: 120, align:'center'}
            ]],
            parseData: function(res){
                console.log('=== parseData 开始 ===');
                console.log('原始响应数据:', res);

                if(res.code != 1){
                    console.log('响应失败:', res.msg);
                    layer.msg(res.msg || '数据加载失败', {icon: 2});
                    return { "code": res.code || -1, "msg": res.msg || 'Error', "count": 0, "data": [] };
                }
                var count = (res.data && res.data.count) ? res.data.count : 0;
                var lists = (res.data && res.data.lists) ? res.data.lists : [];

                console.log('解析后的数据:');
                console.log('- count:', count);
                console.log('- lists:', lists);
                if(lists.length > 0) {
                    console.log('- 第一条数据的所有字段:', Object.keys(lists[0]));
                    console.log('- 第一条完整数据:', lists[0]);
                }
                console.log('=== parseData 结束 ===');

                // 保存表格数据到全局变量，供图片上传时使用
                window.currentTableData = lists;

                return {
                    "code": 0,
                    "msg": res.msg || '加载成功',
                    "count": count,
                    "data": lists
                };
            }
        });

        //监听搜索
        form.on('submit(search)', function(data){
            var field = data.field;

            // 显示搜索加载动画
            var searchBtn = $('.search-btn');
            var originalText = searchBtn.html();
            searchBtn.html('<i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop"></i> 搜索中...');
            searchBtn.prop('disabled', true);

            //执行重载
            tableIns.reload({
                where: field,
                page: {curr: 1}
            });

            // 恢复按钮状态
            setTimeout(function(){
                searchBtn.html(originalText);
                searchBtn.prop('disabled', false);

                // 显示搜索结果提示
                var hasConditions = false;
                for(var key in field) {
                    if(field[key] && field[key].trim() !== '') {
                        hasConditions = true;
                        break;
                    }
                }
                if(hasConditions) {
                    layer.msg('搜索完成，已应用筛选条件', {icon: 1, time: 2000});
                }
            }, 1000);
        });

        //清空查询
        form.on('submit(clear-search)', function () {
            // 显示重置动画
            var resetBtn = $('.reset-btn');
            var originalText = resetBtn.html();
            resetBtn.html('<i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop"></i> 重置中...');
            resetBtn.prop('disabled', true);

            $('input[name="position_name"]').val('');
            $('select[name="status"]').val('');
            $('#start_time').val('');
            $('#end_time').val('');
            form.render('select');

            //刷新列表，恢复默认搜索条件
            tableIns.reload({
                where: {
                    status: '1' // 默认显示未配置状态
                },
                page: {curr: 1}
            });

            // 恢复按钮状态
            setTimeout(function(){
                resetBtn.html(originalText);
                resetBtn.prop('disabled', false);
                layer.msg('已重置所有筛选条件', {icon: 1, time: 2000});
            }, 800);
        });

        // 添加快捷搜索功能
        $('.layui-input[name="position_name"]').on('input', function(){
            var value = $(this).val();
            if(value.length > 2) {
                // 延迟搜索，避免频繁请求
                clearTimeout(window.searchTimer);
                window.searchTimer = setTimeout(function(){
                    $('.search-btn').click();
                }, 1000);
            }
        });

        // 清理轮询和二维码显示的函数
        function clearPaymentProcess() {
            if (checkInterval) {
                clearInterval(checkInterval);
                checkInterval = null;
            }
            if (qrCodeInstance) {
                // 如果 qrcode.js 提供了清理方法，可以在这里调用
                qrCodeInstance = null;
            }
            // 关闭可能存在的支付二维码弹窗
            layer.close(layer.index);
        }
        // 轮询检查支付状态的函数
        function checkPaymentStatus(orderId) {
            $.ajax({
                url: '/shop/store/getAdPayStatus', // 替换为实际的查询支付状态接口URL
                type: 'GET',
                data: {
                    id: orderId,
                    from:'trade'
                },
                success: function(response) {
                    if (response.code === 1 && response.data.status === 'paid') {
                        // 支付成功
                        layer.msg('支付成功！');
                        clearInterval(); // 停止轮询
                        location.reload();
                        // 执行其他支付成功后的逻辑...
                    } else if (response.code === 1 && response.data.status !== 'paid') {
                        // 支付未完成，继续轮询
                        console.log('支付未完成，继续轮询...');
                    } else {
                        // 查询支付状态失败
                        layer.msg('查询支付状态失败：' + response.msg);
                        clearInterval(); // 停止轮询
                    }
                },
                error: function() {
                    // 请求失败
                    layer.msg('查询支付状态请求异常');
                    clearInterval(); // 停止轮询
                }
            });
        }
        // 显示支付二维码的弹窗
        function showQRCode(qrCode, orderData) {
            orderData = orderData || {};
            var paymentContent = '<div style="text-align: center; padding: 30px; background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%); border-radius: 12px;">' +
                '<div style="background: white; border-radius: 12px; padding: 30px; box-shadow: 0 8px 32px rgba(0,0,0,0.1); margin-bottom: 20px;">' +
                '<h2 style="color: #2d3436; margin-bottom: 20px; font-weight: 600;">' +
                '<i class="layui-icon layui-icon-rmb" style="color: #00b894; font-size: 24px;"></i> 扫码支付</h2>' +
                '<div style="background: #f8f9fa; border-radius: 8px; padding: 15px; margin-bottom: 20px;">' +
                '<img src="' + qrCode + '" style="width: 200px; height: 200px; border-radius: 8px; box-shadow: 0 4px 16px rgba(0,0,0,0.1);"/></div>' +
                '<div style="color: #636e72; font-size: 16px; line-height: 1.6;">' +
                '<p style="margin: 10px 0;"><i class="layui-icon layui-icon-cellphone" style="color: #00b894;"></i> 请使用微信扫一扫完成支付</p>' +
                '<p style="margin: 10px 0;"><i class="layui-icon layui-icon-time" style="color: #fdcb6e;"></i> 订单将在 <span style="color: #e17055; font-weight: 600;">5分钟</span> 后自动取消</p>' +
                '</div></div>' +
                '<div style="background: rgba(255,255,255,0.9); border-radius: 8px; padding: 15px; font-size: 14px; color: #636e72;">' +
                '<p><strong>订单信息：</strong></p>' +
                '<p>订单号：' + (orderData.order_sn || orderData.id || '未知') + '</p>' +
                '<p>广告位：' + (orderData.ad_position_info ? orderData.ad_position_info.name : '未知') + '</p>' +
                '<p>支付金额：<span style="color: #e17055; font-weight: 600;">¥' + (orderData.ad_price || '0') + '</span></p>' +
                '</div></div>';
            layer.open({
                type: 1,
                title: '<i class="layui-icon layui-icon-rmb"></i> 微信支付',
                area: ['450px', '600px'],
                content: paymentContent,
                btn: ['支付完成', '取消支付'],
                btnAlign: 'c',
                closeBtn: 1,
                shade: 0.8,
                shadeClose: false,
                yes: function (index, layero) {
                    // 用户点击“确认支付”后，可以在这里添加支付成功后的回调逻辑
                    // 注意：这里只是模拟用户确认支付，实际的支付成功回调应由支付平台通知
                    layer.msg('支付成功！');
                    // 刷新页面或执行其他操作...
                    location.reload(); // 示例：刷新页面
                    layer.close(index);
                },
                btn2: function (index, layero) {
                    // 用户点击“取消”
                    layer.close(index);
                }
            });
        }
        table.on('tool(ad-buy-log-table)', function(obj) {
            var data = obj.data;
            var layEvent = obj.event;

            if (layEvent === 'detail') {
                layer.open({
                    type: 2,
                    title: '<i class="layui-icon layui-icon-list"></i> 广告购买记录详情 - ' + (data.order_sn || '#' + data.id),
                    shadeClose: true,
                    shade: 0.6,
                    area: ['80%', '80%'],
                    maxmin: true,
                    content: "{:url('shop/decoration.ad/buyLogDetailView')}?id=" + data.id
                });
            } else if (layEvent === 'config') {
                // 一键配置广告内容 - 使用iframe方式打开新页面
                console.log('点击一键配置，数据:', data);

                layer.open({
                    type: 2,
                    title: '<i class="layui-icon layui-icon-set"></i> 一键配置广告内容 - ' + (data.ad_position_info ? data.ad_position_info.name : ''),
                    shadeClose: false,
                    shade: 0.6,
                    area: ['800px', '700px'],
                    maxmin: true,
                    content: "{:url('shop/decoration.ad/config')}?id=" + data.id,
                    btn: ['确认配置', '取消'],
                    btnAlign: 'c',
                    yes: function(index, layero) {
                        // 获取iframe窗口对象
                        var iframeWindow = window['layui-layer-iframe' + index];
                        if (iframeWindow && iframeWindow.document) {
                            // 触发iframe内部的表单提交
                            var submitBtn = iframeWindow.document.getElementById('config-submit');
                            if (submitBtn) {
                                submitBtn.click();
                            } else {
                                layer.msg('配置页面加载异常，请重试', {icon: 2});
                            }
                        } else {
                            layer.msg('无法获取配置页面，请重试', {icon: 2});
                        }
                        return false; // 阻止默认关闭
                    },
                    btn2: function(index, layero) {
                        // 用户点击取消
                        layer.close(index);
                        return false;
                    }
                });
            } else if (layEvent === 'pay') {
                // 清理之前的支付流程（如果存在）
                clearPaymentProcess();

                var order_id = data.id; // 使用订单ID
                var order_sn = data.order_sn; // 保留订单号用于轮询

                if (!order_id || !order_sn) {
                    layer.msg('无法获取订单信息', {icon: 2});
                    return;
                }

                // 显示加载动画
                var loading = layer.load(1);
                console.log('发起支付请求，订单ID:', order_id);

                $.ajax({
                    // 修改请求URL为统一支付接口
                    url: "/api/pay/unifiedpay",
                    type: 'POST',
                    // 构造符合 unifiedpay 的参数
                    data: {
                        order_id: order_id,
                        from: 'AdOrder', // 指定订单来源为广告订单
                        pay_way: 1, // 假设 2 代表微信扫码支付，请根据实际情况修改
                        client: 5 // 假设客户端为 PC
                    },
                    success: function(response){
                        if(response.code ===1){ // 假设支付接口返回code为1表示成功
                            // 显示支付二维码
                            // 开始轮询检查支付状态
                            var qrCode = response.data; // 假设返回的数据中包含base64编码的二维码
                            showQRCode(qrCode, data);
                            var pollInterval = setInterval(function() {
                                checkPaymentStatus(order_id);
                            }, 1000); // 每5秒轮询一次
                        } else {
                            // 支付请求失败
                            layer.msg('支付请求失败：' + response.msg);
                        }
                    },
                    error: function(){
                        // 请求失败
                        layer.msg('支付请求异常');
                    }
                });
            } else if (layEvent === 'previewPositionImage') {
                console.log('点击预览广告位图片，数据:', data);
                console.log('ad_position_info:', data.ad_position_info);

                if(data.ad_position_info && data.ad_position_info.image){
                    var src = data.ad_position_info.image;
                    console.log('原始广告位图片路径:', src);

                    // 处理图片路径
                    if (src && !/^(http|https|\/\/)/.test(src)) {
                        try {
                            src = UrlServer.getFileUrl(src);
                            console.log('处理后广告位图片路径:', src);
                        } catch(e) {
                            console.error('处理图片路径失败:', e);
                            layer.msg('图片路径处理失败', {icon: 2});
                            return;
                        }
                    }
                    if(src) {
                        layer.photos({
                            photos: { "data": [{"src": src, "alt": data.ad_position_info.name || '广告位图片'}] },
                            anim: 5
                        });
                    } else {
                        layer.msg('广告位图片路径为空', {icon: 2});
                    }
                } else {
                    console.log('广告位信息或图片字段不存在');
                    layer.msg('该广告位暂无图片', {icon: 0});
                }
            } else if (layEvent === 'previewAdImage') {
                console.log('点击预览广告图片，数据:', data);
                console.log('ad_content:', data.ad_content);

                if(data.ad_content && data.ad_content.image_url){
                    var src = data.ad_content.image_url;
                    console.log('原始图片路径:', src);

                    // 处理图片路径
                    if (src && !/^(http|https|\/\/)/.test(src)) {
                        try {
                            src = UrlServer.getFileUrl(src);
                            console.log('处理后图片路径:', src);
                        } catch(e) {
                            console.error('处理图片路径失败:', e);
                            layer.msg('图片路径处理失败', {icon: 2});
                            return;
                        }
                    }
                    if(src) {
                        layer.photos({
                            photos: { "data": [{"src": src, "alt": data.ad_content.title || '广告图片'}] },
                            anim: 5
                        });
                    } else {
                        layer.msg('图片路径为空', {icon: 2});
                    }
                } else {
                    console.log('广告内容或图片字段不存在');
                    layer.msg('该广告暂无图片内容', {icon: 0});
                }
            }
        });





        // 页面卸载或离开时清理定时器
        $(window).on('beforeunload', function() {
            clearPaymentProcess();
        });
    });
</script>
