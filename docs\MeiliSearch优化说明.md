# MeiliSearch搜索接口优化说明

## 问题分析

经过代码分析，发现以下问题：

1. **搜索接口调用错误的方法**：`api/search_record/searchGoods`接口原本调用的是`SearchRecordLogic::searchGoods()`，但这个方法没有使用MeiliSearch
2. **商品同步问题**：商品数据可能没有正确同步到MeiliSearch
3. **MeiliSearch连接配置问题**：需要确保MeiliSearch服务正常运行
4. **索引配置不完整**：缺少`split_word`等重要搜索字段

## 解决方案

### 1. 优化后的搜索接口

已将`api/search_record/searchGoods`接口修改为使用MeiliSearch：

- 主要使用`SearchRecordLogic::searchGoodsByMeili()`方法
- 添加了错误处理和数据库搜索回退机制
- 支持多种排序方式和过滤条件

### 2. 新增的管理命令

#### 测试MeiliSearch连接
```bash
php think test:meilisearch
```

#### 诊断MeiliSearch问题
```bash
php think diagnose:meilisearch
```

#### 自动修复MeiliSearch问题
```bash
php think fix:meilisearch
```

#### 强制重建索引和数据
```bash
php think fix:meilisearch --force
```

#### 同步商品到MeiliSearch
```bash
php think sync:goods-to-meilisearch
```

#### 重置并重新同步
```bash
php think sync:goods-to-meilisearch --reset
```

### 3. 优化的功能

#### MeiliSearch类优化
- 添加了连接测试功能
- 改进了错误处理和日志记录
- 优化了HTTP请求方法

#### 搜索逻辑优化
- 支持智能回退到数据库搜索
- 改进了错误处理机制
- 添加了详细的日志记录

#### 索引配置优化
- 添加了`split_word`字段到可搜索属性
- 完善了过滤和排序字段配置
- 优化了排序规则

## 使用步骤

### 第一步：诊断问题
```bash
php think diagnose:meilisearch
```

### 第二步：修复问题
```bash
php think fix:meilisearch
```

### 第三步：测试搜索
访问接口：`api/search_record/searchGoods?keywords=手机`

## 接口参数说明

### 搜索接口：`api/search_record/searchGoods`

#### 请求参数
- `keywords` (string): 搜索关键词
- `page` (int): 页码，默认1
- `limit` (int): 每页数量，默认10
- `sort` (string): 排序方式
  - `price_asc`: 价格升序
  - `price_desc`: 价格降序
  - `sales_desc`: 销量降序
  - `create_time_desc`: 创建时间降序
- `cate_id` (int): 商品分类ID
- `shop_id` (int): 店铺ID
- `brand_id` (int): 品牌ID
- `min_price` (float): 最低价格
- `max_price` (float): 最高价格
- `is_hot` (int): 是否热门商品
- `is_recommend` (int): 是否推荐商品

#### 响应格式
```json
{
    "code": 1,
    "msg": "搜索成功",
    "data": {
        "list": [
            {
                "id": 1,
                "name": "商品名称",
                "image": "商品图片",
                "remark": "商品描述",
                "min_price": 99.00,
                "market_price": 199.00,
                "sales_actual": 100,
                "sales_virtual": 200
            }
        ],
        "total": 100,
        "processing_time_ms": 15
    }
}
```

## 配置说明

### MeiliSearch配置
在`config/meilisearch.php`中配置：
```php
return [
    'host' => 'http://***************:7700',
    'api_key' => '27bb9198372f81f8b95fb75d0252912de061fb6fa90d0ad6eb347cc051f0abe3'
];
```

### 索引配置
- **可搜索字段**：name, remark, content, split_word
- **可过滤字段**：shop_id, first_cate_id, second_cate_id, third_cate_id, brand_id, status, audit_status, del, is_hot, is_recommend
- **可排序字段**：min_price, sales_actual, sales_virtual, create_time, update_time

## 故障排除

### 常见问题

1. **MeiliSearch连接失败**
   - 检查MeiliSearch服务是否运行
   - 验证host和api_key配置是否正确

2. **搜索结果为空**
   - 运行`php think diagnose:meilisearch`检查数据同步状态
   - 使用`php think fix:meilisearch`自动修复

3. **商品数据不完整**
   - 运行`php think sync:goods-to-meilisearch --reset`重新同步

4. **搜索性能问题**
   - 检查索引配置是否正确
   - 考虑优化搜索关键词和过滤条件

### 日志查看
搜索相关日志会记录在ThinkPHP的日志文件中，可以通过以下方式查看：
- 检查`runtime/log/`目录下的日志文件
- 搜索关键词：`MeiliSearch`、`搜索`、`search`

## 性能优化建议

1. **定期同步数据**：建议设置定时任务定期同步商品数据
2. **监控搜索性能**：关注搜索响应时间和成功率
3. **优化搜索关键词**：使用分词功能提高搜索准确性
4. **缓存热门搜索**：对热门搜索结果进行缓存

## 后续维护

1. **定期检查**：使用诊断命令定期检查系统状态
2. **数据备份**：定期备份MeiliSearch索引数据
3. **版本更新**：关注MeiliSearch版本更新
4. **性能监控**：监控搜索接口的性能指标
