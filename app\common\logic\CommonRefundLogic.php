<?php
namespace app\common\logic;

use app\common\enum\PayEnum;
use app\common\model\AccountLog;
use app\common\model\user\User;
use app\common\server\AliPayServer;
use app\common\server\WeChatPayServer;
use app\common\server\WeChatServer;
use think\Exception;
use think\facade\Db;

/**
 * 通用退款逻辑
 * Class CommonRefundLogic
 * @package app\common\logic
 */
class CommonRefundLogic
{
    /**
     * 错误信息
     * @var string
     */
    private static $error = '';

    /**
     * 获取错误信息
     * @return string
     */
    public static function getError()
    {
        return self::$error;
    }

    /**
     * 退款类型常量
     */
    const REFUND_TYPE_SHOP_DEPOSIT = 1; // 商家保证金
    const REFUND_TYPE_AD_FEE = 2; // 广告费
    const REFUND_TYPE_MEMBER_FEE = 3; // 会员费
    const REFUND_TYPE_AGENT_DEPOSIT = 4; // 代理保证金
    const REFUND_TYPE_INSPECTION_FEE = 5; // 检验费

    /**
     * 退款状态常量
     */
    const REFUND_STATUS_PROCESSING = 0; // 退款中
    const REFUND_STATUS_SUCCESS = 1; // 退款成功
    const REFUND_STATUS_FAILED = 2; // 退款失败

    /**
     * 处理退款
     * @param array $params 退款参数
     * @return bool
     * @throws Exception
     */
    public static function refund(array $params)
    {
        Db::startTrans();
        try {
            // 验证必要参数
            if (empty($params['refund_type']) || empty($params['source_id']) ||
                empty($params['refund_amount']) || empty($params['total_amount']) ||
                empty($params['payment_method'])) {
                throw new Exception('退款参数不完整');
            }

            // 创建退款记录
            $refund_id = self::addRefundLog($params);
            if (!$refund_id) {
                throw new Exception('创建退款记录失败');
            }
            //如果$params['payment_method']包含微信二字则返回2
            if (strpos($params['payment_method'], '微信') !== false) {
                $params['payment_method'] = PayEnum::WECHAT_PAY;
            }

            // 根据支付方式进行退款
            switch ($params['payment_method']) {
                // 余额支付退款
                case PayEnum::BALANCE_PAY:
                    self::balancePayRefund($params);
                    break;
                // 微信支付退款
                case PayEnum::WECHAT_PAY:
                    self::wechatPayRefund($params, $refund_id);
                    break;
                // 支付宝支付退款
                case PayEnum::ALI_PAY:
                    self::aliPayRefund($params, $refund_id);
                    break;
                default:
                    throw new Exception('不支持的支付方式');
            }

            // 更新相关业务表的状态
            self::updateBusinessStatus($params);

            Db::commit();
            return true;
        } catch (Exception $e) {
            Db::rollback();
            self::$error = $e->getMessage();
            return false;
        }
    }

    /**
     * 添加退款记录
     * @param array $params 退款参数
     * @return int 退款记录ID
     */
    private static function addRefundLog(array $params)
    {
        $data = [
            'refund_sn' => createSn('common_refund', 'refund_sn'),
            'refund_type' => $params['refund_type'],
            'source_id' => $params['source_id'],
            'shop_id' => $params['shop_id'] ?? 0,
            'user_id' => $params['user_id'] ?? 0,
            'agent_id' => $params['agent_id'] ?? 0,
            'refund_amount' => $params['refund_amount'],
            'total_amount' => $params['total_amount'],
            'payment_method' => $params['payment_method'],
            'transaction_id' => $params['transaction_id'] ?? '',
            'refund_status' => self::REFUND_STATUS_PROCESSING,
            'admin_id' => $params['admin_id'] ?? 0,
            'remark' => $params['remark'] ?? '',
            'created_at' => date('Y-m-d H:i:s')
        ];

        return Db::name('common_refund')->insertGetId($data);
    }

    /**
     * 余额支付退款
     * @param array $params 退款参数
     * @return bool
     */
    private static function balancePayRefund(array $params)
    {
        if (empty($params['user_id'])) {
            throw new Exception('用户ID不能为空');
        }

        $user = User::find($params['user_id']);
        if (!$user) {
            throw new Exception('用户不存在');
        }

        // 增加用户余额
        $user->user_money = ['inc', $params['refund_amount']];
        $user->save();

        // 记录账户变动
        AccountLogLogic::AccountRecord(
            $params['user_id'],
            $params['refund_amount'],
            1,
            AccountLog::refund_money,
            $params['remark'] ?? '退款',
            $params['source_id'],
            $params['refund_sn'] ?? ''
        );

        // 更新退款记录状态
        Db::name('common_refund')
            ->where('refund_sn', $params['refund_sn'])
            ->update([
                'refund_status' => self::REFUND_STATUS_SUCCESS,
                'refund_msg' => '余额退款成功',
                'updated_at' => date('Y-m-d H:i:s')
            ]);

        return true;
    }

    /**
     * 微信支付退款
     * @param array $params 退款参数
     * @param int $refund_id 退款记录ID
     * @return bool
     * @throws Exception
     */
    private static function wechatPayRefund(array $params, int $refund_id)
    {
        // 获取微信支付配置
        $config = WeChatServer::getPayConfigBySource($params['order_source'] ?? 'oa')['config'];

        if (empty($config)) {
            throw new Exception('请联系管理员设置微信相关配置!');
        }

        if (!isset($config['cert_path']) || !isset($config['key_path'])) {
            throw new Exception('请联系管理员设置微信证书!');
        }

        if (!file_exists($config['cert_path']) || !file_exists($config['key_path'])) {
            throw new Exception('微信证书不存在,请联系管理员!');
        }

        // 获取退款记录
        $refund_log = Db::name('common_refund')->where(['id' => $refund_id])->find();

        // 获取交易号
        $transaction_id = '';

        if (!empty($params['transaction_id'])) {
            $transaction_id = $params['transaction_id'];
        } else if ($params['refund_type'] == self::REFUND_TYPE_SHOP_DEPOSIT) {
            // 如果是商家保证金退款，从shop_deposit表获取transaction_id
            $deposit = Db::name('shop_deposit')->where('id', $params['source_id'])->find();
            if ($deposit && !empty($deposit['transaction_id'])) {
                $transaction_id = $deposit['transaction_id'];
                // 记录日志
                Db::name('log')->strict(false)->insert([
                    'type' => 'refund_transaction_id',
                    'log' => json_encode([
                        'source_id' => $params['source_id'],
                        'transaction_id' => $transaction_id
                    ]),
                    'creat_time' => date('Y-m-d H:i:s')
                ]);
            }
        }

        if (empty($transaction_id)) {
            throw new Exception('微信支付交易号不能为空');
        }

        // 准备退款数据
        $data = [
            'transaction_id' => $transaction_id,
            'refund_sn' => $refund_log['refund_sn'],
            'total_fee' => bcmul($params['total_amount'], 100), // 订单金额,单位为分
            'refund_fee' => bcmul($params['refund_amount'], 100), // 退款金额,单位为分
        ];

        // 调用微信退款接口
        $result = WeChatPayServer::refund($config, $data);

        if (isset($result['return_code']) && $result['return_code'] == 'FAIL') {
            // 更新退款记录为失败
            Db::name('common_refund')
                ->where(['id' => $refund_id])
                ->update([
                    'refund_status' => self::REFUND_STATUS_FAILED,
                    'refund_msg' => json_encode($result, JSON_UNESCAPED_UNICODE),
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
            throw new Exception($result['return_msg']);
        }

        if (isset($result['err_code_des'])) {
            // 更新退款记录为失败
            Db::name('common_refund')
                ->where(['id' => $refund_id])
                ->update([
                    'refund_status' => self::REFUND_STATUS_FAILED,
                    'refund_msg' => json_encode($result, JSON_UNESCAPED_UNICODE),
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
            throw new Exception($result['err_code_des']);
        }

        if ($result['return_code'] == 'SUCCESS' && $result['result_code'] == 'SUCCESS') {
            // 更新退款记录为成功
            Db::name('common_refund')
                ->where(['id' => $refund_id])
                ->update([
                    'refund_status' => self::REFUND_STATUS_SUCCESS,
                    'wechat_refund_id' => $result['refund_id'] ?? '',
                    'refund_msg' => json_encode($result, JSON_UNESCAPED_UNICODE),
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
            return true;
        } else {
            // 更新退款记录为失败
            Db::name('common_refund')
                ->where(['id' => $refund_id])
                ->update([
                    'refund_status' => self::REFUND_STATUS_FAILED,
                    'refund_msg' => json_encode($result, JSON_UNESCAPED_UNICODE),
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
            throw new Exception('微信支付退款失败');
        }
    }

    /**
     * 支付宝支付退款
     * @param array $params 退款参数
     * @param int $refund_id 退款记录ID
     * @return bool
     * @throws Exception
     */
    private static function aliPayRefund(array $params, int $refund_id)
    {
        // 获取退款记录
        $refund_log = Db::name('common_refund')->where(['id' => $refund_id])->find();

        // 调用支付宝退款接口
        $result = (new AliPayServer())->refund(
            $params['order_sn'] ?? $params['transaction_id'],
            $params['refund_amount'],
            $refund_log['refund_sn']
        );

        if ($result['code'] == '10000' && $result['msg'] == 'Success' && $result['fund_change'] == 'Y') {
            // 更新退款记录为成功
            Db::name('common_refund')
                ->where(['id' => $refund_id])
                ->update([
                    'refund_status' => self::REFUND_STATUS_SUCCESS,
                    'refund_msg' => json_encode($result, JSON_UNESCAPED_UNICODE),
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
            return true;
        } else {
            // 更新退款记录为失败
            Db::name('common_refund')
                ->where(['id' => $refund_id])
                ->update([
                    'refund_status' => self::REFUND_STATUS_FAILED,
                    'refund_msg' => json_encode($result, JSON_UNESCAPED_UNICODE),
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
            throw new Exception('支付宝退款失败');
        }
    }

    /**
     * 更新业务表状态
     * @param array $params 退款参数
     * @return bool
     */
    private static function updateBusinessStatus(array $params)
    {
        switch ($params['refund_type']) {
            // 商家保证金退款
            case self::REFUND_TYPE_SHOP_DEPOSIT:
                // 更新商家保证金状态
                Db::name('shop_deposit')
                    ->where('id', $params['source_id'])
                    ->update([
                        'pay_status' => 3, // 已退款
                        'refund_status' => 2, // 已退款
                        'refund_time' => date('Y-m-d H:i:s'),
                        'refund_remark' => $params['remark'] ?? '管理员操作退款',
                        'refund_admin_id' => $params['admin_id'] ?? 0,
                        'updated_at' => date('Y-m-d H:i:s')
                    ]);

                // 更新商家会员状态
                if (!empty($params['shop_id'])) {
                    Db::name('shop')
                        ->where('id', $params['shop_id'])
                        ->update([
                            'jcshop_vip' => 0, // 取消会员资格
                            'update_time' => time()
                        ]);
                }
                break;

            // 广告费退款
            case self::REFUND_TYPE_AD_FEE:
                // TODO: 实现广告费退款状态更新
                break;

            // 会员费退款
            case self::REFUND_TYPE_MEMBER_FEE:
                // TODO: 实现会员费退款状态更新
                break;

            // 代理保证金退款
            case self::REFUND_TYPE_AGENT_DEPOSIT:
                // TODO: 实现代理保证金退款状态更新
                break;

            // 检验费退款
            case self::REFUND_TYPE_INSPECTION_FEE:
                // TODO: 实现检验费退款状态更新
                break;
        }

        return true;
    }

    /**
     * 查询退款状态
     * @param int $refund_id 退款记录ID
     * @return array 退款状态信息
     */
    public static function queryRefundStatus($refund_id)
    {
        $refund = Db::name('common_refund')->where('id', $refund_id)->find();
        if (!$refund) {
            return ['code' => 0, 'msg' => '退款记录不存在'];
        }

        switch ($refund['refund_status']) {
            case self::REFUND_STATUS_PROCESSING:
                return ['code' => 0, 'msg' => '退款处理中', 'data' => $refund];
            case self::REFUND_STATUS_SUCCESS:
                return ['code' => 1, 'msg' => '退款成功', 'data' => $refund];
            case self::REFUND_STATUS_FAILED:
                return ['code' => 2, 'msg' => '退款失败', 'data' => $refund];
            default:
                return ['code' => 0, 'msg' => '未知状态', 'data' => $refund];
        }
    }
}
