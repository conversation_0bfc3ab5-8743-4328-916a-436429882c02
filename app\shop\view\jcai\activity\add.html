{layout name="layout2" /}
<style>
    .layui-form-label { width: 110px; }
</style>

<div class="layui-card layui-form" style="box-shadow:none;">
    <div class="layui-card-body">
        <!-- 选择商品 -->
        <div class="layui-form-item">
            <label class="layui-form-label"><font color="red">*</font>集采众筹商品：</label>
            <div class="layui-input-block">
                <a class="layui-btn layui-btn-normal layEvent" lay-event="select">选择集采众筹商品</a>
            </div>
        </div>
        <!-- 商品信息 -->
        <div class="layui-form-item">
            <label class="layui-form-label"></label>
            <div class="layui-input-block">
                <div class="goods_info"></div>
                <table id="goods_list" class="layui-table" lay-size="sm" style="display:none;width:630px;">
                    <thead>
                        <tr style="background-color: #f3f5f9">
                            <th style="width: 120px;text-align: center">商品规格</th>
                            <th style="width: 60px;text-align: center">商品价格</th>
                            <th style="width: 40px;text-align: center">集采众筹价格</th>
                        </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>
        <div class="layui-form-item" style="margin-bottom: 0;">
            <label for="intro" class="layui-form-label" style="width:110px;">集采众筹简介：</label>
            <div class="layui-input-block" >
                <textarea  style="width:300px;" id="intro"  placeholder="请输入内容" name="intro" class="layui-textarea" autocomplete="off" lay-verType="tips"></textarea>
            </div>
        </div>
        <br/>
        <!-- 集采众筹人数 -->
        <div class="layui-form-item" style="margin-bottom: 0;">
            <label for="people_num" class="layui-form-label">限购：</label>
            <div class="layui-input-inline">
                <input type="number" min="0" id="people_num" name="buy_num"
                       class="layui-input" autocomplete="off"
                       onkeyup="value=value.replace(/[^\d]/g,'')"
                       lay-verType="tips" lay-verify="required|number|people_num">
                <div class="layui-form-mid layui-word-aux" style="white-space:nowrap;">为0时，不限制购买数量</div>
            </div>
            <div class="layui-form-mid">件</div>
        </div>
        <!-- 集采众筹人数 -->
        <div class="layui-form-item" style="margin-bottom: 0;">
            <label for="people_num" class="layui-form-label">募集总金额：</label>
            <div class="layui-input-inline">
                <input type="number" min="0" id="total_num" name="buy_num"
                       class="layui-input" autocomplete="off"
                       onkeyup="value=value.replace(/[^\d]/g,'')"
                       lay-verType="tips" lay-verify="required|number|people_num">
                <div class="layui-form-mid layui-word-aux" style="white-space:nowrap;">为0时，不限制，时间结束后自动结束众筹并众筹成功</div>
            </div>
            <div class="layui-form-mid">元</div>
        </div>
        <!-- 集采众筹活动时间 -->
        <div class="layui-form-item" style="margin-bottom: 0;">
            <label class="layui-form-label"><font color="red">*</font>众筹期限：</label>
                <div class="layui-input-block">
                <div class="layui-inline">
                    <input type="text" id="activity_start_time" name="activity_start_time" class="layui-input" autocomplete="off" lay-verType="tips" lay-verify="required">
                </div>
                <div class="layui-inline">-</div>
                <div class="layui-inline">
                    <input type="text" id="activity_end_time" name="activity_end_time" class="layui-input" autocomplete="off" lay-verType="tips" lay-verify="required">
                </div>
            </div>
            <div class="layui-form-mid layui-word-aux" style="white-space:nowrap;margin-left:140px;">商品参与集采众筹营销活动的时间，超出活动时间则不能开启新团</div>
        </div>
        <!-- 拼够分享标题 -->
        <div class="layui-form-item" style="margin-bottom: 0;">
            <label for="share_title" class="layui-form-label" style="width:110px;">集采众筹分享标题：</label>
            <div class="layui-input-inline" style="width: 300px;">
                <input type="text" id="share_title" name="share_title" class="layui-input" autocomplete="off" lay-verType="tips">
                <div class="layui-form-mid layui-word-aux" style="white-space:nowrap;">分享集采众筹活动时的标题，不填则默认使用商品标题</div>
            </div>
        </div>
        <!-- 拼够分享简介 -->
        <div class="layui-form-item" style="margin-bottom: 0;">
            <label for="share_intro" class="layui-form-label" style="width:110px;">集采众筹分享简介：</label>
            <div class="layui-input-inline" style="width: 300px;">
                <input type="text" id="share_intro" name="share_intro" class="layui-input" autocomplete="off" lay-verType="tips">
                <div class="layui-form-mid layui-word-aux" style="white-space:nowrap;">分享集采众筹活动时的简介，不填则默认使用商品简介</div>
            </div>
        </div>
        <!-- 集采众筹状态 -->
<!--        <div class="layui-form-item" style="margin-bottom: 0;">-->
<!--            <label class="layui-form-label"><font color="red">*</font>集采众筹状态：</label>-->
<!--            <div class="layui-input-inline">-->
        <div style="display: none;">  <input type="radio" name="status" value="1" title="开启" > </div>
<!--                <input type="radio" name="status" value="0" title="关闭" checked>-->
<!--                <div class="layui-form-mid layui-word-aux" style="white-space:nowrap;">商品参与集采众筹营销活动的状态，停止活动则不能开启</div>-->
<!--            </div>-->
<!--        </div>-->
        <!-- 集众筹协议复选框和链接 -->

        <div class="layui-form-item" style="margin-bottom: 0;">
            <label class="layui-form-label">众筹协议:</label>
            <div class="layui-input-block">
                <input type="checkbox" name="agreement" value="1" lay-verify="required" lay-filter="agreementCheckbox" title="我同意">
                <a href="javascript:void(0);" class="layui-btn layui-btn-xs" id="agreementLink">查看《家庆福集采众筹支持协议》</a>
                <br/>
                <div class="layui-form-mid layui-word-aux" style="white-space:nowrap;">请认真阅读众筹协议内容，勾选“同意”即代表接受本协议所有内容。</div>

            </div>
        </div>
        <div class="layui-form-item layui-hide">
            <input type="button" lay-submit lay-filter="addSubmit" id="addSubmit" value="确认">
        </div>
    </div>
</div>

<script>
    layui.use(["laydate",'layer','form'], function () {
        var laydate = layui.laydate;
        var layer = layui.layer;
        var form = layui.form;

        laydate.render({type:'datetime',elem:'#activity_start_time',trigger:'click'});
        laydate.render({type:'datetime',elem:'#activity_end_time',trigger:'click'});

        // 显示协议弹窗
        document.getElementById('agreementLink').onclick = function() {
            layer.open({
                type: 1,
                title: '集众筹协议',
                area: ['600px', '400px'], // 弹窗大小
                content: '<div id="agreementContent" style="padding: 20px;"></div>',
                btn: ['确认已阅读'],
                yes: function(index, layero){
                    // 关闭弹窗时执行的操作
                    layer.close(index);
                }
            });

            // 加载协议内容
            fetch("{:url('jcai.Activity/agreementContent')}") // 替换为实际协议文件的路径
                .then(response => response.text())
                .then(data => {
                    document.getElementById('agreementContent').innerHTML = data;
                })
                .catch(error => console.error('Error loading agreement:', error));

            return false; // 阻止默认行为
        };

        // 表单验证
        form.verify({
            agreementCheckbox: function(value, item){ //value：表单的值、item：表单的DOM对象
                if(!item.is(':checked')){
                    return '您必须同意集众筹协议';
                }
            }
        });

        // 监听提交
        form.on('submit(formSubmit)', function(data){
            // 可以在这里添加其他表单提交前的处理逻辑
            return true; // 允许表单提交
        });
        var active = {
            select: function () {
                layer.open({
                    type: 2
                    ,title: "选择众筹商品"
                    ,content: "{:url('jcai.Activity/select')}"
                    ,area: ["80%", "90%"]
                    ,btn: ["确定", "取消"]
                    ,yes: function(index){
                        var iframeWindow = window["layui-layer-iframe" + index];
                        var data = iframeWindow.callbackData();
                        var goods_ids = [];
                        console.log(data)
                        if(data.length){
                            goods_ids = [];
                            $('#goods_list tbody').empty();
                        }

                        data.forEach(function(item) {
                            // 商品基本信息
                            var goods_info_html  = '<img src="'+item.image+'" alt="商品图片" style="width:80px;height:80px;">\n';
                                goods_info_html += '<span style="margin-left:5px;">'+item.name+'</span>\n';
                                goods_info_html += '<input type="hidden" name="goods_id" value="'+item.id+'">';
                                $(".goods_info").html(goods_info_html);

                            // 商品规格表格
                            for(var i = 0; item.GoodsItem.length > i; i++){
                                var goods_item = item.GoodsItem[i];
                                if(goods_ids.indexOf(goods_item.id) === -1) {
                                    goods_ids.push(goods_item.id);
                                    var goods_html  = '<tr>\n';
                                        goods_html += '<td style="text-align: center">'+goods_item.spec_value_str+'</td>\n';
                                        goods_html += '<td style="text-align: center">'+goods_item.price+'</td>\n';
                                        goods_html += '<td style="width: 40px;">';
                                        goods_html += '<input type="number" name=item['+item.id+']['+goods_item.id+'] lay-verType="tips" lay-verify="required" autocomplete="off" class="layui-input">\n';
                                        goods_html += '</td></tr>';
                                        $('#goods_list tbody').append(goods_html);
                                }
                            }

                            // 显示规格表格
                            $('table#goods_list').show();
                        });


                    }
                });
            }
        };
        like.eventClick(active);
    })
</script>