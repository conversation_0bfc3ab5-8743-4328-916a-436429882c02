<?php
namespace app\common\model;

use think\Model;

/**
 * 采购商联系记录模型
 * Class PurchaserContactRecord
 * @package app\common\model
 */
class PurchaserContactRecord extends Model
{
    protected $name = 'purchaser_contact_record';
    
    // 设置字段信息
    protected $schema = [
        'id' => 'int',
        'user_id' => 'int',
        'purchaser_id' => 'int',
        'contact_time' => 'int',
        'ip' => 'string',
        'user_agent' => 'string',
        'source' => 'string',
        'create_time' => 'int',
        'update_time' => 'int',
    ];

    // 自动时间戳
    protected $autoWriteTimestamp = false;

    // 关联用户表
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    // 关联采购商（社区文章）
    public function purchaser()
    {
        return $this->belongsTo(CommunityArticle::class, 'purchaser_id', 'id');
    }

    // 获取联系时间文本
    public function getContactTimeTextAttr($value, $data)
    {
        return date('Y-m-d H:i:s', $data['contact_time']);
    }

    // 获取来源文本
    public function getSourceTextAttr($value, $data)
    {
        $sourceMap = [
            'web' => '网页',
            'app' => '应用',
            'mini' => '小程序',
            'h5' => 'H5页面'
        ];
        
        return $sourceMap[$data['source']] ?? $data['source'];
    }
}
