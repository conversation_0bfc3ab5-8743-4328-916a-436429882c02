#!/bin/bash

# 版本管理脚本
# 用法: ./version_manager.sh [list|current|rollback|cleanup]

set -e

# 配置
RELEASES_DIR="/var/www/kshop-releases"
PROD_DIR="/var/www/kshop-production"
BACKUP_DIR="/var/www/kshop-backups"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示当前版本
show_current_version() {
    echo "=================================="
    echo "📋 当前版本信息"
    echo "=================================="
    
    if [ -L "$PROD_DIR" ]; then
        local current_target=$(readlink "$PROD_DIR")
        local current_version=$(basename "$current_target")
        echo "当前版本: $current_version"
        echo "部署路径: $current_target"
        
        # 显示版本创建时间
        if [ -d "$current_target" ]; then
            local create_time=$(stat -c %y "$current_target" | cut -d'.' -f1)
            echo "部署时间: $create_time"
        fi
    else
        echo "❌ 未找到当前版本信息"
    fi
    
    echo ""
}

# 列出所有可用版本
list_versions() {
    echo "=================================="
    echo "📦 可用版本列表"
    echo "=================================="
    
    if [ ! -d "$RELEASES_DIR" ]; then
        log_warn "版本目录不存在: $RELEASES_DIR"
        return
    fi
    
    local current_version=""
    if [ -L "$PROD_DIR" ]; then
        current_version=$(basename "$(readlink "$PROD_DIR")")
    fi
    
    echo "格式: [状态] 版本号 | 大小 | 创建时间"
    echo "----------------------------------"
    
    for version_dir in "$RELEASES_DIR"/*; do
        if [ -d "$version_dir" ]; then
            local version=$(basename "$version_dir")
            local size=$(du -sh "$version_dir" | cut -f1)
            local create_time=$(stat -c %y "$version_dir" | cut -d'.' -f1)
            
            if [ "$version" = "$current_version" ]; then
                echo -e "${GREEN}[当前]${NC} $version | $size | $create_time"
            else
                echo -e "${BLUE}[可用]${NC} $version | $size | $create_time"
            fi
        fi
    done
    
    echo ""
    
    # 显示备份版本
    if [ -d "$BACKUP_DIR" ] && [ "$(ls -A "$BACKUP_DIR" 2>/dev/null)" ]; then
        echo "📁 备份版本:"
        ls -lt "$BACKUP_DIR" | head -5 | tail -n +2 | while read line; do
            local backup_name=$(echo "$line" | awk '{print $9}')
            local backup_time=$(echo "$line" | awk '{print $6, $7, $8}')
            echo "   $backup_name ($backup_time)"
        done
        echo ""
    fi
}

# 交互式回退
interactive_rollback() {
    echo "=================================="
    echo "🔄 版本回退"
    echo "=================================="
    
    # 显示当前版本
    show_current_version
    
    # 显示可用版本
    if [ ! -d "$RELEASES_DIR" ]; then
        log_error "版本目录不存在: $RELEASES_DIR"
        exit 1
    fi
    
    local versions=($(ls -1 "$RELEASES_DIR" 2>/dev/null | sort -V))
    
    if [ ${#versions[@]} -eq 0 ]; then
        log_error "没有可用的版本进行回退"
        exit 1
    fi
    
    echo "可回退的版本:"
    for i in "${!versions[@]}"; do
        echo "  $((i+1)). ${versions[i]}"
    done
    echo ""
    
    # 选择版本
    while true; do
        read -p "请选择要回退的版本 (输入序号或版本号): " choice
        
        if [[ "$choice" =~ ^[0-9]+$ ]] && [ "$choice" -ge 1 ] && [ "$choice" -le ${#versions[@]} ]; then
            local selected_version="${versions[$((choice-1))]}"
            break
        elif [[ " ${versions[@]} " =~ " ${choice} " ]]; then
            local selected_version="$choice"
            break
        else
            echo "无效选择，请重新输入"
        fi
    done
    
    # 确认回退
    echo ""
    log_warn "即将回退到版本: $selected_version"
    read -p "确认继续？(y/N): " -n 1 -r
    echo ""
    
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "回退操作已取消"
        exit 0
    fi
    
    # 执行回退
    if [ -f "scripts/deploy.sh" ]; then
        bash scripts/deploy.sh rollback "$selected_version"
    else
        log_error "部署脚本不存在: scripts/deploy.sh"
        exit 1
    fi
}

# 清理旧版本
cleanup_old_versions() {
    echo "=================================="
    echo "🧹 清理旧版本"
    echo "=================================="
    
    local keep_count=${1:-5}  # 默认保留5个版本
    
    if [ ! -d "$RELEASES_DIR" ]; then
        log_warn "版本目录不存在: $RELEASES_DIR"
        return
    fi
    
    local current_version=""
    if [ -L "$PROD_DIR" ]; then
        current_version=$(basename "$(readlink "$PROD_DIR")")
    fi
    
    # 获取所有版本，按时间排序
    local versions=($(ls -1t "$RELEASES_DIR" 2>/dev/null))
    local total_count=${#versions[@]}
    
    if [ $total_count -le $keep_count ]; then
        log_info "当前版本数量($total_count)不超过保留数量($keep_count)，无需清理"
        return
    fi
    
    echo "当前版本总数: $total_count"
    echo "保留版本数量: $keep_count"
    echo "将要删除的版本:"
    
    local to_delete=()
    for ((i=$keep_count; i<$total_count; i++)); do
        local version="${versions[i]}"
        if [ "$version" != "$current_version" ]; then
            echo "  - $version"
            to_delete+=("$version")
        fi
    done
    
    if [ ${#to_delete[@]} -eq 0 ]; then
        log_info "没有可清理的版本"
        return
    fi
    
    echo ""
    read -p "确认删除以上版本？(y/N): " -n 1 -r
    echo ""
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        for version in "${to_delete[@]}"; do
            log_info "删除版本: $version"
            sudo rm -rf "$RELEASES_DIR/$version"
        done
        log_info "清理完成！"
    else
        log_info "清理操作已取消"
    fi
    
    # 清理旧备份
    echo ""
    log_info "清理旧备份文件..."
    if [ -d "$BACKUP_DIR" ]; then
        cd "$BACKUP_DIR"
        sudo ls -t | tail -n +11 | sudo xargs -r rm -rf
        log_info "备份清理完成（保留最近10个备份）"
    fi
}

# 显示帮助信息
show_help() {
    echo "版本管理脚本"
    echo ""
    echo "用法: $0 [命令] [参数]"
    echo ""
    echo "命令:"
    echo "  current              显示当前版本信息"
    echo "  list                 列出所有可用版本"
    echo "  rollback             交互式版本回退"
    echo "  cleanup [数量]       清理旧版本（默认保留5个）"
    echo "  help                 显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 current           # 查看当前版本"
    echo "  $0 list              # 列出所有版本"
    echo "  $0 rollback          # 交互式回退"
    echo "  $0 cleanup 3         # 只保留最新3个版本"
}

# 主函数
main() {
    local command=${1:-"help"}
    
    case $command in
        "current")
            show_current_version
            ;;
        "list")
            show_current_version
            list_versions
            ;;
        "rollback")
            interactive_rollback
            ;;
        "cleanup")
            cleanup_old_versions "$2"
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

# 执行主函数
main "$@"
