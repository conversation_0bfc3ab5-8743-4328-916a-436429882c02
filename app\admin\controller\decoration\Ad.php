<?php

namespace app\admin\controller\decoration;
use app\admin\logic\decoration\AdLogic;
use app\admin\validate\decoration\AdValidate;
use app\common\basics\AdminBase;
use app\common\enum\AdEnum;
use app\common\server\JsonServer;

class Ad extends AdminBase{

    /**
     * Notes:获取广告列表
     * @return \think\response\Json|\think\response\View
     * @author: cjhao 2021/4/20 11:00
     */
    public function lists(){
        if($this->request->isAjax()){
            $get = $this->request->get();
            $get['is_sys']=0;
            $list = AdLogic::lists($get);
            return JsonServer::success('',$list);
        }
        return view();
    }
    
     /*
      * 获取配置列表
      */
    public function configList(){
        if($this->request->isAjax()){
            $get = $this->request->get();
            $get['status']=0;
            $list = AdLogic::lists($get);
            return JsonServer::success('',$list);
        }
        return view();
    }
    
    

    /**
     * Notes:添加广告
     * @return \think\response\Json|\think\response\View
     * @author: cjhao 2021/4/20 11:00
     */
    public function add(){
        if($this->request->isAjax()){
            $post = $this->request->post();
            $post['del'] = 0;
            (new AdValidate())->goCheck('Add',$post);

            $result = AdLogic::add($post);
            if($result){
                return JsonServer::success('添加成功');
            }
            return JsonServer::error('添加失败');
        }
        $terminal = $this->request->get('terminal');
        $position_list = AdLogic::getPositionList($terminal);
        $category_list = AdLogic::getCategoryList();
        $link_page = AdEnum::getLinkPage($terminal);
        $shop_list = AdLogic::getShopList();
        return view('',['position_list'=>$position_list,'category_list'=>$category_list,'link_page'=>$link_page,'shop_list'=>$shop_list]);
    }

    /**
     * Notes:编辑广告
     * @return \think\response\Json|\think\response\View
     * @author: cjhao 2021/4/20 11:01
     */
    public function edit(){
        if($this->request->isAjax()){
            $post = $this->request->post();
            $post['del'] = 0;
            (new AdValidate())->goCheck('edit',$post);
            AdLogic::edit($post);
            return JsonServer::success('修改成功');


        }
        $id = $this->request->get('id');
        $detail = AdLogic::getAd($id);
        $position_list = AdLogic::getPositionList($detail['terminal']);
        $category_list = AdLogic::getCategoryList();
        $link_page = AdEnum::getLinkPage($detail['terminal']);
        $shop_list = AdLogic::getShopList();
        return view('',['detail'=>$detail,'position_list'=>$position_list,'category_list'=>$category_list,'link_page'=>$link_page,'shop_list'=>$shop_list]);
    }

    /**
     * Notes:删除广告
     * @return \think\response\Json
     * @author: cjhao 2021/4/20 11:01
     */
    public function del(){
        $id = $this->request->post('id');
        (new AdValidate())->goCheck('del');
        ADLogic::del($id);
        return JsonServer::success('删除成功');
    }

    /**
     * Notes:切换广告状态
     * @return \think\response\Json
     * @author: cjhao 2021/4/20 11:01
     */
    public function swtichStatus(){
        (new AdValidate())->goCheck('swtich');
        $post = $this->request->post();
        ADLogic::swtichStatus($post);
        return JsonServer::success('操作成功');
    }

    /**
     * Notes:获取广告位列表
     * @return \think\response\Json
     * @author: cjhao 2021/4/22 11:09
     */
    public function getPositionList(){
        $terminal = $this->request->get('terminal');
        $list = ADLogic::getPositionList($terminal);
        return JsonServer::success('',$list->toArray());
    }





    /////
    /**
     * Notes:获取广告列表
     * @return \think\response\Json|\think\response\View
     * @author: cjhao 2021/4/20 11:00
     */
    public function lists2(){
        if($this->request->isAjax()){
            $get = $this->request->get();
            $get['is_sys']=1;
            $list = AdLogic::lists($get);
            return JsonServer::success('',$list);
        }
        return view();
    }

    /*
     * 获取配置列表
     */
    public function configList2(){
        if($this->request->isAjax()){
            $get = $this->request->get();
            $get['status']=0;
            $list = AdLogic::lists($get);
            return JsonServer::success('',$list);
        }
        return view();
    }



    /**
     * Notes:添加广告
     * @return \think\response\Json|\think\response\View
     * @author: cjhao 2021/4/20 11:00
     */
    public function add2(){
        if($this->request->isAjax()){
            $post = $this->request->post();
            $post['del'] = 0;
            (new AdValidate())->goCheck('Add',$post);

            $result = AdLogic::add($post);
            if($result){
                return JsonServer::success('添加成功');
            }
            return JsonServer::error('添加失败');
        }
        $terminal = $this->request->get('terminal');
        $position_list = AdLogic::getPositionList($terminal,1);
        $category_list = AdLogic::getCategoryList();
        $link_page = AdEnum::getLinkPage($terminal);
        $shop_list = AdLogic::getShopList();
        return view('',['position_list'=>$position_list,'category_list'=>$category_list,'link_page'=>$link_page,'shop_list'=>$shop_list]);
    }

    /**
     * Notes:编辑广告
     * @return \think\response\Json|\think\response\View
     * @author: cjhao 2021/4/20 11:01
     */
    public function edit2(){
        if($this->request->isAjax()){
            $post = $this->request->post();
            $post['del'] = 0;
            (new AdValidate())->goCheck('edit',$post);
            AdLogic::edit($post);
            return JsonServer::success('修改成功');


        }
        $id = $this->request->get('id');
        $detail = AdLogic::getAd($id);
        $position_list = AdLogic::getPositionList($detail['terminal'],1);
        $category_list = AdLogic::getCategoryList();
        $link_page = AdEnum::getLinkPage($detail['terminal']);
        $shop_list = AdLogic::getShopList();
        return view('',['detail'=>$detail,'position_list'=>$position_list,'category_list'=>$category_list,'link_page'=>$link_page,'shop_list'=>$shop_list]);
    }

    /**
     * Notes:删除广告
     * @return \think\response\Json
     * @author: cjhao 2021/4/20 11:01
     */
    public function del2(){
        $id = $this->request->post('id');
        (new AdValidate())->goCheck('del');
        ADLogic::del($id);
        return JsonServer::success('删除成功');
    }

    /**
     * Notes:切换广告状态
     * @return \think\response\Json
     * @author: cjhao 2021/4/20 11:01
     */
    public function swtichStatus2(){
        (new AdValidate())->goCheck('swtich');
        $post = $this->request->post();
        ADLogic::swtichStatus($post);
        return JsonServer::success('操作成功');
    }

    /**
     * Notes:获取广告位列表
     * @return \think\response\Json
     * @author: cjhao 2021/4/22 11:09
     */
    public function getPositionList2(){
        $terminal = $this->request->get('terminal');
        $is_sys=1;
        $list = ADLogic::getPositionList($terminal,$is_sys);
        return JsonServer::success('',$list->toArray());
    }


}