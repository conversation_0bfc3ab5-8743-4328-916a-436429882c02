(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["bundle-pages-activity_detail-activity_detail"],{"0360":function(t,e,n){var a=n("24fb");e=a(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.activity-detail[data-v-f2137068]{overflow:hidden}.activity-detail .header[data-v-f2137068]{height:%?410?%;width:100%;position:relative}.activity-detail .header .header-bg[data-v-f2137068]{position:absolute;width:100%;height:100%}.activity-detail .header .header-con[data-v-f2137068]{position:relative;padding-top:%?50?%}.activity-detail .header .header-con .title[data-v-f2137068]{font-size:%?60?%}.activity-detail .header .header-con .desc[data-v-f2137068]{margin-top:%?30?%;background-color:rgba(255,203,203,.5);padding:%?4?% %?40?%}.activity-detail .content[data-v-f2137068]{position:relative;margin-top:%?-140?%;padding:0 %?20?%}',""]),t.exports=e},"33d0":function(t,e,n){var a=n("0360");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var i=n("4f06").default;i("2b7ad3f3",a,!0,{sourceMap:!1,shadowMode:!1})},4287:function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return r})),n.d(e,"a",(function(){return a}));var a={goodsList:n("2d92").default},i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("mescroll-body",{ref:"mescrollRef",attrs:{up:t.upOption},on:{init:function(e){arguments[0]=e=t.$handleEvent(e),t.mescrollInit.apply(void 0,arguments)},up:function(e){arguments[0]=e=t.$handleEvent(e),t.upCallback.apply(void 0,arguments)},down:function(e){arguments[0]=e=t.$handleEvent(e),t.downCallback.apply(void 0,arguments)}}},[n("v-uni-view",{staticClass:"activity-detail"},[t.goodsList.length?n("v-uni-view",{staticClass:"header"},[n("v-uni-image",{staticClass:"header-bg",attrs:{src:"/bundle/static/activity_detail_bg.png"}}),n("v-uni-view",{staticClass:"header-con flex-col col-center"},[n("v-uni-view",{staticClass:"title white"},[t._v(t._s(t.name))]),n("v-uni-view",{staticClass:"desc white sm br60"},[t._v(t._s(t.title))])],1)],1):t._e(),n("v-uni-view",{staticClass:"content"},[n("v-uni-view",{staticClass:"goods-container"},[n("goods-list",{attrs:{list:t.goodsList,type:"activity"}})],1)],1)],1)],1)},r=[]},6280:function(t,e,n){"use strict";n("7a82");var a=n("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("99af");var i=n("c60f"),r=a(n("bde1")),o={mixins:[r.default],data:function(){return{upOption:{empty:{icon:"/static/images/goods_null.png",tip:"暂无商品"}},goodsList:[],name:"",title:""}},onLoad:function(t){var e=this.$Route.query,n=e.id,a=e.title,i=e.name;this.id=n,this.title=a,this.name=i,uni.setNavigationBarTitle({title:i})},methods:{upCallback:function(t){var e=this,n=t.num,a=t.size;(0,i.getActivityGoodsLists)({page_size:a,page_no:n,id:this.id}).then((function(n){var a=n.data;1==t.num&&(e.goodsList=[]);var i=a.list,r=i.length,o=!!a.more;e.goodsList=e.goodsList.concat(i),e.mescroll.endSuccess(r,o)})).catch((function(){e.mescroll.endErr()}))}}};e.default=o},"877a":function(t,e,n){"use strict";n.r(e);var a=n("6280"),i=n.n(a);for(var r in a)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(r);e["default"]=i.a},"9f49":function(t,e,n){"use strict";var a=n("33d0"),i=n.n(a);i.a},bde1:function(t,e,n){"use strict";n("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a={data:function(){return{mescroll:null}},onPullDownRefresh:function(){this.mescroll&&this.mescroll.onPullDownRefresh()},onPageScroll:function(t){this.mescroll&&this.mescroll.onPageScroll(t)},onReachBottom:function(){this.mescroll&&this.mescroll.onReachBottom()},methods:{mescrollInit:function(t){console.log(t),this.mescroll=t,this.mescrollInitByRef()},mescrollInitByRef:function(){if(!this.mescroll||!this.mescroll.resetUpScroll){var t=this.$refs.mescrollRef;t&&(this.mescroll=t.mescroll)}},downCallback:function(){var t=this;this.mescroll.optUp.use?this.mescroll.resetUpScroll():setTimeout((function(){t.mescroll.endSuccess()}),500)},upCallback:function(){var t=this;setTimeout((function(){t.mescroll.endErr()}),500)}},mounted:function(){this.mescrollInitByRef()}},i=a;e.default=i},c60f:function(t,e,n){"use strict";n("7a82");var a=n("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.cancelIntegralOrder=function(t){return i.default.post("integral_order/cancel",{id:t})},e.closeBargainOrder=function(t){return i.default.get("bargain/closeBargain",{params:t})},e.confirmIntegralOrder=function(t){return i.default.post("integral_order/confirm",{id:t})},e.delIntegralOrder=function(t){return i.default.post("integral_order/del",{id:t})},e.getActivityGoodsLists=function(t){return i.default.get("activity_area/activityGoodsList",{params:t})},e.getBargainActivityDetail=function(t){return i.default.get("bargain/bargainDetail",{params:t})},e.getBargainActivityList=function(t){return i.default.get("bargain/orderList",{params:t})},e.getBargainDetail=function(t){return i.default.get("bargain/detail",{params:t})},e.getBargainList=function(t){return i.default.get("bargain/lists",{params:t})},e.getBargainNumber=function(){return i.default.get("bargain/barginNumber")},e.getBargainPost=function(t){return i.default.get("share/shareBargain",{params:t})},e.getCoupon=function(t){return i.default.post("coupon/getCoupon",{coupon_id:t})},e.getCouponList=function(t){return i.default.get("coupon/getCouponList",{params:t})},e.getGroupList=function(t){return i.default.get("team/activity",{params:t})},e.getIntegralGoods=function(t){return i.default.get("integral_goods/lists",{params:t})},e.getIntegralGoodsDetail=function(t){return i.default.get("integral_goods/detail",{params:t})},e.getIntegralOrder=function(t){return i.default.get("integral_order/lists",{params:t})},e.getIntegralOrderDetail=function(t){return i.default.get("integral_order/detail",{params:{id:t}})},e.getIntegralOrderTraces=function(t){return i.default.get("integral_order/orderTraces",{params:{id:t}})},e.getMyCoupon=function(t){return i.default.get("coupon/myCouponList",{params:t})},e.getOrderCoupon=function(t){return i.default.post("coupon/getBuyCouponList",t)},e.getSeckillGoods=function(t){return i.default.get("seckill_goods/getSeckillGoods",{params:t})},e.getSeckillTime=function(){return i.default.get("seckill_goods/getSeckillTime")},e.getSignLists=function(){return i.default.get("sign/lists")},e.getSignRule=function(){return i.default.get("sign/rule")},e.getTeamInfo=function(t){return i.default.get("team/teamInfo",{params:t})},e.getUserGroup=function(t){return i.default.get("team/record",{params:t})},e.helpBargain=function(t){return i.default.post("bargain/knife",t)},e.integralSettlement=function(t){return i.default.get("integral_order/settlement",{params:t})},e.integralSubmitOrder=function(t){return i.default.post("integral_order/submitOrder",t)},e.launchBargain=function(t){return i.default.post("bargain/sponsor",t)},e.teamBuy=function(t){return i.default.post("team/buy",t)},e.teamCheck=function(t){return i.default.post("team/check",t)},e.teamKaiTuan=function(t){return i.default.post("team/kaituan",t)},e.userSignIn=function(){return i.default.get("sign/sign")};var i=a(n("3b33"));n("b08d")},c96c:function(t,e,n){"use strict";n.r(e);var a=n("4287"),i=n("877a");for(var r in i)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(r);n("9f49");var o=n("f0c5"),s=Object(o["a"])(i["default"],a["b"],a["c"],!1,null,"f2137068",null,!1,a["a"],void 0);e["default"]=s.exports}}]);