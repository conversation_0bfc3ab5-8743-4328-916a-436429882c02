<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Clb\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * DescribeRewrite请求参数结构体
 *
 * @method string getLoadBalancerId() 获取负载均衡实例ID。
 * @method void setLoadBalancerId(string $LoadBalancerId) 设置负载均衡实例ID。
 * @method array getSourceListenerIds() 获取负载均衡监听器ID数组。
 * @method void setSourceListenerIds(array $SourceListenerIds) 设置负载均衡监听器ID数组。
 * @method array getSourceLocationIds() 获取负载均衡转发规则的ID数组。
 * @method void setSourceLocationIds(array $SourceLocationIds) 设置负载均衡转发规则的ID数组。
 */
class DescribeRewriteRequest extends AbstractModel
{
    /**
     * @var string 负载均衡实例ID。
     */
    public $LoadBalancerId;

    /**
     * @var array 负载均衡监听器ID数组。
     */
    public $SourceListenerIds;

    /**
     * @var array 负载均衡转发规则的ID数组。
     */
    public $SourceLocationIds;

    /**
     * @param string $LoadBalancerId 负载均衡实例ID。
     * @param array $SourceListenerIds 负载均衡监听器ID数组。
     * @param array $SourceLocationIds 负载均衡转发规则的ID数组。
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("LoadBalancerId",$param) and $param["LoadBalancerId"] !== null) {
            $this->LoadBalancerId = $param["LoadBalancerId"];
        }

        if (array_key_exists("SourceListenerIds",$param) and $param["SourceListenerIds"] !== null) {
            $this->SourceListenerIds = $param["SourceListenerIds"];
        }

        if (array_key_exists("SourceLocationIds",$param) and $param["SourceLocationIds"] !== null) {
            $this->SourceLocationIds = $param["SourceLocationIds"];
        }
    }
}
