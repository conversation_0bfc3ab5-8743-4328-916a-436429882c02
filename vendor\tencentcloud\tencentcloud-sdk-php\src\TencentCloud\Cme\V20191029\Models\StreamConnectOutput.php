<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Cme\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * 云转推输出源。
 *
 * @method string getId() 获取云转推输出源标识，转推项目级别唯一。若不填则由后端生成。
 * @method void setId(string $Id) 设置云转推输出源标识，转推项目级别唯一。若不填则由后端生成。
 * @method string getName() 获取云转推输出源名称。
 * @method void setName(string $Name) 设置云转推输出源名称。
 * @method string getType() 获取云转推输出源类型，取值：
<li>URL ：URL类型</li>
不填默认为URL类型。
 * @method void setType(string $Type) 设置云转推输出源类型，取值：
<li>URL ：URL类型</li>
不填默认为URL类型。
 * @method string getPushUrl() 获取云转推推流地址。
 * @method void setPushUrl(string $PushUrl) 设置云转推推流地址。
 */
class StreamConnectOutput extends AbstractModel
{
    /**
     * @var string 云转推输出源标识，转推项目级别唯一。若不填则由后端生成。
     */
    public $Id;

    /**
     * @var string 云转推输出源名称。
     */
    public $Name;

    /**
     * @var string 云转推输出源类型，取值：
<li>URL ：URL类型</li>
不填默认为URL类型。
     */
    public $Type;

    /**
     * @var string 云转推推流地址。
     */
    public $PushUrl;

    /**
     * @param string $Id 云转推输出源标识，转推项目级别唯一。若不填则由后端生成。
     * @param string $Name 云转推输出源名称。
     * @param string $Type 云转推输出源类型，取值：
<li>URL ：URL类型</li>
不填默认为URL类型。
     * @param string $PushUrl 云转推推流地址。
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("Id",$param) and $param["Id"] !== null) {
            $this->Id = $param["Id"];
        }

        if (array_key_exists("Name",$param) and $param["Name"] !== null) {
            $this->Name = $param["Name"];
        }

        if (array_key_exists("Type",$param) and $param["Type"] !== null) {
            $this->Type = $param["Type"];
        }

        if (array_key_exists("PushUrl",$param) and $param["PushUrl"] !== null) {
            $this->PushUrl = $param["PushUrl"];
        }
    }
}
