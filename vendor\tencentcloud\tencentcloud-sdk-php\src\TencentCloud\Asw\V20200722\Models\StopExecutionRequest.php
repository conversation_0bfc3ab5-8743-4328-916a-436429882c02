<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Asw\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * StopExecution请求参数结构体
 *
 * @method string getExecutionQrn() 获取执行名称
 * @method void setExecutionQrn(string $ExecutionQrn) 设置执行名称
 */
class StopExecutionRequest extends AbstractModel
{
    /**
     * @var string 执行名称
     */
    public $ExecutionQrn;

    /**
     * @param string $ExecutionQrn 执行名称
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("ExecutionQrn",$param) and $param["ExecutionQrn"] !== null) {
            $this->ExecutionQrn = $param["ExecutionQrn"];
        }
    }
}
