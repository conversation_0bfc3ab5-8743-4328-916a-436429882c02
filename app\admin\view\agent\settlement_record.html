{layout name="layout1" /}

<div class="wrapper">
    <div class="layui-card">
        <!-- 结算记录 -->
        <h2 style="padding:20px;">结算记录</h2>
        <!-- 主体区域 -->
        <div class="layui-card-body">
            <table id="like-table-lists" lay-filter="like-table-lists"></table>
            <script type="text/html" id="table-operation">
                <button class="layui-btn layui-btn-sm layui-btn-primary" lay-event="detail">详情</button>
                {{#  if(d.freeze_status == 1){ }}
                <button class="layui-btn layui-btn-sm u layui-bg-red" lay-event="status">冻结</button>
                {{#  } else if(d.freeze_status == 0) { }}
                <button class="layui-btn layui-btn-sm layui-btn-primary" lay-event="status">解冻</button>
                {{#  } }}
            </script>
        </div>
    </div>
</div>

<script>
    layui.use(["table"], function(){
        var table = layui.table;
        like.tableLists("#like-table-lists", "{:url('agent.agent/settlementRecord')}?agent_id={$agent_id}", [
            {field:"id", width:60, title:"ID"}
            ,{field:"sn", width:100, align:"center", title:"佣金批次号", templet:"#table-storeInfo"}
            ,{field:"order_total", width:100, align:"center",title:"订单总额"}
            ,{field:"ratio", width:80, align:"center", title:"佣金百分比"}
            ,{field:"money", width:100, align:"center", title:"应得佣金"}
            ,{field:"status_txt", width:180, align:"center", title:"佣金状态"}
            ,{field:"order_type", width:180, align:"center", title:"订单类型"}
            // ,{field:"pt_get", width:160, align:"center", title:"平台结余"}
            ,{field:"yu_settlement_time", width:160, align:"center", title:"预计结算时间"}
            ,{field:"settlement_time", width:100, align:"center", title:"实际结算时间"}
            ,{field:"remark", width:100, align:"center", title:"原因备注"}
            ,{title:"操作", width:200, align:"center", fixed:"right", toolbar:"#table-operation"}
        ]);

        var active = {
            detail: function (obj) {
                layer.open({
                    type: 2
                    ,title: "订单详情"
                    ,content: "{:url('agent.agent/settlementDetail')}?id="+obj.data.id
                    ,area: ["90%", "90%"]
                });
            },
            status: function(obj) {
                layer.open({
                    type: 2
                    ,title: "佣金操作"
                    ,content: "{:url('agent.agent/isOrderFreeze')}?id=" + obj.data.id
                    ,area: ["90%", "90%"]
                    ,btn: ["确定", "取消"]
                    ,yes: function(index, layero){
                        var iframeWindow = window["layui-layer-iframe" + index];
                        var submit = layero.find("iframe").contents().find("#addSubmit");
                        iframeWindow.layui.form.on("submit(addSubmit)", function(data){
                            data.field['id'] = obj.data.id;
                            like.ajax({
                                url: "{:url('agent.agent/isOrderFreeze')}",
                                data: data.field,
                                type: "POST",
                                success:function(res) {
                                    if(res.code === 1) {
                                        layui.layer.msg(res.msg, {offset: '15px', icon: 1, time: 1000});
                                        layer.close(index);
                                        table.reload("like-table-lists", {
                                            where: {},
                                            page: { cur: 1 }
                                        });
                                    }
                                }
                            });
                        });
                        submit.trigger("click");
                    }
                });
            },
        };
        like.eventClick(active);
    })
</script>