<?php

namespace app\common\enum;


/**
 * 众筹审核状态枚举
 * Class JcaiAuditEnum
 * @package app\common\enum
 */
class JcaiAuditEnum
{
    const PENDING = 0;  // 待审核
    const APPROVED = 1; // 审核通过
    const REJECTED = 2; // 审核拒绝

    /**
     * @notes 获取描述
     * @param int $value
     * @return string
     */
    public static function getDesc($value): string
    {
        switch ($value) {
            case self::PENDING:
                return '待审核';
            case self::APPROVED:
                return '审核通过';
            case self::REJECTED:
                return '审核拒绝';
            default:
                return '未知状态';
        }
    }

    /**
     * @notes 获取列表
     * @return array[]
     */
    public static function getList(): array
    {
        return [
            ['value' => self::PENDING, 'desc' => '待审核'],
            ['value' => self::APPROVED, 'desc' => '审核通过'],
            ['value' => self::REJECTED, 'desc' => '审核拒绝'],
        ];
    }
}
