# 商家等级体系路由配置

## API路由配置

在 `route/api.php` 中添加以下路由：

```php
// 商家等级相关路由
Route::group('shop_tier', function () {
    // 获取等级配置列表
    Route::get('configs', 'ShopTier/tierConfigs');
    
    // 选择等级并创建订单
    Route::post('select', 'ShopTier/selectTier');
    
    // 创建支付订单
    Route::post('payment', 'ShopTier/createPayment');
    
    // 获取支付状态
    Route::get('payment_status', 'ShopTier/getPaymentStatus');
    
    // 商家等级升级
    Route::post('upgrade', 'ShopTier/upgrade');
    
    // 获取可升级等级列表
    Route::get('upgradable', 'ShopTier/getUpgradableTiers');
})->middleware(['ApiAuth']);
```

## 接口使用示例

### 1. 获取等级配置
```
GET /api/shop_tier/configs
```

### 2. 选择等级
```
POST /api/shop_tier/select
{
    "target_tier_level": 1
}
```

### 3. 创建支付
```
POST /api/shop_tier/payment
{
    "order_sn": "SM202412011200001",
    "pay_way": 1,
    "from": 2
}
```

### 4. 查询支付状态
```
GET /api/shop_tier/payment_status?order_sn=SM202412011200001
```

### 5. 等级升级
```
POST /api/shop_tier/upgrade
{
    "target_tier_level": 2
}
```

### 6. 获取可升级等级
```
GET /api/shop_tier/upgradable
```

## 修改现有商家申请接口

修改 `app/api/controller/Shop.php` 中的 `apply` 方法，支持新的等级字段：

```php
public function apply()
{
    $post = $this->request->post();
    
    // 添加等级参数验证
    if (!isset($post['target_tier_level'])) {
        $post['target_tier_level'] = 0; // 默认0元入驻
    }
    
    $result = ShopApplyLogic::apply($post, $this->user_id);
    // ... 其他逻辑
}
```
