<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shop模块视频上传测试</title>
    <link rel="stylesheet" href="/static/lib/layui/css/layui.css">
    <link rel="stylesheet" href="/static/admin/css/app.css">
    <link rel="stylesheet" href="/static/admin/css/like.css">
    <style>
        body { padding: 20px; font-family: Arial, sans-serif; }
        .container { max-width: 800px; margin: 0 auto; }
        .video-container { 
            border: 2px dashed #ccc; 
            padding: 20px; 
            margin: 20px 0; 
            border-radius: 8px;
            background: #f9f9f9;
            min-height: 200px;
        }
        .like-upload-video { 
            text-align: center; 
            padding: 20px;
            background: #fff;
            border-radius: 4px;
        }
        .add-upload-video { 
            display: inline-block;
            padding: 10px 20px;
            background: #1890ff;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .add-upload-video:hover { background: #40a9ff; }
        
        .upload-video-div { 
            background: #fff;
            padding: 20px;
            border-radius: 4px;
            margin-top: 10px;
            position: relative;
        }
        .del-upload-btn {
            position: absolute;
            top: 5px;
            right: 5px;
            background: #ff4d4f;
            color: white;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            text-align: center;
            line-height: 20px;
            cursor: pointer;
            font-size: 12px;
        }
        .del-upload-btn:hover { background: #ff7875; }
        
        .test-log { 
            background: #f0f0f0; 
            padding: 15px; 
            margin: 20px 0; 
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .success { color: #52c41a; }
        .error { color: #ff4d4f; }
        .info { color: #1890ff; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎥 Shop模块视频上传测试</h1>
        
        <div class="video-container" id="videoContainer">
            <div class="like-upload-video">
                <div class="upload-image-elem">
                    <a class="add-upload-video" id="video">+ 添加视频</a>
                </div>
            </div>
        </div>
        
        <div style="text-align: center; margin: 20px 0;">
            <button class="layui-btn layui-btn-normal" onclick="testVideoUpload()">🧪 测试视频上传功能</button>
            <button class="layui-btn" onclick="checkState()" style="background: #52c41a; color: white;">🔍 检查状态</button>
            <button class="layui-btn" onclick="clearLog()" style="background: #666; color: white;">🧹 清空日志</button>
        </div>
        
        <div class="test-log" id="testLog">等待测试开始...</div>
    </div>

    <script src="/static/lib/layui/layui.js"></script>
    <script src="/static/admin/js/app.js"></script>
    <script src="/static/admin/js/jquery.min.js"></script>
    <script src="/static/admin/js/function.js"></script>
    <script>
        // 配置layui并加载like模块
        layui.config({
            base: '/static/plug/layui-admin/dist/layuiadmin/'
        }).extend({
            like: 'modules/like'
        });
        
        // 日志函数
        function log(message, type) {
            type = type || 'info';
            var timestamp = new Date().toLocaleTimeString();
            var logElement = document.getElementById('testLog');
            var className = type === 'success' ? 'success' : (type === 'error' ? 'error' : 'info');
            logElement.innerHTML += '<span class="' + className + '">[' + timestamp + '] ' + message + '</span>\n';
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        // 检查当前状态
        function checkState() {
            log('=== 当前状态检查 ===', 'info');
            
            var container = document.getElementById('videoContainer');
            var uploadDiv = container.querySelector('.like-upload-video');
            var videoDiv = container.querySelector('.upload-video-div');
            var addButton = container.querySelector('.add-upload-video');
            
            log('容器子元素数量: ' + container.children.length, 'info');
            log('上传区域存在: ' + (uploadDiv ? '是' : '否'), uploadDiv ? 'success' : 'error');
            log('视频显示区域存在: ' + (videoDiv ? '是' : '否'), videoDiv ? 'info' : 'success');
            log('添加按钮存在: ' + (addButton ? '是' : '否'), addButton ? 'success' : 'error');
            
            if (addButton) {
                log('添加按钮可见: ' + (addButton.offsetParent !== null ? '是' : '否'), 
                    addButton.offsetParent !== null ? 'success' : 'error');
            }
            
            log('=== 状态检查完成 ===', 'info');
        }
        
        // 清空日志
        function clearLog() {
            document.getElementById('testLog').innerHTML = '日志已清空\n';
        }
        
        // 测试视频上传功能
        function testVideoUpload() {
            log('开始测试Shop模块视频上传功能', 'info');
            
            // 检查like对象是否存在
            if (typeof like === 'undefined') {
                log('❌ like对象不存在，无法测试', 'error');
                return;
            }
            
            // 检查videoUpload方法是否存在
            if (typeof like.videoUpload !== 'function') {
                log('❌ like.videoUpload方法不存在，无法测试', 'error');
                return;
            }
            
            // 检查delUpload方法是否存在
            if (typeof like.delUpload !== 'function') {
                log('❌ like.delUpload方法不存在，无法测试', 'error');
                return;
            }
            
            log('✅ like对象和相关方法都存在', 'success');
            
            // 初始化删除功能
            try {
                like.delUpload();
                log('✅ 删除功能初始化成功', 'success');
            } catch (e) {
                log('❌ 删除功能初始化失败: ' + e.message, 'error');
            }
            
            // 测试视频上传调用
            try {
                var $videoButton = $('#video');
                log('✅ 找到视频按钮: ' + $videoButton.length + '个', 'success');
                
                // 模拟点击事件绑定
                $videoButton.off('click').on('click', function() {
                    log('🎯 视频按钮被点击', 'info');
                    like.videoUpload({
                        limit: 1,
                        field: "video",
                        that: $(this),
                        content: '/shop/file/videoList'
                    });
                });
                
                log('✅ 视频上传事件绑定成功', 'success');
                log('💡 现在可以点击"+ 添加视频"按钮测试上传功能', 'info');
                
            } catch (e) {
                log('❌ 视频上传测试失败: ' + e.message, 'error');
            }
        }
        
        // 初始化layui
        layui.use(['layer', 'upload', 'like'], function() {
            var layer = layui.layer;
            var upload = layui.upload;
            var like = layui.like;
            
            // 将模块暴露到全局
            window.layer = layer;
            window.upload = upload;
            window.like = like;
            
            log('✅ Layui模块加载完成', 'success');
            
            // 自动初始化
            setTimeout(function() {
                testVideoUpload();
                checkState();
            }, 500);
            
        }, function() {
            log('❌ Layui模块加载失败', 'error');
        });
        
        // 监听DOM变化
        var observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList') {
                    log('🔍 检测到DOM变化: ' + mutation.addedNodes.length + '个节点添加, ' + 
                        mutation.removedNodes.length + '个节点删除', 'info');
                }
            });
        });
        
        observer.observe(document.getElementById('videoContainer'), {
            childList: true,
            subtree: true
        });
        
        log('🔧 页面初始化完成', 'success');
    </script>
</body>
</html>
