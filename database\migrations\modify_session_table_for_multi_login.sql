-- 修改session表结构以支持多方登录
-- 移除唯一索引，允许同一用户在同一客户端有多个有效token

-- 删除ls_session表的唯一索引
ALTER TABLE `ls_session` DROP INDEX `user_id_client`;

-- 为了性能考虑，添加普通索引
ALTER TABLE `ls_session` ADD INDEX `idx_user_id` (`user_id`);
ALTER TABLE `ls_session` ADD INDEX `idx_token` (`token`);
ALTER TABLE `ls_session` ADD INDEX `idx_user_client` (`user_id`, `client`);

-- 同样修改shop_session表（如果需要商家端也支持多方登录）
ALTER TABLE `ls_shop_session` DROP INDEX `user_id_client`;
ALTER TABLE `ls_shop_session` ADD INDEX `idx_admin_id` (`admin_id`);
ALTER TABLE `ls_shop_session` ADD INDEX `idx_token` (`token`);
ALTER TABLE `ls_shop_session` ADD INDEX `idx_admin_client` (`admin_id`, `client`);
