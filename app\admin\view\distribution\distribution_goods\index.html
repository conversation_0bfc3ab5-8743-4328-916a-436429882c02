{layout name="layout1" /}
<style>
    .btns {
        margin-top: 15px;
    }
    .layui-table-cell {
        height: auto;
    }
    .layui-form-label {
        width: 120px;
    }
</style>
<div class="wrapper">
    <div class="layui-card">
        <!--操作提示-->
        <div class="layui-card-body">
            <div class="layui-collapse like-layui-collapse" lay-accordion="" style="border:1px dashed #c4c4c4">
                <div class="layui-colla-item">
                    <h2 class="layui-colla-title like-layui-colla-title" style="background-color: #fff">操作提示</h2>
                    <div class="layui-colla-content layui-show">
                        <p>*查看商品分销佣金比例；</p>
                    </div>
                </div>
            </div>
        </div>
            <!--搜索区域-->
        <div class="layui-card-body layui-form">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <div class="layui-form-label">商品信息：</div>
                    <div class="layui-input-inline">
                        <input type="text" id="keyword" name="keyword" class="layui-input" />
                    </div>
                </div>
                <div class="layui-inline">
                    <div class="layui-form-label">平台商品分类：</div>
                    <div class="layui-input-inline">
                        <select name="platform_cate_id" id="platform_cate_id"  placeholder="请选择" >
                            <option value="all">全部</option>
                            {foreach $cate_list as $val }
                            <option value="{$val.id}">{$val.html}{$val.name}</option>
                            {/foreach}
                        </select>
                    </div>
                </div>
<!--                <div class="layui-inline">-->
<!--                    <div class="layui-form-label">分销状态：</div>-->
<!--                    <div class="layui-input-inline">-->
<!--                        <select name="is_distribution" id="is_distribution"  placeholder="请选择" >-->
<!--                            <option value="all">全部</option>-->
<!--                            <option value="0">不参与</option>-->
<!--                            <option value="1">参与</option>-->
<!--                        </select>-->
<!--                    </div>-->
<!--                </div>-->
                <div class="layui-inline">
                    <button class="layui-btn layui-btn-primary layui-bg-blue" lay-submit lay-filter="search">搜索</button>
                    <button class="layui-btn layui-btn-primary" lay-submit lay-filter="reset">重置</button>
                </div>
            </div>
        </div>
        <!--主体区域-->
        <div class="layui-card-body">
            <!--数据表格-->
            <table id="lists" lay-filter="lists"></table>
            <!--工具条模板-->
            <script type="text/html" id="operate">
                <a class="layui-btn layui-btn-sm layui-bg-blue" lay-event="detail">查看佣金设置</a>
            </script>
            <!--自定义模板-->
            <script type="text/html" id="shop-info">
                <img src="{{d.Shop.logo}}" style="height:60px;width: 60px" class="image-show">
                <div class="layui-input-inline"  style="text-align: left;">
                    <p>商家编号:{{d.Shop.id}}</p>
                    <p>商家名称:{{d.Shop.name}}</p>
                </div>
            </script>
            <script type="text/html" id="goods-info">
                <img src="{{d.image}}" style="height:60px;width: 60px" class="image-show">
                <div class="layui-input-inline"  style="text-align: left;">
                    <p>商品编号:{{d.code}}</p>
                    <p>商品名称:{{d.name}}</p>
                </div>
            </script>
            <script type="text/html" id="goods-price">
                ¥ {{d.min_price}} - ¥ {{d.max_price}}
            </script>
            <script type="text/html" id="goods-distribution">
                {{#  if(d.distribution_flag){ }}
                参与
                {{#  } else { }}
                不参与
                {{#  } }}
            </script>
        </div>
    </div>
</div>


<script>

    layui.config({
        version:"{$front_version}",
        base: '/static/lib/'
    }).use(['table', 'form'], function () {
        let $ = layui.$
            , form = layui.form
            , table = layui.table;

        //监听搜索
        form.on('submit(search)', function(data){
            var field = data.field;
            //执行重载
            table.reload('lists', {
                where: field,
                page: {curr: 1}
            });
        });

        //清空查询
        form.on('submit(reset)', function(){
            $('#keyword').val('');
            $('#platform_cate_id').val('all');
            $('#shop_cate_id').val('all');
            $('#is_distribution').val('all');
            form.render('select');
            //刷新列表
            table.reload('lists', {
                where: [], page: {curr: 1}
            });
        });

        // 数据表格渲染
        table.render({
            elem: '#lists'
            ,url: '{:url("distribution.distribution_goods/index")}' //数据接口
            ,method: 'post'
            ,page: true //开启分页
            ,cols: [[ //表头
                {templet: '#shop-info', title: '商家信息'}
                ,{templet: '#goods-info', title: '商品信息'}
                ,{templet: '#goods-price', title: '价格', width:200}
                // ,{templet: '#goods-distribution',align:'center', title: '分销状态', width: 150}
                ,{title: '操作', align:'center', toolbar: '#operate',  width: 150}
            ]]
            , text: {none: '暂无数据！'}
            , parseData: function (res) { //将原始数据解析成 table 组件所规定的数据
                return {
                    "code": res.code,
                    "msg": res.msg,
                    "count": res.data.count, //解析数据长度
                    "data": res.data.lists, //解析数据列表
                };
            },
            response: {
                statusCode: 1
            }
            ,done: function(res, curr, count){
                // 解决操作栏因为内容过多换行问题
                $(".layui-table-main tr").each(function (index, val) {
                    $($(".layui-table-fixed-l .layui-table-body tbody tr")[index]).height($(val).height());
                    $($(".layui-table-fixed-r .layui-table-body tbody tr")[index]).height($(val).height());
                });
            }
        });

        // 工具条事件
        table.on('tool(lists)', function(obj){
            var layEvent = obj.event; //获得 lay-event 对应的值（也可以是表头的 event 参数对应的值）

            if(layEvent === 'detail'){ // 设置佣金
                id = obj.data.id;
                // 弹窗显示添加页
                layer.open({
                    type: 2
                    ,title: "查看佣金比例"
                    ,content: "{:url('distribution.distribution_goods/detail')}?id=" + id
                    ,area: ["90%", "90%"]
                    ,btn: ["返回"]
                });
            }
        });

    });


</script>