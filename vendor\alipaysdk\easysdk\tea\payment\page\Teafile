{"scope": "alipay", "name": "easysdk-payment-page", "version": "0.0.1", "main": "./main.tea", "java": {"package": "com.alipay.easysdk.payment.page", "baseClient": "com.alipay.easysdk.kernel.BaseClient"}, "csharp": {"namespace": "Alipay.EasySDK.Payment.Page", "baseClient": "Alipay.EasySDK.Kernel:BaseClient"}, "typescript": {"baseClient": "@alipay/easysdk-baseclient"}, "php": {"package": "Alipay.EasySDK.Payment.Page"}, "go": {"namespace": "payment/page"}, "libraries": {"EasySDKKernel": "alipay:easysdk-kernel:*"}}