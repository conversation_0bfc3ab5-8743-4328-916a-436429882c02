<!DOCTYPE html>
<html>
<head>
    <title>SSE 客户端示例</title>
    <meta charset="utf-8">
    <style>
        .message {
            padding: 10px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        #messageContainer {
            max-height: 400px;
            overflow-y: auto;
            margin: 20px 0;
        }
        .status {
            color: #666;
            font-style: italic;
        }
    </style>
</head>
<body>
    <h1>SSE 客户端示例</h1>
    <div class="status" id="status">等待连接...</div>
    <div id="messageContainer"></div>

    <script>
        const messageContainer = document.getElementById('messageContainer');
        const statusElement = document.getElementById('status');
        
        // 创建 EventSource 实例
        const evtSource = new EventSource('sse_example.php');
        
        // 连接打开时
        evtSource.onopen = function() {
            statusElement.textContent = '已连接到服务器';
        };
        
        // 监听消息
        evtSource.addEventListener('message', function(e) {
            const data = JSON.parse(e.data);
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message';
            messageDiv.innerHTML = `
                <strong>时间:</strong> ${data.time}<br>
                <strong>计数:</strong> ${data.count}<br>
                <strong>消息:</strong> ${data.message}
            `;
            messageContainer.appendChild(messageDiv);
            // 滚动到最新消息
            messageContainer.scrollTop = messageContainer.scrollHeight;
        });
        
        // 错误处理
        evtSource.onerror = function(e) {
            statusElement.textContent = '连接错误，正在尝试重连...';
        };

        // 在页面关闭时关闭连接
        window.addEventListener('beforeunload', function() {
            evtSource.close();
        });
    </script>
</body>
</html>
