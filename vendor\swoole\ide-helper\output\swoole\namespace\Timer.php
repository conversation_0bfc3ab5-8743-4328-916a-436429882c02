<?php

namespace Swoole;

class Timer
{

    /**
     * @return mixed
     */
    public static function set(array $settings)
    {
    }

    /**
     * @return mixed
     */
    public static function tick($ms, callable $callback, ... $params)
    {
    }

    /**
     * @return mixed
     */
    public static function after($ms, callable $callback, ... $params)
    {
    }

    /**
     * @return mixed
     */
    public static function exists($timer_id)
    {
    }

    /**
     * @return mixed
     */
    public static function info($timer_id)
    {
    }

    /**
     * @return mixed
     */
    public static function stats()
    {
    }

    /**
     * @return mixed
     */
    public static function list()
    {
    }

    /**
     * @return mixed
     */
    public static function clear($timer_id)
    {
    }

    /**
     * @return mixed
     */
    public static function clearAll()
    {
    }


}
