# 商家 WebSocket 通知修复指南

## 问题描述
商家模块前台无法接收到来自 PayNotifyLogic.php 发送的订单支付通知。

## 问题原因
1. **UID 绑定缺失**：商家客户端连接 WebSocket 后没有正确绑定商家 ID，导致系统无法向特定商家发送通知。
2. **消息类型不匹配**：原有的 `shop_panel_online` 消息类型只是加入了商家组，但没有绑定具体的商家 UID。
3. **前端监听缺失**：商家前端可能没有正确监听和处理 `notification` 类型的消息。

## 解决方案

### 1. 后端修改

#### Events.php 更新
在 `app/common/websocket/Events.php` 中添加了新的消息类型 `shop_online`，用于商家绑定：

```php
case 'shop_online':
    // 商家上线，加入商家组并绑定UID
    Gateway::joinGroup($client_id, 'shop_group');
    if (!empty($data['shop_id'])) {
        Gateway::bindUid($client_id, 'shop_' . $data['shop_id']);
        Log::info("Shop [{$client_id}] bound to shop_" . $data['shop_id']);
        
        // 发送绑定成功消息
        Gateway::sendToClient($client_id, json_encode([
            'type' => 'bind_success',
            'message' => '商家绑定成功',
            'shop_id' => $data['shop_id'],
            'timestamp' => time()
        ]));
    }
    break;
```

#### NotificationService.php 优化
增强了日志记录，方便调试：
- 检查商家在线状态
- 如果商家不在线，尝试发送到商家组作为备用方案
- 详细的错误日志记录

### 2. 前端集成

#### 使用提供的 JavaScript 模块
在商家前端页面引入 `shop-websocket-notification.js`：

```html
<script src="/static/js/shop-websocket-notification.js"></script>
```

#### 初始化 WebSocket 连接

```javascript
// 获取当前商家 ID（从页面或会话中获取）
const shopId = <?php echo $shop_id; ?>; // 或从其他地方获取

// 创建 WebSocket 通知实例
const shopNotification = new ShopWebSocketNotification({
    shopId: shopId,
    debug: true, // 开发时开启调试
    onNotification: function(notification) {
        // 自定义通知处理
        console.log('收到新通知:', notification);
        
        // 显示通知
        if (notification.notification_type === 'order_paid') {
            // 处理订单支付通知
            showOrderNotification(notification);
            // 可以更新页面上的订单列表或显示提示
        }
    },
    onStatusChange: function(status) {
        // 连接状态变化回调
        console.log('WebSocket 状态:', status);
        updateConnectionStatus(status);
    }
});

// 请求浏览器通知权限
ShopWebSocketNotification.requestNotificationPermission();

// 初始化连接
shopNotification.init();

// 页面卸载时断开连接
window.addEventListener('beforeunload', function() {
    shopNotification.disconnect();
});
```

#### 处理通知示例

```javascript
function showOrderNotification(notification) {
    // 在页面上显示通知
    const notificationElement = document.createElement('div');
    notificationElement.className = 'notification-item';
    notificationElement.innerHTML = `
        <h4>${notification.title}</h4>
        <p>${notification.content}</p>
        <a href="${notification.url}" class="btn btn-primary">查看订单</a>
    `;
    
    document.getElementById('notification-container').appendChild(notificationElement);
    
    // 更新未读消息数量
    updateUnreadCount();
    
    // 刷新订单列表（如果需要）
    if (typeof refreshOrderList === 'function') {
        refreshOrderList();
    }
}
```

### 3. 测试工具

使用提供的测试页面 `/test_shop_websocket.html` 来测试 WebSocket 连接和通知接收：

1. 访问测试页面：`http://your-domain.com/test_shop_websocket.html?shop_id=YOUR_SHOP_ID`
2. 输入商家 ID 并点击连接
3. 观察连接状态和消息日志
4. 触发一个订单支付来测试通知接收

### 4. 调试步骤

1. **检查 WebSocket 服务是否运行**
   ```bash
   ps aux | grep GatewayWorker
   ```

2. **查看系统日志**
   ```sql
   SELECT * FROM ls_log WHERE type LIKE '%notification%' ORDER BY id DESC LIMIT 20;
   ```

3. **检查商家在线状态**
   在 GatewayWorker 中添加调试代码检查商家是否正确绑定。

4. **浏览器控制台调试**
   - 检查 WebSocket 连接状态
   - 查看是否有错误信息
   - 确认消息是否正确接收

### 5. 注意事项

1. **商家 ID 获取**：确保前端能正确获取当前登录商家的 ID。
2. **跨域问题**：WebSocket 连接可能存在跨域问题，确保服务器配置正确。
3. **SSL 证书**：使用 WSS 协议时，确保 SSL 证书有效。
4. **防火墙**：确保服务器防火墙允许 WebSocket 端口（9282）。
5. **心跳保持**：模块已包含心跳机制，确保长连接稳定。

### 6. 常见问题

**Q: 连接成功但收不到通知？**
A: 检查商家 ID 是否正确绑定，查看服务器日志确认通知是否发送。

**Q: 连接经常断开？**
A: 可能是网络不稳定或服务器配置问题，检查心跳设置和服务器日志。

**Q: 如何处理离线消息？**
A: 当前实现是实时通知，离线消息需要从数据库中查询未读通知。

### 7. 扩展功能

1. **消息已读标记**：调用 API 标记消息已读
2. **历史消息查询**：从数据库加载历史通知
3. **通知分类**：根据 `notification_type` 分类处理不同类型的通知
4. **声音提醒**：默认已包含，可自定义声音文件

## 总结

通过以上修改，商家前端现在可以：
1. 正确连接到 WebSocket 服务器
2. 绑定商家 ID 以接收定向通知
3. 实时接收订单支付等各类通知
4. 提供稳定的重连机制和错误处理

这个解决方案提供了一个完整的商家通知系统，可以轻松集成到现有的商家前端页面中。
