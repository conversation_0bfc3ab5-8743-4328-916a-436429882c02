/**
 * 表单交互增强版
 * 解决layui弹窗遮挡、表格固定列遮挡等交互问题
 * <AUTHOR> Assistant
 * @version 2.0
 */

(function() {
    'use strict';
    
    // 配置选项
    var config = {
        // 层级配置
        zIndex: {
            modal: 19891014,
            modalShade: 19891013,
            fixedTable: 999,
            dropdown: 9999,
            tooltip: 10000
        },
        // 重试配置
        retry: {
            maxAttempts: 3,
            delay: 200
        },
        // 调试模式
        debug: false
    };

    // 工具函数
    var utils = {
        log: function(message, data) {
            if (config.debug) {
                console.log('[FormInteraction] ' + message, data || '');
            }
        },
        
        // 防抖函数
        debounce: function(func, wait) {
            var timeout;
            return function executedFunction() {
                var context = this;
                var args = arguments;
                var later = function() {
                    timeout = null;
                    func.apply(context, args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        },
        
        // 检查元素是否可见且可点击
        isClickable: function(element) {
            if (!element || !element.length) return false;
            
            var $el = $(element);
            var offset = $el.offset();
            
            if (!offset) return false;
            
            // 检查元素是否在视口中
            var windowHeight = $(window).height();
            var windowWidth = $(window).width();
            var scrollTop = $(window).scrollTop();
            var scrollLeft = $(window).scrollLeft();
            
            return (
                offset.top >= scrollTop &&
                offset.left >= scrollLeft &&
                offset.top <= scrollTop + windowHeight &&
                offset.left <= scrollLeft + windowWidth &&
                $el.is(':visible') &&
                !$el.is(':disabled')
            );
        },
        
        // 安全点击元素
        safeClick: function(selector, callback) {
            var attempts = 0;
            var maxAttempts = config.retry.maxAttempts;
            
            function tryClick() {
                attempts++;
                var $element = $(selector);
                
                if (utils.isClickable($element)) {
                    $element.click();
                    if (callback) callback(true);
                    utils.log('安全点击成功', selector);
                    return;
                }
                
                if (attempts < maxAttempts) {
                    setTimeout(tryClick, config.retry.delay);
                } else {
                    utils.log('安全点击失败，已达到最大重试次数', selector);
                    if (callback) callback(false);
                }
            }
            
            tryClick();
        }
    };

    // 层级管理器
    var ZIndexManager = {
        // 初始化
        init: function() {
            this.fixLayuiZIndex();
            this.fixTableZIndex();
            this.bindEvents();
            utils.log('层级管理器初始化完成');
        },

        // 修复layui层级问题
        fixLayuiZIndex: function() {
            // 修复layui弹窗层级
            if (typeof layui !== 'undefined') {
                layui.use(['layer'], function() {
                    var layer = layui.layer;
                    
                    // 设置默认层级
                    layer.config({
                        zIndex: config.zIndex.modal
                    });
                });
            }
            
            // 添加CSS规则
            var style = document.createElement('style');
            style.textContent = `
                .layui-layer { z-index: ${config.zIndex.modal} !important; }
                .layui-layer-shade { z-index: ${config.zIndex.modalShade} !important; }
                .layui-table-fixed { z-index: ${config.zIndex.fixedTable} !important; }
                .layui-table-fixed .layui-table-body { z-index: ${config.zIndex.fixedTable} !important; }
                .layui-nav .layui-nav-child { z-index: ${config.zIndex.dropdown} !important; }
                .layui-form-select .layui-anim { z-index: ${config.zIndex.dropdown} !important; }
            `;
            document.head.appendChild(style);
        },

        // 修复表格层级问题
        fixTableZIndex: function() {
            $(document).on('DOMNodeInserted', '.layui-table-fixed', function() {
                $(this).css('z-index', config.zIndex.fixedTable);
            });
        },

        // 绑定事件
        bindEvents: function() {
            var self = this;
            
            // 监听弹窗打开
            $(document).on('layui-layer-open', function() {
                self.adjustModalZIndex();
            });
            
            // 监听表格渲染
            $(document).on('layui-table-rendered', function() {
                self.fixTableZIndex();
            });
        },

        // 调整弹窗层级
        adjustModalZIndex: function() {
            $('.layui-layer').each(function() {
                var $layer = $(this);
                var currentZIndex = parseInt($layer.css('z-index')) || 0;
                
                if (currentZIndex < config.zIndex.modal) {
                    $layer.css('z-index', config.zIndex.modal);
                }
            });
        }
    };

    // 点击增强器
    var ClickEnhancer = {
        // 初始化
        init: function() {
            this.bindEvents();
            this.enhanceButtons();
            utils.log('点击增强器初始化完成');
        },

        // 绑定事件
        bindEvents: function() {
            var self = this;
            
            // 增强所有按钮点击
            $(document).on('click', '.layui-btn', function(e) {
                self.handleButtonClick($(this), e);
            });
            
            // 增强表格操作按钮
            $(document).on('click', '.layui-table-cell .layui-btn', function(e) {
                self.handleTableButtonClick($(this), e);
            });
        },

        // 增强现有按钮
        enhanceButtons: function() {
            $('.layui-btn').each(function() {
                var $btn = $(this);
                if (!$btn.data('enhanced')) {
                    $btn.data('enhanced', true);
                    $btn.css('position', 'relative');
                    $btn.css('z-index', '10');
                }
            });
        },

        // 处理按钮点击
        handleButtonClick: function($button, event) {
            // 确保按钮可见且可点击
            if (!utils.isClickable($button)) {
                event.preventDefault();
                event.stopPropagation();
                
                // 尝试滚动到按钮位置
                this.scrollToElement($button);
                
                // 延迟重试点击
                setTimeout(function() {
                    if (utils.isClickable($button)) {
                        $button[0].click();
                    }
                }, config.retry.delay);
                
                return false;
            }
            
            utils.log('按钮点击处理', $button.text());
        },

        // 处理表格按钮点击
        handleTableButtonClick: function($button, event) {
            var $table = $button.closest('.layui-table');
            var $fixed = $table.find('.layui-table-fixed');
            
            // 如果存在固定列，临时降低其层级
            if ($fixed.length > 0) {
                $fixed.css('z-index', '1');
                
                setTimeout(function() {
                    $fixed.css('z-index', config.zIndex.fixedTable);
                }, 1000);
            }
            
            this.handleButtonClick($button, event);
        },

        // 滚动到元素位置
        scrollToElement: function($element) {
            if (!$element.length) return;
            
            var offset = $element.offset();
            if (offset) {
                $('html, body').animate({
                    scrollTop: offset.top - 100
                }, 300);
            }
        }
    };

    // 表单增强器
    var FormEnhancer = {
        // 初始化
        init: function() {
            this.enhanceInputs();
            this.bindEvents();
            utils.log('表单增强器初始化完成');
        },

        // 增强输入框
        enhanceInputs: function() {
            $('input, textarea, select').each(function() {
                var $input = $(this);
                if (!$input.data('enhanced')) {
                    $input.data('enhanced', true);
                    
                    // 确保输入框可编辑
                    if (!$input.is('[readonly]') && !$input.is('[disabled]')) {
                        $input.css({
                            'pointer-events': 'auto',
                            'user-select': 'text',
                            'background-color': '#fff'
                        });
                    }
                }
            });
        },

        // 绑定事件
        bindEvents: function() {
            var self = this;
            
            // 监听输入框焦点
            $(document).on('focus', 'input, textarea', function() {
                self.handleInputFocus($(this));
            });
            
            // 监听表单提交
            $(document).on('submit', 'form', function() {
                self.handleFormSubmit($(this));
            });
        },

        // 处理输入框焦点
        handleInputFocus: function($input) {
            // 确保输入框在视口中可见
            var offset = $input.offset();
            if (offset) {
                var windowHeight = $(window).height();
                var scrollTop = $(window).scrollTop();
                
                if (offset.top < scrollTop || offset.top > scrollTop + windowHeight - 200) {
                    $('html, body').animate({
                        scrollTop: offset.top - 100
                    }, 300);
                }
            }
            
            utils.log('输入框获得焦点', $input.attr('name'));
        },

        // 处理表单提交
        handleFormSubmit: function($form) {
            // 移除可能的遮挡层
            $('.layui-layer-shade').css('z-index', '1');
            
            utils.log('表单提交处理', $form.attr('id'));
        }
    };

    // 主初始化函数
    function initFormInteractionEnhanced() {
        utils.log('开始初始化表单交互增强功能');
        
        $(document).ready(function() {
            // 初始化各个模块
            ZIndexManager.init();
            ClickEnhancer.init();
            FormEnhancer.init();
            
            // 定期检查和修复
            setInterval(function() {
                ZIndexManager.fixLayuiZIndex();
                ClickEnhancer.enhanceButtons();
                FormEnhancer.enhanceInputs();
            }, 5000);
            
            utils.log('表单交互增强功能初始化完成');
        });
    }

    // 暴露到全局
    window.FormInteractionEnhanced = {
        init: initFormInteractionEnhanced,
        ZIndexManager: ZIndexManager,
        ClickEnhancer: ClickEnhancer,
        FormEnhancer: FormEnhancer,
        utils: utils,
        config: config
    };

    // 自动初始化
    initFormInteractionEnhanced();

})();
