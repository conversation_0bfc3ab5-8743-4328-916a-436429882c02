{layout name="layout1" /}

<div class="wrapper">
    <div class="layui-card">
        <!--操作提示-->
        <div class="layui-card-body">
            <div class="layui-collapse like-layui-collapse" lay-accordion="" style="border:1px dashed #c4c4c4">
                <div class="layui-colla-item">
                    <h2 class="layui-colla-title like-layui-colla-title" style="background-color: #fff">操作提示</h2>
                    <div class="layui-colla-content layui-show">
                        <p>*小票打印设置，全自动接单打印机，可自定义模板。</p>
                        <p>*小票打印机是更加方便打印订单，在订单中未处理的订单、配送中的订单、已完成的订单和订单查询中都可使用打印小票功能来打印出来。</p>
                        <p>*小票打印需提前添加配置好打印机类型方可使用。</p>
                    </div>
                </div>
            </div>
        </div>
        <!--列表-->
        <div class="layui-tab layui-tab-card" lay-filter="tab-all">
            <div class="layui-card-body">
                <div style="padding-bottom: 10px;" class="add">
                    <button class="layui-btn layui-btn-sm layEvent {$view_theme_color}" lay-event="add">
                        新增打印机
                    </button>
                </div>
                <table id="like-table-lists" lay-filter="like-table-lists"></table>
                <script type="text/html" id="printer-operation">
                    <a class="layui-btn layui-btn-normal layui-btn-sm" lay-event="edit">编辑</a>
                    <a class="layui-btn layui-btn-primary layui-btn-sm" lay-event="test_print">测试打印</a>
                    <a class="layui-btn layui-btn-danger layui-btn-sm" lay-event="del">删除</a>
                </script>
            </div>
        </div>
    </div>
</div>

<script>
    layui.use(["table", "form"], function () {
        var $ = layui.$
            , form = layui.form
            , table = layui.table;

        // 列表
        like.tableLists("#like-table-lists", "{:url()}", [
            {type: 'checkbox', width: "5%"}
            , {field: 'type_desc', title: '设备类型', width: 100,}
            , {field: 'name', width: "15%", title: '打印机名称', align: 'center'}
            , {field: 'machine_code', width: "15%", align: 'center', title: '终端号'}
            , {field: 'print_number', width: 160, title: '打印联数'}
            , {field: 'auto_print_desc', width: 160, title: '自动打印'}
            , {field: 'status_desc', width: 160, title: '状态'}
            , {fixed: 'right', title: '操作', align: 'center', width: "20%", toolbar: '#printer-operation'}
        ]);


        //事件
        var active = {
            add: function () {
                layer.open({
                    type: 2,
                    title: '添加打印机',
                    content: '{:url("printer.printer/add")}',
                    area: ['90%', '90%'],
                    btn: ['保存', '取消'],
                    maxmin: true,
                    yes: function (index, layero) {
                        var iframeWindow = window["layui-layer-iframe" + index];
                        var submit = layero.find("iframe").contents().find("#addSubmit");
                        iframeWindow.layui.form.on("submit(addSubmit)", function (data) {
                            var field = data.field;
                            like.ajax({
                                url: '{:url("printer.printer/add")}',
                                data: field,
                                type: "post",
                                success: function (res) {
                                    if (res.code === 1) {
                                        layui.layer.msg(res.msg, {
                                            offset: '15px'
                                            , icon: 1
                                            , time: 1000
                                        });
                                        layer.close(index);
                                        table.reload('like-table-lists');
                                    }
                                }
                            });
                        });
                        submit.trigger('click');
                    }
                });
            },
            edit: function (obj) {
                layer.open({
                    type: 2,
                    title: '编辑打印机',
                    content: '{:url("printer.printer/edit")}?id=' + obj.data.id,
                    area: ['90%', '90%'],
                    btn: ['确定', '取消'],
                    yes: function (index, layero) {
                        var iframeWindow = window["layui-layer-iframe" + index];
                        var submit = layero.find("iframe").contents().find("#addSubmit");
                        iframeWindow.layui.form.on('submit(addSubmit)', function (data) {
                            var field = data.field;
                            like.ajax({
                                url: '{:url("printer.printer/edit")}',
                                data: field,
                                type: "POST",
                                success: function (res) {
                                    if (res.code === 1) {
                                        layui.layer.msg(res.msg, {
                                            offset: '15px',
                                            icon: 1,
                                            time: 1000
                                        });
                                        layer.close(index);
                                        table.reload('like-table-lists');
                                    }
                                }
                            });
                        });
                        submit.trigger('click');
                    }
                })
            },
            test_print: function (obj) {
                layer.confirm('确定要测试：' + '<span style="color: red">' + obj.data.name + '</span>' + '的打印吗?', function (index) {
                    like.ajax({
                        url: '{:url("printer.printer/testPrint")}'
                        , data: {id: obj.data.id, type: obj.data.type}
                        , type: 'post'
                        , success: function (res) {
                            if (res.code === 1) {
                                layer.msg(res.msg, {
                                    offset: '15px'
                                    , icon: 1
                                    , time: 1000
                                });
                            }
                        }
                    });
                })
            },
            del: function (obj) {
                layer.confirm('确定删除打印机：' + '<span style="color: red">' + obj.data.name + '</span>', function (index) {
                    like.ajax({
                        url: '{:url("printer.printer/del")}',
                        data: {id: obj.data.id},
                        type: "post",
                        success: function (res) {
                            if (res.code === 1) {
                                layui.layer.msg(res.msg, {
                                    offset: '15px',
                                    icon: 1,
                                    time: 1000
                                });
                                layer.close(index);
                                table.reload('like-table-lists');
                            }
                        }
                    });
                    layer.close(index);
                })
            }
        }

        like.eventClick(active);

    });
</script>