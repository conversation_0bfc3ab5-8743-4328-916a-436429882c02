<?php
// app/common/enum/JcaiStatusEnum.php
namespace app\common\enum;


class JcaiStatusEnum {
        const PENDING_AUDIT = -1; // 自定义一个明确的待审核状态
        const NOT_STARTED = 0;
        const ONGOING = 1;
        const SUCCESSFUL = 2;
        const FAILED = 3;
        const ENDED = 4; // 时间到期自然结束
        const CANCELED = 5;
        const AUDIT_REJECTED = 6; // 审核拒绝的状态

        public static function getDesc($value){
            $map = [
                self::PENDING_AUDIT => '待审核',
                self::NOT_STARTED => '未开始',
                self::ONGOING => '进行中',
                self::SUCCESSFUL => '已成功',
                self::FAILED => '已失败',
                self::ENDED => '已结束',
                self::CANCELED => '已取消',
                self::AUDIT_REJECTED => '审核拒绝',
            ];
            return $map[$value] ?? '未知状态';
        }
        // 可以添加 getColor 等方法
    }