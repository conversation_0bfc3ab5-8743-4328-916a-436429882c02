/**
 * 布局修复脚本 - 处理没有二级菜单时的布局调整
 */

(function() {
    'use strict';
    
    // 初始化布局状态
    function initializeLayout() {
        // 检查当前是否有显示的二级菜单
        var hasVisibleSubmenu = false;
        var allSubmenus = document.querySelectorAll('.layui-sidebar > .layui-side-menu li > dl');
        
        for(var i = 0; i < allSubmenus.length; i++) {
            var style = window.getComputedStyle(allSubmenus[i]);
            if(style.display === 'block') {
                hasVisibleSubmenu = true;
                break;
            }
        }
        
        console.log('[Layout Fix] Has visible submenu:', hasVisibleSubmenu);
        
        // 设置相应的CSS类
        if (hasVisibleSubmenu) {
            document.body.classList.remove('no-submenu');
            document.body.classList.add('has-submenu');
            console.log('[Layout Fix] Applied has-submenu layout');
        } else {
            document.body.classList.remove('has-submenu');
            document.body.classList.add('no-submenu');
            console.log('[Layout Fix] Applied no-submenu layout');
        }
    }
    
    // 页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(initializeLayout, 100);
        });
    } else {
        setTimeout(initializeLayout, 100);
    }
    
    // 监听菜单点击事件，动态调整布局
    document.addEventListener('click', function(e) {
        var target = e.target;
        
        // 检查是否点击了菜单项
        if (target.closest('.layui-sidebar .layui-side-menu li > a')) {
            setTimeout(initializeLayout, 50);
        }
    });
    
    console.log('[Layout Fix] Script loaded');
})();
