{"version": 3, "file": "pages/account/login.js", "sources": ["webpack:///./components/count-down.vue?4f61", "webpack:///./utils/parseTime.js", "webpack:///./components/count-down.vue", "webpack:///./components/count-down.vue?a8c1", "webpack:///./components/count-down.vue?1b2a", "webpack:///./utils/type.js", "webpack:///./pages/account/login.vue?00c3", "webpack:///./pages/account/login.vue?612d", "webpack:///./pages/account/login.vue?ed0f", "webpack:///./pages/account/login.vue?5a52", "webpack:///./pages/account/login.vue", "webpack:///./pages/account/login.vue?75ec", "webpack:///./pages/account/login.vue?6579"], "sourcesContent": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return (_vm.time >= 0)?_c('div',[_c('client-only',[(_vm.isSlot)?_vm._t(\"default\"):_c('span',[_vm._v(_vm._s(_vm.formateTime))])],2)],1):_vm._e()}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "\n\nconst SECOND = 1000;\nconst MINUTE = 60 * SECOND;\nconst HOUR = 60 * MINUTE;\nconst DAY = 24 * HOUR;\nexport function parseTimeData(time) {\n    const days = Math.floor(time / DAY);\n    const hours = sliceTwo(Math.floor((time % DAY) / HOUR));\n    const minutes = sliceTwo(Math.floor((time % HOUR) / MINUTE));\n    const seconds = sliceTwo(Math.floor((time % MINUTE) / SECOND));\n    return {\n        days: days,\n        hours: hours,\n        minutes: minutes,\n        seconds: seconds,\n    };\n}\n\nfunction sliceTwo(str) {\n    return (0 + str.toString()).slice(-2)\n}\n\nexport  function parseFormat(format, timeData) {\n    let days = timeData.days;\n    let hours = timeData.hours, minutes = timeData.minutes, seconds = timeData.seconds\n    if (format.indexOf('dd') !== -1) {\n        format = format.replace('dd', days);\n    }\n    if (format.indexOf('hh') !== -1) {\n        format = format.replace('hh', sliceTwo(hours) );\n    }\n    if (format.indexOf('mm') !== -1) {\n        format = format.replace('mm', sliceTwo(minutes));\n    }\n    if (format.indexOf('ss') !== -1) {\n        format = format.replace('ss', sliceTwo(seconds));\n    }\n    return format\n}", "//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { parseTimeData, parseFormat } from '~/utils/parseTime'\nexport default {\n    components: {},\n    props: {\n        isSlot: {\n            type: Boolean,\n            default: false,\n        },\n        time: {\n            type: Number,\n            default: 0,\n        },\n        format: {\n            type: String,\n            default: 'hh:mm:ss',\n        },\n        autoStart: {\n            type: Boolean,\n            default: true,\n        },\n    },\n    watch: {\n        time: {\n            immediate: true,\n            handler(value) {\n                if (value) {\n                    this.reset()\n                }\n            },\n        },\n    },\n    data() {\n        return {\n            timeObj: {},\n            formateTime: 0,\n        }\n    },\n    created() {},\n    computed: {},\n    methods: {\n        createTimer(fn) {\n            return setTimeout(fn, 100)\n        },\n        isSameSecond(time1, time2) {\n            return Math.floor(time1) === Math.floor(time2)\n        },\n        start() {\n            if (this.counting) {\n                return\n            }\n            this.counting = true\n            this.endTime = Date.now() + this.remain * 1000\n            this.setTimer()\n        },\n        setTimer() {\n            this.tid = this.createTimer(() => {\n                let remain = this.getRemain()\n                if (!this.isSameSecond(remain, this.remain) || remain === 0) {\n                    this.setRemain(remain)\n                }\n                if (this.remain !== 0) {\n                    this.setTimer()\n                }\n            })\n        },\n        getRemain() {\n            return Math.max(this.endTime - Date.now(), 0)\n        },\n        pause() {\n            this.counting = false\n            clearTimeout(this.tid)\n        },\n        reset() {\n            this.pause()\n            this.remain = this.time\n            this.setRemain(this.remain)\n            if (this.autoStart) {\n                this.start()\n            }\n        },\n        setRemain(remain) {\n            const { format } = this\n            this.remain = remain\n            const timeData = parseTimeData(remain)\n            this.formateTime = parseFormat(format, timeData)\n            this.$emit('change', timeData)\n            if (remain === 0) {\n                this.pause()\n                this.$emit('finish')\n            }\n        },\n    },\n}\n", "import mod from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./count-down.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./count-down.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./count-down.vue?vue&type=template&id=2fbaab86&\"\nimport script from \"./count-down.vue?vue&type=script&lang=js&\"\nexport * from \"./count-down.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  \n}\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  null,\n  \"4090b4e2\"\n  \n)\n\nexport default component.exports", "export const client = 5\n\nexport const loginType = {\n    SMS: 0,\n    ACCOUNT: 1\n}\n\n\n// 短信发送\nexport const SMSType = {\n    // 注册\n    REGISTER: 'ZCYZ',\n    // 找回密码\n    FINDPWD: 'ZHMM',\n    // 登陆\n    LOGIN: 'YZMDL',\n    // 商家申请入驻\n    SJSQYZ: 'SJSQYZ',\n    // 更换手机号\n    CHANGE_MOBILE: 'BGSJHM',\n    // 绑定手机号\n    BIND: 'BDSJHM'\n}\n\nexport const FieldType = {\n    NONE: '',\n    SEX: 'sex',\n    NICKNAME: 'nickname',\n    AVATAR: 'avatar',\n    MOBILE: 'mobile'\n}\n\n\n// 售后状态\nexport const AfterSaleType = {\n    // 售后申请 \n    NORMAL: 'normal',\n    // 处理中\n    HANDLING: 'apply',\n    // 已处理\n    FINISH: 'finish'\n}\n", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../../node_modules/sass-resources-loader/lib/loader.js??ref--7-oneOf-1-4!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./login.vue?vue&type=style&index=0&id=a0d0f46a&lang=scss&scoped=true&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"0c894dd8\", content, true, context)\n};", "export * from \"-!../../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../../node_modules/sass-resources-loader/lib/loader.js??ref--7-oneOf-1-4!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./login.vue?vue&type=style&index=0&id=a0d0f46a&lang=scss&scoped=true&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".login[data-v-a0d0f46a]{flex:1;background-size:cover;background-repeat:no-repeat;background-position:50%;min-width:1180px}.login .login-container[data-v-a0d0f46a]{margin:0 auto;width:1180px;height:100%;position:relative}.login .login-container .login-banner[data-v-a0d0f46a]{display:flex;align-items:center;justify-content:center;width:750px;margin-right:30px;height:440px;overflow:hidden;-webkit-animation:loadimg-data-v-a0d0f46a 2s infinite;animation:loadimg-data-v-a0d0f46a 2s infinite;transition:background-color 2s}@-webkit-keyframes loadimg-data-v-a0d0f46a{0%{background-color:#e4e4e4}50%{background-color:#f0f0f0}to{background-color:#e4e4e4}}@keyframes loadimg-data-v-a0d0f46a{0%{background-color:#e4e4e4}50%{background-color:#f0f0f0}to{background-color:#e4e4e4}}.login .login-container .login-float-form-wrap[data-v-a0d0f46a]{width:400px;height:440px}.login .login-container .login-float-form-wrap .login-box[data-v-a0d0f46a]{background-color:#fff;height:100%;display:flex;flex-direction:column;justify-content:space-between}.login .login-container .login-float-form-wrap .login-box .login-header-box[data-v-a0d0f46a]{padding-top:20px}.login .login-container .login-float-form-wrap .login-box .login-header-box .header-tabs .header-tab[data-v-a0d0f46a]{width:160px;height:35px;display:flex;flex-direction:column;align-items:center;cursor:pointer}.login .login-container .login-float-form-wrap .login-box .login-header-box .header-tabs .active-tab[data-v-a0d0f46a]{color:#ff2c3c;text-align:center}.login .login-container .login-float-form-wrap .login-box .login-header-box .header-tabs .active-tab[data-v-a0d0f46a]:after{content:\\\"\\\";height:2px;width:72px;margin-top:8px;background-color:#ff2c3c}.login .login-container .login-float-form-wrap .login-box .login-header-box .login-form-box[data-v-a0d0f46a]{padding:0 30px}.login .login-container .login-float-form-wrap .login-box .login-header-box .login-form-box .login-form-item[data-v-a0d0f46a]{margin-top:24px}.login .login-container .login-float-form-wrap .login-box .login-header-box .login-form-box .login-form-item .input-phone-num[data-v-a0d0f46a]{width:340px}.login .login-container .login-float-form-wrap .login-box .login-header-box .login-form-box .login-form-item .verify-code-img[data-v-a0d0f46a]{width:100px;height:40px;margin-left:20px;background-color:red}.login .login-container .login-float-form-wrap .login-box .login-header-box .login-form-box .login-form-item .sms-btn[data-v-a0d0f46a]{margin-left:20px;height:40px}.login .login-container .login-float-form-wrap .login-box .login-header-box .option-box[data-v-a0d0f46a]{padding:0 30px;margin-top:60px}.login .login-container .login-float-form-wrap .login-box .login-header-box .option-box[data-v-a0d0f46a]  .el-checkbox{color:#888}.login .login-container .login-float-form-wrap .login-box .login-footer-box[data-v-a0d0f46a]{height:50px;padding:15px}.login .login-container .login-float-form-wrap .login-box .login-footer-box .login__other-item[data-v-a0d0f46a]{cursor:pointer}.login .login-container .login-float-form-wrap .login-box .login-footer-box .login__weixin-icon[data-v-a0d0f46a]{width:1.5em;height:1.5em;text-align:center;line-height:1.5em;border-radius:50%;background-color:#0abd5d;color:#fff}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"login flex col-center\"},[_vm._ssrNode(\"<div class=\\\"login-container flex col-stretch\\\" data-v-a0d0f46a>\",\"</div>\",[_vm._ssrNode(\"<div class=\\\"login-banner\\\" data-v-a0d0f46a><img\"+(_vm._ssrAttr(\"src\",_vm.config.pc_login_logo))+\" height=\\\"100%\\\" data-v-a0d0f46a></div> \"),_vm._ssrNode(\"<div class=\\\"login-float-form-wrap\\\" data-v-a0d0f46a>\",\"</div>\",[_vm._ssrNode(\"<div class=\\\"login-box\\\" data-v-a0d0f46a>\",\"</div>\",[_vm._ssrNode(\"<div class=\\\"login-header-box\\\" data-v-a0d0f46a>\",\"</div>\",[_vm._ssrNode(\"<div class=\\\"header-tabs flex row-center\\\" data-v-a0d0f46a><div\"+(_vm._ssrClass(\"header-tab xxl\",{ 'active-tab': _vm.loginStatus == 0 }))+\" data-v-a0d0f46a>\\n                                验证码登录\\n                            </div> <div\"+(_vm._ssrClass(\"header-tab xxl\",{ 'active-tab': _vm.loginStatus == 1 }))+\" data-v-a0d0f46a>\\n                                账号密码登录\\n                            </div></div> \"),_vm._ssrNode(\"<div\"+(_vm._ssrStyle(null,null, { display: (_vm.loginStatus == 0) ? '' : 'none' }))+\" data-v-a0d0f46a>\",\"</div>\",[_vm._ssrNode(\"<div class=\\\"login-form-box\\\" data-v-a0d0f46a>\",\"</div>\",[_vm._ssrNode(\"<div class=\\\"login-form-item\\\" data-v-a0d0f46a>\",\"</div>\",[_c('el-input',{staticClass:\"input-phone-num\",attrs:{\"placeholder\":\"请输入手机号码\"},model:{value:(_vm.telephone),callback:function ($$v) {_vm.telephone=$$v},expression:\"telephone\"}},[_c('el-select',{staticStyle:{\"width\":\"100px\"},attrs:{\"slot\":\"prepend\",\"placeholder\":\"请选择\"},slot:\"prepend\",model:{value:(_vm.selectNumberType),callback:function ($$v) {_vm.selectNumberType=$$v},expression:\"selectNumberType\"}},[_c('el-option',{attrs:{\"label\":\"中国+86\",\"value\":\"1\"}})],1)],1)],1),_vm._ssrNode(\" \"),_vm._ssrNode(\"<div class=\\\"login-form-item flex\\\" data-v-a0d0f46a>\",\"</div>\",[_c('el-input',{staticStyle:{\"width\":\"210px\"},attrs:{\"placeholder\":\"短信验证码\"},model:{value:(_vm.smsCode),callback:function ($$v) {_vm.smsCode=$$v},expression:\"smsCode\"}}),_vm._ssrNode(\" \"),_c('el-button',{staticClass:\"sms-btn\",on:{\"click\":_vm.sendSMSCode}},[(_vm.canSend)?_c('div',[_vm._v(\"获取验证码\")]):_c('count-down',{attrs:{\"time\":60,\"format\":\"ss秒\",\"autoStart\":true},on:{\"finish\":function($event){_vm.canSend = true}}})],1)],2)],2),_vm._ssrNode(\" \"),_vm._ssrNode(\"<div class=\\\"option-box flex-col\\\" data-v-a0d0f46a>\",\"</div>\",[_c('el-checkbox',{staticClass:\"muted\",model:{value:(_vm.isRemember),callback:function ($$v) {_vm.isRemember=$$v},expression:\"isRemember\"}},[_vm._v(\"记住账号\")]),_vm._ssrNode(\" \"),_vm._ssrNode(\"<div class=\\\"m-t-20 flex-col\\\" data-v-a0d0f46a>\",\"</div>\",[_c('el-button',{attrs:{\"type\":\"primary\"},on:{\"click\":_vm.smsLogin}},[_vm._v(\"立即登录\")])],1)],2)],2),_vm._ssrNode(\" \"),_vm._ssrNode(\"<div\"+(_vm._ssrStyle(null,null, { display: (_vm.loginStatus == 1) ? '' : 'none' }))+\" data-v-a0d0f46a>\",\"</div>\",[_vm._ssrNode(\"<div class=\\\"login-form-box\\\" data-v-a0d0f46a>\",\"</div>\",[_vm._ssrNode(\"<div class=\\\"login-form-item\\\" data-v-a0d0f46a>\",\"</div>\",[_c('el-input',{staticClass:\"input-phone-num\",attrs:{\"placeholder\":\"请输入账号/手机号码\"},model:{value:(_vm.account),callback:function ($$v) {_vm.account=$$v},expression:\"account\"}},[_c('i',{staticClass:\"el-icon-user\",staticStyle:{\"font-size\":\"18px\"},attrs:{\"slot\":\"prepend\"},slot:\"prepend\"})])],1),_vm._ssrNode(\" \"),_vm._ssrNode(\"<div class=\\\"login-form-item flex\\\" data-v-a0d0f46a>\",\"</div>\",[_c('el-input',{attrs:{\"placeholder\":\"请输入密码\",\"show-password\":\"\"},model:{value:(_vm.password),callback:function ($$v) {_vm.password=$$v},expression:\"password\"}},[_c('i',{staticClass:\"el-icon-more-outline\",staticStyle:{\"font-size\":\"18px\"},attrs:{\"slot\":\"prepend\"},slot:\"prepend\"})])],1)],2),_vm._ssrNode(\" \"),_vm._ssrNode(\"<div class=\\\"option-box flex-col\\\" data-v-a0d0f46a>\",\"</div>\",[_vm._ssrNode(\"<div class=\\\"flex row-between\\\" data-v-a0d0f46a>\",\"</div>\",[_c('el-checkbox',{staticClass:\"muted\",model:{value:(_vm.isRemember),callback:function ($$v) {_vm.isRemember=$$v},expression:\"isRemember\"}},[_vm._v(\"记住账号\")]),_vm._ssrNode(\" \"),_c('nuxt-link',{staticClass:\"muted\",attrs:{\"to\":\"/account/forget_pwd\"}},[_vm._v(\"忘记密码？\")])],2),_vm._ssrNode(\" \"),_vm._ssrNode(\"<div class=\\\"m-t-20 flex-col\\\" data-v-a0d0f46a>\",\"</div>\",[_c('el-button',{attrs:{\"type\":\"primary\"},on:{\"click\":_vm.accountLogin}},[_vm._v(\"立即登录\")])],1)],2)],2)],2),_vm._ssrNode(\" \"),_vm._ssrNode(\"<div class=\\\"login-footer-box flex row-between\\\" data-v-a0d0f46a>\",\"</div>\",[_vm._ssrNode(\"<div class=\\\"flex\\\" data-v-a0d0f46a><div class=\\\"flex login__other-item\\\" data-v-a0d0f46a><i class=\\\"\\n                                        iconfont\\n                                        icon-weixin1\\n                                        login__weixin-icon\\n                                    \\\" data-v-a0d0f46a></i> <div class=\\\"m-l-4 muted\\\" data-v-a0d0f46a>微信</div></div></div> \"),_c('nuxt-link',{staticClass:\"primary\",attrs:{\"to\":\"/account/register\"}},[_vm._v(\"\\n                            注册账号\\n                        \")])],2)],2)])],2)])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { loginType, client, SMSType } from '@/utils/type'\nimport Cookies from 'js-cookie'\nimport { mapMutations, mapActions, mapState } from 'vuex'\nimport CountDown from '@/components/count-down'\n\nexport default {\n    head() {\n        return {\n            title: this.$store.getters.headTitle,\n            link: [\n                {\n                    rel: 'icon',\n                    type: 'image/x-icon',\n                    href: this.$store.getters.favicon,\n                },\n            ],\n        }\n    },\n\n    layout: 'main',\n\n    components: {\n        CountDown,\n    },\n\n    data() {\n        return {\n            // 选择电话类型\n            selectNumberType: '中国+86',\n            // 账号\n            account: '',\n            // 密码\n            password: '',\n            // 电话号码\n            telephone: '',\n            // 图形验证码\n            verifyCode: '',\n            // 短信验证码\n            smsCode: '',\n            isRemember: true,\n            // 短信登陆或账号登陆 0 ==》 短信 1 ==》账号\n            loginStatus: loginType.SMS,\n            canSend: true,\n        }\n    },\n    computed: {\n        ...mapState(['config']),\n    },\n    methods: {\n        ...mapMutations(['setToken']),\n        ...mapActions(['getPublicData']),\n\n        changeLoginType(type) {\n            this.loginStatus = type\n            this.telephone = ''\n            this.verifyCode = ''\n            this.smsCode = ''\n            let jsonPaser = JSON.parse(localStorage.getItem('ACCOUNT'))\n            let telJson = JSON.parse(localStorage.getItem('TEL'))\n            if (jsonPaser && jsonPaser.account) {\n                this.account = jsonPaser.account\n            }\n            if (telJson && telJson.telephone) {\n                this.telephone = telJson.telephone\n            }\n        },\n\n        // 前往微信登录\n        goWechatLogin() {\n            this.$get('account/scanCode')\n                .then(({ code, msg, data }) => {\n                    if (code !== 1) throw new Error(msg)\n                    window.open(data.url, '_self')\n                })\n                .catch((err) => {\n                    this.$message.error(err.message)\n                })\n        },\n\n        // 微信授权登录处理\n        handleWechatLogin(params) {\n            this.$post('account/scanLogin', params)\n                .then(({ code, msg, data }) => {\n                    if (code !== 1) throw new Error(msg)\n\n                    Cookies.set('token', data.token, { expires: 60 })\n                    this.setToken(data.token)\n                    this.$router.replace({\n                        path: Cookies.get('back_url') || '/',\n                    })\n                    Cookies.remove('back_url')\n                    this.getPublicData()\n                })\n                .catch((err) => {\n                    this.$message.error(err.message)\n                })\n        },\n\n        async sendSMSCode() {\n            if (!this.canSend) {\n                return\n            }\n            let res = await this.$post('sms/send', {\n                mobile: this.telephone,\n                key: SMSType.LOGIN,\n                client,\n            })\n            if (res.code == 1) {\n                this.$message({\n                    message: res.msg,\n                    type: 'success',\n                })\n                this.canSend = false\n            }\n        },\n\n        async smsLogin() {\n            let res = await this.$post('account/smsLogin', {\n                mobile: this.telephone,\n                code: this.smsCode,\n                client,\n            })\n            if (res.code == 1) {\n                const token = res.data.token\n                Cookies.set('token', token, { expires: 60 })\n                this.setToken(token)\n                this.$router.replace({\n                    path: Cookies.get('back_url') || '/',\n                })\n                Cookies.remove('back_url')\n                this.getPublicData()\n                if (this.isRemember) {\n                    localStorage.setItem(\n                        'TEL',\n                        JSON.stringify({\n                            telephone: this.telephone,\n                        })\n                    )\n                } else {\n                    localStorage.setItem(\n                        'TEL',\n                        JSON.stringify({\n                            telephone: '',\n                        })\n                    )\n                }\n            }\n        },\n\n        async accountLogin() {\n            let res = await this.$post('account/login', {\n                mobile: this.account,\n                password: this.password,\n                client,\n            })\n            if (res.code == 1) {\n                const token = res.data.token\n                Cookies.set('token', token, { expires: 60 })\n                this.setToken(token)\n                this.$router.replace({\n                    path: Cookies.get('back_url') || '/',\n                })\n                Cookies.remove('back_url')\n                this.getPublicData()\n                if (this.isRemember) {\n                    localStorage.setItem(\n                        'ACCOUNT',\n                        JSON.stringify({\n                            account: this.account,\n                        })\n                    )\n                } else {\n                    localStorage.setItem(\n                        'ACCOUNT',\n                        JSON.stringify({\n                            account: '',\n                        })\n                    )\n                }\n            }\n        },\n    },\n\n    created() {\n        // 获取URL Query参数\n        const query = this.$route.query\n\n        // 如果存在code和state值，即为微信扫码授权后重定向至此\n        query.code &&\n            query.state &&\n            this.handleWechatLogin({ code: query.code, state: query.state })\n\n        // 获取缓存数据\n        const jsonPaser = JSON.parse(localStorage.getItem('ACCOUNT')) ?? {}\n        const telJson = JSON.parse(localStorage.getItem('TEL')) ?? {}\n        // 利用缓存数据初始化表单值\n        this.account = jsonPaser?.account ?? ''\n        this.telephone = telJson?.telephone ?? ''\n    },\n}\n", "import mod from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./login.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./login.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./login.vue?vue&type=template&id=a0d0f46a&scoped=true&\"\nimport script from \"./login.vue?vue&type=script&lang=js&\"\nexport * from \"./login.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./login.vue?vue&type=style&index=0&id=a0d0f46a&lang=scss&scoped=true&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  \"a0d0f46a\",\n  \"54a2d336\"\n  \n)\n\nexport default component.exports\n\n/* nuxt-component-imports */\ninstallComponents(component, {CountDown: require('/Users/<USER>/Desktop/vue/pc/components/count-down.vue').default})\n"], "mappings": ";;;;;;;;;;;AAAA;AACA;AACA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAJA;AAMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;;ACvCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAbA;AAkBA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAPA;AADA;AACA;AASA;AACA;AACA;AACA;AAFA;AAIA;AACA;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AApDA;AAtCA;;ACXA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACrBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;AACA;AAFA;AACA;AAMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAZA;AAeA;AACA;AACA;AACA;AACA;AACA;AALA;AACA;AASA;AACA;AACA;AACA;AACA;AACA;AACA;AANA;;;;;;;AClCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACXA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACNA;AACA;AACA;;;;;;;;;;;;;;;;;;ACFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAHA;AAHA;AAUA;AACA;AACA;AAEA;AACA;AADA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAhBA;AAkBA;AACA;AAAA;AAAA;AAGA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAEA;AAAA;AAAA;AACA;AACA;AACA;AADA;AAGA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAHA;AACA;AAIA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAHA;AACA;AAIA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AADA;AAGA;AACA;AACA;AAAA;AACA;AAGA;AADA;AAIA;AACA;AAGA;AADA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAHA;AACA;AAIA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AADA;AAGA;AACA;AACA;AAAA;AACA;AAGA;AADA;AAIA;AACA;AAGA;AADA;AAIA;AACA;AACA;AACA;AArIA;AACA;AAsIA;AAAA;AACA;AAAA;AACA;AACA;AAEA;AAEA;AAAA;AAAA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAlMA;;AC1KA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;A", "sourceRoot": ""}