<?php
namespace app\api\controller;

use app\api\logic\PdfSignatureProcessor;
use app\api\logic\PdfCreator;
use app\api\logic\ImageMagickPdfProcessor;
use app\api\logic\GdPdfProcessor;
use app\api\logic\FpdiPdfProcessor;
use app\api\logic\SimplePdfEditor;
use app\common\basics\Api;
use app\common\server\JsonServer;
use app\common\server\UrlServer;
use app\common\server\FileServer;
use think\facade\Log;
use Exception;

class PdfProcessor extends Api
{
    public $like_not_need_login = ['addSignature', 'createSignedPdf', 'addSignatureWithImageMagick', 'addSignatureWithGd', 'addSignatureWithFpdi', 'addSignatureSimple'];

    /**
     * 添加签名和日期到PDF文件
     * @return \think\response\Json
     */
    public function addSignature()
    {
        try {
            // 获取请求参数
            $post = $this->request->post();

            // 检查必要参数
            if (empty($post['signature_base64']) && empty($post['signature_url'])) {
                return JsonServer::error('缺少签名图片数据或图片URL');
            }

            // 获取PDF文件路径
            $pdfPath = public_path() . 'images/share/zsxy.pdf';
            if (!file_exists($pdfPath)) {
                return JsonServer::error('PDF文件不存在');
            }

            // 准备临时目录
            $tempDir = runtime_path() . 'temp';
            // 确保temp目录存在
            if (!is_dir($tempDir)) {
                // 如果temp目录不存在，使用runtime目录
                $tempDir = runtime_path();
            }

            // 处理签名图片
            if (!empty($post['signature_url'])) {
                // 使用URL获取图片
                $signatureUrl = $post['signature_url'];

                // 从URL中提取文件扩展名
                $pathInfo = pathinfo($signatureUrl);
                $imageType = isset($pathInfo['extension']) ? strtolower($pathInfo['extension']) : 'png';

                // 确保扩展名是有效的图片类型
                if (!in_array($imageType, ['png', 'jpg', 'jpeg'])) {
                    $imageType = 'jpg'; // 默认使用jpg，因为FPDI更好地支持它
                }

                // 保存签名图片到临时文件
                $signaturePath = $tempDir . '/' . uniqid() . '.' . $imageType;

                // 下载图片
                try {
                    // 使用curl下载图片（更可靠）
                    $ch = curl_init();
                    curl_setopt($ch, CURLOPT_URL, $signatureUrl);
                    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
                    curl_setopt($ch, CURLOPT_TIMEOUT, 30); // 增加超时时间
                    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
                    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true); // 允许重定向
                    $imageData = curl_exec($ch);
                    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                    curl_close($ch);

                    if (!$imageData || $httpCode != 200) {
                        // 如果curl失败，尝试使用file_get_contents
                        $imageData = @file_get_contents($signatureUrl);
                        if ($imageData === false) {
                            return JsonServer::error('无法从URL下载图片，HTTP状态码: ' . $httpCode);
                        }
                    }

                    // 保存图片
                    if (file_put_contents($signaturePath, $imageData) === false) {
                        return JsonServer::error('保存签名图片失败，可能是权限问题');
                    }

                    // 验证图片是否有效
                    if (!file_exists($signaturePath)) {
                        return JsonServer::error('保存后的签名图片文件不存在');
                    }

                    $fileSize = filesize($signaturePath);
                    if ($fileSize <= 0) {
                        return JsonServer::error('保存的签名图片文件为空，大小: ' . $fileSize);
                    }

                    // 验证图片格式
                    $imageInfo = @getimagesize($signaturePath);
                    if (!$imageInfo) {
                        return JsonServer::error('无法获取图片信息，可能不是有效的图片文件');
                    }

                    // 记录图片信息到日志
                    Log::info('签名图片信息: ' . json_encode($imageInfo));
                    Log::info('签名图片路径: ' . $signaturePath);
                    Log::info('签名图片大小: ' . $fileSize . ' 字节');

                } catch (Exception $e) {
                    return JsonServer::error('下载图片时出错: ' . $e->getMessage());
                }
            } else {
                // 使用base64数据
                $signatureBase64 = $post['signature_base64'];

                // 检查并提取base64数据
                if (preg_match('/^data:image\/(png|jpeg|jpg|gif);base64,/', $signatureBase64, $matches)) {
                    $imageType = $matches[1];
                    $signatureBase64 = preg_replace('/^data:image\/(png|jpeg|jpg|gif);base64,/', '', $signatureBase64);
                    $signatureBase64 = str_replace(' ', '+', $signatureBase64); // 修复可能的空格问题
                } else {
                    // 如果没有前缀，假设是纯base64数据
                    $imageType = 'jpg'; // 默认使用jpg
                }

                // 确保扩展名是有效的图片类型
                if (!in_array($imageType, ['png', 'jpg', 'jpeg'])) {
                    $imageType = 'jpg';
                }

                // 保存签名图片到临时文件
                $signaturePath = $tempDir . '/' . uniqid() . '.' . $imageType;

                // 解码并保存
                $imageData = base64_decode($signatureBase64);
                if (!$imageData) {
                    return JsonServer::error('无效的base64图片数据');
                }

                if (file_put_contents($signaturePath, $imageData) === false) {
                    return JsonServer::error('保存签名图片失败，可能是权限问题');
                }

                // 验证图片是否有效
                if (!file_exists($signaturePath)) {
                    return JsonServer::error('保存后的签名图片文件不存在');
                }

                $fileSize = filesize($signaturePath);
                if ($fileSize <= 0) {
                    return JsonServer::error('保存的签名图片文件为空，大小: ' . $fileSize);
                }

                // 验证图片格式
                $imageInfo = @getimagesize($signaturePath);
                if (!$imageInfo) {
                    return JsonServer::error('无法获取图片信息，可能不是有效的图片文件');
                }

                // 记录图片信息到日志
                Log::info('签名图片信息: ' . json_encode($imageInfo));
                Log::info('签名图片路径: ' . $signaturePath);
                Log::info('签名图片大小: ' . $fileSize . ' 字节');
            }

            // 设置输出PDF路径
            $baseOutputDir = public_path() . 'uploads/pdf';
            // 确保基本输出目录存在
            if (!is_dir($baseOutputDir)) {
                // 如果pdf目录不存在，使用uploads目录
                $baseOutputDir = public_path() . 'uploads';
                // 如果uploads目录也不存在，使用public目录
                if (!is_dir($baseOutputDir)) {
                    $baseOutputDir = public_path();
                }
            }
            $outputPath = $baseOutputDir . '/' . uniqid() . '.pdf';

            // 获取PDF总页数以定位到最后一页
            $pdf = new \setasign\Fpdi\Fpdi();
            $pageCount = $pdf->setSourceFile($pdfPath);
            $pageNumber = $pageCount; // 最后一页

            // 获取页面尺寸以正确定位
            $templateId = $pdf->importPage(1);
            $size = $pdf->getTemplateSize($templateId);

            // 记录页面尺寸信息
            Log::info('PDF页面尺寸 - 宽度: ' . $size['width'] . ', 高度: ' . $size['height']);

            // 创建处理器实例
            $processor = new PdfSignatureProcessor($pdfPath);
            $processor->setSignaturePath($signaturePath)
                ->setOutputPath($outputPath);

            // 获取PDF页面尺寸信息，用于计算精确位置
            $pageWidth = $size['width'];
            $pageHeight = $size['height'];

            // 根据图片中红框位置精确定位签名和日期
            // 使用PDF页面尺寸的比例来计算位置，确保在不同尺寸的PDF上都能正确显示

            // 签名位置（右侧红框位置）
            $signatureX =55;  // 右侧红框的X坐标
            $signatureY = 137;  // 右侧红框的Y坐标
            $signatureWidth = 39.6; // 签名图片宽度，缩小以适应红框
            $signatureHeight = 13.45; // 签名图片高度，缩小以适应红框

            // 记录调整后的坐标
            Log::info('调整后的签名位置 - X: ' . $signatureX . ', Y: ' . $signatureY .
                     ', 宽度: ' . $signatureWidth . ', 高度: ' . $signatureHeight);
            $processor->setSignaturePosition($signatureX, $signatureY, $signatureWidth, $signatureHeight);

            // 设置日期位置（左侧两个红框位置）
            // 第一个日期位置（上方红框）
            $dateX = 138;  // 左侧上方红框的X坐标
            $dateY = 131;  // 上方红框的Y坐标
            $processor->setDatePosition($dateX, $dateY);

            // 设置第二个日期位置（下方红框）
            $secondDateX = 138;  // 左侧下方红框的X坐标
            $secondDateY = 144;  // 下方红框的Y坐标
            $processor->setSecondDatePosition($secondDateX, $secondDateY);

            // 记录PDF尺寸信息，便于调试
            Log::info('PDF页面尺寸 - 宽度: ' . $pageWidth . ', 高度: ' . $pageHeight);

            // 记录第二个日期位置信息
            Log::info('第二个日期位置 - X: ' . $secondDateX . ', Y: ' . $secondDateY);

            // 记录位置信息
            Log::info('签名位置 - X: ' . $signatureX . ', Y: ' . $signatureY . ', 宽度: ' . $signatureWidth . ', 高度: ' . $signatureHeight);
            Log::info('日期位置 - X: ' . $dateX . ', Y: ' . $dateY);

            // 处理PDF
            // 使用简单的日期格式，避免中文乱码问题
            $dateFormat = $post['date_format'] ?? 'Y-m-d';
            $resultPath = $processor->process($pageNumber, $dateFormat);

            // 清理临时文件
            @unlink($signaturePath);

            // 确保文件上传到OSS（如果配置了OSS）
            $engine = \app\common\server\ConfigServer::get('storage', 'default', 'local');
            if ($engine != 'local') {
                Log::info('检测到非本地存储配置，尝试上传到OSS: ' . $engine);
                try {
                    // 获取存储配置
                    $config = [
                        'default' => $engine,
                        'engine' => \app\common\server\ConfigServer::get('storage_engine')
                    ];

                    // 创建存储驱动
                    $StorageDriver = new \app\common\server\storage\Driver($config);

                    // 设置文件
                    $StorageDriver->setUploadFileByReal($resultPath);

                    // 上传到OSS
                    $savePath = 'uploads/pdf';
                    if ($StorageDriver->upload($savePath)) {
                        $fileName = $StorageDriver->getFileName();
                        $ossPath = $savePath . '/' . $fileName;
                        Log::info('文件已成功上传到OSS: ' . $ossPath);

                        // 获取OSS域名
                        $domain = \app\common\server\ConfigServer::get('storage_engine', $engine, [])['domain'] ?? '';
                        if (!empty($domain)) {
                            // 更新输出路径为完整的OSS URL
                            $resultPath = rtrim($domain, '/') . '/' . $ossPath;
                            Log::info('更新结果路径为完整的OSS URL: ' . $resultPath);
                        } else {
                            // 如果没有配置域名，则使用相对路径
                            $resultPath = $ossPath;
                            Log::info('更新结果路径为OSS相对路径: ' . $resultPath);
                        }
                    } else {
                        Log::error('上传到OSS失败: ' . $StorageDriver->getError());
                    }
                } catch (Exception $e) {
                    Log::error('上传到OSS过程中出错: ' . $e->getMessage());
                    // 继续使用本地文件路径
                }
            }

            // 检查结果路径是否为OSS路径
            $isOssPath = (strpos($resultPath, 'http://') === 0 || strpos($resultPath, 'https://') === 0);
            if ($isOssPath) {
                // 如果是OSS路径，直接使用
                $fileUrl = $resultPath;
                Log::info('使用OSS路径作为文件URL: ' . $fileUrl);
            } else {
                // 否则，使用UrlServer获取文件URL
                $fileUrl = UrlServer::getFileUrl(str_replace(public_path(), '', $resultPath));
                Log::info('使用UrlServer获取文件URL: ' . $fileUrl);
            }

            // 确定使用的签名来源
            $signatureSource = !empty($post['signature_url']) ? '图片URL' : 'base64图片';

            return JsonServer::success('处理成功', [
                'file_path' => $resultPath,
                'file_url' => $fileUrl,
                'message' => "已成功使用{$signatureSource}在PDF文件中添加签名和日期"
            ]);

        } catch (Exception $e) {
            // 记录详细错误信息
            $signatureSource = !empty($post['signature_url']) ? '图片URL' : 'base64图片';
            Log::error("PDF处理错误(使用{$signatureSource}): " . $e->getMessage());
            Log::error('错误位置: ' . $e->getFile() . ':' . $e->getLine());
            Log::error('错误堆栈: ' . $e->getTraceAsString());

            // 清理临时文件
            if (isset($signaturePath) && file_exists($signaturePath)) {
                @unlink($signaturePath);
            }

            if (isset($outputPath) && file_exists($outputPath)) {
                @unlink($outputPath);
            }

            // 清理临时文件
            if (isset($signaturePath) && file_exists($signaturePath)) {
                @unlink($signaturePath);
            }

            // 如果输出文件已创建但处理失败，删除它
            if (isset($outputPath) && file_exists($outputPath)) {
                @unlink($outputPath);
            }

            return JsonServer::error('处理PDF文件时出错: ' . $e->getMessage());
        }
    }

    /**
     * 创建带签名和日期的PDF文件
     * @return \think\response\Json
     */
    public function createSignedPdf()
    {
        try {
            // 获取请求参数
            $post = $this->request->post();

            // 检查必要参数
            if (empty($post['signature_url']) && empty($post['signature_base64'])) {
                return JsonServer::error('缺少签名图片数据或图片URL');
            }

            // 获取PDF文件路径
            $pdfPath = public_path() . 'images/share/zsxy.pdf';
            if (!file_exists($pdfPath)) {
                return JsonServer::error('PDF文件不存在');
            }

            // 准备临时目录
            $tempDir = runtime_path() . 'temp';
            // 确保temp目录存在
            if (!is_dir($tempDir)) {
                // 如果temp目录不存在，使用runtime目录
                $tempDir = runtime_path();
            }

            // 处理签名图片
            if (!empty($post['signature_url'])) {
                // 使用URL获取图片
                $signatureUrl = $post['signature_url'];

                // 从URL中提取文件扩展名
                $pathInfo = pathinfo($signatureUrl);
                $imageType = isset($pathInfo['extension']) ? strtolower($pathInfo['extension']) : 'jpg';

                // 确保扩展名是有效的图片类型
                if (!in_array($imageType, ['png', 'jpg', 'jpeg'])) {
                    $imageType = 'jpg'; // 默认使用jpg
                }

                // 保存签名图片到临时文件
                $signaturePath = $tempDir . '/' . uniqid() . '.' . $imageType;

                // 下载图片
                try {
                    // 使用curl下载图片（更可靠）
                    $ch = curl_init();
                    curl_setopt($ch, CURLOPT_URL, $signatureUrl);
                    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
                    curl_setopt($ch, CURLOPT_TIMEOUT, 30); // 增加超时时间
                    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
                    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true); // 允许重定向
                    $imageData = curl_exec($ch);
                    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                    curl_close($ch);

                    if (!$imageData || $httpCode != 200) {
                        // 如果curl失败，尝试使用file_get_contents
                        $imageData = @file_get_contents($signatureUrl);
                        if ($imageData === false) {
                            return JsonServer::error('无法从URL下载图片，HTTP状态码: ' . $httpCode);
                        }
                    }

                    // 保存图片
                    if (file_put_contents($signaturePath, $imageData) === false) {
                        return JsonServer::error('保存签名图片失败，可能是权限问题');
                    }

                    // 验证图片是否有效
                    if (!file_exists($signaturePath)) {
                        return JsonServer::error('保存后的签名图片文件不存在');
                    }

                    $fileSize = filesize($signaturePath);
                    if ($fileSize <= 0) {
                        return JsonServer::error('保存的签名图片文件为空，大小: ' . $fileSize);
                    }

                    // 验证图片格式
                    $imageInfo = @getimagesize($signaturePath);
                    if (!$imageInfo) {
                        return JsonServer::error('无法获取图片信息，可能不是有效的图片文件');
                    }

                    // 记录图片信息到日志
                    Log::info('签名图片信息: ' . json_encode($imageInfo));
                    Log::info('签名图片路径: ' . $signaturePath);
                    Log::info('签名图片大小: ' . $fileSize . ' 字节');

                } catch (Exception $e) {
                    return JsonServer::error('下载图片时出错: ' . $e->getMessage());
                }
            } else {
                // 使用base64数据
                $signatureBase64 = $post['signature_base64'];

                // 检查并提取base64数据
                if (preg_match('/^data:image\/(png|jpeg|jpg|gif);base64,/', $signatureBase64, $matches)) {
                    $imageType = $matches[1];
                    $signatureBase64 = preg_replace('/^data:image\/(png|jpeg|jpg|gif);base64,/', '', $signatureBase64);
                    $signatureBase64 = str_replace(' ', '+', $signatureBase64); // 修复可能的空格问题
                } else {
                    // 如果没有前缀，假设是纯base64数据
                    $imageType = 'jpg'; // 默认使用jpg
                }

                // 确保扩展名是有效的图片类型
                if (!in_array($imageType, ['png', 'jpg', 'jpeg'])) {
                    $imageType = 'jpg';
                }

                // 保存签名图片到临时文件
                $signaturePath = $tempDir . '/' . uniqid() . '.' . $imageType;

                // 解码并保存
                $imageData = base64_decode($signatureBase64);
                if (!$imageData) {
                    return JsonServer::error('无效的base64图片数据');
                }

                if (file_put_contents($signaturePath, $imageData) === false) {
                    return JsonServer::error('保存签名图片失败，可能是权限问题');
                }

                // 验证图片是否有效
                if (!file_exists($signaturePath)) {
                    return JsonServer::error('保存后的签名图片文件不存在');
                }

                $fileSize = filesize($signaturePath);
                if ($fileSize <= 0) {
                    return JsonServer::error('保存的签名图片文件为空，大小: ' . $fileSize);
                }

                // 验证图片格式
                $imageInfo = @getimagesize($signaturePath);
                if (!$imageInfo) {
                    return JsonServer::error('无法获取图片信息，可能不是有效的图片文件');
                }

                // 记录图片信息到日志
                Log::info('签名图片信息: ' . json_encode($imageInfo));
                Log::info('签名图片路径: ' . $signaturePath);
                Log::info('签名图片大小: ' . $fileSize . ' 字节');
            }

            // 设置输出PDF路径
            $baseOutputDir = public_path() . 'uploads/pdf';
            // 确保基本输出目录存在
            if (!is_dir($baseOutputDir)) {
                // 如果pdf目录不存在，使用uploads目录
                $baseOutputDir = public_path() . 'uploads';
                // 如果uploads目录也不存在，使用public目录
                if (!is_dir($baseOutputDir)) {
                    $baseOutputDir = public_path();
                }
            }
            $outputPath = $baseOutputDir . '/' . uniqid() . '.pdf';

            // 创建PDF处理器实例
            $creator = new PdfCreator($pdfPath);
            $creator->setSignaturePath($signaturePath)
                ->setOutputPath($outputPath);

            // 设置签名位置（根据实际PDF调整）
            $signatureX = $post['signature_x'] ?? 50;
            $signatureY = $post['signature_y'] ?? 50;
            $signatureWidth = $post['signature_width'] ?? 80;
            $signatureHeight = $post['signature_height'] ?? 40;
            $creator->setSignaturePosition($signatureX, $signatureY, $signatureWidth, $signatureHeight);

            // 设置日期位置（根据实际PDF调整）
            $dateX = $post['date_x'] ?? 50;
            $dateY = $post['date_y'] ?? 100;
            $creator->setDatePosition($dateX, $dateY);

            // 处理PDF
            // 使用简单的日期格式，避免中文乱码问题
            $dateFormat = $post['date_format'] ?? 'Y-m-d';
            $resultPath = $creator->create($dateFormat);

            // 清理临时文件
            @unlink($signaturePath);

            // 确保文件上传到OSS（如果配置了OSS）
            $engine = \app\common\server\ConfigServer::get('storage', 'default', 'local');
            if ($engine != 'local') {
                Log::info('检测到非本地存储配置，尝试上传到OSS: ' . $engine);
                try {
                    // 获取存储配置
                    $config = [
                        'default' => $engine,
                        'engine' => \app\common\server\ConfigServer::get('storage_engine')
                    ];

                    // 创建存储驱动
                    $StorageDriver = new \app\common\server\storage\Driver($config);

                    // 设置文件
                    $StorageDriver->setUploadFileByReal($resultPath);

                    // 上传到OSS
                    $savePath = 'uploads/pdf';
                    if ($StorageDriver->upload($savePath)) {
                        $fileName = $StorageDriver->getFileName();
                        $ossPath = $savePath . '/' . $fileName;
                        Log::info('文件已成功上传到OSS: ' . $ossPath);

                        // 获取OSS域名
                        $domain = \app\common\server\ConfigServer::get('storage_engine', $engine, [])['domain'] ?? '';
                        if (!empty($domain)) {
                            // 更新输出路径为完整的OSS URL
                            $resultPath = rtrim($domain, '/') . '/' . $ossPath;
                            Log::info('更新结果路径为完整的OSS URL: ' . $resultPath);
                        } else {
                            // 如果没有配置域名，则使用相对路径
                            $resultPath = $ossPath;
                            Log::info('更新结果路径为OSS相对路径: ' . $resultPath);
                        }
                    } else {
                        Log::error('上传到OSS失败: ' . $StorageDriver->getError());
                    }
                } catch (Exception $e) {
                    Log::error('上传到OSS过程中出错: ' . $e->getMessage());
                    // 继续使用本地文件路径
                }
            }

            // 检查结果路径是否为OSS路径
            $isOssPath = (strpos($resultPath, 'http://') === 0 || strpos($resultPath, 'https://') === 0);
            if ($isOssPath) {
                // 如果是OSS路径，直接使用
                $fileUrl = $resultPath;
                Log::info('使用OSS路径作为文件URL: ' . $fileUrl);
            } else {
                // 否则，使用UrlServer获取文件URL
                $fileUrl = UrlServer::getFileUrl(str_replace(public_path(), '', $resultPath));
                Log::info('使用UrlServer获取文件URL: ' . $fileUrl);
            }

            // 确定使用的签名来源
            $signatureSource = !empty($post['signature_url']) ? '图片URL' : 'base64图片';

            // 检查输出文件类型
            $isZip = strtolower(pathinfo($resultPath, PATHINFO_EXTENSION)) === 'zip';

            return JsonServer::success('处理成功', [
                'file_path' => $resultPath,
                'file_url' => $fileUrl,
                'is_zip' => $isZip,
                'message' => "已成功使用{$signatureSource}创建包含签名和日期的文件" . ($isZip ? '，请下载ZIP文件并打开其中的HTML文件查看' : '')
            ]);

        } catch (Exception $e) {
            // 记录详细错误信息
            $signatureSource = !empty($post['signature_url']) ? '图片URL' : 'base64图片';
            Log::error("PDF处理错误(使用{$signatureSource}): " . $e->getMessage());
            Log::error('错误位置: ' . $e->getFile() . ':' . $e->getLine());
            Log::error('错误堆栈: ' . $e->getTraceAsString());

            // 清理临时文件
            if (isset($signaturePath) && file_exists($signaturePath)) {
                @unlink($signaturePath);
            }

            if (isset($outputPath) && file_exists($outputPath)) {
                @unlink($outputPath);
            }

            return JsonServer::error('处理PDF文件时出错: ' . $e->getMessage());
        }
    }

    /**
     * 使用ImageMagick添加签名和日期到PDF文件
     * @return \think\response\Json
     */
    public function addSignatureWithImageMagick()
    {
        try {
            // 获取请求参数
            $post = $this->request->post();

            // 检查必要参数
            if (empty($post['signature_base64']) && empty($post['signature_url'])) {
                return JsonServer::error('缺少签名图片数据或图片URL');
            }

            // 获取PDF文件路径
            $pdfPath = public_path() . 'images/share/zsxy.pdf';
            if (!file_exists($pdfPath)) {
                return JsonServer::error('PDF文件不存在');
            }

            // 准备临时目录
            $tempDir = runtime_path() . 'temp';
            // 确保temp目录存在
            if (!is_dir($tempDir)) {
                // 如果temp目录不存在，使用runtime目录
                $tempDir = runtime_path();
            }

            // 处理签名图片
            if (!empty($post['signature_url'])) {
                // 使用URL获取图片
                $signatureUrl = $post['signature_url'];

                // 从URL中提取文件扩展名
                $pathInfo = pathinfo($signatureUrl);
                $imageType = isset($pathInfo['extension']) ? strtolower($pathInfo['extension']) : 'jpg';

                // 确保扩展名是有效的图片类型
                if (!in_array($imageType, ['png', 'jpg', 'jpeg'])) {
                    $imageType = 'jpg'; // 默认使用jpg
                }

                // 保存签名图片到临时文件
                $signaturePath = $tempDir . '/' . uniqid() . '.' . $imageType;

                // 下载图片
                try {
                    // 使用curl下载图片（更可靠）
                    $ch = curl_init();
                    curl_setopt($ch, CURLOPT_URL, $signatureUrl);
                    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
                    curl_setopt($ch, CURLOPT_TIMEOUT, 30); // 增加超时时间
                    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
                    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true); // 允许重定向
                    $imageData = curl_exec($ch);
                    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                    curl_close($ch);

                    if (!$imageData || $httpCode != 200) {
                        // 如果curl失败，尝试使用file_get_contents
                        $imageData = @file_get_contents($signatureUrl);
                        if ($imageData === false) {
                            return JsonServer::error('无法从URL下载图片，HTTP状态码: ' . $httpCode);
                        }
                    }

                    // 保存图片
                    if (file_put_contents($signaturePath, $imageData) === false) {
                        return JsonServer::error('保存签名图片失败，可能是权限问题');
                    }

                    // 验证图片是否有效
                    if (!file_exists($signaturePath)) {
                        return JsonServer::error('保存后的签名图片文件不存在');
                    }

                    $fileSize = filesize($signaturePath);
                    if ($fileSize <= 0) {
                        return JsonServer::error('保存的签名图片文件为空，大小: ' . $fileSize);
                    }

                    // 验证图片格式
                    $imageInfo = @getimagesize($signaturePath);
                    if (!$imageInfo) {
                        return JsonServer::error('无法获取图片信息，可能不是有效的图片文件');
                    }

                    // 记录图片信息到日志
                    Log::info('签名图片信息: ' . json_encode($imageInfo));
                    Log::info('签名图片路径: ' . $signaturePath);
                    Log::info('签名图片大小: ' . $fileSize . ' 字节');

                } catch (Exception $e) {
                    return JsonServer::error('下载图片时出错: ' . $e->getMessage());
                }
            } else {
                // 使用base64数据
                $signatureBase64 = $post['signature_base64'];

                // 检查并提取base64数据
                if (preg_match('/^data:image\/(png|jpeg|jpg|gif);base64,/', $signatureBase64, $matches)) {
                    $imageType = $matches[1];
                    $signatureBase64 = preg_replace('/^data:image\/(png|jpeg|jpg|gif);base64,/', '', $signatureBase64);
                    $signatureBase64 = str_replace(' ', '+', $signatureBase64); // 修复可能的空格问题
                } else {
                    // 如果没有前缀，假设是纯base64数据
                    $imageType = 'jpg'; // 默认使用jpg
                }

                // 确保扩展名是有效的图片类型
                if (!in_array($imageType, ['png', 'jpg', 'jpeg'])) {
                    $imageType = 'jpg';
                }

                // 保存签名图片到临时文件
                $signaturePath = $tempDir . '/' . uniqid() . '.' . $imageType;

                // 解码并保存
                $imageData = base64_decode($signatureBase64);
                if (!$imageData) {
                    return JsonServer::error('无效的base64图片数据');
                }

                if (file_put_contents($signaturePath, $imageData) === false) {
                    return JsonServer::error('保存签名图片失败，可能是权限问题');
                }

                // 验证图片是否有效
                if (!file_exists($signaturePath)) {
                    return JsonServer::error('保存后的签名图片文件不存在');
                }

                $fileSize = filesize($signaturePath);
                if ($fileSize <= 0) {
                    return JsonServer::error('保存的签名图片文件为空，大小: ' . $fileSize);
                }

                // 验证图片格式
                $imageInfo = @getimagesize($signaturePath);
                if (!$imageInfo) {
                    return JsonServer::error('无法获取图片信息，可能不是有效的图片文件');
                }

                // 记录图片信息到日志
                Log::info('签名图片信息: ' . json_encode($imageInfo));
                Log::info('签名图片路径: ' . $signaturePath);
                Log::info('签名图片大小: ' . $fileSize . ' 字节');
            }

            // 设置输出PDF路径
            $baseOutputDir = public_path() . 'uploads/pdf';
            // 确保基本输出目录存在
            if (!is_dir($baseOutputDir)) {
                // 如果pdf目录不存在，使用uploads目录
                $baseOutputDir = public_path() . 'uploads';
                // 如果uploads目录也不存在，使用public目录
                if (!is_dir($baseOutputDir)) {
                    $baseOutputDir = public_path();
                }
            }
            $outputPath = $baseOutputDir . '/' . uniqid() . '.pdf';

            // 创建处理器实例
            $processor = new ImageMagickPdfProcessor($pdfPath);
            $processor->setSignaturePath($signaturePath)
                ->setOutputPath($outputPath);

            // 设置签名位置（根据实际PDF调整）
            $signatureX = $post['signature_x'] ?? 50;
            $signatureY = $post['signature_y'] ?? 50;
            $signatureWidth = $post['signature_width'] ?? 80;
            $signatureHeight = $post['signature_height'] ?? 40;
            $processor->setSignaturePosition($signatureX, $signatureY, $signatureWidth, $signatureHeight);

            // 设置日期位置（根据实际PDF调整）
            $dateX = $post['date_x'] ?? 50;
            $dateY = $post['date_y'] ?? 100;
            $processor->setDatePosition($dateX, $dateY);

            // 处理PDF
            $pageNumber = $post['page_number'] ?? 1;
            // 使用简单的日期格式，避免中文乱码问题
            $dateFormat = $post['date_format'] ?? 'Y-m-d';
            $resultPath = $processor->process($pageNumber, $dateFormat);

            // 清理临时文件
            @unlink($signaturePath);

            // 确保文件上传到OSS（如果配置了OSS）
            $engine = \app\common\server\ConfigServer::get('storage', 'default', 'local');
            if ($engine != 'local') {
                Log::info('检测到非本地存储配置，尝试上传到OSS: ' . $engine);
                try {
                    // 获取存储配置
                    $config = [
                        'default' => $engine,
                        'engine' => \app\common\server\ConfigServer::get('storage_engine')
                    ];

                    // 创建存储驱动
                    $StorageDriver = new \app\common\server\storage\Driver($config);

                    // 设置文件
                    $StorageDriver->setUploadFileByReal($resultPath);

                    // 上传到OSS
                    $savePath = 'uploads/pdf';
                    if ($StorageDriver->upload($savePath)) {
                        $fileName = $StorageDriver->getFileName();
                        $ossPath = $savePath . '/' . $fileName;
                        Log::info('文件已成功上传到OSS: ' . $ossPath);

                        // 获取OSS域名
                        $domain = \app\common\server\ConfigServer::get('storage_engine', $engine, [])['domain'] ?? '';
                        if (!empty($domain)) {
                            // 更新输出路径为完整的OSS URL
                            $resultPath = rtrim($domain, '/') . '/' . $ossPath;
                            Log::info('更新结果路径为完整的OSS URL: ' . $resultPath);
                        } else {
                            // 如果没有配置域名，则使用相对路径
                            $resultPath = $ossPath;
                            Log::info('更新结果路径为OSS相对路径: ' . $resultPath);
                        }
                    } else {
                        Log::error('上传到OSS失败: ' . $StorageDriver->getError());
                    }
                } catch (Exception $e) {
                    Log::error('上传到OSS过程中出错: ' . $e->getMessage());
                    // 继续使用本地文件路径
                }
            }

            // 检查结果路径是否为OSS路径
            $isOssPath = (strpos($resultPath, 'http://') === 0 || strpos($resultPath, 'https://') === 0);
            if ($isOssPath) {
                // 如果是OSS路径，直接使用
                $fileUrl = $resultPath;
                Log::info('使用OSS路径作为文件URL: ' . $fileUrl);
            } else {
                // 否则，使用UrlServer获取文件URL
                $fileUrl = UrlServer::getFileUrl(str_replace(public_path(), '', $resultPath));
                Log::info('使用UrlServer获取文件URL: ' . $fileUrl);
            }

            // 确定使用的签名来源
            $signatureSource = !empty($post['signature_url']) ? '图片URL' : 'base64图片';

            return JsonServer::success('处理成功', [
                'file_path' => $resultPath,
                'file_url' => $fileUrl,
                'message' => "已成功使用{$signatureSource}在PDF文件中添加签名和日期"
            ]);

        } catch (Exception $e) {
            // 记录详细错误信息
            $signatureSource = !empty($post['signature_url']) ? '图片URL' : 'base64图片';
            Log::error("PDF处理错误(使用ImageMagick，{$signatureSource}): " . $e->getMessage());
            Log::error('错误位置: ' . $e->getFile() . ':' . $e->getLine());
            Log::error('错误堆栈: ' . $e->getTraceAsString());

            // 清理临时文件
            if (isset($signaturePath) && file_exists($signaturePath)) {
                @unlink($signaturePath);
            }

            if (isset($outputPath) && file_exists($outputPath)) {
                @unlink($outputPath);
            }

            return JsonServer::error('处理PDF文件时出错: ' . $e->getMessage());
        }
    }

    /**
     * 使用GD库添加签名和日期到PDF文件
     * @return \think\response\Json
     */
    public function addSignatureWithGd()
    {
        try {
            // 获取请求参数
            $post = $this->request->post();

            // 检查必要参数
            if (empty($post['signature_base64']) && empty($post['signature_url'])) {
                return JsonServer::error('缺少签名图片数据或图片URL');
            }

            // 获取PDF文件路径
            $pdfPath = public_path() . 'images/share/zsxy.pdf';
            if (!file_exists($pdfPath)) {
                return JsonServer::error('PDF文件不存在');
            }

            // 准备临时目录
            $tempDir = runtime_path() . 'temp';
            // 确保temp目录存在
            if (!is_dir($tempDir)) {
                // 如果temp目录不存在，使用runtime目录
                $tempDir = runtime_path();
            }

            // 处理签名图片
            if (!empty($post['signature_url'])) {
                // 使用URL获取图片
                $signatureUrl = $post['signature_url'];

                // 从URL中提取文件扩展名
                $pathInfo = pathinfo($signatureUrl);
                $imageType = isset($pathInfo['extension']) ? strtolower($pathInfo['extension']) : 'jpg';

                // 确保扩展名是有效的图片类型
                if (!in_array($imageType, ['png', 'jpg', 'jpeg'])) {
                    $imageType = 'jpg'; // 默认使用jpg
                }

                // 保存签名图片到临时文件
                $signaturePath = $tempDir . '/' . uniqid() . '.' . $imageType;

                // 下载图片
                try {
                    // 使用curl下载图片（更可靠）
                    $ch = curl_init();
                    curl_setopt($ch, CURLOPT_URL, $signatureUrl);
                    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
                    curl_setopt($ch, CURLOPT_TIMEOUT, 30); // 增加超时时间
                    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
                    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true); // 允许重定向
                    $imageData = curl_exec($ch);
                    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                    curl_close($ch);

                    if (!$imageData || $httpCode != 200) {
                        // 如果curl失败，尝试使用file_get_contents
                        $imageData = @file_get_contents($signatureUrl);
                        if ($imageData === false) {
                            return JsonServer::error('无法从URL下载图片，HTTP状态码: ' . $httpCode);
                        }
                    }

                    // 保存图片
                    if (file_put_contents($signaturePath, $imageData) === false) {
                        return JsonServer::error('保存签名图片失败，可能是权限问题');
                    }

                    // 验证图片是否有效
                    if (!file_exists($signaturePath)) {
                        return JsonServer::error('保存后的签名图片文件不存在');
                    }

                    $fileSize = filesize($signaturePath);
                    if ($fileSize <= 0) {
                        return JsonServer::error('保存的签名图片文件为空，大小: ' . $fileSize);
                    }

                    // 验证图片格式
                    $imageInfo = @getimagesize($signaturePath);
                    if (!$imageInfo) {
                        return JsonServer::error('无法获取图片信息，可能不是有效的图片文件');
                    }

                    // 记录图片信息到日志
                    Log::info('签名图片信息: ' . json_encode($imageInfo));
                    Log::info('签名图片路径: ' . $signaturePath);
                    Log::info('签名图片大小: ' . $fileSize . ' 字节');

                } catch (Exception $e) {
                    return JsonServer::error('下载图片时出错: ' . $e->getMessage());
                }
            } else {
                // 使用base64数据
                $signatureBase64 = $post['signature_base64'];

                // 检查并提取base64数据
                if (preg_match('/^data:image\/(png|jpeg|jpg|gif);base64,/', $signatureBase64, $matches)) {
                    $imageType = $matches[1];
                    $signatureBase64 = preg_replace('/^data:image\/(png|jpeg|jpg|gif);base64,/', '', $signatureBase64);
                    $signatureBase64 = str_replace(' ', '+', $signatureBase64); // 修复可能的空格问题
                } else {
                    // 如果没有前缀，假设是纯base64数据
                    $imageType = 'jpg'; // 默认使用jpg
                }

                // 确保扩展名是有效的图片类型
                if (!in_array($imageType, ['png', 'jpg', 'jpeg'])) {
                    $imageType = 'jpg';
                }

                // 保存签名图片到临时文件
                $signaturePath = $tempDir . '/' . uniqid() . '.' . $imageType;

                // 解码并保存
                $imageData = base64_decode($signatureBase64);
                if (!$imageData) {
                    return JsonServer::error('无效的base64图片数据');
                }

                if (file_put_contents($signaturePath, $imageData) === false) {
                    return JsonServer::error('保存签名图片失败，可能是权限问题');
                }

                // 验证图片是否有效
                if (!file_exists($signaturePath)) {
                    return JsonServer::error('保存后的签名图片文件不存在');
                }

                $fileSize = filesize($signaturePath);
                if ($fileSize <= 0) {
                    return JsonServer::error('保存的签名图片文件为空，大小: ' . $fileSize);
                }

                // 验证图片格式
                $imageInfo = @getimagesize($signaturePath);
                if (!$imageInfo) {
                    return JsonServer::error('无法获取图片信息，可能不是有效的图片文件');
                }

                // 记录图片信息到日志
                Log::info('签名图片信息: ' . json_encode($imageInfo));
                Log::info('签名图片路径: ' . $signaturePath);
                Log::info('签名图片大小: ' . $fileSize . ' 字节');
            }

            // 设置输出PDF路径
            $baseOutputDir = public_path() . 'uploads/pdf';
            // 确保基本输出目录存在
            if (!is_dir($baseOutputDir)) {
                // 如果pdf目录不存在，使用uploads目录
                $baseOutputDir = public_path() . 'uploads';
                // 如果uploads目录也不存在，使用public目录
                if (!is_dir($baseOutputDir)) {
                    $baseOutputDir = public_path();
                }
            }
            $outputPath = $baseOutputDir . '/' . uniqid() . '.pdf';

            // 创建日志文件
            $logFile = runtime_path() . 'log/pdf_processor.log';
            file_put_contents($logFile, date('Y-m-d H:i:s') . ' - 开始处理PDF文件' . PHP_EOL, FILE_APPEND);
            file_put_contents($logFile, date('Y-m-d H:i:s') . ' - PDF文件路径: ' . $pdfPath . PHP_EOL, FILE_APPEND);
            file_put_contents($logFile, date('Y-m-d H:i:s') . ' - 签名图片路径: ' . $signaturePath . PHP_EOL, FILE_APPEND);
            file_put_contents($logFile, date('Y-m-d H:i:s') . ' - 输出PDF路径: ' . $outputPath . PHP_EOL, FILE_APPEND);

            // 检查签名图片是否存在
            if (!file_exists($signaturePath)) {
                file_put_contents($logFile, date('Y-m-d H:i:s') . ' - 错误: 签名图片文件不存在: ' . $signaturePath . PHP_EOL, FILE_APPEND);
                return JsonServer::error('签名图片文件不存在: ' . $signaturePath);
            }

            // 检查图片文件大小
            $fileSize = filesize($signaturePath);
            file_put_contents($logFile, date('Y-m-d H:i:s') . ' - 签名图片文件大小: ' . $fileSize . ' 字节' . PHP_EOL, FILE_APPEND);
            if ($fileSize <= 0) {
                file_put_contents($logFile, date('Y-m-d H:i:s') . ' - 错误: 签名图片文件为空: ' . $signaturePath . ', 大小: ' . $fileSize . PHP_EOL, FILE_APPEND);
                return JsonServer::error('签名图片文件为空: ' . $signaturePath . ', 大小: ' . $fileSize);
            }

            // 创建处理器实例
            file_put_contents($logFile, date('Y-m-d H:i:s') . ' - 创建处理器实例' . PHP_EOL, FILE_APPEND);
            $processor = new GdPdfProcessor($pdfPath);
            $processor->setSignaturePath($signaturePath)
                ->setOutputPath($outputPath);

            // 设置签名位置（根据实际PDF调整）
            $signatureX = $post['signature_x'] ?? 50;
            $signatureY = $post['signature_y'] ?? 50;
            $signatureWidth = $post['signature_width'] ?? 80;
            $signatureHeight = $post['signature_height'] ?? 40;
            $processor->setSignaturePosition($signatureX, $signatureY, $signatureWidth, $signatureHeight);

            // 设置日期位置（根据实际PDF调整）
            $dateX = $post['date_x'] ?? 50;
            $dateY = $post['date_y'] ?? 100;
            $processor->setDatePosition($dateX, $dateY);

            // 处理PDF
            $pageNumber = $post['page_number'] ?? 1;
            // 使用简单的日期格式，避免中文乱码问题
            $dateFormat = $post['date_format'] ?? 'Y-m-d';
            $resultPath = $processor->process($pageNumber, $dateFormat);

            // 清理临时文件
            @unlink($signaturePath);

            // 确保文件上传到OSS（如果配置了OSS）
            $engine = \app\common\server\ConfigServer::get('storage', 'default', 'local');
            if ($engine != 'local') {
                Log::info('检测到非本地存储配置，尝试上传到OSS: ' . $engine);
                try {
                    // 获取存储配置
                    $config = [
                        'default' => $engine,
                        'engine' => \app\common\server\ConfigServer::get('storage_engine')
                    ];

                    // 创建存储驱动
                    $StorageDriver = new \app\common\server\storage\Driver($config);

                    // 设置文件
                    $StorageDriver->setUploadFileByReal($resultPath);

                    // 上传到OSS
                    $savePath = 'uploads/pdf';
                    if ($StorageDriver->upload($savePath)) {
                        $fileName = $StorageDriver->getFileName();
                        $ossPath = $savePath . '/' . $fileName;
                        Log::info('文件已成功上传到OSS: ' . $ossPath);

                        // 获取OSS域名
                        $domain = \app\common\server\ConfigServer::get('storage_engine', $engine, [])['domain'] ?? '';
                        if (!empty($domain)) {
                            // 更新输出路径为完整的OSS URL
                            $resultPath = rtrim($domain, '/') . '/' . $ossPath;
                            Log::info('更新结果路径为完整的OSS URL: ' . $resultPath);
                        } else {
                            // 如果没有配置域名，则使用相对路径
                            $resultPath = $ossPath;
                            Log::info('更新结果路径为OSS相对路径: ' . $resultPath);
                        }
                    } else {
                        Log::error('上传到OSS失败: ' . $StorageDriver->getError());
                    }
                } catch (Exception $e) {
                    Log::error('上传到OSS过程中出错: ' . $e->getMessage());
                    // 继续使用本地文件路径
                }
            }

            // 检查结果路径是否为OSS路径
            $isOssPath = (strpos($resultPath, 'http://') === 0 || strpos($resultPath, 'https://') === 0);
            if ($isOssPath) {
                // 如果是OSS路径，直接使用
                $fileUrl = $resultPath;
                Log::info('使用OSS路径作为文件URL: ' . $fileUrl);
            } else {
                // 否则，使用UrlServer获取文件URL
                $fileUrl = UrlServer::getFileUrl(str_replace(public_path(), '', $resultPath));
                Log::info('使用UrlServer获取文件URL: ' . $fileUrl);
            }

            // 确定使用的签名来源
            $signatureSource = !empty($post['signature_url']) ? '图片URL' : 'base64图片';

            return JsonServer::success('处理成功', [
                'file_path' => $resultPath,
                'file_url' => $fileUrl,
                'message' => "已成功使用{$signatureSource}在PDF文件中添加签名和日期"
            ]);

        } catch (Exception $e) {
            // 记录详细错误信息
            $signatureSource = !empty($post['signature_url']) ? '图片URL' : 'base64图片';
            Log::error("PDF处理错误(使用GD库，{$signatureSource}): " . $e->getMessage());
            Log::error('错误位置: ' . $e->getFile() . ':' . $e->getLine());
            Log::error('错误堆栈: ' . $e->getTraceAsString());

            // 清理临时文件
            if (isset($signaturePath) && file_exists($signaturePath)) {
                @unlink($signaturePath);
            }

            if (isset($outputPath) && file_exists($outputPath)) {
                @unlink($outputPath);
            }

            return JsonServer::error('处理PDF文件时出错: ' . $e->getMessage());
        }
    }

    /**
     * 使用FPDI库添加签名和日期到PDF文件
     * @return \think\response\Json
     */
    public function addSignatureWithFpdi()
    {
        try {
            // 获取请求参数
            $post = $this->request->post();

            // 检查必要参数
            if (empty($post['signature_base64']) && empty($post['signature_url'])) {
                return JsonServer::error('缺少签名图片数据或图片URL');
            }

            // 获取PDF文件路径
            $pdfPath = public_path() . 'images/share/zsxy.pdf';
            if (!file_exists($pdfPath)) {
                return JsonServer::error('PDF文件不存在');
            }

            // 准备临时目录
            $tempDir = runtime_path() . 'temp';
            // 确保temp目录存在
            if (!is_dir($tempDir)) {
                // 如果temp目录不存在，使用runtime目录
                $tempDir = runtime_path();
            }

            // 处理签名图片
            if (!empty($post['signature_url'])) {
                // 使用URL获取图片
                $signatureUrl = $post['signature_url'];

                // 从URL中提取文件扩展名
                $pathInfo = pathinfo($signatureUrl);
                $imageType = isset($pathInfo['extension']) ? strtolower($pathInfo['extension']) : 'jpg';

                // 确保扩展名是有效的图片类型
                if (!in_array($imageType, ['png', 'jpg', 'jpeg'])) {
                    $imageType = 'jpg'; // 默认使用jpg
                }

                // 保存签名图片到临时文件
                $signaturePath = $tempDir . '/' . uniqid() . '.' . $imageType;

                // 下载图片
                try {
                    // 使用curl下载图片（更可靠）
                    $ch = curl_init();
                    curl_setopt($ch, CURLOPT_URL, $signatureUrl);
                    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
                    curl_setopt($ch, CURLOPT_TIMEOUT, 30); // 增加超时时间
                    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
                    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true); // 允许重定向
                    $imageData = curl_exec($ch);
                    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                    curl_close($ch);

                    if (!$imageData || $httpCode != 200) {
                        // 如果curl失败，尝试使用file_get_contents
                        $imageData = @file_get_contents($signatureUrl);
                        if ($imageData === false) {
                            return JsonServer::error('无法从URL下载图片，HTTP状态码: ' . $httpCode);
                        }
                    }

                    // 保存图片
                    if (file_put_contents($signaturePath, $imageData) === false) {
                        return JsonServer::error('保存签名图片失败，可能是权限问题');
                    }

                    // 验证图片是否有效
                    if (!file_exists($signaturePath)) {
                        return JsonServer::error('保存后的签名图片文件不存在');
                    }

                    $fileSize = filesize($signaturePath);
                    if ($fileSize <= 0) {
                        return JsonServer::error('保存的签名图片文件为空，大小: ' . $fileSize);
                    }

                    // 验证图片格式
                    $imageInfo = @getimagesize($signaturePath);
                    if (!$imageInfo) {
                        return JsonServer::error('无法获取图片信息，可能不是有效的图片文件');
                    }

                    // 记录图片信息到日志
                    Log::info('签名图片信息: ' . json_encode($imageInfo));
                    Log::info('签名图片路径: ' . $signaturePath);
                    Log::info('签名图片大小: ' . $fileSize . ' 字节');

                } catch (Exception $e) {
                    return JsonServer::error('下载图片时出错: ' . $e->getMessage());
                }
            } else {
                // 使用base64数据
                $signatureBase64 = $post['signature_base64'];

                // 检查并提取base64数据
                if (preg_match('/^data:image\/(png|jpeg|jpg|gif);base64,/', $signatureBase64, $matches)) {
                    $imageType = $matches[1];
                    $signatureBase64 = preg_replace('/^data:image\/(png|jpeg|jpg|gif);base64,/', '', $signatureBase64);
                    $signatureBase64 = str_replace(' ', '+', $signatureBase64); // 修复可能的空格问题
                } else {
                    // 如果没有前缀，假设是纯base64数据
                    $imageType = 'jpg'; // 默认使用jpg
                }

                // 确保扩展名是有效的图片类型
                if (!in_array($imageType, ['png', 'jpg', 'jpeg'])) {
                    $imageType = 'jpg';
                }

                // 保存签名图片到临时文件
                $signaturePath = $tempDir . '/' . uniqid() . '.' . $imageType;

                // 解码并保存
                $imageData = base64_decode($signatureBase64);
                if (!$imageData) {
                    return JsonServer::error('无效的base64图片数据');
                }

                if (file_put_contents($signaturePath, $imageData) === false) {
                    return JsonServer::error('保存签名图片失败，可能是权限问题');
                }

                // 验证图片是否有效
                if (!file_exists($signaturePath)) {
                    return JsonServer::error('保存后的签名图片文件不存在');
                }

                $fileSize = filesize($signaturePath);
                if ($fileSize <= 0) {
                    return JsonServer::error('保存的签名图片文件为空，大小: ' . $fileSize);
                }

                // 验证图片格式
                $imageInfo = @getimagesize($signaturePath);
                if (!$imageInfo) {
                    return JsonServer::error('无法获取图片信息，可能不是有效的图片文件');
                }

                // 记录图片信息到日志
                Log::info('签名图片信息: ' . json_encode($imageInfo));
                Log::info('签名图片路径: ' . $signaturePath);
                Log::info('签名图片大小: ' . $fileSize . ' 字节');
            }

            // 设置输出PDF路径
            $baseOutputDir = public_path() . 'uploads/pdf';
            // 确保基本输出目录存在
            if (!is_dir($baseOutputDir)) {
                // 如果pdf目录不存在，使用uploads目录
                $baseOutputDir = public_path() . 'uploads';
                // 如果uploads目录也不存在，使用public目录
                if (!is_dir($baseOutputDir)) {
                    $baseOutputDir = public_path();
                }
            }
            $outputPath = $baseOutputDir . '/' . uniqid() . '.pdf';

            // 创建处理器实例
            $processor = new FpdiPdfProcessor($pdfPath);
            $processor->setSignaturePath($signaturePath)
                ->setOutputPath($outputPath);

            // 设置签名位置（根据实际PDF调整）
            // 注意：PDF坐标系原点在左下角，但我们的输入坐标是基于左上角的
            // 坐标转换在FpdiPdfProcessor类中处理
            $signatureX = $post['signature_x'] ?? 50;
            $signatureY = $post['signature_y'] ?? 50;
            $signatureWidth = $post['signature_width'] ?? 80;
            $signatureHeight = $post['signature_height'] ?? 40;
            $processor->setSignaturePosition($signatureX, $signatureY, $signatureWidth, $signatureHeight);

            // 设置日期位置（根据实际PDF调整）
            $dateX = $post['date_x'] ?? 50;
            $dateY = $post['date_y'] ?? 100;
            $processor->setDatePosition($dateX, $dateY);

            // 处理PDF
            $pageNumber = $post['page_number'] ?? 1;
            // 使用简单的日期格式，避免中文乱码问题
            $dateFormat = $post['date_format'] ?? 'Y-m-d';

            // 记录处理前的配置信息
            Log::info('PDF处理配置: 页码=' . $pageNumber . ', 日期格式=' . $dateFormat);
            Log::info('签名位置: X=' . $signatureX . ', Y=' . $signatureY . ', 宽=' . $signatureWidth . ', 高=' . $signatureHeight);
            Log::info('日期位置: X=' . $dateX . ', Y=' . $dateY);

            // 处理PDF并获取结果路径
            $resultPath = $processor->process($pageNumber, $dateFormat);

            // 检查结果路径是否为OSS路径
            $isOssPath = (strpos($resultPath, 'http://') === 0 || strpos($resultPath, 'https://') === 0);
            if (!$isOssPath) {
                Log::info('处理后的文件路径是本地路径，将通过UrlServer获取完整URL');
            } else {
                Log::info('处理后的文件路径已经是OSS路径: ' . $resultPath);
            }

            // 清理临时文件
            @unlink($signaturePath);

            // 检查结果路径是否为OSS路径
            $isOssPath = (strpos($resultPath, 'http://') === 0 || strpos($resultPath, 'https://') === 0);
            if ($isOssPath) {
                // 如果是OSS路径，直接使用
                $fileUrl = $resultPath;
                Log::info('使用OSS路径作为文件URL: ' . $fileUrl);
            } else {
                // 否则，使用UrlServer获取文件URL
                $fileUrl = UrlServer::getFileUrl(str_replace(public_path(), '', $resultPath));
                Log::info('使用UrlServer获取文件URL: ' . $fileUrl);
            }

            // 确定使用的签名来源
            $signatureSource = !empty($post['signature_url']) ? '图片URL' : 'base64图片';

            return JsonServer::success('处理成功', [
                'file_path' => $resultPath,
                'file_url' => $fileUrl,
                'message' => "已成功使用{$signatureSource}在PDF文件中添加签名和日期"
            ]);

        } catch (Exception $e) {
            // 记录详细错误信息
            $signatureSource = !empty($post['signature_url']) ? '图片URL' : 'base64图片';
            Log::error("PDF处理错误(使用FPDI库，{$signatureSource}): " . $e->getMessage());
            Log::error('错误位置: ' . $e->getFile() . ':' . $e->getLine());
            Log::error('错误堆栈: ' . $e->getTraceAsString());

            // 清理临时文件
            if (isset($signaturePath) && file_exists($signaturePath)) {
                @unlink($signaturePath);
            }

            if (isset($outputPath) && file_exists($outputPath)) {
                @unlink($outputPath);
            }

            return JsonServer::error('处理PDF文件时出错: ' . $e->getMessage());
        }
    }

    /**
     * 使用简单方法添加签名和日期到PDF文件
     * @return \think\response\Json
     */
    public function addSignatureSimple()
    {
        try {
            // 获取请求参数
            $post = $this->request->post();

            // 检查必要参数
            if (empty($post['signature_base64']) && empty($post['signature_url'])) {
                return JsonServer::error('缺少签名图片数据或图片URL');
            }

            // 获取PDF文件路径
            $pdfPath = public_path() . 'images/share/zsxy.pdf';
            if (!file_exists($pdfPath)) {
                return JsonServer::error('PDF文件不存在');
            }

            // 准备临时目录
            $tempDir = runtime_path() . 'temp';
            // 确保temp目录存在
            if (!is_dir($tempDir)) {
                // 如果temp目录不存在，使用runtime目录
                $tempDir = runtime_path();
            }

            // 处理签名图片
            if (!empty($post['signature_url'])) {
                // 使用URL获取图片
                $signatureUrl = $post['signature_url'];

                // 从URL中提取文件扩展名
                $pathInfo = pathinfo($signatureUrl);
                $imageType = isset($pathInfo['extension']) ? strtolower($pathInfo['extension']) : 'jpg';

                // 确保扩展名是有效的图片类型
                if (!in_array($imageType, ['png', 'jpg', 'jpeg'])) {
                    $imageType = 'jpg'; // 默认使用jpg
                }

                // 保存签名图片到临时文件
                $signaturePath = $tempDir . '/' . uniqid() . '.' . $imageType;

                // 下载图片
                try {
                    // 使用curl下载图片（更可靠）
                    $ch = curl_init();
                    curl_setopt($ch, CURLOPT_URL, $signatureUrl);
                    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
                    curl_setopt($ch, CURLOPT_TIMEOUT, 30); // 增加超时时间
                    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
                    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true); // 允许重定向
                    $imageData = curl_exec($ch);
                    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                    curl_close($ch);

                    if (!$imageData || $httpCode != 200) {
                        // 如果curl失败，尝试使用file_get_contents
                        $imageData = @file_get_contents($signatureUrl);
                        if ($imageData === false) {
                            return JsonServer::error('无法从URL下载图片，HTTP状态码: ' . $httpCode);
                        }
                    }

                    // 保存图片
                    if (file_put_contents($signaturePath, $imageData) === false) {
                        return JsonServer::error('保存签名图片失败，可能是权限问题');
                    }

                    // 验证图片是否有效
                    if (!file_exists($signaturePath)) {
                        return JsonServer::error('保存后的签名图片文件不存在');
                    }

                    $fileSize = filesize($signaturePath);
                    if ($fileSize <= 0) {
                        return JsonServer::error('保存的签名图片文件为空，大小: ' . $fileSize);
                    }

                    // 验证图片格式
                    $imageInfo = @getimagesize($signaturePath);
                    if (!$imageInfo) {
                        return JsonServer::error('无法获取图片信息，可能不是有效的图片文件');
                    }

                    // 记录图片信息到日志
                    Log::info('签名图片信息: ' . json_encode($imageInfo));
                    Log::info('签名图片路径: ' . $signaturePath);
                    Log::info('签名图片大小: ' . $fileSize . ' 字节');

                } catch (Exception $e) {
                    return JsonServer::error('下载图片时出错: ' . $e->getMessage());
                }
            } else {
                // 使用base64数据
                $signatureBase64 = $post['signature_base64'];

                // 检查并提取base64数据
                if (preg_match('/^data:image\/(png|jpeg|jpg|gif);base64,/', $signatureBase64, $matches)) {
                    $imageType = $matches[1];
                    $signatureBase64 = preg_replace('/^data:image\/(png|jpeg|jpg|gif);base64,/', '', $signatureBase64);
                    $signatureBase64 = str_replace(' ', '+', $signatureBase64); // 修复可能的空格问题
                } else {
                    // 如果没有前缀，假设是纯base64数据
                    $imageType = 'jpg'; // 默认使用jpg
                }

                // 确保扩展名是有效的图片类型
                if (!in_array($imageType, ['png', 'jpg', 'jpeg'])) {
                    $imageType = 'jpg';
                }

                // 保存签名图片到临时文件
                $signaturePath = $tempDir . '/' . uniqid() . '.' . $imageType;

                // 解码并保存
                $imageData = base64_decode($signatureBase64);
                if (!$imageData) {
                    return JsonServer::error('无效的base64图片数据');
                }

                if (file_put_contents($signaturePath, $imageData) === false) {
                    return JsonServer::error('保存签名图片失败，可能是权限问题');
                }

                // 验证图片是否有效
                if (!file_exists($signaturePath)) {
                    return JsonServer::error('保存后的签名图片文件不存在');
                }

                $fileSize = filesize($signaturePath);
                if ($fileSize <= 0) {
                    return JsonServer::error('保存的签名图片文件为空，大小: ' . $fileSize);
                }

                // 验证图片格式
                $imageInfo = @getimagesize($signaturePath);
                if (!$imageInfo) {
                    return JsonServer::error('无法获取图片信息，可能不是有效的图片文件');
                }

                // 记录图片信息到日志
                Log::info('签名图片信息: ' . json_encode($imageInfo));
                Log::info('签名图片路径: ' . $signaturePath);
                Log::info('签名图片大小: ' . $fileSize . ' 字节');
            }

            // 设置输出PDF路径
            $baseOutputDir = public_path() . 'uploads/pdf';
            // 确保基本输出目录存在
            if (!is_dir($baseOutputDir)) {
                // 如果pdf目录不存在，使用uploads目录
                $baseOutputDir = public_path() . 'uploads';
                // 如果uploads目录也不存在，使用public目录
                if (!is_dir($baseOutputDir)) {
                    $baseOutputDir = public_path();
                }
            }
            $outputPath = $baseOutputDir . '/' . uniqid() . '.pdf';

            // 创建处理器实例
            $processor = new SimplePdfEditor($pdfPath);
            $processor->setSignaturePath($signaturePath)
                ->setOutputPath($outputPath);

            // 设置签名位置（根据实际PDF调整）
            $signatureX = $post['signature_x'] ?? 50;
            $signatureY = $post['signature_y'] ?? 50;
            $signatureWidth = $post['signature_width'] ?? 80;
            $signatureHeight = $post['signature_height'] ?? 40;
            $processor->setSignaturePosition($signatureX, $signatureY, $signatureWidth, $signatureHeight);

            // 设置日期位置（根据实际PDF调整）
            $dateX = $post['date_x'] ?? 50;
            $dateY = $post['date_y'] ?? 100;
            $processor->setDatePosition($dateX, $dateY);

            // 处理PDF
            $pageNumber = $post['page_number'] ?? 1;
            // 使用简单的日期格式，避免中文乱码问题
            $dateFormat = $post['date_format'] ?? 'Y-m-d';
            $resultPath = $processor->process($pageNumber, $dateFormat);

            // 清理临时文件
            @unlink($signaturePath);

            // 检查结果路径是否为OSS路径
            $isOssPath = (strpos($resultPath, 'http://') === 0 || strpos($resultPath, 'https://') === 0);
            if ($isOssPath) {
                // 如果是OSS路径，直接使用
                $fileUrl = $resultPath;
                Log::info('使用OSS路径作为文件URL: ' . $fileUrl);
            } else {
                // 否则，使用UrlServer获取文件URL
                $fileUrl = UrlServer::getFileUrl(str_replace(public_path(), '', $resultPath));
                Log::info('使用UrlServer获取文件URL: ' . $fileUrl);
            }

            // 确定使用的签名来源
            $signatureSource = !empty($post['signature_url']) ? '图片URL' : 'base64图片';

            return JsonServer::success('处理成功', [
                'file_path' => $resultPath,
                'file_url' => $fileUrl,
                'message' => "已成功使用{$signatureSource}在PDF文件中添加签名和日期"
            ]);

        } catch (Exception $e) {
            // 记录详细错误信息
            $signatureSource = !empty($post['signature_url']) ? '图片URL' : 'base64图片';
            Log::error("PDF处理错误(使用简单方法，{$signatureSource}): " . $e->getMessage());
            Log::error('错误位置: ' . $e->getFile() . ':' . $e->getLine());
            Log::error('错误堆栈: ' . $e->getTraceAsString());

            // 清理临时文件
            if (isset($signaturePath) && file_exists($signaturePath)) {
                @unlink($signaturePath);
            }

            if (isset($outputPath) && file_exists($outputPath)) {
                @unlink($outputPath);
            }

            return JsonServer::error('处理PDF文件时出错: ' . $e->getMessage());
        }
    }
}