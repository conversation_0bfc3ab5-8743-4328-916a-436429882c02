<?php

namespace app\listener;

use app\common\logic\OrderRefundLogic;
use app\event\OrderRefundEvent;
use think\facade\Log;

class OrderRefundListener
{
    /**
     * @notes 异步处理订单退款
     * @param OrderRefundEvent $event
     */
    public function handle(OrderRefundEvent $event)
    {
        $order = $event->order;
        try {
            OrderRefundLogic::refund($order, $order['order_amount'], $order['order_amount']);
        } catch (\Exception $e) {
            // 记录错误日志
            Log::error('异步退款失败，订单ID：' . $order['id'] . '，错误信息：' . $e->getMessage());
            // 这里可以添加更多的错误处理逻辑，例如失败重试
        }
    }
}