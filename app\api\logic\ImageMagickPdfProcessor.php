<?php
namespace app\api\logic;

use app\common\basics\Logic;
use Exception;
use think\facade\Log;

/**
 * 使用ImageMagick处理PDF文件
 * 用于在PDF文件中添加签名和日期
 * Class ImageMagickPdfProcessor
 * @package app\api\logic
 */
class ImageMagickPdfProcessor extends Logic
{
    /**
     * @var string PDF文件路径
     */
    protected $pdfPath;
    
    /**
     * @var string 签名图片路径
     */
    protected $signaturePath;
    
    /**
     * @var string 输出PDF路径
     */
    protected $outputPath;
    
    /**
     * @var array 签名位置坐标
     */
    protected $signaturePosition = [
        'x' => 400,  // 默认X坐标
        'y' => 680,  // 默认Y坐标
        'width' => 80, // 默认宽度
        'height' => 40 // 默认高度
    ];
    
    /**
     * @var array 日期位置坐标
     */
    protected $datePosition = [
        'x' => 400,  // 默认X坐标
        'y' => 720,  // 默认Y坐标
    ];
    
    /**
     * 构造函数
     * @param string $pdfPath PDF文件路径
     */
    public function __construct($pdfPath = '')
    {
        if (!empty($pdfPath)) {
            $this->setPdfPath($pdfPath);
        }
    }
    
    /**
     * 设置PDF文件路径
     * @param string $pdfPath
     * @return $this
     * @throws Exception
     */
    public function setPdfPath($pdfPath)
    {
        if (!file_exists($pdfPath)) {
            throw new Exception('PDF文件不存在: ' . $pdfPath);
        }
        $this->pdfPath = $pdfPath;
        return $this;
    }
    
    /**
     * 设置签名图片路径
     * @param string $signaturePath
     * @return $this
     * @throws Exception
     */
    public function setSignaturePath($signaturePath)
    {
        if (!file_exists($signaturePath)) {
            throw new Exception('签名图片不存在: ' . $signaturePath);
        }
        $this->signaturePath = $signaturePath;
        return $this;
    }
    
    /**
     * 设置输出PDF路径
     * @param string $outputPath
     * @return $this
     */
    public function setOutputPath($outputPath)
    {
        $this->outputPath = $outputPath;
        return $this;
    }
    
    /**
     * 设置签名位置
     * @param float $x X坐标
     * @param float $y Y坐标
     * @param float|null $width 宽度
     * @param float|null $height 高度
     * @return $this
     */
    public function setSignaturePosition($x, $y, $width = null, $height = null)
    {
        $this->signaturePosition['x'] = $x;
        $this->signaturePosition['y'] = $y;
        
        if ($width !== null) {
            $this->signaturePosition['width'] = $width;
        }
        
        if ($height !== null) {
            $this->signaturePosition['height'] = $height;
        }
        
        return $this;
    }
    
    /**
     * 设置日期位置
     * @param float $x X坐标
     * @param float $y Y坐标
     * @return $this
     */
    public function setDatePosition($x, $y)
    {
        $this->datePosition['x'] = $x;
        $this->datePosition['y'] = $y;
        return $this;
    }
    
    /**
     * 处理PDF文件，添加签名和日期
     * @param int $pageNumber 页码，从1开始
     * @param string $dateFormat 日期格式，默认为Y-m-d
     * @return string 处理后的PDF文件路径
     * @throws Exception
     */
    public function process($pageNumber = 1, $dateFormat = 'Y-m-d')
    {
        // 检查必要参数
        if (empty($this->pdfPath)) {
            throw new Exception('未设置PDF文件路径');
        }
        
        if (empty($this->signaturePath)) {
            throw new Exception('未设置签名图片路径');
        }
        
        if (empty($this->outputPath)) {
            // 如果未设置输出路径，则生成一个临时路径
            $pathInfo = pathinfo($this->pdfPath);
            $this->outputPath = $pathInfo['dirname'] . '/' . $pathInfo['filename'] . '_signed.' . $pathInfo['extension'];
        }
        
        try {
            // 创建临时目录
            $tempDir = dirname($this->outputPath) . '/temp_' . uniqid();
            if (!is_dir($tempDir)) {
                mkdir($tempDir, 0777, true);
            }
            
            // 使用pdftk分解PDF文件为单页
            $command = "pdftk {$this->pdfPath} burst output {$tempDir}/page_%04d.pdf";
            exec($command, $output, $returnVar);
            
            if ($returnVar !== 0) {
                throw new Exception('分解PDF文件失败');
            }
            
            // 获取要处理的页面文件
            $pageFile = $tempDir . '/page_' . sprintf('%04d', $pageNumber) . '.pdf';
            if (!file_exists($pageFile)) {
                throw new Exception('指定的页面不存在: ' . $pageNumber);
            }
            
            // 创建一个包含签名和日期的图片
            $overlayImage = $tempDir . '/overlay.png';
            
            // 获取PDF页面尺寸
            $command = "identify -format \"%w %h\" {$pageFile}[0]";
            exec($command, $sizeOutput, $returnVar);
            
            if ($returnVar !== 0 || empty($sizeOutput)) {
                throw new Exception('获取PDF页面尺寸失败');
            }
            
            list($width, $height) = explode(' ', $sizeOutput[0]);
            
            // 创建透明背景的图片
            $command = "convert -size {$width}x{$height} xc:transparent";
            
            // 添加签名图片
            $command .= " \( {$this->signaturePath} -resize {$this->signaturePosition['width']}x{$this->signaturePosition['height']} \) -geometry +{$this->signaturePosition['x']}+{$this->signaturePosition['y']} -composite";
            
            // 添加日期文本
            $date = date($dateFormat);
            $command .= " -font Arial -pointsize 12 -fill black -draw \"text {$this->datePosition['x']},{$this->datePosition['y']} '{$date}'\" {$overlayImage}";
            
            exec($command, $output, $returnVar);
            
            if ($returnVar !== 0 || !file_exists($overlayImage)) {
                throw new Exception('创建叠加图片失败');
            }
            
            // 将叠加图片合并到PDF页面
            $modifiedPage = $tempDir . '/modified_page.pdf';
            $command = "convert {$pageFile} {$overlayImage} -flatten {$modifiedPage}";
            exec($command, $output, $returnVar);
            
            if ($returnVar !== 0 || !file_exists($modifiedPage)) {
                throw new Exception('合并图片到PDF页面失败');
            }
            
            // 替换原始PDF中的页面
            $command = "pdftk {$this->pdfPath} cat 1-" . ($pageNumber-1) . " output {$tempDir}/part1.pdf";
            if ($pageNumber > 1) {
                exec($command, $output, $returnVar);
                if ($returnVar !== 0) {
                    throw new Exception('分割PDF文件失败 (part1)');
                }
            }
            
            $command = "pdftk {$this->pdfPath} cat " . ($pageNumber+1) . "-end output {$tempDir}/part2.pdf";
            $pageCount = $this->getPdfPageCount($this->pdfPath);
            if ($pageNumber < $pageCount) {
                exec($command, $output, $returnVar);
                if ($returnVar !== 0) {
                    throw new Exception('分割PDF文件失败 (part2)');
                }
            }
            
            // 合并所有部分
            $parts = [];
            if ($pageNumber > 1 && file_exists($tempDir . '/part1.pdf')) {
                $parts[] = $tempDir . '/part1.pdf';
            }
            $parts[] = $modifiedPage;
            if ($pageNumber < $pageCount && file_exists($tempDir . '/part2.pdf')) {
                $parts[] = $tempDir . '/part2.pdf';
            }
            
            $command = "pdftk " . implode(' ', $parts) . " cat output {$this->outputPath}";
            exec($command, $output, $returnVar);
            
            if ($returnVar !== 0 || !file_exists($this->outputPath)) {
                throw new Exception('合并PDF文件失败');
            }
            
            // 清理临时文件
            $this->cleanupTempDir($tempDir);
            
            return $this->outputPath;
        } catch (Exception $e) {
            // 清理临时文件
            if (isset($tempDir) && is_dir($tempDir)) {
                $this->cleanupTempDir($tempDir);
            }
            
            throw new Exception('处理PDF文件时出错: ' . $e->getMessage());
        }
    }
    
    /**
     * 获取PDF文件的页数
     * @param string $pdfPath
     * @return int
     * @throws Exception
     */
    protected function getPdfPageCount($pdfPath)
    {
        $command = "pdfinfo {$pdfPath} | grep Pages | awk '{print $2}'";
        exec($command, $output, $returnVar);
        
        if ($returnVar !== 0 || empty($output)) {
            throw new Exception('获取PDF页数失败');
        }
        
        return (int)$output[0];
    }
    
    /**
     * 清理临时目录
     * @param string $tempDir
     */
    protected function cleanupTempDir($tempDir)
    {
        if (is_dir($tempDir)) {
            $files = glob($tempDir . '/*');
            foreach ($files as $file) {
                if (is_file($file)) {
                    @unlink($file);
                }
            }
            @rmdir($tempDir);
        }
    }
}
