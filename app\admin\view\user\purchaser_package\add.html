{layout name="layout2" /}

<div class="layui-form" lay-filter="layuiadmin-form-admin" id="layuiadmin-form-admin" style="padding: 20px 30px 0 0;">
    <div class="layui-form-item">
        <label class="layui-form-label">套餐名称</label>
        <div class="layui-input-block">
            <input type="text" name="name" lay-verify="required" placeholder="请输入套餐名称" autocomplete="off" class="layui-input">
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">分配人数</label>
        <div class="layui-input-block">
            <input type="number" name="purchaser_count" lay-verify="required|number" placeholder="请输入分配人数" autocomplete="off" class="layui-input">
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">套餐价格</label>
        <div class="layui-input-block">
            <input type="number" name="price" lay-verify="required|number" placeholder="请输入套餐价格" autocomplete="off" class="layui-input">
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">排序</label>
        <div class="layui-input-block">
            <input type="number" name="sort" lay-verify="required|number" placeholder="请输入排序" autocomplete="off" class="layui-input" value="100">
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">状态</label>
        <div class="layui-input-block">
            <input type="radio" name="status" value="1" title="启用" checked>
            <input type="radio" name="status" value="0" title="禁用">
        </div>
    </div>
    <div class="layui-form-item layui-hide">
        <input type="button" lay-submit lay-filter="addSubmit" id="addSubmit" value="确认">
    </div>
</div>

<script>
    layui.use(['form'],function () {
        var form = layui.form;
        form.render();
    })
</script>
