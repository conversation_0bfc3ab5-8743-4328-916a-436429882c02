<?php

namespace app\shop\controller;

use app\common\basics\ShopBase;
use app\common\server\JsonServer;
use think\facade\Db;

/**
 * 商家功能图标配置管理
 * Class IconConfig
 * @package app\shop\controller
 */
class IconConfig extends ShopBase
{
    /**
     * @notes 图标配置列表页面
     * @return \think\response\View
     */
    public function index()
    {
        return view();
    }

    /**
     * @notes 获取图标配置列表
     * @return \think\response\Json
     */
    public function lists()
    {
        $page = $this->request->get('page', 1);
        $limit = $this->request->get('limit', 15);
        $shop_id = $this->shop_id;

        $where = [];
        // 获取全局配置和当前商家的配置
        $where[] = ['shop_id', 'in', [0, $shop_id]];

        $count = Db::name('shop_icon_config')->where($where)->count();
        
        $list = Db::name('shop_icon_config')
            ->where($where)
            ->order('sort_order ASC, id ASC')
            ->page($page, $limit)
            ->select()
            ->toArray();

        // 关联权限名称
        foreach ($list as &$item) {
            $auth_info = Db::name('dev_shop_auth')->where('id', $item['auth_id'])->find();
            $item['auth_name'] = $auth_info['name'] ?? '未知权限';
            $item['scope'] = $item['shop_id'] == 0 ? '全局' : '当前商家';
        }

        return JsonServer::success('', [
            'count' => $count,
            'data' => $list
        ]);
    }

    /**
     * @notes 添加图标配置
     * @return \think\response\Json
     */
    public function add()
    {
        if ($this->request->isPost()) {
            $post = $this->request->post();
            
            // 验证必填字段
            if (empty($post['icon_name']) || empty($post['icon_url']) || empty($post['icon_path'])) {
                return JsonServer::error('请填写完整的图标信息');
            }

            $data = [
                'shop_id' => $this->shop_id,
                'icon_url' => $post['icon_url'],
                'icon_name' => $post['icon_name'],
                'icon_path' => $post['icon_path'],
                'auth_id' => $post['auth_id'] ?? 0,
                'sort_order' => $post['sort_order'] ?? 50,
                'status' => $post['status'] ?? 1,
                'created_at' => time(),
                'updated_at' => time()
            ];

            $result = Db::name('shop_icon_config')->insert($data);
            
            if ($result) {
                return JsonServer::success('添加成功');
            } else {
                return JsonServer::error('添加失败');
            }
        }

        // 获取权限列表
        $auth_list = Db::name('dev_shop_auth')
            ->where('disable', 0)
            ->where('del', 0)
            ->field('id,name,pid')
            ->order('sort ASC')
            ->select()
            ->toArray();

        return view('add', ['auth_list' => $auth_list]);
    }

    /**
     * @notes 编辑图标配置
     * @return \think\response\Json
     */
    public function edit()
    {
        $id = $this->request->param('id');
        
        if ($this->request->isPost()) {
            $post = $this->request->post();
            
            // 验证必填字段
            if (empty($post['icon_name']) || empty($post['icon_url']) || empty($post['icon_path'])) {
                return JsonServer::error('请填写完整的图标信息');
            }

            $data = [
                'icon_url' => $post['icon_url'],
                'icon_name' => $post['icon_name'],
                'icon_path' => $post['icon_path'],
                'auth_id' => $post['auth_id'] ?? 0,
                'sort_order' => $post['sort_order'] ?? 50,
                'status' => $post['status'] ?? 1,
                'updated_at' => time()
            ];

            $result = Db::name('shop_icon_config')
                ->where('id', $id)
                ->where('shop_id', $this->shop_id) // 只能编辑自己商家的配置
                ->update($data);
            
            if ($result !== false) {
                return JsonServer::success('更新成功');
            } else {
                return JsonServer::error('更新失败');
            }
        }

        // 获取配置详情
        $detail = Db::name('shop_icon_config')
            ->where('id', $id)
            ->where('shop_id', $this->shop_id)
            ->find();
            
        if (empty($detail)) {
            return JsonServer::error('配置不存在');
        }

        // 获取权限列表
        $auth_list = Db::name('dev_shop_auth')
            ->where('disable', 0)
            ->where('del', 0)
            ->field('id,name,pid')
            ->order('sort ASC')
            ->select()
            ->toArray();

        return view('edit', ['detail' => $detail, 'auth_list' => $auth_list]);
    }

    /**
     * @notes 删除图标配置
     * @return \think\response\Json
     */
    public function del()
    {
        $id = $this->request->param('id');
        
        $result = Db::name('shop_icon_config')
            ->where('id', $id)
            ->where('shop_id', $this->shop_id) // 只能删除自己商家的配置
            ->delete();
        
        if ($result) {
            return JsonServer::success('删除成功');
        } else {
            return JsonServer::error('删除失败');
        }
    }

    /**
     * @notes 更新状态
     * @return \think\response\Json
     */
    public function status()
    {
        $id = $this->request->param('id');
        $status = $this->request->param('status', 1);
        
        $result = Db::name('shop_icon_config')
            ->where('id', $id)
            ->where('shop_id', $this->shop_id)
            ->update(['status' => $status, 'updated_at' => time()]);
        
        if ($result !== false) {
            return JsonServer::success('状态更新成功');
        } else {
            return JsonServer::error('状态更新失败');
        }
    }
}
