<?php
namespace app\common\model\agent;

use app\common\basics\Models;
use app\common\enum\PayEnum;
use app\common\model\Client_;
use app\common\model\Pay;
use app\common\model\shop\Shop;
use app\common\model\user\User;
use app\common\model\order\Order;
use app\common\model\order\OrderGoods;
use app\common\server\ConfigServer;
use think\facade\Db;

class AgentRelationship extends Models
{
    // 设置当前模型对应的完整数据表名称
    protected $table = 'ls_agent_relationships';

    // 设置主键名
    protected $pk = 'id';

    // 关联用户
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    // 关联上级代理
    public function sponsor()
    {
        return $this->belongsTo(User::class, 'sponsor_id', 'id');
    }
}