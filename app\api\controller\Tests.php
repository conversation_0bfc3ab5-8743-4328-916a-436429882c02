<?php
namespace app\api\controller;

use app\common\basics\Api;
use app\api\logic\GoodsLogic;
use app\common\server\JsonServer;
use app\common\logic\PayNotifyLogic;
use think\facade\Db;
use think\facade\Log;

class Tests extends Api
{
    public $like_not_need_login =  ['agentTest'];
    /**
     * 原始测试方法
     */
    public function test()
    {
        $goodsId = 1;
        $goodsDetail = GoodsLogic::getGoodsDetail($goodsId, $this->user_id);
        return JsonServer::success('获取商品详情成功', $goodsDetail);
    }

    /**
     * 代理系统测试入口
     * 1. 创建500个用户，其中10个为代理
     * 2. 模拟代理保证金支付回调
     * 3. 随机分配用户到代理名下
     */
    public function agentTest()
    {
        try {
            // 创建用户和代理
            $result = $this->createUsersAndAgents();

            // 模拟代理保证金支付回调
            $this->simulateAgentDepositPayCallback($result['agents']);

            // 随机分配用户到代理名下
            $this->assignUsersToAgents($result['users'], $result['agents']);

            return JsonServer::success('代理系统测试完成', [
                'total_users' => count($result['users']),
                'total_agents' => count($result['agents']),
                'agent_details' => $result['agents']
            ]);
        } catch (\Exception $e) {
            Log::error('代理系统测试失败: ' . $e->getMessage());
            return JsonServer::error('代理系统测试失败: ' . $e->getMessage());
        }
    }

    /**
     * 创建500个用户，其中10个为代理
     * @return array 返回创建的用户和代理信息
     */
    private function createUsersAndAgents()
    {
        // 电影明星名字列表
        $starNames = [
            '成龙', '李连杰', '甄子丹', '周星驰', '刘德华', '梁朝伟', '周润发', '李小龙', '吴京', '黄渤',
            '姜文', '葛优', '冯小刚', '张国荣', '郭富城', '梁家辉', '张学友', '黎明', '古天乐', '张家辉',
            '刘青云', '吴镇宇', '张涵予', '彭于晏', '陈坤', '胡歌', '邓超', '王宝强', '刘烨', '陈道明',
            '章子怡', '巩俐', '李冰冰', '范冰冰', '周迅', '刘亦菲', '杨幂', '赵薇', '舒淇', '张曼玉',
            '林青霞', '王祖贤', '张柏芝', '李嘉欣', '袁咏仪', '关之琳', '钟楚红', '张艾嘉', '林志玲', '徐若瑄',
            '汤姆·汉克斯', '莱昂纳多·迪卡普里奥', '布拉德·皮特', '罗伯特·唐尼', '约翰尼·德普', '威尔·史密斯', '基努·里维斯', '汤姆·克鲁斯', '丹泽尔·华盛顿', '摩根·弗里曼',
            '安吉丽娜·朱莉', '斯嘉丽·约翰逊', '詹妮弗·劳伦斯', '艾玛·沃特森', '妮可·基德曼', '凯特·布兰切特', '朱莉娅·罗伯茨', '梅丽尔·斯特里普', '娜塔莉·波特曼', '安妮·海瑟薇',
            '成东镒', '宋康昊', '李秉宪', '河正宇', '孔侑', '全智贤', '裴勇俊', '李敏镐', '金秀贤', '朴信惠',
            '木村拓哉', '山下智久', '福山雅治', '二宫和也', '松本润', '北川景子', '新垣结衣', '石原里美', '长泽雅美', '绫濑遥',
            '阿米尔·汗', '沙鲁克·汗', '赫里尼克·罗斯汉', '拉吉尼坎塔', '艾西瓦娅·雷', '迪皮卡·帕度柯妮', '卡特莉娜·卡芙', '普丽扬卡·乔普拉', '安努舒卡·莎玛', '卡琳娜·卡普尔',
        ];

        // 随机选择10个作为代理
        $agentNames = array_rand(array_flip($starNames), 10);
        $normalUserNames = array_diff($starNames, $agentNames);

        // 创建10个代理用户
        $agents = [];
        foreach ($agentNames as $name) {
            $userId = $this->createUser($name, true);
            $agents[] = [
                'user_id' => $userId,
                'name' => $name
            ];
        }

        // 创建490个普通用户
        $users = [];
        $count = 0;
        foreach ($normalUserNames as $name) {
            if ($count >= 490) break; // 确保总数为500

            $userId = $this->createUser($name, false);
            $users[] = [
                'user_id' => $userId,
                'name' => $name
            ];
            $count++;
        }

        return [
            'users' => $users,
            'agents' => $agents
        ];
    }

    /**
     * 创建单个用户
     * @param string $name 用户名
     * @param bool $isAgent 是否为代理
     * @return int 用户ID
     */
    private function createUser($name, $isAgent = false)
    {
        // 生成随机手机号
        $mobile = '1' . rand(3, 9) . rand(1000000, 9999999);

        // 创建用户数据
        $userData = [
            'nickname' => $name,
            'avatar' => '/static/common/image/default_avatar.png',
            'mobile' => $mobile,
            'account' => $mobile,
            'password' => md5('123456'),
            'sex' => rand(0, 2),
            'birthday' => strtotime('-' . rand(18, 60) . ' years'),
            'user_money' => rand(0, 10000) / 100,
            'user_integral' => rand(0, 1000),
            'is_agent' => $isAgent ? 1 : 0,
            'client' => 1,
            'create_time' => time(),
            'update_time' => time()
        ];

        // 插入用户数据
        return Db::name('user')->insertGetId($userData);
    }

    /**
     * 模拟代理保证金支付回调
     * @param array $agents 代理信息
     */
    private function simulateAgentDepositPayCallback($agents)
    {
        foreach ($agents as $agent) {
            // 创建代理保证金订单
            $orderSn = 'AG' . date('YmdHis') . rand(1000, 9999);
            $depositData = [
                'user_id' => $agent['user_id'],
                'order_sn' => $orderSn,
                'amount' => 100.00, // 保证金金额100元
                'payment_date' => 0,
                'status' => 0, // 未支付
                'created_at' => date('Y-m-d H:i:s')
            ];

            // 插入代理保证金订单
            Db::name('agent_merchantfees')->insertGetId($depositData);

            // 模拟支付回调
            $extra = [
                'transaction_id' => 'WX' . date('YmdHis') . rand(10000, 99999)
            ];

            // 调用支付回调逻辑
            try {
                PayNotifyLogic::handle('agent_merchantfees', $orderSn, $extra);

                // 更新用户为代理
                Db::name('user')->where('id', $agent['user_id'])->update([
                    'is_agent' => 1,
                    'agent_id' => $agent['user_id']
                ]);

                // 创建代理表记录
                $agentData = [
                    'user_id' => $agent['user_id'],
                    'area_id' => 0, // 默认区域ID
                    'is_type' => 0, // 非区域代理
                    'is_freeze' => 0, // 未冻结
                    'remark' => '自动测试创建的代理',
                    'level' => 1, // 一级代理
                    'parent_id' => 0, // 无上级
                    'distribution_start_time' => time(),
                    'distribution_end_time' => time() + 365 * 86400, // 一年有效期
                    'agent_code' => 'AG' . str_pad(rand(1, 99999), 5, '0', STR_PAD_LEFT),
                    'create_time' => time(),
                    'buy_type' => '测试创建'
                ];

                $agentId = Db::name('agent')->insertGetId($agentData);

                Log::info("代理 {$agent['name']} (ID: {$agent['user_id']}, 代理ID: {$agentId}) 保证金支付成功");
            } catch (\Exception $e) {
                Log::error("代理 {$agent['name']} (ID: {$agent['user_id']}) 保证金支付失败: " . $e->getMessage());
                // 继续处理下一个代理，不中断流程
            }
        }
    }

    /**
     * 随机分配用户到代理名下
     * @param array $users 普通用户列表
     * @param array $agents 代理列表
     */
    private function assignUsersToAgents($users, $agents)
    {
        // 随机分配用户到代理名下
        foreach ($users as $user) {
            // 随机选择一个代理
            $randomAgent = $agents[array_rand($agents)];

            // 获取代理ID
            $agentRecord = Db::name('agent')->where('user_id', $randomAgent['user_id'])->find();
            if (!$agentRecord) {
                Log::error("代理 {$randomAgent['name']} (ID: {$randomAgent['user_id']}) 在代理表中不存在，跳过分配");
                continue;
            }

            // 创建代理关系
            $relationData = [
                'user_id' => $user['user_id'],
                'agent_id' => $agentRecord['id'], // 使用代理表的ID
                'sponsor_id' => $randomAgent['user_id'], // 使用代理用户的ID
                'created_at' => date('Y-m-d H:i:s')
            ];

            // 插入代理关系
            Db::name('agent_relationships')->insert($relationData);

            Log::info("用户 {$user['name']} (ID: {$user['user_id']}) 已分配给代理 {$randomAgent['name']} (ID: {$randomAgent['user_id']}, 代理ID: {$agentRecord['id']})");
        }
    }

    /**
     * 模拟代理保证金支付回调（单独测试）
     */
    public function simulateAgentPayCallback()
    {
        $userId = input('user_id', 0, 'intval');
        if (!$userId) {
            return JsonServer::error('请提供用户ID');
        }

        // 检查用户是否存在
        $user = Db::name('user')->where('id', $userId)->find();
        if (!$user) {
            return JsonServer::error('用户不存在');
        }

        // 创建代理保证金订单
        $orderSn = 'AG' . date('YmdHis') . rand(1000, 9999);
        $depositData = [
            'user_id' => $userId,
            'order_sn' => $orderSn,
            'amount' => 100.00, // 保证金金额100元
            'payment_date' => 0,
            'status' => 0, // 未支付
            'created_at' => date('Y-m-d H:i:s')
        ];

        // 插入代理保证金订单
        Db::name('agent_merchantfees')->insertGetId($depositData);

        // 模拟支付回调
        $extra = [
            'transaction_id' => 'WX' . date('YmdHis') . rand(10000, 99999)
        ];

        // 调用支付回调逻辑
        try {
            PayNotifyLogic::handle('agent_merchantfees', $orderSn, $extra);

            // 更新用户为代理
            Db::name('user')->where('id', $userId)->update([
                'is_agent' => 1,
                'agent_id' => $userId
            ]);

            // 创建代理表记录
            $agentData = [
                'user_id' => $userId,
                'area_id' => 0, // 默认区域ID
                'is_type' => 0, // 非区域代理
                'is_freeze' => 0, // 未冻结
                'remark' => '自动测试创建的代理',
                'level' => 1, // 一级代理
                'parent_id' => 0, // 无上级
                'distribution_start_time' => time(),
                'distribution_end_time' => time() + 365 * 86400, // 一年有效期
                'agent_code' => 'AG' . str_pad(rand(1, 99999), 5, '0', STR_PAD_LEFT),
                'create_time' => time(),
                'buy_type' => '测试创建'
            ];

            $agentId = Db::name('agent')->insertGetId($agentData);

            return JsonServer::success('代理保证金支付回调模拟成功', [
                'user_id' => $userId,
                'agent_id' => $agentId,
                'order_sn' => $orderSn,
                'amount' => 100.00
            ]);
        } catch (\Exception $e) {
            return JsonServer::error('代理保证金支付回调模拟失败: ' . $e->getMessage());
        }
    }
}