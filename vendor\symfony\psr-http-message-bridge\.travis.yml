language: php
sudo: false
cache:
    directories:
        - $HOME/.composer/cache/files
        - $HOME/symfony-bridge/.phpunit

env:
    global:
        - PHPUNIT_FLAGS="-v"
        - SYMFONY_PHPUNIT_VERSION=9.5
        - SYMFONY_PHPUNIT_DIR="$HOME/symfony-bridge/.phpunit"

matrix:
    fast_finish: true
    include:
          # Minimum supported dependencies with the latest and oldest PHP version
        - php: 7.4
          env: COMPOSER_FLAGS="--prefer-stable --prefer-lowest" SYMFONY_DEPRECATIONS_HELPER="weak_vendors"

        - php: 7.1
          env: SYMFONY_PHPUNIT_VERSION=7.5
        - php: 7.2
          env: SYMFONY_PHPUNIT_VERSION=8.5
        - php: 7.3
        - php: 7.4
          env: COVERAGE=true PHPUNIT_FLAGS="-v --coverage-text" XDEBUG_MODE=coverage
        - php: 8.0

          # Latest commit to master
        - php: 7.4
          env: STABILITY="dev"

    allow_failures:
          # Dev-master is allowed to fail.
        - env: STABILITY="dev"

before_install:
    - if [[ $COVERAGE != true ]]; then phpenv config-rm xdebug.ini || true; fi
    - if ! [ -z "$STABILITY" ]; then composer config minimum-stability ${STABILITY}; fi;
    - if ! [ -v "$DEPENDENCIES" ]; then composer require --no-update ${DEPENDENCIES}; fi;

install:
    # To be removed when this issue will be resolved: https://github.com/composer/composer/issues/5355
    - if [[ "$COMPOSER_FLAGS" == *"--prefer-lowest"* ]]; then composer update --prefer-dist --no-interaction --prefer-stable --quiet; fi
    - composer update ${COMPOSER_FLAGS} --prefer-dist --no-interaction
    - ./vendor/bin/simple-phpunit install

script:
    - composer validate --strict --no-check-lock
    # simple-phpunit is the PHPUnit wrapper provided by the PHPUnit Bridge component and
    # it helps with testing legacy code and deprecations (composer require symfony/phpunit-bridge)
    - ./vendor/bin/simple-phpunit $PHPUNIT_FLAGS
