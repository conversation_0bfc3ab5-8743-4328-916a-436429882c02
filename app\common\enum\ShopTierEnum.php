<?php

namespace app\common\enum;

/**
 * 商家等级枚举类
 */
class ShopTierEnum
{
    /**
     * 等级常量
     */
    const TIER_FREE = 0;      // 0元入驻
    const TIER_MEMBER = 1;    // 商家会员
    const TIER_PREMIUM = 2;   // 实力厂商

    /**
     * 等级类型
     */
    const TYPE_NEW_ENTRY = 0; // 新入驻
    const TYPE_UPGRADE = 1;   // 等级升级
    const TYPE_RENEWAL = 2;   // 续费

    /**
     * 获取等级名称
     */
    public static function getTierName($tierLevel)
    {
        $names = [
            self::TIER_FREE => '0元入驻',
            self::TIER_MEMBER => '商家会员',
            self::TIER_PREMIUM => '实力厂商',
        ];
        return $names[$tierLevel] ?? '未知等级';
    }

    /**
     * 获取所有等级
     */
    public static function getTierList()
    {
        return [
            self::TIER_FREE => '0元入驻',
            self::TIER_MEMBER => '商家会员',
            self::TIER_PREMIUM => '实力厂商',
        ];
    }

    /**
     * 获取等级类型名称
     */
    public static function getTypeName($type)
    {
        $names = [
            self::TYPE_NEW_ENTRY => '新入驻',
            self::TYPE_UPGRADE => '等级升级',
            self::TYPE_RENEWAL => '续费',
        ];
        return $names[$type] ?? '未知类型';
    }

    /**
     * 检查等级是否有效
     */
    public static function isValidTier($tierLevel)
    {
        return in_array($tierLevel, [self::TIER_FREE, self::TIER_MEMBER, self::TIER_PREMIUM]);
    }

    /**
     * 检查类型是否有效
     */
    public static function isValidType($type)
    {
        return in_array($type, [self::TYPE_NEW_ENTRY, self::TYPE_UPGRADE, self::TYPE_RENEWAL]);
    }
}
