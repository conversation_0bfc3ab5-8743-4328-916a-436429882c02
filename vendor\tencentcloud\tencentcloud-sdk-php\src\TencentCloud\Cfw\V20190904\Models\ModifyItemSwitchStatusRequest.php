<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Cfw\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * ModifyItemSwitchStatus请求参数结构体
 *
 * @method integer getId() 获取id值
 * @method void setId(integer $Id) 设置id值
 * @method integer getStatus() 获取状态值，0: 关闭 ,1:开启
 * @method void setStatus(integer $Status) 设置状态值，0: 关闭 ,1:开启
 * @method integer getType() 获取0: 互联网边界边界防火墙开关，1：vpc防火墙开关
 * @method void setType(integer $Type) 设置0: 互联网边界边界防火墙开关，1：vpc防火墙开关
 */
class ModifyItemSwitchStatusRequest extends AbstractModel
{
    /**
     * @var integer id值
     */
    public $Id;

    /**
     * @var integer 状态值，0: 关闭 ,1:开启
     */
    public $Status;

    /**
     * @var integer 0: 互联网边界边界防火墙开关，1：vpc防火墙开关
     */
    public $Type;

    /**
     * @param integer $Id id值
     * @param integer $Status 状态值，0: 关闭 ,1:开启
     * @param integer $Type 0: 互联网边界边界防火墙开关，1：vpc防火墙开关
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("Id",$param) and $param["Id"] !== null) {
            $this->Id = $param["Id"];
        }

        if (array_key_exists("Status",$param) and $param["Status"] !== null) {
            $this->Status = $param["Status"];
        }

        if (array_key_exists("Type",$param) and $param["Type"] !== null) {
            $this->Type = $param["Type"];
        }
    }
}
