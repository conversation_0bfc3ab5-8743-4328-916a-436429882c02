<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Cloudaudit\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * LookUpEvents返回参数结构体
 *
 * @method string getNextToken() 获取查看更多日志的凭证
注意：此字段可能返回 null，表示取不到有效值。
 * @method void setNextToken(string $NextToken) 设置查看更多日志的凭证
注意：此字段可能返回 null，表示取不到有效值。
 * @method array getEvents() 获取日志集合
注意：此字段可能返回 null，表示取不到有效值。
 * @method void setEvents(array $Events) 设置日志集合
注意：此字段可能返回 null，表示取不到有效值。
 * @method boolean getListOver() 获取日志集合是否结束
注意：此字段可能返回 null，表示取不到有效值。
 * @method void setListOver(boolean $ListOver) 设置日志集合是否结束
注意：此字段可能返回 null，表示取不到有效值。
 * @method string getRequestId() 获取唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
 * @method void setRequestId(string $RequestId) 设置唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
 */
class LookUpEventsResponse extends AbstractModel
{
    /**
     * @var string 查看更多日志的凭证
注意：此字段可能返回 null，表示取不到有效值。
     */
    public $NextToken;

    /**
     * @var array 日志集合
注意：此字段可能返回 null，表示取不到有效值。
     */
    public $Events;

    /**
     * @var boolean 日志集合是否结束
注意：此字段可能返回 null，表示取不到有效值。
     */
    public $ListOver;

    /**
     * @var string 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
     */
    public $RequestId;

    /**
     * @param string $NextToken 查看更多日志的凭证
注意：此字段可能返回 null，表示取不到有效值。
     * @param array $Events 日志集合
注意：此字段可能返回 null，表示取不到有效值。
     * @param boolean $ListOver 日志集合是否结束
注意：此字段可能返回 null，表示取不到有效值。
     * @param string $RequestId 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("NextToken",$param) and $param["NextToken"] !== null) {
            $this->NextToken = $param["NextToken"];
        }

        if (array_key_exists("Events",$param) and $param["Events"] !== null) {
            $this->Events = [];
            foreach ($param["Events"] as $key => $value){
                $obj = new Event();
                $obj->deserialize($value);
                array_push($this->Events, $obj);
            }
        }

        if (array_key_exists("ListOver",$param) and $param["ListOver"] !== null) {
            $this->ListOver = $param["ListOver"];
        }

        if (array_key_exists("RequestId",$param) and $param["RequestId"] !== null) {
            $this->RequestId = $param["RequestId"];
        }
    }
}
