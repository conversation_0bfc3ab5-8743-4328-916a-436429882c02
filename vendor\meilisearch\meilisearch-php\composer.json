{"name": "meilisearch/meilisearch-php", "description": "PHP wrapper for the Meilisearch API", "keywords": ["meilisearch", "instant", "search", "api", "client", "php"], "type": "library", "license": "MIT", "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "minimum-stability": "stable", "require": {"php": "^7.4 || ^8.0", "ext-json": "*", "php-http/discovery": "^1.7", "psr/http-client": "^1.0"}, "autoload": {"psr-4": {"MeiliSearch\\": "src/", "Meilisearch\\": "src/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "suggest": {"guzzlehttp/guzzle": "Use Guzzle ^7 as HTTP client", "http-interop/http-factory-guzzle": "Factory for guzzlehttp/guzzle"}, "require-dev": {"phpunit/phpunit": "^9.5 || ^10.5", "php-cs-fixer/shim": "^3.59.3", "guzzlehttp/guzzle": "^7.8.1", "http-interop/http-factory-guzzle": "^1.2.0", "phpstan/phpstan": "^1.11.5", "phpstan/extension-installer": "^1.4.1", "phpstan/phpstan-strict-rules": "^1.6.0", "phpstan/phpstan-phpunit": "^1.4.0", "phpstan/phpstan-deprecation-rules": "^1.2.0"}, "scripts": {"lint": ["./vendor/bin/php-cs-fixer fix --verbose --config=.php-cs-fixer.dist.php --using-cache=no --dry-run --diff"], "lint:fix": ["./vendor/bin/php-cs-fixer fix --verbose --config=.php-cs-fixer.dist.php --using-cache=no  --diff"], "phpstan": "./vendor/bin/phpstan", "test": ["sh scripts/tests.sh"]}, "config": {"allow-plugins": {"phpstan/extension-installer": true, "php-http/discovery": true}}}