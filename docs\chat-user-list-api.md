# 聊天联系人列表API说明

## 🔧 修复内容

修复了 `getChatUserList` 方法，现在返回用户的**所有聊天记录**，包括：
1. **客服聊天**：用户与商家客服的聊天（`shop_id > 0`）
2. **用户聊天**：用户与用户之间的聊天（`shop_id = 0`）

## 📱 API接口

### 获取所有聊天列表

```bash
GET /api/user/getChatUserList?page_no=1
```

**参数说明：**
- `page_no`: 页码（必填）
- `nickname`: 搜索昵称（可选）

**返回示例：**
```json
{
    "code": 1,
    "msg": "获取成功",
    "data": {
        "list": [
            {
                "id": 1,
                "shop_id": 1,
                "user_id": 100,
                "kefu_id": 5,
                "nickname": "测试商家",
                "avatar": "客服头像URL",
                "shop_name": "测试商家",
                "kefu": {
                    "id": 5,
                    "nickname": "客服小王",
                    "avatar": "头像"
                },
                "msg": "你好，有什么可以帮助您的吗？",
                "is_read": 0,
                "time_txt": "2分钟前",
                "online": 1,
                "chat_type": "kefu"
            },
            {
                "id": 2,
                "shop_id": 0,
                "user_id": 100,
                "kefu_id": 200,
                "nickname": "张三",
                "avatar": "用户头像URL",
                "shop_name": "",
                "contact_user": {
                    "id": 200,
                    "nickname": "张三",
                    "avatar": "头像"
                },
                "msg": "你好",
                "is_read": 1,
                "time_txt": "5分钟前",
                "online": 0,
                "chat_type": "user"
            }
        ],
        "page": 1,
        "size": 20,
        "no_read": 1,
        "count": 2,
        "more": false
    }
}
```

## 🔄 数据结构说明

### 客服聊天记录字段

| 字段 | 类型 | 说明 |
|------|------|------|
| `shop_id` | int | 商家ID（大于0） |
| `kefu_id` | int | 客服ID |
| `shop_name` | string | 商家名称 |
| `kefu` | object | 客服信息对象 |
| `chat_type` | string | 固定值 "kefu" |

### 用户聊天记录字段

| 字段 | 类型 | 说明 |
|------|------|------|
| `shop_id` | int | 固定值 0 |
| `kefu_id` | int | 联系人用户ID（复用字段） |
| `shop_name` | string | 空字符串 |
| `contact_user` | object | 联系人用户信息对象 |
| `chat_type` | string | 固定值 "user" |

### 通用字段

| 字段 | 类型 | 说明 |
|------|------|------|
| `id` | int | 关系记录ID |
| `user_id` | int | 当前用户ID |
| `nickname` | string | 显示昵称 |
| `avatar` | string | 显示头像URL |
| `msg` | string | 最后一条消息内容 |
| `is_read` | int | 已读状态（0未读，1已读） |
| `time_txt` | string | 时间显示文本 |
| `online` | int | 在线状态（0离线，1在线） |

## 🧪 测试步骤

### 1. 测试客服聊天列表

```bash
# 获取客服聊天列表
curl -X GET "http://your-domain/api/user/getChatUserList?page_no=1&chat_type=kefu" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

**验证点：**
- ✅ 只返回 `shop_id > 0` 的记录
- ✅ 包含 `kefu` 对象信息
- ✅ `chat_type` 为 "kefu"
- ✅ 正确显示商家名称

### 2. 测试用户聊天列表

```bash
# 获取用户聊天列表
curl -X GET "http://your-domain/api/user/getChatUserList?page_no=1&chat_type=user" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

**验证点：**
- ✅ 只返回 `shop_id = 0` 的记录
- ✅ 包含 `contact_user` 对象信息
- ✅ `chat_type` 为 "user"
- ✅ 正确显示联系人用户信息

### 3. 测试便捷方法

```bash
# 使用便捷方法获取用户聊天列表
curl -X GET "http://your-domain/api/user/getUserChatList?page_no=1" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

**验证点：**
- ✅ 自动设置为用户聊天类型
- ✅ 返回结果与上面的用户聊天列表一致

## 🔍 错误处理

### 修复的问题

1. **空值访问错误**：
   ```php
   // 修复前（会报错）
   $item['avatar'] = $item['kefu']['avatar'] ? UrlServer::getFileUrl($item['kefu']['avatar']) : '';
   
   // 修复后（安全访问）
   $item['avatar'] = '';
   if (!empty($item['kefu']) && !empty($item['kefu']['avatar'])) {
       $item['avatar'] = UrlServer::getFileUrl($item['kefu']['avatar']);
   }
   ```

2. **在线用户数组检查**：
   ```php
   // 修复前（可能为null）
   $online_user = CommonChatLogic::getOnlineUser();
   
   // 修复后（确保为数组）
   $online_user = CommonChatLogic::getOnlineUser();
   if (empty($online_user) || !is_array($online_user)) {
       $online_user = [];
   }
   ```

## 📝 注意事项

1. **向后兼容**：不传 `chat_type` 参数时，默认为客服聊天
2. **字段复用**：用户聊天复用了 `kefu_id` 字段存储联系人用户ID
3. **数据区分**：通过 `shop_id` 和 `chat_type` 区分不同类型的聊天
4. **在线状态**：用户聊天检查联系人是否在线，客服聊天检查当前用户是否在线
5. **未读统计**：根据聊天类型使用不同的查询条件

通过这些修改，现在可以正确处理两种不同类型的聊天列表，避免了数据混乱和错误！
