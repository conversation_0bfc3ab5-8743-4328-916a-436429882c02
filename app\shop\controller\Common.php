<?php

namespace app\shop\controller;
use app\common\server\JsonServer;
use app\shop\logic\goods\CategoryLogic;
use app\common\basics\ShopBase;
use app\shop\logic\CommonLogic;

class Common extends ShopBase{
    public function selectGoods(){
        if ($this->request->isAjax()){
            $get = $this->request->get();
            $get['shop_id'] = $this->shop_id;
            $data = CommonLogic::getGoodsList($get);
            return JsonServer::success('', $data);
        }

        $category_list = CategoryLogic::listAll($this->shop_id);
        return view('', [
            'category_list' => $category_list
        ]);
    }

    public function selectGoodsTwo(){
        if ($this->request->isAjax()){
            $get = $this->request->get();
            $get['shop_id'] = $this->shop_id;
            $goods_list = CommonLogic::getGoodsListTwo($get,true);
            return JsonServer::success('', $goods_list);
        }
        $category_list = CategoryLogic::listAll($this->shop_id);
        return view('', [
            'category_list' => $category_list
        ]);
    }
}