(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["bundle-pages-license-license"],{"1e2e":function(t,e,i){var n=i("3096");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=i("4f06").default;a("ccafd518",n,!0,{sourceMap:!1,shadowMode:!1})},"1f90":function(t,e,i){"use strict";i.r(e);var n=i("8f5b"),a=i.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(r);e["default"]=a.a},2846:function(t,e){t.exports="data:image/png;base64,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"},3096:function(t,e,i){var n=i("24fb");e=n(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.u-image[data-v-1bf07c9a]{position:relative;transition:opacity .5s ease-in-out}.u-image__image[data-v-1bf07c9a]{width:100%;height:100%}.u-image__loading[data-v-1bf07c9a], .u-image__error[data-v-1bf07c9a]{position:absolute;top:0;left:0;width:100%;height:100%;display:flex;flex-direction:row;align-items:center;justify-content:center;background-color:#f3f4f6;color:#909399;font-size:%?46?%}',""]),t.exports=e},"3f30":function(t,e,i){"use strict";i.r(e);var n=i("4219"),a=i.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(r);e["default"]=a.a},4219:function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("a9e3");var n={name:"u-image",props:{src:{type:String,default:""},mode:{type:String,default:"aspectFill"},width:{type:[String,Number],default:"100%"},height:{type:[String,Number],default:"auto"},shape:{type:String,default:"square"},borderRadius:{type:[String,Number],default:0},lazyLoad:{type:Boolean,default:!0},showMenuByLongpress:{type:Boolean,default:!0},loadingIcon:{type:String,default:"photo"},errorIcon:{type:String,default:"error-circle"},showLoading:{type:Boolean,default:!0},showError:{type:Boolean,default:!0},fade:{type:Boolean,default:!0},webp:{type:Boolean,default:!1},duration:{type:[String,Number],default:500},bgColor:{type:String,default:"#f3f4f6"}},data:function(){return{isError:!1,loading:!0,opacity:1,durationTime:this.duration,backgroundStyle:{}}},watch:{src:{immediate:!0,handler:function(t){t?this.isError=!1:(this.isError=!0,this.loading=!1)}}},computed:{wrapStyle:function(){var t={};return t.width=this.$u.addUnit(this.width),t.height=this.$u.addUnit(this.height),t.borderRadius="circle"==this.shape?"50%":this.$u.addUnit(this.borderRadius),t.overflow=this.borderRadius>0?"hidden":"visible",this.fade&&(t.opacity=this.opacity,t.transition="opacity ".concat(Number(this.durationTime)/1e3,"s ease-in-out")),t}},methods:{onClick:function(){this.$emit("click")},onErrorHandler:function(t){this.loading=!1,this.isError=!0,this.$emit("error",t)},onLoadHandler:function(){var t=this;if(this.loading=!1,this.isError=!1,this.$emit("load"),!this.fade)return this.removeBgColor();this.opacity=0,this.durationTime=0,setTimeout((function(){t.durationTime=t.duration,t.opacity=1,setTimeout((function(){t.removeBgColor()}),t.durationTime)}),50)},removeBgColor:function(){this.backgroundStyle={backgroundColor:"transparent"}}}};e.default=n},"47c5":function(t,e,i){var n=i("c448");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=i("4f06").default;a("81dfa122",n,!0,{sourceMap:!1,shadowMode:!1})},"4d87":function(t,e,i){"use strict";i.r(e);var n=i("7bf6"),a=i("1f90");for(var r in a)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(r);i("b1b2");var o=i("f0c5"),s=Object(o["a"])(a["default"],n["b"],n["c"],!1,null,"510da5f5",null,!1,n["a"],void 0);e["default"]=s.exports},"7bf6":function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return r})),i.d(e,"a",(function(){return n}));var n={uImage:i("f919").default},a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{staticClass:"license"},[n("v-uni-view",{staticClass:"box bg-white"},[t.images.length?t._l(t.images,(function(e,i){return n("v-uni-view",{key:i,staticClass:"m-b-25",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.viewImage(i)}}},[n("u-image",{attrs:{src:e,width:"100%",height:"348rpx"}})],1)})):[n("v-uni-view",{staticClass:"data-null xs muted"},[n("v-uni-image",{attrs:{src:i("2846"),mode:""}}),n("v-uni-view",[t._v("商家暂时还没有上传资质哦~")])],1)]],2)],1)},r=[]},"8f5b":function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=i("1524"),a={data:function(){return{images:[]}},methods:{getCopyrightFunc:function(t){var e=this;(0,n.getCopyright)({shop_id:t}).then((function(t){e.images=t.data}))},viewImage:function(t){uni.previewImage({current:t,urls:this.images})}},onLoad:function(){var t=this.$Route.query.id;this.getCopyrightFunc(t)}};e.default=a},b1b2:function(t,e,i){"use strict";var n=i("47c5"),a=i.n(n);a.a},c448:function(t,e,i){var n=i("24fb");e=n(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.license[data-v-510da5f5]{padding:%?30?%}.license .box[data-v-510da5f5]{padding:%?30?%;border-radius:%?16?%}.license .box .data-null[data-v-510da5f5]{padding-top:%?200?%;height:%?700?%;text-align:center}.license .box .data-null uni-image[data-v-510da5f5]{width:%?200?%;height:%?200?%}',""]),t.exports=e},c529:function(t,e,i){"use strict";var n=i("1e2e"),a=i.n(n);a.a},f743:function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return r})),i.d(e,"a",(function(){return n}));var n={uIcon:i("6976").default},a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"u-image",style:[t.wrapStyle,t.backgroundStyle],on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onClick.apply(void 0,arguments)}}},[t.isError?t._e():i("v-uni-image",{staticClass:"u-image__image",style:{borderRadius:"circle"==t.shape?"50%":t.$u.addUnit(t.borderRadius)},attrs:{src:t.src,mode:t.mode,"lazy-load":t.lazyLoad},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.onErrorHandler.apply(void 0,arguments)},load:function(e){arguments[0]=e=t.$handleEvent(e),t.onLoadHandler.apply(void 0,arguments)}}}),t.showLoading&&t.loading?i("v-uni-view",{staticClass:"u-image__loading",style:{borderRadius:"circle"==t.shape?"50%":t.$u.addUnit(t.borderRadius),backgroundColor:this.bgColor}},[t.$slots.loading?t._t("loading"):i("u-icon",{attrs:{name:t.loadingIcon,width:t.width,height:t.height}})],2):t._e(),t.showError&&t.isError&&!t.loading?i("v-uni-view",{staticClass:"u-image__error",style:{borderRadius:"circle"==t.shape?"50%":t.$u.addUnit(t.borderRadius)}},[t.$slots.error?t._t("error"):i("u-icon",{attrs:{name:t.errorIcon,width:t.width,height:t.height}})],2):t._e()],1)},r=[]},f919:function(t,e,i){"use strict";i.r(e);var n=i("f743"),a=i("3f30");for(var r in a)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(r);i("c529");var o=i("f0c5"),s=Object(o["a"])(a["default"],n["b"],n["c"],!1,null,"1bf07c9a",null,!1,n["a"],void 0);e["default"]=s.exports}}]);