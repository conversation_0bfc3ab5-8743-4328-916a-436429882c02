(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-brand_list-brand_list"],{1227:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return r})),i.d(e,"a",(function(){return a}));var a={uImage:i("ba4b").default},n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"brand-list"},[t.hasData?i("v-uni-view",t._l(t.brandList,(function(e,a){return i("v-uni-view",{key:a,staticClass:"p-t-30"},[i("v-uni-view",{staticClass:"title muted m-b-10 m-l-30"},[t._v(t._s(e.letter))]),i("v-uni-view",{staticClass:"list flex flex-wrap bg-white col-top"},t._l(e.list,(function(e,a){return i("router-link",{key:a,staticClass:"item",attrs:{to:{path:"/pages/goods_search/goods_search",query:{id:e.id,name:e.name,type:0}}}},[i("v-uni-view",{staticClass:"flex-col col-center m-b-30"},[i("u-image",{attrs:{mode:"aspectFit",width:"150rpx",height:"150rpx",src:e.image}}),i("v-uni-view",{staticClass:"text-center m-t-10 xs"},[t._v(t._s(e.name))])],1)],1)})),1)],1)})),1):i("v-uni-view",{staticClass:"details-null flex-col col-center",staticStyle:{"padding-top":"200rpx"}},[i("v-uni-image",{staticClass:"img-null",attrs:{src:"/static/images/goods_null.png"}}),i("v-uni-view",{staticClass:"xs muted"},[t._v("暂无品牌~")])],1)],1)},r=[]},1522:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return r})),i.d(e,"a",(function(){return a}));var a={uIcon:i("90f3").default},n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"u-image",style:[t.wrapStyle,t.backgroundStyle],on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onClick.apply(void 0,arguments)}}},[t.isError?t._e():i("v-uni-image",{staticClass:"u-image__image",style:{borderRadius:"circle"==t.shape?"50%":t.$u.addUnit(t.borderRadius)},attrs:{src:t.src,mode:t.mode,"lazy-load":t.lazyLoad},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.onErrorHandler.apply(void 0,arguments)},load:function(e){arguments[0]=e=t.$handleEvent(e),t.onLoadHandler.apply(void 0,arguments)}}}),t.showLoading&&t.loading?i("v-uni-view",{staticClass:"u-image__loading",style:{borderRadius:"circle"==t.shape?"50%":t.$u.addUnit(t.borderRadius),backgroundColor:this.bgColor}},[t.$slots.loading?t._t("loading"):i("u-icon",{attrs:{name:t.loadingIcon,width:t.width,height:t.height}})],2):t._e(),t.showError&&t.isError&&!t.loading?i("v-uni-view",{staticClass:"u-image__error",style:{borderRadius:"circle"==t.shape?"50%":t.$u.addUnit(t.borderRadius)}},[t.$slots.error?t._t("error"):i("u-icon",{attrs:{name:t.errorIcon,width:t.width,height:t.height}})],2):t._e()],1)},r=[]},"169c":function(t,e,i){var a=i("24fb");e=a(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.brand-list .list[data-v-2bdab40a]{margin:0 %?20?%;padding:%?30?% %?20?% 0;border-radius:%?10?%}.brand-list .list .item[data-v-2bdab40a]{width:25%}',""]),t.exports=e},"1f41":function(t,e,i){"use strict";i("7a82");var a=i("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=a(i("f07e")),r=a(i("c964")),o=i("9953"),s={data:function(){return{brandList:[],hasData:!0}},onLoad:function(){this.getBrandListFun()},methods:{getBrandListFun:function(){var t=this;return(0,r.default)((0,n.default)().mark((function e(){var i,a,r;return(0,n.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,(0,o.getBrandList)();case 2:i=e.sent,a=i.data,r=i.code,1==r&&(a.length||(t.hasData=!1),t.brandList=a);case 6:case"end":return e.stop()}}),e)})))()}}};e.default=s},2322:function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("a9e3");var a={name:"u-image",props:{src:{type:String,default:""},mode:{type:String,default:"aspectFill"},width:{type:[String,Number],default:"100%"},height:{type:[String,Number],default:"auto"},shape:{type:String,default:"square"},borderRadius:{type:[String,Number],default:0},lazyLoad:{type:Boolean,default:!0},showMenuByLongpress:{type:Boolean,default:!0},loadingIcon:{type:String,default:"photo"},errorIcon:{type:String,default:"error-circle"},showLoading:{type:Boolean,default:!0},showError:{type:Boolean,default:!0},fade:{type:Boolean,default:!0},webp:{type:Boolean,default:!1},duration:{type:[String,Number],default:500},bgColor:{type:String,default:"#f3f4f6"}},data:function(){return{isError:!1,loading:!0,opacity:1,durationTime:this.duration,backgroundStyle:{}}},watch:{src:{immediate:!0,handler:function(t){t?this.isError=!1:(this.isError=!0,this.loading=!1)}}},computed:{wrapStyle:function(){var t={};return t.width=this.$u.addUnit(this.width),t.height=this.$u.addUnit(this.height),t.borderRadius="circle"==this.shape?"50%":this.$u.addUnit(this.borderRadius),t.overflow=this.borderRadius>0?"hidden":"visible",this.fade&&(t.opacity=this.opacity,t.transition="opacity ".concat(Number(this.durationTime)/1e3,"s ease-in-out")),t}},methods:{onClick:function(){this.$emit("click")},onErrorHandler:function(t){this.loading=!1,this.isError=!0,this.$emit("error",t)},onLoadHandler:function(){var t=this;if(this.loading=!1,this.isError=!1,this.$emit("load"),!this.fade)return this.removeBgColor();this.opacity=0,this.durationTime=0,setTimeout((function(){t.durationTime=t.duration,t.opacity=1,setTimeout((function(){t.removeBgColor()}),t.durationTime)}),50)},removeBgColor:function(){this.backgroundStyle={backgroundColor:"transparent"}}}};e.default=a},4928:function(t,e,i){var a=i("169c");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("4f06").default;n("ef330e52",a,!0,{sourceMap:!1,shadowMode:!1})},6021:function(t,e,i){"use strict";var a=i("64b1"),n=i.n(a);n.a},"64b1":function(t,e,i){var a=i("6e35");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("4f06").default;n("3d1e497c",a,!0,{sourceMap:!1,shadowMode:!1})},"6e35":function(t,e,i){var a=i("24fb");e=a(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.u-image[data-v-1bf07c9a]{position:relative;transition:opacity .5s ease-in-out}.u-image__image[data-v-1bf07c9a]{width:100%;height:100%}.u-image__loading[data-v-1bf07c9a], .u-image__error[data-v-1bf07c9a]{position:absolute;top:0;left:0;width:100%;height:100%;display:flex;flex-direction:row;align-items:center;justify-content:center;background-color:#f3f4f6;color:#909399;font-size:%?46?%}',""]),t.exports=e},af8d:function(t,e,i){"use strict";i.r(e);var a=i("2322"),n=i.n(a);for(var r in a)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(r);e["default"]=n.a},ba4b:function(t,e,i){"use strict";i.r(e);var a=i("1522"),n=i("af8d");for(var r in n)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(r);i("6021");var o=i("f0c5"),s=Object(o["a"])(n["default"],a["b"],a["c"],!1,null,"1bf07c9a",null,!1,a["a"],void 0);e["default"]=s.exports},bc78:function(t,e,i){"use strict";var a=i("4928"),n=i.n(a);n.a},ef67:function(t,e,i){"use strict";i.r(e);var a=i("1227"),n=i("f1b9");for(var r in n)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(r);i("bc78");var o=i("f0c5"),s=Object(o["a"])(n["default"],a["b"],a["c"],!1,null,"2bdab40a",null,!1,a["a"],void 0);e["default"]=s.exports},f1b9:function(t,e,i){"use strict";i.r(e);var a=i("1f41"),n=i.n(a);for(var r in a)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(r);e["default"]=n.a}}]);