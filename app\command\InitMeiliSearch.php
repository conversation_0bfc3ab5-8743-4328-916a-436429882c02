<?php
declare (strict_types = 1);

namespace app\command;

use app\common\library\MeiliSearch;
use think\console\Command;
use think\console\Input;
use think\console\input\Option;
use think\console\Output;

/**
 * 初始化MeiliSearch
 */
class InitMeiliSearch extends Command
{

    protected function configure()
    {
        // 指令配置
        $this->setName('init:meilisearch')
            ->addOption('reset', 'r', Option::VALUE_NONE, '重置所有索引')
            ->setDescription('初始化MeiliSearch索引和设置');
    }

    protected function execute(Input $input, Output $output)
    {
        $reset = $input->getOption('reset');

        // 初始化MeiliSearch客户端
        $meili = new MeiliSearch();

        // 如果需要重置索引
        if ($reset) {
            $output->writeln('正在重置所有索引...');
            $meili->deleteIndex('goods');
            $output->writeln('所有索引已重置');
        }

        // 初始化商品索引
        $this->initGoodsIndex($meili, $output);

        $output->writeln('MeiliSearch初始化完成');
    }

    /**
     * 初始化商品索引
     * @param MeiliSearch $meili
     * @param Output $output
     */
    protected function initGoodsIndex(MeiliSearch $meili, Output $output)
    {
        $output->writeln('正在初始化商品索引...');

        // 创建或获取索引
        $indexInfo = $meili->getIndex('goods');
        if (!$indexInfo || isset($indexInfo['error'])) {
            $output->writeln('正在创建商品索引...');
            // 索引不存在，创建索引
            $settings = [
                'searchableAttributes' => ['name', 'category_path', 'brand_name', 'tags', 'remark', 'content'],
                'filterableAttributes' => [
                    'shop_id',
                    'first_cate_id',
                    'second_cate_id',
                    'third_cate_id',
                    'brand_id',
                    'status',
                    'is_hot',
                    'is_recommend',
                    'category_path',
                    'brand_name',
                    'tags'
                ],
                'sortableAttributes' => [
                    'min_price',
                    'sales_actual',
                    'sales_virtual',
                    'create_time',
                    'update_time'
                ],
                'rankingRules' => [
                    'words',
                    'typo',
                    'proximity',
                    'attribute',
                    'sort',
                    'exactness',
                    'custom.sales_actual:desc',
                    'custom.create_time:desc'
                ],
                'synonyms' => [
                    '上衣' => ['T恤', '衬衫', '卫衣'],
                    '裤子' => ['长裤', '休闲裤', '牛仔裤'],
                    '笔记本电脑' => ['笔记本', '手提电脑'],
                    '手机' => ['移动电话', '手提电话']
                ]
            ];
            $meili->createIndex('goods', $settings);
            $output->writeln('商品索引已创建');
        } else {
            $output->writeln('商品索引已存在，更新索引设置');
            
            // 更新索引设置
            $settings = [
                'searchableAttributes' => ['name', 'category_path', 'brand_name', 'tags', 'remark', 'content'],
                'filterableAttributes' => [
                    'shop_id',
                    'first_cate_id',
                    'second_cate_id',
                    'third_cate_id',
                    'brand_id',
                    'status',
                    'is_hot',
                    'is_recommend',
                    'category_path',
                    'brand_name',
                    'tags'
                ],
                'sortableAttributes' => [
                    'min_price',
                    'sales_actual',
                    'sales_virtual',
                    'create_time',
                    'update_time'
                ],
                'rankingRules' => [
                    'words',
                    'typo',
                    'proximity',
                    'attribute',
                    'sort',
                    'exactness',
                    'custom.sales_actual:desc',
                    'custom.create_time:desc'
                ],
                'synonyms' => [
                    '上衣' => ['T恤', '衬衫', '卫衣'],
                    '裤子' => ['长裤', '休闲裤', '牛仔裤'],
                    '笔记本电脑' => ['笔记本', '手提电脑'],
                    '手机' => ['移动电话', '手提电话']
                ]
            ];
            $meili->updateIndexSettings('goods', $settings);
            $output->writeln('商品索引设置已更新');
        }
    }
}
