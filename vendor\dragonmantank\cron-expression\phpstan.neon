parameters:
    checkMissingIterableValueType: false

    ignoreErrors:
        - '#Call to an undefined method DateTimeInterface::add\(\)#'
        - '#Call to an undefined method DateTimeInterface::modify\(\)#'
        - '#Call to an undefined method DateTimeInterface::setDate\(\)#'
        - '#Call to an undefined method DateTimeInterface::setTime\(\)#'
        - '#Call to an undefined method DateTimeInterface::setTimezone\(\)#'
        - '#Call to an undefined method DateTimeInterface::sub\(\)#'

    level: max

    paths:
        - src/
