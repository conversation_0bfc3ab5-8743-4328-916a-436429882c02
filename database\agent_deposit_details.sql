-- 创建代理保证金明细表
CREATE TABLE IF NOT EXISTS `ls_agent_deposit_details` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '记录的唯一标识符',
  `user_id` int(11) NOT NULL COMMENT '代理用户ID',
  `deposit_id` int(11) NOT NULL COMMENT '代理保证金表ID',
  `sn` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '明细单号',
  `deposit_change` decimal(10, 2) NOT NULL COMMENT '保证金变动金额（正数表示增加，负数表示扣除）',
  `change_type` tinyint(4) NOT NULL COMMENT '变动类型：1-缴纳 2-增加 3-扣除 4-退还',
  `amount` decimal(10, 2) NOT NULL COMMENT '变动金额的绝对值',
  `reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '保证金变动原因',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `admin_id` int(11) NULL DEFAULT NULL COMMENT '操作管理员ID',
  `change_date` date NOT NULL COMMENT '变动日期',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录最后更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id` (`user_id`) USING BTREE,
  INDEX `idx_deposit_id` (`deposit_id`) USING BTREE,
  INDEX `idx_change_type` (`change_type`) USING BTREE,
  INDEX `idx_change_date` (`change_date`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '代理保证金明细表' ROW_FORMAT = DYNAMIC;
