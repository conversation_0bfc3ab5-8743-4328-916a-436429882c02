<?php

namespace app\common\listener\websocket;


use app\common\enum\KefuEnum;
use app\common\model\kefu\Kefu;
use app\common\model\shop\Shop;
use app\common\model\user\User;
use app\common\websocket\Response;

/**
 * 登录事件
 * Class Login
 * @package app\common\listener\websocket
 */
class Login
{

    protected $response;

    public function __construct(Response $response)
    {
        $this->response = $response;
    }


    public function handle(array $event)
    {
        // 从事件中提取参数
        ['type' => $type, 'token' => $token, 'client' => $client] = $event;

        // 记录详细的参数信息
        // \think\facade\Log::info("Login事件参数: type={$type}, token=" . (empty($token) ? '空' : '已设置') . ", client={$client}");

        // 对于管理员连接，client参数可选
        if ($type === 'admin') {
            // 管理员登录逻辑 - 简化处理，直接返回成功
            return $this->response->success('', [
                'id' => $event['admin_id'] ?? 0,
                'nickname' => $event['nickname'] ?? '管理员',
                'client' => $client,
                'type' => 'admin'
            ]);
        } else {
            // 对于客服/用户连接，所有参数都必须
            if (empty($type) || empty($token) || empty($client)) {
                $missingParams = [];
                if (empty($type)) $missingParams[] = 'type';
                if (empty($token)) $missingParams[] = 'token';
                if (empty($client)) $missingParams[] = 'client';

                \think\facade\Log::error("客服/用户登录参数缺失: " . implode(', ', $missingParams));
                return $this->response->error('参数缺失: ' . implode(', ', $missingParams));
            }

            if (!in_array($type, ['user', 'kefu', 'shop'])) {
                \think\facade\Log::error("登录类型错误: {$type}");
                return $this->response->error('类型错误');
            }
        }

        if ('user' == $type) {
            // 查询用户信息
            $user = (new User())->alias('u')
                ->field('u.id, u.sn, u.nickname, u.avatar, u.mobile, u.level, u.group_id, u.sex, u.disable, u.del')
                ->join('session s', 'u.id = s.user_id')
                ->where(['s.token' => $token])
                ->findOrEmpty();
        } else {
            if($type == 'shop') {
                // 查询商户信息
                $user = (new Shop())->alias('k')
                    ->field('k.id, k.mobile as sn, k.name as nickname, k.logo as avatar, k.mobile, k.tier_level as level, k.cid as group_id')
                    ->join('kefu_session s', 'k.id = s.kefu_id')
                    ->where(['s.token' => $token])
                    ->findOrEmpty();
            } else {
              
            // 查询客服信息
            $user = (new Kefu())->alias('k')
                ->field('k.*')
                ->join('kefu_session s', 'k.id = s.kefu_id')
                ->where(['s.token' => $token])
                ->findOrEmpty();
            }
        }
    
        if ($user->isEmpty() || $user['del'] || $user['disable']) {
            return $this->response->error('用户信息不存在或用户已被禁用-'. $type);
        }

        $user['client'] = $client;

        return $this->response->success('', $user->toArray());
    }

}