<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Cms\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * ManualReview请求参数结构体
 *
 * @method ManualReviewContent getReviewContent() 获取人工审核信息
 * @method void setReviewContent(ManualReviewContent $ReviewContent) 设置人工审核信息
 */
class ManualReviewRequest extends AbstractModel
{
    /**
     * @var ManualReviewContent 人工审核信息
     */
    public $ReviewContent;

    /**
     * @param ManualReviewContent $ReviewContent 人工审核信息
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("ReviewContent",$param) and $param["ReviewContent"] !== null) {
            $this->ReviewContent = new ManualReviewContent();
            $this->ReviewContent->deserialize($param["ReviewContent"]);
        }
    }
}
