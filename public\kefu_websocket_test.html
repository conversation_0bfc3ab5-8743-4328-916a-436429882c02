<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>客服WebSocket测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .status.connected {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.disconnected {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .form-group {
            margin: 15px 0;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, textarea, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .messages {
            height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            background-color: #f8f9fa;
            margin: 15px 0;
        }
        .message {
            margin: 5px 0;
            padding: 8px;
            border-radius: 4px;
        }
        .message.sent {
            background-color: #007bff;
            color: white;
            text-align: right;
        }
        .message.received {
            background-color: #e9ecef;
            color: #333;
        }
        .message.system {
            background-color: #fff3cd;
            color: #856404;
            text-align: center;
            font-style: italic;
        }
        .message-time {
            font-size: 12px;
            opacity: 0.7;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>客服WebSocket测试页面</h1>
        
        <div id="status" class="status disconnected">未连接</div>
        
        <div class="form-group">
            <label for="token">客服Token:</label>
            <input type="text" id="token" placeholder="请输入客服token">
        </div>
        
        <div class="form-group">
            <label for="shopId">商家ID:</label>
            <input type="number" id="shopId" placeholder="请输入商家ID" value="86">
        </div>
        
        <div class="form-group">
            <button id="connectBtn" onclick="connect()">连接</button>
            <button id="disconnectBtn" onclick="disconnect()" disabled>断开连接</button>
        </div>
        
        <div class="messages" id="messages"></div>
        
        <div class="form-group">
            <label for="toId">接收者ID:</label>
            <input type="number" id="toId" placeholder="用户ID">
        </div>
        
        <div class="form-group">
            <label for="toType">接收者类型:</label>
            <select id="toType">
                <option value="user">用户</option>
                <option value="kefu">客服</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="messageType">消息类型:</label>
            <select id="messageType">
                <option value="1">文本消息</option>
                <option value="2">图片消息</option>
                <option value="3">商品消息</option>
                <option value="4">语音消息</option>
                <option value="5">视频消息</option>
                <option value="6">订单消息</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="messageContent">消息内容:</label>
            <textarea id="messageContent" rows="3" placeholder="请输入消息内容"></textarea>
        </div>
        
        <div class="form-group">
            <button onclick="sendMessage()" disabled id="sendBtn">发送消息</button>
            <button onclick="clearMessages()">清空消息</button>
        </div>
        
        <div class="form-group">
            <h3>连接状态信息:</h3>
            <pre id="connectionInfo"></pre>
        </div>
    </div>

    <script src="/static/js/kefu-websocket.js"></script>
    <script>
        let kefuWS = null;
        
        function updateStatus(text, isConnected) {
            const statusEl = document.getElementById('status');
            statusEl.textContent = text;
            statusEl.className = 'status ' + (isConnected ? 'connected' : 'disconnected');
            
            // 更新按钮状态
            document.getElementById('connectBtn').disabled = isConnected;
            document.getElementById('disconnectBtn').disabled = !isConnected;
            document.getElementById('sendBtn').disabled = !isConnected;
        }
        
        function addMessage(content, type = 'system') {
            const messagesEl = document.getElementById('messages');
            const messageEl = document.createElement('div');
            messageEl.className = 'message ' + type;
            
            const time = new Date().toLocaleTimeString();
            messageEl.innerHTML = `
                <div>${content}</div>
                <div class="message-time">${time}</div>
            `;
            
            messagesEl.appendChild(messageEl);
            messagesEl.scrollTop = messagesEl.scrollHeight;
        }
        
        function updateConnectionInfo() {
            if (kefuWS) {
                const info = kefuWS.getConnectionStatus();
                document.getElementById('connectionInfo').textContent = JSON.stringify(info, null, 2);
            }
        }
        
        function connect() {
            const token = document.getElementById('token').value.trim();
            const shopId = parseInt(document.getElementById('shopId').value) || 0;
            
            if (!token) {
                alert('请输入客服token');
                return;
            }
            
            try {
                kefuWS = new KefuWebSocket({
                    url: 'wss://kefu.huohanghang.cn',
                    token: token,
                    shop_id: shopId,
                    client: 2
                });
                
                // 监听连接成功
                kefuWS.on('connected', () => {
                    updateStatus('已连接', true);
                    addMessage('WebSocket连接成功', 'system');
                    updateConnectionInfo();
                });
                
                // 监听登录成功
                kefuWS.on('login', (data) => {
                    addMessage('登录成功: ' + JSON.stringify(data), 'system');
                });
                
                // 监听聊天消息
                kefuWS.on('message', (data) => {
                    addMessage('收到消息: ' + JSON.stringify(data), 'received');
                });
                
                // 监听错误
                kefuWS.on('error', (error) => {
                    addMessage('错误: ' + JSON.stringify(error), 'system');
                });
                
                // 监听连接断开
                kefuWS.on('disconnected', () => {
                    updateStatus('连接断开', false);
                    addMessage('WebSocket连接断开', 'system');
                    updateConnectionInfo();
                });
                
                // 监听重连失败
                kefuWS.on('reconnect_failed', () => {
                    addMessage('重连失败，已达到最大重连次数', 'system');
                });
                
                updateStatus('连接中...', false);
                addMessage('正在建立WebSocket连接...', 'system');
                
            } catch (error) {
                addMessage('连接失败: ' + error.message, 'system');
            }
        }
        
        function disconnect() {
            if (kefuWS) {
                kefuWS.close();
                kefuWS = null;
                updateStatus('已断开连接', false);
                addMessage('主动断开连接', 'system');
                updateConnectionInfo();
            }
        }
        
        function sendMessage() {
            if (!kefuWS) {
                alert('请先连接WebSocket');
                return;
            }
            
            const toId = parseInt(document.getElementById('toId').value);
            const toType = document.getElementById('toType').value;
            const messageType = parseInt(document.getElementById('messageType').value);
            const content = document.getElementById('messageContent').value.trim();
            
            if (!toId || !content) {
                alert('请填写接收者ID和消息内容');
                return;
            }
            
            try {
                kefuWS.sendMessage(toId, toType, content, messageType);
                addMessage(`发送给${toType}(${toId}): ${content}`, 'sent');
                document.getElementById('messageContent').value = '';
            } catch (error) {
                addMessage('发送失败: ' + error.message, 'system');
            }
        }
        
        function clearMessages() {
            document.getElementById('messages').innerHTML = '';
        }
        
        // 定期更新连接信息
        setInterval(updateConnectionInfo, 1000);
        
        // 页面卸载时关闭连接
        window.addEventListener('beforeunload', () => {
            if (kefuWS) {
                kefuWS.close();
            }
        });
    </script>
</body>
</html>
