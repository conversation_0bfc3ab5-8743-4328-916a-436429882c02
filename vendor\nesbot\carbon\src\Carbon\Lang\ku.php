<?php

/**
 * This file is part of the Carbon package.
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

/*
 * Authors: <AUTHORS>
 */

return [
    'ago' => 'berî :time',
    'from_now' => 'di :time de',
    'after' => ':time piştî',
    'before' => ':time berê',
    'year' => ':count sal',
    'year_ago' => ':count salê|:count salan',
    'year_from_now' => 'salekê|:count salan',
    'month' => ':count meh',
    'week' => ':count hefte',
    'day' => ':count roj',
    'hour' => ':count saet',
    'minute' => ':count deqîqe',
    'second' => ':count saniye',
    'months' => ['rêbendanê', 'reşemiyê', 'adarê', 'avrêlê', 'gulanê', 'pûşper<PERSON>', 'tîrmeh<PERSON>', 'gelawêjê', 'rezberê', 'kewçêrê', 'sermawezê', 'berfanbarê'],
    'months_standalone' => ['rêbendan', 'reşemî', 'adar', 'avrêl', 'gulan', 'pûşper', 'tîrmeh', 'gelawêj', 'rezber', 'kewçêr', 'sermawez', 'berfanbar'],
    'months_short' => ['rêb', 'reş', 'ada', 'avr', 'gul', 'pûş', 'tîr', 'gel', 'rez', 'kew', 'ser', 'ber'],
    'weekdays' => ['yekşem', 'duşem', 'sêşem', 'çarşem', 'pêncşem', 'în', 'şemî'],
    'weekdays_short' => ['yş', 'dş', 'sş', 'çş', 'pş', 'în', 'ş'],
    'weekdays_min' => ['Y', 'D', 'S', 'Ç', 'P', 'Î', 'Ş'],
    'list' => [', ', ' û '],
    'first_day_of_week' => 6,
    'day_of_first_week_of_year' => 1,
];
