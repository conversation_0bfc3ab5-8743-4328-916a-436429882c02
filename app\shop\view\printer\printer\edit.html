{layout name="layout2" /}
<style>
    .tips {
        color: red;
    }
    .form-content {
        padding: 20px;
    }
</style>

<div class="layui-form layui-card form-content">
    <input name="id" value="{$detail.id}" type="hidden">
    <div class="layui-tab">
        <div class="layui-form-item">
            <label class="layui-form-label"><span class="tips">*</span>类型：</label>
            <div class="layui-input-block">
                <div class="layui-col-md4">
                    <select name="config_id" placeholder="请选择">
                        {foreach $type_list as $type}
                        <option value="{$type.id}">{$type.name}</option>
                        {/foreach}
                    </select>
                </div>
            </div>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label"><span class="tips">*</span>名称：</label>
        <div class="layui-input-block">
            <div class="layui-col-md4">
                <input type="text" name="name" value="{$detail.name}" lay-verify="required" lay-verType="tips" placeholder="请输入名称"
                       autocomplete="off" class="layui-input">
            </div>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label"></label>
        <span style="color: #a3a3a3;">自定义打印机名称</span>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label"><span class="tips">*</span>终端号：</label>
        <div class="layui-input-block">
            <div class="layui-col-md4">
                <input type="text" name="machine_code" value="{$detail.machine_code}" placeholder="请输入终端号" class="layui-input">
            </div>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label"><span class="tips">*</span>秘钥：</label>
        <div class="layui-input-block">
            <div class="layui-col-md4">
                <input type="text" name="private_key" value="{$detail.private_key}" placeholder="请输入秘钥" class="layui-input">
            </div>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label"><span class="tips">*</span>打印联数：</label>
        <div class="layui-input-block">
            <div class="layui-col-md4">
                <input type="number" name="print_number" value="{$detail.print_number}" placeholder="请输入打印联数" class="layui-input">
            </div>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label"></label>
        <span style="color: #a3a3a3;">打印小票的张数</span>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label"><span class="tips">*</span>自动打印：</label>
        <div class="layui-input-block">
            <div class="layui-col-md4">
                <input type="radio" name="auto_print" value="1" title="开启" {if condition="$detail.auto_print eq 1" }checked{/if}>
                <input type="radio" name="auto_print" value="0" title="关闭" {if condition="$detail.auto_print eq 0" }checked{/if}>
            </div>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label"></label>
        <span style="color: #a3a3a3;">自动打印订单，默认关闭</span>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label"><span class="tips">*</span>状态</label>
        <div class="layui-input-inline">
            <input type="radio" name="status" value="1" title="启用" {if condition="$detail.status eq 1" }checked{/if}>
            <input type="radio" name="status" value="0" title="停用" {if condition="$detail.status eq 0" }checked{/if}>
        </div>
    </div>
    <div class="layui-form-item layui-hide">
        <input type="button" lay-submit lay-filter="addSubmit" id="addSubmit" value="确认">
    </div>
</div>
