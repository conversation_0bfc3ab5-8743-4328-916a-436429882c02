{layout name="layout2" /}
<style>
  .layui-form-label {
    color: #6a6f6c;
    width: 140px;
  }
  .layui-input-block{
    margin-left:170px;
  }
  .reqRed::before {
    content: '*';
    color: red;
  }
</style>
<div class="layui-form" lay-filter="layuiadmin-form-qualification" id="layuiadmin-form-qualification" style="padding: 20px 30px 0 0;">
  <input type="hidden" name="id" value="{$detail.id}" />
  <div class="layui-form-item">
    <label class="layui-form-label reqRed">资质名称：</label>
    <div class="layui-input-inline">
      <input type="text" name="name" value="{$detail.name}" lay-verify="required" lay-verType="tips" placeholder="请输入资质名称" autocomplete="off" class="layui-input">
    </div>
  </div>
  <div class="layui-form-item">
    <label class="layui-form-label">资质描述：</label>
    <div class="layui-input-block">
      <textarea name="description" placeholder="请输入资质描述" class="layui-textarea">{$detail.description}</textarea>
    </div>
  </div>
  <div class="layui-form-item">
    <label class="layui-form-label">文档附件：</label>
    <div class="layui-input-block">
      <div class="layui-upload">
        <button type="button" class="layui-btn layui-btn-normal" id="upload-document">选择文档</button>
        <div class="layui-upload-list">
          <div id="document-list">
            {if $detail.document_path}
              <p style="color: #5FB878;"><i class="layui-icon layui-icon-file"></i> {$detail.document_name|default='已上传文档'} <a href="javascript:;" onclick="removeDocument()" style="color: #FF5722; margin-left: 10px;">[删除]</a></p>
            {/if}
          </div>
        </div>
        <input type="hidden" name="document_path" id="document_path" value="{$detail.document_path|default=''}">
        <input type="hidden" name="document_name" id="document_name" value="{$detail.document_name|default=''}">
      </div>
      <div class="layui-form-mid layui-word-aux">支持格式：pdf、doc、docx、xls、xlsx、ppt、pptx等，大小不超过10MB</div>
    </div>
  </div>
  <div class="layui-form-item" style="display: none;">
    <label class="layui-form-label">有效期：</label>
    <div class="layui-input-inline">
      <input type="number" name="valid_days" value="{$detail.valid_days}" min="0" placeholder="天数，0表示永久有效" class="layui-input">
    </div>
    <div class="layui-form-mid layui-word-aux">天（0表示永久有效）</div>
  </div>
  <div class="layui-form-item">
    <label class="layui-form-label">排序：</label>
    <div class="layui-input-inline">
      <input type="number" name="sort" value="{$detail.sort}" min="0" class="layui-input">
    </div>
    <div class="layui-form-mid layui-word-aux">数值越小，排序越靠前</div>
  </div>
  <div class="layui-form-item">
    <label class="layui-form-label">是否必传：</label>
    <div class="layui-input-inline">
      <input type="radio" name="is_required" value="1" title="必传" {if $detail.is_required==1}checked{/if} />
      <input type="radio" name="is_required" value="0" title="非必传" {if $detail.is_required==0}checked{/if}>
    </div>
    <div class="layui-form-mid layui-word-aux">必传资质商家必须上传，否则无法发布商品</div>
  </div>
  <div class="layui-form-item">
    <label class="layui-form-label">状态：</label>
    <div class="layui-input-inline">
      <input type="radio" name="status" value="1" title="启用" {if $detail.status==1}checked{/if} />
      <input type="radio" name="status" value="0" title="禁用" {if $detail.status==0}checked{/if}>
    </div>
  </div>
  <div class="layui-form-item layui-hide">
      <input type="button" lay-submit lay-filter="qualification-submit-edit" id="qualification-submit-edit" value="确认">
  </div>
</div>
<script>
  layui.config({
    version:"{$front_version}",
    base: '/static/lib' //静态资源所在路径
  }).use(['form', 'upload'], function(){
    var $ = layui.$;
    var form = layui.form;
    var upload = layui.upload;

    // 文档上传
    upload.render({
      elem: '#upload-document',
      url: '{:url("Upload/document")}',
      accept: 'file',
      exts: 'pdf|doc|docx|xls|xlsx|ppt|pptx|txt|zip|rar',
      size: 10240, // 10MB
      done: function(res){
        if(res.code === 1) {
          $('#document_path').val(res.data.uri);
          $('#document_name').val(res.data.name);
          $('#document-list').html('<p style="color: #5FB878;"><i class="layui-icon layui-icon-file"></i> ' + res.data.name + ' <a href="javascript:;" onclick="removeDocument()" style="color: #FF5722; margin-left: 10px;">[删除]</a></p>');
          layer.msg('文档上传成功');
        } else {
          layer.msg(res.msg || '上传失败');
        }
      },
      error: function(){
        layer.msg('上传失败');
      }
    });
  });

  // 删除文档
  function removeDocument() {
    $('#document_path').val('');
    $('#document_name').val('');
    $('#document-list').html('');
    layer.msg('文档已删除');
  }
</script>
