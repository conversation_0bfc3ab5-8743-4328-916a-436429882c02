{layout name="layout1" /}
<div class="wrapper">
    <div class="layui-card layui-form">
        <div class="layui-card-body">
            <div class="layui-collapse like-layui-collapse" style="border:1px dashed #c4c4c4">
                <div class="layui-colla-item">
                    <h2 class="layui-colla-title like-layui-colla-title" style="background-color: #fff">温馨提示：</h2>
                    <div class="layui-colla-content layui-show">
                        <p>1.使用对象存储，需要将public目录下的资源文件保留原来目录路径传输到对象存储空间。</p>
                        <p>2.请勿随意切换存储方式，可能导致图片无法查看。</p>
                        <p>3.需要在对象存储后台设置域名跨域，否则图片生成场景无法使用，例海报合成等。</p>
                        <p>4.需将对象存储的图片域名添加到微信小程序官方后台request合法域名和downloadFile合法域名。</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-form layui-card-header layuiadmin-card-header-auto">
            <h1 class="site-h1">上传设置</h1>
        </div>

        <div class="layui-card-body" id="card-body">
            <table id="storage-lists" lay-filter="storage-lists"></table>
            <script type="text/html" id="table-status">
                <input type="checkbox"  lay-filter="switch-status" data-engine='{{d.engine}}' lay-skin="switch"
                       lay-text="是|否" {{#  if(d.status){ }} checked  {{#  } }} />
            </script>
            <script type="text/html" id="operation">
                <a class="layui-btn layui-btn-normal layui-btn-sm" lay-event="edit">配置</a>
            </script>
        </div>

    </div>
</div>


<script>
    layui.use(['table','form'], function(){
        var $ = layui.$
            ,form = layui.form
            ,table = layui.table;


        like.tableLists('#storage-lists', '{:url("setting.StorageConfig/lists")}', [
            {field: 'name', width: 150, title: '存储方式'}
            ,{field: 'path', width: 320, title: '存储位置'}
            ,{field: 'status', title: '状态', width: 100, templet: '#table-status', align: 'center'}
            ,{fixed: 'right', title: '操作', width: 150, align: 'center', toolbar: '#operation'}
        ],{}, false);
        
        table.on('tool(storage-lists)', function(obj){
            if(obj.event === 'edit'){
                layer.open({
                    type: 2
                    ,title: '编辑' + obj.data.name
                    ,content: '{:url("setting.StorageConfig/edit")}?engine=' + obj.data.engine
                    ,area: ['90%', '90%']
                    ,btn: ['确定', '取消']
                    ,yes: function(index, layero){
                        var iframeWindow = window['layui-layer-iframe'+ index];
                        var submit = layero.find('iframe').contents().find('#addSubmit');
                        //监听提交
                        iframeWindow.layui.form.on('submit(addSubmit)', function(data){
                            var field = data.field;
                            if (field['engine'] === 'local') {
                                layer.close(index);
                                return true;
                            }
                            like.ajax({
                                url:'{:url("setting.StorageConfig/edit")}',
                                data:field,
                                type:"post",
                                success:function(res) {
                                    if(res.code === 1) {
                                        layui.layer.msg(res.msg, {
                                            offset: '15px'
                                            , icon: 1
                                            , time: 1000
                                        });
                                        layer.close(index);
                                        table.reload('storage-lists');
                                    }
                                }
                            });
                        });
                        submit.trigger('click');
                    }
                })
            }
        });

        form.on('switch(switch-status)',function (obj) {
            var engine = obj.elem.attributes['data-engine'].nodeValue;
            if(!this.checked) {
                engine = '';
                layer.confirm('您确定要停用吗？停用后请尽快开启其他存储方式', {
                    btn: ['确定','取消']
                }, function(){
                    like.ajax({
                        url: '{:url("setting.StorageConfig/changeEngine")}',
                        data: {engine: engine},
                        type: 'post',
                        dataType: 'json',
                        success: function (res) {
                            if (res.code === 1) {
                                layui.layer.msg(res.msg, {
                                    offset: '15px'
                                    , icon: 1
                                    , time: 1000
                                });
                                table.reload('storage-lists');
                            }
                        }
                    })
                }, function(){
                    table.reload('storage-lists');
                });
            } else {
                like.ajax({
                    url: '{:url("setting.StorageConfig/changeEngine")}',
                    data: {engine: engine},
                    type: 'post',
                    dataType: 'json',
                    success: function (res) {
                        if (res.code === 1) {
                            layui.layer.msg(res.msg, {
                                offset: '15px'
                                , icon: 1
                                , time: 1000
                            });
                            table.reload('storage-lists');
                        }
                    }
                })
            }
        })
    });

</script>