{extend name="public/base" /}

{block name="content"}
<div class="layui-fluid">
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-header">
                    <h3>商家权限商家类型管理</h3>
                    <div class="layui-card-header-tips">
                        <p>* 可以设置不同权限对应的商家类型访问控制</p>
                        <p>* 0元入驻：免费商家；商家会员：付费会员；实力厂商：最高等级</p>
                    </div>
                </div>
                <div class="layui-card-body">
                    <!-- 统计信息 -->
                    <div class="layui-row layui-col-space10" style="margin-bottom: 20px;">
                        <div class="layui-col-md2">
                            <div class="layui-card">
                                <div class="layui-card-body" style="text-align: center;">
                                    <div style="font-size: 20px; font-weight: bold; color: #1E9FFF;" id="total-auths">-</div>
                                    <div>总权限数</div>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md2">
                            <div class="layui-card">
                                <div class="layui-card-body" style="text-align: center;">
                                    <div style="font-size: 20px; font-weight: bold; color: #5FB878;" id="all-merchants">-</div>
                                    <div>全部商家</div>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md2">
                            <div class="layui-card">
                                <div class="layui-card-body" style="text-align: center;">
                                    <div style="font-size: 20px; font-weight: bold; color: #FF5722;" id="paid-only">-</div>
                                    <div>仅付费商家</div>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md2">
                            <div class="layui-card">
                                <div class="layui-card-body" style="text-align: center;">
                                    <div style="font-size: 20px; font-weight: bold; color: #FFB800;" id="member-plus">-</div>
                                    <div>会员以上</div>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md2">
                            <div class="layui-card">
                                <div class="layui-card-body" style="text-align: center;">
                                    <div style="font-size: 20px; font-weight: bold; color: #999;" id="free-only">-</div>
                                    <div>仅免费商家</div>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md2">
                            <div class="layui-card">
                                <div class="layui-card-body" style="text-align: center;">
                                    <button class="layui-btn layui-btn-normal layui-btn-sm" onclick="refreshStats()">
                                        <i class="layui-icon layui-icon-refresh"></i> 刷新
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 快捷操作按钮 -->
                    <div class="layui-form-item">
                        <div class="layui-btn-group">
                            <button class="layui-btn" onclick="batchSetMerchantType(['0','1','2'])">
                                <i class="layui-icon layui-icon-ok"></i> 批量设为全部商家
                            </button>
                            <button class="layui-btn layui-btn-warm" onclick="batchSetMerchantType(['1','2'])">
                                <i class="layui-icon layui-icon-vip"></i> 批量设为付费商家
                            </button>
                            <button class="layui-btn layui-btn-normal" onclick="batchSetMerchantType(['2'])">
                                <i class="layui-icon layui-icon-diamond"></i> 批量设为实力厂商
                            </button>
                            <button class="layui-btn layui-btn-primary" onclick="batchSetMerchantType(['0'])">
                                <i class="layui-icon layui-icon-user"></i> 批量设为免费商家
                            </button>
                        </div>
                    </div>

                    <!-- 数据表格 -->
                    <table class="layui-hide" id="authTable" lay-filter="authTable"></table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 表格操作模板 -->
<script type="text/html" id="toolbarDemo">
    <div class="layui-btn-container">
        <button class="layui-btn layui-btn-sm" lay-event="batchAll">批量设为全部商家</button>
        <button class="layui-btn layui-btn-warm layui-btn-sm" lay-event="batchPaid">批量设为付费商家</button>
        <button class="layui-btn layui-btn-normal layui-btn-sm" lay-event="batchPremium">批量设为实力厂商</button>
    </div>
</script>

<script type="text/html" id="merchantTypeTpl">
    <span class="layui-badge {{d.type_class}}">{{d.type_text}}</span>
    <br><small style="color: #999;">{{d.merchant_type_names}}</small>
</script>

<script type="text/html" id="operationTpl">
    <div class="layui-btn-group">
        <a class="layui-btn layui-btn-xs" lay-event="setAll">全部</a>
        <a class="layui-btn layui-btn-warm layui-btn-xs" lay-event="setPaid">付费</a>
        <a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="setPremium">实力</a>
        <a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="setFree">免费</a>
    </div>
</script>

{/block}

{block name="script"}
<script>
layui.use(['table', 'layer'], function(){
    var table = layui.table;
    var layer = layui.layer;

    // 渲染表格
    var tableIns = table.render({
        elem: '#authTable',
        url: '{:url("index")}',
        toolbar: '#toolbarDemo',
        defaultToolbar: ['filter', 'exports', 'print'],
        cols: [[
            {type: 'checkbox', fixed: 'left'},
            {field: 'id', title: 'ID', width: 60, sort: true},
            {field: 'name', title: '权限名称', width: 150},
            {field: 'uri', title: '权限路径', width: 200},
            {field: 'merchant_type_names', title: '允许商家类型', width: 200, templet: '#merchantTypeTpl'},
            {field: 'sort', title: '排序', width: 80},
            {title: '操作', width: 200, toolbar: '#operationTpl', align: 'center'}
        ]],
        page: true,
        height: 'full-220'
    });

    // 头工具栏事件
    table.on('toolbar(authTable)', function(obj){
        var checkStatus = table.checkStatus(obj.config.id);
        var data = checkStatus.data;
        
        switch(obj.event){
            case 'batchAll':
                if(data.length === 0){
                    layer.msg('请选择要设置的权限');
                    return;
                }
                batchSetMerchantTypeByData(data, ['0','1','2']);
                break;
            case 'batchPaid':
                if(data.length === 0){
                    layer.msg('请选择要设置的权限');
                    return;
                }
                batchSetMerchantTypeByData(data, ['1','2']);
                break;
            case 'batchPremium':
                if(data.length === 0){
                    layer.msg('请选择要设置的权限');
                    return;
                }
                batchSetMerchantTypeByData(data, ['2']);
                break;
        }
    });

    // 行工具栏事件
    table.on('tool(authTable)', function(obj){
        var data = obj.data;
        
        switch(obj.event){
            case 'setAll':
                setSingleMerchantType(data.id, ['0','1','2'], data.name);
                break;
            case 'setPaid':
                setSingleMerchantType(data.id, ['1','2'], data.name);
                break;
            case 'setPremium':
                setSingleMerchantType(data.id, ['2'], data.name);
                break;
            case 'setFree':
                setSingleMerchantType(data.id, ['0'], data.name);
                break;
        }
    });

    // 批量设置商家类型（通过按钮）
    window.batchSetMerchantType = function(types) {
        var checkStatus = table.checkStatus('authTable');
        var data = checkStatus.data;
        
        if(data.length === 0){
            layer.msg('请选择要设置的权限');
            return;
        }
        
        batchSetMerchantTypeByData(data, types);
    };

    // 批量设置商家类型（通过数据）
    function batchSetMerchantTypeByData(data, types) {
        var auth_ids = data.map(function(item) {
            return item.id;
        });
        
        var typeNames = {
            '0': '0元入驻',
            '1': '商家会员',
            '2': '实力厂商'
        };
        var typeText = types.map(function(type) {
            return typeNames[type];
        }).join('、');
        
        layer.confirm('确定要将选中的 ' + data.length + ' 个权限设置为允许【' + typeText + '】访问吗？', function(index){
            layer.close(index);
            
            var loading = layer.load(2);
            $.post('{:url("batchSet")}', {
                auth_ids: auth_ids,
                merchant_types: types
            }, function(res) {
                layer.close(loading);
                if(res.code === 0) {
                    layer.msg(res.msg, {icon: 1});
                    tableIns.reload();
                    refreshStats();
                } else {
                    layer.msg(res.msg, {icon: 2});
                }
            });
        });
    }

    // 单个设置商家类型
    function setSingleMerchantType(auth_id, types, auth_name) {
        var typeNames = {
            '0': '0元入驻',
            '1': '商家会员',
            '2': '实力厂商'
        };
        var typeText = types.map(function(type) {
            return typeNames[type];
        }).join('、');
        
        layer.confirm('确定要将权限"' + auth_name + '"设置为允许【' + typeText + '】访问吗？', function(index){
            layer.close(index);
            
            var loading = layer.load(2);
            $.post('{:url("setSingle")}', {
                auth_id: auth_id,
                merchant_types: types
            }, function(res) {
                layer.close(loading);
                if(res.code === 0) {
                    layer.msg(res.msg, {icon: 1});
                    tableIns.reload();
                    refreshStats();
                } else {
                    layer.msg(res.msg, {icon: 2});
                }
            });
        });
    }

    // 刷新统计数据
    window.refreshStats = function() {
        $.get('{:url("getStats")}', function(res) {
            if(res.code === 0) {
                var data = res.data;
                $('#total-auths').text(data.total);
                $('#all-merchants').text(data.all_merchants);
                $('#paid-only').text(data.paid_only);
                $('#member-plus').text(data.member_plus);
                $('#free-only').text(data.free_only);
            }
        });
    };

    // 页面加载时获取统计数据
    refreshStats();
});
</script>
{/block}
