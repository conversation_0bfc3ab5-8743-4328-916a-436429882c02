<?php
namespace app\common\command;

use app\common\library\MeiliSearch;
use app\common\library\AliNlpService;
use app\common\model\goods\Goods;
//use app\common\model\SearchRecord;
use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\facade\Db;
use think\facade\Log;

/**
 * 更新商品分词和搜索候选词索引
 * 定时任务，用于定期更新商品分词和搜索候选词索引
 */
class UpdateSearchSuggestions extends Command
{
    /**
     * 配置指令
     */
    protected function configure()
    {
        $this->setName('update_search_suggestions')
            ->setDescription('更新商品分词和搜索候选词索引');
    }

    // 批量处理的商品数量
    protected $batchSize = 100;

    // 批量导入的候选词数量
    protected $importBatchSize = 1000;

    // 不使用缓存，直接处理所有数据
    protected function execute(Input $input, Output $output)
    {
        try {
            $startTime = microtime(true);
            $output->writeln('开始更新搜索候选词索引...');

            // 初始化MeiliSearch客户端
            $meili = new MeiliSearch();

            // 检查索引是否存在
            $indexInfo = $meili->getIndex('search_suggestions');
            if (!$indexInfo || isset($indexInfo['error'])) {
                $output->writeln('搜索候选词索引不存在，正在创建...');

                // 创建新索引，明确指定主键为id
                $response = $meili->createIndex('search_suggestions', ['primaryKey' => 'id']);

                if (isset($response['error'])) {
                    $output->writeln('创建索引失败：' . json_encode($response));
                    return false;
                }

                // 更新索引设置
                $settings = [
                    'searchableAttributes' => [
                        'text',
                        'tags'
                    ],
                    'sortableAttributes' => [
                        'weight',
                        'popularity'
                    ],
                    'rankingRules' => [
                        'words',
                        'typo',
                        'proximity',
                        'attribute',
                        'sort',
                        'exactness'
                    ]
                ];
                $response = $meili->updateIndexSettings('search_suggestions', $settings);

                if (isset($response['error'])) {
                    $output->writeln('创建索引失败：' . json_encode($response));
                    return false;
                }

                $output->writeln('索引创建成功，等待索引就绪...');
                // 等待索引就绪
                sleep(2);
            }

            // 初始化阿里云NLP服务
            $aliNlp = new AliNlpService();
            $output->writeln('已初始化阿里云NLP服务');

            // 获取商品数据
            $goods = Goods::where([
                ['del', '=', 0],
                ['status', '=', 1],
                ['audit_status', '=', 1]
            ])
            ->field('id, name, split_word, sales_actual, sales_virtual, update_time')
            ->select()
            ->toArray();

            $totalGoods = count($goods);
            $output->writeln("共有 {$totalGoods} 个商品需要处理");

            // 更新商品分词
            $updatedCount = 0;
            foreach ($goods as &$item) {
                // 如果分词为空或者最后更新时间超过7天，则更新分词
                if (empty($item['split_word']) || (time() - $item['update_time'] > 7 * 86400)) {
                    try {
                        // 使用阿里云NLP服务进行分词
                        $segmentResult = $aliNlp->segment($item['name']);
                        $nerResult = $aliNlp->nerEcom($item['name']);
                        
                        // 合并分词结果
                        $words = [];
                        if (isset($segmentResult['words'])) {
                            // 处理可能是对象或数组的情况
                            if (is_array($segmentResult['words'])) {
                                if (array_keys($segmentResult['words']) === range(0, count($segmentResult['words']) - 1)) {
                                    // 索引数组
                                    $words = array_merge($words, $segmentResult['words']);
                                } else {
                                    // 关联数组
                                    $words = array_merge($words, array_values($segmentResult['words']));
                                }
                            }
                        }
                        
                        if (isset($nerResult['words'])) {
                            // 处理可能是对象或数组的情况
                            if (is_array($nerResult['words'])) {
                                if (array_keys($nerResult['words']) === range(0, count($nerResult['words']) - 1)) {
                                    // 索引数组
                                    $words = array_merge($words, $nerResult['words']);
                                } else {
                                    // 关联数组
                                    $words = array_merge($words, array_values($nerResult['words']));
                                }
                            }
                        }
                        
                        // 去重
                        $words = array_unique($words);
                        
                        // 过滤掉长度小于2的词和非字符串值
                        $words = array_filter($words, function($word) {
                            return is_string($word) && mb_strlen($word, 'UTF-8') >= 2;
                        });
                        
                        // 更新商品分词字段
                        $splitWord = implode(',', $words);
                        Db::name('goods')->where('id', $item['id'])->update([
                            'split_word' => $splitWord,
                            'update_time' => time()
                        ]);
                        
                        // 更新当前商品对象的分词字段
                        $item['split_word'] = $splitWord;
                        $updatedCount++;
                    } catch (\Exception $e) {
                        Log::error("更新商品分词失败 [ID: {$item['id']}]: " . $e->getMessage());
                    }
                }
            }
            
            $output->writeln("商品分词更新完成！共更新 {$updatedCount} 个商品的分词");

            // 生成简单的候选词
            $suggestions = [];
            $id = 1;

            foreach ($goods as $item) {
                // 确保销量字段是整数
                $item['sales_actual'] = (int)$item['sales_actual'];
                $item['sales_virtual'] = (int)$item['sales_virtual'];
                
                // 添加商品名称
                $suggestions[] = [
                    'id' => $id++,
                    'text' => $item['name'],
                    'tags' => '商品名称',
                    'weight' => 50,
                    'popularity' => (int)($item['sales_actual'] + $item['sales_virtual']),
                    'goods_id' => (int)$item['id'],
                    'updated_at' => (int)time()
                ];
                
                // 添加分词
                if (!empty($item['split_word'])) {
                    $splitWords = explode(',', $item['split_word']);
                    foreach ($splitWords as $word) {
                        if (mb_strlen($word, 'UTF-8') >= 2) {
                            $suggestions[] = [
                                'id' => $id++,
                                'text' => $word,
                                'tags' => '分词',
                                'weight' => 80,
                                'popularity' => (int)($item['sales_actual'] + $item['sales_virtual']),
                                'goods_id' => (int)$item['id'],
                                'updated_at' => (int)time()
                            ];
                        }
                    }
                }
            }
            
            $output->writeln("生成了 " . count($suggestions) . " 个候选词");

            // 获取热门搜索词
            $hotKeywords = Db::name('search_record')
                ->field('keyword, COUNT(*) as count')
                ->where('del', 0)
                ->group('keyword')
                ->order('count DESC')
                ->limit(100)
                ->select()
                ->toArray();

            if (!empty($hotKeywords)) {
                $output->writeln('找到 ' . count($hotKeywords) . ' 个热门搜索词');
                
                foreach ($hotKeywords as $item) {
                    $keyword = $item['keyword'];
                    if (mb_strlen($keyword, 'UTF-8') >= 2) {
                        // 添加热门搜索词
                        $suggestions[] = [
                            'id' => $id++,
                            'text' => $keyword,
                            'tags' => '热门搜索',
                            'weight' => 90,
                            'popularity' => (int)$item['count'],
                            'goods_id' => 0,
                            'updated_at' => (int)time()
                        ];
                        
                        // 如果关键词太长，截取前10个字符
                        if (mb_strlen($keyword, 'UTF-8') > 10) {
                            $shortKeyword = mb_substr($keyword, 0, 10, 'UTF-8');
                            $suggestions[] = [
                                'id' => $id++,
                                'text' => $shortKeyword,
                                'tags' => '热门搜索短语',
                                'weight' => 95,
                                'popularity' => (int)$item['count'],
                                'goods_id' => 0,
                                'updated_at' => (int)time()
                            ];
                        }
                    }
                }
            }
            
            // 导入候选词
            if (!empty($suggestions)) {
                $output->writeln('导入 ' . count($suggestions) . ' 个候选词到索引...');
                $response = $meili->importDocuments('search_suggestions', $suggestions, 'id');
                
                if (isset($response['taskUid'])) {
                    $output->writeln("导入成功，任务ID：{$response['taskUid']}");
                } else {
                    $output->writeln("导入失败：" . json_encode($response));
                }
            }

            $endTime = microtime(true);
            $executionTime = round($endTime - $startTime, 2);
            $output->writeln("搜索候选词索引更新完成！执行时间: {$executionTime} 秒");
            return true;
        } catch (\Exception $e) {
            Log::error('更新搜索候选词索引异常: ' . $e->getMessage());
            $output->writeln('更新搜索候选词索引异常: ' . $e->getMessage());
            return false;
        }
    }
}
