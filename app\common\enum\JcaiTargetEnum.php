<?php

namespace app\common\enum;


/**
 * 众筹目标类型枚举
 * Class JcaiTargetEnum
 * @package app\common\enum
 */
class JcaiTargetEnum
{
    const COUNT = 1; // 按件数
    const AMOUNT = 2; // 按金额

    /**
     * @notes 获取描述
     * @param int $value
     * @return string
     */
    public static function getDesc($value): string
    {
        switch ($value) {
            case self::COUNT:
                return '按件数';
            case self::AMOUNT:
                return '按金额';
            default:
                return '未知类型';
        }
    }

    /**
     * @notes 获取列表
     * @return array[]
     */
    public static function getList(): array
    {
        return [
            ['value' => self::COUNT, 'desc' => '按件数'],
            ['value' => self::AMOUNT, 'desc' => '按金额'],
        ];
    }
} 