{layout name="layout1" /}


<div class="wrapper">
    <div class="layui-card">
        <div class="layui-card-body">
            <!--操作提示-->
            <div class="layui-card-body">
                <div class="layui-collapse like-layui-collapse" lay-accordion="" style="border:1px dashed #c4c4c4">
                    <div class="layui-colla-item">
                        <h2 class="layui-colla-title like-layui-colla-title" style="background-color: #fff">操作提示</h2>
                        <div class="layui-colla-content layui-show">
                            <p>*填写的备案信息将会在小程序里展示，请填写完整信息</p>
                            <p>*上传的资质信息将会在小程序里显示，请上传清晰的图片</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="layui-form" lay-filter="">
                <div class="layui-form-item">
                    <fieldset class="layui-elem-field layui-field-title">
                        <legend>备案信息</legend>
                    </fieldset>
                </div>
                <div class="layui-form-item layui-col-sm6 layui-col-md4">
                    <label class="layui-form-label">版权信息：</label>
                    <div class="layui-input-block">
                        <input type="text" name="company_name" lay-verType="tips" autocomplete="off"
                               value="{$config.company_name}" class="layui-input">
                    </div>
                    <div class=" layui-form-mid layui-word-aux" style="left:110px">Copyright © 2019-2020 公司名称</div>
                </div>

                <div class="layui-form-item layui-col-sm6 layui-col-md4">
                    <label class="layui-form-label" style="white-space: nowrap;">ICP备案号：</label>
                    <div class="layui-input-block">
                        <input type="text" name="number" lay-verType="tips" autocomplete="off" value="{$config.number}"
                               class="layui-input">
                    </div>
                </div>

                <div class="layui-form-item layui-col-sm6 layui-col-md4">
                    <label class="layui-form-label" style="white-space: nowrap;">备案号链接：</label>
                    <div class="layui-input-block">
                        <input type="text" name="link" lay-verType="tips" autocomplete="off" value="{$config.link}"
                               class="layui-input">
                    </div>
                    <div class=" layui-form-mid layui-word-aux" style="left:110px">
                        域名信息备案系统链接。http://beian.miit.gov.cn/
                    </div>
                </div>


                <div class="layui-form-item">
                    <fieldset class="layui-elem-field layui-field-title">
                        <legend>资质信息</legend>
                    </fieldset>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">营业执照：</label>
                    <div class="layui-input-inline">
                        <div class="like-upload-image">
                            {if !empty($config.business_license)}
                            <div class="upload-image-div">
                                <img src="{$config.business_license}" alt="img" style="background-color:#EEEEEE;height: 80px;width:auto">
                                <input name="business_license" type="hidden" value="{$config.business_license}">
                                <div class="del-upload-btn">x</div>
                            </div>
                            <div class="upload-image-elem" style="display:none;"><a class="add-upload-image business_license"> + 添加图片</a></div>
                            {else}
                            <div class="upload-image-elem"><a class="add-upload-image business_license"> + 添加图片</a></div>
                            {/if}
                        </div>
                    </div>
                </div>
                <div class="layui-form-item" style="margin-bottom: 0px">
                    <label class="layui-form-label">其他资质：</label>
                    <div class="layui-input-block" id="qualifications_images">
                        {if !empty($config.other_qualifications)}
                        {foreach $config.other_qualifications as $val}
                        <div class="upload-image-div">
                            <img src="{$val}" alt="img" />
                            <input type="hidden" name="other_qualifications[]" value="{$val}">
                            <div class="del-upload-btn">x</div>
                        </div>
                        {/foreach}
                        {/if}
                        <div class="like-upload-image">
                            <div class="upload-image-elem"><a class="add-upload-image" id="other_qualifications"> + 添加图片</a></div>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"></label>
                    <span style="color: #a3a3a3;font-size: 9px">最多上传5张</span>
                </div>

                <div class="layui-form-item">
                    <div class="layui-input-block">
                        <button class="layui-btn layui-btn-sm {$view_theme_color}" lay-submit lay-filter="setSubmit">确认
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


<script>
    layui.use(['table'], function () {
        var $ = layui.$
            , form = layui.form;

        form.on('submit(setSubmit)', function (data) {
            like.ajax({
                url: '{:url("setting.Basic/setCopyright")}'
                , data: data.field
                , type: 'post'
                , success: function (res) {
                    layer.msg(res.msg, {
                        offset: '15px'
                        , icon: 1
                        , time: 1500
                    }, function () {
                        location.href = location.href;
                    });
                }
            });
        });


        like.delUpload();
        $(document).on("click", ".business_license", function () {
            like.imageUpload({
                limit: 1,
                field: "business_license",
                that: $(this)
            });
        });
        $(document).on("click", "#other_qualifications", function () {
            like.imageUpload({
                limit: 5,
                field: "other_qualifications[]",
                that: $(this),
                content: '/admin/file/lists?type=10'
            });
        })

    });

</script>