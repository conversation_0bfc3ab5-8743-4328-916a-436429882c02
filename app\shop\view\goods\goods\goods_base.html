<div class="layui-tab-item goods-content layui-show">
    <div class="layui-card-body" pad15>
        <div>
            <!-- 商品类型 -->
            <!-- <div class="layui-form-item" >
                <label class="layui-form-label" ><span class="form-label-asterisk" style="display: none;">*</span>商品类型：</label>
                <div class="layui-input-block">
                    <!-- <input type="radio" name="type" style="display: none;"  value="0" title="实物商品" lay-filter="goods_type" class="layui-input" checked/> -->
                    <!-- <input type="radio" name="type" value="1" title="虚拟商品" lay-filter="goods_type" class="layui-input"  /> -->
                <!-- </div>
            </div>  -->
            <!-- <div class="layui-form-item">
                <label class="layui-form-label"></label>
                <span style="color: #a3a3a3;font-size: 9px">选择好商品类型之后，编辑时不能修改类型。请谨慎选择</span>
            </div> -->
            <!--商品名称-->
            <div class="layui-form-item">
                <label class="layui-form-label"><span class="form-label-asterisk">*</span>商品名称：</label>
                <div class="layui-input-block">
                    <input name="goods_id" type="hidden">
                    <input type="text" name="name" lay-verify="custom_required" lay-verType="tips"
                           autocomplete="off" maxlength="64"
                           switch-tab="0" verify-msg="请输入商品名称，最多64个字符" placeholder="请输入商品名称，最少3个字符，最多64个字符"
                           class="layui-input">
                </div>
                 <input type="hidden" name="type"  value="0" title="实物商品" lay-filter="goods_type" class="layui-input" checked/>
            </div>
            <!--商品编码-->
            <div class="layui-form-item">
                <label class="layui-form-label">商品编码：</label>
                <div class="layui-input-block">
                    <input type="text" name="code" lay-verType="tips" placeholder="若不填，系统随机8位数字" autocomplete="off" switch-tab="0" class="layui-input">
                </div>
            </div>
            <!--平台分类-->
            <div class="layui-form-item">
                <label class="layui-form-label"><span class="form-label-asterisk">*</span>平台分类：</label>
                <div class="layui-input-inline">
                    <select name="first_cate_id" lay-filter="first_category" lay-verify="custom_required"
                            lay-verType="tips" switch-tab="0" verify-msg="请选择分类">
                        <option value="">请选择分类</option>
                    </select>
                </div>
                <div class="layui-input-inline">
                    <select name="second_cate_id" lay-filter="second_category" switch-tab="0" verify-msg="请选择分类">
                        <option value="">请选择分类</option>
                    </select>
                </div>
                <div class="layui-input-inline">
                    <select name="third_cate_id" lay-filter="third_category" switch-tab="0" verify-msg="请选择分类">
                        <option value="">请选择分类</option>
                    </select>
                </div>
            </div>

            <!-- 资质提醒区域 -->
            <div class="layui-form-item" id="qualification-alert" style="display: none;">
                <label class="layui-form-label"></label>
                <div class="layui-input-block">
                    <div class="qualification-alert-container">
                        <div id="qualification-message"></div>
                        <div id="qualification-actions" style="margin-top: 15px;"></div>
                    </div>
                </div>
            </div>

            <!-- 资质提醒样式 -->
            <style>
            .qualification-alert-container {
                border: 1px solid #faad14;
                border-radius: 6px;
                background: #fffbe6;
                padding: 16px;
                margin-bottom: 10px;
            }

            .qualification-item {
                margin-bottom: 15px;
                padding: 12px;
                border: 1px solid #e8e8e8;
                border-radius: 4px;
                background: #fafafa;
            }

            .qualification-item.required {
                border-color: #ff4d4f;
                background: #fff2f0;
            }

            .qualification-item.optional {
                border-color: #faad14;
                background: #fffbe6;
            }

            .qualification-item.expired {
                border-color: #ff7875;
                background: #fff1f0;
            }

            .qualification-header {
                display: flex;
                align-items: center;
                margin-bottom: 8px;
            }

            .qualification-title {
                font-weight: bold;
                font-size: 14px;
                margin-right: 8px;
            }

            .qualification-badge {
                padding: 2px 6px;
                border-radius: 2px;
                font-size: 11px;
                color: white;
            }

            .qualification-badge.required {
                background: #ff4d4f;
            }

            .qualification-badge.optional {
                background: #faad14;
            }

            .qualification-description {
                color: #666;
                font-size: 12px;
                line-height: 1.5;
                margin-bottom: 8px;
            }

            .qualification-template {
                margin-bottom: 8px;
            }

            .qualification-template-btn {
                display: inline-flex;
                align-items: center;
                padding: 4px 8px;
                background: #e6f7ff;
                border: 1px solid #91d5ff;
                border-radius: 3px;
                color: #1890ff;
                text-decoration: none;
                font-size: 12px;
                transition: all 0.3s;
            }

            .qualification-template-btn:hover {
                background: #bae7ff;
                border-color: #69c0ff;
                color: #096dd9;
                text-decoration: none;
            }

            .qualification-template-btn i {
                margin-right: 4px;
            }

            .qualification-actions {
                display: flex;
                gap: 8px;
                flex-wrap: wrap;
            }

            .qualification-upload-btn {
                padding: 6px 12px;
                border-radius: 3px;
                font-size: 12px;
                border: none;
                cursor: pointer;
                transition: all 0.3s;
            }

            .qualification-upload-btn.required {
                background: #1890ff;
                color: white;
            }

            .qualification-upload-btn.required:hover {
                background: #40a9ff;
            }

            .qualification-upload-btn.optional {
                background: #faad14;
                color: white;
            }

            .qualification-upload-btn.optional:hover {
                background: #ffc53d;
            }
            </style>
            <!--商品分类-->
            <div class="layui-form-item" style="display:none;">
                <label class="layui-form-label">商品分类：</label>
                <div class="layui-input-inline">
                    <select name="shop_cate_id" lay-filter="shop_cate_id" switch-tab="0" verify-msg="请选择分类">
                    </select>
                </div>
            </div>
            <!--商品卖点-->
            <div class="layui-form-item">
                <label class="layui-form-label">商品卖点：</label>
                <div class="layui-input-block">
                    <input type="text" maxlength="60" name="remark"  autocomplete="off" class="layui-input">
                </div>
            </div>
            <!--商品单位-->
            <!-- <div class="layui-form-item">
                <label class="layui-form-label">商品单位：</label>
                <div class="layui-input-inline">
                    <select name="unit_id"  switch-tab="0" verify-msg="请选择分类">
                        <option value="">请选择单位</option>
                    </select>
                </div>
            </div> -->
            <!--商品品牌-->
            <!-- <div class="layui-form-item">
                <label class="layui-form-label">商品品牌：</label>
                <div class="layui-input-inline">
                    <select name="brand_id" lay-verType="tips" switch-tab="0" verify-msg="请选择商品品牌">
                        <option value="">请选择品牌</option>
                    </select>
                </div>
            </div> -->
            <!--供货商-->
            <!-- <div class="layui-form-item">
                <label class="layui-form-label">供货商：</label>
                <div class="layui-input-inline">
                    <select name="supplier_id" lay-verType="tips" switch-tab="0" verify-msg="请选择供货商">
                        <option value="">请选择供货商</option>
                    </select>
                </div>
            </div> -->
            <!--商品主图（隐藏，用于存储主图数据）-->
            <div class="layui-form-item" style="display: none;">
                <div class="layui-input-block" id="imageContainer">
                    <input type="hidden" name="image" id="mainImageInput">
                </div>
            </div>
            <!--自定义分享海报-->
            <!-- <div class="layui-form-item" style="margin-bottom: 0px">
                <label class="layui-form-label">分享海报：</label>
                <div class="layui-input-block" id="posterContainer">
                    <div class="like-upload-image">
                        <div class="upload-image-elem"><a class="add-upload-image" id="poster"> + 添加图片</a></div>
                    </div>
                </div>
            </div> -->
            <!--商品图片（合并封面图和轮播图）-->
            <div class="layui-form-item" style="margin-bottom: 0px">
                <label class="layui-form-label"><span class="form-label-asterisk">*</span>商品图片：</label>
                <div class="layui-input-block" id="goodsImageContainer">
                    <div class="like-upload-image sortable-upload-container">
                        <div class="upload-image-elem"><a class="add-upload-image" id="goodsimage"> + 添加图片</a></div>
                      </div>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label"></label>
                <span style="color: #a3a3a3;font-size: 9px">建议尺寸：800*800像素，最多上传6张，第一张为主图（封面图）,以选择顺序为主</span>
            </div>
            <!--商品视频-->
            <div class="layui-form-item">
                <label class="layui-form-label">商品视频：</label>
                <div class="layui-input-block" id="videoContainer">
                    <div class="like-upload-video">
                        <div class="upload-image-elem"><a class="add-upload-video" id="video"> + 添加视频</a></div>
                      </div>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label"></label>
                <span style="color: #a3a3a3;font-size: 9px">商品视频，在商品详情页面播放。(限制4m内)</span>
            </div>
        </div>
    </div>
</div>