<?php
namespace app\common\model\agent;

use app\common\basics\Models;
use app\common\server\UrlServer;
use app\model\User;
use app\model\AgentRelationship;
use app\model\Commission;
use think\facade\Db;

class AgentWithdrawal extends Models
{
    /**
     * @Notes: 关联商家模型
     * @Author: 张无忌
     */
    public function agent()
    {
        return $this->hasOne('Agent', 'id', 'agent_id');
    }
    /**
     * @Notes: 关联商家模型
     * @Author: 张无忌
     */
    public function user()
    {
        return $this->hasOne(\app\common\model\user\User::class, 'id', 'user_id');
    }


    /**
     * @notes 关联提现支付宝账号
     * @return \think\model\relation\HasOne
     * <AUTHOR>
     * @datetime 2023-06-07 14:24:07
     */
    function alipay()
    {
        return $this->hasOne(AgentAlipay::class, 'id', 'alipay_id');
    }


    /**
     * @Notes: 获取器-转换图片路径
     * @Author: 张无忌
     * @param $value
     * @return string
     */
    public function getTransferVoucherAttr($value)
    {
        if ($value) {
            return UrlServer::getFileUrl($value);
        }

        return '';
    }

    /**
     * @Notes: 修改器-转换图片路径
     * @Author: 张无忌
     * @param $value
     * @return string
     */
    public function setTransferVoucherAttr($value)
    {
        if ($value) {
            return UrlServer::setFileUrl($value);
        }

        return '';
    }

    /**
     * @Notes: 获取器-格式化创建时间
     * @param $value
     * @return string
     */
    public function getCreateTimeAttr($value)
    {
        return empty($value) || $value == 0 ? '' : date('Y-m-d H:i:s', is_numeric($value) ? $value : strtotime($value));
    }

    /**
     * @Notes: 获取器-格式化更新时间
     * @param $value
     * @return string
     */
    public function getUpdateTimeAttr($value)
    {
        return empty($value) || $value == 0 ? '' : date('Y-m-d H:i:s', is_numeric($value) ? $value : strtotime($value));
    }

    /**
     * @Notes: 获取器-格式化转账时间
     * @param $value
     * @return string
     */
    public function getTransferTimeAttr($value)
    {
        return empty($value) || $value == 0 ? '' : date('Y-m-d H:i:s', is_numeric($value) ? $value : strtotime($value));
    }

}