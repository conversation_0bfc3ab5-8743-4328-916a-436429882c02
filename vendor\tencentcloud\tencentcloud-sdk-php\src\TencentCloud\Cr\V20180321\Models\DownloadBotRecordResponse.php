<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Cr\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * DownloadBotRecord返回参数结构体
 *
 * @method string getRecordCosUrl() 获取录音地址。请求后30分钟内有效
 * @method void setRecordCosUrl(string $RecordCosUrl) 设置录音地址。请求后30分钟内有效
 * @method string getTextCosUrl() 获取文本地址。请求后30分钟内有效
 * @method void setTextCosUrl(string $TextCosUrl) 设置文本地址。请求后30分钟内有效
 * @method string getRequestId() 获取唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
 * @method void setRequestId(string $RequestId) 设置唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
 */
class DownloadBotRecordResponse extends AbstractModel
{
    /**
     * @var string 录音地址。请求后30分钟内有效
     */
    public $RecordCosUrl;

    /**
     * @var string 文本地址。请求后30分钟内有效
     */
    public $TextCosUrl;

    /**
     * @var string 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
     */
    public $RequestId;

    /**
     * @param string $RecordCosUrl 录音地址。请求后30分钟内有效
     * @param string $TextCosUrl 文本地址。请求后30分钟内有效
     * @param string $RequestId 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("RecordCosUrl",$param) and $param["RecordCosUrl"] !== null) {
            $this->RecordCosUrl = $param["RecordCosUrl"];
        }

        if (array_key_exists("TextCosUrl",$param) and $param["TextCosUrl"] !== null) {
            $this->TextCosUrl = $param["TextCosUrl"];
        }

        if (array_key_exists("RequestId",$param) and $param["RequestId"] !== null) {
            $this->RequestId = $param["RequestId"];
        }
    }
}
