<?php


namespace app\admin\logic\wechat;

use app\common\basics\Logic;
use app\common\server\ConfigServer;
use app\common\server\UrlServer;
use app\common\server\WeChatServer;
use EasyWeChat\Factory;
use EasyWeChat\Kernel\Exceptions\Exception;
use app\common\model\wechat\WechatReply;
use app\common\model\wechat\Wechat;

class ReplyLogic extends Logic
{
    public static function lists($get)
    {
        $where[] = ['del','=',0];

        if(isset($get['type'])){ // 回复类型
            $where[] = ['reply_type','=',$get['type']];
        }

        $count = WechatReply::where($where)->count();
        $list = WechatReply::where($where)
            ->order([
                'sort' => 'asc',
                'id' => 'desc'
            ])
            ->page($get['page'],$get['limit'])
            ->select()
            ->toArray();

        foreach ($list as $key =>  $reply) {
            // 内容类型
            switch ($reply['content_type']) {
                case 1:
                    $list[$key]['content_type'] = '文本';
                    break;
                case 2:
                    $list[$key]['content_type'] = '图片';
                    break;
                case 3:
                    $list[$key]['content_type'] = '文本+图片';
                    break;
                default:
                    $list[$key]['content_type'] = '文本';
                    break;
            }

            // 匹配类型
            switch ($reply['matching_type']){
                case 1:
                    $list[$key]['matching_type'] = '全匹配';
                    break;
                case 2:
                    $list[$key]['matching_type'] = '模糊匹配';
                    break;
            }
        }
        return ['count'=>$count,'list'=>$list];
    }

    public static function add($post)
    {
        $post['create_time'] = time();
        $post['del'] = 0;

        // 处理多条消息的情况
        if (isset($post['content_type']) && $post['content_type'] == 3) {
            // 文本+图片类型，设置为多条消息
            $post['message_count'] = 2;
        } else {
            $post['message_count'] = 1;
        }

        // 处理图片URL
        if (isset($post['image_url']) && !empty($post['image_url'])) {
            // 如果是完整URL，转换为相对路径
            $domain = request()->domain();
            $post['image_url'] = str_replace($domain, '', $post['image_url']);
        }

        if (isset($post['second_image_url']) && !empty($post['second_image_url'])) {
            // 如果是完整URL，转换为相对路径
            $domain = request()->domain();
            $post['second_image_url'] = str_replace($domain, '', $post['second_image_url']);
        }

        // 清理不需要的字段
        if ($post['content_type'] != 2) {
            // 非单独图片类型，清空image_url
            $post['image_url'] = '';
        }

        if ($post['content_type'] != 3) {
            // 非混合类型，清空第二条消息相关字段
            $post['second_content'] = '';
            $post['second_content_type'] = null;
            $post['second_image_url'] = '';
            $post['message_count'] = 1;
        }

        if($post['reply_type'] !== WeChat::msg_type_text && $post['status']){
            // 除了关键词回复，其他回复类型开启记录只允许一条,若当前正在新增的记录将是开启状态，则该回复类型下的现有记录需先更新为停用状态
            WechatReply::where(['reply_type'=>$post['reply_type']])->update(['update_time'=>time(),'status'=>0]);
        }
        return WechatReply::insert($post);
    }

    public static function getReply($id)
    {
        $detail =  WechatReply::findOrEmpty($id);
        $detail = $detail->isEmpty() ? [] : $detail->toArray();
        return $detail;
    }

    public static function edit($post){
        $post['update_time'] = time();

        // 处理多条消息的情况
        if (isset($post['content_type']) && $post['content_type'] == 3) {
            // 文本+图片类型，设置为多条消息
            $post['message_count'] = 2;
        } else {
            $post['message_count'] = 1;
        }

        // 处理图片URL
        if (isset($post['image_url']) && !empty($post['image_url'])) {
            // 如果是完整URL，转换为相对路径
            $domain = request()->domain();
            $post['image_url'] = str_replace($domain, '', $post['image_url']);
        }

        if (isset($post['second_image_url']) && !empty($post['second_image_url'])) {
            // 如果是完整URL，转换为相对路径
            $domain = request()->domain();
            $post['second_image_url'] = str_replace($domain, '', $post['second_image_url']);
        }

        // 清理不需要的字段
        if ($post['content_type'] != 2) {
            // 非单独图片类型，清空image_url
            $post['image_url'] = '';
        }

        if ($post['content_type'] != 3) {
            // 非混合类型，清空第二条消息相关字段
            $post['second_content'] = '';
            $post['second_content_type'] = null;
            $post['second_image_url'] = '';
            $post['message_count'] = 1;
        }

        if($post['reply_type'] !== WeChat::msg_type_text && $post['status']){
            WechatReply::where(['reply_type'=>$post['reply_type']])->update(['update_time'=>time(),'status'=>0]);
        }
        return WechatReply::where(['id'=>$post['id']])->update($post);
    }

    public static function del($id){
        return WechatReply::where(['id'=>$id])->update(['update_time'=>time(),'del'=>1]);
    }

    public static function changeFields($id,$field,$field_value,$reply_type){
        if( 'status' === $field && $field_value && $reply_type !== WeChat::msg_type_text){
            WechatReply::where(['reply_type'=>$reply_type])->update(['update_time'=>time(),'status'=>0]);
        }
        return WechatReply::where(['id'=>$id])->update(['update_time'=>time(),$field=>$field_value]);
    }
}