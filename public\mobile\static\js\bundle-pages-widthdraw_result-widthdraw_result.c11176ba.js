(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["bundle-pages-widthdraw_result-widthdraw_result"],{"3ab4":function(t,e,i){"use strict";i.r(e);var n=i("4f01"),a=i.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(r);e["default"]=a.a},4196:function(t,e,i){"use strict";i.r(e);var n=i("c2e6"),a=i.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(r);e["default"]=a.a},"4e46":function(t,e,i){var n=i("ae86");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=i("4f06").default;a("233b29e5",n,!0,{sourceMap:!1,shadowMode:!1})},"4f01":function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("a9e3"),i("acd8");var n={data:function(){return{priceSlice:{}}},components:{},props:{firstSize:{type:[String,Number],default:28},secondSize:{type:[String,Number],default:28},color:{type:String},weight:{type:[String,Number],default:400},price:{type:[String,Number],default:""},showSubscript:{type:Boolean,default:!0},subscriptSize:{type:[String,Number],default:28},lineThrough:{type:Boolean,default:!1}},created:function(){this.priceFormat()},watch:{price:function(t){this.priceFormat()}},methods:{priceFormat:function(){var t=this.price,e={};null!==t&&""!==t&&void 0!==t?(t=parseFloat(t),t=String(t).split("."),e.first=t[0],e.second=t[1],this.priceSlice=e):this.priceSlice={first:0}}}};e.default=n},"60ac":function(t,e,i){"use strict";i.r(e);var n=i("78bc"),a=i("4196");for(var r in a)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(r);i("75aa");var s=i("f0c5"),c=Object(s["a"])(a["default"],n["b"],n["c"],!1,null,"a2ff231c",null,!1,n["a"],void 0);e["default"]=c.exports},6944:function(t,e,i){var n=i("24fb");e=n(!1),e.push([t.i,".price-format[data-v-0a5a34e0]{font-family:Avenir,SourceHanSansCN,PingFang SC,Arial,Hiragino Sans GB,Microsoft YaHei,sans-serif}",""]),t.exports=e},"75aa":function(t,e,i){"use strict";var n=i("4e46"),a=i.n(n);a.a},"78bc":function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return r})),i.d(e,"a",(function(){return n}));var n={priceFormat:i("a272").default},a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"widthdraw-result"},[i("v-uni-view",{staticClass:"contain bg-white"},[i("v-uni-view",{staticClass:"header flex-col col-center"},[i("v-uni-view",[i("v-uni-image",{staticClass:"tips-icon",attrs:{src:t.getTipsIcon(t.widthdrawInfo.status)}})],1),i("v-uni-view",{staticClass:"xl m-t-20 bold"},[t._v(t._s(t.widthdrawInfo.statusDesc))]),i("v-uni-view",{staticClass:"flex-col col-center"},[i("price-format",{attrs:{price:t.widthdrawInfo.money,color:t.colorConfig.primary,"subscript-size":"30","first-size":"46","second-size":"46",weight:"bold"}})],1)],1),i("v-uni-view",{staticClass:"info"},[i("v-uni-view",{staticClass:"flex row-between m-t-20"},[i("v-uni-view",[t._v("流水号")]),i("v-uni-view",[t._v(t._s(t.widthdrawInfo.sn))])],1),i("v-uni-view",{staticClass:"flex row-between m-t-20"},[i("v-uni-view",[t._v("提交时间")]),i("v-uni-view",[t._v(t._s(t.widthdrawInfo.create_time))])],1),i("v-uni-view",{staticClass:"flex row-between m-t-20"},[i("v-uni-view",[t._v("提现至")]),i("v-uni-view",[t._v(t._s(t.widthdrawInfo.typeDesc))])],1),i("v-uni-view",{staticClass:"flex row-between m-t-20"},[i("v-uni-view",[t._v("服务费")]),i("v-uni-view",[i("price-format",{attrs:{price:t.widthdrawInfo.poundage}})],1)],1),i("v-uni-view",{staticClass:"flex row-between m-t-20"},[i("v-uni-view",[t._v("实际到账")]),i("v-uni-view",[i("price-format",{attrs:{price:t.widthdrawInfo.left_money}})],1)],1)],1),i("v-uni-view",{staticClass:"line m-t-40"}),i("v-uni-view",{staticClass:"m-t-40 flex-col row-center"},[i("router-link",{attrs:{to:"/bundle/pages/user_withdraw_code/user_withdraw_code"}},[i("v-uni-button",{staticClass:"br60",attrs:{type:"primary",size:"lg"}},[t._v("查看历史提现记录")])],1),i("router-link",{attrs:{navType:"pushTab",to:"/pages/index/index"}},[i("v-uni-button",{staticClass:"br60 plain primary m-t-30",attrs:{size:"lg"}},[t._v("返回首页")])],1)],1)],1),i("v-uni-view",{staticClass:"muted m-t-20 xs text-center"},[t._v("* 审核通过后约72小时内到账，请留意账户明细")])],1)},r=[]},8158:function(t,e,i){"use strict";var n=i("e6f3"),a=i.n(n);a.a},a272:function(t,e,i){"use strict";i.r(e);var n=i("e2ba"),a=i("3ab4");for(var r in a)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(r);i("8158");var s=i("f0c5"),c=Object(s["a"])(a["default"],n["b"],n["c"],!1,null,"0a5a34e0",null,!1,n["a"],void 0);e["default"]=c.exports},ae86:function(t,e,i){var n=i("24fb");e=n(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.widthdraw-result .contain[data-v-a2ff231c]{border-radius:%?10?%;padding:0 %?30?% %?40?%;position:relative;margin:%?78?% %?20?% 0}.widthdraw-result .contain .tips-icon[data-v-a2ff231c]{width:%?112?%;height:%?112?%}.widthdraw-result .contain .header[data-v-a2ff231c]{position:absolute;left:50%;-webkit-transform:translateX(-50%);transform:translateX(-50%);top:%?-50?%}.widthdraw-result .contain .info[data-v-a2ff231c]{padding-top:%?180?%}.widthdraw-result .contain .line[data-v-a2ff231c]{border-top:1px solid #e5e5e5}.widthdraw-result .contain .plain[data-v-a2ff231c]{border:1px solid #ff2c3c}',""]),t.exports=e},c2e6:function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=i("1524"),a={data:function(){return{widthdrawInfo:{}}},onLoad:function(){this.id=this.$Route.query.id,this.getWithdrawDetailFun()},methods:{getWithdrawDetailFun:function(){var t=this;(0,n.getWithdrawDetail)({id:this.id}).then((function(e){1==e.code&&(t.widthdrawInfo=e.data)}))},getTipsIcon:function(t){switch(t){case 1:case 2:return"/static/images/icon_wait.png";case 3:return"/static/images/icon_success.png";case 4:return"/static/images/icon_fail.png"}}}};e.default=a},e2ba:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){}));var n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-text",{class:(t.lineThrough?"line-through":"")+" price-format",style:{color:t.color,"font-weight":t.weight}},[t.showSubscript?i("v-uni-text",{style:{"font-size":t.subscriptSize+"rpx","margin-right":"2rpx"}},[t._v("¥")]):t._e(),i("v-uni-text",{style:{"font-size":t.firstSize+"rpx","margin-right":"1rpx"}},[t._v(t._s(t.priceSlice.first))]),t.priceSlice.second?i("v-uni-text",{style:{"font-size":t.secondSize+"rpx"}},[t._v("."+t._s(t.priceSlice.second))]):t._e()],1)},a=[]},e6f3:function(t,e,i){var n=i("6944");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=i("4f06").default;a("66e034c8",n,!0,{sourceMap:!1,shadowMode:!1})}}]);