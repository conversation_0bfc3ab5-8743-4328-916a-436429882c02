{extend name="public/base" /}

{block name="content"}
<div class="layui-fluid">
    <div class="layui-row layui-col-space15">
        <!-- 统计信息 -->
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-header">系统统计</div>
                <div class="layui-card-body">
                    <div class="layui-row layui-col-space10" id="statistics-container">
                        <!-- 统计数据将通过JS动态加载 -->
                    </div>
                </div>
            </div>
        </div>

        <!-- 配置表单 -->
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-header">
                    用户活跃度设置
                    <div class="layui-btn-group" style="float: right;">
                        <button type="button" class="layui-btn layui-btn-sm" id="recalculate-btn">
                            <i class="layui-icon layui-icon-refresh"></i> 重新计算等级
                        </button>
                        <button type="button" class="layui-btn layui-btn-sm layui-btn-normal" id="view-logs-btn">
                            <i class="layui-icon layui-icon-list"></i> 查看日志
                        </button>
                    </div>
                </div>
                <div class="layui-card-body">
                    <form class="layui-form" id="config-form" lay-filter="config-form">
                        <!-- 系统设置 -->
                        <fieldset class="layui-elem-field">
                            <legend>系统设置</legend>
                            <div class="layui-field-box">
                                <div class="layui-row layui-col-space10">
                                    <div class="layui-col-md4">
                                        <label class="layui-form-label">启用系统</label>
                                        <div class="layui-input-block">
                                            <input type="checkbox" name="system[is_enabled]" value="1" lay-skin="switch" lay-text="开启|关闭">
                                        </div>
                                    </div>
                                    <div class="layui-col-md4">
                                        <label class="layui-form-label">登录检查天数</label>
                                        <div class="layui-input-block">
                                            <input type="number" name="system[login_check_days]" placeholder="天数" class="layui-input" min="1" max="30">
                                            <div class="layui-form-mid layui-word-aux">采购商登录积分检查天数</div>
                                        </div>
                                    </div>
                                    <div class="layui-col-md4">
                                        <label class="layui-form-label">聊天积分上限</label>
                                        <div class="layui-input-block">
                                            <input type="number" name="system[chat_daily_limit]" placeholder="次数" class="layui-input" min="1" max="100">
                                            <div class="layui-form-mid layui-word-aux">每日聊天积分获取上限次数</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </fieldset>

                        <!-- 积分规则 -->
                        <fieldset class="layui-elem-field">
                            <legend>积分规则</legend>
                            <div class="layui-field-box">
                                <div class="layui-row layui-col-space10">
                                    <div class="layui-col-md6">
                                        <label class="layui-form-label">采购商登录</label>
                                        <div class="layui-input-block">
                                            <input type="number" name="scoring[purchaser_login_score]" placeholder="积分" class="layui-input" min="0" max="1000">
                                            <div class="layui-form-mid layui-word-aux">采购商7天内登录一次获得积分</div>
                                        </div>
                                    </div>
                                    <div class="layui-col-md6">
                                        <label class="layui-form-label">发布采购信息</label>
                                        <div class="layui-input-block">
                                            <input type="number" name="scoring[publish_demand_score]" placeholder="积分" class="layui-input" min="0" max="1000">
                                            <div class="layui-form-mid layui-word-aux">发布采购信息一次获得积分</div>
                                        </div>
                                    </div>
                                    <div class="layui-col-md6">
                                        <label class="layui-form-label">用户聊天</label>
                                        <div class="layui-input-block">
                                            <input type="number" name="scoring[chat_score]" placeholder="积分" class="layui-input" min="0" max="100">
                                            <div class="layui-form-mid layui-word-aux">用户聊天一次获得积分</div>
                                        </div>
                                    </div>
                                    <div class="layui-col-md6">
                                        <label class="layui-form-label">购买商品</label>
                                        <div class="layui-input-block">
                                            <input type="number" name="scoring[purchase_score]" placeholder="积分" class="layui-input" min="0" max="1000">
                                            <div class="layui-form-mid layui-word-aux">购买商品一次获得积分</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </fieldset>

                        <!-- 等级设置 -->
                        <fieldset class="layui-elem-field">
                            <legend>等级设置</legend>
                            <div class="layui-field-box">
                                <div class="layui-row layui-col-space10">
                                    <div class="layui-col-md6">
                                        <label class="layui-form-label">1级（活跃新手）</label>
                                        <div class="layui-input-block">
                                            <input type="number" name="levels[level_1_score]" placeholder="所需积分" class="layui-input" min="0">
                                        </div>
                                    </div>
                                    <div class="layui-col-md6">
                                        <label class="layui-form-label">2级（活跃用户）</label>
                                        <div class="layui-input-block">
                                            <input type="number" name="levels[level_2_score]" placeholder="所需积分" class="layui-input" min="0">
                                        </div>
                                    </div>
                                    <div class="layui-col-md6">
                                        <label class="layui-form-label">3级（活跃达人）</label>
                                        <div class="layui-input-block">
                                            <input type="number" name="levels[level_3_score]" placeholder="所需积分" class="layui-input" min="0">
                                        </div>
                                    </div>
                                    <div class="layui-col-md6">
                                        <label class="layui-form-label">4级（活跃专家）</label>
                                        <div class="layui-input-block">
                                            <input type="number" name="levels[level_4_score]" placeholder="所需积分" class="layui-input" min="0">
                                        </div>
                                    </div>
                                    <div class="layui-col-md6">
                                        <label class="layui-form-label">5级（活跃大师）</label>
                                        <div class="layui-input-block">
                                            <input type="number" name="levels[level_5_score]" placeholder="所需积分" class="layui-input" min="0">
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-form-mid layui-word-aux" style="margin-top: 10px;">
                                    <i class="layui-icon layui-icon-tips"></i> 等级积分必须递增设置（1级 < 2级 < 3级 < 4级 < 5级）
                                </div>
                            </div>
                        </fieldset>

                        <div class="layui-form-item">
                            <div class="layui-input-block">
                                <button type="submit" class="layui-btn" lay-submit lay-filter="save-config">保存配置</button>
                                <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 日志弹窗 -->
<div id="logs-modal" style="display: none; padding: 20px;">
    <table class="layui-hide" id="logs-table"></table>
</div>

{/block}

{block name="script"}
<script>
layui.use(['form', 'layer', 'table', 'element'], function(){
    var form = layui.form;
    var layer = layui.layer;
    var table = layui.table;
    var element = layui.element;

    // 加载配置和统计数据
    loadConfigAndStats();

    // 保存配置
    form.on('submit(save-config)', function(data){
        layer.load();
        $.post('{:url("save")}', data.field, function(res){
            layer.closeAll('loading');
            if(res.code == 1){
                layer.msg('保存成功', {icon: 1});
                loadConfigAndStats(); // 重新加载统计数据
            } else {
                layer.msg(res.msg || '保存失败', {icon: 2});
            }
        });
        return false;
    });

    // 重新计算等级
    $('#recalculate-btn').click(function(){
        layer.confirm('确定要重新计算所有用户的活跃度等级吗？', {icon: 3, title:'提示'}, function(index){
            layer.load();
            $.post('{:url("recalculate")}', {}, function(res){
                layer.closeAll();
                if(res.code == 1){
                    layer.msg('重新计算完成', {icon: 1});
                    loadConfigAndStats(); // 重新加载统计数据
                } else {
                    layer.msg(res.msg || '计算失败', {icon: 2});
                }
            });
        });
    });

    // 查看日志
    $('#view-logs-btn').click(function(){
        showLogsModal();
    });

    // 加载配置和统计数据
    function loadConfigAndStats(){
        $.get('{:url("index")}', function(res){
            if(res.code == 1){
                // 填充配置表单
                fillConfigForm(res.data.config);
                // 显示统计数据
                showStatistics(res.data.statistics);
                form.render();
            }
        });
    }

    // 填充配置表单
    function fillConfigForm(config){
        // 系统设置
        $('input[name="system[is_enabled]"]').prop('checked', config.system.is_enabled == 1);
        $('input[name="system[login_check_days]"]').val(config.system.login_check_days);
        $('input[name="system[chat_daily_limit]"]').val(config.system.chat_daily_limit);

        // 积分规则
        $('input[name="scoring[purchaser_login_score]"]').val(config.scoring.purchaser_login_score);
        $('input[name="scoring[publish_demand_score]"]').val(config.scoring.publish_demand_score);
        $('input[name="scoring[chat_score]"]').val(config.scoring.chat_score);
        $('input[name="scoring[purchase_score]"]').val(config.scoring.purchase_score);

        // 等级设置
        $('input[name="levels[level_1_score]"]').val(config.levels.level_1_score);
        $('input[name="levels[level_2_score]"]').val(config.levels.level_2_score);
        $('input[name="levels[level_3_score]"]').val(config.levels.level_3_score);
        $('input[name="levels[level_4_score]"]').val(config.levels.level_4_score);
        $('input[name="levels[level_5_score]"]').val(config.levels.level_5_score);
    }

    // 显示统计数据
    function showStatistics(stats){
        var html = '';
        
        // 等级分布
        html += '<div class="layui-col-md8">';
        html += '<div class="layui-card">';
        html += '<div class="layui-card-header">用户等级分布</div>';
        html += '<div class="layui-card-body">';
        html += '<table class="layui-table">';
        html += '<thead><tr><th>等级</th><th>等级名称</th><th>用户数量</th></tr></thead>';
        html += '<tbody>';
        stats.level_distribution.forEach(function(item){
            html += '<tr><td>' + item.level + '</td><td>' + item.level_name + '</td><td>' + item.count + '</td></tr>';
        });
        html += '</tbody></table>';
        html += '</div></div></div>';

        // 其他统计
        html += '<div class="layui-col-md4">';
        html += '<div class="layui-card">';
        html += '<div class="layui-card-header">系统概况</div>';
        html += '<div class="layui-card-body">';
        html += '<div class="layui-row layui-col-space10">';
        html += '<div class="layui-col-md12"><p>采购商数量：<span class="layui-badge layui-bg-blue">' + stats.purchaser_count + '</span></p></div>';
        html += '<div class="layui-col-md12"><p>今日活跃记录：<span class="layui-badge layui-bg-green">' + stats.today_activity_count + '</span></p></div>';
        html += '</div>';
        
        html += '<div style="margin-top: 15px;"><strong>最近7天活动统计：</strong></div>';
        stats.activity_stats.forEach(function(item){
            html += '<p>' + item.type_name + '：<span class="layui-badge">' + item.count + '</span></p>';
        });
        
        html += '</div></div></div>';

        $('#statistics-container').html(html);
    }

    // 显示日志弹窗
    function showLogsModal(){
        layer.open({
            type: 1,
            title: '活跃度日志',
            area: ['90%', '80%'],
            content: $('#logs-modal'),
            success: function(){
                // 初始化日志表格
                table.render({
                    elem: '#logs-table',
                    url: '{:url("logs")}',
                    page: true,
                    cols: [[
                        {field: 'id', title: 'ID', width: 80},
                        {field: 'user_info', title: '用户', width: 150, templet: function(d){
                            return d.user_info ? d.user_info.nickname + '<br><small>' + (d.user_info.mobile || '') + '</small>' : '-';
                        }},
                        {field: 'activity_type_desc', title: '活动类型', width: 120},
                        {field: 'score_change_text', title: '积分变化', width: 100, align: 'center'},
                        {field: 'after_score', title: '当前积分', width: 100, align: 'center'},
                        {field: 'level_change_text', title: '等级变化', width: 200},
                        {field: 'remark', title: '备注', width: 150},
                        {field: 'create_time', title: '时间', width: 160}
                    ]],
                    text: {none: '暂无数据'},
                    response: {
                        statusCode: 1
                    },
                    parseData: function (res) {
                        return {
                            "code": res.code,
                            "msg": res.msg,
                            "count": res.data.count,
                            "data": res.data.lists
                        };
                    }
                });
            }
        });
    }
});
</script>
{/block}
