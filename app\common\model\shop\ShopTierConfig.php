<?php

namespace app\common\model\shop;

use app\common\basics\Models;

/**
 * 商家等级配置模型
 */
class ShopTierConfig extends Models
{
    protected $name = 'shop_tier_config';

    /**
     * 获取器 - 特权配置
     */
    public function getFeaturesAttr($value)
    {
        return $value ? json_decode($value, true) : [];
    }

    /**
     * 修改器 - 特权配置
     */
    public function setFeaturesAttr($value)
    {
        return is_array($value) ? json_encode($value, JSON_UNESCAPED_UNICODE) : $value;
    }

    /**
     * 获取器 - 限制配置
     */
    public function getLimitsAttr($value)
    {
        return $value ? json_decode($value, true) : [];
    }

    /**
     * 修改器 - 限制配置
     */
    public function setLimitsAttr($value)
    {
        return is_array($value) ? json_encode($value, JSON_UNESCAPED_UNICODE) : $value;
    }

    /**
     * 获取器 - 创建时间
     */
    public function getCreateTimeAttr($value)
    {
        return date('Y-m-d H:i:s', $value);
    }

    /**
     * 获取器 - 更新时间
     */
    public function getUpdateTimeAttr($value)
    {
        return date('Y-m-d H:i:s', $value);
    }

    /**
     * 获取器 - 价格文本
     */
    public function getPriceTextAttr($value, $data)
    {
        return $data['price'] > 0 ? '¥' . $data['price'] : '免费';
    }

    /**
     * 获取器 - 有效期文本
     */
    public function getDurationTextAttr($value, $data)
    {
        $duration = $data['duration_days'] ?? 0;
        if ($duration <= 0) {
            return '永久';
        }
        
        if ($duration >= 365) {
            return intval($duration / 365) . '年';
        } elseif ($duration >= 30) {
            return intval($duration / 30) . '个月';
        } else {
            return $duration . '天';
        }
    }
}
