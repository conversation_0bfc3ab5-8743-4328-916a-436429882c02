(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["bundle-pages-Feedback-Feedback~bundle-pages-shop_setting-shop_setting"],{"06c7":function(e,t,i){"use strict";i.d(t,"b",(function(){return a})),i.d(t,"c",(function(){return r})),i.d(t,"a",(function(){return n}));var n={uIcon:i("15cd").default,uLineProgress:i("f891").default},a=function(){var e=this,t=e.$createElement,i=e._self._c||t;return e.disabled?e._e():i("v-uni-view",{staticClass:"u-upload"},[e._l(e.lists,(function(t,n){return e.showUploadList?i("v-uni-view",{key:n,staticClass:"u-list-item u-preview-wrap",style:{width:e.$u.addUnit(e.width),height:e.$u.addUnit(e.height)}},[e.deletable?i("v-uni-view",{staticClass:"u-delete-icon",style:{background:e.delBgColor},on:{click:function(t){t.stopPropagation(),arguments[0]=t=e.$handleEvent(t),e.deleteItem(n)}}},[i("u-icon",{staticClass:"u-icon",attrs:{name:e.delIcon,size:"20",color:e.delColor}})],1):e._e(),e.showProgress&&t.progress>0&&!t.error?i("u-line-progress",{staticClass:"u-progress",attrs:{"show-percent":!1,height:"16",percent:t.progress}}):e._e(),t.error?i("v-uni-view",{staticClass:"u-error-btn",on:{click:function(t){t.stopPropagation(),arguments[0]=t=e.$handleEvent(t),e.retry(n)}}},[e._v("点击重试")]):e._e(),t.isImage?e._e():i("v-uni-image",{staticClass:"u-preview-image",attrs:{src:t.url||t.path,mode:e.imageMode},on:{click:function(i){i.stopPropagation(),arguments[0]=i=e.$handleEvent(i),e.doPreviewImage(t.url||t.path,n)}}})],1):e._e()})),e._t("file",null,{file:e.lists}),e.maxCount>e.lists.length?i("v-uni-view",{staticStyle:{display:"inline-block"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.selectFile.apply(void 0,arguments)}}},[e._t("addBtn"),e.customBtn?e._e():i("v-uni-view",{staticClass:"u-list-item u-add-wrap",style:{width:e.$u.addUnit(e.width),height:e.$u.addUnit(e.height)},attrs:{"hover-class":"u-add-wrap__hover","hover-stay-time":"150"}},[i("u-icon",{staticClass:"u-add-btn",attrs:{name:"plus",size:"40"}}),i("v-uni-view",{staticClass:"u-add-tips"},[e._v(e._s(e.uploadText))])],1)],2):e._e()],2)},r=[]},"147b":function(e,t,i){var n=i("c86c");t=n(!1),t.push([e.i,"@charset \"UTF-8\";\n/* 颜色变量 */\n/** S Font's size **/\n/** E Font's size **/[data-v-90eaadc2]:export{red_theme:#ff2c3c;orange_theme:#f7971e;pink_theme:#fa444d;gold_theme:#e0a356;blue_theme:#2f80ed;green_theme:#2ec840}.u-progress[data-v-90eaadc2]{overflow:hidden;height:15px;display:inline-flex;align-items:center;width:100%;border-radius:%?100?%}.u-active[data-v-90eaadc2]{width:0;height:100%;align-items:center;display:flex;flex-direction:row;justify-items:flex-end;justify-content:space-around;font-size:%?20?%;color:#fff;transition:all .4s ease}.u-striped[data-v-90eaadc2]{background-image:linear-gradient(45deg,hsla(0,0%,100%,.15) 25%,transparent 0,transparent 50%,hsla(0,0%,100%,.15) 0,hsla(0,0%,100%,.15) 75%,transparent 0,transparent);background-size:39px 39px}.u-striped-active[data-v-90eaadc2]{-webkit-animation:progress-stripes-data-v-90eaadc2 2s linear infinite;animation:progress-stripes-data-v-90eaadc2 2s linear infinite}@-webkit-keyframes progress-stripes-data-v-90eaadc2{0%{background-position:0 0}100%{background-position:39px 0}}@keyframes progress-stripes-data-v-90eaadc2{0%{background-position:0 0}100%{background-position:39px 0}}",""]),e.exports=t},"215d":function(e,t,i){var n=i("5f91");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[e.i,n,""]]),n.locals&&(e.exports=n.locals);var a=i("967d").default;a("41b543b7",n,!0,{sourceMap:!1,shadowMode:!1})},"2ace":function(e,t,i){"use strict";var n=i("215d"),a=i.n(n);a.a},"440d":function(e,t,i){"use strict";i("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,i("64aa");var n={name:"u-line-progress",props:{round:{type:Boolean,default:!0},type:{type:String,default:""},activeColor:{type:String,default:"#19be6b"},inactiveColor:{type:String,default:"#ececec"},percent:{type:Number,default:0},showPercent:{type:Boolean,default:!0},height:{type:[Number,String],default:28},striped:{type:Boolean,default:!1},stripedActive:{type:Boolean,default:!1}},data:function(){return{}},computed:{progressStyle:function(){var e={};return e.width=this.percent+"%",this.activeColor&&(e.backgroundColor=this.activeColor),e}},methods:{}};t.default=n},"5f91":function(e,t,i){var n=i("c86c");t=n(!1),t.push([e.i,"@charset \"UTF-8\";\n/* 颜色变量 */\n/** S Font's size **/\n/** E Font's size **/[data-v-528f429d]:export{red_theme:#ff2c3c;orange_theme:#f7971e;pink_theme:#fa444d;gold_theme:#e0a356;blue_theme:#2f80ed;green_theme:#2ec840}.u-upload[data-v-528f429d]{display:flex;flex-direction:row;flex-wrap:wrap;align-items:center}.u-list-item[data-v-528f429d]{width:%?200?%;height:%?200?%;overflow:hidden;margin:%?10?%;background:#f4f5f6;position:relative;border-radius:%?10?%;display:flex;align-items:center;justify-content:center}.u-preview-wrap[data-v-528f429d]{border:1px solid #ebecee}.u-add-wrap[data-v-528f429d]{flex-direction:column;color:#606266;font-size:%?26?%}.u-add-tips[data-v-528f429d]{margin-top:%?20?%;line-height:%?40?%}.u-add-wrap__hover[data-v-528f429d]{background-color:#ebecee}.u-preview-image[data-v-528f429d]{display:block;width:100%;height:100%;border-radius:%?10?%}.u-delete-icon[data-v-528f429d]{position:absolute;top:%?10?%;right:%?10?%;z-index:10;background-color:#fa3534;border-radius:%?100?%;width:%?44?%;height:%?44?%;display:flex;flex-direction:row;align-items:center;justify-content:center}.u-icon[data-v-528f429d]{display:flex;flex-direction:row;align-items:center;justify-content:center}.u-progress[data-v-528f429d]{position:absolute;bottom:%?10?%;left:%?8?%;right:%?8?%;z-index:9;width:auto}.u-error-btn[data-v-528f429d]{color:#fff;background-color:#fa3534;font-size:%?20?%;padding:4px 0;text-align:center;position:absolute;bottom:0;left:0;right:0;z-index:9;line-height:1}",""]),e.exports=t},"81b6":function(e,t,i){"use strict";i.r(t);var n=i("440d"),a=i.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){i.d(t,e,(function(){return n[e]}))}(r);t["default"]=a.a},"8d00":function(e,t,i){"use strict";i.d(t,"b",(function(){return n})),i.d(t,"c",(function(){return a})),i.d(t,"a",(function(){}));var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("v-uni-view",{staticClass:"u-progress",style:{borderRadius:e.round?"100rpx":0,height:e.height+"rpx",backgroundColor:e.inactiveColor}},[i("v-uni-view",{staticClass:"u-active",class:[e.type?"u-type-"+e.type+"-bg":"",e.striped?"u-striped":"",e.striped&&e.stripedActive?"u-striped-active":""],style:[e.progressStyle]},[e.$slots.default||e.$slots.$default?e._t("default"):e.showPercent?[e._v(e._s(e.percent+"%"))]:e._e()],2)],1)},a=[]},"8d86":function(e,t,i){"use strict";i.r(t);var n=i("06c7"),a=i("a6e5");for(var r in a)["default"].indexOf(r)<0&&function(e){i.d(t,e,(function(){return a[e]}))}(r);i("2ace");var s=i("828b"),o=Object(s["a"])(a["default"],n["b"],n["c"],!1,null,"528f429d",null,!1,n["a"],void 0);t["default"]=o.exports},93208:function(e,t,i){var n=i("147b");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[e.i,n,""]]),n.locals&&(e.exports=n.locals);var a=i("967d").default;a("ac489ac6",n,!0,{sourceMap:!1,shadowMode:!1})},a6e5:function(e,t,i){"use strict";i.r(t);var n=i("d6f0"),a=i.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){i.d(t,e,(function(){return n[e]}))}(r);t["default"]=a.a},c260:function(e,t,i){"use strict";var n=i("93208"),a=i.n(n);a.a},d6f0:function(e,t,i){"use strict";i("6a54");var n=i("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=n(i("2634")),r=n(i("2fdc"));i("64aa"),i("fd3c"),i("bf0f"),i("aa9c"),i("5c47"),i("0506"),i("4626"),i("dd2b"),i("a1c1");var s={name:"u-upload",props:{showUploadList:{type:Boolean,default:!0},action:{type:String,default:""},maxCount:{type:[String,Number],default:52},showProgress:{type:Boolean,default:!0},disabled:{type:Boolean,default:!1},imageMode:{type:String,default:"aspectFill"},header:{type:Object,default:function(){return{}}},formData:{type:Object,default:function(){return{}}},name:{type:String,default:"file"},sizeType:{type:Array,default:function(){return["original","compressed"]}},sourceType:{type:Array,default:function(){return["album","camera"]}},previewFullImage:{type:Boolean,default:!0},multiple:{type:Boolean,default:!0},deletable:{type:Boolean,default:!0},maxSize:{type:[String,Number],default:Number.MAX_VALUE},fileList:{type:Array,default:function(){return[]}},uploadText:{type:String,default:"选择图片"},autoUpload:{type:Boolean,default:!0},showTips:{type:Boolean,default:!0},customBtn:{type:Boolean,default:!1},width:{type:[String,Number],default:200},height:{type:[String,Number],default:200},delBgColor:{type:String,default:"#fa3534"},delColor:{type:String,default:"#ffffff"},delIcon:{type:String,default:"close"},toJson:{type:Boolean,default:!0},beforeUpload:{type:Function,default:null},beforeRemove:{type:Function,default:null},limitType:{type:Array,default:function(){return["png","jpg","jpeg","webp","gif","image"]}},index:{type:[Number,String],default:""}},mounted:function(){},data:function(){return{lists:[],isInCount:!0,uploading:!1}},watch:{fileList:{immediate:!0,handler:function(e){var t=this;e.map((function(e){var i=t.lists.some((function(t){return t.url==e.url}));!i&&t.lists.push({url:e.url,error:!1,progress:100})}))}},lists:function(e){this.$emit("on-list-change",e,this.index)}},methods:{clear:function(){this.lists=[]},reUpload:function(){this.uploadFile()},selectFile:function(){var e=this;if(!this.disabled){this.name;var t=this.maxCount,i=this.multiple,n=this.maxSize,a=this.sizeType,r=this.lists,s=(this.camera,this.compressed,this.maxDuration,this.sourceType),o=null,l=t-r.length;o=new Promise((function(e,t){uni.chooseImage({count:i?l>9?9:l:1,sourceType:s,sizeType:a,success:e,fail:t})})),o.then((function(a){var s=e.lists.length;a.tempFiles.map((function(a,s){if(e.checkFileExt(a)&&(i||!(s>=1)))if(a.size>n)e.$emit("on-oversize",a,e.lists,e.index),e.showToast("超出允许的文件大小");else{if(t<=r.length)return e.$emit("on-exceed",a,e.lists,e.index),void e.showToast("超出最大允许的文件个数");r.push({url:a.path,progress:0,error:!1,file:a})}})),e.$emit("on-choose-complete",e.lists,e.index),e.autoUpload&&e.uploadFile(s)})).catch((function(t){e.$emit("on-choose-fail",t)}))}},showToast:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];(this.showTips||t)&&uni.showToast({title:e,icon:"none"})},upload:function(){this.uploadFile()},retry:function(e){this.lists[e].progress=0,this.lists[e].error=!1,this.lists[e].response=null,uni.showLoading({title:"重新上传"}),this.uploadFile(e)},uploadFile:function(){var e=arguments,t=this;return(0,r.default)((0,a.default)().mark((function i(){var n,r,s;return(0,a.default)().wrap((function(i){while(1)switch(i.prev=i.next){case 0:if(n=e.length>0&&void 0!==e[0]?e[0]:0,!t.disabled){i.next=3;break}return i.abrupt("return");case 3:if(!t.uploading){i.next=5;break}return i.abrupt("return");case 5:if(!(n>=t.lists.length)){i.next=8;break}return t.$emit("on-uploaded",t.lists,t.index),i.abrupt("return");case 8:if(100!=t.lists[n].progress){i.next=11;break}return 0==t.autoUpload&&t.uploadFile(n+1),i.abrupt("return");case 11:if(!t.beforeUpload||"function"!==typeof t.beforeUpload){i.next=22;break}if(r=t.beforeUpload.bind(t.$u.$parent.call(t))(n,t.lists),!r||"function"!==typeof r.then){i.next=18;break}return i.next=16,r.then((function(e){})).catch((function(e){return t.uploadFile(n+1)}));case 16:i.next=22;break;case 18:if(!1!==r){i.next=22;break}return i.abrupt("return",t.uploadFile(n+1));case 22:if(t.action){i.next=25;break}return t.showToast("请配置上传地址",!0),i.abrupt("return");case 25:t.lists[n].error=!1,t.uploading=!0,s=uni.uploadFile({url:t.action,filePath:t.lists[n].url,name:t.name,formData:t.formData,header:t.header,success:function(e){var i=t.toJson&&t.$u.test.jsonString(e.data)?JSON.parse(e.data):e.data;[200,201,204].includes(e.statusCode)?(t.lists[n].response=i,t.lists[n].progress=100,t.lists[n].error=!1,t.$emit("on-success",i,n,t.lists,t.index)):t.uploadError(n,i)},fail:function(e){t.uploadError(n,e)},complete:function(e){uni.hideLoading(),t.uploading=!1,t.uploadFile(n+1),t.$emit("on-change",e,n,t.lists,t.index)}}),s.onProgressUpdate((function(e){e.progress>0&&(t.lists[n].progress=e.progress,t.$emit("on-progress",e,n,t.lists,t.index))}));case 29:case"end":return i.stop()}}),i)})))()},uploadError:function(e,t){this.lists[e].progress=0,this.lists[e].error=!0,this.lists[e].response=null,this.$emit("on-error",t,e,this.lists,this.index),this.showToast("上传失败，请重试")},deleteItem:function(e){var t=this;uni.showModal({title:"提示",content:"您确定要删除此项吗？",success:function(){var i=(0,r.default)((0,a.default)().mark((function i(n){var r;return(0,a.default)().wrap((function(i){while(1)switch(i.prev=i.next){case 0:if(!n.confirm){i.next=12;break}if(!t.beforeRemove||"function"!==typeof t.beforeRemove){i.next=11;break}if(r=t.beforeRemove.bind(t.$u.$parent.call(t))(e,t.lists),!r||"function"!==typeof r.then){i.next=8;break}return i.next=6,r.then((function(i){t.handlerDeleteItem(e)})).catch((function(e){t.showToast("已终止移除")}));case 6:i.next=9;break;case 8:!1===r?t.showToast("已终止移除"):t.handlerDeleteItem(e);case 9:i.next=12;break;case 11:t.handlerDeleteItem(e);case 12:case"end":return i.stop()}}),i)})));return function(e){return i.apply(this,arguments)}}()})},handlerDeleteItem:function(e){this.lists[e].process<100&&this.lists[e].process>0&&"undefined"!=typeof this.lists[e].uploadTask&&this.lists[e].uploadTask.abort(),this.lists.splice(e,1),this.$forceUpdate(),this.$emit("on-remove",e,this.lists,this.index),this.showToast("移除成功")},remove:function(e){e>=0&&e<this.lists.length&&(this.lists.splice(e,1),this.$emit("on-list-change",this.lists,this.index))},doPreviewImage:function(e,t){var i=this;if(this.previewFullImage){var n=this.lists.map((function(e){return e.url||e.path}));uni.previewImage({urls:n,current:e,success:function(){i.$emit("on-preview",e,i.lists,i.index)},fail:function(){uni.showToast({title:"预览图片失败",icon:"none"})}})}},checkFileExt:function(e){var t,i;return i=e.name.replace(/.+\./,"").toLowerCase(),t=this.limitType.some((function(e){return e.toLowerCase()===i})),t||this.showToast("不允许选择".concat(i,"格式的文件")),t}}};t.default=s},f891:function(e,t,i){"use strict";i.r(t);var n=i("8d00"),a=i("81b6");for(var r in a)["default"].indexOf(r)<0&&function(e){i.d(t,e,(function(){return a[e]}))}(r);i("c260");var s=i("828b"),o=Object(s["a"])(a["default"],n["b"],n["c"],!1,null,"90eaadc2",null,!1,n["a"],void 0);t["default"]=o.exports}}]);