(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["bundle-pages-user_set-user_set"],{"036b":function(e,t,n){"use strict";var i=n("5732"),a=n.n(i);a.a},5732:function(e,t,n){var i=n("6f93");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[e.i,i,""]]),i.locals&&(e.exports=i.locals);var a=n("4f06").default;a("9f2a6516",i,!0,{sourceMap:!1,shadowMode:!1})},"5f3b":function(e,t,n){"use strict";n("7a82");var i=n("ee27").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=i(n("f3f3")),o=i(n("d0af")),s=i(n("f07e")),r=i(n("c964"));n("14d9"),n("ac1f"),n("5319");var l=n("1524"),u=n("f287"),c=n("2eec"),f=n("753f"),d=n("26cb"),p=n("a5ae"),v=n("d650"),h=i(n("7b2e")),w={NONE:"",SEX:"sex",NICKNAME:"nickname",AVATAR:"avatar",MOBILE:"mobile"},b={name:"userProfile",data:function(){return{action:u.baseURL+"/api/file/formimage",fileList:[],userInfo:{},new_mobile:"",pwdCode:"",mobileCode:"",newNickname:"",sexList:["男","女"],fieldType:w.NONE,showPicker:!1,showMobile:!1,showPwd:!1,showNickName:!1,codeTips:"",canSendSms:!0,pwd:"",comfirmPwd:"",smsType:"",code:"",version:u.version}},methods:{handleTouser:function(){this.$Router.push({path:"/bundle/pages/user_profile/user_profile"})},codeChange:function(e){this.codeTips=e},onSuccess:function(e){console.log(e)},uploadImage:function(e){var t=this;uni.showLoading({title:"正在上传中...",mask:!0}),(0,p.uploadFile)(e).then((function(e){uni.hideLoading(),t.setUserInfoFun(e.uri)})).catch((function(){uni.hideLoading(),t.$toast({title:"上传失败"})}))},bindWeixin:function(){var e=this;return(0,r.default)((0,s.default)().mark((function t(){return(0,s.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:e.oaAuth();case 1:case"end":return t.stop()}}),t)})))()},oaAuth:function(){h.default.getWxUrl()},onChooseAvatar:function(e){this.fieldType=w.AVATAR,uni.$u.route({url:"/components/uview-ui/components/u-avatar-cropper/u-avatar-cropper",params:{destWidth:300,rectWidth:200,fileType:"jpg"}})},changeNameConfirm:function(e){var t=this;return(0,r.default)((0,s.default)().mark((function n(){return(0,s.default)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(t.fieldType=w.NICKNAME,t.newNickname=e.detail.value.nickname,t.newNickname){n.next=4;break}return n.abrupt("return",t.$toast({title:"请输入新的昵称"}));case 4:return n.next=6,t.setUserInfoFun(t.newNickname);case 6:t.showNickName=!1;case 7:case"end":return n.stop()}}),n)})))()},getUserProfileFun:function(){var e=this;return(0,r.default)((0,s.default)().mark((function t(){var n,i,a,o,r,u,c,f;return(0,s.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,(0,v.getUserProfile)();case 2:return n=t.sent,i=n.userInfo,a=i.avatarUrl,o=i.nickName,r=i.gender,t.next=7,(0,l.setWechatInfo)({nickname:o,avatar:a,sex:r});case 7:u=t.sent,c=u.msg,f=u.code,1==f&&(e.$toast({title:c}),e.getUserInfoFun());case 11:case"end":return t.stop()}}),t)})))()},logout:function(){var e=this;return(0,r.default)((0,s.default)().mark((function t(){var n,i,a;return(0,s.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,uni.showModal({title:"提示",content:"是否退出登录？"});case 2:if(n=t.sent,i=(0,o.default)(n,2),i[0],a=i[1],a.confirm){t.next=8;break}return t.abrupt("return");case 8:(0,l.userLogout)().then((function(t){1==t.code&&(e.$store.commit("logout"),e.$toast({title:"退出成功"}),setTimeout((function(){e.$Router.replace("/pages/login/login")}),500))}));case 9:case"end":return t.stop()}}),t)})))()},goToExplain:function(e){this.$Router.push({path:"/bundle/pages/server_explan/server_explan",query:{type:e}})},goLicense:function(){this.$Router.push({path:"/bundle/pages/license/license",query:{id:""}})},goToCancel:function(){this.$Router.push({path:"/bundle/pages/cancel/cancel",query:{id:""}})},sendSmsFun:function(e){var t=this;this.$refs.uCode.canGetCode&&(0,c.sendSms)({mobile:this.userInfo.mobile||this.new_mobile,key:this.smsType}).then((function(e){1==e.code&&(t.$toast({title:e.msg}),t.$refs.uCode.start())}))},getUserInfoFun:function(){var e=this;(0,l.getUserInfo)().then((function(t){1==t.code&&(e.userInfo=t.data)}))},showModifyMobile:function(){this.new_mobile="",this.showMobile=!0,this.smsType=this.userInfo.mobile?f.SMSType.CHANGE_MOBILE:f.SMSType.BIND},changeUserMobileFun:function(){var e=this;this.mobileCode?this.new_mobile?(0,l.changeUserMobile)({mobile:this.userInfo.mobile,new_mobile:this.new_mobile,code:this.mobileCode,action:this.userInfo.mobile?"change":"binding"}).then((function(t){1==t.code&&(e.showMobile=!1,e.$toast({title:t.msg}),e.getUserInfoFun())})):this.$toast({title:"请输入新的手机号码"}):this.$toast({title:"请输入验证码"})},setUserInfoFun:function(e){var t=this;return(0,r.default)((0,s.default)().mark((function n(){var i;return(0,s.default)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return n.next=2,(0,l.setUserInfo)({field:t.fieldType,value:e});case 2:i=n.sent,1==i.code&&(t.$toast({title:i.msg}),t.getUserInfoFun());case 4:case"end":return n.stop()}}),n)})))()},onConfirm:function(e){this.setUserInfoFun(e[0]+1),this.showPicker=!1},changeSex:function(e){this.showPicker=!0,this.fieldType=w.SEX},showPwdPop:function(){this.userInfo.mobile?(this.smsType=f.SMSType.FINDPWD,this.showPwd=!0):this.$toast({title:"请绑定手机后再设置密码"})},forgetPwdFun:function(){var e=this,t=this.pwdCode,n=this.pwd,i=this.comfirmPwd;if(t)if(n)if(i)if(n==i){var a={mobile:this.userInfo.mobile,code:t,password:n,repassword:i};(0,c.forgetPwd)(a).then((function(t){1==t.code&&(e.showPwd=!1,e.$toast({title:"设置密码成功"}),e.getUserInfoFun())}))}else this.$toast({title:"两次密码输入不一致"});else this.$toast({title:"再次输入新密码确认"});else this.$toast({title:"请输入新密码"});else this.$toast({title:"请输入短信验证码"})},changeName:function(){this.fieldType=w.NICKNAME,this.newNickname="",this.showNickName=!0}},onLoad:function(){var e=this;return(0,r.default)((0,s.default)().mark((function t(){var n,i;return(0,s.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e.getUserInfoFun(),n=e.$Route.query,i=n.code,n.form,!i){t.next=6;break}return Array.isArray(i)&&(i=i.pop()),t.next=6,(0,l.apibindOa)({code:i});case 6:uni.$on("uAvatarCropper",(function(t){console.log(t),e.uploadImage(t)})),e.getUserProfileFun=(0,p.trottle)(e.getUserProfileFun,500,e);case 8:case"end":return t.stop()}}),t)})))()},onUnload:function(){uni.$off("uAvatarCropper")},computed:(0,a.default)({},(0,d.mapGetters)(["token","appConfig"]))};t.default=b},"6f93":function(e,t,n){var i=n("24fb");t=i(!1),t.push([e.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.user-profile[data-v-3f37a6bf]{min-height:calc(100vh - env(safe-area-inset-bottom));display:flex;flex-direction:column}.user-profile .content[data-v-3f37a6bf]{border-top-left-radius:%?28?%;border-top-right-radius:%?28?%}.user-profile .content .user-avatar-box[data-v-3f37a6bf]{padding:%?30?% %?20?%}.user-profile .content .user-avatar-box .user-avatar[data-v-3f37a6bf]{width:%?120?%;height:%?120?%;border-radius:50%}.user-profile .content .row-info[data-v-3f37a6bf]{padding:%?30?% %?20?%}.user-profile .content .row-info .label[data-v-3f37a6bf]{width:%?180?%}.user-profile .content .row-info .bd-btn[data-v-3f37a6bf]{padding:%?8?% %?24?%;border:1px solid #ff2c3c;color:#ff2c3c}.user-profile .content .bdb-line[data-v-3f37a6bf]{border-bottom:%?1?% solid #e5e5e5}.user-profile .license[data-v-3f37a6bf]{margin-top:%?20?%;color:#a7a7a7}.user-profile .save-btn[data-v-3f37a6bf]{margin:%?40?% %?30?% 0;height:%?88?%}.user-profile .modify-container[data-v-3f37a6bf]{padding:%?30?%;width:%?620?%;border-radius:%?30?%}.user-profile .modify-container .title[data-v-3f37a6bf]{padding:%?26?% %?0?%}.user-profile .modify-container .btn[data-v-3f37a6bf]{height:%?80?%;border-radius:%?20?%;margin:%?60?% %?50?% 0}',""]),e.exports=t},"98cb":function(e,t,n){"use strict";n.r(t);var i=n("e22c"),a=n("b4c2");for(var o in a)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(o);n("036b");var s=n("f0c5"),r=Object(s["a"])(a["default"],i["b"],i["c"],!1,null,"3f37a6bf",null,!1,i["a"],void 0);t["default"]=r.exports},b4c2:function(e,t,n){"use strict";n.r(t);var i=n("5f3b"),a=n.n(i);for(var o in i)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(o);t["default"]=a.a},e22c:function(e,t,n){"use strict";n.d(t,"b",(function(){return a})),n.d(t,"c",(function(){return o})),n.d(t,"a",(function(){return i}));var i={uIcon:n("6976").default,uPopup:n("5676").default,uField:n("b6ae").default,uVerificationCode:n("0af6").default,uFormItem:n("99d5").default,uPicker:n("15c7").default},a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("v-uni-view",{staticClass:"user-profile p-t-10"},[n("v-uni-view",{staticClass:"content"},[n("v-uni-view",[n("v-uni-view",{staticClass:"user-avatar-box flex bg-white",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.handleTouser.apply(void 0,arguments)}}},[n("v-uni-image",{staticClass:"user-avatar column column-center",attrs:{src:""!=e.userInfo.avatar1?e.userInfo.avatar:"/static/images/portrait_empty.png"}}),n("v-uni-view",{staticClass:"flex row-between flex-1"},[n("v-uni-view",{staticClass:"m-l-20 flex-col row-around",staticStyle:{height:"120rpx"}},[n("v-uni-view",[e._v(e._s(e.userInfo.nickname))]),n("v-uni-view",{staticClass:"muted"},[e._v("ID:"+e._s(e.userInfo.sn))])],1),n("u-icon",{attrs:{name:"arrow-right"}})],1)],1),n("v-uni-view",{staticClass:"bg-white m-t-10"},[n("v-uni-view",{staticClass:"row-info flex row-between bdb-line",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.showPwdPop.apply(void 0,arguments)}}},[n("v-uni-view",{staticClass:"label md"},[e._v("登录密码")]),n("v-uni-view",{staticClass:"flex"},[n("v-uni-view",{staticClass:"muted"},[e._v("未设置")]),n("u-icon",{attrs:{name:"arrow-right"}})],1)],1),e.userInfo.oa_auth?e._e():n("v-uni-view",{staticClass:"row-info flex row-between bdb-line",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.bindWeixin.apply(void 0,arguments)}}},[n("v-uni-view",{staticClass:"label md"},[e._v("微信授权")]),n("u-icon",{attrs:{name:"arrow-right"}})],1)],1),n("v-uni-view",{staticClass:"bg-white"},[n("v-uni-view",{staticClass:"row-info flex row-between bdb-line m-t-10",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.goLicense()}}},[n("v-uni-view",{staticClass:"label md"},[e._v("资质信息")]),n("u-icon",{attrs:{name:"arrow-right"}})],1),n("v-uni-view",{staticClass:"row-info flex row-between bdb-line",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.goToExplain(1)}}},[n("v-uni-view",{staticClass:"label md"},[e._v("隐私政策")]),n("u-icon",{attrs:{name:"arrow-right"}})],1),n("v-uni-view",{staticClass:"row-info flex row-between bdb-line mt10",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.goToExplain(0)}}},[n("v-uni-view",{staticClass:"label md"},[e._v("服务协议")]),n("u-icon",{attrs:{name:"arrow-right"}})],1),n("v-uni-view",{staticClass:"row-info flex row-between"},[n("v-uni-view",{staticClass:"label md"},[e._v("关于我们")]),n("v-uni-view",[e._v("v"+e._s(e.version))])],1)],1)],1)],1),n("v-uni-view",{staticClass:"bg-primary br60 white save-btn flex row-center lg",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.logout.apply(void 0,arguments)}}},[e._v("退出登录")]),e.appConfig.copyright_info?n("v-uni-view",{staticClass:"license xs text-center"},[n("v-uni-view",[e._v(e._s(e.appConfig.copyright_info))]),n("v-uni-view",[e._v(e._s(e.appConfig.icp_number))])],1):e._e(),n("u-popup",{attrs:{type:"center",closeable:!0,mode:"center","border-radius":"14"},model:{value:e.showMobile,callback:function(t){e.showMobile=t},expression:"showMobile"}},[n("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:e.showMobile,expression:"showMobile"}],staticClass:"modify-container bg-white"},[n("v-uni-view",{staticClass:"title xl text-center"},[e._v(e._s(e.userInfo.mobile?"更换手机号":"绑定手机号"))]),e.userInfo.mobile?n("u-field",{attrs:{label:"+86","label-width":"100",disabled:!0},model:{value:e.userInfo.mobile,callback:function(t){e.$set(e.userInfo,"mobile",t)},expression:"userInfo.mobile"}}):n("u-field",{attrs:{label:"+86","label-width":"140",placeholder:"请输入手机号"},model:{value:e.new_mobile,callback:function(t){e.new_mobile=t},expression:"new_mobile"}}),n("u-field",{attrs:{label:"验证码","label-width":"140",placeholder:"请输入验证码"},model:{value:e.mobileCode,callback:function(t){e.mobileCode=t},expression:"mobileCode"}},[n("v-uni-view",{staticClass:"primary send-code-btn br60 flex row-center",attrs:{slot:"right"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.sendSmsFun.apply(void 0,arguments)}},slot:"right"},[n("u-verification-code",{ref:"uCode",attrs:{"keep-running":!0,"unique-key":"mobile"},on:{change:function(t){arguments[0]=t=e.$handleEvent(t),e.codeChange.apply(void 0,arguments)}}}),n("v-uni-view",{staticClass:"sm"},[e._v(e._s(e.codeTips))])],1)],1),e.userInfo.mobile?n("u-field",{attrs:{label:"新手机号","label-width":"140",placeholder:"请输入新的手机号码"},model:{value:e.new_mobile,callback:function(t){e.new_mobile=t},expression:"new_mobile"}}):e._e(),n("v-uni-view",{staticClass:"primary m-t-10 xs"},[e._v(e._s(e.userInfo.mobile?"更改":"绑定")+"手机号码成功后，您的账号将会变更为该设置号码")]),n("v-uni-view",{staticClass:"btn bg-primary white flex row-center",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.changeUserMobileFun.apply(void 0,arguments)}}},[e._v("确定")])],1)],1),n("u-popup",{attrs:{closeable:!0,maskCloseAble:!1,mode:"center","border-radius":"14"},model:{value:e.showNickName,callback:function(t){e.showNickName=t},expression:"showNickName"}},[n("v-uni-view",{staticClass:"modify-container bg-white",staticStyle:{width:"70vw",padding:"24rpx"}},[n("v-uni-view",{staticClass:"title xl text-center"},[e._v("修改用户名")]),n("v-uni-form",{on:{submit:function(t){arguments[0]=t=e.$handleEvent(t),e.changeNameConfirm.apply(void 0,arguments)}}},[n("u-form-item",{attrs:{label:"新昵称",labelWidth:120}},[n("v-uni-input",{staticClass:"nr",staticStyle:{height:"60rpx"},attrs:{value:e.userInfo.nickname,name:"nickname",type:"nickname",placeholder:"请输入新的昵称"}})],1),n("v-uni-button",{staticClass:"btn bg-primary white flex row-center",attrs:{"form-type":"submit"}},[e._v("确定")])],1)],1)],1),n("u-popup",{attrs:{closeable:!0,mode:"center","border-radius":"14"},model:{value:e.showPwd,callback:function(t){e.showPwd=t},expression:"showPwd"}},[n("v-uni-view",{staticClass:"modify-container bg-white"},[n("v-uni-view",{staticClass:"title xl text-center"},[e._v("设置密码")]),n("u-field",{attrs:{label:"+86",disabled:!0,"label-width":"100"},model:{value:e.userInfo.mobile,callback:function(t){e.$set(e.userInfo,"mobile",t)},expression:"userInfo.mobile"}}),n("u-field",{attrs:{label:"验证码","label-width":"140",placeholder:"请输入验证码"},model:{value:e.pwdCode,callback:function(t){e.pwdCode=t},expression:"pwdCode"}},[n("v-uni-view",{staticClass:"primary send-code-btn br60 flex row-center",attrs:{slot:"right"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.sendSmsFun.apply(void 0,arguments)}},slot:"right"},[n("u-verification-code",{ref:"uCode",attrs:{"unique-key":"password","keep-running":!0},on:{change:function(t){arguments[0]=t=e.$handleEvent(t),e.codeChange.apply(void 0,arguments)}}}),n("v-uni-view",{staticClass:"sm"},[e._v(e._s(e.codeTips))])],1)],1),n("u-field",{attrs:{label:"设置密码","label-width":"140",type:"password",placeholder:"请输入新密码"},model:{value:e.pwd,callback:function(t){e.pwd=t},expression:"pwd"}}),n("u-field",{attrs:{label:"确认密码","label-width":"140",type:"password",placeholder:"再次输入新密码确认"},model:{value:e.comfirmPwd,callback:function(t){e.comfirmPwd=t},expression:"comfirmPwd"}}),n("v-uni-view",{staticClass:"btn bg-primary white flex row-center",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.forgetPwdFun.apply(void 0,arguments)}}},[e._v("确定")])],1)],1),n("u-picker",{attrs:{mode:"selector","default-selector":[0],range:e.sexList},on:{confirm:function(t){arguments[0]=t=e.$handleEvent(t),e.onConfirm.apply(void 0,arguments)}},model:{value:e.showPicker,callback:function(t){e.showPicker=t},expression:"showPicker"}})],1)},o=[]}}]);