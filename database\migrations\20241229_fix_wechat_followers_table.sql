-- 修复微信关注用户表结构
-- 执行时间：2024-12-29
-- 说明：确保表中有必要的字段

-- 检查并添加create_time字段（如果不存在）
SET @sql = (
    SELECT IF(
        COUNT(*) = 0,
        'ALTER TABLE `ls_wechat_followers` ADD COLUMN `create_time` int(11) DEFAULT NULL COMMENT ''创建时间'';',
        'SELECT ''create_time字段已存在'' as result;'
    )
    FROM information_schema.COLUMNS 
    WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'ls_wechat_followers' 
    AND COLUMN_NAME = 'create_time'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加update_time字段（如果不存在）
SET @sql = (
    SELECT IF(
        COUNT(*) = 0,
        'ALTER TABLE `ls_wechat_followers` ADD COLUMN `update_time` int(11) DEFAULT NULL COMMENT ''更新时间'';',
        'SELECT ''update_time字段已存在'' as result;'
    )
    FROM information_schema.COLUMNS 
    WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'ls_wechat_followers' 
    AND COLUMN_NAME = 'update_time'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 更新subscribe字段注释
ALTER TABLE `ls_wechat_followers` 
MODIFY COLUMN `subscribe` tinyint(1) DEFAULT '1' 
COMMENT '关注状态(0=未关注/已取消关注, 1=已关注, 2=待验证状态-用于同步时临时标记)';

-- 为现有记录设置create_time（如果为空）
UPDATE `ls_wechat_followers` 
SET `create_time` = UNIX_TIMESTAMP() 
WHERE `create_time` IS NULL OR `create_time` = 0;

-- 为现有记录设置update_time（如果为空）
UPDATE `ls_wechat_followers` 
SET `update_time` = UNIX_TIMESTAMP() 
WHERE `update_time` IS NULL OR `update_time` = 0;

-- 显示表结构确认
DESCRIBE `ls_wechat_followers`;
