(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-index-index","bundle_b-pages-community_search-community_search~bundle_b-pages-community_topic-community_topic","bundle_b-pages-nearby_shops-nearby_shops~pages-shop_street-shop_street"],{"017b":function(t,e,A){"use strict";var i=A("048f"),n=A.n(i);n.a},"01c10":function(t,e,A){"use strict";A.d(e,"b",(function(){return n})),A.d(e,"c",(function(){return a})),A.d(e,"a",(function(){return i}));var i={uIcon:A("6976").default,communityList:A("8783").default},n=function(){var t=this,e=t.$createElement,A=t._self._c||e;return A("v-uni-view",{class:["community-recommend bg-white"]},[t.title?A("v-uni-view",{staticClass:"active-hd flex row-between"},[A("v-uni-view",{staticClass:"xxl bold"},[t._v(t._s(t.title))]),A("router-link",{attrs:{to:t.url,navType:"pushTab"}},[A("v-uni-view",{staticClass:"sm"},[t._v("发现好物"),A("u-icon",{attrs:{name:"arrow-right"}})],1)],1)],1):t._e(),A("v-uni-scroll-view",{staticStyle:{"white-space":"nowrap"},attrs:{"scroll-x":!0}},[A("v-uni-view",{staticClass:"community-wrap"},[A("community-list",{attrs:{type:"index",list:t.list}})],1)],1)],1)},a=[]},"02b2":function(t,e,A){var i=A("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */uni-page-body[data-v-01eafde1]{\n  /* 定义一些主题色及基础样式 */font-family:PingFang SC,Arial,Hiragino Sans GB,Microsoft YaHei,sans-serif;font-size:%?28?%;color:#333;padding-bottom:env(safe-area-inset-bottom);background-color:#f6f6f6}body.?%PAGE?%[data-v-01eafde1]{background-color:#f6f6f6}.bold[data-v-01eafde1]{font-weight:700}\n/* 定义字体颜色 */.primary[data-v-01eafde1]{color:#ff2c3c}.bg-primary[data-v-01eafde1]{background-color:#ff2c3c}.bg-white[data-v-01eafde1]{background-color:#fff}.bg-body[data-v-01eafde1]{background-color:#f6f6f6}.bg-gray[data-v-01eafde1]{background-color:#e5e5e5}.black[data-v-01eafde1]{color:#101010}.white[data-v-01eafde1]{color:#fff}.normal[data-v-01eafde1]{color:#333}.lighter[data-v-01eafde1]{color:#666}.muted[data-v-01eafde1]{color:#999}\n/* 定义字体大小 */.xxl[data-v-01eafde1]{font-size:%?36?%}.xl[data-v-01eafde1]{font-size:%?34?%}.lg[data-v-01eafde1]{font-size:%?32?%}.md[data-v-01eafde1]{font-size:%?30?%}.nr[data-v-01eafde1]{font-size:%?28?%}.sm[data-v-01eafde1]{font-size:%?26?%}.xs[data-v-01eafde1]{font-size:%?24?%}.xxs[data-v-01eafde1]{font-size:%?22?%}\n/* 定义常用外边距 */.ml5[data-v-01eafde1]{margin-left:%?5?%}.ml10[data-v-01eafde1]{margin-left:%?10?%}.ml20[data-v-01eafde1]{margin-left:%?20?%}.ml30[data-v-01eafde1]{margin-left:%?30?%}.mr5[data-v-01eafde1]{margin-right:%?5?%}.mr10[data-v-01eafde1]{margin-right:%?10?%}.mr20[data-v-01eafde1]{margin-right:%?20?%}.mr30[data-v-01eafde1]{margin-right:%?30?%}.mt5[data-v-01eafde1]{margin-top:%?5?%}.mt10[data-v-01eafde1]{margin-top:%?10?%}.mt20[data-v-01eafde1]{margin-top:%?20?%}.mt30[data-v-01eafde1]{margin-top:%?30?%}.mb5[data-v-01eafde1]{margin-bottom:%?5?%}.mb10[data-v-01eafde1]{margin-bottom:%?10?%}.mb20[data-v-01eafde1]{margin-bottom:%?20?%}.mb30[data-v-01eafde1]{margin-bottom:%?30?%}\n/* 定义常用的弹性布局 */.flex1[data-v-01eafde1]{flex:1}.flexnone[data-v-01eafde1]{flex:none}.wrap[data-v-01eafde1]{flex-wrap:wrap}.row[data-v-01eafde1]{display:flex;align-items:center}.row-center[data-v-01eafde1]{display:flex;align-items:center;justify-content:center}.row-end[data-v-01eafde1]{display:flex;align-items:center;justify-content:flex-end}.row-between[data-v-01eafde1]{display:flex;align-items:center;justify-content:space-between}.row-around[data-v-01eafde1]{display:flex;align-items:center;justify-content:space-around}.column[data-v-01eafde1]{display:flex;flex-direction:column;justify-content:center}.column-center[data-v-01eafde1]{display:flex;flex-direction:column;align-items:center;justify-content:center}.column-around[data-v-01eafde1]{display:flex;flex-direction:column;align-items:center;justify-content:space-around}.column-end[data-v-01eafde1]{display:flex;flex-direction:column;align-items:center;justify-content:flex-end}.column-between[data-v-01eafde1]{display:flex;flex-direction:column;align-items:center;justify-content:space-between}\n/* 超出隐藏 */.line1[data-v-01eafde1]{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.line-1[data-v-01eafde1]{word-break:break-all;display:-webkit-box;-webkit-line-clamp:1;-webkit-box-orient:vertical;overflow:hidden}.line2[data-v-01eafde1]{word-break:break-all;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical;overflow:hidden}\n/* 中划线 */.line-through[data-v-01eafde1]{text-decoration:line-through}\n/* br60 */.br60[data-v-01eafde1]{border-radius:%?60?%}\n/* 初始化按钮 */uni-page-body uni-button[data-v-01eafde1]{padding:0;margin:0;background-color:initial;font-weight:400;font-size:%?28?%;overflow:unset;margin-left:0;margin-right:0}uni-page-body uni-button[data-v-01eafde1]::after{border:none}uni-button[type=primary][data-v-01eafde1]{background-color:#ff2c3c}.button-hover[type=primary][data-v-01eafde1]{background-color:#ff2c3c}uni-button[disabled][type=primary][data-v-01eafde1]{background-color:#ff2c3c}\n/* 按钮大小 */uni-button[size="xs"][data-v-01eafde1]{line-height:%?58?%;height:%?58?%;font-size:%?26?%;padding:0 %?30?%}uni-button[size="sm"][data-v-01eafde1]{line-height:%?62?%;height:%?62?%;font-size:%?28?%;padding:0 %?30?%}uni-button[size="md"][data-v-01eafde1]{line-height:%?70?%;height:%?70?%;font-size:%?30?%;padding:0 %?30?%}uni-button[size="lg"][data-v-01eafde1]{line-height:%?80?%;height:%?80?%;font-size:%?32?%;padding:0 %?30?%}.icon-xs[data-v-01eafde1]{min-height:%?28?%;min-width:%?28?%;height:%?28?%;width:%?28?%;vertical-align:middle}.icon-sm[data-v-01eafde1]{min-height:%?30?%;min-width:%?30?%;height:%?30?%;width:%?30?%;vertical-align:middle}.icon[data-v-01eafde1]{min-height:%?34?%;min-width:%?34?%;height:%?34?%;width:%?34?%;vertical-align:middle}.icon-md[data-v-01eafde1]{min-height:%?44?%;min-width:%?44?%;height:%?44?%;width:%?44?%;vertical-align:middle}.icon-lg[data-v-01eafde1]{min-height:%?52?%;min-width:%?52?%;height:%?52?%;width:%?52?%;vertical-align:middle}.icon-xl[data-v-01eafde1]{min-height:%?64?%;min-width:%?64?%;height:%?64?%;width:%?64?%;vertical-align:middle}.icon-xxl[data-v-01eafde1]{min-height:%?120?%;min-width:%?120?%;height:%?120?%;width:%?120?%;vertical-align:middle}.img-null[data-v-01eafde1]{width:%?300?%;height:%?300?%}\n/* 隐藏滚动条 */[data-v-01eafde1]::-webkit-scrollbar{width:0;height:0;color:transparent}.bubble-tips-container[data-v-01eafde1]{position:fixed;z-index:98}.bubble-tips-container .bubble-content[data-v-01eafde1]{padding:%?4?% %?20?% %?4?% %?10?%;background-color:rgba(0,0,0,.7);color:#fff;border-radius:%?120?%}.bubble-tips-container .bubble-content .bubble-img[data-v-01eafde1]{width:%?50?%;height:%?50?%;border-radius:50%;margin-right:%?10?%}',""]),t.exports=e},"048f":function(t,e,A){var i=A("5c72");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=A("4f06").default;n("14f65568",i,!0,{sourceMap:!1,shadowMode:!1})},"0523":function(t,e,A){"use strict";A("7a82");var i=A("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,A("d81d"),A("4de4"),A("d3b7"),A("c740");var n=i(A("f3f3")),a=A("26cb"),o={data:function(){return{currentRoute:""}},mounted:function(){var t=getCurrentPages(),e=t[t.length-1];this.currentRoute=e.route},computed:(0,n.default)({tabbarStyle:function(){return this.appConfig.navigation_setting||{}},tabbarList:function(){var t=this,e=this.appConfig.navigation_menu||[];return console.log(this.cartNum),e.filter((function(t){return 1==t.status})).map((function(e){return{iconPath:e.un_selected_icon,selectedIconPath:e.selected_icon,text:e.name,count:"pages/shop_cart/shop_cart"==e.page_path?t.cartNum:0,pagePath:"/"+e.page_path}}))},showTabbar:function(){var t=this,e=this.tabbarList.findIndex((function(e){return e.pagePath==="/"+t.currentRoute}));return e>=0}},(0,a.mapGetters)(["cartNum"]))};e.default=o},"0533":function(t,e,A){t.exports=A.p+"static/images/index_community_bg.png"},"05da":function(t,e,A){"use strict";A.d(e,"b",(function(){return i})),A.d(e,"c",(function(){return n})),A.d(e,"a",(function(){}));var i=function(){var t=this,e=t.$createElement,A=t._self._c||e;return A("v-uni-view",{staticClass:"u-countdown"},[t.showDays&&(t.hideZeroDay||!t.hideZeroDay&&"00"!=t.d)?A("v-uni-view",{staticClass:"u-countdown-item",style:[t.itemStyle]},[A("v-uni-view",{staticClass:"u-countdown-time",style:{fontSize:t.fontSize+"rpx",color:t.color}},[t._v(t._s(t.d))])],1):t._e(),t.showDays&&(t.hideZeroDay||!t.hideZeroDay&&"00"!=t.d)?A("v-uni-view",{style:{fontSize:t.separatorSize+"rpx","margin-right":"6rpx",color:t.separatorColor}},[t._v("天")]):t._e(),t.showHours?A("v-uni-view",{staticClass:"u-countdown-item",style:[t.itemStyle]},[A("v-uni-view",{staticClass:"u-countdown-time",style:{fontSize:t.fontSize+"rpx",color:t.color}},[t._v(t._s(t.h))])],1):t._e(),t.showHours?A("v-uni-view",{staticClass:"u-countdown-colon",style:{fontSize:t.separatorSize+"rpx",color:t.separatorColor,paddingBottom:"colon"==t.separator?"4rpx":0}},[t._v(t._s("colon"==t.separator?":":"时"))]):t._e(),t.showMinutes?A("v-uni-view",{staticClass:"u-countdown-item",style:[t.itemStyle]},[A("v-uni-view",{staticClass:"u-countdown-time",style:{fontSize:t.fontSize+"rpx",color:t.color}},[t._v(t._s(t.i))])],1):t._e(),t.showMinutes?A("v-uni-view",{staticClass:"u-countdown-colon",style:{fontSize:t.separatorSize+"rpx",color:t.separatorColor,paddingBottom:"colon"==t.separator?"4rpx":0}},[t._v(t._s("colon"==t.separator?":":"分"))]):t._e(),t.showSeconds?A("v-uni-view",{staticClass:"u-countdown-item",staticStyle:{width:"36rpx"},style:[t.itemStyle]},[A("v-uni-view",{staticClass:"u-countdown-time",style:{fontSize:t.fontSize+"rpx",color:t.color}},[t._v(t._s(t.s))])],1):t._e(),t.showSeconds&&"zh"==t.separator?A("v-uni-view",{staticClass:"u-countdown-colon",style:{fontSize:t.separatorSize+"rpx",color:t.separatorColor,paddingBottom:"colon"==t.separator?"4rpx":0}},[t._v("秒")]):t._e()],1)},n=[]},"0b17":function(t,e,A){"use strict";var i=A("9ee2"),n=A.n(i);n.a},1034:function(t,e,A){"use strict";A.d(e,"b",(function(){return n})),A.d(e,"c",(function(){return a})),A.d(e,"a",(function(){return i}));var i={uIcon:A("6976").default},n=function(){var t=this,e=t.$createElement,A=t._self._c||e;return A("v-uni-view",{},[A("v-uni-view",{staticClass:"u-navbar",class:{"u-navbar-fixed":t.isFixed,"u-border-bottom":t.borderBottom},style:[t.navbarStyle]},[A("v-uni-view",{staticClass:"u-status-bar",style:{height:t.statusBarHeight+"px"}}),A("v-uni-view",{staticClass:"u-navbar-inner",style:[t.navbarInnerStyle]},[t.isBack?A("v-uni-view",{staticClass:"u-back-wrap",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.goBack.apply(void 0,arguments)}}},[A("v-uni-view",{staticClass:"u-icon-wrap",style:{backgroundColor:t.backBg,borderRadius:"50%",padding:"8rpx"}},[A("u-icon",{attrs:{name:t.isHome?"home":t.backIconName,color:t.backIconColor,size:t.backIconSize}})],1),t.backText?A("v-uni-view",{staticClass:"u-icon-wrap u-back-text u-line-1",style:[t.backTextStyle]},[t._v(t._s(t.backText))]):t._e()],1):t._e(),t.title?A("v-uni-view",{staticClass:"u-navbar-content-title",style:[t.titleStyle]},[A("v-uni-view",{staticClass:"u-title u-line-1",style:{color:t.titleColor,fontSize:t.titleSize+"rpx",fontWeight:t.titleBold?"bold":"normal"}},[t._v(t._s(t.title))])],1):t._e(),A("v-uni-view",{staticClass:"u-slot-content"},[t._t("default")],2),A("v-uni-view",{staticClass:"u-slot-right"},[t._t("right")],2)],1)],1),t.isFixed&&!t.immersive?A("v-uni-view",{staticClass:"u-navbar-placeholder",style:{width:"100%",height:Number(t.navbarHeight)+t.statusBarHeight+"px"}}):t._e()],1)},a=[]},1080:function(t,e,A){"use strict";A.r(e);var i=A("8670"),n=A("bd8c");for(var a in n)["default"].indexOf(a)<0&&function(t){A.d(e,t,(function(){return n[t]}))}(a);A("017b");var o=A("f0c5"),r=Object(o["a"])(n["default"],i["b"],i["c"],!1,null,"e9923850",null,!1,i["a"],void 0);e["default"]=r.exports},"125f":function(t,e,A){var i=A("ef09");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=A("4f06").default;n("2bd7fd7c",i,!0,{sourceMap:!1,shadowMode:!1})},1315:function(t,e,A){"use strict";var i=A("be2e"),n=A.n(i);n.a},"14c8":function(t,e,A){"use strict";var i=A("4be5"),n=A.n(i);n.a},"1b43":function(t,e,A){var i=A("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.u-no-network[data-v-19271174]{background-color:#fff;position:fixed;top:0;left:0;right:0;bottom:0}.u-inner[data-v-19271174]{height:100vh;display:flex;flex-direction:row;flex-direction:column;align-items:center;justify-content:center;margin-top:-15%}.u-tips[data-v-19271174]{color:#909399;font-size:%?28?%;padding:%?30?% 0}.u-error-icon[data-v-19271174]{width:%?300?%}.u-to-setting[data-v-19271174]{color:#c0c4cc;font-size:%?26?%}.u-setting-btn[data-v-19271174]{font-size:%?26?%;color:#ff2c3c}.u-retry[data-v-19271174]{margin-top:%?30?%;border:1px solid #909399;color:#909399;font-size:%?28?%;padding:%?6?% %?30?%;border-radius:3px}.u-retry-hover[data-v-19271174]{color:#fff;background-color:#909399}',""]),t.exports=e},"1de5":function(t,e,A){"use strict";var i=A("9196"),n=A.n(i);n.a},"1de5b":function(t,e,A){"use strict";t.exports=function(t,e){return e||(e={}),t=t&&t.__esModule?t.default:t,"string"!==typeof t?t:(/^['"].*['"]$/.test(t)&&(t=t.slice(1,-1)),e.hash&&(t+=e.hash),/["'() \t\n]/.test(t)||e.needQuotes?'"'.concat(t.replace(/"/g,'\\"').replace(/\n/g,"\\n"),'"'):t)}},2251:function(t,e,A){"use strict";A("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,A("a9e3");var i=uni.getSystemInfoSync(),n={},a={name:"u-navbar",props:{height:{type:[String,Number],default:""},backIconColor:{type:String,default:"#606266"},backBg:{type:String,default:"transparent"},backIconName:{type:String,default:"nav-back"},backIconSize:{type:[String,Number],default:"42"},backText:{type:String,default:""},backTextStyle:{type:Object,default:function(){return{color:"#606266"}}},title:{type:String,default:""},titleWidth:{type:[String,Number],default:"250"},titleColor:{type:String,default:"#606266"},titleBold:{type:Boolean,default:!1},titleSize:{type:[String,Number],default:32},isBack:{type:[Boolean,String],default:!0},background:{type:Object,default:function(){return{background:"#ffffff"}}},isFixed:{type:Boolean,default:!0},immersive:{type:Boolean,default:!1},borderBottom:{type:Boolean,default:!0},zIndex:{type:[String,Number],default:""},customBack:{type:Function,default:null}},data:function(){return{menuButtonInfo:n,statusBarHeight:i.statusBarHeight,isHome:!1}},computed:{navbarInnerStyle:function(){var t={};return t.height=this.navbarHeight+"px",t},navbarStyle:function(){var t={};return t.zIndex=this.zIndex?this.zIndex:this.$u.zIndex.navbar,Object.assign(t,this.background),t},titleStyle:function(){var t={};return t.left=(i.windowWidth-uni.upx2px(this.titleWidth))/2+"px",t.right=(i.windowWidth-uni.upx2px(this.titleWidth))/2+"px",t.width=uni.upx2px(this.titleWidth)+"px",t},navbarHeight:function(){return this.height?this.height:44}},created:function(){var t=getCurrentPages().length;1==t&&(this.isHome=!0)},methods:{goBack:function(){"function"===typeof this.customBack?this.customBack.bind(this.$u.$parent.call(this))():this.isHome?uni.switchTab({url:"/pages/index/index"}):uni.navigateBack()}}};e.default=a},2639:function(t,e,A){"use strict";A.r(e);var i=A("d653"),n=A.n(i);for(var a in i)["default"].indexOf(a)<0&&function(t){A.d(e,t,(function(){return i[t]}))}(a);e["default"]=n.a},"271d":function(t,e,A){"use strict";A.d(e,"b",(function(){return n})),A.d(e,"c",(function(){return a})),A.d(e,"a",(function(){return i}));var i={uIcon:A("6976").default,goodsList:A("c574").default},n=function(){var t=this,e=t.$createElement,A=t._self._c||e;return A("v-uni-view",{class:["active-area bg-white",t.type]},[t.title?A("v-uni-view",{staticClass:"active-hd flex row-between"},[A("v-uni-view",{staticClass:"xxl bold"},[t._v(t._s(t.title))]),t.url&&"hot"==t.type?A("router-link",{attrs:{to:t.url}},[A("v-uni-view",{staticClass:"sm"},[t._v("更多"),A("u-icon",{attrs:{name:"arrow-right"}})],1)],1):t._e()],1):t._e(),A("v-uni-view",{staticClass:"active-con"},["hot"==t.type?A("v-uni-scroll-view",{staticStyle:{"white-space":"nowrap"},attrs:{"scroll-x":!0}},[A("v-uni-view",{staticClass:"goods-wrap"},[A("goods-list",{attrs:{"show-bg":!1,type:"row-hot",list:t.list}})],1)],1):t._e(),"new"==t.type?A("goods-list",{attrs:{type:"new",list:t.list,"show-bg":!1}}):t._e()],1),t.url&&"new"==t.type?A("router-link",{attrs:{to:t.url}},[A("v-uni-view",{staticClass:"bg-white xs flex row-center more"},[t._v("查看更多"),A("u-icon",{attrs:{name:"arrow-right"}})],1)],1):t._e()],1)},a=[]},"2b94":function(t,e,A){"use strict";A.r(e);var i=A("b06f"),n=A.n(i);for(var a in i)["default"].indexOf(a)<0&&function(t){A.d(e,t,(function(){return i[t]}))}(a);e["default"]=n.a},"2bd8":function(t,e,A){var i=A("24fb"),n=A("1de5b"),a=A("0533");e=i(!1);var o=n(a);e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.community-recommend[data-v-088590f0]{background-size:100% auto;background-repeat:no-repeat;border-radius:%?14?%;overflow:hidden;background-image:url('+o+")}.community-recommend .active-hd[data-v-088590f0]{padding:%?24?% %?20?%}.community-recommend .community-wrap[data-v-088590f0]{padding:0 %?20?% %?20?%;display:inline-block}.community-recommend .more[data-v-088590f0]{height:%?80?%;border-top:1px solid #e5e5e5}",""]),t.exports=e},"2e13":function(t,e,A){var i=A("6d44");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=A("4f06").default;n("60161b32",i,!0,{sourceMap:!1,shadowMode:!1})},"3ff6":function(t,e,A){"use strict";A("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=A("a5ae"),n={name:"cate-nav",props:{list:{type:Array}},data:function(){return{navSwiperH:0,navList:[],currentSwiper:0}},watch:{list:{handler:function(t){t.length<=5?this.navSwiperH=200:this.navSwiperH=374,this.navList=(0,i.arraySlice)(t)},immediate:!0}},methods:{swiperChange:function(t){this.currentSwiper=t.detail.current}}};e.default=n},"422e":function(t,e,A){"use strict";A.d(e,"b",(function(){return n})),A.d(e,"c",(function(){return a})),A.d(e,"a",(function(){return i}));var i={uWaterfall:A("bb4f").default,goodsList:A("c574").default},n=function(){var t=this,e=t.$createElement,A=t._self._c||e;return t.hasData?A("mescroll-body",{ref:"mescrollRef",attrs:{up:t.upOption,down:t.downOption},on:{init:function(e){arguments[0]=e=t.$handleEvent(e),t.mescrollInit.apply(void 0,arguments)},down:function(e){arguments[0]=e=t.$handleEvent(e),t.downCallback.apply(void 0,arguments)},up:function(e){arguments[0]=e=t.$handleEvent(e),t.upCallback.apply(void 0,arguments)}}},[A("v-uni-view",{staticClass:"goods-column"},[A("v-uni-scroll-view",{attrs:{"scroll-x":"true"}},[A("v-uni-view",{staticClass:"column-wrap"},t._l(t.columnList,(function(e,i){return A("v-uni-view",{key:i,staticClass:"item flex-col m-r-50 muted",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.changeActive(i)}}},[A("v-uni-view",{staticClass:"xxl normal title",class:{bold:t.active==i}},[t._v(t._s(e.name))]),A("v-uni-view",{staticClass:"m-t-8 xs text-center",class:{normal:t.active==i}},[t._v(t._s(e.remark))]),t.active==i?A("v-uni-view",{staticClass:"line br60"}):t._e()],1)})),1)],1)],1),A("v-uni-view",{staticClass:"goods"},[A("u-waterfall",{ref:"uWaterfall",attrs:{"add-time":20},scopedSlots:t._u([{key:"left",fn:function(t){var e=t.leftList;return[A("v-uni-view",{staticStyle:{padding:"0 9rpx 0 30rpx"}},[A("goods-list",{attrs:{width:"336rpx",type:"waterfall",list:e}})],1)]}},{key:"right",fn:function(t){var e=t.rightList;return[A("v-uni-view",{staticStyle:{padding:"0 30rpx 0 9rpx"}},[A("goods-list",{attrs:{width:"336rpx",type:"waterfall",list:e}})],1)]}}],null,!1,2662770354),model:{value:t.goodsList,callback:function(e){t.goodsList=e},expression:"goodsList"}})],1)],1):t._e()},a=[]},"43c1":function(t,e,A){t.exports=A.p+"static/images/index_bg.png"},4572:function(t,e,A){"use strict";var i=A("9019"),n=A.n(i);n.a},"474a":function(t,e,A){"use strict";var i=A("bedf"),n=A.n(i);n.a},"49d8":function(t,e,A){var i=A("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.swiper-wrap[data-v-5a2d1aa7]{overflow:hidden;box-sizing:initial}.swiper-wrap .swiper-con[data-v-5a2d1aa7]{position:relative;height:100%;overflow:hidden;-webkit-transform:translateY(0);transform:translateY(0)}.swiper-wrap .swiper[data-v-5a2d1aa7]{width:100%;height:100%;position:relative}.swiper-wrap .swiper .slide-image[data-v-5a2d1aa7]{height:100%}.swiper-wrap .dots[data-v-5a2d1aa7]{position:absolute;left:50%;-webkit-transform:translateX(-50%);transform:translateX(-50%);bottom:%?20?%;display:flex}.swiper-wrap .dots .dot[data-v-5a2d1aa7]{width:%?8?%;height:%?8?%;border-radius:50%;margin-right:%?10?%;background-color:#fff}.swiper-wrap .dots .dot.active[data-v-5a2d1aa7]{width:%?16?%;border-radius:%?8?%;background-color:#ff2c3c}',""]),t.exports=e},"4b4a":function(t,e,A){var i=A("e179");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=A("4f06").default;n("973711c2",i,!0,{sourceMap:!1,shadowMode:!1})},"4be5":function(t,e,A){var i=A("02b2");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=A("4f06").default;n("70e4970f",i,!0,{sourceMap:!1,shadowMode:!1})},"4f2b":function(t,e,A){"use strict";A("7a82");var i=A("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,A("99af");var n=i(A("d0ff")),a=i(A("f3f3")),o=i(A("f07e")),r=i(A("c964")),s=i(A("9911")),u=i(A("53f3")),c=A("26cb"),l=A("b550"),d=A("2eec"),f=(A("9e3f"),A("a5ae"),getApp(),{name:"首页"}),p={mixins:[u.default,s.default],data:function(){return{offPrompt:!0,active:0,navBg:0,goodsList:[],homeData:{},navList:[],enable:!0,cateList:[f],showCateList:[],liveLists:[],showPrivacyPopup:!1}},onLoad:function(t){var e=this;return(0,r.default)((0,o.default)().mark((function A(){return(0,o.default)().wrap((function(A){while(1)switch(A.prev=A.next){case 0:if(null===t||void 0===t||!t.refresh){A.next=3;break}return A.next=3,e.getHomeFun();case 3:uni.$on("refreshhome",(function(){e.getHomeFun()}));case 4:case"end":return A.stop()}}),A)})))()},onUnload:function(){uni.$off("refreshhome")},onShow:function(){this.enable=0==this.active,this.getCartNum()},onHide:function(){this.enable=!1},onReady:function(){},methods:(0,a.default)((0,a.default)((0,a.default)({},(0,c.mapActions)(["getCartNum","getUser","initLocationFunc"])),(0,c.mapMutations)(["setConfig"])),{},{handleRetry:function(){var t=this;return(0,r.default)((0,o.default)().mark((function e(){var A,i,n;return(0,o.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return console.log("网络重试刷"),e.next=3,(0,d.getConfig)();case 3:A=e.sent,i=A.code,n=A.data,1==i&&(t.setConfig(n),n.is_open_nearby&&t.initLocationFunc()),t.getUser();case 8:case"end":return e.stop()}}),e)})))()},getLevelOneListFun:function(){var t=this;return(0,r.default)((0,o.default)().mark((function e(){var A,i,a;return(0,o.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,(0,l.getLevelOneList)();case 2:A=e.sent,i=A.code,a=A.data,1==i&&(t.cateList=[f].concat((0,n.default)(a)),t.showCateList=[]);case 6:case"end":return e.stop()}}),e)})))()},getMenuFun:function(){var t=this;return(0,r.default)((0,o.default)().mark((function e(){var A,i,n;return(0,o.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,(0,l.getMenu)({type:1});case 2:A=e.sent,i=A.code,n=A.data,1==i&&(t.navList=n);case 6:case"end":return e.stop()}}),e)})))()},getHomeFun:function(){var t=this;return(0,r.default)((0,o.default)().mark((function e(){var A,i,n;return(0,o.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,(0,l.getHome)({city_id:t.cityInfo.id});case 2:A=e.sent,i=A.code,n=A.data,1==i&&(t.homeData=n);case 6:case"end":return e.stop()}}),e)})))()},changeActive:function(t){this.active=t,this.enable=0==t,this.mescroll.optDown.use=0==t,this.showCateList[t]=!0},downCallback:function(){var t=this;return(0,r.default)((0,o.default)().mark((function e(){return(0,o.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.getLevelOneListFun();case 2:return e.next=4,t.getMenuFun();case 4:return e.next=6,t.getHomeFun();case 6:t.$refs.mescrollItem.getData(),t.mescroll.endSuccess(0,!1);case 8:case"end":return e.stop()}}),e)})))()}}),computed:(0,a.default)((0,a.default)({},(0,c.mapGetters)(["sysInfo","inviteCode","appConfig","cityInfo"])),{},{navH:function(){return this.sysInfo.navHeight+"px"},cateTop:function(){return this.sysInfo.navHeight+uni.upx2px(80)+"px"},headerStyle:function(){var t=this.active,e=this.cateList,A=this.appConfig,i=e[t].bg_image;return 0==t&&(i=A.index_setting.top_bg_image),i?{"background-image":"url(".concat(i,")")}:{}}})};e.default=p},"509b":function(t,e){t.exports="data:image/png;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAA4KCwwLCQ4MCwwQDw4RFSMXFRMTFSsfIRojMy02NTItMTA4P1FFODxNPTAxRmBHTVRWW1xbN0RjamNYalFZW1f/2wBDAQ8QEBUSFSkXFylXOjE6V1dXV1dXV1dXV1dXV1dXV1dXV1dXV1dXV1dXV1dXV1dXV1dXV1dXV1dXV1dXV1dXV1f/wgARCAHMArIDAREAAhEBAxEB/8QAGQABAQEBAQEAAAAAAAAAAAAAAAECAwQH/8QAFwEBAQEBAAAAAAAAAAAAAAAAAAECA//aAAwDAQACEAMQAAAA+i51yu5KAAAAAAAAAAANIMqAAAAAAAAAAAAAAIoAqRds5UAAAAAAAADVlme2sefPWBQAAAAAAAAAAANJlQAAAAAAAAAAAAABFABNJTKgAAAAAAAAAbZigAAAAAAAAAAAAADKgAAAAAAAAAAAACKKkXTNAAAAAAAAAAAAAAAAAAAAAAAAAAAMqAAAAAAAAAAAABFFTSKQAAAAAAAAAAAAAAAAAAAAAAAAAAABlQAAAAAAAAAAABasgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAyoAAAAAAAEtsgltkqUIUAAAAAAAAAAAAAAAAAAAAAAAAAAABQQFBEIoAAAAAAAVpAAAAAAAAAAAAAAAAgAAAAAAAAAKAAACAoiUKIlBAIpCiIKoIUUJNWwACFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAQAAoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAIAAAAAAAAAAAAAAAAAAAAAAAABCgAAAAAAAAAAAAAAAAAAAVAAAAAAAAAAAAAAAAAAAAAAAAAABFAAAAAAAAAAAAAAAAtgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABUAAAAAAAAAAAAAAAAAAAAAAAAigAAAAAAAAAAAAAAAAACoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAEAAAAAAAAAAAAAAAAAAKAAAAAAAAAAAAAAAAAAAAAAAACAAAAAAAAAAAAAAAAAALYAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAKAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD/xAAhEAADAAEEAQUAAAAAAAAAAAAAARFAAhIgUGAQITBwwP/aAAgBAQABPwDXqnsu/SxtGt2PPfQpUWPuffJUXcTNS7yZMF38xUheBwhPlhCeAThOMIREIbTaQhEQnpCcZ9738BH/AP/EABQRAQAAAAAAAAAAAAAAAAAAAMD/2gAIAQIBAT8AHbf/xAAYEQEAAwEAAAAAAAAAAAAAAAABABFgwP/aAAgBAwEBPwAIZRMxXGE//9k="},"51bc":function(t,e,A){"use strict";A.d(e,"b",(function(){return n})),A.d(e,"c",(function(){return a})),A.d(e,"a",(function(){return i}));var i={uSticky:A("1080").default,uNavbar:A("53a6").default,uIcon:A("6976").default,uSearch:A("5744").default,uTabs:A("1704").default,indexHome:A("c383").default,goodsColumn:A("c943").default,cateHome:A("b572").default,tabbar:A("a6c8").default,uNoNetwork:A("c88b").default},n=function(){var t=this,e=t.$createElement,A=t._self._c||e;return A("v-uni-view",[A("mescroll-body",{ref:"mescrollRef",attrs:{up:{use:!1,toTop:{bottom:"300rpx"}}},on:{init:function(e){arguments[0]=e=t.$handleEvent(e),t.mescrollInit.apply(void 0,arguments)},down:function(e){arguments[0]=e=t.$handleEvent(e),t.downCallback.apply(void 0,arguments)},up:function(e){arguments[0]=e=t.$handleEvent(e),t.upCallback.apply(void 0,arguments)}}},[A("v-uni-view",{staticClass:"index index-bg",style:[t.headerStyle]},[A("u-sticky",{attrs:{"offset-top":"0","h5-nav-height":"0","bg-color":"transparent"}},[A("v-uni-view",{staticClass:"index-bg",style:[t.headerStyle]},[A("u-navbar",{attrs:{"is-fixed":!1,"border-bottom":!1,background:{background:"transparent"},"is-back":!1}},[A("v-uni-view",{staticClass:"flex-1 flex row",staticStyle:{position:"relative"}},[t.appConfig.is_open_nearby?A("router-link",{staticClass:"m-l-16 flex white row-center",attrs:{to:"/bundle_b/pages/city/city"}},[A("v-uni-text",{staticClass:"m-r-6"},[t._v(t._s(t.cityInfo.name||"选择"))]),A("u-icon",{attrs:{name:"arrow-down",size:"24",color:"#FFFFFF"}})],1):t._e(),A("v-uni-view",{staticClass:"flex-1"},[A("router-link",{attrs:{to:"/pages/goods_search/goods_search"}},[A("u-search",{attrs:{"wrap-bg-color":"transparent","bg-color":"#fff",disabled:!0,height:62}})],1)],1)],1)],1),A("v-uni-view",{staticClass:"flex"},[A("v-uni-view",{staticClass:"flex-1",staticStyle:{"min-width":"600rpx"}},[A("u-tabs",{attrs:{"active-color":"#fff","inactive-color":"#fff",current:t.active,list:t.cateList,"bg-color":"transparent","active-item-style":{fontSize:"32rpx"},"bar-width":64,bold:!1,barStyle:{background:"linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, #FFFFFF 49%, rgba(255, 255, 255, 0) 100%)"}},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.changeActive.apply(void 0,arguments)}}})],1),A("router-link",{attrs:{to:"/pages/goods_cate/goods_cate",navType:"pushTab"}},[A("v-uni-view",{staticClass:"white flex cate-btn m-l-10"},[A("v-uni-image",{staticClass:"icon-xs m-r-4",attrs:{src:"/static/images/icon_cate.png",alt:""}}),t._v("分类")],1)],1)],1)],1)],1),A("v-uni-view",{staticClass:"tab-con"},[A("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:0==t.active,expression:"active == 0"}]},[A("index-home",{ref:"indexHome",attrs:{"home-data":t.homeData,"nav-list":t.navList,"live-list":t.liveLists}}),A("goods-column",{ref:"mescrollItem",attrs:{autoGetData:!1}})],1),t._l(t.cateList,(function(e,i){return[i>0?A("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:t.active==i,expression:"active == index"}],key:i,staticClass:"tab-item"},[A("v-uni-view",[t.showCateList[i]?A("cate-home",{ref:"cateItem",refInFor:!0,attrs:{top:t.cateTop,i:i,index:t.active,cate:e}}):t._e()],1)],1):t._e()]}))],2)],1)],1),A("v-uni-view",{staticClass:"record_number"},[A("a",{staticStyle:{color:"#495770","text-decoration":"none"},attrs:{href:t.appConfig.icp_link}},[t._v(t._s(t.appConfig.icp_number))])]),A("tabbar"),A("u-no-network",{attrs:{"z-index":"1200"},on:{retry:function(e){arguments[0]=e=t.$handleEvent(e),t.handleRetry.apply(void 0,arguments)}}})],1)},a=[]},"53a6":function(t,e,A){"use strict";A.r(e);var i=A("1034"),n=A("ce5f");for(var a in n)["default"].indexOf(a)<0&&function(t){A.d(e,t,(function(){return n[t]}))}(a);A("73cb");var o=A("f0c5"),r=Object(o["a"])(n["default"],i["b"],i["c"],!1,null,"6d93ee5a",null,!1,i["a"],void 0);e["default"]=r.exports},5458:function(t,e,A){"use strict";A.d(e,"b",(function(){return n})),A.d(e,"c",(function(){return a})),A.d(e,"a",(function(){return i}));var i={mescrollUni:A("5403").default,cateNav:A("6a72").default,activeArea:A("7374").default,uWaterfall:A("bb4f").default,goodsList:A("c574").default},n=function(){var t=this,e=t.$createElement,A=t._self._c||e;return A("v-uni-view",{staticClass:"cate-home"},[A("mescroll-uni",{ref:"mescrollRef"+t.i,attrs:{"bg-color":"#f4f4f4",top:t.positionTop,bottom:"50px",safearea:!0,down:t.downOption,up:t.upOption},on:{init:function(e){arguments[0]=e=t.$handleEvent(e),t.mescrollInit.apply(void 0,arguments)},down:function(e){arguments[0]=e=t.$handleEvent(e),t.downCallback.apply(void 0,arguments)},up:function(e){arguments[0]=e=t.$handleEvent(e),t.upCallback.apply(void 0,arguments)}}},[A("v-uni-view",{staticClass:"content"},[A("v-uni-view",{staticStyle:{margin:"0 30rpx"}},[A("cate-nav",{attrs:{list:t.navList}})],1),t.hotGoods.length?A("v-uni-view",{staticClass:"contain"},[A("active-area",{attrs:{list:t.hotGoods,type:"hot",title:"品类热销"}})],1):t._e(),t.newGoods.length?A("v-uni-view",{staticClass:"contain"},[A("active-area",{attrs:{list:t.newGoods,type:"new",title:"品类推荐"}})],1):t._e(),A("v-uni-view",{staticClass:"goods"},[A("v-uni-image",{staticClass:"title-img",attrs:{src:"/static/images/category_title.png"}}),A("u-waterfall",{ref:"uWaterfall",attrs:{"add-time":20},scopedSlots:t._u([{key:"left",fn:function(t){var e=t.leftList;return[A("v-uni-view",{staticStyle:{padding:"0 9rpx 0 30rpx"}},[A("goods-list",{attrs:{width:"336rpx",type:"waterfall",list:e}})],1)]}},{key:"right",fn:function(t){var e=t.rightList;return[A("v-uni-view",{staticStyle:{padding:"0 30rpx 0 9rpx"}},[A("goods-list",{attrs:{width:"336rpx",type:"waterfall",list:e}})],1)]}}]),model:{value:t.goodsList,callback:function(e){t.goodsList=e},expression:"goodsList"}})],1)],1)],1)],1)},a=[]},5744:function(t,e,A){"use strict";A.r(e);var i=A("b413"),n=A("f9f3");for(var a in n)["default"].indexOf(a)<0&&function(t){A.d(e,t,(function(){return n[t]}))}(a);A("f5a5");var o=A("f0c5"),r=Object(o["a"])(n["default"],i["b"],i["c"],!1,null,"3c66e606",null,!1,i["a"],void 0);e["default"]=r.exports},"574d":function(t,e,A){"use strict";A("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,A("a9e3");var i={name:"u-search",props:{shape:{type:String,default:"round"},bgColor:{type:String,default:"#f2f2f2"},placeholder:{type:String,default:"请输入关键字"},clearabled:{type:Boolean,default:!0},focus:{type:Boolean,default:!1},showAction:{type:Boolean,default:!0},actionStyle:{type:Object,default:function(){return{}}},actionText:{type:String,default:"搜索"},inputAlign:{type:String,default:"left"},disabled:{type:Boolean,default:!1},animation:{type:Boolean,default:!1},borderColor:{type:String,default:"none"},value:{type:String,default:""},height:{type:[Number,String],default:64},inputStyle:{type:Object,default:function(){return{}}},maxlength:{type:[Number,String],default:"-1"},searchIconColor:{type:String,default:""},color:{type:String,default:"#606266"},placeholderColor:{type:String,default:"#909399"},margin:{type:String},searchIcon:{type:String,default:"search"},wrapBgColor:{type:String,default:"#fff"},hideRight:{type:Boolean,default:!1}},data:function(){return{keyword:"",showClear:!1,show:!1,focused:this.focus}},watch:{keyword:function(t){this.$emit("input",t),this.$emit("change",t)},value:{immediate:!0,handler:function(t){this.keyword=t}}},computed:{showActionBtn:function(){return!(this.animation||!this.showAction)},borderStyle:function(){return this.borderColor?"1px solid ".concat(this.borderColor):"none"}},methods:{inputChange:function(t){this.keyword=t.detail.value},clear:function(){var t=this;this.keyword="",this.$nextTick((function(){t.$emit("clear")}))},search:function(t){this.$emit("search",t.detail.value);try{uni.hideKeyboard()}catch(t){}},custom:function(){this.$emit("custom",this.keyword);try{uni.hideKeyboard()}catch(t){}},getFocus:function(){this.focused=!0,this.animation&&this.showAction&&(this.show=!0),this.$emit("focus",this.keyword)},blur:function(){var t=this;setTimeout((function(){t.focused=!1}),100),this.show=!1,this.$emit("blur",this.keyword)},clickHandler:function(){this.disabled&&this.$emit("click")}}};e.default=i},"59ef":function(t,e,A){"use strict";A.r(e);var i=A("01c10"),n=A("64d4");for(var a in n)["default"].indexOf(a)<0&&function(t){A.d(e,t,(function(){return n[t]}))}(a);A("474a");var o=A("f0c5"),r=Object(o["a"])(n["default"],i["b"],i["c"],!1,null,"088590f0",null,!1,i["a"],void 0);e["default"]=r.exports},"5bc4":function(t,e,A){"use strict";A("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=A("a5ae"),n={name:"shop-recommend",props:{type:{type:String,default:""},title:{type:String,default:""},url:{type:String,default:""},list:{type:Array,default:function(){return[]}}},data:function(){return{swiperH:0,currentSwiper:0,shopList:[]}},methods:{swiperChange:function(t){this.currentSwiper=t.detail.current}},watch:{list:{handler:function(t){t.length<=3?this.swiperH=320:this.swiperH=606,this.shopList=(0,i.arraySlice)(t,[],6)},immediate:!0}}};e.default=n},"5bc8":function(t,e,A){var i=A("24fb"),n=A("1de5b"),a=A("a88a");e=i(!1);var o=n(a);e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.home-seckill[data-v-154ee532]{border-radius:%?14?%;overflow:hidden}.home-seckill .seckill-hd[data-v-154ee532]{padding:0 %?20?%;height:%?100?%;background:url('+o+") no-repeat;background-size:100%}.home-seckill .time-item[data-v-154ee532]{display:inline-flex;width:%?160?%;height:100%}.home-seckill .time-item.active .time[data-v-154ee532]{color:#ff2c3c}.home-seckill .time-item.active .state[data-v-154ee532]{color:#fff;background-color:#ff2c3c}.home-seckill .time-item .state[data-v-154ee532]{padding:0 %?10?%}.home-seckill .goods-seckill .item[data-v-154ee532]{padding:%?20?%}.home-seckill .goods-seckill .item .border-btn[data-v-154ee532]{border:1px solid #ff2c3c;color:#ff2c3c}.home-seckill .more[data-v-154ee532]{height:%?80?%;border-top:1px solid #e5e5e5}",""]),t.exports=e},"5bff":function(t,e,A){"use strict";var i=A("5e36"),n=A.n(i);n.a},"5c72":function(t,e,A){var i=A("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.u-sticky[data-v-e9923850]{z-index:9999999999}',""]),t.exports=e},"5e36":function(t,e,A){var i=A("995f");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=A("4f06").default;n("a7a627a4",i,!0,{sourceMap:!1,shadowMode:!1})},"607d":function(t,e,A){"use strict";A.r(e);var i=A("6223"),n=A.n(i);for(var a in i)["default"].indexOf(a)<0&&function(t){A.d(e,t,(function(){return i[t]}))}(a);e["default"]=n.a},6119:function(t,e,A){"use strict";A.d(e,"b",(function(){return n})),A.d(e,"c",(function(){return a})),A.d(e,"a",(function(){return i}));var i={uIcon:A("6976").default,uBadge:A("c93e").default},n=function(){var t=this,e=t.$createElement,A=t._self._c||e;return t.show?A("v-uni-view",{staticClass:"u-tabbar",on:{touchmove:function(e){e.stopPropagation(),e.preventDefault(),arguments[0]=e=t.$handleEvent(e),function(){}.apply(void 0,arguments)}}},[A("v-uni-view",{staticClass:"u-tabbar__content safe-area-inset-bottom",class:{"u-border-top":t.borderTop},style:{height:t.$u.addUnit(t.height),backgroundColor:t.bgColor}},[t._l(t.list,(function(e,i){return A("v-uni-view",{key:i,staticClass:"u-tabbar__content__item",class:{"u-tabbar__content__circle":t.midButton&&e.midButton},style:{backgroundColor:t.bgColor},on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.clickHandler(i)}}},[A("v-uni-view",{class:[t.midButton&&e.midButton?"u-tabbar__content__circle__button":"u-tabbar__content__item__button"]},[A("u-icon",{attrs:{size:t.midButton&&e.midButton?t.midButtonSize:t.iconSize,name:t.elIconPath(i),"img-mode":"scaleToFill",color:t.elColor(i),"custom-prefix":e.customIcon?"custom-icon":"uicon"}}),e.count?A("u-badge",{attrs:{count:e.count,"is-dot":e.isDot,offset:[-2,t.getOffsetRight(e.count,e.isDot)]}}):t._e()],1),A("v-uni-view",{staticClass:"u-tabbar__content__item__text",style:{color:t.elColor(i)}},[A("v-uni-text",{staticClass:"u-line-1"},[t._v(t._s(e.text))])],1)],1)})),t.midButton?A("v-uni-view",{staticClass:"u-tabbar__content__circle__border",class:{"u-border":t.borderTop},style:{backgroundColor:t.bgColor,left:t.midButtonLeft}}):t._e()],2),A("v-uni-view",{staticClass:"u-fixed-placeholder safe-area-inset-bottom",style:{height:"calc("+t.$u.addUnit(t.height)+" + "+(t.midButton?48:0)+"rpx)"}})],1):t._e()},a=[]},6223:function(t,e,A){"use strict";A("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i={props:{list:{type:Array,default:function(){return[]}},width:{type:String,default:"347rpx"},type:{type:String}},data:function(){return{}}};e.default=i},6326:function(t,e,A){"use strict";A.r(e);var i=A("9dbd"),n=A.n(i);for(var a in i)["default"].indexOf(a)<0&&function(t){A.d(e,t,(function(){return i[t]}))}(a);e["default"]=n.a},"64d4":function(t,e,A){"use strict";A.r(e);var i=A("aac5"),n=A.n(i);for(var a in i)["default"].indexOf(a)<0&&function(t){A.d(e,t,(function(){return i[t]}))}(a);e["default"]=n.a},6737:function(t,e,A){"use strict";A("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i={name:"shop-item",props:{item:{type:Object,defalut:function(){return{}}}},data:function(){return{}}};e.default=i},"6a72":function(t,e,A){"use strict";A.r(e);var i=A("c038"),n=A("8dad");for(var a in n)["default"].indexOf(a)<0&&function(t){A.d(e,t,(function(){return n[t]}))}(a);A("8b7c");var o=A("f0c5"),r=Object(o["a"])(n["default"],i["b"],i["c"],!1,null,"8168704c",null,!1,i["a"],void 0);e["default"]=r.exports},"6adb":function(t,e){t.exports="data:image/png;base64,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"},"6bbc":function(t,e,A){"use strict";A.r(e);var i=A("fc0d"),n=A.n(i);for(var a in i)["default"].indexOf(a)<0&&function(t){A.d(e,t,(function(){return i[t]}))}(a);e["default"]=n.a},"6c53":function(t,e,A){"use strict";A.d(e,"b",(function(){return i})),A.d(e,"c",(function(){return n})),A.d(e,"a",(function(){}));var i=function(){var t=this,e=t.$createElement,A=t._self._c||e;return t.isConnected?t._e():A("v-uni-view",{staticClass:"u-no-network",style:{"z-index":t.uZIndex},on:{touchmove:function(e){e.stopPropagation(),e.preventDefault(),arguments[0]=e=t.$handleEvent(e),function(){}.apply(void 0,arguments)}}},[A("v-uni-view",{staticClass:"u-inner"},[A("v-uni-image",{staticClass:"u-error-icon",attrs:{src:t.image,mode:"widthFix"}}),A("v-uni-view",{staticClass:"u-tips"},[t._v(t._s(t.tips))]),A("v-uni-view",{staticClass:"u-retry",attrs:{"hover-stay-time":150,"hover-class":"u-retry-hover"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.retry.apply(void 0,arguments)}}},[t._v("重试")])],1)],1)},n=[]},"6d44":function(t,e,A){var i=A("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.u-countdown[data-v-38cbd707]{display:inline-flex;align-items:center}.u-countdown-item[data-v-38cbd707]{display:flex;flex-direction:row;align-items:center;justify-content:center;padding:%?2?%;border-radius:%?6?%;white-space:nowrap;-webkit-transform:translateZ(0);transform:translateZ(0)}.u-countdown-time[data-v-38cbd707]{margin:0;padding:0}.u-countdown-colon[data-v-38cbd707]{display:flex;flex-direction:row;justify-content:center;padding:0 %?5?%;line-height:1;align-items:center;padding-bottom:%?4?%}.u-countdown-scale[data-v-38cbd707]{-webkit-transform:scale(.9);transform:scale(.9);-webkit-transform-origin:center center;transform-origin:center center}',""]),t.exports=e},"6eea":function(t,e,A){var i=A("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.u-wrap[data-v-a968d7f2]{background-color:#eee;overflow:hidden}.u-lazy-item[data-v-a968d7f2]{width:100%;-webkit-transform:transition3d(0,0,0);transform:transition3d(0,0,0);will-change:transform;display:block;max-height:240px!important}',""]),t.exports=e},"715c":function(t,e,A){"use strict";A.r(e);var i=A("921a"),n=A.n(i);for(var a in i)["default"].indexOf(a)<0&&function(t){A.d(e,t,(function(){return i[t]}))}(a);e["default"]=n.a},7374:function(t,e,A){"use strict";A.r(e);var i=A("271d"),n=A("f011");for(var a in n)["default"].indexOf(a)<0&&function(t){A.d(e,t,(function(){return n[t]}))}(a);A("f879");var o=A("f0c5"),r=Object(o["a"])(n["default"],i["b"],i["c"],!1,null,"264e701a",null,!1,i["a"],void 0);e["default"]=r.exports},7383:function(t,e,A){"use strict";A.d(e,"b",(function(){return n})),A.d(e,"c",(function(){return a})),A.d(e,"a",(function(){return i}));var i={uImage:A("f919").default},n=function(){var t=this,e=t.$createElement,A=t._self._c||e;return A("v-uni-view",{staticClass:"bubble-tips-container",style:{top:t.top,left:t.left}},t._l(t.currentList,(function(e){return A("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:t.showBubble,expression:"showBubble"}],key:e.id,staticClass:"bubble-content row"},[A("u-image",{staticClass:"bubble-img",attrs:{width:"50rpx",height:"50rpx",src:e.user.avatar,"border-radius":"50%"}}),A("v-uni-view",{staticClass:"xs"},[t._v(t._s(e.template))])],1)})),1)},a=[]},"73cb":function(t,e,A){"use strict";var i=A("bdc6"),n=A.n(i);n.a},"761c":function(t,e,A){"use strict";A.r(e);var i=A("f215"),n=A("9da6");for(var a in n)["default"].indexOf(a)<0&&function(t){A.d(e,t,(function(){return n[t]}))}(a);A("7eb4");var o=A("f0c5"),r=Object(o["a"])(n["default"],i["b"],i["c"],!1,null,"1cd62f78",null,!1,i["a"],void 0);e["default"]=r.exports},7655:function(t,e,A){var i=A("49d8");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=A("4f06").default;n("1dd734b6",i,!0,{sourceMap:!1,shadowMode:!1})},"76a9":function(t,e,A){var i=A("24fb"),n=A("1de5b"),a=A("6adb"),o=A("509b");e=i(!1);var r=n(a),s=n(o);e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.active-area[data-v-264e701a]{background-size:100% auto;background-repeat:no-repeat;border-radius:%?14?%;overflow:hidden}.active-area.new[data-v-264e701a]{background-image:url('+r+")}.active-area.hot[data-v-264e701a]{background-image:url("+s+")}.active-area .active-hd[data-v-264e701a]{padding:%?24?% %?20?%}.active-area .goods-wrap[data-v-264e701a]{padding:0 %?20?% %?20?%;display:inline-block}.active-area .more[data-v-264e701a]{height:%?80?%;border-top:1px solid #e5e5e5}",""]),t.exports=e},7942:function(t,e,A){"use strict";A("7a82");var i=A("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,A("a9e3"),A("e9c4"),A("fb6a");var n=A("2eec"),a=i(A("0de5")),o={name:"BubbleTips",props:{discharge:{type:Boolean,default:!1},top:{type:String,default:"40rpx"},left:{type:String,default:"20rpx"},updateTime:{type:Number,default:3e5}},data:function(){return{index:a.default.get("currentIndex")||0,list:[],currentList:[],timer:null,showBubble:!1}},watch:{index:function(t,e){var A=this;if(!(this.index-this.list.length>=0))return this.timer&&(clearInterval(this.timer),this.timer=null),void this.fadeUpBubble();this.showBubble=!1;var i=setTimeout((function(){a.default.set("currentIndex",0),A.timer&&(clearInterval(A.timer),A.timer=null),A.fadeUpBubble(),clearTimeout(i)}),2e3)},discharge:function(){if(this.discharge)return a.default.set("currentIndex",this.index),clearInterval(this.timer),this.timer=null,!1;var t=a.default.get("currentInex")||this.list.length;t-this.list.length<0&&(this.timer&&(setInterval(this.timer),this.timer=null),this.fadeUpBubble())}},methods:{getBubbleListsFunc:function(){var t=this;(0,n.getBubbleLists)().then((function(e){if(e){t.list=e.data.lists;var A=1e3*e.data.time;a.default.set("bubbleList",JSON.stringify(t.list),300),a.default.set("requestTime",A),t.timer&&(clearInterval(t.timer),t.timer=null),t.fadeUpBubble()}}))},fadeUpBubble:function(){var t=this,e=a.default.get("requestTime"),A=new Date;if(this.showBubble=!0,this.index=a.default.get("currentIndex")||0,this.list=a.default.get("bubbleList")?JSON.parse(a.default.get("bubbleList")):[],A.getTime()-e>=this.updateTime)return this.getBubbleListsFunc(),void a.default.set("currentIndex",0,300);this.timer=setInterval((function(){t.currentList=t.list.slice(t.index,t.index+1),a.default.set("currentIndex",++t.index)}),4e3)}},created:function(){var t=a.default.get("currentIndex")||0,e=a.default.get("requestTime"),A=new Date,i=a.default.get("bubbleList")?JSON.parse(a.default.get("bubbleList")):[];i.length<=0?(this.getBubbleListsFunc(),a.default.set("currentIndex",0)):t-i.length>=0?(a.default.set("currentIndex",0),this.timer&&(clearInterval(this.timer),this.timer=null),this.fadeUpBubble()):A.getTime()-e>=this.updateTime?(this.getBubbleListsFunc(),a.default.set("currentIndex",0)):(this.timer&&(clearInterval(this.timer),this.timer=null),this.fadeUpBubble())},onLoad:function(){},destroyed:function(){this.timer&&(clearInterval(this.timer),this.timer=null)}};e.default=o},"79ec":function(t,e,A){"use strict";A.d(e,"b",(function(){return n})),A.d(e,"c",(function(){return a})),A.d(e,"a",(function(){return i}));var i={uTabbar:A("9644").default},n=function(){var t=this.$createElement,e=this._self._c||t;return e("u-tabbar",{directives:[{name:"show",rawName:"v-show",value:this.showTabbar,expression:"showTabbar"}],attrs:{activeColor:this.tabbarStyle.st_color,inactiveColor:this.tabbarStyle.ust_color,list:this.tabbarList}})},a=[]},"7bfe":function(t,e,A){"use strict";A("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,A("a9e3");var i={name:"u-count-down",props:{timestamp:{type:[Number,String],default:0},autoplay:{type:Boolean,default:!0},separator:{type:String,default:"colon"},separatorSize:{type:[Number,String],default:30},separatorColor:{type:String,default:"#303133"},color:{type:String,default:"#303133"},fontSize:{type:[Number,String],default:30},bgColor:{type:String,default:"#fff"},height:{type:[Number,String],default:"auto"},showBorder:{type:Boolean,default:!1},borderColor:{type:String,default:"#303133"},showSeconds:{type:Boolean,default:!0},showMinutes:{type:Boolean,default:!0},showHours:{type:Boolean,default:!0},showDays:{type:Boolean,default:!0},hideZeroDay:{type:Boolean,default:!1}},watch:{timestamp:function(t,e){this.clearTimer(),this.start()}},data:function(){return{d:"00",h:"00",i:"00",s:"00",timer:null,seconds:0}},computed:{itemStyle:function(){var t={};return this.height&&(t.height=this.height+"rpx"),this.showBorder&&(t.borderStyle="solid",t.borderColor=this.borderColor,t.borderWidth="1px"),this.bgColor&&(t.backgroundColor=this.bgColor),t},letterStyle:function(){var t={};return this.fontSize&&(t.fontSize=this.fontSize+"rpx"),this.color&&(t.color=this.color),t}},mounted:function(){this.autoplay&&this.timestamp&&this.start()},methods:{start:function(){var t=this;this.clearTimer(),this.timestamp<=0||(this.seconds=Number(this.timestamp),this.formatTime(this.seconds),this.timer=setInterval((function(){if(t.seconds--,t.$emit("change",t.seconds),t.seconds<0)return t.end();t.formatTime(t.seconds)}),1e3))},formatTime:function(t){t<=0&&this.end();var e,A=0,i=0,n=0;A=Math.floor(t/86400),e=Math.floor(t/3600)-24*A;var a=null;a=this.showDays?e:Math.floor(t/3600),i=Math.floor(t/60)-60*e-24*A*60,n=Math.floor(t)-24*A*60*60-60*e*60-60*i,a=a<10?"0"+a:a,i=i<10?"0"+i:i,n=n<10?"0"+n:n,A=A<10?"0"+A:A,this.d=A,this.h=a,this.i=i,this.s=n},end:function(){this.clearTimer(),this.$emit("end",{})},reset:function(){this.clearTimer(),this.seconds=Number(this.timestamp),this.s=this.timestamp,console.log(this.s)},clearTimer:function(){this.timer&&(clearInterval(this.timer),this.timer=null)}},beforeDestroy:function(){clearInterval(this.timer),this.timer=null}};e.default=i},"7e40":function(t,e,A){"use strict";A.r(e);var i=A("51bc"),n=A("d2d7");for(var a in n)["default"].indexOf(a)<0&&function(t){A.d(e,t,(function(){return n[t]}))}(a);A("5bff");var o=A("f0c5"),r=Object(o["a"])(n["default"],i["b"],i["c"],!1,null,"09aab398",null,!1,i["a"],void 0);e["default"]=r.exports},"7eb4":function(t,e,A){"use strict";var i=A("b97a"),n=A.n(i);n.a},"7f80":function(t,e,A){var i=A("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.u-search[data-v-3c66e606]{display:flex;flex-direction:row;align-items:center;flex:1;padding:%?15?% %?20?%}.u-content[data-v-3c66e606]{display:flex;flex-direction:row;align-items:center;padding:0 %?18?%;flex:1}.u-clear-icon[data-v-3c66e606]{display:flex;flex-direction:row;align-items:center}.u-input[data-v-3c66e606]{flex:1;font-size:%?28?%;line-height:1;margin:0 %?10?%;color:#909399}.u-close-wrap[data-v-3c66e606]{width:%?40?%;height:100%;display:flex;flex-direction:row;align-items:center;justify-content:center;border-radius:50%}.u-placeholder-class[data-v-3c66e606]{color:#909399}.u-action[data-v-3c66e606]{font-size:%?28?%;color:#303133;width:0;overflow:hidden;transition:all .3s;white-space:nowrap;text-align:center}.u-action-active[data-v-3c66e606]{width:%?80?%;margin-left:%?10?%}',""]),t.exports=e},"80bf":function(t,e,A){t.exports=A.p+"static/images/index_shop_bg.png"},8115:function(t,e,A){var i=A("cbdb");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=A("4f06").default;n("5fca232b",i,!0,{sourceMap:!1,shadowMode:!1})},"84a4":function(t,e,A){"use strict";A("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i={name:"active-area",props:{title:String,url:String,list:{type:Array,default:function(){return[]}},type:String},data:function(){return{}}};e.default=i},8670:function(t,e,A){"use strict";A.d(e,"b",(function(){return i})),A.d(e,"c",(function(){return n})),A.d(e,"a",(function(){}));var i=function(){var t=this,e=t.$createElement,A=t._self._c||e;return A("v-uni-view",{},[A("v-uni-view",{staticClass:"u-sticky-wrap",class:[t.elClass],style:{height:t.fixed?t.height+"px":"auto",backgroundColor:t.bgColor}},[A("v-uni-view",{staticClass:"u-sticky",style:{position:t.fixed?"fixed":"static",top:t.stickyTop+"px",left:t.left+"px",width:"auto"==t.width?"auto":t.width+"px",zIndex:t.uZIndex}},[t._t("default")],2)],1)],1)},n=[]},8762:function(t,e,A){var i=A("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.community-list--item[data-v-24fb8771]{border-radius:%?14?%}.community-list--item .community-img[data-v-24fb8771]{width:%?336?%;position:relative}.community-list--item .community-img .works[data-v-24fb8771]{width:100%;height:100%;z-index:10;border-radius:%?14?%;background-color:rgba(0,0,0,.4);position:absolute}.community-list--item .community-index[data-v-24fb8771]{width:%?240?%;position:relative}.community-list--item .community-index .wrap[data-v-24fb8771]{width:100%;height:100%;z-index:10;border-radius:%?14?%;background-color:rgba(0,0,0,.4);position:absolute;padding-top:%?140?%}.community-list--item .community-index .wrap .index-title[data-v-24fb8771]{width:%?210?%}.community-list--item .community-index .wrap .index-name[data-v-24fb8771]{width:%?160?%}.community-list--item .community-info[data-v-24fb8771]{padding:%?10?%}.community-list--item .community-info .community-title[data-v-24fb8771]{font-size:%?28?%;line-height:%?40?%;color:#333}.community-list--item .community-info .user-name[data-v-24fb8771]{color:#999;font-size:%?24?%;margin:0 %?10?%}.community-list--item .community-info .likes uni-image[data-v-24fb8771]{width:%?32?%;height:%?32?%;vertical-align:middle}',""]),t.exports=e},8783:function(t,e,A){"use strict";A.r(e);var i=A("d5c4"),n=A("607d");for(var a in n)["default"].indexOf(a)<0&&function(t){A.d(e,t,(function(){return n[t]}))}(a);A("1315");var o=A("f0c5"),r=Object(o["a"])(n["default"],i["b"],i["c"],!1,null,"24fb8771",null,!1,i["a"],void 0);e["default"]=r.exports},"8aef":function(t,e,A){"use strict";A.r(e);var i=A("e9b9"),n=A("e522");for(var a in n)["default"].indexOf(a)<0&&function(t){A.d(e,t,(function(){return n[t]}))}(a);A("cb75");var o=A("f0c5"),r=Object(o["a"])(n["default"],i["b"],i["c"],!1,null,"391f5b4a",null,!1,i["a"],void 0);e["default"]=r.exports},"8b7c":function(t,e,A){"use strict";var i=A("8115"),n=A.n(i);n.a},"8dad":function(t,e,A){"use strict";A.r(e);var i=A("3ff6"),n=A.n(i);for(var a in i)["default"].indexOf(a)<0&&function(t){A.d(e,t,(function(){return i[t]}))}(a);e["default"]=n.a},"8e20":function(t,e,A){"use strict";A.d(e,"b",(function(){return n})),A.d(e,"c",(function(){return a})),A.d(e,"a",(function(){return i}));var i={uImage:A("f919").default,uIcon:A("6976").default,goodsList:A("c574").default},n=function(){var t=this,e=t.$createElement,A=t._self._c||e;return A("v-uni-view",{staticClass:"shop-item",style:{"background-image":"url("+t.item.background+")"}},[A("v-uni-view",{staticClass:"shop-hd flex row-between m-l-20"},[A("v-uni-view",[A("v-uni-view",{staticClass:"flex"},[A("u-image",{attrs:{width:"68rpx",height:"68rpx","border-radius":"60rpx",src:t.item.logo}}),A("v-uni-view",{staticClass:"white md m-l-20 bold"},[t._v(t._s(t.item.name))]),1==t.item.type?A("v-uni-view",{staticClass:"store-tag xxs m-t-10 m-l-20 white"},[t._v("自营")]):t._e()],1),A("router-link",{attrs:{to:{path:"/pages/store_index/store_index",query:{id:t.item.id}}}},[A("v-uni-view",{staticClass:"enter-btn flex row-center bg-white xxs"},[t._v("进店看看 >")])],1)],1),A("v-uni-view",[A("v-uni-view",{staticClass:"shop-tips white flex row-center xxs"},[t._v(t._s(t.item.visited_num)+"人进店")]),t.item.distance?A("v-uni-view",{staticClass:"m-t-30 white flex row-center xs m-r-10"},[A("u-icon",{attrs:{name:"map",size:"30"}}),A("v-uni-text",{staticClass:"m-l-4"},[t._v("距离"+t._s(t.item.distance))])],1):t._e()],1)],1),A("v-uni-scroll-view",{staticStyle:{"white-space":"nowrap"},attrs:{"scroll-x":!0}},[A("v-uni-view",{staticClass:"goods"},[A("goods-list",{attrs:{type:"row",list:t.item.goods_list}})],1)],1)],1)},a=[]},"8fb5":function(t,e,A){"use strict";A("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,A("a9e3");var i={name:"u-lazy-load",props:{index:{type:[Number,String]},image:{type:String,default:""},imgMode:{type:String,default:"widthFix"},loadingImg:{type:String,default:"data:image/png;base64,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"},errorImg:{type:String,default:"data:image/png;base64,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"},threshold:{type:[Number,String],default:100},duration:{type:[Number,String],default:500},effect:{type:String,default:"ease-in-out"},isEffect:{type:Boolean,default:!0},borderRadius:{type:[Number,String],default:0},height:{type:[Number,String],default:"450"}},data:function(){return{isShow:!1,opacity:1,time:this.duration,loadStatus:"",isError:!1,elIndex:this.$u.guid()}},computed:{getThreshold:function(){var t=uni.upx2px(Math.abs(this.threshold));return this.threshold<0?-t:t},imgHeight:function(){return this.$u.addUnit(this.height)}},created:function(){this.observer={}},watch:{isShow:function(t){var e=this;this.isEffect&&(this.time=0,this.opacity=0,setTimeout((function(){e.time=e.duration,e.opacity=1}),30))},image:function(t){t?(this.init(),this.isError=!1):this.isError=!0}},methods:{init:function(){this.isError=!1,this.loadStatus=""},clickImg:function(){0==this.isShow||this.isError,this.$emit("click",this.index)},imgLoaded:function(){""==this.loadStatus?this.loadStatus="lazyed":"lazyed"==this.loadStatus&&(this.loadStatus="loaded",this.$emit("load",this.index))},errorImgLoaded:function(){this.$emit("error",this.index)},loadError:function(){this.isError=!0},disconnectObserver:function(t){var e=this[t];e&&e.disconnect()}},beforeDestroy:function(){},mounted:function(){var t=this;this.$nextTick((function(){uni.$once("uOnReachBottom",(function(){t.isShow||(t.isShow=!0)}))})),setTimeout((function(){t.disconnectObserver("contentObserver");var e=uni.createIntersectionObserver(t);e.relativeToViewport({bottom:t.getThreshold}).observe(".u-lazy-item-"+t.elIndex,(function(e){e.intersectionRatio>0&&(t.isShow=!0,t.disconnectObserver("contentObserver"))})),t.contentObserver=e}),30)}};e.default=i},9019:function(t,e,A){var i=A("c49e");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=A("4f06").default;n("8effa39a",i,!0,{sourceMap:!1,shadowMode:!1})},9196:function(t,e,A){var i=A("5bc8");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=A("4f06").default;n("51711bf4",i,!0,{sourceMap:!1,shadowMode:!1})},"921a":function(t,e,A){"use strict";A("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,A("c740");var i={name:"home-seckill",props:{list:{type:Array,default:function(){return[]}}},data:function(){return{active:-1,goodsList:[]}},methods:{exchangeTime:function(t){this.active=t,this.goodsList=this.list[t].goods},refresh:function(){uni.$emit("refreshhome")}},watch:{list:{handler:function(t){var e=t.findIndex((function(t){return 1==t.status}));-1==e&&(e=t.findIndex((function(t){return 0==t.status}))),-1==e&&(e=t.length-1),this.active=e,this.goodsList=t[e].goods},immediate:!0}},computed:{currentStatus:function(){var t=this.active,e=this.list;return e[t]&&e[t].status},currentTime:function(){var t=this.active,e=this.list;return e[t]?e[t].end_time_int-Date.now()/1e3:0}}};e.default=i},9325:function(t,e,A){"use strict";A.r(e);var i=A("8fb5"),n=A.n(i);for(var a in i)["default"].indexOf(a)<0&&function(t){A.d(e,t,(function(){return i[t]}))}(a);e["default"]=n.a},9337:function(t,e,A){"use strict";var i=A("125f"),n=A.n(i);n.a},9367:function(t,e,A){var i=A("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.cate-home[data-v-c49f2172]{border-radius:%?20?% %?20?% 0 0;overflow:hidden}.cate-home .contain[data-v-c49f2172]{padding:%?20?% %?30?% 0}.cate-home .title-img[data-v-c49f2172]{width:100%;height:%?120?%}',""]),t.exports=e},"95fd":function(t,e,A){"use strict";A.d(e,"b",(function(){return i})),A.d(e,"c",(function(){return n})),A.d(e,"a",(function(){}));var i=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{staticClass:"u-waterfall"},[e("v-uni-view",{staticClass:"u-column",attrs:{id:"u-left-column"}},[this._t("left",null,{leftList:this.leftList})],2),e("v-uni-view",{staticClass:"u-column",attrs:{id:"u-right-column"}},[this._t("right",null,{rightList:this.rightList})],2)],1)},n=[]},9644:function(t,e,A){"use strict";A.r(e);var i=A("6119"),n=A("9b6b");for(var a in n)["default"].indexOf(a)<0&&function(t){A.d(e,t,(function(){return n[t]}))}(a);A("9a4f");var o=A("f0c5"),r=Object(o["a"])(n["default"],i["b"],i["c"],!1,null,"94201f12",null,!1,i["a"],void 0);e["default"]=r.exports},"96cf":function(t,e,A){var i=A("b68d");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=A("4f06").default;n("56819ad8",i,!0,{sourceMap:!1,shadowMode:!1})},9911:function(t,e,A){"use strict";A("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i={onPageScroll:function(t){this.handlePageScroll(t)},onReachBottom:function(){this.handleReachBottom()},onPullDownRefresh:function(){this.handlePullDownRefresh()},data:function(){var t=this;return{mescroll:{onPageScroll:function(e){t.handlePageScroll(e)},onReachBottom:function(){t.handleReachBottom()},onPullDownRefresh:function(){t.handlePullDownRefresh()}}}},methods:{handlePageScroll:function(t){var e=this.$refs["mescrollItem"];e&&e.mescroll&&e.mescroll.onPageScroll(t)},handleReachBottom:function(){var t=this.$refs["mescrollItem"];t&&t.mescroll&&t.mescroll.onReachBottom()},handlePullDownRefresh:function(){var t=this.$refs["mescrollItem"];t&&t.mescroll&&t.mescroll.onPullDownRefresh()}}};e.default=i},"995f":function(t,e,A){var i=A("24fb"),n=A("1de5b"),a=A("43c1");e=i(!1);var o=n(a);e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */uni-page-body[data-v-09aab398]{padding:0}.index-bg[data-v-09aab398]{background-image:url('+o+');background-size:100% auto;background-repeat:no-repeat}.index[data-v-09aab398]{min-height:calc(100vh - var(--window-bottom))}.index .u-navbar[data-v-09aab398]  .u-search{padding:0 %?30?%}.index .capsule-tips[data-v-09aab398]{width:%?584?%;color:#fff;padding:%?12?% %?18?%;border-radius:%?14?%;background:rgba(0,0,0,.7);position:relative;position:absolute;z-index:9999;bottom:%?-80?%;right:%?-150?%}.index .capsule-tips[data-v-09aab398]::after{content:"";border-bottom:%?14?% solid rgba(0,0,0,.7);border-right:%?14?% solid transparent;border-left:%?14?% solid transparent;position:absolute;top:%?-14?%;right:%?88?%}.index .cate-btn[data-v-09aab398]{padding:%?12?% %?16?% %?12?% %?20?%;border-radius:%?60?% 0 0 %?60?%;background-color:hsla(0,0%,100%,.4)}.record_number[data-v-09aab398]{text-align:center;padding:%?30?%;font-size:%?24?%}',""]),t.exports=e},"9a4f":function(t,e,A){"use strict";var i=A("4b4a"),n=A.n(i);n.a},"9b6b":function(t,e,A){"use strict";A.r(e);var i=A("e531"),n=A.n(i);for(var a in i)["default"].indexOf(a)<0&&function(t){A.d(e,t,(function(){return i[t]}))}(a);e["default"]=n.a},"9b8d":function(t,e,A){"use strict";A.r(e);var i=A("dcc5"),n=A("9325");for(var a in n)["default"].indexOf(a)<0&&function(t){A.d(e,t,(function(){return n[t]}))}(a);A("efe4");var o=A("f0c5"),r=Object(o["a"])(n["default"],i["b"],i["c"],!1,null,"a968d7f2",null,!1,i["a"],void 0);e["default"]=r.exports},"9ba6":function(t,e,A){"use strict";A("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,A("a9e3");var i={props:{i:Number,index:{type:Number,default:function(){return 0}}},data:function(){return{downOption:{auto:!1},upOption:{auto:!1},isInit:!1}},watch:{index:function(t){this.i!==t||this.isInit||(this.isInit=!0,this.mescroll&&this.mescroll.triggerDownScroll())}},methods:{mescrollInitByRef:function(){if(!this.mescroll||!this.mescroll.resetUpScroll){var t=this.$refs.mescrollRef||this.$refs["mescrollRef"+this.i];t&&(this.mescroll=t.mescroll)}},mescrollInit:function(t){this.mescroll=t,this.mescrollInitByRef&&this.mescrollInitByRef(),this.i===this.index&&(this.isInit=!0,this.mescroll.triggerDownScroll())}}},n=i;e.default=n},"9d0f":function(t,e,A){"use strict";var i=A("7655"),n=A.n(i);n.a},"9da6":function(t,e,A){"use strict";A.r(e);var i=A("a2a9"),n=A.n(i);for(var a in i)["default"].indexOf(a)<0&&function(t){A.d(e,t,(function(){return i[t]}))}(a);e["default"]=n.a},"9dbd":function(t,e,A){"use strict";A("7a82");var i=A("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=i(A("f07e")),a=i(A("c964"));A("a9e3"),A("14d9");var o=A("b550"),r=(A("a5ae"),{data:function(){return{lists:[],currentSwiper:0}},props:{pid:{type:Number},circular:{type:Boolean,default:!0},autoplay:{type:Boolean,default:!0},height:{type:String},radius:{type:String,default:"0"},padding:{type:String,default:"0rpx"},previousMargin:{type:String,default:"0rpx"},isSwiper:{type:Boolean,default:!0}},created:function(){this.getAdListFun()},watch:{pid:function(t){this.getAdListFun()}},methods:{getAdListFun:function(){var t=this;return(0,a.default)((0,n.default)().mark((function e(){var A,i,a;return(0,n.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,(0,o.getAdList)({pid:t.pid,terminal:1});case 2:A=e.sent,i=A.code,a=A.data,1==i&&(t.lists=a);case 6:case"end":return e.stop()}}),e)})))()},swiperChange:function(t){this.currentSwiper=t.detail.current},goPage:function(t){var e=t.link,A=t.link_type,i=t.params,n=t.is_tab;switch(A){case 1:case 2:n?this.$Router.pushTab({path:e}):this.$Router.push({path:e,query:i});break;case 3:this.$Router.push({path:"/pages/webview/webview",query:{url:e}});break}}}});e.default=r},"9e3f":function(t,e,A){"use strict";A("7a82");var i=A("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.getLiveLists=function(t){return n.default.get("live/lists",{params:t})},e.getShopLive=function(t){return n.default.get("live/shopLive",{params:t})};var n=i(A("2774"))},"9ee2":function(t,e,A){var i=A("f654");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=A("4f06").default;n("52ed2832",i,!0,{sourceMap:!1,shadowMode:!1})},a2a9:function(t,e,A){"use strict";A("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,A("a9e3");var i={name:"u-tag",props:{type:{type:String,default:"primary"},disabled:{type:[Boolean,String],default:!1},size:{type:String,default:"default"},shape:{type:String,default:"square"},text:{type:[String,Number],default:""},bgColor:{type:String,default:""},color:{type:String,default:""},borderColor:{type:String,default:""},closeColor:{type:String,default:""},index:{type:[Number,String],default:""},mode:{type:String,default:"light"},closeable:{type:Boolean,default:!1},show:{type:Boolean,default:!0}},data:function(){return{}},computed:{customStyle:function(){var t={};return this.color&&(t.color=this.color),this.bgColor&&(t.backgroundColor=this.bgColor),"plain"==this.mode&&this.color&&!this.borderColor?t.borderColor=this.color:t.borderColor=this.borderColor,t},iconStyle:function(){if(this.closeable){var t={};return"mini"==this.size?t.fontSize="20rpx":t.fontSize="22rpx","plain"==this.mode||"light"==this.mode?t.color=this.type:"dark"==this.mode&&(t.color="#ffffff"),this.closeColor&&(t.color=this.closeColor),t}},closeIconColor:function(){return this.closeColor?this.closeColor:this.color?this.color:"dark"==this.mode?"#ffffff":this.type}},methods:{clickTag:function(){this.disabled||this.$emit("click",this.index)},close:function(){this.$emit("close",this.index)}}};e.default=i},a4e9:function(t,e,A){var i=A("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.u-tag[data-v-1cd62f78]{box-sizing:border-box;align-items:center;border-radius:%?6?%;display:inline-block}.u-size-default[data-v-1cd62f78]{font-size:%?22?%;padding:%?6?% %?12?%}.u-size-mini[data-v-1cd62f78]{font-size:%?20?%;padding:%?1?% %?6?%}.u-mode-light-primary[data-v-1cd62f78]{background-color:#ecf5ff;color:#ff2c3c;border:1px solid #a0cfff}.u-mode-light-success[data-v-1cd62f78]{background-color:#dbf1e1;color:#19be6b;border:1px solid #71d5a1}.u-mode-light-error[data-v-1cd62f78]{background-color:#fef0f0;color:#fa3534;border:1px solid #fab6b6}.u-mode-light-warning[data-v-1cd62f78]{background-color:#fdf6ec;color:#f90;border:1px solid #fcbd71}.u-mode-light-info[data-v-1cd62f78]{background-color:#f4f4f5;color:#909399;border:1px solid #c8c9cc}.u-mode-dark-primary[data-v-1cd62f78]{background-color:#ff2c3c;color:#fff}.u-mode-dark-success[data-v-1cd62f78]{background-color:#19be6b;color:#fff}.u-mode-dark-error[data-v-1cd62f78]{background-color:#fa3534;color:#fff}.u-mode-dark-warning[data-v-1cd62f78]{background-color:#f90;color:#fff}.u-mode-dark-info[data-v-1cd62f78]{background-color:#909399;color:#fff}.u-mode-plain-primary[data-v-1cd62f78]{background-color:#fff;color:#ff2c3c;border:1px solid #ff2c3c}.u-mode-plain-success[data-v-1cd62f78]{background-color:#fff;color:#19be6b;border:1px solid #19be6b}.u-mode-plain-error[data-v-1cd62f78]{background-color:#fff;color:#fa3534;border:1px solid #fa3534}.u-mode-plain-warning[data-v-1cd62f78]{background-color:#fff;color:#f90;border:1px solid #f90}.u-mode-plain-info[data-v-1cd62f78]{background-color:#fff;color:#909399;border:1px solid #909399}.u-disabled[data-v-1cd62f78]{opacity:.55}.u-shape-circle[data-v-1cd62f78]{border-radius:%?100?%}.u-shape-circleRight[data-v-1cd62f78]{border-radius:0 %?100?% %?100?% 0}.u-shape-circleLeft[data-v-1cd62f78]{border-radius:%?100?% 0 0 %?100?%}.u-close-icon[data-v-1cd62f78]{margin-left:%?14?%;font-size:%?22?%;color:#19be6b}.u-icon-wrap[data-v-1cd62f78]{display:inline-flex;-webkit-transform:scale(.86);transform:scale(.86)}',""]),t.exports=e},a664:function(t,e,A){"use strict";A.r(e);var i=A("6737"),n=A.n(i);for(var a in i)["default"].indexOf(a)<0&&function(t){A.d(e,t,(function(){return i[t]}))}(a);e["default"]=n.a},a6c8:function(t,e,A){"use strict";A.r(e);var i=A("79ec"),n=A("cdf2");for(var a in n)["default"].indexOf(a)<0&&function(t){A.d(e,t,(function(){return n[t]}))}(a);var o=A("f0c5"),r=Object(o["a"])(n["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);e["default"]=r.exports},a88a:function(t,e){t.exports="data:image/png;base64,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"},aac5:function(t,e,A){"use strict";A("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i={name:"community-recommend",props:{title:String,url:String,list:{type:Array,default:function(){return[]}}}};e.default=i},ae3a:function(t,e,A){"use strict";A.r(e);var i=A("05da"),n=A("b27c");for(var a in n)["default"].indexOf(a)<0&&function(t){A.d(e,t,(function(){return n[t]}))}(a);A("cba6");var o=A("f0c5"),r=Object(o["a"])(n["default"],i["b"],i["c"],!1,null,"38cbd707",null,!1,i["a"],void 0);e["default"]=r.exports},ae8b:function(t,e,A){var i=A("1b43");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=A("4f06").default;n("694b2a30",i,!0,{sourceMap:!1,shadowMode:!1})},b06f:function(t,e,A){"use strict";A("7a82");var i=A("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=i(A("f3f3")),a=A("a5ae"),o=A("26cb"),r={props:{navList:{type:Array,default:function(){return[]}},list:{type:Array,default:function(){return[]}},homeData:{type:Object,default:function(){return{}}},liveList:{type:Array,default:function(){return[]}}},data:function(){return{newNavList:[],navSwiperH:"",currentSwiper:0,isDischarge:!1,goodsList:[]}},mounted:function(){this.isDischarge=!1},destroyed:function(){this.isDischarge=!0},methods:{swiperChange:function(t){console.log(t),this.currentSwiper=t.detail.current},menuJump:function(t){(0,a.menuJump)(t)}},watch:{navList:function(t){t.length<=5?this.navSwiperH=200:this.navSwiperH=374,this.newNavList=(0,a.arraySlice)(t)},list:function(t){this.goodsList=t}},computed:(0,n.default)((0,n.default)({},(0,o.mapGetters)(["appConfig"])),{},{newsList:function(){return this.homeData.headlines||[]},nearbyShop:function(){return this.homeData.nearby_shops||[]},hotGoods:function(){return this.homeData.hots||[]},newGoods:function(){return this.homeData.news||[]},communityArticle:function(){return this.homeData.community_article||[]},activityList:function(){return this.homeData.activity_area||[]},shopRecommend:function(){return this.homeData.shop_recommend||[]},shopLists:function(){return this.homeData.shop_lists||[]},seckillGoods:function(){return this.homeData.seckill_goods||[]},activityArea:function(){return this.homeData.activity_area||[]}})};e.default=r},b27c:function(t,e,A){"use strict";A.r(e);var i=A("7bfe"),n=A.n(i);for(var a in i)["default"].indexOf(a)<0&&function(t){A.d(e,t,(function(){return i[t]}))}(a);e["default"]=n.a},b413:function(t,e,A){"use strict";A.d(e,"b",(function(){return n})),A.d(e,"c",(function(){return a})),A.d(e,"a",(function(){return i}));var i={uIcon:A("6976").default},n=function(){var t=this,e=t.$createElement,A=t._self._c||e;return A("v-uni-view",{staticClass:"u-search",style:{margin:t.margin,backgroundColor:t.wrapBgColor},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.clickHandler.apply(void 0,arguments)}}},[A("v-uni-view",{staticClass:"u-content",style:{backgroundColor:t.bgColor,borderRadius:"round"==t.shape?"100rpx":"10rpx",border:t.borderStyle,height:t.height+"rpx"}},[A("v-uni-view",{staticClass:"u-icon-wrap"},[A("u-icon",{staticClass:"u-clear-icon",attrs:{size:30,name:t.searchIcon,color:t.searchIconColor?t.searchIconColor:t.color}})],1),A("v-uni-input",{staticClass:"u-input",style:[{textAlign:t.inputAlign,color:t.color,backgroundColor:t.bgColor},t.inputStyle],attrs:{"confirm-type":"search",value:t.value,disabled:t.disabled,focus:t.focus,maxlength:t.maxlength,"placeholder-class":"u-placeholder-class",placeholder:t.placeholder,"placeholder-style":"color: "+t.placeholderColor,type:"text"},on:{blur:function(e){arguments[0]=e=t.$handleEvent(e),t.blur.apply(void 0,arguments)},confirm:function(e){arguments[0]=e=t.$handleEvent(e),t.search.apply(void 0,arguments)},input:function(e){arguments[0]=e=t.$handleEvent(e),t.inputChange.apply(void 0,arguments)},focus:function(e){arguments[0]=e=t.$handleEvent(e),t.getFocus.apply(void 0,arguments)}}}),t.keyword&&t.clearabled&&t.focused?A("v-uni-view",{staticClass:"u-close-wrap",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.clear.apply(void 0,arguments)}}},[A("u-icon",{staticClass:"u-clear-icon",attrs:{name:"close-circle-fill",size:"34",color:"#c0c4cc"}})],1):t._e()],1),t.hideRight?A("v-uni-view",{staticClass:"u-action",class:[t.showActionBtn||t.show?"u-action-active":""],style:[t.actionStyle],on:{click:function(e){e.stopPropagation(),e.preventDefault(),arguments[0]=e=t.$handleEvent(e),t.custom.apply(void 0,arguments)}}},[t._v(t._s(t.actionText))]):t._e()],1)},a=[]},b572:function(t,e,A){"use strict";A.r(e);var i=A("5458"),n=A("6bbc");for(var a in n)["default"].indexOf(a)<0&&function(t){A.d(e,t,(function(){return n[t]}))}(a);A("f47e");var o=A("f0c5"),r=Object(o["a"])(n["default"],i["b"],i["c"],!1,null,"c49f2172",null,!1,i["a"],void 0);e["default"]=r.exports},b60a:function(t,e,A){var i=A("24fb"),n=A("1de5b"),a=A("0533"),o=A("80bf");e=i(!1);var r=n(a),s=n(o);e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.nearby-shops[data-v-391f5b4a]{background-image:url('+r+")}.shop-recommends[data-v-391f5b4a]{background:url("+s+") no-repeat}.shop-recommend[data-v-391f5b4a]{background-size:cover;background-repeat:no-repeat}.shop-recommend .shop-list .item-link[data-v-391f5b4a]{width:33.3%}.shop-recommend .shop-list .shop-item[data-v-391f5b4a]{width:%?220?%;border-radius:%?16?%;margin-bottom:%?16?%;overflow:hidden}.shop-recommend .shop-list .shop-item .text[data-v-391f5b4a]{padding:%?8?% %?8?% %?19?%;text-align:center}.shop-recommend .shop-list .shop-item .text .name[data-v-391f5b4a]{width:%?200?%}.shop-recommend .shop-list .shop-item .text .sale[data-v-391f5b4a]{background-color:#f4f4f4;padding:%?4?% %?16?%;display:inline-block}.shop-recommend .dots .dot[data-v-391f5b4a]{width:%?12?%;height:%?12?%;border-radius:50%;margin-right:%?10?%;background-color:#e5e5e5}.shop-recommend .dots .dot.active[data-v-391f5b4a]{width:%?12?%;background-color:#ff2c3c}",""]),t.exports=e},b68d:function(t,e,A){var i=A("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.index-home .content[data-v-6720fe7d]{padding:0 %?30?% %?20?%}.index-home .content .nav[data-v-6720fe7d]{position:relative;border-radius:%?20?%}.index-home .content .nav .nav-item[data-v-6720fe7d]{width:20%}.index-home .content .nav .nav-item .nav-icon[data-v-6720fe7d]{width:%?82?%;height:%?82?%}.index-home .content .nav .dots[data-v-6720fe7d]{position:absolute;left:50%;-webkit-transform:translateX(-50%);transform:translateX(-50%);bottom:%?20?%;display:flex}.index-home .content .nav .dots .dot[data-v-6720fe7d]{width:%?10?%;height:%?6?%;border-radius:%?6?%;margin-right:%?10?%;background-color:rgba(255,44,60,.4)}.index-home .content .nav .dots .dot.active[data-v-6720fe7d]{width:%?20?%;background-color:#ff2c3c}.index-home .content .information[data-v-6720fe7d]{height:%?76?%;box-shadow:0 0 10px rgba(0,0,0,.06);padding:0 %?20?%;border-radius:%?20?%}.index-home .content .information .news[data-v-6720fe7d]{position:relative}.index-home .content .information .news .shade[data-v-6720fe7d]{position:absolute;width:100%;height:100%;z-index:100}.index-home .content .information .icon-toutiao[data-v-6720fe7d]{width:%?114?%;height:%?34?%}.index-home .content .information .gap-line[data-v-6720fe7d]{height:%?28?%;width:1px;background-color:#dcdddc;margin:0 %?30?%}.index-home .content .title-iamge[data-v-6720fe7d]{width:%?200?%;height:%?100?%}.index-home .content .activity-zone[data-v-6720fe7d]{background-image:url(data:image/png;base64,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);background-size:100% auto;padding:0 %?15?%;border-radius:%?14?%}.index-home .content .activity-zone .item[data-v-6720fe7d]{border-radius:%?14?%;width:%?324?%;padding:%?20?% %?16?%}.index-home .content .activity-zone .item .desc[data-v-6720fe7d]{width:%?160?%}.index-home .content .activity-zone .item .btn[data-v-6720fe7d]{display:inline-block;padding:%?2?% %?20?%}.index-home .content .live[data-v-6720fe7d]{background:linear-gradient(180deg,#ffe9e9,#fff 74%);padding-bottom:%?28?%;border-radius:%?14?%}.index-home .content .live .live-header[data-v-6720fe7d]{padding:%?28?% %?24?%}',""]),t.exports=e},b871:function(t,e,A){"use strict";A.r(e);var i=A("ba65"),n=A.n(i);for(var a in i)["default"].indexOf(a)<0&&function(t){A.d(e,t,(function(){return i[t]}))}(a);e["default"]=n.a},b8a4:function(t,e,A){var i=A("9367");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=A("4f06").default;n("5102a0ac",i,!0,{sourceMap:!1,shadowMode:!1})},b97a:function(t,e,A){var i=A("a4e9");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=A("4f06").default;n("42aaaaa7",i,!0,{sourceMap:!1,shadowMode:!1})},ba65:function(t,e,A){"use strict";A("7a82");var i=A("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=i(A("f07e")),a=i(A("c964"));A("a9e3"),A("99af"),A("fb6a"),A("14d9"),A("a434"),A("e9c4"),A("c740");var o={name:"u-waterfall",props:{value:{type:Array,required:!0,default:function(){return[]}},addTime:{type:[Number,String],default:200},idKey:{type:String,default:"id"}},data:function(){return{leftList:[],rightList:[],tempList:[],children:[]}},watch:{copyFlowList:function(t,e){var A=Array.isArray(e)&&e.length>0?e.length:0;this.tempList=this.tempList.concat(this.cloneData(t.slice(A))),this.splitData()}},mounted:function(){this.tempList=this.cloneData(this.copyFlowList),this.splitData()},computed:{copyFlowList:function(){return this.cloneData(this.value)}},methods:{splitData:function(){var t=this;return(0,a.default)((0,n.default)().mark((function e(){var A,i,a;return(0,n.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t.tempList.length){e.next=2;break}return e.abrupt("return");case 2:return e.next=4,t.$uGetRect("#u-left-column");case 4:return A=e.sent,e.next=7,t.$uGetRect("#u-right-column");case 7:if(i=e.sent,a=t.tempList[0],a){e.next=11;break}return e.abrupt("return");case 11:A.height<i.height?t.leftList.push(a):A.height>i.height?t.rightList.push(a):t.leftList.length<=t.rightList.length?t.leftList.push(a):t.rightList.push(a),t.tempList.splice(0,1),t.tempList.length&&setTimeout((function(){t.splitData()}),t.addTime);case 14:case"end":return e.stop()}}),e)})))()},cloneData:function(t){return JSON.parse(JSON.stringify(t))},clear:function(){this.leftList=[],this.rightList=[],this.$emit("input",[]),this.tempList=[]},remove:function(t){var e=this,A=-1;A=this.leftList.findIndex((function(A){return A[e.idKey]==t})),-1!=A?this.leftList.splice(A,1):(A=this.rightList.findIndex((function(A){return A[e.idKey]==t})),-1!=A&&this.rightList.splice(A,1)),A=this.value.findIndex((function(A){return A[e.idKey]==t})),-1!=A&&this.$emit("input",this.value.splice(A,1))},modify:function(t,e,A){var i=this,n=-1;if(n=this.leftList.findIndex((function(e){return e[i.idKey]==t})),-1!=n?this.leftList[n][e]=A:(n=this.rightList.findIndex((function(e){return e[i.idKey]==t})),-1!=n&&(this.rightList[n][e]=A)),n=this.value.findIndex((function(e){return e[i.idKey]==t})),-1!=n){var a=this.cloneData(this.value);a[n][e]=A,this.$emit("input",a)}}}};e.default=o},bb4f:function(t,e,A){"use strict";A.r(e);var i=A("95fd"),n=A("b871");for(var a in n)["default"].indexOf(a)<0&&function(t){A.d(e,t,(function(){return n[t]}))}(a);A("9337");var o=A("f0c5"),r=Object(o["a"])(n["default"],i["b"],i["c"],!1,null,"7664bcb0",null,!1,i["a"],void 0);e["default"]=r.exports},bd8c:function(t,e,A){"use strict";A.r(e);var i=A("e4b1"),n=A.n(i);for(var a in i)["default"].indexOf(a)<0&&function(t){A.d(e,t,(function(){return i[t]}))}(a);e["default"]=n.a},bdc6:function(t,e,A){var i=A("f86e");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=A("4f06").default;n("ed7fa9e8",i,!0,{sourceMap:!1,shadowMode:!1})},be2e:function(t,e,A){var i=A("8762");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=A("4f06").default;n("169a6294",i,!0,{sourceMap:!1,shadowMode:!1})},bedf:function(t,e,A){var i=A("2bd8");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=A("4f06").default;n("42e30c8d",i,!0,{sourceMap:!1,shadowMode:!1})},bf73:function(t,e,A){"use strict";A.r(e);var i=A("f1ad"),n=A("715c");for(var a in n)["default"].indexOf(a)<0&&function(t){A.d(e,t,(function(){return n[t]}))}(a);A("1de5");var o=A("f0c5"),r=Object(o["a"])(n["default"],i["b"],i["c"],!1,null,"154ee532",null,!1,i["a"],void 0);e["default"]=r.exports},c038:function(t,e,A){"use strict";A.d(e,"b",(function(){return n})),A.d(e,"c",(function(){return a})),A.d(e,"a",(function(){return i}));var i={uImage:A("f919").default},n=function(){var t=this,e=t.$createElement,A=t._self._c||e;return t.navList.length?A("v-uni-view",{staticClass:"p-t-20"},[A("v-uni-view",{staticClass:"cate-nav bg-white"},[A("v-uni-swiper",{style:"height:"+t.navSwiperH+"rpx;",on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.swiperChange.apply(void 0,arguments)}}},t._l(t.navList,(function(e,i){return A("v-uni-swiper-item",{key:i},[A("v-uni-view",{staticClass:"nav-list flex flex-wrap"},t._l(e,(function(e,i){return A("router-link",{key:i,staticClass:"nav-item m-t-30",attrs:{to:{path:"/pages/goods_search/goods_search",query:{id:e.id,name:e.name,type:1}}}},[A("v-uni-view",{staticClass:"flex-col col-center"},[A("u-image",{attrs:{width:"82rpx",height:"82rpx",src:e.image,"border-radius":"50%"}}),A("v-uni-view",{staticClass:"m-t-14 xs line-1 text-center",staticStyle:{width:"90%"}},[t._v(t._s(e.name))])],1)],1)})),1)],1)})),1),t.navList.length>1?A("v-uni-view",{staticClass:"dots"},t._l(t.navList,(function(e,i){return A("v-uni-view",{key:i,class:"dot "+(i==t.currentSwiper?"active":"")})})),1):t._e()],1)],1):t._e()},a=[]},c33d:function(t,e,A){"use strict";A.r(e);var i=A("fb41"),n=A.n(i);for(var a in i)["default"].indexOf(a)<0&&function(t){A.d(e,t,(function(){return i[t]}))}(a);e["default"]=n.a},c383:function(t,e,A){"use strict";A.r(e);var i=A("ffc0"),n=A("2b94");for(var a in n)["default"].indexOf(a)<0&&function(t){A.d(e,t,(function(){return n[t]}))}(a);A("edf6");var o=A("f0c5"),r=Object(o["a"])(n["default"],i["b"],i["c"],!1,null,"6720fe7d",null,!1,i["a"],void 0);e["default"]=r.exports},c49e:function(t,e,A){var i=A("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.shop-item[data-v-22e17908]{padding-top:%?30?%;background-repeat:no-repeat;background-size:cover;background-position:50%;border-radius:%?20?%;background-color:#fff}.shop-item .shop-hd .store-tag[data-v-22e17908]{background:linear-gradient(267deg,#ff2c3c,#f52e99);border-radius:%?6?%;padding:%?4?% %?9?%}.shop-item .shop-hd .enter-btn[data-v-22e17908]{height:%?48?%;width:%?148?%;margin-top:%?30?%;border-radius:%?50?%}.shop-item .shop-hd .shop-tips[data-v-22e17908]{background-color:rgba(0,0,0,.6);border-radius:%?50?% %?0?% %?0?% %?50?%;height:%?50?%;min-width:%?152?%;padding:0 %?10?%}.shop-item .goods[data-v-22e17908]{padding:%?20?%;display:inline-block}',""]),t.exports=e},c88b:function(t,e,A){"use strict";A.r(e);var i=A("6c53"),n=A("2639");for(var a in n)["default"].indexOf(a)<0&&function(t){A.d(e,t,(function(){return n[t]}))}(a);A("fbae");var o=A("f0c5"),r=Object(o["a"])(n["default"],i["b"],i["c"],!1,null,"19271174",null,!1,i["a"],void 0);e["default"]=r.exports},c943:function(t,e,A){"use strict";A.r(e);var i=A("422e"),n=A("c33d");for(var a in n)["default"].indexOf(a)<0&&function(t){A.d(e,t,(function(){return n[t]}))}(a);A("0b17");var o=A("f0c5"),r=Object(o["a"])(n["default"],i["b"],i["c"],!1,null,"67ba699e",null,!1,i["a"],void 0);e["default"]=r.exports},cb75:function(t,e,A){"use strict";var i=A("f479"),n=A.n(i);n.a},cba6:function(t,e,A){"use strict";var i=A("2e13"),n=A.n(i);n.a},cbdb:function(t,e,A){var i=A("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.cate-nav[data-v-8168704c]{position:relative;border-radius:%?20?%}.cate-nav .nav-item[data-v-8168704c]{width:20%}.cate-nav .dots[data-v-8168704c]{position:absolute;left:50%;-webkit-transform:translateX(-50%);transform:translateX(-50%);bottom:%?20?%;display:flex}.cate-nav .dots .dot[data-v-8168704c]{width:%?10?%;height:%?6?%;border-radius:%?6?%;margin-right:%?10?%;background-color:rgba(255,44,60,.4)}.cate-nav .dots .dot.active[data-v-8168704c]{width:%?20?%;background-color:#ff2c3c}',""]),t.exports=e},cdf2:function(t,e,A){"use strict";A.r(e);var i=A("0523"),n=A.n(i);for(var a in i)["default"].indexOf(a)<0&&function(t){A.d(e,t,(function(){return i[t]}))}(a);e["default"]=n.a},ce5f:function(t,e,A){"use strict";A.r(e);var i=A("2251"),n=A.n(i);for(var a in i)["default"].indexOf(a)<0&&function(t){A.d(e,t,(function(){return i[t]}))}(a);e["default"]=n.a},cfb4:function(t,e,A){var i=A("6eea");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=A("4f06").default;n("bd416cd4",i,!0,{sourceMap:!1,shadowMode:!1})},d058:function(t,e,A){"use strict";A.r(e);var i=A("d930"),n=A("6326");for(var a in n)["default"].indexOf(a)<0&&function(t){A.d(e,t,(function(){return n[t]}))}(a);A("9d0f");var o=A("f0c5"),r=Object(o["a"])(n["default"],i["b"],i["c"],!1,null,"5a2d1aa7",null,!1,i["a"],void 0);e["default"]=r.exports},d2d7:function(t,e,A){"use strict";A.r(e);var i=A("4f2b"),n=A.n(i);for(var a in i)["default"].indexOf(a)<0&&function(t){A.d(e,t,(function(){return i[t]}))}(a);e["default"]=n.a},d5c4:function(t,e,A){"use strict";A.d(e,"b",(function(){return n})),A.d(e,"c",(function(){return a})),A.d(e,"a",(function(){return i}));var i={uLazyLoad:A("9b8d").default,uImage:A("f919").default},n=function(){var t=this,e=t.$createElement,A=t._self._c||e;return A("v-uni-view",{},["waterfall"==t.type?A("v-uni-view",{staticClass:"community-list"},t._l(t.list,(function(e,i){return A("router-link",{key:i,attrs:{to:"/bundle_b/pages/community_detail/community_detail?id="+e.id}},[A("v-uni-view",{staticClass:"community-list--item bg-white m-t-20"},[A("v-uni-view",{staticClass:"community-img"},[A("u-lazy-load",{attrs:{threshold:"0","border-radius":"10",image:e.image,index:i}})],1),A("v-uni-view",{staticClass:"community-info"},[A("v-uni-view",{staticClass:"community-title line-2"},[t._v(t._s(e.content))]),A("v-uni-view",{staticClass:"m-t-10 flex"},[A("u-image",{attrs:{width:"50",height:"50","border-radius":"50%",src:e.avatar}}),A("v-uni-view",{staticClass:"user-name flex-1 line-2"},[t._v(t._s(e.nickname))]),A("v-uni-view",{staticClass:"likes"},[A("v-uni-image",{attrs:{src:e.is_like?"/static/images/icon_collection_s.png":"/static/images/icon_likes.png"}}),A("v-uni-text",{staticClass:"xs muted m-l-6"},[t._v(t._s(e.like))])],1)],1)],1)],1)],1)})),1):t._e(),"works"==t.type?A("v-uni-view",{staticClass:"community-list"},t._l(t.list,(function(e,i){return A("router-link",{key:i,attrs:{to:"/bundle_b/pages/community_detail/community_detail?id="+e.id}},[A("v-uni-view",{staticClass:"community-list--item bg-white m-t-20"},[A("v-uni-view",{staticClass:"community-img"},[0===e.status||2===e.status?A("v-uni-view",{staticClass:"works flex row-center "},[A("v-uni-view",{staticClass:"text-center nr white"},[A("v-uni-view",[t._v(t._s(e.status_desc))]),A("v-uni-view",{staticClass:"m-t-10"},[t._v(t._s(e.audit_remark_desc))])],1)],1):t._e(),A("u-lazy-load",{attrs:{threshold:"0","border-radius":"10",image:e.image,index:i}})],1),A("v-uni-view",{staticClass:"community-info"},[A("v-uni-view",{staticClass:"community-title line-2"},[t._v(t._s(e.content))]),A("v-uni-view",{staticClass:"m-t-20 flex"},[A("v-uni-view",{staticClass:"user-name flex-1 line-2"},[t._v(t._s(e.create_time))]),A("v-uni-view",{staticClass:"likes"},[A("v-uni-image",{attrs:{src:e.is_like?"/static/images/icon_collection_s.png":"/static/images/icon_likes.png"}}),A("v-uni-text",{staticClass:"xs muted m-l-6"},[t._v(t._s(e.like))])],1)],1)],1)],1)],1)})),1):t._e(),"index"==t.type?A("v-uni-view",{staticClass:"community-list flex"},t._l(t.list,(function(e,i){return A("router-link",{key:i,staticClass:"community-list--item bg-white m-r-20",attrs:{to:"/bundle_b/pages/community_detail/community_detail?id="+e.id}},[A("v-uni-view",{staticClass:"community-index"},[A("v-uni-view",{staticClass:"wrap white sm p-l-10"},[A("v-uni-view",{staticClass:"index-title line-1"},[t._v(t._s(e.content))]),A("v-uni-view",{staticClass:"flex m-t-10"},[A("u-lazy-load",{attrs:{threshold:"0","border-radius":"10",image:e.image,index:i}}),A("v-uni-view",{staticClass:"index-name line-1 m-l-6"},[t._v(t._s(e.nickname))])],1)],1),A("u-image",{attrs:{width:"240",height:"240",src:e.image,borderRadius:"14"}})],1)],1)})),1):t._e()],1)},a=[]},d653:function(t,e,A){"use strict";A("7a82");var i=A("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=i(A("f07e")),a=i(A("c964"));A("a9e3");var o={name:"u-no-network",props:{tips:{type:String,default:"哎呀，网络信号丢失"},zIndex:{type:[Number,String],default:"1200"},image:{type:String,default:"data:image/png;base64,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"}},data:function(){return{isConnected:!0,networkType:"none"}},computed:{uZIndex:function(){return this.zIndex?this.zIndex:this.$u.zIndex.noNetwork}},mounted:function(){var t=this;this.isIOS="ios"===uni.getSystemInfoSync().platform,uni.onNetworkStatusChange((function(e){t.isConnected=e.isConnected,t.isConnected&&t.refresh(),t.networkType=e.networkType})),uni.getNetworkType({success:function(e){t.networkType=e.networkType,"none"==e.networkType?t.isConnected=!1:t.isConnected=!0}})},methods:{refresh:function(){this.$emit("retry"),location.reload()},retry:function(){var t=this;uni.getNetworkType({success:function(e){t.networkType=e.networkType,"none"==e.networkType?(uni.showToast({title:"无网络连接",icon:"none",position:"top"}),t.isConnected=!1):(uni.showToast({title:"网络已连接",icon:"none",position:"top"}),t.isConnected=!0)}})},openSettings:function(){var t=this;return(0,a.default)((0,n.default)().mark((function e(){return(0,n.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if("none"!=t.networkType){e.next=3;break}return t.openSystemSettings(),e.abrupt("return");case 3:case"end":return e.stop()}}),e)})))()},openAppSettings:function(){this.gotoAppSetting()},openSystemSettings:function(){this.isIOS?this.gotoiOSSetting():this.gotoAndroidSetting()},network:function(){var t=null,e=plus.ios.newObject("CTCellularData"),A=e.plusGetAttribute("restrictedState");return 0==A?t=null:2==A?t=1:1==A&&(t=2),plus.ios.deleteObject(e),t},gotoAppSetting:function(){if(this.isIOS){var t=plus.ios.import("UIApplication"),e=t.sharedApplication(),A=plus.ios.import("NSURL"),i=A.URLWithString("app-settings:");e.openURL(i),plus.ios.deleteObject(i),plus.ios.deleteObject(A),plus.ios.deleteObject(e)}else{var n=plus.android.importClass("android.content.Intent"),a=plus.android.importClass("android.provider.Settings"),o=plus.android.importClass("android.net.Uri"),r=plus.android.runtimeMainActivity(),s=new n;s.setAction(a.ACTION_APPLICATION_DETAILS_SETTINGS);var u=o.fromParts("package",r.getPackageName(),null);s.setData(u),r.startActivity(s)}},gotoiOSSetting:function(){var t=plus.ios.import("UIApplication"),e=t.sharedApplication(),A=plus.ios.import("NSURL"),i=A.URLWithString("App-prefs:root=General");e.openURL(i),plus.ios.deleteObject(i),plus.ios.deleteObject(A),plus.ios.deleteObject(e)},gotoAndroidSetting:function(){var t=plus.android.importClass("android.content.Intent"),e=plus.android.importClass("android.provider.Settings"),A=plus.android.runtimeMainActivity(),i=new t(e.ACTION_SETTINGS);A.startActivity(i)}}};e.default=o},d930:function(t,e,A){"use strict";A.d(e,"b",(function(){return n})),A.d(e,"c",(function(){return a})),A.d(e,"a",(function(){return i}));var i={uImage:A("f919").default},n=function(){var t=this,e=t.$createElement,A=t._self._c||e;return t.lists.length?A("v-uni-view",{staticClass:"swiper-wrap",style:{height:t.height,padding:t.padding}},[A("v-uni-view",{staticClass:"swiper-con",style:{borderRadius:t.radius}},[t.isSwiper?[A("v-uni-swiper",{staticClass:"swiper",attrs:{autoplay:t.autoplay,circular:t.circular,"previous-margin":t.previousMargin,"display-multiple-items":"1"},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.swiperChange.apply(void 0,arguments)}}},t._l(t.lists,(function(e,i){return A("v-uni-swiper-item",{key:i},[A("v-uni-view",{staticStyle:{width:"100%",height:"100%"},attrs:{"data-item":e},on:{click:function(A){arguments[0]=A=t.$handleEvent(A),t.goPage(e)}}},[A("u-image",{attrs:{mode:"aspectFill",width:"calc(100% - "+t.previousMargin+")",height:"100%","border-radius":t.radius,src:e.image}})],1)],1)})),1),t.lists.length>1?A("v-uni-view",{staticClass:"dots"},t._l(t.lists,(function(e,i){return A("v-uni-view",{key:i,class:"dot "+(i==t.currentSwiper?"active":"")})})),1):t._e()]:t._e(),t._l(t.lists,(function(e,i){return[i<1?A("v-uni-view",{key:i,staticStyle:{width:"100%",height:"100%"},attrs:{"data-item":e},on:{click:function(A){arguments[0]=A=t.$handleEvent(A),t.goPage(e)}}},[A("u-image",{attrs:{mode:"aspectFill",width:"calc(100% - "+t.previousMargin+")",height:"100%","border-radius":t.radius,src:e.image}})],1):t._e()]}))],2)],1):t._e()},a=[]},db63:function(t,e,A){"use strict";A.r(e);var i=A("8e20"),n=A("a664");for(var a in n)["default"].indexOf(a)<0&&function(t){A.d(e,t,(function(){return n[t]}))}(a);A("4572");var o=A("f0c5"),r=Object(o["a"])(n["default"],i["b"],i["c"],!1,null,"22e17908",null,!1,i["a"],void 0);e["default"]=r.exports},dcc5:function(t,e,A){"use strict";A.d(e,"b",(function(){return i})),A.d(e,"c",(function(){return n})),A.d(e,"a",(function(){}));var i=function(){var t=this,e=t.$createElement,A=t._self._c||e;return A("v-uni-view",{staticClass:"u-wrap",class:"u-lazy-item-"+t.elIndex,style:{opacity:Number(t.opacity),borderRadius:t.borderRadius+"rpx",transition:"opacity "+t.time/1e3+"s ease-in-out"}},[A("v-uni-view",{class:"u-lazy-item-"+t.elIndex},[t.isError?A("v-uni-image",{staticClass:"u-lazy-item error",style:{borderRadius:t.borderRadius+"rpx",height:t.imgHeight},attrs:{src:t.errorImg,mode:t.imgMode},on:{load:function(e){arguments[0]=e=t.$handleEvent(e),t.errorImgLoaded.apply(void 0,arguments)},click:function(e){arguments[0]=e=t.$handleEvent(e),t.clickImg.apply(void 0,arguments)}}}):A("v-uni-image",{staticClass:"u-lazy-item",style:{borderRadius:t.borderRadius+"rpx",height:t.imgHeight},attrs:{src:t.isShow?t.image:t.loadingImg,mode:t.imgMode},on:{load:function(e){arguments[0]=e=t.$handleEvent(e),t.imgLoaded.apply(void 0,arguments)},error:function(e){arguments[0]=e=t.$handleEvent(e),t.loadError.apply(void 0,arguments)},click:function(e){arguments[0]=e=t.$handleEvent(e),t.clickImg.apply(void 0,arguments)}}})],1)],1)},n=[]},e179:function(t,e,A){var i=A("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.u-fixed-placeholder[data-v-94201f12]{box-sizing:initial}.u-tabbar__content[data-v-94201f12]{display:flex;align-items:center;position:relative;position:fixed;bottom:0;left:0;width:100%;z-index:998;box-sizing:initial}.u-tabbar__content__circle__border[data-v-94201f12]{border-radius:100%;width:%?110?%;height:%?110?%;top:%?-48?%;position:absolute;z-index:4;background-color:#fff;left:50%;-webkit-transform:translateX(-50%);transform:translateX(-50%)}.u-tabbar__content__circle__border[data-v-94201f12]:after{border-radius:100px}.u-tabbar__content__item[data-v-94201f12]{flex:1;justify-content:center;height:100%;padding:%?12?% 0;display:flex;flex-direction:row;flex-direction:column;align-items:center;position:relative}.u-tabbar__content__item__button[data-v-94201f12]{position:absolute;top:%?14?%;left:50%;-webkit-transform:translateX(-50%);transform:translateX(-50%)}.u-tabbar__content__item__text[data-v-94201f12]{color:#606266;font-size:%?26?%;line-height:%?28?%;position:absolute;bottom:%?14?%;left:50%;-webkit-transform:translateX(-50%);transform:translateX(-50%);width:100%;text-align:center}.u-tabbar__content__circle[data-v-94201f12]{position:relative;display:flex;flex-direction:row;flex-direction:column;justify-content:space-between;z-index:10;height:calc(100% - 1px)}.u-tabbar__content__circle__button[data-v-94201f12]{width:%?90?%;height:%?90?%;border-radius:100%;display:flex;flex-direction:row;justify-content:center;align-items:center;position:absolute;background-color:#fff;top:%?-40?%;left:50%;z-index:6;-webkit-transform:translateX(-50%);transform:translateX(-50%)}',""]),t.exports=e},e1e1:function(t,e,A){"use strict";A.r(e);var i=A("7383"),n=A("e550");for(var a in n)["default"].indexOf(a)<0&&function(t){A.d(e,t,(function(){return n[t]}))}(a);A("14c8");var o=A("f0c5"),r=Object(o["a"])(n["default"],i["b"],i["c"],!1,null,"01eafde1",null,!1,i["a"],void 0);e["default"]=r.exports},e4b1:function(t,e,A){"use strict";A("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,A("a9e3"),A("2c3e"),A("e25e");var i={name:"u-sticky",props:{offsetTop:{type:[Number,String],default:0},index:{type:[Number,String],default:""},enable:{type:Boolean,default:!0},h5NavHeight:{type:[Number,String],default:44},bgColor:{type:String,default:"#ffffff"},zIndex:{type:[Number,String],default:""}},data:function(){return{fixed:!1,height:"auto",stickyTop:0,elClass:this.$u.guid(),left:0,width:"auto"}},watch:{offsetTop:function(t){this.initObserver()},enable:function(t){0==t?(this.fixed=!1,this.disconnectObserver("contentObserver")):this.initObserver()}},computed:{uZIndex:function(){return this.zIndex?this.zIndex:this.$u.zIndex.sticky}},mounted:function(){this.initObserver()},methods:{initObserver:function(){var t=this;if(this.enable){var e="string"==typeof this.offsetTop?parseInt(this.offsetTop):uni.upx2px(this.offsetTop);this.stickyTop=0!=this.offsetTop?e+this.h5NavHeight:this.h5NavHeight,this.disconnectObserver("contentObserver"),this.$nextTick((function(){t.$uGetRect("."+t.elClass).then((function(e){t.height=e.height,t.left=e.left,t.width=e.width,t.$nextTick((function(){t.observeContent()}))}))}))}},observeContent:function(){var t=this;this.disconnectObserver("contentObserver");var e=this.createIntersectionObserver({thresholds:[.95,.98,1]});e.relativeToViewport({top:-this.stickyTop}),e.observe("."+this.elClass,(function(e){t.enable&&t.setFixed(e.boundingClientRect.top)})),this.contentObserver=e},setFixed:function(t){var e=t<this.stickyTop;e?this.$emit("fixed",this.index):this.fixed&&this.$emit("unfixed",this.index),this.fixed=e},disconnectObserver:function(t){var e=this[t];e&&e.disconnect()}},beforeDestroy:function(){this.disconnectObserver("contentObserver")}};e.default=i},e522:function(t,e,A){"use strict";A.r(e);var i=A("5bc4"),n=A.n(i);for(var a in i)["default"].indexOf(a)<0&&function(t){A.d(e,t,(function(){return i[t]}))}(a);e["default"]=n.a},e531:function(t,e,A){"use strict";A("7a82");var i=A("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=i(A("f07e")),a=i(A("c964"));A("a9e3");var o={props:{show:{type:Boolean,default:!0},value:{type:[String,Number],default:0},bgColor:{type:String,default:"#ffffff"},height:{type:[String,Number],default:"50px"},iconSize:{type:[String,Number],default:40},midButtonSize:{type:[String,Number],default:90},activeColor:{type:String,default:"#303133"},inactiveColor:{type:String,default:"#606266"},midButton:{type:Boolean,default:!1},list:{type:Array,default:function(){return[]}},beforeSwitch:{type:Function,default:null},borderTop:{type:Boolean,default:!0},hideTabBar:{type:Boolean,default:!0}},data:function(){return{midButtonLeft:"50%",pageUrl:""}},created:function(){this.hideTabBar&&uni.hideTabBar();var t=getCurrentPages();this.pageUrl=t[t.length-1].route},computed:{elIconPath:function(){var t=this;return function(e){var A=t.list[e].pagePath;return A?A==t.pageUrl||A=="/"+t.pageUrl?t.list[e].selectedIconPath:t.list[e].iconPath:e==t.value?t.list[e].selectedIconPath:t.list[e].iconPath}},elColor:function(){var t=this;return function(e){var A=t.list[e].pagePath;return A?A==t.pageUrl||A=="/"+t.pageUrl?t.activeColor:t.inactiveColor:e==t.value?t.activeColor:t.inactiveColor}}},mounted:function(){this.midButton&&this.getMidButtonLeft()},methods:{clickHandler:function(t){var e=this;return(0,a.default)((0,n.default)().mark((function A(){var i;return(0,n.default)().wrap((function(A){while(1)switch(A.prev=A.next){case 0:if(!e.beforeSwitch||"function"!==typeof e.beforeSwitch){A.next=10;break}if(i=e.beforeSwitch.bind(e.$u.$parent.call(e))(t),!i||"function"!==typeof i.then){A.next=7;break}return A.next=5,i.then((function(A){e.switchTab(t)})).catch((function(t){}));case 5:A.next=8;break;case 7:!0===i&&e.switchTab(t);case 8:A.next=11;break;case 10:e.switchTab(t);case 11:case"end":return A.stop()}}),A)})))()},switchTab:function(t){this.$emit("change",t),this.list[t].pagePath?uni.switchTab({url:this.list[t].pagePath}):this.$emit("input",t)},getOffsetRight:function(t,e){return e?-20:t>9?-40:-30},getMidButtonLeft:function(){var t=this.$u.sys().windowWidth;this.midButtonLeft=t/2+"px"}}};e.default=o},e550:function(t,e,A){"use strict";A.r(e);var i=A("7942"),n=A.n(i);for(var a in i)["default"].indexOf(a)<0&&function(t){A.d(e,t,(function(){return i[t]}))}(a);e["default"]=n.a},e9b9:function(t,e,A){"use strict";A.d(e,"b",(function(){return n})),A.d(e,"c",(function(){return a})),A.d(e,"a",(function(){return i}));var i={uIcon:A("6976").default,uImage:A("f919").default},n=function(){var t=this,e=t.$createElement,A=t._self._c||e;return A("v-uni-view",{staticClass:"shop-recommend",class:t.type},[t.title?A("v-uni-view",{staticClass:"flex row-between",staticStyle:{padding:"26rpx 20rpx"}},[A("v-uni-view",{staticClass:"bold xl"},[t._v(t._s(t.title))]),t.url?A("router-link",{attrs:{to:t.url}},[A("v-uni-view",{staticClass:"sm"},[t._v("更多"),A("u-icon",{attrs:{name:"arrow-right",size:"22"}})],1)],1):t._e()],1):t._e(),A("v-uni-view",[A("v-uni-swiper",{style:"height:"+t.swiperH+"rpx;",on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.swiperChange.apply(void 0,arguments)}}},t._l(t.shopList,(function(e,i){return A("v-uni-swiper-item",{key:i},[A("v-uni-view",{staticClass:"shop-list flex flex-wrap"},t._l(e,(function(e,i){return A("router-link",{key:i,staticClass:"item-link",attrs:{to:{path:"/pages/store_index/store_index",query:{id:e.id}}}},[A("v-uni-view",{staticClass:"flex-col col-center m-l-24"},[A("v-uni-view",{staticClass:"shop-item bg-white"},[A("u-image",{attrs:{width:"100%",height:"140rpx",mode:"aspectFill",src:e.background}}),A("v-uni-view",{staticClass:"flex-col col-center",staticStyle:{"margin-top":"-34rpx"}},[A("u-image",{attrs:{width:"68rpx",height:"68rpx","border-radius":"50%",src:e.logo}}),A("v-uni-view",{staticClass:"text flex-col col-center"},[A("v-uni-view",{staticClass:"line-1 name"},[t._v(t._s(e.name))]),A("v-uni-view",{staticClass:"br60 muted sale xxs m-t-10 line-1"},[t._v("共"+t._s(e.on_sales_count)+"件商品")])],1)],1)],1)],1)],1)})),1)],1)})),1),t.shopList.length>1?A("v-uni-view",{staticClass:"dots flex row-center m-t-20"},t._l(t.shopList,(function(e,i){return A("v-uni-view",{key:i,class:"dot "+(i==t.currentSwiper?"active":"")})})),1):t._e()],1)],1)},a=[]},edf6:function(t,e,A){"use strict";var i=A("96cf"),n=A.n(i);n.a},ef09:function(t,e,A){var i=A("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.u-waterfall[data-v-7664bcb0]{display:flex;flex-direction:row;flex-direction:row;align-items:flex-start}.u-column[data-v-7664bcb0]{display:flex;flex-direction:row;flex:1;flex-direction:column;height:auto}.u-image[data-v-7664bcb0]{width:100%}',""]),t.exports=e},efe4:function(t,e,A){"use strict";var i=A("cfb4"),n=A.n(i);n.a},f011:function(t,e,A){"use strict";A.r(e);var i=A("84a4"),n=A.n(i);for(var a in i)["default"].indexOf(a)<0&&function(t){A.d(e,t,(function(){return i[t]}))}(a);e["default"]=n.a},f05d:function(t,e,A){var i=A("7f80");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=A("4f06").default;n("7f5f35b1",i,!0,{sourceMap:!1,shadowMode:!1})},f1ad:function(t,e,A){"use strict";A.d(e,"b",(function(){return n})),A.d(e,"c",(function(){return a})),A.d(e,"a",(function(){return i}));var i={uCountDown:A("ae3a").default,uImage:A("f919").default,priceFormat:A("a272").default,uIcon:A("6976").default},n=function(){var t=this,e=t.$createElement,A=t._self._c||e;return A("v-uni-view",{staticClass:"home-seckill bg-white"},[A("v-uni-view",{staticClass:"seckill-hd flex row-between"},[A("v-uni-view",{staticClass:"white xxl bold"},[t._v("超值秒杀")]),A("v-uni-view",[A("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:0==t.currentStatus,expression:"currentStatus == 0"}],staticClass:"white"},[t._v("未开始")]),A("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:2==t.currentStatus,expression:"currentStatus == 2"}],staticClass:"white"},[t._v("已结束")]),t.currentTime>0?A("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:1==t.currentStatus,expression:"currentStatus==1"}],staticClass:"white xs flex"},[A("v-uni-view",{staticClass:"m-r-10"},[t._v("距本场结束")]),A("u-count-down",{attrs:{timestamp:t.currentTime,color:t.colorConfig.primary,"bg-color":"#fff","separator-color":"#fff","font-size":"24",height:"36","separator-size":"26"},on:{end:function(e){arguments[0]=e=t.$handleEvent(e),t.refresh.apply(void 0,arguments)}}})],1):t._e()],1)],1),A("v-uni-scroll-view",{staticStyle:{height:"120rpx","white-space":"nowrap"},attrs:{"scroll-into-view":"item-"+(t.active-2),"scroll-x":"true","scroll-with-animation":"true"}},t._l(t.list,(function(e,i){return A("v-uni-view",{key:i,staticClass:"time-item flex-col row-center col-center",class:{active:i==t.active},attrs:{id:"item-"+i},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.exchangeTime(i)}}},[A("v-uni-view",{class:"xl bold time"},[t._v(t._s(e.start_time))]),A("v-uni-view",{class:"sm br60 state "+(2===e.status?"muted":"")},[t._v(t._s(e.tips))])],1)})),1),t.goodsList.length?A("v-uni-view",{staticClass:"goods-seckill"},t._l(t.goodsList,(function(e,i){return A("router-link",{key:i,attrs:{to:{path:"/pages/goods_details/goods_details",query:{id:e.goods_id||e.id}}}},[A("v-uni-view",{staticClass:"item bg-white flex"},[A("v-uni-view",{staticClass:"goods-img"},[A("u-image",{attrs:{width:"214rpx",height:"214rpx","border-radius":10,src:e.goods_image}})],1),A("v-uni-view",{staticClass:"goods-info m-l-20 flex-1"},[A("v-uni-view",{staticClass:"goods-name line-2"},[t._v(t._s(e.goods_name))]),A("v-uni-view",{staticClass:"price m-t-20 flex row-between"},[A("v-uni-view",{staticClass:"muted xxs"},[t._v("原价"),A("price-format",{attrs:{"first-size":22,"second-size":22,"subscript-size":22,price:e.goods_min_price}})],1),A("v-uni-view",{staticClass:"muted xxs"},[t._v(t._s(e.seckill_total)+"人购买")])],1),A("v-uni-view",{staticClass:"btn flex row-between m-t-20"},[A("price-format",{staticClass:"mr10",attrs:{color:t.colorConfig.primary,"first-size":38,"second-size":26,"subscript-size":26,price:e.seckill_price,weight:500}}),A("v-uni-button",{class:"br60 white "+(2==t.currentStatus?" bg-gray":1==t.currentStatus?"bg-primary":"border-btn"),attrs:{size:"xs"}},[t._v(t._s(2==t.currentStatus?"已结束":1==t.currentStatus?"立即抢购":"未开始"))])],1)],1)],1)],1)})),1):t._e(),A("router-link",{attrs:{to:"/bundle/pages/goods_seckill/goods_seckill"}},[A("v-uni-view",{staticClass:"xs flex row-center more"},[t._v("查看更多"),A("u-icon",{attrs:{name:"arrow-right"}})],1)],1)],1)},a=[]},f215:function(t,e,A){"use strict";A.d(e,"b",(function(){return n})),A.d(e,"c",(function(){return a})),A.d(e,"a",(function(){return i}));var i={uIcon:A("6976").default},n=function(){var t=this,e=t.$createElement,A=t._self._c||e;return t.show?A("v-uni-view",{staticClass:"u-tag",class:[t.disabled?"u-disabled":"","u-size-"+t.size,"u-shape-"+t.shape,"u-mode-"+t.mode+"-"+t.type],style:[t.customStyle],on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.clickTag.apply(void 0,arguments)}}},[t._v(t._s(t.text)),A("v-uni-view",{staticClass:"u-icon-wrap",on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e)}}},[t.closeable?A("u-icon",{staticClass:"u-close-icon",style:[t.iconStyle],attrs:{size:"22",color:t.closeIconColor,name:"close"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.close.apply(void 0,arguments)}}}):t._e()],1)],1):t._e()},a=[]},f479:function(t,e,A){var i=A("b60a");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=A("4f06").default;n("fb742f8e",i,!0,{sourceMap:!1,shadowMode:!1})},f47e:function(t,e,A){"use strict";var i=A("b8a4"),n=A.n(i);n.a},f5a5:function(t,e,A){"use strict";var i=A("f05d"),n=A.n(i);n.a},f654:function(t,e,A){var i=A("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.goods-column .column-wrap[data-v-67ba699e]{display:inline-block;white-space:nowrap;padding:%?30?%}.goods-column .column-wrap .item[data-v-67ba699e]{display:inline-block;position:relative;overflow:hidden}.goods-column .column-wrap .item .title[data-v-67ba699e]{position:relative;z-index:1}.goods-column .column-wrap .item .line[data-v-67ba699e]{position:absolute;top:%?32?%;left:0;width:%?144?%;height:%?12?%;background:linear-gradient(90deg,#ff2c3c,rgba(255,44,60,0))}',""]),t.exports=e},f86e:function(t,e,A){var i=A("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.u-navbar[data-v-6d93ee5a]{width:100%}.u-navbar-fixed[data-v-6d93ee5a]{position:fixed;left:0;right:0;top:0;z-index:991}.u-status-bar[data-v-6d93ee5a]{width:100%}.u-navbar-inner[data-v-6d93ee5a]{width:100%;display:flex;flex-direction:row;justify-content:space-between;position:relative;align-items:center}.u-back-wrap[data-v-6d93ee5a]{display:flex;flex-direction:row;align-items:center;flex:1;flex-grow:0;padding:%?14?% %?14?% %?14?% %?24?%}.u-back-text[data-v-6d93ee5a]{padding-left:%?4?%;font-size:%?30?%}.u-navbar-content-title[data-v-6d93ee5a]{display:flex;flex-direction:row;align-items:center;justify-content:center;flex:1;position:absolute;left:0;right:0;height:%?60?%;text-align:center;flex-shrink:0}.u-navbar-centent-slot[data-v-6d93ee5a]{flex:1}.u-title[data-v-6d93ee5a]{line-height:%?60?%;font-size:%?32?%;flex:1}.u-navbar-right[data-v-6d93ee5a]{flex:1;display:flex;flex-direction:row;align-items:center;justify-content:flex-end}.u-slot-content[data-v-6d93ee5a]{flex:1;display:flex;flex-direction:row;align-items:center;position:relative}',""]),t.exports=e},f879:function(t,e,A){"use strict";var i=A("fccb"),n=A.n(i);n.a},f9f3:function(t,e,A){"use strict";A.r(e);var i=A("574d"),n=A.n(i);for(var a in i)["default"].indexOf(a)<0&&function(t){A.d(e,t,(function(){return i[t]}))}(a);e["default"]=n.a},fb41:function(t,e,A){"use strict";A("7a82");var i=A("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,A("99af");var n=i(A("f07e")),a=i(A("c964")),o=i(A("53f3")),r=A("b550"),s={mixins:[o.default],props:{autoGetData:{type:Boolean,default:!0}},data:function(){return{goodsList:[],active:0,columnList:[],upOption:{auto:!1,empty:{icon:"/static/images/goods_null.png",tip:"暂无商品"},toTop:{bottom:"300rpx"}},downOption:{use:!1,isLock:!0},hasData:!0}},mounted:function(){this.autoGetData&&this.getData()},methods:{getData:function(){var t=this;return(0,a.default)((0,n.default)().mark((function e(){return(0,n.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.getGoodsColumnFun();case 2:t.$refs.uWaterfall&&t.$refs.uWaterfall.clear(),t.mescroll.resetUpScroll();case 4:case"end":return e.stop()}}),e)})))()},changeActive:function(t){this.active=t,this.$refs.uWaterfall.clear(),this.mescroll.resetUpScroll()},upCallback:function(t){var e=this,A=this.columnList,i=this.active,n=t.num,a=t.size;if(A.length){var o=A[i].id;(0,r.getGoodsListColumn)({page_size:a,page_no:n,column_id:o}).then((function(A){var i=A.data,n=i.lists,a=n.length,o=!!i.more;1==t.num&&(e.goodsList=[]),e.goodsList=e.goodsList.concat(n),e.mescroll.endSuccess(a,o)}))}},getGoodsColumnFun:function(){var t=this;return(0,a.default)((0,n.default)().mark((function e(){var A,i,a;return(0,n.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,(0,r.getGoodsColumn)();case 2:A=e.sent,i=A.data,a=A.code,1==a&&(t.columnList=i,t.hasData=!!i.length);case 6:case"end":return e.stop()}}),e)})))()}}};e.default=s},fbae:function(t,e,A){"use strict";var i=A("ae8b"),n=A.n(i);n.a},fc0d:function(t,e,A){"use strict";A("7a82");var i=A("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=i(A("f07e")),a=i(A("c964"));A("a9e3"),A("99af");var o=i(A("53f3")),r=i(A("9ba6")),s=A("b550"),u=(getApp(),{mixins:[o.default,r.default],name:"cate-home",props:{top:{type:[Number,String]},cate:{type:Object,default:function(){return{}}}},data:function(){return{goodsList:[],navList:[],hotGoods:[],newGoods:[],downOption:{auto:!1},upOption:{auto:!1,noMoreSize:1,empty:{icon:"/static/images/goods_null.png",tip:"暂无商品~"}}}},created:function(){},computed:{positionTop:function(){return this.top}},methods:{downCallback:function(){var t=this;return(0,a.default)((0,n.default)().mark((function e(){return(0,n.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.getIndexCategory();case 2:t.$refs.uWaterfall.clear&&t.$refs.uWaterfall.clear(),t.mescroll.resetUpScroll();case 4:case"end":return e.stop()}}),e)})))()},upCallback:function(t){var e=this,A=t.num,i=t.size;(0,s.getGoodsList)({page_size:i,page_no:A,platform_cate_id:this.cate.id}).then((function(A){var i=A.data,n=i.lists,a=n.length,o=!!i.more;1==t.num&&(e.goodsList=[]),e.goodsList=e.goodsList.concat(n),e.mescroll.endSuccess(a,o)}))},getIndexCategory:function(){var t=this;return(0,a.default)((0,n.default)().mark((function e(){var A,i,a,o,r,u,c;return(0,n.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return A=t.cate.id,e.next=3,(0,s.getIndexCategory)({platform_category_id:A});case 3:i=e.sent,a=i.code,o=i.data,1==a&&(r=o.level_two,u=o.category_hots,c=o.category_recommend,t.navList=r,t.hotGoods=u,t.newGoods=c);case 7:case"end":return e.stop()}}),e)})))()}}});e.default=u},fccb:function(t,e,A){var i=A("76a9");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=A("4f06").default;n("0e3d70e9",i,!0,{sourceMap:!1,shadowMode:!1})},ffc0:function(t,e,A){"use strict";A.d(e,"b",(function(){return n})),A.d(e,"c",(function(){return a})),A.d(e,"a",(function(){return i}));var i={adSwipers:A("d058").default,bubbleTips:A("e1e1").default,uTag:A("761c").default,uIcon:A("6976").default,uImage:A("f919").default,homeSeckill:A("bf73").default,shopRecommend:A("8aef").default,activeArea:A("7374").default,communityRecommend:A("59ef").default,shopItem:A("db63").default},n=function(){var t=this,e=t.$createElement,A=t._self._c||e;return A("v-uni-view",{staticClass:"index-home p-t-20"},[A("ad-swipers",{attrs:{pid:7,height:"312rpx",padding:"0 30rpx",radius:"20rpx"}}),A("bubble-tips",{attrs:{top:"280rpx",discharge:t.isDischarge}}),A("v-uni-view",{staticClass:"content"},[t.newNavList.length?A("v-uni-view",{staticClass:"nav bg-white m-t-20"},[A("v-uni-swiper",{style:"height:"+t.navSwiperH+"rpx;",on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.swiperChange.apply(void 0,arguments)}}},t._l(t.newNavList,(function(e,i){return A("v-uni-swiper-item",{key:i},[A("v-uni-view",{staticClass:"nav-list flex flex-wrap"},t._l(e,(function(e,i){return A("v-uni-view",{key:i,staticClass:"nav-item m-t-30",on:{click:function(A){arguments[0]=A=t.$handleEvent(A),t.menuJump(e)}}},[A("v-uni-view",{staticClass:"flex-col col-center"},[A("v-uni-image",{staticClass:"nav-icon m-b-15",attrs:{src:e.image}}),A("v-uni-view",{staticClass:"name xs"},[t._v(t._s(e.name))])],1)],1)})),1)],1)})),1),t.newNavList.length>1?A("v-uni-view",{staticClass:"dots"},t._l(t.newNavList,(function(e,i){return A("v-uni-view",{key:i,class:"dot "+(i==t.currentSwiper?"active":"")})})),1):t._e()],1):t._e(),A("ad-swipers",{attrs:{pid:8,height:"165rpx","is-swiper":!1,padding:"20rpx 0 0",radius:"20rpx"}}),t.newsList.length?A("router-link",{attrs:{to:{path:"/pages/news_list/news_list"}}},[A("v-uni-view",{staticClass:"information bg-white flex m-t-20"},[A("v-uni-image",{staticClass:"icon-toutiao",attrs:{src:"/static/images/icon_toutiao.png"}}),A("v-uni-text",{staticClass:"gap-line"}),A("v-uni-view",{staticClass:"news flex-1 flex"},[A("v-uni-view",{staticClass:"shade"}),A("v-uni-swiper",{staticClass:"flex-1",staticStyle:{height:"76rpx"},attrs:{autoplay:"true",vertical:"true",circular:"true",interval:3e3}},t._l(t.newsList,(function(e,i){return A("v-uni-swiper-item",{key:i,staticClass:"flex"},[A("v-uni-view",{staticClass:"flex-none"},[e.is_new?A("u-tag",{attrs:{shape:"circle",text:"最新",size:"mini",type:"primary",mode:"plain"}}):t._e()],1),A("v-uni-view",{staticClass:"text-swiper m-l-10 line-1"},[t._v(t._s(e.title))])],1)})),1)],1),A("u-icon",{attrs:{name:"arrow-right"}})],1)],1):t._e(),t.activityArea.length?A("v-uni-view",{staticClass:"activity-zone m-t-20"},[A("v-uni-view",{staticClass:"flex p-20 row-center xxl bold white"},[t._v("活动专区")]),A("v-uni-view",{staticClass:"list flex flex-wrap row-between"},t._l(t.activityArea,(function(e,i){return A("router-link",{key:i,attrs:{to:{path:"/bundle/pages/activity_detail/activity_detail",query:{name:e.name,title:e.title,id:e.id}}}},[A("v-uni-view",{staticClass:"item flex bg-white m-b-20"},[A("u-image",{attrs:{width:"120rpx",height:"120rpx",src:e.image}}),A("v-uni-view",{staticClass:"m-l-20 flex-1"},[A("v-uni-view",{staticClass:"bold lg line-1 desc"},[t._v(t._s(e.name))]),A("v-uni-view",{staticClass:"primary sm m-t-5 line-1 desc"},[t._v(t._s(e.title))]),A("v-uni-view",{staticClass:"br60 bg-primary white xxs m-t-10 btn"},[t._v("前往查看")])],1)],1)],1)})),1)],1):t._e(),t.seckillGoods.length?A("v-uni-view",{staticClass:"seckill m-t-20"},[A("home-seckill",{attrs:{list:t.seckillGoods}})],1):t._e(),t.nearbyShop.length?A("v-uni-view",{staticClass:"m-t-20"},[A("shop-recommend",{attrs:{type:"nearby-shops",title:"附近店铺",list:t.nearbyShop,url:"/bundle_b/pages/nearby_shops/nearby_shops"}})],1):t._e(),t.appConfig.index_setting.host_show&&t.hotGoods.length?A("v-uni-view",{staticClass:"m-t-20"},[A("active-area",{attrs:{list:t.hotGoods,type:"hot",title:"热销榜单",url:"/pages/active_list/active_list?type=hot"}})],1):t._e(),t.appConfig.index_setting.new_show&&t.newGoods.length?A("v-uni-view",{staticClass:"m-t-20"},[A("active-area",{attrs:{list:t.newGoods,type:"new",title:"新品推荐",url:"/pages/active_list/active_list?type=new"}})],1):t._e(),t.appConfig.index_setting.community_show&&t.communityArticle.length?A("v-uni-view",{staticClass:"m-t-20"},[A("community-recommend",{attrs:{list:t.communityArticle,title:"种草社区",url:"/pages/community/community"}})],1):t._e(),t.appConfig.index_setting.shop_show&&t.shopLists.length?A("v-uni-view",{staticClass:"m-t-20"},[A("shop-recommend",{attrs:{type:"shop-recommends",title:"店铺推荐",list:t.shopLists}})],1):t._e(),t._l(t.shopRecommend,(function(e,i){return t.appConfig.index_setting.shop_show&&t.shopRecommend.length?A("v-uni-view",{key:i,staticClass:"m-t-20"},[A("shop-item",{attrs:{item:e}})],1):t._e()}))],2)],1)},a=[]}}]);