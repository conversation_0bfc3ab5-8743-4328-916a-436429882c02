# 身份证OCR识别功能使用说明

## 功能概述

本功能集成了微信身份证OCR识别API，支持身份证正面和背面的自动识别，并提供数据验证和信息提取功能。

## 主要特性

1. **身份证正面识别**：提取姓名、身份证号、性别、民族、地址等信息
2. **身份证背面识别**：提取有效期等信息
3. **数据验证**：校验识别结果与用户输入信息的一致性
4. **错误处理**：完善的错误码映射和异常处理
5. **信息提取**：自动提取和格式化识别结果

## 数据库变更

### 添加字段

```sql
-- 添加身份证识别数据字段到 ls_agent 表
ALTER TABLE `ls_agent` ADD COLUMN `card_data` text COMMENT '身份证识别原始数据' AFTER `id_card_back`;
```

## 核心类和方法

### 1. WechatService::idCardOcr()

**位置**: `app\common\server\WechatService.php`

**功能**: 调用微信身份证OCR API进行单张图片识别

**参数**:
- `$imageUrl` (string): 图片URL或本地路径
- `$type` (string): 识别类型，'Front' 或 'Back'

**返回值**:
```php
[
    'errcode' => 0,        // 错误码，0表示成功
    'errmsg' => 'ok',      // 错误信息
    'type' => 'Front',     // 识别类型
    'name' => '张三',       // 姓名（正面）
    'id' => '123456...',   // 身份证号（正面）
    'addr' => '广东省...',  // 地址（正面）
    'gender' => '男',      // 性别（正面）
    'nationality' => '汉', // 民族（正面）
    'valid_date' => '2030-12-31' // 有效期（背面）
]
```

### 2. IdCardOcrServer::recognizeIdCard()

**位置**: `app\common\server\IdCardOcrServer.php`

**功能**: 同时识别身份证正面和背面，并进行基本验证

**参数**:
- `$frontImageUrl` (string): 身份证正面图片URL
- `$backImageUrl` (string): 身份证背面图片URL

**返回值**:
```php
[
    'success' => true,     // 是否成功
    'message' => '',       // 消息
    'data' => [
        'front' => [...],  // 正面识别结果
        'back' => [...]    // 背面识别结果
    ],
    'errors' => []         // 错误列表
]
```

### 3. IdCardOcrServer::validateIdCardInfo()

**功能**: 验证OCR识别结果与用户输入信息的一致性

**参数**:
- `$ocrData` (array): OCR识别结果
- `$inputData` (array): 用户输入数据

### 4. IdCardOcrServer::extractInfo()

**功能**: 从OCR结果中提取格式化的信息

## 在 investmentConsultantAuth 接口中的使用

### 接口路径
`POST /api/user/investmentConsultantAuth`

### 请求参数
```json
{
    "id": "代理ID",
    "name": "姓名",
    "mobile": "手机号",
    "address": "地址",
    "id_card_number": "身份证号",
    "id_card_front": "身份证正面图片URL",
    "id_card_back": "身份证背面图片URL",
    "province": "省份",
    "city": "城市",
    "district": "区县"
}
```

### 处理流程

1. **参数验证**: 检查必填字段
2. **身份证识别**: 调用OCR API识别正面和背面
3. **结果验证**: 
   - 验证图片类型（正面/背面）
   - 检查身份证有效期
   - 校验姓名和身份证号一致性
4. **信息补全**: 自动填充识别到的信息
5. **数据存储**: 将原始OCR数据存储到 `card_data` 字段

### 响应示例

**成功响应**:
```json
{
    "code": 1,
    "msg": "认证资料提交成功",
    "data": {
        "ocr_data": {
            "front": {...},
            "back": {...}
        },
        "extracted_info": {
            "name": "张三",
            "id_card_number": "123456...",
            "gender": "男",
            "nationality": "汉",
            "address": "广东省...",
            "valid_date": "2030-12-31"
        }
    }
}
```

**错误响应**:
```json
{
    "code": 0,
    "msg": "身份证正面识别失败: 未检测到证件",
    "data": []
}
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 0 | 成功 |
| -1 | 系统错误 |
| 40001 | access_token无效或已过期 |
| 101000 | 图片URL错误 |
| 101001 | 未检测到证件 |
| 101002 | 图片大小超过限制或解码失败 |
| 101003 | 市场配额不足 |

## 测试接口

### 1. 测试身份证识别
`POST /api/testIdCard/testOcr`

### 2. 测试单张图片识别
`POST /api/testIdCard/testSingle`

### 3. 测试信息验证
`POST /api/testIdCard/testValidate`

### 4. 获取错误码说明
`GET /api/testIdCard/getErrorCodes`

## 注意事项

1. **图片要求**:
   - 支持URL和本地文件路径
   - 文件大小不超过2MB
   - 支持常见图片格式

2. **API限制**:
   - 微信小程序每天100次免费调用
   - 需要已认证的小程序账号

3. **安全考虑**:
   - 接口应在服务器端调用，不可在前端直接调用
   - OCR原始数据会存储在数据库中，注意数据安全

4. **错误处理**:
   - 完善的错误码映射
   - 详细的日志记录
   - 友好的错误提示

## 配置要求

确保在配置文件中正确设置微信小程序的 AppID 和 AppSecret：

```php
// 配置路径示例
'mnp' => [
    'app_id' => 'your_app_id',
    'secret' => 'your_app_secret'
]
```
