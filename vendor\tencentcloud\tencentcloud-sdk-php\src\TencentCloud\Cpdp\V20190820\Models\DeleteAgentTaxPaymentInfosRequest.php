<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Cpdp\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * DeleteAgentTaxPaymentInfos请求参数结构体
 *
 * @method integer getBatchNum() 获取批次号
 * @method void setBatchNum(integer $BatchNum) 设置批次号
 */
class DeleteAgentTaxPaymentInfosRequest extends AbstractModel
{
    /**
     * @var integer 批次号
     */
    public $BatchNum;

    /**
     * @param integer $BatchNum 批次号
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("BatchNum",$param) and $param["BatchNum"] !== null) {
            $this->BatchNum = $param["BatchNum"];
        }
    }
}
