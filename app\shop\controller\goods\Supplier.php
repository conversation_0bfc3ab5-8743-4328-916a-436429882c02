<?php



namespace app\shop\controller\goods;


use app\shop\logic\goods\SupplierLogic;
use app\shop\validate\goods\SupplierValidate;
use app\common\basics\ShopBase;
use app\common\model\goods\Supplier as SupplierModel;
use app\common\server\JsonServer;


/**
 * 供应商
 * Class GoodsBrand
 * @package app\admin\controller
 */
class Supplier extends ShopBase
{
    /**
     * Notes: 列表
     * <AUTHOR> 10:49)
     * @return string|\think\response\Json
     */
    public function lists()
    {
        if ($this->request->isAjax()) {
            $get = $this->request->get();
            return JsonServer::success('获取成功', SupplierLogic::lists($this->shop_id, $get));
        }
        return view();
    }


    /**
     * Notes: 添加
     * <AUTHOR> 10:49)
     * @return string|\think\response\Json
     */
    public function add()
    {
        if ($this->request->isAjax()) {
            $post = $this->request->post();
            (new SupplierValidate())->goCheck('add');
            if (SupplierLogic::add($this->shop_id, $post)) {
                return JsonServer::success('操作成功');
            }
            return JsonServer::error(SupplierLogic::getError() ?: '操作失败');
        }
        return view('', ['capital' => getCapital()]);
    }

    /**
     * Notes: 编辑
     * <AUTHOR> 10:49)
     * @return string|\think\response\Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function edit()
    {
        $id = $this->request->get('id');
        if ($this->request->isAjax()) {
            $post = $this->request->post();
            (new SupplierValidate())->goCheck('edit');
            if (SupplierLogic::edit($this->shop_id, $post)) {
                return JsonServer::success('操作成功');
            }
            return JsonServer::error(SupplierLogic::getError() ?: '操作失败');
        }
        return view('', ['detail' => SupplierModel::find($id)]);
    }

    /**
     * Notes: 删除
     * <AUTHOR> 10:49)
     * @return \think\response\Json
     */
    public function del()
    {
        if ($this->request->isAjax()) {
            $id = $this->request->post('id');
            (new SupplierValidate())->goCheck('del');
            if (SupplierLogic::del($this->shop_id, $id)) {
                return JsonServer::success('操作成功');
            }
            return JsonServer::error(SupplierLogic::getError() ?: '操作失败');
        }
    }
}