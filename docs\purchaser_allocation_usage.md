# 采购人员分配系统使用说明

## 概述

采购人员分配系统是一个自动化的商家采购人员分配解决方案，根据商家等级自动分配不同数量和活跃度等级的采购人员，同时管理群发信息限制。

## 功能特性

1. **自动分配机制**：根据商家等级自动分配采购人员
2. **活跃度分级**：按用户活跃度积分分为低、中、高三个等级
3. **群发信息限制**：每个等级商家有不同的每日群发限制
4. **年度重新分配**：每年自动重新分配采购人员
5. **手动管理**：支持手动重新分配和配置管理

## 数据库表结构

### 1. ls_mass_message_limit (群发信息限制配置表)
- 扩展了原有表，增加了采购人员分配相关字段
- 包含每日群发限制、采购人员总数、各等级占比等配置

### 2. ls_shop_purchaser_allocation (商家采购人员分配记录表)
- 记录每个商家分配的采购人员
- 包含分配年份、用户等级、分配时间等信息

### 3. ls_mass_message_record (群发信息发送记录表)
- 记录商家的群发信息发送历史
- 用于统计每日发送数量和限制检查

## 后台管理

### 配置管理
1. 访问后台 -> 设置 -> 群发信息限制与采购人员分配配置
2. 可以添加、编辑、删除不同商家等级的配置
3. 配置包括：
   - 每日群发限制数量
   - 采购人员总数
   - 低/中/高活跃度用户占比
   - 活跃度积分阈值

### 重新分配
- 支持一键重新分配所有商家的采购人员
- 可以查看分配统计和详情

## API接口

### ShopAPI接口列表

#### 1. 获取采购人员列表
```
GET /shopapi/purchaser/lists
参数：
- page: 页码 (可选，默认1)
- limit: 每页数量 (可选，默认20)

返回：
{
    "code": 1,
    "msg": "获取成功",
    "data": {
        "list": [...],
        "total": 100,
        "page": 1,
        "limit": 20,
        "has_more": true
    }
}
```

#### 2. 获取分配统计
```
GET /shopapi/purchaser/stats

返回：
{
    "code": 1,
    "msg": "获取成功",
    "data": {
        "total": 50,
        "level1": 25,
        "level2": 15,
        "level3": 10,
        "config": {...},
        "allocation_rate": 100.0
    }
}
```

#### 3. 获取群发限制信息
```
GET /shopapi/purchaser/massMessageLimit

返回：
{
    "code": 1,
    "msg": "获取成功",
    "data": {
        "daily_limit": 20,
        "sent_today": 5,
        "remaining": 15,
        "tier_level": 1,
        "tier_name": "商家会员"
    }
}
```

#### 4. 检查群发限制
```
GET /shopapi/purchaser/checkMassMessageLimit

返回：
{
    "code": 1,
    "msg": "检查完成",
    "data": {
        "can_send": true,
        "message": "可以发送"
    }
}
```

#### 5. 记录群发信息
```
POST /shopapi/purchaser/recordMassMessage
参数：
{
    "content": "群发内容",
    "message_type": "text",
    "target_user_ids": [1, 2, 3],
    "success_count": 3,
    "fail_count": 0
}

返回：
{
    "code": 1,
    "msg": "记录成功",
    "data": {
        "id": 123
    }
}
```

## 自动分配逻辑

### 触发时机
1. **商家入驻时**：0元入驻完成后自动分配
2. **商家升级时**：购买会员或实力厂商后自动分配
3. **年度重新分配**：每年1月1日自动检查并重新分配

### 分配规则
1. 根据商家等级获取配置的采购人员总数和各等级占比
2. 优先分配采购商用户（is_purchaser=1）
3. 采购商不足时用普通用户补充
4. 按活跃度积分分级：
   - 低活跃度：积分 < 101
   - 中活跃度：积分 101-500
   - 高活跃度：积分 >= 501
5. 同一用户不会重复分配给同一商家
6. 不会分配商家自己的用户

### 事件触发
在商家升级时触发事件：
```php
// 触发商家升级事件
event('ShopTierUpgrade', [
    'shop_id' => $shopId,
    'new_tier_level' => $newTierLevel,
    'old_tier_level' => $oldTierLevel
]);
```

## 定时任务

### 年度重新分配任务
```bash
# 年度检查（推荐每天运行一次）
php think purchaser:allocate yearly-check

# 强制重新分配所有商家
php think purchaser:allocate force-reallocate

# 为指定商家重新分配
php think purchaser:allocate force-reallocate --shop-id=123

# 查看统计信息
php think purchaser:allocate stats

# 试运行模式（不实际执行）
php think purchaser:allocate yearly-check --dry-run
```

### 定时任务配置
建议在crontab中配置：
```bash
# 每天凌晨2点执行年度检查
0 2 * * * cd /path/to/project && php think purchaser:allocate yearly-check
```

## 使用示例

### 1. 商家升级时自动分配
```php
use app\common\service\PurchaserAllocationService;

// 商家升级后调用
$result = PurchaserAllocationService::onShopTierUpgrade($shopId, $newTierLevel, $oldTierLevel);
```

### 2. 手动重新分配
```php
use app\admin\logic\PurchaserAllocationLogic;

// 为单个商家重新分配
$result = PurchaserAllocationLogic::manualReAllocate($shopId);
```

### 3. 检查群发限制
```php
use app\api\logic\MassMessageLogic;

// 检查是否可以发送群发信息
$canSend = MassMessageLogic::checkMassMessageLimit($shopId);
```

## 注意事项

1. **数据一致性**：分配过程中会清除现有分配记录，确保数据一致性
2. **性能考虑**：大量商家重新分配时可能耗时较长，建议在低峰期执行
3. **日志记录**：所有分配操作都会记录日志，便于问题排查
4. **权限控制**：手动重新分配功能需要适当的权限控制
5. **配置验证**：各等级用户占比总和必须为100%

## 故障排除

### 常见问题
1. **分配失败**：检查采购人员数量是否充足
2. **配置错误**：确保百分比总和为100%
3. **权限问题**：确保有足够的数据库操作权限
4. **日志查看**：查看系统日志了解详细错误信息

### 日志位置
- 系统日志：`runtime/log/`
- 分配日志：搜索关键词 "采购人员分配"
