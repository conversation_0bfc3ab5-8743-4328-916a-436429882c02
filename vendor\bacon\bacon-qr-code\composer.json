{"name": "bacon/bacon-qr-code", "description": "BaconQrCode is a QR code generator for PHP.", "license": "BSD-2-<PERSON><PERSON>", "homepage": "https://github.com/Bacon/BaconQrCode", "require": {"php": "^7.1 || ^8.0", "ext-iconv": "*", "dasprid/enum": "^1.0.3"}, "suggest": {"ext-imagick": "to generate QR code images"}, "authors": [{"name": "<PERSON> 'DASPRiD'", "email": "<EMAIL>", "homepage": "https://dasprids.de/", "role": "Developer"}], "autoload": {"psr-4": {"BaconQrCode\\": "src/"}}, "require-dev": {"phpunit/phpunit": "^7 | ^8 | ^9", "squizlabs/php_codesniffer": "^3.4", "phly/keep-a-changelog": "^1.4"}}