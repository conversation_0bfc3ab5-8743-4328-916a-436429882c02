-- 扩展微信回复表，支持多媒体消息和多条消息
-- 执行时间：2024-12-28

-- 1. 修改 content_type 字段注释，扩展支持的类型
ALTER TABLE `ls_wechat_reply` MODIFY COLUMN `content_type` tinyint(1) NULL DEFAULT NULL COMMENT '内容类型：null-不设置；1-文本；2-图片；3-混合（文本+图片）';

-- 2. 添加图片相关字段
ALTER TABLE `ls_wechat_reply` ADD COLUMN `image_url` varchar(255) NULL DEFAULT NULL COMMENT '图片URL地址' AFTER `content`;

-- 3. 添加多消息支持字段
ALTER TABLE `ls_wechat_reply` ADD COLUMN `message_count` tinyint(1) NOT NULL DEFAULT 1 COMMENT '消息数量：1-单条；2-多条' AFTER `image_url`;

-- 4. 添加第二条消息内容字段（用于存储图片消息的文字说明等）
ALTER TABLE `ls_wechat_reply` ADD COLUMN `second_content` text NULL DEFAULT NULL COMMENT '第二条消息内容' AFTER `message_count`;

-- 5. 添加第二条消息类型字段
ALTER TABLE `ls_wechat_reply` ADD COLUMN `second_content_type` tinyint(1) NULL DEFAULT NULL COMMENT '第二条消息类型：1-文本；2-图片' AFTER `second_content`;

-- 6. 添加第二条消息图片URL字段
ALTER TABLE `ls_wechat_reply` ADD COLUMN `second_image_url` varchar(255) NULL DEFAULT NULL COMMENT '第二条消息图片URL' AFTER `second_content_type`;

-- 插入测试数据：进采购群回复规则
INSERT INTO `ls_wechat_reply` (
    `name`, 
    `keyword`, 
    `reply_type`, 
    `matching_type`, 
    `content_type`, 
    `content`, 
    `image_url`,
    `message_count`,
    `second_content`,
    `second_content_type`,
    `second_image_url`,
    `status`, 
    `sort`, 
    `create_time`, 
    `del`
) VALUES (
    '进采购群回复', 
    '进采购群', 
    'text', 
    1, 
    1, 
    '欢迎加入我们的采购群！请扫描下方二维码进群，我们有专业的采购团队为您服务。', 
    NULL,
    2,
    '采购群二维码',
    2,
    '/uploads/qrcode/purchase_group_qr.jpg',
    1, 
    1, 
    UNIX_TIMESTAMP(), 
    0
);
