(window.webpackJsonp=window.webpackJsonp||[]).push([[26,10],{473:function(e,t,r){"use strict";var n=r(14),o=r(4),c=r(5),l=r(141),f=r(24),m=r(18),d=r(290),h=r(54),v=r(104),x=r(289),S=r(3),w=r(105).f,_=r(45).f,y=r(23).f,C=r(474),k=r(475).trim,N="Number",I=o.Number,E=I.prototype,M=o.TypeError,T=c("".slice),O=c("".charCodeAt),R=function(e){var t=x(e,"number");return"bigint"==typeof t?t:A(t)},A=function(e){var t,r,n,o,c,l,f,code,m=x(e,"number");if(v(m))throw M("Cannot convert a Symbol value to a number");if("string"==typeof m&&m.length>2)if(m=k(m),43===(t=O(m,0))||45===t){if(88===(r=O(m,2))||120===r)return NaN}else if(48===t){switch(O(m,1)){case 66:case 98:n=2,o=49;break;case 79:case 111:n=8,o=55;break;default:return+m}for(l=(c=T(m,2)).length,f=0;f<l;f++)if((code=O(c,f))<48||code>o)return NaN;return parseInt(c,n)}return+m};if(l(N,!I(" 0o1")||!I("0b1")||I("+0x1"))){for(var $,D=function(e){var t=arguments.length<1?0:I(R(e)),r=this;return h(E,r)&&S((function(){C(r)}))?d(Object(t),r,D):t},F=n?w(I):"MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,EPSILON,MAX_SAFE_INTEGER,MIN_SAFE_INTEGER,isFinite,isInteger,isNaN,isSafeInteger,parseFloat,parseInt,fromString,range".split(","),j=0;F.length>j;j++)m(I,$=F[j])&&!m(D,$)&&y(D,$,_(I,$));D.prototype=E,E.constructor=D,f(o,N,D,{constructor:!0})}},474:function(e,t,r){var n=r(5);e.exports=n(1..valueOf)},475:function(e,t,r){var n=r(5),o=r(36),c=r(19),l=r(476),f=n("".replace),m="["+l+"]",d=RegExp("^"+m+m+"*"),h=RegExp(m+m+"*$"),v=function(e){return function(t){var r=c(o(t));return 1&e&&(r=f(r,d,"")),2&e&&(r=f(r,h,"")),r}};e.exports={start:v(1),end:v(2),trim:v(3)}},476:function(e,t){e.exports="\t\n\v\f\r                　\u2028\u2029\ufeff"},485:function(e,t,r){"use strict";r.r(t);r(473),r(86),r(62),r(12),r(107),r(40),r(106);var n=6e4,o=36e5,c=24*o;function l(e){return(0+e.toString()).slice(-2)}var f={components:{},props:{isSlot:{type:Boolean,default:!1},time:{type:Number,default:0},format:{type:String,default:"hh:mm:ss"},autoStart:{type:Boolean,default:!0}},watch:{time:{immediate:!0,handler:function(e){e&&this.reset()}}},data:function(){return{timeObj:{},formateTime:0}},created:function(){},computed:{},methods:{createTimer:function(e){return setTimeout(e,100)},isSameSecond:function(e,t){return Math.floor(e)===Math.floor(t)},start:function(){this.counting||(this.counting=!0,this.endTime=Date.now()+1e3*this.remain,this.setTimer())},setTimer:function(){var e=this;this.tid=this.createTimer((function(){var t=e.getRemain();e.isSameSecond(t,e.remain)&&0!==t||e.setRemain(t),0!==e.remain&&e.setTimer()}))},getRemain:function(){return Math.max(this.endTime-Date.now(),0)},pause:function(){this.counting=!1,clearTimeout(this.tid)},reset:function(){this.pause(),this.remain=this.time,this.setRemain(this.remain),this.autoStart&&this.start()},setRemain:function(e){var t=this.format;this.remain=e;var time,r=(time=e,{days:Math.floor(time/c),hours:l(Math.floor(time%c/o)),minutes:l(Math.floor(time%o/n)),seconds:l(Math.floor(time%n/1e3))});this.formateTime=function(e,t){var r=t.days,n=t.hours,o=t.minutes,c=t.seconds;return-1!==e.indexOf("dd")&&(e=e.replace("dd",r)),-1!==e.indexOf("hh")&&(e=e.replace("hh",l(n))),-1!==e.indexOf("mm")&&(e=e.replace("mm",l(o))),-1!==e.indexOf("ss")&&(e=e.replace("ss",l(c))),e}(t,r),this.$emit("change",r),0===e&&(this.pause(),this.$emit("finish"))}}},m=r(8),component=Object(m.a)(f,(function(){var e=this,t=e._self._c;return e.time>=0?t("div",[t("client-only",[e.isSlot?e._t("default"):t("span",[e._v(e._s(e.formateTime))])],2)],1):e._e()}),[],!1,null,null,null);t.default=component.exports},493:function(e,t,r){"use strict";r.d(t,"d",(function(){return n})),r.d(t,"e",(function(){return o})),r.d(t,"c",(function(){return c})),r.d(t,"b",(function(){return l})),r.d(t,"a",(function(){return f}));var n=5,o={SMS:0,ACCOUNT:1},c={REGISTER:"ZCYZ",FINDPWD:"ZHMM",LOGIN:"YZMDL",SJSQYZ:"SJSQYZ",CHANGE_MOBILE:"BGSJHM",BIND:"BDSJHM"},l={NONE:"",SEX:"sex",NICKNAME:"nickname",AVATAR:"avatar",MOBILE:"mobile"},f={NORMAL:"normal",HANDLING:"apply",FINISH:"finish"}},578:function(e,t,r){var content=r(644);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[e.i,content,""]]),content.locals&&(e.exports=content.locals);(0,r(17).default)("6125cba8",content,!0,{sourceMap:!1})},643:function(e,t,r){"use strict";r(578)},644:function(e,t,r){var n=r(16)(!1);n.push([e.i,".register-container[data-v-9ce8e830]{flex:1}.register-container .register-box[data-v-9ce8e830]{padding-top:40px;padding-bottom:55px;width:880px;border:1px solid #e5e5e5}.register-container .register-box .register-title[data-v-9ce8e830]{font-size:24px}.register-container .register-box .form-box .register-form-item[data-v-9ce8e830]{margin-top:24px}.register-container .register-box .form-box .register-form-item .form-input[data-v-9ce8e830]{width:400px}.register-container .register-box .form-box .register-form-item .verify-code-img[data-v-9ce8e830]{width:100px;height:40px;margin-left:26px;background-color:red}.register-container .register-box .form-box .register-form-item .sms-btn[data-v-9ce8e830]{margin-left:16px;height:40px;width:120px}",""]),e.exports=n},712:function(e,t,r){"use strict";r.r(t);var n=r(9),o=(r(40),r(106),r(53),r(493)),c={head:function(){return{title:this.$store.getters.headTitle,link:[{rel:"icon",type:"image/x-icon",href:this.$store.getters.favicon}]}},layout:"main",components:{CountDown:r(485).default},data:function(){return{telephone:"",smsCode:"",password:"",againPwd:"",canSend:!0,checked:!1}},computed:{registerSetting:function(){return this.$store.state.config.register_setting}},methods:{sendSMSCode:function(){var e=this;return Object(n.a)(regeneratorRuntime.mark((function t(){var r;return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(e.canSend){t.next=2;break}return t.abrupt("return");case 2:return t.next=4,e.$post("sms/send",{mobile:e.telephone,key:o.c.REGISTER});case 4:1==(r=t.sent).code&&(e.$message({message:r.msg,type:"success"}),e.canSend=!1);case 6:case"end":return t.stop()}}),t)})))()},registerFun:function(){var e=this;return Object(n.a)(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(e.telephone){t.next=3;break}return e.$message({message:"请输入手机号",type:"error"}),t.abrupt("return");case 3:if(!e.registerSetting||e.smsCode){t.next=6;break}return e.$message({message:"请输入短信验证码",type:"error"}),t.abrupt("return");case 6:if(e.password){t.next=9;break}return e.$message({message:"请输入密码",type:"error"}),t.abrupt("return");case 9:if(e.password==e.againPwd){t.next=12;break}return e.$message({message:"两次密码输入不一致",type:"error"}),t.abrupt("return");case 12:if(e.checked){t.next=15;break}return e.$message({message:"请勾选已阅读并同意《服务协议》、《政策协议》",type:"error"}),t.abrupt("return");case 15:return t.next=17,e.$post("account/register",{mobile:e.telephone,password:e.password,code:e.smsCode,client:o.d});case 17:1==t.sent.code&&(e.$message({message:"注册成功",type:"success"}),e.$router.replace("/account/login"));case 19:case"end":return t.stop()}}),t)})))()},jumpUrl:function(e){window.open("server_explan?type=".concat(e),"_blank")}}},l=c,f=(r(643),r(8)),component=Object(f.a)(l,(function(){var e=this,t=e._self._c;return t("div",{staticClass:"register-container flex-col row-center col-center"},[t("div",{staticClass:"register-box flex-col col-center bg-white"},[t("div",{staticClass:"register-title"},[e._v("注册账号")]),e._v(" "),t("el-form",{staticClass:"form-box flex-col"},[t("div",{staticClass:"register-form-item"},[t("el-input",{staticClass:"form-input",attrs:{placeholder:"请输入手机号码"},model:{value:e.telephone,callback:function(t){e.telephone=t},expression:"telephone"}},[t("i",{staticClass:"el-icon-user",staticStyle:{"font-size":"18px"},attrs:{slot:"prepend"},slot:"prepend"})])],1),e._v(" "),e.registerSetting?t("div",{staticClass:"register-form-item flex"},[t("el-input",{staticClass:"form-input",staticStyle:{width:"264px"},attrs:{placeholder:"短信验证码"},model:{value:e.smsCode,callback:function(t){e.smsCode=t},expression:"smsCode"}},[t("i",{staticClass:"el-icon-lock",staticStyle:{"font-size":"18px"},attrs:{slot:"prepend"},slot:"prepend"})]),e._v(" "),t("el-button",{staticClass:"sms-btn",on:{click:e.sendSMSCode}},[e.canSend?t("div",[e._v("获取验证码")]):t("count-down",{attrs:{time:60,format:"ss秒",autoStart:""},on:{finish:function(t){e.canSend=!0}}})],1)],1):e._e(),e._v(" "),t("div",{staticClass:"register-form-item"},[t("el-input",{attrs:{placeholder:"请输入密码 (数字与字母自由组合)","show-password":""},model:{value:e.password,callback:function(t){e.password=t},expression:"password"}},[t("i",{staticClass:"el-icon-more-outline",staticStyle:{"font-size":"18px"},attrs:{slot:"prepend"},slot:"prepend"})])],1),e._v(" "),t("div",{staticClass:"register-form-item"},[t("el-input",{attrs:{placeholder:"再次输入密码","show-password":""},model:{value:e.againPwd,callback:function(t){e.againPwd=t},expression:"againPwd"}},[t("i",{staticClass:"el-icon-key",staticStyle:{"font-size":"18px"},attrs:{slot:"prepend"},slot:"prepend"})])],1),e._v(" "),t("div",{staticClass:"m-t-20"},[t("el-checkbox",{model:{value:e.checked,callback:function(t){e.checked=t},expression:"checked"}},[t("span",{staticClass:"black"},[e._v("已阅读并同意")]),e._v(" "),t("span",{staticClass:"primary",attrs:{to:"/server_explan?type=1"},on:{click:function(t){return e.jumpUrl(1)}}},[e._v("《服务协议》、")]),t("span",{staticClass:"primary",attrs:{to:"/server_explan?type=2"},on:{click:function(t){return e.jumpUrl(2)}}},[e._v("《隐私政策》")])])],1),e._v(" "),t("div",{staticClass:"m-t-20 flex-col"},[t("el-button",{attrs:{type:"primary"},on:{click:e.registerFun}},[e._v("立即注册")])],1),e._v(" "),t("div",{staticClass:"flex row-center",staticStyle:{"margin-top":"36px"}},[t("nuxt-link",{attrs:{to:"/account/login"}},[e._v("已有账号，去登录")])],1)])],1)])}),[],!1,null,"9ce8e830",null);t.default=component.exports;installComponents(component,{CountDown:r(485).default})}}]);