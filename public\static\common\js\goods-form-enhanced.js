/**
 * 商品表单增强功能
 * 统一管理商品添加/编辑页面的所有交互功能
 */

(function($) {
    'use strict';

    var GoodsForm = {
        // 配置选项
        config: {
            imageContainer: '#goodsImageContainer',
            maxImages: 6,
            sortableOptions: {
                animation: 150,
                ghostClass: 'sortable-ghost',
                chosenClass: 'sortable-chosen',
                dragClass: 'sortable-drag',
                handle: '.upload-image-div',
                filter: '.upload-image-elem'
            }
        },

        // 状态管理
        state: {
            isUpdating: false,
            sortableInstance: null,
            observer: null
        },

        // 初始化
        init: function() {
            this.bindEvents();
            this.initImageFeatures();
            this.initFormValidation();
            console.log('商品表单增强功能已初始化');
        },

        // 绑定事件
        bindEvents: function() {
            var self = this;

            // 图片删除事件
            $(document).on('click', self.config.imageContainer + ' .del-upload-btn', function(e) {
                e.preventDefault();
                $(this).parent().remove();
                self.updateMainImage();
                self.updateImageOrder();
                self.updateImageControls();
            });

            // 设置主图事件
            $(document).on('click', self.config.imageContainer + ' .btn-set-main', function(e) {
                e.preventDefault();
                e.stopPropagation();

                var currentDiv = $(this).closest('.upload-image-div');
                var container = $(self.config.imageContainer + ' .like-upload-image');

                // 将当前图片移到第一位
                currentDiv.prependTo(container);

                // 更新相关状态
                self.updateMainImage();
                self.updateImageOrder();
                self.updateImageControls();
            });

            // 表单提交处理
            $(document).on('submit', 'form', function() {
                return self.handleFormSubmit();
            });
        },

        // 初始化图片功能
        initImageFeatures: function() {
            var self = this;
            
            // 延迟初始化，确保DOM完全加载
            setTimeout(function() {
                self.updateImageOrder();
                self.updateImageControls();
                self.initImageSortable();
                self.initDOMObserver();
            }, 500);
        },

        // 初始化拖拽排序
        initImageSortable: function() {
            var self = this;
            var container = document.querySelector(self.config.imageContainer + ' .like-upload-image');
            
            if (!container || typeof Sortable === 'undefined') {
                console.warn('无法初始化图片排序功能');
                return;
            }

            // 销毁已存在的实例
            if (self.state.sortableInstance) {
                self.state.sortableInstance.destroy();
                self.state.sortableInstance = null;
            }

            // 创建新实例
            self.state.sortableInstance = Sortable.create(container, {
                ...self.config.sortableOptions,
                onEnd: function(evt) {
                    self.updateMainImage();
                    self.updateImageOrder();
                    self.updateImageControls();
                }
            });

            console.log('图片拖拽排序已初始化');
        },

        // 初始化DOM观察器
        initDOMObserver: function() {
            var self = this;
            
            if (self.state.observer) {
                self.state.observer.disconnect();
            }

            self.state.observer = new MutationObserver(function(mutations) {
                if (self.state.isUpdating) return;

                var shouldUpdate = false;
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'childList') {
                        var target = $(mutation.target);
                        if (target.hasClass('like-upload-image') && target.closest(self.config.imageContainer).length) {
                            shouldUpdate = true;
                        }
                    }
                });

                if (shouldUpdate) {
                    self.state.isUpdating = true;
                    setTimeout(function() {
                        self.updateImageOrder();
                        self.updateImageControls();
                        self.initImageSortable();
                        self.state.isUpdating = false;
                    }, 200);
                }
            });

            // 开始观察
            var container = document.querySelector(self.config.imageContainer);
            if (container) {
                self.state.observer.observe(container, {
                    childList: true,
                    subtree: true
                });
            }
        },

        // 更新图片序号
        updateImageOrder: function() {
            var self = this;
            $(self.config.imageContainer + ' .upload-image-div').each(function(index) {
                $(this).attr('data-order', index + 1);
            });
        },

        // 更新主图
        updateMainImage: function() {
            var self = this;
            var firstImage = $(self.config.imageContainer + ' .upload-image-div:first input[name="goods_image[]"]').val();
            $('#mainImageInput').val(firstImage || '');
        },

        // 更新图片控制按钮
        updateImageControls: function() {
            var self = this;
            $(self.config.imageContainer + ' .upload-image-div').each(function(index) {
                var $this = $(this);
                var controls = $this.find('.image-controls');

                // 创建控制按钮容器
                if (controls.length === 0) {
                    controls = $('<div class="image-controls"></div>');
                    $this.append(controls);
                }

                // 清空并重新添加按钮
                controls.empty();

                // 非主图显示"设为主图"按钮
                if (index > 0) {
                    controls.append('<button type="button" class="btn-set-main" title="设为主图">主</button>');
                }
            });
        },

        // 初始化表单验证
        initFormValidation: function() {
            // 这里可以添加额外的表单验证逻辑
            console.log('表单验证已初始化');
        },

        // 处理表单提交
        handleFormSubmit: function() {
            var self = this;
            
            // 处理商品图片数据
            var goodsImages = [];
            $(self.config.imageContainer + ' .upload-image-div').each(function() {
                var imageUrl = $(this).find('input[name="goods_image[]"]').val();
                if (imageUrl) {
                    goodsImages.push(imageUrl);
                }
            });

            // 设置主图和轮播图
            if (goodsImages.length > 0) {
                $('#mainImageInput').val(goodsImages[0]);
                
                // 更新表单数据
                var form = $('form')[0];
                if (form) {
                    // 清除原有的goods_image[]字段
                    $('input[name="goods_image[]"]').remove();
                    
                    // 重新添加图片字段
                    goodsImages.forEach(function(imageUrl, index) {
                        $('<input>').attr({
                            type: 'hidden',
                            name: 'goods_image[]',
                            value: imageUrl
                        }).appendTo(form);
                    });
                }
            }

            return true; // 继续提交
        },

        // 销毁功能
        destroy: function() {
            if (this.state.sortableInstance) {
                this.state.sortableInstance.destroy();
                this.state.sortableInstance = null;
            }
            
            if (this.state.observer) {
                this.state.observer.disconnect();
                this.state.observer = null;
            }
            
            console.log('商品表单增强功能已销毁');
        }
    };

    // 自动初始化
    $(document).ready(function() {
        // 延迟初始化，确保其他脚本加载完成
        setTimeout(function() {
            GoodsForm.init();
        }, 1000);
    });

    // 暴露到全局
    window.GoodsForm = GoodsForm;

})(jQuery);
