<?php



namespace app\shop\controller\after_sale;


use app\common\basics\ShopBase;
use app\common\server\JsonServer;
use app\common\model\after_sale\AfterSale as AfterSaleModel;
use app\shop\logic\after_sale\AfterSaleLogic;


/**
 * 售后退款管理
 * Class Goods
 */
class AfterSale extends ShopBase
{
    /**
     * @notes 售后列表
     * @return \think\response\Json|\think\response\View
     * <AUTHOR>
     * @date 2021/7/14 10:12 上午
     */
    public function lists()
    {

        if ($this->request->isAjax()) {
            $get = $this->request->get();
            return JsonServer::success('', AfterSaleLogic::list($get, $this->shop_id));
        }
        $data = AfterSaleLogic::list([], $this->shop_id);
        // 售后状态
        $status = AfterSaleModel::getStatusDesc(true);
        $status = AfterSaleLogic::getStatus($status, $this->shop_id);
        $all = AfterSaleLogic::getAll($this->shop_id);
        return view('', [
            'data' => $data,
            'all' => $all,
            'status' => $status
        ]);
    }

    /**
     * @notes 售后详情
     * @return \think\response\View
     * <AUTHOR>
     * @date 2021/7/14 10:13 上午
     */
    public function detail()
    {

        $id = $this->request->get('id');
        $detail = AfterSaleLogic::getDetail($id,$this->shop_id);
        return view('', [
            'detail' => $detail
        ]);
    }

    /**
     * @notes 同意
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2021/7/14 10:13 上午
     */
    public function agree()
    {

        if ($this->request->isAjax()) {
            $post = $this->request->post();
            $data = AfterSaleLogic::agree($post['id'], $this->shop_id);
            if($data !== false){
                return JsonServer::success('操作成功');
            }else{
                return JsonServer::error('该退款申请已撤销');
            }
        }
    }

    /**
     * @notes 拒绝
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2021/7/14 10:13 上午
     */
    public function refuse()
    {

        if ($this->request->isAjax()) {
            $post = $this->request->post();
            AfterSaleLogic::refuse($post, $this->shop_id);
            return JsonServer::success('操作成功');
        }

    }

    /**
     * @notes 收货
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2021/7/14 10:13 上午
     */
    public function take()
    {

        if ($this->request->isAjax()) {
            $post = $this->request->post();
            AfterSaleLogic::takeGoods($post, $this->admin_id);
            return JsonServer::success('操作成功');
        }
    }

    /**
     * @notes 拒绝收货
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2021/7/14 10:13 上午
     */
    public function refuseGoods()
    {

        if ($this->request->isAjax()) {
            $post = $this->request->post();
            AfterSaleLogic::refuseGoods($post, $this->admin_id);
            return JsonServer::success('操作成功');
        }
    }


    /**
     * @notes 确认退款
     * @return \think\response\Json|\think\response\View
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     * <AUTHOR>
     * @date 2021/7/14 10:13 上午
     */
    public function confirm()
    {
        if ($this->request->isAjax()) {
            $post = $this->request->post();
            $confirm = AfterSaleLogic::confirm($post, $this->admin_id);
            if($confirm !== true){
                return JsonServer::error($confirm,[],0,0);
            }
            return JsonServer::success('操作成功');
        }
        $id = $this->request->get('id');
        $refund_price = $this->request->get('refund_price');
        return view('',['id'=>$id,'refund_price'=>$refund_price]);
    }


    /**
     * @notes 导出Excel
     * @return \think\response\Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date 2022/4/24 10:20
     */
    public function export()
    {
        $params = $this->request->get();
        $result = AfterSaleLogic::list($params, $this->shop_id, true);
        if(false === $result) {
            return JsonServer::error(AfterSaleLogic::getError() ?: '导出失败');
        }
        return JsonServer::success('', $result);
    }
}