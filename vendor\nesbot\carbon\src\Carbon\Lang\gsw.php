<?php

/**
 * This file is part of the Carbon package.
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

/*
 * Authors: <AUTHORS>
 * - <PERSON>
 * - <PERSON>
 * - <PERSON>
 * - <PERSON><PERSON>
 * - <PERSON>
 * - <PERSON><PERSON><PERSON>
 */
return [
    'year' => ':count <PERSON><PERSON>',
    'month' => ':count Monet',
    'week' => ':count Woche',
    'day' => ':count Tag',
    'hour' => ':count Schtund',
    'minute' => ':count Minute',
    'second' => ':count Sekunde',
    'weekdays' => ['Sunntig', 'Mäntig', 'Ziischtig', 'Mittwuch', 'Dunschtig', 'Friitig', 'Samschtig'],
    'weekdays_short' => ['Su', 'Mä', 'Zi', 'Mi', 'Du', 'Fr', 'Sa'],
    'weekdays_min' => ['Su', '<PERSON><PERSON>', 'Zi', 'Mi', '<PERSON>', '<PERSON>', 'Sa'],
    'months' => ['<PERSON><PERSON>r', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 'April', '<PERSON>', 'Jun<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 'September', 'Oktober', 'November', 'Dezember'],
    'months_short' => ['Jan', 'Feb', 'Mär', 'Apr', 'Mai', 'Jun', 'Jul', 'Aug', 'Sep', 'Okt', 'Nov', 'Dez'],
    'meridiem' => ['am Vormittag', 'am Namittag'],
    'ordinal' => ':number.',
    'list' => [', ', ' und '],
    'diff_now' => 'now',
    'diff_yesterday' => 'geschter',
    'diff_tomorrow' => 'moorn',
    'formats' => [
        'LT' => 'HH:mm',
        'LTS' => 'HH:mm:ss',
        'L' => 'DD.MM.YYYY',
        'LL' => 'Do MMMM YYYY',
        'LLL' => 'Do MMMM, HH:mm [Uhr]',
        'LLLL' => 'dddd, Do MMMM YYYY, HH:mm [Uhr]',
    ],
];
