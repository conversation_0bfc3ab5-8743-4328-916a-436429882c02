<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Ccc\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * 单条消息
 *
 * @method string getType() 获取消息类型
 * @method void setType(string $Type) 设置消息类型
 * @method string getContent() 获取消息内容
 * @method void setContent(string $Content) 设置消息内容
 */
class Message extends AbstractModel
{
    /**
     * @var string 消息类型
     */
    public $Type;

    /**
     * @var string 消息内容
     */
    public $Content;

    /**
     * @param string $Type 消息类型
     * @param string $Content 消息内容
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("Type",$param) and $param["Type"] !== null) {
            $this->Type = $param["Type"];
        }

        if (array_key_exists("Content",$param) and $param["Content"] !== null) {
            $this->Content = $param["Content"];
        }
    }
}
