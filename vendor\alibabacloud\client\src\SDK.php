<?php

namespace AlibabaCloud\Client;

/**
 * Class SDK
 *
 * @package AlibabaCloud\Client
 */
class SDK
{
    /**
     * Invalid credential
     */
    const INVALID_CREDENTIAL = 'SDK.InvalidCredential';

    /**
     * Client Not Found
     */
    const CLIENT_NOT_FOUND = 'SDK.ClientNotFound';

    /**
     * Host Not Found
     */
    const HOST_NOT_FOUND = 'SDK.HostNotFound';

    /**
     * Server Unreachable
     */
    const SERVER_UNREACHABLE = 'SDK.ServerUnreachable';

    /**
     * Invalid RegionId
     */
    const INVALID_REGION_ID = 'SDK.InvalidRegionId';

    /**
     * Invalid Argument
     */
    const INVALID_ARGUMENT = 'SDK.InvalidArgument';

    /**
     * Service Not Found
     */

    const SERVICE_NOT_FOUND = 'SDK.ServiceNotFound';

    /**
     * Service Unknown Error
     */
    const SERVICE_UNKNOWN_ERROR = 'SDK.UnknownError';

    /**
     * Response Empty
     */
    const RESPONSE_EMPTY = 'The response is empty';
}
