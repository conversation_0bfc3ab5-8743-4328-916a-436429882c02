<?php
// +----------------------------------------------------------------------
// | MeiliSearch配置
// +----------------------------------------------------------------------

return [
    // MeiliSearch服务器地址
    'host' => env('MEILISEARCH_HOST', 'http://***************:7700'),

    // MeiliSearch API密钥
    'api_key' => env('MEILISEARCH_API_KEY', '27bb9198372f81f8b95fb75d0252912de061fb6fa90d0ad6eb347cc051f0abe3'),

    // 索引配置
    'indexes' => [
        // 商品索引
        'goods' => [
            'searchableAttributes' => [
                'name',
                'remark',
                'split_word',
                'content'
            ],
            'filterableAttributes' => [
                'shop_id',
                'first_cate_id',
                'second_cate_id',
                'third_cate_id',
                'brand_id',
                'status',
                'is_hot',
                'is_recommend'
            ],
            'sortableAttributes' => [
                'min_price',
                'sales_actual',
                'sales_virtual',
                'create_time',
                'update_time'
            ],
            'rankingRules' => [
                'words',
                'typo',
                'proximity',
                'attribute',
                'sort',
                'exactness'
            ]
        ],

        // 搜索候选词索引
        'search_suggestions' => [
            'searchableAttributes' => [
                'text',
                'tags'
            ],
            'sortableAttributes' => [
                'weight',
                'popularity'
            ],
            'rankingRules' => [
                'words',
                'typo',
                'proximity',
                'attribute',
                'sort',
                'exactness'
            ]
        ]
    ]
];
