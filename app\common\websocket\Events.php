<?php
namespace app\common\websocket;

use GatewayWorker\Lib\Gateway;
use think\facade\Log;

/**
 * 所有客户端连接的回调
 * 此处使用的是 GatewayWorker 框架
 */
class Events
{
    /**
     * 当客户端连接时触发
     * @param int $client_id 连接id
     */
    public static function onConnect($client_id)
    {
        // 记录日志
        Log::info("Client [{$client_id}] connected");
        
        // 向当前客户端发送连接成功消息
        Gateway::sendToClient($client_id, json_encode([
            'type' => 'connect',
            'message' => '连接成功',
            'client_id' => $client_id,
            'timestamp' => time()
        ]));
    }
    
    /**
     * 当客户端发来消息时触发
     * @param int $client_id 连接id
     * @param mixed $message 具体消息
     */
    public static function onMessage($client_id, $message)
    {
        // 记录日志
        Log::info("Client [{$client_id}] message: " . $message);
        
        // 解析消息
        $data = json_decode($message, true);
        if (!$data) {
            Gateway::sendToClient($client_id, json_encode([
                'type' => 'error',
                'message' => '消息格式错误',
                'timestamp' => time()
            ]));
            return;
        }
        
        // 根据消息类型处理
        switch ($data['type'] ?? '') {
            case 'admin_panel_online':
                // 管理员上线，加入管理员组
                Gateway::joinGroup($client_id, 'admin_group');
                // Log::info("Admin [{$client_id}] joined admin_group");
                break;
                
            case 'shop_panel_online':
                // 商家上线，加入商家组
                Gateway::joinGroup($client_id, 'shop_group');
                // Log::info("Shop [{$client_id}] joined shop_group");
                break;
                
            case 'shop_online':
                // 商家上线，加入商家组并绑定UID
                Gateway::joinGroup($client_id, 'shop_group');
                if (!empty($data['shop_id'])) {
                    Gateway::bindUid($client_id, 'shop_' . $data['shop_id']);
                    // Log::info("Shop [{$client_id}] bound to shop_" . $data['shop_id']);
                    
                    // 发送绑定成功消息
                    Gateway::sendToClient($client_id, json_encode([
                        'type' => 'bind_success',
                        'message' => '商家绑定成功',
                        'shop_id' => $data['shop_id'],
                        'timestamp' => time()
                    ]));
                }
                break;
                
            case 'user_online':
                // 用户上线，加入用户组
                Gateway::joinGroup($client_id, 'user_group');
                // 如果有用户ID，绑定UID
                if (!empty($data['user_id'])) {
                    Gateway::bindUid($client_id, 'user_' . $data['user_id']);
                    // Log::info("User [{$client_id}] bound to user_" . $data['user_id']);
                }
                // Log::info("User [{$client_id}] joined user_group");
                break;
                
            case 'heartbeat':
                // 心跳包，回复
                Gateway::sendToClient($client_id, json_encode([
                    'type' => 'heartbeat',
                    'timestamp' => time()
                ]));
                break;
                
            default:
                // 未知消息类型
                Gateway::sendToClient($client_id, json_encode([
                    'type' => 'error',
                    'message' => '未知消息类型',
                    'timestamp' => time()
                ]));
                break;
        }
    }
    
    /**
     * 当客户端断开连接时触发
     * @param int $client_id 连接id
     */
    public static function onClose($client_id)
    {
        // 记录日志
        Log::info("Client [{$client_id}] closed");
    }
}
