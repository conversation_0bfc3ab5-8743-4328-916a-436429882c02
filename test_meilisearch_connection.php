<?php
// 简单的MeiliSearch连接测试脚本

$host = 'http://***************:7700';
$apiKey = '27bb9198372f81f8b95fb75d0252912de061fb6fa90d0ad6eb347cc051f0abe3';

echo "测试MeiliSearch连接...\n";
echo "Host: $host\n";
echo "API Key: " . substr($apiKey, 0, 10) . "...\n\n";

// 测试健康检查
$url = $host . '/health';
$headers = [
    'Authorization: Bearer ' . $apiKey,
];

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);
curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 5);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);

if ($error) {
    echo "CURL错误: $error\n";
} else {
    echo "HTTP状态码: $httpCode\n";
    echo "响应内容: $response\n";
    
    if ($httpCode == 200) {
        $data = json_decode($response, true);
        if (isset($data['status']) && $data['status'] === 'available') {
            echo "✓ MeiliSearch连接成功！\n";
        } else {
            echo "✗ MeiliSearch状态异常\n";
        }
    } else {
        echo "✗ MeiliSearch连接失败\n";
    }
}

curl_close($ch);

// 测试获取索引列表
echo "\n测试获取索引列表...\n";
$url = $host . '/indexes';

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);

if ($error) {
    echo "CURL错误: $error\n";
} else {
    echo "HTTP状态码: $httpCode\n";
    echo "索引列表: $response\n";
}

curl_close($ch);
?>
