<?php
declare(strict_types=1);

namespace app\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\facade\Db;

class QueryConfig extends Command
{
    protected function configure()
    {
        $this->setName('query_config')
            ->setDescription('查询配置表信息');
    }

    protected function execute(Input $input, Output $output)
    {
        // 查询表结构
        $output->writeln('配置表结构:');
        try {
            $structure = Db::query('SHOW COLUMNS FROM ls_config');
            foreach ($structure as $field) {
                $output->writeln($field['Field'] . ' - ' . $field['Type'] . ' - ' . $field['Null'] . ' - ' . $field['Key']);
            }
        } catch (\Exception $e) {
            $output->writeln('查询表结构失败: ' . $e->getMessage());
        }

        // 查询百度API配置
        $output->writeln("\n百度API配置:");
        try {
            $baiduConfig = Db::name('config')->where('type', 'baiduapi')->select()->toArray();
            if (empty($baiduConfig)) {
                $output->writeln('未找到百度API配置');
            } else {
                foreach ($baiduConfig as $config) {
                    $output->writeln($config['name'] . ' - ' . $config['value']);
                }
            }
        } catch (\Exception $e) {
            $output->writeln('查询百度API配置失败: ' . $e->getMessage());
        }
    }
}
