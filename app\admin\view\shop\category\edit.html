{layout name="layout2" /}

<div class="layui-card layui-form" style="box-shadow:none;">
    <div class="layui-card-body">
        <div class="layui-form-item">
            <label for="name" class="layui-form-label"><span style="color:red;">*</span>主营类目：</label>
            <div class="layui-input-block">
                <input type="text" name="name" id="name" value="{$detail.name}" lay-verType="tips" lay-verify="required" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"><span style="color:red;">*</span>类目图标：</label>
            <div class="layui-input-block">
                <div class="like-upload-image">
                    {if $detail.image}
                        <div class="upload-image-div">
                            <img src="{$detail.image}" alt="img">
                            <input type="hidden" name="image" value="{$detail.image}">
                            <div class="del-upload-btn">x</div>
                        </div>
                        <div class="upload-image-elem" style="display:none;"><a class="add-upload-image"> + 添加图片</a></div>
                    {else}
                        <div class="upload-image-elem"><a class="add-upload-image"> + 添加图片</a></div>
                    {/if}
                </div>
                <div class="layui-form-mid layui-word-aux">建议尺寸：宽200像素*高200像素的jpg，jpeg，png图片</div>
            </div>
        </div>
        <div class="layui-form-item">
            <label for="sort" class="layui-form-label">排序：</label>
            <div class="layui-input-block">
                <input type="number" name="sort" id="sort" value="{$detail.sort}" autocomplete="off" class="layui-input">
                <div class="layui-form-mid layui-word-aux">排序值必须为整数；数值越大，越靠前</div>
            </div>
        </div>


        <div class="layui-form-item layui-hide">
            <input type="button" lay-submit lay-filter="addSubmit" id="addSubmit" value="确认">
        </div>
    </div>
</div>

<script>
    layui.use([], function () {

        like.delUpload();
        $(document).on("click", ".add-upload-image", function () {
            like.imageUpload({
                limit: 1,
                field: "image",
                that: $(this)
            });
        })

    })
</script>