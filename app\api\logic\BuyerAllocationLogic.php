<?php
namespace app\api\logic;

use app\common\basics\Logic;
use app\common\model\User;
use app\common\model\Shop;
use think\facade\Db;

/**
 * 采购人员分配逻辑层
 * Class BuyerAllocationLogic
 * @package app\api\logic
 */
class BuyerAllocationLogic extends Logic
{
    /**
     * 获取当前商家的采购人员列表
     * @param int $shopId 商家ID
     * @return array
     */
    public static function getBuyerList($shopId)
    {
        $currentYear = date('Y');
        // 获取当前年份的分配记录
        $allocatedBuyers = Db::name('buyer_allocation_record')
            ->where('shop_id', $shopId)
            ->where('allocation_year', $currentYear)
            ->select()
            ->toArray();

        $buyerList = [];
        foreach ($allocatedBuyers as $record) {
            $user = User::find($record['user_id']);
            if ($user) {
                $buyerList[] = [
                    'id' => $user->id,
                    'nickname' => $user->nickname,
                    'avatar' => $user->avatar,
                    'activity_level' => $record['activity_level'],
                    'allocation_time' => $record['create_time']
                ];
            }
        }

        return $buyerList;
    }

    /**
     * 自动分配采购人员给商家
     * @param int $shopId 商家ID
     * @return bool
     */
    public static function autoAllocateBuyers($shopId)
    {
        $shop = Shop::find($shopId);
        if (!$shop) {
            return false;
        }

        $currentYear = date('Y');
        // 检查是否已经分配过
        $existingAllocation = Db::name('buyer_allocation_record')
            ->where('shop_id', $shopId)
            ->where('allocation_year', $currentYear)
            ->find();

        if ($existingAllocation) {
            return false; // 已经分配过，不再重复分配
        }

        // 获取商家等级
        $tierLevel = $shop->tier_level;
        // 获取分配比例配置
        $config = Db::name('buyer_allocation_config')
            ->where('tier_level', $tierLevel)
            ->find();

        if (!$config) {
            return false; // 没有配置，无法分配
        }

        // 计算各个活跃度等级应分配的人数
        $totalBuyers = $config['total_buyers'];
        $level1Count = floor($totalBuyers * $config['level_1_ratio'] / 100);
        $level2Count = floor($totalBuyers * $config['level_2_ratio'] / 100);
        $level3Count = $totalBuyers - $level1Count - $level2Count; // 确保总数一致

        // 获取已经分配给其他商家的采购人员ID
        $allocatedUserIds = Db::name('buyer_allocation_record')
            ->where('allocation_year', $currentYear)
            ->column('user_id');

        // 分配采购人员
        $allocationRecords = [];
        $currentTime = time();

        // 分配1级活跃度用户
        $level1Users = self::getAvailableBuyers(1, $level1Count, $allocatedUserIds);
        foreach ($level1Users as $user) {
            $allocationRecords[] = [
                'shop_id' => $shopId,
                'user_id' => $user['id'],
                'allocation_year' => $currentYear,
                'activity_level' => 1,
                'create_time' => $currentTime
            ];
            $allocatedUserIds[] = $user['id'];
        }

        // 分配2级活跃度用户
        $level2Users = self::getAvailableBuyers(2, $level2Count, $allocatedUserIds);
        foreach ($level2Users as $user) {
            $allocationRecords[] = [
                'shop_id' => $shopId,
                'user_id' => $user['id'],
                'allocation_year' => $currentYear,
                'activity_level' => 2,
                'create_time' => $currentTime
            ];
            $allocatedUserIds[] = $user['id'];
        }

        // 分配3级活跃度用户
        $level3Users = self::getAvailableBuyers(3, $level3Count, $allocatedUserIds);
        foreach ($level3Users as $user) {
            $allocationRecords[] = [
                'shop_id' => $shopId,
                'user_id' => $user['id'],
                'allocation_year' => $currentYear,
                'activity_level' => 3,
                'create_time' => $currentTime
            ];
            $allocatedUserIds[] = $user['id'];
        }

        // 如果分配数量不足，用普通用户补充
        $currentAllocatedCount = count($allocationRecords);
        if ($currentAllocatedCount < $totalBuyers) {
            $remainingCount = $totalBuyers - $currentAllocatedCount;
            $normalUsers = self::getAvailableNormalUsers($remainingCount, $allocatedUserIds);
            foreach ($normalUsers as $user) {
                $allocationRecords[] = [
                    'shop_id' => $shopId,
                    'user_id' => $user['id'],
                    'allocation_year' => $currentYear,
                    'activity_level' => 3, // 默认设置为3级活跃度
                    'create_time' => $currentTime
                ];
            }
        }

        // 保存分配记录
        if (!empty($allocationRecords)) {
            Db::name('buyer_allocation_record')->insertAll($allocationRecords);
        }

        return true;
    }

    /**
     * 获取可用的采购人员
     * @param int $activityLevel 活跃度等级
     * @param int $count 需要获取的数量
     * @param array $excludeIds 排除的用户ID
     * @return array
     */
    private static function getAvailableBuyers($activityLevel, $count, $excludeIds = [])
    {
        $query = User::where('activity_score', '>=', self::getActivityScoreThreshold($activityLevel))
            ->where('is_purchaser', 1)
            ->limit($count);

        if (!empty($excludeIds)) {
            $query->whereNotIn('id', $excludeIds);
        }

        return $query->select()->toArray();
    }

    /**
     * 获取普通用户作为补充
     * @param int $count 需要获取的数量
     * @param array $excludeIds 排除的用户ID
     * @return array
     */
    private static function getAvailableNormalUsers($count, $excludeIds = [])
    {
        $query = User::limit($count);

        if (!empty($excludeIds)) {
            $query->whereNotIn('id', $excludeIds);
        }

        return $query->select()->toArray();
    }

    /**
     * 获取活跃度等级的积分阈值
     * @param int $activityLevel 活跃度等级
     * @return int
     */
    private static function getActivityScoreThreshold($activityLevel)
    {
        // 这里可以根据实际需求调整积分阈值
        switch ($activityLevel) {
            case 1:
                return 500; // 1级活跃度需要的积分
            case 2:
                return 300; // 2级活跃度需要的积分
            case 3:
            default:
                return 100; // 3级活跃度需要的积分
        }
    }
}
