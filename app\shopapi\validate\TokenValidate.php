<?php


namespace app\shopapi\validate;


use app\common\basics\Validate;
use app\common\model\shop\ShopAdmin;
use app\common\model\ShopSession as SessionModel;

/**
 * 商家移动端管理员登录token验证
 * Class TokenValidate
 * @package app\shopapi\validate
 */
class TokenValidate extends Validate
{
    protected $rule = [
        'token' => 'require|valid|admin',
    ];

    /**
     * User: 意象信息科技 lr
     * Desc: token验证
     * @param $token
     * @param $other
     * @param $data
     * @return bool|string
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    protected function valid($token, $other, $data)
    {
        $session = SessionModel::where(['token' => $token])->find();

        if (empty($session)) {
            return '会话失效，请重新登录';
        }
        if ($session['expire_time'] <= time()) {
            return '登录超时，请重新登录';
        }
        return true;
    }

    /**
     * User: 意象信息科技 lr
     * Desc 用户验证
     * @param $token
     * @param $other
     * @param $data
     * @return string
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    protected function admin($token, $other, $data)
    {
        $admin_id = SessionModel::where(['token' => $token])
            ->value('admin_id');

        $admin_info = ShopAdmin::where(['id' => $admin_id, 'del' => 0])
            ->find();
        if (empty($admin_info)) {
            return '用户不存在';
        }
        if ($admin_info['disable'] == 1) {
            return '用户被禁用';
        }
        return true;
    }


}