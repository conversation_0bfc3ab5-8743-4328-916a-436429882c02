{layout name="layout2" /}
<style>
    .layui-form-label{
        width: 120px;
    }
    .layui-card .layui-tab{
        margin-left: 7px;
    }
    .layui-form-label{
        margin-left: 20px;
        width: 98px;
    }
    .layui-input-inline{
        width:160px;
    }
    .layui-table-cell {
        height: auto;
    }

</style>
<div class="layui-form" lay-filter="layuiadmin-form-user_group" id="layuiadmin-form-user_group" style="padding: 20px 30px 0 0;">
    <div class="layui-form-item div-flex">
        <fieldset class="layui-elem-field layui-field-title">
            <legend>广告信息</legend>
        </fieldset>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">广告位名称：</label>
        <div class="layui-input-inline">
            <label class="layui-form-mid">{$detail.name}</label>
        </div>
        <label class="layui-form-label">广告编号：</label>
        <div class="layui-input-inline">
            <label class="layui-form-mid">{$detail.ad_sn}-{$detail.ad_yn}</label>
        </div>

    </div>
    <div class="layui-form-item">
    <label class="layui-form-label">广告费用：</label>
    <div class="layui-input-inline">
        <input type="hidden" id="ad_fee" value="{$detail.ad_fee}">
        <label class="layui-form-mid" >{$detail.ad_fee}元</label>
    </div>
    <label class="layui-form-label">广告周期：</label>
    <div class="layui-input-inline">
        <input type="hidden" id="cycle" value="{$cycle[$detail.billing_cycle]}">
        <label class="layui-form-mid">一{$cycle[$detail.billing_cycle]}起</label>
    </div>
    </div>
    <div class="layui-form-item">

        <div class="layui-form-item" style="margin-bottom: 0;">

            <label for="buy_time" class="layui-form-label"><font color="red">*</font>购买时长：</label>
            <div class="layui-input-inline">
                <input type="number" min="1" id="buy_time" name="buy_time"
                       class="layui-input" autocomplete="off" value="1"
                       onkeyup="calculate()"
                       lay-verType="tips" lay-verify="required|number|people_num">
                <div class="layui-form-mid layui-word-aux" style="white-space:nowrap;">最少为1{$cycle[$detail.billing_cycle]},从付款日开始计算</div>
            </div>
            <div class="layui-form-mid">{$cycle[$detail.billing_cycle]}</div>
        </div>
        <label for="ad_buynums" class="layui-form-label"><font color="red">*</font>购买数量：</label>
        <div class="layui-input-inline">
            <input type="number" min="1" max="{$detail.ad_nums-$detail.ad_buys}" id="ad_buynums" name="ad_buynums"
                   class="layui-input" autocomplete="off" value="1"
                   oninput="restoreDefaultIfExceedsMax(this, 1)"
                   lay-verType="tips" lay-verify="required|number|ad_buynums">
            <div class="layui-form-mid layui-word-aux" style="white-space:nowrap;">最少为1个,最多{$detail.ad_nums-$detail.ad_buys}个</div>

        </div>
        <div class="layui-form-mid">个</div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">费用合计：</label>
        <div class="layui-input-inline">
            <label class="layui-form-mid" id="ad_total">0.00元</label>
        </div>
<!--        <label class="layui-form-label">广告失效期：</label>-->
<!--        <div class="layui-input-inline">-->
<!--            <label class="layui-form-mid" id="end_time"></label>-->
<!--        </div>-->
    </div>
    <div class="layui-form-item layui-hide">
        <input type="button" lay-submit lay-filter="edit-submit" id="edit-submit" value="确认">
    </div>
</div>
<script>
    // 假设广告费用、购买数量和购买时长已通过某种方式获取到
    var adFee = parseFloat(document.getElementById('ad_fee').value); // 获取广告费用，并转换为浮点数
    function restoreDefaultIfExceedsMax(inputElement, defaultValue) {
        var maxValue = parseInt(inputElement.getAttribute('max'));
        if (inputElement.value > maxValue) {
            inputElement.value = defaultValue;
        }
    }
    // 计算函数
    function calculate() {
        var adBuynums = parseInt(document.getElementById('ad_buynums').value); // 获取购买数量
        var buyTime = parseInt(document.getElementById('buy_time').value); // 获取购买时长
        var billingCycle =parseInt(document.getElementById('cycle').value); // 获取广告周期类型

        // 计算广告总费用
        var totalFee = adFee * adBuynums * buyTime;
        document.getElementById('ad_total').innerText = totalFee.toFixed(2) + '元'; // 显示总费用，保留两位小数
    }

    // 初始调用一次计算函数以显示初始值
    calculate();
</script>