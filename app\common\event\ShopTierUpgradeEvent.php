<?php
namespace app\common\event;

/**
 * 商家等级升级事件
 * Class ShopTierUpgradeEvent
 * @package app\common\event
 */
class ShopTierUpgradeEvent
{
    public $shopId;
    public $newTierLevel;
    public $oldTierLevel;
    public $upgradeTime;
    
    public function __construct($shopId, $newTierLevel, $oldTierLevel = null)
    {
        $this->shopId = $shopId;
        $this->newTierLevel = $newTierLevel;
        $this->oldTierLevel = $oldTierLevel;
        $this->upgradeTime = time();
    }
    
    /**
     * 获取事件数据
     * @return array
     */
    public function getData()
    {
        return [
            'shop_id' => $this->shopId,
            'new_tier_level' => $this->newTierLevel,
            'old_tier_level' => $this->oldTierLevel,
            'upgrade_time' => $this->upgradeTime
        ];
    }
}
