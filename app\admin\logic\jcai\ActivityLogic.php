<?php


namespace app\admin\logic\jcai;


use app\common\basics\Logic;
use app\common\enum\OrderEnum;
use app\common\enum\OrderLogEnum;
use app\common\enum\JcaiEnum;
use app\common\logic\OrderRefundLogic;
use app\common\model\order\Order;
use app\common\model\jcai\JcaiActivity;
use app\common\model\jcai\JcaiFound;
use app\common\model\jcai\JcaiJoin;
use app\common\server\UrlServer;
use Exception;
use think\facade\Db;

class ActivityLogic extends Logic
{
    /**
     * @Notes: 获取拼团活动
     * @Author: 张无忌
     * @param $get
     * @return array|bool
     */
    public static function lists($get)
    {
        try {
            $where = [];
            $where[] = ['T.del', '=', 0];
            if (!empty($get['datetime']) and $get['datetime']) {
                list($start, $end) = explode(' - ', $get['datetime']);
                $where[] = ['T.create_time', '>=', strtotime($start.' 00:00:00')];
                $where[] = ['T.create_time', '<=', strtotime($end.' 23:59:59')];
            }

            if (!empty($get['shop']) and $get['shop']) {
                $where[] = ['S.name|S.id', 'like', '%'.$get['shop'].'%'];
            }

            if (!empty($get['name']) and $get['name']) {
                $where[] = ['G.name', 'like', '%'.$get['name'].'%'];
            }

            if (!empty($get['status']) and $get['status']) {
                $where[] = ['T.status', '=', $get['status']];
            }

            if (!empty($get['type']) and $get['type']) {
                $where[] = ['T.audit', '=', $get['type']-1];
            }

            $model = new JcaiActivity();
            $lists = $model->alias('T')->field(['T.*', 'S.name as shop_name,S.type as shop_type,S.logo'])
                ->where($where)
                ->with(['goods'])
                ->join('goods G', 'G.id = T.goods_id')
                ->join('shop S', 'S.id = T.shop_id')
                ->paginate([
                    'page' => $get['page'] ?? 1,
                    'list_rows' => $get['limit'] ?? 20,
                    'var_page' => 'page'
                ])->toArray();

            $teamFoundModel = new JcaiFound();
            $teamJoinModel =  new JcaiJoin();

            foreach ($lists['data'] as &$item) {
                $item['activity_start_time'] = date('Y-m-d H:i', $item['activity_start_time']);
                $item['activity_end_time'] = date('Y-m-d H:i', $item['activity_end_time']);
                $item['status_text'] = JcaiEnum::getTeamStatusDesc($item['status']);
                $item['audit_text'] = JcaiEnum::getTeamAuditDesc($item['audit']);
                $item['logo'] = UrlServer::getFileUrl($item['logo']);
                $item['shop_type'] = $item['shop_type'] == 1 ? '商家自营' : '入驻商家';
                $item['team_count'] = $teamFoundModel->where(['jcai_activity_id'=>$item['id']])->count();
                $item['success_found'] = $teamFoundModel->where(['status'=>1, 'jcai_activity_id'=>$item['id']])->count();
                $item['join_found'] = $teamJoinModel->where(['jcai_activity_id'=>$item['id']])->count();
            }
            return ['count'=>$lists['total'], 'lists'=>$lists['data']];
        } catch (Exception $e) {
            static::$error = $e->getMessage();

            return false;
        }
    }


    /**
     * @notes 集采众筹商品的订单记录
     * @param $get
     * @return array|bool
     * <AUTHOR>
     * @date 2021/7/19 11:02
     */
    public static function record($get)
    {
        try {
            $where = [];
            $where[] = ['TJ.jcai_activity_id', '=', (int)$get['jcai_activity_id']];

            if (isset($get['type']) and is_numeric($get['type']) and $get['type'] != 100) {
                $where[] = ['TJ.status', '=', (int)$get['type']];
            }

            if (!empty($get['order_sn']) and $get['order_sn']) {
                $where[] = ['O.order_sn', 'like', '%'.$get['order_sn'].'%'];
            }

            if (!empty($get['nickname']) and $get['nickname']) {
                $where[] = ['U.nickname', 'like', '%'.$get['nickname'].'%'];
            }

            if (!empty($get['datetime']) and $get['datetime']) {
                list($start, $end) = explode(' - ', $get['datetime']);
                $where[] = ['O.pay_time', '>=', strtotime($start.' 00:00:00')];
                $where[] = ['O.pay_time', '<=', strtotime($end.' 23:59:59')];
            }

            $model = new JcaiJoin();
            $lists = $model->alias('TJ')
                ->field([
                    'TJ.*',
                    'U.nickname', 'U.sn', 'U.avatar',
                    'O.order_sn', 'O.order_amount', 'O.pay_time', 'O.shipping_status', 'O.order_status',
                    'OG.goods_num', 'OG.goods_name', 'OG.image', 'OG.spec_value'
                ])
                ->join('user U', 'U.id = TJ.user_id')
                ->join('order O', 'O.id = TJ.order_id')
                ->join('order_goods OG', 'OG.order_id = O.id')
                ->where($where)
                ->where('O.pay_status', '=', 1) // 只查询已支付的订单
                ->order('O.pay_time desc')
                ->paginate([
                    'page'      => $get['page'] ?? 1,
                    'list_rows' => $get['limit'] ?? 20,
                    'var_page'  => 'page'
                ])->toArray();

            foreach ($lists['data'] as &$item) {
                // 安全地格式化时间
                $item['pay_time'] = !empty($item['pay_time']) && is_numeric($item['pay_time']) ?
                    date('Y-m-d H:i:s', (int)$item['pay_time']) : '';

                $item['create_time'] = !empty($item['create_time']) && is_numeric($item['create_time']) ?
                    date('Y-m-d H:i:s', (int)$item['create_time']) : '';

                $item['status_text'] = JcaiEnum::getStatusDesc($item['status']);
                $item['shipping_status_text'] = $item['shipping_status'] == 1 ? '已发货' : '未发货';
                $item['order_status_text'] = OrderEnum::getOrderStatus($item['order_status']);

                // 处理头像URL
                $item['avatar'] = !empty($item['avatar']) ? UrlServer::getFileUrl($item['avatar']) : '';

                // 处理商品图片URL
                $item['image'] = !empty($item['image']) ? UrlServer::getFileUrl($item['image']) : '';

                // 解析团购快照数据
                if (!empty($item['team_snap'])) {
                    $item['team_snap'] = json_decode($item['team_snap'], true);
                }
            }

            return ['count'=>$lists['total'], 'lists'=>$lists['data']];
        } catch (Exception $e) {
            static::$error = $e->getMessage();
            return false;
        }
    }

    /**
     * @notes 集采众筹订单记录统计
     * @param $get
     * @return mixed
     * <AUTHOR>
     * @date 2021/7/19 11:05
     */
    public static function recordStatistics($get)
    {
        $where[] = ['TJ.jcai_activity_id', '=', (int)$get['id']];

        $model = new JcaiJoin();
        $detail['total'] = $model->alias('TJ')
            ->join('order O', 'O.id = TJ.order_id')
            ->where($where)
            ->where('O.pay_status', '=', 1) // 只统计已支付的订单
            ->count();

        $detail['stayStatus'] = $model->alias('TJ')
            ->join('order O', 'O.id = TJ.order_id')
            ->where($where)
            ->where('O.pay_status', '=', 1)
            ->where('TJ.status', '=', 0)
            ->count();

        $detail['successStatus'] = $model->alias('TJ')
            ->join('order O', 'O.id = TJ.order_id')
            ->where($where)
            ->where('O.pay_status', '=', 1)
            ->where('TJ.status', '=', 1)
            ->count();

        $detail['failStatus'] = $model->alias('TJ')
            ->join('order O', 'O.id = TJ.order_id')
            ->where($where)
            ->where('O.pay_status', '=', 1)
            ->where('TJ.status', '=', 2)
            ->count();

        return $detail;
    }


    /**
     * @Notes: 数据统计
     * @Author: 张无忌
     * @return mixed
     */
    public static function statistics()
    {
        $where[] = ['del', '=', 0];

        $model = new JcaiActivity();
        $detail['total']       = $model->where($where)->count();
        $detail['stayAudit']   = $model->where($where)->where(['audit'=>0])->count();
        $detail['adoptAudit']  = $model->where($where)->where(['audit'=>1])->count();
        $detail['refuseAudit'] = $model->where($where)->where(['audit'=>2])->count();
        return $detail;
    }


    /**
     * @Notes: 审核拼团活动
     * @Author: 张无忌
     * @param $post
     * @return bool
     */
    public static function audit($post)
    {
        try {
            if (!$post['audit'] and empty($post['explain'])) {
                throw new \think\Exception('拒绝时请填写拒绝理由');
            }

            JcaiActivity::update([
                'audit' => $post['audit'],
                'update_time' => time()
            ], ['id'=>$post['id']]);

            return true;
        } catch (\Exception $e) {
            static::$error = $e->getMessage();
            return false;
        }
    }

    /**
     * @Notes: 违规重审核
     * @Author: 张无忌
     * @param $id
     * @return bool
     */
    public static function violation($id)
    {
        try {
            JcaiActivity::update([
                'audit'  => 2,
                'status' => 0,
                'update_time' => time()
            ], ['id' => $id]);

            $team_ids = (new JcaiFound())->where(['jcai_activity_id' => $id, 'status' => 0])->column('id');

            $teamJoin = (new JcaiJoin())->alias('TJ')
                ->field(['TJ.*,O.order_sn,O.order_status,O.pay_status,O.refund_status,O.order_amount'])
                ->where('team_id', 'in', $team_ids)
                ->join('order O', 'O.id=TJ.order_id')
                ->select()->toArray();

            self::teamFail($teamJoin, $team_ids, time());

            return true;
        } catch (\Exception $e) {
            static::$error = $e->getMessage();
            return false;
        }
    }

    /**
     * @Notes: 拼团活动详细
     * @Author: 张无忌
     * @param $id
     * @return array
     */
    public static function detail($id)
    {
        $model = new JcaiActivity();
        $detail = $model->field(true)
            ->with(['goods', 'teamGoods'])
            ->findOrEmpty($id)
            ->toArray();

        $detail['activity_start_time'] = !empty($detail['activity_start_time']) && is_numeric($detail['activity_start_time']) ?
            date('Y-m-d H:i:s', (int)$detail['activity_start_time']) : '';
        $detail['activity_end_time'] = !empty($detail['activity_end_time']) && is_numeric($detail['activity_end_time']) ?
            date('Y-m-d H:i:s', (int)$detail['activity_end_time']) : '';
        return $detail;
    }

    /**
     * @Notes: 拼团失败
     * @Author: 张无忌
     * @param $teamJoin (参团列表数据)
     * @param $found_ids
     * @param $time (时间)
     * @throws \think\Exception
     */
    private static function teamFail($teamJoin, $found_ids, $time)
    {
        Db::startTrans();
        try {
            (new JcaiFound())->whereIn('id', $found_ids)
                ->update(['status'=>JcaiEnum::TEAM_STATUS_FAIL, 'team_end_time'=>$time]);

            foreach ($teamJoin as $item) {
                JcaiJoin::update(['status' => JcaiEnum::TEAM_STATUS_FAIL, 'update_time' => $time], ['id' => $item['id']]);
                if ($item['order_status'] == OrderEnum::ORDER_STATUS_DOWN) continue;
                if ($item['refund_status'] != OrderEnum::REFUND_STATUS_NO_REFUND) continue;
                $order = (new Order())->findOrEmpty($item['order_id'])->toArray();
                // 取消订单
                OrderRefundLogic::cancelOrder($order['id'], OrderLogEnum::TYPE_SYSTEM);
                if ($order['pay_status'] == OrderEnum::PAY_STATUS_PAID) {
                    // 更新订单状态
                    OrderRefundLogic::cancelOrderRefundUpdate($order);
                    // 订单退款
                    OrderRefundLogic::refund($order, $order['order_amount'], $order['order_amount']);
                }
            }
            Db::commit();
        } catch (Exception $e) {
            Db::rollback();
            throw new \think\Exception($e->getMessage());
        }
    }


    public static function pdjcailist($get){
        try {
            $where = [];
            $where[] = ['T.del', '=', 0];
            if (!empty($get['datetime']) and $get['datetime']) {
                list($start, $end) = explode(' - ', $get['datetime']);
                $where[] = ['T.create_time', '>=', strtotime($start.' 00:00:00')];
                $where[] = ['T.create_time', '<=', strtotime($end.' 23:59:59')];
            }

            if (!empty($get['shop']) and $get['shop']) {
                $where[] = ['S.name|S.id', 'like', '%'.$get['shop'].'%'];
            }

            if (!empty($get['name']) and $get['name']) {
                $where[] = ['G.name', 'like', '%'.$get['name'].'%'];
            }

            if (!empty($get['status']) and $get['status']) {
                $where[] = ['T.status', '=', $get['status']];
            }

            if (!empty($get['type']) and $get['type']) {
                $where[] = ['T.audit', '=', $get['type']-1];
            }

            $model = new JcaiActivity();
            $lists = $model->alias('T')->field(['T.*', 'S.name as shop_name,S.type as shop_type,S.logo'])
                ->where($where)
                ->with(['goods'])
                ->join('goods G', 'G.id = T.goods_id')
                ->join('shop S', 'S.id = T.shop_id')
                ->paginate([
                    'page' => $get['page'] ?? 1,
                    'list_rows' => $get['limit'] ?? 20,
                    'var_page' => 'page'
                ])->toArray();

            $teamFoundModel = new JcaiFound();
            $teamJoinModel =  new JcaiJoin();

            foreach ($lists['data'] as &$item) {
                $item['activity_start_time'] = !empty($item['activity_start_time']) && is_numeric($item['activity_start_time']) ?
                    date('Y-m-d H:i', (int)$item['activity_start_time']) : '';
                $item['activity_end_time'] = !empty($item['activity_end_time']) && is_numeric($item['activity_end_time']) ?
                    date('Y-m-d H:i', (int)$item['activity_end_time']) : '';
                $item['status_text'] = JcaiEnum::getTeamStatusDesc($item['status']);
                $item['audit_text'] = JcaiEnum::getTeamAuditDesc($item['audit']);
                $item['logo'] = UrlServer::getFileUrl($item['logo']);
                $item['shop_type'] = $item['shop_type'] == 1 ? '商家自营' : '入驻商家';
                $item['team_count'] = $teamFoundModel->where(['jcai_activity_id'=>$item['id']])->count();
                $item['success_found'] = $teamFoundModel->where(['status'=>1, 'jcai_activity_id'=>$item['id']])->count();
                $item['join_found'] = $teamJoinModel->where(['jcai_activity_id'=>$item['id']])->count();
            }
            return ['count'=>$lists['total'], 'lists'=>$lists['data']];
        } catch (Exception $e) {
            static::$error = $e->getMessage();

            return false;
        }
    }
}