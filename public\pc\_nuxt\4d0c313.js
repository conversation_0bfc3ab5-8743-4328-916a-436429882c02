(window.webpackJsonp=window.webpackJsonp||[]).push([[25,10],{473:function(e,t,o){"use strict";var n=o(14),r=o(4),l=o(5),c=o(141),f=o(24),d=o(18),m=o(290),h=o(54),v=o(104),x=o(289),w=o(3),y=o(105).f,S=o(45).f,k=o(23).f,C=o(474),_=o(475).trim,O="Number",N=r.Number,T=N.prototype,I=r.TypeError,E=l("".slice),L=l("".charCodeAt),R=function(e){var t=x(e,"number");return"bigint"==typeof t?t:M(t)},M=function(e){var t,o,n,r,l,c,f,code,d=x(e,"number");if(v(d))throw I("Cannot convert a Symbol value to a number");if("string"==typeof d&&d.length>2)if(d=_(d),43===(t=L(d,0))||45===t){if(88===(o=L(d,2))||120===o)return NaN}else if(48===t){switch(L(d,1)){case 66:case 98:n=2,r=49;break;case 79:case 111:n=8,r=55;break;default:return+d}for(c=(l=E(d,2)).length,f=0;f<c;f++)if((code=L(l,f))<48||code>r)return NaN;return parseInt(l,n)}return+d};if(c(O,!N(" 0o1")||!N("0b1")||N("+0x1"))){for(var j,A=function(e){var t=arguments.length<1?0:N(R(e)),o=this;return h(T,o)&&w((function(){C(o)}))?m(Object(t),o,A):t},D=n?y(N):"MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,EPSILON,MAX_SAFE_INTEGER,MIN_SAFE_INTEGER,isFinite,isInteger,isNaN,isSafeInteger,parseFloat,parseInt,fromString,range".split(","),$=0;D.length>$;$++)d(N,j=D[$])&&!d(A,j)&&k(A,j,S(N,j));A.prototype=T,T.constructor=A,f(r,O,A,{constructor:!0})}},474:function(e,t,o){var n=o(5);e.exports=n(1..valueOf)},475:function(e,t,o){var n=o(5),r=o(36),l=o(19),c=o(476),f=n("".replace),d="["+c+"]",m=RegExp("^"+d+d+"*"),h=RegExp(d+d+"*$"),v=function(e){return function(t){var o=l(r(t));return 1&e&&(o=f(o,m,"")),2&e&&(o=f(o,h,"")),o}};e.exports={start:v(1),end:v(2),trim:v(3)}},476:function(e,t){e.exports="\t\n\v\f\r                　\u2028\u2029\ufeff"},485:function(e,t,o){"use strict";o.r(t);o(473),o(86),o(62),o(12),o(107),o(40),o(106);var n=6e4,r=36e5,l=24*r;function c(e){return(0+e.toString()).slice(-2)}var f={components:{},props:{isSlot:{type:Boolean,default:!1},time:{type:Number,default:0},format:{type:String,default:"hh:mm:ss"},autoStart:{type:Boolean,default:!0}},watch:{time:{immediate:!0,handler:function(e){e&&this.reset()}}},data:function(){return{timeObj:{},formateTime:0}},created:function(){},computed:{},methods:{createTimer:function(e){return setTimeout(e,100)},isSameSecond:function(e,t){return Math.floor(e)===Math.floor(t)},start:function(){this.counting||(this.counting=!0,this.endTime=Date.now()+1e3*this.remain,this.setTimer())},setTimer:function(){var e=this;this.tid=this.createTimer((function(){var t=e.getRemain();e.isSameSecond(t,e.remain)&&0!==t||e.setRemain(t),0!==e.remain&&e.setTimer()}))},getRemain:function(){return Math.max(this.endTime-Date.now(),0)},pause:function(){this.counting=!1,clearTimeout(this.tid)},reset:function(){this.pause(),this.remain=this.time,this.setRemain(this.remain),this.autoStart&&this.start()},setRemain:function(e){var t=this.format;this.remain=e;var time,o=(time=e,{days:Math.floor(time/l),hours:c(Math.floor(time%l/r)),minutes:c(Math.floor(time%r/n)),seconds:c(Math.floor(time%n/1e3))});this.formateTime=function(e,t){var o=t.days,n=t.hours,r=t.minutes,l=t.seconds;return-1!==e.indexOf("dd")&&(e=e.replace("dd",o)),-1!==e.indexOf("hh")&&(e=e.replace("hh",c(n))),-1!==e.indexOf("mm")&&(e=e.replace("mm",c(r))),-1!==e.indexOf("ss")&&(e=e.replace("ss",c(l))),e}(t,o),this.$emit("change",o),0===e&&(this.pause(),this.$emit("finish"))}}},d=o(8),component=Object(d.a)(f,(function(){var e=this,t=e._self._c;return e.time>=0?t("div",[t("client-only",[e.isSlot?e._t("default"):t("span",[e._v(e._s(e.formateTime))])],2)],1):e._e()}),[],!1,null,null,null);t.default=component.exports},493:function(e,t,o){"use strict";o.d(t,"d",(function(){return n})),o.d(t,"e",(function(){return r})),o.d(t,"c",(function(){return l})),o.d(t,"b",(function(){return c})),o.d(t,"a",(function(){return f}));var n=5,r={SMS:0,ACCOUNT:1},l={REGISTER:"ZCYZ",FINDPWD:"ZHMM",LOGIN:"YZMDL",SJSQYZ:"SJSQYZ",CHANGE_MOBILE:"BGSJHM",BIND:"BDSJHM"},c={NONE:"",SEX:"sex",NICKNAME:"nickname",AVATAR:"avatar",MOBILE:"mobile"},f={NORMAL:"normal",HANDLING:"apply",FINISH:"finish"}},577:function(e,t,o){var content=o(642);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[e.i,content,""]]),content.locals&&(e.exports=content.locals);(0,o(17).default)("024844cf",content,!0,{sourceMap:!1})},641:function(e,t,o){"use strict";o(577)},642:function(e,t,o){var n=o(16)(!1);n.push([e.i,'.login[data-v-6ee76944]{flex:1;background-size:cover;background-repeat:no-repeat;background-position:50%;min-width:1180px}.login .login-container[data-v-6ee76944]{margin:0 auto;width:1180px;height:100%;position:relative}.login .login-container .login-banner[data-v-6ee76944]{display:flex;align-items:center;justify-content:center;width:750px;margin-right:30px;height:440px;overflow:hidden;animation:loadimg-6ee76944 2s infinite;transition:background-color 2s}@keyframes loadimg-6ee76944{0%{background-color:#e4e4e4}50%{background-color:#f0f0f0}to{background-color:#e4e4e4}}.login .login-container .login-float-form-wrap[data-v-6ee76944]{width:400px;height:440px}.login .login-container .login-float-form-wrap .login-box[data-v-6ee76944]{background-color:#fff;height:100%;display:flex;flex-direction:column;justify-content:space-between}.login .login-container .login-float-form-wrap .login-box .login-header-box[data-v-6ee76944]{padding-top:20px}.login .login-container .login-float-form-wrap .login-box .login-header-box .header-tabs .header-tab[data-v-6ee76944]{width:160px;height:35px;display:flex;flex-direction:column;align-items:center;cursor:pointer}.login .login-container .login-float-form-wrap .login-box .login-header-box .header-tabs .active-tab[data-v-6ee76944]{color:#ff2c3c;text-align:center}.login .login-container .login-float-form-wrap .login-box .login-header-box .header-tabs .active-tab[data-v-6ee76944]:after{content:"";height:2px;width:72px;margin-top:8px;background-color:#ff2c3c}.login .login-container .login-float-form-wrap .login-box .login-header-box .login-form-box[data-v-6ee76944]{padding:0 30px}.login .login-container .login-float-form-wrap .login-box .login-header-box .login-form-box .login-form-item[data-v-6ee76944]{margin-top:24px}.login .login-container .login-float-form-wrap .login-box .login-header-box .login-form-box .login-form-item .input-phone-num[data-v-6ee76944]{width:340px}.login .login-container .login-float-form-wrap .login-box .login-header-box .login-form-box .login-form-item .verify-code-img[data-v-6ee76944]{width:100px;height:40px;margin-left:20px;background-color:red}.login .login-container .login-float-form-wrap .login-box .login-header-box .login-form-box .login-form-item .sms-btn[data-v-6ee76944]{margin-left:20px;height:40px}.login .login-container .login-float-form-wrap .login-box .login-header-box .option-box[data-v-6ee76944]{padding:0 30px;margin-top:60px}.login .login-container .login-float-form-wrap .login-box .login-header-box .option-box[data-v-6ee76944] .el-checkbox{color:#888}.login .login-container .login-float-form-wrap .login-box .login-footer-box[data-v-6ee76944]{height:50px;padding:15px}.login .login-container .login-float-form-wrap .login-box .login-footer-box .login__other-item[data-v-6ee76944]{cursor:pointer}.login .login-container .login-float-form-wrap .login-box .login-footer-box .login__weixin-icon[data-v-6ee76944]{width:1.5em;height:1.5em;text-align:center;line-height:1.5em;border-radius:50%;background-color:#0abd5d;color:#fff}',""]),e.exports=n},711:function(e,t,o){"use strict";o.r(t);o(26),o(20),o(25),o(12),o(30),o(21),o(31);var n=o(9),r=o(10),l=(o(53),o(40),o(106),o(493)),c=o(81),f=o.n(c),d=o(13);function m(object,e){var t=Object.keys(object);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(object);e&&(o=o.filter((function(e){return Object.getOwnPropertyDescriptor(object,e).enumerable}))),t.push.apply(t,o)}return t}function h(e){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?m(Object(source),!0).forEach((function(t){Object(r.a)(e,t,source[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(source)):m(Object(source)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(source,t))}))}return e}var v={head:function(){return{title:this.$store.getters.headTitle,link:[{rel:"icon",type:"image/x-icon",href:this.$store.getters.favicon}]}},layout:"main",components:{CountDown:o(485).default},data:function(){return{selectNumberType:"中国+86",account:"",password:"",telephone:"",verifyCode:"",smsCode:"",isRemember:!0,loginStatus:l.e.SMS,canSend:!0}},computed:h({},Object(d.d)(["config"])),methods:h(h(h({},Object(d.c)(["setToken"])),Object(d.b)(["getPublicData"])),{},{changeLoginType:function(e){this.loginStatus=e,this.telephone="",this.verifyCode="",this.smsCode="";var t=JSON.parse(localStorage.getItem("ACCOUNT")),o=JSON.parse(localStorage.getItem("TEL"));t&&t.account&&(this.account=t.account),o&&o.telephone&&(this.telephone=o.telephone)},goWechatLogin:function(){var e=this;this.$get("account/scanCode").then((function(e){var code=e.code,t=e.msg,data=e.data;if(1!==code)throw new Error(t);window.open(data.url,"_self")})).catch((function(t){e.$message.error(t.message)}))},handleWechatLogin:function(e){var t=this;this.$post("account/scanLogin",e).then((function(e){var code=e.code,o=e.msg,data=e.data;if(1!==code)throw new Error(o);f.a.set("token",data.token,{expires:60}),t.setToken(data.token),t.$router.replace({path:f.a.get("back_url")||"/"}),f.a.remove("back_url"),t.getPublicData()})).catch((function(e){t.$message.error(e.message)}))},sendSMSCode:function(){var e=this;return Object(n.a)(regeneratorRuntime.mark((function t(){var o;return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(e.canSend){t.next=2;break}return t.abrupt("return");case 2:return t.next=4,e.$post("sms/send",{mobile:e.telephone,key:l.c.LOGIN,client:l.d});case 4:1==(o=t.sent).code&&(e.$message({message:o.msg,type:"success"}),e.canSend=!1);case 6:case"end":return t.stop()}}),t)})))()},smsLogin:function(){var e=this;return Object(n.a)(regeneratorRuntime.mark((function t(){var o,n;return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,e.$post("account/smsLogin",{mobile:e.telephone,code:e.smsCode,client:l.d});case 2:1==(o=t.sent).code&&(n=o.data.token,f.a.set("token",n,{expires:60}),e.setToken(n),e.$router.replace({path:f.a.get("back_url")||"/"}),f.a.remove("back_url"),e.getPublicData(),e.isRemember?localStorage.setItem("TEL",JSON.stringify({telephone:e.telephone})):localStorage.setItem("TEL",JSON.stringify({telephone:""})));case 4:case"end":return t.stop()}}),t)})))()},accountLogin:function(){var e=this;return Object(n.a)(regeneratorRuntime.mark((function t(){var o,n;return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,e.$post("account/login",{mobile:e.account,password:e.password,client:l.d});case 2:1==(o=t.sent).code&&(n=o.data.token,f.a.set("token",n,{expires:60}),e.setToken(n),e.$router.replace({path:f.a.get("back_url")||"/"}),f.a.remove("back_url"),e.getPublicData(),e.isRemember?localStorage.setItem("ACCOUNT",JSON.stringify({account:e.account})):localStorage.setItem("ACCOUNT",JSON.stringify({account:""})));case 4:case"end":return t.stop()}}),t)})))()}}),created:function(){try{var e,t,o,n,r=this.$route.query;r.code&&r.state&&this.handleWechatLogin({code:r.code,state:r.state});var l=null!==(e=JSON.parse(localStorage.getItem("ACCOUNT")))&&void 0!==e?e:{},c=null!==(t=JSON.parse(localStorage.getItem("TEL")))&&void 0!==t?t:{};this.account=null!==(o=null==l?void 0:l.account)&&void 0!==o?o:"",this.telephone=null!==(n=null==c?void 0:c.telephone)&&void 0!==n?n:""}catch(e){}}},x=(o(641),o(8)),component=Object(x.a)(v,(function(){var e=this,t=e._self._c;return t("div",{staticClass:"login flex col-center"},[t("div",{staticClass:"login-container flex col-stretch"},[t("div",{staticClass:"login-banner"},[t("img",{attrs:{src:e.config.pc_login_logo,height:"100%"}})]),e._v(" "),t("div",{staticClass:"login-float-form-wrap"},[t("div",{staticClass:"login-box"},[t("div",{staticClass:"login-header-box"},[t("div",{staticClass:"header-tabs flex row-center"},[t("div",{staticClass:"header-tab xxl",class:{"active-tab":0==e.loginStatus},on:{click:function(t){return e.changeLoginType(0)}}},[e._v("验证码登录")]),e._v(" "),t("div",{staticClass:"header-tab xxl",class:{"active-tab":1==e.loginStatus},on:{click:function(t){return e.changeLoginType(1)}}},[e._v("账号密码登录")])]),e._v(" "),t("div",{directives:[{name:"show",rawName:"v-show",value:0==e.loginStatus,expression:"loginStatus == 0"}],on:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.smsLogin.apply(null,arguments)}}},[t("div",{staticClass:"login-form-box"},[t("div",{staticClass:"login-form-item"},[t("el-input",{staticClass:"input-phone-num",attrs:{placeholder:"请输入手机号码"},model:{value:e.telephone,callback:function(t){e.telephone=t},expression:"telephone"}},[t("el-select",{staticStyle:{width:"100px"},attrs:{slot:"prepend",placeholder:"请选择"},slot:"prepend",model:{value:e.selectNumberType,callback:function(t){e.selectNumberType=t},expression:"selectNumberType"}},[t("el-option",{attrs:{label:"中国+86",value:"1"}})],1)],1)],1),e._v(" "),t("div",{staticClass:"login-form-item flex"},[t("el-input",{staticStyle:{width:"210px"},attrs:{placeholder:"短信验证码"},model:{value:e.smsCode,callback:function(t){e.smsCode=t},expression:"smsCode"}}),e._v(" "),t("el-button",{staticClass:"sms-btn",on:{click:e.sendSMSCode}},[e.canSend?t("div",[e._v("获取验证码")]):t("count-down",{attrs:{time:60,format:"ss秒",autoStart:!0},on:{finish:function(t){e.canSend=!0}}})],1)],1)]),e._v(" "),t("div",{staticClass:"option-box flex-col"},[t("el-checkbox",{staticClass:"muted",model:{value:e.isRemember,callback:function(t){e.isRemember=t},expression:"isRemember"}},[e._v("记住账号")]),e._v(" "),t("div",{staticClass:"m-t-20 flex-col"},[t("el-button",{attrs:{type:"primary"},on:{click:e.smsLogin}},[e._v("立即登录")])],1)],1)]),e._v(" "),t("div",{directives:[{name:"show",rawName:"v-show",value:1==e.loginStatus,expression:"loginStatus == 1"}],on:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.accountLogin.apply(null,arguments)}}},[t("div",{staticClass:"login-form-box"},[t("div",{staticClass:"login-form-item"},[t("el-input",{staticClass:"input-phone-num",attrs:{placeholder:"请输入账号/手机号码"},model:{value:e.account,callback:function(t){e.account=t},expression:"account"}},[t("i",{staticClass:"el-icon-user",staticStyle:{"font-size":"18px"},attrs:{slot:"prepend"},slot:"prepend"})])],1),e._v(" "),t("div",{staticClass:"login-form-item flex"},[t("el-input",{attrs:{placeholder:"请输入密码","show-password":""},model:{value:e.password,callback:function(t){e.password=t},expression:"password"}},[t("i",{staticClass:"el-icon-more-outline",staticStyle:{"font-size":"18px"},attrs:{slot:"prepend"},slot:"prepend"})])],1)]),e._v(" "),t("div",{staticClass:"option-box flex-col"},[t("div",{staticClass:"flex row-between"},[t("el-checkbox",{staticClass:"muted",model:{value:e.isRemember,callback:function(t){e.isRemember=t},expression:"isRemember"}},[e._v("记住账号")]),e._v(" "),t("nuxt-link",{staticClass:"muted",attrs:{to:"/account/forget_pwd"}},[e._v("忘记密码？")])],1),e._v(" "),t("div",{staticClass:"m-t-20 flex-col"},[t("el-button",{attrs:{type:"primary"},on:{click:e.accountLogin}},[e._v("立即登录")])],1)])])]),e._v(" "),t("div",{staticClass:"login-footer-box flex row-between"},[t("div",{staticClass:"flex"},[t("div",{staticClass:"flex login__other-item",on:{click:e.goWechatLogin}},[t("i",{staticClass:"iconfont icon-weixin1 login__weixin-icon"}),e._v(" "),t("div",{staticClass:"m-l-4 muted"},[e._v("微信")])])]),e._v(" "),t("nuxt-link",{staticClass:"primary",attrs:{to:"/account/register"}},[e._v("注册账号")])],1)])])])])}),[],!1,null,"6ee76944",null);t.default=component.exports;installComponents(component,{CountDown:o(485).default})}}]);