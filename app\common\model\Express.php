<?php


namespace app\common\model;

use app\common\basics\Models;

class Express extends Models
{
    const ZHONGTONG  = 'zhongtong';
    const SHENTONG   = 'shentong';


    /**
     * @notes 获取快递100编码
     * @param bool $from
     * @return string
     * <AUTHOR>
     * @date 2023/2/14 10:45
     */
    public static function getkuaidi100code($from = true)
    {
        $desc = [
//            self::ZHONGTONG         => 'ztoOpen',
            self::SHENTONG          => '44',
        ];
        return $desc[$from] ?? '';
    }
}