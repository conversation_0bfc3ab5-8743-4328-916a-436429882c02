# 实现计划

- [x] 1. 创建WebSocket通知测试核心方法



  - 在`app/api/controller/Test.php`中添加`testWebsocketNotification`方法
  - 实现参数验证和默认值设置逻辑
  - 添加方法到`$like_not_need_login`数组以允许无需登录访问





  - _需求: 1.1, 1.2, 2.1, 2.2_



- [-] 2. 实现admin模块WebSocket通知推送功能

  - 集成现有的`CombinedHandler`和`AdminNotificationHandler`推送机制



  - 实现向admin_group推送测试通知的逻辑
  - 添加推送结果统计和连接数统计功能
  - 处理推送失败的错误情况和日志记录



  - _需求: 1.1, 1.3, 4.1, 4.2_

- [ ] 3. 实现shop模块WebSocket通知推送功能
  - 利用现有的WebSocket基础设施向shop_group推送通知
  - 实现shop连接的识别和消息推送逻辑
  - 添加shop推送结果的统计和反馈机制
  - _需求: 1.2, 1.3, 4.1, 4.2_

- [ ] 4. 实现消息类型支持和自定义内容功能
  - 根据type参数构建不同类型的测试消息（order, chat, system, test_notification）
  - 实现自定义消息标题、内容、URL和图标的功能
  - 确保消息格式与现有WebSocket处理器兼容
  - _需求: 2.1, 2.2, 5.1, 5.2, 5.3, 5.4_

- [ ] 5. 添加推送结果统计和详细反馈
  - 实现连接数统计功能，统计admin和shop的在线连接数
  - 构建详细的推送结果响应数据结构
  - 添加推送成功/失败的具体信息和错误原因
  - 实现推送方法识别（Redis、GatewayWorker、Swoole）
  - _需求: 4.1, 4.2, 4.3, 4.4_

- [ ] 6. 实现错误处理和参数验证
  - 添加target参数验证（admin, shop, all）
  - 添加type参数验证（order, chat, system, test_notification）
  - 实现WebSocket服务可用性检查
  - 添加友好的错误消息和解决建议
  - _需求: 1.4, 2.3, 2.4_

- [ ] 7. 集成现有的通知推送机制
  - 复用`Notification.php`控制器中的Redis推送逻辑
  - 集成`Websocket.php`控制器中的GatewayWorker推送机制
  - 确保与现有WebSocket处理器的消息格式兼容性
  - 添加多重推送机制的容错处理
  - _需求: 1.1, 1.2, 1.4_

- [ ] 8. 添加访问控制和安全限制
  - 实现基于环境的访问控制（开发/测试/生产）
  - 添加IP和用户级别的频率限制机制
  - 实现测试接口的权限验证逻辑
  - 添加敏感信息过滤和日志脱敏
  - _需求: 3.1, 3.2, 3.3, 3.4_

- [ ] 9. 完善日志记录和监控
  - 添加详细的推送操作日志记录
  - 实现推送结果和性能指标的日志记录
  - 添加错误情况的详细日志和调试信息
  - 确保日志格式与现有系统一致
  - _需求: 4.4_

- [ ] 10. 创建API文档注释
  - 为`testWebsocketNotification`方法添加完整的API文档注释
  - 包含所有请求参数的详细说明和示例
  - 添加响应格式的详细说明和示例
  - 确保文档格式与现有API控制器一致
  - _需求: 2.1, 2.2, 4.1_

- [ ] 11. 编写单元测试
  - 创建测试用例验证参数验证逻辑
  - 测试不同target和type参数组合的推送功能
  - 测试错误处理和异常情况的处理
  - 验证推送结果数据结构的正确性
  - _需求: 1.1, 1.2, 1.3, 1.4_

- [ ] 12. 集成测试和验证
  - 测试与现有WebSocket连接的兼容性
  - 验证admin和shop客户端能正确接收测试通知
  - 测试多连接场景下的推送功能
  - 验证推送结果统计的准确性
  - _需求: 1.1, 1.2, 4.1, 4.2_