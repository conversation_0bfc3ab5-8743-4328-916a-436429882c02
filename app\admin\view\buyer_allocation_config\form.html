{extend name="layout"/}
{block name="content"}
<div class="layui-form" lay-filter="layuiadmin-form-admin" id="layuiadmin-form-admin" style="padding: 20px;">
    <input type="hidden" name="id" value="{{d.detail.id??''}}">
    <div class="layui-form-item">
        <label class="layui-form-label">商家等级 <span class="layui-badge-dot"></span></label>
        <div class="layui-input-block">
            <select name="tier_level" lay-verify="required" {{d.detail.id?'disabled':''}}>
                <option value="">请选择商家等级</option>
                <option value="0" {{# if(d.detail && d.detail.tier_level == 0){ }}selected{{# } }}>0元入驻</option>
                <option value="1" {{# if(d.detail && d.detail.tier_level == 1){ }}selected{{# } }}>商家会员</option>
                <option value="2" {{# if(d.detail && d.detail.tier_level == 2){ }}selected{{# } }}>实力厂商</option>
            </select>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">总采购数 <span class="layui-badge-dot"></span></label>
        <div class="layui-input-block">
            <input type="number" name="total_buyers" value="{{d.detail.total_buyers??''}}" lay-verify="required|number" placeholder="请输入总采购人员数量" autocomplete="off" class="layui-input">
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">一级比例(%) <span class="layui-badge-dot"></span></label>
        <div class="layui-input-block">
            <input type="number" name="level_1_ratio" value="{{d.detail.level_1_ratio??''}}" lay-verify="required|number" placeholder="请输入一级活跃度比例" autocomplete="off" class="layui-input">
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">二级比例(%) <span class="layui-badge-dot"></span></label>
        <div class="layui-input-block">
            <input type="number" name="level_2_ratio" value="{{d.detail.level_2_ratio??''}}" lay-verify="required|number" placeholder="请输入二级活跃度比例" autocomplete="off" class="layui-input">
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">三级比例(%) <span class="layui-badge-dot"></span></label>
        <div class="layui-input-block">
            <input type="number" name="level_3_ratio" value="{{d.detail.level_3_ratio??''}}" lay-verify="required|number" placeholder="请输入三级活跃度比例" autocomplete="off" class="layui-input">
        </div>
    </div>
    <div class="layui-form-item layui-hide">
        <input type="button" lay-submit lay-filter="LAY-user-front-submit" id="LAY-user-front-submit" value="确认">
    </div>
</div>

<script>
layui.use(['form', 'layer'], function () {
    var form = layui.form;
    var layer = layui.layer;
    var $ = layui.$;

    // 提交
    form.on('submit(LAY-user-front-submit)', function (data) {
        var field = data.field;
        var url = field.id ? '{:url("edit")}' : '{:url("add")}';
        
        // 编辑时，tier_level是disabled的，不会被提交，需要手动加上
        if(field.id) {
            field.tier_level = $('select[name=tier_level]').val();
        }

        $.post(url, field, function (res) {
            if (res.code === 0) {
                layer.msg(res.msg, {icon: 1, time: 1000}, function(){
                    var index = parent.layer.getFrameIndex(window.name);
                    parent.layer.close(index);
                    parent.layui.table.reload('dataTable');
                });
            } else {
                layer.msg(res.msg, {icon: 2, time: 1500});
            }
        });
        return false;
    });
});
</script>
{/block}
