{"name": "markbaker/complex", "type": "library", "description": "PHP Class for working with complex numbers", "keywords": ["complex", "mathematics"], "homepage": "https://github.com/MarkBaker/PHPComplex", "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"phpunit/phpunit": "^7.0 || ^8.0 || ^9.3", "squizlabs/php_codesniffer": "^3.4", "phpcompatibility/php-compatibility": "^9.0", "dealerdirect/phpcodesniffer-composer-installer": "^0.7.0"}, "autoload": {"psr-4": {"Complex\\": "classes/src/"}}, "scripts": {"style": "phpcs --report-width=200 --standard=PSR2 --report=summary,full classes/src/ unitTests/classes/src -n", "versions": "phpcs --report-width=200 --standard=PHPCompatibility --report=summary,full classes/src/ --runtime-set testVersion 7.2- -n"}, "minimum-stability": "dev"}