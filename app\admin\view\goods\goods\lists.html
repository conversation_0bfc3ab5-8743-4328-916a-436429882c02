{layout name="layout1" /}
<style>
    .layui-table-cell {
        height: auto;
    }

    /* 商品信息样式优化 */
    .goods-info-container {
        display: flex;
        align-items: flex-start;
        gap: 10px;
    }

    .goods-image {
        width: 80px;
        height: 80px;
        border-radius: 6px;
        object-fit: cover;
        border: 1px solid #e6e6e6;
        transition: transform 0.2s;
        cursor: pointer;
    }

    .goods-image:hover {
        transform: scale(1.05);
        border-color: #1890ff;
    }

    .goods-details {
        flex: 1;
        min-width: 0;
    }

    .goods-name {
        font-size: 14px;
        font-weight: 600;
        color: #333;
        line-height: 1.4;
        margin-bottom: 4px;
        cursor: pointer;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    .goods-name:hover {
        color: #1890ff;
    }

    .goods-meta {
        font-size: 12px;
        color: #666;
        line-height: 1.3;
    }

    .goods-id {
        color: #999;
        margin-bottom: 2px;
    }

    /* 商家信息样式优化 */
    .shop-info-container {
        display: flex;
        align-items: flex-start;
        gap: 8px;
    }

    .shop-logo {
        width: 60px;
        height: 60px;
        border-radius: 6px;
        object-fit: cover;
        border: 1px solid #e6e6e6;
    }

    .shop-details {
        flex: 1;
        min-width: 0;
    }

    .shop-name {
        font-weight: 600;
        color: #333;
        margin-bottom: 2px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .shop-meta {
        font-size: 12px;
        color: #666;
        line-height: 1.3;
    }

    /* 状态标签样式 */
    .status-tag {
        display: inline-block;
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 500;
        margin-right: 4px;
        margin-bottom: 2px;
    }

    .status-selling {
        background: #f6ffed;
        color: #52c41a;
        border: 1px solid #b7eb8f;
    }

    .status-warehouse {
        background: #fff7e6;
        color: #fa8c16;
        border: 1px solid #ffd591;
    }

    .status-recycle {
        background: #f5f5f5;
        color: #8c8c8c;
        border: 1px solid #d9d9d9;
    }

    .status-audit-wait {
        background: #e6f7ff;
        color: #1890ff;
        border: 1px solid #91d5ff;
    }

    .status-audit-refuse {
        background: #fff2f0;
        color: #ff4d4f;
        border: 1px solid #ffccc7;
    }

    /* 价格样式优化 */
    .price-info {
        font-weight: 600;
        color: #ff4d4f;
        font-size: 14px;
    }

    .price-range {
        color: #666;
        font-size: 12px;
        margin-top: 2px;
    }

    /* 数量信息样式 */
    .quantity-info {
        text-align: center;
    }

    .quantity-number {
        font-weight: 600;
        font-size: 16px;
        color: #333;
    }

    .quantity-label {
        font-size: 12px;
        color: #999;
        margin-top: 2px;
    }

    /* 库存预警 */
    .stock-warning {
        color: #ff4d4f;
    }

    .stock-normal {
        color: #52c41a;
    }

    /* 表格行hover效果 */
    .layui-table tbody tr:hover {
        background-color: #fafafa;
    }

    /* 操作按钮组优化 */
    .layui-table-cell .layui-btn-xs {
        margin: 1px;
        padding: 2px 6px;
        font-size: 11px;
    }

    /* 商品栏目样式 */
    .column-tag {
        background: #f0f2f5;
        color: #666;
        padding: 2px 6px;
        border-radius: 4px;
        font-size: 12px;
        display: inline-block;
        max-width: 120px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        border: 1px solid #d9d9d9;
    }

    /* 商品ID样式 */
    .goods-id-cell {
        font-family: 'Courier New', monospace;
        font-weight: 600;
        color: #666;
        background: #fafafa;
        padding: 4px;
        border-radius: 4px;
        font-size: 13px;
    }

    /* 分类信息容器 */
    .category-container {
        line-height: 1.4;
    }

    .category-container .column-tag {
        margin-bottom: 2px;
        max-width: 240px;
    }

    /* 时间显示优化 */
    .time-info {
        font-size: 12px;
        color: #666;
        line-height: 1.4;
    }

    .time-date {
        font-weight: 500;
        color: #333;
    }

    /* 审核说明样式 */
    .audit-remark {
        max-width: 180px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-size: 12px;
        color: #666;
        cursor: pointer;
    }

    .audit-remark:hover {
        color: #1890ff;
    }

    /* 操作按钮样式优化 */
    .layui-table tbody tr td .layui-btn {
        margin: 1px;
        font-weight: 500;
        letter-spacing: 0.5px;
    }

    /* 按钮悬停效果 */
    .layui-table tbody tr td .layui-btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2) !important;
        opacity: 0.9;
    }

    /* 按钮点击效果 */
    .layui-table tbody tr td .layui-btn:active {
        transform: translateY(0);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) !important;
    }

    /* 操作列宽度优化 */
    .layui-table-fixed-r .layui-table-body {
        overflow-x: visible;
    }

    /* 修复固定列遮挡问题 */
    .layui-table-fixed-r {
        z-index: 1 !important;
    }

    .layui-table-fixed-r .layui-table-body {
        z-index: 1 !important;
    }

    /* 按钮容器样式 */
    .layui-table tbody tr td div[style*="white-space: nowrap"] {
        padding: 2px 0;
    }
</style>
<div class="wrapper">
    <div class="layui-card">
        <!--操作提示-->
        <div class="layui-card-body">
            <div class="layui-collapse like-layui-collapse" lay-accordion="" style="border:1px dashed #c4c4c4">
                <div class="layui-colla-item">
                    <h2 class="layui-colla-title like-layui-colla-title" style="background-color: #fff">操作提示</h2>
                    <div class="layui-colla-content layui-show">
                        <p>*平台审核商家发布的商品，当商品审核通过并且处于销售中状态时，商家可以销售该商品</p>
                        <p>*平台可通过“违规重审”，下架违规商品并标记为审核未通过。</p>
                    </div>
                </div>
            </div>
        </div>

        <!--搜索条件-->
        <div class="layui-card-body layui-form">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">商家名称：</label>
                    <div class="layui-input-inline">
                        <input type="text" name="shop_name" id="shop_name" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">商品名称：</label>
                    <div class="layui-input-inline">
                        <input type="text" name="goods_name" id="goods_name" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">商品类型:</label>
                    <div class="layui-input-block">
                        <select name="goods_type" id="goods_type">
                            <option value="">全部</option>
                            {foreach $goods_type as $key => $val }
                            <option value="{$key}">{$val}</option>
                            {/foreach}
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">平台分类:</label>
                    <div class="layui-input-block">
                        <select name="platform_cate_id" id="platform_cate_id" placeholder="请选择平台商品分类">
                            <option value="0">全部</option>
                            {foreach $cate_list as $val }
                            <option value="{$val.id}">{$val.html}{$val.name}</option>
                            {/foreach}
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">商品栏目:</label>
                    <div class="layui-input-block">
                        <select name="goods_column_id" id="goods_column_id" placeholder="请选择商品栏目">
                            <option value="">全部</option>
                            {foreach $column_list as $val }
                            <option value="{$val.id}">{$val.name}</option>
                            {/foreach}
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <button class="layui-btn layui-btn-sm layui-btn-normal" lay-submit lay-filter="search">查询</button>
                    <button class="layui-btn layui-btn-sm layui-btn-primary" lay-submit
                        lay-filter="clear-search">重置</button>
                </div>
            </div>
        </div>

        <!-选项卡-->
            <div class="layui-tab layui-tab-card" lay-filter="like-tabs">
                <ul class="layui-tab-title">
                    <li data-type='1' class="layui-this">销售中商品({$statistics.sell})</li>
                    <li data-type='2'>仓库中商品({$statistics.warehouse})</li>
                    <li data-type='3'>回收站商品({$statistics.recycle})</li>
                    <li data-type='4'>待审核商品({$statistics.audit_stay})</li>
                    <li data-type='5'>审核未通过商品({$statistics.audit_refuse})</li>
                </ul>

                <div class="layui-tab-content" style="padding: 0 15px;">
                    <div class="layui-btn-container" style="margin-top: 10px">
                        <button class="layui-btn layui-btn-sm layui-btn-normal layEvent"
                            lay-event="export">商品导出</button>
                        <button class="layui-btn layui-btn-sm layEvent layui-btn-primary" lay-event="more_audit"
                            id="more_audit" style="display: none">批量审核</button>
                        <button class="layui-btn layui-btn-sm layEvent layui-btn-primary" lay-event="more_lower"
                            id="more_lower">批量下架</button>
                    </div>

                    <table id="goods-lists" lay-filter="goods-lists"></table>

                    <script type="text/html" id="shop-info">
                    <div class="shop-info-container">
                        <!-- <img src="{{d.shop_logo}}" class="shop-logo image-show"> -->
                        <div class="shop-details">
                            <div class="shop-name" title="{{d.shop_name}}">{{d.shop_name}}</div>
                            <div class="shop-meta">
                                <div>编号: {{d.shop_id}}</div>
                                <div>类型: {{d.shop_type_desc}}</div>
                                {{# if(d.shop_level) { }}
                                <div>等级: {{d.shop_level}}</div>
                                {{# } }}
                            </div>
                        </div>
                    </div>
                </script>

                    <script type="text/html" id="goods-info">
                    <div class="goods-info-container">
                        <img src="{{d.image}}" class="goods-image image-show" alt="{{d.name}}">
                        <div class="goods-details">
                            <div class="goods-name" title="{{d.name}}" lay-event="view-goods">{{d.name}}</div>
                            <div class="goods-meta">
                                <div class="goods-id">商品ID: {{d.id}}</div>
                                {{# if(d.goods_type_desc) { }}
                                <div>类型: {{d.goods_type_desc}}</div>
                                {{# } }}
                                {{# if(d.sku_count) { }}
                                <div>规格: {{d.sku_count}}种</div>
                                {{# } }}
                            </div>
                        </div>
                    </div>
                </script>

                    <script type="text/html" id="goods-status">
                    <!-- 删除状态检查 -->
                    {{# if(d.del == 2) { }}
                    <span class="status-tag status-recycle">回收站</span>
                    {{# } else if(d.del == 1) { }}
                    <span class="status-tag"
                        style="background: #f5f5f5; color: #8c8c8c; border: 1px solid #d9d9d9;">已删除</span>
                    {{# } else { }}
                    <!-- 正常商品的销售状态 -->
                    {{# if(d.status == 1) { }}
                    <span class="status-tag status-selling">销售中</span>
                    {{# } else { }}
                    <span class="status-tag status-warehouse">仓库中</span>
                    {{# } }}
                    {{# } }}

                    <!-- 审核状态 -->
                    {{# if(d.audit_status == 0) { }}
                    <span class="status-tag status-audit-wait">待审核</span>
                    {{# } else if(d.audit_status == 1) { }}
                    <span class="status-tag"
                        style="background: #f6ffed; color: #52c41a; border: 1px solid #b7eb8f;">已审核</span>
                    {{# } else if(d.audit_status == 2) { }}
                    <span class="status-tag status-audit-refuse">审核拒绝</span>
                    {{# } }}

                    <!-- 商品特殊标签 -->
                    {{# if(d.is_hot == 1) { }}
                    <span class="status-tag"
                        style="background: #fff0f6; color: #eb2f96; border: 1px solid #ffadd2;">热销</span>
                    {{# } }}

                    {{# if(d.is_recommend == 1) { }}
                    <span class="status-tag"
                        style="background: #f9f0ff; color: #722ed1; border: 1px solid #d3adf7;">推荐</span>
                    {{# } }}

                    {{# if(d.goods_label_top) { }}
                    <span class="status-tag"
                        style="background: #e6fffb; color: #13c2c2; border: 1px solid #87e8de;">{{d.goods_label_top}}</span>
                    {{# } }}

                    {{# if(d.goods_label) { }}
                    <span class="status-tag"
                        style="background: #fff7e6; color: #fa8c16; border: 1px solid #ffd591;">{{d.goods_label}}</span>
                    {{# } }}
                    </script>

                    <script type="text/html" id="price-info">
                    <div class="price-info">
                        ¥{{d.min_price}}
                        {{# if(d.min_price != d.max_price) { }}
                        ~ ¥{{d.max_price}}
                        {{# } }}
                    </div>
                    {{# if(d.market_price && d.market_price > d.max_price) { }}
                    <div class="price-range">市场价: ¥{{d.market_price}}</div>
                    {{# } }}
                </script>

                    <script type="text/html" id="stock-info">
                    <div class="quantity-info">
                        <div class="quantity-number {{# if(d.stock < 10) { }}stock-warning{{# } else { }}stock-normal{{# } }}">
                            {{d.stock}}
                        </div>
                        <div class="quantity-label">库存</div>
                        {{# if(d.stock < 10) { }}
                        <div style="color: #ff4d4f; font-size: 10px;">库存不足</div>
                        {{# } }}
                    </div>
                </script>

                    <script type="text/html" id="sales-info">
                    <div class="quantity-info">
                        <div class="quantity-number">{{d.sales_actual}}</div>
                        <div class="quantity-label">销量</div>
                    </div>
                </script>

                    <script type="text/html" id="column-info">
                    {{# if(d.columnStr) { }}
                    <span class="column-tag" title="{{d.columnStr}}">{{d.columnStr}}</span>
                    {{# } else { }}
                    <span style="color: #ccc;">未分类</span>
                    {{# } }}
                </script>

                    <script type="text/html" id="category-info">
                    <div class="category-container">
                        <!-- 平台分类显示 -->
                        {{# if(d.first_cate_name || d.second_cate_name || d.third_cate_name) { }}
                        <div style="margin-bottom: 4px;">
                            <span class="column-tag" title="平台分类">
                                <i class="layui-icon layui-icon-list" style="font-size: 10px;"></i>
                                {{# if(d.first_cate_name) { }}{{d.first_cate_name}}{{# } }}
                                {{# if(d.second_cate_name) { }} > {{d.second_cate_name}}{{# } }}
                                {{# if(d.third_cate_name) { }} > {{d.third_cate_name}}{{# } }}
                            </span>
                        </div>
                        {{# } else if(d.first_cate_id || d.second_cate_id || d.third_cate_id) { }}
                        <div style="margin-bottom: 4px;">
                            <span class="column-tag" title="平台分类ID">
                                <i class="layui-icon layui-icon-list" style="font-size: 10px;"></i>
                                {{# if(d.first_cate_id && d.first_cate_id > 0) { }}{{d.first_cate_id}}{{# } }}
                                {{# if(d.second_cate_id && d.second_cate_id > 0) { }} > {{d.second_cate_id}}{{# } }}
                                {{# if(d.third_cate_id && d.third_cate_id > 0) { }} > {{d.third_cate_id}}{{# } }}
                            </span>
                        </div>
                        {{# } }}

                   
                    </div>
                </script>

                    <script type="text/html" id="goods-id">
                    <div class="goods-id-cell">
                        {{d.id}}
                    </div>
                </script>

                    <!-- 调试模板，用于查看数据结构 -->
                    <script type="text/html" id="debug-info">
                    <div style="font-size: 10px; color: #999;">
                        状态: {{d.status}} | 删除: {{d.del}} | 审核: {{d.audit_status}}<br>
                        分类ID: {{d.first_cate_id}}-{{d.second_cate_id}}-{{d.third_cate_id}}<br>
                        分类名: {{d.first_cate_name}}-{{d.second_cate_name}}-{{d.third_cate_name}}<br>
                        店铺分类: {{d.shop_cate_id}}-{{d.shop_cate_name}}
                    </div>
                </script>

                    <script type="text/html" id="time-info">
                    <div class="time-info">
                        <div class="time-date">{{d.create_time.split(' ')[0]}}</div>
                        <div>{{d.create_time.split(' ')[1]}}</div>
                    </div>
                </script>

                    <script type="text/html" id="audit-remark-info">
                    {{# if(d.audit_remark) { }}
                    <div class="audit-remark" title="{{d.audit_remark}}">{{d.audit_remark}}</div>
                    {{# } else { }}
                    <span style="color: #ccc;">-</span>
                    {{# } }}
                </script>

                    <script type="text/html" id="ratio">
                    一级分销比例:{{d.first_ratio}}% <br />
                    二级分销比例:{{d.second_ratio}}% <br />
                    三级分销比例:{{d.third_ratio}}% <br />
                </script>

                    <script type="text/html" id="goods-operation">
                    <div style="white-space: nowrap; display: flex; gap: 4px; justify-content: center; align-items: center;">
                        <a class="layui-btn layui-btn-sm" lay-event="view" title="查看详情"
                           style="background: linear-gradient(135deg, #1e9fff, #0d7ecc); border: none; border-radius: 4px; padding: 6px 12px; font-size: 12px; transition: all 0.3s ease; box-shadow: 0 2px 4px rgba(30, 159, 255, 0.3); display: flex; align-items: center; justify-content: center; text-align: center;">
                            <i class="layui-icon layui-icon-search" style="margin-right: 3px;"></i>查看
                        </a>
                        <a class="layui-btn layui-btn-sm" lay-event="set" title="商品设置"
                           style="background: linear-gradient(135deg, #5fb878, #4a9960); border: none; border-radius: 4px; padding: 6px 12px; font-size: 12px; transition: all 0.3s ease; box-shadow: 0 2px 4px rgba(95, 184, 120, 0.3); display: flex; align-items: center; justify-content: center; text-align: center;">
                            <i class="layui-icon layui-icon-set" style="margin-right: 3px;"></i>设置
                        </a>
                        {{# if( (d.audit_status == 1) ){ }}
                        <a class="layui-btn layui-btn-sm" lay-event="re_audit" title="违规重审"
                           style="background: linear-gradient(135deg, #ff5722, #d84315); border: none; border-radius: 4px; padding: 6px 12px; font-size: 12px; transition: all 0.3s ease; box-shadow: 0 2px 4px rgba(255, 87, 34, 0.3); display: flex; align-items: center; justify-content: center; text-align: center;">
                            <i class="layui-icon layui-icon-close" style="margin-right: 3px;"></i>重审
                        </a>
                        {{# } }}
                        {{# if( (d.audit_status == 0) ){ }}
                        <a class="layui-btn layui-btn-sm" lay-event="audit" title="审核商品"
                           style="background: linear-gradient(135deg, #009688, #00695c); border: none; border-radius: 4px; padding: 6px 12px; font-size: 12px; transition: all 0.3s ease; box-shadow: 0 2px 4px rgba(0, 150, 136, 0.3); display: flex; align-items: center; justify-content: center; text-align: center;">
                            <i class="layui-icon layui-icon-ok" style="margin-right: 3px;"></i>审核
                        </a>
                        {{# } }}
                    </div>
                </script>
                </div>
            </div>
    </div>
</div>

<script>
    layui.use(['table', 'form', 'element'], function () {
        var $ = layui.$
            , form = layui.form
            , table = layui.table
            , element = layui.element;

        //监听搜索
        form.on('submit(search)', function (data) {
            var field = data.field;
            //执行重载
            table.reload('goods-lists', {
                where: field,
                page: { curr: 1 }
            });
            updateTabNumber();
        });

        //清空查询
        form.on('submit(clear-search)', function () {
            $('#shop_name').val('');
            $('#goods_name').val('');
            $('#goods_type').val('');
            $('#platform_cate_id').val('');
            $('#shop_cate_id').val('');
            $('#goods_column_id').val('');
            form.render('select');

            // 获取当前激活的tab类型
            var currentType = $('.layui-tab-title .layui-this').attr('data-type') || '1';

            //刷新列表，保持当前tab的类型过滤
            table.reload('goods-lists', {
                where: { type: currentType },
                page: { curr: 1 }
            });
            updateTabNumber();
        });


        $('.layui-btn.layuiadmin-btn-goods').on('click', function () {
            var type = $(this).data('type');
            active[type] ? active[type].call(this) : '';
        });

        //初始化表格
        like.tableLists('#goods-lists', '{:url("goods.goods/lists")}', [
            { type: 'checkbox', fixed: 'left', width: 50 }
            , { title: '商品ID', width: 80, templet: '#goods-id', align: 'center', fixed: 'left' }
            , { title: '商家信息', width: 240, templet: '#shop-info' }
            , { title: '商品信息', width: 300, templet: '#goods-info' }
            , { title: '商品分类', width: 220, templet: '#category-info', align: 'center' }
            , { title: '商品状态', width: 220, templet: '#goods-status', align: 'center' }

            , { title: '价格信息', width: 120, templet: '#price-info', align: 'center' }
            , { title: '销量', width: 70, templet: '#sales-info', align: 'center' }
            , { title: '库存', width: 70, templet: '#stock-info', align: 'center' }
            , { field: 'sort_weight', width: 80, title: '权重', align: 'center' }
            , { title: '审核说明', width: 180, templet: '#audit-remark-info', align: 'center', hide: false }
            , { title: '发布时间', width: 140, templet: '#time-info', align: 'center' }
            , { fixed: 'right', title: '操作', width: 220, align: 'center', toolbar: '#goods-operation' }
        ], { type: 1 }, true); // 初始加载销售中的商品
        //切换列表
        element.on('tab(like-tabs)', function (data) {
            var type = $(this).attr('data-type');
            // 保持搜索条件，不重置搜索模块
            // 注释掉原来的重置代码，保持用户的搜索条件
            /*
            $('#shop_name').val('');
            $('#goods_name').val('');
            $('#platform_cate_id').val('');
            $('#shop_cate_id').val('');
            $('#goods_column_id').val('');
            form.render('select');
            */

            if (type == 4) {
                $("#more_audit").show();
            } else {
                $("#more_audit").hide();
            }
            if (type == 1 || type == 2 || type == 3) {
                $("#more_lower").show();
            } else {
                $("#more_lower").hide();
            }
            // 重新获取商品列表
            getList(type);
        });

        //监听工具条
        table.on('tool(goods-lists)', function (obj) {
            var id = obj.data.id;
            var name = obj.data.name;

            if (obj.event === 'view' || obj.event === 'view-goods') {
                var id = obj.data.id;
                layer.open({
                    type: 2
                    , title: '查看商品详情'
                    , content: '{:url("goods.goods/view")}?goods_id=' + id
                    , area: ['90%', '90%']
                    , btn: ['保存修改', '返回']
                    , maxmin: true
                    , yes: function (index, layero) {
                        var iframeWindow = window['layui-layer-iframe' + index];
                        var iframeDoc = layero.find('iframe').contents();
                        iframeDoc.find('input').removeAttr('readonly');
                        iframeDoc.find(':radio').removeAttr('disabled');
                        iframeDoc.find(':checkbox').removeAttr('disabled');
                        iframeDoc.find('select').removeAttr('disabled');
                        iframeDoc.find('button').removeAttr('disabled');
                        iframeDoc.find('content').removeAttr('disabled');
                        iframeDoc.find('a').attr('onclick', function (i, val) {
                            return val;
                        });

                    
                        // 直接获取表单数据
                        var formData = {};

                        // 获取所有表单元素
                        //console.log('=== 开始获取表单数据 ===');
                        //console.log('iframe窗口对象:', iframeWindow);
                        //console.log('iframe文档对象:', iframeDoc);

                        iframeDoc.find('input[name], select[name], textarea[name]').each(function () {
                            var $this = $(this);
                            var name = $this.attr('name');
                            var type = $this.attr('type');
                            var value = $this.val();

                            //console.log('处理字段:', name, '类型:', type, '值:', value);

                            // 跳过文件类型和按钮类型
                            if (type === 'file' || type === 'button' || type === 'submit') {
                                //console.log('跳过字段:', name, '类型:', type);
                                return;
                            }

                            // 处理单选框和复选框
                            if (type === 'radio' || type === 'checkbox') {
                                if ($this.is(':checked')) {
                                    formData[name] = value;
                                    //console.log('添加选中的radio/checkbox:', name, '=', value);
                                }
                            } else {
                                formData[name] = value;
                                //console.log('添加普通字段:', name, '=', value);
                            }
                        });


                        // 特别处理富文本编辑器内容（支持图片）
                        function getRichTextContentWithImages() {
                            console.log('=== 开始获取富文本内容（支持图片）===');

                            // 检查编辑器中是否包含图片
                            function hasImages() {
                                try {
                                    var editorIframe = iframeDoc.find('iframe[name*="layedit"], .layui-layedit iframe');
                                    if (editorIframe.length > 0) {
                                        var iframeBody = editorIframe.contents().find('body');
                                        var images = iframeBody.find('img');
                                        console.log('编辑器中发现图片数量:', images.length);
                                        if (images.length > 0) {
                                            images.each(function(i, img) {
                                                console.log('图片' + (i+1) + ':', $(img).attr('src'));
                                            });
                                        }
                                        return images.length > 0;
                                    }
                                } catch (e) {
                                    console.log('检查图片时出错:', e);
                                }
                                return false;
                            }

                            var containsImages = hasImages();
                            console.log('编辑器包含图片:', containsImages);

                            // 方法1: 使用layEditor（如果存在）
                            if (iframeWindow.layEditor && iframeWindow.ieditor) {
                                console.log('方法1: 使用layEditor');
                                try {
                                    // 如果包含图片，多次同步确保图片内容被正确处理
                                    if (containsImages) {
                                        console.log('检测到图片，执行多次同步...');
                                        for (var i = 0; i < 3; i++) {
                                            iframeWindow.layEditor.sync(iframeWindow.ieditor);
                                            console.log('第' + (i + 1) + '次同步完成');
                                        }
                                        // 给图片处理一些时间
                                        setTimeout(function() {
                                            console.log('图片处理延迟后再次同步...');
                                            iframeWindow.layEditor.sync(iframeWindow.ieditor);
                                        }, 200);
                                    } else {
                                        iframeWindow.layEditor.sync(iframeWindow.ieditor);
                                    }

                                    var editorContent = iframeWindow.layEditor.getContent(iframeWindow.ieditor);
                                    console.log('layEditor获取的内容长度:', editorContent ? editorContent.length : 0);
                                    console.log('layEditor获取的内容:', editorContent);

                                    if (editorContent && editorContent.trim() !== '') {
                                        formData.content = editorContent;
                                        console.log('从layEditor获取内容成功');
                                        return true;
                                    }
                                } catch (e) {
                                    console.log('layEditor获取失败:', e);
                                }
                            }

                            // 方法2: 直接从编辑器iframe获取HTML内容
                            console.log('方法2: 从编辑器iframe直接获取');
                            try {
                                var editorIframe = iframeDoc.find('iframe[name*="layedit"], .layui-layedit iframe');
                                if (editorIframe.length > 0) {
                                    var iframeContent = editorIframe.contents().find('body').html();
                                    console.log('从iframe获取的内容长度:', iframeContent ? iframeContent.length : 0);
                                    console.log('从iframe获取的内容:', iframeContent);

                                    if (iframeContent && iframeContent.trim() !== '') {
                                        formData.content = iframeContent;
                                        console.log('从编辑器iframe获取内容成功');
                                        return true;
                                    }
                                }
                            } catch (e) {
                                console.log('从iframe获取内容失败:', e);
                            }

                            // 方法3: 从textarea获取
                            console.log('方法3: 从textarea获取');
                            var contentTextarea = iframeDoc.find('textarea[name="content"]');
                            if (contentTextarea.length > 0) {
                                var textareaContent = contentTextarea.val();
                                console.log('textarea内容长度:', textareaContent ? textareaContent.length : 0);
                                console.log('textarea内容:', textareaContent);

                                if (textareaContent && textareaContent.trim() !== '') {
                                    formData.content = textareaContent;
                                    console.log('从textarea获取内容成功');
                                    return true;
                                }
                            }

                            console.log('所有方法都未能获取到内容');
                            return false;
                        }

                        // 获取富文本内容
                        getRichTextContentWithImages();

                        console.log('最终表单数据:', formData);
                        // 确保包含商品ID
                        if (!formData.goods_id) {
                            var goodsId = iframeDoc.find('input[name="goods_id"]').val();
                            if (goodsId) {
                                formData.goods_id = goodsId;
                            }
                        }
                        // 提交数据
                        like.ajax({
                            url: '{:url("goods.goods/updateBasicInfo")}',
                            data: formData,
                            type: "post",
                            success: function (res) {
                                //console.log('服务器响应:', res);
                                if (res.code == 1) {
                                    layui.layer.msg(res.msg, {
                                        offset: '15px'
                                        , icon: 1
                                        , time: 1000
                                    }, function () {
                                        layer.close(index);
                                        updateTabNumber();
                                        table.reload('goods-lists');
                                    });
                                } else {
                                    //console.log('保存失败，错误信息:', res.msg);
                                    layui.layer.msg(res.msg || '保存失败', {
                                        offset: '15px'
                                        , icon: 2
                                        , time: 2000
                                    });
                                }
                            },
                            error: function (xhr, status, error) {
                                //console.log('网络请求错误:', xhr, status, error);
                                layui.layer.msg('网络错误，请重试', {
                                    offset: '15px'
                                    , icon: 2
                                    , time: 2000
                                });
                            }
                        });
                    }
                    , cancel: function (index, layero) {
                        layer.close(index);
                    }
                });
            }
            if (obj.event === 're_audit') {
                var id = obj.data.id;
                layer.open({
                    type: 2
                    , title: '违规重审'
                    , content: '{:url("goods.goods/reAudit")}?goods_id=' + id
                    , area: ['60%', '60%']
                    , btn: ['确定', '返回']
                    , scrollbar: false
                    , maxmin: true
                    , yes: function (index, layero) {
                        var iframeWindow = window['layui-layer-iframe' + index]
                            , submitID = 'reaudit-submit'
                            , submit = layero.find('iframe').contents().find('#' + submitID);
                        //监听提交
                        iframeWindow.layui.form.on('submit(' + submitID + ')', function (data) {
                            var field = data.field;
                            like.ajax({
                                url: '{:url("goods.goods/reAudit")}',
                                data: field,
                                type: "post",
                                success: function (res) {
                                    if (res.code == 1) {
                                        layui.layer.msg(res.msg, {
                                            offset: '15px'
                                            , icon: 1
                                            , time: 1000
                                        }, function () {
                                            // window.location.href = window.location.href;
                                            updateTabNumber();
                                            table.reload('goods-lists');
                                        });
                                    }
                                }
                            });
                        });
                        submit.trigger('click');
                        layer.close(index);
                    }
                    , cancel: function (index, layero) {
                        layer.close(index);
                    }
                });
            }
            if (obj.event === 'set') {
                var id = obj.data.id;
                layer.open({
                    type: 2
                    , title: '设置'
                    , content: '{:url("goods.goods/setInfo")}?goods_id=' + id
                    , area: ['60%', '60%']
                    , btn: ['确定', '返回']
                    , scrollbar: false
                    , maxmin: true
                    , yes: function (index, layero) {
                        var iframeWindow = window['layui-layer-iframe' + index]
                            , submitID = 'setinfo-submit'
                            , submit = layero.find('iframe').contents().find('#' + submitID);
                        //监听提交
                        iframeWindow.layui.form.on('submit(' + submitID + ')', function (data) {
                            var field = data.field;
                            like.ajax({
                                url: '{:url("goods.goods/setInfo")}',
                                data: field,
                                type: "post",
                                success: function (res) {
                                    if (res.code == 1) {
                                        layui.layer.msg(res.msg, {
                                            offset: '15px'
                                            , icon: 1
                                            , time: 1000
                                        }, function () {
                                            // window.location.href = window.location.href;
                                            layer.close(index);
                                            updateTabNumber();
                                            table.reload('goods-lists');
                                        });
                                    }
                                }
                            });
                        });
                        submit.trigger('click');
                    }
                    , cancel: function (index, layero) {
                        layer.close(index);
                    }
                });
            }
            if (obj.event === 'audit') {
                var id = obj.data.id;
                layer.open({
                    type: 2
                    , title: '审核'
                    , content: '{:url("goods.goods/audit")}?goods_id=' + id
                    , area: ['60%', '60%']
                    , btn: ['确定', '返回']
                    , scrollbar: false
                    , maxmin: true
                    , yes: function (index, layero) {
                        var iframeWindow = window['layui-layer-iframe' + index]
                            , submitID = 'audit-submit'
                            , submit = layero.find('iframe').contents().find('#' + submitID);
                        //监听提交
                        iframeWindow.layui.form.on('submit(' + submitID + ')', function (data) {
                            var field = data.field;
                            like.ajax({
                                url: '{:url("goods.goods/audit")}',
                                data: field,
                                type: "post",
                                success: function (res) {
                                    if (res.code == 1) {
                                        layui.layer.msg(res.msg, {
                                            offset: '15px'
                                            , icon: 1
                                            , time: 1000
                                        }, function () {
                                            // window.location.href = window.location.href;
                                            layer.close(index);
                                            updateTabNumber();
                                            table.reload('goods-lists');
                                        });
                                    }
                                }
                            });
                        });
                        submit.trigger('click');
                    }
                    , cancel: function (index, layero) {
                        layer.close(index);
                    }
                });
            }
        });

        // 监听头部工具栏事件
        $('.layEvent').on('click', function () {
            var type = $(this).attr('lay-event');
            active[type] ? active[type].call(this) : '';
        });

        //图片放大
        $(document).on('click', '.image-show', function () {
            var src = $(this).attr('src');
            like.showImg(src, 600);
        });

        //审核说明点击显示完整内容
        $(document).on('click', '.audit-remark', function () {
            var content = $(this).attr('title');
            if (content && content.length > 20) {
                layer.alert(content, {
                    title: '审核说明',
                    area: ['400px', 'auto'],
                    maxmin: false
                });
            }
        });

        function getList(type) {
            // 获取当前搜索条件
            var searchParams = {
                shop_name: $('#shop_name').val(),
                goods_name: $('#goods_name').val(),
                goods_type: $('#goods_type').val(),
                platform_cate_id: $('#platform_cate_id').val(),
                shop_cate_id: $('#shop_cate_id').val(),
                goods_column_id: $('#goods_column_id').val(),
                type: type
            };

            // 使用table.reload方法重新加载数据
            table.reload('goods-lists', {
                where: searchParams,
                page: { curr: 1 }
            });
        }

        /**
         * 更新选项卡 统计数据
         */
        function updateTabNumber() {
            // 获取当前所有搜索条件
            var searchParams = {
                shop_name: $('#shop_name').val(),
                goods_name: $('#goods_name').val(),
                goods_type: $('#goods_type').val(),
                platform_cate_id: $('#platform_cate_id').val(),
                shop_cate_id: $('#shop_cate_id').val(),
                goods_column_id: $('#goods_column_id').val()
            };

            like.ajax({
                url: '{:url("goods.goods/totalCount")}',
                data: searchParams,
                type: "GET",
                success: function (res) {
                    if (res.code === 1) {
                        $(".layui-tab-title li[data-type=1]").html("销售中商品(" + res.data.sell + ")");
                        $(".layui-tab-title li[data-type=2]").html("仓库中商品(" + res.data.warehouse + ")");
                        $(".layui-tab-title li[data-type=3]").html("回收站商品(" + res.data.recycle + ")");
                        $(".layui-tab-title li[data-type=4]").html("待审核商品(" + res.data.audit_stay + ")");
                        $(".layui-tab-title li[data-type=5]").html("审核未通过商品(" + res.data.audit_refuse + ")");
                    }
                }
            });
        }


        //事件
        var active = {
            export: function () {
                var shop_name = $('input[name=shop_name]').val();
                var goods_name = $('input[name=goods_name]').val();
                var goods_type = $('select[name=goods_type]').val();
                var platform_cate_id = $('select[name=platform_cate_id]').val();
                var goods_column_id = $('select[name=goods_column_id]').val();
                var type = $('.layui-tab-title .layui-this').data('type');

                var params = {
                    shop_name: shop_name,
                    goods_name: goods_name,
                    goods_type: goods_type,
                    platform_cate_id: platform_cate_id,
                    goods_column_id: goods_column_id,
                    type: type,
                };

                var url = '{:url("admin/goods.goods/exportGoods")}?' + $.param(params);
                window.location.href = url;
            },
            more_audit: function () {
                var checkStatus = table.checkStatus('goods-lists');
                var checkData = checkStatus.data;
                var ids = [];
                // 取出选中的行ID
                checkData.forEach(function (item) {
                    ids.push(parseInt(item['id']));
                });
                if (ids.length <= 0) {
                    layui.layer.msg('请选择商品', { time: 1000 });
                    return false;
                }
                layer.open({
                    type: 2
                    , title: '批量审核'
                    , content: '{:url("goods.goods/moreAudit")}?ids=' + ids
                    , area: ['60%', '60%']
                    , btn: ['确定', '返回']
                    , scrollbar: false
                    , maxmin: true
                    , yes: function (index, layero) {
                        var iframeWindow = window['layui-layer-iframe' + index]
                            , submitID = 'audit-submit'
                            , submit = layero.find('iframe').contents().find('#' + submitID);
                        //监听提交
                        iframeWindow.layui.form.on('submit(' + submitID + ')', function (data) {
                            var field = data.field;
                            like.ajax({
                                url: '{:url("goods.goods/moreAudit")}',
                                data: field,
                                type: "post",
                                success: function (res) {
                                    if (res.code == 1) {
                                        layui.layer.msg(res.msg, {
                                            offset: '15px'
                                            , icon: 1
                                            , time: 1000
                                        }, function () {
                                            layer.close(index);
                                            updateTabNumber();
                                            table.reload('goods-lists');
                                        });
                                    }
                                }
                            });
                        });
                        submit.trigger('click');
                    }
                    , cancel: function (index, layero) {
                        layer.close(index);
                    }
                });
            },
            more_lower: function () {
                var checkStatus = table.checkStatus('goods-lists');
                var checkData = checkStatus.data;
                var ids = [];
                // 取出选中的行ID
                checkData.forEach(function (item) {
                    ids.push(parseInt(item['id']));
                });
                if (ids.length <= 0) {
                    layui.layer.msg('请选择商品', { time: 1000 });
                    return false;
                }
                layer.open({
                    type: 2
                    , title: '批量下架'
                    , content: '{:url("goods.goods/moreLower")}?ids=' + ids
                    , area: ['60%', '60%']
                    , btn: ['确定', '返回']
                    , scrollbar: false
                    , maxmin: true
                    , yes: function (index, layero) {
                        var iframeWindow = window['layui-layer-iframe' + index]
                            , submitID = 'reaudit-submit'
                            , submit = layero.find('iframe').contents().find('#' + submitID);
                        //监听提交
                        iframeWindow.layui.form.on('submit(' + submitID + ')', function (data) {
                            var field = data.field;
                            like.ajax({
                                url: '{:url("goods.goods/moreLower")}',
                                data: field,
                                type: "post",
                                success: function (res) {
                                    if (res.code == 1) {
                                        layui.layer.msg(res.msg, {
                                            offset: '15px'
                                            , icon: 1
                                            , time: 1000
                                        }, function () {
                                            updateTabNumber();
                                            table.reload('goods-lists');
                                        });
                                    }
                                }
                            });
                        });
                        submit.trigger('click');
                        layer.close(index);
                    }
                    , cancel: function (index, layero) {
                        layer.close(index);
                    }
                });
            },
        };
        like.eventClick(active);

    });
</script>