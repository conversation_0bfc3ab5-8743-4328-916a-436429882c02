<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Cam\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * DeletePolicy请求参数结构体
 *
 * @method array getPolicyId() 获取数组，数组成员是策略 id，支持批量删除策略
 * @method void setPolicyId(array $PolicyId) 设置数组，数组成员是策略 id，支持批量删除策略
 */
class DeletePolicyRequest extends AbstractModel
{
    /**
     * @var array 数组，数组成员是策略 id，支持批量删除策略
     */
    public $PolicyId;

    /**
     * @param array $PolicyId 数组，数组成员是策略 id，支持批量删除策略
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("PolicyId",$param) and $param["PolicyId"] !== null) {
            $this->PolicyId = $param["PolicyId"];
        }
    }
}
