{"version": 3, "sources": ["webpack:///./src/views/account/login.vue", "webpack:///./src/utils/cache.js", "webpack:///src/views/account/login.vue", "webpack:///./src/views/account/login.vue?8b57", "webpack:///./src/views/account/login.vue?f5a4", "webpack:///./src/views/account/login.vue?2c5f", "webpack:///./src/views/account/login.vue?6e71"], "names": ["render", "_vm", "this", "_c", "_self", "staticClass", "_v", "_s", "$route", "query", "type", "ref", "attrs", "accountObj", "rules", "nativeOn", "$event", "indexOf", "_k", "keyCode", "key", "$refs", "inputPwd", "focus", "model", "value", "account", "callback", "$$v", "$set", "expression", "slot", "handleLogin", "apply", "arguments", "password", "rememberAccount", "staticStyle", "loadingLogin", "on", "copyright", "staticRenderFns", "cache", "keyPrev", "set", "expire", "data", "time", "JSON", "stringify", "window", "localStorage", "setItem", "e", "get", "getItem", "parse", "removeItem", "Math", "round", "Date", "getTime", "remove", "<PERSON><PERSON><PERSON>", "required", "message", "trigger", "methods", "remember", "login", "redirect", "computed", "created", "component", "module", "exports"], "mappings": "uHAAA,IAAIA,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,SAAS,CAACF,EAAG,MAAM,CAACE,YAAY,QAAQ,CAACF,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAACF,EAAG,MAAM,CAACE,YAAY,SAAS,CAACJ,EAAIK,GAAGL,EAAIM,GAAGN,EAAIO,OAAOC,MAAMC,KAAM,SAAW,aAAaP,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,UAAU,CAACQ,IAAI,OAAOC,MAAM,CAAC,MAAQX,EAAIY,WAAW,MAAQZ,EAAIa,QAAQ,CAACX,EAAG,eAAe,CAACS,MAAM,CAAC,KAAO,YAAY,CAACT,EAAG,WAAW,CAACS,MAAM,CAAC,YAAc,SAASG,SAAS,CAAC,MAAQ,SAASC,GAAQ,OAAIA,EAAON,KAAKO,QAAQ,QAAQhB,EAAIiB,GAAGF,EAAOG,QAAQ,QAAQ,GAAGH,EAAOI,IAAI,SAAgB,KAAYnB,EAAIoB,MAAMC,SAASC,UAAUC,MAAM,CAACC,MAAOxB,EAAIY,WAAWa,QAASC,SAAS,SAAUC,GAAM3B,EAAI4B,KAAK5B,EAAIY,WAAY,UAAWe,IAAME,WAAW,uBAAuB,CAAC3B,EAAG,IAAI,CAACE,YAAY,kCAAkCO,MAAM,CAAC,KAAO,UAAUmB,KAAK,cAAc,GAAG5B,EAAG,eAAe,CAACS,MAAM,CAAC,KAAO,aAAa,CAACT,EAAG,WAAW,CAACQ,IAAI,WAAWC,MAAM,CAAC,YAAc,QAAQ,gBAAgB,IAAIG,SAAS,CAAC,MAAQ,SAASC,GAAQ,OAAIA,EAAON,KAAKO,QAAQ,QAAQhB,EAAIiB,GAAGF,EAAOG,QAAQ,QAAQ,GAAGH,EAAOI,IAAI,SAAgB,KAAYnB,EAAI+B,YAAYC,MAAM,KAAMC,aAAaV,MAAM,CAACC,MAAOxB,EAAIY,WAAWsB,SAAUR,SAAS,SAAUC,GAAM3B,EAAI4B,KAAK5B,EAAIY,WAAY,WAAYe,IAAME,WAAW,wBAAwB,CAAC3B,EAAG,IAAI,CAACE,YAAY,uCAAuCO,MAAM,CAAC,KAAO,UAAUmB,KAAK,cAAc,GAAG5B,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,cAAc,CAACS,MAAM,CAAC,MAAQ,QAAQY,MAAM,CAACC,MAAOxB,EAAImC,gBAAiBT,SAAS,SAAUC,GAAM3B,EAAImC,gBAAgBR,GAAKE,WAAW,sBAAsB,GAAG3B,EAAG,YAAY,CAACkC,YAAY,CAAC,MAAQ,QAAQzB,MAAM,CAAC,KAAO,UAAU,QAAUX,EAAIqC,cAAcC,GAAG,CAAC,MAAQtC,EAAI+B,cAAc,CAAC/B,EAAIK,GAAG,SAAS,IAAI,OAAOH,EAAG,MAAM,CAACE,YAAY,UAAU,CAACJ,EAAIK,GAAGL,EAAIM,GAAGN,EAAIuC,iBAE5yDC,EAAkB,GCFtB,MAAMC,EAAQ,CAEVC,QAAS,QAEZC,IAAIxB,EAAKK,EAAOoB,GACf,IAAIC,EAAO,CACVD,OAAQA,EAAU3C,KAAK6C,OAASF,EAAU,GAC1CpB,SAGmB,kBAATqB,IACVA,EAAOE,KAAKC,UAAUH,IACvB,IACUI,OAAOC,aAAaC,QAAQhC,EAAK0B,GACzC,MAAOO,GACR,OAAO,IAGTC,IAAIlC,GACH,IACC,IAAI0B,EAAQI,OAAOC,aAAaI,QAAQnC,GAC/B,IAAI0B,EAAM,OAAO,EAC1B,MAAM,MAACrB,EAAK,OAAEoB,GAAUG,KAAKQ,MAAMV,GACnC,OAAGD,GAAUA,EAAS3C,KAAK6C,QAC1BG,OAAOC,aAAaM,WAAWrC,IACxB,GAEAK,EAEP,MAAO4B,GACR,OAAO,IAITN,OACC,OAAOW,KAAKC,OAAM,IAAIC,MAAOC,UAAY,MAE1CC,OAAO1C,GACHA,GAAK8B,OAAOC,aAAaM,WAAWrC,IAErC2C,OAAO3C,GACT,OAAOlB,KAAKyC,QAAUvB,IAITsB,Q,wBCaA,GACfI,OACA,OACAV,mBACAE,gBACAzB,YACAa,WACAS,aAEArB,OACAY,SACA,CACAsC,YACAC,gBACAC,mBAGA/B,UACA,CACA6B,YACAC,gBACAC,sBAMAC,SACAnC,cACA,6BACA,IACAU,0BACA0B,8BACA1C,kCAEA,iBAIA2C,QACA,qBACA,cAAA3C,WAAAS,GAAA,gBAEA,8BACAT,UACAS,WACAzB,iCACA,SACA,MACAD,gBAAA6D,IACA,YACA,4BACA,0BACA,WACA,yBAIAC,aACA,+BAEAC,UACA,kCACA,aACA,gCACA,qCC3H8V,I,wBCQ1VC,EAAY,eACd,EACAzE,EACAyC,GACA,EACA,KACA,KACA,MAIa,aAAAgC,E,8BClBfC,EAAOC,QAAU,CAAC,QAAU,Y,kCCD5B", "file": "js/chunk-7de4568f.992e49e4.js", "sourcesContent": ["var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"login\"},[_c('div',{staticClass:\"main\"},[_c('div',{staticClass:\"login-container\"},[_c('div',{staticClass:\"title\"},[_vm._v(_vm._s(_vm.$route.query.type ?'商家客服登录' : '平台客服登录'))]),_c('div',{staticClass:\"form m-t-40\"},[_c('el-form',{ref:\"form\",attrs:{\"model\":_vm.accountObj,\"rules\":_vm.rules}},[_c('el-form-item',{attrs:{\"prop\":\"account\"}},[_c('el-input',{attrs:{\"placeholder\":\"请输入账号\"},nativeOn:{\"keyup\":function($event){if(!$event.type.indexOf('key')&&_vm._k($event.keyCode,\"enter\",13,$event.key,\"Enter\"))return null;return _vm.$refs.inputPwd.focus()}},model:{value:(_vm.accountObj.account),callback:function ($$v) {_vm.$set(_vm.accountObj, \"account\", $$v)},expression:\"accountObj.account\"}},[_c('i',{staticClass:\"el-input__icon el-icon-s-custom\",attrs:{\"slot\":\"prefix\"},slot:\"prefix\"})])],1),_c('el-form-item',{attrs:{\"prop\":\"password\"}},[_c('el-input',{ref:\"inputPwd\",attrs:{\"placeholder\":\"请输入密码\",\"show-password\":\"\"},nativeOn:{\"keyup\":function($event){if(!$event.type.indexOf('key')&&_vm._k($event.keyCode,\"enter\",13,$event.key,\"Enter\"))return null;return _vm.handleLogin.apply(null, arguments)}},model:{value:(_vm.accountObj.password),callback:function ($$v) {_vm.$set(_vm.accountObj, \"password\", $$v)},expression:\"accountObj.password\"}},[_c('i',{staticClass:\"el-input__icon el-icon-s-cooperation\",attrs:{\"slot\":\"prefix\"},slot:\"prefix\"})])],1),_c('div',{staticClass:\"m-b-10 p-t-10\"},[_c('el-checkbox',{attrs:{\"label\":\"记住账号\"},model:{value:(_vm.rememberAccount),callback:function ($$v) {_vm.rememberAccount=$$v},expression:\"rememberAccount\"}})],1),_c('el-button',{staticStyle:{\"width\":\"100%\"},attrs:{\"type\":\"primary\",\"loading\":_vm.loadingLogin},on:{\"click\":_vm.handleLogin}},[_vm._v(\"登录\")])],1)],1)])]),_c('div',{staticClass:\"footer\"},[_vm._v(_vm._s(_vm.copyright))])])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "const cache = {\n\n    keyPrev: 'kefu_',\n\t//设置缓存(expire为缓存时效)\n\tset(key, value, expire) {\n\t\tlet data = {\n\t\t\texpire: expire ? (this.time() + expire) : \"\",\n\t\t\tvalue\n\t\t}\n\t\t\n\t\tif (typeof data === 'object')\n\t\t\tdata = JSON.stringify(data);\n\t\ttry {\n            window.localStorage.setItem(key, data)\n\t\t} catch (e) {\n\t\t\treturn false;\n\t\t}\n\t},\n\tget(key) {\n\t\ttry {\n\t\t\tlet data =  window.localStorage.getItem(key)\n            if(!data) return false\n\t\t\tconst {value, expire} = JSON.parse(data)\n\t\t\tif(expire && expire < this.time()) {\n\t\t\t\twindow.localStorage.removeItem(key)\n\t\t\t\treturn false;\n\t\t\t}else {\n\t\t\t\treturn value\n\t\t\t}\n\t\t} catch (e) {\n\t\t\treturn false;\n\t\t}\n\t},\n\t//获取当前时间\n\ttime() {\n\t\treturn Math.round(new Date().getTime() / 1000);\n\t},\n\tremove(key) {\n\t\tif(key) window.localStorage.removeItem(key)\n\t},\n    getKey(key) {\n\t\treturn this.keyPrev + key\n\t}\n}\n\nexport default cache;\n", "<template>\n    <div class=\"login\">\n        <div class=\"main\">\n            <div class=\"login-container\">\n                <div class=\"title\">{{$route.query.type ?'商家客服登录' : '平台客服登录'}}</div>\n                <div class=\"form m-t-40\">\n                    <el-form :model=\"accountObj\" :rules=\"rules\" ref=\"form\">\n                        <el-form-item prop=\"account\">\n                            <el-input\n                                placeholder=\"请输入账号\"\n                                v-model=\"accountObj.account\"\n                                @keyup.enter.native=\"$refs.inputPwd.focus()\"\n                            >\n                                <i\n                                    slot=\"prefix\"\n                                    class=\"el-input__icon el-icon-s-custom\"\n                                ></i>\n                            </el-input>\n                        </el-form-item>\n                        <el-form-item prop=\"password\">\n                            <el-input\n                                ref=\"inputPwd\"\n                                placeholder=\"请输入密码\"\n                                v-model=\"accountObj.password\"\n                                show-password\n                                @keyup.enter.native=\"handleLogin\"\n                            >\n                                <i\n                                    slot=\"prefix\"\n                                    class=\"el-input__icon el-icon-s-cooperation\"\n                                ></i>\n                            </el-input>\n                        </el-form-item>\n                        <div class=\"m-b-10 p-t-10\">\n                            <el-checkbox\n                                v-model=\"rememberAccount\"\n                                label=\"记住账号\"\n                            ></el-checkbox>\n                        </div>\n                        <el-button\n                            style=\"width: 100%\"\n                            type=\"primary\"\n                            :loading=\"loadingLogin\"\n                            @click=\"handleLogin\"\n                            >登录</el-button\n                        >\n                    </el-form>\n                </div>\n            </div>\n        </div>\n        <div class=\"footer\">{{ copyright }}</div>\n    </div>\n</template>\n\n<script>\nimport cache from '@/utils/cache'\nimport { apiLogin } from '@/api/app'\nimport { mapGetters } from 'vuex'\nexport default {\n    data() {\n        return {\n            rememberAccount: false,\n            loadingLogin: false,\n            accountObj: {\n                account: '',\n                password: '',\n            },\n            rules: {\n                account: [\n                    {\n                        required: true,\n                        message: '请输入账号',\n                        trigger: ['blur'],\n                    },\n                ],\n                password: [\n                    {\n                        required: true,\n                        message: '请输入密码',\n                        trigger: ['blur'],\n                    },\n                ],\n            },\n        }\n    },\n    methods: {\n        handleLogin() {\n            this.$refs.form.validate((valid) => {\n                if (!valid) return\n                cache.set('remember_account', {\n                    remember: this.rememberAccount,\n                    account: this.accountObj.account,\n                })\n                this.login()\n            })\n        },\n        //登录\n        login() {\n            this.loadingLogin = true\n            const { account, password } = this.accountObj\n\n            this.$store.dispatch('login', {\n                account,\n                password,\n                type: this.$route.query.type || 0,\n            }).then(data => {\n                const {\n                    query: { redirect },\n                } = this.$route\n                const path = typeof redirect === 'string' ? redirect : '/'\n                this.$router.replace(path)\n            }).catch(() => {\n                this.loadingLogin = false\n            })\n        },\n    },\n    computed: {\n        ...mapGetters(['copyright'])\n    },\n    created() {\n        const value = cache.get('remember_account')\n        if (value.remember) {\n            this.rememberAccount = value.remember\n            this.accountObj.account = value.account\n        }\n    },\n}\n</script>\n\n<style lang=\"scss\" scope>\n.login {\n    display: flex;\n    flex-direction: column;\n    height: 100vh;\n    .main {\n        flex: 1;\n        background-image: url(../../assets/images/login_bg.png);\n        background-repeat: no-repeat;\n        background-size: cover;\n        background-position: center;\n        display: flex;\n        justify-content: center;\n        align-items: center;\n        .login-container {\n            width: 400px;\n            border-radius: 10px;\n            background: #fff;\n            box-shadow: 0px 0px 10px rgba(64, 115, 250, 0.08);\n            padding: 30px 40px 80px;\n            .title {\n                text-align: center;\n                font-size: 24px;\n                font-weight: 500;\n            }\n        }\n    }\n    .footer {\n        font-size: 12px;\n        color: #9999;\n        text-align: center;\n        padding: 20px 0;\n    }\n}\n</style>\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./login.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./login.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./login.vue?vue&type=template&id=006baaa0&\"\nimport script from \"./login.vue?vue&type=script&lang=js&\"\nexport * from \"./login.vue?vue&type=script&lang=js&\"\nimport style0 from \"./login.vue?vue&type=style&index=0&id=006baaa0&prod&lang=scss&scope=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "// extracted by mini-css-extract-plugin\nmodule.exports = {\"primary\":\"#4073FA\"};", "export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./login.vue?vue&type=style&index=0&id=006baaa0&prod&lang=scss&scope=true&\""], "sourceRoot": ""}