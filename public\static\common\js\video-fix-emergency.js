/**
 * 紧急修复：视频上传后消失问题
 * 问题：第二次上传视频后，整个视频区域DOM被清空
 * 解决：定期检查并自动修复视频区域状态
 */

(function() {
    'use strict';
    
    //console.log(''🔧 视频紧急修复脚本已加载');
    
    // 配置
    var config = {
        videoContainer: '#videoContainer',
        checkInterval: 2000, // 每2秒检查一次
        debug: true
    };
    
    // 日志函数
    function log(message, type) {
        if (!config.debug) return;
        var prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : '🔍';
        //console.log('prefix + ' [视频修复] ' + message);
    }
    
    // 检查视频区域状态
    function checkVideoAreaStatus() {
        var $container = $(config.videoContainer);
        
        if ($container.length === 0) {
            log('视频容器不存在', 'error');
            return;
        }
        
        var containerText = $container.text().trim();
        var hasChildren = $container.children().length > 0;
        var hasVideo = $container.find('video').length > 0;
        var hasAddButton = $container.find('.add-upload-video, .like-upload-video').length > 0;
        
        // log('容器状态检查: 文本="' + containerText + '", 子元素=' + $container.children().length + ', 有视频=' + hasVideo + ', 有添加按钮=' + hasAddButton);
        
        // 检查是否需要修复
        if (containerText === '商品视频：' || 
            (!hasChildren && containerText === '') ||
            (!hasVideo && !hasAddButton)) {
            
            // log('检测到视频区域异常，开始修复...', 'error');
            fixVideoArea();
        }
    }
    
    // 修复视频区域
    function fixVideoArea() {
        var $container = $(config.videoContainer);
        
        // 恢复添加视频按钮
        var addButtonHtml = 
            '<div class="like-upload-video">' +
                '<div class="upload-image-elem">' +
                    '<a class="add-upload-video" id="video"> + 添加视频</a>' +
                '</div>' +
            '</div>';
        
        $container.html(addButtonHtml);
        // log('已恢复添加视频按钮');
        
        // 重新绑定视频上传事件
        setTimeout(function() {
            bindVideoUploadEvent();
        }, 100);
    }
    
    // 绑定视频上传事件
    function bindVideoUploadEvent() {
        // 检查like对象是否存在
        if (typeof like !== 'undefined' && like.videoUpload) {
            try {
                like.videoUpload('#video', '/shop/file.file/video.html');
                // log('视频上传事件已重新绑定', 'success');
            } catch (e) {
                // log('绑定视频上传事件失败: ' + e.message, 'error');
            }
        } else {
            // log('like对象或videoUpload方法不存在，尝试其他方式绑定');
            
            // 尝试直接绑定点击事件
            $('#video').off('click').on('click', function() {
                // log('点击添加视频按钮');
                // 这里可以添加自定义的视频上传逻辑
            });
        }
    }
    
    // 监听DOM变化
    function observeVideoContainer() {
        var container = document.querySelector(config.videoContainer);
        if (!container) {
            // log('视频容器不存在，无法监听', 'error');
            return;
        }
        
        var observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList') {
                    // log('检测到视频容器DOM变化');
                    
                    // 延迟检查，确保DOM更新完成
                    setTimeout(function() {
                        checkVideoAreaStatus();
                    }, 500);
                }
            });
        });
        
        observer.observe(container, {
            childList: true,
            subtree: true,
            attributes: true
        });
        
        // log('已开始监听视频容器DOM变化', 'success');
    }
    
    // 增强原有的视频上传成功处理
    function enhanceVideoUploadSuccess() {
        // 尝试拦截layui的成功回调
        if (typeof layui !== 'undefined') {
            layui.use(['layer'], function() {
                var layer = layui.layer;
                
                // 保存原始的close方法
                var originalClose = layer.close;
                
                // 重写close方法
                layer.close = function(index) {
                    var result = originalClose.apply(this, arguments);
                    
                    // log('检测到layui弹窗关闭');
                    
                    // 延迟检查视频状态
                    setTimeout(function() {
                        checkVideoAreaStatus();
                    }, 1000);
                    
                    return result;
                };
                
                // log('已拦截layui弹窗关闭事件', 'success');
            });
        }
    }
    
    // 初始化
    function init() {
        // log('开始初始化视频修复脚本');
        
        // 等待页面加载完成
        $(document).ready(function() {
            // log('页面加载完成，开始修复流程');
            
            // 初始检查
            setTimeout(function() {
                checkVideoAreaStatus();
            }, 1000);
            
            // 定期检查
            setInterval(function() {
                checkVideoAreaStatus();
            }, config.checkInterval);
            
            // 监听DOM变化
            observeVideoContainer();
            
            // 增强视频上传成功处理
            enhanceVideoUploadSuccess();
            
            // log('视频修复脚本初始化完成', 'success');
        });
    }
    
    // 暴露到全局，方便调试
    window.VideoFix = {
        check: checkVideoAreaStatus,
        fix: fixVideoArea,
        bind: bindVideoUploadEvent,
        config: config
    };
    
    // 启动
    init();
    
})();

// 使用说明：
// 1. 在商品编辑页面引入此脚本
// 2. 脚本会自动检查和修复视频区域
// 3. 可以通过 VideoFix.check() 手动检查状态
// 4. 可以通过 VideoFix.fix() 手动修复
// 5. 可以通过 VideoFix.config.debug = false 关闭调试日志
