{layout name="layout2" /}
<style>
    .div-flex {
        display: flex;
        align-items: center;
        justify-content: left;
    }

    .layui-form-label {
        width: 100px;
    }

    .width-160 {
        width: 200px;
    }

    .layui-table th {
        text-align: center;
    }

    .table-margin {
        margin-left: 50px;
        margin-right: 50px;
        text-align: center;
    }

    .image {
        height: 80px;
        width: 80px;
    }

</style>

<div class="layui-card-body" >
    <!--基本信息-->
    <div  lay-filter="layuiadmin-form-express" id="layuiadmin-form-express" >

        <!--实物发货-->
        <div class="layui-form">
            <input type="hidden" name="order_id" value="{$detail.id}">
            <div class="layui-form-item">
                <fieldset class="layui-elem-field layui-field-title">
                    <legend>快递配送</legend>
                </fieldset>
            </div>

            <div class="layui-form-item div-flex layui-hide" >
                <label class="layui-form-label ">配送方式:</label>
                <div>
                    <input type="radio" name="send_type" lay-filter="express" value="1" title="快递配送" checked>
                    <input type="radio" name="send_type" lay-filter="express" value="2" title="无需快递">
                </div>
            </div>

            <div class="layui-form-item div-flex select-express">
                <label class="layui-form-label ">选择快递:</label>
                <div>
                    <select name="shipping_id" lay-verify="required">
                        {foreach $express as $k => $v}
                            <?php if(($detail['shipping']['shipping_id'] ?? 0) == $v['id']): ?>
                                <option value="{$v.id}" selected>{$v.name}</option>
                            <?php else: ?>
                                <option value="{$v.id}">{$v.name}</option>
                            <?php endif;?>
                        {/foreach}
                    </select>
                </div>
            </div>

            <div class="layui-form-item div-flex select-express" >
                <label class="layui-form-label ">快递单号:</label>
                <div>
                    <input type="text" name="invoice_no" value="{$detail.shipping.invoice_no|default=''}" placeholder="请输入快递单号" autocomplete="off" class="layui-input">
                </div>
            </div>

            <div class="layui-form-item div-flex ">
                <div class="layui-input-block ">
                    <input type="button" class="layui-btn layui-btn-sm layui-btn-normal width_160" lay-submit lay-filter="send" id="send" value="修改">
                    <button type="button" class="layui-btn layui-btn-sm layui-btn-primary width_160 back" >返回</button>
                </div>
            </div>
        </div>



    </div>
</div>


<script type="text/javascript">
    //注意：选项卡 依赖 element 模块，否则无法进行功能性操作

    layui.config({
        version:"{$front_version}",
        base: '/static/plug/layui-admin/dist/layuiadmin/' //静态资源所在路径
    }).extend({
        index: 'lib/index' //主入口模块
    }).use(['index', 'element', 'jquery', 'like', 'form'], function () {
        var $ = layui.$
            , form = layui.form;
        var like = layui.like;

        //主图放大
        $(document).on('click', '.image-show', function () {
            var src = $(this).attr('src');
            like.showImg(src,600);
        });


        form.on('radio(express)', function (data) {
            var checked = data.value;
            if (checked == 1) {
                $('.select-express').show();
            } else {
                $('.select-express').hide();
            }
        });

        $('.back').click(function () {
            var index=parent.layer.getFrameIndex(window.name); //获取当前窗口的name
            parent.layer.close(index);
            parent.layui.table.reload('order-lists');
            return true;
        });


        // 实物发货
        form.on('submit(send)', function (data) {
            var field = data.field;
            like.ajax({
                url: '{:url("order.order/delivery_change")}'
                , data: field
                , type: 'post'
                , success: function (res) {
                    if (res.code == 1) {
                        layui.layer.msg(res.msg, {
                            offset: '15px'
                            , icon: 1
                            , time: 1000
                        },function () {
                            var index = parent.layer.getFrameIndex(window.name);
                            parent.location.reload();
                            parent.updateTabNumber();
                            parent.layer.close(index);
                        });
                    }
                },
            });
        })

    });
</script>