{"version": 3, "file": "pages/user/after_sales/apply_sale.js", "sources": ["webpack:///./components/price-formate.vue?b158", "webpack:///./components/price-formate.vue?4c8e", "webpack:///./components/price-formate.vue", "webpack:///./components/price-formate.vue?3e0e", "webpack:///./components/price-formate.vue?2b64", "webpack:///./components/price-formate.vue?8e8e", "webpack:///./components/price-formate.vue?32b6", "webpack:///./components/upload.vue?d4ec", "webpack:///./components/upload.vue?cda5", "webpack:///./components/upload.vue?8307", "webpack:///./components/upload.vue?42a2", "webpack:///./components/upload.vue", "webpack:///./components/upload.vue?2a5d", "webpack:///./components/upload.vue?5689", "webpack:///./pages/user/after_sales/apply_sale.vue?7c54", "webpack:///./pages/user/after_sales/apply_sale.vue?e628", "webpack:///./pages/user/after_sales/apply_sale.vue?55c3", "webpack:///./pages/user/after_sales/apply_sale.vue?302f", "webpack:///./pages/user/after_sales/apply_sale.vue", "webpack:///./pages/user/after_sales/apply_sale.vue?6480", "webpack:///./pages/user/after_sales/apply_sale.vue?048f"], "sourcesContent": ["// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../node_modules/css-loader/dist/cjs.js??ref--3-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--3-oneOf-1-2!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./price-formate.vue?vue&type=style&index=0&lang=css&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"3181fc86\", content, true, context)\n};", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('span',{class:(_vm.lineThrough ? 'line-through' : '') + 'price-format',style:({ color: _vm.color, 'font-weight': _vm.weight })},[_vm._ssrNode(((_vm.showSubscript)?(\"<span\"+(_vm._ssrStyle(null,{\n            'font-size': _vm.subscriptSize + 'px',\n            'margin-right': '1px',\n        }, null))+\">¥</span>\"):\"<!---->\")+\" <span\"+(_vm._ssrStyle(null,{ 'font-size': _vm.firstSize + 'px', 'margin-right': '1px' }, null))+\">\"+_vm._ssrEscape(_vm._s(_vm.priceSlice.first))+\"</span> \"+((_vm.priceSlice.second)?(\"<span\"+(_vm._ssrStyle(null,{ 'font-size': _vm.secondSize + 'px' }, null))+\">\"+_vm._ssrEscape(\".\"+_vm._s(_vm.priceSlice.second))+\"</span>\"):\"<!---->\"))])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nexport default {\n    data() {\n        return {\n            priceSlice: {},\n        }\n    },\n    components: {},\n    props: {\n        firstSize: {\n            type: Number,\n            default: 14,\n        },\n        secondSize: {\n            type: Number,\n            default: 14,\n        },\n        color: {\n            type: String,\n        },\n        weight: {\n            type: [String, Number],\n            default: 400,\n        },\n        price: {\n            type: [String, Number],\n            default: '',\n        },\n        showSubscript: {\n            type: Boolean,\n            default: true,\n        },\n        subscriptSize: {\n            type: Number,\n            default: 14,\n        },\n        lineThrough: {\n            type: Boolean,\n            default: false,\n        },\n    },\n    created() {\n        this.priceFormat()\n    },\n    watch: {\n        price(val) {\n            this.priceFormat()\n        },\n    },\n    methods: {\n        priceFormat() {\n            let { price } = this\n            let priceSlice = {}\n            if (price !== null) {\n                price = parseFloat(price)\n                price = String(price).split('.')\n                priceSlice.first = price[0]\n                priceSlice.second = price[1]\n                this.priceSlice = priceSlice\n            }\n        },\n    },\n}\n", "import mod from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./price-formate.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./price-formate.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./price-formate.vue?vue&type=template&id=0c4d5c85&\"\nimport script from \"./price-formate.vue?vue&type=script&lang=js&\"\nexport * from \"./price-formate.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./price-formate.vue?vue&type=style&index=0&lang=css&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  null,\n  \"7ae24710\"\n  \n)\n\nexport default component.exports", "export * from \"-!../node_modules/vue-style-loader/index.js??ref--3-oneOf-1-0!../node_modules/css-loader/dist/cjs.js??ref--3-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--3-oneOf-1-2!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./price-formate.vue?vue&type=style&index=0&lang=css&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".price-format{display:flex;align-items:baseline}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/sass-resources-loader/lib/loader.js??ref--7-oneOf-1-4!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./upload.vue?vue&type=style&index=0&id=05db7967&lang=scss&scoped=true&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"05ffbf2f\", content, true, context)\n};", "export * from \"-!../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/sass-resources-loader/lib/loader.js??ref--7-oneOf-1-4!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./upload.vue?vue&type=style&index=0&id=05db7967&lang=scss&scoped=true&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".v-upload .el-upload--picture-card[data-v-05db7967]{width:76px;height:76px;line-height:76px}.v-upload .el-upload-list--picture-card .el-upload-list__item[data-v-05db7967]{width:76px;height:76px}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"v-upload\"},[_c('el-upload',{attrs:{\"list-type\":\"picture-card\",\"action\":_vm.url + '/api/file/formimage',\"limit\":_vm.limit,\"on-success\":_vm.success,\"on-error\":_vm.error,\"on-remove\":_vm.remove,\"on-change\":_vm.onChange,\"headers\":{ token: _vm.$store.state.token },\"auto-upload\":_vm.autoUpload}},[(_vm.isSlot)?_vm._t(\"default\"):_c('div',[_c('div',{staticClass:\"muted xs\"},[_vm._v(\"上传图片\")])])],2)],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport config from '~/config/app'\nexport default {\n    components: {},\n    props: {\n        limit: {\n            type: Number,\n            default: 1,\n        },\n        isSlot: {\n            type: Boolean,\n            default: false,\n        },\n        autoUpload: {\n            type: Boolean,\n            default: true,\n        },\n        onChange: {\n            type: Function,\n            default: () => {},\n        },\n    },\n    watch: {},\n    data() {\n        return {\n            url: config.baseUrl,\n        }\n    },\n    created() {},\n    computed: {},\n    methods: {\n        success(res, file, fileList) {\n            if (!this.autoUpload) {\n                return\n            }\n            this.$message({\n                message: '上传成功',\n                type: 'success',\n            })\n            this.$emit('success', fileList)\n        },\n        remove(file, fileList) {\n            this.$emit('remove', fileList)\n        },\n        error(res) {\n            this.$message({\n                message: '上传失败，请重新上传',\n                type: 'error',\n            })\n        },\n    },\n}\n", "import mod from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./upload.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./upload.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./upload.vue?vue&type=template&id=05db7967&scoped=true&\"\nimport script from \"./upload.vue?vue&type=script&lang=js&\"\nexport * from \"./upload.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./upload.vue?vue&type=style&index=0&id=05db7967&lang=scss&scoped=true&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  \"05db7967\",\n  \"388748c3\"\n  \n)\n\nexport default component.exports", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../../../node_modules/sass-resources-loader/lib/loader.js??ref--7-oneOf-1-4!../../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./apply_sale.vue?vue&type=style&index=0&id=a1ed73d8&lang=scss&scoped=true&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../../../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"ddd4eaec\", content, true, context)\n};", "export * from \"-!../../../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../../../node_modules/sass-resources-loader/lib/loader.js??ref--7-oneOf-1-4!../../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./apply_sale.vue?vue&type=style&index=0&id=a1ed73d8&lang=scss&scoped=true&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../../../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".apply-sale-list[data-v-a1ed73d8]{padding:10px}.apply-sale-list .goods-info .table-content[data-v-a1ed73d8],.apply-sale-list .goods-info .table-head[data-v-a1ed73d8]{padding:10px 20px;border-bottom:1px solid #e5e5e5}.apply-sale-list .goods-info .info[data-v-a1ed73d8]{width:500px}.apply-sale-list .goods-info .act-pay[data-v-a1ed73d8],.apply-sale-list .goods-info .num[data-v-a1ed73d8],.apply-sale-list .goods-info .price[data-v-a1ed73d8],.apply-sale-list .goods-info .sum[data-v-a1ed73d8]{width:100px}.apply-sale-list .apply-form[data-v-a1ed73d8]{margin-top:24px}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"apply-sale-list\"},[_vm._ssrNode(\"<div class=\\\"goods-info\\\" data-v-a1ed73d8>\",\"</div>\",[_vm._ssrNode(\"<div class=\\\"table-head flex\\\" data-v-a1ed73d8><div class=\\\"info\\\" data-v-a1ed73d8>商品信息</div> <div class=\\\"price flex row-center\\\" data-v-a1ed73d8>单价</div> <div class=\\\"num flex row-center\\\" data-v-a1ed73d8>数量</div> <div class=\\\"sum flex row-center\\\" data-v-a1ed73d8>合计</div> <div class=\\\"act-pay flex row-center\\\" data-v-a1ed73d8>实付</div></div> \"),_vm._ssrNode(\"<div class=\\\"table-content flex m-t-10\\\" data-v-a1ed73d8>\",\"</div>\",[_vm._ssrNode(\"<div class=\\\"info flex\\\" data-v-a1ed73d8>\",\"</div>\",[_vm._ssrNode(\"<div class=\\\"flex\\\" data-v-a1ed73d8>\",\"</div>\",[_c('el-image',{staticStyle:{\"width\":\"72px\",\"height\":\"72px\",\"flex\":\"none\"},attrs:{\"src\":_vm.goods.image}}),_vm._ssrNode(\" <div class=\\\"m-l-10\\\" style=\\\"flex: 1; align-self: flex-start\\\" data-v-a1ed73d8><div class=\\\"line2\\\" data-v-a1ed73d8>\"+_vm._ssrEscape(_vm._s(_vm.goods.goods_name))+\"</div> <div class=\\\"mt10 muted sm\\\" data-v-a1ed73d8>\"+_vm._ssrEscape(_vm._s(_vm.goods.spec_value))+\"</div></div>\")],2)]),_vm._ssrNode(\" \"),_vm._ssrNode(\"<div class=\\\"price flex row-center\\\" style=\\\"align-self: flex-start\\\" data-v-a1ed73d8>\",\"</div>\",[_c('price-formate',{attrs:{\"price\":_vm.goods.goods_price}})],1),_vm._ssrNode(\" <div class=\\\"num flex row-center\\\" style=\\\"align-self: flex-start\\\" data-v-a1ed73d8>\"+_vm._ssrEscape(\"\\n                \"+_vm._s(_vm.goods.goods_num)+\"\\n            \")+\"</div> \"),_vm._ssrNode(\"<div class=\\\"sum flex row-center\\\" style=\\\"align-self: flex-start\\\" data-v-a1ed73d8>\",\"</div>\",[_c('price-formate',{attrs:{\"price\":_vm.goods.total_price}})],1),_vm._ssrNode(\" \"),_vm._ssrNode(\"<div class=\\\"act-pay flex row-center\\\" style=\\\"align-self: flex-start\\\" data-v-a1ed73d8>\",\"</div>\",[_c('price-formate',{attrs:{\"price\":_vm.goods.total_pay_price}})],1)],2)],2),_vm._ssrNode(\" \"),_vm._ssrNode(\"<div class=\\\"apply-form\\\" data-v-a1ed73d8>\",\"</div>\",[_c('el-form',{ref:\"form\",attrs:{\"model\":_vm.form,\"rules\":_vm.rules,\"label-width\":\"100px\"}},[_c('el-form-item',{attrs:{\"label\":\"退款类型：\",\"prop\":\"applyType\"}},[_c('el-radio-group',{on:{\"change\":_vm.applyRadioChange},model:{value:(_vm.applyType),callback:function ($$v) {_vm.applyType=$$v},expression:\"applyType\"}},[_c('el-radio',{attrs:{\"label\":\"仅退款\"}}),_vm._v(\" \"),_c('el-radio',{attrs:{\"label\":\"退货退款\"}})],1)],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"退款原因：\",\"prop\":\"reason\"}},[_c('el-select',{attrs:{\"placeholder\":\"请选择\"},model:{value:(_vm.form.reason),callback:function ($$v) {_vm.$set(_vm.form, \"reason\", $$v)},expression:\"form.reason\"}},_vm._l((_vm.reason),function(item,index){return _c('el-option',{key:index,attrs:{\"label\":item,\"value\":item}})}),1)],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"退款说明：\",\"prop\":\"desc\"}},[_c('el-input',{staticStyle:{\"width\":\"600px\"},attrs:{\"type\":\"textarea\",\"placeholder\":\"退款说明（200字以内）\",\"maxlength\":\"200\",\"show-word-limit\":\"\",\"resize\":\"none\",\"rows\":\"5\"},model:{value:(_vm.form.desc),callback:function ($$v) {_vm.$set(_vm.form, \"desc\", $$v)},expression:\"form.desc\"}})],1),_vm._v(\" \"),_c('el-form-item',[_c('upload',{attrs:{\"isSlot\":\"\",\"file-list\":_vm.fileList,\"limit\":3},on:{\"remove\":_vm.uploadSuccess,\"success\":_vm.uploadSuccess}},[_c('div',{staticStyle:{\"height\":\"100%\"}},[_c('i',{staticClass:\"el-icon-camera xs\",staticStyle:{\"font-size\":\"24px\"}})])]),_vm._v(\" \"),_c('div',{staticClass:\"xs muted\"},[_vm._v(\"最多可上传3张图片，支持jpg、png格式，图片大小1M以内\")])],1),_vm._v(\" \"),_c('el-form-item',[_c('div',{staticClass:\"flex\"},[_c('el-button',{attrs:{\"type\":\"primary\"},on:{\"click\":_vm.onSubmit}},[_vm._v(\"提交申请\")]),_vm._v(\" \"),_c('div',{staticClass:\"m-l-20\"},[_vm._v(\"\\n                        退款金额：\"),_c('span',{staticClass:\"primary\"},[_vm._v(\"¥\"+_vm._s(_vm.goods.total_price))])])],1)])],1)],1)],2)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nexport default {\n    head() {\n        return {\n            title: this.$store.getters.headTitle,\n            link: [\n                {\n                    rel: \"icon\",\n                    type: \"image/x-icon\",\n                    href: this.$store.getters.favicon,\n                },\n            ],\n        };\n    },\n    layout: \"user\",\n    name: \"applySale\",\n    async asyncData({ $get, $post, query }) {\n        let goods = {};\n        let reason = [];\n        let res = await $get(\"after_sale/goodsInfo\", {\n            params: {\n                order_id: query.order_id,\n                item_id: query.item_id,\n            },\n        });\n        if (res.code == 1) {\n            goods = res.data.goods;\n            reason = res.data.reason;\n        }\n        return {\n            reason,\n            goods,\n        };\n    },\n    data() {\n        return {\n            applyType: \"仅退款\",\n            form: {\n                applyType: 0,\n                reason: \"\",\n                desc: \"\",\n            },\n            rules: {\n                applyType: [{ required: true, message: \"请选择退款类型\" }],\n                reason: [\n                    {\n                        required: true,\n                        message: \"请选择退款原因\",\n                        triggle: \"blur\",\n                    },\n                ],\n            },\n            fileList: [],\n        };\n    },\n    methods: {\n        applyRadioChange(value) {\n            value == \"仅退款\"\n                ? (this.form.applyType = 0)\n                : (this.form.applyType = 1);\n        },\n\n        onSubmit(e) {\n            this.$refs[\"form\"].validate((valid) => {\n                if (valid) {\n                    if (this.$route.query.afterSaleId) {\n                        this.applyAgainFun();\n                    } else {\n                        this.$applyAfterSale();\n                    }\n                } else {\n                    return false;\n                }\n            });\n        },\n\n        onUploadChange(e) {\n            let fileList = Object.assign([], this.fileList);\n            fileList.push(e);\n            this.fileList = fileList;\n            console.log(\"onChange\", e, \" fileList:\", this.fileList);\n        },\n\n        uploadSuccess(e) {\n            this.fileList = e.map((item) => item.response.data.uri);\n        },\n\n        // 重新申请\n        async $applyAgain() {\n            const data = {\n                id: this.$route.query.afterSaleId,\n                reason: this.form.reason,\n                refund_type: this.form.applyType,\n                remark: this.form.desc,\n                img: fileList.length <= 0 ? \"\" : this.fileList[0],\n            };\n            let res = await $post(\"after_sale/again\", data);\n            if (res.code == 1) {\n                this.$message({\n                    message: \"提交成功\",\n                    type: \"success\",\n                });\n                this.$router.push(\n                    \"/user/after_sales/apply_result?afterSaleId=\" +\n                        res.data.after_sale_id\n                );\n            }\n        },\n\n        // 初次申请售后\n        async $applyAfterSale() {\n            console.log(this.fileList[0])\n            const data = {\n                item_id: this.$route.query.item_id,\n                order_id: this.$route.query.order_id,\n                reason: this.form.reason,\n                refund_type: this.form.applyType,\n                remark: this.form.desc,\n                // 目前只支持单个\n                img: this.fileList[0],\n            };\n            let res = await this.$post(\"after_sale/add\", data);\n            if (res.code == 1) {\n                this.$message({\n                    message: \"提交成功\",\n                    type: \"success\",\n                });\n                this.$router.push(\n                    \"/user/after_sales/apply_result?afterSaleId=\" +\n                        res.data.after_sale_id\n                );\n            }\n        },\n    },\n};\n", "import mod from \"-!../../../node_modules/babel-loader/lib/index.js??ref--2-0!../../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./apply_sale.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/babel-loader/lib/index.js??ref--2-0!../../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./apply_sale.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./apply_sale.vue?vue&type=template&id=a1ed73d8&scoped=true&\"\nimport script from \"./apply_sale.vue?vue&type=script&lang=js&\"\nexport * from \"./apply_sale.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./apply_sale.vue?vue&type=style&index=0&id=a1ed73d8&lang=scss&scoped=true&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  \"a1ed73d8\",\n  \"675cfb80\"\n  \n)\n\nexport default component.exports\n\n/* nuxt-component-imports */\ninstallComponents(component, {PriceFormate: require('/Users/<USER>/Desktop/vue/pc/components/price-formate.vue').default,Upload: require('/Users/<USER>/Desktop/vue/pc/components/upload.vue').default})\n"], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACXA;AACA;AACA;AACA;AACA;AACA;;;;;ACLA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AADA;AAGA;AACA;AAAA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AADA;AAGA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AA5BA;AACA;AAgCA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAJA;AAKA;AACA;AACA;AAAA;AAAA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAZA;AAhDA;;AC1BA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACvBA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACNA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACXA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACNA;AACA;AACA;;;;;;;;ACFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAbA;AAkBA;AACA;AAAA;AACA;AACA;AADA;AAGA;AACA;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAFA;AAIA;AACA;AApBA;AA5BA;;ACvBA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;ACvBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACXA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACNA;AACA;AACA;;;;;ACFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAHA;AAHA;AAUA;AACA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAFA;AADA;AACA;AAKA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AAFA;AAIA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAHA;AAKA;AACA;AAAA;AAAA;AAAA;AACA;AAEA;AACA;AACA;AAHA;AAHA;AAUA;AAjBA;AAmBA;AACA;AAAA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AALA;AAOA;AACA;AAAA;AACA;AACA;AACA;AAFA;AAIA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAPA;AASA;AACA;AAAA;AACA;AACA;AACA;AAFA;AAIA;AAIA;AACA;AACA;AA9EA;AAtDA;;ACzEA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;A", "sourceRoot": ""}