<?php


namespace app\shopapi\controller;


use app\common\basics\ShopApi;
use app\common\server\JsonServer;
use app\shopapi\logic\VerificationLogic;
use app\shopapi\validate\VerificationValidate;
use app\shopapi\logic\OrderLogic;

/**
 * 自提核销
 * Class Verification
 * @package app\shopapi\controller
 */
class Verification extends ShopApi
{

    /**
     * @notes 核销订单
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2022/11/2 17:13
     */
    public function lists()
    {
        $params = $this->request->get();
        $result = VerificationLogic::lists($params, $this->page_no, $this->page_size, $this->shop_id);
        return JsonServer::success('获取成功', $result);
    }


    /**
     * @notes 订单详情
     * @return \think\response\Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date 2022/11/2 18:08
     */
    public function detail()
    {
        $params = $this->request->get();
        (new VerificationValidate())->goCheck('detail', ['shop_id' => $this->shop_id]);
        $result = VerificationLogic::detail($params, $this->shop_id);
        return JsonServer::success('', $result);
    }


    /**
     * @notes 核销订单
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2022/11/2 17:11
     */
    public function confirm()
    {
        $params = $this->request->post();
        (new VerificationValidate())->goCheck('confirm', ['shop_id' => $this->shop_id]);
        $result = VerificationLogic::verification($params, $this->shop);
        if(false === $result) {
            return JsonServer::error(OrderLogic::getError() ?: '操作失败');
        }
        return JsonServer::success('操作成功');
    }

}