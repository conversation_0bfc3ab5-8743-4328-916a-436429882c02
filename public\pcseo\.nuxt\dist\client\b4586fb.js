(window.webpackJsonp=window.webpackJsonp||[]).push([[28,5,13,16,18],{437:function(t,e,r){"use strict";var n=r(17),o=r(2),c=r(3),l=r(136),d=r(27),f=r(18),m=r(271),v=r(52),h=r(135),x=r(270),_=r(5),y=r(98).f,w=r(44).f,S=r(26).f,k=r(438),N=r(439).trim,C="Number",T=o.Number,E=T.prototype,I=o.TypeError,O=c("".slice),z=c("".charCodeAt),M=function(t){var e=x(t,"number");return"bigint"==typeof e?e:j(e)},j=function(t){var e,r,n,o,c,l,d,code,f=x(t,"number");if(h(f))throw I("Cannot convert a Symbol value to a number");if("string"==typeof f&&f.length>2)if(f=N(f),43===(e=z(f,0))||45===e){if(88===(r=z(f,2))||120===r)return NaN}else if(48===e){switch(z(f,1)){case 66:case 98:n=2,o=49;break;case 79:case 111:n=8,o=55;break;default:return+f}for(l=(c=O(f,2)).length,d=0;d<l;d++)if((code=z(c,d))<48||code>o)return NaN;return parseInt(c,n)}return+f};if(l(C,!T(" 0o1")||!T("0b1")||T("+0x1"))){for(var $,A=function(t){var e=arguments.length<1?0:T(M(t)),r=this;return v(E,r)&&_((function(){k(r)}))?m(Object(e),r,A):e},F=n?y(T):"MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,EPSILON,MAX_SAFE_INTEGER,MIN_SAFE_INTEGER,isFinite,isInteger,isNaN,isSafeInteger,parseFloat,parseInt,fromString,range".split(","),L=0;F.length>L;L++)f(T,$=F[L])&&!f(A,$)&&S(A,$,w(T,$));A.prototype=E,E.constructor=A,d(o,C,A)}},438:function(t,e,r){var n=r(3);t.exports=n(1..valueOf)},439:function(t,e,r){var n=r(3),o=r(33),c=r(16),l=r(440),d=n("".replace),f="["+l+"]",m=RegExp("^"+f+f+"*"),v=RegExp(f+f+"*$"),h=function(t){return function(e){var r=c(o(e));return 1&t&&(r=d(r,m,"")),2&t&&(r=d(r,v,"")),r}};t.exports={start:h(1),end:h(2),trim:h(3)}},440:function(t,e){t.exports="\t\n\v\f\r                　\u2028\u2029\ufeff"},441:function(t,e,r){var content=r(444);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,r(14).default)("3181fc86",content,!0,{sourceMap:!1})},442:function(t,e,r){"use strict";r.r(e);r(437),r(80),r(272);var n={data:function(){return{priceSlice:{}}},components:{},props:{firstSize:{type:Number,default:14},secondSize:{type:Number,default:14},color:{type:String},weight:{type:[String,Number],default:400},price:{type:[String,Number],default:""},showSubscript:{type:Boolean,default:!0},subscriptSize:{type:Number,default:14},lineThrough:{type:Boolean,default:!1}},created:function(){this.priceFormat()},watch:{price:function(t){this.priceFormat()}},methods:{priceFormat:function(){var t=this.price,e={};null!==t&&(t=parseFloat(t),t=String(t).split("."),e.first=t[0],e.second=t[1],this.priceSlice=e)}}},o=(r(443),r(9)),component=Object(o.a)(n,(function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("span",{class:(t.lineThrough?"line-through":"")+"price-format",style:{color:t.color,"font-weight":t.weight}},[t.showSubscript?r("span",{style:{"font-size":t.subscriptSize+"px","margin-right":"1px"}},[t._v("¥")]):t._e(),t._v(" "),r("span",{style:{"font-size":t.firstSize+"px","margin-right":"1px"}},[t._v(t._s(t.priceSlice.first))]),t._v(" "),t.priceSlice.second?r("span",{style:{"font-size":t.secondSize+"px"}},[t._v("."+t._s(t.priceSlice.second))]):t._e()])}),[],!1,null,null,null);e.default=component.exports},443:function(t,e,r){"use strict";r(441)},444:function(t,e,r){var n=r(13)(!1);n.push([t.i,".price-format{display:flex;align-items:baseline}",""]),t.exports=n},445:function(t,e,r){var content=r(447);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,r(14).default)("12a18d22",content,!0,{sourceMap:!1})},446:function(t,e,r){"use strict";r(445)},447:function(t,e,r){var n=r(13)(!1);n.push([t.i,".null-data[data-v-93598fb0]{padding:100px}.null-data .img-null[data-v-93598fb0]{width:150px;height:150px}",""]),t.exports=n},448:function(t,e,r){"use strict";r.r(e);var n={components:{},props:{img:{type:String},text:{type:String,default:"暂无数据"},imgStyle:{type:String,default:""}},methods:{}},o=(r(446),r(9)),component=Object(o.a)(n,(function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",{staticClass:"bg-white flex-col col-center null-data"},[r("img",{staticClass:"img-null",style:t.imgStyle,attrs:{src:t.img,alt:""}}),t._v(" "),r("div",{staticClass:"muted mt8"},[t._v(t._s(t.text))])])}),[],!1,null,"93598fb0",null);e.default=component.exports},450:function(t,e,r){"use strict";r.d(e,"b",(function(){return o})),r.d(e,"a",(function(){return c}));var n=r(34);r(80),r(272),r(101),r(61),r(24),r(38),r(62),r(45),r(19),r(63),r(64),r(46);var o=function(t){var time=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1e3,e=arguments.length>2?arguments[2]:void 0,r=new Date(0).getTime();return function(){var n=(new Date).getTime();if(n-r>time){for(var o=arguments.length,c=new Array(o),l=0;l<o;l++)c[l]=arguments[l];t.apply(e,c),r=n}}};function c(t){var p="";if("object"==Object(n.a)(t)){for(var e in p="?",t)p+="".concat(e,"=").concat(t[e],"&");p=p.slice(0,-1)}return p}},451:function(t,e,r){var content=r(456);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,r(14).default)("1469a4e1",content,!0,{sourceMap:!1})},452:function(t,e,r){var content=r(461);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,r(14).default)("532bec65",content,!0,{sourceMap:!1})},455:function(t,e,r){"use strict";r(451)},456:function(t,e,r){var n=r(13)(!1);n.push([t.i,".goods-list[data-v-060944d1]{align-items:stretch}.goods-list .goods-item[data-v-060944d1]{display:block;box-sizing:border-box;width:224px;height:310px;margin-bottom:16px;padding:12px 12px 16px;border-radius:4px;transition:all .2s}.goods-list .goods-item[data-v-060944d1]:hover{transform:translateY(-8px);box-shadow:0 0 6px rgba(0,0,0,.1)}.goods-list .goods-item .goods-img[data-v-060944d1]{width:200px;height:200px}.goods-list .goods-item .name[data-v-060944d1]{margin-bottom:10px;height:40px;line-height:20px}.goods-list .goods-item .seckill .btn[data-v-060944d1]{padding:4px 12px;border-radius:4px;border:1px solid transparent}.goods-list .goods-item .seckill .btn.not-start[data-v-060944d1]{border-color:#ff2c3c;color:#ff2c3c;background-color:transparent}.goods-list .goods-item .seckill .btn.end[data-v-060944d1]{background-color:#e5e5e5;color:#fff}",""]),t.exports=n},457:function(t,e,r){"use strict";var n=r(7),o=r(458);n({target:"String",proto:!0,forced:r(459)("link")},{link:function(t){return o(this,"a","href",t)}})},458:function(t,e,r){var n=r(3),o=r(33),c=r(16),l=/"/g,d=n("".replace);t.exports=function(t,e,r,n){var f=c(o(t)),m="<"+e;return""!==r&&(m+=" "+r+'="'+d(c(n),l,"&quot;")+'"'),m+">"+f+"</"+e+">"}},459:function(t,e,r){var n=r(5);t.exports=function(t){return n((function(){var e=""[t]('"');return e!==e.toLowerCase()||e.split('"').length>3}))}},460:function(t,e,r){"use strict";r(452)},461:function(t,e,r){var n=r(13)(!1);n.push([t.i,".ad-item[data-v-368017b1]{width:100%;height:100%;cursor:pointer}",""]),t.exports=n},462:function(t,e,r){"use strict";r.r(e);r(437);var n={props:{list:{type:Array,default:function(){return[]}},num:{type:Number,default:5},type:{type:String},status:{type:Number}},watch:{list:{immediate:!0,handler:function(t){}}},computed:{getSeckillText:function(){switch(this.status){case 0:return"未开始";case 1:return"立即抢购";case 2:return"已结束"}}}},o=(r(455),r(9)),component=Object(o.a)(n,(function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",{staticClass:"goods-list flex flex-wrap"},t._l(t.list,(function(e,n){return r("nuxt-link",{key:n,staticClass:"goods-item bg-white",style:{marginRight:(n+1)%t.num==0?0:"14px"},attrs:{to:"/goods_details/"+(e.id||e.goods_id)}},[r("el-image",{staticClass:"goods-img",attrs:{lazy:"",src:e.image||e.goods_image,alt:""}}),t._v(" "),r("div",{staticClass:"name line-2"},[t._v(t._s(e.name||e.goods_name))]),t._v(" "),"seckill"==t.type?r("div",{staticClass:"seckill flex row-between"},[r("div",{staticClass:"primary flex"},[t._v("\n                秒杀价\n                "),r("price-formate",{attrs:{price:e.seckill_price,"first-size":18}})],1),t._v(" "),r("div",{class:["btn bg-primary white",{"not-start":0==t.status,end:2==t.status}]},[t._v(t._s(t.getSeckillText)+"\n            ")])]):r("div",{staticClass:"flex row-between flex-wrap"},[r("div",{staticClass:"price flex col-baseline"},[r("div",{staticClass:"primary m-r-8"},[r("price-formate",{attrs:{price:e.min_price||e.price,"first-size":16}})],1),t._v(" "),r("div",{staticClass:"muted sm line-through"},[r("price-formate",{attrs:{price:e.market_price}})],1)]),t._v(" "),r("div",{staticClass:"muted xs"},[t._v(t._s(e.sales_total||e.sales_sum||0)+"人购买")])])],1)})),1)}),[],!1,null,"060944d1",null);e.default=component.exports;installComponents(component,{PriceFormate:r(442).default})},463:function(t,e,r){"use strict";r.r(e);r(457),r(82);var n=r(450),o={components:{},props:{item:{type:Object,default:function(){return{}}}},methods:{goPage:function(t){var e=t.link_type,link=t.link,r=t.params;if(3===e)window.open(t.link);else["/goods_details"].includes(link)?link+="/".concat(r.id):link+=Object(n.a)(r),this.$router.push({path:link})}}},c=(r(460),r(9)),component=Object(c.a)(o,(function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",{staticClass:"ad-item",on:{click:function(e){return e.stopPropagation(),t.goPage(t.item)}}},[r("el-image",{staticStyle:{width:"100%",height:"100%"},attrs:{src:t.item.image,fit:"cover"}})],1)}),[],!1,null,"368017b1",null);e.default=component.exports},464:function(t,e,r){t.exports=r.p+"img/goods_null.38f1689.png"},554:function(t,e,r){var content=r(645);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,r(14).default)("4d39614a",content,!0,{sourceMap:!1})},644:function(t,e,r){"use strict";r(554)},645:function(t,e,r){var n=r(13)(!1);n.push([t.i,".goods-list .banner img{width:100%;display:block}.goods-list .sort{padding:15px 16px}.goods-list .sort .sort-name .item{margin-right:30px;cursor:pointer}.goods-list .sort .sort-name .item.active{color:#ff2c3c}",""]),t.exports=n},682:function(t,e,r){"use strict";r.r(e);var n=r(6),o=(r(38),r(51),r(450)),c={head:function(){return{title:this.$store.getters.headTitle,link:[{rel:"icon",type:"image/x-icon",href:this.$store.getters.favicon}]}},asyncData:function(t){return Object(n.a)(regeneratorRuntime.mark((function e(){var r,n,o,c,l,d,data;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:r=t.$get,n=t.params,t.query,o=n.type,c=0,l=[],e.t0=o,e.next="1"===e.t0?7:"2"===e.t0?9:11;break;case 7:return c=25,e.abrupt("break",11);case 9:return c=26,e.abrupt("break",11);case 11:return e.next=13,r("ad/lists",{params:{pid:c,terminal:2}});case 13:if(d=e.sent,(data=d.data).length){e.next=17;break}return e.abrupt("return");case 17:return l=data,e.abrupt("return",{ad:l});case 19:case"end":return e.stop()}}),e)})))()},components:{},data:function(){return{sortType:"",swiperOptions:{width:1180},saleSort:"desc",priceSort:"desc",page:"",count:0,ad:"",goodsList:[]}},created:function(){this.getGoods(),this.changeSortType=Object(o.b)(this.changeSortType,500,this)},methods:{changeSortType:function(t){switch(this.sortType=t,t){case"price":"asc"==this.priceSort?this.priceSort="desc":"desc"==this.priceSort&&(this.priceSort="asc");break;case"sales_sum":"asc"==this.saleSort?this.saleSort="desc":"desc"==this.saleSort&&(this.saleSort="asc")}this.getGoods()},changePage:function(t){this.page=t,this.getGoods()},getGoods:function(){var t=this;return Object(n.a)(regeneratorRuntime.mark((function e(){var r,n,o,c,l,d,f,m,v;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:r=t.$route.query.name,n=t.priceSort,o=t.sortType,c=t.saleSort,l="",e.t0=o,e.next="price"===e.t0?6:"sales_sum"===e.t0?8:10;break;case 6:return l=n,e.abrupt("break",10);case 8:return l=c,e.abrupt("break",10);case 10:return e.next=12,t.$get("pc/goodsList",{params:{page_size:20,page_no:t.page,sort_type:o,sort:l,name:r}});case 12:d=e.sent,f=d.data,m=f.list,v=f.count,t.count=v,t.goodsList=m;case 18:case"end":return e.stop()}}),e)})))()}},watch:{"$route.query.name":function(){this.getGoods()}}},l=(r(644),r(9)),component=Object(l.a)(c,(function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"goods-list"},[n("div",{staticClass:"banner m-t-16"},[n("client-only",[n("swiper",{ref:"mySwiper",attrs:{options:t.swiperOptions}},t._l(t.ad,(function(t,e){return n("swiper-slide",{key:e,staticClass:"swiper-item"},[n("ad-item",{attrs:{item:t}})],1)})),1)],1)],1),t._v(" "),n("div",{staticClass:"sort m-b-16 flex bg-white"},[n("div",{staticClass:"sort-title"},[t._v("排序方式：")]),t._v(" "),n("div",{staticClass:"sort-name m-l-16 flex"},[n("div",{class:["item",{active:""==t.sortType}],on:{click:function(e){return t.changeSortType("")}}},[t._v("\n                综合\n            ")]),t._v(" "),n("div",{class:["item",{active:"price"==t.sortType}],on:{click:function(e){return t.changeSortType("price")}}},[t._v("\n                价格\n                "),n("i",{directives:[{name:"show",rawName:"v-show",value:"desc"==t.priceSort,expression:"priceSort == 'desc'"}],staticClass:"el-icon-arrow-down"}),t._v(" "),n("i",{directives:[{name:"show",rawName:"v-show",value:"asc"==t.priceSort,expression:"priceSort == 'asc'"}],staticClass:"el-icon-arrow-up"})]),t._v(" "),n("div",{class:["item",{active:"sales_sum"==t.sortType}],on:{click:function(e){return t.changeSortType("sales_sum")}}},[t._v("\n                销量\n                "),n("i",{directives:[{name:"show",rawName:"v-show",value:"desc"==t.saleSort,expression:"saleSort == 'desc'"}],staticClass:"el-icon-arrow-down"}),t._v(" "),n("i",{directives:[{name:"show",rawName:"v-show",value:"asc"==t.saleSort,expression:"saleSort == 'asc'"}],staticClass:"el-icon-arrow-up"})])])]),t._v(" "),t.goodsList.length?[n("goods-list",{attrs:{list:t.goodsList}}),t._v(" "),t.count?n("div",{staticClass:"pagination flex m-t-30 row-center",staticStyle:{"padding-bottom":"38px"}},[n("el-pagination",{attrs:{background:"",layout:"prev, pager, next",total:t.count,"prev-text":"上一页","next-text":"下一页","hide-on-single-page":"","page-size":20},on:{"current-change":t.changePage}})],1):t._e()]:n("null-data",{attrs:{img:r(464),text:"暂无商品~"}})],2)}),[],!1,null,null,null);e.default=component.exports;installComponents(component,{AdItem:r(463).default,GoodsList:r(462).default,NullData:r(448).default})}}]);