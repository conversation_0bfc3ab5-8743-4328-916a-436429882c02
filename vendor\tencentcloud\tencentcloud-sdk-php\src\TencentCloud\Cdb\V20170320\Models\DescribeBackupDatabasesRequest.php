<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Cdb\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * DescribeBackupDatabases请求参数结构体
 *
 * @method string getInstanceId() 获取实例 ID，格式如：cdb-c1nl9rpv。与云数据库控制台页面中显示的实例 ID 相同。
 * @method void setInstanceId(string $InstanceId) 设置实例 ID，格式如：cdb-c1nl9rpv。与云数据库控制台页面中显示的实例 ID 相同。
 * @method string getStartTime() 获取开始时间，格式为：2017-07-12 10:29:20。
 * @method void setStartTime(string $StartTime) 设置开始时间，格式为：2017-07-12 10:29:20。
 * @method string getSearchDatabase() 获取要查询的数据库名前缀。
 * @method void setSearchDatabase(string $SearchDatabase) 设置要查询的数据库名前缀。
 * @method integer getOffset() 获取分页偏移量。
 * @method void setOffset(integer $Offset) 设置分页偏移量。
 * @method integer getLimit() 获取分页大小，最小值为1，最大值为2000。
 * @method void setLimit(integer $Limit) 设置分页大小，最小值为1，最大值为2000。
 */
class DescribeBackupDatabasesRequest extends AbstractModel
{
    /**
     * @var string 实例 ID，格式如：cdb-c1nl9rpv。与云数据库控制台页面中显示的实例 ID 相同。
     */
    public $InstanceId;

    /**
     * @var string 开始时间，格式为：2017-07-12 10:29:20。
     */
    public $StartTime;

    /**
     * @var string 要查询的数据库名前缀。
     */
    public $SearchDatabase;

    /**
     * @var integer 分页偏移量。
     */
    public $Offset;

    /**
     * @var integer 分页大小，最小值为1，最大值为2000。
     */
    public $Limit;

    /**
     * @param string $InstanceId 实例 ID，格式如：cdb-c1nl9rpv。与云数据库控制台页面中显示的实例 ID 相同。
     * @param string $StartTime 开始时间，格式为：2017-07-12 10:29:20。
     * @param string $SearchDatabase 要查询的数据库名前缀。
     * @param integer $Offset 分页偏移量。
     * @param integer $Limit 分页大小，最小值为1，最大值为2000。
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("InstanceId",$param) and $param["InstanceId"] !== null) {
            $this->InstanceId = $param["InstanceId"];
        }

        if (array_key_exists("StartTime",$param) and $param["StartTime"] !== null) {
            $this->StartTime = $param["StartTime"];
        }

        if (array_key_exists("SearchDatabase",$param) and $param["SearchDatabase"] !== null) {
            $this->SearchDatabase = $param["SearchDatabase"];
        }

        if (array_key_exists("Offset",$param) and $param["Offset"] !== null) {
            $this->Offset = $param["Offset"];
        }

        if (array_key_exists("Limit",$param) and $param["Limit"] !== null) {
            $this->Limit = $param["Limit"];
        }
    }
}
