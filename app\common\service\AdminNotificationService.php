<?php

namespace app\common\service;

use app\common\websocket\AdminNotificationHandler;
use think\facade\Log;
use think\facade\Cache;

/**
 * 管理员通知服务类
 * 用于在服务器端触发管理员通知
 */
class AdminNotificationService
{
    /**
     * WebSocket处理器实例
     * @var AdminNotificationHandler|null
     */
    protected static $wsHandler = null;

    /**
     * 缓存前缀
     * @var string
     */
    protected static $cachePrefix = 'admin_notification_';

    /**
     * 通知类型映射
     * @var array
     */
    protected static $typeMap = [
        'system' => 'system_notification',
        'error' => 'error_notification',
        'warning' => 'warning_notification',
        'info' => 'info_notification',
        'success' => 'success_notification',
        'admin' => 'admin_notification',
        'default' => 'admin_notification'
    ];

    /**
     * 图标映射
     * @var array
     */
    protected static $iconMap = [
        'system_notification' => 1,
        'error_notification' => 2,
        'warning_notification' => 3,
        'info_notification' => 4,
        'success_notification' => 1,
        'admin_notification' => 0
    ];

    /**
     * 设置WebSocket处理器
     * @param AdminNotificationHandler $handler
     */
    public static function setWebSocketHandler(AdminNotificationHandler $handler)
    {
        self::$wsHandler = $handler;
    }

    /**
     * 发送通知给所有管理员
     * @param string $title 通知标题
     * @param string $content 通知内容
     * @param string $type 通知类型
     * @param string $url 跳转链接
     * @param int|null $icon 图标（可选，会根据类型自动设置）
     * @param array $extra 额外数据
     * @return bool 发送结果
     */
    public static function sendNotification($title, $content, $type = 'admin', $url = '', $icon = null, $extra = [])
    {
        try {
            // 标准化通知类型
            $notificationType = self::normalizeType($type);
            
            // 自动设置图标
            if ($icon === null) {
                $icon = self::$iconMap[$notificationType] ?? 0;
            }

            // 构建通知数据
            $notificationData = array_merge([
                'type' => $notificationType,
                'title' => $title,
                'content' => $content,
                'url' => $url,
                'icon' => $icon,
                'timestamp' => time(),
                'id' => self::generateNotificationId()
            ], $extra);

            Log::info('准备发送管理员通知: ' . json_encode($notificationData, JSON_UNESCAPED_UNICODE));

            // 记录通知到缓存（用于统计和历史记录）
            self::recordNotification($notificationData);

            // 通过WebSocket发送通知
            $wsResult = self::sendViaWebSocket($notificationData);

            // 通过HTTP API发送通知（备用方案）
            $httpResult = self::sendViaHttpApi($notificationData);

            $success = $wsResult || $httpResult;

            if ($success) {
                Log::info('管理员通知发送成功: ' . $title);
            } else {
                Log::error('管理员通知发送失败: ' . $title);
            }

            return $success;

        } catch (\Throwable $e) {
            Log::error('发送管理员通知异常: ' . $e->getMessage() . "\n" . $e->getTraceAsString());
            return false;
        }
    }

    /**
     * 发送系统通知
     * @param string $title 标题
     * @param string $content 内容
     * @param string $url 链接
     * @return bool
     */
    public static function sendSystemNotification($title, $content, $url = '')
    {
        return self::sendNotification($title, $content, 'system', $url);
    }

    /**
     * 发送错误通知
     * @param string $title 标题
     * @param string $content 内容
     * @param string $url 链接
     * @return bool
     */
    public static function sendErrorNotification($title, $content, $url = '')
    {
        return self::sendNotification($title, $content, 'error', $url);
    }

    /**
     * 发送警告通知
     * @param string $title 标题
     * @param string $content 内容
     * @param string $url 链接
     * @return bool
     */
    public static function sendWarningNotification($title, $content, $url = '')
    {
        return self::sendNotification($title, $content, 'warning', $url);
    }

    /**
     * 发送信息通知
     * @param string $title 标题
     * @param string $content 内容
     * @param string $url 链接
     * @return bool
     */
    public static function sendInfoNotification($title, $content, $url = '')
    {
        return self::sendNotification($title, $content, 'info', $url);
    }

    /**
     * 发送成功通知
     * @param string $title 标题
     * @param string $content 内容
     * @param string $url 链接
     * @return bool
     */
    public static function sendSuccessNotification($title, $content, $url = '')
    {
        return self::sendNotification($title, $content, 'success', $url);
    }

    /**
     * 批量发送通知
     * @param array $notifications 通知列表
     * @return array 发送结果
     */
    public static function sendBatchNotifications($notifications)
    {
        $results = [];
        
        foreach ($notifications as $index => $notification) {
            $title = $notification['title'] ?? '批量通知';
            $content = $notification['content'] ?? '';
            $type = $notification['type'] ?? 'admin';
            $url = $notification['url'] ?? '';
            $icon = $notification['icon'] ?? null;
            $extra = $notification['extra'] ?? [];

            $result = self::sendNotification($title, $content, $type, $url, $icon, $extra);
            $results[$index] = $result;

            // 避免发送过快，添加小延迟
            if (count($notifications) > 1) {
                usleep(100000); // 0.1秒
            }
        }

        return $results;
    }

    /**
     * 通过WebSocket发送通知
     * @param array $notificationData 通知数据
     * @return bool
     */
    protected static function sendViaWebSocket($notificationData)
    {
        try {
            if (self::$wsHandler) {
                return self::$wsHandler->sendNotificationToAdmin(
                    $notificationData['title'],
                    $notificationData['content'],
                    $notificationData['type'],
                    $notificationData['url'],
                    $notificationData['icon']
                );
            }

            Log::warning('WebSocket处理器未设置，跳过WebSocket发送');
            return false;

        } catch (\Throwable $e) {
            Log::error('WebSocket发送通知失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 通过HTTP API发送通知（备用方案）
     * @param array $notificationData 通知数据
     * @return bool
     */
    protected static function sendViaHttpApi($notificationData)
    {
        try {
            // 这里可以实现HTTP API调用，比如调用外部通知服务
            // 或者存储到数据库中，由前端轮询获取

            // 示例：存储到缓存中，供前端轮询
            $cacheKey = self::$cachePrefix . 'pending_' . time() . '_' . mt_rand(1000, 9999);
            Cache::set($cacheKey, $notificationData, 300); // 5分钟过期

            Log::info('通知已存储到缓存: ' . $cacheKey);
            return true;

        } catch (\Throwable $e) {
            Log::error('HTTP API发送通知失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 标准化通知类型
     * @param string $type 原始类型
     * @return string 标准化后的类型
     */
    protected static function normalizeType($type)
    {
        return self::$typeMap[$type] ?? self::$typeMap['default'];
    }

    /**
     * 生成通知ID
     * @return string
     */
    protected static function generateNotificationId()
    {
        return 'notify_' . date('YmdHis') . '_' . mt_rand(1000, 9999);
    }

    /**
     * 记录通知到缓存
     * @param array $notificationData 通知数据
     */
    protected static function recordNotification($notificationData)
    {
        try {
            $recordKey = self::$cachePrefix . 'history_' . date('Y-m-d');
            $history = Cache::get($recordKey, []);
            
            $history[] = [
                'id' => $notificationData['id'],
                'title' => $notificationData['title'],
                'type' => $notificationData['type'],
                'timestamp' => $notificationData['timestamp'],
                'sent_at' => date('Y-m-d H:i:s')
            ];

            // 只保留最近100条记录
            if (count($history) > 100) {
                $history = array_slice($history, -100);
            }

            Cache::set($recordKey, $history, 86400); // 24小时过期

        } catch (\Throwable $e) {
            Log::error('记录通知历史失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取通知历史记录
     * @param string $date 日期（Y-m-d格式）
     * @return array
     */
    public static function getNotificationHistory($date = null)
    {
        if (!$date) {
            $date = date('Y-m-d');
        }

        $recordKey = self::$cachePrefix . 'history_' . $date;
        return Cache::get($recordKey, []);
    }

    /**
     * 获取待处理的通知（用于前端轮询）
     * @return array
     */
    public static function getPendingNotifications()
    {
        try {
            $pattern = self::$cachePrefix . 'pending_*';
            $keys = Cache::store('redis')->keys($pattern);
            
            $notifications = [];
            foreach ($keys as $key) {
                $notification = Cache::get($key);
                if ($notification) {
                    $notifications[] = $notification;
                    // 获取后删除
                    Cache::delete($key);
                }
            }

            return $notifications;

        } catch (\Throwable $e) {
            Log::error('获取待处理通知失败: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * 清理过期的通知记录
     */
    public static function cleanExpiredNotifications()
    {
        try {
            // 清理7天前的历史记录
            for ($i = 7; $i <= 30; $i++) {
                $date = date('Y-m-d', strtotime("-{$i} days"));
                $recordKey = self::$cachePrefix . 'history_' . $date;
                Cache::delete($recordKey);
            }

            Log::info('清理过期通知记录完成');

        } catch (\Throwable $e) {
            Log::error('清理过期通知记录失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取通知统计信息
     * @param string $date 日期
     * @return array
     */
    public static function getNotificationStats($date = null)
    {
        if (!$date) {
            $date = date('Y-m-d');
        }

        $history = self::getNotificationHistory($date);
        
        $stats = [
            'total' => count($history),
            'by_type' => [],
            'by_hour' => []
        ];

        foreach ($history as $record) {
            // 按类型统计
            $type = $record['type'];
            if (!isset($stats['by_type'][$type])) {
                $stats['by_type'][$type] = 0;
            }
            $stats['by_type'][$type]++;

            // 按小时统计
            $hour = date('H', $record['timestamp']);
            if (!isset($stats['by_hour'][$hour])) {
                $stats['by_hour'][$hour] = 0;
            }
            $stats['by_hour'][$hour]++;
        }

        return $stats;
    }
}