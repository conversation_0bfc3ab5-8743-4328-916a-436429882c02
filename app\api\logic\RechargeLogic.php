<?php
namespace app\api\logic;

use app\api\logic\PayLogic;
use app\common\basics\Logic;
use app\common\model\JcaiTemplate;
use app\common\model\RechargeTemplate;
use app\common\model\RechargeOrder;
use app\common\model\shop\ShopLevel;
use app\common\server\ConfigServer;
use app\common\enum\PayEnum;
use app\common\server\UrlServer;
use think\facade\Db;
use app\common\logic\AccountLogLogic;
use app\common\model\AccountLog;
use app\common\model\shop\ShopDeposit;
use app\common\model\shop\ShopDepositDetails;
use app\common\enum\ClientEnum;
use app\common\enum\OrderEnum;

class RechargeLogic extends Logic
{
    public static function getTemplate(){
        $list = RechargeTemplate::where(['del'=>0])
            ->order('sort desc')
            ->field('id,money,give_money,is_recommend')
            ->select()
            ->toArray();

        foreach ($list as &$item){
            $item['tips'] = '';
            if($item['give_money'] > 0){
                $item['tips'] = '充'.intval($item['money']).'赠送'.intval($item['give_money']).'元';
            }
        }
        return $list;
    }


    public static function getuzhuTemplate($shop_id=0){

        $list['entry_fee'] = ConfigServer::get('shop_entry', 'entry_fee', 0);
        $list['ins_fee'] = ConfigServer::get('shop_entry', 'ins_fee', 0);
        $list['zins_fee'] = ConfigServer::get('shop_entry', 'zins_fee', 0);
        $shop_level= Db::name('shop_level')->field('name,remark,image')->where(['del'=>0])->order('id desc')->select()->toArray();
        foreach ($shop_level as $key=>&$val){
            $val['image']=UrlServer::getFileUrl($val['image']);
        }
        $list['admin_mobile']=Db::name('user')->where('shop_id',$shop_id)->value('mobile');
        $list['shop_level'] = $shop_level;
        $image_data=Db::name('ad')->where('pid',78)->where('del',0)->where('status',1)->order('sort asc')->column('image');
        foreach($image_data as $key=>&$val){
            $image_data[$key]=UrlServer::getFileUrl($val);
        }
        $list['background_image'] = $image_data;
        return $list;
    }
    public static function recharge($user_id,$client,$post)
    {
        try{
            $give_growth  = ConfigServer::get('recharge', 'give_growth', 0);

            //充值模板
            if(isset($post['id'])){
                $template = RechargeTemplate::where(['del'=>0,'id'=>$post['id']])
                    ->field('id,money,give_money')
                    ->findOrEmpty();
                if($template->isEmpty()) {
                    throw new \think\Exception('充值模板不存在');
                }
                $money = $template['money'];
                $give_money = $template['give_money'];

            }else{//自定义充值金额
                $template = RechargeTemplate::where(['del'=>0,'money'=>$post['money']])
                    ->field('id,money,give_money')
                    ->findOrEmpty();
                $money = $post['money'];
                $give_money = 0;
                if(!$template->isEmpty()){
                    $money = $template['money'];
                    $give_money = $template['give_money'];
                }
            }
            //赠送的积分和成长值
            $growth = $money * $give_growth;
            $growth = $growth > 0 ? intval($growth) : 0;

            $add_order = [
                'user_id'       => $user_id,
                'order_sn'      => createSn('recharge_order','order_sn'),
                'order_amount'  => $money,
                'order_source'  => $client,
                'pay_status'    => PayEnum::UNPAID,    //待支付状态；
                'pay_way'       => $post['pay_way'] ?? 1,
                'template_id'   => $template['id'] ?? 0,
                'give_money'    => $give_money,
                'give_growth'   => $growth,
                'create_time'   => time(),
            ];

            $id = Db::name('recharge_order')->insertGetId($add_order);
            if($id){
                return Db::name('recharge_order')->where(['id'=>$id])->field('id,order_sn,give_growth')->find();
            }
            return [];
        }catch(\Exception $e) {
            self::$error = $e->getMessage();
            return false;
        }
    }
    public static function rejcharge($user_id,$client,$post)
    {
        try{

            //集采购模板
            if(isset($post['id'])){
                $template = JcaiTemplate::where(['del'=>0,'id'=>$post['id']])
                    ->field('id,money,give_type,types')
                    ->findOrEmpty();
                if($template->isEmpty()) {
                    throw new \think\Exception('会员模板不存在');
                }
                $money = $template['money'];
            }
            //计算一个月的时间
            //一个月的时间
            $time = strtotime('+1 month')-time();
            //一季度的时间戳
            $time1 = strtotime('+3 month')-time();
            //一年的时间戳
            $time2 = strtotime('+1 year')-time();
            //两年的时间戳
            $time3 = strtotime('+2 year')-time();
            //终身的时间戳
            $time4 = strtotime('+100 year')-time();
            $dateTime=[$time,$time1,$time2,$time3,$time4];
            $add_order = [
                'user_id'       => $user_id,
                'order_sn'      => createSn('jcai_order','order_sn'),
                'order_amount'  => $money,
                'order_source'  => $client,
                'buy_time'  => $dateTime[$template['give_type']],
                'pay_status'    => PayEnum::UNPAID,    //待支付状态；
                'pay_way'       => $post['pay_way'] ?? 1,
                'template_id'   => $template['id'] ?? 0,
                'create_time'   => time(),
            ];

            $id = Db::name('jcai_order')->insertGetId($add_order);
            if($id){
                return Db::name('jcai_order')->where(['id'=>$id])->field('id,order_sn')->find();
            }
            return [];
        }catch(\Exception $e) {
            self::$error = $e->getMessage();
            return false;
        }
    }
    public static function ruzhucharge($user_id,$post)
    {
        try{
            $shop=Db::name('shop_apply')->where(['user_id'=>$user_id])->find();
            if(!$shop){
                self::$error = '当前没有申请入驻信息';
                return false;
            }
            if($post['feetype']==0){
                $money   = ConfigServer::get('shop_entry', 'entry_fee', 0);
            }else if($post['feetype']==1){
                $money         = ConfigServer::get('shop_entry', 'zins_fee',0);
            }else {
                $money = ConfigServer::get('shop_entry', 'ins_fee', 0);
            }
            $add_order = [
                'user_id'       => $user_id,
                'shop_id'       => 0,
                'order_sn'      => createSn('shop_merchantfees','order_sn'),
                'amount'  => $money,
                'feetype'  => $post['feetype'],
                'status'    => PayEnum::UNPAID,    //待支付状态；
                'created_at'   => date('Y-m-d H:i:s',time()),
            ];

            $id = Db::name('shop_merchantfees')->insertGetId($add_order);
            if($id){
                return Db::name('shop_merchantfees')->where(['id'=>$id])->field('id,order_sn,amount')->find();
            }
            return [];
        }catch(\Exception $e) {
            self::$error = $e->getMessage();
            return false;
        }
    }
    public static function rechargeRecord($get)
    {
        $list = RechargeOrder::field('order_sn, order_amount, give_money, create_time')
            ->where([
                'user_id' => $get['user_id'],
//                'pay_status' => PayEnum::UNPAID, // 已支付的
            ])
            ->order('create_time', 'desc')
            ->page($get['page_no'], $get['page_size'])
            ->select()
            ->toArray();
        $count = RechargeOrder::where([
                'user_id' => $get['user_id'],
//                'pay_status' => PayEnum::UNPAID
            ])
            ->count();

        foreach($list as &$item) {
            if($item['give_money'] > 0) {
                $item['desc'] = '充值'. clearZero($item['order_amount']) . '赠送' . clearZero($item['give_money']);
            }else{
                $item['desc'] = '充值'. clearZero($item['order_amount']);
            }
            $item['total'] = $item['order_amount'] + $item['give_money']; // 充值金额 + 赠送金额
        }

        $result = [
            'count' => $count,
            'lists' => $list,
            'more' =>  is_more($count, $get['page_no'], $get['page_size']),
            'page_no' =>  $get['page_no'],
            'page_size' =>  $get['page_size']
        ];

        return $result;
    }



    /*
     * 缴纳代理费
     */
    public static function agentcharge($user_id,$post)
    {

        try{
            $shop=Db::name('agent')->where(['user_id'=>$user_id])->find();
            if($shop){
                self::$error = '您已经是代理了';
                return false;
            }
            $custom_deposit_amount=Db::name('user')->where(['id'=>$user_id])->value('custom_deposit_amount');
            $money=$custom_deposit_amount?$custom_deposit_amount:ConfigServer::get('agent', 'agent_deposit_amount', 0);#代理费
            if(empty($money) || $money=='0.00'){
                self::$error = '养老顾问保证金最低设置金额不能小于0.01';
                return false;
            }  
            Db::name('agent_merchantfees')->where(
                ['user_id'=>$user_id,'status'=>PayEnum::UNPAID]
            )->delete();
            $add_order = [
                'user_id'       => $user_id,
                'order_sn'      => createSn('agent_merchantfees','order_sn'),
                'amount'  => $money,
                'qian_url'  => $post['qian_url']??'',
                'status'    => PayEnum::UNPAID,    //待支付状态；
                'created_at'   =>date('Y-m-d H:i:s',time()),
            ];
            $id = Db::name('agent_merchantfees')->insertGetId($add_order);
            if($id){
                return Db::name('agent_merchantfees')->where(['id'=>$id])->field('id,order_sn,amount')->find();
            }
            return [];
        }catch(\Exception $e) {
            self::$error = $e->getMessage();
            return false;
        }
    }


    /*
     * 商家缴纳保证金
     */
    public static function bondcharge($shop_id, $post)
    {
        Db::startTrans();
        try {
            // 检查是否已存在保证金记录
            $deposit = ShopDeposit::where('shop_id', $shop_id)->find();

            $pay_info = PayLogic::wechatPay($deposit['id'], 'bondcharge', 1,$post);


            Db::commit();
            return $pay_info;

        } catch (\Exception $e) {
            Db::rollback();
            self::$error = $e->getMessage();
            return false;
        }
    }

    /*
     * 商家补缴保证金
     */
    public static function replenishDeposit($shop_id, $post)
    {
        Db::startTrans();
        try {
            $deposit = ShopDeposit::where('shop_id', $shop_id)->find();
            if (!$deposit) {
                throw new \Exception('您尚未缴纳保证金，请先缴纳');
            }

            $money = floatval($post['amount']);
            $deposit_id = $deposit->id;

            // 创建保证金明细记录(待支付)
            $detail_sn = createSn('shop_deposit_details', 'sn');
            ShopDepositDetails::create([
                'deposit_id' => $deposit_id,
                'shop_id' => $shop_id,
                'sn' => $detail_sn,
                'change_type' => 2, // 2-增加/补缴
                'amount' => $money,
                'pay_status' => PayEnum::UNPAID, // 待支付
                'pay_way' => $post['pay_way'] ?? null,
                'remark' => '补缴保证金',
                'create_time' => time(),
            ]);

            // 创建支付订单
            $from = $post['from'] ?? ClientEnum::h5;
            $pay_way = $post['pay_way'] ?? PayEnum::WECHAT_PAY;

            switch ($pay_way) {
                case PayEnum::WECHAT_PAY:
                    $pay_info = PayLogic::wechatPay($detail_sn, OrderEnum::SHOP_DEPOSIT, $from);
                    break;
                case PayEnum::ALI_PAY:
                    $pay_info = PayLogic::aliPay($detail_sn, OrderEnum::SHOP_DEPOSIT, $from);
                    break;
                default:
                    throw new \Exception('不支持的支付方式');
            }

            if ($pay_info === false || (isset($pay_info['code']) && $pay_info['code'] == 0)) {
                $error_msg = PayLogic::getError() ?: (isset($pay_info['msg']) ? $pay_info['msg'] : '创建支付订单失败');
                throw new \Exception($error_msg);
            }

            Db::commit();
            return $pay_info;

        } catch (\Exception $e) {
            Db::rollback();
            self::$error = $e->getMessage();
            return false;
        }
    }

    /*
     * 补缴代理费
     */
    public static function agentbucharge($user_id, $post)
    {
        try{
            // 检查用户是否是代理
            $agent = Db::name('agent')->where(['user_id' => $user_id])->find();
            if(!$agent){
                self::$error = '您不是代理，无法补缴代理费';
                return false;
            }

            // 检查代理状态
            if($agent['distribution_end_time'] > time()){
                self::$error = '您的代理资格尚未到期，无需补缴';
                return false;
            }

            // 获取代理费金额
            $money = ConfigServer::get('agent', 'agent_price', 0); // 代理费

            // 查找最近的代理保证金记录
            $deposit = Db::name('agent_merchantfees')
                ->where('user_id', $user_id)
                ->where('status', 'in', [1, 2]) // 已支付的记录
                ->order('id', 'desc')
                ->find();

            if (!$deposit) {
                self::$error = '未找到有效的代理保证金记录';
                return false;
            }

            // 生成订单号
            $order_sn = createSn('agent_deposit_details', 'sn');

            // 创建代理保证金明细记录（待支付状态）
            $detailData = [
                'user_id' => $user_id,
                'deposit_id' => $deposit['id'],
                'sn' => $order_sn,
                'pay_status' => 0, // 0:未支付
                'deposit_change' => $money, // 正数表示增加
                'change_type' => 1, // 1-线上缴纳
                'amount' => $money,
                'reason' => '补缴代理费',
                'remark' => '代理资格续期',
                'change_date' => date('Y-m-d'),
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $detailId = Db::name('agent_deposit_details')->insertGetId($detailData);
            if($detailId){
                return [
                    'id' => $detailId,
                    'order_sn' => $order_sn,
                    'amount' => $money
                ];
            }
            return [];
        }catch(\Exception $e) {
            self::$error = $e->getMessage();
            return false;
        }
    }
}
