{layout name="layout1" /}
<div class="wrapper">
    <div class="layui-card">
        <div class="layui-card-body">
            <div class="layui-collapse like-layui-collapse" lay-accordion="" style="border:1px dashed #c4c4c4">
                <div class="layui-colla-item">
                    <h2 class="layui-colla-title like-layui-colla-title" style="background-color: #fff">操作提示</h2>
                    <div class="layui-colla-content layui-show">
                        <p>*商家荣誉勋章设置</p>
                        <p>*验厂验商验品牌相关。</p>
                        <p>*当前入驻费:<span style="color:red">{$entry_fee}</span></p>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-tab layui-tab-card" lay-filter="tab-all">
            <div class="layui-card">

                <div class="layui-card-body">
                    <div style="padding-bottom: 10px;" class="add">
                        <button class="layui-btn layui-btn-sm layuiadmin-btn-user_level {$view_theme_color}" data-type="add">新增会员等级</button>
                        <button class="layui-btn layui-btn-sm layuiadmin-btn-user_level layui-btn-primary" data-type="set">设置等级说明</button>
                    </div>

                    <table id="user_level-lists" lay-filter="user_level-lists"></table>
                    <script type="text/html" id="image">
                        <img src="{{d.image}}" style="height:auto;width: auto" class="image-show">
                    </script>
                    <script type="text/html" id="background">
                        <img src="{{d.background_image}}" style="height:auto;width: auto" class="image-show">
                    </script>
                    <script type="text/html" id="user_level-operation">
                        <a class="layui-btn layui-btn-normal layui-btn-sm" lay-event="edit">编辑</a>
                        <a class="layui-btn layui-btn-danger layui-btn-sm" lay-event="del">删除</a>
                    </script>

                </div>
            </div>

        </div>

    </div>
</div>
<script type="text/html" id="discount-info">
    {{d.discount}}折
</script>
<style>
    .layui-table-cell {
        height: auto;
    }
</style>
<script>
    layui.config({
        version:"{$front_version}",
        base: '/static/lib/' //静态资源所在路径
    }).use(['table','form'], function(){
        var $ = layui.$
            ,form = layui.form
            ,table = layui.table
            ,element = layui.element;

        //图片放大
        $(document).on('click', '.image-show', function () {
            var src = $(this).attr('src');
            like.showImg(src,600);
        });

        $('.layui-btn.layuiadmin-btn-user_level').on('click', function(){
            var type = $(this).data('type');
            active[type] ? active[type].call(this) : '';
        });
        layui.define(['table', 'form'], function(exports){
            var $ = layui.$
                ,table = layui.table
                ,form = layui.form;

            table.render({
                id:'user_level-lists'
                ,elem: '#user_level-lists'
                ,url: '{:url("shop.apply/levels")}'  //模拟接口
                ,cols: [[
                    {field: 'name', title: '等级名称',width:160}
                    ,{field: 'icon',width:160, title: '等级图标',toolbar:'#image'}
                    // ,{field: 'privilege',width:160, title: '等级权益'}
                    // ,{field: 'background_image',width:160, title: '等级背景图片',toolbar:'#background'}
                    // ,{width:160, title:'等级折扣', templet: '#discount-info'}
                    // ,{field: 'discount',width:160, title:'费用'}
                    // ,{field: 'hdiscount',width:160, title:'优惠组合(与入驻费一同购买)'}
                    ,{field: 'remark',width:160, title:'等级说明'}
                    ,{fixed: 'right', title: '操作', width:160,align: 'center', toolbar: '#user_level-operation'}
                ]]
                ,page:true
                ,text: {none: '暂无数据！'}
                ,parseData: function(res){ //将原始数据解析成 table 组件所规定的数据
                    return {
                        "code":res.code,
                        "msg":res.msg,
                        "count": res.data.count, //解析数据长度
                        "data": res.data.lists, //解析数据列表
                    };
                }
                ,response: {
                    statusCode: 1
                }
                ,done: function(res, curr, count){
                    // 解决操作栏因为内容过多换行问题
                    $(".layui-table-main tr").each(function (index, val) {
                        $($(".layui-table-fixed-l .layui-table-body tbody tr")[index]).height($(val).height());
                        $($(".layui-table-fixed-r .layui-table-body tbody tr")[index]).height($(val).height());
                    });
                }
            });

        });


        //事件
        var active = {
            add: function(){
                var index = layer.open({
                    type: 2
                    ,title: '新增等级'
                    ,content: '{:url("shop.apply/add")}'
                    ,area: ['90%', '90%']
                    ,btn: ['保存', '取消']
                    ,maxmin: true
                    ,yes: function(index, layero){
                        var iframeWindow = window['layui-layer-iframe'+ index]
                            ,submitID = 'add-user_level-submit'
                            ,submit = layero.find('iframe').contents().find('#'+ submitID);
                        //监听提交
                        iframeWindow.layui.form.on('submit('+ submitID +')', function(data){
                            var field = data.field;
                            like.ajax({
                                url:'{:url("shop.apply/add")}',
                                data:field,
                                type:"post",
                                success:function(res)
                                {
                                    if(res.code == 1)
                                    {
                                        layui.layer.msg(res.msg, {
                                            offset: '15px'
                                            , icon: 1
                                            , time: 1000
                                        });
                                        layer.close(index); //关闭弹层
                                        table.reload('user_level-lists'); //数据刷新
                                    }
                                }
                            });
                        });

                        submit.trigger('click');
                    }
                });
            },
            set: function() {
                var index = layer.open({
                    type: 2
                    ,title: '设置等级说明'
                    ,content: '{:url("user.level/set")}'
                    ,area: ['60%', '60%']
                    ,btn: ['保存', '取消']
                    ,maxmin: true
                    ,yes: function(index, layero){
                        var iframeWindow = window['layui-layer-iframe'+ index]
                            ,submitID = 'add-user_intro-submit'
                            ,submit = layero.find('iframe').contents().find('#'+ submitID);
                        //监听提交
                        iframeWindow.layui.form.on('submit('+ submitID +')', function(data){
                            var field = data.field;
                            like.ajax({
                                url:'{:url("user.level/set")}',
                                data:field,
                                type:"post",
                                success:function(res)
                                {
                                    if(res.code == 1)
                                    {
                                        layui.layer.msg(res.msg, {
                                            offset: '15px'
                                            , icon: 1
                                            , time: 1000
                                        });
                                        layer.close(index); //关闭弹层
                                        table.reload('user_level-lists'); //数据刷新
                                    }
                                }
                            });
                        });

                        submit.trigger('click');
                    }
                });
            }
        }

        table.on('tool(user_level-lists)', function(obj) {
            var id = obj.data.id;
            if (obj.event === 'edit') {
                var index = layer.open({
                    type: 2
                    , title: '编辑等级'
                    , content: '{:url("shop.apply/edit")}?id=' + id
                    , area: ['90%', '90%']
                    , btn: ['保存', '取消']
                    , maxmin: true
                    , yes: function (index, layero) {
                        var iframeWindow = window['layui-layer-iframe' + index]
                            , submitID = 'edit-user_level-submit'
                            , submit = layero.find('iframe').contents().find('#' + submitID);
                        //监听提交
                        iframeWindow.layui.form.on('submit(' + submitID + ')', function (data) {
                            var field = data.field;
                            like.ajax({
                                url: '{:url("shop.apply/edit")}',
                                data: field,
                                type: "post",
                                success: function (res) {
                                    if (res.code == 1) {
                                        layui.layer.msg(res.msg, {
                                            offset: '15px'
                                            , icon: 1
                                            , time: 1000
                                        });
                                        layer.close(index); //关闭弹层
                                        table.reload('user_level-lists'); //数据刷新
                                    }
                                }
                            });
                        });

                        submit.trigger('click');
                    }
                });

            }
            if (obj.event === 'del') {
                var name = obj.data.name;
                layer.confirm('确定删除会员等级：<span style="color: red">' + name + '</span>', function (index) {
                    like.ajax({
                        url: '{:url("shop.apply/del")}',
                        data: {id: id},
                        type: "post",
                        success: function (res) {
                            if (res.code == 1) {
                                layui.layer.msg(res.msg, {
                                    offset: '15px'
                                    , icon: 1
                                    , time: 1000
                                });
                                layer.close(index); //关闭弹层
                                table.reload('user_level-lists'); //数据刷新
                            }
                        }
                    });
                    layer.close(index);
                })

            }
        })

    });
</script>