<?php
namespace app\common\listener;

use app\common\service\PurchaserAllocationService;
use think\facade\Log;

/**
 * 商家等级升级监听器
 * Class ShopTierUpgradeListener
 * @package app\common\listener
 */
class ShopTierUpgradeListener
{
    /**
     * 处理商家等级升级事件
     * @param array $data 事件数据
     */
    public function handle($data)
    {
        try {
            $shopId = $data['shop_id'] ?? null;
            $newTierLevel = $data['new_tier_level'] ?? null;
            $oldTierLevel = $data['old_tier_level'] ?? null;
            
            if (!$shopId || $newTierLevel === null) {
                Log::warning("商家升级事件参数不完整", $data);
                return;
            }
            
            Log::info("处理商家升级事件", [
                'shop_id' => $shopId,
                'old_tier_level' => $oldTierLevel,
                'new_tier_level' => $newTierLevel
            ]);
            
            // 执行自动分配
            PurchaserAllocationService::onShopTierUpgrade($shopId, $newTierLevel, $oldTierLevel);
            
        } catch (\Exception $e) {
            Log::error("处理商家升级事件异常", [
                'data' => $data,
                'error' => $e->getMessage()
            ]);
        }
    }
}
