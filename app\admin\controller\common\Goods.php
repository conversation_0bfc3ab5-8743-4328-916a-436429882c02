<?php

namespace app\admin\controller\common;
use app\admin\logic\common\GoodsLogic;
use app\common\basics\AdminBase;
use app\common\server\JsonServer;

class goods extends AdminBase{

    public function selectGoods(){
        if($this->request->isAjax()){
            $get = $this->request->get();
            $list = GoodsLogic::selectGoods($get);
            return JsonServer::success('',$list);
        }
        return view();
    }
}