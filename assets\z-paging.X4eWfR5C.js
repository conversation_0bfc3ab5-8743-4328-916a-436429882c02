import{o as e,c as t,w as i,a as o,n as s,d as a,b as r,t as l,A as h,h as n,q as d,N as c,i as u,a1 as g,a2 as p,a3 as f,a4 as m,a5 as T,j as y,F as S,z as R,a6 as w,a7 as M,a8 as C,Q as x,a9 as I,aa as A,ab as b,ac as L,V as v,U as D,T as V,f as z,B as N,k as P,S as k}from"./index-B6kWyIrN.js";import{_ as B,r as E}from"./uv-icon.D6fiO-QB.js";const F="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABkBAMAAACCzIhnAAAAD1BMVEVHcExRUVFMTExRUVFRUVE9CdWsAAAABHRSTlMAjjrY9ZnUjwAAAQFJREFUWMPt2MsNgzAMgGEEE1B1gKJmAIRYoCH7z9RCXrabh33iYktcIv35EEg5ZBh07pvxJU6MFSPOSRnjnBUjUsaciRUjMsb4xIoRCWNiYsUInzE5sWKEyxiYWDbyefqHx1zIeiYTk7mQYziTYecxHvEJjwmIT3hMQELCYSISEg4TkZj0mYTEpM8kJCU9JiMp6TEZyUmbAUhO2gxAQNJiIAKSFgMRmNQZhMCkziAEJTUGIyipMRjBSZkhCE7KDEFIUmTeGCHJxWz0zXaE0GTCG8ZFtEaS347r/1fe11YyHYVfubxayfjoHmc0YYwmmmiiiSaaaKLJ7ckyz5ve+dw3Xw2emdwm9xSbAAAAAElFTkSuQmCC",H="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABkBAMAAACCzIhnAAAAElBMVEVHcEz///////////////////+IGTx/AAAABnRSTlMA/dAkXZOhASU/AAABYElEQVRYw+2YwXLCIBCGsdAHWGbyAKZ4zxi9O017rxLf/1UaWFAgA1m8dcpedNSPf/l/Vh0Ya/Wn6hN0JcGvoCqRM4C8VBFiDwBqqNuJKV0rAnCgy3AUqZE57x0iqTL8Br4U3WBf/YWaIlTKfAcELU/h9w72CSVPa3C3OCDvhpHbRp/s2vq4fHhCeiCl2A3m4Qd71DQR257mFBlMcTlbFnFWzNtHxewYEfSiaLS4el8d8nyhmKJd1CF4eOS0keLMAuSxubLBIeIGQW8YHCFFo7EH9+YDcQt9FMZEswTheaNxTHwHT8SZorJjMrEVwo4Zo0U8HSEyZvJMOg4RjnmmRr8nDYeIz3OMkbfE/QhBo+U9RnZJxjGCRh/WKmHEMWLNkfPKsGh/CWJk1JjG0kcuJggTt34VDP8aWAFhp4nybVb5+9qQhjSkIQ1pSEMa8k+Q5U9rV3dF8MpFBK+/7miVq1/HZ2qmo9D+pAAAAABJRU5ErkJggg==",W="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABkBAMAAACCzIhnAAAAKlBMVEVHcEzDw8Ovr6+pqamUlJTCwsKenp61tbWxsbGysrLNzc2bm5u5ubmjo6MpovhuAAAACnRSTlMA/P79/sHDhiZS0DxZowAABBBJREFUWMPtl89rE0EUx7ctTXatB3MI1SWnDbUKPUgXqh4ED8Uf7KUVSm3ooVSpSii0Fn/gD4j4o+APiEoVmos9FO2celiqZVgwgaKHPQiCCkv+F99kM7Ozm5kxq1dfD91k9pPve9/3ZjbRNHHok/mKli4eIPNgSuRObuN9SqSEzM20iGnm0yIbqCuV7NSSSIV7uyPM6JMBYdeTOanh/QihJYZsUCSby+VkMj2AvOt0rAeQAwqE3lfKMZVlQCZk1QOCKkkVPadITCfIRNKxfoJI5+0OIFtJx14CMSg1mRSDko7VAfksRQzEbGYqxOJcVTWMCH2I1/IACNW0PWU2M8cmAVHtnH5mM1VRWtwKZjOd5JbF6s1IbaYqaotjNlPHgDAnlAizubTR6ovMYn052g/U5qcmOpi0WL8xTS/3IfSet5m8MEr5ajjF5le6dq/OJpobrdY0t3i9QgefWrxW9/1BLhk0E9m8FeUMhhXal499iD0eQRfDF+ts/tttORRerfp+oV7f4xJj82iUYm1Yzod+ZQEAlS/8mMBwKebVmCVp1f0JLS6zKd17+iwRKTARVg2SHtz3iEbBH+Q+U28zW2Jiza8Tjb1YFoYZMsJyjDqp3M9XBQdSdPLFdxEpvOB37JrHcmR/y9+LgoTlCFGZEa2sc6d4PGlweEa2JSVPoVm+IfGG3ZL037iV9oH+P+Jxc4HGVflNq1M0pivao/EopO4b/ojVCP9GjmiXOeS0DOn1o/iiccT4ORnyvBGF3yUywkQajW4Ti0SGuiy/wVSg/L8w+X/8Q+hvUx8Xd90z4oV5a1i88MbFWHz0WZZ1UrTwBGPX3Rat9AFiXRMRjoMdIdJLEOt2h7jrYOzgOamKZSWSNspOS0X8SAqRYmxRL7sg4eLzYmNehcxh3uoyud/BH2Udux4ywxFTc1xC7Mgf4vMhc5S+kSH3Y7yj+qpwIWSoPTVCOOPVthGx9FbGqrwFw6wSFxJr+17zeKcztt3u+2roAEVgUjDd+AHGuxHy2rZHaa8JMkTHEeyi85ANPO9j9BVuBRD2FY5LDMo/Sz/2hReqGIs/KiFin+CsPsYO/yvM3jL2vE8EbX7/Bf8ejtr2GLN65bioAdgLd8Bis/mD5GmP2qeqyo2ZwQEOtAjRIDH7mBKpUcMoApbZJ5UIxkEwxyMZyMxW/uKFvHCFR3SSmerHyDNQ2dF4JG6zIMpBgLfjSF9x1D6smFcYnGApjmSLICO3ecCDWrQ48geba9DI3STy2i7ax6WIB62fSyIZIiO3GFQqSURp8wCo7GhJBGwuSovJBNjb7kT6FPVnIa9qJ2Ko+l9mefGIdinaMp0yC1URYiwsdfNE45EuA5Cx9EhalfvN5s+UyItm81vaB3p4joniN+SCP7Qc1hblAAAAAElFTkSuQmCC",Z="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABkBAMAAACCzIhnAAAAElBMVEX///9HcEz///////////////84chYNAAAABnRSTlP/AGzCOYZj5g1nAAACfklEQVRYw+2YTVPDIBCGtza9Jw25a0bvcax30o73OOr//yvma2F3YWlpPTijXNpAHrK8LLALVPFium2vNIFSbwGKTGQA2GUiHcD29yDNy3sMIdUBQl7r2H8mOEVqAHgPkYZUS6Qc2zYhQqtjyDZEximCZwWZLIBeIgYShs2NzxKpSUehYpMJhURGb+O+w5BpMCAREKPnCDHbIY20SzhM5yxziAXpOiBXydrekT9i5XDEq4NIIHHgyU5mRGqviII4mREJJA4QJzMiILwlRJzpKxJKvCBm8OsBBbLux0tsPl4RKYm5aPu6jw1U4mGxEUR9g8M1PcqBEp/WJliNgYOXueBzS4jZSIcgY5lCtevgDSgyzE+rAfuOTQMq0yzvoGH18qju27Mayzs4fPyMziCx81NJa5RNfW7vPYK9KOfDiVkBxFHG8hAj9txuoBuSWORsFfkpBf7xKFLSeaOefEojh5jz22DJEqMP8fUyaKdQx+RnG+yXMpe8Aars8ueR1pVH/bW3FyyvPRw90upLDHwpgBDtg4aUBNkxRLXMAi03IhcZtr1m+FeI/O/JNyDmmL1djLOauSlNflBpW18RQ2bPqXI22MXXEk75KRHTnkPkYbESbdKP2ZFk0r5sIwffAjy1lx+vx7NLjB6/E7Jfv5ERKhzpN0w8IDE8IGFDv5dhz10s7GFiXRZcUeLCEG5P5nDq9k4PFDcoMpE3GY4OuxuCXhmuyNB6k0RsLIAvqp9NE5r8ZCSS8gxnUp7ODdYhZTqxuiJ9uyJJtPmpqJ7wVj+XVieS903iViHziqAhchLEJAyb7jWU647EpUofQ0ziUuXXXhDddtlllSwjgSQu7r4BRWhQqfDPMVwAAAAASUVORK5CYII=",j="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABkBAMAAACCzIhnAAAAElBMVEVRUVFHcExTU1NRUVFRUVFRUVFOSlSUAAAABnRSTlP/AI6+VySB3ZENAAACcElEQVRYw+2YyYKCMAyGI8hdpdxdZu7gcpdZ7jL6/s8yYheSNi0aPdqbwOffpGmaFOYPD3gj4bisN7vddv17N/JVgxn5x12IWgIaWTuO/IE3PseQbwjGPo2cgRmHFLJwdm/X643zwiqOKPPJ1nj3sjEP2iiifZWj5bhopSyGaEO2HX5fbQJzwJ+W7x/jw5ZFjsEU0PMph9xE8i5EqprKALW95eJQURkgzw98uJ/JvwGecR7bIjWWsUgVrrIfFZ2HlLy3sKETD1mmRLRMRhGVssRa0xJkdn3SpJBymBkM8+pSSDXMDNyDaToVHd2fgpNt0sjwiUZO19+jGQ+gQEg9Oq+bufmAVGihomNmjQG7UG3020vrlm7lkFnKFGU3kZ0KGAdmKe821pipQ+qEKcrZeTL2g5FsUks4cStjEZWwXg0b0n4GxmEpkWwIs5VBynjgK7xZaz1/0D7OxkVuLpsY5BQNFyLS84VBjjbg0iL2r2EQHBOxBhikuUOkdxODVF1cxHoWtPPsiyXO455Iv34hssCO8EV4ZIYTjS8SR4qYSHRiTiYQ4ZFbHi0iIhhBTi6dTCgSWRcnw4h4yGTuyTAiOGBIWGoZTgSHJQl+LcOJ4OCnW6yX2bMnJ9pidCOXtkTkTrIGpYuOynAiOF14SamMiOCk5Ke+mq8BcOrrvym8d0zKIQnWT+M1WwOQNO4fFiWb18hhERxJPx2fblbPHHyC41VyiAtKBUFBIih7JMWVoIQTFIr3lKPN80WvoLSWFPC653ioTZA0I0FrQ7qU6asaK0H7JmkSJa2ooOGVtNUsc3j9FYHkIkJy3SG6VHnfXKXGP9t4N9Q4Ye98AAAAAElFTkSuQmCC",O="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABkBAMAAACCzIhnAAAAGFBMVEVHcEz///////////////////////////8dS1W+AAAAB3RSTlMAiVYk6KvDHLfaegAAAo1JREFUWMPtWEtzmzAQNhCTq910ytXpiyvxTNOr60zrayepx9d02gnX4sTm7xcEiJX2gdnkGJ1A4tOnfWqXyeR1vMRYzrcPD9v5h5MBl3/Ldvx4cxIg/FWC8X0xjLjalM54uhhCfCrRuJURX0pi3EmIqZV7O59vrRZmguStHL9b7S7ftfLwOtiZDw7AHMtmquAQ12b5Wwbnordm8g9zLLO49qc/m2n6aKnhwPOGZ08hAiNHhheiHae1lOUPGZpQkPKa3q0mOUjaRzSRaGUjpy/mmWSwySSpllcEteBKAT52KEnSbblA51pJEPxBQoiH1FP4E3s5+FJv07h6/ylD6ui7B+9fq/ehrFB98ghec9EoVtyjK8pqCHLmCBOwMWSCeWFNN4MbPAk55NhsvoFHSSVR0k5TCTTEzlUGcqV/nVp7n9oIVkmtaqbAEqEgfdgHJPwsEAyZ9r4VAZXFjpEwyaw3+H2v42KYxKhs1XvY/gSSGv+IHyUSuHXCeZhLAgVI3EjgSGo1Fb3xO0tGGU9S2/KAIbtjxpJASG73qox6w5LUq0cEOa+iIONIWIilQSQ0pPa2jgaRQAgQP7c0mITRWGxpMAmEQFN2NAQJNCV0mI6GIIEO47hlQ0ORQLd0nL+hoUjg1m6I1TRr8uYEAriBHLcVFQ5UEMiBe3XkTBEG04WXlGKGxPnMS305XQPA1Ocn2JiuAZwE66fxnKwBnDTuXxZTMq85lwW6kt5ndLqZPefiU1yvmktcUSooChJF2aMprhQlnKJQ5FxRKkcVRa+itNYU8Io2oVkY14w0NMWYlqft91Bj9VHq+ca3b43BxjWJmla0sfKohlfTVpPN+93L/yLQ/IjQ/O5Q/VR5HdL4D7mlxmjwVdELAAAAAElFTkSuQmCC",U="data:image/png;base64,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",G="data:image/png;base64,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",Y="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMgAAADIBAMAAABfdrOtAAAAElBMVEVRUVH+/v5HcEyZmZlRUVFRUVGm1ByOAAAABnRSTlPMzADMTZAJBBGsAAAEnElEQVR42t2cS27jMAyGf7/2U+QCQeDsbeQCgZDujaC5/1UmkzaJn+JDFGcw3LdfflKibJkkDnxrL7dbg7sNt6+L4O8OYBM+B0ys+QrGkHZG+OEEQ8g6go8Bx1GIGMdpNOQyIG6XdMgnSPtKhLQDGEZFBgYMkhKFtGBb0EIEjDgFRowoBVaMGAWpMedEfxMiZtwpUsgZCqtlkCNUdpVAWigtCCCDFtLwIWeoreZCWiRYYEKGFEjDg+yRZCUH0iLRAgNyToXUNCRZyMqWhGnUN2IPm3wSlwJ7IUspyCBkIQUZhCykIIeQuRTkEDKXAuM9srrtYbrZN7Y98giZSoFd+t1OxmMITG0dcrSFXFchZ1tIvQZpYWxhBbK3hpQrkMEa0iwh5t4a+QvZvDXyF7J5a+Qv5PPW21/I5623v5DPW29/IaO3Xv5Clrw1y1/Ikrdm+Qs5svw83yNnSJ5BQb4F/F7EIEJSnThGBAXxkFQfLOviQUE8JAUPsosHBfGQfDAtHhREQ1JxIV00KIgmrnRI84S0yAd5BAXxxJUck0f6Qnwr9qmr6xF5xLMjcwn/iudIEAdWnyjkEXlQKZiRVzoqRyLbgeUKKR8Q4alY7cSnoxzSf2ggsqehKr6YVpcXpOd7H93f60cKhOd7Re2LteUF4eLqiVS1mr0ge4io6C2+soaFkJ7MuuuQs1yITEp9hwwKISIpzR2iESKSIoT0rLNwuVHQqoSIpAQJpGce60vIUSdEIuUqgPTsJ5QFZK8UIpBS8iG94GFrDjlrhfCl8CG96Llxmle4kEr6vKWBPIVo9kqDQSRk9/3cWoikcCFPAd33v4dIChPyEvLzBA6RlEYWke4JEUnhKXkLeUEKxRHJFfKCQHGucIW8IdZSRkLeEGMpYyEjiK2UsZARxFTKRMgYYillImQMMZQyFTKB2EmZCplAuFLIHT8TMoWwpQwiIVMIUwqpZP5bp5CCvCTiQKr5f5lCQN+tPCBn2ZvVDFJwIDUP0m1BYAfZYRNSsCB7BqTbhoARePIxtZ9tgwWkoJcwCalmv3MBAemtO4R6dah2HaKQqj8Zvp9sQDjvJ21+SPCBHPJDDk6QITekEV7gqCC19CpKAym9IMfckKv4olMBCeIrWwVEfvkshzQekO9r9P1/ALk+IG1eSPCDiCJfyG+FyU+A6ZCa/piZDinpz7LpkCv5gdkAEshP5emQhv7onw6pGeULyZCSUYiRDAmMkpJkCKs4JhFSq8p8hJBSVbAkhARV6ZUQoisik0FqXTmcDHLVFfbJIEFXoiiCNMpiSxGkVJaNiiBBWQArgTTaUl4JpNQWJUsgQVteXQg+AKkLxQWFGKW+5J2+eVp4S168X3CF1CltCKdTJ8lb84YK2bUBO+wZW0Pqv9nk4tKu49N45NJC5dMM5tLW5tOg59Jq6NM06dL+abFXwr/RkuvTXJwae1abtE/Dt0/ruksTvs84AZ/BCC4jHnyGVfiM3VBQFANEXEah+Ax18RlP4zNox2dkkM/wI58xTn8yDCXGYCDV3W5RGSajtXyGhG1jbpbjzpwGt/0MJft8jqC7iUbQ/QZaxdnKqcIftwAAAABJRU5ErkJggg==";const _=B({name:"z-paging-empty-view",data:()=>({}),props:{emptyViewText:{type:String,default:"没有数据哦~"},emptyViewImg:{type:String,default:""},showEmptyViewReload:{type:Boolean,default:!1},emptyViewReloadText:{type:String,default:"重新加载"},isLoadFailed:{type:Boolean,default:!1},emptyViewStyle:{type:Object,default:function(){return{}}},emptyViewImgStyle:{type:Object,default:function(){return{}}},emptyViewTitleStyle:{type:Object,default:function(){return{}}},emptyViewReloadStyle:{type:Object,default:function(){return{}}},emptyViewZIndex:{type:Number,default:9},emptyViewFixed:{type:Boolean,default:!0},unit:{type:String,default:"rpx"}},computed:{emptyImg(){return this.isLoadFailed?G:U},finalEmptyViewStyle(){return this.emptyViewStyle["z-index"]=this.emptyViewZIndex,this.emptyViewStyle}},methods:{reloadClick(){this.$emit("reload")},emptyViewClick(){this.$emit("viewClick")}}},[["render",function(g,p,f,m,T,y){const S=d,R=c,w=u;return e(),t(w,{class:s({"zp-container":!0,"zp-container-fixed":f.emptyViewFixed}),style:a([y.finalEmptyViewStyle]),onClick:y.emptyViewClick},{default:i((()=>[o(w,{class:"zp-main"},{default:i((()=>[f.emptyViewImg.length?(e(),t(S,{key:1,class:s({"zp-main-image-rpx":"rpx"===f.unit,"zp-main-image-px":"px"===f.unit}),mode:"aspectFit",style:a([f.emptyViewImgStyle]),src:f.emptyViewImg},null,8,["class","style","src"])):(e(),t(S,{key:0,class:s({"zp-main-image-rpx":"rpx"===f.unit,"zp-main-image-px":"px"===f.unit}),style:a([f.emptyViewImgStyle]),src:y.emptyImg},null,8,["class","style","src"])),o(R,{class:s(["zp-main-title",{"zp-main-title-rpx":"rpx"===f.unit,"zp-main-title-px":"px"===f.unit}]),style:a([f.emptyViewTitleStyle])},{default:i((()=>[r(l(f.emptyViewText),1)])),_:1},8,["class","style"]),f.showEmptyViewReload?(e(),t(R,{key:2,class:s({"zp-main-error-btn":!0,"zp-main-error-btn-rpx":"rpx"===f.unit,"zp-main-error-btn-px":"px"===f.unit}),style:a([f.emptyViewReloadStyle]),onClick:h(y.reloadClick,["stop"])},{default:i((()=>[r(l(f.emptyViewReloadText),1)])),_:1},8,["class","style","onClick"])):n("",!0)])),_:1})])),_:1},8,["class","style","onClick"])}],["__scopeId","data-v-15f3d1f0"]]),Q="2.8.6",J=100,K="z-paging-error-emit",X="z-paging-complete-emit",q="z-paging-cache",$="zp_index",ee="zp_unique_index",te={},ie="Z-PAGING-REFRESHER-TIME-STORAGE-KEY";let oe=null,se=!1,ae=null;const re={};function le(){return p(ie)}function he(e){return f(e)}function ne(){return(new Date).getTime()}function de(e){return 1===(e=e.toString()).length?"0"+e:e}const ce={gc:function(e,t){return()=>{if(function(){if(se)return;te&&Object.keys(te).length&&(oe=te);!oe&&uni.$zp&&(oe=uni.$zp.config);oe=oe?Object.keys(oe).reduce(((e,t)=>(e[function(e){return e.replace(/-([a-z])/g,((e,t)=>t.toUpperCase()))}(t)]=oe[t],e)),{}):null,se=!0}(),!oe)return t;const i=oe[e];return void 0===i?t:i}},setRefesrherTime:function(e,t){const i=le()||{};i[t]=e,g(ie,i)},getRefesrherFormatTimeByKey:function(e,t){const i=function(e){const t=le();return t&&t[e]?t[e]:null}(e),o=i?function(e,t){const i=new Date(e),o=new Date,s=new Date(e).setHours(0,0,0,0),a=(new Date).setHours(0,0,0,0),r=s-a;let l="";const h=function(e){const t=e.getHours(),i=e.getMinutes();return`${de(t)}:${de(i)}`}(i);l=0===r?t.today:-864e5===r?t.yesterday:function(e,t=!0){const i=e.getFullYear(),o=e.getMonth()+1,s=e.getDate();return t?`${i}-${de(o)}-${de(s)}`:`${de(o)}-${de(s)}`}(i,i.getFullYear()!==o.getFullYear());return`${l} ${h}`}(i,t):t.none;return`${t.title}${o}`},getTouch:function(e){let t=null;if(e.touches&&e.touches.length)t=e.touches[0];else if(e.changedTouches&&e.changedTouches.length)t=e.changedTouches[0];else{if(!e.datail||e.datail=={})return{touchX:0,touchY:0};t=e.datail}return{touchX:t.clientX,touchY:t.clientY}},getTouchFromZPaging:function e(t){if(t&&t.tagName&&"BODY"!==t.tagName&&"UNI-PAGE-BODY"!==t.tagName){const i=t.classList;return i&&i.contains("z-paging-content")?{isFromZp:!0,isPageScroll:i.contains("z-paging-content-page"),isReachedTop:i.contains("z-paging-reached-top"),isUseChatRecordMode:i.contains("z-paging-use-chat-record-mode")}:e(t.parentNode)}return{isFromZp:!1}},getParent:function e(t){return t?t.$refs.paging?t:e(t.$parent):null},convertToPx:function(e){if("[object Number]"===Object.prototype.toString.call(e))return e;let t=!1;return-1!==e.indexOf("rpx")||-1!==e.indexOf("upx")?(e=e.replace("rpx","").replace("upx",""),t=!0):-1!==e.indexOf("px")&&(e=e.replace("px","")),isNaN(e)?0:Number(t?he(e):e)},getTime:ne,getInstanceId:function(){const e=[];for(let t=0;t<10;t++)e[t]="0123456789abcdef".substr(Math.floor(16*Math.random()),1);return e.join("")+ne()},consoleErr:function(e){console.error(`[z-paging]${e}`)},delay:function(e,t=J,i){const o=setTimeout(e,t);return i&&(re[i]&&clearTimeout(re[i]),re[i]=o),o},wait:function(e){return new Promise((t=>{setTimeout(t,e)}))},isPromise:function(e){return"[object Promise]"===Object.prototype.toString.call(e)},addUnit:function(e,t){if("[object String]"===Object.prototype.toString.call(e)){let t=e;t=t.replace("rpx","").replace("upx","").replace("px",""),-1===e.indexOf("rpx")&&-1===e.indexOf("upx")&&-1!==e.indexOf("px")&&(t=2*parseFloat(t)),e=t}return"rpx"===t?e+"rpx":e/2+"px"},deepCopy:function e(t){if("object"!=typeof t||null===t)return t;let i=Array.isArray(t)?[]:{};for(let o in t)t.hasOwnProperty(o)&&(i[o]=e(t[o]));return i},rpx2px:he,getSystemInfoSync:function(e=!1){if(e&&ae)return ae;const{deviceInfo:t,appBaseInfo:i,windowInfo:o}=["DeviceInfo","AppBaseInfo","WindowInfo"].reduce(((e,t)=>{const i=`get${t}`;return uni[i]&&m(i)&&(e[t.charAt(0).toLowerCase()+t.slice(1)]=uni[i]()),e}),{});return ae=t&&i&&o?{...t,...i,...o}:T(),ae}},ue={LoadingType:{Refresher:"refresher",LoadMore:"load-more"},Refresher:{Default:"default",ReleaseToRefresh:"release-to-refresh",Loading:"loading",Complete:"complete",GoF2:"go-f2"},More:{Default:"default",Loading:"loading",NoMore:"no-more",Fail:"fail"},QueryFrom:{UserPullDown:"user-pull-down",Reload:"reload",Refresh:"refresh",LoadMore:"load-more"},CellHeightMode:{Fixed:"fixed",Dynamic:"dynamic"},CacheMode:{Default:"default",Always:"always"}};const ge=B({name:"z-paging-refresh",data:()=>({R:ue.Refresher,refresherTimeText:"",zTheme:{title:{white:"#efefef",black:"#555555"},arrow:{white:H,black:F},flower:{white:Z,black:W},success:{white:O,black:j},indicator:{white:"#eeeeee",black:"#777777"}}}),props:["status","defaultThemeStyle","defaultText","pullingText","refreshingText","completeText","goF2Text","defaultImg","pullingImg","refreshingImg","completeImg","refreshingAnimated","showUpdateTime","updateTimeKey","imgStyle","titleStyle","updateTimeStyle","updateTimeTextMap","unit","isIos"],computed:{ts(){return this.defaultThemeStyle},statusTextMap(){this.updateTime();const{R:e,defaultText:t,pullingText:i,refreshingText:o,completeText:s,goF2Text:a}=this;return{[e.Default]:t,[e.ReleaseToRefresh]:i,[e.Loading]:o,[e.Complete]:s,[e.GoF2]:a}},currentTitle(){return this.statusTextMap[this.status]||this.defaultText},leftImageClass(){const e=`zp-r-left-image-pre-size-${this.unit}`;return this.status===this.R.Complete?e:`zp-r-left-image ${e} ${this.status===this.R.Default?"zp-r-arrow-down":"zp-r-arrow-top"}`},leftImageStyle(){const e=this.showUpdateTime,t=e?ce.addUnit(36,this.unit):ce.addUnit(34,this.unit);return{width:t,height:t,"margin-right":e?ce.addUnit(20,this.unit):ce.addUnit(9,this.unit)}},leftImageSrc(){const e=this.R,t=this.status;return t===e.Default?this.defaultImg?this.defaultImg:this.zTheme.arrow[this.ts]:t===e.ReleaseToRefresh?this.pullingImg?this.pullingImg:this.defaultImg?this.defaultImg:this.zTheme.arrow[this.ts]:t===e.Loading?this.refreshingImg?this.refreshingImg:this.zTheme.flower[this.ts]:t===e.Complete?this.completeImg?this.completeImg:this.zTheme.success[this.ts]:t===e.GoF2?this.zTheme.arrow[this.ts]:""},rightTextStyle(){let e={};return e.color=this.zTheme.title[this.ts],e["font-size"]=ce.addUnit(30,this.unit),e}},methods:{addUnit:(e,t)=>ce.addUnit(e,t),updateTime(){this.showUpdateTime&&(this.refresherTimeText=ce.getRefesrherFormatTimeByKey(this.updateTimeKey,this.updateTimeTextMap))}}},[["render",function(h,g,p,f,m,T){const y=d,S=u,R=c;return e(),t(S,{style:{height:"100%"}},{default:i((()=>[o(S,{class:s(p.showUpdateTime?"zp-r-container zp-r-container-padding":"zp-r-container")},{default:i((()=>[o(S,{class:"zp-r-left"},{default:i((()=>[p.status!==m.R.Loading?(e(),t(y,{key:0,class:s(T.leftImageClass),style:a([T.leftImageStyle,p.imgStyle]),src:T.leftImageSrc},null,8,["class","style","src"])):(e(),t(y,{key:1,class:s({"zp-line-loading-image":p.refreshingAnimated,"zp-r-left-image":!0,"zp-r-left-image-pre-size-rpx":"rpx"===p.unit,"zp-r-left-image-pre-size-px":"px"===p.unit}),style:a([T.leftImageStyle,p.imgStyle]),src:T.leftImageSrc},null,8,["class","style","src"]))])),_:1}),o(S,{class:"zp-r-right"},{default:i((()=>[o(R,{class:"zp-r-right-text",style:a([T.rightTextStyle,p.titleStyle])},{default:i((()=>[r(l(T.currentTitle),1)])),_:1},8,["style"]),p.showUpdateTime&&m.refresherTimeText.length?(e(),t(R,{key:0,class:s(["zp-r-right-text",{"zp-r-right-time-text-rpx":"rpx"===p.unit,"zp-r-right-time-text-px":"px"===p.unit}]),style:a([{color:m.zTheme.title[T.ts]},p.updateTimeStyle])},{default:i((()=>[r(l(m.refresherTimeText),1)])),_:1},8,["class","style"])):n("",!0)])),_:1})])),_:1},8,["class"])])),_:1})}],["__scopeId","data-v-2ee9a40c"]]);const pe=B({name:"z-paging-load-more",data:()=>({M:ue.More,zTheme:{title:{white:"#efefef",black:"#a4a4a4"},line:{white:"#efefef",black:"#eeeeee"},circleBorder:{white:"#aaaaaa",black:"#c8c8c8"},circleBorderTop:{white:"#ffffff",black:"#444444"},flower:{white:Z,black:W},indicator:{white:"#eeeeee",black:"#777777"}}}),props:["zConfig"],computed:{ts(){return this.c.defaultThemeStyle},c(){return this.zConfig||{}},ownLoadingMoreText(){return{[this.M.Default]:this.c.defaultText,[this.M.Loading]:this.c.loadingText,[this.M.NoMore]:this.c.noMoreText,[this.M.Fail]:this.c.failText}[this.finalStatus]},finalStatus(){return this.c.defaultAsLoading&&this.c.status===this.M.Default?this.M.Loading:this.c.status},finalLoadingIconType(){return this.c.loadingIconType}},methods:{doClick(){this.$emit("doClick")}}},[["render",function(o,h,g,p,f,m){const T=c,R=d,w=u;return e(),t(w,{class:s(["zp-l-container",{"zp-l-container-rpx":"rpx"===m.c.unit,"zp-l-container-px":"px"===m.c.unit}]),style:a([m.c.customStyle]),onClick:m.doClick},{default:i((()=>[m.c.hideContent?n("",!0):(e(),y(S,{key:0},[m.c.showNoMoreLine&&m.finalStatus===f.M.NoMore?(e(),t(T,{key:0,class:s({"zp-l-line-rpx":"rpx"===m.c.unit,"zp-l-line-px":"px"===m.c.unit}),style:a([{backgroundColor:f.zTheme.line[m.ts]},m.c.noMoreLineCustomStyle])},null,8,["class","style"])):n("",!0),m.finalStatus===f.M.Loading&&m.c.loadingIconCustomImage?(e(),t(R,{key:1,src:m.c.loadingIconCustomImage,style:a([m.c.iconCustomStyle]),class:s({"zp-l-line-loading-custom-image":!0,"zp-l-line-loading-custom-image-animated":m.c.loadingAnimated,"zp-l-line-loading-custom-image-rpx":"rpx"===m.c.unit,"zp-l-line-loading-custom-image-px":"px"===m.c.unit})},null,8,["src","style","class"])):n("",!0),m.finalStatus!==f.M.Loading||"flower"!==m.finalLoadingIconType||m.c.loadingIconCustomImage.length?n("",!0):(e(),t(R,{key:2,class:s({"zp-line-loading-image":!0,"zp-line-loading-image-rpx":"rpx"===m.c.unit,"zp-line-loading-image-px":"px"===m.c.unit}),style:a([m.c.iconCustomStyle]),src:f.zTheme.flower[m.ts]},null,8,["class","style","src"])),m.finalStatus!==f.M.Loading||"circle"!==m.finalLoadingIconType||m.c.loadingIconCustomImage.length?n("",!0):(e(),t(T,{key:3,class:s(["zp-l-circle-loading-view",{"zp-l-circle-loading-view-rpx":"rpx"===m.c.unit,"zp-l-circle-loading-view-px":"px"===m.c.unit}]),style:a([{borderColor:f.zTheme.circleBorder[m.ts],borderTopColor:f.zTheme.circleBorderTop[m.ts]},m.c.iconCustomStyle])},null,8,["class","style"])),!m.c.isChat||!m.c.chatDefaultAsLoading&&m.finalStatus===f.M.Default||m.finalStatus===f.M.Fail?(e(),t(T,{key:4,class:s({"zp-l-text-rpx":"rpx"===m.c.unit,"zp-l-text-px":"px"===m.c.unit}),style:a([{color:f.zTheme.title[m.ts]},m.c.titleCustomStyle])},{default:i((()=>[r(l(m.ownLoadingMoreText),1)])),_:1},8,["class","style"])):n("",!0),m.c.showNoMoreLine&&m.finalStatus===f.M.NoMore?(e(),t(T,{key:5,class:s({"zp-l-line-rpx":"rpx"===m.c.unit,"zp-l-line-px":"px"===m.c.unit}),style:a([{backgroundColor:f.zTheme.line[m.ts]},m.c.noMoreLineCustomStyle])},null,8,["class","style"])):n("",!0)],64))])),_:1},8,["class","style","onClick"])}],["__scopeId","data-v-141cb4b6"]]),fe={data:()=>({systemInfo:null,cssSafeAreaInsetBottom:-1,isReadyDestroy:!1}),computed:{windowTop(){if(!this.systemInfo)return 0;return document.getElementsByTagName("uni-page-head").length&&this.systemInfo.windowTop||0},safeAreaBottom(){if(!this.systemInfo)return 0;let e=0;return e=Math.max(this.cssSafeAreaInsetBottom,0),e},isOldWebView(){try{const e=ce.getSystemInfoSync(!0).system.split(" "),t=e[0],i=parseInt(e[1]);if("iOS"===t&&i<=10||"Android"===t&&i<=6)return!0}catch(e){return!1}return!1},zSlots(){return this.$slots}},beforeDestroy(){this.isReadyDestroy=!0},unmounted(){this.isReadyDestroy=!0},methods:{updateFixedLayout(){this.fixed&&this.$nextTick((()=>{this.systemInfo=ce.getSystemInfoSync()}))},_getNodeClientRect(e,t=!0,i=!1){if(this.isReadyDestroy)return Promise.resolve(!1);let o=t?R().in(!0===t?this:t):R();return i?o.select(e).scrollOffset():o.select(e).boundingClientRect(),new Promise(((e,t)=>{o.exec((t=>{e(!(!t||""==t||null==t||!t.length)&&t)}))}))},_updateLeftAndRightWidth(e,t){this.$nextTick((()=>{setTimeout((()=>{["left","right"].map((i=>{this._getNodeClientRect(`.${t}-${i}`).then((t=>{this.$set(e,i,t?t[0].width+"px":"0px")}))}))}),0)}))},_getCssSafeAreaInsetBottom(e){this._getNodeClientRect(".zp-safe-area-inset-bottom").then((t=>{this.cssSafeAreaInsetBottom=t?t[0].height:-1,t&&e&&e()}))},_getSystemInfoSync:(e=!1)=>ce.getSystemInfoSync(e)}},me={props:{defaultPageNo:{type:Number,default:ce.gc("defaultPageNo",1),observer:function(e){this.pageNo=e}},defaultPageSize:{type:Number,default:ce.gc("defaultPageSize",10),validator:e=>(e<=0&&ce.consoleErr("default-page-size必须大于0！"),e>0)},dataKey:{type:[Number,String,Object],default:ce.gc("dataKey",null)},useCache:{type:Boolean,default:ce.gc("useCache",!1)},cacheKey:{type:String,default:ce.gc("cacheKey",null)},cacheMode:{type:String,default:ce.gc("cacheMode",ue.CacheMode.Default)},autowireListName:{type:String,default:ce.gc("autowireListName","")},autowireQueryName:{type:String,default:ce.gc("autowireQueryName","")},fetch:{type:Function,default:null},fetchParams:{type:Object,default:ce.gc("fetchParams",null)},auto:{type:Boolean,default:ce.gc("auto",!0)},reloadWhenRefresh:{type:Boolean,default:ce.gc("reloadWhenRefresh",!0)},autoScrollToTopWhenReload:{type:Boolean,default:ce.gc("autoScrollToTopWhenReload",!0)},autoCleanListWhenReload:{type:Boolean,default:ce.gc("autoCleanListWhenReload",!0)},showRefresherWhenReload:{type:Boolean,default:ce.gc("showRefresherWhenReload",!1)},showLoadingMoreWhenReload:{type:Boolean,default:ce.gc("showLoadingMoreWhenReload",!1)},createdReload:{type:Boolean,default:ce.gc("createdReload",!1)},localPagingLoadingTime:{type:[Number,String],default:ce.gc("localPagingLoadingTime",200)},concat:{type:Boolean,default:ce.gc("concat",!0)},callNetworkReject:{type:Boolean,default:ce.gc("callNetworkReject",!0)},value:{type:Array,default:function(){return[]}},modelValue:{type:Array,default:function(){return[]}}},data:()=>({currentData:[],totalData:[],realTotalData:[],totalLocalPagingList:[],dataPromiseResultMap:{reload:null,complete:null,localPaging:null},isSettingCacheList:!1,pageNo:1,currentRefreshPageSize:0,isLocalPaging:!1,isAddedData:!1,isTotalChangeFromAddData:!1,privateConcat:!0,myParentQuery:-1,firstPageLoaded:!1,pagingLoaded:!1,loaded:!1,isUserReload:!0,fromEmptyViewReload:!1,queryFrom:"",listRendering:!1,isHandlingRefreshToPage:!1,isFirstPageAndNoMore:!1,totalDataChangeThrow:!0}),computed:{pageSize(){return this.defaultPageSize},finalConcat(){return this.concat&&this.privateConcat},finalUseCache(){return this.useCache&&!this.cacheKey&&ce.consoleErr("use-cache为true时，必须设置cache-key，否则缓存无效！"),this.useCache&&!!this.cacheKey},finalCacheKey(){return this.cacheKey?`${q}-${this.cacheKey}`:null},isFirstPage(){return this.pageNo===this.defaultPageNo}},watch:{totalData(e,t){this._totalDataChange(e,t,this.totalDataChangeThrow),this.totalDataChangeThrow=!0},currentData(e,t){this._currentDataChange(e,t)},useChatRecordMode(e,t){e&&(this.nLoadingMoreFixedHeight=!1)},value:{handler(e){e!==this.totalData&&(this.totalDataChangeThrow=!1,this.totalData=e)},immediate:!0},modelValue:{handler(e){e!==this.totalData&&(this.totalDataChangeThrow=!1,this.totalData=e)},immediate:!0}},methods:{complete(e,t=!0){return this.customNoMore=-1,this.addData(e,t)},completeByKey(e,t=null,i=!0){return null!==t&&null!==this.dataKey&&t!==this.dataKey?(this.isFirstPage&&this.endRefresh(),new Promise((e=>e()))):(this.customNoMore=-1,this.addData(e,i))},completeByTotal(e,t,i=!0){if("undefined"==t)this.customNoMore=-1;else{const o=this._checkDataType(e,i,!1);if(e=o.data,i=o.success,t>=0&&i)return new Promise(((o,s)=>{this.$nextTick((()=>{let a=!1;let r=(this.pageNo==this.defaultPageNo?0:this.realTotalData.length)+(this.privateConcat?e.length:0)-t;r>=0&&(a=!0,r=this.defaultPageSize-r,this.privateConcat&&r>0&&r<e.length&&(e=e.splice(0,r))),this.completeByNoMore(e,a,i).then((e=>o(e))).catch((()=>s()))}))}))}return this.addData(e,i)},completeByNoMore(e,t,i=!0){return"undefined"!=t&&(this.customNoMore=1==t?1:0),this.addData(e,i)},completeByError(e){return this.customerEmptyViewErrorText=e,this.complete(!1)},addData(e,t=!0){this.fromCompleteEmit||(this.disabledCompleteEmit=!0,this.fromCompleteEmit=!1);const i=ce.getTime()-this.requestTimeStamp;let o=this.minDelay;this.isFirstPage&&this.finalShowRefresherWhenReload&&(o=Math.max(400,o));const s=this.requestTimeStamp>0&&i<o?o-i:0;return this.$nextTick((()=>{ce.delay((()=>{this._addData(e,t,!1)}),this.delay>0?this.delay:s)})),new Promise(((e,t)=>{this.dataPromiseResultMap.complete={resolve:e,reject:t}}))},addDataFromTop(e,t=!0,i=!0){let o=!this.isChatRecordModeAndNotInversion;e="[object Array]"!==Object.prototype.toString.call(e)?[e]:o?e.reverse():e,this.finalUseVirtualList&&this._setCellIndex(e,"top"),this.totalData=o?[...e,...this.totalData]:[...this.totalData,...e],t&&ce.delay((()=>this.useChatRecordMode?this.scrollToBottom(i):this.scrollToTop(i)))},resetTotalData(e){this.isTotalChangeFromAddData=!0,e="[object Array]"!==Object.prototype.toString.call(e)?[e]:e,this.totalData=e},setLocalPaging(e,t=!0){return this.isLocalPaging=!0,this.$nextTick((()=>{this._addData(e,t,!0)})),new Promise(((e,t)=>{this.dataPromiseResultMap.localPaging={resolve:e,reject:t}}))},reload(e=this.showRefresherWhenReload){return e&&(this.privateShowRefresherWhenReload=e,this.isUserPullDown=!0),this.showLoadingMoreWhenReload||(this.listRendering=!0),this.$nextTick((()=>{this._preReload(e,!1)})),new Promise(((e,t)=>{this.dataPromiseResultMap.reload={resolve:e,reject:t}}))},refresh(){return this._handleRefreshWithDisPageNo(this.pageNo-this.defaultPageNo+1)},refreshToPage(e){return this.isHandlingRefreshToPage=!0,this._handleRefreshWithDisPageNo(e+this.defaultPageNo-1)},updateCache(){this.finalUseCache&&this.totalData.length&&this._saveLocalCache(this.totalData.slice(0,Math.min(this.totalData.length,this.pageSize)))},clean(){this._reload(!0),this._addData([],!0,!1)},clear(){this.clean()},_preReload(e=this.showRefresherWhenReload,t=!0,i=0){const o=this.finalRefresherEnabled&&this.useCustomRefresher;-1===this.customRefresherHeight&&o?ce.delay((()=>{++i%10==0&&this._updateCustomRefresherHeight(),this._preReload(e,t,i)}),J/2):(this.isUserReload=!0,this.loadingType=ue.LoadingType.Refresher,e?(this.privateShowRefresherWhenReload=e,this.useCustomRefresher?this._doRefresherRefreshAnimate():this.refresherTriggered=!0):this._refresherEnd(!1,!1,!1,!1),this._reload(!1,t))},_reload(e=!1,t=!1,i=!1){if(this.isAddedData=!1,this.insideOfPaging=-1,this.cacheScrollNodeHeight=-1,this.pageNo=this.defaultPageNo,this._cleanRefresherEndTimeout(),!this.privateShowRefresherWhenReload&&!e&&this._startLoading(!0),this.firstPageLoaded=!0,this.isTotalChangeFromAddData=!1,this.isSettingCacheList||(this.totalData=[]),!e){this._emitQuery(this.pageNo,this.defaultPageSize,i?ue.QueryFrom.UserPullDown:ue.QueryFrom.Reload);let e=0;ce.delay(this._callMyParentQuery,e),!t&&this.autoScrollToTopWhenReload&&this._scrollToTop(!1)}},_addData(e,t,i){this.isAddedData=!0,this.fromEmptyViewReload=!1,this.isTotalChangeFromAddData=!0,this.refresherTriggered=!1,this._endSystemLoadingAndRefresh();const o=this.isUserPullDown;this.showRefresherUpdateTime&&this.isFirstPage&&(ce.setRefesrherTime(ce.getTime(),this.refresherUpdateTimeKey),this.$refs.refresh&&this.$refs.refresh.updateTime()),!i&&o&&this.isFirstPage&&(this.isUserPullDown=!1),this.listRendering=!0,this.$nextTick((()=>{ce.delay((()=>this.listRendering=!1))}));let s=this._checkDataType(e,t,i);e=s.data,t=s.success;let a=J;if(this.useChatRecordMode&&(a=0),this.loadingForNow=!1,ce.delay((()=>{this.pagingLoaded=!0,this.$nextTick((()=>{!i&&this._refresherEnd(a>0,!0,o)}))})),this.isFirstPage&&(this.isLoadFailed=!t,this.$emit("isLoadFailedChange",this.isLoadFailed),this.finalUseCache&&t&&(this.cacheMode===ue.CacheMode.Always||this.isSettingCacheList)&&this._saveLocalCache(e)),this.isSettingCacheList=!1,t){if((!1!==this.privateConcat||this.isHandlingRefreshToPage||this.loadingStatus!==ue.More.NoMore)&&(this.loadingStatus=ue.More.Default),i){this.totalLocalPagingList=e;const t=this.defaultPageNo,i=this.queryFrom!==ue.QueryFrom.Refresh?this.defaultPageSize:this.currentRefreshPageSize;this._localPagingQueryList(t,i,0,(e=>{ce.delay((()=>{this.completeByTotal(e,this.totalLocalPagingList.length)}),0)}))}else{let t=0;ce.delay((()=>{this._currentDataChange(e,this.currentData),this._callDataPromise(!0,this.totalData)}),t)}this.isHandlingRefreshToPage&&(this.isHandlingRefreshToPage=!1,this.pageNo=this.defaultPageNo+Math.ceil(e.length/this.pageSize)-1,e.length%this.pageSize!=0&&(this.customNoMore=1))}else this._currentDataChange(e,this.currentData),this._callDataPromise(!1),this.loadingStatus=ue.More.Fail,this.isHandlingRefreshToPage=!1,this.loadingType===ue.LoadingType.LoadMore&&this.pageNo--},_totalDataChange(e,t,i=!0){(this.isUserReload&&this.autoCleanListWhenReload||!this.firstPageLoaded||e.length||!t.length)&&(this._doCheckScrollViewShouldFullHeight(e),this.realTotalData.length||e.length||(i=!1),this.realTotalData=e,i&&(this.$emit("input",e),this.$emit("update:modelValue",e),this.$emit("update:list",e),this.$emit("listChange",e),this._callMyParentList(e)),this.firstPageLoaded=!1,this.isTotalChangeFromAddData=!1,this.$nextTick((()=>{ce.delay((()=>{this._getNodeClientRect(".zp-paging-container-content").then((e=>{e&&this.$emit("contentHeightChanged",e[0].height)}))}),J*(this.isIos?1:3))})))},_currentDataChange(e,t){e=[...e],this.finalUseVirtualList&&this._setCellIndex(e,"bottom"),this.isFirstPage&&this.finalConcat&&(this.totalData=[]),-1!==this.customNoMore?(1===this.customNoMore||0!==this.customNoMore&&!e.length)&&(this.loadingStatus=ue.More.NoMore):(!e.length||e.length&&e.length<this.defaultPageSize)&&(this.loadingStatus=ue.More.NoMore),this.totalData.length&&this.finalConcat?(this.oldScrollTop,this.totalData=[...this.totalData,...e]):this.totalData=e,this.privateConcat=!0},_handleRefreshWithDisPageNo(e){if(!this.isHandlingRefreshToPage&&!this.realTotalData.length)return this.reload();if(e>=1){this.loading=!0,this.privateConcat=!1;const t=e*this.pageSize;this.currentRefreshPageSize=t,this.isLocalPaging&&this.isHandlingRefreshToPage?this._localPagingQueryList(this.defaultPageNo,t,0,(e=>{this.complete(e)})):(this._emitQuery(this.defaultPageNo,t,ue.QueryFrom.Refresh),this._callMyParentQuery(this.defaultPageNo,t))}return new Promise(((e,t)=>{this.dataPromiseResultMap.reload={resolve:e,reject:t}}))},_localPagingQueryList(e,t,i,o){e=Math.max(1,e),t=Math.max(1,t);const s=[...this.totalLocalPagingList],a=(e-1)*t,r=Math.min(s.length,a+t),l=s.splice(a,r-a);ce.delay((()=>o(l)),i)},_saveLocalCache(e){g(this.finalCacheKey,e)},_setListByLocalCache(){this.totalData=p(this.finalCacheKey)||[],this.isSettingCacheList=!0},_callMyParentList(e){if(this.autowireListName.length){const t=ce.getParent(this.$parent);t&&t[this.autowireListName]&&(t[this.autowireListName]=e)}},_callMyParentQuery(e=0,t=0){if(this.autowireQueryName){if(-1===this.myParentQuery){const e=ce.getParent(this.$parent);e&&e[this.autowireQueryName]&&(this.myParentQuery=e[this.autowireQueryName])}-1!==this.myParentQuery&&(t>0?this.myParentQuery(e,t):this.myParentQuery(this.pageNo,this.defaultPageSize))}},_emitQuery(e,t,i){this.queryFrom=i,this.requestTimeStamp=ce.getTime();const[o]=this.realTotalData.slice(-1);if(this.fetch){const s=w._handleFetchParams({pageNo:e,pageSize:t,from:i,lastItem:o||null},this.fetchParams),a=this.fetch(s);w._handleFetchResult(a,this,s)||(ce.isPromise(a)?a.then((e=>{this.complete(e)})).catch((e=>{this.complete(!1)})):this.complete(a))}else this.$emit("query",...w._handleQuery(e,t,i,o||null))},_callDataPromise(e,t){for(const i in this.dataPromiseResultMap){const o=this.dataPromiseResultMap[i];o&&(e?o.resolve({totalList:t,noMore:this.loadingStatus===ue.More.NoMore}):this.callNetworkReject&&o.reject(`z-paging-${i}-error`))}},_checkDataType(e,t,i){const o=Object.prototype.toString.call(e);return"[object Boolean]"===o?(t=e,e=[]):"[object Array]"!==o&&(e=[],"[object Undefined]"!==o&&"[object Null]"!==o&&ce.consoleErr((i?"setLocalPaging":"complete")+"参数类型不正确，第一个参数类型必须为Array!")),{data:e,success:t}}}},Te={en:{"zp.refresher.default":"Pull down to refresh","zp.refresher.pulling":"Release to refresh","zp.refresher.refreshing":"Refreshing...","zp.refresher.complete":"Refresh succeeded","zp.refresher.f2":"Refresh to enter 2f","zp.loadingMore.default":"Click to load more","zp.loadingMore.loading":"Loading...","zp.loadingMore.noMore":"No more data","zp.loadingMore.fail":"Load failed,click to reload","zp.emptyView.title":"No data","zp.emptyView.reload":"Reload","zp.emptyView.error":"Sorry,load failed","zp.refresherUpdateTime.title":"Last update: ","zp.refresherUpdateTime.none":"None","zp.refresherUpdateTime.today":"Today","zp.refresherUpdateTime.yesterday":"Yesterday","zp.systemLoading.title":"Loading..."},"zh-Hans":{"zp.refresher.default":"继续下拉刷新","zp.refresher.pulling":"松开立即刷新","zp.refresher.refreshing":"正在刷新...","zp.refresher.complete":"刷新成功","zp.refresher.f2":"松手进入二楼","zp.loadingMore.default":"点击加载更多","zp.loadingMore.loading":"正在加载...","zp.loadingMore.noMore":"没有更多了","zp.loadingMore.fail":"加载失败，点击重新加载","zp.emptyView.title":"没有数据哦~","zp.emptyView.reload":"重新加载","zp.emptyView.error":"很抱歉，加载失败","zp.refresherUpdateTime.title":"最后更新：","zp.refresherUpdateTime.none":"无","zp.refresherUpdateTime.today":"今天","zp.refresherUpdateTime.yesterday":"昨天","zp.systemLoading.title":"加载中..."},"zh-Hant":{"zp.refresher.default":"繼續下拉重繪","zp.refresher.pulling":"鬆開立即重繪","zp.refresher.refreshing":"正在重繪...","zp.refresher.complete":"重繪成功","zp.refresher.f2":"鬆手進入二樓","zp.loadingMore.default":"點擊加載更多","zp.loadingMore.loading":"正在加載...","zp.loadingMore.noMore":"沒有更多了","zp.loadingMore.fail":"加載失敗，點擊重新加載","zp.emptyView.title":"沒有數據哦~","zp.emptyView.reload":"重新加載","zp.emptyView.error":"很抱歉，加載失敗","zp.refresherUpdateTime.title":"最後更新：","zp.refresherUpdateTime.none":"無","zp.refresherUpdateTime.today":"今天","zp.refresherUpdateTime.yesterday":"昨天","zp.systemLoading.title":"加載中..."}},{t:ye}=M(Te),Se={computed:{finalLanguage(){try{const e=C(),t=this.systemInfo.appLanguage;return"auto"===e?w._handleLanguage2Local(t,this._language2Local(t)):e}catch(e){return"zh-Hans"}},finalRefresherDefaultText(){return this._getI18nText("zp.refresher.default",this.refresherDefaultText)},finalRefresherPullingText(){return this._getI18nText("zp.refresher.pulling",this.refresherPullingText)},finalRefresherRefreshingText(){return this._getI18nText("zp.refresher.refreshing",this.refresherRefreshingText)},finalRefresherCompleteText(){return this._getI18nText("zp.refresher.complete",this.refresherCompleteText)},finalRefresherUpdateTimeTextMap:()=>({title:ye("zp.refresherUpdateTime.title"),none:ye("zp.refresherUpdateTime.none"),today:ye("zp.refresherUpdateTime.today"),yesterday:ye("zp.refresherUpdateTime.yesterday")}),finalRefresherGoF2Text(){return this._getI18nText("zp.refresher.f2",this.refresherGoF2Text)},finalLoadingMoreDefaultText(){return this._getI18nText("zp.loadingMore.default",this.loadingMoreDefaultText)},finalLoadingMoreLoadingText(){return this._getI18nText("zp.loadingMore.loading",this.loadingMoreLoadingText)},finalLoadingMoreNoMoreText(){return this._getI18nText("zp.loadingMore.noMore",this.loadingMoreNoMoreText)},finalLoadingMoreFailText(){return this._getI18nText("zp.loadingMore.fail",this.loadingMoreFailText)},finalEmptyViewText(){return this.isLoadFailed?this.finalEmptyViewErrorText:this._getI18nText("zp.emptyView.title",this.emptyViewText)},finalEmptyViewReloadText(){return this._getI18nText("zp.emptyView.reload",this.emptyViewReloadText)},finalEmptyViewErrorText(){return this.customerEmptyViewErrorText||this._getI18nText("zp.emptyView.error",this.emptyViewErrorText)},finalSystemLoadingText(){return this._getI18nText("zp.systemLoading.title",this.systemLoadingText)}},methods:{getLanguage(){return this.finalLanguage},_getI18nText(e,t){const i=Object.prototype.toString.call(t);if("[object Object]"===i){const e=t[this.finalLanguage];if(e)return e}else if("[object String]"===i)return t;return ye(e)},_language2Local(e){const t=e.toLowerCase().replace(new RegExp("_",""),"-");return-1!==t.indexOf("zh")?"zh"===t||"zh-cn"===t||-1!==t.indexOf("zh-hans")?"zh-Hans":"zh-Hant":-1!==t.indexOf("en")?"en":e}}},Re={props:{},data:()=>({nRefresherLoading:!1,nListIsDragging:!1,nShowBottom:!0,nFixFreezing:!1,nShowRefresherReveal:!1,nLoadingMoreFixedHeight:!1,nShowRefresherRevealHeight:0,nOldShowRefresherRevealHeight:-1,nRefresherWidth:ce.rpx2px(750),nF2Opacity:0}),computed:{},mounted(){},methods:{}},we={props:{hideEmptyView:{type:Boolean,default:ce.gc("hideEmptyView",!1)},emptyViewText:{type:[String,Object],default:ce.gc("emptyViewText",null)},showEmptyViewReload:{type:Boolean,default:ce.gc("showEmptyViewReload",!1)},showEmptyViewReloadWhenError:{type:Boolean,default:ce.gc("showEmptyViewReloadWhenError",!0)},emptyViewReloadText:{type:[String,Object],default:ce.gc("emptyViewReloadText",null)},emptyViewImg:{type:String,default:ce.gc("emptyViewImg","")},emptyViewErrorText:{type:[String,Object],default:ce.gc("emptyViewErrorText",null)},emptyViewErrorImg:{type:String,default:ce.gc("emptyViewErrorImg","")},emptyViewStyle:{type:Object,default:ce.gc("emptyViewStyle",{})},emptyViewSuperStyle:{type:Object,default:ce.gc("emptyViewSuperStyle",{})},emptyViewImgStyle:{type:Object,default:ce.gc("emptyViewImgStyle",{})},emptyViewTitleStyle:{type:Object,default:ce.gc("emptyViewTitleStyle",{})},emptyViewReloadStyle:{type:Object,default:ce.gc("emptyViewReloadStyle",{})},emptyViewFixed:{type:Boolean,default:ce.gc("emptyViewFixed",!1)},emptyViewCenter:{type:Boolean,default:ce.gc("emptyViewCenter",!0)},autoHideEmptyViewWhenLoading:{type:Boolean,default:ce.gc("autoHideEmptyViewWhenLoading",!0)},autoHideEmptyViewWhenPull:{type:Boolean,default:ce.gc("autoHideEmptyViewWhenPull",!0)},emptyViewZIndex:{type:Number,default:ce.gc("emptyViewZIndex",9)}},data:()=>({customerEmptyViewErrorText:""}),computed:{finalEmptyViewImg(){return this.isLoadFailed?this.emptyViewErrorImg:this.emptyViewImg},finalShowEmptyViewReload(){return this.isLoadFailed?this.showEmptyViewReloadWhenError:this.showEmptyViewReload},showEmpty(){return!(this.refresherOnly||this.hideEmptyView||this.realTotalData.length)&&(!this.autoHideEmptyViewWhenLoading||(!(!this.isAddedData||this.firstPageLoaded||this.loading)||!this.autoHideEmptyViewWhenPull&&!this.isUserReload))}},methods:{_emptyViewReload(){let e=!1;this.$emit("emptyViewReload",(t=>{void 0!==t&&!0!==t||(this.fromEmptyViewReload=!0,this.reload().catch((()=>{}))),e=!0})),this.$nextTick((()=>{e||(this.fromEmptyViewReload=!0,this.reload().catch((()=>{})))}))},_emptyViewClick(){this.$emit("emptyViewClick")}}},Me={props:{refresherThemeStyle:{type:String,default:ce.gc("refresherThemeStyle","")},refresherImgStyle:{type:Object,default:ce.gc("refresherImgStyle",{})},refresherTitleStyle:{type:Object,default:ce.gc("refresherTitleStyle",{})},refresherUpdateTimeStyle:{type:Object,default:ce.gc("refresherUpdateTimeStyle",{})},watchRefresherTouchmove:{type:Boolean,default:ce.gc("watchRefresherTouchmove",!1)},loadingMoreThemeStyle:{type:String,default:ce.gc("loadingMoreThemeStyle","")},refresherOnly:{type:Boolean,default:ce.gc("refresherOnly",!1)},refresherDefaultDuration:{type:[Number,String],default:ce.gc("refresherDefaultDuration",100)},refresherCompleteDelay:{type:[Number,String],default:ce.gc("refresherCompleteDelay",0)},refresherCompleteDuration:{type:[Number,String],default:ce.gc("refresherCompleteDuration",300)},refresherRefreshingScrollable:{type:Boolean,default:ce.gc("refresherRefreshingScrollable",!0)},refresherCompleteScrollable:{type:Boolean,default:ce.gc("refresherCompleteScrollable",!1)},useCustomRefresher:{type:Boolean,default:ce.gc("useCustomRefresher",!0)},refresherFps:{type:[Number,String],default:ce.gc("refresherFps",40)},refresherMaxAngle:{type:[Number,String],default:ce.gc("refresherMaxAngle",40)},refresherAngleEnableChangeContinued:{type:Boolean,default:ce.gc("refresherAngleEnableChangeContinued",!1)},refresherDefaultText:{type:[String,Object],default:ce.gc("refresherDefaultText",null)},refresherPullingText:{type:[String,Object],default:ce.gc("refresherPullingText",null)},refresherRefreshingText:{type:[String,Object],default:ce.gc("refresherRefreshingText",null)},refresherCompleteText:{type:[String,Object],default:ce.gc("refresherCompleteText",null)},refresherGoF2Text:{type:[String,Object],default:ce.gc("refresherGoF2Text",null)},refresherDefaultImg:{type:String,default:ce.gc("refresherDefaultImg",null)},refresherPullingImg:{type:String,default:ce.gc("refresherPullingImg",null)},refresherRefreshingImg:{type:String,default:ce.gc("refresherRefreshingImg",null)},refresherCompleteImg:{type:String,default:ce.gc("refresherCompleteImg",null)},refresherRefreshingAnimated:{type:Boolean,default:ce.gc("refresherRefreshingAnimated",!0)},refresherEndBounceEnabled:{type:Boolean,default:ce.gc("refresherEndBounceEnabled",!0)},refresherEnabled:{type:Boolean,default:ce.gc("refresherEnabled",!0)},refresherThreshold:{type:[Number,String],default:ce.gc("refresherThreshold","80rpx")},refresherDefaultStyle:{type:String,default:ce.gc("refresherDefaultStyle","black")},refresherBackground:{type:String,default:ce.gc("refresherBackground","transparent")},refresherFixedBackground:{type:String,default:ce.gc("refresherFixedBackground","transparent")},refresherFixedBacHeight:{type:[Number,String],default:ce.gc("refresherFixedBacHeight",0)},refresherOutRate:{type:Number,default:ce.gc("refresherOutRate",.65)},refresherF2Enabled:{type:Boolean,default:ce.gc("refresherF2Enabled",!1)},refresherF2Threshold:{type:[Number,String],default:ce.gc("refresherF2Threshold","200rpx")},refresherF2Duration:{type:[Number,String],default:ce.gc("refresherF2Duration",200)},showRefresherF2:{type:Boolean,default:ce.gc("showRefresherF2",!0)},refresherPullRate:{type:Number,default:ce.gc("refresherPullRate",.75)},showRefresherUpdateTime:{type:Boolean,default:ce.gc("showRefresherUpdateTime",!1)},refresherUpdateTimeKey:{type:String,default:ce.gc("refresherUpdateTimeKey","default")},refresherVibrate:{type:Boolean,default:ce.gc("refresherVibrate",!1)},refresherNoTransform:{type:Boolean,default:ce.gc("refresherNoTransform",!1)},useRefresherStatusBarPlaceholder:{type:Boolean,default:ce.gc("useRefresherStatusBarPlaceholder",!1)}},data:()=>({R:ue.Refresher,refresherStatus:ue.Refresher.Default,refresherTouchstartY:0,lastRefresherTouchmove:null,refresherReachMaxAngle:!0,refresherTransform:"translateY(0px)",refresherTransition:"",finalRefresherDefaultStyle:"black",refresherRevealStackCount:0,refresherCompleteTimeout:null,refresherCompleteSubTimeout:null,refresherEndTimeout:null,isTouchmovingTimeout:null,refresherTriggered:!1,isTouchmoving:!1,isTouchEnded:!1,isUserPullDown:!1,privateRefresherEnabled:-1,privateShowRefresherWhenReload:!1,customRefresherHeight:-1,showCustomRefresher:!1,doRefreshAnimateAfter:!1,isRefresherInComplete:!1,showF2:!1,f2Transform:"",pullDownTimeStamp:0,moveDis:0,oldMoveDis:0,currentDis:0,oldCurrentMoveDis:0,oldRefresherTouchmoveY:0,oldTouchDirection:"",oldEmitedTouchDirection:"",oldPullingDistance:-1,refresherThresholdUpdateTag:0}),watch:{refresherDefaultStyle:{handler(e){e.length&&(this.finalRefresherDefaultStyle=e)},immediate:!0},refresherStatus(e){e===ue.Refresher.Loading&&this._cleanRefresherEndTimeout(),this.refresherVibrate&&(e===ue.Refresher.ReleaseToRefresh||e===ue.Refresher.GoF2)&&this._doVibrateShort(),this.$emit("refresherStatusChange",e),this.$emit("update:refresherStatus",e)},refresherEnabled(e){!e&&this.endRefresh()}},computed:{pullDownDisTimeStamp(){return 1e3/this.refresherFps},refresherThresholdUnitConverted(){return ce.addUnit(this.refresherThreshold,this.unit)},finalRefresherEnabled(){return!this.useChatRecordMode&&(-1===this.privateRefresherEnabled?this.refresherEnabled:1===this.privateRefresherEnabled)},finalRefresherThreshold(){let e=this.refresherThresholdUnitConverted,t=!1;return e===ce.addUnit(80,this.unit)&&(t=!0,this.showRefresherUpdateTime&&(e=ce.addUnit(120,this.unit))),t&&this.customRefresherHeight>0?this.customRefresherHeight+this.finalRefresherThresholdPlaceholder:ce.convertToPx(e)+this.finalRefresherThresholdPlaceholder},finalRefresherF2Threshold(){return ce.convertToPx(ce.addUnit(this.refresherF2Threshold,this.unit))},finalRefresherThresholdPlaceholder(){return this.useRefresherStatusBarPlaceholder?this.statusBarHeight:0},finalRefresherFixedBacHeight(){return ce.convertToPx(this.refresherFixedBacHeight)},finalRefresherThemeStyle(){return this.refresherThemeStyle.length?this.refresherThemeStyle:this.defaultThemeStyle},finalRefresherOutRate(){let e=this.refresherOutRate;return e=Math.max(0,e),e=Math.min(1,e),e},finalRefresherPullRate(){let e=this.refresherPullRate;return e=Math.max(0,e),e},finalRefresherTransform(){return this.refresherNoTransform||"translateY(0px)"===this.refresherTransform?"none":this.refresherTransform},finalShowRefresherWhenReload(){return this.showRefresherWhenReload||this.privateShowRefresherWhenReload},finalRefresherTriggered(){return!(!this.finalRefresherEnabled||this.useCustomRefresher)&&this.refresherTriggered},showRefresher(){const e=this.finalRefresherEnabled||this.useCustomRefresher&&!this.useChatRecordMode;return this.active&&-1===this.customRefresherHeight&&e&&this.updateCustomRefresherHeight(),e},hasTouchmove(){return this.watchRefresherTouchmove}},methods:{endRefresh(){this.totalData=this.realTotalData,this._refresherEnd(),this._endSystemLoadingAndRefresh(),this._handleScrollViewBounce({bounce:!0}),this.$nextTick((()=>{this.refresherTriggered=!1}))},updateCustomRefresherHeight(){ce.delay((()=>this.$nextTick(this._updateCustomRefresherHeight)))},closeF2(){this._handleCloseF2()},_onRefresh(e=!1,t=!0){(!e||this.finalRefresherEnabled&&!this.useCustomRefresher)&&(this.$emit("onRefresh"),this.$emit("Refresh"),this.loading||this.isRefresherInComplete||(this.loadingType=ue.LoadingType.Refresher,this.nShowRefresherReveal||(this.isUserPullDown=t,this.isUserReload=!t,this._startLoading(!0),this.refresherTriggered=!0,this.reloadWhenRefresh&&t&&(this.useChatRecordMode?this._onLoadingMore("click"):this._reload(!1,!1,t)))))},_onRestore(){this.refresherTriggered="restore",this.$emit("onRestore"),this.$emit("Restore")},_handleRefresherTouchstart(e){!this.loading&&this.isTouchEnded&&(this.isTouchmoving=!1),this.loadingType=ue.LoadingType.Refresher,this.isTouchmovingTimeout&&clearTimeout(this.isTouchmovingTimeout),this.isTouchEnded=!1,this.refresherTransition="",this.refresherTouchstartY=e.touchY,this.$emit("refresherTouchstart",this.refresherTouchstartY),this.lastRefresherTouchmove=e,this._cleanRefresherCompleteTimeout(),this._cleanRefresherEndTimeout()},_handleRefresherTouchmove(e,t){this.refresherReachMaxAngle=!0,this.isTouchmovingTimeout&&clearTimeout(this.isTouchmovingTimeout),this.isTouchmoving=!0,this.isTouchEnded=!1,e>=this.finalRefresherThreshold?this.refresherStatus=this.refresherF2Enabled&&e>=this.finalRefresherF2Threshold?ue.Refresher.GoF2:ue.Refresher.ReleaseToRefresh:this.refresherStatus=ue.Refresher.Default,this.moveDis=e},_handleRefresherTouchend(e){this.isTouchmovingTimeout&&clearTimeout(this.isTouchmovingTimeout),this.refresherReachMaxAngle=!0,this.isTouchEnded=!0;const t=this.finalRefresherThreshold;e>=t&&(this.refresherStatus===ue.Refresher.ReleaseToRefresh||this.refresherStatus===ue.Refresher.GoF2)?this.refresherStatus===ue.Refresher.GoF2?(this._handleGoF2(),this._refresherEnd()):(ce.delay((()=>{this._emitTouchmove({pullingDistance:t,dy:this.moveDis-t})}),.1),this.moveDis=t,this.refresherStatus=ue.Refresher.Loading,this._doRefresherLoad()):(this._refresherEnd(),this.isTouchmovingTimeout=ce.delay((()=>{this.isTouchmoving=!1}),this.refresherDefaultDuration)),this.scrollEnable=!0,this.$emit("refresherTouchend",e)},_handleListTouchstart(){this.useChatRecordMode&&this.autoHideKeyboardWhenChat&&(x(),this.$emit("hidedKeyboard"))},_handleScrollViewBounce({bounce:e}){this.usePageScroll||this.scrollToTopBounceEnabled||(this.wxsScrollTop<=5?(this.refresherTransition="",this.scrollEnable=e):e&&(this.scrollEnable=e))},_handleWxsPullingDownStatusChange(e){this.wxsOnPullingDown=e,e&&!this.useChatRecordMode&&(this.renderPropScrollTop=0)},_handleWxsPullingDown({moveDis:e,diffDis:t}){this._emitTouchmove({pullingDistance:e,dy:t})},_handleTouchDirectionChange({direction:e}){this.$emit("touchDirectionChange",e)},_handlePropUpdate(){this.wxsPropType=ce.getTime().toString()},_refresherEnd(e=!0,t=!1,i=!1,o=!0){if(this.loadingType===ue.LoadingType.Refresher){const e=t&&(i||this.showRefresherWhenReload)?this.refresherCompleteDelay:0,o=e>0?ue.Refresher.Complete:ue.Refresher.Default;if(this.finalShowRefresherWhenReload){const e=this.refresherRevealStackCount;if(this.refresherRevealStackCount--,e>1)return}this._cleanRefresherEndTimeout(),this.refresherEndTimeout=ce.delay((()=>{this.refresherStatus=o,o!==ue.Refresher.Complete&&(this.isRefresherInComplete=!1)}),this.refresherStatus!==ue.Refresher.Default&&o===ue.Refresher.Default?this.refresherCompleteDuration:0),e>0&&(this.isRefresherInComplete=!0),this._cleanRefresherCompleteTimeout(),this.refresherCompleteTimeout=ce.delay((()=>{let e=1;const i=this.refresherEndBounceEnabled&&t?"cubic-bezier(0.19,1.64,0.42,0.72)":"linear";t&&(e=this.refresherEndBounceEnabled?this.refresherCompleteDuration/1e3:this.refresherCompleteDuration/3e3),this.refresherTransition=`transform ${t?e:this.refresherDefaultDuration/1e3}s ${i}`,this.wxsPropType=this.refresherTransition+"end"+ce.getTime(),this.moveDis=0,o===ue.Refresher.Complete&&(this.refresherCompleteSubTimeout&&(clearTimeout(this.refresherCompleteSubTimeout),this.refresherCompleteSubTimeout=null),this.refresherCompleteSubTimeout=ce.delay((()=>{this.$nextTick((()=>{this.refresherStatus=ue.Refresher.Default,this.isRefresherInComplete=!1}))}),800*e)),this._emitTouchmove({pullingDistance:0,dy:this.moveDis})}),e)}o&&(ce.delay((()=>this.loading=!1),e?10:0),i&&this._onRestore())},_handleGoF2(){!this.showF2&&this.refresherF2Enabled&&(this.$emit("refresherF2Change","go"),this.showRefresherF2&&(this.f2Transform=`translateY(${-this.superContentHeight}px)`,this.showF2=!0,ce.delay((()=>{this.f2Transform="translateY(0px)"}),100,"f2ShowDelay")))},_handleCloseF2(){this.showF2&&this.refresherF2Enabled&&(this.$emit("refresherF2Change","close"),this.showRefresherF2&&(this.f2Transform=`translateY(${-this.superContentHeight}px)`,ce.delay((()=>{this.showF2=!1,this.nF2Opacity=0}),this.refresherF2Duration,"f2CloseDelay")))},_doRefresherRefreshAnimate(){this._cleanRefresherCompleteTimeout();!this.doRefreshAnimateAfter&&this.finalShowRefresherWhenReload&&-1===this.customRefresherHeight&&this.refresherThreshold===ce.addUnit(80,this.unit)?this.doRefreshAnimateAfter=!0:(this.refresherRevealStackCount++,this.wxsPropType="begin"+ce.getTime(),this.moveDis=this.finalRefresherThreshold,this.refresherStatus=ue.Refresher.Loading,this.isTouchmoving=!0,this.isTouchmovingTimeout&&clearTimeout(this.isTouchmovingTimeout),this._doRefresherLoad(!1))},_doRefresherLoad(e=!0){this._onRefresh(!1,e),this.loading=!0},_updateCustomRefresherHeight(){this._getNodeClientRect(".zp-custom-refresher-slot-view").then((e=>{this.customRefresherHeight=e?e[0].height:0,this.showCustomRefresher=this.customRefresherHeight>0,this.doRefreshAnimateAfter&&(this.doRefreshAnimateAfter=!1,this._doRefresherRefreshAnimate())}))},_emitTouchmove(e){e.viewHeight=this.finalRefresherThreshold,e.rate=e.viewHeight>0?e.pullingDistance/e.viewHeight:0,this.hasTouchmove&&this.oldPullingDistance!==e.pullingDistance&&this.$emit("refresherTouchmove",e),this.oldPullingDistance=e.pullingDistance},_cleanRefresherCompleteTimeout(){this.refresherCompleteTimeout=this._cleanTimeout(this.refresherCompleteTimeout)},_cleanRefresherEndTimeout(){this.refresherEndTimeout=this._cleanTimeout(this.refresherEndTimeout)}}},Ce={props:{loadingMoreCustomStyle:{type:Object,default:ce.gc("loadingMoreCustomStyle",{})},loadingMoreTitleCustomStyle:{type:Object,default:ce.gc("loadingMoreTitleCustomStyle",{})},loadingMoreLoadingIconCustomStyle:{type:Object,default:ce.gc("loadingMoreLoadingIconCustomStyle",{})},loadingMoreLoadingIconType:{type:String,default:ce.gc("loadingMoreLoadingIconType","flower")},loadingMoreLoadingIconCustomImage:{type:String,default:ce.gc("loadingMoreLoadingIconCustomImage","")},loadingMoreLoadingAnimated:{type:Boolean,default:ce.gc("loadingMoreLoadingAnimated",!0)},loadingMoreEnabled:{type:Boolean,default:ce.gc("loadingMoreEnabled",!0)},toBottomLoadingMoreEnabled:{type:Boolean,default:ce.gc("toBottomLoadingMoreEnabled",!0)},loadingMoreDefaultAsLoading:{type:Boolean,default:ce.gc("loadingMoreDefaultAsLoading",!1)},loadingMoreDefaultText:{type:[String,Object],default:ce.gc("loadingMoreDefaultText",null)},loadingMoreLoadingText:{type:[String,Object],default:ce.gc("loadingMoreLoadingText",null)},loadingMoreNoMoreText:{type:[String,Object],default:ce.gc("loadingMoreNoMoreText",null)},loadingMoreFailText:{type:[String,Object],default:ce.gc("loadingMoreFailText",null)},hideNoMoreInside:{type:Boolean,default:ce.gc("hideNoMoreInside",!1)},hideNoMoreByLimit:{type:Number,default:ce.gc("hideNoMoreByLimit",0)},showDefaultLoadingMoreText:{type:Boolean,default:ce.gc("showDefaultLoadingMoreText",!0)},showLoadingMoreNoMoreView:{type:Boolean,default:ce.gc("showLoadingMoreNoMoreView",!0)},showLoadingMoreNoMoreLine:{type:Boolean,default:ce.gc("showLoadingMoreNoMoreLine",!0)},loadingMoreNoMoreLineCustomStyle:{type:Object,default:ce.gc("loadingMoreNoMoreLineCustomStyle",{})},insideMore:{type:Boolean,default:ce.gc("insideMore",!1)},lowerThreshold:{type:[Number,String],default:ce.gc("lowerThreshold","100rpx")}},data:()=>({M:ue.More,loadingStatus:ue.More.Default,loadingStatusAfterRender:ue.More.Default,loadingMoreTimeStamp:0,loadingMoreDefaultSlot:null,showLoadingMore:!1,customNoMore:-1}),computed:{zLoadMoreConfig(){return{status:this.loadingStatusAfterRender,defaultAsLoading:this.loadingMoreDefaultAsLoading||this.useChatRecordMode&&this.chatLoadingMoreDefaultAsLoading,defaultThemeStyle:this.finalLoadingMoreThemeStyle,customStyle:this.loadingMoreCustomStyle,titleCustomStyle:this.loadingMoreTitleCustomStyle,iconCustomStyle:this.loadingMoreLoadingIconCustomStyle,loadingIconType:this.loadingMoreLoadingIconType,loadingIconCustomImage:this.loadingMoreLoadingIconCustomImage,loadingAnimated:this.loadingMoreLoadingAnimated,showNoMoreLine:this.showLoadingMoreNoMoreLine,noMoreLineCustomStyle:this.loadingMoreNoMoreLineCustomStyle,defaultText:this.finalLoadingMoreDefaultText,loadingText:this.finalLoadingMoreLoadingText,noMoreText:this.finalLoadingMoreNoMoreText,failText:this.finalLoadingMoreFailText,hideContent:!this.loadingMoreDefaultAsLoading&&this.listRendering,unit:this.unit,isChat:this.useChatRecordMode,chatDefaultAsLoading:this.chatLoadingMoreDefaultAsLoading}},finalLoadingMoreThemeStyle(){return this.loadingMoreThemeStyle.length?this.loadingMoreThemeStyle:this.defaultThemeStyle},finalLowerThreshold(){return ce.convertToPx(this.lowerThreshold)},showLoadingMoreDefault(){return this._showLoadingMore("Default")},showLoadingMoreLoading(){return this._showLoadingMore("Loading")},showLoadingMoreNoMore(){return this._showLoadingMore("NoMore")},showLoadingMoreFail(){return this._showLoadingMore("Fail")},showLoadingMoreCustom(){return this._showLoadingMore("Custom")},loadingMoreFixedHeight(){return ce.addUnit("80rpx",this.unit)}},methods:{pageReachBottom(){!this.useChatRecordMode&&this.toBottomLoadingMoreEnabled&&this._onLoadingMore("toBottom")},doLoadMore(e){this._onLoadingMore(e)},_checkScrolledToBottom(e,t=!1){-1===this.cacheScrollNodeHeight?this._getNodeClientRect(".zp-scroll-view").then((t=>{if(t){const i=t[0].height;this.cacheScrollNodeHeight=i,e-i<=this.finalLowerThreshold&&this._onLoadingMore("toBottom")}})):(e-this.cacheScrollNodeHeight<=this.finalLowerThreshold?this._onLoadingMore("toBottom"):e-this.cacheScrollNodeHeight<=500&&!t&&ce.delay((()=>{this._getNodeClientRect(".zp-scroll-view",!0,!0).then((e=>{if(e){this.oldScrollTop=e[0].scrollTop;const t=e[0].scrollHeight-this.oldScrollTop;this._checkScrolledToBottom(t,!0)}}))}),150,"checkScrolledToBottomDelay"),this.oldScrollTop<=150&&0!==this.oldScrollTop&&ce.delay((()=>{0!==this.oldScrollTop&&this._getNodeClientRect(".zp-scroll-view",!0,!0).then((e=>{e&&0===e[0].scrollTop&&0!==this.oldScrollTop&&this._onScrollToUpper()}))}),150,"checkScrolledToTopDelay"))},_onLoadingMore(e="click"){this.isIos&&"toBottom"===e&&!this.scrollToBottomBounceEnabled&&this.scrollEnable&&(this.scrollEnable=!1,this.$nextTick((()=>{this.scrollEnable=!0}))),this._emitScrollEvent("scrolltolower"),this.refresherOnly||!this.loadingMoreEnabled||this.loadingStatus!==ue.More.Default&&this.loadingStatus!==ue.More.Fail||this.loading||this.showEmpty||this._doLoadingMore()},_doLoadingMore(){this.pageNo>=this.defaultPageNo&&this.loadingStatus!==ue.More.NoMore&&(this.pageNo++,this._startLoading(!1),this.isLocalPaging?this._localPagingQueryList(this.pageNo,this.defaultPageSize,this.localPagingLoadingTime,(e=>{this.completeByTotal(e,this.totalLocalPagingList.length),this.queryFrom=ue.QueryFrom.LoadMore})):(this._emitQuery(this.pageNo,this.defaultPageSize,ue.QueryFrom.LoadMore),this._callMyParentQuery()),this.loadingType=ue.LoadingType.LoadMore)},_preCheckShowNoMoreInside(e,t,i){this.loadingStatus===ue.More.NoMore&&this.hideNoMoreByLimit>0&&e.length?this.showLoadingMore=e.length>this.hideNoMoreByLimit:this.loadingStatus===ue.More.NoMore&&this.hideNoMoreInside&&e.length||this.insideMore&&!1!==this.insideOfPaging&&e.length?(this.$nextTick((()=>{this._checkShowNoMoreInside(e,t,i)})),this.insideMore&&!1!==this.insideOfPaging&&e.length&&(this.showLoadingMore=e.length)):this.showLoadingMore=e.length},async _checkShowNoMoreInside(e,t,i){try{const e=t||await this._getNodeClientRect(".zp-scroll-view");if(this.usePageScroll){if(e){const t=e[0].top+e[0].height;this.insideOfPaging=t<this.windowHeight,this.hideNoMoreInside&&(this.showLoadingMore=!this.insideOfPaging),this._updateInsideOfPaging()}}else{const t=i||await this._getNodeClientRect(".zp-paging-container-content"),o=t?t[0].height:0,s=e?e[0].height:0;this.insideOfPaging=o<s,this.hideNoMoreInside&&(this.showLoadingMore=!this.insideOfPaging),this._updateInsideOfPaging()}}catch(o){this.insideOfPaging=!e.length,this.hideNoMoreInside&&(this.showLoadingMore=!this.insideOfPaging),this._updateInsideOfPaging()}},_showLoadingMore(e){if(!this.showLoadingMoreWhenReload&&(this.loadingStatus===ue.More.Default&&!this.nShowBottom||!this.realTotalData.length))return!1;if((!this.showLoadingMoreWhenReload||this.isUserPullDown||this.loadingStatus!==ue.More.Loading)&&!this.showLoadingMore||!this.loadingMoreEnabled&&(!this.showLoadingMoreWhenReload||this.isUserPullDown||this.loadingStatus!==ue.More.Loading)||this.refresherOnly)return!1;if(this.useChatRecordMode&&"Loading"!==e)return!1;if(!this.zSlots)return!1;if("Custom"===e)return this.showDefaultLoadingMoreText&&!(this.loadingStatus===ue.More.NoMore&&!this.showLoadingMoreNoMoreView);return this.loadingStatus===ue.More[e]&&this.zSlots[`loadingMore${e}`]&&("NoMore"!==e||this.showLoadingMoreNoMoreView)}}},xe={props:{autoHideLoadingAfterFirstLoaded:{type:Boolean,default:ce.gc("autoHideLoadingAfterFirstLoaded",!0)},loadingFullFixed:{type:Boolean,default:ce.gc("loadingFullFixed",!1)},autoShowSystemLoading:{type:Boolean,default:ce.gc("autoShowSystemLoading",!1)},systemLoadingMask:{type:Boolean,default:ce.gc("systemLoadingMask",!0)},systemLoadingText:{type:[String,Object],default:ce.gc("systemLoadingText",null)}},data:()=>({loading:!1,loadingForNow:!1}),watch:{loadingStatus(e){this.$emit("loadingStatusChange",e),this.$nextTick((()=>{this.loadingStatusAfterRender=e})),!this.useChatRecordMode||!this.isFirstPage||e!==ue.More.NoMore&&e!==ue.More.Fail?this.isFirstPageAndNoMore=!1:this.isFirstPageAndNoMore=!0},loading(e){e&&(this.loadingForNow=e)}},computed:{showLoading(){return!(this.firstPageLoaded||!this.loading||!this.loadingForNow)&&(this.finalShowSystemLoading&&I({title:this.finalSystemLoadingText,mask:this.systemLoadingMask}),this.autoHideLoadingAfterFirstLoaded?!!this.fromEmptyViewReload||!this.pagingLoaded:this.loadingType===ue.LoadingType.Refresher)},finalShowSystemLoading(){return this.autoShowSystemLoading&&this.loadingType===ue.LoadingType.Refresher}},methods:{_startLoading(e=!1){(this.showLoadingMoreWhenReload&&!this.isUserPullDown||!e)&&(this.loadingStatus=ue.More.Loading),this.loading=!0},_endSystemLoadingAndRefresh(){this.finalShowSystemLoading&&A(),!this.useCustomRefresher&&b()}}},Ie={props:{useChatRecordMode:{type:Boolean,default:ce.gc("useChatRecordMode",!1)},chatRecordMoreOffset:{type:[Number,String],default:ce.gc("chatRecordMoreOffset","0rpx")},autoHideKeyboardWhenChat:{type:Boolean,default:ce.gc("autoHideKeyboardWhenChat",!0)},autoAdjustPositionWhenChat:{type:Boolean,default:ce.gc("autoAdjustPositionWhenChat",!0)},chatAdjustPositionOffset:{type:[Number,String],default:ce.gc("chatAdjustPositionOffset","0rpx")},autoToBottomWhenChat:{type:Boolean,default:ce.gc("autoToBottomWhenChat",!1)},showChatLoadingWhenReload:{type:Boolean,default:ce.gc("showChatLoadingWhenReload",!1)},chatLoadingMoreDefaultAsLoading:{type:Boolean,default:ce.gc("chatLoadingMoreDefaultAsLoading",!0)}},data:()=>({keyboardHeight:0,isKeyboardHeightChanged:!1}),computed:{finalChatRecordMoreOffset(){return ce.convertToPx(this.chatRecordMoreOffset)},finalChatAdjustPositionOffset(){return ce.convertToPx(this.chatAdjustPositionOffset)},chatRecordRotateStyle(){let e;return e=this.useChatRecordMode?{transform:"scaleY(-1)"}:{},this.$emit("update:cellStyle",e),this.$emit("cellStyleChange",e),this.$nextTick((()=>{this.isFirstPage&&this.isChatRecordModeAndNotInversion&&this.$nextTick((()=>{this._scrollToBottom(!1),ce.delay((()=>{this._scrollToBottom(!1),ce.delay((()=>{this._scrollToBottom(!1)}),50)}),50)}))})),e},isChatRecordModeHasTransform(){return this.useChatRecordMode&&this.chatRecordRotateStyle&&this.chatRecordRotateStyle.transform},isChatRecordModeAndNotInversion(){return this.isChatRecordModeHasTransform&&"scaleY(1)"===this.chatRecordRotateStyle.transform},isChatRecordModeAndInversion(){return this.isChatRecordModeHasTransform&&"scaleY(-1)"===this.chatRecordRotateStyle.transform},chatRecordModeSafeAreaBottom(){return this.safeAreaInsetBottom&&!this.keyboardHeight?this.safeAreaBottom:0}},mounted(){},methods:{addChatRecordData(e,t=!0,i=!0){this.useChatRecordMode&&(this.isTotalChangeFromAddData=!0,this.addDataFromTop(e,t,i))},doChatRecordLoadMore(){this.useChatRecordMode&&this._onLoadingMore("click")},_handleKeyboardHeightChange(e){this.$emit("keyboardHeightChange",e),this.autoAdjustPositionWhenChat&&(this.isKeyboardHeightChanged=!0,this.keyboardHeight=e.height>0?e.height+this.finalChatAdjustPositionOffset:e.height),this.autoToBottomWhenChat&&this.keyboardHeight>0&&ce.delay((()=>{this.scrollToBottom(!1),ce.delay((()=>{this.scrollToBottom(!1)}))}))}}},Ae={props:{usePageScroll:{type:Boolean,default:ce.gc("usePageScroll",!1)},scrollable:{type:Boolean,default:ce.gc("scrollable",!0)},showScrollbar:{type:Boolean,default:ce.gc("showScrollbar",!0)},scrollX:{type:Boolean,default:ce.gc("scrollX",!1)},scrollToTopBounceEnabled:{type:Boolean,default:ce.gc("scrollToTopBounceEnabled",!1)},scrollToBottomBounceEnabled:{type:Boolean,default:ce.gc("scrollToBottomBounceEnabled",!0)},scrollWithAnimation:{type:Boolean,default:ce.gc("scrollWithAnimation",!1)},scrollIntoView:{type:String,default:ce.gc("scrollIntoView","")}},data:()=>({scrollTop:0,oldScrollTop:0,scrollLeft:0,oldScrollLeft:0,scrollViewStyle:{},scrollViewContainerStyle:{},scrollViewInStyle:{},pageScrollTop:-1,scrollEnable:!0,privateScrollWithAnimation:-1,cacheScrollNodeHeight:-1,superContentHeight:0}),watch:{oldScrollTop(e){!this.usePageScroll&&this._scrollTopChange(e,!1)},pageScrollTop(e){this.usePageScroll&&this._scrollTopChange(e,!0)},usePageScroll:{handler(e){this.loaded&&this.autoHeight&&this._setAutoHeight(!e),e&&this.$nextTick((()=>{const e=this.$refs["zp-scroll-view"].$refs.main;e&&(e.style={})}))},immediate:!0},finalScrollTop(e){this.renderPropScrollTop=e<6?0:10}},computed:{finalScrollWithAnimation(){return-1!==this.privateScrollWithAnimation?1===this.privateScrollWithAnimation:this.scrollWithAnimation},finalScrollViewStyle(){return 1!=this.superContentZIndex&&(this.scrollViewStyle["z-index"]=this.superContentZIndex,this.scrollViewStyle.position="relative"),this.scrollViewStyle},finalScrollTop(){return this.usePageScroll?this.pageScrollTop:this.oldScrollTop},finalIsOldWebView(){return this.isOldWebView&&!this.usePageScroll},finalScrollable(){return this.scrollable&&!this.usePageScroll&&this.scrollEnable&&(!!this.refresherCompleteScrollable||this.refresherStatus!==ue.Refresher.Complete)&&(!!this.refresherRefreshingScrollable||this.refresherStatus!==ue.Refresher.Loading)}},methods:{scrollToTop(e,t=!0){this.useChatRecordMode&&t&&!this.isChatRecordModeAndNotInversion?this.scrollToBottom(e,!1):this.$nextTick((()=>{this._scrollToTop(e,!1)}))},scrollToBottom(e,t=!0){this.useChatRecordMode&&t&&!this.isChatRecordModeAndNotInversion?this.scrollToTop(e,!1):this.$nextTick((()=>{this._scrollToBottom(e)}))},scrollIntoViewById(e,t,i){this._scrollIntoView(e,t,i)},scrollIntoViewByNodeTop(e,t,i){this.scrollTop=this.oldScrollTop,this.$nextTick((()=>{this._scrollIntoViewByNodeTop(e,t,i)}))},scrollToY(e,t,i){this.scrollTop=this.oldScrollTop,this.$nextTick((()=>{this._scrollToY(e,t,i)}))},scrollToX(e,t,i){this.scrollLeft=this.oldScrollLeft,this.$nextTick((()=>{this._scrollToX(e,t,i)}))},scrollIntoViewByIndex(e,t,i){e>=this.realTotalData.length?ce.consoleErr("当前滚动的index超出已渲染列表长度，请先通过refreshToPage加载到对应index页并等待渲染成功后再调用此方法！"):this.$nextTick((()=>{if(this.finalUseVirtualList){const o=this.cellHeightMode===ue.CellHeightMode.Fixed;ce.delay((()=>{if(this.finalUseVirtualList){const s=o?this.virtualCellHeight*e:this.virtualHeightCacheList[e].lastTotalHeight;this.scrollToY(s,t,i)}}),o?0:100)}}))},scrollIntoViewByView(e,t,i){this._scrollIntoView(e,t,i)},updatePageScrollTop(e){this.pageScrollTop=e},updatePageScrollTopHeight(){this._updatePageScrollTopOrBottomHeight("top")},updatePageScrollBottomHeight(){this._updatePageScrollTopOrBottomHeight("bottom")},updateLeftAndRightWidth(){this.finalIsOldWebView&&this.$nextTick((()=>this._updateLeftAndRightWidth(this.scrollViewContainerStyle,"zp-page")))},updateScrollViewScrollTop(e,t=!0){this._updatePrivateScrollWithAnimation(t),this.scrollTop=this.oldScrollTop,this.$nextTick((()=>{this.scrollTop=e,this.oldScrollTop=this.scrollTop}))},_onScrollToUpper(){this._emitScrollEvent("scrolltoupper"),this.$emit("scrollTopChange",0),this.$nextTick((()=>{this.oldScrollTop=0}))},_onScrollToLower(e){(!e.detail||!e.detail.direction||"bottom"===e.detail.direction)&&this.toBottomLoadingMoreEnabled&&this._onLoadingMore(this.useChatRecordMode?"click":"toBottom")},_scrollToTop(e=!0,t=!0){this.usePageScroll?this.$nextTick((()=>{L({scrollTop:0,duration:e?100:0})})):(this._updatePrivateScrollWithAnimation(e),this.scrollTop=this.oldScrollTop,this.$nextTick((()=>{this.scrollTop=0,this.oldScrollTop=this.scrollTop})))},async _scrollToBottom(e=!0){if(this.usePageScroll)this.$nextTick((()=>{L({scrollTop:Number.MAX_VALUE,duration:e?100:0})}));else try{this._updatePrivateScrollWithAnimation(e);const t=await this._getNodeClientRect(".zp-paging-container"),i=await this._getNodeClientRect(".zp-scroll-view"),o=t?t[0].height:0,s=i?i[0].height:0;o>s&&(this.scrollTop=this.oldScrollTop,this.$nextTick((()=>{this.scrollTop=o-s+this.virtualPlaceholderTopHeight,this.oldScrollTop=this.scrollTop})))}catch(t){}},_scrollIntoView(e,t=0,i=!1,o){try{this.scrollTop=this.oldScrollTop,this.$nextTick((()=>{this._getNodeClientRect("#"+e.replace("#",""),this.$parent).then((e=>{if(e){let s=e[0].top;this._scrollIntoViewByNodeTop(s,t,i),o&&o()}}))}))}catch(s){}},_scrollIntoViewByNodeTop(e,t=0,i=!1){this.isChatRecordModeAndInversion?this._getNodeClientRect(".zp-scroll-view").then((o=>{o&&this._scrollToY(o[0].height-e,t,i,!0)})):this._scrollToY(e,t,i,!0)},_scrollToY(e,t=0,i=!1,o=!1){this._updatePrivateScrollWithAnimation(i),ce.delay((()=>{if(this.usePageScroll){o&&-1!==this.pageScrollTop&&(e+=this.pageScrollTop);L({scrollTop:e-t,duration:i?100:0})}else o&&(e+=this.oldScrollTop),this.scrollTop=e-t}),10)},_scrollToX(e,t=0,i=!1){this._updatePrivateScrollWithAnimation(i),ce.delay((()=>{this.usePageScroll?ce.consoleErr("使用页面滚动时不支持scrollToX"):this.scrollLeft=e-t}),10)},_scroll(e){this.$emit("scroll",e);const{scrollTop:t,scrollLeft:i}=e.detail;this.finalUseVirtualList&&this._updateVirtualScroll(t,this.oldScrollTop-t),this.oldScrollTop=t,this.oldScrollLeft=i;const o=e.detail.scrollHeight-this.oldScrollTop;!this.isIos&&this._checkScrolledToBottom(o)},_emitScrollEvent(e){const t="scrolltolower"===e?"scrolltoupper":"scrolltolower",i=this.useChatRecordMode&&!this.isChatRecordModeAndNotInversion?t:e;this.$emit(i)},_updatePrivateScrollWithAnimation(e){this.privateScrollWithAnimation=e?1:0,ce.delay((()=>this.$nextTick((()=>{this.privateScrollWithAnimation=-1}))),100,"updateScrollWithAnimationDelay")},_doCheckScrollViewShouldFullHeight(e){this.autoFullHeight&&this.usePageScroll&&this.isTotalChangeFromAddData?this.$nextTick((()=>{this._checkScrollViewShouldFullHeight(((t,i)=>{this._preCheckShowNoMoreInside(e,t,i)}))})):this._preCheckShowNoMoreInside(e)},async _checkScrollViewShouldFullHeight(e){try{const t=await this._getNodeClientRect(".zp-scroll-view"),i=await this._getNodeClientRect(".zp-paging-container-content");if(!t||!i)return;const o=i[0].height,s=t[0].top;this.isAddedData&&o+s<=this.windowHeight?(this._setAutoHeight(!0,t),e(t,i)):(this._setAutoHeight(!1),e(null,null))}catch(t){e(null,null)}},async _updateCachedSuperContentHeight(){const e=await this._getNodeClientRect(".z-paging-content");e&&(this.superContentHeight=e[0].height)},_scrollTopChange(e,t){this.$emit("scrollTopChange",e),this.$emit("update:scrollTop",e),this._checkShouldShowBackToTop(e);const i=e>5?6:0;t&&this.wxsPageScrollTop!==i?this.wxsPageScrollTop=i:t||this.wxsScrollTop===i||(this.wxsScrollTop=i,i>6&&(this.scrollEnable=!0))},_updatePageScrollTopOrBottomHeight(e){if(!this.usePageScroll)return;this._doCheckScrollViewShouldFullHeight(this.realTotalData);const t=`.zp-page-${e}`,i=`margin${e.slice(0,1).toUpperCase()+e.slice(1)}`;let o=this.safeAreaInsetBottom;this.$nextTick((()=>{ce.delay((()=>{this._getNodeClientRect(t).then((t=>{if(t){let s=t[0].height;"bottom"===e?o&&(s+=this.safeAreaBottom):this.cacheTopHeight=s,this.$set(this.scrollViewStyle,i,`${s}px`)}else o&&this.$set(this.scrollViewStyle,i,`${this.safeAreaBottom}px`)}))}),0)}))}}},be={props:{autoShowBackToTop:{type:Boolean,default:ce.gc("autoShowBackToTop",!1)},backToTopThreshold:{type:[Number,String],default:ce.gc("backToTopThreshold","400rpx")},backToTopImg:{type:String,default:ce.gc("backToTopImg","")},backToTopWithAnimate:{type:Boolean,default:ce.gc("backToTopWithAnimate",!0)},backToTopBottom:{type:[Number,String],default:ce.gc("backToTopBottom","160rpx")},backToTopStyle:{type:Object,default:ce.gc("backToTopStyle",{})},enableBackToTop:{type:Boolean,default:ce.gc("enableBackToTop",!0)}},data:()=>({backToTopClass:"zp-back-to-top zp-back-to-top-hide",lastBackToTopShowTime:0,showBackToTopClass:!1}),computed:{backToTopThresholdUnitConverted(){return ce.addUnit(this.backToTopThreshold,this.unit)},backToTopBottomUnitConverted(){return ce.addUnit(this.backToTopBottom,this.unit)},finalEnableBackToTop(){return!this.usePageScroll&&this.enableBackToTop},finalBackToTopThreshold(){return ce.convertToPx(this.backToTopThresholdUnitConverted)},finalBackToTopStyle(){const e=this.backToTopStyle;return e.bottom||(e.bottom=this.windowBottom+ce.convertToPx(this.backToTopBottomUnitConverted)+"px"),e.position||(e.position=this.usePageScroll?"fixed":"absolute"),e},finalBackToTopClass(){return`${this.backToTopClass} zp-back-to-top-${this.unit}`}},methods:{_backToTopClick(){let e=!1;this.$emit("backToTopClick",(t=>{(void 0===t||!0===t)&&this._handleToTop(),e=!0})),this.$nextTick((()=>{!e&&this._handleToTop()}))},_handleToTop(){!this.backToTopWithAnimate&&this._checkShouldShowBackToTop(0),this.useChatRecordMode?this.scrollToBottom(this.backToTopWithAnimate):this.scrollToTop(this.backToTopWithAnimate)},_checkShouldShowBackToTop(e){this.autoShowBackToTop?e>this.finalBackToTopThreshold?this.showBackToTopClass||(this.showBackToTopClass=!0,this.lastBackToTopShowTime=(new Date).getTime(),ce.delay((()=>{this.backToTopClass="zp-back-to-top zp-back-to-top-show"}),300)):this.showBackToTopClass&&(this.backToTopClass="zp-back-to-top zp-back-to-top-hide",ce.delay((()=>{this.showBackToTopClass=!1}),(new Date).getTime()-this.lastBackToTopShowTime<500?0:300)):this.showBackToTopClass=!1}}},Le={props:{useVirtualList:{type:Boolean,default:ce.gc("useVirtualList",!1)},useCompatibilityMode:{type:Boolean,default:ce.gc("useCompatibilityMode",!1)},extraData:{type:Object,default:ce.gc("extraData",{})},useInnerList:{type:Boolean,default:ce.gc("useInnerList",!1)},forceCloseInnerList:{type:Boolean,default:ce.gc("forceCloseInnerList",!1)},cellKeyName:{type:String,default:ce.gc("cellKeyName","")},innerListStyle:{type:Object,default:ce.gc("innerListStyle",{})},innerCellStyle:{type:Object,default:ce.gc("innerCellStyle",{})},preloadPage:{type:[Number,String],default:ce.gc("preloadPage",12),validator:e=>(e<=0&&ce.consoleErr("preload-page必须大于0！"),e>0)},cellHeightMode:{type:String,default:ce.gc("cellHeightMode",ue.CellHeightMode.Fixed)},fixedCellHeight:{type:[Number,String],default:ce.gc("fixedCellHeight",0)},virtualListCol:{type:[Number,String],default:ce.gc("virtualListCol",1)},virtualScrollFps:{type:[Number,String],default:ce.gc("virtualScrollFps",80)},virtualCellIdPrefix:{type:String,default:ce.gc("virtualCellIdPrefix","")},virtualInSwiperSlot:{type:Boolean,default:!1}},data:()=>({virtualListKey:ce.getInstanceId(),virtualPageHeight:0,virtualCellHeight:0,virtualScrollTimeStamp:0,virtualList:[],virtualPlaceholderTopHeight:0,virtualPlaceholderBottomHeight:0,virtualTopRangeIndex:0,virtualBottomRangeIndex:0,lastVirtualTopRangeIndex:0,lastVirtualBottomRangeIndex:0,virtualItemInsertedCount:0,virtualHeightCacheList:[],getCellHeightRetryCount:{fixed:0,dynamic:0},pagingOrgTop:-1,updateVirtualListFromDataChange:!1}),watch:{realTotalData(){this.updateVirtualListRender()},virtualList(e){this.$emit("update:virtualList",e),this.$emit("virtualListChange",e)},virtualPlaceholderTopHeight(e){this.$emit("virtualTopHeightChange",e)}},computed:{virtualCellIndexKey:()=>$,finalUseVirtualList(){return this.useVirtualList&&this.usePageScroll&&ce.consoleErr("使用页面滚动时，开启虚拟列表无效！"),this.useVirtualList&&!this.usePageScroll},finalUseInnerList(){return this.useInnerList||this.finalUseVirtualList&&!this.forceCloseInnerList},finalCellKeyName(){return this.cellKeyName},finalVirtualPageHeight(){return this.virtualPageHeight>0?this.virtualPageHeight:this.windowHeight},finalFixedCellHeight(){return ce.convertToPx(this.fixedCellHeight)},fianlVirtualCellIdPrefix(){return(this.virtualCellIdPrefix?this.virtualCellIdPrefix+"-":"")+"zp-id"},finalPlaceholderTopHeightStyle:()=>({}),virtualRangePageHeight(){return this.finalVirtualPageHeight*this.preloadPage},virtualScrollDisTimeStamp(){return 1e3/this.virtualScrollFps}},methods:{doInsertVirtualListItem(e,t){if(this.cellHeightMode!==ue.CellHeightMode.Dynamic)return;this.realTotalData.splice(t,0,e),this.realTotalData=[...this.realTotalData],this.virtualItemInsertedCount++,e&&"[object Object]"===Object.prototype.toString.call(e)||(e={item:e});const i=this.virtualCellIndexKey;e[i]=`custom-${this.virtualItemInsertedCount}`,e[ee]=`${this.virtualListKey}-${e[i]}`,this.$nextTick((async()=>{let o=0;for(;o<=10;){await ce.wait(J);const s=await this._getVirtualCellNodeByIndex(e[i]);if(!s){o++;continue}const a=s?s[0].height:0,r=this.virtualHeightCacheList[t-1],l=r?r.totalHeight:0;this.virtualHeightCacheList.splice(t,0,{height:a,lastTotalHeight:l,totalHeight:l+a});for(let e=t+1;e<this.virtualHeightCacheList.length;e++){const t=this.virtualHeightCacheList[e];t.lastTotalHeight+=a,t.totalHeight+=a}this._updateVirtualScroll(this.oldScrollTop);break}}))},didUpdateVirtualListCell(e){if(this.cellHeightMode!==ue.CellHeightMode.Dynamic)return;const t=this.virtualHeightCacheList[e];this.$nextTick((()=>{this._getVirtualCellNodeByIndex(e).then((i=>{const o=i?i[0].height:0,s=o-t.height;t.height=o,t.totalHeight=t.lastTotalHeight+o;for(let t=e+1;t<this.virtualHeightCacheList.length;t++){const e=this.virtualHeightCacheList[t];e.totalHeight+=s,e.lastTotalHeight+=s}}))}))},didDeleteVirtualListCell(e){if(this.cellHeightMode!==ue.CellHeightMode.Dynamic)return;const t=this.virtualHeightCacheList[e];for(let i=e+1;i<this.virtualHeightCacheList.length;i++){const e=this.virtualHeightCacheList[i];e.totalHeight-=t.height,e.lastTotalHeight-=t.height}this.virtualHeightCacheList.splice(e,1)},updateVirtualListRender(){this.finalUseVirtualList&&(this.updateVirtualListFromDataChange=!0,this.$nextTick((()=>{this.getCellHeightRetryCount.fixed=0,this.realTotalData.length?this.cellHeightMode===ue.CellHeightMode.Fixed&&this.isFirstPage&&this._updateFixedCellHeight():this._resetDynamicListState(!this.isUserPullDown),this._updateVirtualScroll(this.oldScrollTop)})))},_virtualListInit(){this.$nextTick((()=>{ce.delay((()=>{this._getNodeClientRect(".zp-scroll-view").then((e=>{e&&(this.pagingOrgTop=e[0].top,this.virtualPageHeight=e[0].height)}))}))}))},_updateFixedCellHeight(){this.finalFixedCellHeight?this.virtualCellHeight=this.finalFixedCellHeight:this.$nextTick((()=>{ce.delay((()=>{this._getVirtualCellNodeByIndex(0).then((e=>{if(e)this.virtualCellHeight=e[0].height,this._updateVirtualScroll(this.oldScrollTop);else{if(this.getCellHeightRetryCount.fixed>10)return;this.getCellHeightRetryCount.fixed++,this._updateFixedCellHeight()}}))}),J,"updateFixedCellHeightDelay")}))},_updateDynamicCellHeight(e,t="bottom"){const i="top"===t,o=this.virtualHeightCacheList,s=i?[]:o;let a=0;this.$nextTick((()=>{ce.delay((async()=>{for(let r=0;r<e.length;r++){const l=await this._getVirtualCellNodeByIndex(e[r][this.virtualCellIndexKey]),h=l?l[0].height:0;if(!l)return void(this.getCellHeightRetryCount.dynamic<=10&&(o.splice(o.length-r,r),this.getCellHeightRetryCount.dynamic++,this._updateDynamicCellHeight(e,t)));const n=s.length?s.slice(-1)[0]:null,d=n?n.totalHeight:0;s.push({height:h,lastTotalHeight:d,totalHeight:d+h}),i&&(a+=h)}if(i&&e.length){for(let e=0;e<o.length;e++){const t=o[e];t.lastTotalHeight+=a,t.totalHeight+=a}this.virtualHeightCacheList=s.concat(o)}this._updateVirtualScroll(this.oldScrollTop)}),J,"updateDynamicCellHeightDelay")}))},_setCellIndex(e,t="bottom"){let i=0;const o=this.virtualCellIndexKey;if("bottom"===t&&[ue.QueryFrom.Refresh,ue.QueryFrom.Reload].indexOf(this.queryFrom)>=0&&this._resetDynamicListState(),this.totalData.length&&this.queryFrom!==ue.QueryFrom.Refresh){if("bottom"===t){i=this.realTotalData.length;const e=this.realTotalData.length?this.realTotalData.slice(-1)[0]:null;e&&void 0!==e[o]&&(i=e[o]+1)}else if("top"===t){const t=this.realTotalData.length?this.realTotalData[0]:null;t&&void 0!==t[o]&&(i=t[o]-e.length)}}else this._resetDynamicListState();for(let s=0;s<e.length;s++){let t=e[s];t&&"[object Object]"===Object.prototype.toString.call(t)||(t={item:t}),t[ee]&&(t=ce.deepCopy(t)),t[o]=i+s,t[ee]=`${this.virtualListKey}-${t[o]}`,e[s]=t}this.getCellHeightRetryCount.dynamic=0,this.cellHeightMode===ue.CellHeightMode.Dynamic&&this._updateDynamicCellHeight(e,t)},_updateVirtualScroll(e,t=0){const i=ce.getTime();if(0===e&&this._resetTopRange(),0!==e&&this.virtualScrollTimeStamp&&i-this.virtualScrollTimeStamp<=this.virtualScrollDisTimeStamp)return;this.virtualScrollTimeStamp=i;let o=0;const s=this.cellHeightMode;if(s===ue.CellHeightMode.Fixed)o=parseInt(e/this.virtualCellHeight)||0,this._updateFixedTopRangeIndex(o),this._updateFixedBottomRangeIndex(o);else if(s===ue.CellHeightMode.Dynamic){const i=t>0?"top":"bottom",o=this.virtualRangePageHeight,s=e-o,a=e+this.finalVirtualPageHeight+o;let r=0,l=0,h=!1;const n=this.virtualHeightCacheList,d=n?n.slice(-1)[0]:null;let c=this.virtualTopRangeIndex;if("bottom"===i)for(let e=c;e<n.length;e++){const t=n[e];if(t&&t.totalHeight>s){this.virtualTopRangeIndex=e,this.virtualPlaceholderTopHeight=t.lastTotalHeight;break}}else{let e=!1;for(let t=c;t>=0;t--){const i=n[t];if(i&&i.totalHeight<s){this.virtualTopRangeIndex=t,this.virtualPlaceholderTopHeight=i.lastTotalHeight,e=!0;break}}!e&&this._resetTopRange()}for(let e=this.virtualTopRangeIndex;e<n.length;e++){const t=n[e];if(t&&t.totalHeight>a){r=e,l=d.totalHeight-t.totalHeight,h=!0;break}}h&&0!==this.virtualBottomRangeIndex?(this.virtualBottomRangeIndex=r,this.virtualPlaceholderBottomHeight=l):(this.virtualBottomRangeIndex=this.realTotalData.length?this.realTotalData.length-1:this.pageSize,this.virtualPlaceholderBottomHeight=0),this._updateVirtualList()}},_updateFixedTopRangeIndex(e){let t=0===this.virtualCellHeight?0:e-(parseInt(this.finalVirtualPageHeight/this.virtualCellHeight)||1)*this.preloadPage;t*=this.virtualListCol,t=Math.max(0,t),this.virtualTopRangeIndex=t,this.virtualPlaceholderTopHeight=t/this.virtualListCol*this.virtualCellHeight},_updateFixedBottomRangeIndex(e){let t=0===this.virtualCellHeight?this.pageSize:e+(parseInt(this.finalVirtualPageHeight/this.virtualCellHeight)||1)*(this.preloadPage+1);t*=this.virtualListCol,t=Math.min(this.realTotalData.length,t),this.virtualBottomRangeIndex=t,this.virtualPlaceholderBottomHeight=(this.realTotalData.length-t)*this.virtualCellHeight/this.virtualListCol,this._updateVirtualList()},_updateVirtualList(){(this.updateVirtualListFromDataChange||this.lastVirtualTopRangeIndex!==this.virtualTopRangeIndex||this.lastVirtualBottomRangeIndex!==this.virtualBottomRangeIndex)&&(this.updateVirtualListFromDataChange=!1,this.lastVirtualTopRangeIndex=this.virtualTopRangeIndex,this.lastVirtualBottomRangeIndex=this.virtualBottomRangeIndex,this.virtualList=this.realTotalData.slice(this.virtualTopRangeIndex,this.virtualBottomRangeIndex+1))},_resetDynamicListState(e=!1){this.virtualHeightCacheList=[],e&&(this.virtualList=[]),this.virtualTopRangeIndex=0,this.virtualPlaceholderTopHeight=0},_resetTopRange(){this.virtualTopRangeIndex=0,this.virtualPlaceholderTopHeight=0,this._updateVirtualList()},_checkVirtualListScroll(){this.finalUseVirtualList&&this.$nextTick((()=>{this._getNodeClientRect(".zp-paging-touch-view").then((e=>{const t=e?e[0].top:0;(!e||t===this.pagingOrgTop&&0!==this.virtualPlaceholderTopHeight)&&this._updateVirtualScroll(0)}))}))},_getVirtualCellNodeByIndex(e){let t=this.finalUseInnerList;return this._getNodeClientRect(`#${this.fianlVirtualCellIdPrefix}-${e}`,t)},_innerCellClick(e,t){this.$emit("innerCellClick",e,t)}}},ve=ce.getSystemInfoSync(),De={name:"z-paging",components:{zPagingRefresh:ge,zPagingLoadMore:pe,zPagingEmptyView:_},mixins:[fe,me,Se,Re,we,Me,Ce,xe,Ie,Ae,be,Le],data:()=>({base64BackToTop:Y,loadingType:ue.LoadingType.Refresher,requestTimeStamp:0,wxsPropType:"",renderPropScrollTop:-1,checkScrolledToBottomTimeOut:null,cacheTopHeight:-1,statusBarHeight:ve.statusBarHeight,insideOfPaging:-1,isLoadFailed:!1,isIos:"ios"===ve.platform,disabledBounce:!1,fromCompleteEmit:!1,disabledCompleteEmit:!1,pageLaunched:!1,active:!1,wxsIsScrollTopInTopRange:!0,wxsScrollTop:0,wxsPageScrollTop:0,wxsOnPullingDown:!1}),props:{delay:{type:[Number,String],default:ce.gc("delay",0)},minDelay:{type:[Number,String],default:ce.gc("minDelay",0)},pagingStyle:{type:Object,default:ce.gc("pagingStyle",{})},height:{type:String,default:ce.gc("height","")},width:{type:String,default:ce.gc("width","")},maxWidth:{type:String,default:ce.gc("maxWidth","")},bgColor:{type:String,default:ce.gc("bgColor","")},pagingContentStyle:{type:Object,default:ce.gc("pagingContentStyle",{})},autoHeight:{type:Boolean,default:ce.gc("autoHeight",!1)},autoHeightAddition:{type:[Number,String],default:ce.gc("autoHeightAddition","0px")},defaultThemeStyle:{type:String,default:ce.gc("defaultThemeStyle","black")},fixed:{type:Boolean,default:ce.gc("fixed",!0)},safeAreaInsetBottom:{type:Boolean,default:ce.gc("safeAreaInsetBottom",!1)},useSafeAreaPlaceholder:{type:Boolean,default:ce.gc("useSafeAreaPlaceholder",!1)},bottomBgColor:{type:String,default:ce.gc("bottomBgColor","")},topZIndex:{type:Number,default:ce.gc("topZIndex",99)},superContentZIndex:{type:Number,default:ce.gc("superContentZIndex",1)},contentZIndex:{type:Number,default:ce.gc("contentZIndex",1)},f2ZIndex:{type:Number,default:ce.gc("f2ZIndex",100)},autoFullHeight:{type:Boolean,default:ce.gc("autoFullHeight",!0)},watchTouchDirectionChange:{type:Boolean,default:ce.gc("watchTouchDirectionChange",!1)},unit:{type:String,default:ce.gc("unit","rpx")}},created(){this.createdReload&&!this.refresherOnly&&this.auto&&(this._startLoading(),this.$nextTick(this._preReload))},mounted(){this.active=!0,this.wxsPropType=ce.getTime().toString(),this.renderJsIgnore,this.createdReload||this.refresherOnly||!this.auto||ce.delay((()=>this.$nextTick(this._preReload)),0),this.finalUseCache&&this._setListByLocalCache();let e=0;e=J,this.$nextTick((()=>{this.systemInfo=ce.getSystemInfoSync(),!this.usePageScroll&&this.autoHeight&&this._setAutoHeight(),this.loaded=!0,ce.delay((()=>{this.updateFixedLayout(),this._updateCachedSuperContentHeight()}))})),this.updatePageScrollTopHeight(),this.updatePageScrollBottomHeight(),this.updateLeftAndRightWidth(),this.finalRefresherEnabled&&this.useCustomRefresher&&this.$nextTick((()=>{this.isTouchmoving=!0})),this._onEmit(),this.finalUseVirtualList&&this._virtualListInit(),this.$nextTick((()=>{setTimeout((()=>{this._getCssSafeAreaInsetBottom((()=>this.safeAreaInsetBottom&&this.updatePageScrollBottomHeight()))}),e)}))},destroyed(){this._handleUnmounted()},unmounted(){this._handleUnmounted()},watch:{defaultThemeStyle:{handler(e){e.length&&(this.finalRefresherDefaultStyle=e)},immediate:!0},autoHeight(e){this.loaded&&!this.usePageScroll&&this._setAutoHeight(e)},autoHeightAddition(e){this.loaded&&!this.usePageScroll&&this.autoHeight&&this._setAutoHeight(e)}},computed:{finalPagingStyle(){const e={...this.pagingStyle};if(!this.systemInfo)return e;const{windowTop:t,windowBottom:i}=this;return!this.usePageScroll&&this.fixed&&(t&&!e.top&&(e.top=t+"px"),i&&!e.bottom&&(e.bottom=i+"px")),this.bgColor.length&&!e.background&&(e.background=this.bgColor),this.height.length&&!e.height&&(e.height=this.height),this.width.length&&!e.width&&(e.width=this.width),this.maxWidth.length&&!e["max-width"]&&(e["max-width"]=this.maxWidth,e.margin="0 auto"),e},finalPagingContentStyle(){return 1!=this.contentZIndex&&(this.pagingContentStyle["z-index"]=this.contentZIndex,this.pagingContentStyle.position="relative"),this.pagingContentStyle},renderJsIgnore(){return(this.usePageScroll&&this.useChatRecordMode||!this.refresherEnabled&&this.scrollable||!this.useCustomRefresher)&&this.$nextTick((()=>{this.renderPropScrollTop=10})),0},windowHeight(){return this.systemInfo&&this.systemInfo.windowHeight||0},windowBottom(){if(!this.systemInfo)return 0;let e=this.systemInfo.windowBottom||0;return!this.safeAreaInsetBottom||this.useSafeAreaPlaceholder||this.useChatRecordMode||(e+=this.safeAreaBottom),e},isIosAndH5(){return this.isIos}},methods:{getVersion:()=>`z-paging v${Q}`,setSpecialEffects(e){this.setListSpecialEffects(e)},setListSpecialEffects(e){this.nFixFreezing=e&&Object.keys(e).length,this.isIos&&(this.privateRefresherEnabled=0),!this.usePageScroll&&this.$refs["zp-n-list"].setSpecialEffects(e)},_doVibrateShort(){},async _setAutoHeight(e=!0,t=null){const i="min-height";try{if(e){let e=t||await this._getNodeClientRect(".zp-scroll-view"),o=await this._getNodeClientRect(".zp-page-bottom");if(e){const t=e[0].top;let s=this.windowHeight-t;s-=o?o[0].height:0;const a=ce.convertToPx(this.autoHeightAddition);let r=" !important";const l=s+a-(this.insideMore?1:0)+"px"+r;this.$set(this.scrollViewStyle,i,l),this.$set(this.scrollViewInStyle,i,l)}}else this.$delete(this.scrollViewStyle,i),this.$delete(this.scrollViewInStyle,i)}catch(o){}},_handleUnmounted(){this.active=!1,this._offEmit()},_updateInsideOfPaging(){this.insideMore&&!0===this.insideOfPaging&&setTimeout(this.doLoadMore,200)},_cleanTimeout:e=>(e&&(clearTimeout(e),e=null),e),_onEmit(){v(K,(e=>{this.loading&&(e&&(this.customerEmptyViewErrorText=e),this.complete(!1).catch((()=>{})))})),v(X,(e=>{setTimeout((()=>{if(this.loading)if(this.disabledCompleteEmit)this.disabledCompleteEmit=!1;else{const t=e.type||"normal",i=e.list||e,o=e.rule;switch(this.fromCompleteEmit=!0,t){case"normal":this.complete(i);break;case"total":this.completeByTotal(i,o);break;case"nomore":this.completeByNoMore(i,o);break;case"key":this.completeByKey(i,o)}}}),1)}))},_offEmit(){D(K),D(X)}}},Ve={startY:0,isTouchFromZPaging:!1,isUsePageScroll:!1,isReachedTop:!0,isIosAndH5:!1,useChatRecordMode:!1,appLaunched:!1},ze={name:"z-paging",mixins:[{mounted(){window&&this._handleTouch()},methods:{renderPropIsIosAndH5Change(e){-1!==e&&(Ve.isIosAndH5=e)},_handleTouch(){window.$zPagingRenderJsInited||(window.$zPagingRenderJsInited=!0,window.addEventListener("touchstart",this._handleTouchstart,{passive:!0}),window.addEventListener("touchmove",this._handleTouchmove,{passive:!1}))},_handleTouchstart(e){const t=ce.getTouch(e);Ve.startY=t.touchY;const i=ce.getTouchFromZPaging(e.target);Ve.isTouchFromZPaging=i.isFromZp,Ve.isUsePageScroll=i.isPageScroll,Ve.isReachedTop=i.isReachedTop,Ve.useChatRecordMode=i.isUseChatRecordMode},_handleTouchmove(e){const t=ce.getTouch(e).touchY-Ve.startY;Ve.isTouchFromZPaging&&(Ve.isReachedTop&&(Ve.useChatRecordMode?t<0:t>0)||!Ve.useChatRecordMode&&Ve.isIosAndH5&&!Ve.isUsePageScroll&&t<0)&&e.cancelable&&!e.defaultPrevented&&e.preventDefault()},_removeAllEventListener(){window.removeEventListener("touchstart"),window.removeEventListener("touchmove")}}}]},Ne=e=>{e.$renderjs||(e.$renderjs=[]),e.$renderjs.push("pagingRenderjs"),e.mixins||(e.mixins=[]),e.mixins.push({beforeCreate(){this.pagingRenderjs=this},mounted(){this.$ownerInstance=this.$gcd(this,!0)}}),e.mixins.push(ze)};var Pe=0,ke=-1,Be=-1;function Ee(e,t){var i=Ye(t),o={},s={};if(t.callMethod("_handleListTouchstart"),!i||(o=i.getState(),s=i.getDataset(),!_e(e,i))){var a=o.isTouchEnded;o.oldMoveDis=0;var r=Ge(e),l=Je(s.loading);o.startY=r.touchY,Be=o.startY,o.lastTouch=r,!l&&a&&(o.isTouchmoving=!1),o.isTouchEnded=!1,t.callMethod("_handleRefresherTouchstart",r)}}function Fe(e,t){var i=Ge(e),o=Ye(t),s=o.getDataset(),a=s.refresherthreshold,r=s.refresherf2threshold,l=Je(s.refresherf2enabled);Je(s.isios);var h=o.getState(),n={},d=0,c=!1;if(Je(s.watchtouchdirectionchange)){d=(n=Ue(e,o)).currentDis;var u=(c=n.isDown)?"top":"bottom";c==h.oldTouchDirection&&c!=h.oldEmitedTouchDirection&&(t.callMethod("_handleTouchDirectionChange",{direction:u}),h.oldEmitedTouchDirection=c),h.oldTouchDirection=c}if(_e(e,o))return Qe(h,t,!1),!0;if(!function(e,t,i,o){var s=o.refreshermaxangle,a=Je(o.refresheraecc),r=i.lastTouch,l=i.reachMaxAngle,h=i.oldMoveDis;if(!r)return!0;if(s>=0&&s<=90&&r){if((!h||h<1)&&!a&&null!=l&&!l)return!1;var n=Math.abs(t.touchX-r.touchX),d=Math.abs(t.touchY-r.touchY),c=Math.sqrt(Math.pow(n,2)+Math.pow(d,2));if((n||d)&&n>1)if(Math.asin(d/c)/Math.PI*180<s){var u=i.hitReachMaxAngleCount||0;return i.hitReachMaxAngleCount=++u,i.hitReachMaxAngleCount>2&&(i.lastTouch=t,i.reachMaxAngle=!1),!1}}return i.lastTouch=t,!0}(0,i,h,s))return Qe(h,t,!1),!0;if(d=(n=Ue(e,o)).currentDis,c=n.isDown,d<0)return je(0,o,h,!1),Qe(h,t,!1),!0;if(c&&!h.disabledBounce)return t.callMethod("_handleScrollViewBounce",{bounce:!1}),h.disabledBounce=!0,Qe(h,t,c),!c;je(d,o,h,!1);var g=h.refresherStatus,p=Je(s.oldistouchmoving),f=Je(s.hastouchmove),m=h.isTouchmoving;return h.refresherStatus=d>=a?l&&d>r?"goF2":"releaseToRefresh":"default",m||(h.isTouchmoving=!0,m=!0),h.isTouchEnded&&(h.isTouchEnded=!1),f&&t.callMethod("_handleWxsPullingDown",{moveDis:d,diffDis:n.diffDis}),null!=g&&g==h.refresherStatus&&p==m||t.callMethod("_handleRefresherTouchmove",d,i),Qe(h,t,c),!c}function He(e,t){Ge(e);var i=Ye(t);i.getDataset();var o=i.getState();if(o.disabledBounce&&(t.callMethod("_handleScrollViewBounce",{bounce:!0}),o.disabledBounce=!1),!_e(e,i)&&(o.reachMaxAngle=!0,o.hitReachMaxAngleCount=0,o.fixedIsTopHitCount=0,o.isTouchmoving)){var s=o.refresherStatus,a=o.moveDis,r=i.getDataset().refresherthreshold,l=Ue(e,i).currentDis;if(l>=r&&"releaseToRefresh"===s||(o.isTouchmoving=!1),t.callMethod("_handleRefresherTouchend",l),o.isTouchEnded=!0,!(a<r)){var h=!1;l>=r&&(l=r,h=!0),je(l,i,o,h)}}}function We(){if(!navigator)return!1;if(-1!=ke)return ke;return ke=["Android","iPhone","SymbianOS","Windows Phone","iPad","iPod"].every((function(e){return navigator.userAgent.indexOf(e)<0}))}var Ze=!1;function je(e,t,i,o){e=e||0,i.moveDis!=e&&(i.moveDis=e,Oe("translateY("+e+"px)",t,o,""))}function Oe(e,t,i,o){Je(t.getDataset().refreshernotransform)||(e="translateY(0px)"==e?"none":e,t.requestAnimationFrame((function(){var s={transform:e};i&&(s.transition="transform .1s linear"),o.length&&(s.transition=o),t.setStyle(s)})))}function Ue(e,t){var i=t.getState(),o=parseFloat(t.getDataset().refresherthreshold),s=parseFloat(t.getDataset().refresheroutrate),a=parseFloat(t.getDataset().refresherpullrate),r=Ge(e),l=i.startY&&"NaN"!=i.startY?i.startY:Be,h=r.touchY-l,n=i.oldMoveDis||0;i.oldMoveDis=h;var d=h-n;return d>0&&(d*=a,Pe>o&&(d*=1-s)),Pe+=d=d>100?d/100:d>20?d/2.2:d,{currentDis:Pe=Math.max(0,Pe),diffDis:d,isDown:d>0}}function Ge(e){var t=e;return e.touches&&e.touches.length?t=e.touches[0]:e.changedTouches&&e.changedTouches.length?t=e.changedTouches[0]:e.datail&&e.datail!={}&&(t=e.datail),{touchX:t.clientX,touchY:t.clientY}}function Ye(e){var t=e.getState().currentIns;return t||e.callMethod("_handlePropUpdate"),t}function _e(e,t,i){var o=t.getDataset(),s=t.getState(),a=Je(o.loading),r=Je(o.usechatrecordmode),l=Je(o.refresherenabled),h=Je(o.usecustomrefresher),n=Je(o.usepagescroll),d=parseFloat(o.pagescrolltop),c=parseFloat(o.scrolltop);return s.fixedIsTopHitCount,s.fixedIsTopHitCount=0,a||r||!l||!h||n&&h&&d>5&&!0||!n&&h&&c>5&&!0}function Qe(e,t,i){(e.onPullingDown||!1)!=i&&t.callMethod("_handleWxsPullingDownStatusChange",i),e.onPullingDown=i}function Je(e){return 1==(e=("string"==typeof e?JSON.parse(e):e)||!1)||"true"==e}const Ke={touchstart:Ee,touchmove:Fe,touchend:He,mousedown:function(e,t){We()&&(Ee(e,t),Ze=!0)},mousemove:function(e,t){We()&&Ze&&Fe(e,t)},mouseup:function(e,t){We()&&(He(e,t),Ze=!1)},mouseleave:function(e,t){We()&&(Ze=!1)},propObserver:function(e,t,i,o){var s=i.getState()||{};if(s.currentIns=o,o.getDataset().loading,e&&-1!=e.indexOf("end"))Oe("translateY(0px)",o,!1,e.split("end")[0]),s.moveDis=0,s.oldMoveDis=0,Pe=0;else if(e&&-1!=e.indexOf("begin")){je(o.getDataset().refresherthreshold,o,s,!1)}}},Xe=e=>{e.$wxs||(e.$wxs=[]),e.$wxs.push("pagingWxs"),e.mixins||(e.mixins=[]),e.mixins.push({beforeCreate(){this.pagingWxs=Ke}})};Ne(De),Xe(De);const qe=B(De,[["render",function(l,c,g,p,f,m){const T=u,R=V("z-paging-refresh"),w=V("z-paging-load-more"),M=E(z("z-paging-empty-view"),_),C=k,x=d;return e(),t(T,{class:s({"z-paging-content":!0,"z-paging-content-full":!l.usePageScroll,"z-paging-content-fixed":!l.usePageScroll&&l.fixed,"z-paging-content-page":l.usePageScroll,"z-paging-reached-top":l.renderPropScrollTop<1,"z-paging-use-chat-record-mode":l.useChatRecordMode}),style:a([l.finalPagingStyle])},{default:i((()=>[-1===l.cssSafeAreaInsetBottom?(e(),t(T,{key:0,class:"zp-safe-area-inset-bottom"})):n("",!0),l.showF2&&l.showRefresherF2?(e(),t(T,{key:1,onTouchmove:c[0]||(c[0]=h((()=>{}),["stop","prevent"])),class:"zp-f2-content",style:a([{transform:l.f2Transform,transition:"transform .2s linear",height:l.superContentHeight+"px","z-index":l.f2ZIndex}])},{default:i((()=>[N(l.$slots,"f2",{},void 0,!0)])),_:3},8,["style"])):n("",!0),!l.usePageScroll&&l.zSlots.top?N(l.$slots,"top",{key:2},void 0,!0):l.usePageScroll&&l.zSlots.top?(e(),t(T,{key:3,class:"zp-page-top",onTouchmove:c[1]||(c[1]=h((()=>{}),["stop","prevent"])),style:a([{top:`${l.windowTop}px`,"z-index":l.topZIndex}])},{default:i((()=>[N(l.$slots,"top",{},void 0,!0)])),_:3},8,["style"])):n("",!0),o(T,{class:s({"zp-view-super":!0,"zp-scroll-view-super":!l.usePageScroll}),style:a([l.finalScrollViewStyle])},{default:i((()=>[l.zSlots.left?(e(),t(T,{key:0,class:s({"zp-page-left":!0,"zp-absoulte":l.finalIsOldWebView})},{default:i((()=>[N(l.$slots,"left",{},void 0,!0)])),_:3},8,["class"])):n("",!0),o(T,{class:s({"zp-scroll-view-container":!0,"zp-absoulte":l.finalIsOldWebView}),style:a([l.scrollViewContainerStyle])},{default:i((()=>[o(C,{ref:"zp-scroll-view",class:s({"zp-scroll-view":!0,"zp-scroll-view-absolute":!l.usePageScroll,"zp-scroll-view-hide-scrollbar":!l.showScrollbar}),style:a([l.chatRecordRotateStyle]),"scroll-top":l.scrollTop,"scroll-left":l.scrollLeft,"scroll-x":l.scrollX,"scroll-y":l.finalScrollable,"enable-back-to-top":l.finalEnableBackToTop,"show-scrollbar":l.showScrollbar,"scroll-with-animation":l.finalScrollWithAnimation,"scroll-into-view":l.scrollIntoView,"lower-threshold":l.finalLowerThreshold,"upper-threshold":5,"refresher-enabled":l.finalRefresherEnabled&&!l.useCustomRefresher,"refresher-threshold":l.finalRefresherThreshold,"refresher-default-style":l.finalRefresherDefaultStyle,"refresher-background":l.refresherBackground,"refresher-triggered":l.finalRefresherTriggered,onScroll:l._scroll,onScrolltolower:l._onScrollToLower,onScrolltoupper:l._onScrollToUpper,onRefresherrestore:l._onRestore,onRefresherrefresh:c[4]||(c[4]=e=>l._onRefresh(!0))},{default:i((()=>[o(T,{class:"zp-paging-touch-view",onTouchstart:l.pagingWxs.touchstart,onTouchmove:l.pagingWxs.touchmove,onTouchend:l.pagingWxs.touchend,onTouchcancel:l.pagingWxs.touchend,onMousedown:l.pagingWxs.mousedown,onMousemove:l.pagingWxs.mousemove,onMouseup:l.pagingWxs.mouseup,onMouseleave:l.pagingWxs.mouseleave},{default:i((()=>[l.finalRefresherFixedBacHeight>0?(e(),t(T,{key:0,class:"zp-fixed-bac-view",style:a([{background:l.refresherFixedBackground,height:`${l.finalRefresherFixedBacHeight}px`}])},null,8,["style"])):n("",!0),o(T,{class:"zp-paging-main",style:a([l.scrollViewInStyle,{transform:l.finalRefresherTransform,transition:l.refresherTransition}]),"change:prop":l.pagingWxs.propObserver,prop:l.wxsPropType,"data-refresherThreshold":l.finalRefresherThreshold,"data-refresherF2Enabled":l.refresherF2Enabled,"data-refresherF2Threshold":l.finalRefresherF2Threshold,"data-isIos":l.isIos,"data-loading":l.loading||l.isRefresherInComplete,"data-useChatRecordMode":l.useChatRecordMode,"data-refresherEnabled":l.refresherEnabled,"data-useCustomRefresher":l.useCustomRefresher,"data-pageScrollTop":l.wxsPageScrollTop,"data-scrollTop":l.wxsScrollTop,"data-refresherMaxAngle":l.refresherMaxAngle,"data-refresherNoTransform":l.refresherNoTransform,"data-refresherAecc":l.refresherAngleEnableChangeContinued,"data-usePageScroll":l.usePageScroll,"data-watchTouchDirectionChange":l.watchTouchDirectionChange,"data-oldIsTouchmoving":l.isTouchmoving,"data-refresherOutRate":l.finalRefresherOutRate,"data-refresherPullRate":l.finalRefresherPullRate,"data-hasTouchmove":l.hasTouchmove,"change:renderPropIsIosAndH5":l.pagingRenderjs.renderPropIsIosAndH5Change,renderPropIsIosAndH5:l.isIosAndH5},{default:i((()=>[l.showRefresher?(e(),t(T,{key:0,class:"zp-custom-refresher-view",style:a([{"margin-top":`-${l.finalRefresherThreshold+l.refresherThresholdUpdateTag}px`,background:l.refresherBackground,opacity:l.isTouchmoving?1:0}])},{default:i((()=>[o(T,{class:"zp-custom-refresher-container",style:a([{height:`${l.finalRefresherThreshold}px`,background:l.refresherBackground}])},{default:i((()=>[l.useRefresherStatusBarPlaceholder?(e(),t(T,{key:0,class:"zp-custom-refresher-status-bar-placeholder",style:a([{height:`${l.statusBarHeight}px`}])},null,8,["style"])):n("",!0),o(T,{class:"zp-custom-refresher-slot-view"},{default:i((()=>[l.zSlots.refresherComplete&&l.refresherStatus===l.R.Complete||l.zSlots.refresherF2&&l.refresherStatus===l.R.GoF2?n("",!0):N(l.$slots,"refresher",{key:0,refresherStatus:l.refresherStatus},void 0,!0)])),_:3}),l.zSlots.refresherComplete&&l.refresherStatus===l.R.Complete?N(l.$slots,"refresherComplete",{key:1},void 0,!0):l.zSlots.refresherF2&&l.refresherStatus===l.R.GoF2?N(l.$slots,"refresherF2",{key:2},void 0,!0):l.showCustomRefresher?n("",!0):(e(),t(R,{key:3,ref:"refresh",class:"zp-custom-refresher-refresh",style:a([{height:l.finalRefresherThreshold-l.finalRefresherThresholdPlaceholder+"px"}]),status:l.refresherStatus,defaultThemeStyle:l.finalRefresherThemeStyle,defaultText:l.finalRefresherDefaultText,isIos:l.isIos,pullingText:l.finalRefresherPullingText,refreshingText:l.finalRefresherRefreshingText,completeText:l.finalRefresherCompleteText,goF2Text:l.finalRefresherGoF2Text,defaultImg:l.refresherDefaultImg,pullingImg:l.refresherPullingImg,refreshingImg:l.refresherRefreshingImg,completeImg:l.refresherCompleteImg,refreshingAnimated:l.refresherRefreshingAnimated,showUpdateTime:l.showRefresherUpdateTime,updateTimeKey:l.refresherUpdateTimeKey,updateTimeTextMap:l.finalRefresherUpdateTimeTextMap,imgStyle:l.refresherImgStyle,titleStyle:l.refresherTitleStyle,updateTimeStyle:l.refresherUpdateTimeStyle,unit:l.unit},null,8,["style","status","defaultThemeStyle","defaultText","isIos","pullingText","refreshingText","completeText","goF2Text","defaultImg","pullingImg","refreshingImg","completeImg","refreshingAnimated","showUpdateTime","updateTimeKey","updateTimeTextMap","imgStyle","titleStyle","updateTimeStyle","unit"]))])),_:3},8,["style"])])),_:3},8,["style"])):n("",!0),o(T,{class:"zp-paging-container",style:a([{justifyContent:l.useChatRecordMode?"flex-end":"flex-start"}])},{default:i((()=>[l.showLoading&&l.zSlots.loading&&!l.loadingFullFixed?N(l.$slots,"loading",{key:0},void 0,!0):n("",!0),o(T,{class:"zp-paging-container-content",style:a([l.finalPlaceholderTopHeightStyle,l.finalPagingContentStyle])},{default:i((()=>[l.useVirtualList?(e(),t(T,{key:0,class:"zp-virtual-placeholder",style:a([{height:l.virtualPlaceholderTopHeight+"px"}])},null,8,["style"])):n("",!0),N(l.$slots,"default",{},void 0,!0),l.finalUseInnerList?(e(),y(S,{key:1},[N(l.$slots,"header",{},void 0,!0),o(T,{class:"zp-list-container",style:a([l.innerListStyle])},{default:i((()=>[l.finalUseVirtualList?(e(!0),y(S,{key:0},P(l.virtualList,((o,s)=>(e(),t(T,{class:"zp-list-cell",style:a([l.innerCellStyle]),id:`${l.fianlVirtualCellIdPrefix}-${o[l.virtualCellIndexKey]}`,key:o.zp_unique_index,onClick:e=>l._innerCellClick(o,l.virtualTopRangeIndex+s)},{default:i((()=>[l.useCompatibilityMode?(e(),t(T,{key:0},{default:i((()=>[r("使用兼容模式请在组件源码z-paging.vue第103行中注释这一行，并打开下面一行注释")])),_:1})):N(l.$slots,"cell",{key:1,item:o,index:l.virtualTopRangeIndex+s},void 0,!0)])),_:2},1032,["style","id","onClick"])))),128)):(e(!0),y(S,{key:1},P(l.realTotalData,((o,s)=>(e(),t(T,{class:"zp-list-cell",key:s,onClick:e=>l._innerCellClick(o,s)},{default:i((()=>[N(l.$slots,"cell",{item:o,index:s},void 0,!0)])),_:2},1032,["onClick"])))),128))])),_:3},8,["style"]),N(l.$slots,"footer",{},void 0,!0)],64)):n("",!0),l.useChatRecordMode&&l.realTotalData.length>=l.defaultPageSize&&(l.loadingStatus!==l.M.NoMore||l.zSlots.chatNoMore)&&(l.realTotalData.length||l.showChatLoadingWhenReload&&l.showLoading)&&!l.isFirstPageAndNoMore?(e(),t(T,{key:2,style:a([l.chatRecordRotateStyle])},{default:i((()=>[l.loadingStatus===l.M.NoMore&&l.zSlots.chatNoMore?N(l.$slots,"chatNoMore",{key:0},void 0,!0):(e(),y(S,{key:1},[l.zSlots.chatLoading?N(l.$slots,"chatLoading",{key:0,loadingMoreStatus:l.loadingStatus},void 0,!0):(e(),t(w,{key:1,onDoClick:c[2]||(c[2]=e=>l._onLoadingMore("click")),zConfig:l.zLoadMoreConfig},null,8,["zConfig"]))],64))])),_:3},8,["style"])):n("",!0),l.useVirtualList?(e(),t(T,{key:3,class:"zp-virtual-placeholder",style:a([{height:l.virtualPlaceholderBottomHeight+"px"}])},null,8,["style"])):n("",!0),l.showLoadingMoreDefault?N(l.$slots,"loadingMoreDefault",{key:4},void 0,!0):l.showLoadingMoreLoading?N(l.$slots,"loadingMoreLoading",{key:5},void 0,!0):l.showLoadingMoreNoMore?N(l.$slots,"loadingMoreNoMore",{key:6},void 0,!0):l.showLoadingMoreFail?N(l.$slots,"loadingMoreFail",{key:7},void 0,!0):l.showLoadingMoreCustom?(e(),t(w,{key:8,onDoClick:c[3]||(c[3]=e=>l._onLoadingMore("click")),zConfig:l.zLoadMoreConfig},null,8,["zConfig"])):n("",!0),l.safeAreaInsetBottom&&l.useSafeAreaPlaceholder&&!l.useChatRecordMode?(e(),t(T,{key:9,class:"zp-safe-area-placeholder",style:a([{height:l.safeAreaBottom+"px"}])},null,8,["style"])):n("",!0)])),_:3},8,["style"]),l.showEmpty?(e(),t(T,{key:1,class:s({"zp-empty-view":!0,"zp-empty-view-center":l.emptyViewCenter}),style:a([l.emptyViewSuperStyle,l.chatRecordRotateStyle])},{default:i((()=>[l.zSlots.empty?N(l.$slots,"empty",{key:0,isLoadFailed:l.isLoadFailed},void 0,!0):(e(),t(M,{key:1,emptyViewImg:l.finalEmptyViewImg,emptyViewText:l.finalEmptyViewText,showEmptyViewReload:l.finalShowEmptyViewReload,emptyViewReloadText:l.finalEmptyViewReloadText,isLoadFailed:l.isLoadFailed,emptyViewStyle:l.emptyViewStyle,emptyViewTitleStyle:l.emptyViewTitleStyle,emptyViewImgStyle:l.emptyViewImgStyle,emptyViewReloadStyle:l.emptyViewReloadStyle,emptyViewZIndex:l.emptyViewZIndex,emptyViewFixed:l.emptyViewFixed,unit:l.unit,onReload:l._emptyViewReload,onViewClick:l._emptyViewClick},null,8,["emptyViewImg","emptyViewText","showEmptyViewReload","emptyViewReloadText","isLoadFailed","emptyViewStyle","emptyViewTitleStyle","emptyViewImgStyle","emptyViewReloadStyle","emptyViewZIndex","emptyViewFixed","unit","onReload","onViewClick"]))])),_:3},8,["class","style"])):n("",!0)])),_:3},8,["style"])])),_:3},8,["style","change:prop","prop","data-refresherThreshold","data-refresherF2Enabled","data-refresherF2Threshold","data-isIos","data-loading","data-useChatRecordMode","data-refresherEnabled","data-useCustomRefresher","data-pageScrollTop","data-scrollTop","data-refresherMaxAngle","data-refresherNoTransform","data-refresherAecc","data-usePageScroll","data-watchTouchDirectionChange","data-oldIsTouchmoving","data-refresherOutRate","data-refresherPullRate","data-hasTouchmove","change:renderPropIsIosAndH5","renderPropIsIosAndH5"])])),_:3},8,["onTouchstart","onTouchmove","onTouchend","onTouchcancel","onMousedown","onMousemove","onMouseup","onMouseleave"])])),_:3},8,["class","style","scroll-top","scroll-left","scroll-x","scroll-y","enable-back-to-top","show-scrollbar","scroll-with-animation","scroll-into-view","lower-threshold","refresher-enabled","refresher-threshold","refresher-default-style","refresher-background","refresher-triggered","onScroll","onScrolltolower","onScrolltoupper","onRefresherrestore"])])),_:3},8,["class","style"]),l.zSlots.right?(e(),t(T,{key:1,class:s({"zp-page-right":!0,"zp-absoulte zp-right":l.finalIsOldWebView})},{default:i((()=>[N(l.$slots,"right",{},void 0,!0)])),_:3},8,["class"])):n("",!0)])),_:3},8,["class","style"]),o(T,{class:"zp-page-bottom-container",style:a({background:l.bottomBgColor})},{default:i((()=>[!l.usePageScroll&&l.zSlots.bottom?N(l.$slots,"bottom",{key:0},void 0,!0):l.usePageScroll&&l.zSlots.bottom?(e(),t(T,{key:1,class:"zp-page-bottom",onTouchmove:c[5]||(c[5]=h((()=>{}),["stop","prevent"])),style:a([{bottom:`${l.windowBottom}px`}])},{default:i((()=>[N(l.$slots,"bottom",{},void 0,!0)])),_:3},8,["style"])):n("",!0),l.useChatRecordMode&&l.autoAdjustPositionWhenChat?(e(),y(S,{key:2},[o(T,{style:a([{height:l.chatRecordModeSafeAreaBottom+"px"}])},null,8,["style"]),o(T,{class:"zp-page-bottom-keyboard-placeholder-animate",style:a([{height:l.keyboardHeight+"px"}])},null,8,["style"])],64)):n("",!0)])),_:3},8,["style"]),l.showBackToTopClass?(e(),t(T,{key:4,class:s(l.finalBackToTopClass),style:a([l.finalBackToTopStyle]),onClick:h(l._backToTopClick,["stop"])},{default:i((()=>[l.zSlots.backToTop?N(l.$slots,"backToTop",{key:0},void 0,!0):(e(),t(x,{key:1,class:s(["zp-back-to-top-img",{"zp-back-to-top-img-inversion":l.useChatRecordMode&&!l.backToTopImg.length}]),src:l.backToTopImg.length?l.backToTopImg:l.base64BackToTop},null,8,["class","src"]))])),_:3},8,["class","style","onClick"])):n("",!0),l.showLoading&&l.zSlots.loading&&l.loadingFullFixed?(e(),t(T,{key:5,class:"zp-loading-fixed"},{default:i((()=>[N(l.$slots,"loading",{},void 0,!0)])),_:3})):n("",!0)])),_:3},8,["class","style"])}],["__scopeId","data-v-0f372674"]]);export{qe as _};
