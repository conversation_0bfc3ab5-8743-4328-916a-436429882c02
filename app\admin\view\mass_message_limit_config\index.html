{layout name="layout1" /}

<div class="wrapper">
    <div class="layui-card">
        <div class="layui-card-body">
            <!--添加按钮-->
            <div style="padding-bottom: 10px;">
                <button class="layui-btn layui-btn-sm layuiadmin-btn-admin {$view_theme_color}" data-type="add">
                    <i class="layui-icon layui-icon-add-1"></i>添加配置
                </button>
                <button class="layui-btn layui-btn-sm layui-btn-warm" data-type="reAllocateAll">
                    <i class="layui-icon layui-icon-refresh"></i>重新分配所有商家
                </button>
            </div>

            <!--表格-->
            <table id="mass-message-lists" lay-filter="mass-message-lists"></table>

            <!--js模板-->
            <script type="text/html" id="tier-level-tpl">
                {{# if(d.tier_level == 0) { }}
                    <span class="layui-badge layui-bg-gray">0元入驻</span>
                {{# } else if(d.tier_level == 1) { }}
                    <span class="layui-badge layui-bg-blue">商家会员</span>
                {{# } else if(d.tier_level == 2) { }}
                    <span class="layui-badge layui-bg-orange">实力厂商</span>
                {{# } else { }}
                    <span class="layui-badge">未知等级</span>
                {{# } }}
            </script>

            <script type="text/html" id="allocation-detail-tpl">
                <div style="line-height: 20px;">
                    <div>总数：{{d.total_purchaser_count}}人</div>
                    <div style="font-size: 12px; color: #999;">
                        低活跃：{{d.level1_count}}人({{d.level1_percent}}%) |
                        中活跃：{{d.level2_count}}人({{d.level2_percent}}%) |
                        高活跃：{{d.level3_count}}人({{d.level3_percent}}%)
                    </div>
                </div>
            </script>

            <script type="text/html" id="mass-message-operation">
                <a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="edit">
                    <i class="layui-icon layui-icon-edit"></i>编辑
                </a>
                <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">
                    <i class="layui-icon layui-icon-delete"></i>删除
                </a>
            </script>
        </div>
    </div>
</div>
<script>
    layui.use(['table'], function(){
        var form = layui.form
            ,table = layui.table;

        // 初始化表格
        like.tableLists('#mass-message-lists', '{:url("index")}', [
            {field: 'id', width: 80, title: 'ID', sort: true, align: 'center'},
            {field: 'tier_level', width: 120, title: '商家等级', align: 'center', templet: '#tier-level-tpl'},
            {field: 'daily_limit', width: 120, title: '每日群发限制', align: 'center'},
            {field: 'total_purchaser_count', width: 120, title: '采购人员总数', align: 'center'},
            {field: 'allocation_detail', width: 280, title: '分配详情', align: 'center', templet: '#allocation-detail-tpl'},
            {field: 'create_time', width: 160, title: '创建时间', align: 'center', templet: function(d) {
                return layui.util.toDateString(d.create_time * 1000, 'yyyy-MM-dd HH:mm:ss');
            }},
            {title: '操作', width: 150, align: 'center', fixed: 'right', toolbar: '#mass-message-operation'}
        ]);

        //监听工具条
        table.on('tool(mass-message-lists)', function(obj){
            if(obj.event === 'del'){
                var id = obj.data.id;
                layer.confirm('确定删除此配置？', function(index){
                    like.ajax({
                        url:'{:url("delete")}',
                        data:{'id':id},
                        type:"post",
                        success:function(res)
                        {
                            if(res.code == 1) {
                                obj.del();
                                layui.layer.msg(res.msg, {
                                    offset: '15px'
                                    , icon: 1
                                    , time: 1000
                                });
                                layer.close(index);
                            }
                        }
                    });
                });
            }else if(obj.event === 'edit'){
                var id = obj.data.id;
                layer.open({
                    type: 2
                    ,title: '编辑配置'
                    ,content: '{:url("edit")}?id='+id
                    ,area: ['90%', '90%']
                    ,btn: ['确定', '取消']
                    ,yes: function(index, layero){
                        var iframeWindow = window['layui-layer-iframe'+ index]
                            ,submitID = 'editSubmit'
                            ,submit = layero.find('iframe').contents().find('#'+ submitID);

                        //监听提交
                        iframeWindow.layui.form.on('submit('+ submitID +')', function(data){
                            var field = data.field;
                            like.ajax({
                                url:'{:url("edit")}',
                                data:field,
                                type:"post",
                                success:function(res)
                                {
                                    if(res.code == 1) {
                                        layui.layer.msg(res.msg, {
                                            offset: '15px'
                                            , icon: 1
                                            , time: 1000
                                        });
                                        layer.close(index); //关闭弹层
                                        table.reload('mass-message-lists'); //数据刷新
                                    }
                                }
                            });
                        });

                        submit.trigger('click');
                    }
                })
            }
        });

        //事件
        var active = {
            add: function(){
                layer.open({
                    type: 2
                    ,title: '添加配置'
                    ,content: '{:url("add")}'
                    ,area: ['90%', '90%']
                    ,btn: ['确定', '取消']
                    ,yes: function(index, layero){
                        var iframeWindow = window['layui-layer-iframe'+ index]
                            ,submitID = 'addSubmit'
                            ,submit = layero.find('iframe').contents().find('#'+ submitID);
                        //监听提交
                        iframeWindow.layui.form.on('submit('+ submitID +')', function(data){
                            var field = data.field;
                            like.ajax({
                                url:'{:url("add")}',
                               data:field,
                               type:"post",
                               success:function(res)
                               {
                                   if(res.code == 1) {
                                       layui.layer.msg(res.msg, {
                                           offset: '15px'
                                           , icon: 1
                                           , time: 1000
                                       });
                                       layer.close(index); //关闭弹层
                                       table.reload('mass-message-lists'); //数据刷新
                                   }
                               }
                            });
                        });

                        submit.trigger('click');
                    }
                });
            },
            reAllocateAll: function(){
                layer.confirm('确认重新分配所有商家的采购人员吗？此操作将清除现有分配并重新分配。', function(index) {
                    like.ajax({
                        url: '{:url("reAllocateAll")}',
                        type: "post",
                        success: function(res) {
                            if (res.code == 1) {
                                layui.layer.msg(res.msg, {
                                    offset: '15px'
                                    , icon: 1
                                    , time: 2000
                                });
                            } else {
                                layui.layer.msg(res.msg, {
                                    offset: '15px'
                                    , icon: 2
                                    , time: 2000
                                });
                            }
                            layer.close(index);
                        }
                    });
                });
            }
        };
        $('.layui-btn.layuiadmin-btn-admin').on('click', function(){
            var type = $(this).data('type');
            active[type] ? active[type].call(this) : '';
        });
    });
</script>

