<?php

use app\common\websocket\CombinedHandler;
use think\swoole\Table;

return [
    'server'     => [
        'host'      => env('SWOOLE_HOST', '0.0.0.0'), // 监听地址
        'port'      => env('SWOOLE_PORT', 20211), // 监听端口
        'mode'      => SWOOLE_PROCESS, // 运行模式 默认为SWOOLE_PROCESS
        'sock_type' => SWOOLE_SOCK_TCP, // sock type 默认为SWOOLE_SOCK_TCP
        'options'   => [
            'pid_file'              => runtime_path() . 'swoole.pid',
            'log_file'              => runtime_path() . 'swoole.log',
            'daemonize'             => false,
            // Normally this value should be 1~4 times larger according to your cpu cores.
            'reactor_num'           => swoole_cpu_num(),
            'worker_num'            => swoole_cpu_num(),
            'task_worker_num'       => swoole_cpu_num(),
            'enable_static_handler' => true,
            'document_root'         => root_path('public'),
            'package_max_length'    => 20 * 1024 * 1024,
            'buffer_output_size'    => 10 * 1024 * 1024,
            'socket_buffer_size'    => 128 * 1024 * 1024,
            'heartbeat_idle_time'        => 900, // 增加到15分钟，避免连接过早关闭
            'heartbeat_check_interval'   => 120, // 增加到2分钟，减少检查频率
        ],
    ],
    'websocket'  => [
        'enable'        => true,
        'handler'       => CombinedHandler::class, // 使用新的组合处理类，同时支持客服和管理员通知
        'ping_interval' => 15000, // 15秒，单位毫秒，适中的心跳间隔
        'ping_timeout'  => 45000, // 45秒，单位毫秒，是ping_interval的3倍
        'room'          => [
            'type'  => 'redis', // 从table切换为redis，避免内存限制问题
            'table' => [
                'room_rows'   => 16384,  // 增加到原来的4倍
                'room_size'   => 4096,   // 增加到原来的2倍
                'client_rows' => 32768,  // 增加到原来的4倍
                'client_size' => 4096,   // 增加到原来的2倍
            ],
            'redis' => [
                'host'          => 'r-bp180wgq6voudt9d75.redis.rds.aliyuncs.com',
                'port'          => 6379,
                'max_active'    => 100,  // 增加最大活跃连接数
                'max_wait_time' => 5,
                'database'      => 0,   // 指定Redis数据库
                'timeout'       => 5,   // 连接超时时间
            ],
        ],
        'listen'        => [
            // 监听的事件
            'login' => \app\common\listener\websocket\Login::class,
            'chat' => \app\common\listener\websocket\Chat::class, // 统一处理客服聊天和用户聊天
            'close' => \app\common\listener\websocket\Close::class,
            'read' => \app\common\listener\websocket\Read::class,
            'admin_notification' => \app\common\listener\websocket\NotificationListener::class, // 添加管理员通知事件
            'ping' => function($data) {
                // 处理ping事件，返回pong响应
                return [
                    'event' => 'pong',
                    'data' => [
                        'server_time' => time(),
                        'client_time' => $data['timestamp'] ?? time()
                    ]
                ];
            },
        ],
        'subscribe'     => [
            'admin_notifications' => \app\common\listener\websocket\NotificationListener::class,
        ],
    ],
    'rpc'        => [
        'server' => [
            'enable'   => false,
            'port'     => 9000,
            'services' => [
            ],
        ],
        'client' => [
        ],
    ],
    'hot_update' => [
        'enable'  => env('APP_DEBUG', false),
        'name'    => ['*.php'],
        'include' => [app_path()],
        'exclude' => [],
    ],
    //连接池
    'pool'       => [
        'db'    => [
            'enable'        => true,
            'max_active'    => 3,
            'max_wait_time' => 5,
        ],
        'cache' => [
            'enable'        => true,
            'max_active'    => 3,
            'max_wait_time' => 5,
        ],
        //自定义连接池
    ],
    //队列
    'queue'      => [
        'enable'  => false,
        'workers' => [],
    ],
    'coroutine'  => [
        'enable' => true,
        'flags'  => SWOOLE_HOOK_ALL,
    ],
    'tables'     => [],
    //每个worker里需要预加载以共用的实例
    'concretes'  => [],
    //重置器
    'resetters'  => [],
    //每次请求前需要清空的实例
    'instances'  => [],
    //每次请求前需要重新执行的服务
    'services'   => [],
];
