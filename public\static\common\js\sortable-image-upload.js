/**
 * 支持拖动排序的图片上传组件
 * 基于Sortable.js实现
 */

(function($) {
    'use strict';

    // 扩展like对象，添加可排序的图片上传功能
    if (typeof like !== 'undefined') {
        like.sortableImageUpload = function(options) {
            var defaults = {
                limit: 5,
                field: "images[]",
                that: null,
                content: '/admin/file/lists?type=10',
                sortable: true,
                showOrder: true,
                onSort: null,
                onAdd: null,
                onRemove: null
            };
            
            var opts = $.extend(defaults, options);
            var that = opts.that;
            
            // 打开图片选择弹窗
            parent.layer.open({
                type: 2,
                title: "上传图片",
                shadeClose: true,
                maxmin: true,
                anim: 1,
                shade: 0.3,
                area: ["90%", "90%"],
                content: opts.content,
                success: function (layero, index) {
                    var iframeNode = $(layero).find("iframe").contents();

                    iframeNode.find("#okFile").click(function () {
                        var fileUrls = iframeNode.find("#fileUrl").val();
                        var urls = fileUrls.split(',');

                        if (urls.length <= 0) {
                            parent.layer.msg("请至少选择一张图片");
                            return false;
                        }

                        if (urls.length > opts.limit) {
                            parent.layer.msg("限制只能选" + opts.limit + "张");
                            return false;
                        }

                        // 添加图片到容器
                        urls.forEach(function (url, index) {
                            addImageToContainer(url, opts, that);
                        });

                        // 初始化拖动排序
                        if (opts.sortable) {
                            initSortable(that, opts);
                        }

                        // 更新序号
                        if (opts.showOrder) {
                            updateImageOrder(that);
                        }

                        // 回调函数
                        if (typeof opts.onAdd === 'function') {
                            opts.onAdd(urls);
                        }

                        parent.layer.close(index);
                    });
                }
            });
        };

        // 添加图片到容器
        function addImageToContainer(url, opts, that) {
            var template = '<div class="sortable-image-item" data-url="' + url + '">';
            template += '<div class="image-wrapper">';
            template += '<img src="' + url + '" alt="img" class="uploaded-image">';
            template += '<input type="hidden" name="' + opts.field + '" value="' + url + '">';
            template += '<div class="image-controls">';
            if (opts.showOrder) {
                template += '<span class="image-order">1</span>';
            }
            template += '<div class="image-actions">';
            template += '<button type="button" class="btn-preview" title="预览"><i class="layui-icon layui-icon-search"></i></button>';
            template += '<button type="button" class="btn-delete" title="删除"><i class="layui-icon layui-icon-delete"></i></button>';
            template += '</div>';
            template += '</div>';
            template += '<div class="drag-handle" title="拖动排序"><i class="layui-icon layui-icon-slider"></i></div>';
            template += '</div>';
            template += '</div>';
            
            that.parent().before(template);
        }

        // 初始化拖动排序
        function initSortable(that, opts) {
            var container = that.closest('.like-upload-image').parent();
            
            if (container.find('.sortable-container').length === 0) {
                // 创建排序容器
                var sortableContainer = $('<div class="sortable-container"></div>');
                container.find('.sortable-image-item').appendTo(sortableContainer);
                container.prepend(sortableContainer);
                
                // 初始化Sortable
                if (typeof Sortable !== 'undefined') {
                    new Sortable(sortableContainer[0], {
                        animation: 150,
                        handle: '.drag-handle',
                        ghostClass: 'sortable-ghost',
                        chosenClass: 'sortable-chosen',
                        dragClass: 'sortable-drag',
                        onEnd: function(evt) {
                            updateImageOrder(that);
                            if (typeof opts.onSort === 'function') {
                                opts.onSort(evt);
                            }
                        }
                    });
                }
            }
        }

        // 更新图片序号
        function updateImageOrder(that) {
            var container = that.closest('.like-upload-image').parent();
            container.find('.sortable-image-item').each(function(index) {
                $(this).find('.image-order').text(index + 1);
            });
        }

        // 删除图片事件
        $(document).on('click', '.btn-delete', function() {
            var item = $(this).closest('.sortable-image-item');
            var container = item.closest('.like-upload-image').parent();
            
            layer.confirm('确定要删除这张图片吗？', {
                icon: 3,
                title: '提示'
            }, function(index) {
                item.remove();
                updateImageOrder(container.find('.upload-image-elem'));
                layer.close(index);
            });
        });

        // 预览图片事件
        $(document).on('click', '.btn-preview', function() {
            var imgSrc = $(this).closest('.sortable-image-item').find('img').attr('src');
            layer.photos({
                photos: {
                    "title": "图片预览",
                    "data": [{
                        "src": imgSrc
                    }]
                },
                anim: 5,
                area: ['90%', '90%'],
                maxWidth: '90%',
                maxHeight: '90%',
                shade: 0.8,
                shadeClose: true,
                closeBtn: 1,
                move: false
            });
        });

        // 扩展删除上传文件功能，支持新的结构
        var originalDelUpload = like.delUpload;
        like.delUpload = function() {
            // 调用原始的删除功能
            if (originalDelUpload) {
                originalDelUpload.call(this);
            }
            
            // 添加新的删除功能
            $(document).off('click', '.del-upload-btn').on('click', '.del-upload-btn', function() {
                var item = $(this).closest('.upload-image-div, .sortable-image-item');
                var container = item.parent();
                
                item.remove();
                
                // 如果是可排序容器，更新序号
                if (container.hasClass('sortable-container')) {
                    container.find('.sortable-image-item').each(function(index) {
                        $(this).find('.image-order').text(index + 1);
                    });
                }
                
                // 显示添加按钮
                var uploadElem = container.siblings('.like-upload-image').find('.upload-image-elem');
                if (uploadElem.length > 0) {
                    uploadElem.show();
                }
            });
        };
    }

    // 商品媒体上传组件（图片+视频混合）
    if (typeof like !== 'undefined') {
        like.goodsMediaUpload = {
            maxCount: 6,
            mediaList: [],
            container: null,

            init: function(container) {
                this.container = $(container);
                this.bindEvents();
                this.initSortable();
            },

            bindEvents: function() {
                var self = this;

                // 添加图片
                $(document).on('click', '#add-images', function() {
                    self.addImages();
                });

                // 添加视频
                $(document).on('click', '#add-video', function() {
                    self.addVideo();
                });

                // 删除媒体
                $(document).on('click', '.btn-delete-media', function() {
                    var index = $(this).data('index');
                    self.removeMedia(index);
                });

                // 预览媒体
                $(document).on('click', '.btn-preview-media', function() {
                    var index = $(this).data('index');
                    self.previewMedia(index);
                });

                // 设为封面
                $(document).on('click', '.btn-set-cover', function() {
                    var index = $(this).data('index');
                    self.setCover(index);
                });
            },

            addImages: function() {
                var self = this;
                var remainingCount = this.maxCount - this.mediaList.length;

                if (remainingCount <= 0) {
                    layer.msg('最多只能上传' + this.maxCount + '个媒体文件', {icon: 2});
                    return;
                }

                parent.layer.open({
                    type: 2,
                    title: "选择图片",
                    shadeClose: true,
                    maxmin: true,
                    anim: 1,
                    shade: 0.3,
                    area: ["90%", "90%"],
                    content: '/shop/file/lists?type=10',
                    success: function (layero, index) {
                        var iframeNode = $(layero).find("iframe").contents();

                        iframeNode.find("#okFile").click(function () {
                            var fileUrls = iframeNode.find("#fileUrl").val();
                            var urls = fileUrls.split(',').filter(url => url.trim());

                            if (urls.length > remainingCount) {
                                parent.layer.msg("最多还能选择" + remainingCount + "个文件");
                                return false;
                            }

                            urls.forEach(function(url) {
                                self.addMediaItem({
                                    type: 'image',
                                    url: url.trim(),
                                    name: '图片'
                                });
                            });

                            self.updateDisplay();
                            parent.layer.close(index);
                        });
                    }
                });
            },

            addVideo: function() {
                var self = this;
                var remainingCount = this.maxCount - this.mediaList.length;

                if (remainingCount <= 0) {
                    layer.msg('最多只能上传' + this.maxCount + '个媒体文件', {icon: 2});
                    return;
                }

                parent.layer.open({
                    type: 2,
                    title: "选择视频",
                    shadeClose: true,
                    maxmin: true,
                    anim: 1,
                    shade: 0.3,
                    area: ["90%", "90%"],
                    content: '/shop/file/videoList',
                    success: function (layero, index) {
                        var iframeNode = $(layero).find("iframe").contents();

                        iframeNode.find("#okFile").click(function () {
                            var fileUrl = iframeNode.find("#fileUrl").val();

                            if (fileUrl) {
                                self.addMediaItem({
                                    type: 'video',
                                    url: fileUrl.trim(),
                                    name: '视频'
                                });
                                self.updateDisplay();
                            }

                            parent.layer.close(index);
                        });
                    }
                });
            },

            addMediaItem: function(media) {
                this.mediaList.push(media);
            },

            removeMedia: function(index) {
                var self = this;
                layer.confirm('确定要删除这个媒体文件吗？', {
                    icon: 3,
                    title: '提示'
                }, function(layerIndex) {
                    self.mediaList.splice(index, 1);
                    self.updateDisplay();
                    layer.close(layerIndex);
                });
            },

            setCover: function(index) {
                // 将指定项移到第一位
                if (index > 0) {
                    var item = this.mediaList.splice(index, 1)[0];
                    this.mediaList.unshift(item);
                    this.updateDisplay();
                    layer.msg('已设为封面', {icon: 1});
                }
            },

            previewMedia: function(index) {
                var media = this.mediaList[index];
                if (media.type === 'image') {
                    layer.photos({
                        photos: {
                            "title": "图片预览",
                            "data": [{
                                "src": media.url
                            }]
                        },
                        anim: 5
                    });
                } else if (media.type === 'video') {
                    layer.open({
                        type: 1,
                        title: '视频预览',
                        area: ['80%', '80%'],
                        content: '<video controls style="width:100%;height:100%;"><source src="' + media.url + '" type="video/mp4"></video>'
                    });
                }
            },

            updateDisplay: function() {
                var container = $('#mediaPreviewContainer');
                container.empty();

                var self = this;
                this.mediaList.forEach(function(media, index) {
                    var mediaHtml = self.createMediaHtml(media, index);
                    container.append(mediaHtml);
                });

                this.updateFormFields();
                this.initSortable();
            },

            createMediaHtml: function(media, index) {
                var isCover = index === 0;
                var mediaContent = '';

                if (media.type === 'image') {
                    mediaContent = '<img src="' + media.url + '" alt="商品图片">';
                } else if (media.type === 'video') {
                    mediaContent = '<video src="' + media.url + '" muted></video>' +
                                 '<div class="video-play-btn"><i class="layui-icon layui-icon-play"></i></div>';
                }

                return '<div class="media-item' + (isCover ? ' is-cover' : '') + '" data-index="' + index + '">' +
                    '<div class="media-content">' + mediaContent + '</div>' +
                    '<div class="media-controls">' +
                        '<span class="media-order">' + (index + 1) + '</span>' +
                        '<div class="media-type-badge ' + media.type + '">' +
                            (media.type === 'image' ? '图片' : '视频') +
                        '</div>' +
                        '<div class="media-actions">' +
                            '<button type="button" class="btn-preview-media" data-index="' + index + '" title="预览">' +
                                '<i class="layui-icon layui-icon-search"></i>' +
                            '</button>' +
                            (index > 0 ? '<button type="button" class="btn-set-cover" data-index="' + index + '" title="设为封面">' +
                                '<i class="layui-icon layui-icon-star"></i>' +
                            '</button>' : '') +
                            '<button type="button" class="btn-delete-media" data-index="' + index + '" title="删除">' +
                                '<i class="layui-icon layui-icon-delete"></i>' +
                            '</button>' +
                        '</div>' +
                    '</div>' +
                    '<div class="drag-handle" title="拖动排序">' +
                        '<i class="layui-icon layui-icon-slider"></i>' +
                    '</div>' +
                '</div>';
            },

            updateFormFields: function() {
                // 更新隐藏表单字段
                var images = this.mediaList.filter(item => item.type === 'image');
                var videos = this.mediaList.filter(item => item.type === 'video');

                // 封面图（第一个媒体文件）
                $('#coverImage').val(this.mediaList.length > 0 ? this.mediaList[0].url : '');

                // 分享海报（使用封面图）
                $('#posterImage').val(this.mediaList.length > 0 ? this.mediaList[0].url : '');

                // 商品视频（第一个视频）
                $('#goodsVideo').val(videos.length > 0 ? videos[0].url : '');

                // 商品轮播图
                var goodsImagesContainer = $('#goodsImagesContainer');
                goodsImagesContainer.empty();
                images.forEach(function(image) {
                    goodsImagesContainer.append('<input type="hidden" name="goods_image[]" value="' + image.url + '">');
                });
            },

            initSortable: function() {
                var container = document.getElementById('mediaPreviewContainer');
                if (container && typeof Sortable !== 'undefined') {
                    var self = this;
                    new Sortable(container, {
                        animation: 150,
                        handle: '.drag-handle',
                        ghostClass: 'sortable-ghost',
                        chosenClass: 'sortable-chosen',
                        dragClass: 'sortable-drag',
                        onEnd: function(evt) {
                            // 更新数组顺序
                            var item = self.mediaList.splice(evt.oldIndex, 1)[0];
                            self.mediaList.splice(evt.newIndex, 0, item);
                            self.updateDisplay();
                        }
                    });
                }
            }
        };
    }

})(jQuery);
