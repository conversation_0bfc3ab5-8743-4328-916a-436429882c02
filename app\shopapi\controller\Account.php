<?php


namespace app\shopapi\controller;


use app\common\basics\ShopApi;
use app\shopapi\logic\LoginLogic;
use app\common\server\JsonServer;
use app\shopapi\validate\LoginValidate;

/**
 * 商家移动端账号登录
 * Class Account
 * @package app\shopapi\controller
 */
class Account extends ShopApi
{

    public $like_not_need_login = ['login','oaLogin','codeUrl']; // Removed bindWechat as it might require login context now


    /**
     * @notes 账号密码登录
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2021/11/9 16:21
     */
    public function login()
    {
        $post = $this->request->post();
        (new LoginValidate())->goCheck();
        $result = LoginLogic::accountLogin($post);
        return JsonServer::success('登录成功', $result);
    }

    /**
     * showdoc
     * @catalog 接口/账号
     * @title 微信H5登录
     * @description 微信H5登录
     * @method post
     * @url /account/oalogin
     * @return {"code":1,"msg":"登录成功","data":["token":"3237676fa733d73333341",//登录令牌"nickname":"好象cms小林",//昵称"avatar":"http://b2c.yixiangonline.com/uploads/user/avatar/3f102df244d5b40f21c4b25dc321c5ab.jpeg",//头像url"level":0,//等级],"show":0,"time":"0.775400"}
     * @param code 必填 string code
     * @return_param token string 登录令牌
     * @return_param nickname string 昵称
     * @return_param avatar string 头像
     * @remark
     * @number 1
     */
    public function oaLogin()
    {
        $post = $this->request->post();
        if (empty($post)) {
            // 尝试从GET参数获取
            $post = $this->request->get();
        }
        try {
            // 检查是否是绑定操作（账号密码 + openid）
            $isBindOperation = !empty($post['account']) && !empty($post['password']) && !empty($post['openid']);

            if ($isBindOperation) {
                trace('检测到绑定操作: 账号=' . $post['account'] . ', openid=' . $post['openid'], 'info');

                // 调用绑定逻辑
                $data = LoginLogic::bindAccountWithOpenid($post);
                // 记录返回数据，方便调试
                trace('账号绑定微信返回数据: ' . json_encode($data), 'info');
                if (is_string($data)) {
                    // 如果返回的是字符串，表示出错
                    return JsonServer::error($data);
                }
                return JsonServer::success('绑定成功并登录', $data);
            } else {
                // 使用商家后台的登录逻辑
                $data = LoginLogic::oaLogin($post);

                // 记录返回数据，方便调试
                trace('微信登录返回数据: ' . json_encode($data), 'info');

                // 处理返回结果
                if (is_string($data)) {
                    // 如果返回的是字符串，表示出错
                    return JsonServer::error($data);
                }

                // 根据 LoginLogic 返回的状态处理响应
                if (isset($data['status'])) {
                    switch ($data['status']) {
                        case 'success':
                            return JsonServer::success('登录成功', $data);
                        case 'bind_success':
                            return JsonServer::success('绑定成功', $data);
                        case 'unbind':
                            // 返回需要绑定的信息给前端
                            return json(['code' => 1102, 'msg' => '微信未绑定账号，请先使用账号密码登录后绑定', 'data' => $data,'show'=>1]);
                        default:
                            return JsonServer::error('未知的处理状态');
                    }
                } else {
                    // 兼容旧逻辑
                    return JsonServer::success('登录成功', $data);
                }
            }
        } catch (\Exception $e) {
            trace('微信登录/绑定异常: ' . $e->getMessage(), 'error');
            return JsonServer::error('微信未绑定账号，请先使用账号密码登录后绑定');
        }
    }


    /**
     * showdoc
     * @catalog 接口/账号
     * @title 获取获取向微信请求code的链接
     * @description
     * @method get
     * @url /account/codeurl
     * @param url 必填 varchar 前端当前url
     * @return_param url string codeurl
     * @remark 这里是备注信息
     * @number 0
     * @return  {"code":1,"msg":"获取成功","data":{"url":'http://mp.weixin……'}}
     */
    public function codeUrl()
    {
        $url = $this->request->get('url');
        $type = $this->request->get('type', 'login'); // 获取 type 参数，默认为 login

        if (!$url) {
            return JsonServer::error('缺少 url 参数');
        }

        try {
            $codeUrl = LoginLogic::codeUrl($url, $type);
            trace('生成微信授权URL: ' . $codeUrl, 'info');
            return JsonServer::success('获取成功', ['url' => $codeUrl]);
        } catch (\Exception $e) {
            trace('生成微信授权URL异常: ' . $e->getMessage(), 'error');
            return JsonServer::error('获取微信授权URL失败: ' . $e->getMessage());
        }
    }

     /**
     * @notes 退出登录
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2021/11/9 15:49
     */
    public function logout()
    {
        LoginLogic::logout($this->admin_id, $this->client);
        return JsonServer::success();
    }

    /**
     * @notes 绑定微信
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2023/11/15 15:49
     */
    public function bindWechat()
    {
        try {
            // 获取请求参数
            $post = $this->request->post();
            if (empty($post)) {
                // 尝试从GET参数获取
                $post = $this->request->get();
            }

            // 记录日志，方便调试
            trace('微信绑定回调参数: ' . json_encode($post), 'info');
            trace('当前管理员ID: ' . $this->admin_id, 'info');

            // 检查是否已登录
            if (empty($this->admin_id)) {
                trace('未登录状态下尝试绑定微信', 'error');
                return JsonServer::error('请先登录后再绑定微信');
            }

            // 参数验证
            if (empty($post['code'])) {
                trace('缺少code参数', 'error');
                return JsonServer::error('缺少code参数');
            }
            if (empty($post['state'])) {
                trace('缺少state参数', 'error');
                return JsonServer::error('缺少state参数');
            }

            // 调用绑定逻辑
            trace('开始调用绑定逻辑', 'info');
            $result = LoginLogic::bindWechat($this->admin_id, $post);
            trace('绑定逻辑返回结果: ' . (is_string($result) ? $result : json_encode($result)), 'info');

            if ($result === true) {
                trace('绑定成功', 'info');
                return JsonServer::success('绑定成功');
            }

            trace('绑定失败: ' . $result, 'error');
            return JsonServer::error($result);
        } catch (\Exception $e) {
            trace('绑定微信过程中发生异常: ' . $e->getMessage(), 'error');
            return JsonServer::error('绑定过程中出错: ' . $e->getMessage());
        }
        // 注意：绑定逻辑已整合到 oaLogin 方法中，通过 type='bind' 触发。
        // 此独立接口可能不再需要，或者需要重新设计其用途（例如，仅用于前端发起绑定流程的跳转）。
        // 暂时返回错误提示。
        return JsonServer::error('绑定操作请通过微信授权回调处理');

        /*
        // 如果仍需独立绑定接口，需要调整逻辑，例如：
        // 1. 确保用户已登录 ($this->admin_id 存在)
        // 2. 获取前端传递的 code 和 state
        // 3. 在 $post 数据中加入 'admin_id' =-> $this->admin_id
        // 4. 调用 LoginLogic::oaLogin($post) 并处理返回结果
        $post = $this->request->post();
        if (empty($this->admin_id)) {
             return JsonServer::error('请先登录');
        }
        if (empty($post['code']) || empty($post['state'])) {
             return JsonServer::error('缺少微信授权参数');
        }
        $post['admin_id'] = $this->admin_id; // 注入当前管理员ID
        $data = LoginLogic::oaLogin($post); // 调用统一处理接口
        // ... 后续处理同 oaLogin ...
        */
     }

     /**
     * @notes 解绑微信
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2023/11/15 15:49
     */
    public function unbindWechat()
    {
        $result = LoginLogic::unbindWechat($this->admin_id);
        if ($result) {
            return JsonServer::success('解绑成功');
        }
        return JsonServer::error('解绑失败');
    }
}