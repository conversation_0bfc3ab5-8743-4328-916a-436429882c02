<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微信小程序订单回调配置指南</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
            line-height: 1.6;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1, h2, h3 {
            color: #333;
        }
        h1 {
            border-bottom: 3px solid #4CAF50;
            padding-bottom: 10px;
        }
        h2 {
            border-bottom: 2px solid #4CAF50;
            padding-bottom: 8px;
            margin-top: 30px;
        }
        .step {
            background-color: #f8f9fa;
            padding: 20px;
            border-left: 4px solid #4CAF50;
            margin: 20px 0;
        }
        .step-number {
            background-color: #4CAF50;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 10px;
        }
        .url-box {
            background-color: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            word-break: break-all;
            margin: 10px 0;
            border: 1px solid #ddd;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            border: 1px solid #ffeaa7;
            margin: 15px 0;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            border: 1px solid #c3e6cb;
            margin: 15px 0;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            padding: 15px;
            border-radius: 5px;
            border: 1px solid #bee5eb;
            margin: 15px 0;
        }
        .screenshot {
            border: 2px solid #ddd;
            border-radius: 5px;
            margin: 15px 0;
            max-width: 100%;
        }
        .code-block {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            border: 1px solid #e9ecef;
            margin: 10px 0;
            overflow-x: auto;
        }
        .button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
        }
        .button:hover {
            background-color: #45a049;
        }
        .checklist {
            list-style: none;
            padding: 0;
        }
        .checklist li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .checklist li:before {
            content: "✓";
            color: #4CAF50;
            font-weight: bold;
            margin-right: 10px;
        }
        .table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .table th, .table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        .table th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📱 微信小程序订单回调配置指南</h1>
        
        <div class="info">
            <strong>配置目的：</strong>实现微信端和项目端的订单状态双向同步，确保用户在微信端确认收货后，项目中的订单状态也能自动更新。
        </div>

        <h2>🎯 配置概述</h2>
        <p>当用户在微信收到发货通知后点击确认收货时，微信会向您配置的回调URL发送通知，您的系统接收到通知后更新订单状态。</p>

        <div class="warning">
            <strong>重要提醒：</strong><br>
            1. 回调URL必须是HTTPS协议<br>
            2. 服务器必须能正常响应微信的POST请求<br>
            3. 配置完成后需要进行测试验证
        </div>

        <h2>📋 配置步骤</h2>

        <div class="step">
            <h3><span class="step-number">1</span>登录微信小程序后台</h3>
            <p>访问 <a href="https://mp.weixin.qq.com" target="_blank">https://mp.weixin.qq.com</a> 并登录您的小程序账号。</p>
        </div>

        <div class="step">
            <h3><span class="step-number">2</span>进入订单管理功能</h3>
            <p>在左侧菜单中找到：<strong>功能</strong> → <strong>订单与物流</strong> → <strong>订单管理</strong></p>
        </div>

        <div class="step">
            <h3><span class="step-number">3</span>配置回调URL</h3>
            <p>在订单管理页面中找到"回调配置"或"消息推送配置"选项，配置以下URL：</p>
            
            <div class="url-box">
                <strong>确认收货回调URL：</strong><br>
                https://您的域名/wechat/order/confirm-receive
            </div>
            
            <div class="warning">
                请将"您的域名"替换为您的实际域名，例如：<br>
                https://shop.example.com/wechat/order/confirm-receive
            </div>
        </div>

        <div class="step">
            <h3><span class="step-number">4</span>配置消息推送</h3>
            <p>如果需要配置消息推送的Token和EncodingAESKey，请使用以下信息：</p>
            
            <table class="table">
                <tr>
                    <th>参数</th>
                    <th>说明</th>
                    <th>建议值</th>
                </tr>
                <tr>
                    <td>Token</td>
                    <td>用于验证消息来源</td>
                    <td>自定义字符串（建议使用随机生成的32位字符串）</td>
                </tr>
                <tr>
                    <td>EncodingAESKey</td>
                    <td>消息加密密钥</td>
                    <td>微信自动生成或手动填写43位字符串</td>
                </tr>
                <tr>
                    <td>消息加密方式</td>
                    <td>选择加密方式</td>
                    <td>建议选择"安全模式"</td>
                </tr>
            </table>
        </div>

        <div class="step">
            <h3><span class="step-number">5</span>保存并启用配置</h3>
            <p>保存配置后，确保启用订单状态回调功能。</p>
        </div>

        <h2>🧪 测试验证</h2>

        <div class="step">
            <h3>测试回调接口</h3>
            <p>您可以使用以下方法测试回调接口是否正常工作：</p>
            
            <div class="code-block">
curl -X POST https://您的域名/wechat/order/confirm-receive \
  -H "Content-Type: application/json" \
  -d '{
    "order_key": {
      "order_number_type": 2,
      "transaction_id": "测试交易号"
    },
    "received_time": "2024-01-01T12:00:00+08:00"
  }'
            </div>
        </div>

        <div class="step">
            <h3>使用测试工具</h3>
            <p>我们提供了专门的测试工具来验证配置：</p>
            <a href="/wechat_order_sync_test.html" class="button" target="_blank">打开双向同步测试工具</a>
        </div>

        <h2>📊 回调数据格式</h2>

        <h3>微信发送的回调数据格式：</h3>
        <div class="code-block">
{
  "order_key": {
    "order_number_type": 2,
    "transaction_id": "微信支付交易号"
  },
  "received_time": "2024-01-01T12:00:00+08:00"
}
        </div>

        <h3>您的系统应该返回的响应格式：</h3>
        <div class="code-block">
{
  "code": 1,
  "msg": "处理成功",
  "data": {}
}
        </div>

        <h2>🔧 故障排除</h2>

        <div class="step">
            <h3>常见问题及解决方案</h3>
            
            <h4>1. 回调URL无法访问</h4>
            <ul>
                <li>检查域名是否正确解析</li>
                <li>确保使用HTTPS协议</li>
                <li>检查服务器防火墙设置</li>
                <li>确保路由配置正确</li>
            </ul>

            <h4>2. 回调接收不到数据</h4>
            <ul>
                <li>检查微信后台配置是否正确</li>
                <li>确认订单是否已正确发货</li>
                <li>检查日志文件：wechat_order_callback</li>
            </ul>

            <h4>3. 订单状态未更新</h4>
            <ul>
                <li>检查transaction_id是否正确</li>
                <li>确认订单是否存在</li>
                <li>检查订单当前状态</li>
            </ul>
        </div>

        <h2>📝 配置检查清单</h2>

        <ul class="checklist">
            <li>微信小程序后台已配置回调URL</li>
            <li>回调URL使用HTTPS协议</li>
            <li>服务器能正常响应POST请求</li>
            <li>路由配置正确</li>
            <li>已测试回调接口功能</li>
            <li>日志记录正常工作</li>
            <li>订单状态同步正常</li>
        </ul>

        <div class="success">
            <strong>配置完成后：</strong><br>
            用户在微信端确认收货 → 微信发送回调 → 项目订单状态自动更新 → 实现双向同步
        </div>

        <h2>🔗 相关链接</h2>
        <ul>
            <li><a href="/wechat_order_sync_test.html" target="_blank">双向同步测试工具</a></li>
            <li><a href="/wechat_order_flow_test.html" target="_blank">完整流程测试工具</a></li>
            <li><a href="https://developers.weixin.qq.com/miniprogram/dev/platform-capabilities/business-capabilities/order-shipping/order-shipping-half.html" target="_blank">微信官方文档</a></li>
        </ul>
    </div>
</body>
</html>
