<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>提现详情</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="__ADMIN_PATH__/css/layui.css" media="all">
    <link rel="stylesheet" href="__ADMIN_PATH__/css/admin.css" media="all">
    <style>
        .detail-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .detail-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
            border-left: 4px solid #5FB878;
            padding-left: 10px;
        }
        .detail-item {
            display: flex;
            margin-bottom: 12px;
            align-items: center;
        }
        .detail-label {
            width: 120px;
            color: #666;
            font-weight: 500;
        }
        .detail-value {
            flex: 1;
            color: #333;
        }
        .status-badge {
            padding: 4px 12px;
            border-radius: 4px;
            color: white;
            font-size: 12px;
            font-weight: bold;
        }
        .status-0 { background-color: #FFB800; }
        .status-1 { background-color: #5FB878; }
        .status-2 { background-color: #FF5722; }
        .status-3 { background-color: #01AAED; }
        .user-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            margin-right: 15px;
        }
        .user-info {
            display: flex;
            align-items: center;
        }
        .amount-highlight {
            color: #FF5722;
            font-size: 18px;
            font-weight: bold;
        }
        .success-highlight {
            color: #5FB878;
            font-size: 18px;
            font-weight: bold;
        }
        .bank-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #5FB878;
        }
    </style>
</head>
<body>
<div class="layui-fluid">
    <div class="layui-row layui-col-space15">
        <!-- 基本信息 -->
        <div class="layui-col-md12">
            <div class="detail-card">
                <div class="detail-title">基本信息</div>
                <div class="detail-item">
                    <div class="detail-label">提现ID：</div>
                    <div class="detail-value">{$withdrawal.id|default=''}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">提现单号：</div>
                    <div class="detail-value">{$withdrawal.sn|default=''}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">代理信息：</div>
                    <div class="detail-value">
                        <div class="user-info">
                            <img src="{$withdrawal.user_avatar|default='__ADMIN_PATH__/images/default_avatar.png'}" class="user-avatar">
                            <div>
                                <div style="font-weight: bold; font-size: 16px;">{$withdrawal.user_nickname|default='未知代理'}</div>
                                <div style="color: #999; margin-top: 5px;">编号: {$withdrawal.user_sn|default=''}</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">提现金额：</div>
                    <div class="detail-value">
                        <span class="amount-highlight">¥{$withdrawal.apply_amount|default=0}</span>
                    </div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">手续费：</div>
                    <div class="detail-value">¥{$withdrawal.poundage_amount|default=0} ({$withdrawal.poundage_ratio|default=0}%)</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">实际到账：</div>
                    <div class="detail-value">
                        <span class="success-highlight">¥{$withdrawal.left_amount|default=0}</span>
                    </div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">提现方式：</div>
                    <div class="detail-value">
                        {if condition="$withdrawal.type == 1"}
                            银行卡
                        {elseif condition="$withdrawal.type == 2"/}
                            支付宝
                        {else/}
                            未知
                        {/if}
                    </div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">状态：</div>
                    <div class="detail-value">
                        {switch name="$withdrawal.status|default=0"}
                            {case value="0"}
                                <span class="status-badge status-0">待审核</span>
                            {/case}
                            {case value="1"}
                                <span class="status-badge status-1">审核通过</span>
                            {/case}
                            {case value="2"}
                                <span class="status-badge status-2">审核拒绝</span>
                            {/case}
                            {case value="3"}
                                <span class="status-badge status-3">已转账</span>
                            {/case}
                            {default /}
                                <span class="status-badge status-0">未知状态</span>
                        {/switch}
                    </div>
                </div>
            </div>
        </div>

        <!-- 收款信息 -->
        <div class="layui-col-md12">
            <div class="detail-card">
                <div class="detail-title">收款信息</div>
                {if condition="$withdrawal.type == 1"}
                <!-- 银行卡信息 -->
                <div class="bank-info">
                    <div class="detail-item">
                        <div class="detail-label">银行名称：</div>
                        <div class="detail-value">{$withdrawal.bank_name|default='-'}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">开户行：</div>
                        <div class="detail-value">{$withdrawal.bank_branch|default='-'}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">卡号：</div>
                        <div class="detail-value">{$withdrawal.bank_card|default='-'}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">持卡人：</div>
                        <div class="detail-value">{$withdrawal.bank_user|default='-'}</div>
                    </div>
                </div>
                {elseif condition="$withdrawal.type == 2"/}
                <!-- 支付宝信息 -->
                <div class="bank-info">
                    <div class="detail-item">
                        <div class="detail-label">支付宝账号：</div>
                        <div class="detail-value">{$withdrawal.alipay_account|default='-'}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">真实姓名：</div>
                        <div class="detail-value">{$withdrawal.alipay_name|default='-'}</div>
                    </div>
                </div>
                {/if}
            </div>
        </div>

        <!-- 时间信息 -->
        <div class="layui-col-md12">
            <div class="detail-card">
                <div class="detail-title">时间信息</div>
                <div class="detail-item">
                    <div class="detail-label">申请时间：</div>
                    <div class="detail-value">
                        {$withdrawal.create_time|default='-'}
                    </div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">审核时间：</div>
                    <div class="detail-value">
                        {if condition="$withdrawal.update_time && $withdrawal.status > 0"}
                            {$withdrawal.update_time}
                        {else/}
                            -
                        {/if}
                    </div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">转账时间：</div>
                    <div class="detail-value">
                        {$withdrawal.transfer_time|default='-'}
                    </div>
                </div>
            </div>
        </div>

        <!-- 审核信息 -->
        {if condition="$withdrawal.explain || $withdrawal.transfer_content"}
        <div class="layui-col-md12">
            <div class="detail-card">
                <div class="detail-title">审核信息</div>
                {if condition="$withdrawal.explain"}
                <div class="detail-item">
                    <div class="detail-label">提现说明：</div>
                    <div class="detail-value">{$withdrawal.explain|default='-'}</div>
                </div>
                {/if}
                {if condition="$withdrawal.transfer_content"}
                <div class="detail-item">
                    <div class="detail-label">转账说明：</div>
                    <div class="detail-value">{$withdrawal.transfer_content|default='-'}</div>
                </div>
                {/if}
                {if condition="$withdrawal.transfer_voucher"}
                <div class="detail-item">
                    <div class="detail-label">转账凭证：</div>
                    <div class="detail-value">
                        <img src="{$withdrawal.transfer_voucher|default=''}" style="max-width: 200px; max-height: 200px; cursor: pointer;" class="transfer-voucher-img" data-src="{$withdrawal.transfer_voucher|default=''}">
                    </div>
                </div>
                {/if}
            </div>
        </div>
        {/if}


    </div>
</div>

<script src="__ADMIN_PATH__/layui.js"></script>
<script>
layui.use(['layer'], function(){
    var layer = layui.layer;

    // 转账凭证图片点击事件
    $('.transfer-voucher-img').on('click', function(){
        var src = $(this).data('src');
        if(src) {
            layer.photos({
                photos: {
                    data: [{src: src}]
                },
                anim: 5,
                area: ['90%', '90%'],
                maxWidth: '90%',
                maxHeight: '90%',
                shade: 0.8,
                shadeClose: true,
                closeBtn: 1,
                move: false
            });
        }
    });

    // 页面加载完成
    console.log('提现详情页面加载完成');
});
</script>
</body>
</html>
