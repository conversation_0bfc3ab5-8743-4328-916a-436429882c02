<?php

namespace app\admin\validate\goods;

use think\Validate;

/**
 * 资质管理验证器
 * Class QualificationValidate
 * @package app\admin\validate\goods
 */
class QualificationValidate extends Validate
{
    protected $rule = [
        'id' => 'require|integer',
        'name' => 'require|max:100',
        'description' => 'max:500',
        'document_path' => 'max:255',
        'document_name' => 'max:255',
        'valid_days' => 'integer|egt:0',
        'is_required' => 'require|in:0,1',
        'status' => 'require|in:0,1',
        'sort' => 'integer|egt:0'
    ];

    protected $message = [
        'id.require' => 'ID不能为空',
        'id.integer' => 'ID必须为整数',
        'name.require' => '证件名称不能为空',
        'name.max' => '证件名称不能超过100个字符',
        'description.max' => '描述不能超过500个字符',
        'document_path.max' => '文档路径不能超过255个字符',
        'document_name.max' => '文档名称不能超过255个字符',
        'valid_days.integer' => '有效期天数必须为整数',
        'valid_days.egt' => '有效期天数不能小于0',
        'is_required.require' => '是否必传不能为空',
        'is_required.in' => '是否必传值不正确',
        'status.require' => '状态不能为空',
        'status.in' => '状态值不正确',
        'sort.integer' => '排序必须为整数',
        'sort.egt' => '排序不能小于0'
    ];

    protected $scene = [
        'add' => ['name', 'description', 'valid_days', 'status', 'sort'],
        'edit' => ['id', 'name', 'description', 'valid_days', 'status', 'sort']
    ];
}
