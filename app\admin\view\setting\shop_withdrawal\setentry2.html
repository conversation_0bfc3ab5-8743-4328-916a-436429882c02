{layout name="layout1" /}
<style>
    .layui-form-label {
        width: 120px;
    }
    .layui-input-block {
        margin-left: 150px;
    }
</style>
<div class="wrapper">
    <div class="layui-card">
        <div class="layui-card-body">
            <div class="layui-collapse like-layui-collapse" lay-accordion="" style="border:1px dashed #c4c4c4">
                <div class="layui-colla-item">
                    <h2 class="layui-colla-title like-layui-colla-title" style="background-color: #fff">操作提示</h2>
                    <div class="layui-colla-content layui-show">
                        <p>*平台设置首页弹窗设置</p>
                    </div>
                </div>
            </div>
            <!--            表单区域-->
            <div class="layui-form" style="margin-top: 15px;">
                <fieldset class="layui-elem-field">
                    <legend>基础设置</legend>
                    <div class="layui-field-box">

                        <div class="layui-form-item">
                            <lable class="layui-form-label">弹出时间:</lable>
                            <div class="layui-input-block" style="width:300px;">
                                <input type="text" name="date_string" value="{$detail.date_string}" class="layui-input" />
                            </div>
                            <div class="layui-input-block">
                                <span class="layui-word-aux">多个区间用斜杠"/"分开,如:8:00-9:00/12:00-16:00</span>
                            </div>
                        </div>
                    </div>
                </fieldset>
                <fieldset class="layui-elem-field">
                    <legend>弹窗广告</legend>
                    <br/>
                    <div class="layui-form-item">
                        <lable class="layui-form-label">图片:</lable>
                        <div class="layui-input-block">
                            <div class="like-upload-image">
                                {if $detail.ad}
                                <div class="upload-image-div">
                                    <img src="{$detail.ad}" alt="img">
                                    <input type="hidden" name="image" value="{$detail.ad}">
                                    <div class="del-upload-btn">x</div>
                                </div>
                                <div class="upload-image-elem" style="display:none;"><a class="add-upload-image" id="image"> + 添加图片</a></div>
                                {else}
                                <div class="upload-image-elem"><a class="add-upload-image" id="image"> + 添加图片</a></div>
                                {/if}
                            </div>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <lable class="layui-form-label"></lable>
                        <div class="layui-input-block">
                            <span class="layui-word-aux">支持gif</span>
                        </div>
                    </div>


                    <div class="layui-form-item">
                        <lable class="layui-form-label">跳转路径:</lable>
                        <div class="layui-input-block" style="width:300px;">
                            <input type="text" name="date_link" value="{$detail.date_link}" class="layui-input" />
                        </div>
                        <div class="layui-input-block">
                            <span class="layui-word-aux">需要跳转的地址</span>
                        </div>
                    </div>


                    <legend>集采会员弹窗广告</legend>

                        <br/>
                    <div class="layui-form-item">
                        <lable class="layui-form-label">图片:</lable>
                        <div class="layui-input-block">
                            <div class="like-upload-image">
                                {if $detail.ad}
                                <div class="upload-image-div">
                                    <img src="{$detail.ad}" alt="img">
                                    <input type="hidden" name="image" value="{$detail.ad}">
                                    <div class="del-upload-btn">x</div>
                                </div>
                                <div class="upload-image-elem" style="display:none;"><a class="add-upload-image" id="image"> + 添加图片</a></div>
                                {else}
                                <div class="upload-image-elem"><a class="add-upload-image" id="image"> + 添加图片</a></div>
                                {/if}
                            </div>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <lable class="layui-form-label"></lable>
                        <div class="layui-input-block">
                            <span class="layui-word-aux">支持gif</span>
                        </div>
                    </div>


                </fieldset>

                <div class="layui-form-item">
                    <lable class="layui-form-label"></lable>
                    <div class="layui-input-block">
                        <button class="layui-btn layui-btn-normal" lay-submit lay-filter="set">确定</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    layui.config({
        version:"{$front_version}",
        base: '/static/lib/' //静态资源所在路径
    }).use(['form','element'], function(){
        var $ = layui.$,form = layui.form,element = layui.element;

        // 图片上传
        like.delUpload();
        $(document).on("click", "#image", function () {
            like.imageUpload({
                limit: 1,
                field: "ad",
                that: $(this)
            });
        })

        form.on('submit(set)', function(data) {
            like.ajax({
                url:'{:url("setting.ShopWithdrawal/setshopentry2")}',
                data: data.field,
                type:"post",
                success:function(res)
                {
                    if(res.code == 1)
                    {
                        layui.layer.msg(res.msg, {
                            offset: '15px'
                            , icon: 1
                            , time: 1000
                        });
                    }
                }
            });
        });

    });
</script>