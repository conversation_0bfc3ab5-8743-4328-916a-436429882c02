# 聊天记录API修复说明

## 🔧 问题描述

在原有的 `UserLogic::getChatRecord` 方法中，当 `shop_id` 为 0 时，应该返回用户对用户的聊天记录，但原来的逻辑仍然按照客服聊天的方式处理，导致无法正确获取用户间的聊天记录。

## 🎯 修复方案

### 1. 修改 `UserLogic::getChatRecord` 方法

**文件位置：** `app/api/logic/UserLogic.php`

**修改内容：**
```php
public static function getChatRecord($user_id, $shop_id, $page, $size)
{
    // 如果shop_id为0，表示用户对用户聊天
    if ($shop_id == 0) {
        return self::getUserChatRecord($user_id, $page, $size);
    }
    
    // 客服聊天记录（原有逻辑保持不变）
    // ...
}
```

### 2. 新增 `getUserChatRecord` 方法

**功能：** 专门处理用户对用户的聊天记录获取

**特点：**
- 查询条件：`shop_id = 0` 且 `from_type = 'user'` 且 `to_type = 'user'`
- 自动标记消息为已读
- 使用 `ChatLogic::formatChatRecords` 格式化数据
- 返回特殊的用户聊天标识

**代码实现：**
```php
public static function getUserChatRecord($user_id, $page, $size)
{
    // 用户对用户聊天记录查询条件
    $map1 = [
        ['shop_id', '=', 0], // 用户对用户聊天shop_id为0
        ['from_id', '=', $user_id],
        ['from_type', '=', 'user'],
        ['to_type', '=', 'user'],
    ];
    $map2 = [
        ['shop_id', '=', 0], // 用户对用户聊天shop_id为0
        ['to_id', '=', $user_id],
        ['to_type', '=', 'user'],
        ['from_type', '=', 'user'],
    ];
    
    // 获取聊天记录
    $records = ChatRecord::whereOr([$map1, $map2])
        ->order('id desc')
        ->page($page, $size)
        ->select()->toArray();
        
    // 标记消息为已读
    foreach ($records as &$item) {
        ChatRecord::update(['is_read' => 1], [
            'id' => $item['id'],
            'to_id' => $user_id,
            'to_type' => 'user'
        ]);
    }

    $count = ChatRecord::whereOr([$map1, $map2])->count();

    // 格式化聊天记录
    $records = ChatLogic::formatChatRecords($records, $count, $page, $size);

    // 为用户对用户聊天添加特殊标识和结构
    $records['chat_type'] = 'user_chat';
    $records['kefu'] = null; // 用户聊天没有客服信息
    $records['online'] = []; // 用户聊天不需要在线客服信息
    $records['config'] = 1; // 默认配置，允许聊天

    return $records;
}
```

## 📊 数据库查询逻辑

### 用户对用户聊天记录特征
- `shop_id = 0`：标识为用户间聊天
- `from_type = 'user'` 且 `to_type = 'user'`：双方都是用户
- 查询当前用户作为发送者或接收者的所有记录

### 客服聊天记录特征
- `shop_id > 0`：标识为特定商家的客服聊天
- `from_type` 和 `to_type` 包含 'user' 和 'kefu'
- 需要获取客服信息和在线状态

## 🔄 API接口行为

### 调用方式
```
GET /api/user/getChatRecord
```

### 参数说明
- `shop_id`: 商家ID
  - `0`：获取用户对用户聊天记录
  - `> 0`：获取与指定商家客服的聊天记录
- `page`: 页码（默认1）
- `size`: 每页数量（默认20）

### 返回数据结构

#### 用户对用户聊天 (shop_id = 0)
```json
{
    "code": 1,
    "msg": "获取成功",
    "data": {
        "chat_type": "user_chat",
        "kefu": null,
        "online": [],
        "config": 1,
        "list": [
            {
                "id": 123,
                "from_id": 1,
                "to_id": 2,
                "from_type": "user",
                "to_type": "user",
                "msg": "你好",
                "msg_type": 1,
                "from_nickname": "用户A",
                "from_avatar": "头像URL",
                "create_time": "2024-01-01 12:00:00",
                "create_time_stamp": 1704067200,
                "goods": []
            }
        ],
        "page": 1,
        "size": 20,
        "count": 10,
        "more": false
    }
}
```

#### 客服聊天 (shop_id > 0)
```json
{
    "code": 1,
    "msg": "获取成功", 
    "data": {
        "config": 1,
        "kefu": {
            "id": 1,
            "nickname": "客服小王",
            "avatar": "客服头像URL"
        },
        "record": {
            "list": [...],
            "page": 1,
            "size": 20,
            "count": 15,
            "more": false
        }
    }
}
```

## 🧪 测试接口

为了方便测试，我们创建了专门的测试控制器：

### 测试路由
```
GET /api/chat_test/chat_record?shop_id=0          # 测试用户聊天
GET /api/chat_test/chat_record?shop_id=1          # 测试客服聊天
GET /api/chat_test/user_chat_record               # 直接测试用户聊天
GET /api/chat_test/kefu_chat_record?shop_id=1     # 直接测试客服聊天
```

### 测试步骤
1. **测试用户对用户聊天**：
   ```bash
   curl -H "Authorization: Bearer YOUR_TOKEN" \
        "https://www.huohanghang.cn/api/chat_test/chat_record?shop_id=0"
   ```

2. **测试客服聊天**：
   ```bash
   curl -H "Authorization: Bearer YOUR_TOKEN" \
        "https://www.huohanghang.cn/api/chat_test/chat_record?shop_id=1"
   ```

## ✅ 验证要点

### 功能验证
1. **用户聊天记录**：`shop_id=0` 时只返回用户间聊天记录
2. **客服聊天记录**：`shop_id>0` 时只返回与指定商家客服的聊天记录
3. **数据格式**：两种聊天类型返回的数据结构符合预期
4. **已读状态**：消息正确标记为已读

### 数据验证
1. **用户聊天**：检查 `shop_id=0` 的记录
2. **客服聊天**：检查 `shop_id>0` 的记录
3. **消息类型**：验证不同 `msg_type` 的处理
4. **时间格式**：确认时间戳和格式化时间正确

## 🔧 兼容性说明

### 向后兼容
- 现有的客服聊天功能完全不受影响
- API接口参数和基本返回结构保持一致
- 前端代码无需修改

### 新增功能
- 支持通过 `shop_id=0` 获取用户对用户聊天记录
- 返回数据包含 `chat_type` 字段用于区分聊天类型
- 用户聊天记录包含特殊的标识字段

## 📝 注意事项

1. **权限控制**：确保用户只能获取自己参与的聊天记录
2. **性能优化**：大量聊天记录时考虑分页和索引优化
3. **数据一致性**：确保 `shop_id=0` 的记录确实是用户间聊天
4. **错误处理**：妥善处理数据库查询异常

通过这些修改，现在可以正确区分和处理用户对用户聊天和客服聊天两种不同的场景！
