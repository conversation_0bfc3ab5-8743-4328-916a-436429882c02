<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>功能图标配置</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/static/admin/lib/layui-v2.5.5/css/layui.css" media="all">
</head>
<body>
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-header">
            <h3>功能图标配置管理</h3>
        </div>
        <div class="layui-card-body">
            <div class="layui-row layui-col-space15">
                <div class="layui-col-md12">
                    <div class="layui-btn-group">
                        <button class="layui-btn" id="addBtn">
                            <i class="layui-icon layui-icon-add-1"></i> 添加图标
                        </button>
                    </div>
                </div>
            </div>
            
            <table class="layui-hide" id="iconTable" lay-filter="iconTable"></table>
        </div>
    </div>
</div>

<!-- 操作按钮模板 -->
<script type="text/html" id="toolbarDemo">
    <div class="layui-btn-group">
        <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
        <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
    </div>
</script>

<!-- 状态开关模板 -->
<script type="text/html" id="statusTpl">
    <input type="checkbox" name="status" value="{{d.id}}" lay-skin="switch" lay-text="启用|禁用" lay-filter="statusDemo" {{ d.status == 1 ? 'checked' : '' }}>
</script>

<!-- 图标预览模板 -->
<script type="text/html" id="iconTpl">
    <img src="{{d.icon_url}}" style="width: 32px; height: 32px;" onerror="this.src='/static/admin/images/default-icon.png'">
</script>

<script src="/static/admin/lib/layui-v2.5.5/layui.js"></script>
<script>
layui.use(['table', 'layer', 'form'], function(){
    var table = layui.table;
    var layer = layui.layer;
    var form = layui.form;
    
    // 渲染表格
    table.render({
        elem: '#iconTable',
        url: '{:url("lists")}',
        toolbar: '#toolbarDemo',
        defaultToolbar: ['filter', 'exports', 'print'],
        cols: [[
            {field: 'id', title: 'ID', width: 80, sort: true},
            {field: 'icon_url', title: '图标', width: 100, templet: '#iconTpl'},
            {field: 'icon_name', title: '图标名称', width: 120},
            {field: 'icon_path', title: '跳转路径', width: 200},
            {field: 'auth_name', title: '关联权限', width: 120},
            {field: 'sort_order', title: '排序', width: 80, sort: true},
            {field: 'scope', title: '作用域', width: 100},
            {field: 'status', title: '状态', width: 100, templet: '#statusTpl'},
            {title: '操作', width: 150, toolbar: '#toolbarDemo', align: 'center'}
        ]],
        page: true,
        height: 'full-220'
    });
    
    // 监听工具条
    table.on('tool(iconTable)', function(obj){
        var data = obj.data;
        if(obj.event === 'del'){
            layer.confirm('真的删除这个图标配置吗？', function(index){
                $.post('{:url("del")}', {id: data.id}, function(res){
                    if(res.code == 1){
                        obj.del();
                        layer.close(index);
                        layer.msg('删除成功');
                    } else {
                        layer.msg(res.msg);
                    }
                });
            });
        } else if(obj.event === 'edit'){
            layer.open({
                type: 2,
                title: '编辑图标配置',
                content: '{:url("edit")}?id=' + data.id,
                area: ['600px', '500px'],
                end: function(){
                    table.reload('iconTable');
                }
            });
        }
    });
    
    // 监听状态开关
    form.on('switch(statusDemo)', function(obj){
        var status = obj.elem.checked ? 1 : 0;
        $.post('{:url("status")}', {
            id: this.value,
            status: status
        }, function(res){
            if(res.code != 1){
                layer.msg(res.msg);
                // 恢复开关状态
                obj.elem.checked = !obj.elem.checked;
                form.render('checkbox');
            }
        });
    });
    
    // 添加按钮
    $('#addBtn').click(function(){
        layer.open({
            type: 2,
            title: '添加图标配置',
            content: '{:url("add")}',
            area: ['600px', '500px'],
            end: function(){
                table.reload('iconTable');
            }
        });
    });
});
</script>
</body>
</html>
