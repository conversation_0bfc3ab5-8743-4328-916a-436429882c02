{"version": 3, "file": "pages/user/after_sales/after_sale_details.js", "sources": ["webpack:///./components/price-formate.vue?b158", "webpack:///./components/price-formate.vue?4c8e", "webpack:///./components/price-formate.vue", "webpack:///./components/price-formate.vue?3e0e", "webpack:///./components/price-formate.vue?2b64", "webpack:///./components/price-formate.vue?8e8e", "webpack:///./components/price-formate.vue?32b6", "webpack:///./components/upload.vue?d4ec", "webpack:///./components/upload.vue?cda5", "webpack:///./components/upload.vue?8307", "webpack:///./components/upload.vue?42a2", "webpack:///./components/upload.vue", "webpack:///./components/upload.vue?2a5d", "webpack:///./components/upload.vue?5689", "webpack:///./components/input-Express.vue?85f1", "webpack:///./components/input-Express.vue?cc20", "webpack:///./components/input-Express.vue?9722", "webpack:///./components/input-Express.vue?4bf9", "webpack:///./components/input-Express.vue", "webpack:///./components/input-Express.vue?35c8", "webpack:///./components/input-Express.vue?5971", "webpack:///./pages/user/after_sales/after_sale_details.vue?c868", "webpack:///./pages/user/after_sales/after_sale_details.vue?b233", "webpack:///./pages/user/after_sales/after_sale_details.vue?6d83", "webpack:///./pages/user/after_sales/after_sale_details.vue?2e19", "webpack:///./pages/user/after_sales/after_sale_details.vue", "webpack:///./pages/user/after_sales/after_sale_details.vue?efb8", "webpack:///./pages/user/after_sales/after_sale_details.vue?bb72"], "sourcesContent": ["// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../node_modules/css-loader/dist/cjs.js??ref--3-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--3-oneOf-1-2!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./price-formate.vue?vue&type=style&index=0&lang=css&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"3181fc86\", content, true, context)\n};", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('span',{class:(_vm.lineThrough ? 'line-through' : '') + 'price-format',style:({ color: _vm.color, 'font-weight': _vm.weight })},[_vm._ssrNode(((_vm.showSubscript)?(\"<span\"+(_vm._ssrStyle(null,{\n            'font-size': _vm.subscriptSize + 'px',\n            'margin-right': '1px',\n        }, null))+\">¥</span>\"):\"<!---->\")+\" <span\"+(_vm._ssrStyle(null,{ 'font-size': _vm.firstSize + 'px', 'margin-right': '1px' }, null))+\">\"+_vm._ssrEscape(_vm._s(_vm.priceSlice.first))+\"</span> \"+((_vm.priceSlice.second)?(\"<span\"+(_vm._ssrStyle(null,{ 'font-size': _vm.secondSize + 'px' }, null))+\">\"+_vm._ssrEscape(\".\"+_vm._s(_vm.priceSlice.second))+\"</span>\"):\"<!---->\"))])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nexport default {\n    data() {\n        return {\n            priceSlice: {},\n        }\n    },\n    components: {},\n    props: {\n        firstSize: {\n            type: Number,\n            default: 14,\n        },\n        secondSize: {\n            type: Number,\n            default: 14,\n        },\n        color: {\n            type: String,\n        },\n        weight: {\n            type: [String, Number],\n            default: 400,\n        },\n        price: {\n            type: [String, Number],\n            default: '',\n        },\n        showSubscript: {\n            type: Boolean,\n            default: true,\n        },\n        subscriptSize: {\n            type: Number,\n            default: 14,\n        },\n        lineThrough: {\n            type: Boolean,\n            default: false,\n        },\n    },\n    created() {\n        this.priceFormat()\n    },\n    watch: {\n        price(val) {\n            this.priceFormat()\n        },\n    },\n    methods: {\n        priceFormat() {\n            let { price } = this\n            let priceSlice = {}\n            if (price !== null) {\n                price = parseFloat(price)\n                price = String(price).split('.')\n                priceSlice.first = price[0]\n                priceSlice.second = price[1]\n                this.priceSlice = priceSlice\n            }\n        },\n    },\n}\n", "import mod from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./price-formate.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./price-formate.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./price-formate.vue?vue&type=template&id=0c4d5c85&\"\nimport script from \"./price-formate.vue?vue&type=script&lang=js&\"\nexport * from \"./price-formate.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./price-formate.vue?vue&type=style&index=0&lang=css&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  null,\n  \"7ae24710\"\n  \n)\n\nexport default component.exports", "export * from \"-!../node_modules/vue-style-loader/index.js??ref--3-oneOf-1-0!../node_modules/css-loader/dist/cjs.js??ref--3-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--3-oneOf-1-2!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./price-formate.vue?vue&type=style&index=0&lang=css&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".price-format{display:flex;align-items:baseline}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/sass-resources-loader/lib/loader.js??ref--7-oneOf-1-4!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./upload.vue?vue&type=style&index=0&id=05db7967&lang=scss&scoped=true&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"05ffbf2f\", content, true, context)\n};", "export * from \"-!../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/sass-resources-loader/lib/loader.js??ref--7-oneOf-1-4!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./upload.vue?vue&type=style&index=0&id=05db7967&lang=scss&scoped=true&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".v-upload .el-upload--picture-card[data-v-05db7967]{width:76px;height:76px;line-height:76px}.v-upload .el-upload-list--picture-card .el-upload-list__item[data-v-05db7967]{width:76px;height:76px}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"v-upload\"},[_c('el-upload',{attrs:{\"list-type\":\"picture-card\",\"action\":_vm.url + '/api/file/formimage',\"limit\":_vm.limit,\"on-success\":_vm.success,\"on-error\":_vm.error,\"on-remove\":_vm.remove,\"on-change\":_vm.onChange,\"headers\":{ token: _vm.$store.state.token },\"auto-upload\":_vm.autoUpload}},[(_vm.isSlot)?_vm._t(\"default\"):_c('div',[_c('div',{staticClass:\"muted xs\"},[_vm._v(\"上传图片\")])])],2)],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport config from '~/config/app'\nexport default {\n    components: {},\n    props: {\n        limit: {\n            type: Number,\n            default: 1,\n        },\n        isSlot: {\n            type: Boolean,\n            default: false,\n        },\n        autoUpload: {\n            type: Boolean,\n            default: true,\n        },\n        onChange: {\n            type: Function,\n            default: () => {},\n        },\n    },\n    watch: {},\n    data() {\n        return {\n            url: config.baseUrl,\n        }\n    },\n    created() {},\n    computed: {},\n    methods: {\n        success(res, file, fileList) {\n            if (!this.autoUpload) {\n                return\n            }\n            this.$message({\n                message: '上传成功',\n                type: 'success',\n            })\n            this.$emit('success', fileList)\n        },\n        remove(file, fileList) {\n            this.$emit('remove', fileList)\n        },\n        error(res) {\n            this.$message({\n                message: '上传失败，请重新上传',\n                type: 'error',\n            })\n        },\n    },\n}\n", "import mod from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./upload.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./upload.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./upload.vue?vue&type=template&id=05db7967&scoped=true&\"\nimport script from \"./upload.vue?vue&type=script&lang=js&\"\nexport * from \"./upload.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./upload.vue?vue&type=style&index=0&id=05db7967&lang=scss&scoped=true&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  \"05db7967\",\n  \"388748c3\"\n  \n)\n\nexport default component.exports", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/sass-resources-loader/lib/loader.js??ref--7-oneOf-1-4!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./input-Express.vue?vue&type=style&index=0&id=13601821&lang=scss&scoped=true&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"5eb5ac17\", content, true, context)\n};", "export * from \"-!../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/sass-resources-loader/lib/loader.js??ref--7-oneOf-1-4!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./input-Express.vue?vue&type=style&index=0&id=13601821&lang=scss&scoped=true&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".input-express .dialog-footer[data-v-13601821]{text-align:center}.input-express .dialog-footer .el-button[data-v-13601821]{width:160px}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"input-express\"},[_c('el-dialog',{attrs:{\"title\":\"填写快递单号\",\"visible\":_vm.showDialog,\"width\":\"926px\"},on:{\"update:visible\":function($event){_vm.showDialog=$event}}},[_c('el-form',{ref:\"inputForm\",attrs:{\"inline\":\"\",\"label-width\":\"100px\",\"model\":_vm.form,\"rules\":_vm.rules}},[_c('el-form-item',{attrs:{\"label\":\"物流公司：\",\"prop\":\"business\"}},[_c('el-input',{attrs:{\"size\":\"small\",\"placeholder\":\"请输入物流公司名称\"},model:{value:(_vm.form.business),callback:function ($$v) {_vm.$set(_vm.form, \"business\", $$v)},expression:\"form.business\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"快递单号：\",\"prop\":\"number\"}},[_c('el-input',{attrs:{\"size\":\"small\",\"placeholder\":\"请输入快递单号\"},model:{value:(_vm.form.number),callback:function ($$v) {_vm.$set(_vm.form, \"number\", $$v)},expression:\"form.number\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"备注说明：\",\"prop\":\"desc\"}},[_c('el-input',{staticStyle:{\"width\":\"632px\"},attrs:{\"type\":\"textarea\",\"placeholder\":\"请输入详细内容，选填\",\"resize\":\"none\",\"rows\":\"5\"},model:{value:(_vm.form.desc),callback:function ($$v) {_vm.$set(_vm.form, \"desc\", $$v)},expression:\"form.desc\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"上传凭证：\",\"prop\":\"upload\"}},[_c('div',{staticClass:\"xs muted\"},[_vm._v(\"请上传快递单号凭证，选填\")]),_vm._v(\" \"),_c('upload',{attrs:{\"isSlot\":\"\",\"file-list\":_vm.fileList,\"limit\":3},on:{\"success\":_vm.uploadSuccess}},[_c('div',{staticClass:\"column-center\",staticStyle:{\"height\":\"100%\"}},[_c('i',{staticClass:\"el-icon-camera xs\",staticStyle:{\"font-size\":\"24px\"}})])])],1)],1),_vm._v(\" \"),_c('div',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{attrs:{\"type\":\"primary\"},on:{\"click\":_vm.submitForm}},[_vm._v(\"确定\")]),_vm._v(\" \"),_c('el-button',{on:{\"click\":function($event){_vm.showDialog = false}}},[_vm._v(\"取消\")])],1)],1)],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nexport default {\n    components: {\n    },\n    data() {\n        return {\n            showDialog: false,\n            form: {\n                // 物流公司\n                business: \"\",\n                // 快递单号\n                number: \"\",\n                // 详细内容\n                desc: \"\",\n            },\n            rules: {\n                business: [{ required: true, message: \"请输入物流公司\" }],\n                number: [{ required: true, message: \"请输入快递单号\" }],\n            },\n            fileList: [],\n        };\n    },\n    props: {\n        value: {\n            type: Boolean,\n            default: false,\n        },\n        aid: {\n            type: [String, Number],\n            default: -1,\n        },\n    },\n    methods: {\n        submitForm() {\n            console.log(this.$refs);\n            this.$refs[\"inputForm\"].validate(async (valid) => {\n                if (valid) {\n                    let fileList = [];\n                    this.fileList.forEach((item) => {\n                        fileList.push(item.response.data);\n                    });\n                    let data = {\n                        id: this.aid,\n                        express_name: this.form.business,\n                        invoice_no: this.form.number,\n                        express_remark: this.form.desc,\n                        express_image:\n                            fileList.length <= 0 ? \"\" : fileList[0].base_url,\n                    };\n                    let res = await this.$post(\"after_sale/express\", data);\n                    if (res.code == 1) {\n                        this.$message({\n                            message: \"提交成功\",\n                            type: \"success\",\n                        });\n                        this.showDialog = false;\n                        this.$emit(\"success\");\n                    }\n                } else {\n                    return false;\n                }\n            });\n        },\n        uploadSuccess(e) {\n            let fileList = Object.assign([], e);\n            this.fileList = fileList;\n        },\n    },\n    watch: {\n        value(val) {\n            this.showDialog = val;\n        },\n        showDialog(val) {\n            this.$emit(\"input\", val);\n        },\n    },\n};\n", "import mod from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./input-Express.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./input-Express.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./input-Express.vue?vue&type=template&id=13601821&scoped=true&\"\nimport script from \"./input-Express.vue?vue&type=script&lang=js&\"\nexport * from \"./input-Express.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./input-Express.vue?vue&type=style&index=0&id=13601821&lang=scss&scoped=true&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  \"13601821\",\n  \"6e88187b\"\n  \n)\n\nexport default component.exports\n\n/* nuxt-component-imports */\ninstallComponents(component, {Upload: require('/Users/<USER>/Desktop/vue/pc/components/upload.vue').default})\n", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../../../node_modules/sass-resources-loader/lib/loader.js??ref--7-oneOf-1-4!../../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./after_sale_details.vue?vue&type=style&index=0&id=7daeee73&lang=scss&scoped=true&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../../../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"f311a1f4\", content, true, context)\n};", "export * from \"-!../../../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../../../node_modules/sass-resources-loader/lib/loader.js??ref--7-oneOf-1-4!../../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./after_sale_details.vue?vue&type=style&index=0&id=7daeee73&lang=scss&scoped=true&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../../../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".apply-detail[data-v-7daeee73]{padding:10px}.apply-detail .apply-detail-header[data-v-7daeee73]{padding:15px 0;border-bottom:1px solid #e5e5e5}.apply-detail .apply-detail-address[data-v-7daeee73]{margin:0 10px;padding-top:16px;border-top:1px solid #e5e5e5}.apply-detail .apply-detail-address .copy[data-v-7daeee73]{margin-left:20px;padding:2px 6px;color:#ff2c3c;background-color:rgba(255,44,60,.2)}.apply-detail .result-content[data-v-7daeee73]{padding:24px 20px}.apply-detail .result-content .result-item[data-v-7daeee73]{margin-bottom:16px}.apply-detail .result-content .result-item:not(:last-of-type) .label[data-v-7daeee73]{width:82px;align-self:flex-start;text-align:right}.apply-detail .result-content .result-item:not(:last-of-type) .label[data-v-7daeee73]:before{content:\\\"* \\\";color:red}.apply-detail .result-content .result-item .label[data-v-7daeee73]{width:82px;align-self:flex-start;text-align:right}.apply-detail .result-content .result-item .desc[data-v-7daeee73]{margin-left:24px;width:680px}.apply-detail .apply-detail-content .btn-group .apply-btn[data-v-7daeee73]{border:1px solid #ccc;border-radius:2px;width:100px;height:32px;align-self:flex-start;margin-right:10px}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"apply-detail\"},[_vm._ssrNode(\"<div class=\\\"apply-detail-content\\\" data-v-7daeee73>\",\"</div>\",[_c('el-table',{staticStyle:{\"width\":\"100%\"},attrs:{\"data\":_vm.lists.order_goods}},[_c('el-table-column',{attrs:{\"prop\":\"date\",\"label\":\"商品信息\",\"max-width\":\"180\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('div',{staticClass:\"flex\"},[_c('el-image',{staticStyle:{\"width\":\"80px\",\"height\":\"80px\"},attrs:{\"src\":scope.row.image,\"fit\":\"fit\"}}),_vm._v(\" \"),_c('div',{staticClass:\"m-l-10\"},[_c('div',{staticClass:\"line-2\"},[_vm._v(\"\\n                                \"+_vm._s(scope.row.goods_name)+\"\\n                            \")]),_vm._v(\" \"),_c('div',[_vm._v(\"\\n                                \"+_vm._s(scope.row.spec_value)+\"\\n                            \")])])],1)]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"name\",\"label\":\"价格\",\"width\":\"180\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_vm._v(\"\\n                    ¥\"+_vm._s(scope.row.goods_price)+\"\\n                \")]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"goods_num\",\"label\":\"数量\",\"width\":\"180\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"address\",\"label\":\"申请状态\",\"width\":\"180\"}},[[_vm._v(_vm._s(_vm.lists.status_text))]],2)],1),_vm._ssrNode(\" \"),_vm._ssrNode(\"<div class=\\\"m-t-30\\\" style=\\\"padding: 0 20px\\\" data-v-7daeee73>\",\"</div>\",[_vm._ssrNode(\"<div class=\\\"result-content\\\" data-v-7daeee73>\",\"</div>\",[_vm._ssrNode(\"<div class=\\\"result-item flex\\\" data-v-7daeee73><div class=\\\"label\\\" data-v-7daeee73>退款类型:</div> <div class=\\\"desc\\\" data-v-7daeee73>\"+_vm._ssrEscape(_vm._s(_vm.lists.refund_type_text))+\"</div></div> <div class=\\\"result-item flex\\\" data-v-7daeee73><div class=\\\"label\\\" data-v-7daeee73>退款原因:</div> <div class=\\\"desc\\\" data-v-7daeee73>\"+_vm._ssrEscape(_vm._s(_vm.lists.refund_reason))+\"</div></div> \"),_vm._ssrNode(\"<div class=\\\"result-item flex\\\" data-v-7daeee73>\",\"</div>\",[_vm._ssrNode(\"<div class=\\\"label\\\" data-v-7daeee73>退款金额:</div> \"),_vm._ssrNode(\"<div class=\\\"desc\\\" data-v-7daeee73>\",\"</div>\",[_c('price-formate',{attrs:{\"price\":_vm.lists.refund_price,\"showSubscript\":\"\",\"color\":\"red\"}})],1)],2),_vm._ssrNode(\" <div class=\\\"result-item flex\\\" data-v-7daeee73><div class=\\\"label\\\" data-v-7daeee73>申请时间:</div> <div class=\\\"desc\\\" data-v-7daeee73>\"+_vm._ssrEscape(_vm._s(_vm.lists.create_time))+\"</div></div> <div class=\\\"result-item flex\\\" data-v-7daeee73><div class=\\\"label\\\" data-v-7daeee73>退款编号:</div> <div class=\\\"desc\\\" data-v-7daeee73>\"+_vm._ssrEscape(_vm._s(_vm.lists.sn))+\"</div></div> \"),_vm._ssrNode(\"<div class=\\\"result-item flex\\\" data-v-7daeee73>\",\"</div>\",[_vm._ssrNode(\"<div class=\\\"label\\\" data-v-7daeee73>退款说明:</div> \"),_vm._ssrNode(\"<div class=\\\"column desc\\\" data-v-7daeee73>\",\"</div>\",[_vm._ssrNode(\"<div class=\\\"m-b-16\\\" data-v-7daeee73></div> \"),(_vm.lists.refund_image)?_c('el-image',{staticStyle:{\"width\":\"76px\",\"height\":\"76px\"},attrs:{\"src\":_vm.lists.refund_image,\"preview-src-list\":[_vm.lists.refund_image]}}):_vm._e()],2)],2)],2)]),_vm._ssrNode(\" \"+((_vm.lists.refund_type_text == '退款退货' && _vm.lists.status == 2)?(\"<div class=\\\"apply-detail-address flex\\\" data-v-7daeee73>\"+_vm._ssrEscape(\"\\n            退货地址：\"+_vm._s(_vm.lists.shop.contact||'-')+\",\"+_vm._s(_vm.lists.shop.mobile||'-')+\", \"+_vm._s(_vm.lists.shop.address||'-')+\"\\n            \")+\"<div class=\\\"copy pointer\\\" data-v-7daeee73>复制</div></div>\"):\"<!---->\")+\" \"),_vm._ssrNode(\"<div class=\\\"btn-group flex row-center m-t-60\\\" data-v-7daeee73>\",\"</div>\",[_c('el-popconfirm',{attrs:{\"title\":\"确定撤销商品吗？\",\"confirm-button-text\":\"确定\",\"cancel-button-text\":\"取消\",\"icon\":\"el-icon-info\",\"icon-color\":\"red\"},on:{\"confirm\":function($event){return _vm.cancelApply(_vm.lists.id)}}},[(_vm.lists.status!=6)?_c('el-button',{staticClass:\"apply-btn flex row-center sm\",attrs:{\"slot\":\"reference\",\"size\":\"small\"},slot:\"reference\"},[_vm._v(\"撤销申请\")]):_vm._e()],1),_vm._ssrNode(\" \"),(_vm.lists.status==2)?_c('el-button',{staticClass:\"apply-btn flex row-center sm\",attrs:{\"size\":\"small\"},on:{\"click\":function($event){_vm.showInput=true}}},[_vm._v(\"填写快递单号\")]):_vm._e()],2)],2),_vm._ssrNode(\" \"),_c('input-express',{attrs:{\"aid\":_vm.lists.id},on:{\"success\":_vm.getDetail},model:{value:(_vm.showInput),callback:function ($$v) {_vm.showInput=$$v},expression:\"showInput\"}})],2)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nexport default {\n    head() {\n        return {\n            title: this.$store.getters.headTitle,\n            link: [\n                {\n                    rel: \"icon\",\n                    type: \"image/x-icon\",\n                    href: this.$store.getters.favicon,\n                },\n            ],\n        };\n    },\n    layout: \"user\",\n\n    data() {\n        return {\n            lists: {\n                order_goods: [],\n                shop: {},\n            },\n            showInput: false,\n        };\n    },\n\n    mounted() {\n        this.getDetail();\n    },\n\n    methods: {\n        async getDetail() {\n            let res = await this.$get(\"after_sale/detail\", {\n                params: {\n                    id: this.$route.query.afterSaleId,\n                },\n            });\n            if (res.code == 1) {\n                let goods = [res.data.order_goods];\n                res.data.order_goods = goods;\n                console.log(goods);\n                this.lists = res.data;\n            }\n        },\n\n        onCopy() {\n            // this.deliverOrder.invoice_no;\n            let oInput = document.createElement(\"input\");\n            oInput.value = this.lists.shop.address;\n            document.body.appendChild(oInput);\n            oInput.select();\n            document.execCommand(\"Copy\");\n            this.$message.success(\"复制成功\");\n            oInput.remove();\n        },\n\n        async cancelApply(afterSaleId) {\n            let res = await this.$post(\"after_sale/cancel\", {\n                id: afterSaleId,\n            });\n            if (res.code == 1) {\n                this.$message({\n                    message: res.msg,\n                    type: \"success\",\n                });\n                setTimeout(() => {\n                    this.$router.go(-1);\n                }, 500);\n            }\n        },\n\n        goRefund(afterSaleId, orderId, itemId) {\n            this.$router.push(\n                \"/user/after_sales/apply_result?afterSaleId=\" +\n                    afterSaleId +\n                    \"&order_id=\" +\n                    orderId +\n                    \"&item_id=\" +\n                    itemId\n            );\n        },\n    },\n};\n", "import mod from \"-!../../../node_modules/babel-loader/lib/index.js??ref--2-0!../../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./after_sale_details.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/babel-loader/lib/index.js??ref--2-0!../../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./after_sale_details.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./after_sale_details.vue?vue&type=template&id=7daeee73&scoped=true&\"\nimport script from \"./after_sale_details.vue?vue&type=script&lang=js&\"\nexport * from \"./after_sale_details.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./after_sale_details.vue?vue&type=style&index=0&id=7daeee73&lang=scss&scoped=true&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  \"7daeee73\",\n  \"3dc04b56\"\n  \n)\n\nexport default component.exports\n\n/* nuxt-component-imports */\ninstallComponents(component, {PriceFormate: require('/Users/<USER>/Desktop/vue/pc/components/price-formate.vue').default,InputExpress: require('/Users/<USER>/Desktop/vue/pc/components/input-Express.vue').default})\n"], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACXA;AACA;AACA;AACA;AACA;AACA;;;;;ACLA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AADA;AAGA;AACA;AAAA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AADA;AAGA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AA5BA;AACA;AAgCA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAJA;AAKA;AACA;AACA;AAAA;AAAA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAZA;AAhDA;;AC1BA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACvBA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACNA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACXA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACNA;AACA;AACA;;;;;;;;ACFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAbA;AAkBA;AACA;AAAA;AACA;AACA;AADA;AAGA;AACA;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAFA;AAIA;AACA;AApBA;AA5BA;;ACvBA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;ACvBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACXA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACNA;AACA;AACA;;;;;ACFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AANA;AAQA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAFA;AAIA;AAdA;AAgBA;AACA;AAAA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AALA;AAUA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AALA;AAQA;AACA;AAAA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAnCA;AAoCA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAPA;AAnEA;;AChCA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AC1BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACXA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACNA;AACA;AACA;;;;;ACFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAHA;AAHA;AAUA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAFA;AAIA;AALA;AAOA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AADA;AADA;AACA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AADA;AACA;AAEA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAQA;AACA;AAnDA;AA7BA;;ACtFA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;A", "sourceRoot": ""}