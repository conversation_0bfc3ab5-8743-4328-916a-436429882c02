{"scope": "alipay", "name": "easysdk-marketing-templatemessage", "version": "0.0.1", "main": "./main.tea", "java": {"package": "com.alipay.easysdk.marketing.templatemessage", "baseClient": "com.alipay.easysdk.kernel.BaseClient"}, "csharp": {"namespace": "Alipay.EasySDK.Marketing.TemplateMessage", "baseClient": "Alipay.EasySDK.Kernel:BaseClient"}, "typescript": {"baseClient": "@alipay/easysdk-baseclient"}, "php": {"package": "Alipay.EasySDK.Marketing.TemplateMessage"}, "go": {"namespace": "marketing/templatemessage"}, "libraries": {"EasySDKKernel": "alipay:easysdk-kernel:*"}}