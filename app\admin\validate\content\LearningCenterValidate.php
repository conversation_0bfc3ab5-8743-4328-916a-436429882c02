<?php

namespace app\admin\validate\content;

use app\common\basics\Validate;

class LearningCenterValidate extends Validate
{
    protected $rule = [
        'id'          => 'require|number',
        'category_id' => 'require|number',
        'title'       => 'require|max:255',
        'image'       => 'max:255',
        'video'       => 'max:255',
        'content'     => 'max:65535',
        'sort'        => 'number',
        'is_show'     => 'require|in:0,1',
        'status'      => 'require|in:0,1'
    ];

    protected $message = [
        'id.require'          => 'ID不能为空',
        'id.number'          => 'ID必须为数字',
        'category_id.require' => '分类不能为空',
        'category_id.number' => '分类ID必须为数字',
        'title.require'      => '标题不能为空',
        'title.max'          => '标题最多不能超过255个字符',
        'image.max'          => '图片路径最多不能超过255个字符',
        'video.max'          => '视频路径最多不能超过255个字符',
        'content.max'        => '内容超出限制',
        'sort.number'        => '排序必须为数字',
        'is_show.require'    => '是否显示不能为空',
        'is_show.in'         => '是否显示参数错误',
        'status.require'     => '状态不能为空',
        'status.in'          => '状态参数错误'
    ];

    protected $scene = [
        'id'     => ['id'],
        'add'    => ['category_id', 'title', 'image', 'video', 'content', 'sort', 'is_show'],
        'edit'   => ['id', 'category_id', 'title', 'image', 'video', 'content', 'sort', 'is_show'],
        'status' => ['id', 'status']
    ];
}