<?php

namespace app\admin\controller\goods;

use app\common\basics\AdminBase;
use app\admin\logic\goods\QualificationLogic;
use app\admin\validate\goods\QualificationValidate;
use think\exception\ValidateException;
use app\common\server\JsonServer;
use app\common\model\goods\Qualification as QualificationModel;
use think\facade\View;

/**
 * 资质管理
 * Class Qualification
 * @package app\admin\controller\goods
 */
class Qualification extends AdminBase
{
    /**
     * 列表
     */
    public function lists()
    {
        if ($this->request->isAjax()) {
            $get = $this->request->get();
            $data = QualificationLogic::lists($get);
            return JsonServer::success('获取列表成功', $data);
        }
        return view();
    }

    /**
     * 搜索资质（用于分类关联）
     */
    public function search()
    {
        $get = $this->request->get();
        $keyword = $get['keyword'] ?? '';
        $data = QualificationLogic::search($keyword);
        return JsonServer::success('搜索成功', $data);
    }

    /**
     * 添加
     */
    public function add()
    {
        if ($this->request->isAjax()) {
            $post = $this->request->post();
            $post['del'] = 0;
            try {
                validate(QualificationValidate::class)->scene('add')->check($post);
            } catch (ValidateException $e) {
                return JsonServer::error($e->getError());
            }
            $res = QualificationLogic::add($post);
            if ($res) {
                return JsonServer::success('资质添加成功');
            } else {
                return JsonServer::error('资质添加失败');
            }
        }
        return view();
    }

    /**
     * 编辑
     */
    public function edit()
    {
        if ($this->request->isAjax()) {
            $post = $this->request->post();
            try {
                validate(QualificationValidate::class)->scene('edit')->check($post);
            } catch (ValidateException $e) {
                return JsonServer::error($e->getError());
            }
            $res = QualificationLogic::edit($post);
            if ($res) {
                return JsonServer::success('资质编辑成功');
            } else {
                return JsonServer::error('资质编辑失败');
            }
        }

        $id = $this->request->get('id');
        $detail = QualificationLogic::detail($id);
        return view('edit', ['detail' => $detail]);
    }

    /**
     * 删除
     */
    public function del()
    {
        $id = $this->request->post('id');
        $res = QualificationLogic::del($id);
        if ($res) {
            return JsonServer::success('资质删除成功');
        } else {
            return JsonServer::error('资质删除失败');
        }
    }

    /**
     * 状态切换
     */
    public function switchStatus()
    {
        $post = $this->request->post();
        QualificationModel::update(['status' => $post['status']],['id' => $post['id']]);
        return JsonServer::success('操作成功');
    }

    /**
     * 绑定分类页面
     */
    public function bindCategories()
    {
        $id = $this->request->get('id');
        $qualification = QualificationModel::find($id);

        if (!$qualification) {
            return JsonServer::error('资质不存在');
        }

        // 获取分类树数据
        $categoryTree = QualificationLogic::getCategoryTree($id);

        // 调试信息
        \think\facade\Log::info('分类树数据: ' . json_encode($categoryTree));

        // 确保JSON编码正确
        $categoryTreeJson = json_encode($categoryTree, JSON_UNESCAPED_UNICODE);
        if (json_last_error() !== JSON_ERROR_NONE) {
            $categoryTreeJson = '[]';
        }

        View::assign('qualification', $qualification);
        View::assign('categoryTree', $categoryTreeJson);
        return View::fetch('bind_categories');
    }

    /**
     * 获取分类树数据（AJAX）
     */
    public function getCategoriesTree()
    {
        $qualificationId = $this->request->param('qualification_id');

        try {
            // 获取分类树数据
            $categoryTree = QualificationLogic::getCategoryTree($qualificationId);

            return JsonServer::success('获取成功', $categoryTree);
        } catch (\Exception $e) {
            return JsonServer::error('获取失败：' . $e->getMessage());
        }
    }

    /**
     * 保存绑定关系
     */
    public function saveBinding()
    {
        $id = $this->request->post('id');
        $categoryIds = $this->request->post('category_ids', []);

        if (!$id) {
            return JsonServer::error('资质ID不能为空');
        }

        $result = QualificationLogic::saveBinding($id, $categoryIds);

        if ($result) {
            return JsonServer::success('绑定成功');
        } else {
            return JsonServer::error('绑定失败');
        }
    }

    /**
     * 切换是否必传状态
     */
    public function switchRequired()
    {
        $id = $this->request->post('id');
        $is_required = $this->request->post('is_required');

        if (empty($id)) {
            return JsonServer::error('参数错误');
        }

        $result = QualificationLogic::switchRequired($id, $is_required);

        if ($result) {
            return JsonServer::success('切换成功');
        } else {
            return JsonServer::error('切换失败');
        }
    }


}
