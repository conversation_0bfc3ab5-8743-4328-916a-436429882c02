<?php

namespace app\shop\controller\activity_area;

use app\common\basics\ShopBase;
use app\common\server\JsonServer;
use think\facade\View;
use app\shop\logic\activity_area\AreaLogic;
use app\shop\logic\activity_area\GoodsLogic;
use app\shop\validate\activity_area\ActivityGoodsValidate;

/**
 * Class Goods
 * @package app\shop\controller\activity_area
 */
class Goods extends ShopBase
{

    /**
     * @notes 活动专区商品列表
     * @return \think\response\Json|\think\response\View
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date 2021/7/14 10:12 上午
     */
    public function lists()
    {

        $shop_id = $this->shop_id;
        if ($this->request->isAjax()) {
            $get = $this->request->get();
            $get['shop_id'] = $shop_id;
            $list = GoodsLogic::lists($get);
            return JsonServer::success('获取成功', $list);
        }
        $activity_area = AreaLogic::getActivityAreaAll();
        $num = GoodsLogic::getNum($shop_id);
        View::assign('num', $num);
        View::assign('activity_area', $activity_area);
        return View();
    }

    /**
     * @notes 新增活动专区商品
     * @return \think\response\Json|\think\response\View
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date 2021/7/14 10:12 上午
     */
    public function add()
    {

        if ($this->request->isAjax()) {
            $post = $this->request->post();
            $shop_id = $this->shop_id;
            $post['shop_id'] = $shop_id;
            $post['del'] = 0;

            (new ActivityGoodsValidate())->goCheck('add', $post);
            GoodsLogic::add($post);
            return JsonServer::success('添加成功');
        }
        $activity_area = AreaLogic::getActivityAreaAll();
        View::assign('activity_area', $activity_area);
        return View();
    }

    /**
     * @notes 编辑活动商品
     * @return \think\response\Json|\think\response\View
     * <AUTHOR>
     * @date 2021/7/14 10:12 上午
     */
    public function edit()
    {

        if ($this->request->isAjax()) {
            $post = $this->request->post();
            $post['del'] = 0;
            (new ActivityGoodsValidate())->goCheck('edit', $post);
            $result = GoodsLogic::edit($post);
            if ($result) {
                return JsonServer::success('编辑成功');
            }
            return JsonServer::error('编辑失败');
        }
        $goods_id = $this->request->get('goods_id');
        $activity_id = $this->request->get('activity_id');
        View::assign('activity_list', GoodsLogic::getActivityList());
        View::assign('info', GoodsLogic::getActivityGoods($goods_id, $activity_id));
        return View();
    }

    /**
     * @notes 删除活动商品
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2021/7/14 10:12 上午
     */
    public function del()
    {

        $id = $this->request->post('id', '', 'intval');
        $result = GoodsLogic::del($id);
        if ($result == true) {
            return JsonServer::success('删除成功');
        }
        return JsonServer::error('删除失败');
    }

}
