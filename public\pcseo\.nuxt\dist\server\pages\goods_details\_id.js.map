{"version": 3, "file": "pages/goods_details/_id.js", "sources": ["webpack:///./components/price-formate.vue?b158", "webpack:///./components/price-formate.vue?4c8e", "webpack:///./components/price-formate.vue", "webpack:///./components/price-formate.vue?3e0e", "webpack:///./components/price-formate.vue?2b64", "webpack:///./components/price-formate.vue?8e8e", "webpack:///./components/price-formate.vue?32b6", "webpack:///./components/null-data.vue?48f8", "webpack:///./components/null-data.vue?97fe", "webpack:///./components/null-data.vue?fba4", "webpack:///./components/null-data.vue?cbf9", "webpack:///./components/null-data.vue", "webpack:///./components/null-data.vue?da63", "webpack:///./components/null-data.vue?475d", "webpack:///./components/count-down.vue?4f61", "webpack:///./utils/parseTime.js", "webpack:///./components/count-down.vue", "webpack:///./components/count-down.vue?a8c1", "webpack:///./components/count-down.vue?1b2a", "webpack:///./static/images/news_null.png", "webpack:///./components/number-box.vue?64ab", "webpack:///./components/number-box.vue?e931", "webpack:///./components/number-box.vue?4591", "webpack:///./components/comment-list.vue?05e4", "webpack:///./components/number-box.vue?c46e", "webpack:///./components/number-box.vue", "webpack:///./components/number-box.vue?9baa", "webpack:///./components/number-box.vue?bc32", "webpack:///./components/comment-list.vue?fe21", "webpack:///./components/comment-list.vue?7e6b", "webpack:///./pages/goods_details/_id.vue?12ef", "webpack:///./components/comment-list.vue?6de6", "webpack:///./components/comment-list.vue", "webpack:///./components/comment-list.vue?c5eb", "webpack:///./components/comment-list.vue?f9e4", "webpack:///./static/images/icon_star_s.png", "webpack:///./static/images/icon_star.png", "webpack:///./pages/goods_details/_id.vue?a5b1", "webpack:///./pages/goods_details/_id.vue?8d7f", "webpack:///./pages/goods_details/_id.vue?1cf7", "webpack:///./pages/goods_details/_id.vue", "webpack:///./pages/goods_details/_id.vue?e7b3", "webpack:///./pages/goods_details/_id.vue?fc67"], "sourcesContent": ["// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../node_modules/css-loader/dist/cjs.js??ref--3-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--3-oneOf-1-2!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./price-formate.vue?vue&type=style&index=0&lang=css&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"3181fc86\", content, true, context)\n};", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('span',{class:(_vm.lineThrough ? 'line-through' : '') + 'price-format',style:({ color: _vm.color, 'font-weight': _vm.weight })},[_vm._ssrNode(((_vm.showSubscript)?(\"<span\"+(_vm._ssrStyle(null,{\n            'font-size': _vm.subscriptSize + 'px',\n            'margin-right': '1px',\n        }, null))+\">¥</span>\"):\"<!---->\")+\" <span\"+(_vm._ssrStyle(null,{ 'font-size': _vm.firstSize + 'px', 'margin-right': '1px' }, null))+\">\"+_vm._ssrEscape(_vm._s(_vm.priceSlice.first))+\"</span> \"+((_vm.priceSlice.second)?(\"<span\"+(_vm._ssrStyle(null,{ 'font-size': _vm.secondSize + 'px' }, null))+\">\"+_vm._ssrEscape(\".\"+_vm._s(_vm.priceSlice.second))+\"</span>\"):\"<!---->\"))])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nexport default {\n    data() {\n        return {\n            priceSlice: {},\n        }\n    },\n    components: {},\n    props: {\n        firstSize: {\n            type: Number,\n            default: 14,\n        },\n        secondSize: {\n            type: Number,\n            default: 14,\n        },\n        color: {\n            type: String,\n        },\n        weight: {\n            type: [String, Number],\n            default: 400,\n        },\n        price: {\n            type: [String, Number],\n            default: '',\n        },\n        showSubscript: {\n            type: Boolean,\n            default: true,\n        },\n        subscriptSize: {\n            type: Number,\n            default: 14,\n        },\n        lineThrough: {\n            type: Boolean,\n            default: false,\n        },\n    },\n    created() {\n        this.priceFormat()\n    },\n    watch: {\n        price(val) {\n            this.priceFormat()\n        },\n    },\n    methods: {\n        priceFormat() {\n            let { price } = this\n            let priceSlice = {}\n            if (price !== null) {\n                price = parseFloat(price)\n                price = String(price).split('.')\n                priceSlice.first = price[0]\n                priceSlice.second = price[1]\n                this.priceSlice = priceSlice\n            }\n        },\n    },\n}\n", "import mod from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./price-formate.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./price-formate.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./price-formate.vue?vue&type=template&id=0c4d5c85&\"\nimport script from \"./price-formate.vue?vue&type=script&lang=js&\"\nexport * from \"./price-formate.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./price-formate.vue?vue&type=style&index=0&lang=css&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  null,\n  \"7ae24710\"\n  \n)\n\nexport default component.exports", "export * from \"-!../node_modules/vue-style-loader/index.js??ref--3-oneOf-1-0!../node_modules/css-loader/dist/cjs.js??ref--3-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--3-oneOf-1-2!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./price-formate.vue?vue&type=style&index=0&lang=css&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".price-format{display:flex;align-items:baseline}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/sass-resources-loader/lib/loader.js??ref--7-oneOf-1-4!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./null-data.vue?vue&type=style&index=0&id=93598fb0&lang=scss&scoped=true&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"12a18d22\", content, true, context)\n};", "export * from \"-!../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/sass-resources-loader/lib/loader.js??ref--7-oneOf-1-4!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./null-data.vue?vue&type=style&index=0&id=93598fb0&lang=scss&scoped=true&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".null-data[data-v-93598fb0]{padding:100px}.null-data .img-null[data-v-93598fb0]{width:150px;height:150px}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"bg-white flex-col col-center null-data\"},[_vm._ssrNode(\"<img\"+(_vm._ssrAttr(\"src\",_vm.img))+\" alt class=\\\"img-null\\\"\"+(_vm._ssrStyle(null,_vm.imgStyle, null))+\" data-v-93598fb0> <div class=\\\"muted mt8\\\" data-v-93598fb0>\"+_vm._ssrEscape(_vm._s(_vm.text))+\"</div>\")])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n\nexport default {\n    components: {},\n    props: {\n        img: {\n            type: String,\n        },\n        text: {\n            type: String,\n            default: '暂无数据',\n        },\n        imgStyle: {\n            type: String,\n            default: '',\n        },\n    },\n    methods: {},\n}\n", "import mod from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./null-data.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./null-data.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./null-data.vue?vue&type=template&id=93598fb0&scoped=true&\"\nimport script from \"./null-data.vue?vue&type=script&lang=js&\"\nexport * from \"./null-data.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./null-data.vue?vue&type=style&index=0&id=93598fb0&lang=scss&scoped=true&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  \"93598fb0\",\n  \"728f99de\"\n  \n)\n\nexport default component.exports", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return (_vm.time >= 0)?_c('div',[_c('client-only',[(_vm.isSlot)?_vm._t(\"default\"):_c('span',[_vm._v(_vm._s(_vm.formateTime))])],2)],1):_vm._e()}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "\n\nconst SECOND = 1000;\nconst MINUTE = 60 * SECOND;\nconst HOUR = 60 * MINUTE;\nconst DAY = 24 * HOUR;\nexport function parseTimeData(time) {\n    const days = Math.floor(time / DAY);\n    const hours = sliceTwo(Math.floor((time % DAY) / HOUR));\n    const minutes = sliceTwo(Math.floor((time % HOUR) / MINUTE));\n    const seconds = sliceTwo(Math.floor((time % MINUTE) / SECOND));\n    return {\n        days: days,\n        hours: hours,\n        minutes: minutes,\n        seconds: seconds,\n    };\n}\n\nfunction sliceTwo(str) {\n    return (0 + str.toString()).slice(-2)\n}\n\nexport  function parseFormat(format, timeData) {\n    let days = timeData.days;\n    let hours = timeData.hours, minutes = timeData.minutes, seconds = timeData.seconds\n    if (format.indexOf('dd') !== -1) {\n        format = format.replace('dd', days);\n    }\n    if (format.indexOf('hh') !== -1) {\n        format = format.replace('hh', sliceTwo(hours) );\n    }\n    if (format.indexOf('mm') !== -1) {\n        format = format.replace('mm', sliceTwo(minutes));\n    }\n    if (format.indexOf('ss') !== -1) {\n        format = format.replace('ss', sliceTwo(seconds));\n    }\n    return format\n}", "//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { parseTimeData, parseFormat } from '~/utils/parseTime'\nexport default {\n    components: {},\n    props: {\n        isSlot: {\n            type: Boolean,\n            default: false,\n        },\n        time: {\n            type: Number,\n            default: 0,\n        },\n        format: {\n            type: String,\n            default: 'hh:mm:ss',\n        },\n        autoStart: {\n            type: Boolean,\n            default: true,\n        },\n    },\n    watch: {\n        time: {\n            immediate: true,\n            handler(value) {\n                if (value) {\n                    this.reset()\n                }\n            },\n        },\n    },\n    data() {\n        return {\n            timeObj: {},\n            formateTime: 0,\n        }\n    },\n    created() {},\n    computed: {},\n    methods: {\n        createTimer(fn) {\n            return setTimeout(fn, 100)\n        },\n        isSameSecond(time1, time2) {\n            return Math.floor(time1) === Math.floor(time2)\n        },\n        start() {\n            if (this.counting) {\n                return\n            }\n            this.counting = true\n            this.endTime = Date.now() + this.remain * 1000\n            this.setTimer()\n        },\n        setTimer() {\n            this.tid = this.createTimer(() => {\n                let remain = this.getRemain()\n                if (!this.isSameSecond(remain, this.remain) || remain === 0) {\n                    this.setRemain(remain)\n                }\n                if (this.remain !== 0) {\n                    this.setTimer()\n                }\n            })\n        },\n        getRemain() {\n            return Math.max(this.endTime - Date.now(), 0)\n        },\n        pause() {\n            this.counting = false\n            clearTimeout(this.tid)\n        },\n        reset() {\n            this.pause()\n            this.remain = this.time\n            this.setRemain(this.remain)\n            if (this.autoStart) {\n                this.start()\n            }\n        },\n        setRemain(remain) {\n            const { format } = this\n            this.remain = remain\n            const timeData = parseTimeData(remain)\n            this.formateTime = parseFormat(format, timeData)\n            this.$emit('change', timeData)\n            if (remain === 0) {\n                this.pause()\n                this.$emit('finish')\n            }\n        },\n    },\n}\n", "import mod from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./count-down.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./count-down.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./count-down.vue?vue&type=template&id=2fbaab86&\"\nimport script from \"./count-down.vue?vue&type=script&lang=js&\"\nexport * from \"./count-down.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  \n}\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  null,\n  \"4090b4e2\"\n  \n)\n\nexport default component.exports", "module.exports = __webpack_public_path__ + \"img/news_null.856b3f3.png\";", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/sass-resources-loader/lib/loader.js??ref--7-oneOf-1-4!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./number-box.vue?vue&type=style&index=0&id=1d9d8f36&lang=scss&scoped=true&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"663bee12\", content, true, context)\n};", "export * from \"-!../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/sass-resources-loader/lib/loader.js??ref--7-oneOf-1-4!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./number-box.vue?vue&type=style&index=0&id=1d9d8f36&lang=scss&scoped=true&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".number-box[data-v-1d9d8f36]{display:inline-flex;align-items:center}.number-box .number-input[data-v-1d9d8f36]{position:relative;text-align:center;padding:0;margin:0 6px;align-items:center;justify-content:center}.number-box .minus[data-v-1d9d8f36],.number-box .plus[data-v-1d9d8f36]{width:32px;display:flex;justify-content:center;align-items:center;cursor:pointer}.number-box .plus[data-v-1d9d8f36]{border-radius:0 2px 2px 0}.number-box .minus[data-v-1d9d8f36]{border-radius:2px 0 0 2px}.number-box .disabled[data-v-1d9d8f36]{color:#c8c9cc!important;background:#f7f8fa!important}.number-box .input-disabled[data-v-1d9d8f36]{color:#c8c9cc!important;background-color:#f2f3f5!important}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/sass-resources-loader/lib/loader.js??ref--7-oneOf-1-4!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./comment-list.vue?vue&type=style&index=0&id=4e1720b8&lang=scss&scoped=true&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"23143360\", content, true, context)\n};", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"number-box\"},[_vm._ssrNode(\"<div\"+(_vm._ssrClass(null,{ minus: true, disabled: _vm.disabled || _vm.inputVal <= _vm.min }))+(_vm._ssrStyle(null,{\n            background: _vm.bgColor,\n            height: _vm.inputHeight + 'px',\n            color: _vm.color,\n        }, null))+\" data-v-1d9d8f36><div\"+(_vm._ssrStyle(null,{ fontSize: _vm.size + 'px' }, null))+\" data-v-1d9d8f36>-</div></div> <input\"+(_vm._ssrAttr(\"disabled\",_vm.disabledInput || _vm.disabled))+\" type=\\\"text\\\"\"+(_vm._ssrAttr(\"value\",(_vm.inputVal)))+(_vm._ssrClass(null,{ 'number-input': true, 'input-disabled': _vm.disabled }))+(_vm._ssrStyle(null,{\n            color: _vm.color,\n            fontSize: _vm.size + 'px',\n            background: _vm.bgColor,\n            height: _vm.inputHeight + 'px',\n            width: _vm.inputWidth + 'px',\n        }, null))+\" data-v-1d9d8f36> <div\"+(_vm._ssrClass(\"plus\",{ disabled: _vm.disabled || _vm.inputVal >= _vm.max }))+(_vm._ssrStyle(null,{\n            background: _vm.bgColor,\n            height: _vm.inputHeight + 'px',\n            color: _vm.color,\n        }, null))+\" data-v-1d9d8f36><div\"+(_vm._ssrStyle(null,{ fontSize: _vm.size + 'px' }, null))+\" data-v-1d9d8f36>+</div></div>\")])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nexport default {\n    components: {},\n    props: {\n        // 预显示的数字\n        value: {\n            type: Number,\n            default: 1,\n        },\n        // 背景颜色\n        bgColor: {\n            type: String,\n            default: ' #F2F3F5',\n        },\n        // 最小值\n        min: {\n            type: Number,\n            default: 0,\n        },\n        // 最大值\n        max: {\n            type: Number,\n            default: 99999,\n        },\n        // 步进值，每次加或减的值\n        step: {\n            type: Number,\n            default: 1,\n        },\n        // 是否禁用加减操作\n        disabled: {\n            type: Boolean,\n            default: false,\n        },\n        // input的字体大小，单位px\n        size: {\n            type: [Number, String],\n            default: 14,\n        },\n        // input宽度，单位px\n        inputWidth: {\n            type: [Number, String],\n            default: 64,\n        },\n        //字体颜色\n        color: {\n            type: String,\n            default: '#333',\n        },\n        // input高度，单位px\n        inputHeight: {\n            type: [Number, String],\n            default: 32,\n        },\n        // index索引，用于列表中使用，让用户知道是哪个numberbox发生了变化，一般使用for循环出来的index值即可\n        index: {\n            type: [Number, String],\n            default: '',\n        },\n        // 是否禁用输入框，与disabled作用于输入框时，为OR的关系，即想要禁用输入框，又可以加减的话\n        // 设置disabled为false，disabledInput为true即可\n        disabledInput: {\n            type: Boolean,\n            default: false,\n        },\n\n        // 是否只能输入大于或等于0的整数(正整数)\n        positiveInteger: {\n            type: Boolean,\n            default: true,\n        },\n        asyncChange: {\n            type: Boolean,\n            default: false,\n        },\n    },\n    watch: {\n        value(v1, v2) {\n            if (!this.changeFromInner) {\n                this.inputVal = v1\n                this.$nextTick(function () {\n                    this.changeFromInner = false\n                })\n            }\n        },\n        inputVal(v1, v2) {\n            if (v1 == '') return\n            let value = 0\n            let tmp = /^(?:-?\\d+|-?\\d{1,3}(?:,\\d{3})+)?(?:\\.\\d+)?$/.test(v1)\n            if (tmp && v1 >= this.min && v1 <= this.max) value = v1\n            else value = v2\n            if (this.positiveInteger) {\n                if (v1 < 0 || String(v1).indexOf('.') !== -1) {\n                    value = v2\n                    this.$nextTick(() => {\n                        this.inputVal = v2\n                    })\n                }\n            }\n            if (this.asyncChange) {\n                return\n            }\n            // 发出change事件\n            this.handleChange(value, 'change')\n        },\n    },\n    data() {\n        return {\n            inputVal: 1, // 输入框中的值，不能直接使用props中的value，因为应该改变props的状态\n            timer: null, // 用作长按的定时器\n            changeFromInner: false, // 值发生变化，是来自内部还是外部\n            innerChangeTimer: null, // 内部定时器\n        }\n    },\n    created() {\n        this.inputVal = Number(this.value)\n    },\n    computed: {},\n    methods: {\n        btnTouchStart(callback) {\n            this[callback]()\n        },\n        minus() {\n            this.computeVal('minus')\n        },\n        plus() {\n            this.computeVal('plus')\n        },\n        calcPlus(num1, num2) {\n            let baseNum, baseNum1, baseNum2\n            try {\n                baseNum1 = num1.toString().split('.')[1].length\n            } catch (e) {\n                baseNum1 = 0\n            }\n            try {\n                baseNum2 = num2.toString().split('.')[1].length\n            } catch (e) {\n                baseNum2 = 0\n            }\n            baseNum = Math.pow(10, Math.max(baseNum1, baseNum2))\n            let precision = baseNum1 >= baseNum2 ? baseNum1 : baseNum2\n            return ((num1 * baseNum + num2 * baseNum) / baseNum).toFixed(\n                precision\n            )\n        },\n        calcMinus(num1, num2) {\n            let baseNum, baseNum1, baseNum2\n            try {\n                baseNum1 = num1.toString().split('.')[1].length\n            } catch (e) {\n                baseNum1 = 0\n            }\n            try {\n                baseNum2 = num2.toString().split('.')[1].length\n            } catch (e) {\n                baseNum2 = 0\n            }\n            baseNum = Math.pow(10, Math.max(baseNum1, baseNum2))\n            let precision = baseNum1 >= baseNum2 ? baseNum1 : baseNum2\n            return ((num1 * baseNum - num2 * baseNum) / baseNum).toFixed(\n                precision\n            )\n        },\n        computeVal(type) {\n            if (this.disabled) return\n            let value = 0\n            // 减\n            if (type === 'minus') {\n                value = this.calcMinus(this.inputVal, this.step)\n            } else if (type === 'plus') {\n                value = this.calcPlus(this.inputVal, this.step)\n            }\n            // 判断是否小于最小值和大于最大值\n            if (value < this.min || value > this.max) {\n                return\n            }\n            if (this.asyncChange) {\n                this.$emit('change', value)\n            } else {\n                this.inputVal = value\n                this.handleChange(value, type)\n            }\n        },\n        // 处理用户手动输入的情况\n        onBlur(event) {\n            let val = 0\n            let value = event.target.value\n\n            console.log(value)\n            if (!/(^\\d+$)/.test(value)) {\n                val = this.min\n            } else {\n                val = +value\n            }\n            if (val > this.max) {\n                val = this.max\n            } else if (val < this.min) {\n                val = this.min\n            }\n            this.$nextTick(() => {\n                this.inputVal = val\n            })\n            this.handleChange(val, 'blur')\n        },\n        // 输入框获得焦点事件\n        onFocus() {\n            this.$emit('focus')\n        },\n        handleChange(value, type) {\n            if (this.disabled) return\n            // 清除定时器，避免造成混乱\n            if (this.innerChangeTimer) {\n                clearTimeout(this.innerChangeTimer)\n                this.innerChangeTimer = null\n            }\n            this.changeFromInner = true\n            this.innerChangeTimer = setTimeout(() => {\n                this.changeFromInner = false\n            }, 150)\n            this.$emit('input', Number(value))\n            this.$emit(type, {\n                value: Number(value),\n                index: this.index,\n            })\n        },\n    },\n}\n", "import mod from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./number-box.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./number-box.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./number-box.vue?vue&type=template&id=1d9d8f36&scoped=true&\"\nimport script from \"./number-box.vue?vue&type=script&lang=js&\"\nexport * from \"./number-box.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./number-box.vue?vue&type=style&index=0&id=1d9d8f36&lang=scss&scoped=true&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  \"1d9d8f36\",\n  \"284477ee\"\n  \n)\n\nexport default component.exports", "export * from \"-!../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/sass-resources-loader/lib/loader.js??ref--7-oneOf-1-4!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./comment-list.vue?vue&type=style&index=0&id=4e1720b8&lang=scss&scoped=true&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".comment-list .comment-con>.item[data-v-4e1720b8]{padding:20px;border-bottom:1px dashed #e5e5e5;align-items:flex-start}.comment-list .comment-con>.item .avatar img[data-v-4e1720b8]{border-radius:50%;width:44px;height:44px}.comment-list .comment-con>.item .comment-imglist[data-v-4e1720b8]{margin-top:10px}.comment-list .comment-con>.item .comment-imglist .item[data-v-4e1720b8]{width:80px;height:80px;margin-right:6px}.comment-list .comment-con>.item .reply[data-v-4e1720b8]{background-color:#f2f2f2;align-items:flex-start;padding:10px}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../../node_modules/sass-resources-loader/lib/loader.js??ref--7-oneOf-1-4!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./_id.vue?vue&type=style&index=0&lang=scss&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"6ab4852a\", content, true, context)\n};", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"comment-list\"},[_vm._ssrNode(\"<div class=\\\"comment-con\\\" data-v-4e1720b8>\",\"</div>\",[(_vm.commentList.length)?[_vm._l((_vm.commentList),function(item,index){return _vm._ssrNode(\"<div class=\\\"item flex\\\" data-v-4e1720b8>\",\"</div>\",[_vm._ssrNode(\"<div class=\\\"avatar m-r-8\\\" data-v-4e1720b8><img\"+(_vm._ssrAttr(\"src\",item.avatar))+\" alt data-v-4e1720b8></div> \"),_vm._ssrNode(\"<div class=\\\"content flex-1\\\" data-v-4e1720b8>\",\"</div>\",[_vm._ssrNode(\"<div data-v-4e1720b8>\"+_vm._ssrEscape(_vm._s(item.nickname))+\"</div> <div class=\\\"lighter\\\" style=\\\"margin: 5px 0 10px\\\" data-v-4e1720b8><span data-v-4e1720b8>\"+_vm._ssrEscape(_vm._s(item.create_time))+\"</span> <span data-v-4e1720b8>|</span> <span data-v-4e1720b8>\"+_vm._ssrEscape(\"规格：\"+_vm._s(item.spec_value_str))+\"</span></div> <div data-v-4e1720b8>\"+_vm._ssrEscape(\"\\n                        \"+_vm._s(item.comment)+\"\\n                    \")+\"</div> \"),_vm._ssrNode(\"<div class=\\\"comment-imglist flex\\\" data-v-4e1720b8>\",\"</div>\",_vm._l((item.image),function(img,index){return _vm._ssrNode(\"<div class=\\\"item\\\" data-v-4e1720b8>\",\"</div>\",[_c('el-image',{staticStyle:{\"height\":\"100%\",\"width\":\"100%\"},attrs:{\"preview-src-list\":item.image,\"src\":img,\"fit\":\"contain\"}})],1)}),0),_vm._ssrNode(\" \"+((item.reply)?(\"<div class=\\\"flex reply m-t-16\\\" data-v-4e1720b8><div class=\\\"primary flex-none\\\" data-v-4e1720b8>商家回复：</div> <div class=\\\"lighter\\\" data-v-4e1720b8>\"+_vm._ssrEscape(\"\\n                            \"+_vm._s(item.reply)+\"\\n                        \")+\"</div></div>\"):\"<!---->\"))],2)],2)}),_vm._ssrNode(\" \"),(_vm.count)?_vm._ssrNode(\"<div class=\\\"pagination flex row-center\\\" style=\\\"padding: 38px 0\\\" data-v-4e1720b8>\",\"</div>\",[_c('el-pagination',{attrs:{\"background\":\"\",\"hide-on-single-page\":\"\",\"layout\":\"prev, pager, next\",\"total\":_vm.count,\"page-size\":10},on:{\"current-change\":_vm.changePage}})],1):_vm._e()]:_c('null-data',{attrs:{\"img\":require('~/static/images/news_null.png'),\"text\":\"暂无评价~\"}})],2)])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nexport default {\n    components: {},\n    props: {\n        list: {\n            type: Array,\n            default: () => [],\n        },\n        type: Number,\n        goodsId: [String, Number],\n    },\n    data() {\n        return {\n            commentList: [],\n            count: 0,\n            page: 1,\n        }\n    },\n    created() {\n        this.getCommentList()\n    },\n    methods: {\n        async getCommentList() {\n            const { data, code } = await this.$get('goods_comment/lists', {\n                params: {\n                    type: this.type,\n                    goods_id: this.goodsId,\n                    page_size: 10,\n                    page_no: this.page,\n                },\n            })\n            if (code == 1) {\n                this.commentList = data.lists\n                this.count = data.count\n            }\n        },\n        changePage(current) {\n            this.page = current\n            this.getCommentList()\n        },\n    },\n}\n", "import mod from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./comment-list.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./comment-list.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./comment-list.vue?vue&type=template&id=4e1720b8&scoped=true&\"\nimport script from \"./comment-list.vue?vue&type=script&lang=js&\"\nexport * from \"./comment-list.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./comment-list.vue?vue&type=style&index=0&id=4e1720b8&lang=scss&scoped=true&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  \"4e1720b8\",\n  \"849205e4\"\n  \n)\n\nexport default component.exports\n\n/* nuxt-component-imports */\ninstallComponents(component, {NullData: require('/Users/<USER>/Desktop/vue/pc/components/null-data.vue').default})\n", "module.exports = \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACUAAAAkCAMAAAA5HAOUAAAAQlBMVEUAAAD/IDD/KDj/Kjr/LDz/KTn/Kzv/Kjr/Kzv/LDz/Kzv/Kzv/Kzv/LDz/Kzv/LDz/LDz/Kzv/Kzv/LDz/LDv/LDyPingBAAAAFXRSTlMAECAwQFBfYHCAj5+gr7C/wNDf7/B6g4n4AAAAvUlEQVQ4y8XUyRKDIBAEUBZlUYxs8/+/mmiMWtQwkFzS51cFtF0y9v9w3oE0gG4iCa/Illo3tTaQgT2Gvnl6q0S+YIEjC4EGODPUz4uXiviZQk0JbkmTEkVJao6AJM7qrM4kIJLM1TYV2a+Yp5E/CggUCp9KeK6jfPUmqyzfRzTW1FguFEu5WochR8yBGEafspgyXcr+ph5db/TEh0aU19o3VHb71oXLuNq6D/ocANcBuxcztviHSGu+/Kc9AXSSLqTq6c2LAAAAAElFTkSuQmCC\"", "module.exports = \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACUAAAAkCAMAAAA5HAOUAAAAS1BMVEUAAABQUFBQUFBVVVVUVFRTU1NTU1NVVVVUVFRUVFRUVFRVVVVVVVVUVFRVVVVUVFRUVFRVVVVVVVVVVVVVVVVUVFRUVFRVVVVVVVUmEHPwAAAAGHRSTlMAECAwQFBfYHCAj5CfoK+wv8DP0N/g7/AGrtdjAAABEUlEQVQ4y8WUy5aDIBBEeUQeUVTUwP3/L53FaJIR1MxsxhX2udBdRakQ//9I+QFkwV5CGkBfUSNty3gBOR5SZtz55IlGiIZ0qqBnEEKISH8C3chKCCFU5nbcb9kG8iz1nsrcE/P2NpPuRu1MMt0CEJ8HyAiwdOZpnUsAefA/zNR+yADJbW4/gqvard3wWG9Ck9SxbJXW+4pMhybKibiuZqYjamLeTpCZrg515FcbnfE1yJPfVTXV6FlodoVSqErF1lD29IQyDnFfimUwPqM87b7UlsH2tbn+WBpW1dL0vZGrO6E+qu4SQOrUsSAzAtHaCIymTvUJcvj+hkKG1JdUAGb7yr2doZxLOL8Ltfbul/+0Lw1XEXqaPu71AAAAAElFTkSuQmCC\"", "export * from \"-!../../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../../node_modules/sass-resources-loader/lib/loader.js??ref--7-oneOf-1-4!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./_id.vue?vue&type=style&index=0&lang=scss&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".goods-details{padding:16px 0 44px}.goods-details .goods-info .goods-swiper{width:400px;border-radius:4px}.goods-details .goods-info .goods-swiper .swiper{margin:10px 0;padding:0 25px;--swiper-navigation-size:15px;--swiper-navigation-color:#888}.goods-details .goods-info .goods-swiper .swiper .swiper-button-next,.goods-details .goods-info .goods-swiper .swiper .swiper-button-prev{top:0;width:25px;height:100%;margin-top:0;background-size:12px 22px}.goods-details .goods-info .goods-swiper .swiper .swiper-button-prev{left:0}.goods-details .goods-info .goods-swiper .swiper .swiper-button-next{right:0}.goods-details .goods-info .goods-swiper .swiper .swiper-item{cursor:pointer;height:66px;width:66px;border:2px solid transparent}.goods-details .goods-info .goods-swiper .swiper .swiper-item~.swiper-item{margin-left:10px}.goods-details .goods-info .goods-swiper .swiper .swiper-item.active{border-color:#ff2c3c}.goods-details .goods-info .goods-swiper .current-img{width:100%;height:400px}.goods-details .goods-info .info-wrap{min-height:486px;border-radius:4px;padding:20px}.goods-details .goods-info .info-wrap .name{font-size:20px}.goods-details .goods-info .info-wrap .price-wrap{background:#f2f2f2;background-size:cover;height:80px;padding:0 50px 0 20px;margin-bottom:26px}.goods-details .goods-info .info-wrap .price-wrap.seckill{background:#ff2c3c}.goods-details .goods-info .info-wrap .price-wrap.seckill .count-down .item{width:30px;height:30px;background:rgba(0,0,0,.5);text-align:center;line-height:30px;border-radius:4px}.goods-details .goods-info .info-wrap .spec-wrap .spec{align-items:flex-start}.goods-details .goods-info .info-wrap .spec-wrap .spec .spec-name{margin-right:20px;margin-top:6px;flex:none}.goods-details .goods-info .info-wrap .spec-wrap .spec .spec-item{padding:0 20px;line-height:32px;border:1px solid hsla(0,0%,89.8%,.89804);border-radius:2px;margin-right:10px;margin-bottom:10px;cursor:pointer}.goods-details .goods-info .info-wrap .spec-wrap .spec .spec-item.active{color:#ff2c3c;background-color:#ffeeef;border-color:currentColor}.goods-details .goods-info .info-wrap .goods-num{margin-bottom:30px}.goods-details .goods-info .info-wrap .goods-num .num{margin-right:20px}.goods-details .goods-info .info-wrap .goods-btns .btn{margin-right:14px;text-align:center;width:120px;font-size:16px}.goods-details .goods-info .info-wrap .goods-btns .btn.collection{width:146px;line-height:42px;border:1px solid hsla(0,0%,89.8%,.89804);background-color:#fff;border-radius:4px;cursor:pointer;color:#666}.goods-details .goods-info .info-wrap .goods-btns .btn.collection:hover{color:#ff2c3c}.goods-details .goods-info .info-wrap .goods-btns .btn.collection .start-icon{width:18.5px;height:18px}.goods-details .goods-info .shop{width:210px;padding:16px}.goods-details .goods-info .shop .logo-img{width:62px;height:62px;border-radius:50%;overflow:hidden}.goods-details .goods-info .shop .el-rate__icon{font-size:16px}.goods-details .details-wrap{align-items:stretch}.goods-details .details-wrap .details{padding:10px 0;overflow:hidden}.goods-details .details-wrap .details .rich-text{padding:0 10px;width:100%;overflow:hidden}.goods-details .details-wrap .details .rich-text img{width:100%;display:block}.goods-details .details-wrap .details .rich-text p{margin:0}.goods-details .details-wrap .details .evaluation .evaluation-hd{height:80px;margin:0 10px}.goods-details .details-wrap .details .evaluation .evaluation-hd .rate{height:60px;width:220px;border-right:1px solid #e5e5e5;padding-left:10px;margin-right:40px}.goods-details .details-wrap .details .evaluation .evaluation-tab{margin:16px 20px}.goods-details .details-wrap .details .evaluation .evaluation-tab .item{border-radius:2px;cursor:pointer;height:32px;padding:6px 20px;color:#666;background-color:#f2f2f2;margin-right:10px}.goods-details .details-wrap .details .evaluation .evaluation-tab .item.active{color:#fff;background-color:#ff2c3c}.goods-details .goods-like{width:210px}.goods-details .goods-like .title{border-bottom:1px solid hsla(0,0%,89.8%,.89804);height:45px}.goods-details .goods-like .goods-list .item{padding:10px;display:block}.goods-details .goods-like .goods-list .item .goods-img{width:190px;height:190px;margin-bottom:10px}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return (_vm.goodsDetails.id)?_c('div',{staticClass:\"goods-details\"},[_vm._ssrNode(\"<div class=\\\"goods-info flex col-stretch\\\">\",\"</div>\",[_vm._ssrNode(\"<div class=\\\"goods-swiper m-r-16 bg-white flex-col\\\">\",\"</div>\",[_c('el-image',{staticClass:\"current-img\",attrs:{\"preview-src-list\":_vm.goodsImage.map(function (item) { return item.uri; }),\"src\":_vm.goodsImage[_vm.swiperIndex].uri}}),_vm._ssrNode(\" \"),_c('client-only',[_c('swiper',{ref:\"mySwiper\",staticClass:\"swiper\",attrs:{\"options\":_vm.swiperOptions}},[_vm._l((_vm.goodsImage),function(item,index){return _c('swiper-slide',{key:index,class:{\n                            'swiper-item': true,\n                            active: index === _vm.swiperIndex,\n                        }},[_c('div',{staticStyle:{\"width\":\"100%\",\"height\":\"100%\"},on:{\"mouseover\":function($event){_vm.swiperIndex = index}}},[_c('el-image',{staticStyle:{\"width\":\"100%\",\"height\":\"100%\"},attrs:{\"src\":item.uri}})],1)])}),_vm._v(\" \"),_c('div',{staticClass:\"swiper-button-prev\",attrs:{\"slot\":\"button-prev\"},slot:\"button-prev\"}),_vm._v(\" \"),_c('div',{staticClass:\"swiper-button-next\",attrs:{\"slot\":\"button-next\"},slot:\"button-next\"})],2)],1)],2),_vm._ssrNode(\" \"),_vm._ssrNode(\"<div class=\\\"info-wrap bg-white flex-1\\\">\",\"</div>\",[_vm._ssrNode(\"<div class=\\\"name weight-500 m-b-16\\\">\"+_vm._ssrEscape(\"\\n                \"+_vm._s(_vm.goodsDetails.name)+\"\\n            \")+\"</div> \"),(_vm.activity.type == 1)?_vm._ssrNode(\"<div class=\\\"price-wrap flex row-between white seckill\\\">\",\"</div>\",[_vm._ssrNode(\"<div class=\\\"price flex\\\" style=\\\"align-items: baseline\\\">\",\"</div>\",[_vm._ssrNode(\"<div class=\\\"m-r-8\\\">价格</div> \"),_vm._ssrNode(\"<div>\",\"</div>\",[_c('price-formate',{attrs:{\"price\":_vm.checkedGoods.price || _vm.goodsDetails.price,\"subscript-size\":16,\"first-size\":22,\"second-size\":16}})],1),_vm._ssrNode(\" \"),_vm._ssrNode(\"<div class=\\\"line-through m-l-8 flex\\\">\",\"</div>\",[_vm._ssrNode(\"\\n                        原价\\n                        \"),_c('price-formate',{attrs:{\"price\":_vm.checkedGoods.market_price ||\n                                _vm.goodsDetails.market_price}})],2)],2),_vm._ssrNode(\" \"),_vm._ssrNode(\"<div class=\\\"flex\\\">\",\"</div>\",[_vm._ssrNode(\"<div class=\\\"white m-r-16\\\">距离结束还有</div> \"),_c('count-down',{attrs:{\"time\":_vm.countTime,\"is-slot\":true},on:{\"change\":_vm.onChangeDate}},[_c('div',{staticClass:\"flex row-center count-down xxl\"},[_c('div',{staticClass:\"item white\"},[_vm._v(\"\\n                                \"+_vm._s(_vm.timeData.hours)+\"\\n                            \")]),_vm._v(\" \"),_c('div',{staticClass:\"white\",staticStyle:{\"margin\":\"0 4px\"}},[_vm._v(\":\")]),_vm._v(\" \"),_c('div',{staticClass:\"item white\"},[_vm._v(\"\\n                                \"+_vm._s(_vm.timeData.minutes)+\"\\n                            \")]),_vm._v(\" \"),_c('div',{staticClass:\"white\",staticStyle:{\"margin\":\"0 4px\"}},[_vm._v(\":\")]),_vm._v(\" \"),_c('div',{staticClass:\"item white\"},[_vm._v(\"\\n                                \"+_vm._s(_vm.timeData.seconds)+\"\\n                            \")])])])],2)],2):_vm._ssrNode(\"<div class=\\\"price-wrap flex row-between lighter\\\">\",\"</div>\",[_vm._ssrNode(\"<div class=\\\"price flex\\\" style=\\\"align-items: baseline\\\">\",\"</div>\",[_vm._ssrNode(\"<div class=\\\"m-r-8\\\">价格</div> \"),_vm._ssrNode(\"<div class=\\\"primary\\\">\",\"</div>\",[_c('price-formate',{attrs:{\"price\":_vm.checkedGoods.price || _vm.goodsDetails.price,\"subscript-size\":16,\"first-size\":22,\"second-size\":16}})],1),_vm._ssrNode(\" \"),_vm._ssrNode(\"<div class=\\\"line-through m-l-8 muted\\\">\",\"</div>\",[_c('price-formate',{attrs:{\"price\":_vm.checkedGoods.market_price ||\n                                _vm.goodsDetails.market_price}})],1)],2),_vm._ssrNode(\" <div class=\\\"flex\\\">\"+((_vm.goodsDetails.stock !== true)?(\"<div style=\\\"margin-right: 60px\\\"><div class=\\\"m-b-8\\\">库存</div> <div>\"+_vm._ssrEscape(\"\\n                            \"+_vm._s(_vm.checkedGoods.stock || _vm.goodsDetails.stock)+\"\\n                        \")+\"</div></div>\"):\"<!---->\")+\" <div><div class=\\\"m-b-8\\\">销量</div> <div>\"+_vm._ssrEscape(_vm._s(_vm.goodsDetails.sales_sum))+\"</div></div></div>\")],2),_vm._ssrNode(\" <div class=\\\"spec-wrap\\\">\"+(_vm._ssrList((_vm.goodsSpec),function(item,index){return (\"<div class=\\\"spec flex m-b-16\\\"><div class=\\\"lighter spec-name\\\">\"+_vm._ssrEscape(_vm._s(item.name))+\"</div> <div class=\\\"spec-list flex flex-wrap\\\">\"+(_vm._ssrList((item.spec_value),function(specitem,sindex){return (\"<div\"+(_vm._ssrClass(null,[\n                                'spec-item lighter',\n                                { active: specitem.checked } ]))+\">\"+_vm._ssrEscape(\"\\n                            \"+_vm._s(specitem.value)+\"\\n                        \")+\"</div>\")}))+\"</div></div>\")}))+\"</div> \"),_vm._ssrNode(\"<div class=\\\"goods-num flex\\\">\",\"</div>\",[_vm._ssrNode(\"<div class=\\\"num lighter\\\">数量</div> \"),_c('number-box',{attrs:{\"min\":1,\"max\":_vm.checkedGoods.stock},model:{value:(_vm.goodsNum),callback:function ($$v) {_vm.goodsNum=$$v},expression:\"goodsNum\"}})],2),_vm._ssrNode(\" \"),_vm._ssrNode(\"<div class=\\\"goods-btns flex lg\\\">\",\"</div>\",[_c('el-button',{staticClass:\"btn white\",attrs:{\"type\":\"primary\"},on:{\"click\":_vm.onBuyNow}},[_vm._v(\"\\n                    立即购买\\n                \")]),_vm._ssrNode(\" \"),(_vm.activity.type != 1)?_c('el-button',{staticClass:\"btn addcart\",attrs:{\"type\":\"primary\",\"plain\":\"\"},on:{\"click\":_vm.onAddCart}},[_vm._v(\"\\n                    加入购物车\\n                \")]):_vm._e(),_vm._ssrNode(\" <div class=\\\"btn collection flex row-center\\\"><img\"+(_vm._ssrAttr(\"src\",_vm.goodsDetails.is_collect\n                                    ? require('~/static/images/icon_star_s.png')\n                                    : require('~/static/images/icon_star.png')))+\" class=\\\"start-icon m-r-8\\\"> <span>\"+_vm._ssrEscape(_vm._s(_vm.goodsDetails.is_collect ? '取消收藏' : '收藏商品'))+\"</span></div>\")],2)],2),_vm._ssrNode(\" \"),_vm._ssrNode(\"<div class=\\\"shop m-l-16 bg-white\\\">\",\"</div>\",[_vm._ssrNode(\"<div class=\\\"shop-logo flex-col col-center\\\">\",\"</div>\",[_c('el-image',{staticClass:\"logo-img\",attrs:{\"src\":_vm.shop.logo}}),_vm._ssrNode(\" \"),_c('nuxt-link',{staticClass:\"m-t-10\",attrs:{\"to\":(\"/shop_street_detail?id=\" + (_vm.shop.id))}},[(_vm.shop.type == 1)?_c('el-tag',{attrs:{\"size\":\"mini\"}},[_vm._v(\"自营\")]):_vm._e(),_vm._v(\" \"),_c('span',{staticClass:\"weight-500\"},[_vm._v(_vm._s(_vm.shop.name))])],1),_vm._ssrNode(\" <div class=\\\"xs muted m-t-10 line-5\\\">\"+_vm._ssrEscape(\"\\n                \"+_vm._s(_vm.shop.intro)+\"\\n                \")+\"</div>\")],2),_vm._ssrNode(\" <div class=\\\"flex m-t-20\\\"><div class=\\\"flex-1 text-center\\\"><div class=\\\"xxl m-b-10\\\">\"+_vm._ssrEscape(_vm._s(_vm.shop.goods_on_sale))+\"</div> <div>全部商品</div></div> <div class=\\\"flex-1 text-center\\\"><div class=\\\"xxl m-b-10\\\">\"+_vm._ssrEscape(_vm._s(_vm.shop.follow_num))+\"</div> <div>关注人数</div></div></div> \"),_c('el-divider'),_vm._ssrNode(\" \"),_vm._ssrNode(\"<div class=\\\"flex xs m-b-16\\\">\",\"</div>\",[_vm._ssrNode(\"<div class=\\\"m-r-12\\\">店铺星级</div> \"),_vm._ssrNode(\"<div class=\\\"m-t-5\\\">\",\"</div>\",[_c('el-rate',{attrs:{\"disabled\":\"\"},model:{value:(_vm.shop.star),callback:function ($$v) {_vm.$set(_vm.shop, \"star\", $$v)},expression:\"shop.star\"}})],1)],2),_vm._ssrNode(\" <div class=\\\"flex xs m-b-16\\\"><div class=\\\"m-r-12\\\">店铺评分</div> <div>\"+_vm._ssrEscape(_vm._s(_vm.shop.score)+\"分\")+\"</div></div> \"),_vm._ssrNode(\"<div>\",\"</div>\",[_c('el-button',{attrs:{\"type\":\"primary\",\"size\":\"mini\"},on:{\"click\":function($event){return _vm.$router.push((\"/shop_street_detail?id=\" + (_vm.shop.id)))}}},[_vm._v(\"进入店铺\")]),_vm._ssrNode(\" \"),_c('el-button',{attrs:{\"size\":\"mini\"},on:{\"click\":_vm.changeShopFollow}},[_vm._v(_vm._s(_vm.shop.shop_follow_status == 1 ? '已关注' : '关注店铺'))])],2),_vm._ssrNode(\" \"),_c('el-popover',{attrs:{\"placement\":\"bottom\",\"width\":\"200\",\"trigger\":\"hover\"}},[_c('div',[_c('el-image',{staticStyle:{\"width\":\"100%\"},attrs:{\"src\":_vm.shop.customer_image}})],1),_vm._v(\" \"),_c('div',{staticClass:\"xs lighter text-center m-t-30\",attrs:{\"slot\":\"reference\"},slot:\"reference\"},[_c('i',{staticClass:\"el-icon-chat-dot-round nr\"}),_vm._v(\" \"),_c('span',[_vm._v(\"联系客服\")])])])],2)],2),_vm._ssrNode(\" \"),_vm._ssrNode(\"<div class=\\\"details-wrap flex m-t-16\\\">\",\"</div>\",[_vm._ssrNode(\"<div class=\\\"details bg-white flex-1\\\">\",\"</div>\",[_c('el-tabs',{model:{value:(_vm.active),callback:function ($$v) {_vm.active=$$v},expression:\"active\"}},[_c('el-tab-pane',{attrs:{\"label\":\"商品详情\"}},[_c('div',{staticClass:\"rich-text\",domProps:{\"innerHTML\":_vm._s(_vm.goodsDetails.content)}})]),_vm._v(\" \"),_c('el-tab-pane',{attrs:{\"label\":\"商品评价\"}},[_c('div',{staticClass:\"evaluation\"},[_c('div',{staticClass:\"evaluation-hd flex\"},[_c('div',{staticClass:\"rate flex\"},[_c('div',{staticClass:\"lighter m-r-8\"},[_vm._v(\"好评率\")]),_vm._v(\" \"),_c('div',{staticClass:\"primary\",staticStyle:{\"font-size\":\"30px\"}},[_vm._v(\"\\n                                    \"+_vm._s(_vm.goodsDetails.comment.percent)+\"\\n                                \")])]),_vm._v(\" \"),_c('div',{staticClass:\"score flex\"},[_c('span',{staticClass:\"m-r-8 lighter\"},[_vm._v(\"评分\")]),_vm._v(\" \"),_c('el-rate',{attrs:{\"value\":_vm.goodsDetails.comment.goods_comment,\"disabled\":\"\",\"allow-half\":\"\"}})],1)]),_vm._v(\" \"),_c('div',{staticClass:\"evaluation-tab flex\"},_vm._l((_vm.comment.comment),function(item,index){return _c('div',{key:index,class:[\n                                    'item',\n                                    { active: _vm.commentActive == item.id } ],on:{\"click\":function($event){_vm.commentActive = item.id}}},[_vm._v(\"\\n                                \"+_vm._s(item.name)+\"(\"+_vm._s(item.count)+\")\\n                            \")])}),0)]),_vm._v(\" \"),_c('div',[_vm._l((_vm.comment.comment),function(item,index){return [(_vm.commentActive == item.id)?_c('comment-list',{key:index,attrs:{\"goods-id\":_vm.id,\"type\":item.id}}):_vm._e()]})],2)])],1)],1),_vm._ssrNode(\" \"),(_vm.shop.goods_list.length)?_vm._ssrNode(\"<div class=\\\"goods-like m-l-16\\\">\",\"</div>\",[_vm._ssrNode(\"<div class=\\\"title bg-white flex p-l-15\\\">店铺推荐</div> \"),_vm._ssrNode(\"<div class=\\\"goods-list\\\">\",\"</div>\",[_vm._l((_vm.shop.goods_list),function(item,index){return [(index < 5)?_c('nuxt-link',{key:index,staticClass:\"item bg-white m-b-16\",attrs:{\"to\":(\"/goods_details/\" + (item.id))}},[_c('el-image',{staticClass:\"goods-img\",attrs:{\"src\":item.image}}),_vm._v(\" \"),_c('div',{staticClass:\"goods-name line-2\"},[_vm._v(\"\\n                            \"+_vm._s(item.name)+\"\\n                        \")]),_vm._v(\" \"),_c('div',{staticClass:\"price flex m-t-8\"},[_c('div',{staticClass:\"primary m-r-8\"},[_c('price-formate',{attrs:{\"price\":item.min_price,\"first-size\":16}})],1),_vm._v(\" \"),_c('div',{staticClass:\"muted sm line-through\"},[_c('price-formate',{attrs:{\"price\":item.market_price}})],1)])],1):_vm._e()]})],2)],2):_vm._e()],2)],2):_vm._e()}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { mapActions } from 'vuex'\nimport { Message } from 'element-ui'\nexport default {\n    head() {\n        return {\n            title: this.$store.getters.headTitle,\n            link: [\n                {\n                    rel: 'icon',\n                    type: 'image/x-icon',\n                    href: this.$store.getters.favicon,\n                },\n            ],\n        }\n    },\n    async asyncData({ params, $get, app }) {\n        const { data, code, msg } = await $get('goods/getGoodsDetail', {\n            params: { goods_id: params.id },\n        })\n        if (code == 0) {\n            setTimeout(() => app.router.back(), 1500)\n        }\n        return {\n            goodsDetails: data,\n            goodsImage: data.goods_image,\n            activity: data.activity,\n            shop: data.shop,\n        }\n    },\n\n    data() {\n        return {\n            goodsDetails: {},\n            goodsImage: [],\n            activity: {},\n            shop: {\n                goods_list: [],\n            },\n            swiperOptions: {\n                pagination: {\n                    el: '.swiper-pagination',\n                    clickable: true,\n                },\n                navigation: {\n                    nextEl: '.swiper-button-next',\n                    prevEl: '.swiper-button-prev',\n                },\n                preventClicks: true,\n                slidesPerView: 'auto',\n            },\n            active: '0',\n            commentActive: 0,\n            swiperIndex: 0,\n            checkedGoods: {},\n            comment: {},\n            goodsNum: 1,\n            goodsSpec: [],\n            id: '',\n            timeData: {},\n        }\n    },\n    created() {\n        this.id = this.$route.params.id\n        this.getComment(this.id)\n    },\n    methods: {\n        ...mapActions(['getPublicData']),\n        onClickSlide(e) {\n            this.swiperIndex = e\n        },\n        onChoseSpecItem(id, specid) {\n            const { goodsSpec } = this\n            goodsSpec.forEach((item) => {\n                if (item.spec_value && item.id == id) {\n                    item.spec_value.forEach((specitem) => {\n                        specitem.checked = 0\n                        if (specitem.id == specid) {\n                            specitem.checked = 1\n                        }\n                    })\n                }\n            })\n            this.goodsSpec = [...goodsSpec]\n        },\n        async onAddCart() {\n            const {\n                goodsNum,\n                checkedGoods: { id },\n            } = this\n            const { code, data, msg } = await this.$post('cart/add', {\n                item_id: id,\n                goods_num: goodsNum,\n            })\n            if (code == 1) {\n                this.getPublicData()\n                this.$message({\n                    message: msg,\n                    type: 'success',\n                })\n            }\n        },\n        async changeShopFollow() {\n            const { code, msg } = await this.$post('shop_follow/changeStatus', {\n                shop_id: this.shop.id,\n            })\n            if (code == 1) {\n                this.$message({\n                    message: msg,\n                    type: 'success',\n                })\n                this.getGoodsDetail()\n            }\n        },\n        onBuyNow() {\n            const {\n                goodsNum,\n                checkedGoods: { id },\n            } = this\n            const goods = [\n                {\n                    item_id: id,\n                    num: goodsNum,\n                    goods_id: this.id,\n                    shop_id: this.shop.id,\n                },\n            ]\n            this.$router.push({\n                path: '/confirm_order',\n                query: {\n                    data: encodeURIComponent(\n                        JSON.stringify({\n                            goods,\n                            type: 'buy',\n                        })\n                    ),\n                },\n            })\n        },\n        async getGoodsDetail() {\n            const { data, code } = await this.$get('goods/getGoodsDetail', {\n                params: { goods_id: this.id },\n            })\n            if (code == 1) {\n                this.goodsDetails = data\n                this.shop = data.shop\n            }\n        },\n        async onCollectionGoods() {\n            const { data, code, msg } = await this.$post(\n                'goods_collect/changeStatus',\n                {\n                    goods_id: this.id,\n                }\n            )\n            if (code == 1) {\n                this.$message({\n                    message: msg,\n                    type: 'success',\n                })\n                this.getGoodsDetail()\n            }\n        },\n        async getComment() {\n            const { data, code } = await this.$get('/goods_comment/category', {\n                params: { goods_id: this.id },\n            })\n            if (code == 1) {\n                this.comment = data\n                this.commentActive = data.comment[0].id\n            }\n        },\n        onChangeDate(e) {\n            let timeData = {}\n            for (let prop in e) {\n                if (prop !== 'milliseconds')\n                    timeData[prop] = ('0' + e[prop]).slice(-2)\n            }\n            this.timeData = timeData\n        },\n    },\n    watch: {\n        goodsSpec: {\n            immediate: true,\n            handler(value) {\n                const { goods_item } = this.goodsDetails\n                let keyArr = []\n                value.forEach((item) => {\n                    if (item.spec_value) {\n                        item.spec_value.forEach((specitem) => {\n                            if (specitem.checked) {\n                                keyArr.push(specitem.id)\n                            }\n                        })\n                    }\n                })\n                if (!keyArr.length) return\n                let key = keyArr.join(',')\n                let index = goods_item.findIndex((item) => {\n                    return item.spec_value_ids == key\n                })\n                if (index == -1) {\n                    index = 0\n                }\n                this.checkedGoods = goods_item[index]\n                console.log(this.checkedGoods)\n            },\n        },\n        goodsDetails: {\n            immediate: true,\n            handler(value) {\n                if (!value.goods_spec) return\n                value.goods_spec.forEach((item) => {\n                    item.spec_value.forEach((specitem, specindex) => {\n                        if (specindex == 0) {\n                            specitem.checked = 1\n                        } else {\n                            specitem.checked = 0\n                        }\n                    })\n                })\n                this.goodsSpec = [...value.goods_spec]\n            },\n        },\n    },\n    computed: {\n        countTime() {\n            const end_time = this.activity.end_time\n            return end_time ? end_time - Date.now() / 1000 : 0\n        },\n    },\n}\n", "import mod from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./_id.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./_id.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./_id.vue?vue&type=template&id=f2d94ef8&\"\nimport script from \"./_id.vue?vue&type=script&lang=js&\"\nexport * from \"./_id.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./_id.vue?vue&type=style&index=0&lang=scss&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  null,\n  \"b793ee3c\"\n  \n)\n\nexport default component.exports\n\n/* nuxt-component-imports */\ninstallComponents(component, {PriceFormate: require('/Users/<USER>/Desktop/vue/pc/components/price-formate.vue').default,CountDown: require('/Users/<USER>/Desktop/vue/pc/components/count-down.vue').default,NumberBox: require('/Users/<USER>/Desktop/vue/pc/components/number-box.vue').default,CommentList: require('/Users/<USER>/Desktop/vue/pc/components/comment-list.vue').default})\n"], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACXA;AACA;AACA;AACA;AACA;AACA;;;;;ACLA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AADA;AAGA;AACA;AAAA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AADA;AAGA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AA5BA;AACA;AAgCA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAJA;AAKA;AACA;AACA;AAAA;AAAA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAZA;AAhDA;;AC1BA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACvBA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACNA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACXA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACNA;AACA;AACA;;;;;ACFA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AADA;AAGA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AARA;AAaA;AAfA;;ACRA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACvBA;AACA;AACA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAJA;AAMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;;ACvCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAbA;AAkBA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAPA;AADA;AACA;AASA;AACA;AACA;AACA;AAFA;AAIA;AACA;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AApDA;AAtCA;;ACXA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;ACrBA;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACXA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACNA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACXA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;AChBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AACA;AACA;AAFA;AAKA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AApEA;AAyEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AA7BA;AACA;AA6BA;AACA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AACA;AALA;AAMA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AAGA;AACA;AAAA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AAGA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAEA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AA5GA;AArHA;;AC5CA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACvBA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACNA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACXA;AACA;AACA;;;;;ACFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AANA;AACA;AAOA;AACA;AACA;AACA;AACA;AAHA;AAKA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AAJA;AADA;AACA;AAOA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAnBA;AApBA;;ACtEA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AC1BA;;;;;;;ACAA;;;;;;;;ACAA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACNA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;ACbA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAHA;AAHA;AAUA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AADA;AACA;AAEA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAJA;AAMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AADA;AAGA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AAVA;AAYA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AA3BA;AA6BA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AAAA;AAFA;AAIA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAFA;AACA;AAGA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AADA;AACA;AAEA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AAAA;AAFA;AAIA;AAEA;AACA;AACA;AACA;AAJA;AAOA;AACA;AACA;AACA;AAEA;AACA;AAFA;AAFA;AAFA;AAWA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AADA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAGA;AADA;AACA;AAGA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AADA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AAAA;AACA;AAEA;AACA;AAAA;AACA;AACA;AAlHA;AAmHA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAzBA;AA0BA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAfA;AA3BA;AA4CA;AACA;AACA;AACA;AACA;AACA;AALA;AA9NA;;ACnWA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;A", "sourceRoot": ""}