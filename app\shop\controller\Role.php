<?php



namespace app\shop\controller;

use app\shop\logic\RoleLogic;
use app\shop\validate\RoleValidate;
use app\common\basics\ShopBase;
use app\common\server\JsonServer;
use app\common\model\shop\Shop;

class Role extends ShopBase
{
    /**
     * Notes: 列表
     * <AUTHOR> 10:34)
     * @return string|\think\response\Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function lists()
    {
        if ($this->request->isAjax()) {
            $get = $this->request->get();
            return JsonServer::success('', RoleLogic::lists($this->shop_id, $get));
        }
        return view();
    }


    /**
     * Notes: 添加
     * <AUTHOR> 10:34)
     * @return string|\think\response\Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function add()
    {
        if ($this->request->isAjax()) {
            $post = $this->request->post();
            (new RoleValidate())->goCheck('add');
            $result = RoleLogic::addRole($this->shop_id, $post);
            if ($result !== true) {
                return JsonServer::error(RoleLogic::getError() ?: '操作失败');
            }
            return JsonServer::success('操作成功');
        }

        // 获取当前商家的等级信息
        $shop_tier_level = $this->getShopTierLevel();

        return view('', [
            'auth_tree' => json_encode(RoleLogic::authTree('', $shop_tier_level), true)
        ]);
    }


    /**
     * Notes: 编辑
     * @param string $role_id
     * <AUTHOR> 10:34)
     * @return string|\think\response\Json
     * @throws \think\Exception
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function edit($role_id = '')
    {
        if ($this->request->isAjax()) {
            $post = $this->request->post();
            (new RoleValidate())->goCheck('edit');
            $result = RoleLogic::editRole($this->shop_id, $post);
            if ($result !== true) {
                return JsonServer::error(RoleLogic::getError() ?: '操作失败');
            }
            return JsonServer::success('操作成功');
        }

        // 获取当前商家的等级信息
        $shop_tier_level = $this->getShopTierLevel();
        $auth_tree = RoleLogic::authTree($role_id, $shop_tier_level);

        return view('', [
            'info' => RoleLogic::roleInfo($role_id),
            'auth_tree' => json_encode($auth_tree),
        ]);
    }

    /**
     * Notes: 删除
     * @param $role_id
     * <AUTHOR> 10:35)
     * @return \think\response\Json
     * @throws \think\Exception
     */
    public function del($id)
    {
        if ($this->request->isAjax()) {
            (new RoleValidate())->goCheck('del');
            RoleLogic::delRole($this->shop_id, $id);
            return JsonServer::success('删除成功');
        }
    }

    /**
     * Notes: 获取当前商家的等级信息
     * @return int
     */
    private function getShopTierLevel()
    {
        if (empty($this->shop_id)) {
            return 0; // 默认为0元入驻
        }

        $shop = Shop::where('id', $this->shop_id)->field('tier_level')->find();
        return $shop ? $shop['tier_level'] : 0;
    }
}