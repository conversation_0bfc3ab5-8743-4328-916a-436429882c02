<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Bm\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * ModifyDeviceAliases请求参数结构体
 *
 * @method array getDeviceAliases() 获取需要改名的设备与别名列表
 * @method void setDeviceAliases(array $DeviceAliases) 设置需要改名的设备与别名列表
 */
class ModifyDeviceAliasesRequest extends AbstractModel
{
    /**
     * @var array 需要改名的设备与别名列表
     */
    public $DeviceAliases;

    /**
     * @param array $DeviceAliases 需要改名的设备与别名列表
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("DeviceAliases",$param) and $param["DeviceAliases"] !== null) {
            $this->DeviceAliases = [];
            foreach ($param["DeviceAliases"] as $key => $value){
                $obj = new DeviceAlias();
                $obj->deserialize($value);
                array_push($this->DeviceAliases, $obj);
            }
        }
    }
}
