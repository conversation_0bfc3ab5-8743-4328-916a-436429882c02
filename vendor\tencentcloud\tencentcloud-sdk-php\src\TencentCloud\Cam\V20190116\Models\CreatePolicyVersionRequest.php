<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Cam\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * CreatePolicyVersion请求参数结构体
 *
 * @method integer getPolicyId() 获取策略ID
 * @method void setPolicyId(integer $PolicyId) 设置策略ID
 * @method string getPolicyDocument() 获取策略文本信息
 * @method void setPolicyDocument(string $PolicyDocument) 设置策略文本信息
 * @method boolean getSetAsDefault() 获取是否设置为当前策略的版本
 * @method void setSetAsDefault(boolean $SetAsDefault) 设置是否设置为当前策略的版本
 */
class CreatePolicyVersionRequest extends AbstractModel
{
    /**
     * @var integer 策略ID
     */
    public $PolicyId;

    /**
     * @var string 策略文本信息
     */
    public $PolicyDocument;

    /**
     * @var boolean 是否设置为当前策略的版本
     */
    public $SetAsDefault;

    /**
     * @param integer $PolicyId 策略ID
     * @param string $PolicyDocument 策略文本信息
     * @param boolean $SetAsDefault 是否设置为当前策略的版本
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("PolicyId",$param) and $param["PolicyId"] !== null) {
            $this->PolicyId = $param["PolicyId"];
        }

        if (array_key_exists("PolicyDocument",$param) and $param["PolicyDocument"] !== null) {
            $this->PolicyDocument = $param["PolicyDocument"];
        }

        if (array_key_exists("SetAsDefault",$param) and $param["SetAsDefault"] !== null) {
            $this->SetAsDefault = $param["SetAsDefault"];
        }
    }
}
