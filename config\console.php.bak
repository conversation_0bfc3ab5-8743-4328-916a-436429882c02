<?php
// +----------------------------------------------------------------------
// | 控制台配置
// +----------------------------------------------------------------------
use app\common\command\WechatMiniExpressSendSync;

return [
    // 指令定义
    'commands' => [
        'crontab'            => 'app\common\command\Crontab',
        'order_close'        => 'app\common\command\OrderClose',
        'order_finish'       => 'app\common\command\OrderFinish',
        'distribution_order' => 'app\common\command\DistributionOrder',
        'stat_daily'         => 'app\common\command\StatDaily', // 添加数据统计命令
        'user_distribution'  => 'app\common\command\UserDistribution', //更新会员分销信息
        'bargain_close'      => 'app\common\command\BargainClose', //更新砍价记录状态
        'team_end'           => 'app\common\command\TeamEnd', //拼团超时关闭
        'password'           => 'app\common\command\Password', //管理员密码
        'award_integral'     => 'app\common\command\AwardIntegral', //结算消费赠送积分
        'wechat_merchant_transfer'     => 'app\common\command\WechatMerchantTransfer', //商家转账到零钱查询
        'wechat_live_room'     => 'app\common\command\WechatLiveRoom', //更新直播间状态
        'wechat_live_goods'     => 'app\common\command\WechatLiveGoods', //更新直播商品状态
        'goods_word'     => 'app\common\command\WordUpdate', //更新商品分词
        'sync_goods_to_meilisearch' => 'app\command\SyncGoodsToMeiliSearch', //同步商品数据到MeiliSearch
        'init_search_suggestions' => 'app\common\command\InitSearchSuggestions', //初始化搜索候选词索引
        'update_search_suggestions' => 'app\common\command\UpdateSearchSuggestions', //更新搜索候选词索引
        'update_websocket_ssl' => 'app\common\command\UpdateWebSocketSSL', //更新WebSocket SSL证书
        'create_offline_messages_table' => 'app\common\command\CreateOfflineMessagesTable', //创建离线消息表

        // 微信小程序 发货信息同步
        'wechat_mini_express_send_sync' => WechatMiniExpressSendSync::class,
    ],
];

