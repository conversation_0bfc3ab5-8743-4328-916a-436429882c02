-- 更新微信关注用户表的subscribe字段注释
-- 执行时间：2024-12-29
-- 说明：明确subscribe字段的状态值含义，支持取消关注检测

-- 修改subscribe字段注释，明确各状态值含义
ALTER TABLE `ls_wechat_followers` 
MODIFY COLUMN `subscribe` tinyint(1) DEFAULT '1' 
COMMENT '关注状态(0=未关注/已取消关注, 1=已关注, 2=待验证状态-用于同步时临时标记)';

-- 确保所有现有记录的subscribe字段都是有效值（0或1）
UPDATE `ls_wechat_followers` SET `subscribe` = 0 WHERE `subscribe` NOT IN (0, 1);

-- 验证更新结果
SELECT 
    subscribe,
    COUNT(*) as count,
    CASE 
        WHEN subscribe = 0 THEN '未关注/已取消关注'
        WHEN subscribe = 1 THEN '已关注'
        WHEN subscribe = 2 THEN '待验证状态'
        ELSE '未知状态'
    END as status_desc
FROM `ls_wechat_followers` 
GROUP BY subscribe 
ORDER BY subscribe;
