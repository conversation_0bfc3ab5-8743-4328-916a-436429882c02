<?php
namespace app\common\library;

use Mei<PERSON>ear<PERSON>\Client;
class MeiliSearch
{
    private $host;
    private $apiKey;

    public function __construct($host=null, $apiKey=null)
    {
        // 从配置文件或环境变量中获取MeiliSearch配置
        $this->host = $host ?: config('meilisearch.host', 'http://*************:7700');
        $this->apiKey = $apiKey ?: config('meilisearch.api_key', '27bb9198372f81f8b95fb75d0252912de061fb6fa90d0ad6eb347cc051f0abe3');

        // 记录连接信息
        \think\facade\Log::info('MeiliSearch连接信息: ' . $this->host);
    }

    /**
     * 添加文档到索引
     *
     * @param string $indexName 索引名称
     * @param array $documents 文档数组
     * @return mixed
     */
    public function addDocumentsToIndex(string $indexName, array $documents)
    {
        $url = "{$this->host}/indexes/{$indexName}/documents";
        $headers = [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $this->apiKey,
        ];
        $data = json_encode($documents);

        return $this->curlPost($url, $data, $headers);
    }

    /**
     * 在索引中搜索文档
     *
     * @param string $indexName 索引名称
     * @param string $query 查询字符串
     * @return mixed
     */
    public function searchInIndex(string $indexName, string $query)
    {
        $url = "{$this->host}/indexes/{$indexName}/search";
        $headers = [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $this->apiKey,
        ];
        $data = json_encode(['q' => $query]);

        return $this->curlPost($url, $data, $headers);
    }

    /**
     * 更新索引中的文档
     *
     * @param string $indexName 索引名称
     * @param array $documents 文档数组，每个文档应包含 'id' 字段以标识要更新的文档
     * @return mixed
     */
    public function updateDocumentsInIndex(string $indexName, array $documents) {
        $url = "{$this->host}/indexes/{$indexName}/documents/update";
        $headers = [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $this->apiKey,
        ];
        $data = json_encode($documents);

        return $this->curlPost($url, $data, $headers);
    }

    /**
     * 更新索引中的文档
     *
     * @param string $indexName 索引名称
     * @param array $documents 文档数组，每个文档应包含 'id' 字段以标识要更新的文档
     * @return mixed
     */
    public function updateSynonyms(string $indexName, array $documents) {
        $url = "{$this->host}/indexes/{$indexName}/documents/update";
        $headers = [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $this->apiKey,
        ];
        $data = json_encode($documents);

        return $this->curlPost($url, $data, $headers);
    }

    /**
     * 为索引增加同义词规则
     *
     * @param string $indexName 索引名称
     * @param array $synonyms 同义词规则数组，每个元素是一个包含"input"和"synonyms"的关联数组
     * @return mixed
     */
    public function addSynonymsToIndex(string $indexName, array $synonyms) {
        $url = "{$this->host}/indexes/{$indexName}/settings/synonyms";
        $headers = [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $this->apiKey,
        ];
        $data = json_encode($synonyms);

        return $this->curlPut($url, $data, $headers);
    }

    public function getTy(string $indexName) {
        $url = "{$this->host}/indexes/{$indexName}/settings/synonyms";
        $headers = [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $this->apiKey,
        ];
//        $data = json_encode($synonyms);

        return $this->curlPost($url, [], $headers);
    }


    /**
     * 搜索候选词
     * @param string $indexName 索引名称
     * @param string $query 搜索关键词
     * @param int $limit 返回结果数量
     * @return array
     */
    public function searchCandidates(string $indexName, string $query, int $limit = 10): array
    {
        // 使用高级搜索方法
        $searchParams = [
            'limit' => $limit,
            'attributesToHighlight' => ['*'], // 高亮匹配字段
            'attributesToRetrieve' => ['text', 'tags'], // 返回的字段
        ];

        $results = $this->advancedSearch($indexName, $query, $searchParams);

        // 格式化结果
        $candidates = [];
        if (isset($results['hits']) && !empty($results['hits'])) {
            foreach ($results['hits'] as $hit) {
                $candidates[] = [
                    'text' => $hit['text'] ?? '',
                    'tags' => $hit['tags'] ?? '',
                    'highlight' => isset($hit['_formatted']) && isset($hit['_formatted']['text'])
                        ? $hit['_formatted']['text']
                        : ($hit['text'] ?? ''), // 高亮结果
                ];
            }
        }

        return $candidates;
    }

    /**
     * 获取搜索建议
     * @param string $query 搜索关键词
     * @param int $limit 返回结果数量
     * @return array
     */
    public function getSearchSuggestions(string $query, int $limit = 10): array
    {
        if (empty($query)) {
            return [];
        }

        // 使用高级搜索方法
        $searchParams = [
            'limit' => $limit * 2, // 获取更多结果，以便后续筛选
            'attributesToRetrieve' => ['text', 'tags'],
            'sort' => ['weight:desc', 'popularity:desc']
        ];

        $results = $this->advancedSearch('search_suggestions', $query, $searchParams);

        // 记录搜索结果，便于调试
        \think\facade\Log::info('Search suggestions results: ' . json_encode($results));

        // 格式化结果，优先返回短词
        $suggestions = [];
        $longSuggestions = [];

        if (isset($results['hits']) && !empty($results['hits'])) {
            foreach ($results['hits'] as $hit) {
                if (isset($hit['text']) && !empty($hit['text'])) {
                    $text = $hit['text'];
                    $tags = $hit['tags'] ?? '';

                    // 如果是分词或词组，优先添加（这些通常更短）
                    if (strpos($tags, '分词') !== false || strpos($tags, '词组') !== false) {
                        // 如果长度小于15个字符，直接添加到优先列表
                        if (mb_strlen($text, 'UTF-8') <= 10) {
                            $suggestions[] = $text;
                        } else {
                            $longSuggestions[] = $text;
                        }
                    }
                    // 如果是热门搜索，也优先添加
                    else if (strpos($tags, '热门搜索') !== false) {
                        if (mb_strlen($text, 'UTF-8') <= 10) {
                            $suggestions[] = $text;
                        } else {
                            $longSuggestions[] = $text;
                        }
                    }
                    // 商品名称通常较长，放入长候选词列表
                    else {
                        // 尝试提取商品名称中的关键部分
                        $shortText = $this->extractShortKeyword($text, $query);
                        if (!empty($shortText) && !in_array($shortText, $suggestions)) {
                            $suggestions[] = $shortText;
                        } else {
                            $longSuggestions[] = $text;
                        }
                    }
                }
            }
        }

        // 如果短候选词不足，从长候选词中提取关键词补充
        if (count($suggestions) < $limit && !empty($longSuggestions)) {
            foreach ($longSuggestions as $longText) {
                if (count($suggestions) >= $limit) break;

                $shortText = $this->extractShortKeyword($longText, $query);
                if (!empty($shortText) && !in_array($shortText, $suggestions)) {
                    $suggestions[] = $shortText;
                }
            }
        }

        // 如果还是不足，直接使用长候选词
        if (count($suggestions) < $limit && !empty($longSuggestions)) {
            $remaining = $limit - count($suggestions);
            $suggestions = array_merge($suggestions, array_slice($longSuggestions, 0, $remaining));
        }

        // 去重并限制数量
        $suggestions = array_unique($suggestions);
        $suggestions = array_slice($suggestions, 0, $limit);

        return $suggestions;
    }

    /**
     * 从长文本中提取短关键词
     * @param string $text 原文本
     * @param string $query 搜索关键词
     * @return string 提取的短关键词
     */
    private function extractShortKeyword(string $text, string $query): string
    {
        // 如果文本本身就很短，直接返回
        if (mb_strlen($text, 'UTF-8') <= 10) {
            return $text;
        }

        // 尝试提取包含查询词的短语
        if (!empty($query) && mb_strlen($query, 'UTF-8') >= 2) {
            $position = mb_strpos($text, $query, 0, 'UTF-8');
            if ($position !== false) {
                // 向前找3个字，向后找7个字
                $start = max(0, $position - 3);
                $length = min(10, mb_strlen($text, 'UTF-8') - $start);
                return mb_substr($text, $start, $length, 'UTF-8');
            }
        }

        // 如果没有找到查询词，或查询词太短，尝试提取前10个字符
        return mb_substr($text, 0, 10, 'UTF-8');
    }

    /**
     * 创建索引并配置设置
     *
     * @param string $indexName 索引名称
     * @param array $settings 索引设置，如可搜索字段、排序规则等
     * @return mixed
     */
    public function createIndex(string $indexName, array $settings = [])
    {
        // 创建索引
        $url = "{$this->host}/indexes";
        $headers = [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $this->apiKey,
        ];

        $data = ['uid' => $indexName];

        // 如果提供了primaryKey，添加到请求数据中
        if (isset($settings['primaryKey'])) {
            $data['primaryKey'] = $settings['primaryKey'];
            unset($settings['primaryKey']);
        }

        // 使用POST方法创建索引
        $response = $this->curlPost($url, json_encode($data), $headers);

        // 记录响应，便于调试
        \think\facade\Log::info('Create index response: ' . json_encode($response));

        // 如果有设置，则更新索引设置
        if (!empty($settings) && !isset($response['error'])) {
            $this->updateIndexSettings($indexName, $settings);
        }

        return $response;
    }

    /**
     * 批量导入商品数据到索引
     *
     * @param string $indexName 索引名称
     * @param array $documents 商品数据数组
     * @param string|null $primaryKey 主键字段名
     * @return mixed
     */
    public function importDocuments(string $indexName, array $documents, ?string $primaryKey = null)
    {
        $url = "{$this->host}/indexes/{$indexName}/documents";

        // 如果提供了主键，添加到URL参数中
        if ($primaryKey) {
            $url .= "?primaryKey={$primaryKey}";
        }

        $headers = [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $this->apiKey,
        ];
        $data = json_encode($documents);

        return $this->curlPost($url, $data, $headers);
    }

    /**
     * 更新索引设置
     *
     * @param string $indexName 索引名称
     * @param array $settings 索引设置
     * @return mixed
     */
    public function updateIndexSettings(string $indexName, array $settings)
    {
        $url = "{$this->host}/indexes/{$indexName}/settings";
        $headers = [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $this->apiKey,
        ];
        $data = json_encode($settings);

        return $this->curlPut($url, $data, $headers);
    }

    /**
     * 获取索引信息
     *
     * @param string $indexName 索引名称
     * @return mixed
     */
    public function getIndex(string $indexName)
    {
        $url = "{$this->host}/indexes/{$indexName}";
        $headers = [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $this->apiKey,
        ];

        return $this->curlGet($url, $headers);
    }

    /**
     * 高级搜索功能
     *
     * @param string $indexName 索引名称
     * @param string $query 查询字符串
     * @param array $options 搜索选项，如过滤器、排序、分页等
     * @return mixed
     */
    public function advancedSearch(string $indexName, string $query, array $options = [])
    {
        $url = "{$this->host}/indexes/{$indexName}/search";
        $headers = [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $this->apiKey,
        ];

        $searchParams = array_merge(['q' => $query], $options);
        $data = json_encode($searchParams);

        return $this->curlPost($url, $data, $headers);
    }

    /**
     * 执行 cURL GET 请求
     *
     * @param string $url 请求 URL
     * @param array $headers 请求头
     * @return mixed
     */
    private function curlGet($url, $headers = [])
    {
        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_TIMEOUT, 60); // 设置超时时间为60秒
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // 不验证SSL证书
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false); // 不验证SSL主机
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        if (curl_errno($ch)) {
            $error = 'Curl Error: ' . curl_error($ch);
            \think\facade\Log::write($error);
            curl_close($ch);
            return ['error' => $error];
        }

        curl_close($ch);

        $decodedResponse = json_decode($response, true);

        // 如果HTTP状态码不是2xx，记录错误
        if ($httpCode < 200 || $httpCode >= 300) {
            $error = "HTTP Error: {$httpCode}, Response: " . $response;
            \think\facade\Log::write($error);
            return ['error' => $error, 'http_code' => $httpCode, 'response' => $decodedResponse];
        }

        return $decodedResponse;
    }

    /**
     * 发送PUT请求的辅助方法
     *
     * @param string $url 请求的URL
     * @param string $data 要发送的数据
     * @param array $headers HTTP头部信息
     * @return mixed
     */
    protected function curlPut($url, $data, $headers) {
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "PUT");
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 60); // 设置超时时间为60秒
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // 不验证SSL证书
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false); // 不验证SSL主机
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        if (curl_errno($ch)) {
            $error = 'Curl Error: ' . curl_error($ch);
            \think\facade\Log::write($error);
            curl_close($ch);
            return ['error' => $error];
        }

        curl_close($ch);

        $decodedResponse = json_decode($response, true);

        // 如果HTTP状态码不是2xx，记录错误
        if ($httpCode < 200 || $httpCode >= 300) {
            $error = "HTTP Error: {$httpCode}, Response: " . $response;
            \think\facade\Log::write($error);
            return ['error' => $error, 'http_code' => $httpCode, 'response' => $decodedResponse];
        }

        return $decodedResponse;
    }


    /**
     * 执行 cURL POST 请求
     *
     * @param string $url 请求 URL
     * @param string $data 请求数据
     * @param array $headers 请求头
     * @return mixed
     */
    private function curlPost($url, $data, $headers = [])
    {
        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        curl_setopt($ch, CURLOPT_TIMEOUT, 60); // 设置超时时间为60秒
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // 不验证SSL证书
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false); // 不验证SSL主机
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        if (curl_errno($ch)) {
            $error = 'Curl Error: ' . curl_error($ch);
            \think\facade\Log::write($error);
            curl_close($ch);
            return ['error' => $error];
        }

        curl_close($ch);

        $decodedResponse = json_decode($response, true);

        // 如果HTTP状态码不是2xx，记录错误
        if ($httpCode < 200 || $httpCode >= 300) {
            $error = "HTTP Error: {$httpCode}, Response: " . $response;
            \think\facade\Log::write($error);
            return ['error' => $error, 'http_code' => $httpCode, 'response' => $decodedResponse];
        }

        return $decodedResponse;
    }

    /**
     * 从索引中删除文档
     *
     * @param string $indexName 索引名称
     * @param array $documentIds 要删除的文档ID数组
     * @return mixed
     */
    public function deleteDocuments(string $indexName, array $documentIds)
    {
        $url = "{$this->host}/indexes/{$indexName}/documents/delete";
        $headers = [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $this->apiKey,
        ];
        $data = json_encode(['ids' => $documentIds]);

        return $this->curlPost($url, $data, $headers);
    }

    /**
     * 删除指定的索引
     *
     * @param string $indexName 要删除的索引名称
     * @return mixed 删除成功返回响应，失败返回错误信息
     */
    public function deleteIndex($indexName) {
        // 构造删除索引的URL
        $url = "{$this->host}/indexes/{$indexName}";
        $headers = [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $this->apiKey,
        ];

        // 初始化cURL会话
        $ch = curl_init();

        // 设置cURL选项
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'DELETE');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 60); // 设置超时时间为60秒
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // 不验证SSL证书
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false); // 不验证SSL主机
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        if (curl_errno($ch)) {
            $error = 'Curl Error: ' . curl_error($ch);
            \think\facade\Log::write($error);
            curl_close($ch);
            return ['error' => $error];
        }

        curl_close($ch);

        // 204状态码表示成功删除
        if ($httpCode == 204) {
            return ['success' => true];
        }

        $decodedResponse = json_decode($response, true);

        // 如果HTTP状态码不是2xx，记录错误
        if ($httpCode < 200 || $httpCode >= 300) {
            $error = "HTTP Error: {$httpCode}, Response: " . $response;
            \think\facade\Log::write($error);
            return ['error' => $error, 'http_code' => $httpCode, 'response' => $decodedResponse];
        }

        return $decodedResponse;
    }