/**
 * 管理员通知测试脚本
 * 
 * 使用方法：
 * 1. 在管理后台页面打开浏览器控制台
 * 2. 复制粘贴此脚本内容到控制台
 * 3. 调用 testNotification() 函数发送测试通知
 */

// 测试通知函数
function testNotification(title, content, type, url, icon) {
    // 设置默认值
    title = title || '测试通知';
    content = content || '这是一条通过控制台发送的测试通知，请查收！';
    type = type || 'admin_notification';
    url = url || '';
    icon = icon || 0;
    
    console.log('%c发送测试通知', 'color: blue; font-weight: bold', {
        title: title,
        content: content,
        type: type,
        url: url,
        icon: icon
    });
    
    // 发送测试通知请求
    fetch('/api/notification/testNotification?title=' + encodeURIComponent(title) + 
          '&content=' + encodeURIComponent(content) + 
          '&type=' + encodeURIComponent(type) + 
          '&url=' + encodeURIComponent(url) + 
          '&icon=' + icon, {
        method: 'GET'
    })
    .then(response => response.json())
    .then(data => {
        console.log('%c通知发送结果', 'color: green; font-weight: bold', data);
        if (data.code === 1) {
            console.log('%c通知发送成功', 'color: green; font-weight: bold');
        } else {
            console.error('通知发送失败', data.msg);
        }
    })
    .catch(error => {
        console.error('发送通知请求出错', error);
    });
}

// 测试直接调用通知显示函数
function testShowNotification(title, content, type, url, icon) {
    // 设置默认值
    title = title || '测试通知';
    content = content || '这是一条通过控制台直接显示的测试通知，请查收！';
    type = type || 'admin_notification';
    url = url || '';
    icon = icon || 0;
    
    console.log('%c直接显示测试通知', 'color: blue; font-weight: bold', {
        title: title,
        content: content,
        type: type,
        url: url,
        icon: icon
    });
    
    // 直接调用通知显示函数
    if (typeof showNotification === 'function') {
        showNotification(title, content, type, url, icon);
    } else {
        console.error('showNotification函数不存在，无法显示通知');
        // 尝试使用layer
        if (typeof layer !== 'undefined') {
            layer.open({
                type: 1,
                title: title,
                content: '<div style="padding: 20px; line-height: 1.8;">' + content + '</div>',
                shade: 0,
                offset: 'rb',
                area: ['350px', 'auto'],
                btn: ['关闭']
            });
        } else {
            // 使用原生alert
            alert(title + '\n' + content);
        }
    }
}

// 测试发送WebSocket消息
function testWebSocketMessage(event, data) {
    // 设置默认值
    event = event || 'notification';
    data = data || {
        title: '测试WebSocket通知',
        content: '这是一条通过WebSocket直接发送的测试通知，请查收！',
        type: 'admin_notification',
        url: '',
        icon: 0,
        timestamp: Date.now()
    };
    
    console.log('%c发送WebSocket测试消息', 'color: blue; font-weight: bold', {
        event: event,
        data: data
    });
    
    // 检查WebSocket是否存在
    if (typeof socket !== 'undefined' && socket.readyState === WebSocket.OPEN) {
        // 构建消息
        var message = {
            event: event,
            data: data
        };
        
        // 发送消息
        socket.send(JSON.stringify(message));
        console.log('%cWebSocket消息已发送', 'color: green; font-weight: bold');
    } else {
        console.error('WebSocket未连接或不存在，无法发送消息');
    }
}

// 测试模拟接收WebSocket消息
function testReceiveWebSocketMessage(event, data) {
    // 设置默认值
    event = event || 'notification';
    data = data || {
        title: '模拟接收的通知',
        content: '这是一条模拟接收的WebSocket通知，请查收！',
        type: 'admin_notification',
        url: '',
        icon: 0,
        timestamp: Date.now()
    };
    
    console.log('%c模拟接收WebSocket消息', 'color: blue; font-weight: bold', {
        event: event,
        data: data
    });
    
    // 构建消息
    var messageEvent = {
        data: JSON.stringify({
            event: event,
            data: data
        })
    };
    
    // 检查WebSocket是否存在
    if (typeof socket !== 'undefined' && typeof socket.onmessage === 'function') {
        // 直接调用onmessage处理函数
        socket.onmessage(messageEvent);
        console.log('%c已模拟接收WebSocket消息', 'color: green; font-weight: bold');
    } else {
        console.error('WebSocket未连接或onmessage处理函数不存在，无法模拟接收消息');
    }
}

// 显示帮助信息
console.log('%c管理员通知测试脚本已加载', 'color: green; font-weight: bold');
console.log('%c可用函数:', 'color: blue; font-weight: bold');
console.log('1. testNotification(title, content, type, url, icon) - 发送测试通知');
console.log('2. testShowNotification(title, content, type, url, icon) - 直接显示测试通知');
console.log('3. testWebSocketMessage(event, data) - 发送WebSocket测试消息');
console.log('4. testReceiveWebSocketMessage(event, data) - 模拟接收WebSocket消息');
console.log('%c示例:', 'color: blue; font-weight: bold');
console.log('testNotification("测试标题", "测试内容", "admin_notification", "", 0)');
console.log('testShowNotification("直接显示", "这是直接显示的通知", "system_notification", "", 1)');
console.log('testReceiveWebSocketMessage("notification", {title: "模拟通知", content: "模拟内容"})');
