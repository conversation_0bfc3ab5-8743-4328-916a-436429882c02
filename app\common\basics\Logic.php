<?php


namespace app\common\basics;


/**
 * 逻辑层基类
 * Class Logic
 * <AUTHOR>
 * @package app\common\basics
 */
abstract class Logic
{
    /**
     * 错误信息
     * @var string
     */
    protected static $error;

    /**
     * 返回错误信息
     * @access public
     * @return string|array
     */
    public static function getError()
    {
        return self::$error;
    }

    /**
     * 设置错误信息
     * @access public
     * @param string $error
     * @return void
     */
    public static function setError($error)
    {
        self::$error = $error;
    }
}