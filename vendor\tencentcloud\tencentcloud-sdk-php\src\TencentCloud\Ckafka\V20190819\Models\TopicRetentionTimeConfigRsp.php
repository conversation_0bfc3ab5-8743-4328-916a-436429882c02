<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Ckafka\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * Topic消息保留时间配置返回信息
 *
 * @method integer getExpect() 获取期望值，即用户配置的Topic消息保留时间(单位分钟)
注意：此字段可能返回 null，表示取不到有效值。
 * @method void setExpect(integer $Expect) 设置期望值，即用户配置的Topic消息保留时间(单位分钟)
注意：此字段可能返回 null，表示取不到有效值。
 * @method integer getCurrent() 获取当前值，即当前生效值(可能存在动态调整，单位分钟)
注意：此字段可能返回 null，表示取不到有效值。
 * @method void setCurrent(integer $Current) 设置当前值，即当前生效值(可能存在动态调整，单位分钟)
注意：此字段可能返回 null，表示取不到有效值。
 * @method integer getModTimeStamp() 获取最近变更时间
注意：此字段可能返回 null，表示取不到有效值。
 * @method void setModTimeStamp(integer $ModTimeStamp) 设置最近变更时间
注意：此字段可能返回 null，表示取不到有效值。
 */
class TopicRetentionTimeConfigRsp extends AbstractModel
{
    /**
     * @var integer 期望值，即用户配置的Topic消息保留时间(单位分钟)
注意：此字段可能返回 null，表示取不到有效值。
     */
    public $Expect;

    /**
     * @var integer 当前值，即当前生效值(可能存在动态调整，单位分钟)
注意：此字段可能返回 null，表示取不到有效值。
     */
    public $Current;

    /**
     * @var integer 最近变更时间
注意：此字段可能返回 null，表示取不到有效值。
     */
    public $ModTimeStamp;

    /**
     * @param integer $Expect 期望值，即用户配置的Topic消息保留时间(单位分钟)
注意：此字段可能返回 null，表示取不到有效值。
     * @param integer $Current 当前值，即当前生效值(可能存在动态调整，单位分钟)
注意：此字段可能返回 null，表示取不到有效值。
     * @param integer $ModTimeStamp 最近变更时间
注意：此字段可能返回 null，表示取不到有效值。
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("Expect",$param) and $param["Expect"] !== null) {
            $this->Expect = $param["Expect"];
        }

        if (array_key_exists("Current",$param) and $param["Current"] !== null) {
            $this->Current = $param["Current"];
        }

        if (array_key_exists("ModTimeStamp",$param) and $param["ModTimeStamp"] !== null) {
            $this->ModTimeStamp = $param["ModTimeStamp"];
        }
    }
}
