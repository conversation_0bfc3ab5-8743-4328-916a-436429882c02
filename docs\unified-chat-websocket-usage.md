# 统一聊天WebSocket使用指南

## 概述

现在系统支持在一个WebSocket连接中处理多种聊天类型：
- **客服聊天**：用户与客服之间的对话
- **用户聊天**：用户与用户之间的对话

## 前端集成

### 1. 引入WebSocket客户端

```javascript
// 引入统一聊天WebSocket管理器
// 文件位置：public/static/js/user-chat-websocket.js
```

### 2. 初始化连接

```javascript
// 微信小程序或H5中初始化
const chatWS = new UnifiedChatWebSocket({
    url: 'wss://kefu.huohanghang.cn',
    token: 'your_user_token_here',
    shop_id: 49, // 商家ID，用于客服聊天
    reconnectInterval: 5000,
    maxReconnectAttempts: 5,
    heartbeatInterval: 30000
});
```

### 3. 监听消息事件

```javascript
// 监听连接成功
chatWS.on('connected', () => {
    console.log('WebSocket连接成功');
});

// 监听登录成功
chatWS.on('login', (data) => {
    console.log('登录成功:', data);
});

// 监听所有消息（统一处理）
chatWS.on('message', (data) => {
    console.log('收到消息:', data);
    if (data.chat_type === 'user_chat') {
        console.log('这是用户对用户的消息');
    } else {
        console.log('这是客服消息');
    }
});

// 监听用户对用户消息
chatWS.on('user_message', (data) => {
    console.log('收到用户消息:', data);
    // 处理用户对用户的消息
});

// 监听客服消息
chatWS.on('kefu_message', (data) => {
    console.log('收到客服消息:', data);
    // 处理客服消息
});

// 监听错误
chatWS.on('error', (error) => {
    console.error('WebSocket错误:', error);
});

// 监听连接断开
chatWS.on('disconnected', () => {
    console.log('WebSocket连接断开');
});
```

### 4. 发送消息

#### 发送用户对用户消息

```javascript
// 发送文本消息
chatWS.sendUserMessage(targetUserId, '你好，这是用户对用户的消息', 1);

// 发送图片消息
chatWS.sendUserMessage(targetUserId, 'image_url_here', 2);

// 发送语音消息
chatWS.sendUserMessage(targetUserId, 'voice_url_here', 4, 30); // 30秒语音

// 发送商品消息
chatWS.sendUserMessage(targetUserId, goodsId, 3);
```

#### 发送客服消息

```javascript
// 发送给客服
chatWS.sendKefuMessage(kefuId, 'kefu', '你好，我需要帮助', 1);

// 客服回复用户
chatWS.sendKefuMessage(userId, 'user', '您好，有什么可以帮您的？', 1);
```

### 5. 消息类型说明

```javascript
const MessageTypes = {
    TEXT: 1,    // 文本消息
    IMAGE: 2,   // 图片消息
    GOODS: 3,   // 商品消息
    VOICE: 4,   // 语音消息
    VIDEO: 5,   // 视频消息
    ORDER: 6    // 订单消息
};
```

## 后端API接口

### 1. 获取用户聊天列表

```
GET /api/user_chat/chat_list
```

**参数：**
- `page`: 页码（可选，默认1）
- `limit`: 每页数量（可选，默认20）

**返回：**
```json
{
    "code": 1,
    "msg": "获取成功",
    "data": [
        {
            "user_id": 123,
            "nickname": "用户昵称",
            "avatar": "头像URL",
            "last_message": "最后一条消息",
            "last_message_time": 1640995200,
            "last_message_time_text": "2022-01-01 12:00",
            "unread_count": 3
        }
    ]
}
```

### 2. 获取聊天记录

```
GET /api/user_chat/chat_history
```

**参数：**
- `chat_user_id`: 聊天对象用户ID（必填）
- `page`: 页码（可选，默认1）
- `limit`: 每页数量（可选，默认20）

### 3. 搜索用户

```
GET /api/user_chat/search_user
```

**参数：**
- `keyword`: 搜索关键词（必填）
- `page`: 页码（可选，默认1）
- `limit`: 每页数量（可选，默认20）

## 微信小程序示例

```javascript
// pages/chat/chat.js
Page({
    data: {
        chatWS: null,
        messages: []
    },

    onLoad(options) {
        this.initWebSocket();
    },

    initWebSocket() {
        const token = wx.getStorageSync('token');
        const shopId = wx.getStorageSync('shop_id') || 0;
        
        this.data.chatWS = new UnifiedChatWebSocket({
            url: 'wss://kefu.huohanghang.cn',
            token: token,
            shop_id: shopId
        });

        // 监听消息
        this.data.chatWS.on('message', (data) => {
            this.addMessage(data);
        });

        // 监听连接状态
        this.data.chatWS.on('connected', () => {
            wx.showToast({ title: '连接成功', icon: 'success' });
        });

        this.data.chatWS.on('error', (error) => {
            wx.showToast({ title: '连接失败', icon: 'error' });
        });
    },

    addMessage(message) {
        this.setData({
            messages: [...this.data.messages, message]
        });
    },

    sendUserMessage(targetUserId, content) {
        this.data.chatWS.sendUserMessage(targetUserId, content, 1);
    },

    sendKefuMessage(kefuId, content) {
        this.data.chatWS.sendKefuMessage(kefuId, 'kefu', content, 1);
    },

    onUnload() {
        if (this.data.chatWS) {
            this.data.chatWS.close();
        }
    }
});
```

## 注意事项

1. **统一连接**：现在只需要建立一个WebSocket连接，通过 `chat_type` 字段区分聊天类型
2. **向后兼容**：现有的客服聊天功能完全兼容，无需修改
3. **消息区分**：通过监听不同的事件或检查 `chat_type` 字段来区分消息类型
4. **连接参数**：使用 `type=user` 连接，保持 `shop_id` 参数用于客服聊天
5. **全局管理**：后端自动管理用户连接状态，支持在线状态检查

## 数据库说明

用户对用户聊天记录存储在 `ls_chat_record` 表中：
- `shop_id = 0`：表示用户对用户聊天
- `from_type = 'user'` 且 `to_type = 'user'`：表示用户间聊天
- 其他字段与客服聊天记录相同
