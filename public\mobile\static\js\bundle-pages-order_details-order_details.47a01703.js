(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["bundle-pages-order_details-order_details"],{"069f":function(t,e,i){"use strict";i("7a82");var n=i("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=n(i("f07e")),o=n(i("c964"));i("a9e3");var r={props:{type:Number,orderId:[Number,String]},data:function(){return{show:!1}},methods:{open:function(){this.show=!0},close:function(){this.show=!1},onConfirm:function(){var t=this;return(0,o.default)((0,a.default)().mark((function e(){return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.type,t.orderId,null,t.$emit("confirm");case 3:case"end":return e.stop()}}),e)})))()}},computed:{getTipsText:function(){var t=this.type;switch(t){case 0:return"确认取消订单吗？";case 1:return"确认删除订单吗?";case 2:return"确认收货吗?"}}}};e.default=r},1377:function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=i("b08d"),a={name:"float-tab",data:function(){return{showMore:!1,top:0}},mounted:function(){var t=this;(0,n.getRect)(".tab-img",!1,this).then((function(e){t.height=e.height,console.log(t.height)}))},methods:{onChange:function(){this.showMore=!this.showMore}},watch:{showMore:function(t){this.top=t?-this.height:0}}};e.default=a},"1ddf":function(t,e,i){"use strict";i.r(e);var n=i("bf65"),a=i("dc47");for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);i("abf6");var r=i("f0c5"),s=Object(r["a"])(a["default"],n["b"],n["c"],!1,null,"3807ccda",null,!1,n["a"],void 0);e["default"]=s.exports},2480:function(t,e,i){"use strict";i.r(e);var n=i("a65b"),a=i("f2f2");for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);i("256b");var r=i("f0c5"),s=Object(r["a"])(a["default"],n["b"],n["c"],!1,null,"8854b6f8",null,!1,n["a"],void 0);e["default"]=s.exports},"256b":function(t,e,i){"use strict";var n=i("3c99"),a=i.n(n);a.a},2698:function(t,e,i){"use strict";var n=i("5bed"),a=i.n(n);a.a},2875:function(t,e,i){"use strict";i.r(e);var n=i("a712"),a=i("c13e");for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);i("8fed");var r=i("f0c5"),s=Object(r["a"])(a["default"],n["b"],n["c"],!1,null,"061dd044",null,!1,n["a"],void 0);e["default"]=s.exports},"2f9a":function(t,e,i){"use strict";i.r(e);var n=i("ec1d"),a=i.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);e["default"]=a.a},"347d":function(t,e,i){var n=i("24fb");e=n(!1),e.push([t.i,".tki-qrcode[data-v-3807ccda]{position:relative}.tki-qrcode-canvas[data-v-3807ccda]{position:fixed;top:%?-99999?%;left:%?-99999?%;z-index:-99999}",""]),t.exports=e},"3c87":function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){return n}));var n={uModal:i("8d42").default},a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("u-modal",{attrs:{"show-cancel-button":!0,content:t.getTipsText,"confirm-color":"#ff2c3c"},on:{confirm:function(e){arguments[0]=e=t.$handleEvent(e),t.onConfirm.apply(void 0,arguments)}},model:{value:t.show,callback:function(e){t.show=e},expression:"show"}})},o=[]},"3c99":function(t,e,i){var n=i("8805");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=i("4f06").default;a("2acf2182",n,!0,{sourceMap:!1,shadowMode:!1})},"46d6":function(t,e,i){var n=i("24fb");e=n(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.shop-title[data-v-705e9a38]{height:%?80?%;flex:1;min-width:0}.shop-title .tag[data-v-705e9a38]{background:linear-gradient(267deg,#ff2c3c,#f52e99);border-radius:%?6?%;padding:%?5?% %?9?%}',""]),t.exports=e},"4d65":function(t,e,i){"use strict";i.r(e);var n=i("069f"),a=i.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);e["default"]=a.a},"58cb":function(t,e,i){"use strict";i("7a82");var n=i("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("caad6"),i("d3b7");var a=n(i("f07e")),o=n(i("c964")),r=i("b0dc"),s=i("b08d"),u=i("98a1"),l=(i("b08d"),{data:function(){return{id:"",orderDetail:{shop:{}},team:{},isFirstLoading:!0,type:0,cancelTime:0,showCancel:"",showLoading:!1,moreStatus:!1,invoiceType:u.invoiceType["ORDERDETAILADD"],showPopup:!1}},onLoad:function(t){var e=this.$Route.query.id;this.id=e},onShow:function(){this.moreStatus=!1,this.getOrderDetailFun()},computed:{showCode:function(){var t=!1;return"拼团订单"==this.orderDetail.order_type?(t="1"==this.orderDetail.team.status,this.orderDetail.pickup_code||(t=!1)):t=!!this.orderDetail.pickup_code,t}},methods:{confirmDialog:function(){var t=this;return(0,o.default)((0,a.default)().mark((function e(){var i,n,o;return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:i=t.type,n=t.id,o=null,e.t0=i,e.next=0===e.t0?5:1===e.t0?9:2===e.t0?13:17;break;case 5:return e.next=7,(0,r.cancelOrder)(n);case 7:return o=e.sent,e.abrupt("break",17);case 9:return e.next=11,(0,r.delOrder)(n);case 11:return o=e.sent,e.abrupt("break",17);case 13:return e.next=15,(0,r.confirmOrder)(n);case 15:return o=e.sent,e.abrupt("break",17);case 17:1==o.code&&(uni.$emit("refreshorder"),[0,2].includes(i)?t.getOrderDetailFun():1==i&&setTimeout((function(){uni.navigateBack()}),2e3));case 18:case"end":return e.stop()}}),e)})))()},dialogOpen:function(){this.$refs.orderDialog.open()},delOrder:function(){var t=this;this.type=1,this.$nextTick((function(){t.dialogOpen()}))},comfirmReceive:function(t){return new Promise((function(e,i){wx.openBusinessView({businessType:"weappOrderConfirm",extraData:{transaction_id:t},success:function(t){var i=t.extraData;"success"==i.status?e("确认收货"):e("取消收货")},fail:function(t){i(t)}})}))},querycomfirmReceive:function(t){return new Promise((function(e,i){(0,r.getwechatSyncCheck)({id:t}).then((function(t){var n=t.data;4===n.order.order_state?e("已确认收货"):i("未确认收货")})).catch((function(t){i(t)}))}))},comfirmOrder:function(){var t=this;this.type=2,this.$nextTick((0,o.default)((0,a.default)().mark((function e(){return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.dialogOpen();case 1:case"end":return e.stop()}}),e)}))))},cancelOrder:function(){var t=this;this.type=0,this.$nextTick((function(){t.dialogOpen()}))},getWeek:function(t){switch(t){case"1":return"周一";case"2":return"周二";case"3":return"周三";case"4":return"周四";case"5":return"周五";case"6":return"周六";case"0":return"周日";default:break}},payNow:function(){var t=this;uni.$on("payment",(function(e){setTimeout((function(){e.result?(t.$toast({title:"支付成功"}),t.getOrderDetailFun(),uni.$emit("refreshorder"),uni.$off("payment")):t.$toast({title:"支付失败"})}),500)})),uni.navigateTo({url:"/pages/payment/payment?from=".concat("order","&order_id=",this.id)})},getOrderDetailFun:function(){var t=this;(0,r.getOrderDetail)(this.id).then((function(e){console.log(e),1==e.code?(t.cancelTime=e.data.order_cancel_time-Date.now()/1e3,t.orderDetail=e.data,t.team=e.data.team||{},t.$nextTick((function(){if(t.isFirstLoading=!1,2===e.data.delivery_type){var i=t.$refs["qrcode"];i._makeCode()}}))):setTimeout((function(){return uni.navigateBack()}),1500)}))},copy:function(t){(0,s.copy)(t)}}});e.default=l},"5bed":function(t,e,i){var n=i("46d6");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=i("4f06").default;a("50d1694e",n,!0,{sourceMap:!1,shadowMode:!1})},"65c2":function(t,e,i){"use strict";i.r(e);var n=i("ad6f"),a=i("7c75");for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);i("9415");var r=i("f0c5"),s=Object(r["a"])(a["default"],n["b"],n["c"],!1,null,"130bc95c",null,!1,n["a"],void 0);e["default"]=s.exports},7226:function(t,e,i){"use strict";i.r(e);var n=i("7bab"),a=i("2f9a");for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);i("985a");var r=i("f0c5"),s=Object(r["a"])(a["default"],n["b"],n["c"],!1,null,"38cbd707",null,!1,n["a"],void 0);e["default"]=s.exports},"7bab":function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){}));var n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"u-countdown"},[t.showDays&&(t.hideZeroDay||!t.hideZeroDay&&"00"!=t.d)?i("v-uni-view",{staticClass:"u-countdown-item",style:[t.itemStyle]},[i("v-uni-view",{staticClass:"u-countdown-time",style:{fontSize:t.fontSize+"rpx",color:t.color}},[t._v(t._s(t.d))])],1):t._e(),t.showDays&&(t.hideZeroDay||!t.hideZeroDay&&"00"!=t.d)?i("v-uni-view",{style:{fontSize:t.separatorSize+"rpx","margin-right":"6rpx",color:t.separatorColor}},[t._v("天")]):t._e(),t.showHours?i("v-uni-view",{staticClass:"u-countdown-item",style:[t.itemStyle]},[i("v-uni-view",{staticClass:"u-countdown-time",style:{fontSize:t.fontSize+"rpx",color:t.color}},[t._v(t._s(t.h))])],1):t._e(),t.showHours?i("v-uni-view",{staticClass:"u-countdown-colon",style:{fontSize:t.separatorSize+"rpx",color:t.separatorColor,paddingBottom:"colon"==t.separator?"4rpx":0}},[t._v(t._s("colon"==t.separator?":":"时"))]):t._e(),t.showMinutes?i("v-uni-view",{staticClass:"u-countdown-item",style:[t.itemStyle]},[i("v-uni-view",{staticClass:"u-countdown-time",style:{fontSize:t.fontSize+"rpx",color:t.color}},[t._v(t._s(t.i))])],1):t._e(),t.showMinutes?i("v-uni-view",{staticClass:"u-countdown-colon",style:{fontSize:t.separatorSize+"rpx",color:t.separatorColor,paddingBottom:"colon"==t.separator?"4rpx":0}},[t._v(t._s("colon"==t.separator?":":"分"))]):t._e(),t.showSeconds?i("v-uni-view",{staticClass:"u-countdown-item",staticStyle:{width:"36rpx"},style:[t.itemStyle]},[i("v-uni-view",{staticClass:"u-countdown-time",style:{fontSize:t.fontSize+"rpx",color:t.color}},[t._v(t._s(t.s))])],1):t._e(),t.showSeconds&&"zh"==t.separator?i("v-uni-view",{staticClass:"u-countdown-colon",style:{fontSize:t.separatorSize+"rpx",color:t.separatorColor,paddingBottom:"colon"==t.separator?"4rpx":0}},[t._v("秒")]):t._e()],1)},a=[]},"7c75":function(t,e,i){"use strict";i.r(e);var n=i("1377"),a=i.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);e["default"]=a.a},"7fea":function(t,e,i){"use strict";i("7a82");var n=i("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;n(i("0122"));i("14d9"),i("d9e2"),i("d401"),i("a9e3"),i("cb29"),i("e9c4");var a={};(function(){function t(t){var e,i,n;return t<128?[t]:t<2048?(e=192+(t>>6),i=128+(63&t),[e,i]):(e=224+(t>>12),i=128+(t>>6&63),n=128+(63&t),[e,i,n])}function e(e,i){this.typeNumber=-1,this.errorCorrectLevel=i,this.modules=null,this.moduleCount=0,this.dataCache=null,this.rsBlocks=null,this.totalDataCount=-1,this.data=e,this.utf8bytes=function(e){for(var i=[],n=0;n<e.length;n++)for(var a=e.charCodeAt(n),o=t(a),r=0;r<o.length;r++)i.push(o[r]);return i}(e),this.make()}e.prototype={constructor:e,getModuleCount:function(){return this.moduleCount},make:function(){this.getRightType(),this.dataCache=this.createData(),this.createQrcode()},makeImpl:function(t){this.moduleCount=4*this.typeNumber+17,this.modules=new Array(this.moduleCount);for(var e=0;e<this.moduleCount;e++)this.modules[e]=new Array(this.moduleCount);this.setupPositionProbePattern(0,0),this.setupPositionProbePattern(this.moduleCount-7,0),this.setupPositionProbePattern(0,this.moduleCount-7),this.setupPositionAdjustPattern(),this.setupTimingPattern(),this.setupTypeInfo(!0,t),this.typeNumber>=7&&this.setupTypeNumber(!0),this.mapData(this.dataCache,t)},setupPositionProbePattern:function(t,e){for(var i=-1;i<=7;i++)if(!(t+i<=-1||this.moduleCount<=t+i))for(var n=-1;n<=7;n++)e+n<=-1||this.moduleCount<=e+n||(this.modules[t+i][e+n]=0<=i&&i<=6&&(0==n||6==n)||0<=n&&n<=6&&(0==i||6==i)||2<=i&&i<=4&&2<=n&&n<=4)},createQrcode:function(){for(var t=0,e=0,i=null,n=0;n<8;n++){this.makeImpl(n);var a=o.getLostPoint(this);(0==n||t>a)&&(t=a,e=n,i=this.modules)}this.modules=i,this.setupTypeInfo(!1,e),this.typeNumber>=7&&this.setupTypeNumber(!1)},setupTimingPattern:function(){for(var t=8;t<this.moduleCount-8;t++)null==this.modules[t][6]&&(this.modules[t][6]=t%2==0,null==this.modules[6][t]&&(this.modules[6][t]=t%2==0))},setupPositionAdjustPattern:function(){for(var t=o.getPatternPosition(this.typeNumber),e=0;e<t.length;e++)for(var i=0;i<t.length;i++){var n=t[e],a=t[i];if(null==this.modules[n][a])for(var r=-2;r<=2;r++)for(var s=-2;s<=2;s++)this.modules[n+r][a+s]=-2==r||2==r||-2==s||2==s||0==r&&0==s}},setupTypeNumber:function(t){for(var e=o.getBCHTypeNumber(this.typeNumber),i=0;i<18;i++){var n=!t&&1==(e>>i&1);this.modules[Math.floor(i/3)][i%3+this.moduleCount-8-3]=n,this.modules[i%3+this.moduleCount-8-3][Math.floor(i/3)]=n}},setupTypeInfo:function(t,e){for(var n=i[this.errorCorrectLevel]<<3|e,a=o.getBCHTypeInfo(n),r=0;r<15;r++){var s=!t&&1==(a>>r&1);r<6?this.modules[r][8]=s:r<8?this.modules[r+1][8]=s:this.modules[this.moduleCount-15+r][8]=s;s=!t&&1==(a>>r&1);r<8?this.modules[8][this.moduleCount-r-1]=s:r<9?this.modules[8][15-r-1+1]=s:this.modules[8][15-r-1]=s}this.modules[this.moduleCount-8][8]=!t},createData:function(){var t=new c,i=this.typeNumber>9?16:8;t.put(4,4),t.put(this.utf8bytes.length,i);for(var n=0,a=this.utf8bytes.length;n<a;n++)t.put(this.utf8bytes[n],8);t.length+4<=8*this.totalDataCount&&t.put(0,4);while(t.length%8!=0)t.putBit(!1);while(1){if(t.length>=8*this.totalDataCount)break;if(t.put(e.PAD0,8),t.length>=8*this.totalDataCount)break;t.put(e.PAD1,8)}return this.createBytes(t)},createBytes:function(t){for(var e=0,i=0,n=0,a=this.rsBlock.length/3,r=new Array,s=0;s<a;s++)for(var l=this.rsBlock[3*s+0],c=this.rsBlock[3*s+1],d=this.rsBlock[3*s+2],f=0;f<l;f++)r.push([d,c]);for(var v=new Array(r.length),h=new Array(r.length),p=0;p<r.length;p++){var m=r[p][0],g=r[p][1]-m;i=Math.max(i,m),n=Math.max(n,g),v[p]=new Array(m);for(s=0;s<v[p].length;s++)v[p][s]=255&t.buffer[s+e];e+=m;var b=o.getErrorCorrectPolynomial(g),w=new u(v[p],b.getLength()-1),_=w.mod(b);h[p]=new Array(b.getLength()-1);for(s=0;s<h[p].length;s++){var y=s+_.getLength()-h[p].length;h[p][s]=y>=0?_.get(y):0}}var C=new Array(this.totalDataCount),x=0;for(s=0;s<i;s++)for(p=0;p<r.length;p++)s<v[p].length&&(C[x++]=v[p][s]);for(s=0;s<n;s++)for(p=0;p<r.length;p++)s<h[p].length&&(C[x++]=h[p][s]);return C},mapData:function(t,e){for(var i=-1,n=this.moduleCount-1,a=7,r=0,s=this.moduleCount-1;s>0;s-=2){6==s&&s--;while(1){for(var u=0;u<2;u++)if(null==this.modules[n][s-u]){var l=!1;r<t.length&&(l=1==(t[r]>>>a&1));var c=o.getMask(e,n,s-u);c&&(l=!l),this.modules[n][s-u]=l,a--,-1==a&&(r++,a=7)}if(n+=i,n<0||this.moduleCount<=n){n-=i,i=-i;break}}}}},e.PAD0=236,e.PAD1=17;for(var i=[1,0,3,2],n={PATTERN000:0,PATTERN001:1,PATTERN010:2,PATTERN011:3,PATTERN100:4,PATTERN101:5,PATTERN110:6,PATTERN111:7},o={PATTERN_POSITION_TABLE:[[],[6,18],[6,22],[6,26],[6,30],[6,34],[6,22,38],[6,24,42],[6,26,46],[6,28,50],[6,30,54],[6,32,58],[6,34,62],[6,26,46,66],[6,26,48,70],[6,26,50,74],[6,30,54,78],[6,30,56,82],[6,30,58,86],[6,34,62,90],[6,28,50,72,94],[6,26,50,74,98],[6,30,54,78,102],[6,28,54,80,106],[6,32,58,84,110],[6,30,58,86,114],[6,34,62,90,118],[6,26,50,74,98,122],[6,30,54,78,102,126],[6,26,52,78,104,130],[6,30,56,82,108,134],[6,34,60,86,112,138],[6,30,58,86,114,142],[6,34,62,90,118,146],[6,30,54,78,102,126,150],[6,24,50,76,102,128,154],[6,28,54,80,106,132,158],[6,32,58,84,110,136,162],[6,26,54,82,110,138,166],[6,30,58,86,114,142,170]],G15:1335,G18:7973,G15_MASK:21522,getBCHTypeInfo:function(t){var e=t<<10;while(o.getBCHDigit(e)-o.getBCHDigit(o.G15)>=0)e^=o.G15<<o.getBCHDigit(e)-o.getBCHDigit(o.G15);return(t<<10|e)^o.G15_MASK},getBCHTypeNumber:function(t){var e=t<<12;while(o.getBCHDigit(e)-o.getBCHDigit(o.G18)>=0)e^=o.G18<<o.getBCHDigit(e)-o.getBCHDigit(o.G18);return t<<12|e},getBCHDigit:function(t){var e=0;while(0!=t)e++,t>>>=1;return e},getPatternPosition:function(t){return o.PATTERN_POSITION_TABLE[t-1]},getMask:function(t,e,i){switch(t){case n.PATTERN000:return(e+i)%2==0;case n.PATTERN001:return e%2==0;case n.PATTERN010:return i%3==0;case n.PATTERN011:return(e+i)%3==0;case n.PATTERN100:return(Math.floor(e/2)+Math.floor(i/3))%2==0;case n.PATTERN101:return e*i%2+e*i%3==0;case n.PATTERN110:return(e*i%2+e*i%3)%2==0;case n.PATTERN111:return(e*i%3+(e+i)%2)%2==0;default:throw new Error("bad maskPattern:"+t)}},getErrorCorrectPolynomial:function(t){for(var e=new u([1],0),i=0;i<t;i++)e=e.multiply(new u([1,r.gexp(i)],0));return e},getLostPoint:function(t){for(var e=t.getModuleCount(),i=0,n=0,a=0;a<e;a++)for(var o=0,r=t.modules[a][0],s=0;s<e;s++){var u=t.modules[a][s];if(s<e-6&&u&&!t.modules[a][s+1]&&t.modules[a][s+2]&&t.modules[a][s+3]&&t.modules[a][s+4]&&!t.modules[a][s+5]&&t.modules[a][s+6]&&(s<e-10?t.modules[a][s+7]&&t.modules[a][s+8]&&t.modules[a][s+9]&&t.modules[a][s+10]&&(i+=40):s>3&&t.modules[a][s-1]&&t.modules[a][s-2]&&t.modules[a][s-3]&&t.modules[a][s-4]&&(i+=40)),a<e-1&&s<e-1){var l=0;u&&l++,t.modules[a+1][s]&&l++,t.modules[a][s+1]&&l++,t.modules[a+1][s+1]&&l++,0!=l&&4!=l||(i+=3)}r^u?o++:(r=u,o>=5&&(i+=3+o-5),o=1),u&&n++}for(s=0;s<e;s++)for(o=0,r=t.modules[0][s],a=0;a<e;a++){u=t.modules[a][s];a<e-6&&u&&!t.modules[a+1][s]&&t.modules[a+2][s]&&t.modules[a+3][s]&&t.modules[a+4][s]&&!t.modules[a+5][s]&&t.modules[a+6][s]&&(a<e-10?t.modules[a+7][s]&&t.modules[a+8][s]&&t.modules[a+9][s]&&t.modules[a+10][s]&&(i+=40):a>3&&t.modules[a-1][s]&&t.modules[a-2][s]&&t.modules[a-3][s]&&t.modules[a-4][s]&&(i+=40)),r^u?o++:(r=u,o>=5&&(i+=3+o-5),o=1)}var c=Math.abs(100*n/e/e-50)/5;return i+=10*c,i}},r={glog:function(t){if(t<1)throw new Error("glog("+t+")");return r.LOG_TABLE[t]},gexp:function(t){while(t<0)t+=255;while(t>=256)t-=255;return r.EXP_TABLE[t]},EXP_TABLE:new Array(256),LOG_TABLE:new Array(256)},s=0;s<8;s++)r.EXP_TABLE[s]=1<<s;for(s=8;s<256;s++)r.EXP_TABLE[s]=r.EXP_TABLE[s-4]^r.EXP_TABLE[s-5]^r.EXP_TABLE[s-6]^r.EXP_TABLE[s-8];for(s=0;s<255;s++)r.LOG_TABLE[r.EXP_TABLE[s]]=s;function u(t,e){if(void 0==t.length)throw new Error(t.length+"/"+e);var i=0;while(i<t.length&&0==t[i])i++;this.num=new Array(t.length-i+e);for(var n=0;n<t.length-i;n++)this.num[n]=t[n+i]}u.prototype={get:function(t){return this.num[t]},getLength:function(){return this.num.length},multiply:function(t){for(var e=new Array(this.getLength()+t.getLength()-1),i=0;i<this.getLength();i++)for(var n=0;n<t.getLength();n++)e[i+n]^=r.gexp(r.glog(this.get(i))+r.glog(t.get(n)));return new u(e,0)},mod:function(t){var e=this.getLength(),i=t.getLength();if(e-i<0)return this;for(var n=new Array(e),a=0;a<e;a++)n[a]=this.get(a);while(n.length>=i){var o=r.glog(n[0])-r.glog(t.get(0));for(a=0;a<t.getLength();a++)n[a]^=r.gexp(r.glog(t.get(a))+o);while(0==n[0])n.shift()}return new u(n,0)}};var l=[[1,26,19],[1,26,16],[1,26,13],[1,26,9],[1,44,34],[1,44,28],[1,44,22],[1,44,16],[1,70,55],[1,70,44],[2,35,17],[2,35,13],[1,100,80],[2,50,32],[2,50,24],[4,25,9],[1,134,108],[2,67,43],[2,33,15,2,34,16],[2,33,11,2,34,12],[2,86,68],[4,43,27],[4,43,19],[4,43,15],[2,98,78],[4,49,31],[2,32,14,4,33,15],[4,39,13,1,40,14],[2,121,97],[2,60,38,2,61,39],[4,40,18,2,41,19],[4,40,14,2,41,15],[2,146,116],[3,58,36,2,59,37],[4,36,16,4,37,17],[4,36,12,4,37,13],[2,86,68,2,87,69],[4,69,43,1,70,44],[6,43,19,2,44,20],[6,43,15,2,44,16],[4,101,81],[1,80,50,4,81,51],[4,50,22,4,51,23],[3,36,12,8,37,13],[2,116,92,2,117,93],[6,58,36,2,59,37],[4,46,20,6,47,21],[7,42,14,4,43,15],[4,133,107],[8,59,37,1,60,38],[8,44,20,4,45,21],[12,33,11,4,34,12],[3,145,115,1,146,116],[4,64,40,5,65,41],[11,36,16,5,37,17],[11,36,12,5,37,13],[5,109,87,1,110,88],[5,65,41,5,66,42],[5,54,24,7,55,25],[11,36,12],[5,122,98,1,123,99],[7,73,45,3,74,46],[15,43,19,2,44,20],[3,45,15,13,46,16],[1,135,107,5,136,108],[10,74,46,1,75,47],[1,50,22,15,51,23],[2,42,14,17,43,15],[5,150,120,1,151,121],[9,69,43,4,70,44],[17,50,22,1,51,23],[2,42,14,19,43,15],[3,141,113,4,142,114],[3,70,44,11,71,45],[17,47,21,4,48,22],[9,39,13,16,40,14],[3,135,107,5,136,108],[3,67,41,13,68,42],[15,54,24,5,55,25],[15,43,15,10,44,16],[4,144,116,4,145,117],[17,68,42],[17,50,22,6,51,23],[19,46,16,6,47,17],[2,139,111,7,140,112],[17,74,46],[7,54,24,16,55,25],[34,37,13],[4,151,121,5,152,122],[4,75,47,14,76,48],[11,54,24,14,55,25],[16,45,15,14,46,16],[6,147,117,4,148,118],[6,73,45,14,74,46],[11,54,24,16,55,25],[30,46,16,2,47,17],[8,132,106,4,133,107],[8,75,47,13,76,48],[7,54,24,22,55,25],[22,45,15,13,46,16],[10,142,114,2,143,115],[19,74,46,4,75,47],[28,50,22,6,51,23],[33,46,16,4,47,17],[8,152,122,4,153,123],[22,73,45,3,74,46],[8,53,23,26,54,24],[12,45,15,28,46,16],[3,147,117,10,148,118],[3,73,45,23,74,46],[4,54,24,31,55,25],[11,45,15,31,46,16],[7,146,116,7,147,117],[21,73,45,7,74,46],[1,53,23,37,54,24],[19,45,15,26,46,16],[5,145,115,10,146,116],[19,75,47,10,76,48],[15,54,24,25,55,25],[23,45,15,25,46,16],[13,145,115,3,146,116],[2,74,46,29,75,47],[42,54,24,1,55,25],[23,45,15,28,46,16],[17,145,115],[10,74,46,23,75,47],[10,54,24,35,55,25],[19,45,15,35,46,16],[17,145,115,1,146,116],[14,74,46,21,75,47],[29,54,24,19,55,25],[11,45,15,46,46,16],[13,145,115,6,146,116],[14,74,46,23,75,47],[44,54,24,7,55,25],[59,46,16,1,47,17],[12,151,121,7,152,122],[12,75,47,26,76,48],[39,54,24,14,55,25],[22,45,15,41,46,16],[6,151,121,14,152,122],[6,75,47,34,76,48],[46,54,24,10,55,25],[2,45,15,64,46,16],[17,152,122,4,153,123],[29,74,46,14,75,47],[49,54,24,10,55,25],[24,45,15,46,46,16],[4,152,122,18,153,123],[13,74,46,32,75,47],[48,54,24,14,55,25],[42,45,15,32,46,16],[20,147,117,4,148,118],[40,75,47,7,76,48],[43,54,24,22,55,25],[10,45,15,67,46,16],[19,148,118,6,149,119],[18,75,47,31,76,48],[34,54,24,34,55,25],[20,45,15,61,46,16]];function c(){this.buffer=new Array,this.length=0}e.prototype.getRightType=function(){for(var t=1;t<41;t++){var e=l[4*(t-1)+this.errorCorrectLevel];if(void 0==e)throw new Error("bad rs block @ typeNumber:"+t+"/errorCorrectLevel:"+this.errorCorrectLevel);for(var i=e.length/3,n=0,a=0;a<i;a++){var o=e[3*a+0],r=e[3*a+2];n+=r*o}var s=t>9?2:1;if(this.utf8bytes.length+s<n||40==t){this.typeNumber=t,this.rsBlock=e,this.totalDataCount=n;break}}},c.prototype={get:function(t){var e=Math.floor(t/8);return this.buffer[e]>>>7-t%8&1},put:function(t,e){for(var i=0;i<e;i++)this.putBit(t>>>e-i-1&1)},putBit:function(t){var e=Math.floor(this.length/8);this.buffer.length<=e&&this.buffer.push(0),t&&(this.buffer[e]|=128>>>this.length%8),this.length++}};var d=[];a=function(t){if(this.options={text:"",size:256,correctLevel:3,background:"#ffffff",foreground:"#000000",pdground:"#000000",image:"",imageSize:30,canvasId:t.canvasId,context:t.context,usingComponents:t.usingComponents,showLoading:t.showLoading,loadingText:t.loadingText},"string"===typeof t&&(t={text:t}),t)for(var i in t)this.options[i]=t[i];for(var n=null,a=(i=0,d.length);i<a;i++)if(d[i].text==this.options.text&&d[i].text.correctLevel==this.options.correctLevel){n=d[i].obj;break}i==a&&(n=new e(this.options.text,this.options.correctLevel),d.push({text:this.options.text,correctLevel:this.options.correctLevel,obj:n}));var o=function(t){var e=t.options;return e.pdground&&(t.row>1&&t.row<5&&t.col>1&&t.col<5||t.row>t.count-6&&t.row<t.count-2&&t.col>1&&t.col<5||t.row>1&&t.row<5&&t.col>t.count-6&&t.col<t.count-2)?e.pdground:e.foreground};(function(t){t.showLoading&&uni.showLoading({title:t.loadingText,mask:!0});for(var e=uni.createCanvasContext(t.canvasId,t.context),i=n.getModuleCount(),a=t.size,r=t.imageSize,s=(a/i).toPrecision(4),u=(a/i).toPrecision(4),l=0;l<i;l++)for(var c=0;c<i;c++){var d=Math.ceil((c+1)*s)-Math.floor(c*s),f=Math.ceil((l+1)*s)-Math.floor(l*s),v=o({row:l,col:c,count:i,options:t});e.setFillStyle(n.modules[l][c]?v:t.background),e.fillRect(Math.round(c*s),Math.round(l*u),d,f)}if(t.image){var h=Number(((a-r)/2).toFixed(2)),p=Number(((a-r)/2).toFixed(2));(function(e,i,n,a,o,r,s,u,l){e.setLineWidth(s),e.setFillStyle(t.background),e.setStrokeStyle(t.background),e.beginPath(),e.moveTo(i+r,n),e.arcTo(i+a,n,i+a,n+r,r),e.arcTo(i+a,n+o,i+a-r,n+o,r),e.arcTo(i,n+o,i,n+o-r,r),e.arcTo(i,n,i+r,n,r),e.closePath(),u&&e.fill(),l&&e.stroke()})(e,h,p,r,r,2,6,!0,!0),e.drawImage(t.image,h,p,r,r)}setTimeout((function(){e.draw(!0,(function(){setTimeout((function(){uni.canvasToTempFilePath({width:t.width,height:t.height,destWidth:t.width,destHeight:t.height,canvasId:t.canvasId,quality:Number(1),success:function(e){t.cbResult&&t.cbResult(e.tempFilePath)},fail:function(e){t.cbResult&&t.cbResult(e)},complete:function(){t.showLoading&&uni.hideLoading()}},t.context)}),t.text.length+100)}))}),t.usingComponents?0:150)})(this.options)},a.prototype.clear=function(t){var e=uni.createCanvasContext(this.options.canvasId,this.options.context);e.clearRect(0,0,this.options.size,this.options.size),e.draw(!1,(function(){t&&t()}))}})();var o=a;e.default=o},"822c":function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n={data:function(){return{}},props:{type:{type:String,default:"fixed"},backgroundColor:{type:String,default:"#fff"}},methods:{}};e.default=n},8551:function(t,e,i){var n=i("92ea");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=i("4f06").default;a("fd46f924",n,!0,{sourceMap:!1,shadowMode:!1})},8805:function(t,e,i){var n=i("24fb");e=n(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.bt[data-v-8854b6f8]{width:100%;text-align:right}.order-details[data-v-8854b6f8]{position:relative;padding-bottom:calc(%?120?% + env(safe-area-inset-bottom))}.order-details .header-bg[data-v-8854b6f8]{position:absolute;top:0;width:100%;height:%?200?%;background-color:#ff2c3c;z-index:0}.order-details .goods .status[data-v-8854b6f8]{height:%?88?%;padding:0 %?20?%}.order-details .main[data-v-8854b6f8]{position:relative;z-index:1}.order-details .contain[data-v-8854b6f8]{margin:0 %?20?% %?20?%;border-radius:%?14?%;background-color:#fff}.order-details .header[data-v-8854b6f8]{padding:%?24?% %?40?%;box-sizing:border-box}.order-details .img-line[data-v-8854b6f8]{height:1.5px;width:100%;display:block}.order-details .address-wrap[data-v-8854b6f8]{height:%?164?%;padding:0 %?24?%}.order-details .order-info[data-v-8854b6f8]{padding:%?12?% 0}.order-details .order-info .item[data-v-8854b6f8]{padding:%?12?% %?24?%}.order-details .order-info .copy-btn[data-v-8854b6f8]{font-size:%?24?%;padding:%?6?% %?18?%;border-radius:%?8?%;color:#ff2c3c;background:rgba(255,44,60,.1)}.order-details .order-info .item .title[data-v-8854b6f8]{width:%?180?%;flex:none}.receive-qr[data-v-8854b6f8]{display:flex;flex-direction:column;align-items:center;justify-content:center;min-height:%?460?%}.qr-contain[data-v-8854b6f8]{box-sizing:border-box;display:flex;justify-content:center;align-items:center;border-radius:5px}.qr-container[data-v-8854b6f8]{box-sizing:border-box;display:flex;justify-content:center;align-items:center;width:140px;height:140px;padding:8px;border-radius:5px}.qr-contain--die[data-v-8854b6f8]{position:relative}.qr-contain--die[data-v-8854b6f8]::before{position:absolute;z-index:99;top:0;left:0;right:0;bottom:0;display:block;content:"";background-color:hsla(0,0%,100%,.5)}.qr-code[data-v-8854b6f8]{padding:%?8?% %?30?%;border-radius:60px;background-color:#f6f6f6}.order-details .price > uni-view[data-v-8854b6f8]{height:%?60?%;padding:0 %?24?%}.order-details .footer[data-v-8854b6f8]{position:fixed;bottom:0;left:0;right:0;height:%?100?%;padding:0 %?24?%;box-sizing:initial;padding-bottom:env(safe-area-inset-bottom)}.footer .plain[data-v-8854b6f8]{border:1px solid #bbb}.footer .plain.red[data-v-8854b6f8]{border:1px solid #ff2c3c}.tips-dialog[data-v-8854b6f8]{height:%?230?%;width:100%}.order-details .invite-btn[data-v-8854b6f8]{background:linear-gradient(270deg,#ff2c3c,#f95f2f);margin:%?30?% %?26?% %?40?%}.image-code[data-v-8854b6f8]{width:%?72?%;height:%?72?%}.more[data-v-8854b6f8]{position:relative}.more .more-container[data-v-8854b6f8]{width:%?200?%;bottom:%?70?%;left:%?-20?%;position:absolute;border:1px solid #e9e9e9}.more .more-container .more-item[data-v-8854b6f8]{padding:%?10?% %?20?%;text-align:center;border-top:1px solid #e9e9e9}.more .more-container .more-item[data-v-8854b6f8]:first-child{border-top:0}',""]),t.exports=e},"8aa9":function(t,e,i){"use strict";i.r(e);var n=i("fb1d"),a=i.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);e["default"]=a.a},"8aee":function(t,e,i){var n=i("b9d8");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=i("4f06").default;a("901755d2",n,!0,{sourceMap:!1,shadowMode:!1})},"8fed":function(t,e,i){"use strict";var n=i("e2db"),a=i.n(n);a.a},"92ea":function(t,e,i){var n=i("24fb");e=n(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.u-countdown[data-v-38cbd707]{display:inline-flex;align-items:center}.u-countdown-item[data-v-38cbd707]{display:flex;flex-direction:row;align-items:center;justify-content:center;padding:%?2?%;border-radius:%?6?%;white-space:nowrap;-webkit-transform:translateZ(0);transform:translateZ(0)}.u-countdown-time[data-v-38cbd707]{margin:0;padding:0}.u-countdown-colon[data-v-38cbd707]{display:flex;flex-direction:row;justify-content:center;padding:0 %?5?%;line-height:1;align-items:center;padding-bottom:%?4?%}.u-countdown-scale[data-v-38cbd707]{-webkit-transform:scale(.9);transform:scale(.9);-webkit-transform-origin:center center;transform-origin:center center}',""]),t.exports=e},9415:function(t,e,i){"use strict";var n=i("8aee"),a=i.n(n);a.a},"95a6":function(t,e,i){"use strict";i.r(e);var n=i("ac23"),a=i("8aa9");for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);i("2698");var r=i("f0c5"),s=Object(r["a"])(a["default"],n["b"],n["c"],!1,null,"705e9a38",null,!1,n["a"],void 0);e["default"]=s.exports},"985a":function(t,e,i){"use strict";var n=i("8551"),a=i.n(n);a.a},a65b:function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){return n}));var n={uCountDown:i("7226").default,shopTitle:i("95a6").default,orderGoods:i("d9ab").default,priceFormat:i("fefe").default,loadingView:i("2875").default,orderDialog:i("a7f3").default,floatTab:i("65c2").default,uPopup:i("5cc5").default,tkiQrcode:i("1ddf").default},a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",[i("v-uni-view",{staticClass:"order-details"},[i("v-uni-view",{staticClass:"header-bg"}),i("v-uni-view",{staticClass:"main"},[i("v-uni-view",{staticClass:"header"},[0==t.orderDetail.order_status?i("v-uni-view",{staticClass:"item"},[4!=t.orderDetail.pay_way_base?i("v-uni-view",{staticClass:"white lg m-b-10"},[t._v("等待买家付款")]):i("v-uni-view",{staticClass:"white lg m-b-10"},[t._v("等待商家处理，请耐心等待")]),t.cancelTime>0?i("v-uni-view",{staticClass:"white sm flex"},[t._v("支付剩余"),i("u-count-down",{attrs:{separator:"zh",timestamp:t.cancelTime,"separator-color":"#fff",color:"#fff","separator-size":26,"font-size":26,"bg-color":"transparent"},on:{end:function(e){arguments[0]=e=t.$handleEvent(e),t.getOrderDetailFun.apply(void 0,arguments)}}}),t._v("自动关闭")],1):t._e()],1):t._e(),1!=t.orderDetail.order_status||t.orderDetail.pickup_code?t._e():i("v-uni-view",{staticClass:"item"},[i("v-uni-view",{staticClass:"white lg m-b-10"},[t._v("等待商家发货")]),i("v-uni-view",{staticClass:"white sm"},[t._v("您的商品正在打包中，请耐心等待…")])],1),2==t.orderDetail.order_status&&t.orderDetail.pickup_code?i("v-uni-view",{staticClass:"item"},[i("v-uni-view",{staticClass:"white lg m-b-10"},[t._v("待取货")]),i("v-uni-view",{staticClass:"white sm"},[t._v("请前往门店取货")])],1):t._e(),2!=t.orderDetail.order_status||t.orderDetail.pickup_code?t._e():i("v-uni-view",{staticClass:"item"},[i("v-uni-view",{staticClass:"white lg m-b-10"},[t._v("已发货")]),i("v-uni-view",{staticClass:"white sm"},[t._v("您的商品正在路中，请耐心等待…")])],1),3==t.orderDetail.order_status?i("v-uni-view",{staticClass:"item"},[i("v-uni-view",{staticClass:"white lg m-b-10"},[t._v("已完成")]),i("v-uni-view",{staticClass:"white sm"},[t._v("商品已签收，期待再次购买！")])],1):t._e(),4==t.orderDetail.order_status?i("v-uni-view",{staticClass:"item"},[i("v-uni-view",{staticClass:"white lg m-b-10"},[t._v("订单已关闭")])],1):t._e()],1),1!==t.orderDetail.delivery_type?i("v-uni-view",{staticClass:"address-wrap flex contain"},[i("v-uni-image",{staticClass:"icon-md m-r-20",attrs:{src:"/static/images/icon_address.png"}}),2==t.orderDetail.delivery_type?i("v-uni-view",{staticClass:"address"},[i("v-uni-view",{staticClass:"lg bold"},[t._v("门店自提点："+t._s(t.orderDetail.shop.name))]),i("v-uni-view",{staticClass:"m-t-10"},[t._v(t._s(t.orderDetail.shop_address))])],1):i("v-uni-view",{staticClass:"address"},[i("v-uni-view",[i("v-uni-text",{staticClass:"name md m-r-10"},[t._v(t._s(t.orderDetail.consignee))]),i("v-uni-text",{staticClass:"phone md"},[t._v(t._s(t.orderDetail.mobile))]),i("v-uni-view",{staticClass:"area sm m-t-10 lighter"},[t._v(t._s(t.orderDetail.delivery_address))])],1)],1)],1):t._e(),t.showCode?i("v-uni-view",{staticClass:"order-info contain"},[i("v-uni-view",{staticClass:"item flex",staticStyle:{"align-items":"flex-start",width:"100%"}},[i("v-uni-view",{staticClass:"xs"},[i("v-uni-view",{staticClass:"title"},[t._v("门店营业时间")]),i("v-uni-view",{staticClass:"title m-t-10",staticStyle:{width:"100%"}},[t._l(t.orderDetail.shop.weekdays,(function(e){return i("v-uni-text",{directives:[{name:"show",rawName:"v-show",value:7!=t.orderDetail.shop.weekdays.length,expression:"orderDetail.shop.weekdays.length != 7"}],key:e,staticClass:"m-r-10"},[t._v(t._s(t.getWeek(e)))])})),t._v(t._s(t.orderDetail.shop.run_start_time)+" ~\n                                "+t._s(t.orderDetail.shop.run_end_time))],2)],1),i("v-uni-view",{ref:"qr-image",staticClass:"qr-contain",class:{"qr-contain--die":t.orderDetail.verification_status},staticStyle:{"margin-left":"auto"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.showPopup=!0}}},[i("v-uni-image",{staticClass:"image-code",attrs:{src:"/bundle/static/scen_code.png"}})],1)],1)],1):t._e(),t.team.status_text?i("v-uni-view",{staticClass:"order-info contain"},[i("v-uni-view",{staticClass:"item flex",staticStyle:{"align-items":"flex-start"}},[i("v-uni-view",{staticClass:"title"},[t._v("拼购状态")]),i("v-uni-view",{staticClass:"bt"},[t._v(t._s(t.team.status_text))])],1)],1):t._e(),i("v-uni-view",{staticClass:"goods contain"},[i("v-uni-view",{staticClass:"m-l-20"},[i("shop-title",{attrs:{shop:t.orderDetail.shop}})],1),i("order-goods",{attrs:{team:t.team,link:!0,isJumpGoods:!0,list:t.orderDetail.order_goods}})],1),t.orderDetail.delivery_content?[i("v-uni-view",{staticClass:"order-info contain",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.copy(t.orderDetail.delivery_content)}}},[i("v-uni-view",{staticClass:"item"},[i("v-uni-view",{staticClass:"black",staticStyle:{"word-break":"break-all"}},[t._v(t._s(t.orderDetail.delivery_content||"无"))]),i("v-uni-view",{staticClass:"flex row-right m-t-30"},[i("v-uni-view",{staticClass:"copy-btn"},[t._v("复制")])],1)],1)],1)]:t._e(),2==t.orderDetail.delivery_type?i("v-uni-view",{staticClass:"contain receive"}):t._e(),i("v-uni-view",{staticClass:"price contain"},[i("v-uni-view",{staticClass:"flex row-between"},[i("v-uni-view",[t._v("商品金额")]),i("v-uni-view",{staticClass:"black"},[i("price-format",{attrs:{price:t.orderDetail.goods_price}})],1)],1),i("v-uni-view",{staticClass:"flex row-between"},[i("v-uni-view",[t._v("运费")]),i("v-uni-view",{staticClass:"black"},[t._v("+"),i("price-format",{attrs:{price:t.orderDetail.shipping_price}})],1)],1),0!=t.orderDetail.discount_amount?i("v-uni-view",{staticClass:"flex row-between"},[i("v-uni-view",[t._v("优惠券")]),i("v-uni-view",{staticClass:"primary"},[t._v("-"),i("price-format",{attrs:{price:t.orderDetail.discount_amount}})],1)],1):t._e(),t.orderDetail.member_amount?i("v-uni-view",{staticClass:"flex row-between"},[i("v-uni-view",[t._v("会员抵扣")]),i("v-uni-view",{staticClass:"primary"},[t._v("-"),i("price-format",{attrs:{price:t.orderDetail.member_amount}})],1)],1):t._e(),i("v-uni-view",{staticClass:"flex row-right"},[i("v-uni-view",{staticClass:"lighter"},[t._v("实付金额：")]),i("v-uni-view",{staticClass:"primary xl"},[i("price-format",{attrs:{weight:"500","first-size":34,"second-size":34,price:t.orderDetail.order_amount}})],1)],1)],1),i("v-uni-view",{staticClass:"order-info contain"},[i("v-uni-view",{staticClass:"item flex",staticStyle:{"align-items":"flex-start"}},[i("v-uni-view",{staticClass:"title"},[t._v("买家留言")]),i("v-uni-view",{staticClass:"black"},[t._v(t._s(t.orderDetail.user_remark||"无"))])],1)],1),i("v-uni-view",{staticClass:"order-info contain"},[i("v-uni-view",{staticClass:"item flex"},[i("v-uni-view",{staticClass:"title"},[t._v("订单编号")]),i("v-uni-view",{staticClass:"black"},[t._v(t._s(t.orderDetail.order_sn))])],1),i("v-uni-view",{staticClass:"item flex"},[i("v-uni-view",{staticClass:"title"},[t._v("订单类型")]),i("v-uni-view",{staticClass:"black"},[t._v(t._s(t.orderDetail.order_type))])],1),i("v-uni-view",{staticClass:"item flex"},[i("v-uni-view",{staticClass:"title"},[t._v("支付方式")]),i("v-uni-view",{staticClass:"black"},[t._v(t._s(t.orderDetail.pay_way))])],1),i("v-uni-view",{staticClass:"item flex"},[i("v-uni-view",{staticClass:"title"},[t._v("下单时间")]),i("v-uni-view",{staticClass:"black"},[t._v(t._s(t.orderDetail.create_time))])],1),t.orderDetail.pay_time?i("v-uni-view",{staticClass:"item flex"},[i("v-uni-view",{staticClass:"title"},[t._v("付款时间")]),i("v-uni-view",{staticClass:"black"},[t._v(t._s(t.orderDetail.pay_time))])],1):t._e(),t.orderDetail.shipping_time?i("v-uni-view",{staticClass:"item flex"},[i("v-uni-view",{staticClass:"title"},[t._v("发货时间")]),i("v-uni-view",{staticClass:"black"},[t._v(t._s(t.orderDetail.shipping_time))])],1):t._e(),t.orderDetail.confirm_take_time?i("v-uni-view",{staticClass:"item flex"},[i("v-uni-view",{staticClass:"title"},[t._v("成交时间")]),i("v-uni-view",{staticClass:"black"},[t._v(t._s(t.orderDetail.confirm_take_time))])],1):t._e(),t.orderDetail.cancel_time?i("v-uni-view",{staticClass:"item flex"},[i("v-uni-view",{staticClass:"title"},[t._v("关闭时间")]),i("v-uni-view",{staticClass:"black"},[t._v(t._s(t.orderDetail.cancel_time))])],1):t._e()],1),t.orderDetail.cancel_btn||t.orderDetail.delivery_btn||t.orderDetail.take_btn||t.orderDetail.del_btn||t.orderDetail.pay_btn||t.orderDetail.view_invoice_btn||t.orderDetail.save_invoice_btn?i("v-uni-view",{staticClass:"footer bg-white flex fixed row-right"},[i("v-uni-view",{staticClass:"flex"},[t.orderDetail.cancel_btn?i("v-uni-view",[i("v-uni-button",{staticClass:"plain br60",attrs:{size:"sm","hover-class":"none"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.cancelOrder.apply(void 0,arguments)}}},[t._v("取消订单")])],1):t._e(),t.orderDetail.view_invoice_btn&&t.orderDetail.shop.open_invoice?i("v-uni-navigator",{staticClass:"m-l-20",attrs:{"hover-class":"none",url:"/bundle/pages/invoice_detail/invoice_detail?id="+t.orderDetail.id}},[i("v-uni-button",{staticClass:"plain br60",attrs:{size:"sm","hover-class":"none"}},[t._v("查看发票")])],1):t._e(),t.orderDetail.save_invoice_btn&&t.orderDetail.shop.open_invoice&&4!==t.orderDetail.order_status?i("v-uni-navigator",{staticClass:"m-l-20",attrs:{"hover-class":"none",url:"/bundle/pages/invoice/invoice?shop_id="+t.orderDetail.shop.id+"&order_id="+t.orderDetail.id+"&type="+t.invoiceType}},[i("v-uni-button",{staticClass:"plain br60",attrs:{size:"sm","hover-class":"none"}},[t._v("申请开票")])],1):t._e(),t.orderDetail.delivery_btn?i("v-uni-navigator",{staticClass:"m-l-20",attrs:{"hover-class":"none",url:"/bundle/pages/goods_logistics/goods_logistics?id="+t.orderDetail.id}},[i("v-uni-button",{staticClass:"plain br60",attrs:{size:"sm","hover-class":"none"}},[t._v("查看物流")])],1):t._e(),t.orderDetail.take_btn?i("v-uni-view",{staticClass:"m-l-20"},[i("v-uni-button",{staticClass:"plain br60 primary red",attrs:{size:"sm","hover-class":"none"},on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.comfirmOrder.apply(void 0,arguments)}}},[t._v("确认收货")])],1):t._e(),t.orderDetail.del_btn?i("v-uni-view",{staticClass:"m-l-20"},[i("v-uni-button",{staticClass:"plain br60",attrs:{size:"sm","hover-class":"none"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.delOrder.apply(void 0,arguments)}}},[t._v("删除订单")])],1):t._e(),t.orderDetail.pay_btn&&4!=t.orderDetail.pay_way_base?i("v-uni-view",{staticClass:"m-l-20"},[i("v-uni-button",{staticClass:"bg-primary br60 white",attrs:{size:"sm"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.payNow.apply(void 0,arguments)}}},[t._v("立即付款")])],1):t._e()],1)],1):t._e()],2)],1),t.isFirstLoading?i("loading-view"):t._e(),i("order-dialog",{ref:"orderDialog",attrs:{orderId:t.orderDetail.id,type:t.type},on:{confirm:function(e){arguments[0]=e=t.$handleEvent(e),t.confirmDialog.apply(void 0,arguments)}}}),t.showLoading?i("loading-view",{attrs:{"background-color":"transparent",size:50}}):t._e(),i("float-tab"),i("u-popup",{attrs:{mode:"bottom","border-radius":"14",closeable:!1,"safe-area-inset-bottom":!0,"mask-close-able":!0},model:{value:t.showPopup,callback:function(e){t.showPopup=e},expression:"showPopup"}},[i("v-uni-view",{staticClass:"receive-qr"},[i("v-uni-text",{staticClass:"xs lighter"},[t._v("请凭二维码取货")]),i("v-uni-view",{ref:"qr-image",staticClass:"m-t-20 qr-container",class:{"qr-contain--die":t.orderDetail.verification_status}},[t.showPopup?i("tki-qrcode",{ref:"qrcode",attrs:{uni:"px",val:t.orderDetail.pickup_code,size:236,showLoading:!1}}):t._e()],1),i("v-uni-view",{staticClass:"m-t-60 xs black qr-code"},[t._v("提货码："+t._s(t.orderDetail.pickup_code))])],1)],1)],1)},o=[]},a712:function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){return n}));var n={uLoading:i("8fc0").default},a=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{class:"loading "+("flex"==this.type?"flex":""),style:{backgroundColor:this.backgroundColor}},[e("u-loading",{attrs:{mode:"flower",size:60}})],1)},o=[]},a7f3:function(t,e,i){"use strict";i.r(e);var n=i("3c87"),a=i("4d65");for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);var r=i("f0c5"),s=Object(r["a"])(a["default"],n["b"],n["c"],!1,null,"15d7f11b",null,!1,n["a"],void 0);e["default"]=s.exports},abf6:function(t,e,i){"use strict";var n=i("e7ac"),a=i.n(n);a.a},ac23:function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){return n}));var n={uIcon:i("90f3").default},a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"shop-title flex",on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.toShop.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"shop-name line-1 bold"},[t._v(t._s(t.shop.shop_name||t.shop.name||t.name))]),t.isLink?i("u-icon",{staticClass:"m-l-10 m-r-20",attrs:{name:"arrow-right",size:"28"}}):t._e()],1)},o=[]},ad6f:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){}));var n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"float-tab ~column"},[i("v-uni-navigator",{staticClass:"tab-img",style:{top:3*t.top+"px"},attrs:{"hover-class":"none","open-type":"switchTab",url:"/pages/index/index"}},[i("v-uni-image",{staticClass:"tab-icon",attrs:{src:"/static/images/icon_float_home.png"}})],1),i("v-uni-navigator",{staticClass:"tab-img",style:{top:2*t.top+"px"},attrs:{"hover-class":"none","open-type":"navigate",url:"/bundle/pages/chat/chat"}},[i("v-uni-image",{staticClass:"tab-icon",attrs:{src:"/static/images/icon_float_help.png"}})],1),i("v-uni-navigator",{staticClass:"tab-img",style:{top:t.top+"px"},attrs:{"hover-class":"none","open-type":"switchTab",url:"/pages/shop_cart/shop_cart"}},[i("v-uni-image",{staticClass:"tab-icon",attrs:{src:"/static/images/icon_float_cart.png"}})],1),i("v-uni-image",{staticClass:"tab-img",staticStyle:{"z-index":"99"},style:{transform:"rotateZ("+(t.showMore?135:0)+"deg)"},attrs:{src:"/static/images/icon_float_more.png"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onChange.apply(void 0,arguments)}}})],1)},a=[]},b0dc:function(t,e,i){"use strict";i("7a82");var n=i("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.cancelOrder=function(t){return a.default.post("order/cancel",{id:t})},e.confirmOrder=function(t){return a.default.post("order/confirm",{id:t})},e.delOrder=function(t){return a.default.post("order/del",{id:t})},e.getOrderDetail=function(t){return a.default.get("order/getOrderDetail",{params:{id:t}})},e.getOrderList=function(t){return a.default.get("order/lists",{params:t})},e.getPayResult=function(t){return a.default.get("order/pay_result",{params:t})},e.getwechatSyncCheck=function(t){return a.default.get("order/wechatSyncCheck",{params:t})},e.getwxReceiveDetail=function(t){return a.default.get("order/wxReceiveDetail",{params:t})},e.orderBuy=function(t){return a.default.post("order/submitOrder",t)},e.orderInfo=function(t){return a.default.post("order/settlement",t)},e.orderTraces=function(t){return a.default.get("order/orderTraces",{params:{id:t}})};var a=n(i("3b33"));i("b08d")},b83e:function(t,e,i){var n=i("24fb");e=n(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.loading[data-v-061dd044]{position:fixed;top:0;left:0;width:100vw;height:100vh;z-index:9999;display:flex;justify-content:center;align-items:center}.loading.flex[data-v-061dd044]{position:static;flex:1;width:100%}.loading .loading-img[data-v-061dd044]{width:%?100?%;height:%?100?%}',""]),t.exports=e},b9d8:function(t,e,i){var n=i("24fb");e=n(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.float-tab[data-v-130bc95c]{position:fixed;right:%?16?%;bottom:%?200?%;width:%?96?%;height:%?96?%;z-index:777}.float-tab .tab-img[data-v-130bc95c]{width:100%;height:100%;position:absolute;transition:all .5s}.float-tab .tab-img .tab-icon[data-v-130bc95c]{width:100%;height:100%}',""]),t.exports=e},be6a:function(t,e,i){"use strict";i("7a82");var n=i("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=n(i("0122"));i("a9e3"),i("e9c4");var o,r=n(i("7fea")),s={name:"tki-qrcode",props:{cid:{type:String,default:"tki-qrcode-canvas"},size:{type:Number,default:200},unit:{type:String,default:"upx"},show:{type:Boolean,default:!0},val:{type:String,default:""},background:{type:String,default:"#ffffff"},foreground:{type:String,default:"#000000"},pdground:{type:String,default:"#000000"},icon:{type:String,default:""},iconSize:{type:Number,default:40},lv:{type:Number,default:3},onval:{type:Boolean,default:!1},loadMake:{type:Boolean,default:!1},usingComponents:{type:Boolean,default:!0},showLoading:{type:Boolean,default:!0},loadingText:{type:String,default:"二维码生成中"}},data:function(){return{result:""}},methods:{_makeCode:function(){var t=this;this._empty(this.val)?uni.showToast({title:"二维码内容不能为空",icon:"none",duration:2e3}):(o=new r.default({context:t,canvasId:t.cid,usingComponents:t.usingComponents,showLoading:t.showLoading,loadingText:t.loadingText,text:t.val,size:t.cpSize,background:t.background,foreground:t.foreground,pdground:t.pdground,correctLevel:t.lv,image:t.icon,imageSize:t.iconSize,cbResult:function(e){t._result(e)}}),console.log(o,111))},_clearCode:function(){this._result(""),o.clear()},_saveCode:function(){""!=this.result&&uni.saveImageToPhotosAlbum({filePath:this.result,success:function(){uni.showToast({title:"二维码保存成功",icon:"success",duration:2e3})}})},_result:function(t){this.result=t,this.$emit("result",t)},_empty:function(t){var e=(0,a.default)(t),i=!1;return"number"==e&&""==String(t)||"undefined"==e?i=!0:"object"==e?"{}"!=JSON.stringify(t)&&"[]"!=JSON.stringify(t)&&null!=t||(i=!0):"string"==e?""!=t&&"undefined"!=t&&"null"!=t&&"{}"!=t&&"[]"!=t||(i=!0):"function"==e&&(i=!1),i}},watch:{size:function(t,e){var i=this;t==e||this._empty(t)||(this.cSize=t,this._empty(this.val)||setTimeout((function(){i._makeCode()}),100))},val:function(t,e){var i=this;this.onval&&(t==e||this._empty(t)||setTimeout((function(){i._makeCode()}),0))}},computed:{cpSize:function(){return"upx"==this.unit?uni.upx2px(this.size):this.size}},mounted:function(){var t=this;this.loadMake||this._empty(this.val)||setTimeout((function(){t._makeCode()}),0)}};e.default=s},bf65:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){}));var n=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{staticClass:"tki-qrcode"},[e("v-uni-canvas",{staticClass:"tki-qrcode-canvas",style:{width:this.cpSize+"px",height:this.cpSize+"px"},attrs:{"canvas-id":this.cid}}),e("v-uni-image",{directives:[{name:"show",rawName:"v-show",value:this.show,expression:"show"}],style:{width:this.cpSize+"px",height:this.cpSize+"px"},attrs:{src:this.result}})],1)},a=[]},c13e:function(t,e,i){"use strict";i.r(e);var n=i("822c"),a=i.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);e["default"]=a.a},cb29:function(t,e,i){"use strict";var n=i("23e7"),a=i("81d5"),o=i("44d2");n({target:"Array",proto:!0},{fill:a}),o("fill")},dc47:function(t,e,i){"use strict";i.r(e);var n=i("be6a"),a=i.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);e["default"]=a.a},e2db:function(t,e,i){var n=i("b83e");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=i("4f06").default;a("fbf8b9f0",n,!0,{sourceMap:!1,shadowMode:!1})},e7ac:function(t,e,i){var n=i("347d");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=i("4f06").default;a("39b49848",n,!0,{sourceMap:!1,shadowMode:!1})},ec1d:function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("a9e3");var n={name:"u-count-down",props:{timestamp:{type:[Number,String],default:0},autoplay:{type:Boolean,default:!0},separator:{type:String,default:"colon"},separatorSize:{type:[Number,String],default:30},separatorColor:{type:String,default:"#303133"},color:{type:String,default:"#303133"},fontSize:{type:[Number,String],default:30},bgColor:{type:String,default:"#fff"},height:{type:[Number,String],default:"auto"},showBorder:{type:Boolean,default:!1},borderColor:{type:String,default:"#303133"},showSeconds:{type:Boolean,default:!0},showMinutes:{type:Boolean,default:!0},showHours:{type:Boolean,default:!0},showDays:{type:Boolean,default:!0},hideZeroDay:{type:Boolean,default:!1}},watch:{timestamp:function(t,e){this.clearTimer(),this.start()}},data:function(){return{d:"00",h:"00",i:"00",s:"00",timer:null,seconds:0}},computed:{itemStyle:function(){var t={};return this.height&&(t.height=this.height+"rpx"),this.showBorder&&(t.borderStyle="solid",t.borderColor=this.borderColor,t.borderWidth="1px"),this.bgColor&&(t.backgroundColor=this.bgColor),t},letterStyle:function(){var t={};return this.fontSize&&(t.fontSize=this.fontSize+"rpx"),this.color&&(t.color=this.color),t}},mounted:function(){this.autoplay&&this.timestamp&&this.start()},methods:{start:function(){var t=this;this.clearTimer(),this.timestamp<=0||(this.seconds=Number(this.timestamp),this.formatTime(this.seconds),this.timer=setInterval((function(){if(t.seconds--,t.$emit("change",t.seconds),t.seconds<0)return t.end();t.formatTime(t.seconds)}),1e3))},formatTime:function(t){t<=0&&this.end();var e,i=0,n=0,a=0;i=Math.floor(t/86400),e=Math.floor(t/3600)-24*i;var o=null;o=this.showDays?e:Math.floor(t/3600),n=Math.floor(t/60)-60*e-24*i*60,a=Math.floor(t)-24*i*60*60-60*e*60-60*n,o=o<10?"0"+o:o,n=n<10?"0"+n:n,a=a<10?"0"+a:a,i=i<10?"0"+i:i,this.d=i,this.h=o,this.i=n,this.s=a},end:function(){this.clearTimer(),this.$emit("end",{})},reset:function(){this.clearTimer(),this.seconds=Number(this.timestamp),this.s=this.timestamp,console.log(this.s)},clearTimer:function(){this.timer&&(clearInterval(this.timer),this.timer=null)}},beforeDestroy:function(){clearInterval(this.timer),this.timer=null}};e.default=n},f2f2:function(t,e,i){"use strict";i.r(e);var n=i("58cb"),a=i.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);e["default"]=a.a},fb1d:function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("14d9");var n={name:"shop-title",options:{virtualHost:!0},props:{name:{type:String},shop:{type:Object},isLink:{type:Boolean,default:!0}},data:function(){return{}},methods:{toShop:function(){var t=this.isLink,e=this.shop;t&&this.$Router.push({path:"/pages/store_index/store_index",query:{id:e.shop_id||e.id}})}}};e.default=n}}]);