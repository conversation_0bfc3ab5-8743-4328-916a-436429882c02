(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["bundle-pages-editRole-editRole"],{"5ffd":function(e,t,a){"use strict";a.d(t,"b",(function(){return i})),a.d(t,"c",(function(){return r})),a.d(t,"a",(function(){return n}));var n={uToast:a("341e").default},i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("v-uni-view",{staticClass:"padding-top-20"},[a("v-uni-view",{staticClass:"padding-about-20",staticStyle:{background:"#FFFFFF"}},[a("v-uni-view",{staticClass:"after-item"},[a("v-uni-view",{staticClass:"after-left"},[e._v("角色名称")]),a("v-uni-view",{staticClass:"after-right"},[a("v-uni-view",{staticClass:"input"},[a("v-uni-input",{staticClass:"font-size-26",attrs:{placeholder:"请输入角色名称",type:"text"},model:{value:e.name,callback:function(t){e.name=t},expression:"name"}})],1)],1)],1),a("v-uni-view",{staticClass:"after-item"},[a("v-uni-view",{staticClass:"after-left"},[e._v("角色权限")]),a("v-uni-view",{staticClass:"after-right"},[a("DaTreeVue2",{ref:"DaTreeRef",attrs:{data:e.roomTreeData,labelField:"title",valueField:"id",showCheckbox:!0,defaultCheckedKeys:e.defaultCheckedKeysValue},on:{change:function(t){arguments[0]=t=e.$handleEvent(t),e.handleTreeChange.apply(void 0,arguments)}}})],1)],1),a("v-uni-view",{staticClass:"after-item"},[a("v-uni-view",{staticClass:"after-left"},[e._v("角色说明")]),a("v-uni-view",{staticClass:"after-right textarea"},[a("v-uni-textarea",{staticClass:"font-size-26",staticStyle:{width:"100%",height:"100rpx"},attrs:{name:"",placeholder:"请输入角色说明",id:"",cols:"30",rows:"10"},model:{value:e.miaos,callback:function(t){e.miaos=t},expression:"miaos"}})],1)],1)],1),a("v-uni-view",{staticClass:"padding-about-20"},[a("v-uni-button",{staticClass:"login-btn white",attrs:{size:"lg"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.submit.apply(void 0,arguments)}}},[e._v("确定")])],1),a("u-toast",{ref:"uToast"})],1)},r=[]},"68f4":function(e,t,a){"use strict";var n=a("b139"),i=a.n(n);i.a},"8f80":function(e,t,a){var n=a("c86c");t=n(!1),t.push([e.i,"@charset \"UTF-8\";\n/* 颜色变量 */\n/** S Font's size **/\n/** E Font's size **/[data-v-744daa0e]:export{red_theme:#ff2c3c;orange_theme:#f7971e;pink_theme:#fa444d;gold_theme:#e0a356;blue_theme:#2f80ed;green_theme:#2ec840}[data-v-744daa0e] .mescroll-uni{height:100vh!important}.login-btn[data-v-744daa0e]{border-radius:%?80?%;margin:%?60?% 0 0;background-color:#40affa;font-size:%?28?%}.after-item[data-v-744daa0e]{display:flex;align-items:center;padding:%?20?% 0}.after-item .textarea[data-v-744daa0e]{padding:%?20?%;background:#f5f5f5;border-radius:%?20?%}.after-item .after-left[data-v-744daa0e]{width:%?150?%}.after-item .after-right[data-v-744daa0e]{flex:1}.after-item .after-right .input[data-v-744daa0e]{padding:%?20?%;border-radius:%?12?%;border:%?1?% solid #666}",""]),e.exports=t},a81c:function(e,t,a){"use strict";a("6a54");var n=a("3639").default,i=a("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=i(a("2634")),s=i(a("2fdc")),u=n(a("6bba")),o=i(a("f987")),d={components:{DaTreeVue2:o.default},data:function(){return{name:"",miaos:"",defaultCheckedKeysValue:[],roomTreeData:[],allSelectedKeys:[]}},methods:{submit:function(){var e=this;return(0,s.default)((0,r.default)().mark((function t(){var a;return(0,r.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return a={name:e.name,desc:e.miaos,auth_ids:e.allSelectedKeys},t.next=3,u.roleAdd(a);case 3:t.sent,e.$refs.uToast.show({title:"操作成功",type:"success"}),setTimeout((function(){uni.navigateBack()}),1e3);case 6:case"end":return t.stop()}}),t)})))()},handleTreeChange:function(e,t){console.log("handleTreeChange ==>",e),this.allSelectedKeys=e},roleAuthTree:function(){var e=this;return(0,s.default)((0,r.default)().mark((function t(){var a;return(0,r.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,u.roleAuthTree();case 2:a=t.sent,e.roomTreeData=a;case 4:case"end":return t.stop()}}),t)})))()},roleInfo:function(e){return(0,s.default)((0,r.default)().mark((function t(){var a;return(0,r.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return a={id:e},t.next=3,u.roleInfo(a);case 3:t.sent;case 4:case"end":return t.stop()}}),t)})))()}},onLoad:function(e){e.id&&this.roleInfo(e.id),uni.setNavigationBarTitle({title:e.id?"编辑角色":"添加角色"}),this.roleAuthTree()},onShow:function(){}};t.default=d},b139:function(e,t,a){var n=a("8f80");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[e.i,n,""]]),n.locals&&(e.exports=n.locals);var i=a("967d").default;i("9da8e0a2",n,!0,{sourceMap:!1,shadowMode:!1})},e2e9:function(e,t,a){"use strict";a.r(t);var n=a("a81c"),i=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=i.a},e613:function(e,t,a){"use strict";a.r(t);var n=a("5ffd"),i=a("e2e9");for(var r in i)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(r);a("68f4");var s=a("828b"),u=Object(s["a"])(i["default"],n["b"],n["c"],!1,null,"744daa0e",null,!1,n["a"],void 0);t["default"]=u.exports}}]);