# 微信回复管理功能升级说明

## 🎉 新功能概述

微信回复管理功能已升级，现在支持多媒体回复，包括：
- **文本回复**：传统的纯文本消息
- **图片回复**：单独的图片消息
- **文本+图片回复**：先发送文本，再发送图片（两条消息）

## 📋 功能特性

### 1. 多种内容类型
- **文本**：发送纯文本消息
- **图片**：发送单张图片
- **文本+图片**：发送文本消息后自动发送图片消息

### 2. 智能图片处理
- 自动上传图片到微信服务器获取临时素材ID
- 支持JPG/PNG格式，最大2MB
- 图片预览和管理功能

### 3. 优化的管理界面
- 直观的内容类型选择
- 实时预览图片
- 响应式设计，支持移动端操作

## 🚀 使用方法

### 添加新回复

1. **进入回复管理**
   ```
   后台 → 微信管理 → 回复管理 → 关键词回复
   ```

2. **选择内容类型**
   - **文本**：输入回复文本内容
   - **图片**：上传要回复的图片
   - **文本+图片**：输入文本内容 + 选择第二条消息类型（文本或图片）

3. **配置第二条消息**（仅文本+图片类型）
   - 选择第二条消息类型：文本或图片
   - 输入第二条消息内容或上传图片

### 编辑现有回复

1. **找到要编辑的回复**
   - 在列表中点击"编辑"按钮

2. **修改内容类型**
   - 可以在文本、图片、文本+图片之间切换
   - 系统会自动显示/隐藏相应的配置项

3. **保存更改**
   - 点击"确定"保存修改

## 📊 列表显示说明

### 新增显示项
- **消息数量**：显示该回复包含几条消息
  - `单条`：只有一条消息
  - `2条`：包含两条消息（文本+图片类型）

- **回复内容**：智能显示不同类型的内容
  - 文本：直接显示文本内容
  - 图片：显示图片缩略图
  - 文本+图片：显示文本内容 + 第二条消息标识

### 内容类型标识
- **文本**：纯文本内容
- **图片**：单独图片
- **文本+图片**：混合内容

## 🔧 技术实现

### 数据库字段
```sql
-- 新增字段
image_url           -- 图片URL地址
message_count       -- 消息数量
second_content      -- 第二条消息内容
second_content_type -- 第二条消息类型
second_image_url    -- 第二条消息图片URL
```

### 消息发送流程
1. **接收用户消息**
2. **匹配关键词**
3. **检查消息数量**
4. **发送第一条消息**（文本或图片）
5. **如果有第二条消息**：
   - 上传图片到微信服务器（如果是图片）
   - 通过客服消息接口发送第二条消息

## 📝 使用示例

### 示例1：进采购群功能
```
关键词：进采购群
内容类型：文本+图片
第一条消息：欢迎加入我们的采购群！请扫描下方二维码进群。
第二条消息类型：图片
第二条消息图片：采购群二维码.jpg
```

### 示例2：产品介绍
```
关键词：产品介绍
内容类型：文本+图片
第一条消息：我们的主打产品具有以下特点：高品质、低价格、快速发货。
第二条消息类型：图片
第二条消息图片：产品展示图.jpg
```

### 示例3：纯图片回复
```
关键词：价格表
内容类型：图片
图片：最新价格表.jpg
```

## ⚠️ 注意事项

### 图片要求
- **格式**：JPG、PNG
- **大小**：不超过2MB
- **尺寸**：建议不超过1024x1024像素
- **内容**：符合微信公众号规范

### 消息限制
- 微信临时素材有效期为3天
- 每次发送都会重新上传图片获取新的media_id
- 客服消息有发送频率限制

### 兼容性
- 支持现有的纯文本回复
- 旧数据自动兼容，无需手动迁移
- 新功能向下兼容

## 🛠️ 故障排除

### 图片无法上传
1. 检查图片格式和大小
2. 确认服务器有足够存储空间
3. 检查上传目录权限

### 图片消息发送失败
1. 检查微信公众号配置
2. 确认access_token有效
3. 查看错误日志：`runtime/wechat/当前年月/当前日期.log`

### 第二条消息未发送
1. 确认消息类型配置正确
2. 检查客服消息接口权限
3. 查看详细日志信息

## 📞 技术支持

如遇到问题，请提供：
1. 具体的错误现象
2. 相关的日志信息
3. 配置截图
4. 测试用的关键词和内容

---

**版本信息**：v2.0  
**更新日期**：2024-12-28  
**兼容性**：向下兼容所有旧版本数据
