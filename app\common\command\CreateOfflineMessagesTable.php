<?php

namespace app\common\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\facade\Db;
use think\facade\Log;

/**
 * 创建离线消息表
 * Class CreateOfflineMessagesTable
 * @package app\common\command
 */
class CreateOfflineMessagesTable extends Command
{
    protected function configure()
    {
        $this->setName('create_offline_messages_table')
            ->setDescription('创建离线消息表');
    }

    protected function execute(Input $input, Output $output)
    {
        $output->writeln('开始创建离线消息表...');
        
        try {
            // 检查表是否已存在
            $tableExists = Db::query("SHOW TABLES LIKE 'ls_offline_messages'");
            
            if (!empty($tableExists)) {
                $output->writeln('离线消息表已存在，无需创建');
                return;
            }
            
            // 创建表
            $sql = "CREATE TABLE `ls_offline_messages` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `to_id` int(11) NOT NULL COMMENT '接收者ID',
                `to_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '接收者类型',
                `msg` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '消息内容',
                `msg_type` int(11) NOT NULL COMMENT '消息类型',
                `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '离线消息表' ROW_FORMAT = DYNAMIC;";
            
            Db::execute($sql);
            
            $output->writeln('离线消息表创建成功');
        } catch (\Exception $e) {
            Log::error('创建离线消息表失败: ' . $e->getMessage());
            $output->writeln('创建离线消息表失败: ' . $e->getMessage());
        }
    }
}
