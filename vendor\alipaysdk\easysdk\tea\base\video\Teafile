{"scope": "alipay", "name": "easysdk-base-video", "version": "0.0.1", "main": "./main.tea", "java": {"package": "com.alipay.easysdk.base.video", "baseClient": "com.alipay.easysdk.kernel.BaseClient"}, "csharp": {"namespace": "Alipay.EasySDK.Base.Video", "baseClient": "Alipay.EasySDK.Kernel:BaseClient"}, "typescript": {"baseClient": "@alipay/easysdk-baseclient"}, "php": {"package": "Alipay.EasySDK.Base.Video"}, "go": {"namespace": "base/video"}, "libraries": {"EasySDKKernel": "alipay:easysdk-kernel:*"}}