{layout name="layout1" /}

<link href="__PUBLIC__/static/lib/layui/layeditor/layedit.css" rel="stylesheet"/>
<script src="__PUBLIC__/static/lib/layui/layeditor/index.js"></script>
<script src="__PUBLIC__/static/lib/layui/layeditor/ace/ace.js"></script>
<div class="wrapper">
    <div class="layui-card">
        <!-- 操作提示 -->
        <div class="layui-card-body">
            <div class="layui-collapse" style="border:1px dashed #c4c4c4">
                <div class="layui-colla-item">
                    <h2 class="layui-colla-title like-layui-colla-title">操作提示</h2>
                    <div class="layui-colla-content layui-show">
                        <p>*设置商家入驻协议，需同意协议才能提交入驻申请。</p>
                        <p>*您可以在下面的编辑框中,编辑更新入驻协议内容。</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主体区域 -->
        <div class="layui-card-body layui-form">
            <div class="layui-form-item">
                <textarea name="content" id="content">{$detail ?  $detail.content : ''}</textarea>
            </div>
            <div class="layui-form-item">
                <button class="layui-btn layui-btn-normal" lay-submit lay-filter="addSubmit">确定</button>
            </div>
        </div>

    </div>
</div>

<script>
    layui.config({
        base: "/static/lib/"
    }).extend({
        likeedit: "likeedit/likeedit"
    }).use(["form", "layEditor"], function(){
        var form = layui.form;
        var layEditor = layui.layEditor;
        layEditor.set({
            uploadImage: {
                url: '{:url("file/lists")}?type=10'
            },
        })
        var ieditor = layEditor.build('content')
        // form.verify({
        //     content: function(value) {
        //         return layEditor.sync(ieditor);
        //     }
        // });

        form.on("submit(addSubmit)", function(data){
            data.field['content'] = layEditor.getContent(ieditor);
            like.ajax({
                url: "{:url('shop.Treaty/index')}",
                data: data.field,
                type: "POST",
                success:function(res) {
                    if(res.code === 1) {
                        layui.layer.msg(res.msg);
                    }
                }
            });
        });

    })
</script>