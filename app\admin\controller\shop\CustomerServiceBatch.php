<?php
namespace app\admin\controller\shop;

use app\common\basics\AdminBase;
use app\admin\logic\shop\CustomerServiceBatchLogic;
use app\common\server\JsonServer;

/**
 * 客服批量操作控制器
 * Class CustomerServiceBatch
 * @package app\admin\controller\shop
 */
class CustomerServiceBatch extends AdminBase
{
    /**
     * 客服批量操作列表
     * @return string|\think\response\Json
     * @throws \Exception
     */
    public function index()
    {
        if ($this->request->isAjax()) {
            $get = $this->request->get();
            $result = CustomerServiceBatchLogic::getListsForTable($get);
            return JsonServer::success('获取成功', $result);
        }
        
        return view('', [
            'shop_list' => CustomerServiceBatchLogic::getShopOptions()
        ]);
    }

    /**
     * 批量分配客服
     * @return \think\response\Json
     * @throws \Exception
     */
    public function batchAssign()
    {
        if ($this->request->isAjax()) {
            $post = $this->request->post();

            // 数据验证
            if (empty($post['shop_ids']) || !is_array($post['shop_ids'])) {
                return JsonServer::error('请选择要分配的商家');
            }

            if (empty($post['kefu_ids']) || !is_array($post['kefu_ids'])) {
                return JsonServer::error('请选择要分配的客服');
            }

            $result = CustomerServiceBatchLogic::batchAssignKefu($post);
            if ($result) {
                return JsonServer::success('批量分配成功');
            } else {
                return JsonServer::error(CustomerServiceBatchLogic::getError() ?: '批量分配失败');
            }
        }

        return view('batch_assign', [
            'kefu_list' => CustomerServiceBatchLogic::getKefuOptions()
        ]);
    }

    /**
     * 批量移除客服
     * @return \think\response\Json
     * @throws \Exception
     */
    public function batchRemove()
    {
        if ($this->request->isAjax()) {
            $post = $this->request->post();
            
            // 数据验证
            if (empty($post['shop_ids']) || !is_array($post['shop_ids'])) {
                return JsonServer::error('请选择要操作的商家');
            }
            
            if (empty($post['kefu_ids']) || !is_array($post['kefu_ids'])) {
                return JsonServer::error('请选择要移除的客服');
            }
            
            $result = CustomerServiceBatchLogic::batchRemoveKefu($post);
            if ($result) {
                return JsonServer::success('批量移除成功');
            } else {
                return JsonServer::error(CustomerServiceBatchLogic::getError() ?: '批量移除失败');
            }
        }
    }

    /**
     * 获取商家客服列表
     * @return \think\response\Json
     */
    public function getShopKefu()
    {
        $shopId = $this->request->get('shop_id');
        
        if (empty($shopId)) {
            return JsonServer::error('商家ID不能为空');
        }
        
        $result = CustomerServiceBatchLogic::getShopKefuList($shopId);
        return JsonServer::success('获取成功', $result);
    }

    /**
     * 批量设置客服状态
     * @return \think\response\Json
     */
    public function batchSetStatus()
    {
        if ($this->request->isAjax()) {
            $post = $this->request->post();
            
            // 数据验证
            if (empty($post['kefu_ids']) || !is_array($post['kefu_ids'])) {
                return JsonServer::error('请选择要操作的客服');
            }
            
            if (!isset($post['status'])) {
                return JsonServer::error('请选择状态');
            }
            
            $result = CustomerServiceBatchLogic::batchSetKefuStatus($post);
            if ($result) {
                return JsonServer::success('批量设置成功');
            } else {
                return JsonServer::error(CustomerServiceBatchLogic::getError() ?: '批量设置失败');
            }
        }
    }

    /**
     * 导出客服数据
     * @return \think\response\Json
     */
    public function export()
    {
        $get = $this->request->get();
        $result = CustomerServiceBatchLogic::exportKefuData($get);
        
        if ($result) {
            return JsonServer::success('导出成功', $result);
        } else {
            return JsonServer::error(CustomerServiceBatchLogic::getError() ?: '导出失败');
        }
    }

    /**
     * 获取客服统计信息
     * @return \think\response\Json
     */
    public function getStats()
    {
        $result = CustomerServiceBatchLogic::getKefuStats();
        return JsonServer::success('获取成功', $result);
    }

    /**
     * 查看客服详情
     * @return string
     * @throws \Exception
     */
    public function view()
    {
        $id = $this->request->get('id');

        if (empty($id)) {
            throw new \Exception('参数错误');
        }

        $info = CustomerServiceBatchLogic::getKefuDetail($id);
        if (!$info) {
            throw new \Exception('客服不存在');
        }

        return view('', [
            'info' => $info
        ]);
    }

    /**
     * 编辑客服
     * @return string|\think\response\Json
     * @throws \Exception
     */
    public function edit()
    {
        if ($this->request->isAjax()) {
            $post = $this->request->post();

            // 数据验证
            if (empty($post['id'])) {
                return JsonServer::error('参数错误');
            }

            if (empty($post['nickname'])) {
                return JsonServer::error('客服昵称不能为空');
            }

            if (empty($post['account'])) {
                return JsonServer::error('客服账号不能为空');
            }

            $result = CustomerServiceBatchLogic::editKefu($post);
            if ($result) {
                return JsonServer::success('编辑成功');
            } else {
                return JsonServer::error(CustomerServiceBatchLogic::getError() ?: '编辑失败');
            }
        }

        $id = $this->request->get('id');

        if (empty($id)) {
            throw new \Exception('参数错误');
        }

        $info = CustomerServiceBatchLogic::getKefuDetail($id);
        if (!$info) {
            throw new \Exception('客服不存在');
        }

        return view('', [
            'info' => $info
        ]);
    }
}
