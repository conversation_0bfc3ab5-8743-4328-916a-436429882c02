<?php
namespace app\common\model;

use think\Model;

class Notification extends Model
{
    protected $name = 'notifications'; // Explicitly define table name to avoid issues with pluralization

    // Automatically convert target_user_ids to array when getting and to JSON when setting
    protected $json = ['target_user_ids'];
    protected $type = [
        'target_user_ids' => 'json',
    ];

    // Define enum values for target_type for potential validation or use in code
    const TARGET_TYPE_ADMIN = 'admin';
    const TARGET_TYPE_SHOP = 'shop';
    const TARGET_TYPE_USER = 'user'; // General user, if needed in the future

    // Define enum values for created_by_type
    const CREATED_BY_SYSTEM = 'system';
    const CREATED_BY_ADMIN = 'admin';
    const CREATED_BY_SHOP_API = 'shop_api';
    const CREATED_BY_API = 'api'; // General API

    /**
     * Define relationship with NotificationRead model
     * A notification can have many read records
     */
    public function reads()
    {
        return $this->hasMany(NotificationRead::class, 'notification_id', 'id');
    }

    /**
     * Scope to get unread notifications for a specific user.
     * This requires a more complex query joining with notification_reads.
     *
     * @param \think\db\Query $query
     * @param string $userType ('admin' or 'shop')
     * @param int $userId
     * @return \think\db\Query
     */
    public static function scopeUnreadForUser($query, $userType, $userId)
    {
        return $query->where('target_type', $userType) // Basic targeting
            ->where(function ($q) use ($userId) {
                $q->whereNull('target_user_ids') // For all users of this type
                  ->whereOrRaw('JSON_CONTAINS(target_user_ids, CAST(? AS JSON))', [$userId]); // Or specifically for this user
            })
            ->whereNotExists(function ($subQuery) use ($userType, $userId) {
                $prefix = config('database.connections.' . config('database.default') . '.prefix');
                $subQuery->table($prefix . 'notification_reads')
                    ->where('user_type', $userType)
                    ->where('user_id', $userId)
                    ->whereRaw('notification_id = ' . $prefix . 'notifications.id'); // Corrected to refer to outer table
            })
            ->order('created_at', 'desc');
    }
    
    /**
     * Scope to get notifications for a specific user (including read and unread).
     *
     * @param \think\db\Query $query
     * @param string $userType ('admin' or 'shop')
     * @param int $userId
     * @return \think\db\Query
     */
    public static function scopeForUser($query, $userType, $userId)
    {
        return $query->where('target_type', $userType)
            ->where(function ($q) use ($userId) {
                $q->whereNull('target_user_ids') // For all users of this type
                  ->whereOrRaw('JSON_CONTAINS(target_user_ids, CAST(? AS JSON))', [$userId]); // Or specifically for this user
            })
            ->order('created_at', 'desc');
    }
}
