<?php
namespace app\common\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\facade\Log;

/**
 * 更新WebSocket SSL证书
 * 
 * 此命令用于定期检查并更新WebSocket服务器的SSL证书
 * 可以通过cron定时执行，确保证书更新后WebSocket服务器也能使用最新的证书
 */
class UpdateWebSocketSSL extends Command
{
    protected function configure()
    {
        $this->setName('update_websocket_ssl')
            ->setDescription('更新WebSocket服务器的SSL证书');
    }

    protected function execute(Input $input, Output $output)
    {
        $output->writeln('开始更新WebSocket SSL证书...');
        
        // 定义目标路径
        $targetDir = root_path() . 'cert';
        $targetCertPath = $targetDir . '/ssl.pem';
        $targetKeyPath = $targetDir . '/ssl.key';
        
        // 定义源路径（系统证书路径）
        $sourceCertPaths = [
            '/etc/letsencrypt/live/www.huohanghang.cn/fullchain.pem',
            '/etc/letsencrypt/live/huohanghang.cn/fullchain.pem',
            '/etc/letsencrypt/live/kefu.huohanghang.cn/fullchain.pem',
        ];
        
        $sourceKeyPaths = [
            '/etc/letsencrypt/live/www.huohanghang.cn/privkey.pem',
            '/etc/letsencrypt/live/huohanghang.cn/privkey.pem',
            '/etc/letsencrypt/live/kefu.huohanghang.cn/privkey.pem',
        ];
        
        // 创建目标目录（如果不存在）
        if (!is_dir($targetDir)) {
            if (!mkdir($targetDir, 0755, true)) {
                $output->writeln('无法创建目录: ' . $targetDir);
                Log::error('无法创建目录: ' . $targetDir);
                return 1;
            }
            $output->writeln('已创建目录: ' . $targetDir);
        }
        
        // 检查现有证书的修改时间
        $existingCertTime = file_exists($targetCertPath) ? filemtime($targetCertPath) : 0;
        $existingKeyTime = file_exists($targetKeyPath) ? filemtime($targetKeyPath) : 0;
        
        // 复制证书
        $certCopied = false;
        foreach ($sourceCertPaths as $sourceCertPath) {
            if (file_exists($sourceCertPath)) {
                $sourceTime = filemtime($sourceCertPath);
                
                // 只有当源证书比目标证书新时才复制
                if ($sourceTime > $existingCertTime) {
                    if (copy($sourceCertPath, $targetCertPath)) {
                        $output->writeln('已复制证书: ' . $sourceCertPath . ' -> ' . $targetCertPath);
                        chmod($targetCertPath, 0644);
                        $certCopied = true;
                        break;
                    } else {
                        $output->writeln('复制证书失败: ' . $sourceCertPath . ' -> ' . $targetCertPath);
                        Log::error('复制证书失败: ' . $sourceCertPath . ' -> ' . $targetCertPath . ', 错误: ' . error_get_last()['message']);
                    }
                } else {
                    $output->writeln('现有证书已是最新，无需更新');
                    $certCopied = true; // 标记为已复制，因为现有证书已是最新
                    break;
                }
            }
        }
        
        if (!$certCopied) {
            $output->writeln('未找到可用的证书文件');
            Log::warning('未找到可用的证书文件');
        }
        
        // 复制密钥
        $keyCopied = false;
        foreach ($sourceKeyPaths as $sourceKeyPath) {
            if (file_exists($sourceKeyPath)) {
                $sourceTime = filemtime($sourceKeyPath);
                
                // 只有当源密钥比目标密钥新时才复制
                if ($sourceTime > $existingKeyTime) {
                    if (copy($sourceKeyPath, $targetKeyPath)) {
                        $output->writeln('已复制密钥: ' . $sourceKeyPath . ' -> ' . $targetKeyPath);
                        chmod($targetKeyPath, 0644);
                        $keyCopied = true;
                        break;
                    } else {
                        $output->writeln('复制密钥失败: ' . $sourceKeyPath . ' -> ' . $targetKeyPath);
                        Log::error('复制密钥失败: ' . $sourceKeyPath . ' -> ' . $targetKeyPath . ', 错误: ' . error_get_last()['message']);
                    }
                } else {
                    $output->writeln('现有密钥已是最新，无需更新');
                    $keyCopied = true; // 标记为已复制，因为现有密钥已是最新
                    break;
                }
            }
        }
        
        if (!$keyCopied) {
            $output->writeln('未找到可用的密钥文件');
            Log::warning('未找到可用的密钥文件');
        }
        
        // 检查结果
        if ($certCopied && $keyCopied) {
            $output->writeln('SSL证书更新成功，WebSocket服务器可以使用WSS协议');
            
            // 重启WebSocket服务器（如果需要）
            // 这里可以添加重启WebSocket服务器的命令
            // 例如：exec('php ' . root_path() . 'restart_gateway.php');
            
            return 0;
        } else {
            $output->writeln('SSL证书更新失败，WebSocket服务器将使用WS协议');
            return 1;
        }
    }
}
