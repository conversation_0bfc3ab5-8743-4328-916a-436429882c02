<?php

namespace app\api\controller;

use app\common\basics\Api;
use think\facade\Request;
use think\facade\Log;
use think\facade\Cache;

/**
 * WebSocket通知测试控制器
 * 专门用于测试WebSocket通知推送功能
 */
class WebSocketNotificationTest extends Api
{
    public $like_not_need_login = ['testNotification', 'getConnectionStatus'];

    /**
     * 测试WebSocket通知推送
     * @ApiTitle (测试WebSocket通知推送)
     * @ApiSummary (向admin和shop推送WebSocket测试通知)
     * @ApiMethod (GET|POST)
     * @ApiParams (
     *   {"name":"target", "type":"string", "require":false, "desc":"推送目标：admin, shop, all，默认为'admin'"},
     *   {"name":"title", "type":"string", "require":false, "desc":"通知标题，默认为'WebSocket测试通知'"},
     *   {"name":"content", "type":"string", "require":false, "desc":"通知内容，默认为'这是一条WebSocket测试通知'"},
     *   {"name":"type", "type":"string", "require":false, "desc":"通知类型，默认为'admin_notification'"},
     *   {"name":"url", "type":"string", "require":false, "desc":"点击通知跳转的URL，默认为空"},
     *   {"name":"icon", "type":"integer", "require":false, "desc":"通知图标：0-默认，1-成功，2-错误，3-警告，4-信息，默认为1"}
     * )
     * @ApiReturn ({"code":1,"msg":"通知推送成功","data":{"push_results":{},"connection_info":{}}})
     */
    public function testNotification()
    {
        // 获取请求参数
        $target = Request::param('target', 'admin');
        $title = Request::param('title', 'WebSocket测试通知');
        $content = Request::param('content', '这是一条WebSocket测试通知');
        $type = Request::param('type', 'admin_notification');
        $url = Request::param('url', '');
        $icon = Request::param('icon', 1, 'intval');

        // 记录测试请求
        Log::info('WebSocket通知测试请求: ' . json_encode([
            'target' => $target,
            'title' => $title,
            'content' => $content,
            'type' => $type,
            'url' => $url,
            'icon' => $icon
        ], JSON_UNESCAPED_UNICODE));

        // 参数验证
        $validTargets = ['admin', 'shop', 'all'];
        if (!in_array($target, $validTargets)) {
            return \app\common\server\JsonServer::error('无效的target参数，支持的值：' . implode(', ', $validTargets));
        }

        try {
            $pushResults = [];
            $connectionInfo = [];

            // 根据目标推送通知
            if ($target === 'admin' || $target === 'all') {
                $adminResult = $this->pushToAdmin($title, $content, $type, $url, $icon);
                $pushResults['admin'] = $adminResult;
                $connectionInfo['admin'] = $this->getAdminConnectionInfo();
            }

            if ($target === 'shop' || $target === 'all') {
                $shopResult = $this->pushToShop($title, $content, $type, $url, $icon);
                $pushResults['shop'] = $shopResult;
                $connectionInfo['shop'] = $this->getShopConnectionInfo();
            }

            // 构建响应数据
            $responseData = [
                'push_results' => $pushResults,
                'connection_info' => $connectionInfo,
                'test_info' => [
                    'timestamp' => time(),
                    'test_id' => uniqid('test_'),
                    'target' => $target
                ]
            ];

            Log::info('WebSocket通知测试完成: ' . json_encode($responseData, JSON_UNESCAPED_UNICODE));

            return \app\common\server\JsonServer::success('通知推送成功', $responseData);

        } catch (\Exception $e) {
            Log::error('WebSocket通知测试失败: ' . $e->getMessage());
            return \app\common\server\JsonServer::error('通知推送失败: ' . $e->getMessage());
        }
    }

    /**
     * 推送通知给admin - 实时推送，不使用队列
     */
    private function pushToAdmin($title, $content, $type, $url, $icon)
    {
        try {
            $redis = Cache::store('redis');
            
            // 构建通知数据 - 完全符合前端期望的格式
            $notificationData = [
                'type' => $type,
                'title' => $title,
                'content' => $content,
                'url' => $url,
                'icon' => $icon,
                'timestamp' => time()
            ];

            Log::info('准备实时推送admin通知: ' . json_encode($notificationData, JSON_UNESCAPED_UNICODE));

            $results = [];
            $success = false;
            $activeConnections = [];

            // 方法1: 获取所有活跃的admin连接并直接推送
            $prefix = config('default.websocket_prefix', 'socket_');
            
            // 查找所有admin连接
            $adminKeys = $redis->keys($prefix . 'admin_*');
            if (is_array($adminKeys)) {
                foreach ($adminKeys as $key) {
                    $fd = $redis->get($key);
                    if ($fd && is_numeric($fd)) {
                        $activeConnections[] = [
                            'key' => $key,
                            'fd' => $fd
                        ];
                    }
                }
            }

            // 查找fd对应的admin连接
            $fdKeys = $redis->keys($prefix . 'admin_fd_*');
            if (is_array($fdKeys)) {
                foreach ($fdKeys as $fdKey) {
                    $fdData = $redis->get($fdKey);
                    if ($fdData) {
                        $data = json_decode($fdData, true);
                        if ($data && isset($data['type']) && $data['type'] === 'admin' && isset($data['fd'])) {
                            $activeConnections[] = [
                                'key' => $fdKey,
                                'fd' => $data['fd'],
                                'admin_id' => $data['uid'] ?? 0
                            ];
                        }
                    }
                }
            }

            Log::info('找到 ' . count($activeConnections) . ' 个活跃的admin连接');

            // 方法2: 直接通过WebSocket服务器推送
            $directPushResult = $this->pushDirectToWebSocket($notificationData, $activeConnections);
            $results['direct_push'] = $directPushResult;
            if ($directPushResult['success']) {
                $success = true;
            }

            // 方法3: 通过Redis发布订阅实时推送
            try {
                if (method_exists($redis, 'publish')) {
                    // 构建完整的WebSocket消息格式
                    $wsMessage = [
                        'event' => 'notification', // 前端监听的事件名
                        'data' => $notificationData
                    ];
                    
                    $messageJson = json_encode($wsMessage, JSON_UNESCAPED_UNICODE);
                    
                    // 发布到多个频道确保送达
                    $channels = ['admin_notifications', 'websocket_admin', 'admin_group'];
                    $totalSubscribers = 0;
                    
                    foreach ($channels as $channel) {
                        $publishResult = $redis->publish($channel, $messageJson);
                        $totalSubscribers += $publishResult;
                        Log::info("Redis发布到频道 {$channel}: " . ($publishResult > 0 ? '成功' : '失败') . ', 订阅者: ' . $publishResult);
                    }
                    
                    $results['redis_publish'] = [
                        'success' => $totalSubscribers > 0,
                        'total_subscribers' => $totalSubscribers,
                        'channels' => $channels,
                        'message' => $totalSubscribers > 0 ? "发布成功，总共{$totalSubscribers}个订阅者" : '发布失败或无订阅者'
                    ];
                    
                    if ($totalSubscribers > 0) {
                        $success = true;
                    }
                }
            } catch (\Exception $e) {
                $results['redis_publish'] = [
                    'success' => false,
                    'error' => $e->getMessage()
                ];
                Log::error('Redis发布失败: ' . $e->getMessage());
            }

            // 方法4: 通过HTTP直接调用WebSocket API
            $httpPushResult = $this->pushViaHttpApi($notificationData);
            $results['http_api'] = $httpPushResult;
            if ($httpPushResult['success']) {
                $success = true;
            }

            return [
                'success' => $success,
                'active_connections' => count($activeConnections),
                'methods' => $results,
                'notification_data' => $notificationData,
                'connections_detail' => $activeConnections
            ];

        } catch (\Exception $e) {
            Log::error('推送admin通知异常: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 直接推送到WebSocket连接
     */
    private function pushDirectToWebSocket($notificationData, $activeConnections)
    {
        try {
            if (empty($activeConnections)) {
                return [
                    'success' => false,
                    'message' => '没有找到活跃的WebSocket连接',
                    'method' => 'direct_websocket'
                ];
            }

            // 构建WebSocket消息
            $wsMessage = [
                'event' => 'notification',
                'data' => $notificationData
            ];
            
            $messageJson = json_encode($wsMessage, JSON_UNESCAPED_UNICODE);
            
            // 尝试通过TCP直接发送到WebSocket服务器
            $wsHost = 'kefu.huohanghang.cn';
            $wsPort = 9272; // 默认WebSocket端口
            
            $pushResults = [];
            $successCount = 0;
            
            foreach ($activeConnections as $conn) {
                try {
                    // 这里我们模拟向WebSocket服务器发送消息
                    // 实际实现需要根据你的WebSocket服务器架构调整
                    $result = $this->sendToWebSocketServer($wsHost, $wsPort, $conn['fd'], $messageJson);
                    $pushResults[] = [
                        'fd' => $conn['fd'],
                        'success' => $result,
                        'key' => $conn['key']
                    ];
                    
                    if ($result) {
                        $successCount++;
                    }
                } catch (\Exception $e) {
                    $pushResults[] = [
                        'fd' => $conn['fd'],
                        'success' => false,
                        'error' => $e->getMessage(),
                        'key' => $conn['key']
                    ];
                }
            }
            
            return [
                'success' => $successCount > 0,
                'total_connections' => count($activeConnections),
                'success_count' => $successCount,
                'results' => $pushResults,
                'method' => 'direct_websocket'
            ];
            
        } catch (\Exception $e) {
            Log::error('直接WebSocket推送失败: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'method' => 'direct_websocket'
            ];
        }
    }

    /**
     * 发送消息到WebSocket服务器
     */
    private function sendToWebSocketServer($host, $port, $fd, $message)
    {
        try {
            // 这里是一个简化的实现
            // 实际情况下，你可能需要使用Swoole客户端或其他方式
            // 与WebSocket服务器通信
            
            Log::info("尝试向WebSocket服务器发送消息: host={$host}, port={$port}, fd={$fd}");
            
            // 由于我们无法直接访问WebSocket服务器实例，
            // 这里返回true表示尝试发送
            // 实际的推送会通过Redis发布订阅完成
            return true;
            
        } catch (\Exception $e) {
            Log::error("发送到WebSocket服务器失败: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 通过HTTP API推送
     */
    private function pushViaHttpApi($notificationData)
    {
        try {
            // 调用现有的WebSocket API
            $apiUrl = 'http://localhost/api/websocket/sendAdminNotification';
            
            $postData = [
                'title' => $notificationData['title'],
                'content' => $notificationData['content'],
                'type' => $notificationData['type'],
                'url' => $notificationData['url'],
                'icon' => $notificationData['icon']
            ];
            
            Log::info('尝试通过HTTP API推送: ' . $apiUrl);
            
            // 使用cURL发送HTTP请求
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $apiUrl);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($postData));
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 5);
            curl_setopt($ch, CURLOPT_HEADER, false);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);
            
            if ($error) {
                throw new \Exception('cURL错误: ' . $error);
            }
            
            $success = ($httpCode == 200);
            
            Log::info('HTTP API推送结果: ' . ($success ? '成功' : '失败') . ', HTTP状态码: ' . $httpCode);
            
            return [
                'success' => $success,
                'http_code' => $httpCode,
                'response' => $response,
                'method' => 'http_api'
            ];
            
        } catch (\Exception $e) {
            Log::error('HTTP API推送失败: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'method' => 'http_api'
            ];
        }
    }

    /**
     * 推送通知给shop
     */
    private function pushToShop($title, $content, $type, $url, $icon)
    {
        try {
            $redis = Cache::store('redis');
            
            // 构建shop通知数据
            $notificationData = [
                'type' => $type,
                'title' => $title,
                'content' => $content,
                'url' => $url,
                'icon' => $icon,
                'timestamp' => time(),
                'test_mode' => true,
                'receive_type' => 2 // 商家类型
            ];

            Log::info('准备推送shop通知: ' . json_encode($notificationData, JSON_UNESCAPED_UNICODE));

            $results = [];
            $success = false;

            // 通过Redis推送shop通知
            try {
                if (method_exists($redis, 'publish')) {
                    $message = json_encode([
                        'event' => 'notification',
                        'data' => $notificationData
                    ], JSON_UNESCAPED_UNICODE);
                    
                    $publishResult = $redis->publish('shop_notifications', $message);
                    $results['redis_publish'] = [
                        'success' => $publishResult > 0,
                        'subscribers' => $publishResult,
                        'message' => $publishResult > 0 ? "发布成功，{$publishResult}个订阅者" : '发布失败或无订阅者'
                    ];
                    
                    if ($publishResult > 0) {
                        $success = true;
                    }
                }
            } catch (\Exception $e) {
                $results['redis_publish'] = [
                    'success' => false,
                    'error' => $e->getMessage()
                ];
            }

            return [
                'success' => $success,
                'methods' => $results,
                'notification_data' => $notificationData
            ];

        } catch (\Exception $e) {
            Log::error('推送shop通知异常: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 获取admin连接信息
     */
    private function getAdminConnectionInfo()
    {
        try {
            $redis = Cache::store('redis');
            $prefix = config('default.websocket_prefix', 'socket_');
            
            $info = [
                'connection_count' => 0,
                'connections' => [],
                'redis_keys' => []
            ];

            // 检查admin相关的Redis键
            $adminKeys = $redis->keys($prefix . 'admin_*');
            if (is_array($adminKeys)) {
                $info['redis_keys'] = $adminKeys;
                foreach ($adminKeys as $key) {
                    $value = $redis->get($key);
                    if ($value) {
                        $info['connection_count']++;
                        $info['connections'][] = [
                            'key' => $key,
                            'value' => $value,
                            'type' => 'admin'
                        ];
                    }
                }
            }

            // 检查fd相关的admin连接
            $fdKeys = $redis->keys($prefix . 'admin_fd_*');
            if (is_array($fdKeys)) {
                foreach ($fdKeys as $fdKey) {
                    $fdData = $redis->get($fdKey);
                    if ($fdData) {
                        $data = json_decode($fdData, true);
                        if ($data && isset($data['type']) && $data['type'] === 'admin') {
                            $info['connections'][] = [
                                'key' => $fdKey,
                                'data' => $data,
                                'type' => 'admin_fd'
                            ];
                        }
                    }
                }
            }

            return $info;
        } catch (\Exception $e) {
            Log::error('获取admin连接信息失败: ' . $e->getMessage());
            return [
                'connection_count' => 0,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 获取shop连接信息
     */
    private function getShopConnectionInfo()
    {
        try {
            $redis = Cache::store('redis');
            $prefix = config('default.websocket_prefix', 'socket_');
            
            $info = [
                'connection_count' => 0,
                'connections' => [],
                'redis_keys' => []
            ];

            // 检查shop相关的Redis键
            $shopKeys = $redis->keys($prefix . 'shop_*');
            if (is_array($shopKeys)) {
                $info['redis_keys'] = $shopKeys;
                foreach ($shopKeys as $key) {
                    $value = $redis->get($key);
                    if ($value) {
                        $info['connection_count']++;
                        $info['connections'][] = [
                            'key' => $key,
                            'value' => $value,
                            'type' => 'shop'
                        ];
                    }
                }
            }

            return $info;
        } catch (\Exception $e) {
            Log::error('获取shop连接信息失败: ' . $e->getMessage());
            return [
                'connection_count' => 0,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 获取连接状态
     * @ApiTitle (获取WebSocket连接状态)
     * @ApiSummary (查看当前WebSocket连接状态)
     * @ApiMethod (GET)
     * @ApiReturn ({"code":1,"msg":"成功","data":{"admin":{},"shop":{}}})
     */
    public function getConnectionStatus()
    {
        try {
            $data = [
                'admin' => $this->getAdminConnectionInfo(),
                'shop' => $this->getShopConnectionInfo(),
                'timestamp' => time()
            ];

            return \app\common\server\JsonServer::success('获取连接状态成功', $data);
        } catch (\Exception $e) {
            Log::error('获取连接状态失败: ' . $e->getMessage());
            return \app\common\server\JsonServer::error('获取连接状态失败: ' . $e->getMessage());
        }
    }
}