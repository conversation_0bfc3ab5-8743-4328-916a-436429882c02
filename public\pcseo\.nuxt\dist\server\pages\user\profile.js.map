{"version": 3, "file": "pages/user/profile.js", "sources": ["webpack:///./components/count-down.vue?4f61", "webpack:///./utils/parseTime.js", "webpack:///./components/count-down.vue", "webpack:///./components/count-down.vue?a8c1", "webpack:///./components/count-down.vue?1b2a", "webpack:///./utils/type.js", "webpack:///./pages/user/profile.vue?559b", "webpack:///./pages/user/profile.vue?8fd4", "webpack:///./pages/user/profile.vue?5906", "webpack:///./pages/user/profile.vue?abee", "webpack:///./pages/user/profile.vue", "webpack:///./pages/user/profile.vue?d12f", "webpack:///./pages/user/profile.vue?1c79"], "sourcesContent": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return (_vm.time >= 0)?_c('div',[_c('client-only',[(_vm.isSlot)?_vm._t(\"default\"):_c('span',[_vm._v(_vm._s(_vm.formateTime))])],2)],1):_vm._e()}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "\n\nconst SECOND = 1000;\nconst MINUTE = 60 * SECOND;\nconst HOUR = 60 * MINUTE;\nconst DAY = 24 * HOUR;\nexport function parseTimeData(time) {\n    const days = Math.floor(time / DAY);\n    const hours = sliceTwo(Math.floor((time % DAY) / HOUR));\n    const minutes = sliceTwo(Math.floor((time % HOUR) / MINUTE));\n    const seconds = sliceTwo(Math.floor((time % MINUTE) / SECOND));\n    return {\n        days: days,\n        hours: hours,\n        minutes: minutes,\n        seconds: seconds,\n    };\n}\n\nfunction sliceTwo(str) {\n    return (0 + str.toString()).slice(-2)\n}\n\nexport  function parseFormat(format, timeData) {\n    let days = timeData.days;\n    let hours = timeData.hours, minutes = timeData.minutes, seconds = timeData.seconds\n    if (format.indexOf('dd') !== -1) {\n        format = format.replace('dd', days);\n    }\n    if (format.indexOf('hh') !== -1) {\n        format = format.replace('hh', sliceTwo(hours) );\n    }\n    if (format.indexOf('mm') !== -1) {\n        format = format.replace('mm', sliceTwo(minutes));\n    }\n    if (format.indexOf('ss') !== -1) {\n        format = format.replace('ss', sliceTwo(seconds));\n    }\n    return format\n}", "//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { parseTimeData, parseFormat } from '~/utils/parseTime'\nexport default {\n    components: {},\n    props: {\n        isSlot: {\n            type: Boolean,\n            default: false,\n        },\n        time: {\n            type: Number,\n            default: 0,\n        },\n        format: {\n            type: String,\n            default: 'hh:mm:ss',\n        },\n        autoStart: {\n            type: Boolean,\n            default: true,\n        },\n    },\n    watch: {\n        time: {\n            immediate: true,\n            handler(value) {\n                if (value) {\n                    this.reset()\n                }\n            },\n        },\n    },\n    data() {\n        return {\n            timeObj: {},\n            formateTime: 0,\n        }\n    },\n    created() {},\n    computed: {},\n    methods: {\n        createTimer(fn) {\n            return setTimeout(fn, 100)\n        },\n        isSameSecond(time1, time2) {\n            return Math.floor(time1) === Math.floor(time2)\n        },\n        start() {\n            if (this.counting) {\n                return\n            }\n            this.counting = true\n            this.endTime = Date.now() + this.remain * 1000\n            this.setTimer()\n        },\n        setTimer() {\n            this.tid = this.createTimer(() => {\n                let remain = this.getRemain()\n                if (!this.isSameSecond(remain, this.remain) || remain === 0) {\n                    this.setRemain(remain)\n                }\n                if (this.remain !== 0) {\n                    this.setTimer()\n                }\n            })\n        },\n        getRemain() {\n            return Math.max(this.endTime - Date.now(), 0)\n        },\n        pause() {\n            this.counting = false\n            clearTimeout(this.tid)\n        },\n        reset() {\n            this.pause()\n            this.remain = this.time\n            this.setRemain(this.remain)\n            if (this.autoStart) {\n                this.start()\n            }\n        },\n        setRemain(remain) {\n            const { format } = this\n            this.remain = remain\n            const timeData = parseTimeData(remain)\n            this.formateTime = parseFormat(format, timeData)\n            this.$emit('change', timeData)\n            if (remain === 0) {\n                this.pause()\n                this.$emit('finish')\n            }\n        },\n    },\n}\n", "import mod from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./count-down.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./count-down.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./count-down.vue?vue&type=template&id=2fbaab86&\"\nimport script from \"./count-down.vue?vue&type=script&lang=js&\"\nexport * from \"./count-down.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  \n}\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  null,\n  \"4090b4e2\"\n  \n)\n\nexport default component.exports", "export const client = 5\n\nexport const loginType = {\n    SMS: 0,\n    ACCOUNT: 1\n}\n\n\n// 短信发送\nexport const SMSType = {\n    // 注册\n    REGISTER: 'ZCYZ',\n    // 找回密码\n    FINDPWD: 'ZHMM',\n    // 登陆\n    LOGIN: 'YZMDL',\n    // 商家申请入驻\n    SJSQYZ: 'SJSQYZ',\n    // 更换手机号\n    CHANGE_MOBILE: 'BGSJHM',\n    // 绑定手机号\n    BIND: 'BDSJHM'\n}\n\nexport const FieldType = {\n    NONE: '',\n    SEX: 'sex',\n    NICKNAME: 'nickname',\n    AVATAR: 'avatar',\n    MOBILE: 'mobile'\n}\n\n\n// 售后状态\nexport const AfterSaleType = {\n    // 售后申请 \n    NORMAL: 'normal',\n    // 处理中\n    HANDLING: 'apply',\n    // 已处理\n    FINISH: 'finish'\n}\n", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../../node_modules/sass-resources-loader/lib/loader.js??ref--7-oneOf-1-4!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./profile.vue?vue&type=style&index=0&lang=scss&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"98ca9702\", content, true, context)\n};", "export * from \"-!../../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../../node_modules/sass-resources-loader/lib/loader.js??ref--7-oneOf-1-4!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./profile.vue?vue&type=style&index=0&lang=scss&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".user-profile{width:980px;padding:10px}.user-profile .user-header{padding:10px 5px;border-bottom:1px solid #e5e5e5}.user-profile .user-container{margin-top:35px}.user-profile .user-container .user-form-item{padding-left:13px;margin-top:24px}.user-profile .user-container .user-form-item .user-form-label{width:60px;text-align:left;margin-right:24px}.user-profile .user-container .user-form-item .user-avatar-upload .avatar-uploader:hover .avatar .mask{display:flex}.user-profile .user-container .user-form-item .user-avatar-upload .avatar-uploader:hover .avatar:after{opacity:1}.user-profile .user-container .user-form-item .user-avatar-upload .avatar-uploader .avatar{position:relative}.user-profile .user-container .user-form-item .user-avatar-upload .avatar-uploader .avatar .mask{display:none;position:absolute}.user-profile .user-container .user-form-item .user-avatar-upload .avatar-uploader .avatar:after{content:\\\"更换头像\\\";position:absolute;transition:opacity .3s ease;opacity:0;width:100%;height:64px;left:0;top:0;border-radius:60px;background-color:rgba(0,0,0,.3);color:#fff;display:flex;flex-direction:row;justify-content:center;align-items:center;font-size:12px}.user-profile .user-container .user-form-item .user-input{width:240px}.user-profile .user-container .user-form-item .el-radio__input.is-checked+.el-radio__label{color:#007aff}.user-profile .user-container .user-form-item .el-input__inner:focus{border-color:#007aff}.user-profile .user-container .user-form-item .el-radio__input.is-checked .el-radio__inner{border-color:#007aff;background:#007aff}.user-profile .user-container .user-form-item .el-radio__inner:hover{border-color:#007aff}.user-profile .user-container .primary-btn{height:32px;width:100px;margin-top:32px;border:none;border-radius:4px;cursor:pointer}.user-profile .user-container .primary-btn:focus{border:none;outline:none}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"user-profile\"},[_vm._ssrNode(\"<div class=\\\"user-header lg\\\">\\n        个人资料\\n    </div> \"),_vm._ssrNode(\"<div class=\\\"user-container\\\">\",\"</div>\",[_vm._ssrNode(\"<div class=\\\"user-form-item flex\\\">\",\"</div>\",[_vm._ssrNode(\"<label class=\\\"user-form-label nr\\\">头像</label> \"),_vm._ssrNode(\"<div class=\\\"user-avatar-upload\\\">\",\"</div>\",[_c('el-upload',{staticClass:\"avatar-uploader\",attrs:{\"action\":_vm.action,\"show-file-list\":false,\"file-list\":_vm.fileList,\"on-success\":_vm.uploadFileSuccess,\"headers\":{token: _vm.$store.state.token}}},[_c('div',{staticClass:\"avatar\"},[_c('el-image',{staticStyle:{\"width\":\"64px\",\"height\":\"64px\",\"border-radius\":\"60px\"},attrs:{\"src\":_vm.avatar}}),_vm._v(\" \"),_c('div',{staticClass:\"mask white\"})],1)])],1)],2),_vm._ssrNode(\" <div class=\\\"user-form-item flex\\\"><label class=\\\"user-form-label nr\\\">用户ID</label> <div class=\\\"normal nr\\\">\"+_vm._ssrEscape(_vm._s(_vm.sn))+\"</div></div> \"),_vm._ssrNode(\"<div class=\\\"user-form-item flex\\\">\",\"</div>\",[_vm._ssrNode(\"<label class=\\\"user-form-label nr\\\">昵称</label> \"),_c('el-input',{staticClass:\"user-input\",attrs:{\"suffix-icon\":\"el-icon-edit\"},model:{value:(_vm.nickName),callback:function ($$v) {_vm.nickName=$$v},expression:\"nickName\"}})],2),_vm._ssrNode(\" \"),_vm._ssrNode(\"<div class=\\\"user-form-item flex\\\">\",\"</div>\",[_vm._ssrNode(\"<label class=\\\"user-form-label nr\\\">性别</label> \"),_c('el-radio-group',{model:{value:(_vm.radio),callback:function ($$v) {_vm.radio=$$v},expression:\"radio\"}},[_c('el-radio',{attrs:{\"label\":'男'}},[_vm._v(\"男\")]),_vm._v(\" \"),_c('el-radio',{attrs:{\"label\":'女'}},[_vm._v(\"女\")])],1)],2),_vm._ssrNode(\" <div class=\\\"user-form-item flex\\\"><label class=\\\"user-form-label nr\\\">手机号</label> <div class=\\\"normal nr\\\">\"+_vm._ssrEscape(_vm._s(_vm.mobile))+\"</div> <div style=\\\"color: #6699CC;margin-left: 13px;cursor: pointer;\\\">\"+_vm._ssrEscape(\"\\n                \"+_vm._s(_vm.mobile?'修改号码':'绑定手机号'))+\"</div></div> <div class=\\\"user-form-item flex\\\"><label class=\\\"user-form-label nr\\\">注册时间</label> <div class=\\\"normal nr\\\">\"+_vm._ssrEscape(_vm._s(_vm.createTime))+\"</div></div> <div class=\\\"user-form-item flex\\\"><label class=\\\"user-form-label nr\\\">登录密码</label> <div class=\\\"nr\\\" style=\\\"color: #6699CC;cursor: pointer;\\\">修改密码</div></div> <button class=\\\"primary-btn bg-primary flex-center white\\\">\\n            保存\\n        </button>\")],2),_vm._ssrNode(\" \"),_c('el-dialog',{attrs:{\"center\":true,\"title\":_vm.mobile ? '更换手机号': '绑定手机',\"visible\":_vm.showChangeNumber,\"width\":\"40%\"},on:{\"update:visible\":function($event){_vm.showChangeNumber=$event}}},[_c('div',[_c('el-form',{staticStyle:{\"width\":\"50%\",\"margin\":\"0 auto\"}},[_c('el-form-item',[_c('el-input',{attrs:{\"placeholder\":\"请输入新的手机号码\"},model:{value:(_vm.telephone),callback:function ($$v) {_vm.telephone=$$v},expression:\"telephone\"}})],1),_vm._v(\" \"),_c('el-form-item',[_c('div',{staticClass:\"flex\"},[_c('el-input',{attrs:{\"placeholder\":\"短信验证码\"},model:{value:(_vm.verifyCode),callback:function ($$v) {_vm.verifyCode=$$v},expression:\"verifyCode\"}}),_vm._v(\" \"),_c('el-button',{staticStyle:{\"margin-left\":\"14px\"},on:{\"click\":_vm.sndSmsToPhone}},[(_vm.canSendNumber)?_c('div',[_vm._v(\"获取验证码\")]):_c('count-down',{attrs:{\"time\":60,\"format\":\"ss秒\",\"autoStart\":\"\"},on:{\"finish\":function($event){_vm.canSendNumber = true}}})],1)],1)])],1)],1),_vm._v(\" \"),_c('div',{attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{staticStyle:{\"width\":\"134px\"},attrs:{\"type\":\"primary\"},on:{\"click\":_vm.changeUserMobile}},[_vm._v(\"确认\")]),_vm._v(\" \"),_c('el-button',{staticStyle:{\"width\":\"134px\"},on:{\"click\":_vm.closeChangeNumber}},[_vm._v(\"取消\")])],1)]),_vm._ssrNode(\" \"),_c('el-dialog',{attrs:{\"title\":\"设置登录密码\",\"center\":true,\"visible\":_vm.showPwdPop,\"width\":\"40%\"},on:{\"update:visible\":function($event){_vm.showPwdPop=$event}}},[_c('div',[_c('el-form',{staticStyle:{\"width\":\"50%\",\"margin\":\"0 auto\"}},[_c('el-form-item',[_c('el-input',{attrs:{\"placeholder\":\"请输入手机号码\"},model:{value:(_vm.mobile),callback:function ($$v) {_vm.mobile=$$v},expression:\"mobile\"}})],1),_vm._v(\" \"),_c('el-form-item',[_c('div',{staticClass:\"flex\"},[_c('el-input',{attrs:{\"placeholder\":\"短信验证码\"},model:{value:(_vm.verifyCode),callback:function ($$v) {_vm.verifyCode=$$v},expression:\"verifyCode\"}}),_vm._v(\" \"),_c('el-button',{staticStyle:{\"margin-left\":\"14px\"},on:{\"click\":_vm.sndSmsToPhone}},[(_vm.canSendPwd)?_c('div',[_vm._v(\"获取验证码\")]):_c('count-down',{attrs:{\"time\":60,\"format\":\"ss秒\",\"autoStart\":\"\"},on:{\"finish\":function($event){_vm.canSendPwd = true}}})],1)],1)]),_vm._v(\" \"),_c('el-form-item',[_c('el-input',{attrs:{\"type\":\"password\",\"placeholder\":\"请输入密码 (数字与字母自由组合)\"},model:{value:(_vm.pwd),callback:function ($$v) {_vm.pwd=$$v},expression:\"pwd\"}})],1),_vm._v(\" \"),_c('el-form-item',[_c('el-input',{attrs:{\"type\":\"password\",\"placeholder\":\"再次输入密码\"},model:{value:(_vm.againPwd),callback:function ($$v) {_vm.againPwd=$$v},expression:\"againPwd\"}})],1)],1)],1),_vm._v(\" \"),_c('div',{attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{staticStyle:{\"width\":\"134px\"},attrs:{\"type\":\"primary\"},on:{\"click\":_vm.setPassWord}},[_vm._v(\"确认\")]),_vm._v(\" \"),_c('el-button',{staticStyle:{\"width\":\"134px\"},on:{\"click\":_vm.closePwdPop}},[_vm._v(\"取消\")])],1)])],2)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { SMSType, client, FieldType } from \"~/utils/type\";\nimport Cookies from \"js-cookie\";\nimport { mapActions } from \"vuex\";\nimport config from \"~/config/app\";\nexport default {\n    head() {\n        return {\n            title: this.$store.getters.headTitle,\n            link: [\n                {\n                    rel: \"icon\",\n                    type: \"image/x-icon\",\n                    href: this.$store.getters.favicon,\n                },\n            ],\n        };\n    },\n    layout: \"user\",\n    mounted() {\n        this.getUserInfoFun();\n    },\n    data() {\n        return {\n            avatar: \"\",\n            mobile: \"\",\n            sex: 0,\n            createTime: \"\",\n            sn: \"\",\n            action: config.baseUrl + \"/api/file/formimage\",\n            nickName: \"\",\n            radio: 1,\n            showChangeNumber: false,\n            showPwdPop: false,\n            telephone: \"\",\n            verifyCode: \"\",\n            pwd: \"\",\n            againPwd: \"\",\n            smsType: SMSType.CHANGE_MOBILE,\n            canSendNumber: true,\n            canSendPwd: true,\n            fileList: [],\n        };\n    },\n    methods: {\n        ...mapActions([\"getPublicData\"]),\n\n        async getUserInfoFun() {\n            let res = await this.$get(\"user/info\");\n            if (res.code == 1) {\n                this.avatar = res.data.avatar;\n                this.nickName = res.data.nickname;\n                this.mobile = res.data.mobile;\n                this.sex = res.data.sex;\n                this.radio = this.sex;\n                this.createTime = res.data.create_time;\n                this.sn = res.data.sn;\n            }\n        },\n\n        async saveUserInfo() {\n            let res = await this.$post(\"pc/changeUserInfo\", {\n                sex: this.radio == \"男\" ? 1 : 2,\n                nickname: this.nickName,\n            });\n            if (res.code == 1) {\n                this.$message({\n                    message: res.msg,\n                    type: \"success\",\n                });\n                this.getPublicData();\n            }\n        },\n        closeChangeNumber() {\n            this.telephone = \"\";\n            this.verifyCode = \"\";\n            this.showChangeNumber = false;\n        },\n        closePwdPop() {\n            this.telephone = \"\";\n            this.verifyCode = \"\";\n            this.showPwdPop = false;\n        },\n\n        // 打开修改手机号的弹窗\n        openChangeNumber() {\n            this.showChangeNumber = true;\n            this.smsType = this.mobile ? SMSType.CHANGE_MOBILE : SMSType.BIND;\n        },\n\n        // 打开修改登录密码的弹窗\n        openChangePwdPop() {\n            if (this.mobile == \"\") return this.$message.error(\"请先绑定手机号\");\n            this.showPwdPop = true;\n            this.smsType = SMSType.FINDPWD;\n        },\n\n        // 发送验证码\n        async sndSmsToPhone() {\n            if (\n                (this.smsType == SMSType.CHANGE_MOBILE ||\n                    this.smsType == SMSType.BIND) &&\n                !this.canSendNumber\n            )\n                return;\n            else if (this.smsType == SMSType.FINDPWD && !this.canSendPwd)\n                return;\n            if (this.smsType == SMSType.CHANGE_MOBILE && !this.telephone)\n                return this.$message.error(\"请输入手机号\");\n\n            let res = await this.$post(\"sms/send\", {\n                mobile:\n                    this.smsType == SMSType.FINDPWD\n                        ? this.mobile\n                        : this.telephone,\n                key: this.smsType,\n            });\n            if (res.code == 1) {\n                this.smsType == SMSType.FINDPWD\n                    ? (this.canSendPwd = false)\n                    : (this.canSendNumber = false);\n                this.$message.success(\"发送成功\");\n            }\n        },\n\n        // 修改手机号码\n        async changeUserMobile() {\n            if (!this.telephone)\n                return this.$message.error(\"请输入新的手机号码\");\n            if (!this.verifyCode) return this.$message.error(\"请输入验证码\");\n            let res = await this.$post(\"user/changeMobile\", {\n                mobile: this.mobile,\n                new_mobile: this.telephone,\n                code: this.verifyCode,\n                action: this.mobile ? \"change\" : \"\",\n                client: client,\n            });\n            if (res.code == 1) {\n                this.showChangeNumber = false;\n                this.$message.success(res.msg);\n                this.getPublicData();\n                this.getUserInfoFun();\n            }\n        },\n\n        // 设置登录密码\n        async setPassWord() {\n            if (!this.verifyCode) return this.$message.error(\"请输入验证码\");\n            if (!this.pwd) return this.$message.error(\"请输入密码\");\n            if (!this.againPwd) return this.$message.error(\"请输入确认密码\");\n            if (this.pwd != this.againPwd)\n                return this.$message.error(\"两次密码输入不一致\");\n            let res = await this.$post(\"login_password/forget\", {\n                mobile: this.mobile,\n                code: this.verifyCode,\n                password: this.pwd,\n                repassword: this.againPwd,\n                client: client,\n            });\n            if (res.code == 1) {\n                this.$message({\n                    message: res.msg,\n                    type: \"success\",\n                });\n                this.showPwdPop = false;\n                const token = res.data.token;\n                Cookies.set(\"token\", token, { expires: 60 });\n            }\n        },\n        async uploadFileSuccess(res, fileList) {\n            let respond = await this.$post(\"user/setInfo\", {\n                field: FieldType.AVATAR,\n                value: res.data.uri,\n            });\n            if (respond.code == 1) {\n                this.$message({\n                    message: respond.msg,\n                    type: \"success\",\n                });\n                let userRes = await this.$get(\"user/info\");\n                if (userRes.code == 1) {\n                    this.avatar = userRes.data.avatar;\n                    this.nickName = userRes.data.nickname;\n                    this.mobile = userRes.data.mobile;\n                    this.sex = userRes.data.sex;\n                    this.radio = this.sex;\n                    this.createTime = userRes.data.create_time;\n                }\n            }\n        },\n    },\n};\n", "import mod from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./profile.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./profile.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./profile.vue?vue&type=template&id=5917c3b2&\"\nimport script from \"./profile.vue?vue&type=script&lang=js&\"\nexport * from \"./profile.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./profile.vue?vue&type=style&index=0&lang=scss&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  null,\n  \"6ecdb877\"\n  \n)\n\nexport default component.exports\n\n/* nuxt-component-imports */\ninstallComponents(component, {CountDown: require('/Users/<USER>/Desktop/vue/pc/components/count-down.vue').default})\n"], "mappings": ";;;;;;;;;;;AAAA;AACA;AACA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAJA;AAMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;;ACvCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAbA;AAkBA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAPA;AADA;AACA;AASA;AACA;AACA;AACA;AAFA;AAIA;AACA;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AApDA;AAtCA;;ACXA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACrBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;AACA;AAFA;AACA;AAMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAZA;AAeA;AACA;AACA;AACA;AACA;AACA;AALA;AACA;AASA;AACA;AACA;AACA;AACA;AACA;AACA;AANA;;;;;;;AClCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACXA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACNA;AACA;AACA;;;;;;;;;;;;;;;;;;ACFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAHA;AAHA;AAUA;AACA;AAAA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAlBA;AAoBA;AACA;AAAA;AACA;AAEA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAFA;AACA;AAGA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAQA;AAGA;AACA;AAIA;AALA;AACA;AAMA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AALA;AACA;AAMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AALA;AACA;AAMA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAFA;AACA;AAGA;AACA;AACA;AACA;AAFA;AAIA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAlJA;AAvCA;;AClHA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;A", "sourceRoot": ""}