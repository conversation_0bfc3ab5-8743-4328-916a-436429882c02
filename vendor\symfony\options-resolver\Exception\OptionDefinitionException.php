<?php

/*
 * This file is part of the Symfony package.
 *
 * (c) <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Symfony\Component\OptionsResolver\Exception;

/**
 * Thrown when two lazy options have a cyclic dependency.
 *
 * <AUTHOR> <bsch<PERSON><PERSON>@gmail.com>
 */
class OptionDefinitionException extends \LogicException implements ExceptionInterface
{
}
