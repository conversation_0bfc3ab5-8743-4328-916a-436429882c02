<?php
namespace app\api\logic;

use app\common\basics\Logic;
use Exception;
use setasign\Fpdi\Fpdi;

/**
 * PDF签名处理器
 * 用于在PDF文件中添加签名和日期
 * Class PdfSignatureProcessor
 * @package app\api\logic
 */
class PdfSignatureProcessor extends Logic
{
    /**
     * @var string PDF文件路径
     */
    protected $pdfPath;

    /**
     * @var string 签名图片路径
     */
    protected $signaturePath;

    /**
     * @var string 输出PDF路径
     */
    protected $outputPath;

    /**
     * @var array 签名位置坐标
     */
    protected $signaturePosition = [
        'x' => 120,  // 默认X坐标
        'y' => 240,  // 默认Y坐标
        'width' => 50, // 默认宽度
        'height' => 30 // 默认高度
    ];

    /**
     * @var array 日期位置坐标
     */
    protected $datePosition = [
        'x' => 120,  // 默认X坐标
        'y' => 260,  // 默认Y坐标
    ];

    /**
     * @var array 第二个日期位置坐标
     */
    protected $secondDatePosition = [
        'x' => 120,  // 默认X坐标
        'y' => 280,  // 默认Y坐标
    ];

    /**
     * 构造函数
     * @param string $pdfPath PDF文件路径
     */
    public function __construct($pdfPath = '')
    {
        if (!empty($pdfPath)) {
            $this->setPdfPath($pdfPath);
        }
    }

    /**
     * 设置PDF文件路径
     * @param string $pdfPath
     * @return $this
     * @throws Exception
     */
    public function setPdfPath($pdfPath)
    {
        if (!file_exists($pdfPath)) {
            throw new Exception('PDF文件不存在: ' . $pdfPath);
        }
        $this->pdfPath = $pdfPath;
        return $this;
    }

    /**
     * 设置签名图片路径
     * @param string $signaturePath
     * @return $this
     * @throws Exception
     */
    public function setSignaturePath($signaturePath)
    {
        if (!file_exists($signaturePath)) {
            throw new Exception('签名图片不存在: ' . $signaturePath);
        }
        $this->signaturePath = $signaturePath;
        return $this;
    }

    /**
     * 设置输出PDF路径
     * @param string $outputPath
     * @return $this
     */
    public function setOutputPath($outputPath)
    {
        $this->outputPath = $outputPath;
        return $this;
    }

    /**
     * 设置签名位置
     * @param float $x X坐标
     * @param float $y Y坐标
     * @param float|null $width 宽度
     * @param float|null $height 高度
     * @return $this
     */
    public function setSignaturePosition($x, $y, $width = null, $height = null)
    {
        $this->signaturePosition['x'] = $x;
        $this->signaturePosition['y'] = $y;

        if ($width !== null) {
            $this->signaturePosition['width'] = $width;
        }

        if ($height !== null) {
            $this->signaturePosition['height'] = $height;
        }

        return $this;
    }

    /**
     * 设置日期位置
     * @param float $x X坐标
     * @param float $y Y坐标
     * @return $this
     */
    public function setDatePosition($x, $y)
    {
        $this->datePosition['x'] = $x;
        $this->datePosition['y'] = $y;
        return $this;
    }

    /**
     * 设置第二个日期位置
     * @param float $x X坐标
     * @param float $y Y坐标
     * @return $this
     */
    public function setSecondDatePosition($x, $y)
    {
        $this->secondDatePosition['x'] = $x;
        $this->secondDatePosition['y'] = $y;
        return $this;
    }

    /**
     * 处理PDF文件，添加签名和日期
     * @param int $pageNumber 要处理的页码，默认为1
     * @param string $dateFormat 日期格式，默认为Y年m月d日
     * @return string 处理后的PDF文件路径
     * @throws Exception
     */
    public function process($pageNumber = 1, $dateFormat = 'Y年m月d日')
    {
        // 检查必要参数
        if (empty($this->pdfPath)) {
            throw new Exception('未设置PDF文件路径');
        }

        if (empty($this->signaturePath)) {
            throw new Exception('未设置签名图片路径');
        }

        if (empty($this->outputPath)) {
            // 如果未设置输出路径，则生成一个临时路径
            $pathInfo = pathinfo($this->pdfPath);
            $this->outputPath = $pathInfo['dirname'] . '/' . $pathInfo['filename'] . '_signed.' . $pathInfo['extension'];
        }

        try {
            // 创建FPDI实例
            $pdf = new Fpdi();

            // 设置一些基本属性
            $pdf->SetAutoPageBreak(false);
            $pdf->SetCompression(true);

            // 获取页数
            $pageCount = $pdf->setSourceFile($this->pdfPath);

            // 检查页码是否有效
            if ($pageNumber < 1 || $pageNumber > $pageCount) {
                throw new Exception('无效的页码: ' . $pageNumber);
            }

            // 导入页面
            for ($i = 1; $i <= $pageCount; $i++) {
                $templateId = $pdf->importPage($i);
                $size = $pdf->getTemplateSize($templateId);
                $orientation = ($size['width'] > $size['height']) ? 'L' : 'P';

                // 记录页面尺寸信息
                \think\facade\Log::info('PDF页面 #' . $i . ' 尺寸 - 宽度: ' . $size['width'] . ', 高度: ' . $size['height']);

                // 添加页面
                $pdf->AddPage($orientation, [$size['width'], $size['height']]);
                $pdf->useTemplate($templateId);

                // 获取当前页面高度
                $currentPageHeight = $pdf->getPageHeight();
                $currentPageWidth = $pdf->getPageWidth();
                \think\facade\Log::info('当前页面 #' . $i . ' 尺寸 - 宽度: ' . $currentPageWidth . ', 高度: ' . $currentPageHeight);

                // 只在指定页面添加签名和日期
                if ($i == $pageNumber) {
                    // 添加签名图片
                    // 检查图片文件是否存在
                    if (!file_exists($this->signaturePath)) {
                        throw new Exception('签名图片文件不存在: ' . $this->signaturePath);
                    }

                    // 检查图片文件大小
                    $fileSize = filesize($this->signaturePath);
                    if ($fileSize <= 0) {
                        throw new Exception('签名图片文件为空: ' . $this->signaturePath . ', 大小: ' . $fileSize);
                    }

                    // 使用Image方法添加图片
                    try {
                        // 获取图片信息
                        $imageInfo = getimagesize($this->signaturePath);
                        if (!$imageInfo) {
                            throw new Exception('无法获取图片信息');
                        }

                        // 确定图片类型
                        $imageType = '';
                        switch ($imageInfo['mime']) {
                            case 'image/jpeg':
                            case 'image/jpg':
                                $imageType = 'JPG';
                                break;
                            case 'image/png':
                                $imageType = 'PNG';
                                break;
                            default:
                                throw new Exception('不支持的图片格式: ' . $imageInfo['mime']);
                        }

                        // 添加图片到PDF
                        // 记录添加图片前的信息
                        \think\facade\Log::info('添加签名图片 - 路径: ' . $this->signaturePath);
                        \think\facade\Log::info('签名位置 - X: ' . $this->signaturePosition['x'] . ', Y: ' . $this->signaturePosition['y'] .
                                              ', 宽度: ' . $this->signaturePosition['width'] . ', 高度: ' . $this->signaturePosition['height']);

                        // 直接使用原始坐标，不进行转换
                        // FPDI中的坐标系可能与我们预期的不同，但我们先尝试直接使用原始坐标

                        // 添加图片到PDF - 尝试不同的方法
                        // 1. 检查图片是否存在且可读
                        if (!is_readable($this->signaturePath)) {
                            \think\facade\Log::error('签名图片不可读: ' . $this->signaturePath);
                            throw new Exception('签名图片不可读: ' . $this->signaturePath);
                        }

                        // 2. 记录图片信息
                        $imgInfo = getimagesize($this->signaturePath);
                        \think\facade\Log::info('图片原始尺寸 - 宽度: ' . $imgInfo[0] . ', 高度: ' . $imgInfo[1]);

                        // 3. 使用绝对路径
                        $absolutePath = realpath($this->signaturePath);
                        \think\facade\Log::info('图片绝对路径: ' . $absolutePath);

                        // 添加图片到PDF
                        $pdf->Image(
                            $this->signaturePath,
                            $this->signaturePosition['x'],
                            $this->signaturePosition['y'],
                            $this->signaturePosition['width'],
                            $this->signaturePosition['height'],
                            $imageType
                        );

                        \think\facade\Log::info('签名图片已添加到PDF');
                    } catch (Exception $e) {
                        throw new Exception('添加图片到PDF时出错: ' . $e->getMessage() . ', 图片路径: ' . $this->signaturePath);
                    }

                    // 添加当前日期
                    $dateTextForPdf = '';
                    $fontToUse = 'Helvetica'; // 默认字体
                    $dateFormatToUse = 'Y-m-d'; // 默认日期格式
                    $fontLoadedSuccessfully = false;
                    $debugFontMessage = 'Chinese font: ';

                    // 确定项目根目录和字体文件绝对路径
                    $projectRoot = dirname(dirname(dirname(dirname(__DIR__)))); // 假设 PdfSignatureProcessor.php 在 app/api/logic/ 下
                    $fontDir = $projectRoot . DIRECTORY_SEPARATOR.'server' .DIRECTORY_SEPARATOR .'public'
                               . DIRECTORY_SEPARATOR . 'assets'
                               . DIRECTORY_SEPARATOR . 'fonts' . DIRECTORY_SEPARATOR;
                    $chineseFontName = 'simsun'; // 用于 SetFont 的字体族名
                    $chineseFontFileName = 'simsun.ttf'; // 实际的字体文件名
                    $absoluteChineseFontPath = $fontDir . $chineseFontFileName;

                    // 检查中文字体文件是否存在于指定绝对路径
                    if (file_exists($absoluteChineseFontPath)) {
                        try {
                            // 尝试直接使用绝对路径加载 TTF 文件 (需要 FPDI 支持, 通常较新版本可以)
                            // 第三个参数为字体文件路径, 第四个参数 true 表示 Unicode (UTF-8)
                            $pdf->AddFont($chineseFontName, '', $absoluteChineseFontPath, true);
                            $pdf->SetFont($chineseFontName, '', 10);
                            $fontToUse = $chineseFontName;
                            $dateFormatToUse = $dateFormat; // 使用用户传入的原始格式, 如 'Y年m月d日'
                            $fontLoadedSuccessfully = true;
                            $debugFontMessage .= 'SimSun loaded from ' . $absoluteChineseFontPath;
                        } catch (Exception $e) {
                            // AddFont 或 SetFont 失败
                            $debugFontMessage .= 'Failed to load SimSun from ' . $absoluteChineseFontPath . '. Error: ' . $e->getMessage() . '. Using Helvetica.';
                            // fontLoadedSuccessfully 保持 false, fontToUse 和 dateFormatToUse 保持默认值
                        }
                    } else {
                        // 字体文件在指定路径不存在
                        $debugFontMessage .= 'SimSun not found at ' . $absoluteChineseFontPath . '. Using Helvetica.';
                        // fontLoadedSuccessfully 保持 false, fontToUse 和 dateFormatToUse 保持默认值
                    }

                    // 设置最终使用的字体和日期格式并写入PDF
                    $pdf->SetFont($fontToUse, '', 10);
                    $dateTextForPdf = date($dateFormatToUse);

                    // 直接使用原始坐标，不进行转换

                    // 添加第一个日期
                    \think\facade\Log::info('第一个日期位置 - X: ' . $this->datePosition['x'] . ', Y: ' . $this->datePosition['y']);
                    $pdf->SetXY($this->datePosition['x'], $this->datePosition['y']);
                    $pdf->Write(0, $dateTextForPdf);

                    // 添加第二个日期（在下方红框位置）
                    \think\facade\Log::info('第二个日期位置 - X: ' . $this->secondDatePosition['x'] . ', Y: ' . $this->secondDatePosition['y']);
                    $pdf->SetXY($this->secondDatePosition['x'], $this->secondDatePosition['y']);
                    $pdf->Write(0, $dateTextForPdf);

                    \think\facade\Log::info('日期已添加到PDF');

                    // 添加调试文本
                    // 1. ASCII调试文本 (红色)
                    $pdf->SetFont('Helvetica', '', 12);
                    $pdf->SetTextColor(255, 0, 0); // Red
                    $pdf->SetXY(10, 10);
//                    $pdf->Write(0, 'DEBUG ASCII TEXT');

                    // 2. 字体加载状态调试文本 (蓝色)
                    $pdf->SetFont('Helvetica', '', 10); // 使用通用字体显示此信息
                    $pdf->SetTextColor(0, 0, 255); // Blue
                    $pdf->SetXY(10, 20);
//                    $pdf->Write(0, $debugFontMessage);

                    // 3. 如果中文字体加载成功,额外用该字体显示中文调试信息
                    if ($fontLoadedSuccessfully) {
                        $pdf->SetFont($chineseFontName, '', 12);
                        $pdf->SetTextColor(0, 0, 200); // Darker Blue
                        $pdf->SetXY(10, 30); // 调整位置避免重叠
                        // 使用原始的包含中文的日期格式进行测试
                        // 注释掉未使用的变量
                        // $testChineseDate = date($dateFormat);
                        // $pdf->Write(0, '调试中文 (SimSun): ' . $testChineseDate);
                    }
                }
            }

            // 输出到文件
            $pdf->Output('F', $this->outputPath);

            return $this->outputPath;
        } catch (Exception $e) {
            throw new Exception('处理PDF文件时出错: ' . $e->getMessage());
        }
    }
}
