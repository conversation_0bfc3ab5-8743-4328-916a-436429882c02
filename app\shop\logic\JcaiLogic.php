<?php

namespace app\shop\logic;

use app\common\model\jcai\JcaiOrder;
use app\common\model\jcai\JcaiJoin;
use think\facade\Db;

/**
 * 集采购逻辑类
 * Class JcaiLogic
 * @package app\shop\logic
 */
class JcaiLogic
{
    /**
     * @notes 获取集采订单列表
     * @param array $params
     * @return array
     * @throws \think\db\exception\DbException
     */
    public static function lists($params)
    {
        $where = [];
        if (isset($params['shop_id']) && !empty($params['shop_id'])) {
            $where[] = ['shop_id', '=', $params['shop_id']];
        }
        
        if (isset($params['status']) && $params['status'] !== '') {
            $where[] = ['status', '=', $params['status']];
        }
        
        if (isset($params['keyword']) && !empty($params['keyword'])) {
            $where[] = ['order_sn|buyer_name', 'like', '%' . $params['keyword'] . '%'];
        }
        
        $jcaiOrder = new JcaiJoin();
        $count = $jcaiOrder->where($where)->count();
        $lists = $jcaiOrder->where($where)
            ->order('id desc')
             ->paginate([
                'page'      => $params['page'],
                'list_rows' => $params['limit'],
                'var_page' => 'page'
            ])->toArray();
                
            
        
        return [
            'count' => $count,
            'lists' => $lists,
            'page' => $params['page'],
            'limit' => $params['limit'],
        ];
    }
    
    /**
     * @notes 获取集采订单详情
     * @param int $id
     * @param int $shop_id
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function detail($id, $shop_id)
    {
        $detail = JcaiJoin::where([
            ['id', '=', $id],
            ['shop_id', '=', $shop_id]
        ])->find();
        
        if (empty($detail)) {
            return [];
        }
        
        $detail = $detail->toArray();
       
        // 获取订单商品信息
        $detail['goods'] = Db::name('order_goods')
            ->where('order_id', $detail['order_id'])
            ->select()
            ->toArray();
            
        return $detail;
    }
    
    /**
     * @notes 获取订单状态文本
     * @param int $status
     * @return string
     */
    private static function getStatusText($status)
    {
        $statusArr = [
            0 => '待付款',
            1 => '待发货',
            2 => '待收货',
            3 => '已完成',
            -1 => '已取消',
        ];
        
        return $statusArr[$status] ?? '未知状态';
    }
} 