<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Bmlb\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * 流量镜像健康检查返回的接收机状态信息。
 *
 * @method string getLanIp() 获取内网IP。
 * @method void setLanIp(string $LanIp) 设置内网IP。
 * @method array getReceiversPortStatusSet() 获取端口及对应的状态。
 * @method void setReceiversPortStatusSet(array $ReceiversPortStatusSet) 设置端口及对应的状态。
 */
class TrafficMirrorReciversStatus extends AbstractModel
{
    /**
     * @var string 内网IP。
     */
    public $LanIp;

    /**
     * @var array 端口及对应的状态。
     */
    public $ReceiversPortStatusSet;

    /**
     * @param string $LanIp 内网IP。
     * @param array $ReceiversPortStatusSet 端口及对应的状态。
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("LanIp",$param) and $param["LanIp"] !== null) {
            $this->LanIp = $param["LanIp"];
        }

        if (array_key_exists("ReceiversPortStatusSet",$param) and $param["ReceiversPortStatusSet"] !== null) {
            $this->ReceiversPortStatusSet = [];
            foreach ($param["ReceiversPortStatusSet"] as $key => $value){
                $obj = new TrafficMirrorPortStatus();
                $obj->deserialize($value);
                array_push($this->ReceiversPortStatusSet, $obj);
            }
        }
    }
}
