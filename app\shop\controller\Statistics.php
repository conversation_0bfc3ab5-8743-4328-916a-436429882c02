<?php



namespace app\shop\controller;


use app\common\basics\ShopBase;
use app\common\server\JsonServer;
use app\shop\logic\StatisticsLogic;


/**
 * 数据统计
 * Class Statistics
 * @package app\admin\controller
 */
class Statistics extends ShopBase
{
   
    //交易分析
    public function trading()
    {
        if($this->request->isAjax()){
            $post = $this->request->post();
            $res = StatisticsLogic::trading($post,$this->shop_id);
            return JsonServer::success('',$res);
        }
        return view();
    }


    //访问分析
    public function visit()
    {
        if($this->request->isAjax()){
            $post = $this->request->post();
            $res = StatisticsLogic::visit($post,$this->shop_id);
            return JsonServer::success('',$res);
        }
        return view();
    }


    //商品分析
    public function goods()
    {
        if($this->request->isAjax()){
            $get= $this->request->get();
            $res = StatisticsLogic::goods($get,$this->shop_id);
            return JsonServer::success('',$res);
        }
        return view();
    }

}