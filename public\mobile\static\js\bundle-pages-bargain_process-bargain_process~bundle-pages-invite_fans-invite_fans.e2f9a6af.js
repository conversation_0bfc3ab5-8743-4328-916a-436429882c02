(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["bundle-pages-bargain_process-bargain_process~bundle-pages-invite_fans-invite_fans"],{1400:function(t,e,i){"use strict";i.r(e);var n=i("9873"),r=i("48d0");for(var o in r)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return r[t]}))}(o);var a=i("f0c5"),s=Object(a["a"])(r["default"],n["b"],n["c"],!1,null,"5024cd44",null,!1,n["a"],void 0);e["default"]=s.exports},"1c75":function(t,e,i){"use strict";var n=i("7a54"),r=i.n(n);r.a},2399:function(t,e,i){"use strict";var n=i("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.children=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};e.indexKey;return{inject:(0,a.default)({},t,{default:null}),watch:{el:{handler:function(t,e){JSON.stringify(t)!=JSON.stringify(e)&&this.bindRelation()},deep:!0,immediate:!0},src:{handler:function(t,e){t!=e&&this.bindRelation()},immediate:!0},text:{handler:function(t,e){t!=e&&this.bindRelation()},immediate:!0},css:{handler:function(t,e){t!=e&&(this.el.css="object"==(0,o.default)(t)?t:t&&Object.assign.apply(Object,(0,r.default)(s(t)))||{})},immediate:!0},replace:{handler:function(t,e){JSON.stringify(t)!=JSON.stringify(e)&&this.bindRelation()},deep:!0,immediate:!0}},created:function(){var e=this;Object.defineProperty(this,"parent",{get:function(){return e[t]}}),Object.defineProperty(this,"index",{get:function(){var t,i;return e.bindRelation(),null===(t=e.parent)||void 0===t||null===(i=t.el.views)||void 0===i?void 0:i.indexOf(e.el)}}),this.el.type=this.type},beforeDestroy:function(){var t=this;this.parent&&(this.parent.el.views=this.parent.el.views.filter((function(e){return e._uid!==t._uid})))},methods:{bindRelation:function(){var t,e,i,n,o=this;(this.el._uid||(this.el._uid=this._uid),["text","qrcode"].includes(this.type))&&(this.el.text=(null===(t=this.$slots)||void 0===t||null===(e=t.default)||void 0===e||null===(i=e[0])||void 0===i?void 0:i.text)||(null===(n=this.text)||void 0===n?void 0:n.replace(/\\n/g,"\n")));if("text"==this.type&&this.replace&&(this.el.replace=this.replace),"image"==this.type&&(this.el.src=this.src),this.parent){var a=this.parent.el.views||[];-1!==a.indexOf(this.el)?this.parent.el.views=a.map((function(t){return t._uid==o._uid?o.el:t})):this.parent.el.views=[].concat((0,r.default)(a),[this.el])}}},mounted:function(){this.bindRelation()}}},e.parent=function(t){return{provide:function(){return(0,a.default)({},t,this)},data:function(){return{el:{css:{},views:[]}}},watch:{css:{handler:function(t){var e,i;this.canvasId&&(this.el.css="object"==(0,o.default)(t)?t:t&&Object.assign.apply(Object,(0,r.default)(s(t)))||{},this.canvasWidth=(null===(e=this.el.css)||void 0===e?void 0:e.width)||this.canvasWidth,this.canvasHeight=(null===(i=this.el.css)||void 0===i?void 0:i.height)||this.canvasHeight)},immediate:!0}}}};var r=n(i("d0ff")),o=n(i("0122")),a=n(i("fc11"));i("d81d"),i("4de4"),i("d3b7"),i("ac1f"),i("00b4"),i("5319"),i("e9c4"),i("7a82"),i("c975"),i("caad6"),i("99af");i("7f84");var s=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return t.split(";").filter((function(t){return t&&!/^[\n\s]+$/.test(t)})).map((function(t){var e,i,n=t.split(":");return(0,a.default)({},n[0].replace(/-([a-z])/g,(function(){return arguments[1].toUpperCase()})).replace(/\s+/g,""),(null===n||void 0===n||null===(e=n[1])||void 0===e||null===(i=e.replace(/^\s+/,""))||void 0===i?void 0:i.replace(/\s+$/,""))||"")}))}},"23f2":function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return r})),i.d(e,"a",(function(){}));var n=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view")},r=[]},2669:function(t,e,i){"use strict";i.r(e);var n=i("23f2"),r=i("ffe8");for(var o in r)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return r[t]}))}(o);var a=i("f0c5"),s=Object(a["a"])(r["default"],n["b"],n["c"],!1,null,"70de87fc",null,!1,n["a"],void 0);e["default"]=s.exports},2875:function(t,e,i){"use strict";i.r(e);var n=i("a712"),r=i("c13e");for(var o in r)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return r[t]}))}(o);i("8fed");var a=i("f0c5"),s=Object(a["a"])(r["default"],n["b"],n["c"],!1,null,"061dd044",null,!1,n["a"],void 0);e["default"]=s.exports},"2ab4":function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return r})),i.d(e,"a",(function(){}));var n=function(){var t=this.$createElement,e=this._self._c||t;return this.show?e("v-uni-view",{staticClass:"u-loading",class:"circle"==this.mode?"u-loading-circle":"u-loading-flower",style:[this.cricleStyle]}):this._e()},r=[]},3178:function(t,e,i){"use strict";i.r(e);var n=i("a51c"),r=i("c4b5");for(var o in r)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return r[t]}))}(o);var a=i("f0c5"),s=Object(a["a"])(r["default"],n["b"],n["c"],!1,null,"5f2f7706",null,!1,n["a"],void 0);e["default"]=s.exports},"356b":function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("a9e3");var n={name:"u-loading",props:{mode:{type:String,default:"circle"},color:{type:String,default:"#c7c7c7"},size:{type:[String,Number],default:"34"},show:{type:Boolean,default:!0}},computed:{cricleStyle:function(){var t={};return t.width=this.size+"rpx",t.height=this.size+"rpx","circle"==this.mode&&(t.borderColor="#e4e4e4 #e4e4e4 #e4e4e4 ".concat(this.color?this.color:"#c7c7c7")),t}}};e.default=n},"416f":function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=i("2399"),r={name:"lime-painter-qrcode",mixins:[(0,n.children)("painter")],props:{css:[String,Object],text:String},data:function(){return{type:"qrcode",el:{css:{},text:null}}}};e.default=r},"48d0":function(t,e,i){"use strict";i.r(e);var n=i("98b4"),r=i.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);e["default"]=r.a},"4aa8":function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=i("2399"),r={name:"lime-painter-view",mixins:[(0,n.children)("painter"),(0,n.parent)("painter")],props:{css:[String,Object]},data:function(){return{type:"view",el:{css:{},views:[]}}}};e.default=r},"4f21":function(t,e,i){var n,r,o,a=i("62f5").default;i("6c57"),i("d3b7"),i("a4d3"),i("e01a"),i("d28b"),i("3ca3"),i("ddb0"),i("d9e2"),i("d401"),i("14d9"),i("ac1f"),i("00b4"),i("acd8"),i("5319"),i("e9c4"),i("466d"),i("2ca0"),i("c975"),i("498a"),i("baa5"),i("7a82"),i("159b"),i("b64b"),i("d81d"),i("caad6"),i("2532"),i("13d5"),i("fb6a"),i("cb29"),i("4de4"),i("07ac"),function(i,s){"object"==a(e)&&"undefined"!=typeof t?s(e):(r=[e],n=s,o="function"===typeof n?n.apply(e,r):n,void 0===o||(t.exports=o))}(0,(function(t){"use strict";
/*! *****************************************************************************
      Copyright (c) Microsoft Corporation.
  
      Permission to use, copy, modify, and/or distribute this software for any
      purpose with or without fee is hereby granted.
  
      THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
      REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
      AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
      INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
      LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
      OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
      PERFORMANCE OF THIS SOFTWARE.
      ***************************************************************************** */var e,i=function(){return(i=Object.assign||function(t){for(var e,i=1,n=arguments.length;i<n;i++)for(var r in e=arguments[i])Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t}).apply(this,arguments)};function n(t,e,i,n){return new(i||(i=Promise))((function(r,o){function a(t){try{d(n.next(t))}catch(t){o(t)}}function s(t){try{d(n.throw(t))}catch(t){o(t)}}function d(t){var e;t.done?r(t.value):(e=t.value,e instanceof i?e:new i((function(t){t(e)}))).then(a,s)}d((n=n.apply(t,e||[])).next())}))}function r(t,e){var i,n,r,o,a={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]};return o={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function s(o){return function(s){return function(o){if(i)throw new TypeError("Generator is already executing.");for(;a;)try{if(i=1,n&&(r=2&o[0]?n.return:o[0]?n.throw||((r=n.return)&&r.call(n),0):n.next)&&!(r=r.call(n,o[1])).done)return r;switch(n=0,r&&(o=[2&o[0],r.value]),o[0]){case 0:case 1:r=o;break;case 4:return a.label++,{value:o[1],done:!1};case 5:a.label++,n=o[1],o=[0];continue;case 7:o=a.ops.pop(),a.trys.pop();continue;default:if(r=a.trys,!((r=r.length>0&&r[r.length-1])||6!==o[0]&&2!==o[0])){a=0;continue}if(3===o[0]&&(!r||o[1]>r[0]&&o[1]<r[3])){a.label=o[1];break}if(6===o[0]&&a.label<r[1]){a.label=r[1],r=o;break}if(r&&a.label<r[2]){a.label=r[2],a.ops.push(o);break}r[2]&&a.ops.pop(),a.trys.pop();continue}o=e.call(t,a)}catch(t){o=[6,t],n=0}finally{i=r=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,s])}}}var o={upx2px:function(t){return window.innerWidth/750*t},getSystemInfoSync:function(){return{screenWidth:window.innerWidth}},getImageInfo:function(t){var e=t.src,i=t.success,n=t.fail,r=new Image;r.onload=function(){i({width:this.naturalWidth,height:this.naturalHeight,path:this.src,src:e})},r.onerror=n,r.src=e}},s="object"==("undefined"===typeof swan?"undefined":a(swan))?"mp-baidu":"object"==("undefined"===typeof tt?"undefined":a(tt))?"mp-toutao":"object"==("undefined"===typeof plus?"undefined":a(plus))?"plus":"object"==("undefined"===typeof window?"undefined":a(window))?"undefined"==typeof uni||"undefined"!=typeof uni&&!(null===uni||void 0===uni?void 0:uni.addInterceptor)?"web":"h5":"mp-weixin",d="mp-weixin"==s?wx:"mp-toutao"==s?tt:"undefined"!=typeof uni?uni.upx2px?uni:Object.assign(uni,o):"undefined"!=typeof window?o:uni;if(!d.upx2px){var c=(null!==(e=d.getSystemInfoSync().screenWidth)&&void 0!==e?e:375)/750;d.upx2px=function(t){return c*t}}function u(t){return/^-?\d+(\.\d+)?$/.test(t)}function l(t,e,i){if("number"==typeof t)return t;if(u(t))return 1*t;if("string"==typeof t){var n=/^-?([0-9]+)?([.]{1}[0-9]+){0,1}(em|rpx|px|%)$/g.exec(t);if(!t||!n)return 0;var r=n[3];t=parseFloat(t);var o=0;return"rpx"===r?o=d.upx2px(t):"px"===r?o=1*t:"%"===r&&e?o=t*l(e)/100:"em"===r&&e&&(o=t*l(e||14)),i?1*o.toFixed(2):Math.round(o)}return 0}function f(t){var e=this;return new Promise((function(i,o){return n(e,void 0,void 0,(function(){var e,n;return r(this,(function(r){switch(r.label){case 0:if(e=t,"plus"!=s&&!/^mp/.test(s)||!/data:image\/(\w+);base64,(.*)/.test(t))return[3,4];r.label=1;case 1:return r.trys.push([1,3,,4]),[4,(a=t,new Promise((function(t,e){var i=/data:image\/(\w+);base64,(.*)/.exec(a)||[],n=i[1];if(i[2],/^mp/.test(s)){var r=d.getFileSystemManager();n||(console.error("ERROR_BASE64SRC_PARSE"),e(new Error("ERROR_BASE64SRC_PARSE")));var o=(new Date).getTime(),c=d.env.USER_DATA_PATH+"/"+o+"."+n;r.writeFile({filePath:c,data:a.replace(/^data:\S+\/\S+;base64,/,""),encoding:"base64",success:function(){t(c)},fail:function(t){console.error("获取base64图片失败",JSON.stringify(t)),e(t)}})}else if("plus"!=s)e(new Error("not support"));else{var u=a.split(",")[0].match(/data\:\S+\/(\S+);/);u?u=u[1]:e(new Error("base64 error"));var l=Date.now()+"."+u,f="_doc/uniapp_temp/"+l;if(!function(t,e){for(var i=t.split("."),n=e.split("."),r=!1,o=0;o<n.length;o++){var a=i[o]-n[o];if(0!==a){r=a>0;break}}return r}("Android"===plus.os.name?"1.9.9.80627":"1.9.9.80472",plus.runtime.innerVersion))return void plus.io.resolveLocalFileSystemURL("_doc",(function(i){i.getDirectory("uniapp_temp",{create:!0,exclusive:!1},(function(i){i.getFile(l,{create:!0,exclusive:!1},(function(i){i.createWriter((function(i){var n;i.onwrite=function(){t(f)},i.onerror=e,i.seek(0),i.writeAsBinary((n=a.split(","))[n.length-1])}),e)}),e)}),e)}),e);var h=new plus.nativeObj.Bitmap(l);h.loadBase64Data(a,(function(){h.save(f,{},(function(){h.clear(),t(f)}),(function(t){h.clear(),e(t)}))}),(function(t){h.clear(),e(t)}))}})))];case 2:return e=r.sent(),[3,4];case 3:return n=r.sent(),console.log(n),[3,4];case 4:return d.getImageInfo({src:e,success:function(t){return i(t)},fail:function(t){return o(t)}}),[2]}var a}))}))}))}function h(t){for(var e=[],i=[],n=0,r=t.substring(0,t.length-1).split("%,");n<r.length;n++){var o=r[n];e.push(o.substring(0,o.lastIndexOf(" ")).trim()),i.push(o.substring(o.lastIndexOf(" "),o.length)/100)}return{colors:e,percents:i}}function v(t,e,i){return e in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}function p(){return(p=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var i=arguments[e];for(var n in i)Object.prototype.hasOwnProperty.call(i,n)&&(t[n]=i[n])}return t}).apply(this,arguments)}var g=0,b={left:null,top:null,width:null,height:null},m=function(){function t(t,e,i,n){var r=this;v(this,"id",g++),v(this,"style",{left:null,top:null,width:null,height:null}),v(this,"computedStyle",{}),v(this,"children",{}),v(this,"layoutBox",p({},b)),v(this,"contentSize",p({},b,{maxLineHeight:0})),v(this,"clientSize",p({},b)),v(this,"borderSize",p({},b)),v(this,"offsetSize",p({},b)),this.ctx=n,this.root=i,e&&(this.parent=e),this.name=t.name||t.type,this.attributes=this.getAttributes(t);var o=this.getComputedStyle(t,null==e?void 0:e.computedStyle);this.isAbsolute="absolute"==o.position,this.isFixed="fixed"==o.position,Object.keys(o).forEach((function(t){Object.defineProperty(r.style,t,{configurable:!0,enumerable:!0,get:function(){return o[t]},set:function(e){o[t]=e}})}));var a={contentSize:p({},this.contentSize),clientSize:p({},this.clientSize),borderSize:p({},this.borderSize),offsetSize:p({},this.offsetSize)};Object.keys(a).forEach((function(t){Object.keys(r[t]).forEach((function(e){Object.defineProperty(r[t],e,{configurable:!0,enumerable:!0,get:function(){return a[t][e]},set:function(i){a[t][e]=i}})}))})),this.computedStyle=this.style}var e=t.prototype;return e.add=function(t){t.parent=this,this.children[t.id]=t},e.remove=function(t){var e=this;t?this.children[t.id]&&(t.remove(),delete this.children[t.id]):Object.keys(this.children).forEach((function(t){e.children[t].remove(),delete e.children[t]}))},e.getChildren=function(){var t=this;return Object.keys(this.children).map((function(e){return t.children[e]}))},e.getComputedStyle=function(t,e){var i=["color","fontSize","lineHeight","verticalAlign","fontWeight","textAlign"],n=t.css,r=void 0===n?{}:n,o=t.type,s=void 0===o?"view":o,d={};if(e)for(var c=0;c<i.length;c++){var f=i[c];(r[f]||e[f])&&(r[f]=r[f]||e[f])}for(var h=function(){var t=p[v],e=r[t];if(/^(box)?shadow$/i.test(t)){var i=e.split(" ").map((function(t){return/^\d/.test(t)?l(t):t}));return d.boxShadow=i,"continue"}if(/^border/i.test(t)&&!/radius$/i.test(t)){var n,o=t.match(/^border([BTRLa-z]+)?/)[0],c=t.match(/[W|S|C][a-z]+/),f=e.replace(/([\(,])\s+|\s+([\),])/g,"$1$2").split(" ").map((function(t){return/^\d/.test(t)?l(t,"",!0):t}));return d[o]=((n={})[o+"Width"]=(u(f[0])?f[0]:0)||1,n[o+"Style"]=f[1]||"solid",n[o+"Color"]=f[2]||"black",n),1==f.length&&c&&(d[o][o+c[0]]=f[0]),"continue"}if(/^background(color)?$/i.test(t))return d.backgroundColor=e,"continue";if(/^objectPosition$/i.test(t))return d[t]=e.split(" "),"continue";if(/padding|margin|radius/i.test(t)){var h=/radius$/i.test(t),g=h?"borderRadius":t.match(/[a-z]+/)[0],b=[0,0,0,0].map((function(t,e){return h?["borderTopLeftRadius","borderTopRightRadius","borderBottomRightRadius","borderBottomLeftRadius"][e]:[g+"Top",g+"Right",g+"Bottom",g+"Left"][e]}));if("padding"===t||"margin"===t||/^(border)?radius$/i.test(t)){var m,w=(null==e?void 0:e.split(" ").map((function(e){return/^\d+(rpx|px)?$/.test(e)?l(e):"margin"!=t&&/auto/.test(e)?0:e}),[]))||[0],x=h?"borderRadius":t,y=w[0],S=w[1],I=w[2],z=w[3];d[x]=((m={})[b[0]]="auto"==y?0:y,m[b[1]]=u(S)?S:y,m[b[2]]="auto"==(u(I)?I:y)?0:u(I)?I:y,m[b[3]]=u(z)?z:S||y,m)}else{var P;"object"==a(d[g])||(d[g]=((P={})[b[0]]=d[g]||0,P[b[1]]=d[g]||0,P[b[2]]=d[g]||0,P[b[3]]=d[g]||0,P)),d[g][t]="margin"==g&&"auto"==e||/%$/.test(e)?e:l(e)}return"continue"}if(/^transform$/i.test(t))return d[t]={},e.replace(/([a-zA-Z]+)\(([0-9,-\.%rpxdeg\s]+)\)/g,(function(e,i,n){var o=n.split(",").map((function(t){return t.replace(/(^\s*)|(\s*$)/g,"")})),a=function(t,e){return t.includes("deg")?1*t:e&&!/%$/.test(e)?l(t,e):t};i.includes("matrix")?d[t][i]=o.map((function(t){return 1*t})):i.includes("rotate")?d[t][i]=1*n.match(/^-?\d+(\.\d+)?/)[0]:/[X, Y]/.test(i)?d[t][i]=/[X]/.test(i)?a(o[0],r.width):a(o[0],r.height):(d[t][i+"X"]=a(o[0],r.width),d[t][i+"Y"]=a(o[1]||o[0],r.height))})),"continue";/^left|top$/i.test(t)&&!["absolute","fixed"].includes(r.position)?d[t]=0:d[t]=/^[\d\.]+(px|rpx)?$/.test(e)?l(e):/em$/.test(e)&&"text"==s?l(e,r.fontSize):e},v=0,p=Object.keys(r);v<p.length;v++)h();return d},e.setPosition=function(t,e){var i={left:"width",top:"height",right:"width",bottom:"height"};Object.keys(i).forEach((function(n){["right","bottom"].includes(n)&&void 0!==t.style[n]?t.style["right"==n?"left":"top"]=e[i[n]]-t.offsetSize[i[n]]-l(t.style[n],e[i[n]]):t.style[n]=l(t.style[n],e[i[n]])}))},e.getAttributes=function(t){var e=t.attributes||{};return(null!=t&&t.url||null!=t&&t.src)&&(e.src=e.src||t.url||(null==t?void 0:t.src)),t.replace&&(e.replace=t.replace),null!=t&&t.text&&(e.text=t.text),e},e.getOffsetSize=function(t,e,i){void 0===i&&(i="offsetSize");var n=e||{},r=n.margin,o=(r=void 0===r?{}:r).marginLeft,a=void 0===o?0:o,s=r.marginTop,d=void 0===s?0:s,c=r.marginRight,u=void 0===c?0:c,l=r.marginBottom,f=void 0===l?0:l,h=n.padding,v=(h=void 0===h?{}:h).paddingLeft,p=void 0===v?0:v,g=h.paddingTop,b=void 0===g?0:g,m=h.paddingRight,w=void 0===m?0:m,x=h.paddingBottom,y=void 0===x?0:x,S=n.border,I=(S=void 0===S?{}:S).borderWidth,z=void 0===I?0:I,P=n.borderTop,M=(P=void 0===P?{}:P).borderTopWidth,k=void 0===M?z:M,B=n.borderBottom,j=(B=void 0===B?{}:B).borderBottomWidth,R=void 0===j?z:j,O=n.borderRight,T=(O=void 0===O?{}:O).borderRightWidth,C=void 0===T?z:T,W=n.borderLeft,H=(W=void 0===W?{}:W).borderLeftWidth,N=void 0===H?z:H;return"contentSize"==i&&(this[i].left=t.left+a+p+N,this[i].top=t.top+d+b+k,this[i].width=t.width,this[i].height=t.height),"clientSize"==i&&(this[i].left=t.left+a+N,this[i].top=t.top+d+k,this[i].width=t.width+p+w,this[i].height=t.height+b+y),"borderSize"==i&&(this[i].left=t.left+a+N/2,this[i].top=t.top+d+k/2,this[i].width=t.width+p+w+N/2+C/2,this[i].height=t.height+b+y+R/2+k/2),"offsetSize"==i&&(this[i].left=t.left,this[i].top=t.top,this[i].width=t.width+p+w+N+C+a+u,this[i].height=t.height+b+y+R+k+f+d),this[i]},e.layoutBoxUpdate=function(t,e,i,n){if(void 0===i&&(i=""),"border-box"==(null==e?void 0:e.boxSizing)){var r=e||{},o=r.border,a=(o=void 0===o?{}:o).borderWidth,s=void 0===a?0:a,d=r.borderTop,c=(d=void 0===d?{}:d).borderTopWidth,u=void 0===c?s:c,l=r.borderBottom,f=(l=void 0===l?{}:l).borderBottomWidth,h=void 0===f?s:f,v=r.borderRight,p=(v=void 0===v?{}:v).borderRightWidth,g=void 0===p?s:p,b=r.borderLeft,m=(b=void 0===b?{}:b).borderLeftWidth,w=void 0===m?s:m,x=r.padding,y=(x=void 0===x?{}:x).paddingTop,S=void 0===y?0:y,I=x.paddingRight,z=void 0===I?0:I,P=x.paddingBottom,M=void 0===P?0:P,k=x.paddingLeft,B=void 0===k?0:k;"width"==i&&(t.width-=B+z+g+w),"height"!=i||n||(t.height-=S+M+u+h)}this.layoutBox.contentSize=this.getOffsetSize(t,e,"contentSize"),this.layoutBox.clientSize=this.getOffsetSize(t,e,"clientSize"),this.layoutBox.borderSize=this.getOffsetSize(t,e,"borderSize"),this.layoutBox.offsetSize=this.getOffsetSize(t,e,"offsetSize"),this.layoutBox=Object.assign({},this.layoutBox,this.layoutBox.borderSize)},e.getBoxPosition=function(t){var e=this.computedStyle,i=this.getChildren(),n=e.verticalAlign,r=e.left,o=void 0===r?0:r,a=e.top,s=void 0===a?0:a,d=p({},this.contentSize,{left:o,top:s}),c=this.contentSize.top-this.offsetSize.top,u=this.contentSize.left-this.offsetSize.left,l=0;if("bottom"==n&&this.contentSize.maxLineHeight?l=this.contentSize.maxLineHeight-this.contentSize.height:"middle"==n&&this.contentSize.maxLineHeight&&(l=(this.contentSize.maxLineHeight-this.contentSize.height)/2),d.top+=l,i.length){o+=u,s+=c;for(var f=null,h=null,v=!1,g=0;g<i.length;g++){var b=i[g];if(b.isAbsolute||b.isFixed)b.isAbsolute?(b.setPosition(b,d),b.style.left+=o,b.style.top+=s,b.getBoxPosition()):(b.setPosition(b,this.root),b.getBoxPosition());else if(0==g)b.style.left+=o,b.style.top+=s,b.getBoxPosition(),f=b,h=b;else{var m,w,x,y;(null==(m=h)?void 0:m.offsetSize.height)<(null==(w=f)?void 0:w.offsetSize.height)&&(h=f);var S,I,z,P,M=(null==(x=f)?void 0:x.offsetSize.left)+(null==(y=f)?void 0:y.offsetSize.width)+b.offsetSize.width>d.left+d.width+u;if(this.getBoxState(f,b)||M)b.style.left+=o,(null==(S=f)?void 0:S.offsetSize.height)>=(null==(I=h)?void 0:I.offsetSize.height)?b.style.top+=f.offsetSize.top+f.offsetSize.height||0:b.style.top+=(null==(z=h)?void 0:z.offsetSize.top)+(null==(P=h)?void 0:P.offsetSize.height)||0,b.getBoxPosition(),f=b,h=b,v=!0;else b.style.left+=f.offsetSize.left+f.offsetSize.width,b.style.top+=v?f.offsetSize.top:s,b.getBoxPosition(),f=b}}this.layoutBoxUpdate(d,e)}else this.layoutBoxUpdate(d,e);return this.layoutBox},e.setMaxLineHeight=function(t,e,i){for(var n=t;n>=0&&!e[n].contentSize.maxLineHeight;)e[n].contentSize.maxLineHeight=i,n--},e.getBoxState=function(t,e){return"view"==e.name&&"inline-block"!==e.style.display||"view"==(null==t?void 0:t.name)&&"inline-block"!==(null==t?void 0:t.style.display)||"block"==e.style.display||"block"==(null==t?void 0:t.style.display)},e.getBoxHieght=function(){var t,e=this,i=this.name,n=this.computedStyle,r=this.attributes,o=this.parent,a=this.getChildren(),s=n.top,d=n.bottom,c=n.height,u=void 0===c?0:c,f=n.fontSize,h=void 0===f?14:f,v=n.position,g=n.lineHeight,b=void 0===g?"1.4em":g;n.lineClamp;var m=p({},this.contentSize);if("image"==i&&null==u){var w=r.width,x=r.height;r.mode,m.height=Math.round(m.width*x/w)||0,this.layoutBoxUpdate(m,n,"height")}else if(u)if(a.length){var y=null,S=0;a.forEach((function(t,i){var n,r,o=i==a.length-1;t.getBoxHieght();var s=(null==(n=y)?void 0:n.offsetSize.left)+(null==(r=y)?void 0:r.offsetSize.width)+t.offsetSize.width>m.left+m.width,d=e.getBoxState(y,t);if(s||d){if(s){for(var c=i-1;c>=0&&!a[c].contentSize.maxLineHeight;)S<a[c].contentSize.height&&(S=a[c].contentSize.height),c--;e.setMaxLineHeight(i-1,a,S),S=0}}else if(o){for(var u=i;u>=0&&!a[u].contentSize.maxLineHeight;)S<a[u].contentSize.height&&(S=a[u].contentSize.height),u--;e.setMaxLineHeight(i,a,S),S=0}y=t}))}else this.layoutBoxUpdate(m,n,"height");else{var I=0;if(null!==s&&(this.isAbsolute||this.isFixed&&o.contentSize.height)){var z="absolute"==v?o.contentSize.height:this.root.height;I=z-(/%$/.test(s)?l(s,z):s)-(/%$/.test(d)?l(d,z):d)}if("text"==i)b=l(b,h),m.height=I||this.attributes.lines*b,this.layoutBoxUpdate(m,n,"height",!0);else if(a.length){var P=0,M=null,k=0;m.height=a.reduce((function(t,i,n){var r=n==a.length-1;if(i.isAbsolute||i.isFixed)return r?t+P:t;i.getBoxHieght();var o=e.getBoxState(M,i),s=k+i.offsetSize.width>m.width;if(s||o){var d,c,u=0;return s||M&&("view"!==(null==(d=M)?void 0:d.name)||"inline-block"==(null==(c=M)?void 0:c.style.display))?(r&&(e.setMaxLineHeight(n-1,a,P),P+=i.offsetSize.height),u=t+P,P=i.offsetSize.height,k=i.offsetSize.width,M=i,u):(k=0,P=0,t+i.offsetSize.height)}return k+=i.offsetSize.width,P=Math.max(P,i.offsetSize.height)||0,r?(e.setMaxLineHeight(n,a,P),t+P):(M=i,t)}),0),I&&(m.height=I),this.layoutBoxUpdate(m,n)}else I&&(m.height=I),this.layoutBoxUpdate(m,n,"height")}if(n.borderRadius&&null!=(t=this.borderSize)&&t.width)for(var B in n.borderRadius)Object.hasOwnProperty.call(n.borderRadius,B)&&(n.borderRadius[B]=l(n.borderRadius[B],this.borderSize.width));return this.layoutBox},e.contrastSize=function(t,e,i){var n=t;return i&&(n=Math.min(n,i)),e&&(n=Math.max(n,e)),n},e.measureText=function(t,e){var i=this.ctx.measureText(t);return{width:i.width,fontHeight:(i.actualBoundingBoxAscent||.7*e)+1}},e.getBoxWidth=function(){var t,e=this,i=this.name,n=this.computedStyle,r=this.attributes,o=this.parent,a=void 0===o?{}:o,s=this.ctx,d=this.getChildren(),c=n.left,u=void 0===c?0:c;n.top;var f=n.right,h=n.width,v=void 0===h?0:h,p=n.minWidth,g=n.maxWidth,b=n.height,m=void 0===b?0:b,w=n.fontSize,x=void 0===w?14:w,y=n.fontWeight,S=n.fontFamily,I=n.textStyle,z=n.position,P=n.display,M=n.lineClamp,k=n.padding,B=void 0===k?{}:k,j=n.margin,R=void 0===j?{}:j,O=n.border,T=(O=void 0===O?{}:O).borderWidth,C=void 0===T?0:T,W=n.borderRight,H=(W=void 0===W?{}:W).borderRightWidth,N=void 0===H?C:H,L=n.borderLeft,D=(L=void 0===L?{}:L).borderLeftWidth,A=void 0===D?C:D;if(/%$/.test(v)&&a.contentSize.width&&(v=l(v,a.contentSize.width,!0)),/%$/.test(m)&&a.contentSize.height&&(m=l(m,a.contentSize.height)),/%$/.test(p)&&a.contentSize.width&&(p=l(p,a.contentSize.width,!0)),/%$/.test(g)&&a.contentSize.width&&(g=l(g,a.contentSize.width,!0)),n.padding&&null!=(t=a.contentSize)&&t.width)for(var _ in n.padding)Object.hasOwnProperty.call(n.padding,_)&&(n.padding[_]=l(n.padding[_],a.contentSize.width));var F=B.paddingRight,U=void 0===F?0:F,$=B.paddingLeft,J=void 0===$?0:$;if(n.margin&&[n.margin.marginLeft,n.margin.marginRight].includes("auto"))if(v){var E=a.contentSize.width-v-U-J-A-N||0;n.margin.marginLeft==n.margin.marginRight?n.margin.marginLeft=n.margin.marginRight=E/2:"auto"==n.margin.marginLeft?n.margin.marginLeft=E:n.margin.marginRight=E}else n.margin.marginLeft=n.margin.marginRight=0;var Z=R.marginRight,G=void 0===Z?0:Z,Q=R.marginLeft,Y={width:v,height:m,left:0,top:0},V=J+U+A+N+(void 0===Q?0:Q)+G;if("text"==i&&!this.attributes.widths){var q=r.text||"";s.save(),s.setFonts({fontFamily:S,fontSize:x,fontWeight:y,textStyle:I}),q.split("\n").map((function(t){var i=t.split("").map((function(t){return e.measureText(t,x).width}));e.attributes.fontHeight=e.measureText(t,x).fontHeight,e.attributes.widths||(e.attributes.widths=[]),e.attributes.widths.push({widths:i,total:i.reduce((function(t,e){return t+e}),0)})})),s.restore()}if("image"==i&&null==v){var X=r.width,K=r.height;Y.width=this.contrastSize(Math.round(X*m/K)||0,p,g),this.layoutBoxUpdate(Y,n,"width")}else if(v)d.length?(this.layoutBoxUpdate(Y,n,"width"),d.forEach((function(t){t.getBoxWidth()}))):this.layoutBoxUpdate(Y,n,"width");else{var tt=0;if((this.isAbsolute||this.isFixed)&&a.contentSize.width){var et="absolute"==z?a.contentSize.width:this.root.width;tt=et-(/%$/.test(u)?l(u,et):u)-(/%$/.test(f)?l(f,et):f)}if("text"==i){var it=this.attributes.widths,nt=Math.max.apply(Math,it.map((function(t){return t.total})));a&&a.contentSize.width>0&&(nt>a.contentSize.width||"block"==P)&&!this.isAbsolute&&!this.isFixed&&(nt=a.contentSize.width-V),Y.width=tt||this.contrastSize(nt,p,g),this.layoutBoxUpdate(Y,n,"width")}else if("view"!=i||!a||"inline-block"===P||this.isAbsolute||this.isFixed)if(d.length){for(var rt=0,ot=null,at=0;d.length>at;){var st=d[at],dt=at==d.length-1,ct=this.getBoxState(ot,st);if(!st.isFixed&&!st.isAbsolute)if(!ot||ct){var ut=st.getBoxWidth();rt=Math.max(rt,ut.width)||0,ot=st}else if(ot.offsetSize.left+ot.offsetSize.width+st.offsetSize.width<a.contentSize.width&&at!==d.length-1)rt+=st.getBoxWidth().width,ot=st;else{var lt=st.getBoxWidth();dt?rt+=lt.width:rt=a.contentSize.width,ot=null}at++}Y.width=tt||this.contrastSize(Math.ceil(rt),p,g),d.forEach((function(t){"block"!=t.style.display||"text"!=t.name||t.isFixed||t.isAbsolute||t.style.width||(t.style.width=Y.width,t.getBoxWidth())})),this.layoutBoxUpdate(Y,n,"width")}else Y.width=tt,this.layoutBoxUpdate(Y,n,"width");else Y.width=this.contrastSize(a.contentSize.width-V,p,g),this.layoutBoxUpdate(Y,n),d.length&&d.forEach((function(t){t.getBoxWidth()}))}if("text"==i&&!this.attributes.lines){var ft=this.attributes.widths.length;this.attributes.widths.forEach((function(t){return t.widths.reduce((function(t,e,i){return t+e>Y.width?(ft++,e):t+e}),0)})),ft=M&&ft>M?M:ft,this.attributes.lines=ft}return this.layoutBox},e.layout=function(){return this.getBoxWidth(),this.getBoxHieght(),this.getBoxPosition(),this.offsetSize},t}(),w=function(){var t,e,i,n,r,o,a=[0,11,15,19,23,27,31,16,18,20,22,24,26,28,20,22,24,24,26,28,28,22,24,24,26,26,28,28,24,24,26,26,26,28,28,24,26,26,26,28,28],s=[3220,1468,2713,1235,3062,1890,2119,1549,2344,2936,1117,2583,1330,2470,1667,2249,2028,3780,481,4011,142,3098,831,3445,592,2517,1776,2234,1951,2827,1070,2660,1345,3177],d=[30660,29427,32170,30877,26159,25368,27713,26998,21522,20773,24188,23371,17913,16590,20375,19104,13663,12392,16177,14854,9396,8579,11994,11245,5769,5054,7399,6608,1890,597,3340,2107],c=[1,0,19,7,1,0,16,10,1,0,13,13,1,0,9,17,1,0,34,10,1,0,28,16,1,0,22,22,1,0,16,28,1,0,55,15,1,0,44,26,2,0,17,18,2,0,13,22,1,0,80,20,2,0,32,18,2,0,24,26,4,0,9,16,1,0,108,26,2,0,43,24,2,2,15,18,2,2,11,22,2,0,68,18,4,0,27,16,4,0,19,24,4,0,15,28,2,0,78,20,4,0,31,18,2,4,14,18,4,1,13,26,2,0,97,24,2,2,38,22,4,2,18,22,4,2,14,26,2,0,116,30,3,2,36,22,4,4,16,20,4,4,12,24,2,2,68,18,4,1,43,26,6,2,19,24,6,2,15,28,4,0,81,20,1,4,50,30,4,4,22,28,3,8,12,24,2,2,92,24,6,2,36,22,4,6,20,26,7,4,14,28,4,0,107,26,8,1,37,22,8,4,20,24,12,4,11,22,3,1,115,30,4,5,40,24,11,5,16,20,11,5,12,24,5,1,87,22,5,5,41,24,5,7,24,30,11,7,12,24,5,1,98,24,7,3,45,28,15,2,19,24,3,13,15,30,1,5,107,28,10,1,46,28,1,15,22,28,2,17,14,28,5,1,120,30,9,4,43,26,17,1,22,28,2,19,14,28,3,4,113,28,3,11,44,26,17,4,21,26,9,16,13,26,3,5,107,28,3,13,41,26,15,5,24,30,15,10,15,28,4,4,116,28,17,0,42,26,17,6,22,28,19,6,16,30,2,7,111,28,17,0,46,28,7,16,24,30,34,0,13,24,4,5,121,30,4,14,47,28,11,14,24,30,16,14,15,30,6,4,117,30,6,14,45,28,11,16,24,30,30,2,16,30,8,4,106,26,8,13,47,28,7,22,24,30,22,13,15,30,10,2,114,28,19,4,46,28,28,6,22,28,33,4,16,30,8,4,122,30,22,3,45,28,8,26,23,30,12,28,15,30,3,10,117,30,3,23,45,28,4,31,24,30,11,31,15,30,7,7,116,30,21,7,45,28,1,37,23,30,19,26,15,30,5,10,115,30,19,10,47,28,15,25,24,30,23,25,15,30,13,3,115,30,2,29,46,28,42,1,24,30,23,28,15,30,17,0,115,30,10,23,46,28,10,35,24,30,19,35,15,30,17,1,115,30,14,21,46,28,29,19,24,30,11,46,15,30,13,6,115,30,14,23,46,28,44,7,24,30,59,1,16,30,12,7,121,30,12,26,47,28,39,14,24,30,22,41,15,30,6,14,121,30,6,34,47,28,46,10,24,30,2,64,15,30,17,4,122,30,29,14,46,28,49,10,24,30,24,46,15,30,4,18,122,30,13,32,46,28,48,14,24,30,42,32,15,30,20,4,117,30,40,7,47,28,43,22,24,30,10,67,15,30,19,6,118,30,18,31,47,28,34,34,24,30,20,61,15,30],u=[255,0,1,25,2,50,26,198,3,223,51,238,27,104,199,75,4,100,224,14,52,141,239,129,28,193,105,248,200,8,76,113,5,138,101,47,225,36,15,33,53,147,142,218,240,18,130,69,29,181,194,125,106,39,249,185,201,154,9,120,77,228,114,166,6,191,139,98,102,221,48,253,226,152,37,179,16,145,34,136,54,208,148,206,143,150,219,189,241,210,19,92,131,56,70,64,30,66,182,163,195,72,126,110,107,58,40,84,250,133,186,61,202,94,155,159,10,21,121,43,78,212,229,172,115,243,167,87,7,112,192,247,140,128,99,13,103,74,222,237,49,197,254,24,227,165,153,119,38,184,180,124,17,68,146,217,35,32,137,46,55,63,209,91,149,188,207,205,144,135,151,178,220,252,190,97,242,86,211,171,20,42,93,158,132,60,57,83,71,109,65,162,31,45,67,216,183,123,164,118,196,23,73,236,127,12,111,246,108,161,59,82,41,157,85,170,251,96,134,177,187,204,62,90,203,89,95,176,156,169,160,81,11,245,22,235,122,117,44,215,79,174,213,233,230,231,173,232,116,214,244,234,168,80,88,175],l=[1,2,4,8,16,32,64,128,29,58,116,232,205,135,19,38,76,152,45,90,180,117,234,201,143,3,6,12,24,48,96,192,157,39,78,156,37,74,148,53,106,212,181,119,238,193,159,35,70,140,5,10,20,40,80,160,93,186,105,210,185,111,222,161,95,190,97,194,153,47,94,188,101,202,137,15,30,60,120,240,253,231,211,187,107,214,177,127,254,225,223,163,91,182,113,226,217,175,67,134,17,34,68,136,13,26,52,104,208,189,103,206,129,31,62,124,248,237,199,147,59,118,236,197,151,51,102,204,133,23,46,92,184,109,218,169,79,158,33,66,132,21,42,84,168,77,154,41,82,164,85,170,73,146,57,114,228,213,183,115,230,209,191,99,198,145,63,126,252,229,215,179,123,246,241,255,227,219,171,75,150,49,98,196,149,55,110,220,165,87,174,65,130,25,50,100,200,141,7,14,28,56,112,224,221,167,83,166,81,162,89,178,121,242,249,239,195,155,43,86,172,69,138,9,18,36,72,144,61,122,244,245,247,243,251,235,203,139,11,22,44,88,176,125,250,233,207,131,27,54,108,216,173,71,142,0],f=[],h=[],v=[],p=[],g=[],b=2;function m(t,e){var i;t>e&&(i=t,t=e,e=i),i=e,i*=e,i+=e,i>>=1,p[i+=t]=1}function w(t,i){var n;for(v[t+e*i]=1,n=-2;n<2;n++)v[t+n+e*(i-2)]=1,v[t-2+e*(i+n+1)]=1,v[t+2+e*(i+n)]=1,v[t+n+1+e*(i+2)]=1;for(n=0;n<2;n++)m(t-1,i+n),m(t+1,i-n),m(t-n,i-1),m(t+n,i+1)}function x(t){for(;t>=255;)t=((t-=255)>>8)+(255&t);return t}var y=[];function S(t,e,i,n){var r,o,a;for(r=0;r<n;r++)f[i+r]=0;for(r=0;r<e;r++){if(255!=(a=u[f[t+r]^f[i]]))for(o=1;o<n;o++)f[i+o-1]=f[i+o]^l[x(a+y[n-o])];else for(o=i;o<i+n;o++)f[o]=f[o+1];f[i+n-1]=255==a?0:l[x(a+y[0])]}}function I(t,e){var i;return t>e&&(i=t,t=e,e=i),i=e,i+=e*e,i>>=1,p[i+=t]}function z(t){var i,n,r,o;switch(t){case 0:for(n=0;n<e;n++)for(i=0;i<e;i++)i+n&1||I(i,n)||(v[i+n*e]^=1);break;case 1:for(n=0;n<e;n++)for(i=0;i<e;i++)1&n||I(i,n)||(v[i+n*e]^=1);break;case 2:for(n=0;n<e;n++)for(r=0,i=0;i<e;i++,r++)3==r&&(r=0),r||I(i,n)||(v[i+n*e]^=1);break;case 3:for(o=0,n=0;n<e;n++,o++)for(3==o&&(o=0),r=o,i=0;i<e;i++,r++)3==r&&(r=0),r||I(i,n)||(v[i+n*e]^=1);break;case 4:for(n=0;n<e;n++)for(r=0,o=n>>1&1,i=0;i<e;i++,r++)3==r&&(r=0,o=!o),o||I(i,n)||(v[i+n*e]^=1);break;case 5:for(o=0,n=0;n<e;n++,o++)for(3==o&&(o=0),r=0,i=0;i<e;i++,r++)3==r&&(r=0),(i&n&1)+!(!r|!o)||I(i,n)||(v[i+n*e]^=1);break;case 6:for(o=0,n=0;n<e;n++,o++)for(3==o&&(o=0),r=0,i=0;i<e;i++,r++)3==r&&(r=0),(i&n&1)+(r&&r==o)&1||I(i,n)||(v[i+n*e]^=1);break;case 7:for(o=0,n=0;n<e;n++,o++)for(3==o&&(o=0),r=0,i=0;i<e;i++,r++)3==r&&(r=0),(r&&r==o)+(i+n&1)&1||I(i,n)||(v[i+n*e]^=1)}}function P(t){var e,i=0;for(e=0;e<=t;e++)g[e]>=5&&(i+=3+g[e]-5);for(e=3;e<t-1;e+=2)g[e-2]==g[e+2]&&g[e+2]==g[e-1]&&g[e-1]==g[e+1]&&3*g[e-1]==g[e]&&(0==g[e-3]||e+3>t||3*g[e-3]>=4*g[e]||3*g[e+3]>=4*g[e])&&(i+=40);return i}function M(){var t,i,n,r,o,a=0,s=0;for(i=0;i<e-1;i++)for(t=0;t<e-1;t++)(v[t+e*i]&&v[t+1+e*i]&&v[t+e*(i+1)]&&v[t+1+e*(i+1)]||!(v[t+e*i]||v[t+1+e*i]||v[t+e*(i+1)]||v[t+1+e*(i+1)]))&&(a+=3);for(i=0;i<e;i++){for(g[0]=0,n=r=t=0;t<e;t++)(o=v[t+e*i])==r?g[n]++:g[++n]=1,s+=(r=o)?1:-1;a+=P(n)}s<0&&(s=-s);var d=s,c=0;for(d+=d<<2,d<<=1;d>e*e;)d-=e*e,c++;for(a+=10*c,t=0;t<e;t++){for(g[0]=0,n=r=i=0;i<e;i++)(o=v[t+e*i])==r?g[n]++:g[++n]=1,r=o;a+=P(n)}return a}var k=null;return{api:{get ecclevel(){return b},set ecclevel(t){b=t},get size(){return _size},set size(t){_size=t},get canvas(){return k},set canvas(t){k=t},getFrame:function(g){return function(g){var P,k,B,j,R,O,T,C;j=g.length,t=0;do{if(t++,B=4*(b-1)+16*(t-1),i=c[B++],n=c[B++],r=c[B++],o=c[B],j<=(B=r*(i+n)+n-3+(t<=9)))break}while(t<40);for(e=17+4*t,R=r+(r+o)*(i+n)+n,j=0;j<R;j++)h[j]=0;for(f=g.slice(0),j=0;j<e*e;j++)v[j]=0;for(j=0;j<(e*(e+1)+1)/2;j++)p[j]=0;for(j=0;j<3;j++){for(B=0,k=0,1==j&&(B=e-7),2==j&&(k=e-7),v[k+3+e*(B+3)]=1,P=0;P<6;P++)v[k+P+e*B]=1,v[k+e*(B+P+1)]=1,v[k+6+e*(B+P)]=1,v[k+P+1+e*(B+6)]=1;for(P=1;P<5;P++)m(k+P,B+1),m(k+1,B+P+1),m(k+5,B+P),m(k+P+1,B+5);for(P=2;P<4;P++)v[k+P+e*(B+2)]=1,v[k+2+e*(B+P+1)]=1,v[k+4+e*(B+P)]=1,v[k+P+1+e*(B+4)]=1}if(t>1)for(j=a[t],k=e-7;;){for(P=e-7;P>j-3&&(w(P,k),!(P<j));)P-=j;if(k<=j+9)break;w(6,k-=j),w(k,6)}for(v[8+e*(e-8)]=1,k=0;k<7;k++)m(7,k),m(e-8,k),m(7,k+e-7);for(P=0;P<8;P++)m(P,7),m(P+e-8,7),m(P,e-8);for(P=0;P<9;P++)m(P,8);for(P=0;P<8;P++)m(P+e-8,8),m(8,P);for(k=0;k<7;k++)m(8,k+e-7);for(P=0;P<e-14;P++)1&P?(m(8+P,6),m(6,8+P)):(v[8+P+6*e]=1,v[6+e*(8+P)]=1);if(t>6)for(j=s[t-7],B=17,P=0;P<6;P++)for(k=0;k<3;k++,B--)1&(B>11?t>>B-12:j>>B)?(v[5-P+e*(2-k+e-11)]=1,v[2-k+e-11+e*(5-P)]=1):(m(5-P,2-k+e-11),m(2-k+e-11,5-P));for(k=0;k<e;k++)for(P=0;P<=k;P++)v[P+e*k]&&m(P,k);for(R=f.length,O=0;O<R;O++)h[O]=f.charCodeAt(O);if(f=h.slice(0),R>=(P=r*(i+n)+n)-2&&(R=P-2,t>9&&R--),O=R,t>9){for(f[O+2]=0,f[O+3]=0;O--;)j=f[O],f[O+3]|=255&j<<4,f[O+2]=j>>4;f[2]|=255&R<<4,f[1]=R>>4,f[0]=64|R>>12}else{for(f[O+1]=0,f[O+2]=0;O--;)j=f[O],f[O+2]|=255&j<<4,f[O+1]=j>>4;f[1]|=255&R<<4,f[0]=64|R>>4}for(O=R+3-(t<10);O<P;)f[O++]=236,f[O++]=17;for(y[0]=1,O=0;O<o;O++){for(y[O+1]=1,T=O;T>0;T--)y[T]=y[T]?y[T-1]^l[x(u[y[T]]+O)]:y[T-1];y[0]=l[x(u[y[0]]+O)]}for(O=0;O<=o;O++)y[O]=u[y[O]];for(B=P,k=0,O=0;O<i;O++)S(k,r,B,o),k+=r,B+=o;for(O=0;O<n;O++)S(k,r+1,B,o),k+=r+1,B+=o;for(k=0,O=0;O<r;O++){for(T=0;T<i;T++)h[k++]=f[O+T*r];for(T=0;T<n;T++)h[k++]=f[i*r+O+T*(r+1)]}for(T=0;T<n;T++)h[k++]=f[i*r+O+T*(r+1)];for(O=0;O<o;O++)for(T=0;T<i+n;T++)h[k++]=f[P+O+T*o];for(f=h,P=k=e-1,B=R=1,C=(r+o)*(i+n)+n,O=0;O<C;O++)for(j=f[O],T=0;T<8;T++,j<<=1){128&j&&(v[P+e*k]=1);do{R?P--:(P++,B?0!=k?k--:(B=!B,6==(P-=2)&&(P--,k=9)):k!=e-1?k++:(B=!B,6==(P-=2)&&(P--,k-=8))),R=!R}while(I(P,k))}for(f=v.slice(0),j=0,k=3e4,B=0;B<8&&(z(B),(P=M())<k&&(k=P,j=B),7!=j);B++)v=f.slice(0);for(j!=B&&z(j),k=d[j+(b-1<<3)],B=0;B<8;B++,k>>=1)1&k&&(v[e-1-B+8*e]=1,B<6?v[8+e*B]=1:v[8+e*(B+1)]=1);for(B=0;B<7;B++,k>>=1)1&k&&(v[8+e*(e-7+B)]=1,B?v[6-B+8*e]=1:v[7+8*e]=1);return v}(g)},utf16to8:function(t){var e,i,n,r;for(e="",n=t.length,i=0;i<n;i++)(r=t.charCodeAt(i))>=1&&r<=127?e+=t.charAt(i):r>2047?(e+=String.fromCharCode(224|r>>12&15),e+=String.fromCharCode(128|r>>6&63),e+=String.fromCharCode(128|r>>0&63)):(e+=String.fromCharCode(192|r>>6&31),e+=String.fromCharCode(128|r>>0&63));return e},draw:function(t,i,n,r,o){i.drawView(n,r);var a=i.ctx,s=n.contentSize,d=s.width,c=s.height;r.borderRadius,r.backgroundColor;var u=r.color,l=void 0===u?"#000000":u;r.border;var f=n.contentSize.left-n.borderSize.left,h=n.contentSize.top-n.borderSize.top;if(b=o||b,a){a.save(),i.setOpacity(r);var v=i.setTransform(n,r),p=v.x,g=v.y;p+=f,g+=h;var m=Math.min(d,c);t=this.utf16to8(t);var w=this.getFrame(t),x=m/e;a.setFillStyle(l);for(var y=0;y<e;y++)for(var S=0;S<e;S++)w[S*e+y]&&a.fillRect(p+x*y,g+x*S,x,x);a.restore(),i.setBorder(n,r)}else console.warn("No canvas provided to draw QR code in!")}}}}(),x=function(){function t(t,e){var n,r,o=this,a=t.id,d=t.context,c=t.canvas,u=t.pixelRatio,l=t.width,f=t.height;this.count=0,this.isDraw=!0,this.id=a,this.canvas=c,this.pixelRatio=1*u.toFixed(2),this.width=l,this.height=f,this.platform=s,this.isRate=!1,this.component=e,this.ctx=(r=this,(n=d).setFonts=function(t){var e=t.fontFamily,i=void 0===e?"sans-serif":e,o=t.fontSize,a=void 0===o?14:o,s=t.fontWeight,d=void 0===s?"normal":s,c=t.textStyle,u=void 0===c?"normal":c;"mp-toutao"==r.platform&&(d="bold"==d?"bold":"",u="italic"==u?"italic":""),n.font=u+" "+d+" "+a+"px "+i},n.draw?n:Object.assign(n,{setStrokeStyle:function(t){n.strokeStyle=t},setLineWidth:function(t){n.lineWidth=t},setLineCap:function(t){n.lineCap=t},setFillStyle:function(t){n.fillStyle=t},setFontSize:function(t){n.font=String(t)+"px sans-serif"},setGlobalAlpha:function(t){n.globalAlpha=t},setLineJoin:function(t){n.lineJoin=t},setTextAlign:function(t){n.textAlign=t},setMiterLimit:function(t){n.miterLimit=t},setShadow:function(t,e,i,r){n.shadowOffsetX=t,n.shadowOffsetY=e,n.shadowBlur=i,n.shadowColor=r},setTextBaseline:function(t){n.textBaseline=t},createCircularGradient:function(){},draw:function(){}})),this.sleep=1e3/30,this.progress=0,this.root={width:l,height:f,fontSizeRate:1};var h=i({},this.size);Object.defineProperty(this,"size",{configurable:!0,set:function(t){Object.keys(t).forEach((function(e){h[e]=t[e],o.root[e]=t[e]}))},get:function(){return h}}),this.init()}return t.prototype.init=function(){this.canvas.height&&(this.canvas.height=this.root.height*this.pixelRatio,this.canvas.width=this.root.width*this.pixelRatio,this.ctx.scale(this.pixelRatio,this.pixelRatio))},t.prototype.clear=function(){this.ctx.clearRect(0,0,this.root.width,this.root.height)},t.prototype.roundRect=function(t,e,i,n,r,o,a){if(void 0===o&&(o=!1),void 0===a&&(a=!1),!(r<0)){var s=this.ctx;if(s.beginPath(),r){var d=r||{},c=d.borderTopLeftRadius,u=void 0===c?r||0:c,l=d.borderTopRightRadius,f=void 0===l?r||0:l,h=d.borderBottomRightRadius,v=void 0===h?r||0:h,p=d.borderBottomLeftRadius,g=void 0===p?r||0:p;s.arc(t+i-v,e+n-v,v,0,.5*Math.PI),s.lineTo(t+g,e+n),s.arc(t+g,e+n-g,g,.5*Math.PI,Math.PI),s.lineTo(t,e+u),s.arc(t+u,e+u,u,Math.PI,1.5*Math.PI),s.lineTo(t+i-f,e),s.arc(t+i-f,e+f,f,1.5*Math.PI,2*Math.PI),s.lineTo(t+i,e+n-v)}else s.rect(t,e,i,n);s.closePath(),a&&s.stroke(),o&&s.fill()}},t.prototype.setTransform=function(t,e){var i=e.transform,n=e.transformOrigin,r=void 0===n?"center center":n,o=e.position,a=this.ctx,s=i||{},d=s.scaleX,c=void 0===d?1:d,f=s.scaleY,h=void 0===f?1:f,v=s.translateX,p=void 0===v?0:v,g=s.translateY,b=void 0===g?0:g,m=s.rotate,w=void 0===m?0:m,x=s.skewX,y=void 0===x?0:x,S=s.skewY,I=void 0===S?0:S,z=t.left,P=t.top,M=t.width,k=t.height;if(!["absolute","fixed"].includes(o))return{x:z,y:P,w:M,h:k};p=l(p,M)||0,b=l(b,k)||0;var B={top:l("0%",1),center:l("50%",1,!0),bottom:l("100%",1)},j={left:l("0%",1),center:l("50%",1,!0),right:l("100%",1)};r=r.split(" ").filter((function(t,e){return e<2})).reduce((function(t,e){if(/\d+/.test(e)){var i=l(e,1,!0)/(/px|rpx$/.test(e)?u(t.x)?k:M:1);return u(t.x)?Object.assign(t,{y:i}):Object.assign(t,{x:i})}return u(j[e])&&!u(t.x)?Object.assign(t,{x:j[e]}):Object.assign(t,{y:B[e]||.5})}),{}),a.scale(c,h);var R={x:M*(c>0?1:-1)*r.x+(z+p)/c,y:k*(h>0?1:-1)*r.y+(P+b)/h};return a.translate(R.x,R.y),w&&a.rotate(w*Math.PI/180),(y||I)&&a.transform(1,Math.tan(I*Math.PI/180),Math.tan(y*Math.PI/180),1,0,0),{x:-M*r.x,y:-k*r.y,w:M,h:k}},t.prototype.setBackground=function(t,e,i,n,r){var o=this.ctx;t&&"transparent"!=t?function(t){return!(!t||!t.startsWith("linear")&&!t.startsWith("radial"))}(t)?function(t,e,i,n,r,o){t.startsWith("linear")?function(t,e,i,n,r,o){for(var a=function(t,e,i,n,r){void 0===n&&(n=0),void 0===r&&(r=0);var o=t.match(/([-]?\d{1,3})deg/),a=o&&o[1]?parseFloat(o[1]):0;if(a>=360&&(a-=360),a<0&&(a+=360),0===(a=Math.round(a)))return{x0:Math.round(e/2)+n,y0:i+r,x1:Math.round(e/2)+n,y1:r};if(180===a)return{x0:Math.round(e/2)+n,y0:r,x1:Math.round(e/2)+n,y1:i+r};if(90===a)return{x0:n,y0:Math.round(i/2)+r,x1:e+n,y1:Math.round(i/2)+r};if(270===a)return{x0:e+n,y0:Math.round(i/2)+r,x1:n,y1:Math.round(i/2)+r};var s=Math.round(180*Math.asin(e/Math.sqrt(Math.pow(e,2)+Math.pow(i,2)))/Math.PI);if(a===s)return{x0:n,y0:i+r,x1:e+n,y1:r};if(a===180-s)return{x0:n,y0:r,x1:e+n,y1:i+r};if(a===180+s)return{x0:e+n,y0:r,x1:n,y1:i+r};if(a===360-s)return{x0:e+n,y0:i+r,x1:n,y1:r};var d,c=0,u=0,l=0,f=0;if(a<s||a>180-s&&a<180||a>180&&a<180+s||a>360-s){var h=a*Math.PI/180,v=a<s||a>360-s?i/2:-i/2,p=Math.tan(h)*v,g=a<s||a>180-s&&a<180?e/2-p:-e/2-p;c=-(l=p+(d=Math.pow(Math.sin(h),2)*g)),u=-(f=v+d/Math.tan(h))}(a>s&&a<90||a>90&&a<90+s||a>180+s&&a<270||a>270&&a<360-s)&&(h=(90-a)*Math.PI/180,p=a>s&&a<90||a>90&&a<90+s?e/2:-e/2,v=Math.tan(h)*p,g=a>s&&a<90||a>270&&a<360-s?i/2-v:-i/2-v,c=-(l=p+(d=Math.pow(Math.sin(h),2)*g)/Math.tan(h)),u=-(f=v+d));return c=Math.round(c+e/2)+n,u=Math.round(i/2-u)+r,l=Math.round(l+e/2)+n,f=Math.round(i/2-f)+r,{x0:c,y0:u,x1:l,y1:f}}(r,t,e,i,n),s=a.x0,d=a.y0,c=a.x1,u=a.y1,l=o.createLinearGradient(s,d,c,u),f=r.match(/linear-gradient\((.+)\)/)[1],v=h(f.substring(f.indexOf(",")+1)),p=0;p<v.colors.length;p++)l.addColorStop(v.percents[p],v.colors[p]);o.setFillStyle(l)}(e,i,n,r,t,o):t.startsWith("radial")&&function(t,e,i,n,r,o){for(var a=h(r.match(/radial-gradient\((.+)\)/)[1]),s=Math.round(t/2)+i,d=Math.round(e/2)+n,c=o.createRadialGradient(s,d,0,s,d,Math.max(t,e)/2),u=0;u<a.colors.length;u++)c.addColorStop(a.percents[u],a.colors[u]);o.setFillStyle(c)}(e,i,n,r,t,o)}(t,e,i,n,r,o):o.setFillStyle(t):["mp-toutiao","mp-baidu"].includes(this.platform)?o.setFillStyle("transparent"):o.setFillStyle("rgba(0,0,0,0)")},t.prototype.setShadow=function(t){var e=t.boxShadow,i=void 0===e?[]:e,n=this.ctx;if(i.length){var r=i[0],o=i[1],a=i[2],s=i[3];n.setShadow(r,o,a,s)}},t.prototype.setBorder=function(t,e){var i=this,n=this.ctx,r=t.width,o=t.height,a=e.border,s=e.borderBottom,d=e.borderTop,c=e.borderRight,u=e.borderLeft,l=e.borderRadius,f=a||{},h=f.borderWidth,v=void 0===h?0:h,p=f.borderStyle,g=f.borderColor,b=s||{},m=b.borderBottomWidth,w=void 0===m?v:m,x=b.borderBottomStyle,y=void 0===x?p:x,S=b.borderBottomColor,I=void 0===S?g:S,z=d||{},P=z.borderTopWidth,M=void 0===P?v:P,k=z.borderTopStyle,B=void 0===k?p:k,j=z.borderTopColor,R=void 0===j?g:j,O=c||{},T=O.borderRightWidth,C=void 0===T?v:T,W=O.borderRightStyle,H=void 0===W?p:W,N=O.borderRightColor,L=void 0===N?g:N,D=u||{},A=D.borderLeftWidth,_=void 0===A?v:A,F=D.borderLeftStyle,U=void 0===F?p:F,$=D.borderLeftColor,J=void 0===$?g:$,E=l||{},Z=E.borderTopLeftRadius,G=void 0===Z?l||0:Z,Q=E.borderTopRightRadius,Y=void 0===Q?l||0:Q,V=E.borderBottomRightRadius,q=void 0===V?l||0:V,X=E.borderBottomLeftRadius,K=void 0===X?l||0:X;if(s||u||d||c||a){var tt=function(t,e,r){"dashed"==e?/mp/.test(i.platform)?n.setLineDash([Math.ceil(4*t/3),Math.ceil(4*t/3)]):n.setLineDash([Math.ceil(6*t),Math.ceil(6*t)]):"dotted"==e&&n.setLineDash([t,t]),n.setStrokeStyle(r)},et=function(t,e,i,r,o,a,s,d,c,u,l,f,h,v){n.save(),n.setLineWidth(f),tt(f,h,v),n.beginPath(),n.arc(t,e,s,Math.PI*c,Math.PI*u),n.lineTo(i,r),n.arc(o,a,d,Math.PI*u,Math.PI*l),n.stroke(),n.restore()};n.save(),this.setOpacity(e);var it=this.setTransform(t,e),nt=it.x,rt=it.y;a&&(n.setLineWidth(v),tt(v,p,g),this.roundRect(nt,rt,r,o,l,!1,!!g),n.restore()),s&&et(nt+r-q,rt+o-q,nt+K,rt+o,nt+K,rt+o-K,q,K,.25,.5,.75,w,y,I),u&&et(nt+K,rt+o-K,nt,rt+G,nt+G,rt+G,K,G,.75,1,1.25,_,U,J),d&&et(nt+G,rt+G,nt+r-Y,rt,nt+r-Y,rt+Y,G,Y,1.25,1.5,1.75,M,B,R),c&&et(nt+r-Y,rt+Y,nt+r,rt+o-q,nt+r-q,rt+o-q,Y,q,1.75,2,.25,C,H,L)}},t.prototype.setOpacity=function(t){var e=t.opacity,i=void 0===e?1:e;this.ctx.setGlobalAlpha(i)},t.prototype.drawPattern=function(t,e,i){return n(this,void 0,void 0,(function(){var o=this;return r(this,(function(a){return[2,new Promise((function(a,s){var d=o,c=d.ctx,u=d.canvas,l=e.width,f=e.height,h=i||{},v=h.borderRadius,p=void 0===v?0:v,g=h.backgroundColor,b=void 0===g?"transparent":g,m=h.backgroundImage,w=h.backgroundRepeat,x=void 0===w?"repeat":w;c.save(),o.setOpacity(i);var y=o.setTransform(e,i),S=y.x,I=y.y;o.setShadow(i),o.setBackground(b,l,f,S,I),o.roundRect(S,I,l,f,p,!0,!1);var z=function(t){var n=c.createPattern(t.src,x);c.setFillStyle(n),o.roundRect(S,I,l,f,p,!0,!1),o.setBorder(e,i),c.restore(),a()};if(m)if(u.createImage||"web"==o.platform){var P=null;(P=u.createImage?u.createImage():new Image).onload=function(){t.src=P,z(t)},P.onerror=function(){return n(o,void 0,void 0,(function(){return r(this,(function(e){return console.log("createImage fail: "+JSON.stringify(t)),a(),[2]}))}))},P.src=t.path}else z(t)}))]}))}))},t.prototype.drawView=function(t,e,i,n,r){void 0===i&&(i=!0),void 0===n&&(n=!0),void 0===r&&(r=!0);var o=this.ctx,a=t.width,s=t.height,d=e||{},c=d.borderRadius,u=void 0===c?0:c,l=d.backgroundColor,f=void 0===l?"transparent":l;o.save(),this.setOpacity(e);var h=this.setTransform(t,e),v=h.x,p=h.y;r&&this.setShadow(e),i&&this.setBackground(f,a,s,v,p),this.roundRect(v,p,a,s,u,i,!1),o.restore(),n&&this.setBorder(t,e)},t.prototype.drawImage=function(t,e,i,o){return void 0===e&&(e={}),void 0===i&&(i={}),void 0===o&&(o=!0),n(this,void 0,void 0,(function(){var a=this;return r(this,(function(s){switch(s.label){case 0:return[4,new Promise((function(s,d){return n(a,void 0,void 0,(function(){var a,d,c,u,h,v,p,g,b,m,w,x,y,S,I,z,P,M,k,B,j,R,O,T,C,W,H,N=this;return r(this,(function(L){switch(L.label){case 0:return i.boxShadow&&this.drawView(e,Object.assign(i,{backgroundColor:i.backgroundColor||i.boxShadow&&(i.backgroundColor||"#ffffff")}),!0,!1,!0),d=(a=this).ctx,c=a.sleep,u=a.canvas,h=i.borderRadius,v=void 0===h?0:h,p=i.backgroundColor,g=void 0===p?"transparent":p,b=i.objectFit,m=void 0===b?"fill":b,w=i.objectPosition,x=e.width,y=e.height,S=e.left,I=e.top,d.save(),z=e.contentSize.left-e.borderSize.left,P=e.contentSize.top-e.borderSize.top,o||(this.setOpacity(i),M=this.setTransform(e,i),k=M.x,B=M.y,this.setBackground(g,x,y,S,I),S=k,I=B,this.roundRect(S,I,x,y,v,!!v,!1)),S+=z,I+=P,d.clip(),j=function(t){if("fill"!==m){var i=function(t,e,i){var n=t.objectFit,r=t.objectPosition,o=e.width/e.height,a=i.width/i.height,s=1;"contain"==n&&o>=a||"cover"==n&&o<a?s=e.height/i.height:("contain"==n&&o<a||"cover"==n&&o>=a)&&(s=e.width/i.width);var d=i.width*s,c=i.height*s,u=/^\d+px|rpx$/.test(null==r?void 0:r[0])?l(null==r?void 0:r[0],e.width):(e.width-d)*(/%$/.test(null==r?void 0:r[0])?l(null==r?void 0:r[0],1,!0):{left:0,center:.5,right:1}[(null==r?void 0:r[0])||"center"]),f=/^\d+px|rpx$/.test(null==r?void 0:r[1])?l(null==r?void 0:r[1],e.height):(e.height-c)*(/%$/.test(null==r?void 0:r[1])?l(null==r?void 0:r[1],1,!0):{top:0,center:.5,bottom:1}[(null==r?void 0:r[1])||"center"]),h=function(t,e){return[(t-u)/s,(e-f)/s]},v=h(0,0),p=v[0],g=v[1],b=h(e.width,e.height),m=b[0],w=b[1];return{sx:Math.max(p,0),sy:Math.max(g,0),sw:Math.min(m-p,i.width),sh:Math.min(w-g,i.height),dx:Math.max(u,0),dy:Math.max(f,0),dw:Math.min(d,e.width),dh:Math.min(c,e.height)}}({objectFit:m,objectPosition:w},e.contentSize,t),n=i.sx,r=i.sy,o=i.sh,a=i.sw,s=i.dx,c=i.dy,u=i.dh,f=i.dw;"mp-baidu"==N.platform?d.drawImage(t.src,s+S,c+I,f,u,n,r,a,o):d.drawImage(t.src,n,r,a,o,s+S,c+I,f,u)}else d.drawImage(t.src,S,I,x,y)},R=function(){d.restore(),N.drawView(e,i,!1,!0,!1),setTimeout(s,c)},O=function(t){if(u.createImage||"web"==N.platform){var e=null;(e=u.createImage?u.createImage():new Image).onload=function(){t.src=e,j(t),R()},e.onerror=function(){return n(N,void 0,void 0,(function(){return r(this,(function(e){return console.log("createImage fail: "+JSON.stringify(t)),s(!0),[2]}))}))},e.src=t.path}else j(t),R()},"string"!=typeof t?[3,2]:[4,f(t)];case 1:return T=L.sent(),C=T.path,W=T.width,H=T.height,O({path:C,src:C,width:W,height:H}),[3,3];case 2:O(t),L.label=3;case 3:return[2]}}))}))}))];case 1:return s.sent(),[2]}}))}))},t.prototype.drawText=function(t,e,i,n){this.drawView(e,i);var r=this.ctx,o=e.borderSize,a=e.contentSize,s=a.width,d=a.height,c=a.left-o.left,u=a.top-o.top,f=i.color,h=void 0===f?"#000000":f,v=i.lineHeight,p=void 0===v?"1.4em":v,g=i.fontSize,b=void 0===g?14:g,m=i.fontWeight,w=i.fontFamily,x=i.textStyle,y=i.textAlign,S=void 0===y?"left":y,I=i.verticalAlign,z=void 0===I?"middle":I;i.backgroundColor;var P=i.lineClamp,M=i.textDecoration;if(p=l(p,b),t){r.save(),this.setOpacity(i);var k=this.setTransform(e,i),B=k.x,j=k.y;switch(B+=c,j+=u,r.setFonts({fontFamily:w,fontSize:b,fontWeight:m,textStyle:x}),r.setTextBaseline("middle"),r.setTextAlign(S),r.setFillStyle(h),j+=b/2,S){case"left":break;case"center":B+=.5*s;break;case"right":B+=s}var R=n.lines*p,O=Math.ceil((d-R)/2);switch(O<0&&(O=0),z){case"top":break;case"middle":j+=O;break;case"bottom":j+=2*O}var T=(p-n.fontHeight)/2,C=function(t,e,i){var o=t;switch(S){case"left":t=t,o+=i;break;case"center":o=(t-=i/2)+i;break;case"right":o=t,t-=i}M&&(r.setLineWidth(b/13),r.beginPath(),e-=T,/\bunderline\b/.test(M)&&(r.moveTo(t,e-.5*n.fontHeight),r.lineTo(o,e-.5*n.fontHeight)),/\boverline\b/.test(M)&&(r.moveTo(t,e-1.5*n.fontHeight),r.lineTo(o,e-1.5*n.fontHeight)),/\bline-through\b/.test(M)&&(r.moveTo(t,e-n.fontHeight),r.lineTo(o,e-n.fontHeight)),r.closePath(),r.setStrokeStyle(h),r.stroke())};if(1==n.widths.lenght&&n.widths[0].total<=a.width)return r.fillText(t,B,j+T),C(B,j+=p,n.widths[0].total),r.restore(),void this.setBorder(e,i);for(var W=t.split(""),H=j,N=B,L="",D=0,A=0;A<=W.length;A++){var _=W[A]||"",F="\n"===_,U=""==_,$=L+(_=F?"":_),J=r.measureText($).width;if(D>=P)break;if(N=B,(J=J)>a.width||F||U){if(D++,L=U&&J<=a.width?$:L,D===P&&J>s){for(;r.measureText(L+"...").width>a.width&&!(L.length<=1);)L=L.substring(0,L.length-1);L+="..."}if(r.fillText(L,N,j+T),C(N,j+=p,J),L=_,j+p>H+d)break}else L=$}r.restore()}},t.prototype.source=function(t){var e;return n(this,void 0,void 0,(function(){var i,n;return r(this,(function(r){switch(r.label){case 0:if("{}"==JSON.stringify(t))return[2];if(!t.type)for(i in t.type="view",t.css=t.css||{},t)["views","children","type","css"].includes(i)||(t.css[i]=t[i],delete t[i]);return(null===(e=null==t?void 0:t.css)||void 0===e?void 0:e.width)||(t.css?t.css.width=this.root.width:t.css={width:this.root.width}),[4,this.create(t)];case 1:return n=r.sent(),this.size=n.layout(),this.node=n,[2,this.size]}}))}))},t.prototype.create=function(t,e){var i,o,a,s;return n(this,void 0,void 0,(function(){var n,d,c,u,l,h,v,p,g,b,w,x;return r(this,(function(r){switch(r.label){case 0:if("image"==t.type&&!t.src&&!t.url||"qrcode"==t.type&&!t.text)return[2];if("none"==(null===(i=null==t?void 0:t.css)||void 0===i?void 0:i.display))return console.error("element display none"),[2];r.label=1;case 1:return r.trys.push([1,4,,5]),"image"==t.type||"view"==t.type&&(null===(o=t.css)||void 0===o?void 0:o.backgroundImage)?(n=null,d=/url\((.+)\)/,t.css.backgroundImage&&(null===(a=d.exec(t.css.backgroundImage))||void 0===a?void 0:a[1])&&(n=null===(s=d.exec(t.css.backgroundImage))||void 0===s?void 0:s[1]),[4,f(t.src||n)]):[3,3];case 2:c=r.sent(),u=c.width,l=c.height,h=c.path,["mp-weixin","mp-baidu","mp-qq","mp-toutiao"].includes(this.platform)&&(h=/^\.|^\/(?=[^\/])/.test(t.src||n)?"/"+h:h),t.attributes=Object.assign(t.attributes||{},{width:u,height:l,path:h,src:h,naturalSrc:t.src||n}),r.label=3;case 3:return[3,5];case 4:return v=r.sent(),console.log(v),[2];case 5:if(this.count+=1,p=new m(t,e,this.root,this.ctx),!(g=t.views||t.children))return[3,9];b=0,r.label=6;case 6:return b<g.length?(w=g[b],[4,this.create(w,p)]):[3,9];case 7:(x=r.sent())&&p.add(x),r.label=8;case 8:return b++,[3,6];case 9:return[2,p]}}))}))},t.prototype.drawNode=function(t){return n(this,void 0,void 0,(function(){var e,i,n,o,a,s,d,c,u,l,f,h;return r(this,(function(r){switch(r.label){case 0:return e=t.layoutBox,i=t.computedStyle,n=t.attributes,o=t.name,a=t.children,s=t.attributes,d=s.src,c=s.text,"view"!==o?[3,4]:d?[4,this.drawPattern(n,e,i)]:[3,2];case 1:return r.sent(),[3,3];case 2:this.drawView(e,i),r.label=3;case 3:return[3,7];case 4:return"image"===o&&d?[4,this.drawImage(n,e,i,!1)]:[3,6];case 5:return r.sent(),[3,7];case 6:"text"===o?this.drawText(c,e,i,n):"qrcode"===o&&(null==w?void 0:w.api)&&w.api.draw(c,this,e,i),r.label=7;case 7:if(this.progress+=1,!a)return[2];u=Object.values?Object.values(a):Object.keys(a).map((function(t){return a[t]})),l=0,f=u,r.label=8;case 8:return l<f.length?(h=f[l],[4,this.drawNode(h)]):[3,11];case 9:r.sent(),r.label=10;case 10:return l++,[3,8];case 11:return[2]}}))}))},t.prototype.render=function(){return n(this,void 0,void 0,(function(){return r(this,(function(t){switch(t.label){case 0:return this.init(),[4,(e=30,void 0===e&&(e=0),new Promise((function(t){return setTimeout(t,e)})))];case 1:return t.sent(),[4,this.drawNode(this.node)];case 2:return t.sent(),[2]}var e}))}))},t.prototype.listen=function(t,e){var i=this;if("progressChange"==t){var n=0;Object.defineProperty(this,"progress",{configurable:!0,set:function(t){n=t,e(t/i.count)},get:function(){return n||0}})}},t.prototype.save=function(t){try{var e=t||{},i=e.fileType,n=void 0===i?"png":i,r=e.quality,o=void 0===r?1:r;return this.canvas.toDataURL("image/"+n,o)}catch(t){return console.log("image cross domain"),t}},t}();"web"==s&&(window.Painter=x),t.Painter=x,Object.defineProperty(t,"__esModule",{value:!0})}))},"64ea":function(t,e,i){"use strict";i.r(e);var n=i("d9bd"),r=i.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);e["default"]=r.a},"6c57":function(t,e,i){"use strict";var n=i("23e7"),r=i("da84");n({global:!0,forced:r.globalThis!==r},{globalThis:r})},"7a54":function(t,e,i){var n=i("aa36");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var r=i("4f06").default;r("4f941e16",n,!0,{sourceMap:!1,shadowMode:!1})},"7b9c":function(t,e,i){var n=i("24fb");e=n(!1),e.push([t.i,".lime-painter[data-v-64f1d435], .lime-painter__canvas[data-v-64f1d435]{\nwidth:100%;\n}",""]),t.exports=e},"7f84":function(t,e,i){"use strict";i("7a82");var n=i("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.base64ToPath=f,e.compareVersion=function(t,e){t=t.split("."),e=e.split(".");var i=Math.max(t.length,e.length);while(t.length<i)t.push("0");while(e.length<i)e.push("0");for(var n=0;n<i;n++){var r=parseInt(t[n],10),o=parseInt(e[n],10);if(r>o)return 1;if(r<o)return-1}return 0},e.getImageInfo=function(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return new Promise(function(){var i=(0,o.default)((0,r.default)().mark((function i(n,o){var a;return(0,r.default)().wrap((function(i){while(1)switch(i.prev=i.next){case 0:if(/^\.|^\/(?=[^\/])/,!s.test(t)||!e){i.next=5;break}return i.next=4,h(t);case 4:t=i.sent;case 5:if(!d(t)){i.next=15;break}if(!c&&u[t]){i.next=14;break}return a=t,i.next=10,f(t);case 10:t=i.sent,u[a]=t,i.next=15;break;case 14:t=u[t];case 15:u[t]&&u[t].errMsg?n(u[t]):uni.getImageInfo({src:t,success:function(e){e.path=e.path.replace(/^\./,window.location.origin),e.naturalSrc=t,c?n(e):(u[t]=e,n(u[t]))},fail:function(e){n({path:t}),console.error("getImageInfo:fail ".concat(t," failed ").concat(JSON.stringify(e)))}});case 16:case"end":return i.stop()}}),i)})));return function(t,e){return i.apply(this,arguments)}}())},e.isBase64=void 0,e.isNumber=l,e.pathToBase64=h,e.sleep=function(t){return new Promise((function(e){return setTimeout(e,t)}))},e.toPx=function t(e,i){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if("number"===typeof e)return e;if(l(e))return 1*e;if("string"===typeof e){var r=/^-?([0-9]+)?([.]{1}[0-9]+){0,1}(em|rpx|px|%)$/g,o=r.exec(e);if(!e||!o)return 0;var a=o[3];e=parseFloat(e);var s=0;return"rpx"===a?s=uni.upx2px(e):"px"===a?s=1*e:"%"===a?s=e*t(i)/100:"em"===a&&(s=e*t(i||14)),n?1*s.toFixed(2):Math.round(s)}return 0};var r=n(i("f07e")),o=n(i("c964")),a=n(i("d0af"));i("ac1f"),i("00b4"),i("d3b7"),i("caad6"),i("acd8"),i("14d9"),i("e25e"),i("c19f"),i("ace4"),i("c975"),i("5cc6"),i("907a"),i("9a8c"),i("a975"),i("735e"),i("c1ac"),i("d139"),i("3a7b"),i("986a"),i("1d02"),i("d5d6"),i("82f8"),i("e91f"),i("60bd"),i("5f96"),i("3280"),i("3fcc"),i("ca91"),i("25a1"),i("cd26"),i("3c5d"),i("2954"),i("649e"),i("219c"),i("b39a"),i("72f7"),i("5319"),i("d401"),i("81b2"),i("0eb6"),i("b7ef"),i("8bd4"),i("3ca3"),i("ddb0"),i("2b3d"),i("9861"),i("e9c4"),i("d9e2"),i("99af");var s=/^(http|\/\/)/,d=function(t){return/^data:image\/(\w+);base64/.test(t)};e.isBase64=d;var c=["devtools"].includes(uni.getSystemInfoSync().platform),u={};function l(t){return/^-?\d+(\.\d+)?$/.test(t)}function f(t){var e=/data:image\/(\w+);base64,(.*)/.exec(t)||[],i=(0,a.default)(e,3);i[1],i[2];return new Promise((function(e,i){for(var n=t.split(",")[0].split(":")[1].split(";")[0],r=atob(t.split(",")[1]),o=new ArrayBuffer(r.length),a=new Uint8Array(o),s=0;s<r.length;s++)a[s]=r.charCodeAt(s);e(URL.createObjectURL(new Blob([a],{type:n})))}))}function h(t){return/^data:/.test(t)?t:new Promise((function(e,i){var n=function(){var n=new Image;n.setAttribute("crossOrigin","Anonymous"),n.onload=function(){var t=document.createElement("canvas");t.width=this.naturalWidth,t.height=this.naturalHeight,t.getContext("2d").drawImage(n,0,0);var i=t.toDataURL("image/png");e(i),t.height=t.width=0},n.src=t,n.onerror=function(e){console.error("urlToBase64 error: ".concat(t),JSON.stringify(e)),i(new Error("urlToBase64 error"))}},r=function(t){var n=new FileReader;n.onload=function(t){e(t.target.result)},n.readAsDataURL(t),n.onerror=function(t){console.error("blobToBase64 error:",JSON.stringify(t)),i(new Error("blobToBase64 error"))}},o="function"===typeof FileReader;if(s.test(t)&&o){window.URL=window.URL||window.webkitURL;var a=new XMLHttpRequest;a.open("get",t,!0),a.timeout=2e3,a.responseType="blob",a.onload=function(){200==this.status?r(this.response):n()},a.onreadystatechange=function(){0===this.status&&console.error("图片跨域了，得后端处理咯")},a.send()}else/^blob/.test(t)&&o?r(t):n()}))}},"80a3":function(t,e,i){"use strict";i.r(e);var n=i("356b"),r=i.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);e["default"]=r.a},"822c":function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n={data:function(){return{}},props:{type:{type:String,default:"fixed"},backgroundColor:{type:String,default:"#fff"}},methods:{}};e.default=n},8810:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return r})),i.d(e,"a",(function(){}));var n=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",[this._t("default")],2)},r=[]},"8fc0":function(t,e,i){"use strict";i.r(e);var n=i("2ab4"),r=i("80a3");for(var o in r)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return r[t]}))}(o);i("1c75");var a=i("f0c5"),s=Object(a["a"])(r["default"],n["b"],n["c"],!1,null,"bf7076f2",null,!1,n["a"],void 0);e["default"]=s.exports},"8fed":function(t,e,i){"use strict";var n=i("e2db"),r=i.n(n);r.a},9530:function(t,e,i){"use strict";var n=i("d720"),r=i.n(n);r.a},9873:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return r})),i.d(e,"a",(function(){}));var n=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view")},r=[]},"98b4":function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=i("2399"),r={name:"lime-painter-image",mixins:[(0,n.children)("painter")],props:{css:[String,Object],src:String},data:function(){return{type:"image",el:{css:{},src:null}}}};e.default=r},a1ed:function(t,e,i){"use strict";i.r(e);var n=i("4aa8"),r=i.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);e["default"]=r.a},a51c:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return r})),i.d(e,"a",(function(){}));var n=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-text",{staticStyle:{opacity:"0"}},[this._t("default")],2)},r=[]},a712:function(t,e,i){"use strict";i.d(e,"b",(function(){return r})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){return n}));var n={uLoading:i("8fc0").default},r=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{class:"loading "+("flex"==this.type?"flex":""),style:{backgroundColor:this.backgroundColor}},[e("u-loading",{attrs:{mode:"flower",size:60}})],1)},o=[]},aa36:function(t,e,i){var n=i("24fb");e=n(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.u-loading-circle[data-v-bf7076f2]{display:inline-flex;vertical-align:middle;width:%?28?%;height:%?28?%;background:0 0;border-radius:50%;border:2px solid;border-color:#e5e5e5 #e5e5e5 #e5e5e5 #8f8d8e;-webkit-animation:u-circle-data-v-bf7076f2 1s linear infinite;animation:u-circle-data-v-bf7076f2 1s linear infinite}.u-loading-flower[data-v-bf7076f2]{width:20px;height:20px;display:inline-block;vertical-align:middle;-webkit-animation:a 1s steps(12) infinite;animation:u-flower-data-v-bf7076f2 1s steps(12) infinite;background:transparent url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMjAiIGhlaWdodD0iMTIwIiB2aWV3Qm94PSIwIDAgMTAwIDEwMCI+PHBhdGggZmlsbD0ibm9uZSIgZD0iTTAgMGgxMDB2MTAwSDB6Ii8+PHJlY3Qgd2lkdGg9IjciIGhlaWdodD0iMjAiIHg9IjQ2LjUiIHk9IjQwIiBmaWxsPSIjRTlFOUU5IiByeD0iNSIgcnk9IjUiIHRyYW5zZm9ybT0idHJhbnNsYXRlKDAgLTMwKSIvPjxyZWN0IHdpZHRoPSI3IiBoZWlnaHQ9IjIwIiB4PSI0Ni41IiB5PSI0MCIgZmlsbD0iIzk4OTY5NyIgcng9IjUiIHJ5PSI1IiB0cmFuc2Zvcm09InJvdGF0ZSgzMCAxMDUuOTggNjUpIi8+PHJlY3Qgd2lkdGg9IjciIGhlaWdodD0iMjAiIHg9IjQ2LjUiIHk9IjQwIiBmaWxsPSIjOUI5OTlBIiByeD0iNSIgcnk9IjUiIHRyYW5zZm9ybT0icm90YXRlKDYwIDc1Ljk4IDY1KSIvPjxyZWN0IHdpZHRoPSI3IiBoZWlnaHQ9IjIwIiB4PSI0Ni41IiB5PSI0MCIgZmlsbD0iI0EzQTFBMiIgcng9IjUiIHJ5PSI1IiB0cmFuc2Zvcm09InJvdGF0ZSg5MCA2NSA2NSkiLz48cmVjdCB3aWR0aD0iNyIgaGVpZ2h0PSIyMCIgeD0iNDYuNSIgeT0iNDAiIGZpbGw9IiNBQkE5QUEiIHJ4PSI1IiByeT0iNSIgdHJhbnNmb3JtPSJyb3RhdGUoMTIwIDU4LjY2IDY1KSIvPjxyZWN0IHdpZHRoPSI3IiBoZWlnaHQ9IjIwIiB4PSI0Ni41IiB5PSI0MCIgZmlsbD0iI0IyQjJCMiIgcng9IjUiIHJ5PSI1IiB0cmFuc2Zvcm09InJvdGF0ZSgxNTAgNTQuMDIgNjUpIi8+PHJlY3Qgd2lkdGg9IjciIGhlaWdodD0iMjAiIHg9IjQ2LjUiIHk9IjQwIiBmaWxsPSIjQkFCOEI5IiByeD0iNSIgcnk9IjUiIHRyYW5zZm9ybT0icm90YXRlKDE4MCA1MCA2NSkiLz48cmVjdCB3aWR0aD0iNyIgaGVpZ2h0PSIyMCIgeD0iNDYuNSIgeT0iNDAiIGZpbGw9IiNDMkMwQzEiIHJ4PSI1IiByeT0iNSIgdHJhbnNmb3JtPSJyb3RhdGUoLTE1MCA0NS45OCA2NSkiLz48cmVjdCB3aWR0aD0iNyIgaGVpZ2h0PSIyMCIgeD0iNDYuNSIgeT0iNDAiIGZpbGw9IiNDQkNCQ0IiIHJ4PSI1IiByeT0iNSIgdHJhbnNmb3JtPSJyb3RhdGUoLTEyMCA0MS4zNCA2NSkiLz48cmVjdCB3aWR0aD0iNyIgaGVpZ2h0PSIyMCIgeD0iNDYuNSIgeT0iNDAiIGZpbGw9IiNEMkQyRDIiIHJ4PSI1IiByeT0iNSIgdHJhbnNmb3JtPSJyb3RhdGUoLTkwIDM1IDY1KSIvPjxyZWN0IHdpZHRoPSI3IiBoZWlnaHQ9IjIwIiB4PSI0Ni41IiB5PSI0MCIgZmlsbD0iI0RBREFEQSIgcng9IjUiIHJ5PSI1IiB0cmFuc2Zvcm09InJvdGF0ZSgtNjAgMjQuMDIgNjUpIi8+PHJlY3Qgd2lkdGg9IjciIGhlaWdodD0iMjAiIHg9IjQ2LjUiIHk9IjQwIiBmaWxsPSIjRTJFMkUyIiByeD0iNSIgcnk9IjUiIHRyYW5zZm9ybT0icm90YXRlKC0zMCAtNS45OCA2NSkiLz48L3N2Zz4=) no-repeat;background-size:100%}@-webkit-keyframes u-flower-data-v-bf7076f2{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}@keyframes u-flower-data-v-bf7076f2{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}@-webkit-keyframes u-circle-data-v-bf7076f2{0%{-webkit-transform:rotate(0);transform:rotate(0)}100%{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}',""]),t.exports=e},ac99:function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("a9e3");var n=i("2399"),r={name:"lime-painter-text",mixins:[(0,n.children)("painter")],props:{css:[String,Object],text:[String,Number],replace:Object},data:function(){return{type:"text",el:{css:{},text:null}}}};e.default=r},b83e:function(t,e,i){var n=i("24fb");e=n(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.loading[data-v-061dd044]{position:fixed;top:0;left:0;width:100vw;height:100vh;z-index:9999;display:flex;justify-content:center;align-items:center}.loading.flex[data-v-061dd044]{position:static;flex:1;width:100%}.loading .loading-img[data-v-061dd044]{width:%?100?%;height:%?100?%}',""]),t.exports=e},baa5:function(t,e,i){"use strict";var n=i("23e7"),r=i("e58c");n({target:"Array",proto:!0,forced:r!==[].lastIndexOf},{lastIndexOf:r})},be37:function(t,e,i){"use strict";i.r(e);var n=i("f48a"),r=i("64ea");for(var o in r)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return r[t]}))}(o);i("9530");var a=i("f0c5"),s=Object(a["a"])(r["default"],n["b"],n["c"],!1,null,"64f1d435",null,!1,n["a"],void 0);e["default"]=s.exports},c13e:function(t,e,i){"use strict";i.r(e);var n=i("822c"),r=i.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);e["default"]=r.a},c4b5:function(t,e,i){"use strict";i.r(e);var n=i("ac99"),r=i.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);e["default"]=r.a},cb29:function(t,e,i){"use strict";var n=i("23e7"),r=i("81d5"),o=i("44d2");n({target:"Array",proto:!0},{fill:r}),o("fill")},d720:function(t,e,i){var n=i("7b9c");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var r=i("4f06").default;r("5a6ac1e0",n,!0,{sourceMap:!1,shadowMode:!1})},d9bd:function(t,e,i){"use strict";i("7a82");var n=i("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var r=n(i("f07e")),o=n(i("c964"));i("a9e3"),i("99af"),i("e9c4"),i("ac1f"),i("d3b7"),i("d9e2"),i("d401");var a=i("7f84"),s=i("2399"),d=i("4f21"),c={name:"lime-painter",mixins:[(0,s.parent)("painter")],props:{board:Object,pathType:{type:String},fileType:{type:String,default:"png"},quality:{type:Number,default:1},css:[String,Object],width:[Number,String],height:[Number,String],pixelRatio:Number,customStyle:String,isCanvasToTempFilePath:Boolean,sleep:{type:Number,default:1e3/30},beforeDelay:{type:Number,default:100},afterDelay:{type:Number,default:100}},data:function(){return{use2dCanvas:!1,canvasHeight:150,canvasWidth:null,isDrawIng:!1,isPC:!1,inited:!1,name:"view",progress:0}},computed:{canvasId:function(){return"l-painter".concat(this._uid)},size:function(){if(this.boardWidth&&this.boardHeight)return"width:".concat(this.boardWidth,"px; height: ").concat(this.boardHeight,"px;")},dpr:function(){return this.pixelRatio||uni.getSystemInfoSync().pixelRatio},boardWidth:function(){var t,e=this.width,i=void 0===e?0:e,n=this.canvasWidth,r=void 0===n?0:n,o=(null===(t=this.board)||void 0===t?void 0:t.css)||this.board||{},s=o.width,d=void 0===s?0:s;return Math.max((0,a.toPx)(i||d),(0,a.toPx)(r))},boardHeight:function(){var t,e=this.height,i=void 0===e?0:e,n=this.canvasHeight,r=void 0===n?0:n,o=(null===(t=this.board)||void 0===t?void 0:t.css)||this.board||{},s=o.height,d=void 0===s?0:s;return Math.max((0,a.toPx)(i||d),(0,a.toPx)(r))}},watch:{canvasWidth:function(t){var e;!this.el.css||null!==(e=this.el.css)&&void 0!==e&&e.width||(this.el.css.width=t)},size:function(t){}},mounted:function(){var t=this;this.$nextTick((function(){setTimeout((function(){t.board?t.$watch("board",t.watchRender,{deep:!0,immediate:!0}):t.el.views.length&&t.$watch("el",t.watchRender,{deep:!0,immediate:!0})}),30)}))},methods:{watchRender:function(t){var e=this;return(0,o.default)((0,r.default)().mark((function i(){return(0,r.default)().wrap((function(i){while(1)switch(i.prev=i.next){case 0:if(e.progress=0,"{}"!==JSON.stringify(t)&&t){i.next=3;break}return i.abrupt("return");case 3:clearTimeout(e.rendertimer),e.rendertimer=setTimeout((function(){e.render(t)}),e.beforeDelay);case 5:case"end":return i.stop()}}),i)})))()},setFilePath:function(t,e){var i=this;return(0,o.default)((0,r.default)().mark((function n(){var o,s;return(0,r.default)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(o=t,s=i.pathType,"base64"!=s||(0,a.isBase64)(t)){n.next=8;break}return n.next=5,(0,a.pathToBase64)(t);case 5:o=n.sent,n.next=12;break;case 8:if("url"!=s||!(0,a.isBase64)(t)){n.next=12;break}return n.next=11,(0,a.base64ToPath)(t);case 11:o=n.sent;case 12:return e&&i.$emit("success",o),n.abrupt("return",o);case 14:case"end":return n.stop()}}),n)})))()},getParentWeith:function(){var t=this;uni.createSelectorQuery().in(this).select(".lime-painter").boundingClientRect().exec((function(e){t.canvasWidth=Math.ceil(e[0].width),t.canvasHeight=e[0].height}))},update:function(t,e){var i=this;return(0,o.default)((0,r.default)().mark((function n(){return(0,r.default)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return i.painter=null,i.isDrawIng=!1,n.next=4,new Promise((function(t){return i.$nextTick(t)}));case 4:return n.next=6,(0,a.sleep)(200);case 6:return n.abrupt("return",i.render(t,e));case 7:case"end":return n.stop()}}),n)})))()},render:function(){var t=arguments,e=this;return(0,o.default)((0,r.default)().mark((function i(){var n,s,c,u,l,f,h,v,p,g,b,m,w;return(0,r.default)().wrap((function(i){while(1)switch(i.prev=i.next){case 0:if(c=t.length>0&&void 0!==t[0]?t[0]:{},u=t.length>1&&void 0!==t[1]&&t[1],!e.isDrawIng){i.next=4;break}return i.abrupt("return",e.update(c,u));case 4:return e.isDrawIng=!0,"{}"!=JSON.stringify(c),i.next=8,e.getContext();case 8:if(l=i.sent,f=e.use2dCanvas,h=e.boardWidth,v=e.boardHeight,p=e.canvas,g=e.afterDelay,!f||p){i.next=12;break}return i.abrupt("return",Promise.reject(new Error("render: fail canvas has not been created")));case 12:return e.boundary={top:0,left:0,width:h,height:v},e.painter||(e.painter=new d.Painter({context:l,canvas:p,width:h,height:v,pixelRatio:e.dpr},e)),e.painter.listen("progressChange",(function(t){e.$emit("progress",t)})),i.next=17,e.painter.source(c);case 17:return b=i.sent,m=b.width,w=b.height,e.canvasHeight=(0,a.toPx)(null===(n=e.el.css)||void 0===n?void 0:n.height)||w,e.canvasWidth=(0,a.toPx)(null===(s=e.el.css)||void 0===s?void 0:s.width)||m,e.boundary.height=e.canvasHeight,e.boundary.width=e.canvasWidth,i.next=26,(0,a.sleep)(e.sleep);case 26:return i.next=28,e.painter.render();case 28:return i.next=30,new Promise((function(t){return e.$nextTick(t)}));case 30:if(f||u){i.next=33;break}return i.next=33,e.canvasDraw();case 33:if(!g||!f){i.next=36;break}return i.next=36,(0,a.sleep)(g);case 36:return e.$emit("done"),e.isCanvasToTempFilePath&&!u&&e.isDrawIng&&e.canvasToTempFilePath().then(function(){var t=(0,o.default)((0,r.default)().mark((function t(i){return(0,r.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:e.$emit("success",i.tempFilePath);case 1:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()).catch((function(t){e.$emit("fail",new Error(JSON.stringify(t)))})),e.isDrawIng=!1,i.abrupt("return",Promise.resolve({ctx:l,draw:e.painter,node:e.node}));case 40:case"end":return i.stop()}}),i)})))()},custom:function(t){var e=this;return(0,o.default)((0,r.default)().mark((function i(){var n,o,a;return(0,r.default)().wrap((function(i){while(1)switch(i.prev=i.next){case 0:return i.next=2,e.render({},!0);case 2:return n=i.sent,o=n.ctx,a=n.draw,o.save(),i.next=8,t(o,a);case 8:return o.restore(),i.abrupt("return",Promise.resolve(!0));case 10:case"end":return i.stop()}}),i)})))()},single:function(){var t=arguments,e=this;return(0,o.default)((0,r.default)().mark((function i(){var n,o;return(0,r.default)().wrap((function(i){while(1)switch(i.prev=i.next){case 0:return n=t.length>0&&void 0!==t[0]?t[0]:{},i.next=3,e.render(n,!0);case 3:return o=i.sent,i.abrupt("return",Promise.resolve(o));case 5:case"end":return i.stop()}}),i)})))()},canvasDraw:function(){var t=this,e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return new Promise((function(i,n){return t.ctx.draw(e,(function(){return setTimeout((function(){return i()}),t.afterDelay)}))}))},getContext:function(){var t=this;return(0,o.default)((0,r.default)().mark((function e(){var i,n,o;return(0,r.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!t.ctx||!t.inited){e.next=2;break}return e.abrupt("return",Promise.resolve(t.ctx));case 2:return t.getParentWeith(),i=t.type,t.use2dCanvas,n=t.dpr,t.boardWidth,t.boardHeight,o=function(){return new Promise((function(e){uni.createSelectorQuery().in(t).select("#".concat(t.canvasId)).boundingClientRect().exec((function(i){if(i){var r=uni.createCanvasContext(t.canvasId,t);t.inited||(t.inited=!0,t.use2dCanvas=!1,t.canvas=i),t.isPC&&r.scale(1/n,1/n),t.ctx=r,e(t.ctx)}}))}))},e.abrupt("return",o());case 8:return e.abrupt("return",new Promise((function(e){uni.createSelectorQuery().in(t).select("#".concat(t.canvasId)).node().exec((function(n){var r=n[0].node;r||(t.use2dCanvas=!1,e(t.getContext()));var o=r.getContext(i);t.inited||(t.inited=!0,t.use2dCanvas=!0,t.canvas=r),t.ctx=o,e(t.ctx)}))})));case 9:case"end":return e.stop()}}),e)})))()},canvasToTempFilePath:function(){var t=this,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},i=this.use2dCanvas,n=this.canvasId,a=this.dpr,s=this.fileType,d=this.quality;return new Promise((function(c,u){var l=t.boundary||t,f=l.top,h=void 0===f?0:f,v=l.left,p=void 0===v?0:v,g=l.width,b=l.height,m=g*a,w=b*a,x=function(){var e=(0,o.default)((0,r.default)().mark((function e(i){var n;return(0,r.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.setFilePath(i.tempFilePath);case 2:n=e.sent,c(Object.assign(i,{tempFilePath:n}));case 4:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),y=Object.assign({x:p,y:h,width:g,height:b,destWidth:m,destHeight:w,canvasId:n,fileType:s,quality:d,success:x,fail:u},e);i&&(delete y.canvasId,y.canvas=t.canvas),uni.canvasToTempFilePath(y,t)}))}}};e.default=c},e2db:function(t,e,i){var n=i("b83e");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var r=i("4f06").default;r("fbf8b9f0",n,!0,{sourceMap:!1,shadowMode:!1})},eb32:function(t,e,i){"use strict";i.r(e);var n=i("8810"),r=i("a1ed");for(var o in r)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return r[t]}))}(o);var a=i("f0c5"),s=Object(a["a"])(r["default"],n["b"],n["c"],!1,null,"1328a9e4",null,!1,n["a"],void 0);e["default"]=s.exports},f48a:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return r})),i.d(e,"a",(function(){}));var n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return t.canvasId&&t.size?i("v-uni-view",{staticClass:"lime-painter",style:t.size+t.customStyle},[t.use2dCanvas?i("v-uni-canvas",{staticClass:"lime-painter__canvas",style:t.size,attrs:{id:t.canvasId,type:"2d"}}):i("v-uni-canvas",{staticClass:"lime-painter__canvas",style:t.size,attrs:{"canvas-id":t.canvasId,id:t.canvasId,width:t.boardWidth*t.dpr,height:t.boardHeight*t.dpr}}),t._t("default")],2):t._e()},r=[]},ffe8:function(t,e,i){"use strict";i.r(e);var n=i("416f"),r=i.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);e["default"]=r.a}}]);