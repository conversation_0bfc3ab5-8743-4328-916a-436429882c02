<?php

namespace app\api\logic;

use app\common\basics\Logic;
use app\common\model\user\User;
use app\common\server\ConfigServer;
use think\facade\Db;

/**
 * 微信关注检查逻辑
 * Class WechatFollowLogic
 * @package app\api\logic
 */
class WechatFollowLogic extends Logic
{
    /**
     * 检查用户是否关注公众号
     * @param int $userId
     * @return array
     */
    public static function checkUserFollow($userId)
    {
        // 获取用户信息
        $user = User::where(['id' => $userId, 'del' => 0])->findOrEmpty();
        
        if ($user->isEmpty()) {
            return [
                'is_follow' => false,
                'message' => '用户不存在'
            ];
        }
        $user=Db::name('user_auth')->where('user_id',$userId)->findOrEmpty();
        $unionid = $user['unionid'] ?? '';
        
        if (empty($unionid)) {
            return [
                'is_follow' => false,
                'message' => '用户未绑定微信',
                'need_bind' => true
            ];
        }
        
        // 检查unionid是否在关注列表中
        $follower = Db::name('wechat_followers')
            ->where([
                'unionid' => $unionid,
                'subscribe' => 1
            ])
            ->find();
            
        if ($follower) {
            return [
                'is_follow' => true,
                'message' => '用户已关注公众号',
                'follow_time' => $follower['subscribe_time'],
                'follow_date' => date('Y-m-d H:i:s', $follower['subscribe_time'])
            ];
        } else {
            return [
                'is_follow' => false,
                'message' => '用户未关注公众号',
                'guide_info' => self::getFollowGuide()
            ];
        }
    }
    
    /**
     * 获取关注引导信息
     * @return array
     */
    public static function getFollowGuide()
    {
        return [
            'title' => '关注公众号，享受更多服务',
            'description' => '关注我们的微信公众号，获取最新商品信息、优惠活动和专属服务',
            'qr_code' => ConfigServer::get('oa', 'qr_code', ''),
            'account_name' => ConfigServer::get('oa', 'name', ''),
            'benefits' => [
                '第一时间获取新品上架通知',
                '专享会员优惠和活动信息',
                '便捷的客服咨询服务',
                '个性化商品推荐'
            ]
        ];
    }
    
    /**
     * 记录用户关注引导展示
     * @param int $userId
     * @return bool
     */
    public static function recordGuideShow($userId)
    {
        try {
            // 检查今天是否已经记录过
            $today = date('Y-m-d');
            $exists = Db::name('wechat_follow_guide_log')
                ->where([
                    'user_id' => $userId,
                    'show_date' => $today
                ])
                ->find();
                
            if (!$exists) {
                Db::name('wechat_follow_guide_log')->insert([
                    'user_id' => $userId,
                    'show_date' => $today,
                    'show_count' => 1,
                    'create_time' => time(),
                    'update_time' => time()
                ]);
            } else {
                Db::name('wechat_follow_guide_log')
                    ->where(['id' => $exists['id']])
                    ->update([
                        'show_count' => $exists['show_count'] + 1,
                        'update_time' => time()
                    ]);
            }
            
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * 获取用户关注引导统计
     * @param int $userId
     * @return array
     */
    public static function getUserGuideStats($userId)
    {
        $stats = Db::name('wechat_follow_guide_log')
            ->where(['user_id' => $userId])
            ->field('COUNT(*) as total_days, SUM(show_count) as total_shows')
            ->find();
            
        return [
            'total_days' => $stats['total_days'] ?? 0,
            'total_shows' => $stats['total_shows'] ?? 0,
            'last_show' => Db::name('wechat_follow_guide_log')
                ->where(['user_id' => $userId])
                ->order('update_time desc')
                ->value('update_time')
        ];
    }
    
    /**
     * 批量检查用户关注状态
     * @param array $userIds
     * @return array
     */
    public static function batchCheckFollow($userIds)
    {
        if (empty($userIds)) {
            return [];
        }
        
        // 获取用户unionid
        $users = User::where([
            ['id', 'in', $userIds],
            ['del', '=', 0]
        ])->column('unionid', 'id');
        
        $result = [];
        foreach ($userIds as $userId) {
            $unionid = $users[$userId] ?? '';
            if (empty($unionid)) {
                $result[$userId] = false;
                continue;
            }
            
            $isFollow = Db::name('wechat_followers')
                ->where([
                    'unionid' => $unionid,
                    'subscribe' => 1
                ])
                ->count() > 0;
                
            $result[$userId] = $isFollow;
        }
        
        return $result;
    }
}
