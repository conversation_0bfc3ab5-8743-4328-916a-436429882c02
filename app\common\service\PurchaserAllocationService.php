<?php
namespace app\common\service;

use app\admin\logic\PurchaserAllocationLogic;
use app\common\model\shop\Shop;
use app\common\model\ShopPurchaserAllocation;
use think\facade\Db;
use think\facade\Log;
use think\console\Output;
/**
 * 采购人员分配服务
 * Class PurchaserAllocationService
 * @package app\common\service
 */
class PurchaserAllocationService
{
    /**
     * 商家升级时自动分配采购人员
     * @param int $shopId 商家ID
     * @param int $newTierLevel 新等级
     * @param int $oldTierLevel 原等级
     * @return bool
     */
    public static function onShopTierUpgrade($shopId, $newTierLevel, $oldTierLevel = null)
    {
        try {
            Log::info("商家升级自动分配采购人员", [
                'shop_id' => $shopId,
                'new_tier_level' => $newTierLevel,
                'old_tier_level' => $oldTierLevel
            ]);
            
            // 执行自动分配
            $result = PurchaserAllocationLogic::autoAllocatePurchasers($shopId, $newTierLevel);
            
            if ($result) {
                Log::info("商家升级自动分配采购人员成功", ['shop_id' => $shopId]);
            } else {
                Log::error("商家升级自动分配采购人员失败", [
                    'shop_id' => $shopId,
                    'error' => PurchaserAllocationLogic::getError()
                ]);
            }
            
            return $result;
            
        } catch (\Exception $e) {
            Log::error("商家升级自动分配采购人员异常", [
                'shop_id' => $shopId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 年度重新分配检查和执行
     * @return bool
     */
    public static function yearlyReAllocation()
    {
        try {
            Log::info("开始执行年度重新分配检查");
            
            $result = PurchaserAllocationLogic::checkAndReAllocate();
            
            if ($result) {
                Log::info("年度重新分配执行成功");
            } else {
                Log::error("年度重新分配执行失败", [
                    'error' => PurchaserAllocationLogic::getError()
                ]);
            }
            
            return $result;
            
        } catch (\Exception $e) {
            Log::error("年度重新分配执行异常", ['error' => $e->getMessage()]);
            return false;
        }
    }

    /**
     * 检查商家是否需要重新分配
     * @param int $shopId 商家ID
     * @return bool
     */
    public static function checkShopNeedReAllocation($shopId)
    {
        try {
            $currentYear = date('Y');
            
            // 检查今年是否有分配记录
            $hasCurrentYearAllocation = ShopPurchaserAllocation::where([
                ['shop_id', '=', $shopId],
                ['allocation_year', '=', $currentYear],
                ['status', '=', 1]
            ])->count() > 0;
            
            if ($hasCurrentYearAllocation) {
                return false; // 今年已有分配，无需重新分配
            }
            
            // 检查去年是否有分配记录
            $lastYear = $currentYear - 1;
            $hasLastYearAllocation = ShopPurchaserAllocation::where([
                ['shop_id', '=', $shopId],
                ['allocation_year', '=', $lastYear],
                ['status', '=', 1]
            ])->count() > 0;
            
            return $hasLastYearAllocation; // 去年有分配但今年没有，需要重新分配
            
        } catch (\Exception $e) {
            Log::error("检查商家是否需要重新分配异常", [
                'shop_id' => $shopId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 为单个商家执行重新分配
     * @param int $shopId 商家ID
     * @return bool
     */
    public static function reAllocateForShop($shopId)
    {
        try {
            $shop = Shop::find($shopId);
            if (!$shop) {
                Log::error("商家不存在", ['shop_id' => $shopId]);
               
                return false;
            }
            
            Log::info("为商家执行重新分配", [
                'shop_id' => $shopId,
                'tier_level' => $shop->tier_level
            ]);
            
            $result = PurchaserAllocationLogic::autoAllocatePurchasers($shopId, $shop->tier_level);
            
            if ($result) {
                Log::info("商家重新分配成功", ['shop_id' => $shopId]);
            } else {
                Log::error("商家重新分配失败", [
                    'shop_id' => $shopId,
                    'error' => PurchaserAllocationLogic::getError()
                ]);
            }
            
            return $result;
            
        } catch (\Exception $e) {
            Log::error("商家重新分配异常", [
                'shop_id' => $shopId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 获取需要重新分配的商家列表
     * @return array
     */
    public static function getShopsNeedReAllocation()
    {
        try {
            $currentYear = date('Y');
            $lastYear = $currentYear - 1;
            
            // 获取去年有分配记录但今年没有的商家
            $shops = Db::query("
                SELECT DISTINCT s.id, s.tier_level, s.name
                FROM ls_shop s 
                INNER JOIN ls_shop_purchaser_allocation spa ON s.id = spa.shop_id 
                WHERE spa.allocation_year = ? 
                AND spa.status = 1
                AND s.id NOT IN (
                    SELECT DISTINCT shop_id 
                    FROM ls_shop_purchaser_allocation 
                    WHERE allocation_year = ? AND status = 1
                )
                AND s.del = 0
                ORDER BY s.id
            ", [$lastYear, $currentYear]);
            if (empty($shops)) {
                // 如果没有去年有分配但今年没有的商家，则查询ls_shop_purchaser_allocation中没有数据的商家
                $shops = Db::query("
                    SELECT s.id, s.tier_level, s.name
                    FROM ls_shop s
                    LEFT JOIN ls_shop_purchaser_allocation spa ON s.id = spa.shop_id
                    WHERE spa.shop_id IS NULL AND s.del = 0
                    ORDER BY s.id
                ");
            }
            return $shops;
            
        } catch (\Exception $e) {
            Log::error("获取需要重新分配的商家列表异常", ['error' => $e->getMessage()]);
            return [];
        }
    }

    /**
     * 批量重新分配
     * @param array $shopIds 商家ID数组
     * @return array 分配结果
     */
    public static function batchReAllocate($shopIds)
    {
        $results = [
            'success' => [],
            'failed' => [],
            'total' => count($shopIds)
        ];
        
        foreach ($shopIds as $shopId) {
            if (self::reAllocateForShop($shopId)) {
                $results['success'][] = $shopId;
            } else {
                $results['failed'][$shopId] = PurchaserAllocationLogic::getError() ?: '未知错误';
            }
        }
        
        Log::info("批量重新分配完成", $results);
        
        return $results;
    }

    /**
     * 获取分配统计信息
     * @return array
     */
    public static function getAllocationStatistics()
    {
        try {
            $currentYear = date('Y');
            
            // 总体统计
            $totalShops = Shop::where('del', 0)->count();
            $allocatedShops = ShopPurchaserAllocation::where([
                ['allocation_year', '=', $currentYear],
                ['status', '=', 1]
            ])->distinct('shop_id')->count();
            
            // 按等级统计
            $tierStats = Db::query("
                SELECT 
                    s.tier_level,
                    COUNT(DISTINCT s.id) as total_shops,
                    COUNT(DISTINCT spa.shop_id) as allocated_shops
                FROM ls_shop s
                LEFT JOIN ls_shop_purchaser_allocation spa ON s.id = spa.shop_id 
                    AND spa.allocation_year = ? AND spa.status = 1
                WHERE s.del = 0
                GROUP BY s.tier_level
                ORDER BY s.tier_level
            ", [$currentYear]);
            
            // 用户等级分布统计
            $userLevelStats = Db::query("
                SELECT 
                    user_level,
                    COUNT(*) as count
                FROM ls_shop_purchaser_allocation
                WHERE allocation_year = ? AND status = 1
                GROUP BY user_level
                ORDER BY user_level
            ", [$currentYear]);
            
            return [
                'total_shops' => $totalShops,
                'allocated_shops' => $allocatedShops,
                'allocation_rate' => $totalShops > 0 ? round(($allocatedShops / $totalShops) * 100, 2) : 0,
                'tier_stats' => $tierStats,
                'user_level_stats' => $userLevelStats
            ];
            
        } catch (\Exception $e) {
            Log::error("获取分配统计信息异常", ['error' => $e->getMessage()]);
            return [];
        }
    }
}
