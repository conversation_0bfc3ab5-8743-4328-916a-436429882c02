{layout name="layout2" /}

<div class="wrapper">
    <div class="layui-card">
        <div class="layui-card-body">
            <form class="layui-form" lay-filter="form">
                <input type="hidden" name="id" value="{$detail.id}">
                <div class="layui-row layui-col-space15">
                    <div class="layui-col-md8">
                        <div class="layui-form-item">
                            <label class="layui-form-label">图标名称 <span style="color: red;">*</span></label>
                            <div class="layui-input-block">
                                <input type="text" name="icon_name" value="{$detail.icon_name}" placeholder="请输入图标名称"
                                       autocomplete="off" class="layui-input" lay-verify="required">
                            </div>
                        </div>

                        <div class="layui-form-item">
                            <label class="layui-form-label">图标URL</label>
                            <div class="layui-input-block">
                                <div class="like-upload-image">
                                    {if condition="$detail.icon_url"}
                                    <div class="upload-image-div">
                                        <img src="{$detail.icon_url}" alt="图标">
                                        <input type="hidden" name="icon_url" value="{$detail.icon_url}">
                                        <div class="del-upload-btn">x</div>
                                    </div>
                                    <div class="upload-image-elem" style="display:none;"><a class="add-upload-image"> + 添加图片</a></div>
                                    {else/}
                                    <div class="upload-image-elem"><a class="add-upload-image"> + 添加图片</a></div>
                                    {/if}
                                </div>
                                <div class="layui-form-mid layui-word-aux">建议尺寸：64x64像素，支持jpg、jpeg、png格式</div>
                            </div>
                        </div>

                        <div class="layui-form-item">
                            <label class="layui-form-label">跳转路径</label>
                            <div class="layui-input-block">
                                <input type="text" name="icon_path" value="{$detail.icon_path}" placeholder="请输入跳转路径，如：/pages/goods/list"
                                       autocomplete="off" class="layui-input">
                                <div class="layui-form-mid layui-word-aux">移动端页面路径</div>
                            </div>
                        </div>

                        <div class="layui-form-item">
                            <label class="layui-form-label">关联权限</label>
                            <div class="layui-input-block">
                                <select name="auth_id" lay-search>
                                    <option value="0">请选择关联权限</option>
                                    {volist name="authList" id="auth"}
                                    <option value="{$auth.id}" {if condition="$auth.id == $detail.auth_id"}selected{/if}>
                                        {$auth.name} ({$auth.uri})
                                    </option>
                                    {/volist}
                                </select>
                                <div class="layui-form-mid layui-word-aux">选择对应的商家权限菜单</div>
                            </div>
                        </div>

                        <div class="layui-form-item">
                            <label class="layui-form-label">商家类型 <span style="color: red;">*</span></label>
                            <div class="layui-input-block">
                                {volist name="merchantTypeOptions" id="option"}
                                <input type="checkbox" name="merchant_types[]" value="{$option.value}" title="{$option.text}"
                                       {php}
                                       $selectedTypes = explode(',', $detail['merchant_types'] ?? '0,1,2');
                                       if (in_array($option['value'], $selectedTypes)) echo 'checked';
                                       {/php}>
                                {/volist}
                                <div class="layui-form-mid layui-word-aux">选择允许访问此图标的商家类型</div>
                            </div>
                        </div>

                        <div class="layui-form-item">
                            <label class="layui-form-label">排序</label>
                            <div class="layui-input-block">
                                <input type="number" name="sort_order" value="{$detail.sort_order}" placeholder="请输入排序值"
                                       autocomplete="off" class="layui-input" lay-verify="number">
                                <div class="layui-form-mid layui-word-aux">数值越小越靠前</div>
                            </div>
                        </div>

                        <div class="layui-form-item">
                            <label class="layui-form-label">状态</label>
                            <div class="layui-input-block">
                                <input type="radio" name="status" value="1" title="启用" {if condition="$detail.status == 1"}checked{/if}>
                                <input type="radio" name="status" value="0" title="禁用" {if condition="$detail.status == 0"}checked{/if}>
                            </div>
                        </div>

                        <!-- 隐藏的提交按钮，供JavaScript调用 -->
                        <button class="layui-btn" lay-submit lay-filter="submit" id="submit" style="display: none;">提交</button>

                    </div>

                    <div class="layui-col-md4">
                        <div class="layui-card">
                            <div class="layui-card-header">图标预览</div>
                            <div class="layui-card-body" style="text-align: center; padding: 30px;">
                                <div id="icon-preview" style="min-height: 100px; display: flex; align-items: center; justify-content: center; border: 1px dashed #ddd; border-radius: 4px;">
                                    {if condition="$detail.icon_url"}
                                    <img src="{$detail.icon_url}" style="max-width: 64px; max-height: 64px; border-radius: 4px;">
                                    {else/}
                                    <span style="color: #999;">暂无图标</span>
                                    {/if}
                                </div>
                            </div>
                        </div>

                        <div class="layui-card" style="margin-top: 15px;">
                            <div class="layui-card-header">上传说明</div>
                            <div class="layui-card-body">
                                <p style="margin: 0; color: #666; font-size: 12px;">
                                    • 建议尺寸：64x64像素<br>
                                    • 支持格式：jpg、jpeg、png<br>
                                    • 文件大小：不超过2MB<br>
                                    • 用于移动端图标显示
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
layui.config({
    version: "{$front_version}",
    base: '/static/lib/'
}).use(['form'], function () {
    var form = layui.form;

    // 图标上传
    like.delUpload();
    $(document).on("click", ".add-upload-image", function () {
        like.imageUpload({
            limit: 1,
            field: "icon_url",
            that: $(this)
        });
    });

    // 监听上传完成后的预览更新
    $(document).on('DOMSubtreeModified', '.like-upload-image', function() {
        var iconUrl = $('input[name="icon_url"]').val();
        if (iconUrl) {
            updateIconPreview(iconUrl);
        }
    });

    // 更新图标预览
    function updateIconPreview(url) {
        var preview = $('#icon-preview');
        if (url) {
            preview.html('<img src="' + url + '" style="max-width: 64px; max-height: 64px; border-radius: 4px;">');
        } else {
            preview.html('<span style="color: #999;">暂无图标</span>');
        }
    }

    // 表单提交
    form.on('submit(submit)', function (data) {
        // 这里不做实际提交，由父页面处理
        return false;
    });
});
</script>
