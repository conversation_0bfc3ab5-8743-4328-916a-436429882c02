<?php

namespace app\common\listener\websocket;


use app\common\enum\ChatMsgEnum;
use app\common\enum\ChatRecordEnum;
use app\common\logic\ChatLogic;
use app\common\model\goods\Goods;
use app\common\model\kefu\ChatRecord;
use app\common\model\kefu\ChatRelation;
use app\common\server\UrlServer;
use app\common\websocket\Response;
use app\common\model\OfflineMessages;
use think\facade\Log;

/**
 * 对话事件
 * Class Chat
 * @package app\common\listener\websocket
 */
class Chat
{

    protected $response;

    public function __construct(Response $response)
    {
        $this->response = $response;
    }


    public function handle($params): bool
    {
        $from_fd = $params['fd'];
        $request_data = $params['data'];
        $handleClass = $params['handle'];

        $from_data = $handleClass->getDataByFd($from_fd);

        // 当前发送人信息是否存在
        if (empty($from_data['type']) || empty($from_data['uid'])) {
            return $handleClass->pushData($from_fd, 'error', $this->response->formatSendError('聊天用户不存在或不在线'));
        }

        // 检查聊天类型，默认为客服聊天
        $chat_type = $request_data['chat_type'] ?? 'kefu_chat';

        // 根据聊天类型处理不同的逻辑
        if ($chat_type === 'user_chat') {
            return $this->handleUserChat($from_fd, $request_data, $handleClass, $from_data);
        } else {
            return $this->handleKefuChat($from_fd, $request_data, $handleClass, $from_data);
        }
    }

    /**
     * 处理客服聊天
     */
    protected function handleKefuChat($from_fd, $request_data, $handleClass, $from_data): bool
    {
        // 验证必要参数
        if (empty($request_data['to_id'])) {
            return $handleClass->pushData($from_fd, 'error', $this->response->formatSendError('接收者ID不能为空'));
        }

        if (empty($request_data['to_type'])) {
            return $handleClass->pushData($from_fd, 'error', $this->response->formatSendError('接收者类型不能为空'));
        }

        $to_fd = $handleClass->getFdByUid($request_data['to_id'], $request_data['to_type']);
        // 接收人不在线
        $online_fd = $handleClass->onlineFd($to_fd);

        // 验证后台配置是否开启
        $check = $this->checkConfig($from_data['shop_id']);
        if (true !== $check) {
            return $handleClass->pushData($from_fd, 'error', $this->response->formatSendError($check));
        }

        $toName = '客服';
        if ('kefu' == $from_data['type']) {
            $toName = '用户';
        }
        if (empty($online_fd)) {
            // 存储离线消息
            $this->storeOfflineMessage($request_data['to_id'], $request_data['to_type'], $request_data['msg'], $request_data['msg_type']);
        }

        // 添加聊天记录
        $record = $this->insertRecord([
            'shop_id' => $this->getCorrectShopId($from_data, $request_data),
            'from_id' => $from_data['uid'],
            'from_type' => $from_data['type'],
            'to_id' => $request_data['to_id'],
            'to_type' => $request_data['to_type'],
            'msg' => $request_data['msg'],
            'msg_type' => $request_data['msg_type'],
            'voice_duration' => $request_data['voice_duration'] ?? 0,
        ]);

        // 格式化消息用于推送
        $this->formatRecordForPush($record, $from_data, $request_data['msg_type']);
        $record['chat_type'] = 'kefu_chat'; // 标识为客服聊天

        // 更新聊天关系记录 - 修复shop_id问题
        $this->bindRelation([
            'shop_id' => $this->getCorrectShopId($from_data, $request_data),
            'from_id' => $from_data['uid'],
            'from_type' => $from_data['type'],
            'to_id' => $request_data['to_id'],
            'to_type' => $request_data['to_type'],
            'msg' => $request_data['msg'],
            'msg_type' => $request_data['msg_type'],
            'client' => $from_data['client']
        ]);

        if (!empty($record)) {
            // 发送给发送者（确认消息）
            $handleClass->pushData($from_fd, 'chat', $record);

            // 发送给接收者（如果在线）
            if (!empty($online_fd)) {
                $handleClass->pushData($to_fd, 'chat', $record);
            }
        }

        return true;
    }

    /**
     * 处理用户对用户聊天
     */
    protected function handleUserChat($from_fd, $request_data, $handleClass, $from_data): bool
    {
        // 验证接收者ID
        if (empty($request_data['to_id']) || !is_numeric($request_data['to_id'])) {
            return $handleClass->pushData($from_fd, 'error', $this->response->formatSendError('接收者ID无效'));
        }

        // 验证消息内容
        if (empty($request_data['msg']) && $request_data['msg_type'] == ChatMsgEnum::TYPE_TEXT) {
            return $handleClass->pushData($from_fd, 'error', $this->response->formatSendError('消息内容不能为空'));
        }

        // 获取接收者连接 - 用户对用户聊天，接收者也是user类型
        $to_fd = $handleClass->getFdByUid($request_data['to_id'], 'user');

        // 添加聊天记录 - 用户对用户聊天shop_id为0
        $record = $this->insertRecord([
            'shop_id' => 0, // 用户对用户聊天不属于特定商家
            'from_id' => $from_data['uid'],
            'from_type' => 'user',
            'to_id' => $request_data['to_id'],
            'to_type' => 'user',
            'msg' => $request_data['msg'],
            'msg_type' => $request_data['msg_type'],
            'voice_duration' => $request_data['voice_duration'] ?? 0,
        ]);

        if (!$record) {
            return $handleClass->pushData($from_fd, 'error', $this->response->formatSendError('消息发送失败'));
        }

        // 格式化消息数据
        $this->formatRecordForPush($record, $from_data, $request_data['msg_type']);
        $record['chat_type'] = 'user_chat'; // 标识为用户聊天

        // 创建用户对用户的聊天关系
        $this->bindUserChatRelation($from_data['uid'], $request_data['to_id'], [
            'msg' => $request_data['msg'],
            'msg_type' => $request_data['msg_type'],
            'client' => $from_data['client']
        ]);

        // 发送给发送者（确认消息）
        $handleClass->pushData($from_fd, 'chat', $record);

        // 发送给接收者
        if (!empty($to_fd)) {
            $online_fd = $handleClass->onlineFd($to_fd);
            if (!empty($online_fd)) {
                $handleClass->pushData($to_fd, 'chat', $record);
            } else {
                // 接收者不在线，存储离线消息
                $this->storeOfflineMessage($request_data['to_id'], 'user', $request_data['msg'], $request_data['msg_type']);
                Log::info("用户对用户消息发送: 接收者不在线，已存储离线消息, from_id={$from_data['uid']}, to_id={$request_data['to_id']}");
            }
        } else {
            // 接收者从未连接过，也算离线，存储离线消息
            $this->storeOfflineMessage($request_data['to_id'], 'user', $request_data['msg'], $request_data['msg_type']);
            Log::info("用户对用户消息发送: 接收者FD不存在，已存储离线消息, from_id={$from_data['uid']}, to_id={$request_data['to_id']}");
        }

        return true;
    }

    /**
     * 格式化聊天记录用于推送
     * @param array $record 引用传递的记录数组
     * @param array $from_data 发送者信息
     * @param int $msg_type 消息类型
     * @return void
     */
    private function formatRecordForPush(array &$record, array $from_data, int $msg_type): void
    {
        $record['from_avatar'] = !empty($from_data['avatar']) ? UrlServer::getFileUrl($from_data['avatar']) : '';
        $record['from_nickname'] = $from_data['nickname'];
        $record['create_time_stamp'] = $record['create_time'];
        $record['create_time'] = date('Y-m-d H:i:s', $record['create_time']);
        $record['update_time'] = date('Y-m-d H:i:s', $record['update_time']);
        $record['goods'] = [];

        // 根据消息类型处理不同的媒体文件URL
        switch ($msg_type) {
            case ChatMsgEnum::TYPE_IMG:
                // 图片消息，确保URL完整
                $record['msg'] = UrlServer::getFileUrl($record['msg']);
                break;

            case ChatMsgEnum::TYPE_VOICE:
                // 语音消息，确保URL完整
                $record['msg'] = UrlServer::getFileUrl($record['msg']);
                break;

            case ChatMsgEnum::TYPE_VIDEO:
                // 视频消息，确保URL完整
                $record['msg'] = UrlServer::getFileUrl($record['msg']);
                break;

            case ChatMsgEnum::TYPE_GOODS:
                // 商品消息，解析JSON并处理图片URL
                $goods = json_decode($record['msg'], true);
                if ($goods && isset($goods['image'])) {
                    $goods['image'] = UrlServer::getFileUrl($goods['image']);
                    $record['msg'] = json_encode($goods, JSON_UNESCAPED_UNICODE);
                }
                $record['goods'] = $goods ?: [];
                break;
            case ChatMsgEnum::TYPE_ORDER:
                // 订单消息，解析JSON
                $orders = json_decode($record['msg'], true);
                $record['order'] =  $orders ?: [];
                break;   
        }
    }

    /**
     * 获取正确的shop_id，解决客服聊天中shop_id不一致的问题
     * @param array $from_data 发送者信息
     * @param array $request_data 请求数据
     * @return int
     */
    private function getCorrectShopId(array $from_data, array $request_data): int
    {
        // 如果请求中有shop_id，优先使用
        if (!empty($request_data['shop_id'])) {
            return (int)$request_data['shop_id'];
        }

        // 如果发送者信息中有shop_id，使用发送者的
        if (!empty($from_data['shop_id'])) {
            return (int)$from_data['shop_id'];
        }

        // // 如果是客服聊天但没有shop_id，尝试从客服信息中获取
        // if ($request_data['to_type'] === 'kefu' && !empty($request_data['to_id'])) {
        //     $kefu = \app\common\model\kefu\Kefu::where('id', $request_data['to_id'])
        //         ->field('shop_id')
        //         ->findOrEmpty();
        //     if (!$kefu->isEmpty()) {
        //         return (int)$kefu['shop_id'];
        //     }
        // }

        // 如果发送者是客服，尝试从发送者客服信息中获取
        // if ($request_data['client'] === 1) {
        //      return 1;
        //     // $kefu = \app\common\model\kefu\Kefu::where('id', $from_data['to_id'])
        //     //     ->field('shop_id')
        //     //     ->findOrEmpty();
        //     // if (!$kefu->isEmpty()) {
               
        //     // }
        // }

        // 默认返回0（平台客服或用户聊天）
        return 0;
    }

    /**
     * 绑定用户对用户的聊天关系
     * @param int $user_id 当前用户ID
     * @param int $contact_user_id 联系人用户ID
     * @param array $data 消息数据
     * @return void
     */
    private function bindUserChatRelation(int $user_id, int $contact_user_id, array $data): void
    {
        try {
            // 为发送者创建/更新与接收者的关系
            $this->createOrUpdateUserRelation($user_id, $contact_user_id, $data, 1); // 发送者已读

            // 为接收者创建/更新与发送者的关系
            $this->createOrUpdateUserRelation($contact_user_id, $user_id, $data, 0); // 接收者未读
        } catch (\Exception $e) {
            Log::error("绑定用户聊天关系失败: " . $e->getMessage());
        }
    }

    /**
     * 创建或更新用户聊天关系
     * @param int $user_id 用户ID
     * @param int $contact_user_id 联系人用户ID
     * @param array $data 消息数据
     * @param int $is_read 是否已读
     * @return void
     */
    private function createOrUpdateUserRelation(int $user_id, int $contact_user_id, array $data, int $is_read): void
    {
        // 查找现有关系
        $relation = ChatRelation::where([
            'user_id' => $user_id,
            'kefu_id' => $contact_user_id, // 这里复用kefu_id字段存储联系人用户ID
            'shop_id' => 0, // 用户聊天shop_id为0
        ])->findOrEmpty();

        // 获取联系人用户信息
        $contactUser = \app\common\model\user\User::where('id', $contact_user_id)
            ->field('id,nickname,avatar')
            ->findOrEmpty();

        if ($relation->isEmpty()) {
            // 创建新关系
            ChatRelation::create([
                'shop_id' => 0,
                'user_id' => $user_id,
                'kefu_id' => $contact_user_id, // 复用字段存储联系人用户ID
                'nickname' => $contactUser['nickname'] ?: '用户' . $contact_user_id, // 防止nickname为空
                'avatar' => $contactUser['avatar'] ?? '',
                'client' => $data['client'] ?? 0,
                'msg' => $data['msg'] ?? '',
                'msg_type' => $data['msg_type'] ?? ChatMsgEnum::TYPE_TEXT,
                'is_read' => $is_read,
                'create_time' => time(),
                'update_time' => time(),
            ]);
        } else {
            // 更新现有关系
            ChatRelation::update([
                'nickname' => $contactUser['nickname'] ?: '用户' . $contact_user_id, // 防止nickname为空
                'avatar' => $contactUser['avatar'] ?? '',
                'client' => $data['client'] ?? 0,
                'msg' => $data['msg'] ?? '',
                'msg_type' => $data['msg_type'] ?? ChatMsgEnum::TYPE_TEXT,
                'update_time' => time(),
                'is_read' => $is_read
            ], ['id' => $relation->id]); // 修复：使用模型对象的id属性
        }
    }

    /**
     * 存储离线消息
     *
     * @param int $to_id 接收者ID
     * @param string $to_type 接收者类型
     * @param string $msg 消息内容
     * @param int $msg_type 消息类型
     * @return void
     */
    protected function storeOfflineMessage($to_id, $to_type, $msg, $msg_type): void
    {
        try {
            // 存储离线消息
            $offlineMessage = new OfflineMessages();
            $offlineMessage->to_id = $to_id;
            $offlineMessage->to_type = $to_type;
            $offlineMessage->msg = $msg;
            $offlineMessage->msg_type = $msg_type;
            $offlineMessage->create_time = time();
            $result = $offlineMessage->save();

            if (!$result) {
                Log::error("离线消息存储失败: to_id={$to_id}, to_type={$to_type}");
            }
        } catch (\Exception $e) {
            Log::error("离线消息存储异常: " . $e->getMessage() . ", to_id={$to_id}, to_type={$to_type}");
        }
    }


    /**
     * @notes 检查后台配置
     * @param $shop_id
     * @return array|bool|string
     * <AUTHOR>
     * @date 2021/12/20 18:29
     */
    public function checkConfig($shop_id)
    {
        if (false === ChatLogic::checkConfig($shop_id)) {
            return ChatLogic::getError() ?: '请联系管理员设置后台配置';
        }
        return true;
    }


    /**
     * @notes 增加聊天记录
     * @param $data
     * @return array
     * <AUTHOR>
     * @date 2021/12/17 14:33
     */
    public function insertRecord($data): array
    {
        switch ($data['msg_type']) {
            case ChatMsgEnum::TYPE_IMG:
                $msg = $data['msg'];
                break;

            case ChatMsgEnum::TYPE_GOODS:
                $goods = Goods::where(['id' => $data['msg']])->field([
                    'id', 'image', 'min_price', 'name'
                ])->findOrEmpty();

                $msg = json_encode([
                    'id' => $goods['id'] ?? 0,
                    'image' => $goods->getData('image') ?? '',
                    'min_price' => $goods['min_price'] ?? 0,
                    'name' => $goods['name'] ?? '',
                ], JSON_UNESCAPED_UNICODE);
                break;

            case ChatMsgEnum::TYPE_VOICE:
                // 语音消息，直接存储语音文件路径或URL
                $msg = $data['msg'];
                break;

            case ChatMsgEnum::TYPE_VIDEO:
                // 视频消息，直接存储视频文件路径或URL
                $msg = $data['msg'];
                break;

            case ChatMsgEnum::TYPE_ORDER:
                // 订单消息，可能需要存储订单ID或订单信息
                $msg = $data['msg'];
                break;

            default:
                $msg = $data['msg']; // 存储原始数据，由前端负责安全处理
        }

        $result = ChatRecord::create([
            'shop_id' => $data['shop_id'],
            'from_id' => $data['from_id'],
            'from_type' => $data['from_type'],
            'to_id' => $data['to_id'],
            'to_type' => $data['to_type'],
            'msg' => $msg,
            'msg_type' => $data['msg_type'],
            'voice_duration' => $data['voice_duration'] ?? 0,
            'is_read' => 0,
            'type' => ChatRecordEnum::TYPE_NORMAL,
            'create_time' => time(),
        ]);

        // 新消息产生，清除接收者的未读消息缓存
        if ($result && $data['to_id']) {
            \app\api\controller\UserChat::clearUnreadBadgeCache($data['to_id']);
        }

        // 添加活跃度积分（用户聊天）
        // 只有用户发送消息时才加分，且只对用户对用户聊天加分
        if ($data['from_type'] === 'user' && $data['shop_id'] == 0) {
            try {
                \app\common\logic\UserActivityLogic::addActivityScore(
                    $data['from_id'],
                    \app\common\enum\UserActivityEnum::ACTIVITY_CHAT,
                    ['chat_record_id' => $result->id, 'to_id' => $data['to_id']]
                );
            } catch (\Exception $e) {
                // 活跃度系统异常不影响聊天流程，记录日志即可
                trace('用户活跃度检查失败: ' . $e->getMessage(), 'error');
            }
        }

        return $result->toArray();
    }


    /**
     * @notes 绑定关系
     * @param $data
     * <AUTHOR>
     * @date 2021/12/17 14:33
     */
    public function bindRelation($data): void
    {
        if ($data['to_type'] == 'kefu') {
            $kefu_id = $data['to_id'];
            $user_id = $data['from_id'];
        } else {
            $kefu_id = $data['from_id'];
            $user_id = $data['to_id'];
        }

        $is_read = 1;
        if ($data['from_type'] == 'user') {
            $is_read = 0;
        }

        ChatLogic::bindRelation($user_id, $kefu_id, $data['shop_id'], [
            'client' => $data['client'],
            'msg' => $data['msg'],
            'msg_type' => $data['msg_type'],
        ], $is_read);
    }


}