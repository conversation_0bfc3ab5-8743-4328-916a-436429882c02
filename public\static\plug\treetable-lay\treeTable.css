/* 最外层容器 */
.ew-tree-table {
    margin: 10px 0;
    position: relative;
}

.ew-tree-table .layui-table {
    margin: 0;
    table-layout: fixed;
}

/* 表格容器 */
.ew-tree-table-group {
    position: relative;
}

/* 主体表格容器 */
.ew-tree-table > .ew-tree-table-group > .ew-tree-table-box {
    overflow: auto;
    position: relative;
    box-sizing: border-box;
}

/* 表头表格容器 */
.ew-tree-table > .ew-tree-table-group > .ew-tree-table-head {
    overflow: hidden;
    position: relative;
    box-sizing: border-box;
    background-color: #f2f2f2;
}

/* 容器加边框 */
.ew-tree-table .ew-tree-table-border {
    position: absolute;
    background-color: #e6e6e6;
}

.ew-tree-table .ew-tree-table-border.top {
    left: 0;
    right: 0;
    top: 0;
    height: 1px;
}

.ew-tree-table .ew-tree-table-border.left {
    top: 0;
    left: 0;
    bottom: 0;
    width: 1px;
}

.ew-tree-table .ew-tree-table-border.right {
    top: 0;
    right: 0;
    bottom: 0;
    width: 0.52px;
}

.ew-tree-table .ew-tree-table-border.bottom {
    left: 0;
    right: 0;
    bottom: 0;
    height: 0.52px;
}

/* table的loading */
.ew-tree-table .ew-tree-table-box > .ew-tree-table-loading {
    padding: 10px 0;
    text-align: center;
}

.ew-tree-table .ew-tree-table-box > .ew-tree-table-loading > i {
    color: #999;
    font-size: 30px;
}

.ew-tree-table .ew-tree-table-box > .ew-tree-table-loading.ew-loading-float {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
}

/* 空数据提示 */
.ew-tree-table .ew-tree-table-box > .ew-tree-table-empty {
    color: #666;
    font-size: 14px;
    text-align: center;
}

/* 折叠箭头 */
.ew-tree-table .ew-tree-table-arrow {
    margin-right: 5px;
    vertical-align: middle;
}

.ew-tree-table .ew-tree-table-arrow:before {
    content: "\e623";
}

.ew-tree-table .ew-tree-table-open .ew-tree-table-arrow:before {
    content: "\e625";
}

.ew-tree-table .ew-tree-table-arrow.arrow2 {
    font-size: 13px;
    font-weight: 600;
    line-height: 16px;
    height: 16px;
    width: 16px;
    display: inline-block;
    text-align: center;
    color: #888;
}

.ew-tree-table .ew-tree-table-arrow.arrow2:before {
    content: "\e602";
}

.ew-tree-table .ew-tree-table-open .ew-tree-table-arrow.arrow2:before {
    content: "\e61a";
}

/* 箭头隐藏 */
.ew-tree-table-arrow.ew-tree-table-arrow-hide {
    visibility: hidden;
}

/* 箭头变加载中状态 */
.ew-tree-table .ew-tree-table-loading > td .ew-tree-pack > .ew-tree-table-arrow:before {
    content: "\e63d" !important;
}

.ew-tree-table .ew-tree-table-loading > td .ew-tree-pack > .ew-tree-table-arrow {
    margin-right: 0;
}

.ew-tree-table .ew-tree-table-loading > td .ew-tree-pack > .ew-tree-table-arrow + * {
    margin-left: 5px;
}

/* tr加载中禁用事件 */
.ew-tree-table tr.ew-tree-table-loading > * {
    pointer-events: none !important;
}

/* 图标列 */
.ew-tree-table .ew-tree-pack {
    cursor: pointer;
    line-height: 16px;
    display: inline-block;
    vertical-align: middle;
}

.ew-tree-table .ew-tree-pack > span {
    height: 16px;
    line-height: 16px;
    display: inline-block;
    vertical-align: middle;
}

/* 折叠行 */
.ew-tree-table .ew-tree-tb-hide {
    display: none;
}

/* 缩进 */
.ew-tree-table .ew-tree-table-indent {
    margin-right: 5px;
    padding-left: 16px;
}

/* 图标 */
.ew-tree-table .ew-tree-icon {
    margin-right: 5px;
    display: inline-block;
    vertical-align: middle;
}

.ew-tree-table .ew-tree-icon-folder, .ew-tree-table .ew-tree-icon-file {
    width: 22px;
    height: 16px;
    line-height: 16px;
    position: relative;
}

.ew-tree-table .ew-tree-icon-folder:after, .ew-tree-table .ew-tree-icon-file:after {
    content: "";
    width: 22px;
    height: 22px;
    position: absolute;
    left: 0;
    top: -3px;
    background-size: cover;
    background-image: url("data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBzdGFuZGFsb25lPSJubyI/PjwhRE9DVFlQRSBzdmcgUFVCTElDICItLy9XM0MvL0RURCBTVkcgMS4xLy9FTiIgImh0dHA6Ly93d3cudzMub3JnL0dyYXBoaWNzL1NWRy8xLjEvRFREL3N2ZzExLmR0ZCI+PHN2ZyB0PSIxNTc0MDYyMzE3MTQ3IiBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHAtaWQ9IjIxNTgiIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB3aWR0aD0iNjQiIGhlaWdodD0iNjQiPjxkZWZzPjxzdHlsZSB0eXBlPSJ0ZXh0L2NzcyI+PC9zdHlsZT48L2RlZnM+PHBhdGggZD0iTTE4MSA4MjNoLTMxLjFjLTI4LjYgMC01MS45LTIzLjItNTEuOS01MS45VjI1Mi40YzAtMjguNiAyMy4yLTUxLjkgNTEuOS01MS45SDQzMGw4MyA3Ny44aDMzMmM0NS42IDAgODMgMzUgODMgNzcuOHYzODkuMWMwIDQyLjgtMzcuMyA3Ny44LTgzIDc3LjhIMTgxeiIgcC1pZD0iMjE1OSIgZmlsbD0iI0ZGQTUwMCI+PC9wYXRoPjwvc3ZnPg==")
}

.ew-tree-table tr.ew-tree-table-open > td > .ew-tree-pack .ew-tree-icon-folder:after {
    background-image: url("data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBzdGFuZGFsb25lPSJubyI/PjwhRE9DVFlQRSBzdmcgUFVCTElDICItLy9XM0MvL0RURCBTVkcgMS4xLy9FTiIgImh0dHA6Ly93d3cudzMub3JnL0dyYXBoaWNzL1NWRy8xLjEvRFREL3N2ZzExLmR0ZCI+PHN2ZyB0PSIxNTc0MDYyMzA5MDQwIiBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHAtaWQ9IjE5NzciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB3aWR0aD0iNjQiIGhlaWdodD0iNjQiPjxkZWZzPjxzdHlsZSB0eXBlPSJ0ZXh0L2NzcyI+PC9zdHlsZT48L2RlZnM+PHBhdGggZD0iTTMyNi40IDQ2MC4xSDkyOGwtODIuMyAzMjRjLTUuOCAyMy0yNi42IDM5LjEtNTAuMyAzOS4xSDE0OS45Yy0yOC42IDAtNTEuOS0yMy4yLTUxLjktNTEuOVYyNTIuNmMwLTI4LjYgMjMuMi01MS45IDUxLjktNTEuOUg0MTNsMTA1IDEwMy43aDI5MS44YzE0LjMgMCAyNS45IDExLjYgMjUuOSAyNS45djc3LjhoLTUyN0wyMDMuNCA1NjMuOWg1Mi43bDcwLjMtMTAzLjh6IiBwLWlkPSIxOTc4IiBmaWxsPSIjRkZBNTAwIj48L3BhdGg+PC9zdmc+")
}

.ew-tree-table .ew-tree-icon-file:after {
    background-image: url("data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBzdGFuZGFsb25lPSJubyI/PjwhRE9DVFlQRSBzdmcgUFVCTElDICItLy9XM0MvL0RURCBTVkcgMS4xLy9FTiIgImh0dHA6Ly93d3cudzMub3JnL0dyYXBoaWNzL1NWRy8xLjEvRFREL3N2ZzExLmR0ZCI+PHN2ZyB0PSIxNTc0MDYyNTE1MDUxIiBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHAtaWQ9IjEzNTE4IiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayIgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0Ij48ZGVmcz48c3R5bGUgdHlwZT0idGV4dC9jc3MiPjwvc3R5bGU+PC9kZWZzPjxwYXRoIGQ9Ik03NDEuMyAxNjEuNmgtNDIuNGMtMTAuNSAwLTE5LjEgOC42LTE5LjEgMTkuMXM4LjYgMTkuMSAxOS4xIDE5LjFoNDIuNGM0MiAwIDc2LjIgMzQuMiA3Ni4yIDc2LjN2NDc3LjRjMCA0Mi4xLTM0LjMgNzYuMy03Ni40IDc2LjNIMjgyLjljLTQyLjEgMC03Ni4zLTM0LjItNzYuMy03Ni4zVjI3Ni4xYzAtNDIuMSAzNC4yLTc2LjMgNzYuMy03Ni4zaDQ0LjljMTAuNSAwIDE5LjEtOC42IDE5LjEtMTkuMXMtOC42LTE5LjEtMTkuMS0xOS4xaC00NC45Yy02My4xIDAtMTE0LjUgNTEuNC0xMTQuNSAxMTQuNXY0NzcuNGMwIDYzLjEgNTEuNCAxMTQuNSAxMTQuNSAxMTQuNWg0NTguM2M2My4xIDAgMTE0LjUtNTEuNCAxMTQuNS0xMTQuNVYyNzYuMWMtMC4xLTYzLjEtNTEuNC0xMTQuNS0xMTQuNC0xMTQuNXoiIHAtaWQ9IjEzNTE5IiBmaWxsPSIjRkZBNTAwIj48L3BhdGg+PHBhdGggZD0iTTY4MC42IDUwNS4zSDM0My40Yy0xMi4zIDAtMjIuMyA4LjYtMjIuMyAxOS4xczEwIDE5LjEgMjIuMyAxOS4xaDMzNy4yYzEyLjMgMCAyMi4zLTguNiAyMi4zLTE5LjEgMC0xMC42LTEwLTE5LjEtMjIuMy0xOS4xek00MzkuMyAyMTMuM2gxNDQuNmMxOSAwIDM0LjQtMTIuOCAzNC40LTI4LjZzLTE1LjQtMjguNi0zNC40LTI4LjZINDM5LjNjLTE5IDAtMzQuNCAxMi44LTM0LjQgMjguNi0wLjEgMTUuNyAxNS4zIDI4LjYgMzQuNCAyOC42ek02ODAuNiA2NThIMzQzLjRjLTEyLjMgMC0yMi4zIDguNS0yMi4zIDE5LjEgMCAxMC41IDEwIDE5LjEgMjIuMyAxOS4xaDMzNy4yYzEyLjMgMCAyMi4zLTguNiAyMi4zLTE5LjEgMC0xMC42LTEwLTE5LjEtMjIuMy0xOS4xek02ODAuNiAzNTIuNUgzNDMuNGMtMTIuMyAwLTIyLjMgOC42LTIyLjMgMTkuMXMxMCAxOS4xIDIyLjMgMTkuMWgzMzcuMmMxMi4zIDAgMjIuMy04LjYgMjIuMy0xOS4xIDAtMTAuNS0xMC0xOS4xLTIyLjMtMTkuMXoiIHAtaWQ9IjEzNTIwIiBmaWxsPSIjRkZBNTAwIj48L3BhdGg+PC9zdmc+")
}

/* 序号列调整 */
.ew-tree-table td[data-type="numbers"] {
    padding-left: 0;
    padding-right: 0;
    text-align: center;
}

/* 单元格内表单元素样式调整 */
.ew-tree-table .layui-form-switch {
    margin-top: 0;
}

.ew-tree-table .layui-form-radio {
    margin: 0;
}

/* checkbox和radio列调整 */
.ew-tree-table-checkbox + .layui-form-checkbox {
    padding: 0;
}

.ew-tree-table-checkbox + .layui-form-checkbox > .layui-icon {
    color: transparent;
    transition: background-color .1s linear;
}

.ew-tree-table-checkbox + .layui-form-checkbox.layui-form-checked > .layui-icon {
    color: #fff;
}

.ew-tree-table-radio + .layui-form-radio {
    padding: 0;
    height: 20px;
    line-height: 20px;
}

.ew-tree-table-radio + .layui-form-radio > i {
    margin: 0;
    height: 20px;
    font-size: 20px;
    line-height: 20px;
}

/* checkbox半选状态 */
.ew-tree-table .layui-form-checked.ew-form-indeterminate > .layui-icon:before {
    content: "";
    width: 9px;
    height: 2px;
    display: inline-block;
    background-color: #eee;
    vertical-align: middle;
}

.ew-tree-table .layui-form-checked.ew-form-indeterminate > .layui-icon {
    line-height: 14px;
}

/* 单元格编辑 */
.ew-tree-table .layui-table td[data-edit] {
    cursor: text;
}

.ew-tree-table .ew-tree-table-edit {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    border-radius: 0;
    box-shadow: 1px 1px 20px rgba(0, 0, 0, .15);
}

.ew-tree-table .ew-tree-table-edit:focus {
    border-color: #5FB878 !important;
}

.ew-tree-table .ew-tree-table-edit.layui-form-danger {
    border-color: #FF5722 !important;
}

/* 搜索数据隐藏行 */
.ew-tree-table tr.ew-tree-table-filter-hide {
    display: none !important;
}

/* 单元格超出隐藏 */
.ew-tree-table-td-single {
    position: relative;
}

.ew-tree-table-td-single > .ew-tree-tips {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.ew-tree-table-td-single > .ew-tree-tips-c {
    position: absolute;
    right: -10px;
    top: -6px;
    width: 24px;
    height: 24px;
    line-height: 24px;
    font-size: 18px;
    text-align: center;
    color: #fff;
    border-radius: 50%;
    background-color: #666;
    cursor: pointer;
    display: none;
}

.ew-tree-table table tr:first-child .ew-tree-table-td-single > .ew-tree-tips-c {
    top: 2px;
    bottom: auto;
    right: -12px;
}

.ew-tree-table-td-single.ew-tree-tips-open {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 5;
    background-color: #fff;
    min-height: 100%;
    box-sizing: border-box;
    box-shadow: 3px 3px 8px rgba(0, 0, 0, .15);
}

.ew-tree-table table thead .ew-tree-table-td-single.ew-tree-tips-open {
    background-color: #f2f2f2;
}

.ew-tree-table-td-single.ew-tree-tips-open.ew-show-left {
    right: 0;
    left: auto;
    box-shadow: -3px 3px 8px rgba(0, 0, 0, .15);
}

.ew-tree-table-td-single.ew-tree-tips-open.ew-show-bottom {
    bottom: 0;
    top: auto;
    box-shadow: 3px -3px 8px rgba(0, 0, 0, .15);
}

.ew-tree-table-td-single.ew-tree-tips-open.ew-show-left.ew-show-bottom {
    box-shadow: -3px -3px 8px rgba(0, 0, 0, .15);
}

.ew-tree-table-td-single.ew-tree-tips-open > .ew-tree-tips {
    padding: 9px 15px;
    overflow: auto;
    max-width: 280px;
    max-height: 100px;
    width: max-content;
    white-space: normal;
}

.ew-tree-table-td-single.ew-tree-tips-open > .ew-tree-tips-c {
    display: block;
}

.ew-tree-table-td-single.ew-tree-tips-open.ew-show-left > .ew-tree-tips-c {
    left: -10px;
    right: auto !important;
}

.ew-tree-table td > .layui-table-grid-down {
    bottom: 0;
    height: auto;
}

/* 辅助样式 */
.pd-tb-0 {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
}

.break-all {
    word-break: break-all !important;
}

/* 列宽拖拽调整 */
/*.ew-tree-table .ew-tb-resize {
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    width: 10px;
    cursor: col-resize;
}*/
