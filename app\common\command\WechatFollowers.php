<?php

namespace app\common\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\facade\Db;
use app\common\server\WeChatServer;
use EasyWeChat\Factory;

/**
 * 微信公众号关注用户同步命令
 * Class WechatFollowers
 * @package app\command
 */
class WechatFollowers extends Command
{
    protected function configure()
    {
        $this->setName('wechat:followers')
            ->setDescription('同步微信公众号关注用户列表');
    }

    protected function execute(Input $input, Output $output)
    {
        $output->writeln('开始同步微信公众号关注用户列表...');

        try {
            // 获取微信公众号配置
            $config = WeChatServer::getOaConfig();
            $app = Factory::officialAccount($config);

            // 第一步：将所有现有用户标记为"待验证"状态（subscribe=2）
            $output->writeln('标记现有用户为待验证状态...');
            $markCount = Db::name('wechat_followers')
                ->where('subscribe', 1)
                ->update(['subscribe' => 2, 'update_time' => time()]);
            $output->writeln("已标记 {$markCount} 个现有用户为待验证状态");

            $nextOpenId = null;
            $totalCount = 0;
            $newCount = 0;
            
            do {
                // 获取用户列表
                $result = $app->user->list($nextOpenId);
                
                if (!isset($result['data']['openid']) || empty($result['data']['openid'])) {
                    break;
                }
                
                $openIds = $result['data']['openid'];
                $totalCount += count($openIds);
                
                // 批量获取用户详细信息
                $userInfos = $app->user->select($openIds);
                
                if (isset($userInfos['user_info_list'])) {
                    foreach ($userInfos['user_info_list'] as $userInfo) {
                        $newCount += $this->saveFollower($userInfo);
                    }
                }
                
                // 设置下一次请求的起始openid
                $nextOpenId = $result['next_openid'] ?? null;
                
                $output->writeln("已处理 {$totalCount} 个用户");
                
            } while (!empty($nextOpenId));

            // 第三步：处理取消关注的用户（将仍然是待验证状态的用户标记为未关注）
            $output->writeln('处理取消关注的用户...');
            $unfollowCount = Db::name('wechat_followers')
                ->where('subscribe', 2)
                ->update(['subscribe' => 0, 'update_time' => time()]);
            $output->writeln("发现 {$unfollowCount} 个用户取消关注");

            Db::name('dev_crontab')->where('id',23)->update(['last_time'=>time()]);
            // 记录同步日志
            Db::name('log')->insert([
                'log' => json_encode([
                    'total_count' => $totalCount,
                    'new_count' => $newCount,
                    'unfollow_count' => $unfollowCount,
                    'mark_count' => $markCount ?? 0
                ]),
                'type' => 'wechat_followers_sync',
                'creat_time' =>date('Y-m-d H:i:s',time())
            ]);
            $output->writeln("同步完成！总计: {$totalCount} 个用户，新增: {$newCount} 个用户，取消关注: {$unfollowCount} 个用户");
            
        } catch (\Exception $e) {
            $output->writeln("同步失败：" . $e->getMessage());
            
            // 记录错误日志
            Db::name('log')->insert([
                'log' => '微信关注用户同步失败：' . $e->getMessage(),
                'type' => 'wechat_followers_sync',
                 'creat_time' =>date('Y-m-d H:i:s',time())
            ]);
        }
    }
    
    /**
     * 保存关注用户信息
     * @param array $userInfo
     * @return int 1=新增，0=已存在
     */
    private function saveFollower($userInfo)
    {
        // 检查用户是否已存在
        $exists = Db::name('wechat_followers')
            ->where('openid', $userInfo['openid'])
            ->find();

        $data = [
            'openid' => $userInfo['openid'],
            'unionid' => $userInfo['unionid'] ?? '',
            'nickname' => $userInfo['nickname'] ?? '',
            'sex' => $userInfo['sex'] ?? 0,
            'city' => $userInfo['city'] ?? '',
            'country' => $userInfo['country'] ?? '',
            'province' => $userInfo['province'] ?? '',
            'language' => $userInfo['language'] ?? '',
            'headimgurl' => $userInfo['headimgurl'] ?? '',
            'subscribe_time' => $userInfo['subscribe_time'] ?? 0,
            'subscribe' => 1, // 从微信API获取到的用户都是已关注状态
            'update_time' => time()
        ];

        if ($exists) {
            // 更新现有记录，确保标记为已关注
            Db::name('wechat_followers')
                ->where('id', $exists['id'])
                ->update($data);
            return 0;
        } else {
            // 插入新记录 - 检查表结构并处理字段不存在的情况
            try {
                $data['create_time'] = time();
                Db::name('wechat_followers')->insert($data);
                return 1;
            } catch (\Exception $e) {
                // 如果create_time字段不存在，则移除该字段再插入
                if (strpos($e->getMessage(), 'create_time') !== false) {
                    unset($data['create_time']);
                    Db::name('wechat_followers')->insert($data);
                    return 1;
                }
                throw $e;
            }
        }
    }
}
