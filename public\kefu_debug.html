<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>客服WebSocket诊断工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .form-group {
            margin: 15px 0;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, textarea, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .log {
            height: 400px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            background-color: #f8f9fa;
            font-family: monospace;
            font-size: 12px;
        }
        .log-entry {
            margin: 2px 0;
            padding: 2px;
        }
        .log-entry.error {
            color: #dc3545;
        }
        .log-entry.success {
            color: #28a745;
        }
        .log-entry.info {
            color: #17a2b8;
        }
        .diagnostic-item {
            padding: 10px;
            margin: 5px 0;
            border-left: 4px solid #ddd;
            background-color: #f8f9fa;
        }
        .diagnostic-item.pass {
            border-left-color: #28a745;
            background-color: #d4edda;
        }
        .diagnostic-item.fail {
            border-left-color: #dc3545;
            background-color: #f8d7da;
        }
        .diagnostic-item.warning {
            border-left-color: #ffc107;
            background-color: #fff3cd;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>客服WebSocket诊断工具</h1>
        <p>此工具用于诊断客服无法发送消息的问题</p>
        
        <div class="form-group">
            <label for="token">客服Token:</label>
            <input type="text" id="token" placeholder="请输入客服token">
        </div>
        
        <div class="form-group">
            <label for="shopId">商家ID:</label>
            <input type="number" id="shopId" placeholder="请输入商家ID" value="86">
        </div>
        
        <div class="form-group">
            <button onclick="runDiagnostic()">开始诊断</button>
            <button onclick="testConnection()">测试连接</button>
            <button onclick="clearLog()">清空日志</button>
        </div>
    </div>

    <div class="container">
        <h2>诊断结果</h2>
        <div id="diagnosticResults"></div>
    </div>

    <div class="container">
        <h2>详细日志</h2>
        <div id="log" class="log"></div>
    </div>

    <script>
        let ws = null;
        
        function log(message, type = 'info') {
            const logEl = document.getElementById('log');
            const entry = document.createElement('div');
            entry.className = 'log-entry ' + type;
            entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logEl.appendChild(entry);
            logEl.scrollTop = logEl.scrollHeight;
        }
        
        function addDiagnosticResult(title, status, message) {
            const resultsEl = document.getElementById('diagnosticResults');
            const item = document.createElement('div');
            item.className = 'diagnostic-item ' + status;
            item.innerHTML = `<strong>${title}</strong><br>${message}`;
            resultsEl.appendChild(item);
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
            document.getElementById('diagnosticResults').innerHTML = '';
        }
        
        async function runDiagnostic() {
            clearLog();
            log('开始运行诊断...', 'info');
            
            const token = document.getElementById('token').value.trim();
            const shopId = parseInt(document.getElementById('shopId').value) || 0;
            
            if (!token) {
                addDiagnosticResult('参数检查', 'fail', '客服token不能为空');
                return;
            }
            
            addDiagnosticResult('参数检查', 'pass', `Token: ${token.substring(0, 10)}..., Shop ID: ${shopId}`);
            
            // 检查1: 验证客服token
            try {
                log('检查客服token有效性...', 'info');
                const response = await fetch('/kefuapi/chat/kefuInfo', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'token': token
                    }
                });
                
                const result = await response.json();
                if (result.code === 1) {
                    addDiagnosticResult('客服Token验证', 'pass', `客服信息: ${result.data.nickname} (ID: ${result.data.id}, Shop ID: ${result.data.shop_id})`);
                    log(`客服验证成功: ${result.data.nickname}, Shop ID: ${result.data.shop_id}`, 'success');

                    // 如果客服的shop_id与输入的不一致，给出警告
                    if (result.data.shop_id != shopId) {
                        addDiagnosticResult('Shop ID检查', 'warning', `客服实际Shop ID (${result.data.shop_id}) 与输入的Shop ID (${shopId}) 不一致`);
                        log(`Shop ID不一致警告: 客服=${result.data.shop_id}, 输入=${shopId}`, 'warning');
                    }
                } else {
                    addDiagnosticResult('客服Token验证', 'fail', `Token验证失败: ${result.msg}`);
                    log(`客服验证失败: ${result.msg}`, 'error');
                    return;
                }
            } catch (error) {
                addDiagnosticResult('客服Token验证', 'fail', `请求失败: ${error.message}`);
                log(`客服验证请求失败: ${error.message}`, 'error');
                return;
            }
            
            // 检查2: 验证客服配置
            try {
                log('检查客服配置...', 'info');
                const response = await fetch('/api/chat/checkConfig', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        shop_id: shopId
                    })
                });
                
                const result = await response.json();
                if (result.code === 1) {
                    addDiagnosticResult('客服配置检查', 'pass', '客服配置正常，在线客服已开启');
                    log('客服配置检查通过', 'success');
                } else {
                    addDiagnosticResult('客服配置检查', 'fail', `配置错误: ${result.msg}`);
                    log(`客服配置检查失败: ${result.msg}`, 'error');
                }
            } catch (error) {
                addDiagnosticResult('客服配置检查', 'warning', `无法检查配置: ${error.message}`);
                log(`客服配置检查请求失败: ${error.message}`, 'error');
            }
            
            // 检查3: WebSocket连接测试
            log('开始WebSocket连接测试...', 'info');
            testWebSocketConnection(token, shopId);
        }
        
        function testWebSocketConnection(token, shopId) {
            if (ws) {
                ws.close();
            }
            
            const url = `wss://kefu.huohanghang.cn/?type=kefu&token=${token}&client=2&shop_id=${shopId}`;
            log(`尝试连接: ${url}`, 'info');
            
            ws = new WebSocket(url);
            
            const timeout = setTimeout(() => {
                addDiagnosticResult('WebSocket连接', 'fail', '连接超时（10秒）');
                log('WebSocket连接超时', 'error');
                if (ws) {
                    ws.close();
                }
            }, 10000);
            
            ws.onopen = () => {
                clearTimeout(timeout);
                addDiagnosticResult('WebSocket连接', 'pass', 'WebSocket连接成功建立');
                log('WebSocket连接成功', 'success');
                
                // 测试发送消息
                setTimeout(() => {
                    testSendMessage();
                }, 1000);
            };
            
            ws.onmessage = (event) => {
                try {
                    const data = JSON.parse(event.data);
                    log(`收到消息: ${JSON.stringify(data)}`, 'info');
                    
                    if (data.event === 'login') {
                        if (data.data && data.data.code === 1) {
                            addDiagnosticResult('WebSocket登录', 'pass', '客服登录成功');
                            log('客服WebSocket登录成功', 'success');
                        } else {
                            addDiagnosticResult('WebSocket登录', 'fail', `登录失败: ${data.data.msg}`);
                            log(`客服WebSocket登录失败: ${data.data.msg}`, 'error');
                        }
                    } else if (data.event === 'error') {
                        addDiagnosticResult('WebSocket错误', 'fail', `服务器错误: ${data.data.msg}`);
                        log(`WebSocket错误: ${data.data.msg}`, 'error');
                    } else if (data.event === 'chat') {
                        addDiagnosticResult('消息发送测试', 'pass', '消息发送成功，收到确认');
                        log('消息发送测试成功', 'success');
                    }
                } catch (error) {
                    log(`解析消息失败: ${event.data}`, 'error');
                }
            };
            
            ws.onerror = (error) => {
                clearTimeout(timeout);
                addDiagnosticResult('WebSocket连接', 'fail', `连接错误: ${error.message || '未知错误'}`);
                log(`WebSocket连接错误: ${error.message || '未知错误'}`, 'error');
            };
            
            ws.onclose = (event) => {
                clearTimeout(timeout);
                log(`WebSocket连接关闭: code=${event.code}, reason=${event.reason}`, 'info');
                if (event.code !== 1000) {
                    addDiagnosticResult('WebSocket连接', 'fail', `连接异常关闭: ${event.code} - ${event.reason}`);
                }
            };
        }
        
        function testSendMessage() {
            if (!ws || ws.readyState !== WebSocket.OPEN) {
                addDiagnosticResult('消息发送测试', 'fail', 'WebSocket连接未建立');
                return;
            }
            
            log('测试发送消息...', 'info');
            
            const testMessage = {
                event: 'chat',
                data: {
                    to_id: 1, // 测试用户ID
                    to_type: 'user',
                    msg: '这是一条测试消息',
                    msg_type: 1,
                    chat_type: 'kefu_chat'
                }
            };
            
            try {
                ws.send(JSON.stringify(testMessage));
                log(`发送测试消息: ${JSON.stringify(testMessage)}`, 'info');
                
                // 等待响应
                setTimeout(() => {
                    addDiagnosticResult('消息发送测试', 'warning', '消息已发送，但未收到确认（可能接收者不在线）');
                }, 3000);
            } catch (error) {
                addDiagnosticResult('消息发送测试', 'fail', `发送失败: ${error.message}`);
                log(`消息发送失败: ${error.message}`, 'error');
            }
        }
        
        function testConnection() {
            const token = document.getElementById('token').value.trim();
            const shopId = parseInt(document.getElementById('shopId').value) || 0;
            
            if (!token) {
                alert('请输入客服token');
                return;
            }
            
            log('开始简单连接测试...', 'info');
            testWebSocketConnection(token, shopId);
        }
        
        // 页面卸载时关闭连接
        window.addEventListener('beforeunload', () => {
            if (ws) {
                ws.close();
            }
        });
    </script>
</body>
</html>
