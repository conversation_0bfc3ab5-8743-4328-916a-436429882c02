<?php
namespace app\api\logic;

use app\common\basics\Logic;
use Exception;
use setasign\Fpdi\Fpdi;
use think\facade\Log;
use setasign\Fpdf\Fpdf;

/**
 * 使用FPDI库处理PDF文件
 * 用于在PDF文件中添加签名和日期
 * Class FpdiPdfProcessor
 * @package app\api\logic
 */
class FpdiPdfProcessor extends Logic
{
    /**
     * @var string PDF文件路径
     */
    protected $pdfPath;

    /**
     * @var string 签名图片路径
     */
    protected $signaturePath;

    /**
     * @var string 输出PDF路径
     */
    protected $outputPath;

    /**
     * @var array 签名位置坐标
     */
    protected $signaturePosition = [
        'x' => 100,  // 默认X坐标
        'y' => 80,  // 默认Y坐标
        'width' => 60, // 默认宽度
        'height' => 50 // 默认高度
    ];

    /**
     * @var array 日期位置坐标
     */
    protected $datePosition = [
        'x' => 400,  // 默认X坐标
        'y' => 720,  // 默认Y坐标
    ];

    /**
     * 构造函数
     * @param string $pdfPath PDF文件路径
     */
    public function __construct($pdfPath = '')
    {
        if (!empty($pdfPath)) {
            $this->setPdfPath($pdfPath);
        }
    }

    /**
     * 设置PDF文件路径
     * @param string $pdfPath
     * @return $this
     * @throws Exception
     */
    public function setPdfPath($pdfPath)
    {
        if (!file_exists($pdfPath)) {
            throw new Exception('PDF文件不存在: ' . $pdfPath);
        }
        $this->pdfPath = $pdfPath;
        return $this;
    }

    /**
     * 设置签名图片路径
     * @param string $signaturePath
     * @return $this
     * @throws Exception
     */
    public function setSignaturePath($signaturePath)
    {
        if (!file_exists($signaturePath)) {
            throw new Exception('签名图片不存在: ' . $signaturePath);
        }
        $this->signaturePath = $signaturePath;
        return $this;
    }

    /**
     * 设置输出PDF路径
     * @param string $outputPath
     * @return $this
     */
    public function setOutputPath($outputPath)
    {
        $this->outputPath = $outputPath;
        return $this;
    }

    /**
     * 设置签名位置
     * @param float $x X坐标
     * @param float $y Y坐标
     * @param float $width 宽度
     * @param float $height 高度
     * @return $this
     */
    public function setSignaturePosition($x, $y, $width = null, $height = null)
    {
        $this->signaturePosition['x'] = $x;
        $this->signaturePosition['y'] = $y;
        if ($width !== null) {
            $this->signaturePosition['width'] = $width;
        }
        if ($height !== null) {
            $this->signaturePosition['height'] = $height;
        }
        return $this;
    }

    /**
     * 设置日期位置
     * @param float $x X坐标
     * @param float $y Y坐标
     * @return $this
     */
    public function setDatePosition($x, $y)
    {
        $this->datePosition['x'] = $x;
        $this->datePosition['y'] = $y;
        return $this;
    }

    /**
     * 处理PDF文件，添加签名和日期
     * @param int $pageNumber 页码，从1开始
     * @param string $dateFormat 日期格式，默认为Y-m-d
     * @return string 处理后的PDF文件路径
     * @throws Exception
     */
    public function process($pageNumber = 1, $dateFormat = 'Y-m-d')
    {
        // 检查必要参数
        if (empty($this->pdfPath)) {
            throw new Exception('未设置PDF文件路径');
        }

        if (empty($this->signaturePath)) {
            throw new Exception('未设置签名图片路径');
        }

        if (empty($this->outputPath)) {
            // 如果未设置输出路径，则生成一个临时路径
            $pathInfo = pathinfo($this->pdfPath);
            $this->outputPath = $pathInfo['dirname'] . '/' . $pathInfo['filename'] . '_signed.' . $pathInfo['extension'];
        }

        try {
            // 记录处理开始
            Log::info('开始处理PDF文件: ' . $this->pdfPath);
            Log::info('签名图片路径: ' . $this->signaturePath);
            Log::info('输出PDF路径: ' . $this->outputPath);
            Log::info('签名位置: ' . json_encode($this->signaturePosition));
            Log::info('日期位置: ' . json_encode($this->datePosition));

            // 检查签名图片是否存在
            if (!file_exists($this->signaturePath)) {
                Log::error('签名图片文件不存在: ' . $this->signaturePath);
                throw new Exception('签名图片文件不存在: ' . $this->signaturePath);
            }

            // 检查图片文件大小
            $fileSize = filesize($this->signaturePath);
            Log::info('签名图片文件大小: ' . $fileSize . ' 字节');
            if ($fileSize <= 0) {
                Log::error('签名图片文件为空: ' . $this->signaturePath . ', 大小: ' . $fileSize);
                throw new Exception('签名图片文件为空: ' . $this->signaturePath . ', 大小: ' . $fileSize);
            }

            // 获取图片信息
            $imageInfo = getimagesize($this->signaturePath);
            if (!$imageInfo) {
                Log::error('无法获取图片信息: ' . $this->signaturePath);
                throw new Exception('无法获取图片信息: ' . $this->signaturePath);
            }
            Log::info('签名图片信息: ' . json_encode($imageInfo));

            // 创建FPDI实例
            Log::info('创建FPDI实例');
            $pdf = new Fpdi();

            // 设置一些基本属性
            $pdf->SetAutoPageBreak(false);
            $pdf->SetCompression(true);

            // 获取页数
            Log::info('获取PDF页数');
            $pageCount = $pdf->setSourceFile($this->pdfPath);
            Log::info('PDF页数: ' . $pageCount);

            // 检查页码是否有效
            if ($pageNumber < 1 || $pageNumber > $pageCount) {
                Log::error('无效的页码: ' . $pageNumber . ', 总页数: ' . $pageCount);
                throw new Exception('无效的页码: ' . $pageNumber . ', 总页数: ' . $pageCount);
            }

            // 循环处理每一页
            for ($i = 1; $i <= $pageCount; $i++) {
                Log::info('处理第 ' . $i . ' 页');
                // 导入页面
                $templateId = $pdf->importPage($i);
                $size = $pdf->getTemplateSize($templateId);
                $orientation = ($size['width'] > $size['height']) ? 'L' : 'P';
                Log::info('页面尺寸: ' . json_encode($size) . ', 方向: ' . $orientation);

                // 添加页面
                $pdf->AddPage($orientation, [$size['width'], $size['height']]);
                $pdf->useTemplate($templateId);

                // 只在指定页面添加签名和日期
                if ($i == $pageNumber) {
                    Log::info('在第 ' . $i . ' 页添加签名和日期');

                    // 记录页面尺寸信息，便于调试
                    $pageWidth = $size['width'];
                    $pageHeight = $size['height'];
                    Log::info('页面尺寸 - 宽度: ' . $pageWidth . ', 高度: ' . $pageHeight);

                    // 直接使用原始坐标，不进行转换
                    // 这样与PdfSignatureProcessor.php保持一致
                    $signatureX = $this->signaturePosition['x'];
                    $signatureY = $this->signaturePosition['y'];

                    // 直接使用原始日期坐标，不进行转换
                    $dateX = $this->datePosition['x'];
                    $dateY = $this->datePosition['y'];

                    Log::info('调整后的签名位置: X=' . $signatureX . ', Y=' . $signatureY);
                    Log::info('调整后的日期位置: X=' . $dateX . ', Y=' . $dateY);

                    // 添加签名图片，使用调整后的坐标
                    try {
                        // 检查图片类型并处理透明度
                        $imageType = $imageInfo[2]; // 1=GIF, 2=JPG, 3=PNG, etc.

                        if ($imageType == 3) { // PNG
                            // 对于PNG图片，使用特殊处理以保持透明度
                            Log::info('使用PNG特殊处理方式添加签名图片');
                            $pdf->Image(
                                $this->signaturePath,
                                $signatureX,
                                $signatureY,
                                $this->signaturePosition['width'],
                                $this->signaturePosition['height'],
                                'PNG'
                            );
                        } else {
                            // 对于其他类型图片，使用标准方式
                            Log::info('使用标准方式添加签名图片');
                            $pdf->Image(
                                $this->signaturePath,
                                $signatureX,
                                $signatureY,
                                $this->signaturePosition['width'],
                                $this->signaturePosition['height']
                            );
                        }

                        Log::info('签名图片添加成功');
                    } catch (Exception $e) {
                        Log::error('添加签名图片时出错: ' . $e->getMessage());
                        // 继续执行，尝试添加日期
                    }

                    // 添加日期，使用调整后的坐标
                    try {
                        // 设置字体和大小
                        $pdf->SetFont('Arial', 'B', 12); // 使用粗体和更大的字号
                        $pdf->SetTextColor(0, 0, 0); // 黑色文本

                        // 设置位置并添加日期
                        $pdf->SetXY($dateX, $dateY);
                        $currentDate = date($dateFormat);
                        $pdf->Cell(0, 10, $currentDate, 0, 0, 'L');

                        Log::info('日期添加成功: ' . $currentDate);
                    } catch (Exception $e) {
                        Log::error('添加日期时出错: ' . $e->getMessage());
                    }
                }
            }

            // 保存PDF
            Log::info('保存PDF到: ' . $this->outputPath);
            $pdf->Output('F', $this->outputPath);

            // 检查输出文件是否存在
            if (!file_exists($this->outputPath)) {
                Log::error('输出PDF文件不存在: ' . $this->outputPath);
                throw new Exception('输出PDF文件不存在: ' . $this->outputPath);
            }

            // 检查输出文件大小
            $outputFileSize = filesize($this->outputPath);
            Log::info('输出PDF文件大小: ' . $outputFileSize . ' 字节');
            if ($outputFileSize <= 0) {
                Log::error('输出PDF文件为空: ' . $this->outputPath . ', 大小: ' . $outputFileSize);
                throw new Exception('输出PDF文件为空: ' . $this->outputPath . ', 大小: ' . $outputFileSize);
            }

            // 确保文件上传到OSS（如果配置了OSS）
            $engine = \app\common\server\ConfigServer::get('storage', 'default', 'local');
            if ($engine != 'local') {
                Log::info('检测到非本地存储配置，尝试上传到OSS: ' . $engine);
                try {
                    // 获取存储配置
                    $config = [
                        'default' => $engine,
                        'engine' => \app\common\server\ConfigServer::get('storage_engine')
                    ];

                    // 创建存储驱动
                    $StorageDriver = new \app\common\server\storage\Driver($config);

                    // 设置文件
                    $StorageDriver->setUploadFileByReal($this->outputPath);

                    // 上传到OSS
                    $savePath = 'uploads/pdf';
                    if ($StorageDriver->upload($savePath)) {
                        $fileName = $StorageDriver->getFileName();
                        $ossPath = $savePath . '/' . $fileName;
                        Log::info('文件已成功上传到OSS: ' . $ossPath);

                        // 更新输出路径为OSS路径
                        $this->outputPath = $ossPath;
                    } else {
                        Log::error('上传到OSS失败: ' . $StorageDriver->getError());
                    }
                } catch (Exception $e) {
                    Log::error('上传到OSS过程中出错: ' . $e->getMessage());
                    // 继续使用本地文件路径
                }
            }

            Log::info('PDF处理完成');

            return $this->outputPath;
        } catch (Exception $e) {
            Log::error('处理PDF文件时出错: ' . $e->getMessage());
            Log::error('错误位置: ' . $e->getFile() . ':' . $e->getLine());
            Log::error('错误堆栈: ' . $e->getTraceAsString());
            throw new Exception('处理PDF文件时出错: ' . $e->getMessage());
        }
    }
}
