/**
 * 客服专用WebSocket管理器
 * 确保客服发送消息时包含正确的chat_type参数
 */
class KefuWebSocket {
    constructor(options = {}) {
        this.options = {
            url: options.url || 'wss://kefu.huohanghang.cn',
            token: options.token || '',
            shop_id: options.shop_id || 0,
            client: options.client || 2, // 客服端默认client=2
            reconnectInterval: options.reconnectInterval || 5000,
            maxReconnectAttempts: options.maxReconnectAttempts || 5,
            heartbeatInterval: options.heartbeatInterval || 30000,
            ...options
        };

        this.ws = null;
        this.isConnected = false;
        this.reconnectAttempts = 0;
        this.heartbeatTimer = null;
        this.messageQueue = [];
        this.eventHandlers = {};

        this.init();
    }
    
    /**
     * 初始化WebSocket连接
     */
    init() {
        this.connect();
    }
    
    /**
     * 建立WebSocket连接
     */
    connect() {
        try {
            // 客服使用type=kefu连接，保持向后兼容
            const url = `${this.options.url}?type=kefu&token=${this.options.token}&client=${this.options.client}&shop_id=${this.options.shop_id}`;
            
            console.log('客服WebSocket连接URL:', url);
            
            this.ws = new WebSocket(url);
            
            this.ws.onopen = () => {
                this.onOpen();
            };
            
            this.ws.onmessage = (event) => {
                this.onMessage(event.data);
            };
            
            this.ws.onclose = () => {
                this.onClose();
            };
            
            this.ws.onerror = (error) => {
                this.onError(error);
            };
        } catch (error) {
            console.error('创建客服WebSocket连接失败:', error);
            this.handleReconnect();
        }
    }
    
    /**
     * 连接打开事件
     */
    onOpen() {
        console.log('客服WebSocket连接已建立');
        this.isConnected = true;
        this.reconnectAttempts = 0;
        
        // 发送队列中的消息
        this.flushMessageQueue();
        
        // 开始心跳
        this.startHeartbeat();
        
        // 触发连接成功事件
        this.emit('connected');
    }
    
    /**
     * 接收消息事件
     */
    onMessage(data) {
        try {
            const message = JSON.parse(data);
            console.log('客服收到消息:', message);
            
            // 处理不同类型的消息
            switch (message.event) {
                case 'login':
                    this.emit('login', message.data);
                    break;
                case 'chat':
                    // 客服聊天消息，自动添加chat_type标识
                    if (!message.data.chat_type) {
                        message.data.chat_type = 'kefu_chat';
                    }
                    this.emit('message', message.data);
                    this.emit('chat', message.data);
                    break;
                case 'ping':
                    this.sendPong();
                    break;
                case 'pong':
                    // 心跳响应
                    break;
                case 'error':
                    this.emit('error', message.data);
                    break;
                case 'transfer':
                    this.emit('transfer', message.data);
                    break;
                default:
                    this.emit('unknown', message);
            }
        } catch (error) {
            console.error('解析消息失败:', error);
        }
    }
    
    /**
     * 连接关闭事件
     */
    onClose() {
        console.log('客服WebSocket连接已关闭');
        this.isConnected = false;
        this.stopHeartbeat();
        this.emit('disconnected');
        this.handleReconnect();
    }
    
    /**
     * 连接错误事件
     */
    onError(error) {
        console.error('客服WebSocket连接错误:', error);
        this.emit('error', error);
    }
    
    /**
     * 处理重连
     */
    handleReconnect() {
        if (this.reconnectAttempts < this.options.maxReconnectAttempts) {
            this.reconnectAttempts++;
            console.log(`客服WebSocket重连尝试 ${this.reconnectAttempts}/${this.options.maxReconnectAttempts}`);
            setTimeout(() => {
                this.connect();
            }, this.options.reconnectInterval);
        } else {
            console.error('客服WebSocket重连失败，已达到最大重连次数');
            this.emit('reconnect_failed');
        }
    }
    
    /**
     * 发送消息
     */
    send(event, data) {
        const message = {
            event: event,
            data: data
        };
        
        if (this.isConnected && this.ws.readyState === WebSocket.OPEN) {
            try {
                const messageStr = JSON.stringify(message);
                this.ws.send(messageStr);
                console.log('客服消息发送成功:', message);
            } catch (error) {
                console.error('发送消息失败:', error);
                this.messageQueue.push(message);
            }
        } else {
            // 连接未建立，加入队列
            console.log('连接未建立，消息加入队列:', message);
            this.messageQueue.push(message);
        }
    }
    
    /**
     * 发送客服聊天消息
     */
    sendMessage(toId, toType, message, messageType = 1, voiceDuration = 0) {
        this.send('chat', {
            to_id: toId,
            to_type: toType,
            msg: message,
            msg_type: messageType,
            voice_duration: voiceDuration,
            chat_type: 'kefu_chat' // 明确标识为客服聊天
        });
    }
    
    /**
     * 发送心跳包
     */
    sendPing() {
        this.send('ping', {
            timestamp: Date.now()
        });
    }
    
    /**
     * 发送心跳响应
     */
    sendPong() {
        this.send('pong', {
            timestamp: Date.now()
        });
    }
    
    /**
     * 转接用户
     */
    transferUser(userId, kefuId) {
        this.send('transfer', {
            user_id: userId,
            kefu_id: kefuId
        });
    }
    
    /**
     * 开始心跳
     */
    startHeartbeat() {
        this.stopHeartbeat();
        this.heartbeatTimer = setInterval(() => {
            this.sendPing();
        }, this.options.heartbeatInterval);
    }
    
    /**
     * 停止心跳
     */
    stopHeartbeat() {
        if (this.heartbeatTimer) {
            clearInterval(this.heartbeatTimer);
            this.heartbeatTimer = null;
        }
    }
    
    /**
     * 发送队列中的消息
     */
    flushMessageQueue() {
        while (this.messageQueue.length > 0) {
            const message = this.messageQueue.shift();
            this.send(message.event, message.data);
        }
    }
    
    /**
     * 事件监听
     */
    on(event, callback) {
        if (!this.eventHandlers[event]) {
            this.eventHandlers[event] = [];
        }
        this.eventHandlers[event].push(callback);
    }
    
    /**
     * 移除事件监听
     */
    off(event, callback) {
        if (this.eventHandlers[event]) {
            const index = this.eventHandlers[event].indexOf(callback);
            if (index > -1) {
                this.eventHandlers[event].splice(index, 1);
            }
        }
    }
    
    /**
     * 触发事件
     */
    emit(event, data) {
        if (this.eventHandlers[event]) {
            this.eventHandlers[event].forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error('事件处理器执行错误:', error);
                }
            });
        }
    }
    
    /**
     * 关闭连接
     */
    close() {
        this.stopHeartbeat();
        if (this.ws) {
            this.ws.close();
        }
        this.isConnected = false;
    }
    
    /**
     * 获取连接状态
     */
    getConnectionStatus() {
        return {
            isConnected: this.isConnected,
            reconnectAttempts: this.reconnectAttempts,
            messageQueueLength: this.messageQueue.length,
            readyState: this.ws ? this.ws.readyState : -1
        };
    }
}

// 导出类
if (typeof window !== 'undefined') {
    window.KefuWebSocket = KefuWebSocket;
}
