exports.ids = [40,7];
exports.modules = {

/***/ 144:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/count-down.vue?vue&type=template&id=2fbaab86&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return (_vm.time >= 0)?_c('div',[_c('client-only',[(_vm.isSlot)?_vm._t("default"):_c('span',[_vm._v(_vm._s(_vm.formateTime))])],2)],1):_vm._e()}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/count-down.vue?vue&type=template&id=2fbaab86&

// CONCATENATED MODULE: ./utils/parseTime.js
const SECOND = 1000;
const MINUTE = 60 * SECOND;
const HOUR = 60 * MINUTE;
const DAY = 24 * HOUR;
function parseTimeData(time) {
  const days = Math.floor(time / DAY);
  const hours = sliceTwo(Math.floor(time % DAY / HOUR));
  const minutes = sliceTwo(Math.floor(time % HOUR / MINUTE));
  const seconds = sliceTwo(Math.floor(time % MINUTE / SECOND));
  return {
    days: days,
    hours: hours,
    minutes: minutes,
    seconds: seconds
  };
}

function sliceTwo(str) {
  return (0 + str.toString()).slice(-2);
}

function parseFormat(format, timeData) {
  let days = timeData.days;
  let hours = timeData.hours,
      minutes = timeData.minutes,
      seconds = timeData.seconds;

  if (format.indexOf('dd') !== -1) {
    format = format.replace('dd', days);
  }

  if (format.indexOf('hh') !== -1) {
    format = format.replace('hh', sliceTwo(hours));
  }

  if (format.indexOf('mm') !== -1) {
    format = format.replace('mm', sliceTwo(minutes));
  }

  if (format.indexOf('ss') !== -1) {
    format = format.replace('ss', sliceTwo(seconds));
  }

  return format;
}
// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/count-down.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//

/* harmony default export */ var count_downvue_type_script_lang_js_ = ({
  components: {},
  props: {
    isSlot: {
      type: Boolean,
      default: false
    },
    time: {
      type: Number,
      default: 0
    },
    format: {
      type: String,
      default: 'hh:mm:ss'
    },
    autoStart: {
      type: Boolean,
      default: true
    }
  },
  watch: {
    time: {
      immediate: true,

      handler(value) {
        if (value) {
          this.reset();
        }
      }

    }
  },

  data() {
    return {
      timeObj: {},
      formateTime: 0
    };
  },

  created() {},

  computed: {},
  methods: {
    createTimer(fn) {
      return setTimeout(fn, 100);
    },

    isSameSecond(time1, time2) {
      return Math.floor(time1) === Math.floor(time2);
    },

    start() {
      if (this.counting) {
        return;
      }

      this.counting = true;
      this.endTime = Date.now() + this.remain * 1000;
      this.setTimer();
    },

    setTimer() {
      this.tid = this.createTimer(() => {
        let remain = this.getRemain();

        if (!this.isSameSecond(remain, this.remain) || remain === 0) {
          this.setRemain(remain);
        }

        if (this.remain !== 0) {
          this.setTimer();
        }
      });
    },

    getRemain() {
      return Math.max(this.endTime - Date.now(), 0);
    },

    pause() {
      this.counting = false;
      clearTimeout(this.tid);
    },

    reset() {
      this.pause();
      this.remain = this.time;
      this.setRemain(this.remain);

      if (this.autoStart) {
        this.start();
      }
    },

    setRemain(remain) {
      const {
        format
      } = this;
      this.remain = remain;
      const timeData = parseTimeData(remain);
      this.formateTime = parseFormat(format, timeData);
      this.$emit('change', timeData);

      if (remain === 0) {
        this.pause();
        this.$emit('finish');
      }
    }

  }
});
// CONCATENATED MODULE: ./components/count-down.vue?vue&type=script&lang=js&
 /* harmony default export */ var components_count_downvue_type_script_lang_js_ = (count_downvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(1);

// CONCATENATED MODULE: ./components/count-down.vue



function injectStyles (context) {
  
  
}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  components_count_downvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  null,
  "4090b4e2"
  
)

/* harmony default export */ var count_down = __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 149:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "d", function() { return client; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "e", function() { return loginType; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "c", function() { return SMSType; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "b", function() { return FieldType; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "a", function() { return AfterSaleType; });
const client = 5;
const loginType = {
  SMS: 0,
  ACCOUNT: 1
}; // 短信发送

const SMSType = {
  // 注册
  REGISTER: 'ZCYZ',
  // 找回密码
  FINDPWD: 'ZHMM',
  // 登陆
  LOGIN: 'YZMDL',
  // 商家申请入驻
  SJSQYZ: 'SJSQYZ',
  // 更换手机号
  CHANGE_MOBILE: 'BGSJHM',
  // 绑定手机号
  BIND: 'BDSJHM'
};
const FieldType = {
  NONE: '',
  SEX: 'sex',
  NICKNAME: 'nickname',
  AVATAR: 'avatar',
  MOBILE: 'mobile'
}; // 售后状态

const AfterSaleType = {
  // 售后申请 
  NORMAL: 'normal',
  // 处理中
  HANDLING: 'apply',
  // 已处理
  FINISH: 'finish'
};

/***/ }),

/***/ 224:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(282);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(4).default
module.exports.__inject__ = function (context) {
  add("4e76d684", content, true, context)
};

/***/ }),

/***/ 281:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_sass_resources_loader_lib_loader_js_ref_7_oneOf_1_4_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_style_index_0_lang_scss_scope_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(224);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_sass_resources_loader_lib_loader_js_ref_7_oneOf_1_4_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_style_index_0_lang_scss_scope_true___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_sass_resources_loader_lib_loader_js_ref_7_oneOf_1_4_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_style_index_0_lang_scss_scope_true___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_sass_resources_loader_lib_loader_js_ref_7_oneOf_1_4_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_style_index_0_lang_scss_scope_true___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_sass_resources_loader_lib_loader_js_ref_7_oneOf_1_4_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_style_index_0_lang_scss_scope_true___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 282:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(3);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".store{width:100%}.store .main{width:660px;margin:0 auto;padding-bottom:52px}.store .main .title{padding:16px 0;color:#101010;font-size:18px;text-align:center}.store .main ::v-deep .el-input__inner{border-radius:0!important}.store .main .avatar-uploader .el-upload{width:100px;height:100px;border:1px solid #d9d9d9;cursor:pointer;position:relative;line-height:0;padding:20px 0;color:#101010;overflow:hidden;border-radius:0}.store .main .avatar-uploader .el-upload:hover{border-color:#ff2c3c}.store .main .avatar-uploader-icon{font-size:28px;color:#8c939d;text-align:center}.store .main .avatar{width:100px;height:100px;display:block}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 348:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/store_settled/index.vue?vue&type=template&id=208c76e4&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',[_vm._ssrNode("<div class=\"m-t-20\">","</div>",[_c('el-breadcrumb',{attrs:{"separator":"/"}},[_c('el-breadcrumb-item',{attrs:{"to":{ path: '/' }}},[_vm._v("首页")]),_vm._v(" "),_c('el-breadcrumb-item',[_vm._v("商家入驻")])],1)],1),_vm._ssrNode(" "),_vm._ssrNode("<div class=\"store bg-white m-t-16\">","</div>",[_vm._ssrNode("<div class=\"main\">","</div>",[_vm._ssrNode("<div class=\"title\">入驻申请</div> "),_c('el-form',{ref:"form",staticClass:"demo-form",attrs:{"model":_vm.form,"rules":_vm.rules,"label-width":"110px"}},[_c('el-form-item',{attrs:{"label":"商家名称:","prop":"name"}},[_c('el-input',{attrs:{"placeholder":"请输入商家名称"},model:{value:(_vm.form.name),callback:function ($$v) {_vm.$set(_vm.form, "name", $$v)},expression:"form.name"}})],1),_vm._v(" "),_c('el-form-item',{attrs:{"label":"主营类目:","prop":"cid"}},[_c('el-select',{staticStyle:{"width":"100%"},attrs:{"placeholder":"请选择"},model:{value:(_vm.form.cid),callback:function ($$v) {_vm.$set(_vm.form, "cid", $$v)},expression:"form.cid"}},_vm._l((_vm.category),function(item){return _c('el-option',{key:item.id,attrs:{"label":item.name,"value":item.id}},[_vm._v("\n                            "+_vm._s(item.name)+"\n                        ")])}),1)],1),_vm._v(" "),_c('el-form-item',{attrs:{"label":"联系人姓名:","prop":"nickname"}},[_c('el-input',{attrs:{"placeholder":"请输入联系人姓名"},model:{value:(_vm.form.nickname),callback:function ($$v) {_vm.$set(_vm.form, "nickname", $$v)},expression:"form.nickname"}})],1),_vm._v(" "),_c('el-form-item',{attrs:{"label":"手机号码:","prop":"mobile"}},[_c('el-input',{attrs:{"placeholder":"请输入手机号码"},model:{value:(_vm.form.mobile),callback:function ($$v) {_vm.$set(_vm.form, "mobile", $$v)},expression:"form.mobile"}})],1),_vm._v(" "),_c('el-form-item',{attrs:{"label":"验证码:","prop":"code"}},[_c('el-input',{staticStyle:{"width":"355px"},attrs:{"placeholder":"请输入验证码"},model:{value:(_vm.form.code),callback:function ($$v) {_vm.$set(_vm.form, "code", $$v)},expression:"form.code"}}),_vm._v(" "),_c('el-button',{staticStyle:{"margin-left":"14px","width":"175px"},on:{"click":_vm.sndSmsToPhone}},[(_vm.canSendPwd)?_c('div',[_vm._v("获取验证码")]):_c('count-down',{attrs:{"time":60,"format":"ss秒","autoStart":""},on:{"finish":function($event){_vm.canSendPwd = true}}})],1)],1),_vm._v(" "),_c('el-form-item',{attrs:{"label":"创建账号:","prop":"account"}},[_c('el-input',{attrs:{"placeholder":"请设置登录账号(可用手机号代替)"},model:{value:(_vm.form.account),callback:function ($$v) {_vm.$set(_vm.form, "account", $$v)},expression:"form.account"}})],1),_vm._v(" "),_c('el-form-item',{attrs:{"label":"设置密码:","prop":"password"}},[_c('el-input',{attrs:{"placeholder":"请输入登录密码"},model:{value:(_vm.form.password),callback:function ($$v) {_vm.$set(_vm.form, "password", $$v)},expression:"form.password"}})],1),_vm._v(" "),_c('el-form-item',{attrs:{"label":"营业执照:","prop":""}},[_c('el-upload',{staticClass:"avatar-uploader",attrs:{"action":_vm.action,"show-file-list":true,"list-type":"picture-card","on-success":_vm.uploadFileSuccess,"on-remove":_vm.handleRemove,"headers":{token: _vm.$store.state.token}}},[_c('i',{staticClass:"el-icon-picture avatar-uploader-icon"}),_vm._v(" "),_c('div',{staticClass:"m-t-20 xs"},[_vm._v("上传图片")])]),_vm._v(" "),_c('div',{staticClass:"xs muted"},[_vm._v("支持jpg、png、jpeg格式的图片，最多可上传10张")])],1),_vm._v(" "),_c('el-form-item',{attrs:{"label":""}},[_c('div',{staticClass:"xs muted m-t-20"},[_c('el-checkbox',{model:{value:(_vm.checked),callback:function ($$v) {_vm.checked=$$v},expression:"checked"}}),_vm._v("\n                        同意并阅读"),_c('span',{staticClass:"primary pointer",on:{"click":function($event){_vm.dialogVisible=true}}},[_vm._v("《服务协议》")])],1),_vm._v(" "),_c('div',{staticClass:"flex m-t-10"},[_c('el-button',{staticClass:"bg-primary white",staticStyle:{"width":"213px"},on:{"click":function($event){return _vm.onSubmitStore('form')}}},[_vm._v("\n                            提交申请\n                        ")]),_vm._v(" "),_c('span',{staticClass:"m-l-20 xs muted pointer",on:{"click":function($event){return _vm.$router.push('/store_settled/record')}}},[_vm._v("查看申请记录")])],1)])],1)],2)]),_vm._ssrNode(" "),_c('el-dialog',{attrs:{"title":"提示","visible":_vm.dialogVisible,"width":"50%"},on:{"update:visible":function($event){_vm.dialogVisible=$event}}},[_c('div',{staticStyle:{"height":"40vh","overflow-y":"auto"}},[_c('div',{domProps:{"innerHTML":_vm._s(_vm.content)}})]),_vm._v(" "),_c('span',{staticClass:"dialog-footer",attrs:{"slot":"footer"},slot:"footer"},[_c('el-button',{on:{"click":function($event){_vm.dialogVisible = false}}},[_vm._v("取 消")]),_vm._v(" "),_c('el-button',{attrs:{"type":"primary"},on:{"click":function($event){_vm.dialogVisible = false}}},[_vm._v("确 定")])],1)])],2)}
var staticRenderFns = []


// CONCATENATED MODULE: ./pages/store_settled/index.vue?vue&type=template&id=208c76e4&

// EXTERNAL MODULE: ./config/app.js
var app = __webpack_require__(33);

// EXTERNAL MODULE: ./utils/type.js
var type = __webpack_require__(149);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/store_settled/index.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//


/* harmony default export */ var store_settledvue_type_script_lang_js_ = ({
  head() {
    return {
      title: this.$store.getters.headTitle,
      link: [{
        rel: 'icon',
        type: 'image/x-icon',
        href: this.$store.getters.favicon
      }]
    };
  },

  data() {
    return {
      checked: false,
      action: app["a" /* default */].baseUrl + "/api/file/formimage",
      category: [],
      //类目
      fileList: [],
      content: "",
      //隐私协议
      dialogVisible: false,
      //是否显示服务协议
      canSendPwd: true,
      //是否验证码
      form: {
        cid: "",
        //类目ID
        clabel: "",
        //类目名称
        name: "",
        //商家名称
        nickname: "",
        //联系人姓名
        mobile: "",
        //手机号码
        account: "",
        //账号
        password: "",
        //密码
        code: "" //验证码

      },
      rules: {
        name: [{
          required: true,
          message: "请输入商家名称",
          trigger: "blur"
        }],
        cid: [{
          required: true,
          message: "请选择主营类目",
          trigger: "change"
        }],
        nickname: [{
          required: true,
          message: "请输入联系人姓名",
          trigger: "blur"
        }],
        mobile: [{
          required: true,
          message: "请输入手机号码",
          trigger: "blur"
        }, {
          pattern: /^1[3|4|5|7|8][0-9]{9}$/,
          message: "请输入正确的手机号"
        }],
        code: [{
          required: true,
          message: "请输入验证码",
          trigger: "blur"
        }],
        account: [{
          required: true,
          message: "请输入登录账号",
          trigger: "blur"
        }],
        password: [{
          required: true,
          message: "请输入设置登录密码",
          trigger: "blur"
        }],
        imageForm: [{
          required: true,
          message: "请上传营业执照",
          trigger: "blur"
        }]
      }
    };
  },

  async asyncData({
    $get
  }) {
    const {
      data
    } = await $get("shop_category/getList");
    return {
      category: data
    };
  },

  mounted() {
    this.getServiceData();
  },

  methods: {
    async sndSmsToPhone() {
      if (!/^1[3|4|5|7|8][0-9]{9}$/.test(this.form.mobile)) return this.$message.error("请输入正确的手机号码");
      const {
        code
      } = await this.$post("sms/send", {
        mobile: this.form.mobile,
        key: type["c" /* SMSType */].SJSQYZ
      });

      if (code == 1) {
        this.canSendPwd = false;
        return this.$message.success("发送成功");
      } else return this.$message.error("发送失败");
    },

    uploadFileSuccess(res, fileList) {
      this.fileList.push(res.data.uri);
      console.log(res, this.fileList);
    },

    handleRemove(file, fileList) {
      console.log(fileList);

      if (fileList.length >= 0) {
        const res = fileList.map(item => item.response.data.uri);
        this.fileList = res;
      }
    },

    async getServiceData() {
      const res = await this.$get("ShopApply/getTreaty");

      if (res.code == 1) {
        this.content = res.data.content;
      }
    },

    onSubmitStore(formName) {
      if (!this.checked) return this.$message.error("请同意并阅读服务协议");
      this.$refs[formName].validate(async valid => {
        if (valid) {
          const {
            data,
            code,
            msg
          } = await this.$post("ShopApply/apply", { ...this.form,
            license: this.fileList
          });

          if (code == 1) {
            // data.id
            this.$router.push({
              path: "/store_settled/detail",
              query: {
                id: data.id
              }
            });
          }
        } else return false;
      });
    }

  }
});
// CONCATENATED MODULE: ./pages/store_settled/index.vue?vue&type=script&lang=js&
 /* harmony default export */ var pages_store_settledvue_type_script_lang_js_ = (store_settledvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(1);

// CONCATENATED MODULE: ./pages/store_settled/index.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(281)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  pages_store_settledvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  null,
  "7ee9923c"
  
)

/* harmony default export */ var store_settled = __webpack_exports__["default"] = (component.exports);

/* nuxt-component-imports */
installComponents(component, {CountDown: __webpack_require__(144).default})


/***/ })

};;
//# sourceMappingURL=index.js.map