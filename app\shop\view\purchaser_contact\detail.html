{layout name="layout1" /}
<style>
    .detail-card {
        margin-bottom: 15px;
    }
    .detail-item {
        margin-bottom: 10px;
        padding: 10px;
        background-color: #f8f8f8;
        border-radius: 5px;
    }
    .detail-label {
        font-weight: bold;
        color: #333;
        margin-bottom: 5px;
    }
    .detail-value {
        color: #666;
        word-break: break-all;
    }
    .user-avatar {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        margin-right: 15px;
    }
    .user-info {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
    }
    .source-tag {
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        color: #fff;
        display: inline-block;
    }
    .source-web { background-color: #1E9FFF; }
    .source-app { background-color: #FF5722; }
    .source-mini { background-color: #009688; }
    .source-h5 { background-color: #FFB800; }
</style>

<div class="wrapper">
    <div class="layui-card">
        <div class="layui-card-header">
            <h3>联系记录详情</h3>
        </div>
        <div class="layui-card-body">
            <div id="contact-detail-content">
                <!-- 内容将通过AJAX加载 -->
                <div class="layui-text-center" style="padding: 50px;">
                    <i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop" style="font-size: 30px;"></i>
                    <p>加载中...</p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    layui.use(['form'], function () {
        var $ = layui.$;
        
        // 获取URL参数
        function getUrlParam(name) {
            var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
            var r = window.location.search.substr(1).match(reg);
            if (r != null) return unescape(r[2]);
            return null;
        }
        
        var contactId = getUrlParam('id');
        
        if (contactId) {
            // 加载详情数据
            like.ajax({
                url: '{:url("purchaser_contact/detail")}',
                data: {id: contactId},
                type: "get",
                success: function (res) {
                    if (res.code == 1) {
                        renderDetail(res.data);
                    } else {
                        $('#contact-detail-content').html('<div class="layui-text-center" style="padding: 50px;"><p style="color: #ff5722;">' + res.msg + '</p></div>');
                    }
                },
                error: function() {
                    $('#contact-detail-content').html('<div class="layui-text-center" style="padding: 50px;"><p style="color: #ff5722;">加载失败，请重试</p></div>');
                }
            });
        } else {
            $('#contact-detail-content').html('<div class="layui-text-center" style="padding: 50px;"><p style="color: #ff5722;">缺少记录ID参数</p></div>');
        }
        
        function renderDetail(data) {
            var sourceClass = 'source-' + data.source;
            var sourceText = getSourceText(data.source);
            
            var html = `
                <div class="layui-row">
                    <div class="layui-col-md6">
                        <div class="layui-card detail-card">
                            <div class="layui-card-header">联系用户信息</div>
                            <div class="layui-card-body">
                                <div class="user-info">
                                    <img src="${data.avatar || '/static/images/default_avatar.png'}" alt="头像" class="user-avatar">
                                    <div>
                                        <h4>${data.nickname || '未知用户'}</h4>
                                        <p>手机号：${data.mobile || '未绑定'}</p>
                                        <p>注册时间：${data.user_create_time_text || '未知'}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-md6">
                        <div class="layui-card detail-card">
                            <div class="layui-card-header">联系信息</div>
                            <div class="layui-card-body">
                                <div class="detail-item">
                                    <div class="detail-label">联系时间：</div>
                                    <div class="detail-value">${data.contact_time_text}</div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">联系来源：</div>
                                    <div class="detail-value">
                                        <span class="source-tag ${sourceClass}">${sourceText}</span>
                                    </div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">IP地址：</div>
                                    <div class="detail-value">${data.ip || '未记录'}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="layui-card detail-card">
                    <div class="layui-card-header">采购信息内容</div>
                    <div class="layui-card-body">
                        <div class="detail-item">
                            <div class="detail-label">采购内容：</div>
                            <div class="detail-value">${data.purchaser_content || '暂无内容'}</div>
                        </div>
                        ${data.purchaser_image ? `
                        <div class="detail-item">
                            <div class="detail-label">采购图片：</div>
                            <div class="detail-value">
                                <img src="${data.purchaser_image}" style="max-width: 200px; max-height: 200px;" onclick="like.showImg('${data.purchaser_image}', 600)">
                            </div>
                        </div>
                        ` : ''}
                        ${data.wxqrcode ? `
                        <div class="detail-item">
                            <div class="detail-label">微信二维码：</div>
                            <div class="detail-value">
                                <img src="${data.wxqrcode}" style="max-width: 200px; max-height: 200px;" onclick="like.showImg('${data.wxqrcode}', 600)">
                            </div>
                        </div>
                        ` : ''}
                    </div>
                </div>
                
                ${data.user_agent ? `
                <div class="layui-card detail-card">
                    <div class="layui-card-header">技术信息</div>
                    <div class="layui-card-body">
                        <div class="detail-item">
                            <div class="detail-label">用户代理：</div>
                            <div class="detail-value" style="font-size: 12px;">${data.user_agent}</div>
                        </div>
                    </div>
                </div>
                ` : ''}
            `;
            
            $('#contact-detail-content').html(html);
        }
        
        function getSourceText(source) {
            var sourceMap = {
                'web': '网页',
                'app': '应用',
                'mini': '小程序',
                'h5': 'H5页面'
            };
            return sourceMap[source] || source;
        }
    });
</script>
