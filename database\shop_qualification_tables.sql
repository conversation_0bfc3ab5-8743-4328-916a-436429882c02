-- 商家资质管理相关表结构

-- ----------------------------
-- Table structure for ls_shop_qualification (商家资质表)
-- ----------------------------
DROP TABLE IF EXISTS `ls_shop_qualification`;
CREATE TABLE `ls_shop_qualification` (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `shop_id` int(11) UNSIGNED NOT NULL COMMENT '商家ID',
  `qualification_id` int(11) UNSIGNED NOT NULL COMMENT '资质ID',
  `qualification_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '资质名称（冗余字段）',
  `document_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '资质文档路径',
  `document_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '资质文档原始名称',
  `expire_time` int(11) UNSIGNED NULL DEFAULT 0 COMMENT '过期时间，0表示永久有效',
  `status` tinyint(1) UNSIGNED NULL DEFAULT 0 COMMENT '审核状态：0-待审核，1-审核通过，2-审核拒绝',
  `audit_remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '审核备注',
  `audit_time` int(11) UNSIGNED NULL DEFAULT 0 COMMENT '审核时间',
  `audit_user_id` int(11) UNSIGNED NULL DEFAULT 0 COMMENT '审核人ID',
  `del` tinyint(1) UNSIGNED NULL DEFAULT 0 COMMENT '是否删除:1-是;0-否',
  `create_time` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `update_time` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_shop_qualification` (`shop_id`, `qualification_id`, `del`) USING BTREE,
  INDEX `idx_shop_id` (`shop_id`) USING BTREE,
  INDEX `idx_qualification_id` (`qualification_id`) USING BTREE,
  INDEX `idx_status` (`status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商家资质表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ls_shop_qualification_log (商家资质操作日志表)
-- ----------------------------
DROP TABLE IF EXISTS `ls_shop_qualification_log`;
CREATE TABLE `ls_shop_qualification_log` (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `shop_qualification_id` int(11) UNSIGNED NOT NULL COMMENT '商家资质ID',
  `shop_id` int(11) UNSIGNED NOT NULL COMMENT '商家ID',
  `qualification_id` int(11) UNSIGNED NOT NULL COMMENT '资质ID',
  `action` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '操作类型：upload-上传，audit-审核，update-更新',
  `old_status` tinyint(1) UNSIGNED NULL DEFAULT 0 COMMENT '原状态',
  `new_status` tinyint(1) UNSIGNED NULL DEFAULT 0 COMMENT '新状态',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '操作备注',
  `operator_id` int(11) UNSIGNED NULL DEFAULT 0 COMMENT '操作人ID（0表示商家自己）',
  `operator_type` tinyint(1) UNSIGNED NULL DEFAULT 1 COMMENT '操作人类型：1-商家，2-管理员',
  `create_time` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_shop_qualification_id` (`shop_qualification_id`) USING BTREE,
  INDEX `idx_shop_id` (`shop_id`) USING BTREE,
  INDEX `idx_action` (`action`) USING BTREE,
  INDEX `idx_create_time` (`create_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商家资质操作日志表' ROW_FORMAT = Dynamic;
