2024-06-14 Version: 3.1.0
- Support API PostISRerank.


2024-05-20 Version: 3.0.0
- Update API DeleteServiceDataByConditions: add param X-DashScope-OpenAPISource.
- Update API PostISConvRewriter: add param Model.
- Update API PostISConvRewriter: delete param Version.
- Update API PostISRetrieveRouter: add param Model.
- Update API PostISRetrieveRouter: delete param Version.
- Update API PostMSDataProcessingCount: add param X-DashScope-OpenAPISource.
- Update API PostMSSearchEnhance: add param X-DashScope-OpenAPISource.


2024-04-10 Version: 2.5.2
- Generated php 2020-06-29 for alinlp.

2024-04-10 Version: 2.5.1
- Update API PostMSSearchEnhance: add param MinScore.


2024-03-29 Version: 2.5.0
- Support API PostMSServiceDataImport.


2024-03-27 Version: 2.4.0
- Support API ImportServiceDataV2.


2024-03-23 Version: 2.3.0
- Support API PostISConvRewriter.
- Support API PostISRetrieveRouter.
- Update API ADMiniCog: update response param.
- Update API ADMiniCogResult: update response param.
- Update API GetItemPubChEcom: update response param.


2024-03-23 Version: 2.3.0
- Support API PostISConvRewriter.
- Support API PostISRetrieveRouter.
- Update API ADMiniCog: update response param.
- Update API ADMiniCogResult: update response param.
- Update API GetItemPubChEcom: update response param.


2024-03-11 Version: 2.2.1
- Update API ADMiniCog: update response param.
- Update API ADMiniCogResult: update response param.
- Update API GetItemPubChEcom: update response param.


2024-01-23 Version: 2.2.0
- Generated php 2020-06-29 for alinlp.

2023-12-26 Version: 2.1.0
- Generated php 2020-06-29 for alinlp.

2023-02-27 Version: 2.0.3
- Supported SDK for AliNLP version-2.

2022-11-24 Version: 2.0.2
- Supported SDK for AliNLP version-2.

2022-11-14 Version: 2.0.1
- Support GetGateChEcom new version .

2022-06-07 Version: 1.0.0
- Supported SDK for AliNLP version-2.

