{layout name="layout2" /}
<style>
    html {
        height: 249px;
    }
    body {
        height: 100%;
    }
    .div-flex {
        display: flex;
        align-items: center;
        justify-content: left;
    }

    .layui-form-label {
        width: 100px;
    }

    .layui-table th {
        text-align: center;
    }

</style>

<div class="layui-card-body wrapper">
    <!--基本信息-->
    <div class="layui-form" lay-filter="layuiadmin-form-change_address" id="layuiadmin-form-change_address" >
        <input type="hidden" name="id" id="id" value="{$id}">

        <div class="layui-form-item">
            <label class="layui-form-label ">退款方式:</label>
            <div class="layui-input-block">
                <input type="radio" name="refund_way" value="1" title="原路退回" checked>
                <input type="radio" name="refund_way" value="2" title="退回余额">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label ">退款金额:</label>
            <div class="layui-input-inline" style="padding: 6px 0">
                <p>{$refund_price}</p>
            </div>
        </div>

        <div class="layui-form-item div-flex ">
            <div class="layui-input-block ">
                <button type="button" class="layui-btn layui-btn-sm layui-btn-primary width_160 " id="back">取消</button>
                <input type="button" class="layui-btn layui-btn-sm layui-btn-normal width_160" lay-submit lay-filter="send" id="send" value="确认">
            </div>
        </div>
    </div>
</div>


<script type="text/javascript">
    //注意：选项卡 依赖 element 模块，否则无法进行功能性操作

    layui.config({
        version:"{$front_version}",
        base: '/static/lib/' //静态资源所在路径
    }).use(['form'], function(){
        var $ = layui.$
            , form = layui.form;

        $('#back').click(function () {
            var index=parent.layer.getFrameIndex(window.name); //获取当前窗口的name
            parent.layer.close(index);
            parent.layui.table.reload('order-lists');
            return true;
        });


        var throttle = false;
        form.on('submit(send)', function (data) {
            if (throttle) {
                layer.msg('请勿重复操作!');
                return
            }
            throttle = true;
            var field = data.field;
            like.ajax({
                url: '{:url("after_sale.after_sale/confirm")}'
                , data: field
                , type: 'post'
                , success: function (res) {
                    throttle = false;
                    if (res.code == 1) {
                        layui.layer.msg(res.msg, {
                            offset: '15px'
                            , icon: 1
                            , time: 1000
                        },function () {
                            var index = parent.layer.getFrameIndex(window.name);
                            parent.location.reload();
                            parent.layer.close(index);
                        });
                    } else {
                        layer.msg(res.msg, {icon: 2}, function(){
                            // layer.msg('提示框关闭后的回调');
                        });
                    }
                }
                , error: function (res) {
                    throttle = false;
                    layer.msg(res.statusText);
                }
            });
        })

    });
</script>