<?php
namespace app\common\model\agent;

use app\common\basics\Models;
use app\common\enum\PayEnum;
use app\common\model\Client_;
use app\common\model\Pay;
use app\common\model\shop\Shop;
use app\common\model\user\User;
use app\common\model\order\Order;
use app\common\model\order\OrderGoods;
use app\common\server\ConfigServer;
use think\facade\Db;

class UserTransferRecords extends Models
{
    // 设置当前模型对应的完整数据表名称
    protected $table = 'ls_user_transfer_records';

    // 设置主键名
    protected $pk = 'id';

}