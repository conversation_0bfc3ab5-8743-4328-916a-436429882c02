<?php


namespace app\shop\controller;


use app\common\basics\ShopBase;
use app\common\server\FileServer;
use app\common\server\JsonServer;
use Exception;

class Upload extends ShopBase
{
//    public $like_not_need_login = ['image'];

    /**
     * NOTE: 上传图片
     * @author: 张无忌
     */
    public function image()
    {
        try {

            $cid = $this->request->post('cid');
            $result = FileServer::image($cid, $this->shop_id);

            return JsonServer::success("上传成功", $result);
        } catch (Exception $e) {
            return JsonServer::error($e->getMessage());
        }
    }

    /**
     * NOTE: 上传图片
     * @author: 张无忌
     */
    public function file()
    {
        try {

            $save_path = 'uploads/other';
            $result = FileServer::other($save_path);
            return JsonServer::success("上传成功", $result[1]);
        } catch (Exception $e) {
            return JsonServer::error($e->getMessage());
        }
    }

    /**
     * 上传视频
     */
    public function video()
    {
        try {
            $cid = $this->request->post('cid');
            $result = FileServer::video($cid, $this->shop_id);

            return JsonServer::success("上传成功", $result);
        } catch (Exception $e) {
            return JsonServer::error($e->getMessage());
        }
    }

    /**
     * 上传资质图片
     */
    public function document()
    {
        try {
            // 获取上传文件
            $file = request()->file('file');
            if (empty($file)) {
                return JsonServer::error('未找到上传文件');
            }

            // 验证文件类型 - 只允许图片格式
            $allowedExts = ['jpg', 'jpeg', 'png', 'gif', 'bmp'];
            $fileExt = strtolower($file->getOriginalExtension());
            if (!in_array($fileExt, $allowedExts)) {
                return JsonServer::error('只允许上传图片格式：' . implode('、', $allowedExts));
            }

            // 验证文件大小 - 最大5MB
            $maxSize = 5 * 1024 * 1024; // 5MB
            if ($file->getSize() > $maxSize) {
                return JsonServer::error('文件大小不能超过5MB');
            }

            // 验证是否是有效的图片文件
            $imageInfo = getimagesize($file->getPathname());
            if ($imageInfo === false) {
                return JsonServer::error('不是有效的图片文件');
            }

            $save_path = 'uploads/shop_qualifications/' . $this->shop_id;
            $result = FileServer::other($save_path);

            // 记录调试信息
            \think\facade\Log::info('商家资质图片上传结果: ' . json_encode($result));

            // FileServer::other() 返回格式: ['上传文件成功', $data]
            if (is_array($result) && count($result) >= 2 && $result[0] === '上传文件成功') {
                $data = $result[1];
                // 格式化返回数据，与image上传保持一致
                $response = [
                    'id'   => $data['id'] ?? 0,
                    'name' => $data['name'] ?? '',
                    'uri'  => \app\common\server\UrlServer::getFileUrl($data['uri'] ?? ''),
                    'size' => $data['size'] ?? 0,
                ];
                return JsonServer::success("上传成功", $response);
            } else {
                $error_msg = '上传失败';
                if (is_array($result) && isset($result[1])) {
                    if (is_array($result[1]) && !empty($result[1])) {
                        $error_msg = $result[1][0]; // 取数组第一个元素
                    } elseif (is_string($result[1])) {
                        $error_msg = $result[1];
                    }
                }
                return JsonServer::error($error_msg);
            }
        } catch (Exception $e) {
            return JsonServer::error($e->getMessage());
        }
    }
}