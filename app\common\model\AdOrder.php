<?php
namespace app\common\model;

use app\common\basics\Models;

class AdOrder extends Models
{
    /**
     * @notes 关联广告位模型
     */
    public function adPosition()
    {
        return $this->belongsTo(AdPosition::class, 'ad_position_id', 'id');
    }

    /**
     * @notes 关联广告内容模型
     */
    public function adContent()
    {
        // 假设一个广告订单只对应一个广告内容记录
        return $this->hasOne(Ad::class, 'ad_order_id', 'id');
    }
}