{"name": "ezyang/htmlpurifier", "description": "Standards compliant HTML filter written in PHP", "type": "library", "keywords": ["html"], "homepage": "http://htmlpurifier.org/", "license": "LGPL-2.1-or-later", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://ezyang.com"}], "require": {"php": ">=5.2"}, "autoload": {"psr-0": {"HTMLPurifier": "library/"}, "files": ["library/HTMLPurifier.composer.php"], "exclude-from-classmap": ["/library/HTMLPurifier/Language/"]}}