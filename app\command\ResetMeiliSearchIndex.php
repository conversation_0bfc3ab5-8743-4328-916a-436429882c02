<?php
declare (strict_types = 1);

namespace app\command;

use app\common\library\MeiliSearch;
use think\console\Command;
use think\console\Input;
use think\console\Output;

/**
 * 重置MeiliSearch索引
 */
class ResetMeilisearchIndex extends Command
{

    protected function configure()
    {
        // 指令配置
        $this->setName('reset_meilisearch_index')
            ->setDescription('重置MeiliSearch索引');
    }

    protected function execute(Input $input, Output $output)
    {
        $output->writeln("开始重置MeiliSearch索引...");

        // 初始化MeiliSearch客户端
        $meili = new MeiliSearch();

        // 删除索引
        $output->writeln('正在删除索引...');
        $result = $meili->deleteIndex('goods');
        $output->writeln('索引删除结果: ' . json_encode($result));

        // 创建索引
        $output->writeln('正在创建索引...');
        $settings = [
            'searchableAttributes' => ['name', 'category_path', 'brand_name', 'tags', 'remark', 'content'],
            'filterableAttributes' => [
                'shop_id',
                'first_cate_id',
                'second_cate_id',
                'third_cate_id',
                'brand_id',
                'status',
                'is_hot',
                'is_recommend',
                'category_path',
                'brand_name',
                'tags'
            ],
            'sortableAttributes' => [
                'min_price',
                'sales_actual',
                'sales_virtual',
                'create_time',
                'update_time'
            ],
            'rankingRules' => [
                'words',
                'typo',
                'proximity',
                'attribute',
                'sort',
                'exactness',
                'custom.sales_actual:desc',
                'custom.create_time:desc'
            ],
            'synonyms' => [
                '上衣' => ['T恤', '衬衫', '卫衣'],
                '裤子' => ['长裤', '休闲裤', '牛仔裤'],
                '笔记本电脑' => ['笔记本', '手提电脑'],
                '手机' => ['移动电话', '手提电话']
            ]
        ];
        $result = $meili->createIndex('goods', $settings);
        $output->writeln('索引创建结果: ' . json_encode($result));

        $output->writeln("MeiliSearch索引重置完成，请运行 sync_goods_to_meilisearch 命令同步数据");
    }
}
