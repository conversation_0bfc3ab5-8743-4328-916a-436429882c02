{"scope": "alipay", "name": "easysdk-member-identification", "version": "0.0.1", "main": "./main.tea", "java": {"package": "com.alipay.easysdk.member.identification", "baseClient": "com.alipay.easysdk.kernel.BaseClient"}, "csharp": {"namespace": "Alipay.EasySDK.Member.Identification", "baseClient": "Alipay.EasySDK.Kernel:BaseClient"}, "typescript": {"baseClient": "@alipay/easysdk-baseclient"}, "php": {"package": "Alipay.EasySDK.Member.Identification"}, "go": {"namespace": "member/identification"}, "libraries": {"EasySDKKernel": "alipay:easysdk-kernel:*"}}