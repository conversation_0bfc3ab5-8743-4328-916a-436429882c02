<?php

namespace app\api\controller;

use app\common\basics\Api;
use app\common\server\WeChatPayServer;
use think\facade\Db;

/**
 * 测试支付回调控制器
 */
class TestPayment extends Api
{
    /**
     * 测试代理支付回调
     */
    public function testAgentPayment()
    {
        $order_sn = $this->request->param('order_sn', '');
        
        if (empty($order_sn)) {
            return $this->error('订单号不能为空');
        }
        
        $result = WeChatPayServer::testAgentPayment($order_sn);
        
        if ($result === true) {
            return $this->success('代理支付回调处理成功');
        } else {
            return $this->error('代理支付回调处理失败：' . $result);
        }
    }
    
    /**
     * 模拟微信支付回调
     */
    public function simulateWechatPayCallback()
    {
        $order_sn = $this->request->param('order_sn', '');
        
        if (empty($order_sn)) {
            return $this->error('订单号不能为空');
        }
        
        // 查询订单信息
        $order = Db::name('agent_merchantfees')->where(['order_sn' => $order_sn])->find();
        if (!$order) {
            return $this->error('订单不存在：' . $order_sn);
        }
        
        // 构造回调数据
        $callbackData = [
            'appid' => 'wx0b0f5aa20564c385',
            'attach' => 'agent',
            'bank_type' => 'OTHERS',
            'cash_fee' => '1',
            'fee_type' => 'CNY',
            'is_subscribe' => 'N',
            'mch_id' => '**********',
            'nonce_str' => '*************',
            'openid' => 'okozu6y36jWni7bssy6DhXgj_cK4',
            'out_trade_no' => $order_sn,
            'result_code' => 'SUCCESS',
            'return_code' => 'SUCCESS',
            'sign' => '2A1C47145CAB77A89C886856E660FE9E',
            'time_end' => date('YmdHis'),
            'total_fee' => '1',
            'trade_type' => 'JSAPI',
            'transaction_id' => 'SIM_' . date('YmdHis') . rand(1000, 9999)
        ];
        
        // 记录模拟回调数据
        Db::name('log')->insert([
            'type' => 'simulate_callback',
            'log' => json_encode($callbackData),
            'creat_time' => date('Y-m-d H:i:s')
        ]);
        
        // 获取支付配置
        $config = [
            'app_id' => 'wx0b0f5aa20564c385',
            'mch_id' => '**********',
            'key' => '你的商户密钥', // 这里应该使用实际的商户密钥
            'notify_url' => 'https://你的回调地址'
        ];
        
        // 调用回调处理方法
        try {
            // 直接调用处理方法
            $result = WeChatPayServer::testAgentPayment($order_sn);
            
            if ($result === true) {
                return $this->success('模拟回调处理成功');
            } else {
                return $this->error('模拟回调处理失败：' . $result);
            }
        } catch (\Exception $e) {
            return $this->error('模拟回调处理异常：' . $e->getMessage());
        }
    }
    
    /**
     * 查看最近的日志
     */
    public function viewLogs()
    {
        $type = $this->request->param('type', '');
        $limit = $this->request->param('limit', 20, 'intval');
        
        $where = [];
        if (!empty($type)) {
            $where[] = ['type', '=', $type];
        }
        
        $logs = Db::name('log')
            ->where($where)
            ->order('id desc')
            ->limit($limit)
            ->select()
            ->toArray();
        
        return $this->success('获取日志成功', ['logs' => $logs]);
    }
}
