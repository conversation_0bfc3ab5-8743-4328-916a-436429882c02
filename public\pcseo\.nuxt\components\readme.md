# Discovered Components

This is an auto-generated list of components discovered by [nuxt/components](https://github.com/nuxt/components).

You can directly use them in pages and other components without the need to import them.

**Tip:** If a component is conditionally rendered with `v-if` and is big, it is better to use `Lazy` or `lazy-` prefix to lazy load.

- `<ActivityArea>` | `<activity-area>` (components/activity-area.vue)
- `<AdItem>` | `<ad-item>` (components/ad-item.vue)
- `<AddressAdd>` | `<address-add>` (components/address-add.vue)
- `<AddressList>` | `<address-list>` (components/address-list.vue)
- `<AfterSalesList>` | `<after-sales-list>` (components/after-sales-list.vue)
- `<CommentList>` | `<comment-list>` (components/comment-list.vue)
- `<CountDown>` | `<count-down>` (components/count-down.vue)
- `<CouponsList>` | `<coupons-list>` (components/coupons-list.vue)
- `<DeliverSearch>` | `<deliver-search>` (components/deliver-search.vue)
- `<EvaluationList>` | `<evaluation-list>` (components/evaluation-list.vue)
- `<GoodsList>` | `<goods-list>` (components/goods-list.vue)
- `<HomeSeckill>` | `<home-seckill>` (components/home-seckill.vue)
- `<InputExpress>` | `<input-express>` (components/input-Express.vue)
- `<NullData>` | `<null-data>` (components/null-data.vue)
- `<NumberBox>` | `<number-box>` (components/number-box.vue)
- `<OrderList>` | `<order-list>` (components/order-list.vue)
- `<PriceFormate>` | `<price-formate>` (components/price-formate.vue)
- `<ShopItem>` | `<shop-item>` (components/shop-item.vue)
- `<Upload>` | `<upload>` (components/upload.vue)
- `<LayoutAslideNav>` | `<layout-aslide-nav>` (components/layout/aslide-nav.vue)
- `<LayoutCategory>` | `<layout-category>` (components/layout/category.vue)
- `<LayoutFloatNav>` | `<layout-float-nav>` (components/layout/float-nav.vue)
- `<LayoutFooter>` | `<layout-footer>` (components/layout/footer.vue)
- `<LayoutHeader>` | `<layout-header>` (components/layout/header.vue)
- `<LayoutMainNav>` | `<layout-main-nav>` (components/layout/main-nav.vue)
