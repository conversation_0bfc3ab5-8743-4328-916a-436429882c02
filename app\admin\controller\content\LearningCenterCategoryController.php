<?php

namespace app\admin\controller\content;

use app\admin\logic\content\LearningCenterCategoryLogic;
use app\admin\validate\content\LearningCenterCategoryValidate; // Assuming this validator exists or will be created
use app\common\basics\AdminBase;
use app\common\server\JsonServer;

class LearningCenterCategoryController extends AdminBase
{
    /**
     * @NOTES: 学习中心分类列表
     * @author: Trae
     */
    public function lists()
    {
        if ($this->request->isAjax()) {
            $get = $this->request->get();
            $lists = LearningCenterCategoryLogic::lists($get);
            return JsonServer::success("获取成功", $lists);
        }

        return view();
    }

    /**
     * @NOTES: 添加学习中心分类
     * @author: Trae
     */
    public function add()
    {
        if ($this->request->isAjax()) {
            // Assuming LearningCenterCategoryValidate exists and has 'add' scene
            // (new LearningCenterCategoryValidate())->goCheck('add'); 
            $post = $this->request->post();
            $res = LearningCenterCategoryLogic::add($post);
            if ($res === false) {
                $error = LearningCenterCategoryLogic::getError() ?: '新增失败';
                return JsonServer::error($error);
            }
            return JsonServer::success('新增成功');
        }

        return view();
    }

    /**
     * @NOTES: 编辑学习中心分类
     * @author: Trae
     */
    public function edit()
    {
        if ($this->request->isAjax()) {
            // Assuming LearningCenterCategoryValidate exists and has 'edit' scene
            // (new LearningCenterCategoryValidate())->goCheck('edit'); 
            $post = $this->request->post();
            $res = LearningCenterCategoryLogic::edit($post);
            if ($res === false) {
                $error = LearningCenterCategoryLogic::getError() ?: '编辑失败';
                return JsonServer::error($error);
            }
            return JsonServer::success('编辑成功');
        }

        $id = $this->request->get('id');
        return view('', [
            'detail' => LearningCenterCategoryLogic::detail($id)
        ]);
    }

    /**
     * @NOTES: 删除学习中心分类
     * @author: Trae
     */
    public function del()
    {
        if ($this->request->isAjax()) {
             // Assuming LearningCenterCategoryValidate exists and has 'id' scene
            // (new LearningCenterCategoryValidate())->goCheck('id');
            $id = $this->request->post('id');
            $res = LearningCenterCategoryLogic::del($id);
            if ($res === false) {
                $error = LearningCenterCategoryLogic::getError() ?: '删除失败';
                return JsonServer::error($error);
            }
            return JsonServer::success('删除成功');
        }

        return JsonServer::error('异常');
    }

    /**
     * @Notes: 切换显示状态
     * @Author: Trae
     */
    public function switchShow()
    {
        if ($this->request->isAjax()) {
            // Assuming LearningCenterCategoryValidate exists and has 'id' scene
            // (new LearningCenterCategoryValidate())->goCheck('id');
            $id = $this->request->post('id');
            $res = LearningCenterCategoryLogic::switchShow($id);
            if ($res === false) {
                $error = LearningCenterCategoryLogic::getError() ?: '操作失败';
                return JsonServer::error($error);
            }
            return JsonServer::success('操作成功');
        }

        return JsonServer::error('异常');
    }

    /**
     * @Notes: 获取分类选项
     * @Author: Trae
     */
    public function getOptions()
    {
        $options = LearningCenterCategoryLogic::getCategoryOptions();
        return JsonServer::success('获取成功', $options);
    }
}