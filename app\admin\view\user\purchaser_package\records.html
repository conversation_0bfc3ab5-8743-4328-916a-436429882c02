{layout name="layout1" /}

<div class="wrapper">
    <div class="layui-card">
        <div class="layui-card-body">
            <!--搜索-->
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">商家ID</label>
                    <div class="layui-input-inline">
                        <input type="text" name="shop_id" placeholder="请输入商家ID" autocomplete="off" class="layui-input">
                    </div>
                </div>
              
                <div class="layui-inline">
                    <button class="layui-btn layuiadmin-btn-admin" lay-submit lay-filter="search">
                        <i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>
                    </button>
                </div>
            </div>
            
            <!--表格-->
            <table id="record-lists" lay-filter="record-lists"></table>

            <!--js模板-->
            <script type="text/html" id="pay-status-tpl">
                {{# if(d.pay_status == 1) { }}
                    <span class="layui-badge layui-bg-green">已支付</span>
                {{# } else { }}
                    <span class="layui-badge">未支付</span>
                {{# } }}
            </script>
        </div>
    </div>
</div>
<script>
    layui.use(['table', 'form'], function(){
        var form = layui.form
            ,table = layui.table;

        form.render(); // 渲染所有表单元素

        // 初始化表格
        var ins = like.tableLists('#record-lists', '{:url("records")}', [
            {field: 'id', width: 80, title: 'ID', sort: true, align: 'center'},
            {field: 'order_sn', minWidth: 180, title: '订单号'},
            {field: 'shop_id', width: 100, title: '商家ID', align: 'center'},
            {field: 'package_name', minWidth: 150, title: '购买套餐'},
            {field: 'purchaser_count', width: 120, title: '分配人数', align: 'center'},
            {field: 'price', width: 120, title: '支付金额(元)', align: 'center'},
            {field: 'pay_status', width: 100, title: '支付状态', align: 'center', templet: '#pay-status-tpl'},
            {field: 'pay_time_text', width: 160, title: '支付时间', align: 'center'},
            {field: 'create_time_text', width: 160, title: '创建时间', align: 'center'},
        ]);
        
        // 监听搜索
        form.on('submit(search)', function(data){
            var field = data.field;
            // 执行重载
            table.reload('record-lists', {
                where: field,
                page: {
                    curr: 1 //重新从第 1 页开始
                }
            });
        });
    });
</script>
