<?php
namespace app\admin\controller;

use app\admin\logic\MassMessageLimitLogic;
use app\common\basics\AdminBase;
use app\common\utils\AjaxUtils;
use app\common\server\JsonServer;

/**
 * 群发信息限制配置控制器
 * Class MassMessageLimitConfig
 * @package app\admin\controller
 */
class MassMessageLimitConfig extends AdminBase
{
    /**
     * 群发信息限制配置列表
     * @return string
     * @throws \Exception
     */
    public function index()
    {
        if ($this->request->isAjax()) {
            $get = $this->request->get();
            $result = MassMessageLimitLogic::getListsForTable($get);
            return JsonServer::success('获取成功', $result);
        }

        return view('', [
            'tier_options' => MassMessageLimitLogic::getTierLevelOptions()
        ]);
    }

    /**
     * 添加群发信息限制配置
     * @return string|\think\response\Json
     * @throws \Exception
     */
    public function add()
    {
        if ($this->request->isAjax()) {
            $post = $this->request->post();

            // 数据验证
            if (empty($post['tier_level']) && $post['tier_level'] !== '0') {
                return JsonServer::error('请选择商家等级');
            }

            if (empty($post['daily_limit']) || $post['daily_limit'] < 0) {
                return JsonServer::error('请输入有效的每日群发限制数量');
            }

            if (empty($post['total_purchaser_count']) || $post['total_purchaser_count'] < 0) {
                return JsonServer::error('请输入有效的采购人员总数');
            }

            $result = MassMessageLimitLogic::add($post);
            if ($result) {
                return JsonServer::success('添加成功');
            } else {
                return JsonServer::error(MassMessageLimitLogic::getError() ?: '添加失败');
            }
        }

        return view('', [
            'tier_options' => MassMessageLimitLogic::getTierLevelOptions()
        ]);
    }

    /**
     * 编辑群发信息限制配置
     * @return string|\think\response\Json
     * @throws \Exception
     */
    public function edit()
    {
        if ($this->request->isAjax()) {
            $post = $this->request->post();

            // 数据验证
            if (empty($post['id'])) {
                return JsonServer::error('参数错误');
            }

            if (empty($post['daily_limit']) || $post['daily_limit'] < 0) {
                return JsonServer::error('请输入有效的每日群发限制数量');
            }

            if (empty($post['total_purchaser_count']) || $post['total_purchaser_count'] < 0) {
                return JsonServer::error('请输入有效的采购人员总数');
            }

            $result = MassMessageLimitLogic::edit($post);
            if ($result) {
                return JsonServer::success('编辑成功');
            } else {
                return JsonServer::error(MassMessageLimitLogic::getError() ?: '编辑失败');
            }
        }

        $id = $this->request->get('id');
        if (empty($id)) {
           return JsonServer::error('参数错误');
          
        }

        return view('', [
            'info' => MassMessageLimitLogic::getInfo($id),
            'tier_options' => MassMessageLimitLogic::getTierLevelOptions()
        ]);
    }

    /**
     * 删除群发信息限制配置
     * @return \think\response\Json
     * @throws \Exception
     */
    public function delete()
    {
        $id = $this->request->post('id');
        if (empty($id)) {
            return JsonServer::error('参数错误');
        }

        $result = MassMessageLimitLogic::delete($id);
        if ($result) {
            return JsonServer::success('删除成功');
        } else {
            return JsonServer::error('删除失败');
        }
    }

    /**
     * 重新分配所有商家采购人员
     * @return \think\response\Json
     */
    public function reAllocateAll()
    {
        $result = MassMessageLimitLogic::reAllocateAllShops();
        if ($result) {
            return JsonServer::success('重新分配成功');
        } else {
            return JsonServer::error(MassMessageLimitLogic::getError() ?: '重新分配失败');
        }
    }
}
