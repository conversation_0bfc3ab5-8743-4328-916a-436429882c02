{"scope": "alipay", "name": "easysdk-payment-facetoface", "version": "0.0.1", "main": "./main.tea", "java": {"package": "com.alipay.easysdk.payment.facetoface", "baseClient": "com.alipay.easysdk.kernel.BaseClient"}, "csharp": {"namespace": "Alipay.EasySDK.Payment.FaceToFace", "baseClient": "Alipay.EasySDK.Kernel:BaseClient"}, "typescript": {"baseClient": "@alipay/easysdk-baseclient"}, "php": {"package": "Alipay.EasySDK.Payment.FaceToFace"}, "go": {"namespace": "payment/facetoface"}, "libraries": {"EasySDKKernel": "alipay:easysdk-kernel:*"}}