(window.webpackJsonp=window.webpackJsonp||[]).push([[38,13,16,18],{437:function(t,e,o){"use strict";var r=o(17),n=o(2),c=o(3),l=o(136),d=o(27),f=o(18),h=o(271),v=o(52),m=o(135),x=o(270),_=o(5),w=o(98).f,C=o(44).f,y=o(26).f,k=o(438),S=o(439).trim,I="Number",N=n.Number,z=N.prototype,T=n.TypeError,L=c("".slice),E=c("".charCodeAt),$=function(t){var e=x(t,"number");return"bigint"==typeof e?e:G(e)},G=function(t){var e,o,r,n,c,l,d,code,f=x(t,"number");if(m(f))throw T("Cannot convert a Symbol value to a number");if("string"==typeof f&&f.length>2)if(f=S(f),43===(e=E(f,0))||45===e){if(88===(o=E(f,2))||120===o)return NaN}else if(48===e){switch(E(f,1)){case 66:case 98:r=2,n=49;break;case 79:case 111:r=8,n=55;break;default:return+f}for(l=(c=L(f,2)).length,d=0;d<l;d++)if((code=E(c,d))<48||code>n)return NaN;return parseInt(c,r)}return+f};if(l(I,!N(" 0o1")||!N("0b1")||N("+0x1"))){for(var O,M=function(t){var e=arguments.length<1?0:N($(t)),o=this;return v(z,o)&&_((function(){k(o)}))?h(Object(e),o,M):e},R=r?w(N):"MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,EPSILON,MAX_SAFE_INTEGER,MIN_SAFE_INTEGER,isFinite,isInteger,isNaN,isSafeInteger,parseFloat,parseInt,fromString,range".split(","),j=0;R.length>j;j++)f(N,O=R[j])&&!f(M,O)&&y(M,O,C(N,O));M.prototype=z,z.constructor=M,d(n,I,M)}},438:function(t,e,o){var r=o(3);t.exports=r(1..valueOf)},439:function(t,e,o){var r=o(3),n=o(33),c=o(16),l=o(440),d=r("".replace),f="["+l+"]",h=RegExp("^"+f+f+"*"),v=RegExp(f+f+"*$"),m=function(t){return function(e){var o=c(n(e));return 1&t&&(o=d(o,h,"")),2&t&&(o=d(o,v,"")),o}};t.exports={start:m(1),end:m(2),trim:m(3)}},440:function(t,e){t.exports="\t\n\v\f\r                　\u2028\u2029\ufeff"},441:function(t,e,o){var content=o(444);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,o(14).default)("3181fc86",content,!0,{sourceMap:!1})},442:function(t,e,o){"use strict";o.r(e);o(437),o(80),o(272);var r={data:function(){return{priceSlice:{}}},components:{},props:{firstSize:{type:Number,default:14},secondSize:{type:Number,default:14},color:{type:String},weight:{type:[String,Number],default:400},price:{type:[String,Number],default:""},showSubscript:{type:Boolean,default:!0},subscriptSize:{type:Number,default:14},lineThrough:{type:Boolean,default:!1}},created:function(){this.priceFormat()},watch:{price:function(t){this.priceFormat()}},methods:{priceFormat:function(){var t=this.price,e={};null!==t&&(t=parseFloat(t),t=String(t).split("."),e.first=t[0],e.second=t[1],this.priceSlice=e)}}},n=(o(443),o(9)),component=Object(n.a)(r,(function(){var t=this,e=t.$createElement,o=t._self._c||e;return o("span",{class:(t.lineThrough?"line-through":"")+"price-format",style:{color:t.color,"font-weight":t.weight}},[t.showSubscript?o("span",{style:{"font-size":t.subscriptSize+"px","margin-right":"1px"}},[t._v("¥")]):t._e(),t._v(" "),o("span",{style:{"font-size":t.firstSize+"px","margin-right":"1px"}},[t._v(t._s(t.priceSlice.first))]),t._v(" "),t.priceSlice.second?o("span",{style:{"font-size":t.secondSize+"px"}},[t._v("."+t._s(t.priceSlice.second))]):t._e()])}),[],!1,null,null,null);e.default=component.exports},443:function(t,e,o){"use strict";o(441)},444:function(t,e,o){var r=o(13)(!1);r.push([t.i,".price-format{display:flex;align-items:baseline}",""]),t.exports=r},445:function(t,e,o){var content=o(447);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,o(14).default)("12a18d22",content,!0,{sourceMap:!1})},446:function(t,e,o){"use strict";o(445)},447:function(t,e,o){var r=o(13)(!1);r.push([t.i,".null-data[data-v-93598fb0]{padding:100px}.null-data .img-null[data-v-93598fb0]{width:150px;height:150px}",""]),t.exports=r},448:function(t,e,o){"use strict";o.r(e);var r={components:{},props:{img:{type:String},text:{type:String,default:"暂无数据"},imgStyle:{type:String,default:""}},methods:{}},n=(o(446),o(9)),component=Object(n.a)(r,(function(){var t=this,e=t.$createElement,o=t._self._c||e;return o("div",{staticClass:"bg-white flex-col col-center null-data"},[o("img",{staticClass:"img-null",style:t.imgStyle,attrs:{src:t.img,alt:""}}),t._v(" "),o("div",{staticClass:"muted mt8"},[t._v(t._s(t.text))])])}),[],!1,null,"93598fb0",null);e.default=component.exports},450:function(t,e,o){"use strict";o.d(e,"b",(function(){return n})),o.d(e,"a",(function(){return c}));var r=o(34);o(80),o(272),o(101),o(61),o(24),o(38),o(62),o(45),o(19),o(63),o(64),o(46);var n=function(t){var time=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1e3,e=arguments.length>2?arguments[2]:void 0,o=new Date(0).getTime();return function(){var r=(new Date).getTime();if(r-o>time){for(var n=arguments.length,c=new Array(n),l=0;l<n;l++)c[l]=arguments[l];t.apply(e,c),o=r}}};function c(t){var p="";if("object"==Object(r.a)(t)){for(var e in p="?",t)p+="".concat(e,"=").concat(t[e],"&");p=p.slice(0,-1)}return p}},451:function(t,e,o){var content=o(456);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,o(14).default)("1469a4e1",content,!0,{sourceMap:!1})},455:function(t,e,o){"use strict";o(451)},456:function(t,e,o){var r=o(13)(!1);r.push([t.i,".goods-list[data-v-060944d1]{align-items:stretch}.goods-list .goods-item[data-v-060944d1]{display:block;box-sizing:border-box;width:224px;height:310px;margin-bottom:16px;padding:12px 12px 16px;border-radius:4px;transition:all .2s}.goods-list .goods-item[data-v-060944d1]:hover{transform:translateY(-8px);box-shadow:0 0 6px rgba(0,0,0,.1)}.goods-list .goods-item .goods-img[data-v-060944d1]{width:200px;height:200px}.goods-list .goods-item .name[data-v-060944d1]{margin-bottom:10px;height:40px;line-height:20px}.goods-list .goods-item .seckill .btn[data-v-060944d1]{padding:4px 12px;border-radius:4px;border:1px solid transparent}.goods-list .goods-item .seckill .btn.not-start[data-v-060944d1]{border-color:#ff2c3c;color:#ff2c3c;background-color:transparent}.goods-list .goods-item .seckill .btn.end[data-v-060944d1]{background-color:#e5e5e5;color:#fff}",""]),t.exports=r},462:function(t,e,o){"use strict";o.r(e);o(437);var r={props:{list:{type:Array,default:function(){return[]}},num:{type:Number,default:5},type:{type:String},status:{type:Number}},watch:{list:{immediate:!0,handler:function(t){}}},computed:{getSeckillText:function(){switch(this.status){case 0:return"未开始";case 1:return"立即抢购";case 2:return"已结束"}}}},n=(o(455),o(9)),component=Object(n.a)(r,(function(){var t=this,e=t.$createElement,o=t._self._c||e;return o("div",{staticClass:"goods-list flex flex-wrap"},t._l(t.list,(function(e,r){return o("nuxt-link",{key:r,staticClass:"goods-item bg-white",style:{marginRight:(r+1)%t.num==0?0:"14px"},attrs:{to:"/goods_details/"+(e.id||e.goods_id)}},[o("el-image",{staticClass:"goods-img",attrs:{lazy:"",src:e.image||e.goods_image,alt:""}}),t._v(" "),o("div",{staticClass:"name line-2"},[t._v(t._s(e.name||e.goods_name))]),t._v(" "),"seckill"==t.type?o("div",{staticClass:"seckill flex row-between"},[o("div",{staticClass:"primary flex"},[t._v("\n                秒杀价\n                "),o("price-formate",{attrs:{price:e.seckill_price,"first-size":18}})],1),t._v(" "),o("div",{class:["btn bg-primary white",{"not-start":0==t.status,end:2==t.status}]},[t._v(t._s(t.getSeckillText)+"\n            ")])]):o("div",{staticClass:"flex row-between flex-wrap"},[o("div",{staticClass:"price flex col-baseline"},[o("div",{staticClass:"primary m-r-8"},[o("price-formate",{attrs:{price:e.min_price||e.price,"first-size":16}})],1),t._v(" "),o("div",{staticClass:"muted sm line-through"},[o("price-formate",{attrs:{price:e.market_price}})],1)]),t._v(" "),o("div",{staticClass:"muted xs"},[t._v(t._s(e.sales_total||e.sales_sum||0)+"人购买")])])],1)})),1)}),[],!1,null,"060944d1",null);e.default=component.exports;installComponents(component,{PriceFormate:o(442).default})},464:function(t,e,o){t.exports=o.p+"img/goods_null.38f1689.png"},476:function(t,e,o){t.exports=o.p+"img/coupons_img_receive.d691393.png"},477:function(t,e,o){t.exports=o.p+"img/bg_coupon_s.3f57cfd.png"},478:function(t,e,o){t.exports=o.p+"img/bg_coupon.b22691e.png"},529:function(t,e,o){var content=o(586);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,o(14).default)("54772eb0",content,!0,{sourceMap:!1})},585:function(t,e,o){"use strict";o(529)},586:function(t,e,o){var r=o(13),n=o(188),c=o(477),l=o(478),d=r(!1),f=n(c),h=n(l);d.push([t.i,".shop{width:210px;padding:15px}.shop .logo-img{width:62px;height:62px;border-radius:50%;overflow:hidden}.shop .el-rate__icon{font-size:16px}.h-item{width:82px;height:24px;margin-right:30px;cursor:pointer}.h-item-x{border-radius:100px;background-color:#ff2c3c;color:#fff}.search{width:240px}.search ::v-deep .el-input{width:240px;border-radius:10px}.shop-details{margin-top:10px}.shop-details .left .l-border{padding-bottom:27px;border-bottom:1px solid #eee;margin-bottom:27px}.shop-details .left .desc{color:#101010;font-size:12px}.shop-details .left .desc-b{color:#fff;font-size:12px}.shop-details .left .desc-n{color:#fff;font-size:18px;color:#101010;font-size:14px}.shop-details .left .left-btn{width:82px;height:29px;border-radius:4px;border:1px solid #bbb}.shop-details .left .left-shop{background-color:#fff;padding:20px 15px;width:210px;height:364px}.shop-details .left .r-color-h{background-color:#a4adb3;color:#fff}.shop-details .left .l-tips{padding:1px 2px}.shop-details .left .l-fen{width:210px;height:44px;line-height:20px;color:#101010;font-size:14px;text-align:center;cursor:pointer}.shop-details .left .l-fen-select{color:#ff2c3c}.shop-details .right{width:961px}.shop-details .right .coupon-list{background-color:#fff;padding:20px 0;margin:0 20px;border-bottom:1px solid #eee}.shop-details .right .coupon-list .coupons-more{cursor:pointer}.shop-details .right .coupon-list .swiper-item-c{width:760px;flex-wrap:nowrap;overflow:hidden}.shop-details .right .coupon-list .swiper-item-zk{width:770px;flex-wrap:wrap}.shop-details .right .shop-list{background-color:#fff;height:360px;padding:10px 20px 0}.shop-details .right .shop-list .shop-item{width:200px;height:298px;background-color:#fff;margin-right:12px}.shop-details .right .shop-list .shop-item .name{color:#101010;font-size:14px;text-align:left;margin-bottom:18px}.shop-details .sort{padding:16px 16px 0}.shop-details .sort .sort-name .item{margin-right:30px;cursor:pointer}.shop-details .sort .sort-name .item.active{color:#ff2c3c}.shop-details .swiper-item{width:672px}.shop-details .item{margin-bottom:20px;margin-right:16px;position:relative;cursor:pointer}.shop-details .item .coupon-button{background-color:#f2f2f2;width:240px;height:30px;padding:0 8px}.shop-details .item .info{padding:0 10px;background:url("+f+") no-repeat;width:240px;height:80px;background-size:100%}.shop-details .item .info.gray{background-image:url("+h+")}.shop-details .item .info .info-hd{overflow:hidden}.shop-details .item .tips{position:relative;background-color:#f2f2f2;height:30px;padding:0 8px}.shop-details .item .tips .tips-con{width:100%;left:0;background-color:#f2f2f2;position:absolute;top:30px;padding:10px;z-index:99}.shop-details .item .receice{position:absolute;top:0;right:0;width:58px;height:45px}.shop-details .item .choose{position:absolute;top:0;right:0;background-color:#ffe72c;color:#ff2c3c;padding:1px 5px}.shop-details .more{position:absolute;bottom:20px;cursor:pointer;right:30px}",""]),t.exports=d},658:function(t,e,o){"use strict";o.r(e);var r=o(6),n=(o(51),o(61),o(81),o(38),o(450)),c=(o(96),{head:function(){return{title:this.$store.getters.headTitle,link:[{rel:"icon",type:"image/x-icon",href:this.$store.getters.favicon}]}},layout:"street",components:{},asyncData:function(t){return Object(r.a)(regeneratorRuntime.mark((function e(){var o,r,n,c,i,l,d,f,h,v;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return o=t.$get,r=t.query,e.next=3,o("shop/getShopInfo",{params:{shop_id:r.id}});case 3:if(1==(n=e.sent).code&&n.data.goods_list.length>0)for(c=[],i=0;i<Math.ceil(n.data.goods_list.length/4);i++)d=(l=4*i)+4,c.push(n.data.goods_list.slice(l,d));return console.log("num",c),e.next=8,o("coupon/getCouponList",{params:{shop_id:r.id}});case 8:return f=e.sent,e.next=11,o("shop_goods_category/getShopGoodsCategory",{params:{shop_id:r.id}});case 11:if(1==(h=e.sent).code&&h.data.length>0)for(v=[],i=0;i<Math.ceil(h.data.length/6);i++)d=(l=6*i)+6,v.push(h.data.slice(l,d));return console.log("group",v),e.abrupt("return",{recommend:c,shopInfo:n.data,goodsClassList:h.data,goodsClassListGroup:v,couponsList:f.data.lists});case 15:case"end":return e.stop()}}),e)})))()},data:function(){return{goodsClassListGroup:[],recommend:[],couponsList:[],gClassId:"",shopInfo:[],goodsClassList:[],swiperOptions:{pagination:{el:".swiper-pagination",clickable:!0},navigation:{nextEl:".swiper-button-next",prevEl:".swiper-button-prev"},preventClicks:!0,slidesPerView:"auto",autoplay:!0},sortType:"",saleSort:"desc",priceSort:"desc",page:1,count:0,goodsList:[],more:!1,keyword:"",xuanIndex:""}},created:function(){this.getGoods(),this.changeSortType=Object(n.b)(this.changeSortType,500,this)},methods:{search:function(){this.getGoods(),""==this.xuanIndex&&setTimeout((function(){document.getElementById("goods-sort").scrollIntoView()}),500)},changeXuan:function(t){this.xuanIndex=t,"all"==t?(this.gClassId="",this.getGoods()):(this.gClassId=t,this.getGoods())},getShopData:function(){var t=this;return Object(r.a)(regeneratorRuntime.mark((function e(){var o,r,i,n,c;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,t.$get("shop/getShopInfo",{params:{shop_id:t.$route.query.id}});case 2:if(1==(o=e.sent).code){if(t.shopInfo=o.data,o.data.goods_list.length>0)for(r=[],i=0;i<Math.ceil(o.data.goods_list.length/4);i++)c=(n=4*i)+4,r.push(o.data.goods_list.slice(n,c));console.log("num",r),t.recommend=r}case 4:case"end":return e.stop()}}),e)})))()},shopFollow:function(){this.$post("shop_follow/changeStatus",{shop_id:this.$route.query.id});this.getShopData()},hqCoupon:function(t){var e=this;return Object(r.a)(regeneratorRuntime.mark((function o(){var r,n;return regeneratorRuntime.wrap((function(o){for(;;)switch(o.prev=o.next){case 0:return o.next=2,e.$post("coupon/getCoupon",{coupon_id:t});case 2:r=o.sent,n=r.msg,1==r.code&&e.$message({message:n,type:"success"}),e.getCouponList();case 7:case"end":return o.stop()}}),o)})))()},getCouponList:function(){var t=this;return Object(r.a)(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,t.$get("coupon/getCouponList",{params:{shop_id:t.$route.query.id}});case 2:e.sent;case 3:case"end":return e.stop()}}),e)})))()},getClassGoods:function(t){this.gClassId=t,this.xuanIndex=""==t?"all":t,this.getGoods()},changeSortType:function(t){switch(this.sortType=t,t){case"price":"asc"==this.priceSort?this.priceSort="desc":"desc"==this.priceSort&&(this.priceSort="asc");break;case"sales_sum":"asc"==this.saleSort?this.saleSort="desc":"desc"==this.saleSort&&(this.saleSort="asc")}this.getGoods()},changePage:function(t){this.page=t,this.getGoods()},getGoods:function(){var t=this;return Object(r.a)(regeneratorRuntime.mark((function e(){var o,r,n,c,l,d,f,h;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:t.$route.query.name,o=t.priceSort,r=t.sortType,n=t.saleSort,c="",e.t0=r,e.next="price"===e.t0?6:"sales_sum"===e.t0?8:10;break;case 6:return c=o,e.abrupt("break",10);case 8:return c=n,e.abrupt("break",10);case 10:return e.next=12,t.$get("pc/goodsList",{params:{page_size:20,page_no:t.page,sort_type:r,sort:c,category_id:t.gClassId,shop_id:t.$route.query.id,name:t.keyword}});case 12:l=e.sent,d=l.data,f=d.list,h=d.count,t.count=h,t.goodsList=f;case 18:case"end":return e.stop()}}),e)})))()}}}),l=(o(585),o(9)),component=Object(l.a)(c,(function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",{},[r("div",{staticClass:"bg-white"},[t.shopInfo.banner?r("div",{staticClass:"flex flex-1 row-center col-center",staticStyle:{width:"100%",height:"150px"}},[r("el-image",{staticStyle:{height:"100%",width:"100%","max-width":"1920px"},attrs:{src:t.shopInfo.banner,fit:"cover"}})],1):t._e(),t._v(" "),r("div",{staticClass:"wrapper1180 flex flex-1 col-center row-between",staticStyle:{height:"40px"}},[r("div",{staticClass:"h-item flex row-center",class:""==t.xuanIndex?"h-item-x":"",on:{click:function(e){return t.changeXuan("")}}},[t._v("\n                店铺首页\n            ")]),t._v(" "),r("div",{staticClass:"flex row-left flex-1"},[r("div",{staticClass:"h-item flex row-center",class:"all"==t.xuanIndex?"h-item-x":"",on:{click:function(e){return t.changeXuan("all")}}},[t._v("\n                    全部商品\n                ")]),t._v(" "),r("swiper",{ref:"mySwiper",staticClass:"swiper flex row-left",staticStyle:{width:"672px",display:"flex","justify-content":"flex-start",margin:"0"},attrs:{options:t.swiperOptions}},t._l(t.goodsClassListGroup,(function(e,o){return r("swiper-slide",{key:o,staticClass:"swiper-item flex row-left"},[r("div",{staticClass:"flex"},t._l(e,(function(e,o){return r("div",{key:o},[r("div",{staticClass:"h-item flex row-center",class:t.xuanIndex==e.id?"h-item-x":"",on:{click:function(o){return t.changeXuan(e.id)}}},[t._v("\n                                    "+t._s(e.name)+"\n                                ")])])})),0)])})),1)],1),t._v(" "),r("div",{staticClass:"search"},[r("el-input",{attrs:{placeholder:"店铺搜索",size:"mini"},on:{change:t.search},model:{value:t.keyword,callback:function(e){t.keyword=e},expression:"keyword"}})],1)])]),t._v(" "),r("div",{staticClass:"shop-details flex col-top wrapper1180 flex-1"},[r("div",{staticClass:"left"},[r("div",{staticClass:"shop bg-white"},[r("div",{staticClass:"shop-logo flex-col col-center"},[r("el-image",{staticClass:"logo-img",attrs:{src:t.shopInfo.logo}}),t._v(" "),r("div",{staticClass:"m-t-10"},[1==t.shopInfo.type?r("el-tag",{attrs:{size:"mini"}},[t._v("自营")]):t._e(),t._v(" "),r("span",{staticClass:"weight-500"},[t._v(t._s(t.shopInfo.name))])],1),t._v(" "),r("div",{staticClass:"xs muted m-t-10 line-5"},[t._v("\n                        "+t._s(t.shopInfo.intro)+"\n                    ")])],1),t._v(" "),r("div",{staticClass:"flex m-t-30"},[r("div",{staticClass:"flex-1 text-center"},[r("div",{staticClass:"xxl m-b-10"},[t._v(t._s(t.shopInfo.on_sale_count))]),t._v(" "),r("div",[t._v("全部商品")])]),t._v(" "),r("div",{staticClass:"flex-1 text-center"},[r("div",{staticClass:"xxl m-b-10"},[t._v(t._s(t.shopInfo.visited_num))]),t._v(" "),r("div",[t._v("关注人数")])])]),t._v(" "),r("el-divider"),t._v(" "),r("div",{staticClass:"flex xs m-b-16"},[r("div",{staticClass:"m-r-12"},[t._v("店铺星级")]),t._v(" "),r("div",{staticClass:"m-t-5"},[r("el-rate",{attrs:{disabled:""},model:{value:t.shopInfo.star,callback:function(e){t.$set(t.shopInfo,"star",e)},expression:"shopInfo.star"}})],1)]),t._v(" "),r("div",{staticClass:"flex xs m-b-16"},[r("div",{staticClass:"m-r-12"},[t._v("店铺评分")]),t._v(" "),r("div",{},[t._v(t._s(t.shopInfo.score)+"分")])]),t._v(" "),r("div",{staticClass:"flex row-center row-between"},[r("div",{staticClass:"flex row-center"},[r("el-button",{attrs:{size:"mini"},on:{click:t.shopFollow}},[t._v(t._s(1==t.shopInfo.shop_follow_status?"已关注":"关注店铺"))])],1),t._v(" "),r("el-popover",{attrs:{placement:"bottom",width:"200",trigger:"hover"}},[r("div",[r("el-image",{staticStyle:{width:"100%"},attrs:{src:t.shopInfo.customer_image}})],1),t._v(" "),r("div",{staticClass:"xs lighter text-center",attrs:{slot:"reference"},slot:"reference"},[r("i",{staticClass:"el-icon-chat-dot-round nr"}),t._v(" "),r("span",[t._v("联系客服")])])])],1)],1),t._v(" "),r("div",{staticClass:"m-t-10 bg-white"},[r("div",{staticClass:"l-fen flex row-center",class:""==t.gClassId?"l-fen-select":"",on:{click:function(e){return t.getClassGoods("")}}},[t._v("\n                    全部商品\n                ")]),t._v(" "),t._l(t.goodsClassList,(function(e,o){return r("div",{key:o,on:{click:function(o){return t.getClassGoods(e.id)}}},[r("div",{directives:[{name:"show",rawName:"v-show",value:o<4,expression:"index < 4"}],staticClass:"l-fen flex row-center",class:t.gClassId==e.id?"l-fen-select":""},[t._v("\n                        "+t._s(e.name)+"\n                    ")])])}))],2)]),t._v(" "),r("div",{staticClass:"right m-l-15"},[t.couponsList.length&&""==t.xuanIndex?r("div",{staticClass:"bg-white"},[r("div",{staticClass:"coupon-list"},[r("div",{staticClass:"m-b-10 flex row-between"},[r("div",{},[t._v("\n                            领券\n                        ")]),t._v(" "),t.couponsList.length>3?r("div",{staticClass:"flex row-center coupons-more",on:{click:function(e){t.more=!t.more}}},[r("div",{staticClass:"m-r-5"},[t._v("\n                                更多\n                            ")]),t._v(" "),r("i",{class:t.more?"el-icon-arrow-up":"el-icon-arrow-down"})]):t._e()]),t._v(" "),r("div",{staticClass:"flex",class:t.more?"swiper-item-zk":"swiper-item-c"},t._l(t.couponsList,(function(e,n){return r("div",{key:n,staticClass:"item"},[r("div",{class:["info white",{gray:e.is_get}]},[r("div",{staticClass:"info-hd flex"},[r("div",[r("price-formate",{attrs:{price:e.money,"first-size":38,"second-size":38}})],1),t._v(" "),r("div",{staticClass:"m-l-8 flex1"},[r("div",{staticClass:"line1"},[t._v(t._s(e.name))]),t._v(" "),r("div",{staticClass:"xs line1"},[t._v(t._s(e.condition_type_desc)+"\n                                        ")])])]),t._v(" "),r("div",{staticClass:"info-time xs"},[t._v(t._s(e.user_time_desc))])]),t._v(" "),r("div",{staticClass:"flex row-between coupon-button"},[r("div",{staticClass:"tips-con xs lighter"},[t._v("\n                                    "+t._s(e.use_scene_desc)+"\n                                ")]),t._v(" "),e.is_get?t._e():r("div",{staticClass:"primary sm",on:{click:function(o){return t.hqCoupon(e.id)}}},[t._v("\n                                    立即领取\n                                ")])]),t._v(" "),e.is_get?r("img",{staticClass:"receice",attrs:{src:o(476),alt:""}}):t._e()])})),0)])]):t._e(),t._v(" "),t.recommend&&""==t.xuanIndex?r("div",{staticClass:"shop-list"},[r("div",{staticClass:"m-b-10"},[t._v("\n                    店铺推荐\n                ")]),t._v(" "),r("el-carousel",{attrs:{arrow:"never","indicator-position":"outside",trigger:"click",height:"300px",autoplay:!1}},t._l(t.recommend,(function(e,o){return r("el-carousel-item",{key:o},[r("div",{staticClass:"flex"},t._l(e,(function(e,o){return r("div",{key:o,staticClass:"shop-item"},[r("nuxt-link",{attrs:{to:"/goods_details/"+e.id}},[r("div",{},[r("div",{},[r("el-image",{staticStyle:{height:"200px",width:"200px"},attrs:{src:e.image}})],1),t._v(" "),r("div",{staticClass:"name m-l-10 line-1"},[t._v("\n                                            "+t._s(e.name)+"\n                                        ")]),t._v(" "),r("div",{staticClass:"m-l-10 flex"},[r("div",{staticClass:"primary m-r-8"},[r("price-formate",{attrs:{price:e.min_price,"first-size":16}})],1),t._v(" "),r("div",{staticClass:"muted sm line-through"},[r("price-formate",{attrs:{price:e.market_price}})],1)])])])],1)})),0)])})),1)],1):t._e(),t._v(" "),r("div",{class:""==t.xuanIndex?"m-t-10":""},[r("div",{staticClass:"sort m-b-16 flex bg-white col-top",attrs:{id:"goods-sort"}},[r("div",{staticClass:"sort-title"},[t._v("排序方式：")]),t._v(" "),r("div",{staticClass:"sort-name m-l-16 flex"},[r("div",{class:["item",{active:""==t.sortType}],on:{click:function(e){return t.changeSortType("")}}},[t._v("\n                            综合\n                        ")]),t._v(" "),r("div",{class:["item",{active:"price"==t.sortType}],on:{click:function(e){return t.changeSortType("price")}}},[t._v("\n                            价格\n                            "),r("i",{directives:[{name:"show",rawName:"v-show",value:"desc"==t.priceSort,expression:"priceSort == 'desc'"}],staticClass:"el-icon-arrow-down"}),t._v(" "),r("i",{directives:[{name:"show",rawName:"v-show",value:"asc"==t.priceSort,expression:"priceSort == 'asc'"}],staticClass:"el-icon-arrow-up"})]),t._v(" "),r("div",{class:["item",{active:"sales_sum"==t.sortType}],on:{click:function(e){return t.changeSortType("sales_sum")}}},[t._v("\n                            销量\n                            "),r("i",{directives:[{name:"show",rawName:"v-show",value:"desc"==t.saleSort,expression:"saleSort == 'desc'"}],staticClass:"el-icon-arrow-down"}),t._v(" "),r("i",{directives:[{name:"show",rawName:"v-show",value:"asc"==t.saleSort,expression:"saleSort == 'asc'"}],staticClass:"el-icon-arrow-up"})])])]),t._v(" "),t.goodsList.length?[r("goods-list",{attrs:{list:t.goodsList}}),t._v(" "),t.count?r("div",{staticClass:"pagination flex m-t-30 row-center",staticStyle:{"padding-bottom":"38px"}},[r("el-pagination",{attrs:{background:"",layout:"prev, pager, next",total:t.count,"prev-text":"上一页","next-text":"下一页","hide-on-single-page":"","page-size":20},on:{"current-change":t.changePage}})],1):t._e()]:r("null-data",{attrs:{img:o(464),text:"暂无商品~"}})],2)])])])}),[],!1,null,null,null);e.default=component.exports;installComponents(component,{PriceFormate:o(442).default,GoodsList:o(462).default,NullData:o(448).default})}}]);