-- 创建通用退款记录表
CREATE TABLE `ls_common_refund` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '退款记录ID',
  `refund_sn` varchar(64) NOT NULL COMMENT '退款单号',
  `refund_type` tinyint(1) NOT NULL COMMENT '退款类型：1-商家保证金 2-广告费 3-会员费 4-代理保证金 5-检验费',
  `source_id` int(11) NOT NULL COMMENT '关联的原始记录ID',
  `shop_id` int(11) DEFAULT NULL COMMENT '商家ID',
  `user_id` int(11) DEFAULT NULL COMMENT '用户ID',
  `agent_id` int(11) DEFAULT NULL COMMENT '代理ID',
  `refund_amount` decimal(10, 2) NOT NULL COMMENT '退款金额',
  `total_amount` decimal(10, 2) NOT NULL COMMENT '原始总金额',
  `payment_method` varchar(50) NOT NULL COMMENT '原支付方式',
  `transaction_id` varchar(100) DEFAULT NULL COMMENT '原支付交易号',
  `refund_status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '退款状态：0-退款中 1-退款成功 2-退款失败',
  `refund_msg` text DEFAULT NULL COMMENT '退款结果信息',
  `wechat_refund_id` varchar(100) DEFAULT NULL COMMENT '微信退款单号',
  `admin_id` int(11) DEFAULT NULL COMMENT '操作管理员ID',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_refund_sn` (`refund_sn`),
  KEY `idx_refund_type_source` (`refund_type`, `source_id`),
  KEY `idx_shop_id` (`shop_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_agent_id` (`agent_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='通用退款记录表';
