(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["bundle-pages-cancel_result-cancel_result"],{"0e5a":function(e,t,a){var n=a("24fb");t=n(!1),t.push([e.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */uni-page-body[data-v-5f8e39ed]{background-color:#fff}body.?%PAGE?%[data-v-5f8e39ed]{background-color:#fff}uni-page-body .cancel-result[data-v-5f8e39ed]{padding:%?42?%}uni-page-body .img[data-v-5f8e39ed]{width:%?120?%;height:%?120?%}uni-page-body .success[data-v-5f8e39ed]{display:flex;flex-direction:column;justify-content:center;align-items:center;font-size:%?36?%}uni-page-body .btn[data-v-5f8e39ed]{height:%?80?%;line-height:%?80?%;color:#fff;font-size:%?28?%;position:fixed;bottom:env(safe-area-inset-bottom);bottom:constant(safe-area-inset-bottom);left:%?42?%;right:%?42?%}uni-page-body .fail[data-v-5f8e39ed]{display:flex;flex-direction:column;justify-content:center;align-items:center}uni-page-body .fail .tip[data-v-5f8e39ed]{font-size:%?36?%}uni-page-body .fail-body[data-v-5f8e39ed]{font-size:%?32?%;margin-top:%?50?%}',""]),e.exports=t},1817:function(e,t,a){var n=a("0e5a");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[e.i,n,""]]),n.locals&&(e.exports=n.locals);var i=a("4f06").default;i("83fc4e64",n,!0,{sourceMap:!1,shadowMode:!1})},"3aa3":function(e,t,a){"use strict";a.r(t);var n=a("b60f"),i=a("e4d2");for(var s in i)["default"].indexOf(s)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(s);a("f0fe");var o=a("f0c5"),c=Object(o["a"])(i["default"],n["b"],n["c"],!1,null,"5f8e39ed",null,!1,n["a"],void 0);t["default"]=c.exports},b60f:function(e,t,a){"use strict";a.d(t,"b",(function(){return n})),a.d(t,"c",(function(){return i})),a.d(t,"a",(function(){}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("v-uni-view",{staticClass:"cancel-result"},[1==e.passType?a("v-uni-view",{staticClass:"success"},[a("v-uni-image",{staticClass:"img m-b-40",staticStyle:{"margin-top":"150rpx"},attrs:{src:"/static/images/icon_success.png"}}),a("v-uni-view",{staticClass:"m-t-40 m-b-40"},[e._v("注销完成")]),a("v-uni-view",[e._v("你的所有信息都已清空")]),a("v-uni-button",{staticClass:"btn",staticStyle:{"background-color":"#ff2c3c"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.handleComfirm.apply(void 0,arguments)}}},[e._v("确定")])],1):e._e(),0==e.passType?a("v-uni-view",[a("v-uni-view",{staticClass:"fail"},[a("v-uni-image",{staticClass:"img m-b-20",staticStyle:{"margin-top":"50rpx"},attrs:{src:"/static/images/icon_fail.png"}}),a("v-uni-view",{staticClass:"m-t-40 m-b-20 tip"},[e._v("抱歉，无法注销")]),a("v-uni-view",[e._v("很遗憾，由于以下原因，导致账号无法注销")])],1),a("v-uni-view",{staticStyle:{"margin-top":"50rpx"}},[e.showReason("status")?[a("v-uni-view",{staticClass:"fail-body"},[e._v("账号冻结中")]),a("v-uni-view",{staticClass:"muted"},[e._v("你的账号处于冻结状态，暂时无法申请注销，可联系客服进行处理。")])]:e._e(),e.showReason("order")?[a("v-uni-view",{staticClass:"fail-body"},[e._v("存在未完成订单")]),a("v-uni-view",{staticClass:"muted"},[e._v("发现你的账号内有未处理完成的商城订单，待完成后可重新提交申请。")])]:e._e(),e.showReason("after_sale")?[a("v-uni-view",{staticClass:"fail-body"},[e._v("存在售后中订单")]),a("v-uni-view",{staticClass:"muted"},[e._v("发现你的账号内有订单处于售后中，待商家处理完成后可重新提交申请。")])]:e._e(),e.showReason("withdraw")?[a("v-uni-view",{staticClass:"fail-body"},[e._v("存在佣金待提现申请")]),a("v-uni-view",{staticClass:"muted"},[e._v("发现你的账号内有佣金处于待提现中，待商家处理完成后可重新提交申请。")])]:e._e()],2),a("v-uni-button",{staticClass:"btn",staticStyle:{"background-color":"#ff2c3c"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.handleBack.apply(void 0,arguments)}}},[e._v("返回个人中心")])],1):e._e()],1)},i=[]},c255:function(e,t,a){"use strict";a("7a82");var n=a("ee27").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=n(a("f07e")),s=n(a("c964"));a("d3b7"),a("14d9"),a("ac1f"),a("5319"),a("5b81");var o=a("8516"),c=n(a("c094")),u={data:function(){return{passType:"",reason:[]}},computed:{showReason:function(){var e=this;return function(t){return e.reason.some((function(e){return e==t}))}}},methods:{handleReason:function(e){for(var t in e)0==e[t]["pass"]&&this.reason.push(t)},handleComfirm:function(){this.$Router.replaceAll({path:"/pages/index/index"})},handleBack:function(){this.$Router.replaceAll({path:"/pages/user/user"})}},onLoad:function(){var e=this;return(0,s.default)((0,i.default)().mark((function t(){var a,n,s;return(0,i.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(a=e.$Route.query.pass,e.passType=a,1!=a){t.next=4;break}return t.abrupt("return",c.default.commit("logout"));case 4:return t.next=6,(0,o.apiuserDeletecheck)();case 6:n=t.sent,s=n.data,e.handleReason(s.data);case 9:case"end":return t.stop()}}),t)})))()}};t.default=u},e4d2:function(e,t,a){"use strict";a.r(t);var n=a("c255"),i=a.n(n);for(var s in n)["default"].indexOf(s)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(s);t["default"]=i.a},f0fe:function(e,t,a){"use strict";var n=a("1817"),i=a.n(n);i.a}}]);