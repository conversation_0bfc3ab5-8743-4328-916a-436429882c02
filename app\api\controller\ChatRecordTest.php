<?php

namespace app\api\controller;

use app\common\basics\ApiBase;
use app\api\logic\UserLogic;
use app\common\server\JsonServer;

/**
 * 聊天记录接口测试控制器
 * Class ChatRecordTest
 * @package app\api\controller
 */
class ChatRecordTest extends ApiBase
{
    /**
     * 测试获取聊天记录接口（新版本）
     * @return \think\response\Json
     */
    public function testNewChatRecord()
    {
        try {
            $to_id = $this->request->get('to_id/d', 0);
            $shop_id = $this->request->get('shop_id/d', 0);
            $page = $this->request->get('page/d', 1);
            $size = $this->request->get('size/d', 20);

            $user_id = $this->user_id;

            $result = UserLogic::getChatRecord($user_id, $to_id, $shop_id, $page, $size);

            // 判断聊天类型
            $chat_type = '';
            if (!empty($shop_id)) {
                $chat_type = 'kefu_chat';
            } elseif (!empty($to_id)) {
                $chat_type = 'user_chat';
            } else {
                $chat_type = 'empty';
            }

            return JsonServer::success('获取成功', [
                'request_params' => [
                    'user_id' => $user_id,
                    'to_id' => $to_id,
                    'shop_id' => $shop_id,
                    'page' => $page,
                    'size' => $size
                ],
                'chat_type' => $chat_type,
                'data' => $result
            ]);
        } catch (\Exception $e) {
            return JsonServer::error('获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 测试用户对用户聊天记录
     * @return \think\response\Json
     */
    public function testUserChat()
    {
        try {
            $to_id = $this->request->get('to_id/d', 0);
            $page = $this->request->get('page/d', 1);
            $size = $this->request->get('size/d', 20);
            $user_id = $this->user_id;

            if (empty($to_id)) {
                return JsonServer::error('to_id参数不能为空');
            }

            $result = UserLogic::getChatRecord($user_id, $to_id, 0, $page, $size);

            return JsonServer::success('获取用户聊天记录成功', $result);
        } catch (\Exception $e) {
            return JsonServer::error('获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 测试客服聊天记录
     * @return \think\response\Json
     */
    public function testKefuChat()
    {
        try {
            $shop_id = $this->request->get('shop_id/d', 0);
            $page = $this->request->get('page/d', 1);
            $size = $this->request->get('size/d', 20);
            $user_id = $this->user_id;

            if (empty($shop_id)) {
                return JsonServer::error('shop_id参数不能为空');
            }

            $result = UserLogic::getChatRecord($user_id, 0, $shop_id, $page, $size);

            return JsonServer::success('获取客服聊天记录成功', $result);
        } catch (\Exception $e) {
            return JsonServer::error('获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 测试空参数情况
     * @return \think\response\Json
     */
    public function testEmptyParams()
    {
        try {
            $page = $this->request->get('page/d', 1);
            $size = $this->request->get('size/d', 20);
            $user_id = $this->user_id;

            $result = UserLogic::getChatRecord($user_id, 0, 0, $page, $size);

            return JsonServer::success('获取空参数结果成功', $result);
        } catch (\Exception $e) {
            return JsonServer::error('获取失败: ' . $e->getMessage());
        }
    }
}
