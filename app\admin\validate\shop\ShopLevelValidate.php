<?php
namespace app\admin\validate\shop;

use think\Validate;

class ShopLevelValidate extends Validate
{
    protected $rule = [

        'name' => 'require',
        'image' => 'require',
//        'discount2' => 'require',
    ];

    protected $message = [
        'name.require' => '请输入等级名称',
//        'discount2.require' => '年费必填',
//        'discount2.integer' => '年费必须为整数',
//        'discount2.egt' => '年费必须大于或等于0',
        'image.require' => '请选择等级图标',
    ];

    public function sceneAdd() {
        return $this->only(['name', 'image']);
    }

    public function sceneEdit() {
        return $this->only(['id', 'name',  'image']);
    }
}