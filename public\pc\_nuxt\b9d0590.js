(window.webpackJsonp=window.webpackJsonp||[]).push([[21],{473:function(t,e,r){"use strict";var n=r(14),o=r(4),c=r(5),l=r(141),f=r(24),d=r(18),h=r(290),v=r(54),m=r(104),x=r(289),N=r(3),_=r(105).f,I=r(45).f,y=r(23).f,E=r(474),S=r(475).trim,w="Number",k=o.Number,A=k.prototype,C=o.TypeError,T=c("".slice),F=c("".charCodeAt),M=function(t){var e=x(t,"number");return"bigint"==typeof e?e:O(e)},O=function(t){var e,r,n,o,c,l,f,code,d=x(t,"number");if(m(d))throw C("Cannot convert a Symbol value to a number");if("string"==typeof d&&d.length>2)if(d=S(d),43===(e=F(d,0))||45===e){if(88===(r=F(d,2))||120===r)return NaN}else if(48===e){switch(F(d,1)){case 66:case 98:n=2,o=49;break;case 79:case 111:n=8,o=55;break;default:return+d}for(l=(c=T(d,2)).length,f=0;f<l;f++)if((code=F(c,f))<48||code>o)return NaN;return parseInt(c,n)}return+d};if(l(w,!k(" 0o1")||!k("0b1")||k("+0x1"))){for(var R,V=function(t){var e=arguments.length<1?0:k(M(t)),r=this;return v(A,r)&&N((function(){E(r)}))?h(Object(e),r,V):e},G=n?_(k):"MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,EPSILON,MAX_SAFE_INTEGER,MIN_SAFE_INTEGER,isFinite,isInteger,isNaN,isSafeInteger,parseFloat,parseInt,fromString,range".split(","),L=0;G.length>L;L++)d(k,R=G[L])&&!d(V,R)&&y(V,R,I(k,R));V.prototype=A,A.constructor=V,f(o,w,V,{constructor:!0})}},474:function(t,e,r){var n=r(5);t.exports=n(1..valueOf)},475:function(t,e,r){var n=r(5),o=r(36),c=r(19),l=r(476),f=n("".replace),d="["+l+"]",h=RegExp("^"+d+d+"*"),v=RegExp(d+d+"*$"),m=function(t){return function(e){var r=c(o(e));return 1&t&&(r=f(r,h,"")),2&t&&(r=f(r,v,"")),r}};t.exports={start:m(1),end:m(2),trim:m(3)}},476:function(t,e){t.exports="\t\n\v\f\r                　\u2028\u2029\ufeff"},508:function(t,e,r){var content=r(520);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,r(17).default)("b0c05808",content,!0,{sourceMap:!1})},519:function(t,e,r){"use strict";r(508)},520:function(t,e,r){var n=r(16)(!1);n.push([t.i,".shop-item[data-v-871c1244]{width:270px;height:400px;background-size:cover;background-position:50%;padding:10px;border-radius:6px}.shop-item .shop-info[data-v-871c1244]{border-radius:6px;padding:18px 15px}.shop-item .shop-info .logo[data-v-871c1244]{width:70px;height:70px;border-radius:16px;margin-top:-45px}.shop-item .shop-info .sales[data-v-871c1244]{display:inline-block;padding:4px 10px;background-color:#f2f2f2;margin-top:6px;border-radius:4px}",""]),t.exports=n},536:function(t,e,r){"use strict";r.r(e);r(29),r(473);var n={components:{},props:{cover:{type:String},shopId:{type:[String,Number]},logo:{type:String},type:{type:[String,Number]},name:{type:String},sales:{type:[String,Number]}},methods:{}},o=(r(519),r(8)),component=Object(o.a)(n,(function(){var t=this,e=t._self._c;return e("nuxt-link",{staticClass:"shop-item flex-col row-right",style:{"background-image":"url(".concat(t.cover,")")},attrs:{to:"/shop_street_detail?id=".concat(t.shopId)}},[e("div",{staticClass:"bg-white shop-info text-center"},[e("el-image",{staticClass:"logo",attrs:{src:t.logo}}),t._v(" "),e("div",{staticClass:"m-t-12 line-1 lg"},[1==t.type?e("el-tag",{attrs:{size:"mini"}},[t._v("自营")]):t._e(),t._v(" "+t._s(t.name)+"\n        ")],1),t._v(" "),e("span",{staticClass:"xs muted sales"},[t._v("共"+t._s(t.sales)+"件商品")])],1)])}),[],!1,null,"871c1244",null);e.default=component.exports}}]);