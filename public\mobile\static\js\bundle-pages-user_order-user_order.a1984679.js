(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["bundle-pages-user_order-user_order","bundle-pages-after_sales_detail-after_sales_detail~bundle-pages-contact_offical-contact_offical~bund~a5ef3ab8","bundle-pages-invoice_detail-invoice_detail~bundle-pages-order_details-order_details~bundle-pages-use~f8b4506c"],{"069f":function(t,e,i){"use strict";i("7a82");var n=i("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o=n(i("f07e")),r=n(i("c964"));i("a9e3");var a={props:{type:Number,orderId:[Number,String]},data:function(){return{show:!1}},methods:{open:function(){this.show=!0},close:function(){this.show=!1},onConfirm:function(){var t=this;return(0,r.default)((0,o.default)().mark((function e(){return(0,o.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.type,t.orderId,null,t.$emit("confirm");case 3:case"end":return e.stop()}}),e)})))()}},computed:{getTipsText:function(){var t=this.type;switch(t){case 0:return"确认取消订单吗？";case 1:return"确认删除订单吗?";case 2:return"确认收货吗?"}}}};e.default=a},"073a":function(t,e,i){"use strict";var n=i("07aa"),o=i.n(n);o.a},"07aa":function(t,e,i){var n=i("b1d4");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var o=i("4f06").default;o("3abbc265",n,!0,{sourceMap:!1,shadowMode:!1})},1377:function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=i("b08d"),o={name:"float-tab",data:function(){return{showMore:!1,top:0}},mounted:function(){var t=this;(0,n.getRect)(".tab-img",!1,this).then((function(e){t.height=e.height,console.log(t.height)}))},methods:{onChange:function(){this.showMore=!this.showMore}},watch:{showMore:function(t){this.top=t?-this.height:0}}};e.default=o},1522:function(t,e,i){"use strict";i.d(e,"b",(function(){return o})),i.d(e,"c",(function(){return r})),i.d(e,"a",(function(){return n}));var n={uIcon:i("90f3").default},o=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"u-image",style:[t.wrapStyle,t.backgroundStyle],on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onClick.apply(void 0,arguments)}}},[t.isError?t._e():i("v-uni-image",{staticClass:"u-image__image",style:{borderRadius:"circle"==t.shape?"50%":t.$u.addUnit(t.borderRadius)},attrs:{src:t.src,mode:t.mode,"lazy-load":t.lazyLoad},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.onErrorHandler.apply(void 0,arguments)},load:function(e){arguments[0]=e=t.$handleEvent(e),t.onLoadHandler.apply(void 0,arguments)}}}),t.showLoading&&t.loading?i("v-uni-view",{staticClass:"u-image__loading",style:{borderRadius:"circle"==t.shape?"50%":t.$u.addUnit(t.borderRadius),backgroundColor:this.bgColor}},[t.$slots.loading?t._t("loading"):i("u-icon",{attrs:{name:t.loadingIcon,width:t.width,height:t.height}})],2):t._e(),t.showError&&t.isError&&!t.loading?i("v-uni-view",{staticClass:"u-image__error",style:{borderRadius:"circle"==t.shape?"50%":t.$u.addUnit(t.borderRadius)}},[t.$slots.error?t._t("error"):i("u-icon",{attrs:{name:t.errorIcon,width:t.width,height:t.height}})],2):t._e()],1)},r=[]},"1b40":function(t,e,i){"use strict";i.r(e);var n=i("2946"),o=i.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(r);e["default"]=o.a},"1c02":function(t,e,i){var n=i("24fb");e=n(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.btn[data-v-0c40d2f2]{width:%?200?%;background-color:#ff2c3c;border-radius:%?20?%;color:#fff;margin:%?100?% auto}',""]),t.exports=e},"1c75":function(t,e,i){"use strict";var n=i("7a54"),o=i.n(n);o.a},"1cbc":function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("a9e3");var n={name:"u-tag",props:{type:{type:String,default:"primary"},disabled:{type:[Boolean,String],default:!1},size:{type:String,default:"default"},shape:{type:String,default:"square"},text:{type:[String,Number],default:""},bgColor:{type:String,default:""},color:{type:String,default:""},borderColor:{type:String,default:""},closeColor:{type:String,default:""},index:{type:[Number,String],default:""},mode:{type:String,default:"light"},closeable:{type:Boolean,default:!1},show:{type:Boolean,default:!0}},data:function(){return{}},computed:{customStyle:function(){var t={};return this.color&&(t.color=this.color),this.bgColor&&(t.backgroundColor=this.bgColor),"plain"==this.mode&&this.color&&!this.borderColor?t.borderColor=this.color:t.borderColor=this.borderColor,t},iconStyle:function(){if(this.closeable){var t={};return"mini"==this.size?t.fontSize="20rpx":t.fontSize="22rpx","plain"==this.mode||"light"==this.mode?t.color=this.type:"dark"==this.mode&&(t.color="#ffffff"),this.closeColor&&(t.color=this.closeColor),t}},closeIconColor:function(){return this.closeColor?this.closeColor:this.color?this.color:"dark"==this.mode?"#ffffff":this.type}},methods:{clickTag:function(){this.disabled||this.$emit("click",this.index)},close:function(){this.$emit("close",this.index)}}};e.default=n},"1ce8":function(t,e,i){"use strict";var n=i("66bf"),o=i.n(n);o.a},2028:function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("a9e3");var n={name:"u-modal",props:{value:{type:Boolean,default:!1},zIndex:{type:[Number,String],default:""},title:{type:[String],default:"提示"},width:{type:[Number,String],default:600},content:{type:String,default:"内容"},showTitle:{type:Boolean,default:!0},showConfirmButton:{type:Boolean,default:!0},showCancelButton:{type:Boolean,default:!1},confirmText:{type:String,default:"确认"},cancelText:{type:String,default:"取消"},confirmColor:{type:String,default:"#2979ff"},cancelColor:{type:String,default:"#606266"},borderRadius:{type:[Number,String],default:16},titleStyle:{type:Object,default:function(){return{}}},contentStyle:{type:Object,default:function(){return{}}},cancelStyle:{type:Object,default:function(){return{}}},confirmStyle:{type:Object,default:function(){return{}}},zoom:{type:Boolean,default:!0},asyncClose:{type:Boolean,default:!1},maskCloseAble:{type:Boolean,default:!1},negativeTop:{type:[String,Number],default:0}},data:function(){return{loading:!1}},computed:{cancelBtnStyle:function(){return Object.assign({color:this.cancelColor},this.cancelStyle)},confirmBtnStyle:function(){return Object.assign({color:this.confirmColor},this.confirmStyle)},uZIndex:function(){return this.zIndex?this.zIndex:this.$u.zIndex.popup}},watch:{value:function(t){!0===t&&(this.loading=!1)}},methods:{confirm:function(){this.asyncClose?this.loading=!0:this.$emit("input",!1),this.$emit("confirm")},cancel:function(){var t=this;this.$emit("cancel"),this.$emit("input",!1),setTimeout((function(){t.loading=!1}),300)},popupClose:function(){this.$emit("input",!1)},clearLoading:function(){this.loading=!1}}};e.default=n},2322:function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("a9e3");var n={name:"u-image",props:{src:{type:String,default:""},mode:{type:String,default:"aspectFill"},width:{type:[String,Number],default:"100%"},height:{type:[String,Number],default:"auto"},shape:{type:String,default:"square"},borderRadius:{type:[String,Number],default:0},lazyLoad:{type:Boolean,default:!0},showMenuByLongpress:{type:Boolean,default:!0},loadingIcon:{type:String,default:"photo"},errorIcon:{type:String,default:"error-circle"},showLoading:{type:Boolean,default:!0},showError:{type:Boolean,default:!0},fade:{type:Boolean,default:!0},webp:{type:Boolean,default:!1},duration:{type:[String,Number],default:500},bgColor:{type:String,default:"#f3f4f6"}},data:function(){return{isError:!1,loading:!0,opacity:1,durationTime:this.duration,backgroundStyle:{}}},watch:{src:{immediate:!0,handler:function(t){t?this.isError=!1:(this.isError=!0,this.loading=!1)}}},computed:{wrapStyle:function(){var t={};return t.width=this.$u.addUnit(this.width),t.height=this.$u.addUnit(this.height),t.borderRadius="circle"==this.shape?"50%":this.$u.addUnit(this.borderRadius),t.overflow=this.borderRadius>0?"hidden":"visible",this.fade&&(t.opacity=this.opacity,t.transition="opacity ".concat(Number(this.durationTime)/1e3,"s ease-in-out")),t}},methods:{onClick:function(){this.$emit("click")},onErrorHandler:function(t){this.loading=!1,this.isError=!0,this.$emit("error",t)},onLoadHandler:function(){var t=this;if(this.loading=!1,this.isError=!1,this.$emit("load"),!this.fade)return this.removeBgColor();this.opacity=0,this.durationTime=0,setTimeout((function(){t.durationTime=t.duration,t.opacity=1,setTimeout((function(){t.removeBgColor()}),t.durationTime)}),50)},removeBgColor:function(){this.backgroundStyle={backgroundColor:"transparent"}}}};e.default=n},2698:function(t,e,i){"use strict";var n=i("5bed"),o=i.n(n);o.a},2875:function(t,e,i){"use strict";i.r(e);var n=i("a712"),o=i("c13e");for(var r in o)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(r);i("8fed");var a=i("f0c5"),s=Object(a["a"])(o["default"],n["b"],n["c"],!1,null,"061dd044",null,!1,n["a"],void 0);e["default"]=s.exports},2946:function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("c740"),i("14d9");var n=i("98a1"),o={data:function(){return{active:-1,order:[{name:"全部",type:n.orderType.ALL},{name:"待付款",type:n.orderType.PAY},{name:"待收货",type:n.orderType.DELIVERY},{name:"已完成",type:n.orderType.FINISH},{name:"已关闭",type:n.orderType.CLOSE}]}},onLoad:function(t){var e=this.order,i=this.$Route.query.type||n.orderType.ALL,o=e.findIndex((function(t){return t.type==i}));this.changeShow(o)},methods:{changeShow:function(t){var e=this;-1!=t&&this.$nextTick((function(){e.active=t}))},handlelogin:function(){this.$Router.push("/pages/login/login")}}};e.default=o},"2aaa":function(t,e,i){"use strict";i.d(e,"b",(function(){return o})),i.d(e,"c",(function(){return r})),i.d(e,"a",(function(){return n}));var n={mescrollUni:i("0bbb").default,uTag:i("8219").default,shopTitle:i("95a6").default,orderGoods:i("d9ab").default,priceFormat:i("fefe").default,uCountDown:i("7226").default,orderDialog:i("a7f3").default,loadingView:i("2875").default},o=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("mescroll-uni",{ref:"mescrollRef",attrs:{top:"80rpx",down:t.downOption,up:t.upOption},on:{init:function(e){arguments[0]=e=t.$handleEvent(e),t.mescrollInit.apply(void 0,arguments)},down:function(e){arguments[0]=e=t.$handleEvent(e),t.downCallback.apply(void 0,arguments)},up:function(e){arguments[0]=e=t.$handleEvent(e),t.upCallback.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"order-list"},t._l(t.orderList,(function(e,n){return i("v-uni-view",{key:n,staticClass:"order-item bg-white m-t-20"},[i("router-link",{attrs:{to:{path:"/bundle/pages/order_details/order_details",query:{id:e.id}}}},[i("v-uni-view",{staticClass:"order-header flex row-between"},[i("v-uni-view",{staticClass:"flex"},[1==e.order_type?i("v-uni-view",{staticClass:"m-r-10"},[i("u-tag",{attrs:{text:"秒杀",size:"mini",type:"primary",mode:"plain"}})],1):t._e(),2==e.order_type?i("v-uni-view",{staticClass:"m-r-10"},[i("u-tag",{attrs:{text:"拼团",size:"mini",type:"primary",mode:"plain"}})],1):t._e(),3==e.order_type?i("v-uni-view",{staticClass:"m-r-10"},[i("u-tag",{attrs:{text:"砍价",size:"mini",type:"primary",mode:"plain"}})],1):t._e(),2==e.delivery_type?i("v-uni-view",{staticClass:"m-r-10"},[i("u-tag",{attrs:{text:"自提",size:"mini",type:"success",mode:"dark"}})],1):t._e()],1),i("shop-title",{attrs:{shop:e.shop}}),4!=e.pay_way?i("v-uni-view",{class:4==e.order_status?"muted":"primary"},[2!=e.delivery_type?[t._v(t._s(t.getOrderStatus(e.order_status)))]:[t._v(t._s(e.order_status_desc))]],2):i("v-uni-view",{class:4==e.order_status?"muted":"primary"},[0==e.order_status?[t._v("线下支付")]:[2!=e.delivery_type?[t._v(t._s(t.getOrderStatus(e.order_status)))]:[t._v(t._s(e.order_status_desc))]]],2)],1),i("v-uni-view",{staticClass:"order-con"},[i("order-goods",{attrs:{list:e.order_goods,isJumpGoods:!1}}),t.orderstatusText(e)?i("v-uni-view",{staticClass:"m-20 p-20",staticStyle:{"background-color":"#f6f6f6","border-radius":"5px"}},[i("span",{staticClass:"m-r-24"},[t._v(t._s(t.orderstatusTitle(e)))]),i("span",{staticClass:"muted"},[t._v(t._s(t.orderstatusText(e)))])]):t._e(),i("v-uni-view",{staticClass:"all-price flex row-right"},[i("v-uni-text",{staticClass:"muted xs"},[t._v("共"+t._s(t.handlegoodCount(e))+"件商品，实付款：")]),i("price-format",{attrs:{weight:"500","subscript-size":30,"first-size":30,"second-size":30,price:e.order_amount}})],1)],1),e.cancel_btn||e.delivery_btn||e.take_btn||e.del_btn||e.pay_btn||e.comment_btn||e.pickup_btn?i("v-uni-view",{staticClass:"order-footer flex"},[i("v-uni-view",{staticStyle:{flex:"1"}},[t.getCancelTime(e.order_cancel_time)>0?i("v-uni-view",{staticClass:"primary flex sm"},[i("u-count-down",{attrs:{separator:"zh",timestamp:t.getCancelTime(e.order_cancel_time),"separator-color":t.colorConfig.primary,color:t.colorConfig.primary,"separator-size":26,"font-size":26,"bg-color":"transparent"},on:{end:function(e){arguments[0]=e=t.$handleEvent(e),t.refresh.apply(void 0,arguments)}}})],1):t._e()],1),e.cancel_btn?i("v-uni-view",[i("v-uni-button",{staticClass:"plain br60 lighter",attrs:{size:"sm","hover-class":"none"},on:{click:function(i){i.stopPropagation(),arguments[0]=i=t.$handleEvent(i),t.cancelOrder(e.id)}}},[t._v("取消订单")])],1):t._e(),e.delivery_btn?i("v-uni-view",{on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e)}}},[i("router-link",{attrs:{to:{path:"/bundle/pages/goods_logistics/goods_logistics",query:{id:e.id}}}},[i("v-uni-button",{staticClass:"btn plain br60 lighter",attrs:{size:"sm","hover-class":"none"}},[t._v("查看物流")])],1)],1):t._e(),e.content_btn?i("v-uni-view",{staticClass:"m-l-20",on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e)}}},[i("router-link",{attrs:{to:{path:"/bundle/pages/order_details/order_details",query:{id:e.id}}}},[i("v-uni-button",{staticClass:"btn plain br60 lighter",attrs:{size:"sm","hover-class":"none"}},[t._v("查看内容")])],1)],1):t._e(),e.pickup_btn?i("v-uni-view",{staticClass:"m-l-20",on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e)}}},[i("router-link",{attrs:{to:{path:"/bundle/pages/order_details/order_details",query:{id:e.id}}}},[i("v-uni-button",{staticClass:"btn bg-primary plain br60 white",staticStyle:{border:"none"},attrs:{size:"sm","hover-class":"none"}},[t._v("查看取货码")])],1)],1):t._e(),e.del_btn?i("v-uni-view",[i("v-uni-button",{staticClass:"btn plain br60 lighter",attrs:{size:"sm","hover-class":"none"},on:{click:function(i){i.stopPropagation(),arguments[0]=i=t.$handleEvent(i),t.delOrder(e.id)}}},[t._v("删除订单")])],1):t._e(),e.pay_btn&&4!=e.pay_way?i("v-uni-view",{staticClass:"m-l-20"},[i("v-uni-button",{staticClass:"btn bg-primary br60 white",attrs:{size:"sm"},on:{click:function(i){i.stopPropagation(),arguments[0]=i=t.$handleEvent(i),t.payNow(e.id)}}},[t._v("立即付款")])],1):t._e(),e.comment_btn?i("v-uni-view",{staticClass:"m-l-20"},[i("v-uni-button",{staticClass:"btn plain btn br60 primary red",attrs:{size:"sm","hover-class":"none"}},[t._v("去评价")])],1):t._e(),e.take_btn?i("v-uni-view",{staticClass:"m-l-20"},[i("v-uni-button",{staticClass:"btn plain br60 primary red",attrs:{size:"sm","hover-class":"none"},on:{click:function(i){i.stopPropagation(),arguments[0]=i=t.$handleEvent(i),t.comfirmOrder(e.id,e.pay_way_base)}}},[t._v("确认收货")])],1):t._e()],1):t._e()],1)],1)})),1),i("order-dialog",{ref:"orderDialog",attrs:{"order-id":t.orderId,type:t.type},on:{confirm:function(e){arguments[0]=e=t.$handleEvent(e),t.confirmDialog.apply(void 0,arguments)}}}),t.showLoading?i("loading-view",{attrs:{"background-color":"transparent",size:50}}):t._e()],1)},r=[]},"2ab4":function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){}));var n=function(){var t=this.$createElement,e=this._self._c||t;return this.show?e("v-uni-view",{staticClass:"u-loading",class:"circle"==this.mode?"u-loading-circle":"u-loading-flower",style:[this.cricleStyle]}):this._e()},o=[]},"2f9a":function(t,e,i){"use strict";i.r(e);var n=i("ec1d"),o=i.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(r);e["default"]=o.a},"356b":function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("a9e3");var n={name:"u-loading",props:{mode:{type:String,default:"circle"},color:{type:String,default:"#c7c7c7"},size:{type:[String,Number],default:"34"},show:{type:Boolean,default:!0}},computed:{cricleStyle:function(){var t={};return t.width=this.size+"rpx",t.height=this.size+"rpx","circle"==this.mode&&(t.borderColor="#e4e4e4 #e4e4e4 #e4e4e4 ".concat(this.color?this.color:"#c7c7c7")),t}}};e.default=n},"35b3":function(t,e,i){var n=i("8a6b");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var o=i("4f06").default;o("4b52402e",n,!0,{sourceMap:!1,shadowMode:!1})},"3c87":function(t,e,i){"use strict";i.d(e,"b",(function(){return o})),i.d(e,"c",(function(){return r})),i.d(e,"a",(function(){return n}));var n={uModal:i("8d42").default},o=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("u-modal",{attrs:{"show-cancel-button":!0,content:t.getTipsText,"confirm-color":"#ff2c3c"},on:{confirm:function(e){arguments[0]=e=t.$handleEvent(e),t.onConfirm.apply(void 0,arguments)}},model:{value:t.show,callback:function(e){t.show=e},expression:"show"}})},r=[]},4316:function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("a9e3");var n={props:{i:Number,index:{type:Number,default:function(){return 0}}},data:function(){return{downOption:{auto:!1},upOption:{auto:!1},isInit:!1}},watch:{index:function(t){this.i!==t||this.isInit||(this.isInit=!0,this.mescroll&&this.mescroll.triggerDownScroll())}},methods:{mescrollInitByRef:function(){if(!this.mescroll||!this.mescroll.resetUpScroll){var t=this.$refs.mescrollRef||this.$refs["mescrollRef"+this.i];t&&(this.mescroll=t.mescroll)}},mescrollInit:function(t){this.mescroll=t,this.mescrollInitByRef&&this.mescrollInitByRef(),this.i===this.index&&(this.isInit=!0,this.mescroll.triggerDownScroll())}}},o=n;e.default=o},"45ee":function(t,e,i){var n=i("24fb");e=n(!1),e.push([t.i,".price-format[data-v-0a5a34e0]{font-family:Avenir,SourceHanSansCN,PingFang SC,Arial,Hiragino Sans GB,Microsoft YaHei,sans-serif}",""]),t.exports=e},"46d6":function(t,e,i){var n=i("24fb");e=n(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.shop-title[data-v-705e9a38]{height:%?80?%;flex:1;min-width:0}.shop-title .tag[data-v-705e9a38]{background:linear-gradient(267deg,#ff2c3c,#f52e99);border-radius:%?6?%;padding:%?5?% %?9?%}',""]),t.exports=e},"4d65":function(t,e,i){"use strict";i.r(e);var n=i("069f"),o=i.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(r);e["default"]=o.a},"59f5":function(t,e,i){"use strict";i.r(e);var n=i("2028"),o=i.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(r);e["default"]=o.a},"5a16":function(t,e,i){"use strict";i.r(e);var n=i("b510"),o=i("1b40");for(var r in o)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(r);i("6179");var a=i("f0c5"),s=Object(a["a"])(o["default"],n["b"],n["c"],!1,null,"0c40d2f2",null,!1,n["a"],void 0);e["default"]=s.exports},"5bed":function(t,e,i){var n=i("46d6");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var o=i("4f06").default;o("50d1694e",n,!0,{sourceMap:!1,shadowMode:!1})},6011:function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("14d9");var n={data:function(){return{}},components:{},props:{list:{type:Array,default:function(){return[]}},link:{type:Boolean,default:!1},isJumpGoods:{type:Boolean,default:!1}},created:function(){var t=this;setTimeout((function(){console.log(t.list)}),700)},methods:{jumpGoods:function(t){this.isJumpGoods&&this.$Router.push({path:"/pages/goods_details/goods_details?id=",query:{id:t.goods_id}})}}};e.default=n},6017:function(t,e,i){"use strict";var n=i("66cc"),o=i.n(n);o.a},6021:function(t,e,i){"use strict";var n=i("64b1"),o=i.n(n);o.a},6179:function(t,e,i){"use strict";var n=i("638a"),o=i.n(n);o.a},"638a":function(t,e,i){var n=i("1c02");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var o=i("4f06").default;o("49dd4fdc",n,!0,{sourceMap:!1,shadowMode:!1})},"64b1":function(t,e,i){var n=i("6e35");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var o=i("4f06").default;o("3d1e497c",n,!0,{sourceMap:!1,shadowMode:!1})},"65c2":function(t,e,i){"use strict";i.r(e);var n=i("ad6f"),o=i("7c75");for(var r in o)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(r);i("9415");var a=i("f0c5"),s=Object(a["a"])(o["default"],n["b"],n["c"],!1,null,"130bc95c",null,!1,n["a"],void 0);e["default"]=s.exports},"66bf":function(t,e,i){var n=i("f9ff");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var o=i("4f06").default;o("19b0e23e",n,!0,{sourceMap:!1,shadowMode:!1})},"66cc":function(t,e,i){var n=i("ae16");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var o=i("4f06").default;o("3ee5a69c",n,!0,{sourceMap:!1,shadowMode:!1})},"6d79":function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("a9e3"),i("acd8");var n={data:function(){return{priceSlice:{}}},components:{},props:{firstSize:{type:[String,Number],default:28},secondSize:{type:[String,Number],default:28},color:{type:String},weight:{type:[String,Number],default:400},price:{type:[String,Number],default:""},showSubscript:{type:Boolean,default:!0},subscriptSize:{type:[String,Number],default:28},lineThrough:{type:Boolean,default:!1}},created:function(){this.priceFormat()},watch:{price:function(t){this.priceFormat()}},methods:{priceFormat:function(){var t=this.price,e={};null!==t&&""!==t&&void 0!==t?(t=parseFloat(t),t=String(t).split("."),e.first=t[0],e.second=t[1],this.priceSlice=e):this.priceSlice={first:0}}}};e.default=n},"6e35":function(t,e,i){var n=i("24fb");e=n(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.u-image[data-v-1bf07c9a]{position:relative;transition:opacity .5s ease-in-out}.u-image__image[data-v-1bf07c9a]{width:100%;height:100%}.u-image__loading[data-v-1bf07c9a], .u-image__error[data-v-1bf07c9a]{position:absolute;top:0;left:0;width:100%;height:100%;display:flex;flex-direction:row;align-items:center;justify-content:center;background-color:#f3f4f6;color:#909399;font-size:%?46?%}',""]),t.exports=e},7226:function(t,e,i){"use strict";i.r(e);var n=i("7bab"),o=i("2f9a");for(var r in o)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(r);i("985a");var a=i("f0c5"),s=Object(a["a"])(o["default"],n["b"],n["c"],!1,null,"38cbd707",null,!1,n["a"],void 0);e["default"]=s.exports},7247:function(t,e,i){"use strict";i.d(e,"b",(function(){return o})),i.d(e,"c",(function(){return r})),i.d(e,"a",(function(){return n}));var n={uPopup:i("5cc5").default,uLoading:i("8fc0").default},o=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",[i("u-popup",{attrs:{zoom:t.zoom,mode:"center",popup:!1,"z-index":t.uZIndex,length:t.width,"mask-close-able":t.maskCloseAble,"border-radius":t.borderRadius,"negative-top":t.negativeTop},on:{close:function(e){arguments[0]=e=t.$handleEvent(e),t.popupClose.apply(void 0,arguments)}},model:{value:t.value,callback:function(e){t.value=e},expression:"value"}},[i("v-uni-view",{staticClass:"u-model"},[t.showTitle?i("v-uni-view",{staticClass:"u-model__title u-line-1",style:[t.titleStyle]},[t._v(t._s(t.title))]):t._e(),i("v-uni-view",{staticClass:"u-model__content"},[t.$slots.default||t.$slots.$default?i("v-uni-view",{style:[t.contentStyle]},[t._t("default")],2):i("v-uni-view",{staticClass:"u-model__content__message",style:[t.contentStyle]},[t._v(t._s(t.content))])],1),t.showCancelButton||t.showConfirmButton?i("v-uni-view",{staticClass:"u-model__footer u-border-top"},[t.showCancelButton?i("v-uni-view",{staticClass:"u-model__footer__button",style:[t.cancelBtnStyle],attrs:{"hover-stay-time":100,"hover-class":"u-model__btn--hover"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.cancel.apply(void 0,arguments)}}},[t._v(t._s(t.cancelText))]):t._e(),t.showConfirmButton||t.$slots["confirm-button"]?i("v-uni-view",{staticClass:"u-model__footer__button hairline-left",style:[t.confirmBtnStyle],attrs:{"hover-stay-time":100,"hover-class":t.asyncClose?"none":"u-model__btn--hover"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.confirm.apply(void 0,arguments)}}},[t.$slots["confirm-button"]?t._t("confirm-button"):[t.loading?i("u-loading",{attrs:{mode:"circle",color:t.confirmColor}}):[t._v(t._s(t.confirmText))]]],2):t._e()],1):t._e()],1)],1)],1)},r=[]},"73dc":function(t,e,i){"use strict";i.r(e);var n=i("eea0"),o=i.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(r);e["default"]=o.a},7844:function(t,e,i){"use strict";i.d(e,"b",(function(){return o})),i.d(e,"c",(function(){return r})),i.d(e,"a",(function(){return n}));var n={uIcon:i("90f3").default},o=function(){var t=this,e=t.$createElement,i=t._self._c||e;return t.show?i("v-uni-view",{staticClass:"u-tag",class:[t.disabled?"u-disabled":"","u-size-"+t.size,"u-shape-"+t.shape,"u-mode-"+t.mode+"-"+t.type],style:[t.customStyle],on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.clickTag.apply(void 0,arguments)}}},[t._v(t._s(t.text)),i("v-uni-view",{staticClass:"u-icon-wrap",on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e)}}},[t.closeable?i("u-icon",{staticClass:"u-close-icon",style:[t.iconStyle],attrs:{size:"22",color:t.closeIconColor,name:"close"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.close.apply(void 0,arguments)}}}):t._e()],1)],1):t._e()},r=[]},"7a54":function(t,e,i){var n=i("aa36");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var o=i("4f06").default;o("4f941e16",n,!0,{sourceMap:!1,shadowMode:!1})},"7bab":function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){}));var n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"u-countdown"},[t.showDays&&(t.hideZeroDay||!t.hideZeroDay&&"00"!=t.d)?i("v-uni-view",{staticClass:"u-countdown-item",style:[t.itemStyle]},[i("v-uni-view",{staticClass:"u-countdown-time",style:{fontSize:t.fontSize+"rpx",color:t.color}},[t._v(t._s(t.d))])],1):t._e(),t.showDays&&(t.hideZeroDay||!t.hideZeroDay&&"00"!=t.d)?i("v-uni-view",{style:{fontSize:t.separatorSize+"rpx","margin-right":"6rpx",color:t.separatorColor}},[t._v("天")]):t._e(),t.showHours?i("v-uni-view",{staticClass:"u-countdown-item",style:[t.itemStyle]},[i("v-uni-view",{staticClass:"u-countdown-time",style:{fontSize:t.fontSize+"rpx",color:t.color}},[t._v(t._s(t.h))])],1):t._e(),t.showHours?i("v-uni-view",{staticClass:"u-countdown-colon",style:{fontSize:t.separatorSize+"rpx",color:t.separatorColor,paddingBottom:"colon"==t.separator?"4rpx":0}},[t._v(t._s("colon"==t.separator?":":"时"))]):t._e(),t.showMinutes?i("v-uni-view",{staticClass:"u-countdown-item",style:[t.itemStyle]},[i("v-uni-view",{staticClass:"u-countdown-time",style:{fontSize:t.fontSize+"rpx",color:t.color}},[t._v(t._s(t.i))])],1):t._e(),t.showMinutes?i("v-uni-view",{staticClass:"u-countdown-colon",style:{fontSize:t.separatorSize+"rpx",color:t.separatorColor,paddingBottom:"colon"==t.separator?"4rpx":0}},[t._v(t._s("colon"==t.separator?":":"分"))]):t._e(),t.showSeconds?i("v-uni-view",{staticClass:"u-countdown-item",staticStyle:{width:"36rpx"},style:[t.itemStyle]},[i("v-uni-view",{staticClass:"u-countdown-time",style:{fontSize:t.fontSize+"rpx",color:t.color}},[t._v(t._s(t.s))])],1):t._e(),t.showSeconds&&"zh"==t.separator?i("v-uni-view",{staticClass:"u-countdown-colon",style:{fontSize:t.separatorSize+"rpx",color:t.separatorColor,paddingBottom:"colon"==t.separator?"4rpx":0}},[t._v("秒")]):t._e()],1)},o=[]},"7c75":function(t,e,i){"use strict";i.r(e);var n=i("1377"),o=i.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(r);e["default"]=o.a},"80a3":function(t,e,i){"use strict";i.r(e);var n=i("356b"),o=i.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(r);e["default"]=o.a},8219:function(t,e,i){"use strict";i.r(e);var n=i("7844"),o=i("abdf");for(var r in o)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(r);i("073a");var a=i("f0c5"),s=Object(a["a"])(o["default"],n["b"],n["c"],!1,null,"1cd62f78",null,!1,n["a"],void 0);e["default"]=s.exports},"822c":function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n={data:function(){return{}},props:{type:{type:String,default:"fixed"},backgroundColor:{type:String,default:"#fff"}},methods:{}};e.default=n},8551:function(t,e,i){var n=i("92ea");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var o=i("4f06").default;o("fd46f924",n,!0,{sourceMap:!1,shadowMode:!1})},"8a6b":function(t,e,i){var n=i("24fb");e=n(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.u-model[data-v-24dbca0a]{height:auto;overflow:hidden;font-size:%?32?%;background-color:#fff}.u-model__btn--hover[data-v-24dbca0a]{background-color:#e6e6e6}.u-model__title[data-v-24dbca0a]{padding-top:%?48?%;font-weight:500;text-align:center;color:#303133}.u-model__content__message[data-v-24dbca0a]{padding:%?48?%;font-size:%?30?%;text-align:center;color:#606266}.u-model__footer[data-v-24dbca0a]{display:flex;flex-direction:row}.u-model__footer__button[data-v-24dbca0a]{flex:1;height:%?100?%;line-height:%?100?%;font-size:%?32?%;box-sizing:border-box;cursor:pointer;text-align:center;border-radius:%?4?%}',""]),t.exports=e},"8aa9":function(t,e,i){"use strict";i.r(e);var n=i("fb1d"),o=i.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(r);e["default"]=o.a},"8aee":function(t,e,i){var n=i("b9d8");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var o=i("4f06").default;o("901755d2",n,!0,{sourceMap:!1,shadowMode:!1})},"8d42":function(t,e,i){"use strict";i.r(e);var n=i("7247"),o=i("59f5");for(var r in o)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(r);i("b79d");var a=i("f0c5"),s=Object(a["a"])(o["default"],n["b"],n["c"],!1,null,"24dbca0a",null,!1,n["a"],void 0);e["default"]=s.exports},"8fc0":function(t,e,i){"use strict";i.r(e);var n=i("2ab4"),o=i("80a3");for(var r in o)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(r);i("1c75");var a=i("f0c5"),s=Object(a["a"])(o["default"],n["b"],n["c"],!1,null,"bf7076f2",null,!1,n["a"],void 0);e["default"]=s.exports},"8fed":function(t,e,i){"use strict";var n=i("e2db"),o=i.n(n);o.a},"92ea":function(t,e,i){var n=i("24fb");e=n(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.u-countdown[data-v-38cbd707]{display:inline-flex;align-items:center}.u-countdown-item[data-v-38cbd707]{display:flex;flex-direction:row;align-items:center;justify-content:center;padding:%?2?%;border-radius:%?6?%;white-space:nowrap;-webkit-transform:translateZ(0);transform:translateZ(0)}.u-countdown-time[data-v-38cbd707]{margin:0;padding:0}.u-countdown-colon[data-v-38cbd707]{display:flex;flex-direction:row;justify-content:center;padding:0 %?5?%;line-height:1;align-items:center;padding-bottom:%?4?%}.u-countdown-scale[data-v-38cbd707]{-webkit-transform:scale(.9);transform:scale(.9);-webkit-transform-origin:center center;transform-origin:center center}',""]),t.exports=e},9415:function(t,e,i){"use strict";var n=i("8aee"),o=i.n(n);o.a},"95a6":function(t,e,i){"use strict";i.r(e);var n=i("ac23"),o=i("8aa9");for(var r in o)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(r);i("2698");var a=i("f0c5"),s=Object(a["a"])(o["default"],n["b"],n["c"],!1,null,"705e9a38",null,!1,n["a"],void 0);e["default"]=s.exports},"985a":function(t,e,i){"use strict";var n=i("8551"),o=i.n(n);o.a},"9a2b":function(t,e,i){"use strict";i.r(e);var n=i("6011"),o=i.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(r);e["default"]=o.a},a712:function(t,e,i){"use strict";i.d(e,"b",(function(){return o})),i.d(e,"c",(function(){return r})),i.d(e,"a",(function(){return n}));var n={uLoading:i("8fc0").default},o=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{class:"loading "+("flex"==this.type?"flex":""),style:{backgroundColor:this.backgroundColor}},[e("u-loading",{attrs:{mode:"flower",size:60}})],1)},r=[]},a7f3:function(t,e,i){"use strict";i.r(e);var n=i("3c87"),o=i("4d65");for(var r in o)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(r);var a=i("f0c5"),s=Object(a["a"])(o["default"],n["b"],n["c"],!1,null,"15d7f11b",null,!1,n["a"],void 0);e["default"]=s.exports},aa36:function(t,e,i){var n=i("24fb");e=n(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.u-loading-circle[data-v-bf7076f2]{display:inline-flex;vertical-align:middle;width:%?28?%;height:%?28?%;background:0 0;border-radius:50%;border:2px solid;border-color:#e5e5e5 #e5e5e5 #e5e5e5 #8f8d8e;-webkit-animation:u-circle-data-v-bf7076f2 1s linear infinite;animation:u-circle-data-v-bf7076f2 1s linear infinite}.u-loading-flower[data-v-bf7076f2]{width:20px;height:20px;display:inline-block;vertical-align:middle;-webkit-animation:a 1s steps(12) infinite;animation:u-flower-data-v-bf7076f2 1s steps(12) infinite;background:transparent url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMjAiIGhlaWdodD0iMTIwIiB2aWV3Qm94PSIwIDAgMTAwIDEwMCI+PHBhdGggZmlsbD0ibm9uZSIgZD0iTTAgMGgxMDB2MTAwSDB6Ii8+PHJlY3Qgd2lkdGg9IjciIGhlaWdodD0iMjAiIHg9IjQ2LjUiIHk9IjQwIiBmaWxsPSIjRTlFOUU5IiByeD0iNSIgcnk9IjUiIHRyYW5zZm9ybT0idHJhbnNsYXRlKDAgLTMwKSIvPjxyZWN0IHdpZHRoPSI3IiBoZWlnaHQ9IjIwIiB4PSI0Ni41IiB5PSI0MCIgZmlsbD0iIzk4OTY5NyIgcng9IjUiIHJ5PSI1IiB0cmFuc2Zvcm09InJvdGF0ZSgzMCAxMDUuOTggNjUpIi8+PHJlY3Qgd2lkdGg9IjciIGhlaWdodD0iMjAiIHg9IjQ2LjUiIHk9IjQwIiBmaWxsPSIjOUI5OTlBIiByeD0iNSIgcnk9IjUiIHRyYW5zZm9ybT0icm90YXRlKDYwIDc1Ljk4IDY1KSIvPjxyZWN0IHdpZHRoPSI3IiBoZWlnaHQ9IjIwIiB4PSI0Ni41IiB5PSI0MCIgZmlsbD0iI0EzQTFBMiIgcng9IjUiIHJ5PSI1IiB0cmFuc2Zvcm09InJvdGF0ZSg5MCA2NSA2NSkiLz48cmVjdCB3aWR0aD0iNyIgaGVpZ2h0PSIyMCIgeD0iNDYuNSIgeT0iNDAiIGZpbGw9IiNBQkE5QUEiIHJ4PSI1IiByeT0iNSIgdHJhbnNmb3JtPSJyb3RhdGUoMTIwIDU4LjY2IDY1KSIvPjxyZWN0IHdpZHRoPSI3IiBoZWlnaHQ9IjIwIiB4PSI0Ni41IiB5PSI0MCIgZmlsbD0iI0IyQjJCMiIgcng9IjUiIHJ5PSI1IiB0cmFuc2Zvcm09InJvdGF0ZSgxNTAgNTQuMDIgNjUpIi8+PHJlY3Qgd2lkdGg9IjciIGhlaWdodD0iMjAiIHg9IjQ2LjUiIHk9IjQwIiBmaWxsPSIjQkFCOEI5IiByeD0iNSIgcnk9IjUiIHRyYW5zZm9ybT0icm90YXRlKDE4MCA1MCA2NSkiLz48cmVjdCB3aWR0aD0iNyIgaGVpZ2h0PSIyMCIgeD0iNDYuNSIgeT0iNDAiIGZpbGw9IiNDMkMwQzEiIHJ4PSI1IiByeT0iNSIgdHJhbnNmb3JtPSJyb3RhdGUoLTE1MCA0NS45OCA2NSkiLz48cmVjdCB3aWR0aD0iNyIgaGVpZ2h0PSIyMCIgeD0iNDYuNSIgeT0iNDAiIGZpbGw9IiNDQkNCQ0IiIHJ4PSI1IiByeT0iNSIgdHJhbnNmb3JtPSJyb3RhdGUoLTEyMCA0MS4zNCA2NSkiLz48cmVjdCB3aWR0aD0iNyIgaGVpZ2h0PSIyMCIgeD0iNDYuNSIgeT0iNDAiIGZpbGw9IiNEMkQyRDIiIHJ4PSI1IiByeT0iNSIgdHJhbnNmb3JtPSJyb3RhdGUoLTkwIDM1IDY1KSIvPjxyZWN0IHdpZHRoPSI3IiBoZWlnaHQ9IjIwIiB4PSI0Ni41IiB5PSI0MCIgZmlsbD0iI0RBREFEQSIgcng9IjUiIHJ5PSI1IiB0cmFuc2Zvcm09InJvdGF0ZSgtNjAgMjQuMDIgNjUpIi8+PHJlY3Qgd2lkdGg9IjciIGhlaWdodD0iMjAiIHg9IjQ2LjUiIHk9IjQwIiBmaWxsPSIjRTJFMkUyIiByeD0iNSIgcnk9IjUiIHRyYW5zZm9ybT0icm90YXRlKC0zMCAtNS45OCA2NSkiLz48L3N2Zz4=) no-repeat;background-size:100%}@-webkit-keyframes u-flower-data-v-bf7076f2{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}@keyframes u-flower-data-v-bf7076f2{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}@-webkit-keyframes u-circle-data-v-bf7076f2{0%{-webkit-transform:rotate(0);transform:rotate(0)}100%{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}',""]),t.exports=e},abdf:function(t,e,i){"use strict";i.r(e);var n=i("1cbc"),o=i.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(r);e["default"]=o.a},ac23:function(t,e,i){"use strict";i.d(e,"b",(function(){return o})),i.d(e,"c",(function(){return r})),i.d(e,"a",(function(){return n}));var n={uIcon:i("90f3").default},o=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"shop-title flex",on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.toShop.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"shop-name line-1 bold"},[t._v(t._s(t.shop.shop_name||t.shop.name||t.name))]),t.isLink?i("u-icon",{staticClass:"m-l-10 m-r-20",attrs:{name:"arrow-right",size:"28"}}):t._e()],1)},r=[]},ad6f:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){}));var n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"float-tab ~column"},[i("v-uni-navigator",{staticClass:"tab-img",style:{top:3*t.top+"px"},attrs:{"hover-class":"none","open-type":"switchTab",url:"/pages/index/index"}},[i("v-uni-image",{staticClass:"tab-icon",attrs:{src:"/static/images/icon_float_home.png"}})],1),i("v-uni-navigator",{staticClass:"tab-img",style:{top:2*t.top+"px"},attrs:{"hover-class":"none","open-type":"navigate",url:"/bundle/pages/chat/chat"}},[i("v-uni-image",{staticClass:"tab-icon",attrs:{src:"/static/images/icon_float_help.png"}})],1),i("v-uni-navigator",{staticClass:"tab-img",style:{top:t.top+"px"},attrs:{"hover-class":"none","open-type":"switchTab",url:"/pages/shop_cart/shop_cart"}},[i("v-uni-image",{staticClass:"tab-icon",attrs:{src:"/static/images/icon_float_cart.png"}})],1),i("v-uni-image",{staticClass:"tab-img",staticStyle:{"z-index":"99"},style:{transform:"rotateZ("+(t.showMore?135:0)+"deg)"},attrs:{src:"/static/images/icon_float_more.png"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onChange.apply(void 0,arguments)}}})],1)},o=[]},ae16:function(t,e,i){var n=i("24fb");e=n(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.order-goods .item[data-v-594a3e18]{padding:%?20?% %?24?%}.order-goods .item .vip-price[data-v-594a3e18]{margin:0 %?10?%;background-color:#ffe9ba;line-height:%?30?%;border-radius:%?6?%;overflow:hidden}.order-goods .item .vip-price .price-name[data-v-594a3e18]{background-color:#101010;padding:%?3?% %?10?%;color:#ffd4b7;position:relative;overflow:hidden}.order-goods .item .vip-price .price-name[data-v-594a3e18]::after{content:"";display:block;width:%?20?%;height:%?20?%;position:absolute;right:%?-15?%;background-color:#ffe9ba;border-radius:50%;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%);box-sizing:border-box}.order-goods .goods-footer[data-v-594a3e18]{height:%?70?%;align-items:flex-start;padding:0 %?24?%}.order-goods .goods-footer .plain[data-v-594a3e18]{border:1px solid #999;height:%?52?%;line-height:%?52?%;font-size:%?26?%}',""]),t.exports=e},af8d:function(t,e,i){"use strict";i.r(e);var n=i("2322"),o=i.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(r);e["default"]=o.a},b0dc:function(t,e,i){"use strict";i("7a82");var n=i("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.cancelOrder=function(t){return o.default.post("order/cancel",{id:t})},e.confirmOrder=function(t){return o.default.post("order/confirm",{id:t})},e.delOrder=function(t){return o.default.post("order/del",{id:t})},e.getOrderDetail=function(t){return o.default.get("order/getOrderDetail",{params:{id:t}})},e.getOrderList=function(t){return o.default.get("order/lists",{params:t})},e.getPayResult=function(t){return o.default.get("order/pay_result",{params:t})},e.getwechatSyncCheck=function(t){return o.default.get("order/wechatSyncCheck",{params:t})},e.getwxReceiveDetail=function(t){return o.default.get("order/wxReceiveDetail",{params:t})},e.orderBuy=function(t){return o.default.post("order/submitOrder",t)},e.orderInfo=function(t){return o.default.post("order/settlement",t)},e.orderTraces=function(t){return o.default.get("order/orderTraces",{params:{id:t}})};var o=n(i("3b33"));i("b08d")},b1d4:function(t,e,i){var n=i("24fb");e=n(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.u-tag[data-v-1cd62f78]{box-sizing:border-box;align-items:center;border-radius:%?6?%;display:inline-block}.u-size-default[data-v-1cd62f78]{font-size:%?22?%;padding:%?6?% %?12?%}.u-size-mini[data-v-1cd62f78]{font-size:%?20?%;padding:%?1?% %?6?%}.u-mode-light-primary[data-v-1cd62f78]{background-color:#ecf5ff;color:#ff2c3c;border:1px solid #a0cfff}.u-mode-light-success[data-v-1cd62f78]{background-color:#dbf1e1;color:#19be6b;border:1px solid #71d5a1}.u-mode-light-error[data-v-1cd62f78]{background-color:#fef0f0;color:#fa3534;border:1px solid #fab6b6}.u-mode-light-warning[data-v-1cd62f78]{background-color:#fdf6ec;color:#f90;border:1px solid #fcbd71}.u-mode-light-info[data-v-1cd62f78]{background-color:#f4f4f5;color:#909399;border:1px solid #c8c9cc}.u-mode-dark-primary[data-v-1cd62f78]{background-color:#ff2c3c;color:#fff}.u-mode-dark-success[data-v-1cd62f78]{background-color:#19be6b;color:#fff}.u-mode-dark-error[data-v-1cd62f78]{background-color:#fa3534;color:#fff}.u-mode-dark-warning[data-v-1cd62f78]{background-color:#f90;color:#fff}.u-mode-dark-info[data-v-1cd62f78]{background-color:#909399;color:#fff}.u-mode-plain-primary[data-v-1cd62f78]{background-color:#fff;color:#ff2c3c;border:1px solid #ff2c3c}.u-mode-plain-success[data-v-1cd62f78]{background-color:#fff;color:#19be6b;border:1px solid #19be6b}.u-mode-plain-error[data-v-1cd62f78]{background-color:#fff;color:#fa3534;border:1px solid #fa3534}.u-mode-plain-warning[data-v-1cd62f78]{background-color:#fff;color:#f90;border:1px solid #f90}.u-mode-plain-info[data-v-1cd62f78]{background-color:#fff;color:#909399;border:1px solid #909399}.u-disabled[data-v-1cd62f78]{opacity:.55}.u-shape-circle[data-v-1cd62f78]{border-radius:%?100?%}.u-shape-circleRight[data-v-1cd62f78]{border-radius:0 %?100?% %?100?% 0}.u-shape-circleLeft[data-v-1cd62f78]{border-radius:%?100?% 0 0 %?100?%}.u-close-icon[data-v-1cd62f78]{margin-left:%?14?%;font-size:%?22?%;color:#19be6b}.u-icon-wrap[data-v-1cd62f78]{display:inline-flex;-webkit-transform:scale(.86);transform:scale(.86)}',""]),t.exports=e},b510:function(t,e,i){"use strict";i.d(e,"b",(function(){return o})),i.d(e,"c",(function(){return r})),i.d(e,"a",(function(){return n}));var n={tabs:i("741a").default,tab:i("5652").default,orderList:i("b933").default,floatTab:i("65c2").default},o=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"user-order"},[i("tabs",{attrs:{current:t.active,"bar-width":"60","is-scroll":!1},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.changeShow.apply(void 0,arguments)}}},t._l(t.order,(function(e,n){return i("tab",{key:n,attrs:{name:e.name}},[t.isLogin?i("order-list",{attrs:{"order-type":e.type,i:n,index:t.active}}):i("v-uni-button",{staticClass:"btn",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.handlelogin.apply(void 0,arguments)}}},[t._v("立即登录")])],1)})),1),i("float-tab")],1)},r=[]},b79d:function(t,e,i){"use strict";var n=i("35b3"),o=i.n(n);o.a},b83e:function(t,e,i){var n=i("24fb");e=n(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.loading[data-v-061dd044]{position:fixed;top:0;left:0;width:100vw;height:100vh;z-index:9999;display:flex;justify-content:center;align-items:center}.loading.flex[data-v-061dd044]{position:static;flex:1;width:100%}.loading .loading-img[data-v-061dd044]{width:%?100?%;height:%?100?%}',""]),t.exports=e},b933:function(t,e,i){"use strict";i.r(e);var n=i("2aaa"),o=i("73dc");for(var r in o)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(r);i("1ce8");var a=i("f0c5"),s=Object(a["a"])(o["default"],n["b"],n["c"],!1,null,"df399534",null,!1,n["a"],void 0);e["default"]=s.exports},b9d8:function(t,e,i){var n=i("24fb");e=n(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.float-tab[data-v-130bc95c]{position:fixed;right:%?16?%;bottom:%?200?%;width:%?96?%;height:%?96?%;z-index:777}.float-tab .tab-img[data-v-130bc95c]{width:100%;height:100%;position:absolute;transition:all .5s}.float-tab .tab-img .tab-icon[data-v-130bc95c]{width:100%;height:100%}',""]),t.exports=e},ba4b:function(t,e,i){"use strict";i.r(e);var n=i("1522"),o=i("af8d");for(var r in o)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(r);i("6021");var a=i("f0c5"),s=Object(a["a"])(o["default"],n["b"],n["c"],!1,null,"1bf07c9a",null,!1,n["a"],void 0);e["default"]=s.exports},bd6f:function(t,e,i){"use strict";i.r(e);var n=i("6d79"),o=i.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(r);e["default"]=o.a},c13e:function(t,e,i){"use strict";i.r(e);var n=i("822c"),o=i.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(r);e["default"]=o.a},c29c:function(t,e,i){"use strict";i.d(e,"b",(function(){return o})),i.d(e,"c",(function(){return r})),i.d(e,"a",(function(){return n}));var n={uImage:i("ba4b").default,uTag:i("8219").default,priceFormat:i("fefe").default},o=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"order-goods"},t._l(t.list,(function(e,n){return i("v-uni-view",{key:n,staticClass:"item-wrap"},[i("v-uni-view",{staticClass:"item flex",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.jumpGoods(e)}}},[i("v-uni-view",{staticClass:"goods-img"},[i("u-image",{attrs:{width:"180rpx","border-radius":"10rpx",height:"180rpx","lazy-load":!0,src:e.image_str||e.image}})],1),i("v-uni-view",{staticClass:"goods-info m-l-20 flex-1"},[i("v-uni-view",{staticClass:"goods-name line-2 m-b-10"},[e.people_num?i("u-tag",{staticClass:"m-r-10",attrs:{text:e.people_num+"人团",size:"mini",type:"primary",mode:"plain"}}):t._e(),t._v(t._s(e.goods_name||e.name))],1),i("v-uni-view",{staticClass:"goods-spec xs muted m-b-20"},[t._v(t._s(e.spec_value||e.spec_value_str))]),i("v-uni-view",{staticClass:"flex row-between"},[i("v-uni-view",{staticClass:"goods-price"},[i("v-uni-view",{staticClass:"primary flex"},[e.is_seckill?i("price-format",{attrs:{weight:"500","subscript-size":24,"first-size":32,"second-size":24,price:e.original_price||e.goods_price}}):i("price-format",{attrs:{weight:"500","subscript-size":24,"first-size":32,"second-size":24,price:e.price||e.goods_price}}),e.is_member?i("v-uni-view",{staticClass:"vip-price flex"},[i("v-uni-view",{staticClass:"price-name xxs"},[t._v("会员价")]),i("v-uni-view",{staticStyle:{padding:"0 10rpx"}},[i("price-format",{attrs:{price:e.member_amount,"first-size":22,"second-size":22,"subscript-size":22,weight:500,color:"#7B3200"}})],1)],1):t._e(),e.team_price?i("v-uni-view",{staticClass:"vip-price flex"},[i("v-uni-view",{staticClass:"price-name xxs"},[t._v("拼团价")]),i("v-uni-view",{staticStyle:{padding:"0 10rpx"}},[i("price-format",{attrs:{price:e.team_price,"first-size":22,"second-size":22,"subscript-size":22,weight:500,color:"#7B3200"}})],1)],1):t._e(),e.is_seckill?i("v-uni-view",{staticClass:"vip-price flex"},[i("v-uni-view",{staticClass:"price-name xxs"},[t._v("秒杀价")]),i("v-uni-view",{staticStyle:{padding:"0 10rpx"}},[i("price-format",{attrs:{price:e.price,"first-size":22,"second-size":22,"subscript-size":22,weight:500,color:"#7B3200"}})],1)],1):t._e()],1)],1),i("v-uni-view",{staticClass:"goods-num sm"},[t._v("x"+t._s(e.num||e.goods_num||e.count))])],1)],1)],1),t.link&&e.comment_btn||e.refund_btn?i("v-uni-view",{staticClass:"goods-footer flex"},[i("v-uni-view",{staticClass:"flex-1"}),e.comment_btn?i("router-link",{staticClass:"m-r-20",attrs:{to:{path:"/bundle/pages/goods_reviews/goods_reviews",query:{id:e.id}}}},[i("v-uni-button",{staticClass:"plain br60",attrs:{size:"xs","hover-class":"none"}},[t._v("评价晒图")])],1):t._e(),e.refund_btn?i("router-link",{attrs:{to:{path:"/bundle/pages/apply_refund/apply_refund",query:{id:e.id,order_id:e.order_id,item_id:e.item_id}}}},[i("v-uni-button",{staticClass:"plain br60",attrs:{size:"xs","hover-class":"none"}},[t._v("申请退款")])],1):t._e()],1):t._e()],1)})),1)},r=[]},c495:function(t,e,i){var n=i("45ee");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var o=i("4f06").default;o("54b253da",n,!0,{sourceMap:!1,shadowMode:!1})},d5b0:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){}));var n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-text",{class:(t.lineThrough?"line-through":"")+" price-format",style:{color:t.color,"font-weight":t.weight}},[t.showSubscript?i("v-uni-text",{style:{"font-size":t.subscriptSize+"rpx","margin-right":"2rpx"}},[t._v("¥")]):t._e(),i("v-uni-text",{style:{"font-size":t.firstSize+"rpx","margin-right":"1rpx"}},[t._v(t._s(t.priceSlice.first))]),t.priceSlice.second?i("v-uni-text",{style:{"font-size":t.secondSize+"rpx"}},[t._v("."+t._s(t.priceSlice.second))]):t._e()],1)},o=[]},d9ab:function(t,e,i){"use strict";i.r(e);var n=i("c29c"),o=i("9a2b");for(var r in o)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(r);i("6017");var a=i("f0c5"),s=Object(a["a"])(o["default"],n["b"],n["c"],!1,null,"594a3e18",null,!1,n["a"],void 0);e["default"]=s.exports},e2db:function(t,e,i){var n=i("b83e");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var o=i("4f06").default;o("fbf8b9f0",n,!0,{sourceMap:!1,shadowMode:!1})},ec1d:function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("a9e3");var n={name:"u-count-down",props:{timestamp:{type:[Number,String],default:0},autoplay:{type:Boolean,default:!0},separator:{type:String,default:"colon"},separatorSize:{type:[Number,String],default:30},separatorColor:{type:String,default:"#303133"},color:{type:String,default:"#303133"},fontSize:{type:[Number,String],default:30},bgColor:{type:String,default:"#fff"},height:{type:[Number,String],default:"auto"},showBorder:{type:Boolean,default:!1},borderColor:{type:String,default:"#303133"},showSeconds:{type:Boolean,default:!0},showMinutes:{type:Boolean,default:!0},showHours:{type:Boolean,default:!0},showDays:{type:Boolean,default:!0},hideZeroDay:{type:Boolean,default:!1}},watch:{timestamp:function(t,e){this.clearTimer(),this.start()}},data:function(){return{d:"00",h:"00",i:"00",s:"00",timer:null,seconds:0}},computed:{itemStyle:function(){var t={};return this.height&&(t.height=this.height+"rpx"),this.showBorder&&(t.borderStyle="solid",t.borderColor=this.borderColor,t.borderWidth="1px"),this.bgColor&&(t.backgroundColor=this.bgColor),t},letterStyle:function(){var t={};return this.fontSize&&(t.fontSize=this.fontSize+"rpx"),this.color&&(t.color=this.color),t}},mounted:function(){this.autoplay&&this.timestamp&&this.start()},methods:{start:function(){var t=this;this.clearTimer(),this.timestamp<=0||(this.seconds=Number(this.timestamp),this.formatTime(this.seconds),this.timer=setInterval((function(){if(t.seconds--,t.$emit("change",t.seconds),t.seconds<0)return t.end();t.formatTime(t.seconds)}),1e3))},formatTime:function(t){t<=0&&this.end();var e,i=0,n=0,o=0;i=Math.floor(t/86400),e=Math.floor(t/3600)-24*i;var r=null;r=this.showDays?e:Math.floor(t/3600),n=Math.floor(t/60)-60*e-24*i*60,o=Math.floor(t)-24*i*60*60-60*e*60-60*n,r=r<10?"0"+r:r,n=n<10?"0"+n:n,o=o<10?"0"+o:o,i=i<10?"0"+i:i,this.d=i,this.h=r,this.i=n,this.s=o},end:function(){this.clearTimer(),this.$emit("end",{})},reset:function(){this.clearTimer(),this.seconds=Number(this.timestamp),this.s=this.timestamp,console.log(this.s)},clearTimer:function(){this.timer&&(clearInterval(this.timer),this.timer=null)}},beforeDestroy:function(){clearInterval(this.timer),this.timer=null}};e.default=n},ee17:function(t,e,i){"use strict";var n=i("c495"),o=i.n(n);o.a},eea0:function(t,e,i){"use strict";i("7a82");var n=i("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("d3b7"),i("159b"),i("99af");var o=n(i("f07e")),r=n(i("c964")),a=n(i("bde1")),s=n(i("4316")),c=i("b0dc"),d=(i("b08d"),{mixins:[a.default,s.default],data:function(){return{orderList:[],downOption:{auto:!1},upOption:{auto:!1,noMoreSize:4,empty:{icon:"/static/images/order_null.png",tip:"暂无订单~",fixed:!0}},showCancel:!1,type:0,orderId:"",showLoading:!1,pay_way:""}},props:{orderType:{type:String}},created:function(){var t=this;uni.$on("refreshorder",(function(){t.refresh()})),uni.$on("payment",(function(e){setTimeout((function(){e.result?(t.$toast({title:"支付成功"}),t.refresh()):t.$toast({title:"支付失败"})}),500)}))},destroyed:function(){uni.$off("payment"),uni.$off("refreshorder")},methods:{orderstatusTitle:function(t){switch(t.order_status){case 0:return 4==t.pay_way?"线下付款":"待支付";case 1:return 2==t.order_type?"拼团中":2==t.delivery_type?"待取货":"待发货";case 2:return"待收货";default:return""}},orderstatusText:function(t){switch(t.order_status){case 0:return 4==t.pay_way?"如已付款，请通知商家【确认收款】":"订单待支付";case 1:return 2==t.order_type&&0==t.is_team_success?"拼团成功后发货":2==t.delivery_type?"请前往指定门店取货":5==t.order_type?t.presell.order_send_text:"商品准备中";case 2:return"待确认收货";default:return""}},confirmDialog:function(){var t=this;return(0,r.default)((0,o.default)().mark((function e(){var i,n,r;return(0,o.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:i=t.type,n=t.orderId,r=null,e.t0=i,e.next=0===e.t0?5:1===e.t0?9:2===e.t0?13:17;break;case 5:return e.next=7,(0,c.cancelOrder)(n);case 7:return r=e.sent,e.abrupt("break",17);case 9:return e.next=11,(0,c.delOrder)(n);case 11:return r=e.sent,e.abrupt("break",17);case 13:return e.next=15,(0,c.confirmOrder)(n);case 15:return r=e.sent,e.abrupt("break",17);case 17:1==r.code&&(t.refresh(),t.$toast({title:r.msg}));case 18:case"end":return e.stop()}}),e)})))()},dialogOpen:function(){this.$refs.orderDialog.open()},refresh:function(){this.mescroll.resetUpScroll()},delOrder:function(t){var e=this;this.orderId=t,this.type=1,this.$nextTick((function(){e.dialogOpen()}))},comfirmReceive:function(t){return new Promise((function(e,i){wx.openBusinessView({businessType:"weappOrderConfirm",extraData:{transaction_id:t},success:function(t){var i=t.extraData;"success"==i.status?e("确认收货"):e("取消收货")},fail:function(t){i(t)}})}))},querycomfirmReceive:function(t){return new Promise((function(e,i){(0,c.getwechatSyncCheck)({id:t}).then((function(t){var n=t.data;4===n.order.order_state?e("已确认收货"):i("未确认收货")})).catch((function(t){i(t)}))}))},comfirmOrder:function(t,e){var i=this;this.orderId=t,this.pay_way=e,this.type=2,this.$nextTick((0,r.default)((0,o.default)().mark((function t(){return(0,o.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:i.dialogOpen();case 1:case"end":return t.stop()}}),t)}))))},cancelOrder:function(t){var e=this;this.orderId=t,this.type=0,this.$nextTick((function(){e.dialogOpen()}))},payNow:function(t){uni.navigateTo({url:"/pages/payment/payment?from=".concat("order","&order_id=",t)})},handlegoodCount:function(t){var e=0;return t.order_goods.forEach((function(t){e+=t.goods_num})),e},upCallback:function(t){var e=this,i=t.num,n=t.size,o=this.orderType;(0,c.getOrderList)({page_size:n,page_no:i,type:o}).then((function(i){var n=i.data,o=n.list,r=o.length,a=!!n.more;1==t.num&&(e.orderList=[]),e.orderList=e.orderList.concat(o),e.mescroll.endSuccess(r,a)}))}},computed:{getOrderStatus:function(){return function(t){var e="";switch(t){case 0:e="待支付";break;case 1:e="待发货";break;case 2:e="待收货";break;case 3:e="已完成";break;case 4:e="订单已关闭";break}return e}},getCancelTime:function(){return function(t){return t-Date.now()/1e3}}}});e.default=d},f9ff:function(t,e,i){var n=i("24fb");e=n(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.order-list[data-v-df399534]{min-height:calc(100vh - %?80?%);padding:0 %?20?%;overflow:hidden}.order-list .order-item[data-v-df399534]{border-radius:%?10?%}.order-list .order-item .order-header[data-v-df399534]{height:%?80?%;padding:0 %?24?%;border-bottom:1px dotted #e5e5e5}.order-list .order-item .all-price[data-v-df399534]{text-align:right;padding:0 %?24?% %?20?%}.order-list .order-item .order-footer[data-v-df399534]{height:%?100?%;border-top:1px solid #e5e5e5;padding:0 %?24?%}.order-list .order-item .order-footer .plain[data-v-df399534]{border:1px solid #bbb}.order-list .order-item .order-footer .plain.red[data-v-df399534]{border-color:#ff2c3c}',""]),t.exports=e},fb1d:function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("14d9");var n={name:"shop-title",options:{virtualHost:!0},props:{name:{type:String},shop:{type:Object},isLink:{type:Boolean,default:!0}},data:function(){return{}},methods:{toShop:function(){var t=this.isLink,e=this.shop;t&&this.$Router.push({path:"/pages/store_index/store_index",query:{id:e.shop_id||e.id}})}}};e.default=n},fefe:function(t,e,i){"use strict";i.r(e);var n=i("d5b0"),o=i("bd6f");for(var r in o)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(r);i("ee17");var a=i("f0c5"),s=Object(a["a"])(o["default"],n["b"],n["c"],!1,null,"0a5a34e0",null,!1,n["a"],void 0);e["default"]=s.exports}}]);