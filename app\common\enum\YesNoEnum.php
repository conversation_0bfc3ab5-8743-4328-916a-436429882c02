<?php

namespace app\common\enum;

/**
 * 是否枚举
 * Class YesNoEnum
 * @package app\common\enum
 */
class YesNoEnum
{
    const YES = 1;
    const NO = 0;

    /**
     * @notes 获取描述
     * @param int $value
     * @return string
     */
    public static function getDesc($value): string
    {
        switch ($value) {
            case self::YES:
                return '是';
            case self::NO:
                return '否';
            default:
                return '未知';
        }
    }

    /**
     * @notes 获取列表
     * @return array[]
     */
    public static function getList(): array
    {
        return [
            ['value' => self::YES, 'desc' => '是'],
            ['value' => self::NO, 'desc' => '否'],
        ];
    }
} 