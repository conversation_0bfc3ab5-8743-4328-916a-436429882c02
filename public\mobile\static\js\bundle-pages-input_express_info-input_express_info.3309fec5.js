(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["bundle-pages-input_express_info-input_express_info"],{"01f1":function(e,t,i){"use strict";i("7a82"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,i("a9e3"),i("498a");var n={name:"u-field",props:{icon:String,rightIcon:String,required:<PERSON>ole<PERSON>,label:String,password:<PERSON>olean,clearable:{type:<PERSON>olean,default:!0},labelWidth:{type:[Number,String],default:130},labelAlign:{type:String,default:"left"},inputAlign:{type:String,default:"left"},iconColor:{type:String,default:"#606266"},autoHeight:{type:Boolean,default:!0},errorMessage:{type:[String,Boolean],default:""},placeholder:String,placeholderStyle:String,focus:Boolean,fixed:Boolean,value:[Number,String],type:{type:String,default:"text"},disabled:{type:Boolean,default:!1},maxlength:{type:[Number,String],default:140},confirmType:{type:String,default:"done"},labelPosition:{type:String,default:"left"},fieldStyle:{type:Object,default:function(){return{}}},clearSize:{type:[Number,String],default:30},iconStyle:{type:Object,default:function(){return{}}},borderTop:{type:Boolean,default:!1},borderBottom:{type:Boolean,default:!0},trim:{type:Boolean,default:!0}},data:function(){return{focused:!1,itemIndex:0,isIos:"ios"==this.$u.os()}},computed:{inputWrapStyle:function(){var e={};return e.textAlign=this.inputAlign,"left"==this.labelPosition?e.margin="0 8rpx":e.marginRight="8rpx",e},rightIconStyle:function(){var e={};return"top"==this.arrowDirection&&(e.transform="roate(-90deg)"),"bottom"==this.arrowDirection?e.transform="roate(90deg)":e.transform="roate(0deg)",e},labelStyle:function(){var e={};return"left"==this.labelAlign&&(e.justifyContent="flext-start"),"center"==this.labelAlign&&(e.justifyContent="center"),"right"==this.labelAlign&&(e.justifyContent="flext-end"),e},justifyContent:function(){return"left"==this.labelAlign?"flex-start":"center"==this.labelAlign?"center":"right"==this.labelAlign?"flex-end":void 0},inputMaxlength:function(){return Number(this.maxlength)},fieldInnerStyle:function(){var e={};return"left"==this.labelPosition?e.flexDirection="row":e.flexDirection="column",e}},methods:{onInput:function(e){var t=e.detail.value;this.trim&&(t=this.$u.trim(t)),this.$emit("input",t)},onFocus:function(e){this.focused=!0,this.$emit("focus",e)},onBlur:function(e){var t=this;setTimeout((function(){t.focused=!1}),100),this.$emit("blur",e)},onConfirm:function(e){this.$emit("confirm",e.detail.value)},onClear:function(e){this.$emit("input","")},rightIconClick:function(){this.$emit("right-icon-click"),this.$emit("click")},fieldClick:function(){this.$emit("click")}}};t.default=n},"07e8":function(e,t,i){"use strict";i.r(t);var n=i("e3d3"),a=i.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){i.d(t,e,(function(){return n[e]}))}(o);t["default"]=a.a},"18b4":function(e,t,i){"use strict";i.r(t);var n=i("f5f7"),a=i("07e8");for(var o in a)["default"].indexOf(o)<0&&function(e){i.d(t,e,(function(){return a[e]}))}(o);i("330c");var l=i("f0c5"),r=Object(l["a"])(a["default"],n["b"],n["c"],!1,null,"a5ef2a20",null,!1,n["a"],void 0);t["default"]=r.exports},"330c":function(e,t,i){"use strict";var n=i("f422"),a=i.n(n);a.a},"3d06":function(e,t,i){"use strict";var n=i("a806"),a=i.n(n);a.a},"3d24":function(e,t,i){"use strict";i.d(t,"b",(function(){return a})),i.d(t,"c",(function(){return o})),i.d(t,"a",(function(){return n}));var n={uIcon:i("6976").default},a=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("v-uni-view",{staticClass:"u-field",class:{"u-border-top":e.borderTop,"u-border-bottom":e.borderBottom}},[i("v-uni-view",{staticClass:"u-field-inner",class:["textarea"==e.type?"u-textarea-inner":"","u-label-postion-"+e.labelPosition]},[i("v-uni-view",{staticClass:"u-label",class:[e.required?"u-required":""],style:{justifyContent:e.justifyContent,flex:"left"==e.labelPosition?"0 0 "+e.labelWidth+"rpx":"1"}},[e.icon?i("v-uni-view",{staticClass:"u-icon-wrap"},[i("u-icon",{staticClass:"u-icon",attrs:{size:"32","custom-style":e.iconStyle,name:e.icon,color:e.iconColor}})],1):e._e(),e._t("icon"),i("v-uni-text",{staticClass:"u-label-text",class:[this.$slots.icon||e.icon?"u-label-left-gap":""]},[e._v(e._s(e.label))])],2),i("v-uni-view",{staticClass:"fild-body"},[i("v-uni-view",{staticClass:"u-flex-1 u-flex",style:[e.inputWrapStyle]},["textarea"==e.type?i("v-uni-textarea",{staticClass:"u-flex-1 u-textarea-class",class:{"u-textarea-ios":e.isIos},style:[e.fieldStyle],attrs:{value:e.value,placeholder:e.placeholder,placeholderStyle:e.placeholderStyle,disabled:e.disabled,maxlength:e.inputMaxlength,focus:e.focus,autoHeight:e.autoHeight,fixed:e.fixed},on:{input:function(t){arguments[0]=t=e.$handleEvent(t),e.onInput.apply(void 0,arguments)},blur:function(t){arguments[0]=t=e.$handleEvent(t),e.onBlur.apply(void 0,arguments)},focus:function(t){arguments[0]=t=e.$handleEvent(t),e.onFocus.apply(void 0,arguments)},confirm:function(t){arguments[0]=t=e.$handleEvent(t),e.onConfirm.apply(void 0,arguments)},click:function(t){arguments[0]=t=e.$handleEvent(t),e.fieldClick.apply(void 0,arguments)}}}):i("v-uni-input",{staticClass:"u-flex-1 u-field__input-wrap",style:[e.fieldStyle],attrs:{type:e.type,value:e.value,password:e.password||"password"===this.type,placeholder:e.placeholder,placeholderStyle:e.placeholderStyle,disabled:e.disabled,maxlength:e.inputMaxlength,focus:e.focus,confirmType:e.confirmType},on:{focus:function(t){arguments[0]=t=e.$handleEvent(t),e.onFocus.apply(void 0,arguments)},blur:function(t){arguments[0]=t=e.$handleEvent(t),e.onBlur.apply(void 0,arguments)},input:function(t){arguments[0]=t=e.$handleEvent(t),e.onInput.apply(void 0,arguments)},confirm:function(t){arguments[0]=t=e.$handleEvent(t),e.onConfirm.apply(void 0,arguments)},click:function(t){arguments[0]=t=e.$handleEvent(t),e.fieldClick.apply(void 0,arguments)}}})],1),e.clearable&&""!=e.value&&e.focused?i("u-icon",{staticClass:"u-clear-icon",attrs:{size:e.clearSize,name:"close-circle-fill",color:"#c0c4cc"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.onClear.apply(void 0,arguments)}}}):e._e(),i("v-uni-view",{staticClass:"u-button-wrap"},[e._t("right")],2),e.rightIcon?i("u-icon",{staticClass:"u-arror-right",style:[e.rightIconStyle],attrs:{name:e.rightIcon,color:"#c0c4cc",size:"26"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.rightIconClick.apply(void 0,arguments)}}}):e._e()],1)],1),!1!==e.errorMessage&&""!=e.errorMessage?i("v-uni-view",{staticClass:"u-error-message",style:{paddingLeft:e.labelWidth+"rpx"}},[e._v(e._s(e.errorMessage))]):e._e()],1)},o=[]},"557f":function(e,t,i){"use strict";i.r(t);var n=i("01f1"),a=i.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){i.d(t,e,(function(){return n[e]}))}(o);t["default"]=a.a},9840:function(e,t,i){var n=i("24fb");t=n(!1),t.push([e.i,".input-contain .input-item[data-v-a5ef2a20]{padding:%?24?%}.input-item .label[data-v-a5ef2a20]{width:%?152?%}.input-item .input[data-v-a5ef2a20]{flex:1}.upload-contain[data-v-a5ef2a20]{padding:%?24?% %?20?% %?44?%}.upload-contain .header[data-v-a5ef2a20]{margin-bottom:%?30?%}.submit-btn[data-v-a5ef2a20]{margin-top:%?50?%;margin-left:%?26?%;margin-right:%?26?%}",""]),e.exports=t},a806:function(e,t,i){var n=i("eeaa");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[e.i,n,""]]),n.locals&&(e.exports=n.locals);var a=i("4f06").default;a("6b5964ce",n,!0,{sourceMap:!1,shadowMode:!1})},b6ae:function(e,t,i){"use strict";i.r(t);var n=i("3d24"),a=i("557f");for(var o in a)["default"].indexOf(o)<0&&function(e){i.d(t,e,(function(){return a[e]}))}(o);i("3d06");var l=i("f0c5"),r=Object(l["a"])(a["default"],n["b"],n["c"],!1,null,"1ed4a0d8",null,!1,n["a"],void 0);t["default"]=r.exports},e3d3:function(e,t,i){"use strict";i("7a82"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,i("14d9"),i("a434");var n=i("1524"),a=i("f287"),o={data:function(){return{action:a.baseURL+"/api/file/formimage",fileList:[],formInfo:{express:"",number:"",remark:""}}},onLoad:function(){this.id=this.$Route.query.id},methods:{onSuccess:function(e){this.fileList.push(e.data.base_uri)},onRemove:function(e){this.fileList.splice(e,1)},formSubmit:function(e){var t=this,i=this.fileList,a=this.formInfo,o=a.express,l=a.number,r=a.remark;if(!o)return this.$toast({title:"请填写物流公司名称"});if(!l)return this.$toast({title:"请填写快递单号"});var s={id:this.id,express_name:o,invoice_no:l,express_remark:r,express_image:i.length?i[0]:""};(0,n.inputExpressInfo)(s).then((function(e){1==e.code&&(t.$toast({title:"提交成功"},{tab:3,url:1}),uni.$emit("refreshsale"))}))}}};t.default=o},eeaa:function(e,t,i){var n=i("24fb");t=n(!1),t.push([e.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.u-field[data-v-1ed4a0d8]{font-size:%?28?%;padding:%?20?% %?28?%;text-align:left;position:relative;color:#303133}.u-field-inner[data-v-1ed4a0d8]{display:flex;flex-direction:row;align-items:center}.u-textarea-inner[data-v-1ed4a0d8]{align-items:flex-start}.u-textarea-class[data-v-1ed4a0d8]{min-height:%?96?%;width:auto;font-size:%?28?%}.fild-body[data-v-1ed4a0d8]{display:flex;flex-direction:row;flex:1;align-items:center}.u-arror-right[data-v-1ed4a0d8]{margin-left:%?8?%}.u-label-text[data-v-1ed4a0d8]{display:inline-flex}.u-label-left-gap[data-v-1ed4a0d8]{margin-left:%?6?%}.u-label-postion-top[data-v-1ed4a0d8]{flex-direction:column;align-items:flex-start}.u-label[data-v-1ed4a0d8]{width:%?130?%;flex:1 1 %?130?%;text-align:left;position:relative;display:flex;flex-direction:row;align-items:center}.u-required[data-v-1ed4a0d8]::before{content:"*";position:absolute;left:%?-16?%;font-size:14px;color:#fa3534;height:9px;line-height:1}.u-field__input-wrap[data-v-1ed4a0d8]{position:relative;overflow:hidden;font-size:%?28?%;height:%?48?%;flex:1;width:auto}.u-clear-icon[data-v-1ed4a0d8]{display:flex;flex-direction:row;align-items:center}.u-error-message[data-v-1ed4a0d8]{color:#fa3534;font-size:%?26?%;text-align:left}.placeholder-style[data-v-1ed4a0d8]{color:#969799}.u-input-class[data-v-1ed4a0d8]{font-size:%?28?%}.u-button-wrap[data-v-1ed4a0d8]{margin-left:%?8?%}',""]),e.exports=t},f422:function(e,t,i){var n=i("9840");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[e.i,n,""]]),n.locals&&(e.exports=n.locals);var a=i("4f06").default;a("7eae0434",n,!0,{sourceMap:!1,shadowMode:!1})},f5f7:function(e,t,i){"use strict";i.d(t,"b",(function(){return a})),i.d(t,"c",(function(){return o})),i.d(t,"a",(function(){return n}));var n={uField:i("b6ae").default,uUpload:i("1697").default},a=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("v-uni-view",{staticClass:"input-express-info p-t-20"},[i("v-uni-view",{staticClass:"input-contain bg-white"},[i("u-field",{attrs:{"border-bottom":!1,label:"物流公司",placeholder:"请输入物流公司名称"},model:{value:e.formInfo.express,callback:function(t){e.$set(e.formInfo,"express",t)},expression:"formInfo.express"}}),i("u-field",{attrs:{"border-bottom":!1,label:"快递单号",placeholder:"请输入快递单号"},model:{value:e.formInfo.number,callback:function(t){e.$set(e.formInfo,"number",t)},expression:"formInfo.number"}}),i("u-field",{attrs:{"border-bottom":!1,label:"备注说明",placeholder:"选填"},model:{value:e.formInfo.remark,callback:function(t){e.$set(e.formInfo,"remark",t)},expression:"formInfo.remark"}})],1),i("v-uni-view",{staticClass:"upload-contain bg-white m-t-20"},[i("v-uni-view",{staticClass:"header flex"},[i("v-uni-view",{staticClass:"normal"},[e._v("上传凭证")]),i("v-uni-view",{staticClass:"sm muted m-l-20"},[e._v("(请上传快递单号凭证）")])],1),i("v-uni-view",{staticClass:"upload"},[i("u-upload",{ref:"uUpload",attrs:{"show-progress":!1,header:{token:e.$store.getters.token},"max-count":1,width:"160",height:"160",action:e.action,"upload-text":"上传图片"},on:{"on-success":function(t){arguments[0]=t=e.$handleEvent(t),e.onSuccess.apply(void 0,arguments)},"on-remove":function(t){arguments[0]=t=e.$handleEvent(t),e.onRemove.apply(void 0,arguments)}}})],1)],1),i("v-uni-view",{staticClass:"submit-btn"},[i("v-uni-button",{staticClass:" br60 bg-primary white lg",attrs:{size:"lg"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.formSubmit.apply(void 0,arguments)}}},[e._v("提交")])],1)],1)},o=[]}}]);