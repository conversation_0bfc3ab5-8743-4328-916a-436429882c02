{layout name="layout2" /}
<div class="layui-form" lay-filter="">
    <div class="layui-tab">

        <div class="layui-form-item">
            <label class="layui-form-label">会员类型</label>
            <div class="layui-input-inline" style="width:295px;">
                <input type="radio" name="types" value="0" title="用户集采会员" checked>
                <input type="radio" name="types" value="1" title="集采购商家会员" >
            </div>
        </div>
        <div class="layui-form-item ">
            <label class="layui-form-label">会员时长</label>
            <div class="layui-input-inline" style="width:295px;">
                <input type="radio" name="give_type" value="0" title="月卡" checked>
                <input type="radio" name="give_type" value="1" title="季卡" >
                <input type="radio" name="give_type" value="2" title="年卡" >
                <input type="radio" name="give_type" value="3" title="两年卡" >
                <input type="radio" name="give_type" value="4" title="终身" >
            </div>
        </div>
        <div class="layui-form-item layui-col-sm6  layui-col-md4">
            <label class="layui-form-label"><font color="red">*</font>会员金额</label>
            <div class="layui-input-block">
                <input type="number" name="money" lay-verify="required" lay-verType="tips" placeholder="请输入充值金额" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item layui-hide">
            <input type="button" lay-submit lay-filter="add-recharge-submit" id="add-recharge-submit" value="确认">
        </div>
    </div>
</div>
<script>
    layui.config({
        version:"{$front_version}",
        base: '/static/lib/' //静态资源所在路径
    }).use(['form'], function(){
        var $ = layui.$
            ,form = layui.form
    });
</script>
