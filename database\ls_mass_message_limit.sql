-- 群发信息限制表
CREATE TABLE `ls_mass_message_limit` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `tier_level` tinyint(1) NOT NULL COMMENT '商家等级：0=0元入驻，1=商家会员，2=实力厂商',
  `daily_limit` int(11) NOT NULL DEFAULT 0 COMMENT '每日群发信息数量限制',
  `create_time` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `update_time` int(11) UNSIGNED NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_tier_level` (`tier_level`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='群发信息限制表';
