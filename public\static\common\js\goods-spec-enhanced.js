/**
 * 商品规格增强版JavaScript
 * 解决输入框无法编辑、交互问题等
 * <AUTHOR> Assistant
 * @version 2.0
 */

(function() {
    'use strict';
    
    // 全局配置
    var config = {
        // 输入框选择器
        inputSelectors: [
            '.spec-input-field',
            'input[name*="price"]',
            'input[name*="stock"]',
            'input[name*="weight"]',
            'input[name*="volume"]',
            'input[name*="bar_code"]'
        ],
        // 延迟时间配置
        delays: {
            inputDebounce: 300,
            focusDelay: 100,
            tableRebuild: 1000
        },
        // 调试模式
        debug: false
    };

    // 工具函数
    var utils = {
        // 调试日志
        log: function(message, data) {
            if (config.debug) {
                console.log('[GoodsSpec] ' + message, data || '');
            }
        },
        
        // 防抖函数
        debounce: function(func, wait) {
            var timeout;
            return function executedFunction() {
                var context = this;
                var args = arguments;
                var later = function() {
                    timeout = null;
                    func.apply(context, args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        },
        
        // 检查元素是否可见
        isVisible: function(element) {
            return element && element.offsetParent !== null;
        },
        
        // 安全获取jQuery对象
        $safe: function(selector) {
            try {
                return $(selector);
            } catch (e) {
                utils.log('选择器错误: ' + selector, e);
                return $();
            }
        }
    };

    // 输入框增强管理器
    var InputEnhancer = {
        // 初始化
        init: function() {
            this.bindEvents();
            this.enhanceExistingInputs();
            utils.log('输入框增强器初始化完成');
        },

        // 绑定事件
        bindEvents: function() {
            var self = this;
            
            // 使用事件委托处理动态生成的输入框
            $(document).on('focus', config.inputSelectors.join(','), function(e) {
                self.handleInputFocus($(this), e);
            });

            $(document).on('blur', config.inputSelectors.join(','), function(e) {
                self.handleInputBlur($(this), e);
            });

            $(document).on('input', config.inputSelectors.join(','), utils.debounce(function(e) {
                self.handleInputChange($(this), e);
            }, config.delays.inputDebounce));

            // 监听表格重建事件
            $(document).on('tableRebuilt', function() {
                setTimeout(function() {
                    self.enhanceExistingInputs();
                }, config.delays.focusDelay);
            });
        },

        // 增强现有输入框
        enhanceExistingInputs: function() {
            var self = this;
            config.inputSelectors.forEach(function(selector) {
                utils.$safe(selector).each(function() {
                    self.enhanceInput($(this));
                });
            });
            utils.log('现有输入框增强完成');
        },

        // 增强单个输入框
        enhanceInput: function($input) {
            if (!$input.length || $input.data('enhanced')) {
                return;
            }

            // 标记为已增强
            $input.data('enhanced', true);
            
            // 确保输入框可编辑
            $input.prop('readonly', false);
            $input.prop('disabled', false);
            
            // 添加CSS类
            $input.addClass('spec-enhanced-input');
            
            // 设置tabindex确保可聚焦
            if (!$input.attr('tabindex')) {
                $input.attr('tabindex', '0');
            }

            // 添加数据属性
            if (!$input.data('field')) {
                var name = $input.attr('name');
                if (name) {
                    $input.data('field', name.replace(/\[\]$/, ''));
                }
            }

            utils.log('输入框增强完成', $input.attr('name'));
        },

        // 处理输入框获得焦点
        handleInputFocus: function($input, event) {
            $input.addClass('spec-input-focused');
            
            // 确保输入框在视口中可见
            this.ensureInputVisible($input);
            
            // 移除可能的错误状态
            $input.removeClass('layui-form-danger');
            
            utils.log('输入框获得焦点', $input.attr('name'));
        },

        // 处理输入框失去焦点
        handleInputBlur: function($input, event) {
            $input.removeClass('spec-input-focused');
            
            // 验证输入值
            this.validateInput($input);
            
            utils.log('输入框失去焦点', $input.attr('name'));
        },

        // 处理输入变化
        handleInputChange: function($input, event) {
            var value = $input.val();
            var fieldName = $input.data('field');
            
            // 触发自定义事件
            $input.trigger('specInputChange', {
                field: fieldName,
                value: value,
                input: $input
            });
            
            utils.log('输入框值变化', {field: fieldName, value: value});
        },

        // 确保输入框可见
        ensureInputVisible: function($input) {
            if (!utils.isVisible($input[0])) {
                return;
            }

            var inputOffset = $input.offset();
            var windowHeight = $(window).height();
            var scrollTop = $(window).scrollTop();
            
            if (inputOffset.top < scrollTop || inputOffset.top > scrollTop + windowHeight - 100) {
                $('html, body').animate({
                    scrollTop: inputOffset.top - 100
                }, 300);
            }
        },

        // 验证输入
        validateInput: function($input) {
            var value = $input.val();
            var type = $input.attr('type');
            var required = $input.attr('lay-verify') && $input.attr('lay-verify').indexOf('required') > -1;
            
            // 基本验证
            if (required && !value) {
                $input.addClass('layui-form-danger');
                return false;
            }
            
            if (type === 'number' && value && isNaN(parseFloat(value))) {
                $input.addClass('layui-form-danger');
                return false;
            }
            
            $input.removeClass('layui-form-danger');
            return true;
        }
    };

    // 表格管理器
    var TableManager = {
        // MutationObserver实例
        observer: null,

        // 初始化
        init: function() {
            this.bindEvents();
            utils.log('表格管理器初始化完成');
        },

        // 绑定事件
        bindEvents: function() {
            var self = this;

            // 使用MutationObserver监听表格内容变化（替代已弃用的DOMNodeInserted）
            this.setupTableObserver();
        },

        // 设置表格观察器
        setupTableObserver: function() {
            var self = this;
            var targetTable = document.getElementById('more-spec-lists-table');

            if (!targetTable) {
                utils.log('表格元素不存在，稍后重试');
                // 如果表格还不存在，延迟重试
                setTimeout(function() {
                    self.setupTableObserver();
                }, 1000);
                return;
            }

            // 如果已有观察器，先断开
            if (this.observer) {
                this.observer.disconnect();
            }

            // 创建MutationObserver
            this.observer = new MutationObserver(function(mutations) {
                var hasChanges = false;
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'childList' &&
                        (mutation.addedNodes.length > 0 || mutation.removedNodes.length > 0)) {
                        hasChanges = true;
                    }
                });

                if (hasChanges) {
                    setTimeout(function() {
                        self.handleTableUpdate();
                    }, config.delays.focusDelay);
                }
            });

            // 开始观察
            this.observer.observe(targetTable, {
                childList: true,
                subtree: true
            });

            utils.log('表格MutationObserver已启动');
        },

        // 处理表格更新
        handleTableUpdate: function() {
            // 触发表格重建事件
            $(document).trigger('tableRebuilt');

            // 重新初始化输入框
            InputEnhancer.enhanceExistingInputs();

            utils.log('表格更新处理完成');
        },

        // 清理观察器
        destroy: function() {
            if (this.observer) {
                this.observer.disconnect();
                this.observer = null;
                utils.log('表格MutationObserver已清理');
            }
        }
    };



    // 主初始化函数
    function initGoodsSpecEnhanced() {
        utils.log('开始初始化商品规格增强功能');
        
        // 等待DOM和layui加载完成
        $(document).ready(function() {
            if (typeof layui !== 'undefined') {
                layui.use(['form', 'layer'], function() {
                    // 初始化各个模块
                    InputEnhancer.init();
                    TableManager.init();

                    utils.log('商品规格增强功能初始化完成');
                });
            } else {
                // 如果layui未加载，延迟初始化
                setTimeout(initGoodsSpecEnhanced, 1000);
            }
        });
    }

    // 暴露到全局
    window.GoodsSpecEnhanced = {
        init: initGoodsSpecEnhanced,
        InputEnhancer: InputEnhancer,
        TableManager: TableManager,
        utils: utils,
        config: config
    };

    // 页面卸载时清理资源
    $(window).on('beforeunload', function() {
        TableManager.destroy();
    });

    // 自动初始化
    initGoodsSpecEnhanced();

})();
