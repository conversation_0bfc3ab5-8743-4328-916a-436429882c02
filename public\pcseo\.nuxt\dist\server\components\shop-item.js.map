{"version": 3, "file": "components/shop-item.js", "sources": ["webpack:///./components/shop-item.vue?4a98", "webpack:///./components/shop-item.vue?5f0b", "webpack:///./components/shop-item.vue?0a3d", "webpack:///./components/shop-item.vue?5656", "webpack:///./components/shop-item.vue", "webpack:///./components/shop-item.vue?d5fd", "webpack:///./components/shop-item.vue?eb53"], "sourcesContent": ["// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/sass-resources-loader/lib/loader.js??ref--7-oneOf-1-4!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./shop-item.vue?vue&type=style&index=0&id=871c1244&lang=scss&scoped=true&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"d6370bb2\", content, true, context)\n};", "export * from \"-!../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/sass-resources-loader/lib/loader.js??ref--7-oneOf-1-4!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./shop-item.vue?vue&type=style&index=0&id=871c1244&lang=scss&scoped=true&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".shop-item[data-v-871c1244]{width:270px;height:400px;background-size:cover;background-position:50%;padding:10px;border-radius:6px}.shop-item .shop-info[data-v-871c1244]{border-radius:6px;padding:18px 15px}.shop-item .shop-info .logo[data-v-871c1244]{width:70px;height:70px;border-radius:16px;margin-top:-45px}.shop-item .shop-info .sales[data-v-871c1244]{display:inline-block;padding:4px 10px;background-color:#f2f2f2;margin-top:6px;border-radius:4px}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('nuxt-link',{staticClass:\"shop-item flex-col row-right\",style:({\n        'background-image': (\"url(\" + _vm.cover + \")\"),\n    }),attrs:{\"to\":(\"/shop_street_detail?id=\" + _vm.shopId)}},[_c('div',{staticClass:\"bg-white shop-info text-center\"},[_c('el-image',{staticClass:\"logo\",attrs:{\"src\":_vm.logo}}),_vm._v(\" \"),_c('div',{staticClass:\"m-t-12 line-1 lg\"},[(_vm.type == 1)?_c('el-tag',{attrs:{\"size\":\"mini\"}},[_vm._v(\"自营\")]):_vm._e(),_vm._v(\" \"+_vm._s(_vm.name)+\"\\n        \")],1),_vm._v(\" \"),_c('span',{staticClass:\"xs muted sales\"},[_vm._v(\"共\"+_vm._s(_vm.sales)+\"件商品\")])],1)])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nexport default {\n    components: {},\n    props: {\n        cover: {\n            type: String,\n        },\n        shopId: {\n            type: [String, Number],\n        },\n        logo: {\n            type: String,\n        },\n        type: {\n            type: [String, Number],\n        },\n        name: {\n            type: String,\n        },\n        sales: {\n            type: [String, Number],\n        }\n    },\n    methods: {},\n}\n", "import mod from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./shop-item.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./shop-item.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./shop-item.vue?vue&type=template&id=871c1244&scoped=true&\"\nimport script from \"./shop-item.vue?vue&type=script&lang=js&\"\nexport * from \"./shop-item.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./shop-item.vue?vue&type=style&index=0&id=871c1244&lang=scss&scoped=true&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  \"871c1244\",\n  \"2279e038\"\n  \n)\n\nexport default component.exports"], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACXA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACNA;AACA;AACA;AACA;AACA;;;;;ACJA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AADA;AAGA;AACA;AADA;AAGA;AACA;AADA;AAGA;AACA;AADA;AAGA;AACA;AADA;AAGA;AACA;AADA;AAhBA;AAoBA;AAtBA;;ACrBA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;A", "sourceRoot": ""}