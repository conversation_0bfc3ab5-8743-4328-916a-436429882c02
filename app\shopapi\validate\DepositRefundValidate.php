<?php

namespace app\shopapi\validate;

use app\common\validate\BaseValidate;

/**
 * @extends \app\common\validate\BaseValidate
 */
class DepositRefundValidate 
{
    protected $rule = [
        'amount' => 'require|float|gt:0',
        'reason' => 'require|max:200',
        'shop_id' => 'require|integer|gt:0',
    ];

    protected $message = [
        'amount.require' => '退款金额不能为空',
        'amount.float' => '退款金额必须为数字',
        'amount.gt' => '退款金额必须大于0',
        'reason.require' => '退款原因不能为空',
        'reason.max' => '退款原因不能超过200个字符',
        'shop_id.require' => '商家ID不能为空',
        'shop_id.integer' => '商家ID必须为整数',
        'shop_id.gt' => '商家ID必须大于0',
    ];
}