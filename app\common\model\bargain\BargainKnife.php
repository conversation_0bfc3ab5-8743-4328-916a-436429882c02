<?php



namespace app\common\model\bargain;


use app\common\basics\Models;
use app\common\model\user\User;

/**
 * 砍价活动 助力模型
 * Class BargainKnife
 * <AUTHOR>
 * @package app\common\model
 */
class BargainKnife extends Models
{
    /**
     * @notes 关联用户模型
     * @return \think\model\relation\HasOne
     * <AUTHOR>
     * @date 2021/7/13 6:40 下午
     */
    public function user()
    {

        return $this->hasOne(user::class, 'id', 'user_id')
            ->field('id,sn,nickname,avatar,level,mobile,sex,create_time');
    }
}