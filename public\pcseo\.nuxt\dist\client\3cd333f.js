(window.webpackJsonp=window.webpackJsonp||[]).push([[44,15,18,20],{437:function(e,t,r){"use strict";var l=r(17),n=r(2),o=r(3),c=r(136),d=r(27),f=r(18),m=r(271),v=r(52),_=r(135),h=r(270),x=r(5),y=r(98).f,w=r(44).f,C=r(26).f,S=r(438),$=r(439).trim,N="Number",I=n.Number,k=I.prototype,E=n.TypeError,F=o("".slice),z=o("".charCodeAt),M=function(e){var t=h(e,"number");return"bigint"==typeof t?t:O(t)},O=function(e){var t,r,l,n,o,c,d,code,f=h(e,"number");if(_(f))throw E("Cannot convert a Symbol value to a number");if("string"==typeof f&&f.length>2)if(f=$(f),43===(t=z(f,0))||45===t){if(88===(r=z(f,2))||120===r)return NaN}else if(48===t){switch(z(f,1)){case 66:case 98:l=2,n=49;break;case 79:case 111:l=8,n=55;break;default:return+f}for(c=(o=F(f,2)).length,d=0;d<c;d++)if((code=z(o,d))<48||code>n)return NaN;return parseInt(o,l)}return+f};if(c(N,!I(" 0o1")||!I("0b1")||I("+0x1"))){for(var R,T=function(e){var t=arguments.length<1?0:I(M(e)),r=this;return v(k,r)&&x((function(){S(r)}))?m(Object(t),r,T):t},A=l?y(I):"MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,EPSILON,MAX_SAFE_INTEGER,MIN_SAFE_INTEGER,isFinite,isInteger,isNaN,isSafeInteger,parseFloat,parseInt,fromString,range".split(","),D=0;A.length>D;D++)f(I,R=A[D])&&!f(T,R)&&C(T,R,w(I,R));T.prototype=k,k.constructor=T,d(n,N,T)}},438:function(e,t,r){var l=r(3);e.exports=l(1..valueOf)},439:function(e,t,r){var l=r(3),n=r(33),o=r(16),c=r(440),d=l("".replace),f="["+c+"]",m=RegExp("^"+f+f+"*"),v=RegExp(f+f+"*$"),_=function(e){return function(t){var r=o(n(t));return 1&e&&(r=d(r,m,"")),2&e&&(r=d(r,v,"")),r}};e.exports={start:_(1),end:_(2),trim:_(3)}},440:function(e,t){e.exports="\t\n\v\f\r                　\u2028\u2029\ufeff"},441:function(e,t,r){var content=r(444);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[e.i,content,""]]),content.locals&&(e.exports=content.locals);(0,r(14).default)("3181fc86",content,!0,{sourceMap:!1})},442:function(e,t,r){"use strict";r.r(t);r(437),r(80),r(272);var l={data:function(){return{priceSlice:{}}},components:{},props:{firstSize:{type:Number,default:14},secondSize:{type:Number,default:14},color:{type:String},weight:{type:[String,Number],default:400},price:{type:[String,Number],default:""},showSubscript:{type:Boolean,default:!0},subscriptSize:{type:Number,default:14},lineThrough:{type:Boolean,default:!1}},created:function(){this.priceFormat()},watch:{price:function(e){this.priceFormat()}},methods:{priceFormat:function(){var e=this.price,t={};null!==e&&(e=parseFloat(e),e=String(e).split("."),t.first=e[0],t.second=e[1],this.priceSlice=t)}}},n=(r(443),r(9)),component=Object(n.a)(l,(function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("span",{class:(e.lineThrough?"line-through":"")+"price-format",style:{color:e.color,"font-weight":e.weight}},[e.showSubscript?r("span",{style:{"font-size":e.subscriptSize+"px","margin-right":"1px"}},[e._v("¥")]):e._e(),e._v(" "),r("span",{style:{"font-size":e.firstSize+"px","margin-right":"1px"}},[e._v(e._s(e.priceSlice.first))]),e._v(" "),e.priceSlice.second?r("span",{style:{"font-size":e.secondSize+"px"}},[e._v("."+e._s(e.priceSlice.second))]):e._e()])}),[],!1,null,null,null);t.default=component.exports},443:function(e,t,r){"use strict";r(441)},444:function(e,t,r){var l=r(13)(!1);l.push([e.i,".price-format{display:flex;align-items:baseline}",""]),e.exports=l},453:function(e,t,r){var content=r(466);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[e.i,content,""]]),content.locals&&(e.exports=content.locals);(0,r(14).default)("05ffbf2f",content,!0,{sourceMap:!1})},465:function(e,t,r){"use strict";r(453)},466:function(e,t,r){var l=r(13)(!1);l.push([e.i,".v-upload .el-upload--picture-card[data-v-05db7967]{width:76px;height:76px;line-height:76px}.v-upload .el-upload-list--picture-card .el-upload-list__item[data-v-05db7967]{width:76px;height:76px}",""]),e.exports=l},467:function(e,t,r){"use strict";r.r(t);r(437);var l=r(187),n={components:{},props:{limit:{type:Number,default:1},isSlot:{type:Boolean,default:!1},autoUpload:{type:Boolean,default:!0},onChange:{type:Function,default:function(){}}},watch:{},data:function(){return{url:l.a.baseUrl}},created:function(){},computed:{},methods:{success:function(e,t,r){this.autoUpload&&(this.$message({message:"上传成功",type:"success"}),this.$emit("success",r))},remove:function(e,t){this.$emit("remove",t)},error:function(e){this.$message({message:"上传失败，请重新上传",type:"error"})}}},o=(r(465),r(9)),component=Object(o.a)(n,(function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"v-upload"},[r("el-upload",{attrs:{"list-type":"picture-card",action:e.url+"/api/file/formimage",limit:e.limit,"on-success":e.success,"on-error":e.error,"on-remove":e.remove,"on-change":e.onChange,headers:{token:e.$store.state.token},"auto-upload":e.autoUpload}},[e.isSlot?e._t("default"):r("div",[r("div",{staticClass:"muted xs"},[e._v("上传图片")])])],2)],1)}),[],!1,null,"05db7967",null);t.default=component.exports},474:function(e,t,r){var content=r(490);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[e.i,content,""]]),content.locals&&(e.exports=content.locals);(0,r(14).default)("5eb5ac17",content,!0,{sourceMap:!1})},489:function(e,t,r){"use strict";r(474)},490:function(e,t,r){var l=r(13)(!1);l.push([e.i,".input-express .dialog-footer[data-v-13601821]{text-align:center}.input-express .dialog-footer .el-button[data-v-13601821]{width:160px}",""]),e.exports=l},501:function(e,t,r){"use strict";r.r(t);var l=r(6),n=(r(51),r(437),r(20),{components:{},data:function(){return{showDialog:!1,form:{business:"",number:"",desc:""},rules:{business:[{required:!0,message:"请输入物流公司"}],number:[{required:!0,message:"请输入快递单号"}]},fileList:[]}},props:{value:{type:Boolean,default:!1},aid:{type:[String,Number],default:-1}},methods:{submitForm:function(){var e=this;console.log(this.$refs),this.$refs.inputForm.validate(function(){var t=Object(l.a)(regeneratorRuntime.mark((function t(r){var l,data;return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(!r){t.next=10;break}return l=[],e.fileList.forEach((function(e){l.push(e.response.data)})),data={id:e.aid,express_name:e.form.business,invoice_no:e.form.number,express_remark:e.form.desc,express_image:l.length<=0?"":l[0].base_url},t.next=6,e.$post("after_sale/express",data);case 6:1==t.sent.code&&(e.$message({message:"提交成功",type:"success"}),e.showDialog=!1,e.$emit("success")),t.next=11;break;case 10:return t.abrupt("return",!1);case 11:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}())},uploadSuccess:function(e){var t=Object.assign([],e);this.fileList=t}},watch:{value:function(e){this.showDialog=e},showDialog:function(e){this.$emit("input",e)}}}),o=n,c=(r(489),r(9)),component=Object(c.a)(o,(function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"input-express"},[r("el-dialog",{attrs:{title:"填写快递单号",visible:e.showDialog,width:"926px"},on:{"update:visible":function(t){e.showDialog=t}}},[r("el-form",{ref:"inputForm",attrs:{inline:"","label-width":"100px",model:e.form,rules:e.rules}},[r("el-form-item",{attrs:{label:"物流公司：",prop:"business"}},[r("el-input",{attrs:{size:"small",placeholder:"请输入物流公司名称"},model:{value:e.form.business,callback:function(t){e.$set(e.form,"business",t)},expression:"form.business"}})],1),e._v(" "),r("el-form-item",{attrs:{label:"快递单号：",prop:"number"}},[r("el-input",{attrs:{size:"small",placeholder:"请输入快递单号"},model:{value:e.form.number,callback:function(t){e.$set(e.form,"number",t)},expression:"form.number"}})],1),e._v(" "),r("el-form-item",{attrs:{label:"备注说明：",prop:"desc"}},[r("el-input",{staticStyle:{width:"632px"},attrs:{type:"textarea",placeholder:"请输入详细内容，选填",resize:"none",rows:"5"},model:{value:e.form.desc,callback:function(t){e.$set(e.form,"desc",t)},expression:"form.desc"}})],1),e._v(" "),r("el-form-item",{attrs:{label:"上传凭证：",prop:"upload"}},[r("div",{staticClass:"xs muted"},[e._v("请上传快递单号凭证，选填")]),e._v(" "),r("upload",{attrs:{isSlot:"","file-list":e.fileList,limit:3},on:{success:e.uploadSuccess}},[r("div",{staticClass:"column-center",staticStyle:{height:"100%"}},[r("i",{staticClass:"el-icon-camera xs",staticStyle:{"font-size":"24px"}})])])],1)],1),e._v(" "),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确定")]),e._v(" "),r("el-button",{on:{click:function(t){e.showDialog=!1}}},[e._v("取消")])],1)],1)],1)}),[],!1,null,"13601821",null);t.default=component.exports;installComponents(component,{Upload:r(467).default})},548:function(e,t,r){var content=r(631);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[e.i,content,""]]),content.locals&&(e.exports=content.locals);(0,r(14).default)("f311a1f4",content,!0,{sourceMap:!1})},630:function(e,t,r){"use strict";r(548)},631:function(e,t,r){var l=r(13)(!1);l.push([e.i,'.apply-detail[data-v-7daeee73]{padding:10px}.apply-detail .apply-detail-header[data-v-7daeee73]{padding:15px 0;border-bottom:1px solid #e5e5e5}.apply-detail .apply-detail-address[data-v-7daeee73]{margin:0 10px;padding-top:16px;border-top:1px solid #e5e5e5}.apply-detail .apply-detail-address .copy[data-v-7daeee73]{margin-left:20px;padding:2px 6px;color:#ff2c3c;background-color:rgba(255,44,60,.2)}.apply-detail .result-content[data-v-7daeee73]{padding:24px 20px}.apply-detail .result-content .result-item[data-v-7daeee73]{margin-bottom:16px}.apply-detail .result-content .result-item:not(:last-of-type) .label[data-v-7daeee73]{width:82px;align-self:flex-start;text-align:right}.apply-detail .result-content .result-item:not(:last-of-type) .label[data-v-7daeee73]:before{content:"* ";color:red}.apply-detail .result-content .result-item .label[data-v-7daeee73]{width:82px;align-self:flex-start;text-align:right}.apply-detail .result-content .result-item .desc[data-v-7daeee73]{margin-left:24px;width:680px}.apply-detail .apply-detail-content .btn-group .apply-btn[data-v-7daeee73]{border:1px solid #ccc;border-radius:2px;width:100px;height:32px;align-self:flex-start;margin-right:10px}',""]),e.exports=l},676:function(e,t,r){"use strict";r.r(t);var l=r(6),n=(r(81),r(51),{head:function(){return{title:this.$store.getters.headTitle,link:[{rel:"icon",type:"image/x-icon",href:this.$store.getters.favicon}]}},layout:"user",data:function(){return{lists:{order_goods:[],shop:{}},showInput:!1}},mounted:function(){this.getDetail()},methods:{getDetail:function(){var e=this;return Object(l.a)(regeneratorRuntime.mark((function t(){var r,l;return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,e.$get("after_sale/detail",{params:{id:e.$route.query.afterSaleId}});case 2:1==(r=t.sent).code&&(l=[r.data.order_goods],r.data.order_goods=l,console.log(l),e.lists=r.data);case 4:case"end":return t.stop()}}),t)})))()},onCopy:function(){var e=document.createElement("input");e.value=this.lists.shop.address,document.body.appendChild(e),e.select(),document.execCommand("Copy"),this.$message.success("复制成功"),e.remove()},cancelApply:function(e){var t=this;return Object(l.a)(regeneratorRuntime.mark((function r(){var l;return regeneratorRuntime.wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return r.next=2,t.$post("after_sale/cancel",{id:e});case 2:1==(l=r.sent).code&&(t.$message({message:l.msg,type:"success"}),setTimeout((function(){t.$router.go(-1)}),500));case 4:case"end":return r.stop()}}),r)})))()},goRefund:function(e,t,r){this.$router.push("/user/after_sales/apply_result?afterSaleId="+e+"&order_id="+t+"&item_id="+r)}}}),o=(r(630),r(9)),component=Object(o.a)(n,(function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"apply-detail"},[r("div",{staticClass:"apply-detail-content"},[r("el-table",{staticStyle:{width:"100%"},attrs:{data:e.lists.order_goods}},[r("el-table-column",{attrs:{prop:"date",label:"商品信息","max-width":"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("div",{staticClass:"flex"},[r("el-image",{staticStyle:{width:"80px",height:"80px"},attrs:{src:t.row.image,fit:"fit"}}),e._v(" "),r("div",{staticClass:"m-l-10"},[r("div",{staticClass:"line-2"},[e._v("\n                                "+e._s(t.row.goods_name)+"\n                            ")]),e._v(" "),r("div",[e._v("\n                                "+e._s(t.row.spec_value)+"\n                            ")])])],1)]}}])}),e._v(" "),r("el-table-column",{attrs:{prop:"name",label:"价格",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                    ¥"+e._s(t.row.goods_price)+"\n                ")]}}])}),e._v(" "),r("el-table-column",{attrs:{prop:"goods_num",label:"数量",width:"180"}}),e._v(" "),r("el-table-column",{attrs:{prop:"address",label:"申请状态",width:"180"}},[[e._v(e._s(e.lists.status_text))]],2)],1),e._v(" "),r("div",{staticClass:"m-t-30",staticStyle:{padding:"0 20px"}},[r("div",{staticClass:"result-content"},[r("div",{staticClass:"result-item flex"},[r("div",{staticClass:"label"},[e._v("退款类型:")]),e._v(" "),r("div",{staticClass:"desc"},[e._v(e._s(e.lists.refund_type_text))])]),e._v(" "),r("div",{staticClass:"result-item flex"},[r("div",{staticClass:"label"},[e._v("退款原因:")]),e._v(" "),r("div",{staticClass:"desc"},[e._v(e._s(e.lists.refund_reason))])]),e._v(" "),r("div",{staticClass:"result-item flex"},[r("div",{staticClass:"label"},[e._v("退款金额:")]),e._v(" "),r("div",{staticClass:"desc"},[r("price-formate",{attrs:{price:e.lists.refund_price,showSubscript:"",color:"red"}})],1)]),e._v(" "),r("div",{staticClass:"result-item flex"},[r("div",{staticClass:"label"},[e._v("申请时间:")]),e._v(" "),r("div",{staticClass:"desc"},[e._v(e._s(e.lists.create_time))])]),e._v(" "),r("div",{staticClass:"result-item flex"},[r("div",{staticClass:"label"},[e._v("退款编号:")]),e._v(" "),r("div",{staticClass:"desc"},[e._v(e._s(e.lists.sn))])]),e._v(" "),r("div",{staticClass:"result-item flex"},[r("div",{staticClass:"label"},[e._v("退款说明:")]),e._v(" "),r("div",{staticClass:"column desc"},[r("div",{staticClass:"m-b-16"}),e._v(" "),e.lists.refund_image?r("el-image",{staticStyle:{width:"76px",height:"76px"},attrs:{src:e.lists.refund_image,"preview-src-list":[e.lists.refund_image]}}):e._e()],1)])])]),e._v(" "),"退款退货"==e.lists.refund_type_text&&2==e.lists.status?r("div",{staticClass:"apply-detail-address flex"},[e._v("\n            退货地址："+e._s(e.lists.shop.contact||"-")+","+e._s(e.lists.shop.mobile||"-")+", "+e._s(e.lists.shop.address||"-")+"\n            "),r("div",{staticClass:"copy pointer",on:{click:e.onCopy}},[e._v("复制")])]):e._e(),e._v(" "),r("div",{staticClass:"btn-group flex row-center m-t-60"},[r("el-popconfirm",{attrs:{title:"确定撤销商品吗？","confirm-button-text":"确定","cancel-button-text":"取消",icon:"el-icon-info","icon-color":"red"},on:{confirm:function(t){return e.cancelApply(e.lists.id)}}},[6!=e.lists.status?r("el-button",{staticClass:"apply-btn flex row-center sm",attrs:{slot:"reference",size:"small"},slot:"reference"},[e._v("撤销申请")]):e._e()],1),e._v(" "),2==e.lists.status?r("el-button",{staticClass:"apply-btn flex row-center sm",attrs:{size:"small"},on:{click:function(t){e.showInput=!0}}},[e._v("填写快递单号")]):e._e()],1)],1),e._v(" "),r("input-express",{attrs:{aid:e.lists.id},on:{success:e.getDetail},model:{value:e.showInput,callback:function(t){e.showInput=t},expression:"showInput"}})],1)}),[],!1,null,"7daeee73",null);t.default=component.exports;installComponents(component,{PriceFormate:r(442).default,InputExpress:r(501).default})}}]);