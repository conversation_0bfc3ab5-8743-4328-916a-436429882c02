<?php
namespace app\common\model;

use app\common\basics\Models;

/**
 * 商家行业数据模型
 * Class ShopIndustryData
 * @package app\common\model
 */
class ShopIndustryData extends Models
{
    protected $name = 'shop_industry_data'; // 指定数据表名 (不含前缀)
    protected $pk = 'id'; // 指定主键

    // 可在此处定义与其他模型的关联关系，例如：
    // public function category()
    // {
    //     // 假设 category_id 关联商品分类表 ls_goods_category
    //     return $this->belongsTo(GoodsCategory::class, 'category_id', 'id');
    // }

    // 可在此处添加模型事件、获取器、修改器等
}