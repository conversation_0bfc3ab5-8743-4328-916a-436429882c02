<?php
namespace app\common\service;

use think\facade\Db;

class StatService 
{
    /**
     * 记录商品访问数据
     */
    public static function recordGoodsView($goods_id, $shop_id, $user_id = 0)
    {
        try {
            $date = date('Y-m-d');
            $hour = date('H');
            
            // 更新商品小时统计
            Db::name('stat_hourly_goods')->insert([
                'shop_id' => $shop_id,
                'goods_id' => $goods_id,
                'date' => $date,
                'hour' => $hour,
                'view_count' => 1,
                'create_time' => time(),
                'update_time' => time()
            ], true)->onDuplicate([
                'view_count' => Db::raw('view_count + 1'),
                'update_time' => time()
            ]);

            // 更新商品转化统计
            Db::name('stat_goods_conversion')->insert([
                'shop_id' => $shop_id,
                'goods_id' => $goods_id,
                'date' => $date,
                'view_count' => 1,
                'create_time' => time(),
                'update_time' => time()
            ], true)->onDuplicate([
                'view_count' => Db::raw('view_count + 1'),
                'update_time' => time()
            ]);
        } catch (\Exception $e) {
            Log::error('记录商品访问数据异常：' . $e->getMessage());
        }
    }

    /**
     * 记录店铺访客数据
     */
    public static function recordShopVisitor($shop_id, $user_id = 0)
    {
        try {
            $date = date('Y-m-d');
            
            // 判断是否为新访客
            $is_new = 1;
            if ($user_id > 0) {
                $visit_history = Db::name('stat_shop_visitor')
                    ->where([
                        ['shop_id', '=', $shop_id],
                        ['date', '<', $date]
                    ])
                    ->find();
                $is_new = empty($visit_history) ? 1 : 0;
            }

            // 更新访客统计
            Db::name('stat_shop_visitor')->insert([
                'shop_id' => $shop_id,
                'date' => $date,
                'visitor_count' => 1,
                'new_visitor_count' => $is_new,
                'old_visitor_count' => $is_new ? 0 : 1,
                'create_time' => time(),
                'update_time' => time()
            ], true)->onDuplicate([
                'visitor_count' => Db::raw('visitor_count + 1'),
                'new_visitor_count' => Db::raw('new_visitor_count + ' . $is_new),
                'old_visitor_count' => Db::raw('old_visitor_count + ' . ($is_new ? 0 : 1)),
                'update_time' => time()
            ]);
        } catch (\Exception $e) {
            Log::error('记录店铺访客数据异常：' . $e->getMessage());
        }
    }

    /**
     * 记录订单数据
     */
    public static function recordOrder($order)
    {
        try {
            $date = date('Y-m-d');
            $hour = date('H');
            
            // 更新店铺小时统计
            Db::name('stat_hourly_shop')->insert([
                'shop_id' => $order['shop_id'],
                'date' => $date,
                'hour' => $hour,
                'sales_amount' => $order['order_amount'],
                'order_count' => 1,
                'customer_count' => 1,
                'create_time' => time(),
                'update_time' => time()
            ], true)->onDuplicate([
                'sales_amount' => Db::raw('sales_amount + ' . $order['order_amount']),
                'order_count' => Db::raw('order_count + 1'),
                'customer_count' => Db::raw('customer_count + 1'),
                'update_time' => time()
            ]);

            // 更新商品小时统计
            foreach ($order['order_goods'] as $goods) {
                Db::name('stat_hourly_goods')->insert([
                    'shop_id' => $order['shop_id'],
                    'goods_id' => $goods['goods_id'],
                    'date' => $date,
                    'hour' => $hour,
                    'sales_amount' => $goods['total_price'],
                    'sales_quantity' => $goods['quantity'],
                    'create_time' => time(),
                    'update_time' => time()
                ], true)->onDuplicate([
                    'sales_amount' => Db::raw('sales_amount + ' . $goods['total_price']),
                    'sales_quantity' => Db::raw('sales_quantity + ' . $goods['quantity']),
                    'update_time' => time()
                ]);
            }
        } catch (\Exception $e) {
            Log::error('记录订单数据异常：' . $e->getMessage());
        }
    }
}