{"name": "endroid/qr-code", "description": "Endroid QR Code", "keywords": ["endroid", "qrcode", "qr", "code", "bundle", "php"], "homepage": "https://github.com/endroid/qr-code", "type": "library", "license": "MIT", "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "require": {"php": ">=7.2", "bacon/bacon-qr-code": "^2.0", "khanamiryan/qrcode-detector-decoder": "^1.0.2", "symfony/options-resolver": "^3.4||^4.4||^5.0", "symfony/property-access": "^3.4||^4.4||^5.0", "myclabs/php-enum": "^1.5"}, "require-dev": {"endroid/quality": "^1.3.7", "setasign/fpdf": "^1.8"}, "suggest": {"ext-gd": "Required for generating PNG images", "roave/security-advisories": "Avoids installation of package versions with vulnerabilities", "setasign/fpdf": "Required to use the FPDF writer.", "symfony/security-checker": "Checks your composer.lock for vulnerabilities"}, "autoload": {"psr-4": {"Endroid\\QrCode\\": "src/"}}, "autoload-dev": {"psr-4": {"Endroid\\QrCode\\Tests\\": "tests/"}}, "config": {"sort-packages": true}, "extra": {"branch-alias": {"dev-master": "3.x-dev"}}}