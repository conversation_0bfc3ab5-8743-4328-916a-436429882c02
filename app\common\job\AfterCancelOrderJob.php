<?php

namespace app\common\job;

use think\queue\Job;
use app\common\listener\AfterCancelOrder;

class AfterCancelOrderJob
{
    public function fire(Job $job, $data)
    {
        try {
            $listener = new AfterCancelOrder();
            $result = $listener->handle($data);
            
            if ($result === false) {
                // 任务执行失败,重新加入队列
                $job->release(3); // 延迟3秒后重试
                return;
            }
            
            // 任务执行成功,删除任务
            $job->delete();
            
        } catch (\Exception $e) {
            // 记录错误日志
            \think\facade\Log::error('AfterCancelOrderJob 执行失败: ' . $e->getMessage());
            $job->release(3); // 失败后重试
        }
    }

    public function failed($data)
    {
        // 记录最终失败的任务
        \think\facade\Log::error('AfterCancelOrderJob 最终执行失败: ' . json_encode($data, JSON_UNESCAPED_UNICODE));
    }
}
