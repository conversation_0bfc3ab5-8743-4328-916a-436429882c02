<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-form layui-card-header layuiadmin-card-header-auto">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">目标类型</label>
                    <div class="layui-input-block">
                        <select name="target_type">
                            <option value="">全部</option>
                            <option value="admin">管理员</option>
                            <option value="shop">商家</option>
                            <option value="user">用户</option>
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">通知类型</label>
                    <div class="layui-input-block">
                        <input type="text" name="notification_type" placeholder="请输入通知类型" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">标题</label>
                    <div class="layui-input-block">
                        <input type="text" name="title" placeholder="请输入标题关键词" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">创建日期</label>
                    <div class="layui-input-block">
                        <input type="text" name="date_range" class="layui-input" id="ls-date-range" placeholder=" - ">
                    </div>
                </div>
                <div class="layui-inline">
                    <button class="layui-btn layuiadmin-btn-admin" lay-submit lay-filter="LAY-通知管理-search">
                        <i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>搜索
                    </button>
                </div>
            </div>
        </div>

        <div class="layui-card-body">
            <script type="text/html" id="toolbar-operation">
                <div class="layui-btn-container">
                    <button class="layui-btn layui-btn-sm layui-btn-danger" lay-event="batchdel">批量删除</button>
                </div>
            </script>
            <table id="LAY-通知管理-table" lay-filter="LAY-通知管理-table"></table>
            <script type="text/html" id="table-tool-bar">
                <a class="layui-btn layui-btn-xs" lay-event="detail">详情</a>
                <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
            </script>
        </div>
    </div>
</div>

<script>
    layui.use(['table', 'form', 'laydate', 'like'], function (table, form, laydate, like) {
        var $ = layui.$;
        var admin = layui.admin;

        // 日期范围
        laydate.render({
            elem: '#ls-date-range',
            range: true
        });

        // 表格渲染
        table.render({
            elem: '#LAY-通知管理-table',
            url: '{:url("lists")}',
            toolbar: '#toolbar-operation',
            cols: [[
                {type: 'checkbox', fixed: 'left'},
                {field: 'id', title: 'ID', width: 80, sort: true},
                {field: 'title', title: '标题', minWidth: 200},
                {field: 'content', title: '内容摘要', minWidth: 250, templet: function(d){
                    return d.content.length > 50 ? d.content.substring(0, 50) + '...' : d.content;
                }},
                {field: 'target_type', title: '目标类型', width: 100, templet: function(d){
                    if(d.target_type === 'admin') return '<span class="layui-badge layui-bg-blue">管理员</span>';
                    if(d.target_type === 'shop') return '<span class="layui-badge layui-bg-green">商家</span>';
                    if(d.target_type === 'user') return '<span class="layui-badge layui-bg-orange">用户</span>';
                    return '未知';
                }},
                {field: 'notification_type', title: '通知类型', width: 150},
                {field: 'url', title: '跳转链接', width: 150, templet: function(d){
                    return d.url ? '<a href="'+d.url+'" target="_blank" class="layui-table-link">查看</a>' : '-';
                }},
                {field: 'created_by_type', title: '创建来源', width: 120},
                {field: 'created_by_id', title: '创建者ID', width: 100},
                {field: 'created_at', title: '创建时间', width: 170, sort: true},
                {title: '操作', width: 150, align: 'center', fixed: 'right', toolbar: '#table-tool-bar'}
            ]],
            page: true,
            limit: 15,
            limits: [15, 30, 50, 100],
            text: {none: '暂无相关数据'}
        });

        // 工具条操作
        table.on('tool(LAY-通知管理-table)', function (obj) {
            var data = obj.data;
            if (obj.event === 'del') {
                layer.confirm('确定删除这条通知吗？', function (index) {
                    like.ajax({
                        url: '{:url("delete")}',
                        data: {ids: [data.id]},
                        type: 'post',
                        success: function (res) {
                            if (res.code == 1) {
                                obj.del();
                                layer.close(index);
                                layer.msg('删除成功', {icon: 1});
                            } else {
                                layer.msg(res.msg, {icon: 2});
                            }
                        }
                    });
                });
            } else if (obj.event === 'detail') {
                admin.popup({
                    title: '通知详情',
                    content: '', // Placeholder, will be loaded
                    area: ['700px', '500px'],
                    success: function(layero, index) {
                        // Load detail content via AJAX
                        $.ajax({
                            url: like.url.create('{:url("detail")}', {id: data.id}),
                            success: function(res) {
                                if (res.code == 1) {
                                    var detailHtml = '<div style="padding:20px;">';
                                    detailHtml += '<p><strong>ID:</strong> ' + res.data.id + '</p>';
                                    detailHtml += '<p><strong>标题:</strong> ' + res.data.title + '</p>';
                                    detailHtml += '<p><strong>内容:</strong></p><div style="white-space: pre-wrap; word-break: break-all; max-height: 200px; overflow-y: auto; background: #f9f9f9; padding: 10px; border-radius: 4px;">' + res.data.content + '</div>';
                                    detailHtml += '<p><strong>目标类型:</strong> ' + res.data.target_type + '</p>';
                                    detailHtml += '<p><strong>通知类型:</strong> ' + res.data.notification_type + '</p>';
                                    detailHtml += '<p><strong>目标用户IDs:</strong> ' + (res.data.target_user_ids ? JSON.stringify(res.data.target_user_ids) : '全体') + '</p>';
                                    detailHtml += '<p><strong>URL:</strong> ' + (res.data.url || '-') + '</p>';
                                    detailHtml += '<p><strong>图标:</strong> ' + (res.data.icon || '-') + '</p>';
                                    detailHtml += '<p><strong>创建来源:</strong> ' + (res.data.created_by_type || '-') + '</p>';
                                    detailHtml += '<p><strong>创建者ID:</strong> ' + (res.data.created_by_id || '-') + '</p>';
                                    detailHtml += '<p><strong>创建时间:</strong> ' + res.data.created_at + '</p>';
                                    detailHtml += '</div>';
                                    layero.find('.layui-layer-content').html(detailHtml);
                                } else {
                                    layer.msg(res.msg, {icon: 2});
                                }
                            },
                            error: function() {
                                layer.msg('加载详情失败', {icon: 2});
                            }
                        });
                    }
                });
            }
        });
        
        // 监听搜索
        form.on('submit(LAY-通知管理-search)', function(data){
            var field = data.field;
            table.reload('LAY-通知管理-table', {
                where: field,
                page: {
                    curr: 1 
                }
            });
        });

        // 批量删除
        table.on('toolbar(LAY-通知管理-table)', function(obj){
            var checkStatus = table.checkStatus(obj.config.id);
            var data = checkStatus.data; //获取选中的数据
            switch(obj.event){
                case 'batchdel':
                    if(data.length === 0){
                        layer.msg('请选择需要删除的通知');
                        return;
                    }
                    layer.confirm('确定删除选中的 ' + data.length + ' 条通知吗？', function(index){
                        var ids = data.map(function(item){ return item.id; });
                        like.ajax({
                            url: '{:url("delete")}',
                            data: {ids: ids},
                            type: 'post',
                            success: function (res) {
                                if (res.code == 1) {
                                    table.reload('LAY-通知管理-table');
                                    layer.close(index);
                                    layer.msg('删除成功', {icon: 1});
                                } else {
                                    layer.msg(res.msg, {icon: 2});
                                }
                            }
                        });
                    });
                break;
            }
        });
    });
</script>
