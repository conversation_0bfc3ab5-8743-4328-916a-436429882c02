<?php

class_alias(Swoole\Coroutine\Channel::class, Co\Channel::class);
class_alias(Swoole\Coroutine\Client::class, Co\Client::class);
class_alias(Swoole\Coroutine\Context::class, Co\Context::class);
class_alias(Swoole\Coroutine\Curl\Exception::class, Co\Curl\Exception::class);
class_alias(Swoole\Coroutine\Http2\Client::class, Co\Http2\Client::class);
class_alias(Swoole\Coroutine\Http2\Client\Exception::class, Co\Http2\Client\Exception::class);
class_alias(Swoole\Coroutine\Http\Client::class, Co\Http\Client::class);
class_alias(Swoole\Coroutine\Http\Client\Exception::class, Co\Http\Client\Exception::class);
class_alias(Swoole\Coroutine\Http\Server::class, Co\Http\Server::class);
class_alias(Swoole\Coroutine\Iterator::class, Co\Iterator::class);
class_alias(Swoole\Coroutine\MySQL::class, Co\MySQL::class);
class_alias(Swoole\Coroutine\MySQL\Exception::class, Co\MySQL\Exception::class);
class_alias(Swoole\Coroutine\MySQL\Statement::class, Co\MySQL\Statement::class);
class_alias(Swoole\Coroutine\Redis::class, Co\Redis::class);
class_alias(Swoole\Coroutine\Scheduler::class, Co\Scheduler::class);
class_alias(Swoole\Coroutine\Socket::class, Co\Socket::class);
class_alias(Swoole\Coroutine\Socket\Exception::class, Co\Socket\Exception::class);
class_alias(Swoole\Coroutine\System::class, Co\System::class);

class_alias(Swoole\Atomic::class, swoole_atomic::class);
class_alias(Swoole\Atomic\Long::class, swoole_atomic_long::class);
class_alias(Swoole\Client::class, swoole_client::class);
class_alias(Swoole\Connection\Iterator::class, swoole_connection_iterator::class);
class_alias(Swoole\Coroutine::class, co::class);
class_alias(Swoole\Coroutine\Channel::class, chan::class);
class_alias(Swoole\Error::class, swoole_error::class);
class_alias(Swoole\Event::class, swoole_event::class);
class_alias(Swoole\Exception::class, swoole_exception::class);
class_alias(Swoole\Http2\Request::class, swoole_http2_request::class);
class_alias(Swoole\Http2\Response::class, swoole_http2_response::class);
class_alias(Swoole\Http\Request::class, swoole_http_request::class);
class_alias(Swoole\Http\Response::class, swoole_http_response::class);
class_alias(Swoole\Http\Server::class, swoole_http_server::class);
class_alias(Swoole\Lock::class, swoole_lock::class);
class_alias(Swoole\Process::class, swoole_process::class);
class_alias(Swoole\Process\Pool::class, swoole_process_pool::class);
class_alias(Swoole\Redis\Server::class, swoole_redis_server::class);
class_alias(Swoole\Runtime::class, swoole_runtime::class);
class_alias(Swoole\Server::class, swoole_server::class);
class_alias(Swoole\Server\Port::class, swoole_server_port::class);
class_alias(Swoole\Server\Task::class, swoole_server_task::class);
class_alias(Swoole\Table::class, swoole_table::class);
class_alias(Swoole\Timer::class, swoole_timer::class);
class_alias(Swoole\Timer\Iterator::class, swoole_timer_iterator::class);
class_alias(Swoole\Websocket\Closeframe::class, swoole_websocket_closeframe::class);
class_alias(Swoole\Websocket\Frame::class, swoole_websocket_frame::class);
class_alias(Swoole\Websocket\Server::class, swoole_websocket_server::class);
