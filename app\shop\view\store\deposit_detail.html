<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>保证金明细</title>
    <link rel="stylesheet" href="__STATIC__/admin/css/layui.css">
    <style>
        body {
            background: #f8f9fa;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .detail-container {
            padding: 20px;
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .info-card {
            background: white;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .card-title {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 16px;
            padding-bottom: 8px;
            border-bottom: 2px solid #e9ecef;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 16px;
        }
        
        .info-item {
            display: flex;
            align-items: center;
            padding: 12px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        .info-label {
            font-weight: 600;
            color: #495057;
            min-width: 100px;
            margin-right: 12px;
        }
        
        .info-value {
            color: #2c3e50;
            flex: 1;
        }
        
        .amount-positive {
            color: #28a745;
            font-weight: 600;
        }
        
        .amount-negative {
            color: #dc3545;
            font-weight: 600;
        }
        
        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }
        
        .status-success {
            background: #d4edda;
            color: #155724;
        }
        
        .status-warning {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-info {
            background: #d1ecf1;
            color: #0c5460;
        }
        
        .layui-table-cell {
            height: auto !important;
            padding: 8px !important;
        }
        
        .table-container {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="detail-container">
        <!-- 保证金基本信息 -->
        <div class="info-card">
            <div class="card-title">
                <i class="layui-icon layui-icon-rmb"></i> 保证金基本信息
            </div>
            <div class="info-grid">
                <div class="info-item">
                    <span class="info-label">保证金金额:</span>
                    <span class="info-value amount-positive">¥{$deposit.deposit_amount|default=0}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">支付状态:</span>
                    <span class="info-value">
                        {if $deposit.pay_status == 1}
                            <span class="status-badge status-success">已支付</span>
                        {else}
                            <span class="status-badge status-warning">待支付</span>
                        {/if}
                    </span>
                </div>
                <div class="info-item">
                    <span class="info-label">审核状态:</span>
                    <span class="info-value">
                        {if $deposit.status == 1}
                            <span class="status-badge status-success">已通过</span>
                        {elseif $deposit.status == 0}
                            <span class="status-badge status-info">待审核</span>
                        {elseif $deposit.status == 2}
                            <span class="status-badge status-warning">需修改</span>
                        {else}
                            <span class="status-badge status-warning">未知状态</span>
                        {/if}
                    </span>
                </div>
                <div class="info-item">
                    <span class="info-label">创建时间:</span>
                    <span class="info-value">{$deposit.created_at|default=''}</span>
                </div>
            </div>
        </div>
        
        <!-- 明细列表 -->
        <div class="table-container">
            <div class="card-title">
                <i class="layui-icon layui-icon-list"></i> 保证金变动明细
            </div>
            <table id="detail-table" lay-filter="detail-table"></table>
        </div>
    </div>

    <script src="__STATIC__/admin/js/layui.js"></script>
    <script>
        layui.use(['table', 'layer'], function(){
            var table = layui.table;
            var layer = layui.layer;

            // 渲染表格
            table.render({
                elem: '#detail-table',
                url: '{:url("Store/depositDetail")}',
                method: 'GET',
                where: {
                    shop_id: '{$shop_id}',
                    deposit_id: '{$deposit_id|default=0}'
                },
                cols: [[
                    {field: 'id', title: 'ID', width: 80, align: 'center', sort: true},
                    {field: 'sn', title: '流水号', width: 180, align: 'center'},
                    {field: 'change_type_text', title: '变动类型', width: 120, align: 'center'},
                    {field: 'deposit_change_text', title: '变动金额', width: 150, align: 'center', templet: function(d) {
                        if (d.deposit_change > 0) {
                            return '<span class="amount-positive">+' + d.deposit_change + '</span>';
                        } else {
                            return '<span class="amount-negative">' + d.deposit_change + '</span>';
                        }
                    }},
                    {field: 'amount', title: '当前余额', width: 150, align: 'center', templet: function(d) {
                        return '<span class="amount-positive">¥' + (d.amount || 0) + '</span>';
                    }},
                    {field: 'reason', title: '变动原因', align: 'center', minWidth: 200},
                    {field: 'change_date', title: '变动日期', width: 150, align: 'center'},
                    {field: 'created_at', title: '创建时间', width: 180, align: 'center'}
                ]],
                page: true,
                limit: 15,
                limits: [10, 15, 20, 30],
                text: {
                    none: '暂无明细记录'
                },
                response: {
                    statusName: 'code',
                    statusCode: 1,
                    msgName: 'msg',
                    countName: 'count',
                    dataName: 'lists'
                },
                parseData: function(res) {
                    return {
                        "code": res.code,
                        "msg": res.msg,
                        "count": res.data ? res.data.count : 0,
                        "data": res.data ? res.data.lists : []
                    };
                }
            });
        });
    </script>
</body>
</html>
