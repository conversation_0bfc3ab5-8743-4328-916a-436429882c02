{layout name="layout1" /}

<style>
    .layui-table-cell { height: auto; }
    .tips { color: red}
    .mag-10 { margin-top: 10px}
</style>

<div class="wrapper">
    <div class="layui-card layui-form">
        <div class="layui-card-body">
            <div class="layui-collapse like-layui-collapse" style="border:1px dashed #c4c4c4">
                <div class="layui-colla-item">
                    <h2 class="layui-colla-title like-layui-colla-title" style="background-color: #fff">操作提示</h2>
                    <div class="layui-colla-content layui-show">
                        <p>* 版本更新需要逐个版本更新，<font class="tips">更新前请备份好系统和数据库</font>，更新成功后需要强制刷新站点；</p>
                        <p>* 系统没有做二次开发，可以直接选择在线更新功能；</p>
                        <p>* 系统已做二次开发，进行了功能修改，请谨慎选择在线更新功能，推荐以更新包的形式手动更新；</p>
                        <p>* <a href="https://home.likeshop.cn/article/95.html" target="_blank" class ="doc">关于后续版本的升级说明（推荐阅读）</a></p>
                        <p>* <font class="tips">更新至v1.3.0版本后,请执行 www.example.com/admin/distribution.center/updateTable, www.example.com为你的域名。</font></p>
                        <p>* <font class="tips">更新至v1.3.0版本后,商品分销比例需在分销应用-分销设置-分销商品单独设置。</font></p>
                        <p>* <font class="tips">更新至v1.4.1版本后,请执行 www.example.com/api/distribution/fixAncestorRelation, www.example.com为你的域名。</font></p>
                        <p>* v1.4.1版本新增商家移动端，支持打包发布为H5、小程序、APP。H5访问地址为 www.example.com/business, www.example.com为你的商城域名。</p>
                        <p>* <font class="tips">更新至v1.9.2版本后,客服域名配置请参考最新文档。</font></p>
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-card-body">
            <table id="lists" lay-filter="lists"></table>
            <script type="text/html" id="operation">

                <!--一键更新-->
                {{#  if(d.able_update === 1){ }}
                <a class="layui-btn layui-btn-sm layui-btn-normal mag-10" lay-event="update">一键更新</a> <br/>
                {{#  } }}

                <!--服务端更新包-->
                {{#  if(d.package_link != "undefined" && d.package_link != null && d.package_link != ""){ }}
                <a class="layui-btn layui-btn-sm layui-btn-primary mag-10" lay-event="download-server">服务端更新包</a> <br/>
                {{#  } }}

                <!--pc更新包-->
                {{#  if(d.pc_package_link != "undefined" && d.pc_package_link != null && d.pc_package_link != ""){ }}
                <a class="layui-btn layui-btn-sm layui-btn-primary mag-10" lay-event="download-pc">pc更新包</a> <br/>
                {{#  } }}

                <!--uniapp更新包-->
                {{#  if(d.uniapp_package_link != "undefined" && d.uniapp_package_link != null && d.uniapp_package_link != ""){ }}
                <a class="layui-btn layui-btn-sm layui-btn-primary mag-10" lay-event="download-uniapp">uniapp更新包</a> <br/>
                {{#  } }}

                <!--商家后台移动端更新包-->
                {{#  if(d.business_package_link != "undefined" && d.business_package_link != null && d.business_package_link != ""){ }}
                <a class="layui-btn layui-btn-sm layui-btn-primary mag-10" lay-event="download-business">商家移动端更新包</a> <br/>
                {{#  } }}

                <!--客服端更新包-->
                {{#  if(d.kefu_package_link != "undefined" && d.kefu_package_link != null && d.kefu_package_link != ""){ }}
                <a class="layui-btn layui-btn-sm layui-btn-primary mag-10" lay-event="download-kefu">客服端更新包</a> <br/>
                {{#  } }}

                <a class="layui-btn layui-btn-sm layui-btn-primary mag-10" lay-event="download-full">完整安装包</a>
            </script>

            <!--更新内容-->
            <script type="text/html" id="content">
                <div style="text-align: left">
                    {{#  layui.each(d.add, function(index, item){ }}
                    <li>
                        <span>{{ item }}</span>
                    <li>
                        {{# }); }}

                        {{# layui.each(d.optimize, function(index, item){ }}
                    <li>
                        <span>{{ item }}</span>
                    </li>
                    {{#  }); }}

                    {{#  layui.each(d.repair, function(index, item){ }}
                    <li>
                        <span>{{ item }}</span>
                    </li>
                    {{#  }); }}
                </div>
            </script>

            <!--版本-->
            <script type="text/html" id="version">
                {{#  if(d.new_version === 1){ }}
                    <span style="color: red">new-</span><span>{{d.version_no}}</span>
                {{#  } else { }}
                    <span>{{d.version_no}}</span>
                {{#  } }}
                <p>{{d.version_str}}</p>
            </script>

            <!--更新注意提示-->
            <script type="text/html" id="notice">
                {{#  layui.each(d.notice, function(index, item){ }}
                <li>
                    <span>{{ item }}</span>
                </li>
                {{#  }); }}
            </script>

        </div>
    </div>
</div>

<script>
    layui.use(['table'], function () {
        var table = layui.table;
        var layer = layui.layer;
        var $ = layui.$;


        like.tableLists('#lists', '{:url("system.Upgrade/index")}', [
            {field: 'create_time', title: '发布日期', width: 200, align: 'center'}
            , {field: 'version_no', title: '版本', width: 200, align: 'center', templet: '#version'}
            , {field: 'content', title: '版本内容', width: 300, align: 'center', templet: '#content'}
            , {field: 'notice', title: '注意事项', width: 300, align: 'center', templet: '#notice'}
            , {title: '操作', align: 'center', width: 300, toolbar: '#operation'}
        ]);


        // 监听行工具栏按钮
        table.on('tool(lists)', function (obj) {
            var data = obj.data;
            var layEvent = obj.event;

            // 一键更新
            if (layEvent === 'update') {
                layer.open({
                    type: 2,
                    title: '请选择操作',
                    content: '{:url("system.upgrade/choosePage")}',
                    area: ['60%', '60%'],
                    success: function(layero, index){
                        var iframe = window['layui-layer-iframe'+index];
                        iframe.setVersionData(obj.data);
                    }
                });
            }


            // 下载服务端更新包
            if (layEvent === 'download-server') {
                data.update_type = 2; //服务端更新包类型
                like.ajax({
                    url: '{:url("system.upgrade/getPkg")}',
                    type: 'post',
                    data: data,
                    success: function(res) {
                        if(res.code == 1) {
                            // 下载更新包
                            window.location.href = res.data.link;
                        }
                    }
                });
            }


            //下载pc端更新包
            if (layEvent === 'download-pc') {
                data.update_type = 3; // pc端更新包类型
                like.ajax({
                    url: '{:url("system.upgrade/getPkg")}',
                    type: 'post',
                    data: data,
                    success: function(res) {
                        if(res.code == 1) {
                            // 下载更新包
                            window.location.href = res.data.link;
                        }
                    }
                });
            }

            //下载uniapp更新包
            if (layEvent === 'download-uniapp') {
                data.update_type = 4;
                like.ajax({
                    url: '{:url("system.upgrade/getPkg")}',
                    type: 'post',
                    data: data,
                    success: function(res) {
                        if(res.code == 1) {
                            // 下载更新包
                            window.location.href = res.data.link;
                        }
                    }
                });
            }

            // 下载商家移动端更新包 download-business
            if (layEvent === 'download-business') {
                data.update_type = 7;
                like.ajax({
                    url: '{:url("system.upgrade/getPkg")}',
                    type: 'post',
                    data: data,
                    success: function(res) {
                        if(res.code == 1) {
                            // 下载更新包
                            window.location.href = res.data.link;
                        }
                    }
                });
            }

            // 下载客服端更新包 download-kefu
            if (layEvent === 'download-kefu') {
                data.update_type = 8;
                like.ajax({
                    url: '{:url("system.upgrade/getPkg")}',
                    type: 'post',
                    data: data,
                    success: function(res) {
                        if(res.code == 1) {
                            // 下载更新包
                            window.location.href = res.data.link;
                        }
                    }
                });
            }


            // 下载完整安装包
            if (layEvent === 'download-full') {
                data.update_type = 6;
                like.ajax({
                    url: '{:url("system.upgrade/getPkg")}',
                    type: 'post',
                    data: data,
                    success: function(res) {
                        if(res.code == 1) {
                            window.open(res.data.link);
                        }
                    }
                });
            }
        });
    });
</script>