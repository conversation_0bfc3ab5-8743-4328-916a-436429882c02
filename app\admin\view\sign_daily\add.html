{layout name="layout2" /}

    <div class="layui-card layui-form" style=" box-shadow:none;" >
        <div class="layui-card-body">
            <!--连续签到-->
            <div class="layui-form-item">
                <label class="layui-form-label">连续签到：</label>
                <div class="layui-input-inline">
                    <input type="number" name="days" lay-verify="required" lay-verType="tips" placeholder="请输入天数" min="0"
                           autocomplete="off" class="layui-input" >
                </div>
                <label class="layui-form-mid">天</label>
            </div>
            <!--签到奖励- 赠送积分-->
            <div class="layui-form-item">
                <label class="layui-form-label">签到奖励：</label>
                <div class="layui-input-inline " style="margin-right: 0px;width: 110px">
                    <input type="checkbox" name="integral_status" title="赠送积分" lay-skin="primary">
                </div>
                <div class="layui-input-inline">
                    <input type="number" name="integral" lay-verType="tips" placeholder="请输入积分"  min="0"
                           autocomplete="off" class="layui-input">
                </div>
                <div class="layui-input-inline">
                    <label class="layui-form-mid">积分</label>
                </div>
            </div>
            <!--签到奖励- 赠送成长值-->
            <div class="layui-form-item">
                <label class="layui-form-label"></label>
                <div class="layui-input-inline" style="margin-right: 0px;width: 110px">
                    <input type="checkbox" name="growth_status" title="赠送成长值" lay-skin="primary">
                </div>
                <div class="layui-input-inline">
                    <input type="number" name="growth"  lay-verType="tips" placeholder="请输入成长值"  min="0"
                           autocomplete="off" class="layui-input">
                </div>
                <div class="layui-input-inline">
                    <label class="layui-form-mid">成长值</label>
                </div>
            </div>

            <div class="layui-form-item layui-hide">
                <input type="button" lay-submit lay-filter="addSubmit" id="addSubmit" value="确认">
            </div>
        </div>
    </div>

<script>
    layui.use(['table'], function () {
        var $ = layui.$
            , form = layui.form;
    });
</script>