<?php
namespace app\admin\logic\agent;

use app\common\basics\Logic;
use app\common\model\agent\AgentMerchantfees;
use think\facade\Db;

/**
 * 代理保证金管理逻辑类
 */
class AgentDepositLogic extends Logic
{
    /**
     * 获取代理保证金明细列表
     * @param array $params 查询参数
     * @return array
     */
    public static function depositDetailsList($params)
    {
        $where = [];

        // 用户ID筛选
        if (isset($params['user_id']) && !empty($params['user_id'])) {
            $where[] = ['user_id', '=', $params['user_id']];
        }

        // 保证金ID筛选
        if (isset($params['deposit_id']) && !empty($params['deposit_id'])) {
            $where[] = ['deposit_id', '=', $params['deposit_id']];
        }

        // 变动类型筛选
        if (isset($params['change_type']) && $params['change_type'] !== '') {
            $where[] = ['change_type', '=', $params['change_type']];
        }

        // 时间范围筛选
        if (!empty($params['start_date'])) {
            $where[] = ['change_date', '>=', $params['start_date']];
        }
        if (!empty($params['end_date'])) {
            $where[] = ['change_date', '<=', $params['end_date']];
        }

        // 查询保证金明细记录
        $lists = Db::name('agent_deposit_details')
            ->where($where)
            ->order('id', 'desc')
            ->page($params['page'] ?? 1, $params['limit'] ?? 10)
            ->select()
            ->toArray();

        // 获取总记录数
        $count = Db::name('agent_deposit_details')->where($where)->count();

        // 处理数据
        foreach ($lists as &$item) {
            // 获取用户信息
            $user = Db::name('user')->where('id', $item['user_id'])->field('nickname, sn, mobile')->find();
            if ($user) {
                $item['user_nickname'] = $user['nickname'];
                $item['user_sn'] = $user['sn'];
                $item['user_mobile'] = $user['mobile'];
            }

            // 变动类型文本
            $types = [
                1 => '缴纳',
                2 => '增加',
                3 => '扣除',
                4 => '退还'
            ];
            $item['change_type_text'] = $types[$item['change_type']] ?? '未知';

            // 格式化日期
            if (isset($item['change_date'])) {
                $item['change_date'] = date('Y-m-d', strtotime($item['change_date']));
            }

            // 确保金额格式正确
            $item['deposit_change'] = floatval($item['deposit_change']);
            $item['amount'] = floatval($item['amount']);
        }

        // 确保返回的数据格式正确
        foreach ($lists as &$item) {
            // 确保所有字段都存在
            $item['id'] = isset($item['id']) ? intval($item['id']) : 0;
            $item['sn'] = isset($item['sn']) ? $item['sn'] : '';
            $item['change_type'] = isset($item['change_type']) ? intval($item['change_type']) : 0;
            $item['deposit_change'] = isset($item['deposit_change']) ? floatval($item['deposit_change']) : 0;
            $item['reason'] = isset($item['reason']) ? $item['reason'] : '';
            $item['remark'] = isset($item['remark']) ? $item['remark'] : '';
            $item['change_date'] = isset($item['change_date']) ? $item['change_date'] : '';
            $item['created_at'] = isset($item['created_at']) ? $item['created_at'] : '';
        }

        return [
            'count' => $count,
            'lists' => $lists
        ];
    }

    /**
     * 调整代理保证金
     * @param array $data 调整数据
     * @return bool
     */
    public static function adjustDeposit($data)
    {
        Db::startTrans();
        try {
            // 验证参数
            if (empty($data['user_id']) || empty($data['deposit_id']) ||
                empty($data['change_type']) || empty($data['amount']) ||
                empty($data['reason'])) {
                throw new \Exception('请填写完整的表单信息');
            }

            // 查询保证金记录
            $deposit = AgentMerchantfees::where('id', $data['deposit_id'])->find();
            if (!$deposit) {
                throw new \Exception('保证金记录不存在');
            }

            // 检查用户ID是否匹配
            if ($deposit['user_id'] != $data['user_id']) {
                throw new \Exception('用户ID与保证金记录不匹配');
            }

            // 计算当前余额
            $current_balance = self::calculateCurrentBalance($data['deposit_id']);

            // 如果是扣除，检查余额是否足够
            if ($data['change_type'] == 3) {
                if ($data['amount'] > floatval($current_balance)) {
                    throw new \Exception('保证金余额不足，当前余额：' . $current_balance);
                }

                // 扣除时，金额为负数
                $deposit_change = -$data['amount'];
            } else {
                // 增加时，金额为正数
                $deposit_change = $data['amount'];
            }

            // 创建保证金明细记录
            $detail_data = [
                'user_id' => $data['user_id'],
                'deposit_id' => $data['deposit_id'],
                'sn' => createSn('agent_deposit_details', 'sn'),
                'deposit_change' => $deposit_change, // 扣除为负，增加为正
                'change_type' => $data['change_type'],
                'amount' => abs($deposit_change), // 保存绝对值
                'reason' => $data['reason'],
                'remark' => $data['remark'] ?? '',
                'admin_id' => $data['admin_id'] ?? 0,
                'change_date' => date('Y-m-d'),
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            // 插入明细记录
            $result = Db::name('agent_deposit_details')->insert($detail_data);

            if (!$result) {
                throw new \Exception('保证金明细记录创建失败');
            }

            // 如果保证金被扣除，检查代理状态
            if ($data['change_type'] == 3) {
                // 如果扣除后余额为0或负数，可以设置代理状态为冻结
                if (($current_balance - $data['amount']) <= 0) {
                    // 更新代理状态为冻结
                    Db::name('agent')->where('user_id', $data['user_id'])->update([
                        'is_freeze' => 1, // 假设1表示冻结状态
                        'update_time' => time()
                    ]);
                }
            }

            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            self::$error = $e->getMessage();
            return false;
        }
    }

    /**
     * 计算当前保证金余额
     * @param int $deposit_id 保证金ID
     * @return float 当前余额
     */
    public static function calculateCurrentBalance($deposit_id)
    {
        // 获取保证金记录
        $deposit = AgentMerchantfees::where('id', $deposit_id)->find();
        if (!$deposit) {
            return 0;
        }

        // 获取所有明细记录的变动金额总和
        $changes_sum = Db::name('agent_deposit_details')
            ->where('deposit_id', $deposit_id)
            ->sum('deposit_change');

        // 计算当前余额 = 初始金额 + 变动金额总和
        $current_balance = $deposit['amount'] + floatval($changes_sum);

        return max(0, $current_balance); // 确保余额不为负数
    }
}
