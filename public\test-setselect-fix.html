<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>setSelect方法修复测试</title>
    <link rel="stylesheet" href="/static/plug/layui-admin/dist/layuiadmin/layui/css/layui.css" media="all">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .log-entry {
            margin: 2px 0;
        }
        .log-success { color: #28a745; }
        .log-error { color: #dc3545; }
        .log-info { color: #17a2b8; }
        .layui-form-item {
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 setSelect方法修复测试</h1>
        <p>这个页面用于测试修复后的like.setSelect方法，确保下拉选择框能正常渲染。</p>
        
        <div class="test-section">
            <h3>📋 测试表单</h3>
            <form class="layui-form" action="">
                <div class="layui-form-item">
                    <label class="layui-form-label">商家分类：</label>
                    <div class="layui-input-block">
                        <select name="shop_cate_id" lay-verify="required">
                            <option value="">请选择分类</option>
                        </select>
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <label class="layui-form-label">商品单位：</label>
                    <div class="layui-input-block">
                        <select name="unit_id">
                            <option value="">请选择单位</option>
                        </select>
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <label class="layui-form-label">商品品牌：</label>
                    <div class="layui-input-block">
                        <select name="brand_id">
                            <option value="">请选择品牌</option>
                        </select>
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <div class="layui-input-block">
                        <button type="button" class="layui-btn" onclick="testSetSelect()">🧪 测试setSelect方法</button>
                        <button type="button" class="layui-btn layui-btn-normal" onclick="testWithSelectedValue()">🎯 测试带选中值</button>
                        <button type="button" class="layui-btn layui-btn-warm" onclick="clearSelects()">🗑️ 清空选择框</button>
                    </div>
                </div>
            </form>
        </div>
        
        <div class="test-section">
            <h3>📝 测试日志</h3>
            <div id="logContainer" class="log"></div>
            <button type="button" class="layui-btn layui-btn-sm" onclick="clearLog()">清空日志</button>
        </div>
        
        <div class="test-section">
            <h3>🔍 测试说明</h3>
            <p><strong>测试步骤：</strong></p>
            <ol>
                <li>点击"测试setSelect方法"按钮，检查是否能正常填充选项</li>
                <li>点击"测试带选中值"按钮，检查是否能正确选中指定值</li>
                <li>查看浏览器控制台，确认没有"like.setSelect is not a function"错误</li>
                <li>检查下拉框是否正确显示选项和选中状态</li>
            </ol>
        </div>
    </div>

    <!-- 引入layui -->
    <script src="/static/plug/layui-admin/dist/layuiadmin/layui/layui.js"></script>
    
    <script>
        // 日志函数
        function log(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.textContent = `[${timestamp}] ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
            
            // 同时输出到控制台
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
        
        // 清空日志
        function clearLog() {
            document.getElementById('logContainer').innerHTML = '';
        }
        
        // 测试数据
        var testData = {
            categories: [
                {id: 1, name: '电子产品'},
                {id: 2, name: '服装鞋帽'},
                {id: 3, name: '家居用品'},
                {id: 4, name: '食品饮料'}
            ],
            units: [
                {id: 1, name: '件'},
                {id: 2, name: '个'},
                {id: 3, name: '套'},
                {id: 4, name: '盒'}
            ],
            brands: [
                {id: 1, name: '苹果'},
                {id: 2, name: '华为'},
                {id: 3, name: '小米'},
                {id: 4, name: '三星'}
            ]
        };
        
        // 测试setSelect方法
        function testSetSelect() {
            try {
                log('开始测试setSelect方法...', 'info');
                
                // 检查like对象是否存在
                if (typeof like === 'undefined') {
                    log('❌ like对象不存在', 'error');
                    return;
                }
                
                // 检查setSelect方法是否存在
                if (typeof like.setSelect !== 'function') {
                    log('❌ like.setSelect方法不存在', 'error');
                    return;
                }
                
                log('✅ like.setSelect方法存在', 'success');
                
                // 测试填充分类选择框
                like.setSelect('', testData.categories, "shop_cate_id", '请选择分类');
                log('✅ 商家分类选择框已填充', 'success');
                
                // 测试填充单位选择框
                like.setSelect('', testData.units, "unit_id", '请选择单位');
                log('✅ 商品单位选择框已填充', 'success');
                
                // 测试填充品牌选择框
                like.setSelect('', testData.brands, "brand_id", '请选择品牌');
                log('✅ 商品品牌选择框已填充', 'success');
                
                log('🎉 setSelect方法测试完成！', 'success');
                
            } catch (error) {
                log('❌ 测试过程中发生错误: ' + error.message, 'error');
                console.error('setSelect测试错误:', error);
            }
        }
        
        // 测试带选中值的setSelect
        function testWithSelectedValue() {
            try {
                log('开始测试带选中值的setSelect...', 'info');
                
                // 测试选中特定值
                like.setSelect('2', testData.categories, "shop_cate_id", '请选择分类');
                log('✅ 商家分类已选中"服装鞋帽"', 'success');
                
                like.setSelect('3', testData.units, "unit_id", '请选择单位');
                log('✅ 商品单位已选中"套"', 'success');
                
                like.setSelect('1', testData.brands, "brand_id", '请选择品牌');
                log('✅ 商品品牌已选中"苹果"', 'success');
                
                log('🎯 带选中值的测试完成！', 'success');
                
            } catch (error) {
                log('❌ 带选中值测试错误: ' + error.message, 'error');
                console.error('带选中值测试错误:', error);
            }
        }
        
        // 清空选择框
        function clearSelects() {
            try {
                $('select[name="shop_cate_id"]').empty().append('<option value="">请选择分类</option>');
                $('select[name="unit_id"]').empty().append('<option value="">请选择单位</option>');
                $('select[name="brand_id"]').empty().append('<option value="">请选择品牌</option>');
                
                if (typeof layui !== 'undefined' && layui.form) {
                    layui.form.render('select');
                }
                
                log('🗑️ 所有选择框已清空', 'info');
            } catch (error) {
                log('❌ 清空选择框错误: ' + error.message, 'error');
            }
        }
        
        // 页面加载完成后初始化
        layui.config({
            base: '/static/plug/layui-admin/dist/layuiadmin/'
        }).extend({
            like: 'modules/like'
        }).use(['like', 'form'], function() {
            var like = layui.like;
            var form = layui.form;
            
            // 将like暴露到全局
            window.like = like;
            
            log('✅ Layui和like模块加载完成', 'success');
            log('💡 现在可以点击测试按钮验证setSelect方法', 'info');
        });
    </script>
</body>
</html>
