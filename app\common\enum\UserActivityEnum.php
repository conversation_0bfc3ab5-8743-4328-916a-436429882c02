<?php

namespace app\common\enum;

/**
 * 用户活跃度枚举
 * Class UserActivityEnum
 * @package app\common\enum
 */
class UserActivityEnum
{
    // 活动类型
    const ACTIVITY_PURCHASER_LOGIN = 'purchaser_login';  // 采购商登录
    const ACTIVITY_PUBLISH_DEMAND = 'publish_demand';    // 发布采购信息
    const ACTIVITY_CHAT = 'chat';                        // 用户聊天
    const ACTIVITY_PURCHASE = 'purchase';                // 购买商品

    // 活动类型描述
    const ACTIVITY_DESC = [
        self::ACTIVITY_PURCHASER_LOGIN => '采购商登录',
        self::ACTIVITY_PUBLISH_DEMAND => '发布采购信息',
        self::ACTIVITY_CHAT => '用户聊天',
        self::ACTIVITY_PURCHASE => '购买商品',
    ];

    // 配置键名
    const CONFIG_PURCHASER_LOGIN_SCORE = 'purchaser_login_score';
    const CONFIG_PUBLISH_DEMAND_SCORE = 'publish_demand_score';
    const CONFIG_CHAT_SCORE = 'chat_score';
    const CONFIG_PURCHASE_SCORE = 'purchase_score';
    const CONFIG_LEVEL_1_SCORE = 'level_1_score';
    const CONFIG_LEVEL_2_SCORE = 'level_2_score';
    const CONFIG_LEVEL_3_SCORE = 'level_3_score';
    const CONFIG_LEVEL_4_SCORE = 'level_4_score';
    const CONFIG_LEVEL_5_SCORE = 'level_5_score';
    const CONFIG_IS_ENABLED = 'is_enabled';
    const CONFIG_LOGIN_CHECK_DAYS = 'login_check_days';
    const CONFIG_CHAT_DAILY_LIMIT = 'chat_daily_limit';

    // 默认配置值
    const DEFAULT_CONFIG = [
        self::CONFIG_PURCHASER_LOGIN_SCORE => 10,
        self::CONFIG_PUBLISH_DEMAND_SCORE => 20,
        self::CONFIG_CHAT_SCORE => 1,
        self::CONFIG_PURCHASE_SCORE => 20,
        self::CONFIG_LEVEL_1_SCORE => 100,
        self::CONFIG_LEVEL_2_SCORE => 300,
        self::CONFIG_LEVEL_3_SCORE => 500,
        self::CONFIG_LEVEL_4_SCORE => 1000,
        self::CONFIG_LEVEL_5_SCORE => 2000,
        self::CONFIG_IS_ENABLED => 1,
        self::CONFIG_LOGIN_CHECK_DAYS => 7,
        self::CONFIG_CHAT_DAILY_LIMIT => 10, // 每天聊天最多获得积分次数
    ];

    // 等级名称
    const LEVEL_NAMES = [
        0 => '无等级',
        1 => '活跃新手',
        2 => '活跃用户',
        3 => '活跃达人',
        4 => '活跃专家',
        5 => '活跃大师',
    ];

    /**
     * 获取活动类型描述
     * @param string $type
     * @return string
     */
    public static function getActivityDesc($type)
    {
        return self::ACTIVITY_DESC[$type] ?? '未知活动';
    }

    /**
     * 获取等级名称
     * @param int $level
     * @return string
     */
    public static function getLevelName($level)
    {
        return self::LEVEL_NAMES[$level] ?? '未知等级';
    }

    /**
     * 获取所有活动类型
     * @return array
     */
    public static function getAllActivityTypes()
    {
        return [
            self::ACTIVITY_PURCHASER_LOGIN,
            self::ACTIVITY_PUBLISH_DEMAND,
            self::ACTIVITY_CHAT,
            self::ACTIVITY_PURCHASE,
        ];
    }

    /**
     * 获取所有等级配置键
     * @return array
     */
    public static function getAllLevelConfigKeys()
    {
        return [
            self::CONFIG_LEVEL_1_SCORE,
            self::CONFIG_LEVEL_2_SCORE,
            self::CONFIG_LEVEL_3_SCORE,
            self::CONFIG_LEVEL_4_SCORE,
            self::CONFIG_LEVEL_5_SCORE,
        ];
    }
}
