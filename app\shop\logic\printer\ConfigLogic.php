<?php


namespace app\shop\logic\printer;

use app\common\basics\Logic;
use app\common\model\printer\PrinterConfig;

/**
 * 打印设置逻辑层
 * Class ConfigLogic
 * @package app\admin\logic\printer
 */
class ConfigLogic extends Logic
{

    /**
     * @notes 打印设置列表
     * @param $shop_id
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date 2022/1/19 17:20
     */
    public static function lists($shop_id)
    {
        self::createDefaultConfig($shop_id);

        $lists = PrinterConfig::where(['del' => 0, 'shop_id' => $shop_id])->select();

        return ['lists' => $lists];
    }



    /**
     * @notes 创建当前门店默认打印机配置
     * @param $shop_id
     * <AUTHOR>
     * @date 2022/1/19 17:20
     */
    public static function createDefaultConfig($shop_id)
    {
        $config = PrinterConfig::where(['shop_id' => $shop_id, 'del' => 0])->findOrEmpty();

        if ($config->isEmpty()) {
            PrinterConfig::create([
                'name' => '易联云',
                'shop_id' => $shop_id,
                'client_id' => 0,
                'client_secret' => 0,
                'type' => 1,
                'update_time' => time(),
            ]);
        }
    }


    /**
     * @notes 配置详情
     * @param $id
     * @param $shop_id
     * @return array|\think\Model
     * <AUTHOR>
     * @date 2022/1/19 17:21
     */
    public static function getDetail($id, $shop_id)
    {
        return PrinterConfig::where(['id' => $id, 'shop_id' => $shop_id])->findOrEmpty();
    }



    /**
     * @notes 设置配置
     * @param $post
     * @param $shop_id
     * @return PrinterConfig
     * <AUTHOR>
     * @date 2022/1/19 17:21
     */
    public static function editConfig($post, $shop_id)
    {
        $post['status'] = isset($post['status']) && $post['status'] == 'on' ? 1 : 0;
        if ($post['status']) {
            PrinterConfig::where(['status' => 1, 'shop_id' => $shop_id])->update(['status' => 0]);
        }
        $update_data = [
            'client_id' => $post['client_id'],
            'client_secret' => $post['client_secret'],
            'update_time' => time(),
            'status' => $post['status'],
        ];
        return PrinterConfig::where(['id' => $post['id'], 'shop_id' => $shop_id])->update($update_data);
    }

}