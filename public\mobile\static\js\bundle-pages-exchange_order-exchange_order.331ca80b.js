(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["bundle-pages-exchange_order-exchange_order","bundle-pages-after_sales_detail-after_sales_detail~bundle-pages-contact_offical-contact_offical~bund~a5ef3ab8"],{"01f3":function(t,e,n){"use strict";n("7a82");var i=n("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var r=i(n("f07e")),a=i(n("c964"));n("a9e3"),n("99af");var o=i(n("bde1")),s=i(n("4316")),u=n("c60f"),c={mixins:[o.default,s.default],data:function(){return{orderList:[],downOption:{auto:!1},upOption:{auto:!1,noMoreSize:4,empty:{icon:"/static/images/order_null.png",tip:"暂无订单~",fixed:!0}},showCancel:!1,type:0,orderId:"",showLoading:!1}},props:{orderType:{type:[Number,String]}},created:function(){var t=this;uni.$on("refreshorder",(function(){t.refresh()})),uni.$on("payment",(function(e){setTimeout((function(){console.log(e),e.result?(t.$toast({title:"支付成功"}),t.refresh()):t.$toast({title:"支付失败"})}),500)}))},destroyed:function(){uni.$off("payment"),uni.$off("refreshorder")},methods:{confirmDialog:function(){var t=this;return(0,a.default)((0,r.default)().mark((function e(){var n,i,a;return(0,r.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:n=t.type,i=t.orderId,a=null,e.t0=n,e.next=0===e.t0?5:1===e.t0?9:2===e.t0?13:17;break;case 5:return e.next=7,(0,u.cancelIntegralOrder)(i);case 7:return a=e.sent,e.abrupt("break",17);case 9:return e.next=11,(0,u.delIntegralOrder)(i);case 11:return a=e.sent,e.abrupt("break",17);case 13:return e.next=15,(0,u.confirmIntegralOrder)(i);case 15:return a=e.sent,e.abrupt("break",17);case 17:1==a.code&&(t.refresh(),t.$toast({title:a.msg}));case 18:case"end":return e.stop()}}),e)})))()},dialogOpen:function(){this.$refs.orderDialog.open()},refresh:function(){this.mescroll.resetUpScroll()},handleOrder:function(t,e){var n=this;this.orderId=t,this.type=e,this.$nextTick((function(){n.dialogOpen()}))},upCallback:function(t){var e=this,n=t.num,i=t.size,r=this.orderType;(0,u.getIntegralOrder)({page_size:i,page_no:n,type:r}).then((function(n){var i=n.data,r=i.list,a=r.length,o=!!i.more;1==t.num&&(e.orderList=[]),e.orderList=e.orderList.concat(r),e.mescroll.endSuccess(a,o)}))}},computed:{}};e.default=c},"069f":function(t,e,n){"use strict";n("7a82");var i=n("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var r=i(n("f07e")),a=i(n("c964"));n("a9e3");var o={props:{type:Number,orderId:[Number,String]},data:function(){return{show:!1}},methods:{open:function(){this.show=!0},close:function(){this.show=!1},onConfirm:function(){var t=this;return(0,a.default)((0,r.default)().mark((function e(){return(0,r.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.type,t.orderId,null,t.$emit("confirm");case 3:case"end":return e.stop()}}),e)})))()}},computed:{getTipsText:function(){var t=this.type;switch(t){case 0:return"确认取消订单吗？";case 1:return"确认删除订单吗?";case 2:return"确认收货吗?"}}}};e.default=o},1377:function(t,e,n){"use strict";n("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=n("b08d"),r={name:"float-tab",data:function(){return{showMore:!1,top:0}},mounted:function(){var t=this;(0,i.getRect)(".tab-img",!1,this).then((function(e){t.height=e.height,console.log(t.height)}))},methods:{onChange:function(){this.showMore=!this.showMore}},watch:{showMore:function(t){this.top=t?-this.height:0}}};e.default=r},1522:function(t,e,n){"use strict";n.d(e,"b",(function(){return r})),n.d(e,"c",(function(){return a})),n.d(e,"a",(function(){return i}));var i={uIcon:n("90f3").default},r=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{staticClass:"u-image",style:[t.wrapStyle,t.backgroundStyle],on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onClick.apply(void 0,arguments)}}},[t.isError?t._e():n("v-uni-image",{staticClass:"u-image__image",style:{borderRadius:"circle"==t.shape?"50%":t.$u.addUnit(t.borderRadius)},attrs:{src:t.src,mode:t.mode,"lazy-load":t.lazyLoad},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.onErrorHandler.apply(void 0,arguments)},load:function(e){arguments[0]=e=t.$handleEvent(e),t.onLoadHandler.apply(void 0,arguments)}}}),t.showLoading&&t.loading?n("v-uni-view",{staticClass:"u-image__loading",style:{borderRadius:"circle"==t.shape?"50%":t.$u.addUnit(t.borderRadius),backgroundColor:this.bgColor}},[t.$slots.loading?t._t("loading"):n("u-icon",{attrs:{name:t.loadingIcon,width:t.width,height:t.height}})],2):t._e(),t.showError&&t.isError&&!t.loading?n("v-uni-view",{staticClass:"u-image__error",style:{borderRadius:"circle"==t.shape?"50%":t.$u.addUnit(t.borderRadius)}},[t.$slots.error?t._t("error"):n("u-icon",{attrs:{name:t.errorIcon,width:t.width,height:t.height}})],2):t._e()],1)},a=[]},"1c75":function(t,e,n){"use strict";var i=n("7a54"),r=n.n(i);r.a},2028:function(t,e,n){"use strict";n("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("a9e3");var i={name:"u-modal",props:{value:{type:Boolean,default:!1},zIndex:{type:[Number,String],default:""},title:{type:[String],default:"提示"},width:{type:[Number,String],default:600},content:{type:String,default:"内容"},showTitle:{type:Boolean,default:!0},showConfirmButton:{type:Boolean,default:!0},showCancelButton:{type:Boolean,default:!1},confirmText:{type:String,default:"确认"},cancelText:{type:String,default:"取消"},confirmColor:{type:String,default:"#2979ff"},cancelColor:{type:String,default:"#606266"},borderRadius:{type:[Number,String],default:16},titleStyle:{type:Object,default:function(){return{}}},contentStyle:{type:Object,default:function(){return{}}},cancelStyle:{type:Object,default:function(){return{}}},confirmStyle:{type:Object,default:function(){return{}}},zoom:{type:Boolean,default:!0},asyncClose:{type:Boolean,default:!1},maskCloseAble:{type:Boolean,default:!1},negativeTop:{type:[String,Number],default:0}},data:function(){return{loading:!1}},computed:{cancelBtnStyle:function(){return Object.assign({color:this.cancelColor},this.cancelStyle)},confirmBtnStyle:function(){return Object.assign({color:this.confirmColor},this.confirmStyle)},uZIndex:function(){return this.zIndex?this.zIndex:this.$u.zIndex.popup}},watch:{value:function(t){!0===t&&(this.loading=!1)}},methods:{confirm:function(){this.asyncClose?this.loading=!0:this.$emit("input",!1),this.$emit("confirm")},cancel:function(){var t=this;this.$emit("cancel"),this.$emit("input",!1),setTimeout((function(){t.loading=!1}),300)},popupClose:function(){this.$emit("input",!1)},clearLoading:function(){this.loading=!1}}};e.default=i},2322:function(t,e,n){"use strict";n("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("a9e3");var i={name:"u-image",props:{src:{type:String,default:""},mode:{type:String,default:"aspectFill"},width:{type:[String,Number],default:"100%"},height:{type:[String,Number],default:"auto"},shape:{type:String,default:"square"},borderRadius:{type:[String,Number],default:0},lazyLoad:{type:Boolean,default:!0},showMenuByLongpress:{type:Boolean,default:!0},loadingIcon:{type:String,default:"photo"},errorIcon:{type:String,default:"error-circle"},showLoading:{type:Boolean,default:!0},showError:{type:Boolean,default:!0},fade:{type:Boolean,default:!0},webp:{type:Boolean,default:!1},duration:{type:[String,Number],default:500},bgColor:{type:String,default:"#f3f4f6"}},data:function(){return{isError:!1,loading:!0,opacity:1,durationTime:this.duration,backgroundStyle:{}}},watch:{src:{immediate:!0,handler:function(t){t?this.isError=!1:(this.isError=!0,this.loading=!1)}}},computed:{wrapStyle:function(){var t={};return t.width=this.$u.addUnit(this.width),t.height=this.$u.addUnit(this.height),t.borderRadius="circle"==this.shape?"50%":this.$u.addUnit(this.borderRadius),t.overflow=this.borderRadius>0?"hidden":"visible",this.fade&&(t.opacity=this.opacity,t.transition="opacity ".concat(Number(this.durationTime)/1e3,"s ease-in-out")),t}},methods:{onClick:function(){this.$emit("click")},onErrorHandler:function(t){this.loading=!1,this.isError=!0,this.$emit("error",t)},onLoadHandler:function(){var t=this;if(this.loading=!1,this.isError=!1,this.$emit("load"),!this.fade)return this.removeBgColor();this.opacity=0,this.durationTime=0,setTimeout((function(){t.durationTime=t.duration,t.opacity=1,setTimeout((function(){t.removeBgColor()}),t.durationTime)}),50)},removeBgColor:function(){this.backgroundStyle={backgroundColor:"transparent"}}}};e.default=i},"2ab4":function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return r})),n.d(e,"a",(function(){}));var i=function(){var t=this.$createElement,e=this._self._c||t;return this.show?e("v-uni-view",{staticClass:"u-loading",class:"circle"==this.mode?"u-loading-circle":"u-loading-flower",style:[this.cricleStyle]}):this._e()},r=[]},"328e":function(t,e,n){"use strict";var i=n("7cf9"),r=n.n(i);r.a},"356b":function(t,e,n){"use strict";n("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("a9e3");var i={name:"u-loading",props:{mode:{type:String,default:"circle"},color:{type:String,default:"#c7c7c7"},size:{type:[String,Number],default:"34"},show:{type:Boolean,default:!0}},computed:{cricleStyle:function(){var t={};return t.width=this.size+"rpx",t.height=this.size+"rpx","circle"==this.mode&&(t.borderColor="#e4e4e4 #e4e4e4 #e4e4e4 ".concat(this.color?this.color:"#c7c7c7")),t}}};e.default=i},"35b3":function(t,e,n){var i=n("8a6b");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var r=n("4f06").default;r("4b52402e",i,!0,{sourceMap:!1,shadowMode:!1})},"3c87":function(t,e,n){"use strict";n.d(e,"b",(function(){return r})),n.d(e,"c",(function(){return a})),n.d(e,"a",(function(){return i}));var i={uModal:n("8d42").default},r=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("u-modal",{attrs:{"show-cancel-button":!0,content:t.getTipsText,"confirm-color":"#ff2c3c"},on:{confirm:function(e){arguments[0]=e=t.$handleEvent(e),t.onConfirm.apply(void 0,arguments)}},model:{value:t.show,callback:function(e){t.show=e},expression:"show"}})},a=[]},4316:function(t,e,n){"use strict";n("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("a9e3");var i={props:{i:Number,index:{type:Number,default:function(){return 0}}},data:function(){return{downOption:{auto:!1},upOption:{auto:!1},isInit:!1}},watch:{index:function(t){this.i!==t||this.isInit||(this.isInit=!0,this.mescroll&&this.mescroll.triggerDownScroll())}},methods:{mescrollInitByRef:function(){if(!this.mescroll||!this.mescroll.resetUpScroll){var t=this.$refs.mescrollRef||this.$refs["mescrollRef"+this.i];t&&(this.mescroll=t.mescroll)}},mescrollInit:function(t){this.mescroll=t,this.mescrollInitByRef&&this.mescrollInitByRef(),this.i===this.index&&(this.isInit=!0,this.mescroll.triggerDownScroll())}}},r=i;e.default=r},"45ee":function(t,e,n){var i=n("24fb");e=i(!1),e.push([t.i,".price-format[data-v-0a5a34e0]{font-family:Avenir,SourceHanSansCN,PingFang SC,Arial,Hiragino Sans GB,Microsoft YaHei,sans-serif}",""]),t.exports=e},"4aaa":function(t,e,n){"use strict";n.d(e,"b",(function(){return r})),n.d(e,"c",(function(){return a})),n.d(e,"a",(function(){return i}));var i={mescrollUni:n("0bbb").default,uImage:n("ba4b").default,priceFormat:n("fefe").default,orderDialog:n("a7f3").default},r=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("mescroll-uni",{ref:"mescrollRef",attrs:{top:"80rpx",down:t.downOption,up:t.upOption},on:{init:function(e){arguments[0]=e=t.$handleEvent(e),t.mescrollInit.apply(void 0,arguments)},down:function(e){arguments[0]=e=t.$handleEvent(e),t.downCallback.apply(void 0,arguments)},up:function(e){arguments[0]=e=t.$handleEvent(e),t.upCallback.apply(void 0,arguments)}}},[n("v-uni-view",{staticClass:"order-list"},t._l(t.orderList,(function(e,i){return n("v-uni-view",{key:i,staticClass:"order-item bg-white m-t-20"},[n("router-link",{attrs:{to:{path:"/bundle/pages/exchange_order_details/exchange_order_details",query:{id:e.id}}}},[n("v-uni-view",{staticClass:"order-header flex row-between"},[n("v-uni-view",{staticClass:"flex"},[n("v-uni-view",{staticClass:"m-r-10"},[t._v(t._s(e.create_time))])],1),n("v-uni-view",{staticClass:"primary"},[t._v(t._s(e.order_status))])],1),n("v-uni-view",{staticClass:"order-con"},[n("v-uni-view",{staticClass:"order-goods flex"},[n("u-image",{attrs:{src:e.goods.image,"border-radius":"10",width:"160",height:"160"}}),n("v-uni-view",{staticClass:"goods-info flex-1 m-l-20"},[n("v-uni-view",{staticClass:"goods-name line-2"},[t._v(t._s(e.goods.name))]),n("v-uni-view",{staticClass:"flex row-between"},[n("v-uni-view",{staticClass:"goods-price primary m-t-10"},[n("price-format",{attrs:{"show-subscript":!1,"first-size":36,"second-size":24,price:e.goods.need_integral}}),n("v-uni-text",{staticClass:"xs"},[t._v("积分")]),2===e.goods.exchange_way?[n("v-uni-text",[t._v("+")]),n("price-format",{attrs:{"show-subscript":!1,"first-size":36,"second-size":24,price:e.goods.need_money}}),n("v-uni-text",{staticClass:"xs"},[t._v("元")])]:t._e()],2),n("v-uni-view",{staticClass:"lighter"},[t._v("×"+t._s(e.total_num))])],1)],1)],1),n("v-uni-view",{staticClass:"all-price flex row-right"},[n("v-uni-text",{staticClass:"muted xs"},[t._v("共"+t._s(e.total_num)+"件商品，实付款：")]),n("v-uni-view",{},[n("price-format",{attrs:{"show-subscript":!1,"first-size":36,"second-size":24,price:e.order_integral,weight:500}}),n("v-uni-text",{staticClass:"xs"},[t._v("积分")]),e.order_amount>0?[n("v-uni-text",[t._v("+")]),n("price-format",{attrs:{"show-subscript":!1,"first-size":36,"second-size":24,price:e.order_amount,weight:500}}),n("v-uni-text",{staticClass:"xs"},[t._v("元")])]:t._e()],2)],1)],1),e.btns.cancel_btn||e.btns.delivery_btn||e.btns.confirm_btn||e.btns.del_btn||e.btns.pay_btn?n("v-uni-view",{staticClass:"order-footer flex row-right"},[e.btns.cancel_btn?n("v-uni-view",[n("v-uni-button",{staticClass:"plain br60 lighter",attrs:{size:"sm","hover-class":"none"},on:{click:function(n){n.stopPropagation(),arguments[0]=n=t.$handleEvent(n),t.handleOrder(e.id,0)}}},[t._v("取消订单")])],1):t._e(),e.btns.delivery_btn?n("v-uni-view",{on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e)}}},[n("router-link",{attrs:{to:{path:"/bundle/pages/goods_logistics/goods_logistics",query:{id:e.id,type:"integral"}}}},[n("v-uni-button",{staticClass:"btn plain br60 lighter",attrs:{size:"sm","hover-class":"none"}},[t._v("查看物流")])],1)],1):t._e(),e.btns.del_btn?n("v-uni-view",[n("v-uni-button",{staticClass:"btn plain br60 lighter",attrs:{size:"sm","hover-class":"none"},on:{click:function(n){n.stopPropagation(),arguments[0]=n=t.$handleEvent(n),t.handleOrder(e.id,1)}}},[t._v("删除订单")])],1):t._e(),e.btns.pay_btn?n("v-uni-view",{staticClass:"m-l-20"},[n("router-link",{attrs:{to:{path:"/pages/payment/payment",query:{from:"integral",order_id:e.id}}}},[n("v-uni-button",{staticClass:"btn bg-primary br60 white",attrs:{size:"sm"}},[t._v("立即付款")])],1)],1):t._e(),e.btns.confirm_btn?n("v-uni-view",{staticClass:"m-l-20"},[n("v-uni-button",{staticClass:"btn plain br60 primary red",attrs:{size:"sm","hover-class":"none"},on:{click:function(n){n.stopPropagation(),arguments[0]=n=t.$handleEvent(n),t.handleOrder(e.id,2)}}},[t._v("确认收货")])],1):t._e()],1):t._e()],1)],1)})),1),n("order-dialog",{ref:"orderDialog",attrs:{"order-id":t.orderId,type:t.type},on:{confirm:function(e){arguments[0]=e=t.$handleEvent(e),t.confirmDialog.apply(void 0,arguments)}}})],1)},a=[]},"4d65":function(t,e,n){"use strict";n.r(e);var i=n("069f"),r=n.n(i);for(var a in i)["default"].indexOf(a)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(a);e["default"]=r.a},"59f5":function(t,e,n){"use strict";n.r(e);var i=n("2028"),r=n.n(i);for(var a in i)["default"].indexOf(a)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(a);e["default"]=r.a},6021:function(t,e,n){"use strict";var i=n("64b1"),r=n.n(i);r.a},"64b1":function(t,e,n){var i=n("6e35");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var r=n("4f06").default;r("3d1e497c",i,!0,{sourceMap:!1,shadowMode:!1})},"65c2":function(t,e,n){"use strict";n.r(e);var i=n("ad6f"),r=n("7c75");for(var a in r)["default"].indexOf(a)<0&&function(t){n.d(e,t,(function(){return r[t]}))}(a);n("9415");var o=n("f0c5"),s=Object(o["a"])(r["default"],i["b"],i["c"],!1,null,"130bc95c",null,!1,i["a"],void 0);e["default"]=s.exports},"683f":function(t,e,n){"use strict";n.r(e);var i=n("01f3"),r=n.n(i);for(var a in i)["default"].indexOf(a)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(a);e["default"]=r.a},"6d79":function(t,e,n){"use strict";n("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("a9e3"),n("acd8");var i={data:function(){return{priceSlice:{}}},components:{},props:{firstSize:{type:[String,Number],default:28},secondSize:{type:[String,Number],default:28},color:{type:String},weight:{type:[String,Number],default:400},price:{type:[String,Number],default:""},showSubscript:{type:Boolean,default:!0},subscriptSize:{type:[String,Number],default:28},lineThrough:{type:Boolean,default:!1}},created:function(){this.priceFormat()},watch:{price:function(t){this.priceFormat()}},methods:{priceFormat:function(){var t=this.price,e={};null!==t&&""!==t&&void 0!==t?(t=parseFloat(t),t=String(t).split("."),e.first=t[0],e.second=t[1],this.priceSlice=e):this.priceSlice={first:0}}}};e.default=i},"6e35":function(t,e,n){var i=n("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.u-image[data-v-1bf07c9a]{position:relative;transition:opacity .5s ease-in-out}.u-image__image[data-v-1bf07c9a]{width:100%;height:100%}.u-image__loading[data-v-1bf07c9a], .u-image__error[data-v-1bf07c9a]{position:absolute;top:0;left:0;width:100%;height:100%;display:flex;flex-direction:row;align-items:center;justify-content:center;background-color:#f3f4f6;color:#909399;font-size:%?46?%}',""]),t.exports=e},7247:function(t,e,n){"use strict";n.d(e,"b",(function(){return r})),n.d(e,"c",(function(){return a})),n.d(e,"a",(function(){return i}));var i={uPopup:n("5cc5").default,uLoading:n("8fc0").default},r=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",[n("u-popup",{attrs:{zoom:t.zoom,mode:"center",popup:!1,"z-index":t.uZIndex,length:t.width,"mask-close-able":t.maskCloseAble,"border-radius":t.borderRadius,"negative-top":t.negativeTop},on:{close:function(e){arguments[0]=e=t.$handleEvent(e),t.popupClose.apply(void 0,arguments)}},model:{value:t.value,callback:function(e){t.value=e},expression:"value"}},[n("v-uni-view",{staticClass:"u-model"},[t.showTitle?n("v-uni-view",{staticClass:"u-model__title u-line-1",style:[t.titleStyle]},[t._v(t._s(t.title))]):t._e(),n("v-uni-view",{staticClass:"u-model__content"},[t.$slots.default||t.$slots.$default?n("v-uni-view",{style:[t.contentStyle]},[t._t("default")],2):n("v-uni-view",{staticClass:"u-model__content__message",style:[t.contentStyle]},[t._v(t._s(t.content))])],1),t.showCancelButton||t.showConfirmButton?n("v-uni-view",{staticClass:"u-model__footer u-border-top"},[t.showCancelButton?n("v-uni-view",{staticClass:"u-model__footer__button",style:[t.cancelBtnStyle],attrs:{"hover-stay-time":100,"hover-class":"u-model__btn--hover"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.cancel.apply(void 0,arguments)}}},[t._v(t._s(t.cancelText))]):t._e(),t.showConfirmButton||t.$slots["confirm-button"]?n("v-uni-view",{staticClass:"u-model__footer__button hairline-left",style:[t.confirmBtnStyle],attrs:{"hover-stay-time":100,"hover-class":t.asyncClose?"none":"u-model__btn--hover"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.confirm.apply(void 0,arguments)}}},[t.$slots["confirm-button"]?t._t("confirm-button"):[t.loading?n("u-loading",{attrs:{mode:"circle",color:t.confirmColor}}):[t._v(t._s(t.confirmText))]]],2):t._e()],1):t._e()],1)],1)],1)},a=[]},"7a54":function(t,e,n){var i=n("aa36");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var r=n("4f06").default;r("4f941e16",i,!0,{sourceMap:!1,shadowMode:!1})},"7c75":function(t,e,n){"use strict";n.r(e);var i=n("1377"),r=n.n(i);for(var a in i)["default"].indexOf(a)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(a);e["default"]=r.a},"7cf9":function(t,e,n){var i=n("ed20");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var r=n("4f06").default;r("4cdb7898",i,!0,{sourceMap:!1,shadowMode:!1})},"80a3":function(t,e,n){"use strict";n.r(e);var i=n("356b"),r=n.n(i);for(var a in i)["default"].indexOf(a)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(a);e["default"]=r.a},"8a6b":function(t,e,n){var i=n("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.u-model[data-v-24dbca0a]{height:auto;overflow:hidden;font-size:%?32?%;background-color:#fff}.u-model__btn--hover[data-v-24dbca0a]{background-color:#e6e6e6}.u-model__title[data-v-24dbca0a]{padding-top:%?48?%;font-weight:500;text-align:center;color:#303133}.u-model__content__message[data-v-24dbca0a]{padding:%?48?%;font-size:%?30?%;text-align:center;color:#606266}.u-model__footer[data-v-24dbca0a]{display:flex;flex-direction:row}.u-model__footer__button[data-v-24dbca0a]{flex:1;height:%?100?%;line-height:%?100?%;font-size:%?32?%;box-sizing:border-box;cursor:pointer;text-align:center;border-radius:%?4?%}',""]),t.exports=e},"8aee":function(t,e,n){var i=n("b9d8");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var r=n("4f06").default;r("901755d2",i,!0,{sourceMap:!1,shadowMode:!1})},"8d42":function(t,e,n){"use strict";n.r(e);var i=n("7247"),r=n("59f5");for(var a in r)["default"].indexOf(a)<0&&function(t){n.d(e,t,(function(){return r[t]}))}(a);n("b79d");var o=n("f0c5"),s=Object(o["a"])(r["default"],i["b"],i["c"],!1,null,"24dbca0a",null,!1,i["a"],void 0);e["default"]=s.exports},"8fc0":function(t,e,n){"use strict";n.r(e);var i=n("2ab4"),r=n("80a3");for(var a in r)["default"].indexOf(a)<0&&function(t){n.d(e,t,(function(){return r[t]}))}(a);n("1c75");var o=n("f0c5"),s=Object(o["a"])(r["default"],i["b"],i["c"],!1,null,"bf7076f2",null,!1,i["a"],void 0);e["default"]=s.exports},9415:function(t,e,n){"use strict";var i=n("8aee"),r=n.n(i);r.a},"97ec":function(t,e,n){"use strict";n.d(e,"b",(function(){return r})),n.d(e,"c",(function(){return a})),n.d(e,"a",(function(){return i}));var i={tabs:n("741a").default,tab:n("5652").default,exchangeOrderList:n("aa0f").default,floatTab:n("65c2").default},r=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{staticClass:"user-order"},[n("tabs",{attrs:{current:t.active,"bar-width":"60","is-scroll":!0},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.changeShow.apply(void 0,arguments)}}},t._l(t.order,(function(e,i){return n("tab",{key:i,attrs:{name:e.name}},[n("exchange-order-list",{attrs:{"order-type":e.type,i:i,index:t.active}})],1)})),1),n("float-tab")],1)},a=[]},"9eac":function(t,e,n){"use strict";n.r(e);var i=n("97ec"),r=n("dd42");for(var a in r)["default"].indexOf(a)<0&&function(t){n.d(e,t,(function(){return r[t]}))}(a);var o=n("f0c5"),s=Object(o["a"])(r["default"],i["b"],i["c"],!1,null,"20e9ec91",null,!1,i["a"],void 0);e["default"]=s.exports},a7f3:function(t,e,n){"use strict";n.r(e);var i=n("3c87"),r=n("4d65");for(var a in r)["default"].indexOf(a)<0&&function(t){n.d(e,t,(function(){return r[t]}))}(a);var o=n("f0c5"),s=Object(o["a"])(r["default"],i["b"],i["c"],!1,null,"15d7f11b",null,!1,i["a"],void 0);e["default"]=s.exports},aa0f:function(t,e,n){"use strict";n.r(e);var i=n("4aaa"),r=n("683f");for(var a in r)["default"].indexOf(a)<0&&function(t){n.d(e,t,(function(){return r[t]}))}(a);n("328e");var o=n("f0c5"),s=Object(o["a"])(r["default"],i["b"],i["c"],!1,null,"bf36f148",null,!1,i["a"],void 0);e["default"]=s.exports},aa36:function(t,e,n){var i=n("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.u-loading-circle[data-v-bf7076f2]{display:inline-flex;vertical-align:middle;width:%?28?%;height:%?28?%;background:0 0;border-radius:50%;border:2px solid;border-color:#e5e5e5 #e5e5e5 #e5e5e5 #8f8d8e;-webkit-animation:u-circle-data-v-bf7076f2 1s linear infinite;animation:u-circle-data-v-bf7076f2 1s linear infinite}.u-loading-flower[data-v-bf7076f2]{width:20px;height:20px;display:inline-block;vertical-align:middle;-webkit-animation:a 1s steps(12) infinite;animation:u-flower-data-v-bf7076f2 1s steps(12) infinite;background:transparent url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMjAiIGhlaWdodD0iMTIwIiB2aWV3Qm94PSIwIDAgMTAwIDEwMCI+PHBhdGggZmlsbD0ibm9uZSIgZD0iTTAgMGgxMDB2MTAwSDB6Ii8+PHJlY3Qgd2lkdGg9IjciIGhlaWdodD0iMjAiIHg9IjQ2LjUiIHk9IjQwIiBmaWxsPSIjRTlFOUU5IiByeD0iNSIgcnk9IjUiIHRyYW5zZm9ybT0idHJhbnNsYXRlKDAgLTMwKSIvPjxyZWN0IHdpZHRoPSI3IiBoZWlnaHQ9IjIwIiB4PSI0Ni41IiB5PSI0MCIgZmlsbD0iIzk4OTY5NyIgcng9IjUiIHJ5PSI1IiB0cmFuc2Zvcm09InJvdGF0ZSgzMCAxMDUuOTggNjUpIi8+PHJlY3Qgd2lkdGg9IjciIGhlaWdodD0iMjAiIHg9IjQ2LjUiIHk9IjQwIiBmaWxsPSIjOUI5OTlBIiByeD0iNSIgcnk9IjUiIHRyYW5zZm9ybT0icm90YXRlKDYwIDc1Ljk4IDY1KSIvPjxyZWN0IHdpZHRoPSI3IiBoZWlnaHQ9IjIwIiB4PSI0Ni41IiB5PSI0MCIgZmlsbD0iI0EzQTFBMiIgcng9IjUiIHJ5PSI1IiB0cmFuc2Zvcm09InJvdGF0ZSg5MCA2NSA2NSkiLz48cmVjdCB3aWR0aD0iNyIgaGVpZ2h0PSIyMCIgeD0iNDYuNSIgeT0iNDAiIGZpbGw9IiNBQkE5QUEiIHJ4PSI1IiByeT0iNSIgdHJhbnNmb3JtPSJyb3RhdGUoMTIwIDU4LjY2IDY1KSIvPjxyZWN0IHdpZHRoPSI3IiBoZWlnaHQ9IjIwIiB4PSI0Ni41IiB5PSI0MCIgZmlsbD0iI0IyQjJCMiIgcng9IjUiIHJ5PSI1IiB0cmFuc2Zvcm09InJvdGF0ZSgxNTAgNTQuMDIgNjUpIi8+PHJlY3Qgd2lkdGg9IjciIGhlaWdodD0iMjAiIHg9IjQ2LjUiIHk9IjQwIiBmaWxsPSIjQkFCOEI5IiByeD0iNSIgcnk9IjUiIHRyYW5zZm9ybT0icm90YXRlKDE4MCA1MCA2NSkiLz48cmVjdCB3aWR0aD0iNyIgaGVpZ2h0PSIyMCIgeD0iNDYuNSIgeT0iNDAiIGZpbGw9IiNDMkMwQzEiIHJ4PSI1IiByeT0iNSIgdHJhbnNmb3JtPSJyb3RhdGUoLTE1MCA0NS45OCA2NSkiLz48cmVjdCB3aWR0aD0iNyIgaGVpZ2h0PSIyMCIgeD0iNDYuNSIgeT0iNDAiIGZpbGw9IiNDQkNCQ0IiIHJ4PSI1IiByeT0iNSIgdHJhbnNmb3JtPSJyb3RhdGUoLTEyMCA0MS4zNCA2NSkiLz48cmVjdCB3aWR0aD0iNyIgaGVpZ2h0PSIyMCIgeD0iNDYuNSIgeT0iNDAiIGZpbGw9IiNEMkQyRDIiIHJ4PSI1IiByeT0iNSIgdHJhbnNmb3JtPSJyb3RhdGUoLTkwIDM1IDY1KSIvPjxyZWN0IHdpZHRoPSI3IiBoZWlnaHQ9IjIwIiB4PSI0Ni41IiB5PSI0MCIgZmlsbD0iI0RBREFEQSIgcng9IjUiIHJ5PSI1IiB0cmFuc2Zvcm09InJvdGF0ZSgtNjAgMjQuMDIgNjUpIi8+PHJlY3Qgd2lkdGg9IjciIGhlaWdodD0iMjAiIHg9IjQ2LjUiIHk9IjQwIiBmaWxsPSIjRTJFMkUyIiByeD0iNSIgcnk9IjUiIHRyYW5zZm9ybT0icm90YXRlKC0zMCAtNS45OCA2NSkiLz48L3N2Zz4=) no-repeat;background-size:100%}@-webkit-keyframes u-flower-data-v-bf7076f2{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}@keyframes u-flower-data-v-bf7076f2{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}@-webkit-keyframes u-circle-data-v-bf7076f2{0%{-webkit-transform:rotate(0);transform:rotate(0)}100%{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}',""]),t.exports=e},ad6f:function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return r})),n.d(e,"a",(function(){}));var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{staticClass:"float-tab ~column"},[n("v-uni-navigator",{staticClass:"tab-img",style:{top:3*t.top+"px"},attrs:{"hover-class":"none","open-type":"switchTab",url:"/pages/index/index"}},[n("v-uni-image",{staticClass:"tab-icon",attrs:{src:"/static/images/icon_float_home.png"}})],1),n("v-uni-navigator",{staticClass:"tab-img",style:{top:2*t.top+"px"},attrs:{"hover-class":"none","open-type":"navigate",url:"/bundle/pages/chat/chat"}},[n("v-uni-image",{staticClass:"tab-icon",attrs:{src:"/static/images/icon_float_help.png"}})],1),n("v-uni-navigator",{staticClass:"tab-img",style:{top:t.top+"px"},attrs:{"hover-class":"none","open-type":"switchTab",url:"/pages/shop_cart/shop_cart"}},[n("v-uni-image",{staticClass:"tab-icon",attrs:{src:"/static/images/icon_float_cart.png"}})],1),n("v-uni-image",{staticClass:"tab-img",staticStyle:{"z-index":"99"},style:{transform:"rotateZ("+(t.showMore?135:0)+"deg)"},attrs:{src:"/static/images/icon_float_more.png"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onChange.apply(void 0,arguments)}}})],1)},r=[]},af8d:function(t,e,n){"use strict";n.r(e);var i=n("2322"),r=n.n(i);for(var a in i)["default"].indexOf(a)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(a);e["default"]=r.a},b79d:function(t,e,n){"use strict";var i=n("35b3"),r=n.n(i);r.a},b9d8:function(t,e,n){var i=n("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.float-tab[data-v-130bc95c]{position:fixed;right:%?16?%;bottom:%?200?%;width:%?96?%;height:%?96?%;z-index:777}.float-tab .tab-img[data-v-130bc95c]{width:100%;height:100%;position:absolute;transition:all .5s}.float-tab .tab-img .tab-icon[data-v-130bc95c]{width:100%;height:100%}',""]),t.exports=e},ba4b:function(t,e,n){"use strict";n.r(e);var i=n("1522"),r=n("af8d");for(var a in r)["default"].indexOf(a)<0&&function(t){n.d(e,t,(function(){return r[t]}))}(a);n("6021");var o=n("f0c5"),s=Object(o["a"])(r["default"],i["b"],i["c"],!1,null,"1bf07c9a",null,!1,i["a"],void 0);e["default"]=s.exports},bd6f:function(t,e,n){"use strict";n.r(e);var i=n("6d79"),r=n.n(i);for(var a in i)["default"].indexOf(a)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(a);e["default"]=r.a},beb1:function(t,e,n){"use strict";n("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("c740");e.default={data:function(){return{active:-1,order:[{name:"全部",type:""},{name:"待付款",type:0},{name:"待发货",type:1},{name:"待收货",type:2},{name:"已完成",type:3},{name:"已关闭",type:4}]}},methods:{changeShow:function(t){var e=this;-1!=t&&this.$nextTick((function(){e.active=t}))}},onLoad:function(t){var e=this.order,n=this.$Route.query.type||"",i=e.findIndex((function(t){return t.type==n}));this.changeShow(i)}}},c495:function(t,e,n){var i=n("45ee");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var r=n("4f06").default;r("54b253da",i,!0,{sourceMap:!1,shadowMode:!1})},c60f:function(t,e,n){"use strict";n("7a82");var i=n("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.cancelIntegralOrder=function(t){return r.default.post("integral_order/cancel",{id:t})},e.closeBargainOrder=function(t){return r.default.get("bargain/closeBargain",{params:t})},e.confirmIntegralOrder=function(t){return r.default.post("integral_order/confirm",{id:t})},e.delIntegralOrder=function(t){return r.default.post("integral_order/del",{id:t})},e.getActivityGoodsLists=function(t){return r.default.get("activity_area/activityGoodsList",{params:t})},e.getBargainActivityDetail=function(t){return r.default.get("bargain/bargainDetail",{params:t})},e.getBargainActivityList=function(t){return r.default.get("bargain/orderList",{params:t})},e.getBargainDetail=function(t){return r.default.get("bargain/detail",{params:t})},e.getBargainList=function(t){return r.default.get("bargain/lists",{params:t})},e.getBargainNumber=function(){return r.default.get("bargain/barginNumber")},e.getBargainPost=function(t){return r.default.get("share/shareBargain",{params:t})},e.getCoupon=function(t){return r.default.post("coupon/getCoupon",{coupon_id:t})},e.getCouponList=function(t){return r.default.get("coupon/getCouponList",{params:t})},e.getGroupList=function(t){return r.default.get("team/activity",{params:t})},e.getIntegralGoods=function(t){return r.default.get("integral_goods/lists",{params:t})},e.getIntegralGoodsDetail=function(t){return r.default.get("integral_goods/detail",{params:t})},e.getIntegralOrder=function(t){return r.default.get("integral_order/lists",{params:t})},e.getIntegralOrderDetail=function(t){return r.default.get("integral_order/detail",{params:{id:t}})},e.getIntegralOrderTraces=function(t){return r.default.get("integral_order/orderTraces",{params:{id:t}})},e.getMyCoupon=function(t){return r.default.get("coupon/myCouponList",{params:t})},e.getOrderCoupon=function(t){return r.default.post("coupon/getBuyCouponList",t)},e.getSeckillGoods=function(t){return r.default.get("seckill_goods/getSeckillGoods",{params:t})},e.getSeckillTime=function(){return r.default.get("seckill_goods/getSeckillTime")},e.getSignLists=function(){return r.default.get("sign/lists")},e.getSignRule=function(){return r.default.get("sign/rule")},e.getTeamInfo=function(t){return r.default.get("team/teamInfo",{params:t})},e.getUserGroup=function(t){return r.default.get("team/record",{params:t})},e.helpBargain=function(t){return r.default.post("bargain/knife",t)},e.integralSettlement=function(t){return r.default.get("integral_order/settlement",{params:t})},e.integralSubmitOrder=function(t){return r.default.post("integral_order/submitOrder",t)},e.launchBargain=function(t){return r.default.post("bargain/sponsor",t)},e.teamBuy=function(t){return r.default.post("team/buy",t)},e.teamCheck=function(t){return r.default.post("team/check",t)},e.teamKaiTuan=function(t){return r.default.post("team/kaituan",t)},e.userSignIn=function(){return r.default.get("sign/sign")};var r=i(n("3b33"));n("b08d")},d5b0:function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return r})),n.d(e,"a",(function(){}));var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-text",{class:(t.lineThrough?"line-through":"")+" price-format",style:{color:t.color,"font-weight":t.weight}},[t.showSubscript?n("v-uni-text",{style:{"font-size":t.subscriptSize+"rpx","margin-right":"2rpx"}},[t._v("¥")]):t._e(),n("v-uni-text",{style:{"font-size":t.firstSize+"rpx","margin-right":"1rpx"}},[t._v(t._s(t.priceSlice.first))]),t.priceSlice.second?n("v-uni-text",{style:{"font-size":t.secondSize+"rpx"}},[t._v("."+t._s(t.priceSlice.second))]):t._e()],1)},r=[]},dd42:function(t,e,n){"use strict";n.r(e);var i=n("beb1"),r=n.n(i);for(var a in i)["default"].indexOf(a)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(a);e["default"]=r.a},ed20:function(t,e,n){var i=n("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.order-list[data-v-bf36f148]{padding:0 %?20?%;overflow:hidden}.order-list .order-item[data-v-bf36f148]{border-radius:%?10?%}.order-list .order-item .order-header[data-v-bf36f148]{height:%?80?%;padding:0 %?24?%;border-bottom:1px dotted #e5e5e5}.order-list .order-item .order-goods[data-v-bf36f148]{padding:%?30?% %?24?%}.order-list .order-item .order-goods .goods-name[data-v-bf36f148]{line-height:%?40?%;height:%?80?%}.order-list .order-item .all-price[data-v-bf36f148]{text-align:right;padding:0 %?24?% %?20?%}.order-list .order-item .order-footer[data-v-bf36f148]{height:%?100?%;border-top:1px solid #e5e5e5;padding:0 %?24?%}.order-list .order-item .order-footer .plain[data-v-bf36f148]{border:1px solid #bbb}.order-list .order-item .order-footer .plain.red[data-v-bf36f148]{border-color:#ff2c3c}',""]),t.exports=e},ee17:function(t,e,n){"use strict";var i=n("c495"),r=n.n(i);r.a},fefe:function(t,e,n){"use strict";n.r(e);var i=n("d5b0"),r=n("bd6f");for(var a in r)["default"].indexOf(a)<0&&function(t){n.d(e,t,(function(){return r[t]}))}(a);n("ee17");var o=n("f0c5"),s=Object(o["a"])(r["default"],i["b"],i["c"],!1,null,"0a5a34e0",null,!1,i["a"],void 0);e["default"]=s.exports}}]);