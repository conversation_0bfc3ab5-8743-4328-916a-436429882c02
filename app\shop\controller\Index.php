<?php



namespace app\shop\controller;


use app\common\basics\ShopBase;
use app\common\model\shop\ShopRole;
use app\common\server\ConfigServer;
use app\common\server\JsonServer;
use app\shop\logic\index\StatLogic;
use app\shopapi\logic\ShopLogic;
use app\shop\server\MenuServer;
use think\facade\Config;

class Index extends ShopBase
{
    /**
     * 后台前端全局界面
     * @return mixed
     */
    public function index()
    {
        // 获取当前商家等级
        $shop = \app\common\model\shop\Shop::find($this->shop_id);
        $tierLevel = $shop ? $shop->tier_level : 0;

        return view('', [
            'config' => [
                'name' => ConfigServer::get('website', 'name'),
                'web_favicon' => ConfigServer::get('website', 'web_favicon'),
                'backstage_logo' => ConfigServer::get('website_shop', 'shop_admin_logo'),//主页左上角logo
            ],
            'menu' => MenuServer::getMenuTree($this->shop['role_id'], $tierLevel), // 菜单渲染，传递商家等级
            'view_app_trace' => Config::get('app.app_trace'), // 开启右上角前端示例
            'admin_name' => $this->shop['name'],//管理员名称
            'shop_name' => $this->shop_name,//门店名称
            'shop_logo' => $shop['logo'],//门店名称
            'role_name' => (new ShopRole())->getRoleName($this->shop['role_id']), // 角色名称
        ]);
    }

    /**
     * 工作台
     * @return mixed
     */
    public function stat()
    {
        if($this->request->isAjax()){
            return JsonServer::success('', StatLogic::graphData($this->shop_id));
        }

        // 获取当前商家信息
        $shop = \app\common\model\shop\Shop::find($this->shop_id);
        $tierLevel = $shop ? $shop->tier_level : 0;

        // 根据商家等级显示不同的banner
        $bannerConfig = [];
        if ($tierLevel == 0) { // 0元入驻
            $bannerConfig = [
                'show_banner' => true,
                'banner_image' => ConfigServer::get('pc', 'vip_banner', ''),
                'banner_title' => '升级为商家会员',
                'banner_desc' => '享受更多营销工具和数据分析功能',
                'target_tier' => 1
            ];
        } elseif ($tierLevel == 1) { // 商家会员
            $bannerConfig = [
                'show_banner' => true,
                'banner_image' => ConfigServer::get('pc', 'svip_banner', ''),
                'banner_title' => '升级为实力厂商',
                'banner_desc' => '享受最高等级服务，包含验厂认证',
                'target_tier' => 2
            ];
        } else { // 实力厂商，不显示banner
            $bannerConfig = [
                'show_banner' => false
            ];
        }

        $incomplete_items = (new ShopLogic())->checkProfileCompletion($this->shop_id);

        return view('', [
            'res' => StatLogic::stat($this->shop_id),
            'banner_config' => $bannerConfig,
            'current_tier' => $tierLevel,
            'incomplete_items' => $incomplete_items,

        ]);
    }
    
    /**
     * 工作台商品数据
     * @return mixed
     */
    public function shop()
    {
        if ($this->request->isAjax()) {
            $get = $this->request->get();
            return JsonServer::success('', StatLogic::goodsLists($get,$this->shop_id));
        }
    }

    /**
     * 创建商家等级升级支付订单
     * @return mixed
     */
    public function createTierUpgradeOrder()
    {
        if ($this->request->isAjax()) {
            $post = $this->request->post();
            $targetTier = intval($post['target_tier'] ?? 0);

            if (!in_array($targetTier, [1, 2])) {
                return JsonServer::error('无效的目标等级');
            }

            // 获取当前商家信息
            $shop = \app\common\model\shop\Shop::find($this->shop_id);
            if (!$shop) {
                return JsonServer::error('商家信息不存在');
            }

            if ($shop->tier_level >= $targetTier) {
                return JsonServer::error('目标等级不能低于或等于当前等级');
            }

            // 使用ShopTierLogic创建升级订单
            $result = \app\common\logic\ShopTierLogic::createUpgradeOrder($this->shop_id, $targetTier, $this->admin_id);
            if ($result === false) {
                return JsonServer::error(\app\common\logic\ShopTierLogic::getError());
            }

            return JsonServer::success('订单创建成功', $result);
        }

        return JsonServer::error('请求方式错误');
    }

    /**
     * 发起商家等级升级支付
     * @return mixed
     */
    public function payTierUpgrade()
    {
        if ($this->request->isAjax()) {
            $post = $this->request->post();
            $orderSn = $post['order_sn'] ?? '';
            $payWay = intval($post['pay_way'] ?? 1); // 默认微信支付

            if (empty($orderSn)) {
                return JsonServer::error('订单号不能为空');
            }

            // 查找订单
            $order = \app\common\model\shop\ShopMerchantfees::where('order_sn', $orderSn)->find();
            if (!$order) {
                return JsonServer::error('订单不存在');
            }

            if ($order->status != 0) {
                return JsonServer::error('订单状态异常');
            }

            // 调用支付逻辑
            try {
                $payResult = \app\api\logic\PayLogic::wechatPay($orderSn, 'ruzhucharge', 5); // 5表示PC端
                return $payResult;
            } catch (\Exception $e) {
                return JsonServer::error('支付请求失败：' . $e->getMessage());
            }
        }

        return JsonServer::error('请求方式错误');
    }

    /**
     * 检查支付状态
     * @return mixed
     */
    public function checkPaymentStatus()
    {
        if ($this->request->isAjax()) {
            $post = $this->request->post();
            $orderSn = $post['order_sn'] ?? '';

            if (empty($orderSn)) {
                return JsonServer::error('订单号不能为空');
            }

            // 查找订单
            $order = \app\common\model\shop\ShopMerchantfees::where('order_sn', $orderSn)->find();
            if (!$order) {
                return JsonServer::error('订单不存在');
            }

            return JsonServer::success('查询成功', [
                'status' => $order->status,
                'order_sn' => $order->order_sn,
                'amount' => $order->amount
            ]);
        }

        return JsonServer::error('请求方式错误');
    }
}
