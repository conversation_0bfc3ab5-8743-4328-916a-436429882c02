{layout name="layout1" /}

<div class="wrapper">
    <div class="layui-card">
        <div class="layui-card-body">
            <!--添加按钮-->
            <div style="padding-bottom: 10px;">
                <button class="layui-btn layui-btn-sm layuiadmin-btn-admin {$view_theme_color}" data-type="add">
                    <i class="layui-icon layui-icon-add-1"></i>添加套餐
                </button>
            </div>

            <!--表格-->
            <table id="package-lists" lay-filter="package-lists"></table>

            <!--js模板-->
            <script type="text/html" id="status-tpl">
                <input type="checkbox" name="status" value="{{d.id}}" lay-skin="switch" lay-text="启用|禁用" lay-filter="status-switch" {{ d.status == 1 ? 'checked' : '' }}>
            </script>
            
            <script type="text/html" id="package-operation">
                <a class="layui-btn layui-btn-xs" lay-event="records">
                    <i class="layui-icon layui-icon-log"></i>购买记录
                </a>
                <a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="edit">
                    <i class="layui-icon layui-icon-edit"></i>编辑
                </a>
                <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">
                    <i class="layui-icon layui-icon-delete"></i>删除
                </a>
            </script>
        </div>
    </div>
</div>
<script>
    layui.use(['table', 'form'], function(){
        var form = layui.form
            ,table = layui.table;

        // 初始化表格
        like.tableLists('#package-lists', '{:url("index")}', [
            {field: 'id', width: 80, title: 'ID', sort: true, align: 'center'},
            {field: 'sort', width: 80, title: '排序', sort: true, align: 'center', edit: 'text'},
            {field: 'name', minWidth: 150, title: '套餐名称'},
            {field: 'purchaser_count', width: 120, title: '分配人数', align: 'center'},
            {field: 'price', width: 120, title: '价格(元)', align: 'center'},
            {field: 'status', width: 100, title: '状态', align: 'center', templet: '#status-tpl'},
            {field: 'create_time', width: 160, title: '创建时间', align: 'center'},
            {title: '操作', minWidth: 220, align: 'center', fixed: 'right', toolbar: '#package-operation'}
        ]);

        // 监听状态开关
        form.on('switch(status-switch)', function(obj){
            var id = obj.value;
            var status = obj.elem.checked ? 1 : 0;
            like.ajax({
                url: '{:url("status")}',
                data: {id: id, status: status},
                type: 'post',
                success: function(res) {
                    if(res.code != 1) {
                        layer.msg(res.msg);
                        // 状态反转
                        $(obj.elem).prop('checked', !obj.elem.checked);
                        form.render('checkbox');
                    }
                }
            });
        });
        
        // 监听单元格编辑
        table.on('edit(package-lists)', function(obj){
            var value = obj.value,
                data = obj.data,
                field = obj.field;
            like.ajax({
                url: '{:url("edit")}',
                data: {id: data.id, [field]: value},
                type: 'post'
            });
        });

        //监听工具条
        table.on('tool(package-lists)', function(obj){
            var event = obj.event;
            var data = obj.data;
            
            if(event === 'del'){
                layer.confirm('确定删除此套餐？', function(index){
                    like.ajax({
                        url:'{:url("del")}',
                        data:{id: data.id},
                        type:"post",
                        success:function(res) {
                            if(res.code == 1) {
                                obj.del();
                                layer.close(index);
                            }
                        }
                    });
                });
            } else if(event === 'edit'){
                layer.open({
                    type: 2,
                    title: '编辑套餐',
                    content: '{:url("edit")}?id=' + data.id,
                    area: ['600px', '500px'],
                    btn: ['确定', '取消'],
                    yes: function(index, layero){
                        var iframeWindow = window['layui-layer-iframe'+ index]
                            ,submitID = 'editSubmit'
                            ,submit = layero.find('iframe').contents().find('#'+ submitID);

                        iframeWindow.layui.form.on('submit('+ submitID +')', function(data){
                            var field = data.field;
                            like.ajax({
                                url:'{:url("edit")}',
                                data:field,
                                type:"post",
                                success:function(res) {
                                    if(res.code == 1) {
                                        layer.close(index);
                                        table.reload('package-lists');
                                    }
                                }
                            });
                        });
                        submit.trigger('click');
                    }
                })
            } else if (event === 'records') {
                layer.open({
                    type: 2,
                    title: '【' + data.name + '】购买记录',
                    content: '{:url("records")}?package_id=' + data.id,
                    area: ['90%', '90%'], // 可以调整弹窗大小
                    maxmin: true // 允许最大化最小化
                });
            }
        });

        //事件
        var active = {
            add: function(){
                layer.open({
                    type: 2,
                    title: '添加套餐',
                    content: '{:url("add")}',
                    area: ['600px', '500px'],
                    btn: ['确定', '取消'],
                    yes: function(index, layero){
                        var iframeWindow = window['layui-layer-iframe'+ index]
                            ,submitID = 'addSubmit'
                            ,submit = layero.find('iframe').contents().find('#'+ submitID);
                        
                        iframeWindow.layui.form.on('submit('+ submitID +')', function(data){
                            var field = data.field;
                            like.ajax({
                               url:'{:url("add")}',
                               data:field,
                               type:"post",
                               success:function(res) {
                                   if(res.code == 1) {
                                       layer.close(index);
                                       table.reload('package-lists');
                                   }
                               }
                            });
                        });
                        submit.trigger('click');
                    }
                });
            }
        };
        $('.layui-btn.layuiadmin-btn-admin').on('click', function(){
            var type = $(this).data('type');
            active[type] ? active[type].call(this) : '';
        });
    });
</script>
