<?php


namespace app\shop\controller\printer;

use app\common\basics\ShopBase;
use app\shop\logic\printer\ConfigLogic;
use app\shop\validate\printer\ConfigValidate;
use app\common\server\JsonServer;

/**
 * 打印设置控制器
 * Class Config
 * @package app\admin\controller\printer
 */
class Config extends ShopBase
{

    /**
     * @notes 打印机配置列表
     * @return \think\response\Json|\think\response\View
     * <AUTHOR>
     * @date 2022/1/19 17:09
     */
    public function lists()
    {
        if ($this->request->isAjax()) {
            $result = ConfigLogic::lists($this->shop_id);
            return JsonServer::success('',  $result);
        }
        return view();
    }

    /**
     * @notes 编辑打印机配置
     * @return \think\response\Json|\think\response\View
     * <AUTHOR>
     * @date 2022/1/19 17:09
     */
    public function edit()
    {
        $id = $this->request->get('id/d');
        if ($this->request->isAjax()) {
            $post = $this->request->post();
            (new ConfigValidate())->goCheck();
            ConfigLogic::editConfig($post, $this->shop_id);
            return JsonServer::success('修改成功');
        }
        return view('', [
            'detail' => ConfigLogic::getDetail($id, $this->shop_id),
        ]);
    }

}