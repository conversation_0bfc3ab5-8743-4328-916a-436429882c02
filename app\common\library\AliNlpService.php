<?php
namespace app\common\library;

use AlibabaCloud\SDK\Alinlp\V20200629\Alinlp;
use AlibabaCloud\SDK\Alinlp\V20200629\Models\GetWsChGeneralRequest;
use AlibabaCloud\SDK\Alinlp\V20200629\Models\GetPosChGeneralRequest;
use AlibabaCloud\SDK\Alinlp\V20200629\Models\GetNerChEcomRequest;
use AlibabaCloud\SDK\Alinlp\V20200629\Models\GetSaChGeneralRequest;
use AlibabaCloud\SDK\Alinlp\V20200629\Models\GetKeywordChEcomRequest;
use AlibabaCloud\SDK\Alinlp\V20200629\Models\GetKeywordChEcomContentRequest;
use AlibabaCloud\SDK\Alinlp\V20200629\Models\GetTsChEcomRequest;
use AlibabaCloud\SDK\Alinlp\V20200629\Models\GetWeChEcomRequest;
use AlibabaCloud\SDK\Alinlp\V20200629\Models\GetWeChGeneralRequest;
use AlibabaCloud\SDK\Alinlp\V20200629\Models\GetEcommerceGoodsReviewRequest;
use AlibabaCloud\SDK\Alinlp\V20200629\Models\GetItemPubCheckRequest;
use AlibabaCloud\SDK\Alinlp\V20200629\Models\GetTitleGenerateRequest;
use AlibabaCloud\SDK\Alinlp\V20200629\Models\GetTitleIntelligentRequest;
use AlibabaCloud\SDK\Alinlp\V20200629\Models\GetEcommerceGoodsClassifyRequest;
use AlibabaCloud\SDK\Alinlp\V20200629\Models\GetEcommerceGoodsAttributeRequest;
use Darabonba\OpenApi\Models\Config;
use think\facade\Cache;
use think\facade\Log;

/**
 * 阿里云NLP服务类
 * 集成阿里云自然语言处理API，特别是电商相关的API
 */
class AliNlpService
{
    /**
     * 阿里云NLP配置
     */
    private $config;

    /**
     * 阿里云NLP客户端
     */
    private $client;

    /**
     * 原始SSL设置
     */
    private $originalVerifyPeer;
    private $originalVerifyHost;

    /**
     * 构造函数
     */
    public function __construct()
    {
        // 保存原始SSL设置
        $this->originalVerifyPeer = ini_get('curl.cainfo');
        $this->originalVerifyHost = ini_get('curl.capath');

        // 临时禁用SSL验证
        ini_set('curl.cainfo', '');
        ini_set('curl.capath', '');

        // 配置阿里云NLP服务
        $this->config = new Config([
            'accessKeyId' => 'LTAI5tPpqw9JoKAZKrY4k14Q',
            'accessKeySecret' => '******************************',
            'regionId' => 'cn-hangzhou'
        ]);

        // 创建客户端
        $this->client = new Alinlp($this->config);
    }

    /**
     * 析构函数，恢复SSL设置
     */
    public function __destruct()
    {
        // 恢复原始SSL设置
        ini_set('curl.cainfo', $this->originalVerifyPeer);
        ini_set('curl.capath', $this->originalVerifyHost);
    }

    /**
     * 中文分词
     * @param string $text 待分词文本
     * @return array 分词结果
     */
    public function segment($text)
    {
        // 检查缓存中是否已有该关键词的分词结果
        $cacheKey = 'alisegment_' . md5($text);
        $cachedResult = Cache::get($cacheKey);
        if ($cachedResult) {
            return $cachedResult;
        }

        // 如果关键词为空，直接返回空数组
        if (empty($text)) {
            return ['words' => []];
        }

        $request = new GetWsChGeneralRequest();
        $request->serviceCode = 'alinlp';
        $request->text = $text;
        $word = [];

        try {
            // 调用阿里云NLP服务进行分词
            $response = $this->client->GetWsChGeneral($request);
            $json_string = json_encode($response->body, JSON_UNESCAPED_UNICODE);
            $data = json_decode($json_string, true);
            $keywords = json_decode($data['data'], true);

            // 处理分词结果
            if (is_array($keywords) && isset($keywords['result'])) {
                foreach ($keywords['result'] as $val) {
                    if (isset($val['word']) && !in_array($val['word'], $word)) {
                        $word[] = $val['word'];
                    }
                }
            }

            $result = ['words' => $word];

            // 缓存分词结果（1小时）
            Cache::set($cacheKey, $result, 3600);

            return $result;
        } catch (\Exception $e) {
            // 记录错误日志
            Log::error('阿里云分词API调用失败: ' . $e->getMessage());

            // 如果分词失败，尝试简单的字符分割
            $simpleWords = [];
            $len = mb_strlen($text, 'UTF-8');
            for ($i = 0; $i < $len; $i++) {
                $char = mb_substr($text, $i, 1, 'UTF-8');
                if (preg_match('/[\x{4e00}-\x{9fa5}]/u', $char)) { // 仅处理中文字符
                    $simpleWords[] = $char;
                }
            }

            // 如果有空格，也按空格分割
            $spaceWords = preg_split('/\s+/', $text);
            if (count($spaceWords) > 1) {
                $simpleWords = array_merge($simpleWords, $spaceWords);
            }

            $result = ['words' => array_unique(array_filter($simpleWords))];

            // 缓存分词结果（1小时）
            Cache::set($cacheKey, $result, 3600);

            return $result;
        }
    }

    /**
     * 命名实体识别-电商
     * @param string $text 待识别文本
     * @return array 识别结果
     */
    public function nerEcom($text)
    {
        // 检查缓存中是否已有该关键词的分词结果
        $cacheKey = 'alinlp_ner_ecom_' . md5($text);
        $cachedResult = Cache::get($cacheKey);
        if ($cachedResult) {
            return $cachedResult;
        }

        // 如果关键词为空，直接返回空数组
        if (empty($text)) {
            return ['words' => []];
        }

        $request = new GetNerChEcomRequest();
        $request->serviceCode = 'alinlp';
        $request->text = $text;
        $word = [];

        try {
            // 调用阿里云NLP服务进行命名实体识别
            $response = $this->client->getNerChEcom($request);
            $json_string = json_encode($response->body, JSON_UNESCAPED_UNICODE);
            $data = json_decode($json_string, true);
            $keywords = json_decode($data['data'], true);

            // 处理识别结果
            if (is_array($keywords) && isset($keywords['result'])) {
                foreach ($keywords['result'] as $val) {
                    if (isset($val['word']) && !in_array($val['word'], $word)) {
                        $word[] = $val['word'];
                    }
                }
            }

            $result = ['words' => $word];

            // 缓存识别结果（1小时）
            Cache::set($cacheKey, $result, 3600);

            return $result;
        } catch (\Exception $e) {
            // 记录错误日志
            Log::error('阿里云命名实体识别API调用失败: ' . $e->getMessage());

            // 如果API调用失败，返回空结果
            $result = ['words' => []];

            // 缓存空结果（10分钟，避免频繁重试失败的API）
            Cache::set($cacheKey, $result, 600);

            return $result;
        }
    }

    /**
     * 商品标题分类
     * @param string $title 商品标题
     * @return array 分类结果
     */
    public function goodsClassify($title)
    {
        // 检查缓存
        $cacheKey = 'alinlp_goods_classify_' . md5($title);
        $cachedResult = Cache::get($cacheKey);
        if ($cachedResult) {
            return $cachedResult;
        }

        if (empty($title)) {
            return ['categories' => []];
        }

        $request = new GetEcommerceGoodsClassifyRequest();
        $request->serviceCode = 'alinlp';
        $request->text = $title;

        try {
            $response = $this->client->getEcommerceGoodsClassify($request);
            $json_string = json_encode($response->body, JSON_UNESCAPED_UNICODE);
            $data = json_decode($json_string, true);
            $result = json_decode($data['data'], true);

            // 缓存结果（1天）
            Cache::set($cacheKey, $result, 86400);

            return $result;
        } catch (\Exception $e) {
            Log::error('阿里云商品标题分类API调用失败: ' . $e->getMessage());
            return ['categories' => []];
        }
    }

    /**
     * 商品属性抽取
     * @param string $title 商品标题
     * @return array 属性结果
     */
    public function goodsAttribute($title)
    {
        // 检查缓存
        $cacheKey = 'alinlp_goods_attribute_' . md5($title);
        $cachedResult = Cache::get($cacheKey);
        if ($cachedResult) {
            return $cachedResult;
        }

        if (empty($title)) {
            return ['attributes' => []];
        }

        $request = new GetEcommerceGoodsAttributeRequest();
        $request->serviceCode = 'alinlp';
        $request->text = $title;

        try {
            $response = $this->client->getEcommerceGoodsAttribute($request);
            $json_string = json_encode($response->body, JSON_UNESCAPED_UNICODE);
            $data = json_decode($json_string, true);
            $result = json_decode($data['data'], true);

            // 缓存结果（1天）
            Cache::set($cacheKey, $result, 86400);

            return $result;
        } catch (\Exception $e) {
            Log::error('阿里云商品属性抽取API调用失败: ' . $e->getMessage());
            return ['attributes' => []];
        }
    }

    /**
     * 词性标注-通用
     * @param string $text 待标注文本
     * @return array 标注结果
     */
    public function posTagging($text)
    {
        $cacheKey = 'alipos_' . md5($text);
        $cachedResult = Cache::get($cacheKey);
        if ($cachedResult) {
            return $cachedResult;
        }

        if (empty($text)) {
            return ['pos' => []];
        }

        $request = new GetPosChGeneralRequest();
        $request->serviceCode = 'alinlp';
        $request->text = $text;

        try {
            $response = $this->client->getPosChGeneral($request);
            $json_string = json_encode($response->body, JSON_UNESCAPED_UNICODE);
            $data = json_decode($json_string, true);
            $result = json_decode($data['data'], true);

            // 缓存结果（1小时）
            Cache::set($cacheKey, $result, 3600);

            return $result;
        } catch (\Exception $e) {
            Log::error('阿里云词性标注API调用失败: ' . $e->getMessage());
            return ['pos' => []];
        }
    }

    /**
     * 情感分析-通用
     * @param string $text 待分析文本
     * @return array 分析结果
     */
    public function sentimentAnalysis($text)
    {
        $cacheKey = 'alisa_' . md5($text);
        $cachedResult = Cache::get($cacheKey);
        if ($cachedResult) {
            return $cachedResult;
        }

        if (empty($text)) {
            return ['sentiment' => 'neutral', 'score' => 0.5];
        }

        $request = new GetSaChGeneralRequest();
        $request->serviceCode = 'alinlp';
        $request->text = $text;

        try {
            $response = $this->client->getSaChGeneral($request);
            $json_string = json_encode($response->body, JSON_UNESCAPED_UNICODE);
            $data = json_decode($json_string, true);
            $result = json_decode($data['data'], true);

            // 缓存结果（1天）
            Cache::set($cacheKey, $result, 86400);

            return $result;
        } catch (\Exception $e) {
            Log::error('阿里云情感分析API调用失败: ' . $e->getMessage());
            return ['sentiment' => 'neutral', 'score' => 0.5];
        }
    }

    /**
     * 中心词提取-中文电商
     * @param string $text 待提取文本
     * @return array 提取结果
     */
    public function keywordExtractEcom($text)
    {
        $cacheKey = 'alikeyword_ecom_' . md5($text);
        $cachedResult = Cache::get($cacheKey);
        if ($cachedResult) {
            return $cachedResult;
        }

        if (empty($text)) {
            return ['keywords' => []];
        }

        $request = new GetKeywordChEcomRequest();
        $request->serviceCode = 'alinlp';
        $request->text = $text;

        try {
            $response = $this->client->getKeywordChEcom($request);
            $json_string = json_encode($response->body, JSON_UNESCAPED_UNICODE);
            $data = json_decode($json_string, true);
            $result = json_decode($data['data'], true);

            // 缓存结果（1天）
            Cache::set($cacheKey, $result, 86400);

            return $result;
        } catch (\Exception $e) {
            Log::error('阿里云中心词提取-中文电商API调用失败: ' . $e->getMessage());
            return ['keywords' => []];
        }
    }

    /**
     * 中心词提取-英文电商
     * @param string $text 待提取文本
     * @return array 提取结果
     */
    public function keywordExtractEcomEn($text)
    {
        $cacheKey = 'alikeyword_ecom_en_' . md5($text);
        $cachedResult = Cache::get($cacheKey);
        if ($cachedResult) {
            return $cachedResult;
        }

        if (empty($text)) {
            return ['keywords' => []];
        }

        $request = new GetKeywordChEcomContentRequest();
        $request->serviceCode = 'alinlp';
        $request->text = $text;

        try {
            $response = $this->client->getKeywordChEcomContent($request);
            $json_string = json_encode($response->body, JSON_UNESCAPED_UNICODE);
            $data = json_decode($json_string, true);
            $result = json_decode($data['data'], true);

            // 缓存结果（1天）
            Cache::set($cacheKey, $result, 86400);

            return $result;
        } catch (\Exception $e) {
            Log::error('阿里云中心词提取-英文电商API调用失败: ' . $e->getMessage());
            return ['keywords' => []];
        }
    }

    /**
     * 智能文本分类-电商
     * @param string $text 待分类文本
     * @return array 分类结果
     */
    public function textClassifyEcom($text)
    {
        $cacheKey = 'alits_ecom_' . md5($text);
        $cachedResult = Cache::get($cacheKey);
        if ($cachedResult) {
            return $cachedResult;
        }

        if (empty($text)) {
            return ['categories' => []];
        }

        $request = new GetTsChEcomRequest();
        $request->serviceCode = 'alinlp';
        $request->text = $text;

        try {
            $response = $this->client->getTsChEcom($request);
            $json_string = json_encode($response->body, JSON_UNESCAPED_UNICODE);
            $data = json_decode($json_string, true);
            $result = json_decode($data['data'], true);

            // 缓存结果（1天）
            Cache::set($cacheKey, $result, 86400);

            return $result;
        } catch (\Exception $e) {
            Log::error('阿里云智能文本分类-电商API调用失败: ' . $e->getMessage());
            return ['categories' => []];
        }
    }

    /**
     * 通用文本相似度-多语言
     * @param string $text1 文本1
     * @param string $text2 文本2
     * @return array 相似度结果
     */
    public function textSimilarity($text1, $text2)
    {
        $cacheKey = 'alisim_' . md5($text1 . '|' . $text2);
        $cachedResult = Cache::get($cacheKey);
        if ($cachedResult) {
            return $cachedResult;
        }

        if (empty($text1) || empty($text2)) {
            return ['similarity' => 0];
        }

        $request = new GetWeChGeneralRequest();
        $request->serviceCode = 'alinlp';
        $request->text1 = $text1;
        $request->text2 = $text2;

        try {
            $response = $this->client->getWeChGeneral($request);
            $json_string = json_encode($response->body, JSON_UNESCAPED_UNICODE);
            $data = json_decode($json_string, true);
            $result = json_decode($data['data'], true);

            // 缓存结果（1天）
            Cache::set($cacheKey, $result, 86400);

            return $result;
        } catch (\Exception $e) {
            Log::error('阿里云通用文本相似度API调用失败: ' . $e->getMessage());
            return ['similarity' => 0];
        }
    }

    /**
     * 电商文本相似度
     * @param string $text1 文本1
     * @param string $text2 文本2
     * @return array 相似度结果
     */
    public function textSimilarityEcom($text1, $text2)
    {
        $cacheKey = 'alisim_ecom_' . md5($text1 . '|' . $text2);
        $cachedResult = Cache::get($cacheKey);
        if ($cachedResult) {
            return $cachedResult;
        }

        if (empty($text1) || empty($text2)) {
            return ['similarity' => 0];
        }

        $request = new GetWeChEcomRequest();
        $request->serviceCode = 'alinlp';
        $request->text1 = $text1;
        $request->text2 = $text2;

        try {
            $response = $this->client->getWeChEcom($request);
            $json_string = json_encode($response->body, JSON_UNESCAPED_UNICODE);
            $data = json_decode($json_string, true);
            $result = json_decode($data['data'], true);

            // 缓存结果（1天）
            Cache::set($cacheKey, $result, 86400);

            return $result;
        } catch (\Exception $e) {
            Log::error('阿里云电商文本相似度API调用失败: ' . $e->getMessage());
            return ['similarity' => 0];
        }
    }
}
