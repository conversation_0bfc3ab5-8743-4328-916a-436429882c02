

{layout name="layout2" /}
<style>
  .layui-form-label {
    color: #6a6f6c;
    width: 140px;
  }
  .layui-input-block{
    margin-left:170px;
  }
  .reqRed::before {
    content: '*';
    color: red;
  }
</style>
<div class="layui-form" style="padding: 20px 30px 0 0;">
  <div class="layui-form-item">
    <label class="layui-form-label reqRed">分类名称：</label>
    <div class="layui-input-inline">
      <input type="text" name="name" lay-verify="required" lay-verType="tips" autocomplete="off" class="layui-input">
    </div>
  </div>
  <div class="layui-form-item">
    <label class="layui-form-label reqRed">上级分类：</label>
    <div class="layui-input-inline">
      <select name="pid" lay-verify="required" placeholder="请选择" lay-filter="search_pid">
        <option value="0" data-level="1">顶级分类</option>
        {foreach $category_list as $item => $val}
        <option value="{$item}" data-level={$val.level}>{$val.name}</option>
        {/foreach}
      </select>
    </div>
  </div>

  <!-- 资质信息展示区域 -->
  <div class="layui-form-item" style="border: 1px solid #e6e6e6; padding: 15px; margin: 20px 0; background-color: #f9f9f9; border-radius: 6px;">
    <label class="layui-form-label" style="color: #666; font-weight: bold;">关联资质：</label>
    <div class="layui-input-block">
      <div style="color: #999; padding: 10px; text-align: center; background: white; border: 1px dashed #ddd; border-radius: 4px;">
        <i class="layui-icon layui-icon-tips" style="font-size: 16px;"></i>
        新增分类暂无关联资质，请在资质管理中进行绑定
      </div>
      <div style="margin-top: 10px; font-size: 12px; color: #999;">
        <i class="layui-icon layui-icon-about"></i>
        提示：分类创建后，可在"资质管理"页面为资质批量绑定分类，操作更高效
      </div>
    </div>
  </div>

  <div class="layui-form-item" id="bg_image">
    <label class="layui-form-label">首页分类背景图：</label>
    <div class="layui-input-inline">
      <div class="like-upload-image">
        <div class="upload-image-elem"><a class="add-upload-image" id="bgimage"> + 添加图片</a></div>
      </div>
    </div>
  </div>
  <div class="layui-form-item">
    <label class="layui-form-label"></label>
    <span style="color: #a3a3a3;font-size: 9px">建议尺寸：宽200像素*高200像素的jpg，jpeg，png图片。顶级类可以设置首页分类背景图</span>
  </div>
  <div class="layui-form-item">
    <label class="layui-form-label">分类图标：</label>
    <div class="layui-input-inline">
      <div class="like-upload-image">
        <div class="upload-image-elem"><a class="add-upload-image" id="image"> + 添加图片</a></div>
      </div>
    </div>
  </div>
  <div class="layui-form-item">
    <label class="layui-form-label"></label>
    <span style="color: #a3a3a3;font-size: 9px">建议尺寸：宽200像素*高200像素的jpg，jpeg，png图片</span>
  </div>
  <div class="layui-form-item">
    <label class="layui-form-label">排序：</label>
    <div class="layui-input-inline">
      <input type="number" name="sort" value="255" class="layui-input">
    </div>
  </div>
  <div class="layui-form-item">
    <label class="layui-form-label"></label>
    <span style="color: #a3a3a3;font-size: 9px">排序值必须为整数；数值越小，越靠前</span>
  </div>
  <div class="layui-form-item">
    <label class="layui-form-label">显示状态：</label>
    <div class="layui-input-inline">
      <input type="radio" name="is_show" value="1" title="显示" checked>
      <input type="radio" name="is_show" value="0" title="不显示">
    </div>
  </div>
  <div class="layui-form-item">
    <label class="layui-form-label">商家可选：</label>
    <div class="layui-input-inline">
      <input type="radio" name="shop_visible" value="1" title="可选" checked>
      <input type="radio" name="shop_visible" value="0" title="不可选">
    </div>
    <span style="color: #a3a3a3;font-size: 9px">控制商家上传商品时是否可选择此分类</span>
  </div>
  <div class="layui-form-item">
    <label class="layui-form-label">API显示：</label>
    <div class="layui-input-inline">
      <input type="radio" name="api_visible" value="1" title="显示" checked>
      <input type="radio" name="api_visible" value="0" title="不显示">
    </div>
    <span style="color: #a3a3a3;font-size: 9px">控制API接口中是否返回此分类</span>
  </div>
  <div class="layui-form-item">
    <label class="layui-form-label">分类描述：</label>
    <div class="layui-input-inline">
      <textarea name="remark" autocomplete="off" class="layui-textarea"></textarea>
    </div>
  </div>

  <div class="layui-form-item layui-hide">
      <input type="button" lay-submit lay-filter="add-goods_category-submit" id="add-goods_category-submit" value="确认">
  </div>
</div>
<script>
  layui.config({
    version:"{$front_version}",
    base: '/static/lib' //静态资源所在路径
  }).use(['form'], function(){
    var $ = layui.$
    ,form = layui.form;

    //监听上级分类选择
    form.on('select(search_pid)', function(data){
      if(data.value != 0) {
        // 只有顶级类才可以设置首页分类背景图,，其它级别分类隐藏上传设置
        $('#bg_image').addClass('layui-hide');
        // 先存值至data-img属性中
        $("input[name='bg_image']").attr('data-img', $("input[name='bg_image']").val());
        $("input[name='bg_image']").val('');
      }else{
        // 显示图片上传
        $('#bg_image').removeClass('layui-hide');
        // 还原值隐藏字段值
        $("input[name='bg_image']").val($("input[name='bg_image']").attr('data-img'));
      }
    });

    // 图片上传
    like.delUpload();
    // 分类图标
    $(document).on("click", "#image", function () {
        like.imageUpload({
            limit: 1,
            field: "image",
            that: $(this)
        });
    })
    // 首页分类背景图
    $(document).on("click", "#bgimage", function () {
        like.imageUpload({
            limit: 1,
            field: "bg_image",
            that: $(this)
        });
    })



  })
</script>
