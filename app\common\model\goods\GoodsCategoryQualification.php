<?php

namespace app\common\model\goods;

use think\model\Pivot;

/**
 * 分类资质关联模型
 * Class GoodsCategoryQualification
 * @package app\common\model\goods
 */
class GoodsCategoryQualification extends Pivot
{
    protected $table = 'ls_goods_category_qualification';
    /**
     * 关联分类表
     */
    public function category()
    {
        return $this->belongsTo(GoodsCategory::class, 'category_id', 'id');
    }

    /**
     * 关联资质表
     */
    public function qualification()
    {
        return $this->belongsTo(Qualification::class, 'qualification_id', 'id');
    }
}
