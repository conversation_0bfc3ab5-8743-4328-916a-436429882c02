<?php

namespace app\common\model\shop;

use app\common\basics\Models;

class ShopDepositDetails extends Models
{
    /**
     * @Notes: 关联商家账号模型
     */
    public function admin()
    {
        return $this->hasOne('Shop', 'id', 'shop_id');
    }

    /**
     * @Notes: 关联商家账号模型
     */
    public function deposi()
    {
        return $this->hasOne(ShopDeposit::class, 'id', 'deposit_id');
    }

    /*
     * 商家保证金明细列表
     *
     */
    public function detail($id){
        $model = new ShopApply();
        $detail = $model->field(true)
            ->where(['id'=>(int)$id])
            ->with(['category'])
            ->findOrEmpty()->toArray();

        $detail['category']      = $detail['category']['name'] ?? '未知类目';
        $detail['audit_status']  = ShopEnum::getAuditStatusDesc($detail['audit_status']);
        $detail['audit_explain'] = $detail['audit_explain'] == '' ? '无' : $detail['audit_explain'];
        return $detail;

    }
}