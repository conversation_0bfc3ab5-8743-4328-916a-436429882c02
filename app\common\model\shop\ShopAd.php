<?php

namespace app\common\model\shop;

use app\common\enum\ShopAdEnum;

class ShopAd extends \app\common\basics\Models
{
    function getPlaceNameAttr($fieldValue, $data)
    {
        return ShopAdEnum::getPlaceDesc($data['place']);
    }
    
    function getTerminalNameAttr($fieldValue, $data)
    {
        return ShopAdEnum::getTerminal($data['terminal']);
    }
    
    function getStatusNameAttr($fieldValue, $data)
    {
        return $data['status'] == 1 ? '正常' : '关闭';
    }
    
    function getLinkPathAttr($fieldValue, $data)
    {
        return parse_url($data['link'], PHP_URL_PATH);
    }
    
    function getLinkQueryAttr($fieldValue, $data)
    {
        parse_str(parse_url($data['link'], PHP_URL_QUERY), $arr);
        return $arr;
    }
}