<?php

namespace app\admin\model\agent;

use app\common\model\BaseModel;

class MerchantLevelConfig extends BaseModel
{
    protected $name = 'merchant_level_config';

    /**
     * @notes 获取配置列表
     * @param $params
     * @return array
     */
    public static function lists($params):
    array
    {
        $model = new static();
        if (isset($params['level_name']) && $params['level_name']) {
            $model->where('level_name', 'like', '%' . $params['level_name'] . '%');
        }

        $lists = $model->order('level_id', 'asc')
            ->paginate([
                'list_rows' => $params['limit'],
                'page' => $params['page']
            ])->toArray();

        return [
            'count' => $lists['total'],
            'lists' => $lists['data']
        ];
    }
}
