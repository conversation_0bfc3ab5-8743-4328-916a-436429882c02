{"version": 3, "file": "pages/shop_street_detail.js", "sources": ["webpack:///./components/price-formate.vue?b158", "webpack:///./components/price-formate.vue?4c8e", "webpack:///./components/price-formate.vue", "webpack:///./components/price-formate.vue?3e0e", "webpack:///./components/price-formate.vue?2b64", "webpack:///./components/price-formate.vue?8e8e", "webpack:///./components/price-formate.vue?32b6", "webpack:///./components/null-data.vue?48f8", "webpack:///./components/null-data.vue?97fe", "webpack:///./components/null-data.vue?fba4", "webpack:///./components/null-data.vue?cbf9", "webpack:///./components/null-data.vue", "webpack:///./components/null-data.vue?da63", "webpack:///./components/null-data.vue?475d", "webpack:///./utils/tools.js", "webpack:///./components/goods-list.vue?c658", "webpack:///./components/goods-list.vue?fc8c", "webpack:///./components/goods-list.vue?0eb4", "webpack:///./components/goods-list.vue?0d98", "webpack:///./components/goods-list.vue", "webpack:///./components/goods-list.vue?d834", "webpack:///./components/goods-list.vue?12b6", "webpack:///./static/images/goods_null.png", "webpack:///./static/images/coupons_img_receive.png", "webpack:///./static/images/bg_coupon_s.png", "webpack:///./static/images/bg_coupon.png", "webpack:///./pages/shop_street_detail.vue?bb52", "webpack:///./pages/shop_street_detail.vue?5d9b", "webpack:///./pages/shop_street_detail.vue?83e2", "webpack:///./pages/shop_street_detail.vue?383c", "webpack:///./pages/shop_street_detail.vue", "webpack:///./pages/shop_street_detail.vue?371d", "webpack:///./pages/shop_street_detail.vue?54f3"], "sourcesContent": ["// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../node_modules/css-loader/dist/cjs.js??ref--3-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--3-oneOf-1-2!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./price-formate.vue?vue&type=style&index=0&lang=css&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"3181fc86\", content, true, context)\n};", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('span',{class:(_vm.lineThrough ? 'line-through' : '') + 'price-format',style:({ color: _vm.color, 'font-weight': _vm.weight })},[_vm._ssrNode(((_vm.showSubscript)?(\"<span\"+(_vm._ssrStyle(null,{\n            'font-size': _vm.subscriptSize + 'px',\n            'margin-right': '1px',\n        }, null))+\">¥</span>\"):\"<!---->\")+\" <span\"+(_vm._ssrStyle(null,{ 'font-size': _vm.firstSize + 'px', 'margin-right': '1px' }, null))+\">\"+_vm._ssrEscape(_vm._s(_vm.priceSlice.first))+\"</span> \"+((_vm.priceSlice.second)?(\"<span\"+(_vm._ssrStyle(null,{ 'font-size': _vm.secondSize + 'px' }, null))+\">\"+_vm._ssrEscape(\".\"+_vm._s(_vm.priceSlice.second))+\"</span>\"):\"<!---->\"))])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nexport default {\n    data() {\n        return {\n            priceSlice: {},\n        }\n    },\n    components: {},\n    props: {\n        firstSize: {\n            type: Number,\n            default: 14,\n        },\n        secondSize: {\n            type: Number,\n            default: 14,\n        },\n        color: {\n            type: String,\n        },\n        weight: {\n            type: [String, Number],\n            default: 400,\n        },\n        price: {\n            type: [String, Number],\n            default: '',\n        },\n        showSubscript: {\n            type: Boolean,\n            default: true,\n        },\n        subscriptSize: {\n            type: Number,\n            default: 14,\n        },\n        lineThrough: {\n            type: Boolean,\n            default: false,\n        },\n    },\n    created() {\n        this.priceFormat()\n    },\n    watch: {\n        price(val) {\n            this.priceFormat()\n        },\n    },\n    methods: {\n        priceFormat() {\n            let { price } = this\n            let priceSlice = {}\n            if (price !== null) {\n                price = parseFloat(price)\n                price = String(price).split('.')\n                priceSlice.first = price[0]\n                priceSlice.second = price[1]\n                this.priceSlice = priceSlice\n            }\n        },\n    },\n}\n", "import mod from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./price-formate.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./price-formate.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./price-formate.vue?vue&type=template&id=0c4d5c85&\"\nimport script from \"./price-formate.vue?vue&type=script&lang=js&\"\nexport * from \"./price-formate.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./price-formate.vue?vue&type=style&index=0&lang=css&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  null,\n  \"7ae24710\"\n  \n)\n\nexport default component.exports", "export * from \"-!../node_modules/vue-style-loader/index.js??ref--3-oneOf-1-0!../node_modules/css-loader/dist/cjs.js??ref--3-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--3-oneOf-1-2!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./price-formate.vue?vue&type=style&index=0&lang=css&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".price-format{display:flex;align-items:baseline}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/sass-resources-loader/lib/loader.js??ref--7-oneOf-1-4!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./null-data.vue?vue&type=style&index=0&id=93598fb0&lang=scss&scoped=true&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"12a18d22\", content, true, context)\n};", "export * from \"-!../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/sass-resources-loader/lib/loader.js??ref--7-oneOf-1-4!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./null-data.vue?vue&type=style&index=0&id=93598fb0&lang=scss&scoped=true&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".null-data[data-v-93598fb0]{padding:100px}.null-data .img-null[data-v-93598fb0]{width:150px;height:150px}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"bg-white flex-col col-center null-data\"},[_vm._ssrNode(\"<img\"+(_vm._ssrAttr(\"src\",_vm.img))+\" alt class=\\\"img-null\\\"\"+(_vm._ssrStyle(null,_vm.imgStyle, null))+\" data-v-93598fb0> <div class=\\\"muted mt8\\\" data-v-93598fb0>\"+_vm._ssrEscape(_vm._s(_vm.text))+\"</div>\")])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n\nexport default {\n    components: {},\n    props: {\n        img: {\n            type: String,\n        },\n        text: {\n            type: String,\n            default: '暂无数据',\n        },\n        imgStyle: {\n            type: String,\n            default: '',\n        },\n    },\n    methods: {},\n}\n", "import mod from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./null-data.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./null-data.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./null-data.vue?vue&type=template&id=93598fb0&scoped=true&\"\nimport script from \"./null-data.vue?vue&type=script&lang=js&\"\nexport * from \"./null-data.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./null-data.vue?vue&type=style&index=0&id=93598fb0&lang=scss&scoped=true&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  \"93598fb0\",\n  \"728f99de\"\n  \n)\n\nexport default component.exports", "\n//节流\nexport const trottle = (func, time = 1000, context) => {\n\tlet previous = new Date(0).getTime()\n\treturn function(...args) {\n\t\tlet now = new Date().getTime()\n\t\tif (now - previous > time) {\n\t\t\tfunc.apply(context, args)\n\t\t\tprevious = now\n\t\t}\n\t}\n}\n\n\n//获取url后的参数  以对象返回\nexport function strToParams(str) {\n\tvar newparams = {}\n\tfor (let item of str.split('&')) {\n\t\tnewparams[item.split('=')[0]] = item.split('=')[1]\n\t}\n\treturn newparams\n}\n\n//对象参数转为以？&拼接的字符\nexport function paramsToStr(params) {\n\tlet p = '';\n\tif (typeof params == 'object') {\n\t\tp = '?'\n\t\tfor (let props in params) {\n\t\t\tp += `${props}=${params[props]}&`\n\t\t}\n\t\tp = p.slice(0, -1)\n\t}\n\treturn p\n}\n\n/**\n * @description 复制到剪切板\n * @param value { String } 复制内容\n * @return { Promise } resolve | reject\n */\n export const copyClipboard = (value) => {\n    const elInput = document.createElement('input')\n\n    elInput.setAttribute('value', value)\n    document.body.appendChild(elInput)\n    elInput.select()\n\n    try{\n        if(document.execCommand('copy'))\n            return Promise.resolve()\n        else\n            throw new Error()\n    } catch(err) {\n        return Promise.reject(err)\n    } finally {\n        document.body.removeChild(elInput)\n    }\n}", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/sass-resources-loader/lib/loader.js??ref--7-oneOf-1-4!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./goods-list.vue?vue&type=style&index=0&id=060944d1&lang=scss&scoped=true&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"1469a4e1\", content, true, context)\n};", "export * from \"-!../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/sass-resources-loader/lib/loader.js??ref--7-oneOf-1-4!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./goods-list.vue?vue&type=style&index=0&id=060944d1&lang=scss&scoped=true&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".goods-list[data-v-060944d1]{align-items:stretch}.goods-list .goods-item[data-v-060944d1]{display:block;box-sizing:border-box;width:224px;height:310px;margin-bottom:16px;padding:12px 12px 16px;border-radius:4px;transition:all .2s}.goods-list .goods-item[data-v-060944d1]:hover{transform:translateY(-8px);box-shadow:0 0 6px rgba(0,0,0,.1)}.goods-list .goods-item .goods-img[data-v-060944d1]{width:200px;height:200px}.goods-list .goods-item .name[data-v-060944d1]{margin-bottom:10px;height:40px;line-height:20px}.goods-list .goods-item .seckill .btn[data-v-060944d1]{padding:4px 12px;border-radius:4px;border:1px solid transparent}.goods-list .goods-item .seckill .btn.not-start[data-v-060944d1]{border-color:#ff2c3c;color:#ff2c3c;background-color:transparent}.goods-list .goods-item .seckill .btn.end[data-v-060944d1]{background-color:#e5e5e5;color:#fff}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"goods-list flex flex-wrap\"},_vm._l((_vm.list),function(item,index){return _c('nuxt-link',{key:index,staticClass:\"goods-item bg-white\",style:({ marginRight: (index + 1) % _vm.num == 0 ? 0 : '14px' }),attrs:{\"to\":(\"/goods_details/\" + (item.id||item.goods_id))}},[_c('el-image',{staticClass:\"goods-img\",attrs:{\"lazy\":\"\",\"src\":item.image||item.goods_image,\"alt\":\"\"}}),_vm._v(\" \"),_c('div',{staticClass:\"name line-2\"},[_vm._v(_vm._s(item.name||item.goods_name))]),_vm._v(\" \"),(_vm.type == 'seckill')?_c('div',{staticClass:\"seckill flex row-between\"},[_c('div',{staticClass:\"primary flex\"},[_vm._v(\"\\n                秒杀价\\n                \"),_c('price-formate',{attrs:{\"price\":item.seckill_price,\"first-size\":18}})],1),_vm._v(\" \"),_c('div',{class:['btn bg-primary white', {'not-start' : _vm.status == 0, end: _vm.status == 2}]},[_vm._v(_vm._s(_vm.getSeckillText)+\"\\n            \")])]):_c('div',{staticClass:\"flex row-between flex-wrap\"},[_c('div',{staticClass:\"price flex col-baseline\"},[_c('div',{staticClass:\"primary m-r-8\"},[_c('price-formate',{attrs:{\"price\":item.min_price || item.price,\"first-size\":16}})],1),_vm._v(\" \"),_c('div',{staticClass:\"muted sm line-through\"},[_c('price-formate',{attrs:{\"price\":item.market_price}})],1)]),_vm._v(\" \"),_c('div',{staticClass:\"muted xs\"},[_vm._v(_vm._s(item.sales_total || item.sales_sum || 0)+\"人购买\")])])],1)}),1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nexport default {\n    props: {\n        list: {\n            type: Array,\n            default: () => [],\n        },\n        num: {\n            type: Number,\n            default: 5,\n        },\n        type: {\n            type: String,\n        },\n        status: {\n            type: Number,\n        },\n    },\n    watch: {\n        list: {\n            immediate: true,\n            handler: function (val) {},\n        },\n    },\n    computed: {\n        getSeckillText() {\n            switch (this.status) {\n                case 0:\n                    return \"未开始\";\n                case 1:\n                    return \"立即抢购\";\n                case 2:\n                    return \"已结束\";\n            }\n        },\n    },\n};\n", "import mod from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./goods-list.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./goods-list.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./goods-list.vue?vue&type=template&id=060944d1&scoped=true&\"\nimport script from \"./goods-list.vue?vue&type=script&lang=js&\"\nexport * from \"./goods-list.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./goods-list.vue?vue&type=style&index=0&id=060944d1&lang=scss&scoped=true&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  \"060944d1\",\n  \"606a8712\"\n  \n)\n\nexport default component.exports\n\n/* nuxt-component-imports */\ninstallComponents(component, {PriceFormate: require('/Users/<USER>/Desktop/vue/pc/components/price-formate.vue').default})\n", "module.exports = __webpack_public_path__ + \"img/goods_null.38f1689.png\";", "module.exports = __webpack_public_path__ + \"img/coupons_img_receive.d691393.png\";", "module.exports = __webpack_public_path__ + \"img/bg_coupon_s.3f57cfd.png\";", "module.exports = __webpack_public_path__ + \"img/bg_coupon.b22691e.png\";", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/sass-resources-loader/lib/loader.js??ref--7-oneOf-1-4!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./shop_street_detail.vue?vue&type=style&index=0&lang=scss&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"54772eb0\", content, true, context)\n};", "export * from \"-!../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/sass-resources-loader/lib/loader.js??ref--7-oneOf-1-4!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./shop_street_detail.vue?vue&type=style&index=0&lang=scss&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_GET_URL_IMPORT___ = require(\"../node_modules/css-loader/dist/runtime/getUrl.js\");\nvar ___CSS_LOADER_URL_IMPORT_0___ = require(\"../static/images/bg_coupon_s.png\");\nvar ___CSS_LOADER_URL_IMPORT_1___ = require(\"../static/images/bg_coupon.png\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\nvar ___CSS_LOADER_URL_REPLACEMENT_0___ = ___CSS_LOADER_GET_URL_IMPORT___(___CSS_LOADER_URL_IMPORT_0___);\nvar ___CSS_LOADER_URL_REPLACEMENT_1___ = ___CSS_LOADER_GET_URL_IMPORT___(___CSS_LOADER_URL_IMPORT_1___);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".shop{width:210px;padding:15px}.shop .logo-img{width:62px;height:62px;border-radius:50%;overflow:hidden}.shop .el-rate__icon{font-size:16px}.h-item{width:82px;height:24px;margin-right:30px;cursor:pointer}.h-item-x{border-radius:100px;background-color:#ff2c3c;color:#fff}.search{width:240px}.search ::v-deep .el-input{width:240px;border-radius:10px}.shop-details{margin-top:10px}.shop-details .left .l-border{padding-bottom:27px;border-bottom:1px solid #eee;margin-bottom:27px}.shop-details .left .desc{color:#101010;font-size:12px}.shop-details .left .desc-b{color:#fff;font-size:12px}.shop-details .left .desc-n{color:#fff;font-size:18px;color:#101010;font-size:14px}.shop-details .left .left-btn{width:82px;height:29px;border-radius:4px;border:1px solid #bbb}.shop-details .left .left-shop{background-color:#fff;padding:20px 15px;width:210px;height:364px}.shop-details .left .r-color-h{background-color:#a4adb3;color:#fff}.shop-details .left .l-tips{padding:1px 2px}.shop-details .left .l-fen{width:210px;height:44px;line-height:20px;color:#101010;font-size:14px;text-align:center;cursor:pointer}.shop-details .left .l-fen-select{color:#ff2c3c}.shop-details .right{width:961px}.shop-details .right .coupon-list{background-color:#fff;padding:20px 0;margin:0 20px;border-bottom:1px solid #eee}.shop-details .right .coupon-list .coupons-more{cursor:pointer}.shop-details .right .coupon-list .swiper-item-c{width:760px;flex-wrap:nowrap;overflow:hidden}.shop-details .right .coupon-list .swiper-item-zk{width:770px;flex-wrap:wrap}.shop-details .right .shop-list{background-color:#fff;height:360px;padding:10px 20px 0}.shop-details .right .shop-list .shop-item{width:200px;height:298px;background-color:#fff;margin-right:12px}.shop-details .right .shop-list .shop-item .name{color:#101010;font-size:14px;text-align:left;margin-bottom:18px}.shop-details .sort{padding:16px 16px 0}.shop-details .sort .sort-name .item{margin-right:30px;cursor:pointer}.shop-details .sort .sort-name .item.active{color:#ff2c3c}.shop-details .swiper-item{width:672px}.shop-details .item{margin-bottom:20px;margin-right:16px;position:relative;cursor:pointer}.shop-details .item .coupon-button{background-color:#f2f2f2;width:240px;height:30px;padding:0 8px}.shop-details .item .info{padding:0 10px;background:url(\" + ___CSS_LOADER_URL_REPLACEMENT_0___ + \") no-repeat;width:240px;height:80px;background-size:100%}.shop-details .item .info.gray{background-image:url(\" + ___CSS_LOADER_URL_REPLACEMENT_1___ + \")}.shop-details .item .info .info-hd{overflow:hidden}.shop-details .item .tips{position:relative;background-color:#f2f2f2;height:30px;padding:0 8px}.shop-details .item .tips .tips-con{width:100%;left:0;background-color:#f2f2f2;position:absolute;top:30px;padding:10px;z-index:99}.shop-details .item .receice{position:absolute;top:0;right:0;width:58px;height:45px}.shop-details .item .choose{position:absolute;top:0;right:0;background-color:#ffe72c;color:#ff2c3c;padding:1px 5px}.shop-details .more{position:absolute;bottom:20px;cursor:pointer;right:30px}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{},[_vm._ssrNode(\"<div class=\\\"bg-white\\\">\",\"</div>\",[(_vm.shopInfo.banner)?_vm._ssrNode(\"<div class=\\\"flex flex-1 row-center col-center\\\" style=\\\"width: 100%; height: 150px;\\\">\",\"</div>\",[_c('el-image',{staticStyle:{\"height\":\"100%\",\"width\":\"100%\",\"max-width\":\"1920px\"},attrs:{\"src\":_vm.shopInfo.banner,\"fit\":\"cover\"}})],1):_vm._e(),_vm._ssrNode(\" \"),_vm._ssrNode(\"<div class=\\\"wrapper1180 flex flex-1 col-center row-between\\\" style=\\\"height: 40px\\\">\",\"</div>\",[_vm._ssrNode(\"<div\"+(_vm._ssrClass(\"h-item flex row-center\",_vm.xuanIndex =='' ? 'h-item-x':''))+\">\\n                店铺首页\\n            </div> \"),_vm._ssrNode(\"<div class=\\\"flex row-left flex-1\\\">\",\"</div>\",[_vm._ssrNode(\"<div\"+(_vm._ssrClass(\"h-item flex row-center\",_vm.xuanIndex =='all' ? 'h-item-x':''))+\">\\n                    全部商品\\n                </div> \"),_c('swiper',{ref:\"mySwiper\",staticClass:\"swiper flex row-left\",staticStyle:{\"width\":\"672px\",\"display\":\"flex\",\"justify-content\":\"flex-start\",\"margin\":\"0\"},attrs:{\"options\":_vm.swiperOptions}},_vm._l((_vm.goodsClassListGroup),function(itemd,indexd){return _c('swiper-slide',{key:indexd,staticClass:\"swiper-item flex row-left\"},[_c('div',{staticClass:\"flex\"},_vm._l((itemd),function(item,index){return _c('div',{key:index},[_c('div',{staticClass:\"h-item flex row-center\",class:_vm.xuanIndex == item.id ? 'h-item-x':'',on:{\"click\":function($event){return _vm.changeXuan(item.id)}}},[_vm._v(\"\\n                                    \"+_vm._s(item.name)+\"\\n                                \")])])}),0)])}),1)],2),_vm._ssrNode(\" \"),_vm._ssrNode(\"<div class=\\\"search\\\">\",\"</div>\",[_c('el-input',{attrs:{\"placeholder\":\"店铺搜索\",\"size\":\"mini\"},on:{\"change\":_vm.search},model:{value:(_vm.keyword),callback:function ($$v) {_vm.keyword=$$v},expression:\"keyword\"}})],1)],2)],2),_vm._ssrNode(\" \"),_vm._ssrNode(\"<div class=\\\"shop-details flex col-top wrapper1180 flex-1\\\">\",\"</div>\",[_vm._ssrNode(\"<div class=\\\"left\\\">\",\"</div>\",[_vm._ssrNode(\"<div class=\\\"shop bg-white\\\">\",\"</div>\",[_vm._ssrNode(\"<div class=\\\"shop-logo flex-col col-center\\\">\",\"</div>\",[_c('el-image',{staticClass:\"logo-img\",attrs:{\"src\":_vm.shopInfo.logo}}),_vm._ssrNode(\" \"),_vm._ssrNode(\"<div class=\\\"m-t-10\\\">\",\"</div>\",[(_vm.shopInfo.type == 1)?_c('el-tag',{attrs:{\"size\":\"mini\"}},[_vm._v(\"自营\")]):_vm._e(),_vm._ssrNode(\" <span class=\\\"weight-500\\\">\"+_vm._ssrEscape(_vm._s(_vm.shopInfo.name))+\"</span>\")],2),_vm._ssrNode(\" <div class=\\\"xs muted m-t-10 line-5\\\">\"+_vm._ssrEscape(\"\\n                        \"+_vm._s(_vm.shopInfo.intro)+\"\\n                    \")+\"</div>\")],2),_vm._ssrNode(\" <div class=\\\"flex m-t-30\\\"><div class=\\\"flex-1 text-center\\\"><div class=\\\"xxl m-b-10\\\">\"+_vm._ssrEscape(_vm._s(_vm.shopInfo.on_sale_count))+\"</div> <div>全部商品</div></div> <div class=\\\"flex-1 text-center\\\"><div class=\\\"xxl m-b-10\\\">\"+_vm._ssrEscape(_vm._s(_vm.shopInfo.visited_num))+\"</div> <div>关注人数</div></div></div> \"),_c('el-divider'),_vm._ssrNode(\" \"),_vm._ssrNode(\"<div class=\\\"flex xs m-b-16\\\">\",\"</div>\",[_vm._ssrNode(\"<div class=\\\"m-r-12\\\">店铺星级</div> \"),_vm._ssrNode(\"<div class=\\\"m-t-5\\\">\",\"</div>\",[_c('el-rate',{attrs:{\"disabled\":\"\"},model:{value:(_vm.shopInfo.star),callback:function ($$v) {_vm.$set(_vm.shopInfo, \"star\", $$v)},expression:\"shopInfo.star\"}})],1)],2),_vm._ssrNode(\" <div class=\\\"flex xs m-b-16\\\"><div class=\\\"m-r-12\\\">店铺评分</div> <div>\"+_vm._ssrEscape(_vm._s(_vm.shopInfo.score)+\"分\")+\"</div></div> \"),_vm._ssrNode(\"<div class=\\\"flex row-center row-between\\\">\",\"</div>\",[_vm._ssrNode(\"<div class=\\\"flex row-center\\\">\",\"</div>\",[_c('el-button',{attrs:{\"size\":\"mini\"},on:{\"click\":_vm.shopFollow}},[_vm._v(_vm._s(_vm.shopInfo.shop_follow_status == 1 ? '已关注' : '关注店铺'))])],1),_vm._ssrNode(\" \"),_c('el-popover',{attrs:{\"placement\":\"bottom\",\"width\":\"200\",\"trigger\":\"hover\"}},[_c('div',[_c('el-image',{staticStyle:{\"width\":\"100%\"},attrs:{\"src\":_vm.shopInfo.customer_image}})],1),_vm._v(\" \"),_c('div',{staticClass:\"xs lighter text-center\",attrs:{\"slot\":\"reference\"},slot:\"reference\"},[_c('i',{staticClass:\"el-icon-chat-dot-round nr\"}),_vm._v(\" \"),_c('span',[_vm._v(\"联系客服\")])])])],2)],2),_vm._ssrNode(\" <div class=\\\"m-t-10 bg-white\\\"><div\"+(_vm._ssrClass(\"l-fen flex row-center\",_vm.gClassId == ''?'l-fen-select':''))+\">\\n                    全部商品\\n                </div> \"+(_vm._ssrList((_vm.goodsClassList),function(item,index){return (\"<div><div\"+(_vm._ssrClass(\"l-fen flex row-center\",_vm.gClassId == item.id?'l-fen-select':''))+(_vm._ssrStyle(null,null, { display: (index < 4) ? '' : 'none' }))+\">\"+_vm._ssrEscape(\"\\n                        \"+_vm._s(item.name)+\"\\n                    \")+\"</div></div>\")}))+\"</div>\")],2),_vm._ssrNode(\" \"),_vm._ssrNode(\"<div class=\\\"right m-l-15\\\">\",\"</div>\",[(_vm.couponsList.length && _vm.xuanIndex == '')?_vm._ssrNode(\"<div class=\\\"bg-white\\\">\",\"</div>\",[_vm._ssrNode(\"<div class=\\\"coupon-list\\\">\",\"</div>\",[_vm._ssrNode(\"<div class=\\\"m-b-10 flex row-between\\\"><div>\\n                            领券\\n                        </div> \"+((_vm.couponsList.length > 3)?(\"<div class=\\\"flex row-center coupons-more\\\"><div class=\\\"m-r-5\\\">\\n                                更多\\n                            </div> <i\"+(_vm._ssrClass(null,_vm.more?'el-icon-arrow-up':'el-icon-arrow-down'))+\"></i></div>\"):\"<!---->\")+\"</div> \"),_vm._ssrNode(\"<div\"+(_vm._ssrClass(\"flex\",_vm.more? 'swiper-item-zk':'swiper-item-c'))+\">\",\"</div>\",_vm._l((_vm.couponsList),function(item,index){return _vm._ssrNode(\"<div class=\\\"item\\\">\",\"</div>\",[_vm._ssrNode(\"<div\"+(_vm._ssrClass(null,[\n                              'info white',\n                              { gray: item.is_get } ]))+\">\",\"</div>\",[_vm._ssrNode(\"<div class=\\\"info-hd flex\\\">\",\"</div>\",[_vm._ssrNode(\"<div>\",\"</div>\",[_c('price-formate',{attrs:{\"price\":item.money,\"first-size\":38,\"second-size\":38}})],1),_vm._ssrNode(\" <div class=\\\"m-l-8 flex1\\\"><div class=\\\"line1\\\">\"+_vm._ssrEscape(_vm._s(item.name))+\"</div> <div class=\\\"xs line1\\\">\"+_vm._ssrEscape(_vm._s(item.condition_type_desc)+\"\\n                                        \")+\"</div></div>\")],2),_vm._ssrNode(\" <div class=\\\"info-time xs\\\">\"+_vm._ssrEscape(_vm._s(item.user_time_desc))+\"</div>\")],2),_vm._ssrNode(\" <div class=\\\"flex row-between coupon-button\\\"><div class=\\\"tips-con xs lighter\\\">\"+_vm._ssrEscape(\"\\n                                    \"+_vm._s(item.use_scene_desc)+\"\\n                                \")+\"</div> \"+((!item.is_get)?(\"<div class=\\\"primary sm\\\">\\n                                    立即领取\\n                                </div>\"):\"<!---->\")+\"</div> \"+((item.is_get)?(\"<img\"+(_vm._ssrAttr(\"src\",require(\"static/images/coupons_img_receive.png\")))+\" alt class=\\\"receice\\\">\"):\"<!---->\"))],2)}),0)],2)]):_vm._e(),_vm._ssrNode(\" \"),(_vm.recommend && _vm.xuanIndex == '')?_vm._ssrNode(\"<div class=\\\"shop-list\\\">\",\"</div>\",[_vm._ssrNode(\"<div class=\\\"m-b-10\\\">\\n                    店铺推荐\\n                </div> \"),_c('el-carousel',{attrs:{\"arrow\":\"never\",\"indicator-position\":\"outside\",\"trigger\":\"click\",\"height\":\"300px\",\"autoplay\":false}},_vm._l((_vm.recommend),function(itemd,indexd){return _c('el-carousel-item',{key:indexd},[_c('div',{staticClass:\"flex\"},_vm._l((itemd),function(itemg,indexg){return _c('div',{key:indexg,staticClass:\"shop-item\"},[_c('nuxt-link',{attrs:{\"to\":(\"/goods_details/\" + (itemg.id))}},[_c('div',{},[_c('div',{},[_c('el-image',{staticStyle:{\"height\":\"200px\",\"width\":\"200px\"},attrs:{\"src\":itemg.image}})],1),_vm._v(\" \"),_c('div',{staticClass:\"name m-l-10 line-1\"},[_vm._v(\"\\n                                            \"+_vm._s(itemg.name)+\"\\n                                        \")]),_vm._v(\" \"),_c('div',{staticClass:\"m-l-10 flex\"},[_c('div',{staticClass:\"primary m-r-8\"},[_c('price-formate',{attrs:{\"price\":itemg.min_price,\"first-size\":16}})],1),_vm._v(\" \"),_c('div',{staticClass:\"muted sm line-through\"},[_c('price-formate',{attrs:{\"price\":itemg.market_price}})],1)])])])],1)}),0)])}),1)],2):_vm._e(),_vm._ssrNode(\" \"),_vm._ssrNode(\"<div\"+(_vm._ssrClass(null,_vm.xuanIndex == ''? 'm-t-10':''))+\">\",\"</div>\",[_vm._ssrNode(\"<div id=\\\"goods-sort\\\" class=\\\"sort m-b-16 flex bg-white col-top\\\"><div class=\\\"sort-title\\\">排序方式：</div> <div class=\\\"sort-name m-l-16 flex\\\"><div\"+(_vm._ssrClass(null,['item', { active: _vm.sortType == '' }]))+\">\\n                            综合\\n                        </div> <div\"+(_vm._ssrClass(null,['item', { active: _vm.sortType == 'price' }]))+\">\\n                            价格\\n                            <i class=\\\"el-icon-arrow-down\\\"\"+(_vm._ssrStyle(null,null, { display: (_vm.priceSort == 'desc') ? '' : 'none' }))+\"></i> <i class=\\\"el-icon-arrow-up\\\"\"+(_vm._ssrStyle(null,null, { display: (_vm.priceSort == 'asc') ? '' : 'none' }))+\"></i></div> <div\"+(_vm._ssrClass(null,['item', { active: _vm.sortType == 'sales_sum' }]))+\">\\n                            销量\\n                            <i class=\\\"el-icon-arrow-down\\\"\"+(_vm._ssrStyle(null,null, { display: (_vm.saleSort == 'desc') ? '' : 'none' }))+\"></i> <i class=\\\"el-icon-arrow-up\\\"\"+(_vm._ssrStyle(null,null, { display: (_vm.saleSort == 'asc') ? '' : 'none' }))+\"></i></div></div></div> \"),(_vm.goodsList.length)?[_c('goods-list',{attrs:{\"list\":_vm.goodsList}}),_vm._ssrNode(\" \"),(_vm.count)?_vm._ssrNode(\"<div class=\\\"pagination flex m-t-30 row-center\\\" style=\\\"padding-bottom: 38px\\\">\",\"</div>\",[_c('el-pagination',{attrs:{\"background\":\"\",\"layout\":\"prev, pager, next\",\"total\":_vm.count,\"prev-text\":\"上一页\",\"next-text\":\"下一页\",\"hide-on-single-page\":\"\",\"page-size\":20},on:{\"current-change\":_vm.changePage}})],1):_vm._e()]:_c('null-data',{attrs:{\"img\":require('~/static/images/goods_null.png'),\"text\":\"暂无商品~\"}})],2)],2)],2)],2)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport {\n    trottle\n} from \"~/utils/tools\";\nimport {\n    Message\n} from 'element-ui';\nexport default {\n    head() {\n        return {\n            title: this.$store.getters.headTitle,\n            link: [{\n                rel: 'icon',\n                type: 'image/x-icon',\n                href: this.$store.getters.favicon,\n            }, ],\n        }\n    },\n\n    layout: \"street\",\n\n    components: {},\n    async asyncData({\n        $get,\n        query,\n    }) {\n        // 店铺信息\n        const\n            shopData = await $get(\"shop/getShopInfo\", {\n                params: {\n                    shop_id: query.id\n                },\n            });\n        if (shopData.code == 1) {\n            if (shopData.data.goods_list.length > 0) {\n                var num = [];\n                for (var i = 0; i < Math.ceil(shopData.data.goods_list.length / 4); i++) {\n                    var start = i * 4;\n                    var end = start + 4;\n                    num.push(shopData.data.goods_list.slice(start, end));\n                }\n            }\n        }\n        console.log('num', num)\n\n        // 获取优惠券\n        const\n            coupon = await $get(\"coupon/getCouponList\", {\n                params: {\n                    shop_id: query.id\n                },\n            });\n\n        // 商品分类\n        const\n            goodsClass = await $get(\"shop_goods_category/getShopGoodsCategory\", {\n                params: {\n                    shop_id: query.id\n                },\n            });\n\n        if (goodsClass.code == 1) {\n            if (goodsClass.data.length > 0) {\n                var group = [];\n                for (var i = 0; i < Math.ceil(goodsClass.data.length / 6); i++) {\n                    var start = i * 6;\n                    var end = start + 6;\n                    group.push(goodsClass.data.slice(start, end));\n                }\n            }\n        }\n        console.log('group', group)\n\n        return {\n            recommend: num, // 推荐列表\n            shopInfo: shopData.data, // 商家信息\n            goodsClassList: goodsClass.data, // 商品分类列表\n            goodsClassListGroup: group, // 商品分类切割列表\n            couponsList: coupon.data.lists, // 优惠券列表\n        };\n    },\n    data() {\n        return {\n            goodsClassListGroup: [],\n            recommend: [],\n            couponsList: [],\n            gClassId: '',\n            shopInfo: [],\n            goodsClassList: [],\n            swiperOptions: {\n                pagination: {\n                    el: '.swiper-pagination',\n                    clickable: true,\n                },\n                navigation: {\n                    nextEl: '.swiper-button-next',\n                    prevEl: '.swiper-button-prev',\n                },\n                preventClicks: true,\n                slidesPerView: 'auto',\n                autoplay: true,\n            },\n\n            sortType: \"\",\n            saleSort: \"desc\",\n            priceSort: \"desc\",\n            page: 1,\n            count: 0,\n            goodsList: [],\n\n            more: false,\n\n            keyword: '',\n            xuanIndex: '',\n        }\n    },\n\n    created() {\n        this.getGoods();\n        this.changeSortType = trottle(this.changeSortType, 500, this);\n    },\n\n    methods: {\n        // 搜索商品\n        search() {\n            this.getGoods()\n\n            // 搜索后跳转到商品列表位置\n            if(this.xuanIndex == '') {\n                setTimeout(() => {\n                    document.getElementById('goods-sort').scrollIntoView()\n                }, 500)\n            }\n        },\n\n        // 顶部商品分类选择事件\n        changeXuan(id) {\n            this.xuanIndex = id\n            if (id == 'all') {\n                this.gClassId = ''\n                this.getGoods()\n            } else {\n                this.gClassId = id\n                this.getGoods()\n            }\n        },\n\n        // 店铺信息\n        async getShopData() {\n            const\n                shopData = await this.$get(\"shop/getShopInfo\", {\n                    params: {\n                        shop_id: this.$route.query.id\n                    },\n                });\n\n            if (shopData.code == 1) {\n\n                this.shopInfo = shopData.data\n                // 切割推荐列表\n                if (shopData.data.goods_list.length > 0) {\n                    var num = [];\n                    for (var i = 0; i < Math.ceil(shopData.data.goods_list.length / 4); i++) {\n                        var start = i * 4;\n                        var end = start + 4;\n                        num.push(shopData.data.goods_list.slice(start, end));\n                    }\n                }\n\n                console.log('num', num)\n                this.recommend = num\n            }\n        },\n\n\n        // 关注店铺\n        shopFollow() {\n            const a = this.$post(\"shop_follow/changeStatus\", {\n                shop_id: this.$route.query.id\n            });\n            this.getShopData()\n        },\n\n        // 领取优惠券\n        async hqCoupon(id) {\n            const {\n                msg,\n                code\n            } = await this.$post(\"coupon/getCoupon\", {\n                coupon_id: id,\n            });\n\n            if (code == 1) {\n                this.$message({\n                    message: msg,\n                    type: \"success\",\n                });\n            }\n            this.getCouponList()\n        },\n\n        // 获取优惠券列表\n        async getCouponList() {\n            const\n                coupon = await this.$get(\"coupon/getCouponList\", {\n                    params: {\n                        shop_id: this.$route.query.id\n                    },\n                });\n        },\n\n\n        // 点击商品分类\n        getClassGoods(id) {\n            this.gClassId = id\n\n            if (id == '') {\n                this.xuanIndex = 'all'\n            } else {\n                this.xuanIndex = id\n            }\n\n            this.getGoods()\n        },\n\n        changeSortType(type) {\n            this.sortType = type;\n            switch (type) {\n                case \"price\":\n                    if (this.priceSort == \"asc\") {\n                        this.priceSort = \"desc\";\n                    } else if (this.priceSort == \"desc\") {\n                        this.priceSort = \"asc\";\n                    }\n                    break;\n                case \"sales_sum\":\n                    if (this.saleSort == \"asc\") {\n                        this.saleSort = \"desc\";\n                    } else if (this.saleSort == \"desc\") {\n                        this.saleSort = \"asc\";\n                    }\n                    break;\n                default:\n            }\n            this.getGoods();\n        },\n        changePage(current) {\n            this.page = current;\n            this.getGoods();\n        },\n        async getGoods() {\n            const {\n                name\n            } = this.$route.query;\n            const {\n                priceSort,\n                sortType,\n                saleSort\n            } = this;\n            let sort = \"\";\n            switch (sortType) {\n                case \"price\":\n                    sort = priceSort;\n                    break;\n                case \"sales_sum\":\n                    sort = saleSort;\n                    break;\n            }\n            const {\n                data: {\n                    list,\n                    count\n                },\n            } = await this.$get(\"pc/goodsList\", {\n                params: {\n                    page_size: 20,\n                    page_no: this.page,\n                    sort_type: sortType,\n                    sort,\n                    category_id: this.gClassId,\n                    shop_id: this.$route.query.id,\n                    name: this.keyword,\n                },\n            });\n            this.count = count\n            this.goodsList = list;\n        },\n    },\n}\n", "import mod from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./shop_street_detail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./shop_street_detail.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./shop_street_detail.vue?vue&type=template&id=1bbb4a36&\"\nimport script from \"./shop_street_detail.vue?vue&type=script&lang=js&\"\nexport * from \"./shop_street_detail.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./shop_street_detail.vue?vue&type=style&index=0&lang=scss&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  null,\n  \"3b0bc896\"\n  \n)\n\nexport default component.exports\n\n/* nuxt-component-imports */\ninstallComponents(component, {PriceFormate: require('/Users/<USER>/Desktop/vue/pc/components/price-formate.vue').default,GoodsList: require('/Users/<USER>/Desktop/vue/pc/components/goods-list.vue').default,NullData: require('/Users/<USER>/Desktop/vue/pc/components/null-data.vue').default})\n"], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACXA;AACA;AACA;AACA;AACA;AACA;;;;;ACLA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AADA;AAGA;AACA;AAAA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AADA;AAGA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AA5BA;AACA;AAgCA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAJA;AAKA;AACA;AACA;AAAA;AAAA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAZA;AAhDA;;AC1BA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACvBA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACNA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACXA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACNA;AACA;AACA;;;;;ACFA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AADA;AAGA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AARA;AAaA;AAfA;;ACRA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACtBA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AAEA;AACA;AACA;AAAA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AAAA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAIA;AACA;AACA;AACA;AACA;AACA;;;;;;;AC1DA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACXA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACNA;AACA;AACA;;;;;ACFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AADA;AAGA;AACA;AADA;AAZA;AAgBA;AACA;AACA;AACA;AAFA;AADA;AAMA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AAAA;AACA;AANA;AAQA;AACA;AAXA;AAv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cA;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA;AA/BA;AAiCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AADA;AADA;AACA;AAKA;AAEA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AADA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAFA;AAIA;AADA;AACA;AAGA;AACA;AACA;AACA;AAFA;AAIA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AADA;AADA;AAKA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAfA;AACA;AAgBA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AADA;AAGA;AACA;AACA;AACA;AAHA;AAKA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AANA;AACA;AAOA;AACA;AACA;AACA;AAFA;AADA;AAMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAPA;AADA;AAWA;AACA;AACA;AACA;AArKA;AAnHA;;ACpPA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;A", "sourceRoot": ""}