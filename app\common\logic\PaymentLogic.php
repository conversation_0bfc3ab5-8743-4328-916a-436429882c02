<?php


namespace app\common\logic;

use think\facade\Env;

class PaymentLogic
{

    protected static $error = '';
    protected static $return_code = 0;

    /**
     * Notes: 错误信息
     * @return string
     * <AUTHOR> 11:19)
     */
    public static function getError()
    {
        return self::$error;
    }

    /**
     * Notes: 返回状态码
     * @return int
     * <AUTHOR> 11:19)
     */
    public static function getReturnCode()
    {
        return self::$return_code;
    }


}