{layout name="layout1" /}
<style>
    .layui-table-cell {
        height:auto;
    }
    .goods-content>div:not(:last-of-type) {
        border-bottom:1px solid #DCDCDC;
    }
    .goods-data::after{
        display: block;
        content: '';
        clear: both;
    }
    .goods_name_hide{
        overflow:hidden;
        white-space:nowrap;
        text-overflow: ellipsis;
    }
    .operation-btn {
        margin: 5px;
    }
    .table-operate{
        text-align: left;
        font-size:14px;
        padding:0 5px;
        height:auto;
        overflow:visible;
        text-overflow:inherit;
        white-space:normal;
        word-break: break-all;
    }
</style>

<div class="wrapper">
    <div class="layui-card">
        <div class="layui-card-body">
            <div class="layui-collapse like-layui-collapse" lay-accordion="" style="border:1px dashed #c4c4c4">
                <div class="layui-colla-item">
                    <h2 class="layui-colla-title like-layui-colla-title" style="background-color: #fff">操作提示</h2>
                    <div class="layui-colla-content layui-show">
                        <p>*购买广告位，购买想要的广告位后可配置广告信息。</p>
                        <p>*广告位到期之后，商城不展示该广告。</p>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-tab layui-tab-card" lay-filter="tab-all">

<!--            <div class="layui-tab-item layui-show">-->
<!--                <div class="layui-card">-->
<!--                    <div class="layui-card-body">-->
<!--                        <div style="padding-bottom: 10px;">-->
<!--                            <button class="layui-btn layui-btn-sm layui-bg-blue add-ad">新增广告</button>-->
<!--                        </div>-->
<!--                    </div>-->
<!--                </div>-->
<!--            </div>-->
            <div class="layui-tab-item layui-show">
                <div class="layui-card">
                    <div class="layui-card-body">
                        <table id="ad-lists" lay-filter="ad-lists"></table>

                        <script type="text/html" id="operation">
                            <div style="text-align: left;margin-left: 10px">
                                {{#  if(d.buy_status == 1){ }}
                                   <a class="layui-btn layui-btn-sm layui-bg-blue" lay-event="edit">购买</a>
                                {{#  } else { }}
                                   <a class="layui-btn layui-btn-sm layui-bg-red" lay-event="">已售罄</a>
                                {{#  } }}
                            </div>
                        </script>

                        <script type="text/html" id="image">
                            <img src="{{d.image}}" style="height:80px;width: 80px" class="image-show">
                        </script>

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    layui.config({
        version:"{$front_version}",
        base: '/static/lib/' //静态资源所在路径
    }).use(['form'], function(){
        var $ = layui.$
            , form = layui.form
            , table = layui.table
            , element = layui.element
            , laydate = layui.laydate;

        //图片放大
        $(document).on('click', '.image-show', function () {
            var src = $(this).attr('src');
            like.showImg(src,600);
        });

        //获取列表
        getList('');

        function getList(type) {
            table.render({

                elem: '#ad-lists'
                , url: '{:url("decoration.ad/buylists")}'
                , cols: [[
                    {field: 'name', title: '广告位', align: 'center',width:160}
                    , {field: 'ad_nums', title: '广告位数量', align: 'center',width:100}
                    , {field: 'have_nums', title: '剩余数量', align: 'center',width:100}
                    , {field: 'image', title: '位置', templet:'#image',width:160}
                    , {field: 'ad_fee', title: '广告费用', align: 'center',width:120}
                    , {field: 'cycle', title: '广告周期', align: 'center',width:120}
                    , {field: 'status_name', title: '状态', align: 'center',width:100}
                    , {fixed: 'right', title: '操作', width: 100, align: 'center', toolbar: '#operation'}
                ]]
                , page: true
                , text: {none: '暂无数据！'}
                ,response: {
                    statusCode: 1
                }
                , parseData: function (res) {
                    return {
                        "code": res.code,
                        "msg": res.msg,
                        "count": res.data.count,
                        "data": res.data.lists,
                    };
                }
                ,done: function(res, curr, count){
                    // 解决操作栏因为内容过多换行问题
                    $(".layui-table-main tr").each(function (index, val) {
                        $($(".layui-table-fixed-l .layui-table-body tbody tr")[index]).height($(val).height());
                        $($(".layui-table-fixed-r .layui-table-body tbody tr")[index]).height($(val).height());
                    });
                }
            });
        }
        //监听工具条
        table.on('tool(ad-lists)', function (obj) {
            var id = obj.data.id;
            if (obj.event === 'status') {
                like.ajax({
                    url: '{:url("decoration.ad/status")}?id=' + obj.data.id + '&status=0',
                    data: {},
                    type: "post",
                    success: function (res) {
                        if (res.code == 1) {
                            layui.layer.msg(res.msg, {
                                offset: '15px'
                                , icon: 1
                                , time: 1000
                            }, function () {
                                location.reload();
                            });
                        }
                    }
                });
            }
            if (obj.event === 'delete') {
                layer.confirm('确定删除?', {icon: 3, title:'提示'}, function(index) {
                    layer.close(index);
                    like.ajax({
                        url: '{:url("decoration.ad/shopdelete")}?id=' + obj.data.id,
                        data: {},
                        type: "post",
                        success: function (res) {
                            if (res.code == 1) {
                                layui.layer.msg(res.msg, {
                                    offset: '15px'
                                    , icon: 1
                                    , time: 1000
                                }, function () {
                                    location.reload();
                                });
                            }
                        }
                    });
                });
            }
            // 显示支付二维码的弹窗
            function showQRCode(qrCode) {
                layer.open({
                    type: 1,
                    title: '支付二维码',
                    area: ['100%', '100%'], // 弹窗大小
                    content: '<div style="text-align:center;padding:50px;"><img src="' + qrCode + '" style="width:250px;height:250px;"/><h2 class="layui-colla-title like-layui-colla-title" style="background-color: #fff">打开微信扫一扫完成支付</h2>  <p>商家购买广告位,5分钟未付款将删除订单</p></div>',
                    btn: ['确认支付', '取消'],
                    closeBtn: 0, // 不显示关闭按钮
                    yes: function (index, layero) {
                        // 用户点击“确认支付”后，可以在这里添加支付成功后的回调逻辑
                        // 注意：这里只是模拟用户确认支付，实际的支付成功回调应由支付平台通知
                        layer.msg('支付成功！');
                        // 刷新页面或执行其他操作...
                        location.reload(); // 示例：刷新页面
                        layer.close(index);
                    },
                    btn2: function (index, layero) {
                        // 用户点击“取消”
                        layer.close(index);
                    }
                });
            }
            // 轮询检查支付状态的函数
            function checkPaymentStatus(orderId) {
                $.ajax({
                    url: '/shop/store/getAdPayStatus', // 替换为实际的查询支付状态接口URL
                    type: 'GET',
                    data: {
                        id: orderId,
                        from:'trade'
                    },
                    success: function(response) {
                        if (response.code === 1 && response.data.status === 'paid') {
                            // 支付成功
                            layer.msg('支付成功！');
                            clearInterval(); // 停止轮询
                            location.reload();
                            // 执行其他支付成功后的逻辑...
                        } else if (response.code === 1 && response.data.status !== 'paid') {
                            // 支付未完成，继续轮询
                            console.log('支付未完成，继续轮询...');
                        } else {
                            // 查询支付状态失败
                            layer.msg('查询支付状态失败：' + response.msg);
                            clearInterval(); // 停止轮询
                        }
                    },
                    error: function() {
                        // 请求失败
                        layer.msg('查询支付状态请求异常');
                        clearInterval(); // 停止轮询
                    }
                });
            }
            if (obj.event === 'edit') {
                var index_add_edit = layer.open({
                    type: 2
                    , title: '购买广告'
                    , content: '{:url("decoration.ad/buyinfo")}?id=' + obj.data.id
                    , area: ['90%', '90%']
                    , btn: ['付款', '取消']
                    , closeBtn: 0
                    , yes: function (index, layero) {
                        var iframeWindow = window['layui-layer-iframe' + index]
                            , submitID = 'edit-submit'
                            , submit = layero.find('iframe').contents().find('#' + submitID);

                        //监听提交
                        iframeWindow.layui.form.on('submit(' + submitID + ')', function (data) {
                            var field = data.field; //获取提交的字段
                            like.ajax({
                                url: '{:url("decoration.ad/buyinfo")}?id=' + obj.data.id,
                                data: field,
                                type: "post",
                                success: function (res) {
                                    if (res.code == 1) {
                                        // 发起支付请求
                                        $.ajax({
                                            url: '/api/pay/unifiedpay', // 替换为实际的支付接口URL
                                            type: 'POST',
                                            data: {
                                                from: 'AdOrder', // 默认为1
                                                order_id: res.data.order_id, // 替换为实际的订单ID，或根据需要从页面或服务器获取
                                                pay_way: 1, // 默认为微信支付（PC端）
                                                client: 5 // 默认为微信支付（PC端）
                                            },
                                            success: function(response){
                                                if(response.code === 1){ // 假设支付接口返回code为1表示成功
                                                    // 显示支付二维码
                                                    // 开始轮询检查支付状态
                                                    var qrCode = response.data; // 假设返回的数据中包含base64编码的二维码
                                                    showQRCode(qrCode);
                                                    var pollInterval = setInterval(function() {
                                                        checkPaymentStatus(res.data.order_id);
                                                    }, 1000); // 每5秒轮询一次
                                                } else {
                                                    // 支付请求失败
                                                    layer.msg('支付请求失败：' + response.msg);
                                                }
                                            },
                                            error: function(){
                                                // 请求失败
                                                layer.msg('支付请求异常');
                                            }
                                        });
                                    }
                                }
                            });
                        });
                        submit.trigger('click');
                    }
                })
            }
        });
        // 新增
        $(document).on('click', '.add-ad', function () {
            var index_add_edit = layer.open({
                type: 2
                , title: '新增店铺商品分类'
                , content: '{:url("decoration.ad/add")}'
                , area: ['90%', '90%']
                , btn: ['确认', '返回']
                , yes: function (index, layero) {
                    var iframeWindow = window['layui-layer-iframe' + index]
                        , submitID = 'add-submit'
                        , submit = layero.find('iframe').contents().find('#' + submitID);
                    //监听提交
                    iframeWindow.layui.form.on('submit(' + submitID + ')', function (data) {
                        var field = data.field;
                        console.log(data.field);
                        like.ajax({
                            url: '{:url("decoration.ad/add")}',
                            data: field,
                            type: "post",
                            success: function (res) {
                                if (res.code == 1) {
                                    layui.layer.msg(res.msg, {
                                        offset: '15px'
                                        , icon: 1
                                        , time: 1000
                                    });
                                    layer.close(index); //关闭弹层
                                    location.reload();//刷新
                                }
                            }
                        });
                    });
                    // 触发子窗口表单提交事件
                    submit.trigger('click');
                }
            })
        });
    });
</script>