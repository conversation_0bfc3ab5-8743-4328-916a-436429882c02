(window.webpackJsonp=window.webpackJsonp||[]).push([[27,8,9,16,17,18],{437:function(t,e,o){"use strict";var n=o(17),r=o(2),c=o(3),l=o(136),d=o(27),m=o(18),f=o(271),h=o(52),v=o(135),x=o(270),_=o(5),w=o(98).f,y=o(44).f,C=o(26).f,A=o(438),k=o(439).trim,S="Number",V=r.Number,D=V.prototype,N=r.TypeError,E=c("".slice),I=c("".charCodeAt),O=function(t){var e=x(t,"number");return"bigint"==typeof e?e:z(e)},z=function(t){var e,o,n,r,c,l,d,code,m=x(t,"number");if(v(m))throw N("Cannot convert a Symbol value to a number");if("string"==typeof m&&m.length>2)if(m=k(m),43===(e=I(m,0))||45===e){if(88===(o=I(m,2))||120===o)return NaN}else if(48===e){switch(I(m,1)){case 66:case 98:n=2,r=49;break;case 79:case 111:n=8,r=55;break;default:return+m}for(l=(c=E(m,2)).length,d=0;d<l;d++)if((code=I(c,d))<48||code>r)return NaN;return parseInt(c,n)}return+m};if(l(S,!V(" 0o1")||!V("0b1")||V("+0x1"))){for(var T,F=function(t){var e=arguments.length<1?0:V(O(t)),o=this;return h(D,o)&&_((function(){A(o)}))?f(Object(e),o,F):e},U=n?w(V):"MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,EPSILON,MAX_SAFE_INTEGER,MIN_SAFE_INTEGER,isFinite,isInteger,isNaN,isSafeInteger,parseFloat,parseInt,fromString,range".split(","),j=0;U.length>j;j++)m(V,T=U[j])&&!m(F,T)&&C(F,T,y(V,T));F.prototype=D,D.constructor=F,d(r,S,F)}},438:function(t,e,o){var n=o(3);t.exports=n(1..valueOf)},439:function(t,e,o){var n=o(3),r=o(33),c=o(16),l=o(440),d=n("".replace),m="["+l+"]",f=RegExp("^"+m+m+"*"),h=RegExp(m+m+"*$"),v=function(t){return function(e){var o=c(r(e));return 1&t&&(o=d(o,f,"")),2&t&&(o=d(o,h,"")),o}};t.exports={start:v(1),end:v(2),trim:v(3)}},440:function(t,e){t.exports="\t\n\v\f\r                　\u2028\u2029\ufeff"},441:function(t,e,o){var content=o(444);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,o(14).default)("3181fc86",content,!0,{sourceMap:!1})},442:function(t,e,o){"use strict";o.r(e);o(437),o(80),o(272);var n={data:function(){return{priceSlice:{}}},components:{},props:{firstSize:{type:Number,default:14},secondSize:{type:Number,default:14},color:{type:String},weight:{type:[String,Number],default:400},price:{type:[String,Number],default:""},showSubscript:{type:Boolean,default:!0},subscriptSize:{type:Number,default:14},lineThrough:{type:Boolean,default:!1}},created:function(){this.priceFormat()},watch:{price:function(t){this.priceFormat()}},methods:{priceFormat:function(){var t=this.price,e={};null!==t&&(t=parseFloat(t),t=String(t).split("."),e.first=t[0],e.second=t[1],this.priceSlice=e)}}},r=(o(443),o(9)),component=Object(r.a)(n,(function(){var t=this,e=t.$createElement,o=t._self._c||e;return o("span",{class:(t.lineThrough?"line-through":"")+"price-format",style:{color:t.color,"font-weight":t.weight}},[t.showSubscript?o("span",{style:{"font-size":t.subscriptSize+"px","margin-right":"1px"}},[t._v("¥")]):t._e(),t._v(" "),o("span",{style:{"font-size":t.firstSize+"px","margin-right":"1px"}},[t._v(t._s(t.priceSlice.first))]),t._v(" "),t.priceSlice.second?o("span",{style:{"font-size":t.secondSize+"px"}},[t._v("."+t._s(t.priceSlice.second))]):t._e()])}),[],!1,null,null,null);e.default=component.exports},443:function(t,e,o){"use strict";o(441)},444:function(t,e,o){var n=o(13)(!1);n.push([t.i,".price-format{display:flex;align-items:baseline}",""]),t.exports=n},445:function(t,e,o){var content=o(447);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,o(14).default)("12a18d22",content,!0,{sourceMap:!1})},446:function(t,e,o){"use strict";o(445)},447:function(t,e,o){var n=o(13)(!1);n.push([t.i,".null-data[data-v-93598fb0]{padding:100px}.null-data .img-null[data-v-93598fb0]{width:150px;height:150px}",""]),t.exports=n},448:function(t,e,o){"use strict";o.r(e);var n={components:{},props:{img:{type:String},text:{type:String,default:"暂无数据"},imgStyle:{type:String,default:""}},methods:{}},r=(o(446),o(9)),component=Object(r.a)(n,(function(){var t=this,e=t.$createElement,o=t._self._c||e;return o("div",{staticClass:"bg-white flex-col col-center null-data"},[o("img",{staticClass:"img-null",style:t.imgStyle,attrs:{src:t.img,alt:""}}),t._v(" "),o("div",{staticClass:"muted mt8"},[t._v(t._s(t.text))])])}),[],!1,null,"93598fb0",null);e.default=component.exports},449:function(t,e,o){"use strict";o.r(e);o(437),o(81),o(61),o(24),o(100),o(80),o(99);var n=6e4,r=36e5,c=24*r;function l(t){return(0+t.toString()).slice(-2)}var d={components:{},props:{isSlot:{type:Boolean,default:!1},time:{type:Number,default:0},format:{type:String,default:"hh:mm:ss"},autoStart:{type:Boolean,default:!0}},watch:{time:{immediate:!0,handler:function(t){t&&this.reset()}}},data:function(){return{timeObj:{},formateTime:0}},created:function(){},computed:{},methods:{createTimer:function(t){return setTimeout(t,100)},isSameSecond:function(t,e){return Math.floor(t)===Math.floor(e)},start:function(){this.counting||(this.counting=!0,this.endTime=Date.now()+1e3*this.remain,this.setTimer())},setTimer:function(){var t=this;this.tid=this.createTimer((function(){var e=t.getRemain();t.isSameSecond(e,t.remain)&&0!==e||t.setRemain(e),0!==t.remain&&t.setTimer()}))},getRemain:function(){return Math.max(this.endTime-Date.now(),0)},pause:function(){this.counting=!1,clearTimeout(this.tid)},reset:function(){this.pause(),this.remain=this.time,this.setRemain(this.remain),this.autoStart&&this.start()},setRemain:function(t){var e=this.format;this.remain=t;var time,o=(time=t,{days:Math.floor(time/c),hours:l(Math.floor(time%c/r)),minutes:l(Math.floor(time%r/n)),seconds:l(Math.floor(time%n/1e3))});this.formateTime=function(t,e){var o=e.days,n=e.hours,r=e.minutes,c=e.seconds;return-1!==t.indexOf("dd")&&(t=t.replace("dd",o)),-1!==t.indexOf("hh")&&(t=t.replace("hh",l(n))),-1!==t.indexOf("mm")&&(t=t.replace("mm",l(r))),-1!==t.indexOf("ss")&&(t=t.replace("ss",l(c))),t}(e,o),this.$emit("change",o),0===t&&(this.pause(),this.$emit("finish"))}}},m=o(9),component=Object(m.a)(d,(function(){var t=this,e=t.$createElement,o=t._self._c||e;return t.time>=0?o("div",[o("client-only",[t.isSlot?t._t("default"):o("span",[t._v(t._s(t.formateTime))])],2)],1):t._e()}),[],!1,null,null,null);e.default=component.exports},468:function(t,e,o){t.exports=o.p+"img/news_null.856b3f3.png"},469:function(t,e,o){"use strict";var n=o(7),r=o(102).findIndex,c=o(186),l="findIndex",d=!0;l in[]&&Array(1).findIndex((function(){d=!1})),n({target:"Array",proto:!0,forced:d},{findIndex:function(t){return r(this,t,arguments.length>1?arguments[1]:void 0)}}),c(l)},472:function(t,e,o){var content=o(485);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,o(14).default)("663bee12",content,!0,{sourceMap:!1})},479:function(t,e,o){"use strict";o.d(e,"a",(function(){return l}));var n=o(137);var r=o(189),c=o(103);function l(t){return function(t){if(Array.isArray(t))return Object(n.a)(t)}(t)||Object(r.a)(t)||Object(c.a)(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}},483:function(t,e,o){"use strict";var n=o(7),r=o(2),c=o(3),l=o(66),d=o(438),m=o(273),f=o(5),h=r.RangeError,v=r.String,x=Math.floor,_=c(m),w=c("".slice),y=c(1..toFixed),C=function(t,e,o){return 0===e?o:e%2==1?C(t,e-1,o*t):C(t*t,e/2,o)},A=function(data,t,e){for(var o=-1,n=e;++o<6;)n+=t*data[o],data[o]=n%1e7,n=x(n/1e7)},k=function(data,t){for(var e=6,o=0;--e>=0;)o+=data[e],data[e]=x(o/t),o=o%t*1e7},S=function(data){for(var t=6,s="";--t>=0;)if(""!==s||0===t||0!==data[t]){var e=v(data[t]);s=""===s?e:s+_("0",7-e.length)+e}return s};n({target:"Number",proto:!0,forced:f((function(){return"0.000"!==y(8e-5,3)||"1"!==y(.9,0)||"1.25"!==y(1.255,2)||"1000000000000000128"!==y(0xde0b6b3a7640080,0)}))||!f((function(){y({})}))},{toFixed:function(t){var e,o,n,r,c=d(this),m=l(t),data=[0,0,0,0,0,0],f="",x="0";if(m<0||m>20)throw h("Incorrect fraction digits");if(c!=c)return"NaN";if(c<=-1e21||c>=1e21)return v(c);if(c<0&&(f="-",c=-c),c>1e-21)if(o=(e=function(t){for(var e=0,o=t;o>=4096;)e+=12,o/=4096;for(;o>=2;)e+=1,o/=2;return e}(c*C(2,69,1))-69)<0?c*C(2,-e,1):c/C(2,e,1),o*=4503599627370496,(e=52-e)>0){for(A(data,0,o),n=m;n>=7;)A(data,1e7,0),n-=7;for(A(data,C(10,n,1),0),n=e-1;n>=23;)k(data,1<<23),n-=23;k(data,1<<n),A(data,1,1),k(data,2),x=S(data)}else A(data,0,o),A(data,1<<-e,0),x=S(data)+_("0",m);return x=m>0?f+((r=x.length)<=m?"0."+_("0",m-r)+x:w(x,0,r-m)+"."+w(x,r-m)):f+x}})},484:function(t,e,o){"use strict";o(472)},485:function(t,e,o){var n=o(13)(!1);n.push([t.i,".number-box[data-v-1d9d8f36]{display:inline-flex;align-items:center}.number-box .number-input[data-v-1d9d8f36]{position:relative;text-align:center;padding:0;margin:0 6px;align-items:center;justify-content:center}.number-box .minus[data-v-1d9d8f36],.number-box .plus[data-v-1d9d8f36]{width:32px;display:flex;justify-content:center;align-items:center;cursor:pointer}.number-box .plus[data-v-1d9d8f36]{border-radius:0 2px 2px 0}.number-box .minus[data-v-1d9d8f36]{border-radius:2px 0 0 2px}.number-box .disabled[data-v-1d9d8f36]{color:#c8c9cc!important;background:#f7f8fa!important}.number-box .input-disabled[data-v-1d9d8f36]{color:#c8c9cc!important;background-color:#f2f3f5!important}",""]),t.exports=n},494:function(t,e,o){var content=o(513);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,o(14).default)("23143360",content,!0,{sourceMap:!1})},499:function(t,e,o){"use strict";o.r(e);o(437),o(80),o(272),o(24),o(100),o(483),o(81);var n={components:{},props:{value:{type:Number,default:1},bgColor:{type:String,default:" #F2F3F5"},min:{type:Number,default:0},max:{type:Number,default:99999},step:{type:Number,default:1},disabled:{type:Boolean,default:!1},size:{type:[Number,String],default:14},inputWidth:{type:[Number,String],default:64},color:{type:String,default:"#333"},inputHeight:{type:[Number,String],default:32},index:{type:[Number,String],default:""},disabledInput:{type:Boolean,default:!1},positiveInteger:{type:Boolean,default:!0},asyncChange:{type:Boolean,default:!1}},watch:{value:function(t,e){this.changeFromInner||(this.inputVal=t,this.$nextTick((function(){this.changeFromInner=!1})))},inputVal:function(t,e){var o=this;if(""!=t){var n=0;n=/^(?:-?\d+|-?\d{1,3}(?:,\d{3})+)?(?:\.\d+)?$/.test(t)&&t>=this.min&&t<=this.max?t:e,this.positiveInteger&&(t<0||-1!==String(t).indexOf("."))&&(n=e,this.$nextTick((function(){o.inputVal=e}))),this.asyncChange||this.handleChange(n,"change")}}},data:function(){return{inputVal:1,timer:null,changeFromInner:!1,innerChangeTimer:null}},created:function(){this.inputVal=Number(this.value)},computed:{},methods:{btnTouchStart:function(t){this[t]()},minus:function(){this.computeVal("minus")},plus:function(){this.computeVal("plus")},calcPlus:function(t,e){var o,n,r;try{n=t.toString().split(".")[1].length}catch(t){n=0}try{r=e.toString().split(".")[1].length}catch(t){r=0}return((t*(o=Math.pow(10,Math.max(n,r)))+e*o)/o).toFixed(n>=r?n:r)},calcMinus:function(t,e){var o,n,r;try{n=t.toString().split(".")[1].length}catch(t){n=0}try{r=e.toString().split(".")[1].length}catch(t){r=0}return((t*(o=Math.pow(10,Math.max(n,r)))-e*o)/o).toFixed(n>=r?n:r)},computeVal:function(t){if(!this.disabled){var e=0;"minus"===t?e=this.calcMinus(this.inputVal,this.step):"plus"===t&&(e=this.calcPlus(this.inputVal,this.step)),e<this.min||e>this.max||(this.asyncChange?this.$emit("change",e):(this.inputVal=e,this.handleChange(e,t)))}},onBlur:function(t){var e=this,o=0,n=t.target.value;console.log(n),(o=/(^\d+$)/.test(n)?+n:this.min)>this.max?o=this.max:o<this.min&&(o=this.min),this.$nextTick((function(){e.inputVal=o})),this.handleChange(o,"blur")},onFocus:function(){this.$emit("focus")},handleChange:function(t,e){var o=this;this.disabled||(this.innerChangeTimer&&(clearTimeout(this.innerChangeTimer),this.innerChangeTimer=null),this.changeFromInner=!0,this.innerChangeTimer=setTimeout((function(){o.changeFromInner=!1}),150),this.$emit("input",Number(t)),this.$emit(e,{value:Number(t),index:this.index}))}}},r=(o(484),o(9)),component=Object(r.a)(n,(function(){var t=this,e=t.$createElement,o=t._self._c||e;return o("div",{staticClass:"number-box"},[o("div",{class:{minus:!0,disabled:t.disabled||t.inputVal<=t.min},style:{background:t.bgColor,height:t.inputHeight+"px",color:t.color},on:{click:function(e){return e.stopPropagation(),e.preventDefault(),t.btnTouchStart("minus")}}},[o("div",{style:{fontSize:t.size+"px"}},[t._v("-")])]),t._v(" "),o("input",{directives:[{name:"model",rawName:"v-model",value:t.inputVal,expression:"inputVal"}],class:{"number-input":!0,"input-disabled":t.disabled},style:{color:t.color,fontSize:t.size+"px",background:t.bgColor,height:t.inputHeight+"px",width:t.inputWidth+"px"},attrs:{disabled:t.disabledInput||t.disabled,type:"text"},domProps:{value:t.inputVal},on:{blur:t.onBlur,focus:t.onFocus,input:function(e){e.target.composing||(t.inputVal=e.target.value)}}}),t._v(" "),o("div",{staticClass:"plus",class:{disabled:t.disabled||t.inputVal>=t.max},style:{background:t.bgColor,height:t.inputHeight+"px",color:t.color},on:{click:function(e){return e.stopPropagation(),e.preventDefault(),t.btnTouchStart("plus")}}},[o("div",{style:{fontSize:t.size+"px"}},[t._v("+")])])])}),[],!1,null,"1d9d8f36",null);e.default=component.exports},512:function(t,e,o){"use strict";o(494)},513:function(t,e,o){var n=o(13)(!1);n.push([t.i,".comment-list .comment-con>.item[data-v-4e1720b8]{padding:20px;border-bottom:1px dashed #e5e5e5;align-items:flex-start}.comment-list .comment-con>.item .avatar img[data-v-4e1720b8]{border-radius:50%;width:44px;height:44px}.comment-list .comment-con>.item .comment-imglist[data-v-4e1720b8]{margin-top:10px}.comment-list .comment-con>.item .comment-imglist .item[data-v-4e1720b8]{width:80px;height:80px;margin-right:6px}.comment-list .comment-con>.item .reply[data-v-4e1720b8]{background-color:#f2f2f2;align-items:flex-start;padding:10px}",""]),t.exports=n},553:function(t,e,o){var content=o(643);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,o(14).default)("6ab4852a",content,!0,{sourceMap:!1})},559:function(t,e,o){"use strict";o.r(e);var n=o(6),r=(o(51),o(437),{components:{},props:{list:{type:Array,default:function(){return[]}},type:Number,goodsId:[String,Number]},data:function(){return{commentList:[],count:0,page:1}},created:function(){this.getCommentList()},methods:{getCommentList:function(){var t=this;return Object(n.a)(regeneratorRuntime.mark((function e(){var o,data;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,t.$get("goods_comment/lists",{params:{type:t.type,goods_id:t.goodsId,page_size:10,page_no:t.page}});case 2:o=e.sent,data=o.data,1==o.code&&(t.commentList=data.lists,t.count=data.count);case 6:case"end":return e.stop()}}),e)})))()},changePage:function(t){this.page=t,this.getCommentList()}}}),c=(o(512),o(9)),component=Object(c.a)(r,(function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"comment-list"},[n("div",{staticClass:"comment-con"},[t.commentList.length?[t._l(t.commentList,(function(e,o){return n("div",{key:o,staticClass:"item flex"},[n("div",{staticClass:"avatar m-r-8"},[n("img",{attrs:{src:e.avatar,alt:""}})]),t._v(" "),n("div",{staticClass:"content flex-1"},[n("div",[t._v(t._s(e.nickname))]),t._v(" "),n("div",{staticClass:"lighter",staticStyle:{margin:"5px 0 10px"}},[n("span",[t._v(t._s(e.create_time))]),t._v(" "),n("span",[t._v("|")]),t._v(" "),n("span",[t._v("规格："+t._s(e.spec_value_str))])]),t._v(" "),n("div",[t._v("\n                        "+t._s(e.comment)+"\n                    ")]),t._v(" "),n("div",{staticClass:"comment-imglist flex"},t._l(e.image,(function(img,t){return n("div",{key:t,staticClass:"item"},[n("el-image",{staticStyle:{height:"100%",width:"100%"},attrs:{"preview-src-list":e.image,src:img,fit:"contain"}})],1)})),0),t._v(" "),e.reply?n("div",{staticClass:"flex reply m-t-16"},[n("div",{staticClass:"primary flex-none"},[t._v("商家回复：")]),t._v(" "),n("div",{staticClass:"lighter"},[t._v("\n                            "+t._s(e.reply)+"\n                        ")])]):t._e()])])})),t._v(" "),t.count?n("div",{staticClass:"pagination flex row-center",staticStyle:{padding:"38px 0"}},[n("el-pagination",{attrs:{background:"","hide-on-single-page":"",layout:"prev, pager, next",total:t.count,"page-size":10},on:{"current-change":t.changePage}})],1):t._e()]:n("null-data",{attrs:{img:o(468),text:"暂无评价~"}})],2)])}),[],!1,null,"4e1720b8",null);e.default=component.exports;installComponents(component,{NullData:o(448).default})},640:function(t,e){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACUAAAAkCAMAAAA5HAOUAAAAQlBMVEUAAAD/IDD/KDj/Kjr/LDz/KTn/Kzv/Kjr/Kzv/LDz/Kzv/Kzv/Kzv/LDz/Kzv/LDz/LDz/Kzv/Kzv/LDz/LDv/LDyPingBAAAAFXRSTlMAECAwQFBfYHCAj5+gr7C/wNDf7/B6g4n4AAAAvUlEQVQ4y8XUyRKDIBAEUBZlUYxs8/+/mmiMWtQwkFzS51cFtF0y9v9w3oE0gG4iCa/Illo3tTaQgT2Gvnl6q0S+YIEjC4EGODPUz4uXiviZQk0JbkmTEkVJao6AJM7qrM4kIJLM1TYV2a+Yp5E/CggUCp9KeK6jfPUmqyzfRzTW1FguFEu5WochR8yBGEafspgyXcr+ph5db/TEh0aU19o3VHb71oXLuNq6D/ocANcBuxcztviHSGu+/Kc9AXSSLqTq6c2LAAAAAElFTkSuQmCC"},641:function(t,e){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACUAAAAkCAMAAAA5HAOUAAAAS1BMVEUAAABQUFBQUFBVVVVUVFRTU1NTU1NVVVVUVFRUVFRUVFRVVVVVVVVUVFRVVVVUVFRUVFRVVVVVVVVVVVVVVVVUVFRUVFRVVVVVVVUmEHPwAAAAGHRSTlMAECAwQFBfYHCAj5CfoK+wv8DP0N/g7/AGrtdjAAABEUlEQVQ4y8WUy5aDIBBEeUQeUVTUwP3/L53FaJIR1MxsxhX2udBdRakQ//9I+QFkwV5CGkBfUSNty3gBOR5SZtz55IlGiIZ0qqBnEEKISH8C3chKCCFU5nbcb9kG8iz1nsrcE/P2NpPuRu1MMt0CEJ8HyAiwdOZpnUsAefA/zNR+yADJbW4/gqvard3wWG9Ck9SxbJXW+4pMhybKibiuZqYjamLeTpCZrg515FcbnfE1yJPfVTXV6FlodoVSqErF1lD29IQyDnFfimUwPqM87b7UlsH2tbn+WBpW1dL0vZGrO6E+qu4SQOrUsSAzAtHaCIymTvUJcvj+hkKG1JdUAGb7yr2doZxLOL8Ltfbul/+0Lw1XEXqaPu71AAAAAElFTkSuQmCC"},642:function(t,e,o){"use strict";o(553)},643:function(t,e,o){var n=o(13)(!1);n.push([t.i,".goods-details{padding:16px 0 44px}.goods-details .goods-info .goods-swiper{width:400px;border-radius:4px}.goods-details .goods-info .goods-swiper .swiper{margin:10px 0;padding:0 25px;--swiper-navigation-size:15px;--swiper-navigation-color:#888}.goods-details .goods-info .goods-swiper .swiper .swiper-button-next,.goods-details .goods-info .goods-swiper .swiper .swiper-button-prev{top:0;width:25px;height:100%;margin-top:0;background-size:12px 22px}.goods-details .goods-info .goods-swiper .swiper .swiper-button-prev{left:0}.goods-details .goods-info .goods-swiper .swiper .swiper-button-next{right:0}.goods-details .goods-info .goods-swiper .swiper .swiper-item{cursor:pointer;height:66px;width:66px;border:2px solid transparent}.goods-details .goods-info .goods-swiper .swiper .swiper-item~.swiper-item{margin-left:10px}.goods-details .goods-info .goods-swiper .swiper .swiper-item.active{border-color:#ff2c3c}.goods-details .goods-info .goods-swiper .current-img{width:100%;height:400px}.goods-details .goods-info .info-wrap{min-height:486px;border-radius:4px;padding:20px}.goods-details .goods-info .info-wrap .name{font-size:20px}.goods-details .goods-info .info-wrap .price-wrap{background:#f2f2f2;background-size:cover;height:80px;padding:0 50px 0 20px;margin-bottom:26px}.goods-details .goods-info .info-wrap .price-wrap.seckill{background:#ff2c3c}.goods-details .goods-info .info-wrap .price-wrap.seckill .count-down .item{width:30px;height:30px;background:rgba(0,0,0,.5);text-align:center;line-height:30px;border-radius:4px}.goods-details .goods-info .info-wrap .spec-wrap .spec{align-items:flex-start}.goods-details .goods-info .info-wrap .spec-wrap .spec .spec-name{margin-right:20px;margin-top:6px;flex:none}.goods-details .goods-info .info-wrap .spec-wrap .spec .spec-item{padding:0 20px;line-height:32px;border:1px solid hsla(0,0%,89.8%,.89804);border-radius:2px;margin-right:10px;margin-bottom:10px;cursor:pointer}.goods-details .goods-info .info-wrap .spec-wrap .spec .spec-item.active{color:#ff2c3c;background-color:#ffeeef;border-color:currentColor}.goods-details .goods-info .info-wrap .goods-num{margin-bottom:30px}.goods-details .goods-info .info-wrap .goods-num .num{margin-right:20px}.goods-details .goods-info .info-wrap .goods-btns .btn{margin-right:14px;text-align:center;width:120px;font-size:16px}.goods-details .goods-info .info-wrap .goods-btns .btn.collection{width:146px;line-height:42px;border:1px solid hsla(0,0%,89.8%,.89804);background-color:#fff;border-radius:4px;cursor:pointer;color:#666}.goods-details .goods-info .info-wrap .goods-btns .btn.collection:hover{color:#ff2c3c}.goods-details .goods-info .info-wrap .goods-btns .btn.collection .start-icon{width:18.5px;height:18px}.goods-details .goods-info .shop{width:210px;padding:16px}.goods-details .goods-info .shop .logo-img{width:62px;height:62px;border-radius:50%;overflow:hidden}.goods-details .goods-info .shop .el-rate__icon{font-size:16px}.goods-details .details-wrap{align-items:stretch}.goods-details .details-wrap .details{padding:10px 0;overflow:hidden}.goods-details .details-wrap .details .rich-text{padding:0 10px;width:100%;overflow:hidden}.goods-details .details-wrap .details .rich-text img{width:100%;display:block}.goods-details .details-wrap .details .rich-text p{margin:0}.goods-details .details-wrap .details .evaluation .evaluation-hd{height:80px;margin:0 10px}.goods-details .details-wrap .details .evaluation .evaluation-hd .rate{height:60px;width:220px;border-right:1px solid #e5e5e5;padding-left:10px;margin-right:40px}.goods-details .details-wrap .details .evaluation .evaluation-tab{margin:16px 20px}.goods-details .details-wrap .details .evaluation .evaluation-tab .item{border-radius:2px;cursor:pointer;height:32px;padding:6px 20px;color:#666;background-color:#f2f2f2;margin-right:10px}.goods-details .details-wrap .details .evaluation .evaluation-tab .item.active{color:#fff;background-color:#ff2c3c}.goods-details .goods-like{width:210px}.goods-details .goods-like .title{border-bottom:1px solid hsla(0,0%,89.8%,.89804);height:45px}.goods-details .goods-like .goods-list .item{padding:10px;display:block}.goods-details .goods-like .goods-list .item .goods-img{width:190px;height:190px;margin-bottom:10px}",""]),t.exports=n},681:function(t,e,o){"use strict";o.r(e);o(22),o(19),o(21),o(28),o(29);var n=o(479),r=o(10),c=o(6),l=(o(51),o(81),o(20),o(61),o(190),o(469),o(11));o(96);function d(object,t){var e=Object.keys(object);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(object);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(object,t).enumerable}))),e.push.apply(e,o)}return e}function m(t){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?d(Object(source),!0).forEach((function(e){Object(r.a)(t,e,source[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(source)):d(Object(source)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(source,e))}))}return t}var f={head:function(){return{title:this.$store.getters.headTitle,link:[{rel:"icon",type:"image/x-icon",href:this.$store.getters.favicon}]}},asyncData:function(t){return Object(c.a)(regeneratorRuntime.mark((function e(){var o,n,r,c,data,code;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return o=t.params,n=t.$get,r=t.app,e.next=3,n("goods/getGoodsDetail",{params:{goods_id:o.id}});case 3:return c=e.sent,data=c.data,code=c.code,c.msg,0==code&&setTimeout((function(){return r.router.back()}),1500),e.abrupt("return",{goodsDetails:data,goodsImage:data.goods_image,activity:data.activity,shop:data.shop});case 9:case"end":return e.stop()}}),e)})))()},data:function(){return{goodsDetails:{},goodsImage:[],activity:{},shop:{goods_list:[]},swiperOptions:{pagination:{el:".swiper-pagination",clickable:!0},navigation:{nextEl:".swiper-button-next",prevEl:".swiper-button-prev"},preventClicks:!0,slidesPerView:"auto"},active:"0",commentActive:0,swiperIndex:0,checkedGoods:{},comment:{},goodsNum:1,goodsSpec:[],id:"",timeData:{}}},created:function(){this.id=this.$route.params.id,this.getComment(this.id)},methods:m(m({},Object(l.b)(["getPublicData"])),{},{onClickSlide:function(t){this.swiperIndex=t},onChoseSpecItem:function(t,e){var o=this.goodsSpec;o.forEach((function(o){o.spec_value&&o.id==t&&o.spec_value.forEach((function(t){t.checked=0,t.id==e&&(t.checked=1)}))})),this.goodsSpec=Object(n.a)(o)},onAddCart:function(){var t=this;return Object(c.a)(regeneratorRuntime.mark((function e(){var o,n,r,code,c;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return o=t.goodsNum,n=t.checkedGoods.id,e.next=3,t.$post("cart/add",{item_id:n,goods_num:o});case 3:r=e.sent,code=r.code,r.data,c=r.msg,1==code&&(t.getPublicData(),t.$message({message:c,type:"success"}));case 8:case"end":return e.stop()}}),e)})))()},changeShopFollow:function(){var t=this;return Object(c.a)(regeneratorRuntime.mark((function e(){var o,code,n;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,t.$post("shop_follow/changeStatus",{shop_id:t.shop.id});case 2:o=e.sent,code=o.code,n=o.msg,1==code&&(t.$message({message:n,type:"success"}),t.getGoodsDetail());case 6:case"end":return e.stop()}}),e)})))()},onBuyNow:function(){var t=this.goodsNum,e=[{item_id:this.checkedGoods.id,num:t,goods_id:this.id,shop_id:this.shop.id}];this.$router.push({path:"/confirm_order",query:{data:encodeURIComponent(JSON.stringify({goods:e,type:"buy"}))}})},getGoodsDetail:function(){var t=this;return Object(c.a)(regeneratorRuntime.mark((function e(){var o,data;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,t.$get("goods/getGoodsDetail",{params:{goods_id:t.id}});case 2:o=e.sent,data=o.data,1==o.code&&(t.goodsDetails=data,t.shop=data.shop);case 6:case"end":return e.stop()}}),e)})))()},onCollectionGoods:function(){var t=this;return Object(c.a)(regeneratorRuntime.mark((function e(){var o,code,n;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,t.$post("goods_collect/changeStatus",{goods_id:t.id});case 2:o=e.sent,o.data,code=o.code,n=o.msg,1==code&&(t.$message({message:n,type:"success"}),t.getGoodsDetail());case 7:case"end":return e.stop()}}),e)})))()},getComment:function(){var t=this;return Object(c.a)(regeneratorRuntime.mark((function e(){var o,data;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,t.$get("/goods_comment/category",{params:{goods_id:t.id}});case 2:o=e.sent,data=o.data,1==o.code&&(t.comment=data,t.commentActive=data.comment[0].id);case 6:case"end":return e.stop()}}),e)})))()},onChangeDate:function(t){var e={};for(var o in t)"milliseconds"!==o&&(e[o]=("0"+t[o]).slice(-2));this.timeData=e}}),watch:{goodsSpec:{immediate:!0,handler:function(t){var e=this.goodsDetails.goods_item,o=[];if(t.forEach((function(t){t.spec_value&&t.spec_value.forEach((function(t){t.checked&&o.push(t.id)}))})),o.length){var n=o.join(","),r=e.findIndex((function(t){return t.spec_value_ids==n}));-1==r&&(r=0),this.checkedGoods=e[r],console.log(this.checkedGoods)}}},goodsDetails:{immediate:!0,handler:function(t){t.goods_spec&&(t.goods_spec.forEach((function(t){t.spec_value.forEach((function(t,e){t.checked=0==e?1:0}))})),this.goodsSpec=Object(n.a)(t.goods_spec))}}},computed:{countTime:function(){var t=this.activity.end_time;return t?t-Date.now()/1e3:0}}},h=(o(642),o(9)),component=Object(h.a)(f,(function(){var t=this,e=t.$createElement,n=t._self._c||e;return t.goodsDetails.id?n("div",{staticClass:"goods-details"},[n("div",{staticClass:"goods-info flex col-stretch"},[n("div",{staticClass:"goods-swiper m-r-16 bg-white flex-col"},[n("el-image",{staticClass:"current-img",attrs:{"preview-src-list":t.goodsImage.map((function(t){return t.uri})),src:t.goodsImage[t.swiperIndex].uri}}),t._v(" "),n("client-only",[n("swiper",{ref:"mySwiper",staticClass:"swiper",attrs:{options:t.swiperOptions}},[t._l(t.goodsImage,(function(e,o){return n("swiper-slide",{key:o,class:{"swiper-item":!0,active:o===t.swiperIndex}},[n("div",{staticStyle:{width:"100%",height:"100%"},on:{mouseover:function(e){t.swiperIndex=o}}},[n("el-image",{staticStyle:{width:"100%",height:"100%"},attrs:{src:e.uri}})],1)])})),t._v(" "),n("div",{staticClass:"swiper-button-prev",attrs:{slot:"button-prev"},slot:"button-prev"}),t._v(" "),n("div",{staticClass:"swiper-button-next",attrs:{slot:"button-next"},slot:"button-next"})],2)],1)],1),t._v(" "),n("div",{staticClass:"info-wrap bg-white flex-1"},[n("div",{staticClass:"name weight-500 m-b-16"},[t._v("\n                "+t._s(t.goodsDetails.name)+"\n            ")]),t._v(" "),1==t.activity.type?n("div",{staticClass:"price-wrap flex row-between white seckill"},[n("div",{staticClass:"price flex",staticStyle:{"align-items":"baseline"}},[n("div",{staticClass:"m-r-8"},[t._v("价格")]),t._v(" "),n("div",[n("price-formate",{attrs:{price:t.checkedGoods.price||t.goodsDetails.price,"subscript-size":16,"first-size":22,"second-size":16}})],1),t._v(" "),n("div",{staticClass:"line-through m-l-8 flex"},[t._v("\n                        原价\n                        "),n("price-formate",{attrs:{price:t.checkedGoods.market_price||t.goodsDetails.market_price}})],1)]),t._v(" "),n("div",{staticClass:"flex"},[n("div",{staticClass:"white m-r-16"},[t._v("距离结束还有")]),t._v(" "),n("count-down",{attrs:{time:t.countTime,"is-slot":!0},on:{change:t.onChangeDate}},[n("div",{staticClass:"flex row-center count-down xxl"},[n("div",{staticClass:"item white"},[t._v("\n                                "+t._s(t.timeData.hours)+"\n                            ")]),t._v(" "),n("div",{staticClass:"white",staticStyle:{margin:"0 4px"}},[t._v(":")]),t._v(" "),n("div",{staticClass:"item white"},[t._v("\n                                "+t._s(t.timeData.minutes)+"\n                            ")]),t._v(" "),n("div",{staticClass:"white",staticStyle:{margin:"0 4px"}},[t._v(":")]),t._v(" "),n("div",{staticClass:"item white"},[t._v("\n                                "+t._s(t.timeData.seconds)+"\n                            ")])])])],1)]):n("div",{staticClass:"price-wrap flex row-between lighter"},[n("div",{staticClass:"price flex",staticStyle:{"align-items":"baseline"}},[n("div",{staticClass:"m-r-8"},[t._v("价格")]),t._v(" "),n("div",{staticClass:"primary"},[n("price-formate",{attrs:{price:t.checkedGoods.price||t.goodsDetails.price,"subscript-size":16,"first-size":22,"second-size":16}})],1),t._v(" "),n("div",{staticClass:"line-through m-l-8 muted"},[n("price-formate",{attrs:{price:t.checkedGoods.market_price||t.goodsDetails.market_price}})],1)]),t._v(" "),n("div",{staticClass:"flex"},[!0!==t.goodsDetails.stock?n("div",{staticStyle:{"margin-right":"60px"}},[n("div",{staticClass:"m-b-8"},[t._v("库存")]),t._v(" "),n("div",[t._v("\n                            "+t._s(t.checkedGoods.stock||t.goodsDetails.stock)+"\n                        ")])]):t._e(),t._v(" "),n("div",[n("div",{staticClass:"m-b-8"},[t._v("销量")]),t._v(" "),n("div",[t._v(t._s(t.goodsDetails.sales_sum))])])])]),t._v(" "),n("div",{staticClass:"spec-wrap"},t._l(t.goodsSpec,(function(e,o){return n("div",{key:o,staticClass:"spec flex m-b-16"},[n("div",{staticClass:"lighter spec-name"},[t._v(t._s(e.name))]),t._v(" "),n("div",{staticClass:"spec-list flex flex-wrap"},t._l(e.spec_value,(function(o,r){return n("div",{key:r,class:["spec-item lighter",{active:o.checked}],on:{click:function(n){return t.onChoseSpecItem(e.id,o.id)}}},[t._v("\n                            "+t._s(o.value)+"\n                        ")])})),0)])})),0),t._v(" "),n("div",{staticClass:"goods-num flex"},[n("div",{staticClass:"num lighter"},[t._v("数量")]),t._v(" "),n("number-box",{attrs:{min:1,max:t.checkedGoods.stock},model:{value:t.goodsNum,callback:function(e){t.goodsNum=e},expression:"goodsNum"}})],1),t._v(" "),n("div",{staticClass:"goods-btns flex lg"},[n("el-button",{staticClass:"btn white",attrs:{type:"primary"},on:{click:t.onBuyNow}},[t._v("\n                    立即购买\n                ")]),t._v(" "),1!=t.activity.type?n("el-button",{staticClass:"btn addcart",attrs:{type:"primary",plain:""},on:{click:t.onAddCart}},[t._v("\n                    加入购物车\n                ")]):t._e(),t._v(" "),n("div",{staticClass:"btn collection flex row-center",on:{click:t.onCollectionGoods}},[n("img",{staticClass:"start-icon m-r-8",attrs:{src:t.goodsDetails.is_collect?o(640):o(641)}}),t._v(" "),n("span",[t._v(t._s(t.goodsDetails.is_collect?"取消收藏":"收藏商品"))])])],1)]),t._v(" "),n("div",{staticClass:"shop m-l-16 bg-white"},[n("div",{staticClass:"shop-logo flex-col col-center"},[n("el-image",{staticClass:"logo-img",attrs:{src:t.shop.logo}}),t._v(" "),n("nuxt-link",{staticClass:"m-t-10",attrs:{to:"/shop_street_detail?id="+t.shop.id}},[1==t.shop.type?n("el-tag",{attrs:{size:"mini"}},[t._v("自营")]):t._e(),t._v(" "),n("span",{staticClass:"weight-500"},[t._v(t._s(t.shop.name))])],1),t._v(" "),n("div",{staticClass:"xs muted m-t-10 line-5"},[t._v("\n                "+t._s(t.shop.intro)+"\n                ")])],1),t._v(" "),n("div",{staticClass:"flex m-t-20"},[n("div",{staticClass:"flex-1 text-center"},[n("div",{staticClass:"xxl m-b-10"},[t._v(t._s(t.shop.goods_on_sale))]),t._v(" "),n("div",[t._v("全部商品")])]),t._v(" "),n("div",{staticClass:"flex-1 text-center"},[n("div",{staticClass:"xxl m-b-10"},[t._v(t._s(t.shop.follow_num))]),t._v(" "),n("div",[t._v("关注人数")])])]),t._v(" "),n("el-divider"),t._v(" "),n("div",{staticClass:"flex xs m-b-16"},[n("div",{staticClass:"m-r-12"},[t._v("店铺星级")]),t._v(" "),n("div",{staticClass:"m-t-5"},[n("el-rate",{attrs:{disabled:""},model:{value:t.shop.star,callback:function(e){t.$set(t.shop,"star",e)},expression:"shop.star"}})],1)]),t._v(" "),n("div",{staticClass:"flex xs m-b-16"},[n("div",{staticClass:"m-r-12"},[t._v("店铺评分")]),t._v(" "),n("div",{},[t._v(t._s(t.shop.score)+"分")])]),t._v(" "),n("div",[n("el-button",{attrs:{type:"primary",size:"mini"},on:{click:function(e){return t.$router.push("/shop_street_detail?id="+t.shop.id)}}},[t._v("进入店铺")]),t._v(" "),n("el-button",{attrs:{size:"mini"},on:{click:t.changeShopFollow}},[t._v(t._s(1==t.shop.shop_follow_status?"已关注":"关注店铺"))])],1),t._v(" "),n("el-popover",{attrs:{placement:"bottom",width:"200",trigger:"hover"}},[n("div",[n("el-image",{staticStyle:{width:"100%"},attrs:{src:t.shop.customer_image}})],1),t._v(" "),n("div",{staticClass:"xs lighter text-center m-t-30",attrs:{slot:"reference"},slot:"reference"},[n("i",{staticClass:"el-icon-chat-dot-round nr"}),t._v(" "),n("span",[t._v("联系客服")])])])],1)]),t._v(" "),n("div",{staticClass:"details-wrap flex m-t-16"},[n("div",{staticClass:"details bg-white flex-1"},[n("el-tabs",{model:{value:t.active,callback:function(e){t.active=e},expression:"active"}},[n("el-tab-pane",{attrs:{label:"商品详情"}},[n("div",{staticClass:"rich-text",domProps:{innerHTML:t._s(t.goodsDetails.content)}})]),t._v(" "),n("el-tab-pane",{attrs:{label:"商品评价"}},[n("div",{staticClass:"evaluation"},[n("div",{staticClass:"evaluation-hd flex"},[n("div",{staticClass:"rate flex"},[n("div",{staticClass:"lighter m-r-8"},[t._v("好评率")]),t._v(" "),n("div",{staticClass:"primary",staticStyle:{"font-size":"30px"}},[t._v("\n                                    "+t._s(t.goodsDetails.comment.percent)+"\n                                ")])]),t._v(" "),n("div",{staticClass:"score flex"},[n("span",{staticClass:"m-r-8 lighter"},[t._v("评分")]),t._v(" "),n("el-rate",{attrs:{value:t.goodsDetails.comment.goods_comment,disabled:"","allow-half":""}})],1)]),t._v(" "),n("div",{staticClass:"evaluation-tab flex"},t._l(t.comment.comment,(function(e,o){return n("div",{key:o,class:["item",{active:t.commentActive==e.id}],on:{click:function(o){t.commentActive=e.id}}},[t._v("\n                                "+t._s(e.name)+"("+t._s(e.count)+")\n                            ")])})),0)]),t._v(" "),n("div",[t._l(t.comment.comment,(function(e,o){return[t.commentActive==e.id?n("comment-list",{key:o,attrs:{"goods-id":t.id,type:e.id}}):t._e()]}))],2)])],1)],1),t._v(" "),t.shop.goods_list.length?n("div",{staticClass:"goods-like m-l-16"},[n("div",{staticClass:"title bg-white flex p-l-15"},[t._v("店铺推荐")]),t._v(" "),n("div",{staticClass:"goods-list"},[t._l(t.shop.goods_list,(function(e,o){return[o<5?n("nuxt-link",{key:o,staticClass:"item bg-white m-b-16",attrs:{to:"/goods_details/"+e.id}},[n("el-image",{staticClass:"goods-img",attrs:{src:e.image}}),t._v(" "),n("div",{staticClass:"goods-name line-2"},[t._v("\n                            "+t._s(e.name)+"\n                        ")]),t._v(" "),n("div",{staticClass:"price flex m-t-8"},[n("div",{staticClass:"primary m-r-8"},[n("price-formate",{attrs:{price:e.min_price,"first-size":16}})],1),t._v(" "),n("div",{staticClass:"muted sm line-through"},[n("price-formate",{attrs:{price:e.market_price}})],1)])],1):t._e()]}))],2)]):t._e()])]):t._e()}),[],!1,null,null,null);e.default=component.exports;installComponents(component,{PriceFormate:o(442).default,CountDown:o(449).default,NumberBox:o(499).default,CommentList:o(559).default})}}]);