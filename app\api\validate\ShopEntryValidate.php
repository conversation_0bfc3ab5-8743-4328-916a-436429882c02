<?php
namespace app\api\validate;

use app\common\basics\Validate;

/**
 * 商家入驻验证器
 * Class ShopEntryValidate
 * @package app\api\validate
 */
class ShopEntryValidate extends Validate
{
    protected $rule = [
        'cid' => 'require|integer|gt:0',
        'name' => 'require|length:2,50',
        'nickname' => 'require|length:2,20',
        'mobile' => 'require|mobile',
        'account' => 'require|length:3,20|alphaNum',
        'password' => 'require|length:6,20',
        'license' => 'require|array|min:1',
        'pay_way' => 'integer|in:1,2',
        'from' => 'integer|in:1,2,3,4,5',
        'target_tier' => 'require|integer|in:1,2',
        'order_sn' => 'require|length:10,50'
    ];

    protected $message = [
        'cid.require' => '请选择商家分类',
        'cid.integer' => '商家分类格式错误',
        'cid.gt' => '请选择有效的商家分类',
        'name.require' => '请输入商家名称',
        'name.length' => '商家名称长度为2-50个字符',
        'nickname.require' => '请输入商家简称',
        'nickname.length' => '商家简称长度为2-20个字符',
        'mobile.require' => '请输入手机号',
        'mobile.mobile' => '请输入正确的手机号',
        'account.require' => '请输入登录账号',
        'account.length' => '登录账号长度为3-20个字符',
        'account.alphaNum' => '登录账号只能包含字母和数字',
        'password.require' => '请输入登录密码',
        'password.length' => '登录密码长度为6-20个字符',
        'license.require' => '请上传营业执照',
        'license.array' => '营业执照格式错误',
        'license.min' => '请至少上传一张营业执照',
        'pay_way.integer' => '支付方式格式错误',
        'pay_way.in' => '不支持的支付方式',
        'from.integer' => '客户端类型格式错误',
        'from.in' => '不支持的客户端类型',
        'target_tier.require' => '请选择目标等级',
        'target_tier.integer' => '目标等级格式错误',
        'target_tier.in' => '目标等级只能是商家会员或实力厂商',
        'order_sn.require' => '订单号不能为空',
        'order_sn.length' => '订单号格式错误'
    ];

    protected $scene = [
        'freeEntry' => ['cid', 'name', 'nickname', 'mobile', 'account', 'password', 'license'],
        'apply' => ['cid', 'name', 'nickname', 'mobile', 'account', 'password', 'license'],
        'pay' => ['pay_way', 'from'],
        'upgrade' => ['target_tier'],
        'upgradePay' => ['order_sn', 'pay_way', 'from']
    ];
}
