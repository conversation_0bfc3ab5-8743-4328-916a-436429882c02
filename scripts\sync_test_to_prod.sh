#!/bin/bash

# 测试版同步到正式版脚本
# 用法: ./sync_test_to_prod.sh [version_tag]

set -e

# 配置
REPO_URL="https://github.com/your-username/kshop.git"  # 请替换为实际仓库地址
PROJECT_DIR="/var/www/kshop"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查Git状态
check_git_status() {
    log_step "检查Git仓库状态..."
    
    if [ ! -d ".git" ]; then
        log_error "当前目录不是Git仓库！"
        exit 1
    fi
    
    # 检查是否有未提交的更改
    if ! git diff-index --quiet HEAD --; then
        log_warn "检测到未提交的更改："
        git status --porcelain
        echo ""
        read -p "是否继续？(y/N): " -n 1 -r
        echo ""
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_info "操作已取消"
            exit 0
        fi
    fi
}

# 创建版本标签
create_version_tag() {
    local version_tag=$1
    
    if [ -z "$version_tag" ]; then
        # 自动生成版本号
        local last_tag=$(git describe --tags --abbrev=0 2>/dev/null || echo "v0.0.0")
        local version_parts=(${last_tag//v/})
        local version_parts=(${version_parts//./ })
        local major=${version_parts[0]:-0}
        local minor=${version_parts[1]:-0}
        local patch=${version_parts[2]:-0}
        
        # 递增补丁版本号
        patch=$((patch + 1))
        version_tag="v${major}.${minor}.${patch}"
        
        log_info "自动生成版本号: $version_tag"
        read -p "确认使用此版本号？(Y/n): " -n 1 -r
        echo ""
        if [[ $REPLY =~ ^[Nn]$ ]]; then
            read -p "请输入版本号 (格式: v1.0.0): " version_tag
        fi
    fi
    
    echo "$version_tag"
}

# 合并测试分支到主分支
merge_to_master() {
    local version_tag=$1
    
    log_step "合并develop分支到master分支..."
    
    # 切换到master分支
    git checkout master
    git pull origin master
    
    # 合并develop分支
    git merge develop --no-ff -m "Release $version_tag: merge develop to master"
    
    # 创建版本标签
    git tag -a "$version_tag" -m "Release $version_tag"
    
    # 推送到远程仓库
    git push origin master
    git push origin "$version_tag"
    
    log_info "代码已合并到master分支并创建标签: $version_tag"
}

# 部署到正式环境
deploy_to_production() {
    local version_tag=$1
    
    log_step "部署版本 $version_tag 到正式环境..."
    
    # 调用部署脚本
    if [ -f "scripts/deploy.sh" ]; then
        bash scripts/deploy.sh production "$version_tag"
    else
        log_error "部署脚本不存在: scripts/deploy.sh"
        exit 1
    fi
}

# 显示部署后信息
show_deployment_info() {
    local version_tag=$1
    
    echo ""
    echo "=================================="
    echo "🎉 部署完成！"
    echo "=================================="
    echo "版本: $version_tag"
    echo "时间: $(date '+%Y-%m-%d %H:%M:%S')"
    echo ""
    echo "📋 后续操作："
    echo "1. 测试正式环境功能"
    echo "2. 如有问题，可使用以下命令回退："
    echo "   bash scripts/deploy.sh rollback [previous_version]"
    echo ""
    echo "📊 查看可用版本："
    echo "   ls -la /var/www/kshop-releases/"
    echo ""
    echo "🔍 查看部署日志："
    echo "   tail -f /var/log/nginx/access.log"
    echo "=================================="
}

# 主函数
main() {
    local version_tag=$1
    
    log_info "开始测试版到正式版同步流程..."
    
    # 检查当前目录
    if [ ! -f "composer.json" ]; then
        log_error "请在项目根目录执行此脚本！"
        exit 1
    fi
    
    # 检查Git状态
    check_git_status
    
    # 确保在develop分支
    current_branch=$(git branch --show-current)
    if [ "$current_branch" != "develop" ]; then
        log_warn "当前不在develop分支，正在切换..."
        git checkout develop
        git pull origin develop
    fi
    
    # 创建版本标签
    version_tag=$(create_version_tag "$version_tag")
    
    # 确认操作
    echo ""
    log_warn "即将执行以下操作："
    echo "1. 将develop分支合并到master分支"
    echo "2. 创建版本标签: $version_tag"
    echo "3. 部署到正式环境"
    echo ""
    read -p "确认继续？(y/N): " -n 1 -r
    echo ""
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "操作已取消"
        exit 0
    fi
    
    # 执行同步流程
    merge_to_master "$version_tag"
    deploy_to_production "$version_tag"
    show_deployment_info "$version_tag"
    
    # 切换回develop分支
    git checkout develop
    
    log_info "同步流程完成！"
}

# 执行主函数
main "$@"
