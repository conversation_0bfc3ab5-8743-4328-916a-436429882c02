{layout name="layout1" /}

<div class="wrapper">
    <div class="layui-card">
        <!-- 操作提示 -->
        <div class="layui-card-body">
            <div class="layui-collapse" style="border:1px dashed #c4c4c4">
                <div class="layui-colla-item">
                    <h2 class="layui-colla-title like-layui-colla-title">操作提示</h2>
                    <div class="layui-colla-content layui-show">
                        <p>*可对社区话题管理,用户发布文章时需要选择对应的话题.话题下关联文章则不允许删除话题</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 搜索区域 -->
        <div class="layui-card-body layui-form">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label for="name" class="layui-form-label">话题名称：</label>
                    <div class="layui-input-inline">
                        <input type="text" id="name" name="name" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label for="cate" class="layui-form-label">话题分类：</label>
                    <div class="layui-input-inline">
                        <select name="cid" id="cate">
                            <option value="">全部</option>
                            {volist name="cate" id="vo"}
                            <option value="{$vo.id}">{$vo.name}</option>
                            {/volist}
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <a class="layui-btn layui-btn-sm layui-btn-normal" lay-submit lay-filter="search">搜索</a>
                    <a class="layui-btn layui-btn-sm layui-btn-primary" lay-submit lay-filter="clear-search">重置</a>
                </div>
            </div>
        </div>

        <!-- 主体区域 -->
        <div class="layui-card-body">
            <button type="button" class="layui-btn layui-btn-normal layui-btn-sm layEvent" lay-event="add">新增话题</button>

            <table id="like-table-lists" lay-filter="like-table-lists"></table>
            <script type="text/html" id="table-operation">
                <a class="layui-btn layui-btn-normal layui-btn-sm" lay-event="edit">编辑</a>
                <a class="layui-btn layui-btn-danger layui-btn-sm" lay-event="del">删除</a>
            </script>
            <script type="text/html" id="showTpl">
                <input type="checkbox" lay-filter="switch-show" data-id={{d.id}} lay-skin="switch"
                       lay-text="显示|隐藏" {{# if(d.is_show==1){ }} checked {{# } }}/>
            </script>
            <script type="text/html" id="recommendTpl">
                <input type="checkbox" lay-filter="switch-recommend" data-id={{d.id}} lay-skin="switch"
                       lay-text="是|否" {{# if(d.is_recommend==1){ }} checked {{# } }}/>
            </script>
            <script type="text/html" id="table-image">
                {{#  if(d.image){ }}
                <img src="{{d.image}}" class="image-show" alt="图" style="width:80px;height:80px;">
                {{#  } }}
            </script>
        </div>

    </div>
</div>

<script>
    layui.use(["table", "form"], function(){
        var table   = layui.table;
        var form   = layui.form;

        like.tableLists("#like-table-lists", "{:url()}", [
            {field:"name",  align:"center", title:"话题名称"}
            ,{field:"image",  align:"center", title:"话题图标", templet: "#table-image"}
            ,{field:"cate_name",  align:"center", title:"关联分类"}
            ,{field:"article_num",  align:"center", title:"文章数量"}
            ,{field:"sort", align:"center", title:"排序"}
            ,{field:"is_recommend", align:"center", title:"是否推荐",  templet: "#recommendTpl"}
            ,{field:"is_show", align:"center", title:"是否显示",  templet: "#showTpl"}
            ,{title:"操作", align:"center", fixed:"right", toolbar:"#table-operation"}
        ]);

        var active = {
            add: function() {
                layer.open({
                    type: 2
                    ,title: "新增话题"
                    ,content: "{:url('community.CommunityTopic/add')}"
                    ,area: ["90%", "90%"]
                    ,btn: ["确定", "取消"]
                    ,yes: function(index, layero){
                        var iframeWindow = window["layui-layer-iframe" + index];
                        var submit = layero.find("iframe").contents().find("#addSubmit");
                        iframeWindow.layui.form.on("submit(addSubmit)", function(data){
                            like.ajax({
                                url: "{:url('community.CommunityTopic/add')}",
                                data: data.field,
                                type: "POST",
                                success:function(res) {
                                    if(res.code === 1) {
                                        layui.layer.msg(res.msg, {offset: '15px', icon: 1, time: 1000});
                                        layer.close(index);
                                        table.reload("like-table-lists", {
                                            where: {},
                                            page: { cur: 1 }
                                        });
                                    }
                                }
                            });
                        });
                        submit.trigger("click");
                    }
                });
            },
            edit: function(obj) {
                layer.open({
                    type: 2
                    ,title: "编辑话题"
                    ,content: "{:url('community.CommunityTopic/edit')}?id=" + obj.data.id
                    ,area: ["90%", "90%"]
                    ,btn: ["确定", "取消"]
                    ,yes: function(index, layero){
                        var iframeWindow = window["layui-layer-iframe" + index];
                        var submit = layero.find("iframe").contents().find("#addSubmit");
                        iframeWindow.layui.form.on("submit(addSubmit)", function(data){
                            data.field['id'] = obj.data.id;
                            like.ajax({
                                url: "{:url('community.CommunityTopic/edit')}",
                                data: data.field,
                                type: "POST",
                                success:function(res) {
                                    if(res.code === 1) {
                                        layui.layer.msg(res.msg, {offset: '15px', icon: 1, time: 1000});
                                        layer.close(index);
                                        table.reload("like-table-lists", {
                                            where: {},
                                            page: { cur: 1 }
                                        });
                                    }
                                }
                            });
                        });
                        submit.trigger("click");
                    }
                });
            },
            del: function(obj) {
                var cateName =  "<span style='color: red'>"+obj.data.name+"</span>";
                layer.confirm("确定删除话题："+cateName, function(index) {
                    like.ajax({
                        url: "{:url('community.CommunityTopic/del')}",
                        data: {id: obj.data.id},
                        type: "POST",
                        success: function (res) {
                            if (res.code === 1) {
                                layui.layer.msg(res.msg, {offset: '15px', icon: 1, time: 1000});
                                layer.close(index);
                                obj.del();
                            }
                        }
                    });
                    layer.close(index);
                })
            }
        };
        like.eventClick(active);



        //图片放大
        $(document).on('click', '.image-show', function () {
            var src = $(this).attr('src');
            like.showImg(src,400);
        });

        form.on("submit(search)", function(data){
            table.reload("like-table-lists", {
                where: data.field,
                page: {
                    curr: 1
                }
            });
        });

        form.on("submit(clear-search)", function(){
            $("#name").val("");
            $("#cate").val("");
            form.render("select");
            table.reload("like-table-lists", {
                where: {},
                page: {
                    curr: 1
                }
            });
        });

        // 状态切换
        form.on('switch(switch-show)', function (obj) {
            var id = obj.elem.attributes['data-id'].nodeValue;
            var show = 0;
            if (obj.elem.checked) {
                show = 1;
            }
            var data = {field: 'is_show', value:show, id: id};
            updateField(data);
        });

        // 状态切换
        form.on('switch(switch-recommend)', function (obj) {
            var id = obj.elem.attributes['data-id'].nodeValue;
            var recommend = 0;
            if (obj.elem.checked) {
                recommend = 1;
            }
            var data = {field: 'is_recommend', value:recommend, id: id};
            updateField(data);
        });


        function updateField(data) {
            like.ajax({
                url: '{:url("community.CommunityTopic/status")}',
                data: data,
                type: "post",
                success: function (res) {
                    if (res.code === 1) {
                        layui.layer.msg(res.msg, {offset: '15px', icon: 1, time: 1000});
                    }
                }
            });
        }


    })
</script>