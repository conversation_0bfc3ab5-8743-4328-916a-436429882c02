<?php


namespace app\kefuapi\controller;

use app\common\basics\KefuBase;
use app\kefuapi\logic\ChatLogic;
use app\common\server\JsonServer;
use app\kefuapi\validate\ChatValidate;


class Chat extends KefuBase
{

    public $like_not_need_login = ['config'];

    /**
     * @notes 用户列表
     * @return \think\response\Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date 2021/12/14 15:42
     */
    public function user()
    {
        $get = $this->request->get('');
        // 对话过的用户
        $result = ChatLogic::getChatUserList($this->kefu_id, $this->shop_id, $get, $this->page_no, $this->page_size);
        return JsonServer::success('', $result);
    }



    /**
     * @notes 指定用户聊天记录
     * @return \think\response\Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date 2021/12/14 15:43
     */
    public function record()
    {
        $user_id = $this->request->get('user_id/d');
        (new ChatValidate())->goCheck();
        $result = ChatLogic::getChatRecord($this->kefu_id, $user_id, $this->shop_id, $this->page_no, $this->page_size);
        return JsonServer::success('', $result);
    }



    /**
     * @notes 在线的客服
     * @return \think\response\Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date 2021/12/14 15:43
     */
    public function online()
    {
        $result = ChatLogic::getOnlineKefu($this->kefu_id, $this->shop_id);
        return JsonServer::success('', $result);
    }



    /**
     * @notes 快捷回复
     * @return \think\response\Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date 2021/12/15 11:07
     */
    public function reply()
    {
        $keyword = $this->request->get('keyword');
        $result = ChatLogic::getReplyLists($this->shop_id, $keyword, $this->page_no, $this->page_size);
        return JsonServer::success('', $result);
    }


    /**
     * @notes 用户详情接口
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2021/12/15 15:05
     */
    public function userInfo()
    {
        $user_id = $this->request->get('user_id/d');
        $result = ChatLogic::getUserInfo($user_id);
        if (false === $result) {
            return JsonServer::error(ChatLogic::getError() ?: '系统错误');
        }
        return JsonServer::success('', $result);
    }


    /**
     * @notes 获取指定订单列表
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2021/12/15 16:04
     */
    public function order()
    {
        $get = $this->request->get();
        $result = ChatLogic::getOrderLists($get, $this->shop_id, $this->page_no, $this->page_size);
        if (false === $result) {
            return JsonServer::error(ChatLogic::getError() ?: '系统错误');
        }
        return JsonServer::success('', $result);
    }


    /**
     * @notes 客服详情
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2021/12/15 17:34
     */
    public function kefuInfo()
    {
        $result = ChatLogic::getKefuInfo($this->kefu_id);
        return JsonServer::success('', $result);
    }


    /**
     * @notes 配置
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2021/12/16 17:06
     */
    public function config()
    {
        $result = ChatLogic::getConfig();
        return JsonServer::success('', $result);
    }



}