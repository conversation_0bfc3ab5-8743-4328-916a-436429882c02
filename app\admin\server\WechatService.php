<?php

namespace app\admin\server;

use app\common\server\HttpServer;

class WechatService
{
    /**
     * 获取access_token
     * @return mixed
     */
    public static function getAccessToken()
    {
        // 从配置获取appid和secret
        $appid = ConfigServer::get('wechat', 'appid');
        $secret = ConfigServer::get('wechat', 'secret');
        
        $url = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid={$appid}&secret={$secret}";
        $result = HttpServer::httpGet($url);
        $data = json_decode($result, true);
        
        if (isset($data['access_token'])) {
            return $data['access_token'];
        }
        return false;
    }

    /**
     * 创建菜单
     * @param $menuData
     * @return array
     */
    public static function createMenu($menuData)
    {
        $accessToken = self::getAccessToken();
        if (!$accessToken) {
            return ['code' => 0, 'msg' => '获取access_token失败'];
        }
        
        $url = "https://api.weixin.qq.com/cgi-bin/menu/create?access_token={$accessToken}";
        $result = HttpServer::httpPost($url, json_encode($menuData, JSON_UNESCAPED_UNICODE));
        return json_decode($result, true);
    }

    /**
     * 获取菜单
     * @return array|mixed
     */
    public static function getMenu()
    {
        $accessToken = self::getAccessToken();
        if (!$accessToken) {
            return ['code' => 0, 'msg' => '获取access_token失败'];
        }
        
        $url = "https://api.weixin.qq.com/cgi-bin/menu/get?access_token={$accessToken}";
        $result = HttpServer::httpGet($url);
        return json_decode($result, true);
    }

    /**
     * 发送客服消息
     * @param $openId
     * @param $msgType
     * @param $content
     * @return array|mixed
     */
    public static function sendCustomMessage($openId, $msgType, $content)
    {
        $accessToken = self::getAccessToken();
        if (!$accessToken) {
            return ['code' => 0, 'msg' => '获取access_token失败'];
        }
        
        $url = "https://api.weixin.qq.com/cgi-bin/message/custom/send?access_token={$accessToken}";
        
        $data = [
            'touser' => $openId,
            'msgtype' => $msgType,
            $msgType => $content
        ];
        
        $result = HttpServer::httpPost($url, json_encode($data, JSON_UNESCAPED_UNICODE));
        return json_decode($result, true);
    }
}