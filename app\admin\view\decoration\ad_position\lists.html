{layout name="layout1" /}

<div class="wrapper">
    <div class="layui-card">
        <!-- 操作提示 -->
        <div class="layui-card-body">
            <div class="layui-collapse" style="border:1px dashed #c4c4c4">
                <div class="layui-colla-item">
                    <h2 class="layui-colla-title like-layui-colla-title">操作提示</h2>
                    <div class="layui-colla-content layui-show">
                        <p>*平台管理广告位信息，系统默认了部分广告位，允许新建广告位。</p>
                        <p>*移动端商城含H5、小程序、APP。</p>
                        <p>*广告位停用时，该广告位所有广告都会隐藏。</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主体区域 -->
        <div class="layui-tab layui-tab-card" lay-filter="like-tabs">
            <ul class="layui-tab-title">
                <li lay-id="1" class="layui-this">小程序端商城</li>
<!--                <li lay-id="2">PC端商城</li>-->
            </ul>
            <div class="layui-tab-content" style="padding: 6px 15px;">

                <button class="layui-btn layui-btn-sm layEvent {$view_theme_color}" lay-event="add" >新增广告位</button>
                <table id="like-table-lists" lay-filter="like-table-lists"></table>
                <script type="text/html" id="table-operation">
                    {{# if(3 !== d.attr){ }}
                    <a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="edit">编辑</a>
                    {{# } }}
                    {{# if(0 == d.status) { }}
                    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="status">启用</a>
                    {{# }else{ }}
                    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="status">停用</a>
                    {{# } }}
                    {{# if(1 == d.attr){ }}
                    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
                    {{# } }}
                </script>
            </div>
        </div>


    </div>
</div>

<script>
    layui.use(["table", "element", "laydate"], function(){
        var table   = layui.table;
        var element = layui.element;
        var laydate = layui.laydate;
        var terminal = 1;

        laydate.render({type:"datetime", elem:"#apply_start_time", trigger:"click"});
        laydate.render({type:"datetime", elem:"#apply_end_time", trigger:"click"});

        like.tableLists('#like-table-lists', '{:url()}', [
            {field: 'id', width: 60, title: 'ID'}
            ,{field: 'terminal_desc', width: 200, title: '终端'}
            ,{field: 'name', width: 260, align: 'center', title: '广告位'}
            ,{field: 'image_url', width: 120, align: 'center', title: '位置指示图', templet: function(d){
                if(d.image_url){
                    return '<img src="' + d.image_url + '" style="max-height:50px;max-width:80px;cursor:pointer;" class="position-image-preview" data-src="' + d.image_url + '" />';
                }
                return '<span style="color:#999;">无</span>';
            }}
            ,{field: 'attr', width: 120, align: 'center',title: '广告位属性', templet: function(d){
                if(1 == d.attr){
                    return '自定义';
                }
                return  '系统默认';
            }}
            ,{field: 'ad_sn', width: 260, align: 'center', title: '区域码'}
            ,{field: 'ad_yn', width: 260, align: 'center', title: '区域码'}
            ,{field: 'status', width: 120, align: 'center', title: '广告位状态', templet: function(d){
                    if(1 == d.status){
                        return '开启';
                    }
                    return  '停用';
             }}
            ,{title: '操作', width: 180, align: 'center', fixed: 'right', toolbar: '#table-operation'}
        ],{terminal:terminal});

        element.on("tab(like-tabs)", function(){
            terminal = this.getAttribute("lay-id");
            table.reload("like-table-lists", {
                where: {terminal: terminal},
                page: { cur: 1 }
            });
        });

        var active = {
            add:function(obj){
                layer.open({
                    type: 2
                    ,title: '新增广告位'
                    ,content: '{:url("decoration.AdPosition/add")}'
                    ,area: ['90%', '90%']
                    ,btn: ['确定', '取消']
                    ,yes: function(index, layero){
                        var iframeWindow = window['layui-layer-iframe'+ index]
                            ,submitID = 'addSubmit'
                            ,submit = layero.find('iframe').contents().find('#'+ submitID);
                        //监听提交
                        iframeWindow.layui.form.on('submit('+ submitID +')', function(data){
                            var field = data.field;
                            field['terminal'] = terminal,
                            like.ajax({
                                url:'{:url("decoration.AdPosition/add")}',
                                data:field,
                                type:"post",
                                success:function(res)
                                {
                                    if(res.code == 1) {
                                        layui.layer.msg(res.msg);
                                        layer.close(index);
                                        table.reload('like-table-lists');
                                    }
                                }
                            });
                        });
                        submit.trigger('click');
                    }
                });

            },
            edit:function(obj){
                var id = obj.data.id;
                layer.open({
                    type: 2
                    ,title: '编辑广告位'
                    ,content: '{:url("decoration.AdPosition/edit")}?id='+id
                    ,area: ['90%', '90%']
                    ,btn: ['确定', '取消']
                    ,yes: function(index, layero){
                        var iframeWindow = window['layui-layer-iframe'+ index]
                            ,submitID = 'editSubmit'
                            ,submit = layero.find('iframe').contents().find('#'+ submitID);
                        //监听提交
                        iframeWindow.layui.form.on('submit('+ submitID +')', function(data){
                            var field = data.field;
                                like.ajax({
                                    url:'{:url("decoration.AdPosition/edit")}',
                                    data:field,
                                    type:"post",
                                    success:function(res)
                                    {
                                        if(res.code == 1) {
                                            layui.layer.msg(res.msg);
                                            layer.close(index);
                                            table.reload('like-table-lists');
                                        }
                                    }
                                });
                        });
                        submit.trigger('click');
                    }
                });
            },
            status: function(obj) {
                var status =   1 == obj.data.status ? 0 : 1;
                if(status){
                    var tips = "确定启用广告位:<span style='color: red'>"+ obj.data.name +"</span>";
                }else{
                    var tips = "确定停用广告位:<span style='color: red'>"+ obj.data.name +"</span>";
                }
                layer.confirm(tips, function(index) {
                    like.ajax({
                        url: "{:url('decoration.AdPosition/swtichStatus')}",
                        data: {id: obj.data.id,status:status},
                        type: "POST",
                        success: function (res) {
                            if (res.code === 1) {
                                layui.layer.msg(res.msg);
                                layer.close(index);
                                table.reload('like-table-lists');
                            }
                        }
                    });
                    layer.close(index);
                })
            },
            del: function(obj) {
                layer.confirm("确定删除广告位:<span style='color: red'>"+ obj.data.name +"</span>", function(index) {
                    like.ajax({
                        url: "{:url('decoration.AdPosition/del')}",
                        data: {id: obj.data.id},
                        type: "POST",
                        success: function (res) {
                            if (res.code === 1) {
                                layui.layer.msg(res.msg);
                                layer.close(index);
                                obj.del();
                            }
                        }
                    });
                    layer.close(index);
                })
            },
        };
        like.eventClick(active);

        // 添加图片点击放大功能
        $(document).on('click', '.position-image-preview', function(){
            var src = $(this).data('src');
            if(src) {
                layer.photos({
                    photos: {
                        "title": "位置指示图",
                        "data": [{
                            "src": src,
                            "alt": "位置指示图"
                        }]
                    },
                    anim: 5
                });
            }
        });

    })
</script>