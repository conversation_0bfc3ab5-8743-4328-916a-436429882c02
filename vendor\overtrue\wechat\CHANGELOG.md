# Change Log

## [Unreleased](https://github.com/overtrue/wechat/tree/HEAD)

[Full Changelog](https://github.com/overtrue/wechat/compare/4.0.0...HEAD)

**Closed issues:**

- 能否增加对symfony4的支持 [\#1044](https://github.com/overtrue/wechat/issues/1044)

## [4.0.0](https://github.com/overtrue/wechat/tree/4.0.0) (2017-12-11)
[Full Changelog](https://github.com/overtrue/wechat/compare/3.3.21...4.0.0)

**Closed issues:**

- filecache.php 文件     createPathIfNeeded\(string $path\) : bool [\#1046](https://github.com/overtrue/wechat/issues/1046)
- 沙箱模式的Notify总是出错：Invalid request payloads. [\#1045](https://github.com/overtrue/wechat/issues/1045)
- 你好我是SwooleDistributed框架的作者 [\#1040](https://github.com/overtrue/wechat/issues/1040)

## [3.3.21](https://github.com/overtrue/wechat/tree/3.3.21) (2017-12-10)
[Full Changelog](https://github.com/overtrue/wechat/compare/4.0.0-beta.4...3.3.21)

**Closed issues:**

- 开启开放平台自动路由后报错 [\#1042](https://github.com/overtrue/wechat/issues/1042)
- 关于3.x升级4.x的问题 [\#1041](https://github.com/overtrue/wechat/issues/1041)
- 获取不了unionid [\#1038](https://github.com/overtrue/wechat/issues/1038)
- authorizer\_refresh\_token刷新问题 [\#1033](https://github.com/overtrue/wechat/issues/1033)
- lumen+swoole无法获取request信息。 [\#1032](https://github.com/overtrue/wechat/issues/1032)
- 上传素材报错， empty post data hint  [\#1031](https://github.com/overtrue/wechat/issues/1031)
- 开放平台不支持全网发布接入检测（第三方） [\#1029](https://github.com/overtrue/wechat/issues/1029)
- 公众号模板消息不兼容跳转小程序 [\#1025](https://github.com/overtrue/wechat/issues/1025)
- swoole下无法使用 [\#1017](https://github.com/overtrue/wechat/issues/1017)
- 请教有没有高清素材下载方法？ [\#997](https://github.com/overtrue/wechat/issues/997)
- 自动回复多图文素材，错误 [\#996](https://github.com/overtrue/wechat/issues/996)
- xml解释失败 [\#989](https://github.com/overtrue/wechat/issues/989)
- Curl error 77 [\#982](https://github.com/overtrue/wechat/issues/982)
- 3.1.10 H5支付不晓得算不算BUG的BUG [\#968](https://github.com/overtrue/wechat/issues/968)
- 请问是否有遇到微信扫码或内部打开外部网站出现请求2次的情况 [\#963](https://github.com/overtrue/wechat/issues/963)
- 请问4.0何时正式发布？ [\#962](https://github.com/overtrue/wechat/issues/962)
- dev-master 不能用于laravel5.1 [\#952](https://github.com/overtrue/wechat/issues/952)
- 请教小程序的模板消息是否支持 [\#920](https://github.com/overtrue/wechat/issues/920)
- 模板消息的颜色设置问题 [\#914](https://github.com/overtrue/wechat/issues/914)
- 英文文档跳转问题 [\#854](https://github.com/overtrue/wechat/issues/854)
- \[4.0\] 功能测试 [\#849](https://github.com/overtrue/wechat/issues/849)
- \[4.0\] 命名变更 [\#743](https://github.com/overtrue/wechat/issues/743)

**Merged pull requests:**

- Scrutinizer Auto-Fixes [\#1043](https://github.com/overtrue/wechat/pull/1043) ([scrutinizer-auto-fixer](https://github.com/scrutinizer-auto-fixer))
- 修复解密小程序转发信息数据\(wx.getShareInfo\)失败的问题 [\#1037](https://github.com/overtrue/wechat/pull/1037) ([yyqqing](https://github.com/yyqqing))
- 修復微信支付沙盒模式的通知結果本地校驗失敗錯誤。 [\#1036](https://github.com/overtrue/wechat/pull/1036) ([amyuki](https://github.com/amyuki))
- 修复 verifyTicket 使用不了自定义缓存的问题 [\#1034](https://github.com/overtrue/wechat/pull/1034) ([mingyoung](https://github.com/mingyoung))
- 🚧 Auto discover extensions. [\#1027](https://github.com/overtrue/wechat/pull/1027) ([mingyoung](https://github.com/mingyoung))

## [4.0.0-beta.4](https://github.com/overtrue/wechat/tree/4.0.0-beta.4) (2017-11-21)
[Full Changelog](https://github.com/overtrue/wechat/compare/3.3.20...4.0.0-beta.4)

**Closed issues:**

- Order对象的$attributes中不能传入device\_info参数 [\#1030](https://github.com/overtrue/wechat/issues/1030)
- 默认文件缓存的路径是否可以简单修改? [\#1023](https://github.com/overtrue/wechat/issues/1023)
- 3.3.17 版本获取 token 的问题 [\#1022](https://github.com/overtrue/wechat/issues/1022)
- \[V3\] AccessToken.php:243 [\#1021](https://github.com/overtrue/wechat/issues/1021)

**Merged pull requests:**

- more detailed cache key. [\#1028](https://github.com/overtrue/wechat/pull/1028) ([mingyoung](https://github.com/mingyoung))
- Apply fixes from StyleCI [\#1026](https://github.com/overtrue/wechat/pull/1026) ([mingyoung](https://github.com/mingyoung))
- Specify the request instance. [\#1024](https://github.com/overtrue/wechat/pull/1024) ([mingyoung](https://github.com/mingyoung))

## [3.3.20](https://github.com/overtrue/wechat/tree/3.3.20) (2017-11-13)
[Full Changelog](https://github.com/overtrue/wechat/compare/3.3.18...3.3.20)

## [3.3.18](https://github.com/overtrue/wechat/tree/3.3.18) (2017-11-11)
[Full Changelog](https://github.com/overtrue/wechat/compare/3.3.17...3.3.18)

**Closed issues:**

- 临时二维码接口无法生成以字符串为参数的二维码 [\#1020](https://github.com/overtrue/wechat/issues/1020)
- 现金红包出现了500错误，跟进显示http\_error:true [\#1016](https://github.com/overtrue/wechat/issues/1016)
- 4.0 企业微信agent OA的错误 [\#1015](https://github.com/overtrue/wechat/issues/1015)
- 求thinkphp框架demo [\#1010](https://github.com/overtrue/wechat/issues/1010)
- 沙箱模式获取验签 key 时产生无限循环 , 无法正常获取 [\#1009](https://github.com/overtrue/wechat/issues/1009)
- JSSDK里面url导致的invalid signature错误 [\#1002](https://github.com/overtrue/wechat/issues/1002)
- 微信支付沙箱模式下，回调验签错误 [\#998](https://github.com/overtrue/wechat/issues/998)
- 有微信退款回调接口吗？ [\#985](https://github.com/overtrue/wechat/issues/985)
- 希望兼容新出的微信H5支付 [\#966](https://github.com/overtrue/wechat/issues/966)
- 小程序生成无限量二维码接口缺少参数 [\#965](https://github.com/overtrue/wechat/issues/965)

**Merged pull requests:**

- 查询企业付款接口参数调整，加入企业付款到银行卡接口（RSA 参数加密待完成） [\#1019](https://github.com/overtrue/wechat/pull/1019) ([tianyong90](https://github.com/tianyong90))
- Token AESKey can be null. [\#1013](https://github.com/overtrue/wechat/pull/1013) ([mingyoung](https://github.com/mingyoung))
- Apply fixes from StyleCI [\#1012](https://github.com/overtrue/wechat/pull/1012) ([mingyoung](https://github.com/mingyoung))
- Add Mini-program tester's binding/unbinding feature [\#1011](https://github.com/overtrue/wechat/pull/1011) ([caikeal](https://github.com/caikeal))
- Apply fixes from StyleCI [\#1008](https://github.com/overtrue/wechat/pull/1008) ([overtrue](https://github.com/overtrue))
- Apply fixes from StyleCI [\#1007](https://github.com/overtrue/wechat/pull/1007) ([overtrue](https://github.com/overtrue))
- Added open-platform's mini-program code management [\#1003](https://github.com/overtrue/wechat/pull/1003) ([caikeal](https://github.com/caikeal))
- Cleanup payment [\#1001](https://github.com/overtrue/wechat/pull/1001) ([mingyoung](https://github.com/mingyoung))
- Unify get stream. [\#995](https://github.com/overtrue/wechat/pull/995) ([mingyoung](https://github.com/mingyoung))
- Add appCode `page` param. [\#991](https://github.com/overtrue/wechat/pull/991) ([mingyoung](https://github.com/mingyoung))

## [3.3.17](https://github.com/overtrue/wechat/tree/3.3.17) (2017-10-27)
[Full Changelog](https://github.com/overtrue/wechat/compare/4.0.0-beta.3...3.3.17)

**Closed issues:**

- open platform component\_verify\_ticket 错误 [\#984](https://github.com/overtrue/wechat/issues/984)
- 请教下载语音后的文件不完整怎么处理？ [\#980](https://github.com/overtrue/wechat/issues/980)
- 微信支付 API 调用下单解析缓缓 [\#977](https://github.com/overtrue/wechat/issues/977)
- 是否可以加入微信收款（个人转账版）服务接口 [\#970](https://github.com/overtrue/wechat/issues/970)
- 微信公众号消息加解密方式‘兼容模式’也需要填写‘aes\_key’参数，不能为空 [\#967](https://github.com/overtrue/wechat/issues/967)
- 第三方平台 接收消息一直报错 但是能回复消息 也会提示错误 [\#961](https://github.com/overtrue/wechat/issues/961)
- 中文官网无法访问 [\#960](https://github.com/overtrue/wechat/issues/960)
- laravel队列中使用了SDK报Component verify ticket does not exists. [\#958](https://github.com/overtrue/wechat/issues/958)
- 接口调用次数每日限额清零方法没有？ [\#953](https://github.com/overtrue/wechat/issues/953)
- 获取access\_toekn失败之后抛出异常的地方，能够与其他地方统一使用下述这个 resolveResponse 返回数据 [\#951](https://github.com/overtrue/wechat/issues/951)
- 官网挂了 [\#950](https://github.com/overtrue/wechat/issues/950)
- 无法接收到菜单点击事件推送的消息 [\#949](https://github.com/overtrue/wechat/issues/949)
- 请教这个sdk是否可用于android 或者ios 登录？ [\#948](https://github.com/overtrue/wechat/issues/948)
- 关于access token 后端分布式部署的中控服务器的问题 [\#947](https://github.com/overtrue/wechat/issues/947)
- 4.0 不支持laravel 5.2? [\#946](https://github.com/overtrue/wechat/issues/946)
- log不能打印出来 [\#945](https://github.com/overtrue/wechat/issues/945)
- EasyWeChat.org域名挂了？？ [\#940](https://github.com/overtrue/wechat/issues/940)
- 微信静默授权的时候，页面上老是会显示一段很长的英文Redirecting to http://xxxx，很影响用户体验，有没有什么方法可以去掉，保留空白页，或者允许自定义显示内容 [\#939](https://github.com/overtrue/wechat/issues/939)
- 微信小程序生成二维码（接口B）微信扫描不出来结果 [\#938](https://github.com/overtrue/wechat/issues/938)
- 官网可否支持看老版本的文档？ [\#937](https://github.com/overtrue/wechat/issues/937)
- 客服发送消息 收到的中文信息被unicode 编码 [\#935](https://github.com/overtrue/wechat/issues/935)
- 有多个商户时，订单通知的 $payment 怎么创建 [\#934](https://github.com/overtrue/wechat/issues/934)
- console中使用$app-\>user-\>get报错 [\#932](https://github.com/overtrue/wechat/issues/932)
- PC端扫描登录的问题 [\#930](https://github.com/overtrue/wechat/issues/930)
- 关于小程序支付的疑问 [\#912](https://github.com/overtrue/wechat/issues/912)
- 服务商api模式使用可以更加详细吗 [\#653](https://github.com/overtrue/wechat/issues/653)

**Merged pull requests:**

- 修正 微信公众号要求 所有接口使用 HTTPS 方式访问 [\#988](https://github.com/overtrue/wechat/pull/988) ([drogjh](https://github.com/drogjh))
- Apply fixes from StyleCI [\#987](https://github.com/overtrue/wechat/pull/987) ([mingyoung](https://github.com/mingyoung))
- 修复微信收款（个人转账版）商户添加、查询含有多余字段导致签名失败的问题 [\#986](https://github.com/overtrue/wechat/pull/986) ([chenhaizano](https://github.com/chenhaizano))
- Add merchant client. [\#983](https://github.com/overtrue/wechat/pull/983) ([mingyoung](https://github.com/mingyoung))
- Fix PKCS7 unpad issue. [\#981](https://github.com/overtrue/wechat/pull/981) ([mingyoung](https://github.com/mingyoung))
- 💯 Add unit tests. [\#979](https://github.com/overtrue/wechat/pull/979) ([mingyoung](https://github.com/mingyoung))
- Apply fixes from StyleCI [\#978](https://github.com/overtrue/wechat/pull/978) ([overtrue](https://github.com/overtrue))
- Add sub-merchant support. [\#976](https://github.com/overtrue/wechat/pull/976) ([mingyoung](https://github.com/mingyoung))
- Apply fixes from StyleCI [\#974](https://github.com/overtrue/wechat/pull/974) ([overtrue](https://github.com/overtrue))
- Apply fixes from StyleCI [\#973](https://github.com/overtrue/wechat/pull/973) ([mingyoung](https://github.com/mingyoung))
- Refactoring payment [\#972](https://github.com/overtrue/wechat/pull/972) ([mingyoung](https://github.com/mingyoung))
- Fix request method. [\#964](https://github.com/overtrue/wechat/pull/964) ([mingyoung](https://github.com/mingyoung))
- MiniProgram template. [\#959](https://github.com/overtrue/wechat/pull/959) ([mingyoung](https://github.com/mingyoung))
- 企业微信 jssdk ticket [\#954](https://github.com/overtrue/wechat/pull/954) ([mingyoung](https://github.com/mingyoung))
- Scrutinizer Auto-Fixes [\#944](https://github.com/overtrue/wechat/pull/944) ([scrutinizer-auto-fixer](https://github.com/scrutinizer-auto-fixer))
- 简化子商户js config [\#943](https://github.com/overtrue/wechat/pull/943) ([HanSon](https://github.com/HanSon))
- Apply fixes from StyleCI [\#942](https://github.com/overtrue/wechat/pull/942) ([overtrue](https://github.com/overtrue))
- 支持子商户JS CONFIG生成 [\#941](https://github.com/overtrue/wechat/pull/941) ([HanSon](https://github.com/HanSon))

## [4.0.0-beta.3](https://github.com/overtrue/wechat/tree/4.0.0-beta.3) (2017-09-23)
[Full Changelog](https://github.com/overtrue/wechat/compare/3.3.16...4.0.0-beta.3)

**Closed issues:**

- 退款结果通知 [\#858](https://github.com/overtrue/wechat/issues/858)

**Merged pull requests:**

- Update Application.php [\#936](https://github.com/overtrue/wechat/pull/936) ([HanSon](https://github.com/HanSon))

## [3.3.16](https://github.com/overtrue/wechat/tree/3.3.16) (2017-09-20)
[Full Changelog](https://github.com/overtrue/wechat/compare/3.3.15...3.3.16)

**Closed issues:**

- 希望能增加获取回复数据的方法 [\#929](https://github.com/overtrue/wechat/issues/929)
- 3.3 版本 数据类型不对导致无法运行 [\#928](https://github.com/overtrue/wechat/issues/928)

**Merged pull requests:**

- 增加退款回调处理 [\#931](https://github.com/overtrue/wechat/pull/931) ([leo108](https://github.com/leo108))

## [3.3.15](https://github.com/overtrue/wechat/tree/3.3.15) (2017-09-13)
[Full Changelog](https://github.com/overtrue/wechat/compare/3.3.14...3.3.15)

**Closed issues:**

- 微信 for windows 发送文件的时候报错 [\#927](https://github.com/overtrue/wechat/issues/927)

## [3.3.14](https://github.com/overtrue/wechat/tree/3.3.14) (2017-09-13)
[Full Changelog](https://github.com/overtrue/wechat/compare/4.0.0-beta.2...3.3.14)

**Closed issues:**

- 请教授权的时候什么方法拿到用户是否关注了本公众号？ [\#926](https://github.com/overtrue/wechat/issues/926)

## [4.0.0-beta.2](https://github.com/overtrue/wechat/tree/4.0.0-beta.2) (2017-09-12)
[Full Changelog](https://github.com/overtrue/wechat/compare/4.0.0-beta.1...4.0.0-beta.2)

**Closed issues:**

- readme.md写错了? [\#923](https://github.com/overtrue/wechat/issues/923)
- token验证成功，但还是回复暂时不可用，困扰1个星期多了，真心求助！！！有偿都可以！！ [\#922](https://github.com/overtrue/wechat/issues/922)
- 条件判断错了，stripos返回的是“返回在字符串 haystack 中 needle 首次出现的数字位置。”，所以不能直接作为条件判断 [\#915](https://github.com/overtrue/wechat/issues/915)
- README中的链接是否错误 [\#913](https://github.com/overtrue/wechat/issues/913)
- 测试公众号无法接受用户信息 [\#911](https://github.com/overtrue/wechat/issues/911)
- ReadMe文件过期 [\#910](https://github.com/overtrue/wechat/issues/910)
- 开放平台服务，取消授权会有哪些参数过来？ [\#909](https://github.com/overtrue/wechat/issues/909)
- token无法验证 [\#908](https://github.com/overtrue/wechat/issues/908)
- laravel 5.4 composer 失败 [\#907](https://github.com/overtrue/wechat/issues/907)
- 开放平台：组件ticket无法通过 [\#904](https://github.com/overtrue/wechat/issues/904)
- 官方网站一直登陆不了，浙江丽水地区 [\#903](https://github.com/overtrue/wechat/issues/903)
- \[4.0\] Pimple\Exception\UnknownIdentifierException [\#901](https://github.com/overtrue/wechat/issues/901)
- 4.0 报错“Your requirements could not be resolved to an installable set of packages.” [\#898](https://github.com/overtrue/wechat/issues/898)

**Merged pull requests:**

- 修改通过ticket换取二维码图片地址的逻辑 [\#925](https://github.com/overtrue/wechat/pull/925) ([Gwill](https://github.com/Gwill))
- make domain more flexible [\#924](https://github.com/overtrue/wechat/pull/924) ([HanSon](https://github.com/HanSon))
- add code & domain comment [\#921](https://github.com/overtrue/wechat/pull/921) ([HanSon](https://github.com/HanSon))
- Apply fixes from StyleCI [\#919](https://github.com/overtrue/wechat/pull/919) ([overtrue](https://github.com/overtrue))
- \[3.1\] Custom PreAuthCode Support [\#918](https://github.com/overtrue/wechat/pull/918) ([freyo](https://github.com/freyo))
- 修改acess\_token无效时微信返回错误码的判断 [\#916](https://github.com/overtrue/wechat/pull/916) ([blackjune](https://github.com/blackjune))
- \[4.0\] Add optional 'request' parameter to notify handler methods [\#905](https://github.com/overtrue/wechat/pull/905) ([edwardaa](https://github.com/edwardaa))
- Apply fixes from StyleCI [\#902](https://github.com/overtrue/wechat/pull/902) ([overtrue](https://github.com/overtrue))
- Apply fixes from StyleCI [\#897](https://github.com/overtrue/wechat/pull/897) ([overtrue](https://github.com/overtrue))
- 增加OAuth中Guzzle\Client的配置项的设置 [\#893](https://github.com/overtrue/wechat/pull/893) ([khsing](https://github.com/khsing))
- Apply fixes from StyleCI [\#887](https://github.com/overtrue/wechat/pull/887) ([overtrue](https://github.com/overtrue))
- Scrutinizer Auto-Fixes [\#884](https://github.com/overtrue/wechat/pull/884) ([scrutinizer-auto-fixer](https://github.com/scrutinizer-auto-fixer))

## [4.0.0-beta.1](https://github.com/overtrue/wechat/tree/4.0.0-beta.1) (2017-08-31)
[Full Changelog](https://github.com/overtrue/wechat/compare/4.0.0-alpha.2...4.0.0-beta.1)

**Closed issues:**

- http://easywechat.org/ 网站访问不了了? [\#896](https://github.com/overtrue/wechat/issues/896)
- 关于缓存，请问为什么key中包含appId \* 2，有什么讲究吗？ [\#892](https://github.com/overtrue/wechat/issues/892)
- 小程序调用解密程序报-41003错误 [\#891](https://github.com/overtrue/wechat/issues/891)
- 小程序调用加密数据解密时报错，不存在方法 [\#890](https://github.com/overtrue/wechat/issues/890)
- 有关4.0使用文档的问题 [\#883](https://github.com/overtrue/wechat/issues/883)
- \[4.0\] PHP最低版本能否降到7.0  [\#880](https://github.com/overtrue/wechat/issues/880)

**Merged pull requests:**

- \[4.0\] Pass proper arguments to the Response constructor [\#895](https://github.com/overtrue/wechat/pull/895) ([edwardaa](https://github.com/edwardaa))
- Fix baseUrl and json issues. [\#894](https://github.com/overtrue/wechat/pull/894) ([mingyoung](https://github.com/mingyoung))
- Apply fixes from StyleCI [\#889](https://github.com/overtrue/wechat/pull/889) ([overtrue](https://github.com/overtrue))
- Scrutinizer Auto-Fixes [\#885](https://github.com/overtrue/wechat/pull/885) ([scrutinizer-auto-fixer](https://github.com/scrutinizer-auto-fixer))
- Apply fixes from StyleCI [\#882](https://github.com/overtrue/wechat/pull/882) ([overtrue](https://github.com/overtrue))
- 补充通用卡接口 [\#881](https://github.com/overtrue/wechat/pull/881) ([XiaoLer](https://github.com/XiaoLer))
- Apply fixes from StyleCI [\#879](https://github.com/overtrue/wechat/pull/879) ([overtrue](https://github.com/overtrue))
- \[3.1\] Payment/API 没有使用全局的 cache [\#878](https://github.com/overtrue/wechat/pull/878) ([edwardaa](https://github.com/edwardaa))
- Add JSON\_UNESCAPED\_UNICODE option. [\#874](https://github.com/overtrue/wechat/pull/874) ([mingyoung](https://github.com/mingyoung))
- update \_\_set\_state magic method to static [\#872](https://github.com/overtrue/wechat/pull/872) ([8090Lambert](https://github.com/8090Lambert))

## [4.0.0-alpha.2](https://github.com/overtrue/wechat/tree/4.0.0-alpha.2) (2017-08-20)
[Full Changelog](https://github.com/overtrue/wechat/compare/4.0.0-alpha.1...4.0.0-alpha.2)

**Closed issues:**

- 你好，怎么用的 [\#869](https://github.com/overtrue/wechat/issues/869)

**Merged pull requests:**

- Tweak dir [\#871](https://github.com/overtrue/wechat/pull/871) ([mingyoung](https://github.com/mingyoung))
- Fix mini-program guard. [\#870](https://github.com/overtrue/wechat/pull/870) ([mingyoung](https://github.com/mingyoung))

## [4.0.0-alpha.1](https://github.com/overtrue/wechat/tree/4.0.0-alpha.1) (2017-08-14)
[Full Changelog](https://github.com/overtrue/wechat/compare/3.3.13...4.0.0-alpha.1)

**Closed issues:**

- 对doctrine/cache依赖的版本锁定 [\#867](https://github.com/overtrue/wechat/issues/867)

## [3.3.13](https://github.com/overtrue/wechat/tree/3.3.13) (2017-08-13)
[Full Changelog](https://github.com/overtrue/wechat/compare/3.3.12...3.3.13)

**Closed issues:**

- 文档中网页授权实例写的不明确 [\#850](https://github.com/overtrue/wechat/issues/850)
- \[意见\]作者能否提供getTokenFromServer方法扩展从外部第三方获取access\_token [\#837](https://github.com/overtrue/wechat/issues/837)
- invalid credential, access\_token is invalid or not latest [\#808](https://github.com/overtrue/wechat/issues/808)
- \[4.0\] 重构卡券 [\#806](https://github.com/overtrue/wechat/issues/806)
- \[4.0\] 重构 Broadcasting [\#805](https://github.com/overtrue/wechat/issues/805)
- \[4.0\] 变更日志 [\#746](https://github.com/overtrue/wechat/issues/746)

**Merged pull requests:**

- Fixed open-platform authorizer server token. [\#866](https://github.com/overtrue/wechat/pull/866) ([mingyoung](https://github.com/mingyoung))
- payment\ClientTest 优化 [\#865](https://github.com/overtrue/wechat/pull/865) ([tianyong90](https://github.com/tianyong90))
- Apply fixes from StyleCI [\#864](https://github.com/overtrue/wechat/pull/864) ([overtrue](https://github.com/overtrue))
- 退款通知处理及相关单元测试 [\#863](https://github.com/overtrue/wechat/pull/863) ([tianyong90](https://github.com/tianyong90))
- Apply fixes from StyleCI [\#862](https://github.com/overtrue/wechat/pull/862) ([overtrue](https://github.com/overtrue))
- Update dependence version. [\#861](https://github.com/overtrue/wechat/pull/861) ([mingyoung](https://github.com/mingyoung))
- Add tests. [\#859](https://github.com/overtrue/wechat/pull/859) ([mingyoung](https://github.com/mingyoung))
- Apply fixes from StyleCI [\#857](https://github.com/overtrue/wechat/pull/857) ([overtrue](https://github.com/overtrue))
- Payment 单元测试优化 [\#856](https://github.com/overtrue/wechat/pull/856) ([tianyong90](https://github.com/tianyong90))
- Apply fixes from StyleCI [\#855](https://github.com/overtrue/wechat/pull/855) ([overtrue](https://github.com/overtrue))
- lists 方法重命名为 list，相关单元测试调整 [\#853](https://github.com/overtrue/wechat/pull/853) ([tianyong90](https://github.com/tianyong90))
- Apply fixes from StyleCI [\#852](https://github.com/overtrue/wechat/pull/852) ([overtrue](https://github.com/overtrue))
- Payment 单元测试及部分问题修复 [\#851](https://github.com/overtrue/wechat/pull/851) ([tianyong90](https://github.com/tianyong90))
- Apply fixes from StyleCI [\#848](https://github.com/overtrue/wechat/pull/848) ([overtrue](https://github.com/overtrue))
- 调整 Payment\BaseClient 注入的 $app 类型 [\#847](https://github.com/overtrue/wechat/pull/847) ([tianyong90](https://github.com/tianyong90))
- array\_merge 方法参数类型转换， type hints [\#846](https://github.com/overtrue/wechat/pull/846) ([tianyong90](https://github.com/tianyong90))
- Fix oauth. [\#845](https://github.com/overtrue/wechat/pull/845) ([mingyoung](https://github.com/mingyoung))
- Text message. [\#844](https://github.com/overtrue/wechat/pull/844) ([mingyoung](https://github.com/mingyoung))
- Rename BaseService -\> BasicService. [\#843](https://github.com/overtrue/wechat/pull/843) ([overtrue](https://github.com/overtrue))
- Apply fixes from StyleCI [\#842](https://github.com/overtrue/wechat/pull/842) ([overtrue](https://github.com/overtrue))
- Apply fixes from StyleCI [\#841](https://github.com/overtrue/wechat/pull/841) ([overtrue](https://github.com/overtrue))
- phpdoc types。 [\#840](https://github.com/overtrue/wechat/pull/840) ([tianyong90](https://github.com/tianyong90))
- Apply fixes from StyleCI [\#839](https://github.com/overtrue/wechat/pull/839) ([overtrue](https://github.com/overtrue))
- Apply fixes from StyleCI [\#836](https://github.com/overtrue/wechat/pull/836) ([overtrue](https://github.com/overtrue))
- Apply fixes from StyleCI [\#835](https://github.com/overtrue/wechat/pull/835) ([overtrue](https://github.com/overtrue))
- Apply fixes from StyleCI [\#833](https://github.com/overtrue/wechat/pull/833) ([mingyoung](https://github.com/mingyoung))
- Apply fixes from StyleCI [\#831](https://github.com/overtrue/wechat/pull/831) ([overtrue](https://github.com/overtrue))

## [3.3.12](https://github.com/overtrue/wechat/tree/3.3.12) (2017-08-01)
[Full Changelog](https://github.com/overtrue/wechat/compare/3.3.11...3.3.12)

**Closed issues:**

- 能否整合微信开放平台在给出一套demo [\#816](https://github.com/overtrue/wechat/issues/816)
- 请教这个项目的支付部分，尤其是签名和结果回调，是否支持小程序？ [\#814](https://github.com/overtrue/wechat/issues/814)
- 微信意图识别接口返回invalid param [\#804](https://github.com/overtrue/wechat/issues/804)
- 返回param invalid [\#803](https://github.com/overtrue/wechat/issues/803)

**Merged pull requests:**

- change comment word [\#830](https://github.com/overtrue/wechat/pull/830) ([tianyong90](https://github.com/tianyong90))
- Fix getTicket. [\#829](https://github.com/overtrue/wechat/pull/829) ([mingyoung](https://github.com/mingyoung))
- Apply fixes from StyleCI [\#827](https://github.com/overtrue/wechat/pull/827) ([overtrue](https://github.com/overtrue))
- 修正 HasAttributes Trait 引用错误 [\#825](https://github.com/overtrue/wechat/pull/825) ([tianyong90](https://github.com/tianyong90))
- Apply fixes from StyleCI [\#824](https://github.com/overtrue/wechat/pull/824) ([overtrue](https://github.com/overtrue))
- Apply fixes from StyleCI [\#822](https://github.com/overtrue/wechat/pull/822) ([overtrue](https://github.com/overtrue))
- Apply fixes from StyleCI [\#820](https://github.com/overtrue/wechat/pull/820) ([mingyoung](https://github.com/mingyoung))
- Add subscribe message. [\#819](https://github.com/overtrue/wechat/pull/819) ([mingyoung](https://github.com/mingyoung))
- Apply fixes from StyleCI [\#818](https://github.com/overtrue/wechat/pull/818) ([mingyoung](https://github.com/mingyoung))
- 微信开放平台帐号管理 [\#817](https://github.com/overtrue/wechat/pull/817) ([XiaoLer](https://github.com/XiaoLer))
- add method in comment [\#813](https://github.com/overtrue/wechat/pull/813) ([HanSon](https://github.com/HanSon))
- fixed guzzle version [\#812](https://github.com/overtrue/wechat/pull/812) ([HanSon](https://github.com/HanSon))
- Apply fixes from StyleCI [\#811](https://github.com/overtrue/wechat/pull/811) ([mingyoung](https://github.com/mingyoung))
- Downgrade to php 7.0 [\#809](https://github.com/overtrue/wechat/pull/809) ([HanSon](https://github.com/HanSon))

## [3.3.11](https://github.com/overtrue/wechat/tree/3.3.11) (2017-07-17)
[Full Changelog](https://github.com/overtrue/wechat/compare/4.0.0-alpha1...3.3.11)

**Closed issues:**

- 请添加 「退款原因」 参数 [\#802](https://github.com/overtrue/wechat/issues/802)

## [4.0.0-alpha1](https://github.com/overtrue/wechat/tree/4.0.0-alpha1) (2017-07-17)
[Full Changelog](https://github.com/overtrue/wechat/compare/3.3.10...4.0.0-alpha1)

**Closed issues:**

- Overtrue\Wechat\Media not found [\#801](https://github.com/overtrue/wechat/issues/801)
- 在微信的接口配置时Token 无效，可任意输入 [\#800](https://github.com/overtrue/wechat/issues/800)

## [3.3.10](https://github.com/overtrue/wechat/tree/3.3.10) (2017-07-13)
[Full Changelog](https://github.com/overtrue/wechat/compare/3.3.9...3.3.10)

**Closed issues:**

- 第三方平台refresh\_token的保存问题 [\#798](https://github.com/overtrue/wechat/issues/798)
- 网页授权共享session已晚 [\#792](https://github.com/overtrue/wechat/issues/792)

**Merged pull requests:**

- 临时二维码也是支持scene\_str的，这里补充上 [\#797](https://github.com/overtrue/wechat/pull/797) ([lornewang](https://github.com/lornewang))
- Apply fixes from StyleCI [\#795](https://github.com/overtrue/wechat/pull/795) ([overtrue](https://github.com/overtrue))
- add card message type [\#794](https://github.com/overtrue/wechat/pull/794) ([IanGely](https://github.com/IanGely))
- add staff message type wxcard [\#793](https://github.com/overtrue/wechat/pull/793) ([IanGely](https://github.com/IanGely))

## [3.3.9](https://github.com/overtrue/wechat/tree/3.3.9) (2017-07-07)
[Full Changelog](https://github.com/overtrue/wechat/compare/3.3.8...3.3.9)

**Closed issues:**

- \[4.0\] Http 模块 [\#678](https://github.com/overtrue/wechat/issues/678)
- \[4.0\] Http 请求类 [\#582](https://github.com/overtrue/wechat/issues/582)

**Merged pull requests:**

- Apply fixes from StyleCI [\#791](https://github.com/overtrue/wechat/pull/791) ([overtrue](https://github.com/overtrue))
- Add get user portrait method. [\#790](https://github.com/overtrue/wechat/pull/790) ([getive](https://github.com/getive))
- \[Feature\] Move directories [\#789](https://github.com/overtrue/wechat/pull/789) ([overtrue](https://github.com/overtrue))
- \[Feature\] Move traits to kernel. [\#788](https://github.com/overtrue/wechat/pull/788) ([overtrue](https://github.com/overtrue))
- Apply fixes from StyleCI [\#787](https://github.com/overtrue/wechat/pull/787) ([overtrue](https://github.com/overtrue))
- Apply fixes from StyleCI [\#786](https://github.com/overtrue/wechat/pull/786) ([overtrue](https://github.com/overtrue))

## [3.3.8](https://github.com/overtrue/wechat/tree/3.3.8) (2017-07-07)
[Full Changelog](https://github.com/overtrue/wechat/compare/3.3.7...3.3.8)

**Closed issues:**

- $temporary-\>getStream\($media\_id\) 与 file\_get\_contents\(\) 有区别？？? [\#742](https://github.com/overtrue/wechat/issues/742)

## [3.3.7](https://github.com/overtrue/wechat/tree/3.3.7) (2017-07-06)
[Full Changelog](https://github.com/overtrue/wechat/compare/3.3.6...3.3.7)

**Closed issues:**

- 多添加一个$option [\#772](https://github.com/overtrue/wechat/issues/772)
- 消息群发，指定openid群发视频时，微信报错invalid message type hint: \[JUs0Oa0779ge25\] [\#757](https://github.com/overtrue/wechat/issues/757)

## [3.3.6](https://github.com/overtrue/wechat/tree/3.3.6) (2017-07-06)
[Full Changelog](https://github.com/overtrue/wechat/compare/3.3.5...3.3.6)

**Fixed bugs:**

- 素材管理，如果media\_id不存在会保存网页返回的错误代码 [\#592](https://github.com/overtrue/wechat/issues/592)

**Closed issues:**

- https://easywechat.org网站证书刚过期了，知会作者一声 [\#781](https://github.com/overtrue/wechat/issues/781)
- access\_token 是否能不内部主动请求微信 [\#778](https://github.com/overtrue/wechat/issues/778)
- 门店创建API \($poi-\>create\) 建议返回 poi\_id / exception [\#774](https://github.com/overtrue/wechat/issues/774)
- 扩展门店小程序错误 [\#762](https://github.com/overtrue/wechat/issues/762)
- \[4.0\] jssdk 抽出独立模块 [\#754](https://github.com/overtrue/wechat/issues/754)
- \[4.0\] 消息加密解密模块提取到 Kernel [\#753](https://github.com/overtrue/wechat/issues/753)
- 网页能授权但无法获取用户信息，代码跟官方文档一样。 [\#713](https://github.com/overtrue/wechat/issues/713)

**Merged pull requests:**

- Feature: BaseService. [\#785](https://github.com/overtrue/wechat/pull/785) ([overtrue](https://github.com/overtrue))
- Apply fixes from StyleCI [\#784](https://github.com/overtrue/wechat/pull/784) ([overtrue](https://github.com/overtrue))
- Apply fixes from StyleCI [\#783](https://github.com/overtrue/wechat/pull/783) ([mingyoung](https://github.com/mingyoung))

## [3.3.5](https://github.com/overtrue/wechat/tree/3.3.5) (2017-07-04)
[Full Changelog](https://github.com/overtrue/wechat/compare/3.3.4...3.3.5)

**Implemented enhancements:**

- 并发下access\_token存在脏写隐患 [\#696](https://github.com/overtrue/wechat/issues/696)

**Merged pull requests:**

- Apply fixes from StyleCI [\#780](https://github.com/overtrue/wechat/pull/780) ([overtrue](https://github.com/overtrue))

## [3.3.4](https://github.com/overtrue/wechat/tree/3.3.4) (2017-07-04)
[Full Changelog](https://github.com/overtrue/wechat/compare/3.3.3...3.3.4)

**Closed issues:**

- 网页授权获取用户信息无法打开授权页面 [\#773](https://github.com/overtrue/wechat/issues/773)
- Class 'EasyWechat\Foundation\Application' not found  [\#769](https://github.com/overtrue/wechat/issues/769)
- 获取小程序二维码报错 [\#766](https://github.com/overtrue/wechat/issues/766)
- Call to undefined method EasyWeChat\Server\Guard::setRequest\(\) [\#765](https://github.com/overtrue/wechat/issues/765)
- 网页授权问题，提示scopes类型错误 [\#764](https://github.com/overtrue/wechat/issues/764)
- 门店小程序扩展错误问题 [\#763](https://github.com/overtrue/wechat/issues/763)
- 微信开发者平台，全网发布怎么通过 [\#761](https://github.com/overtrue/wechat/issues/761)
- 微信网页授权重复请求报code无效 [\#714](https://github.com/overtrue/wechat/issues/714)

**Merged pull requests:**

- 新版客服功能-获取聊天记录 [\#775](https://github.com/overtrue/wechat/pull/775) ([wuwenbao](https://github.com/wuwenbao))
- Fix mini-program qrcode. [\#768](https://github.com/overtrue/wechat/pull/768) ([mingyoung](https://github.com/mingyoung))
- Add code comments [\#756](https://github.com/overtrue/wechat/pull/756) ([daxiong123](https://github.com/daxiong123))

## [3.3.3](https://github.com/overtrue/wechat/tree/3.3.3) (2017-06-22)
[Full Changelog](https://github.com/overtrue/wechat/compare/3.3.2...3.3.3)

**Implemented enhancements:**

- \[4.0\] Trait HasHttpRequests [\#671](https://github.com/overtrue/wechat/issues/671)
- \[4.0\] 缓存抽象成 trait: InteractsWithCache [\#670](https://github.com/overtrue/wechat/issues/670)
- \[4.0\] 返回值类型可配置 [\#661](https://github.com/overtrue/wechat/issues/661)
- \[4.0\] 报错信息可选 [\#596](https://github.com/overtrue/wechat/issues/596)
- \[4.0\] 简化并完善开发者配置项 [\#584](https://github.com/overtrue/wechat/issues/584)

**Fixed bugs:**

- open\_platform.oauth 过早的获取 access token [\#701](https://github.com/overtrue/wechat/issues/701)

**Closed issues:**

- 微信网页支付配置生成 [\#751](https://github.com/overtrue/wechat/issues/751)
- configForJSSDKPayment [\#744](https://github.com/overtrue/wechat/issues/744)
- 发现微信上有管理公众号留言的接口，不知道是不是新出的 [\#721](https://github.com/overtrue/wechat/issues/721)
- oauth能获取用户信息，再通过access\_token与用户openid去获取信息，部分用户的信息为空 [\#720](https://github.com/overtrue/wechat/issues/720)
- 接入多个公众号 [\#718](https://github.com/overtrue/wechat/issues/718)
- guzzle curl error28 - 去哪设置默认timeout ？ [\#715](https://github.com/overtrue/wechat/issues/715)
-  使用$server-\>getMessage\(\);报错 [\#712](https://github.com/overtrue/wechat/issues/712)
- 怎样从数据库中调取配置 [\#711](https://github.com/overtrue/wechat/issues/711)
- \[4.0\] 支持企业微信 [\#707](https://github.com/overtrue/wechat/issues/707)
- defaultColor does not work. [\#703](https://github.com/overtrue/wechat/issues/703)
- 是否支持H5支付 [\#694](https://github.com/overtrue/wechat/issues/694)
- 生成AccessToken时，似乎没有调用自定义缓存的delete方法 [\#693](https://github.com/overtrue/wechat/issues/693)
- \[4.0\] PSR-6 缓存接口 [\#692](https://github.com/overtrue/wechat/issues/692)
- 微信支付沙盒模式支持配置文件配置 [\#690](https://github.com/overtrue/wechat/issues/690)
- \[4.0\] 优化服务提供器结构 [\#689](https://github.com/overtrue/wechat/issues/689)
- 强制项目不要自动获取AccessToken [\#688](https://github.com/overtrue/wechat/issues/688)
- 小程序解密$encryptedData数据 [\#687](https://github.com/overtrue/wechat/issues/687)
- 微信坑爹timestamp已经解决不需要configForJSSDKPayment改变timestamp中s大小写 [\#686](https://github.com/overtrue/wechat/issues/686)
- \[4.0\] 所有 API 改名为 Client. [\#677](https://github.com/overtrue/wechat/issues/677)
- sandbox\_signkey 过期 [\#675](https://github.com/overtrue/wechat/issues/675)
- 接口配置失败 [\#672](https://github.com/overtrue/wechat/issues/672)
- 下载语音文件偶尔报错：ErrorException: is\_readable\(\) expects parameter 1 to be a valid path [\#667](https://github.com/overtrue/wechat/issues/667)
- 微信支付沙箱地址混乱 [\#665](https://github.com/overtrue/wechat/issues/665)
- 开放平台自动回复出错，提示“该服务号暂时无法提供服务” [\#654](https://github.com/overtrue/wechat/issues/654)
- \[4.0\]自定义微信API的区域接入点 [\#636](https://github.com/overtrue/wechat/issues/636)
- 在命令行使用easywechat如何关闭日志 [\#601](https://github.com/overtrue/wechat/issues/601)
- \[4.0\] PHP 版本最低要求 7.1 [\#586](https://github.com/overtrue/wechat/issues/586)
- \[4.0\] 简化微信 API 请求 [\#583](https://github.com/overtrue/wechat/issues/583)
- \[4.0\] 自定义 endpoint [\#521](https://github.com/overtrue/wechat/issues/521)

**Merged pull requests:**

- Apply fixes from StyleCI [\#750](https://github.com/overtrue/wechat/pull/750) ([overtrue](https://github.com/overtrue))
- Apply fixes from StyleCI [\#749](https://github.com/overtrue/wechat/pull/749) ([overtrue](https://github.com/overtrue))
- Apply fixes from StyleCI [\#747](https://github.com/overtrue/wechat/pull/747) ([overtrue](https://github.com/overtrue))
- Apply fixes from StyleCI [\#745](https://github.com/overtrue/wechat/pull/745) ([overtrue](https://github.com/overtrue))
- Apply fixes from StyleCI [\#740](https://github.com/overtrue/wechat/pull/740) ([mingyoung](https://github.com/mingyoung))
- Apply fixes from StyleCI [\#737](https://github.com/overtrue/wechat/pull/737) ([mingyoung](https://github.com/mingyoung))
- 分模块静态调用 [\#734](https://github.com/overtrue/wechat/pull/734) ([mingyoung](https://github.com/mingyoung))
- Revert "Apply fixes from StyleCI" [\#731](https://github.com/overtrue/wechat/pull/731) ([overtrue](https://github.com/overtrue))
- Apply fixes from StyleCI [\#730](https://github.com/overtrue/wechat/pull/730) ([overtrue](https://github.com/overtrue))
- Apply fixes from StyleCI [\#729](https://github.com/overtrue/wechat/pull/729) ([overtrue](https://github.com/overtrue))
- Revert "Apply fixes from StyleCI" [\#728](https://github.com/overtrue/wechat/pull/728) ([overtrue](https://github.com/overtrue))
- Apply fixes from StyleCI [\#727](https://github.com/overtrue/wechat/pull/727) ([overtrue](https://github.com/overtrue))
- 修复Https 请求判断不准 [\#726](https://github.com/overtrue/wechat/pull/726) ([xutl](https://github.com/xutl))
- Apply fixes from StyleCI [\#725](https://github.com/overtrue/wechat/pull/725) ([mingyoung](https://github.com/mingyoung))
- Apply fixes from StyleCI [\#724](https://github.com/overtrue/wechat/pull/724) ([mingyoung](https://github.com/mingyoung))
- Apply fixes from StyleCI [\#723](https://github.com/overtrue/wechat/pull/723) ([mingyoung](https://github.com/mingyoung))
- Correction notes [\#722](https://github.com/overtrue/wechat/pull/722) ([PersiLiao](https://github.com/PersiLiao))
- Apply fixes from StyleCI [\#717](https://github.com/overtrue/wechat/pull/717) ([mingyoung](https://github.com/mingyoung))
- 新增图文消息留言管理接口 [\#716](https://github.com/overtrue/wechat/pull/716) ([mingyoung](https://github.com/mingyoung))
- Apply fixes from StyleCI [\#710](https://github.com/overtrue/wechat/pull/710) ([mingyoung](https://github.com/mingyoung))
- Apply fixes from StyleCI [\#709](https://github.com/overtrue/wechat/pull/709) ([mingyoung](https://github.com/mingyoung))
- Apply fixes from StyleCI [\#708](https://github.com/overtrue/wechat/pull/708) ([mingyoung](https://github.com/mingyoung))
- Apply fixes from StyleCI [\#706](https://github.com/overtrue/wechat/pull/706) ([overtrue](https://github.com/overtrue))
- 命令行下不打印日志 [\#705](https://github.com/overtrue/wechat/pull/705) ([mingyoung](https://github.com/mingyoung))
- add defaultColor [\#704](https://github.com/overtrue/wechat/pull/704) ([damonto](https://github.com/damonto))
- Fix [\#702](https://github.com/overtrue/wechat/pull/702) ([mingyoung](https://github.com/mingyoung))
- Add api. [\#700](https://github.com/overtrue/wechat/pull/700) ([mingyoung](https://github.com/mingyoung))
- Rename method. [\#699](https://github.com/overtrue/wechat/pull/699) ([mingyoung](https://github.com/mingyoung))
- Apply fixes from StyleCI [\#698](https://github.com/overtrue/wechat/pull/698) ([mingyoung](https://github.com/mingyoung))
- 修正素材管理中的返回值文档注释，正确的类型应该是集合，而不是字符串。 [\#695](https://github.com/overtrue/wechat/pull/695) ([starlight36](https://github.com/starlight36))
- Payment sandbox config. [\#691](https://github.com/overtrue/wechat/pull/691) ([mingyoung](https://github.com/mingyoung))
- Apply fixes from StyleCI [\#684](https://github.com/overtrue/wechat/pull/684) ([mingyoung](https://github.com/mingyoung))
- Apply fixes from StyleCI [\#683](https://github.com/overtrue/wechat/pull/683) ([mingyoung](https://github.com/mingyoung))
- Apply fixes from StyleCI [\#682](https://github.com/overtrue/wechat/pull/682) ([mingyoung](https://github.com/mingyoung))
- Apply fixes from StyleCI [\#681](https://github.com/overtrue/wechat/pull/681) ([mingyoung](https://github.com/mingyoung))
- Apply fixes from StyleCI [\#680](https://github.com/overtrue/wechat/pull/680) ([mingyoung](https://github.com/mingyoung))
- Apply fixes from StyleCI [\#679](https://github.com/overtrue/wechat/pull/679) ([mingyoung](https://github.com/mingyoung))
- Apply fixes from StyleCI [\#676](https://github.com/overtrue/wechat/pull/676) ([mingyoung](https://github.com/mingyoung))
- checks via composer. [\#673](https://github.com/overtrue/wechat/pull/673) ([mingyoung](https://github.com/mingyoung))
- Apply fixes from StyleCI [\#668](https://github.com/overtrue/wechat/pull/668) ([overtrue](https://github.com/overtrue))
- Correct payment sandbox endpoint and add a method to get sandbox sign key [\#666](https://github.com/overtrue/wechat/pull/666) ([skyred](https://github.com/skyred))

## [3.3.2](https://github.com/overtrue/wechat/tree/3.3.2) (2017-04-27)
[Full Changelog](https://github.com/overtrue/wechat/compare/3.3.1...3.3.2)

**Implemented enhancements:**

- \[4.0\] Open Platform 模块 [\#587](https://github.com/overtrue/wechat/issues/587)
- \[4.0\] 微信支付 sandbox模式 [\#507](https://github.com/overtrue/wechat/issues/507)

**Closed issues:**

- \[4.0\] staff 模块改名为 customer service [\#585](https://github.com/overtrue/wechat/issues/585)

**Merged pull requests:**

- Module rename. [\#664](https://github.com/overtrue/wechat/pull/664) ([mingyoung](https://github.com/mingyoung))
- Merge branch master into branch develop. [\#663](https://github.com/overtrue/wechat/pull/663) ([mingyoung](https://github.com/mingyoung))
- Apply fixes from StyleCI [\#662](https://github.com/overtrue/wechat/pull/662) ([mingyoung](https://github.com/mingyoung))
- Fix payment tools API [\#660](https://github.com/overtrue/wechat/pull/660) ([mingyoung](https://github.com/mingyoung))
- Avoid ambiguity [\#659](https://github.com/overtrue/wechat/pull/659) ([mingyoung](https://github.com/mingyoung))
- Support Payment Sandbox mode [\#658](https://github.com/overtrue/wechat/pull/658) ([skyred](https://github.com/skyred))
- Apply fixes from StyleCI [\#656](https://github.com/overtrue/wechat/pull/656) ([overtrue](https://github.com/overtrue))
- Mini program datacube. [\#655](https://github.com/overtrue/wechat/pull/655) ([mingyoung](https://github.com/mingyoung))

## [3.3.1](https://github.com/overtrue/wechat/tree/3.3.1) (2017-04-16)
[Full Changelog](https://github.com/overtrue/wechat/compare/3.3.0...3.3.1)

**Closed issues:**

- 微信第三方平台缓存位置，是否可以在配置文件中自定义 [\#648](https://github.com/overtrue/wechat/issues/648)
- 微信开放平台authorizer token缓存问题 [\#644](https://github.com/overtrue/wechat/issues/644)
- 微信开放平台发起网页授权bug [\#638](https://github.com/overtrue/wechat/issues/638)
- 微信公众号不能回复接收到的消息，日志无报错 [\#637](https://github.com/overtrue/wechat/issues/637)
- \[4.0\]黑名单管理 [\#538](https://github.com/overtrue/wechat/issues/538)

**Merged pull requests:**

- optimizes [\#652](https://github.com/overtrue/wechat/pull/652) ([mingyoung](https://github.com/mingyoung))

## [3.3.0](https://github.com/overtrue/wechat/tree/3.3.0) (2017-04-13)
[Full Changelog](https://github.com/overtrue/wechat/compare/3.2.7...3.3.0)

**Closed issues:**

- 微信接口获取openid是怎么排序的? [\#650](https://github.com/overtrue/wechat/issues/650)
- 缺少网页扫码支付接口 [\#647](https://github.com/overtrue/wechat/issues/647)
- 微信下的单的默认过期时间是多少啊 [\#645](https://github.com/overtrue/wechat/issues/645)
- 在获取用户信息是出错 [\#643](https://github.com/overtrue/wechat/issues/643)
- 调用$app =app\('wechat'\);时报错Use of undefined constant CURLOPT\_IPRESOLVE - assumed 'CURLOPT\_IPRESOLVE' [\#633](https://github.com/overtrue/wechat/issues/633)
- 提示找不到EasyWeChat\Server\Guard::setRequest\(\)方法 [\#626](https://github.com/overtrue/wechat/issues/626)
- 开放平台接收ComponentVerifyTicket,会出现Undefined index: FromUserName [\#623](https://github.com/overtrue/wechat/issues/623)
- 美国移动网络获取不到accessToken [\#610](https://github.com/overtrue/wechat/issues/610)
- 开放平台 APP 微信登录 [\#604](https://github.com/overtrue/wechat/issues/604)

**Merged pull requests:**

- Merge from open-platform branch. [\#651](https://github.com/overtrue/wechat/pull/651) ([mingyoung](https://github.com/mingyoung))
- Update code for open-platform [\#649](https://github.com/overtrue/wechat/pull/649) ([mingyoung](https://github.com/mingyoung))
- Code cleanup & refactoring. [\#646](https://github.com/overtrue/wechat/pull/646) ([mingyoung](https://github.com/mingyoung))
- support cash coupon [\#642](https://github.com/overtrue/wechat/pull/642) ([HanSon](https://github.com/HanSon))
- ♻️ All tests have been namespaced. [\#641](https://github.com/overtrue/wechat/pull/641) ([mingyoung](https://github.com/mingyoung))
- tweak code. [\#640](https://github.com/overtrue/wechat/pull/640) ([mingyoung](https://github.com/mingyoung))
- modify oauth property [\#639](https://github.com/overtrue/wechat/pull/639) ([jekst](https://github.com/jekst))
- Apply fixes from StyleCI [\#635](https://github.com/overtrue/wechat/pull/635) ([overtrue](https://github.com/overtrue))
- ✨ Blacklist. [\#634](https://github.com/overtrue/wechat/pull/634) ([mingyoung](https://github.com/mingyoung))
- 🔨 Refactoring for mini-program. [\#632](https://github.com/overtrue/wechat/pull/632) ([mingyoung](https://github.com/mingyoung))

## [3.2.7](https://github.com/overtrue/wechat/tree/3.2.7) (2017-03-31)
[Full Changelog](https://github.com/overtrue/wechat/compare/3.2.6...3.2.7)

**Closed issues:**

- 不管哪个公众号，只要填写 这个接口地址，都能配置或应用成功，实际上是不成功的，不到怎么找错。。 [\#611](https://github.com/overtrue/wechat/issues/611)

**Merged pull requests:**

- 修复一个创建卡券时的 bug, 添加获取微信门店类目表的api [\#631](https://github.com/overtrue/wechat/pull/631) ([Hexor](https://github.com/Hexor))

## [3.2.6](https://github.com/overtrue/wechat/tree/3.2.6) (2017-03-31)
[Full Changelog](https://github.com/overtrue/wechat/compare/3.2.5...3.2.6)

**Closed issues:**

- 我想大量发模板消息，但send每次都等待返回太慢，有啥解决办法吗？ [\#630](https://github.com/overtrue/wechat/issues/630)
- 3.2开放平台缺少authorizer\_token和authorization [\#629](https://github.com/overtrue/wechat/issues/629)
- 微信开发平台接受消息报Invalid request signature bug [\#625](https://github.com/overtrue/wechat/issues/625)
- 图文上传thumb\_media\_id 返回 {"errcode":40007,"errmsg":"invalid media\_id hint: \[\]"}  [\#622](https://github.com/overtrue/wechat/issues/622)
- Encryptor基类hack导致小程序的sessionKey base64\_decode失败 [\#614](https://github.com/overtrue/wechat/issues/614)
- 是否有 2.1 升级到最新版的方案？ [\#609](https://github.com/overtrue/wechat/issues/609)
- laravel5.3 安装 "overtrue/wechat:~3.1 失败 [\#607](https://github.com/overtrue/wechat/issues/607)
- overtrue/wechat和phpdoc包依赖冲突。 [\#605](https://github.com/overtrue/wechat/issues/605)
- \[bug\]2个问题 [\#597](https://github.com/overtrue/wechat/issues/597)
- 微信第三方平台开发是否只做了一部分？ [\#594](https://github.com/overtrue/wechat/issues/594)
- \[4.0\] ServiceProvider 移动到各自模块里 [\#588](https://github.com/overtrue/wechat/issues/588)
- Cannot use EasyWeChat\OpenPlatform\Traits\VerifyTicket as VerifyTicket because the name is already in use [\#579](https://github.com/overtrue/wechat/issues/579)
- 授权state值怎么设置 [\#573](https://github.com/overtrue/wechat/issues/573)
- mini\_app get jscode problem, report appid & secret value is null [\#569](https://github.com/overtrue/wechat/issues/569)
- 小程序生成二维码问题 [\#568](https://github.com/overtrue/wechat/issues/568)

**Merged pull requests:**

- Update OpenPlatform AppId [\#624](https://github.com/overtrue/wechat/pull/624) ([jeftom](https://github.com/jeftom))
- Apply fixes from StyleCI [\#621](https://github.com/overtrue/wechat/pull/621) ([overtrue](https://github.com/overtrue))
- Apply fixes from StyleCI [\#618](https://github.com/overtrue/wechat/pull/618) ([overtrue](https://github.com/overtrue))
- Compatible with php5.5 [\#617](https://github.com/overtrue/wechat/pull/617) ([mingyoung](https://github.com/mingyoung))
- Make the testcase works. [\#616](https://github.com/overtrue/wechat/pull/616) ([mingyoung](https://github.com/mingyoung))
- Fix mini-program decryptor [\#615](https://github.com/overtrue/wechat/pull/615) ([mingyoung](https://github.com/mingyoung))
- Missing message handling [\#613](https://github.com/overtrue/wechat/pull/613) ([mingyoung](https://github.com/mingyoung))
- Apply fixes from StyleCI [\#612](https://github.com/overtrue/wechat/pull/612) ([overtrue](https://github.com/overtrue))
- 添加卡券创建二维码接口 [\#608](https://github.com/overtrue/wechat/pull/608) ([forecho](https://github.com/forecho))
- 开放平台大幅重构并且添加测试 [\#606](https://github.com/overtrue/wechat/pull/606) ([tsunamilx](https://github.com/tsunamilx))
- Update MessageBuilder.php [\#603](https://github.com/overtrue/wechat/pull/603) ([U2Fsd](https://github.com/U2Fsd))
- 生成 js添加到卡包接口 增加fixed\_begintimestamp、outer\_str字段 [\#602](https://github.com/overtrue/wechat/pull/602) ([gychg](https://github.com/gychg))
- tests for speed [\#600](https://github.com/overtrue/wechat/pull/600) ([mingyoung](https://github.com/mingyoung))
- Update test files [\#599](https://github.com/overtrue/wechat/pull/599) ([mingyoung](https://github.com/mingyoung))
- 允许自定义ticket缓存key [\#598](https://github.com/overtrue/wechat/pull/598) ([XiaoLer](https://github.com/XiaoLer))
- delete top color [\#595](https://github.com/overtrue/wechat/pull/595) ([HanSon](https://github.com/HanSon))
- Add payment scan notify handler [\#593](https://github.com/overtrue/wechat/pull/593) ([acgrid](https://github.com/acgrid))
- Apply fixes from StyleCI [\#591](https://github.com/overtrue/wechat/pull/591) ([overtrue](https://github.com/overtrue))
- Upgrade packages version to 4.0 [\#590](https://github.com/overtrue/wechat/pull/590) ([reatang](https://github.com/reatang))
- Move providers to module dir. \#588 [\#589](https://github.com/overtrue/wechat/pull/589) ([overtrue](https://github.com/overtrue))
- 把OpenPlatform中的组件依赖解耦 [\#581](https://github.com/overtrue/wechat/pull/581) ([reatang](https://github.com/reatang))

## [3.2.5](https://github.com/overtrue/wechat/tree/3.2.5) (2017-02-04)
[Full Changelog](https://github.com/overtrue/wechat/compare/3.2.4...3.2.5)

**Merged pull requests:**

- fix naming [\#580](https://github.com/overtrue/wechat/pull/580) ([mingyoung](https://github.com/mingyoung))
- Allow client code configure its own GuzzleHTTP handler [\#578](https://github.com/overtrue/wechat/pull/578) ([acgrid](https://github.com/acgrid))

## [3.2.4](https://github.com/overtrue/wechat/tree/3.2.4) (2017-01-24)
[Full Changelog](https://github.com/overtrue/wechat/compare/3.2.3...3.2.4)

**Closed issues:**

- 如何在其他框架下使用$app-\>payment-\>handleNotify [\#574](https://github.com/overtrue/wechat/issues/574)
- 前后端分离单页下获取的config，认证失败 [\#565](https://github.com/overtrue/wechat/issues/565)
- 支付签名错误 [\#563](https://github.com/overtrue/wechat/issues/563)

**Merged pull requests:**

- Update Authorizer.php [\#577](https://github.com/overtrue/wechat/pull/577) ([ww380459000](https://github.com/ww380459000))
- 补全通用卡接口 [\#575](https://github.com/overtrue/wechat/pull/575) ([XiaoLer](https://github.com/XiaoLer))
- require ext-SimpleXML [\#572](https://github.com/overtrue/wechat/pull/572) ([garveen](https://github.com/garveen))
- fix README Contribution link [\#571](https://github.com/overtrue/wechat/pull/571) ([zhwei](https://github.com/zhwei))
- Add user data decryption. [\#570](https://github.com/overtrue/wechat/pull/570) ([mingyoung](https://github.com/mingyoung))
- change request parameter [\#567](https://github.com/overtrue/wechat/pull/567) ([cloudsthere](https://github.com/cloudsthere))
- 完善小程序代码 [\#566](https://github.com/overtrue/wechat/pull/566) ([mingyoung](https://github.com/mingyoung))
- 添加小程序支持 [\#564](https://github.com/overtrue/wechat/pull/564) ([mingyoung](https://github.com/mingyoung))

## [3.2.3](https://github.com/overtrue/wechat/tree/3.2.3) (2017-01-04)
[Full Changelog](https://github.com/overtrue/wechat/compare/3.2.2...3.2.3)

**Closed issues:**

- 文档里的自定义菜单中，group\_id是否为tag\_id的误写？ [\#561](https://github.com/overtrue/wechat/issues/561)
- Open Platform有简明的使用文档吗？3ks [\#560](https://github.com/overtrue/wechat/issues/560)
- 刷新access\_token有效期，未发现有相关的封装 [\#540](https://github.com/overtrue/wechat/issues/540)

**Merged pull requests:**

- Update Card.php [\#562](https://github.com/overtrue/wechat/pull/562) ([XiaoLer](https://github.com/XiaoLer))
- Apply fixes from StyleCI [\#559](https://github.com/overtrue/wechat/pull/559) ([overtrue](https://github.com/overtrue))
- Update API.php [\#558](https://github.com/overtrue/wechat/pull/558) ([drogjh](https://github.com/drogjh))
- optimized code [\#557](https://github.com/overtrue/wechat/pull/557) ([mingyoung](https://github.com/mingyoung))

## [3.2.2](https://github.com/overtrue/wechat/tree/3.2.2) (2016-12-27)
[Full Changelog](https://github.com/overtrue/wechat/compare/3.2.1...3.2.2)

**Closed issues:**

- How to get authorize url? [\#555](https://github.com/overtrue/wechat/issues/555)

**Merged pull requests:**

- fixed downloadBill method result [\#556](https://github.com/overtrue/wechat/pull/556) ([hidehalo](https://github.com/hidehalo))
- add config:log.permission for monolog [\#554](https://github.com/overtrue/wechat/pull/554) ([woshizoufeng](https://github.com/woshizoufeng))
- Improve open platform support. [\#553](https://github.com/overtrue/wechat/pull/553) ([mingyoung](https://github.com/mingyoung))
- Improve. [\#552](https://github.com/overtrue/wechat/pull/552) ([mingyoung](https://github.com/mingyoung))
- add $forceRefresh param to js-\>ticket\(\) method [\#551](https://github.com/overtrue/wechat/pull/551) ([leo108](https://github.com/leo108))

## [3.2.1](https://github.com/overtrue/wechat/tree/3.2.1) (2016-12-20)
[Full Changelog](https://github.com/overtrue/wechat/compare/3.2.0...3.2.1)

**Merged pull requests:**

- 增加小程序用jscode获取用户信息的接口 [\#550](https://github.com/overtrue/wechat/pull/550) ([soone](https://github.com/soone))

## [3.2.0](https://github.com/overtrue/wechat/tree/3.2.0) (2016-12-19)
[Full Changelog](https://github.com/overtrue/wechat/compare/3.1.9...3.2.0)

**Closed issues:**

- 喵喵喵 [\#545](https://github.com/overtrue/wechat/issues/545)
- HttpException with uploadArticle API [\#544](https://github.com/overtrue/wechat/issues/544)
- 是否有接入小程序的计划 [\#543](https://github.com/overtrue/wechat/issues/543)
- "Call to undefined method Overtrue\Socialite\Providers\WeChat Provider::driver\(\) [\#536](https://github.com/overtrue/wechat/issues/536)
- 服务端Server模块回复音乐消息出错 [\#533](https://github.com/overtrue/wechat/issues/533)
- 用户授权出现The key "access\_token" could not be empty [\#527](https://github.com/overtrue/wechat/issues/527)

**Merged pull requests:**

- Apply fixes from StyleCI [\#549](https://github.com/overtrue/wechat/pull/549) ([overtrue](https://github.com/overtrue))
- 添加摇一摇周边模块 [\#548](https://github.com/overtrue/wechat/pull/548) ([allen05ren](https://github.com/allen05ren))
- Make some compatible. [\#542](https://github.com/overtrue/wechat/pull/542) ([mingyoung](https://github.com/mingyoung))
- Apply fixes from StyleCI [\#541](https://github.com/overtrue/wechat/pull/541) ([overtrue](https://github.com/overtrue))
- 改变了http 中 json 方法的接口, 从而支持 添加 添加 query参数 [\#539](https://github.com/overtrue/wechat/pull/539) ([shoaly](https://github.com/shoaly))
- 提交 [\#537](https://github.com/overtrue/wechat/pull/537) ([shoaly](https://github.com/shoaly))
- Apply fixes from StyleCI [\#535](https://github.com/overtrue/wechat/pull/535) ([overtrue](https://github.com/overtrue))

## [3.1.9](https://github.com/overtrue/wechat/tree/3.1.9) (2016-12-01)
[Full Changelog](https://github.com/overtrue/wechat/compare/3.1.8...3.1.9)

**Closed issues:**

- 还是不懂怎么获取unionid [\#531](https://github.com/overtrue/wechat/issues/531)
- Scope 参数错误或没有 Scope 权限 [\#528](https://github.com/overtrue/wechat/issues/528)
- $\_SERVER\['SERVER\_ADDR'\] 在mac php7中获取不到 [\#520](https://github.com/overtrue/wechat/issues/520)
- 能否永久素材其他类型封装个download方法，跟临时一样 [\#505](https://github.com/overtrue/wechat/issues/505)
- V3.1 JSSDK使用疑惑 [\#503](https://github.com/overtrue/wechat/issues/503)
- 如何加入QQ群 [\#501](https://github.com/overtrue/wechat/issues/501)
- 能否在下一个版本把企业的相关接口整合集成进去 [\#496](https://github.com/overtrue/wechat/issues/496)
- 既然使用了monolog，那么在Application::initializeLogger只使用了文件流的特定形式来记录日志是否合理？ [\#494](https://github.com/overtrue/wechat/issues/494)
- configForShareAddress [\#482](https://github.com/overtrue/wechat/issues/482)
- 更新微信文章的时候MatialEasyWeChat\Material，如果设置了show\_pic\_cover和content\_source\_url不会生效 [\#470](https://github.com/overtrue/wechat/issues/470)
- 请问 SDK 是否支持授权接入的公众号接口调用？ [\#438](https://github.com/overtrue/wechat/issues/438)
- 通过unionid发送信息。 [\#411](https://github.com/overtrue/wechat/issues/411)
- 【新增】设备管理 [\#77](https://github.com/overtrue/wechat/issues/77)

**Merged pull requests:**

- Add support wechat open platform. [\#532](https://github.com/overtrue/wechat/pull/532) ([mingyoung](https://github.com/mingyoung))
- Applied fixes from StyleCI [\#530](https://github.com/overtrue/wechat/pull/530) ([overtrue](https://github.com/overtrue))
- 新增硬件设备api [\#529](https://github.com/overtrue/wechat/pull/529) ([soone](https://github.com/soone))

## [3.1.8](https://github.com/overtrue/wechat/tree/3.1.8) (2016-11-23)
[Full Changelog](https://github.com/overtrue/wechat/compare/3.1.7...3.1.8)

**Closed issues:**

- SCAN 事件会出现无法提供服务 [\#525](https://github.com/overtrue/wechat/issues/525)

## [3.1.7](https://github.com/overtrue/wechat/tree/3.1.7) (2016-10-26)
[Full Changelog](https://github.com/overtrue/wechat/compare/3.1.6...3.1.7)

**Closed issues:**

- preg\_replace unicode 的兼容问题 [\#515](https://github.com/overtrue/wechat/issues/515)

**Merged pull requests:**

- support psr-http-message-bridge 1.0 [\#524](https://github.com/overtrue/wechat/pull/524) ([wppd](https://github.com/wppd))
- Applied fixes from StyleCI [\#523](https://github.com/overtrue/wechat/pull/523) ([overtrue](https://github.com/overtrue))
- for \#520 [\#522](https://github.com/overtrue/wechat/pull/522) ([jinchun](https://github.com/jinchun))

## [3.1.6](https://github.com/overtrue/wechat/tree/3.1.6) (2016-10-19)
[Full Changelog](https://github.com/overtrue/wechat/compare/3.1.5...3.1.6)

**Closed issues:**

- PHP Fatal error: Uncaught HttpException [\#517](https://github.com/overtrue/wechat/issues/517)
- 微信支付回调出错 [\#514](https://github.com/overtrue/wechat/issues/514)

**Merged pull requests:**

- Fix xml preg replace [\#519](https://github.com/overtrue/wechat/pull/519) ([springjk](https://github.com/springjk))
- fix the DOC [\#518](https://github.com/overtrue/wechat/pull/518) ([ac1982](https://github.com/ac1982))

## [3.1.5](https://github.com/overtrue/wechat/tree/3.1.5) (2016-10-13)
[Full Changelog](https://github.com/overtrue/wechat/compare/3.1.4...3.1.5)

**Closed issues:**

- wechat 在 larave l5.3 使用 passport 包下无法安装 [\#513](https://github.com/overtrue/wechat/issues/513)

**Merged pull requests:**

- Applied fixes from StyleCI [\#512](https://github.com/overtrue/wechat/pull/512) ([overtrue](https://github.com/overtrue))

## [3.1.4](https://github.com/overtrue/wechat/tree/3.1.4) (2016-10-12)
[Full Changelog](https://github.com/overtrue/wechat/compare/2.1.39...3.1.4)

**Closed issues:**

- 微信卡券特殊票券创建之后为什么无法更新卡券信息一致提示code非法。 [\#511](https://github.com/overtrue/wechat/issues/511)
- 请添加 「退款方式」 参数 [\#509](https://github.com/overtrue/wechat/issues/509)
- 2.1.40命名空间巨变引发的重大问题\(疑似提错版本了\) [\#508](https://github.com/overtrue/wechat/issues/508)
- 卡券核销、查询建议 [\#506](https://github.com/overtrue/wechat/issues/506)
- 支付重复回调问题 [\#504](https://github.com/overtrue/wechat/issues/504)

**Merged pull requests:**

- Changed method doc to the right accepted param type [\#510](https://github.com/overtrue/wechat/pull/510) ([marianoasselborn](https://github.com/marianoasselborn))
- 增加判断是否有人工客服帐号，避免出现无账号时候，头像为默认头像的情况 [\#502](https://github.com/overtrue/wechat/pull/502) ([hello2t](https://github.com/hello2t))
- Applied fixes from StyleCI [\#500](https://github.com/overtrue/wechat/pull/500) ([overtrue](https://github.com/overtrue))
- 为initializeLogger日志初始话函数添加判断分支 [\#499](https://github.com/overtrue/wechat/pull/499) ([403studio](https://github.com/403studio))

## [2.1.39](https://github.com/overtrue/wechat/tree/2.1.39) (2016-09-05)
[Full Changelog](https://github.com/overtrue/wechat/compare/2.1.41...2.1.39)

## [2.1.41](https://github.com/overtrue/wechat/tree/2.1.41) (2016-09-05)
[Full Changelog](https://github.com/overtrue/wechat/compare/3.1.3...2.1.41)

**Closed issues:**

- 调用接口次数超过最大限制问题 [\#493](https://github.com/overtrue/wechat/issues/493)
- 微信退款证书报错 Unable to set private key file  [\#492](https://github.com/overtrue/wechat/issues/492)
- 微信支付存在问题 [\#489](https://github.com/overtrue/wechat/issues/489)
- 预支付下单 response body 为空 [\#488](https://github.com/overtrue/wechat/issues/488)
- https check issue [\#486](https://github.com/overtrue/wechat/issues/486)

**Merged pull requests:**

- update composer.json [\#498](https://github.com/overtrue/wechat/pull/498) ([ac1982](https://github.com/ac1982))
- use openssl instead of mcrypt [\#497](https://github.com/overtrue/wechat/pull/497) ([ac1982](https://github.com/ac1982))
- 修复 with 方法带数据的问题 [\#491](https://github.com/overtrue/wechat/pull/491) ([XiaoLer](https://github.com/XiaoLer))

## [3.1.3](https://github.com/overtrue/wechat/tree/3.1.3) (2016-08-08)
[Full Changelog](https://github.com/overtrue/wechat/compare/3.1.2...3.1.3)

**Closed issues:**

- Laravel中写的最简单的例子在phpunit出错。 [\#485](https://github.com/overtrue/wechat/issues/485)
- 微信的消息回复的FromUserName和ToUserName是不是对调了 [\#484](https://github.com/overtrue/wechat/issues/484)
- 微信红包不能发给别的公众号的用户吗 [\#483](https://github.com/overtrue/wechat/issues/483)
- 用户授权登录问题 [\#481](https://github.com/overtrue/wechat/issues/481)
- cURL error 56: SSLRead\(\) return error -9806 [\#473](https://github.com/overtrue/wechat/issues/473)
- 会员卡开卡字段文档有错误 [\#471](https://github.com/overtrue/wechat/issues/471)
- Getting more done in GitHub with ZenHub [\#439](https://github.com/overtrue/wechat/issues/439)
- 微信支付下单错误 [\#376](https://github.com/overtrue/wechat/issues/376)

**Merged pull requests:**

- update the File class to recognize pdf file. [\#480](https://github.com/overtrue/wechat/pull/480) ([ac1982](https://github.com/ac1982))
- update testActivateUserForm [\#478](https://github.com/overtrue/wechat/pull/478) ([wangniuniu](https://github.com/wangniuniu))
- Scrutinizer Auto-Fixes [\#477](https://github.com/overtrue/wechat/pull/477) ([scrutinizer-auto-fixer](https://github.com/scrutinizer-auto-fixer))
- Applied fixes from StyleCI [\#476](https://github.com/overtrue/wechat/pull/476) ([overtrue](https://github.com/overtrue))
- Scrutinizer Auto-Fixes [\#475](https://github.com/overtrue/wechat/pull/475) ([scrutinizer-auto-fixer](https://github.com/scrutinizer-auto-fixer))
- 开放自定义prefix和缓存键值方法 [\#474](https://github.com/overtrue/wechat/pull/474) ([XiaoLer](https://github.com/XiaoLer))
- Applied fixes from StyleCI [\#469](https://github.com/overtrue/wechat/pull/469) ([overtrue](https://github.com/overtrue))
- modify stats [\#468](https://github.com/overtrue/wechat/pull/468) ([wangniuniu](https://github.com/wangniuniu))

## [3.1.2](https://github.com/overtrue/wechat/tree/3.1.2) (2016-07-21)
[Full Changelog](https://github.com/overtrue/wechat/compare/2.1.38...3.1.2)

**Closed issues:**

- 素材管理中，上传图文下的上传图片，关于返回内容的差异 [\#466](https://github.com/overtrue/wechat/issues/466)
- spbill\_create\_ip参数设置 [\#461](https://github.com/overtrue/wechat/issues/461)

**Merged pull requests:**

- 更新获取标签下粉丝列表方法 [\#467](https://github.com/overtrue/wechat/pull/467) ([dingdayu](https://github.com/dingdayu))
- Applied fixes from StyleCI [\#465](https://github.com/overtrue/wechat/pull/465) ([overtrue](https://github.com/overtrue))
- card module. [\#464](https://github.com/overtrue/wechat/pull/464) ([wangniuniu](https://github.com/wangniuniu))
- Applied fixes from StyleCI [\#463](https://github.com/overtrue/wechat/pull/463) ([overtrue](https://github.com/overtrue))
- Scrutinizer Auto-Fixes [\#462](https://github.com/overtrue/wechat/pull/462) ([scrutinizer-auto-fixer](https://github.com/scrutinizer-auto-fixer))

## [2.1.38](https://github.com/overtrue/wechat/tree/2.1.38) (2016-07-16)
[Full Changelog](https://github.com/overtrue/wechat/compare/3.1.1...2.1.38)

**Closed issues:**

- 请问卡券管理功能整合上日程表了吗 [\#454](https://github.com/overtrue/wechat/issues/454)

**Merged pull requests:**

- Typo. [\#460](https://github.com/overtrue/wechat/pull/460) ([tianyong90](https://github.com/tianyong90))
- Applied fixes from StyleCI [\#459](https://github.com/overtrue/wechat/pull/459) ([overtrue](https://github.com/overtrue))
- add voice recognition [\#458](https://github.com/overtrue/wechat/pull/458) ([leniy](https://github.com/leniy))
- Applied fixes from StyleCI [\#457](https://github.com/overtrue/wechat/pull/457) ([overtrue](https://github.com/overtrue))
- Update API.php [\#456](https://github.com/overtrue/wechat/pull/456) ([marvin8212](https://github.com/marvin8212))
- Update XML.php [\#455](https://github.com/overtrue/wechat/pull/455) ([canon4ever](https://github.com/canon4ever))

## [3.1.1](https://github.com/overtrue/wechat/tree/3.1.1) (2016-07-12)
[Full Changelog](https://github.com/overtrue/wechat/compare/3.1.0...3.1.1)

**Closed issues:**

- 拿到code=CODE&state=STATE之后怎么拿到openid? [\#452](https://github.com/overtrue/wechat/issues/452)
- 安装出错 [\#450](https://github.com/overtrue/wechat/issues/450)
- 自定义菜单接口\(新版\)出错 [\#448](https://github.com/overtrue/wechat/issues/448)
- h5上没法打开微信app授权界面 [\#447](https://github.com/overtrue/wechat/issues/447)
- 重构卡券 [\#76](https://github.com/overtrue/wechat/issues/76)

**Merged pull requests:**

- typos. [\#453](https://github.com/overtrue/wechat/pull/453) ([tianye](https://github.com/tianye))
- edit readme.md [\#451](https://github.com/overtrue/wechat/pull/451) ([tianyong90](https://github.com/tianyong90))
- Add cache driver config. [\#449](https://github.com/overtrue/wechat/pull/449) ([dingdayu](https://github.com/dingdayu))

## [3.1.0](https://github.com/overtrue/wechat/tree/3.1.0) (2016-06-28)
[Full Changelog](https://github.com/overtrue/wechat/compare/3.0.21...3.1.0)

**Merged pull requests:**

- Applied fixes from StyleCI [\#446](https://github.com/overtrue/wechat/pull/446) ([overtrue](https://github.com/overtrue))
- New Staff API. [\#445](https://github.com/overtrue/wechat/pull/445) ([overtrue](https://github.com/overtrue))
- 2.1 [\#444](https://github.com/overtrue/wechat/pull/444) ([dongnanyanhai](https://github.com/dongnanyanhai))
- Fix path. [\#443](https://github.com/overtrue/wechat/pull/443) ([overtrue](https://github.com/overtrue))

## [3.0.21](https://github.com/overtrue/wechat/tree/3.0.21) (2016-06-17)
[Full Changelog](https://github.com/overtrue/wechat/compare/3.0.1...3.0.21)

**Closed issues:**

- scan出现公众号暂时无法服务的消息 [\#436](https://github.com/overtrue/wechat/issues/436)
- scan出现公众号暂时无法服务的消息 [\#435](https://github.com/overtrue/wechat/issues/435)
- 用户标签接口无法使用 [\#433](https://github.com/overtrue/wechat/issues/433)
- WeChatProvider下的getAuthUrl个人觉得应该暴露出来 [\#432](https://github.com/overtrue/wechat/issues/432)
- 支持二维码扫描进入公众号推送的SCAN事件 [\#431](https://github.com/overtrue/wechat/issues/431)
- \[3.0\] EasyWeChat\Support\XML::parse方法会将空节点解析为空数组，而不是空字符串 [\#426](https://github.com/overtrue/wechat/issues/426)
- 下载二维码, $qrcode-\>download\($ticket,$paths\);  目录参数不可加入 中文 [\#420](https://github.com/overtrue/wechat/issues/420)
- \[help want\]Is hard to change default configuration of GuzzleHttp [\#415](https://github.com/overtrue/wechat/issues/415)
- PHP7.0 curl\_setopt 设置问题 [\#413](https://github.com/overtrue/wechat/issues/413)
- 无法通知微信支付完成 [\#412](https://github.com/overtrue/wechat/issues/412)
- 如何获取用户的unionid? [\#407](https://github.com/overtrue/wechat/issues/407)
- 是否支持多框架 [\#406](https://github.com/overtrue/wechat/issues/406)
- fuckTheWeChatInvalidJSON [\#405](https://github.com/overtrue/wechat/issues/405)
- Class 'GuzzleHttp\Middleware' not found [\#404](https://github.com/overtrue/wechat/issues/404)
- 支付统一下单接口签名错误 [\#402](https://github.com/overtrue/wechat/issues/402)
- payment里没有configForJSSDKPayment方法 [\#401](https://github.com/overtrue/wechat/issues/401)
- 查询支付的地址多了一个空格，导致查询失败，去掉最后的那个空格后就好了 [\#393](https://github.com/overtrue/wechat/issues/393)
- 网页授权过不了 [\#392](https://github.com/overtrue/wechat/issues/392)
- 微信AccessToken被动更新可能会有并发更新的情况出现 [\#390](https://github.com/overtrue/wechat/issues/390)
- 临时素材下载，文件名和扩展名之间会有2个\[.\] [\#389](https://github.com/overtrue/wechat/issues/389)
- 有一个地方变量名对不上 [\#380](https://github.com/overtrue/wechat/issues/380)
- 自定义缓存 [\#379](https://github.com/overtrue/wechat/issues/379)
- https://easywechat.org/ 底部 “开始使用” url拼错 [\#378](https://github.com/overtrue/wechat/issues/378)
- 在server.php里面调用yii的model，一直报错 [\#375](https://github.com/overtrue/wechat/issues/375)
- overture/wechat 2.1.36\(客服消息转发错误\) [\#374](https://github.com/overtrue/wechat/issues/374)
- 建议支持开发模式下禁用验证 [\#373](https://github.com/overtrue/wechat/issues/373)
- https://easywechat.org/ 导航 首页 about:blank [\#370](https://github.com/overtrue/wechat/issues/370)
- laravel 下session问题 [\#369](https://github.com/overtrue/wechat/issues/369)
- 关于Access——toekn [\#368](https://github.com/overtrue/wechat/issues/368)
- 返回支付页面时报错："access\_token" could not be empty [\#367](https://github.com/overtrue/wechat/issues/367)
- xampp下js-\>config报错 [\#366](https://github.com/overtrue/wechat/issues/366)
- 官方文档有误 [\#360](https://github.com/overtrue/wechat/issues/360)
- \[BUG\] 微信收货地址无法成功 [\#359](https://github.com/overtrue/wechat/issues/359)
- 无法获取 $message-\>ScanCodeInfo-\>ScanType 对象 [\#358](https://github.com/overtrue/wechat/issues/358)
- \[Bugs\] 项目文档首页跳转问题 [\#357](https://github.com/overtrue/wechat/issues/357)
- Business和UnifiedOrder没有定义 [\#356](https://github.com/overtrue/wechat/issues/356)
- 你的网站访问不了。。。。https://easywechat.org/ [\#352](https://github.com/overtrue/wechat/issues/352)
- 连续多次执行微信支付退款报错 [\#348](https://github.com/overtrue/wechat/issues/348)
- 客服操作 都是  -1 错误 [\#344](https://github.com/overtrue/wechat/issues/344)
- 请使用openssl 而不是不安全的mcrypt来加密 [\#342](https://github.com/overtrue/wechat/issues/342)
- 文本类型的通知消息 [\#341](https://github.com/overtrue/wechat/issues/341)
- 服务器配置https 并且 通过阿里云 https cdn之后, 会出现 https 判断语句失效 [\#338](https://github.com/overtrue/wechat/issues/338)
- 作者请问者个sdk支持企业号吗? [\#336](https://github.com/overtrue/wechat/issues/336)
- laravel 5.1引入包报错 [\#331](https://github.com/overtrue/wechat/issues/331)
- 申请退款有问题 [\#328](https://github.com/overtrue/wechat/issues/328)
- 订单相关接口bug [\#327](https://github.com/overtrue/wechat/issues/327)
- 临时素材接口无法使用 [\#319](https://github.com/overtrue/wechat/issues/319)
- 使用sendNormal\(\),sendGroup\(\)发送红包时，报Undefined index: HTTP\_CLIENT\_IP [\#316](https://github.com/overtrue/wechat/issues/316)
- v3中微信卡券功能缺失？ [\#307](https://github.com/overtrue/wechat/issues/307)
- 测试 [\#305](https://github.com/overtrue/wechat/issues/305)
- \[3.0\] 永久素材上传视频无法上传问题 [\#304](https://github.com/overtrue/wechat/issues/304)
- Cannot destroy active lambda function [\#296](https://github.com/overtrue/wechat/issues/296)
- 微信支付-》企业付款也可以增加个类上去，跟企业红包类似 [\#232](https://github.com/overtrue/wechat/issues/232)

**Merged pull requests:**

- Applied fixes from StyleCI [\#442](https://github.com/overtrue/wechat/pull/442) ([overtrue](https://github.com/overtrue))
- NGINX HTTPS无法签名 [\#441](https://github.com/overtrue/wechat/pull/441) ([ares333](https://github.com/ares333))
- Develop [\#440](https://github.com/overtrue/wechat/pull/440) ([overtrue](https://github.com/overtrue))
- Develop [\#437](https://github.com/overtrue/wechat/pull/437) ([overtrue](https://github.com/overtrue))
- Applied fixes from StyleCI [\#434](https://github.com/overtrue/wechat/pull/434) ([overtrue](https://github.com/overtrue))
- 修改错误提示信息，方便跟踪错误 [\#430](https://github.com/overtrue/wechat/pull/430) ([zerozh](https://github.com/zerozh))
- Develop [\#429](https://github.com/overtrue/wechat/pull/429) ([overtrue](https://github.com/overtrue))
- Applied fixes from StyleCI [\#428](https://github.com/overtrue/wechat/pull/428) ([overtrue](https://github.com/overtrue))
- Applied fixes from StyleCI [\#427](https://github.com/overtrue/wechat/pull/427) ([overtrue](https://github.com/overtrue))
- Applied fixes from StyleCI [\#425](https://github.com/overtrue/wechat/pull/425) ([overtrue](https://github.com/overtrue))
- update annotation [\#424](https://github.com/overtrue/wechat/pull/424) ([lilocon](https://github.com/lilocon))
- Develop [\#421](https://github.com/overtrue/wechat/pull/421) ([overtrue](https://github.com/overtrue))
- Set default timeout. [\#419](https://github.com/overtrue/wechat/pull/419) ([overtrue](https://github.com/overtrue))
- Develop [\#418](https://github.com/overtrue/wechat/pull/418) ([overtrue](https://github.com/overtrue))
- Develop [\#416](https://github.com/overtrue/wechat/pull/416) ([overtrue](https://github.com/overtrue))
- better implementation for prepare oauth callback url [\#414](https://github.com/overtrue/wechat/pull/414) ([lichunqiang](https://github.com/lichunqiang))
- Develop [\#410](https://github.com/overtrue/wechat/pull/410) ([overtrue](https://github.com/overtrue))
- Applied fixes from StyleCI [\#409](https://github.com/overtrue/wechat/pull/409) ([overtrue](https://github.com/overtrue))
- 增加微信支付服务商支持 [\#408](https://github.com/overtrue/wechat/pull/408) ([takatost](https://github.com/takatost))
- Develop [\#403](https://github.com/overtrue/wechat/pull/403) ([overtrue](https://github.com/overtrue))
- Applied fixes from StyleCI [\#400](https://github.com/overtrue/wechat/pull/400) ([overtrue](https://github.com/overtrue))
- Scrutinizer Auto-Fixes [\#399](https://github.com/overtrue/wechat/pull/399) ([scrutinizer-auto-fixer](https://github.com/scrutinizer-auto-fixer))
- Develop [\#398](https://github.com/overtrue/wechat/pull/398) ([overtrue](https://github.com/overtrue))
- Develop [\#397](https://github.com/overtrue/wechat/pull/397) ([overtrue](https://github.com/overtrue))
- Applied fixes from StyleCI [\#396](https://github.com/overtrue/wechat/pull/396) ([overtrue](https://github.com/overtrue))
- Typo & Improve code. [\#395](https://github.com/overtrue/wechat/pull/395) ([jinchun](https://github.com/jinchun))
- Develop [\#394](https://github.com/overtrue/wechat/pull/394) ([overtrue](https://github.com/overtrue))
- Bugfix close \#389 [\#391](https://github.com/overtrue/wechat/pull/391) ([overtrue](https://github.com/overtrue))
- Update NoticeNoticeTest.php [\#388](https://github.com/overtrue/wechat/pull/388) ([xiabeifeng](https://github.com/xiabeifeng))
- Update Notice.php [\#387](https://github.com/overtrue/wechat/pull/387) ([xiabeifeng](https://github.com/xiabeifeng))
- Tests for \#384 [\#386](https://github.com/overtrue/wechat/pull/386) ([xiabeifeng](https://github.com/xiabeifeng))
- Improve Notice API. [\#384](https://github.com/overtrue/wechat/pull/384) ([xiabeifeng](https://github.com/xiabeifeng))
- 对应根 版本依赖 [\#382](https://github.com/overtrue/wechat/pull/382) ([parkshinhye](https://github.com/parkshinhye))
- Develop [\#381](https://github.com/overtrue/wechat/pull/381) ([overtrue](https://github.com/overtrue))
- Develop [\#377](https://github.com/overtrue/wechat/pull/377) ([overtrue](https://github.com/overtrue))
- Fix test for \#371 [\#372](https://github.com/overtrue/wechat/pull/372) ([overtrue](https://github.com/overtrue))
- 刷卡支付不需要notify\_url参数 [\#371](https://github.com/overtrue/wechat/pull/371) ([lilocon](https://github.com/lilocon))
- Applied fixes from StyleCI [\#365](https://github.com/overtrue/wechat/pull/365) ([overtrue](https://github.com/overtrue))
- Applied fixes from StyleCI [\#364](https://github.com/overtrue/wechat/pull/364) ([overtrue](https://github.com/overtrue))
- Merge Develop [\#363](https://github.com/overtrue/wechat/pull/363) ([overtrue](https://github.com/overtrue))
- Update composer.json [\#361](https://github.com/overtrue/wechat/pull/361) ([jaychan](https://github.com/jaychan))
- Applied fixes from StyleCI [\#355](https://github.com/overtrue/wechat/pull/355) ([overtrue](https://github.com/overtrue))
- \[ci skip\]fix document typo [\#354](https://github.com/overtrue/wechat/pull/354) ([lichunqiang](https://github.com/lichunqiang))
- 自定义Logger [\#353](https://github.com/overtrue/wechat/pull/353) ([lilocon](https://github.com/lilocon))
- Update Refund.php [\#351](https://github.com/overtrue/wechat/pull/351) ([jaring](https://github.com/jaring))
- Applied fixes from StyleCI [\#350](https://github.com/overtrue/wechat/pull/350) ([overtrue](https://github.com/overtrue))
- OpenSSL bugfix. [\#349](https://github.com/overtrue/wechat/pull/349) ([overtrue](https://github.com/overtrue))
- Applied fixes from StyleCI [\#347](https://github.com/overtrue/wechat/pull/347) ([overtrue](https://github.com/overtrue))
- Applied fixes from StyleCI [\#346](https://github.com/overtrue/wechat/pull/346) ([overtrue](https://github.com/overtrue))
- Merge Develop [\#345](https://github.com/overtrue/wechat/pull/345) ([overtrue](https://github.com/overtrue))
- 添加代码提示 [\#343](https://github.com/overtrue/wechat/pull/343) ([lilocon](https://github.com/lilocon))
- Applied fixes from StyleCI [\#340](https://github.com/overtrue/wechat/pull/340) ([overtrue](https://github.com/overtrue))
- Fix bug: Payment::downloadBill\(\) response error. [\#339](https://github.com/overtrue/wechat/pull/339) ([overtrue](https://github.com/overtrue))
- change get\_client\_ip to get\_server\_ip [\#335](https://github.com/overtrue/wechat/pull/335) ([tianyong90](https://github.com/tianyong90))
- Payment SSL. [\#334](https://github.com/overtrue/wechat/pull/334) ([overtrue](https://github.com/overtrue))
- Add a helper to get correct client ip address. fixed \#316 [\#333](https://github.com/overtrue/wechat/pull/333) ([tianyong90](https://github.com/tianyong90))
- Dependency Bugfix. overtrue/laravel-wechat\#24 [\#332](https://github.com/overtrue/wechat/pull/332) ([overtrue](https://github.com/overtrue))
- Applied fixes from StyleCI [\#330](https://github.com/overtrue/wechat/pull/330) ([overtrue](https://github.com/overtrue))
- Merge Develop [\#329](https://github.com/overtrue/wechat/pull/329) ([overtrue](https://github.com/overtrue))
- Applied fixes from StyleCI [\#326](https://github.com/overtrue/wechat/pull/326) ([overtrue](https://github.com/overtrue))
- Add order default notify\_url. [\#325](https://github.com/overtrue/wechat/pull/325) ([foreverglory](https://github.com/foreverglory))
- Revert "Applied fixes from StyleCI" [\#323](https://github.com/overtrue/wechat/pull/323) ([overtrue](https://github.com/overtrue))
- Applied fixes from StyleCI [\#322](https://github.com/overtrue/wechat/pull/322) ([overtrue](https://github.com/overtrue))
- Develop [\#321](https://github.com/overtrue/wechat/pull/321) ([overtrue](https://github.com/overtrue))
- Applied fixes from StyleCI [\#320](https://github.com/overtrue/wechat/pull/320) ([overtrue](https://github.com/overtrue))
- 模板消息添加【 获取模板列表】和【 删除模板】接口 [\#318](https://github.com/overtrue/wechat/pull/318) ([forecho](https://github.com/forecho))
- Applied fixes from StyleCI [\#314](https://github.com/overtrue/wechat/pull/314) ([overtrue](https://github.com/overtrue))
- fix Temporary upload bug [\#313](https://github.com/overtrue/wechat/pull/313) ([mani95lisa](https://github.com/mani95lisa))
- Applied fixes from StyleCI [\#312](https://github.com/overtrue/wechat/pull/312) ([overtrue](https://github.com/overtrue))
- MerchantPay Class [\#311](https://github.com/overtrue/wechat/pull/311) ([ac1982](https://github.com/ac1982))
- Applied fixes from StyleCI [\#309](https://github.com/overtrue/wechat/pull/309) ([overtrue](https://github.com/overtrue))
- Merge Develop [\#308](https://github.com/overtrue/wechat/pull/308) ([overtrue](https://github.com/overtrue))
- 删除裂变红包接口中的ip参数 [\#306](https://github.com/overtrue/wechat/pull/306) ([xjchengo](https://github.com/xjchengo))
- fix code style and some spelling mistakes [\#303](https://github.com/overtrue/wechat/pull/303) ([jinchun](https://github.com/jinchun))
- Merge Develop [\#302](https://github.com/overtrue/wechat/pull/302) ([overtrue](https://github.com/overtrue))
- Add method for app payment [\#301](https://github.com/overtrue/wechat/pull/301) ([lichunqiang](https://github.com/lichunqiang))
- Removed the return syntax [\#300](https://github.com/overtrue/wechat/pull/300) ([lichunqiang](https://github.com/lichunqiang))
- add return tag [\#299](https://github.com/overtrue/wechat/pull/299) ([lichunqiang](https://github.com/lichunqiang))
- Merge Develop [\#298](https://github.com/overtrue/wechat/pull/298) ([overtrue](https://github.com/overtrue))
- Applied fixes from StyleCI [\#297](https://github.com/overtrue/wechat/pull/297) ([overtrue](https://github.com/overtrue))
- \[ci skip\]Update .gitattributes [\#295](https://github.com/overtrue/wechat/pull/295) ([lichunqiang](https://github.com/lichunqiang))
- Merge Develop [\#294](https://github.com/overtrue/wechat/pull/294) ([overtrue](https://github.com/overtrue))

## [3.0.1](https://github.com/overtrue/wechat/tree/3.0.1) (2016-02-19)
[Full Changelog](https://github.com/overtrue/wechat/compare/3.0...3.0.1)

**Closed issues:**

- composer 安装 3.0版本，报错如下： [\#291](https://github.com/overtrue/wechat/issues/291)
- \[3.0\] 下载永久素材时，微信返回的Content-Type不正确，导致出错。 [\#290](https://github.com/overtrue/wechat/issues/290)
- 挖个坑，自己跳 [\#147](https://github.com/overtrue/wechat/issues/147)

**Merged pull requests:**

- Applied fixes from StyleCI [\#293](https://github.com/overtrue/wechat/pull/293) ([overtrue](https://github.com/overtrue))
- Merge Develop [\#292](https://github.com/overtrue/wechat/pull/292) ([overtrue](https://github.com/overtrue))

## [3.0](https://github.com/overtrue/wechat/tree/3.0) (2016-02-17)
[Full Changelog](https://github.com/overtrue/wechat/compare/2.1.0...3.0)

**Implemented enhancements:**

- MIME json 格式检查优化 [\#49](https://github.com/overtrue/wechat/issues/49)
- 获取 refresh\_token，access\_token [\#43](https://github.com/overtrue/wechat/issues/43)
- 关于API\_TOKEN\_REFRESH [\#20](https://github.com/overtrue/wechat/issues/20)

**Closed issues:**

- \[3.0\] 无法获取用户分组信息 [\#285](https://github.com/overtrue/wechat/issues/285)
- 新的laravel 5.2 不能兼容了 [\#284](https://github.com/overtrue/wechat/issues/284)
- \[3.0\]Message/Article类的$properties内的source\_url没有正常转换为content\_source\_url. [\#281](https://github.com/overtrue/wechat/issues/281)
- 3.0删除个性菜单失败 [\#280](https://github.com/overtrue/wechat/issues/280)
- 也许你该给一个代码贡献规范 [\#277](https://github.com/overtrue/wechat/issues/277)
- 3.0网页授权时scope为snsapi\_base得不到openid [\#276](https://github.com/overtrue/wechat/issues/276)
- wechat3.0中 有2个地方的js调用参数不一样，超哥没有提供 [\#272](https://github.com/overtrue/wechat/issues/272)
- 我想知道2.X和3.0有什么大的区别！ [\#270](https://github.com/overtrue/wechat/issues/270)
- 2.1： Link 消息类型没有实现 [\#269](https://github.com/overtrue/wechat/issues/269)
- 关于模板消息换行的问题 [\#266](https://github.com/overtrue/wechat/issues/266)
- easywechat Invalid request [\#265](https://github.com/overtrue/wechat/issues/265)
- 40029不合法的oauth\_code [\#264](https://github.com/overtrue/wechat/issues/264)
- 下载素材的一个小问题 [\#263](https://github.com/overtrue/wechat/issues/263)
- \[2.1\] 微信自定义菜单结构变更导致`Menu::get\(\)` 无法读取个性化菜单 [\#262](https://github.com/overtrue/wechat/issues/262)
- payment中是不是不包含H5和JS的生成配置文件的方法了？ [\#261](https://github.com/overtrue/wechat/issues/261)
- payment下prepare方法bug [\#260](https://github.com/overtrue/wechat/issues/260)
- UserServiceProvider中似乎忘记注册user.group了 [\#256](https://github.com/overtrue/wechat/issues/256)
- 2.1.X版媒体下载没有扩展名 [\#252](https://github.com/overtrue/wechat/issues/252)
- 为什么所有的子模块在自己的库都是develop分支 [\#247](https://github.com/overtrue/wechat/issues/247)
- 网页授权使用跳转的bug [\#246](https://github.com/overtrue/wechat/issues/246)
- typo of variable [\#245](https://github.com/overtrue/wechat/issues/245)
- The implementation class of ServerServiceProvider missing an important  [\#244](https://github.com/overtrue/wechat/issues/244)
- \[3.0\]\[payment\] 两个可能的bug [\#235](https://github.com/overtrue/wechat/issues/235)
- 发送多图文 [\#233](https://github.com/overtrue/wechat/issues/233)
- 自定义菜单返回应该把个性化自定义菜单也一起返回 [\#231](https://github.com/overtrue/wechat/issues/231)
- 发送模板消息 CRUL 错误 [\#223](https://github.com/overtrue/wechat/issues/223)
- 客服接口暂时测到有3个bug，麻烦修复 [\#222](https://github.com/overtrue/wechat/issues/222)
- JSSDK access\_token missing [\#211](https://github.com/overtrue/wechat/issues/211)
- Js.php/ticket [\#210](https://github.com/overtrue/wechat/issues/210)
- 微信支付里有一个收货地址共享 ,超哥你这里没有,可以加一下不? [\#204](https://github.com/overtrue/wechat/issues/204)
- 小问题 [\#203](https://github.com/overtrue/wechat/issues/203)
- 网页授权 跳转 [\#202](https://github.com/overtrue/wechat/issues/202)
- access token 重复添加的问题 [\#201](https://github.com/overtrue/wechat/issues/201)
- authorize snsapi\_base 下可以获取unionid [\#198](https://github.com/overtrue/wechat/issues/198)
- 网页授权 [\#189](https://github.com/overtrue/wechat/issues/189)
- 一点建议 [\#188](https://github.com/overtrue/wechat/issues/188)
- 接口更新-新增临时素材接口变动 [\#186](https://github.com/overtrue/wechat/issues/186)
- 接入多个公众号不用id [\#185](https://github.com/overtrue/wechat/issues/185)
- \[Insight\] Files should not be executable [\#184](https://github.com/overtrue/wechat/issues/184)
- 建议不要写死Http [\#183](https://github.com/overtrue/wechat/issues/183)
- laravel4.2安装不成功 [\#182](https://github.com/overtrue/wechat/issues/182)
- 是否支持laravel4.2 [\#181](https://github.com/overtrue/wechat/issues/181)
- 微信出个性化菜单了，希望支持 [\#180](https://github.com/overtrue/wechat/issues/180)
- 3.0 composer依赖Symfony2.7。能不能支持Symfony3.0? [\#179](https://github.com/overtrue/wechat/issues/179)
- 发送链接类消息错误 [\#175](https://github.com/overtrue/wechat/issues/175)
- Throw Exception的时候 Intel server status 设置为200是不是好一些 [\#174](https://github.com/overtrue/wechat/issues/174)
- 生成临时二维码时，返回EventKey不是传递的值 [\#173](https://github.com/overtrue/wechat/issues/173)
- 关于素材获取的一个建议 [\#172](https://github.com/overtrue/wechat/issues/172)
- 能否增加微信APP支付相关方法 [\#171](https://github.com/overtrue/wechat/issues/171)
- 微信回调URL回调不到 [\#170](https://github.com/overtrue/wechat/issues/170)
- 素材管理添加永久素材返回JSON/XML内容错误 [\#169](https://github.com/overtrue/wechat/issues/169)
- \[消息的使用\] 中 \[上传素材文件\] 的文档示例貌似有误 [\#168](https://github.com/overtrue/wechat/issues/168)
- 素材管理里的download方法不是很符合sdk一站式的解决. [\#165](https://github.com/overtrue/wechat/issues/165)
- \[Wechat\]不合法的oauth\_code' in /src/Wechat/Http.php:124 [\#164](https://github.com/overtrue/wechat/issues/164)
- AccessToken Expired Error Code [\#163](https://github.com/overtrue/wechat/issues/163)
- 素材管理接口出错 [\#162](https://github.com/overtrue/wechat/issues/162)
- 两处代码php5.4才能运行 [\#158](https://github.com/overtrue/wechat/issues/158)
- extension is null when calling `download video` in wechat.media [\#157](https://github.com/overtrue/wechat/issues/157)
- Payment/UnifiedOrder does not support serialize or create by array [\#155](https://github.com/overtrue/wechat/issues/155)
- 没有找到"微信支付-\>查询订单"相关功能 [\#150](https://github.com/overtrue/wechat/issues/150)
- 请教，Cache::setter中your\_custom\_set\_cache怎么使用 [\#149](https://github.com/overtrue/wechat/issues/149)
- 发生异常时, 希望能把发送和接收的原始数据记录下来. [\#148](https://github.com/overtrue/wechat/issues/148)
- 发送红包，证书错误 [\#144](https://github.com/overtrue/wechat/issues/144)
- 发视频消息总返回 -1 [\#143](https://github.com/overtrue/wechat/issues/143)
- 关于PHP版本 [\#141](https://github.com/overtrue/wechat/issues/141)
- Server消息回复必须以事件方式吗？ [\#140](https://github.com/overtrue/wechat/issues/140)
- 微信支付相关文档细化 [\#138](https://github.com/overtrue/wechat/issues/138)
- 好奇地问个问题，这项目的测试用例放在哪？ [\#135](https://github.com/overtrue/wechat/issues/135)
- 试了两次，真的不会用 [\#134](https://github.com/overtrue/wechat/issues/134)
- 不知道这算不算是个BUG [\#133](https://github.com/overtrue/wechat/issues/133)
- 微信小店 [\#130](https://github.com/overtrue/wechat/issues/130)
- 多次遇到 accesstoken 无效的问题 [\#129](https://github.com/overtrue/wechat/issues/129)
- MCH\_KEY 微信支付 [\#128](https://github.com/overtrue/wechat/issues/128)
- 使用flightphp框架,验证URL的时候,在Apache下接入成功,在Nginx接入失败 [\#126](https://github.com/overtrue/wechat/issues/126)
- 好东西！可惜没有我需要的微信红包 [\#125](https://github.com/overtrue/wechat/issues/125)
- Cache存储部件可定制 [\#120](https://github.com/overtrue/wechat/issues/120)
- 关于Bag [\#119](https://github.com/overtrue/wechat/issues/119)
- 将代码部署到负载均衡上如何管理access token  [\#118](https://github.com/overtrue/wechat/issues/118)
- 消息接受和回复时，如果不对消息做回复，该如何做？ [\#117](https://github.com/overtrue/wechat/issues/117)
- 请教一个问题 [\#116](https://github.com/overtrue/wechat/issues/116)
- 关于 Cache [\#115](https://github.com/overtrue/wechat/issues/115)
- 如何才能获取普通的access\_token [\#113](https://github.com/overtrue/wechat/issues/113)
- $HTTP\_RAW\_POST\_DATA DEPRECATED [\#111](https://github.com/overtrue/wechat/issues/111)
- App支付缺少错误码 [\#109](https://github.com/overtrue/wechat/issues/109)
- 当用户信息有 " 字符时系统出错 \(用户与用户组管理接口\) [\#107](https://github.com/overtrue/wechat/issues/107)
- 提示错误 [\#106](https://github.com/overtrue/wechat/issues/106)
- 使用企业号的时候 接入失败啊，在验证url的时候 [\#104](https://github.com/overtrue/wechat/issues/104)
- 支付签名错误 [\#101](https://github.com/overtrue/wechat/issues/101)
- 微信支付.$payment-\>getConfig\(\)调用时候\[Wechat\]系统繁忙,此时请开发者稍候再试. [\#96](https://github.com/overtrue/wechat/issues/96)
- wechat/src/Wechat/Payment/UnifiedOrder.php 小问题 [\#94](https://github.com/overtrue/wechat/issues/94)
- 请教laravel中如何在微信支付中 catch  UnifiedOrder 抛出的异常？ [\#93](https://github.com/overtrue/wechat/issues/93)
- 是否可以增加一个第三方接口融合功能 [\#91](https://github.com/overtrue/wechat/issues/91)
- 订单查询 [\#90](https://github.com/overtrue/wechat/issues/90)
- 如何不下载图片，通过mediaId获取图片存储的URL [\#89](https://github.com/overtrue/wechat/issues/89)
- 'Undefined index: HTTP\_HOST'  [\#88](https://github.com/overtrue/wechat/issues/88)
- Undefined index: HTTP\_HOST [\#87](https://github.com/overtrue/wechat/issues/87)
- 不能上传gif格式的图片素材 [\#84](https://github.com/overtrue/wechat/issues/84)
- OAuth重构 [\#74](https://github.com/overtrue/wechat/issues/74)
- \[3.0\] Tasks [\#50](https://github.com/overtrue/wechat/issues/50)
- appId 和 appSecret不要作为各个类的构造参数 [\#114](https://github.com/overtrue/wechat/issues/114)
- 增加debug相关的选项 [\#112](https://github.com/overtrue/wechat/issues/112)
- 好像没有获取自动回复数据接口 [\#108](https://github.com/overtrue/wechat/issues/108)
- js端查看微信卡券接口 chooseCard [\#79](https://github.com/overtrue/wechat/issues/79)
- 【新增】支付 [\#78](https://github.com/overtrue/wechat/issues/78)
- 模板消息重构 [\#75](https://github.com/overtrue/wechat/issues/75)
- 素材下载自动识别MIME生成后缀 [\#54](https://github.com/overtrue/wechat/issues/54)
- \[建议\] 深度结合微信多图文与素材管理 [\#46](https://github.com/overtrue/wechat/issues/46)
- 群发功能 [\#18](https://github.com/overtrue/wechat/issues/18)

**Merged pull requests:**

- 3.0 [\#289](https://github.com/overtrue/wechat/pull/289) ([overtrue](https://github.com/overtrue))
- Merge Develop [\#288](https://github.com/overtrue/wechat/pull/288) ([overtrue](https://github.com/overtrue))
- Applied fixes from StyleCI [\#287](https://github.com/overtrue/wechat/pull/287) ([overtrue](https://github.com/overtrue))
- Applied fixes from StyleCI [\#286](https://github.com/overtrue/wechat/pull/286) ([overtrue](https://github.com/overtrue))
- Fix bug in batchGet method. [\#283](https://github.com/overtrue/wechat/pull/283) ([tianyong90](https://github.com/tianyong90))
- Typo. [\#279](https://github.com/overtrue/wechat/pull/279) ([overtrue](https://github.com/overtrue))
- Add contribution guide. resolves \#277 [\#278](https://github.com/overtrue/wechat/pull/278) ([overtrue](https://github.com/overtrue))
- Develop [\#274](https://github.com/overtrue/wechat/pull/274) ([overtrue](https://github.com/overtrue))
- Applied fixes from StyleCI [\#273](https://github.com/overtrue/wechat/pull/273) ([overtrue](https://github.com/overtrue))
- Develop [\#271](https://github.com/overtrue/wechat/pull/271) ([overtrue](https://github.com/overtrue))
- Merge Develop [\#268](https://github.com/overtrue/wechat/pull/268) ([overtrue](https://github.com/overtrue))
- Applied fixes from StyleCI [\#267](https://github.com/overtrue/wechat/pull/267) ([overtrue](https://github.com/overtrue))
- Update QRCode.php [\#258](https://github.com/overtrue/wechat/pull/258) ([webshiyue](https://github.com/webshiyue))
- Add tests for LuckyMoney. [\#255](https://github.com/overtrue/wechat/pull/255) ([tianyong90](https://github.com/tianyong90))
- CS. [\#254](https://github.com/overtrue/wechat/pull/254) ([overtrue](https://github.com/overtrue))
- Scrutinizer Auto-Fixes [\#253](https://github.com/overtrue/wechat/pull/253) ([scrutinizer-auto-fixer](https://github.com/scrutinizer-auto-fixer))
- Applied fixes from StyleCI [\#251](https://github.com/overtrue/wechat/pull/251) ([overtrue](https://github.com/overtrue))
- Applied fixes from StyleCI [\#250](https://github.com/overtrue/wechat/pull/250) ([overtrue](https://github.com/overtrue))
- Merge Develop [\#249](https://github.com/overtrue/wechat/pull/249) ([overtrue](https://github.com/overtrue))
- Merge Develop [\#248](https://github.com/overtrue/wechat/pull/248) ([overtrue](https://github.com/overtrue))
- Merge from Develop [\#243](https://github.com/overtrue/wechat/pull/243) ([overtrue](https://github.com/overtrue))
- Applied fixes from StyleCI [\#242](https://github.com/overtrue/wechat/pull/242) ([overtrue](https://github.com/overtrue))
- Applied fixes from StyleCI [\#241](https://github.com/overtrue/wechat/pull/241) ([overtrue](https://github.com/overtrue))
- Add Luckymoney. [\#240](https://github.com/overtrue/wechat/pull/240) ([tianyong90](https://github.com/tianyong90))
- Applied fixes from StyleCI [\#237](https://github.com/overtrue/wechat/pull/237) ([overtrue](https://github.com/overtrue))
- Applied fixes from StyleCI [\#234](https://github.com/overtrue/wechat/pull/234) ([overtrue](https://github.com/overtrue))
- Multiple News Items Support [\#230](https://github.com/overtrue/wechat/pull/230) ([fanglinks](https://github.com/fanglinks))
- Applied fixes from StyleCI [\#221](https://github.com/overtrue/wechat/pull/221) ([overtrue](https://github.com/overtrue))
- \[3.0\]\[Bugfix\]发送图文消息缺少type [\#217](https://github.com/overtrue/wechat/pull/217) ([sunbiao0526](https://github.com/sunbiao0526))
- fix Js.php 获取自定义cache对象 [\#215](https://github.com/overtrue/wechat/pull/215) ([sunbiao0526](https://github.com/sunbiao0526))
- Applied fixes from StyleCI [\#197](https://github.com/overtrue/wechat/pull/197) ([overtrue](https://github.com/overtrue))
- Add alias [\#196](https://github.com/overtrue/wechat/pull/196) ([ruchengtang](https://github.com/ruchengtang))
- Applied fixes from StyleCI [\#195](https://github.com/overtrue/wechat/pull/195) ([overtrue](https://github.com/overtrue))
- Applied fixes from StyleCI [\#194](https://github.com/overtrue/wechat/pull/194) ([overtrue](https://github.com/overtrue))
- Add Broadcast. [\#193](https://github.com/overtrue/wechat/pull/193) ([ruchengtang](https://github.com/ruchengtang))
- 微信红包类优化 [\#190](https://github.com/overtrue/wechat/pull/190) ([tianyong90](https://github.com/tianyong90))
- Update ServerServiceProvider.php [\#187](https://github.com/overtrue/wechat/pull/187) ([ghost](https://github.com/ghost))
- Update README\_EN.md [\#178](https://github.com/overtrue/wechat/pull/178) ([spekulatius](https://github.com/spekulatius))
- 添加群发消息文档 [\#177](https://github.com/overtrue/wechat/pull/177) ([ruchengtang](https://github.com/ruchengtang))
- 群发消息 [\#176](https://github.com/overtrue/wechat/pull/176) ([ruchengtang](https://github.com/ruchengtang))
- Master [\#167](https://github.com/overtrue/wechat/pull/167) ([xiaohome](https://github.com/xiaohome))
- 微信小店 [\#166](https://github.com/overtrue/wechat/pull/166) ([xiaohome](https://github.com/xiaohome))
- 红包类更新 [\#161](https://github.com/overtrue/wechat/pull/161) ([overtrue](https://github.com/overtrue))
- 加入摇一摇红包类，红包类提升至Overtrue命名空间 [\#160](https://github.com/overtrue/wechat/pull/160) ([tianyong90](https://github.com/tianyong90))
- 2.1 [\#159](https://github.com/overtrue/wechat/pull/159) ([overtrue](https://github.com/overtrue))
- Update QRCode.php [\#156](https://github.com/overtrue/wechat/pull/156) ([ruchengtang](https://github.com/ruchengtang))
- 修复使用!=，来判断0 != null 的时候的一个bug [\#154](https://github.com/overtrue/wechat/pull/154) ([Liv1020](https://github.com/Liv1020))
- 调整多客服类删除客服方法 [\#151](https://github.com/overtrue/wechat/pull/151) ([tianyong90](https://github.com/tianyong90))
- 修复个bug [\#146](https://github.com/overtrue/wechat/pull/146) ([xiaohome](https://github.com/xiaohome))
- Update README.md [\#142](https://github.com/overtrue/wechat/pull/142) ([parkshinhye](https://github.com/parkshinhye))
- Fix code style to PSR-2 [\#139](https://github.com/overtrue/wechat/pull/139) ([tianyong90](https://github.com/tianyong90))
- 加入红包工具类，支持现金和裂变红包的发送及查询 [\#137](https://github.com/overtrue/wechat/pull/137) ([tianyong90](https://github.com/tianyong90))
- 卡券类批量获取卡券ID方法支持仅获取指定状态卡券 [\#132](https://github.com/overtrue/wechat/pull/132) ([tianyong90](https://github.com/tianyong90))
- 添加客服 卡券回复!!! [\#124](https://github.com/overtrue/wechat/pull/124) ([parkshinhye](https://github.com/parkshinhye))
- 调整退款类中一处异常抛出逻辑并修正单词拼写错误 [\#122](https://github.com/overtrue/wechat/pull/122) ([tianyong90](https://github.com/tianyong90))
- 加入创建卡券货架接口 [\#121](https://github.com/overtrue/wechat/pull/121) ([tianyong90](https://github.com/tianyong90))
- 增加退款类 [\#105](https://github.com/overtrue/wechat/pull/105) ([jaring](https://github.com/jaring))
- 增加获取用户已领取卡券方法 [\#103](https://github.com/overtrue/wechat/pull/103) ([tenstone](https://github.com/tenstone))
- Scrutinizer Auto-Fixes [\#100](https://github.com/overtrue/wechat/pull/100) ([scrutinizer-auto-fixer](https://github.com/scrutinizer-auto-fixer))
- 修正二维码类中生成卡券二维码方法 [\#99](https://github.com/overtrue/wechat/pull/99) ([tianyong90](https://github.com/tianyong90))
- 卡券接口加入添加测试白名单方法 [\#98](https://github.com/overtrue/wechat/pull/98) ([tianyong90](https://github.com/tianyong90))
- 依样画葫芦写了一个查询订单，更改了UnifiedOrder中Http初始化 [\#95](https://github.com/overtrue/wechat/pull/95) ([jaring](https://github.com/jaring))
- accessToken根据appId变化 [\#92](https://github.com/overtrue/wechat/pull/92) ([keepeye](https://github.com/keepeye))
- Fix payment sign bug. [\#82](https://github.com/overtrue/wechat/pull/82) ([0i](https://github.com/0i))
- \[wiki\] wechat payment [\#81](https://github.com/overtrue/wechat/pull/81) ([0i](https://github.com/0i))

## [2.1.0](https://github.com/overtrue/wechat/tree/2.1.0) (2015-08-18)
[Full Changelog](https://github.com/overtrue/wechat/compare/2.0.35...2.1.0)

**Merged pull requests:**

- Wechat Payment [\#80](https://github.com/overtrue/wechat/pull/80) ([0i](https://github.com/0i))

## [2.0.35](https://github.com/overtrue/wechat/tree/2.0.35) (2015-08-11)
[Full Changelog](https://github.com/overtrue/wechat/compare/2.0.1...2.0.35)

**Implemented enhancements:**

- Overtrue\Wechat\Http识别JSON的问题 [\#47](https://github.com/overtrue/wechat/issues/47)

**Fixed bugs:**

- 模板消息简单格式无效 [\#34](https://github.com/overtrue/wechat/issues/34)

**Closed issues:**

- $data是数组,title输出不了内容 [\#73](https://github.com/overtrue/wechat/issues/73)
- 回调是如何传递外部参数的？ [\#72](https://github.com/overtrue/wechat/issues/72)
- 【建议】可以添加微信js的功能吗？ [\#71](https://github.com/overtrue/wechat/issues/71)
- Message::make\('link'\) 无效 [\#70](https://github.com/overtrue/wechat/issues/70)
- 监听消息 返回Bad Request [\#65](https://github.com/overtrue/wechat/issues/65)
- 微信素材管理小改版，求跟上~ [\#64](https://github.com/overtrue/wechat/issues/64)
- 在新浪SAE平台上的部署问题 [\#63](https://github.com/overtrue/wechat/issues/63)
- $xmlInput = file\_get\_contents\('php://input'\);貌似在某些版本的PHP有问题还是怎的 [\#57](https://github.com/overtrue/wechat/issues/57)
- 卡券的 attachExtension 方法  [\#56](https://github.com/overtrue/wechat/issues/56)
- 网页授权$auth-\>authorize\(\) 后还需要保存access\_token吗？ [\#53](https://github.com/overtrue/wechat/issues/53)
- php 5.6版本下出现错误（5.6以下版本正常） [\#51](https://github.com/overtrue/wechat/issues/51)
- 消息发送后服务器无法正确返回响应 [\#48](https://github.com/overtrue/wechat/issues/48)
- token验证失败 [\#45](https://github.com/overtrue/wechat/issues/45)
- 微信关注自动回复问题 [\#44](https://github.com/overtrue/wechat/issues/44)
- js sdk config 建议增加 beta 字段 [\#35](https://github.com/overtrue/wechat/issues/35)
- 关于Util\HTTP::encode\(\)中的urlencode\(\)/urldecode\(\)成组操作的疑问 [\#31](https://github.com/overtrue/wechat/issues/31)
- Media::updateNews\(\) 方法与微信API不一致 [\#29](https://github.com/overtrue/wechat/issues/29)
- 希望能有一个ThinkPHP的使用示例 [\#28](https://github.com/overtrue/wechat/issues/28)
- 事件消息 [\#22](https://github.com/overtrue/wechat/issues/22)
- 模板消息notice [\#21](https://github.com/overtrue/wechat/issues/21)
- 关于获取（接收）用户发送消息 [\#19](https://github.com/overtrue/wechat/issues/19)
- 微信公众号绑定的一点问题，请教。 [\#16](https://github.com/overtrue/wechat/issues/16)
- 获取素材列表错误 [\#15](https://github.com/overtrue/wechat/issues/15)

**Merged pull requests:**

- Scrutinizer Auto-Fixes [\#69](https://github.com/overtrue/wechat/pull/69) ([scrutinizer-auto-fixer](https://github.com/scrutinizer-auto-fixer))
- Scrutinizer Auto-Fixes [\#68](https://github.com/overtrue/wechat/pull/68) ([scrutinizer-auto-fixer](https://github.com/scrutinizer-auto-fixer))
- Scrutinizer Auto-Fixes [\#67](https://github.com/overtrue/wechat/pull/67) ([scrutinizer-auto-fixer](https://github.com/scrutinizer-auto-fixer))
- Fixed StyleCI config [\#66](https://github.com/overtrue/wechat/pull/66) ([GrahamCampbell](https://github.com/GrahamCampbell))
- 洁癖爆发了。。。 [\#62](https://github.com/overtrue/wechat/pull/62) ([TheNorthMemory](https://github.com/TheNorthMemory))
- fix: js getUrl use Url::current\(\) [\#61](https://github.com/overtrue/wechat/pull/61) ([wdjwxh](https://github.com/wdjwxh))
- bug-fix: add x-forwarded-host for Url::current [\#60](https://github.com/overtrue/wechat/pull/60) ([wdjwxh](https://github.com/wdjwxh))
- Fix request method for User::batchGet\(\), should be POST with JSON. [\#59](https://github.com/overtrue/wechat/pull/59) ([acgrid](https://github.com/acgrid))
- optimize some code [\#58](https://github.com/overtrue/wechat/pull/58) ([tabalt](https://github.com/tabalt))
- 增加使用media id发送图文消息的功能 [\#52](https://github.com/overtrue/wechat/pull/52) ([zengohm](https://github.com/zengohm))
- fix Staff::delete, let it works [\#42](https://github.com/overtrue/wechat/pull/42) ([TheNorthMemory](https://github.com/TheNorthMemory))
- 支持自定义菜单类型：下发消息media\_id、跳转图文消息view\_limited [\#40](https://github.com/overtrue/wechat/pull/40) ([acgrid](https://github.com/acgrid))
- docline comments & fix AccessToken parameter typos [\#39](https://github.com/overtrue/wechat/pull/39) ([TheNorthMemory](https://github.com/TheNorthMemory))
- Merge from master [\#38](https://github.com/overtrue/wechat/pull/38) ([overtrue](https://github.com/overtrue))
- 客服接口Bugfix [\#37](https://github.com/overtrue/wechat/pull/37) ([overtrue](https://github.com/overtrue))
- fix Staff and AccessToken typos [\#36](https://github.com/overtrue/wechat/pull/36) ([TheNorthMemory](https://github.com/TheNorthMemory))
- Update QRCode.php [\#33](https://github.com/overtrue/wechat/pull/33) ([refear99](https://github.com/refear99))
- English Readme [\#32](https://github.com/overtrue/wechat/pull/32) ([hareluya](https://github.com/hareluya))
- 更新图文消息方法Media::updateNews\(\)与微信API不一致 [\#30](https://github.com/overtrue/wechat/pull/30) ([acgrid](https://github.com/acgrid))
- 代码之美在于不断修正 :\) [\#27](https://github.com/overtrue/wechat/pull/27) ([TheNorthMemory](https://github.com/TheNorthMemory))
- the json\_encode $depth parameter was added@5.5.0 [\#26](https://github.com/overtrue/wechat/pull/26) ([TheNorthMemory](https://github.com/TheNorthMemory))
- fix \#4 for PHP5.3 [\#25](https://github.com/overtrue/wechat/pull/25) ([TheNorthMemory](https://github.com/TheNorthMemory))
- fix \#4 for PHP5.3 [\#23](https://github.com/overtrue/wechat/pull/23) ([TheNorthMemory](https://github.com/TheNorthMemory))
- Update QRCode.php [\#17](https://github.com/overtrue/wechat/pull/17) ([gundanx10](https://github.com/gundanx10))

## [2.0.1](https://github.com/overtrue/wechat/tree/2.0.1) (2015-05-08)
[Full Changelog](https://github.com/overtrue/wechat/compare/2.0.0...2.0.1)

**Closed issues:**

- 2.0版本使用问题 [\#14](https://github.com/overtrue/wechat/issues/14)

## [2.0.0](https://github.com/overtrue/wechat/tree/2.0.0) (2015-05-07)
[Full Changelog](https://github.com/overtrue/wechat/compare/1.0.1...2.0.0)

**Closed issues:**

-  素材管理 -- 部分图片下载失败 [\#13](https://github.com/overtrue/wechat/issues/13)
-  素材管理 -- 图片下载失败 [\#12](https://github.com/overtrue/wechat/issues/12)
- 请问这样判断Mcrypt到底准不准? [\#11](https://github.com/overtrue/wechat/issues/11)
- 好奇怪啊，开发者中心的服务器配置已经提交并验证成功了，可是message不起作用 [\#10](https://github.com/overtrue/wechat/issues/10)
- 网页授权一刷新页面就出现40029 不合法的oauth\_code [\#8](https://github.com/overtrue/wechat/issues/8)
- mcrypt\_module\_open error [\#7](https://github.com/overtrue/wechat/issues/7)
- composer update 之后报错 [\#6](https://github.com/overtrue/wechat/issues/6)
- 今天开始，授权时候一直报40029，invalid  code的错误 [\#5](https://github.com/overtrue/wechat/issues/5)
- Using $this when not in object context [\#4](https://github.com/overtrue/wechat/issues/4)
- 监听事件时不区分 $target（监听所有event和message） [\#3](https://github.com/overtrue/wechat/issues/3)
- Does this support Oauth already? [\#1](https://github.com/overtrue/wechat/issues/1)

**Merged pull requests:**

- Fix wiki url error [\#9](https://github.com/overtrue/wechat/pull/9) ([sinoon](https://github.com/sinoon))
- Update Bag.php [\#2](https://github.com/overtrue/wechat/pull/2) ([zerozh](https://github.com/zerozh))

## [1.0.1](https://github.com/overtrue/wechat/tree/1.0.1) (2015-03-19)
[Full Changelog](https://github.com/overtrue/wechat/compare/1.0...1.0.1)

## [1.0](https://github.com/overtrue/wechat/tree/1.0) (2015-03-13)


\* *This Change Log was automatically generated by [github_changelog_generator](https://github.com/skywinder/Github-Changelog-Generator)*