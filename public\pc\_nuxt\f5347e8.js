(window.webpackJsonp=window.webpackJsonp||[]).push([[1,10,12,19],{473:function(e,t,r){"use strict";var n=r(14),o=r(4),l=r(5),d=r(141),c=r(24),v=r(18),m=r(290),f=r(54),h=r(104),_=r(289),x=r(3),y=r(105).f,w=r(45).f,C=r(23).f,S=r(474),k=r(475).trim,T="Number",O=o.Number,N=O.prototype,D=o.TypeError,I=l("".slice),F=l("".charCodeAt),M=function(e){var t=_(e,"number");return"bigint"==typeof t?t:z(t)},z=function(e){var t,r,n,o,l,d,c,code,v=_(e,"number");if(h(v))throw D("Cannot convert a Symbol value to a number");if("string"==typeof v&&v.length>2)if(v=k(v),43===(t=F(v,0))||45===t){if(88===(r=F(v,2))||120===r)return NaN}else if(48===t){switch(F(v,1)){case 66:case 98:n=2,o=49;break;case 79:case 111:n=8,o=55;break;default:return+v}for(d=(l=I(v,2)).length,c=0;c<d;c++)if((code=F(l,c))<48||code>o)return NaN;return parseInt(l,n)}return+v};if(d(T,!O(" 0o1")||!O("0b1")||O("+0x1"))){for(var A,E=function(e){var t=arguments.length<1?0:O(M(e)),r=this;return f(N,r)&&x((function(){S(r)}))?m(Object(t),r,E):t},B=n?y(O):"MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,EPSILON,MAX_SAFE_INTEGER,MIN_SAFE_INTEGER,isFinite,isInteger,isNaN,isSafeInteger,parseFloat,parseInt,fromString,range".split(","),R=0;B.length>R;R++)v(O,A=B[R])&&!v(E,A)&&C(E,A,w(O,A));E.prototype=N,N.constructor=E,c(o,T,E,{constructor:!0})}},474:function(e,t,r){var n=r(5);e.exports=n(1..valueOf)},475:function(e,t,r){var n=r(5),o=r(36),l=r(19),d=r(476),c=n("".replace),v="["+d+"]",m=RegExp("^"+v+v+"*"),f=RegExp(v+v+"*$"),h=function(e){return function(t){var r=l(o(t));return 1&e&&(r=c(r,m,"")),2&e&&(r=c(r,f,"")),r}};e.exports={start:h(1),end:h(2),trim:h(3)}},476:function(e,t){e.exports="\t\n\v\f\r                　\u2028\u2029\ufeff"},477:function(e,t,r){var content=r(480);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[e.i,content,""]]),content.locals&&(e.exports=content.locals);(0,r(17).default)("7c52e05d",content,!0,{sourceMap:!1})},478:function(e,t,r){"use strict";r.r(t);r(473);var n={data:function(){return{priceSlice:{}}},components:{},props:{firstSize:{type:Number,default:14},secondSize:{type:Number,default:14},color:{type:String},weight:{type:[String,Number],default:400},price:{type:[String,Number],default:""},showSubscript:{type:Boolean,default:!0},subscriptSize:{type:Number,default:14},lineThrough:{type:Boolean,default:!1}},created:function(){this.priceFormat()},watch:{price:function(e){this.priceFormat()}},methods:{priceFormat:function(){var e=this.price,t={};null!==e&&(e=String(e).split("."),t.first=e[0],t.second=e[1],this.priceSlice=t)}}},o=(r(479),r(8)),component=Object(o.a)(n,(function(){var e=this,t=e._self._c;return t("span",{class:(e.lineThrough?"line-through":"")+"price-format",style:{color:e.color,"font-weight":e.weight}},[e.showSubscript?t("span",{style:{"font-size":e.subscriptSize+"px","margin-right":"1px"}},[e._v("¥")]):e._e(),e._v(" "),t("span",{style:{"font-size":e.firstSize+"px","margin-right":"1px"}},[e._v(e._s(e.priceSlice.first))]),e._v(" "),e.priceSlice.second?t("span",{style:{"font-size":e.secondSize+"px"}},[e._v("."+e._s(e.priceSlice.second))]):e._e()])}),[],!1,null,null,null);t.default=component.exports},479:function(e,t,r){"use strict";r(477)},480:function(e,t,r){var n=r(16)(!1);n.push([e.i,".price-format{display:flex;align-items:baseline}",""]),e.exports=n},485:function(e,t,r){"use strict";r.r(t);r(473),r(86),r(62),r(12),r(107),r(40),r(106);var n=6e4,o=36e5,l=24*o;function d(e){return(0+e.toString()).slice(-2)}var c={components:{},props:{isSlot:{type:Boolean,default:!1},time:{type:Number,default:0},format:{type:String,default:"hh:mm:ss"},autoStart:{type:Boolean,default:!0}},watch:{time:{immediate:!0,handler:function(e){e&&this.reset()}}},data:function(){return{timeObj:{},formateTime:0}},created:function(){},computed:{},methods:{createTimer:function(e){return setTimeout(e,100)},isSameSecond:function(e,t){return Math.floor(e)===Math.floor(t)},start:function(){this.counting||(this.counting=!0,this.endTime=Date.now()+1e3*this.remain,this.setTimer())},setTimer:function(){var e=this;this.tid=this.createTimer((function(){var t=e.getRemain();e.isSameSecond(t,e.remain)&&0!==t||e.setRemain(t),0!==e.remain&&e.setTimer()}))},getRemain:function(){return Math.max(this.endTime-Date.now(),0)},pause:function(){this.counting=!1,clearTimeout(this.tid)},reset:function(){this.pause(),this.remain=this.time,this.setRemain(this.remain),this.autoStart&&this.start()},setRemain:function(e){var t=this.format;this.remain=e;var time,r=(time=e,{days:Math.floor(time/l),hours:d(Math.floor(time%l/o)),minutes:d(Math.floor(time%o/n)),seconds:d(Math.floor(time%n/1e3))});this.formateTime=function(e,t){var r=t.days,n=t.hours,o=t.minutes,l=t.seconds;return-1!==e.indexOf("dd")&&(e=e.replace("dd",r)),-1!==e.indexOf("hh")&&(e=e.replace("hh",d(n))),-1!==e.indexOf("mm")&&(e=e.replace("mm",d(o))),-1!==e.indexOf("ss")&&(e=e.replace("ss",d(l))),e}(t,r),this.$emit("change",r),0===e&&(this.pause(),this.$emit("finish"))}}},v=r(8),component=Object(v.a)(c,(function(){var e=this,t=e._self._c;return e.time>=0?t("div",[t("client-only",[e.isSlot?e._t("default"):t("span",[e._v(e._s(e.formateTime))])],2)],1):e._e()}),[],!1,null,null,null);t.default=component.exports},510:function(e,t,r){var content=r(527);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[e.i,content,""]]),content.locals&&(e.exports=content.locals);(0,r(17).default)("2129d832",content,!0,{sourceMap:!1})},526:function(e,t,r){"use strict";r(510)},527:function(e,t,r){var n=r(16)(!1);n.push([e.i,".deliver-search-container .deliver-box .deliver-recode-box[data-v-79dec466]{padding:10px 20px;background-color:#f2f2f2}.deliver-search-container .deliver-box .deliver-recode-box .recode-img[data-v-79dec466]{position:relative;width:72px;height:72px}.deliver-search-container .deliver-box .deliver-recode-box .recode-img .float-count[data-v-79dec466]{position:absolute;bottom:0;height:20px;width:100%;background-color:rgba(0,0,0,.5);color:#fff;font-size:12px}.deliver-search-container .deliver-box .deliver-recode-box .recode-info-container[data-v-79dec466]{flex:1}.deliver-search-container .deliver-box .deliver-recode-box .recode-info-container .recode-label[data-v-79dec466]{width:70px}.deliver-search-container .deliver-box .deliver-recode-box .recode-info-container .copy-btn[data-v-79dec466]{height:20px;min-width:42px;border:1px solid #ff2c3c;font-size:12px;margin-left:8px;border-radius:60px;cursor:pointer}.deliver-search-container .deliver-box .deliver-recode-box .recode-info-container .copy-btn[data-v-79dec466]:hover{background-color:#fff}.deliver-search-container .deliver-box .deliver-flow-box[data-v-79dec466]{padding-left:15px}.deliver-search-container .deliver-box .time-line-title[data-v-79dec466]{font-weight:500px;font-size:16px;margin-bottom:10px}",""]),e.exports=n},538:function(e,t,r){"use strict";r.r(t);var n=r(9),o=(r(53),r(473),r(12),{props:{value:{type:Boolean,default:!1},aid:{type:Number|String}},data:function(){return{showDialog:!1,deliverBuy:{},delivery:{},deliverFinish:{},deliverOrder:{},deliverShipment:{},deliverTake:{},timeLineArray:[]}},watch:{value:function(e){console.log(e,"val"),this.showDialog=e},showDialog:function(e){e&&this.aid&&(this.timeLineArray=[],this.getDeliverTraces()),this.$emit("input",e)}},methods:{getDeliverTraces:function(){var e=this;return Object(n.a)(regeneratorRuntime.mark((function t(){var data,r,n,o,l,d,c,v,m;return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return data={id:e.aid},t.next=3,e.$get("order/orderTraces",{params:data});case 3:1==(r=t.sent).code&&(n=r.data,o=n.buy,l=n.delivery,d=n.finish,c=n.order,v=n.shipment,m=n.take,e.deliverBuy=o,e.delivery=l,e.deliverFinish=d,e.deliverOrder=c,e.deliverShipment=v,e.deliverTake=m,e.timeLineArray.push(e.deliverFinish),e.timeLineArray.push(e.delivery),e.timeLineArray.push(e.deliverShipment),e.timeLineArray.push(e.deliverBuy),console.log(e.timeLineArray));case 5:case"end":return t.stop()}}),t)})))()},onCopy:function(){var e=document.createElement("input");e.value=this.deliverOrder.invoice_no,document.body.appendChild(e),e.select(),document.execCommand("Copy"),this.$message.success("复制成功"),e.remove()}}}),l=(r(526),r(8)),component=Object(l.a)(o,(function(){var e=this,t=e._self._c;return t("div",{staticClass:"deliver-search-container"},[t("el-dialog",{attrs:{visible:e.showDialog,top:"30vh",width:"900px",title:"物流查询"},on:{"update:visible":function(t){e.showDialog=t}}},[t("div",{staticClass:"deliver-box"},[t("div",{staticClass:"deliver-recode-box flex"},[t("div",{staticClass:"recode-img"},[t("el-image",{staticStyle:{width:"100%",height:"100%"},attrs:{fit:"cover",src:e.deliverOrder.image}}),e._v(" "),t("div",{staticClass:"float-count flex row-center"},[e._v("共"+e._s(e.deliverOrder.count)+"件商品")])],1),e._v(" "),t("div",{staticClass:"recode-info-container m-l-10"},[t("div",{staticClass:"flex"},[t("div",{staticClass:"recode-label"},[e._v("物流状态：")]),e._v(" "),t("div",{staticClass:"primary lg",staticStyle:{"font-weight":"500"}},[e._v(e._s(e.deliverOrder.tips))])]),e._v(" "),t("div",{staticClass:"flex",staticStyle:{margin:"6px 0"}},[t("div",{staticClass:"recode-label"},[e._v("快递公司：")]),e._v(" "),t("div",[e._v(e._s(e.deliverOrder.shipping_name))])]),e._v(" "),t("div",{staticClass:"flex"},[t("div",{staticClass:"recode-label"},[e._v("快递单号：")]),e._v(" "),t("div",[e._v(e._s(e.deliverOrder.invoice_no))]),e._v(" "),t("div",{staticClass:"copy-btn primary flex row-center",on:{click:e.onCopy}},[e._v("复制")])])])]),e._v(" "),t("div",{staticClass:"deliver-flow-box m-t-16"},[t("el-timeline",[e.deliverFinish.tips?t("el-timeline-item",[t("div",[t("div",{staticClass:"flex lg"},[t("div",{staticClass:"m-r-8",staticStyle:{"font-weight":"500"}},[e._v("\n                                    "+e._s(e.deliverTake.contacts)+"\n                                ")]),e._v(" "),t("div",{staticStyle:{"font-weight":"500"}},[e._v(e._s(e.deliverTake.mobile))])]),e._v(" "),t("div",{staticClass:"lighter m-t-8"},[e._v(e._s(e.deliverTake.address))])])]):e._e(),e._v(" "),e.deliverFinish.tips?t("el-timeline-item",{attrs:{timestamp:e.deliverFinish.time}},[t("div",{staticClass:"time-line-title"},[e._v(e._s(e.deliverFinish.title))]),e._v(" "),t("div",[e._v(e._s(e.deliverFinish.tips))])]):e._e(),e._v(" "),e.delivery.traces&&e.delivery.traces.length?t("el-timeline-item",{attrs:{timestamp:e.delivery.time}},[t("div",{staticClass:"time-line-title m-b-8"},[e._v(e._s(e.delivery.title))]),e._v(" "),e._l(e.delivery.traces,(function(r,n){return t("el-timeline-item",{key:n,attrs:{timestamp:r[0]}},[t("div",{staticClass:"muted"},[e._v(e._s(r[1]))])])}))],2):e._e(),e._v(" "),e.deliverShipment.tips?t("el-timeline-item",{attrs:{timestamp:e.deliverShipment.time}},[t("div",{staticClass:"time-line-title"},[e._v(e._s(e.deliverShipment.title))]),e._v(" "),t("div",[e._v(e._s(e.deliverShipment.tips))])]):e._e(),e._v(" "),e.deliverBuy.tips?t("el-timeline-item",{attrs:{timestamp:e.deliverBuy.time}},[t("div",{staticClass:"time-line-title"},[e._v(e._s(e.deliverBuy.title))]),e._v(" "),t("div",[e._v(e._s(e.deliverBuy.tips))])]):e._e()],1)],1)])])],1)}),[],!1,null,"79dec466",null);t.default=component.exports},589:function(e,t,r){var content=r(672);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[e.i,content,""]]),content.locals&&(e.exports=content.locals);(0,r(17).default)("9fd66b3e",content,!0,{sourceMap:!1})},671:function(e,t,r){"use strict";r(589)},672:function(e,t,r){var n=r(16)(!1);n.push([e.i,".order-list[data-v-760e01d2]{padding:0 16px}.order-list .item[data-v-760e01d2]{margin-bottom:20px}.order-list .item .item-hd[data-v-760e01d2]{height:40px;border:1px solid #e5e5e5;background:#f2f2f2;padding:0 20px}.order-list .item .item-hd .status[data-v-760e01d2]{width:100px;text-align:right}.order-list .item .item-con[data-v-760e01d2]{box-shadow:0 3px 4px rgba(0,0,0,.08);align-items:stretch}.order-list .item .item-con .goods[data-v-760e01d2]{padding:17px 0;width:560px}.order-list .item .item-con .goods .goods-item[data-v-760e01d2]{padding:10px 20px}.order-list .item .item-con .goods .goods-item .goods-img[data-v-760e01d2]{flex:none;margin-right:10px;width:72px;height:72px}.order-list .item .item-con .goods .goods-item .goods-name[data-v-760e01d2]{width:100%}.order-list .item .item-con .goods .goods-item .goods-name .num[data-v-760e01d2]{padding:0 42px}.order-list .item .item-con .pay-price[data-v-760e01d2]{width:200px;border-left:1px solid #e5e5e5;border-right:1px solid #e5e5e5}.order-list .item .item-con .operate[data-v-760e01d2]{width:185px}.order-list .item .item-con .operate>div[data-v-760e01d2]{cursor:pointer}.order-list .item .item-con .operate .btn[data-v-760e01d2]{width:120px;height:32px;border-radius:2px}.order-list .item .item-con .operate .btn.plain[data-v-760e01d2]{border:1px solid #ff2c3c}",""]),e.exports=n},696:function(e,t,r){"use strict";r.r(t);r(29);var n=r(9),o=(r(53),{props:{list:{type:Array,default:function(){return[]}}},data:function(){return{showDeliver:!1,aid:-1}},created:function(){console.log(this.list)},methods:{handleOrder:function(e,t){var r=this;this.type=e,this.orderId=t,this.$confirm(this.getTipsText(e),{title:"温馨提示",center:!0,confirmButtonText:"确定",cancelButtonText:"取消",callback:function(e){"confirm"==e&&r.postOrder()}})},postOrder:function(){var e=this;return Object(n.a)(regeneratorRuntime.mark((function t(){var r,n,o,l,code,d;return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:r=e.type,n=e.orderId,o="",t.t0=r,t.next=0===t.t0?5:1===t.t0?7:2===t.t0?9:11;break;case 5:return o="order/cancel",t.abrupt("break",11);case 7:return o="order/del",t.abrupt("break",11);case 9:return o="order/confirm",t.abrupt("break",11);case 11:return t.next=13,e.$post(o,{id:n});case 13:l=t.sent,code=l.code,l.data,d=l.msg,1==code&&(e.$message({message:d,type:"success"}),e.$emit("refresh"));case 18:case"end":return t.stop()}}),t)})))()},getTipsText:function(e){switch(e){case 0:return"确认取消订单吗？";case 1:return"确认删除订单吗?";case 2:return"确认收货吗?"}},showDeliverDialog:function(e){console.log("showDeliverDialog"),this.aid=e,this.showDeliver=!0}},computed:{getOrderStatus:function(){return function(e){var text="";switch(e){case 0:text="待支付";break;case 1:text="待发货";break;case 2:text="待收货";break;case 3:text="已完成";break;case 4:text="订单已关闭"}return text}},getCancelTime:function(){return function(time){return time-Date.now()/1e3}}}}),l=(r(671),r(8)),component=Object(l.a)(o,(function(){var e=this,t=e._self._c;return t("div",{staticClass:"order-list"},[e._l(e.list,(function(r){return t("div",{key:r.id,staticClass:"item m-b-16"},[t("div",{staticClass:"item-hd flex"},[t("nuxt-link",{staticClass:"flex-1 lighter sm line-1 m-r-20",staticStyle:{"min-width":"0"},attrs:{to:"/shop_street_detail?id=".concat(r.shop.id)}},[e._v("\n                "+e._s(r.shop.name)+"\n            ")]),e._v(" "),t("div",{staticClass:"flex-1 lighter sm"},[e._v("\n                下单时间："+e._s(r.create_time)+"\n            ")]),e._v(" "),t("div",{staticClass:"flex-1 lighter sm"},[e._v("\n                订单编号："+e._s(r.order_sn)+"\n            ")]),e._v(" "),4==r.pay_way&&0==r.order_status?t("div",{class:["status sm",{primary:0==r.order_status}]},[e._v("\n                线下付款\n            ")]):t("div",{class:["status sm",{primary:0==r.order_status}]},[e._v("\n                "+e._s(e.getOrderStatus(r.order_status))+"\n            ")])],1),e._v(" "),t("div",{staticClass:"item-con flex"},[t("div",{staticClass:"goods"},e._l(r.order_goods,(function(r,n){return t("nuxt-link",{key:n,staticClass:"goods-item flex",attrs:{to:"/goods_details/".concat(r.goods_id)}},[t("el-image",{staticClass:"goods-img",attrs:{src:r.image,alt:""}}),e._v(" "),t("div",{staticClass:"goods-info flex-1"},[t("div",{staticClass:"goods-name m-b-8 flex row-between"},[t("div",{staticClass:"line1",staticStyle:{width:"350px"}},[r.is_seckill?t("el-tag",{attrs:{size:"mini",effect:"plain"}},[e._v("秒杀")]):e._e(),e._v("\n                                "+e._s(r.goods_name)+"\n                            ")],1),e._v(" "),t("div",{staticClass:"num"},[e._v("x"+e._s(r.goods_num))])]),e._v(" "),t("div",{staticClass:"sm muted m-b-8"},[e._v("\n                            "+e._s(r.spec_value)+"\n                        ")]),e._v(" "),t("div",{staticClass:"primary"},[t("price-formate",{attrs:{price:r.goods_price}})],1)])],1)})),1),e._v(" "),t("div",{staticClass:"pay-price flex-col col-center row-center",staticStyle:{"padding-left":"30px"}},[t("div",{staticClass:"lighter"},[e._v("共"+e._s(r.goods_count)+"件商品")]),e._v(" "),t("div",{staticClass:"lighter m-t-8 flex"},[e._v("\n                    付款金额：\n                    "),t("span",{staticClass:"primary"},[t("price-formate",{attrs:{price:r.order_amount,"subscript-size":12,"first-size":16,"second-size":12}})],1)])]),e._v(" "),t("div",{staticClass:"operate flex-col col-center row-center sm"},[r.pay_btn&&4!=r.pay_way?t("nuxt-link",{staticClass:"btn m-b-16 bg-primary flex row-center white sm",attrs:{to:"/payment?id=".concat(r.id,"&from=order")}},[t("span",{staticClass:"m-r-8"},[e._v("去付款")]),e._v(" "),e.getCancelTime(r.order_cancel_time)>0?t("count-down",{attrs:{time:e.getCancelTime(r.order_cancel_time),format:"hh:mm:ss"},on:{finish:function(t){return e.$emit("refresh")}}}):e._e()],1):e._e(),e._v(" "),r.take_btn?t("div",{staticClass:"btn m-b-10 primary flex row-center sm plain",on:{click:function(t){return e.handleOrder(2,r.id)}}},[e._v("\n                    确认收货\n                ")]):e._e(),e._v(" "),r.delivery_btn?t("div",{staticClass:"m-b-10 muted flex row-center sm",on:{click:function(t){return e.showDeliverDialog(r.id)}}},[e._v("\n                    物流查询\n                ")]):e._e(),e._v(" "),r.cancel_btn?t("div",{staticClass:"m-b-10 muted row-center sm",on:{click:function(t){return e.handleOrder(0,r.id)}}},[e._v("\n                    取消订单\n                ")]):e._e(),e._v(" "),r.del_btn?t("div",{staticClass:"m-b-10 muted row-center sm",on:{click:function(t){return e.handleOrder(1,r.id)}}},[e._v("\n                    删除订单\n                ")]):e._e(),e._v(" "),t("nuxt-link",{staticClass:"lighter",attrs:{to:"/user/order/detail?id=".concat(r.id)}},[t("span",[e._v("查看详情")])])],1)]),e._v(" "),4==r.pay_way&&0==r.order_status?t("div",{staticClass:"lighter sm muted m-t-5",staticStyle:{"text-align":"right"}},[e._v("\n            如已付款，请通知商家【确认收款】\n        ")]):e._e()])})),e._v(" "),t("deliver-search",{attrs:{aid:e.aid},model:{value:e.showDeliver,callback:function(t){e.showDeliver=t},expression:"showDeliver"}})],2)}),[],!1,null,"760e01d2",null);t.default=component.exports;installComponents(component,{PriceFormate:r(478).default,CountDown:r(485).default,DeliverSearch:r(538).default})}}]);