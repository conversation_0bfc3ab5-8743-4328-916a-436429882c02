{layout name="layout1" /}

<div class="layui-fluid">
    <div class="layui-card" style="margin-top: 15px;"><!--操作提示-->
        <div class="layui-card-body">
            <div class="layui-collapse like-layui-collapse" lay-accordion="" style="border:1px dashed #c4c4c4">
                <div class="layui-colla-item">
                    <h2 class="layui-colla-title like-layui-colla-title" style="background-color: #fff">操作提示</h2>
                    <div class="layui-colla-content layui-show">
                        <p>*上传资质后,分类所涉及的资质可以不再上传。</p>
                        <p>*如果资质不符或者未上传资质将被驳回。</p>
                        <p>*审核通过后，重新编辑商品或者添加商品相应的分类则无需再次提交资质。</p>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-card-header">
            <h3>我的资质</h3>
        </div>
        <div class="layui-card-body">
            <!-- 搜索区域 -->
            <div class="layui-form">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">资质名称:</label>
                        <div class="layui-input-inline">
                            <input type="text" name="qualification_name" placeholder="请输入资质名称" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">审核状态:</label>
                        <div class="layui-input-inline">
                            <select name="status">
                                <option value="">全部状态</option>
                                <option value="0">待审核</option>
                                <option value="1">审核通过</option>
                                <option value="2">审核拒绝</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline" style="margin-left: 20px;">
                        <button class="layui-btn layui-btn-normal" lay-submit lay-filter="search">查询</button>
                        <button class="layui-btn layui-btn-primary" lay-submit lay-filter="clear-search">重置</button>
                    </div>
                </div>
            </div>

            <!-- 操作按钮 -->
            <div style="margin-bottom: 15px;">
                <button type="button" class="layui-btn layui-btn-normal" id="upload-btn">
                    <i class="layui-icon layui-icon-upload"></i> 上传资质
                </button>
            </div>

            <!-- 数据表格 -->
            <table id="qualification-table" lay-filter="qualification-table"></table>
        </div>
    </div>
</div>

<!-- 状态模板 -->
<script type="text/html" id="statusTpl">
    {{# if(d.status == 0){ }}
        <span class="layui-badge layui-bg-orange">待审核</span>
    {{# } else if(d.status == 1){ }}
        <span class="layui-badge layui-bg-green">审核通过</span>
    {{# } else if(d.status == 2){ }}
        <span class="layui-badge layui-bg-red">审核拒绝</span>
    {{# } }}
</script>

<!-- 图片模板 -->
<script type="text/html" id="documentTpl">
    {{# if(d.document_path){ }}
        <div style="text-align: center;">
            <img src="{{d.document_path}}" style="max-width: 80px; max-height: 60px; border: 1px solid #ddd; border-radius: 4px; cursor: pointer;" onclick="layer.photos({photos: {data: [{src: '{{d.document_path}}', alt: '{{d.document_name}}'}]}, anim: 5});" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA4MCA2MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjgwIiBoZWlnaHQ9IjYwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik00MCAzMEM0MS4xMDQ2IDMwIDQyIDI5LjEwNDYgNDIgMjhDNDIgMjYuODk1NCA0MS4xMDQ2IDI2IDQwIDI2QzM4Ljg5NTQgMjYgMzggMjYuODk1NCAzOCAyOEMzOCAyOS4xMDQ2IDM4Ljg5NTQgMzAgNDAgMzBaIiBmaWxsPSIjQ0NDIi8+CjxwYXRoIGQ9Ik0zNSAzNEw0MCAyOUw0NSAzNEg0MEgzNVoiIGZpbGw9IiNDQ0MiLz4KPC9zdmc+'; this.style.opacity='0.5';">
            <div style="margin-top: 5px; font-size: 12px; color: #666;">{{d.document_name}}</div>
        </div>
    {{# } else { }}
        <span style="color: #999;">未上传</span>
    {{# } }}
</script>

<!-- 过期状态模板 -->
<script type="text/html" id="expireTpl">
    {{# if(d.expire_time == 0){ }}
        <span style="color: #5FB878;">永久有效</span>
    {{# } else if(d.is_expired){ }}
        <span style="color: #FF5722;">已过期</span>
    {{# } else { }}
        <span style="color: #333;">{{d.expire_time_text}}</span>
    {{# } }}
</script>

<!-- 操作模板 -->
<script type="text/html" id="operationTpl">
   <a class="layui-btn layui-btn-xs layui-btn-warm" lay-event="reupload">重新上传</a>
</script>

<script>
layui.config({
    version: "{$front_version}",
    base: '/static/lib/'
}).use(['layer', 'table', 'form'], function () {
    var $ = layui.jquery;
    var layer = layui.layer;
    var form = layui.form;
    var table = layui.table;

    // 渲染表格
    function getList() {
        table.render({
            id: 'qualification-table'
            ,elem: '#qualification-table'
            ,url: "{:url('shop_qualification/lists')}"
            , parseData: function(res) { // res 原始返回数据
                return {
                    'code' : res.code  // 0 代表正常返回
                    , 'msg' : res.msg  // 提示消息
                    , 'count' : res.data.count // 数据长度
                    , 'data' : res.data.lists  // 数据列表
                }
            }
            , response: { // 重新设定返回的数据格式
                statusCode: 1, // 成功的状态码，默认0
            }
            , page: true // 开启分页
            , limit: 15
            , limits: [10, 15, 20, 30, 50]
            , text: {
                none: '暂无数据'
            }
            , cols: [[ // 设置表头，二维数组，方法渲染必填
                {field: 'id', width: 80, title: 'ID', sort: true},
                {field: 'qualification_name', title: '资质名称', width: 200},
                {field: 'document_path', title: '资质图片', width: 150, templet: '#documentTpl', align: 'center'},
                {field: 'status', title: '审核状态', width: 120, templet: '#statusTpl'},
                <!-- {field: 'expire_time', title: '有效期', width: 150, templet: '#expireTpl'}, -->
                {field: 'audit_remark', title: '审核备注', width: 200},
                {field: 'create_time_text', title: '上传时间', width: 160},
                {fixed: 'right', title: '操作', width: 200, toolbar: '#operationTpl'}
            ]], done: function () {
                setTimeout(function () {
                    $(".layui-table-main tr").each(function (index, val) {
                        $($(".layui-table-fixed-l .layui-table-body tbody tr")[index]).height($(val).height());
                        $($(".layui-table-fixed-r .layui-table-body tbody tr")[index]).height($(val).height());
                    });
                }, 200);
            }
        });
    }

    // 首次加载
    getList();

    // 监听查询
    form.on('submit(search)', function(data){
        var field = data.field;
        // 执行重载
        table.reload('qualification-table', {
            where: field,
            page: {curr: 1}
        });
        return false;
    });

    // 重置查询
    form.on('submit(clear-search)', function(data){
        // 重置搜索
        $('input[name=qualification_name]').val('');
        $('select[name=status]').val('');
        form.render('select');
        // 重新加载数据表格
        table.reload('qualification-table', {
            where: {},
            page: {curr: 1}
        });
        return false;
    });

    // 上传资质按钮
    $('#upload-btn').click(function(){
        layer.open({
            type: 2,
            title: '上传资质',
            area: ['800px', '600px'],
            content: '{:url("shop_qualification/upload")}',
            end: function(){
                table.reload('qualification-table');
            }
        });
    });

    // 监听工具条
    table.on('tool(qualification-table)', function(obj){
        var data = obj.data;

        if(obj.event === 'view'){
            // 查看详情
            layer.open({
                type: 2,
                title: '资质详情',
                area: ['800px', '600px'],
                content: '{:url("shop_qualification/detail")}?id=' + data.id
            });
        } else if(obj.event === 'reupload'){
            // 重新上传
            layer.open({
                type: 2,
                title: '重新上传资质',
                area: ['800px', '600px'],
                content: '{:url("shop_qualification/upload")}?qualification_id=' + data.qualification_id,
                end: function(){
                    table.reload('qualification-table');
                }
            });
        } else if(obj.event === 'delete'){
            // 删除
            layer.confirm('确定要删除这个资质吗？', function(index){
                like.ajax({
                    url: '{:url("shop_qualification/delete")}',
                    data: {id: data.id},
                    type: "post",
                    success: function (res) {
                        if(res.code == 1) {
                            layer.msg(res.msg);
                            table.reload('qualification-table');
                        } else {
                            layer.msg(res.msg || '删除失败');
                        }
                    }
                });
                layer.close(index);
            });
        }
    });
});
</script>
