<?php



namespace app\common\model;


use app\common\basics\Models;
use app\common\model\goods\Goods;
/**
 * 角色 模型
 * Class Menu
 * @package app\common\model
 */
class ProductWord extends Models
{
    protected $name = 'product_word';


    // 自动时间戳
    protected $autoWriteTimestamp = true;
    protected $createTime = 'created_at';


    /**
     * 商品SKU 关联模型
     */
    public function word()
    {
        return $this->hasMany('word', 'id', 'word_id');
    }

    /**
     * 商品SKU 关联模型
     */
    public function goods()
    {
        return $this->hasMany(Goods::class, 'id', 'goods_id');
    }
}