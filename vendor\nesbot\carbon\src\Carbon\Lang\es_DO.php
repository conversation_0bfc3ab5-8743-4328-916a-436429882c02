<?php

/**
 * This file is part of the Carbon package.
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

/*
 * Authors: <AUTHORS>
 * - <PERSON> B
 * - <PERSON>
 * - <PERSON><PERSON>
 * - <PERSON>
 * - <PERSON>
 * - <PERSON><PERSON>
 * - <PERSON>
 */
return array_replace_recursive(require __DIR__.'/es.php', [
    'diff_before_yesterday' => 'anteayer',
    'formats' => [
        'LT' => 'h:mm A',
        'LTS' => 'h:mm:ss A',
        'LLL' => 'D [de] MMMM [de] YYYY h:mm A',
        'LLLL' => 'dddd, D [de] MMMM [de] YYYY h:mm A',
    ],
]);
