<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Apigateway\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * 服务发布列表详情
 *
 * @method string getVersionName() 获取版本号。
注意：此字段可能返回 null，表示取不到有效值。
 * @method void setVersionName(string $VersionName) 设置版本号。
注意：此字段可能返回 null，表示取不到有效值。
 * @method string getVersionDesc() 获取版本描述。
注意：此字段可能返回 null，表示取不到有效值。
 * @method void setVersionDesc(string $VersionDesc) 设置版本描述。
注意：此字段可能返回 null，表示取不到有效值。
 * @method string getReleaseTime() 获取版本发布时间。
注意：此字段可能返回 null，表示取不到有效值。
 * @method void setReleaseTime(string $ReleaseTime) 设置版本发布时间。
注意：此字段可能返回 null，表示取不到有效值。
 */
class ServiceReleaseHistoryInfo extends AbstractModel
{
    /**
     * @var string 版本号。
注意：此字段可能返回 null，表示取不到有效值。
     */
    public $VersionName;

    /**
     * @var string 版本描述。
注意：此字段可能返回 null，表示取不到有效值。
     */
    public $VersionDesc;

    /**
     * @var string 版本发布时间。
注意：此字段可能返回 null，表示取不到有效值。
     */
    public $ReleaseTime;

    /**
     * @param string $VersionName 版本号。
注意：此字段可能返回 null，表示取不到有效值。
     * @param string $VersionDesc 版本描述。
注意：此字段可能返回 null，表示取不到有效值。
     * @param string $ReleaseTime 版本发布时间。
注意：此字段可能返回 null，表示取不到有效值。
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("VersionName",$param) and $param["VersionName"] !== null) {
            $this->VersionName = $param["VersionName"];
        }

        if (array_key_exists("VersionDesc",$param) and $param["VersionDesc"] !== null) {
            $this->VersionDesc = $param["VersionDesc"];
        }

        if (array_key_exists("ReleaseTime",$param) and $param["ReleaseTime"] !== null) {
            $this->ReleaseTime = $param["ReleaseTime"];
        }
    }
}
