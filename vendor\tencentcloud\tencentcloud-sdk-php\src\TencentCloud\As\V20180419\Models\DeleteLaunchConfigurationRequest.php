<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\As\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * DeleteLaunchConfiguration请求参数结构体
 *
 * @method string getLaunchConfigurationId() 获取需要删除的启动配置ID。
 * @method void setLaunchConfigurationId(string $LaunchConfigurationId) 设置需要删除的启动配置ID。
 */
class DeleteLaunchConfigurationRequest extends AbstractModel
{
    /**
     * @var string 需要删除的启动配置ID。
     */
    public $LaunchConfigurationId;

    /**
     * @param string $LaunchConfigurationId 需要删除的启动配置ID。
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("LaunchConfigurationId",$param) and $param["LaunchConfigurationId"] !== null) {
            $this->LaunchConfigurationId = $param["LaunchConfigurationId"];
        }
    }
}
