(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["bundle-pages-invite_fans-invite_fans"],{"0a26":function(t,e,n){"use strict";n.r(e);var i=n("b68f"),a=n("e8f1");for(var r in a)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(r);var s=n("f0c5"),o=Object(s["a"])(a["default"],i["b"],i["c"],!1,null,"3ecf84cc",null,!1,i["a"],void 0);e["default"]=o.exports},"10b0":function(t,e,n){"use strict";n.r(e);var i=n("a6592"),a=n("803f");for(var r in a)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(r);n("7c45");var s=n("f0c5"),o=Object(s["a"])(a["default"],i["b"],i["c"],!1,null,"b6610858",null,!1,i["a"],void 0);e["default"]=o.exports},4558:function(t,e,n){"use strict";n("7a82");var i=n("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("a9e3");var a=i(n("a129")),r=i(n("daac")),s=i(n("39b0")),o=i(n("e9fe")),c=i(n("3be6")),u={name:"share-poster",components:{lPainter:a.default,lPainterImage:r.default,lPainterText:s.default,lPainterView:o.default,lPainterQrcode:c.default},props:{config:{type:Object,default:function(){return{}}},goodsId:{type:[Number,String],default:""},qrcode:{type:[String],default:""}},data:function(){return{}},methods:{handleSuccess:function(t){this.$emit("success",t)}}};e.default=u},4932:function(t,e,n){var i=n("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */uni-page-body[data-v-b6610858]{padding:0}.invite-fans[data-v-b6610858]{min-height:100vh;overflow:hidden}.invite-fans .poster[data-v-b6610858]{width:%?600?%;margin:%?40?% 0}.invite-fans .footer[data-v-b6610858]{padding:%?30?%;width:100%}.invite-fans .copy-btn[data-v-b6610858]{color:#ff2c3c}.invite-fans .save-btn[data-v-b6610858]{color:#fff;background-color:#ff2c3c}',""]),t.exports=e},"5bfd":function(t,e,n){"use strict";n("7a82");var i=n("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("99af");var a=i(n("f3f3")),r=i(n("f07e")),s=i(n("c964")),o=n("2eec"),c=n("f287"),u=n("26cb"),f=n("1524"),d=n("a5ae"),l={data:function(){return{path:"",qrCode:"",loading:!0,showPoster:!1,poster:""}},onLoad:function(){var t=this;return(0,s.default)((0,r.default)().mark((function e(){return(0,r.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.getPoster();case 2:t.showPoster=!0;case 3:case"end":return e.stop()}}),e)})))()},methods:{onCopy:function(t){(0,d.copy)(t)},getPoster:function(){var t=this;return(0,s.default)((0,r.default)().mark((function e(){var n;return(0,r.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,(0,f.apiDistributionPoster)();case 2:n=e.sent,t.poster=n.data.poster;case 4:case"end":return e.stop()}}),e)})))()},getMnpQrCode:function(){var t=this;(0,o.apiMnpQrCode)({type:0,url:"pages/index/index"}).then((function(e){console.log(e),t.qrCode=e.data.qr_code,t.showPoster=!0}))},saveImageToAlbum:function(){this.$toast({title:"请长按图片保存"})},handleSuccess:function(t){this.path=t,this.loading=!1}},computed:(0,a.default)((0,a.default)({},(0,u.mapGetters)(["inviteCode","userInfo"])),{},{link:function(){return"".concat(c.baseURL).concat(c.basePath,"?invite_code=").concat(this.inviteCode)}})};e.default=l},"7c45":function(t,e,n){"use strict";var i=n("a33a"),a=n.n(i);a.a},"803f":function(t,e,n){"use strict";n.r(e);var i=n("5bfd"),a=n.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(r);e["default"]=a.a},a33a:function(t,e,n){var i=n("4932");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var a=n("4f06").default;a("5e433af2",i,!0,{sourceMap:!1,shadowMode:!1})},a6592:function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"c",(function(){return r})),n.d(e,"a",(function(){return i}));var i={invitePoster:n("0a26").default,loadingView:n("dbb2").default},a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{staticClass:"pages"},[n("v-uni-view",{staticClass:"invite-fans flex-col col-center"},[n("v-uni-image",{staticClass:"poster",attrs:{src:t.path,mode:"widthFix"}}),t.showPoster?n("invite-poster",{attrs:{config:{avatar:t.userInfo.avatar,nickname:t.userInfo.nickname,code:t.inviteCode,link:t.link,qrCode:t.qrCode,poster:t.poster}},on:{success:function(e){arguments[0]=e=t.$handleEvent(e),t.handleSuccess.apply(void 0,arguments)}}}):t._e(),n("v-uni-view",{staticClass:"bg-white footer flex-1"},[n("v-uni-view",{staticClass:"m-b-40"},[n("v-uni-view",{staticClass:"m-b-10 sm lighter"},[t._v("我的邀请码")]),n("v-uni-view",{staticClass:"flex row-between"},[n("v-uni-view",{staticClass:"font-size-44"},[t._v(t._s(t.inviteCode))]),n("v-uni-view",{staticClass:"sm m-r-30 copy-btn",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onCopy(t.inviteCode)}}},[t._v("点击复制")])],1)],1),n("v-uni-button",{staticClass:"save-btn br60",attrs:{size:"lg"}},[t._v("长按保存到相册")])],1)],1),n("loading-view",{directives:[{name:"show",rawName:"v-show",value:t.loading,expression:"loading"}]})],1)},r=[]},b68f:function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return a})),n.d(e,"a",(function(){}));var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",[n("l-painter",{attrs:{css:"width: 600rpx;height: 960rpx;",isCanvasToTempFilePath:!0,"custom-style":"position: fixed; left: 200%"},on:{success:function(e){arguments[0]=e=t.$handleEvent(e),t.handleSuccess.apply(void 0,arguments)}}},[n("l-painter-image",{attrs:{src:t.config.poster,css:"position:absolute; width: 600rpx;height: 960rpx;object-fit: cover; border-radius: 20rpx;"}}),n("l-painter-view",{attrs:{css:"position:absolute;background-color: #ffffff;width:100%;height: 240rpx;border-radius: 14rpx; bottom: 0;left: 0;padding-top: 30rpx;"}},[n("l-painter-view",{attrs:{css:"width: 330rpx; display: inline-block;padding-left: 30rpx;"}},[n("l-painter-view",[n("l-painter-image",{attrs:{src:t.config.avatar,css:"width: 80rpx;  height: 80rpx; border-radius: 50%;"}}),n("l-painter-text",{attrs:{text:t.config.nickname,css:"margin-top: 10rpx; margin-left: 20rpx; color: #333333; font-size: 34rpx;line-clamp:1;font-weight: bold; "}})],1),n("l-painter-view",{attrs:{css:"margin-top: 30rpx;line-height: 34rpx;"}},[n("l-painter-text",{attrs:{text:"邀请你一起来赚大钱",css:"color: #333333; font-size: 28rpx;"}})],1),n("l-painter-view",{attrs:{css:"margin-top: 20rpx;"}},[n("l-painter-text",{attrs:{text:"邀请码："+t.config.code,css:"color: #FF2C3C; font-size: 28rpx;"}})],1)],1),n("l-painter-view",{attrs:{css:"display: inline-block;margin-top:10rpx;"}},[n("l-painter-view",{attrs:{css:"padding-left: 30rpx;"}},[n("l-painter-qrcode",{attrs:{css:"width: 180rpx; height: 180rpx;",text:t.config.link}})],1)],1)],1)],1)],1)},a=[]},e8f1:function(t,e,n){"use strict";n.r(e);var i=n("4558"),a=n.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(r);e["default"]=a.a}}]);