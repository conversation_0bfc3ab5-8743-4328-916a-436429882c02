export const ActivityArea = () => import('../../components/activity-area.vue' /* webpackChunkName: "components/activity-area" */).then(c => wrapFunctional(c.default || c))
export const AdItem = () => import('../../components/ad-item.vue' /* webpackChunkName: "components/ad-item" */).then(c => wrapFunctional(c.default || c))
export const AddressAdd = () => import('../../components/address-add.vue' /* webpackChunkName: "components/address-add" */).then(c => wrapFunctional(c.default || c))
export const AddressList = () => import('../../components/address-list.vue' /* webpackChunkName: "components/address-list" */).then(c => wrapFunctional(c.default || c))
export const AfterSalesList = () => import('../../components/after-sales-list.vue' /* webpackChunkName: "components/after-sales-list" */).then(c => wrapFunctional(c.default || c))
export const CommentList = () => import('../../components/comment-list.vue' /* webpackChunkName: "components/comment-list" */).then(c => wrapFunctional(c.default || c))
export const CountDown = () => import('../../components/count-down.vue' /* webpackChunkName: "components/count-down" */).then(c => wrapFunctional(c.default || c))
export const CouponsList = () => import('../../components/coupons-list.vue' /* webpackChunkName: "components/coupons-list" */).then(c => wrapFunctional(c.default || c))
export const DeliverSearch = () => import('../../components/deliver-search.vue' /* webpackChunkName: "components/deliver-search" */).then(c => wrapFunctional(c.default || c))
export const EvaluationList = () => import('../../components/evaluation-list.vue' /* webpackChunkName: "components/evaluation-list" */).then(c => wrapFunctional(c.default || c))
export const GoodsList = () => import('../../components/goods-list.vue' /* webpackChunkName: "components/goods-list" */).then(c => wrapFunctional(c.default || c))
export const HomeSeckill = () => import('../../components/home-seckill.vue' /* webpackChunkName: "components/home-seckill" */).then(c => wrapFunctional(c.default || c))
export const InputExpress = () => import('../../components/input-Express.vue' /* webpackChunkName: "components/input-express" */).then(c => wrapFunctional(c.default || c))
export const NullData = () => import('../../components/null-data.vue' /* webpackChunkName: "components/null-data" */).then(c => wrapFunctional(c.default || c))
export const NumberBox = () => import('../../components/number-box.vue' /* webpackChunkName: "components/number-box" */).then(c => wrapFunctional(c.default || c))
export const OrderList = () => import('../../components/order-list.vue' /* webpackChunkName: "components/order-list" */).then(c => wrapFunctional(c.default || c))
export const PriceFormate = () => import('../../components/price-formate.vue' /* webpackChunkName: "components/price-formate" */).then(c => wrapFunctional(c.default || c))
export const ShopItem = () => import('../../components/shop-item.vue' /* webpackChunkName: "components/shop-item" */).then(c => wrapFunctional(c.default || c))
export const Upload = () => import('../../components/upload.vue' /* webpackChunkName: "components/upload" */).then(c => wrapFunctional(c.default || c))
export const LayoutAslideNav = () => import('../../components/layout/aslide-nav.vue' /* webpackChunkName: "components/layout-aslide-nav" */).then(c => wrapFunctional(c.default || c))
export const LayoutCategory = () => import('../../components/layout/category.vue' /* webpackChunkName: "components/layout-category" */).then(c => wrapFunctional(c.default || c))
export const LayoutFloatNav = () => import('../../components/layout/float-nav.vue' /* webpackChunkName: "components/layout-float-nav" */).then(c => wrapFunctional(c.default || c))
export const LayoutFooter = () => import('../../components/layout/footer.vue' /* webpackChunkName: "components/layout-footer" */).then(c => wrapFunctional(c.default || c))
export const LayoutHeader = () => import('../../components/layout/header.vue' /* webpackChunkName: "components/layout-header" */).then(c => wrapFunctional(c.default || c))
export const LayoutMainNav = () => import('../../components/layout/main-nav.vue' /* webpackChunkName: "components/layout-main-nav" */).then(c => wrapFunctional(c.default || c))

// nuxt/nuxt.js#8607
function wrapFunctional(options) {
  if (!options || !options.functional) {
    return options
  }

  const propKeys = Array.isArray(options.props) ? options.props : Object.keys(options.props || {})

  return {
    render(h) {
      const attrs = {}
      const props = {}

      for (const key in this.$attrs) {
        if (propKeys.includes(key)) {
          props[key] = this.$attrs[key]
        } else {
          attrs[key] = this.$attrs[key]
        }
      }

      return h(options, {
        on: this.$listeners,
        attrs,
        props,
        scopedSlots: this.$scopedSlots,
      }, this.$slots.default)
    }
  }
}
