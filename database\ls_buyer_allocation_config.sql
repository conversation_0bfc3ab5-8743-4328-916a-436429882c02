-- 采购人员分配比例配置表
CREATE TABLE `ls_buyer_allocation_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `tier_level` tinyint(1) NOT NULL COMMENT '商家等级：0=0元入驻，1=商家会员，2=实力厂商',
  `total_count` int(11) NOT NULL DEFAULT 0 COMMENT '分配总数',
  `level_1_ratio` decimal(5,2) NOT NULL DEFAULT 0.00 COMMENT '1级活跃度比例',
  `level_2_ratio` decimal(5,2) NOT NULL DEFAULT 0.00 COMMENT '2级活跃度比例',
  `level_3_ratio` decimal(5,2) NOT NULL DEFAULT 0.00 COMMENT '3级活跃度比例',
  `create_time` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `update_time` int(11) UNSIGNED NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_tier_level` (`tier_level`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='采购人员分配比例配置表';
