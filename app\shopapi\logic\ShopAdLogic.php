<?php
namespace app\shopapi\logic;

use app\common\basics\Logic;
use app\common\model\AdPosition;
use app\common\model\AdOrder;
use app\common\model\Ad;
use app\common\enum\PayEnum;
use app\common\enum\OrderEnum;
use app\common\enum\ClientEnum;
use app\api\logic\PayLogic; // 引用API下的PayLogic处理支付
use app\common\server\UrlServer;
use think\facade\Db;

class ShopAdLogic extends Logic
{
    /**
     * @notes 获取可购买的广告位列表
     * @param array $params 查询参数 (可包含终端类型 terminal, 状态 status 等)
     * @return array
     */
    public static function listAvailableSlots($params = [])
    {
        $model = new AdPosition();
        $where = [
            ['del', '=', 0],
            ['status', '=', 1], // 只显示启用的
            ['is_buy', '=', 1] // 假设1代表可购买的广告位类型
        ];

        if (!empty($params['terminal'])) {
            $where[] = ['terminal', '=', $params['terminal']];
        }

        // 可以根据需要添加更多筛选条件，比如排除已售罄的
        $page = $params['page'] ?? 1;
        $pageSize = $params['page_size'] ?? 10;

        $total = $model->where($where)->count();

        $lists = $model->where($where)
            ->order('id desc')
            ->page($page, $pageSize)
            ->select()
            ->toArray();

        foreach ($lists as &$item) {
            $item['image_url'] = UrlServer::getFileUrl($item['image']);
        }

        return [
            'lists' => $lists,
            'page' => $page,
            'page_size' => $pageSize,
            'count' => $total,
            'more' => is_more($total, $page, $pageSize)
        ];
    }

    /**
     * @notes 购买广告位
     * @param int $shop_id
     * @param array $post (包含 ad_position_id, pay_way, from, buy_nums 等)
     * @return array|bool
     */
    public static function purchaseSlot($shop_id, $post)
    {
        Db::startTrans();
        try {
            $ad_position_id = intval($post['ad_position_id']);
            $buy_nums = isset($post['buy_nums']) ? intval($post['buy_nums']) : 1; // 购买数量，默认为1

            $ad_position = AdPosition::where([
                ['id', '=', $ad_position_id],
                ['del', '=', 0],
                ['status', '=', 1],
                ['ad_type', '=', 1]
            ])->find();

            if (!$ad_position) {
                throw new \Exception('广告位不存在或不可购买');
            }

            // 检查购买限制
            if ($ad_position->ad_limits > 0 && ($ad_position->ad_buys + $buy_nums) > $ad_position->ad_limits) {
                throw new \Exception('超出广告位购买限制');
            }

            $ad_fee = $ad_position->ad_fee; // 单个广告位费用
            $ad_days = $ad_position->ad_days; // 单个广告位天数
            $total_amount = $ad_fee * $buy_nums; // 总金额
            $total_days = $ad_days * $buy_nums; // 总天数 (如果购买多份是叠加时间的话)

            // 创建广告订单 AdOrder
            $order_sn = createSn('ad_order', 'order_sn');
            $ad_order_data = [
                'shop_id' => $shop_id,
                'ad_position_id' => $ad_position_id,
                'order_sn' => $order_sn,
                'ad_price' => $total_amount,
                'ad_buynums' => $buy_nums,
                'buy_time' => $total_days * 86400, // 购买时长（秒）
                'status' => PayEnum::UNPAID, // 待支付
                'create_time' => time(),
                // 其他需要记录的字段，如广告位快照信息
                'ad_position_snap' => json_encode($ad_position->toArray(), JSON_UNESCAPED_UNICODE)
            ];
            $ad_order = AdOrder::create($ad_order_data);
            if (!$ad_order) {
                throw new \Exception('创建广告订单失败');
            }

            // 调用支付逻辑 (复用 RechargeLogic 中的方式)
            $from = $post['from'] ?? ClientEnum::h5;
            $pay_way = $post['pay_way'] ?? PayEnum::WECHAT_PAY;

            switch ($pay_way) {
                case PayEnum::WECHAT_PAY:
                    $pay_info = PayLogic::wechatPay($order_sn, OrderEnum::AD_ORDER, $from); // 注意：需要定义 OrderEnum::AD_ORDER
                    break;
                case PayEnum::ALI_PAY:
                    $pay_info = PayLogic::aliPay($order_sn, OrderEnum::AD_ORDER, $from); // 注意：需要定义 OrderEnum::AD_ORDER
                    break;
                default:
                    throw new \Exception('不支持的支付方式');
            }

            if ($pay_info === false || (isset($pay_info['code']) && $pay_info['code'] == 0)) {
                $error_msg = PayLogic::getError() ?: (isset($pay_info['msg']) ? $pay_info['msg'] : '创建支付订单失败');
                throw new \Exception($error_msg);
            }

            Db::commit();
            return $pay_info; // 返回支付所需信息

        } catch (\Exception $e) {
            Db::rollback();
            self::setError($e->getMessage());
            return false;
        }
    }

    /**
     * @notes 获取商家已购买的广告位列表 (广告订单列表)
     * @param int $shop_id
     * @param array $params (分页, 状态等)
     * @return array
     */
    public static function listMyShopSlots($shop_id, $params)
    {
        $model = new AdOrder();
        $where = [
            ['shop_id', '=', $shop_id]
        ];

        if (isset($params['status']) && $params['status'] !== '') {
            $where[] = ['status', '=', intval($params['status'])];
        }

        $page = $params['page_no'] ?? 1;
        $limit = $params['page_size'] ?? 15;

        $count = $model->where($where)->count();
        $lists = $model->where($where)
            ->with(['adPosition']) // 关联广告位信息
            ->page($page, $limit)
            ->order('id desc')
            ->select()
            ->toArray();

            foreach ($lists as &$item) {
                // 处理广告位快照和关联信息
                if (isset($item['ad_position_snap']) && is_string($item['ad_position_snap'])) {
                    $item['ad_position_info'] = json_decode($item['ad_position_snap'], true);
                } elseif (isset($item['ad_position'])) {
                     $item['ad_position_info'] = $item['ad_position']; // 使用实时关联信息
                } else {
                     $item['ad_position_info'] = null;
                }
                unset($item['ad_position_snap'], $item['ad_position']); // 清理原始字段

                // 优化状态显示
                $item['status_text'] = PayEnum::getPayStatus($item['status']);
                $item['status_style'] = $item['status'] == PayEnum::ISPAID ? 'success' : ($item['status'] == PayEnum::UNPAID ? 'warning' : 'danger');

                // 计算剩余时间等...
                if ($item['status'] == PayEnum::ISPAID && $item['start_time'] && $item['end_time']) {
                     $item['remaining_seconds'] = max(0, $item['end_time'] - time());
                     $item['remaining_days'] = floor($item['remaining_seconds'] / 86400);
                     $item['progress'] = min(100, round(100 * (time() - $item['start_time']) / ($item['end_time'] - $item['start_time'])));
                } else {
                     $item['remaining_seconds'] = 0;
                     $item['remaining_days'] = 0;
                     $item['progress'] = 0;
                }

                // 统一时间格式
                $item['create_time_text'] = $item['create_time'];
//                $item['start_time_text'] = $item['start_time'] ? date('Y-m-d H:i', $item['start_time']) : '-';
//                $item['end_time_text'] = $item['end_time'] ? date('Y-m-d H:i', $item['end_time']) : '-';

                // 获取广告内容
                $ad_content = Ad::where('ad_order_id', $item['id'])->find();
                $item['ad_content'] = $ad_content ? $ad_content->toArray() : null;

                // 优化图片处理
                if ($item['ad_content'] && $item['ad_content']['image']) {
                    $item['ad_content']['image_url'] = UrlServer::getFileUrl($item['ad_content']['image']);
                    $item['ad_content']['image_thumb'] = UrlServer::getFileUrl($item['ad_content']['image'], 'thumb');
                }

                // 优化商品信息获取
                $item['goods_info'] = [];
                if ($item['ad_content'] && $item['ad_content']['link']) {
                    // 支持多种链接格式解析
                    $link = $item['ad_content']['link'];
                    if (strpos($link, 'productDetails/index?id=') !== false) {
                        $goods_id = explode('=', $link)[1];
                    } elseif (strpos($link, 'goods/detail?id=') !== false) {
                        $goods_id = explode('=', $link)[1];
                    } elseif (is_numeric($link)) {
                        $goods_id = $link;
                    }

                    if (isset($goods_id) && is_numeric($goods_id)) {
                        $item['goods_info'] = Db::name('goods')
                            ->where('id', $goods_id)
                            ->field('id,name,image')
                            ->find();
                        if ($item['goods_info']) {
                            $item['goods_info']['image_url'] = UrlServer::getFileUrl($item['goods_info']['image']);
                        }
                    }
                }
            }

        return [
            'count' => $count,
            'lists' => $lists,
            'page_no' => $page,
            'page_size' => $limit,
            'more' => is_more($count, $page, $limit)
        ];
    }

    /**
     * @notes 更新/管理已购买的广告位内容
     * @param int $shop_id
     * @param array $post (包含 ad_order_id, title, image, link_type, link 等)
     * @return bool
     */
    public static function updateMyAdSlot($shop_id, $post)
    {
        Db::startTrans();
        try {
            $ad_order_id = intval($post['ad_order_id']);
            $ad_order = AdOrder::where([
                ['id', '=', $ad_order_id],
                ['shop_id', '=', $shop_id],
                ['status', '=', PayEnum::ISPAID] // 必须是已支付的
            ])->find();

            if (!$ad_order) {
                throw new \Exception('广告订单不存在或状态不正确');
            }

            // 检查广告是否已过期
            if ($ad_order->end_time < time()) {
                 throw new \Exception('广告已过期，无法修改');
            }

            // 查找或创建 Ad 记录 (广告内容)
            $ad_content = Ad::where('ad_order_id', $ad_order_id)->find();
            $ad_data = [
                'pid' => $ad_order->ad_position_id,
                'shop_id' => $shop_id,
                'ad_order_id' => $ad_order_id,
                'title' => $post['title'] ?? '',
                'image' => $post['image'] ?? '',
                'link_type' => intval($post['link_type'] ?? 0),
                'link' => $post['link'] ?? '',
                'terminal' => $ad_order->adPosition->terminal ?? 0, // 从关联或快照获取终端
                'status' => 1, // 默认启用
                'start_time' => $ad_order->start_time,
                'end_time' => $ad_order->end_time,
                // 其他字段...
            ];

            if ($ad_content) {
                // 更新现有内容
                $ad_data['update_time'] = time();
                Ad::update($ad_data, ['id' => $ad_content->id]);
            } else {
                // 创建新内容
                $ad_data['create_time'] = time();
                Ad::create($ad_data);
            }

            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            self::setError($e->getMessage());
            return false;
        }
    }

    //获取购买的未过期的广告位 title和id
   static public function listSlots($shop_id){

        $where = [
            ['shop_id', '=', $shop_id],
            ['status', '=', PayEnum::ISPAID],
            ['end_time', '>', time()]
        ];
        $lists = AdOrder::where($where)->alias('ao')
        ->join('ad_position a','a.id = ao.ad_position_id')
        ->field('a.id,title')
        ->select()
        ->toArray();
        return $lists;
    }
} 