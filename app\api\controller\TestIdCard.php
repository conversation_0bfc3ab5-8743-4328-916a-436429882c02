<?php

namespace app\api\controller;

use app\common\basics\Api;
use app\common\server\JsonServer;
use app\common\server\IdCardOcrServer;
use app\common\server\WechatService;

/**
 * 身份证识别测试控制器
 * Class TestIdCard
 * @package app\api\controller
 */
class TestIdCard extends Api
{
    public $like_not_need_login = ['testOcr', 'testSingle'];

    /**
     * @notes 测试身份证OCR识别
     * @return \think\response\Json
     */
    public function testOcr()
    {
        $post = $this->request->post();
        $frontUrl = $post['front_url'] ?? '';
        $backUrl = $post['back_url'] ?? '';

        if (empty($frontUrl) && empty($backUrl)) {
            return JsonServer::error('请提供身份证正面或背面图片URL');
        }

        try {
            $result = IdCardOcrServer::recognizeIdCard($frontUrl, $backUrl);
            
            if ($result['success']) {
                $extractedInfo = IdCardOcrServer::extractInfo($result['data']);
                return JsonServer::success('识别成功', [
                    'ocr_result' => $result,
                    'extracted_info' => $extractedInfo
                ]);
            } else {
                return JsonServer::error($result['message'], $result);
            }
        } catch (\Exception $e) {
            return JsonServer::error('识别异常: ' . $e->getMessage());
        }
    }

    /**
     * @notes 测试单张身份证识别
     * @return \think\response\Json
     */
    public function testSingle()
    {
        $post = $this->request->post();
        $imageUrl = $post['image_url'] ?? '';
        $type = $post['type'] ?? 'Front'; // Front 或 Back

        if (empty($imageUrl)) {
            return JsonServer::error('请提供图片URL');
        }

        try {
            $result = WechatService::idCardOcr($imageUrl, $type);
            
            if ($result['errcode'] === 0) {
                return JsonServer::success('识别成功', $result);
            } else {
                return JsonServer::error('识别失败: ' . $result['errmsg'], $result);
            }
        } catch (\Exception $e) {
            return JsonServer::error('识别异常: ' . $e->getMessage());
        }
    }

    /**
     * @notes 测试身份证信息验证
     * @return \think\response\Json
     */
    public function testValidate()
    {
        $post = $this->request->post();
        $ocrData = $post['ocr_data'] ?? [];
        $inputData = $post['input_data'] ?? [];

        if (empty($ocrData) || empty($inputData)) {
            return JsonServer::error('请提供OCR数据和输入数据');
        }

        try {
            $result = IdCardOcrServer::validateIdCardInfo($ocrData, $inputData);
            
            if ($result['valid']) {
                return JsonServer::success('验证通过', $result);
            } else {
                return JsonServer::error('验证失败: ' . implode('; ', $result['errors']), $result);
            }
        } catch (\Exception $e) {
            return JsonServer::error('验证异常: ' . $e->getMessage());
        }
    }

    /**
     * @notes 获取错误码说明
     * @return \think\response\Json
     */
    public function getErrorCodes()
    {
        $errorCodes = [
            0 => '成功',
            -1 => '系统错误',
            40001 => 'access_token无效或已过期',
            101000 => '图片URL错误',
            101001 => '未检测到证件',
            101002 => '图片大小超过限制或解码失败',
            101003 => '市场配额不足'
        ];

        return JsonServer::success('错误码说明', $errorCodes);
    }
}
