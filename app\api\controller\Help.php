<?php
namespace app\api\controller;

use app\api\logic\CommunityLogic;
use app\common\basics\Api;
use app\common\server\JsonServer;
use app\api\logic\HelpLogic;

class Help extends Api
{
    public $like_not_need_login = ['category',  'detail','js'];

    /**
     * 帮助中心分类
     */
    public function category()
    {
        $data = HelpLogic::category();
        return JsonServer::success('获取成功', $data);
    }

    /**
     * 列表
     */
    public function lists()
    {
        $get = $this->request->get();
        $get['user_id'] = $this->user_id;
        $get['page_no'] = $this->page_no;
        $get['page_size'] = $this->page_size;
        $data = HelpLogic::lists($get);
        return JsonServer::success('获取成功', $data);
    }

    /**
     * 详情
     */
    public function detail()
    {
        $id = $this->request->get('id', '', 'intval');
        $data = HelpLogic::detail($id);
        return JsonServer::success('获取成功', $data);
    }

    /**
     * 返回指定地址的js文件内容并转换为数组
     */
    public function js()
    {
        $url = 'https://www.huohanghang.cn/uploads/js/addressParseBundle.js';
        $content = file_get_contents($url);
        $array = json_decode($content, true);
        return $array;
    }

    /*
     * 意见反馈
     *
     */
    public function feedback(){
        $post = $this->request->post();
        $data = HelpLogic::addFeedback($this->user_id, $post);
        return $data;
    }
}