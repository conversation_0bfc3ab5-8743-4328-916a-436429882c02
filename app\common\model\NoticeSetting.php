<?php

namespace app\common\model;

use app\common\basics\Models;
use app\common\enum\NoticeEnum;

class NoticeSetting extends Models
{
    protected $name = 'dev_notice_setting';

    protected $json = ['variable', 'system_notice', 'sms_notice', 'oa_notice', 'mnp_notice'];

    protected $jsonAssoc = true;

    public function getSceneAttr($value, $data)
    {
        return NoticeEnum::getSceneDesc($value);
    }
}