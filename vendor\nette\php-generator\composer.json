{"name": "nette/php-generator", "description": "🐘 Nette PHP Generator: generates neat PHP code for you. Supports new PHP 8.0 features.", "keywords": ["nette", "php", "code", "scaffolding"], "homepage": "https://nette.org", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "require": {"php": ">=7.1", "nette/utils": "^3.1.2"}, "require-dev": {"nette/tester": "^2.0", "nikic/php-parser": "^4.4", "tracy/tracy": "^2.3", "phpstan/phpstan": "^0.12"}, "suggest": {"nikic/php-parser": "to use ClassType::withBodiesFrom() & GlobalFunction::withBodyFrom()"}, "autoload": {"classmap": ["src/"]}, "minimum-stability": "dev", "scripts": {"phpstan": "phpstan analyse", "tester": "tester tests -s"}, "extra": {"branch-alias": {"dev-master": "3.5-dev"}}}