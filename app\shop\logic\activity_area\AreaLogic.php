<?php

namespace app\shop\logic\activity_area;
use app\common\basics\Logic;
use app\common\server\JsonServer;
use think\facade\Db;
use app\common\model\activity_area\ActivityArea;
use app\common\model\activity_area\ActivityAreaGoods;
use app\common\server\UrlServer;


class  AreaLogic extends Logic {

    /**
     * Notes:活动专区列表
     * @param $get
     * @return array
     * @author: cjhao 2021/4/15 16:25
     */
    public static function lists($get){
        $where[] = ['del','=',0];
        $lists = ActivityArea::where($where)
            ->page($get['page'],$get['limit'])
            ->select();

        $count = ActivityArea::where($where)
            ->page($get['page'],$get['limit'])
            ->count();

        return ['count'=>$count,'lists'=>$lists];
    }

    /**
     * Notes:添加活动专区
     * @param $post
     * @return int|string
     * @author: cjhao 2021/4/15 17:25
     */
    public static function add($post){
        $post['create_time'] = time();
        if($post['status'] == 'on'){
            $post['status'] = 1; //专区显示
        }else{
            $post['status'] = 0; //专区隐藏
        }
        $post['image'] = UrlServer::setFileUrl($post['image']);
        return ActivityArea::insert($post);
    }

    /**
     * Notes:获取活动专区
     * @param $id
     * @return array|\think\Model|null
     * @author: cjhao 2021/4/15 17:29
     */
    public static function getActivityArea($id){
        return ActivityArea::where(['id'=>$id,'del'=>0])->find();
    }

    /***
     * 获取所有活动专区
     * @return \think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function getActivityAreaAll(){
        return ActivityArea::where(['del'=>0])
            ->select();
    }

    /***
     * @param $post
     * @return ActivityArea
     */
    public static function edit($post){
        $post['image'] = UrlServer::setFileUrl($post['image']);
        return ActivityArea::update($post);
    }

    /***
     * @param $id
     * @return ActivityArea
     */
    public static function del($id){

        return ActivityArea::update(['del'=>1,'id'=>$id]);
    }
}