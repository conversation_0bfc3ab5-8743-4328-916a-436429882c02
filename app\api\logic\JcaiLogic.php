<?php


namespace app\api\logic;


use app\common\basics\Logic;
use app\common\enum\GoodsEnum;
use app\common\enum\OrderEnum;
use app\common\enum\JcaiEnum;
use app\common\model\dev\DevRegion;
use app\common\model\goods\Goods;
use app\common\model\goods\GoodsItem;
use app\common\model\order\Order;
use app\common\model\order\OrderGoods;
use app\common\model\order\OrderTrade;
use app\common\model\shop\Shop;
use app\common\model\jcai\JcaiActivity;
use app\common\model\jcai\JcaiFound;
use app\common\model\goods\GoodsImage;
use app\common\model\jcai\JcaiJoin;
use app\common\model\user\User;
use Exception;
use think\facade\Db;
use app\common\server\UrlServer;

class JcaiLogic extends Logic
{
    /**
     * @Notes: 获取集众筹活动
     * @Author: 张无忌
     * @param array $get
     * @return array|bool
     */
    public static function activity(array $get,$user_id=0)
    {
        try {
            $pageNo   = $get['page_no'] ?? 1;
            $pageSize = $get['page_size'] ??20;
            if(isset($get['is_start']) && $get['is_start']==1){
                //未开始的活动
                $where[] = ['T.activity_start_time', '>', time()];
            }else{
                //已开始的活动
                $where[] = ['T.activity_start_time', '<=', time()];
            }

            if($get['first_cate_id']){
                $where[] = ['G.first_cate_id', '=', $get['first_cate_id']];
            }
            $model = new JcaiActivity();
            $lists = $model::with(['goods_image'])->alias('T')->field([
                'T.id,T.goods_id,T.intro,T.people_num,T.total_num,T.team_max_price,T.team_min_price,sales_volume',
                'G.name,G.image,G.max_price,G.min_price,G.market_price,T.activity_end_time,T.activity_start_time'
            ])
                ->where([
                    ['T.audit', '=', 1],
                    ['T.status', '>', 0],
                    ['T.del', '=', 0],
                    ['T.activity_end_time', '>=', time()],
                    ['S.is_freeze', '=', 0],
                    ['S.is_run', '=', 1],
                ])
                ->where($where)
                ->join('goods G', 'G.id = T.goods_id')
                ->join('shop S', 'S.id = T.shop_id')
                ->paginate([
                    'page'      => $pageNo,
                    'list_rows' => $pageSize,
                    'var_page'  => 'page'
                ])->toArray();
            foreach ($lists['data'] as $k => &$v) {
                foreach ($v['goods_image'] as $kk => &$vv) {
                    $vv['uri'] = UrlServer::getFileUrl($vv['uri']);
                }
                $v['pre_nums']=Db::name('jcai_precontract')->where(['jcai_id'=>$v['id']])->count();
                $v['yu_status']=Db::name('jcai_precontract')->where(['jcai_id'=>$v['id'],'user_id'=>$user_id])->count();
                $goods_order_nums=Db::name('order_goods')->alias('OG')
                    ->leftJoin('order O', 'O.id = OG.order_id')
                    ->where([
                        ['OG.create_time', '<=', $v['activity_end_time']],
                        ['O.pay_status', '=', 1],
                        ['O.order_type', '=', OrderEnum::JCAIZC_ORDER],
                        ['OG.goods_id', '=', $v['goods_id']],
                        ])->sum('OG.total_pay_price');
                 //进度百分比 订单总额:$goods_order_nums   目标金额是$v['total_num']
                $v['join_precontract']=0;
                //获取关注状态
                if(isset($get['is_start']) && $get['is_start']==0){
                    $v['join_precontract']= Db::name('jcai_precontract')->where(['user_id'=>$user_id,'jcai_id'=>$v['id']])->value('id');
                }
                //已经募集资金
                if(empty((int)$v['total_num'])){
                    $v['sales_volume'] = 0;
                }else{
                    $v['sales_volume'] =!empty($goods_order_nums)?round(($goods_order_nums/$v['total_num'])*100,0):0;
                }

                 //支持人数
                $v['total_num'] =$goods_order_nums;
                $v['people_num']=Db::name('order_goods')->alias('OG')
                    ->leftJoin('order O', 'O.id = OG.order_id')
                    ->where([
                        ['OG.create_time', '<=', $v['activity_end_time']],
                        ['O.pay_status', '=', 1],
                        ['O.order_type', '=', OrderEnum::JCAIZC_ORDER],
                        ['OG.goods_id', '=', $v['goods_id']],
                    ])->group('O.user_id')
                    ->count();
                //剩余天数
                $v['surplus_day'] = ceil(($v['activity_end_time'] - time()) / 86400);

            }
            $all_lists=$lists['data'];
            // 把$lists['data']中的第一个数组单独分出来并重置索引
            $firstItem = array_shift($lists['data']);
            // $lists['data']已经被重新排序，因为array_shift会移除第一个元素并重置索引
            return [
                'first_item'  => $firstItem,
                'all'         => $all_lists,
                'list'        => $lists['data'],
                'count'       => $lists['total'],
                'more'        => is_more($lists['total'], $pageNo, $pageSize),
                'page_no'     => $pageNo,
                'page_size'   => $pageSize
            ];
        } catch (Exception $e) {
            static::$error = $e->getMessage();
            return false;
        }
    }

    /**
     * @Notes: 集众筹开始信息
     * @Author: 张无忌
     * @param $post
     * @param $user_id
     * @return bool|array
     */
    public static function kaituanInfo($post, $user_id)
    {
        try {
            $model = new JcaiActivity();
            $lists = $model::with(['goods_image'])->alias('T')->field([
                'T.id,T.id as jcai_id,T.goods_id,T.people_num,T.intro,T.total_num,T.team_max_price,T.team_min_price,sales_volume',
                'G.name,G.image,G.max_price,G.min_price,G.market_price,G.content,T.activity_end_time,T.activity_start_time,T.buy_num'
            ])
                ->where([
                    ['T.audit', '=', 1],
                    ['T.status', '=', 1],
                    ['T.del', '=', 0],
                    ['T.activity_end_time', '>=', time()],
                    ['S.is_freeze', '=', 0],
                    ['S.is_run', '=', 1],
                    ['G.id', '=', $post['goods_id']],
                ])
                ->join('goods G', 'G.id = T.goods_id')
                ->join('shop S', 'S.id = T.shop_id')
                ->paginate([
                    'page'      => 1,
                    'list_rows' => 100,
                    'var_page'  => 'page'
                ])->toArray();

            foreach ($lists['data'] as $k => &$v) {
                foreach ($v['goods_image'] as $kk => &$vv) {
                    $vv['uri'] = UrlServer::getFileUrl($vv['uri']);
                }
                //价格只显示整数
                $v['team_min_price']= intval($v['team_min_price']);
             
                $goods_order_nums=Db::name('order_goods')->alias('OG')
                    ->leftJoin('order O', 'O.id = OG.order_id')
                    ->where([
                        ['OG.create_time', '<=', $v['activity_end_time']],
                        ['O.pay_status', '=', 1],
                        ['O.order_type', '=', OrderEnum::JCAIZC_ORDER],
                        ['OG.goods_id', '=', $v['goods_id']],
                    ])->sum('OG.total_pay_price');
                //进度百分比 订单总额:$goods_order_nums   目标金额是$v['total_num']
                $v['join_precontract']=0;
                //获取关注状态
                if(isset($get['is_start']) && $get['is_start']==0){
                    $v['join_precontract']= Db::name('jcai_precontract')->where(['user_id'=>$user_id,'jcai_id'=>$v['id']])->value('id');
                }
                //已经募集资金
                $v['status_yu']=0;
                if($v['activity_start_time']<time()){
                    $v['status_yu'] = 1;
                }

                $v['yu_status']=Db::name('jcai_precontract')->where(['jcai_id'=>$v['id'],'user_id'=>$user_id])->count();

//                $v['sales_volume'] =!empty($goods_order_nums)?round(($goods_order_nums/$v['total_num'])*100,0):0;
                if(empty((int)$v['total_num'])){
                    $v['sales_volume'] = 0;
                }else{
                    $v['sales_volume'] =!empty($goods_order_nums)?round(($goods_order_nums/$v['total_num'])*100,0):0;
                }
                //预约倒计时
                if($v['activity_start_time']>time()){
                    $v['surplus_time'] = ceil(($v['activity_start_time'] - time()));
                }
                $v['start_time']=date('Y-m-d H:i:s',$v['activity_start_time']);
                //支持人数
                $v['total_num'] =$goods_order_nums;
                $v['people_num']=Db::name('order_goods')->alias('OG')
                    ->leftJoin('order O', 'O.id = OG.order_id')
                    ->where([
                        ['OG.create_time', '<=', $v['activity_end_time']],
                        ['O.pay_status', '=', 1],
                        ['O.order_type', '=', OrderEnum::JCAIZC_ORDER],
                        ['OG.goods_id', '=', $v['goods_id']],
                    ])->group('O.user_id')
                    ->count();
                //剩余天数
                $v['surplus_day'] = ceil(($v['activity_end_time'] - time()) / 86400);

            }
            $goods_info= GoodsLogic::getGoodsDetail($post['goods_id'], $user_id);
            foreach($goods_info['goods_item'] as $kk => &$vv) {
                $vv['price']=Db::name('jcai_goods')->where(['goods_id'=>$post['goods_id'],'item_id'=>$vv['id']])->value('jcai_price');
                $vv['market_price']= intval($vv['market_price']);
            }
            $lists['data'][0]['goods_info']=$goods_info;
            return $lists['data'][0];
        } catch (Exception $e) {
            static::$error = $e->getMessage();
            return false;
        }
    }

    /**
     * @Notes: 发起集众筹开始/参加集众筹
     * @Author: 张无忌
     * @param $info
     * @param $user_id
     * @return bool|array
     */
    public static function kaituan($info, $userInfo,$post=[])
    {
        Db::startTrans();
        try {
            $time = time();
            $teamGoods =$post;

            // 参加集众筹验证
            if ($info['jcai_id']) {
                $JcaiFound = (new JcaiActivity())->where(['id'=>$info['jcai_id']])->findOrEmpty()->toArray();
                if (!$JcaiFound) throw new \think\Exception('选择的众筹不存在');
                if ($JcaiFound['status'] != 1) throw new \think\Exception('当前集众筹已结束，请重新选择集众筹');
                if ($JcaiFound['activity_end_time'] <= time()) throw new \think\Exception('当前集众筹已结束，请重新选择集众筹');
                // 获取已参加集众筹记录
//                $people = (new JcaiJoin())->where(['jcai_id'=>$info['jcai_id'], 'user_id'=>$userInfo['id']])->findOrEmpty()->toArray();
//                if ($people) throw new \think\Exception('您已参与过众筹了,不能重复参加众筹哦！');
            }
            $info['address']=Db::name('user_address')->where(['id'=>$post['address_id']])->find();
            //配送方式 转换
            $post['delivery_type'] = OrderEnum::getChangeDeliveryTypeItem($post['delivery_type']);

            // 验证收货地址
            if ($post['delivery_type'] == OrderEnum::DELIVERY_TYPE_EXPRESS && empty($info['address'])) {
                throw new \think\Exception('请选择收货地址');
            }

            // 校验发票信息 返回以店铺id为键,原发票参数为值的数组
            $invoice = OrderInvoiceLogic::checkOrderInvoice($info, 'team');
            if (false === $invoice) {
                throw new \think\Exception(OrderInvoiceLogic::getError());
            }

            //线下自提-提货码
            if ($post['delivery_type'] == OrderEnum::DELIVERY_TYPE_SELF) {
                $pickup_code = create_rand_number('order', 'pickup_code', 6);
            }

            //金额核算
            $goods_jzc=Db::name('jcai_goods')->where(['item_id'=>$teamGoods['item_id'],'goods_id'=>$post['goods_id']])->find();
            if(!$goods_jzc) throw new \think\Exception('所选商品不存在');
            $jzc_price=$goods_jzc['jcai_price'];
            $jzc_total=$jzc_price*$teamGoods['count'];
            if($jzc_total!=$post['total_amount']) throw new \think\Exception('请重新核算金额后提交!');


            // 创建交易单
            $trade = OrderTrade::create([
                't_sn'            => createSn('order_trade', 't_sn'),
                'user_id'         => $userInfo['id'],
                'goods_price'     => $post['total_amount'],
                'order_amount'    => $post['total_amount'],
                'total_amount'    => $post['total_amount'],
                'shop_id'         => $post['shop_id'],
                'create_time'     => $time
            ]);

            // 创建订单
            $order = Order::create([
                'trade_id'       => $trade['id'],
                'shop_id'        => $post['shop_id'],
                'order_sn'       => createSn('order', 'order_sn'),
                'user_id'        => $userInfo['id'],
                'order_type'     => OrderEnum::JCAIZC_ORDER,
                'order_source'     => $userInfo['client'],
                'delivery_type'  => $post['delivery_type'],
                'pay_way'        => $post['pay_way'],
                'consignee'      => $info['address']['contact'] ?? '',
                'province'       => $info['address']['province_id'] ?? 0,
                'city'           => $info['address']['city_id'] ?? 0,
                'district'       => $info['address']['district_id'] ?? 0,
                'address'        => $info['address']['address'] ?? '',
                'mobile'         => $info['address']['telephone'] ?? '',
                'goods_price'    => $post['total_amount'],
                'total_amount'   => $post['total_amount'],
                'order_amount'   => $post['total_amount'],
                'total_num'      => $post['count'],
                'shipping_price' => $post['shipping_price']??0,
                'discount_amount' => 0,
                'user_remark'    => $post['remark'] ?? '',
                'create_time'    => $time,
                'pickup_code'    => $pickup_code ?? null
            ]);

            // 创建订单商品
            OrderGoods::create([
                'order_id'        => $order['id'],
                'goods_id'        => $teamGoods['goods_id'],
                'item_id'         => $teamGoods['item_id'],
                'goods_num'       => $teamGoods['count'],
                'goods_name'      => $teamGoods['goods_name'],
                'goods_price'     => $teamGoods['goods_price'],
                'total_price'     => bcadd($teamGoods['goods_price'] * $teamGoods['count'], 0, 2),
                'total_pay_price' => bcadd($teamGoods['goods_price'] * $teamGoods['count'], 0, 2),
                'discount_price'  => 0,
                'spec_value'      => $teamGoods['spec_value'],
                'spec_value_ids'  => $teamGoods['spec_value_ids'],
                'image'           => $teamGoods['image'],
                'shop_id'         => $teamGoods['shop_id']
            ]);

            // 增加发票
//            OrderInvoiceLogic::insertOrderInvoice($post['shop_id'], $userInfo['id'], $order['id'], $invoice);

            // 开新众筹
            $jcai_id = 0;
            if (!$info['jcai_id']) {
                $JcaiFound = JcaiFound::create([
                    'shop_id'          => $post['shop_id'],
                    'jcai_activity_id' => $info['jcai_id'],
                    'team_sn'          => createSn('team_found', 'team_sn'),
                    'user_id'          => $userInfo['id'],
                    'status'           => 0,
                    'join'             => 0,
                    'people'           => 1,
                    'goods_snap'       => json_encode([
                        'id'      => $teamGoods['goods_id'],
                        'shop_id' => $teamGoods['shop_id'],
                        'name'    => $teamGoods['name'],
                        'image'   => $teamGoods['image']
                    ]),
                    'kaituan_time'     => $time,
                    'invalid_time'     => 0
                ]);
                $jcai_id = $JcaiFound['id'];
            }

            // 加入集众筹
            JcaiJoin::create([
                'shop_id'          => $post['shop_id'],
                'jcai_activity_id' => $info['jcai_id'],
                'jcai_id'          => $jcai_id ?: $info['jcai_id'],
                'sn'               => createSn('jcai_join', 'sn'),
                'user_id'          => $userInfo['id'],
                'order_id'         => $order['id'],
                'identity'         => $info['jcai_id'] ? 2 : 1,
                'team_snap'        => json_encode($post, JSON_UNESCAPED_UNICODE),
                'create_time'      => $time,
                'update_time'      => $time
            ]);

            // 扣减库存
            (new GoodsItem())->where([
                'goods_id'=>$teamGoods['goods_id'],
                'id'      =>$teamGoods['item_id']
            ])->update(['stock' => ['dec', $teamGoods['count']]]);

            (new Goods())->where([
                'id'=>$teamGoods['goods_id']
            ])->update(['stock' => ['dec', $teamGoods['count']]]);

            // 更新参数人数
            JcaiFound::update([
                'join' => ['inc', 1]
            ], ['id'=>$jcai_id ?: $info['jcai_id']]);

            // 更新活动集众筹数
            JcaiActivity::update([
                'sales_volume' => ['inc', 1]
            ], ['id'=>$info['jcai_id']]);

            Db::commit();
            return [
                'jcai_id'  => $jcai_id ?: $info['jcai_id'],
                'type'     => 'trade',
                'trade_id' => $trade['id'],
                'order_id' => $order['id']
            ];
        } catch (Exception $e) {
            Db::rollback();
            static::$error = $e->getMessage();
            return false;
        }
    }

    /**
     * @Notes: 获取集众筹记录
     * @Author: 张无忌
     * @param $get
     * @param $user_id
     * @return array|bool
     */
    public static function record($get, $user_id)
    {
        try {
            $pageNo   = $get['page_no'] ?? 1;
            $pageSize = $get['page_size'] ?? 20;

            $where = [];
            if (isset($get['type']) and $get['type'] >= 0) {
                $type = intval($get['type']);
                $where[] = ['TJ.status', '=', $type];
            }

            $model = new JcaiJoin();
            $lists = $model->alias('TJ')->field(['TJ.*,S.name as shop_name,O.order_amount'])
                ->where(['TJ.user_id'=>$user_id])
                ->order('id desc')
                ->where($where)
                ->join('Shop S', 'S.id = TJ.shop_id')
                ->join('order O', 'O.id = TJ.order_id')
                ->paginate([
                    'page'      => $pageNo,
                    'list_rows' => $pageSize,
                    'var_page'  => 'page'
                ])->toArray();

            $data = [];
            foreach ($lists['data'] as &$item) {
                $item['team_snap'] = json_decode($item['team_snap'], true);
                $data[] = [
                    'id' => $item['id'],
                    'order_id'   => $item['order_id'],
                    'shop_name'  => $item['shop_name'],
                    'people_num' => $item['team_snap']['people_num'],
                    'name'       => $item['team_snap']['name'],
                    'image'      => $item['team_snap']['image'],
                    'price'      => $item['team_snap']['price'],
                    'count'      => $item['team_snap']['count'],
                    'spec_value_str' => $item['team_snap']['spec_value_str'],
                    'order_amount'   => $item['order_amount'],
                    'status'         => $item['status'],
                    'identity'       => $item['identity'],
                    'identity_text'  => $item['identity'] == 1 ? '团长' : '团员',
                    'status_text'    => JcaiEnum::getStatusDesc($item['status'])
                ];
            }

            return [
                'list'      => $data,
                'count'     => $lists['total'],
                'more'      => is_more($lists['total'], $pageNo, $pageSize),
                'page_no'   => $pageNo,
                'page_size' => $pageSize
            ];

        } catch (Exception $e) {
            static::$error = $e->getMessage();
            return false;
        }
    }

    /**
     * @Notes: 验证集众筹
     * @Author: 张无忌
     * @param $post
     * @param $user_id
     * @return bool
     */
    public static function check($post, $user_id)
    {
        try {
            $JcaiActivity = (new JcaiActivity())->alias('T')
                ->field([
                    'T.id as jcai_activity_id,T.shop_id,T.effective_time,GI.goods_id,GI.id as item_id,T.people_num,TG.team_price',
                    'G.name,G.image,GI.spec_value_str,GI.spec_value_ids,GI.market_price',
                    'GI.price,GI.stock'
                ])->where([
                    ['T.goods_id', '=', (int)$post['goods_id']],
                    ['T.audit', '=', 1],
                    ['T.status', '=', 1],
                    ['T.del', '=', 0],
                    ['T.activity_start_time', '<=', time()],
                    ['T.activity_end_time', '>=', time()],
                    ['TG.goods_id', '=', (int)$post['goods_id']],
                    ['TG.item_id', '=', (int)$post['item_id']],
                ])->join('team_goods TG', 'TG.jcai_id = T.id')
                ->join('goods G', 'G.id = TG.goods_id')
                ->join('goods_item GI', 'GI.id = TG.item_id')
                ->findOrEmpty()->toArray();

            if (!$JcaiActivity) throw new \think\Exception('当前商品未参与集众筹活动，下次再来吧');
            if ($JcaiActivity['stock'] - intval($post['count']) < 0) throw new \think\Exception('抱歉,库存不足');

            // 参加集众筹验证
            if (!empty($post['jcai_id']) and $post['jcai_id']) {
                $JcaiFound = (new JcaiFound())->where(['id'=>$post['jcai_id']])->findOrEmpty()->toArray();
                if (!$JcaiFound) throw new \think\Exception('选择的众筹不存在');
                if ($JcaiFound['status'] != 0) throw new \think\Exception('当前集众筹已结束，请重新选择集众筹');
                if ($JcaiFound['invalid_time'] <= time()) throw new \think\Exception('当前集众筹已结束，请重新选择集众筹');
                if ($JcaiFound['user_id'] == $user_id) throw new \think\Exception('您已参加过该众筹了,不能重复参加集众筹哦！');
                if ($JcaiFound['people'] == $JcaiFound['join']) throw new \think\Exception('当前集众筹已满员，请重新选择集众筹！');

                // 获取已参加集众筹记录
                $people = (new JcaiJoin())->where(['jcai_id'=>$post['jcai_id'], 'user_id'=>$user_id])->findOrEmpty()->toArray();
                if ($people) throw new \think\Exception('您已参加过该众筹了,不能重复参加集众筹哦！');
            }


            return true;
        } catch (Exception $e) {
            static::$error = $e->getMessage();
            return false;
        }
    }
    public static function getPdJcaiGoods($get){

    }


    /*
     * 添加集采众筹预约记录
     */
    public static function addJcaiJoin($post){
        $post['user_id'] = $post['user_id'];
        $post['jcai_id'] = $post['jcai_id'];
        //判断是否已经预约
        $jcaiJoin = Db::name('jcai_precontract')->where(['user_id'=>$post['user_id'],'jcai_id'=>$post['jcai_id']])->find();
        if($jcaiJoin){
            return false;
        }
        $post['create_time'] = time();
        $post['update_time'] = time();
        Db::name('jcai_precontract')->insert($post);
        return true;

    }




}