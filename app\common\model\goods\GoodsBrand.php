<?php



namespace app\common\model\goods;


use app\common\basics\Models;

/**
 * 商品品牌
 * Class GoodsBrand
 * @package app\common\model
 */
class GoodsBrand extends Models
{
    /**
     * Notes: 获取以id为键的数据
     * <AUTHOR> 17:35)
     * @return array
     */
    public static function getNameColumn()
    {
        $lists = self::where([
            'del' => 0,
            'is_show' => 1
        ])->column('id,name', 'id');

        return empty($lists) ? [] : $lists;
    }
}