<?php

namespace app\admin\controller\decoration;

use app\admin\logic\decoration\AdPositionLogic;
use app\admin\validate\decoration\AdPositionValidate;
use app\common\basics\AdminBase;
use app\common\server\JsonServer;
use think\facade\View;
use app\common\model\AdOrder as AdOrderModel;
use app\common\model\AdPosition as AdPositionModel;
use app\common\model\user\User as UserModel;

class AdPosition extends AdminBase
{

    public function __construct(\think\App $app)
    {
        parent::__construct($app);
    }



    /**
     * Notes:获取广告位列表
     * @return \think\response\Json|\think\response\View
     * @author: cjhao 2021/4/17 14:26
     */
    public function lists(){
        if($this->request->isAjax()){
            $get = $this->request->get();
            $get['is_sys']=0;
            $list = AdPositionLogic::lists($get);
            return JsonServer::success('',$list);
        }
        return view();
    }

    /**
     * Notes:添加广告位
     * @return \think\response\Json
     * @author: cjhao 2021/4/17 14:49
     */
    public function add(){
        if($this->request->isAjax()){
            $post = $this->request->post();
            $post['del'] = 0;
            (new AdPositionValidate())->goCheck('Add',$post);
            $result = AdPositionLogic::add($post);
            if($result){
                return JsonServer::success('添加成功');
            }
            return JsonServer::error('添加失败');
        }
        return view();
    }

    /**
     * Notes:编辑广告位
     * @return \think\response\Json
     * @author: cjhao 2021/4/17 14:52
     */
    public function edit(){
        if($this->request->isAjax()){
            $post = $this->request->post();
            $post['del'] = 0;
            (new AdPositionValidate())->goCheck('Add',$post);
            AdPositionLogic::edit($post);
            return JsonServer::success('修改成功');
        }
        $id = $this->request->get('id');
        $detail = AdPositionLogic::getPosition($id);
        View::assign([
            'detail'        => $detail,
        ]);
        return view('',['detail'=>$detail]);
    }

    /**
     * Notes:删除广告位
     * @return \think\response\Json
     * @author: cjhao 2021/4/19 11:21
     */
    public function del(){
        (new AdPositionValidate())->goCheck('del');
        $id = $this->request->post('id');
        AdPositionLogic::del($id);
        return JsonServer::success('删除成功');
    }

    public function swtichStatus(){
        (new AdPositionValidate())->goCheck('swtich');
        $post = $this->request->post();
        AdPositionLogic::swtichStatus($post);
        return JsonServer::success('操作成功');
    }

    /**
     * Notes: 获取配置列表
     * @return \think\response\Json|\think\response\View
     * @author: cjhao 2021/4/17 14:26
     */
    public function lists2(){
        if($this->request->isAjax()){
            $get = $this->request->get();
            $get['is_sys']=1;
            $list = AdPositionLogic::lists($get);
            return JsonServer::success('',$list);
        }
        return view();
    }

    /**
     * Notes:添加广告位
     * @return \think\response\Json
     * @author: cjhao 2021/4/17 14:49
     */
    public function add2(){
        if($this->request->isAjax()){
            $post = $this->request->post();
            $post['del'] = 0;
            $post['is_sys'] = 1;
            (new AdPositionValidate())->goCheck('Add',$post);
            $result = AdPositionLogic::add($post);
          
            if($result){
                return JsonServer::success('添加成功');
            }
            return JsonServer::error('添加失败');
        }
        return view();
    }

    /**
     * Notes:编辑广告位
     * @return \think\response\Json
     * @author: cjhao 2021/4/17 14:52
     */
    public function edit2(){
        if($this->request->isAjax()){
            $post = $this->request->post();
            $post['del'] = 0;
            (new AdPositionValidate())->goCheck('Add',$post);
            AdPositionLogic::edit($post);
            return JsonServer::success('修改成功');
        }
        $id = $this->request->get('id');
        $detail = AdPositionLogic::getPosition($id);
        return view('',['detail'=>$detail]);
    }

    /**
     * Notes:删除广告位
     * @return \think\response\Json
     * @author: cjhao 2021/4/19 11:21
     */
    public function del2(){
        (new AdPositionValidate())->goCheck('del');
        $id = $this->request->post('id');
        AdPositionLogic::del($id);
        return JsonServer::success('删除成功');
    }

    public function swtichStatus2(){
        (new AdPositionValidate())->goCheck('swtich');
        $post = $this->request->post();
        AdPositionLogic::swtichStatus($post);
        return JsonServer::success('操作成功');
    }

    /**
     * Notes:获取广告位购买列表
     * @return \think\response\Json|\think\response\View
     * @author: cjhao 2021/4/17 14:26
     */
    public function buylists2(){
        if($this->request->isAjax()){
            $get = $this->request->get();
            $list = AdPositionLogic::lists($get);
            return JsonServer::success('',$list);
        }
        return view();
    }

    /**
     * @notes 广告位购买记录列表
     * @return \think\response\Json|string
     * @throws \think\db\exception\DbException
     * <AUTHOR> Team
     * @date 2024/07/27
     */
    public function buyLists()
    {
        $orderModel = new AdOrderModel();

        if ($this->request->isAjax()) {
            $params = $this->request->get('', '', 'trim');
            $limit = $this->request->get('limit/d', 15);

            $query = $orderModel->alias('ao')
                ->join('user u', 'u.id = ao.user_id', 'LEFT')
                ->join('ad_position ap', 'ap.id = ao.ad_position_id', 'LEFT')
                // 确保查询了 ao.buy_time 和 ap.billing_cycle
                ->field('ao.*, u.nickname as user_nickname, ap.name as position_name, ap.billing_cycle')
                ->where('ao.del', '=', 0);

            if (!empty($params['order_sn'])) {
                $query->where('ao.order_sn', 'like', '%' . $params['order_sn'] . '%');
            }
            if (!empty($params['position_name'])) {
                $query->where('ap.name', 'like', '%' . $params['position_name'] . '%');
            }
            if (!empty($params['user_nickname'])) {
                $query->where('u.nickname', 'like', '%' . $params['user_nickname'] . '%');
            }
            if (isset($params['pay_status']) && $params['pay_status'] !== '') {
                $query->where('ao.pay_status', '=', $params['pay_status']);
            }
            if (isset($params['order_status']) && $params['order_status'] !== '') {
                $query->where('ao.order_status', '=', $params['order_status']);
            }
            if (isset($params['pay_way']) && $params['pay_way'] !== '') {
                $query->where('ao.pay_way', '=', $params['pay_way']);
            }
            if (!empty($params['create_time'])) {
                $dateRange = explode(' - ', $params['create_time']);
                if (count($dateRange) == 2) {
                   $query->whereTime('ao.create_time', 'between', [$dateRange[0], $dateRange[1] . ' 23:59:59']);
                }
            }

            // --- 修改开始: 克隆查询对象用于 count ---
            $countQuery = clone $query;
            $count = $countQuery->count();

            $currentPage = $this->request->get('page/d', 1);
            $limit = $this->request->get('limit/d', 15); // 获取 limit
            $lists = $query->page($currentPage, $limit)
                ->order('ao.create_time', 'desc')
                ->select();

            $payStatusDesc = [0 => '待支付', 1 => '已支付'];
            $orderStatusDesc = [0 => '待处理', 1 => '已完成', 2 => '已取消']; // 注意：ad_order 表似乎没有 order_status，这里可能基于 status
            $payWayDesc = [1 => '微信支付', 2 => '支付宝支付', 3 => '余额支付', 4 => '线下支付'];
            // --- 修改开始: 添加计费周期描述 ---
            $billingCycleDesc = [0 => '天', 1 => '周', 2 => '月', 3 => '年'];
            // --- 修改结束 ---

            // --- 修改开始: 定义各单位对应的秒数 (月/年为近似值) ---
            $secondsPerUnit = [
                0 => 86400,         // 秒/天
                1 => 604800,        // 秒/周 (7 * 86400)
                2 => 2592000,       // 秒/月 (30 * 86400) - 近似值
                3 => 31536000       // 秒/年 (365 * 86400) - 近似值
            ];


            foreach ( $lists as &$item) {
                // 确保数值字段有效
                $item['pay_way'] = isset($item['pay_way']) ? (int)$item['pay_way'] : 1;
                $item['status'] = isset($item['status']) ? (int)$item['status'] : 0;
                // $item['order_status'] = isset($item['status']) ? (int)$item['status'] : 0; // ad_order 表似乎没有 order_status
                $item['ad_price'] = isset($item['ad_price']) ? (float)$item['ad_price'] : 0;
                $item['buy_time_seconds'] = isset($item['buy_time']) ? (int)$item['buy_time'] : 0; // 获取秒数
                $item['billing_cycle'] = isset($item['billing_cycle']) ? (int)$item['billing_cycle'] : 0; // 获取计费单位类型

                // 状态描述
                $item['pay_status_desc'] = $payStatusDesc[$item['status']] ?? '未知';
                // $item['order_status_desc'] = $orderStatusDesc[$item['order_status']] ?? '未知'; // 同上
                $item['pay_way_desc'] = $payWayDesc[$item['pay_way']] ?? '未知';

                // --- 修改开始: 处理购买时长和投放时间 ---
                // 计算购买时长描述
                $cycleText = $billingCycleDesc[$item['billing_cycle']] ?? '?';
                $item['duration_desc'] = $item['buy_time'] > 0 ? $item['buy_time'] . ' ' . $cycleText : '-';

                // 格式化投放时间
                $startTimeStr = '-';
                $endTimeStr = '-';
                if (isset($item['start_time']) && is_numeric($item['start_time']) && $item['start_time'] > 0) {
                    $startTimeStr = date('Y-m-d', (int)$item['start_time']); // 只显示日期
                }
                if (isset($item['end_time']) && is_numeric($item['end_time']) && $item['end_time'] > 0) {
                    $endTimeStr = date('Y-m-d', (int)$item['end_time']); // 只显示日期
                }
                $item['validity_period'] = $startTimeStr . ' 至 ' . $endTimeStr;

                // 移除不再需要的时间字段的默认格式化 (如果前端不再直接使用它们)
                // unset($item['pay_time']); // ad_order 表没有 pay_time
                // unset($item['create_time']); // 如果前端不再显示原始创建时间

                // 安全处理 create_time (如果仍需在其他地方使用)
                 // --- 修改开始: 使用 secondsPerUnit 计算购买时长描述 ---
                 $duration_desc = '-';
                 if ($item['buy_time_seconds'] > 0 && isset($billingCycleDesc[$item['billing_cycle']])) {
                     $unitText = $billingCycleDesc[$item['billing_cycle']];
                     $secondsInChosenUnit = $secondsPerUnit[$item['billing_cycle']] ?? 1; // 获取对应单位的秒数
                     
                     if ($secondsInChosenUnit > 0) {
                         // 计算购买了多少个单位 (四舍五入)
                         $numberOfUnits = round($item['buy_time_seconds'] / $secondsInChosenUnit);
                         
                         if ($numberOfUnits >= 1) {
                             $duration_desc = $numberOfUnits . ' ' . $unitText;
                         } else {
                             // 如果计算出的单位数小于1，尝试用天数显示
                             $days = round($item['buy_time_seconds'] / $secondsPerUnit[0]);
                             if ($days > 0) {
                                 $duration_desc = $days . ' ' . $billingCycleDesc[0]; // 显示天数
                             } else {
                                 $duration_desc = '< 1 天'; // 不足一天
                             }
                         }
                     }
                 }
                 $item['duration_desc'] = $duration_desc; // 将计算结果赋值给 duration_desc
                 // --- 修改结束 ---


                // --- 修改结束 ---

                // 格式化金额
                $item['ad_price'] = '￥' . number_format($item['ad_price'], 2);
            }
            // --- 修改开始: 确保返回的数据结构正确 ---
            $data=['count'=>$count,'lists'=>$lists->toArray()]; // 使用 toArray() 转换集合
            // --- 修改结束 ---
            return JsonServer::success('', $data,1);
        }

        View::assign([
            'payStatus' => [0 => '待支付', 1 => '已支付'],
            'orderStatus' => [0 => '待处理', 1 => '已完成', 2 => '已取消'], // 同上，可能需要调整
            'payWay' => [1 => '微信支付', 2 => '支付宝支付', 3 => '余额支付', 4 => '线下支付'],
        ]);
        return View::fetch();
    }
    
}
