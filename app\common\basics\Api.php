<?php


namespace app\common\basics;


use think\App;
use think\facade\Db;

/**
 * API接口基类
 * Class Api
 * <AUTHOR>
 * @package app\common\basics
 */
abstract class Api
{
    /**
     * Request实例
     */
    protected $request;

    /**
     * 应用实例
     */
    protected $app;

    /**
     * 用户ID
     * @var int
     */
    protected $user_id = null;

    /**
     * 用户信息
     * @var array
     */
    public $user_info = [];

    /**
     * 客户端
     * @var null
     */
    public $client = null;

    /**
     * 页码
     * @var int
     */
    public $page_no = 1;

    /**
     * 每页显示条数
     * @var int
     */
    public $page_size = 15;

    /**
     * 无需登录即可访问的方法
     * @var array
     */
    public $like_not_need_login = [];

    /**
     * 构造方法
     * @access public
     * @param  App  $app  应用对象
     */
    public function __construct(App $app)
    {
        $this->app     = app();
        $this->request = request();


        // 控制器初始化
        $this->initialize();
    }

    /**
     * 初始化
     */
    protected function initialize()
    {
        //用户信息
        $this->user_info = $this->request->user_info ?? [];
        if(boolval($this->user_info)) {
            $this->user_id = $this->user_info['id'] ?? null;
            $this->client = $this->user_info['client'] ?? null;
        }
        //分页参数
        $page_no = (int)$this->request->get('page_no');
        $this->page_no = $page_no && is_numeric($page_no) ? $page_no : $this->page_no;
        $page_size = (int)$this->request->get('page_size');
        $this->page_size = $page_size && is_numeric($page_size) ? $page_size : $this->page_size;
        $this->page_size = min($this->page_size, 100);



    }
}