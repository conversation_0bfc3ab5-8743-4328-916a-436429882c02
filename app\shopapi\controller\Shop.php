<?php

namespace app\shopapi\controller;

use app\shopapi\logic\ShopDepositLogic;
use app\admin\validate\FreightValidate;
use app\common\basics\ShopApi;
use app\common\enum\ClientEnum;
use app\common\logic\ExpressLogic;
use app\common\logic\FreightLogic;
use app\common\model\DevRegion;
use app\common\model\Freight as FreightModel;
use app\common\model\shop\ShopAdmin;
use app\common\model\shop\ShopMerchantfees;
use app\common\server\ConfigServer;
use app\common\server\JsonServer;
use app\shop\logic\AdminLogic;
use app\shop\logic\user\UserLogic;
use app\shop\validate\AdminValidate;
use app\shop\validate\BankValidate;
use app\shopapi\{
    logic\ShopLogic,
    validate\ShopDataSetValidate,
    validate\ShopWithdrawValidate,
    validate\AdminPasswordValidate,
    validate\ShopAccountValidate
};
use think\exception\ValidateException;
use app\shop\logic\order\InvoiceLogic;
use app\shop\validate\order\OrderInvoiceValidate;

use app\common\model\shop\ShopRole;
use app\shop\logic\kefu\KefuLogic;
use app\shop\validate\kefu\KefuValidate;
use app\shop\validate\kefu\LoginValidate;
use app\api\logic\RechargeLogic;
use app\api\logic\AdLogic;
use app\api\logic\HelpLogic;
use think\facade\Db;

// Import AfterSale related classes
use app\shop\logic\after_sale\AfterSaleLogic;

// Import Pay related classes
use app\api\logic\PayLogic;
use app\common\enum\PayEnum;
use app\common\model\order\OrderTrade;
use app\common\model\RechargeOrder;
use app\common\model\AdOrder;
use app\common\model\shop\ShopDeposit;
use app\common\model\jcai\JcaiOrder;
use app\common\model\agent\AgentMerchantfees;
use app\common\model\integral\IntegralOrder;
use app\common\model\shop\Shop as ShopShop;
use app\common\model\order\Order;
use app\common\model\after_sale\AfterSale as AfterSaleModel;
use app\common\enum\OrderEnum;
use think\facade\Log;

// Import Role related classes
use app\shop\logic\RoleLogic;
use app\shop\validate\RoleValidate;

// Import new classes
use app\shopapi\validate\DepositRechargeValidate;
use app\shopapi\validate\DepositContractValidate;
use app\shop\logic\JcaiLogic;
use app\shop\logic\RefundAddressLogic;
use app\shop\logic\kefu\KefuLangLogic;
use app\shop\validate\kefu\KefuLangValidate;

// Import new classes
use app\shopapi\logic\ShopAdLogic;

use app\common\logic\SettingLogic;
use app\shop\logic\decoration\ShopAdLogic as DecorationShopAdLogic;

/**
 * 商家控制器
 * Class Shop
 * @package app\shopapi\controller
 */
class Shop extends ShopApi
{
    public $like_not_need_login = ['areaTree','protocolDetail'];
    /**
     * @notes 获取商家可提现余额
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2021/11/10 16:16
     */
    public function getShopInfo()
    {
        $shop = (new ShopLogic)->getShopInfo($this->shop_id);
        return JsonServer::success('', $shop);
    }

    /**
     * @notes 获取提现信息
     * @return \think\response\Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date 2021/11/10 16:30
     */
    public function getWithdrawInfo()
    {
        $result = (new ShopLogic)->getWithdrawInfo($this->shop_id);
        return JsonServer::success('', $result);
    }

    /**
     * @notes 提现操作
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2021/11/10 17:03
     */
    public function withdraw()
    {
        $post = $this->request->post();
        $post['shop_id'] = $this->shop_id;
        (new ShopWithdrawValidate())->goCheck('', $post);
        $result = (new ShopLogic)->withdraw($post);
        if (true === $result) {
            return JsonServer::success('提现成功');
        }
        return JsonServer::error($result);
    }

    /**
     * @notes 提现记录
     * @return \think\response\Json
     * @throws \think\db\exception\DbException
     * <AUTHOR>
     * @date 2021/11/10 17:11
     */
    public function withdrawLog()
    {
        $list = (new ShopLogic)->withdrawLog($this->shop_id, $this->page_no, $this->page_size);
        return JsonServer::success('', $list);
    }

    /**
     * @notes 添加账户
     * @return \think\response\Json
     * @throws \think\db\exception\DbException
     * <AUTHOR>
     * @date 2021/11/10 18:26
     */
    public function addBank()
    {
        (new BankValidate())->goCheck('add');
        $post = $this->request->post();
        $post['shop_id'] = $this->shop_id;
        (new ShopLogic)->addBank($post);
        return JsonServer::success('添加成功');
    }

    /**
     * @notes 获取银行卡
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2021/11/11 15:47
     */
    public function getBank()
    {
        (new BankValidate())->goCheck('id');
        $id = $this->request->get('id');
        $data = (new ShopLogic)->getBank($id, $this->shop_id);
        return JsonServer::success('', $data);
    }

    /**
     * @notes 编辑银行卡
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2021/11/10 18:40
     */
    public function editBank()
    {
        (new BankValidate())->goCheck('edit');
        $post = $this->request->post();
        $post['shop_id'] = $this->shop_id;
        (new ShopLogic)->editBank($post);
        return JsonServer::success('编辑成功');
    }

    /**
     * @notes 删除银行卡
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2021/11/10 18:42
     */
    public function delBank()
    {
        (new BankValidate())->goCheck('id');
        $id = $this->request->post('id');
        (new ShopLogic)->delBank($id, $this->shop_id);
        return JsonServer::success('删除成功');
    }

    /**
     * @notes 设置商家信息
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2021/11/11 10:40
     */
    public function shopSet()
    {

        $post = $this->request->post();
        (new ShopDataSetValidate())->goCheck('', $post);
        (new ShopLogic)->shopSet($post, $this->shop_id);
        return JsonServer::success('设置成功');
    }

    /**
     * @notes 获取商家待完善的信息项
     * @return \think\response\Json
     * <AUTHOR> AI
     * @date 2024/05/16
     */
    public function getIncompleteInfo()
    {
        $incompleteFields = (new ShopLogic)->checkProfileCompletion($this->shop_id);
        return JsonServer::success('获取成功', ['incomplete_fields' => $incompleteFields]);
    }

    /**
     * @notes 修改密码接口
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2021/11/11 16:11
     */
    public function changePassword()
    {
        $post = $this->request->post();
        $post['admin_id'] = $this->admin_id;
        (new AdminPasswordValidate())->goCheck('', $post);
        $res = (new ShopLogic)->updatePassword($post, $this->shop_id);
        if (true === $res) {
            return JsonServer::success('密码修改成功');
        }
        return JsonServer::error($res);
    }

    /**
     * User: 意象信息科技 mjf
     * Desc: 设置快递方式
     */
    public function set()
    {
        $post = $this->request->post();
        $post['type'] = isset($post['type']) && $post['type'] == 'on' ? 1 : 0;
        ConfigServer::set('express', 'is_express', $post['type']);
        return JsonServer::success('操作成功');
    }

    /**
     * User: 意象信息科技 mjf
     * Desc: 运费模板列表
     */
    public function lists()
    {
        $get = $this->request->get();
        $get['shop_id'] = $this->shop_id;
        $get['page'] = $get['page_no'] ?? 1;
        $get['limit'] =  $get['page_size'] ?? 15;
        $data['data'] = FreightLogic::lists($get);
        $data['charge_way_lists'] = FreightModel::getChargeWay(true);
        $data['config'] = ExpressLogic::getExpress();
        return JsonServer::success('获取成功', $data);
    }

    /**
     * User: 意象信息科技 mjf
     * Desc: 添加运费模板
     */
    public function add()
    {
        try {
            $post = $this->request->post();
            validate(FreightValidate::class)->scene('add')->check($post);
            $post['shop_id'] = $this->shop_id;
            FreightLogic::add($post);
            return JsonServer::success('添加成功');
        } catch (ValidateException $e) {
            return JsonServer::error($e->getMessage());
        }
    }

    /**
     * User: 意象信息科技 mjf
     * Desc: 删除运费模板
     */
    public function del()
    {
        try {
            $post = $this->request->post();
            validate(FreightValidate::class)->scene('del')->check($post);
            FreightLogic::del($post);
            return JsonServer::success('删除成功');
        } catch (ValidateException $e) {
            return JsonServer::error($e->getMessage());
        }
    }

    /**
     * User: 意象信息科技 mjf
     * Desc: 运费模板详情
     */
    public function detail()
    {
        $id = $this->request->get('id');
        $detail = FreightLogic::detail($id);
        return JsonServer::success('获取成功', $detail);
    }

    /**
     * User: 意象信息科技 mjf
     * Desc: 运费模板编辑
     */
    public function edit()
    {
        try {
            $post = $this->request->post();
            validate(FreightValidate::class)->scene('edit')->check($post);
            FreightLogic::edit($post);
            return JsonServer::success('编辑成功');
        } catch (ValidateException $e) {
            return JsonServer::error($e->getMessage());
        }
    }

    /**
     * 区域树状结构
     * @return \think\response\Json
     */
    public function areaTree()
    {
        $DevRegion = new DevRegion();
        $lists = $DevRegion
            ->field(['name', 'id', 'parent_id', 'level'])
            ->where('level', '>', 0)
            ->select()->toArray();
        $list = FreightLogic::areaSort($lists);
        return JsonServer::success('', $list);
    }

    /************************* 发票管理模块 *************************/

    /**
     * @notes 发票列表
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2022/4/12 17:34
     */
    public function Invoicelists()
    {
        $get = $this->request->get();
        return JsonServer::success('', InvoiceLogic::getInvoiceLists($get, $this->shop_id));
    }

    /**
     * @notes 开票
     * @return \think\response\Json|\think\response\View
     * <AUTHOR>
     * @date 2022/4/12 19:00
     */
    public function setInvoice()
    {
        $form = $this->request->get('form/d');
        if (isset($form) && $form == 1) {
            $id = $this->request->get('id/d');
            if (isset($id) && !empty($id)) {
                return JsonServer::success('', InvoiceLogic::detail($id));
            }
            return JsonServer::error('参数错误');
        } else {
            $params = (new OrderInvoiceValidate())->goCheck();
            InvoiceLogic::setInvoice($params);
            return JsonServer::success('操作成功');
        }
    }

    /**
     * @notes 导出Excel
     * @return \think\response\Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date 2022/4/24 10:20
     */
    public function export()
    {
        $params = $this->request->get();
        $result = InvoiceLogic::export($params, $this->shop_id);
        if (false === $result) {
            return JsonServer::error(InvoiceLogic::getError() ?: '导出失败');
        }
        return JsonServer::success('', $result);
    }

    /************************* 客服管理模块 *************************/

    /**
     * @notes 客服列表
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2021/11/26 18:40
     */
    public function kefulists()
    {
        $get = $this->request->get();
        $get['page'] = isset($get['page']) ? intval($get['page']) : 1;
        $get['limit'] = isset($get['limit']) ? intval($get['limit']) : 10;
        $lists = KefuLogic::getLists($get, $this->shop_id);
        return JsonServer::success('获取成功', $lists);
    }




    /**
     * @notes 添加客服
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2021/11/26 18:04
     */
    public function kefuadd()
    {
        $post = $this->request->post();
        $post['disable'] = isset($post['disable']) && $post['disable'] == 'on' ? 0 : 1;
        $post['shop_id'] = $this->shop_id;
        (new KefuValidate())->goCheck('add', $post);
        $res = KefuLogic::add($post, $this->shop_id);
        if (false === $res) {
            $error = KefuLogic::getError() ?: '操作失败';
            return JsonServer::error($error);
        }
        return JsonServer::success('操作成功');
    }

    /**
     * @notes 编辑客服
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2021/11/27 10:45
     */
    public function kefuedit()
    {
        $post = $this->request->post();
        if (empty($post)) {
            // GET请求，获取详情
            $id = $this->request->get('id');
            return JsonServer::success('', ['detail' => KefuLogic::detail($id, $this->shop_id)]);
        }

        // POST请求，更新数据
        $post['disable'] = isset($post['disable']) && $post['disable'] == 'on' ? 0 : 1;
        (new KefuValidate())->goCheck('edit', $post);
        $res = KefuLogic::edit($post, $this->shop_id);
        if (false === $res) {
            $error = KefuLogic::getError() ?: '操作失败';
            return JsonServer::error($error);
        }
        return JsonServer::success('操作成功');
    }

    /**
     * @notes 删除客服
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2021/11/26 18:53
     */
    public function kefudel()
    {
        $post = $this->request->post();
        (new KefuValidate())->goCheck('del');
        KefuLogic::del($post, $this->shop_id);
        return JsonServer::success('操作成功');
    }

    /**
     * @notes 设置状态
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2021/11/26 18:40
     */
    public function status()
    {
        $post = $this->request->post();
        $result = KefuLogic::setStatus($post, $this->shop_id);
        if ('至少保留一个客服' == $result) {
            return JsonServer::error($result);
        }
        return JsonServer::success('操作成功');
    }

    /**
     * @notes 客服详情
     * @return \think\response\Json
     */
    public function kefuDetail()
    {
        $id = $this->request->get('id');
        if (empty($id)) {
            return JsonServer::error('缺少参数id', []);
        }
        $detail = KefuLogic::detail($id, $this->shop_id);
        if (empty($detail)) {
            return JsonServer::error('未找到客服信息', []);
        }

        return JsonServer::success('获取成功', $detail);
    }

    /**
     * @notes 获取客服设置（移动端接口）
     * @return \think\response\Json
     */
    public function getCustomerService()
    {
        $image2 = ConfigServer::get('shop_customer_service', 'image', '', $this->shop_id);
        $image = $image2 ? \app\common\server\UrlServer::getFileUrl($image2) : '';
        $config = [
            'type' => ConfigServer::get('shop_customer_service', 'type', 1, $this->shop_id),
            'wechat' => ConfigServer::get('shop_customer_service', 'wechat', '', $this->shop_id),
            'phone' => ConfigServer::get('shop_customer_service', 'phone', '', $this->shop_id),
            'business_time' => ConfigServer::get('shop_customer_service', 'business_time', '', $this->shop_id),
            'image' => $image,
            'image_url' => $image2,
        ];
        return JsonServer::success('获取成功', $config);
    }

    /**
     * @notes 设置客服设置（移动端接口）
     * @return \think\response\Json
     */
    public function setCustomerService()
    {
        $post = $this->request->post();
        $post['wechat']=$post['wechat'] ?? '';
        $post['type']=$post['type'] ?? '';
        $post['phone']=$post['phone'] ?? '';
        $post['business_time']=$post['business_time'] ?? '';
        ConfigServer::set('shop_customer_service', 'type', $post['type'], $this->shop_id);
        ConfigServer::set('shop_customer_service', 'wechat', $post['wechat'], $this->shop_id);
        ConfigServer::set('shop_customer_service', 'phone', $post['phone'], $this->shop_id);
        ConfigServer::set('shop_customer_service', 'business_time', $post['business_time'], $this->shop_id);
        if (isset($post['image'])) {
            ConfigServer::set('shop_customer_service', 'image', $post['image'], $this->shop_id);
        }
        return JsonServer::success('设置成功');
    }

    /**
     * @notes 客服话术列表
     * @return \think\response\Json
     */
    public function kefuLangGroupList()
    {
        $groups = \app\shop\logic\kefu\KefuLangLogic::getGroups($this->shop_id);
        return JsonServer::success('获取成功', $groups);
    }

    /**
     * @notes 新增客服话术分组
     * @return \think\response\Json
     */
    public function kefuLangGroupAdd()
    {
        $post = $this->request->post();
        $post['shop_id'] = $this->shop_id;
        $result = \app\shop\logic\kefu\KefuLangLogic::addGroup($post);
        if ($result) {
            return JsonServer::success('新增成功');
        }
        return JsonServer::error('新增失败');
    }

    /**
     * @notes 客服自动回复设置
     * @return \think\response\Json
     */
    public function kefuAutoReply()
    {
        $post = $this->request->post();
        if (empty($post)) {
            // GET请求，获取当前设置
            $config = ConfigServer::get('kefu_auto_reply', 'content', '', $this->shop_id);
            $status = ConfigServer::get('kefu_auto_reply', 'status', 0, $this->shop_id);
            return JsonServer::success('获取成功', [
                'content' => $config,
                'status' => $status
            ]);
        }

        // POST请求，更新设置
        ConfigServer::set('kefu_auto_reply', 'content', $post['content'], $this->shop_id);
        ConfigServer::set('kefu_auto_reply', 'status', $post['status'] ?? 1, $this->shop_id);
        return JsonServer::success('设置成功');
    }

    /**
     * @notes 获取集采购记录列表
     * @return \think\response\Json
     */
    public function jcaiOrderLists()
    {
        $get = $this->request->get();
        $get['shop_id'] = $this->shop_id;
        $get['page'] = $get['page_no'] ?? 1;
        $get['limit'] = $get['page_size'] ?? 15;

        $lists = JcaiLogic::lists($get);
        return JsonServer::success('获取成功', $lists);
    }

    /**
     * @notes 集采购订单详情
     * @return \think\response\Json
     */
    public function jcaiOrderDetail()
    {
        $id = $this->request->get('id');
        $detail = JcaiLogic::detail($id, $this->shop_id);
        return JsonServer::success('获取成功', $detail);
    }

    /**
     * @notes 获取退货地址列表
     * @return \think\response\Json
     */
    public function refundAddressList()
    {
        $lists = RefundAddressLogic::lists($this->shop_id);
        return JsonServer::success('获取成功', $lists);
    }

    /**
     * @notes 添加退货地址
     * @return \think\response\Json
     */
    public function refundAddressAdd()
    {
        $post = $this->request->post();
        $post['shop_id'] = $this->shop_id;
        $result = RefundAddressLogic::add($post);
        if ($result) {
            return JsonServer::success('添加成功');
        }
        return JsonServer::error('添加失败');
    }

    /**
     * @notes 编辑退货地址
     * @return \think\response\Json
     */
    public function refundAddressEdit()
    {
        $post = $this->request->post();
        $result = RefundAddressLogic::edit($post, $this->shop_id);
        if ($result) {
            return JsonServer::success('编辑成功');
        }
        return JsonServer::error('编辑失败');
    }

    /**
     * @notes 删除退货地址
     * @return \think\response\Json
     */
    public function refundAddressDel()
    {
        $id = $this->request->post('id');
        $result = RefundAddressLogic::del($id, $this->shop_id);
        if ($result) {
            return JsonServer::success('删除成功');
        }
        return JsonServer::error('删除失败');
    }

    /**
     * @notes 设置默认退货地址
     * @return \think\response\Json
     */
    public function setDefaultRefundAddress()
    {
        $id = $this->request->post('id');
        $result = RefundAddressLogic::setDefault($id, $this->shop_id);
        if ($result) {
            return JsonServer::success('设置成功');
        }
        return JsonServer::error('设置失败');
    }

    /************************* 会员管理模块 *************************/

    /**
     * 会员列表
     * @return \think\response\Json
     */
    public function Userlists()
    {

        $get = $this->request->get();
        $get['shop_id'] = $this->shop_id;
        $data = UserLogic::lists($get);
        return JsonServer::success('', $data);
    }

    /**
     * 会员详情
     * @return \think\response\Json
     */
    public function Userinfo()
    {
        $id = $this->request->get('id', '', 'intval');
        $detail = UserLogic::getInfo($id);
        return JsonServer::success('', $detail);
    }

    /************************* 管理员和角色权限模块 *************************/

    /**
     * 管理员列表
     * @return \think\response\Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function adminLists()
    {
        $get = $this->request->get();
        return JsonServer::success('', AdminLogic::lists($get, $this->shop_id));
    }

    /**
     * 添加管理员
     * @return \think\response\Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function Adminadd()
    {
        $post = $this->request->post();
        $post['disable'] = isset($post['disable']) && $post['disable'] == 'on' ? 0 : 1;
        (new AdminValidate())->goCheck('add');
        if (AdminLogic::addAdmin($this->shop_id, $post)) {
            return JsonServer::success('操作成功');
        }
        return JsonServer::error(AdminLogic::getError() ?: '操作失败');
    }

    /**
     * 编辑管理员
     * @return \think\response\Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function Adminedit()
    {
        $post = $this->request->post();
        if (empty($post)) {
            // GET请求，获取详情
            $id = $this->request->get('admin_id');
            $detail = ShopAdmin::find($id);
            $detail['role_name'] = (new ShopRole())->getRoleName($detail['role_id']);
            return JsonServer::success('', [
                'detail' => $detail,
                'role_lists' => (new ShopRole())->getRoleLists(['shop_id' => $this->shop_id])
            ]);
        }

        // POST请求，更新数据
        $post['disable'] = isset($post['disable']) && $post['disable'] == 'on' ? 0 : 1;
        (new AdminValidate())->goCheck('edit');
        if (AdminLogic::editAdmin($this->shop_id, $post)) {
            return JsonServer::success('操作成功');
        }
        return JsonServer::error(AdminLogic::getError() ?: '操作失败');
    }

    /*
     * 获取商家入驻费及组合购买权益
     * DM
     */
    public function getAdTemplate()
    {
        // 考虑移除硬编码的值，或在注释中解释这些数字的含义
        $page = $this->request->get('page', 1);
        $page_size = $this->request->get('page_size', 10);
        $list = AdLogic::lists(31, $page, $page_size);
        return JsonServer::success('', $list);
    }

    /**
     * 缴纳入驻费
     *
     */
    public function ruzhucharge()
    {
        $post = $this->request->post();
        if (!isset($post['mobile']) || empty($post['mobile'])) {
            return JsonServer::error('缺少手机号参数');
        }
        $user_id = Db::name('user')->where('mobile', $post['mobile'])->value('id');
        if (!$user_id) {
            return JsonServer::error('未找到关联用户');
        }
        $post['feetype']=2;
        $result = RechargeLogic::ruzhucharge($user_id, $post);
        if ($result === false) {
            return JsonServer::error(RechargeLogic::getError() ?: '入驻费支付请求失败');
        }
        return JsonServer::success('', $result);
    }

    /*
         * 获取商家入驻费及组合购买
         * DM
         */
    public function getuzhuTemplate()
    {
        $list = RechargeLogic::getuzhuTemplate($this->shop_id ?: 0);
        return JsonServer::success('', $list);
    }

    /**
     * 删除管理员
     * @return \think\response\Json
     */
    public function Admindel()
    {
        $id = $this->request->post('admin_id');
        if (AdminLogic::delAdmin($this->shop_id, $id)) {
            return JsonServer::success('操作成功');
        }
        return JsonServer::error(AdminLogic::getError() ?: '操作失败');
    }

    /**
     * 修改密码
     * @return \think\response\Json
     */
    public function password()
    {
        $post = $this->request->post();
        $post['admin_id'] = $this->admin_id;
        (new \app\shop\validate\AdminPasswordValidate())->goCheck('', $post);
        $res = AdminLogic::updatePassword($post['password'], $this->admin_id, $this->shop_id);
        if ($res) {
            return JsonServer::success('操作成功');
        }
        return JsonServer::error(AdminLogic::getError() ?: '系统错误');
    }

    /**
     * 获取角色列表
     * @return \think\response\Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function roleLists()
    {
        if (!$this->shop_id) {
            return JsonServer::error('未获取到商家信息');
        }

        $get = $this->request->get();
        $get['limit'] = $get['page_size'] ?? $this->page_size;
        $get['page'] = $get['page_no'] ?? $this->page_no;

        $data = RoleLogic::lists($this->shop_id, $get);
        return JsonServer::success('获取成功', $data);
    }

    /**
     * 添加角色
     * @return \think\response\Json
     */
    public function roleAdd()
    {
        if (!$this->shop_id) {
            return JsonServer::error('未获取到商家信息');
        }

        $post = $this->request->post();
        (new RoleValidate())->goCheck('add');

        $result = RoleLogic::addRole($this->shop_id, $post);
        if ($result !== true) {
            return JsonServer::error(RoleLogic::getError() ?: '操作失败');
        }
        return JsonServer::success('操作成功');
    }

    /**
     * 编辑角色
     * @return \think\response\Json
     */
    public function roleEdit()
    {
        if (!$this->shop_id) {
            return JsonServer::error('未获取到商家信息');
        }

        $post = $this->request->post();
        (new RoleValidate())->goCheck('edit');

        $result = RoleLogic::editRole($this->shop_id, $post);
        if ($result !== true) {
            return JsonServer::error(RoleLogic::getError() ?: '操作失败');
        }
        return JsonServer::success('操作成功');
    }

    /**
     * 删除角色
     * @return \think\response\Json
     * @throws \think\Exception
     */
    public function roleDel()
    {
        if (!$this->shop_id) {
            return JsonServer::error('未获取到商家信息');
        }

        $post = $this->request->post();
        (new RoleValidate())->goCheck('del'); // 验证 'id' 参数

        $result = RoleLogic::delRole($this->shop_id, $post['id']);
        if ($result === false) {
            return JsonServer::error(RoleLogic::getError() ?: '删除失败');
        }
        return JsonServer::success('删除成功');
    }

    /**
     * 获取角色详情 (主要用于编辑前回显)
     * @return \think\response\Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function roleInfo()
    {
        $role_id = $this->request->get('id/d', 0); // 使用 '/d' 强制转换为整数
        if (!$role_id) {
            return JsonServer::error('缺少角色ID');
        }
        if (!$this->shop_id) {
            return JsonServer::error('未获取到商家信息');
        }

        // 获取角色信息
        $info = RoleLogic::roleInfo($role_id);

        // 确保只返回属于当前店铺的角色信息
        if (!$info || $info->shop_id != $this->shop_id) {
            return JsonServer::error('角色不存在或无权访问');
        }

        return JsonServer::success('', $info);
    }

    /**
     * 获取权限树 (用于添加/编辑角色时分配权限)
     * @return \think\response\Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function roleAuthTree()
    {
        $role_id = $this->request->get('role_id/d', 0); // 编辑时传入角色ID以选中已有权限
        $auth_tree = RoleLogic::authTree($role_id);
        return JsonServer::success('', $auth_tree);
    }

    /************************* 售后管理模块 *************************/

    /**
     * 售后列表
     * @return \think\response\Json
     */
    public function afterSaleLists()
    {
        $get = $this->request->get();
        if (!$this->shop_id) {
            return JsonServer::error('未获取到商家信息');
        }
        // 调整page和limit参数以适应AfterSaleLogic::list方法
        $get['page'] = $get['page_no'] ?? $this->page_no;
        $get['limit'] = $get['page_size'] ?? $this->page_size;

        $data = AfterSaleLogic::list($get, $this->shop_id);
        return JsonServer::success('', $data);
    }

    /**
     * 售后详情
     * @return \think\response\Json
     */
    public function afterSaleDetail()
    {
        $id = $this->request->get('id/d');
        if (empty($id)) {

            return JsonServer::error('请提供售后单ID');
        }
        if (!$this->shop_id) {
            return JsonServer::error('未获取到商家信息');
        }

        $detail = AfterSaleLogic::getDetail($id, $this->shop_id);
        if (empty($detail)) {
            return JsonServer::error('未找到售后记录或无权访问');
        }

        return JsonServer::success('', $detail);
    }

    /**
     * 同意售后申请
     * @return \think\response\Json
     */
    public function afterSaleAgree()
    {
        $post = $this->request->post();
        if (!isset($post['id']) || empty($post['id'])) {
            return JsonServer::error('缺少必要的参数: id');
        }
        if (!$this->shop_id) {
            return JsonServer::error('未获取到商家信息');
        }

        $data = AfterSaleLogic::agree($post['id'], $this->shop_id);
        if ($data !== false) {
            return JsonServer::success('操作成功');
        } else {
            return JsonServer::error(AfterSaleLogic::getError() ?: '退款确认失败');
        }
    }

    /**
     * 拒绝售后申请
     * @return \think\response\Json
     */
    public function afterSaleRefuse()
    {
        $post = $this->request->post();
        if (!isset($post['id']) || !isset($post['remark'])) {
            return JsonServer::error('缺少必要的参数: id 或 remark');
        }
        if (!$this->shop_id) {
            return JsonServer::error('未获取到商家信息');
        }

        $result = AfterSaleLogic::refuse($post, $this->shop_id);
        if ($result === false) {
            return JsonServer::error(AfterSaleLogic::getError() ?: '操作失败');
        }
        return JsonServer::success('操作成功');
    }

    /**
     * 确认收货 (退货场景)
     * @return \think\response\Json
     */
    public function afterSaleTake()
    {
        $post = $this->request->post();
        if (!isset($post['id'])) {
            return JsonServer::error('缺少必要的参数: id');
        }
        if (!$this->admin_id) {
            return JsonServer::error('未获取到管理员信息');
        }

        $post['shop_id'] = $this->shop_id;
        $result = AfterSaleLogic::takeGoods($post, $this->admin_id);
        if ($result === false) {
            return JsonServer::error(AfterSaleLogic::getError() ?: '操作失败');
        }
        return JsonServer::success('操作成功');
    }

    /**
     * 拒绝收货 (退货场景)
     * @return \think\response\Json
     */
    public function afterSaleRefuseGoods()
    {
        $post = $this->request->post();
        if (!isset($post['id']) || !isset($post['remark'])) {
            return JsonServer::error('缺少必要的参数: id 或 remark');
        }
        if (!$this->admin_id) {
            return JsonServer::error('未获取到管理员信息');
        }

        $post['shop_id'] = $this->shop_id;
        $result = AfterSaleLogic::refuseGoods($post, $this->admin_id);
        if ($result === false) {
            return JsonServer::error(AfterSaleLogic::getError() ?: '操作失败');
        }
        return JsonServer::success('操作成功');
    }

    /**
     * 确认退款
     * @return \think\response\Json
     */
    public function afterSaleConfirm()
    {
        $post = $this->request->post();
        if (!isset($post['id'])) {
            return JsonServer::error('缺少必要的参数: id');
        }
        if (!$this->admin_id) {
            return JsonServer::error('未获取到管理员信息');
        }

        $post['shop_id'] = $this->shop_id;
        $confirm = AfterSaleLogic::confirm($post, $this->admin_id);
        if ($confirm !== true) {
            return JsonServer::error($confirm ?: '退款确认失败');
        }
        return JsonServer::success('操作成功');
    }

    /**
     * 售后导出Excel
     * @return \think\response\Json
     */
    public function afterSaleExport()
    {
        if (!$this->shop_id) {
            return JsonServer::error('未获取到商家信息');
        }

        $params = $this->request->get();
        // 将参数调整为AfterSaleLogic::list方法期望的格式
        $params['page'] = $params['page_no'] ?? 1;
        $params['limit'] = $params['page_size'] ?? 20;

        $result = AfterSaleLogic::list($params, $this->shop_id, true);
        if (false === $result) {
            return JsonServer::error(AfterSaleLogic::getError() ?: '导出失败');
        }

        return JsonServer::success('导出数据生成成功', $result);
    }

    /************************* 支付管理模块 *************************/

    /**
     * 统一支付入口 (Shop API version)
     * @return \think\response\Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function unifiedPay()
    {
        $post = $this->request->post();

        if (!isset($post['pay_way'])) {
            return JsonServer::error('请选择支付方式');
        }
        if (!isset($post['from'])) {
            return JsonServer::error('缺少来源参数 from');
        }
        if (!isset($post['order_sn'])) {
            return JsonServer::error('缺少订单编号');
        }

        $order_sn = $post['order_sn'];
        $user_id = null;

        if (isset($post['user_id'])) {
            $user_id = intval($post['user_id']);
        } elseif (isset($post['mobile'])) {
            $user_id = Db::name('user')->where('mobile', $post['mobile'])->value('id');
        }

        $order = (new ShopMerchantfees)->where([
            ['order_sn', '=', $order_sn]
        ])->find();

        if (!$order) {
            return JsonServer::error('未找到订单: ' . $order_sn);
        }

        try {
            $result = PayLogic::wechatPay($order_sn, $post['from'], 1);
            return $result;
        } catch (\Exception $e) {
            Log::error('Payment processing error: ' . $e->getMessage());
            return JsonServer::error('支付处理异常：' . $e->getMessage());
        }
    }

    /**
     * @notes 规则协议列表
     * @return \think\response\Json
     */
    public function protocolLists()
    {
        try {
            $get = $this->request->get();
            $get['page_no'] = $this->page_no;
            $get['page_size'] = 10000;
            $data = HelpLogic::lists($get);
            return JsonServer::success('获取成功', $data);
        } catch (\Exception $e) {
            Log::error('Protocol lists error: ' . $e->getMessage());
            return JsonServer::error('获取规则协议列表失败');
        }
    }

    /**
     * @notes 规则协议详情
     * @return \think\response\Json
     */
    public function protocolDetail()
    {
        try {
            $id = $this->request->get('id', '', 'intval');
            if (empty($id)) {
                return JsonServer::error('参数错误');
            }
            $data = HelpLogic::detail($id);
            return JsonServer::success('获取成功', $data);
        } catch (\Exception $e) {
            Log::error('Protocol detail error: ' . $e->getMessage());
            return JsonServer::error('获取规则协议详情失败');
        }
    }

    /**
     * @notes 意见反馈
     * @return \think\response\Json
     */
    public function feedback()
    {
        try {
            $post = $this->request->post();

            if (empty($post['content'])) {
                return JsonServer::error('请输入反馈内容');
            }
            $result = HelpLogic::addFeedback($this->shop_id, $post);
            if ($result === false) {
                return JsonServer::error(HelpLogic::getError() ?: '提交反馈失败');
            }
            return JsonServer::success('提交成功');
        } catch (\Exception $e) {
            Log::error('Feedback submission error: ' . $e->getMessage());
            return JsonServer::error('提交反馈失败');
        }
    }

    /**
     * @notes form表单方式上传图片（商家端专用）
     * @return \think\response\Json
     */
    public function uploadImage()
    {
        try {
            $data = \app\common\server\FileServer::image(0, 0, $this->shop_id, 'uploads/shop/' . $this->shop_id);
            return \app\common\server\JsonServer::success('上传成功', $data);
        } catch (\Exception $e) {
            return \app\common\server\JsonServer::error($e->getMessage());
        }
    }

    /**
     * @notes 客服话术列表
     * @return \think\response\Json
     */
    public function kefuLangList()
    {
        $get = $this->request->get();
        $page = $get['page'] ?? $this->page_no;
        $limit = $get['limit'] ?? $this->page_size;
        $data = KefuLangLogic::lists($this->shop_id, $limit, $page);
        return JsonServer::success('获取成功', $data);
    }

    /**
     * @notes 客服话术详情
     * @return \think\response\Json
     */
    public function kefuLangDetail()
    {
        $id = $this->request->get('id', '', 'intval');
        $data = KefuLangLogic::detail($this->shop_id, $id);
        return JsonServer::success('获取成功', $data);
    }

    /**
     * @notes 添加客服话术
     * @return \think\response\Json
     */
    public function kefuLangAdd()
    {
        (new KefuLangValidate())->goCheck('sceneAdd');
        $post = $this->request->post();
        $result = KefuLangLogic::add($this->shop_id, $post);
        if (!$result) {
            return JsonServer::error(KefuLangLogic::getError() ?: '添加失败');
        }
        return JsonServer::success('添加成功');
    }

    /**
     * @notes 编辑客服话术
     * @return \think\response\Json
     */
    public function kefuLangEdit()
    {
        $post = $this->request->post();
        $post['shop_id'] = $this->shop_id;
        (new KefuLangValidate())->goCheck('edit',$post  );


        $result = KefuLangLogic::edit($post);
        if (!$result) {
            return JsonServer::error(KefuLangLogic::getError() ?: '编辑失败');
        }
        return JsonServer::success('编辑成功');
    }

    /**
     * @notes 删除客服话术
     * @return \think\response\Json
     */
    public function kefuLangDel()
    {
        (new KefuLangValidate())->goCheck('sceneDel');
        $id = $this->request->post('id');
        $result = KefuLangLogic::del($this->shop_id, $id);
        if (!$result) {
            return JsonServer::error(KefuLangLogic::getError() ?: '删除失败');
        }
        return JsonServer::success('删除成功');
    }

    /************************* 保证金管理模块 *************************/

    /**
     * @notes 获取保证金配置
     * @return \think\response\Json
     */
    public function getDepositConfig()
    {
        $config = SettingLogic::getShopWithdraw();
        // 同时返回当前商家今年未过期的已缴纳的保证金信息

        $depositInfo = ShopDeposit::where('shop_id', $this->shop_id)->order('id desc')->find();
        $data = [
            'config' => $config,
            'deposit_info' => $depositInfo ? $depositInfo->toArray() : null
        ];
        return JsonServer::success('获取成功', $data);
    }
    /*
     * 上传保证金合同
     */
//    public function uploadDepositContract()
//    {
//        try {
//            $post = $this->request->post();
//            $post['shop_id'] = $this->shop_id;
//            $result = ShopDepositLogic::uploadContract($post);
//            if ($result === false) {
//                return JsonServer::error(ShopDepositLogic::getError() ?: '上传失败');
//            }
//            return JsonServer::success('上传成功');
//        } catch (\Exception $e) {
//            Log::error('Upload deposit contract error: ' . $e->getMessage());
//        }
//    }

    /**
     * @notes 获取保证金明细
     * @return \think\response\Json
     * @throws \think\db\exception\DbException
     */
    public function depositDetails()
    {
        if (!$this->shop_id) {
            return JsonServer::error('未获取到商家信息');
        }
        $get = $this->request->get();
        // 调用ShopLogic获取保证金明细
        $shopLogic = new ShopLogic();
        $details = $shopLogic->getDepositDetails($this->shop_id, $get['page'],$get['limit']);
        return JsonServer::success('', $details);
    }

    /**
     * @notes 缴纳/支付保证金
     * @return \think\response\Json
     */
    public function bondcharge()
    {
        $post = $this->request->post();

        // 强制设置shop_id
        $post['shop_id'] = $this->shop_id;
        $post['admin_id'] = $this->admin_id;

        // 调用 RechargeLogic 处理保证金缴纳逻辑
        $result = RechargeLogic::bondcharge($this->shop_id, $post);

        if ($result === false) {
            return JsonServer::error(RechargeLogic::getError() ?: '支付处理失败');
        }

        if ($result instanceof \think\response\Json) {
            return $result;
        }

        // 成功时，$result 包含支付所需信息
        return JsonServer::success('支付请求成功', (array)$result);
    }

    /**
     * @notes 补缴保证金
     * @return \think\response\Json
     */
    public function replenishDeposit()
    {
        $post = $this->request->post();
        // 使用 DepositRechargeValidate 验证金额和支付方式
        (new DepositRechargeValidate())->goCheck('', $post);

        // 强制设置 shop_id
        $post['shop_id'] = $this->shop_id;

        // 调用 RechargeLogic 处理保证金补缴逻辑
        $result = RechargeLogic::replenishDeposit($this->shop_id, $post);

        if ($result === false) {
            return JsonServer::error(RechargeLogic::getError() ?: '保证金补缴请求失败');
        }

        // 成功时，$result 包含支付所需信息
        return JsonServer::success('支付请求成功', (array) $result);
    }

    /************************* 广告位管理模块 *************************/

    /**
     * 获取可用广告位列表
     *
     * @param array $params 请求参数
     * @param int $page 页码,默认1
     * @param int $pageSize 每页数量,默认10
     * @return array 广告位列表数据
     * @throws \Exception
     */
    public function listAvailableAdSlots()
    {
        $params = $this->request->get();
        // 添加默认分页参数
        $page = isset($params['page']) ? max(1, intval($params['page'])) : 1;
        $pageSize = isset($params['page_size']) ? max(1, intval($params['page_size'])) : 10;

        $result = ShopAdLogic::listAvailableSlots($params, $page, $pageSize);
        return JsonServer::success('获取成功', is_array($result) ? $result : []);
    }
    //购买的未过期的广告位 title和id
    public function listAdSlots()
    {
        $result = ShopAdLogic::listSlots($this->shop_id);
        return JsonServer::success('获取成功', is_array($result) ? $result : []);
    }


    /**
     * @notes 购买广告位
     * @return \think\response\Json
     */
    public function purchaseAdSlot()
    {
        $post = $this->request->post();
        // 验证必要参数，例如 ad_position_id, pay_way
        if (empty($post['ad_position_id'])) {
            return JsonServer::error('请选择要购买的广告位');
        }
        if (empty($post['pay_way'])) {
            return JsonServer::error('请选择支付方式');
        }
        $post['id'] = $post['ad_position_id'];
        $result = DecorationShopAdLogic::AdOrderadd($post, $this->shop_id);
        if ($result) {
            return JsonServer::success('添加成功', ['order_id' => $result]);
        } else {
            return JsonServer::error(ShopAdLogic::getError() ?: '购买失败');
        }
    }


    /**
     * @notes 购买/续费广告位并发起支付
     * @return \think\response\Json
     */
    public function payAdSlot()
    {
        $params = $this->request->post();

        // --- 开始修改/添加支付逻辑 ---
        try {
            // 验证必要参数
            if (empty($params['ad_order_id'])) {
                return JsonServer::error('缺少广告订单ID (ad_order_id)');
            }
            if (empty($params['pay_way'])) {
                return JsonServer::error('请选择支付方式 (pay_way)');
            }

            // 获取广告订单，并验证归属权和状态
            $adOrder = AdOrder::where('id', $params['ad_order_id'])
                ->where('shop_id', $this->shop_id)
                ->find();
            
            if (!$adOrder) {
                return JsonServer::error('广告订单不存在或无权访问');
            }

            // 检查订单是否已支付或处于不可支付状态 (假设 pay_status = 0 表示未支付)
            if ($adOrder->pay_status != 0) {
                return JsonServer::error('此广告订单状态无法支付');
            }

            $payData = [
                'from'      => 'AdOrder', // 订单来源: 广告订单
                'order_id'  => $adOrder->id,      // 广告订单ID
                'pay_way'   => $params['pay_way'], // 从请求参数获取支付方式
                'client'    => 1, // 客户端类型: 商家API (通常是 6)
                'user_id'   => Db::name('user')->where('shop_id', $this->shop_id)->value('id'),   // 使用当前登录的商家管理员ID作为支付用户ID
                'shop_id'   => $this->shop_id,    // 传递 shop_id
            ];
            // 实例化支付逻辑类
            $payLogic = new PayLogic();

            // 调用统一支付接口
            $payResult = PayLogic::wechatPay($payData['order_id'], $payData['from'], $payData['client']);

            $payResultData = $payResult->getData();
            if (isset($payResultData['code']) && $payResultData['code'] != 1) {
                return $payResult;
            }

            return $payResult;
        } catch (\Exception $e) {

            // 记录错误日志
            Log::error('PayAdSlot Error: ' . $e->getMessage());
            return JsonServer::error('支付请求处理异常，请稍后重试');
        }
        // --- 结束修改/添加支付逻辑 ---
    }

    /**     * @notes 获取我的广告位列表 (已购列表)
     * @return \think\response\Json
     */
    public function listMyAdSlots()
    {
        $params = $this->request->get();
        $params['page_no'] = $params['page'] ?? $this->page_no;
        $params['page_size'] = $params['limit'] ?? $this->page_size;

        $data = ShopAdLogic::listMyShopSlots($this->shop_id, $params);
        return JsonServer::success('获取成功', is_array($data) ? $data : []);
    }

    /**
     * @notes 更新/管理我的广告位内容
     * @return \think\response\Json
     */
    public function updateMyAdSlot()
    {
        $post = $this->request->post();
        // 验证必要参数，例如 ad_order_id, image, link_type 等
        if (empty($post['ad_order_id'])) {
            return JsonServer::error('缺少广告订单ID');
        }
        if (empty($post['image'])) {
            return JsonServer::error('请上传广告图片');
        }
        if (!isset($post['link_type'])) {
            return JsonServer::error('请选择链接类型');
        }

        $result = ShopAdLogic::updateMyAdSlot($this->shop_id, $post);
        if ($result === false) {
            return JsonServer::error(ShopAdLogic::getError() ?: '更新广告内容失败');
        }
        return JsonServer::success('更新成功', is_array($result) ? $result : []);
    }

    /**
     * @notes 修改手机号
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2021/11/11 16:11
     */
    public function updateMobile()
    {
        try {
            // Original line causing the error:
            // (new \app\shopapi\validate\ShopAccountValidate())->goCheck('updateMobile', ['shop_id' => $this->shop_id]);

            // Corrected line:
            validate(ShopAccountValidate::class)->scene('updateMobile')->check(['shop_id' => $this->shop_id]);

            $post = $this->request->post();
            $post['shop_id'] = $this->shop_id;
            $res = (new ShopLogic)->updateMobile($post, $this->shop_id, $this->admin_id);
            if (true === $res) {
                return JsonServer::success('手机号修改成功');
            }
            return JsonServer::error($res);
        } catch (ValidateException $e) {
            return JsonServer::error($e->getMessage());
        } catch (\Exception $e) {
            // It might be helpful to log the actual exception message for debugging
            // \think\facade\Log::error('Update mobile failed: ' . $e->getMessage());
            return JsonServer::error('手机号修改失败'); // Avoid exposing detailed error messages to the client
        }
    }

    /**
     * @notes 申请注销账号前检查条件
     * @return \think\response\Json
     */
    public function applyDeactivate()
    {
        try {
            // 检查各项条件
            $result = [];

            // 1. 检查未完结的订单（订单确认收货7天后才算完结）
            $orderCompletionTime = time() - (7 * 24 * 60 * 60); // 7天前的时间戳
            $unfinishedOrders = Order::where([
                ['shop_id', '=', $this->shop_id],
                ['order_status', 'in', [
                    OrderEnum::ORDER_STATUS_NO_PAID,  // 待支付
                    OrderEnum::ORDER_STATUS_DELIVERY, // 待发货
                    OrderEnum::ORDER_STATUS_GOODS     // 待收货
                ]]
            ])->count();

            // 已完成但未超过7天的订单
            $recentCompletedOrders = Order::where([
                ['shop_id', '=', $this->shop_id],
                ['order_status', '=', OrderEnum::ORDER_STATUS_COMPLETE], // 已完成
                ['confirm_take_time', '>', $orderCompletionTime] // 确认收货时间在7天内
            ])->count();

            $totalUnfinishedOrders = $unfinishedOrders + $recentCompletedOrders;
            $result['unfinished_orders'] = [
                'status' => $totalUnfinishedOrders === 0,
                'message' => $totalUnfinishedOrders === 0 ? '没有未完结的订单' : "有 {$totalUnfinishedOrders} 个未完结的订单",
                'count' => $totalUnfinishedOrders
            ];

            // 2. 检查未处理完成的售后单
            $unfinishedAfterSales = AfterSaleModel::alias('a')
                ->join('order o', 'o.id = a.order_id')
                ->where([
                    ['o.shop_id', '=', $this->shop_id],
                    ['a.status', 'not in', [
                        AfterSaleModel::STATUS_SUCCESS_REFUND, // 退款成功
                        AfterSaleModel::STATUS_REFUSE_REFUND   // 商家拒绝
                    ]]
                ])->count();

            $result['unfinished_after_sales'] = [
                'status' => $unfinishedAfterSales === 0,
                'message' => $unfinishedAfterSales === 0 ? '没有未处理完成的售后单' : "有 {$unfinishedAfterSales} 个未处理完成的售后单",
                'count' => $unfinishedAfterSales
            ];

            // 3. 检查未结算的资金
            $unsettledAmount = ShopMerchantfees::where([
                ['shop_id', '=', $this->shop_id],
                ['status', '=', 0] // 未结算状态
            ])->sum('amount');

            $result['unsettled_amount'] = [
                'status' => $unsettledAmount <= 0,
                'message' => $unsettledAmount <= 0 ? '没有未结算的资金' : "有 {$unsettledAmount} 元未结算的资金",
                'amount' => $unsettledAmount
            ];

            // 4. 检查未提现的钱
            $shop = ShopShop::where('id', $this->shop_id)->field('wallet')->find();
            $unwithdrawnAmount = $shop ? $shop['wallet'] : 0;

            $result['unwithdrawn_amount'] = [
                'status' => $unwithdrawnAmount <= 0,
                'message' => $unwithdrawnAmount <= 0 ? '没有未提现的资金' : "有 {$unwithdrawnAmount} 元未提现的资金",
                'amount' => $unwithdrawnAmount
            ];

            // 5. 总体状态
            $canDeactivate = $result['unfinished_orders']['status'] &&
                            $result['unfinished_after_sales']['status'] &&
                            $result['unsettled_amount']['status'] &&
                            $result['unwithdrawn_amount']['status'];

            // $result['can_deactivate'] = $canDeactivate;
            $result['can_deactivate']= true;
            // 如果所有条件都满足，并且提交了密码，则执行注销申请
            $post = $this->request->post();
            if (isset($post['password']) && !empty($post['password'])) {
                $shopLogic = new ShopLogic();
                $deactivateResult = $shopLogic->applyDeactivate($this->shop_id, $this->admin_id, $post);

                if ($deactivateResult !== true) {
                    return JsonServer::error('申请失败：' . $deactivateResult);
                }
                return JsonServer::success('申请成功,等待系统审核');
            }

            return JsonServer::success('账号注销条件检查结果', $result);
        } catch (\Exception $e) {
            Log::error('Account deactivation check error: ' . $e->getMessage());
            return JsonServer::error('检查注销条件失败：' . $e->getMessage());
        }
    }

    /**
     * @notes 获取账号安全信息
     * @return \think\response\Json
     */
    public function getAccountSecurity()
    {
        $data = (new ShopLogic())->getAccountSecurity($this->shop_id, $this->admin_id);
        return JsonServer::success('获取成功', $data);
    }

    /**
     * @notes 上传保证金合同（支持所有文件类型）
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2024/05/17
     */
    public function uploadDepositContract()
    {
        try {
            // 检查是否有文件上传
            $file = request()->file('file');
            if (empty($file)) {
                return JsonServer::error('请选择要上传的文件');
            }

            // 准备参数
            $post = [
                'shop_id' => $this->shop_id
            ];

            // 调用逻辑层处理上传
            $result = ShopDepositLogic::uploadContract($post);
            if ($result === false) {
                return JsonServer::error(ShopDepositLogic::getError() ?: '上传失败');
            }else{
                $result = [
                    'file_id' => $result
                ];
            }
            return JsonServer::success('上传成功', $result);
        } catch (\Exception $e) {
            Log::error('Upload deposit contract error: ' . $e->getMessage());
            return JsonServer::error('上传失败：' . $e->getMessage());
        }
    }
}
