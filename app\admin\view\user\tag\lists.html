{layout name="layout1" /}
<!-- 样式 -->
<style>
    .layui-table-cell {
        height: auto;
    }
</style>
<!-- 操作提示 -->
<div class="layui-fluid">
    <div class="layui-card" style="margin-top: 15px;">
        <div class="layui-card-body">
            <div class="layui-collapse like-layui-collapse" lay-accordion="" style="border:1px dashed #c4c4c4">
                <div class="layui-colla-item">
                    <h2 class="layui-colla-title like-layui-colla-title" style="background-color: #fff">操作提示</h2>
                    <div class="layui-colla-content layui-show">
                        <p>*平台可以根据会员特征、消费行为等要素给会员分配标签，进行针对性管理；</p>
                        <p>*会员可以同时打上多个标签；</p>
                    </div>
                </div>
            </div>
        </div>
        <!-- 功能按钮 -->
        <div class="layui-card-header layuiadmin-card-header-auto">
            <button class="layui-btn layui-btn-sm {$view_theme_color}"
                    id="add-user-tag">新增会员标签</button>
        </div>

        <div class="layui-card-body">
            <!-- 树形表格 -->
            <table id="user-tag-lists" lay-filter="user-tag-lists"></table>
        </div>
    </div>
</div>
<script type="text/html" id="operate">
    <button class="layui-btn layui-btn-normal layui-btn-sm" lay-event="edit">编辑</button>
    <button class="layui-btn layui-btn-danger layui-btn-sm" lay-event="del">删除</button>
</script>

<script>
    layui.config({
        version: "{$front_version}",
        base: '/static/lib/'
    }).use(['layer', 'table'], function () {
        var $ = layui.jquery;
        var layer = layui.layer;
        var table = layui.table;

        table.render({
            id: 'user-tag-lists'
            ,elem: '#user-tag-lists'
            ,url: "{:url('user.tag/lists')}"
            , parseData: function(res) { // res 原始返回数据
                return {
                    'code' : res.code  // 0 代表正常返回
                    , 'msg' : res.msg  // 提示消息
                    , 'count' : res.data.count // 数据长度
                    , 'data' : res.data.lists  // 数据列表
                }
            }
            , response: { // 重新设定返回的数据格式
                statusCode: 1, // 成功的状态码，默认0
            }
            , page: true // 开启分页
            , limit: 10
            , limits: [10, 20, 30, 40, 50]
            , text: {
                none: '暂无数据'
            }
            , cols: [[ // 设置表头，二维数组，方法渲染必填
                { field: 'name', title: '标签名称', width: 200, align: 'center' },
                { field: 'remark', title: '备注', width: 380, align: 'center' },
                { title: '操作', width: 380, align: 'center', toolbar: '#operate'}
            ]]
        });

        // 添加
        $('#add-user-tag').click(function() {
            layer.open({
                type: 2,
                title: '新增',
                content: "{:url('user.tag/add')}",
                area: ['60%', '60%']
            });
        });

        table.on('tool(user-tag-lists)', function(obj) {
            var id = obj.data.id;
            switch(obj.event) {
                case 'edit':
                    layer.open({
                        type: 2,
                        title: '编辑',
                        content: "{:url('user.tag/edit')}?id=" + id,
                        area: ['60%', '60%']
                    });
                    break;

                case 'del':
                    layer.confirm('确定删除会员标签?<span style="color:red;">' + obj.data.name + '</span>', {icon: 3, title:'提示'}, function(index){
                        like.ajax({
                            url:'{:url("user.tag/del")}',
                            type:'post',
                            data: {id:id},
                            success:function (res) {
                                if(res.code == 1) {
                                    layer.msg(res.msg,{time:1000}, function() {
                                        layer.close(index);
                                        window.location.reload();
                                    });
                                }
                            }
                        })
                    });
                    break;
            }
        });
    });
</script>