<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Cloudaudit\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * ListCmqEnableRegion请求参数结构体
 *
 * @method string getWebsiteType() 获取站点类型。zh表示中国区，en表示国际区。默认中国区。
 * @method void setWebsiteType(string $WebsiteType) 设置站点类型。zh表示中国区，en表示国际区。默认中国区。
 */
class ListCmqEnableRegionRequest extends AbstractModel
{
    /**
     * @var string 站点类型。zh表示中国区，en表示国际区。默认中国区。
     */
    public $WebsiteType;

    /**
     * @param string $WebsiteType 站点类型。zh表示中国区，en表示国际区。默认中国区。
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("WebsiteType",$param) and $param["WebsiteType"] !== null) {
            $this->WebsiteType = $param["WebsiteType"];
        }
    }
}
