<?php



namespace app\index\controller;



use app\common\basics\IndexBase;
use app\common\cache\ExportCache;


class Download extends IndexBase
{

    public $like_not_need_login = ['export'];

    public function export()
    {
        //获取文件缓存的key
        $fileKey = request()->get('file');

        //通过文件缓存的key获取文件储存的路径
        $exportCache = new ExportCache($fileKey);
        $fileInfo = $exportCache->getFile($fileKey);

        if (empty($fileInfo)) {
            abort(404, '下载文件不存在');
        }

        //下载前删除缓存
        $exportCache->del($fileKey);

        return download($fileInfo['src'] . $fileInfo['name'], $fileInfo['name']);
    }



}