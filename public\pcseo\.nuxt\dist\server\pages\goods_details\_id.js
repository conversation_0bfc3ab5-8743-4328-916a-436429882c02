exports.ids = [26,6,7,14,15,17];
exports.modules = {

/***/ 136:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(139);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(4).default
module.exports.__inject__ = function (context) {
  add("3181fc86", content, true, context)
};

/***/ }),

/***/ 137:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/price-formate.vue?vue&type=template&id=0c4d5c85&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('span',{class:(_vm.lineThrough ? 'line-through' : '') + 'price-format',style:({ color: _vm.color, 'font-weight': _vm.weight })},[_vm._ssrNode(((_vm.showSubscript)?("<span"+(_vm._ssrStyle(null,{
            'font-size': _vm.subscriptSize + 'px',
            'margin-right': '1px',
        }, null))+">¥</span>"):"<!---->")+" <span"+(_vm._ssrStyle(null,{ 'font-size': _vm.firstSize + 'px', 'margin-right': '1px' }, null))+">"+_vm._ssrEscape(_vm._s(_vm.priceSlice.first))+"</span> "+((_vm.priceSlice.second)?("<span"+(_vm._ssrStyle(null,{ 'font-size': _vm.secondSize + 'px' }, null))+">"+_vm._ssrEscape("."+_vm._s(_vm.priceSlice.second))+"</span>"):"<!---->"))])}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/price-formate.vue?vue&type=template&id=0c4d5c85&

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/price-formate.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
/* harmony default export */ var price_formatevue_type_script_lang_js_ = ({
  data() {
    return {
      priceSlice: {}
    };
  },

  components: {},
  props: {
    firstSize: {
      type: Number,
      default: 14
    },
    secondSize: {
      type: Number,
      default: 14
    },
    color: {
      type: String
    },
    weight: {
      type: [String, Number],
      default: 400
    },
    price: {
      type: [String, Number],
      default: ''
    },
    showSubscript: {
      type: Boolean,
      default: true
    },
    subscriptSize: {
      type: Number,
      default: 14
    },
    lineThrough: {
      type: Boolean,
      default: false
    }
  },

  created() {
    this.priceFormat();
  },

  watch: {
    price(val) {
      this.priceFormat();
    }

  },
  methods: {
    priceFormat() {
      let {
        price
      } = this;
      let priceSlice = {};

      if (price !== null) {
        price = parseFloat(price);
        price = String(price).split('.');
        priceSlice.first = price[0];
        priceSlice.second = price[1];
        this.priceSlice = priceSlice;
      }
    }

  }
});
// CONCATENATED MODULE: ./components/price-formate.vue?vue&type=script&lang=js&
 /* harmony default export */ var components_price_formatevue_type_script_lang_js_ = (price_formatevue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(1);

// CONCATENATED MODULE: ./components/price-formate.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(138)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  components_price_formatevue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  null,
  "7ae24710"
  
)

/* harmony default export */ var price_formate = __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 138:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_3_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_3_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_3_oneOf_1_2_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_price_formate_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(136);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_3_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_3_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_3_oneOf_1_2_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_price_formate_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_3_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_3_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_3_oneOf_1_2_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_price_formate_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_3_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_3_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_3_oneOf_1_2_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_price_formate_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_3_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_3_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_3_oneOf_1_2_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_price_formate_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 139:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(3);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".price-format{display:flex;align-items:baseline}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 140:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(142);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(4).default
module.exports.__inject__ = function (context) {
  add("12a18d22", content, true, context)
};

/***/ }),

/***/ 141:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_sass_resources_loader_lib_loader_js_ref_7_oneOf_1_4_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_null_data_vue_vue_type_style_index_0_id_93598fb0_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(140);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_sass_resources_loader_lib_loader_js_ref_7_oneOf_1_4_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_null_data_vue_vue_type_style_index_0_id_93598fb0_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_sass_resources_loader_lib_loader_js_ref_7_oneOf_1_4_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_null_data_vue_vue_type_style_index_0_id_93598fb0_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_sass_resources_loader_lib_loader_js_ref_7_oneOf_1_4_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_null_data_vue_vue_type_style_index_0_id_93598fb0_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_sass_resources_loader_lib_loader_js_ref_7_oneOf_1_4_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_null_data_vue_vue_type_style_index_0_id_93598fb0_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 142:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(3);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".null-data[data-v-93598fb0]{padding:100px}.null-data .img-null[data-v-93598fb0]{width:150px;height:150px}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 143:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/null-data.vue?vue&type=template&id=93598fb0&scoped=true&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:"bg-white flex-col col-center null-data"},[_vm._ssrNode("<img"+(_vm._ssrAttr("src",_vm.img))+" alt class=\"img-null\""+(_vm._ssrStyle(null,_vm.imgStyle, null))+" data-v-93598fb0> <div class=\"muted mt8\" data-v-93598fb0>"+_vm._ssrEscape(_vm._s(_vm.text))+"</div>")])}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/null-data.vue?vue&type=template&id=93598fb0&scoped=true&

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/null-data.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
/* harmony default export */ var null_datavue_type_script_lang_js_ = ({
  components: {},
  props: {
    img: {
      type: String
    },
    text: {
      type: String,
      default: '暂无数据'
    },
    imgStyle: {
      type: String,
      default: ''
    }
  },
  methods: {}
});
// CONCATENATED MODULE: ./components/null-data.vue?vue&type=script&lang=js&
 /* harmony default export */ var components_null_datavue_type_script_lang_js_ = (null_datavue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(1);

// CONCATENATED MODULE: ./components/null-data.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(141)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  components_null_datavue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  "93598fb0",
  "728f99de"
  
)

/* harmony default export */ var null_data = __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 144:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/count-down.vue?vue&type=template&id=2fbaab86&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return (_vm.time >= 0)?_c('div',[_c('client-only',[(_vm.isSlot)?_vm._t("default"):_c('span',[_vm._v(_vm._s(_vm.formateTime))])],2)],1):_vm._e()}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/count-down.vue?vue&type=template&id=2fbaab86&

// CONCATENATED MODULE: ./utils/parseTime.js
const SECOND = 1000;
const MINUTE = 60 * SECOND;
const HOUR = 60 * MINUTE;
const DAY = 24 * HOUR;
function parseTimeData(time) {
  const days = Math.floor(time / DAY);
  const hours = sliceTwo(Math.floor(time % DAY / HOUR));
  const minutes = sliceTwo(Math.floor(time % HOUR / MINUTE));
  const seconds = sliceTwo(Math.floor(time % MINUTE / SECOND));
  return {
    days: days,
    hours: hours,
    minutes: minutes,
    seconds: seconds
  };
}

function sliceTwo(str) {
  return (0 + str.toString()).slice(-2);
}

function parseFormat(format, timeData) {
  let days = timeData.days;
  let hours = timeData.hours,
      minutes = timeData.minutes,
      seconds = timeData.seconds;

  if (format.indexOf('dd') !== -1) {
    format = format.replace('dd', days);
  }

  if (format.indexOf('hh') !== -1) {
    format = format.replace('hh', sliceTwo(hours));
  }

  if (format.indexOf('mm') !== -1) {
    format = format.replace('mm', sliceTwo(minutes));
  }

  if (format.indexOf('ss') !== -1) {
    format = format.replace('ss', sliceTwo(seconds));
  }

  return format;
}
// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/count-down.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//

/* harmony default export */ var count_downvue_type_script_lang_js_ = ({
  components: {},
  props: {
    isSlot: {
      type: Boolean,
      default: false
    },
    time: {
      type: Number,
      default: 0
    },
    format: {
      type: String,
      default: 'hh:mm:ss'
    },
    autoStart: {
      type: Boolean,
      default: true
    }
  },
  watch: {
    time: {
      immediate: true,

      handler(value) {
        if (value) {
          this.reset();
        }
      }

    }
  },

  data() {
    return {
      timeObj: {},
      formateTime: 0
    };
  },

  created() {},

  computed: {},
  methods: {
    createTimer(fn) {
      return setTimeout(fn, 100);
    },

    isSameSecond(time1, time2) {
      return Math.floor(time1) === Math.floor(time2);
    },

    start() {
      if (this.counting) {
        return;
      }

      this.counting = true;
      this.endTime = Date.now() + this.remain * 1000;
      this.setTimer();
    },

    setTimer() {
      this.tid = this.createTimer(() => {
        let remain = this.getRemain();

        if (!this.isSameSecond(remain, this.remain) || remain === 0) {
          this.setRemain(remain);
        }

        if (this.remain !== 0) {
          this.setTimer();
        }
      });
    },

    getRemain() {
      return Math.max(this.endTime - Date.now(), 0);
    },

    pause() {
      this.counting = false;
      clearTimeout(this.tid);
    },

    reset() {
      this.pause();
      this.remain = this.time;
      this.setRemain(this.remain);

      if (this.autoStart) {
        this.start();
      }
    },

    setRemain(remain) {
      const {
        format
      } = this;
      this.remain = remain;
      const timeData = parseTimeData(remain);
      this.formateTime = parseFormat(format, timeData);
      this.$emit('change', timeData);

      if (remain === 0) {
        this.pause();
        this.$emit('finish');
      }
    }

  }
});
// CONCATENATED MODULE: ./components/count-down.vue?vue&type=script&lang=js&
 /* harmony default export */ var components_count_downvue_type_script_lang_js_ = (count_downvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(1);

// CONCATENATED MODULE: ./components/count-down.vue



function injectStyles (context) {
  
  
}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  components_count_downvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  null,
  "4090b4e2"
  
)

/* harmony default export */ var count_down = __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 162:
/***/ (function(module, exports, __webpack_require__) {

module.exports = __webpack_require__.p + "img/news_null.856b3f3.png";

/***/ }),

/***/ 164:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(180);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(4).default
module.exports.__inject__ = function (context) {
  add("663bee12", content, true, context)
};

/***/ }),

/***/ 179:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_sass_resources_loader_lib_loader_js_ref_7_oneOf_1_4_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_number_box_vue_vue_type_style_index_0_id_1d9d8f36_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(164);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_sass_resources_loader_lib_loader_js_ref_7_oneOf_1_4_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_number_box_vue_vue_type_style_index_0_id_1d9d8f36_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_sass_resources_loader_lib_loader_js_ref_7_oneOf_1_4_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_number_box_vue_vue_type_style_index_0_id_1d9d8f36_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_sass_resources_loader_lib_loader_js_ref_7_oneOf_1_4_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_number_box_vue_vue_type_style_index_0_id_1d9d8f36_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_sass_resources_loader_lib_loader_js_ref_7_oneOf_1_4_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_number_box_vue_vue_type_style_index_0_id_1d9d8f36_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 180:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(3);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".number-box[data-v-1d9d8f36]{display:inline-flex;align-items:center}.number-box .number-input[data-v-1d9d8f36]{position:relative;text-align:center;padding:0;margin:0 6px;align-items:center;justify-content:center}.number-box .minus[data-v-1d9d8f36],.number-box .plus[data-v-1d9d8f36]{width:32px;display:flex;justify-content:center;align-items:center;cursor:pointer}.number-box .plus[data-v-1d9d8f36]{border-radius:0 2px 2px 0}.number-box .minus[data-v-1d9d8f36]{border-radius:2px 0 0 2px}.number-box .disabled[data-v-1d9d8f36]{color:#c8c9cc!important;background:#f7f8fa!important}.number-box .input-disabled[data-v-1d9d8f36]{color:#c8c9cc!important;background-color:#f2f3f5!important}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 188:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(207);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(4).default
module.exports.__inject__ = function (context) {
  add("23143360", content, true, context)
};

/***/ }),

/***/ 192:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/number-box.vue?vue&type=template&id=1d9d8f36&scoped=true&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:"number-box"},[_vm._ssrNode("<div"+(_vm._ssrClass(null,{ minus: true, disabled: _vm.disabled || _vm.inputVal <= _vm.min }))+(_vm._ssrStyle(null,{
            background: _vm.bgColor,
            height: _vm.inputHeight + 'px',
            color: _vm.color,
        }, null))+" data-v-1d9d8f36><div"+(_vm._ssrStyle(null,{ fontSize: _vm.size + 'px' }, null))+" data-v-1d9d8f36>-</div></div> <input"+(_vm._ssrAttr("disabled",_vm.disabledInput || _vm.disabled))+" type=\"text\""+(_vm._ssrAttr("value",(_vm.inputVal)))+(_vm._ssrClass(null,{ 'number-input': true, 'input-disabled': _vm.disabled }))+(_vm._ssrStyle(null,{
            color: _vm.color,
            fontSize: _vm.size + 'px',
            background: _vm.bgColor,
            height: _vm.inputHeight + 'px',
            width: _vm.inputWidth + 'px',
        }, null))+" data-v-1d9d8f36> <div"+(_vm._ssrClass("plus",{ disabled: _vm.disabled || _vm.inputVal >= _vm.max }))+(_vm._ssrStyle(null,{
            background: _vm.bgColor,
            height: _vm.inputHeight + 'px',
            color: _vm.color,
        }, null))+" data-v-1d9d8f36><div"+(_vm._ssrStyle(null,{ fontSize: _vm.size + 'px' }, null))+" data-v-1d9d8f36>+</div></div>")])}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/number-box.vue?vue&type=template&id=1d9d8f36&scoped=true&

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/number-box.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
/* harmony default export */ var number_boxvue_type_script_lang_js_ = ({
  components: {},
  props: {
    // 预显示的数字
    value: {
      type: Number,
      default: 1
    },
    // 背景颜色
    bgColor: {
      type: String,
      default: ' #F2F3F5'
    },
    // 最小值
    min: {
      type: Number,
      default: 0
    },
    // 最大值
    max: {
      type: Number,
      default: 99999
    },
    // 步进值，每次加或减的值
    step: {
      type: Number,
      default: 1
    },
    // 是否禁用加减操作
    disabled: {
      type: Boolean,
      default: false
    },
    // input的字体大小，单位px
    size: {
      type: [Number, String],
      default: 14
    },
    // input宽度，单位px
    inputWidth: {
      type: [Number, String],
      default: 64
    },
    //字体颜色
    color: {
      type: String,
      default: '#333'
    },
    // input高度，单位px
    inputHeight: {
      type: [Number, String],
      default: 32
    },
    // index索引，用于列表中使用，让用户知道是哪个numberbox发生了变化，一般使用for循环出来的index值即可
    index: {
      type: [Number, String],
      default: ''
    },
    // 是否禁用输入框，与disabled作用于输入框时，为OR的关系，即想要禁用输入框，又可以加减的话
    // 设置disabled为false，disabledInput为true即可
    disabledInput: {
      type: Boolean,
      default: false
    },
    // 是否只能输入大于或等于0的整数(正整数)
    positiveInteger: {
      type: Boolean,
      default: true
    },
    asyncChange: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    value(v1, v2) {
      if (!this.changeFromInner) {
        this.inputVal = v1;
        this.$nextTick(function () {
          this.changeFromInner = false;
        });
      }
    },

    inputVal(v1, v2) {
      if (v1 == '') return;
      let value = 0;
      let tmp = /^(?:-?\d+|-?\d{1,3}(?:,\d{3})+)?(?:\.\d+)?$/.test(v1);
      if (tmp && v1 >= this.min && v1 <= this.max) value = v1;else value = v2;

      if (this.positiveInteger) {
        if (v1 < 0 || String(v1).indexOf('.') !== -1) {
          value = v2;
          this.$nextTick(() => {
            this.inputVal = v2;
          });
        }
      }

      if (this.asyncChange) {
        return;
      } // 发出change事件


      this.handleChange(value, 'change');
    }

  },

  data() {
    return {
      inputVal: 1,
      // 输入框中的值，不能直接使用props中的value，因为应该改变props的状态
      timer: null,
      // 用作长按的定时器
      changeFromInner: false,
      // 值发生变化，是来自内部还是外部
      innerChangeTimer: null // 内部定时器

    };
  },

  created() {
    this.inputVal = Number(this.value);
  },

  computed: {},
  methods: {
    btnTouchStart(callback) {
      this[callback]();
    },

    minus() {
      this.computeVal('minus');
    },

    plus() {
      this.computeVal('plus');
    },

    calcPlus(num1, num2) {
      let baseNum, baseNum1, baseNum2;

      try {
        baseNum1 = num1.toString().split('.')[1].length;
      } catch (e) {
        baseNum1 = 0;
      }

      try {
        baseNum2 = num2.toString().split('.')[1].length;
      } catch (e) {
        baseNum2 = 0;
      }

      baseNum = Math.pow(10, Math.max(baseNum1, baseNum2));
      let precision = baseNum1 >= baseNum2 ? baseNum1 : baseNum2;
      return ((num1 * baseNum + num2 * baseNum) / baseNum).toFixed(precision);
    },

    calcMinus(num1, num2) {
      let baseNum, baseNum1, baseNum2;

      try {
        baseNum1 = num1.toString().split('.')[1].length;
      } catch (e) {
        baseNum1 = 0;
      }

      try {
        baseNum2 = num2.toString().split('.')[1].length;
      } catch (e) {
        baseNum2 = 0;
      }

      baseNum = Math.pow(10, Math.max(baseNum1, baseNum2));
      let precision = baseNum1 >= baseNum2 ? baseNum1 : baseNum2;
      return ((num1 * baseNum - num2 * baseNum) / baseNum).toFixed(precision);
    },

    computeVal(type) {
      if (this.disabled) return;
      let value = 0; // 减

      if (type === 'minus') {
        value = this.calcMinus(this.inputVal, this.step);
      } else if (type === 'plus') {
        value = this.calcPlus(this.inputVal, this.step);
      } // 判断是否小于最小值和大于最大值


      if (value < this.min || value > this.max) {
        return;
      }

      if (this.asyncChange) {
        this.$emit('change', value);
      } else {
        this.inputVal = value;
        this.handleChange(value, type);
      }
    },

    // 处理用户手动输入的情况
    onBlur(event) {
      let val = 0;
      let value = event.target.value;
      console.log(value);

      if (!/(^\d+$)/.test(value)) {
        val = this.min;
      } else {
        val = +value;
      }

      if (val > this.max) {
        val = this.max;
      } else if (val < this.min) {
        val = this.min;
      }

      this.$nextTick(() => {
        this.inputVal = val;
      });
      this.handleChange(val, 'blur');
    },

    // 输入框获得焦点事件
    onFocus() {
      this.$emit('focus');
    },

    handleChange(value, type) {
      if (this.disabled) return; // 清除定时器，避免造成混乱

      if (this.innerChangeTimer) {
        clearTimeout(this.innerChangeTimer);
        this.innerChangeTimer = null;
      }

      this.changeFromInner = true;
      this.innerChangeTimer = setTimeout(() => {
        this.changeFromInner = false;
      }, 150);
      this.$emit('input', Number(value));
      this.$emit(type, {
        value: Number(value),
        index: this.index
      });
    }

  }
});
// CONCATENATED MODULE: ./components/number-box.vue?vue&type=script&lang=js&
 /* harmony default export */ var components_number_boxvue_type_script_lang_js_ = (number_boxvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(1);

// CONCATENATED MODULE: ./components/number-box.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(179)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  components_number_boxvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  "1d9d8f36",
  "284477ee"
  
)

/* harmony default export */ var number_box = __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 206:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_sass_resources_loader_lib_loader_js_ref_7_oneOf_1_4_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_comment_list_vue_vue_type_style_index_0_id_4e1720b8_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(188);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_sass_resources_loader_lib_loader_js_ref_7_oneOf_1_4_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_comment_list_vue_vue_type_style_index_0_id_4e1720b8_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_sass_resources_loader_lib_loader_js_ref_7_oneOf_1_4_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_comment_list_vue_vue_type_style_index_0_id_4e1720b8_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_sass_resources_loader_lib_loader_js_ref_7_oneOf_1_4_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_comment_list_vue_vue_type_style_index_0_id_4e1720b8_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_sass_resources_loader_lib_loader_js_ref_7_oneOf_1_4_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_comment_list_vue_vue_type_style_index_0_id_4e1720b8_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 207:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(3);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".comment-list .comment-con>.item[data-v-4e1720b8]{padding:20px;border-bottom:1px dashed #e5e5e5;align-items:flex-start}.comment-list .comment-con>.item .avatar img[data-v-4e1720b8]{border-radius:50%;width:44px;height:44px}.comment-list .comment-con>.item .comment-imglist[data-v-4e1720b8]{margin-top:10px}.comment-list .comment-con>.item .comment-imglist .item[data-v-4e1720b8]{width:80px;height:80px;margin-right:6px}.comment-list .comment-con>.item .reply[data-v-4e1720b8]{background-color:#f2f2f2;align-items:flex-start;padding:10px}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 245:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(332);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(4).default
module.exports.__inject__ = function (context) {
  add("6ab4852a", content, true, context)
};

/***/ }),

/***/ 252:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/comment-list.vue?vue&type=template&id=4e1720b8&scoped=true&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:"comment-list"},[_vm._ssrNode("<div class=\"comment-con\" data-v-4e1720b8>","</div>",[(_vm.commentList.length)?[_vm._l((_vm.commentList),function(item,index){return _vm._ssrNode("<div class=\"item flex\" data-v-4e1720b8>","</div>",[_vm._ssrNode("<div class=\"avatar m-r-8\" data-v-4e1720b8><img"+(_vm._ssrAttr("src",item.avatar))+" alt data-v-4e1720b8></div> "),_vm._ssrNode("<div class=\"content flex-1\" data-v-4e1720b8>","</div>",[_vm._ssrNode("<div data-v-4e1720b8>"+_vm._ssrEscape(_vm._s(item.nickname))+"</div> <div class=\"lighter\" style=\"margin: 5px 0 10px\" data-v-4e1720b8><span data-v-4e1720b8>"+_vm._ssrEscape(_vm._s(item.create_time))+"</span> <span data-v-4e1720b8>|</span> <span data-v-4e1720b8>"+_vm._ssrEscape("规格："+_vm._s(item.spec_value_str))+"</span></div> <div data-v-4e1720b8>"+_vm._ssrEscape("\n                        "+_vm._s(item.comment)+"\n                    ")+"</div> "),_vm._ssrNode("<div class=\"comment-imglist flex\" data-v-4e1720b8>","</div>",_vm._l((item.image),function(img,index){return _vm._ssrNode("<div class=\"item\" data-v-4e1720b8>","</div>",[_c('el-image',{staticStyle:{"height":"100%","width":"100%"},attrs:{"preview-src-list":item.image,"src":img,"fit":"contain"}})],1)}),0),_vm._ssrNode(" "+((item.reply)?("<div class=\"flex reply m-t-16\" data-v-4e1720b8><div class=\"primary flex-none\" data-v-4e1720b8>商家回复：</div> <div class=\"lighter\" data-v-4e1720b8>"+_vm._ssrEscape("\n                            "+_vm._s(item.reply)+"\n                        ")+"</div></div>"):"<!---->"))],2)],2)}),_vm._ssrNode(" "),(_vm.count)?_vm._ssrNode("<div class=\"pagination flex row-center\" style=\"padding: 38px 0\" data-v-4e1720b8>","</div>",[_c('el-pagination',{attrs:{"background":"","hide-on-single-page":"","layout":"prev, pager, next","total":_vm.count,"page-size":10},on:{"current-change":_vm.changePage}})],1):_vm._e()]:_c('null-data',{attrs:{"img":__webpack_require__(162),"text":"暂无评价~"}})],2)])}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/comment-list.vue?vue&type=template&id=4e1720b8&scoped=true&

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/comment-list.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
/* harmony default export */ var comment_listvue_type_script_lang_js_ = ({
  components: {},
  props: {
    list: {
      type: Array,
      default: () => []
    },
    type: Number,
    goodsId: [String, Number]
  },

  data() {
    return {
      commentList: [],
      count: 0,
      page: 1
    };
  },

  created() {
    this.getCommentList();
  },

  methods: {
    async getCommentList() {
      const {
        data,
        code
      } = await this.$get('goods_comment/lists', {
        params: {
          type: this.type,
          goods_id: this.goodsId,
          page_size: 10,
          page_no: this.page
        }
      });

      if (code == 1) {
        this.commentList = data.lists;
        this.count = data.count;
      }
    },

    changePage(current) {
      this.page = current;
      this.getCommentList();
    }

  }
});
// CONCATENATED MODULE: ./components/comment-list.vue?vue&type=script&lang=js&
 /* harmony default export */ var components_comment_listvue_type_script_lang_js_ = (comment_listvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(1);

// CONCATENATED MODULE: ./components/comment-list.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(206)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  components_comment_listvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  "4e1720b8",
  "849205e4"
  
)

/* harmony default export */ var comment_list = __webpack_exports__["default"] = (component.exports);

/* nuxt-component-imports */
installComponents(component, {NullData: __webpack_require__(143).default})


/***/ }),

/***/ 329:
/***/ (function(module, exports) {

module.exports = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACUAAAAkCAMAAAA5HAOUAAAAQlBMVEUAAAD/IDD/KDj/Kjr/LDz/KTn/Kzv/Kjr/Kzv/LDz/Kzv/Kzv/Kzv/LDz/Kzv/LDz/LDz/Kzv/Kzv/LDz/LDv/LDyPingBAAAAFXRSTlMAECAwQFBfYHCAj5+gr7C/wNDf7/B6g4n4AAAAvUlEQVQ4y8XUyRKDIBAEUBZlUYxs8/+/mmiMWtQwkFzS51cFtF0y9v9w3oE0gG4iCa/Illo3tTaQgT2Gvnl6q0S+YIEjC4EGODPUz4uXiviZQk0JbkmTEkVJao6AJM7qrM4kIJLM1TYV2a+Yp5E/CggUCp9KeK6jfPUmqyzfRzTW1FguFEu5WochR8yBGEafspgyXcr+ph5db/TEh0aU19o3VHb71oXLuNq6D/ocANcBuxcztviHSGu+/Kc9AXSSLqTq6c2LAAAAAElFTkSuQmCC"

/***/ }),

/***/ 330:
/***/ (function(module, exports) {

module.exports = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACUAAAAkCAMAAAA5HAOUAAAAS1BMVEUAAABQUFBQUFBVVVVUVFRTU1NTU1NVVVVUVFRUVFRUVFRVVVVVVVVUVFRVVVVUVFRUVFRVVVVVVVVVVVVVVVVUVFRUVFRVVVVVVVUmEHPwAAAAGHRSTlMAECAwQFBfYHCAj5CfoK+wv8DP0N/g7/AGrtdjAAABEUlEQVQ4y8WUy5aDIBBEeUQeUVTUwP3/L53FaJIR1MxsxhX2udBdRakQ//9I+QFkwV5CGkBfUSNty3gBOR5SZtz55IlGiIZ0qqBnEEKISH8C3chKCCFU5nbcb9kG8iz1nsrcE/P2NpPuRu1MMt0CEJ8HyAiwdOZpnUsAefA/zNR+yADJbW4/gqvard3wWG9Ck9SxbJXW+4pMhybKibiuZqYjamLeTpCZrg515FcbnfE1yJPfVTXV6FlodoVSqErF1lD29IQyDnFfimUwPqM87b7UlsH2tbn+WBpW1dL0vZGrO6E+qu4SQOrUsSAzAtHaCIymTvUJcvj+hkKG1JdUAGb7yr2doZxLOL8Ltfbul/+0Lw1XEXqaPu71AAAAAElFTkSuQmCC"

/***/ }),

/***/ 331:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_sass_resources_loader_lib_loader_js_ref_7_oneOf_1_4_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_id_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(245);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_sass_resources_loader_lib_loader_js_ref_7_oneOf_1_4_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_id_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_sass_resources_loader_lib_loader_js_ref_7_oneOf_1_4_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_id_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_sass_resources_loader_lib_loader_js_ref_7_oneOf_1_4_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_id_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_sass_resources_loader_lib_loader_js_ref_7_oneOf_1_4_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_id_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 332:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(3);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".goods-details{padding:16px 0 44px}.goods-details .goods-info .goods-swiper{width:400px;border-radius:4px}.goods-details .goods-info .goods-swiper .swiper{margin:10px 0;padding:0 25px;--swiper-navigation-size:15px;--swiper-navigation-color:#888}.goods-details .goods-info .goods-swiper .swiper .swiper-button-next,.goods-details .goods-info .goods-swiper .swiper .swiper-button-prev{top:0;width:25px;height:100%;margin-top:0;background-size:12px 22px}.goods-details .goods-info .goods-swiper .swiper .swiper-button-prev{left:0}.goods-details .goods-info .goods-swiper .swiper .swiper-button-next{right:0}.goods-details .goods-info .goods-swiper .swiper .swiper-item{cursor:pointer;height:66px;width:66px;border:2px solid transparent}.goods-details .goods-info .goods-swiper .swiper .swiper-item~.swiper-item{margin-left:10px}.goods-details .goods-info .goods-swiper .swiper .swiper-item.active{border-color:#ff2c3c}.goods-details .goods-info .goods-swiper .current-img{width:100%;height:400px}.goods-details .goods-info .info-wrap{min-height:486px;border-radius:4px;padding:20px}.goods-details .goods-info .info-wrap .name{font-size:20px}.goods-details .goods-info .info-wrap .price-wrap{background:#f2f2f2;background-size:cover;height:80px;padding:0 50px 0 20px;margin-bottom:26px}.goods-details .goods-info .info-wrap .price-wrap.seckill{background:#ff2c3c}.goods-details .goods-info .info-wrap .price-wrap.seckill .count-down .item{width:30px;height:30px;background:rgba(0,0,0,.5);text-align:center;line-height:30px;border-radius:4px}.goods-details .goods-info .info-wrap .spec-wrap .spec{align-items:flex-start}.goods-details .goods-info .info-wrap .spec-wrap .spec .spec-name{margin-right:20px;margin-top:6px;flex:none}.goods-details .goods-info .info-wrap .spec-wrap .spec .spec-item{padding:0 20px;line-height:32px;border:1px solid hsla(0,0%,89.8%,.89804);border-radius:2px;margin-right:10px;margin-bottom:10px;cursor:pointer}.goods-details .goods-info .info-wrap .spec-wrap .spec .spec-item.active{color:#ff2c3c;background-color:#ffeeef;border-color:currentColor}.goods-details .goods-info .info-wrap .goods-num{margin-bottom:30px}.goods-details .goods-info .info-wrap .goods-num .num{margin-right:20px}.goods-details .goods-info .info-wrap .goods-btns .btn{margin-right:14px;text-align:center;width:120px;font-size:16px}.goods-details .goods-info .info-wrap .goods-btns .btn.collection{width:146px;line-height:42px;border:1px solid hsla(0,0%,89.8%,.89804);background-color:#fff;border-radius:4px;cursor:pointer;color:#666}.goods-details .goods-info .info-wrap .goods-btns .btn.collection:hover{color:#ff2c3c}.goods-details .goods-info .info-wrap .goods-btns .btn.collection .start-icon{width:18.5px;height:18px}.goods-details .goods-info .shop{width:210px;padding:16px}.goods-details .goods-info .shop .logo-img{width:62px;height:62px;border-radius:50%;overflow:hidden}.goods-details .goods-info .shop .el-rate__icon{font-size:16px}.goods-details .details-wrap{align-items:stretch}.goods-details .details-wrap .details{padding:10px 0;overflow:hidden}.goods-details .details-wrap .details .rich-text{padding:0 10px;width:100%;overflow:hidden}.goods-details .details-wrap .details .rich-text img{width:100%;display:block}.goods-details .details-wrap .details .rich-text p{margin:0}.goods-details .details-wrap .details .evaluation .evaluation-hd{height:80px;margin:0 10px}.goods-details .details-wrap .details .evaluation .evaluation-hd .rate{height:60px;width:220px;border-right:1px solid #e5e5e5;padding-left:10px;margin-right:40px}.goods-details .details-wrap .details .evaluation .evaluation-tab{margin:16px 20px}.goods-details .details-wrap .details .evaluation .evaluation-tab .item{border-radius:2px;cursor:pointer;height:32px;padding:6px 20px;color:#666;background-color:#f2f2f2;margin-right:10px}.goods-details .details-wrap .details .evaluation .evaluation-tab .item.active{color:#fff;background-color:#ff2c3c}.goods-details .goods-like{width:210px}.goods-details .goods-like .title{border-bottom:1px solid hsla(0,0%,89.8%,.89804);height:45px}.goods-details .goods-like .goods-list .item{padding:10px;display:block}.goods-details .goods-like .goods-list .item .goods-img{width:190px;height:190px;margin-bottom:10px}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 369:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/goods_details/_id.vue?vue&type=template&id=f2d94ef8&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return (_vm.goodsDetails.id)?_c('div',{staticClass:"goods-details"},[_vm._ssrNode("<div class=\"goods-info flex col-stretch\">","</div>",[_vm._ssrNode("<div class=\"goods-swiper m-r-16 bg-white flex-col\">","</div>",[_c('el-image',{staticClass:"current-img",attrs:{"preview-src-list":_vm.goodsImage.map(function (item) { return item.uri; }),"src":_vm.goodsImage[_vm.swiperIndex].uri}}),_vm._ssrNode(" "),_c('client-only',[_c('swiper',{ref:"mySwiper",staticClass:"swiper",attrs:{"options":_vm.swiperOptions}},[_vm._l((_vm.goodsImage),function(item,index){return _c('swiper-slide',{key:index,class:{
                            'swiper-item': true,
                            active: index === _vm.swiperIndex,
                        }},[_c('div',{staticStyle:{"width":"100%","height":"100%"},on:{"mouseover":function($event){_vm.swiperIndex = index}}},[_c('el-image',{staticStyle:{"width":"100%","height":"100%"},attrs:{"src":item.uri}})],1)])}),_vm._v(" "),_c('div',{staticClass:"swiper-button-prev",attrs:{"slot":"button-prev"},slot:"button-prev"}),_vm._v(" "),_c('div',{staticClass:"swiper-button-next",attrs:{"slot":"button-next"},slot:"button-next"})],2)],1)],2),_vm._ssrNode(" "),_vm._ssrNode("<div class=\"info-wrap bg-white flex-1\">","</div>",[_vm._ssrNode("<div class=\"name weight-500 m-b-16\">"+_vm._ssrEscape("\n                "+_vm._s(_vm.goodsDetails.name)+"\n            ")+"</div> "),(_vm.activity.type == 1)?_vm._ssrNode("<div class=\"price-wrap flex row-between white seckill\">","</div>",[_vm._ssrNode("<div class=\"price flex\" style=\"align-items: baseline\">","</div>",[_vm._ssrNode("<div class=\"m-r-8\">价格</div> "),_vm._ssrNode("<div>","</div>",[_c('price-formate',{attrs:{"price":_vm.checkedGoods.price || _vm.goodsDetails.price,"subscript-size":16,"first-size":22,"second-size":16}})],1),_vm._ssrNode(" "),_vm._ssrNode("<div class=\"line-through m-l-8 flex\">","</div>",[_vm._ssrNode("\n                        原价\n                        "),_c('price-formate',{attrs:{"price":_vm.checkedGoods.market_price ||
                                _vm.goodsDetails.market_price}})],2)],2),_vm._ssrNode(" "),_vm._ssrNode("<div class=\"flex\">","</div>",[_vm._ssrNode("<div class=\"white m-r-16\">距离结束还有</div> "),_c('count-down',{attrs:{"time":_vm.countTime,"is-slot":true},on:{"change":_vm.onChangeDate}},[_c('div',{staticClass:"flex row-center count-down xxl"},[_c('div',{staticClass:"item white"},[_vm._v("\n                                "+_vm._s(_vm.timeData.hours)+"\n                            ")]),_vm._v(" "),_c('div',{staticClass:"white",staticStyle:{"margin":"0 4px"}},[_vm._v(":")]),_vm._v(" "),_c('div',{staticClass:"item white"},[_vm._v("\n                                "+_vm._s(_vm.timeData.minutes)+"\n                            ")]),_vm._v(" "),_c('div',{staticClass:"white",staticStyle:{"margin":"0 4px"}},[_vm._v(":")]),_vm._v(" "),_c('div',{staticClass:"item white"},[_vm._v("\n                                "+_vm._s(_vm.timeData.seconds)+"\n                            ")])])])],2)],2):_vm._ssrNode("<div class=\"price-wrap flex row-between lighter\">","</div>",[_vm._ssrNode("<div class=\"price flex\" style=\"align-items: baseline\">","</div>",[_vm._ssrNode("<div class=\"m-r-8\">价格</div> "),_vm._ssrNode("<div class=\"primary\">","</div>",[_c('price-formate',{attrs:{"price":_vm.checkedGoods.price || _vm.goodsDetails.price,"subscript-size":16,"first-size":22,"second-size":16}})],1),_vm._ssrNode(" "),_vm._ssrNode("<div class=\"line-through m-l-8 muted\">","</div>",[_c('price-formate',{attrs:{"price":_vm.checkedGoods.market_price ||
                                _vm.goodsDetails.market_price}})],1)],2),_vm._ssrNode(" <div class=\"flex\">"+((_vm.goodsDetails.stock !== true)?("<div style=\"margin-right: 60px\"><div class=\"m-b-8\">库存</div> <div>"+_vm._ssrEscape("\n                            "+_vm._s(_vm.checkedGoods.stock || _vm.goodsDetails.stock)+"\n                        ")+"</div></div>"):"<!---->")+" <div><div class=\"m-b-8\">销量</div> <div>"+_vm._ssrEscape(_vm._s(_vm.goodsDetails.sales_sum))+"</div></div></div>")],2),_vm._ssrNode(" <div class=\"spec-wrap\">"+(_vm._ssrList((_vm.goodsSpec),function(item,index){return ("<div class=\"spec flex m-b-16\"><div class=\"lighter spec-name\">"+_vm._ssrEscape(_vm._s(item.name))+"</div> <div class=\"spec-list flex flex-wrap\">"+(_vm._ssrList((item.spec_value),function(specitem,sindex){return ("<div"+(_vm._ssrClass(null,[
                                'spec-item lighter',
                                { active: specitem.checked } ]))+">"+_vm._ssrEscape("\n                            "+_vm._s(specitem.value)+"\n                        ")+"</div>")}))+"</div></div>")}))+"</div> "),_vm._ssrNode("<div class=\"goods-num flex\">","</div>",[_vm._ssrNode("<div class=\"num lighter\">数量</div> "),_c('number-box',{attrs:{"min":1,"max":_vm.checkedGoods.stock},model:{value:(_vm.goodsNum),callback:function ($$v) {_vm.goodsNum=$$v},expression:"goodsNum"}})],2),_vm._ssrNode(" "),_vm._ssrNode("<div class=\"goods-btns flex lg\">","</div>",[_c('el-button',{staticClass:"btn white",attrs:{"type":"primary"},on:{"click":_vm.onBuyNow}},[_vm._v("\n                    立即购买\n                ")]),_vm._ssrNode(" "),(_vm.activity.type != 1)?_c('el-button',{staticClass:"btn addcart",attrs:{"type":"primary","plain":""},on:{"click":_vm.onAddCart}},[_vm._v("\n                    加入购物车\n                ")]):_vm._e(),_vm._ssrNode(" <div class=\"btn collection flex row-center\"><img"+(_vm._ssrAttr("src",_vm.goodsDetails.is_collect
                                    ? __webpack_require__(329)
                                    : __webpack_require__(330)))+" class=\"start-icon m-r-8\"> <span>"+_vm._ssrEscape(_vm._s(_vm.goodsDetails.is_collect ? '取消收藏' : '收藏商品'))+"</span></div>")],2)],2),_vm._ssrNode(" "),_vm._ssrNode("<div class=\"shop m-l-16 bg-white\">","</div>",[_vm._ssrNode("<div class=\"shop-logo flex-col col-center\">","</div>",[_c('el-image',{staticClass:"logo-img",attrs:{"src":_vm.shop.logo}}),_vm._ssrNode(" "),_c('nuxt-link',{staticClass:"m-t-10",attrs:{"to":("/shop_street_detail?id=" + (_vm.shop.id))}},[(_vm.shop.type == 1)?_c('el-tag',{attrs:{"size":"mini"}},[_vm._v("自营")]):_vm._e(),_vm._v(" "),_c('span',{staticClass:"weight-500"},[_vm._v(_vm._s(_vm.shop.name))])],1),_vm._ssrNode(" <div class=\"xs muted m-t-10 line-5\">"+_vm._ssrEscape("\n                "+_vm._s(_vm.shop.intro)+"\n                ")+"</div>")],2),_vm._ssrNode(" <div class=\"flex m-t-20\"><div class=\"flex-1 text-center\"><div class=\"xxl m-b-10\">"+_vm._ssrEscape(_vm._s(_vm.shop.goods_on_sale))+"</div> <div>全部商品</div></div> <div class=\"flex-1 text-center\"><div class=\"xxl m-b-10\">"+_vm._ssrEscape(_vm._s(_vm.shop.follow_num))+"</div> <div>关注人数</div></div></div> "),_c('el-divider'),_vm._ssrNode(" "),_vm._ssrNode("<div class=\"flex xs m-b-16\">","</div>",[_vm._ssrNode("<div class=\"m-r-12\">店铺星级</div> "),_vm._ssrNode("<div class=\"m-t-5\">","</div>",[_c('el-rate',{attrs:{"disabled":""},model:{value:(_vm.shop.star),callback:function ($$v) {_vm.$set(_vm.shop, "star", $$v)},expression:"shop.star"}})],1)],2),_vm._ssrNode(" <div class=\"flex xs m-b-16\"><div class=\"m-r-12\">店铺评分</div> <div>"+_vm._ssrEscape(_vm._s(_vm.shop.score)+"分")+"</div></div> "),_vm._ssrNode("<div>","</div>",[_c('el-button',{attrs:{"type":"primary","size":"mini"},on:{"click":function($event){return _vm.$router.push(("/shop_street_detail?id=" + (_vm.shop.id)))}}},[_vm._v("进入店铺")]),_vm._ssrNode(" "),_c('el-button',{attrs:{"size":"mini"},on:{"click":_vm.changeShopFollow}},[_vm._v(_vm._s(_vm.shop.shop_follow_status == 1 ? '已关注' : '关注店铺'))])],2),_vm._ssrNode(" "),_c('el-popover',{attrs:{"placement":"bottom","width":"200","trigger":"hover"}},[_c('div',[_c('el-image',{staticStyle:{"width":"100%"},attrs:{"src":_vm.shop.customer_image}})],1),_vm._v(" "),_c('div',{staticClass:"xs lighter text-center m-t-30",attrs:{"slot":"reference"},slot:"reference"},[_c('i',{staticClass:"el-icon-chat-dot-round nr"}),_vm._v(" "),_c('span',[_vm._v("联系客服")])])])],2)],2),_vm._ssrNode(" "),_vm._ssrNode("<div class=\"details-wrap flex m-t-16\">","</div>",[_vm._ssrNode("<div class=\"details bg-white flex-1\">","</div>",[_c('el-tabs',{model:{value:(_vm.active),callback:function ($$v) {_vm.active=$$v},expression:"active"}},[_c('el-tab-pane',{attrs:{"label":"商品详情"}},[_c('div',{staticClass:"rich-text",domProps:{"innerHTML":_vm._s(_vm.goodsDetails.content)}})]),_vm._v(" "),_c('el-tab-pane',{attrs:{"label":"商品评价"}},[_c('div',{staticClass:"evaluation"},[_c('div',{staticClass:"evaluation-hd flex"},[_c('div',{staticClass:"rate flex"},[_c('div',{staticClass:"lighter m-r-8"},[_vm._v("好评率")]),_vm._v(" "),_c('div',{staticClass:"primary",staticStyle:{"font-size":"30px"}},[_vm._v("\n                                    "+_vm._s(_vm.goodsDetails.comment.percent)+"\n                                ")])]),_vm._v(" "),_c('div',{staticClass:"score flex"},[_c('span',{staticClass:"m-r-8 lighter"},[_vm._v("评分")]),_vm._v(" "),_c('el-rate',{attrs:{"value":_vm.goodsDetails.comment.goods_comment,"disabled":"","allow-half":""}})],1)]),_vm._v(" "),_c('div',{staticClass:"evaluation-tab flex"},_vm._l((_vm.comment.comment),function(item,index){return _c('div',{key:index,class:[
                                    'item',
                                    { active: _vm.commentActive == item.id } ],on:{"click":function($event){_vm.commentActive = item.id}}},[_vm._v("\n                                "+_vm._s(item.name)+"("+_vm._s(item.count)+")\n                            ")])}),0)]),_vm._v(" "),_c('div',[_vm._l((_vm.comment.comment),function(item,index){return [(_vm.commentActive == item.id)?_c('comment-list',{key:index,attrs:{"goods-id":_vm.id,"type":item.id}}):_vm._e()]})],2)])],1)],1),_vm._ssrNode(" "),(_vm.shop.goods_list.length)?_vm._ssrNode("<div class=\"goods-like m-l-16\">","</div>",[_vm._ssrNode("<div class=\"title bg-white flex p-l-15\">店铺推荐</div> "),_vm._ssrNode("<div class=\"goods-list\">","</div>",[_vm._l((_vm.shop.goods_list),function(item,index){return [(index < 5)?_c('nuxt-link',{key:index,staticClass:"item bg-white m-b-16",attrs:{"to":("/goods_details/" + (item.id))}},[_c('el-image',{staticClass:"goods-img",attrs:{"src":item.image}}),_vm._v(" "),_c('div',{staticClass:"goods-name line-2"},[_vm._v("\n                            "+_vm._s(item.name)+"\n                        ")]),_vm._v(" "),_c('div',{staticClass:"price flex m-t-8"},[_c('div',{staticClass:"primary m-r-8"},[_c('price-formate',{attrs:{"price":item.min_price,"first-size":16}})],1),_vm._v(" "),_c('div',{staticClass:"muted sm line-through"},[_c('price-formate',{attrs:{"price":item.market_price}})],1)])],1):_vm._e()]})],2)],2):_vm._e()],2)],2):_vm._e()}
var staticRenderFns = []


// CONCATENATED MODULE: ./pages/goods_details/_id.vue?vue&type=template&id=f2d94ef8&

// EXTERNAL MODULE: external "vuex"
var external_vuex_ = __webpack_require__(2);

// EXTERNAL MODULE: ./node_modules/element-ui/lib/element-ui.common.js
var element_ui_common = __webpack_require__(14);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/goods_details/_id.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//


/* harmony default export */ var _idvue_type_script_lang_js_ = ({
  head() {
    return {
      title: this.$store.getters.headTitle,
      link: [{
        rel: 'icon',
        type: 'image/x-icon',
        href: this.$store.getters.favicon
      }]
    };
  },

  async asyncData({
    params,
    $get,
    app
  }) {
    const {
      data,
      code,
      msg
    } = await $get('goods/getGoodsDetail', {
      params: {
        goods_id: params.id
      }
    });

    if (code == 0) {
      setTimeout(() => app.router.back(), 1500);
    }

    return {
      goodsDetails: data,
      goodsImage: data.goods_image,
      activity: data.activity,
      shop: data.shop
    };
  },

  data() {
    return {
      goodsDetails: {},
      goodsImage: [],
      activity: {},
      shop: {
        goods_list: []
      },
      swiperOptions: {
        pagination: {
          el: '.swiper-pagination',
          clickable: true
        },
        navigation: {
          nextEl: '.swiper-button-next',
          prevEl: '.swiper-button-prev'
        },
        preventClicks: true,
        slidesPerView: 'auto'
      },
      active: '0',
      commentActive: 0,
      swiperIndex: 0,
      checkedGoods: {},
      comment: {},
      goodsNum: 1,
      goodsSpec: [],
      id: '',
      timeData: {}
    };
  },

  created() {
    this.id = this.$route.params.id;
    this.getComment(this.id);
  },

  methods: { ...Object(external_vuex_["mapActions"])(['getPublicData']),

    onClickSlide(e) {
      this.swiperIndex = e;
    },

    onChoseSpecItem(id, specid) {
      const {
        goodsSpec
      } = this;
      goodsSpec.forEach(item => {
        if (item.spec_value && item.id == id) {
          item.spec_value.forEach(specitem => {
            specitem.checked = 0;

            if (specitem.id == specid) {
              specitem.checked = 1;
            }
          });
        }
      });
      this.goodsSpec = [...goodsSpec];
    },

    async onAddCart() {
      const {
        goodsNum,
        checkedGoods: {
          id
        }
      } = this;
      const {
        code,
        data,
        msg
      } = await this.$post('cart/add', {
        item_id: id,
        goods_num: goodsNum
      });

      if (code == 1) {
        this.getPublicData();
        this.$message({
          message: msg,
          type: 'success'
        });
      }
    },

    async changeShopFollow() {
      const {
        code,
        msg
      } = await this.$post('shop_follow/changeStatus', {
        shop_id: this.shop.id
      });

      if (code == 1) {
        this.$message({
          message: msg,
          type: 'success'
        });
        this.getGoodsDetail();
      }
    },

    onBuyNow() {
      const {
        goodsNum,
        checkedGoods: {
          id
        }
      } = this;
      const goods = [{
        item_id: id,
        num: goodsNum,
        goods_id: this.id,
        shop_id: this.shop.id
      }];
      this.$router.push({
        path: '/confirm_order',
        query: {
          data: encodeURIComponent(JSON.stringify({
            goods,
            type: 'buy'
          }))
        }
      });
    },

    async getGoodsDetail() {
      const {
        data,
        code
      } = await this.$get('goods/getGoodsDetail', {
        params: {
          goods_id: this.id
        }
      });

      if (code == 1) {
        this.goodsDetails = data;
        this.shop = data.shop;
      }
    },

    async onCollectionGoods() {
      const {
        data,
        code,
        msg
      } = await this.$post('goods_collect/changeStatus', {
        goods_id: this.id
      });

      if (code == 1) {
        this.$message({
          message: msg,
          type: 'success'
        });
        this.getGoodsDetail();
      }
    },

    async getComment() {
      const {
        data,
        code
      } = await this.$get('/goods_comment/category', {
        params: {
          goods_id: this.id
        }
      });

      if (code == 1) {
        this.comment = data;
        this.commentActive = data.comment[0].id;
      }
    },

    onChangeDate(e) {
      let timeData = {};

      for (let prop in e) {
        if (prop !== 'milliseconds') timeData[prop] = ('0' + e[prop]).slice(-2);
      }

      this.timeData = timeData;
    }

  },
  watch: {
    goodsSpec: {
      immediate: true,

      handler(value) {
        const {
          goods_item
        } = this.goodsDetails;
        let keyArr = [];
        value.forEach(item => {
          if (item.spec_value) {
            item.spec_value.forEach(specitem => {
              if (specitem.checked) {
                keyArr.push(specitem.id);
              }
            });
          }
        });
        if (!keyArr.length) return;
        let key = keyArr.join(',');
        let index = goods_item.findIndex(item => {
          return item.spec_value_ids == key;
        });

        if (index == -1) {
          index = 0;
        }

        this.checkedGoods = goods_item[index];
        console.log(this.checkedGoods);
      }

    },
    goodsDetails: {
      immediate: true,

      handler(value) {
        if (!value.goods_spec) return;
        value.goods_spec.forEach(item => {
          item.spec_value.forEach((specitem, specindex) => {
            if (specindex == 0) {
              specitem.checked = 1;
            } else {
              specitem.checked = 0;
            }
          });
        });
        this.goodsSpec = [...value.goods_spec];
      }

    }
  },
  computed: {
    countTime() {
      const end_time = this.activity.end_time;
      return end_time ? end_time - Date.now() / 1000 : 0;
    }

  }
});
// CONCATENATED MODULE: ./pages/goods_details/_id.vue?vue&type=script&lang=js&
 /* harmony default export */ var goods_details_idvue_type_script_lang_js_ = (_idvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(1);

// CONCATENATED MODULE: ./pages/goods_details/_id.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(331)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  goods_details_idvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  null,
  "b793ee3c"
  
)

/* harmony default export */ var _id = __webpack_exports__["default"] = (component.exports);

/* nuxt-component-imports */
installComponents(component, {PriceFormate: __webpack_require__(137).default,CountDown: __webpack_require__(144).default,NumberBox: __webpack_require__(192).default,CommentList: __webpack_require__(252).default})


/***/ })

};;
//# sourceMappingURL=_id.js.map