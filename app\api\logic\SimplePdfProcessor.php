<?php
namespace app\api\logic;

use app\common\basics\Logic;
use Exception;
use think\facade\Log;

/**
 * 简单PDF处理器
 * 用于在PDF文件中添加签名和日期
 * Class SimplePdfProcessor
 * @package app\api\logic
 */
class SimplePdfProcessor extends Logic
{
    /**
     * @var string PDF文件路径
     */
    protected $pdfPath;
    
    /**
     * @var string 签名图片路径
     */
    protected $signaturePath;
    
    /**
     * @var string 输出PDF路径
     */
    protected $outputPath;
    
    /**
     * @var array 签名位置坐标
     */
    protected $signaturePosition = [
        'x' => 400,  // 默认X坐标
        'y' => 680,  // 默认Y坐标
        'width' => 80, // 默认宽度
        'height' => 40 // 默认高度
    ];
    
    /**
     * @var array 日期位置坐标
     */
    protected $datePosition = [
        'x' => 400,  // 默认X坐标
        'y' => 720,  // 默认Y坐标
    ];
    
    /**
     * 构造函数
     * @param string $pdfPath PDF文件路径
     */
    public function __construct($pdfPath = '')
    {
        if (!empty($pdfPath)) {
            $this->setPdfPath($pdfPath);
        }
    }
    
    /**
     * 设置PDF文件路径
     * @param string $pdfPath
     * @return $this
     * @throws Exception
     */
    public function setPdfPath($pdfPath)
    {
        if (!file_exists($pdfPath)) {
            throw new Exception('PDF文件不存在: ' . $pdfPath);
        }
        $this->pdfPath = $pdfPath;
        return $this;
    }
    
    /**
     * 设置签名图片路径
     * @param string $signaturePath
     * @return $this
     * @throws Exception
     */
    public function setSignaturePath($signaturePath)
    {
        if (!file_exists($signaturePath)) {
            throw new Exception('签名图片不存在: ' . $signaturePath);
        }
        $this->signaturePath = $signaturePath;
        return $this;
    }
    
    /**
     * 设置输出PDF路径
     * @param string $outputPath
     * @return $this
     */
    public function setOutputPath($outputPath)
    {
        $this->outputPath = $outputPath;
        return $this;
    }
    
    /**
     * 设置签名位置
     * @param float $x X坐标
     * @param float $y Y坐标
     * @param float|null $width 宽度
     * @param float|null $height 高度
     * @return $this
     */
    public function setSignaturePosition($x, $y, $width = null, $height = null)
    {
        $this->signaturePosition['x'] = $x;
        $this->signaturePosition['y'] = $y;
        
        if ($width !== null) {
            $this->signaturePosition['width'] = $width;
        }
        
        if ($height !== null) {
            $this->signaturePosition['height'] = $height;
        }
        
        return $this;
    }
    
    /**
     * 设置日期位置
     * @param float $x X坐标
     * @param float $y Y坐标
     * @return $this
     */
    public function setDatePosition($x, $y)
    {
        $this->datePosition['x'] = $x;
        $this->datePosition['y'] = $y;
        return $this;
    }
    
    /**
     * 处理PDF文件，添加签名和日期
     * @param int $pageNumber 页码，从1开始
     * @param string $dateFormat 日期格式，默认为Y-m-d
     * @return string 处理后的PDF文件路径
     * @throws Exception
     */
    public function process($pageNumber = 1, $dateFormat = 'Y-m-d')
    {
        // 检查必要参数
        if (empty($this->pdfPath)) {
            throw new Exception('未设置PDF文件路径');
        }
        
        if (empty($this->signaturePath)) {
            throw new Exception('未设置签名图片路径');
        }
        
        if (empty($this->outputPath)) {
            // 如果未设置输出路径，则生成一个临时路径
            $pathInfo = pathinfo($this->pdfPath);
            $this->outputPath = $pathInfo['dirname'] . '/' . $pathInfo['filename'] . '_signed.' . $pathInfo['extension'];
        }
        
        try {
            // 创建一个HTML文件，包含PDF、签名图片和日期
            $htmlPath = dirname($this->outputPath) . '/signed_' . uniqid() . '.html';
            
            // 获取PDF文件的相对路径
            $pdfRelativePath = str_replace(public_path(), '', $this->pdfPath);
            
            // 复制签名图片到public目录
            $signatureOutputDir = public_path() . 'uploads/signatures';
            if (!is_dir($signatureOutputDir)) {
                mkdir($signatureOutputDir, 0777, true);
            }
            $signatureOutputPath = $signatureOutputDir . '/' . uniqid() . '_' . basename($this->signaturePath);
            copy($this->signaturePath, $signatureOutputPath);
            
            // 获取签名图片的相对路径
            $signatureRelativePath = str_replace(public_path(), '', $signatureOutputPath);
            
            // 创建HTML内容
            $html = '<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>签名和日期</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            position: relative;
        }
        .pdf-container {
            position: relative;
            width: 100%;
            height: 100vh;
        }
        .pdf-frame {
            width: 100%;
            height: 100%;
            border: none;
        }
        .signature {
            position: absolute;
            left: ' . $this->signaturePosition['x'] . 'px;
            top: ' . $this->signaturePosition['y'] . 'px;
            width: ' . $this->signaturePosition['width'] . 'px;
            height: ' . $this->signaturePosition['height'] . 'px;
            z-index: 10;
        }
        .date {
            position: absolute;
            left: ' . $this->datePosition['x'] . 'px;
            top: ' . $this->datePosition['y'] . 'px;
            font-size: 14px;
            font-family: Arial, sans-serif;
            z-index: 10;
        }
    </style>
</head>
<body>
    <div class="pdf-container">
        <iframe class="pdf-frame" src="' . $pdfRelativePath . '"></iframe>
        <img class="signature" src="' . $signatureRelativePath . '" alt="签名">
        <div class="date">' . date($dateFormat) . '</div>
    </div>
</body>
</html>';
            
            // 保存HTML文件
            $htmlOutputPath = public_path() . 'uploads/pdf/' . basename($htmlPath);
            file_put_contents($htmlOutputPath, $html);
            
            // 设置输出路径为HTML文件路径
            $this->outputPath = $htmlOutputPath;
            
            return $this->outputPath;
        } catch (Exception $e) {
            throw new Exception('处理PDF文件时出错: ' . $e->getMessage());
        }
    }
}
