{layout name="layout1" /}
<link rel="stylesheet" href="/static/plug/layui-admin/dist/layuiadmin/layui/css/layui.css?v={$front_version}" media="all">
<link rel="stylesheet" href="/static/plug/layui-admin/dist/layuiadmin/style/admin.css?v={$front_version}" media="all">
<link rel="stylesheet" href="/static/plug/layui-admin/dist/layuiadmin/style/like.css?v={$front_version}" media="all">
<script src="__PUBLIC__/static/lib/layui/layui.js"></script>
<script src="__PUBLIC__/static/admin/js/app.js"></script>
<style>
    
    .header-font{
        font-size: 20px
    }
    .layui-card .data-all {
        display: flex;
        justify-content: space-between;
        margin-top: 20px;
    }
    .layui-card  .text-style {
        font-size: 16px;
    }
    .header-time{
        margin-left: 20px
    }
    .layadmin-shortcut .shortcut-list  {
        padding: 30px 0;
        display: flex;
        flex-wrap: wrap;
    }
    .layadmin-shortcut .shortcut-list li {
        min-width: 100px;
        margin-bottom: 20px;
        width: 12.5%;
    }
 
    .layadmin-shortcut .shortcut-list .icon{
        width: 62px;
        height: 62px;
        margin-bottom: 5px;
    }

    .tips {
        float: right;
        padding: 0 10px;
        margin-top: 10px;
        height: 20px;
        line-height: 20px;
        font-size: 12px;
        color: #3A91FB;
        border-radius: 30px;
        border: 1px #3A91FB solid;
        background: #eaf3ff;
    }
    .laytable-cell-1-0-1,.laytable-cell-2-0-1 {
        width: 280px;
        height: auto;
    }

    /* 商家等级升级Banner样式 */
    .tier-upgrade-banner {
        transition: all 0.3s ease;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .tier-upgrade-banner:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(0,0,0,0.15);
    }

    /* 新增样式 */
    .layadmin-big-font {
        font-size: 36px;
        font-weight: 700;
        color: #333;
    }
    .layui-card-body .data-all {
        margin-top: 15px;
        padding-top: 15px;
        border-top: 1px solid #f0f0f0;
    }
    .green {
        color: #52c41a;
    }
    .red {
        color: #f5222d;
    }
    .layadmin-card-list p {
        color: #8c8c8c;
    }
    .layadmin-shortcut .shortcut-list li a {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        transition: all .3s;
    }
    .layadmin-shortcut .shortcut-list li a:hover {
        transform: translateY(-5px);
        color: #009688;
    }
    /* 资料完整性提示弹窗 */
    .incomplete-info-popup-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.6);
        z-index: 9998;
        display: none; /* 初始隐藏 */
    }
    .body-popup-open {
        overflow: hidden;
    }
    .incomplete-info-popup {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background-color: #fff;
        padding: 40px;
        border-radius: 12px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        z-index: 9999;
        width: 600px;
        max-width: 90%;
        text-align: center;
        display: none; /* 初始隐藏 */
        border-top: 5px solid #FF5722;
    }
    .incomplete-info-popup h3 {
        font-size: 24px;
        font-weight: 700;
        color: #333;
        margin-bottom: 10px;
    }
    .incomplete-info-popup p {
        font-size: 16px;
        color: #666;
        margin-bottom: 25px;
    }
    .incomplete-info-popup .popup-buttons {
        display: flex;
        justify-content: center;
        gap: 20px;
    }
    .incomplete-info-popup .popup-buttons .layui-btn {
        width: 120px;
    }
</style>
<!-- 资料完整性提示弹窗 -->
{if !empty($incomplete_items)}
<div class="incomplete-info-popup-overlay" id="incomplete-info-overlay" style="display: block;"></div>
<div class="incomplete-info-popup" id="incomplete-info-popup" style="display: block;">
    <h3>重要提示</h3>
    <p>您的店铺资料尚有以下几项未完善，请尽快处理！</p>
    <div style="text-align: left; margin: 0 auto 30px auto; padding: 15px; background-color: #f8f8f8; border-radius: 8px;">
        <ul style="list-style-type: none; padding-left: 0;">
            {foreach $incomplete_items as $item}
                <li style="color: #333; font-size: 15px; padding: 8px 0; border-bottom: 1px solid #eee;">
                    <i class="layui-icon layui-icon-close-fill" style="color: #FF5722; margin-right: 10px;"></i>{$item}
                </li>
            {/foreach}
        </ul>
    </div>
    <div class="popup-buttons">
        <button class="layui-btn layui-btn-primary" onclick="openPage('{:url(\'goods.goods/lists\')}', '7', '商品管理')">添加商品</button>
        <button class="layui-btn" onclick="openPage('{:url(\'Store/index\')}', '10', '商家信息')">立即完善</button>
    </div>
</div>
<script>
    // When the popup is shown, prevent background scrolling
    document.body.classList.add('body-popup-open');
</script>
{/if}

<div class="layui-fluid" style="padding: 20px; background-color: #f4f6f8;">
    <!-- 商家等级升级Banner -->
    {if condition="$banner_config.show_banner"}
    <div class="layui-card tier-upgrade-banner" style="margin-bottom: 20px; cursor: pointer;" onclick="showTierUpgradeModal()">
        <div class="layui-card-body" style="padding: 0; position: relative; overflow: hidden; border-radius: 8px;">
            {if condition="$banner_config.banner_image"}
            <img src="{$banner_config.banner_image}" alt="升级Banner" style="width: 100%; height: 120px; object-fit: cover;">
            {else/}
            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); height: 120px; display: flex; align-items: center; justify-content: center; color: white;">
                <div style="text-align: center;">
                    <h3 style="margin: 0; font-size: 24px; font-weight: bold;">{$banner_config.banner_title}</h3>
                    <p style="margin: 8px 0 0 0; font-size: 14px; opacity: 0.9;">{$banner_config.banner_desc}</p>
                </div>
            </div>
            {/if}
        </div>
    </div>
    {/if}

    <div class="layui-card" style="margin-bottom: 20px;">
        <div class="layui-card-header">
            <i class="layui-icon layui-icon-chart-screen" style="margin-right: 8px;"></i>运营数据
            <span class="header-time" style="font-size: 14px; color: #999; font-weight: normal;">更新时间：{$res.time}</span>
        </div>
    </div>

    <div class="layui-row layui-col-space15">
        <div class="layui-col-md3 layui-col-sm6">
            <div class="layui-card">
                <div class="layui-card-header">
                    成交订单 (笔)
                    <span class="layui-badge layui-bg-blue pull-right">今日</span>
                </div>
                <div class="layui-card-body layuiadmin-card-list">
                    <p class="layuiadmin-big-font">{$res.data.order_num.today}</p>
                    <p>昨日：<span class="text-style">{$res.data.order_num.yesterday}</span>
                        <span class="pull-right">
                        {if ($res.data.order_num.change_red > 0) }
                        <span class="green"><i class="layui-icon layui-icon-down"></i> {$res.data.order_num.change_red}</span>
                        {else/}
                        <span class="red"><i class="layui-icon layui-icon-up"></i> {$res.data.order_num.change_add}</span>
                        {/if}
                        </span>
                    </p>
                    <div class="data-all">
                        <span>累计成交订单</span>
                        <span class="text-style">{$res.data.order_num.all_num}</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-col-md3 layui-col-sm6">
            <div class="layui-card">
                <div class="layui-card-header">
                    营业额 (元)
                    <span class="layui-badge layui-bg-cyan pull-right">今日</span>
                </div>
                <div class="layui-card-body layuiadmin-card-list">
                    <p class="layuiadmin-big-font">{$res.data.order_price.today}</p>
                    <p>昨日：<span class="text-style">{$res.data.order_price.yesterday}</span>
                        <span class="pull-right">
                        {if ($res.data.order_price.change_red > 0) }
                        <span class="green"><i class="layui-icon layui-icon-down"></i> {$res.data.order_price.change_red}</span>
                        {else/}
                        <span class="red"><i class="layui-icon layui-icon-up"></i> {$res.data.order_price.change_add}</span>
                        {/if}
                        </span>
                    </p>
                    <div class="data-all">
                        <span>累计营业额</span>
                        <span class="text-style">{$res.data.order_price.all_price}</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-col-md3 layui-col-sm6">
            <div class="layui-card">
                <div class="layui-card-header">
                    进店人数 (人)
                    <span class="layui-badge layui-bg-green pull-right">今日</span>
                </div>
                <div class="layui-card-body layuiadmin-card-list">
                    <p class="layuiadmin-big-font">{$res.data.add_user_num.today}</p>
                    <p>昨日：<span class="text-style">{$res.data.add_user_num.yesterday}</span>
                        <span class="pull-right">
                        {if ($res.data.add_user_num.change_red > 0) }
                        <span class="green"><i class="layui-icon layui-icon-down"></i> {$res.data.add_user_num.change_red}</span>
                        {else/}
                        <span class="red"><i class="layui-icon layui-icon-up"></i> {$res.data.add_user_num.change_add}</span>
                        {/if}
                        </span>
                    </p>
                    <div class="data-all">
                        <span>累计进店人数</span>
                        <span class="text-style">{$res.data.add_user_num.all_num}</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-col-md3 layui-col-sm6">
            <div class="layui-card">
                <div class="layui-card-header">
                    商品浏览人数 (人)
                    <span class="layui-badge layui-bg-orange pull-right">今日</span>
                </div>
                <div class="layui-card-body layuiadmin-card-list">
                    <p class="layuiadmin-big-font">{$res.data.visit_user_num.today}</p>
                    <p>昨日：<span class="text-style">{$res.data.visit_user_num.yesterday}</span>
                        <span class="pull-right">
                        {if ($res.data.visit_user_num.change_red > 0) }
                        <span class="green"><i class="layui-icon layui-icon-down"></i> {$res.data.visit_user_num.change_red}</span>
                        {else/}
                        <span class="red"><i class="layui-icon layui-icon-up"></i> {$res.data.visit_user_num.change_add}</span>
                        {/if}
                        </span>
                    </p>
                    <div class="data-all">
                        <span>累计商品浏览人数</span>
                        <span class="text-style">{$res.data.visit_user_num.all_num}</span>
                    </div>
                </div>
            </div>
        </div>

        <!--快捷功能-->
        <div class="layui-col-sm12" style="display:none;">
            <div class="layui-card">
                <div class="layui-card-header">快捷功能</div>
                <div class="layui-card-body">
                    <div class="layadmin-shortcut">
                        <ul class="shortcut-list">
                            <li class="shop-item">
                                <a lay-href="{:url('goods.goods/lists')}" data-id="29">
                                    <img class="icon" src="__PUBLIC__/static/common/image/workbench/goods.png" alt="">
                                    <cite>商品</cite>
                                </a>
                            </li>
                            <li class="shop-item">
                                <a lay-href="{:url('order.order/lists')}" data-id="37">
                                    <img class="icon" src="__PUBLIC__/static/common/image/workbench/order.png" alt="">
                                    <cite>订单</cite>
                                </a>
                            </li>
                            <li class="shop-item">
                                <a lay-href="{:url('user.user/lists')}" data-id="33">
                                    <img class="icon" src="__PUBLIC__/static/common/image/workbench/user.png" alt="">
                                    <cite>会员</cite>
                                </a>
                            </li>
                            <li class="shop-item">
                                <a lay-href="{:url('coupon.shop_coupon/lists')}" data-id="39">
                                    <img class="icon" src="__PUBLIC__/static/common/image/workbench/coupon.png" alt="">
                                    <cite>优惠券</cite>
                                </a>
                            </li>
                            <li class="shop-item">
                                <a lay-href="{:url('seckill.seckill_goods/lists')}" data-id="39">
                                    <img class="icon" src="__PUBLIC__/static/common/image/workbench/seckill.png" alt="">
                                    <cite>限时秒杀</cite>
                                </a>
                            </li>
                            <li class="shop-item">
                                <a lay-href="{:url('distribution.member/index')}" data-id="85">
                                    <img class="icon" src="__PUBLIC__/static/common/image/workbench/distribution.png" alt="">
                                    <cite>分销</cite>
                                </a>
                            </li>
                            <li class="shop-item">
                                <a lay-href="{:url('content.help/lists')}" data-id="65">
                                    <img class="icon" src="__PUBLIC__/static/common/image/workbench/content.png" alt="">
                                    <cite>内容</cite>
                                </a>
                            </li>
                            <li class="shop-item">
                                <a lay-href="{:url('finance.finance/center')}" data-id="81">
                                    <img class="icon" src="__PUBLIC__/static/common/image/workbench/finance.png" alt="">
                                    <cite>财务</cite>
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- 销冠商品 -->
        <div class="layui-col-md6">
            <div class="layui-card">
                <div class="layui-card-header"><i class="layui-icon layui-icon-fire" style="margin-right: 8px;"></i>销冠商品 (前5名)</div>
                <div class="layui-card-body">
                    <table class="layui-table" id="goods-lists" lay-filter="goods-lists"></table>
                </div>
            </div>
        </div>

        <!-- 人气商品 -->
        <div class="layui-col-md6">
            <div class="layui-card">
                <div class="layui-card-header"><i class="layui-icon layui-icon-star" style="margin-right: 8px;"></i>人气商品 (前5名)</div>
                <div class="layui-card-body">
                    <table class="layui-table" id="goods-lists2" lay-filter="goods-lists2"></table>
                </div>
            </div>
        </div>

        <script type="text/html" id="goods-info-tpl">
            <div style="display: flex; align-items: center;">
                <img src="{{ d.image }}" style="width: 48px; height: 48px; border-radius: 4px; margin-right: 10px;">
                <div style="line-height: 1.4;">
                    <p style="white-space: normal; color: #333;">{{ d.name }}</p>
                </div>
            </div>
        </script>

        <!--表格-->
        <div class="layui-col-sm12">
            <div class="layui-card">
                <div class="layui-card-body">
                    <div class="layui-row">
                        近15天营业额（元）
                        <div class="layadmin-dataview">
                            <div id="sale-charts" style="width: 100%;height: 100%">
                                <div><i class="layui-icon layui-icon-loading1 layadmin-loading"></i></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>


        <div class="layui-col-sm12">
            <div class="layui-card">
                <div class="layui-card-body">
                    <div class="layui-row">
                        近15天进店人数（人）
                        <div class="layadmin-dataview">
                            <div id="user-charts" style="width: 100%;height: 100%;">
                                <div><i class="layui-icon layui-icon-loading1 layadmin-loading"></i></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>
</div>
<footer class="info_footer">
    {$company_name}&nbsp;&nbsp;|&nbsp;&nbsp;版本号：{$version}
    <br><br>
</footer>
<script>
    /**
     * 打开新 Tab 页面 - 全局函数
     * @param url
     * @param id
     * @param name
     */
    // 页面跳转函数
    function openPage(url, id, title) {
        console.log("[stat.html] openPage called:", url, id, title);

        // 调用父窗口的openTabsPage函数
        if (window.parent && window.parent.openTabsPage) {
            console.log("[stat.html] 调用父窗口的openTabsPage函数");
            window.parent.openTabsPage(url, title, id);
        } else {
            console.error("[stat.html] 父窗口或openTabsPage函数未找到");
            // 如果无法找到父窗口函数，尝试直接跳转
            window.location.href = url;
        }
    }

    layui.config({
        version:"{$front_version}",
        base: '/static/lib/'
    }).extend({
        echarts: 'echarts/echarts',
        echartsTheme: 'echarts/echartsTheme',
    }).use(['jquery', 'echarts','form','element', 'echartsTheme'], function () {
        var $ = layui.$
            ,echarts = layui.echarts;

        let bgColor = "#fff";
        let color = [
            "#009688", // A more modern green
            "#1E9FFF", // A vibrant blue
            "#FFB800", // A warm yellow
            "#FF5722", // A striking orange
            "#5FB878",
            "#393D49"
        ];
        const hexToRgba = (hex, opacity) => {
            let rgbaColor = "";
            let reg = /^#[\da-f]{6}$/i;
            if (reg.test(hex)) {
                rgbaColor = `rgba(${parseInt("0x" + hex.slice(1, 3))},${parseInt(
                    "0x" + hex.slice(3, 5)
                )},${parseInt("0x" + hex.slice(5, 7))},${opacity})`;
            }
            return rgbaColor;
        }



        like.ajax({
            url: '{:url("index/stat")}',
            type: "get",
            success: function (res) {
                var dates = res.data.dates,
                    echarts_order_amount  = res.data.echarts_order_amount,
                    echarts_user_visit = res.data.echarts_user_visit;

                var sale_option = {
                    backgroundColor: bgColor,
                    color: color,
                    tooltip: {
                        trigger: "axis",
                        axisPointer: {
                            type: 'cross',
                            label: {
                                backgroundColor: '#6a7985'
                            }
                        }
                    },
                    grid: {
                        left: '3%',
                        right: '4%',
                        bottom: '3%',
                        containLabel: true
                    },
                    xAxis: [{
                        type: "category",
                        boundaryGap: false,
                        axisLabel: {
                            formatter: '{value}',
                            textStyle: {
                                color: "#333"
                            }
                        },
                        axisLine: {
                            lineStyle: {
                                color: "#D9D9D9"
                            }
                        },
                        data: dates
                    }],
                    yAxis: [{
                        type: "value",
                        name: '营业额',
                        axisLabel: {
                            textStyle: {
                                color: "#666"
                            }
                        },
                        nameTextStyle: {
                            color: "#666",
                            fontSize: 12,
                            lineHeight: 40
                        },
                        splitLine: {
                            lineStyle: {
                                type: "dashed",
                                color: "#E9E9E9"
                            }
                        },
                        axisLine: {
                            show: false
                        },
                        axisTick: {
                            show: false
                        }
                    }],
                    series: [{
                        name: '营业额',
                        type: "line",
                        smooth: true,
                        symbol: 'circle',
                        symbolSize: 8,
                        itemStyle: {
                            color: color[0]
                        },
                        areaStyle: {
                            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                                offset: 0,
                                color: hexToRgba(color[0], 0.3)
                            }, {
                                offset: 1,
                                color: hexToRgba(color[0], 0)
                            }])
                        },
                        data: echarts_order_amount
                    }]
                };

                var user_option = {
                    backgroundColor: bgColor,
                    color: color,
                    tooltip: {
                        trigger: "axis",
                        axisPointer: {
                            type: 'cross',
                            label: {
                                backgroundColor: '#6a7985'
                            }
                        }
                    },
                    grid: {
                        left: '3%',
                        right: '4%',
                        bottom: '3%',
                        containLabel: true
                    },
                    xAxis: [{
                        type: "category",
                        boundaryGap: false,
                        axisLabel: {
                            formatter: '{value}',
                            textStyle: {
                                color: "#333"
                            }
                        },
                        axisLine: {
                            lineStyle: {
                                color: "#D9D9D9"
                            }
                        },
                        data: dates
                    }],
                    yAxis: [{
                        type: "value",
                        name: '进店人数',
                        axisLabel: {
                            textStyle: {
                                color: "#666"
                            }
                        },
                        nameTextStyle: {
                            color: "#666",
                            fontSize: 12,
                            lineHeight: 40
                        },
                        splitLine: {
                            lineStyle: {
                                type: "dashed",
                                color: "#E9E9E9"
                            }
                        },
                        axisLine: {
                            show: false
                        },
                        axisTick: {
                            show: false
                        }
                    }],
                    series: [{
                        name: '进店人数',
                        type: "line",
                        smooth: true,
                        symbol: 'circle',
                        symbolSize: 8,
                        itemStyle: {
                            color: color[1]
                        },
                        areaStyle: {
                            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                                offset: 0,
                                color: hexToRgba(color[1], 0.3)
                            }, {
                                offset: 1,
                                color: hexToRgba(color[1], 0)
                            }])
                        },
                        data: echarts_user_visit
                    }]
                };

                var sale_charts = echarts.init(document.getElementById('sale-charts'));
                var user_charts = echarts.init(document.getElementById('user-charts'));
                sale_charts.setOption(sale_option);
                user_charts.setOption(user_option);

                window.onresize = function(){
                    sale_charts.resize();
                    user_charts.resize();
                };
            }
        });


        // 销冠商品
        var goods_lists = {
            elem: '#goods-lists'
            ,url: '{:url("index/hotGoods")}'
            ,cols: [[
                {field: 'name', title: '商品信息', templet: '#goods-info-tpl', width: 300},
                {field: 'count', title: '销量', sort: true}
            ]]
            ,skin: 'line'
            ,limit: 5
        };

        // 人气商品
        var goods_lists2 = {
            elem: '#goods-lists2'
            ,url: '{:url("index/visitGoods")}'
            ,cols: [[
                {field: 'name', title: '商品信息', templet: '#goods-info-tpl', width: 300},
                {field: 'count', title: '访客数', sort: true}
            ]]
            ,skin: 'line'
            ,limit: 5
        };

         layui.table.render(goods_lists);
         layui.table.render(goods_lists2);

        /**
         * 检查并显示资料不完整提示
         */
        // The popup is now controlled by server-side rendering, so this function is no longer needed.

        // 页面加载后执行检查
        // checkAndShowPopup();

        // 绑定按钮事件
        $('#go-to-goods').on('click', function() {
            openPage('{:url("goods.goods/lists")}', '7', '商品管理');
        });

        $('#complete-info').on('click', function() {
            openPage('{:url("Store/index")}', '10', '商家信息');
        });
    });
</script>

<!-- The conditional display logic is now handled by the main template block -->
