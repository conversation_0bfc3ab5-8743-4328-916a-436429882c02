{layout name="layout2" /}
<style>
    .pay-li {
        float: left;
        opacity: 1;
        position: relative;
    }
    .pay-img {
        width: 80px;
        height: 80px;
        padding: 4px;
    }
    .pay-img-del-x {
        position: absolute;
        z-index: 100;
        top: -4px;
        right: -2px;
        width: 20px;
        height: 20px;
        font-size: 16px;
        line-height: 16px;
        color: #fff;
        text-align: center;
        cursor: pointer;
        background: hsla(0, 0%, 60%, .6);
        border-radius: 10px;
    }
    .upload-cert-a{
        cursor: pointer;
        position: absolute;
        z-index: 100;
        top: 58px;
        right: -10%;
        width: 100px;
        height: 20px;
        font-size: 8px;
        line-height: 16px;
        text-align: center;
        border-radius: 10px;
        color: #4e8bff;
    }
    .upload-cert-a:hover {
        color: #0641cb;
    }
    .pay-cert{
        height:80px;line-height:80px
    }
    .cert-add,.key-add{
        height: 80px;
        width: 80px;
        float: left;
        opacity: 1;
        position: relative;
        border:1px dashed #a0a0a0;
        background-image:url('/static/common/image/default/add_file.png');
        background-repeat: no-repeat;
        background-position: 50% 35%;
        background-size:40px 40px;
        margin: 4px;
        text-align: center;
    }
</style>
<div class="layui-form" lay-filter="layuiadmin-form" id="layuiadmin-form" style="padding: 20px 30px 0 0;">
    <!--支付简称-->
    <div class="layui-form-item">
        <label class="layui-form-label">支付简称：</label>
        <div class="layui-input-block">
            <div class="layui-col-md4">
                <input type="text" name="short_name" value="{$info.short_name | default = ''}" lay-verify="check_required" lay-verType="tips"  autocomplete="off" class="layui-input">
                <div class="layui-form-mid layui-word-aux">会员在商城看见的支付名称</div>
            </div>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label">商户API密钥：</label>
        <div class="layui-input-block">
            <div class="layui-col-md4">
                <input type="text" name="pay_sign_key" value="{$info.config.pay_sign_key | default = ''}" lay-verify="check_required" lay-verType="tips"  autocomplete="off" class="layui-input">
                <div class="layui-form-mid layui-word-aux">微信支付商户API密钥（paySignKey）</div>
            </div>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label">微信支付商户号：</label>
        <div class="layui-input-block">
            <div class="layui-col-md4">
                <input type="text" name="mch_id" value="{$info.config.mch_id | default = ''}" lay-verify="check_required" lay-verType="tips"  autocomplete="off" class="layui-input">
                <div class="layui-form-mid layui-word-aux">微信支付商户号（MCHID）</div>
            </div>
        </div>
    </div>

    <!--支付图标-->
    <div class="layui-form-item">
        <label class="layui-form-label">支付图标</label>
        <div class="layui-input-inline">
            <div class="like-upload-image">
                {if $info.image}
                <div class="upload-image-div">
                    <img src="{$info.image}" alt="img">
                    <input type="hidden" name="image" value="{$info.image}">
                    <div class="del-upload-btn">x</div>
                </div>
                <div class="upload-image-elem" style="display:none;"><a class="add-upload-image"> + 添加图片</a></div>
                {else}
                <div class="upload-image-elem"><a class="add-upload-image"> + 添加图片</a></div>
                {/if}
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"></label>
            <label class=" layui-form-mid layui-word-aux">支付方式图标。建议尺寸：宽100px*高100px，jpg，jpeg，png格式</label>
        </div>
    </div>

    <!--支付证书-->
    <div class="layui-form-item">
        <label class="layui-form-label">支付证书：</label>
        <div class="layui-inline">
            <div class="pay-cert" id="kkk">
                <input class="text" type="hidden" name="apiclient_cert" value="{$info.config.apiclient_cert | default = ''}" >
                {if !empty($info.config.apiclient_cert)}
                <div class="cert-add" style="display: none;cursor: pointer;">
                    <a class="upload-cert-a">+ 添加文件</a>
                </div>
                <div class="pay-li">
                    <img class="pay-img"  src="/static/common/image/default/upload.png">
                    <a class="pay-img-del-x" style="display: none">x</a>
                </div>
                {else/}
                <div class="cert-add" style="cursor: pointer;">
                    <a class="upload-cert-a">+ 添加文件</a>
                </div>
                {/if}
            </div>
            <div class=" layui-form-mid layui-word-aux">
                微信支付证书（apiclient_cert.pem），前往微信商家平台下载，文件名一般为apiclient_cert.pem
            </div>
        </div>
    </div>

    <!--支付证书密钥-->
    <div class="layui-form-item">
        <label class="layui-form-label">支付证书密钥：</label>
        <div class="layui-inline">
            <div class="pay-cert">
                <input class="text" type="hidden" name="apiclient_key" value="{$info.config.apiclient_key | default = ''}" >
                {if !empty($info.config.apiclient_key)}
                <div class="key-add" style="display: none;cursor: pointer;">
                    <a class="upload-cert-a">+ 添加文件</a>
                </div>
                <div class="pay-li">
                    <img class="pay-img"  src="/static/common/image/default/upload.png">
                    <a class="pay-img-del-x" style="display: none">x</a>
                </div>
                {else/}
                <div class="key-add" style="cursor: pointer;">
                    <a class="upload-cert-a">+ 添加文件</a>
                </div>
                {/if}
            </div>
            <div class=" layui-form-mid layui-word-aux">
                微信支付证书密钥（apiclient_key.pem），前往微信商家平台下载。文件名一般为apiclient_key.pem
            </div>
        </div>
    </div>

    <!--支付授权目录-->
    <div class="layui-form-item">
        <label class="layui-form-label" >支付授权目录：</label>
        <div class="layui-input-block">
            <div class="layui-col-md4">
                <input type="text"   value="{$domain}/" autocomplete="off" class="layui-input">
            </div>
            <button type="button" class="layui-btn layui-btn-primary layui-btn-sm copy" >复制</button>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"></label>
            <label class="layui-form-mid layui-word-aux">支付授权目录仅供参考，请根据微信支付的系统提示进行设置</label>
        </div>
    </div>

    <!--排序-->
    <div class="layui-form-item">
        <label class="layui-form-label">排序</label>
        <div class="layui-input-block">
            <div class="layui-col-md4">
                <input type="number"  name="sort" value="{$info.sort | default = ''}" class="layui-input" >
                <div class=" layui-form-mid layui-word-aux">排序越小越前</div>
            </div>
        </div>
    </div>

    <!--状态-->
    <div class="layui-form-item">
        <label class="layui-form-label">状态:</label>
        <div class="layui-input-block">
            <input type="radio" name="status" value=1 title="启用"{if condition="$info.status eq 1" }checked{/if}>
            <input type="radio" name="status" value=0 title="关闭" {if condition="$info.status eq 0" }checked{/if}>
        </div>
    </div>

    <div class="layui-form-item layui-hide">
        <input type="button" lay-submit lay-filter="edit-submit" id="edit-submit" value="确认">
    </div>
</div>
</div>
<script>
    layui.use(['form'], function(){
        var $ = layui.$
            ,form = layui.form;

        like.delUpload();
        $(document).on("click", ".add-upload-image", function () {
            like.imageUpload({
                limit: 1,
                field: "image",
                that: $(this)
            });
        });

        //删除图片/证书
        $(document).on('click', '.pay-img-del-x', function () {
            $(this).parent().siblings('input').val('');
            $(this).parent().siblings().css('display','block');
            $(this).parent().remove();
        });

        form.verify({
            check_required: function (value, item) {
                var status = $('input[name="status"]:checked').val();
                value = value.trim();
                if (status == 1 && value.length == 0) {
                    return '请填写完整';
                }
            }
        });
                    //==========================================上传证书start=========================================================


                    like.certUpload('.cert-add', '{:url("file/other")}?local=1&sub_dir=cert', '{$storageUrl}');
                    like.certUpload('.key-add', '{:url("file/other")}?local=1&sub_dir=cert', '{$storageUrl}');

                    //==========================================上传证书end===========================================================
        //  删除按钮的显示与隐藏
        $(document).on('mouseover', '.pay-img', function () {
            $(this).next().show();
        });
        $(document).on('mouseout', '.pay-img', function () {
            $(this).next().hide();
        });
        $(document).on('mouseover', '.pay-img-del-x', function () {
            $(this).show();
        });
        $(document).on('mouseout', '.pay-img-del-x', function () {
            $(this).hide();
        });

    })
</script>