{layout name="layout1" /}

<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-header">订单基本信息</div>
        <div class="layui-card-body">
            <div class="layui-form layui-form-pane" lay-filter="detailForm">
                <div class="layui-form-item">
                    <label class="layui-form-label">订单号</label>
                    <div class="layui-input-block">
                        <div class="layui-form-mid layui-word-aux" id="order_sn">-</div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">购买用户</label>
                    <div class="layui-input-block">
                        <div class="layui-form-mid layui-word-aux" id="user_nickname">-</div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">支付金额</label>
                    <div class="layui-input-block">
                        <div class="layui-form-mid layui-word-aux" id="ad_price">-</div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">支付状态</label>
                    <div class="layui-input-block">
                        <div class="layui-form-mid layui-word-aux" id="status_text">-</div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">支付方式</label>
                    <div class="layui-input-block">
                        <div class="layui-form-mid layui-word-aux" id="pay_way_text">-</div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">购买时间</label>
                    <div class="layui-input-block">
                        <div class="layui-form-mid layui-word-aux" id="create_time_text">-</div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">支付时间</label>
                    <div class="layui-input-block">
                        <div class="layui-form-mid layui-word-aux" id="pay_time_text">-</div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">生效时间</label>
                    <div class="layui-input-block">
                        <div class="layui-form-mid layui-word-aux" id="start_time_text">-</div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">失效时间</label>
                    <div class="layui-input-block">
                        <div class="layui-form-mid layui-word-aux" id="end_time_text">-</div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">剩余天数</label>
                    <div class="layui-input-block">
                        <div class="layui-form-mid layui-word-aux" id="remaining_days">-</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="layui-card">
        <div class="layui-card-header">广告位信息</div>
        <div class="layui-card-body">
            <div class="layui-form layui-form-pane">
                <div class="layui-form-item">
                    <label class="layui-form-label">广告位名称</label>
                    <div class="layui-input-block">
                        <div class="layui-form-mid layui-word-aux" id="ad_position_name">-</div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">广告位图</label>
                    <div class="layui-input-block">
                        <img id="ad_position_image" src="" style="max-height: 100px; max-width: 100%; display: none;"/>
                        <span id="ad_position_image_none" class="layui-form-mid layui-word-aux">无</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="layui-card">
        <div class="layui-card-header">广告内容信息</div>
        <div class="layui-card-body">
             <div class="layui-form layui-form-pane">
                <div class="layui-form-item">
                    <label class="layui-form-label">广告标题</label>
                    <div class="layui-input-block">
                        <div class="layui-form-mid layui-word-aux" id="ad_title">-</div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">广告图片</label>
                    <div class="layui-input-block">
                         <img id="ad_content_image" src="" style="max-height: 100px; max-width: 100%; display: none;"/>
                         <span id="ad_content_image_none" class="layui-form-mid layui-word-aux">未设置</span>
                    </div>
                </div>
                 <div class="layui-form-item">
                    <label class="layui-form-label">链接类型</label>
                    <div class="layui-input-block">
                        <div class="layui-form-mid layui-word-aux" id="link_type_text">-</div>
                    </div>
                </div>
                 <div class="layui-form-item">
                    <label class="layui-form-label">链接地址</label>
                    <div class="layui-input-block">
                        <div class="layui-form-mid layui-word-aux" id="full_link_address" style="word-break: break-all;">-</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
layui.use(['jquery', 'form', 'layer'], function() {
    var $ = layui.jquery;
    var form = layui.form;
    var layer = layui.layer;

    // 从视图变量或 URL 获取 ID (这里假设后端已将 id 注入到视图)
    var recordId = "{$id}"; 

    if (!recordId) {
        layer.msg('无法获取记录ID', {icon: 2});
        // 可以选择关闭弹窗
        // var index = parent.layer.getFrameIndex(window.name); 
        // parent.layer.close(index);
        return;
    }

    // 加载详情数据
    $.ajax({
        url: "{:url('decoration.ad/buyLogDetail')}",
        type: 'GET',
        data: { id: recordId },
        dataType: 'json',
        success: function(res) {
            if (res.code == 1 && res.data) {
                var data = res.data;
                // 填充基本信息
                $('#order_sn').text(data.order_sn || '-');
                $('#user_nickname').text(data.user_nickname || '-');
                $('#ad_price').text(data.ad_price !== null ? data.ad_price : '-');
                $('#status_text').text(data.status_text || '-');
                $('#pay_way_text').text(data.pay_way_text || '-');
                $('#create_time_text').text(data.create_time_text || '-');
                $('#pay_time_text').text(data.pay_time_text || '-');
                $('#start_time_text').text(data.start_time_text || '-');
                $('#end_time_text').text(data.end_time_text || '-');
                $('#remaining_days').text(data.remaining_days !== null ? data.remaining_days : '-');

                // 填充广告位信息
                if (data.ad_position_info) {
                    $('#ad_position_name').text(data.ad_position_info.name || '未知');
                    if (data.ad_position_info.image_url) {
                        $('#ad_position_image').attr('src', data.ad_position_info.image_url).show();
                        $('#ad_position_image_none').hide();
                        // 点击图片预览
                        $('#ad_position_image').off('click').on('click', function(){
                            layer.photos({ photos: {"data": [{"src": $(this).attr('src')}]}, anim: 5 });
                        });
                    } else {
                        $('#ad_position_image').hide();
                        $('#ad_position_image_none').show();
                    }
                }

                // 填充广告内容信息
                if (data.ad_content) {
                    $('#ad_title').text(data.ad_content.title || '-');
                    $('#link_type_text').text(data.ad_content.link_type_text || '-');
                    $('#full_link_address').text(data.ad_content.full_link_address || '-');
                    if (data.ad_content.image_url) {
                        $('#ad_content_image').attr('src', data.ad_content.image_url).show();
                        $('#ad_content_image_none').hide();
                        // 点击图片预览
                         $('#ad_content_image').off('click').on('click', function(){
                            layer.photos({ photos: {"data": [{"src": $(this).attr('src')}]}, anim: 5 });
                        });
                    } else {
                         $('#ad_content_image').hide();
                        $('#ad_content_image_none').show().text('未设置'); // 如果无图则显示未设置
                    }
                } else {
                    // 如果整个 ad_content 不存在
                    $('#ad_title').text('-');
                    $('#link_type_text').text('-');
                    $('#full_link_address').text('-');
                    $('#ad_content_image').hide();
                    $('#ad_content_image_none').show().text('未设置');
                }
                
                // 重新渲染表单（如果需要，例如有 select 等）
                // form.render(); 

            } else {
                layer.msg(res.msg || '加载数据失败', {icon: 2});
            }
        },
        error: function(xhr, status, error) {
            layer.msg('请求详情接口失败: ' + error, {icon: 2});
        }
    });
});
</script> 