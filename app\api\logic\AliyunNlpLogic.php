<?php
namespace app\api\logic;

use think\facade\Log;
use think\facade\Cache;
use app\common\server\ConfigServer;

/**
 * 阿里云NLP服务逻辑类
 * 支持HTTP请求和SDK两种方式调用API
 */
class AliyunNlpLogic
{
    // 缓存时间（秒）
    const CACHE_TTL = 86400; // 24小时

    /**
     * 调用阿里云NLP词法分析接口
     * @param string $text 要分析的文本
     * @param bool $useCache 是否使用缓存
     * @return array 分析结果
     */
    public static function wordSegment(string $text, bool $useCache = true): array
    {
        // 如果启用缓存，先尝试从缓存获取
        if ($useCache) {
            $cacheKey = 'nlp_segment:' . md5($text);
            $cached = Cache::get($cacheKey);
            if ($cached) {
                Log::info('从缓存获取分词结果: ' . $text);
                return $cached;
            }
        }

        // 获取AccessKey
        $accessKeyId = ConfigServer::get('aliyun', 'access_key_id', '');
        $accessKeySecret = ConfigServer::get('aliyun', 'access_key_secret', '');

        if (empty($accessKeyId) || empty($accessKeySecret)) {
            Log::info('阿里云API未配置，无法调用NLP服务');
            return [];
        }

        try {
            // 先尝试使用SDK方式
            if (class_exists('\\AlibabaCloud\\Client\\AlibabaCloud')) {
                try {
                    Log::info('尝试使用SDK方式调用阿里云NLP服务');
                    $result = self::wordSegmentBySDK($text, $accessKeyId, $accessKeySecret);
                    if (!empty($result)) {
                        // 缓存结果
                        if ($useCache) {
                            Cache::set($cacheKey, $result, self::CACHE_TTL);
                        }
                        return $result;
                    }
                } catch (\Exception $e) {
                    Log::error('SDK方式调用阿里云NLP服务异常: ' . $e->getMessage());
                }
            }

            // SDK方式失败，尝试HTTP方式
            Log::info('尝试使用HTTP方式调用阿里云NLP服务');

            // API参数
            $params = [
                'Format' => 'JSON',
                'Version' => '2018-04-08',
                'AccessKeyId' => $accessKeyId,
                'SignatureMethod' => 'HMAC-SHA1',
                'Timestamp' => gmdate('Y-m-d\TH:i:s\Z'),
                'SignatureVersion' => '1.0',
                'SignatureNonce' => uniqid(),
                'Action' => 'WordSegment',
                'Domain' => 'general',
                'Text' => $text
            ];

            // 构建签名
            $params['Signature'] = self::computeSignature($params, $accessKeySecret);

            // 发送请求
            $url = 'https://alinlp.cn-hangzhou.aliyuncs.com/?' . http_build_query($params);
            $response = self::httpGet($url);

            if ($response) {
                $result = json_decode($response, true);
                if ($result) {
                    // 缓存结果
                    if ($useCache) {
                        Cache::set($cacheKey, $result, self::CACHE_TTL);
                    }
                    return $result;
                }
            }

            return [];
        } catch (\Exception $e) {
            Log::error('阿里云NLP服务调用异常: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * 使用SDK方式调用阿里云NLP词法分析接口
     * @param string $text 要分析的文本
     * @param string $accessKeyId AccessKey ID
     * @param string $accessKeySecret AccessKey Secret
     * @return array 分析结果
     */
    private static function wordSegmentBySDK(string $text, string $accessKeyId, string $accessKeySecret): array
    {
        try {
            // 使用阿里云SDK调用NLP服务
            \AlibabaCloud\Client\AlibabaCloud::accessKeyClient($accessKeyId, $accessKeySecret)
                ->regionId('cn-hangzhou')
                ->asDefaultClient();

            // 调用阿里云NLP词法分析接口
            $result = \AlibabaCloud\Client\AlibabaCloud::nlp()
                ->v20180408()
                ->wordSegment()
                ->withDomain('general')
                ->withText($text)
                ->format('JSON')
                ->request();

            return $result->toArray();
        } catch (\Exception $e) {
            Log::error('SDK方式调用阿里云NLP服务异常: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * 模拟阿里云NLP词法分析结果
     * 当无法连接阿里云服务时使用
     * @param string $text 要分析的文本
     * @return array 分析结果
     */
    public static function localWordSegment(string $text): array
    {
        // 简单的分词逻辑，按空格和常见标点符号分割
        $words = preg_split('/[\s\p{P}]+/u', $text, -1, PREG_SPLIT_NO_EMPTY);

        // 构造与阿里云NLP服务相似的返回格式
        $result = [
            'Data' => [
                'WordList' => []
            ],
            'RequestId' => uniqid()
        ];

        foreach ($words as $word) {
            if (empty($word)) continue;

            // 尝试判断词性（非常简单的判断，实际应用中需要更复杂的逻辑）
            $pos = self::guessPos($word);

            $result['Data']['WordList'][] = [
                'Word' => $word,
                'Pos' => $pos
            ];
        }

        return $result;
    }

    /**
     * 简单猜测词性
     * @param string $word 词语
     * @return string 词性
     */
    private static function guessPos(string $word): string
    {
        // 这里只是一个非常简单的示例，实际应用中需要更复杂的逻辑
        $length = mb_strlen($word, 'UTF-8');

        // 数字
        if (preg_match('/^\d+$/', $word)) {
            return 'm'; // 数词
        }

        // 英文单词
        if (preg_match('/^[a-zA-Z]+$/', $word)) {
            return 'eng'; // 英文单词
        }

        // 常见形容词
        $adjectives = ['大', '小', '高', '低', '新', '旧', '好', '坏', '长', '短', '宽', '窄', '厚', '薄', '轻', '重', '快', '慢', '热', '冷', '硬', '软', '亮', '暗', '甜', '苦', '酸', '辣', '咸', '香', '臭', '美', '丑', '贵', '便宜', '简单', '复杂', '容易', '困难', '清晰', '模糊', '干净', '脏', '安全', '危险', '健康', '生病', '年轻', '年老', '聪明', '愚蠢', '勇敢', '胆小', '诚实', '虚伪', '友好', '敌对', '快乐', '悲伤', '兴奋', '沮丧', '满意', '不满', '成功', '失败', '正确', '错误', '真实', '虚假', '积极', '消极', '主动', '被动', '开放', '封闭', '公开', '秘密', '公正', '不公', '合法', '非法', '道德', '不道德', '合理', '不合理', '必要', '不必要', '可能', '不可能', '确定', '不确定', '明确', '模糊', '重要', '不重要', '紧急', '不紧急', '严重', '轻微', '复杂', '简单', '困难', '容易', '危险', '安全', '有效', '无效', '充分', '不足', '适当', '不当', '合适', '不合适', '正常', '异常', '特殊', '普通', '特别', '一般', '独特', '常见', '罕见', '流行', '过时', '现代', '传统', '先进', '落后', '积极', '消极', '乐观', '悲观', '主动', '被动', '热情', '冷漠', '友好', '敌对', '和平', '暴力', '温和', '激烈', '缓慢', '迅速', '逐渐', '突然', '持久', '短暂', '永久', '临时', '固定', '流动', '稳定', '不稳', '平静', '混乱', '有序', '无序', '规律', '随机', '精确', '粗略', '详细', '简略', '全面', '片面', '系统', '零散', '连续', '间断', '统一', '分散', '集中', '分散', '紧密', '松散', '紧张', '放松', '严格', '宽松', '严肃', '幽默', '认真', '马虎', '细心', '粗心', '谨慎', '大胆', '保守', '激进', '传统', '现代', '古老', '新颖', '经典', '时尚', '优雅', '粗俗', '高雅', '低俗', '高贵', '卑贱', '华丽', '朴素', '奢侈', '节俭', '浪费', '节约', '丰富', '贫乏', '充足', '不足', '过剩', '短缺', '完整', '残缺', '完美', '缺陷', '理想', '现实', '抽象', '具体', '客观', '主观', '理性', '感性', '逻辑', '情感', '科学', '艺术', '理论', '实践', '学术', '通俗', '专业', '业余', '官方', '民间', '公共', '私人', '集体', '个人', '社会', '个体', '群体', '单独', '共同', '独立', '依赖', '自由', '约束', '开放', '封闭', '包容', '排斥', '接受', '拒绝', '赞同', '反对', '支持', '反对', '肯定', '否定', '赞成', '反对', '同意', '反对', '承认', '否认', '接受', '拒绝', '允许', '禁止', '鼓励', '阻止', '促进', '阻碍', '帮助', '阻碍', '有利', '不利', '有益', '有害', '无害', '有毒', '安全', '危险', '无害', '有害', '有利', '不利', '有益', '有害', '无害', '有毒', '安全', '危险'];
        if (in_array($word, $adjectives)) {
            return 'a'; // 形容词
        }

        // 常见动词
        $verbs = ['是', '有', '做', '看', '听', '说', '想', '知道', '认为', '觉得', '希望', '需要', '喜欢', '爱', '恨', '怕', '担心', '害怕', '担忧', '忧虑', '焦虑', '紧张', '放松', '休息', '睡觉', '醒来', '起床', '躺下', '坐下', '站起', '走', '跑', '跳', '游泳', '飞', '爬', '滑', '滚', '转', '弯', '停', '开始', '结束', '继续', '暂停', '中断', '打断', '干扰', '帮助', '阻碍', '促进', '阻止', '禁止', '允许', '同意', '拒绝', '接受', '给予', '获取', '得到', '失去', '保持', '维持', '改变', '转变', '发展', '成长', '衰退', '增加', '减少', '提高', '降低', '升高', '下降', '扩大', '缩小', '扩展', '收缩', '延伸', '缩回', '打开', '关闭', '开启', '关闭', '启动', '关闭', '打开', '关上', '进入', '离开', '到达', '出发', '返回', '回来', '前进', '后退', '上升', '下降', '增长', '减少', '扩大', '缩小', '加强', '减弱', '增强', '削弱', '提高', '降低', '改善', '恶化', '优化', '简化', '复杂化', '净化', '污染', '清洁', '弄脏', '整理', '打乱', '组织', '混乱', '分类', '混合', '分离', '结合', '分解', '组合', '拆分', '连接', '断开', '绑定', '解绑', '捆绑', '释放', '压缩', '膨胀', '收缩', '扩张', '紧缩', '放松', '加速', '减速', '加快', '放慢', '提速', '降速', '加热', '冷却', '升温', '降温', '加热', '制冷', '烘干', '浸湿', '点燃', '熄灭', '燃烧', '熄灭', '照亮', '变暗', '照明', '遮蔽', '显示', '隐藏', '展示', '掩盖', '揭示', '隐瞒', '公开', '隐藏', '暴露', '掩盖', '保护', '伤害', '防护', '攻击', '防御', '进攻', '抵抗', '屈服', '反抗', '顺从', '反抗', '服从', '命令', '服从', '控制', '释放', '支配', '屈服', '统治', '反抗', '管理', '混乱', '组织', '混乱', '规划', '混乱', '安排', '打乱', '协调', '冲突', '和谐', '冲突', '统一', '分裂', '团结', '分散', '集中', '分散', '聚集', '分散', '聚焦', '发散', '集中', '分散', '专注', '分心', '关注', '忽视', '重视', '忽略', '注意', '忽视', '记住', '忘记', '铭记', '遗忘', '学习', '忘却', '掌握', '遗忘', '理解', '误解', '领会', '误会', '明白', '困惑', '清楚', '模糊', '确定', '怀疑', '肯定', '否定', '确认', '否认', '承认', '否认', '接受', '拒绝', '同意', '反对', '赞成', '反对', '支持', '反对', '拥护', '抵制', '赞同', '反对', '认可', '否定', '肯定', '质疑', '相信', '怀疑', '信任', '猜疑', '依赖', '独立', '依靠', '自立', '求助', '自助', '帮助', '妨碍', '协助', '阻碍', '支持', '反对', '鼓励', '阻止', '促进', '阻碍', '推动', '阻止', '激励', '打击', '鼓舞', '沮丧', '表扬', '批评', '赞美', '指责', '夸奖', '责备', '称赞', '谴责', '肯定', '否定', '赞赏', '贬低', '尊重', '蔑视', '敬佩', '鄙视', '仰慕', '轻视', '崇拜', '鄙视', '爱戴', '憎恨', '喜爱', '厌恶', '热爱', '憎恨', '喜欢', '讨厌', '爱', '恨', '疼爱', '憎恨', '宠爱', '厌恶', '疼爱', '虐待', '关心', '忽视', '照顾', '忽略', '关注', '忽视', '在意', '漠视', '重视', '轻视', '珍视', '轻视', '珍惜', '浪费', '爱惜', '糟蹋', '保护', '破坏', '维护', '损害', '保存', '毁灭', '保留', '删除', '储存', '清除', '保管', '丢弃', '保持', '改变', '维持', '变更', '保留', '放弃', '坚持', '放弃', '固守', '改变', '坚守', '背离', '遵守', '违反', '遵循', '背离', '服从', '违抗', '顺从', '反抗', '屈服', '抵抗', '投降', '反抗', '妥协', '坚持', '让步', '坚守', '放弃', '坚定', '动摇', '决定', '犹豫', '确定', '迟疑', '肯定', '怀疑', '确信', '疑惑', '相信', '怀疑', '信任', '猜疑', '依赖', '独立', '依靠', '自立', '求助', '自助', '帮助', '妨碍', '协助', '阻碍', '支持', '反对', '鼓励', '阻止', '促进', '阻碍', '推动', '阻止', '激励', '打击', '鼓舞', '沮丧', '表扬', '批评', '赞美', '指责', '夸奖', '责备', '称赞', '谴责', '肯定', '否定', '赞赏', '贬低', '尊重', '蔑视', '敬佩', '鄙视', '仰慕', '轻视', '崇拜', '鄙视', '爱戴', '憎恨', '喜爱', '厌恶', '热爱', '憎恨', '喜欢', '讨厌', '爱', '恨', '疼爱', '憎恨', '宠爱', '厌恶', '疼爱', '虐待', '关心', '忽视', '照顾', '忽略', '关注', '忽视', '在意', '漠视', '重视', '轻视', '珍视', '轻视', '珍惜', '浪费', '爱惜', '糟蹋', '保护', '破坏', '维护', '损害', '保存', '毁灭', '保留', '删除', '储存', '清除', '保管', '丢弃', '保持', '改变', '维持', '变更', '保留', '放弃', '坚持', '放弃', '固守', '改变', '坚守', '背离', '遵守', '违反', '遵循', '背离', '服从', '违抗', '顺从', '反抗', '屈服', '抵抗', '投降', '反抗', '妥协', '坚持', '让步', '坚守', '放弃', '坚定', '动摇', '决定', '犹豫', '确定', '迟疑', '肯定', '怀疑', '确信', '疑惑', '相信', '怀疑', '信任', '猜疑'];
        if (in_array($word, $verbs)) {
            return 'v'; // 动词
        }

        // 根据长度简单判断
        if ($length == 1) {
            return 'n'; // 名词
        } else if ($length == 2) {
            return 'n'; // 名词
        } else {
            return 'n'; // 默认为名词
        }
    }

    /**
     * 处理商品分词并返回有效的分词结果
     * @param string $goodsName 商品名称
     * @param string $goodsRemark 商品描述
     * @return array 分词结果数组
     */
    public static function processGoodsSplitWords(string $goodsName, string $goodsRemark = ''): array
    {
        // 合并商品名称和描述
        $text = $goodsName;
        if (!empty($goodsRemark)) {
            $text .= ' ' . $goodsRemark;
        }

        // 调用阿里云NLP分词
        $result = self::wordSegment($text);

        // 提取有效词语
        $validWords = [];

        // 处理阿里云NLP返回结果
        if (!empty($result) && isset($result['Data']) && isset($result['Data']['WordList'])) {
            foreach ($result['Data']['WordList'] as $wordInfo) {
                $word = $wordInfo['Word'] ?? '';
                $pos = $wordInfo['Pos'] ?? '';

                // 过滤掉无效词语
                if (empty($word) || mb_strlen($word, 'UTF-8') < 2) {
                    continue;
                }

                // 过滤掉停用词
                if (self::isStopWord($word)) {
                    continue;
                }

                // 保留名词、动词、形容词等有意义的词语
                if (in_array($pos, ['n', 'v', 'a', 'eng', 'nz', 'vn', 'an'])) {
                    $validWords[] = $word;
                }
            }
        } else {
            // 如果阿里云NLP分词失败，使用本地分词逻辑
            $localResult = self::localWordSegment($text);
            if (!empty($localResult) && isset($localResult['Data']) && isset($localResult['Data']['WordList'])) {
                foreach ($localResult['Data']['WordList'] as $wordInfo) {
                    $word = $wordInfo['Word'] ?? '';
                    $pos = $wordInfo['Pos'] ?? '';

                    // 过滤掉无效词语
                    if (empty($word) || mb_strlen($word, 'UTF-8') < 2) {
                        continue;
                    }

                    // 过滤掉停用词
                    if (self::isStopWord($word)) {
                        continue;
                    }

                    $validWords[] = $word;
                }
            }
        }

        // 去重
        $validWords = array_unique($validWords);

        return $validWords;
    }

    /**
     * 判断是否为停用词
     * @param string $word 词语
     * @return bool 是否为停用词
     */
    private static function isStopWord(string $word): bool
    {
        // 常见停用词列表
        $stopWords = ['的', '了', '和', '与', '或', '是', '在', '有', '个', '这', '那', '你', '我', '他', '她', '它', '们', '啊', '吧', '呢', '哦', '哈', '呀', '嗯', '哎', '哟', '喂', '嘿', '嗨', '啦', '呵', '哇', '咦', '唉', '哼', '哩', '咯', '咚', '咕', '咙', '咣', '哗', '啪', '啧', '嘭', '嗒', '嗖', '嗡', '嘶', '嘻', '嘿', '噢', '噗', '噜', '噼', '嚓', '嚯', '一', '二', '三', '四', '五', '六', '七', '八', '九', '十', '百', '千', '万', '亿', '第', '每', '各', '某', '该', '此', '这些', '那些', '所有', '任何', '所', '之', '乎', '也', '于', '则', '却', '为', '以', '因', '所以', '因此', '但是', '而且', '并且', '如果', '虽然', '即使', '无论', '只要', '不管', '除非', '若', '即', '既', '既然', '尽管', '不论', '假如', '假使', '倘若', '如', '当', '若是', '假若', '倘', '倘使', '要是', '一旦', '只有', '然后', '否则', '不然', '要不', '要不然', '如此', '这样', '那样', '如何', '怎样', '怎么', '什么', '哪', '谁', '何', '怎', '多少', '几', '如何', '多么', '哪里', '哪儿', '哪个', '哪些', '哪种', '哪样', '哪边', '哪方', '如何', '怎样', '怎么样', '怎么着', '怎么办', '怎么回事', '什么样', '什么时候', '什么地方', '什么人', '什么东西', '什么事', '什么意思', '什么关系', '什么情况', '什么问题', '为什么', '为何', '为啥', '怎么', '怎的', '怎么着', '怎么办', '怎么样', '怎么回事', '怎么搞的', '怎么弄', '怎么整', '怎么做', '怎么说', '怎么想', '怎么看', '怎么听', '怎么写', '怎么读', '怎么学', '怎么教', '怎么用', '怎么吃', '怎么穿', '怎么走', '怎么来', '怎么去', '怎么进', '怎么出', '怎么上', '怎么下', '怎么左', '怎么右', '怎么前', '怎么后', '怎么里', '怎么外', '怎么东', '怎么西', '怎么南', '怎么北', '怎么中', '怎么边', '怎么角', '怎么面', '怎么侧', '怎么旁', '怎么处', '怎么头', '怎么尾', '怎么身', '怎么心', '怎么眼', '怎么耳', '怎么鼻', '怎么口', '怎么手', '怎么脚', '怎么腿', '怎么胳膊', '怎么肩', '怎么胸', '怎么腰', '怎么背', '怎么肚子', '怎么屁股', '怎么脸', '怎么头发', '怎么脖子', '怎么嘴', '怎么牙', '怎么舌', '怎么喉', '怎么额', '怎么颊', '怎么颈', '怎么臂', '怎么肘', '怎么腕', '怎么掌', '怎么指', '怎么爪', '怎么股', '怎么膝', '怎么踝', '怎么跖', '怎么趾', '怎么跟', '怎么蹄', '怎么翅', '怎么翼', '怎么羽', '怎么毛', '怎么发', '怎么须', '怎么鬃', '怎么鬣', '怎么壳', '怎么甲', '怎么鳞', '怎么皮', '怎么革', '怎么毛皮', '怎么茸', '怎么角', '怎么骨', '怎么牙', '怎么爪', '怎么蹄', '怎么喙', '怎么嘴', '怎么吻', '怎么鼻', '怎么眼', '怎么耳', '怎么须', '怎么鬃', '怎么尾', '怎么尾巴', '怎么鳍', '怎么鳃', '怎么翅', '怎么翼', '怎么羽', '怎么毛', '怎么茸', '怎么角', '怎么骨', '怎么牙', '怎么爪', '怎么蹄', '怎么喙', '怎么嘴', '怎么吻', '怎么鼻', '怎么眼', '怎么耳', '怎么须', '怎么鬃', '怎么尾', '怎么尾巴', '怎么鳍', '怎么鳃', '怎么翅', '怎么翼', '怎么羽', '怎么毛'];

        return in_array($word, $stopWords);
    }

    /**
     * 计算阿里云API签名
     * @param array $parameters 请求参数
     * @param string $accessKeySecret AccessKey Secret
     * @return string 签名
     */
    private static function computeSignature(array $parameters, string $accessKeySecret): string
    {
        ksort($parameters);

        $canonicalizedQueryString = '';
        foreach ($parameters as $key => $value) {
            $canonicalizedQueryString .= '&' . self::percentEncode($key) . '=' . self::percentEncode($value);
        }

        $stringToSign = 'GET&%2F&' . self::percentEncode(substr($canonicalizedQueryString, 1));

        return base64_encode(hash_hmac('sha1', $stringToSign, $accessKeySecret . '&', true));
    }

    /**
     * URL编码
     * @param string $value 要编码的值
     * @return string 编码后的值
     */
    private static function percentEncode(string $value): string
    {
        $value = urlencode($value);
        $value = str_replace('+', '%20', $value);
        $value = str_replace('*', '%2A', $value);
        $value = str_replace('%7E', '~', $value);
        return $value;
    }

    /**
     * 发送HTTP GET请求
     * @param string $url 请求URL
     * @return string|false 响应内容或false
     */
    private static function httpGet(string $url)
    {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        $response = curl_exec($ch);
        curl_close($ch);
        return $response;
    }
}
