{layout name="layout2" /}
<style>
    .layui-form {
        margin: 5px;
    }
    .layui-form-label {
        width: 120px;
        text-align: left;
        padding-left:30px;
    }
    .layui-input-block {
        width: 300px;
        line-height: 38px;
    }
    .layui-btn {
        margin-top: 5px;
    }
    select {
        width: 300px;
    }
    .layui-input {
        width: 300px;
    }
</style>
<form class="layui-form">
    <input type="hidden" name="user_id" value="{$user.user_id}">
    <div class="layui-form-item">
        <label class="layui-form-label">用户编号</label>
        <div class="layui-input-block">
            {$user.user_sn}
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">用户昵称</label>
        <div class="layui-input-block">
            {$user.user_nickname}
        </div>
    </div>
    <!-- 集采众筹活动时间 -->
    <div class="layui-form-item" style="margin-bottom: 0;">
        <label class="layui-form-label"><font color="red">*</font>代理期限：</label>
        <div class="layui-input-block">
            <div class="layui-inline">
                <input type="text" id="distribution_start_time" value="{$user.distribution_start_time}" name="distribution_start_time" class="layui-input" autocomplete="off" lay-verType="tips" lay-verify="required">
            </div>
            <div class="layui-inline">-</div>
            <div class="layui-inline">
                <input type="text" id="distribution_end_time" value="{$user.distribution_end_time}" name="distribution_end_time" class="layui-input" autocomplete="off" lay-verType="tips" lay-verify="required">
            </div>
        </div>
        <div class="layui-form-mid layui-word-aux" style="white-space:nowrap;margin-left:140px;">代理到期时,代理将冻结,将不能继续邀请下级及享受佣金分成.</div>
    </div>

    <div class="layui-form-item">
        <div class="layui-input-block layui-hide">
            <button class="layui-btn" lay-submit lay-filter="formSubmit" id="formSubmit">立即提交</button>
        </div>
    </div>
</form>

<script>

    layui.config({
        version: "{$front_version}",
        base: '/static/lib/'
    }).use(['element', 'form'], function () {
        var $ = layui.$
            , form = layui.form
            , layer = layui.layer
            , element = layui.element;
    });
</script>
