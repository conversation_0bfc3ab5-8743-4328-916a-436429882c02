<?php



namespace app\kefuapi\controller;

use app\common\basics\KefuBase;
use app\common\server\FileServer;
use app\common\server\JsonServer;
use app\common\server\ConfigServer;

class File extends KefuBase
{

    public $like_not_need_login = [];

    /**
     * @notes upload image
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2021/12/16 11:11
     */
    public function formImage()
    {
        try{
            $data = FileServer::image(0, $this->shop_id, 0, 'uploads/kefu/'.$this->kefu_id);
            return JsonServer::success('上传成功', $data);
        }catch(\Exception $e) {
            return JsonServer::error($e->getMessage());
        }
    }

    /**
     * @notes upload video
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2024/12/24 10:00
     */
    public function formVideo()
    {
        try{
            $audit_article = ConfigServer::get('community', 'audit_article');
            $data = FileServer::video(0, $this->shop_id, 0, 'uploads/kefu/'.$this->kefu_id);

            if($audit_article){
                $msg = video_filter($data['base_url'], $data['id']);
                if(empty($msg)){
                    return JsonServer::error('视频文件损坏,请更换上传!');
                }else{
                    if(isset($msg['code']) && $msg['code'] === 1101){
                        return JsonServer::error($msg['msg'], [], 1101);
                    }
                }
            }

            return JsonServer::success('上传成功', $data);
        }catch(\Exception $e) {
            return JsonServer::error($e->getMessage());
        }
    }

    /**
     * @notes upload audio
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2024/12/24 10:00
     */
    public function formAudio()
    {
        try{
            // 录音文件不需要审核，直接上传
            $data = FileServer::audio(0, $this->shop_id, 0, 'uploads/kefu/'.$this->kefu_id);
            return JsonServer::success('上传成功', $data);
        }catch(\Exception $e) {
            return JsonServer::error($e->getMessage());
        }
    }
}