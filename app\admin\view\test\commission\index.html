{layout name="layout1" /}
<div class="wrapper">
    <div class="layui-card">
        <div class="layui-card-header">
            <h3>佣金计算功能测试</h3>
        </div>
        <div class="layui-card-body">
            <div class="layui-row layui-col-space15">
                <div class="layui-col-md6">
                    <button class="layui-btn layui-btn-normal" id="test-btn">开始测试</button>
                    <button class="layui-btn layui-btn-warm" id="update-btn">更新订单佣金</button>
                    <button class="layui-btn layui-btn-danger" id="refund-test-btn">退款佣金测试</button>
                </div>
            </div>
            
            <div id="test-results" style="margin-top: 20px; display: none;">
                <div class="layui-collapse" lay-accordion="">
                    <div class="layui-colla-item">
                        <h2 class="layui-colla-title">配置信息</h2>
                        <div class="layui-colla-content">
                            <div id="config-info"></div>
                        </div>
                    </div>
                    <div class="layui-colla-item">
                        <h2 class="layui-colla-title">商品佣金测试</h2>
                        <div class="layui-colla-content">
                            <table class="layui-table">
                                <thead>
                                    <tr>
                                        <th>商品ID</th>
                                        <th>商品名称</th>
                                        <th>集采标识</th>
                                        <th>商品类型</th>
                                        <th>佣金比例</th>
                                        <th>佣金金额(¥100)</th>
                                        <th>商家收入(¥100)</th>
                                    </tr>
                                </thead>
                                <tbody id="goods-table">
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="layui-colla-item">
                        <h2 class="layui-colla-title">订单佣金测试</h2>
                        <div class="layui-colla-content">
                            <table class="layui-table">
                                <thead>
                                    <tr>
                                        <th>订单ID</th>
                                        <th>订单号</th>
                                        <th>订单金额</th>
                                        <th>总佣金</th>
                                        <th>商家收入</th>
                                        <th>商品数量</th>
                                    </tr>
                                </thead>
                                <tbody id="orders-table">
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="layui-colla-item">
                        <h2 class="layui-colla-title">退款佣金测试</h2>
                        <div class="layui-colla-content">
                            <div style="margin-bottom: 15px;">
                                <label>订单ID：</label>
                                <input type="number" id="refund-order-id" placeholder="请输入订单ID" style="width: 150px; margin-right: 10px;">
                                <label>退款金额：</label>
                                <input type="number" id="refund-amount" placeholder="退款金额" step="0.01" style="width: 150px; margin-right: 10px;">
                                <button class="layui-btn layui-btn-sm" onclick="testRefundCommission()">测试退款影响</button>
                            </div>
                            <div id="refund-result"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
layui.use(['element', 'layer'], function(){
    var element = layui.element;
    var layer = layui.layer;
    
    // 测试按钮点击事件
    $('#test-btn').click(function() {
        var loading = layer.load(1, {shade: [0.1,'#fff']});
        
        $.ajax({
            url: '{:url("test.commission/test")}',
            type: 'POST',
            dataType: 'json',
            success: function(res) {
                layer.close(loading);
                
                if (res.code == 1) {
                    displayResults(res.data);
                    $('#test-results').show();
                    layer.msg('测试完成', {icon: 1});
                } else {
                    layer.msg(res.msg, {icon: 2});
                }
            },
            error: function() {
                layer.close(loading);
                layer.msg('请求失败', {icon: 2});
            }
        });
    });
    
    // 更新佣金按钮点击事件
    $('#update-btn').click(function() {
        layer.prompt({title: '请输入订单ID', formType: 0}, function(value, index){
            layer.close(index);

            var loading = layer.load(1, {shade: [0.1,'#fff']});

            $.ajax({
                url: '{:url("test.commission/updateCommission")}',
                type: 'POST',
                data: {order_id: value},
                dataType: 'json',
                success: function(res) {
                    layer.close(loading);
                    layer.msg(res.msg, {icon: res.code == 1 ? 1 : 2});
                },
                error: function() {
                    layer.close(loading);
                    layer.msg('请求失败', {icon: 2});
                }
            });
        });
    });

    // 退款佣金测试按钮点击事件
    $('#refund-test-btn').click(function() {
        $('#test-results').show();
        element.render('collapse');
    });
    
    function displayResults(data) {
        // 显示配置信息
        var configHtml = '<p><strong>普通商品佣金比例:</strong> ' + data.config.normal_goods_commission_ratio + '%</p>';
        configHtml += '<p><strong>集采商品佣金比例:</strong> ' + data.config.jcai_goods_commission_ratio + '%</p>';
        configHtml += '<p><strong>集采众筹商品佣金比例:</strong> ' + data.config.jcai_crowdfunding_commission_ratio + '%</p>';
        $('#config-info').html(configHtml);
        
        // 显示商品测试结果
        var goodsHtml = '';
        if (data.goods && data.goods.length > 0) {
            data.goods.forEach(function(item) {
                goodsHtml += '<tr>';
                goodsHtml += '<td>' + item.id + '</td>';
                goodsHtml += '<td>' + item.name + '</td>';
                goodsHtml += '<td>' + (item.join_jc == 1 ? '是' : '否') + '</td>';
                goodsHtml += '<td>' + item.type_desc + '</td>';
                goodsHtml += '<td>' + item.commission_ratio + '%</td>';
                goodsHtml += '<td>¥' + item.commission_amount + '</td>';
                goodsHtml += '<td>¥' + item.merchant_amount + '</td>';
                goodsHtml += '</tr>';
            });
        }
        $('#goods-table').html(goodsHtml);
        
        // 显示订单测试结果
        var ordersHtml = '';
        if (data.orders && data.orders.length > 0) {
            data.orders.forEach(function(item) {
                ordersHtml += '<tr>';
                ordersHtml += '<td>' + item.id + '</td>';
                ordersHtml += '<td>' + item.order_sn + '</td>';
                ordersHtml += '<td>¥' + item.order_amount + '</td>';
                ordersHtml += '<td>¥' + item.total_commission + '</td>';
                ordersHtml += '<td>¥' + item.total_merchant_amount + '</td>';
                ordersHtml += '<td>' + item.goods_count + '</td>';
                ordersHtml += '</tr>';
            });
        }
        $('#orders-table').html(ordersHtml);
    }
});

// 测试退款佣金影响
function testRefundCommission() {
    var orderId = $('#refund-order-id').val();
    var refundAmount = $('#refund-amount').val();

    if (!orderId) {
        layer.msg('请输入订单ID', {icon: 2});
        return;
    }

    var loading = layer.load(1, {shade: [0.1,'#fff']});

    $.ajax({
        url: '{:url("test.commission/testRefundCommission")}',
        type: 'POST',
        data: {
            order_id: orderId,
            refund_amount: refundAmount
        },
        dataType: 'json',
        success: function(res) {
            layer.close(loading);

            if (res.code == 1) {
                displayRefundResult(res.data);
                layer.msg('测试完成', {icon: 1});
            } else {
                layer.msg(res.msg, {icon: 2});
            }
        },
        error: function() {
            layer.close(loading);
            layer.msg('请求失败', {icon: 2});
        }
    });
}

// 显示退款佣金测试结果
function displayRefundResult(data) {
    var html = '<div class="layui-row layui-col-space15">';

    // 原始佣金信息
    html += '<div class="layui-col-md6">';
    html += '<h4>原始佣金信息</h4>';
    html += '<p><strong>总佣金：</strong>¥' + data.original_commission.total_commission + '</p>';
    html += '<p><strong>商家收入：</strong>¥' + data.original_commission.total_merchant_amount + '</p>';
    html += '</div>';

    // 调整后佣金信息
    html += '<div class="layui-col-md6">';
    html += '<h4>考虑退款后佣金信息</h4>';
    html += '<p><strong>调整后总佣金：</strong>¥' + data.adjusted_commission.total_commission + '</p>';
    html += '<p><strong>调整后商家收入：</strong>¥' + data.adjusted_commission.total_merchant_amount + '</p>';
    html += '</div>';

    html += '</div>';

    // 佣金调整详情
    html += '<div style="margin-top: 20px;">';
    html += '<h4>佣金调整详情</h4>';
    html += '<p><strong>退款金额：</strong>¥' + data.adjustment_info.refund_amount + '</p>';
    html += '<p><strong>佣金调整金额：</strong>¥' + data.adjustment_info.commission_adjustment + '</p>';
    html += '<p><strong>说明：</strong>退款后需要退还给商家的佣金金额</p>';
    html += '</div>';

    // 调整记录
    if (data.adjustment_records && data.adjustment_records.length > 0) {
        html += '<div style="margin-top: 20px;">';
        html += '<h4>历史调整记录</h4>';
        html += '<table class="layui-table">';
        html += '<thead><tr><th>调整类型</th><th>调整金额</th><th>退款金额</th><th>状态</th><th>创建时间</th></tr></thead>';
        html += '<tbody>';
        data.adjustment_records.forEach(function(record) {
            html += '<tr>';
            html += '<td>' + (record.adjustment_type == 1 ? '退款调整' : '其他调整') + '</td>';
            html += '<td>¥' + record.adjustment_amount + '</td>';
            html += '<td>¥' + record.refund_amount + '</td>';
            html += '<td>' + (record.status == 1 ? '待处理' : '已处理') + '</td>';
            html += '<td>' + new Date(record.create_time * 1000).toLocaleString() + '</td>';
            html += '</tr>';
        });
        html += '</tbody></table>';
        html += '</div>';
    }

    $('#refund-result').html(html);
}
</script>
