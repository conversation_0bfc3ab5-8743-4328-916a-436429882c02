<?php


namespace app\shopapi\logic;

use app\common\basics\Logic;
use app\common\model\shop\Shop;
use app\common\server\ConfigServer;
use app\common\server\UrlServer;
use think\facade\Db;

/**
 * 商家移动端管理员默认配置
 * Class LoginLogic
 * @package app\shopapi\logic
 */
class IndexLogic extends Logic
{

    /**
     * @notes 配置信息
     * @return array
     * <AUTHOR>
     * @date 2021/11/13 17:13
     */
    public static function config()
    {
        $config = [
            'platform_name' => ConfigServer::get('website', 'name'),
            'user_agreement' => ConfigServer::get('website_shop', 'user_agreement'),//商家注销须知
            'mnp_lxr_phone' => ConfigServer::get('mnp', 'mnp_lxr_phone'),//商家注销须知
            //厂商联盟海报
            'poster_image' => ConfigServer::get('website_shop', 'poster_image'),
            //客服服务地址
            'kefu_service_url' => ConfigServer::get('communityform', 'kefu_service_url'),
            //商家PC端地址
            'pc_url' => ConfigServer::get('website_shop', 'pc_url','https://www.huohanghang.cn/shop'),

        ];
        return $config;
    }

    /**
     * @notes 版权资质
     * @param $shop_id
     * @return mixed
     * <AUTHOR>
     * @date 2022/2/22 11:10 上午
     */
    public static function copyright($shop_id)
    {
        $result = Shop::where('id', $shop_id)->json(['other_qualifications'], true)->field('business_license,other_qualifications')->findOrEmpty()->toArray();
        $business_license = $result['business_license'] ? UrlServer::getFileUrl($result['business_license']) : '';
        foreach ($result['other_qualifications'] as &$val) {
            $other_qualifications[] = UrlServer::getFileUrl($val);
        }
        if (!empty($business_license)) {
            array_unshift($other_qualifications, $business_license);
        }

        return $other_qualifications;
    }

    /**
     * 申请退保证金
     * @param int $shop_id 商家ID
     * @param string $reason 退款原因
     * @return bool
     */
    public static function applyRefundDeposit($shop_id, $reason)
    {
        try {
            // 查询保证金记录
            $deposit = \think\facade\Db::name('shop_deposit')
                ->where('shop_id', $shop_id)
                ->where('status', 1) // 已审核通过的保证金
                ->where('refund_status', 0) // 未申请退款的
                ->find();

            if (!$deposit) {
                throw new \Exception('未找到可退款的保证金记录或保证金审核未通过');
            }

            // 计算当前保证金余额
            $total_deposit = floatval($deposit['deposit_amount']);
            $changes = \think\facade\Db::name('shop_deposit_details')
                ->where('deposit_id', $deposit['id'])
                ->sum('deposit_change');

            $current_balance = $total_deposit + floatval($changes);
            $current_balance = number_format($current_balance, 2, '.', '');

            // 检查余额是否大于0
            if (floatval($current_balance) <= 0) {
                throw new \Exception('保证金余额为0，无法申请退款');
            }

            // 更新保证金记录
            $updateData = [
                'refund_status' => 1, // 申请中
                'refund_apply_time' => date('Y-m-d H:i:s'),
                'refund_reason' => $reason,
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $result = \think\facade\Db::name('shop_deposit')
                ->where('id', $deposit['id'])
                ->update($updateData);


            return true;
        } catch (\Exception $e) {

            return $e->getMessage();
        }
    }

    /**
     * @notes 获取商家保证金退款警告信息
     * @return array
     */
    public static function getShopDepositRefundNotice()
    {
        // 从配置表中获取商家保证金退款警告图片
        $warning_image = ConfigServer::get('shop_entry', 'refund_warning_image', '');

        // 如果没有配置，使用默认警告信息
        if (empty($warning_image)) {
            $notice = '<div style="padding: 20px; text-align: center; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; color: #856404;">
                <h3>⚠️ 保证金退款重要提示</h3>
                <p>申请保证金退款前，请确保：</p>
                <ul style="text-align: left; display: inline-block;">
                    <li>所有订单已完成处理</li>
                    <li>所有商品已下架</li>
                    <li>所有售后问题已解决</li>
                    <li>已过维权期的订单无争议</li>
                </ul>
                <p><strong>退款申请提交后将进入公示期，期间无法撤销</strong></p>
            </div>';
        } else {
            // 使用配置的警告图片，确保图片URL正确处理
            $image_url = $warning_image;
            if (!empty($image_url) && !str_starts_with($image_url, 'http')) {
                // 如果不是完整URL，添加域名前缀
                $image_url = request()->domain() . $image_url;
            }
            $notice = '<div style="text-align: center;"><img src="' . $image_url . '" style="max-width: 100%; height: auto;" alt="保证金退款警告" /></div>';
        }

        // 获取商家保证金公示期天数
        $publicity_period_days = ConfigServer::get('shop_entry', 'deposit_publicity_days', 7);

        return [
            'notice' => $notice,
            'tui_docs'=> ConfigServer::get('shop_entry', 'deposit_doc'),//商家退保證金協議 33
            'publicity_period_days' => intval($publicity_period_days),
            'warning_text' => '申请保证金退款后将进入 ' . $publicity_period_days . ' 天公示期，期间无法撤销申请，请谨慎操作。'
        ];
    }

    /**
     * @notes 检查商家保证金退款条件
     * @param int $shop_id 商家ID
     * @return array 检查结果
     */
    public static function checkShopDepositRefundCondition($shop_id)
    {
        // 初始化返回结果，格式与代理保证金退款接口一致
        $result = [
            'code' => 1,
            'show' => 0,
            'msg' => '商家保证金退款条件检查结果',
            'data' => [
                'can_deactivate' => true
            ]
        ];

        try {
            // 1. 检查是否有保证金记录
            $deposit = \think\facade\Db::name('shop_deposit')
                ->where('shop_id', $shop_id)
                ->where('status', 1) // 已审核通过的保证金
                ->where('refund_status', 0) // 未申请退款的
                ->order('id', 'desc')
                ->find();

            if (!$deposit) {
                $result['data']['has_deposit'] = [
                    'status' => true,
                    'message' => '未找到有效的保证金支付记录或保证金审核未通过'
                ];
                $result['data']['can_deactivate'] = true;
            } else {
                // 计算当前保证金余额
                $total_deposit = floatval($deposit['deposit_amount']);
                $changes = \think\facade\Db::name('shop_deposit_details')
                    ->where('deposit_id', $deposit['id'])
                    ->sum('deposit_change');

                $current_balance = $total_deposit + floatval($changes);

                if ($current_balance <= 0) {
                    $result['data']['has_deposit'] = [
                        'status' => true,
                        'message' => '保证金余额为0，无法申请退款'
                    ];
                    $result['data']['can_deactivate'] = true;
                } else {
                    $result['data']['has_deposit'] = [
                        'status' => true,
                        'message' => '保证金状态有效，当前余额：' . number_format($current_balance, 2) . '元'
                    ];
                }
            }

            // 2. 检查是否有进行中的集采购订单
            $ongoing_orders = \think\facade\Db::name('order')
                ->where('shop_id', $shop_id)
                ->where('order_status', 'not in', [30, 40]) // 30-已完成, 40-已关闭
                ->where('del', 0)
                ->count();

            if ($ongoing_orders > 0) {
                $result['data']['ongoing_orders'] = [
                    'status' => true,
                    'message' => "您有 {$ongoing_orders} 个订单未完成，请先处理完成"
                ];
                $result['data']['can_deactivate'] = true;
            } else {
                $result['data']['ongoing_orders'] = [
                    'status' => true,
                    'message' => '所有订单已完成'
                ];
            }

            // 3. 检查是否有集采购商品（上架状态的商品）
            $active_goods = \think\facade\Db::name('goods')
                ->where('shop_id', $shop_id)
                ->where('status', 1) // 上架状态
                ->where('del', 0)
                ->count();

            if ($active_goods > 0) {
                $result['data']['active_goods'] = [
                    'status' => true,
                    'message' => "您有 {$active_goods} 个商品仍在上架状态，请先下架所有商品"
                ];
                $result['data']['can_deactivate'] = true;
            } else {
                $result['data']['active_goods'] = [
                    'status' => true,
                    'message' => '所有商品已下架'
                ];
            }

            // 4. 检查拼单集采商品是否有未过维权期的
            $recent_orders = \think\facade\Db::name('order')
                ->where('shop_id', $shop_id)
                ->where('order_status', 30) // 已完成订单
                ->where('confirm_take_time', '>', time() - (15 * 24 * 3600)) // 15天内确认收货的订单
                ->where('del', 0)
                ->count();

            if ($recent_orders > 0) {
                $result['data']['rights_protection'] = [
                    'status' => true,
                    'message' => "您有 {$recent_orders} 个订单仍在维权期内（确认收货后15天），请等待维权期结束"
                ];
                $result['data']['can_deactivate'] = true;
            } else {
                $result['data']['rights_protection'] = [
                    'status' => true,
                    'message' => '所有订单已过维权期'
                ];
            }

            // 5. 检查是否有未发货的商品
            $unshipped_orders = \think\facade\Db::name('order')
                ->where('shop_id', $shop_id)
                ->where('order_status', 'in', [1, 2]) // 1-待发货, 2-待收货
                ->where('del', 0)
                ->count();

            if ($unshipped_orders > 0) {
                $result['data']['unshipped_orders'] = [
                    'status' => true,
                    'message' => "您有 {$unshipped_orders} 个订单未发货或未收货，请先处理完成"
                ];
                $result['data']['can_deactivate'] = true;
            } else {
                $result['data']['unshipped_orders'] = [
                    'status' => true,
                    'message' => '所有订单已发货并收货'
                ];
            }

            // 6. 检查是否有未处理的售后
            $pending_aftersales = \think\facade\Db::name('after_sale')
                ->alias('a')
                ->join('order o', 'a.order_id = o.id')
                ->where('o.shop_id', $shop_id)
                ->where('a.status', 'not in', [40, 50]) // 40-已完成, 50-商家拒绝
                ->where('a.del', 0)
                ->count();

            if ($pending_aftersales > 0) {
                $result['data']['pending_aftersales'] = [
                    'status' => true,
                    'message' => "您有 {$pending_aftersales} 个售后订单未处理完成，请先处理完成"
                ];
                $result['data']['can_deactivate'] = true;
            } else {
                $result['data']['pending_aftersales'] = [
                    'status' => true,
                    'message' => '所有售后已处理完成'
                ];
            }

        } catch (\Exception $e) {
            $result['code'] = 1;
            $result['show'] = 0;
            $result['msg'] = '检查失败：' . $e->getMessage();
            $result['data']['can_deactivate'] = true;
        }

        // 添加公示期相关信息
        $publicity_period_days = ConfigServer::get('shop_entry', 'deposit_publicity_days', 7);
        $result['data']['publicity_info'] = [
            'publicity_period_days' => intval($publicity_period_days),
            'warning_text' => '申请保证金退款后将进入 ' . $publicity_period_days . ' 天公示期，期间将不可使用集采购相关功能,但可以随时撤销申请，请谨慎操作。'
        ];

        // 如果不能退款，更新消息提示
        if (!$result['data']['can_deactivate']) {
            $failed_checks = [];
            foreach ($result['data'] as $check) {
                if (is_array($check) && isset($check['status']) && !$check['status']) {
                    $failed_checks[] = $check['message'];
                }
            }
            if (!empty($failed_checks)) {
                $result['msg'] = '不满足退款条件：' . implode('；', $failed_checks);
                $result['show'] = 0;
            }
        }

        return $result;
    }

    /**
     * @notes 确认商家保证金退款
     * @param int $shop_id 商家ID
     * @param array $post 请求参数
     * @return bool|string|array
     */
    public static function confirmShopDepositRefund($shop_id, $post)
    {
        try {
            // 先检查退款条件
            $checkResult = self::checkShopDepositRefundCondition($shop_id);
            if (!$checkResult['data']['can_deactivate']) {
                // 返回完整的检查结果，让前端可以显示详细信息
                return $checkResult;
            }

            // 查询保证金记录
            $deposit = \think\facade\Db::name('shop_deposit')
                ->where('shop_id', $shop_id)
                ->where('status', 1) // 已审核通过的保证金
                ->where('refund_status', 0) // 未申请退款的
                ->order('id', 'desc')
                ->find();

            if (!$deposit) {
                return [
                    'code' => 0,
                    'show' => 1,
                    'msg' => '未找到有效的保证金支付记录',
                    'data' => [
                        'can_deactivate' => false
                    ]
                ];
            }

            // 计算当前保证金余额
            $total_deposit = floatval($deposit['deposit_amount']);
            $changes = \think\facade\Db::name('shop_deposit_details')
                ->where('deposit_id', $deposit['id'])
                ->sum('deposit_change');

            $current_balance = $total_deposit + floatval($changes);

            if ($current_balance <= 0) {
                return [
                    'code' => 0,
                    'show' => 1,
                    'msg' => '保证金余额为0，无法申请退款',
                    'data' => [
                        'can_deactivate' => false
                    ]
                ];
            }

            // 开始事务
            \think\facade\Db::startTrans();

            // 获取公示期配置
            $publicity_period_days = ConfigServer::get('shop_entry', 'deposit_publicity_days', 7);

            // 计算公示期时间
            $publicity_start_time = date('Y-m-d H:i:s');
            $publicity_end_time = date('Y-m-d H:i:s', time() + ($publicity_period_days * 24 * 3600));

            // 更新保证金记录状态为申请退款中
            $updateData = [
                'refund_status' => 1, // 申请中
                'refund_apply_time' => date('Y-m-d H:i:s'),
                'refund_reason' => $post['reason'] ?? '商家申请退款',
                'refund_publicity_start_time' => $publicity_start_time, // 公示期开始时间
                'refund_publicity_end_time' => $publicity_end_time, // 公示期结束时间
                'updated_at' => date('Y-m-d H:i:s'),
                'images'=>$post['images']??''
            ];

            $result = \think\facade\Db::name('shop_deposit')
                ->where('id', $deposit['id'])
                ->update($updateData);

            if (!$result) {
                throw new \Exception('更新保证金状态失败');
            }

            \think\facade\Db::commit();

            return [
                'code' => 1,
                'show' => 0,
                'msg' => '退款申请提交成功，已进入公示期',
                'data' => [
                    'can_deactivate' => true,
                    'refund_amount' => number_format($current_balance, 2),
                    'publicity_period_days' => intval($publicity_period_days),
                    'publicity_start_time' => $publicity_start_time,
                    'publicity_end_time' => $publicity_end_time,
                    'publicity_end_date' => date('Y-m-d', time() + ($publicity_period_days * 24 * 3600)),
                    'warning_text' => '退款申请已提交，已进入 ' . $publicity_period_days . ' 天公示期，公示期结束后可撤销申请。'
                ]
            ];

        } catch (\Exception $e) {
            \think\facade\Db::rollback();
            return [
                'code' => 0,
                'show' => 1,
                'msg' => '申请失败：' . $e->getMessage(),
                'data' => [
                    'can_deactivate' => false
                ]
            ];
        }
    }

    /**
     * @notes 撤销商家保证金退款申请
     * @param int $shop_id 商家ID
     * @return bool|string|array
     */
    public static function cancelShopDepositRefund($shop_id)
    {
        try {
            // 查询保证金记录，条件：refund_time不为空就能撤销
            $deposit = \think\facade\Db::name('shop_deposit')
                ->where('shop_id', $shop_id)
                ->where('refund_time', 'null')
                ->whereIn('refund_status', [1, 2]) // 申请中或已退款状态
                ->order('id', 'desc')
                ->find();
               
            if (!$deposit) {
                return [
                    'code' => 0,
                    'show' => 1,
                    'msg' => '未找到可撤销的退款记录',
                    'data' => []
                ];
            }

            // 检查是否已经处理完成（如果已经实际退款到账户，则不能撤销）
            if ($deposit['refund_status'] == 2 && !empty($deposit['refund_remark'])) {
                return [
                    'code' => 0,
                    'show' => 1,
                    'msg' => '退款已处理完成，无法撤销',
                    'data' => []
                ];
            }

            // 开始事务
            \think\facade\Db::startTrans();

            // 撤销退款：重置所有退款相关字段
            $updateData = [
                'refund_status' => 0, // 重置为未申请
                'refund_apply_time' => null, // 清空申请时间
                'refund_reason' => null, // 清空申请原因
                'refund_time' => null, // 清空退款时间
                'refund_remark' => null, // 清空退款备注
                'refund_admin_id' => null, // 清空处理管理员
                'refund_publicity_start_time' => null, // 清空公示期开始时间
                'refund_publicity_end_time' => null, // 清空公示期结束时间
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $result = \think\facade\Db::name('shop_deposit')
                ->where('id', $deposit['id'])
                ->update($updateData);

            if (!$result) {
                throw new \Exception('撤销退款失败');
            }

            // 删除相关的退款记录（如果存在）
            \think\facade\Db::name('common_refund')
                ->where('source_id', $deposit['id'])
                ->where('refund_type', 1) // 1-商家保证金
                ->where('refund_status', 0) // 只删除未处理的退款记录
                ->delete();

            \think\facade\Db::commit();

            return [
                'code' => 1,
                'show' => 0,
                'msg' => '退款申请已成功撤销',
                'data' => [
                    'deposit_id' => $deposit['id'],
                    'deposit_amount' => $deposit['deposit_amount'],
                    'cancel_time' => date('Y-m-d H:i:s')
                ]
            ];

        } catch (\Exception $e) {
            \think\facade\Db::rollback();
            return [
                'code' => 0,
                'show' => 1,
                'msg' => '撤销失败：' . $e->getMessage(),
                'data' => []
            ];
        }
    }
}


