# Contributing to the Alibaba Cloud Client for PHP

We work hard to provide a high-quality and useful SDK for Alibaba Cloud, and
we greatly value feedback and contributions from our community. Please submit
your [issues][issues] or [pull requests][pull-requests] through GitHub.

## Tips

- The SDK is released under the [Apache license][license]. Any code you submit
   will be released under that license. For substantial contributions, we may
   ask you to sign a [Alibaba Documentation Corporate Contributor License 
   Agreement (CLA)][cla].
- We follow all of the relevant PSR recommendations from the [PHP Framework
   Interop Group][php-fig]. Please submit code that follows these standards.
   The [PHP CS Fixer][cs-fixer] tool can be helpful for formatting your code.
   Your can use `composer fixer` to fix code.
- We maintain a high percentage of code coverage in our unit tests. If you make
   changes to the code, please add, update, and/or remove tests as appropriate.
- If your code does not conform to the PSR standards, does not include adequate
   tests, or does not contain a changelog document, we may ask you to update
   your pull requests before we accept them. We also reserve the right to deny
   any pull requests that do not align with our standards or goals.

[issues]: https://github.com/aliyun/openapi-sdk-php-client/issues
[pull-requests]: https://github.com/aliyun/openapi-sdk-php-client/pulls
[license]: http://www.apache.org/licenses/LICENSE-2.0
[cla]: https://alibaba-cla-2018.oss-cn-beijing.aliyuncs.com/Alibaba_Documentation_Open_Source_Corporate_CLA.pdf
[php-fig]: http://php-fig.org
[cs-fixer]: http://cs.sensiolabs.org/
[docs-readme]: https://github.com/aliyun/openapi-sdk-php-client/blob/master/README.md
