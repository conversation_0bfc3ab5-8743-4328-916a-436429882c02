(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["bundle-pages-settled_result-settled_result"],{"00b3":function(t,e,i){"use strict";var a=i("6c89"),n=i.n(a);n.a},1522:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return r})),i.d(e,"a",(function(){return a}));var a={uIcon:i("90f3").default},n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"u-image",style:[t.wrapStyle,t.backgroundStyle],on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onClick.apply(void 0,arguments)}}},[t.isError?t._e():i("v-uni-image",{staticClass:"u-image__image",style:{borderRadius:"circle"==t.shape?"50%":t.$u.addUnit(t.borderRadius)},attrs:{src:t.src,mode:t.mode,"lazy-load":t.lazyLoad},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.onErrorHandler.apply(void 0,arguments)},load:function(e){arguments[0]=e=t.$handleEvent(e),t.onLoadHandler.apply(void 0,arguments)}}}),t.showLoading&&t.loading?i("v-uni-view",{staticClass:"u-image__loading",style:{borderRadius:"circle"==t.shape?"50%":t.$u.addUnit(t.borderRadius),backgroundColor:this.bgColor}},[t.$slots.loading?t._t("loading"):i("u-icon",{attrs:{name:t.loadingIcon,width:t.width,height:t.height}})],2):t._e(),t.showError&&t.isError&&!t.loading?i("v-uni-view",{staticClass:"u-image__error",style:{borderRadius:"circle"==t.shape?"50%":t.$u.addUnit(t.borderRadius)}},[t.$slots.error?t._t("error"):i("u-icon",{attrs:{name:t.errorIcon,width:t.width,height:t.height}})],2):t._e()],1)},r=[]},1563:function(t,e,i){var a=i("24fb");e=a(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.settled-result[data-v-087270d8]{padding:%?20?%}.settled-result .result-box .result-header[data-v-087270d8]{padding:0 %?75?% %?50?%;border-radius:%?10?%}.settled-result .result-box .result-header .btn-copy[data-v-087270d8]{width:%?96?%;height:%?42?%;line-height:%?42?%;border:1px solid #e5e5e5}.settled-result .result-box .back-btn[data-v-087270d8]{height:%?88?%;border:1px solid #ccc}.settled-result .result-box .back-btn.primary[data-v-087270d8]{border-color:#ff2c3c;color:#ff2c3c}.settled-result .result-box .result-content[data-v-087270d8]{border-radius:%?10?%}.settled-result .result-box .result-content .apply-form-item[data-v-087270d8]{padding:%?30?% 0}.settled-result .result-box .result-content .apply-form-item[data-v-087270d8]:not(:last-of-type){border-bottom:1px solid #e5e5e5}',""]),t.exports=e},2322:function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("a9e3");var a={name:"u-image",props:{src:{type:String,default:""},mode:{type:String,default:"aspectFill"},width:{type:[String,Number],default:"100%"},height:{type:[String,Number],default:"auto"},shape:{type:String,default:"square"},borderRadius:{type:[String,Number],default:0},lazyLoad:{type:Boolean,default:!0},showMenuByLongpress:{type:Boolean,default:!0},loadingIcon:{type:String,default:"photo"},errorIcon:{type:String,default:"error-circle"},showLoading:{type:Boolean,default:!0},showError:{type:Boolean,default:!0},fade:{type:Boolean,default:!0},webp:{type:Boolean,default:!1},duration:{type:[String,Number],default:500},bgColor:{type:String,default:"#f3f4f6"}},data:function(){return{isError:!1,loading:!0,opacity:1,durationTime:this.duration,backgroundStyle:{}}},watch:{src:{immediate:!0,handler:function(t){t?this.isError=!1:(this.isError=!0,this.loading=!1)}}},computed:{wrapStyle:function(){var t={};return t.width=this.$u.addUnit(this.width),t.height=this.$u.addUnit(this.height),t.borderRadius="circle"==this.shape?"50%":this.$u.addUnit(this.borderRadius),t.overflow=this.borderRadius>0?"hidden":"visible",this.fade&&(t.opacity=this.opacity,t.transition="opacity ".concat(Number(this.durationTime)/1e3,"s ease-in-out")),t}},methods:{onClick:function(){this.$emit("click")},onErrorHandler:function(t){this.loading=!1,this.isError=!0,this.$emit("error",t)},onLoadHandler:function(){var t=this;if(this.loading=!1,this.isError=!1,this.$emit("load"),!this.fade)return this.removeBgColor();this.opacity=0,this.durationTime=0,setTimeout((function(){t.durationTime=t.duration,t.opacity=1,setTimeout((function(){t.removeBgColor()}),t.durationTime)}),50)},removeBgColor:function(){this.backgroundStyle={backgroundColor:"transparent"}}}};e.default=a},"445c":function(t,e,i){"use strict";i.r(e);var a=i("4d92"),n=i("b188");for(var r in n)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(r);i("00b3");var s=i("f0c5"),o=Object(s["a"])(n["default"],a["b"],a["c"],!1,null,"087270d8",null,!1,a["a"],void 0);e["default"]=o.exports},"4d92":function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return r})),i.d(e,"a",(function(){return a}));var a={uImage:i("ba4b").default},n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"settled-result"},[i("v-uni-view",{staticClass:"result-box"},[i("v-uni-view",{staticClass:"result-header bg-white flex-col col-center"},[i("u-image",{staticClass:"m-t-42",attrs:{width:"165rpx",height:"165rpx",src:t.getStatus.img}}),i("v-uni-view",{staticClass:"m-t-32 lg bold"},[t._v(t._s(t.getStatus.text))]),i("v-uni-view",[2==t.applyDetail.audit_status?i("v-uni-view",[i("v-uni-view",{staticClass:"m-t-40 flex flex-wrap"},[i("v-uni-view",{staticClass:"m-r-20"},[t._v("PC管理后台地址："),i("v-uni-text",{staticClass:"lighter"},[t._v(t._s(t.applyDetail.admin_address))])],1),i("v-uni-view",{staticClass:"btn-copy br60 text-center",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onCopy(t.applyDetail.admin_address)}}},[t._v("复制")])],1),i("v-uni-view",{staticClass:"m-t-30 flex flex-wrap"},[i("v-uni-view",{staticClass:"m-r-20"},[t._v("商家账号："),i("v-uni-text",{staticClass:"lighter"},[t._v(t._s(t.applyDetail.account))])],1),i("v-uni-view",{staticClass:"btn-copy br60 text-center",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onCopy(t.applyDetail.account)}}},[t._v("复制")])],1)],1):t._e(),i("v-uni-view",{staticClass:"m-t-20 muted sm text-center"},[t._v(t._s(t.getStatus.desc))]),2!=t.applyDetail.audit_status?i("v-uni-view",{staticClass:"flex"},[3==t.applyDetail.audit_status?i("router-link",{staticClass:"flex-1 m-r-20",attrs:{to:"/bundle/pages/store_settled/store_settled"}},[i("v-uni-view",{staticClass:"br60 flex row-center primary back-btn m-t-60 md"},[t._v("重新提交")])],1):t._e(),i("router-link",{staticClass:"flex-1",attrs:{to:"/pages/index/index",navType:"pushTab"}},[i("v-uni-view",{staticClass:"br60 flex row-center back-btn m-t-60 md"},[t._v("返回首页")])],1)],1):t._e()],1)],1),i("v-uni-view",{staticClass:"result-content bg-white m-t-20 p-20"},[i("v-uni-view",{staticClass:"apply-form-item flex row-between"},[i("v-uni-view",[i("v-uni-text",{staticClass:"primary m-r-10"},[t._v("*")]),t._v("商家名称")],1),i("v-uni-view",[t._v(t._s(t.applyDetail.name))])],1),i("v-uni-view",{staticClass:"apply-form-item flex row-between"},[i("v-uni-view",[i("v-uni-text",{staticClass:"primary m-r-10"},[t._v("*")]),t._v("主营类目")],1),i("v-uni-view",[t._v(t._s(t.applyDetail.cid_desc))])],1),i("v-uni-view",{staticClass:"apply-form-item flex row-between"},[i("v-uni-view",[i("v-uni-text",{staticClass:"primary m-r-10"},[t._v("*")]),t._v("联系人姓名")],1),i("v-uni-view",[t._v(t._s(t.applyDetail.nickname))])],1),i("v-uni-view",{staticClass:"apply-form-item flex row-between"},[i("v-uni-view",[i("v-uni-text",{staticClass:"primary m-r-10"},[t._v("*")]),t._v("手机号码")],1),i("v-uni-view",[t._v(t._s(t.applyDetail.mobile))])],1),i("v-uni-view",{staticClass:"apply-form-item flex row-between"},[i("v-uni-view",[i("v-uni-text",{staticClass:"primary m-r-10"},[t._v("*")]),t._v("商家账号")],1),i("v-uni-view",[t._v(t._s(t.applyDetail.account))])],1),i("v-uni-view",{staticClass:"apply-form-item"},[i("v-uni-view",[i("v-uni-text",{staticClass:"primary m-r-10"},[t._v("*")]),t._v("营业执照")],1),i("v-uni-view",{staticClass:"license-list flex m-t-10 flex-wrap"},t._l(t.license,(function(e,a){return i("v-uni-view",{key:a,staticClass:"m-r-14 m-t-20",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.previewImage(a)}}},[i("u-image",{attrs:{width:"152rpx",height:"152rpx",src:e}})],1)})),1)],1)],1)],1)],1)},r=[]},6021:function(t,e,i){"use strict";var a=i("64b1"),n=i.n(a);n.a},"64b1":function(t,e,i){var a=i("6e35");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("4f06").default;n("3d1e497c",a,!0,{sourceMap:!1,shadowMode:!1})},"6c89":function(t,e,i){var a=i("1563");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("4f06").default;n("19ca634c",a,!0,{sourceMap:!1,shadowMode:!1})},"6e35":function(t,e,i){var a=i("24fb");e=a(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.u-image[data-v-1bf07c9a]{position:relative;transition:opacity .5s ease-in-out}.u-image__image[data-v-1bf07c9a]{width:100%;height:100%}.u-image__loading[data-v-1bf07c9a], .u-image__error[data-v-1bf07c9a]{position:absolute;top:0;left:0;width:100%;height:100%;display:flex;flex-direction:row;align-items:center;justify-content:center;background-color:#f3f4f6;color:#909399;font-size:%?46?%}',""]),t.exports=e},"7e6f":function(t,e,i){"use strict";i("7a82");var a=i("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.apiInvoiceAdd=function(t){return n.default.post("order_invoice/add",t)},e.apiInvoiceDetail=function(t){return n.default.get("order_invoice/detail",{params:t})},e.apiInvoiceEdit=function(t){return n.default.post("order_invoice/edit",t)},e.apiOrderInvoiceDetail=function(t){return n.default.get("order/invoice",{params:t})},e.changeShopFollow=function(t){return n.default.post("shop_follow/changeStatus",t)},e.getInvoiceSetting=function(t){return n.default.get("order_invoice/setting",{params:t})},e.getNearbyShops=function(t){return n.default.get("shop/getNearbyShops",{params:t})},e.getShopCategory=function(){return n.default.get("shop_category/getList")},e.getShopGoodsCategory=function(t){return n.default.get("shop_goods_category/getShopGoodsCategory",{params:t})},e.getShopInfo=function(t){return n.default.get("shop/getShopInfo",{params:t})},e.getShopList=function(t){return n.default.get("shop/getShopList",{params:t})},e.getShopService=function(t){return n.default.get("setting/getShopCustomerService",{params:{shop_id:t}})},e.getTreaty=function(){return n.default.get("ShopApply/getTreaty")},e.shopApply=function(t){return n.default.post("ShopApply/apply",t)},e.shopApplyDetail=function(t){return n.default.get("ShopApply/detail",{params:{id:t}})},e.shopApplyRecord=function(t){return n.default.get("ShopApply/record",{params:t})};var n=a(i("3b33"));i("b08d")},aa9b:function(t,e,i){"use strict";i("7a82");var a=i("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=a(i("f07e")),r=a(i("c964")),s=i("7e6f"),o=i("b08d"),u={data:function(){return{applyDetail:{},license:[]}},methods:{getShopApplyDetail:function(){var t=this;return(0,r.default)((0,n.default)().mark((function e(){var i,a,r;return(0,n.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,(0,s.shopApplyDetail)(t.id);case 2:i=e.sent,a=i.data,r=i.code,1==r&&(t.applyDetail=a,t.license=a.license);case 6:case"end":return e.stop()}}),e)})))()},previewImage:function(t){uni.previewImage({current:t,urls:this.license})},onCopy:function(t){(0,o.copy)(t)}},onLoad:function(t){this.id=this.$Route.query.id,this.getShopApplyDetail()},computed:{getStatus:function(){var t=this.applyDetail.audit_status;switch(t){case 1:return{img:"/static/images/img_store_submit.png",text:"恭喜您，资料提交成功！",desc:"预计在3个工作日内审核完毕，如通过我们将会发送短信通知您，请注意查收！"};case 2:return{img:"/static/images/img_store_success.png",text:"恭喜您，审核已通过！",desc:"温馨提示：密码是您在创建账号时设置的登录密码，如忘记密码可联系官方客服进行修改！"};case 3:return{img:"/static/images/img_store_fail.png",text:"很遗憾，审核不通过！",desc:"请尽量完善您的资料信息再重新提交！"};default:return{}}}}};e.default=u},af8d:function(t,e,i){"use strict";i.r(e);var a=i("2322"),n=i.n(a);for(var r in a)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(r);e["default"]=n.a},b188:function(t,e,i){"use strict";i.r(e);var a=i("aa9b"),n=i.n(a);for(var r in a)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(r);e["default"]=n.a},ba4b:function(t,e,i){"use strict";i.r(e);var a=i("1522"),n=i("af8d");for(var r in n)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(r);i("6021");var s=i("f0c5"),o=Object(s["a"])(n["default"],a["b"],a["c"],!1,null,"1bf07c9a",null,!1,a["a"],void 0);e["default"]=o.exports}}]);