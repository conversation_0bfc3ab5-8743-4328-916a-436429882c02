{layout name="layout2" /}
<style>
    .layui-form-label{
        width: 120px;
    }
    .reqRed::before {
        content: '*';
        color: red;
        margin-right: 5px;
    }
</style>
<div class="layui-form" lay-filter="refund" id="layuiadmin-form-refund" style="padding: 20px 30px 0 0;">
    <input type="hidden" value="{$deposit.id}" name="deposit_id">
    <div class="layui-fluid">
        <div class="layui-card">
            <div class="layui-card-header">申请退款保证金</div>
            <div class="layui-card-body">
                <div class="layui-form-item">
                    <label class="layui-form-label">商家名称：</label>
                    <div class="layui-input-inline">
                        <label class="layui-form-mid">{$deposit.shop_name}</label>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">当前保证金余额：</label>
                    <div class="layui-input-inline">
                        <label class="layui-form-mid">{$deposit.current_amount}</label>
                    </div>
                    <label class="layui-form-mid">元</label>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label reqRed">退款原因：</label>
                    <div class="layui-input-block">
                        <textarea type="text" name="reason" lay-verify="required" lay-vertype="tips" placeholder="请输入退款原因" autocomplete="off" class="layui-textarea" style="width: 50%;"></textarea>
                        <div class="layui-form-mid layui-word-aux" style="margin-top: 5px;">不超过100字</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="layui-form-item layui-hide">
        <input type="button" lay-submit lay-filter="refund_submit" id="refund_submit" value="确认">
    </div>
</div>
<script>
    layui.config({
        version:"{$front_version}",
        base: '/static/lib/' //静态资源所在路径
    }).use(['form'], function(){
        var $ = layui.$, form = layui.form;
        
        // 表单验证
        form.verify({
            reason: function(value) {
                if (!value) {
                    return '请输入退款原因';
                }
                if (value.length > 100) {
                    return '退款原因不能超过100字';
                }
            }
        });
    })
</script>
