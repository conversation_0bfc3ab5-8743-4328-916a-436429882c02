(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["bundle_b-pages-community_topic-community_topic"],{"1e2e":function(t,e,i){var n=i("3096");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var o=i("4f06").default;o("ccafd518",n,!0,{sourceMap:!1,shadowMode:!1})},2625:function(t,e,i){"use strict";i("7a82");var n=i("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.apiCommunityAdd=function(t){return o.default.post("community/addArticle",t)},e.apiCommunityClearSearchHistory=function(){return o.default.post("community_search/clear")},e.apiCommunityCommentAdd=function(t){return o.default.post("community_comment/add",t)},e.apiCommunityCommentLike=function(t){return o.default.post("community/giveLike",t)},e.apiCommunityDel=function(t){return o.default.post("community/delArticle",t)},e.apiCommunityEdit=function(t){return o.default.post("community/editArticle",t)},e.apiCommunityFollow=function(t){return o.default.post("community/follow",t)},e.apiCommunitySetSetting=function(t){return o.default.post("community_user/setSetting",t)},e.getCommunityArticleLists=function(t){return o.default.get("community/articleLists",{params:t})},e.getCommunityCate=function(){return o.default.get("community/cate")},e.getCommunityCommentChildLists=function(t){return o.default.get("community_comment/commentChild",{params:t})},e.getCommunityCommentLists=function(t){return o.default.get("community_comment/lists",{params:t})},e.getCommunityDetail=function(t){return o.default.get("community/detail",{params:t})},e.getCommunityFollow=function(t){return o.default.get("community/followArticle",{params:t})},e.getCommunityGoods=function(t){return o.default.get("community/goods",{params:t})},e.getCommunityGoodsLists=function(t){return o.default.get("community/relationGoods",{params:t})},e.getCommunityLikeLists=function(t){return o.default.get("community/likeLists",{params:t})},e.getCommunityRecommendTopic=function(){return o.default.get("community/recommendTopic")},e.getCommunitySearchHistory=function(){return o.default.get("community_search/lists")},e.getCommunitySetting=function(){return o.default.get("community_user/getSetting")},e.getCommunityShop=function(t){return o.default.get("community/shop",{params:t})},e.getCommunityShopLists=function(t){return o.default.get("community/relationShop",{params:t})},e.getCommunityTopicArticle=function(t){return o.default.get("community/topicArticle",{params:t})},e.getCommunityTopicLists=function(t){return o.default.get("community/topicLists",{params:t})},e.getCommunityUserCenter=function(t){return o.default.get("community_user/center",{params:t})},e.getCommunityWorksLists=function(t){return o.default.get("community/worksLists",{params:t})};var o=n(i("2774"))},"28f7":function(t,e,i){"use strict";var n=i("7983"),o=i.n(n);o.a},"2e7c":function(t,e,i){"use strict";i.r(e);var n=i("37fa"),o=i.n(n);for(var a in n)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(a);e["default"]=o.a},3096:function(t,e,i){var n=i("24fb");e=n(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.u-image[data-v-1bf07c9a]{position:relative;transition:opacity .5s ease-in-out}.u-image__image[data-v-1bf07c9a]{width:100%;height:100%}.u-image__loading[data-v-1bf07c9a], .u-image__error[data-v-1bf07c9a]{position:absolute;top:0;left:0;width:100%;height:100%;display:flex;flex-direction:row;align-items:center;justify-content:center;background-color:#f3f4f6;color:#909399;font-size:%?46?%}',""]),t.exports=e},"32ca":function(t,e,i){var n=i("24fb");e=n(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */uni-page-body[data-v-cae5cabe]{background-color:#fff}body.?%PAGE?%[data-v-cae5cabe]{background-color:#fff}.topic .header[data-v-cae5cabe]{padding:%?40?% %?40?% %?30?% %?30?%;border-top:1px solid #f6f6f6;border-bottom:1px solid #f6f6f6}.topic .header uni-image[data-v-cae5cabe]{width:%?42?%;height:%?42?%}.topic .menu[data-v-cae5cabe]{padding:0 %?30?%}.topic .menu--item[data-v-cae5cabe]{margin:%?20?% 0;font-size:%?28?%;color:#999;margin-right:%?50?%}.topic .menu .active[data-v-cae5cabe]{color:#ff2c3c;font-weight:500}',""]),t.exports=e},"37fa":function(t,e,i){"use strict";i("7a82");var n=i("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("99af");var o=n(i("d0ff")),a=i("2625"),r={data:function(){return{topicId:"",topicName:"",sortHot:!0,sortNew:!1,lists:[],click:0,page:1,more:1,pageSize:10}},onLoad:function(){var t=this.$Route.query;this.topicId=t.id||"",this.topicName=t.name||""},onShow:function(){this.getCommunityTopic()},onReachBottom:function(){console.log("触底"),this.more&&this.getCommunityTopic()},methods:{handleSortHot:function(){this.sortHot=!this.sortHot,this.page=1,this.getCommunityTopic()},handleSortNew:function(){this.sortNew=!this.sortNew,this.page=1,this.getCommunityTopic()},getCommunityTopic:function(){var t=this;(0,a.getCommunityTopicArticle)({topic_id:this.topicId,sort_hot:this.sortHot?"desc":"asc",sort_new:this.sortNew?"desc":"asc",page_no:this.page,page_size:this.pageSize}).then((function(e){1==t.page&&("uWaterfall"in t.$refs&&t.$refs.uWaterfall.clear(),t.lists=[]),1===e.data.more&&(t.page+=1),t.click=e.data.click,t.more=e.data.lists.more,setTimeout((function(){t.lists=[].concat((0,o.default)(t.lists),(0,o.default)(e.data.lists.list))}),0)}))}}};e.default=r},"3f30":function(t,e,i){"use strict";i.r(e);var n=i("4219"),o=i.n(n);for(var a in n)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(a);e["default"]=o.a},4219:function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("a9e3");var n={name:"u-image",props:{src:{type:String,default:""},mode:{type:String,default:"aspectFill"},width:{type:[String,Number],default:"100%"},height:{type:[String,Number],default:"auto"},shape:{type:String,default:"square"},borderRadius:{type:[String,Number],default:0},lazyLoad:{type:Boolean,default:!0},showMenuByLongpress:{type:Boolean,default:!0},loadingIcon:{type:String,default:"photo"},errorIcon:{type:String,default:"error-circle"},showLoading:{type:Boolean,default:!0},showError:{type:Boolean,default:!0},fade:{type:Boolean,default:!0},webp:{type:Boolean,default:!1},duration:{type:[String,Number],default:500},bgColor:{type:String,default:"#f3f4f6"}},data:function(){return{isError:!1,loading:!0,opacity:1,durationTime:this.duration,backgroundStyle:{}}},watch:{src:{immediate:!0,handler:function(t){t?this.isError=!1:(this.isError=!0,this.loading=!1)}}},computed:{wrapStyle:function(){var t={};return t.width=this.$u.addUnit(this.width),t.height=this.$u.addUnit(this.height),t.borderRadius="circle"==this.shape?"50%":this.$u.addUnit(this.borderRadius),t.overflow=this.borderRadius>0?"hidden":"visible",this.fade&&(t.opacity=this.opacity,t.transition="opacity ".concat(Number(this.durationTime)/1e3,"s ease-in-out")),t}},methods:{onClick:function(){this.$emit("click")},onErrorHandler:function(t){this.loading=!1,this.isError=!0,this.$emit("error",t)},onLoadHandler:function(){var t=this;if(this.loading=!1,this.isError=!1,this.$emit("load"),!this.fade)return this.removeBgColor();this.opacity=0,this.durationTime=0,setTimeout((function(){t.durationTime=t.duration,t.opacity=1,setTimeout((function(){t.removeBgColor()}),t.durationTime)}),50)},removeBgColor:function(){this.backgroundStyle={backgroundColor:"transparent"}}}};e.default=n},"5ec1":function(t,e,i){"use strict";i.d(e,"b",(function(){return o})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){return n}));var n={uWaterfall:i("bb4f").default,communityList:i("8783").default,uImage:i("f919").default},o=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"topic"},[i("v-uni-view",{staticClass:"header flex row-between"},[i("v-uni-view",{staticClass:"normal lg bold"},[t._v("# "+t._s(t.topicName))]),i("v-uni-view",{staticClass:"flex"},[i("v-uni-image",{attrs:{src:"/bundle_b/static/icon_see.png"}}),i("v-uni-text",{staticClass:"lighter sm m-l-10"},[t._v(t._s(t.click))])],1)],1),i("v-uni-view",{staticClass:"menu flex"},[i("v-uni-view",{staticClass:"menu--item",class:{active:t.sortHot},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.handleSortHot.apply(void 0,arguments)}}},[t._v("最热")]),i("v-uni-view",{staticClass:"menu--item",class:{active:t.sortNew},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.handleSortNew.apply(void 0,arguments)}}},[t._v("最新")])],1),i("v-uni-view",[t.lists.length?i("v-uni-view",[i("u-waterfall",{ref:"uWaterfall",attrs:{"add-time":200},scopedSlots:t._u([{key:"left",fn:function(t){var e=t.leftList;return[i("v-uni-view",{staticStyle:{padding:"0 9rpx 0 30rpx"}},[i("community-list",{attrs:{width:"336rpx",type:"waterfall",list:e}})],1)]}},{key:"right",fn:function(t){var e=t.rightList;return[i("v-uni-view",{staticStyle:{padding:"0 30rpx 0 9rpx"}},[i("community-list",{attrs:{width:"336rpx",type:"waterfall",list:e}})],1)]}}],null,!1,3463676658),model:{value:t.lists,callback:function(e){t.lists=e},expression:"lists"}})],1):i("v-uni-view",{staticClass:"p-t-60"},[i("v-uni-view",{staticClass:"flex row-center m-t-40"},[i("u-image",{attrs:{width:"300",height:"300",src:"/bundle_b/static/works_null.png"}})],1),i("v-uni-view",{staticClass:"text-center muted m-t-40"},[t._v("暂无话题文章～")])],1)],1)],1)},a=[]},7983:function(t,e,i){var n=i("32ca");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var o=i("4f06").default;o("a5a7e284",n,!0,{sourceMap:!1,shadowMode:!1})},c298:function(t,e,i){"use strict";i.r(e);var n=i("5ec1"),o=i("2e7c");for(var a in o)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(a);i("28f7");var r=i("f0c5"),u=Object(r["a"])(o["default"],n["b"],n["c"],!1,null,"cae5cabe",null,!1,n["a"],void 0);e["default"]=u.exports},c529:function(t,e,i){"use strict";var n=i("1e2e"),o=i.n(n);o.a},f743:function(t,e,i){"use strict";i.d(e,"b",(function(){return o})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){return n}));var n={uIcon:i("6976").default},o=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"u-image",style:[t.wrapStyle,t.backgroundStyle],on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onClick.apply(void 0,arguments)}}},[t.isError?t._e():i("v-uni-image",{staticClass:"u-image__image",style:{borderRadius:"circle"==t.shape?"50%":t.$u.addUnit(t.borderRadius)},attrs:{src:t.src,mode:t.mode,"lazy-load":t.lazyLoad},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.onErrorHandler.apply(void 0,arguments)},load:function(e){arguments[0]=e=t.$handleEvent(e),t.onLoadHandler.apply(void 0,arguments)}}}),t.showLoading&&t.loading?i("v-uni-view",{staticClass:"u-image__loading",style:{borderRadius:"circle"==t.shape?"50%":t.$u.addUnit(t.borderRadius),backgroundColor:this.bgColor}},[t.$slots.loading?t._t("loading"):i("u-icon",{attrs:{name:t.loadingIcon,width:t.width,height:t.height}})],2):t._e(),t.showError&&t.isError&&!t.loading?i("v-uni-view",{staticClass:"u-image__error",style:{borderRadius:"circle"==t.shape?"50%":t.$u.addUnit(t.borderRadius)}},[t.$slots.error?t._t("error"):i("u-icon",{attrs:{name:t.errorIcon,width:t.width,height:t.height}})],2):t._e()],1)},a=[]},f919:function(t,e,i){"use strict";i.r(e);var n=i("f743"),o=i("3f30");for(var a in o)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(a);i("c529");var r=i("f0c5"),u=Object(r["a"])(o["default"],n["b"],n["c"],!1,null,"1bf07c9a",null,!1,n["a"],void 0);e["default"]=u.exports}}]);