<?php

namespace app\admin\controller\shop;


use app\admin\logic\shop\DeactivateLogic;
use app\common\basics\AdminBase;
use app\common\server\ConfigServer;
use app\common\server\JsonServer;
use think\facade\View;

/**
 * 商家注销管理
 * Class Deactivate
 * @package app\admin\controller\shop
 */
class Deactivate extends AdminBase
{
    /**
     * @notes 注销申请列表
     * @return \think\response\Json|\think\response\View
     */
    public function lists()
    {
        if ($this->request->isAjax()) {
            $get = $this->request->get();
            $lists = DeactivateLogic::lists($get);
            return JsonServer::success('获取成功', $lists);
        }
        return view();
    }

    /**
     * @notes 注销申请详情
     * @return \think\response\View
     */
    public function detail()
    {
        $id = $this->request->get('id');
        $detail = DeactivateLogic::detail($id);
        View::assign('detail', $detail);
        return view();
    }

    /**
     * @notes 审核注销申请
     * @return \think\response\Json|\think\response\View
     */
    public function audit()
    {
        if ($this->request->isAjax()) {
            $post = $this->request->post();
            $result = DeactivateLogic::audit($post);
            if ($result) {
                return JsonServer::success('操作成功');
            }
            $error = DeactivateLogic::getError() ?: '操作失败';
            return JsonServer::error($error);
        }
        $id = $this->request->get('id');
        View::assign('id', $id);
        return view();
    }

    /**
     * @notes 执行注销
     * @return \think\response\Json
     */
    public function execute()
    {
        if ($this->request->isAjax()) {
            $post = $this->request->post();
            $result = DeactivateLogic::execute($post);
            if ($result) {
                return JsonServer::success('操作成功', $result);
            }
            $error = DeactivateLogic::getError() ?: '操作失败';
            return JsonServer::error($error);
        }
        return JsonServer::error('请求异常');
    }
}
