(window.webpackJsonp=window.webpackJsonp||[]).push([[54,9],{437:function(e,t,r){"use strict";var n=r(17),o=r(2),c=r(3),l=r(136),f=r(27),m=r(18),d=r(271),v=r(52),h=r(135),x=r(270),w=r(5),_=r(98).f,y=r(44).f,C=r(26).f,N=r(438),k=r(439).trim,S="Number",P=o.Number,I=P.prototype,O=o.TypeError,T=c("".slice),E=c("".charCodeAt),M=function(e){var t=x(e,"number");return"bigint"==typeof t?t:$(t)},$=function(e){var t,r,n,o,c,l,f,code,m=x(e,"number");if(h(m))throw O("Cannot convert a Symbol value to a number");if("string"==typeof m&&m.length>2)if(m=k(m),43===(t=E(m,0))||45===t){if(88===(r=E(m,2))||120===r)return NaN}else if(48===t){switch(E(m,1)){case 66:case 98:n=2,o=49;break;case 79:case 111:n=8,o=55;break;default:return+m}for(l=(c=T(m,2)).length,f=0;f<l;f++)if((code=E(c,f))<48||code>o)return NaN;return parseInt(c,n)}return+m};if(l(S,!P(" 0o1")||!P("0b1")||P("+0x1"))){for(var D,R=function(e){var t=arguments.length<1?0:P(M(e)),r=this;return v(I,r)&&w((function(){N(r)}))?d(Object(t),r,R):t},j=n?_(P):"MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,EPSILON,MAX_SAFE_INTEGER,MIN_SAFE_INTEGER,isFinite,isInteger,isNaN,isSafeInteger,parseFloat,parseInt,fromString,range".split(","),A=0;j.length>A;A++)m(P,D=j[A])&&!m(R,D)&&C(R,D,y(P,D));R.prototype=I,I.constructor=R,f(o,S,R)}},438:function(e,t,r){var n=r(3);e.exports=n(1..valueOf)},439:function(e,t,r){var n=r(3),o=r(33),c=r(16),l=r(440),f=n("".replace),m="["+l+"]",d=RegExp("^"+m+m+"*"),v=RegExp(m+m+"*$"),h=function(e){return function(t){var r=c(o(t));return 1&e&&(r=f(r,d,"")),2&e&&(r=f(r,v,"")),r}};e.exports={start:h(1),end:h(2),trim:h(3)}},440:function(e,t){e.exports="\t\n\v\f\r                　\u2028\u2029\ufeff"},449:function(e,t,r){"use strict";r.r(t);r(437),r(81),r(61),r(24),r(100),r(80),r(99);var n=6e4,o=36e5,c=24*o;function l(e){return(0+e.toString()).slice(-2)}var f={components:{},props:{isSlot:{type:Boolean,default:!1},time:{type:Number,default:0},format:{type:String,default:"hh:mm:ss"},autoStart:{type:Boolean,default:!0}},watch:{time:{immediate:!0,handler:function(e){e&&this.reset()}}},data:function(){return{timeObj:{},formateTime:0}},created:function(){},computed:{},methods:{createTimer:function(e){return setTimeout(e,100)},isSameSecond:function(e,t){return Math.floor(e)===Math.floor(t)},start:function(){this.counting||(this.counting=!0,this.endTime=Date.now()+1e3*this.remain,this.setTimer())},setTimer:function(){var e=this;this.tid=this.createTimer((function(){var t=e.getRemain();e.isSameSecond(t,e.remain)&&0!==t||e.setRemain(t),0!==e.remain&&e.setTimer()}))},getRemain:function(){return Math.max(this.endTime-Date.now(),0)},pause:function(){this.counting=!1,clearTimeout(this.tid)},reset:function(){this.pause(),this.remain=this.time,this.setRemain(this.remain),this.autoStart&&this.start()},setRemain:function(e){var t=this.format;this.remain=e;var time,r=(time=e,{days:Math.floor(time/c),hours:l(Math.floor(time%c/o)),minutes:l(Math.floor(time%o/n)),seconds:l(Math.floor(time%n/1e3))});this.formateTime=function(e,t){var r=t.days,n=t.hours,o=t.minutes,c=t.seconds;return-1!==e.indexOf("dd")&&(e=e.replace("dd",r)),-1!==e.indexOf("hh")&&(e=e.replace("hh",l(n))),-1!==e.indexOf("mm")&&(e=e.replace("mm",l(o))),-1!==e.indexOf("ss")&&(e=e.replace("ss",l(c))),e}(t,r),this.$emit("change",r),0===e&&(this.pause(),this.$emit("finish"))}}},m=r(9),component=Object(m.a)(f,(function(){var e=this,t=e.$createElement,r=e._self._c||t;return e.time>=0?r("div",[r("client-only",[e.isSlot?e._t("default"):r("span",[e._v(e._s(e.formateTime))])],2)],1):e._e()}),[],!1,null,null,null);t.default=component.exports},454:function(e,t,r){"use strict";r.d(t,"d",(function(){return n})),r.d(t,"e",(function(){return o})),r.d(t,"c",(function(){return c})),r.d(t,"b",(function(){return l})),r.d(t,"a",(function(){return f}));var n=5,o={SMS:0,ACCOUNT:1},c={REGISTER:"ZCYZ",FINDPWD:"ZHMM",LOGIN:"YZMDL",SJSQYZ:"SJSQYZ",CHANGE_MOBILE:"BGSJHM",BIND:"BDSJHM"},l={NONE:"",SEX:"sex",NICKNAME:"nickname",AVATAR:"avatar",MOBILE:"mobile"},f={NORMAL:"normal",HANDLING:"apply",FINISH:"finish"}},546:function(e,t,r){var content=r(627);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[e.i,content,""]]),content.locals&&(e.exports=content.locals);(0,r(14).default)("98ca9702",content,!0,{sourceMap:!1})},626:function(e,t,r){"use strict";r(546)},627:function(e,t,r){var n=r(13)(!1);n.push([e.i,'.user-profile{width:980px;padding:10px}.user-profile .user-header{padding:10px 5px;border-bottom:1px solid #e5e5e5}.user-profile .user-container{margin-top:35px}.user-profile .user-container .user-form-item{padding-left:13px;margin-top:24px}.user-profile .user-container .user-form-item .user-form-label{width:60px;text-align:left;margin-right:24px}.user-profile .user-container .user-form-item .user-avatar-upload .avatar-uploader:hover .avatar .mask{display:flex}.user-profile .user-container .user-form-item .user-avatar-upload .avatar-uploader:hover .avatar:after{opacity:1}.user-profile .user-container .user-form-item .user-avatar-upload .avatar-uploader .avatar{position:relative}.user-profile .user-container .user-form-item .user-avatar-upload .avatar-uploader .avatar .mask{display:none;position:absolute}.user-profile .user-container .user-form-item .user-avatar-upload .avatar-uploader .avatar:after{content:"更换头像";position:absolute;transition:opacity .3s ease;opacity:0;width:100%;height:64px;left:0;top:0;border-radius:60px;background-color:rgba(0,0,0,.3);color:#fff;display:flex;flex-direction:row;justify-content:center;align-items:center;font-size:12px}.user-profile .user-container .user-form-item .user-input{width:240px}.user-profile .user-container .user-form-item .el-radio__input.is-checked+.el-radio__label{color:#007aff}.user-profile .user-container .user-form-item .el-input__inner:focus{border-color:#007aff}.user-profile .user-container .user-form-item .el-radio__input.is-checked .el-radio__inner{border-color:#007aff;background:#007aff}.user-profile .user-container .user-form-item .el-radio__inner:hover{border-color:#007aff}.user-profile .user-container .primary-btn{height:32px;width:100px;margin-top:32px;border:none;border-radius:4px;cursor:pointer}.user-profile .user-container .primary-btn:focus{border:none;outline:none}',""]),e.exports=n},674:function(e,t,r){"use strict";r.r(t);r(22),r(19),r(21),r(28),r(20),r(29);var n=r(6),o=r(10),c=(r(51),r(454)),l=r(75),f=r.n(l),m=r(11),d=r(187);function v(object,e){var t=Object.keys(object);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(object);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(object,e).enumerable}))),t.push.apply(t,r)}return t}function h(e){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?v(Object(source),!0).forEach((function(t){Object(o.a)(e,t,source[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(source)):v(Object(source)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(source,t))}))}return e}var x={head:function(){return{title:this.$store.getters.headTitle,link:[{rel:"icon",type:"image/x-icon",href:this.$store.getters.favicon}]}},layout:"user",mounted:function(){this.getUserInfoFun()},data:function(){return{avatar:"",mobile:"",sex:0,createTime:"",sn:"",action:d.a.baseUrl+"/api/file/formimage",nickName:"",radio:1,showChangeNumber:!1,showPwdPop:!1,telephone:"",verifyCode:"",pwd:"",againPwd:"",smsType:c.c.CHANGE_MOBILE,canSendNumber:!0,canSendPwd:!0,fileList:[]}},methods:h(h({},Object(m.b)(["getPublicData"])),{},{getUserInfoFun:function(){var e=this;return Object(n.a)(regeneratorRuntime.mark((function t(){var r;return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,e.$get("user/info");case 2:1==(r=t.sent).code&&(e.avatar=r.data.avatar,e.nickName=r.data.nickname,e.mobile=r.data.mobile,e.sex=r.data.sex,e.radio=e.sex,e.createTime=r.data.create_time,e.sn=r.data.sn);case 4:case"end":return t.stop()}}),t)})))()},saveUserInfo:function(){var e=this;return Object(n.a)(regeneratorRuntime.mark((function t(){var r;return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,e.$post("pc/changeUserInfo",{sex:"男"==e.radio?1:2,nickname:e.nickName});case 2:1==(r=t.sent).code&&(e.$message({message:r.msg,type:"success"}),e.getPublicData());case 4:case"end":return t.stop()}}),t)})))()},closeChangeNumber:function(){this.telephone="",this.verifyCode="",this.showChangeNumber=!1},closePwdPop:function(){this.telephone="",this.verifyCode="",this.showPwdPop=!1},openChangeNumber:function(){this.showChangeNumber=!0,this.smsType=this.mobile?c.c.CHANGE_MOBILE:c.c.BIND},openChangePwdPop:function(){if(""==this.mobile)return this.$message.error("请先绑定手机号");this.showPwdPop=!0,this.smsType=c.c.FINDPWD},sndSmsToPhone:function(){var e=this;return Object(n.a)(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(e.smsType!=c.c.CHANGE_MOBILE&&e.smsType!=c.c.BIND||e.canSendNumber){t.next=4;break}return t.abrupt("return");case 4:if(e.smsType!=c.c.FINDPWD||e.canSendPwd){t.next=6;break}return t.abrupt("return");case 6:if(e.smsType!=c.c.CHANGE_MOBILE||e.telephone){t.next=8;break}return t.abrupt("return",e.$message.error("请输入手机号"));case 8:return t.next=10,e.$post("sms/send",{mobile:e.smsType==c.c.FINDPWD?e.mobile:e.telephone,key:e.smsType});case 10:1==t.sent.code&&(e.smsType==c.c.FINDPWD?e.canSendPwd=!1:e.canSendNumber=!1,e.$message.success("发送成功"));case 12:case"end":return t.stop()}}),t)})))()},changeUserMobile:function(){var e=this;return Object(n.a)(regeneratorRuntime.mark((function t(){var r;return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(e.telephone){t.next=2;break}return t.abrupt("return",e.$message.error("请输入新的手机号码"));case 2:if(e.verifyCode){t.next=4;break}return t.abrupt("return",e.$message.error("请输入验证码"));case 4:return t.next=6,e.$post("user/changeMobile",{mobile:e.mobile,new_mobile:e.telephone,code:e.verifyCode,action:e.mobile?"change":"",client:c.d});case 6:1==(r=t.sent).code&&(e.showChangeNumber=!1,e.$message.success(r.msg),e.getPublicData(),e.getUserInfoFun());case 8:case"end":return t.stop()}}),t)})))()},setPassWord:function(){var e=this;return Object(n.a)(regeneratorRuntime.mark((function t(){var r,n;return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(e.verifyCode){t.next=2;break}return t.abrupt("return",e.$message.error("请输入验证码"));case 2:if(e.pwd){t.next=4;break}return t.abrupt("return",e.$message.error("请输入密码"));case 4:if(e.againPwd){t.next=6;break}return t.abrupt("return",e.$message.error("请输入确认密码"));case 6:if(e.pwd==e.againPwd){t.next=8;break}return t.abrupt("return",e.$message.error("两次密码输入不一致"));case 8:return t.next=10,e.$post("login_password/forget",{mobile:e.mobile,code:e.verifyCode,password:e.pwd,repassword:e.againPwd,client:c.d});case 10:1==(r=t.sent).code&&(e.$message({message:r.msg,type:"success"}),e.showPwdPop=!1,n=r.data.token,f.a.set("token",n,{expires:60}));case 12:case"end":return t.stop()}}),t)})))()},uploadFileSuccess:function(e,t){var r=this;return Object(n.a)(regeneratorRuntime.mark((function t(){var n,o;return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,r.$post("user/setInfo",{field:c.b.AVATAR,value:e.data.uri});case 2:if(1!=(n=t.sent).code){t.next=9;break}return r.$message({message:n.msg,type:"success"}),t.next=7,r.$get("user/info");case 7:1==(o=t.sent).code&&(r.avatar=o.data.avatar,r.nickName=o.data.nickname,r.mobile=o.data.mobile,r.sex=o.data.sex,r.radio=r.sex,r.createTime=o.data.create_time);case 9:case"end":return t.stop()}}),t)})))()}})},w=(r(626),r(9)),component=Object(w.a)(x,(function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"user-profile"},[r("div",{staticClass:"user-header lg"},[e._v("\n        个人资料\n    ")]),e._v(" "),r("div",{staticClass:"user-container"},[r("div",{staticClass:"user-form-item flex"},[r("label",{staticClass:"user-form-label nr"},[e._v("头像")]),e._v(" "),r("div",{staticClass:"user-avatar-upload"},[r("el-upload",{staticClass:"avatar-uploader",attrs:{action:e.action,"show-file-list":!1,"file-list":e.fileList,"on-success":e.uploadFileSuccess,headers:{token:e.$store.state.token}}},[r("div",{staticClass:"avatar"},[r("el-image",{staticStyle:{width:"64px",height:"64px","border-radius":"60px"},attrs:{src:e.avatar}}),e._v(" "),r("div",{staticClass:"mask white"})],1)])],1)]),e._v(" "),r("div",{staticClass:"user-form-item flex"},[r("label",{staticClass:"user-form-label nr"},[e._v("用户ID")]),e._v(" "),r("div",{staticClass:"normal nr"},[e._v(e._s(e.sn))])]),e._v(" "),r("div",{staticClass:"user-form-item flex"},[r("label",{staticClass:"user-form-label nr"},[e._v("昵称")]),e._v(" "),r("el-input",{staticClass:"user-input",attrs:{"suffix-icon":"el-icon-edit"},model:{value:e.nickName,callback:function(t){e.nickName=t},expression:"nickName"}})],1),e._v(" "),r("div",{staticClass:"user-form-item flex"},[r("label",{staticClass:"user-form-label nr"},[e._v("性别")]),e._v(" "),r("el-radio-group",{model:{value:e.radio,callback:function(t){e.radio=t},expression:"radio"}},[r("el-radio",{attrs:{label:"男"}},[e._v("男")]),e._v(" "),r("el-radio",{attrs:{label:"女"}},[e._v("女")])],1)],1),e._v(" "),r("div",{staticClass:"user-form-item flex"},[r("label",{staticClass:"user-form-label nr"},[e._v("手机号")]),e._v(" "),r("div",{staticClass:"normal nr"},[e._v(e._s(e.mobile))]),e._v(" "),r("div",{staticStyle:{color:"#6699CC","margin-left":"13px",cursor:"pointer"},on:{click:e.openChangeNumber}},[e._v("\n                "+e._s(e.mobile?"修改号码":"绑定手机号"))])]),e._v(" "),r("div",{staticClass:"user-form-item flex"},[r("label",{staticClass:"user-form-label nr"},[e._v("注册时间")]),e._v(" "),r("div",{staticClass:"normal nr"},[e._v(e._s(e.createTime))])]),e._v(" "),r("div",{staticClass:"user-form-item flex"},[r("label",{staticClass:"user-form-label nr"},[e._v("登录密码")]),e._v(" "),r("div",{staticClass:"nr",staticStyle:{color:"#6699CC",cursor:"pointer"},on:{click:e.openChangePwdPop}},[e._v("修改密码")])]),e._v(" "),r("button",{staticClass:"primary-btn bg-primary flex-center white",on:{click:e.saveUserInfo}},[e._v("\n            保存\n        ")])]),e._v(" "),r("el-dialog",{attrs:{center:!0,title:e.mobile?"更换手机号":"绑定手机",visible:e.showChangeNumber,width:"40%"},on:{"update:visible":function(t){e.showChangeNumber=t}}},[r("div",[r("el-form",{staticStyle:{width:"50%",margin:"0 auto"}},[r("el-form-item",[r("el-input",{attrs:{placeholder:"请输入新的手机号码"},model:{value:e.telephone,callback:function(t){e.telephone=t},expression:"telephone"}})],1),e._v(" "),r("el-form-item",[r("div",{staticClass:"flex"},[r("el-input",{attrs:{placeholder:"短信验证码"},model:{value:e.verifyCode,callback:function(t){e.verifyCode=t},expression:"verifyCode"}}),e._v(" "),r("el-button",{staticStyle:{"margin-left":"14px"},on:{click:e.sndSmsToPhone}},[e.canSendNumber?r("div",[e._v("获取验证码")]):r("count-down",{attrs:{time:60,format:"ss秒",autoStart:""},on:{finish:function(t){e.canSendNumber=!0}}})],1)],1)])],1)],1),e._v(" "),r("div",{attrs:{slot:"footer"},slot:"footer"},[r("el-button",{staticStyle:{width:"134px"},attrs:{type:"primary"},on:{click:e.changeUserMobile}},[e._v("确认")]),e._v(" "),r("el-button",{staticStyle:{width:"134px"},on:{click:e.closeChangeNumber}},[e._v("取消")])],1)]),e._v(" "),r("el-dialog",{attrs:{title:"设置登录密码",center:!0,visible:e.showPwdPop,width:"40%"},on:{"update:visible":function(t){e.showPwdPop=t}}},[r("div",[r("el-form",{staticStyle:{width:"50%",margin:"0 auto"}},[r("el-form-item",[r("el-input",{attrs:{placeholder:"请输入手机号码"},model:{value:e.mobile,callback:function(t){e.mobile=t},expression:"mobile"}})],1),e._v(" "),r("el-form-item",[r("div",{staticClass:"flex"},[r("el-input",{attrs:{placeholder:"短信验证码"},model:{value:e.verifyCode,callback:function(t){e.verifyCode=t},expression:"verifyCode"}}),e._v(" "),r("el-button",{staticStyle:{"margin-left":"14px"},on:{click:e.sndSmsToPhone}},[e.canSendPwd?r("div",[e._v("获取验证码")]):r("count-down",{attrs:{time:60,format:"ss秒",autoStart:""},on:{finish:function(t){e.canSendPwd=!0}}})],1)],1)]),e._v(" "),r("el-form-item",[r("el-input",{attrs:{type:"password",placeholder:"请输入密码 (数字与字母自由组合)"},model:{value:e.pwd,callback:function(t){e.pwd=t},expression:"pwd"}})],1),e._v(" "),r("el-form-item",[r("el-input",{attrs:{type:"password",placeholder:"再次输入密码"},model:{value:e.againPwd,callback:function(t){e.againPwd=t},expression:"againPwd"}})],1)],1)],1),e._v(" "),r("div",{attrs:{slot:"footer"},slot:"footer"},[r("el-button",{staticStyle:{width:"134px"},attrs:{type:"primary"},on:{click:e.setPassWord}},[e._v("确认")]),e._v(" "),r("el-button",{staticStyle:{width:"134px"},on:{click:e.closePwdPop}},[e._v("取消")])],1)])],1)}),[],!1,null,null,null);t.default=component.exports;installComponents(component,{CountDown:r(449).default})}}]);