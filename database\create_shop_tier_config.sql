-- 创建商家等级配置表（简化版本）
CREATE TABLE `ls_shop_tier_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `tier_code` varchar(20) NOT NULL COMMENT '等级代码：free, member, premium',
  `tier_name` varchar(50) NOT NULL COMMENT '等级名称：0元入驻, 商家会员, 实力厂商',
  `tier_level` tinyint(1) NOT NULL COMMENT '等级数值：0, 1, 2',
  `price` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '等级价格',
  `duration_days` int(11) NOT NULL DEFAULT 365 COMMENT '有效期天数',
  `features` text COMMENT '功能特权JSON配置',
  `limits` text COMMENT '限制配置JSON',
  `description` text COMMENT '等级描述',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用：0=禁用，1=启用',
  `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `create_time` int(11) NOT NULL,
  `update_time` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `tier_code` (`tier_code`),
  UNIQUE KEY `tier_level` (`tier_level`),
  KEY `is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商家等级配置表';

-- 插入默认配置数据
INSERT INTO `ls_shop_tier_config` (`tier_code`, `tier_name`, `tier_level`, `price`, `duration_days`, `features`, `limits`, `description`, `is_active`, `sort_order`, `create_time`, `update_time`) VALUES
('free', '0元入驻', 0, 0.00, 0, '{"goods_limit":50,"basic_decoration":true,"basic_service":true,"marketing_tools":false,"data_analysis":false,"factory_certification":false,"vip_service":false}', '{"goods_count":50,"decoration_templates":["basic"],"marketing_tools":[],"customer_service":"basic"}', '免费注册，享受基础功能，商品发布限制50个', 1, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('member', '商家会员', 1, 1000.00, 365, '{"goods_limit":500,"basic_decoration":true,"advanced_decoration":true,"marketing_tools":true,"data_analysis":true,"factory_certification":false,"vip_service":false}', '{"goods_count":500,"decoration_templates":["basic","advanced"],"marketing_tools":["coupon","discount","group_buy"],"customer_service":"standard"}', '付费会员，享受更多营销工具和数据分析，商品发布限制500个', 1, 2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('premium', '实力厂商', 2, 3000.00, 365, '{"goods_limit":-1,"basic_decoration":true,"advanced_decoration":true,"full_decoration":true,"marketing_tools":true,"data_analysis":true,"advanced_analysis":true,"factory_certification":true,"vip_service":true}', '{"goods_count":-1,"decoration_templates":["basic","advanced","premium"],"marketing_tools":["coupon","discount","group_buy","flash_sale","live_stream"],"customer_service":"vip"}', '最高等级，包含验厂认证和VIP服务，无商品发布限制', 1, 3, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 为现有商家表添加等级字段（如果不存在）
ALTER TABLE `ls_shop` 
ADD COLUMN IF NOT EXISTS `tier_level` tinyint(1) NOT NULL DEFAULT 0 COMMENT '商家等级：0=0元入驻，1=商家会员，2=实力厂商' AFTER `type`,
ADD COLUMN IF NOT EXISTS `tier_expire_time` int(11) NOT NULL DEFAULT 0 COMMENT '等级到期时间' AFTER `expire_time`;

-- 为商家申请表添加等级字段（如果不存在）
ALTER TABLE `ls_shop_apply`
ADD COLUMN IF NOT EXISTS `target_tier_level` tinyint(1) NOT NULL DEFAULT 0 COMMENT '目标等级：0=0元入驻，1=商家会员，2=实力厂商' AFTER `cid`,
ADD COLUMN IF NOT EXISTS `is_prepaid` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否预付费：0=未支付，1=已支付' AFTER `target_tier_level`;

-- 为商家费用表添加等级字段（如果不存在）
ALTER TABLE `ls_shop_merchantfees`
ADD COLUMN IF NOT EXISTS `tier_level` tinyint(1) NOT NULL DEFAULT 1 COMMENT '目标等级：0=0元入驻，1=商家会员，2=实力厂商' AFTER `feetype`,
ADD COLUMN IF NOT EXISTS `tier_type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '费用类型：0=新入驻，1=等级升级，2=续费' AFTER `tier_level`;

-- 数据迁移：根据现有feetype字段设置tier_level
UPDATE `ls_shop_merchantfees` 
SET `tier_level` = CASE 
    WHEN `feetype` = 1 THEN 2  -- 超级商家套餐 -> 实力厂商
    WHEN `feetype` = 0 THEN 1  -- 入驻费 -> 商家会员
    WHEN `feetype` = 2 THEN 2  -- 检验费 -> 实力厂商
    ELSE 1
END
WHERE `tier_level` = 1 AND `feetype` IS NOT NULL;

-- 数据迁移：设置现有商家的等级
UPDATE `ls_shop` 
SET `tier_level` = 2, `tier_expire_time` = `expire_time`
WHERE `yan_fee` = 1 AND `del` = 0;

UPDATE `ls_shop` 
SET `tier_level` = 1, `tier_expire_time` = `expire_time`
WHERE `tier_level` = 0 AND `del` = 0 AND `id` IN (
    SELECT DISTINCT shop_id FROM `ls_shop_merchantfees` WHERE `status` = 1 AND `shop_id` > 0
);

-- 创建商家等级常量类对应的配置
INSERT IGNORE INTO `ls_config` (`type`, `name`, `value`, `desc`) VALUES
('shop_tier', 'tier_levels', '{"0":"0元入驻","1":"商家会员","2":"实力厂商"}', '商家等级配置'),
('shop_tier', 'default_tier', '0', '默认商家等级'),
('shop_tier', 'upgrade_enabled', '1', '是否允许等级升级');
