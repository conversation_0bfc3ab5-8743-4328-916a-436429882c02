(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["bundle-pages-shop_data-shop_data~pages-index-index"],{"15ab":function(e,t,a){"use strict";var n=a("7658"),i=a("57e7");n("Map",(function(e){return function(){return e(this,arguments.length?arguments[0]:void 0)}}),i)},1851:function(e,t,a){"use strict";var n=a("8bdb"),i=a("84d6"),r=a("1cb5");n({target:"Array",proto:!0},{fill:i}),r("fill")},"1a4c":function(e,t,a){"use strict";a.r(t);var n=a("cbb4"),i=a("1c54");for(var r in i)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(r);a("30d0");var o=a("828b"),s=Object(o["a"])(i["default"],n["b"],n["c"],!1,null,"06f9c5d4",null,!1,n["a"],void 0);t["default"]=s.exports},"1af1":function(e,t,a){"use strict";a.r(t);var n=a("45cc"),i=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=i.a},"1c54":function(e,t,a){"use strict";a.r(t);var n=a("7e25"),i=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=i.a},"1cc3":function(e,t,a){"use strict";var n=a("f9cd"),i=a.n(n);i.a},"1cfa":function(e,t,a){"use strict";a("6a54");var n=a("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=n(a("5de6")),r=n(a("fcf3"));a("7a76"),a("c9b5"),a("bf0f"),a("ab80"),a("2797"),a("aa9c"),a("5c47"),a("a1c1"),a("e966"),a("5ef2"),a("0506"),a("473f"),a("c223"),a("fd3c"),a("8f71"),a("f7a5"),a("dc69"),a("1851"),a("4626"),a("5ac7"),a("3efd");var o={version:"v2.5.0-20230101",yAxisWidth:15,xAxisHeight:22,padding:[10,10,10,10],rotate:!1,fontSize:13,fontColor:"#666666",dataPointShape:["circle","circle","circle","circle"],color:["#1890FF","#91CB74","#FAC858","#EE6666","#73C0DE","#3CA272","#FC8452","#9A60B4","#ea7ccc"],linearColor:["#0EE2F8","#2BDCA8","#FA7D8D","#EB88E2","#2AE3A0","#0EE2F8","#EB88E2","#6773E3","#F78A85"],pieChartLinePadding:15,pieChartTextPadding:5,titleFontSize:20,subtitleFontSize:15,radarLabelTextMargin:13},s=function(e){for(var t=arguments.length,a=new Array(t>1?t-1:0),n=1;n<t;n++)a[n-1]=arguments[n];if(null==e)throw new TypeError("[uCharts] Cannot convert undefined or null to object");if(!a||a.length<=0)return e;function i(e,t){for(var a in t)e[a]=e[a]&&"[object Object]"===e[a].toString()?i(e[a],t[a]):e[a]=t[a];return e}return a.forEach((function(t){e=i(e,t)})),e},l={toFixed:function(e,t){return t=t||2,this.isFloat(e)&&(e=e.toFixed(t)),e},isFloat:function(e){return e%1!==0},approximatelyEqual:function(e,t){return Math.abs(e-t)<1e-10},isSameSign:function(e,t){return Math.abs(e)===e&&Math.abs(t)===t||Math.abs(e)!==e&&Math.abs(t)!==t},isSameXCoordinateArea:function(e,t){return this.isSameSign(e.x,t.x)},isCollision:function(e,t){e.end={},e.end.x=e.start.x+e.width,e.end.y=e.start.y-e.height,t.end={},t.end.x=t.start.x+t.width,t.end.y=t.start.y-t.height;var a=t.start.x>e.end.x||t.end.x<e.start.x||t.end.y>e.start.y||t.start.y<e.end.y;return!a}};function c(e,t){var a=e.replace(/^#?([a-f\d])([a-f\d])([a-f\d])$/i,(function(e,t,a,n){return t+t+a+a+n+n})),n=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(a),i=parseInt(n[1],16),r=parseInt(n[2],16),o=parseInt(n[3],16);return"rgba("+i+","+r+","+o+","+t+")"}function u(e,t,a){if(isNaN(e))throw new Error("[uCharts] series数据需为Number格式");a=a||10,t=t||"upper";var n=1;while(a<1)a*=10,n*=10;e="upper"===t?Math.ceil(e*n):Math.floor(e*n);while(e%a!==0)if("upper"===t){if(e==e+1)break;e++}else e--;return e/n}function d(e,t,a,n,i){var r=i.width-i.area[1]-i.area[3],o=a.eachSpacing*(i.chartData.xAxisData.xAxisPoints.length-1);"mount"==i.type&&i.extra&&i.extra.mount&&i.extra.mount.widthRatio&&i.extra.mount.widthRatio>1&&(i.extra.mount.widthRatio>2&&(i.extra.mount.widthRatio=2),o+=(i.extra.mount.widthRatio-1)*a.eachSpacing);var s=t;return t>=0?(s=0,e.uevent.trigger("scrollLeft"),e.scrollOption.position="left",i.xAxis.scrollPosition="left"):Math.abs(t)>=o-r?(s=r-o,e.uevent.trigger("scrollRight"),e.scrollOption.position="right",i.xAxis.scrollPosition="right"):(e.scrollOption.position=t,i.xAxis.scrollPosition=t),s}function h(e,t,a){function n(e){while(e<0)e+=2*Math.PI;while(e>2*Math.PI)e-=2*Math.PI;return e}return e=n(e),t=n(t),a=n(a),t>a&&(a+=2*Math.PI,e<t&&(e+=2*Math.PI)),e>=t&&e<=a}function f(e,t){function a(e,t){return!(!e[t-1]||!e[t+1])&&(e[t].y>=Math.max(e[t-1].y,e[t+1].y)||e[t].y<=Math.min(e[t-1].y,e[t+1].y))}function n(e,t){return!(!e[t-1]||!e[t+1])&&(e[t].x>=Math.max(e[t-1].x,e[t+1].x)||e[t].x<=Math.min(e[t-1].x,e[t+1].x))}var i=.2,r=.2,o=null,s=null,l=null,c=null;if(t<1?(o=e[0].x+(e[1].x-e[0].x)*i,s=e[0].y+(e[1].y-e[0].y)*i):(o=e[t].x+(e[t+1].x-e[t-1].x)*i,s=e[t].y+(e[t+1].y-e[t-1].y)*i),t>e.length-3){var u=e.length-1;l=e[u].x-(e[u].x-e[u-1].x)*r,c=e[u].y-(e[u].y-e[u-1].y)*r}else l=e[t+1].x-(e[t+2].x-e[t].x)*r,c=e[t+1].y-(e[t+2].y-e[t].y)*r;return a(e,t+1)&&(c=e[t+1].y),a(e,t)&&(s=e[t].y),n(e,t+1)&&(l=e[t+1].x),n(e,t)&&(o=e[t].x),(s>=Math.max(e[t].y,e[t+1].y)||s<=Math.min(e[t].y,e[t+1].y))&&(s=e[t].y),(c>=Math.max(e[t].y,e[t+1].y)||c<=Math.min(e[t].y,e[t+1].y))&&(c=e[t+1].y),(o>=Math.max(e[t].x,e[t+1].x)||o<=Math.min(e[t].x,e[t+1].x))&&(o=e[t].x),(l>=Math.max(e[t].x,e[t+1].x)||l<=Math.min(e[t].x,e[t+1].x))&&(l=e[t+1].x),{ctrA:{x:o,y:s},ctrB:{x:l,y:c}}}function p(e,t,a){return{x:a.x+e,y:a.y-t}}function g(e,t){if(t)while(l.isCollision(e,t))e.start.x>0?e.start.y--:e.start.x<0||e.start.y>0?e.start.y++:e.start.y--;return e}function x(e,t,a){for(var n=0,i=0;i<e.length;i++){var r=e[i];if(r.color||(r.color=a.color[n],n=(n+1)%a.color.length),r.linearIndex||(r.linearIndex=i),r.index||(r.index=0),r.type||(r.type=t.type),"undefined"==typeof r.show&&(r.show=!0),r.type||(r.type=t.type),r.pointShape||(r.pointShape="circle"),!r.legendShape)switch(r.type){case"line":r.legendShape="line";break;case"column":case"bar":r.legendShape="rect";break;case"area":case"mount":r.legendShape="triangle";break;default:r.legendShape="circle"}}return e}function v(e,t,a,n){var i=t||[];if("custom"==e&&0==i.length&&(i=n.linearColor),"custom"==e&&i.length<a.length)for(var r=a.length-i.length,o=0;o<r;o++)i.push(n.linearColor[(o+1)%n.linearColor.length]);return i}function m(e,t){var a=0,n=t-e;return a=n>=1e4?1e3:n>=1e3?100:n>=100?10:n>=10?5:n>=1?1:n>=.1?.1:n>=.01?.01:n>=.001?.001:n>=1e-4?1e-4:n>=1e-5?1e-5:1e-6,{minRange:u(e,"lower",a),maxRange:u(t,"upper",a)}}function y(e,t,a){var n=0;if(e=String(e),!1!==a&&void 0!==a&&a.setFontSize&&a.measureText)return a.setFontSize(t),a.measureText(e).width;e=e.split("");for(var i=0;i<e.length;i++){var r=e[i];/[a-zA-Z]/.test(r)?n+=7:/[0-9]/.test(r)?n+=5.5:/\./.test(r)?n+=2.7:/-/.test(r)?n+=3.25:/:/.test(r)?n+=2.5:/[\u4e00-\u9fa5]/.test(r)?n+=10:/\(|\)/.test(r)?n+=3.73:/\s/.test(r)?n+=2.5:/%/.test(r)?n+=8:n+=10}return n*t/10}function b(e){return e.reduce((function(e,t){return(e.data?e.data:e).concat(t.data)}),[])}function w(e,t){for(var a=new Array(t),n=0;n<a.length;n++)a[n]=0;for(var i=0;i<e.length;i++)for(n=0;n<a.length;n++)a[n]+=e[i].data[n];return e.reduce((function(e,t){return(e.data?e.data:e).concat(t.data).concat(a)}),[])}function S(e,t,a){var n,i;return e.clientX?t.rotate?(i=t.height-e.clientX*t.pix,n=(e.pageY-a.currentTarget.offsetTop-t.height/t.pix/2*(t.pix-1))*t.pix):(n=e.clientX*t.pix,i=(e.pageY-a.currentTarget.offsetTop-t.height/t.pix/2*(t.pix-1))*t.pix):t.rotate?(i=t.height-e.x*t.pix,n=e.y*t.pix):(n=e.x*t.pix,i=e.y*t.pix),{x:n,y:i}}function k(e,t,a){var n=[],i=[],r=t.constructor.toString().indexOf("Array")>-1;if(r)for(var o=D(e),s=0;s<a.length;s++)i.push(o[a[s]]);else i=e;for(var l=0;l<i.length;l++){var c=i[l],u=-1;if(u=r?t[l]:t,null!==c.data[u]&&"undefined"!==typeof c.data[u]&&c.show){var d={};d.color=c.color,d.type=c.type,d.style=c.style,d.pointShape=c.pointShape,d.disableLegend=c.disableLegend,d.legendShape=c.legendShape,d.name=c.name,d.show=c.show,d.data=c.formatter?c.formatter(c.data[u]):c.data[u],n.push(d)}}return n}function A(e,t,a){var n=e.map((function(e){return y(e,t,a)}));return Math.max.apply(null,n)}function T(e){for(var t=2*Math.PI/e,a=[],n=0;n<e;n++)a.push(t*n);return a.map((function(e){return-1*e+Math.PI/2}))}function _(e,t,a,n,i){var r=arguments.length>5&&void 0!==arguments[5]?arguments[5]:{},o=t.chartData.calPoints?t.chartData.calPoints:[],s={};if(n.length>0){for(var l=[],c=0;c<n.length;c++)l.push(o[n[c]]);s=l[0][a[0]]}else for(var u=0;u<o.length;u++)if(o[u][a]){s=o[u][a];break}var d=e.map((function(e){var n=null;return t.categories&&t.categories.length>0&&(n=i[a]),{text:r.formatter?r.formatter(e,n,a,t):e.name+": "+e.data,color:e.color,legendShape:"auto"==t.extra.tooltip.legendShape?e.legendShape:t.extra.tooltip.legendShape}})),h={x:Math.round(s.x),y:Math.round(s.y)};return{textList:d,offset:h}}function P(e,t,a,n){var i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:{},r=t.chartData.xAxisPoints[a]+t.chartData.eachSpacing/2,o=e.map((function(e){return{text:i.formatter?i.formatter(e,n[a],a,t):e.name+": "+e.data,color:e.color,disableLegend:!!e.disableLegend,legendShape:"auto"==t.extra.tooltip.legendShape?e.legendShape:t.extra.tooltip.legendShape}}));o=o.filter((function(e){if(!0!==e.disableLegend)return e}));var s={x:Math.round(r),y:0};return{textList:o,offset:s}}function C(e,t,a,n,i,r){var o=a.chartData.calPoints,s=r.color.upFill,l=r.color.downFill,c=[s,s,l,s],u=[];t.map((function(t){0==n?t.data[1]-t.data[0]<0?c[1]=l:c[1]=s:(t.data[0]<e[n-1][1]&&(c[0]=l),t.data[1]<t.data[0]&&(c[1]=l),t.data[2]>e[n-1][1]&&(c[2]=s),t.data[3]<e[n-1][1]&&(c[3]=l));var i={text:"开盘："+t.data[0],color:c[0],legendShape:"auto"==a.extra.tooltip.legendShape?t.legendShape:a.extra.tooltip.legendShape},r={text:"收盘："+t.data[1],color:c[1],legendShape:"auto"==a.extra.tooltip.legendShape?t.legendShape:a.extra.tooltip.legendShape},o={text:"最低："+t.data[2],color:c[2],legendShape:"auto"==a.extra.tooltip.legendShape?t.legendShape:a.extra.tooltip.legendShape},d={text:"最高："+t.data[3],color:c[3],legendShape:"auto"==a.extra.tooltip.legendShape?t.legendShape:a.extra.tooltip.legendShape};u.push(i,r,o,d)}));for(var d=[],h={x:0,y:0},f=0;f<o.length;f++){var p=o[f];"undefined"!==typeof p[n]&&null!==p[n]&&d.push(p[n])}return h.x=Math.round(d[0][0].x),{textList:u,offset:h}}function D(e){for(var t=[],a=0;a<e.length;a++)1==e[a].show&&t.push(e[a]);return t}function M(e,t,a){return e.x<=t.width-t.area[1]+10&&e.x>=t.area[3]-10&&e.y>=t.area[0]&&e.y<=t.height-t.area[2]}function I(e,t,a){return Math.pow(e.x-t.x,2)+Math.pow(e.y-t.y,2)<=Math.pow(a,2)}function L(e,t){var a=[],n=[];return e.forEach((function(e,i){t.connectNulls?null!==e&&n.push(e):null!==e?n.push(e):(n.length&&a.push(n),n=[])})),n.length&&a.push(n),a}function O(e,t,a,n,i){var r={angle:0,xAxisHeight:t.xAxis.lineHeight*t.pix+t.xAxis.marginTop*t.pix},o=t.xAxis.fontSize*t.pix,s=e.map((function(e,a){var n=t.xAxis.formatter?t.xAxis.formatter(e,a,t):e;return y(String(n),o,i)})),l=Math.max.apply(this,s);if(1==t.xAxis.rotateLabel){r.angle=t.xAxis.rotateAngle*Math.PI/180;var c=t.xAxis.marginTop*t.pix*2+Math.abs(l*Math.sin(r.angle));c=c<o+t.xAxis.marginTop*t.pix*2?c+t.xAxis.marginTop*t.pix*2:c,r.xAxisHeight=c}return t.enableScroll&&t.xAxis.scrollShow&&(r.xAxisHeight+=6*t.pix),t.xAxis.disabled&&(r.xAxisHeight=0),r}function F(e,t,a,n){var i=s({},{type:""},t.extra.bar),o={angle:0,xAxisHeight:t.xAxis.lineHeight*t.pix+t.xAxis.marginTop*t.pix};o.ranges=function(e,t,a,n){var i,o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:-1;i="stack"==n?w(e,t.categories.length):b(e);var s=[];i=i.filter((function(e){return"object"===(0,r.default)(e)&&null!==e?e.constructor.toString().indexOf("Array")>-1?null!==e:null!==e.value:null!==e})),i.map((function(e){"object"===(0,r.default)(e)?e.constructor.toString().indexOf("Array")>-1?"candle"==t.type?e.map((function(e){s.push(e)})):s.push(e[0]):s.push(e.value):s.push(e)}));var l=0,c=0;if(s.length>0&&(l=Math.min.apply(this,s),c=Math.max.apply(this,s)),o>-1?("number"===typeof t.xAxis.data[o].min&&(l=Math.min(t.xAxis.data[o].min,l)),"number"===typeof t.xAxis.data[o].max&&(c=Math.max(t.xAxis.data[o].max,c))):("number"===typeof t.xAxis.min&&(l=Math.min(t.xAxis.min,l)),"number"===typeof t.xAxis.max&&(c=Math.max(t.xAxis.max,c))),l===c){var u=c||10;c+=u}for(var d=l,h=c,f=[],p=(h-d)/t.xAxis.splitNumber,g=0;g<=t.xAxis.splitNumber;g++)f.push(d+p*g);return f}(e,t,a,i.type),o.rangesFormat=o.ranges.map((function(e){return e=l.toFixed(e,2),e}));var c=o.ranges.map((function(e){return e=l.toFixed(e,2),e}));o=Object.assign(o,K(c,t,a));o.eachSpacing,c.map((function(e){return y(e,t.xAxis.fontSize*t.pix,n)}));return!0===t.xAxis.disabled&&(o.xAxisHeight=0),o}function E(e,t,a,n,i){var r=arguments.length>5&&void 0!==arguments[5]?arguments[5]:1,o=i.extra.radar||{};o.max=o.max||0;for(var s=Math.max(o.max,Math.max.apply(null,b(n))),l=[],c=function(i){var o=n[i],c={};c.color=o.color,c.legendShape=o.legendShape,c.pointShape=o.pointShape,c.data=[],o.data.forEach((function(n,i){var o={};o.angle=e[i],o.proportion=n/s,o.value=n,o.position=p(a*o.proportion*r*Math.cos(o.angle),a*o.proportion*r*Math.sin(o.angle),t),c.data.push(o)})),l.push(c)},u=0;u<n.length;u++)c(u);return l}function R(e,t){for(var a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,n=0,i=0,r=0;r<e.length;r++){var o=e[r];o.data=null===o.data?0:o.data,n+=o.data}for(var s=0;s<e.length;s++){var l=e[s];l.data=null===l.data?0:l.data,l._proportion_=0===n?1/e.length*a:l.data/n*a,l._radius_=t}for(var c=0;c<e.length;c++){var u=e[c];u._start_=i,i+=2*u._proportion_*Math.PI}return e}function B(e,t,a,n){for(var i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,r=0;r<e.length;r++)"funnel"==a.type?e[r].radius=e[r].data/e[0].data*t*i:e[r].radius=n*(e.length-r)/(n*e.length)*t*i,e[r]._proportion_=e[r].data/e[0].data;return e}function N(e,t,a,n){for(var i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,r=0,o=0,s=[],l=0;l<e.length;l++){var c=e[l];c.data=null===c.data?0:c.data,r+=c.data,s.push(c.data)}for(var u=Math.min.apply(null,s),d=Math.max.apply(null,s),h=n-a,f=0;f<e.length;f++){var p=e[f];p.data=null===p.data?0:p.data,0===r?(p._proportion_=1/e.length*i,p._rose_proportion_=1/e.length*i):(p._proportion_=p.data/r*i,p._rose_proportion_="area"==t?1/e.length*i:p.data/r*i),p._radius_=a+h*((p.data-u)/(d-u))||n}for(var g=0;g<e.length;g++){var x=e[g];x._start_=o,o+=2*x._rose_proportion_*Math.PI}return e}function U(e,t){var a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1;1==a&&(a=.999999);for(var n=0;n<e.length;n++){var i=e[n];i.data=null===i.data?0:i.data;var r=void 0;r="circle"==t.type?2:"ccw"==t.direction?t.startAngle<t.endAngle?2+t.startAngle-t.endAngle:t.startAngle-t.endAngle:t.endAngle<t.startAngle?2+t.endAngle-t.startAngle:t.startAngle-t.endAngle,i._proportion_=r*i.data*a+t.startAngle,"ccw"==t.direction&&(i._proportion_=t.startAngle-r*i.data*a),i._proportion_>=2&&(i._proportion_=i._proportion_%2)}return e}function z(e,t){var a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1;1==a&&(a=.999999);for(var n=0;n<e.length;n++){var i=e[n];i.data=null===i.data?0:i.data;var r=void 0;r="circle"==t.type?2:t.endAngle<t.startAngle?2+t.endAngle-t.startAngle:t.startAngle-t.endAngle,i._proportion_=r*i.data*a+t.startAngle,i._proportion_>=2&&(i._proportion_=i._proportion_%2)}return e}function W(e,t,a){var n;n=a<t?2+a-t:t-a;for(var i=t,r=0;r<e.length;r++)e[r].value=null===e[r].value?0:e[r].value,e[r]._startAngle_=i,e[r]._endAngle_=n*e[r].value+t,e[r]._endAngle_>=2&&(e[r]._endAngle_=e[r]._endAngle_%2),i=e[r]._endAngle_;return e}function j(e,t,a){for(var n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1,i=0;i<e.length;i++){var r=e[i];if(r.data=null===r.data?0:r.data,"auto"==a.pointer.color){for(var o=0;o<t.length;o++)if(r.data<=t[o].value){r.color=t[o].color;break}}else r.color=a.pointer.color;var s=void 0;s=a.endAngle<a.startAngle?2+a.endAngle-a.startAngle:a.startAngle-a.endAngle,r._endAngle_=s*r.data+a.startAngle,r._oldAngle_=a.oldAngle,a.oldAngle<a.endAngle&&(r._oldAngle_+=2),r.data>=a.oldData?r._proportion_=(r._endAngle_-r._oldAngle_)*n+a.oldAngle:r._proportion_=r._oldAngle_-(r._oldAngle_-r._endAngle_)*n,r._proportion_>=2&&(r._proportion_=r._proportion_%2)}return e}function q(e,t,a,n,i,r){return e.map((function(e){if(null===e)return null;var i=0,o=0;return"mix"==r.type?(i=r.extra.mix.column.seriesGap*r.pix||0,o=r.extra.mix.column.categoryGap*r.pix||0):(i=r.extra.column.seriesGap*r.pix||0,o=r.extra.column.categoryGap*r.pix||0),i=Math.min(i,t/a),o=Math.min(o,t/a),e.width=Math.ceil((t-2*o-i*(a-1))/a),r.extra.mix&&r.extra.mix.column.width&&+r.extra.mix.column.width>0&&(e.width=Math.min(e.width,+r.extra.mix.column.width*r.pix)),r.extra.column&&r.extra.column.width&&+r.extra.column.width>0&&(e.width=Math.min(e.width,+r.extra.column.width*r.pix)),e.width<=0&&(e.width=1),e.x+=(n+.5-a/2)*(e.width+i),e}))}function G(e,t,a,n,i,r){return e.map((function(e){if(null===e)return null;var i=0,o=0;return i=r.extra.bar.seriesGap*r.pix||0,o=r.extra.bar.categoryGap*r.pix||0,i=Math.min(i,t/a),o=Math.min(o,t/a),e.width=Math.ceil((t-2*o-i*(a-1))/a),r.extra.bar&&r.extra.bar.width&&+r.extra.bar.width>0&&(e.width=Math.min(e.width,+r.extra.bar.width*r.pix)),e.width<=0&&(e.width=1),e.y+=(n+.5-a/2)*(e.width+i),e}))}function H(e,t,a,n,i,r,o){var s=r.extra.column.categoryGap*r.pix||0;return e.map((function(e){return null===e?null:(e.width=t-2*s,r.extra.column&&r.extra.column.width&&+r.extra.column.width>0&&(e.width=Math.min(e.width,+r.extra.column.width*r.pix)),n>0&&(e.width-=o),e)}))}function J(e,t,a,n,i,r,o){var s=r.extra.column.categoryGap*r.pix||0;return e.map((function(e,a){return null===e?null:(e.width=Math.ceil(t-2*s),r.extra.column&&r.extra.column.width&&+r.extra.column.width>0&&(e.width=Math.min(e.width,+r.extra.column.width*r.pix)),e.width<=0&&(e.width=1),e)}))}function Y(e,t,a,n,i,r,o){var s=r.extra.bar.categoryGap*r.pix||0;return e.map((function(e,a){return null===e?null:(e.width=Math.ceil(t-2*s),r.extra.bar&&r.extra.bar.width&&+r.extra.bar.width>0&&(e.width=Math.min(e.width,+r.extra.bar.width*r.pix)),e.width<=0&&(e.width=1),e)}))}function K(e,t,a){var n=t.width-t.area[1]-t.area[3],i=t.enableScroll?Math.min(t.xAxis.itemCount,e.length):e.length;("line"==t.type||"area"==t.type||"scatter"==t.type||"bubble"==t.type||"bar"==t.type)&&i>1&&"justify"==t.xAxis.boundaryGap&&(i-=1);var r=0;"mount"==t.type&&t.extra&&t.extra.mount&&t.extra.mount.widthRatio&&t.extra.mount.widthRatio>1&&(t.extra.mount.widthRatio>2&&(t.extra.mount.widthRatio=2),r=t.extra.mount.widthRatio-1,i+=r);var o=n/i,s=[],l=t.area[3],c=t.width-t.area[1];return e.forEach((function(e,t){s.push(l+r/2*o+t*o)})),"justify"!==t.xAxis.boundaryGap&&(!0===t.enableScroll?s.push(l+r*o+e.length*o):s.push(c)),{xAxisPoints:s,startX:l,endX:c,eachSpacing:o}}function X(e,t,a,n,i,r,o){var s=arguments.length>7&&void 0!==arguments[7]?arguments[7]:1,l=[],c=r.height-r.area[0]-r.area[2];return e.forEach((function(e,o){if(null===e)l.push(null);else{var u=[];e.forEach((function(e,l){var d={};d.x=n[o]+Math.round(i/2);var h=e.value||e,f=c*(h-t)/(a-t);f*=s,d.y=r.height-Math.round(f)-r.area[2],u.push(d)})),l.push(u)}})),l}function V(e,t,a,n,i,o,s){var l=arguments.length>7&&void 0!==arguments[7]?arguments[7]:1,c="center";"line"!=o.type&&"area"!=o.type&&"scatter"!=o.type&&"bubble"!=o.type||(c=o.xAxis.boundaryGap);var u=[],d=o.height-o.area[0]-o.area[2],h=o.width-o.area[1]-o.area[3];return e.forEach((function(e,s){if(null===e)u.push(null);else{var f={};f.color=e.color,f.x=n[s];var p,g,x,v=e;if("object"===(0,r.default)(e)&&null!==e)if(e.constructor.toString().indexOf("Array")>-1)p=[].concat(o.chartData.xAxisData.ranges),g=p.shift(),x=p.pop(),v=e[1],f.x=o.area[3]+h*(e[0]-g)/(x-g),"bubble"==o.type&&(f.r=e[2],f.t=e[3]);else v=e.value;"center"==c&&(f.x+=i/2);var m=d*(v-t)/(a-t);m*=l,f.y=o.height-m-o.area[2],u.push(f)}})),u}function Q(e,t,a,n,i,o,s,l,c){c=arguments.length>8&&void 0!==arguments[8]?arguments[8]:1;var u=o.xAxis.boundaryGap,d=[],h=o.height-o.area[0]-o.area[2],f=o.width-o.area[1]-o.area[3];return e.forEach((function(e,s){if(null===e)d.push(null);else{var p={};if(p.color=e.color,"vertical"==l.animation){p.x=n[s];var g,x,v,m=e;if("object"===(0,r.default)(e)&&null!==e)if(e.constructor.toString().indexOf("Array")>-1)g=[].concat(o.chartData.xAxisData.ranges),x=g.shift(),v=g.pop(),m=e[1],p.x=o.area[3]+f*(e[0]-x)/(v-x);else m=e.value;"center"==u&&(p.x+=i/2);var y=h*(m-t)/(a-t);y*=c,p.y=o.height-y-o.area[2],d.push(p)}else{p.x=n[0]+i*s*c;m=e;"center"==u&&(p.x+=i/2);y=h*(m-t)/(a-t);p.y=o.height-y-o.area[2],d.push(p)}}})),d}function Z(e,t,a,n,i,o,s,l,c){c=arguments.length>8&&void 0!==arguments[8]?arguments[8]:1;var u=[],d=o.height-o.area[0]-o.area[2],h=o.width-o.area[1]-o.area[3];return e.forEach((function(e,s){if(null===e)u.push(null);else{var l={};l.color=e.color,l.x=n[s];var f,p,g,x=e;if("object"===(0,r.default)(e)&&null!==e)if(e.constructor.toString().indexOf("Array")>-1)f=[].concat(o.chartData.xAxisData.ranges),p=f.shift(),g=f.pop(),x=e[1],l.x=o.area[3]+h*(e[0]-p)/(g-p);else x=e.value;l.x+=i/2;var v=d*(x*c-t)/(a-t);l.y=o.height-v-o.area[2],u.push(l)}})),u}function $(e,t,a,n,i,r,o,s){var l=arguments.length>8&&void 0!==arguments[8]?arguments[8]:1,c=[],u=r.height-r.area[0]-r.area[2],d=(r.width,r.area[1],r.area[3],i*o.widthRatio);return e.forEach((function(e,o){if(null===e)c.push(null);else{var s={};s.color=e.color,s.x=n[o],s.x+=i/2;var h=e.data,f=u*(h*l-t)/(a-t);s.y=r.height-f-r.area[2],s.value=h,s.width=d,c.push(s)}})),c}function ee(e,t,a,n,i,o,s){var l=arguments.length>7&&void 0!==arguments[7]?arguments[7]:1,c=[],u=(o.height,o.area[0],o.area[2],o.width-o.area[1]-o.area[3]);return e.forEach((function(e,i){if(null===e)c.push(null);else{var s={};s.color=e.color,s.y=n[i];var d=e;"object"===(0,r.default)(e)&&null!==e&&(d=e.value);var h=u*(d-t)/(a-t);h*=l,s.height=h,s.value=d,s.x=h+o.area[3],c.push(s)}})),c}function te(e,t,a,n,i,o,s,l,c){var u=arguments.length>9&&void 0!==arguments[9]?arguments[9]:1,d=[],h=o.height-o.area[0]-o.area[2];return e.forEach((function(e,s){if(null===e)d.push(null);else{var f={};if(f.color=e.color,f.x=n[s]+Math.round(i/2),l>0){for(var p=0,g=0;g<=l;g++)p+=c[g].data[s];var x=p-e,v=h*(p-t)/(a-t),m=h*(x-t)/(a-t)}else{p=e;"object"===(0,r.default)(e)&&null!==e&&(p=e.value);v=h*(p-t)/(a-t),m=0}var y=m;v*=u,y*=u,f.y=o.height-Math.round(v)-o.area[2],f.y0=o.height-Math.round(y)-o.area[2],d.push(f)}})),d}function ae(e,t,a,n,i,o,s,l,c){var u=arguments.length>9&&void 0!==arguments[9]?arguments[9]:1,d=[],h=o.width-o.area[1]-o.area[3];return e.forEach((function(e,i){if(null===e)d.push(null);else{var s={};if(s.color=e.color,s.y=n[i],l>0){for(var f=0,p=0;p<=l;p++)f+=c[p].data[i];var g=f-e,x=h*(f-t)/(a-t),v=h*(g-t)/(a-t)}else{f=e;"object"===(0,r.default)(e)&&null!==e&&(f=e.value);x=h*(f-t)/(a-t),v=0}var m=v;x*=u,m*=u,s.height=x-m,s.x=o.area[3]+x,s.x0=o.area[3]+m,d.push(s)}})),d}function ne(e,t,a,n,i){var o;o="stack"==n?w(e,t.categories.length):b(e);var s=[];o=o.filter((function(e){return"object"===(0,r.default)(e)&&null!==e?e.constructor.toString().indexOf("Array")>-1?null!==e:null!==e.value:null!==e})),o.map((function(e){"object"===(0,r.default)(e)?e.constructor.toString().indexOf("Array")>-1?"candle"==t.type?e.map((function(e){s.push(e)})):s.push(e[1]):s.push(e.value):s.push(e)}));var l=i.min||0,c=i.max||0;s.length>0&&(l=Math.min.apply(this,s),c=Math.max.apply(this,s)),l===c&&(0==c?c=10:l=0);for(var u=m(l,c),d=void 0===i.min||null===i.min?u.minRange:i.min,h=void 0===i.max||null===i.max?u.maxRange:i.max,f=(h-d)/t.yAxis.splitNumber,p=[],g=0;g<=t.yAxis.splitNumber;g++)p.push(d+f*g);return p.reverse()}function ie(e,t,a,n){var i=s({},{type:""},t.extra.column),r=t.yAxis.data.length,o=new Array(r);if(r>0){for(var c=0;c<r;c++){o[c]=[];for(var u=0;u<e.length;u++)e[u].index==c&&o[c].push(e[u])}for(var d=new Array(r),h=new Array(r),f=new Array(r),p=function(e){var r=t.yAxis.data[e];1==t.yAxis.disabled&&(r.disabled=!0),"categories"===r.type?(r.formatter||(r.formatter=function(e,t,a){return e+(r.unit||"")}),r.categories=r.categories||t.categories,d[e]=r.categories):(r.formatter||(r.formatter=function(e,t,a){return l.toFixed(e,r.tofix||0)+(r.unit||"")}),d[e]=ne(o[e],t,a,i.type,r,e));var s=r.fontSize*t.pix||a.fontSize;f[e]={position:r.position?r.position:"left",width:0},h[e]=d[e].map((function(a,i){return a=r.formatter(a,i,t),f[e].width=Math.max(f[e].width,y(a,s,n)+5),a}));var c=r.calibration?4*t.pix:0;f[e].width+=c+3*t.pix,!0===r.disabled&&(f[e].width=0)},g=0;g<r;g++)p(g)}else{d=new Array(1),h=new Array(1),f=new Array(1);"bar"===t.type?(d[0]=t.categories,t.yAxis.formatter||(t.yAxis.formatter=function(e,t,a){return e+(a.yAxis.unit||"")})):(t.yAxis.formatter||(t.yAxis.formatter=function(e,t,a){return e.toFixed(a.yAxis.tofix)+(a.yAxis.unit||"")}),d[0]=ne(e,t,a,i.type,{})),f[0]={position:"left",width:0};var x=t.yAxis.fontSize*t.pix||a.fontSize;h[0]=d[0].map((function(e,a){return e=t.yAxis.formatter(e,a,t),f[0].width=Math.max(f[0].width,y(e,x,n)+5),e})),f[0].width+=3*t.pix,!0===t.yAxis.disabled?(f[0]={position:"left",width:0},t.yAxis.data[0]={disabled:!0}):(t.yAxis.data[0]={disabled:!1,position:"left",max:t.yAxis.max,min:t.yAxis.min,formatter:t.yAxis.formatter},"bar"===t.type&&(t.yAxis.data[0].categories=t.categories,t.yAxis.data[0].type="categories"))}return{rangesFormat:h,ranges:d,yAxisWidth:f}}function re(e,t){!0!==t.rotateLock?(e.translate(t.height,0),e.rotate(90*Math.PI/180)):!0!==t._rotate_&&(e.translate(t.height,0),e.rotate(90*Math.PI/180),t._rotate_=!0)}function oe(e,t,a,n,i){if(n.beginPath(),"hollow"==i.dataPointShapeType?(n.setStrokeStyle(t),n.setFillStyle(i.background),n.setLineWidth(2*i.pix)):(n.setStrokeStyle("#ffffff"),n.setFillStyle(t),n.setLineWidth(1*i.pix)),"diamond"===a)e.forEach((function(e,t){null!==e&&(n.moveTo(e.x,e.y-4.5),n.lineTo(e.x-4.5,e.y),n.lineTo(e.x,e.y****),n.lineTo(e.x****,e.y),n.lineTo(e.x,e.y-4.5))}));else if("circle"===a)e.forEach((function(e,t){null!==e&&(n.moveTo(e.x*****i.pix,e.y),n.arc(e.x,e.y,3*i.pix,0,2*Math.PI,!1))}));else if("square"===a)e.forEach((function(e,t){null!==e&&(n.moveTo(e.x-3.5,e.y-3.5),n.rect(e.x-3.5,e.y-3.5,7,7))}));else if("triangle"===a)e.forEach((function(e,t){null!==e&&(n.moveTo(e.x,e.y-4.5),n.lineTo(e.x-4.5,e.y****),n.lineTo(e.x****,e.y****),n.lineTo(e.x,e.y-4.5))}));else if("none"===a)return;n.closePath(),n.fill(),n.stroke()}function se(e,t,a,n,i,r,o){if(i.tooltip&&!(i.tooltip.group.length>0&&0==i.tooltip.group.includes(o))){var s="number"===typeof i.tooltip.index?i.tooltip.index:i.tooltip.index[i.tooltip.group.indexOf(o)];if(n.beginPath(),"hollow"==r.activeType?(n.setStrokeStyle(t),n.setFillStyle(i.background),n.setLineWidth(2*i.pix)):(n.setStrokeStyle("#ffffff"),n.setFillStyle(t),n.setLineWidth(1*i.pix)),"diamond"===a)e.forEach((function(e,t){null!==e&&s==t&&(n.moveTo(e.x,e.y-4.5),n.lineTo(e.x-4.5,e.y),n.lineTo(e.x,e.y****),n.lineTo(e.x****,e.y),n.lineTo(e.x,e.y-4.5))}));else if("circle"===a)e.forEach((function(e,t){null!==e&&s==t&&(n.moveTo(e.x*****i.pix,e.y),n.arc(e.x,e.y,3*i.pix,0,2*Math.PI,!1))}));else if("square"===a)e.forEach((function(e,t){null!==e&&s==t&&(n.moveTo(e.x-3.5,e.y-3.5),n.rect(e.x-3.5,e.y-3.5,7,7))}));else if("triangle"===a)e.forEach((function(e,t){null!==e&&s==t&&(n.moveTo(e.x,e.y-4.5),n.lineTo(e.x-4.5,e.y****),n.lineTo(e.x****,e.y****),n.lineTo(e.x,e.y-4.5))}));else if("none"===a)return;n.closePath(),n.fill(),n.stroke()}}function le(e,t,a,n){var i=e.title.fontSize||t.titleFontSize,r=e.subtitle.fontSize||t.subtitleFontSize,o=e.title.name||"",s=e.subtitle.name||"",l=e.title.color||e.fontColor,c=e.subtitle.color||e.fontColor,u=o?i:0,d=s?r:0;if(s){var h=y(s,r*e.pix,a),f=n.x-h/2+(e.subtitle.offsetX||0)*e.pix,p=n.y+r*e.pix/2+(e.subtitle.offsetY||0)*e.pix;o&&(p+=(u*e.pix+5)/2),a.beginPath(),a.setFontSize(r*e.pix),a.setFillStyle(c),a.fillText(s,f,p),a.closePath(),a.stroke()}if(o){var g=y(o,i*e.pix,a),x=n.x-g/2+(e.title.offsetX||0),v=n.y+i*e.pix/2+(e.title.offsetY||0)*e.pix;s&&(v-=(d*e.pix+5)/2),a.beginPath(),a.setFontSize(i*e.pix),a.setFillStyle(l),a.fillText(o,x,v),a.closePath(),a.stroke()}}function ce(e,t,a,n,i){var o=t.data,s=t.textOffset?t.textOffset:0;e.forEach((function(e,l){if(null!==e){n.beginPath();var c=t.textSize?t.textSize*i.pix:a.fontSize;n.setFontSize(c),n.setFillStyle(t.textColor||i.fontColor);var u=o[l];"object"===(0,r.default)(o[l])&&null!==o[l]&&(u=o[l].constructor.toString().indexOf("Array")>-1?o[l][1]:o[l].value);var d=t.formatter?t.formatter(u,l,t,i):u;n.setTextAlign("center"),n.fillText(String(d),e.x,e.y-4+s*i.pix),n.closePath(),n.stroke(),n.setTextAlign("left")}}))}function ue(e,t,a,n,i){var o=t.data,s=t.textOffset?t.textOffset:0,l=i.extra.column.labelPosition;e.forEach((function(e,c){if(null!==e){n.beginPath();var u=t.textSize?t.textSize*i.pix:a.fontSize;n.setFontSize(u),n.setFillStyle(t.textColor||i.fontColor);var d=o[c];"object"===(0,r.default)(o[c])&&null!==o[c]&&(d=o[c].constructor.toString().indexOf("Array")>-1?o[c][1]:o[c].value);var h=t.formatter?t.formatter(d,c,t,i):d;n.setTextAlign("center");var f=e.y-4*i.pix+s*i.pix;e.y>t.zeroPoints&&(f=e.y+s*i.pix+u),"insideTop"==l&&(f=e.y+u+s*i.pix,e.y>t.zeroPoints&&(f=e.y-s*i.pix-4*i.pix)),"center"==l&&(f=e.y+s*i.pix+(i.height-i.area[2]-e.y+u)/2,t.zeroPoints<i.height-i.area[2]&&(f=e.y+s*i.pix+(t.zeroPoints-e.y+u)/2),e.y>t.zeroPoints&&(f=e.y-s*i.pix-(e.y-t.zeroPoints-u)/2),"stack"==i.extra.column.type&&(f=e.y+s*i.pix+(e.y0-e.y+u)/2)),"bottom"==l&&(f=i.height-i.area[2]+s*i.pix-4*i.pix,t.zeroPoints<i.height-i.area[2]&&(f=t.zeroPoints+s*i.pix-4*i.pix),e.y>t.zeroPoints&&(f=t.zeroPoints-s*i.pix+u+2*i.pix),"stack"==i.extra.column.type&&(f=e.y0+s*i.pix-4*i.pix)),n.fillText(String(h),e.x,f),n.closePath(),n.stroke(),n.setTextAlign("left")}}))}function de(e,t,a,n,i,r){t.data;var o=t.textOffset?t.textOffset:0;i.extra.mount.labelPosition;e.forEach((function(e,s){if(null!==e){n.beginPath();var l=t[s].textSize?t[s].textSize*i.pix:a.fontSize;n.setFontSize(l),n.setFillStyle(t[s].textColor||i.fontColor);var c=e.value,u=t[s].formatter?t[s].formatter(c,s,t,i):c;n.setTextAlign("center");var d=e.y-4*i.pix+o*i.pix;e.y>r&&(d=e.y+o*i.pix+l),n.fillText(String(u),e.x,d),n.closePath(),n.stroke(),n.setTextAlign("left")}}))}function he(e,t,a,n,i){var o=t.data;t.textOffset&&t.textOffset;e.forEach((function(e,s){if(null!==e){n.beginPath();var l=t.textSize?t.textSize*i.pix:a.fontSize;n.setFontSize(l),n.setFillStyle(t.textColor||i.fontColor);var c=o[s];"object"===(0,r.default)(o[s])&&null!==o[s]&&(c=o[s].value);var u=t.formatter?t.formatter(c,s,t,i):c;n.setTextAlign("left"),n.fillText(String(u),e.x+4*i.pix,e.y+l/2-3),n.closePath(),n.stroke()}}))}function fe(e,t,a,n,i,r){var o;t-=e.width/2+e.labelOffset*n.pix,t=t<10?10:t,o=e.endAngle<e.startAngle?2+e.endAngle-e.startAngle:e.startAngle-e.endAngle;for(var s=o/e.splitLine.splitNumber,l=e.endNumber-e.startNumber,c=l/e.splitLine.splitNumber,u=e.startAngle,d=e.startNumber,h=0;h<e.splitLine.splitNumber+1;h++){var f={x:t*Math.cos(u*Math.PI),y:t*Math.sin(u*Math.PI)},p=e.formatter?e.formatter(d,h,n):d;f.x+=a.x-y(p,i.fontSize,r)/2,f.y+=a.y;var g=f.x,x=f.y;r.beginPath(),r.setFontSize(i.fontSize),r.setFillStyle(e.labelColor||n.fontColor),r.fillText(p,g,x+i.fontSize/2),r.closePath(),r.stroke(),u+=s,u>=2&&(u%=2),d+=c}}function pe(e,t,a,n,i,r){var o=n.extra.radar||{};e.forEach((function(e,s){if(!0===o.labelPointShow&&""!==n.categories[s]){var c={x:t*Math.cos(e),y:t*Math.sin(e)},u=p(c.x,c.y,a);r.setFillStyle(o.labelPointColor),r.beginPath(),r.arc(u.x,u.y,o.labelPointRadius*n.pix,0,2*Math.PI,!1),r.closePath(),r.fill()}if(!0===o.labelShow){var d={x:(t+i.radarLabelTextMargin*n.pix)*Math.cos(e),y:(t+i.radarLabelTextMargin*n.pix)*Math.sin(e)},h=p(d.x,d.y,a),f=h.x,g=h.y;l.approximatelyEqual(d.x,0)?f-=y(n.categories[s]||"",i.fontSize,r)/2:d.x<0&&(f-=y(n.categories[s]||"",i.fontSize,r)),r.beginPath(),r.setFontSize(i.fontSize),r.setFillStyle(o.labelColor||n.fontColor),r.fillText(n.categories[s]||"",f,g+i.fontSize/2),r.closePath(),r.stroke()}}))}function ge(e,t,a,n,i,r){for(var o=a.pieChartLinePadding,s=[],c=null,u=e.map((function(a,n){var i=a.formatter?a.formatter(a,n,e,t):l.toFixed(100*a._proportion_.toFixed(4))+"%";i=a.labelText?a.labelText:i;var r=2*Math.PI-(a._start_+2*Math.PI*a._proportion_/2);a._rose_proportion_&&(r=2*Math.PI-(a._start_+2*Math.PI*a._rose_proportion_/2));var o=a.color,s=a._radius_;return{arc:r,text:i,color:o,radius:s,textColor:a.textColor,textSize:a.textSize,labelShow:a.labelShow}})),d=0;d<u.length;d++){var h=u[d],f=Math.cos(h.arc)*(h.radius+o),x=Math.sin(h.arc)*(h.radius+o),v=Math.cos(h.arc)*h.radius,m=Math.sin(h.arc)*h.radius,b=f>=0?f+a.pieChartTextPadding:f-a.pieChartTextPadding,w=x,S=y(h.text,h.textSize*t.pix||a.fontSize,n),k=w;c&&l.isSameXCoordinateArea(c.start,{x:b})&&(k=b>0?Math.min(w,c.start.y):f<0||w>0?Math.max(w,c.start.y):Math.min(w,c.start.y)),b<0&&(b-=S);var A={lineStart:{x:v,y:m},lineEnd:{x:f,y:x},start:{x:b,y:k},width:S,height:a.fontSize,text:h.text,color:h.color,textColor:h.textColor,textSize:h.textSize};c=g(A,c),s.push(c)}for(var T=0;T<s.length;T++)if(!1!==u[T].labelShow){var _=s[T],P=p(_.lineStart.x,_.lineStart.y,r),C=p(_.lineEnd.x,_.lineEnd.y,r),D=p(_.start.x,_.start.y,r);n.setLineWidth(1*t.pix),n.setFontSize(_.textSize*t.pix||a.fontSize),n.beginPath(),n.setStrokeStyle(_.color),n.setFillStyle(_.color),n.moveTo(P.x,P.y);var M=_.start.x<0?D.x+_.width:D.x,I=_.start.x<0?D.x-5:D.x+5;n.quadraticCurveTo(C.x,C.y,M,D.y),n.moveTo(P.x,P.y),n.stroke(),n.closePath(),n.beginPath(),n.moveTo(D.x+_.width,D.y),n.arc(M,D.y,2*t.pix,0,2*Math.PI),n.closePath(),n.fill(),n.beginPath(),n.setFontSize(_.textSize*t.pix||a.fontSize),n.setFillStyle(_.textColor||t.fontColor),n.fillText(_.text,I,D.y+3),n.closePath(),n.stroke(),n.closePath()}}function xe(e,t,a){for(var n=s({},{type:"solid",dashLength:4,data:[]},e.extra.markLine),i=e.area[3],r=e.width-e.area[1],o=function(e,t){for(var a,n,i=t.height-t.area[0]-t.area[2],r=0;r<e.length;r++){e[r].yAxisIndex=e[r].yAxisIndex?e[r].yAxisIndex:0;var o=[].concat(t.chartData.yAxisData.ranges[e[r].yAxisIndex]);a=o.pop(),n=o.shift();var s=i*(e[r].value-a)/(n-a);e[r].y=t.height-Math.round(s)-t.area[2]}return e}(n.data,e),l=0;l<o.length;l++){var u=s({},{lineColor:"#DE4A42",showLabel:!1,labelFontSize:13,labelPadding:6,labelFontColor:"#666666",labelBgColor:"#DFE8FF",labelBgOpacity:.8,labelAlign:"left",labelOffsetX:0,labelOffsetY:0},o[l]);if("dash"==n.type&&a.setLineDash([n.dashLength,n.dashLength]),a.setStrokeStyle(u.lineColor),a.setLineWidth(1*e.pix),a.beginPath(),a.moveTo(i,u.y),a.lineTo(r,u.y),a.stroke(),a.setLineDash([]),u.showLabel){var d=u.labelFontSize*e.pix,h=u.labelText?u.labelText:u.value;a.setFontSize(d);var f=y(h,d,a),p=f+u.labelPadding*e.pix*2,g="left"==u.labelAlign?e.area[3]-p:e.width-e.area[1];g+=u.labelOffsetX;var x=u.y-.5*d-u.labelPadding*e.pix;x+=u.labelOffsetY;var v=g+u.labelPadding*e.pix;u.y;a.setFillStyle(c(u.labelBgColor,u.labelBgOpacity)),a.setStrokeStyle(u.labelBgColor),a.setLineWidth(1*e.pix),a.beginPath(),a.rect(g,x,p,d+2*u.labelPadding*e.pix),a.closePath(),a.stroke(),a.fill(),a.setFontSize(d),a.setTextAlign("left"),a.setFillStyle(u.labelFontColor),a.fillText(String(h),v,x+d+u.labelPadding*e.pix/2),a.stroke(),a.setTextAlign("left")}}}function ve(e,t,a,n,i){var r=s({},{gridType:"solid",dashLength:4},e.extra.tooltip),o=e.area[3],l=e.width-e.area[1];if("dash"==r.gridType&&a.setLineDash([r.dashLength,r.dashLength]),a.setStrokeStyle(r.gridColor||"#cccccc"),a.setLineWidth(1*e.pix),a.beginPath(),a.moveTo(o,e.tooltip.offset.y),a.lineTo(l,e.tooltip.offset.y),a.stroke(),a.setLineDash([]),r.yAxisLabel)for(var u=r.boxPadding*e.pix,d=function(e,t,a,n,i){for(var r=[].concat(a.chartData.yAxisData.ranges),o=a.height-a.area[0]-a.area[2],s=a.area[0],l=[],c=0;c<r.length;c++){var u=Math.max.apply(this,r[c]),d=Math.min.apply(this,r[c]),h=u-(u-d)*(e-s)/o;h=a.yAxis.data&&a.yAxis.data[c].formatter?a.yAxis.data[c].formatter(h,c,a):h.toFixed(0),l.push(String(h))}return l}(e.tooltip.offset.y,e.series,e),h=e.chartData.yAxisData.yAxisWidth,f=e.area[3],p=e.width-e.area[1],g=0;g<d.length;g++){a.setFontSize(r.fontSize*e.pix);var x,v=y(d[g],r.fontSize*e.pix,a),m=void 0,b=void 0;"left"==h[g].position?(m=f-(v+2*u)-2*e.pix,b=Math.max(m,m+v+2*u)):(m=p+2*e.pix,b=Math.max(m+h[g].width,m+v+2*u)),x=b-m;var w=m+(x-v)/2,S=e.tooltip.offset.y;a.beginPath(),a.setFillStyle(c(r.labelBgColor||t.toolTipBackground,r.labelBgOpacity||t.toolTipOpacity)),a.setStrokeStyle(r.labelBgColor||t.toolTipBackground),a.setLineWidth(1*e.pix),a.rect(m,S-.5*t.fontSize-u,x,t.fontSize+2*u),a.closePath(),a.stroke(),a.fill(),a.beginPath(),a.setFontSize(t.fontSize),a.setFillStyle(r.labelFontColor||e.fontColor),a.fillText(d[g],w,S+.5*t.fontSize),a.closePath(),a.stroke(),"left"==h[g].position?f-=h[g].width+e.yAxis.padding*e.pix:p+=h[g].width+e.yAxis.padding*e.pix}}function me(e,t,a,n,i){var r=s({},{activeBgColor:"#000000",activeBgOpacity:.08,activeWidth:i},t.extra.column);r.activeWidth=r.activeWidth>i?i:r.activeWidth;var o=t.area[0],l=t.height-t.area[2];n.beginPath(),n.setFillStyle(c(r.activeBgColor,r.activeBgOpacity)),n.rect(e-r.activeWidth/2,o,r.activeWidth,l-o),n.closePath(),n.fill(),n.setFillStyle("#FFFFFF")}function ye(e,t,a,n,i){var r=s({},{activeBgColor:"#000000",activeBgOpacity:.08},t.extra.bar),o=t.area[3],l=t.width-t.area[1];n.beginPath(),n.setFillStyle(c(r.activeBgColor,r.activeBgOpacity)),n.rect(o,e-i/2,l-o,i),n.closePath(),n.fill(),n.setFillStyle("#FFFFFF")}function be(e,t,a,n,i,r,o){var l=s({},{showBox:!0,showArrow:!0,showCategory:!1,bgColor:"#000000",bgOpacity:.7,borderColor:"#000000",borderWidth:0,borderRadius:0,borderOpacity:.7,boxPadding:3,fontColor:"#FFFFFF",fontSize:13,lineHeight:20,legendShow:!0,legendShape:"auto",splitLine:!0},a.extra.tooltip);1==l.showCategory&&a.categories&&e.unshift({text:a.categories[a.tooltip.index],color:null});var u=l.fontSize*a.pix,d=l.lineHeight*a.pix,h=l.boxPadding*a.pix,f=u,p=5*a.pix;0==l.legendShow&&(f=0,p=0);var g=l.showArrow?8*a.pix:0,x=!1;"line"!=a.type&&"mount"!=a.type&&"area"!=a.type&&"candle"!=a.type&&"mix"!=a.type||1==l.splitLine&&function(e,t,a,n){var i=t.extra.tooltip||{};i.gridType=void 0==i.gridType?"solid":i.gridType,i.dashLength=void 0==i.dashLength?4:i.dashLength;var r=t.area[0],o=t.height-t.area[2];if("dash"==i.gridType&&n.setLineDash([i.dashLength,i.dashLength]),n.setStrokeStyle(i.gridColor||"#cccccc"),n.setLineWidth(1*t.pix),n.beginPath(),n.moveTo(e,r),n.lineTo(e,o),n.stroke(),n.setLineDash([]),i.xAxisLabel){var s=t.categories[t.tooltip.index];n.setFontSize(a.fontSize);var l=y(s,a.fontSize,n),u=e-.5*l,d=o+2*t.pix;n.beginPath(),n.setFillStyle(c(i.labelBgColor||a.toolTipBackground,i.labelBgOpacity||a.toolTipOpacity)),n.setStrokeStyle(i.labelBgColor||a.toolTipBackground),n.setLineWidth(1*t.pix),n.rect(u-i.boxPadding*t.pix,d,l+2*i.boxPadding*t.pix,a.fontSize+2*i.boxPadding*t.pix),n.closePath(),n.stroke(),n.fill(),n.beginPath(),n.setFontSize(a.fontSize),n.setFillStyle(i.labelFontColor||t.fontColor),n.fillText(String(s),u,d+i.boxPadding*t.pix+a.fontSize),n.closePath(),n.stroke()}}(a.tooltip.offset.x,a,n,i),t=s({x:0,y:0},t),t.y-=8*a.pix;var v=e.map((function(e){return y(e.text,u,i)})),m=f+p+4*h+Math.max.apply(null,v),b=2*h+e.length*d;if(0!=l.showBox){t.x-Math.abs(a._scrollDistance_||0)+g+m>a.width&&(x=!0),b+t.y>a.height&&(t.y=a.height-b),i.beginPath(),i.setFillStyle(c(l.bgColor,l.bgOpacity)),i.setLineWidth(l.borderWidth*a.pix),i.setStrokeStyle(c(l.borderColor,l.borderOpacity));var w=l.borderRadius;x?(m+g>a.width&&(t.x=a.width+Math.abs(a._scrollDistance_||0)+g+(m-a.width)),m>t.x&&(t.x=a.width+Math.abs(a._scrollDistance_||0)+g+(m-a.width)),l.showArrow&&(i.moveTo(t.x,t.y+10*a.pix),i.lineTo(t.x-g,t.y+10*a.pix+5*a.pix)),i.arc(t.x-g-w,t.y+b-w,w,0,Math.PI/2,!1),i.arc(t.x-g-Math.round(m)+w,t.y+b-w,w,Math.PI/2,Math.PI,!1),i.arc(t.x-g-Math.round(m)+w,t.y+w,w,-Math.PI,-Math.PI/2,!1),i.arc(t.x-g-w,t.y+w,w,-Math.PI/2,0,!1),l.showArrow&&(i.lineTo(t.x-g,t.y+10*a.pix-5*a.pix),i.lineTo(t.x,t.y+10*a.pix))):(l.showArrow&&(i.moveTo(t.x,t.y+10*a.pix),i.lineTo(t.x+g,t.y+10*a.pix-5*a.pix)),i.arc(t.x+g+w,t.y+w,w,-Math.PI,-Math.PI/2,!1),i.arc(t.x+g+Math.round(m)-w,t.y+w,w,-Math.PI/2,0,!1),i.arc(t.x+g+Math.round(m)-w,t.y+b-w,w,0,Math.PI/2,!1),i.arc(t.x+g+w,t.y+b-w,w,Math.PI/2,Math.PI,!1),l.showArrow&&(i.lineTo(t.x+g,t.y+10*a.pix+5*a.pix),i.lineTo(t.x,t.y+10*a.pix))),i.closePath(),i.fill(),l.borderWidth>0&&i.stroke(),l.legendShow&&e.forEach((function(e,n){if(null!==e.color){i.beginPath(),i.setFillStyle(e.color);var r=t.x+g+2*h,o=t.y+(d-u)/2+d*n+h+1;switch(x&&(r=t.x-m-g+2*h),e.legendShape){case"line":i.moveTo(r,o+.5*f-2*a.pix),i.fillRect(r,o+.5*f-2*a.pix,f,4*a.pix);break;case"triangle":i.moveTo(r+7.5*a.pix,o+.5*f-5*a.pix),i.lineTo(r*****a.pix,o+.5*f+5*a.pix),i.lineTo(r+12.5*a.pix,o+.5*f+5*a.pix),i.lineTo(r+7.5*a.pix,o+.5*f-5*a.pix);break;case"diamond":i.moveTo(r+7.5*a.pix,o+.5*f-5*a.pix),i.lineTo(r*****a.pix,o+.5*f),i.lineTo(r+7.5*a.pix,o+.5*f+5*a.pix),i.lineTo(r+12.5*a.pix,o+.5*f),i.lineTo(r+7.5*a.pix,o+.5*f-5*a.pix);break;case"circle":i.moveTo(r+7.5*a.pix,o+.5*f),i.arc(r+7.5*a.pix,o+.5*f,5*a.pix,0,2*Math.PI);break;case"rect":i.moveTo(r,o+.5*f-5*a.pix),i.fillRect(r,o+.5*f-5*a.pix,15*a.pix,10*a.pix);break;case"square":i.moveTo(r+2*a.pix,o+.5*f-5*a.pix),i.fillRect(r+2*a.pix,o+.5*f-5*a.pix,10*a.pix,10*a.pix);break;default:i.moveTo(r,o+.5*f-5*a.pix),i.fillRect(r,o+.5*f-5*a.pix,15*a.pix,10*a.pix)}i.closePath(),i.fill()}})),e.forEach((function(e,a){var n=t.x+g+2*h+f+p;x&&(n=t.x-m-g+2*h+f+p);var r=t.y+d*a+(d-u)/2-1+h+u;i.beginPath(),i.setFontSize(u),i.setTextBaseline("normal"),i.setFillStyle(l.fontColor),i.fillText(e.text,n,r),i.closePath(),i.stroke()}))}}function we(e,t,a,n,i,r){var o=e.extra.tooltip||{};o.horizentalLine&&e.tooltip&&1===n&&("line"==e.type||"area"==e.type||"column"==e.type||"mount"==e.type||"candle"==e.type||"mix"==e.type)&&ve(e,t,a),a.save(),e._scrollDistance_&&0!==e._scrollDistance_&&!0===e.enableScroll&&a.translate(e._scrollDistance_,0),e.tooltip&&e.tooltip.textList&&e.tooltip.textList.length&&1===n&&be(e.tooltip.textList,e.tooltip.offset,e,t,a),a.restore()}function Se(e,t,a,n){var i=t.chartData.xAxisData,r=i.xAxisPoints,o=i.startX,s=i.endX,l=i.eachSpacing,c="center";"bar"!=t.type&&"line"!=t.type&&"area"!=t.type&&"scatter"!=t.type&&"bubble"!=t.type||(c=t.xAxis.boundaryGap);var u=t.height-t.area[2],d=t.area[0];if(t.enableScroll&&t.xAxis.scrollShow){var h=t.height-t.area[2]+a.xAxisHeight,f=s-o,p=l*(r.length-1);"mount"==t.type&&t.extra&&t.extra.mount&&t.extra.mount.widthRatio&&t.extra.mount.widthRatio>1&&(t.extra.mount.widthRatio>2&&(t.extra.mount.widthRatio=2),p+=(t.extra.mount.widthRatio-1)*l);var g=f*f/p,x=0;t._scrollDistance_&&(x=-t._scrollDistance_*f/p),n.beginPath(),n.setLineCap("round"),n.setLineWidth(6*t.pix),n.setStrokeStyle(t.xAxis.scrollBackgroundColor||"#EFEBEF"),n.moveTo(o,h),n.lineTo(s,h),n.stroke(),n.closePath(),n.beginPath(),n.setLineCap("round"),n.setLineWidth(6*t.pix),n.setStrokeStyle(t.xAxis.scrollColor||"#A6A6A6"),n.moveTo(o+x,h),n.lineTo(o+x+g,h),n.stroke(),n.closePath(),n.setLineCap("butt")}if(n.save(),t._scrollDistance_&&0!==t._scrollDistance_&&n.translate(t._scrollDistance_,0),!0===t.xAxis.calibration&&(n.setStrokeStyle(t.xAxis.gridColor||"#cccccc"),n.setLineCap("butt"),n.setLineWidth(1*t.pix),r.forEach((function(e,a){a>0&&(n.beginPath(),n.moveTo(e-l/2,u),n.lineTo(e-l/2,u+3*t.pix),n.closePath(),n.stroke())}))),!0!==t.xAxis.disableGrid&&(n.setStrokeStyle(t.xAxis.gridColor||"#cccccc"),n.setLineCap("butt"),n.setLineWidth(1*t.pix),"dash"==t.xAxis.gridType&&n.setLineDash([t.xAxis.dashLength*t.pix,t.xAxis.dashLength*t.pix]),t.xAxis.gridEval=t.xAxis.gridEval||1,r.forEach((function(e,a){a%t.xAxis.gridEval==0&&(n.beginPath(),n.moveTo(e,u),n.lineTo(e,d),n.stroke())})),n.setLineDash([])),!0!==t.xAxis.disabled){var v=e.length;t.xAxis.labelCount&&(v=t.xAxis.itemCount?Math.ceil(e.length/t.xAxis.itemCount*t.xAxis.labelCount):t.xAxis.labelCount,v-=1);for(var m=Math.ceil(e.length/v),b=[],w=e.length,S=0;S<w;S++)S%m!==0?b.push(""):b.push(e[S]);b[w-1]=e[w-1];var k=t.xAxis.fontSize*t.pix||a.fontSize;0===a._xAxisTextAngle_?b.forEach((function(e,a){var i=t.xAxis.formatter?t.xAxis.formatter(e,a,t):e,o=-y(String(i),k,n)/2;"center"==c&&(o+=l/2);t.xAxis.scrollShow&&t.pix;var s=t._scrollDistance_||0,d="center"==c?r[a]+l/2:r[a];d-Math.abs(s)>=t.area[3]-1&&d-Math.abs(s)<=t.width-t.area[1]+1&&(n.beginPath(),n.setFontSize(k),n.setFillStyle(t.xAxis.fontColor||t.fontColor),n.fillText(String(i),r[a]+o,u+t.xAxis.marginTop*t.pix+(t.xAxis.lineHeight-t.xAxis.fontSize)*t.pix/2+t.xAxis.fontSize*t.pix),n.closePath(),n.stroke())})):b.forEach((function(e,i){var o=t.xAxis.formatter?t.xAxis.formatter(e):e,s=t._scrollDistance_||0,d="center"==c?r[i]+l/2:r[i];if(d-Math.abs(s)>=t.area[3]-1&&d-Math.abs(s)<=t.width-t.area[1]+1){n.save(),n.beginPath(),n.setFontSize(k),n.setFillStyle(t.xAxis.fontColor||t.fontColor);var h=y(String(o),k,n),f=r[i];"center"==c&&(f=r[i]+l/2);t.xAxis.scrollShow&&6*t.pix;var p=u+t.xAxis.marginTop*t.pix+k-k*Math.abs(Math.sin(a._xAxisTextAngle_));t.xAxis.rotateAngle<0?(f-=k/2,h=0):(f+=k/2,h=-h),n.translate(f,p),n.rotate(-1*a._xAxisTextAngle_),n.fillText(String(o),h,0),n.closePath(),n.stroke(),n.restore()}}))}n.restore(),t.xAxis.title&&(n.beginPath(),n.setFontSize(t.xAxis.titleFontSize*t.pix),n.setFillStyle(t.xAxis.titleFontColor),n.fillText(String(t.xAxis.title),t.width-t.area[1]+t.xAxis.titleOffsetX*t.pix,t.height-t.area[2]+t.xAxis.marginTop*t.pix+(t.xAxis.lineHeight-t.xAxis.titleFontSize)*t.pix/2+(t.xAxis.titleFontSize+t.xAxis.titleOffsetY)*t.pix),n.closePath(),n.stroke()),t.xAxis.axisLine&&(n.beginPath(),n.setStrokeStyle(t.xAxis.axisLineColor),n.setLineWidth(1*t.pix),n.moveTo(o,t.height-t.area[2]),n.lineTo(s,t.height-t.area[2]),n.stroke())}function ke(e,t,a,n){if(!0!==t.yAxis.disableGrid){var i=t.height-t.area[0]-t.area[2],r=i/t.yAxis.splitNumber,o=t.area[3],s=t.chartData.xAxisData.xAxisPoints,l=t.chartData.xAxisData.eachSpacing,c=l*(s.length-1);"mount"==t.type&&t.extra&&t.extra.mount&&t.extra.mount.widthRatio&&t.extra.mount.widthRatio>1&&(t.extra.mount.widthRatio>2&&(t.extra.mount.widthRatio=2),c+=(t.extra.mount.widthRatio-1)*l);var u=o+c,d=[],h=1;!1===t.xAxis.axisLine&&(h=0);for(var f=h;f<t.yAxis.splitNumber+1;f++)d.push(t.height-t.area[2]-r*f);n.save(),t._scrollDistance_&&0!==t._scrollDistance_&&n.translate(t._scrollDistance_,0),"dash"==t.yAxis.gridType&&n.setLineDash([t.yAxis.dashLength*t.pix,t.yAxis.dashLength*t.pix]),n.setStrokeStyle(t.yAxis.gridColor),n.setLineWidth(1*t.pix),d.forEach((function(e,t){n.beginPath(),n.moveTo(o,e),n.lineTo(u,e),n.stroke()})),n.setLineDash([]),n.restore()}}function Ae(e,t,a,n){if(!0!==t.yAxis.disabled){var i=t.height-t.area[0]-t.area[2],r=i/t.yAxis.splitNumber,o=t.area[3],s=t.width-t.area[1],l=t.height-t.area[2];n.beginPath(),n.setFillStyle(t.background),1==t.enableScroll&&t.xAxis.scrollPosition&&"left"!==t.xAxis.scrollPosition&&n.fillRect(0,0,o,l+2*t.pix),1==t.enableScroll&&t.xAxis.scrollPosition&&"right"!==t.xAxis.scrollPosition&&n.fillRect(s,0,t.width,l+2*t.pix),n.closePath(),n.stroke();var c=t.area[3],u=t.width-t.area[1],d=t.area[3]+(t.width-t.area[1]-t.area[3])/2;if(t.yAxis.data)for(var h=function(e){var o=t.yAxis.data[e];if(p=[],"categories"===o.type)for(var s=0;s<=o.categories.length;s++)p.push(t.area[0]+i/o.categories.length/2+i/o.categories.length*s);else for(var l=0;l<=t.yAxis.splitNumber;l++)p.push(t.area[0]+r*l);if(!0!==o.disabled){var h=t.chartData.yAxisData.rangesFormat[e],f=o.fontSize?o.fontSize*t.pix:a.fontSize,g=t.chartData.yAxisData.yAxisWidth[e],x=o.textAlign||"right";if(h.forEach((function(e,a){var i=p[a];n.beginPath(),n.setFontSize(f),n.setLineWidth(1*t.pix),n.setStrokeStyle(o.axisLineColor||"#cccccc"),n.setFillStyle(o.fontColor||t.fontColor);var r=0,s=4*t.pix;if("left"==g.position){switch(1==o.calibration&&(n.moveTo(c,i),n.lineTo(c-3*t.pix,i),s+=3*t.pix),x){case"left":n.setTextAlign("left"),r=c-g.width;break;case"right":n.setTextAlign("right"),r=c-s;break;default:n.setTextAlign("center"),r=c-g.width/2}n.fillText(String(e),r,i+f/2-3*t.pix)}else if("right"==g.position){switch(1==o.calibration&&(n.moveTo(u,i),n.lineTo(u+3*t.pix,i),s+=3*t.pix),x){case"left":n.setTextAlign("left"),r=u+s;break;case"right":n.setTextAlign("right"),r=u+g.width;break;default:n.setTextAlign("center"),r=u+g.width/2}n.fillText(String(e),r,i+f/2-3*t.pix)}else if("center"==g.position){switch(1==o.calibration&&(n.moveTo(d,i),n.lineTo(d-3*t.pix,i),s+=3*t.pix),x){case"left":n.setTextAlign("left"),r=d-g.width;break;case"right":n.setTextAlign("right"),r=d-s;break;default:n.setTextAlign("center"),r=d-g.width/2}n.fillText(String(e),r,i+f/2-3*t.pix)}n.closePath(),n.stroke(),n.setTextAlign("left")})),!1!==o.axisLine&&(n.beginPath(),n.setStrokeStyle(o.axisLineColor||"#cccccc"),n.setLineWidth(1*t.pix),"left"==g.position?(n.moveTo(c,t.height-t.area[2]),n.lineTo(c,t.area[0])):"right"==g.position?(n.moveTo(u,t.height-t.area[2]),n.lineTo(u,t.area[0])):"center"==g.position&&(n.moveTo(d,t.height-t.area[2]),n.lineTo(d,t.area[0])),n.stroke()),t.yAxis.showTitle){var v=o.titleFontSize*t.pix||a.fontSize,m=o.title;n.beginPath(),n.setFontSize(v),n.setFillStyle(o.titleFontColor||t.fontColor),"left"==g.position?n.fillText(m,c-y(m,v,n)/2+(o.titleOffsetX||0),t.area[0]-(10-(o.titleOffsetY||0))*t.pix):"right"==g.position?n.fillText(m,u-y(m,v,n)/2+(o.titleOffsetX||0),t.area[0]-(10-(o.titleOffsetY||0))*t.pix):"center"==g.position&&n.fillText(m,d-y(m,v,n)/2+(o.titleOffsetX||0),t.area[0]-(10-(o.titleOffsetY||0))*t.pix),n.closePath(),n.stroke()}"left"==g.position?c-=g.width+t.yAxis.padding*t.pix:u+=g.width+t.yAxis.padding*t.pix}},f=0;f<t.yAxis.data.length;f++){var p;h(f)}}}function Te(e,t,a,n,i){if(!1!==t.legend.show){var r=i.legendData,o=r.points,s=r.area,l=t.legend.padding*t.pix,c=t.legend.fontSize*t.pix,u=15*t.pix,d=5*t.pix,h=t.legend.itemGap*t.pix,f=Math.max(t.legend.lineHeight*t.pix,c);n.beginPath(),n.setLineWidth(t.legend.borderWidth*t.pix),n.setStrokeStyle(t.legend.borderColor),n.setFillStyle(t.legend.backgroundColor),n.moveTo(s.start.x,s.start.y),n.rect(s.start.x,s.start.y,s.width,s.height),n.closePath(),n.fill(),n.stroke(),o.forEach((function(e,i){var o,p=0;p=r.widthArr[i],o=r.heightArr[i];var g=0,x=0;if("top"==t.legend.position||"bottom"==t.legend.position){switch(t.legend.float){case"left":g=s.start.x+l;break;case"right":g=s.start.x+s.width-p;break;default:g=s.start.x+(s.width-p)/2}x=s.start.y+l+i*f}else p=0==i?0:r.widthArr[i-1],g=s.start.x+l+p,x=s.start.y+l+(s.height-o)/2;n.setFontSize(a.fontSize);for(var v=0;v<e.length;v++){var m=e[v];switch(m.area=[0,0,0,0],m.area[0]=g,m.area[1]=x,m.area[3]=x+f,n.beginPath(),n.setLineWidth(1*t.pix),n.setStrokeStyle(m.show?m.color:t.legend.hiddenColor),n.setFillStyle(m.show?m.color:t.legend.hiddenColor),m.legendShape){case"line":n.moveTo(g,x+.5*f-2*t.pix),n.fillRect(g,x+.5*f-2*t.pix,15*t.pix,4*t.pix);break;case"triangle":n.moveTo(g+7.5*t.pix,x+.5*f-5*t.pix),n.lineTo(g*****t.pix,x+.5*f+5*t.pix),n.lineTo(g+12.5*t.pix,x+.5*f+5*t.pix),n.lineTo(g+7.5*t.pix,x+.5*f-5*t.pix);break;case"diamond":n.moveTo(g+7.5*t.pix,x+.5*f-5*t.pix),n.lineTo(g*****t.pix,x+.5*f),n.lineTo(g+7.5*t.pix,x+.5*f+5*t.pix),n.lineTo(g+12.5*t.pix,x+.5*f),n.lineTo(g+7.5*t.pix,x+.5*f-5*t.pix);break;case"circle":n.moveTo(g+7.5*t.pix,x+.5*f),n.arc(g+7.5*t.pix,x+.5*f,5*t.pix,0,2*Math.PI);break;case"rect":n.moveTo(g,x+.5*f-5*t.pix),n.fillRect(g,x+.5*f-5*t.pix,15*t.pix,10*t.pix);break;case"square":n.moveTo(g+5*t.pix,x+.5*f-5*t.pix),n.fillRect(g+5*t.pix,x+.5*f-5*t.pix,10*t.pix,10*t.pix);break;case"none":break;default:n.moveTo(g,x+.5*f-5*t.pix),n.fillRect(g,x+.5*f-5*t.pix,15*t.pix,10*t.pix)}n.closePath(),n.fill(),n.stroke(),g+=u+d;var b=.5*f+.5*c-2,w=m.legendText?m.legendText:m.name;n.beginPath(),n.setFontSize(c),n.setFillStyle(m.show?t.legend.fontColor:t.legend.hiddenColor),n.fillText(w,g,x+b),n.closePath(),n.stroke(),"top"==t.legend.position||"bottom"==t.legend.position?(g+=y(w,c,n)+h,m.area[2]=g):(m.area[2]=g+y(w,c,n)+h,g-=u+d,x+=f)}}))}}function _e(e,t,a,n){var i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,r=s({},{activeOpacity:.5,activeRadius:10,offsetAngle:0,labelWidth:15,ringWidth:30,customRadius:0,border:!1,borderWidth:2,borderColor:"#FFFFFF",centerColor:"#FFFFFF",linearType:"none",customColor:[]},"pie"==t.type?t.extra.pie:t.extra.ring),o={x:t.area[3]+(t.width-t.area[1]-t.area[3])/2,y:t.area[0]+(t.height-t.area[0]-t.area[2])/2};0==a.pieChartLinePadding&&(a.pieChartLinePadding=r.activeRadius*t.pix);var l=Math.min((t.width-t.area[1]-t.area[3])/2-a.pieChartLinePadding-a.pieChartTextPadding-a._pieTextMaxLength_,(t.height-t.area[0]-t.area[2])/2-a.pieChartLinePadding-a.pieChartTextPadding);l=l<10?10:l,r.customRadius>0&&(l=r.customRadius*t.pix),e=R(e,l,i);var u=r.activeRadius*t.pix;if(r.customColor=v(r.linearType,r.customColor,e,a),e=e.map((function(e){return e._start_+=r.offsetAngle*Math.PI/180,e})),e.forEach((function(e,a){t.tooltip&&t.tooltip.index==a&&(n.beginPath(),n.setFillStyle(c(e.color,r.activeOpacity||.5)),n.moveTo(o.x,o.y),n.arc(o.x,o.y,e._radius_+u,e._start_,e._start_+2*e._proportion_*Math.PI),n.closePath(),n.fill()),n.beginPath(),n.setLineWidth(r.borderWidth*t.pix),n.lineJoin="round",n.setStrokeStyle(r.borderColor);var i,s=e.color;"custom"==r.linearType&&(i=n.createCircularGradient?n.createCircularGradient(o.x,o.y,e._radius_):n.createRadialGradient(o.x,o.y,0,o.x,o.y,e._radius_),i.addColorStop(0,c(r.customColor[e.linearIndex],1)),i.addColorStop(1,c(e.color,1)),s=i);n.setFillStyle(s),n.moveTo(o.x,o.y),n.arc(o.x,o.y,e._radius_,e._start_,e._start_+2*e._proportion_*Math.PI),n.closePath(),n.fill(),1==r.border&&n.stroke()})),"ring"===t.type){var d=.6*l;"number"===typeof r.ringWidth&&r.ringWidth>0&&(d=Math.max(0,l-r.ringWidth*t.pix)),n.beginPath(),n.setFillStyle(r.centerColor),n.moveTo(o.x,o.y),n.arc(o.x,o.y,d,0,2*Math.PI),n.closePath(),n.fill()}return!1!==t.dataLabel&&1===i&&ge(e,t,a,n,0,o),1===i&&"ring"===t.type&&le(t,a,n,o),{center:o,radius:l,series:e}}function Pe(e,t){var a=Array(2),n=20037508.34*e/180,i=Math.log(Math.tan((90+t)*Math.PI/360))/(Math.PI/180);return i=20037508.34*i/180,a[0]=n,a[1]=i,a}function Ce(e,t,a,n,i,r){return{x:(t-a.xMin)*n+i,y:(a.yMax-e)*n+r}}function De(e,t,a){if(t[1]==a[1])return!1;if(t[1]>e[1]&&a[1]>e[1])return!1;if(t[1]<e[1]&&a[1]<e[1])return!1;if(t[1]==e[1]&&a[1]>e[1])return!1;if(a[1]==e[1]&&t[1]>e[1])return!1;if(t[0]<e[0]&&a[1]<e[1])return!1;var n=a[0]-(a[0]-t[0])*(a[1]-e[1])/(a[1]-t[1]);return!(n<e[0])}function Me(e,t,a){for(var n=0,i=0;i<t.length;i++){var r=t[i][0];1==t.length&&(r=t[i][0]);for(var o=0;o<r.length-1;o++){var s=r[o],l=r[o+1];a&&(s=Pe(r[o][0],r[o][1]),l=Pe(r[o+1][0],r[o+1][1])),De(e,s,l)&&(n+=1)}}return n%2==1}function Ie(e,t,a){a=0==a?1:a;for(var n=[],i=0;i<a;i++)n[i]=Math.random();return Math.floor(n.reduce((function(e,t){return e+t}))/a*(t-e))+e}function Le(e,t,a,n){for(var i=!1,r=0;r<t.length;r++)if(t[r].area){if(!(e[3]<t[r].area[1]||e[0]>t[r].area[2]||e[1]>t[r].area[3]||e[2]<t[r].area[0])){i=!0;break}if(e[0]<0||e[1]<0||e[2]>a||e[3]>n){i=!0;break}i=!1}return i}function Oe(e,t,a){var n=e.series;switch(t){case"normal":for(var i=0;i<n.length;i++){var r=n[i].name,o=n[i].textSize*e.pix,s=y(r,o,a),l=void 0,c=void 0,u=void 0,d=0;while(1){d++,l=Ie(-e.width/2,e.width/2,5)-s/2,c=Ie(-e.height/2,e.height/2,5)+o/2,u=[l-5+e.width/2,c-5-o+e.height/2,l+s+5+e.width/2,c+5+e.height/2];var h=Le(u,n,e.width,e.height);if(!h)break;if(1e3==d){u=[-100,-100,-100,-100];break}}n[i].area=u}break;case"vertical":for(var f=function(){return Math.random()>.7},p=0;p<n.length;p++){var g=n[p].name,x=n[p].textSize*e.pix,v=y(g,x,a),m=f(),b=void 0,w=void 0,S=void 0,k=void 0,A=0;while(1){A++;var T=void 0;if(m?(b=Ie(-e.width/2,e.width/2,5)-v/2,w=Ie(-e.height/2,e.height/2,5)+x/2,S=[w-5-v+e.width/2,-b-5+e.height/2,w+5+e.width/2,-b+x+5+e.height/2],k=[e.width-(e.width/2-e.height/2)-(-b+x+5+e.height/2)-5,e.height/2-e.width/2+(w-5-v+e.width/2)-5,e.width-(e.width/2-e.height/2)-(-b+x+5+e.height/2)+x,e.height/2-e.width/2+(w-5-v+e.width/2)+v+5],T=Le(k,n,e.height,e.width)):(b=Ie(-e.width/2,e.width/2,5)-v/2,w=Ie(-e.height/2,e.height/2,5)+x/2,S=[b-5+e.width/2,w-5-x+e.height/2,b+v+5+e.width/2,w+5+e.height/2],T=Le(S,n,e.width,e.height)),!T)break;if(1e3==A){S=[-1e3,-1e3,-1e3,-1e3];break}}m?(n[p].area=k,n[p].areav=S):n[p].area=S,n[p].rotate=m}break}return n}function Fe(e,t,a,n,i,r,o){for(var s=0;s<e.length;s++){var c=e[s];if(!1!==c.labelShow){var u=void 0,d=void 0,h=void 0,f=void 0,p=c.formatter?c.formatter(c,s,e,t):l.toFixed(100*c._proportion_)+"%";p=c.labelText?c.labelText:p,"right"==i&&(u=s==e.length-1?(c.funnelArea[2]+o.x)/2:(c.funnelArea[2]+e[s+1].funnelArea[2])/2,d=u+2*r,h=c.funnelArea[1]+n/2,f=c.textSize*t.pix||t.fontSize*t.pix,a.setLineWidth(1*t.pix),a.setStrokeStyle(c.color),a.setFillStyle(c.color),a.beginPath(),a.moveTo(u,h),a.lineTo(d,h),a.stroke(),a.closePath(),a.beginPath(),a.moveTo(d,h),a.arc(d,h,2*t.pix,0,2*Math.PI),a.closePath(),a.fill(),a.beginPath(),a.setFontSize(f),a.setFillStyle(c.textColor||t.fontColor),a.fillText(p,d+5,h+f/2-2),a.closePath(),a.stroke(),a.closePath()),"left"==i&&(u=s==e.length-1?(c.funnelArea[0]+o.x)/2:(c.funnelArea[0]+e[s+1].funnelArea[0])/2,d=u-2*r,h=c.funnelArea[1]+n/2,f=c.textSize*t.pix||t.fontSize*t.pix,a.setLineWidth(1*t.pix),a.setStrokeStyle(c.color),a.setFillStyle(c.color),a.beginPath(),a.moveTo(u,h),a.lineTo(d,h),a.stroke(),a.closePath(),a.beginPath(),a.moveTo(d,h),a.arc(d,h,2,0,2*Math.PI),a.closePath(),a.fill(),a.beginPath(),a.setFontSize(f),a.setFillStyle(c.textColor||t.fontColor),a.fillText(p,d-5-y(p,f,a),h+f/2-2),a.closePath(),a.stroke(),a.closePath())}}}function Ee(e,t,a,n,i,r,o){for(var s=0;s<e.length;s++){var l=e[s],c=void 0,u=void 0;l.centerText&&(c=l.funnelArea[1]+n/2,u=l.centerTextSize*t.pix||t.fontSize*t.pix,a.beginPath(),a.setFontSize(u),a.setFillStyle(l.centerTextColor||"#FFFFFF"),a.fillText(l.centerText,o.x-y(l.centerText,u,a)/2,c+u/2-2),a.closePath(),a.stroke(),a.closePath())}}function Re(e,t){t.save(),t.translate(0,.5),t.restore(),t.draw()}var Be={easeIn:function(e){return Math.pow(e,3)},easeOut:function(e){return Math.pow(e-1,3)+1},easeInOut:function(e){return(e/=.5)<1?.5*Math.pow(e,3):.5*(Math.pow(e-2,3)+2)},linear:function(e){return e}};function Ne(e){this.isStop=!1,e.duration="undefined"===typeof e.duration?1e3:e.duration,e.timing=e.timing||"easeInOut";var t=function(){return"undefined"!==typeof setTimeout?function(e,t){setTimeout((function(){var t=+new Date;e(t)}),t)}:"undefined"!==typeof requestAnimationFrame?requestAnimationFrame:function(e){e(null)}}(),a=null,n=function(i){if(null===i||!0===this.isStop)return e.onProcess&&e.onProcess(1),void(e.onAnimationFinish&&e.onAnimationFinish());if(null===a&&(a=i),i-a<e.duration){var r=(i-a)/e.duration,o=Be[e.timing];r=o(r),e.onProcess&&e.onProcess(r),t(n,17)}else e.onProcess&&e.onProcess(1),e.onAnimationFinish&&e.onAnimationFinish()};n=n.bind(this),t(n,17)}function Ue(e,t,a,n){var r=this,o=this,u=t.series;"pie"!==e&&"ring"!==e&&"mount"!==e&&"rose"!==e&&"funnel"!==e||(u=function(e,t,a){var n=[];if(e.length>0&&e[0].data.constructor.toString().indexOf("Array")>-1){t._pieSeries_=e;for(var i=e[0].data,r=0;r<i.length;r++)i[r].formatter=e[0].formatter,i[r].data=i[r].value,n.push(i[r]);t.series=n}else n=e;return n}(u,t));var d=t.categories;if("mount"===e){d=[];for(var h=0;h<u.length;h++)!1!==u[h].show&&d.push(u[h].name);t.categories=d}u=x(u,t,a);var g=t.animation?t.duration:0;o.animationInstance&&o.animationInstance.stop();var m=null;if("candle"==e){var w=s({},t.extra.candle.average);w.show?(m=function(e,t,a,n){for(var i=[],r=0;r<e.length;r++){for(var o={data:[],name:t[r],color:a[r]},s=0,l=n.length;s<l;s++)if(s<e[r])o.data.push(null);else{for(var c=0,u=0;u<e[r];u++)c+=n[s-u][1];o.data.push(+(c/e[r]).toFixed(3))}i.push(o)}return i}(w.day,w.name,w.color,u[0].data),m=x(m,t,a),t.seriesMA=m):m=t.seriesMA?t.seriesMA=x(t.seriesMA,t,a):u}else m=u;t._series_=u=D(u),t.area=new Array(4);for(var S=0;S<4;S++)t.area[S]=t.padding[S]*t.pix;var k=function(e,t,a,n,i){var r={area:{start:{x:0,y:0},end:{x:0,y:0},width:0,height:0,wholeWidth:0,wholeHeight:0},points:[],widthArr:[],heightArr:[]};if(!1===t.legend.show)return n.legendData=r,r;var o=t.legend.padding*t.pix,s=t.legend.margin*t.pix,l=t.legend.fontSize?t.legend.fontSize*t.pix:a.fontSize,c=15*t.pix,u=5*t.pix,d=Math.max(t.legend.lineHeight*t.pix,l);if("top"==t.legend.position||"bottom"==t.legend.position){for(var h=[],f=0,p=[],g=[],x=0;x<e.length;x++){var v=e[x],m=v.legendText?v.legendText:v.name,b=c+u+y(m||"undefined",l,i)+t.legend.itemGap*t.pix;f+b>t.width-t.area[1]-t.area[3]?(h.push(g),p.push(f-t.legend.itemGap*t.pix),f=b,g=[v]):(f+=b,g.push(v))}if(g.length){h.push(g),p.push(f-t.legend.itemGap*t.pix),r.widthArr=p;var w=Math.max.apply(null,p);switch(t.legend.float){case"left":r.area.start.x=t.area[3],r.area.end.x=t.area[3]+w+2*o;break;case"right":r.area.start.x=t.width-t.area[1]-w-2*o,r.area.end.x=t.width-t.area[1];break;default:r.area.start.x=(t.width-w)/2-o,r.area.end.x=(t.width+w)/2+o}r.area.width=w+2*o,r.area.wholeWidth=w+2*o,r.area.height=h.length*d+2*o,r.area.wholeHeight=h.length*d+2*o+2*s,r.points=h}}else{var S=e.length,k=t.height-t.area[0]-t.area[2]-2*s-2*o,A=Math.min(Math.floor(k/d),S);switch(r.area.height=A*d+2*o,r.area.wholeHeight=A*d+2*o,t.legend.float){case"top":r.area.start.y=t.area[0]+s,r.area.end.y=t.area[0]+s+r.area.height;break;case"bottom":r.area.start.y=t.height-t.area[2]-s-r.area.height,r.area.end.y=t.height-t.area[2]-s;break;default:r.area.start.y=(t.height-r.area.height)/2,r.area.end.y=(t.height+r.area.height)/2}for(var T=S%A===0?S/A:Math.floor(S/A+1),_=[],P=0;P<T;P++){var C=e.slice(P*A,P*A+A);_.push(C)}if(r.points=_,_.length){for(var D=0;D<_.length;D++){for(var M=_[D],I=0,L=0;L<M.length;L++){var O=c+u+y(M[L].name||"undefined",l,i)+t.legend.itemGap*t.pix;O>I&&(I=O)}r.widthArr.push(I),r.heightArr.push(M.length*d+2*o)}for(var F=0,E=0;E<r.widthArr.length;E++)F+=r.widthArr[E];r.area.width=F-t.legend.itemGap*t.pix+2*o,r.area.wholeWidth=r.area.width+o}}switch(t.legend.position){case"top":r.area.start.y=t.area[0]+s,r.area.end.y=t.area[0]+s+r.area.height;break;case"bottom":r.area.start.y=t.height-t.area[2]-r.area.height-s,r.area.end.y=t.height-t.area[2]-s;break;case"left":r.area.start.x=t.area[3],r.area.end.x=t.area[3]+r.area.width;break;case"right":r.area.start.x=t.width-t.area[1]-r.area.width,r.area.end.x=t.width-t.area[1];break}return n.legendData=r,r}(m,t,a,t.chartData,n),_=k.area.wholeHeight,P=k.area.wholeWidth;switch(t.legend.position){case"top":t.area[0]+=_;break;case"bottom":t.area[2]+=_;break;case"left":t.area[3]+=P;break;case"right":t.area[1]+=P;break}var C={},M=0;if("line"===t.type||"column"===t.type||"mount"===t.type||"area"===t.type||"mix"===t.type||"candle"===t.type||"scatter"===t.type||"bubble"===t.type||"bar"===t.type){if(C=ie(u,t,a,n),M=C.yAxisWidth,t.yAxis.showTitle){for(var I=0,ne=0;ne<t.yAxis.data.length;ne++)I=Math.max(I,t.yAxis.data[ne].titleFontSize?t.yAxis.data[ne].titleFontSize*t.pix:a.fontSize);t.area[0]+=I}for(var ve=0,be=0,De=0;De<M.length;De++)"left"==M[De].position?(t.area[3]+=be>0?M[De].width+t.yAxis.padding*t.pix:M[De].width,be+=1):"right"==M[De].position&&(t.area[1]+=ve>0?M[De].width+t.yAxis.padding*t.pix:M[De].width,ve+=1)}else a.yAxisWidth=M;if(t.chartData.yAxisData=C,t.categories&&t.categories.length&&"radar"!==t.type&&"gauge"!==t.type&&"bar"!==t.type){t.chartData.xAxisData=K(t.categories,t);var Me=O(t.categories,t,0,t.chartData.xAxisData.eachSpacing,n),Ie=Me.xAxisHeight,Le=Me.angle;a.xAxisHeight=Ie,a._xAxisTextAngle_=Le,t.area[2]+=Ie,t.chartData.categoriesData=Me}else if("line"===t.type||"area"===t.type||"scatter"===t.type||"bubble"===t.type||"bar"===t.type){t.chartData.xAxisData=F(u,t,a,n),d=t.chartData.xAxisData.rangesFormat;var Be=O(d,t,0,t.chartData.xAxisData.eachSpacing,n),Ue=Be.xAxisHeight,ze=Be.angle;a.xAxisHeight=Ue,a._xAxisTextAngle_=ze,t.area[2]+=Ue,t.chartData.categoriesData=Be}else t.chartData.xAxisData={xAxisPoints:[]};if(t.enableScroll&&"right"==t.xAxis.scrollAlign&&void 0===t._scrollDistance_){var We,je=t.chartData.xAxisData.xAxisPoints,qe=t.chartData.xAxisData.startX,Ge=t.chartData.xAxisData.endX,He=t.chartData.xAxisData.eachSpacing,Je=He*(je.length-1),Ye=Ge-qe;We=Ye-Je,o.scrollOption.currentOffset=We,o.scrollOption.startTouchX=We,o.scrollOption.distance=0,o.scrollOption.lastMoveTime=0,t._scrollDistance_=We}switch("pie"!==e&&"ring"!==e&&"rose"!==e||(a._pieTextMaxLength_=!1===t.dataLabel?0:function(e,t,a,n){e=R(e);for(var i=0,r=0;r<e.length;r++){var o=e[r],s=o.formatter?o.formatter(+o._proportion_.toFixed(2)):l.toFixed(100*o._proportion_)+"%";i=Math.max(i,y(s,o.textSize*n.pix||t.fontSize,a))}return i}(m,a,n,t)),e){case"word":this.animationInstance=new Ne({timing:t.timing,duration:g,onProcess:function(e){n.clearRect(0,0,t.width,t.height),t.rotate&&re(n,t),function(e,t,a,n){var i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,r=s({},{type:"normal",autoColors:!0},t.extra.word);t.chartData.wordCloudData||(t.chartData.wordCloudData=Oe(t,r.type,n)),n.beginPath(),n.setFillStyle(t.background),n.rect(0,0,t.width,t.height),n.fill(),n.save();var o=t.chartData.wordCloudData;n.translate(t.width/2,t.height/2);for(var l=0;l<o.length;l++){n.save(),o[l].rotate&&n.rotate(90*Math.PI/180);var c=o[l].name,u=o[l].textSize*t.pix,d=y(c,u,n);n.beginPath(),n.setStrokeStyle(o[l].color),n.setFillStyle(o[l].color),n.setFontSize(u),o[l].rotate?o[l].areav[0]>0&&(t.tooltip&&t.tooltip.index==l?n.strokeText(c,(o[l].areav[0]+5-t.width/2)*i-d*(1-i)/2,(o[l].areav[1]+5+u-t.height/2)*i):n.fillText(c,(o[l].areav[0]+5-t.width/2)*i-d*(1-i)/2,(o[l].areav[1]+5+u-t.height/2)*i)):o[l].area[0]>0&&(t.tooltip&&t.tooltip.index==l?n.strokeText(c,(o[l].area[0]+5-t.width/2)*i-d*(1-i)/2,(o[l].area[1]+5+u-t.height/2)*i):n.fillText(c,(o[l].area[0]+5-t.width/2)*i-d*(1-i)/2,(o[l].area[1]+5+u-t.height/2)*i)),n.stroke(),n.restore()}n.restore()}(u,t,a,n,e),Re(0,n)},onAnimationFinish:function(){o.uevent.trigger("renderComplete")}});break;case"map":n.clearRect(0,0,t.width,t.height),function(e,t,a,n){var i,r,o=s({},{border:!0,mercator:!1,borderWidth:1,active:!0,borderColor:"#666666",fillOpacity:.6,activeBorderColor:"#f04864",activeFillColor:"#facc14",activeFillOpacity:1},t.extra.map),l=e,u=function(e){for(var t,a={xMin:180,xMax:0,yMin:90,yMax:0},n=0;n<e.length;n++)for(var i=e[n].geometry.coordinates,r=0;r<i.length;r++){t=i[r],1==t.length&&(t=t[0]);for(var o=0;o<t.length;o++){var s=t[o][0],l=t[o][1],c={x:s,y:l};a.xMin=a.xMin<c.x?a.xMin:c.x,a.xMax=a.xMax>c.x?a.xMax:c.x,a.yMin=a.yMin<c.y?a.yMin:c.y,a.yMax=a.yMax>c.y?a.yMax:c.y}}return a}(l);if(o.mercator){var d=Pe(u.xMax,u.yMax),h=Pe(u.xMin,u.yMin);u.xMax=d[0],u.yMax=d[1],u.xMin=h[0],u.yMin=h[1]}for(var f=t.width/Math.abs(u.xMax-u.xMin),p=t.height/Math.abs(u.yMax-u.yMin),g=f<p?f:p,x=t.width/2-Math.abs(u.xMax-u.xMin)/2*g,v=t.height/2-Math.abs(u.yMax-u.yMin)/2*g,m=0;m<l.length;m++){n.beginPath(),n.setLineWidth(o.borderWidth*t.pix),n.setStrokeStyle(o.borderColor),n.setFillStyle(c(e[m].color,e[m].fillOpacity||o.fillOpacity)),1==o.active&&t.tooltip&&t.tooltip.index==m&&(n.setStrokeStyle(o.activeBorderColor),n.setFillStyle(c(o.activeFillColor,o.activeFillOpacity)));for(var b=l[m].geometry.coordinates,w=0;w<b.length;w++){i=b[w],1==i.length&&(i=i[0]);for(var S=0;S<i.length;S++){var k=Array(2);k=o.mercator?Pe(i[S][0],i[S][1]):i[S],r=Ce(k[1],k[0],u,g,x,v),0===S?(n.beginPath(),n.moveTo(r.x,r.y)):n.lineTo(r.x,r.y)}n.fill(),1==o.border&&n.stroke()}}if(1==t.dataLabel)for(m=0;m<l.length;m++){var A=l[m].properties.centroid;if(A){o.mercator&&(A=Pe(l[m].properties.centroid[0],l[m].properties.centroid[1])),r=Ce(A[1],A[0],u,g,x,v);var T=l[m].textSize*t.pix||a.fontSize,_=l[m].textColor||t.fontColor;o.active&&o.activeTextColor&&t.tooltip&&t.tooltip.index==m&&(_=o.activeTextColor);var P=l[m].properties.name;n.beginPath(),n.setFontSize(T),n.setFillStyle(_),n.fillText(P,r.x-y(P,T,n)/2,r.y+T/2),n.closePath(),n.stroke()}}t.chartData.mapData={bounds:u,scale:g,xoffset:x,yoffset:v,mercator:o.mercator},we(t,a,n,1),n.draw()}(u,t,a,n),setTimeout((function(){r.uevent.trigger("renderComplete")}),50);break;case"funnel":this.animationInstance=new Ne({timing:t.timing,duration:g,onProcess:function(e){n.clearRect(0,0,t.width,t.height),t.rotate&&re(n,t),t.chartData.funnelData=function(e,t,a,n){var i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,r=s({},{type:"funnel",activeWidth:10,activeOpacity:.3,border:!1,borderWidth:2,borderColor:"#FFFFFF",fillOpacity:1,minSize:0,labelAlign:"right",linearType:"none",customColor:[]},t.extra.funnel),o=(t.height-t.area[0]-t.area[2])/e.length,l={x:t.area[3]+(t.width-t.area[1]-t.area[3])/2,y:t.height-t.area[2]},u=r.activeWidth*t.pix,d=Math.min((t.width-t.area[1]-t.area[3])/2-u,(t.height-t.area[0]-t.area[2])/2-u),h=B(e,d,r,o,i);if(n.save(),n.translate(l.x,l.y),r.customColor=v(r.linearType,r.customColor,e,a),"pyramid"==r.type)for(var f=0;f<h.length;f++){if(f==h.length-1){t.tooltip&&t.tooltip.index==f&&(n.beginPath(),n.setFillStyle(c(h[f].color,r.activeOpacity)),n.moveTo(-u,-o),n.lineTo(-h[f].radius-u,0),n.lineTo(h[f].radius+u,0),n.lineTo(u,-o),n.lineTo(-u,-o),n.closePath(),n.fill()),h[f].funnelArea=[l.x-h[f].radius,l.y-o*(f+1),l.x+h[f].radius,l.y-o*f],n.beginPath(),n.setLineWidth(r.borderWidth*t.pix),n.setStrokeStyle(r.borderColor);var p=c(h[f].color,r.fillOpacity);if("custom"==r.linearType){var g=n.createLinearGradient(h[f].radius,-o,-h[f].radius,-o);g.addColorStop(0,c(h[f].color,r.fillOpacity)),g.addColorStop(.5,c(r.customColor[h[f].linearIndex],r.fillOpacity)),g.addColorStop(1,c(h[f].color,r.fillOpacity)),p=g}n.setFillStyle(p),n.moveTo(0,-o),n.lineTo(-h[f].radius,0),n.lineTo(h[f].radius,0),n.lineTo(0,-o),n.closePath(),n.fill(),1==r.border&&n.stroke()}else{t.tooltip&&t.tooltip.index==f&&(n.beginPath(),n.setFillStyle(c(h[f].color,r.activeOpacity)),n.moveTo(0,0),n.lineTo(-h[f].radius-u,0),n.lineTo(-h[f+1].radius-u,-o),n.lineTo(h[f+1].radius+u,-o),n.lineTo(h[f].radius+u,0),n.lineTo(0,0),n.closePath(),n.fill()),h[f].funnelArea=[l.x-h[f].radius,l.y-o*(f+1),l.x+h[f].radius,l.y-o*f],n.beginPath(),n.setLineWidth(r.borderWidth*t.pix),n.setStrokeStyle(r.borderColor);p=c(h[f].color,r.fillOpacity);if("custom"==r.linearType){g=n.createLinearGradient(h[f].radius,-o,-h[f].radius,-o);g.addColorStop(0,c(h[f].color,r.fillOpacity)),g.addColorStop(.5,c(r.customColor[h[f].linearIndex],r.fillOpacity)),g.addColorStop(1,c(h[f].color,r.fillOpacity)),p=g}n.setFillStyle(p),n.moveTo(0,0),n.lineTo(-h[f].radius,0),n.lineTo(-h[f+1].radius,-o),n.lineTo(h[f+1].radius,-o),n.lineTo(h[f].radius,0),n.lineTo(0,0),n.closePath(),n.fill(),1==r.border&&n.stroke()}n.translate(0,-o)}else{n.translate(0,-(h.length-1)*o);for(var x=0;x<h.length;x++){if(x==h.length-1){t.tooltip&&t.tooltip.index==x&&(n.beginPath(),n.setFillStyle(c(h[x].color,r.activeOpacity)),n.moveTo(-u-r.minSize/2,0),n.lineTo(-h[x].radius-u,-o),n.lineTo(h[x].radius+u,-o),n.lineTo(u+r.minSize/2,0),n.lineTo(-u-r.minSize/2,0),n.closePath(),n.fill()),h[x].funnelArea=[l.x-h[x].radius,l.y-o,l.x+h[x].radius,l.y],n.beginPath(),n.setLineWidth(r.borderWidth*t.pix),n.setStrokeStyle(r.borderColor);p=c(h[x].color,r.fillOpacity);if("custom"==r.linearType){g=n.createLinearGradient(h[x].radius,-o,-h[x].radius,-o);g.addColorStop(0,c(h[x].color,r.fillOpacity)),g.addColorStop(.5,c(r.customColor[h[x].linearIndex],r.fillOpacity)),g.addColorStop(1,c(h[x].color,r.fillOpacity)),p=g}n.setFillStyle(p),n.moveTo(0,0),n.lineTo(-r.minSize/2,0),n.lineTo(-h[x].radius,-o),n.lineTo(h[x].radius,-o),n.lineTo(r.minSize/2,0),n.lineTo(0,0),n.closePath(),n.fill(),1==r.border&&n.stroke()}else{t.tooltip&&t.tooltip.index==x&&(n.beginPath(),n.setFillStyle(c(h[x].color,r.activeOpacity)),n.moveTo(0,0),n.lineTo(-h[x+1].radius-u,0),n.lineTo(-h[x].radius-u,-o),n.lineTo(h[x].radius+u,-o),n.lineTo(h[x+1].radius+u,0),n.lineTo(0,0),n.closePath(),n.fill()),h[x].funnelArea=[l.x-h[x].radius,l.y-o*(h.length-x),l.x+h[x].radius,l.y-o*(h.length-x-1)],n.beginPath(),n.setLineWidth(r.borderWidth*t.pix),n.setStrokeStyle(r.borderColor);p=c(h[x].color,r.fillOpacity);if("custom"==r.linearType){g=n.createLinearGradient(h[x].radius,-o,-h[x].radius,-o);g.addColorStop(0,c(h[x].color,r.fillOpacity)),g.addColorStop(.5,c(r.customColor[h[x].linearIndex],r.fillOpacity)),g.addColorStop(1,c(h[x].color,r.fillOpacity)),p=g}n.setFillStyle(p),n.moveTo(0,0),n.lineTo(-h[x+1].radius,0),n.lineTo(-h[x].radius,-o),n.lineTo(h[x].radius,-o),n.lineTo(h[x+1].radius,0),n.lineTo(0,0),n.closePath(),n.fill(),1==r.border&&n.stroke()}n.translate(0,o)}}return n.restore(),!1!==t.dataLabel&&1===i&&Fe(h,t,n,o,r.labelAlign,u,l),1===i&&Ee(h,t,n,o,r.labelAlign,u,l),{center:l,radius:d,series:h}}(u,t,a,n,e),Te(t.series,t,a,n,t.chartData),we(t,a,n,e),Re(0,n)},onAnimationFinish:function(){o.uevent.trigger("renderComplete")}});break;case"line":this.animationInstance=new Ne({timing:t.timing,duration:g,onProcess:function(e){n.clearRect(0,0,t.width,t.height),t.rotate&&re(n,t),ke(0,t,0,n),Se(d,t,a,n);var i=function(e,t,a,n){var i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,r=s({},{type:"straight",width:2,activeType:"none",linearType:"none",onShadow:!1,animation:"vertical"},t.extra.line);r.width*=t.pix;var o=t.chartData.xAxisData,l=o.xAxisPoints,u=o.eachSpacing,d=[];n.save();var h=0,p=t.width+u;return t._scrollDistance_&&0!==t._scrollDistance_&&!0===t.enableScroll&&(n.translate(t._scrollDistance_,0),h=-t._scrollDistance_-2*u+t.area[3],p=h+(t.xAxis.itemCount+4)*u),e.forEach((function(e,o){var s,g,x;n.beginPath(),n.setStrokeStyle(e.color),n.moveTo(-1e4,-1e4),n.lineTo(-10001,-10001),n.stroke(),s=[].concat(t.chartData.yAxisData.ranges[e.index]),g=s.pop(),x=s.shift();var v=e.data,m=Q(v,g,x,l,u,t,a,r,i);d.push(m);var y=L(m,e);if("dash"==e.lineType){var b=e.dashLength?e.dashLength:8;b*=t.pix,n.setLineDash([b,b])}n.beginPath();var w=e.color;if("none"!==r.linearType&&e.linearColor&&e.linearColor.length>0){for(var S=n.createLinearGradient(t.chartData.xAxisData.startX,t.height/2,t.chartData.xAxisData.endX,t.height/2),k=0;k<e.linearColor.length;k++)S.addColorStop(e.linearColor[k][0],c(e.linearColor[k][1],1));w=S}n.setStrokeStyle(w),1==r.onShadow&&e.setShadow&&e.setShadow.length>0?n.setShadow(e.setShadow[0],e.setShadow[1],e.setShadow[2],e.setShadow[3]):n.setShadow(0,0,0,"rgba(0,0,0,0)"),n.setLineWidth(r.width),y.forEach((function(e,t){if(1===e.length)n.moveTo(e[0].x,e[0].y);else{n.moveTo(e[0].x,e[0].y);var a=0;if("curve"===r.type)for(var i=0;i<e.length;i++){var o=e[i];if(0==a&&o.x>h&&(n.moveTo(o.x,o.y),a=1),i>0&&o.x>h&&o.x<p){var s=f(e,i-1);n.bezierCurveTo(s.ctrA.x,s.ctrA.y,s.ctrB.x,s.ctrB.y,o.x,o.y)}}if("straight"===r.type)for(var l=0;l<e.length;l++){var c=e[l];0==a&&c.x>h&&(n.moveTo(c.x,c.y),a=1),l>0&&c.x>h&&c.x<p&&n.lineTo(c.x,c.y)}if("step"===r.type)for(var u=0;u<e.length;u++){var d=e[u];0==a&&d.x>h&&(n.moveTo(d.x,d.y),a=1),u>0&&d.x>h&&d.x<p&&(n.lineTo(d.x,e[u-1].y),n.lineTo(d.x,d.y))}n.moveTo(e[0].x,e[0].y)}})),n.stroke(),n.setLineDash([]),!1!==t.dataPointShape&&oe(m,e.color,e.pointShape,n,t),se(m,e.color,e.pointShape,n,t,r)})),!1!==t.dataLabel&&1===i&&e.forEach((function(e,r){var o,s,c;o=[].concat(t.chartData.yAxisData.ranges[e.index]),s=o.pop(),c=o.shift();var d=e.data,h=V(d,s,c,l,u,t,a,i);ce(h,e,a,n,t)})),n.restore(),{xAxisPoints:l,calPoints:d,eachSpacing:u}}(u,t,a,n,e),r=i.xAxisPoints,o=i.calPoints,l=i.eachSpacing;t.chartData.xAxisPoints=r,t.chartData.calPoints=o,t.chartData.eachSpacing=l,Ae(0,t,a,n),!1!==t.enableMarkLine&&1===e&&xe(t,0,n),Te(t.series,t,a,n,t.chartData),we(t,a,n,e),Re(0,n)},onAnimationFinish:function(){o.uevent.trigger("renderComplete")}});break;case"scatter":this.animationInstance=new Ne({timing:t.timing,duration:g,onProcess:function(e){n.clearRect(0,0,t.width,t.height),t.rotate&&re(n,t),ke(0,t,0,n),Se(d,t,a,n);var i=function(e,t,a,n){var i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,r=(s({},{type:"circle"},t.extra.scatter),t.chartData.xAxisData),o=r.xAxisPoints,l=r.eachSpacing,c=[];n.save();return t.width,t._scrollDistance_&&0!==t._scrollDistance_&&!0===t.enableScroll&&(n.translate(t._scrollDistance_,0),-t._scrollDistance_-2*l+t.area[3],t.xAxis.itemCount),e.forEach((function(e,r){var s,c,u;s=[].concat(t.chartData.yAxisData.ranges[e.index]),c=s.pop(),u=s.shift();var d=e.data,h=V(d,c,u,o,l,t,a,i);n.beginPath(),n.setStrokeStyle(e.color),n.setFillStyle(e.color),n.setLineWidth(1*t.pix);var f=e.pointShape;if("diamond"===f)h.forEach((function(e,t){null!==e&&(n.moveTo(e.x,e.y-4.5),n.lineTo(e.x-4.5,e.y),n.lineTo(e.x,e.y****),n.lineTo(e.x****,e.y),n.lineTo(e.x,e.y-4.5))}));else if("circle"===f)h.forEach((function(e,a){null!==e&&(n.moveTo(e.x*****t.pix,e.y),n.arc(e.x,e.y,3*t.pix,0,2*Math.PI,!1))}));else if("square"===f)h.forEach((function(e,t){null!==e&&(n.moveTo(e.x-3.5,e.y-3.5),n.rect(e.x-3.5,e.y-3.5,7,7))}));else if("triangle"===f)h.forEach((function(e,t){null!==e&&(n.moveTo(e.x,e.y-4.5),n.lineTo(e.x-4.5,e.y****),n.lineTo(e.x****,e.y****),n.lineTo(e.x,e.y-4.5))}));else if("triangle"===f)return;n.closePath(),n.fill(),n.stroke()})),!1!==t.dataLabel&&1===i&&e.forEach((function(e,r){var s,c,u;s=[].concat(t.chartData.yAxisData.ranges[e.index]),c=s.pop(),u=s.shift();var d=e.data,h=V(d,c,u,o,l,t,a,i);ce(h,e,a,n,t)})),n.restore(),{xAxisPoints:o,calPoints:c,eachSpacing:l}}(u,t,a,n,e),r=i.xAxisPoints,o=i.calPoints,l=i.eachSpacing;t.chartData.xAxisPoints=r,t.chartData.calPoints=o,t.chartData.eachSpacing=l,Ae(0,t,a,n),!1!==t.enableMarkLine&&1===e&&xe(t,0,n),Te(t.series,t,a,n,t.chartData),we(t,a,n,e),Re(0,n)},onAnimationFinish:function(){o.uevent.trigger("renderComplete")}});break;case"bubble":this.animationInstance=new Ne({timing:t.timing,duration:g,onProcess:function(e){n.clearRect(0,0,t.width,t.height),t.rotate&&re(n,t),ke(0,t,0,n),Se(d,t,a,n);var i=function(e,t,a,n){var i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,r=s({},{opacity:1,border:2},t.extra.bubble),o=t.chartData.xAxisData,l=o.xAxisPoints,u=o.eachSpacing,d=[];n.save();return t.width,t._scrollDistance_&&0!==t._scrollDistance_&&!0===t.enableScroll&&(n.translate(t._scrollDistance_,0),-t._scrollDistance_-2*u+t.area[3],t.xAxis.itemCount),e.forEach((function(e,o){var s,d,h;s=[].concat(t.chartData.yAxisData.ranges[e.index]),d=s.pop(),h=s.shift();var f=e.data,p=V(f,d,h,l,u,t,a,i);n.beginPath(),n.setStrokeStyle(e.color),n.setLineWidth(r.border*t.pix),n.setFillStyle(c(e.color,r.opacity)),p.forEach((function(e,a){n.moveTo(e.x+e.r,e.y),n.arc(e.x,e.y,e.r*t.pix,0,2*Math.PI,!1)})),n.closePath(),n.fill(),n.stroke(),!1!==t.dataLabel&&1===i&&p.forEach((function(i,r){n.beginPath();var o=e.textSize*t.pix||a.fontSize;n.setFontSize(o),n.setFillStyle(e.textColor||"#FFFFFF"),n.setTextAlign("center"),n.fillText(String(i.t),i.x,i.y+o/2),n.closePath(),n.stroke(),n.setTextAlign("left")}))})),n.restore(),{xAxisPoints:l,calPoints:d,eachSpacing:u}}(u,t,a,n,e),r=i.xAxisPoints,o=i.calPoints,l=i.eachSpacing;t.chartData.xAxisPoints=r,t.chartData.calPoints=o,t.chartData.eachSpacing=l,Ae(0,t,a,n),!1!==t.enableMarkLine&&1===e&&xe(t,0,n),Te(t.series,t,a,n,t.chartData),we(t,a,n,e),Re(0,n)},onAnimationFinish:function(){o.uevent.trigger("renderComplete")}});break;case"mix":this.animationInstance=new Ne({timing:t.timing,duration:g,onProcess:function(e){n.clearRect(0,0,t.width,t.height),t.rotate&&re(n,t),ke(0,t,0,n),Se(d,t,a,n);var r=function(e,t,a,n){var r=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,o=t.chartData.xAxisData,l=o.xAxisPoints,u=o.eachSpacing,d=s({},{width:u/2,barBorderCircle:!1,barBorderRadius:[],seriesGap:2,linearType:"none",linearOpacity:1,customColor:[],colorStop:0},t.extra.mix.column),h=s({},{opacity:.2,gradient:!1},t.extra.mix.area),p=s({},{width:2},t.extra.mix.line),g=t.height-t.area[2],x=[],m=0,y=0;e.forEach((function(e,t){"column"==e.type&&(y+=1)})),n.save();var b=-2,w=l.length+2,S=0,k=t.width+u;if(t._scrollDistance_&&0!==t._scrollDistance_&&!0===t.enableScroll&&(n.translate(t._scrollDistance_,0),b=Math.floor(-t._scrollDistance_/u)-2,w=b+t.xAxis.itemCount+4,S=-t._scrollDistance_-2*u+t.area[3],k=S+(t.xAxis.itemCount+4)*u),d.customColor=v(d.linearType,d.customColor,e,a),e.forEach((function(e,o){var s,v,A;s=[].concat(t.chartData.yAxisData.ranges[e.index]),v=s.pop(),A=s.shift();var T=e.data,_=V(T,v,A,l,u,t,a,r);if(x.push(_),"column"==e.type){_=q(_,u,y,m,0,t);for(var P=0;P<_.length;P++){var C=_[P];if(null!==C&&P>b&&P<w){var D=C.x-C.width/2;t.height,C.y,t.area[2];n.beginPath();var M=C.color||e.color,I=C.color||e.color;if("none"!==d.linearType){var O=n.createLinearGradient(D,C.y,D,t.height-t.area[2]);"opacity"==d.linearType?(O.addColorStop(0,c(M,d.linearOpacity)),O.addColorStop(1,c(M,1))):(O.addColorStop(0,c(d.customColor[e.linearIndex],d.linearOpacity)),O.addColorStop(d.colorStop,c(d.customColor[e.linearIndex],d.linearOpacity)),O.addColorStop(1,c(M,1))),M=O}if(d.barBorderRadius&&4===d.barBorderRadius.length||d.barBorderCircle){var F=D,E=C.y,R=C.width,B=t.height-t.area[2]-C.y;d.barBorderCircle&&(d.barBorderRadius=[R/2,R/2,0,0]);var N=(0,i.default)(d.barBorderRadius,4),U=N[0],z=N[1],W=N[2],j=N[3],G=Math.min(R/2,B/2);U=U>G?G:U,z=z>G?G:z,W=W>G?G:W,j=j>G?G:j,U=U<0?0:U,z=z<0?0:z,W=W<0?0:W,j=j<0?0:j,n.arc(F+U,E+U,U,-Math.PI,-Math.PI/2),n.arc(F+R-z,E+z,z,-Math.PI/2,0),n.arc(F+R-W,E+B-W,W,0,Math.PI/2),n.arc(F+j,E+B-j,j,Math.PI/2,Math.PI)}else n.moveTo(D,C.y),n.lineTo(D+C.width,C.y),n.lineTo(D+C.width,t.height-t.area[2]),n.lineTo(D,t.height-t.area[2]),n.lineTo(D,C.y),n.setLineWidth(1),n.setStrokeStyle(I);n.setFillStyle(M),n.closePath(),n.fill()}}m+=1}if("area"==e.type)for(var H=L(_,e),J=0;J<H.length;J++){var Y=H[J];if(n.beginPath(),n.setStrokeStyle(e.color),n.setStrokeStyle(c(e.color,h.opacity)),h.gradient){var K=n.createLinearGradient(0,t.area[0],0,t.height-t.area[2]);K.addColorStop("0",c(e.color,h.opacity)),K.addColorStop("1.0",c("#FFFFFF",.1)),n.setFillStyle(K)}else n.setFillStyle(c(e.color,h.opacity));if(n.setLineWidth(2*t.pix),Y.length>1){var X=Y[0],Q=Y[Y.length-1];n.moveTo(X.x,X.y);var Z=0;if("curve"===e.style)for(var $=0;$<Y.length;$++){var ee=Y[$];if(0==Z&&ee.x>S&&(n.moveTo(ee.x,ee.y),Z=1),$>0&&ee.x>S&&ee.x<k){var te=f(Y,$-1);n.bezierCurveTo(te.ctrA.x,te.ctrA.y,te.ctrB.x,te.ctrB.y,ee.x,ee.y)}}else for(var ae=0;ae<Y.length;ae++){var ne=Y[ae];0==Z&&ne.x>S&&(n.moveTo(ne.x,ne.y),Z=1),ae>0&&ne.x>S&&ne.x<k&&n.lineTo(ne.x,ne.y)}n.lineTo(Q.x,g),n.lineTo(X.x,g),n.lineTo(X.x,X.y)}else{var ie=Y[0];n.moveTo(ie.x-u/2,ie.y)}n.closePath(),n.fill()}if("line"==e.type){var re=L(_,e);re.forEach((function(a,i){if("dash"==e.lineType){var r=e.dashLength?e.dashLength:8;r*=t.pix,n.setLineDash([r,r])}if(n.beginPath(),n.setStrokeStyle(e.color),n.setLineWidth(p.width*t.pix),1===a.length)n.moveTo(a[0].x,a[0].y);else{n.moveTo(a[0].x,a[0].y);var o=0;if("curve"==e.style)for(var s=0;s<a.length;s++){var l=a[s];if(0==o&&l.x>S&&(n.moveTo(l.x,l.y),o=1),s>0&&l.x>S&&l.x<k){var c=f(a,s-1);n.bezierCurveTo(c.ctrA.x,c.ctrA.y,c.ctrB.x,c.ctrB.y,l.x,l.y)}}else for(var u=0;u<a.length;u++){var d=a[u];0==o&&d.x>S&&(n.moveTo(d.x,d.y),o=1),u>0&&d.x>S&&d.x<k&&n.lineTo(d.x,d.y)}n.moveTo(a[0].x,a[0].y)}n.stroke(),n.setLineDash([])}))}"point"==e.type&&(e.addPoint=!0),1==e.addPoint&&"column"!==e.type&&oe(_,e.color,e.pointShape,n,t)})),!1!==t.dataLabel&&1===r){m=0;e.forEach((function(e,i){var o,s,c;o=[].concat(t.chartData.yAxisData.ranges[e.index]),s=o.pop(),c=o.shift();var d=e.data,h=V(d,s,c,l,u,t,a,r);"column"!==e.type?ce(h,e,a,n,t):(h=q(h,u,y,m,0,t),ce(h,e,a,n,t),m+=1)}))}return n.restore(),{xAxisPoints:l,calPoints:x,eachSpacing:u}}(u,t,a,n,e),o=r.xAxisPoints,l=r.calPoints,h=r.eachSpacing;t.chartData.xAxisPoints=o,t.chartData.calPoints=l,t.chartData.eachSpacing=h,Ae(0,t,a,n),!1!==t.enableMarkLine&&1===e&&xe(t,0,n),Te(t.series,t,a,n,t.chartData),we(t,a,n,e),Re(0,n)},onAnimationFinish:function(){o.uevent.trigger("renderComplete")}});break;case"column":this.animationInstance=new Ne({timing:t.timing,duration:g,onProcess:function(e){n.clearRect(0,0,t.width,t.height),t.rotate&&re(n,t),ke(0,t,0,n),Se(d,t,a,n);var r=function(e,t,a,n){var r=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,o=t.chartData.xAxisData,l=o.xAxisPoints,u=o.eachSpacing,d=s({},{type:"group",width:u/2,meterBorder:4,meterFillColor:"#FFFFFF",barBorderCircle:!1,barBorderRadius:[],seriesGap:2,linearType:"none",linearOpacity:1,customColor:[],colorStop:0,labelPosition:"outside"},t.extra.column),h=[];n.save();var f=-2,p=l.length+2;return t._scrollDistance_&&0!==t._scrollDistance_&&!0===t.enableScroll&&(n.translate(t._scrollDistance_,0),f=Math.floor(-t._scrollDistance_/u)-2,p=f+t.xAxis.itemCount+4),t.tooltip&&t.tooltip.textList&&t.tooltip.textList.length&&1===r&&me(t.tooltip.offset.x,t,0,n,u),d.customColor=v(d.linearType,d.customColor,e,a),e.forEach((function(o,s){var g,x,v;g=[].concat(t.chartData.yAxisData.ranges[o.index]),x=g.pop(),v=g.shift();var m=t.height-t.area[0]-t.area[2],y=m*(0-x)/(v-x),b=t.height-Math.round(y)-t.area[2];o.zeroPoints=b;var w=o.data;switch(d.type){case"group":var S=Z(w,x,v,l,u,t,a,b,r),k=te(w,x,v,l,u,t,a,s,e,r);h.push(k),S=q(S,u,e.length,s,0,t);for(var A=0;A<S.length;A++){var T=S[A];if(null!==T&&A>f&&A<p){var _=T.x-T.width/2,P=t.height-T.y-t.area[2];n.beginPath();var C=T.color||o.color,D=T.color||o.color;if("none"!==d.linearType){var M=n.createLinearGradient(_,T.y,_,b);"opacity"==d.linearType?(M.addColorStop(0,c(C,d.linearOpacity)),M.addColorStop(1,c(C,1))):(M.addColorStop(0,c(d.customColor[o.linearIndex],d.linearOpacity)),M.addColorStop(d.colorStop,c(d.customColor[o.linearIndex],d.linearOpacity)),M.addColorStop(1,c(C,1))),C=M}if(d.barBorderRadius&&4===d.barBorderRadius.length||!0===d.barBorderCircle){var I=_,L=T.y>b?b:T.y,O=T.width,F=Math.abs(b-T.y);d.barBorderCircle&&(d.barBorderRadius=[O/2,O/2,0,0]),T.y>b&&(d.barBorderRadius=[0,0,O/2,O/2]);var E=(0,i.default)(d.barBorderRadius,4),R=E[0],B=E[1],N=E[2],U=E[3],z=Math.min(O/2,F/2);R=R>z?z:R,B=B>z?z:B,N=N>z?z:N,U=U>z?z:U,R=R<0?0:R,B=B<0?0:B,N=N<0?0:N,U=U<0?0:U,n.arc(I+R,L+R,R,-Math.PI,-Math.PI/2),n.arc(I+O-B,L+B,B,-Math.PI/2,0),n.arc(I+O-N,L+F-N,N,0,Math.PI/2),n.arc(I+U,L+F-U,U,Math.PI/2,Math.PI)}else n.moveTo(_,T.y),n.lineTo(_+T.width,T.y),n.lineTo(_+T.width,b),n.lineTo(_,b),n.lineTo(_,T.y),n.setLineWidth(1),n.setStrokeStyle(D);n.setFillStyle(C),n.closePath(),n.fill()}}break;case"stack":S=te(w,x,v,l,u,t,a,s,e,r);h.push(S),S=J(S,u,e.length,0,0,t);for(var W=0;W<S.length;W++){var j=S[W];if(null!==j&&W>f&&W<p){n.beginPath();C=j.color||o.color,_=j.x-j.width/2+1,P=t.height-j.y-t.area[2];var G=t.height-j.y0-t.area[2];s>0&&(P-=G),n.setFillStyle(C),n.moveTo(_,j.y),n.fillRect(_,j.y,j.width,P),n.closePath(),n.fill()}}break;case"meter":S=V(w,x,v,l,u,t,a,r);h.push(S),S=H(S,u,e.length,s,0,t,d.meterBorder);for(var Y=0;Y<S.length;Y++){var K=S[Y];if(null!==K&&Y>f&&Y<p){n.beginPath(),0==s&&d.meterBorder>0&&(n.setStrokeStyle(o.color),n.setLineWidth(d.meterBorder*t.pix)),0==s?n.setFillStyle(d.meterFillColor):n.setFillStyle(K.color||o.color);_=K.x-K.width/2,P=t.height-K.y-t.area[2];if(d.barBorderRadius&&4===d.barBorderRadius.length||!0===d.barBorderCircle){var X=_,Q=K.y,$=K.width,ee=b-K.y;d.barBorderCircle&&(d.barBorderRadius=[$/2,$/2,0,0]);var ae=(0,i.default)(d.barBorderRadius,4),ne=ae[0],ie=ae[1],re=ae[2],oe=ae[3],se=Math.min($/2,ee/2);ne=ne>se?se:ne,ie=ie>se?se:ie,re=re>se?se:re,oe=oe>se?se:oe,ne=ne<0?0:ne,ie=ie<0?0:ie,re=re<0?0:re,oe=oe<0?0:oe,n.arc(X+ne,Q+ne,ne,-Math.PI,-Math.PI/2),n.arc(X+$-ie,Q+ie,ie,-Math.PI/2,0),n.arc(X+$-re,Q+ee-re,re,0,Math.PI/2),n.arc(X+oe,Q+ee-oe,oe,Math.PI/2,Math.PI),n.fill()}else n.moveTo(_,K.y),n.lineTo(_+K.width,K.y),n.lineTo(_+K.width,b),n.lineTo(_,b),n.lineTo(_,K.y),n.fill();0==s&&d.meterBorder>0&&(n.closePath(),n.stroke())}}break}})),!1!==t.dataLabel&&1===r&&e.forEach((function(i,o){var s,c,h;s=[].concat(t.chartData.yAxisData.ranges[i.index]),c=s.pop(),h=s.shift();var f=i.data;switch(d.type){case"group":var p=Z(f,c,h,l,u,t,a,r);p=q(p,u,e.length,o,0,t),ue(p,i,a,n,t);break;case"stack":p=te(f,c,h,l,u,t,a,o,e,r);ue(p,i,a,n,t);break;case"meter":p=V(f,c,h,l,u,t,a,r);ue(p,i,a,n,t);break}})),n.restore(),{xAxisPoints:l,calPoints:h,eachSpacing:u}}(u,t,a,n,e),o=r.xAxisPoints,l=r.calPoints,h=r.eachSpacing;t.chartData.xAxisPoints=o,t.chartData.calPoints=l,t.chartData.eachSpacing=h,Ae(0,t,a,n),!1!==t.enableMarkLine&&1===e&&xe(t,0,n),Te(t.series,t,a,n,t.chartData),we(t,a,n,e),Re(0,n)},onAnimationFinish:function(){o.uevent.trigger("renderComplete")}});break;case"mount":this.animationInstance=new Ne({timing:t.timing,duration:g,onProcess:function(e){n.clearRect(0,0,t.width,t.height),t.rotate&&re(n,t),ke(0,t,0,n),Se(d,t,a,n);var r=function(e,t,a,n){var r=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,o=t.chartData.xAxisData,l=o.xAxisPoints,u=o.eachSpacing,d=s({},{type:"mount",widthRatio:1,borderWidth:1,barBorderCircle:!1,barBorderRadius:[],linearType:"none",linearOpacity:1,customColor:[],colorStop:0},t.extra.mount);d.widthRatio=d.widthRatio<=0?0:d.widthRatio,d.widthRatio=d.widthRatio>=2?2:d.widthRatio,n.save();var h,f,p,g=-2,x=l.length+2;t._scrollDistance_&&0!==t._scrollDistance_&&!0===t.enableScroll&&(n.translate(t._scrollDistance_,0),g=Math.floor(-t._scrollDistance_/u)-2,x=g+t.xAxis.itemCount+4),d.customColor=v(d.linearType,d.customColor,e,a),h=[].concat(t.chartData.yAxisData.ranges[0]),f=h.pop(),p=h.shift();var m=t.height-t.area[0]-t.area[2],y=m*(0-f)/(p-f),b=t.height-Math.round(y)-t.area[2],w=$(e,f,p,l,u,t,d,b,r);switch(d.type){case"bar":for(var S=0;S<w.length;S++){var k=w[S];if(null!==k&&S>g&&S<x){var A=k.x-u*d.widthRatio/2,T=t.height-k.y-t.area[2];n.beginPath();var _=k.color||e[S].color,P=k.color||e[S].color;if("none"!==d.linearType){var C=n.createLinearGradient(A,k.y,A,b);"opacity"==d.linearType?(C.addColorStop(0,c(_,d.linearOpacity)),C.addColorStop(1,c(_,1))):(C.addColorStop(0,c(d.customColor[e[S].linearIndex],d.linearOpacity)),C.addColorStop(d.colorStop,c(d.customColor[e[S].linearIndex],d.linearOpacity)),C.addColorStop(1,c(_,1))),_=C}if(d.barBorderRadius&&4===d.barBorderRadius.length||!0===d.barBorderCircle){var D=A,M=k.y>b?b:k.y,I=k.width,L=Math.abs(b-k.y);d.barBorderCircle&&(d.barBorderRadius=[I/2,I/2,0,0]),k.y>b&&(d.barBorderRadius=[0,0,I/2,I/2]);var O=(0,i.default)(d.barBorderRadius,4),F=O[0],E=O[1],R=O[2],B=O[3],N=Math.min(I/2,L/2);F=F>N?N:F,E=E>N?N:E,R=R>N?N:R,B=B>N?N:B,F=F<0?0:F,E=E<0?0:E,R=R<0?0:R,B=B<0?0:B,n.arc(D+F,M+F,F,-Math.PI,-Math.PI/2),n.arc(D+I-E,M+E,E,-Math.PI/2,0),n.arc(D+I-R,M+L-R,R,0,Math.PI/2),n.arc(D+B,M+L-B,B,Math.PI/2,Math.PI)}else n.moveTo(A,k.y),n.lineTo(A+k.width,k.y),n.lineTo(A+k.width,b),n.lineTo(A,b),n.lineTo(A,k.y);n.setStrokeStyle(P),n.setFillStyle(_),d.borderWidth>0&&(n.setLineWidth(d.borderWidth*t.pix),n.closePath(),n.stroke()),n.fill()}}break;case"triangle":for(var U=0;U<w.length;U++){var z=w[U];if(null!==z&&U>g&&U<x){A=z.x-u*d.widthRatio/2,T=t.height-z.y-t.area[2];n.beginPath();_=z.color||e[U].color,P=z.color||e[U].color;if("none"!==d.linearType){C=n.createLinearGradient(A,z.y,A,b);"opacity"==d.linearType?(C.addColorStop(0,c(_,d.linearOpacity)),C.addColorStop(1,c(_,1))):(C.addColorStop(0,c(d.customColor[e[U].linearIndex],d.linearOpacity)),C.addColorStop(d.colorStop,c(d.customColor[e[U].linearIndex],d.linearOpacity)),C.addColorStop(1,c(_,1))),_=C}n.moveTo(A,b),n.lineTo(z.x,z.y),n.lineTo(A+z.width,b),n.setStrokeStyle(P),n.setFillStyle(_),d.borderWidth>0&&(n.setLineWidth(d.borderWidth*t.pix),n.stroke()),n.fill()}}break;case"mount":for(var W=0;W<w.length;W++){var j=w[W];if(null!==j&&W>g&&W<x){A=j.x-u*d.widthRatio/2,T=t.height-j.y-t.area[2];n.beginPath();_=j.color||e[W].color,P=j.color||e[W].color;if("none"!==d.linearType){C=n.createLinearGradient(A,j.y,A,b);"opacity"==d.linearType?(C.addColorStop(0,c(_,d.linearOpacity)),C.addColorStop(1,c(_,1))):(C.addColorStop(0,c(d.customColor[e[W].linearIndex],d.linearOpacity)),C.addColorStop(d.colorStop,c(d.customColor[e[W].linearIndex],d.linearOpacity)),C.addColorStop(1,c(_,1))),_=C}n.moveTo(A,b),n.bezierCurveTo(j.x-j.width/4,b,j.x-j.width/4,j.y,j.x,j.y),n.bezierCurveTo(j.x+j.width/4,j.y,j.x+j.width/4,b,A+j.width,b),n.setStrokeStyle(P),n.setFillStyle(_),d.borderWidth>0&&(n.setLineWidth(d.borderWidth*t.pix),n.stroke()),n.fill()}}break;case"sharp":for(var q=0;q<w.length;q++){var G=w[q];if(null!==G&&q>g&&q<x){A=G.x-u*d.widthRatio/2,T=t.height-G.y-t.area[2];n.beginPath();_=G.color||e[q].color,P=G.color||e[q].color;if("none"!==d.linearType){C=n.createLinearGradient(A,G.y,A,b);"opacity"==d.linearType?(C.addColorStop(0,c(_,d.linearOpacity)),C.addColorStop(1,c(_,1))):(C.addColorStop(0,c(d.customColor[e[q].linearIndex],d.linearOpacity)),C.addColorStop(d.colorStop,c(d.customColor[e[q].linearIndex],d.linearOpacity)),C.addColorStop(1,c(_,1))),_=C}n.moveTo(A,b),n.quadraticCurveTo(G.x-0,b-T/4,G.x,G.y),n.quadraticCurveTo(G.x+0,b-T/4,A+G.width,b),n.setStrokeStyle(P),n.setFillStyle(_),d.borderWidth>0&&(n.setLineWidth(d.borderWidth*t.pix),n.stroke()),n.fill()}}break}if(!1!==t.dataLabel&&1===r){var H,J,Y;H=[].concat(t.chartData.yAxisData.ranges[0]),J=H.pop(),Y=H.shift();w=$(e,J,Y,l,u,t,d,b,r);de(w,e,a,n,t,b)}return n.restore(),{xAxisPoints:l,calPoints:w,eachSpacing:u}}(u,t,a,n,e),o=r.xAxisPoints,l=r.calPoints,h=r.eachSpacing;t.chartData.xAxisPoints=o,t.chartData.calPoints=l,t.chartData.eachSpacing=h,Ae(0,t,a,n),!1!==t.enableMarkLine&&1===e&&xe(t,0,n),Te(t.series,t,a,n,t.chartData),we(t,a,n,e),Re(0,n)},onAnimationFinish:function(){o.uevent.trigger("renderComplete")}});break;case"bar":this.animationInstance=new Ne({timing:t.timing,duration:g,onProcess:function(e){n.clearRect(0,0,t.width,t.height),t.rotate&&re(n,t),Se(d,t,a,n);var r=function(e,t,a,n){for(var r=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,o=[],l=(t.height-t.area[0]-t.area[2])/t.categories.length,u=0;u<t.categories.length;u++)o.push(t.area[0]+l/2+l*u);var d=s({},{type:"group",width:l/2,meterBorder:4,meterFillColor:"#FFFFFF",barBorderCircle:!1,barBorderRadius:[],seriesGap:2,linearType:"none",linearOpacity:1,customColor:[],colorStop:0},t.extra.bar),h=[];n.save();var f=-2,p=o.length+2;return t.tooltip&&t.tooltip.textList&&t.tooltip.textList.length&&1===r&&ye(t.tooltip.offset.y,t,0,n,l),d.customColor=v(d.linearType,d.customColor,e,a),e.forEach((function(s,u){var g,x,v;g=[].concat(t.chartData.xAxisData.ranges),v=g.pop(),x=g.shift();var m=s.data;switch(d.type){case"group":var y=ee(m,x,v,o,l,t,a,r),b=ae(m,x,v,o,l,t,a,u,e,r);h.push(b),y=G(y,l,e.length,u,0,t);for(var w=0;w<y.length;w++){var S=y[w];if(null!==S&&w>f&&w<p){var k=t.area[3],A=S.y-S.width/2;S.height;n.beginPath();var T=S.color||s.color,_=S.color||s.color;if("none"!==d.linearType){var P=n.createLinearGradient(k,S.y,S.x,S.y);"opacity"==d.linearType?(P.addColorStop(0,c(T,d.linearOpacity)),P.addColorStop(1,c(T,1))):(P.addColorStop(0,c(d.customColor[s.linearIndex],d.linearOpacity)),P.addColorStop(d.colorStop,c(d.customColor[s.linearIndex],d.linearOpacity)),P.addColorStop(1,c(T,1))),T=P}if(d.barBorderRadius&&4===d.barBorderRadius.length||!0===d.barBorderCircle){var C=k,D=S.width,M=S.y-S.width/2,I=S.height;d.barBorderCircle&&(d.barBorderRadius=[D/2,D/2,0,0]);var L=(0,i.default)(d.barBorderRadius,4),O=L[0],F=L[1],E=L[2],R=L[3],B=Math.min(D/2,I/2);O=O>B?B:O,F=F>B?B:F,E=E>B?B:E,R=R>B?B:R,O=O<0?0:O,F=F<0?0:F,E=E<0?0:E,R=R<0?0:R,n.arc(C+R,M+R,R,-Math.PI,-Math.PI/2),n.arc(S.x-O,M+O,O,-Math.PI/2,0),n.arc(S.x-F,M+D-F,F,0,Math.PI/2),n.arc(C+E,M+D-E,E,Math.PI/2,Math.PI)}else n.moveTo(k,A),n.lineTo(S.x,A),n.lineTo(S.x,A+S.width),n.lineTo(k,A+S.width),n.lineTo(k,A),n.setLineWidth(1),n.setStrokeStyle(_);n.setFillStyle(T),n.closePath(),n.fill()}}break;case"stack":y=ae(m,x,v,o,l,t,a,u,e,r);h.push(y),y=Y(y,l,e.length,0,0,t);for(var N=0;N<y.length;N++){var U=y[N];if(null!==U&&N>f&&N<p){n.beginPath();T=U.color||s.color,k=U.x0;n.setFillStyle(T),n.moveTo(k,U.y-U.width/2),n.fillRect(k,U.y-U.width/2,U.height,U.width),n.closePath(),n.fill()}}break}})),!1!==t.dataLabel&&1===r&&e.forEach((function(i,s){var c,u,h;c=[].concat(t.chartData.xAxisData.ranges),h=c.pop(),u=c.shift();var f=i.data;switch(d.type){case"group":var p=ee(f,u,h,o,l,t,a,r);p=G(p,l,e.length,s,0,t),he(p,i,a,n,t);break;case"stack":p=ae(f,u,h,o,l,t,a,s,e,r);he(p,i,a,n,t);break}})),{yAxisPoints:o,calPoints:h,eachSpacing:l}}(u,t,a,n,e),o=r.yAxisPoints,l=r.calPoints,h=r.eachSpacing;t.chartData.yAxisPoints=o,t.chartData.xAxisPoints=t.chartData.xAxisData.xAxisPoints,t.chartData.calPoints=l,t.chartData.eachSpacing=h,Ae(0,t,a,n),!1!==t.enableMarkLine&&1===e&&xe(t,0,n),Te(t.series,t,a,n,t.chartData),we(t,a,n,e),Re(0,n)},onAnimationFinish:function(){o.uevent.trigger("renderComplete")}});break;case"area":this.animationInstance=new Ne({timing:t.timing,duration:g,onProcess:function(e){n.clearRect(0,0,t.width,t.height),t.rotate&&re(n,t),ke(0,t,0,n),Se(d,t,a,n);var i=function(e,t,a,n){var i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,r=s({},{type:"straight",opacity:.2,addLine:!1,width:2,gradient:!1,activeType:"none"},t.extra.area),o=t.chartData.xAxisData,l=o.xAxisPoints,u=o.eachSpacing,d=t.height-t.area[2],h=[];n.save();var p=0,g=t.width+u;return t._scrollDistance_&&0!==t._scrollDistance_&&!0===t.enableScroll&&(n.translate(t._scrollDistance_,0),p=-t._scrollDistance_-2*u+t.area[3],g=p+(t.xAxis.itemCount+4)*u),e.forEach((function(e,o){var s,x,v;s=[].concat(t.chartData.yAxisData.ranges[e.index]),x=s.pop(),v=s.shift();var m=e.data,y=V(m,x,v,l,u,t,a,i);h.push(y);for(var b=L(y,e),w=0;w<b.length;w++){var S=b[w];if(n.beginPath(),n.setStrokeStyle(c(e.color,r.opacity)),r.gradient){var k=n.createLinearGradient(0,t.area[0],0,t.height-t.area[2]);k.addColorStop("0",c(e.color,r.opacity)),k.addColorStop("1.0",c("#FFFFFF",.1)),n.setFillStyle(k)}else n.setFillStyle(c(e.color,r.opacity));if(n.setLineWidth(r.width*t.pix),S.length>1){var A=S[0],T=S[S.length-1];n.moveTo(A.x,A.y);var _=0;if("curve"===r.type)for(var P=0;P<S.length;P++){var C=S[P];if(0==_&&C.x>p&&(n.moveTo(C.x,C.y),_=1),P>0&&C.x>p&&C.x<g){var D=f(S,P-1);n.bezierCurveTo(D.ctrA.x,D.ctrA.y,D.ctrB.x,D.ctrB.y,C.x,C.y)}}if("straight"===r.type)for(var M=0;M<S.length;M++){var I=S[M];0==_&&I.x>p&&(n.moveTo(I.x,I.y),_=1),M>0&&I.x>p&&I.x<g&&n.lineTo(I.x,I.y)}if("step"===r.type)for(var O=0;O<S.length;O++){var F=S[O];0==_&&F.x>p&&(n.moveTo(F.x,F.y),_=1),O>0&&F.x>p&&F.x<g&&(n.lineTo(F.x,S[O-1].y),n.lineTo(F.x,F.y))}n.lineTo(T.x,d),n.lineTo(A.x,d),n.lineTo(A.x,A.y)}else{var E=S[0];n.moveTo(E.x-u/2,E.y)}if(n.closePath(),n.fill(),r.addLine){if("dash"==e.lineType){var R=e.dashLength?e.dashLength:8;R*=t.pix,n.setLineDash([R,R])}if(n.beginPath(),n.setStrokeStyle(e.color),n.setLineWidth(r.width*t.pix),1===S.length)n.moveTo(S[0].x,S[0].y);else{n.moveTo(S[0].x,S[0].y);var B=0;if("curve"===r.type)for(var N=0;N<S.length;N++){var U=S[N];if(0==B&&U.x>p&&(n.moveTo(U.x,U.y),B=1),N>0&&U.x>p&&U.x<g){var z=f(S,N-1);n.bezierCurveTo(z.ctrA.x,z.ctrA.y,z.ctrB.x,z.ctrB.y,U.x,U.y)}}if("straight"===r.type)for(var W=0;W<S.length;W++){var j=S[W];0==B&&j.x>p&&(n.moveTo(j.x,j.y),B=1),W>0&&j.x>p&&j.x<g&&n.lineTo(j.x,j.y)}if("step"===r.type)for(var q=0;q<S.length;q++){var G=S[q];0==B&&G.x>p&&(n.moveTo(G.x,G.y),B=1),q>0&&G.x>p&&G.x<g&&(n.lineTo(G.x,S[q-1].y),n.lineTo(G.x,G.y))}n.moveTo(S[0].x,S[0].y)}n.stroke(),n.setLineDash([])}}!1!==t.dataPointShape&&oe(y,e.color,e.pointShape,n,t),se(y,e.color,e.pointShape,n,t,r,o)})),!1!==t.dataLabel&&1===i&&e.forEach((function(e,r){var o,s,c;o=[].concat(t.chartData.yAxisData.ranges[e.index]),s=o.pop(),c=o.shift();var d=e.data,h=V(d,s,c,l,u,t,a,i);ce(h,e,a,n,t)})),n.restore(),{xAxisPoints:l,calPoints:h,eachSpacing:u}}(u,t,a,n,e),r=i.xAxisPoints,o=i.calPoints,l=i.eachSpacing;t.chartData.xAxisPoints=r,t.chartData.calPoints=o,t.chartData.eachSpacing=l,Ae(0,t,a,n),!1!==t.enableMarkLine&&1===e&&xe(t,0,n),Te(t.series,t,a,n,t.chartData),we(t,a,n,e),Re(0,n)},onAnimationFinish:function(){o.uevent.trigger("renderComplete")}});break;case"ring":this.animationInstance=new Ne({timing:t.timing,duration:g,onProcess:function(e){n.clearRect(0,0,t.width,t.height),t.rotate&&re(n,t),t.chartData.pieData=_e(u,t,a,n,e),Te(t.series,t,a,n,t.chartData),we(t,a,n,e),Re(0,n)},onAnimationFinish:function(){o.uevent.trigger("renderComplete")}});break;case"pie":this.animationInstance=new Ne({timing:t.timing,duration:g,onProcess:function(e){n.clearRect(0,0,t.width,t.height),t.rotate&&re(n,t),t.chartData.pieData=_e(u,t,a,n,e),Te(t.series,t,a,n,t.chartData),we(t,a,n,e),Re(0,n)},onAnimationFinish:function(){o.uevent.trigger("renderComplete")}});break;case"rose":this.animationInstance=new Ne({timing:t.timing,duration:g,onProcess:function(e){n.clearRect(0,0,t.width,t.height),t.rotate&&re(n,t),t.chartData.pieData=function(e,t,a,n){var i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,r=s({},{type:"area",activeOpacity:.5,activeRadius:10,offsetAngle:0,labelWidth:15,border:!1,borderWidth:2,borderColor:"#FFFFFF",linearType:"none",customColor:[]},t.extra.rose);0==a.pieChartLinePadding&&(a.pieChartLinePadding=r.activeRadius*t.pix);var o={x:t.area[3]+(t.width-t.area[1]-t.area[3])/2,y:t.area[0]+(t.height-t.area[0]-t.area[2])/2},l=Math.min((t.width-t.area[1]-t.area[3])/2-a.pieChartLinePadding-a.pieChartTextPadding-a._pieTextMaxLength_,(t.height-t.area[0]-t.area[2])/2-a.pieChartLinePadding-a.pieChartTextPadding);l=l<10?10:l;var u=r.minRadius||.5*l;l<u&&(l=u+10),e=N(e,r.type,u,l,i);var d=r.activeRadius*t.pix;return r.customColor=v(r.linearType,r.customColor,e,a),e=e.map((function(e){return e._start_+=(r.offsetAngle||0)*Math.PI/180,e})),e.forEach((function(e,a){t.tooltip&&t.tooltip.index==a&&(n.beginPath(),n.setFillStyle(c(e.color,r.activeOpacity||.5)),n.moveTo(o.x,o.y),n.arc(o.x,o.y,d+e._radius_,e._start_,e._start_+2*e._rose_proportion_*Math.PI),n.closePath(),n.fill()),n.beginPath(),n.setLineWidth(r.borderWidth*t.pix),n.lineJoin="round",n.setStrokeStyle(r.borderColor);var i,s=e.color;"custom"==r.linearType&&(i=n.createCircularGradient?n.createCircularGradient(o.x,o.y,e._radius_):n.createRadialGradient(o.x,o.y,0,o.x,o.y,e._radius_),i.addColorStop(0,c(r.customColor[e.linearIndex],1)),i.addColorStop(1,c(e.color,1)),s=i);n.setFillStyle(s),n.moveTo(o.x,o.y),n.arc(o.x,o.y,e._radius_,e._start_,e._start_+2*e._rose_proportion_*Math.PI),n.closePath(),n.fill(),1==r.border&&n.stroke()})),!1!==t.dataLabel&&1===i&&ge(e,t,a,n,0,o),{center:o,radius:l,series:e}}(u,t,a,n,e),Te(t.series,t,a,n,t.chartData),we(t,a,n,e),Re(0,n)},onAnimationFinish:function(){o.uevent.trigger("renderComplete")}});break;case"radar":this.animationInstance=new Ne({timing:t.timing,duration:g,onProcess:function(e){n.clearRect(0,0,t.width,t.height),t.rotate&&re(n,t),t.chartData.radarData=function(e,t,a,n){var i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,r=s({},{gridColor:"#cccccc",gridType:"radar",gridEval:1,axisLabel:!1,axisLabelTofix:0,labelShow:!0,labelColor:"#666666",labelPointShow:!1,labelPointRadius:3,labelPointColor:"#cccccc",opacity:.2,gridCount:3,border:!1,borderWidth:2,linearType:"none",customColor:[]},t.extra.radar),o=T(t.categories.length),l={x:t.area[3]+(t.width-t.area[1]-t.area[3])/2,y:t.area[0]+(t.height-t.area[0]-t.area[2])/2},u=(t.width-t.area[1]-t.area[3])/2,d=(t.height-t.area[0]-t.area[2])/2,h=Math.min(u-(A(t.categories,a.fontSize,n)+a.radarLabelTextMargin),d-a.radarLabelTextMargin);h-=a.radarLabelTextMargin*t.pix,h=h<10?10:h,h=r.radius?r.radius:h,n.beginPath(),n.setLineWidth(1*t.pix),n.setStrokeStyle(r.gridColor),o.forEach((function(e,t){var a=p(h*Math.cos(e),h*Math.sin(e),l);n.moveTo(l.x,l.y),t%r.gridEval==0&&n.lineTo(a.x,a.y)})),n.stroke(),n.closePath();for(var f=function(e){var a={};if(n.beginPath(),n.setLineWidth(1*t.pix),n.setStrokeStyle(r.gridColor),"radar"==r.gridType)o.forEach((function(t,i){var o=p(h/r.gridCount*e*Math.cos(t),h/r.gridCount*e*Math.sin(t),l);0===i?(a=o,n.moveTo(o.x,o.y)):n.lineTo(o.x,o.y)})),n.lineTo(a.x,a.y);else{var i=p(h/r.gridCount*e*Math.cos(1.5),h/r.gridCount*e*Math.sin(1.5),l);n.arc(l.x,l.y,l.y-i.y,0,2*Math.PI,!1)}n.stroke(),n.closePath()},g=1;g<=r.gridCount;g++)f(g);r.customColor=v(r.linearType,r.customColor,e,a);var x=E(o,l,h,e,t,i);if(x.forEach((function(a,i){n.beginPath(),n.setLineWidth(r.borderWidth*t.pix),n.setStrokeStyle(a.color);var o,s=c(a.color,r.opacity);"custom"==r.linearType&&(o=n.createCircularGradient?n.createCircularGradient(l.x,l.y,h):n.createRadialGradient(l.x,l.y,0,l.x,l.y,h),o.addColorStop(0,c(r.customColor[e[i].linearIndex],r.opacity)),o.addColorStop(1,c(a.color,r.opacity)),s=o);if(n.setFillStyle(s),a.data.forEach((function(e,t){0===t?n.moveTo(e.position.x,e.position.y):n.lineTo(e.position.x,e.position.y)})),n.closePath(),n.fill(),!0===r.border&&n.stroke(),n.closePath(),!1!==t.dataPointShape){var u=a.data.map((function(e){return e.position}));oe(u,a.color,a.pointShape,n,t)}})),!0===r.axisLabel){var m=Math.max(r.max,Math.max.apply(null,b(e))),y=h/r.gridCount,w=t.fontSize*t.pix;n.setFontSize(w),n.setFillStyle(t.fontColor),n.setTextAlign("left");for(g=0;g<r.gridCount+1;g++){var S=g*m/r.gridCount;S=S.toFixed(r.axisLabelTofix),n.fillText(String(S),l.x+3*t.pix,l.y-g*y+w/2)}}return pe(o,h,l,t,a,n),!1!==t.dataLabel&&1===i&&(x.forEach((function(e,i){n.beginPath();var r=e.textSize*t.pix||a.fontSize;n.setFontSize(r),n.setFillStyle(e.textColor||t.fontColor),e.data.forEach((function(e,t){Math.abs(e.position.x-l.x)<2?e.position.y<l.y?(n.setTextAlign("center"),n.fillText(e.value,e.position.x,e.position.y-4)):(n.setTextAlign("center"),n.fillText(e.value,e.position.x,e.position.y+r+2)):e.position.x<l.x?(n.setTextAlign("right"),n.fillText(e.value,e.position.x-4,e.position.y+r/2-2)):(n.setTextAlign("left"),n.fillText(e.value,e.position.x+4,e.position.y+r/2-2))})),n.closePath(),n.stroke()})),n.setTextAlign("left")),{center:l,radius:h,angleList:o}}(u,t,a,n,e),Te(t.series,t,a,n,t.chartData),we(t,a,n,e),Re(0,n)},onAnimationFinish:function(){o.uevent.trigger("renderComplete")}});break;case"arcbar":this.animationInstance=new Ne({timing:t.timing,duration:g,onProcess:function(e){n.clearRect(0,0,t.width,t.height),t.rotate&&re(n,t),t.chartData.arcbarData=function(e,t,a,n){var i,r,o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,l=s({},{startAngle:.75,endAngle:.25,type:"default",direction:"cw",lineCap:"round",width:12,gap:2,linearType:"none",customColor:[]},t.extra.arcbar);e=U(e,l,o),i=l.centerX||l.centerY?{x:l.centerX?l.centerX:t.width/2,y:l.centerY?l.centerY:t.height/2}:{x:t.width/2,y:t.height/2},l.radius?r=l.radius:(r=Math.min(i.x,i.y),r-=5*t.pix,r-=l.width/2),r=r<10?10:r,l.customColor=v(l.linearType,l.customColor,e,a);for(var u=0;u<e.length;u++){var d=e[u];n.setLineWidth(l.width*t.pix),n.setStrokeStyle(l.backgroundColor||"#E9E9E9"),n.setLineCap(l.lineCap),n.beginPath(),"default"==l.type?n.arc(i.x,i.y,r-(l.width*t.pix+l.gap*t.pix)*u,l.startAngle*Math.PI,l.endAngle*Math.PI,"ccw"==l.direction):n.arc(i.x,i.y,r-(l.width*t.pix+l.gap*t.pix)*u,0,2*Math.PI,"ccw"==l.direction),n.stroke();var h=d.color;if("custom"==l.linearType){var f=n.createLinearGradient(i.x-r,i.y,i.x+r,i.y);f.addColorStop(1,c(l.customColor[d.linearIndex],1)),f.addColorStop(0,c(d.color,1)),h=f}n.setLineWidth(l.width*t.pix),n.setStrokeStyle(h),n.setLineCap(l.lineCap),n.beginPath(),n.arc(i.x,i.y,r-(l.width*t.pix+l.gap*t.pix)*u,l.startAngle*Math.PI,d._proportion_*Math.PI,"ccw"==l.direction),n.stroke()}return le(t,a,n,i),{center:i,radius:r,series:e}}(u,t,a,n,e),Re(0,n)},onAnimationFinish:function(){o.uevent.trigger("renderComplete")}});break;case"gauge":this.animationInstance=new Ne({timing:t.timing,duration:g,onProcess:function(e){n.clearRect(0,0,t.width,t.height),t.rotate&&re(n,t),t.chartData.gaugeData=function(e,t,a,n,i){var r=arguments.length>5&&void 0!==arguments[5]?arguments[5]:1,o=s({},{type:"default",startAngle:.75,endAngle:.25,width:15,labelOffset:13,splitLine:{fixRadius:0,splitNumber:10,width:15,color:"#FFFFFF",childNumber:5,childWidth:5},pointer:{width:15,color:"auto"}},a.extra.gauge);void 0==o.oldAngle&&(o.oldAngle=o.startAngle),void 0==o.oldData&&(o.oldData=0),e=W(e,o.startAngle,o.endAngle);var l={x:a.width/2,y:a.height/2},u=Math.min(l.x,l.y);u-=5*a.pix,u-=o.width/2,u=u<10?10:u;var d=u-o.width,h=0;if("progress"==o.type){var f=u-3*o.width;i.beginPath();var p=i.createLinearGradient(l.x,l.y-f,l.x,l.y+f);p.addColorStop("0",c(t[0].color,.3)),p.addColorStop("1.0",c("#FFFFFF",.1)),i.setFillStyle(p),i.arc(l.x,l.y,f,0,2*Math.PI,!1),i.fill(),i.setLineWidth(o.width),i.setStrokeStyle(c(t[0].color,.3)),i.setLineCap("round"),i.beginPath(),i.arc(l.x,l.y,d,o.startAngle*Math.PI,o.endAngle*Math.PI,!1),i.stroke(),h=o.endAngle<o.startAngle?2+o.endAngle-o.startAngle:o.startAngle-o.endAngle;o.splitLine.splitNumber;var g=h/o.splitLine.splitNumber/o.splitLine.childNumber,x=-u-.5*o.width-o.splitLine.fixRadius,v=-u-o.width-o.splitLine.fixRadius+o.splitLine.width;i.save(),i.translate(l.x,l.y),i.rotate((o.startAngle-1)*Math.PI);for(var m=o.splitLine.splitNumber*o.splitLine.childNumber+1,y=t[0].data*r,b=0;b<m;b++)i.beginPath(),y>b/m?i.setStrokeStyle(c(t[0].color,1)):i.setStrokeStyle(c(t[0].color,.3)),i.setLineWidth(3*a.pix),i.moveTo(x,0),i.lineTo(v,0),i.stroke(),i.rotate(g*Math.PI);i.restore(),t=z(t,o,r),i.setLineWidth(o.width),i.setStrokeStyle(t[0].color),i.setLineCap("round"),i.beginPath(),i.arc(l.x,l.y,d,o.startAngle*Math.PI,t[0]._proportion_*Math.PI,!1),i.stroke();var w=u-2.5*o.width;i.save(),i.translate(l.x,l.y),i.rotate((t[0]._proportion_-1)*Math.PI),i.beginPath(),i.setLineWidth(o.width/3);var S=i.createLinearGradient(0,.6*-w,0,.6*w);S.addColorStop("0",c("#FFFFFF",0)),S.addColorStop("0.5",c(t[0].color,1)),S.addColorStop("1.0",c("#FFFFFF",0)),i.setStrokeStyle(S),i.arc(0,0,w,.85*Math.PI,1.15*Math.PI,!1),i.stroke(),i.beginPath(),i.setLineWidth(1),i.setStrokeStyle(t[0].color),i.setFillStyle(t[0].color),i.moveTo(-w-o.width/3/2,-4),i.lineTo(-w-o.width/3/2-4,0),i.lineTo(-w-o.width/3/2,4),i.lineTo(-w-o.width/3/2,-4),i.stroke(),i.fill(),i.restore()}else{i.setLineWidth(o.width),i.setLineCap("butt");for(var k=0;k<e.length;k++){var A=e[k];i.beginPath(),i.setStrokeStyle(A.color),i.arc(l.x,l.y,u,A._startAngle_*Math.PI,A._endAngle_*Math.PI,!1),i.stroke()}i.save(),h=o.endAngle<o.startAngle?2+o.endAngle-o.startAngle:o.startAngle-o.endAngle;var T=h/o.splitLine.splitNumber,_=h/o.splitLine.splitNumber/o.splitLine.childNumber,P=-u-.5*o.width-o.splitLine.fixRadius,C=-u-.5*o.width-o.splitLine.fixRadius+o.splitLine.width,D=-u-.5*o.width-o.splitLine.fixRadius+o.splitLine.childWidth;i.translate(l.x,l.y),i.rotate((o.startAngle-1)*Math.PI);for(var M=0;M<o.splitLine.splitNumber+1;M++)i.beginPath(),i.setStrokeStyle(o.splitLine.color),i.setLineWidth(2*a.pix),i.moveTo(P,0),i.lineTo(C,0),i.stroke(),i.rotate(T*Math.PI);i.restore(),i.save(),i.translate(l.x,l.y),i.rotate((o.startAngle-1)*Math.PI);for(var I=0;I<o.splitLine.splitNumber*o.splitLine.childNumber+1;I++)i.beginPath(),i.setStrokeStyle(o.splitLine.color),i.setLineWidth(1*a.pix),i.moveTo(P,0),i.lineTo(D,0),i.stroke(),i.rotate(_*Math.PI);i.restore(),t=j(t,e,o,r);for(var L=0;L<t.length;L++){var O=t[L];i.save(),i.translate(l.x,l.y),i.rotate((O._proportion_-1)*Math.PI),i.beginPath(),i.setFillStyle(O.color),i.moveTo(o.pointer.width,0),i.lineTo(0,-o.pointer.width/2),i.lineTo(-d,0),i.lineTo(0,o.pointer.width/2),i.lineTo(o.pointer.width,0),i.closePath(),i.fill(),i.beginPath(),i.setFillStyle("#FFFFFF"),i.arc(0,0,o.pointer.width/6,0,2*Math.PI,!1),i.fill(),i.restore()}!1!==a.dataLabel&&fe(o,u,l,a,n,i)}return le(a,n,i,l),1===r&&"gauge"===a.type&&(a.extra.gauge.oldAngle=t[0]._proportion_,a.extra.gauge.oldData=t[0].data),{center:l,radius:u,innerRadius:d,categories:e,totalAngle:h}}(d,u,t,a,n,e),Re(0,n)},onAnimationFinish:function(){o.uevent.trigger("renderComplete")}});break;case"candle":this.animationInstance=new Ne({timing:t.timing,duration:g,onProcess:function(e){n.clearRect(0,0,t.width,t.height),t.rotate&&re(n,t),ke(0,t,0,n),Se(d,t,a,n);var i=function(e,t,a,n,i){var r=arguments.length>5&&void 0!==arguments[5]?arguments[5]:1,o=s({},{color:{},average:{}},a.extra.candle);o.color=s({},{upLine:"#f04864",upFill:"#f04864",downLine:"#2fc25b",downFill:"#2fc25b"},o.color),o.average=s({},{show:!1,name:[],day:[],color:n.color},o.average),a.extra.candle=o;var l=a.chartData.xAxisData,c=l.xAxisPoints,u=l.eachSpacing,d=[];i.save();var h=-2,p=c.length+2,g=0,x=a.width+u;return a._scrollDistance_&&0!==a._scrollDistance_&&!0===a.enableScroll&&(i.translate(a._scrollDistance_,0),h=Math.floor(-a._scrollDistance_/u)-2,p=h+a.xAxis.itemCount+4,g=-a._scrollDistance_-2*u+a.area[3],x=g+(a.xAxis.itemCount+4)*u),(o.average.show||t)&&t.forEach((function(e,t){var o,s,l;o=[].concat(a.chartData.yAxisData.ranges[e.index]),s=o.pop(),l=o.shift();for(var d=e.data,h=V(d,s,l,c,u,a,n,r),p=L(h,e),v=0;v<p.length;v++){var m=p[v];if(i.beginPath(),i.setStrokeStyle(e.color),i.setLineWidth(1),1===m.length)i.moveTo(m[0].x,m[0].y),i.arc(m[0].x,m[0].y,1,0,2*Math.PI);else{i.moveTo(m[0].x,m[0].y);for(var y=0,b=0;b<m.length;b++){var w=m[b];if(0==y&&w.x>g&&(i.moveTo(w.x,w.y),y=1),b>0&&w.x>g&&w.x<x){var S=f(m,b-1);i.bezierCurveTo(S.ctrA.x,S.ctrA.y,S.ctrB.x,S.ctrB.y,w.x,w.y)}}i.moveTo(m[0].x,m[0].y)}i.closePath(),i.stroke()}})),e.forEach((function(e,t){var s,l,f;s=[].concat(a.chartData.yAxisData.ranges[e.index]),l=s.pop(),f=s.shift();var g=e.data,x=X(g,l,f,c,u,a,n,r);d.push(x);for(var v=L(x,e),m=0;m<v[0].length;m++)if(m>h&&m<p){var y=v[0][m];i.beginPath(),g[m][1]-g[m][0]>0?(i.setStrokeStyle(o.color.upLine),i.setFillStyle(o.color.upFill),i.setLineWidth(1*a.pix),i.moveTo(y[3].x,y[3].y),i.lineTo(y[1].x,y[1].y),i.lineTo(y[1].x-u/4,y[1].y),i.lineTo(y[0].x-u/4,y[0].y),i.lineTo(y[0].x,y[0].y),i.lineTo(y[2].x,y[2].y),i.lineTo(y[0].x,y[0].y),i.lineTo(y[0].x+u/4,y[0].y),i.lineTo(y[1].x+u/4,y[1].y),i.lineTo(y[1].x,y[1].y),i.moveTo(y[3].x,y[3].y)):(i.setStrokeStyle(o.color.downLine),i.setFillStyle(o.color.downFill),i.setLineWidth(1*a.pix),i.moveTo(y[3].x,y[3].y),i.lineTo(y[0].x,y[0].y),i.lineTo(y[0].x-u/4,y[0].y),i.lineTo(y[1].x-u/4,y[1].y),i.lineTo(y[1].x,y[1].y),i.lineTo(y[2].x,y[2].y),i.lineTo(y[1].x,y[1].y),i.lineTo(y[1].x+u/4,y[1].y),i.lineTo(y[0].x+u/4,y[0].y),i.lineTo(y[0].x,y[0].y),i.moveTo(y[3].x,y[3].y)),i.closePath(),i.fill(),i.stroke()}})),i.restore(),{xAxisPoints:c,calPoints:d,eachSpacing:u}}(u,m,t,a,n,e),r=i.xAxisPoints,o=i.calPoints,l=i.eachSpacing;t.chartData.xAxisPoints=r,t.chartData.calPoints=o,t.chartData.eachSpacing=l,Ae(0,t,a,n),!1!==t.enableMarkLine&&1===e&&xe(t,0,n),Te(m?0:t.series,t,a,n,t.chartData),we(t,a,n,e),Re(0,n)},onAnimationFinish:function(){o.uevent.trigger("renderComplete")}});break}}function ze(){this.events={}}Ne.prototype.stop=function(){this.isStop=!0},ze.prototype.addEventListener=function(e,t){this.events[e]=this.events[e]||[],this.events[e].push(t)},ze.prototype.delEventListener=function(e){this.events[e]=[]},ze.prototype.trigger=function(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];var n=t[0],i=t.slice(1);this.events[n]&&this.events[n].forEach((function(e){try{e.apply(null,i)}catch(t){}}))};var We=function(e){e.pix=e.pixelRatio?e.pixelRatio:1,e.fontSize=e.fontSize?e.fontSize:13,e.fontColor=e.fontColor?e.fontColor:o.fontColor,""!=e.background&&"none"!=e.background||(e.background="#FFFFFF"),e.title=s({},e.title),e.subtitle=s({},e.subtitle),e.duration=e.duration?e.duration:1e3,e.yAxis=s({},{data:[],showTitle:!1,disabled:!1,disableGrid:!1,gridSet:"number",splitNumber:5,gridType:"solid",dashLength:4*e.pix,gridColor:"#cccccc",padding:10,fontColor:"#666666"},e.yAxis),e.xAxis=s({},{rotateLabel:!1,rotateAngle:45,disabled:!1,disableGrid:!1,splitNumber:5,calibration:!1,fontColor:"#666666",fontSize:13,lineHeight:20,marginTop:0,gridType:"solid",dashLength:4,scrollAlign:"left",boundaryGap:"center",axisLine:!0,axisLineColor:"#cccccc",titleFontSize:13,titleOffsetY:0,titleOffsetX:0,titleFontColor:"#666666"},e.xAxis),e.xAxis.scrollPosition=e.xAxis.scrollAlign,e.legend=s({},{show:!0,position:"bottom",float:"center",backgroundColor:"rgba(0,0,0,0)",borderColor:"rgba(0,0,0,0)",borderWidth:0,padding:5,margin:5,itemGap:10,fontSize:e.fontSize,lineHeight:e.fontSize,fontColor:e.fontColor,formatter:{},hiddenColor:"#CECECE"},e.legend),e.extra=s({tooltip:{legendShape:"auto"}},e.extra),e.rotate=!!e.rotate,e.animation=!!e.animation,e.rotate=!!e.rotate,e.canvas2d=!!e.canvas2d;var t=s({},o);if(t.color=e.color?e.color:t.color,"pie"==e.type&&(t.pieChartLinePadding=!1===e.dataLabel?0:e.extra.pie.labelWidth*e.pix||t.pieChartLinePadding*e.pix),"ring"==e.type&&(t.pieChartLinePadding=!1===e.dataLabel?0:e.extra.ring.labelWidth*e.pix||t.pieChartLinePadding*e.pix),"rose"==e.type&&(t.pieChartLinePadding=!1===e.dataLabel?0:e.extra.rose.labelWidth*e.pix||t.pieChartLinePadding*e.pix),t.pieChartTextPadding=!1===e.dataLabel?0:t.pieChartTextPadding*e.pix,t.rotate=e.rotate,e.rotate){var a=e.width,n=e.height;e.width=n,e.height=a}if(e.padding=e.padding?e.padding:t.padding,t.yAxisWidth=o.yAxisWidth*e.pix,t.fontSize=e.fontSize*e.pix,t.titleFontSize=o.titleFontSize*e.pix,t.subtitleFontSize=o.subtitleFontSize*e.pix,!e.context)throw new Error("[uCharts] 未获取到context！注意：v2.0版本后，需要自行获取canvas的绘图上下文并传入opts.context！");this.context=e.context,this.context.setTextAlign||(this.context.setStrokeStyle=function(e){return this.strokeStyle=e},this.context.setLineWidth=function(e){return this.lineWidth=e},this.context.setLineCap=function(e){return this.lineCap=e},this.context.setFontSize=function(e){return this.font=e+"px sans-serif"},this.context.setFillStyle=function(e){return this.fillStyle=e},this.context.setTextAlign=function(e){return this.textAlign=e},this.context.setTextBaseline=function(e){return this.textBaseline=e},this.context.setShadow=function(e,t,a,n){this.shadowColor=n,this.shadowOffsetX=e,this.shadowOffsetY=t,this.shadowBlur=a},this.context.draw=function(){}),this.context.setLineDash||(this.context.setLineDash=function(e){}),e.chartData={},this.uevent=new ze,this.scrollOption={currentOffset:0,startTouchX:0,distance:0,lastMoveTime:0},this.opts=e,this.config=t,Ue.call(this,e.type,e,t,this.context)};We.prototype.updateData=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.opts=s({},this.opts,e),this.opts.updateData=!0;var t=e.scrollPosition||"current";switch(t){case"current":this.opts._scrollDistance_=this.scrollOption.currentOffset;break;case"left":this.opts._scrollDistance_=0,this.scrollOption={currentOffset:0,startTouchX:0,distance:0,lastMoveTime:0};break;case"right":var a=ie(this.opts.series,this.opts,this.config,this.context),n=a.yAxisWidth;this.config.yAxisWidth=n;var i=0,r=K(this.opts.categories,this.opts,this.config),o=r.xAxisPoints,l=r.startX,c=r.endX,u=r.eachSpacing,d=u*(o.length-1),h=c-l;i=h-d,this.scrollOption={currentOffset:i,startTouchX:i,distance:0,lastMoveTime:0},this.opts._scrollDistance_=i;break}Ue.call(this,this.opts.type,this.opts,this.config,this.context)},We.prototype.zoom=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.opts.xAxis.itemCount;if(!0===this.opts.enableScroll){var t=Math.round(Math.abs(this.scrollOption.currentOffset)/this.opts.chartData.eachSpacing)+Math.round(this.opts.xAxis.itemCount/2);this.opts.animation=!1,this.opts.xAxis.itemCount=e.itemCount;var a=ie(this.opts.series,this.opts,this.config,this.context),n=a.yAxisWidth;this.config.yAxisWidth=n;var i=0,r=K(this.opts.categories,this.opts,this.config),o=r.xAxisPoints,s=r.startX,l=r.endX,c=r.eachSpacing,u=c*t,h=l-s,f=h-c*(o.length-1);i=h/2-u,i>0&&(i=0),i<f&&(i=f),this.scrollOption={currentOffset:i,startTouchX:0,distance:0,lastMoveTime:0},d(this,i,this.opts.chartData,this.config,this.opts),this.opts._scrollDistance_=i,Ue.call(this,this.opts.type,this.opts,this.config,this.context)}else console.log("[uCharts] 请启用滚动条后使用")},We.prototype.dobuleZoom=function(e){if(!0===this.opts.enableScroll){var t=e.changedTouches;if(!(t.length<2)){for(var a=0;a<t.length;a++)t[a].x=t[a].x?t[a].x:t[a].clientX,t[a].y=t[a].y?t[a].y:t[a].clientY;var n=[S(t[0],this.opts,e),S(t[1],this.opts,e)],i=Math.abs(n[0].x-n[1].x);if(!this.scrollOption.moveCount){var r={changedTouches:[{x:t[0].x,y:this.opts.area[0]/this.opts.pix+2}]},o={changedTouches:[{x:t[1].x,y:this.opts.area[0]/this.opts.pix+2}]};this.opts.rotate&&(r={changedTouches:[{x:this.opts.height/this.opts.pix-this.opts.area[0]/this.opts.pix-2,y:t[0].y}]},o={changedTouches:[{x:this.opts.height/this.opts.pix-this.opts.area[0]/this.opts.pix-2,y:t[1].y}]});var s=this.getCurrentDataIndex(r).index,l=this.getCurrentDataIndex(o).index,c=Math.abs(s-l);return this.scrollOption.moveCount=c,this.scrollOption.moveCurrent1=Math.min(s,l),void(this.scrollOption.moveCurrent2=Math.max(s,l))}var u=i/this.scrollOption.moveCount,h=(this.opts.width-this.opts.area[1]-this.opts.area[3])/u;h=h<=2?2:h,h=h>=this.opts.categories.length?this.opts.categories.length:h,this.opts.animation=!1,this.opts.xAxis.itemCount=h;var f=0,p=K(this.opts.categories,this.opts,this.config),g=p.xAxisPoints,x=p.startX,v=p.endX,m=p.eachSpacing,y=m*this.scrollOption.moveCurrent1,b=v-x,w=b-m*(g.length-1);f=-y+Math.min(n[0].x,n[1].x)-this.opts.area[3]-m,f>0&&(f=0),f<w&&(f=w),this.scrollOption.currentOffset=f,this.scrollOption.startTouchX=0,this.scrollOption.distance=0,d(this,f,this.opts.chartData,this.config,this.opts),this.opts._scrollDistance_=f,Ue.call(this,this.opts.type,this.opts,this.config,this.context)}}else console.log("[uCharts] 请启用滚动条后使用")},We.prototype.stopAnimation=function(){this.animationInstance&&this.animationInstance.stop()},We.prototype.addEventListener=function(e,t){this.uevent.addEventListener(e,t)},We.prototype.delEventListener=function(e){this.uevent.delEventListener(e)},We.prototype.getCurrentDataIndex=function(e){var t=null;if(t=e.changedTouches?e.changedTouches[0]:e.mp.changedTouches[0],t){var a=S(t,this.opts,e);return"pie"===this.opts.type||"ring"===this.opts.type?function(e,t,a){var n=-1,i=R(t.series);if(t&&t.center&&I(e,t.center,t.radius)){var r=Math.atan2(t.center.y-e.y,e.x-t.center.x);r=-r,a.extra.pie&&a.extra.pie.offsetAngle&&(r-=a.extra.pie.offsetAngle*Math.PI/180),a.extra.ring&&a.extra.ring.offsetAngle&&(r-=a.extra.ring.offsetAngle*Math.PI/180);for(var o=0,s=i.length;o<s;o++)if(h(r,i[o]._start_,i[o]._start_+2*i[o]._proportion_*Math.PI)){n=o;break}}return n}({x:a.x,y:a.y},this.opts.chartData.pieData,this.opts):"rose"===this.opts.type?function(e,t,a){var n=-1,i=N(a._series_,a.extra.rose.type,t.radius,t.radius);if(t&&t.center&&I(e,t.center,t.radius)){var r=Math.atan2(t.center.y-e.y,e.x-t.center.x);r=-r,a.extra.rose&&a.extra.rose.offsetAngle&&(r-=a.extra.rose.offsetAngle*Math.PI/180);for(var o=0,s=i.length;o<s;o++)if(h(r,i[o]._start_,i[o]._start_+2*i[o]._rose_proportion_*Math.PI)){n=o;break}}return n}({x:a.x,y:a.y},this.opts.chartData.pieData,this.opts):"radar"===this.opts.type?function(e,t,a){var n=2*Math.PI/a,i=-1;if(I(e,t.center,t.radius)){var r=function(e){return e<0&&(e+=2*Math.PI),e>2*Math.PI&&(e-=2*Math.PI),e},o=Math.atan2(t.center.y-e.y,e.x-t.center.x);o*=-1,o<0&&(o+=2*Math.PI);var s=t.angleList.map((function(e){return e=r(-1*e),e}));s.forEach((function(e,t){var a=r(e-n/2),s=r(e+n/2);s<a&&(s+=2*Math.PI),(o>=a&&o<=s||o+2*Math.PI>=a&&o+2*Math.PI<=s)&&(i=t)}))}return i}({x:a.x,y:a.y},this.opts.chartData.radarData,this.opts.categories.length):"funnel"===this.opts.type?function(e,t){for(var a=-1,n=0,i=t.series.length;n<i;n++){var r=t.series[n];if(e.x>r.funnelArea[0]&&e.x<r.funnelArea[2]&&e.y>r.funnelArea[1]&&e.y<r.funnelArea[3]){a=n;break}}return a}({x:a.x,y:a.y},this.opts.chartData.funnelData):"map"===this.opts.type?function(e,t){for(var a=-1,n=t.chartData.mapData,i=t.series,r=function(e,t,a,n,i,r){return{x:(t-i)/n+a.xMin,y:a.yMax-(e-r)/n}}(e.y,e.x,n.bounds,n.scale,n.xoffset,n.yoffset),o=[r.x,r.y],s=0,l=i.length;s<l;s++){var c=i[s].geometry.coordinates;if(Me(o,c,t.chartData.mapData.mercator)){a=s;break}}return a}({x:a.x,y:a.y},this.opts):"word"===this.opts.type?function(e,t){for(var a=-1,n=0,i=t.length;n<i;n++){var r=t[n];if(e.x>r.area[0]&&e.x<r.area[2]&&e.y>r.area[1]&&e.y<r.area[3]){a=n;break}}return a}({x:a.x,y:a.y},this.opts.chartData.wordCloudData):"bar"===this.opts.type?function(e,t,a,n){var i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0,r={index:-1,group:[]},o=a.chartData.eachSpacing/2,s=a.chartData.yAxisPoints;return t&&t.length>0&&M(e,a,n)&&s.forEach((function(t,a){e.y+i+o>t&&(r.index=a)})),r}({x:a.x,y:a.y},this.opts.chartData.calPoints,this.opts,this.config,Math.abs(this.scrollOption.currentOffset)):function(e,t,a,n){var i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0,r={index:-1,group:[]},o=a.chartData.eachSpacing/2,s=[];if(t&&t.length>0){if(a.categories){for(var l=1;l<a.chartData.xAxisPoints.length;l++)s.push(a.chartData.xAxisPoints[l]-o);"line"!=a.type&&"area"!=a.type||"justify"!=a.xAxis.boundaryGap||(s=a.chartData.xAxisPoints)}else o=0;if(M(e,a,n))if(a.categories)s.forEach((function(t,a){e.x+i+o>t&&(r.index=a)}));else{for(var c=Array(t.length),u=0;u<t.length;u++){c[u]=Array(t[u].length);for(var d=0;d<t[u].length;d++)c[u][d]=Math.abs(t[u][d].x-e.x)}for(var h=Array(c.length),f=Array(c.length),p=0;p<c.length;p++)h[p]=Math.min.apply(null,c[p]),f[p]=c[p].indexOf(h[p]);var g=Math.min.apply(null,h);r.index=[];for(var x=0;x<h.length;x++)h[x]==g&&(r.group.push(x),r.index.push(f[x]))}}return r}({x:a.x,y:a.y},this.opts.chartData.calPoints,this.opts,this.config,Math.abs(this.scrollOption.currentOffset))}return-1},We.prototype.getLegendDataIndex=function(e){var t=null;if(t=e.changedTouches?e.changedTouches[0]:e.mp.changedTouches[0],t){var a=S(t,this.opts,e);return function(e,t,a){var n=-1;if(function(e,t){return e.x>t.start.x&&e.x<t.end.x&&e.y>t.start.y&&e.y<t.end.y}(e,t.area)){for(var i=t.points,r=-1,o=0,s=i.length;o<s;o++)for(var l=i[o],c=0;c<l.length;c++){r+=1;var u=l[c]["area"];if(u&&e.x>u[0]-0&&e.x<u[2]+0&&e.y>u[1]-0&&e.y<u[3]+0){n=r;break}}return n}return n}({x:a.x,y:a.y},this.opts.chartData.legendData)}return-1},We.prototype.touchLegend=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},a=null;if(a=e.changedTouches?e.changedTouches[0]:e.mp.changedTouches[0],a){S(a,this.opts,e);var n=this.getLegendDataIndex(e);n>=0&&("candle"==this.opts.type?this.opts.seriesMA[n].show=!this.opts.seriesMA[n].show:this.opts.series[n].show=!this.opts.series[n].show,this.opts.animation=!!t.animation,this.opts._scrollDistance_=this.scrollOption.currentOffset,Ue.call(this,this.opts.type,this.opts,this.config,this.context))}},We.prototype.showToolTip=function(e){var t=this,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=null;n=e.changedTouches?e.changedTouches[0]:e.mp.changedTouches[0],n||console.log("[uCharts] 未获取到event坐标信息");var i=S(n,this.opts,e),r=this.scrollOption.currentOffset,o=s({},this.opts,{_scrollDistance_:r,animation:!1});if("line"===this.opts.type||"area"===this.opts.type||"column"===this.opts.type||"scatter"===this.opts.type||"bubble"===this.opts.type){var l=this.getCurrentDataIndex(e),c=void 0==a.index?l.index:a.index;if(c>-1||c.length>0){var u=k(this.opts.series,c,l.group);if(0!==u.length){var d=_(u,this.opts,c,l.group,this.opts.categories,a),h=d.textList,f=d.offset;f.y=i.y,o.tooltip={textList:void 0!==a.textList?a.textList:h,offset:void 0!==a.offset?a.offset:f,option:a,index:c,group:l.group}}}Ue.call(this,o.type,o,this.config,this.context)}if("mount"===this.opts.type){c=void 0==a.index?this.getCurrentDataIndex(e).index:a.index;if(c>-1){o=s({},this.opts,{animation:!1}),u=s({},o._series_[c]),h=[{text:a.formatter?a.formatter(u,void 0,c,o):u.name+": "+u.data,color:u.color,legendShape:"auto"==this.opts.extra.tooltip.legendShape?u.legendShape:this.opts.extra.tooltip.legendShape}],f={x:o.chartData.calPoints[c].x,y:i.y};o.tooltip={textList:a.textList?a.textList:h,offset:void 0!==a.offset?a.offset:f,option:a,index:c}}Ue.call(this,o.type,o,this.config,this.context)}if("bar"===this.opts.type){l=this.getCurrentDataIndex(e),c=void 0==a.index?l.index:a.index;if(c>-1||c.length>0){u=k(this.opts.series,c,l.group);if(0!==u.length){d=_(u,this.opts,c,l.group,this.opts.categories,a),h=d.textList,f=d.offset;f.x=i.x,o.tooltip={textList:void 0!==a.textList?a.textList:h,offset:void 0!==a.offset?a.offset:f,option:a,index:c}}}Ue.call(this,o.type,o,this.config,this.context)}if("mix"===this.opts.type){l=this.getCurrentDataIndex(e),c=void 0==a.index?l.index:a.index;if(c>-1){r=this.scrollOption.currentOffset,o=s({},this.opts,{_scrollDistance_:r,animation:!1}),u=k(this.opts.series,c);if(0!==u.length){var p=P(u,this.opts,c,this.opts.categories,a);h=p.textList,f=p.offset;f.y=i.y,o.tooltip={textList:a.textList?a.textList:h,offset:void 0!==a.offset?a.offset:f,option:a,index:c}}}Ue.call(this,o.type,o,this.config,this.context)}if("candle"===this.opts.type){l=this.getCurrentDataIndex(e),c=void 0==a.index?l.index:a.index;if(c>-1){r=this.scrollOption.currentOffset,o=s({},this.opts,{_scrollDistance_:r,animation:!1}),u=k(this.opts.series,c);if(0!==u.length){d=C(this.opts.series[0].data,u,this.opts,c,this.opts.categories,this.opts.extra.candle,a),h=d.textList,f=d.offset;f.y=i.y,o.tooltip={textList:a.textList?a.textList:h,offset:void 0!==a.offset?a.offset:f,option:a,index:c}}}Ue.call(this,o.type,o,this.config,this.context)}if("pie"===this.opts.type||"ring"===this.opts.type||"rose"===this.opts.type||"funnel"===this.opts.type){c=void 0==a.index?this.getCurrentDataIndex(e):a.index;if(c>-1){o=s({},this.opts,{animation:!1}),u=s({},o._series_[c]),h=[{text:a.formatter?a.formatter(u,void 0,c,o):u.name+": "+u.data,color:u.color,legendShape:"auto"==this.opts.extra.tooltip.legendShape?u.legendShape:this.opts.extra.tooltip.legendShape}],f={x:i.x,y:i.y};o.tooltip={textList:a.textList?a.textList:h,offset:void 0!==a.offset?a.offset:f,option:a,index:c}}Ue.call(this,o.type,o,this.config,this.context)}if("map"===this.opts.type){c=void 0==a.index?this.getCurrentDataIndex(e):a.index;if(c>-1){o=s({},this.opts,{animation:!1}),u=s({},this.opts.series[c]);u.name=u.properties.name;h=[{text:a.formatter?a.formatter(u,void 0,c,this.opts):u.name,color:u.color,legendShape:"auto"==this.opts.extra.tooltip.legendShape?u.legendShape:this.opts.extra.tooltip.legendShape}],f={x:i.x,y:i.y};o.tooltip={textList:a.textList?a.textList:h,offset:void 0!==a.offset?a.offset:f,option:a,index:c}}o.updateData=!1,Ue.call(this,o.type,o,this.config,this.context)}if("word"===this.opts.type){c=void 0==a.index?this.getCurrentDataIndex(e):a.index;if(c>-1){o=s({},this.opts,{animation:!1}),u=s({},this.opts.series[c]),h=[{text:a.formatter?a.formatter(u,void 0,c,this.opts):u.name,color:u.color,legendShape:"auto"==this.opts.extra.tooltip.legendShape?u.legendShape:this.opts.extra.tooltip.legendShape}],f={x:i.x,y:i.y};o.tooltip={textList:a.textList?a.textList:h,offset:void 0!==a.offset?a.offset:f,option:a,index:c}}o.updateData=!1,Ue.call(this,o.type,o,this.config,this.context)}if("radar"===this.opts.type){c=void 0==a.index?this.getCurrentDataIndex(e):a.index;if(c>-1){o=s({},this.opts,{animation:!1}),u=k(this.opts.series,c);if(0!==u.length){h=u.map((function(e){return{text:a.formatter?a.formatter(e,t.opts.categories[c],c,t.opts):e.name+": "+e.data,color:e.color,legendShape:"auto"==t.opts.extra.tooltip.legendShape?e.legendShape:t.opts.extra.tooltip.legendShape}})),f={x:i.x,y:i.y};o.tooltip={textList:a.textList?a.textList:h,offset:void 0!==a.offset?a.offset:f,option:a,index:c}}}Ue.call(this,o.type,o,this.config,this.context)}},We.prototype.translate=function(e){this.scrollOption={currentOffset:e,startTouchX:e,distance:0,lastMoveTime:0};var t=s({},this.opts,{_scrollDistance_:e,animation:!1});Ue.call(this,this.opts.type,t,this.config,this.context)},We.prototype.scrollStart=function(e){var t=null;t=e.changedTouches?e.changedTouches[0]:e.mp.changedTouches[0];var a=S(t,this.opts,e);t&&!0===this.opts.enableScroll&&(this.scrollOption.startTouchX=a.x)},We.prototype.scroll=function(e){0===this.scrollOption.lastMoveTime&&(this.scrollOption.lastMoveTime=Date.now());var t=this.opts.touchMoveLimit||60,a=Date.now(),n=a-this.scrollOption.lastMoveTime;if(!(n<Math.floor(1e3/t))&&0!=this.scrollOption.startTouchX){this.scrollOption.lastMoveTime=a;var i=null;if(i=e.changedTouches?e.changedTouches[0]:e.mp.changedTouches[0],i&&!0===this.opts.enableScroll){var r,o=S(i,this.opts,e);r=o.x-this.scrollOption.startTouchX;var l=this.scrollOption.currentOffset,c=d(this,l+r,this.opts.chartData,this.config,this.opts);this.scrollOption.distance=r=c-l;var u=s({},this.opts,{_scrollDistance_:l+r,animation:!1});return this.opts=u,Ue.call(this,u.type,u,this.config,this.context),l+r}}},We.prototype.scrollEnd=function(e){if(!0===this.opts.enableScroll){var t=this.scrollOption,a=t.currentOffset,n=t.distance;this.scrollOption.currentOffset=a+n,this.scrollOption.distance=0,this.scrollOption.moveCount=0}};var je=We;t.default=je},"20f3":function(e,t,a){"use strict";var n=a("8bdb"),i=a("5145");n({target:"Array",proto:!0,forced:i!==[].lastIndexOf},{lastIndexOf:i})},"24cd":function(e,t,a){"use strict";a.r(t);var n=a("8e20"),i=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=i.a},2624:function(e,t,a){var n=a("c86c");t=n(!1),t.push([e.i,".container[data-v-6e5d9c90]{width:30px;height:30px;position:relative}.container.loading6[data-v-6e5d9c90]{-webkit-animation:rotation 1s infinite;animation:rotation 1s infinite}.container.loading6 .shape[data-v-6e5d9c90]{width:12px;height:12px;border-radius:2px}.container .shape[data-v-6e5d9c90]{position:absolute;width:10px;height:10px;border-radius:1px}.container .shape.shape1[data-v-6e5d9c90]{left:0;background-color:#1890ff}.container .shape.shape2[data-v-6e5d9c90]{right:0;background-color:#91cb74}.container .shape.shape3[data-v-6e5d9c90]{bottom:0;background-color:#fac858}.container .shape.shape4[data-v-6e5d9c90]{bottom:0;right:0;background-color:#e66}.loading6 .shape1[data-v-6e5d9c90]{-webkit-animation:animation6shape1-data-v-6e5d9c90 2s linear 0s infinite normal;animation:animation6shape1-data-v-6e5d9c90 2s linear 0s infinite normal}@-webkit-keyframes animation6shape1-data-v-6e5d9c90{0%{-webkit-transform:translate(0);transform:translate(0)}25%{-webkit-transform:translateY(18px);transform:translateY(18px)}50%{-webkit-transform:translate(18px,18px);transform:translate(18px,18px)}75%{-webkit-transform:translate(18px);transform:translate(18px)}}@keyframes animation6shape1-data-v-6e5d9c90{0%{-webkit-transform:translate(0);transform:translate(0)}25%{-webkit-transform:translateY(18px);transform:translateY(18px)}50%{-webkit-transform:translate(18px,18px);transform:translate(18px,18px)}75%{-webkit-transform:translate(18px);transform:translate(18px)}}.loading6 .shape2[data-v-6e5d9c90]{-webkit-animation:animation6shape2-data-v-6e5d9c90 2s linear 0s infinite normal;animation:animation6shape2-data-v-6e5d9c90 2s linear 0s infinite normal}@-webkit-keyframes animation6shape2-data-v-6e5d9c90{0%{-webkit-transform:translate(0);transform:translate(0)}25%{-webkit-transform:translate(-18px);transform:translate(-18px)}50%{-webkit-transform:translate(-18px,18px);transform:translate(-18px,18px)}75%{-webkit-transform:translateY(18px);transform:translateY(18px)}}@keyframes animation6shape2-data-v-6e5d9c90{0%{-webkit-transform:translate(0);transform:translate(0)}25%{-webkit-transform:translate(-18px);transform:translate(-18px)}50%{-webkit-transform:translate(-18px,18px);transform:translate(-18px,18px)}75%{-webkit-transform:translateY(18px);transform:translateY(18px)}}.loading6 .shape3[data-v-6e5d9c90]{-webkit-animation:animation6shape3-data-v-6e5d9c90 2s linear 0s infinite normal;animation:animation6shape3-data-v-6e5d9c90 2s linear 0s infinite normal}@-webkit-keyframes animation6shape3-data-v-6e5d9c90{0%{-webkit-transform:translate(0);transform:translate(0)}25%{-webkit-transform:translate(18px);transform:translate(18px)}50%{-webkit-transform:translate(18px,-18px);transform:translate(18px,-18px)}75%{-webkit-transform:translateY(-18px);transform:translateY(-18px)}}@keyframes animation6shape3-data-v-6e5d9c90{0%{-webkit-transform:translate(0);transform:translate(0)}25%{-webkit-transform:translate(18px);transform:translate(18px)}50%{-webkit-transform:translate(18px,-18px);transform:translate(18px,-18px)}75%{-webkit-transform:translateY(-18px);transform:translateY(-18px)}}.loading6 .shape4[data-v-6e5d9c90]{-webkit-animation:animation6shape4-data-v-6e5d9c90 2s linear 0s infinite normal;animation:animation6shape4-data-v-6e5d9c90 2s linear 0s infinite normal}@-webkit-keyframes animation6shape4-data-v-6e5d9c90{0%{-webkit-transform:translate(0);transform:translate(0)}25%{-webkit-transform:translateY(-18px);transform:translateY(-18px)}50%{-webkit-transform:translate(-18px,-18px);transform:translate(-18px,-18px)}75%{-webkit-transform:translate(-18px);transform:translate(-18px)}}@keyframes animation6shape4-data-v-6e5d9c90{0%{-webkit-transform:translate(0);transform:translate(0)}25%{-webkit-transform:translateY(-18px);transform:translateY(-18px)}50%{-webkit-transform:translate(-18px,-18px);transform:translate(-18px,-18px)}75%{-webkit-transform:translate(-18px);transform:translate(-18px)}}",""]),e.exports=t},"2bfa":function(e,t,a){"use strict";var n=a("b0e4"),i=a.n(n);i.a},"2cc9":function(e,t,a){"use strict";a.r(t);var n=a("4c75"),i=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=i.a},"2e28":function(e,t,a){"use strict";a.r(t);var n=a("4d04"),i=a("d7d4");for(var r in i)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(r);a("cc63");var o=a("828b"),s=Object(o["a"])(i["default"],n["b"],n["c"],!1,null,"fe0925d0",null,!1,n["a"],void 0);t["default"]=s.exports},"30d0":function(e,t,a){"use strict";var n=a("512c"),i=a.n(n);i.a},3472:function(e,t,a){var n=a("c86c");t=n(!1),t.push([e.i,'.chartsview[data-v-06f9c5d4]{width:100%;height:100%;display:flex;flex-direction:column;flex:1;justify-content:center;align-items:center}.charts-font[data-v-06f9c5d4]{font-size:14px;color:#ccc;margin-top:10px}.charts-error[data-v-06f9c5d4]{width:128px;height:128px;background:url("data:image/png;base64,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");background-position:50%}',""]),e.exports=t},"37fa":function(e,t,a){var n=a("c86c");t=n(!1),t.push([e.i,".container[data-v-4e1c1bfa]{width:30px;height:30px;position:relative}.container.loading3[data-v-4e1c1bfa]{-webkit-animation:rotation 1s infinite;animation:rotation 1s infinite}.container.loading3 .shape1[data-v-4e1c1bfa]{border-top-left-radius:10px}.container.loading3 .shape2[data-v-4e1c1bfa]{border-top-right-radius:10px}.container.loading3 .shape3[data-v-4e1c1bfa]{border-bottom-left-radius:10px}.container.loading3 .shape4[data-v-4e1c1bfa]{border-bottom-right-radius:10px}.container .shape[data-v-4e1c1bfa]{position:absolute;width:10px;height:10px;border-radius:1px}.container .shape.shape1[data-v-4e1c1bfa]{left:0;background-color:#1890ff}.container .shape.shape2[data-v-4e1c1bfa]{right:0;background-color:#91cb74}.container .shape.shape3[data-v-4e1c1bfa]{bottom:0;background-color:#fac858}.container .shape.shape4[data-v-4e1c1bfa]{bottom:0;right:0;background-color:#e66}.loading3 .shape1[data-v-4e1c1bfa]{-webkit-animation:animation3shape1-data-v-4e1c1bfa .5s ease 0s infinite alternate;animation:animation3shape1-data-v-4e1c1bfa .5s ease 0s infinite alternate}@-webkit-keyframes animation3shape1-data-v-4e1c1bfa{from{-webkit-transform:translate(0);transform:translate(0)}to{-webkit-transform:translate(5px,5px);transform:translate(5px,5px)}}@keyframes animation3shape1-data-v-4e1c1bfa{from{-webkit-transform:translate(0);transform:translate(0)}to{-webkit-transform:translate(5px,5px);transform:translate(5px,5px)}}.loading3 .shape2[data-v-4e1c1bfa]{-webkit-animation:animation3shape2-data-v-4e1c1bfa .5s ease 0s infinite alternate;animation:animation3shape2-data-v-4e1c1bfa .5s ease 0s infinite alternate}@-webkit-keyframes animation3shape2-data-v-4e1c1bfa{from{-webkit-transform:translate(0);transform:translate(0)}to{-webkit-transform:translate(-5px,5px);transform:translate(-5px,5px)}}@keyframes animation3shape2-data-v-4e1c1bfa{from{-webkit-transform:translate(0);transform:translate(0)}to{-webkit-transform:translate(-5px,5px);transform:translate(-5px,5px)}}.loading3 .shape3[data-v-4e1c1bfa]{-webkit-animation:animation3shape3-data-v-4e1c1bfa .5s ease 0s infinite alternate;animation:animation3shape3-data-v-4e1c1bfa .5s ease 0s infinite alternate}@-webkit-keyframes animation3shape3-data-v-4e1c1bfa{from{-webkit-transform:translate(0);transform:translate(0)}to{-webkit-transform:translate(5px,-5px);transform:translate(5px,-5px)}}@keyframes animation3shape3-data-v-4e1c1bfa{from{-webkit-transform:translate(0);transform:translate(0)}to{-webkit-transform:translate(5px,-5px);transform:translate(5px,-5px)}}.loading3 .shape4[data-v-4e1c1bfa]{-webkit-animation:animation3shape4-data-v-4e1c1bfa .5s ease 0s infinite alternate;animation:animation3shape4-data-v-4e1c1bfa .5s ease 0s infinite alternate}@-webkit-keyframes animation3shape4-data-v-4e1c1bfa{from{-webkit-transform:translate(0);transform:translate(0)}to{-webkit-transform:translate(-5px,-5px);transform:translate(-5px,-5px)}}@keyframes animation3shape4-data-v-4e1c1bfa{from{-webkit-transform:translate(0);transform:translate(0)}to{-webkit-transform:translate(-5px,-5px);transform:translate(-5px,-5px)}}",""]),e.exports=t},"3c70":function(e,t,a){"use strict";a.r(t);var n=a("86d5"),i=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=i.a},4085:function(e,t,a){"use strict";var n=a("8bdb"),i=a("85c1");n({global:!0,forced:i.globalThis!==i},{globalThis:i})},"45cc":function(e,t,a){"use strict";a("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default={name:"loading2",data:function(){return{}}}},4953:function(e,t,a){"use strict";a.r(t);var n=a("df46"),i=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=i.a},"4c75":function(e,t,a){"use strict";a("6a54");var n=a("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("64aa");var i=n(a("2e28")),r=n(a("902c")),o=n(a("d304")),s=n(a("7040")),l=n(a("7502")),c={components:{Loading1:i.default,Loading2:r.default,Loading3:o.default,Loading4:s.default,Loading5:l.default},name:"qiun-loading",props:{loadingType:{type:Number,default:2}},data:function(){return{}}};t.default=c},"4d04":function(e,t,a){"use strict";a.d(t,"b",(function(){return n})),a.d(t,"c",(function(){return i})),a.d(t,"a",(function(){}));var n=function(){var e=this.$createElement,t=this._self._c||e;return t("v-uni-view",{staticClass:"container loading1"},[t("v-uni-view",{staticClass:"shape shape1"}),t("v-uni-view",{staticClass:"shape shape2"}),t("v-uni-view",{staticClass:"shape shape3"}),t("v-uni-view",{staticClass:"shape shape4"})],1)},i=[]},"512c":function(e,t,a){var n=a("3472");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[e.i,n,""]]),n.locals&&(e.exports=n.locals);var i=a("967d").default;i("58faa0d5",n,!0,{sourceMap:!1,shadowMode:!1})},5840:function(e,t,a){"use strict";a.r(t);var n=a("ac02"),i=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=i.a},"5e71":function(e,t,a){"use strict";var n=a("9d51"),i=a.n(n);i.a},"62b0":function(e,t,a){"use strict";a("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){if(t&&("object"===(0,n.default)(t)||"function"===typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return(0,i.default)(e)},a("7a76"),a("c9b5");var n=r(a("fcf3")),i=r(a("f478"));function r(e){return e&&e.__esModule?e:{default:e}}},"68ef":function(e,t,a){"use strict";a("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=l,a("c1a3"),a("bf0f"),a("18f7"),a("de6c"),a("7a76"),a("c9b5");var n=s(a("f1f8")),i=s(a("e668")),r=s(a("d441")),o=s(a("d2c4"));function s(e){return e&&e.__esModule?e:{default:e}}function l(e){var a="function"===typeof Map?new Map:void 0;return t.default=l=function(e){if(null===e||!(0,r.default)(e))return e;if("function"!==typeof e)throw new TypeError("Super expression must either be null or a function");if("undefined"!==typeof a){if(a.has(e))return a.get(e);a.set(e,t)}function t(){return(0,o.default)(e,arguments,(0,n.default)(this).constructor)}return t.prototype=Object.create(e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),(0,i.default)(t,e)},l(e)}},6978:function(e,t,a){"use strict";a.d(t,"b",(function(){return n})),a.d(t,"c",(function(){return i})),a.d(t,"a",(function(){}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("v-uni-view",[1==e.loadingType?a("Loading1"):e._e(),2==e.loadingType?a("Loading2"):e._e(),3==e.loadingType?a("Loading3"):e._e(),4==e.loadingType?a("Loading4"):e._e(),5==e.loadingType?a("Loading5"):e._e()],1)},i=[]},"6c31":function(e,t,a){"use strict";a("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}},a("bf0f"),a("7996"),a("6a88")},"6e1f":function(e,t,a){"use strict";var n=a("7663"),i=a.n(n);i.a},7040:function(e,t,a){"use strict";a.r(t);var n=a("e4bb"),i=a("d110");for(var r in i)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(r);a("e8eb");var o=a("828b"),s=Object(o["a"])(i["default"],n["b"],n["c"],!1,null,"37562626",null,!1,n["a"],void 0);t["default"]=s.exports},"70e7":function(e,t,a){"use strict";a.r(t);var n=a("6978"),i=a("2cc9");for(var r in i)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(r);var o=a("828b"),s=Object(o["a"])(i["default"],n["b"],n["c"],!1,null,"24ee7f38",null,!1,n["a"],void 0);t["default"]=s.exports},"734d":function(e,t,a){var n=a("7c40");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[e.i,n,""]]),n.locals&&(e.exports=n.locals);var i=a("967d").default;i("c885cb2c",n,!0,{sourceMap:!1,shadowMode:!1})},7502:function(e,t,a){"use strict";a.r(t);var n=a("9583"),i=a("5840");for(var r in i)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(r);a("2bfa");var o=a("828b"),s=Object(o["a"])(i["default"],n["b"],n["c"],!1,null,"6e5d9c90",null,!1,n["a"],void 0);t["default"]=s.exports},7663:function(e,t,a){var n=a("bcb2");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[e.i,n,""]]),n.locals&&(e.exports=n.locals);var i=a("967d").default;i("dea3bc4e",n,!0,{sourceMap:!1,shadowMode:!1})},7789:function(e,t,a){"use strict";a("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default={name:"loading5",data:function(){return{}}}},"7c40":function(e,t,a){var n=a("c86c");t=n(!1),t.push([e.i,".container[data-v-2ef93b2c]{width:30px;height:30px;position:relative}.container.loading2[data-v-2ef93b2c]{-webkit-transform:rotate(10deg);transform:rotate(10deg)}.container.loading2 .shape[data-v-2ef93b2c]{border-radius:5px}.container.loading2[data-v-2ef93b2c]{-webkit-animation:rotation 1s infinite;animation:rotation 1s infinite}.container .shape[data-v-2ef93b2c]{position:absolute;width:10px;height:10px;border-radius:1px}.container .shape.shape1[data-v-2ef93b2c]{left:0;background-color:#1890ff}.container .shape.shape2[data-v-2ef93b2c]{right:0;background-color:#91cb74}.container .shape.shape3[data-v-2ef93b2c]{bottom:0;background-color:#fac858}.container .shape.shape4[data-v-2ef93b2c]{bottom:0;right:0;background-color:#e66}.loading2 .shape1[data-v-2ef93b2c]{-webkit-animation:animation2shape1-data-v-2ef93b2c .5s ease 0s infinite alternate;animation:animation2shape1-data-v-2ef93b2c .5s ease 0s infinite alternate}@-webkit-keyframes animation2shape1-data-v-2ef93b2c{from{-webkit-transform:translate(0);transform:translate(0)}to{-webkit-transform:translate(20px,20px);transform:translate(20px,20px)}}@keyframes animation2shape1-data-v-2ef93b2c{from{-webkit-transform:translate(0);transform:translate(0)}to{-webkit-transform:translate(20px,20px);transform:translate(20px,20px)}}.loading2 .shape2[data-v-2ef93b2c]{-webkit-animation:animation2shape2-data-v-2ef93b2c .5s ease 0s infinite alternate;animation:animation2shape2-data-v-2ef93b2c .5s ease 0s infinite alternate}@-webkit-keyframes animation2shape2-data-v-2ef93b2c{from{-webkit-transform:translate(0);transform:translate(0)}to{-webkit-transform:translate(-20px,20px);transform:translate(-20px,20px)}}@keyframes animation2shape2-data-v-2ef93b2c{from{-webkit-transform:translate(0);transform:translate(0)}to{-webkit-transform:translate(-20px,20px);transform:translate(-20px,20px)}}.loading2 .shape3[data-v-2ef93b2c]{-webkit-animation:animation2shape3-data-v-2ef93b2c .5s ease 0s infinite alternate;animation:animation2shape3-data-v-2ef93b2c .5s ease 0s infinite alternate}@-webkit-keyframes animation2shape3-data-v-2ef93b2c{from{-webkit-transform:translate(0);transform:translate(0)}to{-webkit-transform:translate(20px,-20px);transform:translate(20px,-20px)}}@keyframes animation2shape3-data-v-2ef93b2c{from{-webkit-transform:translate(0);transform:translate(0)}to{-webkit-transform:translate(20px,-20px);transform:translate(20px,-20px)}}.loading2 .shape4[data-v-2ef93b2c]{-webkit-animation:animation2shape4-data-v-2ef93b2c .5s ease 0s infinite alternate;animation:animation2shape4-data-v-2ef93b2c .5s ease 0s infinite alternate}@-webkit-keyframes animation2shape4-data-v-2ef93b2c{from{-webkit-transform:translate(0);transform:translate(0)}to{-webkit-transform:translate(-20px,-20px);transform:translate(-20px,-20px)}}@keyframes animation2shape4-data-v-2ef93b2c{from{-webkit-transform:translate(0);transform:translate(0)}to{-webkit-transform:translate(-20px,-20px);transform:translate(-20px,-20px)}}",""]),e.exports=t},"7e25":function(e,t,a){"use strict";a("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n={name:"qiun-error",props:{errorMessage:{type:String,default:null}},data:function(){return{}}};t.default=n},"833d":function(e,t,a){"use strict";a("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=["#1890FF","#91CB74","#FAC858","#EE6666","#73C0DE","#3CA272","#FC8452","#9A60B4","#ea7ccc"],i={type:["pie","ring","rose","word","funnel","map","arcbar","line","column","mount","bar","area","radar","gauge","candle","mix","tline","tarea","scatter","bubble","demotype"],range:["饼状图","圆环图","玫瑰图","词云图","漏斗图","地图","圆弧进度条","折线图","柱状图","山峰图","条状图","区域图","雷达图","仪表盘","K线图","混合图","时间轴折线","时间轴区域","散点图","气泡图","自定义类型"],categories:["line","column","mount","bar","area","radar","gauge","candle","mix","demotype"],instance:{},option:{},formatter:{yAxisDemo1:function(e,t,a){return e+"元"},yAxisDemo2:function(e,t,a){return e.toFixed(2)},xAxisDemo1:function(e,t,a){return e+"年"},xAxisDemo2:function(e,t,a){return function(e,t){var a=new Date;a.setTime(1e3*e);var n=a.getFullYear(),i=a.getMonth()+1;i=i<10?"0"+i:i;var r=a.getDate();r=r<10?"0"+r:r;var o=a.getHours();o=o<10?"0"+o:o;var s=a.getMinutes(),l=a.getSeconds();return s=s<10?"0"+s:s,l=l<10?"0"+l:l,"full"==t?n+"-"+i+"-"+r+" "+o+":"+s+":"+l:"y-m-d"==t?n+"-"+i+"-"+r:"h:m"==t?o+":"+s:"h:m:s"==t?o+":"+s+":"+l:[n,i,r,o,s,l]}(e,"h:m")},seriesDemo1:function(e,t,a,n){return e+"元"},tooltipDemo1:function(e,t,a,n){return 0==a?"随便用"+e.data+"年":"其他我没改"+e.data+"天"},pieDemo:function(e,t,a,n){if(void 0!==t)return a[t].name+"："+a[t].data+"元"}},demotype:{type:"line",color:n,padding:[15,10,0,15],xAxis:{disableGrid:!0},yAxis:{gridType:"dash",dashLength:2},legend:{},extra:{line:{type:"curve",width:2}}},pie:{type:"pie",color:n,padding:[5,5,5,5],extra:{pie:{activeOpacity:.5,activeRadius:10,offsetAngle:0,labelWidth:15,border:!0,borderWidth:3,borderColor:"#FFFFFF"}}},ring:{type:"ring",color:n,padding:[5,5,5,5],rotate:!1,dataLabel:!0,legend:{show:!0,position:"right",lineHeight:25},title:{name:"收益率",fontSize:15,color:"#666666"},subtitle:{name:"70%",fontSize:25,color:"#7cb5ec"},extra:{ring:{ringWidth:30,activeOpacity:.5,activeRadius:10,offsetAngle:0,labelWidth:15,border:!0,borderWidth:3,borderColor:"#FFFFFF"}}},rose:{type:"rose",color:n,padding:[5,5,5,5],legend:{show:!0,position:"left",lineHeight:25},extra:{rose:{type:"area",minRadius:50,activeOpacity:.5,activeRadius:10,offsetAngle:0,labelWidth:15,border:!1,borderWidth:2,borderColor:"#FFFFFF"}}},word:{type:"word",color:n,extra:{word:{type:"normal",autoColors:!1}}},funnel:{type:"funnel",color:n,padding:[15,15,0,15],extra:{funnel:{activeOpacity:.3,activeWidth:10,border:!0,borderWidth:2,borderColor:"#FFFFFF",fillOpacity:1,labelAlign:"right"}}},map:{type:"map",color:n,padding:[0,0,0,0],dataLabel:!0,extra:{map:{border:!0,borderWidth:1,borderColor:"#666666",fillOpacity:.6,activeBorderColor:"#F04864",activeFillColor:"#FACC14",activeFillOpacity:1}}},arcbar:{type:"arcbar",color:n,title:{name:"百分比",fontSize:25,color:"#00FF00"},subtitle:{name:"默认标题",fontSize:15,color:"#666666"},extra:{arcbar:{type:"default",width:12,backgroundColor:"#E9E9E9",startAngle:.75,endAngle:.25,gap:2}}},line:{type:"line",color:n,padding:[15,10,0,15],xAxis:{disableGrid:!0},yAxis:{gridType:"dash",dashLength:2},legend:{},extra:{line:{type:"straight",width:2,activeType:"hollow"}}},tline:{type:"line",color:n,padding:[15,10,0,15],xAxis:{disableGrid:!1,boundaryGap:"justify"},yAxis:{gridType:"dash",dashLength:2,data:[{min:0,max:80}]},legend:{},extra:{line:{type:"curve",width:2,activeType:"hollow"}}},tarea:{type:"area",color:n,padding:[15,10,0,15],xAxis:{disableGrid:!0,boundaryGap:"justify"},yAxis:{gridType:"dash",dashLength:2,data:[{min:0,max:80}]},legend:{},extra:{area:{type:"curve",opacity:.2,addLine:!0,width:2,gradient:!0,activeType:"hollow"}}},column:{type:"column",color:n,padding:[15,15,0,5],xAxis:{disableGrid:!0},yAxis:{data:[{min:0}]},legend:{},extra:{column:{type:"group",width:30,activeBgColor:"#000000",activeBgOpacity:.08}}},mount:{type:"mount",color:n,padding:[15,15,0,5],xAxis:{disableGrid:!0},yAxis:{data:[{min:0}]},legend:{},extra:{mount:{type:"mount",widthRatio:1.5}}},bar:{type:"bar",color:n,padding:[15,30,0,5],xAxis:{boundaryGap:"justify",disableGrid:!1,min:0,axisLine:!1},yAxis:{},legend:{},extra:{bar:{type:"group",width:30,meterBorde:1,meterFillColor:"#FFFFFF",activeBgColor:"#000000",activeBgOpacity:.08}}},area:{type:"area",color:n,padding:[15,15,0,15],xAxis:{disableGrid:!0},yAxis:{gridType:"dash",dashLength:2},legend:{},extra:{area:{type:"straight",opacity:.2,addLine:!0,width:2,gradient:!1,activeType:"hollow"}}},radar:{type:"radar",color:n,padding:[5,5,5,5],dataLabel:!1,legend:{show:!0,position:"right",lineHeight:25},extra:{radar:{gridType:"radar",gridColor:"#CCCCCC",gridCount:3,opacity:.2,max:200,labelShow:!0}}},gauge:{type:"gauge",color:n,title:{name:"66Km/H",fontSize:25,color:"#2fc25b",offsetY:50},subtitle:{name:"实时速度",fontSize:15,color:"#1890ff",offsetY:-50},extra:{gauge:{type:"default",width:30,labelColor:"#666666",startAngle:.75,endAngle:.25,startNumber:0,endNumber:100,labelFormat:"",splitLine:{fixRadius:0,splitNumber:10,width:30,color:"#FFFFFF",childNumber:5,childWidth:12},pointer:{width:24,color:"auto"}}}},candle:{type:"candle",color:n,padding:[15,15,0,15],enableScroll:!0,enableMarkLine:!0,dataLabel:!1,xAxis:{labelCount:4,itemCount:40,disableGrid:!0,gridColor:"#CCCCCC",gridType:"solid",dashLength:4,scrollShow:!0,scrollAlign:"left",scrollColor:"#A6A6A6",scrollBackgroundColor:"#EFEBEF"},yAxis:{},legend:{},extra:{candle:{color:{upLine:"#f04864",upFill:"#f04864",downLine:"#2fc25b",downFill:"#2fc25b"},average:{show:!0,name:["MA5","MA10","MA30"],day:[5,10,20],color:["#1890ff","#2fc25b","#facc14"]}},markLine:{type:"dash",dashLength:5,data:[{value:2150,lineColor:"#f04864",showLabel:!0},{value:2350,lineColor:"#f04864",showLabel:!0}]}}},mix:{type:"mix",color:n,padding:[15,15,0,15],xAxis:{disableGrid:!0},yAxis:{disabled:!1,disableGrid:!1,splitNumber:5,gridType:"dash",dashLength:4,gridColor:"#CCCCCC",padding:10,showTitle:!0,data:[]},legend:{},extra:{mix:{column:{width:20}}}},scatter:{type:"scatter",color:n,padding:[15,15,0,15],dataLabel:!1,xAxis:{disableGrid:!1,gridType:"dash",splitNumber:5,boundaryGap:"justify",min:0},yAxis:{disableGrid:!1,gridType:"dash"},legend:{},extra:{scatter:{}}},bubble:{type:"bubble",color:n,padding:[15,15,0,15],xAxis:{disableGrid:!1,gridType:"dash",splitNumber:5,boundaryGap:"justify",min:0,max:250},yAxis:{disableGrid:!1,gridType:"dash",data:[{min:0,max:150}]},legend:{},extra:{bubble:{border:2,opacity:.5}}}},r=i;t.default=r},"861b":function(e,t,a){"use strict";(function(e){var n=a("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.uniCloud=t.default=t.UniCloudError=void 0;var i=n(a("f478")),r=n(a("5de6")),o=n(a("fcf3")),s=n(a("b7c7")),l=n(a("3471")),c=n(a("2634")),u=n(a("2fdc")),d=n(a("9b1b")),h=n(a("acb1")),f=n(a("cad9")),p=n(a("68ef")),g=n(a("80b1")),x=n(a("efe5"));a("4085"),a("7a76"),a("c9b5"),a("bf0f"),a("ab80"),a("f7a5"),a("aa9c"),a("e966"),a("c223"),a("dd2b"),a("5ef2"),a("2797"),a("dc8a"),a("473f"),a("4626"),a("5ac7"),a("4100"),a("5c47"),a("d4b5"),a("0c26"),a("0506"),a("fd3c"),a("6a54"),a("a1c1"),a("de6c"),a("c1a3"),a("18f7"),a("af8f"),a("64aa"),a("8f71"),a("23f4"),a("7d2f"),a("9c4e"),a("4db2"),a("c976"),a("4d8f"),a("7b97"),a("668a"),a("c5b7"),a("8ff5"),a("2378"),a("641a"),a("64e0"),a("cce3"),a("efba"),a("d009"),a("bd7d"),a("7edd"),a("d798"),a("f547"),a("5e54"),a("b60a"),a("8c18"),a("12973"),a("f991"),a("198e"),a("8557"),a("63b1"),a("1954"),a("1cf1"),a("01a2"),a("e39c"),a("e062"),a("aa77"),a("2c10"),a("f555"),a("dc69"),a("9370"),a("6730"),a("08eb"),a("15d1"),a("d5c6"),a("5a56"),a("f074"),a("20f3");var v=n(a("c86b1"));function m(e,t,a){return e(a={path:t,exports:{},require:function(e,t){return function(){throw new Error("Dynamic requires are not currently supported by @rollup/plugin-commonjs")}(null==t&&a.path)}},a.exports),a.exports}"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof e||"undefined"!=typeof self&&self;var y=m((function(e,t){var a;e.exports=(a=a||function(e,t){var a=Object.create||function(){function e(){}return function(t){var a;return e.prototype=t,a=new e,e.prototype=null,a}}(),n={},i=n.lib={},r=i.Base={extend:function(e){var t=a(this);return e&&t.mixIn(e),t.hasOwnProperty("init")&&this.init!==t.init||(t.init=function(){t.$super.init.apply(this,arguments)}),t.init.prototype=t,t.$super=this,t},create:function(){var e=this.extend();return e.init.apply(e,arguments),e},init:function(){},mixIn:function(e){for(var t in e)e.hasOwnProperty(t)&&(this[t]=e[t]);e.hasOwnProperty("toString")&&(this.toString=e.toString)},clone:function(){return this.init.prototype.extend(this)}},o=i.WordArray=r.extend({init:function(e,t){e=this.words=e||[],this.sigBytes=void 0!=t?t:4*e.length},toString:function(e){return(e||l).stringify(this)},concat:function(e){var t=this.words,a=e.words,n=this.sigBytes,i=e.sigBytes;if(this.clamp(),n%4)for(var r=0;r<i;r++){var o=a[r>>>2]>>>24-r%4*8&255;t[n+r>>>2]|=o<<24-(n+r)%4*8}else for(r=0;r<i;r+=4)t[n+r>>>2]=a[r>>>2];return this.sigBytes+=i,this},clamp:function(){var t=this.words,a=this.sigBytes;t[a>>>2]&=4294967295<<32-a%4*8,t.length=e.ceil(a/4)},clone:function(){var e=r.clone.call(this);return e.words=this.words.slice(0),e},random:function(t){for(var a,n=[],i=function(t){var a=987654321,n=4294967295;return function(){var i=((a=36969*(65535&a)+(a>>16)&n)<<16)+(t=18e3*(65535&t)+(t>>16)&n)&n;return i/=4294967296,(i+=.5)*(e.random()>.5?1:-1)}},r=0;r<t;r+=4){var s=i(4294967296*(a||e.random()));a=987654071*s(),n.push(4294967296*s()|0)}return new o.init(n,t)}}),s=n.enc={},l=s.Hex={stringify:function(e){for(var t=e.words,a=e.sigBytes,n=[],i=0;i<a;i++){var r=t[i>>>2]>>>24-i%4*8&255;n.push((r>>>4).toString(16)),n.push((15&r).toString(16))}return n.join("")},parse:function(e){for(var t=e.length,a=[],n=0;n<t;n+=2)a[n>>>3]|=parseInt(e.substr(n,2),16)<<24-n%8*4;return new o.init(a,t/2)}},c=s.Latin1={stringify:function(e){for(var t=e.words,a=e.sigBytes,n=[],i=0;i<a;i++){var r=t[i>>>2]>>>24-i%4*8&255;n.push(String.fromCharCode(r))}return n.join("")},parse:function(e){for(var t=e.length,a=[],n=0;n<t;n++)a[n>>>2]|=(255&e.charCodeAt(n))<<24-n%4*8;return new o.init(a,t)}},u=s.Utf8={stringify:function(e){try{return decodeURIComponent(escape(c.stringify(e)))}catch(e){throw new Error("Malformed UTF-8 data")}},parse:function(e){return c.parse(unescape(encodeURIComponent(e)))}},d=i.BufferedBlockAlgorithm=r.extend({reset:function(){this._data=new o.init,this._nDataBytes=0},_append:function(e){"string"==typeof e&&(e=u.parse(e)),this._data.concat(e),this._nDataBytes+=e.sigBytes},_process:function(t){var a=this._data,n=a.words,i=a.sigBytes,r=this.blockSize,s=i/(4*r),l=(s=t?e.ceil(s):e.max((0|s)-this._minBufferSize,0))*r,c=e.min(4*l,i);if(l){for(var u=0;u<l;u+=r)this._doProcessBlock(n,u);var d=n.splice(0,l);a.sigBytes-=c}return new o.init(d,c)},clone:function(){var e=r.clone.call(this);return e._data=this._data.clone(),e},_minBufferSize:0});i.Hasher=d.extend({cfg:r.extend(),init:function(e){this.cfg=this.cfg.extend(e),this.reset()},reset:function(){d.reset.call(this),this._doReset()},update:function(e){return this._append(e),this._process(),this},finalize:function(e){return e&&this._append(e),this._doFinalize()},blockSize:16,_createHelper:function(e){return function(t,a){return new e.init(a).finalize(t)}},_createHmacHelper:function(e){return function(t,a){return new h.HMAC.init(e,a).finalize(t)}}});var h=n.algo={};return n}(Math),a)})),b=y,w=(m((function(e,t){var a;e.exports=(a=b,function(e){var t=a,n=t.lib,i=n.WordArray,r=n.Hasher,o=t.algo,s=[];!function(){for(var t=0;t<64;t++)s[t]=4294967296*e.abs(e.sin(t+1))|0}();var l=o.MD5=r.extend({_doReset:function(){this._hash=new i.init([1732584193,4023233417,2562383102,271733878])},_doProcessBlock:function(e,t){for(var a=0;a<16;a++){var n=t+a,i=e[n];e[n]=16711935&(i<<8|i>>>24)|4278255360&(i<<24|i>>>8)}var r=this._hash.words,o=e[t+0],l=e[t+1],f=e[t+2],p=e[t+3],g=e[t+4],x=e[t+5],v=e[t+6],m=e[t+7],y=e[t+8],b=e[t+9],w=e[t+10],S=e[t+11],k=e[t+12],A=e[t+13],T=e[t+14],_=e[t+15],P=r[0],C=r[1],D=r[2],M=r[3];P=c(P,C,D,M,o,7,s[0]),M=c(M,P,C,D,l,12,s[1]),D=c(D,M,P,C,f,17,s[2]),C=c(C,D,M,P,p,22,s[3]),P=c(P,C,D,M,g,7,s[4]),M=c(M,P,C,D,x,12,s[5]),D=c(D,M,P,C,v,17,s[6]),C=c(C,D,M,P,m,22,s[7]),P=c(P,C,D,M,y,7,s[8]),M=c(M,P,C,D,b,12,s[9]),D=c(D,M,P,C,w,17,s[10]),C=c(C,D,M,P,S,22,s[11]),P=c(P,C,D,M,k,7,s[12]),M=c(M,P,C,D,A,12,s[13]),D=c(D,M,P,C,T,17,s[14]),P=u(P,C=c(C,D,M,P,_,22,s[15]),D,M,l,5,s[16]),M=u(M,P,C,D,v,9,s[17]),D=u(D,M,P,C,S,14,s[18]),C=u(C,D,M,P,o,20,s[19]),P=u(P,C,D,M,x,5,s[20]),M=u(M,P,C,D,w,9,s[21]),D=u(D,M,P,C,_,14,s[22]),C=u(C,D,M,P,g,20,s[23]),P=u(P,C,D,M,b,5,s[24]),M=u(M,P,C,D,T,9,s[25]),D=u(D,M,P,C,p,14,s[26]),C=u(C,D,M,P,y,20,s[27]),P=u(P,C,D,M,A,5,s[28]),M=u(M,P,C,D,f,9,s[29]),D=u(D,M,P,C,m,14,s[30]),P=d(P,C=u(C,D,M,P,k,20,s[31]),D,M,x,4,s[32]),M=d(M,P,C,D,y,11,s[33]),D=d(D,M,P,C,S,16,s[34]),C=d(C,D,M,P,T,23,s[35]),P=d(P,C,D,M,l,4,s[36]),M=d(M,P,C,D,g,11,s[37]),D=d(D,M,P,C,m,16,s[38]),C=d(C,D,M,P,w,23,s[39]),P=d(P,C,D,M,A,4,s[40]),M=d(M,P,C,D,o,11,s[41]),D=d(D,M,P,C,p,16,s[42]),C=d(C,D,M,P,v,23,s[43]),P=d(P,C,D,M,b,4,s[44]),M=d(M,P,C,D,k,11,s[45]),D=d(D,M,P,C,_,16,s[46]),P=h(P,C=d(C,D,M,P,f,23,s[47]),D,M,o,6,s[48]),M=h(M,P,C,D,m,10,s[49]),D=h(D,M,P,C,T,15,s[50]),C=h(C,D,M,P,x,21,s[51]),P=h(P,C,D,M,k,6,s[52]),M=h(M,P,C,D,p,10,s[53]),D=h(D,M,P,C,w,15,s[54]),C=h(C,D,M,P,l,21,s[55]),P=h(P,C,D,M,y,6,s[56]),M=h(M,P,C,D,_,10,s[57]),D=h(D,M,P,C,v,15,s[58]),C=h(C,D,M,P,A,21,s[59]),P=h(P,C,D,M,g,6,s[60]),M=h(M,P,C,D,S,10,s[61]),D=h(D,M,P,C,f,15,s[62]),C=h(C,D,M,P,b,21,s[63]),r[0]=r[0]+P|0,r[1]=r[1]+C|0,r[2]=r[2]+D|0,r[3]=r[3]+M|0},_doFinalize:function(){var t=this._data,a=t.words,n=8*this._nDataBytes,i=8*t.sigBytes;a[i>>>5]|=128<<24-i%32;var r=e.floor(n/4294967296),o=n;a[15+(i+64>>>9<<4)]=16711935&(r<<8|r>>>24)|4278255360&(r<<24|r>>>8),a[14+(i+64>>>9<<4)]=16711935&(o<<8|o>>>24)|4278255360&(o<<24|o>>>8),t.sigBytes=4*(a.length+1),this._process();for(var s=this._hash,l=s.words,c=0;c<4;c++){var u=l[c];l[c]=16711935&(u<<8|u>>>24)|4278255360&(u<<24|u>>>8)}return s},clone:function(){var e=r.clone.call(this);return e._hash=this._hash.clone(),e}});function c(e,t,a,n,i,r,o){var s=e+(t&a|~t&n)+i+o;return(s<<r|s>>>32-r)+t}function u(e,t,a,n,i,r,o){var s=e+(t&n|a&~n)+i+o;return(s<<r|s>>>32-r)+t}function d(e,t,a,n,i,r,o){var s=e+(t^a^n)+i+o;return(s<<r|s>>>32-r)+t}function h(e,t,a,n,i,r,o){var s=e+(a^(t|~n))+i+o;return(s<<r|s>>>32-r)+t}t.MD5=r._createHelper(l),t.HmacMD5=r._createHmacHelper(l)}(Math),a.MD5)})),m((function(e,t){var a;e.exports=(a=b,void function(){var e=a,t=e.lib.Base,n=e.enc.Utf8;e.algo.HMAC=t.extend({init:function(e,t){e=this._hasher=new e.init,"string"==typeof t&&(t=n.parse(t));var a=e.blockSize,i=4*a;t.sigBytes>i&&(t=e.finalize(t)),t.clamp();for(var r=this._oKey=t.clone(),o=this._iKey=t.clone(),s=r.words,l=o.words,c=0;c<a;c++)s[c]^=1549556828,l[c]^=909522486;r.sigBytes=o.sigBytes=i,this.reset()},reset:function(){var e=this._hasher;e.reset(),e.update(this._iKey)},update:function(e){return this._hasher.update(e),this},finalize:function(e){var t=this._hasher,a=t.finalize(e);return t.reset(),t.finalize(this._oKey.clone().concat(a))}})}())})),m((function(e,t){e.exports=b.HmacMD5}))),S=m((function(e,t){e.exports=b.enc.Utf8})),k=m((function(e,t){var a;e.exports=(a=b,function(){var e=a,t=e.lib.WordArray;function n(e,a,n){for(var i=[],r=0,o=0;o<a;o++)if(o%4){var s=n[e.charCodeAt(o-1)]<<o%4*2,l=n[e.charCodeAt(o)]>>>6-o%4*2;i[r>>>2]|=(s|l)<<24-r%4*8,r++}return t.create(i,r)}e.enc.Base64={stringify:function(e){var t=e.words,a=e.sigBytes,n=this._map;e.clamp();for(var i=[],r=0;r<a;r+=3)for(var o=(t[r>>>2]>>>24-r%4*8&255)<<16|(t[r+1>>>2]>>>24-(r+1)%4*8&255)<<8|t[r+2>>>2]>>>24-(r+2)%4*8&255,s=0;s<4&&r+.75*s<a;s++)i.push(n.charAt(o>>>6*(3-s)&63));var l=n.charAt(64);if(l)for(;i.length%4;)i.push(l);return i.join("")},parse:function(e){var t=e.length,a=this._map,i=this._reverseMap;if(!i){i=this._reverseMap=[];for(var r=0;r<a.length;r++)i[a.charCodeAt(r)]=r}var o=a.charAt(64);if(o){var s=e.indexOf(o);-1!==s&&(t=s)}return n(e,t,i)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="}}(),a.enc.Base64)})),A="uni_id_token",T="uni_id_token_expired",_={DEFAULT:"FUNCTION",FUNCTION:"FUNCTION",OBJECT:"OBJECT",CLIENT_DB:"CLIENT_DB"},P="pending",C="fulfilled",D="rejected";function M(e){return Object.prototype.toString.call(e).slice(8,-1).toLowerCase()}function I(e){return"object"===M(e)}function L(e){return"function"==typeof e}function O(e){return function(){try{return e.apply(e,arguments)}catch(e){console.error(e)}}}var F="REJECTED",E="NOT_PENDING",R=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},a=t.createPromise,n=t.retryRule,i=void 0===n?F:n;(0,g.default)(this,e),this.createPromise=a,this.status=null,this.promise=null,this.retryRule=i}return(0,x.default)(e,[{key:"needRetry",get:function(){if(!this.status)return!0;switch(this.retryRule){case F:return this.status===D;case E:return this.status!==P}}},{key:"exec",value:function(){var e=this;return this.needRetry?(this.status=P,this.promise=this.createPromise().then((function(t){return e.status=C,Promise.resolve(t)}),(function(t){return e.status=D,Promise.reject(t)})),this.promise):this.promise}}]),e}(),B=function(){function e(){(0,g.default)(this,e),this._callback={}}return(0,x.default)(e,[{key:"addListener",value:function(e,t){this._callback[e]||(this._callback[e]=[]),this._callback[e].push(t)}},{key:"on",value:function(e,t){return this.addListener(e,t)}},{key:"removeListener",value:function(e,t){if(!t)throw new Error('The "listener" argument must be of type function. Received undefined');var a=this._callback[e];if(a){var n=function(e,t){for(var a=e.length-1;a>=0;a--)if(e[a]===t)return a;return-1}(a,t);a.splice(n,1)}}},{key:"off",value:function(e,t){return this.removeListener(e,t)}},{key:"removeAllListener",value:function(e){delete this._callback[e]}},{key:"emit",value:function(e){for(var t=this._callback[e],a=arguments.length,n=new Array(a>1?a-1:0),i=1;i<a;i++)n[i-1]=arguments[i];if(t)for(var r=0;r<t.length;r++)t[r].apply(t,n)}}]),e}();function N(e){return e&&"string"==typeof e?JSON.parse(e):e}var U=N([]),z="web",W=(N(void 0),N([])||[]);try{(a("d416").default||a("d416")).appid}catch(mn){}var j,q={};function G(e){var t,a,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return t=q,a=e,Object.prototype.hasOwnProperty.call(t,a)||(q[e]=n),q[e]}"app"===z&&(q=uni._globalUniCloudObj?uni._globalUniCloudObj:uni._globalUniCloudObj={});var H=["invoke","success","fail","complete"],J=G("_globalUniCloudInterceptor");function Y(e,t){J[e]||(J[e]={}),I(t)&&Object.keys(t).forEach((function(a){H.indexOf(a)>-1&&function(e,t,a){var n=J[e][t];n||(n=J[e][t]=[]),-1===n.indexOf(a)&&L(a)&&n.push(a)}(e,a,t[a])}))}function K(e,t){J[e]||(J[e]={}),I(t)?Object.keys(t).forEach((function(a){H.indexOf(a)>-1&&function(e,t,a){var n=J[e][t];if(n){var i=n.indexOf(a);i>-1&&n.splice(i,1)}}(e,a,t[a])})):delete J[e]}function X(e,t){return e&&0!==e.length?e.reduce((function(e,a){return e.then((function(){return a(t)}))}),Promise.resolve()):Promise.resolve()}function V(e,t){return J[e]&&J[e][t]||[]}function Q(e){Y("callObject",e)}var Z=G("_globalUniCloudListener"),$={RESPONSE:"response",NEED_LOGIN:"needLogin",REFRESH_TOKEN:"refreshToken"},ee={CLIENT_DB:"clientdb",CLOUD_FUNCTION:"cloudfunction",CLOUD_OBJECT:"cloudobject"};function te(e){return Z[e]||(Z[e]=[]),Z[e]}function ae(e,t){var a=te(e);a.includes(t)||a.push(t)}function ne(e,t){var a=te(e),n=a.indexOf(t);-1!==n&&a.splice(n,1)}function ie(e,t){for(var a=te(e),n=0;n<a.length;n++)(0,a[n])(t)}var re,oe=!1;function se(){return re||(re=new Promise((function(e){oe&&e(),function t(){if("function"==typeof getCurrentPages){var a=getCurrentPages();a&&a[0]&&(oe=!0,e())}oe||setTimeout((function(){t()}),30)}()})),re)}function le(e){var t={};for(var a in e){var n=e[a];L(n)&&(t[a]=O(n))}return t}var ce=function(e){(0,h.default)(a,e);var t=(0,f.default)(a);function a(e){var n;return(0,g.default)(this,a),n=t.call(this,e.message),n.errMsg=e.message||e.errMsg||"unknown system error",n.code=n.errCode=e.code||e.errCode||"SYSTEM_ERROR",n.errSubject=n.subject=e.subject||e.errSubject,n.cause=e.cause,n.requestId=e.requestId,n}return(0,x.default)(a,[{key:"toJson",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;if(!(e>=10))return e++,{errCode:this.errCode,errMsg:this.errMsg,errSubject:this.errSubject,cause:this.cause&&this.cause.toJson?this.cause.toJson(e):this.cause}}}]),a}((0,p.default)(Error));t.UniCloudError=ce;var ue,de,he={request:function(e){return uni.request(e)},uploadFile:function(e){return uni.uploadFile(e)},setStorageSync:function(e,t){return uni.setStorageSync(e,t)},getStorageSync:function(e){return uni.getStorageSync(e)},removeStorageSync:function(e){return uni.removeStorageSync(e)},clearStorageSync:function(){return uni.clearStorageSync()},connectSocket:function(e){return uni.connectSocket(e)}};function fe(){return{token:he.getStorageSync(A)||he.getStorageSync("uniIdToken"),tokenExpired:he.getStorageSync(T)}}function pe(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.token,a=e.tokenExpired;t&&he.setStorageSync(A,t),a&&he.setStorageSync(T,a)}function ge(){return ue||(ue=uni.getSystemInfoSync()),ue}var xe={};function ve(){var e=uni.getLocale&&uni.getLocale()||"en";if(de)return(0,d.default)((0,d.default)((0,d.default)({},xe),de),{},{locale:e,LOCALE:e});var t=ge(),a=t.deviceId,n=t.osName,i=t.uniPlatform,r=t.appId,o=["appId","appLanguage","appName","appVersion","appVersionCode","appWgtVersion","browserName","browserVersion","deviceBrand","deviceId","deviceModel","deviceType","osName","osVersion","romName","romVersion","ua","hostName","hostVersion","uniPlatform","uniRuntimeVersion","uniRuntimeVersionCode","uniCompilerVersion","uniCompilerVersionCode"];for(var s in t)Object.hasOwnProperty.call(t,s)&&-1===o.indexOf(s)&&delete t[s];return de=(0,d.default)((0,d.default)({PLATFORM:i,OS:n,APPID:r,DEVICEID:a},function(){var e,t;try{if(uni.getLaunchOptionsSync){if(uni.getLaunchOptionsSync.toString().indexOf("not yet implemented")>-1)return;var a=uni.getLaunchOptionsSync(),n=a.scene,i=a.channel;e=i,t=n}}catch(e){}return{channel:e,scene:t}}()),t),(0,d.default)((0,d.default)((0,d.default)({},xe),de),{},{locale:e,LOCALE:e})}var me,ye={sign:function(e,t){var a="";return Object.keys(e).sort().forEach((function(t){e[t]&&(a=a+"&"+t+"="+e[t])})),a=a.slice(1),w(a,t).toString()},wrappedRequest:function(e,t){return new Promise((function(a,n){t(Object.assign(e,{complete:function(e){e||(e={});var t=e.data&&e.data.header&&e.data.header["x-serverless-request-id"]||e.header&&e.header["request-id"];if(!e.statusCode||e.statusCode>=400){var i=e.data&&e.data.error&&e.data.error.code||"SYS_ERR",r=e.data&&e.data.error&&e.data.error.message||e.errMsg||"request:fail";return n(new ce({code:i,message:r,requestId:t}))}var o=e.data;if(o.error)return n(new ce({code:o.error.code,message:o.error.message,requestId:t}));o.result=o.data,o.requestId=t,delete o.data,a(o)}}))}))},toBase64:function(e){return k.stringify(S.parse(e))}},be=function(){function e(t){var a=this;(0,g.default)(this,e),["spaceId","clientSecret"].forEach((function(e){if(!Object.prototype.hasOwnProperty.call(t,e))throw new Error("".concat(e," required"))})),this.config=Object.assign({},{endpoint:0===t.spaceId.indexOf("mp-")?"https://api.next.bspapp.com":"https://api.bspapp.com"},t),this.config.provider="aliyun",this.config.requestUrl=this.config.endpoint+"/client",this.config.envType=this.config.envType||"public",this.config.accessTokenKey="access_token_"+this.config.spaceId,this.adapter=he,this._getAccessTokenPromiseHub=new R({createPromise:function(){return a.requestAuth(a.setupRequest({method:"serverless.auth.user.anonymousAuthorize",params:"{}"},"auth")).then((function(e){if(!e.result||!e.result.accessToken)throw new ce({code:"AUTH_FAILED",message:"获取accessToken失败"});a.setAccessToken(e.result.accessToken)}))},retryRule:E})}return(0,x.default)(e,[{key:"hasAccessToken",get:function(){return!!this.accessToken}},{key:"setAccessToken",value:function(e){this.accessToken=e}},{key:"requestWrapped",value:function(e){return ye.wrappedRequest(e,this.adapter.request)}},{key:"requestAuth",value:function(e){return this.requestWrapped(e)}},{key:"request",value:function(e,t){var a=this;return Promise.resolve().then((function(){return a.hasAccessToken?t?a.requestWrapped(e):a.requestWrapped(e).catch((function(t){return new Promise((function(e,a){!t||"GATEWAY_INVALID_TOKEN"!==t.code&&"InvalidParameter.InvalidToken"!==t.code?a(t):e()})).then((function(){return a.getAccessToken()})).then((function(){var t=a.rebuildRequest(e);return a.request(t,!0)}))})):a.getAccessToken().then((function(){var t=a.rebuildRequest(e);return a.request(t,!0)}))}))}},{key:"rebuildRequest",value:function(e){var t=Object.assign({},e);return t.data.token=this.accessToken,t.header["x-basement-token"]=this.accessToken,t.header["x-serverless-sign"]=ye.sign(t.data,this.config.clientSecret),t}},{key:"setupRequest",value:function(e,t){var a=Object.assign({},e,{spaceId:this.config.spaceId,timestamp:Date.now()}),n={"Content-Type":"application/json"};return"auth"!==t&&(a.token=this.accessToken,n["x-basement-token"]=this.accessToken),n["x-serverless-sign"]=ye.sign(a,this.config.clientSecret),{url:this.config.requestUrl,method:"POST",data:a,dataType:"json",header:n}}},{key:"getAccessToken",value:function(){return this._getAccessTokenPromiseHub.exec()}},{key:"authorize",value:function(){var e=(0,u.default)((0,c.default)().mark((function e(){return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this.getAccessToken();case 2:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"callFunction",value:function(e){var t={method:"serverless.function.runtime.invoke",params:JSON.stringify({functionTarget:e.name,functionArgs:e.data||{}})};return this.request((0,d.default)((0,d.default)({},this.setupRequest(t)),{},{timeout:e.timeout}))}},{key:"getOSSUploadOptionsFromPath",value:function(e){var t={method:"serverless.file.resource.generateProximalSign",params:JSON.stringify(e)};return this.request(this.setupRequest(t))}},{key:"uploadFileToOSS",value:function(e){var t=this,a=e.url,n=e.formData,i=e.name,r=e.filePath,o=e.fileType,s=e.onUploadProgress;return new Promise((function(e,l){var c=t.adapter.uploadFile({url:a,formData:n,name:i,filePath:r,fileType:o,header:{"X-OSS-server-side-encrpytion":"AES256"},success:function(t){t&&t.statusCode<400?e(t):l(new ce({code:"UPLOAD_FAILED",message:"文件上传失败"}))},fail:function(e){l(new ce({code:e.code||"UPLOAD_FAILED",message:e.message||e.errMsg||"文件上传失败"}))}});"function"==typeof s&&c&&"function"==typeof c.onProgressUpdate&&c.onProgressUpdate((function(e){s({loaded:e.totalBytesSent,total:e.totalBytesExpectedToSend})}))}))}},{key:"reportOSSUpload",value:function(e){var t={method:"serverless.file.resource.report",params:JSON.stringify(e)};return this.request(this.setupRequest(t))}},{key:"uploadFile",value:function(){var e=(0,u.default)((0,c.default)().mark((function e(t){var a,n,i,r,o,s,l,u,d,h,f,p,g,x,v,m,y,b,w,S,k,A;return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(a=t.filePath,n=t.cloudPath,i=t.fileType,r=void 0===i?"image":i,o=t.cloudPathAsRealPath,s=void 0!==o&&o,l=t.onUploadProgress,u=t.config,"string"===M(n)){e.next=3;break}throw new ce({code:"INVALID_PARAM",message:"cloudPath必须为字符串类型"});case 3:if(n=n.trim()){e.next=5;break}throw new ce({code:"INVALID_PARAM",message:"cloudPath不可为空"});case 5:if(!/:\/\//.test(n)){e.next=7;break}throw new ce({code:"INVALID_PARAM",message:"cloudPath不合法"});case 7:if(d=u&&u.envType||this.config.envType,!(s&&("/"!==n[0]&&(n="/"+n),n.indexOf("\\")>-1))){e.next=10;break}throw new ce({code:"INVALID_PARAM",message:"使用cloudPath作为路径时，cloudPath不可包含“\\”"});case 10:return e.next=12,this.getOSSUploadOptionsFromPath({env:d,filename:s?n.split("/").pop():n,fileId:s?n:void 0});case 12:return h=e.sent.result,f="https://"+h.cdnDomain+"/"+h.ossPath,p=h.securityToken,g=h.accessKeyId,x=h.signature,v=h.host,m=h.ossPath,y=h.id,b=h.policy,w=h.ossCallbackUrl,S={"Cache-Control":"max-age=2592000","Content-Disposition":"attachment",OSSAccessKeyId:g,Signature:x,host:v,id:y,key:m,policy:b,success_action_status:200},p&&(S["x-oss-security-token"]=p),w&&(k=JSON.stringify({callbackUrl:w,callbackBody:JSON.stringify({fileId:y,spaceId:this.config.spaceId}),callbackBodyType:"application/json"}),S.callback=ye.toBase64(k)),A={url:"https://"+h.host,formData:S,fileName:"file",name:"file",filePath:a,fileType:r},e.next=27,this.uploadFileToOSS(Object.assign({},A,{onUploadProgress:l}));case 27:if(!w){e.next=29;break}return e.abrupt("return",{success:!0,filePath:a,fileID:f});case 29:return e.next=31,this.reportOSSUpload({id:y});case 31:if(!e.sent.success){e.next=33;break}return e.abrupt("return",{success:!0,filePath:a,fileID:f});case 33:throw new ce({code:"UPLOAD_FAILED",message:"文件上传失败"});case 34:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"getTempFileURL",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.fileList;return new Promise((function(e,a){Array.isArray(t)&&0!==t.length||a(new ce({code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"})),e({fileList:t.map((function(e){return{fileID:e,tempFileURL:e}}))})}))}},{key:"getFileInfo",value:function(){var e=(0,u.default)((0,c.default)().mark((function e(){var t,a,n,i=arguments;return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t=i.length>0&&void 0!==i[0]?i[0]:{},a=t.fileList,Array.isArray(a)&&0!==a.length){e.next=3;break}throw new ce({code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"});case 3:return n={method:"serverless.file.resource.info",params:JSON.stringify({id:a.map((function(e){return e.split("?")[0]})).join(",")})},e.next=6,this.request(this.setupRequest(n));case 6:return e.t0=e.sent.result,e.abrupt("return",{fileList:e.t0});case 8:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()}]),e}(),we={init:function(e){var t=new be(e),a={signInAnonymously:function(){return t.authorize()},getLoginState:function(){return Promise.resolve(!1)}};return t.auth=function(){return a},t.customAuth=t.auth,t}},Se="undefined"!=typeof location&&"http:"===location.protocol?"http:":"https:";!function(e){e.local="local",e.none="none",e.session="session"}(me||(me={}));var ke,Ae=function(){},Te=m((function(e,t){var a;e.exports=(a=b,function(e){var t=a,n=t.lib,i=n.WordArray,r=n.Hasher,o=t.algo,s=[],l=[];!function(){function t(t){for(var a=e.sqrt(t),n=2;n<=a;n++)if(!(t%n))return!1;return!0}function a(e){return 4294967296*(e-(0|e))|0}for(var n=2,i=0;i<64;)t(n)&&(i<8&&(s[i]=a(e.pow(n,.5))),l[i]=a(e.pow(n,1/3)),i++),n++}();var c=[],u=o.SHA256=r.extend({_doReset:function(){this._hash=new i.init(s.slice(0))},_doProcessBlock:function(e,t){for(var a=this._hash.words,n=a[0],i=a[1],r=a[2],o=a[3],s=a[4],u=a[5],d=a[6],h=a[7],f=0;f<64;f++){if(f<16)c[f]=0|e[t+f];else{var p=c[f-15],g=(p<<25|p>>>7)^(p<<14|p>>>18)^p>>>3,x=c[f-2],v=(x<<15|x>>>17)^(x<<13|x>>>19)^x>>>10;c[f]=g+c[f-7]+v+c[f-16]}var m=n&i^n&r^i&r,y=(n<<30|n>>>2)^(n<<19|n>>>13)^(n<<10|n>>>22),b=h+((s<<26|s>>>6)^(s<<21|s>>>11)^(s<<7|s>>>25))+(s&u^~s&d)+l[f]+c[f];h=d,d=u,u=s,s=o+b|0,o=r,r=i,i=n,n=b+(y+m)|0}a[0]=a[0]+n|0,a[1]=a[1]+i|0,a[2]=a[2]+r|0,a[3]=a[3]+o|0,a[4]=a[4]+s|0,a[5]=a[5]+u|0,a[6]=a[6]+d|0,a[7]=a[7]+h|0},_doFinalize:function(){var t=this._data,a=t.words,n=8*this._nDataBytes,i=8*t.sigBytes;return a[i>>>5]|=128<<24-i%32,a[14+(i+64>>>9<<4)]=e.floor(n/4294967296),a[15+(i+64>>>9<<4)]=n,t.sigBytes=4*a.length,this._process(),this._hash},clone:function(){var e=r.clone.call(this);return e._hash=this._hash.clone(),e}});t.SHA256=r._createHelper(u),t.HmacSHA256=r._createHmacHelper(u)}(Math),a.SHA256)})),_e=Te,Pe=m((function(e,t){e.exports=b.HmacSHA256})),Ce=function(){var e;if(!Promise){e=function(){},e.promise={};var t=function(){throw new ce({message:'Your Node runtime does support ES6 Promises. Set "global.Promise" to your preferred implementation of promises.'})};return Object.defineProperty(e.promise,"then",{get:t}),Object.defineProperty(e.promise,"catch",{get:t}),e}var a=new Promise((function(t,a){e=function(e,n){return e?a(e):t(n)}}));return e.promise=a,e};function De(e){return void 0===e}function Me(e){return"[object Null]"===Object.prototype.toString.call(e)}function Ie(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return e.replace(/([\s\S]+)\s+(请前往云开发AI小助手查看问题：.*)/,"$1")}function Le(){for(var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:32,t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",a="",n=0;n<e;n++)a+=t.charAt(Math.floor(62*Math.random()));return a}!function(e){e.WEB="web",e.WX_MP="wx_mp"}(ke||(ke={}));var Oe={adapter:null,runtime:void 0},Fe=["anonymousUuidKey"],Ee=function(e){(0,h.default)(a,e);var t=(0,f.default)(a);function a(){var e;return(0,g.default)(this,a),e=t.call(this),Oe.adapter.root.tcbObject||(Oe.adapter.root.tcbObject={}),e}return(0,x.default)(a,[{key:"setItem",value:function(e,t){Oe.adapter.root.tcbObject[e]=t}},{key:"getItem",value:function(e){return Oe.adapter.root.tcbObject[e]}},{key:"removeItem",value:function(e){delete Oe.adapter.root.tcbObject[e]}},{key:"clear",value:function(){delete Oe.adapter.root.tcbObject}}]),a}(Ae);function Re(e,t){switch(e){case"local":return t.localStorage||new Ee;case"none":return new Ee;default:return t.sessionStorage||new Ee}}var Be=function(){function e(t){if((0,g.default)(this,e),!this._storage){this._persistence=Oe.adapter.primaryStorage||t.persistence,this._storage=Re(this._persistence,Oe.adapter);var a="access_token_".concat(t.env),n="access_token_expire_".concat(t.env),i="refresh_token_".concat(t.env),r="anonymous_uuid_".concat(t.env),o="login_type_".concat(t.env),s="token_type_".concat(t.env),l="user_info_".concat(t.env);this.keys={accessTokenKey:a,accessTokenExpireKey:n,refreshTokenKey:i,anonymousUuidKey:r,loginTypeKey:o,userInfoKey:l,deviceIdKey:"device_id",tokenTypeKey:s}}}return(0,x.default)(e,[{key:"updatePersistence",value:function(e){if(e!==this._persistence){var t="local"===this._persistence;this._persistence=e;var a=Re(e,Oe.adapter);for(var n in this.keys){var i=this.keys[n];if(!t||!Fe.includes(n)){var r=this._storage.getItem(i);De(r)||Me(r)||(a.setItem(i,r),this._storage.removeItem(i))}}this._storage=a}}},{key:"setStore",value:function(e,t,a){if(this._storage){var n={version:a||"localCachev1",content:t},i=JSON.stringify(n);try{this._storage.setItem(e,i)}catch(e){throw e}}}},{key:"getStore",value:function(e,t){try{if(!this._storage)return}catch(e){return""}t=t||"localCachev1";var a=this._storage.getItem(e);return a&&a.indexOf(t)>=0?JSON.parse(a).content:""}},{key:"removeStore",value:function(e){this._storage.removeItem(e)}}]),e}(),Ne={},Ue={};function ze(e){return Ne[e]}var We=(0,x.default)((function e(t,a){(0,g.default)(this,e),this.data=a||null,this.name=t})),je=function(e){(0,h.default)(a,e);var t=(0,f.default)(a);function a(e,n){var i;return(0,g.default)(this,a),i=t.call(this,"error",{error:e,data:n}),i.error=e,i}return(0,x.default)(a)}(We),qe=new(function(){function e(){(0,g.default)(this,e),this._listeners={}}return(0,x.default)(e,[{key:"on",value:function(e,t){return function(e,t,a){a[e]=a[e]||[],a[e].push(t)}(e,t,this._listeners),this}},{key:"off",value:function(e,t){return function(e,t,a){if(a&&a[e]){var n=a[e].indexOf(t);-1!==n&&a[e].splice(n,1)}}(e,t,this._listeners),this}},{key:"fire",value:function(e,t){if(e instanceof je)return console.error(e.error),this;var a="string"==typeof e?new We(e,t||{}):e,n=a.name;if(this._listens(n)){a.target=this;var i,r=this._listeners[n]?(0,s.default)(this._listeners[n]):[],o=(0,l.default)(r);try{for(o.s();!(i=o.n()).done;){var c=i.value;c.call(this,a)}}catch(u){o.e(u)}finally{o.f()}}return this}},{key:"_listens",value:function(e){return this._listeners[e]&&this._listeners[e].length>0}}]),e}());function Ge(e,t){qe.on(e,t)}function He(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};qe.fire(e,t)}function Je(e,t){qe.off(e,t)}var Ye,Ke="loginStateChanged",Xe="loginStateExpire",Ve="loginTypeChanged",Qe="anonymousConverted",Ze="refreshAccessToken";!function(e){e.ANONYMOUS="ANONYMOUS",e.WECHAT="WECHAT",e.WECHAT_PUBLIC="WECHAT-PUBLIC",e.WECHAT_OPEN="WECHAT-OPEN",e.CUSTOM="CUSTOM",e.EMAIL="EMAIL",e.USERNAME="USERNAME",e.NULL="NULL"}(Ye||(Ye={}));var $e=function(){function e(){(0,g.default)(this,e),this._fnPromiseMap=new Map}return(0,x.default)(e,[{key:"run",value:function(){var e=(0,u.default)((0,c.default)().mark((function e(t,a){var n,i=this;return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n=this._fnPromiseMap.get(t),e.abrupt("return",(n||(n=new Promise(function(){var e=(0,u.default)((0,c.default)().mark((function e(n,r){var o;return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,i._runIdlePromise();case 3:return o=a(),e.t0=n,e.next=7,o;case 7:e.t1=e.sent,(0,e.t0)(e.t1),e.next=14;break;case 11:e.prev=11,e.t2=e["catch"](0),r(e.t2);case 14:return e.prev=14,i._fnPromiseMap.delete(t),e.finish(14);case 17:case"end":return e.stop()}}),e,null,[[0,11,14,17]])})));return function(t,a){return e.apply(this,arguments)}}()),this._fnPromiseMap.set(t,n)),n));case 2:case"end":return e.stop()}}),e,this)})));return function(t,a){return e.apply(this,arguments)}}()},{key:"_runIdlePromise",value:function(){return Promise.resolve()}}]),e}(),et=function(){function e(t){(0,g.default)(this,e),this._singlePromise=new $e,this._cache=ze(t.env),this._baseURL="https://".concat(t.env,".ap-shanghai.tcb-api.tencentcloudapi.com"),this._reqClass=new Oe.adapter.reqClass({timeout:t.timeout,timeoutMsg:"请求在".concat(t.timeout/1e3,"s内未完成，已中断"),restrictedMethods:["post"]})}return(0,x.default)(e,[{key:"_getDeviceId",value:function(){if(this._deviceID)return this._deviceID;var e=this._cache.keys.deviceIdKey,t=this._cache.getStore(e);return"string"==typeof t&&t.length>=16&&t.length<=48||(t=Le(),this._cache.setStore(e,t)),this._deviceID=t,t}},{key:"_request",value:function(){var e=(0,u.default)((0,c.default)().mark((function e(t,a){var n,i,r,o,s,l=arguments;return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(n=l.length>2&&void 0!==l[2]?l[2]:{},i={"x-request-id":Le(),"x-device-id":this._getDeviceId()},!n.withAccessToken){e.next=9;break}return r=this._cache.keys.tokenTypeKey,e.next=6,this.getAccessToken();case 6:o=e.sent,s=this._cache.getStore(r),i.authorization="".concat(s," ").concat(o);case 9:return e.abrupt("return",this._reqClass["get"===n.method?"get":"post"]({url:"".concat(this._baseURL).concat(t),data:a,headers:i}));case 10:case"end":return e.stop()}}),e,this)})));return function(t,a){return e.apply(this,arguments)}}()},{key:"_fetchAccessToken",value:function(){var e=(0,u.default)((0,c.default)().mark((function e(){var t,a,n,i,r,o,s,l,d,h,f=this;return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t=this._cache.keys,a=t.loginTypeKey,n=t.accessTokenKey,i=t.accessTokenExpireKey,r=t.tokenTypeKey,o=this._cache.getStore(a),!o||o===Ye.ANONYMOUS){e.next=3;break}throw new ce({code:"INVALID_OPERATION",message:"非匿名登录不支持刷新 access token"});case 3:return e.next=5,this._singlePromise.run("fetchAccessToken",(0,u.default)((0,c.default)().mark((function e(){return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,f._request("/auth/v1/signin/anonymously",{},{method:"post"});case 2:return e.abrupt("return",e.sent.data);case 3:case"end":return e.stop()}}),e)}))));case 5:return s=e.sent,l=s.access_token,d=s.expires_in,h=s.token_type,e.abrupt("return",(this._cache.setStore(r,h),this._cache.setStore(n,l),this._cache.setStore(i,Date.now()+1e3*d),l));case 10:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"isAccessTokenExpired",value:function(e,t){var a=!0;return e&&t&&(a=t<Date.now()),a}},{key:"getAccessToken",value:function(){var e=(0,u.default)((0,c.default)().mark((function e(){var t,a,n,i,r;return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t=this._cache.keys,a=t.accessTokenKey,n=t.accessTokenExpireKey,i=this._cache.getStore(a),r=this._cache.getStore(n),e.abrupt("return",this.isAccessTokenExpired(i,r)?this._fetchAccessToken():i);case 2:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"refreshAccessToken",value:function(){var e=(0,u.default)((0,c.default)().mark((function e(){var t,a,n,i;return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t=this._cache.keys,a=t.accessTokenKey,n=t.accessTokenExpireKey,i=t.loginTypeKey,e.abrupt("return",(this._cache.removeStore(a),this._cache.removeStore(n),this._cache.setStore(i,Ye.ANONYMOUS),this.getAccessToken()));case 2:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"getUserInfo",value:function(){var e=(0,u.default)((0,c.default)().mark((function e(){var t=this;return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",this._singlePromise.run("getUserInfo",(0,u.default)((0,c.default)().mark((function e(){return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t._request("/auth/v1/user/me",{},{withAccessToken:!0,method:"get"});case 2:return e.abrupt("return",e.sent.data);case 3:case"end":return e.stop()}}),e)})))));case 1:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()}]),e}(),tt=["auth.getJwt","auth.logout","auth.signInWithTicket","auth.signInAnonymously","auth.signIn","auth.fetchAccessTokenWithRefreshToken","auth.signUpWithEmailAndPassword","auth.activateEndUserMail","auth.sendPasswordResetEmail","auth.resetPasswordWithToken","auth.isUsernameRegistered"],at={"X-SDK-Version":"1.3.5"};function nt(e,t,a){var n=e[t];e[t]=function(t){var i={},r={};a.forEach((function(a){var n=a.call(e,t),o=n.data,s=n.headers;Object.assign(i,o),Object.assign(r,s)}));var o=t.data;return o&&function(){var e;if(e=o,"[object FormData]"!==Object.prototype.toString.call(e))t.data=(0,d.default)((0,d.default)({},o),i);else for(var a in i)o.append(a,i[a])}(),t.headers=(0,d.default)((0,d.default)({},t.headers||{}),r),n.call(e,t)}}function it(){var e=Math.random().toString(16).slice(2);return{data:{seqId:e},headers:(0,d.default)((0,d.default)({},at),{},{"x-seqid":e})}}var rt=function(){function e(){var t,a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};(0,g.default)(this,e),this.config=a,this._reqClass=new Oe.adapter.reqClass({timeout:this.config.timeout,timeoutMsg:"请求在".concat(this.config.timeout/1e3,"s内未完成，已中断"),restrictedMethods:["post"]}),this._cache=ze(this.config.env),this._localCache=(t=this.config.env,Ue[t]),this.oauth=new et(this.config),nt(this._reqClass,"post",[it]),nt(this._reqClass,"upload",[it]),nt(this._reqClass,"download",[it])}return(0,x.default)(e,[{key:"post",value:function(){var e=(0,u.default)((0,c.default)().mark((function e(t){return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this._reqClass.post(t);case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"upload",value:function(){var e=(0,u.default)((0,c.default)().mark((function e(t){return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this._reqClass.upload(t);case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"download",value:function(){var e=(0,u.default)((0,c.default)().mark((function e(t){return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this._reqClass.download(t);case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"refreshAccessToken",value:function(){var e=(0,u.default)((0,c.default)().mark((function e(){var t,a;return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this._refreshAccessTokenPromise||(this._refreshAccessTokenPromise=this._refreshAccessToken()),e.prev=1,e.next=4,this._refreshAccessTokenPromise;case 4:t=e.sent,e.next=10;break;case 7:e.prev=7,e.t0=e["catch"](1),a=e.t0;case 10:if(this._refreshAccessTokenPromise=null,this._shouldRefreshAccessTokenHook=null,!a){e.next=12;break}throw a;case 12:return e.abrupt("return",t);case 13:case"end":return e.stop()}}),e,this,[[1,7]])})));return function(){return e.apply(this,arguments)}}()},{key:"_refreshAccessToken",value:function(){var e=(0,u.default)((0,c.default)().mark((function e(){var t,a,n,i,r,o,s,l,u,d,h,f,p;return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t=this._cache.keys,a=t.accessTokenKey,n=t.accessTokenExpireKey,i=t.refreshTokenKey,r=t.loginTypeKey,o=t.anonymousUuidKey,this._cache.removeStore(a),this._cache.removeStore(n),s=this._cache.getStore(i),s){e.next=5;break}throw new ce({message:"未登录CloudBase"});case 5:return l={refresh_token:s},e.next=8,this.request("auth.fetchAccessTokenWithRefreshToken",l);case 8:if(u=e.sent,!u.data.code){e.next=21;break}if(d=u.data.code,"SIGN_PARAM_INVALID"!==d&&"REFRESH_TOKEN_EXPIRED"!==d&&"INVALID_REFRESH_TOKEN"!==d){e.next=20;break}if(this._cache.getStore(r)!==Ye.ANONYMOUS||"INVALID_REFRESH_TOKEN"!==d){e.next=19;break}return h=this._cache.getStore(o),f=this._cache.getStore(i),e.next=17,this.send("auth.signInAnonymously",{anonymous_uuid:h,refresh_token:f});case 17:return p=e.sent,e.abrupt("return",(this.setRefreshToken(p.refresh_token),this._refreshAccessToken()));case 19:He(Xe),this._cache.removeStore(i);case 20:throw new ce({code:u.data.code,message:"刷新access token失败：".concat(u.data.code)});case 21:if(!u.data.access_token){e.next=23;break}return e.abrupt("return",(He(Ze),this._cache.setStore(a,u.data.access_token),this._cache.setStore(n,u.data.access_token_expire+Date.now()),{accessToken:u.data.access_token,accessTokenExpire:u.data.access_token_expire}));case 23:u.data.refresh_token&&(this._cache.removeStore(i),this._cache.setStore(i,u.data.refresh_token),this._refreshAccessToken());case 24:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"getAccessToken",value:function(){var e=(0,u.default)((0,c.default)().mark((function e(){var t,a,n,i,r,o,s;return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t=this._cache.keys,a=t.accessTokenKey,n=t.accessTokenExpireKey,i=t.refreshTokenKey,this._cache.getStore(i)){e.next=3;break}throw new ce({message:"refresh token不存在，登录状态异常"});case 3:if(r=this._cache.getStore(a),o=this._cache.getStore(n),s=!0,e.t0=this._shouldRefreshAccessTokenHook,!e.t0){e.next=9;break}return e.next=8,this._shouldRefreshAccessTokenHook(r,o);case 8:e.t0=!e.sent;case 9:if(e.t1=e.t0,!e.t1){e.next=12;break}s=!1;case 12:return e.abrupt("return",(!r||!o||o<Date.now())&&s?this.refreshAccessToken():{accessToken:r,accessTokenExpire:o});case 13:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"request",value:function(){var e=(0,u.default)((0,c.default)().mark((function e(t,a,n){var i,r,o,s,l,u,h,f,p,g,x,v,m,y,b;return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(i="x-tcb-trace_".concat(this.config.env),r="application/x-www-form-urlencoded",o=(0,d.default)({action:t,env:this.config.env,dataVersion:"2019-08-16"},a),e.t0=-1===tt.indexOf(t),!e.t0){e.next=9;break}return this._cache.keys,e.next=8,this.oauth.getAccessToken();case 8:o.access_token=e.sent;case 9:if("storage.uploadFile"!==t){e.next=15;break}for(l in s=new FormData,s)s.hasOwnProperty(l)&&void 0!==s[l]&&s.append(l,o[l]);r="multipart/form-data",e.next=17;break;case 15:for(u in r="application/json",s={},o)void 0!==o[u]&&(s[u]=o[u]);case 17:return h={headers:{"content-type":r}},n&&n.timeout&&(h.timeout=n.timeout),n&&n.onUploadProgress&&(h.onUploadProgress=n.onUploadProgress),f=this._localCache.getStore(i),f&&(h.headers["X-TCB-Trace"]=f),p=a.parse,g=a.inQuery,x=a.search,v={env:this.config.env},p&&(v.parse=!0),g&&(v=(0,d.default)((0,d.default)({},g),v)),m=function(e,t){var a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},n=/\?/.test(t),i="";for(var r in a)""===i?!n&&(t+="?"):i+="&",i+="".concat(r,"=").concat(encodeURIComponent(a[r]));return/^http(s)?\:\/\//.test(t+=i)?t:"".concat(e).concat(t)}(Se,"//tcb-api.tencentcloudapi.com/web",v),x&&(m+=x),e.next=28,this.post((0,d.default)({url:m,data:s},h));case 28:if(y=e.sent,b=y.header&&y.header["x-tcb-trace"],b&&this._localCache.setStore(i,b),(200===Number(y.status)||200===Number(y.statusCode))&&y.data){e.next=32;break}throw new ce({code:"NETWORK_ERROR",message:"network request error"});case 32:return e.abrupt("return",y);case 33:case"end":return e.stop()}}),e,this)})));return function(t,a,n){return e.apply(this,arguments)}}()},{key:"send",value:function(){var e=(0,u.default)((0,c.default)().mark((function e(t){var a,n,i,r,o=arguments;return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return a=o.length>1&&void 0!==o[1]?o[1]:{},n=o.length>2&&void 0!==o[2]?o[2]:{},e.next=4,this.request(t,a,(0,d.default)((0,d.default)({},n),{},{onUploadProgress:a.onUploadProgress}));case 4:if(i=e.sent,"ACCESS_TOKEN_DISABLED"!==i.data.code&&"ACCESS_TOKEN_EXPIRED"!==i.data.code||-1!==tt.indexOf(t)){e.next=14;break}return e.next=8,this.oauth.refreshAccessToken();case 8:return e.next=10,this.request(t,a,(0,d.default)((0,d.default)({},n),{},{onUploadProgress:a.onUploadProgress}));case 10:if(r=e.sent,!r.data.code){e.next=13;break}throw new ce({code:r.data.code,message:Ie(r.data.message)});case 13:return e.abrupt("return",r.data);case 14:if(!i.data.code){e.next=16;break}throw new ce({code:i.data.code,message:Ie(i.data.message)});case 16:return e.abrupt("return",i.data);case 17:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"setRefreshToken",value:function(e){var t=this._cache.keys,a=t.accessTokenKey,n=t.accessTokenExpireKey,i=t.refreshTokenKey;this._cache.removeStore(a),this._cache.removeStore(n),this._cache.setStore(i,e)}}]),e}(),ot={};function st(e){return ot[e]}var lt=function(){function e(t){(0,g.default)(this,e),this.config=t,this._cache=ze(t.env),this._request=st(t.env)}return(0,x.default)(e,[{key:"setRefreshToken",value:function(e){var t=this._cache.keys,a=t.accessTokenKey,n=t.accessTokenExpireKey,i=t.refreshTokenKey;this._cache.removeStore(a),this._cache.removeStore(n),this._cache.setStore(i,e)}},{key:"setAccessToken",value:function(e,t){var a=this._cache.keys,n=a.accessTokenKey,i=a.accessTokenExpireKey;this._cache.setStore(n,e),this._cache.setStore(i,t)}},{key:"refreshUserInfo",value:function(){var e=(0,u.default)((0,c.default)().mark((function e(){var t,a;return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this._request.send("auth.getUserInfo",{});case 2:return t=e.sent,a=t.data,e.abrupt("return",(this.setLocalUserInfo(a),a));case 5:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"setLocalUserInfo",value:function(e){var t=this._cache.keys.userInfoKey;this._cache.setStore(t,e)}}]),e}(),ct=function(){function e(t){if((0,g.default)(this,e),!t)throw new ce({code:"PARAM_ERROR",message:"envId is not defined"});this._envId=t,this._cache=ze(this._envId),this._request=st(this._envId),this.setUserInfo()}return(0,x.default)(e,[{key:"linkWithTicket",value:function(e){if("string"!=typeof e)throw new ce({code:"PARAM_ERROR",message:"ticket must be string"});return this._request.send("auth.linkWithTicket",{ticket:e})}},{key:"linkWithRedirect",value:function(e){e.signInWithRedirect()}},{key:"updatePassword",value:function(e,t){return this._request.send("auth.updatePassword",{oldPassword:t,newPassword:e})}},{key:"updateEmail",value:function(e){return this._request.send("auth.updateEmail",{newEmail:e})}},{key:"updateUsername",value:function(e){if("string"!=typeof e)throw new ce({code:"PARAM_ERROR",message:"username must be a string"});return this._request.send("auth.updateUsername",{username:e})}},{key:"getLinkedUidList",value:function(){var e=(0,u.default)((0,c.default)().mark((function e(){var t,a,n,i;return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this._request.send("auth.getLinkedUidList",{});case 2:return t=e.sent,a=t.data,n=!1,i=a.users,e.abrupt("return",(i.forEach((function(e){e.wxOpenId&&e.wxPublicId&&(n=!0)})),{users:i,hasPrimaryUid:n}));case 7:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"setPrimaryUid",value:function(e){return this._request.send("auth.setPrimaryUid",{uid:e})}},{key:"unlink",value:function(e){return this._request.send("auth.unlink",{platform:e})}},{key:"update",value:function(){var e=(0,u.default)((0,c.default)().mark((function e(t){var a,n,i,r,o,s,l,u;return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return a=t.nickName,n=t.gender,i=t.avatarUrl,r=t.province,o=t.country,s=t.city,e.next=8,this._request.send("auth.updateUserInfo",{nickName:a,gender:n,avatarUrl:i,province:r,country:o,city:s});case 8:l=e.sent,u=l.data,this.setLocalUserInfo(u);case 11:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"refresh",value:function(){var e=(0,u.default)((0,c.default)().mark((function e(){var t;return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this._request.oauth.getUserInfo();case 2:return t=e.sent,e.abrupt("return",(this.setLocalUserInfo(t),t));case 4:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"setUserInfo",value:function(){var e=this,t=this._cache.keys.userInfoKey,a=this._cache.getStore(t);["uid","loginType","openid","wxOpenId","wxPublicId","unionId","qqMiniOpenId","email","hasPassword","customUserId","nickName","gender","avatarUrl"].forEach((function(t){e[t]=a[t]})),this.location={country:a.country,province:a.province,city:a.city}}},{key:"setLocalUserInfo",value:function(e){var t=this._cache.keys.userInfoKey;this._cache.setStore(t,e),this.setUserInfo()}}]),e}(),ut=function(){function e(t){if((0,g.default)(this,e),!t)throw new ce({code:"PARAM_ERROR",message:"envId is not defined"});this._cache=ze(t);var a=this._cache.keys,n=a.refreshTokenKey,i=a.accessTokenKey,r=a.accessTokenExpireKey,o=this._cache.getStore(n),s=this._cache.getStore(i),l=this._cache.getStore(r);this.credential={refreshToken:o,accessToken:s,accessTokenExpire:l},this.user=new ct(t)}return(0,x.default)(e,[{key:"isAnonymousAuth",get:function(){return this.loginType===Ye.ANONYMOUS}},{key:"isCustomAuth",get:function(){return this.loginType===Ye.CUSTOM}},{key:"isWeixinAuth",get:function(){return this.loginType===Ye.WECHAT||this.loginType===Ye.WECHAT_OPEN||this.loginType===Ye.WECHAT_PUBLIC}},{key:"loginType",get:function(){return this._cache.getStore(this._cache.keys.loginTypeKey)}}]),e}(),dt=function(e){(0,h.default)(a,e);var t=(0,f.default)(a);function a(){return(0,g.default)(this,a),t.apply(this,arguments)}return(0,x.default)(a,[{key:"signIn",value:function(){var e=(0,u.default)((0,c.default)().mark((function e(){var t;return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this._cache.updatePersistence("local"),e.next=3,this._request.oauth.getAccessToken();case 3:return He(Ke),He(Ve,{env:this.config.env,loginType:Ye.ANONYMOUS,persistence:"local"}),t=new ut(this.config.env),e.next=8,t.user.refresh();case 8:return e.abrupt("return",t);case 9:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"linkAndRetrieveDataWithTicket",value:function(){var e=(0,u.default)((0,c.default)().mark((function e(t){var a,n,i,r,o,s;return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return a=this._cache.keys,n=a.anonymousUuidKey,i=a.refreshTokenKey,r=this._cache.getStore(n),o=this._cache.getStore(i),e.next=7,this._request.send("auth.linkAndRetrieveDataWithTicket",{anonymous_uuid:r,refresh_token:o,ticket:t});case 7:if(s=e.sent,!s.refresh_token){e.next=16;break}return this._clearAnonymousUUID(),this.setRefreshToken(s.refresh_token),e.next=13,this._request.refreshAccessToken();case 13:return He(Qe,{env:this.config.env}),He(Ve,{loginType:Ye.CUSTOM,persistence:"local"}),e.abrupt("return",{credential:{refreshToken:s.refresh_token}});case 16:throw new ce({message:"匿名转化失败"});case 17:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"_setAnonymousUUID",value:function(e){var t=this._cache.keys,a=t.anonymousUuidKey,n=t.loginTypeKey;this._cache.removeStore(a),this._cache.setStore(a,e),this._cache.setStore(n,Ye.ANONYMOUS)}},{key:"_clearAnonymousUUID",value:function(){this._cache.removeStore(this._cache.keys.anonymousUuidKey)}}]),a}(lt),ht=function(e){(0,h.default)(a,e);var t=(0,f.default)(a);function a(){return(0,g.default)(this,a),t.apply(this,arguments)}return(0,x.default)(a,[{key:"signIn",value:function(){var e=(0,u.default)((0,c.default)().mark((function e(t){var a,n;return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if("string"==typeof t){e.next=2;break}throw new ce({code:"PARAM_ERROR",message:"ticket must be a string"});case 2:return a=this._cache.keys.refreshTokenKey,e.next=5,this._request.send("auth.signInWithTicket",{ticket:t,refresh_token:this._cache.getStore(a)||""});case 5:if(n=e.sent,!n.refresh_token){e.next=15;break}return this.setRefreshToken(n.refresh_token),e.next=10,this._request.refreshAccessToken();case 10:return He(Ke),He(Ve,{env:this.config.env,loginType:Ye.CUSTOM,persistence:this.config.persistence}),e.next=14,this.refreshUserInfo();case 14:return e.abrupt("return",new ut(this.config.env));case 15:throw new ce({message:"自定义登录失败"});case 16:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()}]),a}(lt),ft=function(e){(0,h.default)(a,e);var t=(0,f.default)(a);function a(){return(0,g.default)(this,a),t.apply(this,arguments)}return(0,x.default)(a,[{key:"signIn",value:function(){var e=(0,u.default)((0,c.default)().mark((function e(t,a){var n,i,r,o,s;return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if("string"==typeof t){e.next=2;break}throw new ce({code:"PARAM_ERROR",message:"email must be a string"});case 2:return n=this._cache.keys.refreshTokenKey,e.next=5,this._request.send("auth.signIn",{loginType:"EMAIL",email:t,password:a,refresh_token:this._cache.getStore(n)||""});case 5:if(i=e.sent,r=i.refresh_token,o=i.access_token,s=i.access_token_expire,!r){e.next=22;break}if(this.setRefreshToken(r),!o||!s){e.next=15;break}this.setAccessToken(o,s),e.next=17;break;case 15:return e.next=17,this._request.refreshAccessToken();case 17:return e.next=19,this.refreshUserInfo();case 19:return He(Ke),He(Ve,{env:this.config.env,loginType:Ye.EMAIL,persistence:this.config.persistence}),e.abrupt("return",new ut(this.config.env));case 22:throw i.code?new ce({code:i.code,message:"邮箱登录失败: ".concat(i.message)}):new ce({message:"邮箱登录失败"});case 23:case"end":return e.stop()}}),e,this)})));return function(t,a){return e.apply(this,arguments)}}()},{key:"activate",value:function(){var e=(0,u.default)((0,c.default)().mark((function e(t){return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",this._request.send("auth.activateEndUserMail",{token:t}));case 1:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"resetPasswordWithToken",value:function(){var e=(0,u.default)((0,c.default)().mark((function e(t,a){return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",this._request.send("auth.resetPasswordWithToken",{token:t,newPassword:a}));case 1:case"end":return e.stop()}}),e,this)})));return function(t,a){return e.apply(this,arguments)}}()}]),a}(lt),pt=function(e){(0,h.default)(a,e);var t=(0,f.default)(a);function a(){return(0,g.default)(this,a),t.apply(this,arguments)}return(0,x.default)(a,[{key:"signIn",value:function(){var e=(0,u.default)((0,c.default)().mark((function e(t,a){var n,i,r,o,s;return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if("string"==typeof t){e.next=2;break}throw new ce({code:"PARAM_ERROR",message:"username must be a string"});case 2:return"string"!=typeof a&&(a="",console.warn("password is empty")),n=this._cache.keys.refreshTokenKey,e.next=6,this._request.send("auth.signIn",{loginType:Ye.USERNAME,username:t,password:a,refresh_token:this._cache.getStore(n)||""});case 6:if(i=e.sent,r=i.refresh_token,o=i.access_token_expire,s=i.access_token,!r){e.next=23;break}if(this.setRefreshToken(r),!s||!o){e.next=16;break}this.setAccessToken(s,o),e.next=18;break;case 16:return e.next=18,this._request.refreshAccessToken();case 18:return e.next=20,this.refreshUserInfo();case 20:return He(Ke),He(Ve,{env:this.config.env,loginType:Ye.USERNAME,persistence:this.config.persistence}),e.abrupt("return",new ut(this.config.env));case 23:throw i.code?new ce({code:i.code,message:"用户名密码登录失败: ".concat(i.message)}):new ce({message:"用户名密码登录失败"});case 24:case"end":return e.stop()}}),e,this)})));return function(t,a){return e.apply(this,arguments)}}()}]),a}(lt),gt=function(){function e(t){(0,g.default)(this,e),this.config=t,this._cache=ze(t.env),this._request=st(t.env),this._onAnonymousConverted=this._onAnonymousConverted.bind(this),this._onLoginTypeChanged=this._onLoginTypeChanged.bind(this),Ge(Ve,this._onLoginTypeChanged)}return(0,x.default)(e,[{key:"currentUser",get:function(){var e=this.hasLoginState();return e&&e.user||null}},{key:"loginType",get:function(){return this._cache.getStore(this._cache.keys.loginTypeKey)}},{key:"anonymousAuthProvider",value:function(){return new dt(this.config)}},{key:"customAuthProvider",value:function(){return new ht(this.config)}},{key:"emailAuthProvider",value:function(){return new ft(this.config)}},{key:"usernameAuthProvider",value:function(){return new pt(this.config)}},{key:"signInAnonymously",value:function(){var e=(0,u.default)((0,c.default)().mark((function e(){return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",new dt(this.config).signIn());case 1:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"signInWithEmailAndPassword",value:function(){var e=(0,u.default)((0,c.default)().mark((function e(t,a){return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",new ft(this.config).signIn(t,a));case 1:case"end":return e.stop()}}),e,this)})));return function(t,a){return e.apply(this,arguments)}}()},{key:"signInWithUsernameAndPassword",value:function(e,t){return new pt(this.config).signIn(e,t)}},{key:"linkAndRetrieveDataWithTicket",value:function(){var e=(0,u.default)((0,c.default)().mark((function e(t){return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this._anonymousAuthProvider||(this._anonymousAuthProvider=new dt(this.config)),Ge(Qe,this._onAnonymousConverted),e.next=3,this._anonymousAuthProvider.linkAndRetrieveDataWithTicket(t);case 3:return e.abrupt("return",e.sent);case 4:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"signOut",value:function(){var e=(0,u.default)((0,c.default)().mark((function e(){var t,a,n,i,r,o;return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(this.loginType!==Ye.ANONYMOUS){e.next=2;break}throw new ce({message:"匿名用户不支持登出操作"});case 2:if(t=this._cache.keys,a=t.refreshTokenKey,n=t.accessTokenKey,i=t.accessTokenExpireKey,r=this._cache.getStore(a),r){e.next=5;break}return e.abrupt("return");case 5:return e.next=7,this._request.send("auth.logout",{refresh_token:r});case 7:return o=e.sent,e.abrupt("return",(this._cache.removeStore(a),this._cache.removeStore(n),this._cache.removeStore(i),He(Ke),He(Ve,{env:this.config.env,loginType:Ye.NULL,persistence:this.config.persistence}),o));case 9:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"signUpWithEmailAndPassword",value:function(){var e=(0,u.default)((0,c.default)().mark((function e(t,a){return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",this._request.send("auth.signUpWithEmailAndPassword",{email:t,password:a}));case 1:case"end":return e.stop()}}),e,this)})));return function(t,a){return e.apply(this,arguments)}}()},{key:"sendPasswordResetEmail",value:function(){var e=(0,u.default)((0,c.default)().mark((function e(t){return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",this._request.send("auth.sendPasswordResetEmail",{email:t}));case 1:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"onLoginStateChanged",value:function(e){var t=this;Ge(Ke,(function(){var a=t.hasLoginState();e.call(t,a)}));var a=this.hasLoginState();e.call(this,a)}},{key:"onLoginStateExpired",value:function(e){Ge(Xe,e.bind(this))}},{key:"onAccessTokenRefreshed",value:function(e){Ge(Ze,e.bind(this))}},{key:"onAnonymousConverted",value:function(e){Ge(Qe,e.bind(this))}},{key:"onLoginTypeChanged",value:function(e){var t=this;Ge(Ve,(function(){var a=t.hasLoginState();e.call(t,a)}))}},{key:"getAccessToken",value:function(){var e=(0,u.default)((0,c.default)().mark((function e(){return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this._request.getAccessToken();case 2:return e.t0=e.sent.accessToken,e.t1=this.config.env,e.abrupt("return",{accessToken:e.t0,env:e.t1});case 5:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"hasLoginState",value:function(){var e=this._cache.keys,t=e.accessTokenKey,a=e.accessTokenExpireKey,n=this._cache.getStore(t),i=this._cache.getStore(a);return this._request.oauth.isAccessTokenExpired(n,i)?null:new ut(this.config.env)}},{key:"isUsernameRegistered",value:function(){var e=(0,u.default)((0,c.default)().mark((function e(t){var a,n;return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if("string"==typeof t){e.next=2;break}throw new ce({code:"PARAM_ERROR",message:"username must be a string"});case 2:return e.next=4,this._request.send("auth.isUsernameRegistered",{username:t});case 4:return a=e.sent,n=a.data,e.abrupt("return",n&&n.isRegistered);case 7:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"getLoginState",value:function(){return Promise.resolve(this.hasLoginState())}},{key:"signInWithTicket",value:function(){var e=(0,u.default)((0,c.default)().mark((function e(t){return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",new ht(this.config).signIn(t));case 1:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"shouldRefreshAccessToken",value:function(e){this._request._shouldRefreshAccessTokenHook=e.bind(this)}},{key:"getUserInfo",value:function(){return this._request.send("auth.getUserInfo",{}).then((function(e){return e.code?e:(0,d.default)((0,d.default)({},e.data),{},{requestId:e.seqId})}))}},{key:"getAuthHeader",value:function(){var e=this._cache.keys,t=e.refreshTokenKey,a=e.accessTokenKey,n=this._cache.getStore(t);return{"x-cloudbase-credentials":this._cache.getStore(a)+"/@@/"+n}}},{key:"_onAnonymousConverted",value:function(e){var t=e.data.env;t===this.config.env&&this._cache.updatePersistence(this.config.persistence)}},{key:"_onLoginTypeChanged",value:function(e){var t=e.data,a=t.loginType,n=t.persistence,i=t.env;i===this.config.env&&(this._cache.updatePersistence(n),this._cache.setStore(this._cache.keys.loginTypeKey,a))}}]),e}(),xt=function(e,t){t=t||Ce();var a=st(this.config.env),n=e.cloudPath,i=e.filePath,r=e.onUploadProgress,o=e.fileType,s=void 0===o?"image":o;return a.send("storage.getUploadMetadata",{path:n}).then((function(e){var o=e.data,l=o.url,c=o.authorization,u=o.token,d=o.fileId,h=o.cosFileId,f=e.requestId,p={key:n,signature:c,"x-cos-meta-fileid":h,success_action_status:"201","x-cos-security-token":u};a.upload({url:l,data:p,file:i,name:n,fileType:s,onUploadProgress:r}).then((function(e){201===e.statusCode?t(null,{fileID:d,requestId:f}):t(new ce({code:"STORAGE_REQUEST_FAIL",message:"STORAGE_REQUEST_FAIL: ".concat(e.data)}))})).catch((function(e){t(e)}))})).catch((function(e){t(e)})),t.promise},vt=function(e,t){t=t||Ce();var a=st(this.config.env),n=e.cloudPath;return a.send("storage.getUploadMetadata",{path:n}).then((function(e){t(null,e)})).catch((function(e){t(e)})),t.promise},mt=function(e,t){var a=e.fileList;if(t=t||Ce(),!a||!Array.isArray(a))return{code:"INVALID_PARAM",message:"fileList必须是非空的数组"};var n,i=(0,l.default)(a);try{for(i.s();!(n=i.n()).done;){var r=n.value;if(!r||"string"!=typeof r)return{code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"}}}catch(s){i.e(s)}finally{i.f()}var o={fileid_list:a};return st(this.config.env).send("storage.batchDeleteFile",o).then((function(e){e.code?t(null,e):t(null,{fileList:e.data.delete_list,requestId:e.requestId})})).catch((function(e){t(e)})),t.promise},yt=function(e,t){var a=e.fileList;t=t||Ce(),a&&Array.isArray(a)||t(null,{code:"INVALID_PARAM",message:"fileList必须是非空的数组"});var n,i=[],r=(0,l.default)(a);try{for(r.s();!(n=r.n()).done;){var s=n.value;"object"==(0,o.default)(s)?(s.hasOwnProperty("fileID")&&s.hasOwnProperty("maxAge")||t(null,{code:"INVALID_PARAM",message:"fileList的元素必须是包含fileID和maxAge的对象"}),i.push({fileid:s.fileID,max_age:s.maxAge})):"string"==typeof s?i.push({fileid:s}):t(null,{code:"INVALID_PARAM",message:"fileList的元素必须是字符串"})}}catch(u){r.e(u)}finally{r.f()}var c={file_list:i};return st(this.config.env).send("storage.batchGetDownloadUrl",c).then((function(e){e.code?t(null,e):t(null,{fileList:e.data.download_list,requestId:e.requestId})})).catch((function(e){t(e)})),t.promise},bt=function(){var e=(0,u.default)((0,c.default)().mark((function e(t,a){var n,i,r,o;return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n=t.fileID,e.next=3,yt.call(this,{fileList:[{fileID:n,maxAge:600}]});case 3:if(i=e.sent.fileList[0],"SUCCESS"===i.code){e.next=6;break}return e.abrupt("return",a?a(i):new Promise((function(e){e(i)})));case 6:if(r=st(this.config.env),o=i.download_url,o=encodeURI(o),a){e.next=10;break}return e.abrupt("return",r.download({url:o}));case 10:return e.t0=a,e.next=13,r.download({url:o});case 13:e.t1=e.sent,(0,e.t0)(e.t1);case 15:case"end":return e.stop()}}),e,this)})));return function(t,a){return e.apply(this,arguments)}}(),wt=function(e,t){var a,n=e.name,i=e.data,r=e.query,o=e.parse,s=e.search,l=e.timeout,c=t||Ce();try{a=i?JSON.stringify(i):""}catch(n){return Promise.reject(n)}if(!n)return Promise.reject(new ce({code:"PARAM_ERROR",message:"函数名不能为空"}));var u={inQuery:r,parse:o,search:s,function_name:n,request_data:a};return st(this.config.env).send("functions.invokeFunction",u,{timeout:l}).then((function(e){if(e.code)c(null,e);else{var t=e.data.response_data;if(o)c(null,{result:t,requestId:e.requestId});else try{t=JSON.parse(e.data.response_data),c(null,{result:t,requestId:e.requestId})}catch(e){c(new ce({message:"response data must be json"}))}}return c.promise})).catch((function(e){c(e)})),c.promise},St={timeout:15e3,persistence:"session"},kt={},At=function(){function e(t){(0,g.default)(this,e),this.config=t||this.config,this.authObj=void 0}return(0,x.default)(e,[{key:"init",value:function(t){switch(Oe.adapter||(this.requestClient=new Oe.adapter.reqClass({timeout:t.timeout||5e3,timeoutMsg:"请求在".concat((t.timeout||5e3)/1e3,"s内未完成，已中断")})),this.config=(0,d.default)((0,d.default)({},St),t),!0){case this.config.timeout>6e5:console.warn("timeout大于可配置上限[10分钟]，已重置为上限数值"),this.config.timeout=6e5;break;case this.config.timeout<100:console.warn("timeout小于可配置下限[100ms]，已重置为下限数值"),this.config.timeout=100}return new e(this.config)}},{key:"auth",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.persistence;if(this.authObj)return this.authObj;var a,n=t||Oe.adapter.primaryStorage||St.persistence;return n!==this.config.persistence&&(this.config.persistence=n),function(e){var t=e.env;Ne[t]=new Be(e),Ue[t]=new Be((0,d.default)((0,d.default)({},e),{},{persistence:"local"}))}(this.config),a=this.config,ot[a.env]=new rt(a),this.authObj=new gt(this.config),this.authObj}},{key:"on",value:function(e,t){return Ge.apply(this,[e,t])}},{key:"off",value:function(e,t){return Je.apply(this,[e,t])}},{key:"callFunction",value:function(e,t){return wt.apply(this,[e,t])}},{key:"deleteFile",value:function(e,t){return mt.apply(this,[e,t])}},{key:"getTempFileURL",value:function(e,t){return yt.apply(this,[e,t])}},{key:"downloadFile",value:function(e,t){return bt.apply(this,[e,t])}},{key:"uploadFile",value:function(e,t){return xt.apply(this,[e,t])}},{key:"getUploadMetadata",value:function(e,t){return vt.apply(this,[e,t])}},{key:"registerExtension",value:function(e){kt[e.name]=e}},{key:"invokeExtension",value:function(){var e=(0,u.default)((0,c.default)().mark((function e(t,a){var n;return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(n=kt[t],n){e.next=3;break}throw new ce({message:"扩展".concat(t," 必须先注册")});case 3:return e.next=5,n.invoke(a,this);case 5:return e.abrupt("return",e.sent);case 6:case"end":return e.stop()}}),e,this)})));return function(t,a){return e.apply(this,arguments)}}()},{key:"useAdapters",value:function(e){var t=function(e){var t,a,n=(t=e,"[object Array]"===Object.prototype.toString.call(t)?e:[e]),i=(0,l.default)(n);try{for(i.s();!(a=i.n()).done;){var r=a.value,o=r.isMatch,s=r.genAdapter,c=r.runtime;if(o())return{adapter:s(),runtime:c}}}catch(u){i.e(u)}finally{i.f()}}(e)||{},a=t.adapter,n=t.runtime;a&&(Oe.adapter=a),n&&(Oe.runtime=n)}}]),e}(),Tt=new At;function _t(e,t,a){void 0===a&&(a={});var n=/\?/.test(t),i="";for(var r in a)""===i?!n&&(t+="?"):i+="&",i+=r+"="+encodeURIComponent(a[r]);return/^http(s)?:\/\//.test(t+=i)?t:""+e+t}var Pt=function(){function e(){(0,g.default)(this,e)}return(0,x.default)(e,[{key:"get",value:function(e){var t=e.url,a=e.data,n=e.headers,i=e.timeout;return new Promise((function(e,r){he.request({url:_t("https:",t),data:a,method:"GET",header:n,timeout:i,success:function(t){e(t)},fail:function(e){r(e)}})}))}},{key:"post",value:function(e){var t=e.url,a=e.data,n=e.headers,i=e.timeout;return new Promise((function(e,r){he.request({url:_t("https:",t),data:a,method:"POST",header:n,timeout:i,success:function(t){e(t)},fail:function(e){r(e)}})}))}},{key:"upload",value:function(e){return new Promise((function(t,a){var n=e.url,i=e.file,r=e.data,o=e.headers,s=e.fileType,l=he.uploadFile({url:_t("https:",n),name:"file",formData:Object.assign({},r),filePath:i,fileType:s,header:o,success:function(e){var a={statusCode:e.statusCode,data:e.data||{}};200===e.statusCode&&r.success_action_status&&(a.statusCode=parseInt(r.success_action_status,10)),t(a)},fail:function(e){a(new Error(e.errMsg||"uploadFile:fail"))}});"function"==typeof e.onUploadProgress&&l&&"function"==typeof l.onProgressUpdate&&l.onProgressUpdate((function(t){e.onUploadProgress({loaded:t.totalBytesSent,total:t.totalBytesExpectedToSend})}))}))}}]),e}(),Ct={setItem:function(e,t){he.setStorageSync(e,t)},getItem:function(e){return he.getStorageSync(e)},removeItem:function(e){he.removeStorageSync(e)},clear:function(){he.clearStorageSync()}},Dt={genAdapter:function(){return{root:{},reqClass:Pt,localStorage:Ct,primaryStorage:"local"}},isMatch:function(){return!0},runtime:"uni_app"};Tt.useAdapters(Dt);var Mt=Tt,It=Mt.init;Mt.init=function(e){e.env=e.spaceId;var t=It.call(this,e);t.config.provider="tencent",t.config.spaceId=e.spaceId;var a=t.auth;return t.auth=function(e){var t=a.call(this,e);return["linkAndRetrieveDataWithTicket","signInAnonymously","signOut","getAccessToken","getLoginState","signInWithTicket","getUserInfo"].forEach((function(e){var a;t[e]=(a=t[e],function(e){e=e||{};var t=le(e),n=t.success,i=t.fail,r=t.complete;if(!(n||i||r))return a.call(this,e);a.call(this,e).then((function(e){n&&n(e),r&&r(e)}),(function(e){i&&i(e),r&&r(e)}))}).bind(t)})),t},t.customAuth=t.auth,t};var Lt=Mt;function Ot(e,t){return Ft.apply(this,arguments)}function Ft(){return Ft=(0,u.default)((0,c.default)().mark((function e(t,a){var n,i,r;return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n="http://".concat(t,":").concat(a,"/system/ping"),e.prev=1,e.next=4,r={url:n,timeout:500},new Promise((function(e,t){he.request((0,d.default)((0,d.default)({},r),{},{success:function(t){e(t)},fail:function(e){t(e)}}))}));case 4:return i=e.sent,e.abrupt("return",!(!i.data||0!==i.data.code));case 8:return e.prev=8,e.t0=e["catch"](1),e.abrupt("return",!1);case 11:case"end":return e.stop()}}),e,null,[[1,8]])}))),Ft.apply(this,arguments)}function Et(e,t){return Rt.apply(this,arguments)}function Rt(){return Rt=(0,u.default)((0,c.default)().mark((function e(t,a){var n,i,r;return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:i=0;case 1:if(!(i<t.length)){e.next=11;break}return r=t[i],e.next=5,Ot(r,a);case 5:if(!e.sent){e.next=8;break}return n=r,e.abrupt("break",11);case 8:i++,e.next=1;break;case 11:return e.abrupt("return",{address:n,port:a});case 12:case"end":return e.stop()}}),e)}))),Rt.apply(this,arguments)}var Bt={"serverless.file.resource.generateProximalSign":"storage/generate-proximal-sign","serverless.file.resource.report":"storage/report","serverless.file.resource.delete":"storage/delete","serverless.file.resource.getTempFileURL":"storage/get-temp-file-url"},Nt=function(){function e(t){if((0,g.default)(this,e),["spaceId","clientSecret"].forEach((function(e){if(!Object.prototype.hasOwnProperty.call(t,e))throw new Error("".concat(e," required"))})),!t.endpoint)throw new Error("集群空间未配置ApiEndpoint，配置后需要重新关联服务空间后生效");this.config=Object.assign({},t),this.config.provider="dcloud",this.config.requestUrl=this.config.endpoint+"/client",this.config.envType=this.config.envType||"public",this.adapter=he}return(0,x.default)(e,[{key:"request",value:function(){var e=(0,u.default)((0,c.default)().mark((function e(t){var a,n=this,i=arguments;return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!(i.length>1&&void 0!==i[1])||i[1],a=!1,!a){e.next=8;break}return e.next=5,this.setupLocalRequest(t);case 5:e.t0=e.sent,e.next=9;break;case 8:e.t0=this.setupRequest(t);case 9:return t=e.t0,e.abrupt("return",Promise.resolve().then((function(){return a?n.requestLocal(t):ye.wrappedRequest(t,n.adapter.request)})));case 11:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"requestLocal",value:function(e){var t=this;return new Promise((function(a,n){t.adapter.request(Object.assign(e,{complete:function(e){if(e||(e={}),!e.statusCode||e.statusCode>=400){var t=e.data&&e.data.code||"SYS_ERR",i=e.data&&e.data.message||"request:fail";return n(new ce({code:t,message:i}))}a({success:!0,result:e.data})}}))}))}},{key:"setupRequest",value:function(e){var t=Object.assign({},e,{spaceId:this.config.spaceId,timestamp:Date.now()}),a={"Content-Type":"application/json"};a["x-serverless-sign"]=ye.sign(t,this.config.clientSecret);var n=ve();a["x-client-info"]=encodeURIComponent(JSON.stringify(n));var i=fe(),r=i.token;return a["x-client-token"]=r,{url:this.config.requestUrl,method:"POST",data:t,dataType:"json",header:JSON.parse(JSON.stringify(a))}}},{key:"setupLocalRequest",value:function(){var e=(0,u.default)((0,c.default)().mark((function e(t){var a,n,i,r,o,s,l,u,d;return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return a=ve(),n=fe(),i=n.token,r=Object.assign({},t,{spaceId:this.config.spaceId,timestamp:Date.now(),clientInfo:a,token:i}),o=this.__dev__&&this.__dev__.debugInfo||{},s=o.address,l=o.servePort,e.next=9,Et(s,l);case 9:return u=e.sent,d=u.address,e.abrupt("return",{url:"http://".concat(d,":").concat(l,"/").concat(Bt[t.method]),method:"POST",data:r,dataType:"json",header:JSON.parse(JSON.stringify({"Content-Type":"application/json"}))});case 12:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"callFunction",value:function(e){var t={method:"serverless.function.runtime.invoke",params:JSON.stringify({functionTarget:e.name,functionArgs:e.data||{}})};return this.request(t,!1)}},{key:"getUploadFileOptions",value:function(e){var t={method:"serverless.file.resource.generateProximalSign",params:JSON.stringify(e)};return this.request(t)}},{key:"reportUploadFile",value:function(e){var t={method:"serverless.file.resource.report",params:JSON.stringify(e)};return this.request(t)}},{key:"uploadFile",value:function(e){var t,a=this,n=e.filePath,i=e.cloudPath,r=e.fileType,o=void 0===r?"image":r,s=e.onUploadProgress;if(!i)throw new ce({code:"CLOUDPATH_REQUIRED",message:"cloudPath不可为空"});return this.getUploadFileOptions({cloudPath:i}).then((function(e){var i=e.result,r=i.url,l=i.formData,c=i.name;return t=e.result.fileUrl,new Promise((function(e,t){var i=a.adapter.uploadFile({url:r,formData:l,name:c,filePath:n,fileType:o,success:function(a){a&&a.statusCode<400?e(a):t(new ce({code:"UPLOAD_FAILED",message:"文件上传失败"}))},fail:function(e){t(new ce({code:e.code||"UPLOAD_FAILED",message:e.message||e.errMsg||"文件上传失败"}))}});"function"==typeof s&&i&&"function"==typeof i.onProgressUpdate&&i.onProgressUpdate((function(e){s({loaded:e.totalBytesSent,total:e.totalBytesExpectedToSend})}))}))})).then((function(){return a.reportUploadFile({cloudPath:i})})).then((function(e){return new Promise((function(a,i){e.success?a({success:!0,filePath:n,fileID:t}):i(new ce({code:"UPLOAD_FAILED",message:"文件上传失败"}))}))}))}},{key:"deleteFile",value:function(e){var t=e.fileList,a={method:"serverless.file.resource.delete",params:JSON.stringify({fileList:t})};return this.request(a).then((function(e){if(e.success)return e.result;throw new ce({code:"DELETE_FILE_FAILED",message:"删除文件失败"})}))}},{key:"getTempFileURL",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.fileList,a=e.maxAge;if(!Array.isArray(t)||0===t.length)throw new ce({code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"});var n={method:"serverless.file.resource.getTempFileURL",params:JSON.stringify({fileList:t,maxAge:a})};return this.request(n).then((function(e){if(e.success)return{fileList:e.result.fileList.map((function(e){return{fileID:e.fileID,tempFileURL:e.tempFileURL}}))};throw new ce({code:"GET_TEMP_FILE_URL_FAILED",message:"获取临时文件链接失败"})}))}}]),e}(),Ut={init:function(e){var t=new Nt(e),a={signInAnonymously:function(){return Promise.resolve()},getLoginState:function(){return Promise.resolve(!1)}};return t.auth=function(){return a},t.customAuth=t.auth,t}},zt=m((function(e,t){e.exports=b.enc.Hex}));function Wt(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){var t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)}))}function jt(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},a=t.data,n=t.functionName,i=t.method,o=t.headers,s=t.signHeaderKeys,l=void 0===s?[]:s,c=t.config,u=String(Date.now()),d=Wt(),h=Object.assign({},o,{"x-from-app-id":c.spaceAppId,"x-from-env-id":c.spaceId,"x-to-env-id":c.spaceId,"x-from-instance-id":u,"x-from-function-name":n,"x-client-timestamp":u,"x-alipay-source":"client","x-request-id":d,"x-alipay-callid":d,"x-trace-id":d}),f=["x-from-app-id","x-from-env-id","x-to-env-id","x-from-instance-id","x-from-function-name","x-client-timestamp"].concat(l),p=e.split("?")||[],g=(0,r.default)(p,2),x=g[0],v=void 0===x?"":x,m=g[1],y=void 0===m?"":m,b=function(e){var t="HMAC-SHA256",a=e.signedHeaders.join(";"),n=e.signedHeaders.map((function(t){return"".concat(t.toLowerCase(),":").concat(e.headers[t],"\n")})).join(""),i=_e(e.body).toString(zt),r="".concat(e.method.toUpperCase(),"\n").concat(e.path,"\n").concat(e.query,"\n").concat(n,"\n").concat(a,"\n").concat(i,"\n"),o=_e(r).toString(zt),s="".concat(t,"\n").concat(e.timestamp,"\n").concat(o,"\n"),l=Pe(s,e.secretKey).toString(zt);return"".concat(t," Credential=").concat(e.secretId,", SignedHeaders=").concat(a,", Signature=").concat(l)}({path:v,query:y,method:i,headers:h,timestamp:u,body:JSON.stringify(a),secretId:c.accessKey,secretKey:c.secretKey,signedHeaders:f.sort()});return{url:"".concat(c.endpoint).concat(e),headers:Object.assign({},h,{Authorization:b})}}function qt(e){var t=e.url,a=e.data,n=e.method,i=void 0===n?"POST":n,r=e.headers,s=void 0===r?{}:r,l=e.timeout;return new Promise((function(e,n){he.request({url:t,method:i,data:"object"==(0,o.default)(a)?JSON.stringify(a):a,header:s,dataType:"json",timeout:l,complete:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},a=s["x-trace-id"]||"";if(!t.statusCode||t.statusCode>=400){var i=t.data||{},r=i.message,o=i.errMsg,l=i.trace_id;return n(new ce({code:"SYS_ERR",message:r||o||"request:fail",requestId:l||a}))}e({status:t.statusCode,data:t.data,headers:t.header,requestId:a})}})}))}function Gt(e,t){var a=e.path,n=e.data,i=e.method,r=void 0===i?"GET":i,o=jt(a,{functionName:"",data:n,method:r,headers:{"x-alipay-cloud-mode":"oss","x-data-api-type":"oss","x-expire-timestamp":Date.now()+6e4},signHeaderKeys:["x-data-api-type","x-expire-timestamp"],config:t}),s=o.url,l=o.headers;return qt({url:s,data:n,method:r,headers:l}).then((function(e){var t=e.data||{};if(!t.success)throw new ce({code:e.errCode,message:e.errMsg,requestId:e.requestId});return t.data||{}})).catch((function(e){throw new ce({code:e.errCode,message:e.errMsg,requestId:e.requestId})}))}function Ht(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=e.trim().replace(/^cloud:\/\//,""),a=t.indexOf("/");if(a<=0)throw new ce({code:"INVALID_PARAM",message:"fileID不合法"});var n=t.substring(0,a),i=t.substring(a+1);return n!==this.config.spaceId&&console.warn("file ".concat(e," does not belong to env ").concat(this.config.spaceId)),i}function Jt(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return"cloud://".concat(this.config.spaceId,"/").concat(e.replace(/^\/+/,""))}var Yt=function(){function e(t){(0,g.default)(this,e),this.config=t}return(0,x.default)(e,[{key:"signedURL",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},a="/ws/function/".concat(e),n=this.config.wsEndpoint.replace(/^ws(s)?:\/\//,""),i=Object.assign({},t,{accessKeyId:this.config.accessKey,signatureNonce:Wt(),timestamp:""+Date.now()}),r=[a,["accessKeyId","authorization","signatureNonce","timestamp"].sort().map((function(e){return i[e]?"".concat(e,"=").concat(i[e]):null})).filter(Boolean).join("&"),"host:".concat(n)].join("\n"),o=["HMAC-SHA256",_e(r).toString(zt)].join("\n"),s=Pe(o,this.config.secretKey).toString(zt),l=Object.keys(i).map((function(e){return"".concat(e,"=").concat(encodeURIComponent(i[e]))})).join("&");return"".concat(this.config.wsEndpoint).concat(a,"?").concat(l,"&signature=").concat(s)}}]),e}(),Kt=function(){function e(t){if((0,g.default)(this,e),["spaceId","spaceAppId","accessKey","secretKey"].forEach((function(e){if(!Object.prototype.hasOwnProperty.call(t,e))throw new Error("".concat(e," required"))})),t.endpoint){if("string"!=typeof t.endpoint)throw new Error("endpoint must be string");if(!/^https:\/\//.test(t.endpoint))throw new Error("endpoint must start with https://");t.endpoint=t.endpoint.replace(/\/$/,"")}this.config=Object.assign({},t,{endpoint:t.endpoint||"https://".concat(t.spaceId,".api-hz.cloudbasefunction.cn"),wsEndpoint:t.wsEndpoint||"wss://".concat(t.spaceId,".api-hz.cloudbasefunction.cn")}),this._websocket=new Yt(this.config)}return(0,x.default)(e,[{key:"callFunction",value:function(e){return function(e,t){var a=e.name,n=e.data,i=e.async,r=void 0!==i&&i,o=e.timeout,s="POST",l={"x-to-function-name":a};r&&(l["x-function-invoke-type"]="async");var c=jt("/functions/invokeFunction",{functionName:a,data:n,method:s,headers:l,signHeaderKeys:["x-to-function-name"],config:t}),u=c.url,d=c.headers;return qt({url:u,data:n,method:s,headers:d,timeout:o}).then((function(e){var t=0;if(r){var a=e.data||{};t="200"===a.errCode?0:a.errCode,e.data=a.data||{},e.errMsg=a.errMsg}if(0!==t)throw new ce({code:t,message:e.errMsg,requestId:e.requestId});return{errCode:t,success:0===t,requestId:e.requestId,result:e.data}})).catch((function(e){throw new ce({code:e.errCode,message:e.errMsg,requestId:e.requestId})}))}(e,this.config)}},{key:"uploadFileToOSS",value:function(e){var t=e.url,a=e.filePath,n=e.fileType,i=e.formData,r=e.onUploadProgress;return new Promise((function(e,o){var s=he.uploadFile({url:t,filePath:a,fileType:n,formData:i,name:"file",success:function(t){t&&t.statusCode<400?e(t):o(new ce({code:"UPLOAD_FAILED",message:"文件上传失败"}))},fail:function(e){o(new ce({code:e.code||"UPLOAD_FAILED",message:e.message||e.errMsg||"文件上传失败"}))}});"function"==typeof r&&s&&"function"==typeof s.onProgressUpdate&&s.onProgressUpdate((function(e){r({loaded:e.totalBytesSent,total:e.totalBytesExpectedToSend})}))}))}},{key:"uploadFile",value:function(){var e=(0,u.default)((0,c.default)().mark((function e(t){var a,n,i,r,o,s,l,u,d,h,f;return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(a=t.filePath,n=t.cloudPath,i=void 0===n?"":n,r=t.fileType,o=void 0===r?"image":r,s=t.onUploadProgress,"string"===M(i)){e.next=3;break}throw new ce({code:"INVALID_PARAM",message:"cloudPath必须为字符串类型"});case 3:if(i=i.trim()){e.next=5;break}throw new ce({code:"INVALID_PARAM",message:"cloudPath不可为空"});case 5:if(!/:\/\//.test(i)){e.next=7;break}throw new ce({code:"INVALID_PARAM",message:"cloudPath不合法"});case 7:return e.next=9,Gt({path:"/".concat(i.replace(/^\//,""),"?post_url")},this.config);case 9:return l=e.sent,u=l.file_id,d=l.upload_url,h=l.form_data,f=h&&h.reduce((function(e,t){return e[t.key]=t.value,e}),{}),e.abrupt("return",this.uploadFileToOSS({url:d,filePath:a,fileType:o,formData:f,onUploadProgress:s}).then((function(){return{fileID:u}})));case 15:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"getTempFileURL",value:function(){var e=(0,u.default)((0,c.default)().mark((function e(t){var a,n=this;return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return a=t.fileList,e.abrupt("return",new Promise((function(e,t){(!a||a.length<0)&&e({code:"INVALID_PARAM",message:"fileList不能为空数组"}),a.length>50&&e({code:"INVALID_PARAM",message:"fileList数组长度不能超过50"});var i,r=[],o=(0,l.default)(a);try{for(o.s();!(i=o.n()).done;){var s=i.value,c=void 0;"string"!==M(s)&&e({code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"});try{c=Ht.call(n,s)}catch(e){console.warn(e.errCode,e.errMsg),c=s}r.push({file_id:c,expire:600})}}catch(u){o.e(u)}finally{o.f()}Gt({path:"/?download_url",data:{file_list:r},method:"POST"},n.config).then((function(t){var a=t.file_list,i=void 0===a?[]:a;e({fileList:i.map((function(e){return{fileID:Jt.call(n,e.file_id),tempFileURL:e.download_url}}))})})).catch((function(e){return t(e)}))})));case 2:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()},{key:"connectWebSocket",value:function(){var e=(0,u.default)((0,c.default)().mark((function e(t){var a,n;return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return a=t.name,n=t.query,e.abrupt("return",he.connectSocket({url:this._websocket.signedURL(a,n),complete:function(){}}));case 2:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()}]),e}(),Xt={init:function(e){e.provider="alipay";var t=new Kt(e);return t.auth=function(){return{signInAnonymously:function(){return Promise.resolve()},getLoginState:function(){return Promise.resolve(!0)}}},t}};function Vt(e){var t,a=e.data;t=ve();var n=JSON.parse(JSON.stringify(a||{}));if(Object.assign(n,{clientInfo:t}),!n.uniIdToken){var i=fe(),r=i.token;r&&(n.uniIdToken=r)}return n}var Qt=[{rule:/fc_function_not_found|FUNCTION_NOT_FOUND/,content:"，云函数[{functionName}]在云端不存在，请检查此云函数名称是否正确以及该云函数是否已上传到服务空间",mode:"append"}],Zt=/[\\^$.*+?()[\]{}|]/g,$t=RegExp(Zt.source);function ea(e,t,a){return e.replace(new RegExp((n=t)&&$t.test(n)?n.replace(Zt,"\\$&"):n,"g"),a);var n}var ta={NONE:"none",REQUEST:"request",RESPONSE:"response",BOTH:"both"},aa="_globalUniCloudStatus",na="_globalUniCloudSecureNetworkCache__{spaceId}";var ia;ia="0123456789abcdef";var ra="uni-secure-network",oa={SYSTEM_ERROR:{code:2e4,message:"System error"},APP_INFO_INVALID:{code:20101,message:"Invalid client"},GET_ENCRYPT_KEY_FAILED:{code:20102,message:"Get encrypt key failed"}};function sa(e){var t=e||{},a=t.errSubject,n=t.subject,i=t.errCode,r=t.errMsg,o=t.code,s=t.message,l=t.cause;return new ce({subject:a||n||ra,code:i||o||oa.SYSTEM_ERROR.code,message:r||s,cause:l})}var la;function ca(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.secretType;return t===ta.REQUEST||t===ta.RESPONSE||t===ta.BOTH}function ua(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.name,a=e.data,n=void 0===a?{}:a;return"app"===z&&"DCloud-clientDB"===t&&"encryption"===n.redirectTo&&"getAppClientKey"===n.action}function da(e){e.functionName,e.result,e.logPvd}function ha(e){var t=e.callFunction,a=function(a){var n=this,i=a.name;a.data=Vt.call(e,{data:a.data});var r={aliyun:"aliyun",tencent:"tcb",tcb:"tcb",alipay:"alipay",dcloud:"dcloud"}[this.config.provider],o=ca(a),s=ua(a),l=o||s;return t.call(this,a).then((function(e){return e.errCode=0,!l&&da.call(n,{functionName:i,result:e,logPvd:r}),Promise.resolve(e)}),(function(e){return!l&&da.call(n,{functionName:i,result:e,logPvd:r}),e&&e.message&&(e.message=function(){for(var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.message,a=void 0===t?"":t,n=e.extraInfo,i=void 0===n?{}:n,r=e.formatter,o=void 0===r?[]:r,s=0;s<o.length;s++){var l=o[s],c=l.rule,u=l.content,d=l.mode,h=a.match(c);if(h){for(var f=u,p=1;p<h.length;p++)f=ea(f,"{$".concat(p,"}"),h[p]);for(var g in i)f=ea(f,"{".concat(g,"}"),i[g]);return"replace"===d?f:a+f}}return a}({message:"[".concat(a.name,"]: ").concat(e.message),formatter:Qt,extraInfo:{functionName:i}})),Promise.reject(e)}))};e.callFunction=function(t){var n,i,r=e.config,o=r.provider,s=r.spaceId,l=t.name;return t.data=t.data||{},n=a,n=n.bind(e),i=ua(t)?a.call(e,t):function(e){var t=e.name,a=e.data,n=void 0===a?{}:a;return"mp-weixin"===z&&"uni-id-co"===t&&"secureNetworkHandshakeByWeixin"===n.method}(t)?n.call(e,t):ca(t)?new la({secretType:t.secretType,uniCloudIns:e}).wrapEncryptDataCallFunction(a.bind(e))(t):function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.provider,a=e.spaceId,n=e.functionName,i=ge(),r=i.appId,o=i.uniPlatform,s=i.osName,l=o;"app"===o&&(l=s);var c=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.provider,a=e.spaceId,n=U;if(!n)return{};t=function(e){return"tencent"===e?"tcb":e}(t);var i=n.find((function(e){return e.provider===t&&e.spaceId===a}));return i&&i.config}({provider:t,spaceId:a});if(!c||!c.accessControl||!c.accessControl.enable)return!1;var u=c.accessControl.function||{},d=Object.keys(u);if(0===d.length)return!0;var h=function(e,t){for(var a,n,i,r=0;r<e.length;r++){var o=e[r];o!==t?"*"!==o?o.split(",").map((function(e){return e.trim()})).indexOf(t)>-1&&(n=o):i=o:a=o}return a||n||i}(d,n);if(!h)return!1;if((u[h]||[]).find((function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return e.appId===r&&(e.platform||"").toLowerCase()===l.toLowerCase()})))return!0;throw console.error("此应用[appId: ".concat(r,", platform: ").concat(l,"]不在云端配置的允许访问的应用列表内，参考：https://uniapp.dcloud.net.cn/uniCloud/secure-network.html#verify-client")),sa(oa.APP_INFO_INVALID)}({provider:o,spaceId:s,functionName:l})?new la({secretType:t.secretType,uniCloudIns:e}).wrapVerifyClientCallFunction(a.bind(e))(t):n(t),Object.defineProperty(i,"result",{get:function(){return console.warn("当前返回结果为Promise类型，不可直接访问其result属性，详情请参考：https://uniapp.dcloud.net.cn/uniCloud/faq?id=promise"),{}}}),i.then((function(e){return"undefined"!=typeof UTSJSONObject&&(e.result=new UTSJSONObject(e.result)),e}))}}la="mp-weixin"!==z&&"app"!==z?function(){return(0,x.default)((function e(){throw(0,g.default)(this,e),sa({message:"Platform ".concat(z," is not supported by secure network")})}))}():function(){return(0,x.default)((function e(){throw(0,g.default)(this,e),sa({message:"Platform ".concat(z," is not enabled, please check whether secure network module is enabled in your manifest.json")})}))}();var fa=Symbol("CLIENT_DB_INTERNAL");function pa(e,t){return e.then="DoNotReturnProxyWithAFunctionNamedThen",e._internalType=fa,e.inspect=null,e.__ob__=void 0,new Proxy(e,{get:function(e,a,n){if("_uniClient"===a)return null;if("symbol"==(0,o.default)(a))return e[a];if(a in e||"string"!=typeof a){var i=e[a];return"function"==typeof i?i.bind(e):i}return t.get(e,a,n)}})}function ga(e){return{on:function(t,a){e[t]=e[t]||[],e[t].indexOf(a)>-1||e[t].push(a)},off:function(t,a){e[t]=e[t]||[];var n=e[t].indexOf(a);-1!==n&&e[t].splice(n,1)}}}var xa=["db.Geo","db.command","command.aggregate"];function va(e,t){return xa.indexOf("".concat(e,".").concat(t))>-1}function ma(e){switch(M(e)){case"array":return e.map((function(e){return ma(e)}));case"object":return e._internalType===fa||Object.keys(e).forEach((function(t){e[t]=ma(e[t])})),e;case"regexp":return{$regexp:{source:e.source,flags:e.flags}};case"date":return{$date:e.toISOString()};default:return e}}function ya(e){return e&&e.content&&e.content.$method}var ba=function(){function e(t,a,n){(0,g.default)(this,e),this.content=t,this.prevStage=a||null,this.udb=null,this._database=n}return(0,x.default)(e,[{key:"toJSON",value:function(){for(var e=this,t=[e.content];e.prevStage;)e=e.prevStage,t.push(e.content);return{$db:t.reverse().map((function(e){return{$method:e.$method,$param:ma(e.$param)}}))}}},{key:"toString",value:function(){return JSON.stringify(this.toJSON())}},{key:"getAction",value:function(){var e=this.toJSON().$db.find((function(e){return"action"===e.$method}));return e&&e.$param&&e.$param[0]}},{key:"getCommand",value:function(){return{$db:this.toJSON().$db.filter((function(e){return"action"!==e.$method}))}}},{key:"isAggregate",get:function(){for(var e=this;e;){var t=ya(e),a=ya(e.prevStage);if("aggregate"===t&&"collection"===a||"pipeline"===t)return!0;e=e.prevStage}return!1}},{key:"isCommand",get:function(){for(var e=this;e;){if("command"===ya(e))return!0;e=e.prevStage}return!1}},{key:"isAggregateCommand",get:function(){for(var e=this;e;){var t=ya(e),a=ya(e.prevStage);if("aggregate"===t&&"command"===a)return!0;e=e.prevStage}return!1}},{key:"getNextStageFn",value:function(e){var t=this;return function(){return wa({$method:e,$param:ma(Array.from(arguments))},t,t._database)}}},{key:"count",get:function(){return this.isAggregate?this.getNextStageFn("count"):function(){return this._send("count",Array.from(arguments))}}},{key:"remove",get:function(){return this.isCommand?this.getNextStageFn("remove"):function(){return this._send("remove",Array.from(arguments))}}},{key:"get",value:function(){return this._send("get",Array.from(arguments))}},{key:"add",get:function(){return this.isCommand?this.getNextStageFn("add"):function(){return this._send("add",Array.from(arguments))}}},{key:"update",value:function(){return this._send("update",Array.from(arguments))}},{key:"end",value:function(){return this._send("end",Array.from(arguments))}},{key:"set",get:function(){return this.isCommand?this.getNextStageFn("set"):function(){throw new Error("JQL禁止使用set方法")}}},{key:"_send",value:function(e,t){var a=this.getAction(),n=this.getCommand();return n.$db.push({$method:e,$param:ma(t)}),this._database._callCloudFunction({action:a,command:n})}}]),e}();function wa(e,t,a){return pa(new ba(e,t,a),{get:function(e,t){var n="db";return e&&e.content&&(n=e.content.$method),va(n,t)?wa({$method:t},e,a):function(){return wa({$method:t,$param:ma(Array.from(arguments))},e,a)}}})}function Sa(e){var t=e.path,a=e.method;return function(){function e(){(0,g.default)(this,e),this.param=Array.from(arguments)}return(0,x.default)(e,[{key:"toJSON",value:function(){return{$newDb:[].concat((0,s.default)(t.map((function(e){return{$method:e}}))),[{$method:a,$param:this.param}])}}},{key:"toString",value:function(){return JSON.stringify(this.toJSON())}}]),e}()}var ka=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},a=t.uniClient,n=void 0===a?{}:a,i=t.isJQL,r=void 0!==i&&i;(0,g.default)(this,e),this._uniClient=n,this._authCallBacks={},this._dbCallBacks={},n._isDefault&&(this._dbCallBacks=G("_globalUniCloudDatabaseCallback")),r||(this.auth=ga(this._authCallBacks)),this._isJQL=r,Object.assign(this,ga(this._dbCallBacks)),this.env=pa({},{get:function(e,t){return{$env:t}}}),this.Geo=pa({},{get:function(e,t){return Sa({path:["Geo"],method:t})}}),this.serverDate=Sa({path:[],method:"serverDate"}),this.RegExp=Sa({path:[],method:"RegExp"})}return(0,x.default)(e,[{key:"getCloudEnv",value:function(e){if("string"!=typeof e||!e.trim())throw new Error("getCloudEnv参数错误");return{$env:e.replace("$cloudEnv_","")}}},{key:"_callback",value:function(e,t){var a=this._dbCallBacks;a[e]&&a[e].forEach((function(e){e.apply(void 0,(0,s.default)(t))}))}},{key:"_callbackAuth",value:function(e,t){var a=this._authCallBacks;a[e]&&a[e].forEach((function(e){e.apply(void 0,(0,s.default)(t))}))}},{key:"multiSend",value:function(){var e=Array.from(arguments),t=e.map((function(e){var t=e.getAction(),a=e.getCommand();if("getTemp"!==a.$db[a.$db.length-1].$method)throw new Error("multiSend只支持子命令内使用getTemp");return{action:t,command:a}}));return this._callCloudFunction({multiCommand:t,queryList:e})}}]),e}();function Aa(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return pa(new e(t),{get:function(e,t){return va("db",t)?wa({$method:t},null,e):function(){return wa({$method:t,$param:ma(Array.from(arguments))},null,e)}}})}var Ta=function(e){(0,h.default)(a,e);var t=(0,f.default)(a);function a(){return(0,g.default)(this,a),t.apply(this,arguments)}return(0,x.default)(a,[{key:"_parseResult",value:function(e){return this._isJQL?e.result:e}},{key:"_callCloudFunction",value:function(e){var t=this,a=e.action,n=e.command,i=e.multiCommand,r=e.queryList;function o(e,t){if(i&&r)for(var a=0;a<r.length;a++){var n=r[a];n.udb&&"function"==typeof n.udb.setResult&&(t?n.udb.setResult(t):n.udb.setResult(e.result.dataList[a]))}}var s=this,l=this._isJQL?"databaseForJQL":"database";function c(e){return s._callback("error",[e]),X(V(l,"fail"),e).then((function(){return X(V(l,"complete"),e)})).then((function(){return o(null,e),ie($.RESPONSE,{type:ee.CLIENT_DB,content:e}),Promise.reject(e)}))}var u=X(V(l,"invoke")),d=this._uniClient;return u.then((function(){return d.callFunction({name:"DCloud-clientDB",type:_.CLIENT_DB,data:{action:a,command:n,multiCommand:i}})})).then((function(e){var a=e.result,n=a.code,i=a.message,r=a.token,u=a.tokenExpired,d=a.systemInfo,h=void 0===d?[]:d;if(h)for(var f=0;f<h.length;f++){var p=h[f],g=p.level,x=p.message,v=p.detail,m="[System Info]"+x;v&&(m="".concat(m,"\n详细信息：").concat(v)),(console["app"===z&&"warn"===g?"error":g]||console.log)(m)}if(n)return c(new ce({code:n,message:i,requestId:e.requestId}));e.result.errCode=e.result.errCode||e.result.code,e.result.errMsg=e.result.errMsg||e.result.message,r&&u&&(pe({token:r,tokenExpired:u}),t._callbackAuth("refreshToken",[{token:r,tokenExpired:u}]),t._callback("refreshToken",[{token:r,tokenExpired:u}]),ie($.REFRESH_TOKEN,{token:r,tokenExpired:u}));for(var y=[{prop:"affectedDocs",tips:"affectedDocs不再推荐使用，请使用inserted/deleted/updated/data.length替代"},{prop:"code",tips:"code不再推荐使用，请使用errCode替代"},{prop:"message",tips:"message不再推荐使用，请使用errMsg替代"}],b=function(t){var a=y[t],n=a.prop,i=a.tips;if(n in e.result){var r=e.result[n];Object.defineProperty(e.result,n,{get:function(){return console.warn(i),r}})}},w=0;w<y.length;w++)b(w);return function(e){return X(V(l,"success"),e).then((function(){return X(V(l,"complete"),e)})).then((function(){o(e,null);var t=s._parseResult(e);return ie($.RESPONSE,{type:ee.CLIENT_DB,content:t}),Promise.resolve(t)}))}(e)}),(function(e){return/fc_function_not_found|FUNCTION_NOT_FOUND/g.test(e.message)&&console.warn("clientDB未初始化，请在web控制台保存一次schema以开启clientDB"),c(new ce({code:e.code||"SYSTEM_ERROR",message:e.message,requestId:e.requestId}))}))}}]),a}(ka),_a="token无效，跳转登录页面",Pa="token过期，跳转登录页面",Ca={TOKEN_INVALID_TOKEN_EXPIRED:Pa,TOKEN_INVALID_INVALID_CLIENTID:_a,TOKEN_INVALID:_a,TOKEN_INVALID_WRONG_TOKEN:_a,TOKEN_INVALID_ANONYMOUS_USER:_a},Da={"uni-id-token-expired":Pa,"uni-id-check-token-failed":_a,"uni-id-token-not-exist":_a,"uni-id-check-device-feature-failed":_a};function Ma(e,t){var a="";return a=e?"".concat(e,"/").concat(t):t,a.replace(/^\//,"")}function Ia(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",a=[],n=[];return e.forEach((function(e){!0===e.needLogin?a.push(Ma(t,e.path)):!1===e.needLogin&&n.push(Ma(t,e.path))})),{needLoginPage:a,notNeedLoginPage:n}}function La(e){return e.split("?")[0].replace(/^\//,"")}function Oa(){return function(e){var t=e&&e.$page&&e.$page.fullPath||"";return t?("/"!==t.charAt(0)&&(t="/"+t),t):t}(function(){var e=getCurrentPages();return e[e.length-1]}())}function Fa(){return La(Oa())}function Ea(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!e)return!1;if(!(t&&t.list&&t.list.length))return!1;var a=t.list,n=La(e);return a.some((function(e){return e.pagePath===n}))}var Ra,Ba=!!v.default.uniIdRouter,Na=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:v.default,t=e.pages,a=void 0===t?[]:t,n=e.subPackages,i=void 0===n?[]:n,r=e.uniIdRouter,o=void 0===r?{}:r,l=e.tabBar,c=void 0===l?{}:l,u=o.loginPage,d=o.needLogin,h=void 0===d?[]:d,f=o.resToLogin,p=void 0===f||f,g=Ia(a),x=g.needLoginPage,m=g.notNeedLoginPage,y=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=[],a=[];return e.forEach((function(e){var n=e.root,i=e.pages,r=void 0===i?[]:i,o=Ia(r,n),l=o.needLoginPage,c=o.notNeedLoginPage;t.push.apply(t,(0,s.default)(l)),a.push.apply(a,(0,s.default)(c))})),{needLoginPage:t,notNeedLoginPage:a}}(i),b=y.needLoginPage,w=y.notNeedLoginPage;return{loginPage:u,routerNeedLogin:h,resToLogin:p,needLoginPage:[].concat((0,s.default)(x),(0,s.default)(b)),notNeedLoginPage:[].concat((0,s.default)(m),(0,s.default)(w)),loginPageInTabBar:Ea(u,c)}}(),Ua=Na.loginPage,za=Na.routerNeedLogin,Wa=Na.resToLogin,ja=Na.needLoginPage,qa=Na.notNeedLoginPage,Ga=Na.loginPageInTabBar;if(ja.indexOf(Ua)>-1)throw new Error("Login page [".concat(Ua,'] should not be "needLogin", please check your pages.json'));function Ha(e){var t=Fa();if("/"===e.charAt(0))return e;var a=e.split("?"),n=(0,r.default)(a,2),i=n[0],o=n[1],s=i.replace(/^\//,"").split("/"),l=t.split("/");l.pop();for(var c=0;c<s.length;c++){var u=s[c];".."===u?l.pop():"."!==u&&l.push(u)}return""===l[0]&&l.shift(),"/"+l.join("/")+(o?"?"+o:"")}function Ja(e){var t=La(Ha(e));return!(qa.indexOf(t)>-1)&&(ja.indexOf(t)>-1||za.some((function(t){return function(e,t){return new RegExp(t).test(e)}(e,t)})))}function Ya(e){var t=e.redirect,a=La(t),n=La(Ua);return Fa()!==n&&a!==n}function Ka(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.api,a=e.redirect;if(a&&Ya({redirect:a})){var n=function(e,t){return"/"!==e.charAt(0)&&(e="/"+e),t?e.indexOf("?")>-1?e+"&uniIdRedirectUrl=".concat(encodeURIComponent(t)):e+"?uniIdRedirectUrl=".concat(encodeURIComponent(t)):e}(Ua,a);Ga?"navigateTo"!==t&&"redirectTo"!==t||(t="switchTab"):"switchTab"===t&&(t="navigateTo");var i={navigateTo:uni.navigateTo,redirectTo:uni.redirectTo,switchTab:uni.switchTab,reLaunch:uni.reLaunch};setTimeout((function(){i[t]({url:n})}),0)}}function Xa(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.url,a={abortLoginPageJump:!1,autoToLoginPage:!1},n=function(){var e,t=fe(),a=t.token,n=t.tokenExpired;if(a){if(n<Date.now()){var i="uni-id-token-expired";e={errCode:i,errMsg:Da[i]}}}else{var r="uni-id-check-token-failed";e={errCode:r,errMsg:Da[r]}}return e}();if(Ja(t)&&n){if(n.uniIdRedirectUrl=t,te($.NEED_LOGIN).length>0)return setTimeout((function(){ie($.NEED_LOGIN,n)}),0),a.abortLoginPageJump=!0,a;a.autoToLoginPage=!0}return a}function Va(){!function(){var e=Oa(),t=Xa({url:e}),a=t.abortLoginPageJump,n=t.autoToLoginPage;a||n&&Ka({api:"redirectTo",redirect:e})}();for(var e=["navigateTo","redirectTo","reLaunch","switchTab"],t=function(t){var a=e[t];uni.addInterceptor(a,{invoke:function(e){var t=Xa({url:e.url}),n=t.abortLoginPageJump,i=t.autoToLoginPage;return n?e:i?(Ka({api:a,redirect:Ha(e.url)}),!1):e}})},a=0;a<e.length;a++)t(a)}function Qa(){this.onResponse((function(e){var t=e.type,a=e.content,n=!1;switch(t){case"cloudobject":n=function(e){if("object"!=(0,o.default)(e))return!1;var t=e||{},a=t.errCode;return a in Da}(a);break;case"clientdb":n=function(e){if("object"!=(0,o.default)(e))return!1;var t=e||{},a=t.errCode;return a in Ca}(a)}n&&function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=te($.NEED_LOGIN);se().then((function(){var a=Oa();if(a&&Ya({redirect:a}))return t.length>0?ie($.NEED_LOGIN,Object.assign({uniIdRedirectUrl:a},e)):void(Ua&&Ka({api:"navigateTo",redirect:a}))}))}(a)}))}function Za(e){!function(e){e.onResponse=function(e){ae($.RESPONSE,e)},e.offResponse=function(e){ne($.RESPONSE,e)}}(e),function(e){e.onNeedLogin=function(e){ae($.NEED_LOGIN,e)},e.offNeedLogin=function(e){ne($.NEED_LOGIN,e)},Ba&&(G(aa).needLoginInit||(G(aa).needLoginInit=!0,se().then((function(){Va.call(e)})),Wa&&Qa.call(e)))}(e),function(e){e.onRefreshToken=function(e){ae($.REFRESH_TOKEN,e)},e.offRefreshToken=function(e){ne($.REFRESH_TOKEN,e)}}(e)}var $a="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",en=/^(?:[A-Za-z\d+/]{4})*?(?:[A-Za-z\d+/]{2}(?:==)?|[A-Za-z\d+/]{3}=?)?$/;function tn(){var e,t,a=fe().token||"",n=a.split(".");if(!a||3!==n.length)return{uid:null,role:[],permission:[],tokenExpired:0};try{e=JSON.parse((t=n[1],decodeURIComponent(Ra(t).split("").map((function(e){return"%"+("00"+e.charCodeAt(0).toString(16)).slice(-2)})).join(""))))}catch(a){throw new Error("获取当前用户信息出错，详细错误信息为："+a.message)}return e.tokenExpired=1e3*e.exp,delete e.exp,delete e.iat,e}Ra="function"!=typeof atob?function(e){if(e=String(e).replace(/[\t\n\f\r ]+/g,""),!en.test(e))throw new Error("Failed to execute 'atob' on 'Window': The string to be decoded is not correctly encoded.");var t;e+="==".slice(2-(3&e.length));for(var a,n,i="",r=0;r<e.length;)t=$a.indexOf(e.charAt(r++))<<18|$a.indexOf(e.charAt(r++))<<12|(a=$a.indexOf(e.charAt(r++)))<<6|(n=$a.indexOf(e.charAt(r++))),i+=64===a?String.fromCharCode(t>>16&255):64===n?String.fromCharCode(t>>16&255,t>>8&255):String.fromCharCode(t>>16&255,t>>8&255,255&t);return i}:atob;var an=m((function(e,t){Object.defineProperty(t,"__esModule",{value:!0});var a="chooseAndUploadFile:ok",n="chooseAndUploadFile:fail";function i(e,t){return e.tempFiles.forEach((function(e,a){e.name||(e.name=e.path.substring(e.path.lastIndexOf("/")+1)),t&&(e.fileType=t),e.cloudPath=Date.now()+"_"+a+e.name.substring(e.name.lastIndexOf("."))})),e.tempFilePaths||(e.tempFilePaths=e.tempFiles.map((function(e){return e.path}))),e}function r(e,t,n){var i=n.onChooseFile,r=n.onUploadProgress;return t.then((function(e){if(i){var t=i(e);if(void 0!==t)return Promise.resolve(t).then((function(t){return void 0===t?e:t}))}return e})).then((function(t){return!1===t?{errMsg:a,tempFilePaths:[],tempFiles:[]}:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:5,i=arguments.length>3?arguments[3]:void 0;(t=Object.assign({},t)).errMsg=a;var r=t.tempFiles,o=r.length,s=0;return new Promise((function(a){for(;s<n;)l();function l(){var n=s++;if(n>=o)!r.find((function(e){return!e.url&&!e.errMsg}))&&a(t);else{var c=r[n];e.uploadFile({provider:c.provider,filePath:c.path,cloudPath:c.cloudPath,fileType:c.fileType,cloudPathAsRealPath:c.cloudPathAsRealPath,onUploadProgress:function(e){e.index=n,e.tempFile=c,e.tempFilePath=c.path,i&&i(e)}}).then((function(e){c.url=e.fileID,n<o&&l()})).catch((function(e){c.errMsg=e.errMsg||e.message,n<o&&l()}))}}}))}(e,t,5,r)}))}t.initChooseAndUploadFile=function(e){return function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{type:"all"};return"image"===t.type?r(e,function(e){var t=e.count,a=e.sizeType,r=e.sourceType,o=void 0===r?["album","camera"]:r,s=e.extension;return new Promise((function(e,r){uni.chooseImage({count:t,sizeType:a,sourceType:o,extension:s,success:function(t){e(i(t,"image"))},fail:function(e){r({errMsg:e.errMsg.replace("chooseImage:fail",n)})}})}))}(t),t):"video"===t.type?r(e,function(e){var t=e.camera,a=e.compressed,r=e.maxDuration,o=e.sourceType,s=void 0===o?["album","camera"]:o,l=e.extension;return new Promise((function(e,o){uni.chooseVideo({camera:t,compressed:a,maxDuration:r,sourceType:s,extension:l,success:function(t){var a=t.tempFilePath,n=t.duration,r=t.size,o=t.height,s=t.width;e(i({errMsg:"chooseVideo:ok",tempFilePaths:[a],tempFiles:[{name:t.tempFile&&t.tempFile.name||"",path:a,size:r,type:t.tempFile&&t.tempFile.type||"",width:s,height:o,duration:n,fileType:"video",cloudPath:""}]},"video"))},fail:function(e){o({errMsg:e.errMsg.replace("chooseVideo:fail",n)})}})}))}(t),t):r(e,function(e){var t=e.count,a=e.extension;return new Promise((function(e,r){var o=uni.chooseFile;if("undefined"!=typeof wx&&"function"==typeof wx.chooseMessageFile&&(o=wx.chooseMessageFile),"function"!=typeof o)return r({errMsg:n+" 请指定 type 类型，该平台仅支持选择 image 或 video。"});o({type:"all",count:t,extension:a,success:function(t){e(i(t))},fail:function(e){r({errMsg:e.errMsg.replace("chooseFile:fail",n)})}})}))}(t),t)}}})),nn=function(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}(an),rn={auto:"auto",onready:"onready",manual:"manual"};function on(e){return{props:{localdata:{type:Array,default:function(){return[]}},options:{type:[Object,Array],default:function(){return{}}},spaceInfo:{type:Object,default:function(){return{}}},collection:{type:[String,Array],default:""},action:{type:String,default:""},field:{type:String,default:""},orderby:{type:String,default:""},where:{type:[String,Object],default:""},pageData:{type:String,default:"add"},pageCurrent:{type:Number,default:1},pageSize:{type:Number,default:20},getcount:{type:[Boolean,String],default:!1},gettree:{type:[Boolean,String],default:!1},gettreepath:{type:[Boolean,String],default:!1},startwith:{type:String,default:""},limitlevel:{type:Number,default:10},groupby:{type:String,default:""},groupField:{type:String,default:""},distinct:{type:[Boolean,String],default:!1},foreignKey:{type:String,default:""},loadtime:{type:String,default:"auto"},manual:{type:Boolean,default:!1}},data:function(){return{mixinDatacomLoading:!1,mixinDatacomHasMore:!1,mixinDatacomResData:[],mixinDatacomErrorMessage:"",mixinDatacomPage:{},mixinDatacomError:null}},created:function(){var e=this;this.mixinDatacomPage={current:this.pageCurrent,size:this.pageSize,count:0},this.$watch((function(){var t=[];return["pageCurrent","pageSize","localdata","collection","action","field","orderby","where","getont","getcount","gettree","groupby","groupField","distinct"].forEach((function(a){t.push(e[a])})),t}),(function(t,a){if(e.loadtime!==rn.manual){for(var n=!1,i=[],r=2;r<t.length;r++)t[r]!==a[r]&&(i.push(t[r]),n=!0);t[0]!==a[0]&&(e.mixinDatacomPage.current=e.pageCurrent),e.mixinDatacomPage.size=e.pageSize,e.onMixinDatacomPropsChange(n,i)}}))},methods:{onMixinDatacomPropsChange:function(e,t){},mixinDatacomEasyGet:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},a=t.getone,n=void 0!==a&&a,i=t.success,r=t.fail;this.mixinDatacomLoading||(this.mixinDatacomLoading=!0,this.mixinDatacomErrorMessage="",this.mixinDatacomError=null,this.mixinDatacomGet().then((function(t){e.mixinDatacomLoading=!1;var a=t.result,r=a.data,o=a.count;e.getcount&&(e.mixinDatacomPage.count=o),e.mixinDatacomHasMore=r.length<e.pageSize;var s=n?r.length?r[0]:void 0:r;e.mixinDatacomResData=s,i&&i(s)})).catch((function(t){e.mixinDatacomLoading=!1,e.mixinDatacomErrorMessage=t,e.mixinDatacomError=t,r&&r(t)})))},mixinDatacomGet:function(){var t,a,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};n=n||{},a="undefined"!=typeof __uniX&&__uniX?e.databaseForJQL(this.spaceInfo):e.database(this.spaceInfo);var i=n.action||this.action;i&&(a=a.action(i));var r=n.collection||this.collection;a=Array.isArray(r)?(t=a).collection.apply(t,(0,s.default)(r)):a.collection(r);var o=n.where||this.where;o&&Object.keys(o).length&&(a=a.where(o));var l=n.field||this.field;l&&(a=a.field(l));var c=n.foreignKey||this.foreignKey;c&&(a=a.foreignKey(c));var u=n.groupby||this.groupby;u&&(a=a.groupBy(u));var d=n.groupField||this.groupField;d&&(a=a.groupField(d)),!0===(void 0!==n.distinct?n.distinct:this.distinct)&&(a=a.distinct());var h=n.orderby||this.orderby;h&&(a=a.orderBy(h));var f=void 0!==n.pageCurrent?n.pageCurrent:this.mixinDatacomPage.current,p=void 0!==n.pageSize?n.pageSize:this.mixinDatacomPage.size,g=void 0!==n.getcount?n.getcount:this.getcount,x=void 0!==n.gettree?n.gettree:this.gettree,v=void 0!==n.gettreepath?n.gettreepath:this.gettreepath,m={getCount:g},y={limitLevel:void 0!==n.limitlevel?n.limitlevel:this.limitlevel,startWith:void 0!==n.startwith?n.startwith:this.startwith};return x&&(m.getTree=y),v&&(m.getTreePath=y),a=a.skip(p*(f-1)).limit(p).get(m),a}}}}function sn(e){return G(na.replace("{spaceId}",e.config.spaceId))}function ln(){return cn.apply(this,arguments)}function cn(){return cn=(0,u.default)((0,c.default)().mark((function e(){var t,a,n,i,r,o,s,l=arguments;return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t=l.length>0&&void 0!==l[0]?l[0]:{},a=t.openid,n=t.callLoginByWeixin,i=void 0!==n&&n,r=sn(this),"mp-weixin"===z){e.next=4;break}throw new Error("[SecureNetwork] API `initSecureNetworkByWeixin` is not supported on platform `".concat(z,"`"));case 4:if(!a||!i){e.next=6;break}throw new Error("[SecureNetwork] openid and callLoginByWeixin cannot be passed at the same time");case 6:if(!a){e.next=8;break}return e.abrupt("return",(r.mpWeixinOpenid=a,{}));case 8:return e.next=10,new Promise((function(e,t){uni.login({success:function(t){e(t.code)},fail:function(e){t(new Error(e.errMsg))}})}));case 10:return o=e.sent,s=this.importObject("uni-id-co",{customUI:!0}),e.next=14,s.secureNetworkHandshakeByWeixin({code:o,callLoginByWeixin:i});case 14:return r.mpWeixinCode=o,e.abrupt("return",{code:o});case 16:case"end":return e.stop()}}),e,this)}))),cn.apply(this,arguments)}function un(e){return dn.apply(this,arguments)}function dn(){return dn=(0,u.default)((0,c.default)().mark((function e(t){var a;return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return a=sn(this),e.abrupt("return",(a.initPromise||(a.initPromise=ln.call(this,t).then((function(e){return e})).catch((function(e){throw delete a.initPromise,e}))),a.initPromise));case 2:case"end":return e.stop()}}),e,this)}))),dn.apply(this,arguments)}function hn(e){!function(e){xe=e}(e)}function fn(e){var t={getSystemInfo:uni.getSystemInfo,getPushClientId:uni.getPushClientId};return function(a){return new Promise((function(n,i){t[e]((0,d.default)((0,d.default)({},a),{},{success:function(e){n(e)},fail:function(e){i(e)}}))}))}}var pn=function(e){(0,h.default)(a,e);var t=(0,f.default)(a);function a(){var e;return(0,g.default)(this,a),e=t.call(this),e._uniPushMessageCallback=e._receivePushMessage.bind((0,i.default)(e)),e._currentMessageId=-1,e._payloadQueue=[],e}return(0,x.default)(a,[{key:"init",value:function(){var e=this;return Promise.all([fn("getSystemInfo")(),fn("getPushClientId")()]).then((function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],a=(0,r.default)(t,2),n=a[0];n=void 0===n?{}:n;var i=n.appId,o=a[1];o=void 0===o?{}:o;var s=o.cid;if(!i)throw new Error("Invalid appId, please check the manifest.json file");if(!s)throw new Error("Invalid push client id");e._appId=i,e._pushClientId=s,e._seqId=Date.now()+"-"+Math.floor(9e5*Math.random()+1e5),e.emit("open"),e._initMessageListener()}),(function(t){throw e.emit("error",t),e.close(),t}))}},{key:"open",value:function(){var e=(0,u.default)((0,c.default)().mark((function e(){return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",this.init());case 1:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"_isUniCloudSSE",value:function(e){if("receive"!==e.type)return!1;var t=e&&e.data&&e.data.payload;return!(!t||"UNI_CLOUD_SSE"!==t.channel||t.seqId!==this._seqId)}},{key:"_receivePushMessage",value:function(e){if(this._isUniCloudSSE(e)){var t=e&&e.data&&e.data.payload,a=t.action,n=t.messageId,i=t.message;this._payloadQueue.push({action:a,messageId:n,message:i}),this._consumMessage()}}},{key:"_consumMessage",value:function(){for(var e=this;;){var t=this._payloadQueue.find((function(t){return t.messageId===e._currentMessageId+1}));if(!t)break;this._currentMessageId++,this._parseMessagePayload(t)}}},{key:"_parseMessagePayload",value:function(e){var t=e.action,a=e.messageId,n=e.message;"end"===t?this._end({messageId:a,message:n}):"message"===t&&this._appendMessage({messageId:a,message:n})}},{key:"_appendMessage",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=(e.messageId,e.message);this.emit("message",t)}},{key:"_end",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=(e.messageId,e.message);this.emit("end",t),this.close()}},{key:"_initMessageListener",value:function(){uni.onPushMessage(this._uniPushMessageCallback)}},{key:"_destroy",value:function(){uni.offPushMessage(this._uniPushMessageCallback)}},{key:"toJSON",value:function(){return{appId:this._appId,pushClientId:this._pushClientId,seqId:this._seqId}}},{key:"close",value:function(){this._destroy(),this.emit("close")}}]),a}(B);var gn={tcb:Lt,tencent:Lt,aliyun:we,private:Ut,dcloud:Ut,alipay:Xt},xn=new(function(){function e(){(0,g.default)(this,e)}return(0,x.default)(e,[{key:"init",value:function(e){var t={},a=gn[e.provider];if(!a)throw new Error("未提供正确的provider参数");return t=a.init(e),function(e){e._initPromiseHub||(e._initPromiseHub=new R({createPromise:function(){var t=Promise.resolve();t=new Promise((function(e){setTimeout((function(){e()}),1)}));var a=e.auth();return t.then((function(){return a.getLoginState()})).then((function(e){return e?Promise.resolve():a.signInAnonymously()}))}}))}(t),ha(t),function(e){var t=e.uploadFile;e.uploadFile=function(e){return t.call(this,e)}}(t),function(e){e.database=function(t){if(t&&Object.keys(t).length>0)return e.init(t).database();if(this._database)return this._database;var a=Aa(Ta,{uniClient:e});return this._database=a,a},e.databaseForJQL=function(t){if(t&&Object.keys(t).length>0)return e.init(t).databaseForJQL();if(this._databaseForJQL)return this._databaseForJQL;var a=Aa(Ta,{uniClient:e,isJQL:!0});return this._databaseForJQL=a,a}}(t),function(e){e.getCurrentUserInfo=tn,e.chooseAndUploadFile=nn.initChooseAndUploadFile(e),Object.assign(e,{get mixinDatacom(){return on(e)}}),e.SSEChannel=pn,e.initSecureNetworkByWeixin=function(e){return function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},a=t.openid,n=t.callLoginByWeixin,i=void 0!==n&&n;return un.call(e,{openid:a,callLoginByWeixin:i})}}(e),e.setCustomClientInfo=hn,e.importObject=function(e){return function(t){var a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};a=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return e.customUI=t.customUI||e.customUI,e.parseSystemError=t.parseSystemError||e.parseSystemError,Object.assign(e.loadingOptions,t.loadingOptions),Object.assign(e.errorOptions,t.errorOptions),"object"==(0,o.default)(t.secretMethods)&&(e.secretMethods=t.secretMethods),e}({customUI:!1,loadingOptions:{title:"加载中...",mask:!0},errorOptions:{type:"modal",retry:!1}},a);var n=a,i=n.customUI,r=n.loadingOptions,s=n.errorOptions,l=n.parseSystemError,h=!i;return new Proxy({},{get:function(n,i){switch(i){case"toString":return"[object UniCloudObject]";case"toJSON":return{}}return function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.fn,a=e.interceptorName,n=e.getCallbackArgs;return(0,u.default)((0,c.default)().mark((function e(){var i,r,o,s,l,u,h=arguments;return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:for(i=h.length,r=new Array(i),o=0;o<i;o++)r[o]=h[o];return s=n?n({params:r}):{},e.prev=2,e.next=5,X(V(a,"invoke"),(0,d.default)({},s));case 5:return e.next=7,t.apply(void 0,r);case 7:return l=e.sent,e.next=10,X(V(a,"success"),(0,d.default)((0,d.default)({},s),{},{result:l}));case 10:return e.abrupt("return",l);case 13:return e.prev=13,e.t0=e["catch"](2),u=e.t0,e.next=18,X(V(a,"fail"),(0,d.default)((0,d.default)({},s),{},{error:u}));case 18:throw u;case 19:return e.prev=19,e.next=22,X(V(a,"complete"),u?(0,d.default)((0,d.default)({},s),{},{error:u}):(0,d.default)((0,d.default)({},s),{},{result:l}));case 22:return e.finish(19);case 23:case"end":return e.stop()}}),e,null,[[2,13,19,23]])})))}({fn:function(){var n=(0,u.default)((0,c.default)().mark((function n(){var p,g,x,v,m,y,b,w,S,k,A,T,P,C,D,M=arguments;return(0,c.default)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:for(h&&uni.showLoading({title:r.title,mask:r.mask}),g=M.length,x=new Array(g),v=0;v<g;v++)x[v]=M[v];return m={name:t,type:_.OBJECT,data:{method:i,params:x}},"object"==(0,o.default)(a.secretMethods)&&function(e,t){var a=t.data.method,n=e.secretMethods||{},i=n[a]||n["*"];i&&(t.secretType=i)}(a,m),y=!1,n.prev=5,n.next=8,e.callFunction(m);case 8:p=n.sent,n.next=14;break;case 11:n.prev=11,n.t0=n["catch"](5),y=!0,p={result:new ce(n.t0)};case 14:if(b=p.result||{},w=b.errSubject,S=b.errCode,k=b.errMsg,A=b.newToken,h&&uni.hideLoading(),A&&A.token&&A.tokenExpired&&(pe(A),ie($.REFRESH_TOKEN,(0,d.default)({},A))),!S){n.next=39;break}if(T=k,!y||!l){n.next=24;break}return n.next=20,l({objectName:t,methodName:i,params:x,errSubject:w,errCode:S,errMsg:k});case 20:if(n.t1=n.sent.errMsg,n.t1){n.next=23;break}n.t1=k;case 23:T=n.t1;case 24:if(!h){n.next=37;break}if("toast"!==s.type){n.next=29;break}uni.showToast({title:T,icon:"none"}),n.next=37;break;case 29:if("modal"===s.type){n.next=31;break}throw new Error("Invalid errorOptions.type: ".concat(s.type));case 31:return n.next=33,(0,u.default)((0,c.default)().mark((function e(){var t,a,n,i,r,o,s=arguments;return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t=s.length>0&&void 0!==s[0]?s[0]:{},a=t.title,n=t.content,i=t.showCancel,r=t.cancelText,o=t.confirmText,e.abrupt("return",new Promise((function(e,t){uni.showModal({title:a,content:n,showCancel:i,cancelText:r,confirmText:o,success:function(t){e(t)},fail:function(){e({confirm:!1,cancel:!0})}})})));case 2:case"end":return e.stop()}}),e)})))({title:"提示",content:T,showCancel:s.retry,cancelText:"取消",confirmText:s.retry?"重试":"确定"});case 33:if(P=n.sent,C=P.confirm,!s.retry||!C){n.next=37;break}return n.abrupt("return",f.apply(void 0,x));case 37:throw D=new ce({subject:w,code:S,message:k,requestId:p.requestId}),D.detail=p.result,ie($.RESPONSE,{type:ee.CLOUD_OBJECT,content:D}),D;case 39:return n.abrupt("return",(ie($.RESPONSE,{type:ee.CLOUD_OBJECT,content:p.result}),p.result));case 40:case"end":return n.stop()}}),n,null,[[5,11]])})));function f(){return n.apply(this,arguments)}return f}(),interceptorName:"callObject",getCallbackArgs:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},a=e.params;return{objectName:t,methodName:i,params:a}}})}})}}(e)}(t),["callFunction","uploadFile","deleteFile","getTempFileURL","downloadFile","chooseAndUploadFile"].forEach((function(e){if(t[e]){var a=t[e];t[e]=function(){return a.apply(t,Array.from(arguments))},t[e]=function(e,t){return function(a){var n=this,i=!1;if("callFunction"===t){var r=a&&a.type||_.DEFAULT;i=r!==_.DEFAULT}var o="callFunction"===t&&!i,s=this._initPromiseHub.exec();a=a||{};var l=le(a),c=l.success,u=l.fail,d=l.complete,h=s.then((function(){return i?Promise.resolve():X(V(t,"invoke"),a)})).then((function(){return e.call(n,a)})).then((function(e){return i?Promise.resolve(e):X(V(t,"success"),e).then((function(){return X(V(t,"complete"),e)})).then((function(){return o&&ie($.RESPONSE,{type:ee.CLOUD_FUNCTION,content:e}),Promise.resolve(e)}))}),(function(e){return i?Promise.reject(e):X(V(t,"fail"),e).then((function(){return X(V(t,"complete"),e)})).then((function(){return ie($.RESPONSE,{type:ee.CLOUD_FUNCTION,content:e}),Promise.reject(e)}))}));if(!(c||u||d))return h;h.then((function(e){c&&c(e),d&&d(e),o&&ie($.RESPONSE,{type:ee.CLOUD_FUNCTION,content:e})}),(function(e){u&&u(e),d&&d(e),o&&ie($.RESPONSE,{type:ee.CLOUD_FUNCTION,content:e})}))}}(t[e],e).bind(t)}})),t.init=this.init,t}}]),e}());t.uniCloud=xn,function(){var e=W,a={};if(e&&1===e.length)a=e[0],t.uniCloud=xn=xn.init(a),xn._isDefault=!0;else{var n;n=e&&e.length>0?"应用有多个服务空间，请通过uniCloud.init方法指定要使用的服务空间":"应用未关联服务空间，请在uniCloud目录右键关联服务空间",["auth","callFunction","uploadFile","deleteFile","getTempFileURL","downloadFile","database","getCurrentUSerInfo","importObject"].forEach((function(e){xn[e]=function(){return console.error(n),Promise.reject(new ce({code:"SYS_ERR",message:n}))}}))}if(Object.assign(xn,{get mixinDatacom(){return on(xn)}}),Za(xn),xn.addInterceptor=Y,xn.removeInterceptor=K,xn.interceptObject=Q,"app"===z&&(uni.__uniCloud=xn),"app"===z||"web"===z){var i=function(){return j||(j=function(){if("undefined"!=typeof globalThis)return globalThis;if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;function e(){return this}return void 0!==e()?e():new Function("return this")()}(),j)}();i.uniCloud=xn,i.UniCloudError=ce}}();var vn=xn;t.default=vn}).call(this,a("0ee4"))},8626:function(e,t,a){"use strict";a.d(t,"b",(function(){return i})),a.d(t,"c",(function(){return r})),a.d(t,"a",(function(){return n}));var n={qiunDataCharts:a("a142").default},i=function(){var e=this.$createElement,t=this._self._c||e;return t("v-uni-view",{staticClass:"charts-box"},[t("qiun-data-charts",{attrs:{type:"line",opts:this.opts,chartData:this.chartData,background:"none"}})],1)},r=[]},"86d5":function(e,t,a){"use strict";a("6a54");var n=a("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("d4b5"),a("4626"),a("5ac7"),a("aa9c"),a("3efd");var i=n(a("fcf3")),r=n(a("1cfa")),o=n(a("833d")),s=n(a("9a72")),l={},c=null;function u(){for(var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length,a=new Array(t>1?t-1:0),n=1;n<t;n++)a[n-1]=arguments[n];for(var r in a)for(var o in a[r])a[r].hasOwnProperty(o)&&(e[o]=a[r][o]&&"object"===(0,i.default)(a[r][o])?u(Array.isArray(a[r][o])?[]:{},e[o],a[r][o]):a[r][o]);return e}function d(e,t){for(var a in e)e.hasOwnProperty(a)&&null!==e[a]&&"object"===(0,i.default)(e[a])?d(e[a],t):"format"===a&&"string"===typeof e[a]&&(e["formatter"]=t[e[a]]?t[e[a]]:void 0);return e}var h={data:function(){return{rid:null}},mounted:function(){var e=this;c={top:0,left:0};var t=document.querySelectorAll("uni-main")[0];void 0===t&&(t=document.querySelectorAll("uni-page-wrapper")[0]),c={top:t.offsetTop,left:t.offsetLeft},setTimeout((function(){null===e.rid&&e.$ownerInstance&&e.$ownerInstance.callMethod("getRenderType")}),200)},destroyed:function(){delete o.default.option[this.rid],delete o.default.instance[this.rid],delete s.default.option[this.rid],delete s.default.instance[this.rid]},methods:{ecinit:function(e,t,a,n){var r=JSON.stringify(e.id);this.rid=r,l[r]=this.$ownerInstance||n;var o=JSON.parse(JSON.stringify(e)),c=o.type;c&&s.default.type.includes(c)?s.default.option[r]=u({},s.default[c],o):s.default.option[r]=u({},o);var d=o.chartData;if(d){s.default.option[r].xAxis&&s.default.option[r].xAxis.type&&"category"===s.default.option[r].xAxis.type&&(s.default.option[r].xAxis.data=d.categories),s.default.option[r].yAxis&&s.default.option[r].yAxis.type&&"category"===s.default.option[r].yAxis.type&&(s.default.option[r].yAxis.data=d.categories),s.default.option[r].series=[];for(var h=0;h<d.series.length;h++){s.default.option[r].seriesTemplate=s.default.option[r].seriesTemplate?s.default.option[r].seriesTemplate:{};var f=u({},s.default.option[r].seriesTemplate,d.series[h]);s.default.option[r].series.push(f)}}if("object"===(0,i.default)(window.echarts))this.newEChart();else{var p=document.createElement("script"),g=window.location.origin,x=n.getDataset().directory;p.src=g+x+"uni_modules/qiun-data-charts/static/h5/echarts.min.js",p.onload=this.newEChart,document.head.appendChild(p)}},ecresize:function(e,t,a,n){s.default.instance[this.rid]&&s.default.instance[this.rid].resize()},newEChart:function(){var e=this.rid;void 0===s.default.instance[e]?(s.default.instance[e]=echarts.init(l[e].$el.children[0]),!0===s.default.option[e].ontap&&(s.default.instance[e].on("click",(function(t){var a=JSON.parse(JSON.stringify({x:t.event.offsetX,y:t.event.offsetY}));l[e].callMethod("emitMsg",{name:"getIndex",params:{type:"getIndex",event:a,currentIndex:t.dataIndex,value:t.data,seriesName:t.seriesName,id:e}})})),s.default.instance[e].on("highlight",(function(t){l[e].callMethod("emitMsg",{name:"getHighlight",params:{type:"highlight",res:t,id:e}})}))),this.updataEChart(e,s.default.option[e])):this.updataEChart(e,s.default.option[e])},updataEChart:function(e,t){if(t=d(t,s.default.formatter),t.tooltip&&(t.tooltip.show=!!t.tooltipShow,t.tooltip.position=this.tooltipPosition(),"string"===typeof t.tooltipFormat&&s.default.formatter[t.tooltipFormat]&&(t.tooltip.formatter=t.tooltip.formatter?t.tooltip.formatter:s.default.formatter[t.tooltipFormat])),t.series)for(var a in t.series){var n=t.series[a].linearGradient;n&&(t.series[a].color=new echarts.graphic.LinearGradient(n[0],n[1],n[2],n[3],n[4]))}s.default.instance[e].setOption(t,t.notMerge),s.default.instance[e].on("finished",(function(){l[e].callMethod("emitMsg",{name:"complete",params:{type:"complete",complete:!0,id:e}}),s.default.instance[e]&&s.default.instance[e].off("finished")})),"undefined"!=typeof l[e].$el.children[0].clientWidth&&(Math.abs(l[e].$el.children[0].clientWidth-s.default.instance[e].getWidth())>3||Math.abs(l[e].$el.children[0].clientHeight-s.default.instance[e].getHeight())>3)&&this.ecresize()},tooltipPosition:function(){return function(e,t,a,n,i){var r=e[0],o=e[1],s=i.viewSize[0],l=i.viewSize[1],c=i.contentSize[0],u=i.contentSize[1],d=r+30,h=o+30;return d+c>s&&(d=r-c-30),h+u>l&&(h=o-u-30),[d,h]}},ucinit:function(e,t,a,n){var i=this;if(JSON.stringify(e)!=JSON.stringify(t)&&e.canvasId){var r=JSON.parse(JSON.stringify(e.canvasId));this.rid=r,l[r]=this.$ownerInstance||n,o.default.option[r]=JSON.parse(JSON.stringify(e)),o.default.option[r]=d(o.default.option[r],o.default.formatter);var s=document.getElementById(r);s&&s.children[0]&&(o.default.option[r].context=s.children[0].getContext("2d"),o.default.instance[r]&&o.default.option[r]&&!0===o.default.option[r].update?this.updataUChart():setTimeout((function(){o.default.option[r].context.restore(),o.default.option[r].context.save(),i.newUChart()}),100))}},newUChart:function(){var e=this.rid;o.default.instance[e]=new r.default(o.default.option[e]),o.default.instance[e].addEventListener("renderComplete",(function(){l[e].callMethod("emitMsg",{name:"complete",params:{type:"complete",complete:!0,id:e,opts:o.default.instance[e].opts}}),o.default.instance[e].delEventListener("renderComplete")})),o.default.instance[e].addEventListener("scrollLeft",(function(){l[e].callMethod("emitMsg",{name:"scrollLeft",params:{type:"scrollLeft",scrollLeft:!0,id:e,opts:o.default.instance[e].opts}})})),o.default.instance[e].addEventListener("scrollRight",(function(){l[e].callMethod("emitMsg",{name:"scrollRight",params:{type:"scrollRight",scrollRight:!0,id:e,opts:o.default.instance[e].opts}})}))},updataUChart:function(){var e=this.rid;o.default.instance[e].updateData(o.default.option[e])},tooltipDefault:function(e,t,a,n){if(t){var r=e.data;return"object"===(0,i.default)(e.data)&&(r=e.data.value),t+" "+e.name+":"+r}return e.properties&&e.properties.name?e.properties.name:e.name+":"+e.data},showTooltip:function(e,t){var a=this,n=o.default.option[t].tooltipCustom;if(n&&void 0!==n&&null!==n){var i=void 0;n.x>=0&&n.y>=0&&(i={x:n.x,y:n.y+10}),o.default.instance[t].showToolTip(e,{index:n.index,offset:i,textList:n.textList,formatter:function(e,n,i,r){return"string"===typeof o.default.option[t].tooltipFormat&&o.default.formatter[o.default.option[t].tooltipFormat]?o.default.formatter[o.default.option[t].tooltipFormat](e,n,i,r):a.tooltipDefault(e,n,i,r)}})}else o.default.instance[t].showToolTip(e,{formatter:function(e,n,i,r){return"string"===typeof o.default.option[t].tooltipFormat&&o.default.formatter[o.default.option[t].tooltipFormat]?o.default.formatter[o.default.option[t].tooltipFormat](e,n,i,r):a.tooltipDefault(e,n,i,r)}})},tap:function(e){var t=this.rid,a=o.default.option[t].ontap,n=o.default.option[t].tooltipShow,i=o.default.option[t].tapLegend;if(0!=a){var r,s,u=document.getElementById("UC"+t).getBoundingClientRect(),d={};d=e.detail.x?{x:e.detail.x-u.left,y:e.detail.y-u.top+c.top}:{x:e.clientX-u.left,y:e.clientY-u.top+c.top},e.changedTouches=[],e.changedTouches.unshift(d),r=o.default.instance[t].getCurrentDataIndex(e),s=o.default.instance[t].getLegendDataIndex(e),!0===i&&o.default.instance[t].touchLegend(e),1==n&&this.showTooltip(e,t),l[t].callMethod("emitMsg",{name:"getIndex",params:{type:"getIndex",event:d,currentIndex:r,legendIndex:s,id:t,opts:o.default.instance[t].opts}})}},touchStart:function(e){var t=this.rid,a=o.default.option[t].ontouch;0!=a&&(!0===o.default.option[t].enableScroll&&1==e.touches.length&&o.default.instance[t].scrollStart(e),l[t].callMethod("emitMsg",{name:"getTouchStart",params:{type:"touchStart",event:e.changedTouches[0],id:t,opts:o.default.instance[t].opts}}))},touchMove:function(e){var t=this.rid,a=o.default.option[t].ontouch;if(0!=a){if(!0===o.default.option[t].enableScroll&&1==e.changedTouches.length&&o.default.instance[t].scroll(e),!0===o.default.option[t].ontap&&!1===o.default.option[t].enableScroll&&!0===o.default.option[t].onmovetip){var n=document.getElementById("UC"+t).getBoundingClientRect(),i={x:e.changedTouches[0].clientX-n.left,y:e.changedTouches[0].clientY-n.top+c.top};e.changedTouches.unshift(i),!0===o.default.option[t].tooltipShow&&this.showTooltip(e,t)}!0===a&&!0===o.default.option[t].enableScroll&&!0===o.default.option[t].onzoom&&2==e.changedTouches.length&&o.default.instance[t].dobuleZoom(e),l[t].callMethod("emitMsg",{name:"getTouchMove",params:{type:"touchMove",event:e.changedTouches[0],id:t,opts:o.default.instance[t].opts}})}},touchEnd:function(e){var t=this.rid,a=o.default.option[t].ontouch;0!=a&&(!0===o.default.option[t].enableScroll&&0==e.touches.length&&o.default.instance[t].scrollEnd(e),l[t].callMethod("emitMsg",{name:"getTouchEnd",params:{type:"touchEnd",event:e.changedTouches[0],id:t,opts:o.default.instance[t].opts}}))},mouseDown:function(e){var t=this.rid,a=o.default.option[t].onmouse;if(0!=a){var n,i=document.getElementById("UC"+t).getBoundingClientRect();n={x:e.clientX-i.left,y:e.clientY-i.top+c.top},e.changedTouches=[],e.changedTouches.unshift(n),o.default.instance[t].scrollStart(e),o.default.option[t].mousedown=!0,l[t].callMethod("emitMsg",{name:"getTouchStart",params:{type:"mouseDown",event:n,id:t,opts:o.default.instance[t].opts}})}},mouseMove:function(e){var t=this.rid,a=o.default.option[t].onmouse,n=o.default.option[t].tooltipShow;if(0!=a){var i,r=document.getElementById("UC"+t).getBoundingClientRect();i={x:e.clientX-r.left,y:e.clientY-r.top+c.top},e.changedTouches=[],e.changedTouches.unshift(i),o.default.option[t].mousedown?(o.default.instance[t].scroll(e),l[t].callMethod("emitMsg",{name:"getTouchMove",params:{type:"mouseMove",event:i,id:t,opts:o.default.instance[t].opts}})):o.default.instance[t]&&1==n&&this.showTooltip(e,t)}},mouseUp:function(e){var t=this.rid,a=o.default.option[t].onmouse;if(0!=a){var n,i=document.getElementById("UC"+t).getBoundingClientRect();n={x:e.clientX-i.left,y:e.clientY-i.top+c.top},e.changedTouches=[],e.changedTouches.unshift(n),o.default.instance[t].scrollEnd(e),o.default.option[t].mousedown=!1,l[t].callMethod("emitMsg",{name:"getTouchEnd",params:{type:"mouseUp",event:n,id:t,opts:o.default.instance[t].opts}})}}}};t.default=h},"8d06":function(e,t,a){"use strict";(function(e){a("6a54");var n=a("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("64aa"),a("d4b5"),a("4626"),a("5ac7"),a("aa9c"),a("fd3c"),a("5c47");var i=n(a("fcf3")),r=(n(a("1cfa")),n(a("833d"))),o=n(a("9a72"));function s(){for(var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length,a=new Array(t>1?t-1:0),n=1;n<t;n++)a[n-1]=arguments[n];for(var r in a)for(var o in a[r])a[r].hasOwnProperty(o)&&(e[o]=a[r][o]&&"object"===(0,i.default)(a[r][o])?s(Array.isArray(a[r][o])?[]:{},e[o],a[r][o]):a[r][o]);return e}function l(e){var t=e.getFullYear(),a=e.getMonth()+1,n=e.getDate();a>=1&&a<=9&&(a="0"+a),n>=0&&n<=9&&(n="0"+n);var i=t+"-"+a+"-"+n;return i}var c={name:"qiun-data-charts",mixins:[e.mixinDatacom],props:{type:{type:String,default:null},canvasId:{type:String,default:"uchartsid"},canvas2d:{type:Boolean,default:!1},background:{type:String,default:"rgba(0,0,0,0)"},animation:{type:Boolean,default:!0},chartData:{type:Object,default:function(){return{categories:[],series:[]}}},opts:{type:Object,default:function(){return{}}},eopts:{type:Object,default:function(){return{}}},loadingType:{type:Number,default:2},errorShow:{type:Boolean,default:!0},errorReload:{type:Boolean,default:!0},errorMessage:{type:String,default:null},inScrollView:{type:Boolean,default:!1},reshow:{type:Boolean,default:!1},reload:{type:Boolean,default:!1},disableScroll:{type:Boolean,default:!1},optsWatch:{type:Boolean,default:!0},onzoom:{type:Boolean,default:!1},ontap:{type:Boolean,default:!0},ontouch:{type:Boolean,default:!1},onmouse:{type:Boolean,default:!0},onmovetip:{type:Boolean,default:!1},echartsH5:{type:Boolean,default:!1},echartsApp:{type:Boolean,default:!1},tooltipShow:{type:Boolean,default:!0},tooltipFormat:{type:String,default:void 0},tooltipCustom:{type:Object,default:void 0},startDate:{type:String,default:void 0},endDate:{type:String,default:void 0},textEnum:{type:Array,default:function(){return[]}},groupEnum:{type:Array,default:function(){return[]}},pageScrollTop:{type:Number,default:0},directory:{type:String,default:"/"},tapLegend:{type:Boolean,default:!0},menus:{type:Array,default:function(){return[]}}},data:function(){return{cid:"uchartsid",inWx:!1,inAli:!1,inTt:!1,inBd:!1,inH5:!1,inApp:!1,inWin:!1,type2d:!0,disScroll:!1,openmouse:!1,pixel:1,cWidth:375,cHeight:250,showchart:!1,echarts:!1,echartsResize:{state:!1},uchartsOpts:{},echartsOpts:{},drawData:{},lastDrawTime:null}},created:function(){if(this.cid=this.canvasId,"uchartsid"==this.canvasId||""==this.canvasId){for(var e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz",t=e.length,a="",n=0;n<32;n++)a+=e.charAt(Math.floor(Math.random()*t));this.cid=a}var i=uni.getSystemInfoSync();"windows"!==i.platform&&"mac"!==i.platform||(this.inWin=!0),this.type2d=!1,this.disScroll=this.disableScroll},mounted:function(){var e=this;this.inH5=!0,!0===this.inWin&&(this.openmouse=this.onmouse),!0===this.echartsH5&&(this.echarts=!0),this.$nextTick((function(){e.beforeInit()}));var t=this.inH5?500:200,a=this;uni.onWindowResize(function(e,t){var a=!1;return function(){var n=arguments,i=this;clearTimeout(a),a&&clearTimeout(a),a=setTimeout((function(){a=!1,e.apply(i,n)}),t)}}((function(e){if(1!=a.mixinDatacomLoading){var t=a.mixinDatacomErrorMessage;null!==t&&"null"!==t&&""!==t||(a.echarts?a.echartsResize.state=!a.echartsResize.state:a.resizeHandler())}}),t))},destroyed:function(){!0===this.echarts?(delete o.default.option[this.cid],delete o.default.instance[this.cid]):(delete r.default.option[this.cid],delete r.default.instance[this.cid]),uni.offWindowResize((function(){}))},watch:{chartDataProps:{handler:function(e,t){"object"===(0,i.default)(e)?JSON.stringify(e)!==JSON.stringify(t)&&(this._clearChart(),e.series&&e.series.length>0?this.beforeInit():(this.mixinDatacomLoading=!0,this.showchart=!1,this.mixinDatacomErrorMessage=null)):(this.mixinDatacomLoading=!1,this._clearChart(),this.showchart=!1,this.mixinDatacomErrorMessage="参数错误：chartData数据类型错误")},immediate:!1,deep:!0},localdata:{handler:function(e,t){JSON.stringify(e)!==JSON.stringify(t)&&(e.length>0?this.beforeInit():(this.mixinDatacomLoading=!0,this._clearChart(),this.showchart=!1,this.mixinDatacomErrorMessage=null))},immediate:!1,deep:!0},optsProps:{handler:function(e,t){"object"===(0,i.default)(e)?JSON.stringify(e)!==JSON.stringify(t)&&!1===this.echarts&&1==this.optsWatch&&this.checkData(this.drawData):(this.mixinDatacomLoading=!1,this._clearChart(),this.showchart=!1,this.mixinDatacomErrorMessage="参数错误：opts数据类型错误")},immediate:!1,deep:!0},eoptsProps:{handler:function(e,t){"object"===(0,i.default)(e)?JSON.stringify(e)!==JSON.stringify(t)&&!0===this.echarts&&this.checkData(this.drawData):(this.mixinDatacomLoading=!1,this.showchart=!1,this.mixinDatacomErrorMessage="参数错误：eopts数据类型错误")},immediate:!1,deep:!0},reshow:function(e,t){var a=this;!0===e&&!1===this.mixinDatacomLoading&&setTimeout((function(){a.mixinDatacomErrorMessage=null,a.echartsResize.state=!a.echartsResize.state,a.checkData(a.drawData)}),200)},reload:function(e,t){!0===e&&(this.showchart=!1,this.mixinDatacomErrorMessage=null,this.reloading())},mixinDatacomErrorMessage:function(e,t){e&&(this.emitMsg({name:"error",params:{type:"error",errorShow:this.errorShow,msg:e,id:this.cid}}),this.errorShow&&console.log("[秋云图表组件]"+e))},errorMessage:function(e,t){e&&this.errorShow&&null!==e&&"null"!==e&&""!==e?(this.showchart=!1,this.mixinDatacomLoading=!1,this.mixinDatacomErrorMessage=e):(this.showchart=!1,this.mixinDatacomErrorMessage=null,this.reloading())}},computed:{optsProps:function(){return JSON.parse(JSON.stringify(this.opts))},eoptsProps:function(){return JSON.parse(JSON.stringify(this.eopts))},chartDataProps:function(){return JSON.parse(JSON.stringify(this.chartData))}},methods:{beforeInit:function(){this.mixinDatacomErrorMessage=null,"object"===(0,i.default)(this.chartData)&&null!=this.chartData&&void 0!==this.chartData.series&&this.chartData.series.length>0?(this.drawData=s({},this.chartData),this.mixinDatacomLoading=!1,this.showchart=!0,this.checkData(this.chartData)):this.localdata.length>0?(this.mixinDatacomLoading=!1,this.showchart=!0,this.localdataInit(this.localdata)):""!==this.collection?(this.mixinDatacomLoading=!1,this.getCloudData()):this.mixinDatacomLoading=!0},localdataInit:function(e){if(this.groupEnum.length>0)for(var t=0;t<e.length;t++)for(var a=0;a<this.groupEnum.length;a++)e[t].group===this.groupEnum[a].value&&(e[t].group=this.groupEnum[a].text);if(this.textEnum.length>0)for(var n=0;n<e.length;n++)for(var i=0;i<this.textEnum.length;i++)e[n].text===this.textEnum[i].value&&(e[n].text=this.textEnum[i].text);var c=!1,u={categories:[],series:[]},d=[],h=[];if(c=!0===this.echarts?o.default.categories.includes(this.type):r.default.categories.includes(this.type),!0===c){if(this.chartData&&this.chartData.categories&&this.chartData.categories.length>0)d=this.chartData.categories;else if(this.startDate&&this.endDate){var f=new Date(this.startDate),p=new Date(this.endDate);while(f<=p)d.push(l(f)),f=f.setDate(f.getDate()+1),f=new Date(f)}else{var g={};e.map((function(e,t){void 0==e.text||g[e.text]||(d.push(e.text),g[e.text]=!0)}))}u.categories=d}var x={};if(e.map((function(e,t){void 0==e.group||x[e.group]||(h.push({name:e.group,data:[]}),x[e.group]=!0)})),0==h.length)if(h=[{name:"默认分组",data:[]}],!0===c)for(var v=0;v<d.length;v++){for(var m=0,y=0;y<e.length;y++)e[y].text==d[v]&&(m=e[y].value);h[0].data.push(m)}else for(var b=0;b<e.length;b++)h[0].data.push({name:e[b].text,value:e[b].value});else for(var w=0;w<h.length;w++)if(d.length>0)for(var S=0;S<d.length;S++){for(var k=0,A=0;A<e.length;A++)h[w].name==e[A].group&&e[A].text==d[S]&&(k=e[A].value);h[w].data.push(k)}else for(var T=0;T<e.length;T++)h[w].name==e[T].group&&h[w].data.push(e[T].value);u.series=h,this.drawData=s({},u),this.checkData(u)},reloading:function(){!1!==this.errorReload&&(this.showchart=!1,this.mixinDatacomErrorMessage=null,""!==this.collection?(this.mixinDatacomLoading=!1,this.onMixinDatacomPropsChange(!0)):this.beforeInit())},checkData:function(e){var t=this,a=this.cid;!0===this.echarts?(o.default.option[a]=s({},this.eopts),o.default.option[a].id=a,o.default.option[a].type=this.type):this.type&&r.default.type.includes(this.type)?(r.default.option[a]=s({},r.default[this.type],this.opts),r.default.option[a].canvasId=a):(this.mixinDatacomLoading=!1,this.showchart=!1,this.mixinDatacomErrorMessage="参数错误：props参数中type类型不正确");var n=s({},e);void 0!==n.series&&n.series.length>0&&(this.mixinDatacomErrorMessage=null,!0===this.echarts?(o.default.option[a].chartData=n,this.$nextTick((function(){t.init()}))):(r.default.option[a].categories=n.categories,r.default.option[a].series=n.series,this.$nextTick((function(){t.init()}))))},resizeHandler:function(){var e=this,t=Date.now(),a=this.lastDrawTime?this.lastDrawTime:t-3e3,n=t-a;if(!(n<1e3))uni.createSelectorQuery().in(this).select("#ChartBoxId"+this.cid).boundingClientRect((function(t){e.showchart=!0,t.width>0&&t.height>0&&(t.width===e.cWidth&&t.height===e.cHeight||e.checkData(e.drawData))})).exec()},getCloudData:function(){var e=this;1!=this.mixinDatacomLoading&&(this.mixinDatacomLoading=!0,this.mixinDatacomGet().then((function(t){e.mixinDatacomResData=t.result.data,e.localdataInit(e.mixinDatacomResData)})).catch((function(t){e.mixinDatacomLoading=!1,e.showchart=!1,e.mixinDatacomErrorMessage="请求错误："+t})))},onMixinDatacomPropsChange:function(e,t){1==e&&""!==this.collection&&(this.showchart=!1,this.mixinDatacomErrorMessage=null,this._clearChart(),this.getCloudData())},_clearChart:function(){var e=this.cid;if(!0!==this.echarts&&r.default.option[e]&&r.default.option[e].context){var t=r.default.option[e].context;"object"!==(0,i.default)(t)||r.default.option[e].update||(t.clearRect(0,0,this.cWidth*this.pixel,this.cHeight*this.pixel),t.draw())}},init:function(){var e=this,t=this.cid;uni.createSelectorQuery().in(this).select("#ChartBoxId"+t).boundingClientRect((function(a){a.width>0&&a.height>0?(e.mixinDatacomLoading=!1,e.showchart=!0,e.lastDrawTime=Date.now(),e.cWidth=a.width,e.cHeight=a.height,!0!==e.echarts&&(r.default.option[t].background="rgba(0,0,0,0)"==e.background?"#FFFFFF":e.background,r.default.option[t].canvas2d=e.type2d,r.default.option[t].pixelRatio=e.pixel,r.default.option[t].animation=e.animation,r.default.option[t].width=a.width*e.pixel,r.default.option[t].height=a.height*e.pixel,r.default.option[t].onzoom=e.onzoom,r.default.option[t].ontap=e.ontap,r.default.option[t].ontouch=e.ontouch,r.default.option[t].onmouse=e.openmouse,r.default.option[t].onmovetip=e.onmovetip,r.default.option[t].tooltipShow=e.tooltipShow,r.default.option[t].tooltipFormat=e.tooltipFormat,r.default.option[t].tooltipCustom=e.tooltipCustom,r.default.option[t].inScrollView=e.inScrollView,r.default.option[t].lastDrawTime=e.lastDrawTime,r.default.option[t].tapLegend=e.tapLegend),e.inH5||e.inApp?1==e.echarts?(o.default.option[t].ontap=e.ontap,o.default.option[t].onmouse=e.openmouse,o.default.option[t].tooltipShow=e.tooltipShow,o.default.option[t].tooltipFormat=e.tooltipFormat,o.default.option[t].tooltipCustom=e.tooltipCustom,o.default.option[t].lastDrawTime=e.lastDrawTime,e.echartsOpts=s({},o.default.option[t])):(r.default.option[t].rotateLock=r.default.option[t].rotate,e.uchartsOpts=s({},r.default.option[t])):(r.default.option[t]=function e(t,a){for(var n in t)t.hasOwnProperty(n)&&null!==t[n]&&"object"===(0,i.default)(t[n])?e(t[n],a):"format"===n&&"string"===typeof t[n]&&(t["formatter"]=a[t[n]]?a[t[n]]:void 0);return t}(r.default.option[t],r.default.formatter),e.mixinDatacomErrorMessage=null,e.mixinDatacomLoading=!1,e.showchart=!0,e.$nextTick((function(){if(!0===e.type2d){var n=uni.createSelectorQuery().in(e);n.select("#"+t).fields({node:!0,size:!0}).exec((function(n){if(n[0]){var i=n[0].node,o=i.getContext("2d");r.default.option[t].context=o,r.default.option[t].rotateLock=r.default.option[t].rotate,r.default.instance[t]&&r.default.option[t]&&!0===r.default.option[t].update?e._updataUChart(t):(i.width=a.width*e.pixel,i.height=a.height*e.pixel,i._width=a.width*e.pixel,i._height=a.height*e.pixel,setTimeout((function(){r.default.option[t].context.restore(),r.default.option[t].context.save(),e._newChart(t)}),100))}else e.showchart=!1,e.mixinDatacomErrorMessage="参数错误：开启2d模式后，未获取到dom节点，canvas-id:"+t}))}else e.inAli&&(r.default.option[t].rotateLock=r.default.option[t].rotate),r.default.option[t].context=uni.createCanvasContext(t,e),r.default.instance[t]&&r.default.option[t]&&!0===r.default.option[t].update?e._updataUChart(t):setTimeout((function(){r.default.option[t].context.restore(),r.default.option[t].context.save(),e._newChart(t)}),100)})))):(e.mixinDatacomLoading=!1,e.showchart=!1,1==e.reshow&&(e.mixinDatacomErrorMessage="布局错误：未获取到父元素宽高尺寸！canvas-id:"+t))})).exec()},saveImage:function(){var e=this;uni.canvasToTempFilePath({canvasId:this.cid,success:function(t){var a=document.createElement("a");a.href=t.tempFilePath,a.download=e.cid,a.target="_blank",a.click()}},this)},getImage:function(){var e=this;if(0==this.type2d)uni.canvasToTempFilePath({canvasId:this.cid,success:function(t){e.emitMsg({name:"getImage",params:{type:"getImage",base64:t.tempFilePath}})}},this);else{var t=uni.createSelectorQuery().in(this);t.select("#"+this.cid).fields({node:!0,size:!0}).exec((function(t){if(t[0]){var a=t[0].node;e.emitMsg({name:"getImage",params:{type:"getImage",base64:a.toDataURL("image/png")}})}}))}},_error:function(e){this.mixinDatacomErrorMessage=e.detail.errMsg},emitMsg:function(e){this.$emit(e.name,e.params)},getRenderType:function(){!0===this.echarts&&!1===this.mixinDatacomLoading&&this.beforeInit()},toJSON:function(){return this}}};t.default=c}).call(this,a("861b")["uniCloud"])},"8d6f":function(e,t,a){var n=a("e4e0");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[e.i,n,""]]),n.locals&&(e.exports=n.locals);var i=a("967d").default;i("63fa16b7",n,!0,{sourceMap:!1,shadowMode:!1})},"8e20":function(e,t,a){"use strict";a("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default={name:"loading3",data:function(){return{}}}},"902c":function(e,t,a){"use strict";a.r(t);var n=a("971d"),i=a("1af1");for(var r in i)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(r);a("f1d2");var o=a("828b"),s=Object(o["a"])(i["default"],n["b"],n["c"],!1,null,"2ef93b2c",null,!1,n["a"],void 0);t["default"]=s.exports},"92f2":function(e,t,a){"use strict";a("6a54");var n=a("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.getIncompleteInfo=t.apiStatisticsVisit=t.apiStatisticsTrading=t.apiStatisticsGoodslist=t.apiSetShopInfo=t.apiIndex=void 0;var i=n(a("be47"));t.apiIndex=function(){return i.default.get("Statistics/workbench")};t.apiSetShopInfo=function(e){return i.default.post("shop/shopSet",e)};t.apiStatisticsGoodslist=function(){return i.default.get("Statistics/goodslist")};t.apiStatisticsTrading=function(){return i.default.get("Statistics/trading")};t.apiStatisticsVisit=function(){return i.default.get("Statistics/visit")};t.getIncompleteInfo=function(){return i.default.get("shop/getIncompleteInfo")}},9583:function(e,t,a){"use strict";a.d(t,"b",(function(){return n})),a.d(t,"c",(function(){return i})),a.d(t,"a",(function(){}));var n=function(){var e=this.$createElement,t=this._self._c||e;return t("v-uni-view",{staticClass:"container loading6"},[t("v-uni-view",{staticClass:"shape shape1"}),t("v-uni-view",{staticClass:"shape shape2"}),t("v-uni-view",{staticClass:"shape shape3"}),t("v-uni-view",{staticClass:"shape shape4"})],1)},i=[]},"971d":function(e,t,a){"use strict";a.d(t,"b",(function(){return n})),a.d(t,"c",(function(){return i})),a.d(t,"a",(function(){}));var n=function(){var e=this.$createElement,t=this._self._c||e;return t("v-uni-view",{staticClass:"container loading2"},[t("v-uni-view",{staticClass:"shape shape1"}),t("v-uni-view",{staticClass:"shape shape2"}),t("v-uni-view",{staticClass:"shape shape3"}),t("v-uni-view",{staticClass:"shape shape4"})],1)},i=[]},"974b":function(e,t,a){"use strict";a.r(t);var n=a("8626"),i=a("4953");for(var r in i)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(r);a("5e71");var o=a("828b"),s=Object(o["a"])(i["default"],n["b"],n["c"],!1,null,"9852448c",null,!1,n["a"],void 0);t["default"]=s.exports},"9a72":function(e,t,a){"use strict";a("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=["#1890FF","#91CB74","#FAC858","#EE6666","#73C0DE","#3CA272","#FC8452","#9A60B4","#ea7ccc"],i={type:["pie","ring","rose","funnel","line","column","area","radar","gauge","candle","demotype"],categories:["line","column","area","radar","gauge","candle","demotype"],instance:{},option:{},formatter:{tooltipDemo1:function(e){var t="";for(var a in e){0==a&&(t+=e[a].axisValueLabel+"年销售额");var n="--";null!==e[a].data&&(n=e[a].data),t+="\n"+e[a].seriesName+"："+n+" 万元"}return t},legendFormat:function(e){return"自定义图例+"+e},yAxisFormatDemo:function(e,t){return e+"元"},seriesFormatDemo:function(e){return e.name+"年"+e.value+"元"}},demotype:{color:n},column:{color:n,title:{text:""},tooltip:{trigger:"axis"},grid:{top:30,bottom:50,right:15,left:40},legend:{bottom:"left"},toolbox:{show:!1},xAxis:{type:"category",axisLabel:{color:"#666666"},axisLine:{lineStyle:{color:"#CCCCCC"}},boundaryGap:!0,data:[]},yAxis:{type:"value",axisTick:{show:!1},axisLabel:{color:"#666666"},axisLine:{lineStyle:{color:"#CCCCCC"}}},seriesTemplate:{name:"",type:"bar",data:[],barwidth:20,label:{show:!0,color:"#666666",position:"top"}}},line:{color:n,title:{text:""},tooltip:{trigger:"axis"},grid:{top:30,bottom:50,right:15,left:40},legend:{bottom:"left"},toolbox:{show:!1},xAxis:{type:"category",axisLabel:{color:"#666666"},axisLine:{lineStyle:{color:"#CCCCCC"}},boundaryGap:!0,data:[]},yAxis:{type:"value",axisTick:{show:!1},axisLabel:{color:"#666666"},axisLine:{lineStyle:{color:"#CCCCCC"}}},seriesTemplate:{name:"",type:"line",data:[],barwidth:20,label:{show:!0,color:"#666666",position:"top"}}},area:{color:n,title:{text:""},tooltip:{trigger:"axis"},grid:{top:30,bottom:50,right:15,left:40},legend:{bottom:"left"},toolbox:{show:!1},xAxis:{type:"category",axisLabel:{color:"#666666"},axisLine:{lineStyle:{color:"#CCCCCC"}},boundaryGap:!0,data:[]},yAxis:{type:"value",axisTick:{show:!1},axisLabel:{color:"#666666"},axisLine:{lineStyle:{color:"#CCCCCC"}}},seriesTemplate:{name:"",type:"line",data:[],areaStyle:{},label:{show:!0,color:"#666666",position:"top"}}},pie:{color:n,title:{text:""},tooltip:{trigger:"item"},grid:{top:40,bottom:30,right:15,left:15},legend:{bottom:"left"},seriesTemplate:{name:"",type:"pie",data:[],radius:"50%",label:{show:!0,color:"#666666",position:"top"}}},ring:{color:n,title:{text:""},tooltip:{trigger:"item"},grid:{top:40,bottom:30,right:15,left:15},legend:{bottom:"left"},seriesTemplate:{name:"",type:"pie",data:[],radius:["40%","70%"],avoidLabelOverlap:!1,label:{show:!0,color:"#666666",position:"top"},labelLine:{show:!0}}},rose:{color:n,title:{text:""},tooltip:{trigger:"item"},legend:{top:"bottom"},seriesTemplate:{name:"",type:"pie",data:[],radius:"55%",center:["50%","50%"],roseType:"area"}},funnel:{color:n,title:{text:""},tooltip:{trigger:"item",formatter:"{b} : {c}%"},legend:{top:"bottom"},seriesTemplate:{name:"",type:"funnel",left:"10%",top:60,bottom:60,width:"80%",min:0,max:100,minSize:"0%",maxSize:"100%",sort:"descending",gap:2,label:{show:!0,position:"inside"},labelLine:{length:10,lineStyle:{width:1,type:"solid"}},itemStyle:{bordercolor:"#fff",borderwidth:1},emphasis:{label:{fontSize:20}},data:[]}},gauge:{color:n,tooltip:{formatter:"{a} <br/>{b} : {c}%"},seriesTemplate:{name:"业务指标",type:"gauge",detail:{formatter:"{value}%"},data:[{value:50,name:"完成率"}]}},candle:{xAxis:{data:[]},yAxis:{},color:n,title:{text:""},dataZoom:[{type:"inside",xAxisIndex:[0,1],start:10,end:100},{show:!0,xAxisIndex:[0,1],type:"slider",bottom:10,start:10,end:100}],seriesTemplate:{name:"",type:"k",data:[]}}},r=i;t.default=r},"9d51":function(e,t,a){var n=a("e7d0");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[e.i,n,""]]),n.locals&&(e.exports=n.locals);var i=a("967d").default;i("36b644de",n,!0,{sourceMap:!1,shadowMode:!1})},a142:function(e,t,a){"use strict";a.r(t);var n=a("cf27"),i=a("3c70");for(var r in i)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(r);var o=a("aed6");for(var r in o)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(r);a("6e1f");var s=a("828b");i["default"].__module="rdcharts";var l=Object(s["a"])(o["default"],n["b"],n["c"],!1,null,"3dd6a45f",null,!1,n["a"],i["default"]);t["default"]=l.exports},a301:function(e,t,a){"use strict";a.d(t,"b",(function(){return n})),a.d(t,"c",(function(){return i})),a.d(t,"a",(function(){}));var n=function(){var e=this.$createElement,t=this._self._c||e;return t("v-uni-view",{staticClass:"container loading3"},[t("v-uni-view",{staticClass:"shape shape1"}),t("v-uni-view",{staticClass:"shape shape2"}),t("v-uni-view",{staticClass:"shape shape3"}),t("v-uni-view",{staticClass:"shape shape4"})],1)},i=[]},ac02:function(e,t,a){"use strict";a("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default={name:"loading6",data:function(){return{}}}},acb1:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){if("function"!==typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&(0,n.default)(e,t)},a("7a76"),a("c9b5"),a("6a54");var n=function(e){return e&&e.__esModule?e:{default:e}}(a("e668"))},aed6:function(e,t,a){"use strict";a.r(t);var n=a("8d06"),i=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=i.a},b0e4:function(e,t,a){var n=a("2624");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[e.i,n,""]]),n.locals&&(e.exports=n.locals);var i=a("967d").default;i("114f29b7",n,!0,{sourceMap:!1,shadowMode:!1})},bcb2:function(e,t,a){var n=a("c86c");t=n(!1),t.push([e.i,".chartsview[data-v-3dd6a45f]{width:100%;height:100%;display:flex;flex:1;justify-content:center;align-items:center}",""]),e.exports=t},bd17:function(e,t,a){"use strict";a("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default={name:"loading1",data:function(){return{}}}},c1a3:function(e,t,a){"use strict";a("15ab")},c86b1:function(e,t,a){"use strict";a("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default={pages:[{path:"pages/index/index",style:{navigationBarTitleText:"工作台",enablePullDownRefresh:!0},meta:{auth:!1}},{path:"pages/goods_manage/goods_manage",name:"goods_manage",meta:{auth:!1,keepScroll:!0},style:{navigationBarTitleText:"商品管理"}},{path:"pages/order_manage/order_manage",style:{navigationBarTitleText:"订单管理"},meta:{auth:!1}},{path:"pages/user/user",style:{navigationBarTitleText:"我的",navigationStyle:"custom"},meta:{auth:!1}},{path:"pages/login/login",style:{navigationBarTitleText:"登录"},meta:{auth:!1}},{path:"pages/goods_detail/goods_detail",style:{navigationBarTitleText:"商品详情"},meta:{auth:!1}},{path:"pages/order_detail/order_detail",style:{navigationBarTitleText:"订单详情"},meta:{auth:!0}},{path:"pages/address_edit/address_edit",style:{navigationBarTitleText:"地址编辑"},meta:{auth:!0}},{path:"pages/verification_order/verification_order",style:{navigationBarTitleText:"核销订单"},meta:{auth:!0}},{path:"pages/verification_detail/verification_detail",style:{navigationBarTitleText:"核销订单"},meta:{auth:!0}},{path:"pages/webview/webview",meta:{auth:!1}},{path:"pages/message/message",style:{navigationBarTitleText:"消息"}},{path:"pages/sessionlist/sessionlist",style:{navigationBarTitleText:""}},{path:"pages/ces/ces",style:{navigationBarTitleText:""}},{path:"pages/home/<USER>",style:{navigationBarTitleText:""}}],subPackages:[{root:"bundle",pages:[{path:"pages/user_profile/user_profile",style:{navigationBarTitleText:"个人设置"},meta:{auth:!0}},{path:"pages/server_explan/server_explan",style:{navigationBarTitleText:""},meta:{auth:!1}},{path:"pages/user_wallet/user_wallet",style:{navigationBarTitleText:"商家钱包"},meta:{auth:!0}},{path:"pages/user_withdraw/user_withdraw",style:{navigationBarTitleText:"提现"},meta:{auth:!0}},{path:"pages/bank_list/bank_list",style:{navigationBarTitleText:"银行卡列表"},meta:{auth:!0}},{path:"pages/bank_add/bank_add",style:{navigationBarTitleText:"银行卡添加"},meta:{auth:!0}},{path:"pages/shop_setting/shop_setting",style:{navigationBarTitleText:"商家设置"},meta:{auth:!0}},{path:"pages/shop_setting_edit/shop_setting_edit",style:{navigationBarTitleText:"商家设置"},meta:{auth:!0}},{path:"pages/run_time/run_time",style:{navigationBarTitleText:"编辑营业时间"},meta:{auth:!0}},{path:"pages/spec_edit/spec_edit",style:{navigationBarTitleText:"商品编辑"},meta:{auth:!0}},{path:"pages/deliver_goods/deliver_goods",style:{navigationBarTitleText:"填写物流"},meta:{auth:!0}},{path:"pages/shop_data/shop_data",style:{navigationBarTitleText:"数据统计"},meta:{auth:!0}},{path:"pages/After_sales_management/After_sales_management",style:{navigationBarTitleText:"售后管理"}},{path:"pages/invoiceManage/invoiceManage",style:{navigationBarTitleText:"发票管理"}},{path:"pages/Invoicing/Invoicing",style:{navigationBarTitleText:""}},{path:"pages/verify/verify",style:{navigationBarTitleText:"权威验证"}},{path:"pages/staffManage/staffManage",style:{navigationBarTitleText:"管理员"}},{path:"pages/roleManage/roleManage",style:{navigationBarTitleText:"角色管理"}},{path:"pages/editRole/editRole",style:{navigationBarTitleText:"管理员"}},{path:"pages/customerServiceList/customerServiceList",style:{navigationBarTitleText:"客服列表"}},{path:"pages/JprocurementGoods/JprocurementGoods",style:{navigationBarTitleText:"集采商品"}},{path:"pages/JprocurementGoodsList/JprocurementGoodsList",style:{navigationBarTitleText:"集采记录"}},{path:"pages/customerServiceSet/customerServiceSet",style:{navigationBarTitleText:"客服设置"}},{path:"pages/customerServiceScript/customerServiceScript",style:{navigationBarTitleText:"客服话术"}},{path:"pages/addScript/addScript",style:{navigationBarTitleText:""}},{path:"pages/memberList/memberList",style:{navigationBarTitleText:"会员列表"}},{path:"pages/payTheDeposit/payTheDeposit",style:{navigationBarTitleText:"缴纳保证金"}},{path:"pages/promotion/promotion",style:{navigationBarTitleText:"推广中心"}},{path:"pages/advertisementManage/advertisementManage",style:{navigationBarTitleText:"广告管理"}},{path:"pages/addAdvertisement/addAdvertisement",style:{navigationBarTitleText:""}},{path:"pages/ManufacturerAlliance/ManufacturerAlliance",style:{navigationBarTitleText:"厂商联盟"}},{path:"pages/set/set",style:{navigationBarTitleText:"设置"}},{path:"pages/addServe/addServe",style:{navigationBarTitleText:""}},{path:"pages/addStaff/addStaff",style:{navigationBarTitleText:""}},{path:"pages/afterSalesDetails/afterSalesDetails",style:{navigationBarTitleText:"售后详情"}},{path:"pages/purchaseRecords/purchaseRecords",style:{navigationBarTitleText:"购买记录"}},{path:"pages/addAdContents/addAdContents",style:{navigationBarTitleText:"配置广告位"}},{path:"pages/applyDeactivate/applyDeactivate",style:{navigationBarTitleText:"注销账号"}},{path:"pages/Feedback/Feedback",style:{navigationBarTitleText:"意见反馈"}},{path:"pages/ruleProtocol/ruleProtocol",style:{navigationBarTitleText:"规则协议"}},{path:"pages/depositDetails/depositDetails",style:{navigationBarTitleText:"保证金明细"}},{path:"pages/returnDeposit/returnDeposit",style:{navigationBarTitleText:"退还保证金"}}]}],tabBar:{color:"#999",selectedColor:"#516FF8",borderStyle:"black",backgroundColor:"#FFFFFF",list:[{pagePath:"pages/home/<USER>",iconPath:"/static/images/tabbar/icon_home.png",selectedIconPath:"/static/images/tabbar/icon_home.png",text:"首页"},{pagePath:"pages/index/index",iconPath:"/static/tabbar/workbenches.png",selectedIconPath:"/static/tabbar/xworkbenches.png",text:"工作台"},{pagePath:"pages/message/message",iconPath:"/static/tabbar/message.png",selectedIconPath:"/static/tabbar/xmessage.png",text:"消息"}]},globalStyle:{navigationBarTextStyle:"black",navigationBarTitleText:"",navigationBarBackgroundColor:"#FFFFFF",backgroundColor:"#F8F8F8",h5:{navigationStyle:"custom"}},easycom:{autoscan:!0,custom:{"^u-(.*)":"@/components/uview-ui/components/u-$1/u-$1.vue","^w-(.*)":"@/components/widgets/$1/$1.vue"}}}},cad9:function(e,t,a){"use strict";a("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){var t=(0,i.default)();return function(){var a,i=(0,n.default)(e);if(t){var o=(0,n.default)(this).constructor;a=Reflect.construct(i,arguments,o)}else a=i.apply(this,arguments);return(0,r.default)(this,a)}},a("6a88"),a("bf0f"),a("7996");var n=o(a("f1f8")),i=o(a("6c31")),r=o(a("62b0"));function o(e){return e&&e.__esModule?e:{default:e}}},cbb4:function(e,t,a){"use strict";a.d(t,"b",(function(){return n})),a.d(t,"c",(function(){return i})),a.d(t,"a",(function(){}));var n=function(){var e=this.$createElement,t=this._self._c||e;return t("v-uni-view",{staticClass:"chartsview"},[t("v-uni-view",{staticClass:"charts-error"}),t("v-uni-view",{staticClass:"charts-font"},[this._v(this._s(null==this.errorMessage?"请点击重试":this.errorMessage))])],1)},i=[]},cc63:function(e,t,a){"use strict";var n=a("8d6f"),i=a.n(n);i.a},cf27:function(e,t,a){"use strict";a.d(t,"b",(function(){return i})),a.d(t,"c",(function(){return r})),a.d(t,"a",(function(){return n}));var n={qiunLoading:a("70e7").default,qiunError:a("1a4c").default},i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("v-uni-view",{staticClass:"chartsview",attrs:{id:"ChartBoxId"+e.cid}},[e.mixinDatacomLoading?a("v-uni-view",[a("qiun-loading",{attrs:{loadingType:e.loadingType}})],1):e._e(),e.mixinDatacomErrorMessage&&e.errorShow?a("v-uni-view",{on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.reloading.apply(void 0,arguments)}}},[a("qiun-error",{attrs:{errorMessage:e.errorMessage}})],1):e._e(),e.echarts?[a("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:e.showchart,expression:"showchart"}],wxsProps:{"change:resize":"echartsResize","change:prop":"echartsOpts"},staticStyle:{width:"100%",height:"100%"},style:{background:e.background},attrs:{"data-directory":e.directory,id:"EC"+e.cid,prop:e.echartsOpts,"change:prop":e.rdcharts.ecinit,resize:e.echartsResize,"change:resize":e.rdcharts.ecresize}})]:[a("v-uni-view",{wxsProps:{"change:prop":"uchartsOpts"},attrs:{id:"UC"+e.cid,prop:e.uchartsOpts,"change:prop":e.rdcharts.ucinit},on:{mousemove:function(t){t=e.$handleWxsEvent(t),e.rdcharts.mouseMove(t,e.$getComponentDescriptor())},mousedown:function(t){t=e.$handleWxsEvent(t),e.rdcharts.mouseDown(t,e.$getComponentDescriptor())},mouseup:function(t){t=e.$handleWxsEvent(t),e.rdcharts.mouseUp(t,e.$getComponentDescriptor())},touchstart:function(t){t=e.$handleWxsEvent(t),e.rdcharts.touchStart(t,e.$getComponentDescriptor())},touchmove:function(t){t=e.$handleWxsEvent(t),e.rdcharts.touchMove(t,e.$getComponentDescriptor())},touchend:function(t){t=e.$handleWxsEvent(t),e.rdcharts.touchEnd(t,e.$getComponentDescriptor())},click:function(t){t=e.$handleWxsEvent(t),e.rdcharts.tap(t,e.$getComponentDescriptor())}}},[a("v-uni-canvas",{directives:[{name:"show",rawName:"v-show",value:e.showchart,expression:"showchart"}],style:{width:e.cWidth+"px",height:e.cHeight+"px",background:e.background},attrs:{id:e.cid,canvasId:e.cid,"disable-scroll":e.disableScroll},on:{error:function(t){arguments[0]=t=e.$handleEvent(t),e._error.apply(void 0,arguments)}}})],1)]],2)},r=[]},d110:function(e,t,a){"use strict";a.r(t);var n=a("7789"),i=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=i.a},d279:function(e,t,a){var n=a("c86c");t=n(!1),t.push([e.i,".container[data-v-37562626]{width:30px;height:30px;position:relative}.container.loading5 .shape[data-v-37562626]{width:15px;height:15px}.container .shape[data-v-37562626]{position:absolute;width:10px;height:10px;border-radius:1px}.container .shape.shape1[data-v-37562626]{left:0;background-color:#1890ff}.container .shape.shape2[data-v-37562626]{right:0;background-color:#91cb74}.container .shape.shape3[data-v-37562626]{bottom:0;background-color:#fac858}.container .shape.shape4[data-v-37562626]{bottom:0;right:0;background-color:#e66}.loading5 .shape1[data-v-37562626]{animation:animation5shape1-data-v-37562626 2s ease 0s infinite reverse}@-webkit-keyframes animation5shape1-data-v-37562626{0%{-webkit-transform:translate(0);transform:translate(0)}25%{-webkit-transform:translateY(15px);transform:translateY(15px)}50%{-webkit-transform:translate(15px,15px);transform:translate(15px,15px)}75%{-webkit-transform:translate(15px);transform:translate(15px)}}@keyframes animation5shape1-data-v-37562626{0%{-webkit-transform:translate(0);transform:translate(0)}25%{-webkit-transform:translateY(15px);transform:translateY(15px)}50%{-webkit-transform:translate(15px,15px);transform:translate(15px,15px)}75%{-webkit-transform:translate(15px);transform:translate(15px)}}.loading5 .shape2[data-v-37562626]{animation:animation5shape2-data-v-37562626 2s ease 0s infinite reverse}@-webkit-keyframes animation5shape2-data-v-37562626{0%{-webkit-transform:translate(0);transform:translate(0)}25%{-webkit-transform:translate(-15px);transform:translate(-15px)}50%{-webkit-transform:translate(-15px,15px);transform:translate(-15px,15px)}75%{-webkit-transform:translateY(15px);transform:translateY(15px)}}@keyframes animation5shape2-data-v-37562626{0%{-webkit-transform:translate(0);transform:translate(0)}25%{-webkit-transform:translate(-15px);transform:translate(-15px)}50%{-webkit-transform:translate(-15px,15px);transform:translate(-15px,15px)}75%{-webkit-transform:translateY(15px);transform:translateY(15px)}}.loading5 .shape3[data-v-37562626]{animation:animation5shape3-data-v-37562626 2s ease 0s infinite reverse}@-webkit-keyframes animation5shape3-data-v-37562626{0%{-webkit-transform:translate(0);transform:translate(0)}25%{-webkit-transform:translate(15px);transform:translate(15px)}50%{-webkit-transform:translate(15px,-15px);transform:translate(15px,-15px)}75%{-webkit-transform:translateY(-15px);transform:translateY(-15px)}}@keyframes animation5shape3-data-v-37562626{0%{-webkit-transform:translate(0);transform:translate(0)}25%{-webkit-transform:translate(15px);transform:translate(15px)}50%{-webkit-transform:translate(15px,-15px);transform:translate(15px,-15px)}75%{-webkit-transform:translateY(-15px);transform:translateY(-15px)}}.loading5 .shape4[data-v-37562626]{animation:animation5shape4-data-v-37562626 2s ease 0s infinite reverse}@-webkit-keyframes animation5shape4-data-v-37562626{0%{-webkit-transform:translate(0);transform:translate(0)}25%{-webkit-transform:translateY(-15px);transform:translateY(-15px)}50%{-webkit-transform:translate(-15px,-15px);transform:translate(-15px,-15px)}75%{-webkit-transform:translate(-15px);transform:translate(-15px)}}@keyframes animation5shape4-data-v-37562626{0%{-webkit-transform:translate(0);transform:translate(0)}25%{-webkit-transform:translateY(-15px);transform:translateY(-15px)}50%{-webkit-transform:translate(-15px,-15px);transform:translate(-15px,-15px)}75%{-webkit-transform:translate(-15px);transform:translate(-15px)}}",""]),e.exports=t},d2c4:function(e,t,a){"use strict";a("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=o,a("6a88"),a("bf0f"),a("7996"),a("aa9c");var n=r(a("e668")),i=r(a("6c31"));function r(e){return e&&e.__esModule?e:{default:e}}function o(e,a,r){return(0,i.default)()?t.default=o=Reflect.construct.bind():t.default=o=function(e,t,a){var i=[null];i.push.apply(i,t);var r=Function.bind.apply(e,i),o=new r;return a&&(0,n.default)(o,a.prototype),o},o.apply(null,arguments)}},d304:function(e,t,a){"use strict";a.r(t);var n=a("a301"),i=a("24cd");for(var r in i)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(r);a("1cc3");var o=a("828b"),s=Object(o["a"])(i["default"],n["b"],n["c"],!1,null,"4e1c1bfa",null,!1,n["a"],void 0);t["default"]=s.exports},d416:function(e,t,a){"use strict";a("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default={appid:"__UNI__4A3BF85"}},d441:function(e,t,a){"use strict";a("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){return-1!==Function.toString.call(e).indexOf("[native code]")},a("5ef2"),a("c9b5"),a("bf0f"),a("ab80")},d7d4:function(e,t,a){"use strict";a.r(t);var n=a("bd17"),i=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=i.a},d9ef:function(e,t,a){var n=a("d279");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[e.i,n,""]]),n.locals&&(e.exports=n.locals);var i=a("967d").default;i("6eb6ab72",n,!0,{sourceMap:!1,shadowMode:!1})},df46:function(e,t,a){"use strict";a("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n={props:{ids:{type:String},width:{type:String,default:"100%"},height:{type:String,default:"300rpx"},chartData:{type:Object}},data:function(){return{opts:{color:["#369DFF"],padding:[15,10,0,15],enableScroll:!1,legend:{},xAxis:{disableGrid:!0},yAxis:{gridType:"dash",dashLength:2},extra:{line:{type:"curve",width:2,activeType:"hollow"}}}}}};t.default=n},e062:function(e,t,a){"use strict";var n=a("8bdb");n({target:"Number",stat:!0,nonConfigurable:!0,nonWritable:!0},{MAX_SAFE_INTEGER:9007199254740991})},e4bb:function(e,t,a){"use strict";a.d(t,"b",(function(){return n})),a.d(t,"c",(function(){return i})),a.d(t,"a",(function(){}));var n=function(){var e=this.$createElement,t=this._self._c||e;return t("v-uni-view",{staticClass:"container loading5"},[t("v-uni-view",{staticClass:"shape shape1"}),t("v-uni-view",{staticClass:"shape shape2"}),t("v-uni-view",{staticClass:"shape shape3"}),t("v-uni-view",{staticClass:"shape shape4"})],1)},i=[]},e4e0:function(e,t,a){var n=a("c86c");t=n(!1),t.push([e.i,".container[data-v-fe0925d0]{width:30px;height:30px;position:relative}.container.loading1[data-v-fe0925d0]{-webkit-transform:rotate(45deg);transform:rotate(45deg)}.container .shape[data-v-fe0925d0]{position:absolute;width:10px;height:10px;border-radius:1px}.container .shape.shape1[data-v-fe0925d0]{left:0;background-color:#1890ff}.container .shape.shape2[data-v-fe0925d0]{right:0;background-color:#91cb74}.container .shape.shape3[data-v-fe0925d0]{bottom:0;background-color:#fac858}.container .shape.shape4[data-v-fe0925d0]{bottom:0;right:0;background-color:#e66}.loading1 .shape1[data-v-fe0925d0]{-webkit-animation:animation1shape1-data-v-fe0925d0 .5s ease 0s infinite alternate;animation:animation1shape1-data-v-fe0925d0 .5s ease 0s infinite alternate}@-webkit-keyframes animation1shape1-data-v-fe0925d0{from{-webkit-transform:translate(0);transform:translate(0)}to{-webkit-transform:translate(16px,16px);transform:translate(16px,16px)}}@keyframes animation1shape1-data-v-fe0925d0{from{-webkit-transform:translate(0);transform:translate(0)}to{-webkit-transform:translate(16px,16px);transform:translate(16px,16px)}}.loading1 .shape2[data-v-fe0925d0]{-webkit-animation:animation1shape2-data-v-fe0925d0 .5s ease 0s infinite alternate;animation:animation1shape2-data-v-fe0925d0 .5s ease 0s infinite alternate}@-webkit-keyframes animation1shape2-data-v-fe0925d0{from{-webkit-transform:translate(0);transform:translate(0)}to{-webkit-transform:translate(-16px,16px);transform:translate(-16px,16px)}}@keyframes animation1shape2-data-v-fe0925d0{from{-webkit-transform:translate(0);transform:translate(0)}to{-webkit-transform:translate(-16px,16px);transform:translate(-16px,16px)}}.loading1 .shape3[data-v-fe0925d0]{-webkit-animation:animation1shape3-data-v-fe0925d0 .5s ease 0s infinite alternate;animation:animation1shape3-data-v-fe0925d0 .5s ease 0s infinite alternate}@-webkit-keyframes animation1shape3-data-v-fe0925d0{from{-webkit-transform:translate(0);transform:translate(0)}to{-webkit-transform:translate(16px,-16px);transform:translate(16px,-16px)}}@keyframes animation1shape3-data-v-fe0925d0{from{-webkit-transform:translate(0);transform:translate(0)}to{-webkit-transform:translate(16px,-16px);transform:translate(16px,-16px)}}.loading1 .shape4[data-v-fe0925d0]{-webkit-animation:animation1shape4-data-v-fe0925d0 .5s ease 0s infinite alternate;animation:animation1shape4-data-v-fe0925d0 .5s ease 0s infinite alternate}@-webkit-keyframes animation1shape4-data-v-fe0925d0{from{-webkit-transform:translate(0);transform:translate(0)}to{-webkit-transform:translate(-16px,-16px);transform:translate(-16px,-16px)}}@keyframes animation1shape4-data-v-fe0925d0{from{-webkit-transform:translate(0);transform:translate(0)}to{-webkit-transform:translate(-16px,-16px);transform:translate(-16px,-16px)}}",""]),e.exports=t},e668:function(e,t,a){"use strict";function n(e,a){return t.default=n=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},n(e,a)}a("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=n,a("8a8d")},e7d0:function(e,t,a){var n=a("c86c");t=n(!1),t.push([e.i,".charts-box[data-v-9852448c]{width:%?676?%;height:%?544?%}",""]),e.exports=t},e8eb:function(e,t,a){"use strict";var n=a("d9ef"),i=a.n(n);i.a},f1d2:function(e,t,a){"use strict";var n=a("734d"),i=a.n(n);i.a},f1f8:function(e,t,a){"use strict";function n(e){return t.default=n=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},n(e)}a("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=n,a("8a8d"),a("926e")},f478:function(e,t,a){"use strict";a("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e},a("7a76"),a("c9b5")},f555:function(e,t,a){"use strict";var n=a("85c1"),i=a("ab4a"),r=a("e4ca"),o=a("471d"),s=a("af9e"),l=n.RegExp,c=l.prototype,u=i&&s((function(){var e=!0;try{l(".","d")}catch(u){e=!1}var t={},a="",n=e?"dgimsy":"gimsy",i=function(e,n){Object.defineProperty(t,e,{get:function(){return a+=n,!0}})},r={dotAll:"s",global:"g",ignoreCase:"i",multiline:"m",sticky:"y"};for(var o in e&&(r.hasIndices="d"),r)i(o,r[o]);var s=Object.getOwnPropertyDescriptor(c,"flags").get.call(t);return s!==n||a!==n}));u&&r(c,"flags",{configurable:!0,get:o})},f9cd:function(e,t,a){var n=a("37fa");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[e.i,n,""]]),n.locals&&(e.exports=n.locals);var i=a("967d").default;i("0cf58883",n,!0,{sourceMap:!1,shadowMode:!1})}}]);