{layout name="layout1" /}

<div class="wrapper">
    <div class="layui-card">
        <!-- 操作提示 -->
        <div class="layui-card-body">
            <div class="layui-collapse" style="border:1px dashed #c4c4c4">
                <div class="layui-colla-item">
                    <h2 class="layui-colla-title like-layui-colla-title">操作提示</h2>
                    <div class="layui-colla-content layui-show">
                        <p>*商家申请注销账号后，需要管理员审核。</p>
                        <p>*审核通过后，需要执行注销操作，将下架商品、清空用户关联、删除账号等。</p>
                        <p>*根据保证金支付时间，系统会自动判断使用微信退款还是转账退款。</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 搜索区域 -->
        <div class="layui-card-body">
            <div class="layui-form layui-form-pane">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">商家名称</label>
                        <div class="layui-input-inline">
                            <input type="text" name="shop_name" placeholder="请输入商家名称" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">审核状态</label>
                        <div class="layui-input-inline">
                            <select name="status">
                                <option value="">全部</option>
                                <option value="0">待审核</option>
                                <option value="1">已通过</option>
                                <option value="2">已拒绝</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">申请时间</label>
                        <div class="layui-input-inline">
                            <input type="text" name="start_time" id="start_time" placeholder="开始时间" autocomplete="off" class="layui-input">
                        </div>
                        <div class="layui-form-mid">-</div>
                        <div class="layui-input-inline">
                            <input type="text" name="end_time" id="end_time" placeholder="结束时间" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn layui-btn-sm" lay-submit lay-filter="search-btn">搜索</button>
                        <button class="layui-btn layui-btn-sm layui-btn-primary" lay-submit lay-filter="reset-btn">重置</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主体区域 -->
        <div class="layui-tab layui-tab-card" lay-filter="like-tabs">
            <div class="layui-tab-content" style="padding: 0 15px;">
                <table id="like-table-lists" lay-filter="like-table-lists"></table>

                <script type="text/html" id="status-tpl">
                    {{# if(d.status == 0){ }}
                    <span class="layui-badge layui-bg-orange">待审核</span>
                    {{# } else if(d.status == 1){ }}
                    <span class="layui-badge layui-bg-green">已通过</span>
                    {{# } else if(d.status == 2){ }}
                    <span class="layui-badge">已拒绝</span>
                    {{# } }}
                </script>

                <script type="text/html" id="can-deactivate-tpl">
                    {{# if(d.can_deactivate){ }}
                    <span class="layui-badge layui-bg-green">是</span>
                    {{# } else { }}
                    <span class="layui-badge">否</span>
                    {{# } }}
                </script>

                <script type="text/html" id="table-operation">
                    <a class="layui-btn layui-btn-normal layui-btn-sm" lay-event="detail">详情</a>
                    {{# if(d.status == 0){ }}
                    <a class="layui-btn layui-btn-warm layui-btn-sm" lay-event="audit">审核</a>
                    {{# } }}
                    {{# if(d.status == 1){ }}
                    <a class="layui-btn layui-btn-danger layui-btn-sm" lay-event="execute">执行注销</a>
                    {{# } }}
                </script>
            </div>
        </div>
    </div>
</div>

<script>
    layui.use(["table", "form", "element", "laydate"], function(){
        var table = layui.table;
        var form = layui.form;
        var element = layui.element;
        var laydate = layui.laydate;
        var $ = layui.$;

        // 日期选择器
        laydate.render({
            type: "datetime",
            elem: "#start_time",
            trigger: "click"
        });
        laydate.render({
            type: "datetime",
            elem: "#end_time",
            trigger: "click"
        });

        // 表格渲染
        like.tableLists("#like-table-lists", "{:url('shop.deactivate/lists')}", [
            {field: "id", width: 60, title: "ID", align: "center"}
            ,{field: "shop_name", width: 180, title: "商家名称"}
            ,{field: "admin_name", width: 120, title: "申请人", align: "center"}
            ,{field: "reason", width: 180, title: "注销原因"}
            ,{field: "status", width: 100, title: "审核状态", align: "center", templet: "#status-tpl"}
            ,{field: "can_deactivate", width: 120, title: "是否满足条件", align: "center", templet: "#can-deactivate-tpl"}
            ,{field: "create_time", width: 170, title: "申请时间", align: "center"}
            ,{field: "audit_time", width: 170, title: "审核时间", align: "center"}
            ,{title: "操作", width: 230, align: "center", fixed: "right", toolbar: "#table-operation"}
        ]);

        // 监听工具条
        var active = {
            detail: function(obj) {
                layer.open({
                    type: 2
                    ,title: "注销申请详情"
                    ,content: "{:url('shop.deactivate/detail')}?id=" + obj.data.id
                    ,area: ["800px", "600px"]
                });
            },
            audit: function(obj) {
                layer.open({
                    type: 2
                    ,title: "审核注销申请"
                    ,content: "{:url('shop.deactivate/audit')}?id=" + obj.data.id
                    ,area: ["500px", "400px"]
                    ,btn: ["确定", "取消"]
                    ,yes: function(index, layero){
                        var iframeWindow = window["layui-layer-iframe" + index];
                        var submit = layero.find("iframe").contents().find("#audit-submit");
                        iframeWindow.layui.form.on("submit(audit-submit)", function(data){
                            data.field["id"] = obj.data.id;
                            like.ajax({
                                url: "{:url('shop.deactivate/audit')}",
                                data: data.field,
                                type: "POST",
                                success:function(res) {
                                    if(res.code === 1) {
                                        layui.layer.msg(res.msg);
                                        layer.close(index);
                                        table.reload("like-table-lists", {
                                            where: {},
                                            page: { curr: 1 }
                                        });
                                    }
                                }
                            });
                        });
                        submit.trigger("click");
                    }
                });
            },
            execute: function(obj) {
                layer.confirm('确定要执行注销操作吗？此操作将下架商品、清空用户关联、删除账号等，不可恢复！', {icon: 3, title:'提示'}, function(index){
                    like.ajax({
                        url: "{:url('shop.deactivate/execute')}",
                        data: {id: obj.data.id},
                        type: "POST",
                        success: function(res){
                            if(res.code === 1){
                                layer.msg(res.msg, {icon: 1});
                                table.reload("like-table-lists");

                                // 显示执行结果
                                var resultHtml = '<div style="padding: 20px;">';
                                resultHtml += '<h3>执行结果：</h3>';
                                resultHtml += '<ul style="margin-top: 10px;">';
                                $.each(res.data.messages, function(index, message){
                                    resultHtml += '<li>' + message + '</li>';
                                });
                                resultHtml += '</ul>';

                                if(res.data.deposit_refund_type){
                                    resultHtml += '<div style="margin-top: 15px;font-weight:bold;">';
                                    if(res.data.deposit_refund_type === 'wechat'){
                                        resultHtml += '保证金支付时间不超过365天，可以通过微信退款';
                                    } else {
                                        resultHtml += '保证金支付时间超过365天，需要通过转账退款';
                                    }
                                    resultHtml += '</div>';
                                }

                                resultHtml += '</div>';

                                layer.open({
                                    type: 1,
                                    title: '注销执行结果',
                                    content: resultHtml,
                                    area: ['500px', '400px']
                                });
                            }
                        }
                    });
                    layer.close(index);
                });
            }
        };

        // 监听工具条点击
        table.on('tool(like-table-lists)', function(obj){
            var event = obj.event;
            active[event] ? active[event].call(this, obj) : '';
        });

        // 搜索
        form.on('submit(search-btn)', function(data){
            table.reload('like-table-lists', {
                where: data.field,
                page: {curr: 1}
            });
            return false;
        });

        // 重置
        form.on('submit(reset-btn)', function(){
            $('.layui-form input').val('');
            $('.layui-form select').val('');
            form.render('select');
            table.reload('like-table-lists', {
                where: {},
                page: {curr: 1}
            });
            return false;
        });
    });
</script>
