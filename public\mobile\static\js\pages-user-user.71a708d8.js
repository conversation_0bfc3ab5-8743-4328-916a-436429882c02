(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-user-user"],{"06c5":function(t,A,e){"use strict";var n=e("25fa"),i=e.n(n);i.a},"074c":function(t,A,e){"use strict";e("7a82");var n=e("ee27").default;Object.defineProperty(A,"__esModule",{value:!0}),A.default=void 0;var i=n(e("f07e")),a=n(e("c964")),s=n(e("f3f3")),r=n(e("4824")),o=e("26cb"),u=e("9953"),l=e("ea4b"),c=e("b08d"),f=(n(e("8ffc")),getApp(),{mixins:[r.default],data:function(){return{showNav:!1,navBg:0,menuList:[]}},onLoad:function(t){this.getMenuFun()},onShow:function(){this.getUser(),this.getCartNum(),console.log(this.userInfo)},onPageScroll:function(t){var A=uni.upx2px(100),e=t.scrollTop,n=e/A>1?1:e/A;this.navBg=n},onPullDownRefresh:function(){this.getUser().then((function(){uni.stopPullDownRefresh()})),this.getMenuFun()},methods:(0,s.default)((0,s.default)({},(0,o.mapActions)(["getCartNum","getUser"])),{},{goLogin:function(){var t=this.isLogin;t?uni.navigateTo({url:"/bundle/pages/user_profile/user_profile"}):uni.navigateTo({url:"/pages/login/login"})},goPage:function(t){if(!this.isLogin)return(0,l.toLogin)();uni.navigateTo({url:t})},getMenuFun:function(){var t=this;return(0,a.default)((0,i.default)().mark((function A(){var e,n,a;return(0,i.default)().wrap((function(A){while(1)switch(A.prev=A.next){case 0:return A.next=2,(0,u.getMenu)({type:2});case 2:e=A.sent,n=e.data,a=e.code,1==a&&(t.menuList=n);case 6:case"end":return A.stop()}}),A)})))()},onCopy:function(t){(0,c.copy)(this.userInfo.sn)},menuJump:function(t){(0,c.menuJump)(t)}}),computed:(0,s.default)((0,s.default)({},(0,o.mapGetters)(["userInfo","inviteCode","appConfig"])),{},{background:function(){var t=this.appConfig.center_setting;return t.top_bg_image?{"background-image":"url(".concat(t.top_bg_image,")")}:{}}})});A.default=f},"0da2":function(t,A,e){"use strict";var n=e("4955"),i=e.n(n);i.a},1203:function(t,A,e){"use strict";e.d(A,"b",(function(){return i})),e.d(A,"c",(function(){return a})),e.d(A,"a",(function(){return n}));var n={uWaterfall:e("d0a7").default,goodsList:e("2d92").default},i=function(){var t=this,A=t.$createElement,e=t._self._c||A;return t.hasData?e("mescroll-body",{ref:"mescrollRef",attrs:{up:t.upOption,down:t.downOption},on:{init:function(A){arguments[0]=A=t.$handleEvent(A),t.mescrollInit.apply(void 0,arguments)},down:function(A){arguments[0]=A=t.$handleEvent(A),t.downCallback.apply(void 0,arguments)},up:function(A){arguments[0]=A=t.$handleEvent(A),t.upCallback.apply(void 0,arguments)}}},[e("v-uni-view",{staticClass:"goods-column"},[e("v-uni-scroll-view",{attrs:{"scroll-x":"true"}},[e("v-uni-view",{staticClass:"column-wrap"},t._l(t.columnList,(function(A,n){return e("v-uni-view",{key:n,staticClass:"item flex-col m-r-50 muted",on:{click:function(A){arguments[0]=A=t.$handleEvent(A),t.changeActive(n)}}},[e("v-uni-view",{staticClass:"xxl normal title",class:{bold:t.active==n}},[t._v(t._s(A.name))]),e("v-uni-view",{staticClass:"m-t-8 xs text-center",class:{normal:t.active==n}},[t._v(t._s(A.remark))]),t.active==n?e("v-uni-view",{staticClass:"line br60"}):t._e()],1)})),1)],1)],1),e("v-uni-view",{staticClass:"goods"},[e("u-waterfall",{ref:"uWaterfall",attrs:{"add-time":20},scopedSlots:t._u([{key:"left",fn:function(t){var A=t.leftList;return[e("v-uni-view",{staticStyle:{padding:"0 9rpx 0 30rpx"}},[e("goods-list",{attrs:{width:"336rpx",type:"waterfall",list:A}})],1)]}},{key:"right",fn:function(t){var A=t.rightList;return[e("v-uni-view",{staticStyle:{padding:"0 30rpx 0 9rpx"}},[e("goods-list",{attrs:{width:"336rpx",type:"waterfall",list:A}})],1)]}}],null,!1,2662770354),model:{value:t.goodsList,callback:function(A){t.goodsList=A},expression:"goodsList"}})],1)],1):t._e()},a=[]},"1a43":function(t,A,e){"use strict";e.r(A);var n=e("074c"),i=e.n(n);for(var a in n)["default"].indexOf(a)<0&&function(t){e.d(A,t,(function(){return n[t]}))}(a);A["default"]=i.a},"1de5":function(t,A,e){"use strict";t.exports=function(t,A){return A||(A={}),t=t&&t.__esModule?t.default:t,"string"!==typeof t?t:(/^['"].*['"]$/.test(t)&&(t=t.slice(1,-1)),A.hash&&(t+=A.hash),/["'() \t\n]/.test(t)||A.needQuotes?'"'.concat(t.replace(/"/g,'\\"').replace(/\n/g,"\\n"),'"'):t)}},2219:function(t,A,e){"use strict";e.r(A);var n=e("e5e1"),i=e("6216");for(var a in i)["default"].indexOf(a)<0&&function(t){e.d(A,t,(function(){return i[t]}))}(a);var s=e("f0c5"),r=Object(s["a"])(i["default"],n["b"],n["c"],!1,null,null,null,!1,n["a"],void 0);A["default"]=r.exports},"25fa":function(t,A,e){var n=e("78c4");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var i=e("4f06").default;i("486d21a1",n,!0,{sourceMap:!1,shadowMode:!1})},"2f60":function(t,A,e){"use strict";e.d(A,"b",(function(){return i})),e.d(A,"c",(function(){return a})),e.d(A,"a",(function(){return n}));var n={uIcon:e("90f3").default,uBadge:e("321b").default},i=function(){var t=this,A=t.$createElement,e=t._self._c||A;return t.show?e("v-uni-view",{staticClass:"u-tabbar",on:{touchmove:function(A){A.stopPropagation(),A.preventDefault(),arguments[0]=A=t.$handleEvent(A),function(){}.apply(void 0,arguments)}}},[e("v-uni-view",{staticClass:"u-tabbar__content safe-area-inset-bottom",class:{"u-border-top":t.borderTop},style:{height:t.$u.addUnit(t.height),backgroundColor:t.bgColor}},[t._l(t.list,(function(A,n){return e("v-uni-view",{key:n,staticClass:"u-tabbar__content__item",class:{"u-tabbar__content__circle":t.midButton&&A.midButton},style:{backgroundColor:t.bgColor},on:{click:function(A){A.stopPropagation(),arguments[0]=A=t.$handleEvent(A),t.clickHandler(n)}}},[e("v-uni-view",{class:[t.midButton&&A.midButton?"u-tabbar__content__circle__button":"u-tabbar__content__item__button"]},[e("u-icon",{attrs:{size:t.midButton&&A.midButton?t.midButtonSize:t.iconSize,name:t.elIconPath(n),"img-mode":"scaleToFill",color:t.elColor(n),"custom-prefix":A.customIcon?"custom-icon":"uicon"}}),A.count?e("u-badge",{attrs:{count:A.count,"is-dot":A.isDot,offset:[-2,t.getOffsetRight(A.count,A.isDot)]}}):t._e()],1),e("v-uni-view",{staticClass:"u-tabbar__content__item__text",style:{color:t.elColor(n)}},[e("v-uni-text",{staticClass:"u-line-1"},[t._v(t._s(A.text))])],1)],1)})),t.midButton?e("v-uni-view",{staticClass:"u-tabbar__content__circle__border",class:{"u-border":t.borderTop},style:{backgroundColor:t.bgColor,left:t.midButtonLeft}}):t._e()],2),e("v-uni-view",{staticClass:"u-fixed-placeholder safe-area-inset-bottom",style:{height:"calc("+t.$u.addUnit(t.height)+" + "+(t.midButton?48:0)+"rpx)"}})],1):t._e()},a=[]},"321b":function(t,A,e){"use strict";e.r(A);var n=e("57b6"),i=e("95ba");for(var a in i)["default"].indexOf(a)<0&&function(t){e.d(A,t,(function(){return i[t]}))}(a);e("4ef0");var s=e("f0c5"),r=Object(s["a"])(i["default"],n["b"],n["c"],!1,null,"629aeef1",null,!1,n["a"],void 0);A["default"]=r.exports},3249:function(t,A,e){"use strict";e("7a82");var n=e("ee27").default;Object.defineProperty(A,"__esModule",{value:!0}),A.default=void 0,e("99af");var i=n(e("f07e")),a=n(e("c964")),s=n(e("bde1")),r=e("9953"),o={mixins:[s.default],props:{autoGetData:{type:Boolean,default:!0}},data:function(){return{goodsList:[],active:0,columnList:[],upOption:{auto:!1,empty:{icon:"/static/images/goods_null.png",tip:"暂无商品"},toTop:{bottom:"300rpx"}},downOption:{use:!1,isLock:!0},hasData:!0}},mounted:function(){this.autoGetData&&this.getData()},methods:{getData:function(){var t=this;return(0,a.default)((0,i.default)().mark((function A(){return(0,i.default)().wrap((function(A){while(1)switch(A.prev=A.next){case 0:return A.next=2,t.getGoodsColumnFun();case 2:t.$refs.uWaterfall&&t.$refs.uWaterfall.clear(),t.mescroll.resetUpScroll();case 4:case"end":return A.stop()}}),A)})))()},changeActive:function(t){this.active=t,this.$refs.uWaterfall.clear(),this.mescroll.resetUpScroll()},upCallback:function(t){var A=this,e=this.columnList,n=this.active,i=t.num,a=t.size;if(e.length){var s=e[n].id;(0,r.getGoodsListColumn)({page_size:a,page_no:i,column_id:s}).then((function(e){var n=e.data,i=n.lists,a=i.length,s=!!n.more;1==t.num&&(A.goodsList=[]),A.goodsList=A.goodsList.concat(i),A.mescroll.endSuccess(a,s)}))}},getGoodsColumnFun:function(){var t=this;return(0,a.default)((0,i.default)().mark((function A(){var e,n,a;return(0,i.default)().wrap((function(A){while(1)switch(A.prev=A.next){case 0:return A.next=2,(0,r.getGoodsColumn)();case 2:e=A.sent,n=e.data,a=e.code,1==a&&(t.columnList=n,t.hasData=!!n.length);case 6:case"end":return A.stop()}}),A)})))()}}};A.default=o},"37df":function(t,A,e){"use strict";e.d(A,"b",(function(){return i})),e.d(A,"c",(function(){return a})),e.d(A,"a",(function(){return n}));var n={uIcon:e("90f3").default,goodsColumn:e("7579").default,tabbar:e("2219").default},i=function(){var t=this,A=t.$createElement,e=t._self._c||A;return e("v-uni-view",{staticClass:"user",style:[t.background]},[e("v-uni-view",{staticClass:"header"},[e("v-uni-view",{staticClass:"hd-wrap bg-white"},[e("v-uni-view",{staticClass:"user-info flex row-between"},[e("router-link",{attrs:{to:"/bundle/pages/user_profile/user_profile"}},[e("v-uni-view",{staticClass:"info flex"},[e("v-uni-image",{staticClass:"avatar m-r-20 flex-none",attrs:{src:t.isLogin?t.userInfo.avatar:"/static/images/portrait_empty.png"}}),t.isLogin?e("v-uni-view",[e("v-uni-view",{staticClass:"name flex"},[e("v-uni-view",{staticClass:"xxl bold line-2"},[t._v(t._s(t.userInfo.nickname))]),t.userInfo.vip?e("v-uni-view",{staticClass:"level br60 m-l-10 flex-none flex"},[e("v-uni-text",{staticClass:"m-r-10"},[e("v-uni-text",{staticClass:"v xxl m-r-4"},[t._v("v")]),e("v-uni-text",{staticClass:"xxs"},[t._v(t._s(t.userInfo.vip))])],1),e("v-uni-text",{staticClass:"xxs"},[t._v(t._s(t.userInfo.level_name))])],1):t._e()],1),t.userInfo.sn?e("v-uni-view",{staticClass:"flex m-t-10"},[e("v-uni-view",{staticClass:"user-id xs m-r-20"},[t._v("会员ID: "+t._s(t.userInfo.sn))]),e("v-uni-view",{staticClass:"xs primary row-center m-l-5",on:{click:function(A){A.stopPropagation(),arguments[0]=A=t.$handleEvent(A),t.onCopy.apply(void 0,arguments)}}},[t._v("复制")])],1):t._e()],1):e("v-uni-view",[e("v-uni-view",{staticStyle:{"font-size":"42rpx"}},[t._v("点击登录")]),e("v-uni-view",{staticClass:"sm m-t-10 lighter"},[t._v("登录体验更多功能")])],1)],1)],1),e("v-uni-view",{staticClass:"flex m-l-20"},[e("router-link",{attrs:{to:"/bundle/pages/user_set/user_set"}},[e("v-uni-view",{staticClass:"user-opt"},[e("v-uni-image",{staticStyle:{width:"58rpx",height:"58rpx"},attrs:{src:"/static/images/icon_my_setting.png"}})],1)],1)],1)],1),e("v-uni-view",{staticClass:"user-assets flex m-t-20 m-b-20"},[e("router-link",{staticClass:"user-assests-item",attrs:{to:"/bundle/pages/user_wallet/user_wallet"}},[e("v-uni-view",{staticClass:"flex-col col-center"},[e("v-uni-view",{staticClass:"xl primary"},[t._v(t._s(t.userInfo.user_money||0))]),e("v-uni-view",{staticClass:"sm m-t-10"},[t._v("余额")])],1)],1),e("router-link",{staticClass:"user-assests-item",attrs:{to:"/bundle/pages/user_collection/user_collection"}},[e("v-uni-view",{staticClass:"flex-col col-center"},[e("v-uni-view",{staticClass:"xl primary"},[t._v(t._s(t.userInfo.collect||0))]),e("v-uni-view",{staticClass:"sm m-t-10"},[t._v("收藏")])],1)],1),e("router-link",{staticClass:"user-assests-item",attrs:{to:"/bundle/pages/user_coupon/user_coupon"}},[e("v-uni-view",{staticClass:"flex-col col-center"},[e("v-uni-view",{staticClass:"xl primary"},[t._v(t._s(t.userInfo.coupon||0))]),e("v-uni-view",{staticClass:"sm m-t-10"},[t._v("优惠券")])],1)],1)],1)],1)],1),e("v-uni-view",{staticClass:"order-nav bg-white"},[e("router-link",{attrs:{to:"/bundle/pages/user_order/user_order"}},[e("v-uni-view",{staticClass:"title flex row-between"},[e("v-uni-view",{staticClass:"lg"},[t._v("我的订单")]),e("v-uni-view",{staticClass:"muted sm row"},[t._v("全部订单"),e("u-icon",{attrs:{name:"arrow-right",size:"28rpx"}})],1)],1)],1),e("v-uni-view",{staticClass:"nav flex"},[e("router-link",{staticClass:"item",attrs:{to:{path:"/bundle/pages/user_order/user_order",query:{type:"pay"}}}},[e("v-uni-view",{staticClass:"flex-col col-center m-b-20"},[e("v-uni-view",{staticClass:"icon-contain"},[t.userInfo.wait_pay?e("v-uni-view",{staticClass:"badge xs flex row-center bg-white"},[t._v(t._s(t.userInfo.wait_pay))]):t._e(),e("v-uni-image",{staticClass:"nav-icon",attrs:{src:"/static/images/icon_my_payment.png"}})],1),e("v-uni-view",{staticClass:"sm m-t-10"},[t._v("待付款")])],1)],1),e("router-link",{staticClass:"item",attrs:{to:"/bundle/pages/user_order/user_order?type=delivery"}},[e("v-uni-view",{staticClass:"flex-col col-center m-b-20"},[e("v-uni-view",{staticClass:"icon-contain"},[t.userInfo.wait_delivery?e("v-uni-view",{staticClass:"badge xs flex row-center bg-white"},[t._v(t._s(t.userInfo.wait_delivery))]):t._e(),e("v-uni-image",{staticClass:"nav-icon m-b-10",attrs:{src:"/static/images/icon_my_fahuo.png"}})],1),e("v-uni-view",{staticClass:"sm"},[t._v("待发货")])],1)],1),e("router-link",{staticClass:"item",attrs:{to:"/bundle/pages/user_order/user_order?type=delivery"}},[e("v-uni-view",{staticClass:"flex-col col-center m-b-20"},[e("v-uni-view",{staticClass:"icon-contain"},[t.userInfo.wait_take?e("v-uni-view",{staticClass:"badge xs flex row-center bg-white"},[t._v(t._s(t.userInfo.wait_take))]):t._e(),e("v-uni-image",{staticClass:"nav-icon",attrs:{src:"/static/images/icon_my_shouhuo.png"}})],1),e("v-uni-view",{staticClass:"sm m-t-10"},[t._v("待收货")])],1)],1),e("router-link",{staticClass:"item",attrs:{to:"/bundle/pages/user_comment/user_comment"}},[e("v-uni-view",{staticClass:"flex-col col-center m-b-20"},[e("v-uni-view",{staticClass:"icon-contain"},[t.userInfo.wait_comment?e("v-uni-view",{staticClass:"badge xs flex row-center bg-white"},[t._v(t._s(t.userInfo.wait_comment))]):t._e(),e("v-uni-image",{staticClass:"nav-icon",attrs:{src:"/static/images/icon_my_pingjia.png"}})],1),e("v-uni-view",{staticClass:"sm m-t-10"},[t._v("商品评价")])],1)],1),e("router-link",{staticClass:"item",attrs:{to:"/bundle/pages/after_sales/after_sales"}},[e("v-uni-view",{staticClass:"flex-col col-center m-b-20"},[e("v-uni-view",{staticClass:"icon-contain"},[t.userInfo.after_sale?e("v-uni-view",{staticClass:"badge xs flex row-center bg-white"},[t._v(t._s(t.userInfo.after_sale))]):t._e(),e("v-uni-image",{staticClass:"nav-icon",attrs:{src:"/static/images/icon_my_shouhou.png"}})],1),e("v-uni-view",{staticClass:"sm m-t-10"},[t._v("退款/售后")])],1)],1)],1)],1),t.menuList&&t.menuList.length>0?e("v-uni-view",{staticClass:"server-nav bg-white"},[e("v-uni-view",[e("v-uni-view",{staticClass:"title flex row-between"},[e("v-uni-view",{staticClass:"lg"},[t._v("我的功能")])],1)],1),e("v-uni-view",{staticClass:"nav flex flex-wrap"},t._l(t.menuList,(function(A,n){return e("v-uni-view",{key:n,staticClass:"item flex-col col-center m-b-20",staticStyle:{width:"25%"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.menuJump(A)}}},[e("v-uni-image",{staticClass:"nav-icon",attrs:{src:A.image}}),e("v-uni-view",{staticClass:"sm m-t-10"},[t._v(t._s(A.name))])],1)})),1)],1):t._e(),e("goods-column",{ref:"mescrollItem"}),e("tabbar")],1)},a=[]},"3e48":function(t,A,e){"use strict";e("7a82");var n=e("ee27").default;Object.defineProperty(A,"__esModule",{value:!0}),A.default=void 0;var i=n(e("f07e")),a=n(e("c964"));e("a9e3"),e("99af"),e("fb6a"),e("14d9"),e("a434"),e("e9c4"),e("c740");var s={name:"u-waterfall",props:{value:{type:Array,required:!0,default:function(){return[]}},addTime:{type:[Number,String],default:200},idKey:{type:String,default:"id"}},data:function(){return{leftList:[],rightList:[],tempList:[],children:[]}},watch:{copyFlowList:function(t,A){var e=Array.isArray(A)&&A.length>0?A.length:0;this.tempList=this.tempList.concat(this.cloneData(t.slice(e))),this.splitData()}},mounted:function(){this.tempList=this.cloneData(this.copyFlowList),this.splitData()},computed:{copyFlowList:function(){return this.cloneData(this.value)}},methods:{splitData:function(){var t=this;return(0,a.default)((0,i.default)().mark((function A(){var e,n,a;return(0,i.default)().wrap((function(A){while(1)switch(A.prev=A.next){case 0:if(t.tempList.length){A.next=2;break}return A.abrupt("return");case 2:return A.next=4,t.$uGetRect("#u-left-column");case 4:return e=A.sent,A.next=7,t.$uGetRect("#u-right-column");case 7:if(n=A.sent,a=t.tempList[0],a){A.next=11;break}return A.abrupt("return");case 11:e.height<n.height?t.leftList.push(a):e.height>n.height?t.rightList.push(a):t.leftList.length<=t.rightList.length?t.leftList.push(a):t.rightList.push(a),t.tempList.splice(0,1),t.tempList.length&&setTimeout((function(){t.splitData()}),t.addTime);case 14:case"end":return A.stop()}}),A)})))()},cloneData:function(t){return JSON.parse(JSON.stringify(t))},clear:function(){this.leftList=[],this.rightList=[],this.$emit("input",[]),this.tempList=[]},remove:function(t){var A=this,e=-1;e=this.leftList.findIndex((function(e){return e[A.idKey]==t})),-1!=e?this.leftList.splice(e,1):(e=this.rightList.findIndex((function(e){return e[A.idKey]==t})),-1!=e&&this.rightList.splice(e,1)),e=this.value.findIndex((function(e){return e[A.idKey]==t})),-1!=e&&this.$emit("input",this.value.splice(e,1))},modify:function(t,A,e){var n=this,i=-1;if(i=this.leftList.findIndex((function(A){return A[n.idKey]==t})),-1!=i?this.leftList[i][A]=e:(i=this.rightList.findIndex((function(A){return A[n.idKey]==t})),-1!=i&&(this.rightList[i][A]=e)),i=this.value.findIndex((function(A){return A[n.idKey]==t})),-1!=i){var a=this.cloneData(this.value);a[i][A]=e,this.$emit("input",a)}}}};A.default=s},4185:function(t,A,e){var n=e("701d");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var i=e("4f06").default;i("e2d009ca",n,!0,{sourceMap:!1,shadowMode:!1})},"43e8":function(t,A){t.exports="data:image/png;base64,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"},4824:function(t,A,e){"use strict";e("7a82"),Object.defineProperty(A,"__esModule",{value:!0}),A.default=void 0;var n={onPageScroll:function(t){this.handlePageScroll(t)},onReachBottom:function(){this.handleReachBottom()},onPullDownRefresh:function(){this.handlePullDownRefresh()},data:function(){var t=this;return{mescroll:{onPageScroll:function(A){t.handlePageScroll(A)},onReachBottom:function(){t.handleReachBottom()},onPullDownRefresh:function(){t.handlePullDownRefresh()}}}},methods:{handlePageScroll:function(t){var A=this.$refs["mescrollItem"];A&&A.mescroll&&A.mescroll.onPageScroll(t)},handleReachBottom:function(){var t=this.$refs["mescrollItem"];t&&t.mescroll&&t.mescroll.onReachBottom()},handlePullDownRefresh:function(){var t=this.$refs["mescrollItem"];t&&t.mescroll&&t.mescroll.onPullDownRefresh()}}};A.default=n},4955:function(t,A,e){var n=e("e8a9");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var i=e("4f06").default;i("9c016ae0",n,!0,{sourceMap:!1,shadowMode:!1})},"4ef0":function(t,A,e){"use strict";var n=e("58ae"),i=e.n(n);i.a},"57b6":function(t,A,e){"use strict";e.d(A,"b",(function(){return n})),e.d(A,"c",(function(){return i})),e.d(A,"a",(function(){}));var n=function(){var t=this,A=t.$createElement,e=t._self._c||A;return t.show?e("v-uni-view",{staticClass:"u-badge",class:[t.isDot?"u-badge-dot":"","mini"==t.size?"u-badge-mini":"",t.type?"u-badge--bg--"+t.type:""],style:[{top:t.offset[0]+"rpx",right:t.offset[1]+"rpx",fontSize:t.fontSize+"rpx",position:t.absolute?"absolute":"static",color:t.color,backgroundColor:t.bgColor},t.boxStyle]},[t._v(t._s(t.showText))]):t._e()},i=[]},"58ae":function(t,A,e){var n=e("e214");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var i=e("4f06").default;i("8a988d78",n,!0,{sourceMap:!1,shadowMode:!1})},"5d4b":function(t,A,e){"use strict";e.r(A);var n=e("c7d3"),i=e.n(n);for(var a in n)["default"].indexOf(a)<0&&function(t){e.d(A,t,(function(){return n[t]}))}(a);A["default"]=i.a},6216:function(t,A,e){"use strict";e.r(A);var n=e("8232"),i=e.n(n);for(var a in n)["default"].indexOf(a)<0&&function(t){e.d(A,t,(function(){return n[t]}))}(a);A["default"]=i.a},"664a":function(t,A,e){"use strict";e.r(A);var n=e("37df"),i=e("1a43");for(var a in i)["default"].indexOf(a)<0&&function(t){e.d(A,t,(function(){return i[t]}))}(a);e("c5e0");var s=e("f0c5"),r=Object(s["a"])(i["default"],n["b"],n["c"],!1,null,"5ae8bec4",null,!1,n["a"],void 0);A["default"]=r.exports},"701d":function(t,A,e){var n=e("24fb");A=n(!1),A.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.goods-column .column-wrap[data-v-67ba699e]{display:inline-block;white-space:nowrap;padding:%?30?%}.goods-column .column-wrap .item[data-v-67ba699e]{display:inline-block;position:relative;overflow:hidden}.goods-column .column-wrap .item .title[data-v-67ba699e]{position:relative;z-index:1}.goods-column .column-wrap .item .line[data-v-67ba699e]{position:absolute;top:%?32?%;left:0;width:%?144?%;height:%?12?%;background:linear-gradient(90deg,#ff2c3c,rgba(255,44,60,0))}',""]),t.exports=A},7579:function(t,A,e){"use strict";e.r(A);var n=e("1203"),i=e("ba81");for(var a in i)["default"].indexOf(a)<0&&function(t){e.d(A,t,(function(){return i[t]}))}(a);e("8af8");var s=e("f0c5"),r=Object(s["a"])(i["default"],n["b"],n["c"],!1,null,"67ba699e",null,!1,n["a"],void 0);A["default"]=r.exports},7853:function(t,A,e){"use strict";e.r(A);var n=e("3e48"),i=e.n(n);for(var a in n)["default"].indexOf(a)<0&&function(t){e.d(A,t,(function(){return n[t]}))}(a);A["default"]=i.a},"78c4":function(t,A,e){var n=e("24fb");A=n(!1),A.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.u-fixed-placeholder[data-v-94201f12]{box-sizing:initial}.u-tabbar__content[data-v-94201f12]{display:flex;align-items:center;position:relative;position:fixed;bottom:0;left:0;width:100%;z-index:998;box-sizing:initial}.u-tabbar__content__circle__border[data-v-94201f12]{border-radius:100%;width:%?110?%;height:%?110?%;top:%?-48?%;position:absolute;z-index:4;background-color:#fff;left:50%;-webkit-transform:translateX(-50%);transform:translateX(-50%)}.u-tabbar__content__circle__border[data-v-94201f12]:after{border-radius:100px}.u-tabbar__content__item[data-v-94201f12]{flex:1;justify-content:center;height:100%;padding:%?12?% 0;display:flex;flex-direction:row;flex-direction:column;align-items:center;position:relative}.u-tabbar__content__item__button[data-v-94201f12]{position:absolute;top:%?14?%;left:50%;-webkit-transform:translateX(-50%);transform:translateX(-50%)}.u-tabbar__content__item__text[data-v-94201f12]{color:#606266;font-size:%?26?%;line-height:%?28?%;position:absolute;bottom:%?14?%;left:50%;-webkit-transform:translateX(-50%);transform:translateX(-50%);width:100%;text-align:center}.u-tabbar__content__circle[data-v-94201f12]{position:relative;display:flex;flex-direction:row;flex-direction:column;justify-content:space-between;z-index:10;height:calc(100% - 1px)}.u-tabbar__content__circle__button[data-v-94201f12]{width:%?90?%;height:%?90?%;border-radius:100%;display:flex;flex-direction:row;justify-content:center;align-items:center;position:absolute;background-color:#fff;top:%?-40?%;left:50%;z-index:6;-webkit-transform:translateX(-50%);transform:translateX(-50%)}',""]),t.exports=A},8232:function(t,A,e){"use strict";e("7a82");var n=e("ee27").default;Object.defineProperty(A,"__esModule",{value:!0}),A.default=void 0,e("d81d"),e("4de4"),e("d3b7"),e("c740");var i=n(e("f3f3")),a=e("26cb"),s={data:function(){return{currentRoute:""}},mounted:function(){var t=getCurrentPages(),A=t[t.length-1];this.currentRoute=A.route},computed:(0,i.default)({tabbarStyle:function(){return this.appConfig.navigation_setting||{}},tabbarList:function(){var t=this,A=this.appConfig.navigation_menu||[];return console.log(this.cartNum),A.filter((function(t){return 1==t.status})).map((function(A){return{iconPath:A.un_selected_icon,selectedIconPath:A.selected_icon,text:A.name,count:"pages/shop_cart/shop_cart"==A.page_path?t.cartNum:0,pagePath:"/"+A.page_path}}))},showTabbar:function(){var t=this,A=this.tabbarList.findIndex((function(A){return A.pagePath==="/"+t.currentRoute}));return A>=0}},(0,a.mapGetters)(["cartNum"]))};A.default=s},"8af8":function(t,A,e){"use strict";var n=e("4185"),i=e.n(n);i.a},"92ad":function(t,A,e){"use strict";e.d(A,"b",(function(){return n})),e.d(A,"c",(function(){return i})),e.d(A,"a",(function(){}));var n=function(){var t=this.$createElement,A=this._self._c||t;return A("v-uni-view",{staticClass:"u-waterfall"},[A("v-uni-view",{staticClass:"u-column",attrs:{id:"u-left-column"}},[this._t("left",null,{leftList:this.leftList})],2),A("v-uni-view",{staticClass:"u-column",attrs:{id:"u-right-column"}},[this._t("right",null,{rightList:this.rightList})],2)],1)},i=[]},"95ba":function(t,A,e){"use strict";e.r(A);var n=e("b91e"),i=e.n(n);for(var a in n)["default"].indexOf(a)<0&&function(t){e.d(A,t,(function(){return n[t]}))}(a);A["default"]=i.a},b91e:function(t,A,e){"use strict";e("7a82"),Object.defineProperty(A,"__esModule",{value:!0}),A.default=void 0,e("a9e3");var n={name:"u-badge",props:{type:{type:String,default:"error"},size:{type:String,default:"default"},isDot:{type:Boolean,default:!1},count:{type:[Number,String]},overflowCount:{type:Number,default:99},showZero:{type:Boolean,default:!1},offset:{type:Array,default:function(){return[20,20]}},absolute:{type:Boolean,default:!0},fontSize:{type:[String,Number],default:"24"},color:{type:String,default:"#ffffff"},bgColor:{type:String,default:""},isCenter:{type:Boolean,default:!1}},computed:{boxStyle:function(){var t={};return this.isCenter?(t.top=0,t.right=0,t.transform="translateY(-50%) translateX(50%)"):(t.top=this.offset[0]+"rpx",t.right=this.offset[1]+"rpx",t.transform="translateY(0) translateX(0)"),"mini"==this.size&&(t.transform=t.transform+" scale(0.8)"),t},showText:function(){return this.isDot?"":this.count>this.overflowCount?"".concat(this.overflowCount,"+"):this.count},show:function(){return 0!=this.count||0!=this.showZero}}};A.default=n},ba81:function(t,A,e){"use strict";e.r(A);var n=e("3249"),i=e.n(n);for(var a in n)["default"].indexOf(a)<0&&function(t){e.d(A,t,(function(){return n[t]}))}(a);A["default"]=i.a},bde1:function(t,A,e){"use strict";e("7a82"),Object.defineProperty(A,"__esModule",{value:!0}),A.default=void 0;var n={data:function(){return{mescroll:null}},onPullDownRefresh:function(){this.mescroll&&this.mescroll.onPullDownRefresh()},onPageScroll:function(t){this.mescroll&&this.mescroll.onPageScroll(t)},onReachBottom:function(){this.mescroll&&this.mescroll.onReachBottom()},methods:{mescrollInit:function(t){console.log(t),this.mescroll=t,this.mescrollInitByRef()},mescrollInitByRef:function(){if(!this.mescroll||!this.mescroll.resetUpScroll){var t=this.$refs.mescrollRef;t&&(this.mescroll=t.mescroll)}},downCallback:function(){var t=this;this.mescroll.optUp.use?this.mescroll.resetUpScroll():setTimeout((function(){t.mescroll.endSuccess()}),500)},upCallback:function(){var t=this;setTimeout((function(){t.mescroll.endErr()}),500)}},mounted:function(){this.mescrollInitByRef()}},i=n;A.default=i},c5e0:function(t,A,e){"use strict";var n=e("ebea"),i=e.n(n);i.a},c7d3:function(t,A,e){"use strict";e("7a82");var n=e("ee27").default;Object.defineProperty(A,"__esModule",{value:!0}),A.default=void 0;var i=n(e("f07e")),a=n(e("c964"));e("a9e3");var s={props:{show:{type:Boolean,default:!0},value:{type:[String,Number],default:0},bgColor:{type:String,default:"#ffffff"},height:{type:[String,Number],default:"50px"},iconSize:{type:[String,Number],default:40},midButtonSize:{type:[String,Number],default:90},activeColor:{type:String,default:"#303133"},inactiveColor:{type:String,default:"#606266"},midButton:{type:Boolean,default:!1},list:{type:Array,default:function(){return[]}},beforeSwitch:{type:Function,default:null},borderTop:{type:Boolean,default:!0},hideTabBar:{type:Boolean,default:!0}},data:function(){return{midButtonLeft:"50%",pageUrl:""}},created:function(){this.hideTabBar&&uni.hideTabBar();var t=getCurrentPages();this.pageUrl=t[t.length-1].route},computed:{elIconPath:function(){var t=this;return function(A){var e=t.list[A].pagePath;return e?e==t.pageUrl||e=="/"+t.pageUrl?t.list[A].selectedIconPath:t.list[A].iconPath:A==t.value?t.list[A].selectedIconPath:t.list[A].iconPath}},elColor:function(){var t=this;return function(A){var e=t.list[A].pagePath;return e?e==t.pageUrl||e=="/"+t.pageUrl?t.activeColor:t.inactiveColor:A==t.value?t.activeColor:t.inactiveColor}}},mounted:function(){this.midButton&&this.getMidButtonLeft()},methods:{clickHandler:function(t){var A=this;return(0,a.default)((0,i.default)().mark((function e(){var n;return(0,i.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!A.beforeSwitch||"function"!==typeof A.beforeSwitch){e.next=10;break}if(n=A.beforeSwitch.bind(A.$u.$parent.call(A))(t),!n||"function"!==typeof n.then){e.next=7;break}return e.next=5,n.then((function(e){A.switchTab(t)})).catch((function(t){}));case 5:e.next=8;break;case 7:!0===n&&A.switchTab(t);case 8:e.next=11;break;case 10:A.switchTab(t);case 11:case"end":return e.stop()}}),e)})))()},switchTab:function(t){this.$emit("change",t),this.list[t].pagePath?uni.switchTab({url:this.list[t].pagePath}):this.$emit("input",t)},getOffsetRight:function(t,A){return A?-20:t>9?-40:-30},getMidButtonLeft:function(){var t=this.$u.sys().windowWidth;this.midButtonLeft=t/2+"px"}}};A.default=s},d0a7:function(t,A,e){"use strict";e.r(A);var n=e("92ad"),i=e("7853");for(var a in i)["default"].indexOf(a)<0&&function(t){e.d(A,t,(function(){return i[t]}))}(a);e("0da2");var s=e("f0c5"),r=Object(s["a"])(i["default"],n["b"],n["c"],!1,null,"7664bcb0",null,!1,n["a"],void 0);A["default"]=r.exports},d2ea:function(t,A,e){"use strict";e.r(A);var n=e("2f60"),i=e("5d4b");for(var a in i)["default"].indexOf(a)<0&&function(t){e.d(A,t,(function(){return i[t]}))}(a);e("06c5");var s=e("f0c5"),r=Object(s["a"])(i["default"],n["b"],n["c"],!1,null,"94201f12",null,!1,n["a"],void 0);A["default"]=r.exports},e214:function(t,A,e){var n=e("24fb");A=n(!1),A.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.u-badge[data-v-629aeef1]{display:inline-flex;justify-content:center;align-items:center;line-height:%?24?%;padding:%?4?% %?8?%;border-radius:%?100?%;z-index:9}.u-badge--bg--primary[data-v-629aeef1]{background-color:#ff2c3c}.u-badge--bg--error[data-v-629aeef1]{background-color:#fa3534}.u-badge--bg--success[data-v-629aeef1]{background-color:#19be6b}.u-badge--bg--info[data-v-629aeef1]{background-color:#909399}.u-badge--bg--warning[data-v-629aeef1]{background-color:#f90}.u-badge-dot[data-v-629aeef1]{height:%?16?%;width:%?16?%;border-radius:%?100?%;line-height:1}.u-badge-mini[data-v-629aeef1]{-webkit-transform:scale(.8);transform:scale(.8);-webkit-transform-origin:center center;transform-origin:center center}.u-info[data-v-629aeef1]{background-color:#909399;color:#fff}',""]),t.exports=A},e5e1:function(t,A,e){"use strict";e.d(A,"b",(function(){return i})),e.d(A,"c",(function(){return a})),e.d(A,"a",(function(){return n}));var n={uTabbar:e("d2ea").default},i=function(){var t=this.$createElement,A=this._self._c||t;return A("u-tabbar",{directives:[{name:"show",rawName:"v-show",value:this.showTabbar,expression:"showTabbar"}],attrs:{activeColor:this.tabbarStyle.st_color,inactiveColor:this.tabbarStyle.ust_color,list:this.tabbarList}})},a=[]},e8a9:function(t,A,e){var n=e("24fb");A=n(!1),A.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.u-waterfall[data-v-7664bcb0]{display:flex;flex-direction:row;flex-direction:row;align-items:flex-start}.u-column[data-v-7664bcb0]{display:flex;flex-direction:row;flex:1;flex-direction:column;height:auto}.u-image[data-v-7664bcb0]{width:100%}',""]),t.exports=A},ebea:function(t,A,e){var n=e("fc14");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var i=e("4f06").default;i("8a7b796e",n,!0,{sourceMap:!1,shadowMode:!1})},fc14:function(t,A,e){var n=e("24fb"),i=e("1de5"),a=e("43e8");A=n(!1);var s=i(a);A.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.user[data-v-5ae8bec4]{background-image:url('+s+");background-size:100% auto;background-repeat:no-repeat}.user .header[data-v-5ae8bec4]{margin:0 %?20?%;padding-top:%?20?%}.user .header .hd-wrap[data-v-5ae8bec4]{padding-bottom:%?20?%;border-radius:%?20?%}.user .header .user-info[data-v-5ae8bec4]{padding:%?30?%}.user .header .user-info .avatar[data-v-5ae8bec4]{height:%?110?%;width:%?110?%;border-radius:50%;overflow:hidden}.user .header .user-info .name[data-v-5ae8bec4]{text-align:left;margin-bottom:%?5?%}.user .header .user-info .user-id[data-v-5ae8bec4]{border:1px solid #e5e5e5;border-radius:%?100?%;padding:%?2?% %?15?%}.user .header .user-info .user-opt[data-v-5ae8bec4]{position:relative}.user .header .user-info .user-opt .dot[data-v-5ae8bec4]{position:absolute;background-color:#ee0a24;border:%?2?% solid #fff;color:#ff2c3c;border-radius:100%;top:%?6?%;right:%?0?%;font-size:%?22?%;min-width:%?16?%;height:%?16?%}.user .header .user-info .level[data-v-5ae8bec4]{background:#333;padding:0 %?15?%;color:#ffdea5;line-height:%?40?%}.user .header .user-info .level .v[data-v-5ae8bec4]{font-style:italic}.user .header .user-assets[data-v-5ae8bec4]{flex:1}.user .header .user-assets .user-assests-item[data-v-5ae8bec4]{flex:1}.user .order-nav .icon-contain[data-v-5ae8bec4]{position:relative}.user .order-nav[data-v-5ae8bec4],\n.user .my-assets[data-v-5ae8bec4]{margin:%?20?% %?20?% 0;border-radius:%?8?%}.user .server-nav[data-v-5ae8bec4]{margin:%?20?%;border-radius:%?8?%}.user .title[data-v-5ae8bec4]{height:%?88?%;padding:0 %?30?%;border-bottom:1px dashed #e5e5e5}.user .nav[data-v-5ae8bec4]{padding:%?26?% 0 0}.user .nav .assets-item[data-v-5ae8bec4]{flex:1}.user .nav .item[data-v-5ae8bec4]{width:25%}.user .nav .badge[data-v-5ae8bec4]{padding:0 %?6?%;min-width:%?28?%;height:%?28?%;border-radius:%?28?%;box-sizing:border-box;border:%?1?% solid #ff2c3c;color:#ff2c3c;position:absolute;left:%?33?%;top:%?-10?%;z-index:2}.user .nav .nav-icon[data-v-5ae8bec4]{width:%?52?%;height:%?52?%}",""]),t.exports=A}}]);