<?php

namespace app\common\middleware;

use app\common\logic\ShopLevelLogic;
use app\common\server\JsonServer;
use think\facade\Cache;

/**
 * 商家等级权限中间件
 */
class ShopLevelMiddleware
{
    /**
     * 权限检查
     */
    public function handle($request, \Closure $next)
    {
        // 获取商家ID（从session、token或其他方式）
        $shopId = $this->getShopId($request);
        if (!$shopId) {
            return JsonServer::error('商家信息获取失败', [], 401);
        }

        // 检查等级是否过期
        if (ShopLevelLogic::checkLevelExpired($shopId)) {
            return JsonServer::error('您的会员等级已过期，请续费后继续使用', [], 403);
        }

        // 获取当前请求的权限要求
        $requiredPermission = $this->getRequiredPermission($request);
        if ($requiredPermission) {
            // 检查权限（使用缓存提高性能）
            $cacheKey = "shop_permission_{$shopId}_{$requiredPermission}";
            $hasPermission = Cache::remember($cacheKey, function() use ($shopId, $requiredPermission) {
                return ShopLevelLogic::checkPermission($shopId, $requiredPermission);
            }, 300); // 缓存5分钟

            if (!$hasPermission) {
                return JsonServer::error('您的等级不支持此功能，请升级后使用', [], 403);
            }
        }

        // 将商家等级信息注入到请求中
        $request->shopLevel = $this->getShopLevel($shopId);
        $request->shopLimits = $this->getShopLimits($shopId);

        return $next($request);
    }

    /**
     * 获取商家ID
     */
    private function getShopId($request)
    {
        // 从session获取
        if ($request->session('shop_id')) {
            return $request->session('shop_id');
        }

        // 从header获取（API接口）
        if ($request->header('shop-id')) {
            return $request->header('shop-id');
        }

        // 从参数获取
        if ($request->param('shop_id')) {
            return $request->param('shop_id');
        }

        return null;
    }

    /**
     * 获取当前请求需要的权限
     */
    private function getRequiredPermission($request)
    {
        $controller = $request->controller();
        $action = $request->action();
        
        // 权限映射表
        $permissionMap = [
            // 商品管理
            'goods.add' => 'goods_management',
            'goods.edit' => 'goods_management',
            'goods.batch' => 'advanced_goods_management',
            
            // 店铺装修
            'decoration.advanced' => 'advanced_decoration',
            'decoration.premium' => 'full_decoration',
            
            // 营销工具
            'marketing.coupon' => 'marketing_tools',
            'marketing.discount' => 'marketing_tools',
            'marketing.group_buy' => 'all_marketing_tools',
            'marketing.flash_sale' => 'all_marketing_tools',
            
            // 数据分析
            'analysis.basic' => 'data_analysis',
            'analysis.advanced' => 'advanced_analysis',
            
            // VIP服务
            'service.vip' => 'vip_service',
        ];

        $key = strtolower($controller . '.' . $action);
        return $permissionMap[$key] ?? null;
    }

    /**
     * 获取商家等级信息
     */
    private function getShopLevel($shopId)
    {
        $cacheKey = "shop_level_info_{$shopId}";
        return Cache::remember($cacheKey, function() use ($shopId) {
            $shop = \app\common\model\shop\Shop::find($shopId);
            if (!$shop) {
                return null;
            }

            $levelConfig = ShopLevelLogic::getLevelConfig($shop->level);
            return [
                'level' => $shop->level,
                'level_name' => ShopLevelLogic::getLevelName($shop->level),
                'expire_time' => $shop->level_expire_time,
                'is_expired' => ShopLevelLogic::checkLevelExpired($shopId),
                'features' => $levelConfig ? json_decode($levelConfig->features, true) : [],
            ];
        }, 300);
    }

    /**
     * 获取商家等级限制
     */
    private function getShopLimits($shopId)
    {
        $cacheKey = "shop_limits_{$shopId}";
        return Cache::remember($cacheKey, function() use ($shopId) {
            return ShopLevelLogic::getLevelLimits($shopId);
        }, 300);
    }
}

/**
 * 商家等级权限注解类
 * 用于在控制器方法上标注所需权限
 */
class ShopLevelPermission
{
    public $permission;

    public function __construct($permission)
    {
        $this->permission = $permission;
    }
}
