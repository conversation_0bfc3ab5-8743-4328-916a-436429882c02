<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员通知测试工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: #fff;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"], textarea, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        textarea {
            height: 100px;
            resize: vertical;
        }
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #45a049;
        }
        .response {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            background-color: #f9f9f9;
            border-left: 4px solid #4CAF50;
            display: none;
        }
        .error {
            border-left-color: #f44336;
        }
        .log-container {
            margin-top: 20px;
            padding: 15px;
            background-color: #f9f9f9;
            border-radius: 4px;
            max-height: 300px;
            overflow-y: auto;
        }
        .log-entry {
            margin-bottom: 5px;
            padding: 5px;
            border-bottom: 1px solid #eee;
        }
        .log-entry.info {
            color: #0066cc;
        }
        .log-entry.success {
            color: #4CAF50;
        }
        .log-entry.error {
            color: #f44336;
        }
        .tabs {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 1px solid #ddd;
        }
        .tab {
            padding: 10px 20px;
            cursor: pointer;
            background-color: #f1f1f1;
            margin-right: 5px;
            border-radius: 4px 4px 0 0;
        }
        .tab.active {
            background-color: #4CAF50;
            color: white;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>管理员通知测试工具</h1>

        <div class="tabs">
            <div class="tab active" data-tab="admin-notification">管理员通知</div>
            <div class="tab" data-tab="websocket-config">WebSocket配置</div>
        </div>

        <div class="tab-content active" id="admin-notification">
            <form id="notification-form">
                <div class="form-group">
                    <label for="title">通知标题</label>
                    <input type="text" id="title" name="title" value="系统通知" required>
                </div>

                <div class="form-group">
                    <label for="content">通知内容</label>
                    <textarea id="content" name="content" required>这是一条测试通知，请查收！</textarea>
                </div>

                <div class="form-group">
                    <label for="type">通知类型</label>
                    <select id="type" name="type">
                        <option value="admin_notification">管理员通知</option>
                        <option value="system_notification">系统通知</option>
                        <option value="custom_notification">自定义通知</option>
                        <option value="error_notification">错误通知</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="url">跳转URL（可选）</label>
                    <input type="text" id="url" name="url" placeholder="点击通知后跳转的URL">
                </div>

                <div class="form-group">
                    <label for="icon">通知图标</label>
                    <select id="icon" name="icon">
                        <option value="0">默认图标</option>
                        <option value="1">成功图标</option>
                        <option value="2">错误图标</option>
                        <option value="3">警告图标</option>
                        <option value="4">信息图标</option>
                    </select>
                </div>

                <button type="submit">发送通知</button>
            </form>

            <div class="response" id="response"></div>
        </div>

        <div class="tab-content" id="websocket-config">
            <button id="get-config">获取WebSocket配置</button>
            <div class="response" id="config-response"></div>
        </div>

        <div class="log-container">
            <h3>操作日志</h3>
            <div id="log"></div>
        </div>
    </div>

    <script>
        // 添加日志函数
        function addLog(message, type = 'info') {
            const logContainer = document.getElementById('log');
            const logEntry = document.createElement('div');
            logEntry.className = 'log-entry ' + type;
            logEntry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logContainer.insertBefore(logEntry, logContainer.firstChild);
        }

        // 切换标签页
        document.querySelectorAll('.tab').forEach(tab => {
            tab.addEventListener('click', function() {
                // 移除所有标签页的active类
                document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
                // 移除所有内容区的active类
                document.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));

                // 添加当前标签页的active类
                this.classList.add('active');
                // 显示对应的内容区
                const tabId = this.getAttribute('data-tab');
                document.getElementById(tabId).classList.add('active');
            });
        });

        // 发送通知表单提交
        document.getElementById('notification-form').addEventListener('submit', function(e) {
            e.preventDefault();

            const title = document.getElementById('title').value;
            const content = document.getElementById('content').value;
            const type = document.getElementById('type').value;
            const url = document.getElementById('url').value;
            const icon = document.getElementById('icon').value;

            if (!title || !content) {
                showResponse('标题和内容不能为空', true);
                return;
            }

            addLog(`准备发送通知: ${title}`);

            // 使用GET方法发送测试通知，避免POST请求的认证问题
            fetch('/api/notification/testNotification?title=' + encodeURIComponent(title) +
                  '&content=' + encodeURIComponent(content) +
                  '&type=' + encodeURIComponent(type) +
                  '&url=' + encodeURIComponent(url) +
                  '&icon=' + icon, {
                method: 'GET'
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 1) {
                    showResponse(data.msg || '通知发送成功', false);
                    addLog(`通知发送成功: ${data.msg}`, 'success');
                } else {
                    showResponse(data.msg || '通知发送失败', true);
                    addLog(`通知发送失败: ${data.msg}`, 'error');
                }
            })
            .catch(error => {
                showResponse('请求出错: ' + error.message, true);
                addLog(`请求出错: ${error.message}`, 'error');
            });
        });

        // 获取WebSocket配置
        document.getElementById('get-config').addEventListener('click', function() {
            addLog('获取WebSocket配置...');

            fetch('/api/notification/websocketConfig')
            .then(response => response.json())
            .then(data => {
                if (data.code === 1) {
                    const configResponse = document.getElementById('config-response');
                    configResponse.innerHTML = '<pre>' + JSON.stringify(data.data, null, 2) + '</pre>';
                    configResponse.style.display = 'block';
                    configResponse.classList.remove('error');
                    addLog('WebSocket配置获取成功', 'success');
                } else {
                    showConfigResponse(data.msg || 'WebSocket配置获取失败', true);
                    addLog(`WebSocket配置获取失败: ${data.msg}`, 'error');
                }
            })
            .catch(error => {
                showConfigResponse('请求出错: ' + error.message, true);
                addLog(`请求出错: ${error.message}`, 'error');
            });
        });

        // 显示响应信息
        function showResponse(message, isError) {
            const responseElement = document.getElementById('response');
            responseElement.textContent = message;
            responseElement.style.display = 'block';

            if (isError) {
                responseElement.classList.add('error');
            } else {
                responseElement.classList.remove('error');
            }
        }

        // 显示WebSocket配置响应信息
        function showConfigResponse(message, isError) {
            const responseElement = document.getElementById('config-response');
            responseElement.textContent = message;
            responseElement.style.display = 'block';

            if (isError) {
                responseElement.classList.add('error');
            } else {
                responseElement.classList.remove('error');
            }
        }

        // 页面加载完成
        window.addEventListener('load', function() {
            addLog('页面加载完成，可以开始测试通知功能');
        });
    </script>
</body>
</html>
