# Snapshots for AI

## Configuration

The `config.json` file allows you to customize the behavior of the Snapshots for AI plugin.

### Options

- `excluded_patterns`: A list of patterns to exclude from the project structure snapshot. Patterns include:
  - `.git`
  - `.gitignore`
  - `gradle`
  - `gradlew`
  - `gradlew.*`
  - `node_modules`
  - `vendor`
  - `.snapshots`
  - `.idea`
  - `.vscode`
  - `*.log`
  - `*.tmp`
  - `target`
  - `dist`
  - `build`
  - `.DS_Store`
  - `*.bak`
  - `*.swp`
  - `*.swo`
  - `*.lock`
  - `*.iml`
  - `coverage`
  - `*.min.js`
  - `*.min.css`
  - `netlify.toml`
  - `package-lock.json`
  - `__pycache__`
  - `LICENSE`
- `included_patterns`: A list of patterns to include in the project structure snapshot. Patterns include:
  - `build.gradle`
  - `settings.gradle`
  - `gradle.properties`
  - `pom.xml`
  - `Makefile`
  - `CMakeLists.txt`
  - `package.json`
  - `package-lock.json`
  - `yarn.lock`
  - `requirements.txt`
  - `Pipfile`
  - `Pipfile.lock`
  - `Gemfile`
  - `Gemfile.lock`
  - `composer.json`
  - `composer.lock`
  - `.editorconfig`
  - `.eslintrc.json`
  - `.eslintrc.js`
  - `.prettierrc`
  - `.babelrc`
  - `.env`
  - `.dockerignore`
  - `.gitattributes`
  - `.stylelintrc`
  - `.npmrc`

## Default Configuration

- `default_prompt`: The default prompt text that will be displayed in the snapshot dialog.
- `default_include_entire_project_structure`: Whether to include the entire project structure by default when creating a snapshot.
- `default_include_all_files`: Whether to include all project files by default when creating a snapshot.

## Usage

To create a snapshot, follow these steps:

### From the Tools Menu
1. Open the `Tools` menu in PHPStorm.
2. Select `Create Snapshot`.
3. Enter your prompt (if not using the default prompt).
4. Select the files to include in the snapshot.
5. Click `OK` to generate the snapshot.

### From the Main Toolbar
1. Click on the `Create Snapshot` icon in the main toolbar.
2. Follow the same steps as above to create a snapshot.

The snapshot will be saved in the `.snapshots` directory within your project.
