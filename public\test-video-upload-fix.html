<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>视频上传功能修复测试</title>
    <link rel="stylesheet" href="/static/lib/layui/css/layui.css">
    <link rel="stylesheet" href="/static/admin/css/like.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #fafafa;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #1890ff;
            padding-bottom: 10px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .status.success {
            background: #f6ffed;
            color: #52c41a;
            border: 1px solid #b7eb8f;
        }
        .status.error {
            background: #fff2f0;
            color: #ff4d4f;
            border: 1px solid #ffccc7;
        }
        .status.warning {
            background: #fffbe6;
            color: #faad14;
            border: 1px solid #ffe58f;
        }
        .layui-form-item {
            margin-bottom: 15px;
        }
        .layui-form-label {
            width: 100px;
            text-align: right;
        }
        .layui-input-block {
            margin-left: 120px;
        }
        .test-controls {
            text-align: center;
            margin: 20px 0;
        }
        .test-log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .log-entry {
            margin: 2px 0;
            padding: 2px 0;
        }
        .log-success { color: #28a745; }
        .log-error { color: #dc3545; }
        .log-info { color: #17a2b8; }
        .log-warning { color: #ffc107; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎥 视频上传功能修复测试</h1>
        <p>这个页面用于测试修复后的视频上传功能，确保关闭弹窗后页面功能不会失效。</p>
        
        <div class="status success">
            ✅ 视频上传修复脚本已加载！现在可以安全地使用视频上传功能。
        </div>
        
        <div class="test-section">
            <h3>🎯 视频上传测试</h3>
            <form class="layui-form">
                <div class="layui-form-item">
                    <label class="layui-form-label">商品视频：</label>
                    <div class="layui-input-block" id="videoContainer">
                        <div class="like-upload-video">
                            <div class="upload-image-elem">
                                <a class="add-upload-video" id="video"> + 添加视频</a>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        
        <div class="test-section">
            <h3>🧪 页面功能测试</h3>
            <form class="layui-form">
                <div class="layui-form-item">
                    <label class="layui-form-label">商品名称：</label>
                    <div class="layui-input-block">
                        <input type="text" name="goods_name" placeholder="请输入商品名称" class="layui-input">
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <label class="layui-form-label">商品分类：</label>
                    <div class="layui-input-block">
                        <select name="category" lay-verify="required">
                            <option value="">请选择分类</option>
                            <option value="1">电子产品</option>
                            <option value="2">服装鞋帽</option>
                            <option value="3">家居用品</option>
                        </select>
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <label class="layui-form-label">上架时间：</label>
                    <div class="layui-input-block">
                        <input type="text" name="start_time" id="start_time" placeholder="选择上架时间" class="layui-input">
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <label class="layui-form-label">商品状态：</label>
                    <div class="layui-input-block">
                        <input type="radio" name="status" value="1" title="上架" checked>
                        <input type="radio" name="status" value="0" title="下架">
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <div class="layui-input-block">
                        <button type="button" class="layui-btn" id="testFormBtn">测试表单功能</button>
                        <button type="button" class="layui-btn layui-btn-normal" id="restoreBtn">手动恢复功能</button>
                    </div>
                </div>
            </form>
        </div>
        
        <div class="test-controls">
            <button class="layui-btn layui-btn-primary" onclick="clearLog()">清空日志</button>
            <button class="layui-btn layui-btn-warm" onclick="checkPageStatus()">检查页面状态</button>
        </div>
        
        <div class="test-section">
            <h3>📝 测试日志</h3>
            <div id="testLog" class="test-log">等待测试开始...</div>
        </div>
        
        <div class="test-section">
            <h3>📋 测试步骤</h3>
            <ol>
                <li>点击"+ 添加视频"按钮</li>
                <li>在弹出的视频选择窗口中，直接点击关闭按钮（不选择视频）</li>
                <li>测试页面上的其他功能是否正常：
                    <ul>
                        <li>文本框是否可以输入</li>
                        <li>下拉框是否可以选择</li>
                        <li>单选框是否可以点击</li>
                        <li>日期选择器是否可以打开</li>
                    </ul>
                </li>
                <li>如果功能异常，点击"手动恢复功能"按钮</li>
            </ol>
        </div>
        
        <div class="status warning">
            ⚠️ 注意：这是测试页面，视频上传功能可能无法完全工作，主要测试关闭弹窗后页面功能是否正常。
        </div>
    </div>

    <script src="/static/lib/layui/layui.js"></script>
    <script src="/static/common/js/video-upload-fix.js"></script>
    <script>
        // 日志函数
        function log(message, type = 'info') {
            const logContainer = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.textContent = `[${timestamp}] ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
            
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
        
        // 清空日志
        function clearLog() {
            document.getElementById('testLog').innerHTML = '日志已清空\n';
        }
        
        // 检查页面状态
        function checkPageStatus() {
            log('开始检查页面状态...', 'info');
            
            // 检查文本框
            var textInput = document.querySelector('input[name="goods_name"]');
            if (textInput && !textInput.disabled) {
                log('✅ 文本框功能正常', 'success');
            } else {
                log('❌ 文本框功能异常', 'error');
            }
            
            // 检查layui组件
            if (typeof layui !== 'undefined') {
                log('✅ Layui已加载', 'success');
                
                layui.use(['form'], function() {
                    var form = layui.form;
                    if (form) {
                        log('✅ Layui Form组件正常', 'success');
                    } else {
                        log('❌ Layui Form组件异常', 'error');
                    }
                });
            } else {
                log('❌ Layui未加载', 'error');
            }
            
            // 检查修复脚本
            if (typeof VideoUploadFix !== 'undefined') {
                log('✅ 视频上传修复脚本已加载', 'success');
            } else {
                log('❌ 视频上传修复脚本未加载', 'error');
            }
        }
        
        // 初始化layui
        layui.use(['form', 'laydate', 'layer'], function() {
            var form = layui.form;
            var laydate = layui.laydate;
            var layer = layui.layer;
            
            // 暴露到全局
            window.layer = layer;
            
            log('✅ Layui模块加载完成', 'success');
            
            // 初始化日期选择器
            laydate.render({
                elem: '#start_time',
                type: 'datetime'
            });
            
            // 测试表单功能按钮
            document.getElementById('testFormBtn').addEventListener('click', function() {
                log('🧪 测试表单功能...', 'info');
                
                var formData = form.val('test-form');
                log('表单数据: ' + JSON.stringify(formData), 'info');
                
                layer.msg('表单功能正常！');
                log('✅ 表单功能测试通过', 'success');
            });
            
            // 手动恢复功能按钮
            document.getElementById('restoreBtn').addEventListener('click', function() {
                log('🔧 手动恢复页面功能...', 'warning');
                
                if (typeof VideoUploadFix !== 'undefined') {
                    VideoUploadFix.restore();
                    log('✅ 页面功能已恢复', 'success');
                } else {
                    log('❌ 修复脚本不可用', 'error');
                }
            });
            
            log('✅ 页面初始化完成', 'success');
            
            // 自动检查状态
            setTimeout(checkPageStatus, 1000);
        });
        
        // 页面加载完成
        window.addEventListener('load', function() {
            log('🎉 测试页面已完全加载', 'success');
        });
    </script>
</body>
</html>
