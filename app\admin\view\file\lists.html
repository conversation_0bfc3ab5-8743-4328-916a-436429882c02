{layout name="layout2" /}
<style>
    /* 拖拽排序样式 */
    .warehouse li {
        position: relative;
    }

    .drag-handle {
        position: absolute;
        top: 5px;
        right: 5px;
        width: 20px;
        height: 20px;
        background: rgba(0,0,0,0.6);
        border-radius: 3px;
        display: none;
        align-items: center;
        justify-content: center;
        cursor: move;
        z-index: 10;
    }

    .drag-handle i {
        color: white;
        font-size: 12px;
    }

    .warehouse li:hover .drag-handle {
        display: flex;
    }

    .warehouse li.sortable-ghost {
        opacity: 0.5;
        background: #f0f8ff;
    }

    .warehouse li.sortable-chosen {
        transform: scale(1.05);
        box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        z-index: 999;
    }

    .warehouse li.sortable-drag {
        transform: rotate(5deg);
    }
</style>
<div class="file-manager">
    <div id="cate-tree-more" class="cate-tree-more" style="width: 200px"></div>
    <div class="gallery">
        <div class="layui-card" style="box-shadow:none;">
            <div class="layui-card-header">
                <button type="button" class="layui-btn layui-btn layui-btn-sm layui-btn-normal layEvent" lay-event="addCate"><i class="layui-icon layui-icon-add-circle"></i> 添加分类</button>
                <button type="button" class="layui-btn layui-btn layui-btn-sm layui-btn-normal" id="upload">
                    <i class="layui-icon layui-icon-upload"></i> {if $type == 10}上传图片{elseif $type == 20}上传视频{else/}上传文件{/if}
                </button>
                <button type="button" class="layui-btn layui-btn layui-btn-sm layEvent layui-btn layui-btn-warm" lay-event="move" style="margin-left:5px;"><i class="layui-icon layui-icon-water"></i>  移动分类</button>
                <button type="button" class="layui-btn layui-btn layui-btn-sm layEvent layui-btn layui-btn-danger" lay-event="del"><i class="layui-icon layui-icon-delete"></i>  删除文件</button>
            </div>
            <div class="layui-card-body">
                <script id="manager" type="text/html">
                    <ul class="warehouse sortable-list">
                        {{#  layui.each(d, function(index, item){ }}
                            <li lay-id="{{item.id}}" lay-href="{{item.uri}}" data-sort="{{item.sort_order || 0}}">
                                <div class="file-icon"><img class="file-image" src="{{item.uri}}"></div>
                                <div class="file-name"><a href="javascript:">{{item.name}}</a></div>
                                <div class="drag-handle" title="拖拽排序">
                                    <i class="layui-icon layui-icon-slider"></i>
                                </div>
                            </li>
                        {{#  }); }}
                    </ul>
                    {{#  if(d.length === 0){ }}
                        <div class="empty">
                            <i class="layui-icon layui-icon-release" style=""></i>
                            <p>木有数据，请去上传</p>
                        </div>
                    {{#  } }}
                </script>
                <div id="view"></div>
                <input type="hidden" id="fileUrl" value="">
            </div>
        </div>
        <div style="height:55px;"></div>
        <div class="footer">
            <button type="button" id="okFile" class="layui-btn layui-btn layui-btn-sm layui-btn-normal">使用选中文件</button>
            <div id="page"></div>
        </div>
    </div>
</div>

<!-- 引入SortableJS库 -->
<script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>

<script>
    layui.use(["form", "element", "tree", "laytpl", "laypage", "upload"], function() {
        var $ = layui.jquery;
        var tree     = layui.tree;
        var laytpl   = layui.laytpl;
        var laypage  = layui.laypage;
        var upload   = layui.upload;
        var category = JSON.parse('{$category|raw}');
        var curCid   = 0;

        var uploadIns = upload.render({
            elem: "#upload"
            ,url: "{:url('Upload/image')}"
            ,accept: "images"
            ,exts: "jpg|png|gif|bmp|jpeg|ico|mp4|avi|wmv|rmvb|rm|flv|mkv"
            ,size: 102400
            ,data : {
                "cid": curCid, 
                "type": "{$type}"
            }
            ,number: 200
            ,multiple: true
            ,done: function (res) {
                if (res.code === 1) {
                    var li  = "<li lay-id='"+res.data.id+"' lay-href='"+res.data.uri+"'>\n";
                        li += "<div class='file-icon'><img class='file-image' src='"+res.data.uri+"'></div>\n"
                        li += "<div class='file-name'><a href='javascript:'>"+res.data.name+"</a></div>\n";
                        li += "</li>"
                    $(".warehouse").prepend(li)
                }

                layer.msg(res.msg);
            }
        });

        var active = {
            getFile: function (cid, page) {
                cid = cid === undefined ? 0 : cid;
                page = page === undefined ? 1 : page;
                var data = {cid:cid, page:page, type: "{$type}"};
                var index = layer.load(1, {shade: false});
                like.ajax({
                    url: "{:url('File/lists')}",
                    type: "GET",
                    data: data,
                    success:function(res) {
                        layer.close(index);
                        if(res.code === 1) {
                            active.renderList(res.data.data);
                            laypage.render({
                                elem: "page"
                                ,count: res.data.total
                                ,curr: res.data.current_page
                                ,limit: res.data.per_page
                                ,last: res.data.last_page
                                ,jump: function (obj, first) {
                                    if (!first) {
                                        active.getFile(cid, obj.curr)
                                    }
                                }
                            });
                        }
                    }
                });
            },
            renderList: function(data) {
                var getTpl = document.getElementById("manager").innerHTML;
                var view   = document.getElementById('view');
                laytpl(getTpl).render(data, function(html){
                    view.innerHTML = html;
                    // 初始化拖拽排序
                    active.initSortable();
                });
            },

            // 初始化拖拽排序
            initSortable: function() {
                var warehouseEl = document.querySelector('.warehouse.sortable-list');
                if (warehouseEl && typeof Sortable !== 'undefined') {
                    // 销毁之前的实例
                    if (window.warehouseSortable) {
                        window.warehouseSortable.destroy();
                    }

                    window.warehouseSortable = Sortable.create(warehouseEl, {
                        handle: '.drag-handle', // 只能通过拖拽手柄拖动
                        animation: 150,
                        ghostClass: 'sortable-ghost',
                        chosenClass: 'sortable-chosen',
                        dragClass: 'sortable-drag',
                        onEnd: function(evt) {
                            // 拖拽结束后保存新的排序
                            active.saveSortOrder();
                        }
                    });
                }
            },

            // 保存排序顺序
            saveSortOrder: function() {
                var sortData = [];
                $('.warehouse li').each(function(index) {
                    var id = $(this).attr('lay-id');
                    if (id) {
                        sortData.push({
                            id: id,
                            sort_order: $('.warehouse li').length - index // 倒序，越靠前权重越大
                        });
                    }
                });

                if (sortData.length > 0) {
                    like.ajax({
                        url: "{:url('File/updateSort')}",
                        data: {
                            sort_data: sortData,
                            type: "{$type}"
                        },
                        type: "POST",
                        success: function(res) {
                            if (res.code === 1) {
                                layer.msg('排序已保存', {icon: 1, time: 1000});
                            } else {
                                layer.msg(res.msg || '排序保存失败', {icon: 2});
                            }
                        },
                        error: function() {
                            layer.msg('排序保存失败', {icon: 2});
                        }
                    });
                }
            },
            getChoice: function() {
                var ids  = [];
                var href = [];
                $(".warehouse li.on").each(function () {
                    ids.push($(this).attr("lay-id"));
                    href.push($(this).attr("lay-href"));
                });

                return {ids:ids, href:href}
            },
            move: function () {
                var fileData = active.getChoice();
                if (fileData.ids.length === 0) {
                    layer.msg("尚未选择任何文件");
                    return false;
                }
                layer.open({
                    type: 2
                    ,title: "移动文件"
                    ,content: "{:url('File/move')}?type={$type}"
                    ,area: ["400px", "440px"]
                    ,btn: ["确定", "取消"]
                    ,yes: function(index, layero){
                        var iframeWindow = window["layui-layer-iframe" + index];
                        var submit = layero.find("iframe").contents().find("#addSubmit");
                        iframeWindow.layui.form.on("submit(addSubmit)", function(data){
                            data.field["file_ids"] = fileData.ids;
                            data.field["type"] = "{$type}";
                            like.ajax({
                                url: "{:url('File/move')}",
                                data: data.field,
                                type: "POST",
                                success:function(res) {
                                    if(res.code === 1) {
                                        // if (data.field['cid'] !== '0') {
                                        //     $(".warehouse li.on").remove();
                                        // }
                                        active.getFile(curCid, 1);
                                        layui.layer.msg(res.msg);
                                        layer.close(index);
                                    }
                                }
                            });
                        });
                        submit.trigger("click");
                        return false;
                    }
                });
            },
            del: function () {
                var fileData = active.getChoice();
                if (fileData.ids.length === 0) {
                    layer.msg("尚未选择任何文件");
                    return false;
                }
                layer.confirm('确定删除所选文件？', function(index){
                    like.ajax({
                        url: "{:url('File/del')}",
                        data: {file_ids: fileData.ids, type: '{$type}'},
                        type: "POST",
                        success:function(res) {
                            console.log(res)
                            if(res.code === 1) {
                                active.getFile(curCid, 1);
                                layui.layer.msg(res.msg);
                            }
                        }
                    });
                    layer.close(index);
                });

            },
            addCate: function () {
                layer.open({
                    type: 2
                    ,title: "新增分类"
                    ,content: "{:url('File/addCate')}"
                    ,area: ["400px", "400px"]
                    ,btn: ["确定", "取消"]
                    ,yes: function(index, layero){
                        var iframeWindow = window["layui-layer-iframe" + index];
                        var submit = layero.find("iframe").contents().find("#addSubmit");
                        iframeWindow.layui.form.on("submit(addSubmit)", function(data){
                            data.field["type"] = "{$type}";
                            like.ajax({
                                url: "{:url('File/addCate')}",
                                data: data.field,
                                type: "POST",
                                success:function(res) {
                                    if(res.code === 1) {
                                        category = res.data;
                                        active.renderTree();
                                        layui.layer.msg(res.msg);
                                        layer.close(index);
                                    }
                                }
                            });
                        });
                        submit.trigger("click");
                    }
                });
            },
            editCate: function (obj) {
                if (obj.data.id === 0) {
                    layer.msg("此分类不允许编辑", {icon: 2});
                    return false;
                }
                layer.open({
                    type: 2
                    ,title: "编辑分类"
                    ,content: "{:url('File/editCate')}?id="+ obj.data.id + '&&type={$type}&&shop_id=0'
                    ,area: ["400px", "400px"]
                    ,btn: ["确定", "取消"]
                    ,yes: function(index, layero){
                        var iframeWindow = window["layui-layer-iframe" + index];
                        var submit = layero.find("iframe").contents().find("#addSubmit");
                        iframeWindow.layui.form.on("submit(addSubmit)", function(data){
                            data.field['id'] = obj.data.id;
                            data.field["type"] = "{$type}";
                            like.ajax({
                                url: "{:url('File/editCate')}",
                                data: data.field,
                                type: "POST",
                                success:function(res) {
                                    if(res.code === 1) {
                                        category = res.data;
                                        active.renderTree();
                                        layui.layer.msg(res.msg);
                                        layer.close(index);
                                    }
                                }
                            });
                        });
                        submit.trigger("click");
                        return false;
                    }
                });
            },
            delCate: function(obj) {
                if (obj.data.id === 0) {
                    layer.msg("此分类不允许删除", {icon: 2});
                    return false;
                }
                like.ajax({
                    url: "{:url('File/delCate')}",
                    data: {id: obj.data.id, type: "{$type}"},
                    type: "POST",
                    success:function(res) {
                        if(res.code === 1) {
                            category = res.data;
                            active.renderTree();
                            layui.layer.msg(res.msg);
                        }else{
                            category = res.data;
                            active.renderTree();
                        }
                    }
                });
            },
            renderTree: function () {
                tree.render({
                    elem: '#cate-tree-more'
                    ,edit: ['update', 'del']
                    ,customOperate: false
                    ,onlyIconControl:true
                    ,data: category
                    ,click: function (obj) {
                        // 节点被点击
                        var cid = obj.data.id;
                        var page = 1;
                        curCid = cid;
                        active.getFile(cid, page);
                        uploadIns.reload({
                            elem: "#upload"
                            ,url: "{:url('Upload/image')}"
                            ,data : {
                                "cid": curCid, 
                                "type": "{$type}"
                            }
                        });
                    }
                    ,operate: function(obj){
                        switch (obj.type) {
                            case "update":
                                active.editCate(obj);
                                break;
                            case "del":
                                active.delCate(obj);
                                break;
                        }
                    }
                });
            }
        };

        like.eventClick(active);
        active.renderTree();
        active.getFile();

        var globalUrls = [];

        $(document).on("click", ".warehouse li", function () {

            var isExist = $.inArray($(this).attr("lay-href"), globalUrls);
            if (isExist >= 0){
                globalUrls.splice(isExist, 1);
            } else {
                globalUrls.push($(this).attr("lay-href"));
            }

            if ($(this).hasClass("on")) {
                $(this).removeClass("on");
            } else {
                $(this).addClass("on");
            }
            $('#fileUrl').val(globalUrls.join(','));
        });


        // 选择图片(用于富文本的图片选择)
        // Author: 张无忌
        $("#okFile").click(function () {
            // var urls = [];
            // $(".warehouse li.on").each(function () {
            //     urls.push($(this).attr("lay-href"));
            // });

            if (globalUrls.length <= 0) {
                parent.layer.msg("请至少选择一张图片");
                return false;
            }

            var index = parent.layer.getFrameIndex(window.name);
            parent.layer.close(index);
            parent.window.callback && parent.window.callback(globalUrls.reverse());
        });

    })
</script>