{"version": 3, "file": "pages/payment.js", "sources": ["webpack:///./components/price-formate.vue?b158", "webpack:///./components/price-formate.vue?4c8e", "webpack:///./components/price-formate.vue", "webpack:///./components/price-formate.vue?3e0e", "webpack:///./components/price-formate.vue?2b64", "webpack:///./components/price-formate.vue?8e8e", "webpack:///./components/price-formate.vue?32b6", "webpack:///./components/count-down.vue?4f61", "webpack:///./utils/parseTime.js", "webpack:///./components/count-down.vue", "webpack:///./components/count-down.vue?a8c1", "webpack:///./components/count-down.vue?1b2a", "webpack:///./utils/type.js", "webpack:///./static/images/pay_success.png", "webpack:///./pages/payment.vue?55eb", "webpack:///./static/images/pay_wait.png", "webpack:///./pages/payment.vue?f811", "webpack:///./pages/payment.vue?fc5e", "webpack:///./pages/payment.vue?b708", "webpack:///./pages/payment.vue", "webpack:///./pages/payment.vue?f153", "webpack:///./pages/payment.vue?9561"], "sourcesContent": ["// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../node_modules/css-loader/dist/cjs.js??ref--3-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--3-oneOf-1-2!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./price-formate.vue?vue&type=style&index=0&lang=css&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"3181fc86\", content, true, context)\n};", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('span',{class:(_vm.lineThrough ? 'line-through' : '') + 'price-format',style:({ color: _vm.color, 'font-weight': _vm.weight })},[_vm._ssrNode(((_vm.showSubscript)?(\"<span\"+(_vm._ssrStyle(null,{\n            'font-size': _vm.subscriptSize + 'px',\n            'margin-right': '1px',\n        }, null))+\">¥</span>\"):\"<!---->\")+\" <span\"+(_vm._ssrStyle(null,{ 'font-size': _vm.firstSize + 'px', 'margin-right': '1px' }, null))+\">\"+_vm._ssrEscape(_vm._s(_vm.priceSlice.first))+\"</span> \"+((_vm.priceSlice.second)?(\"<span\"+(_vm._ssrStyle(null,{ 'font-size': _vm.secondSize + 'px' }, null))+\">\"+_vm._ssrEscape(\".\"+_vm._s(_vm.priceSlice.second))+\"</span>\"):\"<!---->\"))])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nexport default {\n    data() {\n        return {\n            priceSlice: {},\n        }\n    },\n    components: {},\n    props: {\n        firstSize: {\n            type: Number,\n            default: 14,\n        },\n        secondSize: {\n            type: Number,\n            default: 14,\n        },\n        color: {\n            type: String,\n        },\n        weight: {\n            type: [String, Number],\n            default: 400,\n        },\n        price: {\n            type: [String, Number],\n            default: '',\n        },\n        showSubscript: {\n            type: Boolean,\n            default: true,\n        },\n        subscriptSize: {\n            type: Number,\n            default: 14,\n        },\n        lineThrough: {\n            type: Boolean,\n            default: false,\n        },\n    },\n    created() {\n        this.priceFormat()\n    },\n    watch: {\n        price(val) {\n            this.priceFormat()\n        },\n    },\n    methods: {\n        priceFormat() {\n            let { price } = this\n            let priceSlice = {}\n            if (price !== null) {\n                price = parseFloat(price)\n                price = String(price).split('.')\n                priceSlice.first = price[0]\n                priceSlice.second = price[1]\n                this.priceSlice = priceSlice\n            }\n        },\n    },\n}\n", "import mod from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./price-formate.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./price-formate.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./price-formate.vue?vue&type=template&id=0c4d5c85&\"\nimport script from \"./price-formate.vue?vue&type=script&lang=js&\"\nexport * from \"./price-formate.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./price-formate.vue?vue&type=style&index=0&lang=css&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  null,\n  \"7ae24710\"\n  \n)\n\nexport default component.exports", "export * from \"-!../node_modules/vue-style-loader/index.js??ref--3-oneOf-1-0!../node_modules/css-loader/dist/cjs.js??ref--3-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--3-oneOf-1-2!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./price-formate.vue?vue&type=style&index=0&lang=css&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".price-format{display:flex;align-items:baseline}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return (_vm.time >= 0)?_c('div',[_c('client-only',[(_vm.isSlot)?_vm._t(\"default\"):_c('span',[_vm._v(_vm._s(_vm.formateTime))])],2)],1):_vm._e()}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "\n\nconst SECOND = 1000;\nconst MINUTE = 60 * SECOND;\nconst HOUR = 60 * MINUTE;\nconst DAY = 24 * HOUR;\nexport function parseTimeData(time) {\n    const days = Math.floor(time / DAY);\n    const hours = sliceTwo(Math.floor((time % DAY) / HOUR));\n    const minutes = sliceTwo(Math.floor((time % HOUR) / MINUTE));\n    const seconds = sliceTwo(Math.floor((time % MINUTE) / SECOND));\n    return {\n        days: days,\n        hours: hours,\n        minutes: minutes,\n        seconds: seconds,\n    };\n}\n\nfunction sliceTwo(str) {\n    return (0 + str.toString()).slice(-2)\n}\n\nexport  function parseFormat(format, timeData) {\n    let days = timeData.days;\n    let hours = timeData.hours, minutes = timeData.minutes, seconds = timeData.seconds\n    if (format.indexOf('dd') !== -1) {\n        format = format.replace('dd', days);\n    }\n    if (format.indexOf('hh') !== -1) {\n        format = format.replace('hh', sliceTwo(hours) );\n    }\n    if (format.indexOf('mm') !== -1) {\n        format = format.replace('mm', sliceTwo(minutes));\n    }\n    if (format.indexOf('ss') !== -1) {\n        format = format.replace('ss', sliceTwo(seconds));\n    }\n    return format\n}", "//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { parseTimeData, parseFormat } from '~/utils/parseTime'\nexport default {\n    components: {},\n    props: {\n        isSlot: {\n            type: Boolean,\n            default: false,\n        },\n        time: {\n            type: Number,\n            default: 0,\n        },\n        format: {\n            type: String,\n            default: 'hh:mm:ss',\n        },\n        autoStart: {\n            type: Boolean,\n            default: true,\n        },\n    },\n    watch: {\n        time: {\n            immediate: true,\n            handler(value) {\n                if (value) {\n                    this.reset()\n                }\n            },\n        },\n    },\n    data() {\n        return {\n            timeObj: {},\n            formateTime: 0,\n        }\n    },\n    created() {},\n    computed: {},\n    methods: {\n        createTimer(fn) {\n            return setTimeout(fn, 100)\n        },\n        isSameSecond(time1, time2) {\n            return Math.floor(time1) === Math.floor(time2)\n        },\n        start() {\n            if (this.counting) {\n                return\n            }\n            this.counting = true\n            this.endTime = Date.now() + this.remain * 1000\n            this.setTimer()\n        },\n        setTimer() {\n            this.tid = this.createTimer(() => {\n                let remain = this.getRemain()\n                if (!this.isSameSecond(remain, this.remain) || remain === 0) {\n                    this.setRemain(remain)\n                }\n                if (this.remain !== 0) {\n                    this.setTimer()\n                }\n            })\n        },\n        getRemain() {\n            return Math.max(this.endTime - Date.now(), 0)\n        },\n        pause() {\n            this.counting = false\n            clearTimeout(this.tid)\n        },\n        reset() {\n            this.pause()\n            this.remain = this.time\n            this.setRemain(this.remain)\n            if (this.autoStart) {\n                this.start()\n            }\n        },\n        setRemain(remain) {\n            const { format } = this\n            this.remain = remain\n            const timeData = parseTimeData(remain)\n            this.formateTime = parseFormat(format, timeData)\n            this.$emit('change', timeData)\n            if (remain === 0) {\n                this.pause()\n                this.$emit('finish')\n            }\n        },\n    },\n}\n", "import mod from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./count-down.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./count-down.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./count-down.vue?vue&type=template&id=2fbaab86&\"\nimport script from \"./count-down.vue?vue&type=script&lang=js&\"\nexport * from \"./count-down.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  \n}\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  null,\n  \"4090b4e2\"\n  \n)\n\nexport default component.exports", "export const client = 5\n\nexport const loginType = {\n    SMS: 0,\n    ACCOUNT: 1\n}\n\n\n// 短信发送\nexport const SMSType = {\n    // 注册\n    REGISTER: 'ZCYZ',\n    // 找回密码\n    FINDPWD: 'ZHMM',\n    // 登陆\n    LOGIN: 'YZMDL',\n    // 商家申请入驻\n    SJSQYZ: 'SJSQYZ',\n    // 更换手机号\n    CHANGE_MOBILE: 'BGSJHM',\n    // 绑定手机号\n    BIND: 'BDSJHM'\n}\n\nexport const FieldType = {\n    NONE: '',\n    SEX: 'sex',\n    NICKNAME: 'nickname',\n    AVATAR: 'avatar',\n    MOBILE: 'mobile'\n}\n\n\n// 售后状态\nexport const AfterSaleType = {\n    // 售后申请 \n    NORMAL: 'normal',\n    // 处理中\n    HANDLING: 'apply',\n    // 已处理\n    FINISH: 'finish'\n}\n", "module.exports = __webpack_public_path__ + \"img/pay_success.3a82887.png\";", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/sass-resources-loader/lib/loader.js??ref--7-oneOf-1-4!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./payment.vue?vue&type=style&index=0&id=5f312006&lang=scss&scoped=true&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"45c31cf0\", content, true, context)\n};", "module.exports = __webpack_public_path__ + \"img/pay_wait.2214e17.png\";", "export * from \"-!../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/sass-resources-loader/lib/loader.js??ref--7-oneOf-1-4!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./payment.vue?vue&type=style&index=0&id=5f312006&lang=scss&scoped=true&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".payment[data-v-5f312006]{margin:16px 0;padding:0 40px}.payment .payment-hd>img[data-v-5f312006]{width:32px;height:32px}.payment .payment-hd .status[data-v-5f312006]{font-size:24px}.payment .payment-con[data-v-5f312006],.payment .payment-hd[data-v-5f312006]{padding:32px 0;border-bottom:1px dashed hsla(0,0%,89.8%,.89804)}.payment .payment-con .item[data-v-5f312006]{align-items:flex-start}.payment .payment-footer[data-v-5f312006]{padding:32px 0}.payment .payment-footer .pay-way[data-v-5f312006]{min-width:200px;padding:0 20px;height:68px;cursor:pointer;margin-right:32px;border:1px dashed hsla(0,0%,89.8%,.89804)}.payment .payment-footer .pay-way img[data-v-5f312006]{width:30px;height:30px}.payment .payment-footer .btn[data-v-5f312006]{width:134px;height:40px;border:1px solid hsla(0,0%,89.8%,.89804)}.payment .el-dialog .pay-code[data-v-5f312006]{width:270px;height:270px}.payment .el-dialog .pay-money[data-v-5f312006]{font-size:18px;margin-top:24px;align-items:baseline}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"payment bg-white\"},[_vm._ssrNode(\"<div class=\\\"payment-hd flex\\\" data-v-5f312006>\",\"</div>\",[(_vm.order.pay_status == 0)?[_vm._ssrNode(\"<img\"+(_vm._ssrAttr(\"src\",require(\"static/images/pay_wait.png\")))+\" alt data-v-5f312006> <div class=\\\"status m-l-8 m-r-16 weight-500\\\" data-v-5f312006>\"+_vm._ssrEscape(\"\\n                \"+_vm._s(_vm.getOrderStatus(_vm.order.order_status))+\"\\n            \")+\"</div> \"),(_vm.getCancelTime(_vm.order.cancel_time) > 0)?_vm._ssrNode(\"<div class=\\\"flex\\\" data-v-5f312006>\",\"</div>\",[_vm._ssrNode(\"\\n                请在\\n                \"),_c('count-down',{style:({ color: '#FF2C3C' }),attrs:{\"time\":_vm.getCancelTime(_vm.order.cancel_time),\"format\":\"hh时mm分ss秒\"},on:{\"finish\":_vm.getOrder}}),_vm._ssrNode(\"\\n                完成支付, 超时后将取消订单\\n            \")],2):_vm._e()]:_vm._e(),_vm._ssrNode(\" \"+((_vm.order.pay_status == 1)?(\"<img\"+(_vm._ssrAttr(\"src\",require(\"static/images/pay_success.png\")))+\" alt data-v-5f312006> <div class=\\\"status m-l-8 m-r-16 weight-500\\\" data-v-5f312006>支付成功</div>\"):\"<!---->\"))],2),_vm._ssrNode(\" <div class=\\\"payment-con\\\" data-v-5f312006><div class=\\\"item flex m-b-16\\\" data-v-5f312006>\"+_vm._ssrEscape(\"订单编号：\"+_vm._s(_vm.order.order_sn))+\"</div> <div class=\\\"item flex m-b-16\\\" data-v-5f312006>\\n            订单价格：<span class=\\\"primary\\\" data-v-5f312006>\"+_vm._ssrEscape(\"￥\"+_vm._s(_vm.order.total_amount))+\"</span></div> <div class=\\\"item flex m-b-16\\\" data-v-5f312006>\\n            收货地址：\\n            <div data-v-5f312006>\"+_vm._ssrEscape(\"\\n                \"+_vm._s(_vm.address.contact)+\" \"+_vm._s(_vm.address.mobile)+\"\\n                \")+\"<span class=\\\"m-l-16\\\" data-v-5f312006>\"+_vm._ssrEscape(_vm._s(_vm.address.delivery_address))+\"</span></div></div> <div class=\\\"item flex m-b-16\\\" data-v-5f312006>\\n            商品名称：\\n            <div data-v-5f312006>\"+(_vm._ssrList((_vm.order.order_goods),function(item,index){return (\"<div data-v-5f312006><div class=\\\"flex lin-1\\\" data-v-5f312006>\"+_vm._ssrEscape(\"\\n                        【商品\"+_vm._s(index + 1)+\"】- \"+_vm._s(item.goods_name)+\"\\n                    \")+\"</div></div>\")}))+\"</div></div></div> \"),_vm._ssrNode(\"<div class=\\\"payment-footer\\\" data-v-5f312006>\",\"</div>\",[(_vm.order.pay_status == 0 && _vm.order.order_status == 0)?[_vm._ssrNode(\"<div class=\\\"title lg weight-500\\\" data-v-5f312006>请选择支付方式</div> <div class=\\\"flex m-t-16\\\" data-v-5f312006>\"+(_vm._ssrList((_vm.payWayArr),function(item,index){return (\"<div class=\\\"pay-way flex row-center\\\" data-v-5f312006><img\"+(_vm._ssrAttr(\"src\",item.image))+\" alt data-v-5f312006> <div class=\\\"m-l-16\\\" data-v-5f312006><span class=\\\"md\\\" data-v-5f312006>\"+_vm._ssrEscape(_vm._s(item.name))+\"</span> <div class=\\\"muted m-t-2 sm\\\" data-v-5f312006>\"+_vm._ssrEscape(_vm._s(item.extra))+\"</div></div></div>\")}))+\"</div>\")]:_c('nuxt-link',{staticClass:\"btn flex row-center\",attrs:{\"to\":\"/user/order\"}},[_vm._v(\"查看订单\")])],2),_vm._ssrNode(\" \"),_c('el-dialog',{attrs:{\"title\":\"微信支付\",\"visible\":_vm.showWxpay,\"width\":\"700px\",\"center\":\"\"},on:{\"update:visible\":function($event){_vm.showWxpay=$event},\"close\":_vm.clearTimer}},[_c('div',{staticClass:\"flex-col col-center black\"},[_c('img',{staticClass:\"pay-code\",attrs:{\"src\":_vm.payInfo,\"alt\":\"\"}}),_vm._v(\" \"),_c('div',{staticClass:\"m-t-8\",staticStyle:{\"font-size\":\"18px\"}},[_vm._v(\"\\n                微信扫一扫，完成支付\\n            \")]),_vm._v(\" \"),_c('div',{staticClass:\"pay-money flex\"},[_c('span',[_vm._v(\"需支付金额：\")]),_vm._v(\" \"),_c('span',{staticClass:\"primary\"},[_c('price-formate',{attrs:{\"price\":_vm.order.total_amount,\"subscript-size\":18,\"first-size\":28,\"second-size\":28}})],1)])])])],2)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { client } from '@/utils/type'\nexport default {\n    head() {\n        return {\n            title: this.$store.getters.headTitle,\n            link: [\n                {\n                    rel: 'icon',\n                    type: 'image/x-icon',\n                    href: this.$store.getters.favicon,\n                },\n            ],\n        }\n    },\n    async asyncData({ query, $get }) {\n        const { data } = await $get('order/getPayStatus', {\n            params: {\n                id: query.id,\n\t\t\t\tfrom: query.from\n            },\n        })\n        return { order: data, address: data.address }\n    },\n    data() {\n        return {\n            showWxpay: false,\n            showAlipay: false,\n            payWayArr: [],\n            payInfo: {},\n            cancelTime: 0,\n            alipayHtml: '',\n            address: {}\n        }\n    },\n    created() {\n        this.id = this.$route.query.id\n        this.from = this.$route.query.from\n        this.getPayway()\n    },\n    beforeDestroy() {\n        clearInterval(this.timer)\n    },\n    methods: {\n        async getPayway() {\n            const { code, data } = await this.$get('order/getPayWay', {\n                params: { from: 'order', order_id: this.id },\n            })\n            if (code == 1) {\n                this.payWayArr = data.pay_way\n                if (!this.payWayArr.length)\n                    this.$message({\n                        message: '请联系管理员配置支付方式',\n                        type: 'error',\n                    })\n            }\n        },\n        async orderPay(payWay) {\n            const { data, code, msg } = await this.$post('pay/unifiedpay', {\n                order_id: this.id,\n                pay_way: payWay,\n                from: this.from,\n            })\n            if (code == 1) {\n                this.payInfo = data\n                this.showWxpay = true\n                this.createTimer()\n            } else if (code == 20001) {\n                let divForm = document.getElementsByTagName('divform')\n                if (divForm.length) {\n                    document.body.removeChild(divForm[0])\n                }\n                const div = document.createElement('divform')\n                div.innerHTML = data // data就是接口返回的form 表单字符串\n                document.body.appendChild(div)\n                document.forms[0].submit()\n            } else if (code == 10001) {\n                this.$message({\n                    message: msg,\n                    type: 'success',\n                })\n                this.getOrder()\n            }\n        },\n        clearTimer() {\n            clearInterval(this.timer)\n        },\n        createTimer() {\n            if (this.timer) clearInterval(this.timer)\n            this.timer = setInterval(() => {\n                this.getOrder()\n            }, 2000)\n        },\n        async getOrder() {\n            const { data, code, msg } = await this.$get('order/getPayStatus', {\n                params: {\n                    id: this.id,\n                    from: this.from,\n                },\n            })\n            if (code == 1) {\n                this.order = data\n                if (data.pay_status == 1) {\n                    clearInterval(this.timer)\n                    this.showWxpay = false\n                }\n            }\n        },\n    },\n    computed: {\n        getOrderStatus() {\n            return (status) => {\n                let text = ''\n                switch (status) {\n                    case 0:\n                        text = '待支付'\n                        break\n                    case 1:\n                        text = '待发货'\n                        break\n                    case 2:\n                        text = '待收货'\n                        break\n                    case 3:\n                        text = '已完成'\n                        break\n                    case 4:\n                        text = '订单已关闭'\n                        break\n                }\n                return text\n            }\n        },\n        getCancelTime() {\n            return (time) => time - Date.now() / 1000\n        },\n    },\n}\n", "import mod from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./payment.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./payment.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./payment.vue?vue&type=template&id=5f312006&scoped=true&\"\nimport script from \"./payment.vue?vue&type=script&lang=js&\"\nexport * from \"./payment.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./payment.vue?vue&type=style&index=0&id=5f312006&lang=scss&scoped=true&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  \"5f312006\",\n  \"e5bc9738\"\n  \n)\n\nexport default component.exports\n\n/* nuxt-component-imports */\ninstallComponents(component, {CountDown: require('/Users/<USER>/Desktop/vue/pc/components/count-down.vue').default,PriceFormate: require('/Users/<USER>/Desktop/vue/pc/components/price-formate.vue').default})\n"], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACXA;AACA;AACA;AACA;AACA;AACA;;;;;ACLA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AADA;AAGA;AACA;AAAA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AADA;AAGA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AA5BA;AACA;AAgCA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAJA;AAKA;AACA;AACA;AAAA;AAAA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAZA;AAhDA;;AC1BA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACvBA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACNA;AACA;AACA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAJA;AAMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;;ACvCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAbA;AAkBA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAPA;AADA;AACA;AASA;AACA;AACA;AACA;AAFA;AAIA;AACA;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AApDA;AAtCA;;ACXA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACrBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;AACA;AAFA;AACA;AAMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAZA;AAeA;AACA;AACA;AACA;AACA;AACA;AALA;AACA;AASA;AACA;AACA;AACA;AACA;AACA;AACA;AANA;;;;;;;AClCA;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;ACXA;;;;;;;;ACAA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACNA;AACA;AACA;;;;;;;;ACFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAHA;AAHA;AAUA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AACA;AACA;AAFA;AADA;AAMA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAPA;AASA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AADA;AACA;AAEA;AACA;AACA;AAEA;AACA;AAFA;AAIA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAHA;AACA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAFA;AADA;AACA;AAKA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAjEA;AAkEA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AAfA;AACA;AAgBA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AA3BA;AA3GA;;AC3GA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;A", "sourceRoot": ""}