-- ----------------------------
-- Table structure for ls_purchaser_package
-- ----------------------------
DROP TABLE IF EXISTS `ls_purchaser_package`;
CREATE TABLE `ls_purchaser_package`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '套餐名称',
  `purchaser_count` int(11) NOT NULL DEFAULT 0 COMMENT '分配人数',
  `price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '套餐价格',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态(0=禁用, 1=启用)',
  `sort` int(11) NOT NULL DEFAULT 100 COMMENT '排序',
  `create_time` int(11) NULL DEFAULT NULL,
  `update_time` int(11) NULL DEFAULT NULL,
  `delete_time` int(11) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '采购套餐表';

-- ----------------------------
-- Table structure for ls_purchaser_package_order
-- ----------------------------
DROP TABLE IF EXISTS `ls_purchaser_package_order`;
CREATE TABLE `ls_purchaser_package_order`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `order_sn` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '订单号',
  `shop_id` int(11) NOT NULL COMMENT '购买的商家ID',
  `package_id` int(11) NOT NULL COMMENT '购买的套餐ID',
  `package_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '套餐名称',
  `purchaser_count` int(11) NOT NULL COMMENT '购买的分配人数',
  `price` decimal(10, 2) NOT NULL COMMENT '支付价格',
  `pay_status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '支付状态(0=未支付, 1=已支付)',
  `pay_time` int(11) NULL DEFAULT NULL COMMENT '支付时间',
  `create_time` int(11) NULL DEFAULT NULL,
  `update_time` int(11) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `shop_id`(`shop_id`) USING BTREE,
  INDEX `order_sn`(`order_sn`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '采购套餐购买记录表';

-- ----------------------------
-- Records for ls_auth
-- ----------------------------
-- 注意：请根据您自己的 ls_auth 表中的最大ID来调整下面的ID值，以避免主键冲突。
-- 这里假设当前最大ID是501，所以从502开始。
SET @max_id = 501;
SET @pid = 497; -- 父级菜单ID

-- 采购套餐菜单
INSERT INTO `ls_auth` (`id`, `pid`, `name`, `uri`, `type`, `system`, `icon`, `sort`, `status`) VALUES
(@max_id + 1, @pid, '采购套餐', 'user/PurchaserPackage/index', 1, 0, 'layui-icon-cart', 100, 1);

-- 获取新插入的父菜单ID
SET @new_pid = @max_id + 1;

-- 权限节点
INSERT INTO `ls_auth` (`id`, `pid`, `name`, `uri`, `type`, `system`, `icon`, `sort`, `status`) VALUES
(@max_id + 2, @new_pid, '添加套餐', 'user/PurchaserPackage/add', 2, 0, '', 100, 1),
(@max_id + 3, @new_pid, '编辑套餐', 'user/PurchaserPackage/edit', 2, 0, '', 100, 1),
(@max_id + 4, @new_pid, '删除套餐', 'user/PurchaserPackage/del', 2, 0, '', 100, 1),
(@max_id + 5, @new_pid, '修改状态', 'user/PurchaserPackage/status', 2, 0, '', 100, 1),
(@max_id + 6, @new_pid, '购买记录', 'user/PurchaserPackage/records', 2, 0, '', 100, 1);
