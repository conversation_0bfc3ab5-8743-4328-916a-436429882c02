<?php

/**
 * This file is part of the Carbon package.
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

return array_replace_recursive(require __DIR__.'/en.php', [
    'weekdays' => ['<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>tatu', 'Chinai', '<PERSON><PERSON><PERSON>', '<PERSON>bu<PERSON>'],
    'weekdays_short' => ['Dim', 'Pos', 'Pir', 'Tat', 'Nai', 'Sha', 'Sab'],
    'weekdays_min' => ['Dim', 'Pos', 'Pir', 'Tat', 'Nai', 'Sha', 'Sab'],
    'months' => ['<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', 'Abril', 'Mai<PERSON>', 'Junho', 'Julho', 'Augusto', 'Setembro', 'Otubro', 'Novembro', 'Decembro'],
    'months_short' => ['<PERSON>', 'Fev', '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON>', 'Jul', 'Aug', '<PERSON>', 'Otu', 'Nov', 'Dec'],
    'formats' => [
        'LT' => 'HH:mm',
        'LTS' => 'HH:mm:ss',
        'L' => 'D/M/YYYY',
        'LL' => 'd [de] MMM [de] YYYY',
        'LLL' => 'd [de] MMMM [de] YYYY HH:mm',
        'LLLL' => 'dddd, d [de] MMMM [de] YYYY HH:mm',
    ],
]);
