(window.webpackJsonp=window.webpackJsonp||[]).push([[42],{538:function(t,e,r){var content=r(608);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,r(14).default)("22020cdc",content,!0,{sourceMap:!1})},607:function(t,e,r){"use strict";r(538)},608:function(t,e,r){var n=r(13)(!1);n.push([t.i,".record{width:100%;height:788px}.record .main{padding:18px;height:100%}",""]),t.exports=n},667:function(t,e,r){"use strict";r.r(e);var n=r(6),o=(r(51),{head:function(){return{title:this.$store.getters.headTitle,link:[{rel:"icon",type:"image/x-icon",href:this.$store.getters.favicon}]}},data:function(){return{record:[]}},mounted:function(){},asyncData:function(t){return Object(n.a)(regeneratorRuntime.mark((function e(){var r,n,data;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=t.$get,e.next=3,r("ShopApply/record");case 3:return n=e.sent,data=n.data,console.log(data),e.abrupt("return",{record:data.lists});case 7:case"end":return e.stop()}}),e)})))()},methods:{}}),l=(r(607),r(9)),component=Object(l.a)(o,(function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",{staticClass:"record"},[r("div",{staticClass:"m-t-20"},[r("el-breadcrumb",{attrs:{separator:"/"}},[r("el-breadcrumb-item",{attrs:{to:{path:"/"}}},[t._v("首页")]),t._v(" "),r("el-breadcrumb-item",{attrs:{to:{path:"/store_settled"}}},[r("a",[t._v("商家入驻")])]),t._v(" "),r("el-breadcrumb-item",[t._v("申请列表")])],1)],1),t._v(" "),r("div",{staticClass:"main bg-white m-t-20"},[r("el-table",{staticStyle:{width:"100%"},attrs:{data:t.record,size:"medium","header-cell-style":{background:"#eee",color:"#606266"}}},[r("el-table-column",{attrs:{prop:"name",label:"商家名称","max-width":"180"}}),t._v(" "),r("el-table-column",{attrs:{prop:"audit_status_desc",label:"审核状态","max-width":"180"},scopedSlots:t._u([{key:"default",fn:function(e){return[3==e.row.audit_status?r("div",{staticClass:"primary"},[t._v(t._s(e.row.audit_status_desc))]):r("div",[t._v(t._s(e.row.audit_status_desc))])]}}])}),t._v(" "),r("el-table-column",{attrs:{prop:"apply_time",label:"提交时间","max-width":"180"}}),t._v(" "),r("el-table-column",{attrs:{label:"操作","max-width":"180"},scopedSlots:t._u([{key:"default",fn:function(e){return[r("div",{staticClass:"pointer",on:{click:function(r){return t.$router.push({path:"/store_settled/detail",query:{id:e.row.id}})}}},[t._v("查看详情")])]}}])})],1)],1)])}),[],!1,null,null,null);e.default=component.exports}}]);