<?php
namespace app\api\controller;

use app\admin\logic\JcaiLogic;
use app\api\logic\AdLogic;
use app\common\basics\Api;
use app\common\server\JsonServer;
use app\api\logic\RechargeLogic;
use app\api\logic\PayLogic;
use app\api\validate\RechargeValidate;
use app\common\enum\ClientEnum;
use app\common\enum\PayEnum;
use think\exception\ValidateException;

class Recharge extends Api
{
    public $like_not_need_login = ['getAgnetTemplate','rechargetemplate','getTemplates','getuzhuTemplate','getAdTemplate'];

    /**
     * note 充值模板
     */
    public function rechargeTemplate(){
        $list = RechargeLogic::getTemplate();
        return JsonServer::success('', $list);
    }
    /*
     * 获取商家入驻费及组合购买
     * DM
     */
    public function getuzhuTemplate(){
        
        $list = RechargeLogic::getuzhuTemplate();
        return JsonServer::success('', $list);
    }

    /*
     * 获取商家入驻费及组合购买权益
     * DM
     */
     public function getAdTemplate(){
         $list = AdLogic::lists(31, 1);
         return JsonServer::success('', $list);
     }

    /*
        * 获取会员购买方案
        * DM
        */
    public function getAgnetTemplate(){
        $list = AdLogic::lists(44, 1);
        return JsonServer::success('', $list);
    }
    /*
     * 获取集采购会员购买方案
     * DM
     */
    public function getTemplates(){
        $list = JcaiLogic::templatelists();
        return JsonServer::success('', $list);
    }

    /**
     * 购买机采购会员
     *
     */
    public function rejcharge(){
//        try{
//            $post = $this->request->post();
//            validate(RechargeValidate::class)->check($post);
//        }catch(ValidateException $e) {
//            return JsonServer::error($e->getError());
//        }
        $post = $this->request->post();
        $result = RechargeLogic::rejcharge($this->user_id,$this->client,$post);
        if($result === false) {
            return JsonServer::error(RechargeLogic::getError());
        }
        return JsonServer::success('', $result);
    }

    /*
     *
     * 缴纳保证金
     *
     */
    public function bondcharge(){
        $post = $this->request->post();

        $result = RechargeLogic::bondcharge($this->user_id,$post);
        if($result === false) {
            return JsonServer::error(RechargeLogic::getError());
        }
        return JsonServer::success('', $result);

    }






    /**
     * 缴纳入驻费
     *
     */
    public function ruzhucharge(){

        $post = $this->request->post();

        $result = RechargeLogic::ruzhucharge($this->user_id,$post);
        if($result === false) {
            return JsonServer::error(RechargeLogic::getError());
        }
        return JsonServer::success('', $result);
    }


    /**
     * 缴纳代理费
     *
     */
    public function agentcharge(){

        $post = $this->request->post();
        $result = RechargeLogic::agentcharge($this->user_id,$post);
        if($result === false) {
            return JsonServer::error(RechargeLogic::getError());
        }
        return JsonServer::success('', $result);
    }
    /*
     * 补缴纳代理费
     *
     */
    public function agentbucharge(){
        $post = $this->request->post();
        $result = RechargeLogic::agentbucharge($this->user_id,$post);
        if($result === false) {
            return JsonServer::error(RechargeLogic::getError());
        }

        // 创建支付参数
        $pay_way = $post['pay_way'] ?? PayEnum::WECHAT_PAY;
        $from = $post['from'] ?? ClientEnum::h5;

        try {
            switch ($pay_way) {
                case PayEnum::WECHAT_PAY:
                    $pay_info = PayLogic::wechatPay($result['order_sn'], 'agent_deposit_detail', $from);
                    break;
                case PayEnum::ALI_PAY:
                    $pay_info = PayLogic::aliPay($result['order_sn'], 'agent_deposit_detail', $from);
                    break;
                default:
                    return JsonServer::error('不支持的支付方式');
            }

            if ($pay_info === false) {
                return JsonServer::error(PayLogic::getError() ?: '创建支付订单失败');
            }

            return JsonServer::success('', $pay_info);
        } catch (\Exception $e) {
            return JsonServer::error($e->getMessage());
        }
    }


    /**
     * 充值
     */
    public function recharge(){
        try{
            $post = $this->request->post();
            validate(RechargeValidate::class)->check($post);
        }catch(ValidateException $e) {
            return JsonServer::error($e->getError());
        }
        $result = RechargeLogic::recharge($this->user_id,$this->client,$post);
        if($result === false) {
            return JsonServer::error(RechargeLogic::getError());
        }
        return JsonServer::success('', $result);
    }

    /**
     * 充值记录
     */
    public function rechargeRecord()
    {
        $get = $this->request->get();
        $get['page_no'] = $get['page_no'] ?? $this->page_no;
        $get['page_size'] = $get['page_size'] ?? $this->page_size;
        $get['user_id'] = $this->user_id;
        $result =  RechargeLogic::rechargeRecord($get);
        return JsonServer::success('', $result);
    }
}
