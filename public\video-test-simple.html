<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>视频删除测试</title>
    <style>
        body { padding: 20px; font-family: Arial, sans-serif; }
        .container { max-width: 800px; margin: 0 auto; }
        .video-container { 
            border: 2px dashed #ccc; 
            padding: 20px; 
            margin: 20px 0; 
            border-radius: 8px;
            background: #f9f9f9;
        }
        .like-upload-video { 
            text-align: center; 
            padding: 20px;
            background: #fff;
            border-radius: 4px;
        }
        .add-upload-video { 
            display: inline-block;
            padding: 10px 20px;
            background: #1890ff;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .add-upload-video:hover { background: #40a9ff; }
        
        #videoPreviewArea { 
            background: #fff;
            padding: 20px;
            border-radius: 4px;
            margin-top: 10px;
        }
        .video-info { margin: 10px 0; color: #666; }
        .video-actions { margin-top: 15px; }
        .btn { 
            padding: 8px 16px;
            margin: 0 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .btn-danger { background: #ff4d4f; color: white; }
        .btn-normal { background: #1890ff; color: white; }
        .btn:hover { opacity: 0.8; }
        
        .test-log { 
            background: #f0f0f0; 
            padding: 15px; 
            margin: 20px 0; 
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success { color: #52c41a; }
        .error { color: #ff4d4f; }
        .info { color: #1890ff; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎥 视频删除功能测试</h1>
        
        <div class="video-container" id="videoContainer">
            <div class="like-upload-video">
                <a class="add-upload-video" id="video">+ 添加视频</a>
            </div>
            
            <!-- 视频预览区域 -->
            <div id="videoPreviewArea" style="display: none;">
                <div class="video-preview-container">
                    <video id="videoPreview" controls style="max-width: 300px; max-height: 200px; border-radius: 4px;">
                        您的浏览器不支持视频播放
                    </video>
                    <div class="video-info">
                        <span id="videoFileName" class="video-file-name"></span>
                        <span id="videoFileSize" class="video-file-size"></span>
                    </div>
                    <div class="video-actions">
                        <button type="button" class="btn btn-normal" id="replaceVideoBtn">
                            🔄 重新选择
                        </button>
                        <button type="button" class="btn btn-danger" id="deleteVideoBtn">
                            🗑️ 删除视频
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <div style="text-align: center; margin: 20px 0;">
            <button class="btn btn-normal" onclick="simulateVideoUpload()">📤 模拟视频上传</button>
            <button class="btn btn-danger" onclick="testVideoDelete()">🗑️ 测试删除功能</button>
            <button class="btn" onclick="checkState()" style="background: #52c41a; color: white;">🔍 检查状态</button>
            <button class="btn" onclick="clearLog()" style="background: #666; color: white;">🧹 清空日志</button>
        </div>
        
        <div class="test-log" id="testLog">等待测试开始...</div>
    </div>

    <script>
        // 简化的视频管理器
        var VideoManager = {
            currentVideo: null,
            
            log: function(message, type) {
                type = type || 'info';
                var timestamp = new Date().toLocaleTimeString();
                var logElement = document.getElementById('testLog');
                var className = type === 'success' ? 'success' : (type === 'error' ? 'error' : 'info');
                logElement.innerHTML += '<span class="' + className + '">[' + timestamp + '] ' + message + '</span>\n';
                logElement.scrollTop = logElement.scrollHeight;
            },
            
            showVideoPreview: function(videoData) {
                this.currentVideo = videoData;
                this.log('显示视频预览: ' + videoData.filename, 'info');
                
                var container = document.getElementById('videoContainer');
                var previewArea = document.getElementById('videoPreviewArea');
                var addButton = container.querySelector('.like-upload-video');
                
                // 隐藏添加按钮
                if (addButton) {
                    addButton.style.display = 'none';
                    this.log('隐藏添加按钮', 'info');
                }
                
                // 设置视频信息
                document.getElementById('videoPreview').src = videoData.url;
                document.getElementById('videoFileName').textContent = videoData.filename;
                document.getElementById('videoFileSize').textContent = '(' + videoData.size + ')';
                
                // 显示预览区域
                previewArea.style.display = 'block';
                this.log('视频预览显示完成', 'success');
            },
            
            hideVideoPreview: function() {
                this.log('开始隐藏视频预览', 'info');
                
                var container = document.getElementById('videoContainer');
                var previewArea = document.getElementById('videoPreviewArea');
                
                // 检查添加按钮是否存在
                var addButton = container.querySelector('.like-upload-video');
                if (!addButton) {
                    this.log('⚠️ 添加按钮不存在，重新创建', 'error');
                    var addButtonHtml = '<div class="like-upload-video">' +
                        '<a class="add-upload-video" id="video">+ 添加视频</a>' +
                        '</div>';
                    container.insertAdjacentHTML('afterbegin', addButtonHtml);
                    addButton = container.querySelector('.like-upload-video');
                    
                    // 重新绑定事件
                    addButton.querySelector('.add-upload-video').onclick = function() {
                        VideoManager.log('点击添加视频按钮', 'info');
                        simulateVideoUpload();
                    };
                } else {
                    this.log('添加按钮存在，显示它', 'info');
                }
                
                // 显示添加按钮
                addButton.style.display = 'block';
                
                // 隐藏预览区域
                previewArea.style.display = 'none';
                
                // 清空视频源
                document.getElementById('videoPreview').src = '';
                
                // 清空当前视频信息
                this.currentVideo = null;
                
                this.log('视频预览隐藏完成', 'success');
            },
            
            deleteVideo: function() {
                if (!this.currentVideo) {
                    this.log('没有视频可删除', 'error');
                    return;
                }
                
                this.log('开始删除视频: ' + this.currentVideo.filename, 'info');
                
                if (confirm('确定要删除这个视频吗？')) {
                    this.hideVideoPreview();
                    this.log('视频删除成功', 'success');
                } else {
                    this.log('用户取消删除', 'info');
                }
            }
        };
        
        // 模拟视频上传
        function simulateVideoUpload() {
            VideoManager.log('开始模拟视频上传', 'info');
            
            var mockVideoData = {
                url: 'data:video/mp4;base64,AAAAIGZ0eXBpc29tAAACAGlzb21pc28yYXZjMW1wNDEAAAAIZnJlZQAAAr1tZGF0',
                filename: 'test-video-' + Date.now() + '.mp4',
                size: '1.2MB'
            };
            
            setTimeout(function() {
                VideoManager.showVideoPreview(mockVideoData);
            }, 500);
        }
        
        // 测试删除功能
        function testVideoDelete() {
            if (document.getElementById('videoPreviewArea').style.display === 'block') {
                VideoManager.deleteVideo();
            } else {
                VideoManager.log('请先上传视频', 'error');
            }
        }
        
        // 检查当前状态
        function checkState() {
            VideoManager.log('=== 当前状态检查 ===', 'info');
            
            var container = document.getElementById('videoContainer');
            var addButton = container.querySelector('.like-upload-video');
            var previewArea = document.getElementById('videoPreviewArea');
            
            VideoManager.log('添加按钮存在: ' + (addButton ? '是' : '否'), addButton ? 'success' : 'error');
            VideoManager.log('添加按钮可见: ' + (addButton && addButton.style.display !== 'none' ? '是' : '否'), 
                addButton && addButton.style.display !== 'none' ? 'success' : 'error');
            VideoManager.log('预览区域可见: ' + (previewArea.style.display === 'block' ? '是' : '否'), 
                previewArea.style.display === 'block' ? 'info' : 'success');
            VideoManager.log('当前视频: ' + (VideoManager.currentVideo ? VideoManager.currentVideo.filename : '无'), 
                VideoManager.currentVideo ? 'info' : 'success');
            
            VideoManager.log('=== 状态检查完成 ===', 'info');
        }
        
        // 清空日志
        function clearLog() {
            document.getElementById('testLog').innerHTML = '日志已清空\n';
        }
        
        // 绑定删除按钮事件
        document.getElementById('deleteVideoBtn').onclick = function() {
            VideoManager.deleteVideo();
        };
        
        // 绑定重新选择按钮事件
        document.getElementById('replaceVideoBtn').onclick = function() {
            VideoManager.log('点击重新选择视频', 'info');
            simulateVideoUpload();
        };
        
        // 绑定添加视频按钮事件
        document.getElementById('video').onclick = function() {
            VideoManager.log('点击添加视频按钮', 'info');
            simulateVideoUpload();
        };
        
        // 初始化
        VideoManager.log('视频管理器初始化完成', 'success');
        checkState();
    </script>
</body>
</html>
