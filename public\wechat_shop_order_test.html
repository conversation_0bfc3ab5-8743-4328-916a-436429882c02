<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微信小程序商城订单测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1, h2 {
            color: #333;
            border-bottom: 2px solid #4CAF50;
            padding-bottom: 10px;
        }
        .test-section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .test-button:hover {
            background-color: #45a049;
        }
        .test-button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        .test-button.warning {
            background-color: #ff9800;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            font-size: 12px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .api-info {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .flow-step {
            display: flex;
            align-items: center;
            margin: 10px 0;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        .step-number {
            background-color: #4CAF50;
            color: white;
            width: 25px;
            height: 25px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🛒 微信小程序商城订单测试</h1>
        <p>此工具用于测试微信小程序商城的订单相关接口</p>
        
        <div class="warning result">
            <strong>重要说明：</strong><br>
            现在使用的是微信小程序商城的订单接口，而不是订单物流信息管理接口：<br>
            • 创建订单：<code>https://api.weixin.qq.com/shop/order/add</code><br>
            • 确认收货：<code>https://api.weixin.qq.com/shop/delivery/receive</code>
        </div>
    </div>

    <div class="container">
        <h2>📋 订单信息</h2>
        <div class="form-group">
            <label for="order-id">订单ID:</label>
            <input type="number" id="order-id" placeholder="请输入微信支付的订单ID">
        </div>
        <button class="test-button" onclick="loadOrderInfo()">加载订单信息</button>
        <div id="order-info-result" class="result" style="display:none;"></div>
    </div>

    <div class="container">
        <h2>🔄 微信商城订单流程测试</h2>
        
        <div class="api-info">
            <h3>接口说明</h3>
            <p><strong>订单确认收货接口：</strong></p>
            <ul>
                <li><strong>URL：</strong>https://api.weixin.qq.com/shop/delivery/receive</li>
                <li><strong>参数：</strong>order_id（订单ID）、openid（用户openid）、out_order_id（可选）</li>
                <li><strong>功能：</strong>将订单状态从待收货（30）更新为已完成（100）</li>
            </ul>
        </div>

        <div class="test-section">
            <div class="flow-step">
                <div class="step-number">1</div>
                <div>
                    <h3>创建微信订单</h3>
                    <p>在微信小程序商城中创建订单记录</p>
                    <button class="test-button" onclick="createWechatOrder()" id="create-btn" disabled>创建微信订单</button>
                    <div id="create-result" class="result" style="display:none;"></div>
                </div>
            </div>

            <div class="flow-step">
                <div class="step-number">2</div>
                <div>
                    <h3>确认收货</h3>
                    <p>调用微信商城确认收货接口，更新订单状态</p>
                    <button class="test-button" onclick="confirmReceiveOrder()" id="confirm-btn" disabled>确认收货</button>
                    <div id="confirm-result" class="result" style="display:none;"></div>
                </div>
            </div>

            <div class="flow-step">
                <div class="step-number">3</div>
                <div>
                    <h3>验证结果</h3>
                    <p>检查订单状态是否正确更新</p>
                    <button class="test-button warning" onclick="verifyOrderStatus()" id="verify-btn" disabled>验证状态</button>
                    <div id="verify-result" class="result" style="display:none;"></div>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <h2>🔧 独立测试工具</h2>
        
        <div class="test-section">
            <h3>直接测试确认收货</h3>
            <p>直接调用确认收货接口，不依赖其他步骤</p>
            <button class="test-button" onclick="directConfirmTest()">直接测试确认收货</button>
            <div id="direct-test-result" class="result" style="display:none;"></div>
        </div>

        <div class="test-section">
            <h3>检查微信配置</h3>
            <button class="test-button" onclick="checkWechatConfig()">检查微信配置</button>
            <div id="config-result" class="result" style="display:none;"></div>
        </div>
    </div>

    <script>
        let currentOrderId = '';
        let orderInfo = {};

        // 显示结果的通用函数
        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.className = `result ${type}`;
            element.textContent = message;
            element.style.display = 'block';
        }

        // 发送API请求的通用函数
        async function sendRequest(url, data = {}, method = 'POST') {
            try {
                const options = {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json',
                    }
                };
                
                if (method === 'POST') {
                    options.body = JSON.stringify(data);
                }
                
                const response = await fetch(url, options);
                return await response.json();
            } catch (error) {
                return { error: error.message };
            }
        }

        // 加载订单信息
        async function loadOrderInfo() {
            const orderId = document.getElementById('order-id').value;
            if (!orderId) {
                showResult('order-info-result', '请输入订单ID', 'error');
                return;
            }

            currentOrderId = orderId;
            showResult('order-info-result', '正在加载订单信息...', 'info');

            const result = await sendRequest('/wechat/order/sync-status', { order_id: orderId });

            if (result.code === 1) {
                orderInfo = result.data;
                const info = `订单信息：
订单ID: ${orderInfo.order_id}
订单状态: ${orderInfo.order_status}
发货状态: ${orderInfo.shipping_status}
微信同步状态: ${orderInfo.wechat_mini_express_sync}
确认收货时间: ${orderInfo.confirm_take_time ? new Date(orderInfo.confirm_take_time * 1000).toLocaleString() : '未确认'}
是否微信支付: ${orderInfo.is_wechat_pay ? '是' : '否'}`;

                showResult('order-info-result', info, 'success');
                
                // 启用按钮
                if (orderInfo.is_wechat_pay) {
                    document.getElementById('create-btn').disabled = false;
                    document.getElementById('confirm-btn').disabled = false;
                    document.getElementById('verify-btn').disabled = false;
                }
            } else {
                showResult('order-info-result', `加载失败：${result.msg}`, 'error');
            }
        }

        // 创建微信订单
        async function createWechatOrder() {
            if (!currentOrderId) {
                showResult('create-result', '请先加载订单信息', 'error');
                return;
            }

            showResult('create-result', '正在创建微信订单...', 'info');

            const result = await sendRequest('/debug/create-wechat-order', { order_id: currentOrderId });

            if (result.code === 1) {
                showResult('create-result', `微信订单创建成功！\n订单ID: ${result.data.order_id}\n订单号: ${result.data.order_sn}`, 'success');
            } else {
                showResult('create-result', `创建失败：${result.msg}`, 'error');
            }
        }

        // 确认收货
        async function confirmReceiveOrder() {
            if (!currentOrderId) {
                showResult('confirm-result', '请先加载订单信息', 'error');
                return;
            }

            showResult('confirm-result', '正在调用微信商城确认收货接口...', 'info');

            const result = await sendRequest('/debug/test-full-confirm', { 
                order_id: currentOrderId,
                user_id: 1 
            });

            if (result.code === 1) {
                const data = result.data;
                let message = '确认收货测试结果：\n';
                
                Object.keys(data).forEach(key => {
                    message += `${key}: ${typeof data[key] === 'object' ? JSON.stringify(data[key]) : data[key]}\n`;
                });

                showResult('confirm-result', message, 'success');
            } else {
                showResult('confirm-result', `确认收货失败：${result.msg}`, 'error');
            }
        }

        // 验证订单状态
        async function verifyOrderStatus() {
            if (!currentOrderId) {
                showResult('verify-result', '请先加载订单信息', 'error');
                return;
            }

            showResult('verify-result', '正在验证订单状态...', 'info');

            const result = await sendRequest('/wechat/order/sync-status', { order_id: currentOrderId });

            if (result.code === 1) {
                const data = result.data;
                const message = `验证结果：
订单状态: ${data.order_status}
确认收货时间: ${data.confirm_take_time ? new Date(data.confirm_take_time * 1000).toLocaleString() : '未确认'}
微信同步状态: ${data.wechat_mini_express_sync}

${data.order_status == 100 ? '✅ 订单已完成' : '⚠️ 订单未完成'}`;

                showResult('verify-result', message, data.order_status == 100 ? 'success' : 'warning');
            } else {
                showResult('verify-result', `验证失败：${result.msg}`, 'error');
            }
        }

        // 直接测试确认收货
        async function directConfirmTest() {
            if (!currentOrderId) {
                showResult('direct-test-result', '请先加载订单信息', 'error');
                return;
            }

            showResult('direct-test-result', '正在直接测试确认收货...', 'info');

            const result = await sendRequest('/debug/confirm', { order_id: currentOrderId });

            if (result.code === 1) {
                const data = result.data;
                const message = `直接测试结果：
同步结果: ${data.sync_result}
详细信息: ${data.sync_detail || '无'}
订单状态: ${data.order_status}
用户OpenID: ${data.has_openid ? '已获取' : '未获取'}
微信交易号: ${data.has_transaction_id ? '已获取' : '未获取'}`;

                showResult('direct-test-result', message, data.sync_result.includes('成功') ? 'success' : 'error');
            } else {
                showResult('direct-test-result', `测试失败：${result.msg}`, 'error');
            }
        }

        // 检查微信配置
        async function checkWechatConfig() {
            const result = await sendRequest('/debug/wechat-config', {}, 'GET');
            
            if (result.code === 1) {
                const data = result.data;
                const message = `微信配置检查：
${JSON.stringify(data, null, 2)}`;
                showResult('config-result', message, 'success');
            } else {
                showResult('config-result', `配置检查失败：${result.msg}`, 'error');
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('微信小程序商城订单测试工具已加载');
        });
    </script>
</body>
</html>
